/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 */


import com.adva.gradle.imagepublisher.ImagePublisherTask
import com.adva.gradle.jarexec.JarExecTask
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory
import groovy.xml.XmlUtil
import org.apache.tools.ant.taskdefs.condition.Os
import org.cyclonedx.model.AttachmentText
import org.cyclonedx.model.License

import java.nio.charset.StandardCharsets
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.stream.Collectors

buildscript {

    // Build script configuration for custom plugins
    apply from: 'utilities.gradle'
    apply from: 'parameters.gradle'
    apply from: 'repositories.gradle'

    ext {
        ver_hibernate = '6.1.6.Final'
    }

    dependencies {
        classpath("org.hibernate.orm:hibernate-gradle-plugin:$ver_hibernate")
        classpath('org.hidetake:gradle-ssh-plugin:2.10.1')
        // Apply the java based subversion tool for use in build scripts
        classpath group: 'org.tmatesoft.svnkit', name: 'svnkit', version: '1.10.1'
        classpath "org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:2.8"
        classpath libs.jackson.dataformat.yaml
        classpath "org.cyclonedx:cyclonedx-gradle-plugin:1.8.2"
    }
}

plugins {
    id 'java-library'

    id 'com.adva.gradle.plugin.document-generation' apply false

    // plugins to resolve for use in sub-projects
    id 'com.github.jk1.dependency-license-report' version '1.5' apply false
    id 'com.autonomousapps.dependency-analysis' apply false

    // Add plugins to build classpath that contribute tasks and indirect objects to build scripts.
    id 'com.adva.gradle.plugin.project-module-build-plugin'
    id 'com.adva.gradle.plugin.source-analyzer' apply false
    id 'com.adva.gradle.plugin.jar-exec' apply false

    // ENC common plugins used in this build script
    id 'com.adva.gradle.plugin.image-publisher'
    id 'org.openrewrite.rewrite' version '6.24.0'
}

// Define image publish task class to use for defining image publisher tasks in gradle build fragment files.
// Gradle can't import this class in these files so define it here to be used in later in fragment file.
ext.IMAGE_PUBLISHER_TASK_CLASS = ImagePublisherTask.class
// Define closures for creating cyclonedx plugin bean classes to work around gradle import limitation on applied files.
ext.newCyclonedxAttachmentText = {
    return new AttachmentText()
}
ext.newCyclonedxLicense = {
    return new License()
}
// Defined object mapper to use for openapi/asyncapi manifest generation
ext.jacksonObjectMapper = new ObjectMapper(new YAMLFactory())

String sonarProjectConfigFile = 'sonar-project.properties'

// Name to use when defining publication target for image publisher
ext.publicationTargetName = 'publication-target'

ext.JarExecTaskType = JarExecTask.class

// Closure that can be used with image publisher that defines the default file server for publishing build installers
ext.publicationTarget = {
    host = publicationHost
    user = publicationUser
    certificate = publicationPrivateCertificate
    trust = true
}


// Name to use when defining publication target for image publisher
ext.releasePublicationTargetName = 'release-publication-target'

// Closure that can be used with image publisher that defines the default file server for publishing build installers
ext.releasePublicationTarget = {
    host = releasePublicationHost
    user = releasePublicationUser
    certificate = publicationPrivateCertificate
    trust = true
}

// Output some diagnostic information about the build
Long maxMemoryRaw = Runtime.getRuntime().maxMemory()
String maxMemory = "Unlimited"
if (maxMemoryRaw != Long.MAX_VALUE) {
    maxMemoryRaw = maxMemoryRaw / 1_000_000L
    maxMemory = maxMemoryRaw.toString()
}

logger.quiet("Java Version: {}\nJVM Max Heap: {} MB",
        System.getProperty("java.version"),
        maxMemory,
)

defaultTasks 'all'

apply from: 'shared.gradle'
layout.buildDirectory.set(file('build-gradle'))

ext {
    /**
     * Use symbolic links from nms/src/.. to modules/MODULE/src/main/java/...
     * gradle directory structure.
     */
    linkedProjectType = "LINKED"

    /**
     * Use subversion externals for modules/MODULE/src/main/java/...
     * gradle directory structure.
     *
     * Externals are defined in ws root dir (nms).
     * To edit use the following from the root of the workspace:
     *    'svn propedit svn:externals .'
     */
    externalProjectType = "EXTERNAL"
    projectTypes = new HashMap()
}

imagePublisher {
    addTarget publicationTargetName, publicationTarget
}


apply from: 'dependencies.gradle'

// Apply common settings to all projects
allprojects { p ->
    if (p.equals(p.rootProject) || isJavaProject(p)) {
        p.apply plugin: 'java-library'

        // Enable common (stand-alone) test configuration
        // Always execute tests
        // Never stop after first test failure
        // Don't fail the build if no tests are found
        // Don't run test suites
        try {
            Task testTask = p.tasks.getByName('test')
            testTask.ignoreFailures = true
            testTask.outputs.upToDateWhen { false }
            if (testTask.getPath().startsWith(testDebugModule)) {
                testTask.jvmArgs '-agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=*:5005'
            }

            testTask.filter {
                setFailOnNoMatchingTests(false)
                excludeTestsMatching "*Suite"
                excludeTestsMatching "*ScanTest"
            }
        } catch (UnknownTaskException e) {
            // Do nothing if test task does not exist
        }

        tasks.withType(JavaCompile) {
            // Spring 6 upgrade: due to removal of 'LocalVariableTableParameterNameDiscoverer'
            options.compilerArgs.add("-parameters")
            // Apply compiler settings for all projects
            options.encoding = encoding
            options.warnings = compileWarnings
            options.debug = compileDebug
            sourceCompatibility = sourceLevel
            targetCompatibility = targetLevel

            if (briefOutput) {
                // Disable compiler warnings when in brief mode
                options.compilerArgs << '-nowarn'
                options.compilerArgs << '-Xlint:none'
            }

            // Use incremental compile
            // Should help on recompile but may have a slight bump on first time compile so left out for now
            // options.incremental = true

            // Fork the compile to separate process
            // This option has a negative impact on performance and causes build race condition in parallel builds
            // options.fork = true
        }

        tasks.withType(Test) {
            // Continue build even if tests fail
            ignoreFailures = true

            // Uncomment this to display standard out/err as tests progress
            //testLogging.showStandardStreams = true

            // Uncomment to monitor tests progress
            //testLogging {
            //    events "started", "passed", "skipped", "failed"
            //}
        }

        // Add logic to ensure no jar duplicates.
        // When the classes and resources output directory is the same the jar will contain duplicates
        tasks.jar {
            Set includedFiles = new HashSet<>()
            eachFile { details ->
                String path = details.file.getCanonicalPath()
                if (includedFiles.contains(path)) {
                    // This file has already been added
                    details.exclude()
                } else {
                    includedFiles.add(path)
                }
            }
        }
    }
}

configurations {
    advaToolsCustomPath
    advaToolsArtifacts
    activemqLauncherArtifacts
    jmsServerProduction
    obfuscatePasswordLauncherArtifacts
    encryptPassphraseLauncherArtifacts
    obfuscatePasswordProduction
    encryptPassphraseProduction
    snmpForwarderArtifacts

    encDocs

    encYPDB
    encYPDBCommons

    encCmsw

    antConfig

    // jar libraries required in order to do simple development tasks
    mandatoryLibs

    yfilesForJavafxConfiguration

    applicationLibs

    emPlugin

    aspectjWeaver {
        transitive = false
    }
    eclipseLinkWeaver {
        transitive = false
    }

    copssh

    svp

    win32_libraries
    win32_filezilla
    win32_postgres
    win64_postgres

    sonarCli
}

applyConfigurationLimits(project)

dependencies {
    encCmsw dep_enc_cmsw
    encDocs dep_enc_docs
    encDocs dep_enc_docs_other
    encYPDB dep_enc_ypdb
    encYPDBCommons libs.enc.ypdb.commons

    jmsServerProduction project(path: ":", configuration: 'advaToolsArtifacts')
    jmsServerProduction modep( mod_nmscommon )
    jmsServerProduction modep( mod_security_api )
    jmsServerProduction modep( mod_security_impl )
    jmsServerProduction dgroup_jms_broker
    jmsServerProduction libs.activemq.console

    obfuscatePasswordProduction libs.log4j.api
    obfuscatePasswordProduction libs.log4j.core
    obfuscatePasswordProduction libs.commons.net
    obfuscatePasswordProduction modep( mod_nmscommon )

    encryptPassphraseProduction libs.log4j.api
    encryptPassphraseProduction libs.log4j.core
    encryptPassphraseProduction modep( mod_security_api )
    encryptPassphraseProduction modep( mod_security_impl )

    snmpForwarderArtifacts libs.forwarder
    snmpForwarderArtifacts libs.log4j12.api
    snmpForwarderArtifacts libs.log4j.api
    snmpForwarderArtifacts libs.log4j.core

    advaToolsCustomPath libs.log4j.core

    applicationLibs modep( mod_mediation )
    applicationLibs modep( mod_frontend )

    // Extra items for ant required for FSP ant execution
    antConfig group: 'ant-contrib', name: 'ant-contrib', version: '1.0b3', transitive: false
    antConfig group: 'org.apache.ant', name: 'ant-junit', version: '1.10.1', transitive: false
    antConfig group: 'org.apache.ant', name: 'ant-junit4', version: '1.10.1', transitive: false
    antConfig group: 'org.apache.ant', name: 'ant-jsch', version: '1.10.3', transitive: false
    antConfig group: 'com.jcraft', name: 'jsch', version: '0.1.54', transitive: false
    // antConfig group: 'commons-net', name: 'commons-net', version: '1.4.0', transitive: false

    // Aspectj Weaver library is required for launching mediation server since
    // it is referenced by the Idea Jar launcher.
    mandatoryLibs dep_flexnet_winFlxCore
    mandatoryLibs dep_flexnet_linFlxCore

    yfilesForJavafxConfiguration libs.yfiles.for.javafx

    emPlugin libs.emplugin

    aspectjWeaver libs.aspectjweaver
    eclipseLinkWeaver libs.eclipselink

    copssh dep_copssh

    svp dep_svp

    win32_libraries dep_win32_libraries
    win32_postgres dep_win32_postgres
    win32_filezilla dep_win_filezilla
    win64_postgres dep_win64_postgres

    sonarCli group: 'org.sonarsource.scanner.cli', name: 'sonar-scanner-cli', version: '6.2.0.4584'
}

// Custom renaming filter for handling mandatory libs
// Currently this is only used for handling flexera OS specific libraries
ext.mandatoryLibsNamingFilter = { String filename ->
    return filename.replace("-${ver_flexnet}", "")
}

// Enhance ant classpath to include addons needed by FSP ant scripts
task configureAntClasspath() {
    doLast {
        def antClassLoader = org.apache.tools.ant.Project.class.classLoader
        configurations.antConfig.each { File f ->
            antClassLoader.addURL(f.toURI().toURL())
        }
    }
}

// When making calls out to ant dir_NLMS must be set which is taken from dir.properties traditionally
// Setup this file if it does not exist.
File dirProperties = file("$fspRoot/dir.properties")
if (!dirProperties.exists()) {
    String dir = fspRoot.getCanonicalPath().replace("\\", "/")
    dirProperties.write("dir_NLMS = $dir\n")
}

task installYpDb() {
    group('enc.build')
    description('Expand the current Yellow Page database into the ENC workspace')

    // soft dependency to quiet gradle errors
    mustRunAfter(":dist:win:assembleProductionBundles")

    doLast {
        copy {
            from {
                List<File> docs = new ArrayList<>();
                configurations.encYPDB.files.each { docBundle ->
                    docs.addAll(zipTree(docBundle).getFiles())
                }
                return project.files(docs)
            }
            into( "$serverRoot/db/yp" )
        }
    }
}

task installYpDbCommons(type: Copy) {
    group('enc.build')
    description('Install Yellow Page database commons jar file into ENC workspace')

    from { configurations.encYPDBCommons.files }
    into("$serverRoot/lib")
    rename("ypdb-commons-1.0.1-SNAPSHOT", "ypdb-commons")
}

//task installYpDb(type: Copy) {
//    from( zipTree("$fspRoot/db/yp.zip") )
//
//    into( "$fspRoot/db/yp" )
//}
//
//task convertYpDb() {
//    dependsOn( installYpDb )
//
//    doLast {
//        // Ensure directory exists
//        mkdir("$fspRoot/db/yp")
//
//        ant.pathconvert(pathsep: "\${line.separator}") {
//            fileset(dir: "$fspRoot/db/yp")
//        }
//    }
//
//    inputs.file( 'db/yp.zip' )
//    outputs.dir( "$fspRoot/db/yp")
//}

task installSvp() {
    group('enc.build')
    description('Install SVP application into ENC workspace')

    doLast {
        copy {
            from { zipTree { configurations.svp.singleFile } }

            into( "$serverRoot/svp" )
        }
    }
}

//task installCmsw(type: Copy) {
//    from( zipTree("$fspRoot/cmsw/cmsw.zip") )
//
//    into( "$serverRoot/cmsw" )
//}

task cleanClasses(type: Delete) {
    delete "$fspRoot/build/classes"
    delete "$fspRoot/test/classes"
}

task cleanWars() {
    doLast {
        delete fileTree( "ws/deploy" ) {
            include( "*.war" )
        }
    }
}

task cleanLibDirectory(type: Delete) {
    delete "$fspRoot/lib"
}

clean.dependsOn( cleanClasses )
clean.dependsOn( cleanWars )
clean.dependsOn( cleanLibDirectory )

task "clean.archives" (type: Delete) {
    delete "$fspRoot/build/archives"
    delete "$fspRoot/build/yguard"
    delete "$fspRoot/build/package"
}

// Make clean.deep a synonym of spotless
task "clean.deep" () {
    dependsOn("spotless")
}

task analyzeTeamCityAgent() {
    // Only execute this task when running on linux team city agents
    enabled = (System.getenv("TEAMCITY_VERSION") != null && isUnix)

    doLast {
        String result = 'df --output=avail .'.execute().text.trim()
        String[] parts = result.split('\n')

        int HEADER_INDEX = 0
        int VALUE_INDEX = 1
        // ENC builds require significant space, for now assure at least 10 gb
        // Re-building a 'spotless' workspace consumes approximately 6 gb
        int MINIMUM_DISK_SPACE_KB = 10_000_000

        // df on the currently directory should return only two lines: header and row
        if (parts.length == 2 && parts[HEADER_INDEX] == 'Avail') {
            int size = Integer.parseInt(parts[VALUE_INDEX].trim())
            project.getLogger().quiet("Disk space available on agent is $size KB")

            if (size < MINIMUM_DISK_SPACE_KB) {
                project.getLogger().quiet("##teamcity[buildProblem description='Not enough disk space available "+
                        "on TC agent to execute test, contact <EMAIL>']")
            }
        }

        result = 'pgrep -u postgres -fa -- -D'.execute().text

        if (result == null || result.isEmpty()) {
            project.getLogger().quiet("##teamcity[buildProblem description='Postgres is not running on TC Agent, "+
                    "contact <EMAIL>']")
        }
    }
}

// Attempt to cleanup all generated items
// This is tricky based on how FSP is built and will need to be updated as one-off items
// are populated into the top level directory
task spotless(type: Delete) {
    group('enc.build')
    description('Clean all built artifacts and files installed into the ENC workspace')

    dependsOn analyzeTeamCityAgent
    dependsOn([ getTasksByName( 'clean', true ) ])

    // Webapp base filenames to clean if present
    def allWebapps = [
            "advabase",
            "advaproxy",
            "enc",
            "jolokia",
            "manual",
            "mtosi",
            "map",
            "manual-eth",
            "manual-sync",
            "manual-wdm",
            "manual-user",
            "mltopologyui",
            "mltopologyui2"
    ]

    delete "$fspRoot/var"

    delete "$fspRoot/scripts/createDDL_ddlGeneration.jdbc"

    doLast {
        allWebapps.each { name ->
            // Clean from pre-deploy and webapps directories
            delete fileTree("$fspRoot/ws/deploy") {
                include "$name*.war"
            }
            delete fileTree("$fspRoot/ws/webapps") {
                include "$name*.war"
            }
        }
    }

    delete "$fspRoot/ws/webapps/reportdb"

    delete "$fspRoot/docs"

    delete "$fspRoot/test/ws"
    delete "$fspRoot/test/var"
    delete "$fspRoot/test/results"
    delete "$fspRoot/test/CustomProducts"

    delete "$fspRoot/build/archives"
    delete "$fspRoot/build/yguard"
    delete "$fspRoot/build/package"

    delete "$fspRoot/build/java"
    delete "$fspRoot/build/launchers"
    delete "$fspRoot/test/junit/generated"

    delete "$fspRoot/src/main/webapp/META-INF"

    delete "$fspRoot/ssocerts/ssotruststore.jks"

    delete "$fspRoot/copssh-install"

    delete "$fspRoot/svp"

    delete "$fspRoot/postgres/bin"
    delete "$fspRoot/postgres/lib"
    delete "$fspRoot/filezilla-install"

    delete "$fspRoot/codecoverage"

    delete fileTree("$fspRoot/activemq/data/") {
        include("*.log")
        include("*.data")
        include("*.redo")
        include("lock")
    }

    delete fileTree("$fspRoot/cmsw") {
        exclude("cmsw.zip")
    }

    delete fileTree("$fspRoot/lib") {
        exclude("dependencies.txt")
    }

    delete fileTree("$fspRoot/test/lib") {
        exclude("ant/jacocoant.jar")
        exclude("ant/ivy-2.4.0.jar")
    }

    delete fileTree("$fspRoot") {
        include("libeay32.dll")
        include("msvcr71.dll")
        include("ssleay32.dll")
    }

    delete sonarProjectConfigFile

    if (buildPlatform) {
        delete platformDirName
    }

    doLast {
        fileTree(projectDir) {
            include 'layers/**/gradle-wrapper.properties'
            include 'libraries/**/gradle-wrapper.properties'
            include 'modules/**/gradle-wrapper.properties'
        }.files.each { File f ->
            // Delete gradle-wrapper.properties copied from root
            // NOTE: gnss gradle-wrapper.properties is part of repository and should not be removed
            if (!f.getCanonicalPath().endsWith('modules'+File.separator+'gnss'+File.separator+'gradle'+File.separator+'wrapper'+File.separator+'gradle-wrapper.properties')) {
                delete f
            }
        }
    }
}

// Cleanup files leftover from java application execution or other files that have some
// debug/analysis value but can't be retained forever without filling up the file system.
task cleanFiles(type: Delete) {
    mustRunAfter(mod_target(mod_mediation, 'stopMediation'))
    mustRunAfter(mod_target(mod_mediation, 'stopJMS'))

    delete {
        fileTree(rootProject.projectDir) {
            cleanFilePatterns.split(',').each { String pattern ->
                include pattern.trim()
            }
        }.filter { File file ->
            Long epochSeconds = (Long)(file.lastModified() / 1000L)
            LocalDateTime cutoff = LocalDateTime.now(ZoneOffset.systemDefault()).minusDays(Long.parseLong(cleanFileAgeDays))
            LocalDateTime fileDate = LocalDateTime.ofEpochSecond(epochSeconds, 0, ZoneOffset.systemDefault())

            return fileDate.isBefore(cutoff)
        }
    }

}

// A non-modular jar composed of a mish-mash of classes.
// Build this jar here for now as it does not really belong to any one module
task advaToolsJar(type: Jar) {
    archiveBaseName = "adva_tools"

    // Ensure modules are built prior to grabbing artifacts
    dependsOn( mod_compile(mod_mediation) )
    dependsOn( mod_compile(mod_nmscommon) )
    dependsOn( mod_compile(mod_property) )
    dependsOn( mod_compile(mod_frontend) )
    dependsOn( mod_compile(mod_adva_concurrent) )
    if (weaveEnc) {
        dependsOn( mod_target(mod_mediation, 'elWeave') )
        dependsOn( mod_target(mod_mediation, 'ajcWeave') )
    }

    from( fileTree(mod_nmscommon.project.sourceSets.main.output.classesDirs.singleFile) {
        include( 'com/adva/common/util/LoggingOutputStream.class' )
        include( 'com/adva/nlms/common/security/SecurityPropertiesDTO.class' )
        include( 'com/adva/nlms/common/security/MDPasswordValidationException.class' )
    })
    from( fileTree(mod_property.project.sourceSets.main.output.classesDirs.singleFile) {
        include( 'com/adva/nlms/common/property/FNMPropertyFactory.class' )
        include( 'com/adva/nlms/common/property/FNMPropertyFactory*.class' )
        include( 'com/adva/nlms/common/property/FNMPropertyConstants.class' )
    })
    from( fileTree(mod_frontend.project.sourceSets.main.output.classesDirs.singleFile) {
        include( 'com/adva/nlms/frontend/security/InvalidSecurityOperation.class' )
        include( 'com/adva/nlms/frontend/security/PasswordValidationException.class' )
    })
    from( fileTree(mod_adva_concurrent.project.sourceSets.main.output.classesDirs.singleFile) {
        include("com/adva/nlms/mediation/common/concurrent/**/*.class")
    })

    from( fileTree(mod_persistence_common.project.sourceSets.main.output.classesDirs.singleFile) {
        include("com/adva/nlms/mediation/common/persistence/**/*.class")
    })


    // Are any of these processed by Eclipse Link weave? If so should use the weave output dir
    from( fileTree(mod_mediation.project.sourceSets.main.output.classesDirs.singleFile) {
        include("com/adva/nlms/mediation/monitoring/config/MonitoringIntervals.class")
        include("com/adva/nlms/mediation/monitoring/cliApplication/**/*.class")
        include("com/adva/nlms/mediation/monitoring/utils/**/*.class")
        include("com/adva/nlms/mediation/monitoring/extTrigger/**/*.class")
        include("com/adva/nlms/mediation/common/rest/clients/**/*.class")
        include("com/adva/nlms/mediation/ssl/**/*.class")
        include("com/adva/nlms/mediation/gistransfer/geoserver/rest/**/*.class")
        include("com/adva/nlms/mediation/common/concurrent/**/*.class")
        include("com/adva/nlms/mediation/util/TarCompressor.class")
        include("com/adva/nlms/mediation/util/TarCompressor*.class")
    })

    manifest {
        attributes(
                "Manifest-Version": "1.0",
                "Created-By": "RinisM",
                "Main-Class": "com.adva.nlms.mediation.monitoring.cliApplication.AdvaTools",
                "Class-Path": configurations.advaToolsCustomPath.collect { f -> stripVersion(f.name) }.join(" "),
        )
    }
}

task jmsServerProductionWrapper(type: Jar) {
    dependsOn(advaToolsJar)

    archiveBaseName = "activemqLauncher"

    doFirst {
        manifest {
            attributes (
                    "Description":  "ENC activemq server launcher",
                    "Main-Class":   "org.apache.activemq.console.Main",
                    "Class-Path":   configurations.jmsServerProduction.collect { f -> stripVersion(f.name) }.join(" "),
                    "ENC-Version":  Version,
                    "ENC-Build":    BuildNumber,
                    "ENC-Revision": FnmRevision,
                    "CAC-Revision": CacRevision,
                    "Copyright":    "ADVA Optical Networking SE",
            )
        }
    }
}

task obfuscatePasswordProductionWrapper(type: Jar) {
    dependsOn(advaToolsJar)

    archiveBaseName = "obfuscatePasswordLauncher"

    doFirst {
        manifest {
            attributes (
                    "Description":  "Obfuscate password utility launcher",
                    "Main-Class":   "com.adva.nlms.common.EncryptorHelper",
                    "Class-Path":   configurations.obfuscatePasswordProduction.collect { f -> stripVersion(f.name) }.join(" "),
                    "ENC-Version":  Version,
                    "ENC-Build":    BuildNumber,
                    "ENC-Revision": FnmRevision,
                    "CAC-Revision": CacRevision,
                    "Copyright":    "ADVA Optical Networking SE",
            )
        }
    }
}

task encryptPassphraseProductionWrapper(type: Jar) {
    dependsOn(advaToolsJar)

    archiveBaseName = "encryptPassphraseLauncher"

    doFirst {
        manifest {
            attributes (
                    "Description":  "Encrypt passphrase utility launcher",
                    "Main-Class":   "com.adva.nlms.mediation.security.crypto.runners.PassphraseEncryptor",
                    "Class-Path":   configurations.encryptPassphraseProduction.collect { f -> stripVersion(f.name) }.join(" "),
                    "ENC-Version":  Version,
                    "ENC-Build":    BuildNumber,
                    "ENC-Revision": FnmRevision,
                    "CAC-Revision": CacRevision,
                    "Copyright":    "ADVA Optical Networking SE",
            )
        }
    }
}

task snmpForwarderJarWrapper(type: Jar) {
    archiveBaseName = "forwarder"

    doFirst {
        manifest {
            attributes (
                    "Description":  "ENC snmp forwarder launcher",
                    "Main-Class":   "com.adva.fw.Server",
                    "Class-Path":   configurations.snmpForwarderArtifacts.collect { f -> f.name }.join(" ") + " em/log4j.jar",
                    "ENC-Version":  Version,
                    "ENC-Build":    BuildNumber,
                    "ENC-Revision": FnmRevision,
                    "CAC-Revision": CacRevision,
                    "Copyright":    "ADVA Optical Networking SE",
            )
        }
    }
}

artifacts {
    advaToolsArtifacts advaToolsJar
    activemqLauncherArtifacts jmsServerProductionWrapper
    obfuscatePasswordLauncherArtifacts obfuscatePasswordProductionWrapper
    encryptPassphraseLauncherArtifacts encryptPassphraseProductionWrapper
    snmpForwarderArtifacts snmpForwarderJarWrapper
}

task prepareProduction() {
    dependsOn(mod_target(mod_frontend, "yguard"))
    dependsOn(mod_target(mod_frontend, "yfilesCopy"))
}

// On windows copy will fail with md5 error if the copy goes to the workspace root directory
// Not sure why, but as a work around don't copy into the workspace root.
task configureWindows32Postgres() {
    doLast {
        copy {
            from { zipTree { configurations.win32_postgres.singleFile } }
            into("$fspRoot/postgres")
        }
    }
}
task configureWindows64Postgres() {
    doLast {
        copy {
            from { zipTree { configurations.win64_postgres.singleFile } }
            into("$fspRoot/postgres")
        }
    }
}
task configureWindowsFilezilla(type: Copy) {
    // soft dependency to quiet gradle errors
    mustRunAfter(":dist:win:assembleProductionBundles")

    from { configurations.win32_filezilla.singleFile }
    into("$fspRoot/filezilla-install")

    rename { String name ->
        // Install anywhere needs a very specific name for the installer, change it here
        return "FileZilla_Server-0_9_60_2.exe"
    }
}

// customized copy of the windows dll's to the root of the project to avoid
// "Gradle detected a problem with the following location..." which will
// destroy up-to-date checking on all tasks that read from the workspace like
// 'compileJava'.  By defining the outputs and executing the copy internal to the task
// solves this issue as the whole 'workspace' directory is no longer considered the
// output of this task.
task configureWindowsBin() {
    // soft dependency to quiet gradle errors
    mustRunAfter(":dist:win:assembleProductionBundles")

    outputs.files("libeay32.dll", "msvcr71.dll", "ssleay32.dll")

    doLast {
        copy {
            from { zipTree { configurations.win32_libraries.singleFile } }
            into("$fspRoot")
            fileMode = 0644
        }
    }
}

task installCopSSH(type: Copy) {
    group('enc.build')
    description('Install Windows SSH utility into the ENC workspace')

    // soft dependency to quiet gradle errors
    mustRunAfter(":dist:win:assembleProductionBundles")

    from { configurations.copssh.singleFile }
    into("$fspRoot/copssh-install")

    // Ensure installed exe strips the version information, install anywhere looks for a specific name.
    rename { String name ->
        return "copssh_installer.exe"
    }
}

task configureWin32(type: Copy) {
    dependsOn(configureWindows32Postgres)
    dependsOn(configureWindowsFilezilla)
    dependsOn(configureWindowsBin)
    dependsOn(installCopSSH)
}

// On windows copy will fail with md5 error if the copy goes to the workspace root directory
// Not sure why, but as a work around don't copy into the workspace root.
task configureWin64(type: Copy) {
    dependsOn(configureWindows64Postgres)
    dependsOn(configureWindowsFilezilla)
    dependsOn(configureWindowsBin)
    dependsOn(installCopSSH)
}

task configureSolaris(type: Copy) {
    from("$fspRoot/config/sol/bin")
    into("$fspRoot/bin")
}

// For linux postgres must be installed as a service so point to installed files
task configureLinuxPostgres() {
    // Disable this task if the postgres directories already exist.
    // Using outputs.dir() to check for up-to-date does not work well with symlinks
    onlyIf {
        !(file("postgres/bin").exists()) || !(new File("postgres/lib").exists())
    }

    doLast {
        if (usePostgresDirLink) {
            File psDir = file(postgresdir).parentFile

            // Link the entire postgres app bin dir to workspace
            // This is needed if using postgres installed as part of FSP install
            if (!(file("postgres/bin").exists())) {
                "ln -s $psDir/bin $fspRoot/postgres".execute()
            }

            if (!(file("postgres/lib").exists())) {
                "ln -s $psDir/lib $fspRoot/postgres".execute()
            }
        } else {
            // link only individual postgres commands needed
            mkdir "$fspRoot/postgres/bin"
            mkdir "$fspRoot/postgres/lib"

            // Link in postgres commands
            def commands = [ "psql", "dropdb","createdb", "pg_dump", "pg_basebackup" ]

            commands.each { c ->
                "ln -s $postgresdir/$c $fspRoot/postgres/bin/$c".execute()
            }

            File psDir = file(postgresdir).parentFile
            file("$psDir/lib").listFiles().each { File f ->
                "ln -s $f $fspRoot/postgres/lib/$f.name".execute()
            }
        }
    }
}

task configureLinuxFiles(type: Copy) {
    from("$fspRoot/config/lnx")
    into(fspRoot)
}

task configureLinux() {
    dependsOn(configureLinuxPostgres)
    // Not really needed at this time and UP-TO-DATE checks do not seem to work correctly
    // dependsOn(configureLinuxFiles)
}

task installDocs() {
    group('enc.build')
    description('Install ENC docs into the ENC workspace')

    // soft dependency to quiet gradle errors
    mustRunAfter(":dist:win:assembleProductionBundles")

    doLast {
        copy {
            from {
                List<File> docs = new ArrayList<>();
                configurations.encDocs.files.each { docBundle ->
                    docs.addAll(zipTree(docBundle).getFiles())
                }
                return project.files(docs)
            }

            into("$serverRoot/docs")

            fileMode = 0644
        }
    }
}

task installCmsw() {
    group('enc.build')
    description('Install Windows CMSW application into the ENC workspace')

    // soft dependency to quiet gradle errors
    mustRunAfter(":dist:win:assembleProductionBundles")

    doLast {
        copy {
            from {
                List<File> cmswBinaryFiles = new ArrayList<>();
                configurations.encCmsw.files.each { fileBundle ->
                    cmswBinaryFiles.addAll(zipTree(fileBundle).getFiles())
                }
                return project.files(cmswBinaryFiles)
            }

            into("$serverRoot/cmsw")

            fileMode = 0644
        }
    }
}

task installMandatoryLibsRoot(type: Copy) {
    from(configurations.mandatoryLibs)

    into("$serverRoot/lib")

    rename mandatoryLibsNamingFilter
}

task installMandatoryLibs(type: Copy) {
    if (buildPlatform) dependsOn installMandatoryLibsRoot

    from(configurations.mandatoryLibs)

    into("$serverRoot/lib")

    rename mandatoryLibsNamingFilter
}

def scriptCopyFilterClosure
def fnmServerScriptCopyFilterClosure

if (Os.isFamily(Os.FAMILY_UNIX)) {
    scriptCopyFilterClosure = { line ->
        if (line.contains("/etc/setenv.sh")) {
            // replace with location of custom setenv.sh
            return line.replaceAll("/etc/setenv.sh", "$platformDir/$serverParentDirName/etc/setenv.sh")
        }
        return line
    }

    fnmServerScriptCopyFilterClosure = { line ->
        if (line.contains("-Xmx4000M")) {
            // replace with location of custom setenv.sh
            return line.replaceAll("-Xmx4000M", "-Xmx$MediationRuntimeMemory")
        } else if (line.contains("/etc/setenv.sh")) {
            // replace with location of custom setenv.sh
            return line.replaceAll("/etc/setenv.sh", "$platformDir/$serverParentDirName/etc/setenv.sh")
        }
        return line
    }
} else {
    // Windows filter closures...
    scriptCopyFilterClosure = { line ->
        // Look for lines where the adva service names are referenced and replace them with development service names
        if (line.contains("\\system32\\net ")) {
            if (line.contains("advams")) {
                // replace with location of custom setenv.sh
                return line.replaceAll("advams", "advamsDev")
            }
        }
        return line
    }

    // For now not converted
    fnmServerScriptCopyFilterClosure = { line ->
        return line
    }
}

task binCopy(type: Copy) {
    from fileTree("$fspRoot/bin") {
        exclude("fnm.server")
    }

    filter scriptCopyFilterClosure

    into "$serverRoot/bin"

    // Set executable bits
    fileMode 0755
}

task fnmServerCopy(type: Copy) {
    from fileTree("$fspRoot/bin") {
        include("fnm.server")
    }

    filter fnmServerScriptCopyFilterClosure

    into "$serverRoot/bin"

    // Set executable bits
    fileMode 0755
}

task flexnetlsCopy(type: Copy) {
    from "$fspRoot/flexnetls"

    into "$serverRoot/flexnetls"

    // Set executable bits
    fileMode 0755
}

task scriptsCopy(type: Copy) {
    from fileTree("$fspRoot/scripts")

    filter scriptCopyFilterClosure

    into "$serverRoot/scripts"

    // Set executable bits
    fileMode 0755
}

task postgresCopy(type: Copy) {
    // Be sure the postgres applications have been linked into the root of the project
    // for linux before copying to platform directory.
    dependsOn("configureLinuxPostgres")

    from fileTree("$fspRoot/postgres")

    into "$serverRoot/postgres"
}

task setupDeploy(type: Copy) {
    // Ensure install war tasks are executed
    dependsOn([getTasksByName('installWar', true)])
    dependsOn([getTasksByName('replaceWarFile', true)])
    into("$serverRoot/ws/deploy")
}

task setupEmptyDirs() {
    outputs.dir("$serverRoot/var/db.backup")

    doLast {
        new File("$serverRoot/var/db.backup").mkdirs()
        new File("$serverRoot/var/migration").mkdirs()
    }
}

task createEmptyCustomFnmProperties() {
    mustRunAfter(':spotless')
    doLast {
        def customPropertiesPath = "$serverRoot/fnm.custom.properties"
        File customFnmProperties = new File(customPropertiesPath)
        // Ensure the serverRoot directory exists, which may not be the case for buildPlatform=true
        if (!customFnmProperties.getParentFile().exists()) {
            customFnmProperties.getParentFile().mkdirs()
        }
        if (! customFnmProperties.exists() ) {
            customFnmProperties.text = "# Put your personal changes here"
        }
    }
}

task createCustomFnmProperties() {
    mustRunAfter(':spotless')

    File customFnmProperties = new File("$serverRoot/fnm.custom.properties")

    outputs.file(customFnmProperties)
    inputs.property('SnmpTrapPort', SnmpTrapPort)
    inputs.property('flexeraServer', flexeraServer)
    inputs.property('enableSDN', enableSDN)
    inputs.property('backupFlexeraServer', backupFlexeraServer)
    inputs.property('bypassLicenses', bypassLicenses)
    inputs.property('fnmPropertiesOverride', fnmPropertiesOverride)
    inputs.property('defaultPort', defaultPort)

    doLast {

        String licenseProperties

        if (bypassLicenses.isEmpty()) {
            licenseProperties = "com.adva.fnm.option.flexeraServer.ipaddress=$flexeraServer\n" +
                    "com.adva.fnm.option.backupFlexeraServer.ipaddress=$backupFlexeraServer\n"
        } else {
            licenseProperties = "com.adva.option.flexera.bypassLicenses=$bypassLicenses\n"
        }

        // NOTE: the fnmPropertiesOverride property can embed a new line (\n), but if this is done in an
        // command line argument or environment variable this will be a literal \n and not a NEW LINE.
        // Be sure to replace the '\n' sequence with a literal NEW LINE before writing out to the file.
        customFnmProperties.text = "##### Autogenerated fnm.custom.properties file based on gradle build flags #####\n" +
                "com.adva.fnm.option.trapsinkport=$SnmpTrapPort\n" +
                "com.adva.nlms.sdn.enabled=$enableSDN\n" +
                licenseProperties +
                "com.adva.fnm.option.webserver.port=$defaultPort\n" +
                fnmPropertiesOverride.replaceAll("\\\\n", "\n")+"\n";
    }
}

task setupPlatformOS() {
    dependsOn(binCopy)
    dependsOn(fnmServerCopy)
    dependsOn(flexnetlsCopy)
    dependsOn(scriptsCopy)
    dependsOn(postgresCopy)
}

/**
 * Copy all jar files required to launch mediation and frontend java applications
 * into production 'lib' directory
 */
task platformLibs(type: Copy) {
    // See note on installReports Task
    // dependsOn(mod_target(mod_frontend, 'installReports'))

    from(advaToolsJar)
    from(mod_propup.project.jar.outputs.files)
    from(jmsServerProductionWrapper)
    from(configurations.jmsServerProduction)
    from(obfuscatePasswordProductionWrapper)
    from(encryptPassphraseProductionWrapper)
    from(configurations.applicationLibs)
    into("$serverRoot/lib")

    // Exclude jasper files in copy
    exclude("com/**")

    rename { String filename ->
        return stripVersion(filename)
    }

    duplicatesStrategy = 'exclude'
}

task setupLibs(type: Copy) {
    dependsOn(platformLibs)
    from(fileTree(fspRoot) {
        include "em/**", "endorsed/**", "project/**", "ypdb-commons.jar"
    })
    into("$serverRoot/Lib")
    fileMode = 0644
}

task setupDirs() {
    // Removed output definition for this task so the task is never considered up-to-date.
    // If this task fails to run and the ssocerts directory is not created the server may not start.
    // outputs.dir("$serverRoot/ssocerts")

    doLast {
        // an empty ssocerts directory must exist or the server will not start
        new File("$serverRoot/ssocerts").mkdirs()
        // if the var/log directory does not exist the server will not start if weaveEnc=false
        new File("$serverRoot/var/log").mkdirs()
    }
}

/**
 * Element manager requires some jars to be in a specific location at runtime
 * for the FX client to work properly.  This task puts these jars in that location.
 */
task installElementManagerJars (type: Copy) {
    // soft dependency to quiet gradle errors
    mustRunAfter(":dist:win:assembleProductionBundles")

    dependsOn(installMandatoryLibsRoot)
    dependsOn(installYpDbCommons)

    from configurations.emPlugin
    into new File(clientRoot, "lib")

    rename { String filename ->
        return stripVersion(filename)
    }
    fileMode = 0644
}

task disableAdminPasswordChange() {
    doLast {
        String updateResult = dbCommand(dbAdmin, "UPDATE se_user SET change_passwd_on_next_log_req=false where name0 = 'admin'")
        // For debugging
        // String currentSetting = dbCommand(dbAdmin, "SELECT change_passwd_on_next_log_req FROM se_user where name0 = 'admin'")
        if (updateResult.contains("UPDATE 1")) {
            print("\nDisabled password change on login for user 'admin'\n")
        } else {
            print("\nFailed to Disabled password change on login for user 'admin'\n")
        }
    }
}

/**
 * Non-java tasks that must be run to build the project
 */
task configureProject() {
    dependsOn( installDocs )
    dependsOn( installMandatoryLibs )
    dependsOn( installYpDb )
    dependsOn( installYpDbCommons )
    dependsOn( installSvp )
    dependsOn( installCmsw )
    dependsOn( advaToolsJar )
    dependsOn( jmsServerProductionWrapper )
    dependsOn( obfuscatePasswordProductionWrapper )
    dependsOn( encryptPassphraseProductionWrapper )
    dependsOn( mod_target(mod_frontend, "installReports") )
    dependsOn('updateLaunchers')
    dependsOn(installElementManagerJars)

    if (configureFnmProperties) {
        dependsOn(createCustomFnmProperties)
    } else {
        dependsOn(createEmptyCustomFnmProperties)
    }

    if (buildPlatform) {
        dependsOn {
            project.tasks['setupPlatforms']
        }
    } else {
        // copy war files even if not building platform
        dependsOn(setupDeploy)
    }

    if (Os.isFamily(Os.FAMILY_WINDOWS)) {
        if (Os.isArch("amd64")) {
            dependsOn(configureWin64)
        } else {
            dependsOn(configureWin32)
        }
    } else if (Os.isName("SunOS")) {
        dependsOn(configureSolaris)
    } else {
        // Linux/Mac
        dependsOn(configureLinux)
    }

}

/**
 * Hook project configuration into the java 'classes' task.
 * This task is run by IDEA when delegating the build to gradle so hooking this task
 * in will guarantee these tasks run when building IDEA project.
 */
if (!BuildCacheOnly) {
    tasks.classes.dependsOn(configureProject)
}

/**
 * Top level project build from gradle command line
 */
task all() {
    group('enc.build')
    description('Build the ENC workspace and install resources needed for ENC execution (default task)')

    dependsOn([ getTasksByName( '_build_', true ) ])
    dependsOn(configureProject)
    dependsOn("setTcProductBuildNumber")
}

// Find path to all independently built projects to add gradlew wrapper properties.
// Used to ensure independent built projects use same gradle version as root project.
// Idea may pick a non-compatible gradle version if the gradle version can't be determined
fileTree(projectDir) {
    include 'layers/**/settings.gradle'
    include 'libraries/**/settings.gradle'
    include 'modules/**/settings.gradle'
}.files.each { File f ->
    // Copy for all projects but gnss, gradle-wrapper.properties is committed to repository
    if (!f.getCanonicalPath().endsWith('modules'+File.separator+'gnss'+File.separator+'settings.gradle')) {
        String taskName = 'copyGradleWrapper_' + f.parentFile.name

        tasks.register(taskName, Copy) {
            group('enc.build')
            description('Copy gradle.properties to independently build to $f.getParent() project to ensure proper version of gradle is used by Idea')

            from file('gradle/wrapper/gradle-wrapper.properties')
            into f.getParent() + '/gradle/wrapper'
        }

        tasks.all.dependsOn(taskName)
    }
}

task allCompiles() {
    description = "aggregate task to run 'all' and 'testClasses'"
    dependsOn all
    dependsOn([ getTasksByName( 'testClasses', true) ])
}

// The production build needs a target that will install all 3rd party artifacts which is provided below
task installExternalArtifacts() {
    if (Os.isFamily(Os.FAMILY_WINDOWS)) {
        if (Os.isArch("amd64")) {
            dependsOn(configureWin64)
        } else {
            dependsOn(configureWin32)
        }
    } else if (Os.isName("SunOS")) {
        dependsOn(configureSolaris)
    } else {
        // Linux/Mac
        dependsOn(configureLinux)
    }
    dependsOn( installYpDb )
    dependsOn( installYpDbCommons )
    dependsOn( installSvp )
    dependsOn( installCmsw )
}

task installJars(type: Copy) {
    dependsOn(advaToolsJar)
    dependsOn(jmsServerProductionWrapper)
    dependsOn(obfuscatePasswordProductionWrapper)
    dependsOn(encryptPassphraseProductionWrapper)
    dependsOn(mod_propup.project.jar)

    from(advaToolsJar)
    from(mod_propup.project.jar.outputs.files)
    from(jmsServerProductionWrapper)
    from(obfuscatePasswordProductionWrapper)
    from(encryptPassphraseProductionWrapper)
    from(configurations.jmsServerProduction) {
        exclude("**/yfiles-for-javafx*")
    }

    into("$fspRootPackage/mediation/lib")
    duplicatesStrategy = 'exclude'
}

task prepareWarArtifacts(type: Copy) {
    dependsOn([ getTasksByName( 'installWar', true ) ])
    dependsOn([getTasksByName('replaceWarFile', true)])

    from(fspRootArchives)
    into("$fspRootPackage/mediation/ws/deploy")
    include("*.war")
    fileMode = 0644
}

task createDatabase() {
    dependsOn( mod_target(mod_mediation, 'createDatabase') )
}

ext {
    ideaLauncherDest = new File("$rootProject.projectDir/.idea/runConfigurations")
    ideaLauncherDestMediation = new File(ideaLauncherDest, "Mediation_Server_Wrapper.xml")
    ideaLauncherDestFrontend = new File(ideaLauncherDest, "Javafx_Client_Wrapper.xml")
    ideaLauncherDestJms = new File(ideaLauncherDest, "JMS_Server_Wrapper.xml")
    ideaLauncherSrc = new File("$rootProject.projectDir/IDEA/runConfigurations")
    ideaLauncherSrcMediation = new File(ideaLauncherSrc, "Mediation_Server_Wrapper.xml")
    ideaLauncherSrcFrontend = new File(ideaLauncherSrc, "Javafx_Client_Wrapper.xml")
    ideaLauncherSrcJms = new File(ideaLauncherSrc, "JMS_Server_Wrapper.xml")
}

// Windows does not support symbolic links so copy files to destination
// Windows users will need to copy their changes to launchers back to IDEA/runConfigurations to commit them
task ideaConfigureWindows (type: Copy) {
    from ( ideaLauncherSrc )
    into ( ideaLauncherDest )

    inputs.dir(ideaLauncherSrc)
            .withPropertyName('inputDir')
            .withPathSensitivity(PathSensitivity.RELATIVE)

    outputs.dir(ideaLauncherDest)
            .withPropertyName('outputDir')
}

// Use symbolic link on Unix so that updates to launchers will be committed
task ideaConfigureUnix () {
    // Remove any invalid symbolic links, these will cause gradle build failures
    // This can occur when the original launcher is deleted from subversion
    for (File f : ideaLauncherDest.listFiles()) {
        if (!f.isFile() && f.getName().endsWith(".xml")) {
            // This is a launcher link that is broken
            f.delete()
        }
    }

    doLast {
        if (!briefOutput) {
            logger.quiet("Linking in IDEA run configurations to " + ideaLauncherDest.getCanonicalPath() + "\n")
        }
        // Ensure destination directory exists
        ideaLauncherDest.mkdirs()
        for (File f : ideaLauncherSrc.listFiles()) {
            File target = new File(ideaLauncherDest, f.getName())
            if (f.isFile() && !target.exists()) {
                if (f.equals(ideaLauncherSrcMediation) || f.equals(ideaLauncherSrcFrontend) || f.equals(ideaLauncherSrcJms)) {
                    // copy file, will modify content to match workspace
                    copy {
                        from f
                        into ideaLauncherDest
                    }
                } else {
                    // Use symlink
                    ant.symlink(resource: f.getCanonicalPath(), link: target.getCanonicalPath())
                }
            } else {
                // skip this file, already in place
            }
        }
    }

    inputs.dir(ideaLauncherSrc)
            .withPropertyName('inputDir')
            .withPathSensitivity(PathSensitivity.RELATIVE)

    outputs.dir(ideaLauncherDest)
            .withPropertyName('outputDir')
}

task updateLaunchers() {
    String projName = getStringPropWithDefault('ProjectName', 'enc');

    if (Os.isFamily(Os.FAMILY_WINDOWS)) {
        dependsOn(ideaConfigureWindows)
    } else {
        dependsOn(ideaConfigureUnix)
    }

    inputs.property("buildPlatform", buildPlatform)
    inputs.property("projectName", projName)
    inputs.property("weaveEnc", weaveEnc)

    inputs.files(configurations.aspectjWeaver)
    inputs.files(configurations.eclipseLinkWeaver)

    inputs.file(ideaLauncherSrcMediation)
            .withPropertyName('mediationInputFile')
            .withPathSensitivity(PathSensitivity.RELATIVE)

    inputs.file(ideaLauncherSrcFrontend)
            .withPropertyName('frontendInputFile')
            .withPathSensitivity(PathSensitivity.RELATIVE)

    inputs.file(ideaLauncherSrcJms)
            .withPropertyName('jmsInputFile')
            .withPathSensitivity(PathSensitivity.RELATIVE)

    outputs.file(ideaLauncherDestMediation)
            .withPropertyName('mediationOutputFile')

    outputs.file(ideaLauncherDestFrontend)
            .withPropertyName('frontendOutputFile')

    outputs.file(ideaLauncherSrcJms)
            .withPropertyName('jmsOutputFile')

    doLast {
        if (!briefOutput) {
            logger.quiet("Updating mediation server wrapper launcher\n")
        }

        // Convert paths to match 'buildPlatform' setting
        Node rootNode = new XmlParser().parse(ideaLauncherDestMediation)

        NodeList logList = rootNode.configuration.log_file;
        for (Node log : logList) {
            if ('Mediation Log'.equals(log.@alias)) {
                log.@path = new File(serverRoot, "var/log/mediation.log").getCanonicalPath()
            }
        }

        NodeList options = rootNode.configuration.option;
        for (Node option : options) {
            if ('WORKING_DIRECTORY'.equals(option.@name)) {
                option.@value = serverRoot.getCanonicalPath()
            } else if ('VM_PARAMETERS'.equals(option.@name)) {
                String params = option.@value
                if (weaveEnc) {
                    // Remove -javaagent from args
                    if (params.contains('-javaagent:')) {
                        option.@value = params.substring(0, params.indexOf('-javaagent:'))
                    }
                } else {
                    // Add -javaagent to args
                    if (!params.contains('-javaagent:')) {
                        String agentArgs = ""
                        configurations.aspectjWeaver.each {File f ->
                            agentArgs += " -javaagent:"+f.getCanonicalPath()
                        }
                        configurations.eclipseLinkWeaver.each {File f ->
                            agentArgs += " -javaagent:"+f.getCanonicalPath()
                        }
                        // Open relflection to java.lang package needed by java agents for weaving
                        agentArgs += " --add-opens java.base/java.lang=ALL-UNNAMED"
                        option.@value = params.trim() + agentArgs
                    }
                }
            }
        }

        // Write out new file
        ideaLauncherDestMediation.write(XmlUtil.serialize(rootNode))

        // Update frontend client launcher
        if (!briefOutput) {
            logger.quiet("Updating frontend client wrapper launcher\n")
        }

        // Convert paths to match 'buildPlatform' setting
        rootNode = new XmlParser().parse(ideaLauncherDestFrontend)

        options = rootNode.configuration.option;
        for (Node option : options) {
            if ('WORKING_DIRECTORY'.equals(option.@name)) {
                option.@value = clientRoot.getCanonicalPath()
            }
        }

        // Write out new file
        ideaLauncherDestFrontend.write(XmlUtil.serialize(rootNode))

        // Update JMS launcher
        if (!briefOutput) {
            logger.quiet("Updating JMS wrapper launcher\n")
        }

        // Convert paths to match 'buildPlatform' setting
        rootNode = new XmlParser().parse(ideaLauncherDestJms)

        options = rootNode.configuration.option;
        for (Node option : options) {
            if ('WORKING_DIRECTORY'.equals(option.@name)) {
                option.@value = serverRoot.getCanonicalPath()
            }
        }

        // Write out new file
        ideaLauncherDestJms.write(XmlUtil.serialize(rootNode))
    }
}

def void createAppLauncher(Node parent, String name, String mainClass, String moduleName,
                           String vmParams, String progParams, String workingDir, String artifactName) {
    Node config = new Node(parent, "configuration")
    config.@default="false"
    config.@name=name
    config.@type="Application"
    config.@factoryName="Application"

    if (mainClass != null) {
        new Node(config, "option", [name: "MAIN_CLASS_NAME", "value": mainClass])
    }

    if (moduleName != null) {
        new Node(config, "module", [name: moduleName])
    }

    if (vmParams != null) {
        new Node(config, "option", [name: "VM_PARAMETERS", "value": vmParams])
    }

    if (progParams != null) {
        new Node(config, "option", [name: "PROGRAM_PARAMETERS", "value": progParams])
    }

    if (workingDir != null) {
        new Node(config, "option", [name: "WORKING_DIRECTORY", "value": workingDir])
    }
    
    if (artifactName != null) {
        Node method = new Node(config, "method", [v: "2"])
        Node bOption = new Node(method, "option", [name: "BuildArtifacts", enabled: "true"])
        new Node(bOption, "artifact", [name: artifactName])
        new Node(method, "option", [name: "Make", enabled: "true"])
    }
}

def void createJarLauncher(Node parent, String name, String logName, String logPath,
                           String jarPath, String vmParams, String progParams, String workingDir) {
    Node config = new Node(parent, "configuration")
    config.@default="false"
    config.@name=name
    config.@type="JarApplication"
    config.@factoryName="JAR Application"

    Node log = new Node(config, "log_file")
    log.@alias = logName
    log.@path = logPath

    if (jarPath != null) {
        new Node(config, "option", [name: "JAR_PATH", "value": jarPath])
    }

    if (vmParams != null) {
        new Node(config, "option", [name: "VM_PARAMETERS", "value": vmParams])
    }

    if (progParams != null) {
        new Node(config, "option", [name: "PROGRAM_PARAMETERS", "value": progParams])
    }

    if (workingDir != null) {
        new Node(config, "option", [name: "WORKING_DIRECTORY", "value": workingDir])
    }

    Node altJrePath = new Node(config, "ALTERNATIVE_JRE_PATH")

    Node method = new Node(config, "method")

}

def void createGradleLauncher(Node parent, String name, String workingDir, List<String> tasks, List<String> options) {
    Node config = new Node(parent, "configuration")
    config.@default="false"
    config.@name=name
    config.@type="GradleRunConfiguration"
    config.@factoryName="Gradle"

    Node settings = new Node(config, "ExternalSystemSettings")

    new Node(settings, "option", [name: "externalProjectPath", "value": workingDir])
    
    new Node(settings, "option", [name: "externalSystemIdString", "value": "GRADLE"])
    
    options.each { String opt -> 
        new Node(settings, "option", [name: "scriptParameters", "value": opt])
    }
    
    new Node(new Node(settings, "option", [name: "taskDescriptions"]), "list")
    
    Node taskNodeList = new Node(new Node(settings, "option", [name: "taskNames"]), "list")
    tasks.each { String task -> 
        new Node(taskNodeList, "option", ["value": task])
    }
    
    new Node(settings, "option", [name: "vmOptions", "value": ""])

    Node method = new Node(config, "method")
}

task libsForSonar(type: Copy) {
    dependsOn(installJars)
    from fspRootArchives
    into fspRootLibs
}

/**
 * run JMS server
 */
task runJms(type: JavaExec) {

    workingDir = fspRoot

    mainClass = "-jar"

    args = [ "$launchWrappersDir/MainJmsJarWrapper.jar",
             "start",
    ]

    jvmArgs = ["-Xmx1G",
               "-Dactivemq.home=$fspRoot/activemq",
               "-Dactivemq.base=$fspRoot/activemq",
               "-Dactivemq.conf=$fspRoot/activemq/conf",
               "-Dactivemq.data=$fspRoot/activemq/data",
               "-Djava.rmi.server.hostname=localhost",
               "-Dorg.apache.activemq.SERIALIZABLE_PACKAGES=\"java.lang,java.util,org.apache.activemq,org.fusesource.hawtbuf,com.thoughtworks.xstream.mapper,ni,java.time,org.apache.commons.lang3.tuple,com.google.common.collect,java.beans,com.adva\"",
               "-Dlog4j.configurationFile=file:$fspRoot/activemq/conf/log4j2.xml",
    ]
}

/**
 * run mediation server
 */
task runMediation(type: JavaExec) {

    workingDir = fspRoot

    mainClass = "-jar"

    args = [ "$launchWrappersDir/LauncherMediationJarWrapper.jar",
    ]

    jvmArgs = ["-Xmx2G",
//               "-javaagent:$fspRoot/lib/aspectjweaver.jar",
               "-Djakarta.xml.bind.JAXBContextFactory=org.glassfish.jaxb.runtime.v2.ContextFactory",
               "-Djava.util.logging.config.file=$fspRoot/logging.properties",
               "-Dorg.apache.activemq.SERIALIZABLE_PACKAGES=\"java.lang,java.util,org.apache.activemq,org.fusesource.hawtbuf,com.thoughtworks.xstream.mapper,ni,java.time,org.apache.commons.lang3.tuple,com.google.common.collect,java.beans,com.adva\"",
               "-Djavax.net.ssl.keyStore=$fspRoot/activemq/conf/client.ks",
               "-Djavax.net.ssl.keyStorePassword=nmsServer",
               "-Djava.library.path=$fspRoot/lib/",
               "-Djavax.net.ssl.trustStore=$fspRoot/activemq/conf/client.ts",
               "--add-opens=java.base/java.net=ALL-UNNAMED",
               "--add-opens=java.base/sun.net.www.protocol.https=ALL-UNNAMED"
    ]
}

/**
 * run mediation server
 */
task runJavaFxClient(type: JavaExec) {

    workingDir = fspRoot

    mainClass = "-jar"

    args = [ "$launchWrappersDir/DefaultContextManagerJarWrapper.jar",
             "-application", "frontend",
             "-server", "localhost",
             "-user", "admin",
             "-password", "ChgMeNOW",
    ]

    jvmArgs = ["-Xmx500M",
               "-Dorg.apache.activemq.SERIALIZABLE_PACKAGES=\"java.lang,java.util,org.apache.activemq,org.fusesource.hawtbuf,com.thoughtworks.xstream.mapper,ni,java.time,org.apache.commons.lang3.tuple,com.google.common.collect,java.beans,com.adva\"",
    ]
}

// NMS artifactory repository configuration
def setupNmsArtifactoryRepository(repositoryHandler) {
    repositoryHandler.maven {
        name = 'nmsartifactory'

        url = "https://$artifactoryServer/artifactory/NMS"

        credentials {
            username = "nms"
            password = "NetworkManager"
        }
    }
}

// Ensure that tasks related to creating/copying files in the workspace run after spotless.
// When executing multiple tasks in parallel the spotless task can run after creating files
// resulting in unexpected results.  The built-in 'clean' task already handles this 
// ordering with respect to java compile, archiving, etc.
tasks.each { Task t ->
    if ( t.name.startsWith("prepare") ||
            t.name.startsWith("install") ||

            t.name.startsWith("configure") ||
            t.name.startsWith("setup") ||
            t.name.startsWith("package") ||
            t.name.startsWith("deploy") ) {
        t.mustRunAfter('spotless')
    }

}

task prepareOnDemandVersionInfo {
    doLast {
        File propertiesFile = file("$fspRoot/modules/nmscommon/src/main/resources/com/adva/nlms/common/version.properties")

        // Get the content of the properties file
        String content = propertiesFile.text

        // Get custom 'on demand' properties
        String vcsNumber = System.getenv('BUILD_VCS_NUMBER')
        String tcNumber = System.getenv('BUILD_NUMBER')
        String builder = System.getenv('TRIGGEREDBY')

        if (vcsNumber == null) vcsNumber = '0';
        if (tcNumber == null) vcsNumber = '0';
        if (builder == null) vcsNumber = 'unknown';

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyMMddHHmm")
        String timestamp = LocalDateTime.now().format(formatter);

        // Add comment to version.properties and 'on demand' specific settings
        content = "# On demand build for SVN $vcsNumber and TC build number $tcNumber triggered by $builder\n\n" +
                content + "\nTC_BUILD_NUMBER=$tcNumber\nVCS_NUMBER=$vcsNumber\nBUILD_TAG=$builder-$timestamp\n"

        propertiesFile.withWriter('UTF8') {BufferedWriter writer ->
            writer.write(content)
        }
    }
}

def includeProjectForSonar = { Project p, Boolean hasIncludes, Collection<String> includes, Collection<String> excludes, Collection<String> branchProjects ->
    // Check for branch differences first then check for include/exclude filtering
    if (branchProjects != null && !branchProjects.isEmpty()) {
        String rootPath = rootProject.getProjectDir().getCanonicalPath()

        // Find the location of all project source directories (NOTE currently tests are not scanned by sonar)
        Collection<File> sourceDirs = p.sourceSets.main.java.srcDirs

        // if there are any changed files in this project then sonar analysis is active
        for (File s : sourceDirs) {
            // Find workspace relative path
            String path = s.getCanonicalPath().substring(rootPath.length()+1)

            for (String f : sonarBranchChanges) {
                if (f.startsWith(path)) {
                    // At least one change in this project
                    return true
                }
            }
        }

        // if checking branch for changes and the change is not present then don't include in analysis
        return false
    }

    if (hasIncludes) {
        // include all projects in include list and not in exclude list
        // Sort of odd to include and exclude project, but honor exclusion
        if (includes.contains(p.getPath()) && !excludes.contains(p.getPath())) {
            return true
        }
    } else {
        // Include project if not in excludes
        if (!excludes.contains(p.getPath())) {
            return true
        }
    }

    return false
}

def propertyString = { String parameter, Object value ->
    return parameter + "=" + value + "\n"
}

def writeProperty = { FileWriter fw, String parameter, Object value ->
    fw.write( propertyString(parameter, value) )
}

def writeSonarConfig = { FileWriter file, String buildModules, String buildParameters ->
    // Ser Xmx for sonar-scanner-engine
    writeProperty(file, "sonar.scanner.javaOpts", sonarScannerEngineJavaOpts)

    // Write common configuration
    writeProperty(file, "sonar.profile", sonarProfile)

    // Sonar server information
    writeProperty(file, "sonar.host.url", sonarServer)
    writeProperty(file, "sonar.login", sonarToken)

    writeProperty(file, "sonar.organization", sonarOrganization)
    writeProperty(file, "sonar.projectVersion", "$Version $BuildNumber")

    // Project specific settings
    writeProperty(file, "sonar.projectKey", sonarProject)
    writeProperty(file, "sonar.projectName", sonarProjectName)


    writeProperty(file, "sonar.coverage.jacoco.xmlReportPaths",
            project.file("codecoverage/xml/code-coverage-report.xml").getCanonicalPath())

    writeProperty(file, "sonar.scm.provider", "git")

    // Disable code coverage analysis and duplicate checking for performance (as part of trial)
    // property 'sonar.coverage.exclusions', '**/*.class,**/*.java'
    writeProperty(file, "sonar.cpd.exclusions", "**/*.class,**/*.java")

    writeProperty(file, "sonar.log.level", "DEBUG")

    writeProperty(file, "sonar.verbose", "false")
    writeProperty(file, "sonar.showProfiling", "true")

    writeProperty(file, "sonar.language", "java")
    writeProperty(file, "sonar.java.source", "17")
    writeProperty(file, "sonar.sourceEncoding", "UTF-8")

    if (!sonarExclusions.isEmpty()) {
        writeProperty(file, "sonar.exclusions", sonarExclusions)
    }

    // write out project layout configuration
    file.write("sonar.modules=$buildModules\n")
    file.write("\n\n")
    file.write(buildParameters);
    file.write("\n\n")

    if (!sonarParentBranch.isEmpty() && !sonarDevBranch.isEmpty() && !sonarIncrementalID.isEmpty()) {
        writeProperty(file, "sonar.pullrequest.branch", sonarDevBranch)
        writeProperty(file, "sonar.pullrequest.base", sonarParentBranch)
        writeProperty(file, "sonar.pullrequest.key", sonarIncrementalID)
        writeProperty(file, "sonar.buildbreaker.skip", "false")
        writeProperty(file, "sonar.qualitygate.wait", "true")
        writeProperty(file, "sonar.qualitygate.timeout", "1200")
    } else if (sonarTargetBranch != null && !sonarTargetBranch.isEmpty() && sonarEnterprise == 'true') {
        // Define branch name of analysis (if set)
        // NOTE sonar.branch.name CAN NOT be set for development branch (pull request) scans
        writeProperty(file, "sonar.branch.name", sonarTargetBranch)
    }

    file.flush()
}

task generateSonarProjectConfigFile {
    mustRunAfter(clean)
    mustRunAfter(spotless)

    dependsOn(rootProject.tasks.getByName('compileJava'))
    mustRunAfter(rootProject.tasks.getByName('compileJava'))
    dependsOn(rootProject.tasks.getByName('compileTestJava'))
    mustRunAfter(rootProject.tasks.getByName('compileTestJava'))

    doFirst() {
        String buildModules = "";
        String buildParameters = "";

        String version = "$Version $BuildNumber".toString()
        String globalLibDir = rootProject.getProjectDir().getCanonicalPath() + "/lib"
        // The character string representing a new line in a team city service message
        String NL = "\n"

        boolean hasIncludes = !sonarIncludeProjects.trim().isEmpty()
        List<String> includes = sonarIncludeProjects.trim().split(',').toList()
        List<String> excludes = sonarExcludeProjects.trim().split(',').toList()

        if (!briefOutput) {
            project.logger.quiet("Generating Sonar Parameters for ENC build\n\n")
        }

        List<String> allModules = []
        rootProject.allprojects.each { Project p ->
            if (includeProjectForSonar(p, hasIncludes, includes, excludes, sonarBranchChanges)) {
                // Module settings
                String path = p.getPath()
                String sourceDirs = p.sourceSets.main.java.srcDirs.stream()
                        .filter(f -> f.exists())
                        .map(f -> f.getCanonicalPath())
                        .filter(f -> !f.toLowerCase().contains('generated'))
                        .collect(Collectors.joining(','))

                String binDirs = p.sourceSets.main.output.classesDirs.getFiles().stream()
                        .filter(f -> f.exists())
                        .map(f -> f.getCanonicalPath())
                        .collect(Collectors.joining(','))

                String libs = p.sourceSets.main.compileClasspath.getFiles().stream()
                        .filter(f -> f.exists())
                        .map(f -> f.getCanonicalPath())
                        .collect(Collectors.joining(','))

                /* Test files are not currenly analyzed
                String testDirs = p.sourceSets.test.java.srcDirs.stream()
                        .filter(f -> f.exists())
                        .map(f -> f.getCanonicalPath())
                        .collect(Collectors.joining(','))
                 */

                if (!sourceDirs.isEmpty()) {
                    path = path.replaceAll(":","_")
                    buildParameters += propertyString("${path}.sonar.projectBaseDir", p.projectDir)
                    buildParameters += propertyString("${path}.sonar.sources", sourceDirs)
                    buildParameters += propertyString("${path}.sonar.java.libraries", libs)
                    buildParameters += propertyString("${path}.sonar.java.binaries", binDirs)


                    buildParameters += NL

                    allModules.add(path)

                    if (!briefOutput) {
                        p.logger.quiet("Configured $p for sonar analysis\n");
                    }
                }
            }
        }

        if (allModules.size() == 0) {
            logger.quiet("##teamcity[buildStatus status='SUCCESS' text='{build.status.text} no static analysis required (no sources changed)']")
            runSonar.enabled = false
        }

        buildModules = allModules.stream().collect(Collectors.joining(","))

        writeSonarConfig(new FileWriter(sonarProjectConfigFile), buildModules, buildParameters)
    }
}

task runSonar(type: JavaExec) {
    // Create a capturing output stream that will both output data flowing though it to the passed final output stream
    // as well as check that data streaming through for a line that contains the passed 'pattern'.
    // For the sonar scanner the "QUALITY GATE ..." message will be output to standard error if a problem was detected.
    CapturingOutputStream capStream = new CapturingOutputStream(System.err, logger, "QUALITY GATE STATUS: FAILED - View details on ");

    dependsOn(generateSonarProjectConfigFile)

    mainClass = 'org.sonarsource.scanner.cli.Main'

    classpath = configurations.sonarCli

    jvmArgs('-Xmx6G') // this controll xmx for sonar-scaner-cli


    args('--debug')

    // If the sonar scanner finds problems it will write to standard error
    setErrorOutput(capStream)

    // Do not fail the task in JavaExec and let the 'doLast' execute and determine the correct disposition of the result
    ignoreExitValue = true

    doLast {
        if (executionResult.get().exitValue != 0) {
            String capturedLine = capStream.getMatch()
            if (capturedLine != null && !capturedLine.isEmpty()) {
                int index = capturedLine.indexOf("http")
                if (index > 0) {
                    String sonarURL = capturedLine.substring(index)
                    logger.quiet("\n##teamcity[buildStatus status='FAILURE' text='Static analysis failures - $sonarURL']\n")
                }
                throw new GradleException("Sonar analysis found problems")
            } else {
                throw new GradleException("Sonar analysis execution failed")
            }
        }
    }
}

def writeSonarShellConfig = { FileWriter file ->
    // Write common configuration
    writeProperty(file, "sonar.profile", sonarProfile)

    // Sonar server information
    writeProperty(file, "sonar.host.url", sonarServer)
    writeProperty(file, "sonar.token", sonarToken)

    writeProperty(file, "sonar.organization", sonarOrganization)
    writeProperty(file, "sonar.projectVersion", "$Version $BuildNumber")

    // Project specific settings
    writeProperty(file, "sonar.projectKey", sonarProject)
    writeProperty(file, "sonar.projectName", sonarProjectName)

    writeProperty(file, "sonar.scm.provider", "git")

    writeProperty(file, "sonar.log.level", "DEBUG")

    writeProperty(file, "sonar.verbose", "false")
    writeProperty(file, "sonar.showProfiling", "true")

    writeProperty(file, "sonar.profile", sonarProfile)
    writeProperty(file, "sonar.language", "shell")
    writeProperty(file, "sonar.shellcheck.shellcheck.path", "/usr/bin/shellcheck")

    writeProperty(file, "sonar.sourceEncoding", "UTF-8")

    if (!sonarExclusions.isEmpty()) {
        writeProperty(file, "sonar.exclusions", sonarExclusions)
    }

    // write out project layout configuration
    writeProperty(file, "sonar.modules", "root,linux,docker")
    writeProperty(file, "root.sonar.projectBaseDir", "bin")
    writeProperty(file, "root.sonar.sources", ".")
    writeProperty(file, "linux.sonar.projectBaseDir", "dist")
    writeProperty(file, "linux.sonar.sources", "lnx")
    writeProperty(file, "docker.sonar.projectBaseDir", "docker")
    writeProperty(file, "docker.sonar.sources", ".")
    file.write("\n\n")

    if (!sonarParentBranch.isEmpty() && !sonarDevBranch.isEmpty() && !sonarIncrementalID.isEmpty()) {
        writeProperty(file, "sonar.pullrequest.branch", sonarDevBranch)
        writeProperty(file, "sonar.pullrequest.base", sonarParentBranch)
        writeProperty(file, "sonar.pullrequest.key", sonarIncrementalID)
        writeProperty(file, "sonar.buildbreaker.skip", "false")
        writeProperty(file, "sonar.qualitygate.wait", "true")
        writeProperty(file, "sonar.qualitygate.timeout", "1200")
    } else if (sonarTargetBranch != null && !sonarTargetBranch.isEmpty() && sonarEnterprise == 'true') {
        // Define branch name of analysis (if set)
        // NOTE sonar.branch.name CAN NOT be set for development branch (pull request) scans
        writeProperty(file, "sonar.branch.name", sonarTargetBranch)
    }

    file.flush()
}


task generateSonarShellProjectConfigFile {
    mustRunAfter(clean)
    mustRunAfter(spotless)

    doFirst() {
        writeSonarShellConfig(new FileWriter(sonarProjectConfigFile))
    }
}

task runSonarShell(type: JavaExec) {
    // Create a capturing output stream that will both output data flowing though it to the passed final output stream
    // as well as check that data streaming through for a line that contains the passed 'pattern'.
    // For the sonar scanner the "QUALITY GATE ..." message will be output to standard error if a problem was detected.
    CapturingOutputStream capStream = new CapturingOutputStream(System.err, logger, "QUALITY GATE STATUS: FAILED - View details on ");

    dependsOn(generateSonarShellProjectConfigFile)

    mainClass = 'org.sonarsource.scanner.cli.Main'

    classpath = configurations.sonarCli

    jvmArgs('-Xmx16G')

    args('--debug')

    // If the sonar scanner finds problems it will write to standard error
    setErrorOutput(capStream)

    // Do not fail the task in JavaExec and let the 'doLast' execute and determine the correct disposition of the result
    ignoreExitValue = true

    doLast {
        if (executionResult.get().exitValue != 0) {
            String capturedLine = capStream.getMatch()
            if (capturedLine != null && !capturedLine.isEmpty()) {
                int index = capturedLine.indexOf("http")
                if (index > 0) {
                    String sonarURL = capturedLine.substring(index)
                    logger.quiet("\n##teamcity[buildStatus status='FAILURE' text='Static analysis failures - $sonarURL']\n")
                }
                throw new GradleException("Sonar analysis found problems")
            } else {
                throw new GradleException("Sonar analysis execution failed")
            }
        }
    }
}


allprojects {
    // Setup common task restrictions to ensure tasks run at the correct time
    tasks.withType(Copy).each { Task t ->
        t.mustRunAfter(':spotless')
    }
}


task setTcProductBuildNumber {
    doLast() {
        setTcParameter("PRODUCT_BUILD_NUMBER", RawBuildNumber)
    }
}

// Set parameter for team city
def setTcParameter(tcName, tcValue) {
    print("##teamcity[setParameter name='${tcName}' value='${tcValue}']\n")

}

// Here we are using sample recipe which replace used of deprecated StrBuilder class with TextStringBuilder
// for more info please look at rewrite.yml
rewrite {
    activeRecipe("org.openrewrite.java.RemoveUnusedImports")
    exclusion(
            "**/*.gradle",
            "**/*.groovy",
            "modules/gnss/**",
            "libraries/ftp_client_impl/src/main/java/com/adva/nlms/mediation/common/housekeeping/SpringAppContext.java",
            "docker/**",
            "modules/**/build/**"
    )
}

/**
 * This class will send all streamed data to the output stream passed in its constructor.
 * In addition all streamed data will be examined to see if any lines contain the 'pattern'
 * passed in its constructor.  As soon as a match is found the line is recorded and the
 * data stream is no longer examined but flows directly to the defined output stream.
 */
public class CapturingOutputStream extends FilterOutputStream {
    private static final byte NEW_LINE = '\n'
    private static final byte CARRIAGE_RETURN = '\r'
    private final Logger logger

    private ByteArrayOutputStream ostream = new ByteArrayOutputStream()

    private String pattern = null
    private String match = null

    public CapturingOutputStream(OutputStream outStream, Logger logger, String pattern) {
        super(outStream)
        this.logger = logger
        this.pattern = pattern
    }

    /**
     * Override write method to capture data as it is streamed.
     * The FilterOutputStream will call only this OutputSteam write variant when sending to the underlying OutputStream
     *
     * @param b   the {@code byte}.
     * @throws IOException
     */
    @Override
    public void write(int b) throws IOException {
        super.write(b)
        ostream.write(b)
        capture()
    }

    private void capture() {
        if (match != null) {
            // The match is already found, nothing more to do
            return
        }

        byte[] data = ostream.toByteArray()

        if (data.length < pattern.length()) {
            // Not enough data for a match so no need to check.
            // Accumulate more data before looking for a match.
            return;
        }

        String line = chop(data)

        if (line != null && line.contains(pattern)) {
            // Found the target!
            match = line

            // Install a null output stream, no more filtering needed!
            ostream = new NullByteArrayOutputStream()
        }
    }

    /**
     * Look for a line and return it.
     * If a Line is found create a new buffered reader (tossing the line found)
     * and filling with any trailing character for the next match attempt.
     *
     * @param data
     * @return
     */
    private String chop(byte[] data) {
        String line = null

        int start = 0
        int end = 0
        int max = data.length

        while (end < max) {
            if (data[end] == NEW_LINE || data[end] == CARRIAGE_RETURN) {
                if (end > start) {
                    // End of a non-empty line
                    line = new String(data, start, end-start, StandardCharsets.UTF_8)

                    // Creaete a new buffer and put any remnants from the current buffer into it.
                    ostream = new ByteArrayOutputStream()
                    ostream.write(data, end, data.length - end - 1)
                    return line
                } else {
                    // empty line so move on
                    start = ++end
                }
            } else {
                // This is not a line ending so move on
                end++
            }
        }

        return line
    }

    private String getMatch() {
        return match
    }

    private class NullByteArrayOutputStream extends ByteArrayOutputStream {
        @Override
        public synchronized void write(int b) {
            // Do nothing!
        }

        @Override
        public synchronized void write(byte[] b, int off, int len) {
            // Do nothing!
        }

        @Override
        public void writeBytes(byte[] b) {
            // Do nothing!
        }
    }
}
