/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: szymonw
 */

package com.adva.nlms.inf.api.notification;

import com.adva.nlms.inf.api.persistentobject.PersistentObjectNotificationGenerator;

import java.io.Serializable;

/**
 * This class defines information about single {@link Notification} attribute.
 * Notification carries information about its subject and subject's properties are described by set of NotificationAttributes.
 *
 * @param <T> type of notification attribute
 */
public final class NotificationAttribute<T> implements Serializable {

  private static final long serialVersionUID = 1L;

  private final static NotificationAttributeConversionPattern DO_NOTHING_CONVERTER = new DoNothingConverter();

  private transient NotificationAttributeConversionPattern conversionPattern;

  private String name;
  private String id;

  /**
   * Basic constructor. If you want to specify simple notification attribute just use it.
   *
   * @param name name of attribute
   */
  public NotificationAttribute(String name) {
    this(name, name, DO_NOTHING_CONVERTER);
  }

  /**
   * If you cannot build your notification manually (and you delegate it to some automatic generators,
   * for example {@link PersistentObjectNotificationGenerator}) you
   * may want to describe something like conversion pattern. Conversion pattern ({@link NotificationAttributeConversionPattern}) lets you
   * bind particular attribute with procedure which describe how to convert native type of attribute to the type which you wish to have in your notification.
   *
   * @param name              name of attribute
   * @param conversionPattern - converted described conversion procedure from native to the required type.
   */
  public NotificationAttribute(String name, NotificationAttributeConversionPattern conversionPattern) {
    this(name, name, conversionPattern);
  }

  /**
   * If you cannot build your notification manually (and you delegate it to some automatic generators,
   * for example {@link PersistentObjectNotificationGenerator}) you
   * may want to describe something like conversion pattern. Conversion pattern ({@link NotificationAttributeConversionPattern}) lets you
   * bind particular attribute with procedure which describe how to convert native type of attribute to the type which you wish to have in your notification.
   *
   * @param name              name of attribute
   * @param id                id of attribute
   * @param conversionPattern - converted described conversion procedure from native to the required type.
   */
  public NotificationAttribute(String name, String id, NotificationAttributeConversionPattern conversionPattern) {
    this.name = name;
    this.id = id;
    this.conversionPattern = conversionPattern;
  }

  /**
   * Perform conversion described by {@link NotificationAttribute#conversionPattern}.
   * If pattern was not specified, default one (returns value without any conversion) is used.
   *
   * @param o value related with this attributes before conversion
   * @return value related with this attribute after conversion
   */
  public Object convert(Object o) {
    return conversionPattern.convert(o);
  }

  /**
   * @return name of this attribute
   */
  public String getName() {
    return name;
  }

  public String getId() {
    return id;
  }

  @Override
  public String toString() {
    return name;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (!(o instanceof NotificationAttribute)) return false;

    NotificationAttribute that = (NotificationAttribute) o;

    return (id.equals(that.id));
  }

  @Override
  public int hashCode() {
    return id.hashCode();
  }

  static class DoNothingConverter implements NotificationAttributeConversionPattern {
    @Override
    public Object convert (Object o) {
      return o;
    }
  }
}
