/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: okumar
 */

package com.adva.nlms.common.discover.common.dto;

import com.adva.nlms.common.config.NetworkElementDTO;
import com.adva.nlms.common.topology.EthEntityType;

public class FlowPointDto extends ManagedObjectBaseDTO implements Comparable<FlowPointDto> {

    private String shortDescription;
    private String name;

    private int adminState;
    private int operState;
    private NetworkElementDTO networkElementDTO;
    private boolean accessFlow;
    private EthEntityType ethEntityType;
    private String vlansMemberList;

    public String getShortDescription() {
        return shortDescription;
    }

    public void setShortDescription(String shortDescription) {
        this.shortDescription = shortDescription;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getAdminState() {
        return adminState;
    }

    public void setAdminState(int adminState) {
        this.adminState = adminState;
    }

    public int getOperState() {
        return operState;
    }

    public void setOperState(int operState) {
        this.operState = operState;
    }

    public NetworkElementDTO getNetworkElementDTO() {
        return networkElementDTO;
    }

    public void setNetworkElementDTO(NetworkElementDTO networkElementDTO) {
        this.networkElementDTO = networkElementDTO;
    }

    public boolean isAccessFlow() { return accessFlow; }

    public void setAccessFlow(boolean accessFlow) { this.accessFlow = accessFlow; }

    public EthEntityType getEthEntityType() {
        return ethEntityType;
    }

    public void setEthEntityType(EthEntityType ethEntityType) {
        this.ethEntityType = ethEntityType;
    }

    public String getVlansMemberList() {
        return vlansMemberList;
    }

    public void setVlansMemberList(String vlansMemberList) {
        this.vlansMemberList = vlansMemberList;
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }

    @Override
    public int compareTo(FlowPointDto o) {
        return 0;
    }
}
