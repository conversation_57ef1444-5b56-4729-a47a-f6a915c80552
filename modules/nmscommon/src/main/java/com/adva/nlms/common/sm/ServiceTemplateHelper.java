/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: blee
 */
package com.adva.nlms.common.sm;

import com.adva.apps.sm.Definition;
import com.adva.apps.sm.ServiceLayer;
import com.adva.common.model.template.Group;
import com.adva.common.model.template.Keyword;
import com.adva.common.model.template.Parameter;
import com.adva.common.model.template.ParameterGroup;
import com.adva.nlms.common.config.netypes.NEType;
import com.adva.nlms.common.sm.enums.CMProvisionProfile;
import com.adva.nlms.common.sm.enums.OF1200ProvisionProfile;
import com.adva.nlms.common.sm.enums.OF400ProvisionProfile;
import com.adva.nlms.common.sm.enums.TeraFlexProvisionProfile;
import com.adva.nlms.common.yp.JuniperParameterEnums;
import com.adva.nlms.common.yp.db.CardType;
import com.adva.nlms.common.yp.db.YellowPageParameter;
import com.adva.nlms.common.yp.db.YellowPageParameterValue;
import com.adva.nlms.common.yp.db.YellowPageQueryParameter;
import com.adva.yp.api.model.dictionary.YPEntityClassType;
import com.adva.yp.api.model.dictionary.YPInstanceType;
import com.adva.yp.api.model.dictionary.YPParameterType;
import com.adva.yp.api.model.dictionary.enums.ADMIN;
import com.adva.yp.api.model.dictionary.enums.ALSMODE;
import com.adva.yp.api.model.dictionary.enums.APS_HOLDOFF;
import com.adva.yp.api.model.dictionary.enums.BEHAVE;
import com.adva.yp.api.model.dictionary.enums.BIP;
import com.adva.yp.api.model.dictionary.enums.CAP__PROVISION;
import com.adva.yp.api.model.dictionary.enums.CDC_MODE;
import com.adva.yp.api.model.dictionary.enums.CDC_OPR;
import com.adva.yp.api.model.dictionary.enums.CDC_RANGE;
import com.adva.yp.api.model.dictionary.enums.CHANNEL_PLAN;
import com.adva.yp.api.model.dictionary.enums.CHANNEL_RX__PROVISION;
import com.adva.yp.api.model.dictionary.enums.CHANNEL__PROVISION;
import com.adva.yp.api.model.dictionary.enums.CHAN_BW;
import com.adva.yp.api.model.dictionary.enums.CHARANGE__PROVISION;
import com.adva.yp.api.model.dictionary.enums.CHA__SPC;
import com.adva.yp.api.model.dictionary.enums.CODEGAIN;
import com.adva.yp.api.model.dictionary.enums.DEPLOY;
import com.adva.yp.api.model.dictionary.enums.DISCO;
import com.adva.yp.api.model.dictionary.enums.ENCODE;
import com.adva.yp.api.model.dictionary.enums.EOC_CRY;
import com.adva.yp.api.model.dictionary.enums.ERRFWD;
import com.adva.yp.api.model.dictionary.enums.FEC;
import com.adva.yp.api.model.dictionary.enums.FENDCOM;
import com.adva.yp.api.model.dictionary.enums.LANE_CHANNEL1;
import com.adva.yp.api.model.dictionary.enums.LANE_CHANNEL2;
import com.adva.yp.api.model.dictionary.enums.LSROFFDLY;
import com.adva.yp.api.model.dictionary.enums.LSROFFONTM;
import com.adva.yp.api.model.dictionary.enums.MAP;
import com.adva.yp.api.model.dictionary.enums.MODE;
import com.adva.yp.api.model.dictionary.enums.MODNETFEC;
import com.adva.yp.api.model.dictionary.enums.MUXMETHOD;
import com.adva.yp.api.model.dictionary.enums.ODTU_TYPE;
import com.adva.yp.api.model.dictionary.enums.PATHID;
import com.adva.yp.api.model.dictionary.enums.PAUSE_RCV;
import com.adva.yp.api.model.dictionary.enums.PAUSE_TRMT;
import com.adva.yp.api.model.dictionary.enums.PLUGTYP__PROVISION;
import com.adva.yp.api.model.dictionary.enums.PMODE__PROVISION;
import com.adva.yp.api.model.dictionary.enums.PORTROLE__PROVISION;
import com.adva.yp.api.model.dictionary.enums.PT;
import com.adva.yp.api.model.dictionary.enums.RATELIMIT;
import com.adva.yp.api.model.dictionary.enums.RATE__PROVISION;
import com.adva.yp.api.model.dictionary.enums.REACH__PROVISION;
import com.adva.yp.api.model.dictionary.enums.SIGDEF;
import com.adva.yp.api.model.dictionary.enums.STUFF;
import com.adva.yp.api.model.dictionary.enums.TERM;
import com.adva.yp.api.model.dictionary.enums.TIMING;
import com.adva.yp.api.model.dictionary.enums.TIMMODE_ODU;
import com.adva.yp.api.model.dictionary.enums.TIMMODE_OTU;
import com.adva.yp.api.model.dictionary.enums.TIMMODE_S;
import com.adva.yp.api.model.dictionary.enums.TRC_FORM_S;
import com.adva.yp.api.model.dictionary.enums.TYPE__EQUIPMENT;
import com.adva.yp.api.model.dictionary.enums.TYPE__FACILITY;
import com.adva.yp.api.model.dictionary.enums.UTAG__PROVISION;
import com.adva.yp.api.model.dictionary.enums.VCGTYPE;

import java.text.NumberFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * It provides service template helper methods common for the client and the server.
 */
public class ServiceTemplateHelper {
    private static final String SERVICE_DEFINITION_XML = "serviceDefinition.xml";
    private static final Map<Group, ParameterGroup> group2Templates = new HashMap<>();
    private static final Map<Keyword, Map<String, String>> keywordToNameMap = new HashMap<>();
    private static final Map<Keyword, Map<String, String>> keywordToHelpMap = new HashMap<>();
    private static final Map<Keyword, Map<String, String>> nameToKeywordMap = new HashMap<>();
    private static final List<String> JNX_WAVELENGTH = new ArrayList<>();

    static {
        ParameterGroup template = ParameterGroup.unmarshal(ServiceTemplateHelper.class.getResource(SERVICE_DEFINITION_XML));
        validate(template);

        for (ParameterGroup pg : template.getParameterGroups()) {
            pg.removeParent();
            setParameterLabel(pg);
            Group[] groups = Group.valueOfString(pg.getGroup().split("\\|"));
            for (Group g : groups) {
                group2Templates.put(g, pg);
            }
        }

        for (JuniperParameterEnums.JNX_WAVELENGTH value : JuniperParameterEnums.JNX_WAVELENGTH.values()) {
            JNX_WAVELENGTH.add(value.getKeyword());
        }

        initMapping();
    }

    private ServiceTemplateHelper() {
    }

    /**
     * Gets a template defined in serviceDefinition.xml.
     *
     * @param name the name.
     * @return the parameter group if defined, or empty one.
     */
    public static ParameterGroup getTemplate(Group name) {
        ParameterGroup template = group2Templates.get(name);
        template = (template == null ? new ParameterGroup() : (ParameterGroup) template.clone());
        template.setGroup(name.toString());
        if (name == Group.CARD_PARAMETERS) {
            return template;
        }

        for (ParameterGroup subPG : template.getParameterGroups()) {
            Group group = Group.valueOfString(subPG.getGroup());
            switch (group) {
                case NETWORK_PLUGS:
                    subPG.addParameterGroup(getTemplate(Group.NETWORK_PLUG));
                    subPG.addParameterGroup(getTemplate(Group.NETWORK_PLUG));
                    break;
                case NETWORK_PORTS:
                    subPG.addParameterGroup(getTemplate(Group.Network_Interface));
                    subPG.addParameterGroup(getTemplate(Group.Network_Interface));
                    break;
                case XRO:
                    subPG.addParameterGroup(getTemplate(Group.XRO_LINKS));
                    subPG.addParameterGroup(getTemplate(Group.XRO_NODES));
                default:
                    subPG.copyParameterGroup(getTemplate(group));
            }
        }
        return template;
    }

    public static void setParameterLabel(ParameterGroup template) {
        for (Parameter p : template.getParameters()) {
            Keyword keyword = Keyword.valueOfString(p.getKeyword());
            p.setLabel(keyword.getLabel());
        }

        for (ParameterGroup pg : template.getParameterGroups()) {
            setParameterLabel(pg);
        }
    }

    public static YellowPageParameter createYellowPageParameter(Parameter plug, Parameter rate, Parameter reach) {
        TYPE__EQUIPMENT equipmentType;
        try {
            equipmentType = (plug == null || plug.getValue() == null) ? null : TYPE__EQUIPMENT.valueFromKeyword(plug.getValue());
        } catch (IllegalArgumentException e) {
            equipmentType = null;
        }

        List<YellowPageQueryParameter> conditions = new ArrayList<>();
        if (rate != null && rate.getValue() != null) {
            conditions.add(new YellowPageQueryParameter(YPEntityClassType.Equipment, YPParameterType.RATE__PROVISION, rate.getValue()));
        }
        if (reach != null && reach.getValue() != null) {
            conditions.add(new YellowPageQueryParameter(YPEntityClassType.Equipment, YPParameterType.REACH__PROVISION, reach.getValue()));
        }

        YellowPageParameter ypp = new YellowPageParameter(YPParameterType.TYPE__FACILITY, YPEntityClassType.Equipment, equipmentType);
        ypp.setConditionsAsList(conditions);
        return ypp;
    }

    private static Group getGroup(YellowPageQueryParameter qp, Group defaultGroup) {
        switch (qp.getYpEntityClassType()) {
            case Client_Interface:
            case ParentEntity:
                return Group.Client_Interface;
            case Client_Group_Interface:
                return Group.Client_Group_Interface;
            case Client_SingleLane_Interface:
                return Group.Client_SingleLane_Interface;
            case Port_Interface, Port_Group_Interface:
                return defaultGroup;
            case Network_Interface:
                return Group.Network_Interface;
            case Virtual_Interface:
                return Group.VIRTUAL_INTERFACE;
            case Client_Virtual_Interface:
                return Group.Client_Virtual_Interface;
            case Client_Virtual_Group_Interface:
                return Group.Client_Virtual_Group_Interface;
            case Client_Virtual_Group_Interface_Layer:
                return Group.Client_Virtual_Group_Interface_Layer;
            case Client_Virtual_SingleLane_Interface:
                return Group.Client_Virtual_SingleLane_Interface;
            case Equipment:
                switch (qp.getYpParameterType()) {
                    case CAP__PROVISION:
                    case BAND__PROVISION:
                    case MUXMETHOD:
                    case MODE:
                    case MAP:
                    case MODNETFEC:
                    case DEPLOY:
                        return Group.MODULE;
                    case CPLUGS:
                    case CRATES:
                    case PLUGTYP__PROVISION:
                        return Group.CLIENT_PLUG;
                    case RATES:
                    case PLUGS:
                    case NRATES:
                    case NPLUGS:
                        return Group.NETWORK_PLUG;
                    case INTERFACE:
                        return Group.Client_Interface;
                    default:
                        return null;
                }
            case Payload_Channel_Interface:
                return Group.Payload_Channel_Interface;
            default:
                return null;
        }
    }

    private static Keyword getKeyword(YellowPageQueryParameter qp) {
        switch (qp.getYpParameterType()) {
            case CPLUGS:
            case NPLUGS:
            case PLUGS:
                return Keyword.TYPE__EQUIPMENT;
            case CRATES:
            case RATES:
            case NRATES:
                return Keyword.RATE__PROVISION;
            case INTERFACE:
                return Keyword.AID;
            default:
                return Keyword.valueOfString(qp.getYpParameterType().getKeyword());
        }
    }

    public static void updateValues(Parameter p, boolean conditional, Function<YellowPageParameter, List<String>> getValues) {
        if (p == null || Keyword.valueOfString(p.getKeyword()) == null || p.getYellowPageParameter() == null || p.getParent() == null) {
            return;
        }

        boolean conditionsChanged = updateConditions(p, conditional);
        boolean init = p.getYellowPageParameter().getConditions().isEmpty() && p.getValues().isEmpty();
        List<String> values = (conditionsChanged || init) ? getValues.apply(p.getYellowPageParameter()) : p.getValues();
        Keyword keyword = Keyword.valueOfString(p.getKeyword());
        ParameterGroup pg = p.getParent();

        switch (keyword) {
            case ADMIN,
            BAND__PROVISION,
            BEHAVE,
            BIP,
            CAP__PROVISION,
            CDC_OPR,
            CIPHER,
            CDC_MODE,
            CDC_RANGE,
            CHA__SPC,
            DEPLOY,
            DISCO,
            ERRFWD,
            FENDCOM,
            MAP,
            MODE,
            MUXMETHOD,
            ODTU_TYPE,
            ODU_TRIB_PORT,
            ODU_TRIB_SLOT,
            PATHID,
            PT,
            RATE__PROVISION,
            REACH__PROVISION,
            STUFF,
            TERM,
            TIMING,
            TIMMODE_ODU,
            TIMMODE_OTU,
            TIMMODE_S,
            TRC_FORM_S,
            VCGTYPE,
            VETH_AID,
            PORTROLE__PROVISION,
            PMODE__PROVISION,
            PAUSE_RCV,
            PAUSE_TRMT,
            UTAG__PROVISION,
            SIGDEF,
            CODEGAIN,
            LANEGROUP__PROVISION,
            RATELIMIT,
            SDHT_PCSL,
            ENCODE,
            CHAN_BW,
            EOC_CRY,
            LSROFFDLY, // see LSROFFONTM, "DISABLE" if parameterPage.isClientChannelCardProtection()?
            FEC,
            MODNETFEC,
            TYPE__EQUIPMENT, // plugs
            PLUGTYP__PROVISION,
            LSROFFONTM, // default value "DISABLE" if parameterPage.isClientChannelCardProtection(), per artf198091.
            ALSMODE, // if parameterPage.isClientChannelCardProtection(), only values are "NONE", FNM19429/artf158059
            LANE1__PROVISION, // not to force the user to pick a channel per artf192950.
            MODULATION,
            CONSTELLATION,
            FLTSP,
            CHANNEL_PLAN:
                p.setTooltip(values.isEmpty() ? "N/A" : null);
                break;

            case CHARANGE__PROVISION:
                if (values.isEmpty()) {
                    values.add(null);
                    for (CHARANGE__PROVISION cp : CHARANGE__PROVISION.values()) {
                        if (!cp.getKeyword().equals("*")) {
                            values.add(cp.getKeyword());
                        }
                    }
                }
                break;

            case FRACTION:
                p.setTooltip(values.isEmpty() ? "N/A" : null);
                String frValueRange = "0[2|6]"; // defaultValue[min|max]
                p.setValid(!isValueOutOfFloatRange(p.getValue(), frValueRange));
                break;

            case JNX_WAVELENGTH:
                values = new ArrayList<>(JNX_WAVELENGTH);
                break;

            case JNX_FEC:
                values = Arrays.asList("SD-FEC", "HF-FEC", "GFEC");
                break;

            case JNX_TX_OPTICAL_POWER:
                p.setDefaultValue("0");
                values = Collections.singletonList("TBD");
                break;

            case TYPE__FACILITY:
                if (p.getYellowPageParameter() == null || values.isEmpty() || pg.getParent() == null) {
                    break;
                }

                // port dependency
                if (p.getYellowPageParameter() != null &&
                        (p.getYellowPageParameter().getYpEntityClassType() == YPEntityClassType.Client_Interface ||
                            p.getYellowPageParameter().getYpEntityClassType() == YPEntityClassType.Client_Group_Interface)) {
                    String aid = parseAID(pg.getParameter(Keyword.AID));
                    if (aid == null) {
                        aid = parseAID(pg.getParameter(Keyword.NAME));
                    }
                    if (aid != null) {
                        YellowPageParameter ypp = new YellowPageParameter(YPParameterType.TYPE__FACILITY, p.getYellowPageParameter().getYpEntityClassType(), p.getYellowPageParameter().getEquipmentKeyword());
                        ypp.setYpInstanceType(YPInstanceType.MODE);
                        ypp.setConditions(
                            new YellowPageQueryParameter(YPEntityClassType.Equipment, YPParameterType.INTERFACE, aid),
                            new YellowPageQueryParameter(YPEntityClassType.Equipment, YPParameterType.MODE, getModeValue(pg))
                        );
                        List<String> vs = getValues.apply(ypp);
                        if (!vs.isEmpty()) { // impossible or NA or hybrid which do not support MODE
                            values.retainAll(vs);
                        } else {
                            //hybrid card handler
                            ypp = new YellowPageParameter(YPParameterType.TYPE__FACILITY, p.getYellowPageParameter().getYpEntityClassType(), p.getYellowPageParameter().getEquipmentKeyword());
                            vs = getValues.apply(ypp);
                            if (!vs.isEmpty()) { // impossible or NA
                                values.retainAll(vs);
                            }
                        }
                    }
                }

                // plug dependency
                ParameterGroup plugPG = null;
                ParameterGroup multiplexPlugPG = null;

                if (p.getYellowPageParameter().hasDependency(YPParameterType.CPLUGS, YPEntityClassType.Equipment)) {
                    plugPG = pg.getParent().findParameterGroup(Group.CLIENT_PLUG.toString());
                } else if (p.getYellowPageParameter().hasDependency(YPParameterType.NPLUGS, YPEntityClassType.Equipment)) {
                    plugPG = pg.getParent().findParameterGroup(Group.NETWORK_PLUG.toString());
                    multiplexPlugPG = pg.getParent().findParameterGroup(Group.Network_Multiplex_Interface.toString());
                }

                if (plugPG != null) {
                    Parameter plug = plugPG.findParameter(Keyword.TYPE__EQUIPMENT);
                    Parameter rate = plugPG.findParameter(Keyword.RATE__PROVISION);
                    Parameter reach = plugPG.findParameter(Keyword.REACH__PROVISION);
                    YellowPageParameter ypp = createYellowPageParameter(plug, rate, reach);
                    if (!ypp.isUnknown()) {
                        List<String> vs = getValues.apply(ypp);
                        if (p.getYellowPageParameter().getYpEntityClassType() == YPEntityClassType.Client_Group_Interface && vs.contains("F10312")) {
                            vs.add("F41250");
                            vs.add("F103125");
                        }
                        if (!vs.contains(TYPE__FACILITY.ALL.getKeyword())) {
                            values.retainAll(vs);
                        }
                    }
                }else if(multiplexPlugPG != null){
                    String facilityTypeValue = multiplexPlugPG.getParameterValue(Keyword.TYPE__FACILITY);
                    if(!values.contains(facilityTypeValue)){
                        values.add(facilityTypeValue);
                    }

                }
                break;

            case APS_HOLDOFF:
                p.setTooltip(values.isEmpty() ? "N/A" : null);
                p.setDefaultValue(APS_HOLDOFF.NONE.getKeyword());
                values.remove(APS_HOLDOFF.NONE.getKeyword());
                values.sort(Comparator.comparingInt(Integer::parseInt));
                values.add(0, APS_HOLDOFF.NONE.getKeyword()); // Add back as the first element
                break;

            case BUNDLEN:
                Set<String> vs = new HashSet<>();
                for (String v : values) {
                    if (isIntegerRangeValue(v)) {
                        vs.addAll(parseIntegerRangeValues(v));
                    } else if (isInteger(v)) {
                        vs.add(v);
                    }
                }
                values.clear();
                values.addAll(vs);
                values.sort(Comparator.comparingInt(Integer::parseInt));
                break;

            case BUNDLE:
                if (values.isEmpty()) {
                    p.setTooltip("N/A");
                    break;
                }

                Parameter bundleN = pg.getParameter(Keyword.BUNDLEN);
                if (bundleN == null || bundleN.getValues() == null || bundleN.getValues().isEmpty()) {
                    values.clear();
                    p.setTooltip("No bundle length");
                    break;
                }

                values.sort(Comparator.comparingInt(Integer::parseInt));
                Parameter bundleUsed = pg.getParameter(Keyword.BUNDLE_USED);
                List<String> valuesUsed = parseValue(bundleUsed, ",");
                int len = Integer.parseInt(bundleN.getValue() != null ? bundleN.getValue() : bundleN.getValues().get(0));

                for (int i = 0; i < values.size(); i++) {
                    int start = Integer.parseInt(values.get(i));
                    int to = start + len;
                    String bundle = "";

                    for (int j = start; j < to; j++) {
                        String value = String.valueOf(j);
                        if (!valuesUsed.contains(value)) {
                            bundle += (bundle.isEmpty() ? "" : ",") + value;
                        } else {
                            bundle = null;
                            break;
                        }
                    }
                    values.set(i, bundle);
                }

                Set<String> nul = Collections.singleton(null);
                values.removeAll(nul);
                if (values.isEmpty()) {
                    p.setTooltip("All values already in use");
                } else {
                    p.setTooltip(null);
                }
                break;

            case SDHT_L, // 6[5|9], value pattern: default[min|max]
            SDHT_MS, // 30,1~100
            SDHT_OTU, // 7,2~10
            SDPER_MS, // 7,2~10
            SDPER_OTU, // 7,2~10
            SDPER_PCS, // SDPER-PCS, 7[1|10]
            PRIO__PROVISION, // YP: 7 [0...7]; YPDB: 7[0|7]
            FREQ_OFFSET:
                if (values.isEmpty()) {
                    p.setDefaultValue(null);
                } else {
                    String value = values.get(0);
                    p.setDefaultValue(parseDefaultValue(value));
                    values = parseIntegerRangeValues(value);
                    if (values.contains(p.getDefaultValue())) {
                        values.remove(p.getDefaultValue());
                        values.add(0, p.getDefaultValue());
                    }
                }
                break;

            case TAG_TYPE__PROVISION: // e.g. 34984[1501|65535], but it should be 0x88A8,0x05DD~0xFFFF.
                if (values.isEmpty() || values.size() != 1) {
                    p.setDefaultValue(null);
                    p.setTooltip("N/A");
                    p.setValid(true);

                } else {
                    String valueRange = values.get(0);
                    values.set(0, convertIntRangeToHexRange(valueRange));
                    p.setDefaultValue(parseDefaultValue(valueRange));
                    p.setValues(values);
                    p.setTooltip(valueRange);
                    p.setValid(!isValueOutOfHexRange(p));
                }
                break;

            case PVID__PROVISION: // 1,1~4095 or 1[1|4095]
            case MAX_FRAME_SIZE__PROVISION: // 2000[1518|9600]
            case LSROFFTM: // 40,1~1000 or 40[1|1000]
            case LSRONTM: // 10,1~1000 or 10[1|1000]
            case OPTSET: // 0.0[0.0|6.0]
            case OPTSET_LANE1: // 1.0[0.0|6.0]
            case OPTSET_LANE2: // 1.0[0.0|6.0]
            case CIR: // 1000[0|1000]
                if (values.isEmpty()) {
                    p.setDefaultValue(null);
                    p.setTooltip("N/A");
                    p.setValid(true);

                } else {
                    String valueRange = values.get(0); // defaultValue[min|max]
                    p.setDefaultValue(parseDefaultValue(valueRange));
                    p.setTooltip(valueRange);
                    p.setValid(!isValueOutOfFloatRange(p.getValue(), valueRange));
                }
                break;

            case CHANNEL__PROVISION:
            case CHANNEL_RX__PROVISION:
                if (isF8Native(p) && keyword == Keyword.CHANNEL__PROVISION) {
                    validateF8Channel(p);
                    break;
                }

                if (!values.isEmpty() && values.get(0) != null) {
                    values.add(0, null); // default value
                }

                // FNMD-72742: a patch until YP is corrected.
                if (!values.isEmpty() && !values.contains("19123") && pg.isGroup(Group.Network_Interface) && isTeraFlexOrOF1200Service(p)) {
                    values.add(1, "19123");
                }

                // [FNMD-28120] requires a user to pick a virtual external channel
                // [FNMD-31573] requires a user to pick a client channel
                boolean selectionRequired = pg.isGroup(Group.Client_Interface, Group.Client_Group_Interface,
                                                       Group.VIRTUAL_EXTERNAL_CHANNEL, Group.Client_SingleLane_Interface);
                selectionRequired = p.getEditable() && !values.isEmpty() && selectionRequired;
                if (selectionRequired) {
                    values.remove(CHANNEL__PROVISION._19121.getKeyword());
                    values.remove(CHANNEL__PROVISION._19122.getKeyword());
                }
                p.setValid(!selectionRequired || p.getValue() != null);
                p.setTooltip(p.isValid() ? null : "Select a channel");
                break;

            case CHANNEL_LANE_1, CHANNEL_LANE_2:
                if (!values.isEmpty() && values.get(0) != null) {
                    values.add(0, null); // default value
                }
                break;

            default:
                p.setValid(false);
                p.setTooltip("unknown parameter");
                break;
        }

        // update values, but not setValue.
        if (!values.equals(p.getValues())) {
            p.setValues(values);
        }
    }

    /**
     * For TeraFlex card and OF1200 card add the value "19123" (value is not in YP for CHANNEL__PLAN 19600-19125-F).
     *
     * This manually adding channel should be removed and YP needs to update its values to return the correct CHANNEL__PROVISION list
     */
    //TODO: remove this when YP has added "19123" to CHANNEL__PROVISION of TERAFLEX and OF1200 cards
    private static boolean isTeraFlexOrOF1200Service(Parameter p) {
        return p.getParent() != null && p.getParent().getParent() != null &&
               p.getParent().getParent().getParameter(Keyword.CARD_TYPE) != null &&
               (TeraFlexProvisionProfile.isValidCardTypeForChannel19123(CardType.valueOfString(p.getParent().getParent().getParameter(Keyword.CARD_TYPE).getValue())) ||
                OF1200ProvisionProfile.isValidCardType(CardType.valueOfString(p.getParent().getParent().getParameter(Keyword.CARD_TYPE).getValue())) ||
                OF400ProvisionProfile.isValidCardType(CardType.valueOfString(p.getParent().getParent().getParameter(Keyword.CARD_TYPE).getValue())) ||
                CMProvisionProfile.isValidCardType(CardType.valueOfString(p.getParent().getParent().getParameter(Keyword.CARD_TYPE).getValue())));
    }

    private static boolean updateConditions(Parameter p, boolean conditional) {
        YellowPageParameter ypp = p.getYellowPageParameter();
        ParameterGroup entities = p.getParent().findParentParameterGroup(Group.CARD_PARAMETERS);
        if (ypp == null || entities == null || ypp.getDependencies().isEmpty()) {
            return false;
        }

        if (!conditional) {
            return ypp.clearConditions();
        }

        List<YellowPageQueryParameter> newConditions = new ArrayList<>();
        for (YellowPageQueryParameter dependency : ypp.getDependencies()) {
            YellowPageQueryParameter indirectDependency = getIndirectDependency(dependency, ypp);
            Group group = getGroup(indirectDependency != null ? indirectDependency : dependency, p.getParent().toGroup());
            Keyword keyword = getKeyword(indirectDependency != null ? indirectDependency : dependency);
            if (group == null || keyword == null) {
                continue;
            }

            ParameterGroup entity = entities.findParameterGroup(group.toString());
            if (entity == null) {
                continue;
            }

            Parameter vp = entity.getParameter(keyword);
            if (vp != null) {
                vp.addParameterChangeListener(p);
                String value = getValue(vp);
                if (value != null) {
                    newConditions.add(new YellowPageQueryParameter(dependency.getYpEntityClassType(), dependency.getYpParameterType(), vp.getValue()));
                }
            }
        }

        return ypp.setConditionsAsList(newConditions);
    }

    // value pattern: default[min|max]
    private static String convertIntRangeToHexRange(String value) {
        try {
            String[] values = value == null ? new String[0] : value.split("[\\[|\\]]");
            String defaultValue = Integer.toHexString(Integer.parseInt(values[0])).toUpperCase();
            String min = Integer.toHexString(Integer.parseInt(values[1])).toUpperCase();
            String max = Integer.toHexString(Integer.parseInt(values[2])).toUpperCase();
            return defaultValue + "[" + min + "|" + max + "]";
        } catch (Exception e) {
            return value;
        }
    }

    private static String getValue(Parameter p) {
        if (p.getValue() == null || p.getValue().isEmpty()) {
            return null;
        }
        if (Keyword.AID.name().equals(p.getKeyword())) {
            String[] vs = p.getValue().split("-");
            return vs[vs.length - 1];
        }
        return p.getValue();
    }

    private static YellowPageQueryParameter getIndirectDependency(YellowPageQueryParameter qp, YellowPageParameter ypp) {
        switch (qp.getYpParameterType()) {
            case PAYLOAD:
                if (qp.getYpEntityClassType() == YPEntityClassType.Virtual_Interface) {
                    return new YellowPageQueryParameter(YPEntityClassType.Client_Interface, YPParameterType.TYPE__FACILITY, qp.getValue());
                }
                break;
            case CHARANGE__PROVISION:
                return new YellowPageQueryParameter(ypp.getYpEntityClassType(), qp.getYpParameterType(), qp.getValue());
            default:
                break;
        }
        return null;
    }

    // pattern: default[min|max]
    private static boolean isIntegerRangeValue(String value) {
        String[] values = value == null ? new String[0] : value.split("[\\[|\\]]");
        return values.length == 3 && isInteger(values[1]) && isInteger(values[2]) && Integer.parseInt(values[1]) <= Integer.parseInt(values[2]);
    }

    private static boolean isInteger(String value) {
        try {
            Integer.parseInt(value);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    static String parseAID(Parameter p) {
        if (p == null || p.getValue() == null || p.getValue().isEmpty()) {
            return null;
        }
        String[] values = p.getValue().split("-");
        return values[values.length - 1];
    }

    private static String getModeValue(ParameterGroup pg) {
        ParameterGroup cardPG = pg == null ? null : pg.findParentParameterGroup(Group.CARD_PARAMETERS);
        ParameterGroup equipmentPG = cardPG == null ? null : cardPG.findParameterGroup(Group.MODULE.toString());
        Parameter p = equipmentPG == null ? null : equipmentPG.getParameter(Keyword.MODE);
        return p == null ? null : p.getValue();
    }

    // pattern: default[min|max]
    static String parseDefaultValue(String value) {
        String[] values = value == null ? new String[0] : value.split("[\\[|\\]]");
        String dv = values.length == 3 ? values[0] : null;
        return (dv == null || dv.isEmpty() || dv.equalsIgnoreCase("null")) ? null : dv;
    }

    // pattern: default[min|max]
    private static List<String> parseIntegerRangeValues(String value) {
        if (isIntegerRangeValue(value)) {
            String[] range = value.split("[\\[|\\]]");
            int start = Integer.parseInt(range[1]);
            int end = Integer.parseInt(range[2]);
            List<String> values = new ArrayList<>();

            for (int i = start; i <= end; i++) {
                values.add(String.valueOf(i));
            }
            return values;
        }

        return Collections.emptyList();
    }

    private static boolean isValueOutOfHexRange(Parameter p) {
        String value = p.getValue();
        List<String> values = p.getValues();
        if (value != null && (values == null || values.isEmpty())) {
            return true;
        }
        if (value == null) {
            return false;
        }

        // pattern: default[min|max]
        String[] range = values.get(0).split("[\\[|\\]]");
        if (values.size() != 1 || range.length != 3) {
            return true;
        }

        try {
            int min = Integer.parseInt(range[1], 16);
            int max = Integer.parseInt(range[2], 16);
            int v = Integer.parseInt(value, 16);
            return v < min || max < v;

        } catch (NumberFormatException e) {
            return true;
        }
    }

    private static boolean isValueOutOfFloatRange(String value, String valueRange) {
        if (value == null || valueRange == null) {
            return false;
        }

        // pattern: default[min|max]
        String[] range =valueRange.split("[\\[|\\]]");
        if (range.length != 3) {
            return true;
        }

        try {
            float min = Float.parseFloat(range[1]);
            float max = Float.parseFloat(range[2]);
            if (value.contains(",")) { // FNMD-41242: a patch in case of German locale.
                value = value.replace(',', '.');
            }
            float v = Float.parseFloat(value);
            boolean outOfRange = v < min || max < v;
            return outOfRange;

        } catch (NumberFormatException e) {
            return true;
        }
    }

    private static List<String> parseValue(Parameter p, String pattern) {
        String value = (p == null || p.getValue() == null || p.getValue().isEmpty()) ? null : p.getValue();
        List<String> vs = new ArrayList<>();
        if (value != null) {
            for (String v : value.split(pattern)) {
                vs.add(v.trim());
            }
        }
        return vs;
    }

    public static void validate(ParameterGroup template) {
        for (Parameter p : template.getParameters()) {
            if (Keyword.valueOfString(p.getKeyword()) == null) {
                throw new IllegalArgumentException("invalid keyword=" + p.getKeyword());
            }
        }
        for (ParameterGroup pg : template.getParameterGroups()) {
            String[] groups = pg.getGroup().split("\\|");
            for (String group : groups) {
                if (Group.valueOfString(group) == null) {
                    throw new IllegalArgumentException("invalid group=" + group);
                }
            }
            validate(pg);
        }
    }

    public static List<String> getValueNames(Keyword parameterKeyword, List<String> parameterValues) {
        return parameterValues.stream().map(v -> getValueName(parameterKeyword, v)).collect(Collectors.toList());
    }

    /**
     * Gets a value's name for a given parameter.
     */
    public static String getValueName(Keyword parameterKeyword, String parameterValue) {
        return (parameterValue == null || keywordToNameMap.get(parameterKeyword) == null) ? parameterValue :
            keywordToNameMap.get(parameterKeyword).getOrDefault(parameterValue, parameterValue);
    }

    /**
     * Gets a value's keyword for a given parameter.
     */
    public static String getValueKeyword(Keyword parameterKeyword, String parameterValue) {
        return (parameterValue == null || nameToKeywordMap.get(parameterKeyword) == null) ? parameterValue :
            nameToKeywordMap.get(parameterKeyword).getOrDefault(parameterValue, parameterValue);
    }

    /**
     * Gets a value's help for a given parameter.
     */
    public static String getValueHelp(Keyword parameterKeyword, String parameterValue) {
        return (parameterValue == null || keywordToHelpMap.get(parameterKeyword) == null) ? parameterValue :
            keywordToHelpMap.get(parameterKeyword).getOrDefault(parameterValue, parameterValue);
    }

    private static void initMapping() {
        List<YellowPageParameterValue> ypValues;
        for (Keyword keyword : Keyword.values()) {
            switch (keyword) {
                case TYPE__FACILITY:
                    ypValues = Arrays.stream(TYPE__FACILITY.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case ERRFWD:
                    ypValues = Arrays.stream(ERRFWD.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case LSROFFDLY:
                    ypValues = Arrays.stream(LSROFFDLY.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case CAP__PROVISION:
                    ypValues = Arrays.stream(CAP__PROVISION.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case ADMIN:
                    ypValues = Arrays.stream(ADMIN.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case BEHAVE:
                    ypValues = Arrays.stream(BEHAVE.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case BIP:
                    ypValues = Arrays.stream(BIP.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case CDC_OPR:
                    ypValues = Arrays.stream(CDC_OPR.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case CDC_MODE:
                    ypValues = Arrays.stream(CDC_MODE.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case CDC_RANGE:
                    ypValues = Arrays.stream(CDC_RANGE.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case CHA__SPC:
                    ypValues = Arrays.stream(CHA__SPC.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case DEPLOY:
                    ypValues = Arrays.stream(DEPLOY.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case DISCO:
                    ypValues = Arrays.stream(DISCO.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case FENDCOM:
                    ypValues = Arrays.stream(FENDCOM.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case MAP:
                    ypValues = Arrays.stream(MAP.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case MODE:
                    ypValues = Arrays.stream(MODE.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case MUXMETHOD:
                    ypValues = Arrays.stream(MUXMETHOD.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case ODTU_TYPE:
                    ypValues = Arrays.stream(ODTU_TYPE.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case PATHID:
                    ypValues = Arrays.stream(PATHID.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case PT:
                    ypValues = Arrays.stream(PT.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case RATE__PROVISION:
                    ypValues = Arrays.stream(RATE__PROVISION.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case REACH__PROVISION:
                    ypValues = Arrays.stream(REACH__PROVISION.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case STUFF:
                    ypValues = Arrays.stream(STUFF.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case TERM:
                    ypValues = Arrays.stream(TERM.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case TIMING:
                    ypValues = Arrays.stream(TIMING.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case TIMMODE_ODU:
                    ypValues = Arrays.stream(TIMMODE_ODU.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case TIMMODE_OTU:
                    ypValues = Arrays.stream(TIMMODE_OTU.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case TIMMODE_S:
                    ypValues = Arrays.stream(TIMMODE_S.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case TRC_FORM_S:
                    ypValues = Arrays.stream(TRC_FORM_S.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case VCGTYPE:
                    ypValues = Arrays.stream(VCGTYPE.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case PORTROLE__PROVISION:
                    ypValues = Arrays.stream(PORTROLE__PROVISION.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case PMODE__PROVISION:
                    ypValues = Arrays.stream(PMODE__PROVISION.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case PAUSE_RCV:
                    ypValues = Arrays.stream(PAUSE_RCV.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case PAUSE_TRMT:
                    ypValues = Arrays.stream(PAUSE_TRMT.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case UTAG__PROVISION:
                    ypValues = Arrays.stream(UTAG__PROVISION.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case SIGDEF:
                    ypValues = Arrays.stream(SIGDEF.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case CODEGAIN:
                    ypValues = Arrays.stream(CODEGAIN.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case RATELIMIT:
                    ypValues = Arrays.stream(RATELIMIT.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case ENCODE:
                    ypValues = Arrays.stream(ENCODE.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case CHAN_BW:
                    ypValues = Arrays.stream(CHAN_BW.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case EOC_CRY:
                    ypValues = Arrays.stream(EOC_CRY.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case FEC:
                    ypValues = Arrays.stream(FEC.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case MODNETFEC:
                    ypValues = Arrays.stream(MODNETFEC.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case LSROFFONTM:
                    ypValues = Arrays.stream(LSROFFONTM.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case ALSMODE:
                    ypValues = Arrays.stream(ALSMODE.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case CHANNEL_PLAN:
                    ypValues = Arrays.stream(CHANNEL_PLAN.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case CHANNEL__PROVISION:
                    ypValues = Arrays.stream(CHANNEL__PROVISION.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case CHARANGE__PROVISION:
                    ypValues = Arrays.stream(CHARANGE__PROVISION.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case PLUGTYP__PROVISION:
                    ypValues = Arrays.stream(PLUGTYP__PROVISION.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case CHANNEL_RX__PROVISION:
                    ypValues = Arrays.stream(CHANNEL_RX__PROVISION.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).collect(Collectors.toList());
                    break;
                case CHANNEL_LANE_1:
                    ypValues = Arrays.stream(LANE_CHANNEL1.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).toList();
                    break;
                case CHANNEL_LANE_2:
                    ypValues = Arrays.stream(LANE_CHANNEL2.values()).map(v -> new YellowPageParameterValue(v.getKeyword(), v.getName(), v.getHelp())).toList();
                    break;
                default:
                    ypValues = Collections.emptyList();
            }

            if (ypValues.isEmpty()) {
                continue;
            }

            Map<String, String> keywordToName = new HashMap<>();
            Map<String, String> keywordToHelp = new HashMap<>();
            Map<String, String> nameToKeyword = new HashMap<>();
            for (YellowPageParameterValue ypValue : ypValues) {
                keywordToName.put(ypValue.getKeyword(), ypValue.getName());
                keywordToHelp.put(ypValue.getKeyword(), ypValue.getHelp());
                nameToKeyword.put(ypValue.getName(), ypValue.getKeyword());
            }
            keywordToNameMap.put(keyword, keywordToName);
            keywordToHelpMap.put(keyword, keywordToHelp);
            nameToKeywordMap.put(keyword, nameToKeyword);
        }
    }

    /**
     * Returns the service layers for a service creation mode.
     *
     * @param mode the service creation mode
     * @return a list of Definition.ServiceLayer.
     */
    public static List<ServiceLayer> getServiceLayers(Definition.ServiceCreationMode mode) {
        List<ServiceLayer> layers = new ArrayList<>();
        switch (mode) {
            case PROVISION_MODE:
                layers.add(ServiceLayer.ODS);
                layers.add(ServiceLayer.OCS);
                layers.add(ServiceLayer.NTrail);
                break;
            case EXPLORE_MODE:
                layers.add(ServiceLayer.ODS);
                layers.add(ServiceLayer.OCS);
                break;
            case TRACK_MODE:
                layers.add(ServiceLayer.ODS);
                layers.add(ServiceLayer.OCS);
                layers.add(ServiceLayer.EDS);
                layers.add(ServiceLayer.ETrail);
                layers.add(ServiceLayer.NTrail);
                break;
            case CONFIGFILE_MODE:
                layers.add(ServiceLayer.EDS);
                layers.add(ServiceLayer.ETrail);
                break;
            default:
        }
        return layers;
    }

    /**
     * Filters out the parameters and parameter groups which are used by the client not the server.
     *
     * @param template the template to filter.
     */
    public static void filterOutClientParameters(ParameterGroup template) {
        EnumSet<Group> clientGroups = EnumSet.of(
            Group.WORKING_NODES,
            Group.PROTECTION_NODES,
            Group.NOMINAL_WORKING_NODES,
            Group.NOMINAL_PROTECTION_NODES,
            Group.RESTORATION_NODES);

        for (Parameter p : template.getParameters()) {
            Keyword keyword = Keyword.valueOfString(p.getKeyword());
            if (Keyword.ADMIN == keyword && !p.getVisible()) {
                template.removeParameter(p);
            }
        }

        for (ParameterGroup pg : template.getParameterGroups()) {
            Group group = Group.valueOfString(pg.getGroup());
            if (clientGroups.contains(group)) {
                template.removeParameterGroup(pg);
            } else {
                filterOutClientParameters(pg);
            }
        }
    }

    public static ParameterGroup findExternalChannelParameterGroup(ParameterGroup card) {
        ParameterGroup ech = card.findParameterGroup(Group.EXTERNAL_CHANNEL);
        if (ech == null) {
            ech = card.findParameterGroup(Group.VIRTUAL_EXTERNAL_CHANNEL);
        }
        return ech;
    }

    /**
     * Updates the following ParameterGroups from the links.
     * <p>
     * WORKING_NODES
     * PROTECTION_NODES
     * NOMINAL_PROTECTION_NODES
     * NOMINAL_WORKING_NODES
     * RESTORATION_NODES
     *
     * @param serviceDefinition the service definition.
     */
    public static void updateNodes(ParameterGroup serviceDefinition) {
        ParameterGroup sd = serviceDefinition;
        if (sd == null || !serviceDefinition.isGroup(Group.SERVICE_DEFINITION)) {
            return;
        }

        Group[][] nodeGroups = {
            {Group.WORKING_NODES, Group.WORKING_NODE, Group.WORKING_LINKS},
            {Group.PROTECTION_NODES, Group.PROTECTION_NODE, Group.PROTECTION_LINKS},
            {Group.NOMINAL_WORKING_NODES, Group.NOMINAL_WORKING_NODE, Group.WORKING_NOMINAL_LINKS},
            {Group.NOMINAL_PROTECTION_NODES,  Group.NOMINAL_PROTECTION_NODE, Group.PROTECTION_NOMINAL_LINKS},
            {Group.RESTORATION_NODES, Group.RESTORATION_NODE, Group.RESTORATION_LINKS},
        };

        ParameterGroup src = sd.getParameterGroup(Group.SOURCE_NODE);
        ParameterGroup dst = sd.getParameterGroup(Group.DESTINATION_NODE);

        for (Group[] groups : nodeGroups) {
            Group nodesGroup = groups[0];
            Group nodeGroup = groups[1];
            Group linksGroup = groups[2];

            ParameterGroup nodes = sd.getParameterGroup(nodesGroup);
            ParameterGroup links = sd.getParameterGroup(linksGroup);
            if (nodes == null || links == null) {
                continue;
            }

            nodes.clearParameterGroups();
            for (int i = 0; i < links.getParameterGroupCount(); i++) {
                ParameterGroup link = links.getParameterGroup(i);
                ParameterGroup startNode = link.getParameterGroup(Group.START_NODE);
                ParameterGroup endNode = link.getParameterGroup(Group.END_NODE);
                ParameterGroup node = ServiceTemplateHelper.getTemplate(nodeGroup);
                if (src.getId() == startNode.getId()) {
                    node.copyParameterGroup(startNode);
                    nodes.addParameterGroup(node);
                    src = endNode;
                } else if (src.getId() == endNode.getId()) {
                    node.copyParameterGroup(endNode);
                    nodes.addParameterGroup(node);
                    src = startNode;
                } else {
                    break;
                }
                // last node
                if ((i + 1) == links.getParameterGroupCount()) {
                    node = ServiceTemplateHelper.getTemplate(nodeGroup);
                    node.copyParameterGroup(src);
                    nodes.addParameterGroup(node);
                }
            }

            if (links.getParameterGroupCount() == 0) {
                ParameterGroup[] pgs = {src, dst};
                for (ParameterGroup pg : pgs) {
                    if (pg != null && !pg.isEmpty()) {
                        ParameterGroup node = ServiceTemplateHelper.getTemplate(nodeGroup);
                        node.copyParameterGroup(pg);
                        nodes.addParameterGroup(node);
                    }
                }
            }
        }
    }

    public static boolean isF8Native(Parameter p) {
        ParameterGroup parent = p.getParent();
        ParameterGroup sourceNode = parent.findParentParameterGroup(Group.SOURCE_NODE);
        ParameterGroup node = sourceNode == null ? parent.findParentParameterGroup(Group.DESTINATION_NODE) : sourceNode;
        Parameter ptp = node != null ? node.getParameter(Keyword.PRODUCT_TYPE) : null;

        return ptp != null && Objects.equals(ptp.getValue(), NEType.F8.getBrandedName());
    }

    public static Number parseNumber(String value) {
        try {
            return NumberFormat.getIntegerInstance(Locale.US).parse(value);
        } catch (NullPointerException | ParseException e) {
            return null;
        }
    }

    public static void validateF8Channel(Parameter p) {
        int min = 191237500;
        int max = 196118750;
        int step = 3125;

        Number number = parseNumber(p.getValue());
        boolean valid = p.getValue() == null || p.getValue().isEmpty() || number != null && min <= number.intValue() && number.intValue() <= max && ((number.intValue() - min) % step == 0);
        p.setValid(valid);
        p.setTooltip(valid ? null : "enter a value range " + min + " to " + max + ", step " + step);
    }

    /**
     * F7-F8 hybrid cards support Modulation from the provisioning profile
     */
    public static boolean isCardSupportingModulation(CardType ct) {
        return ct == CardType.T_MP_2D12CT    ||
                ct == CardType.T_MP_2D8CT    ||
                ct == CardType.T_MP_M8DCT    ||
                ct == CardType.T_MP_2D8DCT   ||
                ct == CardType.T_MP_2D3DT    ||
                ct == CardType.OF_2D16DCT    ||
                ct == CardType.OF_2D16DCT_SP ||
                ct == CardType.MA_2C5LT      ||
                ct == CardType.MA_2C2C3LT_A  ||
                ct == CardType.MF_M6MDT;
    }
}