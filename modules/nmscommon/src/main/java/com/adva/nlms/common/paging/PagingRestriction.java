/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: v<PERSON><PERSON><PERSON>
 */
package com.adva.nlms.common.paging;

import com.adva.common.model.DataTransferObject;
import com.adva.common.model.navigation.DisplayContext;
import com.adva.common.util.filter.FilterOperator;
import com.adva.nlms.common.sm.request.ServiceDetailsRequest;
import com.adva.common.workbench.binding.model.BindingColumn;
import com.adva.nlms.common.TopologyNodeType;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class PagingRestriction extends DataTransferObject
{

  public enum  PageCmd {TOP, UP, DOWN, BOTTOM}

  // Page area (PageArea._Alarms, ...)
  private PageArea pageArea;

  //page command (user action; default=TOP)
  private PageCmd pageCmd = PageCmd.TOP;

  // reference DTO for the requested page (for UP or DOWN commands)
  private DataTransferObject pageRefDTO;

 // Page size (user data; default=50) and offset
  private long pageSize = 50;
  private long pageOffset = 0;

  // Sorting: Field-name and sorting direction
  private String sortedFieldName;
  private BindingColumn.SortingType sortingType;
  private List<Pair<String, BindingColumn.SortingType>> sortingConditions;

  // Filter conditions: List of filters
  private List<FilterOperator> filterOperators;
  // UserId to apply restricted views
  private int userId;
  //Node-Type & IDs of the related topology elements
  private TopologyNodeType ntnType;
  private List<Integer> ids;
  //Current number of rows
  private long rowsCount = 0;
  private Boolean isServiceClassicView = null;
  private Boolean isServiceRootSelected = false;
  // request of service details containing selection-specific attribute
  private ServiceDetailsRequest serviceDetailsRequest;
  //breadcrumb display context
  private DisplayContext displayContext;
  private Boolean isPacketServiceTabSelected = false;


  public PagingRestriction(PageArea pageArea, PageCmd pageCmd, DataTransferObject pageRefDTO, long pageSize, String sortedFieldName, BindingColumn.SortingType sortingType, List<FilterOperator> filterOperators) {
    this.pageArea = pageArea;
    this.pageCmd = pageCmd;
    this.pageRefDTO = pageRefDTO;
    this.pageSize = pageSize;
    this.sortedFieldName = sortedFieldName;
    this.sortingType = sortingType;
    this.setSortingCondition(sortedFieldName, sortingType);
    this.filterOperators = filterOperators;
  }

  public long getPageSize() {
    return pageSize;
  }

  public void setPageSize(long pageSize) {
    this.pageSize = pageSize;
  }

  public long getPageOffset() {
    return pageOffset;
  }

  public void setPageOffset(long pageOffset) {
    this.pageOffset = pageOffset;
  }

  public BindingColumn.SortingType getSortingType() {
    return sortingType;
  }

  public void setSortingType(BindingColumn.SortingType sortingType) {
    this.sortingType = sortingType;
  }

  public List<Pair<String, BindingColumn.SortingType>> getSortingConditions() {
    return (sortingConditions != null) ? sortingConditions : Collections.emptyList();
  }

  public void setSortingCondition(String fieldName, BindingColumn.SortingType type) {
    sortingConditions = new ArrayList<>();
    if (StringUtils.isNotEmpty(fieldName))
      sortingConditions.add(new ImmutablePair<>(fieldName, type));
  }

  public void setSortingCondition(List<Pair<String, BindingColumn.SortingType>> conditions) {
    sortingConditions = (conditions != null) ? conditions : new ArrayList<>();
  }

  public void addSortingCondition(String fieldName, BindingColumn.SortingType type) {
    sortingConditions = ObjectUtils.defaultIfNull(sortingConditions, new ArrayList<>());
    sortingConditions.add(new ImmutablePair<>(fieldName, type));
  }

  public DataTransferObject getPageRefDTO() {
    return pageRefDTO;
  }

  public void setPageRefDTO(DataTransferObject pageRefDTO) {
    this.pageRefDTO = pageRefDTO;
  }

  public List<FilterOperator> getFilterOperators() {
    return filterOperators;
  }

  public void setFilterOperators(List<FilterOperator> filterOperators) {
    this.filterOperators = filterOperators;
  }

    public boolean hasFilterOperator(String beanName) {
        return filterOperators != null && !filterOperators.isEmpty() &&
            filterOperators.stream().anyMatch(fo -> Objects.equals(fo.getName(), beanName));
    }

  public PageArea getPageArea() {
    return pageArea;
  }

  public String getSortedFieldName() {
    return sortedFieldName;
  }

  public void setSortedFieldName(String sortedFieldName) {
    this.sortedFieldName = sortedFieldName;
  }

  public PageCmd getPageCmd() {
    return pageCmd;
  }

  public void setPageCmd(PageCmd pageCmd) {
    this.pageCmd = pageCmd;
  }

  public long getRowsCount() {
    return rowsCount;
  }

  public void setRowsCount(long rowsCount) {
    this.rowsCount = rowsCount;
  }

  public void setNodeIds(TopologyNodeType ntnType, List<Integer> ids) {
    this.ntnType = ntnType;
    this.ids = ids;
  }

  public TopologyNodeType getNtnType() {
    return ntnType;
  }

  public List<Integer> getIds() {
    return ids;
  }

  public int getUserId() {
    return userId;
  }

  public void setUserId(int userId) {
    this.userId = userId;
  }

  public Boolean getServiceClassicView() {
    return isServiceClassicView;
  }

  public void setServiceClassicView(Boolean serviceClassicView) {
    isServiceClassicView = serviceClassicView;
  }

  public ServiceDetailsRequest getServiceDetailsRequest() {
    return serviceDetailsRequest;
  }

  public void setServiceDetailsRequest(ServiceDetailsRequest serviceDetailsRequest) {
    this.serviceDetailsRequest = serviceDetailsRequest;
  }
  public DisplayContext getDisplayContext() {
    return displayContext;
  }

  public void setDisplayContext(DisplayContext displayContext) {
    this.displayContext = displayContext;
  }

  public Boolean isServiceRootSelected() {
    return isServiceRootSelected;
  }

  public void setServiceRootSelected(Boolean serviceRootSelected) {
    isServiceRootSelected = serviceRootSelected;
  }

  public Boolean getPacketServiceTabSelected() {
    return isPacketServiceTabSelected;
  }

  public void setPacketServiceTabSelected(Boolean packetServiceTabSelected) {
    isPacketServiceTabSelected = packetServiceTabSelected;
  }

    @Override
  public Object clone() throws CloneNotSupportedException {
    PagingRestriction restriction = new PagingRestriction(pageArea,pageCmd,pageRefDTO,pageSize,sortedFieldName,sortingType,filterOperators);
    restriction.setNodeIds(ntnType, ids);
    restriction.setUserId(userId);
    restriction.setRowsCount(rowsCount);
    return restriction;
  }
}
