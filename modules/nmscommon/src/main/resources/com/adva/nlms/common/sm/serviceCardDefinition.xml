<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~ Owner: blee
  -->
<parameterGroup xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="/com/adva/common/model/template/definition.xsd">

    <parameterGroup group="Module">
        <parameter keyword="ADMIN"/>
        <parameter keyword="MODE"/>
        <parameter keyword="MAP"/>
        <parameter keyword="CAP__PROVISION"/>
        <parameter keyword="MUXMETHOD"/>
        <parameter keyword="MODNETFEC"/>
        <parameter keyword="CHANNEL__PROVISION" visible="false"/>
        <parameter keyword="BAND__PROVISION"/>
        <parameter keyword="DEPLOY"/>
    </parameterGroup>

    <parameterGroup group="Network Plug">
        <parameter keyword="name"/>
        <parameter keyword="alarmState"/>
        <parameter keyword="ADMIN"/>
        <parameter keyword="TYPE__EQUIPMENT"/>
        <parameter keyword="PLUGTYP__PROVISION"/>
        <parameter keyword="RATE__PROVISION"/>
        <parameter keyword="CHARANGE__PROVISION" visible="false"/>
    </parameterGroup>

    <parameterGroup group="Client Plug">
        <parameter keyword="name"/>
        <parameter keyword="alarmState"/>
        <parameter keyword="ADMIN"/>
        <parameter keyword="TYPE__EQUIPMENT"/>
        <parameter keyword="PLUGTYP__PROVISION"/>
        <parameter keyword="RATE__PROVISION"/>
        <parameter keyword="CHANNEL__PROVISION" visible="false"/>
    </parameterGroup>

    <parameterGroup group="Virtual Interface">
        <parameter keyword="name"/>
        <parameter keyword="ADMIN"/>
        <parameter keyword="TYPE__FACILITY"/>
        <parameter keyword="CIR"/>
        <parameter keyword="PT"/>
        <parameter keyword="VCGTYPE"/>
        <parameter keyword="BUNDLEN"/>
        <parameter keyword="BUNDLE_USED" visible="false"/>
        <parameter keyword="BUNDLE"/>
    </parameterGroup>

    <parameterGroup group="Network Virtual Interface">
        <parameter keyword="name"/>
        <parameter keyword="ADMIN"/>
        <parameter keyword="TYPE__FACILITY"/>
        <parameter keyword="TERM"/>
    </parameterGroup>

    <parameterGroup group="Network VCH Interface">
        <parameter keyword="name"/>
        <parameter keyword="AID"/>
    </parameterGroup>

    <parameterGroup group="Virtual SubChannel">
        <parameter keyword="ADMIN"/>
        <parameter keyword="TYPE__FACILITY"/>
    </parameterGroup>

    <parameterGroup group="Restoration">
        <parameter keyword="CHANNEL__PROVISION" visible="false"/>
    </parameterGroup>

    <parameterGroup group="Payload Channel Interface">
        <parameter keyword="TYPE__FACILITY"/>
    </parameterGroup>

    <parameterGroup group="External Channel">
        <parameter keyword="ADMIN"/>
        <parameter keyword="CHANNEL__PROVISION"/>
        <parameter keyword="CHAN-BW"/>
        <parameter keyword="TYPE__FACILITY"/>
        <parameter keyword="AID"/>
    </parameterGroup>

    <parameterGroup group="Virtual External Channel">
        <parameter keyword="ADMIN"/>
        <parameter keyword="CHANNEL__PROVISION"/>
        <parameter keyword="CHAN-BW"/>
        <parameter keyword="TYPE__FACILITY"/>
        <parameter keyword="AID"/>
    </parameterGroup>

    <parameterGroup group="VOM EDFA">
        <parameter keyword="name"/>
        <parameter keyword="alarmState"/>
        <parameter keyword="ADMIN"/>
        <parameter keyword="TYPE__FACILITY" editable="false"/>
    </parameterGroup>

    <parameterGroup group="Network Interface">
        <parameter keyword="name"/>
        <parameter keyword="alarmState"/>
        <parameter keyword="ADMIN"/>
        <parameter keyword="MODE" editable="false"/>
        <parameter keyword="ALSMODE" visible="false" defaultValue="NONE"/>
        <parameter keyword="CHANNEL-PLAN"/>
        <parameter keyword="CHANNEL__PROVISION"/>
        <parameter keyword="CHANNEL-RX__PROVISION"/>
        <parameter keyword="TYPE__FACILITY"/>
        <parameter keyword="BEHAVE"/>
        <parameter keyword="CIPHER"/>
        <parameter keyword="CODEGAIN"/>
        <parameter keyword="CDC-RANGE"/>
        <parameter keyword="ERRFWD"/>
        <parameter keyword="FEC"/>
        <parameter keyword="LANE1__PROVISION"/>
        <parameter keyword="LSROFFDLY"/>
        <parameter keyword="OPTSET"/>
        <parameter keyword="PT"/>
        <parameter keyword="TERM"/>
        <parameter keyword="TIMING"/>
        <parameter keyword="VETH-AID"/>
        <parameter keyword="EOC-CRY"/>
        <parameter keyword="PORTROLE__PROVISION"/>
        <parameter keyword="PMODE__PROVISION"/>
        <parameter keyword="MAX-FRAME-SIZE__PROVISION"/>
        <parameter keyword="UTAG__PROVISION"/>
        <parameter keyword="PVID__PROVISION"/>
        <parameter keyword="PRIO__PROVISION"/>
        <parameter keyword="TAG-TYPE__PROVISION"/>
        <parameter keyword="MODULATION"/>
        <parameter keyword="FRACTION"/>
        <parameter keyword="CHAN-BW"/>
        <parameter keyword="ODU-TRIB-PORT"/>
        <parameter keyword="CONSTELLATION"/>
        <parameter keyword="FLTSP"/>
        <parameter keyword="FREQ-OFFSET"/>
    </parameterGroup>

    <parameterGroup group="Network Multiplex Interface">
        <parameter keyword="name"/>
        <parameter keyword="ADMIN"/>
        <parameter keyword="TYPE__FACILITY"/>
        <parameter keyword="OPTSET-LANE1"/>
        <parameter keyword="OPTSET-LANE2"/>
        <parameter keyword="LANE-CHANNEL1"/>
        <parameter keyword="LANE-CHANNEL2"/>
    </parameterGroup>

    <parameterGroup group="Client Interface">
        <parameter keyword="name"/>
        <parameter keyword="alarmState"/>
        <parameter keyword="ADMIN" defaultValue="AINS"/>
        <parameter keyword="AID"/>
        <parameter keyword="ALSMODE"/>
        <parameter keyword="BEHAVE"/>
        <parameter keyword="CHANNEL__PROVISION"/>
        <parameter keyword="CHANNEL-PLAN"/>
        <parameter keyword="CHARANGE__PROVISION" editable="false"/>
        <parameter keyword="TYPE__FACILITY"/>
        <parameter keyword="DISCO"/>
        <parameter keyword="ERRFWD"/>
        <parameter keyword="FEC"/>
        <parameter keyword="FENDCOM"/>
        <parameter keyword="LSROFFDLY"/>
        <parameter keyword="LSROFFONTM"/>
        <parameter keyword="LSROFFTM"/>
        <parameter keyword="LSRONTM"/>
        <parameter keyword="RATELIMIT"/>
        <parameter keyword="TERM"/>
        <parameter keyword="TIMING"/>
    </parameterGroup>

    <parameterGroup group="Client Virtual Interface">
        <parameter keyword="name"/>
        <parameter keyword="ADMIN"/>
        <parameter keyword="TYPE__FACILITY"/>
    </parameterGroup>

    <parameterGroup group="Client Group Interface">
        <parameter keyword="name"/>
        <parameter keyword="alarmState"/>
        <parameter keyword="ADMIN"/>
        <parameter keyword="AID"/>
        <parameter keyword="ALSMODE"/>
        <parameter keyword="BEHAVE"/>
        <parameter keyword="CHANNEL__PROVISION"/>
        <parameter keyword="TYPE__FACILITY"/>
        <parameter keyword="ERRFWD"/>
        <parameter keyword="FEC"/>
        <parameter keyword="LSROFFDLY"/>
        <parameter keyword="LSROFFONTM"/>
        <parameter keyword="LSROFFTM"/>
        <parameter keyword="LSRONTM"/>
        <parameter keyword="TERM"/>
    </parameterGroup>

    <parameterGroup group="Client Virtual Group Interface">
        <parameter keyword="name"/>
        <parameter keyword="AID"/>
        <parameter keyword="TYPE__FACILITY"/>
    </parameterGroup>

    <parameterGroup group="Client Virtual Group Interface Layer">
        <parameter keyword="name"/>
        <parameter keyword="ADMIN"/>
        <parameter keyword="TYPE__FACILITY"/>
    </parameterGroup>

    <parameterGroup group="Client Virtual SingleLane Interface">
        <parameter keyword="name"/>
        <parameter keyword="ADMIN"/>
        <parameter keyword="TYPE__FACILITY"/>
    </parameterGroup>

    <parameterGroup group="Client SingleLane Interface">
        <parameter keyword="name"/>
        <parameter keyword="alarmState"/>
        <parameter keyword="ADMIN"/>
        <parameter keyword="AID"/>
        <parameter keyword="CHANNEL__PROVISION"/>
        <parameter keyword="TYPE__FACILITY"/>
        <parameter keyword="ERRFWD"/>
        <parameter keyword="LSROFFDLY"/>
        <parameter keyword="FEC"/>
        <parameter keyword="TERM"/>
    </parameterGroup>

    <parameterGroup group="Client Protection Group">
        <parameter keyword="APS_HOLDOFF"/>
    </parameterGroup>

    <parameterGroup group="Path Protection Group">
        <parameter keyword="APS_HOLDOFF"/>
    </parameterGroup>

    <parameterGroup group="OTU Section">
        <parameter keyword="SDHT-PCSL"/>
        <parameter keyword="SDPER-PCS"/>
        <parameter keyword="SDHT-OTU"/>
        <parameter keyword="SDPER-OTU"/>
        <parameter keyword="TIMMODE-OTU"/>
    </parameterGroup>

    <parameterGroup group="SONET/SDH">
        <parameter keyword="TRC-FORM-S"/>
        <parameter keyword="TIMMODE-S"/>
        <parameter keyword="SDHT-L"/>
        <parameter keyword="SDHT-MS"/>
        <parameter keyword="SDPER-MS"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <!-- A master card has no yellow page and it's used for unknown or track services. -->
        <parameter keyword="cardType" value="MASTER-C"/>
        <parameterGroup group="Module">
            <parameter keyword="ADMIN"/>
            <parameter keyword="MODE"/>
            <parameter keyword="CHANNEL__PROVISION"/>
            <parameter keyword="JNX_WAVELENGTH"/>
        </parameterGroup>
        <parameterGroup group="Network Plug">
            <parameter keyword="name"/>
            <parameter keyword="alarmState"/>
            <parameter keyword="ADMIN"/>
            <parameter keyword="TYPE__EQUIPMENT"/>
            <parameter keyword="RATE__PROVISION"/>
            <parameter keyword="CHANNEL__PROVISION"/>
        </parameterGroup>
        <parameterGroup group="Client Plug">
            <parameter keyword="name"/>
            <parameter keyword="alarmState"/>
            <parameter keyword="ADMIN"/>
            <parameter keyword="TYPE__EQUIPMENT"/>
            <parameter keyword="RATE__PROVISION"/>
            <parameter keyword="CHANNEL__PROVISION"/>
        </parameterGroup>
        <parameterGroup group="Network Interface">
            <parameter keyword="name"/>
            <parameter keyword="alarmState"/>
            <parameter keyword="ADMIN"/>
            <parameter keyword="TYPE__FACILITY" editable="false"/>
            <parameter keyword="CHANNEL__PROVISION" editable="false"/>
            <parameter keyword="JNX_WAVELENGTH"/>
        </parameterGroup>
        <parameterGroup group="External Channel">
            <parameter keyword="ADMIN"/>
            <parameter keyword="CHANNEL__PROVISION"/>
            <parameter keyword="AID"/>
            <parameter keyword="TYPE__FACILITY"/>
        </parameterGroup>
        <parameterGroup group="Client Interface">
            <parameter keyword="name"/>
            <parameter keyword="alarmState"/>
            <parameter keyword="ADMIN"/>
            <parameter keyword="TYPE__FACILITY" editable="false"/>
            <parameter keyword="CHANNEL__PROVISION" editable="false"/>
        </parameterGroup>
        <parameterGroup group="VOM EDFA"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <!-- Juniper cards -->
        <parameter keyword="cardType" value="JNX-PIC-PTX-1X-100GE-CFP"/>
        <parameter keyword="cardType" value="JNX-PIC-PTX-2X-100GE-CFP"/>
        <parameter keyword="cardType" value="JNX-PIC-PTX-24X-10GE-SFP"/>
        <parameter keyword="cardType" value="JNX-PIC-PTX-2X-100G-OTN"/>
        <parameterGroup group="Module">
            <parameter keyword="ADMIN"/>
            <parameter keyword="MODE"/>
            <parameter keyword="CHANNEL__PROVISION"/>
            <parameter keyword="JNX_WAVELENGTH"/>
        </parameterGroup>
        <parameterGroup group="Network Interface">
            <parameter keyword="name"/>
            <parameter keyword="alarmState"/>
            <parameter keyword="ADMIN"/>
            <parameter keyword="TYPE__FACILITY" visible="false"/>
            <parameter keyword="CHANNEL__PROVISION" editable="false"/>
            <parameter keyword="JNX_WAVELENGTH"/>
        </parameterGroup>
        <parameterGroup group="External Channel"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <!-- juniper cards -->
        <parameter keyword="cardType" value="JNX-PIC-PTX-1X-100GE-CFP2-ACO"/>
        <parameterGroup group="Module">
            <parameter keyword="ADMIN"/>
            <parameter keyword="MODE"/>
            <parameter keyword="CHANNEL__PROVISION"/>
            <parameter keyword="JNX_WAVELENGTH"/>
        </parameterGroup>
        <parameterGroup group="Network Interface">
            <parameter keyword="name"/>
            <parameter keyword="alarmState"/>
            <parameter keyword="ADMIN"/>
            <parameter keyword="TYPE__FACILITY" visible="false"/>
            <parameter keyword="CHANNEL__PROVISION" editable="false"/>
            <parameter keyword="JNX_WAVELENGTH"/>
            <parameter keyword="JNX_TX_OPTICAL_POWER"/>
        </parameterGroup>
        <parameterGroup group="External Channel"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <!-- protection modules -->
        <parameter keyword="cardType" value="1PM"/>
        <parameter keyword="cardType" value="2PM"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Interface">
            <parameter keyword="name"/>
            <parameter keyword="alarmState"/>
            <parameter keyword="ADMIN"/>
            <parameter keyword="TYPE__FACILITY" editable="false"/>
            <parameter keyword="CHANNEL__PROVISION" editable="false"/>
            <parameter keyword="JNX_WAVELENGTH"/>
        </parameterGroup>
        <parameterGroup group="Client Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <!-- filters and shelves with an External Channel -->
        <parameter keyword="cardType" value="16CSM-1HU-SF"/>
        <parameter keyword="cardType" value="16CSM-SF-D"/>
        <parameter keyword="cardType" value="1CSMU-C"/>
        <parameter keyword="cardType" value="10CSMU-D"/>
        <parameter keyword="cardType" value="48CSM-1HU-D"/>
        <parameter keyword="cardType" value="1CSMU-D"/>
        <parameter keyword="cardType" value="1CSMU-EW-C"/>
        <parameter keyword="cardType" value="1CSMU-EW-D"/>
        <parameter keyword="cardType" value="2CSMU-EW-C"/>
        <parameter keyword="cardType" value="40CSM-D"/>
        <parameter keyword="cardType" value="40CSM2HU-D"/>
        <parameter keyword="cardType" value="4CSM-C"/>
        <parameter keyword="cardType" value="4CSM-D"/>
        <parameter keyword="cardType" value="4CSMU-D"/>
        <parameter keyword="cardType" value="4OPCM"/>
        <parameter keyword="cardType" value="8CCM-C80"/>
        <parameter keyword="cardType" value="8CSM-D"/>
        <parameter keyword="cardType" value="8CSM-D-P"/>
        <parameter keyword="cardType" value="8CSMU-C"/>
        <parameter keyword="cardType" value="8CSMU-D"/>
        <parameter keyword="cardType" value="9CSMU-D"/>
        <parameter keyword="cardType" value="8CSMU-D-P"/>
        <parameter keyword="cardType" value="96CSM-2HU-D"/>
        <parameter keyword="cardType" value="96CSM-4HU-D"/>
        <parameter keyword="cardType" value="CCM-8"/>
        <parameter keyword="cardType" value="EROADM-DC"/>
        <parameter keyword="cardType" value="FD-128D"/>
        <parameter keyword="cardType" value="FD-128D-2"/>
        <parameter keyword="cardType" value="FD-40D24L-TD"/>
        <parameter keyword="cardType" value="FD-48E"/>
        <parameter keyword="cardType" value="FD-48E-2"/>
        <parameter keyword="cardType" value="FD-48E-W"/>
        <parameter keyword="cardType" value="FD-64W"/>
        <parameter keyword="cardType" value="FD-8B-U"/>
        <parameter keyword="cardType" value="FD-32LE"/>
        <parameter keyword="cardType" value="FD-32LO"/>
        <parameter keyword="cardType" value="FD-32X"/>
        <parameter keyword="cardType" value="FD-8LE"/>
        <parameter keyword="cardType" value="FD-8LO"/>
        <parameter keyword="cardType" value="FD-8X"/>
        <parameter keyword="cardType" value="SP-16X4"/>
        <parameter keyword="cardType" value="SP-16X8"/>
        <parameter keyword="cardType" value="SP-1X8"/>
        <parameter keyword="cardType" value="SP-8X8"/>
        <parameter keyword="cardType" value="SP-8X4"/>
        <parameter keyword="cardType" value="RD-12RS"/>
        <parameter keyword="cardType" value="RD-32RS"/>
        <parameter keyword="cardType" value="RD-32RS-2"/>
        <parameter keyword="cardType" value="RD-4BS"/>
        <parameter keyword="cardType" value="BD-3B-8LE"/>
        <parameter keyword="cardType" value="BD-3B-8LO"/>
        <parameter keyword="cardType" value="BD-3B-8X"/>
        <parameter keyword="cardType" value="9ROADM-RS"/>
        <parameterGroup group="Module">
            <parameter keyword="ADMIN"/>
            <parameter keyword="CHANNEL__PROVISION"/>
        </parameterGroup>
        <parameterGroup group="Network Interface">
            <parameter keyword="name"/>
            <parameter keyword="alarmState"/>
            <parameter keyword="ADMIN"/>
            <parameter keyword="TYPE__FACILITY" editable="false"/>
            <parameter keyword="CHANNEL__PROVISION" editable="false"/>
        </parameterGroup>
        <parameterGroup group="External Channel"/>
        <parameterGroup group="Client Interface" visible="false">
            <parameter keyword="name"/>
            <parameter keyword="AID"/>
            <parameter keyword="alarmState"/>
            <parameter keyword="ADMIN"/>
            <parameter keyword="TYPE__FACILITY" editable="false"/>
            <parameter keyword="CHANNEL__PROVISION" editable="false"/>
        </parameterGroup>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <!-- filters and shelves with a Virtual External Channel -->
        <parameter keyword="cardType" value="32PSM-1HU"/>
        <parameter keyword="cardType" value="4PSM-S"/>
        <parameter keyword="cardType" value="5PSM"/>
        <parameter keyword="cardType" value="OPPM"/>
        <parameter keyword="cardType" value="8PSM"/>
        <parameter keyword="cardType" value="8PSM-A"/>
        <parameter keyword="cardType" value="8PSM8"/>
        <parameter keyword="cardType" value="16PSM4"/>
        <parameter keyword="cardType" value="16PSM8"/>
        <parameter keyword="cardType" value="9CCM-C96"/>
        <parameterGroup group="Module">
            <parameter keyword="ADMIN"/>
            <parameter keyword="CHANNEL__PROVISION"/>
        </parameterGroup>
        <parameterGroup group="Network Interface">
            <parameter keyword="name"/>
            <parameter keyword="alarmState"/>
            <parameter keyword="ADMIN"/>
            <parameter keyword="TYPE__FACILITY" editable="false"/>
            <parameter keyword="CHANNEL__PROVISION" editable="false"/>
        </parameterGroup>
        <parameterGroup group="Virtual External Channel"/>
        <parameterGroup group="Client Interface" visible="false">
            <parameter keyword="name"/>
            <parameter keyword="AID"/>
            <parameter keyword="alarmState"/>
            <parameter keyword="ADMIN"/>
            <parameter keyword="TYPE__FACILITY" editable="false"/>
            <parameter keyword="CHANNEL__PROVISION" editable="false"/>
        </parameterGroup>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="4TCA4G-C"/>
        <parameter keyword="cardType" value="4TCA4G-D"/>
        <parameter keyword="cardType" value="5WCA16G"/>
        <parameter keyword="cardType" value="6WCA28G"/>
        <parameter keyword="cardType" value="4WCE16G"/>
        <parameter keyword="cardType" value="2TCA2G5"/>
        <parameter keyword="cardType" value="4TCA1G3-C"/>
        <parameter keyword="cardType" value="4TCA1G3-D"/>
        <parameter keyword="cardType" value="4WCC10G"/>
        <parameter keyword="cardType" value="WCC40GT-D"/>
        <parameter keyword="cardType" value="WCC100GAES-F"/>
        <parameter keyword="cardType" value="WCC100GAES-G"/>
        <parameter keyword="cardType" value="WCC100GAESB"/>
        <parameter keyword="cardType" value="WCA10G-C"/>
        <parameter keyword="cardType" value="WCA10G-D"/>
        <parameter keyword="cardType" value="8TCE2G5-C"/>
        <parameter keyword="cardType" value="MF-M6MDT"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface"/>
        <parameterGroup group="Client Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <!--ADM mode besides other modes, so OCS is valid in addition to ODS -->
        <parameter keyword="cardType" value="4TCA4G"/>
        <parameter keyword="cardType" value="10TCCSDI10G"/>
        <parameter keyword="cardType" value="4TCC40GT-D"/>
        <parameter keyword="cardType" value="10TCC100G"/>
        <parameter keyword="cardType" value="9TCE10G"/>
        <parameter keyword="cardType" value="9TCE10GAES"/>
        <parameter keyword="cardType" value="9TCE10GAES-F"/>
        <parameter keyword="cardType" value="9TCE10GAES-G"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface"/>
        <parameterGroup group="Virtual Interface"/>
        <parameterGroup group="Client Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <!-- only ADM mode, which is bottom up so OCS layer is valid -->
        <parameter keyword="cardType" value="2PCA10G"/>
        <parameter keyword="cardType" value="10PCA10G"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Network Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="5TCE10G"/>
        <parameter keyword="cardType" value="5TCE10GAES"/>
        <parameter keyword="cardType" value="5TCE10GT-D"/>
        <parameter keyword="cardType" value="5TCE10GTAES-D"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameterGroup group="Restoration"/>
        </parameterGroup>
        <parameterGroup group="Virtual Interface"/>
        <parameterGroup group="Client Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="10TCE100G"/>
        <parameter keyword="cardType" value="10TCE100G-GF"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug">
            <parameter keyword="LANEGROUP__PROVISION"/>
        </parameterGroup>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface"/>
        <parameterGroup group="Virtual Interface"/>
        <parameterGroup group="Client Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="10TCC100GTB-D"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameter keyword="CHANNEL__PROVISION_USED"/>
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
        <parameterGroup group="Virtual Interface"/>
        <parameterGroup group="Client Interface">
            <parameterGroup group="OTU Section"/>
            <parameterGroup group="SONET/SDH"/>
        </parameterGroup>
        <parameterGroup group="Client Group Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="5TCES10G"/>
        <parameter keyword="cardType" value="5TCES10GT-D"/>
        <parameter keyword="cardType" value="5TCES10GTAES-D"/>
        <parameter keyword="cardType" value="5TCES10GAES"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameterGroup group="Restoration"/>
            <parameterGroup group="SONET/SDH"/>
        </parameterGroup>
        <parameterGroup group="Virtual Interface"/>
        <parameterGroup group="Client Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="10TCCS10GT-D"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameterGroup group="Restoration"/>
            <parameterGroup group="SONET/SDH"/>
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
        <parameterGroup group="Virtual Interface"/>
        <parameterGroup group="Payload Channel Interface"/>
        <parameterGroup group="Client Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="4TCA4GUS"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface"/>
        <parameterGroup group="Virtual Interface"/>
        <parameterGroup group="Virtual SubChannel"/>
        <parameterGroup group="Client Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="2WCA10G"/>
        <parameter keyword="cardType" value="WCA2G5"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameterGroup group="Restoration"/>
        </parameterGroup>
        <parameterGroup group="Client Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="4TCC10G-C"/>
        <parameter keyword="cardType" value="4TCC10G-D"/>
        <parameter keyword="cardType" value="4TCC10GT-D"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameterGroup group="Restoration"/>
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
        <parameterGroup group="Virtual Interface"/>
        <parameterGroup group="Client Interface">
            <parameterGroup group="SONET/SDH"/>
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="4TCC100GAES"/>
        <parameter keyword="cardType" value="4TCC100GAES-G"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameterGroup group="Restoration"/>
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
        <parameterGroup group="Virtual Interface"/>
        <parameterGroup group="Client Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="10TCC10G"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameterGroup group="OTU Section"/>
            <parameterGroup group="Virtual Interface"/>
        </parameterGroup>
        <parameterGroup group="Virtual Interface"/>
        <parameterGroup group="Client Interface">
            <parameterGroup group="SONET/SDH"/>
            <parameterGroup group="OTU Section"/>
            <parameterGroup group="Virtual Interface"/>
        </parameterGroup>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="16TCC10G"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameterGroup group="OTU Section"/>
            <parameterGroup group="Virtual Interface"/>
        </parameterGroup>
        <parameterGroup group="Client Interface">
            <parameterGroup group="SONET/SDH"/>
            <parameterGroup group="OTU Section"/>
            <parameterGroup group="Virtual Interface"/>
        </parameterGroup>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="10TCC10G-C"/>
        <parameter keyword="cardType" value="10TCC10G-D"/>
        <parameter keyword="cardType" value="10TCC10GT-D"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameterGroup group="Restoration"/>
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
        <parameterGroup group="Virtual Interface"/>
        <parameterGroup group="Payload Channel Interface"/>
        <parameterGroup group="Client Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="2TWCC2G7"/>
        <parameter keyword="cardType" value="2TWCC2G7B"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameterGroup group="SONET/SDH"/>
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
        <parameterGroup group="Virtual Interface"/>
        <parameterGroup group="Payload Channel Interface"/>
        <parameterGroup group="Client Interface">
            <parameterGroup group="SONET/SDH"/>
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="WCC2G7-1N"/>
        <parameter keyword="cardType" value="WCC2G7-C"/>
        <parameter keyword="cardType" value="WCC2G7-D"/>
        <parameter keyword="cardType" value="2WCC10G"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameterGroup group="SONET/SDH"/>
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
        <parameterGroup group="Client Interface">
            <parameterGroup group="SONET/SDH"/>
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="WCC10G-C"/>
        <parameter keyword="cardType" value="WCC10G-D"/>
        <parameter keyword="cardType" value="WCC10GT-D"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameterGroup group="Restoration"/>
            <parameterGroup group="SONET/SDH"/>
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
        <parameterGroup group="Client Interface">
            <parameterGroup group="SONET/SDH"/>
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="WCC100GT-D"/>
        <parameter keyword="cardType" value="WCC100GTB-D"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameterGroup group="Restoration"/>
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
        <parameterGroup group="Client Interface">
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="4TCC2G5"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameterGroup group="SONET/SDH"/>
        </parameterGroup>
        <parameterGroup group="Virtual Interface"/>
        <parameterGroup group="Client Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="WCE100G"/>
        <parameter keyword="cardType" value="WCE100GB"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug">
            <parameter keyword="LANEGROUP__PROVISION"/>
        </parameterGroup>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
        <parameterGroup group="Client Interface">
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <!-- XC mode supports OCS and ODS -->
        <parameter keyword="cardType" value="10WXC10G"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameterGroup group="SONET/SDH"/>
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
        <parameterGroup group="Client Interface">
            <parameterGroup group="SONET/SDH"/>
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
        <parameterGroup group="Client Virtual Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="10TCE100GB"/>
        <parameter keyword="cardType" value="10TCE100GBAES"/>
        <parameter keyword="cardType" value="10TCE100GAES"/>
        <parameter keyword="cardType" value="10TCE100GAES-F"/>
        <parameter keyword="cardType" value="10TCE100GAES-BSI"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug">
            <parameter keyword="LANEGROUP__PROVISION"/>
        </parameterGroup>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameter keyword="CHANNEL__PROVISION_USED"/>
        </parameterGroup>
        <parameterGroup group="Virtual Interface"/>
        <parameterGroup group="Client Interface"/>
        <parameterGroup group="Client Group Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="WCC-PCN-100G"/>
        <parameter keyword="cardType" value="WCC-PCN-100G-AES"/>
        <parameter keyword="cardType" value="WCC-PCN-100GB"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug">
            <parameter keyword="LANEGROUP__PROVISION"/>
        </parameterGroup>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface">
            <parameterGroup group="Restoration"/>
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
        <parameterGroup group="Client Interface">
            <parameterGroup group="OTU Section"/>
        </parameterGroup>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="MP-2B4CT"/>
        <parameter keyword="cardType" value="MP-2B4CT-CG"/>
        <parameter keyword="cardType" value="MP-2B4CT-S"/>
        <parameter keyword="cardType" value="T-MP-2D3DT"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface"/>
        <parameterGroup group="Network Multiplex Interface"/>
        <parameterGroup group="Network Virtual Interface"/>
        <parameterGroup group="Client Interface"/>
        <parameterGroup group="Client SingleLane Interface"/>
        <parameterGroup group="Client Virtual Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="T-MP-2D12CT"/>
        <parameter keyword="cardType" value="T-MP-2D8CT"/>
        <parameter keyword="cardType" value="T-MP-M8DCT"/>
        <parameter keyword="cardType" value="T-MP-2D8DCT"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface"/>
        <parameterGroup group="Network Virtual Interface"/>
        <parameterGroup group="Client Interface"/>
        <parameterGroup group="Client SingleLane Interface"/>
        <parameterGroup group="Client Virtual Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="MA-2C2C3LT-A"/>
        <parameter keyword="cardType" value="MA-B2C3LT-A"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface"/>
        <parameterGroup group="Network Virtual Interface"/>
        <parameterGroup group="Client Interface"/>
        <parameterGroup group="Client Virtual Interface"/>
        <parameterGroup group="Client Group Interface"/>
        <parameterGroup group="Client Virtual Group Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="MA-2C5LT"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface"/>
        <parameterGroup group="Network Virtual Interface"/>
        <parameterGroup group="Client Interface"/>
        <parameterGroup group="Client Virtual Interface"/>
        <parameterGroup group="Client Group Interface"/>
        <parameterGroup group="Client Virtual Group Interface"/>
        <parameterGroup group="Client Protection Group"/>
        <parameterGroup group="Path Protection Group"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="MA-B5LT"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface"/>
        <parameterGroup group="Network Virtual Interface"/>
        <parameterGroup group="Client Interface"/>
        <parameterGroup group="Client Virtual Interface"/>
        <parameterGroup group="Client Group Interface"/>
        <parameterGroup group="Client Virtual Group Interface"/>
        <parameterGroup group="Client Protection Group"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="OF-2D16DCT"/>
        <parameter keyword="cardType" value="OF-2D16DCT-SP"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface"/>
        <parameterGroup group="Network Virtual Interface"/>
        <parameterGroup group="Client Interface"/>
        <parameterGroup group="Client Virtual Interface"/>
        <parameterGroup group="Client Group Interface"/>
        <parameterGroup group="Client Virtual Group Interface"/>
        <parameterGroup group="Client SingleLane Interface"/>
        <parameterGroup group="Client Virtual SingleLane Interface"/>
        <parameterGroup group="Client Protection Group"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="AF-4X4XGT"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Plug"/>
        <parameterGroup group="Port"/>
        <parameterGroup group="Virtual Interface"/>
    </parameterGroup>

    <parameterGroup group="Card Parameters">
        <parameter keyword="cardType" value="SF-D19DCT"/>
        <parameter keyword="cardType" value="SF-D19DCT-A"/>
        <parameter keyword="cardType" value="SF-D19DCT-G"/>
        <parameterGroup group="Module"/>
        <parameterGroup group="Network Plug"/>
        <parameterGroup group="Client Plug"/>
        <parameterGroup group="Network Interface"/>
        <parameterGroup group="Network Virtual Interface"/>
        <parameterGroup group="Client Interface"/>
        <parameterGroup group="Client Virtual Interface"/>
        <parameterGroup group="Client Virtual SingleLane Interface"/>
    </parameterGroup>

</parameterGroup>
