
/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */
plugins {
    id 'com.adva.gradle.plugin.eclipse-link-weaver'
    id 'com.adva.gradle.plugin.aspectj-weaver'
}

setupMediationModule(project)

aspectjWeave {
    sourceSets = [ "main" ]

    if (briefOutput) {
        lintLevel = 'ignore'
    }
}

eclipseLinkWeave {
    masterModule mod_mediation.project
}

dependencies {
    api modep(mod_mo_model_core_api)
    api modep( mod_inf_api )
    api modep(mod_optical_router_drivers_api)
    implementation modep(mod_nmscommon)
    implementation modep(mod_persistence_common)
    implementation libs.jakarta.persistence
    implementation libs.aspectjrt
    implementation libs.commons.lang3

    aspectjpath modep(mod_mediation)
    aspectjpath modep(mod_persistence_api)
    aspectjpath modep(mod_optical_router_drivers_api)
}