/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: ricardos
 */

package com.adva.nlms.opticalrouter.persistence;

import com.adva.nlms.inf.api.notification.NotificationAttribute;
import com.adva.nlms.inf.api.notification.NotificationAttributeConversionPattern;
import com.adva.nlms.mediation.config.EntityAttributes;

import java.math.BigDecimal;
import java.util.List;

public class OpticalRouterPortAttributes extends EntityAttributes {

  public static final NotificationAttribute<List<String>> LAYER_QUALIFIERS =
    new NotificationAttribute<>("layerQualifiers");
  public static final NotificationAttribute<String> TARGET_OUTPUT_POWER =
    new NotificationAttribute<>("targetOutputPower", new BigDecimalConverter());
  public static final NotificationAttribute<List<FrequencySlotDto>> FREQUENCY_SLOTS =
    new NotificationAttribute<>("frequencySlots", new FrequencySlotConverter());
  public static final NotificationAttribute<List<WavelengthDto>> WAVELENGTHS =
    new NotificationAttribute<>("wavelengths", new WavelengthConverter());

  public OpticalRouterPortAttributes() {
    super(LAYER_QUALIFIERS, TARGET_OUTPUT_POWER, FREQUENCY_SLOTS, WAVELENGTHS);
  }

  private static String toString(BigDecimal decimal) {
    return decimal != null ? decimal.toString() : null;
  }

  // JMS does not allow serialization to BigDecimal, using String instead
  private static class BigDecimalConverter implements NotificationAttributeConversionPattern<BigDecimal> {
    @Override
    public Object convert(BigDecimal decimal) {
      return OpticalRouterPortAttributes.toString(decimal);
    }
  }

  private static class FrequencySlotConverter implements NotificationAttributeConversionPattern<List<FrequencySlot>> {
    @Override
    public Object convert(List<FrequencySlot> frequencySlots) {
      return frequencySlots.stream()
        .map(v -> new FrequencySlotDto(
          OpticalRouterPortAttributes.toString(v.getCenterFrequency()),
          OpticalRouterPortAttributes.toString(v.getSlotWidth())))
        .toList();
    }
  }

  private static class WavelengthConverter implements NotificationAttributeConversionPattern<List<Wavelength>> {
    @Override
    public Object convert(List<Wavelength> frequencySlots) {
      return frequencySlots.stream()
        .map(v -> new WavelengthDto(OpticalRouterPortAttributes.toString(v.getValue())))
        .toList();
    }
  }

}
