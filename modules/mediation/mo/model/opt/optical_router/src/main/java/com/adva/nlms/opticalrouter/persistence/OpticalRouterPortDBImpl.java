/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.opticalrouter.persistence;

import com.adva.nlms.mediation.config.EntityDBImpl;
import com.adva.nlms.opticalrouter.api.resources.AdminState;
import com.adva.nlms.opticalrouter.api.resources.OperationalState;
import jakarta.persistence.CollectionTable;
import jakarta.persistence.Column;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Entity
@Table(name = "cn_opt_router_port")
@NamedQueries(value = {
  @NamedQuery(
    name = OpticalRouterPortDBImpl.UPDATE_OPTICAL_ROUTER_PORTS_OPERATIONAL_STATE,
    query = "UPDATE OpticalRouterPortDBImpl orp " +
      "SET orp.unifiedOperationalState = :operationalState " +
      "WHERE orp.networkResourceLocator = :nrl"
  )
})
public class OpticalRouterPortDBImpl extends EntityDBImpl {
  public static final String UPDATE_OPTICAL_ROUTER_PORTS_OPERATIONAL_STATE = "UPDATE_OPTICAL_ROUTER_PORTS_OPERATIONAL_STATE";

  @Column(name = "nrl")
  private String networkResourceLocator;

  @Column(name = "layer_qualifiers")
  private List<String> layerQualifiers;

  @Enumerated(EnumType.STRING)
  @Column(name = "unified_admin_state")
  private AdminState unifiedAdminState;

  @Enumerated(EnumType.STRING)
  @Column(name = "unified_operational_state")
  private OperationalState unifiedOperationalState;

  // SetPoint in dBm
  @Column(name = "target_output_power", precision = 20, scale = 2)
  private BigDecimal targetOutputPower;

  // always a single FrequencySlot, with center frequency in THz and bandwidth in GHz
  @ElementCollection
  @CollectionTable(name = "cn_optrouterport_frequencyslot", joinColumns = @JoinColumn(name = "port_id"))
  private List<FrequencySlot> frequencySlots;

  @ElementCollection
  @CollectionTable(name = "cn_optrouterport_wavelength", joinColumns = @JoinColumn(name = "port_id"))
  private List<Wavelength> wavelengths;

  @Override
  public boolean equals(Object o) {
    if (!(o instanceof OpticalRouterPortDBImpl that)) return false;
    if (!super.equals(o)) return false;
    return Objects.equals(networkResourceLocator, that.networkResourceLocator) &&
      Objects.equals(layerQualifiers, that.layerQualifiers) &&
      unifiedAdminState == that.unifiedAdminState &&
      unifiedOperationalState == that.unifiedOperationalState &&
      Objects.equals(targetOutputPower, that.targetOutputPower) &&
      Objects.equals(frequencySlots, that.frequencySlots) &&
      Objects.equals(wavelengths, that.wavelengths);
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), networkResourceLocator, layerQualifiers, unifiedAdminState, unifiedOperationalState, targetOutputPower, frequencySlots, wavelengths);
  }

  public OpticalRouterPortDBImpl() {
  }

  public OpticalRouterPortDBImpl(int neId) {
    super(neId);
  }

  public List<Wavelength> getWavelengths() {
    return wavelengths;
  }

  public void setWavelengths(List<Wavelength> wavelengths) {
    this.wavelengths = wavelengths;
  }

  public BigDecimal getTargetOutputPower() {
    return targetOutputPower;
  }

  public void setTargetOutputPower(BigDecimal targetOutputPower) {
    this.targetOutputPower = targetOutputPower;
  }

  public AdminState getUnifiedAdminState() {
    return unifiedAdminState;
  }

  public void setUnifiedAdminState(AdminState unifiedAdminState) {
    this.unifiedAdminState = unifiedAdminState;
  }

  public OperationalState getUnifiedOperationalState() {
    return unifiedOperationalState;
  }

  public void setUnifiedOperationalState(OperationalState unifiedOperationalState) {
    this.unifiedOperationalState = unifiedOperationalState;
  }

  public List<FrequencySlot> getFrequencySlots() {
    return frequencySlots;
  }

  public void setFrequencySlots(List<FrequencySlot> frequencySlots) {
    this.frequencySlots = frequencySlots;
  }

  public String getNetworkResourceLocator() {
    return networkResourceLocator;
  }

  public void setNetworkResourceLocator(String networkResourceLocator) {
    this.networkResourceLocator = networkResourceLocator;
  }

  public List<String> getLayerQualifiers() {
    return layerQualifiers;
  }

  public void setLayerQualifiers(List<String> layerQualifiers) {
    this.layerQualifiers = layerQualifiers;
  }

  @Override
  public int[] getObjectIndices() {
    if (getEntityIndex() == null) {
      return new int[0];
    }
    return getEntityIndex().toIntArray();
  }
}
