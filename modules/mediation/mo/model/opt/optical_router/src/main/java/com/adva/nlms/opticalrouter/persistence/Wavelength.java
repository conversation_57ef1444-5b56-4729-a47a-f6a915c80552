/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.opticalrouter.persistence;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;

import java.math.BigDecimal;

@Embeddable
public class Wavelength {

  @Column(name = "wavelength", precision = 20, scale = 5)
  private BigDecimal value;

  public Wavelength() {
  }

  public Wavelength(BigDecimal value) {
    this.value = value;
  }

  BigDecimal getValue() {
    return value;
  }

  void setValue(BigDecimal value) {
    this.value = value;
  }
}
