/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.opticalrouter.persistence;

import com.adva.nlms.mediation.config.ModuleDBImpl;
import com.adva.nlms.opticalrouter.api.resources.AdminState;
import com.adva.nlms.opticalrouter.api.resources.OperationalState;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;

import java.util.Objects;

@Entity
@Table(name = "cn_opt_router_plug")
@NamedQueries(value = {
  @NamedQuery(
    name = OpticalRouterPlugDBImpl.UPDATE_OPTICAL_ROUTER_PLUGS_OPERATIONAL_STATE,
    query = "UPDATE OpticalRouterPlugDBImpl orp " +
      "SET orp.unifiedOperationalState = :operationalState " +
      "WHERE orp.networkResourceLocator = :nrl"
  )
})
public class OpticalRouterPlugDBImpl extends ModuleDBImpl {
  public static final String UPDATE_OPTICAL_ROUTER_PLUGS_OPERATIONAL_STATE = "UPDATE_OPTICAL_ROUTER_PLUGS_OPERATIONAL_STATE";

  @Enumerated(EnumType.STRING)
  @Column(name = "unified_admin_state")
  private AdminState unifiedAdminState;

  @Enumerated(EnumType.STRING)
  @Column(name = "unified_operational_state")
  private OperationalState unifiedOperationalState;

  @Column(name = "vendor_part_number")
  private String vendorPartNumber;

  @Column(name = "nrl")
  private String networkResourceLocator;

  public OpticalRouterPlugDBImpl() {
  }

  public OpticalRouterPlugDBImpl(int neId) {
    super(neId);
  }

  public AdminState getUnifiedAdminState() {
    return unifiedAdminState;
  }

  public void setUnifiedAdminState(AdminState unifiedAdminState) {
    this.unifiedAdminState = unifiedAdminState;
  }

  public OperationalState getUnifiedOperationalState() {
    return unifiedOperationalState;
  }

  public void setUnifiedOperationalState(OperationalState unifiedOperationalState) {
    this.unifiedOperationalState = unifiedOperationalState;
  }

  public String getNetworkResourceLocator() {
    return networkResourceLocator;
  }

  public void setNetworkResourceLocator(String networkResourceLocator) {
    this.networkResourceLocator = networkResourceLocator;
  }

  public String getVendorPartNumber() {
    return vendorPartNumber;
  }

  public void setVendorPartNumber(String vendorPartNumber) {
    this.vendorPartNumber = vendorPartNumber;
  }

  @Override
  public int[] getObjectIndices() {
    if (getEntityIndex() == null) {
      return new int[0];
    }
    return getEntityIndex().toIntArray();
  }

  @Override
  public boolean equals(Object o) {
    if (!(o instanceof OpticalRouterPlugDBImpl that)) return false;
    if (!super.equals(o)) return false;
    return unifiedAdminState == that.unifiedAdminState && unifiedOperationalState == that.unifiedOperationalState && Objects.equals(vendorPartNumber, that.vendorPartNumber) && Objects.equals(networkResourceLocator, that.networkResourceLocator);
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), unifiedAdminState, unifiedOperationalState, vendorPartNumber, networkResourceLocator);
  }
}
