/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.opticalrouter.persistence;

import com.adva.nlms.mediation.config.dao.GenericMODao;
import com.adva.nlms.opticalrouter.api.resources.OperationalState;

public interface OpticalRouterPortDao extends GenericMODao<OpticalRouterPortDBImpl> {

  void updateOpticalRouterPortOperationalStateByNrl(String nrl, OperationalState operationalState);
}
