/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.opticalrouter.persistence;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;

import java.math.BigDecimal;

@Embeddable
public class FrequencySlot {

  @Column(name = "center_frequency", precision = 20, scale = 6)
  private BigDecimal centerFrequency;
  @Column(name = "slot_width", precision = 20, scale = 2)
  private BigDecimal slotWidth;

  public FrequencySlot() {
  }

  public FrequencySlot(BigDecimal centerFrequency, BigDecimal slotWidth) {
    this.centerFrequency = centerFrequency;
    this.slotWidth = slotWidth;
  }

  public BigDecimal getCenterFrequency() {
    return centerFrequency;
  }

  void setCenterFrequency(BigDecimal centerFrequency) {
    this.centerFrequency = centerFrequency;
  }

  public BigDecimal getSlotWidth() {
    return slotWidth;
  }

  void setSlotWidth(BigDecimal slotWidth) {
    this.slotWidth = slotWidth;
  }
}
