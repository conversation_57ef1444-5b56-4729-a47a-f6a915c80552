/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: ricardos
 */

package com.adva.nlms.opticalrouter.persistence;

import com.adva.nlms.inf.api.notification.NotificationAttribute;
import com.adva.nlms.mediation.config.EquipmentAttributes;

public class OpticalRouterPlugAttributes extends EquipmentAttributes {

  public static final NotificationAttribute<String> VENDOR_PART_NUMBER =
    new NotificationAttribute<>("vendorPartNumber");

  public OpticalRouterPlugAttributes() {
    super(VENDOR_PART_NUMBER);
  }

}
