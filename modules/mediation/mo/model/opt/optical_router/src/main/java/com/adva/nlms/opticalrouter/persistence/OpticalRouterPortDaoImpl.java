/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.opticalrouter.persistence;

import com.adva.nlms.mediation.common.persistence.MDPersistenceManager;
import com.adva.nlms.mediation.config.dao.GenericMODaoImpl;
import com.adva.nlms.opticalrouter.api.resources.OperationalState;

public class OpticalRouterPortDaoImpl extends GenericMODaoImpl<OpticalRouterPortDBImpl> implements OpticalRouterPortDao {

  private static final String NRL = "nrl";
  private static final String OPERATIONAL_STATE = "operationalState";

  @Override
  public void updateOpticalRouterPortOperationalStateByNrl(String nrl, OperationalState operationalState) {
    MDPersistenceManager.current()
      .createNamedQuery(OpticalRouterPortDBImpl.UPDATE_OPTICAL_ROUTER_PORTS_OPERATIONAL_STATE, OpticalRouterPortDBImpl.class)
      .setParameter(NRL, nrl)
      .setParameter(OPERATIONAL_STATE, operationalState)
      .executeUpdate();
  }

}
