/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 */
package com.adva.nlms.common.rest;

import com.adva.common.model.DataTransferObject;
import com.adva.nlms.common.topology.TopologyDiscoveryResults;

@SuppressWarnings("squid:S1214") // legacy class requires significant refactoring
public interface MDRestPath {
  /**
   * The default server url to get home page of client update
   */
  String DEFAULT_HOME_PAGE = "/advabase/";

  String ALARMS_STR = "alarms";
  String EQUALIZE_STR = "equalize";

  interface TOPOLOGY {
    String PATH = "topology";
    String NODES_STRUCTURE = "nodesStructure";
    String SORTING_DATA = "sortingData";

    interface PLANNER_EXPORT {
      String ROOT = "root";
      String PLANNER_EXPORT = "plannerExport";
    }
  }

  public interface PD_TOPOLOGY {
    public final String PATH = "pdtopology";
    public final String NODES_STRUCTURE = "nodesStructure";
    public  final String SORTING_DATA = "sortingData";
  }

  public interface CommonParams {
    public static final String ID = "id";
  }

  public interface TRAP_FORWARDER {
    public static final String PATH = "trapforwarder";
    public static final String RECIPIENTS = "recipients";
    public static final String RESET_SNMP_V3_SESSION = "resetSnmpV3Session";
  }

  public interface REPORT {
    public static final String PATH = "reports";
    public static final String PATH_DEL = "reports/deletion";
    public static final String PATH_NE_LICENSES_REPORT = "reports/neLicenses";
    public static final String PATH_DEL_ALL = PATH_DEL+"/all";
    public static final String PATH_PERM = "reports/permission";
    public static final String WARNING_THRESHOLD_EXCEEDED = "warningThresholdExceeded";
    public static final String PATH_DEL_BY_TIME_AND_TYPE = "deletion/timeAndType";
    public static final String PATH_GET_DEL_BY_TIME_AND_TYPE = "get/deletion/timeAndType";
    public static final String VIEW_RESTRICTED = "report/view/restricted";
    public static final String REPORT_CUSTOMIZATION = "report/customization";
    public static final String REPORT_UNIQUE_NAME = "report/unique/name";
    public static final String REPORT_CUSTOMIZATION_UPDATE = "report/customization/update";
    public static final String REPORT_ADMIN_USER = "report/admin/user";
    public static final String REPORT_IDS = "reportids";
  }

  public interface CLIENT_UPDATE {
    public static final String PATH = "update";

    public static final String REPOSITORY = "repository";
    public static final String CLIENT_ATTRIBUTES = "clientAttributes";
    public static final String SERVER_THREAD_DUMP = "serverThreadDump";
    public static final String MEDIATION_LOG = "mediationLog";
    public static final String MEDIATION_ERROR_LOG = "mediationErrorLog";
    public static final String SM_LOG = "smLog";
    public static final String LOGFILE = "logfile";
    public static final String UNIT_FILE = "unitFile";
    public static final String SERVER_PROPERTY = "serverProperty";
    public static final String HA_CONFIGURATION = "haConfiguration";
  }

  public interface PAGING {
    public static final String PATH = "paging";

    public static final String DATA = "data";

    public static final String DATA_JSON = "data/json";
    public static final String DETAILS = "details";
    public static final String CONTEXT_ID = "contextid";
    public static final String COUNTER = "counter";
    public static final String NOTIFICATION_LISTENER = "notificationListener";
  }

  public interface TCA {
    public static final String PATH = "tca";

    public static final String UNMANAGED_PROBES = "unmanagedProbes";
    public static final String STANDARD_PROBES = "standaredProbes";
    public static final String STANDARD_ESA_PROBES = "standaredEsaProbes";
    public static final String MANAGED_STANDARD_ESA_PROBES = "managedStandardEsaProbes";
  }

  interface CP_POLICY {
    String PATH = "cppolicy";
    String RESTORATION = "restoration";
    String SRVCSETUP = "srvcsetup";
    String RESOURCE = "resource";
    String TE = "te";
    String LOWESTIP = "lowestip";
    String GETBULK = "getbulk";
  }

  interface FIBERMAP_DIAGNOSTICS {
    String PATH = "fibermap-diagnostics";
    String START = "start";
    String STATE = "state";
    String GET_RESULTS = "results";
    String GET_STATUS = "status";
  }

  interface EOD {
    String PATH = "eod";
    interface TOPOLOGY {
      String PATH = "v1/eod/topology";
      String GET_NODES = "nodes";
      String COMPACT_NODES = "compact-nodes";
      String NAME = "name";

      String GET_NODE_UUID_BY_NE_ID = "nodeUUIDByNeID";
      String GET_NODE_BY_NE_ID="nodeByNeID";
      String GET_CHILD_SUBNET_IDS = "child-subnet-uuids";
      String GET_SUBNETS = "subnets";
    }

    interface CONNECTIVITY_SERVICE {
      String PATH = "v1/eod/connectivity-services";
      String TEARDOWN = "teardown";
      String INSTALL = "install";
      String SET_WORKING_PATH = "switch-to-working";
      String SET_PROTECTING_PATH = "switch-to-protecting";
      String PATCH_UPDATE = "patch-update";
      String EQUALIZE_CS = "equalize-cs";
      String TAG = "tag";
      String UNTAG = "untag";
      String SUPPORTED_SERVICES = "supported-services";
      String RENAME = "rename";
      String MOVE = "move";
      String MODIFY_PROVISIONED = "modify/provisioned";
      String MODIFY_EXPLORED = "modify/explored";
      String EXPLORED = "explored";
      String PROVISIONED = "provisioned";
      String ENABLE_CP = "enable-cp";
      String DISABLE_CP = "disable-cp";
      String SUSPEND_CP = "suspend-cp";
      String RESUME_CP = "resume-cp";
      String ACCEPT_CURRENT_PATH = "accept-current-path";
      String REVERT_TO_NOMINAL = "revert-to-nominal";
      String BY_OMS = "by-oms";
      String REMOVE_RELATED_CS = "remove-related-cs";
      String HAS_SHADOW_SERVICE = "has-shadow-service";
      String SUSPEND_LINE_SERVICES = "suspend-line-services";
      String VALIDATE_LINE_SERVICES = "validate-line-services";
      String SYNCHRONIZE = "synchronize";
      String RESUME_LINE_SERVICES = "resume-line-services";
      String OVERVIEW = "mod/service";
    }

    interface CSM {
      String PATH = "enc/v1/eod/csm/connectivity-services";
      String TEARDOWN = "teardown";
      String EQUALIZE = EQUALIZE_STR;
      String SET_WORKING_PATH = "switch-to-working";
      String SET_PROTECTING_PATH = "switch-to-protecting";
      String ASYNC_OPERATIONS = "enc/v1/eod/csm/async-operations";
      String LINE_SERVICES_PATH = "enc/v1/eod/csm/line-services";
    }

    interface EOD_WEB {
      String PATH = "mod/connectivity-service";
      String EXPLORED = "explored";
      String PROVISIONED = "provisioned";
      String OVERVIEW_PATH = "mod/service";
    }

    interface SERVICE_TOPOLOGY {
      String PATH = "v1/eod/servicetopology";
      String SERVICES = "services";
      String GET_CONTAINERS = "containers";
      String CHILD_CONTAINERS = "children-ids";
      String IS_CONTAINER_VALID = "container/valid";
    }

    interface NRIM {
      String PATH = "v1/eod/nrim";

      // Endpoint API
      String SERVICE_INTERFACE_POINTS = "service-interface-points";
      String POTENTIAL_CONNECTION_ENDPOINTS = "potential-connection-endpoints";
      String CONNECTION_ENDPOINTS = "connection-endpoints";
      String CONNECTION_ENDPOINTS_CEP_NRLS = "connection-endpoints-cep-nrls";
      String NODE_EDGE_POINTS = "node-edge-points";
      // Connections API
      String CONNECTIONS = "connections";
      String POTENTIAL_CONNECTIONS = "potential-connections";
      String PROTECTION_GROUPS = "protection-groups";
      String TARGET_ADMIN_STATE = "target-admin-state";
      // Logical Link API
      String LOGICAL_LINKS = "/logical-links";
      // Endpoint Translation API
      String deviceEndpoints = "device-endpoints";
      interface CAPABILITIES{
        String PATH = NRIM.PATH + "/capabilities";
        String PROFILES = "profiles";
        String ATTRIBUTES = "attributes";
      }
    }

    interface MIGRATION {
      String PATH = "v1/eod/migration";
      String SERVICES = "services";
      String SERVICES_COUNT = "count";
      String RELATED_CONNECTIONS = "related-connections";
      String MIGRATE_CONNECTIONS = "migrate-connections";
      String MIGRATE_PHYSICAL_LINKS = "migrate-physical-links";
      String MIGRATE_CUSTOM_FIELDS = "migrate-custom-fields";
      String MIGRATE_PM_TEMPLATE = "migrate-pm-template";
    }

    interface SERVICE_INTENT {
      String PATH = "v1/eod/service-intent";
      String CUSTOM_FIELDS = "custom-fields";
      String CONNECTIVITY_SERVICE_ID = "connectivity-service-id";
    }
  }

  public interface FM {
    String PATH = "v1/cor/fm";

    String events = "events";
    String standingAlarms = "standingAlarms";
    String ALARM_COUNTERS = "alarmCounters";
    String ALARMS = "alarms";
  }

  public interface SM {
    public static final String PATH = "sm";

    public static final String CRYPTO = "crypto";
    public static final String SERVICES = "services";
    public static final String EQUALIZE = EQUALIZE_STR;
    public static final String OCS_CONF_PROTECTION = "ocsconfprotection";
    public static final String CUSTOMER = "customer";
    public static final String CUSTOMERS = "customers";
    public static final String ALL_CUSTOMERS = "customers/all";
    public static final String UNASSIGNED_CUSTOMER = "customers/unassigned";
    public static final String CUSTOMER_IN_CUSTOMER_GROUP = "customers/customergroup";
    public static final String SERVICE_VIEWS = "serviceviews";
    public static final String SERVICE_VIEWS_FOR_OCS = "serviceviews/ocs";
    public static final String SERVICE_VIEWS_FOR_ODS = "serviceviews/ods";
    public static final String CUSTOMER_SERVICE_GROUPS = "customerservicegroups";
    String CONTRACTS_MODIFY = "contracts/modify";
    String CONTRACTS_CREATE = "contracts/create";
    public static final String CONNECTIVITY_SERVICES = "connectivityservices";
    public static final String TRAIL_NAME = "trailname";
    public static final String NODE_PROPERTIES = "nodeproperties";
    public static final String NODE_ETH_MODULES = "nodeethmodulesproperties";
    public static final String NODE_ETH_LAGS = "nodeethlagsproperties";
    public static final String NODE_ETH_PORTS = "nodeethportsproperties";
    public static final String NODE_ETH_FPS = "nodeethfpssproperties";
    public static final String EXPLORABLE_NODE_PROPERTIES = "explore_nodeproperties";
    public static final String REGEN_PTP_PROPERTIES = "regenptps";
    public static final String ECH_PROPERTIES = "echproperties";
    public static final String TOP_LEVEL_CUSTOMER_GROUPS_ID = "customergroups/toplevelid";
    public static final String SERVICE_COUNT = "servicecount";
    public static final String TRAIL_COUNT = "trailcount";
    public static final String SERVICE_COUNT_BY_LINK = "servicecount/link";
    public static final String SERVICE_COUNT_BY_NODE = "servicecount/node";
    public static final String SERVICES_FOR_LINK_DELETION = "deletionservices/link";
    public static final String SERVICES_FOR_NODE_DELETION = "deletionservices/node";
    public static final String SERVICES_BY_LINK = "services/link";
    public static final String SERVICES_BY_NODE = "services/node";
    public static final String SERVICES_ANY_GHOST_BY_NODE = "services/ghost/any";
    public static final String TERMINATION_PTP = "terminationptp";
    public static final String CONNECTED_POINTS = "connectedpoints";
    public static final String USED_VC_BUNDLES = "usedVCbundles";
    public static final String NETWORK_PORT_PARAMETERS = "networkportparameters";
    public static final String SERVICE_VIEWS_IMPACTED_BY_EVENT = "serviceviews/event";
    public static final String IS_ADM = "check/adm";
    public static final String IS_DELETED = "check/delete";
    public static final String IS_MODIFIED = "check/modify";
    public static final String IS_LINE_AFFECTED_SERVICE = "check/line";
    public static final String CAN_REMOVE_LINK = "check/canRemoveLink";
    public static final String HAS_ODS_BANDWIDTH_ON_OCS = "check/hasodsbandwidth";
    public static final String SERVICE_SYNC = "servicesync";
    public static final String VERIFY_MODULE_SERVICES = "check/service/module";
    public static final String REVERT_PATH = "restoration/revertpath";
    public static final String ACCEPT_PATH = "restoration/acceptpath";
    public static final String IS_REVERSION_ALLOWED = "check/reversion";
    public static final String WHY_REVERSION_NOT_ALLOWED = "reversion/reason";
    public static final String IS_SERVICE_RESTORED = "check/restored";
    public static final String MULTI_LAYER_PROPERTIES = "multilayerproperties";
    public static final String ERP_SERVICE_REFERENCE = "erpservicereference";
    public static final String LINK_SERVICES_LOCKED_OR_UNLOCKED = "link/lock";
    public static final String IS_SERVICE_ID = "check/serviceid";
    public static final String ETHERNET_TRAIL_ID = "ethernettrail/id";
    public static final String ACKNOWLEDGE = "acknowledge";
    public static final String NTN_TYPE = "ntntype";
    public static final String ACK_STATE = "ackstate";
    public static final String DATE_BEFORE = "datebefore";
    public static final String IS_MANAGED = "managed";
    public static final String IS_SERVICE_STITCHED = "stitchedService";
    public static final String RE_EXPLORE_MLSERVICE = "reExploreMlService";
    public static final String JOIN_EXPLORE_MLSERVICE = "joinExploreMlService";
    public static final String SPLIT_MLSERVICE = "splitMlService";
    public static final String RESYNC_SUBCH_CONNECTION = "sync/fsp1500";
    public static final String CONNECTION_DIRECTION = "connectiondirection";
    public static final String SWITCH_CONNECTIONS_TO_DEFAULT = "connectiondirection/default/subnet";
    public static final String CONNECTION_TEST = "connectiontest";
    public static final String RESOURCES = "resources";
    public static final String RESOURCES_PATHS = "resources/paths";
    public static final String GRAPH_MODEL = "graphmodel";
    public static final String ROUTE_PREVIEW = "routePreview";
    public static final String EXPLORE_PATH = "explorepath";
    public static final String RE_EXPLORE_PATHS = "re_explorepaths";
    public static final String RE_ALL_EXPLORE_PATHS = "re_exploreallpaths";
    public static final String JOIN_EXPLORE_PATHS = "join_explorepaths";
    String COMM_CHANNEL_CHECK = "checkcommchannel";
    public static final String UPDATE_ESA = "esa/update";
    public static final String CLEANUP_UNMANAGED = "removeunmanaged";
    public static final String CAN_DELETE_SERVICE = "services/check/delete";
    public static final String SERVICE_CONFIGURATION = "serviceconfiguration";
    public static final String CHECK_PROTECTION = "checkprotection";
    public static final String CHECK_ML_SERVICE_LOOKUP = "checkmlservice";
    public static final String CHECK_SPECIAL_FIX_CONSISTENCY = "fixspecialconsistency";
    public static final String RESYNC_ASSOCIATION = "resyncassociation";
    public static final String REFRESH_OPER = "refreshoperationalstate";
    public static final String REFRESH_ML_OPER = "refreshmloperationalstate";
    public static final String CLEANUP_ML_SERVICE = "cleanUpMlService";
    public static final String SERVICE_CONSISTENCY_CHECK = "serviceConsistencyCheck";
    public static final String FIX_STITCH_OTU1 = "fixotu1stitch";
    public static final String All_NE_NODES = "allNENodes";
    String CHECK_SERVICE_NAME = "SM/checkServiceName";
    public static final String ABANDON = "abandon";
    public static final String ABANDON_LINK_TUNNELS = "link/abandon";
    public static final String ADOPT_LINK_TUNNELS = "link/adopt";
    public static final String OLD_NEW_PATH = "oldNewPath";
    public static final String ALL_SERVICES_NODE_IDS = "allServicesNodeIds";
    String OLD_NEW_ID = "oldNewId";
    public static final String CHECK_OCS_ASSOCIATION = "checkOcsAssociation";
    String POWER_LEVEL_UPDATE = "powerlevelupdate";
    String GET_DEFAULT_SERVICE_NAME = "getDefaultServiceName";
    String GET_SERVICE_NAME_UNIQUE_INDEX = "getServiceNameWithUniqueIndex";
    public static final String HAS_SPECIFIC_CARD_HIGHER_OR_EQUAL_SW_VERSION = "hasSpecificCardHigherOrEqualSwVersion";
    public static final String GET_PM_DATA = "pm/getData";
    public static final String GET_PM_COUNTER_SERIES = "pm/getCounterSeries";
    public static final String GET_CHANNELS = "getChannels";
    public static final String IS_NE_NI_CONTROLLED = "check/ne/ni";
    String ASSOCIATED_CONNECTIVITY_SERVICE = "associatedConnectivityService";
    public static final String GET_RELATED_ML_TRAILS_FOR_CS = "relatedMlTrails";
    public static final String ASSOCIATE_ML_TRAIL_WITH_CS = "associateMlTrailWithCs";
    public static final String DESTROY_CONNECTIVITY_SERVICE = "destroyCs";
    public static final String GET_CLASSIC_CONNECTION_FOR_CS = "getClassicConnectionForCs";
    public static final String IS_C2C = "check/c2c";
    public static final String HAS_TUNNEL_IN_SERVICE = "hasTunnelInService";
    public static final String GET_ALL_DIVERSITIES = "getAllDiversities";

    interface REPLACE {
      String IS_SAVED_CONFIG = "issavedconfig";
      String IS_ACTION_IN_PROGRESS = "isactioninprogress";
      String SAVE_ROADM_CONFIG = "saveroadmconfig";
      String DELETE_ROADM_CONFIG = "deleteroadmconfig";
      String REPLACE_ROADM = "replaceroadm";
      String EQUALIZE_ROADM = "equalizeroadm";
    }

    String ADOPT = "adopt";
    String ETH_SERVICE_PATH_COMPUTATION = "ethServicePathComputation";
    String ETH_SERVICE_PROVISION = "ethServiceProvision";
    String RECALCULATE_SEVERITY_COUNTERS = "recalculateSeverityCounters";
    String RECOMPUTE_ALL_ETH_OPERSTATES = "recalcAllEthOperStates";
    String GRAPH_NODE = "graphnode";
    String LAYERPROTOCOL_MAP = "layerProtocolMap";
    String ENABLE_CONTROL_PLANE = "enableControlPlane";
    String DISABLE_CONTROL_PLANE = "disableControlPlane";
    String SERVICE_UNDER_ENABLE_DISABLE_CP = "serviceUnderEnableDisableCP";

    interface CAPABILITY {
      String PATH = "sm/capability";
      String ATTRIBUTES = "attributes";
      String PROFILES = "profiles";
      String ENDPOINTS = "endpoints";
      String CARDS = "cards";
    }
  }

  public interface HEALTH_CENTER {
    public static final String PATH = "healthCenter";
    public static final String SERVER = "server";
    public static final String NETWORK = "network";
    public static final String SERVER_V2 = "v2/server";
    public static final String NETWORK_V2 = "v2/network";
    public static final String NETWORK_POWER_CONSUMPTION_ANALYSIS_HISTOGRAM = "network/powerConsumption/analysis/histogram";
  }

  public interface  REST_NBI{
    public static final String PATH = "restnbi";
    public static final String ALARM = "fm/alarm";
    public static final String EVENT = "fm/event";
    public static final String NETWORK_ELEMENT = "inventory/network-element";
    public static final String SERVICES = "sm/service";
  }

  interface SERVICE_ADMINSTATE {
    String PATH = "serviceadminstate";
    String REQUEST = "request";
    String ACKNOWLEDGE = "acknowledge";
    String REFUSE = "refuse";
    String TEST = "test";
    enum AdminState {INVALID, DOWN, MAINTENANCE, AINS, UP}
  }

  interface PD_SERVICE_ADMINSTATE {
    String PATH = "pdserviceadminstate";
    String REQUEST = "request";
    String ACKNOWLEDGE = "acknowledge";
    String REFUSE = "refuse";
    String TEST = "test";
    enum PDAdminState {INVALID, DOWN, MAINTENANCE, AINS, UP}
  }

  interface BANDWIDTH_RESTRICT {
    String PATH = "bandwidthrestriction";
    String CREATE = "create";
    String DELETE = "delete";
    String GET = "get";
    String GET_ALL = "getAll";
    String UPDATE = "update";
    String TEST = "test";
  }

  public interface SERVER {
    public static final String PATH = "server";
    public static final String CLIENT_VALID = "clientValid";
    public static final String VERSION = "version";
    public static final String STATUS = "status";
    public static final String PROPERTY = "property";
    public static final String PING = "ping";
    public static final String SHUTDOWN = "shutdown";
    //shutdown triggered by regular action during ha sync
    public static final String SHUTDOWN_SECPORT = "shutdownsecport";
    public static final String SDEVENT = "sdevent";
    public static final String TEST_EMAIL = "testemail";
    public static final String MAILSERVER = "mailserver";
    public static final String IS_MAIL_SERVER_CONFIGURED = "ismailserver";
    public static final String DB_BACKUP = "dbbackup";
    public static final String MAIL = "mail";
    public static final String LICENSE_SERVER = "licenseserver";
    String FNM_APPLICATION_PATH = "fnmapplicationpath";
    String FNM_APPLICATION_PATH_SECPORT = "fnmapplicationpathsecport";
    String OS_NAME = "osname";
    String DBMS_VERSION = "dbmsversion";
    String DB_CREDENTIALS = "dbcredentials";
    String IP_ADDRESS = "ipaddress";
//    String IP_ADDRESSES = "ipaddresses";
    String TRAPSINK_IP_ADDRESS = "trapsinkipaddress";
    String TRAPSINK_IPV6_ADDRESS = "trapsinkipv6address";
    String CONNECT_WITH_DATABASE = "connectwithdatabase";

    // TODO move to CLIENT_UPDATE?
    public static final String COMPATIBLE_CLIENT_VERSIONS = "clientversion";
    // TODO move to SWUpgrade?
    public static final String CHECK_UPGRADES = "check/upgrades";

    public static final String FORM = "/form";
    String FORM_MODEL = "form-models";
    String FORM_MODEL_JSON = "form-models/json";
    String FORM_MODEL_AVAILABLE = "form-models-available";

    String DB_RUNNING_STATE = "dbrunningstate";
    String IS_ALIVE = "isalive";
  }

  interface HIGH_AVAILABILITY {
    String PATH = "ha";
    String CURRENT = "current";
    String WORKMODE = "workmode";
    String REST_LOOPBACK = "restloopback";
    String SSH_LOOPBACK = "sshloopback";
    String SFTP_LOOPBACK = "sftploopback";
    String HA_STRUCTURE = "hastructure";
    String HA_CONFIGURATION = "haconfiguration";
    String HA_DEFAULT_CONFIGURATION = "hadefaultconfiguration";
    String SERVER_MODE = "servermode";
    String ABORT = "abort";
    String PAUSE = "pause";
    String REDUNDANT_STATE = "redundantstate";
    String DB_SYNC = "dbsync";
    String IS_MUTUAL_PORT_OPEN = "ismutualportopen";
  }

  interface MULTI_SERVER_CONNECTOR {
    String PATH = "msc";
    String VERSION = "version";
    String SERVER = "server";
  }

  interface SEARCH {
    String PATH = "search";
    String BY_TYPE = "bytype";
    String NTN_TYPE = "ntntype";
    String ADVANCED_SEARCH = "advsrch";
  }

  interface INDEXSEARCH {
    String PATH = "indexsearch";
    String META_INFORMATION = "metainformation";
    String INDEX_TYPES = "indextypes";
    String INDEX_FIELDS = "indexfields";
    String SIMPLE = "simple";
    String ADVANCED = "advanced";
    String TEST_UI_SIMPLESEARCH = "test_ui_simplesearch";
    String TEST_UI_FORM = "test_ui_form";
    String TEST_UI_ADVANCEDSEARCH = "test_ui_advancedsearch";
    String INITIALIZE = "initialize";
    String CONTENT = "content";
  }

  interface USERDATA {
    String PATH = "userdata";
    String UITEMPLATES = "uitemplates";
    String UI_PROFILES = "uiprofiles";
    String USER_DATA = "userdata";
    String USER_DATA_MAP = "userdatamap";
    String CREATE_GNSS_UITEMPLATES = "create_gnss_uitemplates";
  }

  interface USERSESSIONDATA {
    String PATH = "usersessiondata";
    String DTO_STORAGE = "dto_storage";
  }

  interface SECURITY {
    String PATH = "security";
    String SESSION = "session";
    String SESSION_IS_ACTIVE = "isSessionActive";
    String SESSION_NO_MAINTANANCE = "session/noMaintanance";
    String LOGIN = "login";
    String ENC_LOGIN = "loginenc";
    String LOGIN_UNSUCCESSFUL = "login/unsuccessful";
    String CLIENT_AUTHENTICATED = "clauth";
    String APPROVAL = "approval";
    String APPROVAL_UUID = "approval_uuid";
    String APPROVAL_TIMEOUT = "approval/timeout";
    String APPROVAL_PERIOD = "approval/period";
    String ACTION = "action";
    String GROUP = "group";
    String ROLE = "role";
    String ROLE_STREAM = "role/stream";
    String SHARED_SECRET = "sharedSecret";
    String FM_KEYSTORE_PASSWORD = "fmKeystorePassword";
    String RESET = "reset";
    String CLI = "cli";
    String SSH_CLI = "sshCli";
    String PROPERTIES = "properties";
    String NETWORK_VIEW = "networkView";
    String CUSTOMER_VIEW = "customerView";
    String CHECK_LICENSE = "checkLicense";
    String BASIC_LICENSE_ACQUIRED = "basicLicenseAcquired";
    String FEATURE_LICENSE_ACQUIRED = "featureLicenseAcquired";
    String LICENSE = "license";
    String LICENSE_INFO = "licenseInfo";
    String LICENSE_INFO_ALL = "licenseInfoAll";
    String LICENSE_REFRESH_STATUS = "licenseRefreshStatus";
    String LICENSE_EXPIRING_COUNT = "licenseExpiringCount";
    String LICENSE_NAME = "licenseName";
    String LICENSE_RELOAD = "licenseReload";
    String NEW_VIEW = "newView";
    String IS_FEATURE_GRANTED_BY_NAME = "isFeatureGrantedByName";
    String IS_TAPI_GRANTED = "isTapiGranted";
    String IS_PRESTO_GRANTED = "isPrestoGranted";
    String VERIFY_LICENSES = "verifyLicenses";
    String GET_JMS_TOKEN = "jmsToken";
    String ELS_GUI_URL = "elsGuiUrls";
    String SESSION_DETAILS = "sessiondetails";
    String ALL_PERMISSIONS = "allpermissions";
    String ACCESSIBLE_CONTAINERS = "accessiblecontainers";
    String GET_OTK = "otk";
    String VALIDATE_OTK = "validateOtk";
    String SESSION_FILTER = "sessionFilter";
    String SESSION_ACTIVE_IDS = "activeSessions";
    interface FLEXERA {
        String TEST = "flexeraTest";
        String HOST_ID = "hostid";
    }

    interface ELS {
      String ELS_SSO_CONFIG = "elsSsoConfig";
      String TEST_ELS_SSO_CONNECTION = "testElsSSOConnection";
      String SSO_PRELOGIN = "prelogin";
    }

    interface USER {
      String PATH = "security/user";
      String GROUPS = "groups";
      String SESSIONS = "sessions";
      String PASSWORD = "password";
      String PASSWD = "passwd";
      String CURRENT_USER_PASSWORD = "currentUserPassword";
      String FORCE_CURRENT_USER_PASSWORD = "forceCurrentUserPassword";
    }

    interface DEVICE {
      interface USER {
        String PATH = "security/device/users";
      }
    }

  }

  public interface NBI_V1 {
    public static final String PATH = "v1";

    public static final String DISCOVERY = "discovery";
    public static final String INVENTORY = "inventory";
    public static final String FIBERMAP = "fibermap";
    public static final String REG_MANAGEMENT = "regmanagement";
    public static final String STATUS = "status";
    public static final String SM_TOPOLOGY = "sm/topology";
    public static final String SERVER_WDM = "serverWDM";
    public static final String DEVICE_SNMP = "device/snmp";
    public static final String SNMP_GET = "oper/snmpget";
    public static final String SNMP_GET_NEXT = "oper/snmpgetnext";
    public static final String SNMP_SET = "oper/snmpset";
    public static final String PREFERENCES = "preferences";
  }

  interface PM {
    String PATH = "pm";
    String DATA_SERIES = "series";
    String PERFORMANCE_STRUCTURE = "struct";
    String PERFORMANCE_STRUCTURE2 = "struct2";
    String PERFORMANCE_STRUCTURE_JSON = "struct_json";
    String STATS = "stats";
    String PERFORMANCES = "performances";
    interface TEMPLATE {
      String PATH = "template/";
      String ID = "/id";//todo replace with USED_ID ???
      String NAME = "/name";
      String COLLECTION_FILTER = "/cfilter";
      String DEFAULT = "/default";
      String ASSIGN = "template/assign";
      String ASSIGNQ = "template/assignq";
      String UNASSIGN = "template/unassign";
      String ASSIGN_IN_PROGRES = "template/assignInProgress";
      String IS_ANY_ASSIGNED = "isAnyAssigned";
      String IS_ASSIGNED = "isAssigned";
      String DELETE = "template/delete"; //replace with @DELETE
      String IDS = "template/identifier";
      String USED_ID = "template/usedid";
      String CLEANUP_PERF_DATA = "template/cleanupPerfData";
    }
    interface SPANLOSS {
      String PATH = "pm/spanloss";
      String CURRENT = "current/";
      String REFERENCE = "reference/";
      String LAST = "last/";
      String NEW_REFERENCE = "newReference/";
      String MANUAL = "manual/";
      String ENABLED = "enabled/";
      String THRESHOLD = "thresholds/";
      String THRESHOLD_ENABLED = "thresholds/enabled/";
    }
    interface DATA{
      public static final String STRUCTURE = "data/structure";
    }
    interface TFW {
      String TEST_MAPPINGS = "tfw/testMappings";
      String TEST_RECORDS = "tfw/testRecords";
      String TEST_DATA_TYPES = "tfw/testDataTypes";
      String GET_ENTITY_TYPE = "tfw/getEntityType";
      String TEST_OIDS = "tfw/testOIDs";
      String GET_PERF_DB_OBJ = "tfw/getPerfDBObj";
      String GET_SCALLING = "tfw/getScalling";
      String GET_DIFFERENCES = "tfw/getDifferences";
      String GET_DETAILS = "tfw/getDetails";
      String GET_SNMP_FOR_MAPPINGS = "tfw/getSNMPForMappings";
      String GET_SINGLE_ENTITY_LIST = "tfw/getSingleEntityList";
    }

    interface EOD{
      String TEMPLATE = "v1/eod/pm/template/";
      String IDENTIFIER = "identifier";
      String ASSIGN = "assign";
      String UNASSIGN = "unassign";
    }

  }

interface HOUSEKEEPING {
  String PATH = "housekeeping";
  String TEST_CONNECTION = "testconnection";

  interface NE_BACKUP {
    String PATH = "nebackup";
    String DO_SUPPORT_DATA = "dosupportdata";
    String DO_BACKUP = "dobackup";
    String CANCEL_BACKUP = "cancel";
    String USE_GLOBAL = "useglobal";
    String ENCRYPTION = "encryption";
    String DO_RESTORE = "dorestore";
    String SUPPORTED_FOR = "supportedfor";
    String CHANGE_POLLING_STATUS = "changepollingstatus";
    String TEST_CONNECTION = "testconnection";
    String SETUP_POSITION = "setupposition";
    String BACKUP_FILES = "backupfiles";
    String BACKUP_CONFIG = "backupconfig";
    String NE_BACKUP_CONFIG = "nebackupconfig";
  }
}

  interface MASTER_PROFILE {
    String PATH = "master-profiles";
    String SYNCHRONIZE = "/synchronize";
    String AVAILABLE_NES = "/available-nes";
    String GET_ALL_MASTER_PROFILE_FILES = "/getAllNeProfiles";
    String FTP_CONFIGURATION_STATUS = "/ftp-configuration-status";
    String TRANSFER_TO_FTP = "/transfer-to-ftp";
    String TRANSFER_TO_NE = "/transfer-to-ne";
    String DELETE = "/delete";
    String DEACTIVATE = "/deactivate";
    String REMOVE = "/remove";
    String DEFAULT_PROFILE_NE_ID = "/default-ne";
    String CANCEL = "/cancel";
  }

  interface SUBNET {
    String PATH = "subnet";
    String TOP_LEVEL_SUBNET_ID = "toplevelid";
    String SUBNETS = "subnets";
    String CONTAINED_SUBNETS = "containdesubnets";
    String TREE_NODES = "treenodes";
    String NAME = "name/";
    String CHILD_SUBNET = "childsubnet";
    String ANY_INVENTORY = "anyinventory";

    interface ARC {
      String CSV = "arc/csv";
      String ALARM_INHIBITED_ENTITIES = "arc/alarminhibitedentities";
    }
  }

  interface VIRTUAL_OTN {
    String PATH = "virtualotn";
    String CHILDREN = "children";
    String VIRTUAL_PORT = "virtualport";
  }

  interface PCA {
    final String PATH = "pca";
    final String PASSWORD = "/password";
    final String SUBNET = "/subnetid";
    final String NE = "/neid";
    final String RESET = "/reset";
    final String NE_STATUS = "/nestatus";
    final String NE_STATUS_IDS = "ids";
    final String PCA_STATUS = "/pcacstatus";
    final String PCA_CLEAR_STATUS = "/pcastatus/clear";

  }

  interface CUA {
    String PATH = "cua";
    String CREATE = "/createuser";
    String NE_STATUS = "/nestatus";
    String CUA_STATUS = "/status";
    String RESET = "/reset";
    String CLEAR_STATUS = "/clear";
    String SUBNET = "/subnetids";
    String NES = "/affectednes";
  }

  interface MO {
    interface DATA_EXPORT {
      String PATH = "mo/export";
      String COMPACT_FLASH = "/compactflash";

    }

    interface Topology {
      String CUSTOM_PARAMS = "custom-params";
      String DB_CONSISTENCY = "db-consistency";
      String DB_CONSISTENCY_PRE_CHECK = "db-consistency-pre-check";
      String DISPLAY_CONTEXT = "display-context";
      String EXT_PROPERTIES = "properties/ext";
      String FULLPATH = "path";
      String GRAPH_LAYOUT = "graph-layout";
      String GRAPH_LAYOUT_LEGACY = "graph-layout-legacy";
      String GRAPH_MODEL = "graph-model";
      String DIVERSITY_GRAPH_MODEL = "diversity-graph-model";
      String NODES = "nodes";
      String NODES_NAME = "nodes/name";
      String PARENT = "parent";
      String PATH = "mo/topology";
      String PROPERTIES = "properties";
      String SUBTREE = "subtree";
      String SUBTREE_JSON = "subtree/json";
      String TOPOLOGY_DTO = "topology-DTO";
      String TOPOLOGY_MODELS = "topology-models";
      String TREE = "tree";
      String TREE_ROOT = "tree-root";
      String HAS_NE_CHILDREN = "has-ne-children";
    }

    interface Topologies {
      String PATH = "mo/topologies";
      String RING = "ring";
      String ADOPT = "adopt";
      String ERP_GROUP = "erp-group";
      String DEFAULT_RING_BANDWIDTH = "default-ring-bandwidth";
    }

    interface NE {

      interface NEDiscovery {
        String PATH = "mo/ne/discovery";

        String NODE = "node";
        String NODES = "nodes";
      }

      interface Fallback {
        String PATH = "mo/ne/fallback";

        String CREATE = "passwords";
        String GET = "passwords/get";
        String LIST_ALL = "passwords/all";
        String GENERATE_NEW = "passwords/generate";
        String SHOW_PASSWORD = "passwords/show";
        String IS_ENABLED = "enabled";
        String IS_SUPPORTED_ON_NE = "supported";
        String FALLBACK_USERNAME = "username";
      }


      interface ShelfLocation {
        String PATH = "mo/ne/shelflocation";

        String IS_ENABLED = "isenabled";
        String SET_SHELF_LOCATION = "setShelfLocation";
        String SET_SHELF_LOCATION_EX = "setShelfLocationEx";
      }

      interface Custom {
        String PATH = "mo/ne/customs";
      }

      interface NeCapability {
        public static final String PATH = "mo/ne/capability";
        public static final String IS_AVAILABLE = "isavailable";
        public static final String GET_NETYPES_BY_CAPABILITY_BOOLEAN_VALUE = "getNeTypesByCapabilityBooleanValue";
      }

      interface Operation {
        public static final String PATH = "mo/ne";

        public static final String SHELVES_FOR_NE = "shelves";

        public static final String FIND_BY_IP = "findByIp";
        public static final String FIND_BY_NAME = "findByName";
        public static final String FIND_BY_NE_IDENTIFIER = "findByNeIdentifier";
        public static final String FIND_BY_PATTERN = "findByPattern";
        public static final String FIND_BY_SUBNET = "findBySubnet";
        public static final String FIND_BY_MAC_IDENTIFIER = "findByMacIdentifier";
        public static final String FIND_BY_SERIAL_IDENTIFIER = "findBySerialIdentifier";

        public static final String PEER = "/peer";
        public static final String SUPPORTED_IDENTITY = "/supportedidentity";
        public static final String VERSION = "/version";
        public static final String TYPE = "/type";
        public static final String HOST_NAME = "/hostname";
        public static final String NE_ID = "/id";
        public static final String DNSNAME = HOST_NAME + "/dnsname";
        public static final String IP = "/ip";
        public static final String ALLNEs = "allnes";
        public static final String KAP = "kap";
        public static final String SUBNET_NCD_ALIGN = "ncd_sync";
        String RESPONSE_STATUS = "/responseStatus";
        String EVENT_PROC_SUPPRESS = "suppress";
        String EVENT_PROC_SUPPRESS_IP = "ipAddress";
        String EVENT_PROC_SUPPRESS_STATE = "state";
        String IS_DEPRECATED_CPD= "isDeprecatedCPd";
      }



      interface Inventory {
        public static final String PATH = "/inventory";
        public static final String PATH_TEMP = "mo/inventory";
        public static final String INTRA_NE_CONNECTIONS = "/intra-ne-connections";
        public static final String TRAFFIC_ENGINEERING_LINKS = "telinks";
        public static final String SET_ARC_STATE = "/arc";

        public static final String UNMANAGED_PORTS = "/unmanaged-ports";
        public static final String UNMANAGED_CROSS_CONNECTIONS = "/unmanaged-crs";
        public static final String HANDOVER_PORTS = "/handover-ports";

        interface Shelves {
          public static final String ALL_ENTITIES_INDEXES_UNDER_SHELF = "/allEntityIndexesUnderShelves";
        }

        interface Modules {
          public static final String PATH = "modules";
          public static final String PATH_RSM = "modules/rsm";

          public static final String BY_MODULE_INDEX = "module-index";
          public static final String BY_SHELF_INDEX = "shelf-index";
        }

        interface ModulePorts {
          public static final String PATH = "moduleports";
        }

        interface Ports {
          public static final String PATH = "ports";
          public static final String PORT_RAWDATA = "ports/rawdata";
        }

        public interface UnmanagedCrossConnections {
          public static final String PATH = "unmanagedCrs";
          public static final String GET_ALL_UNMANAGEDCRS = "get";
          public static final String CREATE_UNMANAGEDCRS = "create";
          public static final String DELETE_UNMANAGEDCRS = "delete";
        }

        public interface UnmanagedTerminationPoints {
          public static final String PATH = "unmanagedTps";
          public static final String ODS_NUMBER_OF_SERVICES = "getNumberOfServices";
          public static final String ODS_CREATE = "createOdsTerminationPoint";
          public static final String ODS_REMOVE = "removeOdsTerminationPoint";
          public static final String ODS_MODIFY = "modifyOdsTerminationPoint";
          public static final String ODU_CREATE = "createOduTerminationPoint";
          public static final String ODU_REMOVE = "removeOduTerminationPoint";
        }

        interface Entities {
          public static final String PATH = "entities";
          public static final String BY_TYPE = "entities/bytype";
          public static final String BY_INDEX = "entities/byindex";
        }

        public static final String UNMANAGEDPORT_GET_NEXT_AVAILABLE_UNMANAGEDPORT_NUMBER = "/getNextAvailableUnmanagedPortNumber";
        public static final String UNMANAGEDPORT_UPDATE_UNMANAGEDPORT = "/updateUnmanagedPort";
        public static final String UNMANAGEDPORT_GET_AIDS_ALREADY_USED = "/getAidsAlreadyUsed";
        public static final String UNMANAGEDPORT_GET_DETAILS = "/getHandoverPortDetails";  //TODO replaced by DataAccess.DETAILS
      }

    }

    interface DataAccess {
      public static final String GET = "/get";
      public static final String LIST = "/list";
      public static final String ADD = "/add";
      public static final String UPDATE = "/update";
      public static final String DELETE = "/delete";
      public static final String BATCH = "/batch";
      public static final String DETAILS = "/details";
      public static final String UNMANAGED_PORTS = "mo/da/unmanagedport";
      public static final String UNMANAGED_CROSS_CONNECTS = "mo/da/unmanaged-crs";
      public static final String HANDOVER_PORTS = "mo/da/handoverport";
      public static final String KEY_EXCHANGE_PROFILE = "mo/da/keyexchangeprofile";
    }
  }

  interface CONFIGCTRL {
    String PATH = "/configctrl";

    interface CUSTOMFIELDS {
      String PATH = "/customfields";

    }

    interface MAPVIEW {
      String PATH = "/mapview";

      String IMAGES= "/images";
    }

    interface TOPOLOGY {
      String PATH = "/topology";
      String DISCOVER_TOPOLOGY_ENABLED = "/discoverTopologyEnabled";
      String LLDP_DISCOVER_TOPOLOGY_ENABLED = "/lldpDiscoverTopologyEnabled";
    }
  }

  interface NECOMM {
    interface SNMP {
      String PATH = "necomm/snmp";
      String RESET_SESSION = "resetsession";
      String PROPERTIES = "properties";
    }

    interface CLI {
      String PATH = "necomm/cli";
      String PROPERTIES = "properties";
    }

    interface HTTP {
      String PATH = "necomm/http";
      String WEBSOCKET_CONNECTED = "ws/connection";
      String CERT_HANDLING_METHOD = "/cert-handling-method";
    }

    interface DcnSession {
        String PATH = "necomm/dcnsession";
        String RESET_SESSION = "resetsession";
        String IS_REST = "isrest";
    }

    String PATH = "/necomm";
    String FORWARD_PORT = "forwardport";
    String GET_PAF_CONFIG = "getpafconfig";
    String SET_PAF_CONFIG = "setpafconfig";
    String SET_NI_PAF_CONFIG = "setnipafconfig";
    String PROXY_HOST = "proxyhost";
    String PROXY_PORT = "proxyport";
    String HTTP_PROPERTIES = "http-properties";
  }


  public interface SYNC {
    public static final String PATH = "rest/SyncMngrLwp";

    public static final String PING = "Ping";
    public static final String CONFIG = "Config";
  }

  interface MONITOR {
    String PATH = "monitor";
    String MBEANS = "mbeans";
  }

  interface WEB_MANAGER {

    String PATH = "webmanager";
    String URL = "url";
    String SSOTOKEN = "ssotoken";
    String CLOSESTCONTEXT = "closestcontext";
    String CLOSESTSERVICECONTEXT = "closestservicecontext";
    String ALARM_URI = "alarmuri";
    int SSOTOKEN_TIMEOUT = 300; //seconds
    String CERTIFICATE = "certificate";
    String SSO_PREPARATION = "ssopreparation";
  }

  public interface TFW {
    public static final String PATH = "tfw";

    public static final String NETWORK_ELEMENT = "networkelement";
    public static final String INF = "inf";
    public static final String FIBER_MAP = "fibermap";
    public static final String NETWORK = "network";
    public static final String ENTITY = "entity";
    public static final String FACILITIES = "facilities";
    public static final String ADMINSTATE = "adminstate";
    public static final String PROVISION = "provision";
    public static final String DEPROVISION_LINE = "deprovisionline";
    public static final String GET_ALL_PROTECTION_PAIRS = FIBER_MAP + "/getAllProtectionPairs";
    public static final String GET_ALL_PROTECTION_PAIRS_FOR_CM_CLIENT = FIBER_MAP + "/getProtectionPairsForCMClient";
    public static final String GET_FIBER_MAP_VIEW = FIBER_MAP + "/getFiberMapView";
    public static final String GET_ENTITIES_VIEW = FIBER_MAP + "/getEntitiesView";
    public static final String GET_NETWORK_MAP_VIEW = NETWORK + "/getNetworkMapView";
    public static final String ADJUST_IP_ADDRESS_FOR_SIM_NES = NETWORK + "/adjustIPAddressForSimulatedNEs";
    public static final String MOVE_NE_TO_SUBNET = NETWORK + "/moveNEToSubnet";
    public static final String GET_ALL_CASCADED_PAIRS = FIBER_MAP + "/getAllCascadedPairs";
    public static final String GET_ALL_CASCADED_PAIRS_FOR_PTP = FIBER_MAP + "/getAllCascadedPairsForPtp";
    public static final String GET_ALL_FILTER_CASCADED_PAIRS = FIBER_MAP + "/getAllFilterCascadedPairs";
    public static final String GET_REGEN_SET = FIBER_MAP + "/getRegenSet";
    String OPERSTATE="operstate";
    String NI_CONTROLLER = "/ni-controller";
    String NI_CONTROLLER_IP = "/ip";
    String NI_HEALTH_CHECK = "/health-check";
    String NI_NE_LIST = "/node-list";
    String NI_NODES = "/nodes";
    String NI_TOPOLOGY = "/topology";

    String PLANNER_EXPORT_ROOT = "tfw/plannerExportRoot";

    String PLANNER_EXPORT_SUBNET = "tfw/plannerExportSubnet";

    public interface Trapsink {
      public static final String PATH = "trapsink";
      public static final String AUTO_AGING = "autoaging";
      public static final String MANAGEMENT_STATUS = "managementstatus";
    }

    interface TEST_API {
      String PATH = "test-api";

      interface NE_PROFILE {
        String PATH = "test-api/neprofile/f8";
        String DOWNLOAD_F8 = "/downloadNeProfile";
        String SCAN_NE_PROFILE_F8 = "/ne/{ne-id}";
        String UPLOAD_F8 ="/uploadF8MasterProfile";
        String REMOVE_F8 ="/removeF8MasterProfile/{ne-id}";
        String DEACTIVATE_F8 ="/deactivateF8MasterProfile/{ne-id}";
      }

      interface NBI {
        String PATH = "test-api/nbi";

        String TOPOLOGY_SUBNET = "topology/subnets";
        String TOPOLOGY_NE = "topology/nes";
        String TOPOLOGY_LINK = "topology/links";
        String TOPOLOGY_SERVICES = "topology/services";
        String CONSISTENCY_CHECK = "consistencycheck";
        String ADMINSTATE_DEPENDENCIES = "adminstatedependencies";
        String SDNFLEXCHANNELMAPPINGTEST = "sdnflexchannelmappingtest";
        String CRM_DATA = "crm/model";
        String CRM_NODE_CRC_MAPPER = "crm/nodecrcmapper";
        String CRM_WSS = "crm/wss";
      }

      interface SBI {
        String PATH = "test-api/sbi";

        String OPERSTATE = "operstate";
        String ALARM = "alarm";
        String EVENT = "event";
        String CONN_EVENT = "conn-event";
        String TRAP = "trap";
        String CUSTOM_TRAP = "customtrap";
        String CUSTOM_AOS_NOTIF = "customaosnotif";
        String LAST_LOG_INDEX_FOR_CUSTOM_TRAP = "lastlogindexforcustomtrap";
      }

      interface NRIM {
        String PATH = "test-api/nrim";
        String LOGICAL_LINK = "logical-link";
        String TE_ATTRIBUTES = LOGICAL_LINK + "/te-attributes";


      }
      interface APP {
        String PATH = "test-api/app";
        String PINGSTRING = "pingstring";
      }

      interface NeSim {
        String PATH = "test-api/nesim";

        String NET = "subnet";
        String NES = "nes";
        String ENTITIES = "entities";
        String PARAMS = "params";
      }

      interface SYS_LOGGER {
        String PATH = "test-api/syslogger";

        String MON_POINTS = "mon-points";
        String ACTIVATE = "activate";
        String GRAFANA = "grafana";
        String TOGGLE_COLLECTION = "/collection/areas";
      }

      interface CTP_PROV {
        String PATH = "test-api/ctpprov";

        String AVAILABLE_ENT = "/available-ent/ne/{ne-id}/ctp/{ctp-uri}";
      }

      interface SERVICE_MANAGER {
        String FACADE_F8 = "test-facade/sm/f8";
        interface CTP {
          String PROVISION = "/ctp";
          String PROVISION_EXTENDED = "/ctp-ext";
          String DEPROVISION = "/deprovision-ctp";
          String MODIFY = "/modify-ctp";
          String IS_PRESENT = "/is-present";
          String CTP_EC_FOR_ENTITY_INDEX = "/ne/{ne-id}/ctp/{ctp-entity-index}";
        }

        interface FIBER_CONNECTION {
          String PATH = "fiber-connections";
          String NE_FIBER_CONNECTIONS = "/ne/{ne-id}/fiber-connections";
          String PROVISION_FIBER = "/ne/{ne-id}/{from_ptp_aid}/{to_ptp_aid}/{type}";
          String DELETE_FIBER = "/ne/{ne-id}/{from_ptp_aid}/{to_ptp_aid}";
        }

        interface PTP {
          String PATH = "/ne/{ne-id}/ptp/{aid}";
          String ATTR = "/attr/{attr-name}";
          String MODIFY ="/modify-ptp";
          String CREATE = "/ne/{ne-id}/ptp/{uri}/{type}/{id}";
        }

        interface SNC {
          String PROVISION_SNC = "/snc";
          String DELETE_SNC = "/ne/{ne-id}/snc/{snc-uri}";
          String MODIFY_SNC = "/modify-snc";
        }

        interface CROMA{
          String NE_DEGREES = "/ne/{ne-id}/degrees";
          String NE_ORGS = "/ne/{ne-id}/orgs";
          String NE_SERVICE_ENDPOINTS = "/ne/{ne-id}/svceps";
          String NE_DEGREE = "/ne/{ne-id}/degree/{degree-num}";
          String NE_ORG = "/ne/{ne-id}/org/{org-num}";
          String NE_PORT_ENDPOINT = "/ne/{ne-id}/port/{port-uri}";
          String NE_DEGREE_FOR_CARD = "/ne/{ne-id}/mit/me/1/eqh/{shelf}/eqh/{slot}/eq/card/ptp/{port}/degree";
          String NE_ORG_FOR_CARD = "/ne/{ne-id}/mit/me/1/eqh/{shelf}/eqh/{slot}/eq/card/ptp/{port}/org";
          String NE_ORG_FOR_CARD_WITH_PORT_GROUP = "/ne/{ne-id}/mit/me/1/eqh/{shelf}/eqh/{slot}/eq/card/ptp/{port-group}/ptp/{port}/org";
          String NE_SLCS = "/ne/{ne-id}/slc/all";
          String NE_SLCS_BY_ZENDPOINT_RESOURCE_INSTANCE = "/ne/{ne-id}/slc/by/zendpoint/{resource-instance}";
          String NE_SLC_ADDDROP_CREATE = "/ne/{ne-id}/slc/add-drop";
          String NE_SLC_EXPRESS_CREATE = "/ne/{ne-id}/slc/express";
          String NE_SLC_FOR_SLC_ID = "/ne/{ne-id}/slc/{slc-id}";
          String NE_SLC_DELETE = "/ne/{ne-id}/slc/{slc-id}";
          String NE_SLC_MODIFY_ENDPOINT_ADMINSTATE = "/ne/{ne-id}/slc/{slc-id}/{endpoint}";
          String NE_CRCTMAPS = "/ne/{ne-id}/crctmap";
          String NE_CRCTMAP_FOR_ALIAS = "/ne/{ne-id}/crctmap/{crctmap-id}";
          String NE_SLC_EQUALIZE = "/ne/{ne-id}/slc/{slc-id}/equalize/{direction-path}";
        }

        interface ALIEN {
          String PROVISION_PTP = "/ne/{ne-id}/mit/me/1/eqh/shelf,{shelf-number}/eqh/slot,alien/eq/card/ptp/alien,{ptp-number}";
          String PROVISION_PTP_AUTO = "/ne/{ne-id}/mit/me/1/eqh/shelf,{shelf-number}/eqh/slot,alien/eq/card/ptp/alien";
          String PROVISION_CTP_OTSIA = "/ne/{ne-id}/mit/me/1/eqh/shelf,{shelf-number}/eqh/slot,alien/eq/card/ptp/alien,{ptp-number}/ctp/otsia";
          String REMOVE_PTP = "ne/{ne-id}/mit/me/1/eqh/shelf,{shelf-number}/eqh/slot,alien/eq/card/ptp/alien,{ptp-number}";
          String REMOVE_CTP_OTSIA = "ne/{ne-id}/mit/me/1/eqh/shelf,{shelf-number}/eqh/slot,alien/eq/card/ptp/alien,{ptp-number}/ctp/otsia";
        }

        interface FLTP {
          String CARD_FLTP = "/ne/{ne-id}/card/{module-aid}/fltp/{fltp-type}";
        }

        interface GTP {
          String CARD_GTP = "/ne/{ne-id}/card/{module-aid}/gtp/{gtp-type}";
        }

        interface MODULE {
          String CARD_MODULE = "/ne/{ne-id}/card/{module-aid}";
        }

        interface PROT_GROUP {
          String PATH = "/ne/{ne-id}/prot-group";
          String SWTCH_PRT = "/ne/{ne-id}/swtch-prt/{protection-group-uri}";

          String CREATE = "/prtgrp";

          String DELETE = "/ne/{ne-id}/prtgrp/delete/{prtgrp-aid}";
          String CREATE_CCCP = "/cccp";
          String DELETE_CCCP = "/ne/{ne-id}/cccp/{cccp-name}";

          String FIND_BY_EPTE = "/ne/{ne-id}/prtgrp/{epte-uri}";
          String FIND_BY_FIND_PROTECTION_GROUP_BY_WORKING_AND_PROTECTING_ENTITY = "/ne/{ne-id}/prtgrp/{work-or-protect-1}/{work-or-protect-2}";

          String UPDATE_EPTE = "/ne/{ne-id}/prtgrp/{prtgrp-uri}/update/epte/{epte-id}/{epte-uri}";
          String CLEAR_EPTE = "/ne/{ne-id}/prtgrp/{prtgrp-uri}/update/epte/{epte-id}";
        }

        interface CARD_RESOURCES {
          String CARD_REF_FOR_NE_AND_CARD_AID = "/ne/{ne-id}/card-aid/{aid}";
          String CARD_REF_FOR_NE_AND_CARD_URI = "/ne/{ne-id}/card-uri/{uri}";
          String CARD_REF_TRAFFIC_MODULES_FOR_NE = "/ne/{ne-id}/traffic-modules";
          String PLUG_REFS_FOR_NE_AND_MODULE_AID = "/ne/{ne-id}/module-aid/{aid}/plugs";
          String PTP_REFS_FOR_NE_AND_MODULE_AID = "/ne/{ne-id}/module-aid/{aid}/ptps";
          String CTP_REFS_FOR_NE_AND_MODULE_AID = "/ne/{ne-id}/module-aid/{aid}/ctps";
          String CROSS_REFS_FOR_NE_AND_MODULE_AID = "/ne/{ne-id}/module-aid/{aid}/cross";
          String CARD_CLUSTER_REF_FOR_NE_AND_CARD_AID = "/ne/{ne-id}/card-aid/{aid}/cluster";
          String CARD_CLUSTER_REF_FOR_NE_AND_CARD_URI = "/ne/{ne-id}/card-uri/{uri}/cluster";
          String CARD_CLUSTER_CROSS_REFS_FOR_NE_AND_CARD_CLUSTER_NAME = "/ne/{ne-id}/cluster-name/{cluster-name}/cross";
        }

        interface CROSS_CONNECT_RESOURCES {
          String CARD_REF_FOR_NE_AND_CROSS_CONNECT_AID = "/ne/{ne-id}/cross-aid/{cross-aid}";
          String CARD_REF_FOR_NE_AND_CROSS_CONNECT_URI = "/ne/{ne-id}/cross-uri/{cross-uri}";
          String URI_FOR_NE_UNPROTECTED_CROSS_CONNECT_URI_PREFIX_AND_ENDPOINTS = "/ne/{ne-id}/cross-uri-prefix/{cross-uri-prefix}/aEndpoint/{aEndpoint}/zEndpoint/{zEndpoint}/unprotected";
          String URI_FOR_NE_CROSS_CONNECT_URI_PREFIX_AND_ENDPOINTS = "/ne/{ne-id}/cross-uri-prefix/{cross-uri-prefix}/aEndpoint/{aEndpoint}/zEndpoint/{zEndpoint}";
        }

        interface CTP_RESOURCES {
          String CTP_REF_FOR_NE_AND_CTP_URI = "/ne/{ne-id}/ctp-uri/{uri}";
          String CTP_REF_FOR_NE_AND_CTP_AID = "/ne/{ne-id}/ctp-aid/{aid}";

          String CTP_REF_EXTENDED_FOR_NE_AND_CTP_URI = "/ne/{ne-id}/ctp-uri/{uri}/ctp-extended";

          String CTP_REF_EXTENDED_FOR_NE_AND_CTP_AID = "/ne/{ne-id}/ctp-aid/{aid}/ctp-extended";
          String PTP_REF_FOR_NE_AND_CTP_AID = "/ne/{ne-id}/ctp-aid/{aid}/ptp";
          String PTP_REF_FOR_NE_AND_CTP_URI = "/ne/{ne-id}/ctp-uri/{uri}/ptp";
          String CARD_REF_FOR_NE_AND_CTP_AID = "/ne/{ne-id}/ctp-aid/{aid}/card";
          String CARD_REF_FOR_NE_AND_CTP_URI = "/ne/{ne-id}/ctp-uri/{uri}/card";
          String PLUG_REF_FOR_NE_AND_CTP_AID = "/ne/{ne-id}/ctp-aid/{aid}/plug";
          String PLUG_REF_FOR_NE_AND_CTP_URI = "/ne/{ne-id}/ctp-uri/{uri}/plug";
          String PARENT_AID_FOR_NE_AND_CTP_URI = "/ne/{ne-id}/ctp-uri/{uri}/parent";
          String PARENT_URI_FOR_NE_AND_CTP_URI = "/ne/{ne-id}/ctp-uri/{uri}/parent-uri";

          String URI_FOR_CTP_URI_PREFIX_AND_PARAMS = "/ne/{ne-id}/ctp-uri-prefix/{ctp-uri-prefix}/tp/{tp}/ts/{ts}/ctp-uri";
        }

        interface FIBER_RESOURCES {
          String NE_FIBERS = "/ne/{ne-id}/fibers";
        }

        interface PLUG_RESOURCES {
          String PLUG_REF_FOR_NE_AND_PLUG_URI = "/ne/{ne-id}/plug-uri/{uri}";
          String PLUG_REF_FOR_NE_AND_PLUG_AID = "/ne/{ne-id}/plug-aid/{aid}";
          String PTP_REFS_FOR_NE_AND_PLUG_AID = "/ne/{ne-id}/plug-aid/{aid}/ptps";
          String PTP_REFS_FOR_NE_AND_PLUG_URI = "/ne/{ne-id}/plug-uri/{uri}/ptps";
          String CARD_REF_FOR_NE_AND_PLUG_AID = "/ne/{ne-id}/plug-aid/{aid}/card";
        }

        interface PTP_RESOURCES {
          String PTP_REF_FOR_NE_AND_PTP_URI = "/ne/{ne-id}/ptp-uri/{uri}";
          String PTP_REF_FOR_NE_AND_PTP_AID = "/ne/{ne-id}/ptp-aid/{aid}";
          String CARD_REF_FOR_NE_AND_PTP_AID = "/ne/{ne-id}/ptp-aid/{aid}/card";
          String CARD_REF_FOR_NE_AND_PTP_URI = "/ne/{ne-id}/ptp-uri/{uri}/card";
          String PLUG_REF_FOR_NE_AND_PTP_URI = "/ne/{ne-id}/ptp-uri/{uri}/plug";
          String PLUG_REF_FOR_NE_AND_PTP_AID = "/ne/{ne-id}/ptp-aid/{aid}/plug";
          String CTP_REFS_FOR_NE_AND_PTP_AID = "/ne/{ne-id}/ptp-aid/{aid}/ctps";
          String CTP_REFS_EXTENDED_FOR_NE_AND_PTP_AID = "/ne/{ne-id}/ptp-aid/{aid}/ctps-extended";
        }

        interface PROT_GROUP_RESOURCES {
          String PROT_GROUP_REF_FOR_NE_AND_PROT_GROUP_URI = "/ne/{ne-id}/pg-uri/{uri}";
          String PROT_GROUP_REF_FOR_NE_AND_PROT_GROUP_AID = "/ne/{ne-id}/pg-aid/{aid}";
          String CCCP_PROT_GROUP_REF_FOR_NE_AND_CCCP_AID = "/ne/{ne-id}/cccp-aid/{aid}";
          String CCCP_PROT_GROUP_REFS_FOR_NE = "/ne/{ne-id}/cccp";
          String CARD_REF_FOR_PROT_GROUP_AID = "/ne/{ne-id}/pg-aid/{aid}/card";
          String CCCP_PROT_GROUP_REF_FOR_NE_AND_WORKING_OR_PROTECTING_CTP_URI = "/ne/{ne-id}/cccp/work-prot-ctp-uri/{uri}";
        }

        interface POWER_LEVEL {
          String PATH = "/ne/{ne-id}/entity/{aid}/power-level";
          String OTSIA = "/ne/{ne-id}/entity/otsia/{aid}/power-level";
        }

      }

    }
  }

  interface TOPOLOGY_MODEL {
    String PATH = "topology";
    String RESCAN = "rescan";
    String NETWORK_ELEMENTS = "networkelements";
    String MODULES = "modules";
    String PORTS = "ports";
    String PORT_LOCATIONS = "portlocations";
    String CONNECTION_POINTS = "cp";
    String CROSS_CONNECTS = "cc";
    String LINKS = "links";
    String TRAILS = "trails";
    String SEGMENT_ADAPTATIONS = "segment-adaptations";
    String NETWORK_CONNECTIONS = "networkconnections";
    String PATHS = "paths";
    String ELEMENT = "element";
    String MODULE_HIERARCHY = "module-hierarchy";
    String LAYER_HIERARCHY = "layer-hierarchy";
    String LAYER = "layer";
    String SERVERTRAIL = "servertrail";
    String USECONNECTION = "useconnection";
    String DELETABLE = "deletable";
    String LABEL = "label";
    String EVENTS = "events";
    String STATISTICS = "stats";
    String CREATE = "create";
    String SWITCH_PROTECTION = "switch";
    String SWITCH_PROTECTION_NC = "switch_nc";
    String LAYERS ="layers";

    String DB_PREFIX = "db/";
    String PHY_LINES = "phylines/";
    String PATH_TRACE = "pathtrace";
    String MTP_PATH_TRACE = "mtppathtrace";
    String POTENTIAL_START_POINTS = "potential-start-points";
    String POTENTIAL_START_POINTS2 = "potential-start-points2";
    String SERVICE_LOG = "servicelog";

    String MANAGE ="managed_state";

    public interface LineEditorPage {
      public static final String PATH = "topology/lineeditor";
      public static final String AVAILABLE_TERMINATION_POINTS = "termpoints";
      public static final String AUTO_DETECT = "auto_detect";
      public static final String SINGLE_TP = "single_tp";
    }

    interface LineDetailsPage {
      String PATH = "topology/lineDetails";
      String BANDWIDTH= "bandwidth";
      String CP_LIFS= "cplifs";

    }
    interface LineVlanPage {
      public static final String PATH = "topology/outerVlan";
      public static final String FOR_LINE= "forLine";
    }

    interface TOPOLOGY_DIAGNOSTICS {
      String PATH = TOPOLOGY.PATH + "/diagnostics";
      String MANAGED_SERVICES = "services/managed";
      String MANAGED_DATASERVICES = "services/dataservices";
      String MANAGED_SERVICES_BYID = "services/byid";
      String DATA_SERVICES = "services/ds";
      String LAYERS = "supported_layers";
      String LINES = "lines";
      String FIX_PTPS = "fix_ptps";
      String FIX_LINES = "fix_lines";
      String ADAPTATION_CLIENT = "adaptationclient";
      String DIAGNOSTICS_PROCESS = "process";
      String COMPLETED_PROCESSSING = "completed";
    }
  }

  interface PD_TOPOLOGY_MODEL {
    String PATH = "pdtopology";
    String RESCAN = "rescan";
    String NETWORK_ELEMENTS = "networkelements";
    String MODULES = "modules";
    String PORTS = "ports";
    String PORT_LOCATIONS = "portlocations";
    String CONNECTION_POINTS = "cp";
    String CROSS_CONNECTS = "cc";
    String LINKS = "links";
    String TRAILS = "trails";
    String SEGMENT_ADAPTATIONS = "segment-adaptations";
    String NETWORK_CONNECTIONS = "networkconnections";
    String PATHS = "paths";
    String ELEMENT = "element";
    String MODULE_HIERARCHY = "module-hierarchy";
    String LAYER_HIERARCHY = "layer-hierarchy";
    String LAYER = "layer";
    String SERVERTRAIL = "servertrail";
    String USECONNECTION = "useconnection";
    String DELETABLE = "deletable";
    String LABEL = "label";
    String EVENTS = "events";
    String STATISTICS = "stats";
    String CREATE = "create";
    String SWITCH_PROTECTION = "switch";
    String SWITCH_PROTECTION_NC = "switch_nc";
    String LAYERS ="layers";

    String DB_PREFIX = "db/";
    String PHY_LINES = "phylines/";
    String PATH_TRACE = "pathtrace";
    String MTP_PATH_TRACE = "mtppathtrace";
    String POTENTIAL_START_POINTS = "potential-start-points";
    String POTENTIAL_START_POINTS2 = "potential-start-points2";
    String SERVICE_LOG = "servicelog";

    String MANAGE ="managed_state";

    public interface LineEditorPage {
      public static final String PATH = "pdtopology/lineeditor";
      public static final String AVAILABLE_TERMINATION_POINTS = "termpoints";
      public static final String AUTO_DETECT = "auto_detect";
      public static final String SINGLE_TP = "single_tp";
    }

    interface LineDetailsPage {
      String PATH = "pdtopology/lineDetails";
      String BANDWIDTH= "bandwidth";
      String CP_LIFS= "cplifs";

    }
    interface LineVlanPage {
      public static final String PATH = "pdtopology/outerVlan";
      public static final String FOR_LINE= "forLine";
    }

    interface TOPOLOGY_DIAGNOSTICS {
      String PATH = PD_TOPOLOGY.PATH + "/diagnostics";
      String MANAGED_SERVICES = "services/managed";
      String MANAGED_DATASERVICES = "services/dataservices";
      String MANAGED_SERVICES_BYID = "services/byid";
      String DATA_SERVICES = "services/ds";
      String LAYERS = "supported_layers";
      String LINES = "lines";
      String FIX_PTPS = "fix_ptps";
      String FIX_LINES = "fix_lines";
      String ADAPTATION_CLIENT = "adaptationclient";
      String DIAGNOSTICS_PROCESS = "process";
      String COMPLETED_PROCESSSING = "completed";
    }
  }


  public interface Lines {
    public static final String PATH = "lines";
    public static final String GET_ALL_LINES = "getAllLines";
    public static final String PROTECTED = "protected";
    public static final String TIMING_LINE = "timingLine";
    public static final String TIMING_LINE_GROUP_PORT_TO_GROUP_PORT_LINK_VALIDATION = "timingLine-groupPortToGroupPortLinkValidation";
    public static final String GET_SUB_CHANNEL_CONNECTIONS_COUNT = "sub-channel-connections";
    public static final String GET_CONNECTIONS_COUNT_BY_TYPE_FOR_LINE = "connections-count-by-type";
    public static final String GET_AFFECTED_SERVICES_FOR_LINE = "affected-services";
    public static final String GET_AFFECTED_ETHERNET_RINGS_FOR_LINE = "affected-rings";
    public static final String GET_INTER_SUBNET_LINES = "inter-subnet-lines";
    public static final String GET_SUBNET_LINES = "subnet-lines";
    public static final String GET_RSM_PROTECTION_DATA = "rsm-protection-data";
    public static final String GET_BANDWIDTH_USAGE_TRESHOLDS = "bandwidth-usage-thresholds";
    public static final String NOTIFY_NEW_LINE_CREATED = "notify-new-line-created";
    public static final String CREATE_LINES = "createLines";
    public static final String INSERT_NODE_IN_LINK = "insertNodeInLink";
    public static final String SUMMARY = "summary";
    public static final String ALM_PORTS_ASSIGNED_TO_LINES = "alm-lines";

    String ASSIGN_BANDWIDTH_RESTRICTION = "assignBandwidthRestriction";
    String UNASSIGN_BANDWIDTH_RESTRICTION = "unassignBandwidthRestriction";
  }

  public interface Traffic {
    public static final String PATH = "traffic";
    public static final String MAKE_FREE = "make-free";
    public static final String SET_ACTIVE = "set-active";
  }

  public interface Provisioning {
    public static final String PATH = "provisioning";
    public static final String PROVISION_INTRA_NE_CONNECTION = "provisionIntraNeConnection";
    public static final String PROVISION_EXTERNAL_CHANNEL = "provisionExternalChannel";
    public static final String UNPROVISION_EXTERNAL_CHANNEL = "unprovisionExternalChannel";
    public static final String PROVISION_EOM = "provisionExternalObjectMultiplexer";
    public static final String UNPROVISION_EOM = "unprovisionExternalObjectMultiplexer";
    public static final String PROVISION_VECH = "provisionVirtualExternalChannel";
    public static final String UNPROVISION_VECH = "unprovisionVirtualExternalChannel";
  }

  public interface DRIVER_MANAGER{
    public static final String PATH = "drivermanager";
    public static final String DRIVERS = "drivers";
    public static final String REGISTERED_NE_TYPE_STRINGS = "registerNeTypeStrings";
    public static final String REGISTERED_NE_TYPE_BY_TYPE_CODE = "registeredNeTypeByTypeCode";
    public static final String REGISTERED_NE_TYPE_BY_TYPE_NAME = "registeredNeTypeByTypeName";
  }
  public interface DISPLAY_SETTINGS {
    public static final String PATH = "displaysettings";
    public static final String GET_NE_ID_TYPE = "getneidtype";
    public static final String SET_NE_ID_TYPE = "setneidtype";
    public static final String NE_ICON_LABEL_SETTINGS = "neiconlabel";
    String TIME_ZONE_ENABLED = "timeZoneEnabled";
    String TIME_ZONE_ID = "timeZoneID";
    String NE_ICONS_FOR_TYPE = "customimages/netypes";
    // TODO String NE_ICONS_SHARED = "netypes/all";
  }

  public interface IMPORT_EXPORT {
    public static final String PATH = "importexport";

    public static final String SERVICES = "services";
    public static final String SERVICE_TREE = "servicetree";
    String LINKS = "links";
  }

  public interface TNMS_IMPORT {
    public static final String PATH = "tnmsimport";

    public static final String SUBNET = "tree";
    public static final String SUBNET_ADD = "tree/add";
    public static final String LINE_ADD = "line/add";
    public static final String NETWORK_ELEMENTS = "networkelements";
  }

  interface RES_TAB {
    String PATH = "restab";
    String CSV = "csv";
    String CSV_SYNC = "csvsync";
    String NODE = "node";
  }

  public interface NMS_MANUAL {
    public static final String PATH = "manual";
    public static final String NMS_MANUAL_TEST = "test";
    public static final String BFA = "bfa";
    public static final String BFA_TEST = "bfatest";
  }

  public interface CSV_TRANSFER {
    public static final String PATH = "csvfiletransfer";

    public static final String PREFERENCES = "preferences";
    public static final String PROPERTIES = "properties";

  }

  //Ethernet Config File Manager
  interface ECM {
    String PATH = "ecm";
    String PROFILE = "profile";
    String FUNCTION = "function";
    String MAX_PARALLEL_UPLOADS = "maxParallelUploads";
    String MAX_CONFIG_FILES = "maxConfigFiles";
    String SECURITY_MODE = "securityMode";
    String SERVER_IP = "serverIP";
    String FILENAMES = "fileNames";
    String FILE_SUMMARIES = "fileSummaries";
    String FILE_SUMMARIES_MULTI = "fileSummariesMulti";
    String FILE_INFOS = "fileInfos";
    String FILE = "file";
    String TEMPLATE = "template";
    String TEMPLATE_MODE = "templateMode";
    String TEMPLATE_NE_TYPES = "templateNeTypes";
    String ETH_SERVICE_TEMPLATES = "ethServiceTemplates";
    String BULK_TRANSFER = "bulkTransfer";
    String BULK_TRANSFER_ALL_NEIDS = "bulkTransferAllNeIds";
    String BULK_TRANSFER_ABORT = "bulkTransferAbort";
    String ABORT_TRANSFERS = "abortTransfers";
    String BULK_TRANSFER_LOG = "bulkTransferLog";
    String BULK_TRANSFER_SUMMARY = "bulkTransferSummary";
    String BULK_TRANSFER_STATUS = "bulkTransferStatus";
    String BULK_TRANSFER_STATUS_CLEAR = "bulkTransferStatusClear";
    String SCHEDULE_SELECTED_FILE = "scheduleSelectedFile";
    String SCHEDULE_SELECTED_FILE_DATA = "scheduleSelectedFileData";
    String SCHEDULE_DATE = "scheduleDate";
    String SCHEDULED_NES = "scheduledNes";
    String SCHEDULED_SUBNETS = "scheduledSubnets";
    String SCHEDULED_CONFIG_FILE_ASSIGNMENT ="scheduledConfigFileAssignment";
    String CLEAR_CONFIG_FILE_ASSIGNMENT ="clearConfigFileAssignment";
    String DELTA_CONFIG_FILE = "deltaConfigFile";
    String APPLY_CONFIG_FILE_IPADDRESS = "applyConfigFileToIp";
    String COMPLETE_CONFIG_FILE = "completeConfigFile";
    String APPLY_CONFIG_FILE = "applyConfigFile";
    String APPLY_DELTA_CONFIG_FILE = "applyDeltaConfigFile";
    String FORCE_CONNECT_TO_NE = "forceConnectToNE";
    String CONFIG_FILE_APPLY_MODE = "configFileApplyMode";
    String CONFIG_FILE_APPLY_MODE_BULK = "configFileApplyModeBulk";
    String NEID = "neid";
    String NETYPE = "neType";
    String IS_NE_DISCOVERED = "isNeDiscovered";
    String FTP_SETTINGS = "ftpSettings";
    String UPDATE_GLOBAL_SETTINGS = "updateGlobalSettings";
    String TEST = "test";
    interface TFW {
      String PROFILE_TEST = "profileTest";
      String FUNCTION_TEST = "functionTest";
      String FUN_DESC = "funDesc";
    }
  }

  public interface SNT{
    public static final String PATH = "snt";
    public static final String SNT_IS_NETWORK_VIEW_RESTRICTED = "get/isnetviewrestricted";
    public static final String SNT_GET_SENSORS = "get/sensors";
    public static final String SNT_SENSORS_UPDATE_COLLECTION_STATE = "update/sensors/collectionstate";
    public static final String SNT_UPDATE_COLLECTION_STATE = "update/snt/collectionstate";
    public static final String SNT_UPDATE_COLLECTION_INTERVAL = "update/snt/collectioninterval";
    public static final String SNT_GET_SETUP = "get/setup";
    public static final String SNT_GNMI_GET_NE_SUBSCRIBES = "get/nesubscriptions";
    public static final String SNT_GNMI_UPDATE_NE_SUBSCRIPTIONS = "update/nesubscriptions";
    public static final String SNT_SYNC = "update/snt/sync";
    public static final String SNT_QUALITY_COMPLIANCE_GET_STATUSES_SUMMARY = "/snt/qualitycompliance/statuses/summary";
    public static final String SNT_QUALITY_COMPLIANCE_GET_STATUSES_AVG_ABS_OFFSETS = "/snt/qualitycompliance/statuses/avgabsoffset";
    public static final String SNT_QUALITY_COMPLIANCE_GET_OFFSETS_HISTOGRAM = "/snt/qualitycompliance/offsets/histogram";
    public static final String SNT_QUALITY_COMPLIANCE_GET_OFFSETS_BY_NE = "/snt/qualitycompliance/offsets/nes";
    public static final String SNT_QUALITY_COMPLIANCE_GET_OFFSETS_SUMMARY = "/snt/qualitycompliance/offsets/summary";
    public static final String SNT_QUALITY_COMPLIANCE_GET_COUNTER = "/snt/qualitycompliance/counter";
  }

  public interface SYNCM {
    public static final String PATH = "sync";
    public static final String HEALTH = "health";
    public static final String TEST = "test";
    public static final String TEST_RESULT = "test/result";
    public static final String TEST_DEL = "test/deletion";
    public static final String TEST_DEL_ALL = "test/deletionall";
    public static final String TEST_STOP = "test/stop";
    public static final String SET_SYNC_TEST_DEBUG_MODE = "test/setDebugMode";
    public static final String PRESET_SYNC_TEST_DEBUG_MODE = "test/presetDebugMode";
    public static final String TEST_NEXT_AVAILABLE_PROBE_AID= "test/nextAvailableProbeAid";
    public static final String TPA_COLLECTION_PROBE_DEBUG_MODE_MAX_COUNT_REACHED = "test/debugmodemaxcountreached";
    public static final String TEST_STOP_ALL = "test/stopall";
    public static final String TEST_RESCHEDULE = "test/reschedule";
    public static final String TEST_UPDATE = "test/update";
    public static final String NODE = "node";
    public static final String NODE_PTP_CLOCK = "node/ptpclock";
    public static final String NODE_PTP_CLOCK_FORM_MODEL = "node/ptpclock/formmodel";
    public static final String NODE_TIME_CLOCK_FORM_MODEL = "node/timeclock/formmodel";
    public static final String NODE_PTP_PORTS_FORM_MODEL = "node/ptpports/formmodel";
    public static final String NODE_L3_PTP_PORTS_FORM_MODEL = "node/l3ptpports/formmodel";
    public static final String NODE_GNSS_FORM_MODEL = "node/gnss/formmodel";
    public static final String NODE_SYNCE_FORM_MODEL = "node/synce/formmodel";
    public static final String NODE_SYNCE_DETAILS_FORM_MODEL = "node/synce/details/formmodel";
    public static final String NODE_PRC_DETAILS_FORM_MODEL = "node/prc/details/formmodel";
    public static final String NODE_SYSTEM_CLOCK_DETAILS_FORM_MODEL = "node/systemclock/details/formmodel";
    public static final String NODE_NETWORK_ELEMENT_INFO = "node/network/element/info";
    public static final String NODE_TIME_CLOCK_DETAILS_FORM_MODEL = "node/timeclock/details/formmodel";
    public static final String NODE_FREQUENCY_CLOCK_PG_FORM_MODEL = "node/frequencyclock/pg/formmodel";
    public static final String NODE_TIME_CLOCK_PG_FORM_MODEL = "node/timeclock/pg/formmodel";
    public static final String NODE_MCI_PG_FORM_MODEL = "node/mci/pg/formmodel";
    public static final String NODE_MASTER_CLOCK_DETAILS_FORM_MODEL = "node/masterclock/details/formmodel";
    public static final String NODE_BOUNDARY_CLOCK_DETAILS_FORM_MODEL = "node/boundaryclock/details/formmodel";
    public static final String NODE_TRANSPARENT_CLOCK_DETAILS_FORM_MODEL = "node/transparentclock/details/formmodel";
    public static final String NODE_SOOC_DETAIL_FORM_MODEL = "node/sooc/details/formmodel";
    public static final String NODE_TELECOM_SLAVE_DETAILS_FORM_MODEL = "node/telecomslave/details/formmodel";
    public static final String NODE_MCI_DETAILS_FORM_MODEL = "node/mci/details/formmodel";
    public static final String NODE_PTP_CLOCK_DETAILS_FORM_MODEL = "node/ptpclock/details/formmodel";
    public static final String NODE_PTP_PORT_DETAILS_FORM_MODEL = "node/ptpport/details/formmodel";
    public static final String NODE_L3_PTP_PORT_DETAILS_FORM_MODEL = "node/l3ptpport/details/formmodel";
    public static final String NODE_GNSS_ports_FORM_MODEL = "node/gnssports/details/formmodel";
    public static final String NODE_NTP_CLOCK_FORM_MODEL = "node/ntpclock/details/formmodel";
    public static final String NODE_NTP_CLOCK_INTERFACE_FORM_MODEL = "node/ntpclockinterface/details/formmodel";

    public static final String NODE_ALL_NTP_CLOCK_TABLE_MODEL = "node/all/ntp/clock/tablemodel";
    public static final String NODE_ALL_NTP_CLOCK_INTERFACE_TABLE_MODEL = "node/all/ntp/clock/interfaces/tablemodel";
    public static final String NODE_STL_PORTS_FORM_MODEL = "node/stlports/details/formmodel";
    public static final String NODE_HISTORICAL_GNSS_PORTS_FORM_MODEL = "node/histgnssports/details/formmodel";
    public static final String NODE_BITS_PORT_FORM_MODEL = "node/bitsport/details/formmodel";
    public static final String NODE_ALL_SYNCE_TABLE_MODEL = "node/all/synce/tablemodel";
    public static final String NODE_ALL_PRC_TABLE_MODEL = "node/all/prc/tablemodel";
    public static final String NODE_ALL_PRC_FORM_MODEL = "node/all/prc/formmodel";
    public static final String NODE_ALL_SYSTEM_CLOCK_FORM_MODEL = "node/all/systemclock/formmodel";
    public static final String NODE_ALL_SYSTEM_CLOCK_TABLE_MODEL = "node/all/systemclock/tablemodel";
    public static final String NODE_ALL_SYNCE_FORM_MODEL = "node/all/synce/formmodel";
    public static final String NODE_ALL_BITS_TABLE_MODEL = "node/all/bits/tablemodel";
    public static final String NODE_ALL_TIME_CLOCK_TABLE_MODEL = "node/all/time/clock/tablemodel";
    public static final String NODE_ALL_TIME_CLOCK_FORM_MODEL = "node/all/time/clock/formmodel";
    public static final String NODE_ALL_MASTER_CLOCK_TABLE_MODEL = "node/all/master/clock/tablemodel";
    public static final String NODE_ALL_BOUNDARY_CLOCK_TABLE_MODEL = "node/all/boundary/clock/tablemodel";
    public static final String NODE_ALL_TRANSPARENT_CLOCK_TABLE_MODEL = "node/all/transparent/clock/tablemodel";
    public static final String NODE_ALL_MCI_TABLE_MODEL = "node/all/mci/tablemodel";
    public static final String NODE_ALL_SOOC_TABLE_MODEL = "node/all/sooc/tablemodel";
    public static final String NODE_ALL_TS_TABLE_MODEL = "node/all/ts/tablemodel";
    public static final String NODE_ALL_PTP_CLOCK_TABLE_MODEL = "node/all/ptp/clock/tablemodel";
    public static final String NODE_ALL_PTP_PORT_TABLE_MODEL = "node/all/ptp/port/tablemodel";
    public static final String NODE_ALL_L3_PTP_PORT_TABLE_MODEL = "node/all/ptp/l3port/tablemodel";
    public static final String NODE_ALL_GNNS_PORTS_TABLE_MODEL = "node/all/gnss/ports/tablemodel";
    public static final String NODE_ALL_STL_PORTS_TABLE_MODEL = "node/all/stl/ports/tablemodel";

    public static final String NODE_ALL_GNNS_PORTS_FORM_MODEL = "node/all/gnss/ports/formmodel";
    public static final String NODE_ALL_STL_PORTS_FORM_MODEL = "node/all/stl/ports/formmodel";
    public static final String NODE_TIME_CLOCK_REF_TABLE_MODEL = "node/timeclockref/tablemodel";
    public static final String NODE_TIME_CLOCK_GNSS_PORTS_TABLE_MODEL = "node/all/timeclockgnss/ports/tablemodel";
    public static final String NODE_TIME_CLOCK_GNSS_SATELLITE_TABLE_MODEL = "node/all/timeclocksat/ports/tablemodel";
    public static final String NODE_TIME_CLOCK_STL_PORTS_TABLE_MODEL = "node/all/timeclockstl/ports/tablemodel";
    public static final String NODE_TIME_CLOCK_IRIDIUM_SATELLITE_TABLE_MODEL = "node/all/timeclockiridium/ports/tablemodel";

    public static final String NODE_MASTER_CLOCK_VLAN_IPS_TABLE_MODEL = "node/masterclok/vlan/ips/tablemodel";
    public static final String NODE_SYNC_REF_TABLE_MODEL = "node/syncref/tablemodel";
    public static final String NODE_SYNC_REF_TABLE_BY_NE_ID_MODEL = "node/syncref/table/byneid/model";
    public static final String NODE_TIME_CLOCK_REF_TABLE_MODEL_BY_NE_ID = "node/timeclock/table/model/byneid";
    public static final String NODE_SATELLITE_TABLE_MODEL_BY_NE_ID = "node/satellite/table/model/byneid";
    public static final String NODE_IRIDIUM_SATELLITE_TABLE_MODEL_BY_NE_ID = "node/iridiumsatellite/table/model/byneid";
    public static final String NODE_SATELLITE_TABLE_MODEL_BY_GNSS_ID = "node/satellite/table/model/bygnssid";
    public static final String NODE_SATELLITE_TABLE_MODEL = "node/satellite/tablemodel";
    public static final String NODE_UNMANANGED_FORM_MODEL = "node/unmanaged/formmodel";
    public static final String NODE_TC_FORM_MODEL = "node/tc/formmodel";
    public static final String NODE_MASTER_CLOCK_FORM_MODEL = "node/masterclock/formmodel";
    public static final String NODE_BOUNDARY_CLOCK_FORM_MODEL = "node/boundaryclock/formmodel";
    public static final String NODE_MCI_TABLE_MODEL = "node/mci/tablemodel";
    public static final String NODE_MCI_PROPERTIES_FORM_MODEL = "node/mciproperties/formmodel";
    public static final String NODE_MCI_REMOTE_SLAVES_TABLE_MODEL = "node/mciremoteslaves/tablemodel";
    public static final String NODE_PTP_PORT_REMOTE_SLAVES_TABLE_MODEL = "node/ptpportremoteslaves/tablemodel";
    public static final String NODE_OC_SLAVE_FORM_MODEL = "node/ts/formmodel";
    public static final String NODE_SOOC_FORM_MODEL = "node/sooc/formmodel";
    public static final String NE_DETAILS_FORM_MODEL = "node/nedetails/formmodel";
    public static final String NODE_SOOC_DETAILS_FORM_MODEL = "node/soocdetails/formmodel";
    public static final String NODE_PTP_PORT = "node/ptpport";
    public static final String NODE_PTP_PEER_PORT = "node/ptppeerport";
    public static final String NODE_L3_PTP_PORT = "node/l3ptpport";
    public static final String PORT_OUTAGE_OPER_STATE = "node/portoutageoperstate";
    public static final String MCI_OUTAGE_OPER_STATE = "node/mcioutageoperstate";
    public static final String PTP_CLOCK_PROFILE = "node/ptpclockprofile";
    public static final String PTP_SATELLITE = "node/satellite";
    public static final String CONNECTED_NODES = "node/connectednodes";
    public static final String NODE_NAME = "node/name";
    public static final String CONNECTED_MASTER = "node/connectedmaster";
    public static final String NODE_SLAVE = "node/slave";
    public static final String NODE_MASTER = "node/master";
    public static final String NODE_MODIFY = "node/modify";
    public static final String NODE_MODIFY_BC_CLASS = "node/modify/bcClass";
    public static final String NODE_MODIFY_ALL = "node/modify/all";
    public static final String NCD = "ncd";
    public static final String TEST_NAME = "test/name";
    public static final String IP_PROTOCOL = "test/ipprotocol";
    public static final String BC_CLASS_SUPPORT = "bcclass/support";
    public static final String DEFAULT_SYNC_NODE_SELECTION = "test/defaultSyncNodeSelection";
    public static final String DEFAULT_DEVICE_SELECTION = "test/defaultDeviceSelection";
    public static final String ENTITIES_CONNECTION = "entities/connection";
    public static final String PORT_IDENTITY = "port/identity";
    public static final String PORT_IDENTITY_PTPFP = "port/identity/ptpfp";
    public static final String PORT_IDENTITY_NODE = "port/identity/node";
    public static final String PORT_IDENTITY_ALL = "port/identity/all";
    public static final String PORT_IDENTITY_BC_MEDIATION = "port/identity/bcMediation";
    public static final String PROBING_SLAVE = "port/probingSlave";
    public static final String MANAGED_SLAVE = "managed/slave";
    public static final String MANAGED_MASTER = "managed/master";
    public static final String PHYSICAL_ADDRESS = "physicalAddress";
    public static final String MASTER_AND_SLAVE_BY_PTP_FP = "master/slave/ptp/fp";
    public static final String MASTER_IPS = "master/ips";
    public static final String NE_VERSION = "neVersion";
    public static final String NE_TYPE = "neType";
    public static final String NE_TYPE_SYNC = "neTypeSync";
    public static final String BC_MEDIATION = "bcMediation";
    public static final String USER_MTIE_MASK = "userMTIEMask";
    public static final String USER_MTIE_MASKS = "userMTIEMasks";
    public static final String MASK_DATA_SERIES = "maskDataSeries";
    public static final String BC_STATIC = "bcStatic";
    public static final String MASTER_PORT_IDENTITY = "masterPortIdentity";
    public static final String SOOC_IPS = "soocIPs";
    public static final String SOOC = "sooc";
    public static final String L3_PORT = "l3Port";
    public static final String ETHERNET_PORTS = "ethernet/ports";
    public static final String GNSS_RECEIVER_SATELLITE = "gnss/receiver/satellites";
    public static final String GNSS_RECEIVER_USERS = "gnss/receiver/users";

    public static final String REDISCOVER =  "rediscover";
    public static final String DTAG_MODE_ENABLED =  "dtagModeEnabled";
    public static final String REMOTE_SLAVE = "remoteSlave";
    public static final String REMOTE_SLAVE_MODIFY = "remoteslave/modify";
    public static final String TESTS_FOR_REMOTE_SLAVES = "tests/remoteSlaves";
    public static final String SYNC_JACK_DATA = "jack/data";
    public static final String DEBUG_COMMAND = "debug/command";
    public static final String SLAVE_PTP_FLOWPOINTS = "slave/ptp/flowpoints";
    public static final String CREATE_PTP_FLOWPOINT = "create/ptp/flowpoint";
    public static final String PORT_FLOW = "port/flow";
    public static final String SJTOOL_APPS = "sjtool/apps";
    public static final String SJTOOL_HOSTS = "sjtool/hosts";
    public static final String TC = "tc";
    public static final String ROUTE_PROPS = "route/props";
    public static final String ROUTE_STATS = "route/stats";
    public static final String ROUTE_MODIFY = "route/modify";
    public static final String LOCAL_SLAVE = "node/localslave";
    public static final String SLAVE_IP = "slave/ip";
    public static final String PTP_CLOCK_PORT_ID = "ptp/clock/port/id";
    public static final String CREATE_PTP_PORT = "ptp/port/create";
    public static final String REMOTE_SLAVE_CAPACITY_THRESHOLD = "remoteslave/capacity/setThreshold";
    public static final String REMOTE_SLAVE_CAPACITY_SUPPRESS_ALARMS = "remoteslave/capacity/suppressALARMS";
    public static final String REMOTE_SLAVE_CONNECTION_SUPPRESS_ALARMS = "remoteslave/connection/suppressAlarms";
    public static final String REMOTE_SLAVE_CONNECTION_SELECTED_SUPPRESS_ALARMS = "remoteslave/connection/suppressSelectedAlarms";
    public static final String REMOTE_SLAVE_CONNECTION_PROPERTIES = "remoteslave/connection/properties";
    public static final String REMOTE_SLAVE_CONNECTION_DELETE_OUTAGE = "remoteslave/connection/deleteOutage";
    public static final String REMOTE_SLAVE_CONNECTION_DELETE_SELECTED = "remoteslave/connection/deleteSelected";
    public static final String SYNC_PROPERTIES = "syncproperties";
    public static final String PTP_CLOCK = "all/ptp/clock";

    // GNSS ASSURANCE - current Views
    public static final String GNSS_PORT = "gnssport";
    public static final String GNSS_PORTS = "gnssports";
    public static final String GNSS_PORTS_FOR_SUBNETS = "gnssports/subnets";
    public static final String GNSS_PORTS_STATUS_COUNTS = "/gnssport/statuscounts";
    public static final String GNSS_PORTS_STATUS_COUNTS_FOR_SUBNETS = "/gnssport/statuscounts/subnets";
    public static final String GNSS_SETTINGS_GET = "/gnss/settings";
    public static final String GNSS_SETTINGS_SET = "/gnss/settings/save";
    public static final String GNSS_PORT_LIVE_DETAILS = "/gnssportlive/details";

    // GNSS Assurance - historical Views
    public static final String GNSS_PORT_DETAILS = "gnssport/details";
//    public static final String GNSS_PORTS_OVER_TIME_RANGE = "/gnssport/timerange";
    public static final String GNSS_PORTS_OVER_TIME_RANGE_FOR_SUBNETS = "/gnssport/timerange/subnets";
    public static final String GNSS_PORTS_OVER_TIME_RANGE_FOR_NES = "/gnssport/timerange/nes";
    public static final String GNSS_PORTS_HISTORICAL_STATUS_COUNTS = "/gnssport/histstatuscounts";
    public static final String GNSS_PORTS_STATUS_INFO = "/gnssport/statusinfo";
    public static final String GNSS_PORTS_HISTORICAL_STATUS_COUNTS_FOR_SUBNETS = "/gnssport/histstatuscounts/subnets";
    public static final String GNSS_PORTS_HISTORICAL_STATUS_COUNTS_FOR_NES = "/gnssport/histstatuscounts/nes";
    public static final String GNSS_PORTS_SATELLITES_USAGE_HEAT_MAP = "/gnssport/satellites/usagemap";
    public static final String GNSS_PORTS_SATELLITES_CNO_HEAT_MAP = "/gnssport/satellites/cnomap";
    public static final String GNSS_PORTS_SKY_TRAILS = "/gnssport/skytrails";
    public static final String GNSS_PORTS_NUMBER_OF_SATELLITES = "/gnssport/numberofsatellites";
    public static final String GNSS_PORTS_AGC_HISTOGRAM = "/gnssport/agchistogram";
    public static final String GNSS_PORTS_CNO_AGC_LEVELS = "/gnssport/cnoagclevels";
    public static final String GNSS_PORTS_CNO_LEVELS_PER_SATELLITE = "/gnssport/cnolevelspersatellite";
    public static final String GNSS_PORTS_CNO_2_AGC = "/gnssport/cno2agc";
    public static final String GNSS_PORTS_SATELLITES_CNO_HISTOGRAM = "/gnssport/satellites/cnohistogram";
    public static final String GNSS_PORTS_NE_INFO = "/gnssport/neinfo";
    public static final String GNSS_PORTS_NE_SUBNET_INFO = "/gnssport/nesubnetinfo";
    public static final String GNSS_PORTS_NE_SUBNET_INFO_BY_IPS = "/gnssport/nesubnetinfo/byips";
    public static final String GNSS_PORT_HEALTH_TEST = "/gnssport/healthtest";
    public static final String GNSS_PORT_HEALTH_TEST_FAILURE_LOG = "/gnssport/healthtest/failurelog";
    public static final String GNSS_COLLECTION_OVERVIEW = "/gnss/collection/overview";
    public static final String GNSS_DATA_ACCESS_OVERVIEW = "/gnss/dataaccess/overview";

    // GNSS Assurance - MACHINE LEARNING ALARM
    // NOTE this is a necessary duplication of com.adva.gnsscommon.util.MDRestPath.GNSS_PORTS_MACHINE_LEARNING_ALARM
    // since we cannot import from com.adva.gnsscommon.util package
    public static final String GNSS_PORTS_MACHINE_LEARNING_ALARM = "/gnssport/machine-learning/alarm";

    // Sync Assurance - for raise/clear ALARM from external app
    // NOTE this is a necessary duplication of com.adva.tpacommon.util.TpaRestPath.SYNC_ASSURANCE_ALARM
    // since we cannot import from com.adva.tpacommon.util package
    public static final String SYNC_ASSURANCE_ALARM = "/syncassurance/alarm";

    // GNSS Firewall
    public static final String GNSS_FIREWALL_RULES = "/gnss/firewall/rules";
    public static final String GNSS_FIREWALL_RULES_UPDATE = "/gnss/firewall/rules/update";
    public static final String GNSS_FIREWALL_ENABLE = "/gnss/firewall/enable";
    public static final String GNSS_TIME_CLOCK_REF = "/gnss/firewall/timeref";
    public static final String GNSS_TIME_CLOCK_REF_LOCK = "/gnss/firewall/timeref/lock";

    //TP Assurance
    public static final String TPA_FILESERVERDATASAVE = "/fileserverdatasave";
    public static final String TPA_FILESERVERS = "/fileservers";
    public static final String TPA_FILESERVER_IP = "/fileserver";
    public static final String TPA_FILESERVER_DELETE_IP = "/fileserverdelete";
    public static final String TPA_FILESERVERDATASAVEALL  = "/fileserverdatasaveall";
    public static final String TPA_EXPORTTIE  = "/exporttie";
    public static final String TPA_GET_EXPORT_TO_FILE_MAX_RANGE_IN_DAYS  = "/getexporttofilemaxrangeindays";
    public static final String TPA_TIE_RESULTS = "/tieresults";
    public static final String TPA_DELETE_TEST_DATA = "/deletetestdata";
    public static final String TPA_COLLECTION_OVERVIEW = "/tpa/collection/overview";
    public static final String TPA_QMC_SCHEDULE_CALCULATION = "/tpa/qmc/scheduleCalculation";
    public static final String TPA_QMC_CANCEL = "/tpa/qmc/cancel";
    public static final String TPA_QMC_DELETE = "/tpa/qmc/delete";
    public static final String TPA_QMC_GET_RESULTS = "/tpa/qmc/results";
    public static final String TPA_ONLINE_QM_ASSIGN = "/tpa/onlineqm/assign";
    public static final String TPA_ONLINE_QM_UNASSIGN = "/tpa/onlineqm/unassign";
    public static final String TPA_ONLINE_QM_UNASSIGN_ALL = "/tpa/onlineqm/unassignall";
    public static final String TPA_ONLINE_QM_GET_RESULTS = "/tpa/onlineqm/results";
    public static final String TPA_ONLINE_QM_GET_RESULTS_LOG = "/tpa/onlineqm/resultslog";
    public static final String TPA_ONLINE_QM_PROFILE_CREATE = "/tpa/onlineqm/profile/create";
    public static final String TPA_ONLINE_QM_PROFILE_UPDATE = "/tpa/onlineqm/profile/update";
    public static final String TPA_ONLINE_QM_PROFILE_DELETE = "/tpa/onlineqm/profile/delete";
    public static final String TPA_ONLINE_QM_PROFILE_GET_ALL = "/tpa/onlineqm/profile/getall";
    public static final String TPA_ONLINE_QM_PROFILE_GET_ASSIGN_TO_TEST = "/tpa/onlineqm/profile/get-assign-to-test";
    public static final String TPA_ONLINE_QM_PROFILES_GET_ASSIGN_TO_TEST = "/tpa/onlineqm/profiles/get-assign-to-test";
    public static final String TPA_ONLINE_QM_PROFILE_GET_BY_ID = "/tpa/onlineqm/profile/get-by-id";
    public static final String TPA_ONLINE_QM_PROFILE_GET_BY_NAME = "/tpa/onlineqm/profile/get-by-name";
    public static final String TPA_ONLINE_QM_PROFILE_GET_BY_METRIC = "/tpa/onlineqm/profile/get-by-metric";
    public static final String TPA_ONLINE_QM_GET_SYNC_TESTS_ASSIGN_TO_PROFILE = "/tpa/onlineqm/get-sync-tests-assigned-profile";
    public static final String TPA_ONLINE_QM_OVERVIEW = "/tpa/onlineqm/overview";

    // NOTE this is a necessary duplication of com.adva.tpacommon.util.TpaRestPath.ENC_UPDATE_ONLINE_QM_JOB_STATE
    // since we cannot import from com.adva.tpacommon.util package
    public static final String TPA_ONLINE_QM_JOB_STATE = "/tpa/onlineqm/jobstate/update";

    //Quality metrics mask management
    public static final String TPA_GET_ALL_QM_MASKS = "/qmmasks";
    public static final String TPA_GET_QM_MASK = "/qmmask";
    public static final String TPA_GET_QM_MASK_BY_NAME = "/qmmaskbyname";
    public static final String TPA_SAVE_QM_MASK = "/qmmasksave";
    public static final String TPA_DELETE_QM_MASK = "/qmmaskdelete";

    //Cli script Management
    public static final String GNSS_CLISCRIPTSAVE = "/cliscriptsave";
    public static final String GNSS_CLISCRIPTS = "/cliscripts";
    public static final String GNSS_CLISCRIPTDELETE = "/cliscriptdelete";
    public static final String GNSS_CLISETUPSUGGESTIONS = "/clisetupsuggestions";

    // Black List
    public static final String GNSS_BLOCK_LIST = "v1/gnss/assurance/neblocklist";
    public static final String GNSS_BLOCK_LIST_DELETE = "v1/gnss/assurance/neblocklist/delete";

    // RCA
    public static final String GNSS_RCA_SETTINGS = "/v1/gnss/rca/settings";
    public static final String GNSS_RCA_DEFAULT_SETTINGS = "/v1/gnss/rca/defaultSettings";
    public static final String GNSS_RCA_ON_DEMAND_ANALYZE = "/v1/gnss/rca/onDemandAnalyze";
  }

  public interface THIRDPARTY {
    public static final String MONITOR_SNAPSHOT = "v1/sbi/thirdparty/monitor/ptpMonitorSnapshot";
  }

  //Event Area
  interface EVENT {
    public static final String PATH = "event";
    public static final String SHUTDOWN = "shutdown";
    public static final String MAIL_SERVER_PROPERTIES = "mailprops";
    public static final String ADD_EVENTS = "add";
    public static final String ADD_SECURITY_EVENT = "secevent";
    public static final String ADD_ALARM_ARCHIVE = "addalarmarchive";
    public static final String GET_ALARM_ARCHIVE = "getalarmarchive";
    public static final String SEVERITY = "severity";
    public static final String PARAMETER = "parameter";
    public static final String SHORT_NAME = "shortname";
    public static final String NTN_NODE_ID = "ntnnodeid";
    public static final String ADD_TRAP = "snmptrap";
    public static final String EVENT_IDS = "ids";
    public static final String ACK_EVENTS = "ackev";
    public static final String ACKN_ALL = "acknall";
    public static final String ACKN = "ackn";
    public static final String USER = "user";
    public static final String EVENTS_FROM_BEFORE = "evbefore";
    public static final String NTN_TYPE = "ntntype";
    public static final String NODE_ID = "nodeID";
    public static final String DATE = "date";
    public static final String MASK = "mask";
    public static final String ALARM_COUNTER = "alarmcounter";
    public static final String SYNC_ALARM_COUNTER = "syncalarmcounter";
    public static final String SG_ALARM_COUNTER = "servgrpalarmcounter";
    public static final String SERVICECFM_ALARM_COUNTER = "servicecfmalarmcounter";
    public static final String FBR_ALARM_COUNTER = "fbralarmcounter";
    public static final String MGD_STATE = "mgdstate";
    public static final String ALL_LAYER_SERVICE_IDS = "alllayerserviceids";
    public static final String NE_PATH = "nepath";
    public static final String COMMENT_RES = "commentfield";
    public static final String COMMENT_ALL_EVENTS_RES = "commentfieldallevents";
    public static final String ID = "id";
    public static final String COMMENT = "comment";
    public static final String ALARM_CLEAR = "clearalarm";
    public static final String NE_TYPE_SUPP_EV_SEV_CHG = "netypesuppevsevchg";
    public static final String EVENT_SEVERITY = "eventseverity";
    public static final String EVENT_SEVERITY_LIST = "eventseveritylist";
    public static final String NE_TYPE = "netype";
    public static final String NE_TYPE_LIST = "netypelist";
    public static final String INIT = "init";
    public static final String EVENT_SEVERITY_ACTION = "evsevaction";
    public static final String EVENT_SEVERITY_DETAILS = "evsevdetails";
    public static final String EVENT_SEVERITY_CLASSES = "evsevclasses";
    public static final String EVENT_HIGHEST_SEVERITY = "evhighsev";
    public static final String ENTITY_INDEX = "enind";
    public static final String MODULES = "modules";
    public static final String SHELVES_WITHOUT_MODULES = "shelveswithoutmodules";
    public static final String ENTITIES = "entities";
    public static final String EVENT_LOG_SIZE_PROPERTY = "evlogsizeprop";
    public static final String FILTERS = "filters";
    public static final String DELETE_FILTERS = "filters/delete";
    public static final String TIMEOUT_NOTIFICATION = "timeout";
    public static final String TIMEOUT_NOTIFICATION_DEFAULT = "timeout/default";
    public static final String TIMEOUT_NOTIFICATION_MINIMUM = "timeout/minimum";
    public static final String DELETE_EVENTS = "delevents";
    public static final String DELETE_ALL_EVENTS = "delAllevents";
    public static final String GET_ALL_SECURITY_EVENTS = "getallsecurityevents";
    public static final String EXCLUDE_SECURITY_EVENTS = "excludeSecurityEvents";
    public static final String LAST_FIVE_SECURITY_EVENTS = "lastfivesecurityevents";
    public static final String PAGE_AREA = "pageArea";
    public static final String ENTITY_DESCRIPTION = "entityDesc";
  }

  interface EVENT_NBI {
    String PATH="event-nbi";
    String NBI_SYNC_ALARMS = "snmp/sync";
  }

  //Event Area
  interface EVENT_LOG {
    public static final String PATH = "event-log";
    public static final String EVENTS = "events";
    public static final String NOTIFICATION_LISTENER = "notificationListener";
  }

  interface SWDL {
    String PATH = "swdl";
    String FILES = "files";
    String IMPORT_PRIORITY = "importPriority";
    String UPGRADES = "upgrades";
    String VALIDATE = "validate";
    String VALIDATE_FMW = "validateFmw";
    String VALIDATE_MULTISELECTION = "validateMultiselection";
    String VALIDATE_FMW_MULTISELECTION = "validateFmwMultiselection";
    String VERSIONS_FOR_NE = "versionsForNe";
    String SECURITY_MODE = "securityMode";
    String UPGRADE_CONFIG = "upgradeConfig";
    String NO_OF_PARALLEL_DLS = "noOfParallelDls";
    interface FMW {
      String UPDATE = "fmw/update";
    }
    interface CRYPTO {
      String DOWNLOAD = "crypto/download";
    }

  }
  interface PROFILE_TRANSFER_SETTINGS {
    public static final String PATH = "profileTransferSettings";
    public static final String VALIDATE = "validate";
    public static final String SECURITY_MODE = "securityMode";
    public static final String UPDATE_CONFIG = "/updateConfig";
    public static final String GET_CONFIG = "/getConfig";

  }

  interface FAM {
    String PATH = "fam";
    String FTP_CONFIG = "ftpConfig";
    String FP_MANUAL = "fp_manual";
    String FA_MANUAL = "fa_manual";
    String FP_ALL_MANUAL = "fp_all_manual";
    String FA_ALL_MANUAL = "fa_all_manual";
    String FAM_EVT_BY_MEAS_POINT_ID = "fam_evt_by_measurement_point_id_and_fam_type";
    String FAM_MEAS_SET_BY_MEAS_POINT_ID = "fam_meas_set_by_measurement_point_id_and_fam_type";
    String FAM_MEAS_LINK_DATA_BY_MEAS_POINT_ID = "fam_meas_link_data_by_measurement_point_id_and_fam_type";
    String FAM_TRACE_DATA_BY_MEAS_POINT_ID = "fam_trace_data_by_measurement_point_id_and_fam_type";
    String FA_TRACE_DATA_BY_MEAS_POINT_ID = "fa_trace_data_by_measurement_point_id_and_fam_type";
    String MEASUREMENT_FILE = "measurement_file";
  }

  interface NTP {
    String PATH = "ntp";
    String FTP_CONFIG = "ftpConfig";
    String SERVER_ACTIVITY = "serverActivity";
    String CLIENT_ACTIVITY = "clientActivity";
  }

  public interface PRIVILEGE_CHANGE_WDM {
    public static final String PATH = "privileges-f7";
    public static final String REQUEST = "request";
    public static final String RESPOND = "respond";
    public static final String SESSION = "sessions";
    public static final String GET_TIMEOUT_VALUE = "timeout";
  }

  public interface ETH_CRYPTO {
    public static final String PATH = "ethcrypto";
    public static final String NE_CRYPTO_BY_ID = "necrypto";
    public static final String NE_CRYPTO_BY_TYPE = "necryptobytype";
    public static final String NE_CRYPTO_PROPERTIES = "necryptoprop";
    public static final String SV_CRYPTO_BY_ID = "svcrypto";
    public static final String SV_ENCRYPTION_BY_ID = "svencryption";
    public static final String NE_LIST_SECURE_FLOW = "nelistsecureflow";
    public static final String CRYPTO_MONITORING = "servicemonitoring";
    public static final String CRYPTO_STATISTICS = "servicestatistics";
    public static final String CRYPTO_STATISTICS_F4 = "servicestatisticsf4";
    public static final String FORCE_KEY_EXCHANGE = "forcekeyexchange";
    public static final String START_KEY_PAIRING = "startkeypairing";
    public static final String ACCEPT_RECEIVED_KEY = "acceptreceivedkey";
    public static final String ASSIGN_SECURE_FLOW = "assign";
    public static final String SECURE_FLOW_TEMPLATE = "secureflowtemplate";
    public static final String KEY_EXCHANGE_TEMPLATE = "keyexchangetemplate";
    public static final String CFM_TEMPLATE = "cfmtemplate";
    public static final String SAVE_CRYPTO_PASSWORD = "savecryptopassword";
    public static final String DELETE_CRYPTO_PASSWORD = "deletecryptopassword";
    public static final String GET_CRYPTO_PASSWORD = "getcryptopassword";
    public static final String APPLY_CHANGES = "applychanges";
    public static final String IS_ACTION_IN_PROGRESS = "isactioninprogress";
    public static final String SECURE_FLOW_LIST = "getsecureflowlist";
    public static final String SECURE_FLOW = "getsecureflow";
    public static final String SECURE_FLOW_F4 = "getsecureflowf4";
    public static final String DELETE_SECURE_FLOW = "deletesecureflow";
    public static final String UPDATE_SECURE_FLOW= "updatesecureflow";
    public static final String ADD_SECURE_FLOW = "addsecureflow";
    String UNASSIGN_SECURE_FLOW = "unassignSecureFlow";
    public static final String KEY_EXCHANGE_CHANGE_PASSWORD = "keyexchangechangepassword";
    public static final String GET_KEY_EXCHANGE_PROFILE = "getkeyexchangeprofile";
    public static final String LIST_KEY_EXCHANGE_PROFILE = "getkeyexchangeprofilelist";
    public static final String ADD_KEY_EXCHANGE_PROFILE = "addkeyexchangeprofile";
    public static final String UPDATE_KEY_EXCHANGE_PROFILE = "updatekeyexchangeprofile";
    public static final String DELETE_KEY_EXCHANGE_PROFILE = "deletedownmepkeyexchangeprofile";
    public static final String LIST_DOWN_MEP = "getdownmeplist";
    interface Create {
      String Path = "create";
      String F3_FLOW = "flowf3";
    }
    interface Delete {
      String Path = "delete";
      String F3_FLOW = "flowf3";
    }
    interface FIPS {
      public static final String PATH = ETH_CRYPTO.PATH + "/fips";
      public static final String ZEROIZE_KEYS = "zeroizekeys";
      public static final String SELF_TEST = "selftest";
      public static final String SELF_TEST_RESULTS = "selftestresults";
    }

  }

  public interface MONITORING {
    public static final String PATH = "monitoring";
    public static final String MONITORING_DATA = "mondata";
    public static final String MONITORING_RESULT = "monresult";
    public static final String RAPID_MONITORING_STARTED = "rapmonstarted";
    public static final String RAPID_MONITORING_START = "rapmonstart";
    public static final String RAPID_MONITORING_STOP = "rapmonstop";
    public static final String DURATION = "duration";
    public static final String CLIENT_ID = "clientid";
    public static final String MAX_ALLOWED_RAPID_DURATION = "maxrapduration";
    public static final String SUPPORTED_COMPRESSED_FILE_EXTENSION = "supportedcfe";
    public static final String TRACE_ROUTE = "traceroute";
    public static final String SERVER_WIDGETS = "server/widgets";
    String HEALTH = "health";
    String FILE = "healthfile";
  }

  public interface SOUND {
    public static final String PATH = "sound";
    public static final String GET_ALL_SOUNDS_FOR_EVENTS = "getAllSoundsForEvents";
    public static final String UPDATE_EVENTS_LIST = "updateEventsList";
  }

  interface POLLING {
    public interface PATH_PARAM {
      String ID = "id";
      String TYPE = "type";
      String STATUS = "status";
    }
    public interface OPERATION {
      String IS_RUNNING = "isrunning";
      String FORCE_MANUAL_FROM_GUI = "forcemanual";
      String FORCE_SPECIAL_MODE_INVENTORY = "special-mode-inventory";
    }
    String PATH = "polling";
    String RECURRING_VISIBLE_POLLING_TYPES = "recvispolling";
    String CONFIGURABLE_BY_NE = "confbyne";
    String INTERVAL = "interval";
    String PROPERTIES = "properties";
    String POLLING_DELAY_STATUS = "pollingdelay";
    String SUMMARY = "summary";
    String RESULT = "result";
  }

  interface TopologyDiscoveryManager {
    String PATH = "topology-discovery-manager";
    enum OPERATION {START, CANCEL, DISCOVERY_RANGE, DISCOVERY_NE, DISCOVERY_RESULT}

    class Request extends DataTransferObject
    {
      private final OPERATION operation;
      private final String firstIPAddress;
      private final String secondIPAddress;
      private final long snmpProfileId;
      private final int discoverySession;

      Request(OPERATION operation,
              String firstIPAddress,
              String secondIPAddress,
              long snmpProfileId,
              int discoverySession) {

        this.operation = operation;
        this.firstIPAddress = firstIPAddress;
        this.secondIPAddress = secondIPAddress;
        this.snmpProfileId = snmpProfileId;
        this.discoverySession = discoverySession;
      }

      public OPERATION getOperation() {
        return operation;
      }

      public String getFirstIPAddress() {
        return firstIPAddress;
      }

      public String getSecondIPAddress() {
        return secondIPAddress;
      }

      public long getSnmpProfileId() {
        return snmpProfileId;
      }

      public int getDiscoverySession() {
        return discoverySession;
      }

      public static Request start(String ipAddress, long snmpProfileID) {
        return new Request(OPERATION.START, ipAddress, "", snmpProfileID, -1);
      }

      public static Request cancel(int sessionID) {
        return new Request(OPERATION.CANCEL, "", "", -1, sessionID);
      }

      public static Request discoverNE(String ipAddress, long snmpProfileID) {
        return new Request(OPERATION.DISCOVERY_NE, ipAddress, "", snmpProfileID, -1);
      }

      public static Request discoverRange(String ipAddress, String secondIPAddress, long snmpProfileID) {
        return new Request(OPERATION.DISCOVERY_RANGE, ipAddress, secondIPAddress, snmpProfileID, -1);
      }

      public static Request discoverResults(int discoverySession) {
        return new Request(OPERATION.DISCOVERY_RESULT, "", "", -1, discoverySession);
      }
    }

    class Response extends DataTransferObject
    {
      int discoverySession;
      TopologyDiscoveryResults discoveryResults;

      public Response(int discoverySession) {
        this.discoverySession = discoverySession;
      }

      public Response(TopologyDiscoveryResults discoveryResults) {
        this.discoveryResults = discoveryResults;
      }

      public int getDiscoverySession() {
        return discoverySession;
      }

      public TopologyDiscoveryResults getDiscoveryResults() {
        return discoveryResults;
      }
    }
  }

  interface TI_MTOSI {
    String PATH = "ti-mtosi";

    String VER = "ver";
    String EXECUTE = "execute";
    String NES = "nes";
    String NE = "ne";
    String PTPS = "ptps";
    String CTPS = "ctps";
    String CP = "cp";
    String SNCS = "sncs";
    String SNC = "snc";
    String SNC_PROT = "snc-prot";
    String LINKS = "links";
    String LINK = "link";
    String TEST1 = "test1";
    String TEST2 = "test2";
  }

  interface SM_TEST {
    String PATH = "sm-test";
    String VERSION = "version";
    String CARD_TYPES = "card-types";
    String MODES = "modes";
    String FACILITIES = "facilities";
    String NES = "nes";
    String MODULES = "modules";
    String PTPS = "ptps";
    String PORT_FACS = "port-facs";
    String SERVICE = "service";
  }

  interface SDN {

    interface INVENTORY {
      String PATH= "sdn_inventory";
      String EVENTS = "events";
    }
  }

  interface UserNotification {
    String PATH = "user_notification";
    String EXPIRE = "expire";
    String ACTIVE = "active";
  }

  interface ServiceTemplate {
    String GET_VALUES = "ServiceTemplate/getValues";
    String GET_TEMPLATE = "ServiceTemplate/getTemplate";
    String GET_CARD_TYPES = "ServiceTemplate/getCardTypes";
    String GET_SERVICE_TYPES = "ServiceTemplate/getServiceTypes";
    String IS_CARD_TYPE_VALID = "ServiceTemplate/isCardTypeValid";
  }

  interface NetworkIntelligence {
    String PATH = "ni";

    interface Controllers {
      String PATH = NetworkIntelligence.PATH + "/controllers";
      String ACTIVE = "active"; // active controller
      String COMMONS = "commons";
      String CREDENTIALS = COMMONS + "/credentials";

      String TEST = "test";
      String TEST_ENABLE = TEST + "/enableNi";
    }

    interface CP {
      String PATH = "ni/cp";
      String ENABLE = "enable";
      String DISABLE = "disable";
    }
  }

  interface NIWebSocket {
    String PATH = "niwebsocket";
    String GET_STATUS = "status";
  }

  interface EndpointServiceEstablishment {
    String PATH =         "endpointserviceestablishment";
    String NE_LIST =      "nelist";
    String NPORT_LIST =   "{neId}/nportlist";
    String CPORT_LIST =   "{neId}/cportlist";
    String AUTOPOPULATE = "{neId}/autopopulate";
    String CREATE =       "{neId}/create/{modifyobj}/{record}";
    String ENABLE =       "{neId}/enable/{modifyobj}/{record}";
    String DISABLE =      "{neId}/disable/{modifyobj}/{record}";
    String DESTROY =      "{neId}/destroy/{modifyobj}/{record}";
    String GETINVENTORY = "{neId}/getinventory/{modifyobj}/{record}";
    String TESTCASE =     "testcase";
    String ALL_TESTCASES ="allTestCases";
    String FACILITY_TYPES ="facilitytypes";
  }

  interface FINGERPRINT {
    public static final String PATH = "fingerprint";


  }

  interface GIS_TRANSFER {
    String PATH = "gis";
    String DUCTS = "ducts";
    String CACHED_DTOS = "cacheddtos";
    String BUILDINGS = "buildings";
    String REGIONS = "regions";
    String FAULT_LOCATIONS = "faultlocations";
    String ACCESS_POINTS = "accesspoints";
    String FIBER_ROUTES = "fiberroutes";
    String FIBER_SUB_ROUTES = "fibersubroutes";
    String AFFECTED_FIBER_ROUTES = "affectedroutes";
    String V_FIBER_ALARMS = "vfiberalarms";
    String FIBER_ALARMS = "fiberalarms";
    String ALL_FIBER_OBJECTS = "allfiberobjects";
    String CLEAR_CACHE = "clearcache";
    String CUSTOMER_RELATED_FIBER_DATA = "customerfiberdata";
    String CUSTOMER_RELATED_FIBER_ROUTES = "customerfiberroutes";
    String DTO_BY_UUID = "dtobyuuid";
    String ROUTE_GROUP_RELATED_FIBER_DATA = "rgrelatedfiberdata";
    String ROUTE_GROUP_RELATED_FIBER_ROUTES = "rgrelatedfiberroutes";
    String FAULTED_ROUTES = "faultedroutes";
    String GEO_SERVER = "geo";
    String GEO_SERVER_ALM = "alm";
    String GEO_SERVER_ALMS = "alms";
    String GEO_SERVER_ROUTE_PATH_ELEMENTS = "georoutepathelements";
    String GEO_SERVER_ALARMS_FROM_ROUTE = "geoalarmsfromroute";
    String GEO_SERVER_ALMS_FROM_BUILDING = "geoalmsfrombuilding";
    String GEO_SERVER_ROUTES_OF_ACCESSPOINT = "geoserverroutesofaccesspoint";
    String GEO_SERVER_TOP_ROUTE_OF_UUID = "geoservertoprouteofuuid";
    String GEO_SERVER_ROUTES_OF_DUCT = "geoserverroutesofduct";
    String GEO_SERVER_SETTINGS = "geosetttings";
    String GEO_SERVER_CONNECTION_TEST = "geoconnectiontest";
    String GEO_SERVER_ROUTE_CUSTOMER = "geosrvroutecstmr";
    String GEO_SERVER_ROUTE_UPDATE_CUSTOMER = "geosrvrouteupdatecstmr";
    String GEO_SERVER_ROUTE_UPDATE_GROUP = "geosrvrouteupdategrp";
    String NETWORK_LINK_FROM_ROUTE_UUID = "networklinkfromlinkuuid";
    String NETWORK_LINK_FROM_MP_UUID = "networklinkfrommpuuid";
    String NE_IDENTIFIER = "neidentifier";
    String GEO_SERVER_MONITOR_POINTS_FROM_ACCESS_POINT = "geomonitorpointsfromap";
    String GEO_SERVER_MONITOR_POINTS_FROM_ROUTE = "geomonitorpointsfromroute";
    String GEO_SERVER_MONITOR_POINTS_FROM_ROUTES = "geomonitorpointsfromroutes";
    String GEO_SERVER_ALARMIDS_FROM_OBJECT = "geoserveralarmidsfromobject";
    String GEO_SERVER_ALARMIDS_FROM_OBJECTS = "geoserveralarmidsfromobjects";
    String GEO_SERVER_EVENTIDS_FROM_OBJECT = "geoservereventidsfromobject";
    String GEO_SERVER_EVENTIDS_FROM_OBJECTS = "geoservereventidsfromobjects";
    String GEO_SERVER_ROUTE_MOVED_CUSTOMER="geosrvroutemovecstmr";
    String GEO_SERVER_ALM_PORT_NAME="geosrvalmportname";
    String GEO_SERVER_IS_AVAILABLE="isavaialable";
  }

  interface MOBILE_APP {
    String PATH = "efd";
    String ALM = "alm";
    String NE = "ne";
    String ACCESS_POINTS = "accessPoints";
    String DUCTS = "ducts";
    String BUILDINGS = "buildings";
    String REGIONS = "regions";
    String FIBER_ROUTES = "fiberRoutes";
    String ALARMS = ALARMS_STR;
    String FIBER_ALARMS = "fiberAlarms";
    String HISTORICAL_FIBER_ALARMS = "historicalFiberAlarms";
    String TRACE_TEMPLATES = "traceTemplates";
    String FAULT_LOCATIONS = "faultLocations";
    String MONITOR_POINTS = "monitorPoints";
  }

  interface WEB_CORE {
    String PATH = "core";
    String PING = "ping";
    String CUSTOMERS = "customers";
    String ALARMS_COUNTERS = "alarmsCounters";
    String EVENTS = "events";
    String ALARMS = ALARMS_STR;
    String TILE_SERVERS = "tileServers";
  }

  interface UNSUPPORTED_NETWORK_ELEMENTS {
    String PATH = "unsupne";
    String ALL_UNSUPPORTED = "allunsup";
  }

  class Profile {
    public class SNMP {
      public static final String PATH = "profiles/snmp";
      public static final String ASSIGN = "assign";
      public static final String ASSIGN_BY_NE_ID = "by-ne-id";
      public static final String PAGE_DATA = "pagedata";
      public static final String BY_NAME = "by-name";
    }

  }

  class Properties {
    private Properties(){}

    public static final String PATH = "properties";
    public static final String KEY_PARAM = "key";
    public static final String VALUE_PARAM = "value";
  }

  interface SERVICE_PROVISIONING {
    String PATH= "service/provisioning";
    String BANDWIDTH_PROFILES = "bandwidth-profiles";
    String NODES = "nodes";
    String ENDPOINTS = "endpoints";
    String SERVICES = "services";
    String MODIFY_SERVICE = "modify-service";
    String PROVISION_SERVICE = "provision-service";
    String PROVISION_SERVICE_VIEW = "provision-service-view";
    String VALIDATE = "validate";
    String VALIDATE_NEREACHABILITY = "validate-nereachability";
    String TERMINATION_POINTS = "termination-points";
    String UPDATE_ROUTE_TAB = "update-route-tab";
    String UPDATE_ROUTE_TAB_WITH_DEFAULT_VALUES = "update-route-tab-with-default-values";
    String UPDATE_TAG_MANAGEMENT_SINGLE_NODE_SERVICE = "update-single-node-service";
    String CLEAN_VIEW_DTO_FOR_SINGLE_NODE_SERVICE = "clean-view-dto-for-single-node-service";
    String INTERMEDIATE_NODE_INTERFACES = "intermediate-node-interfaces";
  }

  interface EVPN {
    String EVPN_SERVICE_SEARCH = "evpn";
    String CREATE = "create";
    String DELETE = "delete-evpn"; // Deletes from the database, but does not delete from device
  }

  interface PDDISCOVERED {
    String PD_DISCOVERED_PATH = "pd-discovered";
    String SERVICE_SEARCH = "search";
    String SERVICE_SAVE = "save-service";
    String SERVICE_DELETE = "delete-service";
  }

  interface PDENTITYSWAP {
    String PD_ENTITY_SWAP_PATH = "pd-entity-swap";
    String INTERFACES_SEARCH = "search-interfaces";
  }

  interface SERVICE_DRAFT {
    String PATH= "service/draft";
    String SAVE = "save";
    String OPEN = "open";
    String CLONE = "clone";
  }

  interface TESTING_SERVICE_PROVISIONING {
    String PATH = "test/service/provisioning";
    String PD_SERVICES = "pdservices";
    String PD_ADMIN_STATE = "pdadminstate";
    String PD_DB_DUMP = "pddbdump";
    String PD_ENTITIES = "pdentities";
    String PD_OVERVIEW_TOP_LEVEL_CONN = "pdoverviewtlc";
  }

  interface SERVICE_MONITORING {
    String PATH = "service-monitoring";
    String CFM = "cfm";
    String SAT = "sat";
    String SAT_ACTIVATION = "sat/activation";
  }

  interface SNMP_DIAG {
    String PATH = "snmp-diagnostics";
    String DUPLICATED_ENGINE_IDS = "duplicated-engine-ids";
    String START_SNMP_TRAFFIC_DUMP = "start-dump";
    String STOP_SNMP_TRAFFIC_DUMP = "stop-dump";
    String CHECK_SNMP_TRAFFIC_DUMP = "check-dump";
  }

  interface CRM {
    String PATH = "crm";
    String START = "start";
  }

  interface CA {
    String PATH = "ca";
    String ROOT_CA_CERTIFICATE = "rootcacert";
    String CSR = "csr";
    String REFRESH_TOKEN = "refreshToken";
  }
}
