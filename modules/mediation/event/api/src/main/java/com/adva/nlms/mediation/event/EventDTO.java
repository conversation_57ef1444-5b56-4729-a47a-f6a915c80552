/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: btracy
 */
package com.adva.nlms.mediation.event;

import com.adva.nlms.common.NO_TIMESTAMP;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.config.ModuleType;
import com.adva.nlms.common.config.NetworkElementDTO;
import com.adva.nlms.common.event.AlarmClass;
import com.adva.nlms.common.event.DbChgCategoryType;
import com.adva.nlms.common.event.Direction;
import com.adva.nlms.common.event.EventDetectionType;
import com.adva.nlms.common.event.EventProperties;
import com.adva.nlms.common.event.EventSeverity;
import com.adva.nlms.common.event.EventStatus;
import com.adva.nlms.common.event.EventType;
import com.adva.nlms.common.event.Location;
import com.adva.nlms.common.event.TrapParameterID;
import com.adva.nlms.common.event.WorkingProtectionFlag;
import com.adva.nlms.common.event.types.CategoryType;
import com.adva.nlms.common.jms.JmsPersisenceHint;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.common.snmp.MIBAdvaFspR7;
import com.adva.nlms.common.snmp.MIBFSP_R7;
import com.adva.nlms.common.util.ArrayHelper;
import com.adva.nlms.mediation.bean.provider.api.BeanProvider;
import com.adva.nlms.mediation.common.event.WebSocketNotification;
import com.adva.nlms.mediation.config.EntityDescription;
import com.adva.nlms.mediation.config.f3.entity.EventRelatedObjectDiscriminator;
import com.adva.nlms.mediation.event.IEventProcessingActionHdlr.Action;
import com.adva.nlms.mediation.event.correlation.CorrelTriggerType;
import com.adva.nlms.mediation.evtProc.VarbindObjectInfo;
import com.adva.nlms.mediation.mtosi.v2.utils.translations.r7.IMtosiAlarmObjectType;
import com.adva.nlms.mediation.sm.dao.IConnectionDAO;
import com.adva.nlms.mediation.smapp.nbi.mtosi.entities.IMtosiPropertyKey;
import com.adva.nlms.mediation.smapp.nbi.mtosi.properties.IMtosiSupportedRates;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.TextStringBuilder;
import org.snmp4j.smi.OID;
import org.snmp4j.smi.VariableBinding;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.Vector;

@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.PROTECTED_AND_PUBLIC,
    getterVisibility = JsonAutoDetect.Visibility.NONE,
    setterVisibility = JsonAutoDetect.Visibility.NONE)
public class EventDTO implements Cloneable, JmsPersisenceHint, INEEvent, EventRelatedObjectDiscriminator, VarbindObjectInfo,  EventObject, Serializable
{
  private IConnectionDAO connectionDAO;

  public static final int noTrapID = 0;
  private static final Vector<VariableBinding> emptyVector = new Vector<>();

  // general properties
  public long id;                                 // Unique id for this Event
  public EventDetectionType detectionType;        // How was the event detected in the NM
  public EventType type;                          // RAISE, CLEARING, CLEAR, TRANSIENT, SECURITY, SYSTEM, TRASHED
  public long nmsTimeStamp;                       // Date and time of reception by NMS
  public long neTimeStamp = NO_TIMESTAMP.value;   // Date and time of occurrence in NE


  // NE properties (trap)
  public int neLogIndex;                          // Index into NE resident event log table
  public String sourceNE_IP;                      // IP address of the concerned network element (west network element, if line Event)
  public int sourceNE_ID;                         // ID of the concerned network element
  public int sourceNEType;                        // NetworkElementType (FSPxxx). @see config::NetworkElement::NetworkElementType
  public int subnetID;                            // Subnet, the source ne is contained in

  // Trap properties
  public String snmpTrapOid;                      // original snmp trap oid
  public String enterprise;                       //
  private int trapID;                              // Trap number defined in the MIB or self defined (server alarms)
  public int alarmType;                           // Alarm type value, as defined in the mib
  public String shortName;                        // Short name
  public boolean securityEvent;                   // true if it's a security event (e.g. user activity)
  public EventSeverity severity;                  // Perceived severity
  public AlarmClass alarmClass = AlarmClass.UNKNOWN;  // Alarm class
  public boolean impairment;                      // true if the alarm is service-affecting
  public int direction;                           // direction of the alarm (Direction: RX, ...)
  public int location;                            // location of the alarm (Location: far-end, ...)

  // line properties
  public int lineID;                              // Affected line, if it is a line alarm (e.g. fiber break).

  // derived properties
  public String sourceName = "";                  // Source name (NE, Line, Service, ...)
  public String serviceName = "";                 // Service name (if service related)
  public String customerName;                     // Customer name (if service or customer related)

  // entity properties
  public EntityIndex objectIndex = EntityIndex.ZERO;  // The (Entity-) Index from the MIB
  public int[] objectIndexes;                     // object-indexes (needed if object-index is not unique)
  public CategoryType category;                   // Event category, e.g. 'interface-alarms', 'prot-alarms' defined in Alarm/EventTypeHandler
  public DbChgCategoryType dbChgCategory;         // DB change category (create, delete, update)
  private TrapParameterID parameterID = TrapParameterID.NONE;                         // ID (TrapParameterID) of the Varbind (e.g. Admin-state)
  public long newValue;                           // Value (numeric) of the Varbind
  public String newStringValue = "";              // Value (string) of the Varbind
  public EventMultiVarDTO[] multiVarList;         // Descriptors for further Varbinds
  public boolean textIncomplete;                  // true if the related entioty is not in the DB
  public String entityDescription = "";           // entity short description (AID)
  public String entityAlias = "";                 // entity alias
  public String entityFullDescription = "";       // entity full description (used as a tooltip)
  public EntityIndex moduleIndex = EntityIndex.ZERO;   // Index of the affected module or 0 (not a module Event).
  public ModuleType moduleType;                        // Module type
  public String moduleTypeName = "";                   // Module type name
  public EntityIndex portIndex = EntityIndex.ZERO;     // Port index
  public String phyLocation = "";                 // physical Location (Shelf location or NE location)
  public int instance = -1;                       // Logical instance No (e.g. for TIM).
  public boolean arcSupport;                      // true if entity supports ARC
  private StringBuilder description = null;       // Description of the event
  public String descriptionForJson;

  // alarm administration
  public boolean acknowledge;                     // true if acknowledged by NM user
  public long acknTimeStamp;                      // Date/Time of acknowledgement
  public String acknUser;                         // User name
  private boolean ackExternal;                    // some events can be acknowledged by external system
  private boolean transientAlarm;
  // alarm properties
  public String comment;                          // User comment for alarms
  public WorkingProtectionFlag path;              // related service path (working/protection path)
  public long firstRaisedAlarm;                   // ID of the first raised alarm. It's set for cleared, clearing and reraised alarms.
  public long clearingAlarm;                      // ID of the clearing alarm (cleared alarms only)
  public long neClearedTimeStamp;                 // Date and time when alarm was cleared (NE)
  public long nmsClearedTimeStamp;                // Date and time when alarm was cleared (NMS)

  // alarm correlation
  public long rootCause;                          // ID of the root cause for this event (if Redundant)
  public int correlation = EventStatus.Correlation.PRIMARY.getType();   // it's a Primary or Redundant event

  private List<EventAssocObjectId> associatedObjects = new ArrayList<>();

  // service properties
  public int[] connectionIDs = new int[0];        // serviceIDs
  public int[] parentsIDs = new int[0];           // serviceParentIDs

  // sync properties
  public int[] syncNodeIds = new int[0];
  public int[] syncNCDIds = new int[0];
  public int syncRouteID;

  // miscellaneous
  private int mask;
  public String debugLog;

  // pv transition properties
  public String pventityid;
  public String pvsourceid;
  public String pvsourcetype;

  // trap processing specific types (used during pre- and post-processing only)
  private ProcessingData processingData;

  private static class ProcessingData implements Serializable{
    private int seqNbr;                                 // unique sequence number (valid during event processing)
    private List<VariableBinding> vbl;                // vbl data
    private int origTrapId;                             // original Trap-ID
    private boolean isSpecificTrap;                     // true if specific-Trap
    private boolean hasAlarmDefinition;                 // true if TrapParameters.eventType is set to TRANSIENT
    private String srcIPAddressAlternative;             // alternative IP address (SNMP V1 only)
    private String aidString = "";                      // trapAid
    private int localNeId;                              // local NE (Trap source / event log)
    private boolean lineEndLoc;                         // true if the line event is related to Peer-NE (far-end location)
    private Set<CorrelTriggerType> correlTriggerTypes;
    private Action actionType;
    private Object actionObject;
    private MtosiParams mtosiParams;
    private BulkTrapParams bulkTrapParams;
    private transient WebSocketNotification webSocketNotification; // tagged as transient as it will not be serialized using JMS
    private NotificationProtocolType protocolType =  // Communication protocol
        NotificationProtocolType.SNMP;
    private String mtpDisplayName = "";                // trapMtpDisplayName
  }

  public enum MtosiNotificationType {ALARM, HEART_BEAT, OBJECT_CREATION, OBJECT_DELETION, VALUE_CHANGE, STATE_CHANGE}

  public static class MtosiParams implements Serializable{
    public MtosiNotificationType notificationType ;
    public String objectName;
    public String parentTpName;
    public IMtosiAlarmObjectType parentObjectType;
    public IMtosiAlarmObjectType objectType;
    public Set<IMtosiSupportedRates> layerRate;
    public Map<IMtosiPropertyKey, Object> properties;

    public MtosiParams(MtosiNotificationType notificationType, String objectName, IMtosiAlarmObjectType objectType, Set<IMtosiSupportedRates> layerRate) {
      this.notificationType = notificationType;
      this.objectName = objectName;
      this.objectType = objectType;
      this.layerRate = layerRate;
      this.properties = new HashMap<>();
    }
  }

  public static class BulkTrapParams implements Serializable{
    public Set<EventDTO> subTraps;
    public int bulkTrapNeStartLogIndex;
    public int bulkTrapNeEndLogIndex;

    public BulkTrapParams(Set<EventDTO> subTraps, int bulkTrapNeStartLogIndex, int bulkTrapNeEndLogIndex) {
      this.subTraps = subTraps;
      this.bulkTrapNeStartLogIndex = bulkTrapNeStartLogIndex;
      this.bulkTrapNeEndLogIndex = bulkTrapNeEndLogIndex;
    }
  }



  // transient data (used during post-processing, forwarding, correlation)
  private TransientData transientData;
  private static class TransientData implements Serializable{
    private Entity entity;                // entity (from Entity.Description)
    private boolean isPeer;
    private boolean overrideTrapNbiAlarmClass;  // override alarm-class on Trap-NBI
    private String mtosiType;
    private int removedConnectionID;      // special field to fix FNM21176
    private String lineName;
  }

  public static class Entity implements Serializable{
    private int entityID;                 // entity ID (from objectIndex)
    private int fspEntityClass;           // fspEntityClass of the entity
    private boolean isEndpoint;           // true if the entity (object-index) is an edge-port of a service
    private boolean isDataService;        // true if the entity (object-index) is an edge-port of a data service
    private boolean isCTrailOnCCCP;       // true if the entity (object-index) is an edge-port of a CTrail (CCCP) service
    private boolean isOperStateAffecting; // true if the entity (object-index) is affects the oper state-port of a service
    private boolean isCarryingActiveTraffic;  // true if the entity (object-index) carries the active traffic of a service

    public Entity(EntityDescription entityDescription) {
      this.entityID = entityDescription.id;
      this.fspEntityClass = entityDescription.fspEntityClass;
      this.isEndpoint = entityDescription.isEndpoint;
      this.isDataService = entityDescription.isDataService;
      this.isCTrailOnCCCP = entityDescription.isCTrailOnCCCP;
      this.isOperStateAffecting = entityDescription.isOperStateAffecting;
      this.isCarryingActiveTraffic = entityDescription.isCarryingActiveTraffic;
    }
  }

  public enum Layer {
    all, OCH, OTU, ODU, SDH_SONET, VC_STS, PHY, PROTECTING;

    public static Layer getLayer(String layerString) {
      for (Layer layer : Layer.values()) {
        if (layerString != null && layerString.equals(layer.name()))
          return layer;
      }
      return null;
    }
  }

  private ProcessingData createProcessingData() {
    if (processingData == null)
      processingData = new ProcessingData();
    return processingData;
  }

  private TransientData createTransientData() {
    if (transientData == null)
      transientData = new TransientData();
    return transientData;
  }

  public void setSeqNbr(int seqNbr) { createProcessingData().seqNbr = seqNbr; }
  public int getSeqNbr() { return (processingData != null) ? processingData.seqNbr : 0; }

  public void setVbl(List<VariableBinding> vbl) { createProcessingData().vbl = vbl; }
  public List<VariableBinding> getVbl() { return (processingData != null && processingData.vbl != null) ? processingData.vbl : emptyVector; }

  public void setOrigTrapId(int trapId) { createProcessingData().origTrapId = trapId; }
  public int getOrigTrapId() { return (processingData != null) ? processingData.origTrapId : 0; }

  public void setIsSpecificTrap(boolean isSpecificTrap) { createProcessingData().isSpecificTrap = isSpecificTrap; }
  public boolean isSpecificTrap() { return processingData != null && processingData.isSpecificTrap /*(processingData != null) ? processingData.isSpecificTrap : false*/; }

  public void setHasAlarmDefinition(boolean hasAlarmDefinition) { createProcessingData().hasAlarmDefinition = hasAlarmDefinition; }
  public boolean hasAlarmDefinition() { return processingData != null && processingData.hasAlarmDefinition /*(processingData != null) ? processingData.hasAlarmDefinition : false*/; }

  public void setSrcIPAddressAlternative(String ipAddress) { createProcessingData().srcIPAddressAlternative = ipAddress; }
  public String getSrcIPAddressAlternative() { return (processingData != null) ? processingData.srcIPAddressAlternative : null; }

  public void setAidString(String aidString) {
    createProcessingData().aidString = aidString;
  }
  public String getAidString() { return (processingData != null) ? processingData.aidString : null; }

  public void setMtpDisplayName(String mtpDisplayName) {
    createProcessingData().mtpDisplayName = mtpDisplayName;
  }
  public String getMtpDisplayName() { return (processingData != null) ? processingData.mtpDisplayName : null; }
  public void setLocalNeId(int localNeId) { createProcessingData().localNeId = localNeId; }
  public int getLocalNeId() { return (processingData != null) ? processingData.localNeId : 0;}

  public void setLineEndLoc(boolean lineEndLoc) { if (lineEndLoc) createProcessingData().lineEndLoc = lineEndLoc; }
  public boolean isLineEndLoc() { return processingData != null && processingData.lineEndLoc /*(processingData != null) ? processingData.lineEndLoc : false*/; }

  public void addCorrelTriggerEvent(CorrelTriggerType correlEvent) {
    if (createProcessingData().correlTriggerTypes == null) {
      processingData.correlTriggerTypes = new HashSet<>();
    }
    processingData.correlTriggerTypes.add(correlEvent);
  }

  public Set<CorrelTriggerType> getCorrelTriggerTypes() {
    return (processingData != null) ? processingData.correlTriggerTypes : null;
  }

  public boolean containsTriggerEvent(CorrelTriggerType triggerEvent) {
    return getCorrelTriggerTypes() != null && getCorrelTriggerTypes().contains(triggerEvent);
  }

  public boolean isCorrelTrigger() {
    return (processingData != null) && (processingData.correlTriggerTypes != null) && !processingData.correlTriggerTypes.isEmpty();
  }

  public Set<CorrelTriggerType> clearAndReturnCorrelTriggerEvents() {
    if (getCorrelTriggerTypes() == null) {
      return Collections.emptySet();
    }
    Set<CorrelTriggerType> triggerEvents = new HashSet<>(processingData.correlTriggerTypes);
    processingData.correlTriggerTypes.clear();
    return triggerEvents;
  }


  public void setServerActionType(Action action) { createProcessingData().actionType = action; }
  public Action getServerActionType() { return (processingData != null) ? processingData.actionType : null;}
  public boolean isServerActionEvent() { return (processingData != null && processingData.actionType != null);}

  public void setServerActionObject(Object object) { createProcessingData().actionObject = object; }
  public Object getServerActionObject() { return (processingData != null) ? processingData.actionObject : null;}

  public void setMtosiParams(MtosiParams mtosiParams) { createProcessingData().mtosiParams = mtosiParams; }
  public MtosiParams getMtosiParams() { return (processingData != null) ? processingData.mtosiParams : null;}

  public void setBulkTrapParams(BulkTrapParams bulkTrapParams) { createProcessingData().bulkTrapParams = bulkTrapParams; }
  public BulkTrapParams getBulkTrapParams() { return (processingData != null) ? processingData.bulkTrapParams : null;}


  public void setWebSocketNotification(WebSocketNotification webSocketNotification) { createProcessingData().webSocketNotification = webSocketNotification; }
  public <T extends WebSocketNotification> T getWebSocketNotification() { return (processingData != null) ? (T) processingData.webSocketNotification : null;}

  public void setProtocolType(NotificationProtocolType protocolType) { createProcessingData().protocolType = protocolType; }
  public <T extends NotificationProtocolType> T getProtocolType() { return (processingData != null) ? (T) processingData.protocolType : null;}

  public void setEntity(Entity entity) { createTransientData().entity = entity; }
  public Entity getEntity() { return (transientData != null) ? transientData.entity : null; }
  public int getEntityID() { return (transientData != null && transientData.entity != null) ? transientData.entity.entityID : 0; }
  public int getFspEntityClass() { return (transientData != null && transientData.entity != null) ? transientData.entity.fspEntityClass : 0; }
  public boolean isEndpoint() { return (transientData != null && transientData.entity != null && transientData.entity.isEndpoint); }
  public boolean isDataService() { return (transientData != null && transientData.entity != null && transientData.entity.isDataService); }
  public boolean isCTrailOnCCCP() { return (transientData != null && transientData.entity != null && transientData.entity.isCTrailOnCCCP); }
  public boolean isOperStateAffecting() { return (transientData != null && transientData.entity != null && transientData.entity.isOperStateAffecting); }
  public boolean isCarryingActiveTraffic() { return (transientData != null && transientData.entity != null && transientData.entity.isCarryingActiveTraffic); }

  public void setIsPeer(boolean isPeer) { createTransientData().isPeer = isPeer; }
  public boolean isPeer() { return (transientData != null && transientData.isPeer); }

  public void setOverrideTrapNbiAlarmClass(boolean overrideTrapNbiAlarmClass) { createTransientData().overrideTrapNbiAlarmClass = overrideTrapNbiAlarmClass; }
  public boolean isOverrideTrapNbiAlarmClass() { return (transientData != null && transientData.overrideTrapNbiAlarmClass); }

  public void setMtosiType(String mtosiType) { createTransientData().mtosiType = mtosiType; }
  public String getMtosiType() { return (transientData != null) ? transientData.mtosiType : ""; }

  public void setLineName(String lineName) { createTransientData().lineName = lineName; }
  public String getLineName() { return (transientData != null) ? transientData.lineName : null; }

  public void setRemovedConnectionID(int connID) { createTransientData().removedConnectionID = connID; }
  public int getRemovedConnectionID() { return (transientData != null) ? transientData.removedConnectionID : 0; }

  public StringBuilder getDescription() {
    return description;
  }

  // static Maps to accelerate enum-type conversions
  private static Map<Integer, CategoryType> categoryTypes= new HashMap<>();
  static {
    for (CategoryType type : CategoryType.values())
      categoryTypes.put(type.getType(), type);
  }

  //==============================================================================
  //=== Constructors =============================================================
  //==============================================================================

  public EventDTO()
  {
  }

  public EventDTO(int trapID,
                  int alarmType,
                  EventSeverity severity,
                  long nmsTimeStamp,
                  String sourceIPAddress,
                  int sourceNE_ID,
                  EntityIndex objectIndex,
                  TrapParameterID paramID,
                  long intParamValue,
                  String stringParamValue,
                  EventDetectionType eventDetectionType,
                  CategoryType category){
    this.trapID = trapID;
    this.alarmType = alarmType;
    this.severity = severity;
    this.nmsTimeStamp = nmsTimeStamp;
    this.sourceNE_IP = sourceIPAddress;
    this.sourceNE_ID = sourceNE_ID;
    this.objectIndex = (objectIndex != null) ? objectIndex : EntityIndex.ZERO;
    this.parameterID = paramID;
    this.newValue = intParamValue;
    this.newStringValue = stringParamValue;
    this.detectionType = eventDetectionType;
    this.category = category;
  }


  public EventDTO(EventDTO eventDTO){
    this(eventDTO.id,
         eventDTO.neLogIndex,
         eventDTO.trapID,
         eventDTO.alarmType,
         eventDTO.category.getType(),
         eventDTO.type,
         eventDTO.securityEvent,
         eventDTO.severity,
         eventDTO.nmsTimeStamp,
         eventDTO.neTimeStamp,
         eventDTO.nmsClearedTimeStamp,
         eventDTO.neClearedTimeStamp,
         eventDTO.clearingAlarm,
         eventDTO.firstRaisedAlarm,
         eventDTO.description,
         eventDTO.shortName,
         eventDTO.textIncomplete,
         eventDTO.acknowledge,
         eventDTO.acknTimeStamp,
         eventDTO.acknUser,
         eventDTO.impairment,
         eventDTO.detectionType,
         eventDTO.correlation,
         eventDTO.rootCause,
         eventDTO.mask,
         eventDTO.sourceNE_IP,
         eventDTO.sourceNE_ID,
         eventDTO.sourceNEType,
         eventDTO.subnetID,
         eventDTO.lineID,
         eventDTO.connectionIDs,
         eventDTO.parentsIDs,
         eventDTO.entityDescription,
         eventDTO.entityFullDescription,
         eventDTO.moduleIndex,
         eventDTO.moduleType,
         eventDTO.moduleTypeName,
         eventDTO.portIndex,
         eventDTO.objectIndex,
         eventDTO.parameterID,
         eventDTO.newValue,
         eventDTO.newStringValue,
         eventDTO.location,
         eventDTO.direction,
         eventDTO.enterprise,
         eventDTO.sourceName,
         eventDTO.entityAlias,
         eventDTO.objectIndexes,
         eventDTO.multiVarList,
         "",
         false,
         WorkingProtectionFlag.NA,
         false,
         "",
         false,
         eventDTO.getSyncNodeIds(),
         eventDTO.getSyncNCDIds(),
         eventDTO.syncRouteID,
         eventDTO.pventityid,
         eventDTO.pvsourceid,
         eventDTO.pvsourcetype
    );

    this.processingData = eventDTO.processingData;
    this.transientData = eventDTO.transientData;
  }

  public EventDTO(EventDetectionType detectionType,
                  String srcIPAddress,
                  NetworkElementDTO sourceNE,
                  long nmsTimeStamp,
                  long neTimeStamp,
                  int neLogIndex,
                  int trapID,
                  EventSeverity neAlarmSeverity,
                  EntityIndex objectIndex,
                  TrapParameterID trapParamID,
                  long newValue,
                  String stringNewValue,
                  int alarmType,
                  int location,
                  int direction,
                  final CategoryType category,
                  final String enterprise) {

    this.detectionType = detectionType;
    this.trapID = trapID;
    this.objectIndex = (objectIndex != null) ? objectIndex : EntityIndex.ZERO;
    this.parameterID = trapParamID;
    this.newValue = newValue;
    this.newStringValue = stringNewValue;
    this.alarmType = alarmType;
    this.nmsTimeStamp = nmsTimeStamp;
    this.neTimeStamp = neTimeStamp;
    this.neLogIndex = neLogIndex;
    this.severity = neAlarmSeverity;
    this.sourceNE_IP = srcIPAddress;
    setNetworkElementProperties(sourceNE);
    this.setIsSpecificTrap(true);
    this.id = 0;
    this.location = location;
    this.direction = direction;
    this.category = category;
    this.enterprise = enterprise;
  }

  public EventDTO(long id,
                  int neLogIndex,
                  int trapID,
                  int alarmType,
                  int category,
                  EventType type,
                  boolean securityEvent,
                  EventSeverity severity,
                  long nmsTimeStamp,
                  long neTimeStamp,
                  long nmsClearedTimeStamp,
                  long neClearedTimeStamp,
                  long clearingAlarm,
                  long firstRaisedAlarm,
                  StringBuilder description,
                  String shortName,
                  boolean textIncomplete,
                  boolean acknowledge,
                  long acknTimeStamp,
                  String acknUser,
                  boolean impairment,
                  EventDetectionType detection,
                  int correlation,
                  long rootCause,
                  int mask,
                  String sourceNE_IP,
                  int sourceNE_ID,
                  int sourceNEType,
                  int subnetID,
                  int lineID,
                  int[] connectionIDs,
                  int[] parentsIDs,
                  String entityDescription,
                  String entityFullDescription,
                  EntityIndex moduleIndex,
                  ModuleType moduleType,
                  String moduleTypeName,
                  EntityIndex portIndex,
                  EntityIndex objectIndex,
                  TrapParameterID parameterID,
                  long newValue,
                  String newStringValue,
                  int location,
                  int direction,
                  String enterprise,
                  String sourceName,
                  String entityAlias,
                  int[] objectIndexes,
                  EventMultiVarDTO[] multiVarList,
                  String mtosiType,
                  boolean isPeer,
                  WorkingProtectionFlag path,
                  boolean isFaultedServiceAlarm,
                  String customerName,
                  boolean arcSupport,
                  int[] syncNodeIDs,
                  int[] syncNcdIDs,
                  int syncRouteID,
                  String pventityid,
                  String pvsourceid,
                  String pvsourcetype)
  {
    this.id = id;
    this.neLogIndex = neLogIndex;
    this.trapID = trapID;
    this.alarmType = alarmType;
    this.category = categoryTypes.get(category);
    this.type = type;
    this.securityEvent = securityEvent;
    this.severity = severity;
    this.nmsTimeStamp = nmsTimeStamp;
    this.neTimeStamp = neTimeStamp;
    this.nmsClearedTimeStamp = nmsClearedTimeStamp;
    this.neClearedTimeStamp = neClearedTimeStamp;
    this.clearingAlarm = clearingAlarm;
    this.firstRaisedAlarm = firstRaisedAlarm;
    this.description = new StringBuilder(description);
    this.shortName = shortName;
    this.textIncomplete = textIncomplete;
    this.acknowledge = acknowledge;
    this.acknTimeStamp = acknTimeStamp;
    this.acknUser = acknUser;
    this.impairment = impairment;
    this.detectionType = detection;
    this.correlation = correlation;
    this.rootCause = rootCause;
    this.mask = mask;
    this.sourceNE_IP = sourceNE_IP;
    this.sourceNE_ID = sourceNE_ID;
    this.sourceNEType = sourceNEType;
    this.subnetID = subnetID;
    this.lineID = lineID;
    this.connectionIDs = connectionIDs;
    this.parentsIDs = parentsIDs;
    this.entityDescription = entityDescription;
    this.entityFullDescription = entityFullDescription;
    this.moduleIndex = (moduleIndex != null) ? moduleIndex : EntityIndex.ZERO;
    this.moduleType = moduleType;
    this.moduleTypeName = moduleTypeName;
    this.portIndex = (portIndex != null) ? portIndex : EntityIndex.ZERO ;
    this.objectIndex = (objectIndex != null) ? objectIndex : EntityIndex.ZERO;
    this.parameterID = parameterID;
    this.newValue = newValue;
    this.newStringValue = newStringValue;
    this.location = location;
    this.direction = direction;
    this.enterprise = enterprise;
    this.sourceName = sourceName;
    this.entityAlias = entityAlias;
    this.objectIndexes = objectIndexes;
    this.multiVarList = multiVarList;
    this.path = path;
    this.customerName = customerName;
    this.arcSupport = arcSupport;
    this.syncNodeIds = syncNodeIDs;
    this.syncNCDIds = syncNcdIDs;
    this.syncRouteID = syncRouteID;
    this.pventityid = pventityid;
    this.pvsourceid = pvsourceid;
    this.pvsourcetype = pvsourcetype;
  }

  public EventDTO(EventProperties eventProps)
  {
    this.id = eventProps.id;
    this.neLogIndex = eventProps.neLogIndex;
    this.trapID = eventProps.trapID;
    this.alarmType = eventProps.alarmType;
    this.type = eventProps.type;
    this.securityEvent = eventProps.securityEvent;
    this.severity = eventProps.severity;
    this.nmsTimeStamp = eventProps.nmsTimeStamp;
    this.neTimeStamp = eventProps.neTimeStamp;
    this.nmsClearedTimeStamp = eventProps.nmsClearedTimeStamp;
    //this.neClearedTimeStamp = eventProps.neClearedTimeStamp;
    this.clearingAlarm = eventProps.clearingAlarm;
    this.firstRaisedAlarm = eventProps.firstRaisedAlarm;
    this.description = new StringBuilder(eventProps.description);
    this.shortName = eventProps.shortName;
    this.acknowledge = eventProps.acknowledge;
    this.acknTimeStamp = eventProps.acknTimeStamp;
    this.acknUser = eventProps.acknUser;
    this.ackExternal = eventProps.ackExternal;
    this.impairment = eventProps.impairment.getDbValue();
    this.detectionType = eventProps.detectionType;
    this.correlation = eventProps.correlation.getType();
    this.rootCause = eventProps.rootCause;
    //this.mask = eventProps.mask;
    this.sourceNE_IP = eventProps.sourceNE_IP;
    this.sourceNE_ID = eventProps.sourceNE_ID;
    this.sourceNEType = eventProps.sourceNEType;
    this.subnetID = eventProps.subnetID;
    this.lineID = eventProps.lineID;
    this.connectionIDs = eventProps.serviceIDs;
    this.parentsIDs = eventProps.parentsIDs;
    this.setRemovedConnectionID(eventProps.removedServiceId);
    this.entityDescription = eventProps.entityDescription;
    this.entityFullDescription = eventProps.entityFullDescription;
    this.moduleIndex = new EntityIndex(eventProps.moduleIndex);
    this.objectIndex = new EntityIndex(eventProps.objectIndex);
    this.moduleTypeName = eventProps.moduleTypeName;
    this.phyLocation = eventProps.phyLocation;
    this.category = CategoryType.GENERIC_EVENT;
    this.location = eventProps.location.getMIBValue();
    this.direction = eventProps.direction.getMIBValue();
    this.sourceName = eventProps.sourceName;
    this.entityAlias = eventProps.entityAlias;
    this.path = eventProps.path;
    this.customerName = eventProps.customerName;
    this.enterprise = eventProps.enterprise;
    //fields not contained in EventProperties should be initialized too
    this.category = CategoryType.INTERFACE_EVENT;
    this.textIncomplete = false;
    this.moduleType = ModuleType.NO_MODULE;
    this.portIndex = EntityIndex.ZERO;
//    this.parameterID =TrapParameterID.NONE;
    this.newValue = 0;
    this.newStringValue = "";
    this.comment = eventProps.comment;
    this.arcSupport = eventProps.arcSupport;
    this.syncNCDIds = eventProps.syncNCDIds;
    this.syncNodeIds = eventProps.syncNodeIds;
    this.syncRouteID = eventProps.syncRouteID;
  }

    public EventProperties createEventProperties(boolean isEventUpdate) {
    return createEventProperties(isEventUpdate, false);
  }

  EventProperties createEventProperties(boolean isEventUpdate, boolean isDummyEvent)
  {
    EventProperties eventProperties = new EventProperties();

    eventProperties.id                 = id;
    eventProperties.neLogIndex         = neLogIndex;
    eventProperties.trapID             = trapID;
    eventProperties.alarmType          = alarmType;
    eventProperties.type               = type;
    eventProperties.typeTooltip        = isDummyEvent ? new String[] {""} : AbstractEventHelper.setupTooltip(shortName, type, sourceNEType, trapID, enterprise);
    eventProperties.securityEvent      = securityEvent;
    eventProperties.severity           = severity;
    eventProperties.alarmClass         = alarmClass;
    eventProperties.nmsTimeStamp       = nmsTimeStamp;
    eventProperties.nmsClearedTimeStamp = nmsClearedTimeStamp;
    eventProperties.neTimeStamp        = neTimeStamp;
    eventProperties.clearingAlarm      = clearingAlarm;
    eventProperties.firstRaisedAlarm   = firstRaisedAlarm;
    eventProperties.description        = description.toString();
    eventProperties.shortName          = shortName;
    eventProperties.acknowledge        = acknowledge;
    eventProperties.acknTimeStamp      = acknTimeStamp;
    eventProperties.acknUser           = acknUser;
    eventProperties.ackExternal        = ackExternal;
    eventProperties.impairment         = EventStatus.Impairment.valueOf(impairment);
    eventProperties.detectionType      = detectionType;
    eventProperties.correlation        = EventStatus.Correlation.valueOf(correlation);
    eventProperties.rootCause          = rootCause;
    //eventProperties.mask               = mask;
    eventProperties.sourceNE_IP        = sourceNE_IP;
    eventProperties.sourceNE_ID        = sourceNE_ID;
    eventProperties.sourceNEType       = sourceNEType;
    eventProperties.subnetID           = subnetID;
    eventProperties.lineID             = lineID;
    eventProperties.serviceIDs         = connectionIDs;
    eventProperties.parentsIDs         = parentsIDs;
    for (EventAssocObjectId assocObject : getMlTopologyObjects()) {
      switch (assocObject.getObjectTypeEnum()) {
        case MLT_SERVICE:
        case RING:
          eventProperties.serviceIDs = ArrayUtils.add(eventProperties.serviceIDs, assocObject.getObjectId());
          break;
        case MLT_SERVICE_FOLDER:
        case EOD_CONNECTIVITY_SERVICE:
        case EOD_CONNECTIVITY_SERVICE_FOLDER:
        case RING_FOLDER:
          eventProperties.parentsIDs = ArrayUtils.add(eventProperties.parentsIDs, assocObject.getObjectId());
          break;
        default:
          break;
      }
    }
    eventProperties.removedServiceId   = getRemovedConnectionID();
    eventProperties.serviceManagedStates = new boolean[(connectionIDs != null ? connectionIDs.length : 0)];
    if (isEventUpdate && connectionIDs != null && connectionIDs.length > 0) {
      if (connectionDAO == null) {
        connectionDAO = BeanProvider.get().getBean(IConnectionDAO.class);
      }
      Map<Integer, Boolean> serviceMgmStateMap = connectionDAO.getServiceManagamentStates(connectionIDs);
      for (int idx=0; idx < connectionIDs.length; idx++) {
        eventProperties.serviceManagedStates[idx] = Boolean.TRUE.equals(serviceMgmStateMap.get(connectionIDs[idx]));
      }
    }
    eventProperties.entityDescription  = entityDescription;
    eventProperties.entityFullDescription= entityFullDescription;
    eventProperties.moduleIndex        = moduleIndex;
    eventProperties.objectIndex        = objectIndex;
    eventProperties.moduleTypeName     = moduleTypeName;
    eventProperties.phyLocation        = phyLocation;
    eventProperties.category           = category;
    eventProperties.location           = Location.valueOf(location);
    eventProperties.direction          = Direction.valueOf(direction);
    eventProperties.sourceName         = sourceName;
    eventProperties.entityAlias        = (entityAlias != null ? entityAlias : "");
    eventProperties.path               = path;
    eventProperties.serviceName        = serviceName;
    eventProperties.customerName       = customerName;
    eventProperties.comment            = (comment != null ? comment : "");
    eventProperties.enterprise         = enterprise;
    eventProperties.arcSupport         = arcSupport;
    eventProperties.syncNCDIds         = syncNCDIds;
    eventProperties.syncNodeIds        = syncNodeIds;
    eventProperties.syncRouteID        = syncRouteID;

    return eventProperties;
  }

  /**
   * Creates a properties string for logging purposes.
   */
  public String toString() {
    TextStringBuilder sb = new TextStringBuilder(300);
    sb.append((id > 0) ? "id=" + id : (processingData != null) ? "sn=" + processingData.seqNbr : "");
    sb.append(", DT=").append((detectionType != null) ? detectionType.getType() : "");
    sb.append(", typ=").append((type != null) ? type.getType() : "");
    sb.append(", logIdx=").append(neLogIndex);
    sb.append(", srcNE=").append(StringUtils.isNotEmpty(sourceNE_IP) ? sourceNE_IP : sourceNE_ID).append('/').append(sourceNEType);
    sb.append(", trap=").append(trapID).append("/").append(shortName);
    sb.append(", sev=").append((severity != null) ? severity.getShortName() : "");
    sb.append(", cat=").append((category != null) ? category.getType() : "");
    sb.append(", objIdx=").append(objectIndex)
      .append((objectIndexes != null && objectIndexes.length>0) ? "/" + ArrayHelper.toString(objectIndexes) : "");
    if (!isAlarm())
      sb.append(", param=").append(appendParam(sb));
    sb.append(", aid=").append((entityDescription != null) ? entityDescription : "").append("/").append(getEntityID());
    if (!isAlarm())
      sb.append(", enterprise=").append((StringUtils.isNotEmpty(enterprise) ? enterprise : ""));
    sb.append(", snmpTrapOid=").append((StringUtils.isNotEmpty(snmpTrapOid) ? snmpTrapOid : ""));
    sb.append(", Date=").append(new Date(getTimeStamp())).append(" / ").append(getTimeStamp());
    if (lineID > 0)
      sb.append(", line=").append(lineID);
    if (connectionIDs != null && connectionIDs.length > 0)
      sb.append(", conns=").append(ArrayHelper.toString(connectionIDs));
    if (syncNCDIds != null && syncNodeIds != null && syncNCDIds.length > 0 || syncRouteID > 0)
      sb.append(", sync=").append(ArrayHelper.toString(syncNCDIds)).append('/').append(ArrayHelper.toString(syncNodeIds)).append('/').append(syncRouteID);
    if (isCorrelTrigger())
      sb.append(", corrTrig=").append(getCorrelTriggerTypes());
    if (getServerActionType() != null)
      sb.append(", Action=").append(getServerActionType().name());

    return sb.toString();
  }

  private String appendParam(TextStringBuilder sb) {
    int size = (multiVarList != null ? multiVarList.length : 0) + 1;
    String paramId = parameterID == null ? "No param ID" : String.valueOf(parameterID.getId());
    String paramName = parameterID == null ? "" : StringUtils.substring(parameterID.name(), 0, 20);
    sb.appendAll("[", size, "]{", paramId, "/", paramName, "=",
            StringUtils.isEmpty(newStringValue) ? String.valueOf(newValue) : newStringValue.length() <= 10 ? newStringValue : newStringValue.substring(0, 10) + "...", "}");
    return "";
  }

  /**
   * Creates a properties string for logging purposes.
   */
    public String toString(String prefix, boolean fullLog, List<VariableBinding> vbl) {
      StringBuilder sb = new StringBuilder(prefix);
      sb.append((id > 0) ? "id=" + id : "");
      sb.append(", DT=").append((detectionType != null) ? detectionType.getType() : "");
      sb.append(", SRC-IP=").append((sourceNE_IP != null) ? sourceNE_IP : "")
                            .append(" (").append(sourceNEType).append(")");
      sb.append(", TRP-ID=");
      String _enterprise = (enterprise != null ? enterprise : "");
      int _trapID = trapID;
      boolean _isSpecific = isSpecificTrap();
      if (enterprise == null && vbl != null && vbl.size() >= 2) {
        OID oid = new OID((OID)vbl.get(1).getVariable());
        _trapID = oid.removeLast();
        _enterprise = oid.toDottedString();
      }
      if(_enterprise != null && _enterprise.startsWith(MIB.SNMPV2.OID_GENERIC_TRAPS)) {
        _isSpecific = false;
      }
      sb.append(_trapID).append(" [").append(_enterprise).append("]").append(_isSpecific?"(S)":"(G)");

      if (fullLog) {
       sb.append(", Varbinds=").append(vbl);
      }

      return sb.toString();
    }

  //==============================================================================
  //=== public Methods ===========================================================
  //==============================================================================


  public EventType getType() {
    return type;
  }

  public int getSourceNE_ID() {
    return sourceNE_ID;
  }

  public int getSubnetID() {
    return subnetID;
  }

  public int getLineID() {
    return lineID;
  }

  public EventSeverity getSeverity() {
    return severity;
  }

  public String getSourceName() {
    return sourceName;
  }

  @Override
  public int getNeId() {
    return sourceNE_ID;
  }

  @Override
  public int getNeLogIndex() {
    return neLogIndex;
  }

  public int[] getSyncNodeIds() {
    return syncNodeIds;
  }

  public void setSyncNodeIds(int[] syncNodeIds) {
    this.syncNodeIds = syncNodeIds;
  }

  public int[] getSyncNCDIds() {
    return syncNCDIds;
  }

  public void setSyncNCDIds(int[] syncNCDIds) {
    this.syncNCDIds = syncNCDIds;
  }

  public List<EventAssocObjectId> getMlTopologyObjects() {
    return getAssociatedObjects()
        .stream()
        .filter(eventAssocObjectId -> eventAssocObjectId.getObjectId() > 0)
        .toList();
  }

  public List<EventAssocObjectId> getMlTopologyObjects(EventAssocObjectId.ObjectType objectType) {
    return getMlTopologyObjects()
        .stream()
        .filter(assocObject -> assocObject.getObjectTypeEnum() == objectType)
        .toList();
  }

  public boolean containsObjectType(EventAssocObjectId.ObjectType objectType) {
      return getAssociatedObjects()
          .stream()
          .anyMatch(assocObjectId -> assocObjectId.getObjectTypeEnum() == objectType);
  }

  public boolean containsAssocMlObjectType(EventAssocObjectId.ObjectType objectType) {
    return !getMlTopologyObjects(objectType).isEmpty();
  }

  public void addMlTopologyObject(Integer id, EventAssocObjectId.ObjectType objectType) {
    addAssociatedObject(objectType, id, null);
  }

  public void addMlTopologyObject(Map<Integer, EventAssocObjectId.ObjectType> map) {
    map.forEach(this::addMlTopologyObject);
  }

  public List<EventAssocObjectId> getAssocUuidObjects() {
    return getAssociatedObjects()
        .stream()
        .filter(eventAssocObjectId -> eventAssocObjectId.getObjectUuid() != null)
        .toList();
  }

  public List<EventAssocObjectId> getAssocUuidObjects(EventAssocObjectId.ObjectType objectType) {
    return getAssocUuidObjects()
        .stream()
        .filter(eventAssocObjectId -> eventAssocObjectId.getObjectTypeEnum() == objectType)
        .toList();
  }

  public boolean containsAssocUuidObjectType(EventAssocObjectId.ObjectType objectType) {
    return !getAssocUuidObjects(objectType).isEmpty();
  }

  public void addAssocUuidObject(UUID id, EventAssocObjectId.ObjectType objectType) {
    addAssociatedObject(objectType, 0, id);
  }

  public List<EventAssocObjectId> getAssociatedObjects() {
    if (associatedObjects == null) {
        associatedObjects = new ArrayList<>();
    }
    return associatedObjects;
  }

  public void addAssociatedObject(EventAssocObjectId.ObjectType objectType, int objectId, UUID objectUuid) {
    if (associatedObjects == null) {
      associatedObjects = new ArrayList<>();
    }
    associatedObjects.add(new EventAssocObjectId(objectType, objectId, objectUuid));
  }

  public List<UUID> getCsFolderUuids() {
    return getAssocUuidObjects(EventAssocObjectId.ObjectType.EOD_CONNECTIVITY_SERVICE_FOLDER)
            .stream()
            .map(EventAssocObjectId::getObjectUuid)
            .toList();
  }

  public StringBuilder getText() {
    return description;
  }

  public void resetText() {
    description = null;
  }

  //@Override
  public void setText(StringBuilder sb) {
    description = sb;
  }

  public void setText(String str) {
    description = new StringBuilder(str);
  }

  public void setTextIfEmpty(String str) {
    if (StringUtils.isEmpty(description))
      description = new StringBuilder(str);
  }

  public void extendText(String str) {
    if (description == null)
      description = new StringBuilder(str);
    else
      description.append(str);
  }

  public void addText(String str) {
    if (StringUtils.isEmpty(description))
      description = new StringBuilder(str);
    else
      description.append("; ").append(str);
  }

  /**
   * Returns the NE generated time stamp if available, otherwise the time of reception by the NMS.
   * @return the NE generated time stamp if available, otherwise the time of reception by the NMS.
   */
  public long getTimeStamp() {
    return neTimeStamp > 0 ? neTimeStamp : nmsTimeStamp;
  }

  public long getNMSTimeStamp(){
    return nmsTimeStamp;
  }

  public long getCLearedNMSTimeStamp(){ return nmsClearedTimeStamp; }
  public String getShortName() {
    return shortName;
  }

  public long getFirstRaisedAlarm(){
    return firstRaisedAlarm;
  }

  public boolean isFspR7MIB() {
    return enterprise == null || enterprise.equals(MIBFSP_R7.Entity.OID_ENTERPRISE);
  }

  public boolean isAlarm() {
    return type != null && type.getType() <= EventType._CLEARING;
  }

  // **************
  // Setter for IDs
  // **************

  public static abstract class Param<T> {
    private T value;

    @Override
    public boolean equals(Object o) {
      if (this == o) return true;
      if (o == null || getClass() != o.getClass()) return false;
      Param param = (Param) o;
      if (value != null)
        return value.equals(param.value);
      else
        return param.value == null;
    }

    @Override
    public int hashCode() {
      return value != null ? value.hashCode() : 0;
    }

    protected Param(T value) { this.value = value; }
    protected T getValue() { return value; }
    protected abstract void setParam(EventDTO event);

    public static class Severity extends Param<EventSeverity> {
      public Severity(EventSeverity severity) { super(severity); }
      protected void setParam(EventDTO event) { event.severity = getValue(); }
    }
    public static class Impairment extends Param<Boolean> {
      public Impairment(boolean impairment) { super(impairment); }
      protected void setParam(EventDTO event) { event.impairment = getValue(); }
    }
    public static class Category extends Param<CategoryType> {
      public Category(CategoryType category) { super(category); }
      protected void setParam(EventDTO event) { event.category = getValue(); }
    }
    public static class NmsTimeStamp extends Param<Long> {
      public NmsTimeStamp(Long NmsTimeStamp) { super(NmsTimeStamp); }
      protected void setParam(EventDTO event) { event.nmsTimeStamp = getValue(); }
    }

    public static class NmsCleareTimeStamp extends Param<Long> {
      public NmsCleareTimeStamp(Long NmsCleareTimeStamp) { super(NmsCleareTimeStamp); }
      protected void setParam(EventDTO event) { event.nmsClearedTimeStamp = getValue(); }
    }
    public static class NeTimeStamp extends Param<Long> {
      public NeTimeStamp(Long NeTimeStamp) { super(NeTimeStamp); }
      protected void setParam(EventDTO event) { event.neTimeStamp = getValue(); }
    }
    public static class Parameter extends Param<String> {
      public Parameter(String parameter) { super(parameter); }
      protected void setParam(EventDTO event) { event.description = new StringBuilder(getValue()); }
    }
    public static class SourceNEID extends Param<Integer> {
      public SourceNEID(Integer sourceNEID) { super(sourceNEID); }
      protected void setParam(EventDTO event) { event.sourceNE_ID = getValue(); }
    }
    public static class SubnetID extends Param<Integer> {
      public SubnetID(Integer subnetID) { super(subnetID); }
      protected void setParam(EventDTO event) { event.subnetID = getValue(); }
    }
    public static class NewValue extends Param<Long> {
      public NewValue(long newValue) { super(newValue); }
      protected void setParam(EventDTO event) { event.newValue = getValue(); }
    }
    public static class NewStringValue extends Param<String> {
      public NewStringValue(String newStringValue) { super(newStringValue); }
      protected void setParam(EventDTO event) { event.newStringValue = getValue(); }
    }
    public static class ObjectIndex extends Param<EntityIndex> {
      public ObjectIndex(EntityIndex objectIndex) { super(objectIndex); }
      protected void setParam(EventDTO event) { event.objectIndex = getValue(); }
    }
    public static class EntityDescr extends Param<String> {
      public EntityDescr(String entityDescr) { super(entityDescr); }
      protected void setParam(EventDTO event) { event.entityDescription = getValue(); }
    }
    public static class EntityFullDescr extends Param<String> {
      public EntityFullDescr(String entityFullDescr) { super(entityFullDescr); }
      protected void setParam(EventDTO event) { event.entityFullDescription = getValue(); }
    }
    public static class AidString extends Param<String> {
      public AidString(String aidString) { super(aidString); }
      protected void setParam(EventDTO event) { event.setAidString(getValue()); }
    }

    public static class MtpDisplayName extends Param<String> {
      public MtpDisplayName(String mtpDisplayName) { super(mtpDisplayName); }
      protected void setParam(EventDTO event) { event.setMtpDisplayName(getValue()); }
    }
    public static class EntityAlias extends Param<String> {
      public EntityAlias(String entityAlias) { super(entityAlias); }
      protected void setParam(EventDTO event) { event.entityAlias = getValue(); }
    }
    public static class LineEndLoc extends Param<Boolean> {
      public LineEndLoc(boolean lineEndLoc) { super(lineEndLoc); }
      protected void setParam(EventDTO event) { event.setLineEndLoc(getValue()); }
    }
    public static class LineID extends Param<Integer> {
      public LineID(Integer lineID) { super(lineID); }
      protected void setParam(EventDTO event) { event.lineID = getValue(); }
    }
    public static class ConnIDs extends Param<int[]> {
      public ConnIDs(int[] connIDs) { super(connIDs); }
      protected void setParam(EventDTO event) { event.connectionIDs = getValue(); }
    }
    public static class ParentIDs extends Param<int[]> {
      public ParentIDs(int[] parentIDs) { super(parentIDs); }
      protected void setParam(EventDTO event) { event.parentsIDs = getValue(); }
    }
    public static class SyncNcdIDs extends Param<int[]> {
      public SyncNcdIDs(int[] syncNcdIds) { super(syncNcdIds); }
      protected void setParam(EventDTO event) { event.setSyncNCDIds(getValue()); }
    }
    public static class SyncNodeIDs extends Param<int[]> {
      public SyncNodeIDs(int[] syncNodeIds) { super(syncNodeIds); }
      protected void setParam(EventDTO event) { event.setSyncNodeIds(getValue()); }
    }
    public static class SyncRouteID extends Param<Integer> {
      public SyncRouteID(Integer syncRouteId) { super(syncRouteId); }
      protected void setParam(EventDTO event) { event.syncRouteID = getValue(); }
    }
    public static class AssocIDs extends Param<Map<Integer, EventAssocObjectId.ObjectType>> {
      public AssocIDs(Map<Integer, EventAssocObjectId.ObjectType> assocIDs) { super(assocIDs); }
      protected void setParam(EventDTO event) { event.addMlTopologyObject(getValue()); }
    }
      public static class SourceName extends Param<String> {
      public SourceName(String sourceName) { super(sourceName); }
      protected void setParam(EventDTO event) { event.sourceName = getValue(); }
    }
    public static class ServiceName extends Param<String> {
      public ServiceName(String serviceName) { super(serviceName); }
      protected void setParam(EventDTO event) { event.serviceName = getValue(); }
    }
    public static class CustomerName extends Param<String> {
      public CustomerName(String customerName) { super(customerName); }
      protected void setParam(EventDTO event) { event.customerName = getValue(); }
    }

    public static class Comment extends Param<String> {
      public Comment(String comment) { super(comment);}
      protected void setParam(EventDTO event) { event.comment = getValue(); }
    }

    public static class CorrelTriggerEvent extends Param<CorrelTriggerType> {
      public CorrelTriggerEvent(CorrelTriggerType correlTrigger) { super(correlTrigger); }
      protected void setParam(EventDTO event) { event.addCorrelTriggerEvent(getValue()); }
    }
    public static class DebugLog extends Param<String> {
      public DebugLog(String debugLog) { super(debugLog); }
      protected void setParam(EventDTO event) { event.debugLog = getValue(); }
    }
    public static class ServerActionObject extends Param<Object> {
      public ServerActionObject(Object actionObject) { super(actionObject); }
      protected void setParam(EventDTO event) { event.setServerActionObject(getValue()); }
    }
    public static class PvEntityId extends Param<String> {
      public PvEntityId(String pventityid) { super(pventityid); }
      protected void setParam(EventDTO event) { event.pventityid = getValue(); }
    }
    public static class PvSourceId extends Param<String> {
      public PvSourceId(String pvsourceid) { super(pvsourceid); }
      protected void setParam(EventDTO event) { event.pvsourceid = getValue(); }
    }
    public static class PvSourceType extends Param<String> {
      public PvSourceType(String pvsourcetype) { super(pvsourcetype); }
      protected void setParam(EventDTO event) { event.pvsourcetype = getValue(); }
    }

  }

  public EventDTO setParams(EventDTO.Param... params) {
    for (EventDTO.Param param : params) {
      if (param != null)
        param.setParam(this);
    }

    return this;
  }

  /**
   * Set Trap-type fields
   * @param detectionType   Detection-type (TRP, LOG, STAT, SRV)
   * @param type            Event-type (ALM, CLR, NET)
   * @param securityEvent   Security-event (true, false)
   * @return EventDTO this
   */
  public EventDTO setTrapType(EventDetectionType detectionType, EventType type, boolean securityEvent) {
    this.detectionType = detectionType;
    this.type = type;
    this.securityEvent = securityEvent;
    return this;
  }

  public EventDTO setTrapInfo(EventSeverity severity, int trapID, int alarmType, String shortName, String parameter) {
    this.severity = severity;
    this.trapID = trapID;
    this.alarmType = alarmType;
    this.shortName = shortName;
    this.description = new StringBuilder(parameter);
    return this;
  }

  public EventDTO setSrcNE(String ipAddresss, int neID, int neType, int subnetID) {
    this.sourceNE_IP = ipAddresss;
    this.sourceNE_ID = neID;
    this.sourceNEType = neType;
    this.subnetID = subnetID;
    return this;
  }

  public EventDTO setSubnet(int subnetID) {
    this.subnetID = subnetID;
    return this;
  }

  public EventDTO setEntityDescr(EntityIndex objectIndex, String shortDescr, String fullDescr) {
    this.objectIndex = objectIndex;
    this.entityDescription = shortDescr;
    this.entityFullDescription = fullDescr;
    return this;
  }


  public EventDTO setSyncIds(int[] syncNCDIds, int[] syncNodeIds, int routeId) {
   this.syncNCDIds = syncNCDIds;
    this.syncNodeIds = syncNodeIds;
    this.syncRouteID = routeId;
    return this;
  }

  public void setEntityAlias(String alias) {
    if(alias != null)
      this.entityAlias = alias;
  }

  /**
   * Checks whether trap was created by polling.
   * @return true if trap was created by polling.
   */
  public boolean isCreatedByPolling() {
    return (detectionType == EventDetectionType.CONF_STATUS_UPDATE);
  }

  public long getNewValue() {
    return newValue;
  }

  public String getStringNewValue() {
    return newStringValue;
  }

  public void setStringNewValue(String newStringValue) {
    this.newStringValue = newStringValue;
  }

  public TrapParameterID getTrapParamID() {
    return parameterID;
  }

  public int[] getObjectIndexArray () {
    return objectIndexes;
  }

  @Override
  public EntityIndex getObjectIndex () {
    return objectIndex;
  }

  @Override
  public String getObjectStringIndex () {
    return getAidString();
  }

  @Override
  public CategoryType getCategoryType () {
    return category;
  }

  public int getNeType() {
    return sourceNEType;
  }

  /**
   * Checks whether the event is related to a connection.
   * @return true, if the event is related to a connection.
   */
  public boolean hasConnections() {
    return ((connectionIDs != null && connectionIDs.length > 0) || !getMlTopologyObjects(EventAssocObjectId.ObjectType.MLT_SERVICE).isEmpty());
  }

  public boolean hasClassicConnections() {
    return connectionIDs != null && connectionIDs.length > 0;
  }

  public void setNetworkElementProperties(NetworkElementDTO networkElementProperties){
    sourceNE_ID       = networkElementProperties.id;
    sourceNE_IP       = networkElementProperties.ipAddress;
    sourceNEType      = networkElementProperties.type;
    subnetID          = networkElementProperties.subnet_ID;
  }

  /**
   * Creates and returns a clone.
   * @return the clone.
   */
  @SuppressWarnings({"CloneDoesntDeclareCloneNotSupportedException"})
  @Override
  public Object clone() {
    try {
      return super.clone();
    } catch (CloneNotSupportedException cns) {
      return null;
    }
  }
  public boolean is112() {
    return MIBAdvaFspR7.OID_ENTERPRISE.equals(enterprise);
  }


  @Override
  public void setNewValue(long newValue) {
    this.newValue = newValue;
  }

  @Override
  public void setTrapParamID(TrapParameterID trapParamID) {
    this.parameterID = trapParamID;
  }

  //@Override
  public void setPotentiallyServiceAffecting(boolean isPotentiallyServiceAffecting) {
    this.impairment = isPotentiallyServiceAffecting;
  }

  public void setEntityDescription(String entityDescription) {
    if(entityDescription != null)
      this.entityDescription = entityDescription;
  }

  public void setEntityFullDescription(String entityFullDescription) {
    if(entityFullDescription != null)
      this.entityFullDescription = entityFullDescription;
  }

  public boolean isAckExternal() {
    return ackExternal;
  }

  public void setAckExternal(boolean ackExternal) {
    this.ackExternal = ackExternal;
  }

  public boolean isTransientAlarm() {
    return transientAlarm;
  }

  public void setTransientAlarm(boolean transientAlarm) {
    this.transientAlarm = transientAlarm;
  }

  public boolean isBulkTrap() {
    return processingData != null && processingData.bulkTrapParams != null;
  }

  public List<VarbindObjectInfo> getVarbindObjects() {
    List<VarbindObjectInfo> vboList = new ArrayList<>();
    vboList.add(this);
    if (multiVarList != null) {
      for (EventMultiVarDTO eventMultiVarDTO : multiVarList)
        vboList.add(eventMultiVarDTO);
    }
    return vboList;
  }

  public int getTrapID() {
    return trapID;
  }

  public void setTrapID(int trapID) {
    this.trapID = trapID;
  }

  public void addToDebugLog(String debugEntry) {
    if(debugLog != null) {
      debugLog += ";" + debugEntry;
    } else {
      debugLog = debugEntry;
    }
  }

  public void setSeverity(EventSeverity severity) {
    this.severity = severity;
  }
}
