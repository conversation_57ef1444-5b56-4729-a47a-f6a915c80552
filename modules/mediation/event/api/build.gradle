plugins {
    id 'com.adva.gradle.plugin.eclipse-link-weaver'
    id 'com.adva.gradle.plugin.aspectj-weaver'
}

eclipseLinkWeave {
    masterModule mod_mediation.project
}

aspectjWeave {
    if (briefOutput) {
        lintLevel = 'ignore'
    }
}

setupMediationModule(project)
String buildDirectory = project.buildDir
def genDir = file( "$buildDirectory/generatedSources" ).getCanonicalPath()

dependencies {
    api modep( mod_adva_concurrent )
    api modep( mod_persistence_common )
    api modep( mod_support_bean_provider_api )

    implementation modep( mod_nmscommon )
    implementation modep( mod_ui_framework_api )
    implementation modep( mod_property )

    implementation libs.jakarta.persistence
    implementation libs.jackson.annotations
    implementation libs.commons.lang3
    implementation libs.commons.text
    implementation libs.guava
    implementation libs.snmp4j
    implementation libs.commons.collections4
    implementation libs.spring.context
    implementation libs.slf4j.api
    implementation libs.log4j.api
    implementation libs.aspectjrt
}
