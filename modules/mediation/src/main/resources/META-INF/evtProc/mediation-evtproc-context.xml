<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">
    <context:annotation-config/>

    <!-- ant build checks for string to add this file in jar com.adva.nlms.mediation.event.EventDataAccessHdlrImpl, do not remove this line so as this file is included in jar, unless some beans are added-->
    <bean id="eventProcFacadeImpl" class="com.adva.nlms.mediation.evtProc.EventProcFacadeImpl"/>
    <bean class="com.adva.nlms.mediation.evtProc.F3.BulkTrapParser"/>
    <bean class="com.adva.nlms.mediation.evtProc.EvtProcHelper"/>
    <bean class="com.adva.nlms.mediation.evtProc.evtValue.EventValueHdlr" init-method="init"/>
    <bean class="com.adva.nlms.mediation.evtProc.CorrelationTriggerChecker"/>
    <!--<bean id="eventForDBConsumer" class="com.adva.nlms.mediation.evtProc.pipeline.EventForDBConsumer" scope="prototype"/>-->

    <bean class="com.adva.nlms.mediation.event.MOAdapter"/>
    <bean class="com.adva.nlms.mediation.event.EventHelper"/>
    <bean class="com.adva.nlms.mediation.evtProc.EventQueue" />
    <bean class="com.adva.nlms.mediation.evtProc.F8SlcEventsMLTrailAssociator" />
    <!--<bean id="moEventProcessor" class="com.adva.nlms.mediation.evtProc.pipeline.MOEventProcessor" scope="prototype"/>-->

    <bean class="com.adva.nlms.mediation.event.processor.MOEventStartupLoader"/>
    <!--<bean id="eventDBConsumer" class="com.adva.nlms.mediation.evtProc.pipeline.EventForDBConsumer" scope="prototype"/>-->

    <!--<bean id="distributorToConsumerNotificationLock" class="java.util.concurrent.locks.ReentrantLock"/>-->
    <!--<bean id="haveEventForProcessing" factory-bean="distributorToConsumerNotificationLock" factory-method="newCondition"/>-->


    <!--
    <bean id ="eventDistributor" class="com.adva.nlms.mediation.evtProc.pipeline.EventDistributor">
        <constructor-arg ref="missedEventManager"/>
        <constructor-arg ref="distributorToConsumerNotificationLock"/>
        <constructor-arg ref="haveEventForProcessing"/>
    </bean>
    -->

    <!--
    <bean id="threadManager" class="com.adva.nlms.mediation.evtProc.pipeline.EventProcessingTaskFactory">
        <lookup-method name="createDecorationTask" bean="eventQueue" />
        <lookup-method name="createMOEventProcessingTask" bean="moEventProcessor" />
        <lookup-method name="createDBConsumerTask" bean="eventDBConsumer" />
        <lookup-method name="createDistributorTask" bean="eventDistributor" />
    </bean>
    -->
    <bean class="com.adva.nlms.mediation.evtProc.MissedEventMonitor"/>
    <bean id ="missedEventManager" class="com.adva.nlms.mediation.evtProc.MissedEventManager">
        <constructor-arg>
            <bean class="com.adva.nlms.mediation.evtProc.MissedEventMonitor"/>
        </constructor-arg>
    </bean>
    <bean id="eventBufferManager" class="com.adva.nlms.mediation.evtProc.pipeline.EventBufferManager"/>

    <bean id="eventProcessingManager" class="com.adva.nlms.mediation.evtProc.pipeline.EventProcessingManager"/>
    <bean class="com.adva.nlms.mediation.evtProc.pipeline.EventBufferOverflowHandler"/>
    <bean class="com.adva.nlms.mediation.evtProc.TrapHandler"/>
    <bean class="com.adva.nlms.mediation.evtProc.UnknownTrapHandler" id="unknownTrapHandler"/>

    <bean class="com.adva.nlms.mediation.evtProc.evtValue.EventParamHandler" id="eventParamHandler">
        <constructor-arg ref="moDescriptionSupplier"/>
    </bean>
    <bean class="com.adva.nlms.mediation.evtProc.TrapFloodHdlr"/>
    <bean class="com.adva.nlms.mediation.evtProc.NlmTrapListener"/>
    <bean id = "varbindUtilTool" class="com.adva.nlms.mediation.evtProc.VarbindUtilTool"/>
    <bean class="com.adva.nlms.mediation.evtProc.ALMAlarmUtil"/>
    <bean class="com.adva.nlms.mediation.evtProc.RemarkNameUpdater">
        <constructor-arg ref="fbrPlantHdlr" />
        <constructor-arg ref="reflectorFamRecordDAO"/>
    </bean>
    <bean class="com.adva.nlms.mediation.evtProc.driver.DriverHandler"/>

    <bean id="transientEventHdlrFSP150CC" class="com.adva.nlms.mediation.evtProc.TransientEventHdlrFSP150CC">
        <constructor-arg>
            <bean class="com.adva.nlms.mediation.common.config.EntityIndexSetter"/>
        </constructor-arg>
    </bean>

    <bean id="transientEventHdlrFSP150CM" class="com.adva.nlms.mediation.evtProc.TransientEventHdlrFSP150CM">
        <constructor-arg>
            <bean class="com.adva.nlms.common.config.f3.EntityIndexHelperF3" factory-method="getInstance"/>
        </constructor-arg>
    </bean>
    <bean id="genericEventHandlerHN4000Impl" class="com.adva.nlms.mediation.evtProc.GenericEventHandlerHN4000Impl"/>

    <bean class="com.adva.nlms.mediation.evtProc.TrapParser"/>

    <!-- ***** Trap Processors ***** -->
    <bean id="trapProcessorAbstract" class="com.adva.nlms.mediation.evtProc.TrapProcessorAbstract" abstract="true">
        <property name="moAdapter" ref="com.adva.nlms.mediation.event.MOAdapter"/>
        <property name="trapParser" ref="com.adva.nlms.mediation.evtProc.TrapParser"/>
        <property name="neHdlr" ref="networkElementHdlr"/>
        <property name="varbindUtilTool" ref="varbindUtilTool"/>
    </bean>

    <bean id="trapProcessorFSP150CM" class="com.adva.nlms.mediation.evtProc.TrapProcessorFSP150CM" parent="trapProcessorAbstract">
        <property name="tableDescriptorHdlrFSP150CM" ref="transientEventHdlrFSP150CM"/>
        <property name="entityIndexHelperF3" ref="entityIndexHelperF3"/>
    </bean>
    <bean id="trapProcessorOSAF3" class="com.adva.nlms.mediation.evtProc.TrapProcessorOSAF3" parent="trapProcessorFSP150CM"/>
    <bean id="trapProcessorOSA540x" class="com.adva.nlms.mediation.evtProc.TrapProcessorOSA540x" parent="trapProcessorFSP150CM"/>

    <bean id="trapProcessorHN4000" class="com.adva.nlms.mediation.evtProc.TrapProcessorHN4000" parent="trapProcessorAbstract">
        <property name="genericEventHandler" ref="genericEventHandlerHN4000Impl"/>
    </bean>
    <bean id="trapProcessorHN400" class="com.adva.nlms.mediation.evtProc.TrapProcessorHN400" parent="trapProcessorHN4000"/>

    <bean id="trapProcessorFSP150CC" class="com.adva.nlms.mediation.evtProc.TrapProcessorFSP150CC" parent="trapProcessorAbstract">
        <property name="tableDescriptorHdlrFSP150CC" ref="transientEventHdlrFSP150CC"/>
    </bean>

    <bean id="trapProcessorFSP150CP" class="com.adva.nlms.mediation.evtProc.TrapProcessorFSP150CP" parent="trapProcessorAbstract"/>
    <bean id="trapProcessorFSP150MX" class="com.adva.nlms.mediation.evtProc.TrapProcessorFSP150MX" parent="trapProcessorFSP150CP"/>

    <bean id="trapProcessorFSP" class="com.adva.nlms.mediation.evtProc.TrapProcessorFSP" parent="trapProcessorAbstract" abstract="true"/>
    <bean id="trapProcessorFSP1500" class="com.adva.nlms.mediation.evtProc.TrapProcessorFSP1500" parent="trapProcessorFSP"/>

    <bean id="trapProcessorFSPR7" class="com.adva.nlms.mediation.evtProc.TrapProcessorFSPR7" parent="trapProcessorAbstract">
        <property name="entityIndexHelperF7" ref="com.adva.nlms.mediation.config.fsp_r7.EntityIndexHelperF7"/>
    </bean>
    <bean id="trapProcessorJuniper" class="com.adva.nlms.mediation.evtProc.TrapProcessorJuniper" parent="trapProcessorAbstract"/>
    <bean id="trapProcessorSymmetricom" class="com.adva.nlms.mediation.evtProc.TrapProcessorSymmetricom" parent="trapProcessorAbstract"/>
    <bean id="trapProcessorOscilloquartz" class="com.adva.nlms.mediation.evtProc.TrapProcessorOscilloquartz" parent="trapProcessorAbstract"/>
    <bean id="trapProcessorALM" class="com.adva.nlms.mediation.evtProc.TrapProcessorALM" parent="trapProcessorAbstract">
        <property name="almAlarmUtil" ref="com.adva.nlms.mediation.evtProc.ALMAlarmUtil"/>
        <property name="remarkNameUpdater" ref="com.adva.nlms.mediation.evtProc.RemarkNameUpdater"/>
    </bean>
    <bean id="trapProcessorFSP150EGM" class="com.adva.nlms.mediation.evtProc.TrapProcessorEGM" parent="trapProcessorAbstract"/>
    <bean id="trapProcessorF8" class="com.adva.nlms.mediation.ec.event.TrapProcessorEc" parent="trapProcessorAbstract"/>
    <bean id="trapProcessorMRV" class="com.adva.nlms.mediation.evtProc.TrapProcessorMRV" parent="trapProcessorAbstract"/>
    <bean id="trapProcessorZ4806" class="com.adva.nlms.mediation.evtProc.TrapProcessorZ4806" parent="trapProcessorMRV"/>

    <bean id="trapProcessorCustom" class="com.adva.nlms.mediation.evtProc.TrapProcessorCustom" parent="trapProcessorAbstract"/>
    <bean id="trapProcessorOSAProxy" class="com.adva.nlms.mediation.evtProc.TrapProcessorOSAProxy" parent="trapProcessorCustom"/>
    <bean id="trapProcessorOSA54CR" class="com.adva.nlms.mediation.evtProc.TrapProcessorOSA54CR" parent="trapProcessorFSP150CM"/>


    <bean class="com.adva.nlms.mediation.evtProc.TrapProcessorPool" init-method="init">

    </bean>

    <bean id="snmpTrapOidToAlarmId" class="com.adva.nlms.mediation.evtProc.definition.SnmpTrapOidToAlarmId"/>
    <!--
    <bean id="decorationBufferDescriptor" class="com.adva.nlms.mediation.evtProc.pipeline.EventProcessingBufferDescriptor" scope="prototype">
        <property name="allowedThreadNumber"
                  value ="#{T(com.adva.nlms.common.property.FNMPropertyFactory).getPropertyAsInt(T(com.adva.nlms.common.property.FNMPropertyFactory).DECORATION_THREAD_NUMBER, T(com.adva.nlms.common.property.FNMPropertyFactory).DECORATION_THREAD_NUMBER_DEFAULT)}"/>
        <property name="buffer" ref="decorationQueue"/>
        <property name="name" value="#T{(com.adva.nlms.mediation.evtProc.pipeline.EventBufferManager).DECORATION_QUEUE_NAME}"/>
        <property name="allowedProcessedTaskNumber" value="#T{(com.adva.nlms.mediation.evtProc.pipeline.EventBufferManager).DEFAULT_READ_EVENTS_BY_DECORATOR}"/>
        <property name="idleTime" value="#T{(com.adva.nlms.mediation.evtProc.pipeline.EventBufferManager).THREAD_MIN_IDLE_TIME}"/>

    </bean>
        -->
    <!--
   <bean id="blockingQueue" class="java.util.concurrent.ArrayBlockingQueue" scope="prototype">
       <constructor-arg  index="0">  <value>1111</value>
       </constructor-arg>
   </bean>
   <bean id="dbBufferDescriptor" class="com.adva.nlms.mediation.evtProc.pipeline.EventProcessingBufferDescriptor" scope="prototype">
       <property name="allowedThreadNumber"><value>1</value></property>
       <property name="buffer"><ref bean="blockingQueue"/></property>
   </bean> -->
    <!--
    <bean class="com.adva.nlms.mediation.evtProc.pipeline.EventDistributor"/>     -->
    <!--<import resource="bufferdescriptor/bufferdescriptor-context.xml"/>-->

</beans>