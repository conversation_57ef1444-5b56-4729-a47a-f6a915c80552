<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:annotation-config/>
    <bean class="com.adva.nlms.mediation.smapp.nbi.rest.provisioning.ProvisioningFacadeAdapter"/>
    <bean class="com.adva.nlms.mediation.smapp.nbi.rest.provisioning.EvpnControllerAdapter"/>
    <bean class="com.adva.nlms.mediation.smapp.nbi.rest.provisioning.PDDiscoveryFacadeAdapter"/>
    <bean class="com.adva.nlms.mediation.smapp.nbi.rest.pdentityswap.PDEntitySwapFacadeAdapter"/>

</beans>