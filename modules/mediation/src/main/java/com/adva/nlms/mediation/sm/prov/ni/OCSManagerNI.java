/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: spyrosm
 */

package com.adva.nlms.mediation.sm.prov.ni;

import com.adva.common.model.template.Keyword;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.mib.Dictionary;
import com.adva.nlms.common.sm.ProtMechanism;
import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.common.snmp.NoSuchMDObjectException;
import com.adva.nlms.mediation.common.persistence.MDPersistenceHelper;
import com.adva.nlms.mediation.common.serviceProvisioning.ProvFSPR7Properties;
import com.adva.nlms.mediation.common.transactions.InvalidPollingException;
import com.adva.nlms.mediation.config.EntityDAO;
import com.adva.nlms.mediation.config.ManagedObjectDBImpl;
import com.adva.nlms.mediation.config.ModuleDBImpl;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementHdlrLocal;
import com.adva.nlms.mediation.config.NoSuchEntityException;
import com.adva.nlms.mediation.config.NoSuchNetworkElementException;
import com.adva.nlms.mediation.config.fsp_r7.entity.facility.physical.PortFSP_R7DBImpl;
import com.adva.nlms.mediation.config.fsp_r7.entity.fibermap.TerminationPointFSP_R7DBImpl;
import com.adva.nlms.mediation.config.fsp_r7.polling.entities.DeleteEntityPolling;
import com.adva.nlms.mediation.event.message.MessageManager;
import com.adva.nlms.mediation.mltopologymodel.core.concurrent.api.MLTaskExecutorService;
import com.adva.nlms.mediation.mltopologymodel.model.dao.MLTopologyElementDAO;
import com.adva.nlms.mediation.mltopologymodel.mofacade.MLTopologyResyncFacade;
import com.adva.nlms.mediation.mltopologymodel.momediation.proc.MLTopologyMoFacade;
import com.adva.nlms.mediation.mltopologymodel.sync.entity.task.ResyncModuleTask;
import com.adva.nlms.mediation.ne_comm.f7.necomm.SNMPCtrlFSP_R7DS;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.polling.PollingType;
import com.adva.nlms.mediation.polling.api.PF;
import com.adva.nlms.mediation.sm.SMServiceHelper;
import com.adva.nlms.mediation.sm.ServiceMessageManager;
import com.adva.nlms.mediation.sm.analyzer.PAFReader;
import com.adva.nlms.mediation.sm.dao.OChConnectionDAO;
import com.adva.nlms.mediation.sm.exception.SMValidationException;
import com.adva.nlms.mediation.sm.model.AbstractConnectionDBImpl;
import com.adva.nlms.mediation.sm.model.OChConnectionDBImpl;
import com.adva.nlms.mediation.sm.model.SubChConnectionDBImpl;
import com.adva.nlms.mediation.sm.prov.AbstractOCSManager;
import com.adva.nlms.mediation.sm.prov.ModifyOCSData;
import com.adva.nlms.mediation.sm.prov.OpticalChannelService;
import com.adva.nlms.mediation.sm.prov.PollingUtils;
import com.adva.nlms.mediation.sm.prov.ProvisioningUtils;
import com.adva.nlms.mediation.sm.prov.SMProvException;
import com.adva.nlms.mediation.sm.prov.SMProvParams;
import com.adva.nlms.mediation.sm.prov.SMProvisioningManager;
import com.adva.nlms.mediation.sm.prov.ServiceDiagnosticsMessages;
import com.adva.nlms.mediation.sm.prov.TransportService;
import com.adva.nlms.mediation.sm.prov.cp.OCSAPSGroupHdlr;
import com.adva.nlms.mediation.sm.prov.cp.OCSProcessingParameters;
import com.adva.nlms.mediation.sm.prov.cp.RoutePreview;
import com.adva.nlms.mediation.sm.prov.ni.rest.NITunnelRestHandler;
import com.adva.nlms.mediation.sm.prov.ni.servicecreation.ComputedPathToRoutePreviewMapper;
import com.adva.nlms.mediation.sm.prov.ni.servicecreation.WdmTunnelCreator;
import com.adva.nlms.mediation.sm.prov.ni.servicemodification.ServiceModifyParameters;
import com.adva.nlms.mediation.topology.LineDBImpl;
import ni.proto.external.services.bundle.BundleOuterClass;
import ni.proto.external.services.path.PathOuterClass;
import ni.proto.external.services.service.ServiceOuterClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class OCSManagerNI extends AbstractOCSManager {
  private static final Logger LOGGER = LoggerFactory.getLogger(OCSManagerNI.class);

  private final NITunnelRestHandler niTunnelRestHandler;
  private final NIOCSPathHandler niOCSPathHandler;
  private final NIOCSPortHandler niOCSPortHandler;
  private final PAFReader pafReader;
  private final WdmTunnelCreator wdmTunnelCreator;
  private final SMServiceHelper smServiceHelper;
  private final OCSAPSGroupHdlr ocsAPSGroupHdlr;
  private final OChConnectionDAO connectionDAO;
  private final ServiceMessageManager serviceMessageManager;
  private final PollingUtils pollingUtils;
  private final SMProvisioningManager smProvisioningManager;
  private final NetworkElementHdlrLocal neHdlr;
  private final MLTaskExecutorService mlTaskExecutorService;

  @Autowired
  public OCSManagerNI(NITunnelRestHandler niTunnelRestHandler, PAFReader pafReader, NIOCSPathHandler niOCSPathHandler,
                      NIOCSPortHandler niOCSPortHandler, WdmTunnelCreator wdmTunnelCreator, SMServiceHelper smServiceHelper,
                      OCSAPSGroupHdlr ocsAPSGroupHdlr, OChConnectionDAO connectionDAO, ServiceMessageManager serviceMessageManager,
                      PollingUtils pollingUtils, SMProvisioningManager smProvisioningManager, NetworkElementHdlrLocal neHdlr,
                      MLTaskExecutorService mlTaskExecutorService) {
    this.niTunnelRestHandler = niTunnelRestHandler;
    this.niOCSPathHandler = niOCSPathHandler;
    this.pafReader = pafReader;
    this.niOCSPortHandler = niOCSPortHandler;
    this.wdmTunnelCreator = wdmTunnelCreator;
    this.smServiceHelper = smServiceHelper;
    this.ocsAPSGroupHdlr = ocsAPSGroupHdlr;
    this.connectionDAO = connectionDAO;
    this.serviceMessageManager = serviceMessageManager;
    this.pollingUtils = pollingUtils;
    this.smProvisioningManager = smProvisioningManager;
    this.neHdlr = neHdlr;
    this.mlTaskExecutorService = mlTaskExecutorService;
  }

  @Override
  public void createOCS(SMProvParams provParams, List<OpticalChannelService> opticalChannelServices,
                        List<OpticalChannelService> workList, List<OpticalChannelService> protList,
                        Map<Integer, List<PollingUtils.PollingInfo>> portMapForAsyncPoll) throws SMProvException, MDOperationFailedException {

    OpticalChannelService workOCS = opticalChannelServices.get(0);
    boolean excludePath = isExcludePath(provParams, opticalChannelServices);
    boolean isCccpAndFirstOCS = provParams.getProtMechanism() == ProtMechanism.CLIENT_CHANNEL_CARD_PROTECTION;
    List<NIOCSProcessingParameters> ocsParametersList = new ArrayList<>();
    for (OpticalChannelService ocs : opticalChannelServices) {
      if (!checkTunnelAndHandleMultilayerADM(ocs, ocs != workOCS, excludePath, provParams, workList, protList)) {
        excludePath = false;
        continue;
      }

      NIOCSProcessingParameters parameters = (NIOCSProcessingParameters) createOcsProcParameters(ocs, provParams, portMapForAsyncPoll, true);
      String niServiceId = createOCSOnNi(provParams, parameters);
      ServiceOuterClass.Service niService = wdmTunnelCreator.persistNiServiceAndConnection(niServiceId, ocs);
      parameters.setService(niService);
      Map<String, TerminationPointFSP_R7DBImpl> networkPortPTPs = new HashMap<>();
      parameters.setStartNetworkPortPTPs(networkPortPTPs);
      //set lines
      niOCSPathHandler.handleOCSLines(ocs, niService);
      ocs =  MDPersistenceHelper.refind(ocs, ocs.getId());
      parameters.setOcs(ocs);

      ocsParametersList.add(parameters);
      if (excludePath) {
        provParams.excludeOCSPath(ocs.getStartNEID(), ocs.getPeerNEID(), ocs.getRoute());
        excludePath = false;
      }
      if (isCccpAndFirstOCS) {
        provParams.setOtherOCSNiServiceId(niServiceId); // For createOCSOnNi NEXT time through this loop.
        isCccpAndFirstOCS = false;
      }
    }

    for (NIOCSProcessingParameters ocsProcessingParameters : ocsParametersList) {
      OpticalChannelService ocs = ocsProcessingParameters.getOcs();
      try {
        if (!ocs.hasECHs()) {
          updateOCSInventory(smServiceHelper.getServiceName(provParams), ocsProcessingParameters);
        }
        ocs = MDPersistenceHelper.refind(ocs,ocs.getId());
        smServiceHelper.addOCS(provParams, (OChConnectionDBImpl) ocs, false);
      } catch (NoSuchMDObjectException | InvalidPollingException | SNMPCommFailure | SMValidationException e) {
        throw new SMProvException(e.getMessage(), e);
      }
    }
  }

  @Override
  protected OCSProcessingParameters checkingForUnsupportedAttributes(OpticalChannelService ocs, SMProvParams provParams,
                                                                     Map<Integer, List<PollingUtils.PollingInfo>> portMapForAsyncPolling, boolean logMessages)
          throws SMProvException, NoSuchMDObjectException, SNMPCommFailure {
    OCSProcessingParameters ocsProcessingParameters = super.checkingForUnsupportedAttributes(ocs, provParams, portMapForAsyncPolling, logMessages);
    setPortParamsMap(ocsProcessingParameters, provParams);
    return ocsProcessingParameters;
  }

  /**
   * Sets port parameters map for F7 MFlex OCSProcessingParameters.
   */
  private void setPortParamsMap(OCSProcessingParameters ocsParameters, SMProvParams provParams) {
    if (isMFlex(provParams) && ocsParameters instanceof NIOCSProcessingParameters niocsProcessingParameters) {
      niocsProcessingParameters.setStartPortParamsMap(mapPortParamsMap(provParams.getStart().getNetworkPortProps().toMapOfStrings()));
      niocsProcessingParameters.setPeerPortParamsMap(mapPortParamsMap(provParams.getPeer().getNetworkPortProps().toMapOfStrings()));
    }
  }

  /**
   * Maps only the parameters {CONSTELLATION, FREQ_OFFSET(Frequency Detune)} that are relevant for MFlex WDM Tunnel.
   */
  private Map<String, String> mapPortParamsMap(Map<String, String> paramsMap) {
    Map<String, String> mFlexPortParamsMap = new HashMap<>(paramsMap.size());
    for (Map.Entry<String, String> entry : paramsMap.entrySet()) {
      String key = entry.getKey();
      if (Keyword.CONSTELLATION.toString().equals(key) || Keyword.FREQ_OFFSET.toString().equals(key)) {
        mFlexPortParamsMap.put(key, entry.getValue());
      }
    }
    return mFlexPortParamsMap;
  }

  private boolean isMFlex(SMProvParams smProvParams) {
    return smProvParams.getStart().getWorkCM().getAssignedEntityType() != null &&
           smProvParams.getPeer().getWorkCM().getAssignedEntityType() != null &&
           smProvParams.getStart().getWorkCM().getAssignedEntityType().getEntityType() == Dictionary.EntityType.MF_M6MDT &&
           smProvParams.getPeer().getWorkCM().getAssignedEntityType().getEntityType() == Dictionary.EntityType.MF_M6MDT;
  }

  private String createOCSOnNi(SMProvParams provParams, NIOCSProcessingParameters parameters) throws SMProvException {
    String serviceName = smServiceHelper.getServiceName(provParams);
    serviceMessageManager.addMessageUserAware(MessageManager.Info, serviceName, "Establishment of the WDM tunnel started");
    String niId = niTunnelRestHandler.createWDMTunnel(serviceName, provParams, parameters);
    serviceMessageManager.addMessageUserAware(MessageManager.Info, serviceName, "Establishment of the WDM tunnel completed");
    return niId;
  }

  private void updateOCSInventory(String serviceName, NIOCSProcessingParameters ocsProcessingParameters) throws NoSuchMDObjectException, InvalidPollingException, SMProvException, SNMPCommFailure, SMValidationException {
    LOGGER.info("Setting up network ports");
    niOCSPortHandler.processNetworkPorts(serviceName, ocsProcessingParameters);
    LOGGER.info("Finished setting up network ports");

    connectionDAO.handleChannelCardProtOChConnectionDBImpl(ocsProcessingParameters.getOcs());

    ocsAPSGroupHdlr.discoverAPSForProtectedService(ocsProcessingParameters);
  }

  @Override
  public void deleteOCS(OpticalChannelService ocs) throws SMProvException {
    String serviceId = ocs.getNiServiceId();
    if (serviceId != null) {
      ServiceOuterClass.Service niService = niTunnelRestHandler.getService(serviceId);
      if (niService != null) {
        niTunnelRestHandler.deleteTunnel(serviceId);
      } else {
        LOGGER.info("Service does not exist. Nothing to delete there.");
      }
    }
  }

  @Override
  public void modifyOCS(OpticalChannelService ocs, ModifyOCSData data) throws SMProvException {
    String serviceId = ocs.getNiServiceId();
    if (serviceId != null) {
      ServiceOuterClass.Service niService = niTunnelRestHandler.getService(serviceId);
      if (niService != null) {
        niTunnelRestHandler.modifyWDMTunnel(getServiceModifyParametersForRestoration(niService, data));
        niService = niTunnelRestHandler.getService(serviceId);
        if (niService != null) {
          List<LineDBImpl> updatedRestorationRoute = restorationPathToRoute(niService.getRestorationPath());
          data.setRestorationRoute(updatedRestorationRoute.toArray(new LineDBImpl[0]));// Will be examined in return to SMProvisioningManager.
        } else {
          throw new SMProvException("After modifying tunnel, service does not exist in CPc for id: " + serviceId);
        }
      } else {
        throw new SMProvException("Service does not exist in CPc for id: " + serviceId);
      }
    }
  }

  private ServiceModifyParameters getServiceModifyParametersForRestoration(ServiceOuterClass.Service niService, ModifyOCSData data) {
    ServiceOuterClass.Restoration restoration = niService.getConfiguration().getRestoration();
    LineDBImpl[] restorationRoute = data.getRestorationRoute();
    Integer restorationMode = data.getRestorationMode();
    Integer restorationType = data.getRestorationType();
    Integer reversionMode = data.getReversionMode();
    return new ServiceModifyParameters.Builder()
            .serviceId(niService.getId())
            .restorationRoute(restorationRoute == null ? null : List.of(data.getRestorationRoute()))
            .restorationType(restorationMode == null ? ServiceEnumMaps.mapDefinitionRestorationMode(restoration.getRestorationType()).getId() : restorationMode)
            .restorationMode(restorationType == null ? ServiceEnumMaps.mapDefinitionRestorationType(restoration.getRestorationMode()).getId() : restorationType)
            .reversionType(reversionMode == null ? ServiceEnumMaps.mapDefinitionReversionMode(restoration.getReversionType()).getId() : reversionMode)
            .build();
  }

  /**
   * Translate PathOuterClass.Path's List<PathOuterClass.PathElement> to List<LineDBImpl>
   */
  private List<LineDBImpl> restorationPathToRoute(PathOuterClass.Path restorationPath) {
    List<LineDBImpl> lines = new ArrayList<>();
    for (PathOuterClass.PathElement pel : restorationPath.getPathElementList()) {
      boolean isLink = pel.hasLink();
      LOGGER.info("    Restoration pathElement {}", isLink
              ? "link: " + pel.getLink().getUnnumbered().getRouter().getIp() + " , " + pel.getLink().getUnnumbered().getInterface().getId().getName()
              : "node: " + pel.getTerminationPoint().getNode());
      if (isLink) { // skip the end nodes
        LineDBImpl line = getLineByEndpointLabelAndNeId(pel.getLink().getUnnumbered().getInterface().getId().getName(),
                            Integer.parseInt(pel.getLink().getUnnumbered().getRouter().getIp()));
        if (line == null)
          LOGGER.error("Line not found in database to match Restoration pathElement's router.ip and interface.id.name:\n{}", pel);
        else if (!lines.contains(line))
          lines.add(line);
      }
    }
    if (LOGGER.isInfoEnabled()) {
      LOGGER.info("{} lines to save as Restoration route", lines.size());
      lines.forEach(line -> LOGGER.info("    {}", line));
    }
    return lines;
  }

  @Override
  public boolean checkIfTunnelIsProvisioned(OpticalChannelService ocs) throws SMProvException {
    return niTunnelRestHandler.checkIfTunnelIsProvisioned(ocs.getNiServiceId());
  }

  @Override
  public void equalizeService(TransportService service, boolean monitorOnly, String name, MessageManager messageManager, ServiceDiagnosticsMessages diagnosticsMessages)
          throws SMProvException {
    if (!monitorOnly) {
      serviceMessageManager.addMessageUserAware(MessageManager.Info, name, diagnosticsMessages.getServiceEqualizationStarted());
      try {
        if (service.isOChConnection() && service instanceof AbstractConnectionDBImpl connectionDB && !connectionDB.isSpurLink()) {
          equalizeService((OpticalChannelService) service);
        } else if (service instanceof SubChConnectionDBImpl connectionDB) {
          List<OChConnectionDBImpl> ochs = connectionDB.getAllContainingOChConnectionsDBs().stream().filter(ocs -> !(ocs.isSpurLink())).toList();
          for (OChConnectionDBImpl och : ochs) {
            equalizeService(och);
          }
        }
      } catch (SMProvException e) {
        serviceMessageManager.addMessageUserAware(MessageManager.Error, name, diagnosticsMessages.getServiceEqualizationFailed());
        throw e;
      }
      serviceMessageManager.addMessageUserAware(MessageManager.Ok, name, diagnosticsMessages.getServiceEqualizationCompleted());
    }
  }

  private void equalizeService(OpticalChannelService ocs) throws SMProvException {
    niTunnelRestHandler.equalizeService(ocs);
  }

  @Override
  public void removeDependenciesBetweenProtectedTunnels(String serviceName, List<OpticalChannelService> opticalChannelServices) {
    //Nothing to do
  }

  @Override
  public boolean hasDependenciesBetweenProtectedTunnels(OpticalChannelService ocs) {
    return false;
  }

  @Override
  public List<List<RoutePreview>> previewRoute(SMProvParams provParams, SMProvisioningManager.OCSListContainer ocsLists) throws SMProvException {
    List<List<RoutePreview>> routePreview = new ArrayList<>();
    List<OpticalChannelService> opticalChannelServices = new ArrayList<>(ocsLists.ocsList());
    OpticalChannelService workOCS = opticalChannelServices.get(0);
    boolean excludePath = isExcludePath(provParams, opticalChannelServices);
    provParams.setAutoXRO(excludePath);

    for (int i = 0; i < opticalChannelServices.size(); i++) {

      if((provParams.getCccpProtectionOCS() != null) && (provParams.getCccpProtectionOCS().ordinal() != i)) {
        continue;
      }

      OpticalChannelService ocs = opticalChannelServices.get(i);
      if (!checkTunnelAndHandleMultilayerADM(ocs, ocs != workOCS, provParams.isAutoXRO(), provParams, ocsLists.workList(), ocsLists.protList())) {
        RoutePreview route = new RoutePreview(ocs, null, null, ocs != workOCS ? RoutePreview.RouteType.PROTECTION : RoutePreview.RouteType.WORKING);
        routePreview.add(List.of(route));
        excludePath = false;
        continue;
      }
      OCSProcessingParameters parameters = createOcsProcParameters(ocs, provParams, new HashMap<>(), false);
      String bundleId = sendNiPreComputation(provParams, parameters, ocs != workOCS);
      List<BundleOuterClass.ComputedPath> paths = BundleCache.INSTANCE.getPreComputedPaths(bundleId);
      for (BundleOuterClass.ComputedPath path : paths) {
        List<RoutePreview> preview = new ComputedPathToRoutePreviewMapper(ocs, path).getRoutePreview();
        routePreview.add(preview);
        if (excludePath) {
          if (!paths.isEmpty()) {
            provParams.excludeOCSPath(ocs.getStartNEID(), ocs.getPeerNEID(), getLines(preview.get(0)));
          }
          excludePath = false;
        }
      }
    }
    return RoutePreview.removeDuplicates(routePreview);
  }

  private String sendNiPreComputation(SMProvParams provParams, OCSProcessingParameters parameters, boolean isProtectionOCS) throws SMProvException {
    String bundleId = niTunnelRestHandler.preComputePath(provParams, parameters);
    pafReader.addCacheEntry(bundleId, parameters, isProtectionOCS);
    try {
      niTunnelRestHandler.retryForNiResponse(bundleId, BundleCache.BundleCacheActions.PRE_COMPUTATION);
    } finally {
      pafReader.updateCacheKey(bundleId, provParams.getServiceName());
    }
    return bundleId;
  }

  @Override
  protected OCSProcessingParameters createParameterObject() {
    return new NIOCSProcessingParameters();
  }

  @Override
  public OpticalChannelService createSpurLinkOCS(SMProvParams provParams) throws SMProvException {
    OpticalChannelService ocs = null;
    boolean exists = true;
    try {
      ocs = smServiceHelper.getCandidateSpurLinkOCS(provParams).orElseThrow(() -> new SMProvException("Cannot create Spur Link OCS"));
      OpticalChannelService existingOcs = smServiceHelper.checkForExistingSpurLinkOCSs(ocs).orElse(null);
      boolean shouldNotifyOCSCreated=false;
      if (existingOcs == null) {
        exists = false;
        ocs = smServiceHelper.createSpurLinkOCSIfNotExists(ocs, provParams);
        shouldNotifyOCSCreated = true;
      }
      provisionNetworkAndClientPortsOfSpurLink(provParams);
      smServiceHelper.updateConnectionsForSpurLink(ocs);
      if(shouldNotifyOCSCreated){
        smServiceHelper.notifyOCSCreated((AbstractConnectionDBImpl) ocs);
      }
    } catch (SMValidationException | SMProvException e) {
      if (!exists) {
        smProvisioningManager.cleanUpSpurLinkOCS(ocs);
      }
      throw new SMProvException(e.getMessage(), e);
    }
    return ocs;
  }

  private void provisionNetworkAndClientPortsOfSpurLink(SMProvParams provParams) throws SMProvException {
    TerminationPointFSP_R7DBImpl startPtp = provParams.getStart().getWorkNetPtp();
    String serviceName = smServiceHelper.getServiceName(provParams);
    if (startPtp == null) {
      startPtp = provParams.getStart().getWorkClientPtp();
      provisionClientPort(serviceName, startPtp, provParams.getStart().getClientPortProps());
    } else {
      provisionNetworkPort(serviceName, startPtp, provParams.getStart().getNetworkPortProps());
    }

    TerminationPointFSP_R7DBImpl endPtp = provParams.getPeer().getWorkNetPtp();
    if (endPtp == null) {
      endPtp = provParams.getPeer().getWorkClientPtp();
      provisionClientPort(serviceName, endPtp, provParams.getPeer().getClientPortProps());
    } else {
      provisionNetworkPort(serviceName, endPtp, provParams.getPeer().getNetworkPortProps());
    }
  }

  private void provisionClientPort(String serviceName, TerminationPointFSP_R7DBImpl ptp, ProvFSPR7Properties properties)
          throws SMProvException {
    String msg = "Provisioning of the client port " + ptp.getAidString();
    serviceMessageManager.addMessageUserAware(MessageManager.Info, serviceName, msg);
    LOGGER.info(msg);

    provisionPort(ptp, properties);
    msg += " completed";
    serviceMessageManager.addMessageUserAware(MessageManager.Info, serviceName, msg);
    LOGGER.info(msg);
  }

  private void provisionNetworkPort(String serviceName, TerminationPointFSP_R7DBImpl ptp, ProvFSPR7Properties properties)
          throws SMProvException {
    String msg = "Provisioning of the network port " + ptp.getAidString();
    serviceMessageManager.addMessageUserAware(MessageManager.Info, serviceName, msg);
    LOGGER.info(msg);

    provisionPort(ptp, properties);
    msg += " completed";
    serviceMessageManager.addMessageUserAware(MessageManager.Info, serviceName, msg);
    LOGGER.info(msg);
  }

  private void provisionPort(TerminationPointFSP_R7DBImpl ptp, ProvFSPR7Properties properties) throws SMProvException {
    NetworkElement networkElement = getNetworkElement(ptp.getNeID());
    EntityIndex entityIndex = ptp.getEntityIndexOfRelatedEntity();
    try {
      SNMPCtrlFSP_R7DS snmpCtrl = (SNMPCtrlFSP_R7DS) networkElement.getSNMPCtrl();
      snmpCtrl.provisionClientPort(entityIndex, properties, null);
    } catch (SNMPCommFailure snmpCommFailure) {
      throw new SMProvException(snmpCommFailure.getErrMessage(), snmpCommFailure);
    }
    pollingUtils.discoverFacilitiesForEntityAtNE(ptp.getContainingModule());
  }

  @Override
  public void deleteSpurLinkOCS(OpticalChannelService ocs) throws SMProvException {
    if (ocs.isProvisionMode()) {
      deprovisionNetworkAndClientPortsOfSpurLink(ocs);
    }
    super.deleteSpurLinkOCS(ocs);

    List<ManagedObjectDBImpl> ports = new ArrayList<>();
    ports.add(ocs.getStartPortOrMO());
    ports.add(ocs.getPeerPortOrMO());
    runDeleteEquipmentPolling(ports);

    List<ModuleDBImpl> modules = new ArrayList<>();
    modules.add(ocs.getStartModule());
    modules.add(ocs.getPeerModule());
    resyncMLModules(modules);
  }

  private void runDeleteEquipmentPolling(List<ManagedObjectDBImpl> ports) {
    ports.stream()
            .filter(Objects::nonNull)
            .forEach(p -> PF.getPollingService().inline(
                    p.getNeID(), PollingType.DELETE_EQUIPMENT,
                    new DeleteEntityPolling(p.getEntityIndex())));
  }

  private void resyncMLModules(List<ModuleDBImpl> ports) {
    // after service creation or deletion, for openfabric+ card, the ml layers are not refreshed
    // resync the module in ML modeling, do it for all F8 cards for now
    ports.stream()
            .filter(Objects::nonNull)
            .forEach(p -> mlTaskExecutorService.submitTask(new ResyncModuleTask(
                    p.getNeID(), p.getId(), MLTopologyElementDAO.getInstance(),
                    MLTopologyMoFacade.getInstance(), MLTopologyResyncFacade.getInstance())));
  }

  private void deprovisionNetworkAndClientPortsOfSpurLink(OpticalChannelService ocs) throws SMProvException {
    NetworkElement startNe = getNetworkElement(ocs.getStartNEID());
    NetworkElement peerNe = getNetworkElement(ocs.getPeerNEID());
    PortFSP_R7DBImpl startPort = getPortFromTerminationPoint(ocs.getStartPTP());
    PortFSP_R7DBImpl peerPort = getPortFromTerminationPoint(ocs.getPeerPTP());
    try {
      new ProvisioningUtils().deprovisionSpurLinkPorts(startNe, startPort, peerNe, peerPort);
    } catch (SNMPCommFailure snmpCommFailure) {
      throw new SMProvException(snmpCommFailure.getErrMessage(), snmpCommFailure);
    }
  }

  private NetworkElement getNetworkElement(int neId) throws SMProvException {
    try {
      return neHdlr.getNetworkElement(neId);
    } catch (NoSuchNetworkElementException e) {
      throw new SMProvException(e.getMessage(), e);
    }
  }

  private PortFSP_R7DBImpl getPortFromTerminationPoint(TerminationPointFSP_R7DBImpl tp) throws SMProvException {
    try {
      return EntityDAO.getByIndex(tp.getNeID(), tp.getEntityIndexOfRelatedEntity(), PortFSP_R7DBImpl.class);
    } catch (NoSuchEntityException e) {
      throw new SMProvException(e.getMessage(), e);
    }
  }
}
