/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: vinodr
 */

package com.adva.nlms.mediation.sm.serviceintent;

import com.adva.common.model.template.Group;
import com.adva.common.model.template.Keyword;
import com.adva.common.model.template.Parameter;
import com.adva.common.model.template.ParameterGroup;
import com.adva.nlms.common.config.netypes.NEType;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.config.netypes.NeTypeString;
import com.adva.apps.sm.ServiceLayer;
import com.adva.nlms.common.snmp.MIBFSP_R7Enums;
import com.adva.nlms.common.visual.model.GraphModel;
import com.adva.nlms.common.yp.Parameters;
import com.adva.nlms.mediation.bean.provider.api.BeanProvider;
import com.adva.nlms.mediation.common.mltopologymodel.enums.ServiceIntentParamKeyTypeWdm;
import com.adva.nlms.mediation.common.serviceProvisioning.ProvFSPR7Properties;
import com.adva.nlms.mediation.common.serviceProvisioning.SPProperties;
import com.adva.nlms.mediation.config.EntityDBImpl;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementHdlrLocal;
import com.adva.nlms.mediation.config.NoSuchNetworkElementException;
import com.adva.nlms.mediation.mltopologymodel.service.intent.ServiceIntentDAO;
import com.adva.nlms.mediation.sm.SMServiceHelper;
import com.adva.nlms.mediation.sm.ServiceManagerBuilder;
import com.adva.nlms.mediation.sm.ServiceManagerCtrlImpl;
import com.adva.nlms.mediation.sm.ServiceManagerHelper;
import com.adva.nlms.mediation.sm.analyzer.PAFReader;
import com.adva.nlms.mediation.sm.exception.SMException;
import com.adva.nlms.mediation.sm.exception.SMProvisioningException;
import com.adva.nlms.mediation.sm.model.OChConnectionDBImpl;
import com.adva.nlms.mediation.sm.prov.Endpoint;
import com.adva.nlms.mediation.sm.prov.EndpointF8;
import com.adva.nlms.mediation.sm.prov.OpticalChannelService;
import com.adva.nlms.mediation.sm.prov.SMProvException;
import com.adva.nlms.mediation.sm.prov.SMProvParams;
import com.adva.nlms.mediation.sm.prov.SMProvParamsBuilder;
import com.adva.nlms.mediation.sm.prov.SMProvParamsF8;
import com.adva.nlms.mediation.sm.prov.SMProvParamsMixed;
import com.adva.nlms.mediation.sm.prov.SMProvisioningManager;
import com.adva.nlms.mediation.sm.prov.cp.OCSProcessingParameters;
import com.adva.nlms.mediation.sm.prov.cp.RoutePreview;
import com.adva.nlms.mediation.sm.prov.ni.BundleCache;
import com.adva.nlms.mediation.sm.prov.ni.NIOCSProcessingParameters;
import com.adva.nlms.mediation.sm.prov.ni.rest.NITunnelRestHandler;
import com.adva.nlms.mediation.sm.prov.ni.servicecreation.ComputedPathToRoutePreviewMapper;
import com.adva.nlms.mediation.sm.topology.ServiceTopologyProvider;
import ni.proto.external.services.bundle.BundleOuterClass;
import ni.proto.external.services.service.ServiceOuterClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.function.BiPredicate;

@Component
public class ConnectivityServiceNIOperationsManager {

  private static final Logger log = LoggerFactory.getLogger(ConnectivityServiceNIOperationsManager.class);
  private static final BiPredicate<String, String> areBothF8Nodes = (srcNode, dstNode) -> NeTypeString.F8.equals(srcNode) && NeTypeString.F8.equals(dstNode);

  private final NITunnelRestHandler niTunnelRestHandler;
  private final ServiceIntentDAO serviceIntentDAO;
  private SMProvParamsBuilder smProvParamsBuilder;
  private PAFReader pafReader;
  private SMServiceHelper smServiceHelper;

  @Autowired
  public ConnectivityServiceNIOperationsManager(NITunnelRestHandler niTunnelRestHandler, ServiceIntentDAO serviceIntentDAO, SMProvParamsBuilder smProvParamsBuilder, PAFReader pafReader, ServiceManagerCtrlImpl serviceManagerCtrl) {
    this.niTunnelRestHandler = niTunnelRestHandler;
    this.serviceIntentDAO = serviceIntentDAO;
    this.smProvParamsBuilder = smProvParamsBuilder;
    this.pafReader = pafReader;
    this.smServiceHelper = serviceManagerCtrl.getSMServiceHelper();
  }

  /**
   * @param isOCS
   * @param provParams provisioning parameters got from wizard
   * @return NI Service ID
   * @throws SMProvException if bundle request fails
   */
  public String createTunnel(String serviceName, SMProvParams provParams, int serviceIntentId, boolean isECHF8, boolean isOCS) throws SMException, NoSuchNetworkElementException {
    NIOCSProcessingParameters parameters;
    if(isECHF8){
      parameters = createOCSParametersECH(provParams, serviceName);
    } else if (isOCS){
      parameters = createOCSParameters(provParams, serviceName);
    } else {
      parameters = null;
    }
    pafReader.removeCacheEntry(provParams.getServiceName());
    String bundleId;
    if (provParams instanceof SMProvParamsF8)
      bundleId =  niTunnelRestHandler.sendNiRequest(niTunnelRestHandler.populateCreateBundleF8(provParams, parameters, isECHF8));
    else
      bundleId =  niTunnelRestHandler.sendNiRequest(niTunnelRestHandler.populateCreateBundleMixed(provParams, parameters, isECHF8));
    BundleCache.INSTANCE.setBundleIdsServiceLabels(bundleId, serviceName);
    retryForNiResponse(bundleId, BundleCache.BundleCacheActions.CREATION, serviceIntentId);
    return BundleCache.INSTANCE.getServiceId(bundleId);
  }

  public void equalizeService(String niServiceId, int serviceIntentId) throws SMProvException {
    String bundleId = niTunnelRestHandler.sendNiRequest(niTunnelRestHandler.populateEqualizeConnectionF8(niServiceId));
    retryForNiResponse(bundleId, BundleCache.BundleCacheActions.EQUALIZATION, serviceIntentId);
  }

  public void retryForNiResponse(String bundleId, BundleCache.BundleCacheActions action, int serviceIntentId) throws SMProvException {
    try {
      log.info("Storing ServiceIntentParamKeyTypeWdm.IS_NI_OPERATION_IN_PROGRESS param in serviceIntent={}", serviceIntentId);
      serviceIntentDAO.addOrUpdateParam(serviceIntentId, ServiceIntentParamKeyTypeWdm.IS_NI_OPERATION_IN_PROGRESS, Boolean.TRUE);
      log.info("{} request with bundle id: {}", action, bundleId);
      BundleCache.INSTANCE.retryForNiResponse(bundleId, action);
      log.info("{} returned for bundle id: {}", action, bundleId);
    } catch (SMProvisioningException e) {
      throw new SMProvException(e.getMessage());
    } finally {
      log.info("Deleting ServiceIntentParamKeyTypeWdm.IS_NI_OPERATION_IN_PROGRESS param  in serviceIntent={}", serviceIntentId);
      serviceIntentDAO.removeParam(serviceIntentId, ServiceIntentParamKeyTypeWdm.IS_NI_OPERATION_IN_PROGRESS);
    }
  }

  public ServiceOuterClass.Service getService(String serviceId) throws SMProvException {
    return niTunnelRestHandler.getService(serviceId);
  }

  public void deleteTunnel(String serviceId) throws SMProvException {
    niTunnelRestHandler.deleteTunnel(serviceId);
  }

  public String preComputePath(SMProvParams provParams, OCSProcessingParameters parameters, Boolean isECH) throws SMProvException {
    if (provParams instanceof SMProvParamsMixed)
      return niTunnelRestHandler.sendNiRequest(niTunnelRestHandler.populatePreComputePathBundleMixed(provParams, parameters, isECH));
    else
      return niTunnelRestHandler.sendNiRequest(niTunnelRestHandler.populatePreComputePathBundleF8(provParams, parameters, isECH));

  }

  public void retryForNiResponse(String bundleId, BundleCache.BundleCacheActions action) throws SMProvException {
    niTunnelRestHandler.retryForNiResponse(bundleId, action);
  }

  public List<GraphModel> initiateRoutePreview(ParameterGroup sd) throws SMException, NoSuchNetworkElementException {
    if (areBothEndsF8(sd))
      return initiateRoutePreviewForF8Nodes(sd);
    else
      return initiateRoutePreviewForMixedNodes(sd);
  }

  private boolean areBothEndsF8(ParameterGroup sd) {
    String srcNodeProductType = sd.getParameter(Keyword.PRODUCT_TYPE, Group.SOURCE_NODE).getValue();
    String dstNodeProductType = sd.getParameter(Keyword.PRODUCT_TYPE, Group.DESTINATION_NODE).getValue();
    return areBothF8Nodes.test(srcNodeProductType, dstNodeProductType);
  }

  private List<GraphModel> initiateRoutePreviewForF8Nodes(ParameterGroup sd) throws SMException, NoSuchNetworkElementException {
    SMProvParams provParams = smProvParamsBuilder.create(NEType.F8).process(true, sd);
    if (provParams.getServiceLayer() == ServiceLayer.ODS){
      return doRoutePreviewCFC(provParams);
    } else {
      return doRoutePreviewWDM(sd, provParams);
    }
  }

  private List<GraphModel> initiateRoutePreviewForMixedNodes(ParameterGroup sd) throws SMException, NoSuchNetworkElementException {
    SMProvParamsMixed provParams = smProvParamsBuilder.createMixed().process(true, sd);
    boolean isNmc = provParams.getServiceLayer() == ServiceLayer.OCS && ServiceManagerHelper.isECHOCSLayer(sd);
    if (isNmc) {
      return doRoutePreviewWDM(sd, provParams);
    }
    return Collections.emptyList();
  }

  private List<GraphModel> doRoutePreviewCFC(SMProvParams provParams) throws SMException {
    String bundleId = preComputePath(provParams, null, false);
    pafReader.addCacheEntry(bundleId, provParams);
    try {
      retryForNiResponse(bundleId, BundleCache.BundleCacheActions.PRE_COMPUTATION);
    } finally {
      pafReader.updateCacheKey(bundleId, provParams.getServiceName());
    }
    List<List<RoutePreview>> routePreviews = new ArrayList<>();
    List<BundleOuterClass.ComputedPath> paths = BundleCache.INSTANCE.getPreComputedPaths(bundleId);
    for (BundleOuterClass.ComputedPath path : paths) {
      List<RoutePreview> routePreview;
      routePreview = new ComputedPathToRoutePreviewMapper(null, path).getRoutePreview();
      routePreviews.add(routePreview);
    }
    return BeanProvider.get().getBean(ServiceTopologyProvider.class).getGraphModel(routePreviews, provParams);
  }

  private List<GraphModel> doRoutePreviewWDM(ParameterGroup sd, SMProvParams provParams) throws SMException, NoSuchNetworkElementException {
    String serviceName = getServiceName(sd, provParams);
    NIOCSProcessingParameters parameters = null;
    boolean isECH = ServiceManagerHelper.isECHOCSLayer(sd);
    if(isECH){
      parameters = createOCSParametersECH(provParams, serviceName);
    }
    else{
      parameters = createOCSParameters(provParams, serviceName);
    }
    String bundleId = preComputePath(provParams, parameters, isECH);

    pafReader.addCacheEntry(bundleId, parameters);
    try {
      retryForNiResponse(bundleId, BundleCache.BundleCacheActions.PRE_COMPUTATION);
    } finally {
      pafReader.updateCacheKey(bundleId, provParams.getServiceName());
    }

    List<List<RoutePreview>> routePreviews = new ArrayList<>();
    List<BundleOuterClass.ComputedPath> paths = BundleCache.INSTANCE.getPreComputedPaths(bundleId);
    for (BundleOuterClass.ComputedPath path : paths) {
      List<RoutePreview> routePreview = new ComputedPathToRoutePreviewMapper(parameters.getOcs(), path).getRoutePreview();
      routePreviews.add(routePreview);
    }

    return BeanProvider.get().getBean(ServiceTopologyProvider.class).getGraphModel(routePreviews, provParams);
  }

  private String getServiceName(ParameterGroup sd,SMProvParams provParams){
    String serviceName = provParams.getServiceName();
    Parameter serviceConnectivityName = sd.getParameter(Keyword.SERVICE_CONNECTIVITY_NAME);
    if (serviceConnectivityName != null && serviceConnectivityName.getValue() != null && !serviceConnectivityName.getValue().isEmpty()) {
      serviceName = sd.getParameter(Keyword.SERVICE_CONNECTIVITY_NAME).getValue();
    }
    return serviceName;
  }

  public NIOCSProcessingParameters createOCSParametersECH(SMProvParams provParams, String serviceName) throws SMException {
    NIOCSProcessingParameters parameters = new NIOCSProcessingParameters();

    parameters.setServiceName(serviceName);
    OChConnectionDBImpl ocs = new OChConnectionDBImpl();
    ocs.setName(ServiceManagerBuilder.getName(ServiceLayer.OCS));
    parameters.setOcs(ocs);

    parameters.setStartNe(provParams.getStart().getNetworkElement());
    parameters.setPeerNe(provParams.getPeer().getNetworkElement());

    SPProperties ocsProperties = new SPProperties();

    if (provParams.getStart().isProvisionExternalChannel()) {
      ocsProperties.set(Parameters.Parameter.FROM_AID.getKeyword(), getWorkClientAid(provParams.getStart()));
      if (provParams.getPeer().getExternalChannelDTO() != null)
        ocsProperties.set(Parameters.Parameter.TO_AID.getKeyword(), provParams.getPeer().getExternalChannelDTO().getAidString());  // testing when ECH is already created
      else
        ocsProperties.set(Parameters.Parameter.TO_AID.getKeyword(), getWorkClientAid(provParams.getPeer()));

    } else {
      ocsProperties.set(Parameters.Parameter.FROM_AID.getKeyword(), getWorkNetAid(provParams.getStart()));
      ocsProperties.set(Parameters.Parameter.TO_AID.getKeyword(), getWorkNetAid(provParams.getPeer()));

    }

    parameters.setOcsProperties(ocsProperties);

    return parameters;
  }

  public NIOCSProcessingParameters createOCSParameters(SMProvParams provParams, String serviceName) throws SMException, NoSuchNetworkElementException {
    NIOCSProcessingParameters parameters = new NIOCSProcessingParameters();

    parameters.setServiceName(serviceName);
    SMProvisioningManager.OCSListContainer ocsLists = createOcsListsForRoutePreview(provParams);
    OChConnectionDBImpl ocs = (OChConnectionDBImpl) ocsLists.ocsList().get(0);
    parameters.setOcs(ocs);
    parameters.setStartNe(provParams.getStart().getNetworkElement());
    parameters.setPeerNe(provParams.getPeer().getNetworkElement());

    SPProperties ocsProperties = new SPProperties();
    if (provParams.getStart().isProvisionExternalChannel()) {
      ocsProperties.set(Parameters.Parameter.FROM_AID.getKeyword(), getWorkClientAid(provParams.getStart()));
      ocsProperties.set(Parameters.Parameter.TO_AID.getKeyword(), getWorkClientAid(provParams.getPeer()));
    } else {
      ocsProperties.set(Parameters.Parameter.FROM_AID.getKeyword(), getWorkNetAid(provParams.getStart()));
      ocsProperties.set(Parameters.Parameter.TO_AID.getKeyword(), getWorkNetAid(provParams.getPeer()));
    }
    parameters.setOcsProperties(ocsProperties);
    ProvFSPR7Properties filtered = ((EndpointF8)provParams.getStart()).getWorkNetPtpF8()!=null?provParams.getStart().getNetworkPortProps():provParams.getStart().getClientPortProps();
    ProvFSPR7Properties filteredPeerParameters = ((EndpointF8)provParams.getPeer()).getWorkNetPtpF8()!=null?provParams.getPeer().getNetworkPortProps():provParams.getPeer().getClientPortProps();
    Object termStart = filtered.get(Parameters.Parameter.TERM.getKeyword());
    Object termPeer = filteredPeerParameters.get(Parameters.Parameter.TERM.getKeyword());
    if (!Objects.equals(termStart, termPeer)) {
      filtered.set(Parameters.Parameter.FEND_TERM.getKeyword(), termPeer);
    }
    mapRestorationParameters(provParams, ocs);
    NetworkElementHdlrLocal neHdlr= ((SMProvParamsF8)provParams).getServiceManagerCtrl().getNetworkElementHdlr();
    NetworkElement[] nes = new NetworkElement[]{neHdlr.getNetworkElement(ocs.getStartNEID()), neHdlr.getNetworkElement(ocs.getPeerNEID())};
    int mibVersion = nes[0].getPersistenceHelper().getMIBVariantFromDB();
    if (provParams.getSetPointDelta() != null && mibVersion >= MIBFSP_R7Enums.MibVariant.VER_1711) {
      ocsProperties.set(Parameters.Parameter.OPTSET_DEV.getKeyword(), provParams.getSetPointDelta());
    }
    parameters.setFilteredPeerParameters(filteredPeerParameters);
    parameters.setFilteredStartParameters(filtered);
    parameters.setModify(false);
    parameters.setWorkingIntermediateEntities(provParams.getWorkingIntermediateEntities());

    if (((EndpointF8) provParams.getStart()).getWorkNetPtpF8() != null) {
      parameters.setStartPortParamsMap(provParams.getStart().getNetworkPortProps());
      parameters.setPeerPortParamsMap(provParams.getPeer().getNetworkPortProps());
    }
    return parameters;
  }

  private String getWorkClientAid(Endpoint end) throws SMProvException
  {
    EntityDBImpl entity = end instanceof EndpointF8 endpointF8 ? endpointF8.getWorkClientPtpF8() : end.getWorkClientPtp();
    if (entity == null) {
      throw new SMProvException("Missing working network PTP for " + end);
    }
    return entity.getAidString();
  }

  private String getWorkNetAid(Endpoint end) throws SMProvException {
    EntityDBImpl entity = end instanceof EndpointF8 endpointF8 ? endpointF8.getWorkNetPtpF8() : end.getWorkNetPtp();
    if (entity == null) {
      throw new SMProvException("Missing working network PTP for " + end);
    }
    return entity.getAidString();
  }

  private SMProvisioningManager.OCSListContainer createOcsListsForRoutePreview(SMProvParams provParams) throws SMException {
    List<OpticalChannelService> ocsList;
    List<OpticalChannelService> workList = null;
    List<OpticalChannelService> protList = null;

    if ((provParams.isMultiLayerODU() || provParams.isMultiLayerADM()) && provParams.getWorkOCSList() != null && !provParams.getWorkOCSList().isEmpty()) {
      workList = provParams.getWorkOCSList();
      if ((protList = provParams.getProtOCSList()) == null) {
        protList = new ArrayList<>();
      }
      ocsList = new ArrayList<>();

    } else {
      if(provParams.getStart().getNetworkElement().getNetworkElementType() == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP3000C)
        ocsList = smServiceHelper.getOpticalChannelServicesF8((SMProvParamsF8) provParams);
      else
        ocsList = smServiceHelper.getOpticalChannelServices(provParams);

      if (provParams.isMultiLayerADM()) {
        OChConnectionDBImpl ocs = (OChConnectionDBImpl) ocsList.get(0);
        if (!provParams.isExtendedOCS()) {
          ocs.setName(provParams.getServiceName());
          if (provParams.getServiceGroup() != null) {
            ocs.setServiceGroup(provParams.getServiceGroup());
          }
        }
        workList = new LinkedList<>();
        workList.add(ocs);
      }
    }
    return new SMProvisioningManager.OCSListContainer(ocsList, workList, protList);
  }

  protected final void mapRestorationParameters(SMProvParams provParams, OpticalChannelService ocs) {
    if (provParams.getRestorationType() != null) {
      ocs.setRestoreType(provParams.getRestorationType().getId());
    }
    if (provParams.getRestorationMode() != null) {
      ocs.setRestorationMode(provParams.getRestorationMode().getId());
    }
    if (provParams.getReversionMode() != null) {
      ocs.setReversionMode(provParams.getReversionMode().getId());
    }
    if (provParams.getStart() != null) {
      ocs.setStartRestorationChannel(provParams.getStart().getRestorationChannel());
    }
    if (provParams.getPeer() != null) {
      ocs.setPeerRestorationChannel(provParams.getPeer().getRestorationChannel());
    }
  }

}
