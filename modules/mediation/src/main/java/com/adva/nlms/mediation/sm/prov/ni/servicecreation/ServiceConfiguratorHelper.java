/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: spyrosm
 */

package com.adva.nlms.mediation.sm.prov.ni.servicecreation;

import com.adva.nlms.common.mib.Dictionary;
import com.adva.nlms.common.yp.Parameters;
import com.adva.nlms.common.yp.db.CardType;
import com.adva.nlms.mediation.common.serviceProvisioning.ProvFSPR7Properties;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.sm.prov.Endpoint;
import com.adva.nlms.mediation.sm.prov.EndpointF8;
import com.adva.nlms.mediation.sm.prov.SMProvParams;
import com.adva.nlms.mediation.sm.prov.SMProvParamsF8;
import com.adva.nlms.mediation.sm.prov.SMProvParamsMixed;
import com.adva.nlms.mediation.sm.prov.cp.OCSProcessingParameters;
import com.adva.nlms.mediation.sm.prov.ni.ServiceEnumMaps;
import ni.proto.external.common.signal_description.EqualizationParams;
import ni.proto.external.common.signal_description.OptionalDouble;
import ni.proto.external.common.signal_description.PvParams;
import ni.proto.external.services.service.ServiceOuterClass;
import ni.proto.inet.InterfaceId;

import java.util.List;

enum ServiceConfiguratorHelper {
  INSTANCE;

  ServiceOuterClass.Edge getSourceEdge(OCSProcessingParameters parameters) {
    return getEdge(parameters.getStartNe(), (String) parameters.getOcsProperties().get(Parameters.Parameter.FROM_AID.getKeyword()));
  }

  ServiceOuterClass.Edge getDestinationEdge(OCSProcessingParameters parameters) {
    return getEdge(parameters.getPeerNe(), (String) parameters.getOcsProperties().get(Parameters.Parameter.TO_AID.getKeyword()));
  }

  private ServiceOuterClass.Edge getEdge(NetworkElement ne, String aid) {
    String node = ne.neDBImpl().getNiSync().getNiId();
    return getEdge(node, aid);
  }

  ServiceOuterClass.Edge getEdge(String node, String aid) {
    InterfaceId interfaceId = InterfaceId.newBuilder().setName(aid).build();
    return ServiceOuterClass.Edge.newBuilder().setNode(node).setDevice(interfaceId).build();
  }

  ProvFSPR7Properties getNetworkPortProps(SMProvParams smProvParams) {
      return smProvParams.getStart().getWorkNetPtp() != null ? smProvParams.getStart().getNetworkPortProps() : smProvParams.getPeer().getNetworkPortProps();
  }

  ProvFSPR7Properties getNetworkPortPropsF8(SMProvParamsF8 smProvParams) {
    return smProvParams.getStart().getWorkNetPtpF8() != null ? smProvParams.getStart().getNetworkPortProps() : smProvParams.getPeer().getNetworkPortProps();
  }

  PvParams getPvParams(SMProvParams smProvParams) {
    ProvFSPR7Properties properties = smProvParams instanceof SMProvParamsF8? getPropertiesF8(smProvParams.getStart()) : getProperties(smProvParams.getStart());
    PvParams.FecType fecType = ServiceEnumMaps.mapFec((String) properties.get(Parameters.Parameter.FEC.getKeyword()));
    PvParams.BitStuffingMode bitStuffingMode = ServiceEnumMaps.mapStuff((String) properties.get(Parameters.Parameter.STUFF.getKeyword()));
    PvParams.TerminationType termination = ServiceEnumMaps.mapTerm((String) properties.get(Parameters.Parameter.TERM.getKeyword()));

    ProvFSPR7Properties peerProperties = smProvParams instanceof SMProvParamsF8? getPropertiesF8(smProvParams.getPeer()) : getProperties(smProvParams.getPeer());
    PvParams.TerminationType farEndTermination = ServiceEnumMaps.mapTerm((String) peerProperties.get(Parameters.Parameter.TERM.getKeyword()));

    PvParams.Builder pvParams = PvParams.newBuilder()
            .setForwardErrorCorrection(fecType)
            .setBitStuffing(bitStuffingMode)
            .setTermination(termination)
            .setFarEndTermination(farEndTermination);
    setPoint(properties, pvParams);
    setFarEndPoint(peerProperties, pvParams);
    if(isNotModulationAndFractionRestricted(smProvParams)){
      setModulationType(properties, pvParams);
      setFraction(properties, pvParams);
    }
    return pvParams.build();
  }

  private static boolean isNotModulationAndFractionRestricted(SMProvParams smProvParams) {
    if (smProvParams instanceof SMProvParamsF8) {
      return true;
    }

    CardType source = CardType.valueOfString(smProvParams.getStart().getWorkCM().getAssignedTypeString());
    CardType dest = CardType.valueOfString(smProvParams.getPeer().getWorkCM().getAssignedTypeString());
    return !isCardTypeRestricted(source) && !isCardTypeRestricted(dest);
  }
  private static boolean isCardTypeRestricted(CardType cardType) {
    List<CardType> restricted = List.of(CardType.MA_2C2C3LT_A, CardType.MA_2C5LT);
    return restricted.contains(cardType);
  }

  PvParams getPvParamsMixed(SMProvParamsMixed smProvParams) {
    ProvFSPR7Properties properties = getPropertiesF8(smProvParams.getStart());
    PvParams.FecType fecType = ServiceEnumMaps.mapFec((String) properties.get(Parameters.Parameter.FEC.getKeyword()));
    PvParams.BitStuffingMode bitStuffingMode = ServiceEnumMaps.mapStuff((String) properties.get(Parameters.Parameter.STUFF.getKeyword()));
    PvParams.TerminationType termination = ServiceEnumMaps.mapTerm((String) properties.get(Parameters.Parameter.TERM.getKeyword()));

    ProvFSPR7Properties peerProperties = getProperties(smProvParams.getPeer());
    PvParams.TerminationType farEndTermination = ServiceEnumMaps.mapTerm((String) peerProperties.get(Parameters.Parameter.TERM.getKeyword()));

    PvParams.Builder pvParams = PvParams.newBuilder()
            .setForwardErrorCorrection(fecType)
            .setBitStuffing(bitStuffingMode)
            .setTermination(termination)
            .setFarEndTermination(farEndTermination);
    setPoint(properties, pvParams);
    setFarEndPoint(peerProperties, pvParams);
    setModulationType(properties, pvParams);
    setFraction(properties, pvParams);
    return pvParams.build();
  }

  private ProvFSPR7Properties getProperties(Endpoint endpoint) {
    return endpoint.getWorkNetPtp() != null ? endpoint.getNetworkPortProps() : endpoint.getClientPortProps();
  }

  private ProvFSPR7Properties getPropertiesF8(Endpoint endpoint) {
    return ((EndpointF8)endpoint).getWorkNetPtpF8() != null ? endpoint.getNetworkPortProps() : endpoint.getClientPortProps();
  }

  private void setPoint(ProvFSPR7Properties properties, PvParams.Builder pvParams) {
    OptionalDouble setPoint = getPointValue(properties);
    if (setPoint != null) {
      pvParams.setSetPoint(setPoint);
    }
  }

  private void setFarEndPoint(ProvFSPR7Properties properties, PvParams.Builder pvParams) {
    OptionalDouble farEndSetPoint = getPointValue(properties);
    if (farEndSetPoint != null) {
      pvParams.setFarEndSetPoint(farEndSetPoint);
    }
  }

  private void setModulationType(ProvFSPR7Properties properties, PvParams.Builder pvParams) {
    PvParams.ModulationType modulationType = ServiceEnumMaps.mapModulation((String) properties.get(Parameters.Parameter.MODULATION.getKeyword()));
    if (modulationType != null) {
      pvParams.setModulation(modulationType);
    }
  }

  /**
   * Prior to Capability Broker, expect the property to be FRACTION's keyword "FRACTION" with value as
   *   an integer String needing adjustment for its decimal: "1234" -> 1.234
   * @param properties ProvFSPR7Properties
   * @return double value of adjusted String, or -1 if property not found
   */
  private double getFractionAdjusted(ProvFSPR7Properties properties) {
    String fraction = (String) properties.get(Parameters.Parameter.FRACTION.getKeyword());
    return fraction != null ? Integer.parseInt(fraction) / 1000d : -1;
  }

  /**
   * With Capability Broker, expect the property to be FRACTION's name "Bits Per Symbol" with value as
   *   a double String already adjusted for its decimal: "1.234" -> 1.234
   * @param properties ProvFSPR7Properties
   * @return double value of String, or -1 if property not found
   */
  private double getFraction(ProvFSPR7Properties properties) {
    String fraction = (String) properties.get(Parameters.Parameter.FRACTION.getName());
    return fraction != null ? Double.parseDouble(fraction) : -1;
  }

  private void setFraction(ProvFSPR7Properties properties, PvParams.Builder pvParams) {
    double fractionDouble = getFraction(properties);
    if (fractionDouble < 0)
      fractionDouble = getFractionAdjusted(properties);
    if (fractionDouble >= 0) {
      OptionalDouble optionalFraction = OptionalDouble.newBuilder().setValue(fractionDouble).setOverrideDefault(true).build();
      pvParams.setFraction(optionalFraction);
    }
  }

  private OptionalDouble getPointValue(ProvFSPR7Properties properties) {
    OptionalDouble setPoint = null;
    String optset = (String) properties.get(Parameters.Parameter.OPTSET.getKeyword());
    if (optset != null) {
      double optsetDouble = Integer.parseInt(optset) / 10d;
      setPoint = OptionalDouble.newBuilder().setValue(optsetDouble).setOverrideDefault(true).build();
    }
    return setPoint;
  }

  EqualizationParams getEqualizationParams(SMProvParams smProvParams) {
    if(smProvParams.getSetPointDelta()!=null) {
      double setPointDelta = (double) (smProvParams.getSetPointDelta()) / 10d;
      OptionalDouble optionalDoubleSetPointDelta = OptionalDouble.newBuilder().setValue(setPointDelta).setOverrideDefault(true).build();
      return EqualizationParams.newBuilder().setSetPointDelta(optionalDoubleSetPointDelta).build();
    }
    return EqualizationParams.newBuilder().build();
  }

  boolean isMFlex(SMProvParams smProvParams) {
    return smProvParams.getStart().getWorkCM().getAssignedEntityType() != null &&
           smProvParams.getPeer().getWorkCM().getAssignedEntityType() != null &&
           smProvParams.getStart().getWorkCM().getAssignedEntityType().getEntityType() == Dictionary.EntityType.MF_M6MDT &&
           smProvParams.getPeer().getWorkCM().getAssignedEntityType().getEntityType() == Dictionary.EntityType.MF_M6MDT;
  }
}
