/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: mateuszkw
 */

package com.adva.nlms.mediation.sm.model;

import com.adva.apps.sm.ServiceLayer;
import com.adva.nlms.common.TopologyNodeType;
import com.adva.nlms.common.property.FNMPropertyConstants;
import com.adva.nlms.common.property.FNMPropertyFactory;
import com.adva.nlms.common.sm.Contract;
import com.adva.nlms.common.sm.Customer;
import com.adva.nlms.common.sm.CustomerGroup;
import com.adva.nlms.common.sm.CustomerServiceGroup;
import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.infrastucture.security.SecurityTools;
import com.adva.nlms.mediation.bean.provider.api.BeanProvider;
import com.adva.nlms.mediation.common.MDObjectAlreadyExistsException;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.common.persistence.MDPersistenceHelper;
import com.adva.nlms.mediation.common.persistence.MDPersistenceManager;
import com.adva.nlms.mediation.common.persistence.MDTransactional;
import com.adva.nlms.mediation.common.persistence.model.PersistentObjectHelper;
import com.adva.nlms.mediation.common.persistence.querybuilder.JPAQueryParam;
import com.adva.nlms.mediation.mltopologymodel.service.intent.implementation.db.ServiceIntentDBImpl;
import com.adva.nlms.mediation.sm.ServiceErrorConstant;
import com.adva.nlms.mediation.sm.exception.SMDuplicateNamesNotAllowedException;
import com.adva.nlms.mediation.sm.prov.SMProvException;
import com.adva.nlms.pd.api.in.serviceintent.PDServiceIntentDataController;
import com.adva.nlms.pd.api.in.serviceintent.dto.PDServiceIntentDTO;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.NoResultException;
import jakarta.persistence.Query;
import jakarta.persistence.TypedQuery;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static com.adva.nlms.mediation.sm.model.CustomerHandlerImpl.UNASSIGNED_CUSTOMER_NAME;

@Component
public class CustomerDAO {
  private static final Logger LOGGER = LogManager.getLogger(CustomerDAO.class);

  private static final String CUSTOMER_NAME_PARAMETER = "customerName";
  private static final String PARENT_ID_PARAMETER = "parentId";
  private static final String PARENT_IDS_PARAMETER = "parentIds";
  private static final String PATTERN_PARAMETER = "pattern";
  private static final String CONTAINER_ID_PARAMETER = "containerId";
  private static final String GROUP_TYPE_PARAMETER = "groupType";
  private static final String IDS_PARAMETER = "ids";

  @MDPersistenceContext
  public CustomerDBImpl getCustomerByNameLike(String name) {
    EntityManager em = MDPersistenceManager.current();
    Query query = em.createNamedQuery(CustomerDBImpl.GET_CUSTOMER_BY_NAME_QUERY_NAME);
    query.setParameter("name", name);

    return (CustomerDBImpl) query.getSingleResult();
  }

  @MDTransactional
  void deleteCustomer(int id) throws MDOperationFailedException {
    try {
      CustomerDBImpl customerDBImpl = getCustomerDBImpl(id);
      if (customerDBImpl == null) {
        return;
      }
      //delete contacts
      for (CustomerContactDBImpl ccDBImpl : new LinkedHashSet<>(customerDBImpl.getContacts())) {
        PersistentObjectHelper.destroy(ccDBImpl);
      }
      //delete customer
      PersistentObjectHelper.destroy(customerDBImpl);
    } catch (SMProvException e) {
      throw new MDOperationFailedException(e.getErrorMessage(), e);
    }
  }

  @MDPersistenceContext
  public CustomerDBImpl getCustomerDBImpl(int id) throws SMProvException {
    CustomerDBImpl db = MDPersistenceHelper.find(CustomerDBImpl.class, id);
    if (db == null) {
      throw new SMProvException("Customer not found for id " + id);
    }
    return db;
  }

  @MDPersistenceContext
  public CustomerDBImpl getCustomerDBImplOrNull(int id) {
    return MDPersistenceHelper.find(CustomerDBImpl.class, id);
  }

  @MDPersistenceContext
  public CustomerGroupDBImpl getCustomerGroupDBImpl(int id) throws SMProvException {
    CustomerGroupDBImpl db = MDPersistenceHelper.find(CustomerGroupDBImpl.class, id);
    if (db == null) {
      throw new SMProvException("Customer group not found for id " + id);
    }
    return db;
  }

  @MDPersistenceContext
  public CustomerGroupDBImpl getCustomerGroupDBImplOrNull(int id) {
    return MDPersistenceHelper.find(CustomerGroupDBImpl.class, id);
  }

  @MDPersistenceContext
  public CustomerGroupDBImpl getCustomerGroupByName(String name) {
    EntityManager em = MDPersistenceManager.current();
    TypedQuery<CustomerGroupDBImpl> query = em.createNamedQuery(CustomerGroupDBImpl.GET_CUSTOMER_GROUP_BY_NAME, CustomerGroupDBImpl.class);
    query.setParameter("name",name);
    var resList = query.getResultList();
    if(resList.size() > 1 && LOGGER.isErrorEnabled()) {
      LOGGER.error("Found more than one customer group named: {}", SecurityTools.replaceCRLFs(name));
    }
    return resList.get(0);
  }

  @MDPersistenceContext
  public CustomerServiceGroupDBImpl getCustomerServiceGroupDBImpl(int id) throws SMProvException {
    CustomerServiceGroupDBImpl db = MDPersistenceHelper.find(CustomerServiceGroupDBImpl.class, id);
    if (db == null) {
      throw new SMProvException("Customer service group not found for id " + id);
    }
    return db;
  }

  @MDPersistenceContext
  CustomerServiceGroupDBImpl findCustomerServiceGroupDBImpl(int id) {
    return MDPersistenceHelper.find(CustomerServiceGroupDBImpl.class, id);
  }

  @MDPersistenceContext
  public CustomerDBImpl getCustomerByName(String name) {
    if(name == null) {
      LOGGER.warn("Customer name is empty.");
      return null;
    }
    EntityManager em = MDPersistenceManager.current();
    TypedQuery<CustomerDBImpl> query = em.createNamedQuery(CustomerDBImpl.GET_DISTINCT_CUSTOMER_BY_NAME, CustomerDBImpl.class);
    query.setParameter(CUSTOMER_NAME_PARAMETER, name);

    List<CustomerDBImpl> result = query.getResultList();
    if (result.size() > 1) {
      LOGGER.error("More than one customer with name '{}' in the DB",
          SecurityTools.replaceCRLFs(name));
      return result.get(0);
    } else if (result.isEmpty()) {
      LOGGER.warn("No existing customer found with the name '{}' in the database",
              SecurityTools.replaceCRLFs(name));
      return null;
    } else {
      return result.get(0);
    }
  }

  @MDPersistenceContext
  public CustomerDBImpl getCustomerByUUID(UUID containerId) {
    TypedQuery<CustomerDBImpl> query;
    EntityManager em = MDPersistenceManager.current();
    query = em.createNamedQuery(CustomerDBImpl.GET_CUSTOMER_BY_UUID, CustomerDBImpl.class);
    query.setParameter(CONTAINER_ID_PARAMETER, containerId);

    List<CustomerDBImpl> result = query.getResultList();

    if (result.size() > 1) {
      LOGGER.error("More than one customer with id '{}' in the DB", containerId);
      return null;
    } else if (result.isEmpty()) {
      LOGGER.warn("Not found customer with id '{}' in the DB", containerId);
      return null;
    } else {
      return result.get(0);
    }
  }

  @MDPersistenceContext
  public ServiceIntentDBImpl getConnectivityServiceByUUID(UUID connectivityId) {
    EntityManager em = MDPersistenceManager.current();
    TypedQuery<ServiceIntentDBImpl> query = em.createNamedQuery(
            CustomerServiceGroupDBImpl.GET_CUSTOMER_SERVICE_GROUP_BY_GROUP_TYPE_AND_UUID, ServiceIntentDBImpl.class);
    query.setParameter(GROUP_TYPE_PARAMETER, CustomerGroupDBImpl.GroupType.CONTRACT.getIntValue());
    query.setParameter(CONTAINER_ID_PARAMETER, connectivityId);

    List<ServiceIntentDBImpl> result = query.getResultList();
    if (result.size() > 1) {
      LOGGER.error("More than one connectivity service with id '{}' in the DB", connectivityId);
      return null;
    } else if (result.isEmpty()) {
      LOGGER.warn("Not found connectivity service with id '{}' in the DB", connectivityId);
      return null;
    } else {
      return result.get(0);
    }
  }

  @MDPersistenceContext
  public CustomerServiceGroupDBImpl getServiceFolderByUUID(UUID containerId) {
    EntityManager em = MDPersistenceManager.current();
    TypedQuery<CustomerServiceGroupDBImpl> query = em.createNamedQuery(
            CustomerServiceGroupDBImpl.GET_CUSTOMER_SERVICE_GROUP_BY_GROUP_TYPE_AND_UUID, CustomerServiceGroupDBImpl.class);
    query.setParameter(GROUP_TYPE_PARAMETER, CustomerGroupDBImpl.GroupType.SERVICE.getIntValue());
    query.setParameter(CONTAINER_ID_PARAMETER, containerId);

    List<CustomerServiceGroupDBImpl> result = query.getResultList();

    if (result.size() > 1) {
      LOGGER.error("More than one service folder with id '{}' in the DB", containerId);
      return null;
    } else if (result.isEmpty()) {
      LOGGER.error("Not found service folder with id '{}' in the DB", containerId);
      return null;
    } else {
      return result.get(0);
    }
  }

  @MDPersistenceContext
  public int getCustomerGroupIdByUUID(UUID containerId) {
    EntityManager em = MDPersistenceManager.current();
    CustomerGroupDBImpl customerGroup = getCustomerGroupDB(containerId, em);
    if (customerGroup == null) {
      throw new EntityNotFoundException("Customer group not found for UUID: " + containerId);
    }
    return customerGroup.getId();
  }

  @MDPersistenceContext
  public CustomerGroupDBImpl getCustomerGroupByUUID(UUID containerId) {
    EntityManager em = MDPersistenceManager.current();
    CustomerGroupDBImpl customerGroup = getCustomerGroupDB(containerId, em);
    if (customerGroup == null) {
      throw new EntityNotFoundException("Customer group not found for UUID: " + containerId);
    }
    return customerGroup;
  }

  @MDPersistenceContext
  List<CustomerDBImpl> getCustomersByNames(List<String> names) {
    try {
      EntityManager em = MDPersistenceManager.current();
      String queryString = "SELECT DISTINCT a FROM CustomerDBImpl a WHERE a.customerName in :customerNames ORDER BY a.id ASC";
      TypedQuery<CustomerDBImpl> query = em.createQuery(queryString, CustomerDBImpl.class);
      query.setParameter("customerNames", names);
      var result= query.getResultList();
      if(result.size() != names.size()) {
        LOGGER.error("Some customers not found by name");
      }
      return result;
    } catch (NoResultException e) {
      //
    }
    return Collections.emptyList();
  }

  @MDPersistenceContext
  Customer[] getAllCustomerObjects() {
    Customer[] customers;
    try {
      EntityManager em = MDPersistenceManager.current();
      String queryString = "SELECT a FROM CustomerDBImpl a";
      TypedQuery<CustomerDBImpl> query = em.createQuery(queryString, CustomerDBImpl.class);
      List<CustomerDBImpl> list = query.getResultList();
      customers = new Customer[list.size()];
      for (int i = 0; i < list.size(); i++) {
        Customer dto = new Customer();
        list.get(i).createDTOFromData(dto);
        customers[i] = dto;
      }

    } catch (Exception e) {
      LOGGER.error(e.getMessage(), e);
      customers = new Customer[0];
    }
    return customers;
  }

  @MDPersistenceContext
  List<Integer> getServiceGroupsForParent(int serviceGroupParentId) throws MDOperationFailedException {
    List<Integer> serviceGroupIDList = new ArrayList<>();
    try {
      Set<CustomerServiceGroupDBImpl> result = getCustomerServiceGroupDBImplsForParent(serviceGroupParentId);
      for (CustomerServiceGroupDBImpl sg : result) {
        serviceGroupIDList.add(sg.getId());
      }
    } catch (Exception e) {
      throw new MDOperationFailedException(e.getMessage(), e);
    }
    return serviceGroupIDList;
  }

  @MDPersistenceContext
  Set<CustomerGroupDBImpl> getCustomerGroupDBImplsForParent(int customerGroupParentId) {
    return MDPersistenceHelper.queryByParam(CustomerGroupDBImpl.class, new JPAQueryParam(PARENT_ID_PARAMETER, customerGroupParentId));
  }

  @MDPersistenceContext
  public Set<CustomerServiceGroupDBImpl> getCustomerServiceGroupDBImplsForParent(int parent) {
    return MDPersistenceHelper.queryByParam(CustomerServiceGroupDBImpl.class, new JPAQueryParam(PARENT_ID_PARAMETER, parent));
  }

  @MDPersistenceContext
  Set<CustomerDBImpl> getCustomerDBImplsForParent(int parentId) {
    return MDPersistenceHelper.queryByParam(CustomerDBImpl.class, new JPAQueryParam("parentGroup.id", parentId));
  }

  @MDPersistenceContext
  public Set<UUID> getCustomerGroupUuidsByIds(Set<Integer> viewIds) {
    EntityManager em = MDPersistenceManager.current();
    return new HashSet<>( em.createNamedQuery(CustomerGroupDBImpl.GET_CUSTOMER_GROUP_UUIDS_BY_IDS, UUID.class)
            .setParameter(IDS_PARAMETER, viewIds)
            .getResultList()
    );
  }

  @MDPersistenceContext
  public Set<UUID> getCustomerUuidsByIds(Set<Integer> viewIds) {
    EntityManager em = MDPersistenceManager.current();
    return new HashSet<>(
            em.createNamedQuery(CustomerDBImpl.GET_CUSTOMER_UUIDS_BY_IDS, UUID.class)
                    .setParameter(IDS_PARAMETER, viewIds)
                    .getResultList()
    );
  }

  @MDPersistenceContext
  private List<Integer> getCustomerGroupsForParent(int customerGroupParentId) throws SMProvException {
    List<Integer> customerGroupIDList = new ArrayList<>();

    try {
      Set<CustomerGroupDBImpl> result = getCustomerGroupDBImplsForParent(customerGroupParentId);
      for (CustomerGroupDBImpl cg : result) {
        customerGroupIDList.add(cg.getId());
      }
    } catch (Exception e) {
      throw new SMProvException(e.getMessage(), e);
    }
    return customerGroupIDList;
  }

  @MDPersistenceContext
  public List<Integer> getCustomerGroupIdsForParentId(int customerGroupParentId) throws SMProvException {
    List<Integer> customerGroupIDList = new ArrayList<>();

    try {
      Set<CustomerGroupDBImpl> result = getCustomerGroupDBImplsForParent(customerGroupParentId);
      for (CustomerGroupDBImpl cg : result) {
        if (cg.isCustomerGroup())
          customerGroupIDList.add(cg.getId());
      }
    } catch (Exception e) {
      throw new SMProvException(e.getMessage(), e);
    }
    return customerGroupIDList;
  }



  @MDPersistenceContext
  private List<Integer> getCustomersForParent(int parentId) throws SMProvException {
    List<Integer> customerIDList = new ArrayList<>();

    try {
      Set<CustomerDBImpl> result = getCustomerDBImplsForParent(parentId);
      for (CustomerDBImpl cust : result) {
        customerIDList.add(cust.getId());
      }
    } catch (Exception e) {
      throw new SMProvException(e.getMessage(), e);
    }
    return customerIDList;
  }

  @MDPersistenceContext
  public Set<String> getAllNamesUnderParent(int id) throws SMProvException {
    //Check if the parent is at lease a Customer group or a Customer
    CustomerGroupDBImpl csg = null;
    try {
      csg = MDPersistenceHelper.getObjectById(CustomerGroupDBImpl.class, id);
    } catch (EntityNotFoundException e) {
      //do nothing
    }
    CustomerDBImpl cust = null;
    try {
      cust = MDPersistenceHelper.getObjectById(CustomerDBImpl.class, id);
    } catch (EntityNotFoundException e) {
      //do nothing
    }
    //If both are null then throw exception
    if ((cust == null) && (csg == null)) {
      throw new SMProvException("Parent ID is not a valid folder or a customer");
    }
    //find all children folders for the group
    Set<String> nameSet = getNameSetOfCustomerGroup(id);
    //find all child customers for the group
    nameSet.addAll((getNameSetOfCustomer(id)));
    return nameSet;
  }

  @MDPersistenceContext
  private Set<String> getNameSetOfCustomerGroup(int id) {
    Set<String> nameSet = new HashSet<>();
    String queryStr = "SELECT a from CustomerGroupDBImpl a WHERE a.parentId = :id";
    EntityManager em = MDPersistenceManager.current();
    TypedQuery<CustomerGroupDBImpl> query = em.createQuery(queryStr, CustomerGroupDBImpl.class);
    query.setParameter("id", id);
    List<CustomerGroupDBImpl> resultList = new ArrayList<>(query.getResultList());
    for (CustomerGroupDBImpl group : resultList) {
      nameSet.add(group.getName());
    }
    return nameSet;
  }

  @MDPersistenceContext
  private Set<String> getNameSetOfCustomer(int id) {
    Set<String> nameSet = new HashSet<>();
    String queryStr = "SELECT a from CustomerDBImpl a WHERE a.parentId = :id";
    EntityManager em = MDPersistenceManager.current();
    TypedQuery<CustomerDBImpl> query = em.createQuery(queryStr, CustomerDBImpl.class);
    query.setParameter("id", id);
    List<CustomerDBImpl> resultList = new ArrayList<>(query.getResultList());
    for (CustomerDBImpl customers : resultList) {
      nameSet.add(customers.getName());
    }
    return nameSet;
  }

  @MDPersistenceContext
  public Set<UUID> getChildContainerIds(UUID parentContainerId) throws SMProvException {
    EntityManager em = MDPersistenceManager.current();

    //Check if the parent is at least a Customer group or a Customer
    CustomerGroupDBImpl csg = getCustomerGroupDB(parentContainerId, em);
    CustomerDBImpl cust = getCustomerDB(parentContainerId, em);

    int parentId;
    //If both are null then throw exception
    if ((cust == null) && (csg == null)) {
      throw new SMProvException("UUID is not one of a valid folder or a customer");
    } else {
      parentId = cust != null ? cust.getId() : csg.getId();
    }

    // Recursively find all children folders for the group
    return findAllChildContainerIds(parentId);
  }

  private static CustomerGroupDBImpl getCustomerGroupDB(UUID containerId, EntityManager em) {
    CustomerGroupDBImpl csg = null;
    TypedQuery<CustomerGroupDBImpl> query = em.createNamedQuery(
            CustomerGroupDBImpl.GET_CUSTOMER_GROUP_BY_UUID, CustomerGroupDBImpl.class);
    query.setParameter("uuid", containerId);

    List<CustomerGroupDBImpl> resultList = query.getResultList();
    if (!resultList.isEmpty()) {
      csg = resultList.get(0);
    }
    return csg;
  }

  private static CustomerDBImpl getCustomerDB(UUID containerId, EntityManager em) {
    CustomerDBImpl cust = null;
    TypedQuery<CustomerDBImpl> query2 = em.createNamedQuery(
            CustomerDBImpl.GET_CUSTOMER_BY_UUID, CustomerDBImpl.class);
    query2.setParameter(CONTAINER_ID_PARAMETER, containerId);

    List<CustomerDBImpl> resultList2 = query2.getResultList();
    if (!resultList2.isEmpty()) {
      cust = resultList2.get(0);
    }
    return cust;
  }

  private Set<UUID> findAllChildContainerIds(int parentId) {
    final EntityManager em = MDPersistenceManager.current();

    String recursiveQueryStr = "WITH RECURSIVE foldertree (id, parentid, \"uuid\") AS (" +
            "SELECT id, parentid, \"uuid\" FROM (" +
            "   SELECT id, parentid, \"uuid\" FROM sm_customer_service_group " +
            "       WHERE jdoclass IN ('CustomerGroupDBImpl', 'CustomerServiceGroupDBImpl')" +
            "   UNION ALL " +
            "   SELECT id, parent_id AS parentid, \"uuid\" FROM sm_customers" +
            ") AS foo where foo.parentid = ? " +
            "UNION ALL " +
            "SELECT rec.id, rec.parentid, rec.\"uuid\" FROM (" +
            "   SELECT id, parentid, \"uuid\" FROM sm_customer_service_group " +
            "       WHERE jdoclass IN ('CustomerGroupDBImpl', 'CustomerServiceGroupDBImpl')" +
            "   UNION ALL " +
            "   SELECT id, parent_id AS parentid, \"uuid\" FROM sm_customers" +
            ") AS rec JOIN foldertree f ON f.id = rec.parentid) " +
            "SELECT * FROM foldertree";

    Query query = em.createNativeQuery(recursiveQueryStr);
    query.setParameter(1, parentId);
    List<Object[]> resultList = query.getResultList();

    Set<UUID> containerIds = new HashSet<>();
    for (Object[] obj : resultList) {
      if (obj[2] instanceof String containerUUID) {
        containerIds.add(UUID.fromString(containerUUID));
      }
    }

    return containerIds;
  }

  @MDTransactional
  CustomerServiceGroupDBImpl persistCustomerServiceGroup(CustomerServiceGroup csg) {
    EntityManager em = MDPersistenceManager.current();
    CustomerServiceGroupDBImpl customerServiceGroupDBImpl = new CustomerServiceGroupDBImpl();
    customerServiceGroupDBImpl.setGroupType(CustomerGroupDBImpl.GroupType.SERVICE.getIntValue());
    createCustomerServiceGroupDBImplFromData(customerServiceGroupDBImpl, csg);
    customerServiceGroupDBImpl.setIsCustomerGroup(false);
    em.persist(customerServiceGroupDBImpl);
    return customerServiceGroupDBImpl;
  }

  private void createCustomerServiceGroupDBImplFromData(CustomerServiceGroupDBImpl customerServiceGroupDBImpl, CustomerServiceGroup csg) {
    customerServiceGroupDBImpl.setParentId(csg.getParentId());
    customerServiceGroupDBImpl.setName(csg.getName());
    if (csg.getCustomerId() > 0) {
      try {
        customerServiceGroupDBImpl.setCustomer(getCustomerDBImpl(csg.getCustomerId()));
      } catch (SMProvException e) {
        LOGGER.debug(e);
      }
    }
    if (csg.getParentGroupId() > 0) {
      try {
        customerServiceGroupDBImpl.setParentGroup(getCustomerServiceGroupDBImpl(csg.getParentGroupId()));

      } catch (SMProvException e) {
        LOGGER.debug(e);
      }
    }
  }

  @MDTransactional
  ContractDBImpl persistContractDb(Contract contract) throws SMProvException {
    EntityManager em = MDPersistenceManager.current();
    ContractDBImpl contractDBImpl = new ContractDBImpl();
    contractDBImpl.setGroupType(CustomerGroupDBImpl.GroupType.CONTRACT.getIntValue());
    if ((contract.getParentGroupId() > 0) && (getCustomerServiceGroupDBImpl(contract.getParentGroupId()).getCustomer() != null)) {
      int customerId = getCustomerServiceGroupDBImpl(contract.getParentGroupId()).getCustomer().getId();
      if (customerId > 0) {
        contract.setCustomerId(customerId);
      }
    }
    createCustomerServiceGroupDBImplFromData(contractDBImpl, contract);
    contractDBImpl.setIsCustomerGroup(false);
    contractDBImpl.setDescription(contract.getDescription());
    em.persist(contractDBImpl);
    if (LOGGER.isDebugEnabled()) {
      LOGGER.info("The contract was created: {}", contractDBImpl.getName());
    }
    if (contract.getRelatedIntent() > 0) {
      contractDBImpl.setRelatedIntent(contract.getRelatedIntent());
    }
    return contractDBImpl;
  }

  @MDPersistenceContext
  boolean isDuplicate(JPAQueryParam... params) {
    Long count = MDPersistenceHelper.getCount(CustomerDBImpl.class, MDPersistenceHelper.DEFAULT_IGNORE_FLAG, params);
    return count > 0;
  }

  @MDTransactional
  CustomerDBImpl modifyCustomer(Customer customer) throws SMProvException {
    CustomerDBImpl customerDBImpl = getCustomerDBImpl(customer.getId());
    if (customerDBImpl == null) {
      return null;
    }
    customerDBImpl.modifyFromData(customer);
    MDPersistenceManager.current().persist(customerDBImpl);
    if (LOGGER.isDebugEnabled()) {
      LOGGER.debug("Customer updated successfully: {}", customerDBImpl.getId());
    }
    return customerDBImpl;
  }

  @MDTransactional
  CustomerGroupDBImpl deleteCustomerGroup(int id) throws SMProvException {
    CustomerGroupDBImpl customerGroupDBImpl = getCustomerGroupDBImpl(id);
    if (TopologyNodeType.SERVICES_ROOT.getName().equalsIgnoreCase(customerGroupDBImpl.getName())) {
      final String errMsg = "Not allowed to delete root node of services tree!";
      LOGGER.error(errMsg);
      throw new SMProvException(errMsg, errMsg);
    }
    List<Integer> customers = getCustomersForParent(id);
    if (!customers.isEmpty()) {
      LOGGER.error("Cannot delete customer group from DB: {}", ServiceErrorConstant.C119);
      throw new SMProvException(ServiceErrorConstant.C119, ServiceErrorConstant.C119);
    }
    List<Integer> customerGroups = getCustomerGroupsForParent(id);
    if (!customerGroups.isEmpty()) {
      LOGGER.error("Cannot delete customer group from DB: {}", ServiceErrorConstant.C119);
      throw new SMProvException(ServiceErrorConstant.C119, ServiceErrorConstant.C119);
    }

    try {
      PersistentObjectHelper.destroy(customerGroupDBImpl);
    } catch (Exception e) {
      String errMsg = "Error while deleting customer group object '" + customerGroupDBImpl.getName() + "' from DB. ";
      LOGGER.error(errMsg, e);
      throw new SMProvException(errMsg, errMsg, e);
    }
    return customerGroupDBImpl;
  }

  @MDTransactional
  CustomerDBImpl setCustomerName(int nodeID, String name) throws SMProvException {
    CustomerDBImpl customerDBImpl = getCustomerDBImpl(nodeID);
    customerDBImpl.setName(name);
    LOGGER.debug("Customer name updated successfully -> : {}", name);
    return customerDBImpl;
  }

  @MDTransactional
  CustomerGroupDBImpl setCustomerGroupName(int nodeID, String name) throws SMProvException {
    CustomerGroupDBImpl customerGroupDBImpl = getCustomerGroupDBImpl(nodeID);
    if (TopologyNodeType.SERVICES_ROOT.getName().equalsIgnoreCase(customerGroupDBImpl.getName())) {
      final String errMsg = "Not allowed to modify root node of services tree!";
      LOGGER.error(errMsg);
      throw new SMProvException(errMsg, errMsg);
    }
    customerGroupDBImpl.setName(name);
    return customerGroupDBImpl;
  }

  @MDTransactional
  CustomerServiceGroupDBImpl setServiceGroupName(int nodeID, String name) throws SMProvException {
    CustomerServiceGroupDBImpl customerServiceGroupDBImpl = getCustomerServiceGroupDBImpl(nodeID);
    customerServiceGroupDBImpl.setName(name);
    LOGGER.debug("Customer service group updated successfully: {}", customerServiceGroupDBImpl.getId());
    return customerServiceGroupDBImpl;
  }

  @MDTransactional
  CustomerDBImpl addCustomer(Customer customer) throws SMProvException {
    EntityManager em = MDPersistenceManager.current();
    CustomerDBImpl customerDBImpl = new CustomerDBImpl();
    customerDBImpl.createFromData(customer);
    customerDBImpl.setParentGroup(getCustomerGroupDBImpl(customerDBImpl.getParentId()));
    em.persist(customerDBImpl);
    return customerDBImpl;
  }

  @MDTransactional
  CustomerServiceGroupDBImpl deleteCustomerServiceGroup(int id) throws SMProvException {
    CustomerServiceGroupDBImpl customerServiceGroupDBImpl = findCustomerServiceGroupDBImpl(id);
    if (customerServiceGroupDBImpl == null) {
      return null;
    }
    try {
      PersistentObjectHelper.destroy(customerServiceGroupDBImpl);
    } catch (Exception e) {
      LOGGER.error("Error while deleting customer object from DB: ", e);
      throw new SMProvException("Error while deleting customer object from DB", "Error while deleting customer object from DB:", e);
    }
    return customerServiceGroupDBImpl;
  }

  @MDTransactional
  CustomerDBImpl createUnassignedCustomer() {
    if (isDuplicate(new JPAQueryParam(CUSTOMER_NAME_PARAMETER, UNASSIGNED_CUSTOMER_NAME))) {
      LOGGER.error("Unassigned Customer already exists");
      return null;
    }
    CustomerDBImpl customerDBImpl = new CustomerDBImpl();
    customerDBImpl.setName(UNASSIGNED_CUSTOMER_NAME);

    try {
      CustomerGroupDBImpl customerGroupDBImpl = getTopLevelCustomerGroup();
      if (customerGroupDBImpl == null) {
        LOGGER.error("Top level customer group does not exist for : {}", customerDBImpl.getName());
        return null;
      }
      customerDBImpl.setParentGroup(customerGroupDBImpl);
      customerDBImpl.setParentId(customerGroupDBImpl.getId());
    } catch (Exception e) {
      LOGGER.error("Error creating unassigned Customer failed! {}", e.getMessage());
      return null;
    }
    EntityManager em = MDPersistenceManager.current();
    em.persist(customerDBImpl);
    LOGGER.info("Unassigned customer created successfully: {}", customerDBImpl.getName());

    return customerDBImpl;
  }

  private CustomerGroupDBImpl getTopLevelCustomerGroup() {
    CustomerGroupDBImpl rootFolder = getCreateCustomerGroup(0, "");
    if (rootFolder == null) {
      LOGGER.error("Could not get root group folder");
      return null;
    }

    return getCreateCustomerGroup(rootFolder.getId(), TopologyNodeType.SERVICES_ROOT.getName());
  }

  public TrailGroupDBImpl getCreateTrailGroup(int parentId, String name) {
    TrailGroupDBImpl trailGroup = getTrailGroup(parentId, name);
    if (trailGroup != null) {
      return trailGroup;
    }
    return createTrailGroup(parentId, name);
  }

  public EthernetRingResourceDBImpl getCreateEthernetRingResourceDBImpl(int parent, String name) {
    EthernetRingResourceDBImpl resourceDBImpl = getEthernetRingResourceDBImpl();
    if (resourceDBImpl != null) {
      return resourceDBImpl;
    }
    return createEthernetRingResource(parent, name);
  }


  @MDTransactional
  public void updateCustomerGroupName(CustomerGroupDBImpl customerGroup, String name) {
    customerGroup = MDPersistenceHelper.refind(customerGroup, customerGroup.getId());
    customerGroup.setName(name);
  }

  @MDTransactional
  public TrailGroupDBImpl getTrailGroup(int parentId, String name) {
    EntityManager em = MDPersistenceManager.current();
    TypedQuery<TrailGroupDBImpl> query = em.createQuery("SELECT g FROM TrailGroupDBImpl g WHERE g.parentId = :parentId AND g.name = :name ORDER BY g.id ASC",
        TrailGroupDBImpl.class);
    query.setParameter(PARENT_ID_PARAMETER, parentId);
    query.setParameter("name", name);
    List<TrailGroupDBImpl> result = query.getResultList();
    if (!result.isEmpty()) {
      if (result.size() > 1) {
        LOGGER.error("More than one \"{}\" group with parent ID {}", name, parentId);
      }
      return result.get(0);
    }
    return null;
  }

  @MDTransactional
  public EthernetRingResourceDBImpl getEthernetRingResourceDBImpl() {
    EntityManager em = MDPersistenceManager.current();
    TypedQuery<EthernetRingResourceDBImpl> query = em.createQuery("SELECT g FROM EthernetRingResourceDBImpl g WHERE g.parentId = :parentId ORDER BY g.id ASC",
      EthernetRingResourceDBImpl.class);
    int parentId=getCustomerGroup(0,"").getId();
    query.setParameter(PARENT_ID_PARAMETER, parentId);
    List<EthernetRingResourceDBImpl> result = query.getResultList();
    if (!result.isEmpty()) {
      if (result.size() > 1) {
        LOGGER.error("More than one ethernet ring resource group with parent ID {}",parentId);
      }
      return result.get(0);
    }
    return null;
  }

  @MDTransactional
  public TrailGroupDBImpl createTrailGroup(int parentId, String name) {
    EntityManager em = MDPersistenceManager.current();
    TrailGroupDBImpl trailGroupDB = new TrailGroupDBImpl();
    trailGroupDB.setGroupType(CustomerGroupDBImpl.GroupType.TRAIL.getIntValue());
    trailGroupDB.setParentId(parentId);
    trailGroupDB.setName(name);
    em.persist(trailGroupDB);
    return trailGroupDB;
  }

  @MDTransactional
  public EthernetRingResourceDBImpl createEthernetRingResource(int parentId, String name) {
    EntityManager em = MDPersistenceManager.current();
    EthernetRingResourceDBImpl erpRingResourceDB = new EthernetRingResourceDBImpl();
    erpRingResourceDB.setParentId(parentId);
    erpRingResourceDB.setName(name);
    em.persist(erpRingResourceDB);
    return erpRingResourceDB;
  }

  public CustomerGroupDBImpl getCreateCustomerGroup(int parentId, String name) {
    CustomerGroupDBImpl customerGroup = getCustomerGroup(parentId, name);
    if (customerGroup != null) {
      return customerGroup;
    }
    return createCustomerGroup(parentId, name);
  }

  @MDTransactional
  public List<CustomerGroupDBImpl> getTopLevelFolders() {
    EntityManager em = MDPersistenceManager.current();
    TypedQuery<CustomerGroupDBImpl> query = em.createQuery("SELECT g FROM CustomerGroupDBImpl g WHERE g.parentId = 0 ORDER BY g.id ASC",
        CustomerGroupDBImpl.class);
    return query.getResultList();
  }

  @MDTransactional
  public CustomerGroupDBImpl getCustomerGroup(int parentId, String name) {
    EntityManager em = MDPersistenceManager.current();
    TypedQuery<CustomerGroupDBImpl> query = em.createQuery(
        "SELECT g FROM CustomerGroupDBImpl g WHERE g.parentId = :parentId AND g.name = :name ORDER BY g.id ASC",
        CustomerGroupDBImpl.class);
    query.setParameter(PARENT_ID_PARAMETER, parentId);
    query.setParameter("name", name);
    List<CustomerGroupDBImpl> result = query.getResultList();
    if (!result.isEmpty()) {
      if (result.size() > 1) {
        LOGGER.error("More than one \"{}\" group with parent ID {}", name, parentId);
      }
      return result.get(0);
    }
    return null;
  }

  @MDTransactional
  public CustomerGroupDBImpl getGroupByIdAndCustomerId(int groupId, int customerId) {
    EntityManager em = MDPersistenceManager.current();
    TypedQuery<CustomerGroupDBImpl> query = em.createQuery(
            "SELECT g FROM CustomerServiceGroupDBImpl g WHERE g.customer.id = :customerId AND g.id = :groupId",
            CustomerGroupDBImpl.class);
    query.setParameter("customerId", customerId);
    query.setParameter("groupId", groupId);
    return query.getSingleResult();
  }

  @MDTransactional
  public List<CustomerGroupDBImpl> getGroupByNameAndCustomerId(String name, int customerId) {
    EntityManager em = MDPersistenceManager.current();
    TypedQuery<CustomerGroupDBImpl> query = em.createQuery(
            "SELECT g FROM CustomerServiceGroupDBImpl g WHERE g.customer.id = :customerId AND g.name = :name ORDER BY g.id ASC",
            CustomerGroupDBImpl.class);
    query.setParameter("customerId", customerId);
    query.setParameter("name", name);
    return query.getResultList();
  }

  @MDTransactional
  public CustomerGroupDBImpl createCustomerGroup(int parentId, String name) {
    EntityManager em = MDPersistenceManager.current();
    CustomerGroupDBImpl customerGroupDB = new CustomerGroupDBImpl(UUID.randomUUID());
    customerGroupDB.setGroupType(CustomerGroupDBImpl.GroupType.CUSTOMER.getIntValue());
    customerGroupDB.setParentId(parentId);
    customerGroupDB.setName(name);
    customerGroupDB.setIsCustomerGroup(true);
    em.persist(customerGroupDB);
    return customerGroupDB;
  }

  @MDTransactional
  CustomerGroupDBImpl createCustomerGroup(CustomerGroup cg) {
    CustomerGroupDBImpl customerGroupDBImpl = new CustomerGroupDBImpl(UUID.randomUUID());
    customerGroupDBImpl.setGroupType(CustomerGroupDBImpl.GroupType.CUSTOMER.getIntValue());
    customerGroupDBImpl.createFromData(cg);
    customerGroupDBImpl.setIsCustomerGroup(true);
    MDPersistenceManager.current().persist(customerGroupDBImpl);
    return customerGroupDBImpl;
  }

  //returns customer details for all specific customer id's
  @MDPersistenceContext
  Customer[] getCustomerObjects(List<Integer> customerId) throws MDOperationFailedException {
    if (customerId == null) {
      return new Customer[0];
    }
    Customer[] customers;

    try {

      List<Customer> customerList = new ArrayList<>();
      for (Integer id : customerId) {
        CustomerDBImpl custDB = MDPersistenceHelper.getObjectById(CustomerDBImpl.class, id);
        Customer cust = new Customer();
        custDB.createDTOFromData(cust);
        customerList.add(cust);
      }
      customers = new Customer[customerList.size()];
      customerList.toArray(customers);
    } catch (Exception e) {
      LOGGER.error(ServiceErrorConstant.C126 + ": " + customerId, e);
      throw new MDOperationFailedException(ServiceErrorConstant.C126, e);
    }
    return customers;
  }

  @MDTransactional
  ContractDBImpl modifyContract(Contract contract){
    EntityManager em = MDPersistenceManager.current();
    ContractDBImpl contractDB = em.find(ContractDBImpl.class, contract.getId());
    CustomerDBImpl customerDB = em.find(CustomerDBImpl.class, contract.getCustomerId());
    contractDB.setName(contract.getName());
    if (customerDB != null) {
      contractDB.setCustomer(customerDB);
    }
    contractDB.setDescription(contract.getDescription());
    contractDB = em.merge(contractDB);
    return contractDB;
  }

  @MDPersistenceContext
  ContractDBImpl getContract(int id) {
      return MDPersistenceManager.current().find(ContractDBImpl.class, id);
  }

  @MDPersistenceContext
  void checkNames(String name, Class classType) throws SMDuplicateNamesNotAllowedException, MDObjectAlreadyExistsException {
    String className = "";
    String dbName = "name";
    if (classType == CustomerServiceGroup.class) {
      className = "CustomerServiceGroupDBImpl";
    } else if (classType == CustomerGroup.class) {
      className = "CustomerGroupDBImpl";

    } else if (classType == Customer.class) {
      className = "CustomerDBImpl";
      dbName = CUSTOMER_NAME_PARAMETER;
    }

    EntityManager em = MDPersistenceManager.current();
    Query query = em.createQuery("SELECT a from " + className + " a where a." + dbName + " = :name");
    query.setParameter("name", name);
    Iterator<?> iterator;
    try {
      iterator = query.getResultList().iterator();
    } catch (Exception e) {
      String message = "Name \"" + name + "\" contains invalid characters";
      throw new SMDuplicateNamesNotAllowedException(message);
    }
    if (iterator.hasNext()) {
      iterator.next();
      String message = "Name \"" + name + "\" is already used";
      LOGGER.info(message);
      if (FNMPropertyFactory.getPropertyAsBoolean(FNMPropertyConstants.DUPLICATE_CONNECTION_NAMES_NOT_ALLOWED, true)) {
        throw new SMDuplicateNamesNotAllowedException(message);
      } else {
        throw new MDObjectAlreadyExistsException(message);
      }
    }
  }

  @MDPersistenceContext
   String getDefaultServiceNameWithoutCache() {
    final String serviceNameStart = "Service-";
    TypedQuery<String> query;
    final String serviceNamePattern = serviceNameStart + '%';
    query = MDPersistenceManager.current().createQuery("SELECT c.name FROM ServiceIntentDBImpl c"
        + " WHERE c.name LIKE :pattern", String.class);
    query.setParameter(PATTERN_PARAMETER, serviceNamePattern);
    List<String> existingNames = query.getResultList();

    List<String> pdNames = BeanProvider.get().getBean(PDServiceIntentDataController.class)
        .getServiceIntentsByNamePattern(serviceNamePattern)
        .stream()
        .map(PDServiceIntentDTO::name)
        .toList();
    existingNames.addAll(pdNames);
    int serviceNameIndex = existingNames.size() + 1;

    while (!isServiceNameUnique(serviceNameStart + serviceNameIndex, existingNames)) {
      serviceNameIndex++;
    }

    return serviceNameStart + serviceNameIndex;
  }

  private boolean isServiceNameUnique(String serviceName, List<String> serviceNameList) {
    return !serviceNameList.contains(serviceName);
  }

  @MDPersistenceContext
  List<String> getExistingNames(String pattern) {
    TypedQuery<String> query = MDPersistenceManager.current().createQuery("SELECT c.name FROM ContractDBImpl c"
            + " WHERE c.name LIKE :pattern", String.class);
    query.setParameter(PATTERN_PARAMETER, pattern);
    Set<String>  list = new HashSet<>(query.getResultList());
    TypedQuery<String> query2 = MDPersistenceManager.current().createQuery("SELECT c.name FROM ServiceIntentDBImpl c"
            + " WHERE c.name LIKE :pattern", String.class);
    query2.setParameter(PATTERN_PARAMETER, pattern);
    list.addAll(query2.getResultList());
    query2 = MDPersistenceManager.current().createQuery("SELECT c.label FROM MLTrailDBImpl c"
            + " WHERE c.label LIKE :pattern", String.class);
    query2.setParameter(PATTERN_PARAMETER, pattern);
    list.addAll(query2.getResultList());
    query2 = MDPersistenceManager.current().createQuery("SELECT c.label FROM AbstractConnectionDBImpl c"
            + " WHERE c.isHidden = false and c.label LIKE :pattern", String.class);
    query2.setParameter(PATTERN_PARAMETER, pattern);
    list.addAll(query2.getResultList());
    return new ArrayList<>(list);
  }

  @MDPersistenceContext
  public List<Integer> getCustomerServiceGroupIdsByParentIds(List<Integer> parentIds) {
    EntityManager em = MDPersistenceManager.current();
    Query query = em.createNamedQuery(CustomerServiceGroupDBImpl.GET_CUSTOMER_SERVICE_GROUP_IDS_BY_PARENT_IDS, Integer.class);
    query.setParameter(PARENT_IDS_PARAMETER, parentIds);
    return query.getResultList();
  }

  @MDPersistenceContext
  public List<Integer> getPacketCustomerServiceGroupIdsByParentIds(List<Integer> parentIds) {
    Query query = MDPersistenceManager.current().createQuery("SELECT s.id FROM CustomerGroupDBImpl s WHERE (s.parentId IN :parentIds)" +
            "AND s.id NOT IN (SELECT s1.id FROM CustomerGroupDBImpl s1 JOIN AbstractConnectionDBImpl cn ON (s1.id = cn.serviceIntent.id) WHERE s1.parentId IN :parentIds AND cn.classType NOT IN :classTypes)" +
            "AND s.id NOT IN (SELECT s2.id FROM CustomerGroupDBImpl s2 WHERE s2.groupType = :groupType AND s2.name NOT IN :name)", Integer.class);
    query.setParameter(PARENT_IDS_PARAMETER, parentIds);
    query.setParameter("classTypes", Set.of(ServiceLayer.EDS.getClassTypeIntValue(), ServiceLayer.ETrail.getClassTypeIntValue()));
    query.setParameter(GROUP_TYPE_PARAMETER, CustomerGroupDBImpl.GroupType.TRAIL.getIntValue());
    query.setParameter("name", List.of("Classic Trails", "ETrail"));
    return query.getResultList();
  }

  public List<CustomerDBImpl> findAll() {
    return MDPersistenceHelper.queryForList(CustomerDBImpl.class);
  }


  @MDPersistenceContext
  public CustomerDBImpl getById(int id) {
    return (CustomerDBImpl) MDPersistenceManager.current().createNamedQuery(CustomerDBImpl.GET_CUSTOMER_BY_ID).
            setParameter("id", id).
            getSingleResult();
  }

  @MDPersistenceContext
  public CustomerServiceGroupDBImpl getServiceGroupById(int id) {
    try {
      return MDPersistenceHelper.getObjectById(CustomerServiceGroupDBImpl.class, id);
    } catch (EntityNotFoundException e) {
     return null;
    }
  }

  @MDPersistenceContext
  public int getCustomerGroupParentId(int customerGroupId) {
    CustomerGroupDBImpl customerGroup = MDPersistenceManager.current().find(CustomerGroupDBImpl.class, customerGroupId);
    if (customerGroup != null) {
      return customerGroup.getParentId();
    } else {
      return -1;
    }
  }

  @MDPersistenceContext
  public List<CustomerDBImpl> getAll() {
    return MDPersistenceManager.current().createNamedQuery(CustomerDBImpl.GET_ALL_CUSTOMERS).
            getResultList();
  }

  @MDPersistenceContext
  public List<Integer> getCustomerIdsByParentId(Integer parentId) {
    EntityManager em = MDPersistenceManager.current();
    Query query = em.createNamedQuery(CustomerDBImpl.GET_CUSTOMER_IDS_BY_PARENT_IDS_QUERY_NAME);
    query.setParameter(PARENT_IDS_PARAMETER, Set.of(parentId));
    return query.getResultList();
  }

}
