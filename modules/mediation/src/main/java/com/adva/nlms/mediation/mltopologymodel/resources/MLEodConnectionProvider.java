/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: NikolaosL
 */

package com.adva.nlms.mediation.mltopologymodel.resources;

import com.adva.eod.nrim.api.dto.ConnectionDto;
import com.adva.eod.nrim.api.dto.ConnectionLifecycleState;
import com.adva.eod.nrim.api.dto.ConnectionPointDto;
import com.adva.eod.nrim.api.dto.ConnectionType;
import com.adva.eod.nrim.api.dto.Directionality;
import com.adva.eod.nrim.api.dto.NodeCompactDto;
import com.adva.eod.nrim.api.dto.Routing;
import com.adva.eod.nrim.api.dto.ServerConnections;
import com.adva.eod.nrim.api.dto.SwitchDto;
import com.adva.nlms.common.mltopologymodel.enums.MLLifeCycleState;
import com.adva.nlms.common.sm.ProtMechanism;
import com.adva.nlms.commondefinition.namerepresentation.NameDto;
import com.adva.nlms.commondefinition.namerepresentation.NameRepresentation;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.common.persistence.MDPersistenceHelper;
import com.adva.nlms.mediation.config.ManagedObjectDBImpl;
import com.adva.nlms.mediation.config.f8.entity.protection.ProtectionGroupF8DBImpl;
import com.adva.nlms.mediation.mltopologymodel.helpers.MLEodLayerProtocolHelper;
import com.adva.nlms.mediation.mltopologymodel.helpers.MLEodNRLHelper;
import com.adva.nlms.mediation.mltopologymodel.model.dao.MLTopologyElementDAO;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLConnectionDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLConnectionPointDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLMtpConnectionDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLTopologyElementDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLTrailDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLConnection;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLTopologyMOReference;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLConnectionType;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLNetworkLayer;
import com.adva.nlms.mediation.mltopologymodel.mofacade.MLMoReferenceHelper;
import com.adva.nlms.mediation.mltopologymodel.resources.persistentview.MLUiServiceViewDBImpl;
import com.adva.nlms.mediation.mltopologymodel.stateproc.adminstate.api.ConnectionAdminStateProvider;
import com.adva.nlms.mediation.mltopologymodel.sync.endpoint.MLEodLinkHandler;
import com.adva.nlms.mediation.mltopologymodel.sync.exception.MLReferenceException;
import com.adva.nlms.mediation.sm.connproperties.ConnectionProtectionStateType;
import com.adva.nlms.mediation.topology.eod.NodeProvider;
import com.adva.nlms.nrl.access.api.NetworkResourceLocatorAccess;
import com.adva.nlms.nrl.access.api.exception.WrongNRLSyntaxException;
import com.adva.nlms.opticalparameters.api.OpticalLayerProtocolApi;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.opticalparameters.api.enums.ConnectionAdminState;
import com.adva.nlms.opticalparameters.api.enums.ProtectionState;
import com.adva.nlms.opticalparameters.api.enums.ProtectionType;
import com.adva.nlms.opticalparameters.api.enums.SwitchState;
import com.adva.topology.manager.api.dto.NodeDto;
import com.adva.topology.manager.api.in.TopologyNodeApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;

@Component
public class MLEodConnectionProvider {
  private static final Logger LOGGER = LoggerFactory.getLogger(MLEodConnectionProvider.class);

  public record ProtectionParams(ProtectionType type, ProtectionState state){}
  private static final ProtectionParams EMPTY_PROTECTION =
    new ProtectionParams(ProtectionType.NONE, ProtectionState.UNKNOWN);
  private static final Predicate<MLConnection> IS_OF_TYPE_NETWORK_CONNECTION =
    mlConnection -> mlConnection.getConnectionType() == MLConnectionType.NetworkConnection;

  private final NodeProvider nodeProvider;
  private final MLEodLinkHandler mlEodLinkHandler;
  private final TopologyNodeApi topologyNodeApi;
  private final ConnectionAdminStateProvider connectionAdminStateProvider;
  private MLMoReferenceHelper mlMoReferenceHelper;

  private final List<OpticalLayerProtocolApi.KeyAndName> validLPQs;

  public MLEodConnectionProvider(NodeProvider nodeProvider, MLEodLinkHandler mlEodLinkHandler, TopologyNodeApi topologyNodeApi,
                                 ConnectionAdminStateProvider connectionAdminStateProvider, MLMoReferenceHelper mlMoReferenceHelper) {
    this.nodeProvider = nodeProvider;
    this.mlEodLinkHandler = mlEodLinkHandler;
    this.topologyNodeApi = topologyNodeApi;
    this.connectionAdminStateProvider = connectionAdminStateProvider;
    this.mlMoReferenceHelper = mlMoReferenceHelper;
    validLPQs = OpticalLayerProtocolApi.getLayerProtocolQualifierAndNameList();
  }

  public List<ConnectionDto> getConnections(List<Integer> mlConnectionIds) {
    if (mlConnectionIds.size() == 1) {
      return List.of(getConnectionWithRouting(mlConnectionIds.get(0)));
    }
    return mlConnectionIds.stream()
            .map(this::getConnection)
            .filter(Objects::nonNull)
            .toList();
  }

  public long getConnectionsCountFilteredByProtectionType(List<Integer> mlConnectionIds, ProtectionType protectionType) {
    long connectionsCount = 0;
    for (Integer mlConnectionId : mlConnectionIds) {
      MLTrailDBImpl trailDB = getTrailForId(mlConnectionId);

      if (trailDB == null) {
        continue;
      }

      ProtectionParams protectionParams = getProtectionParametersFromConnectionBelow(trailDB);

      if (protectionType == protectionParams.type()) {
        connectionsCount ++;
      }
    }
    return connectionsCount;
  }

  public String getLayerProtocolQualifierOfAConnection(int mlConnectionId) {
    MLUiServiceViewDBImpl serviceViewDB = getServiceViewForId(mlConnectionId);
    return serviceViewDB != null ? serviceViewDB.getEodLayerProtocolQualifier() : null;
  }

  public ConnectionDto getConnection(int mlConnectionId) {
    MLTrailDBImpl trailDB = getTrailForId(mlConnectionId);
    MLUiServiceViewDBImpl serviceViewDB = getServiceViewForId(mlConnectionId);

    if (trailDB == null || serviceViewDB == null) {
      return null;
    }

    String layerProtocolQualifier = serviceViewDB.getEodLayerProtocolQualifier();
    String payloadQualifier = serviceViewDB.getEodPayloadQualifier();
    OpticalLayerProtocolApi.KeyAndName lpqAndName = MLEodLayerProtocolHelper.getValidLPQ(validLPQs, layerProtocolQualifier);

    NodeCompactDto nodeA = nodeProvider.provideNodeCompact(serviceViewDB.getStartNeId());
    NodeCompactDto nodeZ = nodeProvider.provideNodeCompact(serviceViewDB.getPeerNeId());

    boolean isAEndAlphanumericallyBeforeZEnd = checkAEndBeforeZEnd(trailDB.getAEndCep(), trailDB.getZEndCep());

    List<String> nrlsOrdered = createOrderedListWithNonNullElements(
            trailDB.getAEndCep(), trailDB.getZEndCep(), isAEndAlphanumericallyBeforeZEnd);
    List<String> nodeNamesOrdered = createOrderedListWithNonNullElements(
            nodeProvider.getNodeName(nodeA.name(), NameRepresentation.USER_LABEL),
            nodeProvider.getNodeName(nodeZ.name(), NameRepresentation.USER_LABEL),
            isAEndAlphanumericallyBeforeZEnd);
    List<NameDto> connectionNames = provideNamesForConnection(lpqAndName.key(), nrlsOrdered, nodeNamesOrdered, trailDB.getLabel());

    List<String> tpsOrdered = createOrderedListWithNonNullElements(
            serviceViewDB.getStartTp(), serviceViewDB.getPeerTp(), isAEndAlphanumericallyBeforeZEnd);

    List<NodeCompactDto> nodesOrdered = createOrderedListWithNonNullElements(nodeA, nodeZ, isAEndAlphanumericallyBeforeZEnd);

    List<ConnectionPointDto> connectionPoints = provideConnectionEndpoints(nrlsOrdered, tpsOrdered, nodesOrdered, nodeNamesOrdered);
    ConnectionLifecycleState lc = getLifecycleFromML(serviceViewDB.getLifecycleState());
    ProtectionParams protectionParams = getProtectionParametersFromConnectionBelow(trailDB);
    ConnectionType connectionType = nodeA.equals(nodeZ) ? ConnectionType.FIXED_OR_FLEX_CO : ConnectionType.TOP_CO;
    MLEodOperStateMapper operStateMapper = MLEodOperStateMapper.mapMLOperationalStatus(trailDB.getOperationalStatus());
    ConnectionAdminState connectionAdminState = connectionAdminStateProvider.getOrComputeConnectionAdminState(trailDB.getId());

    return new ConnectionDto(trailDB.getUuid(), connectionNames, lpqAndName.name(), lpqAndName.key(), connectionPoints,
      protectionParams.type, protectionParams.state, payloadQualifier, Directionality.BIDIRECTIONAL, lc,
      operStateMapper.getConnectionOperationalState(), operStateMapper.getReasonCode(), operStateMapper.getSecondaryState(), operStateMapper.getStatusChangeStartTime(),
      connectionType, null, null, getTargetAdminState(trailDB), connectionAdminState, trailDB.isDataService());
  }

  public ProtectionParams getProtectionParametersFromConnectionBelow(MLTrailDBImpl trailDB) {
    if (trailDB == null || trailDB.getWorkingForwardPath() == null || trailDB.getWorkingForwardPath().connectionSequence() == null)
      return EMPTY_PROTECTION;
    MLConnection networkConnection = getNetworkConnection(trailDB);
    if (networkConnection != null)
      return mapTEPropertiesForProtection(networkConnection, trailDB.getLayer());
    return EMPTY_PROTECTION;
  }

  public MLConnection getNetworkConnection(MLTrailDBImpl mlTrailDB) {
    List<MLConnection> networkConnections = mlTrailDB.getWorkingForwardPath().connectionSequence().stream()
            .filter(IS_OF_TYPE_NETWORK_CONNECTION)
            .toList();
    if (networkConnections.isEmpty())
      return null;
    // More than a single protection per trail is not considered in EOD EVO. Just log an error and use the first found protected connection.
    if (networkConnections.size() > 1)
      LOGGER.error("MLTrailDBImpl is protected on {} sections! {}", networkConnections.size(), mlTrailDB);
    return networkConnections.get(0);
  }

  public List<SwitchDto> getNetworkConnections(int mlConnectionId) {
    List<SwitchDto> switchDtoList = Collections.emptyList();
    MLTrailDBImpl trailDB = getTrailForId(mlConnectionId);
    if (trailDB == null || trailDB.getWorkingForwardPath() == null || trailDB.getWorkingForwardPath().connectionSequence() == null)
      return switchDtoList; // return empty list if null

    MLConnection networkConnection = getNetworkConnection(trailDB);
    if (networkConnection != null) {
      List<MLConnectionDBImpl> protCCDBs = MLTopologyElementDAO.getInstance().getConnectionsByEndpoint((MLConnectionPointDBImpl) networkConnection.getAEndConnectionPoint(), MLConnectionDBImpl.class, networkConnection.getLayer());
      protCCDBs.addAll(MLTopologyElementDAO.getInstance().getConnectionsByEndpoint((MLConnectionPointDBImpl) networkConnection.getZEndConnectionPoint(), MLConnectionDBImpl.class, networkConnection.getLayer()));

      List<MLConnectionDBImpl> crossConnectConnections = MLTopologyElementDAO.getInstance().getSegmentAdaptationForClient((MLConnectionDBImpl) networkConnection).getAllConnectionsUnderSegment()
              .stream().filter(MLMtpConnectionDBImpl.class::isInstance).toList();

      switchDtoList =  crossConnectConnections.stream().map(this::getProtSwitchEntities).filter(Objects::nonNull).toList();
    }
    return switchDtoList;
  }

  private SwitchDto getProtSwitchEntities(MLTopologyElementDBImpl crossConnect) {
    try {
      Optional<MLTopologyMOReference> moProtGroupReference = crossConnect.getRelatedMOObjects().stream()
              .filter(v -> v.getEntityType().isAssignableFrom(ProtectionGroupF8DBImpl.class))
              .findAny();
      if (moProtGroupReference.isPresent()) {
        MLTopologyMOReference moReference = moProtGroupReference.get();
        ManagedObjectDBImpl managedObjectDB = mlMoReferenceHelper.moObject(moReference);
        if (managedObjectDB != null) {
          String entityIndex = managedObjectDB.getEntityIndex().toString();
          NodeDto node = topologyNodeApi.getNodeByNeId(managedObjectDB.getNeID());
          SwitchState state = MLTopologyElementDAO.getInstance().calculateSwitchState(managedObjectDB);
          return new SwitchDto(node.id(), getNodeName(node), entityIndex, state);
        }
      }
    } catch (MLReferenceException e) {
      LOGGER.error("Error in retrieving Entity Index: {}", crossConnect.getDescription());
    }
    return null;
  }

  private static String getNodeName(NodeDto node) {
    return node.name().stream()
      .filter(n -> NameRepresentation.USER_LABEL == n.valueName())
      .map(NameDto::value)
      .findFirst()
      .orElse(null);
  }

  private ProtectionParams mapTEPropertiesForProtection(MLConnection mlConnection, MLNetworkLayer layer) {
    ProtMechanism protectionMechanism = mlConnection.getProtectionMechanism();
    ConnectionProtectionStateType protectionStateType = mlConnection.getProtectionStateType();
    if (protectionMechanism == null || protectionStateType == null){
      LOGGER.error("Unable to map protection parameters: NetworkConnection has incomplete protection parameters: {}", mlConnection);
      return EMPTY_PROTECTION;
    }
    ProtectionType type = switch (protectionMechanism){
      case CHANNEL_PROTECTION -> ProtectionType.CNLP;
      case ADM, PATH_PROTECTION -> ProtectionType.SNCP;
      case CLIENT_CHANNEL_CARD_PROTECTION -> ProtectionType.CCCP;
      case CHANNEL_OPPM_PROTECTION -> ProtectionType.OPPC;
      case LINE_PROTECTION -> layer == MLNetworkLayer.OTS ? ProtectionType.OTSP : ProtectionType.OMSP;
      default -> ProtectionType.NONE;
    };
    ProtectionState state = switch (protectionStateType){
      case OK -> ProtectionState.WORKING;
      case REVERSE -> ProtectionState.PROTECTING;
      case UNIDIRECTIONAL -> ProtectionState.MIXED;
      default -> ProtectionState.UNKNOWN;
    };
    return new ProtectionParams(type, state);
  }

  /**
   * Fill and return ConnectionDto given the user selected connection
   * Contains:
   * 1. Basic connection information: connection name, endpoints, NEs, LPQ, etc..
   * 2. Extend information: service preview path
   */
  public ConnectionDto getConnectionWithRouting(int mlConnectionId) {
    ConnectionDto dto = getConnection(mlConnectionId);
    if (dto == null)
      return null;
    MLTrailDBImpl trailDB = getTrailForId(mlConnectionId);
    List<Routing> routing = mlEodLinkHandler.getRouting(trailDB);
    List<ServerConnections> serverConnections = mlEodLinkHandler.getServerConnections(trailDB);
    return addRoutingAndServerConnections(dto, routing, serverConnections);
  }

  public ConnectionDto addRoutingAndServerConnections(ConnectionDto dto, List<Routing> routing, List<ServerConnections> serverConnections){
    return new ConnectionDto(dto.id(), dto.name(), dto.layerProtocolName(), dto.layerProtocolQualifier(), dto.connectionEndPoints(),
      dto.protectionType(), dto.protectionState(), dto.payloadQualifier(), dto.directionality(), dto.lifeCycleState(),
      dto.operationalState(), dto.reasonCode(), dto.secondaryState(), dto.statusChangeStartTime(), dto.connectionType(),
      routing, serverConnections, dto.targetAdminState(), dto.connectionAdminState(), dto.isDataService());
  }

  private AdminState getTargetAdminState(MLTrailDBImpl trailDB) {
    return switch (trailDB.getAdminState()){
      case UP -> AdminState.UP;
      case DOWN -> AdminState.DOWN;
      case MAINTENANCE -> AdminState.MAINTENANCE;
      case UNKNOWN, PLANNED -> AdminState.NA;
    };
  }

  private boolean checkAEndBeforeZEnd(String aEndCep, String zEndCep) {
    boolean isAEndAlphanumericallyBeforeZEnd = true;
    if (aEndCep != null && zEndCep != null) {
      isAEndAlphanumericallyBeforeZEnd = aEndCep.compareToIgnoreCase(zEndCep) > 0;
    }
    return isAEndAlphanumericallyBeforeZEnd;
  }

  private ConnectionLifecycleState getLifecycleFromML(MLLifeCycleState lifecycleState) {
    ConnectionLifecycleState lc = ConnectionLifecycleState.NA;

    if (MLLifeCycleState.MANAGED == lifecycleState) {
      lc = ConnectionLifecycleState.MANAGED;
    } else if (MLLifeCycleState.DISCOVERED == lifecycleState) {
      lc = ConnectionLifecycleState.DISCOVERED;
    }

    return lc;
  }

  private <T> List<T> createOrderedListWithNonNullElements(T a, T b, boolean parameterAFirst) {
    List<T> orderedList = new ArrayList<>();
    if (a != null && b != null) {
      orderedList = parameterAFirst ? List.of(a, b) : List.of(b, a);
    }
    return orderedList;
  }

  @MDPersistenceContext
  private MLTrailDBImpl getTrailForId(int id) {
    return MDPersistenceHelper.find(MLTrailDBImpl.class, id);
  }

  @MDPersistenceContext
  public MLUiServiceViewDBImpl getServiceViewForId(int id) {
    return MDPersistenceHelper.find(MLUiServiceViewDBImpl.class, id);
  }

  private List<NameDto> provideNamesForConnection(String lpq, List<String> nrls, List<String> nodeNamesOrdered, String systemName) {
    List<NameDto> names = new ArrayList<>();

    if (lpq != null && nrls.size() == 2) {
      String standardNrl = lpq + " - " + nrls.get(0) + " - " + nrls.get(1);
      names.add(new NameDto(NameRepresentation.STANDARD_NRL, standardNrl));

      if (nodeNamesOrdered.size() == 2) {
        String compactNrl1 = calculateCompactNRLForConnection(nrls.get(0), nodeNamesOrdered.get(0));
        String compactNrl2 = calculateCompactNRLForConnection(nrls.get(1), nodeNamesOrdered.get(1));
        if (compactNrl1 != null && compactNrl2 != null) {
          String connCompactNrl = lpq + " - " + compactNrl1 + " - " + compactNrl2;
          names.add(new NameDto(NameRepresentation.COMPACT_NRL, connCompactNrl));
        }
      }
    }

    if (systemName != null) {
      names.add(new NameDto(NameRepresentation.SYSTEM, systemName));
    }

    return names;
  }


  private List<ConnectionPointDto> provideConnectionEndpoints(List<String> nrlsOrdered, List<String> tpsOrdered,
                                                              List<NodeCompactDto> nodesOrdered, List<String> nodeNamesOrdered) {
    List<ConnectionPointDto> connectionPointDtos = new ArrayList<>();

    if ((nodesOrdered.size() == 2) && (nrlsOrdered.size() == 2 || tpsOrdered.size() == 2)) {
      for (int i = 0; i < 2; i++) {
        String nrl = null;
        String tp = null;
        String neName = nodeNamesOrdered.get(i);

        if (nrlsOrdered.size() > i) {
          nrl = nrlsOrdered.get(i);
        }
        if (tpsOrdered.size() > i) {
          tp = tpsOrdered.get(i);
        }
        ConnectionPointDto cp = new ConnectionPointDto(nodesOrdered.get(i));
        addNamesToConnectionPointDTO(cp, nrl, tp, neName);
        connectionPointDtos.add(cp);
      }
    }

    return connectionPointDtos;
  }

  private void addNamesToConnectionPointDTO(ConnectionPointDto cpDto, String nrl, String tp, String neName) {
    if (nrl != null) {
      cpDto.addName(new NameDto(NameRepresentation.STANDARD_NRL, nrl));

      String compactNRL = MLEodNRLHelper.calculateCompactNRLForCEP(nrl, neName);
      if (compactNRL != null) {
        cpDto.addName(new NameDto(NameRepresentation.COMPACT_NRL, compactNRL));
      }
    }

    if (tp != null) {
      cpDto.addName(new NameDto(NameRepresentation.SYSTEM, tp));
    }
  }

  private String calculateCompactNRLForConnection(String nrl, String nodeName) {
    NetworkResourceLocatorAccess networkResourceLocatorAccess = new NetworkResourceLocatorAccess();
    String compactNRL = null;
    try {
      compactNRL = networkResourceLocatorAccess.getCompactLabel(nrl, nodeName);
    } catch (WrongNRLSyntaxException e) {
      LOGGER.warn("Wrong syntax for provided NRL string: {}", nrl);
    }

    return compactNRL;
  }

}
