/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: spyrosm
 */

package com.adva.nlms.mediation.sm.prov;

import com.adva.apps.sm.Definition;
import com.adva.nlms.common.sm.ProtMechanism;
import com.adva.apps.sm.ServiceLayer;
import com.adva.nlms.common.snmp.MIBFSP_R7Enums;
import com.adva.nlms.common.snmp.NoSuchMDObjectException;
import com.adva.nlms.common.yp.ParameterEnums;
import com.adva.nlms.common.yp.Parameters;
import com.adva.nlms.mediation.common.CPConstants;
import com.adva.nlms.mediation.common.serviceProvisioning.ProvFSPR7Properties;
import com.adva.nlms.mediation.common.serviceProvisioning.SPProperties;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.DataManagerFactory;
import com.adva.nlms.mediation.config.EntityDAO;
import com.adva.nlms.mediation.config.EntityDBImpl;
import com.adva.nlms.mediation.config.EntityRef;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementHdlrLocal;
import com.adva.nlms.mediation.config.fsp_r7.NetworkElementFSP_R7Impl;
import com.adva.nlms.mediation.config.fsp_r7.entity.facility.physical.PortFSP_R7;
import com.adva.nlms.mediation.config.fsp_r7.entity.facility.physical.PortFSP_R7DBImpl;
import com.adva.nlms.mediation.config.fsp_r7.entity.fibermap.TerminationPointFSP_R7DBImpl;
import com.adva.nlms.mediation.config.fsp_r7.entity.opticalline.PortFSP_R7VirtualDBImpl;
import com.adva.nlms.mediation.config.sm.f7.dto.ExternalChannelDTO;
import com.adva.nlms.mediation.event.message.MessageManager;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.sm.ServiceManagerFacade;
import com.adva.nlms.mediation.sm.ServiceMessageManager;
import com.adva.nlms.mediation.sm.dao.ConnectionDAO;
import com.adva.nlms.mediation.sm.exception.SMException;
import com.adva.nlms.mediation.sm.model.properties.ServiceManagerPropertyDAO;
import com.adva.nlms.mediation.sm.prov.cp.OCSPortHdlr;
import com.adva.nlms.mediation.sm.prov.cp.OCSProcessingParameters;
import com.adva.nlms.mediation.sm.prov.cp.RoutePreview;
import com.adva.nlms.mediation.topology.LineDBImpl;
import com.adva.nlms.mediation.topology.LineDao;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public abstract class AbstractOCSManager implements OCSProvisioning {
  private static final Logger LOGGER = LoggerFactory.getLogger(AbstractOCSManager.class);
  private NetworkElementHdlrLocal neHdlr;
  private OCSValidationHdlr ocsValidationHdlr;
  private ServiceManagerPropertyDAO serviceManagerPropertyDAO;
  private OCSPortHdlr ocsPortHdlr;
  private ConnectionDAO connectionDAO;
  private ServiceManagerFacade serviceManagerFacade;
  private LineDao lineDao;

  @Autowired
  void setNeHdlr(NetworkElementHdlrLocal neHdlr) {
    this.neHdlr = neHdlr;
  }

  @Autowired
  private ServiceMessageManager serviceMessageManager;

  @Autowired
  public void setOcsValidationHdlr(OCSValidationHdlr ocsValidationHdlr) {
    this.ocsValidationHdlr = ocsValidationHdlr;
  }

  @Autowired
  public void setServiceManagerPropertyDAO(ServiceManagerPropertyDAO serviceManagerPropertyDAO) {
    this.serviceManagerPropertyDAO = serviceManagerPropertyDAO;
  }

  @Autowired
  public void setOcsPortHdlr(OCSPortHdlr ocsPortHdlr) {
    this.ocsPortHdlr = ocsPortHdlr;
  }

  @Autowired
  public void setConnectionDAO(ConnectionDAO connectionDAO) {
    this.connectionDAO = connectionDAO;
  }

  @Autowired
  public void setServiceManagerFacade(ServiceManagerFacade serviceManagerFacade) {
    this.serviceManagerFacade = serviceManagerFacade;
  }

  @Autowired
  public void setLineDao(LineDao lineDao) {
    this.lineDao = lineDao;
  }

  protected final OCSProcessingParameters createOcsProcParameters(OpticalChannelService ocs, SMProvParams provParams,
                                                                  Map<Integer, List<PollingUtils.PollingInfo>> portMapForAsyncPolling, boolean logMessages)
          throws SMProvException {
    try {
      if (!ocs.hasECHs() && (ocs.getStartPTP() != null && ocs.getPeerPTP() != null)) {
        ProvFSPR7Properties filtered;
        if (provParams.isProtectingOCS()) {
          filtered = provParams.getStart().getProtNetworkPortProps();
        } else {
          filtered = provParams.getStart().getNetworkPortProps();
        }
        if (!provParams.getServiceType().contains("ODU")) {
          unprovisionNetworkPorts(provParams, ocs, ocsPortHdlr, ocsValidationHdlr, filtered.getParameters(), logMessages);
        }
      }

      return checkingForUnsupportedAttributes(ocs, provParams, portMapForAsyncPolling, logMessages);
    } catch (NoSuchMDObjectException | SNMPCommFailure | SPValidationException e) {
      throw new SMProvException(e.getMessage(), e);
    }
  }

  protected OCSProcessingParameters checkingForUnsupportedAttributes(OpticalChannelService ocs, SMProvParams provParams,
                                                                   Map<Integer, List<PollingUtils.PollingInfo>> portMapForAsyncPolling, boolean logMessages)
          throws SMProvException, NoSuchMDObjectException, SNMPCommFailure {
    String serviceName = provParams.getServiceConnectivityName();
    if (logMessages) {
      serviceMessageManager.addMessageUserAware(MessageManager.Info, serviceName, "Checking for unsupported attributes");
    }
    ProvFSPR7Properties filtered;
    ProvFSPR7Properties filteredPeerParameters;

    if (provParams.isProtectingOCS()) {
      filtered = provParams.getStart().getProtNetworkPortProps();
      filteredPeerParameters = provParams.getPeer().getProtNetworkPortProps();
    } else {
      if (ocs.hasECHs()) {
        ExternalChannelDTO startExternalChannelDTO = provParams.getStart().getExternalChannelDTO();
        ExternalChannelDTO peerExternalChannelDTO = provParams.getPeer().getExternalChannelDTO();

        filtered = startExternalChannelDTO.getProperties();
        filteredPeerParameters = peerExternalChannelDTO.getProperties();

        filtered.set(Parameters.Parameter.TYPE__FACILITY.getKeyword(), provParams.getServiceType());
        filteredPeerParameters.set(Parameters.Parameter.TYPE__FACILITY.getKeyword(), provParams.getServiceType());

        filtered.remove(Parameters.Parameter.CHANNEL__PROVISION.getKeyword());
        filteredPeerParameters.remove(Parameters.Parameter.CHANNEL__PROVISION.getKeyword());


      } else if (provParams.getServiceType().contains("ODU")) {
        filtered = new ProvFSPR7Properties();
        filteredPeerParameters = new ProvFSPR7Properties();
        filtered.set(Parameters.Parameter.TYPE__FACILITY.getKeyword(), provParams.getServiceType());
        filteredPeerParameters.set(Parameters.Parameter.TYPE__FACILITY.getKeyword(), provParams.getServiceType());
      } else {
        filtered = provParams.getStart().getWorkNetPtp()!=null?provParams.getStart().getNetworkPortProps():provParams.getStart().getClientPortProps();
        filteredPeerParameters = provParams.getPeer().getWorkNetPtp()!=null?provParams.getPeer().getNetworkPortProps():provParams.getPeer().getClientPortProps();
      }
    }

    mapFarEndTerm(filtered, filteredPeerParameters);
    mapRestorationParameters(provParams, ocs);
    SPProperties ocsProperties = extractOCSAttributes(ocs,provParams);
    NetworkElement[] nes = new NetworkElement[]{neHdlr.getNetworkElement(ocs.getStartNEID()), neHdlr.getNetworkElement(ocs.getPeerNEID())};
    int mibVersion = nes[0].getPersistenceHelper().getMIBVariantFromDB();
    handleOpticalImpairment(provParams, ocsProperties);
    handleMultiAreaRouting(ocsProperties);
    handleSetPointDelta(provParams, mibVersion, ocsProperties);

    OCSProcessingParameters ocsParameters = initOCSProcessingParameters(ocs, nes[0], nes[1], filtered, filteredPeerParameters);
    ocsParameters.setPortMapForAsyncPolling(portMapForAsyncPolling);
    ocsParameters.setModify(false);
    ocsParameters.setOcsProperties(ocsProperties);
    mapChannels(ocsParameters, provParams, ocs);
    mapIntermediateEntities(ocsParameters, provParams, ocs);

    if (logMessages) {
      serviceMessageManager.addMessageUserAware(MessageManager.Info, serviceName, "Checking for unsupported attributes completed");
    }
    return ocsParameters;
  }

  private void mapFarEndTerm(ProvFSPR7Properties filtered, ProvFSPR7Properties filteredPeerParameters) {
    Object termStart = filtered.get(Parameters.Parameter.TERM.getKeyword());
    Object termPeer = filteredPeerParameters.get(Parameters.Parameter.TERM.getKeyword());
    if (!Objects.equals(termStart, termPeer)) {
      filtered.set(Parameters.Parameter.FEND_TERM.getKeyword(), termPeer);
    }
  }

  /**
   * In this method we unprovision network ports if this is required.
   *
   * @param ocs               ocs
   * @param ocsPortHdlr       ocs port handlr
   * @param ocsValidationHdlr ocs validation hdlr
   * @param filtered          filtered properties
   * @throws SMProvException         provisioning exception
   * @throws NoSuchMDObjectException noSuchException
   */
  private void unprovisionNetworkPorts(SMProvParams provParams,
                                       OpticalChannelService ocs,
                                       OCSPortHdlr ocsPortHdlr,
                                       OCSValidationHdlr ocsValidationHdlr,
                                       Map<Enum<Parameters.Parameter>, Object> filtered,
                                       boolean logMessages)
          throws SMProvException, NoSuchMDObjectException {
    StringBuilder ports = null;
    String serviceName = provParams.getServiceConnectivityName();
    NetworkElement startNe = provParams.getStart().getNetworkElement();
    NetworkElement peerNe = provParams.getPeer().getNetworkElement();
    if (logMessages) {
      ports = new StringBuilder();

      if (provParams.getStart() != null && provParams.getStart().getWorkNetPtp() != null && provParams.getStart().getWorkNetPtp().getAidString() != null) {
        ports.append("related start node ptp(s): ").append(provParams.getStart().getWorkNetPtp().getAidString());
      }
      if (provParams.getStart() != null && provParams.getStart().getProtNetPtp() != null && provParams.getStart().getProtNetPtp().getAidString() != null) {
        ports.append(",").append(provParams.getStart().getProtNetPtp().getAidString());
      }
      if (provParams.getPeer() != null && provParams.getPeer().getWorkNetPtp() != null && provParams.getPeer().getWorkNetPtp().getAidString() != null) {
        ports.append(" related peer node ptp(s): ").append(provParams.getPeer().getWorkNetPtp().getAidString());
      }
      if (provParams.getPeer() != null && provParams.getPeer().getProtNetPtp() != null && provParams.getStart().getProtNetPtp().getAidString() != null) {
        ports.append(",").append(provParams.getPeer().getProtNetPtp().getAidString());
      }

      serviceMessageManager.addMessageUserAware(MessageManager.Info, serviceName, "Preparation of the network ports (" + ports + ")");
    }

    if (ocsValidationHdlr.isUsingPtpAsEndpoints(ocs)) {
      // only unprovision the ports that are used for the OCS, not all network ports of a module
      List<PortFSP_R7DBImpl> ports1 = new ArrayList<>(1);
      try {
        TerminationPointFSP_R7DBImpl ptp = ocs.getStartPTP();
        PortFSP_R7DBImpl portDBImpl = EntityDAO.getByIndex(ocs.getStartNEID(), ptp.getEntityIndexOfRelatedEntity(), PortFSP_R7DBImpl.class);
        PortFSP_R7 port = DataManagerFactory.getInstance().recreateImpl(startNe, portDBImpl);
        if (port.doesNeedToBeUnprovisioned(filtered)) {
          ports1.add(portDBImpl);
        }
      } catch (SPValidationException e) {
        throw new SMProvException(e.getMessage(), e);
      } catch (NoSuchMDObjectException ignored) {
        // no port - nothing to de-provision
        LOGGER.info("Network port related to start PTP of the service was not deprovisioned because the port does not exist.");
      }

      List<PortFSP_R7DBImpl> ports2 = new ArrayList<>(1);
      try {
        TerminationPointFSP_R7DBImpl ptp = ocs.getPeerPTP();
        PortFSP_R7DBImpl portDBImpl = EntityDAO.getByIndex(ocs.getPeerNEID(), ptp.getEntityIndexOfRelatedEntity(), PortFSP_R7DBImpl.class);
        PortFSP_R7 port = DataManagerFactory.getInstance().recreateImpl(peerNe, portDBImpl);
        if (port.doesNeedToBeUnprovisioned(filtered)) {
          ports2.add(portDBImpl);
        }
      } catch (SPValidationException e) {
        throw new SMProvException(e.getMessage(), e);
      } catch (NoSuchMDObjectException ignored) {
        // no port - nothing to de-provision
        LOGGER.info("Network port related to peer PTP of the service was not deprovisioned because the port does not exist.");
      }

      try {
        new ProvisioningUtils().deprovisionPorts(startNe, ports1, peerNe, ports2);
      } catch (SNMPCommFailure x) {
        throw new SMProvException("Unable to deprovision network ports: " + x.getMessage(), x);
      }
    } else {
      try {
        LOGGER.info("Deprovision network ports started.");
        ocsPortHdlr.unprovisionNetworkPorts(filtered, ocs, startNe, peerNe);
        LOGGER.info("Deprovision network ports done.");
      } catch (SNMPCommFailure x) {
        throw new SMProvException("Unable to deprovision network ports: " + x.getMessage(), x);
      }
    }
    if (logMessages) {
      serviceMessageManager.addMessageUserAware(MessageManager.Info, serviceName, "Preparation of the network ports (" + ports + ") completed");
    }
  }

  protected void addMessage(String tabName, String message) {
    serviceMessageManager.addMessageUserAware(MessageManager.Info, tabName, message);
  }

  protected final void mapRestorationParameters(SMProvParams provParams, OpticalChannelService ocs) {
    if (provParams.getRestorationType() != null) {
      ocs.setRestoreType(provParams.getRestorationType().getId());
    }
    if (provParams.getRestorationMode() != null) {
      ocs.setRestorationMode(provParams.getRestorationMode().getId());
    }
    if (provParams.getReversionMode() != null) {
      ocs.setReversionMode(provParams.getReversionMode().getId());
    }
    if (provParams.getStart() != null) {
      ocs.setStartRestorationChannel(provParams.getStart().getRestorationChannel());
    }
    if (provParams.getPeer() != null) {
      ocs.setPeerRestorationChannel(provParams.getPeer().getRestorationChannel());
    }
  }

  protected final SPProperties extractOCSAttributes(final OpticalChannelService ocs, SMProvParams provParams) throws NoSuchMDObjectException, SNMPCommFailure, SMProvException {
    final SPProperties properties = extractOCSAttributes(ocs);
    String tunnelName = deriveTunnelName(ocs, provParams);
    properties.set(Parameters.Parameter.TNLID.getKeyword(), getTunnelName(tunnelName));
    return properties;
  }

  protected String deriveTunnelName(OpticalChannelService ocs, SMProvParams provParams) {
    if(provParams.getServiceLayer() == ServiceLayer.OCS && StringUtils.isNotEmpty(provParams.getServiceConnectivityName())){
      return provParams.getServiceConnectivityName();
    }
    return ocs.getName();
  }

  protected final SPProperties extractOCSAttributes(OpticalChannelService ocs) throws NoSuchMDObjectException, SNMPCommFailure, SMProvException {
    final SPProperties properties = new SPProperties();

    properties.set(Parameters.Parameter.TNLID.getKeyword(), getTunnelName(ocs.getName()));

    NetworkElement startNe = neHdlr.getNetworkElement(ocs.getStartNEID());
    NetworkElement destNe = neHdlr.getNetworkElement(ocs.getPeerNEID());

    //there can be set at the same time ToTid or ToNodeIp which depends on Control Plane WDM Data Node Name Syntax
    final int cPWdmDataNodeNameSyntax = ((NetworkElementFSP_R7Impl) startNe).getCPWdmDataNodeNameSyntaxViaSNMP();
    if (cPWdmDataNodeNameSyntax == MIBFSP_R7Enums.ControlPlaneWdmDataTable.NODE_NAME_SYNTAX_TID) {
      properties.set(Parameters.Parameter.TO_TID.getKeyword(), ((NetworkElementFSP_R7Impl) destNe).getNeSystemIdViaSNMP());
    } else if (cPWdmDataNodeNameSyntax == MIBFSP_R7Enums.ControlPlaneWdmDataTable.NODE_NAME_SYNTAX_IP) {
      properties.set(Parameters.Parameter.TO_NODEIP.getKeyword(), neHdlr.getIPV4Address(destNe));
    } else {
      throw new SMProvException(CPConstants.CREATE_WDM_TUNNEL_UNDEFINED_NODE_SYNTAX);
    }

    ocsValidationHdlr.setAttrFromAndTo(ocs, properties);

    if (ocs.isProtected()) {
      properties.set(Parameters.Parameter.RECOVER.getKeyword(), MIBFSP_R7Enums.TunnelWdmConfigTable.RECOVERY_TYPE_REQUIRED);
    }

    createRestorationParam(ocs.getRestoreType(), properties, false);
    if (ocs.getRestorationMode() != Definition.RestorationMode.NONE.getId()) {
      createExtendedRestorationParams(ocs, properties);
    }
    return properties;
  }

  protected String getTunnelName(String ocsName) {
    return ocsName;
  }

  private void handleOpticalImpairment(SMProvParams provParams, SPProperties ocsProperties) {
    if (Boolean.TRUE.equals(provParams.getOpticalImpairmentEnabled())) {
      ocsProperties.set(Parameters.Parameter.PCE_MODE.getKeyword(), ParameterEnums.PCE_MODE.OSNR_CONSTR.getID());
    }
  }

  private void handleSetPointDelta(SMProvParams provParams, int mibVersion, SPProperties ocsProperties) {
    if (provParams.getSetPointDelta() != null) {
      if (mibVersion < MIBFSP_R7Enums.MibVariant.VER_1711) {
        LOGGER.warn("Set point delta will not be used because the software version of some/all network elements is older than 17.1.1.");
      } else {
        ocsProperties.set(Parameters.Parameter.OPTSET_DEV.getKeyword(), provParams.getSetPointDelta());
      }
    }
  }

  private void handleMultiAreaRouting(SPProperties ocsProperties) {
    if (serviceManagerPropertyDAO.getServiceConfiguration().isMultiAreaRouting()) {
      ocsProperties.set(Parameters.Parameter.PCS_PRFR.getKeyword(), ParameterEnums.PCS_PRFR.YES.getID());
    }
  }

  protected final OCSProcessingParameters initOCSProcessingParameters(OpticalChannelService ocs, NetworkElement startNe, NetworkElement peerNe,
                                                                      ProvFSPR7Properties filtered, ProvFSPR7Properties filteredPeerParameters) {
    OCSProcessingParameters ocsParameters = createParameterObject();
    ocsParameters.setOcs(ocs);
    ocsParameters.setFilteredPeerParameters(filteredPeerParameters);
    ocsParameters.setFilteredStartParameters(filtered);
    ocsParameters.setStartNe(startNe);
    ocsParameters.setPeerNe(peerNe);
    return ocsParameters;
  }

  protected abstract OCSProcessingParameters createParameterObject();

  private void mapChannels(OCSProcessingParameters ocsParameters, SMProvParams provParams, OpticalChannelService ocs) {
    if (provParams.isFirstOCS()) {
      ProvFSPR7Properties filtered = ocsParameters.getFilteredStartParameters();
      if (ocs.hasECHs()) {
        ocsParameters.setStartChannel((String) provParams.getStart().getNetworkPortProps().get(Parameters.Parameter.CHANNEL__PROVISION.getKeyword()));
      } else if (provParams.isSuperChannel()) {
        ocsParameters.setStartChannel((String) filtered.get(Parameters.Parameter.LANE1__PROVISION.getKeyword()));
      } else {
        ocsParameters.setStartChannel((String) filtered.get(Parameters.Parameter.CHANNEL__PROVISION.getKeyword()));
      }
    }
    if (provParams.isLastOCS()) {
      ProvFSPR7Properties filteredPeerParameters = ocsParameters.getFilteredPeerParameters();
      if (ocs.hasECHs()) {
        ocsParameters.setPeerChannel((String) provParams.getPeer().getNetworkPortProps().get(Parameters.Parameter.CHANNEL__PROVISION.getKeyword()));
      } else if (provParams.isSuperChannel()) {
        ocsParameters.setPeerChannel((String) filteredPeerParameters.get(Parameters.Parameter.LANE1__PROVISION.getKeyword()));
      } else {
        ocsParameters.setPeerChannel((String) filteredPeerParameters.get(Parameters.Parameter.CHANNEL__PROVISION.getKeyword()));
      }
    }
    if (ocs.isProtected()) {
      ocsParameters.setProtStartChannel((String) provParams.getStart().getProtNetworkPortProps().get(Parameters.Parameter.CHANNEL__PROVISION.getKeyword()));
      ocsParameters.setProtPeerChannel((String) provParams.getPeer().getProtNetworkPortProps().get(Parameters.Parameter.CHANNEL__PROVISION.getKeyword()));
    }
  }

  private void mapIntermediateEntities(OCSProcessingParameters ocsParameters, SMProvParams provParams, OpticalChannelService ocs) {
    if (provParams.getProtMechanism().equals(ProtMechanism.NO_PROTECTION)) {
      ocsParameters.setWorkingIntermediateEntities(provParams.getWorkingIntermediateEntities());
    } else if (provParams.getProtMechanism().equals(ProtMechanism.CLIENT_CHANNEL_CARD_PROTECTION)) {
      if (provParams.isProtectingOCS()) {
        ocsParameters.setWorkingIntermediateEntities(provParams.getProtectedIntermediateEntities());
      } else {
        ocsParameters.setWorkingIntermediateEntities(provParams.getWorkingIntermediateEntities());
      }
    } else if (ocs.isProtected()) {
      ocsParameters.setWorkingIntermediateEntities(provParams.getWorkingIntermediateEntities());
      ocsParameters.setProtectedIntermediateEntities(provParams.getProtectedIntermediateEntities());
    }
  }

  protected final void createRestorationParam(Integer restoreType, final SPProperties properties, boolean isModify) {
    if (restoreType == Definition.RestorationType.ALL_PATHS.getId()) {
      properties.set(Parameters.Parameter.RESTNTYPE.getKeyword(), MIBFSP_R7Enums.DeployProvTunnelWdmTable.RESTORATION_TYPE_ALL);
    } else if (restoreType == Definition.RestorationType.WORKING_PATH.getId()) {
      properties.set(Parameters.Parameter.RESTNTYPE.getKeyword(), MIBFSP_R7Enums.DeployProvTunnelWdmTable.RESTORATION_TYPE_PRIMARY);
    } else if (restoreType == Definition.RestorationType.NONE.getId() && isModify) {
      // Only set this for modify (??) It is the default anyway, and sometimes setting it doesn't work. (FNM6589)
      properties.set(Parameters.Parameter.RESTNTYPE.getKeyword(), MIBFSP_R7Enums.DeployProvTunnelWdmTable.RESTORATION_TYPE_NONE);
    }
  }

  protected final void createExtendedRestorationParams(OpticalChannelService ocs, SPProperties properties) {
      properties.set(Parameters.Parameter.RESTNMODE.getKeyword(), ocs.getRestorationMode());
      properties.set(Parameters.Parameter.RVSNTYPE.getKeyword(), ocs.getReversionMode());
  }

  protected boolean isCCCPAndAtLeastOneRouteIsEmpty(SMProvParams provParams, List<OpticalChannelService> ocsList) {
    return (provParams.getProtMechanism() == ProtMechanism.CLIENT_CHANNEL_CARD_PROTECTION && ocsList.size() == 2)
            && (ocsList.get(1).getRoute().isEmpty() || ocsList.get(0).getRoute().isEmpty());
  }

  protected void reorderOCSes(List<OpticalChannelService> opticalChannelServices) {
    opticalChannelServices.add(0, opticalChannelServices.remove(1));
  }

  protected boolean checkTunnelAndHandleMultilayerADM(OpticalChannelService ocs, boolean isProtectionOCS, boolean excludePath, SMProvParams provParams,
                                                      List<OpticalChannelService> workList, List<OpticalChannelService> protList) throws SMProvException {
    if (checkIfTunnelIsProvisioned(ocs)) {
      if (excludePath) {
        provParams.excludeOCSPath(ocs.getStartNEID(), ocs.getPeerNEID(), ocs.getRoute());
      }
      return false;
    }
    Iterator<OpticalDataService> it = connectionDAO.getContainedSubChConnections(ocs.getId()).iterator();
    if (it.hasNext()) {
      throw new SMProvException(it.next().getName() + " should be deleted first. Tunnel no longer exists on NE.");
    }

    handleProtection(ocs, isProtectionOCS, provParams, workList, protList);
    return true;
  }

  protected void handleProtection(OpticalChannelService ocs, boolean isProtectionOCS, SMProvParams provParams,
                                  List<OpticalChannelService> workList, List<OpticalChannelService> protList) {
    if (provParams.isMultiLayerADM()) {
      if (protList != null && protList.contains(ocs)) {
        provParams.setProtectingOCS(true);
        provParams.setFirstOCS(ocs == protList.get(0));
        provParams.setLastOCS(ocs == protList.get(protList.size() - 1));
      } else {
        provParams.setProtectingOCS(false);
        provParams.setFirstOCS(ocs == workList.get(0));
        provParams.setLastOCS(ocs == workList.get(workList.size() - 1));
      }
    } else {
      provParams.setProtectingOCS(isProtectionOCS);
      provParams.setFirstOCS(true);
      provParams.setLastOCS(true);
    }
  }

  protected boolean isExcludePath(SMProvParams provParams, List<OpticalChannelService> opticalChannelServices) {
    boolean excludePath = isCCCPAndAtLeastOneRouteIsEmpty(provParams, opticalChannelServices);
    if (excludePath && !opticalChannelServices.get(1).getRoute().isEmpty()) {
      reorderOCSes(opticalChannelServices);
    }
    return excludePath;
  }

  @Override
  public void deleteSpurLinkOCS(OpticalChannelService ocs) throws SMProvException {
    try {
      serviceManagerFacade.deleteService(ocs.getId()); // Delete Track
    } catch (SMException e) {
      throw new SMProvException(e.getMessage(), e);
    }
  }

  protected final List<LineDBImpl> getLines(RoutePreview routePreview) {
    List<LineDBImpl> lineDBs = new ArrayList<>();
    int lastLineID = -1;
    for (EntityRef entityRef : routePreview.getEntityRefs()) {
      EntityDBImpl entityDB = EntityDAO.get(entityRef);
      if (entityDB instanceof PortFSP_R7VirtualDBImpl) {
        LineDBImpl lineDB = lineDao.getLineByOLId(entityDB.getNeID(), entityDB.getEntityIndex());
        if (lineDB != null && lineDB.getId() != lastLineID) {
          lineDBs.add(lineDB);
          lastLineID = lineDB.getId();
        }
      }
    }
    return lineDBs;
  }

  protected final LineDBImpl getLineByEndpointLabelAndNeId(String label, int neId) {
    return lineDao.getLineByEndpointLabelAndNeId(label, neId);
  }
}
