/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: adaemmig
 */

package com.adva.nlms.mediation.evtProc;

import com.adva.fm.api.dto.AppNetworkMgmtEvent;
import com.adva.fm.api.dto.Disposition;
import com.adva.fm.api.dto.EventResource;
import com.adva.fm.api.dto.Impairment;
import com.adva.fm.api.dto.appenc.AppENCAlarmEvent;
import com.adva.fm.api.dto.appenc.LifecycleEvent;
import com.adva.fm.api.dto.appenc.ObjectCreation;
import com.adva.fm.api.dto.appenc.ObjectDeletion;
import com.adva.fm.api.dto.Origin;
import com.adva.nlms.common.AlarmTrapType;
import com.adva.nlms.common.AlarmTypeHandlerImpl;
import com.adva.nlms.common.EventDescription;
import com.adva.nlms.common.NO_TIMESTAMP;
import com.adva.nlms.common.NetworkAlarmTypeProperty;
import com.adva.nlms.common.NetworkEventTypeHandlerImpl;
import com.adva.nlms.common.NetworkEventTypeProperty;
import com.adva.nlms.common.TrapType;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.config.ModuleType;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.event.DbChgCategoryType;
import com.adva.nlms.common.event.EventDetectionType;
import com.adva.nlms.common.event.EventSeverity;
import com.adva.nlms.common.event.EventStatus;
import com.adva.nlms.common.event.EventType;
import com.adva.nlms.common.event.TrapParameterID;
import com.adva.nlms.common.event.WorkingProtectionFlag;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.commondefinition.ResourceType;
import com.adva.nlms.commondefinition.namerepresentation.NameRepresentation;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementDBImpl;
import com.adva.nlms.mediation.config.NetworkElementImpl;
import com.adva.nlms.mediation.config.NoSuchNetworkElementException;
import com.adva.nlms.mediation.config.RegisteredNetworkElements;
import com.adva.nlms.mediation.event.EventAssocObjectId;
import com.adva.nlms.mediation.event.EventDTO;
import com.adva.nlms.mediation.event.EventLogger;
import com.adva.nlms.mediation.event.EventMultiVarDTO;
import com.adva.nlms.mediation.mltopologymodel.service.intent.ServiceIntentDAO;
import com.adva.nlms.mediation.mltopologymodel.service.intent.implementation.db.ServiceIntentDBImpl;
import com.adva.nlms.mediation.topology.TopLevelSubnetHdlrApi;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.UUID;

@Component
public class EvtProcHelper {

  private final TopLevelSubnetHdlrApi topLevelSubnetHdlrApi;
  private final RegisteredNetworkElements registeredNetworkElements;
  private final ServiceIntentDAO serviceIntentDAO;

  private int topLevelSubnetId = 0;

  private static final Logger log = LogManager.getLogger(EventProcFacadeImpl.class.getName());
  private final Logger logSbi = EventLogger.getLogger(EventLogger.Category.itf_sbi);

  private static final EventMultiVarDTO[] EMPTY_EVENT_MULTIVAR_PROPS = new EventMultiVarDTO[0];

  private static final String CUSTOMER = "CUSTOMER";
  private static final String CONTAINER_ID = "containerId";

  @Autowired
  public EvtProcHelper(RegisteredNetworkElements registeredNetworkElements, TopLevelSubnetHdlrApi topLevelSubnetHdlrApi,
                       ServiceIntentDAO serviceIntentDAO) {
    this.registeredNetworkElements = registeredNetworkElements;
    this.topLevelSubnetHdlrApi = topLevelSubnetHdlrApi;
    this.serviceIntentDAO = serviceIntentDAO;
  }

  EventDTO createServerAlarmDTO(int trapId,
                                EventType type,
                                Object neObject,
                                EventDTO.Param... params)
  {
    AlarmTrapType alarmTrapDescriptor;
    try{
      alarmTrapDescriptor = AlarmTypeHandlerImpl.getInstance().getTrapDescriptor(NeTypeIds.NETWORK_ELEMENT_TYPE_ANY, trapId);
    }
    catch (NoSuchElementException nsee) {
      log.error("FM:addServerAlarm() alarm definition not found for " + trapId + " [eventType="+type+", neObject="+neObject+", params="+params+"]", nsee);
      return null;
    }

    EventSeverity severity = alarmTrapDescriptor.getEventSeverityType(NetworkAlarmTypeProperty.SEVERITY_ASSIGNMENT.NO_SERVICE);
    String shortName = alarmTrapDescriptor.getAlarmShortName();
    String parameter = alarmTrapDescriptor.getMessage(EventDescription.entityToBeDetermined);

    EventDTO eventDTO = createEmptyEventDTO();
    eventDTO.setTrapType(EventDetectionType.SERVER, type, false);
    eventDTO.setTrapInfo(severity, trapId, trapId, shortName, parameter);
    eventDTO.category = alarmTrapDescriptor.getCategoryType();
    eventDTO.alarmClass = alarmTrapDescriptor.getAlarmClass();
    if (neObject instanceof NetworkElement) {
      NetworkElement ne = (NetworkElement)neObject;
      eventDTO.setSrcNE(ne.getIPAddress(), ne.getID(), ne.getNetworkElementType(), ne.getParentID());
    }
    else if (neObject instanceof NetworkElementDBImpl) {
      NetworkElementDBImpl ne = (NetworkElementDBImpl)neObject;
      eventDTO.setSrcNE(ne.getIPAddress(), ne.getId(), ne.getNetworkElementType(), ne.getSubnet().getId());
    } else {
      eventDTO.setSubnet(getTopLevelSubnetID());
    }
    eventDTO.setParams(params);
    return eventDTO;
  }

  EventDTO createSecurityEventDTO(EventSeverity severity,
                                  String parameter,
                                  String shortName,
                                  EventDTO.Param... params)
  {
    EventDTO eventDTO = createEmptyEventDTO();
    eventDTO.setTrapType(EventDetectionType.SERVER, EventType.SYSTEM, true);
    eventDTO.setTrapInfo(severity, 0, 0, shortName, parameter);
    eventDTO.setParams(params);

    return eventDTO;
  }

  EventDTO createStatusEventDTO(int trapID, EventType type, EventSeverity severity, String parameter, String shortName,
                                NetworkElement ne, EventDTO.Param... params) {
    EventDTO eventDTO = createEmptyEventDTO();
    eventDTO.setTrapType(EventDetectionType.CONF_STATUS_UPDATE, type, false);
    eventDTO.setTrapInfo(severity, trapID, 0, shortName, parameter);
    if (ne != null) {
      eventDTO.setSrcNE(ne.getIPAddress(), ne.getID(), ne.getNetworkElementType(), ne.getParentID());
    } else {
      eventDTO.setSubnet(getTopLevelSubnetID());
    }
    eventDTO.setParams(params);
    return eventDTO;
  }

  EventDTO createServerEventDTO(int trapId, EventType type,
                                Object neObject, EventDTO.Param... params) {
    TrapType trapDescriptor;
    try{
      trapDescriptor = NetworkEventTypeHandlerImpl.getInstance().getTrapDescriptor(NeTypeIds.NETWORK_ELEMENT_TYPE_ANY, MIB.FSPNm.TrapMibPrefix.OID_NMS_ENTERPRISE, trapId);
    }
    catch (NoSuchElementException nsee) {
      log.error("FM:addServerEvent() event definition not found for " + trapId);
      return null;
    }

    EventSeverity severity = trapDescriptor.getEventSeverityType(NetworkEventTypeProperty.NO_SERVICE_ASSIGNMENT);
    String shortName = trapDescriptor.getEventShortName();
    String parameter = trapDescriptor.getMessage(EventDescription.entityToBeDetermined);

    EventDTO eventDTO = createEmptyEventDTO();
    eventDTO.setTrapType(EventDetectionType.SERVER, type, false);
    eventDTO.setTrapInfo(severity, trapId, 0, shortName, parameter);
    eventDTO.enterprise = MIB.FSPNm.TrapMibPrefix.OID_NMS_ENTERPRISE;

    if (neObject instanceof NetworkElement) {
      NetworkElement ne = (NetworkElement)neObject;
      eventDTO.setSrcNE(ne.getIPAddress(), ne.getID(), ne.getNetworkElementType(), ne.getParentID());
    }
    else if (neObject instanceof NetworkElementDBImpl) {
      NetworkElementDBImpl ne = (NetworkElementDBImpl)neObject;
      eventDTO.setSrcNE(ne.getIPAddress(), ne.getId(), ne.getNetworkElementType(), ne.getSubnet().getId());
    } else {
      eventDTO.setSubnet(getTopLevelSubnetID());
    }
    eventDTO.setParams(params);

    return eventDTO;
  }

  EventDTO createServerEventDTO(int trapID, EventType type, EventSeverity severity,
                                       String parameter, String shortName,
                                       NetworkElement ne, EventDTO.Param... params) {
    EventDTO eventDTO = createEmptyEventDTO();
    eventDTO.setTrapType(EventDetectionType.SERVER, type, false);
    eventDTO.setTrapInfo(severity, trapID, 0, shortName, parameter);
    if (ne != null) {
      eventDTO.setSrcNE(ne.getIPAddress(), ne.getID(), ne.getNetworkElementType(), ne.getParentID());
    } else {
      eventDTO.setSubnet(getTopLevelSubnetID());
    }
    eventDTO.setParams(params);

    return eventDTO;
  }

  private static EventDTO createEmptyEventDTO() {
    return new EventDTO( 0, 0, 0, 0, 0,                   // ID, Log-index, trap-ID, alarm-type, category
                         EventType.TRASHED, false,        // type, security-event
                         EventSeverity.OK,                // severity
                         System.currentTimeMillis(),      // NMS Timestamp
                         NO_TIMESTAMP.value,              // NE Timestamp
                         NO_TIMESTAMP.value,              // NMS Clearing Timestampe
                         NO_TIMESTAMP.value,              // NE Clearing Timestampe
                         0, 0,                            // clearing-alarm, first-raised-alarm
                         new StringBuilder(),             // parameter
                         StringUtils.EMPTY,               // shortName
                         false, false, 0, "",       // text-incomplete, ack, ack-timestamp, disable
                         EventStatus.Impairment.NONSERVICE_AFFECTING.getDbValue(),// impairment
                         EventDetectionType.SERVER,       // detection
                         EventStatus.Correlation.PRIMARY.getType(),             // correlation
                         0, 0,                            // root-cause, mask
                         StringUtils.EMPTY, 0, 0, 0, 0,   // source NE (IP, ID, type), subnet-ID, line-ID
                         //StringUtils.EMPTY, 0,                 // counterpart NE (IP, ID)
                         ArrayUtils.EMPTY_INT_ARRAY, ArrayUtils.EMPTY_INT_ARRAY,// connIDs, parentIDs
                         StringUtils.EMPTY, StringUtils.EMPTY,      // Entity-Descr (short, full)
                         EntityIndex.ZERO, ModuleType.NO_MODULE, // Module-Index, Module-Type
                         "N/A",
                         EntityIndex.ZERO, EntityIndex.ZERO, TrapParameterID.valueOf(0), 0, StringUtils.EMPTY,        // port-index, object-index, parameter-ID, new-value, new-string-value
                         0, 0, StringUtils.EMPTY,              // location, direction, enterprise
                         StringUtils.EMPTY, StringUtils.EMPTY,      // source-name, entity-alias
                         ArrayUtils.EMPTY_INT_ARRAY,      // object-indexes
                         EMPTY_EVENT_MULTIVAR_PROPS,      // multivar-list
                         StringUtils.EMPTY,                    // mtosi-type
                         false, WorkingProtectionFlag.NA, // is-peer, path
                         false, StringUtils.EMPTY,             // is-faulted-service, customer-name
                         false,                           // isArcSupport
                         new int[]{0}, new int[]{0}, 0,                        // syncNodeIDs, syncNcdIDs, syncRouteID
                          "", "", "");  // pventityid, pvsourceid, pvsourcetype
  }

  EventDTO getConnectionEventProperties(EventType type, boolean securityEvent,
                                                EventSeverity severity,
                                                String parameter,
                                                String shortName,
                                                int connID, int parentID,
                                                int sourceneID, int subnetID) {
    EventDTO event = new EventDTO(0, 0, 0, 0, 0,
      type, securityEvent,
      severity,
      System.currentTimeMillis(),
      NO_TIMESTAMP.value,
      NO_TIMESTAMP.value,
      NO_TIMESTAMP.value,
      0, 0,
      new StringBuilder(parameter), // event description
      shortName, // short name of the event
      false, false, 0, "",
      EventStatus.Impairment.NONSERVICE_AFFECTING.getDbValue(),
      EventDetectionType.SERVER,
      EventStatus.Correlation.PRIMARY.getType(),
      0, 0,
      "", sourceneID, // source ne
      0, // ne type
      (subnetID > 0) ? subnetID : getTopLevelSubnetID(),
      0,  // line-id
//      "", 0,
      ArrayUtils.EMPTY_INT_ARRAY, // connectionIDs
      ArrayUtils.EMPTY_INT_ARRAY, // parentsIDs
      "", "",
      EntityIndex.ZERO, ModuleType.NO_MODULE,  "N/A",
      EntityIndex.ZERO, EntityIndex.ZERO, TrapParameterID.valueOf(0), 0, "", 0, 0, "", "", "",
      ArrayUtils.EMPTY_INT_ARRAY, EMPTY_EVENT_MULTIVAR_PROPS, "", false,
      WorkingProtectionFlag.NA, false, "", false, new int[]{0}, new int[]{0}, 0,
     "","","");

    event.connectionIDs = (connID > 0) ? new int[]{connID} : ArrayUtils.EMPTY_INT_ARRAY;
    event.parentsIDs = (parentID > 0) ? new int[]{parentID} : ArrayUtils.EMPTY_INT_ARRAY;

    return event;
  }

  EventDTO getServiceSecurityEventDto(EventSeverity eventSeverity, String eventShortName,
                                      List<Integer> eventParentIds, String connectivityServiceName, String customerName,
                                      String eventDescription) {
    EventDTO event = createEmptyEventDTO();
    event.type = EventType.SYSTEM;
    event.securityEvent = true;
    event.severity = eventSeverity;
    event.shortName = eventShortName;
    event.sourceName = connectivityServiceName;
    event.customerName = customerName;
    event.setText(eventDescription);
    event.parentsIDs = eventParentIds.stream().mapToInt(i -> i).toArray();

    return event;
  }

  private int getTopLevelSubnetID() {
    if (topLevelSubnetId != 0)
      return topLevelSubnetId;

    topLevelSubnetId = topLevelSubnetHdlrApi.getTopLevelSubnetID();

    return topLevelSubnetId;
  }

  public void setSourceNe(EventDTO event, String... sourceIps) {
    if (event.sourceNE_ID > 0)
      return;

    for (String sourceIp : sourceIps) {
      try {
        if (sourceIp != null) {
          // we need this to process traps for unrecognized ips later
          event.sourceNE_IP       = sourceIp; // ne.getIPAddress()
          NetworkElementImpl ne = registeredNetworkElements.getNEImpl(sourceIp,false); // store ne
          event.sourceNE_ID       = ne.getID();
          event.sourceNEType      = ne.getNetworkElementType();
          event.subnetID          = ne.getParentID();

          return;
        }
      } catch (/*NoSuchException |*/ NoSuchNetworkElementException x) {
        EvtProcInfo.Sample.DISCARD_EVENT_WRONG_IP.counter++; // monitoring counter
        EventLogger.logWarnOnce(logSbi, EventLogger.createCacheKey(sourceIp),
                "handleTrap(): Source NE not found", event, null);
      }
    }
  }

  public static boolean isSecurityEvent(final String securityEvent) {
    return (securityEvent != null && securityEvent.equals("true"));
  }

  public EventDTO createEvent(AppNetworkMgmtEvent appEvent) {
    EventDTO eventDTO = createEmptyEventDTO();

    // set common attributes
    setAppCommonAttributes(eventDTO, appEvent);

    // set Event (Lifecycle) or Alarm specific Attributes
    boolean isLifecycleEvent = (appEvent instanceof LifecycleEvent);
    if (isLifecycleEvent)
      setAppLifecycleEventAttributes(eventDTO, appEvent);

    // set NE/ENC specific Attributes
    boolean isEncEvent = (appEvent instanceof LifecycleEvent);
    if (isEncEvent)
      setAppEncAttributes(eventDTO, appEvent);

    // Alarm specific attributes
    if (appEvent instanceof AppENCAlarmEvent appENCAlarmEvent) {
      setAppEncAlarmAttributes(eventDTO, appENCAlarmEvent);
    }

    return eventDTO;
  }

  private void setAppCommonAttributes(EventDTO eventDTO, AppNetworkMgmtEvent appEvent) {
    // check for missing attributes
    if (appEvent.getEventTime() <= 0 ||
        appEvent.getOrigin() == null ||
        appEvent.getDisposition() == null ||
        appEvent.getEventName() == null ||
        appEvent.getPrimaryResource() == null ||
        appEvent.getPrimaryResource().resourceType() == null ||
        appEvent.getPrimaryResource().resourceId() == null) {
      throw new IllegalArgumentException("Inconsistency attributes in AppNetworkMgmtEvent");
    }

    String eventName = appEvent.getEventName();
    Disposition eventDisposition = appEvent.getDisposition();

    AlarmTrapType alarmTrapDescriptor = null;
    if (eventDisposition == Disposition.ARM || eventDisposition == Disposition.CLR) {
      try {
        alarmTrapDescriptor = AlarmTypeHandlerImpl.getInstance().getTrapDescriptorByTrapShortName(NeTypeIds.NETWORK_ELEMENT_TYPE_ANY, eventName);
      } catch (NoSuchElementException nsee) {
        throw new IllegalArgumentException("Missing attributes in AppNetworkMgmtEvent. Could not resolve alarmTrapDescriptor for " + eventName);
      }
    }

    eventDTO.nmsTimeStamp = appEvent.getEventTime();
    eventDTO.detectionType = switch (appEvent.getOrigin()) {
      case TRAP -> EventDetectionType.TRAP;
      case LOG -> EventDetectionType.TRAP_LOG;
      case UPD -> EventDetectionType.CONF_STATUS_UPDATE;
      case SYS -> EventDetectionType.SERVER;
    };
    eventDTO.type = switch (eventDisposition) {
      case ARM -> EventType.RAISED;
      case CLR -> EventType.CLEARED;
      case TRN -> (appEvent.getOrigin() == Origin.SYS) ? EventType.SYSTEM : EventType.TRANSIENT;
    };


    // Trap ID comes from the FM alarm definition
    if (alarmTrapDescriptor != null) {
      if (eventDisposition == Disposition.ARM) {
        eventDTO.setTrapID(alarmTrapDescriptor.getRaisingTrapNumber());
      } else {
        eventDTO.setTrapID(alarmTrapDescriptor.getClearingTrapNumber());
      }
    }

    eventDTO.securityEvent = appEvent.isSecurity();
    eventDTO.shortName = eventName;
    if (appEvent.getSeverity() != null) {
      eventDTO.severity = switch (appEvent.getSeverity()) {
        case CRITICAL -> EventSeverity.CRITICAL;
        case MAJOR -> EventSeverity.MAJOR;
        case MINOR -> EventSeverity.MINOR;
        case WARNING -> EventSeverity.WARNING;
        case INFORMATION -> EventSeverity.INFORMATION;
      };
    } else if (alarmTrapDescriptor != null){
      eventDTO.severity = alarmTrapDescriptor.getEventSeverityType(NetworkAlarmTypeProperty.SEVERITY_ASSIGNMENT.NO_SERVICE);
    } else {
      throw new IllegalArgumentException("Could not get event severity");
    }
    if (appEvent.getDescription() != null) {
      eventDTO.setText(appEvent.getDescription());
    } else if (alarmTrapDescriptor != null) {
      eventDTO.setText(alarmTrapDescriptor.getMessage());
    } else {
      throw new IllegalArgumentException("Could not get event description");
    }

    // set Event Labels
    if (StringUtils.isNotEmpty((String) MapUtils.getObject(appEvent.getEventLabels(), CUSTOMER))) {
      eventDTO.customerName = (String) appEvent.getEventLabels().get(CUSTOMER);
    }

    EventResource eventResource = appEvent.getPrimaryResource();
    if (eventResource.resourceType() == ResourceType.CONNECTIVITY_SERVICE) {
      setConnectivityServiceEventAttributes(eventDTO, appEvent, eventResource);
    }

    for (EventResource secResource : ListUtils.emptyIfNull(appEvent.getSecondaryResources())) {
      if (secResource.resourceType() == null ||
          secResource.resourceId() == null) {
        throw new IllegalArgumentException("Inconsistency attributes in AppNetworkMgmtEvent (secondary resources)");
      }
      if (secResource.resourceType() == ResourceType.CONNECTION) {
        setConnectionEventAttributes(eventDTO, secResource);
      }
    }
  }

  private void setConnectivityServiceEventAttributes(EventDTO eventDTO, AppNetworkMgmtEvent appEvent, EventResource eventResource) {
    UUID uuid = UUID.fromString(eventResource.resourceId());
    ServiceIntentDBImpl serviceIntent = serviceIntentDAO.getServiceIntentByUUID(uuid);
    if (serviceIntent == null) {
      // Only set UUID (e.g. service intent is not persisted yet, will be added in SetServiceIntentDbIdTask)
      eventDTO.addAssocUuidObject(uuid, EventAssocObjectId.ObjectType.EOD_CONNECTIVITY_SERVICE);
    } else {
      // Set connectivity Service UUID and DB id if ServiceIntentDBImpl exists
      eventDTO.addAssociatedObject(EventAssocObjectId.ObjectType.EOD_CONNECTIVITY_SERVICE, serviceIntent.getId(), uuid);
    }

    if (StringUtils.isNotEmpty((String) MapUtils.getObject(appEvent.getEventLabels(), CONTAINER_ID))) {
      UUID containerId = UUID.fromString((String) MapUtils.getObject(appEvent.getEventLabels(), CONTAINER_ID));
      eventDTO.addAssocUuidObject(containerId, EventAssocObjectId.ObjectType.EOD_CONNECTIVITY_SERVICE_FOLDER);
    }
    Optional<String> name = MapUtils.emptyIfNull(eventResource.resourceNames()).entrySet().stream().filter(o-> o.getKey()==NameRepresentation.USER_LABEL && StringUtils.isNotEmpty(o.getValue())).map(Map.Entry::getValue).findFirst();
    if (name.isEmpty()) {
      throw new IllegalArgumentException("Inconsistency attributes in AppNetworkMgmtEvent (PrimaryResource.USER_LABEL is missing)");
    }
    eventDTO.sourceName = name.get();
    eventDTO.serviceName = name.get();
  }

  private void setConnectionEventAttributes(EventDTO eventDTO, EventResource secResource) {
    eventDTO.addAssocUuidObject(UUID.fromString(secResource.resourceId()), EventAssocObjectId.ObjectType.MLT_SERVICE);
  }

  private void setAppLifecycleEventAttributes(EventDTO eventDTO, AppNetworkMgmtEvent appEvent) {
    eventDTO.dbChgCategory =
      (appEvent instanceof ObjectCreation) ? DbChgCategoryType.CREATE :
      (appEvent instanceof ObjectDeletion) ? DbChgCategoryType.DELETE : DbChgCategoryType.UPDATE;
  }

  private void setAppEncAttributes(EventDTO eventDTO, AppNetworkMgmtEvent appEvent) {
    eventDTO.setSubnet(getTopLevelSubnetID());
  }

  private void setAppEncAlarmAttributes(EventDTO eventDTO, AppENCAlarmEvent alarmEvent) {
    if (alarmEvent.getImpairment() != null) {
      eventDTO.impairment = alarmEvent.getImpairment() != Impairment.NONSERVICE_AFFECTING;
    }
  }
}
