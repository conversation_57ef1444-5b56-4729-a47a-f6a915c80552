/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: abaso
 */

package com.adva.nlms.mediation.event.api;

import com.adva.fm.api.dto.AppNetworkMgmtEvent;
import com.adva.nlms.common.logging.ApiLogger;
import com.adva.nlms.infrastucture.security.permission.api.PermissionAction;
import com.adva.nlms.intrastructure.notification.authorization.MessagePermissionAuthorizationHeader;
import com.adva.nlms.intrastructure.notification.authorization.MessageServiceAuthorizationHeader;
import com.adva.nlms.intrastructure.notification.authorization.MessageSubnetAuthorizationHeader;
import com.adva.nlms.intrastructure.notification.authorization.PermissionFilter;
import com.adva.nlms.intrastructure.notification.authorization.ServiceFilter;
import com.adva.nlms.intrastructure.notification.authorization.SubnetFilter;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.common.persistence.MDPersistenceHelper;
import com.adva.nlms.mediation.infrastructure.concurrent.AdvaExecutors;
import com.adva.nlms.mediation.infrastructure.concurrent.NamedThreadFactory;
import com.adva.nlms.mediation.messaging.kafka.conf.Topics;
import com.adva.nlms.mediation.topology.SubnetDBImpl;
import io.github.springwolf.core.asyncapi.annotations.AsyncOperation;
import io.github.springwolf.core.asyncapi.annotations.AsyncPublisher;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.internals.RecordHeader;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.kafka.core.KafkaOperations;
import org.springframework.messaging.handler.annotation.Payload;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutorService;

public class FmEventUpdateSenderAdapter {

  private static final ApiLogger apiLogger = new ApiLogger(ApiLogger.loggerCorFmApi, Level.DEBUG, ApiLogger.logPrefixCorFmSnd);
  private static final Logger LOGGER = LogManager.getLogger(FmEventUpdateSenderAdapter.class);
  private static final ExecutorService fmEventUpdateKafkaExecutor = AdvaExecutors.newSingleThreadExecutor(new NamedThreadFactory("FmEventUpdateKafka"), true);

  private final KafkaOperations<String, AppNetworkMgmtEvent> kafkaTemplate;
  private final Topics topics;

  public FmEventUpdateSenderAdapter(KafkaOperations<String, AppNetworkMgmtEvent> kafkaTemplate, Topics topics) {
    this.kafkaTemplate = kafkaTemplate;
    this.topics = topics;
  }

  public void sendFmEventUpdate(AppNetworkMgmtEvent appNetworkMgmtEvent, int subnetId, List<UUID> csFolderUuidList) {
    List<RecordHeader> headers = new ArrayList<>();
    UUID subnetUuid = getSubnetUuid(subnetId);
    if (subnetUuid != null) {
      SubnetFilter subnetFilter = SubnetFilter.fromSubnets(List.of(subnetUuid));
      headers.add(new MessageSubnetAuthorizationHeader(subnetFilter));
    }
    if (!csFolderUuidList.isEmpty()) {
      ServiceFilter messageServiceFilter = ServiceFilter.fromServiceGroups(csFolderUuidList);
      headers.add(new MessageServiceAuthorizationHeader(messageServiceFilter));
    }
    PermissionFilter messagePermissionFilter = PermissionFilter.fromPermissions(List.of(PermissionAction.BrowseEvents));
    headers.add(new MessagePermissionAuthorizationHeader(messagePermissionFilter));
    sendKafkaMessage(appNetworkMgmtEvent, headers);
    apiLogger.log(topics.getFmEventUpdateTopicName(), subnetUuid, csFolderUuidList, appNetworkMgmtEvent.toShortString());
  }

  @AsyncPublisher(operation = @AsyncOperation(
          channelName = "#{topics.fmEventUpdateTopicName}",
          description = "FM event update for sending status to Web/Tapi for all data"
  ))
  private void sendKafkaMessage(@Payload AppNetworkMgmtEvent appNetworkMgmtEvent, List<RecordHeader> headers) {
    fmEventUpdateKafkaExecutor.submit(() -> {
      try {
        ProducerRecord<String, AppNetworkMgmtEvent> producerRecord = new ProducerRecord<>(topics.getFmEventUpdateTopicName(), appNetworkMgmtEvent);
        headers.forEach(header -> producerRecord.headers().add(header));
        var f = kafkaTemplate.send(producerRecord);
        f.exceptionally(throwable -> {
                  LOGGER.error(throwable);
                  return null;
                }
        );
      } catch (RuntimeException e) {
        LOGGER.warn(e.getMessage(), e);
      }
    });
  }

  @MDPersistenceContext
  private UUID getSubnetUuid(int subnetId) {
    SubnetDBImpl subnetDB = MDPersistenceHelper.find(SubnetDBImpl.class, subnetId);
    return (subnetDB != null) ? subnetDB.getUuid() : null;
  }
}

