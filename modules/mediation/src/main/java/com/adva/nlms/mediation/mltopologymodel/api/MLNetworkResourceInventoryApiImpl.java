/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: andred
 */

package com.adva.nlms.mediation.mltopologymodel.api;

import com.adva.apps.sm.Definition;
import com.adva.eod.nrim.api.dto.ConnectionDto;
import com.adva.eod.nrim.api.dto.ConnectionFilters;
import com.adva.eod.nrim.api.dto.DeviceEndpointDto;
import com.adva.eod.nrim.api.dto.EntityDto;
import com.adva.eod.nrim.api.dto.PotentialConnectionDto;
import com.adva.eod.nrim.api.dto.PotentialConnectionEndPointDto;
import com.adva.eod.nrim.api.dto.RouteDto;
import com.adva.eod.nrim.api.dto.ServiceInterfacePointDto;
import com.adva.eod.nrim.api.dto.SupportedLayerProtocolQualifier;
import com.adva.eod.nrim.api.dto.SwitchDto;
import com.adva.eod.nrim.api.dto.TopConnectionUpdateDto;
import com.adva.eod.nrim.api.in.ConnectionsApi;
import com.adva.eod.nrim.api.in.EndpointTranslationApi;
import com.adva.eod.nrim.api.in.EndpointsApi;
import com.adva.nlms.common.AdministrationStateType;
import com.adva.nlms.common.logging.ApiLogger;
import com.adva.nlms.common.mltopologymodel.enums.MLNewPrimaryLifecycleState;
import com.adva.nlms.common.mltopologymodel.enums.MLNewSecondaryLifecycleState;
import com.adva.nlms.common.rest.MDRestPath;
import com.adva.nlms.infrastucture.security.SecurityTools;
import com.adva.nlms.infrastucture.security.permission.api.PermissionAction;
import com.adva.nlms.mediation.common.mltopologymodel.dto.MLTrailDTO;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.common.persistence.MDPersistenceHelper;
import com.adva.nlms.mediation.common.persistence.MDTransactional;
import com.adva.nlms.mediation.common.rest.MDRestComponent;
import com.adva.nlms.mediation.mltopologymodel.core.concurrent.api.MLTaskExecutorService;
import com.adva.nlms.mediation.mltopologymodel.model.dao.MLNetworkResourceInventoryDAO;
import com.adva.nlms.mediation.mltopologymodel.model.dao.MLTopologyElementDAO;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLConnectionPointDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLEndPoint;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLLayerAdaptationDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLMonitoringSectionDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLPtpConnectionDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLPtpServiceDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLServiceDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLTrailDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLConnection;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLPath;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLNetworkLayer;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLTEPropertyKey;
import com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.tasks.MLServiceViewTask;
import com.adva.nlms.mediation.mltopologymodel.resources.MLEodAdminStateEntitiesMapper;
import com.adva.nlms.mediation.mltopologymodel.resources.MLEodConnectionProvider;
import com.adva.nlms.mediation.mltopologymodel.resources.MLEodConnectionsFilters;
import com.adva.nlms.mediation.mltopologymodel.resources.MLEodConnectionsPageHdlr;
import com.adva.nlms.mediation.mltopologymodel.sync.endpoint.LayerBrowserNrlProvider;
import com.adva.nlms.mediation.mltopologymodel.sync.endpoint.MLEndPointHandler;
import com.adva.nlms.mediation.mltopologymodel.sync.endpoint.MLEndPointHelper;
import com.adva.nlms.mediation.mltopologymodel.sync.endpoint.MLEodLinkHandler;
import com.adva.nlms.mediation.mltopologymodel.sync.endpoint.MLNodeEdgePointHandler;
import com.adva.nlms.mediation.mltopologymodel.sync.exception.MLPropertyException;
import com.adva.nlms.infrastucture.security.permission.api.Authorization;
import com.adva.nlms.mediation.topology.eod.NodeProvider;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.opticalparameters.api.enums.ProtectionType;
import com.adva.nlms.opticalparameters.api.enums.Role;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Swagger REST API Documentation NOTE:
 * <p>There is a defect in the swagger generator where specifying {@literal @}Schema(implementation = SOME.class)
 * does not work as expected for headers defined with {@literal @}Header(...)
 * (no 'schema' generated in the documentation for the header).</p>
 * <p>The workaround is use the following definition instead (all our headers are strings)
 * {@literal @}Schema(type = "string")</p>
 * @see <a href="https://github.com/springdoc/springdoc-openapi/issues/103">https://github.com/springdoc/springdoc-openapi/issues/103</a>
 * @see <a href="https://github.com/swagger-api/swagger-core/issues/2965">https://github.com/swagger-api/swagger-core/issues/2965</a>
 */
@Component
@MDRestComponent
@Path(MDRestPath.EOD.NRIM.PATH)
public class MLNetworkResourceInventoryApiImpl implements EndpointsApi, ConnectionsApi, EndpointTranslationApi {

  private static final Logger log = LogManager.getLogger(MLNetworkResourceInventoryApiImpl.class);

  private final MLNetworkResourceInventoryDAO nrimDAO;
  private final MLNodeEdgePointHandler nodeEdgePointHandler;
  private final MLEndPointHandler endPointHandler;
  private final MLEndPointHelper endPointHelper;
  private final MLEodConnectionsPageHdlr mlEodConnectionsPageHdlr;
  private final MLEodLinkHandler mlEodLinkHandler;
  private final MLEodConnectionProvider mlEodConnectionProvider;
  private final MLTopologyElementDAO mlTopologyElementDAO;
  private final NodeProvider nodeProvider;
  private final LayerBrowserNrlProvider layerBrowserNrlProvider;
  private final ApiLogger apiLogger;

  public MLNetworkResourceInventoryApiImpl(MLNetworkResourceInventoryDAO nrimDAO, MLNodeEdgePointHandler nodeEdgePointHandler,
                                           MLEndPointHandler endPointHandler, MLEndPointHelper endPointHelper,
                                           MLEodConnectionsPageHdlr mlEodConnectionsPageHdlr, MLEodLinkHandler mlEodLinkHandler,
                                           MLEodConnectionProvider mlEodConnectionProvider, MLTopologyElementDAO mlTopologyElementDAO,
                                           NodeProvider nodeProvider) {
    this.nrimDAO = nrimDAO;
    this.nodeEdgePointHandler = nodeEdgePointHandler;
    this.endPointHandler = endPointHandler;
    this.endPointHelper = endPointHelper;
    this.mlEodConnectionsPageHdlr = mlEodConnectionsPageHdlr;
    this.mlEodLinkHandler = mlEodLinkHandler;
    this.mlEodConnectionProvider = mlEodConnectionProvider;
    this.mlTopologyElementDAO = mlTopologyElementDAO;
    this.nodeProvider = nodeProvider;
    this.layerBrowserNrlProvider = new LayerBrowserNrlProvider(mlTopologyElementDAO, endPointHandler, mlEodLinkHandler);
    this.apiLogger = new ApiLogger(log, Level.INFO, ApiLogger.logPrefixEodNrimApi);
  }

  //==============================================================================
  //=== Endpoints API ============================================================
  //==============================================================================

  @Operation(
          operationId = "getServiceInterfacePoints",
          summary = "Read all Service Interface Points for a given Node or CSEP nrl, layer protocol qualifier and payload qualifier",
          description = "Provides a list of all Service Interface Points for a given Node or CSEP nrl, layer protocol qualifier and payload qualifier.",
          tags = { "Connection - NRIM" },
          parameters = {
                  @Parameter(name = "userID",
                          description = "User Id",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "sessionID",
                          description = "Session Id",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Accept",
                          description = "The HTTP Accept header",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Accept-Encoding",
                          description = "The HTTP Accept-Encoding header",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "X-Correlation-Id",
                          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together Rest and java calls in log files",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Encoding",
                          description = "The HTTP Content-Encoding header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Length",
                          description = "The HTTP Content-Length header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Type",
                          description = "The HTTP Content-Type header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Date",
                          description = "The HTTP Date header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Host",
                          description = "The HTTP Host header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "User-Agent",
                          description = "The HTTP User-Agent header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
          },
          responses = {
                  @ApiResponse(responseCode = "200", description = "List of Service Interface Points", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files"),
                          @Header(name = "Content-Length", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Length header"),
                          @Header(name = "Content-Encoding", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Encoding header"),
                          @Header(name = "Content-Type", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Type header"),
                          @Header(name = "Date", required = false, schema = @Schema(type = "string"),
                                  description = "The HTTP Date header"),
                          @Header(name = "Server", required = false, schema = @Schema(type = "string"),
                                  description = "The HTTP Server header"),
                          @Header(name = "Link", required = false, schema = @Schema(type = "string",
                                  description = "Link header can provide URLs to the first / previous / next / last pages of data if paging is used"))
                  }, content = {
                          @Content(mediaType = "application/json", array = @ArraySchema(schema=@Schema(implementation = ServiceInterfacePointDto.class)))
                  }),
                  @ApiResponse(responseCode = "400", description = "The missing or invalid data supplied for this endpoint", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files")
                  }, content = {
                          @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
                  }),
                  @ApiResponse(responseCode = "401", description = "The user is not authorized to access this endpoint", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files")
                  }, content = {
                          @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
                  })
          }
  )
  @GET
  @Path(MDRestPath.EOD.NRIM.SERVICE_INTERFACE_POINTS)
  @Authorization(anyOfPermissions = {PermissionAction.AddService, PermissionAction.ModifyService})
  @Override
  public List<ServiceInterfacePointDto> getServiceInterfacePoints(@QueryParam("nodeId") UUID nodeId,
                                                                  @Parameter(description = "Layer protocol qualifier enum value")
                                                                  @QueryParam("layerProtocol") String layerProtocol,
                                                                  @Parameter(description = "Payload qualifier, the enum value for payload qualifier")
                                                                  @QueryParam("payload") String payload,
                                                                  @QueryParam("protectionType") ProtectionType protectionType,
                                                                  @QueryParam("csep") String csep) {
    layerProtocol = SecurityTools.replaceCRLFs(layerProtocol);
    payload = SecurityTools.replaceCRLFs(payload);
    csep = SecurityTools.replaceCRLFs(csep);

    List<ServiceInterfacePointDto> result;
    if (nodeId == null || MLNetworkLayer.fromLayerQualifierString(layerProtocol).isEmpty()) {
      return apiLogger.passResultAndlog(Collections.emptyList(), nodeId, layerProtocol, payload, protectionType, csep);
    }

    // Explored Services: get SIP for given CSEP
    if (StringUtils.isNotEmpty(csep)) {
      result = endPointHandler.getServiceInterfacePoints(nodeId, csep);
      return apiLogger.passResultAndlog(result, nodeId, layerProtocol, payload, protectionType, csep);
    }

    // Provisioned Services: get SIPs for given NE & LPQ & PLQ
    String layerProtocolDbValue = MLEndPoint.getLayerProtocolDbValue(layerProtocol, payload);
    result = endPointHandler.getServiceInterfacePoints(nodeId, layerProtocolDbValue, protectionType);
    return apiLogger.passResultAndlog(result, nodeId, layerProtocol, payload, protectionType, csep);
  }

  @Operation(
    operationId = "getServiceInterfacePoints",
    summary = "Read all Supported Layer Protocol Qualifiers for a given SIP nrl",
    description = "Provides a list of all Supported Layer Protocol Qualifier for a given SIP CSEP nrl.",
    tags = { "Connection - NRIM" },
    parameters = {
      @Parameter(name = "userID",
        description = "User Id",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "sessionID",
        description = "Session Id",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Accept",
        description = "The HTTP Accept header",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Accept-Encoding",
        description = "The HTTP Accept-Encoding header",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "X-Correlation-Id",
        description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together Rest and java calls in log files",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Encoding",
        description = "The HTTP Content-Encoding header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Length",
        description = "The HTTP Content-Length header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Type",
        description = "The HTTP Content-Type header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Date",
        description = "The HTTP Date header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Host",
        description = "The HTTP Host header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "User-Agent",
        description = "The HTTP User-Agent header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
    },
    responses = {
      @ApiResponse(responseCode = "200", description = "List of Supported Layer Protocol Qualifier", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files"),
        @Header(name = "Content-Length", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Length header"),
        @Header(name = "Content-Encoding", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Encoding header"),
        @Header(name = "Content-Type", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Type header"),
        @Header(name = "Date", required = false, schema = @Schema(type = "string"),
          description = "The HTTP Date header"),
        @Header(name = "Server", required = false, schema = @Schema(type = "string"),
          description = "The HTTP Server header"),
        @Header(name = "Link", required = false, schema = @Schema(type = "string",
          description = "Link header can provide URLs to the first / previous / next / last pages of data if paging is used"))
      }, content = {
        @Content(mediaType = "application/json", array = @ArraySchema(schema=@Schema(implementation = SupportedLayerProtocolQualifier.class)))
      }),
      @ApiResponse(responseCode = "400", description = "The missing or invalid data supplied for this endpoint", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files")
      }, content = {
        @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
      }),
      @ApiResponse(responseCode = "401", description = "The user is not authorized to access this endpoint", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files")
      }, content = {
        @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
      })
    }
  )

  @GET
  @Path(MDRestPath.EOD.NRIM.SERVICE_INTERFACE_POINTS + "/{sipNrl}/supported-layer-protocol-qualifer")
  @Authorization(anyOfPermissions = {PermissionAction.AddService, PermissionAction.ModifyService})
  public SupportedLayerProtocolQualifier getSupportedLayerProtocolQualifierViaRest(@PathParam("sipNrl") String sipNrl,
                                                                                   @Parameter(description = "Layer protocol qualifier enum value")
                                                                                   @QueryParam("layerProtocol") String layerProtocolQualifier,
                                                                                   @Parameter(description = "Payload qualifier, the enum value for payload qualifier")
                                                                                   @QueryParam("payload") String payloadQualifier) {
    return getSupportedLayerProtocolQualifier(sipNrl, layerProtocolQualifier, payloadQualifier);
  }

  @Override
  public SupportedLayerProtocolQualifier getSupportedLayerProtocolQualifier(String sipNrl, String layerProtocolQualifier, String payloadQualifier) {
    String layerProtocolDbValue = MLEndPoint.getLayerProtocolDbValue(layerProtocolQualifier, payloadQualifier);
    List<MLEndPoint> endPoints = nrimDAO.getEndpointBySipNrlAndResource(sipNrl, layerProtocolDbValue, MLNetworkResourceInventoryDAO.SlpqCategory.ANY_SIP);
    if (endPoints.size() != 1)
      return apiLogger.passResultAndlog(null, sipNrl, layerProtocolQualifier, payloadQualifier);
    MLEndPoint endPoint = endPoints.get(0);
    SupportedLayerProtocolQualifier supportedLayerProtocolQualifier = new SupportedLayerProtocolQualifier(endPoint.getType().name(), endPoint.getInterfaceType().name(),
      endPoint.getTermType().getValue(), layerProtocolQualifier, payloadQualifier,
      endPointHelper.getProtectionTypes(endPoint.getProtectionTypes()),
      endPointHelper.getResilienceTypes(endPoint.getProtectionTypes(), layerProtocolQualifier));
    return apiLogger.passResultAndlog(supportedLayerProtocolQualifier, sipNrl, layerProtocolQualifier, payloadQualifier);
  }

  @Operation(
          operationId = "getPotentialConnectionEndPoints",
          summary = "Read all Potential Connection Endpoints, for given SIP nrl, layer protocol qualifier, payload and resource selector.",
          description = "Provides a list of all Potential Connection Endpoints.",
          tags = { "Connection - NRIM" },
          parameters = {
                  @Parameter(name = "userID",
                          description = "User Id",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "sessionID",
                          description = "Session Id",
                            in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Accept",
                          description = "The HTTP Accept header",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Accept-Encoding",
                          description = "The HTTP Accept-Encoding header",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "X-Correlation-Id",
                          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together Rest and java calls in log files",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Encoding",
                          description = "The HTTP Content-Encoding header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Length",
                          description = "The HTTP Content-Length header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Type",
                          description = "The HTTP Content-Type header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Date",
                          description = "The HTTP Date header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Host",
                          description = "The HTTP Host header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "User-Agent",
                          description = "The HTTP User-Agent header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
          },
          responses = {
                  @ApiResponse(responseCode = "200", description = "List of Potential Connection Endpoints", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files"),
                          @Header(name = "Content-Length", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Length header"),
                          @Header(name = "Content-Encoding", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Encoding header"),
                          @Header(name = "Content-Type", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Type header"),
                          @Header(name = "Date", required = false, schema = @Schema(type = "string"),
                                  description = "The HTTP Date header"),
                          @Header(name = "Server", required = false, schema = @Schema(type = "string"),
                                  description = "The HTTP Server header"),
                          @Header(name = "Link", required = false, schema = @Schema(type = "string",
                                  description = "Link header can provide URLs to the first / previous / next / last pages of data if paging is used"))
                  }, content = {
                          @Content(mediaType = "application/json", array = @ArraySchema(schema=@Schema(implementation = PotentialConnectionEndPointDto.class)))
                  }),
                  @ApiResponse(responseCode = "400", description = "The missing or invalid data supplied for this endpoint", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files")
                  }, content = {
                          @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
                  }),
                  @ApiResponse(responseCode = "401", description = "The user is not authorized to access this endpoint", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files")
                  }, content = {
                          @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
                  })
          }
  )
  @GET
  @Path(MDRestPath.EOD.NRIM.POTENTIAL_CONNECTION_ENDPOINTS)
  @Authorization(anyOfPermissions = {PermissionAction.AddService, PermissionAction.ModifyService})
  public List<PotentialConnectionEndPointDto> getPotentialConnectionEndPointsForSIP(
          @Parameter(required = true)
          @QueryParam("sipNrl") String sipNrl,
          @Parameter(description = "Layer protocol qualifier enum value", required = true)
          @QueryParam("layerProtocol") String layerProtocol,
          @Parameter(description = "Payload qualifier, the enum value for payload qualifier", required = true)
          @QueryParam("payload") String payload,
          @Parameter(description = "Frequency")
          @QueryParam("instance") String instance) {
    return getPotentialConnectionEndPoints(sipNrl, layerProtocol, payload, instance);
  }

  @Override
  public List<PotentialConnectionEndPointDto> getPotentialConnectionEndPoints(String sipNrl, String layerProtocol, String payload, String instance) {
    MLNetworkLayer mlLayerProtocol = MLNetworkLayer.fromLayerQualifierString(layerProtocol).orElse(null);
    MLNetworkLayer mlPayload = Optional.ofNullable(payload).flatMap(MLNetworkLayer::fromLayerQualifierString).orElse(null);
    if (mlLayerProtocol != null) {
      List<PotentialConnectionEndPointDto> result = endPointHandler.getPotentialConnectionEndPoints(sipNrl, mlLayerProtocol, mlPayload, instance);
      return apiLogger.passResultAndlog(result, sipNrl, layerProtocol, payload, instance);
    }
    return apiLogger.passResultAndlog(Collections.emptyList(), sipNrl, layerProtocol, payload, instance);
  }

  @Operation(
    operationId = "getConnectionEndPoints",
    summary = "Read Connection Endpoints, for given list of NEP nrls, layer protocol qualifier, payload and resource selector.",
    description = "Provides a list of all Connection Endpoints.",
    tags = { "Connection - NRIM" },
    parameters = {
      @Parameter(name = "userID",
        description = "User Id",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "sessionID",
        description = "Session Id",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Accept",
        description = "The HTTP Accept header",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Accept-Encoding",
        description = "The HTTP Accept-Encoding header",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "X-Correlation-Id",
        description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together Rest and java calls in log files",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Encoding",
        description = "The HTTP Content-Encoding header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Length",
        description = "The HTTP Content-Length header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Type",
        description = "The HTTP Content-Type header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Date",
        description = "The HTTP Date header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Host",
        description = "The HTTP Host header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "User-Agent",
        description = "The HTTP User-Agent header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
    },
    responses = {
      @ApiResponse(responseCode = "200", description = "List of Potential Connection Endpoints", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files"),
        @Header(name = "Content-Length", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Length header"),
        @Header(name = "Content-Encoding", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Encoding header"),
        @Header(name = "Content-Type", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Type header"),
        @Header(name = "Date", required = false, schema = @Schema(type = "string"),
          description = "The HTTP Date header"),
        @Header(name = "Server", required = false, schema = @Schema(type = "string"),
          description = "The HTTP Server header"),
        @Header(name = "Link", required = false, schema = @Schema(type = "string",
          description = "Link header can provide URLs to the first / previous / next / last pages of data if paging is used"))
      }, content = {
        @Content(mediaType = "application/json", array = @ArraySchema(schema=@Schema(implementation = PotentialConnectionEndPointDto.class)))
      }),
      @ApiResponse(responseCode = "400", description = "The missing or invalid data supplied for this endpoint", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files")
      }, content = {
        @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
      }),
      @ApiResponse(responseCode = "401", description = "The user is not authorized to access this endpoint", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files")
      }, content = {
        @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
      })
    }
  )
  @GET
  @Path(MDRestPath.EOD.NRIM.CONNECTION_ENDPOINTS)
  @Authorization(anyOfPermissions = {PermissionAction.AddService, PermissionAction.ModifyService})
  @Override
  public List<String> getConnectionEndPoints(@Parameter(description = "NEP-NRLs", required = true)
                                               @QueryParam("nepNrls") List<String> nepNrls,
                                             @Parameter(description = "LayerQualifier", required = true)
                                               @QueryParam("layerProtocol") String layerProtocol,
                                             @Parameter(description = "PayloadQualifier")
                                               @QueryParam("payload") String payload,
                                             @Parameter(description = "Instance (e.g. ODU-instance, OTSiMC-frequency")
                                               @QueryParam("instance") String instance) {
    if (!endPointHelper.isValidLayerProtocol(layerProtocol) ||
        !endPointHelper.isValidOptionalPayload(payload) ||
        !endPointHelper.isValidOptionalResourceSelector(instance)) {
      return apiLogger.passResultAndlog(Collections.emptyList(), nepNrls, layerProtocol, payload, instance);
    }
    List<String> result = endPointHandler.getConnectionEndPoints(nepNrls, layerProtocol, payload, instance);
    return apiLogger.passResultAndlog(result, nepNrls, layerProtocol, payload, instance);
  }

  @Operation(
    operationId = "getNodeEdgePoints",
    summary = "Read Node Edge Points, for given CEP nrl and layer protocol qualifier.",
    description = "Provides a list of all Node-edge-points.",
    tags = { "Connection - NRIM" },
    parameters = {
      @Parameter(name = "userID",
        description = "User Id",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "sessionID",
        description = "Session Id",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Accept",
        description = "The HTTP Accept header",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Accept-Encoding",
        description = "The HTTP Accept-Encoding header",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "X-Correlation-Id",
        description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together Rest and java calls in log files",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Encoding",
        description = "The HTTP Content-Encoding header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Length",
        description = "The HTTP Content-Length header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Type",
        description = "The HTTP Content-Type header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Date",
        description = "The HTTP Date header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Host",
        description = "The HTTP Host header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "User-Agent",
        description = "The HTTP User-Agent header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
    },
    responses = {
      @ApiResponse(responseCode = "200", description = "List of Potential Connection Endpoints", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files"),
        @Header(name = "Content-Length", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Length header"),
        @Header(name = "Content-Encoding", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Encoding header"),
        @Header(name = "Content-Type", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Type header"),
        @Header(name = "Date", required = false, schema = @Schema(type = "string"),
          description = "The HTTP Date header"),
        @Header(name = "Server", required = false, schema = @Schema(type = "string"),
          description = "The HTTP Server header"),
        @Header(name = "Link", required = false, schema = @Schema(type = "string",
          description = "Link header can provide URLs to the first / previous / next / last pages of data if paging is used"))
      }, content = {
        @Content(mediaType = "application/json", array = @ArraySchema(schema=@Schema(implementation = PotentialConnectionEndPointDto.class)))
      }),
      @ApiResponse(responseCode = "400", description = "The missing or invalid data supplied for this endpoint", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files")
      }, content = {
        @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
      }),
      @ApiResponse(responseCode = "401", description = "The user is not authorized to access this endpoint", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files")
      }, content = {
        @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
      })
    }
  )
  @GET
  @Path(MDRestPath.EOD.NRIM.NODE_EDGE_POINTS)
  @Authorization(anyOfPermissions = {PermissionAction.AddService, PermissionAction.ModifyService})
  @Override
  // SFS: FNMS-85117
  public List<String> getNodeEdgePoints(@Parameter(description = "CEP-NRL", required = true)
                                        @QueryParam("cepNrl") String cepNrl,
                                        @Parameter(description = "LayerQualifier", required = true)
                                        @QueryParam("layerProtocol") String layerProtocol) {
    List<String> result = endPointHandler.getNodeEdgePoints(cepNrl, layerProtocol);
    return apiLogger.passResultAndlog(result, cepNrl, layerProtocol);
  }

  //==============================================================================
  //=== Connections API ==========================================================
  //==============================================================================

  @Override
  @MDTransactional
  public void updateTopConnection(UUID uuid, TopConnectionUpdateDto dto, boolean cpEnabled, String userLabel) {
    MLTrailDBImpl mlTrailDB = Optional.ofNullable(nrimDAO.getTopConnection(uuid)).map(t -> MDPersistenceHelper.find(MLTrailDBImpl.class, t.getId())).orElse(null);
    apiLogger.log(uuid, dto);
    if (mlTrailDB != null) {
      boolean isManaged = dto.managed();
      mlTrailDB.setNewPrimaryLifecycleState(isManaged ? MLNewPrimaryLifecycleState.MANAGED : MLNewPrimaryLifecycleState.DISCOVERED);
      mlTrailDB.setNewSecondaryLifecycleState(isManaged ? MLNewSecondaryLifecycleState.USER_SET_MANAGED : MLNewSecondaryLifecycleState.USER_SET_DISCOVERED);
      if (userLabel != null)
        mlTrailDB.setLabel(userLabel);

      Definition.ServiceCreationMode oldSetupMode = mlTrailDB.getServiceCreationMode();
      Definition.ServiceCreationMode newSetupMode = cpEnabled ? Definition.ServiceCreationMode.PROVISION_MODE : Definition.ServiceCreationMode.EXPLORE_MODE;
      if (oldSetupMode != newSetupMode) {
        try {
          mlTrailDB.addProperty(MLTEPropertyKey.SETUPMODE, newSetupMode);
        }catch (MLPropertyException e) {  /*ignore*/  }
        MLTaskExecutorService.getInstance().submitTask(new MLServiceViewTask(MLServiceViewTask.TriggerType.CreateOrUpdateServiceView, mlTrailDB.getId(), false, true));
      }
    } else {
      throw new IllegalArgumentException("UUID is unknown " + uuid);
    }
  }

  @Operation(
          operationId = "setTargetAdminState",
          summary = "Set target admin state",
          description = "Set target admin state",
          tags = { "Connection - NRIM" },
          parameters = {
                  @Parameter(name = "sessionID",
                          description = "Session Id",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Accept",
                          description = "The HTTP Accept header",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Accept-Encoding",
                          description = "The HTTP Accept-Encoding header",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "X-Correlation-Id",
                          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together Rest and java calls in log files",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Encoding",
                          description = "The HTTP Content-Encoding header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Length",
                          description = "The HTTP Content-Length header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Type",
                          description = "The HTTP Content-Type header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Date",
                          description = "The HTTP Date header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Host",
                          description = "The HTTP Host header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "User-Agent",
                          description = "The HTTP User-Agent header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
          },
          responses = {
                  @ApiResponse(responseCode = "200", description = "Successful operation", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files"),
                          @Header(name = "Content-Length", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Length header"),
                          @Header(name = "Content-Encoding", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Encoding header"),
                          @Header(name = "Content-Type", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Type header"),
                          @Header(name = "Date", required = false, schema = @Schema(type = "string"),
                                  description = "The HTTP Date header"),
                          @Header(name = "Server", required = false, schema = @Schema(type = "string"),
                                  description = "The HTTP Server header"),
                          @Header(name = "Link", required = false, schema = @Schema(type = "string",
                                  description = "Link header can provide URLs to the first / previous / next / last pages of data if paging is used"))
                  }),
                  @ApiResponse(responseCode = "400", description = "The missing or invalid data supplied for this endpoint", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files")
                  }, content = {
                          @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
                  }),
                  @ApiResponse(responseCode = "401", description = "The user is not authorized to access this endpoint", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files")
                  }, content = {
                          @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
                  })
          }
  )
  @POST
  @Path(MDRestPath.EOD.NRIM.CONNECTIONS + "/{id}/" + MDRestPath.EOD.NRIM.TARGET_ADMIN_STATE + "/{targetAdminState}")
  @Authorization(permissions = PermissionAction.SERVICE_ADMIN_STATE)
  @Override
  public void setTargetAdminState(@Parameter(required = true)
                                  @PathParam("id") UUID connectionId,
                                  @Parameter(required = true)
                                  @PathParam("targetAdminState") AdminState targetAdminState) {
    AdministrationStateType adminState = switch (targetAdminState) {
      case UP, AUTOMATIC_IN_SERVICE, NA -> AdministrationStateType.UP;
      case DOWN -> AdministrationStateType.DOWN;
      case MAINTENANCE -> AdministrationStateType.MAINTENANCE;
    };
    MLTrailDTO mlTrailDTO = getMLTrail(connectionId);
    if (mlTrailDTO == null) {
      apiLogger.passResultAndlog(null, connectionId, targetAdminState, "No ML trail found!");
      return;
    }
    apiLogger.passResultAndlog(null, targetAdminState, connectionId, mlTrailDTO.getLabel());
    setAdminState(mlTrailDTO.getId(), adminState);
  }

  @MDTransactional
  private void setAdminState(int mlTrailId , AdministrationStateType adminState) {
    MLTrailDBImpl requestedTrail;
    try {
      requestedTrail = MDPersistenceHelper.getObjectById(MLTrailDBImpl.class, mlTrailId);
    } catch (EntityNotFoundException ex) {
      log.error("No trail found with ID: {}", mlTrailId);
      return;
    }
    requestedTrail.setAdminState(adminState);
    setAdminStateOfProtectionBelow(requestedTrail, adminState);
    if (endPointHelper.isPayloadOfOTSiMC(requestedTrail.getLayer()))
      setAdminStateOfOchBelow(requestedTrail, adminState);
  }

  private void setAdminStateOfProtectionBelow(MLTrailDBImpl requestedTrail, AdministrationStateType adminState) {
    MLConnection networkConnection = mlEodConnectionProvider.getNetworkConnection(requestedTrail);
    if (networkConnection == null)
      return;
    networkConnection.setAdminState(adminState);
    List<MLMonitoringSectionDBImpl> monitoringSections  = mlTopologyElementDAO.getMonitoringSectionsByParent(networkConnection.getId());
    if (monitoringSections != null)
      monitoringSections.forEach(monSection -> monSection.setAdminState(adminState));
  }

  private void setAdminStateOfOchBelow(MLTrailDBImpl requestedTrail, AdministrationStateType adminState) {
    Set<MLPtpConnectionDBImpl> connectionsBelow = Stream.of(requestedTrail.getWorkingForwardPath(), requestedTrail.getWorkingBackwardPath())
      .filter(Objects::nonNull)
      .map(MLPath::connectionSequence)
      .flatMap(Collection::stream)
      .filter(MLPtpConnectionDBImpl.class::isInstance)
      .map(MLPtpConnectionDBImpl.class::cast)
      .collect(Collectors.toSet());
    Set<MLServiceDBImpl> ochTrails = connectionsBelow.stream()
      .map(mlTopologyElementDAO::getAdaptationForClientEntity)
      .filter(Objects::nonNull)
      .map(MLLayerAdaptationDBImpl::getServerEntities)
      .flatMap(Collection::stream)
      .filter(mlService -> mlService.getLayer() == MLNetworkLayer.OCH)
      .collect(Collectors.toSet());
    ochTrails.forEach(och -> och.setAdminState(adminState));
  }

  @Operation(
          operationId = "getConnectionsAPI",
          summary = "Get list of connections.",
          description = "Get list of ENC connections. Includes discovered / managed connections, top or XC and XC_FIXED connections, F8/F7/Uno connections. "+
              "A list of filter / query parameters can limit the get results (eg discovered top connections of a specific layer protocol). "+
              "The query parameter values are not matched exactly at database; the match is considered successful if input parameter value  "+
              "is a substring of connection's object property. "+
              "The list is sorted by \"id\" in ascending order when no sort condition is specified. "+
              "The fields that can be sorted are \"id\", \"name\".",
              tags = { "Connection - NRIM" },
          parameters = {
                  @Parameter(name = "userID",
                          description = "User Id",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "sessionID",
                          description = "Session Id",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Accept",
                          description = "The HTTP Accept header",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Accept-Encoding",
                          description = "The HTTP Accept-Encoding header",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "X-Correlation-Id",
                          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together Rest and java calls in log files",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Encoding",
                          description = "The HTTP Content-Encoding header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Length",
                          description = "The HTTP Content-Length header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Type",
                          description = "The HTTP Content-Type header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Date",
                          description = "The HTTP Date header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Host",
                          description = "The HTTP Host header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "User-Agent",
                          description = "The HTTP User-Agent header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class))
          },
          responses = {
                  @ApiResponse(responseCode = "200", description = "List of Service Interface Points", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files"),
                          @Header(name = "Content-Length", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Length header"),
                          @Header(name = "Content-Encoding", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Encoding header"),
                          @Header(name = "Content-Type", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Type header"),
                          @Header(name = "Date", required = false, schema = @Schema(type = "string"),
                                  description = "The HTTP Date header"),
                          @Header(name = "Server", required = false, schema = @Schema(type = "string"),
                                  description = "The HTTP Server header"),
                          @Header(name = "Link", required = false, schema = @Schema(type = "string",
                                  description = "Link header can provide URLs to the first / previous / next / last pages of data if paging is used"))
                  }, content = {
                          @Content(mediaType = "application/json", array = @ArraySchema(schema=@Schema(implementation = ConnectionDto.class)))
                  }),
                  @ApiResponse(responseCode = "400", description = "The missing or invalid data supplied for this endpoint", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files"),
                  }, content = {
                          @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
                  }),
                  @ApiResponse(responseCode = "401", description = "The user is not authorized to access this endpoint", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files"),
                  }, content = {
                          @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
                  })
          }
  )
  @GET
  @Path(MDRestPath.EOD.NRIM.CONNECTIONS)
  @Authorization(permissions = PermissionAction.BrowseServices)
  public List<ConnectionDto> getConnectionsAPI(@Parameter(description = "flag to indicate that response should contain only top connections")
                                               @QueryParam("topConnections") @DefaultValue("true") boolean topConnections,
                                               @Parameter(schema = @Schema(implementation = MLNewPrimaryLifecycleState.class))
                                               @QueryParam("lifecycle") String lifecycle,
                                               @Parameter(description = "Layer protocol qualifier enum value")
                                               @QueryParam("layerProtocolQualifier") String layerProtocol,
                                               @Parameter(description = "Payload qualifier, the enum value for payload qualifier")
                                               @QueryParam("payloadQualifier") String payload,
                                               @Parameter(description = "a two elements array that contains ne name substring. Each one of the elements should be tried against " +
                                                       "both connections A-end NE and Z-end NE. Value is consider as match, if it is substring of connection's end NE name.",
                                                       example = "?endingNEs=abc1&endingNEs=abc2")
                                               @QueryParam("endingNEs") List<String> endingNEs,
                                               @Parameter(description = "a two elements array that contains ending points compact NRLs. Each one of the elements should be tried against " +
                                                       "both connections end points. Value is consider as match, if it is substring of connection's end point",
                                                       example = "?endingPoints=abc1&endingPoints=abc2")
                                               @QueryParam("endingPoints") List<String> endingPoints,
                                               @Parameter(description = "a two elements array that contains ending points standard NRLs. Each one of the elements should be tried against " +
                                                       "both connections end points. Value is consider as match, if it is substring of connection's end point.",
                                                       example = "?endingPointsStandard=abc1&endingPoints=abc2")
                                               @QueryParam("endingPointsStandard") List<String> endingPointsStandard,
                                               @Parameter(description = "Protection type for the connection")
                                               @QueryParam("protectionType") String protectionType,
                                               @Parameter(description = "an optional free text, that should be test for matching (as a substring of connection's attribute) " +
                                                       "against all the connection's visible attributes")
                                               @QueryParam("freeTextFilter") String freeTextFilter,
                                               @QueryParam("size") Integer pageSize,
                                               @QueryParam("page") Integer pageNo,
                                               @QueryParam("sort") List<String> sort,
                                               @DefaultValue("false") @QueryParam("count") boolean count,
                                               @Context HttpServletResponse response,
                                               @Context HttpServletRequest request) {

    ConnectionFilters connectionFilters = ConnectionFilters.Builder.builder()
            .topConnections(topConnections)
            .lifecycle(lifecycle)
            .layerProtocol(layerProtocol)
            .payload(payload)
            .endingNEs(endingNEs)
            .endingPointsStandard(endingPointsStandard)
            .endingPointsCompact(endingPoints)
            .protectionType(protectionType)
            .freeTextFilter(freeTextFilter)
            .build();
    List<ConnectionDto> connections = List.of();
    // The only case where we fetch connection results from DB is when the request is a GET request. If it is "HEAD", then the body will be empty.
    if ("GET".equalsIgnoreCase(request.getMethod())) {
      connections = getConnections(connectionFilters, pageSize, pageNo, sort);
    }
    if (count) {
      long allConnectionsCount = mlEodConnectionsPageHdlr.getConnectionsCount(getMlEodConnectionsFilters(connectionFilters,
              Integer.MAX_VALUE, 0, null));
      response.addHeader("ENC-Result-Count", String.valueOf(allConnectionsCount));
    }
    return connections;
  }

  @Override
  public List<ConnectionDto> getConnections(ConnectionFilters connectionFilters, Integer pageSize, Integer pageNo, List<String> sort) {
    MLEodConnectionsFilters connectionsFilters = getMlEodConnectionsFilters(connectionFilters, pageSize, pageNo, sort);

    List<ConnectionDto> connections = mlEodConnectionsPageHdlr.getConnections(connectionsFilters);
    log.debug("EOD-GET-CONNECTIONS: Retrieved Connections are: {}", connections);
    return apiLogger.passResultAndlog(connections, connectionFilters, pageSize, pageNo, sort);
  }

  private static MLEodConnectionsFilters getMlEodConnectionsFilters(ConnectionFilters connectionFilters, Integer pageSize, Integer pageNo, List<String> sort) {
    String aNode = null;
    String zNode = null;
    if (connectionFilters.endingNEs() != null) {
      aNode = !connectionFilters.endingNEs().isEmpty() ? connectionFilters.endingNEs().get(0) : null;
      zNode = connectionFilters.endingNEs().size() > 1 ? connectionFilters.endingNEs().get(1) : null;
      log.debug("EOD-GET-CONNECTIONS: Ending NEs values of connection filters are aNode: {} and zNode: {}.", aNode, zNode);
    }
    String aEndPoint = null;
    String zEndPoint = null;
    if (connectionFilters.endingPointsStandard() != null) {
      aEndPoint = !connectionFilters.endingPointsStandard().isEmpty() ? connectionFilters.endingPointsStandard().get(0) : null;
      zEndPoint = connectionFilters.endingPointsStandard().size() > 1 ? connectionFilters.endingPointsStandard().get(1) : null;
      log.debug("EOD-GET-CONNECTIONS: Standard Nrls values of connection filters are aEndPointStandard: {} and zEndPointStandard: {}.", aEndPoint, zEndPoint);
    }

    String aEndPointCompact = null;
    String zEndPointCompact = null;
    if (connectionFilters.endingPointsCompact() != null) {
      aEndPointCompact = !connectionFilters.endingPointsCompact().isEmpty() ? connectionFilters.endingPointsCompact().get(0) : null;
      zEndPointCompact = connectionFilters.endingPointsCompact().size() > 1 ? connectionFilters.endingPointsCompact().get(1) : null;
      log.debug("EOD-GET-CONNECTIONS: Compact Nrls values of connection filters are aEndPointCompact: {} and zEndPointCompact: {}.", aEndPointCompact, zEndPointCompact);
    }

    MLEodConnectionsFilters connectionsFilters = MLEodConnectionsFilters.Builder.builder()
            .topConnections(connectionFilters.topConnections())
            .lifecycle(connectionFilters.lifecycle())
            .layerProtocolQualifier(connectionFilters.layerProtocol())
            .payloadQualifier(connectionFilters.payload())
            .aNode(aNode)
            .zNode(zNode)
            .aEndPoint(aEndPoint)
            .zEndPoint(zEndPoint)
            .aEndPointCompact(aEndPointCompact)
            .zEndPointCompact(zEndPointCompact)
            .protectionType(connectionFilters.protectionType())
            .searchStr(connectionFilters.freeTextFilter())
            .pageSize(pageSize != null ? pageSize : 0)
            .pageNo(pageNo != null ? pageNo : 0)
            .sort(sort)
            .build();
    log.debug("EOD-GET-CONNECTIONS: MLEodConnectionsFilters are: {}", connectionsFilters);
    return connectionsFilters;
  }

  @Override
  public ConnectionDto getConnectionForEndpoints(List<String> endpoints) {
    if (Objects.isNull(endpoints) || endpoints.size() != 2) {
      return null;
    }
    String aEndCep = endpoints.get(0);
    String zEndCep = endpoints.get(1);
    List<MLPtpServiceDBImpl> mlPtpServiceDBS = nrimDAO.getServices(aEndCep, zEndCep);
    ConnectionDto connectionDto;
    if (mlPtpServiceDBS.size() == 1) {
      connectionDto = mlEodConnectionsPageHdlr.getConnection(mlPtpServiceDBS.get(0).getId());
    } else {
      log.info("Number of Connections found for aEndCep {}, zEndCep {} is {}",
          SecurityTools.replaceCRLFs(aEndCep),
          SecurityTools.replaceCRLFs(zEndCep),
          mlPtpServiceDBS.size());
      connectionDto = null;
    }
    return apiLogger.passResultAndlog(connectionDto, aEndCep, zEndCep);
  }

  @Operation(
    operationId = "getEntitiesForConnection",
    summary = "Get ordered list of entities in connection.",
    description = "Get ordered list of entities in connection.",
    tags = { "Connection - NRIM" },
    parameters = {
      @Parameter(name = "userID",
        description = "User Id",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "sessionID",
        description = "Session Id",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Accept",
        description = "The HTTP Accept header",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Accept-Encoding",
        description = "The HTTP Accept-Encoding header",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "X-Correlation-Id",
        description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together Rest and java calls in log files",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Encoding",
        description = "The HTTP Content-Encoding header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Length",
        description = "The HTTP Content-Length header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Type",
        description = "The HTTP Content-Type header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Date",
        description = "The HTTP Date header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Host",
        description = "The HTTP Host header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "User-Agent",
        description = "The HTTP User-Agent header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class))
      },
      responses = {
      @ApiResponse(responseCode = "200", description = "List of entities in connection", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                         "Rest and java calls in log files"),
        @Header(name = "Content-Length", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Length header"),
        @Header(name = "Content-Encoding", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Encoding header"),
        @Header(name = "Content-Type", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Type header"),
        @Header(name = "Date", required = false, schema = @Schema(type = "string"),
          description = "The HTTP Date header"),
        @Header(name = "Server", required = false, schema = @Schema(type = "string"),
          description = "The HTTP Server header"),
        @Header(name = "Link", required = false, schema = @Schema(type = "string",
          description = "Link header can provide URLs to the first / previous / next / last pages of data if paging is used"))
      }, content = {
        @Content(mediaType = "application/json", array = @ArraySchema(schema=@Schema(implementation = ConnectionDto.class)))
        }),
        @ApiResponse(responseCode = "400", description = "The missing or invalid data supplied for this endpoint", headers = {
          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                            "Rest and java calls in log files"),
          }, content = {
        @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
                    }),
        @ApiResponse(responseCode = "401", description = "The user is not authorized to access this endpoint", headers = {
          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                "Rest and java calls in log files"),
          }, content = {
          @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
          })
      }
  )
  @GET
  @Path(MDRestPath.EOD.NRIM.CONNECTIONS + "/{id}/entities")
  @Authorization(permissions = PermissionAction.BrowseServices)
  @Override
  public Map<Role, List<EntityDto>> getEntitiesForConnection(
            @Parameter(description = "Connection UUID", required = true)
            @PathParam("id") UUID connectionId) {
    MLTrailDTO mlTrailDTO = getMLTrail(connectionId);
    if (mlTrailDTO == null) {
      log.error("MLTrailDTO is null for connectionId: {}", connectionId);
      return apiLogger.passResultAndlog(null, connectionId);
    }
    MLTrailDBImpl mlTrailDB = mlTopologyElementDAO.getTrailByID(mlTrailDTO.getId());
    if (mlTrailDB == null) {
      log.error("MLTrailDBImpl is null for connectionId: {}", connectionId);
      return apiLogger.passResultAndlog(null, connectionId);
    }
    Map<Role, List<EntityDto>> resultList = mlEodLinkHandler.getEntitiesByRole(mlTrailDB);
    return apiLogger.passResultAndlog(resultList, connectionId);
  }

    @Operation(
            operationId = "getConnectionEndPoint",
            summary = "Get CEP-NRL for a given connection point",
            description = "Get CEP-NRL for a given connection point",
            tags = { "Connection - NRIM" },
            parameters = {
                    @Parameter(name = "userID",
                            description = "User Id",
                            in = ParameterIn.HEADER,
                            required = true,
                            schema = @Schema(implementation = String.class)),
                    @Parameter(name = "sessionID",
                            description = "Session Id",
                            in = ParameterIn.HEADER,
                            required = true,
                            schema = @Schema(implementation = String.class)),
                    @Parameter(name = "Accept",
                            description = "The HTTP Accept header",
                            in = ParameterIn.HEADER,
                            required = true,
                            schema = @Schema(implementation = String.class)),
                    @Parameter(name = "Accept-Encoding",
                            description = "The HTTP Accept-Encoding header",
                            in = ParameterIn.HEADER,
                            required = true,
                            schema = @Schema(implementation = String.class)),
                    @Parameter(name = "X-Correlation-Id",
                            description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together Rest and java calls in log files",
                            in = ParameterIn.HEADER,
                            required = true,
                            schema = @Schema(implementation = String.class)),
                    @Parameter(name = "Content-Encoding",
                            description = "The HTTP Content-Encoding header",
                            in = ParameterIn.HEADER,
                            schema = @Schema(implementation = String.class)),
                    @Parameter(name = "Content-Length",
                            description = "The HTTP Content-Length header",
                            in = ParameterIn.HEADER,
                            schema = @Schema(implementation = String.class)),
                    @Parameter(name = "Content-Type",
                            description = "The HTTP Content-Type header",
                            in = ParameterIn.HEADER,
                            schema = @Schema(implementation = String.class)),
                    @Parameter(name = "Date",
                            description = "The HTTP Date header",
                            in = ParameterIn.HEADER,
                            schema = @Schema(implementation = String.class)),
                    @Parameter(name = "Host",
                            description = "The HTTP Host header",
                            in = ParameterIn.HEADER,
                            schema = @Schema(implementation = String.class)),
                    @Parameter(name = "User-Agent",
                            description = "The HTTP User-Agent header",
                            in = ParameterIn.HEADER,
                            schema = @Schema(implementation = String.class))
            },
            responses = {
                    @ApiResponse(responseCode = "200", description = "Get CEP-NRL for a given connection point", headers = {
                            @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                    description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                            "Rest and java calls in log files"),
                            @Header(name = "Content-Length", required = true, schema = @Schema(type = "string"),
                                    description = "The HTTP Content-Length header"),
                            @Header(name = "Content-Encoding", required = true, schema = @Schema(type = "string"),
                                    description = "The HTTP Content-Encoding header"),
                            @Header(name = "Content-Type", required = true, schema = @Schema(type = "string"),
                                    description = "The HTTP Content-Type header"),
                            @Header(name = "Date", required = false, schema = @Schema(type = "string"),
                                    description = "The HTTP Date header"),
                            @Header(name = "Server", required = false, schema = @Schema(type = "string"),
                                    description = "The HTTP Server header"),
                            @Header(name = "Link", required = false, schema = @Schema(type = "string",
                                    description = "Link header can provide URLs to the first / previous / next / last pages of data if paging is used"))
                    }, content = {
                            @Content(mediaType = "application/json", array = @ArraySchema(schema=@Schema(implementation = ConnectionDto.class)))
                    }),
                    @ApiResponse(responseCode = "400", description = "The missing or invalid data supplied for this endpoint", headers = {
                            @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                    description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                            "Rest and java calls in log files"),
                    }, content = {
                            @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
                    }),
                    @ApiResponse(responseCode = "401", description = "The user is not authorized to access this endpoint", headers = {
                            @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                    description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                            "Rest and java calls in log files"),
                    }, content = {
                            @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
                    })
            }
    )
  @GET
  @Path(MDRestPath.EOD.NRIM.CONNECTION_ENDPOINTS_CEP_NRLS)
  @Authorization(permissions = PermissionAction.BrowseServices)
  @Override
  public String getConnectionEndPointCepNrl(
            @Parameter(description = "Connection Point", required = true)
            @QueryParam("id") int connectionPointId,
            @Parameter(description = "Network Layer", required = true)
            @QueryParam("layerProtocolQualifier") String layerProtocolQualifier) {

    // connectionpoint and layer protocol are mandatory parameters.
    if (connectionPointId == 0 || StringUtils.isEmpty(layerProtocolQualifier)) {
      log.error("getConnectionEndPoint :: MLConnectionPoint or layerProtocolQualifier is null");
      return StringUtils.EMPTY;
    }
    layerProtocolQualifier = SecurityTools.replaceCRLFs(layerProtocolQualifier);

    MLConnectionPointDBImpl mlConnectionPointDB = mlTopologyElementDAO.getConnPointByID(connectionPointId);
    if (mlConnectionPointDB == null) {
      log.error("MLConnectionPoint is null for ID: {}", connectionPointId);
      return apiLogger.passResultAndlog(null, connectionPointId);
    }

    Optional<MLNetworkLayer> networkLayer = MLNetworkLayer.fromLayerQualifierString(layerProtocolQualifier);
    if (networkLayer.isEmpty()) {
      log.error("Network Layer is null for ID: {}", connectionPointId);
      return apiLogger.passResultAndlog(null, connectionPointId);
    }

    String cepNrl = layerBrowserNrlProvider.computeCepNrlFromCpAndLayer(layerProtocolQualifier, mlConnectionPointDB, networkLayer.get());
    return apiLogger.passResultAndlog(cepNrl, connectionPointId);
  }

  @Operation(
          operationId = "getConnection",
          summary = "Get a connection that corresponds to input end points.",
          description = "Get a connection that corresponds to input end points.",
          tags = { "Connection - NRIM" },
          parameters = {
                  @Parameter(name = "sessionID",
                          description = "Session Id",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Accept",
                          description = "The HTTP Accept header",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Accept-Encoding",
                          description = "The HTTP Accept-Encoding header",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "X-Correlation-Id",
                          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together Rest and java calls in log files",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Encoding",
                          description = "The HTTP Content-Encoding header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Length",
                          description = "The HTTP Content-Length header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Type",
                          description = "The HTTP Content-Type header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Date",
                          description = "The HTTP Date header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Host",
                          description = "The HTTP Host header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "User-Agent",
                          description = "The HTTP User-Agent header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class))
          },
          responses = {
                  @ApiResponse(responseCode = "200", description = "Connection", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files"),
                          @Header(name = "Content-Length", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Length header"),
                          @Header(name = "Content-Encoding", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Encoding header"),
                          @Header(name = "Content-Type", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Type header"),
                          @Header(name = "Date", schema = @Schema(type = "string"),
                                  description = "The HTTP Date header"),
                          @Header(name = "Server", schema = @Schema(type = "string"),
                                  description = "The HTTP Server header")
                  }, content = {
                          @Content(mediaType = "application/json", schema=@Schema(implementation = ConnectionDto.class))
                  }),
                  @ApiResponse(responseCode = "400", description = "The missing or invalid data supplied for this endpoint", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files")
                  }, content = {
                          @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
                  }),
                  @ApiResponse(responseCode = "401", description = "The user is not authorized to access this endpoint", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files")
                  }, content = {
                          @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
                  }),
                  @ApiResponse(responseCode = "404", description = "Not found", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files")
                  }, content = {
                          @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
                  })
          }
  )

  @GET
  @Path(MDRestPath.EOD.NRIM.CONNECTIONS + "/endpoints")
  @Authorization(permissions = PermissionAction.BrowseServices)
  public ConnectionDto getConnectionForEndpointsRest(@QueryParam("cepNrl") List<String> endpoints) {
    return getConnectionForEndpoints(endpoints);
  }

  /**
   * @return ConnectionDto contains service path data
   */
  @Operation(
          operationId = "getConnection",
          summary = "Get a connection that corresponds to input id (UUID of connection).",
          description = "Get a connection that corresponds to input id (UUID of connection).",
          tags = { "Connection - NRIM" },
          parameters = {
                  @Parameter(name = "userID",
                          description = "User Id",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "sessionID",
                          description = "Session Id",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Accept",
                          description = "The HTTP Accept header",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Accept-Encoding",
                          description = "The HTTP Accept-Encoding header",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "X-Correlation-Id",
                          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together Rest and java calls in log files",
                          in = ParameterIn.HEADER,
                          required = true,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Encoding",
                          description = "The HTTP Content-Encoding header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Length",
                          description = "The HTTP Content-Length header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Content-Type",
                          description = "The HTTP Content-Type header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Date",
                          description = "The HTTP Date header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "Host",
                          description = "The HTTP Host header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class)),
                  @Parameter(name = "User-Agent",
                          description = "The HTTP User-Agent header",
                          in = ParameterIn.HEADER,
                          schema = @Schema(implementation = String.class))
          },
          responses = {
                  @ApiResponse(responseCode = "200", description = "Connection", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files"),
                          @Header(name = "Content-Length", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Length header"),
                          @Header(name = "Content-Encoding", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Encoding header"),
                          @Header(name = "Content-Type", required = true, schema = @Schema(type = "string"),
                                  description = "The HTTP Content-Type header"),
                          @Header(name = "Date", required = false, schema = @Schema(type = "string"),
                                  description = "The HTTP Date header"),
                          @Header(name = "Server", required = false, schema = @Schema(type = "string"),
                                  description = "The HTTP Server header")
                  }, content = {
                          @Content(mediaType = "application/json", schema=@Schema(implementation = ConnectionDto.class))
                  }),
                  @ApiResponse(responseCode = "400", description = "The missing or invalid data supplied for this endpoint", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files")
                  }, content = {
                          @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
                  }),
                  @ApiResponse(responseCode = "401", description = "The user is not authorized to access this endpoint", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files")
                  }, content = {
                          @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
                  }),
                  @ApiResponse(responseCode = "404", description = "Not found", headers = {
                          @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
                                  description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
                                          "Rest and java calls in log files")
                  }, content = {
                          @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
                  })
          }
  )
  @Override
  @GET
  @Path(MDRestPath.EOD.NRIM.CONNECTIONS + "/{id}")
  @Authorization(permissions = PermissionAction.BrowseServices)
  public ConnectionDto getConnection(
          @Parameter(description = "Connection UUID", required = true)
          @PathParam("id") UUID connectionId) {
    MLTrailDTO mlTrailDTO = getMLTrail(connectionId);
    if (mlTrailDTO != null) {
      ConnectionDto result = mlEodConnectionsPageHdlr.getConnection(mlTrailDTO.getId());
      return apiLogger.passResultAndlog(result, connectionId);
    }
    return apiLogger.passResultAndlog(null, connectionId);
  }

  @Operation(
    operationId = "getLayerProtocolQualifierOfAConnection",
    summary = "Get layer protocol qualifier of a connection that corresponds to input id (UUID of connection).",
    description = "Get layer protocol qualifier of a connection that corresponds to input id (UUID of connection).",
    tags = { "Connection - NRIM" },
    parameters = {
      @Parameter(name = "userID",
        description = "User Id",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "sessionID",
        description = "Session Id",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Accept",
        description = "The HTTP Accept header",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Accept-Encoding",
        description = "The HTTP Accept-Encoding header",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "X-Correlation-Id",
        description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together Rest and java calls in log files",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Encoding",
        description = "The HTTP Content-Encoding header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Length",
        description = "The HTTP Content-Length header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Type",
        description = "The HTTP Content-Type header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Date",
        description = "The HTTP Date header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Host",
        description = "The HTTP Host header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "User-Agent",
        description = "The HTTP User-Agent header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class))
    },
    responses = {
      @ApiResponse(responseCode = "200", description = "Connection", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files"),
        @Header(name = "Content-Length", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Length header"),
        @Header(name = "Content-Encoding", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Encoding header"),
        @Header(name = "Content-Type", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Type header"),
        @Header(name = "Date", required = false, schema = @Schema(type = "string"),
          description = "The HTTP Date header"),
        @Header(name = "Server", required = false, schema = @Schema(type = "string"),
          description = "The HTTP Server header")
      }, content = {
        @Content(mediaType = "application/json", schema=@Schema(implementation = String.class))
      }),
      @ApiResponse(responseCode = "400", description = "The missing or invalid data supplied for this endpoint", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files")
      }, content = {
        @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
      }),
      @ApiResponse(responseCode = "401", description = "The user is not authorized to access this endpoint", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files")
      }, content = {
        @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
      }),
      @ApiResponse(responseCode = "404", description = "Not found", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files")
      }, content = {
        @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
      })
    }
  )
  @Override
  @GET
  @Path(MDRestPath.EOD.NRIM.CONNECTIONS + "/{id}/layer-protocol-qualifier")
  @Authorization(permissions = PermissionAction.BrowseServices)
  public String getLayerProtocolQualifierOfAConnection(
    @Parameter(description = "Connection UUID", required = true)
    @PathParam("id") UUID connectionId) {
    MLTrailDTO mlTrailDTO = getMLTrail(connectionId);
    if (mlTrailDTO != null) {
      String result = mlEodConnectionProvider.getLayerProtocolQualifierOfAConnection(mlTrailDTO.getId());
      return apiLogger.passResultAndlog(result, connectionId);
    }
    return apiLogger.passResultAndlog(null, connectionId);
  }

  @Operation(
    operationId = "getPotentialConnection",
    summary = "Get Potential Connection that corresponds to input.",
    description = "Get Potential Connection that corresponds to input.",
    tags = { "Connection - NRIM" },
    parameters = {
      @Parameter(name = "userID",
        description = "User Id",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "sessionID",
        description = "Session Id",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Accept",
        description = "The HTTP Accept header",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Accept-Encoding",
        description = "The HTTP Accept-Encoding header",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "X-Correlation-Id",
        description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together Rest and java calls in log files",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Encoding",
        description = "The HTTP Content-Encoding header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Length",
        description = "The HTTP Content-Length header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Type",
        description = "The HTTP Content-Type header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Date",
        description = "The HTTP Date header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Host",
        description = "The HTTP Host header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "User-Agent",
        description = "The HTTP User-Agent header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class))
    },
    responses = {
      @ApiResponse(responseCode = "200", description = "Connection", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files"),
        @Header(name = "Content-Length", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Length header"),
        @Header(name = "Content-Encoding", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Encoding header"),
        @Header(name = "Content-Type", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Type header"),
        @Header(name = "Date", required = false, schema = @Schema(type = "string"),
          description = "The HTTP Date header"),
        @Header(name = "Server", required = false, schema = @Schema(type = "string"),
          description = "The HTTP Server header")
      }, content = {
        @Content(mediaType = "application/json", schema=@Schema(implementation = ConnectionDto.class))
      }),
      @ApiResponse(responseCode = "400", description = "The missing or invalid data supplied for this endpoint", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files")
      }, content = {
        @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
      }),
      @ApiResponse(responseCode = "401", description = "The user is not authorized to access this endpoint", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files")
      }, content = {
        @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
      }),
      @ApiResponse(responseCode = "404", description = "Not found", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files")
      }, content = {
        @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
      })
    }
  )
  @Override
  @GET
  @Path(MDRestPath.EOD.NRIM.POTENTIAL_CONNECTIONS)
  @Authorization(permissions = PermissionAction.BrowseServices)
  public PotentialConnectionDto getPotentialConnection(@QueryParam("aEndSip") String aEndSip,
                                                       @QueryParam("zEndSip") String zEndSip,
                                                       @QueryParam("layerProtocolQualifier") String layerProtocolQualifier,
                                                       @QueryParam("payloadQualifier") String payloadQualifier,
                                                       @QueryParam("protectionType") ProtectionType protectionType) {
    PotentialConnectionDto dto = nodeEdgePointHandler.getPotentialConnection(aEndSip, zEndSip, layerProtocolQualifier, payloadQualifier, protectionType);
    return apiLogger.passResultAndlog(dto, aEndSip, zEndSip, layerProtocolQualifier, payloadQualifier, protectionType);
  }

  @Operation(
    operationId = "getConnection",
    summary = "Get routes of a connection that corresponds to input id (UUID of connection).",
    description = "Get routes of a connection that corresponds to input id (UUID of connection).",
    tags = { "Connection - NRIM" },
    parameters = {
      @Parameter(name = "userID",
        description = "User Id",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "sessionID",
        description = "Session Id",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Accept",
        description = "The HTTP Accept header",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Accept-Encoding",
        description = "The HTTP Accept-Encoding header",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "X-Correlation-Id",
        description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together Rest and java calls in log files",
        in = ParameterIn.HEADER,
        required = true,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Encoding",
        description = "The HTTP Content-Encoding header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Length",
        description = "The HTTP Content-Length header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Content-Type",
        description = "The HTTP Content-Type header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Date",
        description = "The HTTP Date header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "Host",
        description = "The HTTP Host header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class)),
      @Parameter(name = "User-Agent",
        description = "The HTTP User-Agent header",
        in = ParameterIn.HEADER,
        schema = @Schema(implementation = String.class))
    },
    responses = {
      @ApiResponse(responseCode = "200", description = "Connection", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files"),
        @Header(name = "Content-Length", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Length header"),
        @Header(name = "Content-Encoding", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Encoding header"),
        @Header(name = "Content-Type", required = true, schema = @Schema(type = "string"),
          description = "The HTTP Content-Type header"),
        @Header(name = "Date", required = false, schema = @Schema(type = "string"),
          description = "The HTTP Date header"),
        @Header(name = "Server", required = false, schema = @Schema(type = "string"),
          description = "The HTTP Server header")
      }, content = {
        @Content(mediaType = "application/json", schema=@Schema(implementation = ConnectionDto.class))
      }),
      @ApiResponse(responseCode = "400", description = "The missing or invalid data supplied for this endpoint", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files")
      }, content = {
        @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
      }),
      @ApiResponse(responseCode = "401", description = "The user is not authorized to access this endpoint", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files")
      }, content = {
        @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
      }),
      @ApiResponse(responseCode = "404", description = "Not found", headers = {
        @Header(name = "X-Correlation-Id", required = true, schema = @Schema(type = "string"),
          description = "X-Correlation-Id is used to carry a unique identifier for each flow. Will be used to group together " +
            "Rest and java calls in log files")
      }, content = {
        @Content(mediaType = "application/json", schema = @Schema(implementation = com.adva.rest.common.ApiResponse.class))
      })
    }
  )
  @GET
  @Path(MDRestPath.EOD.NRIM.CONNECTIONS + "/{id}/routes")
  @Authorization(anyOfPermissions = {PermissionAction.BrowseServices})
  @Override
  public List<RouteDto> getRoutesForConnection(@Parameter(required = true)
                                               @PathParam("id") UUID connectionId) {
    MLTrailDTO mlTrailDTO = getMLTrail(connectionId);
    MLEodAdminStateEntitiesMapper eodEntitiesMapper = new MLEodAdminStateEntitiesMapper(nodeProvider, mlTopologyElementDAO, mlTrailDTO.getId());
    return apiLogger.passResultAndlog(eodEntitiesMapper.getResultList(), connectionId);
  }

    @GET
    @Path(MDRestPath.EOD.NRIM.PROTECTION_GROUPS + "/{id}/switches")
    @Authorization(anyOfPermissions = {PermissionAction.AddService, PermissionAction.BrowseServices})
    @Override
    public List<SwitchDto> getSwitchesForConnection(@Parameter(required = true)
                                                    @PathParam("id") UUID connectionId) {
        MLTrailDTO mlTrailDTO = getMLTrail(connectionId);
        if (mlTrailDTO != null) {
            List<SwitchDto> result = mlEodConnectionProvider.getNetworkConnections(mlTrailDTO.getId());
            return apiLogger.passResultAndlog(result, connectionId);
        }
        return apiLogger.passResultAndlog(null, connectionId);
    }

  //==============================================================================
  //=== Endpoint Translation API =================================================
  //==============================================================================

  /**
   * Translates a SIP-NRL into a Device endpoint (NE-ID, AID). Relevant SFS: FNMS-76454
   * @param sipNrl : SIP NRL
   * @param layerProtocol : the related Layer protocol; can be null
   * @return : Device Endpoint
   */
  @GET
  @Path(MDRestPath.EOD.NRIM.deviceEndpoints)
  @Authorization(anyOfPermissions = {PermissionAction.AddService, PermissionAction.BrowseServices})
  public DeviceEndpointDto getDeviceEndpoint(@QueryParam("sipNrl") String sipNrl,
                                             @QueryParam("layerProtocol") String layerProtocol) {
    DeviceEndpointDto result = endPointHandler.getDeviceEndpoint(sipNrl);
    return apiLogger.passResultAndlog(result, sipNrl, layerProtocol);
  }

  //==============================================================================
  //=== Private Methods ==========================================================
  //==============================================================================

  @MDPersistenceContext
  private MLTrailDTO getMLTrail(UUID connectionId) {
    return nrimDAO.getTopConnection(connectionId);
  }

}
