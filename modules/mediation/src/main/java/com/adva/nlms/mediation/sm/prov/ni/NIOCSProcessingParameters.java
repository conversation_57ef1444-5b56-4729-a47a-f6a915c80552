/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: konstantinosp
 */

package com.adva.nlms.mediation.sm.prov.ni;

import com.adva.nlms.common.property.FNMPropertyConstants;
import com.adva.nlms.common.property.FNMPropertyFactory;
import com.adva.nlms.mediation.common.serviceProvisioning.ProvFSPR7Properties;
import com.adva.nlms.mediation.sm.prov.cp.OCSProcessingParameters;
import com.adva.nlms.opticalparameters.yp.api.OpticalParametersYP;
import ni.proto.external.services.service.ServiceOuterClass;

import java.util.HashMap;
import java.util.Map;

public class NIOCSProcessingParameters extends OCSProcessingParameters {
  private ServiceOuterClass.Service service;
  private String serviceName;
  private Map<String, String> startPortParamsMap;
  private Map<String, String> peerPortParamsMap;

  @Override
  public String getTunnelId() {
    return getOcs().getNiServiceId();
  }

  public ServiceOuterClass.Service getService() {
    return service;
  }

  public void setService(ServiceOuterClass.Service service) {
    this.service = service;
  }

  public String getServiceName() {
    return serviceName;
  }

  public void setServiceName(String serviceName) {
    this.serviceName = serviceName;
  }

  public Map<String, String> getStartPortParamsMap() {
    return startPortParamsMap;
  }

  public void setStartPortParamsMap(ProvFSPR7Properties networkPortProps) {
    if (FNMPropertyFactory.getPropertyAsBoolean(FNMPropertyConstants.SERVICE_FROM_CAP_BROKER,
            FNMPropertyConstants.SERVICE_FROM_CAP_BROKER_DEFAULT))
      startPortParamsMap = networkPortProps.toMapOfStrings();
    else
      startPortParamsMap = fromYPToCommon(networkPortProps.toMapOfStrings());
  }

  public void setStartPortParamsMap(Map<String, String> startPortParamsMap) {
    this.startPortParamsMap = startPortParamsMap;
  }

  public void setPeerPortParamsMap(Map<String, String> peerPortParamsMap) {
    this.peerPortParamsMap = peerPortParamsMap;
  }

  public Map<String, String> getPeerPortParamsMap() {
    return peerPortParamsMap;
  }

  public void setPeerPortParamsMap(ProvFSPR7Properties networkPortProps) {
    if (FNMPropertyFactory.getPropertyAsBoolean(FNMPropertyConstants.SERVICE_FROM_CAP_BROKER,
            FNMPropertyConstants.SERVICE_FROM_CAP_BROKER_DEFAULT))
      peerPortParamsMap = networkPortProps.toMapOfStrings();
    else
      peerPortParamsMap = fromYPToCommon(networkPortProps.toMapOfStrings());
  }

  private Map<String, String> fromYPToCommon(Map<String, String> paramsMap) {
    Map<String, String> commonParamsMap = new HashMap<>(paramsMap.size());
    OpticalParametersYP.toCommon(paramsMap).forEach(p -> commonParamsMap.put(p.key(), p.value()));
    return commonParamsMap;
  }
}
