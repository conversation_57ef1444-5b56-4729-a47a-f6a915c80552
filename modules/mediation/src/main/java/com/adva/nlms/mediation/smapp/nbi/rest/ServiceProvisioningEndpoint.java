/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: mateuszma
 */

package com.adva.nlms.mediation.smapp.nbi.rest;

import com.adva.nlms.common.discover.common.facade.IPDDiscoveryFacade;
import com.adva.nlms.common.mltopologymodel.pce.MLProvisionServiceResult;
import com.adva.nlms.common.mltopologymodel.pce.ServiceViewToServiceDefinitionConverterNew;
import com.adva.nlms.common.mltopologymodel.pce.definition.ServiceDefinitionDto;
import com.adva.nlms.common.mltopologymodel.pce.definition.ServiceIdentificationDto;
import com.adva.nlms.common.mltopologymodel.pce.definition.ServiceViewDto;
import com.adva.nlms.common.pdentityswap.facade.IPDEntitySwapFacade;
import com.adva.nlms.common.provisioning.IProvisioningFacade;
import com.adva.nlms.common.discover.evpn.IEvpnProvisioningFacade;
import com.adva.nlms.common.rest.MDRestPath;
import com.adva.nlms.common.rest.debug.EndpointDebugInfo;
import com.adva.nlms.infrastucture.security.permission.api.PermissionAction;
import com.adva.nlms.mediation.common.rest.Logged;
import com.adva.nlms.mediation.common.rest.MDRestComponent;
import com.adva.nlms.mediation.config.NetworkElementHdlrImpl;
import com.adva.nlms.mediation.infrastructure.concurrent.AdvaExecutors;
import com.adva.nlms.mediation.infrastructure.concurrent.NamedThreadFactory;
import com.adva.nlms.infrastucture.security.permission.api.Authorization;
import com.adva.nlms.mediation.smapp.nbi.rest.provisioning.EvpnControllerAdapter;
import com.adva.nlms.mediation.smapp.nbi.rest.provisioning.ProvisioningFacadeAdapter;
import com.adva.rest.common.api.model.ErrorMessage;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.WebApplicationException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.inject.Singleton;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.container.AsyncResponse;
import jakarta.ws.rs.container.Suspended;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

@Component
@Logged
@Path(MDRestPath.SERVICE_PROVISIONING.PATH)
@Singleton
@Produces({MediaType.APPLICATION_JSON})
@Consumes({MediaType.APPLICATION_JSON})
@MDRestComponent(basicAuthentication = true)
public class ServiceProvisioningEndpoint {
  private static Logger log = LogManager.getLogger(ServiceProvisioningEndpoint.class);

  private final NetworkElementHdlrImpl networkElementHdlr;
  private final ProvisioningFacadeAdapter provisioningFacadeAdapter;
  private final EvpnControllerAdapter evpnControllerAdapter;
  private final IPDDiscoveryFacade pdDiscoveryFacade;
  private final IPDEntitySwapFacade pdEntitySwapFacade;

  private static final ExecutorService executorService = AdvaExecutors.newFixedThreadPool(15,
          new NamedThreadFactory("ServiceProvisioningEndpointThreadPool"),
          true);

  @Autowired
  public ServiceProvisioningEndpoint(NetworkElementHdlrImpl networkElementHdlr,
                                     ProvisioningFacadeAdapter provisioningFacadeAdapter,
                                     EvpnControllerAdapter evpnControllerAdapter,
                                     IPDDiscoveryFacade pdDiscoveryFacade,
                                     IPDEntitySwapFacade pdEntitySwapFacade) {
    this.networkElementHdlr = networkElementHdlr;
    this.provisioningFacadeAdapter = provisioningFacadeAdapter;
    this.evpnControllerAdapter = evpnControllerAdapter;
    this.pdDiscoveryFacade = pdDiscoveryFacade;
    this.pdEntitySwapFacade = pdEntitySwapFacade;
  }

  @GET
  @Path(MDRestPath.SERVICE_PROVISIONING.BANDWIDTH_PROFILES + "/{nodeID}")
  @Authorization(anyOfPermissions = { PermissionAction.GetNetworkElement, PermissionAction.ModifyService })
  public void getBWProfiles(@Suspended AsyncResponse asyncResponse, @PathParam("nodeID") int nodeID) {
    CompletableFuture
            .supplyAsync(() -> getProvisioningFacade().getBWProfiles(nodeID), executorService)
            .thenApply(profiles -> asyncResponse.resume(Response.status(Response.Status.OK).entity(profiles).build())).join();
  }

  @GET
  @Path(MDRestPath.SERVICE_PROVISIONING.NODES)
  @Authorization(permissions = PermissionAction.BrowseNetworks)
  public void getNodes(@Suspended AsyncResponse asyncResponse) {
    CompletableFuture
            .supplyAsync(networkElementHdlr::getAllNEs, executorService)
            .thenApply(networkElements -> asyncResponse.resume(Response.status(Response.Status.OK).entity(networkElements).build())).join();
  }

  @POST
  @Path(MDRestPath.SERVICE_PROVISIONING.PROVISION_SERVICE)
  @Authorization(permissions = PermissionAction.ENSEMBLE_PACKET_DIRECTOR, anyOfPermissions = { PermissionAction.AddService,  PermissionAction.ModifyService})
  public void provisionService(@Suspended AsyncResponse asyncResponse,
                               ServiceDefinitionDto serviceDefinitionDto,
                               @HeaderParam("DebugInfoEnabled") Boolean debugEnabled) {
    provision(asyncResponse, serviceDefinitionDto, debugEnabled);
  }

  @POST
  @Path(MDRestPath.SERVICE_PROVISIONING.PROVISION_SERVICE_VIEW)
  @Authorization(permissions = PermissionAction.ENSEMBLE_PACKET_DIRECTOR, anyOfPermissions = { PermissionAction.AddService,  PermissionAction.ModifyService})
  public void provisionServiceView(@Suspended AsyncResponse asyncResponse,
                                   ServiceViewDto serviceViewDto,
                                   @HeaderParam("DebugInfoEnabled") Boolean debugEnabled) {
    ServiceDefinitionDto serviceDefinitionDto = new ServiceViewToServiceDefinitionConverterNew().convert(serviceViewDto);
    provision(asyncResponse, serviceDefinitionDto, debugEnabled);
  }

  private void provision(AsyncResponse asyncResponse, ServiceDefinitionDto serviceDefinitionDto, Boolean debugEnabled) {
    CompletableFuture
            .supplyAsync(() -> getProvisioningFacade().createServiceFullProcess(serviceDefinitionDto), executorService)
            .thenApply(service -> asyncResponse.resume(Response.status(Response.Status.CREATED).entity(serviceDefinitionDto).build()))
            .exceptionally(ex -> {
              log.error("[provisionService] Cannot provision service. ServiceDefinitionDto={}", serviceDefinitionDto, ex);
              Object entity = ex.getMessage();
              if (debugEnabled != null && debugEnabled)
                entity = new EndpointDebugInfo.Builder().withThrowable(ex).withAdditionalInfo("serviceDefinitionDto", serviceDefinitionDto).build();

              final Response response = Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(entity).build();
              return asyncResponse.resume(response);
            })
            .join();
  }

  @PUT
  @Path(MDRestPath.SERVICE_PROVISIONING.MODIFY_SERVICE)
  @Authorization(permissions = PermissionAction.ENSEMBLE_PACKET_DIRECTOR, anyOfPermissions = { PermissionAction.AddService,  PermissionAction.ModifyService})
  public Response modifyService(ServiceViewDto serviceViewDto) {
    MLProvisionServiceResult mlProvisionServiceResult = provisioningFacadeAdapter.validateProvisionServiceData(serviceViewDto, false);
    if (mlProvisionServiceResult.getResultType() != MLProvisionServiceResult.ResultType.OK && mlProvisionServiceResult.getResultType() != MLProvisionServiceResult.ResultType.WARNING) {
      return Response.status(Response.Status.BAD_REQUEST).entity(new ErrorMessage(mlProvisionServiceResult.getErrorMsg())).build();
    }
    ServiceDefinitionDto serviceDefinitionDto = new ServiceViewToServiceDefinitionConverterNew().convert(serviceViewDto);
    try {
      getProvisioningFacade().modifyServiceFullProcess(serviceDefinitionDto);
      return Response.ok().build();
    } catch (Exception e) {
      log.error("[modifyService] Cannot modify service. ServiceDefinitionDto={}", serviceDefinitionDto, e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(new ErrorMessage(e.getMessage())).build();
    }
  }

  @GET
  @Path(MDRestPath.EVPN.EVPN_SERVICE_SEARCH + "/{serviceName}")
  @Authorization(permissions = { PermissionAction.AddService, PermissionAction.ENSEMBLE_PACKET_DIRECTOR })
  public void searchEvpnService(@Suspended AsyncResponse asyncResponse,
                                @PathParam("serviceName") String serviceName) {
    CompletableFuture
            .supplyAsync(() -> getEvpnProvisioningFacade().searchEvpnService(serviceName), executorService)
            .thenApply(service -> asyncResponse.resume(Response.status(Response.Status.OK)
                    .entity(service).build())).join();
  }


  @POST
  @Path(MDRestPath.EVPN.CREATE)
  @Authorization(permissions = { PermissionAction.AddService, PermissionAction.ENSEMBLE_PACKET_DIRECTOR })
  public Response createEvpnService(ServiceIdentificationDto dto){
    try {
      return Response.status(Response.Status.CREATED).entity(getEvpnProvisioningFacade().createEvpnService(dto)).build();
    } catch (Exception ex) {
      throw new WebApplicationException(Response.status(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), ex.getMessage()).build());
    }
  }

  @DELETE
  @Path(MDRestPath.EVPN.DELETE + "/{serviceId}")
  @Authorization(permissions = { PermissionAction.AddService, PermissionAction.ENSEMBLE_PACKET_DIRECTOR })
  public void deleteEvpnDiscoveredService(@PathParam("serviceId") String serviceId){
    try {
      getEvpnProvisioningFacade().removeEvpnService(Integer.parseInt(serviceId));
    } catch (Exception ex) {
      throw new WebApplicationException(Response.status(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(),
              ex.getMessage()).build());
    }
  }

  @GET
  @Path(MDRestPath.PDDISCOVERED.PD_DISCOVERED_PATH + "/" + MDRestPath.PDDISCOVERED.SERVICE_SEARCH +
          "/{serviceType}" + "/{serviceName}")
  @Authorization(permissions = { PermissionAction.AddService, PermissionAction.ENSEMBLE_PACKET_DIRECTOR })
  public void searchPdDiscoveredService(@Suspended AsyncResponse asyncResponse,
                                        @PathParam("serviceType") String serviceType,
                                        @PathParam("serviceName") String serviceName) {
    CompletableFuture
            .supplyAsync(() -> getPdDiscoveryFacade().searchPdService(serviceType, serviceName), executorService)
            .thenApply(service -> asyncResponse.resume(Response.status(Response.Status.OK)
                    .entity(service).build())).join();
  }

  @POST
  @Path(MDRestPath.PDDISCOVERED.PD_DISCOVERED_PATH + "/" + MDRestPath.PDDISCOVERED.SERVICE_SAVE +
          "/{serviceType}")
  @Authorization(permissions = { PermissionAction.AddService, PermissionAction.ENSEMBLE_PACKET_DIRECTOR })
  public Response savePdDiscoveredService(ServiceIdentificationDto dto,
                                          @PathParam("serviceType") String serviceType){
    try {
      return Response.status(Response.Status.CREATED)
              .entity(getPdDiscoveryFacade().savePdDiscoveredService(dto, serviceType))
              .build();
    } catch (Exception ex) {
      throw new WebApplicationException(Response.status(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(),
              ex.getMessage()).build());
    }
  }

  @DELETE
  @Path(MDRestPath.PDDISCOVERED.PD_DISCOVERED_PATH + "/" + MDRestPath.PDDISCOVERED.SERVICE_DELETE +
          "/{serviceId}")
  @Authorization(permissions = { PermissionAction.AddService, PermissionAction.ENSEMBLE_PACKET_DIRECTOR })
  public void deletePdDiscoveredService(@PathParam("serviceId") String serviceId){
    try {
      getPdDiscoveryFacade().removePdDiscoveredService(Integer.parseInt(serviceId));
    } catch (Exception ex) {
      throw new WebApplicationException(ex.getMessage());
    }
  }

  @GET
  @Path(MDRestPath.PDENTITYSWAP.PD_ENTITY_SWAP_PATH + "/" + MDRestPath.PDENTITYSWAP.INTERFACES_SEARCH +
          "/{serviceId}")
  @Authorization(permissions = {PermissionAction.AddService, PermissionAction.ENSEMBLE_PACKET_DIRECTOR})
  public void searchPdEntitiesForSwap(@Suspended AsyncResponse asyncResponse,
                                      @PathParam("serviceId") String serviceId) {
    CompletableFuture
            .supplyAsync(() -> getPdEntitySwapFacade().searchInterfaces(serviceId), executorService)
            .thenApply(service -> asyncResponse.resume(Response.status(Response.Status.OK)
                    .entity(service).build())).join();
  }


  @DELETE
  @Path(MDRestPath.SERVICE_PROVISIONING.SERVICES + "/{serviceID}")
  @Authorization(permissions = PermissionAction.DELETE_SERVICE)
  public void unprovisionService(@Suspended AsyncResponse asyncResponse,
                                 @PathParam("serviceID") int serviceID,
                                 @HeaderParam("DebugInfoEnabled") Boolean debugEnabled) {
    CompletableFuture
            .supplyAsync(() -> getProvisioningFacade().unprovisionService(String.valueOf(serviceID)), executorService)
            .thenApply(service -> asyncResponse.resume(Response.status(Response.Status.NO_CONTENT).build()))
            .exceptionally(ex -> {
              Object entity = ex.getMessage();
              if (debugEnabled != null && debugEnabled)
                entity = new EndpointDebugInfo.Builder().withThrowable(ex).withAdditionalInfo("serviceID", serviceID).build();
              final Response response = Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(entity).build();
              return asyncResponse.resume(response);
            })
            .join();
  }

  @GET
  @Path(MDRestPath.SERVICE_PROVISIONING.SERVICES + "/{serviceID}")
  @Authorization(permissions = PermissionAction.BrowseServices)
  public void getService(@Suspended AsyncResponse asyncResponse, @PathParam("serviceID") int serviceID){
    CompletableFuture
            .supplyAsync(() -> getProvisioningFacade().getService(serviceID), executorService)
            .thenApply(service -> asyncResponse.resume(Response.status(Response.Status.OK)
                    .entity(service).build())).join();

  }

  @POST
  @Path(MDRestPath.SERVICE_PROVISIONING.VALIDATE)
  @Authorization(anyOfPermissions = { PermissionAction.AddService, PermissionAction.ModifyService })
  public MLProvisionServiceResult validateProvisionServiceData(String dataKey){
    return getProvisioningFacade().validateProvisionServiceData(dataKey);
  }

  @POST
  @Path(MDRestPath.SERVICE_PROVISIONING.VALIDATE_NEREACHABILITY)
  @Authorization(anyOfPermissions = { PermissionAction.AddService, PermissionAction.ModifyService })
  public MLProvisionServiceResult validateProvisionServiceDataNEReachability(String dataKey){
    return getProvisioningFacade().validateProvisionServiceDataNEReachability(dataKey);
  }

  @POST
  @Path(MDRestPath.SERVICE_PROVISIONING.UPDATE_TAG_MANAGEMENT_SINGLE_NODE_SERVICE)
  @Authorization(permissions = PermissionAction.AddService)
  public void updateSecondEndpointForSingleNodeService(@Suspended AsyncResponse asyncResponse, ServiceViewDto serviceViewDto) {
    CompletableFuture
            .supplyAsync(() -> getProvisioningFacade().updateForSingleNodeService(serviceViewDto), executorService)
            .thenApply(response -> asyncResponse.resume(Response.status(Response.Status.OK)
                    .entity(response).build())).join();
  }

  @POST
  @Path(MDRestPath.SERVICE_PROVISIONING.CLEAN_VIEW_DTO_FOR_SINGLE_NODE_SERVICE)
  @Authorization(anyOfPermissions = { PermissionAction.AddService, PermissionAction.ModifyService })
  public void cleanViewDtoForSingleNodeService(@Suspended AsyncResponse asyncResponse, ServiceViewDto serviceViewDto) {
    CompletableFuture
            .supplyAsync(() -> getProvisioningFacade().cleanForSingleNodeService(serviceViewDto), executorService)
            .thenApply(response -> asyncResponse.resume(Response.status(Response.Status.OK)
                    .entity(response).build())).join();
  }

  private IProvisioningFacade getProvisioningFacade() {
    return provisioningFacadeAdapter;
  }

  private IEvpnProvisioningFacade getEvpnProvisioningFacade() {
    return evpnControllerAdapter;
  }

  private IPDDiscoveryFacade getPdDiscoveryFacade() {
    return pdDiscoveryFacade;
  }

  private IPDEntitySwapFacade getPdEntitySwapFacade() {
    return pdEntitySwapFacade;
  }
}
