/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: tomaszm
 */

package com.adva.nlms.mediation.config;

import com.adva.nlms.mediation.topology.RegisteredTreeNodesHdlr;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.inject.Singleton;

import java.util.Optional;
import java.util.UUID;

@Component
@Singleton
public class RegisteredNetworkElementsHdlr implements RegisteredNetworkElements{


  private final RegisteredTreeNodesHdlr registeredTreeNodesHdlr;

  private final NetworkElementDAO networkElementDAO;

  @Autowired
  public RegisteredNetworkElementsHdlr(RegisteredTreeNodesHdlr registeredTreeNodesHdlr, NetworkElementDAO networkElementDAO) {
    this.registeredTreeNodesHdlr = registeredTreeNodesHdlr;
    this.networkElementDAO = networkElementDAO;
  }

  public NetworkElementImpl getNeById(int neId) throws NoSuchNetworkElementException {
    return (NetworkElementImpl) registeredTreeNodesHdlr.getNEByID(neId);
  }

  public NetworkElementImpl getNEImpl(final String ipAddress, boolean checkWithSystemIpAlso) throws NoSuchNetworkElementException {
    return (NetworkElementImpl) registeredTreeNodesHdlr.getNEImpl(ipAddress,checkWithSystemIpAlso);
  }


  public boolean isNEAvailable(int neId) {
    return registeredTreeNodesHdlr.isNEAvailable(neId);
  }

  public int getNeType(int neId) {
    return registeredTreeNodesHdlr.getNeType(neId);
  }

  public void removeNeFromCache(String ip) {
    registeredTreeNodesHdlr.removeNEFromCache(ip);
  }

  public NetworkElement getByIpAddress(String ipAddress) {
    return registeredTreeNodesHdlr.getByIpAddress(ipAddress);
  }

  public int getSubnetId(int neId) throws NoSuchNetworkElementException {
    try {
      NetworkElement networkElement = registeredTreeNodesHdlr.getNEByID(neId);
      return networkElement.getParentID();
    } catch (NoSuchNetworkElementException nsnee) {
      NetworkElementDBImpl networkElementDB = networkElementDAO.getNetworkElement(NetworkElementDBImpl.class, neId);
      if (networkElementDB == null) {
        throw new NoSuchNetworkElementException("No such network element ID " + neId);
      }
      return networkElementDB.getSubnet().getId();
    }
  }
  public Optional<Integer> getDomainIdByUUID(UUID uuid) {
    return networkElementDAO.getNeId(uuid);
  }
}