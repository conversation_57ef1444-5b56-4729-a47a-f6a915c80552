/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: adaemmig
 */

package com.adva.nlms.mediation.event;

import com.adva.common.util.collect.Iterables;
import com.adva.fm.api.dto.Disposition;
import com.adva.fm.api.dto.EventResource;
import com.adva.fm.api.dto.Origin;
import com.adva.fm.api.dto.Severity;
import com.adva.fm.api.dto.SourceType;
import com.adva.fm.api.dto.lognetworkmgmt.Acknowledged;
import com.adva.fm.api.dto.lognetworkmgmt.Correlation;
import com.adva.fm.api.dto.lognetworkmgmt.Direction;
import com.adva.fm.api.dto.lognetworkmgmt.EventClass;
import com.adva.fm.api.dto.lognetworkmgmt.EventUpdateType;
import com.adva.fm.api.dto.lognetworkmgmt.Location;
import com.adva.fm.api.dto.lognetworkmgmt.LogENCAlarmEvent;
import com.adva.fm.api.dto.lognetworkmgmt.LogLifecycleEvent;
import com.adva.fm.api.dto.lognetworkmgmt.LogNEAlarmEvent;
import com.adva.fm.api.dto.lognetworkmgmt.LogNEEvent;
import com.adva.fm.api.dto.lognetworkmgmt.LogNELifecycleEvent;
import com.adva.fm.api.dto.lognetworkmgmt.LogNetworkMgmtEvent;
import com.adva.fm.api.dto.lognetworkmgmt.LogObjectConfigChange;
import com.adva.fm.api.dto.lognetworkmgmt.LogObjectCreation;
import com.adva.fm.api.dto.lognetworkmgmt.LogObjectDeletion;
import com.adva.fm.api.dto.lognetworkmgmt.LogObjectStateChange;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.config.netypes.NEType;
import com.adva.nlms.common.event.EventDetectionType;
import com.adva.nlms.common.event.EventStatus;
import com.adva.nlms.common.event.EventType;
import com.adva.nlms.commondefinition.ResourceType;
import com.adva.nlms.commondefinition.namerepresentation.NameRepresentation;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.common.persistence.MDPersistenceHelper;
import com.adva.nlms.mediation.config.NetworkElementDBImpl;
import com.adva.nlms.mediation.event.nbi.EventUpdateFieldEnum;
import com.adva.nlms.mediation.mltopologymodel.model.dao.MLTopologyElementDAO;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLLayerAdaptationDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLPtpConnectionDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLTopologyElementDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLTrailDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLNetworkLayer;
import com.adva.nlms.mediation.topology.LineDBImpl;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class LogNetworkMgmtEventBuilder {

  private static final Logger log = LogManager.getLogger(LogNetworkMgmtEventBuilder.class);

  @Autowired
  private MLTopologyElementDAO mlDAO;

  //private final LogNetworkMgmtBuilder logObjectCreationBuilder = new LogObjectCreationBuilder();
  //private final LogNetworkMgmtBuilder logObjectDeletionBuilder = new LogObjectDeletionBuilder();
  private final LogNetworkMgmtBuilder logObjectConfigChangeBuilder = new LogObjectConfigChangeBuilder();
  //private final LogNetworkMgmtBuilder logObjectStateChangeBuilder = new LogObjectStateChangeBuilder();
  private final LogNetworkMgmtBuilder logENCAlarmEventBuilder = new LogENCAlarmEventBuilder();
  private final LogNetworkMgmtBuilder logNEAlarmEventBuilder = new LogNEAlarmEventBuilder();
  private final LogNetworkMgmtBuilder logNELifecycleEventBuilder = new LogNELifecycleEventBuilder();

  @MDPersistenceContext
  public LogNetworkMgmtEvent create(EventDTO eventDTO, EventUpdateFieldEnum updateType) {
    if (eventDTO == null || updateType == EventUpdateFieldEnum.SYNC)
      return null;

    try {
      // ENC Events/Alarms
      if (eventDTO.detectionType == EventDetectionType.SERVER) {
        switch (eventDTO.type) {
          case SYSTEM:
            return logObjectConfigChangeBuilder.create(eventDTO, updateType);
            //switch (eventDTO.dbChgCategory) {
            //  case CREATE: return logObjectCreationBuilder.create(eventDTO, updateType);
            //  case DELETE: return logObjectDeletionBuilder.create(eventDTO, updateType);
            //  case UPDATE: return logObjectConfigChangeBuilder.create(eventDTO, updateType);
            //};
          case RAISED:
          case CLEARED:
          case CLEARING:
            return logENCAlarmEventBuilder.create(eventDTO, updateType);
        }
      }
      // NE Events/Alarms
      else {
        switch (eventDTO.type) {
          case RAISED:
          case CLEARED:
          case CLEARING:
            return logNEAlarmEventBuilder.create(eventDTO, updateType);
          case TRANSIENT:
            return logNELifecycleEventBuilder.create(eventDTO, updateType);
        }
      }
    }catch (RuntimeException e) {
      log.error("LogNetworkMgmtEventBuilder.create failed id={}", eventDTO.id, e);
    }
    return null;
  }

  private abstract class LogNetworkMgmtBuilder {
    abstract protected LogNetworkMgmtEvent create(EventDTO eventDTO, EventUpdateFieldEnum updateType);
    
    protected <T extends LogNetworkMgmtEvent.Builder> void populate(T dto, EventDTO eventDTO, EventUpdateFieldEnum updateType) {
      // handle event labels
      Map<String, Object> eventLabels = new HashMap<>();
      eventLabels.put("id", String.valueOf(eventDTO.id)); //TODO This is a workaround for the GUI based on discussion with Yoav/Shai on 05.05.2025
      if (eventDTO.sourceNE_ID > 0) {
        NetworkElementDBImpl neDB = MDPersistenceHelper.find(NetworkElementDBImpl.class, eventDTO.sourceNE_ID);
        if (neDB != null && neDB.getUuid() != null)
          eventLabels.put("neUuid", neDB.getUuid().toString());
      }
      dto.eventLabels(eventLabels);
      // handle other attributes
      dto.id(String.valueOf(eventDTO.id));
      dto.update(getEventUpdateType(updateType));
      dto.systemName(eventDTO.sourceName);
      dto.systemIpAddress(eventDTO.sourceNE_IP);
      dto.eventClassification(getEventClass(eventDTO));
      dto.acknowledged(getAcknowledged(eventDTO));
      dto.comment(eventDTO.comment);
      dto.corr(getCorrelation(eventDTO));
      dto.corRef(eventDTO.rootCause > 0 ? String.valueOf(eventDTO.rootCause) : null);
      // add correlation-Reference for CLR notifications
      if (updateType == EventUpdateFieldEnum.NEW && eventDTO.type == EventType.CLEARING) {
        dto.corr(Correlation.PRIMARY);
        dto.corRef(String.valueOf(eventDTO.firstRaisedAlarm));
      }
      // AppNetworkMgmtEvent
      dto.origin(getOrigin(eventDTO));
      dto.disposition(getDisposition(eventDTO));
      dto.description(eventDTO.getDescription() != null ? eventDTO.getDescription().toString() : null);
      dto.security(eventDTO.securityEvent);
      dto.eventName(eventDTO.shortName);
      dto.severity(getSeverity(eventDTO));
      dto.primaryResource(getPrimaryResource(eventDTO));
      dto.secondaryResources(getSecondaryResources(eventDTO));
      // AppCommonEvent
      dto.eventTime(eventDTO.nmsTimeStamp);
    }
  }

  private abstract class LogENCEventBuilder extends LogNetworkMgmtBuilder {
  }

  private abstract class LogLifecycleEventBuilder extends LogENCEventBuilder {
    protected <T extends LogLifecycleEvent.Builder> void populate(T builder, EventDTO eventDTO, EventUpdateFieldEnum updateType) {
      super.populate(builder, eventDTO, updateType);
      builder.sourceType(SourceType.UNKNOWN);
    }
  }

  private class LogObjectCreationBuilder extends LogLifecycleEventBuilder {
    protected LogNetworkMgmtEvent create(EventDTO eventDTO, EventUpdateFieldEnum updateType) {
      LogObjectCreation.Builder builder = new LogObjectCreation.Builder();
      populate(builder, eventDTO, updateType);
      return builder.build();
    }

    protected <T extends LogObjectCreation.Builder> void populate(T builder, EventDTO eventDTO, EventUpdateFieldEnum updateType) {
      super.populate(builder, eventDTO, updateType);
      builder.attributeValues(null);
    }
  }

  private class LogObjectDeletionBuilder extends LogLifecycleEventBuilder {
    protected LogNetworkMgmtEvent create(EventDTO eventDTO, EventUpdateFieldEnum updateType) {
      LogObjectDeletion.Builder builder = new LogObjectDeletion.Builder();
      populate(builder, eventDTO, updateType);
      return builder.build();
    }

    protected <T extends LogObjectDeletion.Builder> void populate(T builder, EventDTO eventDTO, EventUpdateFieldEnum updateType) {
      super.populate(builder, eventDTO, updateType);
      builder.attributeValues(null);
    }
  }

  private class LogObjectConfigChangeBuilder extends LogLifecycleEventBuilder {
    protected LogNetworkMgmtEvent create(EventDTO eventDTO, EventUpdateFieldEnum updateType) {
      LogObjectConfigChange.Builder builder = new LogObjectConfigChange.Builder();
      populate(builder, eventDTO, updateType);
      return builder.build();
    }

    protected <T extends LogObjectConfigChange.Builder> void populate(T builder, EventDTO eventDTO, EventUpdateFieldEnum updateType) {
      super.populate(builder, eventDTO, updateType);
      builder.changedValues(null);
    }
  }

  private class LogObjectStateChangeBuilder extends LogLifecycleEventBuilder {
    protected LogNetworkMgmtEvent create(EventDTO eventDTO, EventUpdateFieldEnum updateType) {
      LogObjectStateChange.Builder builder = new LogObjectStateChange.Builder();
      populate(builder, eventDTO, updateType);
      return builder.build();
    }

    protected <T extends LogObjectStateChange.Builder> void populate(T builder, EventDTO eventDTO, EventUpdateFieldEnum updateType) {
      super.populate(builder, eventDTO, updateType);
      builder.changedValues(null);
    }
  }

  private abstract class LogNEEventBuilder extends LogNetworkMgmtBuilder {
    protected <T extends LogNEEvent.Builder> void populate(T builder, EventDTO eventDTO, EventUpdateFieldEnum updateType) {
      super.populate(builder, eventDTO, updateType);
      builder.neType(NEType.valueOf(eventDTO.sourceNEType).getName());
      builder.neTrapId(eventDTO.getTrapID());
      builder.neEventTime(eventDTO.neTimeStamp);
      builder.physLocation(eventDTO.phyLocation);
      builder.mtosiNeType(null); //TODO
    }
  }

  private class LogENCAlarmEventBuilder extends LogNEEventBuilder {
    protected LogNetworkMgmtEvent create(EventDTO eventDTO, EventUpdateFieldEnum updateType) {
      LogENCAlarmEvent.Builder builder = new LogENCAlarmEvent.Builder();
      populate(builder, eventDTO, updateType);
      return builder.build();
    }

    protected <T extends LogENCAlarmEvent.Builder> void populate(T builder, EventDTO eventDTO, EventUpdateFieldEnum updateType) {
      super.populate(builder, eventDTO, updateType);
      builder.impairment(eventDTO.impairment);
    }
  }

  private class LogNEAlarmEventBuilder extends LogNEEventBuilder {
    protected LogNetworkMgmtEvent create(EventDTO eventDTO, EventUpdateFieldEnum updateType) {
      LogNEAlarmEvent.Builder builder = new LogNEAlarmEvent.Builder();
      populate(builder, eventDTO, updateType);
      return builder.build();
    }

    protected <T extends LogNEAlarmEvent.Builder> void populate(T builder, EventDTO eventDTO, EventUpdateFieldEnum updateType) {
      super.populate(builder, eventDTO, updateType);
      builder.impairment(eventDTO.impairment);
      builder.direction(getDirection(eventDTO));
      builder.location(getLocation(eventDTO));
    }
  }

  private class LogNELifecycleEventBuilder extends LogNEEventBuilder {
    protected LogNetworkMgmtEvent create(EventDTO eventDTO, EventUpdateFieldEnum updateType) {
      LogNELifecycleEvent.Builder builder = new LogNELifecycleEvent.Builder();
      populate(builder, eventDTO, updateType);
      return builder.build();
    }

    protected <T extends LogNELifecycleEvent.Builder> void populate(T builder, EventDTO eventDTO, EventUpdateFieldEnum updateType) {
      super.populate(builder, eventDTO, updateType);
    }
  }

  //-------------------------------------------------------------------

  private EventUpdateType getEventUpdateType(EventUpdateFieldEnum updateType) {
    return switch (updateType) {
      case NEW -> EventUpdateType.NEW;
      case UPDATE -> EventUpdateType.UPDATE;
      default -> null;
    };
  }

  private Origin getOrigin(EventDTO eventDTO) {
    return switch (eventDTO.detectionType) {
      case TRAP -> Origin.TRAP;
      case TRAP_LOG -> Origin.LOG;
      case CONF_STATUS_UPDATE -> Origin.UPD;
      case SERVER -> Origin.SYS;
    };
  }

  private Disposition getDisposition(EventDTO eventDTO) {
    return switch (eventDTO.type) {
      case RAISED, CLEARED -> Disposition.ARM;
      case CLEARING -> Disposition.CLR;
      default -> Disposition.TRN;
    };
  }

  private Severity getSeverity(EventDTO eventDTO) {
    return switch (eventDTO.severity) {
      case CRITICAL -> Severity.CRITICAL;
      case MAJOR -> Severity.MAJOR;
      case MINOR -> Severity.MINOR;
      case WARNING -> Severity.WARNING;
      case INFORMATION -> Severity.INFORMATION;
      default -> Severity.INFORMATION;
    };
  }

  private Acknowledged getAcknowledged(EventDTO eventDTO) {
    return eventDTO.acknowledge ? Acknowledged.ACKNOWLEDGED : Acknowledged.NOT_ACKNOWLEDGED;
  }

  private EventClass getEventClass(EventDTO eventDTO) {
    return switch (eventDTO.alarmClass) {
      case UNKNOWN -> EventClass.NONE;
      case COMMUNICATIONS -> EventClass.COMMUNICATIONS;
      case ENVIRONMENT -> EventClass.ENVIRONMENTAL;
      case EQUIPMENT -> EventClass.EQUIPMENT;
      case PROC_ERROR -> EventClass.PROCESSING;
      case QOS -> EventClass.QOS;
    };
  }

  private Correlation getCorrelation(EventDTO eventDTO) {
    return eventDTO.correlation == EventStatus.Correlation.PRIMARY.getType() ? Correlation.PRIMARY : Correlation.REDUNDANT;
  }

  private Direction getDirection(EventDTO eventDTO) {
    com.adva.nlms.common.event.Direction eventDirection = com.adva.nlms.common.event.Direction.valueOf(eventDTO.direction);
    if (eventDirection == null)
      return null;
    return switch (eventDirection) {
      case NONE -> Direction.NONE;
      case BIDIRECTIONAL -> Direction.BIDIRECTIONAL;
      case BOTH_DIRECTIONS -> Direction.BOTH_DIRECTIONS;
      case NOT_APPLICABLE -> Direction.NOT_APPLICABLE;
      case RECEIVE_ONLY -> Direction.RECEIVE_ONLY;
      case TRANSMIT_ONLY -> Direction.TRANSMIT_ONLY;
      case UNIDIRECTIONAL -> Direction.UNIDIRECTIONAL;
    };
  }

  private Location getLocation(EventDTO eventDTO) {
    com.adva.nlms.common.event.Location eventLocation = com.adva.nlms.common.event.Location.valueOf(eventDTO.location);
    if (eventLocation == null)
      return null;
    return switch (eventLocation) {
      case NONE -> Location.NONE;
      case BOTH_ENDS -> Location.BOTH_ENDS;
      case FAR_END -> Location.FAR_END;
      case NEAR_END -> Location.NEAR_END;
      case NOT_APPLICABLE -> Location.NOT_APPLICABLE;
    };
  }

  /**
   * The Primary Resource setting follows the rules for the Unique Key, i.e. this is set based on Priority.
   * For EOD EVO the CS has higher priority than the Connection (for regular EOD it's oposite)
   *
   * 1. Device entity
   * 2. Physical Link (not 100% sure but ok for now)
   * 3. NE related
   * 4. Connectivity Service
   * 5. Service (Connection / ML Trail)
   * 6. Connectivity Service Folder
   * 7. Service Folder
   * @param eventDTO event to derive the primary resource from
   * @return resource build using the objects associated to the event
   */
  private EventResource getPrimaryResource(EventDTO eventDTO) {
    // Device entity related
    if (!EntityIndex.isZero(eventDTO.getObjectIndex())) {
      return new EventResource(ResourceType.DEVICE_ENTITY, null, eventDTO.objectIndex.toString(),
                               Map.of(NameRepresentation.USER_LABEL, eventDTO.entityDescription));
    }
    // Link related (e.g. Fiber-Alarm)
    else if (eventDTO.lineID > 0) {
      return Iterables.getFirst(getLinkResources(eventDTO), null);
    }
    // NE related (e.g. DCN Alarm)
    else if (eventDTO.sourceNE_ID > 0) {

    }
    // Connectivity service related (e.g. LSDI - loss of service dataplane information)
    else if (eventDTO.containsAssocUuidObjectType(EventAssocObjectId.ObjectType.EOD_CONNECTIVITY_SERVICE)) {
      List<EventResource> csResources = getCSResources(eventDTO);
      return csResources.isEmpty() ? null : csResources.get(0);
    }
    // Connection related (e.g. Security Event Add Service)
    else if (eventDTO.containsAssocMlObjectType(EventAssocObjectId.ObjectType.MLT_SERVICE)) {
      List<EventResource> connectionResources = getConnectionResources(eventDTO);
      // Event association may still exist for a moment after trail is deleted, making the look up of the ML trail object/resource creation not possible
      return connectionResources.isEmpty() ? null : connectionResources.get(0);
    }
    // Tenant related
    else if (eventDTO.containsAssocUuidObjectType(EventAssocObjectId.ObjectType.EOD_CONNECTIVITY_SERVICE_FOLDER)) {
      List<EventResource> tenantResources = getTenantResources(eventDTO);
      return tenantResources.isEmpty() ? null : tenantResources.get(0);
    }
    // No primary resource associate with MLT_SERVICE_FOLDER (legacy EOD CS)
    // else it is a global event
    return null;
  }

  private List<EventResource> getSecondaryResources(EventDTO eventDTO) {
    List<EventResource> resourceList = new ArrayList<>();
    // Device entity related
    if (!EntityIndex.isZero(eventDTO.getObjectIndex())) {
      resourceList.addAll(getLinkResources(eventDTO));
      resourceList.addAll(getCSResources(eventDTO));
      resourceList.addAll(getConnectionResources(eventDTO));
      resourceList.addAll(getTenantResources(eventDTO));
    }
    // Link related (e.g. Fiber-Alarm)
    else if (eventDTO.lineID > 0) {
      resourceList.addAll(getCSResources(eventDTO));
      resourceList.addAll(getConnectionResources(eventDTO));
      resourceList.addAll(getTenantResources(eventDTO));
    }
    // NE related (e.g. DCN Alarm)
    else if (eventDTO.sourceNE_ID > 0) {
      // not related to any other object
    }
    // Connectivity service related (e.g. LSDI - loss of service dataplane information)
    else if (eventDTO.containsAssocUuidObjectType(EventAssocObjectId.ObjectType.EOD_CONNECTIVITY_SERVICE)) {
      resourceList.addAll(getConnectionResources(eventDTO));
      resourceList.addAll(getTenantResources(eventDTO));
    }
    // Connection related (e.g. Security Event Add Service)
    else if (eventDTO.containsAssocMlObjectType(EventAssocObjectId.ObjectType.MLT_SERVICE)) {
      resourceList.addAll(getTenantResources(eventDTO));
    }
    // else it is a global event

    return resourceList;
  }

  private List<EventResource> getLinkResources(EventDTO eventDTO) {
    if (eventDTO.lineID > 0) {
      LineDBImpl lineDB = MDPersistenceHelper.find(LineDBImpl.class, eventDTO.lineID);
      if (lineDB != null) {
        return List.of(new EventResource(ResourceType.LOGICAL_LINK, "/Link", String.valueOf(lineDB.getId()),
                                         Map.of(NameRepresentation.USER_LABEL, lineDB.getLabel())));
      }
      else {
        return List.of(new EventResource(ResourceType.LOGICAL_LINK, "/Link", String.valueOf(eventDTO.lineID), null));
      }
    }
    return Collections.emptyList();
  }

  private List<EventResource> getCSResources(EventDTO eventDTO) {
    List<EventResource> resourceList = new ArrayList<>();
    for (EventAssocObjectId entry : eventDTO.getAssocUuidObjects(EventAssocObjectId.ObjectType.EOD_CONNECTIVITY_SERVICE)) {
      String uuidString = entry.getObjectUuid().toString();
      resourceList.add(new EventResource(ResourceType.CONNECTIVITY_SERVICE, "/enc/v1/eod/csm/connectivity-services/" + uuidString, uuidString,
                                           Map.of(NameRepresentation.USER_LABEL, eventDTO.serviceName)));
    }
    return resourceList;
  }

  private List<EventResource> getConnectionResources(EventDTO eventDTO) {
    List<EventResource> resourceList = new ArrayList<>();
    List<Integer> mlTrailIds = eventDTO.getMlTopologyObjects(EventAssocObjectId.ObjectType.MLT_SERVICE)
        .stream()
        .map(EventAssocObjectId::getObjectId)
        .toList();
    for (Integer mlTrailId : mlTrailIds) {
      // TODO: uuid should be stored in the Event
      MLTrailDBImpl trailDB = mlDAO.getTrailByID(mlTrailId);
      if (trailDB != null && trailDB.getUuid() != null) {
        resourceList.add(new EventResource(ResourceType.CONNECTION, "/Connection", trailDB.getUuid().toString(),
                                           Map.of(NameRepresentation.USER_LABEL, trailDB.getLabel())));
        // special handling for OCH associated with a OTSiMC+payload layer
        if (trailDB.getLayer() == MLNetworkLayer.OCH && StringUtils.isEmpty(trailDB.getAEndCep())) {
          MLTrailDBImpl clientTrailDB = getClientTrailForOchTrail(trailDB);
          if (clientTrailDB != null) {
            resourceList.add(new EventResource(ResourceType.CONNECTION, "/Connection", clientTrailDB.getUuid().toString(),
              Map.of(NameRepresentation.USER_LABEL, clientTrailDB.getLabel())));
          }
        }
      }
    }
    return resourceList;
  }

  private MLTrailDBImpl getClientTrailForOchTrail(MLTrailDBImpl trailDB) {
    MLLayerAdaptationDBImpl layerAdaptationDB = mlDAO.getAdaptationForService(trailDB);
    if (layerAdaptationDB != null) {
      for (MLTopologyElementDBImpl clientConn : layerAdaptationDB.getClientEntities()) {
        if (clientConn instanceof MLPtpConnectionDBImpl ptpConnectionDB && ptpConnectionDB.isLinkConnection()) {
          List<MLTrailDBImpl> clientTrails = mlDAO.getServicesForConnection(ptpConnectionDB.getId());
          return (clientTrails.size() == 1) ? clientTrails.get(0) : null;
        }
      }
    }
    return null;
  }

  private List<EventResource> getTenantResources(EventDTO eventDTO) {
    return Collections.emptyList();
  }
}