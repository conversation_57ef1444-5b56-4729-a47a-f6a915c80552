/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: spyrosm
 */

package com.adva.nlms.mediation.sm.prov.ni.servicecreation;

import com.adva.apps.sm.Definition;
import com.adva.nlms.common.sm.ProtMechanism;
import com.adva.apps.sm.ServiceLayer;
import com.adva.nlms.common.sm.diversity.DiversityType;
import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.mediation.config.fsp_r7.cp.interfaces.IWdmPathHandler;
import com.adva.nlms.mediation.mltopologymodel.model.dao.MLTopologyElementDAO;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLTrailDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLTopologyMOReference;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLMoReferenceType;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLTopologyServiceMoReference;
import com.adva.nlms.mediation.mltopologymodel.resources.MLServiceHelper;
import com.adva.nlms.mediation.mltopologymodel.service.intent.implementation.db.ServiceIntent;
import com.adva.nlms.mediation.ne_comm.f7.cp.objects.PathDefinition;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.sm.ServiceManagerCtrl;
import com.adva.nlms.mediation.sm.dao.ConnectionDAO;
import com.adva.nlms.mediation.sm.diversity.ServiceDiversityHelper;
import com.adva.nlms.mediation.sm.diversity.TrailDiversityData;
import com.adva.nlms.mediation.sm.model.AbstractConnectionDBImpl;
import com.adva.nlms.mediation.sm.prov.OpticalChannelService;
import com.adva.nlms.mediation.sm.prov.SMProvException;
import com.adva.nlms.mediation.sm.prov.SMProvParams;
import com.adva.nlms.mediation.sm.prov.cp.OCSPathHdlr;
import com.adva.nlms.mediation.sm.prov.cp.OCSProcessingParameters;
import com.adva.nlms.mediation.sm.prov.ni.NIOCSProcessingParameters;
import com.adva.nlms.mediation.sm.prov.ni.ServiceEnumMaps;
import com.adva.nlms.mediation.sm.prov.ni.model.NITunnelDBImpl;
import com.adva.nlms.mediation.sm.prov.releaseadopt.ServiceTunnelHelper;
import com.adva.nlms.mediation.sm.validation.interfaces.ServiceConnectionFSPR7;
import ni.proto.external.common.PortParamsOuterClass;
import ni.proto.external.common.map.MapOuterClass;
import ni.proto.external.common.signal_description.FacilityType;
import ni.proto.external.common.signal_description.FlexgridParams;
import ni.proto.external.common.signal_description.SignalDescription;
import ni.proto.external.common.signal_description.SignalWdm;
import ni.proto.external.services.path.PathOuterClass;
import ni.proto.external.services.service.ServiceOuterClass;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class ServiceConfigurator extends AbstractServiceConfigurator {
  private static final Logger log = LogManager.getLogger(ServiceConfigurator.class);
  private final ServiceManagerCtrl serviceManagerCtrl;
  private final ServiceConnectionFSPR7 serviceConnection;

  private OCSPathHdlr ocsPathHandler;
  private final IWdmPathHandler wdmPathManager;

  @Autowired
  public ServiceConfigurator(ServiceManagerCtrl serviceManagerCtrl, ServiceConnectionFSPR7 serviceConnection,
                             IWdmPathHandler wdmPathManager) {
    super();
    this.serviceManagerCtrl = serviceManagerCtrl;
    this.serviceConnection = serviceConnection;
    this.wdmPathManager = wdmPathManager;
  }

  public ServiceOuterClass.ServiceConfiguration getServiceConfiguration(SMProvParams smProvParams, OCSProcessingParameters parameters) throws SMProvException {
    initOcsPathHandler(parameters);
    ServiceParameters serviceParameters = mapProvParamsToServiceParameters(smProvParams, parameters);
    return ServiceOuterClass.ServiceConfiguration.newBuilder()
            .setName(serviceParameters.getName())
            .setSource(serviceParameters.getSource())
            .setDestination(serviceParameters.getDestination())
            .setWorkingPath(serviceParameters.getWorkingPath())
            .setProtection(serviceParameters.getProtection())
            .setRestoration(serviceParameters.getRestoration())
            .addAllDiversities(serviceParameters.getDiversities())
            .setSignal(serviceParameters.getSignalDescription())
            .build();
  }

  void initOcsPathHandler(OCSProcessingParameters parameters) throws SMProvException {
    ocsPathHandler = new OCSPathHdlr(serviceConnection, serviceManagerCtrl, wdmPathManager, true);
    try {
      ocsPathHandler.setOcsParameterDestNode(parameters);
    } catch (SNMPCommFailure snmpCommFailure) {
      throw new SMProvException(snmpCommFailure.getMessage(), snmpCommFailure);
    }
  }

  private ServiceParameters mapProvParamsToServiceParameters(SMProvParams smProvParams, OCSProcessingParameters parameters) throws SMProvException {
    PathOuterClass.ProvisionedPath excludedPath = getExcludedPath(smProvParams, parameters);
    PathDefinition pathDefinition = getProvisionPath(parameters);
    String tunnelName = getTunnelName(smProvParams, parameters);
    boolean isMFlex = helper.isMFlex(smProvParams);
    return new ServiceParameters.Builder()
            .name(tunnelName)
            .source(helper.getSourceEdge(parameters))
            .destination(helper.getDestinationEdge(parameters))
            .workingPath(getWorkingPath(pathDefinition, excludedPath))
            .protection(getProtection(smProvParams.getProtMechanism(), pathDefinition, excludedPath))
            .restoration(getRestoration(smProvParams, parameters, excludedPath))
            .diversities(getDiversities(smProvParams))
            .signalDescription(isMFlex ? getWDMSignalDescriptionMFlex(smProvParams, (NIOCSProcessingParameters) parameters) : getWDMSignalDescription(smProvParams))
            .build();
  }

  private SignalDescription getWDMSignalDescriptionMFlex(SMProvParams smProvParams, NIOCSProcessingParameters parameters) {
    FacilityType facilityType = getFacilityType(smProvParams);
    SignalWdm signalWdm = getSignalWdm(smProvParams);
    PortParamsOuterClass.PortParams nearEndPortParams = getPortParams(parameters.getStartPortParamsMap());
    PortParamsOuterClass.PortParams farEndPortParams = getPortParams(parameters.getPeerPortParamsMap());
    return SignalDescription.newBuilder()
            .setWdm(signalWdm)
            .setFacilityType(facilityType)
            .setPortParams(nearEndPortParams)
            .setFarEndPortParams(farEndPortParams)
            .build();
  }

  private PortParamsOuterClass.PortParams getPortParams(Map<String, String> portParamsMap) {
    MapOuterClass.Map.Builder mapBuilder = MapOuterClass.Map.newBuilder();
    portParamsMap.forEach((key, value) -> mapBuilder.addKvp(MapOuterClass.KeyValuePair.newBuilder().setK(key).setV(value).build()));
    return PortParamsOuterClass.PortParams.newBuilder().setParams(mapBuilder.build()).build();
  }

  private String getTunnelName(SMProvParams smProvParams, OCSProcessingParameters parameters) {
    if(smProvParams.getServiceConnectivityName()!=null) {
      return smProvParams.getServiceLayer() == ServiceLayer.ODS ? parameters.getOcs().getLabel() : smProvParams.getServiceConnectivityName();
    }
    return parameters.getOcs().getLabel();
  }

  private PathDefinition getProvisionPath(OCSProcessingParameters parameters) throws SMProvException {
    PathDefinition pathDefinition;
    OpticalChannelService ocs = parameters.getOcs();
    if (!(ocs.getRoute().isEmpty() && ocs.getProtRoute().isEmpty())) {
      pathDefinition = ocsPathHandler.handleProvisionPathForOCSCreation(parameters);
    } else {
      pathDefinition = null;
    }
    return pathDefinition;
  }

  private ServiceOuterClass.ProvisionedPathsSet getWorkingPath(PathDefinition pathDefinition, PathOuterClass.ProvisionedPath excludedPath) {
    List<PathOuterClass.PathElement> pathElements;
    if (pathDefinition != null) {
      pathElements = new PathElementsFactory(pathDefinition.getWorkingPELList()).getPathElementsFromPelList();
    } else {
      pathElements = new ArrayList<>();
    }
    return createPaths(pathElements, excludedPath);
  }

  private PathOuterClass.ProvisionedPath getExcludedPath(SMProvParams smProvParams, OCSProcessingParameters ocsParameters) throws SMProvException {
    List<PathOuterClass.PathElement> pathElements;
    if (smProvParams.hasXROs()) {
      PathDefinition pathDefinition = ocsPathHandler.createExcludedPathForTunnel(ocsParameters, smProvParams.getExcludedLines(), smProvParams.getExcludedNEs());
      pathElements = new PathElementsFactory(pathDefinition.getWorkingPELList()).getPathElementsFromPelList();
    } else {
      pathElements = new ArrayList<>();
    }

    return PathOuterClass.ProvisionedPath.newBuilder()
            .addAllObjects(pathElements)
            .setScope(PathOuterClass.ProvisionedPath.Scope.PATH_SCOPE_SETUP)
            .build();
  }

  private ServiceOuterClass.Protection getProtection(ProtMechanism protMechanism, PathDefinition pathDefinition, PathOuterClass.ProvisionedPath excludedPath) {
    ServiceOuterClass.Protection.ProtectionType protectionType = getProtection(protMechanism);

    List<PathOuterClass.PathElement> pathElements;
    if (pathDefinition != null) {
      pathElements = new PathElementsFactory(pathDefinition.getProtectionPELList()).getPathElementsFromPelList();
    } else {
      pathElements = new ArrayList<>();
    }

    ServiceOuterClass.ProvisionedPathsSet provisionedPaths = createPaths(pathElements, excludedPath);
    return getProtectionBuilder(protectionType).setProvisionedPaths(provisionedPaths).build();
  }


  private List<ServiceOuterClass.Diversity> getDiversities(SMProvParams smProvParams) {
    List<ServiceOuterClass.Diversity> diversities = new ArrayList<>();
    List<Pair<DiversityType, String>> diversityPairs;
    // TODO travis Still need to handle older top-down CCCP?
    if (smProvParams.getOtherOCSNiServiceId() != null) {
      ServiceOuterClass.ServiceId serviceId = ServiceOuterClass.ServiceId.newBuilder().setId(smProvParams.getOtherOCSNiServiceId()).build();
      ServiceOuterClass.Diversity diversity = ServiceOuterClass.Diversity.newBuilder()
              .setType(ServiceOuterClass.Diversity.Type.DIVERSITY_TYPE_SRLG_DISJOINTED)
              .setServiceId(serviceId)
              .build();
      diversities.add(diversity);
    } else if ((diversityPairs = smProvParams.getDiversityList()) != null && !diversityPairs.isEmpty()) { // ... and all other cases here.
      diversityPairs.forEach(dpair -> { // <DiversityType, Service Id String>
        ServiceOuterClass.ServiceId serviceId = ServiceOuterClass.ServiceId.newBuilder().setId(dpair.getRight()).build();
        ServiceOuterClass.Diversity diversity = ServiceOuterClass.Diversity.newBuilder()
                .setType(ServiceEnumMaps.mapDiversityType(dpair.getLeft()))
                .setServiceId(serviceId)
                .build();
        diversities.add(diversity);
      });
    }
    return diversities;
  }

  private ServiceOuterClass.Restoration getRestoration(SMProvParams smProvParams, OCSProcessingParameters parameters,
                                                       PathOuterClass.ProvisionedPath excludedPath)
          throws SMProvException {
    ServiceOuterClass.Restoration.RestorationMode restorationMode = ServiceEnumMaps.mapRestorationType(smProvParams.getRestorationType());
    ServiceOuterClass.Restoration.RestorationType restorationType = ServiceEnumMaps.mapRestorationMode(smProvParams.getRestorationMode());
    ServiceOuterClass.Restoration.ReversionType reversionType = ServiceEnumMaps.mapReversionType(smProvParams.getReversionMode());

    List<PathOuterClass.PathElement> pathElements;
    if (restorationType == ServiceOuterClass.Restoration.RestorationType.RESTORATION_TYPE_PREPLANNED ||
        restorationType == ServiceOuterClass.Restoration.RestorationType.RESTORATION_TYPE_PREPLANNED_DYNAMIC) {
      PathDefinition pathDefinition = ocsPathHandler.handleProvisionedRestorationPathForOCSCreation(parameters);
      pathElements = pathDefinition != null ? new PathElementsFactory(pathDefinition.getWorkingPELList()).getPathElementsFromPelList()
                                            : new ArrayList<>();
    } else {
      pathElements = new ArrayList<>();
    }

    ServiceOuterClass.ProvisionedPathsSet provisionedPaths = createPaths(pathElements, excludedPath);

    return ServiceOuterClass.Restoration.newBuilder()
            .setProvisionedPaths(provisionedPaths)
            .setRestorationMode(restorationMode)
            .setRestorationType(restorationType)
            .setReversionType(reversionType)
            .build();
  }

  @Override
  public FlexgridParams getFlexGridParams(SMProvParams smProvParams) {
    double cf = convertFrequencyToGHz(smProvParams.getCenterFrequency());
    double cf2 = convertFrequencyToGHz(smProvParams.getSecondCenterFrequency());

    int slotWith = smProvParams.getSlotWidth();
    FlexgridParams.SlotWidth sw = ServiceEnumMaps.mapSlotWidth(slotWith);

    FlexgridParams.Builder fgBuilder= FlexgridParams.newBuilder()
            .setFrequencySlotRetention(FlexgridParams.FrequencySlotRetention.RETENTION_CENTER_FREQ_AND_SLOT_WIDTH)
            .setSlotWidth(sw);
    if (cf != 0){
      fgBuilder.setCenterFrequency(FlexgridParams.Frequency.newBuilder().setValue(cf).build());
    }
    if (cf2 != 0){
      fgBuilder.setSecondLaneCenterFrequency(FlexgridParams.Frequency.newBuilder().setValue(cf2).build());
    }

    return fgBuilder.build();
  }

  public ServiceOuterClass.ServiceConfiguration getServiceConfiguration(OpticalChannelService ocs) {
    ServiceParameters serviceParameters = mapOCSToServiceParameters(ocs);
    return ServiceOuterClass.ServiceConfiguration.newBuilder()
            .setName(serviceParameters.getName())
            .setSource(serviceParameters.getSource())
            .setDestination(serviceParameters.getDestination())
            .setSignal(serviceParameters.getSignalDescription())
            .setRestoration(serviceParameters.getRestoration())
            .setProtection(serviceParameters.getProtection())
            .addAllDiversities(serviceParameters.getDiversities())
            .build();
  }

  private ServiceParameters mapOCSToServiceParameters(OpticalChannelService ocs) {
    NITunnelDBImpl niTunnel = ocs.getNITunnel();
    String niTunnelName = StringUtils.isNoneEmpty(niTunnel.getTunnelName()) ? niTunnel.getTunnelName() : ocs.getLabel();
    String tunnelName = ocs.getLabel().startsWith(ServiceLayer.OCS.name()) ? niTunnelName : ocs.getLabel();
    return new ServiceParameters.Builder()
            .name(tunnelName)
            .source(getSourceEdge(niTunnel))
            .destination(getDestinationEdge(niTunnel))
            .signalDescription(getWDMSignalDescription(niTunnel))
            .restoration(getRestoration(ocs))
            .protection(getProtection(ocs))
            .diversities(getDiversityList(ocs))
            .build();
  }

  private List<ServiceOuterClass.Diversity> getDiversityList(OpticalChannelService ocs){
    List<ServiceOuterClass.Diversity> diversityList = new ArrayList<>();
    ServiceDiversityHelper diversityHelper = new ServiceDiversityHelper();
    int mlTrailId = MLServiceHelper.getInstance().getOldNewId(ocs.getId(), false);
    MLTrailDBImpl mlTrailDB = MLTopologyElementDAO.getInstance().getTrailByID(mlTrailId);
    if(mlTrailDB == null){
      log.error("Can't find ML Trail for ocs: {}", ocs.getLabel());
      return diversityList;
    }
    TrailDiversityData mainTrailData = new TrailDiversityData(mlTrailDB);
    List<MLTrailDBImpl> diverseTrailsWithTunnelInService = diversityHelper.getDiverseTrails(mainTrailData).stream()
            //FNMD-112707: do not attempt to specify a diverse peer which has a suspended tunnel. MNC supports this but
            //CPc does not and it will cause the resume/adopt operation to fail.
            .filter(this::hasTunnelInService)
            .toList();

    for(MLTrailDBImpl diversePeer : diverseTrailsWithTunnelInService){
      String niServiceId = getNiServiceId(diversePeer);
      if(niServiceId == null){
        log.error("Can't lookup CPc service id for: {}", diversePeer.getLabel());
        continue;
      }

      DiversityType diversityType = diversityHelper.getDiversityType(mainTrailData, new TrailDiversityData(diversePeer));
      ServiceOuterClass.ServiceId serviceId = ServiceOuterClass.ServiceId.newBuilder().setId(niServiceId).build();
      ServiceOuterClass.Diversity diversity = ServiceOuterClass.Diversity.newBuilder()
              .setType(ServiceEnumMaps.mapDiversityType(diversityType))
              .setServiceId(serviceId)
              .build();
      diversityList.add(diversity);
    }
    return diversityList;
  }

  private boolean hasTunnelInService(MLTrailDBImpl mlTrailDB){
    boolean hasTunnelInService = false;
    ServiceIntent serviceIntent = mlTrailDB.getServiceIntent();
    try{
      hasTunnelInService = serviceIntent != null
              && new ServiceTunnelHelper().hasTunnelInService(serviceIntent.getId());
    } catch(MDOperationFailedException e){
      log.error(e);
    }
    return hasTunnelInService;
  }

  private String getNiServiceId(MLTrailDBImpl mlTrailDB){
    MLTopologyMOReference ocsRef = mlTrailDB.getRelatedMOObject(MLMoReferenceType.ServiceOCSMOReference).orElse(null);
    int ocsId = ocsRef instanceof MLTopologyServiceMoReference serviceRef ? serviceRef.getConnID() : -1;
    AbstractConnectionDBImpl ocs = ConnectionDAO.getInstance().getConnectionById(ocsId);
    return ocs == null ? null : ocs.getNiServiceId();
  }

  private SignalDescription getWDMSignalDescription(NITunnelDBImpl niTunnel) {
    FacilityType facilityType = FacilityType.forNumber(niTunnel.getFacilityType());
    SignalWdm signalWdm = getSignalWdm(niTunnel);
    return SignalDescription.newBuilder().setWdm(signalWdm).setFacilityType(facilityType).build();
  }

  private ServiceOuterClass.Restoration getRestoration(OpticalChannelService ocs) {
    ServiceOuterClass.Restoration.RestorationMode restorationMode =
            ServiceEnumMaps.mapRestorationType(Definition.RestorationType.valueOf(ocs.getRestoreType()));
    ServiceOuterClass.Restoration.RestorationType restorationType =
            ServiceEnumMaps.mapRestorationMode(Definition.RestorationMode.valueOf(ocs.getRestorationMode()));
    ServiceOuterClass.Restoration.ReversionType reversionType = ServiceEnumMaps.mapReversionType(Definition.ReversionMode.valueOf(ocs.getReversionMode()));

    return ServiceOuterClass.Restoration.newBuilder()
            .setRestorationMode(restorationMode)
            .setRestorationType(restorationType)
            .setReversionType(reversionType)
            .build();
  }

  private ServiceOuterClass.Protection getProtection(OpticalChannelService ocs) {
    ServiceOuterClass.Protection.ProtectionType protectionType = getProtection(ocs.getProtMechanism());
    return getProtectionBuilder(protectionType).build();
  }

}
