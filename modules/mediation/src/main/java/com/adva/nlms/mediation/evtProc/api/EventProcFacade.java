/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: adaemmig
 */

package com.adva.nlms.mediation.evtProc.api;

import java.util.List;
import java.util.Map;

import com.adva.fm.api.dto.AppNetworkMgmtEvent;
import org.snmp4j.PDU;
import org.snmp4j.PDUv1;
import org.snmp4j.smi.IpAddress;
import org.snmp4j.smi.VariableBinding;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.event.EventDetectionType;
import com.adva.nlms.common.event.EventSeverity;
import com.adva.nlms.common.event.EventType;
import com.adva.nlms.mediation.common.event.WebSocketNotification;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.event.EventDTO;
import com.adva.nlms.mediation.event.EventProcessingActionHdlr;
import com.adva.nlms.mediation.evtProc.SNMPTrap;

public interface EventProcFacade extends IInvEventProcFacade {

  //------------------------------------------------------------------------------
  //--- Add events for Pre-processing --------------------------------------------
  //------------------------------------------------------------------------------

  /**
   * SNMP V1 Live-Trap from the NE.
   * @param pdu                Trap info
   * @param peerIpAddress      Source IP address
   * @param securityName       The principal
   *
   */
  void addSnmpV1Trap(PDUv1 pdu, IpAddress peerIpAddress, byte[] securityName);

  /**
   * SNMP V2 Live-Trap from the NE.
   * @param pdu                Trap info
   * @param peerIpAddress      Source IP address
   * @param securityName       The principal
   *
   */
  void addSnmpV2Trap(PDU pdu, IpAddress peerIpAddress, byte[] securityName);

  /**
   * Re-generated Traps from the NE event log resulting from explicit (MEM) or periodic (KAP) requests.
   * @param snmpTrap           Trap info
   * @param vbl                Varbind list
   * @param detectionType      Detection type (is LOG or TRP for specific cases)
   */
  void addEventLogTrap(SNMPTrap snmpTrap, List<VariableBinding> vbl, EventDetectionType detectionType);

  /**
   * Alarms and Events resulting from Inventory-, Config- or Status-Polling
   * @param event Event
   */
  void addPollingEvent(EventDTO event);

  /**
   * Alarm or Event based on AppCommonEvent
   * @param event  Event to be stored into the Event-table
   */
  void addEvent(AppNetworkMgmtEvent event);

  /**
   * Alarm or Event based on device snapshot provided by 3rd party device API
   * @param eventDto  Event to be stored into the Event-table
   */
  void add3PartyDeviceEvent(EventDTO eventDto);

  //------------------------------------------------------------------------------
  //--- Add events for Pre-processing - specific methods -------------------------

  void addAsyncEventDTOServerTrap(final EventDTO serverTrap);

  void addSNMPTrap(SNMPTrap snmpTrap);

  void addSNMPTrap(EventDTO eventDTO);

  //------------------------------------------------------------------------------
  //--- Add events w/o Pre-processing --------------------------------------------
  //------------------------------------------------------------------------------

  /**
   * Adds a Server Action Event to the event-processing which triggers the action within the DB Queue
   * @param actionType   Action Type
   * @param neId         NE-ID
   * @param params       Additional parameters (object-index, ...)
   */
  void addServerActionEvent(EventProcessingActionHdlr.Action actionType, int neId, EventDTO.Param... params);

  //------------------------------------------------------------------------------
  //--- Add events for DB storage ------------------------------------------------
  //------------------------------------------------------------------------------

  void addServerActionEvent4DB(EventProcessingActionHdlr.Action actionType, int neId, EventDTO.Param... params);

  void addDBEvent(EventDTO eventDTO);

  void addServerAlarm(int trapID,
                      EventType type,
                      Object ne,
                      EventDTO.Param... params);

  void addServerAlarm(int trapID,
                      EventType type,
                      Object ne,
                      EntityIndex objectIndex,
                      String shortDescription,
                      String fullDescription,
                      EventDTO.Param... params);

  void addStatusEvent(int trapID,
                      EventType eventType,
                      EventSeverity eventSeverityType,
                      String guiDescriptionString,
                      String guiCause,
                      NetworkElement ne,
                      String shortDescription,
                      String fullDescription,
                      EntityIndex objectIndex,
                      EventDTO.Param... params);

  void addServerEvent(int trapID,
                      EventType type,
                      Object ne,
                      EventDTO.Param... params);

  void addServerEvent(int trapId,
                      EventType eventType,
                      EventSeverity eventSeverity,
                      NetworkElement ne,
                      EntityIndex entityIndex,
                      String entityAidString,
                      String entityAlias,
                      String eventShortDescription,
                      String eventFullDescription);

  void addServerEvent(int trapID,
                      EventType type,
                      Object ne,
                      EntityIndex objectIndex,
                      String shortDescription,
                      String fullDescription,
                      EventDTO.Param... params);

  void addServerEvent(int trapID,
                      EventType eventType,
                      EventSeverity eventSeverityType,
                      String guiDescriptionString,
                      String guiCause,
                      NetworkElement ne,
                      EventDTO.Param... params);

  void addGlobalServerAlarm(int trapID,
                            EventType type,
                            EventDTO.Param... params);

  void addGlobalEvent(EventType type, EventSeverity severity, String parameter, String shortName);

  void addGlobalEvent(EventType type, EventSeverity severity, String message, String shortName, Throwable exc);

  void addLineAlarm(int trapID,
                    EventType type,
                    int lineId,
                    EventDTO.Param... params);

  /**
   * Adds a server alarm related to a connection. Alarms can either be raising or clearing.
   * @param trapId The alarm ID (from FSP_NMTraps).
   * @param eventType The event type (RAISE or CLEAR).
   * @param connIDs The connection IDs is set in the alarm.
   * @param sourceNeID The source NE ID (causing the alarm)
   * @param subnetID The source Subnet ID
   * @param objectIndex The entity index
   * @param parentIDs The service parents (service group/customer)
   * @param entityDescr The AID of the entity
   */
  void addConnectionAlarm(int trapId, EventType eventType, int[] connIDs, int sourceNeID, int subnetID, EntityIndex objectIndex, int[] parentIDs, String entityDescr);

  /**
   * Adds an Event to the table. This method is used for events,
   * generated by the server, which are related to a delete-service and delete-customer.
   * @param type      Type of the event.
   * @param securityEvent  security event
   * @param severity  Severity of the event.
   * @param parameter Message of the event.
   * @param shortName Short name of the specific event.
   * @param sourceName  service or customer name
   * @param connectionName Name of the related connection affected by the event
   * @param parentID  ID of the parent
   * @param neId      ID of the corresponding entity's network element id
   */
  void addConnectionEvent(EventType type, boolean securityEvent, EventSeverity severity, String parameter, String shortName, String sourceName, String connectionName, int parentID, int neId);

  /**
   * Adds an Event to the table. This method is used for events,
   * generated by the server, which are related to a connections.
   * @param type      Type of the event.
   * @param securityEvent  security event
   * @param severity  Severity of the event.
   * @param parameter Message of the event.
   * @param shortName Short name of the specific event.
   * @param sourceName  service or customer name
   * @param connID    ID of the connection
   * @param parentID  ID of the parent
   * @param neId      ID of the corresponding entity's network element id
   **/
  void addConnectionEvent(EventType type, boolean securityEvent, EventSeverity severity, String parameter, String shortName, String sourceName, int connID, int parentID,int neId);

  void addConnectivityServiceSecurityEvent(EventSeverity eventSeverity, String eventShortName, List<Integer> eventParentIds,
                                           List<Integer> trailIds, String connectivityServiceName, String customerName,
                                           String eventDescription, String connectionName, int subnetId);

  void addConnectivityServiceAlarm(int trapId, EventType type, int connectivityServiceId, int parentId, String connectivityServiceName, int subnetId);

  void addLineEvent(EventType type, boolean securityEvent, EventSeverity severity, String aParameter, String zParameter, String shortName, int aEndNeID, int zEndNeID, int lineID);

  void addSecurityEvent(EventSeverity severity,
                        String parameter,
                        String shortName,
                        String networkNodeID,
                        int nodeID,
                        EventDTO.Param... params);


  Map<Integer, List<Integer[]>> getBulkTrapsMap();

  void removeBulkTrapEntry(int sourceNE_ID, int bulkTrapNeStartLogIndex, int bulkTrapNeEndLogIndex);

  void addWebSocketTrap(WebSocketNotification pdu, String ipAddress, EventDetectionType detectionType);

  void addPTPRemoteSlaveConnectionEvent(EventSeverity severity, String msg, String shortName, String rsConnectionName, int neId);

  void addGnssFirewallActionInvokedEvent(EventSeverity severity, String entityDescription, String shortName, String description, int neId);

}
