/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: adaemmig
 */
package com.adva.nlms.mediation.event;

import com.adva.common.util.filter.DateOperator;
import com.adva.common.util.filter.EnumOperator;
import com.adva.common.util.filter.FilterOperator;
import com.adva.ethernet.ring.api.in.EthernetRingDataHelper;
import com.adva.nlms.common.NEUtils;
import com.adva.nlms.common.TopologyNodeType;
import com.adva.nlms.common.config.netypes.NeTypeString;
import com.adva.nlms.common.event.EventProperties;
import com.adva.nlms.common.event.EventSecurityType;
import com.adva.nlms.common.networktree.TopologyDTO;
import com.adva.nlms.common.paging.PageArea;
import com.adva.nlms.common.paging.PagingProperties;
import com.adva.nlms.common.paging.PagingRestriction;
import com.adva.nlms.common.synchronization.model.enums.SyncProtocolType;
import com.adva.nlms.common.synchronization.util.SyncConsts;
import com.adva.nlms.common.traps.FSP_NMTraps;
import com.adva.nlms.common.util.ArrayHelper;
import com.adva.nlms.mediation.common.MDRequestFailedException;
import com.adva.nlms.mediation.common.paging.DbLanguage;
import com.adva.nlms.mediation.common.paging.DbSubclauseSelector;
import com.adva.nlms.mediation.common.paging.DefaultPageHdlrImpl;
import com.adva.nlms.mediation.common.paging.FilterCondition;
import com.adva.nlms.mediation.common.paging.FilterCondition.DbOperator;
import com.adva.nlms.mediation.common.paging.PageColumn;
import com.adva.nlms.mediation.common.paging.PagingCondition;
import com.adva.nlms.mediation.common.paging.SortingCondition;
import com.adva.nlms.mediation.common.paging.SortingConditionConstant;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.common.persistence.MDPersistenceHelper;
import com.adva.nlms.mediation.config.ConfigCtrl;
import com.adva.nlms.mediation.config.NetworkElementDAO;
import com.adva.nlms.mediation.config.NetworkElementDBImpl;
import com.adva.nlms.mediation.config.NoSuchConnectionException;
import com.adva.nlms.mediation.event.correlation.CorrelationHdlrImpl;
import com.adva.nlms.mediation.gistransfer.api.GisTransferCtrl;
import com.adva.nlms.mediation.mltopologymodel.model.dao.MLTopologyElementDAO;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLConnectionDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLLayerAdaptationDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLMonitoringSectionDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLNodeDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLPathMonitoringSectionDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLTopologyElementDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLTrailDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLConnectionPoint;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLPath;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLTopologyMOReference;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLConnectionType;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLTopologyNeMoReference;
import com.adva.nlms.mediation.mltopologymodel.service.intent.implementation.db.ServiceIntentDBImpl;
import com.adva.nlms.mediation.security.session.SessionHdlr;
import com.adva.nlms.mediation.sm.ServiceManagerTopologyHdlr;
import com.adva.nlms.mediation.sm.dao.ConnectionDAO;
import com.adva.nlms.mediation.sm.helper.NodeVisibilityHelper;
import com.adva.nlms.mediation.sm.model.AbstractConnectionDBImpl;
import com.adva.nlms.mediation.sm.model.CustomerDAO;
import com.adva.nlms.mediation.sm.model.SubChConnectionDBImpl;
import com.adva.nlms.mediation.sm.model.eth.EthernetConnectionDBImpl;
import com.adva.nlms.mediation.sm.model.eth.PWE3EthernetTrailDBImpl;
import com.adva.nlms.mediation.sm.model.eth.SatopEthernetConnectionDBImpl;
import com.adva.nlms.mediation.sm.prov.TransportService;
import com.adva.nlms.mediation.synchronization.UserRestrictionProvider;
import com.adva.nlms.mediation.topology.AbstractTopologyProvider;
import com.adva.nlms.mediation.topology.SubnetDAO;
import com.adva.nlms.mediation.topology.SubnetDBImpl;
import com.adva.nlms.mediation.topology.TopLevelSubnetHdlrApi;
import com.adva.nlms.pd.api.in.service.PDServiceDataController;
import com.adva.packet_layer3.ipvpnsm.api.dto.PDL3IPVPNServiceImplDto;
import com.adva.packet_layer3.ipvpnsm.api.in.PDL3IPVPNServiceApi;
import com.google.common.primitives.Ints;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * The default implementation of event page handling
 */
@Component
public class EventPageHdlrImpl extends DefaultPageHdlrImpl<EventProperties> {

  private final ConfigCtrl configCtrl;
  private final EventDAO eventDAO;
  private final EventDBQueryHdlr eventDBQueryHdlr;
  private final SessionHdlr sessionHdlr;
  private final CorrelationHdlrImpl correlationHdlr;
  private final UserRestrictionProvider userRestrictionProvider;
  private final TopLevelSubnetHdlrApi topLevelSubnetHdlr;
  private final MOAdapter moAdapter;
  private final MLTopologyElementDAO mlTopologyElementDAO;
  private final GisTransferCtrl gisTransferCtrl;
  private final CustomerDAO customerDAO;

  private final ServiceManagerTopologyHdlr smTopologyHdlr;

  private final PDServiceDataController pdServiceDataController;

  private final PDL3IPVPNServiceApi pdl3IPVPNServiceApi;

  final
  EthernetRingDataHelper ethernetRingDataHelper;

  private static final String EVENT_ASSOC_OBJECT_ID = "ev_event_assoc.objectid";

  private static final String EVENT_ASSOC_JOIN_CONDITION = " JOIN ev_event_assoc ON (ev_event.id = ev_event_assoc.id)";

  /**
   * Logging
   */
  private static Logger log = LogManager.getLogger(EventPageHdlrImpl.class.getPackage().getName());

  public enum EventPageColumn implements PageColumn {   // TODO replace dbSqlColumn-string by EventDBColumn.dbSqlColumn
    // tree-selection: filter
    SUBNET_ID         (null,                                     "subnetid",           null),
    NE_ID             (null,                                     "sourceneid",         null),
    SERVICE_ID        (null,                                     "ev_event_conns.val",         " JOIN ev_event_conns ON (ev_event.id = ev_event_conns.id)"),
    SERVICE_GRP_ID    (null,                                     EVENT_ASSOC_OBJECT_ID,  EVENT_ASSOC_JOIN_CONDITION),
    SYNC_NODE_ID      (null,                                     "ev_event_sync_node.sync_node_id", " JOIN ev_event_sync_node ON (ev_event.id = ev_event_sync_node.event_id)"),
    SYNC_NCD_ID       (null,                                     "ev_event_sync_ncd.ncd_id",        " JOIN ev_event_sync_ncd ON (ev_event.id = ev_event_sync_ncd.event_id)"),
    SYNC_NODE_TYPE    (null,                                     "sync_node.type",        " JOIN ev_event_sync_node ON (ev_event.id = ev_event_sync_node.event_id) " +
                                                                                          " JOIN sync_node on sync_node.id = ev_event_sync_node.sync_node_id"),
    SYNC_ROUTE_ID     (null,                                     "syncrouteid",        null),
    MLT_ID            (null,                                     EVENT_ASSOC_OBJECT_ID,    EVENT_ASSOC_JOIN_CONDITION),
    MLT_ID_GROUP      (null,                                     EVENT_ASSOC_OBJECT_ID,  EVENT_ASSOC_JOIN_CONDITION),
    SERVICE_ID_BOTH   (null,                                     null,    " LEFT OUTER JOIN ev_event_conns ON (ev_event.id = ev_event_conns.id)", " LEFT OUTER JOIN ev_event_assoc ON (ev_event.id = ev_event_assoc.id)",
       "(ev_event_conns.val %v OR ev_event_assoc.objectid %v)"),
    EOD_CONNECTIVITY_SERVICE    (null,                                     null,    EVENT_ASSOC_JOIN_CONDITION, null, "(ev_event_assoc.objectType = 501 AND ev_event_assoc.id IS NOT NULL)"),
    // other selection: filter
    LINE_ID           (null,                                     "lineid",             null),
    OBJECT_INDEX      (PagingProperties.Event.OBJECTINDEX,       "objectindex",        null),
    MODULE_INDEX      (PagingProperties.Event.MODULEINDEX,       "moduleindex",        null),
    FIRST_RAISED_ALARM_ID (PagingProperties.Event.FIRST_RAISED_ALARM_ID, "first_raised_alarm", null),
    CLEARING_ALARM_ID     (PagingProperties.Event.CLEARING_ALARM_ID,     "clearingalarmid",    null),
    // internal: filter
    IS_EVENT          (null,                                     "isevent",            null),
    // column: filter/sorting
    ID                (PagingProperties.Event.ID,                "ev_event.id",        null),
    ACKUSER           (PagingProperties.Event.ACKUSER,           "acknUser",           null),
    ACKTIME           (PagingProperties.Event.ACKTIME,           "acknTimeStamp",      null),
    DETECTION_TYPE    (PagingProperties.Event.DETECTIONTYPE,     "detectiontype",      null),
    TYPE              (PagingProperties.Event.EVENTTYPE,         "typ",                null),
    ALARMCLASS        (PagingProperties.Event.ALARMCLASS,        "alarmclass",         null),
    NMS_TIMESTAMP     (PagingProperties.Event.NMSTIMEFORMATTED,  "nmstimestamp",       null),
    TIMESTAMP         (PagingProperties.Event.TIMEFORMATTED,     "netimestamp",        null),
    SHORT_NAME        (PagingProperties.Event.CAUSE,             "shortname",          null),
    SECURITY_EVENT    (PagingProperties.Event.EVENTSECURITY,     "securityevent",      null),
    SEVERITY          (PagingProperties.Event.SEVERITY,          "severity",           null),
    IMPAIRMENT        (PagingProperties.Event.IMPAIRMENT,        "impairment",         null),
    DIRECTION         (PagingProperties.Event.DIRECTION,         "direction",          null),
    LOCATION          (PagingProperties.Event.LOCATION,          "location",           null),
    PATH              (PagingProperties.Event.PATH,              "path",               null),
    SOURCE_NAME       (PagingProperties.Event.SOURCENAME,        "sourcename",         null),
    ENTITY_DESCR      (PagingProperties.Event.ENTITYDESCRIPTION, "entitydescription",  null),
    MODULE_TYPE_NAME  (PagingProperties.Event.MODULETYPENAME,    "moduletypename",     null),
    PHY_LOCATION      (PagingProperties.Event.PHYLOCATION,       "phylocation",        null),
    ENTITY_ALIAS      (PagingProperties.Event.ENTITYALIAS,       "entityalias",        null),
    ACK               (PagingProperties.Event.ACK,               "acknowledge",        null),
    CORRELATION       (PagingProperties.Event.CORRELATION,       "correlation",        null),
    DESCRIPTION       (PagingProperties.Event.DESCRIPTION,       "parameter",          null),
    COMMENT           (PagingProperties.Event.COMMENT,           "comment",            null),
    SERVICE_NAME      (PagingProperties.Event.SERVICENAME,       "servicename",        null),
    IS_SERVICE_MANAGED(PagingProperties.Event.MANAGED_STATE,     "cn_connection.is_managed",  SERVICE_ID.dbSqlJoinCondition, " JOIN cn_connection ON cn_connection.id = ev_event_conns.val", null),
    SOURCE_NE_TYPE(null,"sourcenetype" ,null ),
    SOURCE_NE         (PagingProperties.Event.SOURCENE,"sourcene", null),
    NMS_CLEARED_TIMESTAMP (PagingProperties.Event.NMSTIMEFORMATTED,"nmsclearedtimestamp", null),
    ;
    PagingProperties.Event pagingColumn;
    String dbSqlColumn;
    String dbSqlJoinCondition;
    String dbSqlJoinCondition2;
    String dbSqlSubQuery;

    EventPageColumn(PagingProperties.Event pagingColumn, String dbSqlColumn, String dbSqlJoinCondition) {
      this.pagingColumn = pagingColumn;
      this.dbSqlColumn = dbSqlColumn;
      this.dbSqlJoinCondition = dbSqlJoinCondition;
    }

    EventPageColumn(PagingProperties.Event pagingColumn, String dbSqlColumn, String dbSqlJoinCondition, String dbSqlJoinCondition2, String dbSqlSubQuery) {
      this.pagingColumn = pagingColumn;
      this.dbSqlColumn = dbSqlColumn;
      this.dbSqlJoinCondition = dbSqlJoinCondition;
      this.dbSqlJoinCondition2 = dbSqlJoinCondition2;
      this.dbSqlSubQuery = dbSqlSubQuery;
    }

    static Map<String, PageColumn> getBeanDbMap() {
      Map<String, PageColumn> beanDbMap = new HashMap<>();
      for (EventPageColumn eventDbMapper : EventPageColumn.values()) {
        if (eventDbMapper.pagingColumn != null)
          beanDbMap.put(eventDbMapper.pagingColumn.getBeanName(), eventDbMapper);
      }
      return beanDbMap;
    }

    @Override
    public String getDbSubclause(DbSubclauseSelector selector, DbLanguage dbLang) {
      switch (selector) {
        case COLUMN_NAME:
          return dbSqlColumn;
        case JOIN_CONDITION:
          return dbSqlJoinCondition;
        case JOIN_CONDITION_2:
          return dbSqlJoinCondition2;
        case SUB_QUERY:
          return dbSqlSubQuery;
        default:
          return null;
      }
    }

    public static EventPageColumn getEventPageColumnByName(String name) {
      for (EventPageColumn column : EventPageColumn.values()) {
        if (column.name().equals(name)) {
          return column;
        }
      }
      return null;
    }

  }

  private static final String EventAssocJoinCondition = " JOIN ev_event_hist_assoc ON (ev_event_history.id = ev_event_hist_assoc.id)";

  public enum EventHistPageColumn implements PageColumn {   // TODO replace dbSqlColumn-string by EventDBColumn.dbSqlColumn
    // tree-selection: filter
    SUBNET_ID         (null,                                     "subnetid",           null),
    NE_ID             (null,                                     "sourceneid",         null),
    SERVICE_ID        (null,                                     "ev_event_hist_assoc.objectid",    EventAssocJoinCondition),
    SERVICE_GRP_ID    (null,                                     "ev_event_hist_assoc.objectid",    EventAssocJoinCondition),
    SYNC_NODE_ID      (null,                                     "ev_event_hist_assoc.objectid",    EventAssocJoinCondition),
    SYNC_NCD_ID       (null,                                     "ev_event_hist_assoc.objectid",    EventAssocJoinCondition),
    SYNC_NODE_TYPE    (null,                                     "sync_node.type",                  EventAssocJoinCondition, " JOIN sync_node on sync_node.id = ev_event_hist_assoc.objectid", null),
    SYNC_ROUTE_ID     (null,                                     "ev_event_hist_assoc.objectid",    EventAssocJoinCondition),
    MLT_ID            (null,                                     "ev_event_hist_assoc.objectid",    EventAssocJoinCondition),
    SERVICE_GRP_ID_BOTH(null,                                     null,    EventAssocJoinCondition, null,
      "(ev_event_hist_assoc.objectid %v OR ev_event_hist_assoc.objectid IN (SELECT id FROM sm_customer_service_group WHERE jdoclass='ServiceIntentDBImpl' AND parentid %v))"),
    SERVICE_ID_BOTH    (null,                                     "ev_event_hist_assoc.objectid",    EventAssocJoinCondition),
    // other selection: filter
    LINE_ID           (null,                                     "lineid",             null),
    OBJECT_INDEX      (PagingProperties.Event.OBJECTINDEX,       "objectindex",        null),
    MODULE_INDEX      (PagingProperties.Event.MODULEINDEX,       "moduleindex",        null),
    FIRST_RAISED_ALARM_ID (PagingProperties.Event.FIRST_RAISED_ALARM_ID, "first_raised_alarm", null),
    CLEARING_ALARM_ID     (PagingProperties.Event.CLEARING_ALARM_ID,     "clearingalarmid",    null),
    // column: filter/sorting
    ID                (PagingProperties.Event.ID,                "ev_event_history.id",        null),
    ACKUSER           (PagingProperties.Event.ACKUSER,           "acknUser",           null),
    ACKTIME           (PagingProperties.Event.ACKTIME,           "acknTimeStamp",      null),
    DETECTION_TYPE    (PagingProperties.Event.DETECTIONTYPE,     "detectiontype",      null),
    TYPE              (PagingProperties.Event.EVENTTYPE,         "typ",                null),
    ALARMCLASS        (PagingProperties.Event.ALARMCLASS,        "alarmclass",         null),
    NMS_TIMESTAMP     (PagingProperties.Event.NMSTIMEFORMATTED,  "nmstimestamp",       null),
    TIMESTAMP         (PagingProperties.Event.TIMEFORMATTED,     "netimestamp",        null),
    SHORT_NAME        (PagingProperties.Event.CAUSE,             "shortname",          null),
    SECURITY_EVENT    (PagingProperties.Event.EVENTSECURITY,     "securityevent",      null),
    SEVERITY          (PagingProperties.Event.SEVERITY,          "severity",           null),
    IMPAIRMENT        (PagingProperties.Event.IMPAIRMENT,        "impairment",         null),
    DIRECTION         (PagingProperties.Event.DIRECTION,         "direction",          null),
    LOCATION          (PagingProperties.Event.LOCATION,          "location",           null),
    PATH              (PagingProperties.Event.PATH,              "path",               null),
    SOURCE_NAME       (PagingProperties.Event.SOURCENAME,        "sourcename",         null),
    ENTITY_DESCR      (PagingProperties.Event.ENTITYDESCRIPTION, "entitydescription",  null),
    MODULE_TYPE_NAME  (PagingProperties.Event.MODULETYPENAME,    "moduletypename",     null),
    PHY_LOCATION      (PagingProperties.Event.PHYLOCATION,       "phylocation",        null),
    ENTITY_ALIAS      (PagingProperties.Event.ENTITYALIAS,       "entityalias",        null),
    ACK               (PagingProperties.Event.ACK,               "acknowledge",        null),
    CORRELATION       (PagingProperties.Event.CORRELATION,       "correlation",        null),
    DESCRIPTION       (PagingProperties.Event.DESCRIPTION,       "parameter",          null),
    COMMENT           (PagingProperties.Event.COMMENT,           "comment",            null),
    SERVICE_NAME      (PagingProperties.Event.SERVICENAME,       "servicename",        null),
    IS_SERVICE_MANAGED(PagingProperties.Event.MANAGED_STATE,     "cn_connection.is_managed",  EventAssocJoinCondition, " JOIN cn_connection ON cn_connection.id = ev_event_assoc.objectid", null),
    NMS_CLEARED_TIMESTAMP (PagingProperties.Event.NMSTIMEFORMATTED,"nmsclearedtimestamp", null),
    ;
    PagingProperties.Event pagingColumn;
    String dbSqlColumn;
    String dbSqlJoinCondition;
    String dbSqlJoinCondition2;
    String dbSqlSubQuery;

    EventHistPageColumn(PagingProperties.Event pagingColumn, String dbSqlColumn, String dbSqlJoinCondition) {
      this.pagingColumn = pagingColumn;
      this.dbSqlColumn = dbSqlColumn;
      this.dbSqlJoinCondition = dbSqlJoinCondition;
    }

    EventHistPageColumn(PagingProperties.Event pagingColumn, String dbSqlColumn, String dbSqlJoinCondition, String dbSqlJoinCondition2, String dbSqlSubQuery) {
      this.pagingColumn = pagingColumn;
      this.dbSqlColumn = dbSqlColumn;
      this.dbSqlJoinCondition = dbSqlJoinCondition;
      this.dbSqlJoinCondition2 = dbSqlJoinCondition2;
      this.dbSqlSubQuery = dbSqlSubQuery;
    }

    static Map<String, PageColumn> getBeanDbMap() {
      Map<String, PageColumn> beanDbMap = new HashMap<>();
      for (EventHistPageColumn eventDbMapper : EventHistPageColumn.values()) {
        if (eventDbMapper.pagingColumn != null)
          beanDbMap.put(eventDbMapper.pagingColumn.getBeanName(), eventDbMapper);
      }
      return beanDbMap;
    }

    @Override
    public String getDbSubclause(DbSubclauseSelector selector, DbLanguage dbLang) {
      switch (selector) {
        case COLUMN_NAME:
          return dbSqlColumn;
        case JOIN_CONDITION:
          return dbSqlJoinCondition;
        case JOIN_CONDITION_2:
          return dbSqlJoinCondition2;
        case SUB_QUERY:
          return dbSqlSubQuery;
        default:
          return null;
      }
    }
  }

  private PageColumn getPageColumn(EventPageColumn eventColumn) {
    if (getPageArea() == PageArea.ALARM_HIST)
      return EventHistPageColumn.valueOf(eventColumn.name());

    return eventColumn;
  }

  @Autowired
  public EventPageHdlrImpl(PageArea pageArea, EventDBQueryHdlr eventDBQueryHdlr, ConfigCtrl configCtrl, EventDAO eventDAO, SessionHdlr sessionHdlr, CorrelationHdlrImpl correlationHdlr, PDServiceDataController pdServiceDataController, PDL3IPVPNServiceApi pdl3IPVPNServiceApi, EthernetRingDataHelper ethernetRingDataHelper, ServiceManagerTopologyHdlr smTopologyHdlr, UserRestrictionProvider userRestrictionProvider, TopLevelSubnetHdlrApi topLevelSubnetHdlr, MOAdapter moAdapter, MLTopologyElementDAO mlTopologyElementDAO, GisTransferCtrl gisTransferCtrl, CustomerDAO customerDAO) {
    super(pageArea, (pageArea == PageArea.ALARM_HIST) ? EventHistPageColumn.getBeanDbMap() : EventPageColumn.getBeanDbMap());
    this.eventDBQueryHdlr = eventDBQueryHdlr;
    this.configCtrl = configCtrl;
    this.eventDAO = eventDAO;
    this.sessionHdlr = sessionHdlr;
    this.correlationHdlr = correlationHdlr;
    this.pdServiceDataController = pdServiceDataController;
    this.pdl3IPVPNServiceApi = pdl3IPVPNServiceApi;
    this.ethernetRingDataHelper = ethernetRingDataHelper;
    this.smTopologyHdlr = smTopologyHdlr;
    this.userRestrictionProvider = userRestrictionProvider;
    this.topLevelSubnetHdlr = topLevelSubnetHdlr;
    this.moAdapter = moAdapter;
    this.mlTopologyElementDAO = mlTopologyElementDAO;
    this.gisTransferCtrl = gisTransferCtrl;
    this.customerDAO = customerDAO;
  }

  @Override
  public List<Integer> getPageContextIDList(PageArea area, TopologyNodeType ntnType, int nodeID) {
    if (ntnType == TopologyNodeType.SERVICE_INTENT) {
      if (isShadowCopy(nodeID))
        return Collections.emptyList();
    }

    if (ntnType == TopologyNodeType.SERVICE_INTENT
            || ntnType == TopologyNodeType.PD_SERVICE_INTENT
            || ntnType == TopologyNodeType.PD_SERVICE
            || ntnType == TopologyNodeType.PD_L3_IPVPN_SERVICE) {
      int dsID = convertServiceContainerToDS(ntnType, nodeID);
      Set<Integer> allLayerResourceID = getAllLayerServiceIDs(dsID);
      // above set could be unmodifiable, trying to add nodeID may throw exception
      List<Integer> allLayerResourceIDIncNodeId = new ArrayList<>(allLayerResourceID);
      if(!allLayerResourceID.contains(nodeID)){
        allLayerResourceIDIncNodeId.add(nodeID);
      }
      return allLayerResourceIDIncNodeId;
    }
    return Collections.emptyList();
  }

  @MDPersistenceContext
  private boolean isShadowCopy(int id) {
    ServiceIntentDBImpl serviceIntentDB = MDPersistenceHelper.find(ServiceIntentDBImpl.class, id);
    return serviceIntentDB != null && serviceIntentDB.isShadowCopy();
  }

  @Override
  //return EventProperties
  public Collection<EventProperties> getPagingData(TopologyNodeType ntnType, int[] nodeIDs, PagingRestriction restriction) {

    if (restriction.getPageArea().equals(PageArea.ALL_LAYERED_ALARMS)) {
        ntnType = ((ntnType == TopologyNodeType.PD_SERVICE_INTENT) || (ntnType == TopologyNodeType.PD_SERVICE)) ? ntnType : TopologyNodeType.SERVICE_INTENT;
      nodeIDs = Ints.toArray(getPageContextIDList(getPageArea(), ntnType, nodeIDs[0]));
    }

    // determine paging, sorting and filter conditions
    PagingCondition pagingCondition = new PagingCondition(getPageArea(), restriction.getPageCmd(), restriction.getPageSize(), restriction.getPageRefDTO(), restriction.getRowsCount(), restriction.getPageOffset());
    List<SortingCondition> sortingConditions = getSortingConditions(restriction, pagingProperties.getDefaultPrimaryColumn(), PagingProperties.Event.ID.getBeanName());
    List<FilterCondition> filterConditions = getFilterConditions(ntnType, nodeIDs, restriction, DbLanguage.DEFAULT);
    List<EventDTO> events = eventDBQueryHdlr.getEventsFor(pagingCondition, sortingConditions, filterConditions);
    // artf224327: special implementation for page-Up/Down when result is one row
    if ((pagingCondition.getPageCmd() == PagingRestriction.PageCmd.UP || pagingCondition.getPageCmd() == PagingRestriction.PageCmd.DOWN) &&
            events.size() <pagingCondition.getPageSize()) {
      PagingRestriction.PageCmd pageCmd = (pagingCondition.getPageCmd() == PagingRestriction.PageCmd.UP) ? PagingRestriction.PageCmd.TOP : PagingRestriction.PageCmd.BOTTOM;
      pagingCondition = new PagingCondition(getPageArea(), pageCmd, restriction.getPageSize(), restriction.getPageRefDTO(), restriction.getRowsCount(), restriction.getPageOffset());
      List<EventDTO> newEvents = eventDBQueryHdlr.getEventsFor(pagingCondition, sortingConditions, filterConditions);
      if (newEvents.size()>events.size())
        events = newEvents;
    }
    // convert to GUI Event
    List<EventProperties> eventList = new ArrayList<>(events.size());
    for (EventDTO event : events) {
      eventList.add(event.createEventProperties(false)); // create EventProperties
    }
    return eventList;
  }

  @Override
  protected List<FilterCondition> getFilterConditions(TopologyNodeType ntnType, int[] nodeIDs, PagingRestriction restriction, DbLanguage queryLang) {
    //userID
    int userID = sessionHdlr.getClientID();

    List<Integer> nodeIdList = ArrayHelper.toIntegerList(nodeIDs);
    List<FilterCondition> filterConditions = new ArrayList<>();
    filterConditions.addAll(getFilterConditionsForPageArea(restriction.getPageArea()));
    
    //FNMD-83740 - Correlated events are not displayed using restored DTAG database
    addFilterConditionsForLongIds(filterConditions, restriction);

    if (Boolean.TRUE.equals(restriction.getPacketServiceTabSelected())
            && (ntnType == TopologyNodeType.CUSTOMER_FOLDER || ntnType == TopologyNodeType.CUSTOMER
            || ntnType == TopologyNodeType.TRAIL_FOLDER || ntnType == TopologyNodeType.SERVICE_FOLDER)) {
      filterConditions.addAll(getFilterConditionsForPacketServiceFolder(ntnType, nodeIdList, userID));
    } else {
      filterConditions.addAll(getFilterConditionsForNodeType(ntnType, nodeIdList, userID));
    }

    adaptPagingRestrictions(restriction);
    filterConditions.addAll(super.getFilterConditions(ntnType, nodeIDs, restriction, queryLang));
    return filterConditions;
  }

  private void addFilterConditionsForLongIds(List<FilterCondition> filterConditions, PagingRestriction restriction) {
    if (restriction.getPageArea() == PageArea.CORRELATED_ALARMS || restriction.getPageArea() == PageArea.CORRELATED_HIST_ALARMS) {
      FilterOperator longIdFilter = null;
      for (FilterOperator filterOperator : restriction.getFilterOperators()) {
        if (filterOperator.getName().equals(PagingProperties.Event.ID.getBeanName())) {
          longIdFilter = filterOperator;

          Set<Long> correlatedIds = restriction.getPageArea() == PageArea.CORRELATED_ALARMS ?
              correlationHdlr.getCorrelatedEventIDs((long) filterOperator.getValue()) :
              correlationHdlr.getCorrelatedEventHistoryIDs((long) filterOperator.getValue());
          filterConditions.add(new FilterCondition<>(EventPageColumn.ID, DbOperator.IN, correlatedIds));
        }
      }
      restriction.getFilterOperators().remove(longIdFilter);
    }
  }
  public int convertServiceContainerToDS(TopologyNodeType ntnType, int serviceContainerID) {
    Collection<TopologyDTO> collection = null;
    try {
      collection = AbstractTopologyProvider.getTopologyProvider(ntnType).getSubTopologyStructure(ntnType, serviceContainerID);
      if (collection.isEmpty()) {
        return serviceContainerID;
      }
      return collection.iterator().next().getId();
    } catch (MDRequestFailedException e) {
      return serviceContainerID;
    }
  }
  public List<FilterCondition> getFilterConditionsForPageArea(PageArea pageArea) {
    List<FilterCondition> filterConditions = new ArrayList<>();
    NodeVisibilityHelper.SessionViewDescriptor sessionView = NodeVisibilityHelper.getInstance().getSessionViewDescriptor(0);

    if (pageArea == PageArea.ALARMS || pageArea == PageArea.ALL_LAYERED_ALARMS)
      filterConditions.add(new FilterCondition<>(EventPageColumn.TYPE, DbOperator.EQ, 0));
    else if (pageArea == PageArea.ALARM_HIST)
      filterConditions.add(new FilterCondition<>(EventPageColumn.TYPE, DbOperator.IN, ArrayHelper.toIntegerSet(1,2)));
    else if (pageArea != PageArea.CORRELATED_ALARMS && pageArea != PageArea.CORRELATED_HIST_ALARMS)
      filterConditions.add(new FilterCondition<>(EventPageColumn.IS_EVENT, DbOperator.EQ, true));

    if (pageArea == PageArea.SECURITY) {
      filterConditions.add(new FilterCondition<>(EventPageColumn.SECURITY_EVENT, DbOperator.EQ, true));
    }
    //if user does not have "Browse Security Event" permission -> add filter to discard security events
    if (!sessionView.isGranted_BrowseSecurityEvents) {
      filterConditions.add(new FilterCondition<>(EventPageColumn.SECURITY_EVENT, DbOperator.EQ, EventSecurityType.NON_SECURITY.getSqlValue()));
    }
    return filterConditions;
  }

  public List<FilterCondition<?>> getFilterConditionsForNodeType(TopologyNodeType ntnType, List<Integer> nodeIdList, int userID) {
    List<FilterCondition<?>> filterConditions = new ArrayList<>();
    //prepare subnet vector for restricted views     //TODO check filter
    List<Long> restrictedEventIds = Collections.emptyList();
    NodeVisibilityHelper.SessionViewDescriptor sessionView = NodeVisibilityHelper.getInstance().getSessionViewDescriptor(userID);
    if ( !sessionView.customerIds.isEmpty() ) {
      restrictedEventIds = eventDAO.getEventIdsForParentIdsAndTrapIds(sessionView.customerIds, ArrayHelper.toIntegerList(FSP_NMTraps.service_mismatch_alarms));
    }
    if (nodeIdList.isEmpty())
      return filterConditions;

    switch (ntnType) {
      case SUBNET:
        getFilterConditionForSubnet(nodeIdList, sessionView, filterConditions, restrictedEventIds);
        break;
      case NETWORK_ELEMENT:
        getFilterConditionForNetworkElement(nodeIdList, filterConditions, restrictedEventIds);
        break;
      case MODULE:
      case ENTITY:
        filterConditions.add(new FilterCondition<>(EventPageColumn.NE_ID, DbOperator.EQ, getNeId(nodeIdList.get(0))));
        break;
      case TIMING_LINE:
      case LINE:
        getFilterConditionForLine(nodeIdList, filterConditions, sessionView, restrictedEventIds);
        break;
      case SERVICE:
        getFilterConditionForService(ntnType, nodeIdList, filterConditions, sessionView);
        break;
      case PD_SERVICE:
        getFilterConditionForPDService(nodeIdList, filterConditions, sessionView);
        break;
      case PD_L3_IPVPN_SERVICE:
        getFilterConditionForPDL3Services(nodeIdList, filterConditions, sessionView);
        break;
      case SERVICE_INTENT:
      case PD_SERVICE_INTENT:
        filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.SERVICE_ID_BOTH), DbOperator.IN, nodeIdList));
        break;
      case CUSTOMER_FOLDER:
      case CUSTOMER:
      case TRAIL_FOLDER:
      case SERVICE_FOLDER:
        getFilterConditionForServiceFolder(ntnType, nodeIdList, filterConditions, sessionView);
        break;
      case SYNC_ROOT:
      case SYNC_NCD:
        getFilterConditionForSyncNcd(nodeIdList, filterConditions);
        break;
      case PTP:
        getFilterConditionForPTPOrSYNC(nodeIdList, SyncConsts.PTP_NCD_INTERVAL, filterConditions, SyncProtocolType.PTP);
        break;
      case SYNC_E:
        getFilterConditionForPTPOrSYNC(nodeIdList, SyncConsts.SyncE_NCD_INTERVAL, filterConditions, SyncProtocolType.SYNCE);
        break;
      case SYNC_NODE:
        filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.SYNC_NODE_ID), DbOperator.EQ, nodeIdList.get(0)));

        if (userRestrictionProvider.isNetworkViewRestricted()) {
          userRestrictionProvider.init();
          filterConditions.add(new FilterCondition<>(EventPageColumn.NE_ID, DbOperator.NOT_IN, userRestrictionProvider.getUserRestrictedNEIds()));
        }

        break;
      case SYNC_ROUTE:
        filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.SYNC_ROUTE_ID), DbOperator.EQ, nodeIdList.get(0)));
        //no restrictions for routes
        break;
      case FIBER_CUSTOMER_GROUP:
      case FIBER_ROUTE_GROUP:
      case FIBER_CUSTOMER:
        getFilterConditionForFiberCustomerAndGroup(ntnType, nodeIdList, filterConditions);
        break;
      case FIBER_RESOURCE_UNASSIGNED:
        getFilterConditionForFiberResourceUnAssigned(ntnType, filterConditions);
        break;
      case FIBER_RESOURCE_DUCT:
      case FIBER_RESOURCE_AP:
      case FIBER_RESOURCE_BUILDING:
      case FIBER_RESOURCE_REGION:
        List<Long> alIds = new ArrayList<>();
        alIds.add(0L);
        filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.ID), DbOperator.IN, alIds));
        break;
      case FIBER_RESOURCE_ROUTE:
        getFilterConditionForFiberResourceRoute(ntnType, nodeIdList, filterConditions);
        break;
      case FIBER_RESOURCE:
      case FIBER_ENTITY:
        filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.MODULE_TYPE_NAME), DbOperator.IN, getPageArea().equals(PageArea.EVENTS) ? Arrays.asList(NeTypeString.ALM, "N/A") : Arrays.asList(NeTypeString.ALM)));
        filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.SHORT_NAME), DbOperator.IN, getShortNameList()));
        break;
      case FIBER_ALARM:
        filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.ID), DbOperator.EQ, nodeIdList.get(0)));
        break;
      case ETHERNET_RING:
        filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.MLT_ID), DbOperator.IN, nodeIdList));
        break;
      case ETHERNET_RING_GROUP:
        getFilterConditionForEthernetRingGroup(nodeIdList, filterConditions);
        break;
      case ETHERNET_RING_RESOURCE:
        getFilterConditionForEthernetRingResource(nodeIdList, filterConditions);
        break;
      default:
        // for all other cases nothing needs to be done
        break;
    }

    return filterConditions;
  }

  public List<FilterCondition<Set<Integer>>> getFilterConditionsForPacketServiceFolder(TopologyNodeType ntnType, List<Integer> nodeIdList, int userID) {
    List<FilterCondition<Set<Integer>>> filterConditions = new ArrayList<>();
    NodeVisibilityHelper.SessionViewDescriptor sessionView = NodeVisibilityHelper.getInstance().getSessionViewDescriptor(userID);

    if (nodeIdList.isEmpty())
      return filterConditions;

    filterConditions.addAll(getFilteredConditionsForPacketServiceGroup(ntnType, nodeIdList));
    if ( !sessionView.subnetIds.isEmpty() )
      filterConditions.add(new FilterCondition<>(EventPageColumn.SUBNET_ID, DbOperator.IN, sessionView.subnetIds));

    return filterConditions;
  }

  private void getFilterConditionForNetworkElement(List<Integer> nodeIdList, List<FilterCondition<?>> filterConditions, List<Long> restrictedEventIds) {
    int neId = getNeId(nodeIdList.get(0));
    filterConditions.add(new FilterCondition<>(EventPageColumn.NE_ID, DbOperator.EQ, neId));
    if ( !restrictedEventIds.isEmpty() )
      filterConditions.add(new FilterCondition<>(EventPageColumn.ID, DbOperator.NOT_IN, restrictedEventIds));
  }

  private void getFilterConditionForPDL3Services(List<Integer> nodeIdList, List<FilterCondition<?>> filterConditions, NodeVisibilityHelper.SessionViewDescriptor sessionView) {
    filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.MLT_ID), DbOperator.IN, nodeIdList));
    if (!sessionView.subnetIds.isEmpty()){
      filterConditions.add(new FilterCondition<>(EventPageColumn.SUBNET_ID, DbOperator.IN, sessionView.subnetIds));
    }
  }

  private void getFilterConditionForServiceFolder(TopologyNodeType ntnType, List<Integer> nodeIdList, List<FilterCondition<?>> filterConditions, NodeVisibilityHelper.SessionViewDescriptor sessionView) {
    filterConditions.add(getFilteredConditionForServiceGroup(ntnType, nodeIdList));
    if ( !sessionView.subnetIds.isEmpty() )
      filterConditions.add(new FilterCondition<>(EventPageColumn.SUBNET_ID, DbOperator.IN, sessionView.subnetIds));
  }

  private void getFilterConditionForSyncNcd(List<Integer> nodeIdList, List<FilterCondition<?>> filterConditions) {
    filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.SYNC_NCD_ID), DbOperator.IN, nodeIdList));
    if (userRestrictionProvider.isNetworkViewRestricted()) {
      userRestrictionProvider.init();
      filterConditions.add(new FilterCondition<>(EventPageColumn.NE_ID, DbOperator.NOT_IN, userRestrictionProvider.getUserRestrictedNEIds()));
    }
  }

  private void getFilterConditionForEthernetRingResource(List<Integer> nodeIdList, List<FilterCondition<?>> filterConditions) {
    List<Integer> ringResourceIdsList;
    ringResourceIdsList = ethernetRingDataHelper.getAllRings().stream()
            .map(TopologyDTO::getId)
            .collect(Collectors.toList());
    ringResourceIdsList.addAll(ethernetRingDataHelper.getAllRingGroups().stream()
            .map(TopologyDTO::getId)
            .toList());

    ringResourceIdsList.add(nodeIdList.get(0));
    filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.MLT_ID_GROUP), DbOperator.IN, ringResourceIdsList));
  }

  private void getFilterConditionForEthernetRingGroup(List<Integer> nodeIdList, List<FilterCondition<?>> filterConditions) {
    List<Integer> ringGroupIdsList;
    ringGroupIdsList = ethernetRingDataHelper.getAllRingsUnder(nodeIdList.get(0)).stream()
            .map(TopologyDTO::getId)
            .collect(Collectors.toList());

    ringGroupIdsList.add(0, nodeIdList.get(0));
    filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.MLT_ID_GROUP), DbOperator.IN, ringGroupIdsList));
  }

  private void getFilterConditionForFiberResourceUnAssigned(TopologyNodeType ntnType, List<FilterCondition<?>> filterConditions) {
    try {
      List<Long> alarmIds;
      if(getPageArea().equals(PageArea.EVENTS)){
        alarmIds = gisTransferCtrl.getEventIdsForFiberObject(ntnType, 0);
      } else {
        alarmIds = gisTransferCtrl.getAlarmIdsForFiberObject(ntnType, 0);
      }
      filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.ID), DbOperator.IN, alarmIds));
    } catch (Exception e) {
        log.error("Error during retrieving alarms for {}", ntnType, e);
      List<Long> alarmIds = new ArrayList<>();
      alarmIds.add(0L);
      filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.ID), DbOperator.IN, alarmIds));
    }
  }

  private void getFilterConditionForPTPOrSYNC(List<Integer> nodeIdList, int ptpNcdInterval, List<FilterCondition<?>> filterConditions, SyncProtocolType ptp) {
    //ncdId is saved in DB as the ptp or synce folder
    Integer ptpOffsetId = nodeIdList.get(0) - ptpNcdInterval;
    filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.SYNC_NCD_ID), DbOperator.EQ, ptpOffsetId));
    filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.SYNC_NODE_TYPE), DbOperator.EQ, ptp.ordinal()));
    if (userRestrictionProvider.isNetworkViewRestricted()) {
      userRestrictionProvider.init();
      filterConditions.add(new FilterCondition<>(EventPageColumn.NE_ID, DbOperator.NOT_IN, userRestrictionProvider.getUserRestrictedNEIds()));
    }
  }

  private void getFilterConditionForFiberCustomerAndGroup(TopologyNodeType ntnType, List<Integer> nodeIdList, List<FilterCondition<?>> filterConditions) {
    try {
      List<Long> eventIds;
      if(getPageArea().equals(PageArea.EVENTS)){
        eventIds = gisTransferCtrl.getEventIdsForFiberObject(ntnType, nodeIdList);
        List<FilterCondition> nonAlarmFilterConditions = new ArrayList<>();
        if(TopologyNodeType.FIBER_ROUTE_GROUP == ntnType){
          nonAlarmFilterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.SERVICE_GRP_ID),
                  DbOperator.IN, gisTransferCtrl.adaptNodeIds(TopologyNodeType.FIBER_ROUTE_GROUP, nodeIdList)));
        } else {
          nonAlarmFilterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.SERVICE_GRP_ID), DbOperator.IN, nodeIdList));
        }
        List<EventDTO> nonAlarmIds = eventDBQueryHdlr.getEventsFor(PagingCondition.DEFAULT, SortingConditionConstant.DEFAULT, nonAlarmFilterConditions);
        eventIds.addAll(nonAlarmIds.stream().map(e -> e.id).toList());
      } else {
        eventIds = gisTransferCtrl.getAlarmIdsForFiberObject(ntnType, nodeIdList);
      }
      filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.ID), DbOperator.IN, eventIds));
    } catch (Exception e) {
        log.error("Error during retrieving alarms for {} and id {}", ntnType, nodeIdList, e);
      List<Long> alarmIds = new ArrayList<>();
      alarmIds.add(0L);
      filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.ID), DbOperator.IN, alarmIds));
    }
  }

  private void getFilterConditionForFiberResourceRoute(TopologyNodeType ntnType, List<Integer> nodeIdList, List<FilterCondition<?>> filterConditions) {
    if(!nodeIdList.isEmpty() && nodeIdList.get(0) == 0){
      filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.MODULE_TYPE_NAME), DbOperator.IN, getPageArea().equals(PageArea.EVENTS) ? Arrays.asList(NeTypeString.ALM, "N/A") : Arrays.asList(NeTypeString.ALM)));
      filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.SHORT_NAME), DbOperator.IN, getShortNameList()));
    } else if(!nodeIdList.isEmpty()) {
      try {
        List<Long> alarmIds;
        if(getPageArea().equals(PageArea.EVENTS)){
          alarmIds = gisTransferCtrl.getEventIdsForFiberObject(ntnType, nodeIdList.get(0));
        } else {
          alarmIds = gisTransferCtrl.getAlarmIdsForFiberObject(ntnType, nodeIdList.get(0));
        }
        filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.ID), DbOperator.IN, alarmIds));
      } catch (Exception e) {
          log.error("Error during retrieving alarms for {} and id {}", ntnType, nodeIdList.get(0), e);
        List<Long> alarmIds = new ArrayList<>();
        alarmIds.add(0L);
        filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.ID), DbOperator.IN, alarmIds));
      }
    }
  }

  private void getFilterConditionForPDService(List<Integer> nodeIdList, List<FilterCondition<?>> filterConditions, NodeVisibilityHelper.SessionViewDescriptor sessionView) {
    if (pdServiceDataController.getPDService(nodeIdList.get(0)) != null ||
            pdl3IPVPNServiceApi.getServiceById(nodeIdList.get(0)) != null)
      filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.MLT_ID), DbOperator.IN, nodeIdList));
    else
      filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.SERVICE_ID), DbOperator.IN, nodeIdList));
    if (!sessionView.subnetIds.isEmpty()){
      filterConditions.add(new FilterCondition<>(EventPageColumn.SUBNET_ID, DbOperator.IN, sessionView.subnetIds));
    }
  }

  private void getFilterConditionForService(TopologyNodeType ntnType, List<Integer> nodeIdList, List<FilterCondition<?>> filterConditions, NodeVisibilityHelper.SessionViewDescriptor sessionView) {
    if (smTopologyHdlr.isMlTopologyNodeType(ntnType, ArrayHelper.toIntArray(nodeIdList)))
      filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.MLT_ID), DbOperator.IN, nodeIdList));
    else
      filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.SERVICE_ID), DbOperator.IN, nodeIdList));

    if ( !sessionView.subnetIds.isEmpty() )
      filterConditions.add(new FilterCondition<>(EventPageColumn.SUBNET_ID, DbOperator.IN, sessionView.subnetIds));
  }

  private void getFilterConditionForLine(List<Integer> nodeIdList, List<FilterCondition<?>> filterConditions, NodeVisibilityHelper.SessionViewDescriptor sessionView, List<Long> restrictedEventIds) {
    if (isNILink(nodeIdList)) {
      filterConditions.add(new FilterCondition<>(getPageColumn(EventPageColumn.MLT_ID), DbOperator.IN, nodeIdList));
      if ( !sessionView.subnetIds.isEmpty() )
        filterConditions.add(new FilterCondition<>(EventPageColumn.SUBNET_ID, DbOperator.IN, sessionView.subnetIds));
      return;
    }
    filterConditions.add(new FilterCondition<>(EventPageColumn.LINE_ID, DbOperator.IN, nodeIdList));
    if ( !sessionView.subnetIds.isEmpty() )
      filterConditions.add(new FilterCondition<>(EventPageColumn.SUBNET_ID, DbOperator.IN, sessionView.subnetIds));
    if ( !restrictedEventIds.isEmpty() )
      filterConditions.add(new FilterCondition<>(EventPageColumn.ID, DbOperator.NOT_IN, restrictedEventIds));
  }

  private void getFilterConditionForSubnet(List<Integer> nodeIdList, NodeVisibilityHelper.SessionViewDescriptor sessionView, List<FilterCondition<?>> filterConditions, List<Long> restrictedEventIds) {
    int topLevelSubnetID = topLevelSubnetHdlr.getTopLevelSubnetID();
    boolean containsRootId = nodeIdList.contains(topLevelSubnetID);
    boolean isNetViewRestricted = sessionView.isNetworkViewRestricted;
    if (isNetViewRestricted || !containsRootId) // skip filter if not restricted and for top-level subnet
      filterConditions.add(new FilterCondition<>(EventPageColumn.SUBNET_ID, DbOperator.IN, nodeIdList));
    //if network view restricted, we dont show events directly related to Network Root
    if (isNetViewRestricted && containsRootId) {
      nodeIdList.remove(nodeIdList.get(nodeIdList.indexOf(topLevelSubnetID)));
    }
    if ( !restrictedEventIds.isEmpty() )
      filterConditions.add(new FilterCondition<>(EventPageColumn.ID, DbOperator.NOT_IN, restrictedEventIds));
  }

  private List<String> getShortNameList() {
    List<String> namesList = new ArrayList<>();
    namesList.add("THRES-CROSSED-FAST");
    namesList.add("THRES-CROSSED-MEDIUM");
    namesList.add("THRES-CROSSED-SLOW");
    namesList.add("L-B-EXCEEDED");
    namesList.add("L-B-N-EXCEEDED");
    namesList.add("RT-LNK-F");
    namesList.add("SEN1-HT");
    namesList.add("SEN2-HT");
    namesList.add("LOSS-DEV-H");
    namesList.add("LOSS-H");
    if(getPageArea().equals(PageArea.EVENTS)) {
      namesList.add("S-CUSTOMER");
      namesList.add("S-FIBER");
      namesList.add("S-CGRP");
    }
    return namesList;
  }

  private FilterCondition<?> getFilteredConditionForServiceGroup(TopologyNodeType ntnType, List<Integer> nodeIds) {
    PageColumn pageColumn = getPageColumn(EventPageColumn.MLT_ID);
    switch(ntnType) {
      case CUSTOMER_FOLDER,CUSTOMER, SERVICE_FOLDER:
        List<Integer> serviceGroupIds = customerDAO.getCustomerServiceGroupIdsByParentIds(nodeIds);
        Set<Integer> filteredIdsSet = new HashSet<>(serviceGroupIds);
        List<Integer> mlTopologyIds = eventDAO.getAssociatedIdsWithEvents(nodeIds);
        List<Integer> pdServiceIds = pdServiceDataController.getPDServiceIdsUnderServiceGroupIdList(nodeIds);
        List<Integer> ipvpnServiceIds = pdl3IPVPNServiceApi.getServicesByParentIds(new HashSet<>(nodeIds))
                .stream()
                .map(service -> service.getId())
                .toList();
        filteredIdsSet.addAll(mlTopologyIds);
        filteredIdsSet.addAll(pdServiceIds);
        filteredIdsSet.addAll(ipvpnServiceIds);
        if (!filteredIdsSet.isEmpty()) {
          return new FilterCondition<>(pageColumn, DbOperator.IN, new ArrayList<>(filteredIdsSet));
        } else {
          // Use original IDs if none are found with the above filtering
          return new FilterCondition<>(pageColumn, DbOperator.IN, nodeIds);
        }
      case TRAIL_FOLDER: // Return nodeIDs which have associated events (connections folder group, only ML trails)
      default:
        return new FilterCondition<>(pageColumn, DbOperator.IN, nodeIds);
    }
  }

  private List<FilterCondition<Set<Integer>>> getFilteredConditionsForPacketServiceGroup(TopologyNodeType ntnType, List<Integer> nodeIds) {
    List<FilterCondition<Set<Integer>>> filterConditions = new ArrayList<>();
    PageColumn pageColumn = getPageColumn(EventPageColumn.MLT_ID);
    switch(ntnType) {
      case CUSTOMER_FOLDER,CUSTOMER, SERVICE_FOLDER:
        filterConditions.add(getFilterConditionForPacketServiceGroup(nodeIds, pageColumn));
        filterConditions.addAll(getFilterConditionsForPacketNEsAndAssociatedSubnets());
        break;
      case TRAIL_FOLDER: // Return nodeIDs which have associated events (connections folder group, only ML trails)
      default:
        filterConditions.add(new FilterCondition<>(pageColumn, DbOperator.IN, new HashSet<>(nodeIds)));
    }
    return filterConditions;
  }

  private FilterCondition<Set<Integer>> getFilterConditionForPacketServiceGroup(List<Integer> nodeIds, PageColumn pageColumn) {
    List<Integer> serviceGroupIds = customerDAO.getPacketCustomerServiceGroupIdsByParentIds(nodeIds);
    Set<Integer> filteredIdsSet = new HashSet<>(serviceGroupIds);
    List<Integer> mlTopologyIds = eventDAO.getAssociatedIdsWithEvents(nodeIds);
    List<Integer> pdServiceIds = pdServiceDataController.getPDServiceIdsUnderServiceGroupIdList(nodeIds);
    List<Integer> ipvpnServiceIds = pdl3IPVPNServiceApi.getServicesByParentIds(new HashSet<>(nodeIds))
            .stream()
            .map(PDL3IPVPNServiceImplDto::getId)
            .toList();
    filteredIdsSet.addAll(mlTopologyIds);
    filteredIdsSet.addAll(pdServiceIds);
    filteredIdsSet.addAll(ipvpnServiceIds);
    if (!filteredIdsSet.isEmpty()) {
      return new FilterCondition<>(pageColumn, DbOperator.IN, new HashSet<>(filteredIdsSet));
    } else {
      // Use original IDs if none are found with the above filtering
      return new FilterCondition<>(pageColumn, DbOperator.IN, new HashSet<>(nodeIds));
    }
  }

  private List<FilterCondition<Set<Integer>>> getFilterConditionsForPacketNEsAndAssociatedSubnets() {
    List<FilterCondition<Set<Integer>>> filterConditions = new ArrayList<>();
    Set<NetworkElementDBImpl> neDBs = NetworkElementDAO.getInstance().getAll();
    Set<NetworkElementDBImpl> f3andF4NeDBs = neDBs.stream()
            .filter(networkElementDB -> NEUtils.isF3orF4Device(networkElementDB.getNetworkElementType()))
            .collect(Collectors.toSet());
    Set<SubnetDBImpl> associatedSubnetDBs = f3andF4NeDBs.stream().map(NetworkElementDBImpl::getSubnet).collect(Collectors.toSet());
    filterConditions.add(getFilterConditionForPacketNEs(f3andF4NeDBs));
    filterConditions.add(getFilterConditionForPacketSubnets(associatedSubnetDBs));
    return filterConditions;
  }

  private FilterCondition<Set<Integer>> getFilterConditionForPacketNEs(Set<NetworkElementDBImpl> f3andF4NeDBs) {
    Set<Integer> neIds = f3andF4NeDBs.stream()
            .map(NetworkElementDBImpl::getId)
            .collect(Collectors.toSet());
    neIds.add(0); // Allow for the case where no NE is associated
    return new FilterCondition<>(EventPageColumn.NE_ID, DbOperator.IN, neIds);
  }

  private FilterCondition<Set<Integer>> getFilterConditionForPacketSubnets(Set<SubnetDBImpl> associatedSubnetDBs) {
    Set<Integer> associatedSubnetIds = associatedSubnetDBs.stream()
            .map(SubnetDBImpl::getId)
            .collect(Collectors.toSet());
    Set<Integer> subnetIds = new HashSet<>(SubnetDAO.getInstance().getAllSubnetsIDs());
    if (!subnetIds.isEmpty()) {
      subnetIds.removeAll(associatedSubnetIds);
      subnetIds.remove(topLevelSubnetHdlr.getTopLevelSubnetID()); // Allow for top level subnet (Network)
    }
    return new FilterCondition<>(EventPageColumn.SUBNET_ID, DbOperator.NOT_IN, subnetIds);
  }

  @MDPersistenceContext
  private int getNeId(int nodeId) {
    if (moAdapter.getNe(MOAdapter.NeKey.ID, nodeId) != null)
      return nodeId;

    MLNodeDBImpl mlNodeDB = mlTopologyElementDAO.getNodeByID(nodeId);
    List<MLTopologyMOReference> moRefs = (mlNodeDB != null) ? mlNodeDB.getRelatedMOObjects() : Collections.emptyList();
    for (MLTopologyMOReference moRef : moRefs) {
      if (moRef instanceof MLTopologyNeMoReference)
        return ((MLTopologyNeMoReference) moRef).getNeID();
    }
    log.warn("EventPageHdlr.getNeId failed for MlNeId={}", nodeId);
    return nodeId;
  }

  @MDPersistenceContext
  private boolean isNILink(List<Integer> nodeIdList) {
    if (nodeIdList.isEmpty())
      return false;
    if (nodeIdList.size() == 1) {
      MLTrailDBImpl mlTrailDB = MDPersistenceHelper.find(MLTrailDBImpl.class, nodeIdList.get(0));
      return mlTrailDB != null && mlTrailDB.getNILink() != null;
    }
    return false;
  }

  private void adaptPagingRestrictions(PagingRestriction pagingRestriction) {
    for (int index = 0; index < pagingRestriction.getFilterOperators().size(); index++) {
      FilterOperator filterOperator = pagingRestriction.getFilterOperators().get(index);
      PageColumn fieldName = getPageColumn(filterOperator.getName());
      if (fieldName != null) {
        // special adaption for fake entities (e.g. PSH/96CSM)
        if (fieldName.equals(EventPageColumn.OBJECT_INDEX) && filterOperator.getValue() instanceof Object[]) {
          List<String> completeObjectIndexList = new ArrayList<>();
          for (Object objectIndex : (Object[]) filterOperator.getValue()) {
            completeObjectIndexList.add((String) objectIndex);
            completeObjectIndexList.add("-" + (String) objectIndex);
          }
          EnumOperator newOperator = new EnumOperator<Object[]>(filterOperator.getType(), filterOperator.getName(),
            completeObjectIndexList.toArray(new String[completeObjectIndexList.size()]));
          pagingRestriction.getFilterOperators().set(index, newOperator);
        }
        // special adaption for Ack-Time column ico BEFORE (LESSTHAN): add GREATERTHAN 0 filter
        if (fieldName.equals(EventPageColumn.ACKTIME) && filterOperator.getType() == FilterOperator.Type.LESSTHAN) {
          pagingRestriction.getFilterOperators().add(new DateOperator(FilterOperator.Type.GREATERTHAN, filterOperator.getName(), new Date(0)));
        }
      }
    }
  }


  public long getTotalCounter(TopologyNodeType ntnType, int[] nodeIDs, PagingRestriction restriction) {
    if (restriction.getPageArea().equals(PageArea.ALL_LAYERED_ALARMS)) {
      ntnType = TopologyNodeType.SERVICE_INTENT;
      nodeIDs = Ints.toArray(getPageContextIDList(getPageArea(), ntnType, nodeIDs[0]));
    }

    List<FilterCondition> filterConditions = getFilterConditions(ntnType, nodeIDs, restriction, DbLanguage.DEFAULT);

    return eventDBQueryHdlr.getTotalCounter(filterConditions, restriction.getPageArea(), false);
  }

  /* This method returns the list of events id's based on the applied filter conditions */
  protected List<Long> getPagingDataIds(TopologyNodeType ntnType, int[] nodeIDs, PagingRestriction restriction, boolean isSecurityEvent) {
    List<FilterCondition> filterConditions = getFilterConditions(ntnType, nodeIDs, restriction, DbLanguage.DEFAULT);

    return eventDBQueryHdlr.getAllFilteredEventIds(filterConditions, restriction.getPageArea(), isSecurityEvent);
  }

  @Override
  public String getDBColumnNameFromBeanName(String beanName, boolean isJPSQL) {
    return null;
  }

  @MDPersistenceContext
  public Set<Integer> getAllLayerServiceIDs(int connID) {

    if (smTopologyHdlr.isMlTopologyNodeType(TopologyNodeType.SERVICE, new int[] {connID}))
      return getAllLayerMlServiceIDs(connID);

    //retrieve all ODS from connection:
    Set<TransportService> connectionList = new HashSet<>();
    try {
      TransportService conn = configCtrl.getServiceManagerCtrl().getServiceManagerFacade().getConnectionByID(connID);
      connectionList.add(conn);
      //is it ODS? -> retrieve lower layers
      if (conn instanceof SubChConnectionDBImpl || conn instanceof EthernetConnectionDBImpl || conn instanceof SatopEthernetConnectionDBImpl) {
        connectionList.addAll(((AbstractConnectionDBImpl) conn).getContainingConnections(true));
      }
      else if (conn instanceof PWE3EthernetTrailDBImpl) {
        connectionList.addAll(ConnectionDAO.getInstance().getUpperLayerConnections(connID));
      }
      else {
        //retrive ODS and then all layers underneath
        for (AbstractConnectionDBImpl connectionDB : ConnectionDAO.getInstance().getUpperLayerConnections(connID)) {
          if (connectionDB instanceof SubChConnectionDBImpl) {
            connectionList.addAll(connectionDB.getContainingConnections(true));
          }
        }
      }

      Set<Integer> connIdsList = new HashSet<>();

      for (TransportService connection : connectionList) {
        connIdsList.add(connection.getId());
      }

      return connIdsList;
    } catch (NoSuchConnectionException e) {
      log.warn("getAllLayerServiceIDs: connection not found for id {}", connID);
      return Collections.singleton(connID);
    }
  }

  private Set<Integer> getAllLayerMlServiceIDs(int connID) {
    final MLTrailDBImpl startTrailDB = mlTopologyElementDAO.getTrailByID(connID);
    Queue<MLTopologyElementDBImpl> entityQueue = new LinkedList<>();
    entityQueue.add(startTrailDB);
    Set<Integer> resultList = new HashSet<>();
    while (!entityQueue.isEmpty()) {
      MLTopologyElementDBImpl teDB = entityQueue.remove();  // next object

      if (teDB instanceof MLTrailDBImpl || teDB instanceof MLMonitoringSectionDBImpl)
        resultList.add(teDB.getId());                       // add Trail-/Section-ID

      if (teDB instanceof MLTrailDBImpl) {
        Set<MLTopologyElementDBImpl> connDBs = new HashSet<>();
        MLTrailDBImpl trailDB = (MLTrailDBImpl) teDB;
        List<MLPath> paths = Stream.of(trailDB.getWorkingForwardPath(), trailDB.getWorkingBackwardPath())
                                   .filter(Objects::nonNull).collect(Collectors.toList());
        paths.forEach(p-> p.connectionSequence().forEach(c-> connDBs.add((MLTopologyElementDBImpl) c)));

        ArrayList<MLPath> mlPaths = new ArrayList<>();
        mlPaths.add(trailDB.getWorkingForwardPath());
        mlPaths.add(trailDB.getWorkingBackwardPath());
        for (MLPath mlPath : mlPaths) {
          for (MLConnectionPoint mlConnectionPoint : mlPath.connectionPointSequence()) {
            connDBs.add((MLTopologyElementDBImpl) mlConnectionPoint);
          }
        }

        entityQueue.addAll(connDBs);   // get children for next iteration
      }
      else if (teDB instanceof MLConnectionDBImpl) {
        MLConnectionDBImpl connDB = (MLConnectionDBImpl) teDB;
        if (connDB.getConnectionType() == MLConnectionType.NetworkConnection) {
          resultList.add(connDB.getId());
          entityQueue.addAll(mlTopologyElementDAO.getMonitoringSectionsByParent(connDB.getId()));
        }
        else {
          MLLayerAdaptationDBImpl layerAdaptationDB = mlTopologyElementDAO.getAdaptationForClientEntity(connDB);
          if (layerAdaptationDB != null) {
            entityQueue.addAll(layerAdaptationDB.getServerEntities());
          }
        }
      }
      else if (teDB instanceof MLPathMonitoringSectionDBImpl) {
        Set<MLTopologyElementDBImpl> connDBs = new HashSet<>();
        MLPathMonitoringSectionDBImpl monitoringSectionDB = (MLPathMonitoringSectionDBImpl) teDB;
        List<MLPath> paths = Stream.of(monitoringSectionDB.getAssociatedPath(), monitoringSectionDB.getAssociatedBackwardPath())
                                   .filter(Objects::nonNull).collect(Collectors.toList());
        paths.forEach(p-> p.connectionSequence().forEach(c-> connDBs.add((MLTopologyElementDBImpl) c)));
        entityQueue.addAll(connDBs);   // get children for next iteration
      }
    }

    //TODO add match filer

    return resultList;
  }

  /**
   * Method to get all services under folder, customer or customer folder
   * @param nodeIdArray array of Node IDs from API request
   * @return returns list of all filtered ID set belonging to the group
   */
  public Set<Integer> getAllNodesIdsUnderFolder(int[] nodeIdArray) {
    List<Integer> nodeIdList = Arrays.stream(nodeIdArray).boxed().toList();
    List<Integer> serviceGroupIds = customerDAO.getCustomerServiceGroupIdsByParentIds(nodeIdList);
    Set<Integer> filteredIdsSet = new HashSet<>(serviceGroupIds);
    List<Integer> mlTopologyIds = eventDAO.getAssociatedIdsWithEvents(nodeIdList);
    List<Integer> pdServiceIds = pdServiceDataController.getPDServiceIdsUnderServiceGroupIdList(nodeIdList);
    List<Integer> ipvpnServiceIds = pdl3IPVPNServiceApi.getServicesByParentIds(new HashSet<>(nodeIdList))
            .stream()
            .map(PDL3IPVPNServiceImplDto::getId)
            .toList();

    filteredIdsSet.addAll(mlTopologyIds);
    filteredIdsSet.addAll(pdServiceIds);
    filteredIdsSet.addAll(ipvpnServiceIds);
    return filteredIdsSet;
  }


}
