/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: smuller
 */

package com.adva.nlms.mediation.topology.eod;

import com.adva.nlms.commondefinition.namerepresentation.NameDto;
import com.adva.nlms.commondefinition.namerepresentation.NameRepresentation;
import com.adva.nlms.mediation.common.eod.PagingAndSortingHelper;
import com.adva.nlms.mediation.common.eod.PagingAndSortingOptions;
import com.adva.nlms.mediation.config.NetworkElementDAO;
import com.adva.nlms.mediation.config.NetworkElementDBImpl;
import com.adva.nlms.mediation.security.api.SecurityCtrl;
import com.adva.nlms.mediation.topology.SubnetDAO;
import com.adva.topology.manager.api.dto.FilterConditionDto;
import com.adva.topology.manager.api.dto.NodeDto;
import com.adva.topology.manager.api.in.TopologyNodeApi;
import jakarta.ws.rs.NotFoundException;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * This class supports reading topology information from database.
 */

@Component
public class TopologyEodManagerImpl implements TopologyNodeApi {
  private final SecurityCtrl securityCtrl;
  private final NetworkElementHandler networkElementHandler;
  private final NodeProvider nodeProvider;

  private final Map<String, String> dto2AcceptableSortingDbFieldMap;
  private final List<String> defaultSortParams;

  TopologyEodManagerImpl(SecurityCtrl securityCtrl, NetworkElementHandler networkElementHandler, NodeProvider nodeProvider) {
    this.securityCtrl = securityCtrl;
    this.networkElementHandler = networkElementHandler;
    this.nodeProvider = nodeProvider;

    defaultSortParams = List.of("name", "asc");
    dto2AcceptableSortingDbFieldMap = new HashMap<>();
    initializeDto2AcceptableSortingDbFieldMap();
  }

  private void initializeDto2AcceptableSortingDbFieldMap() {
    dto2AcceptableSortingDbFieldMap.put("id", "uuid");
    dto2AcceptableSortingDbFieldMap.put("name", "name");
    dto2AcceptableSortingDbFieldMap.put("neId", "id");
    dto2AcceptableSortingDbFieldMap.put("neType", "typeString");
    dto2AcceptableSortingDbFieldMap.put("serialNumber", "serial");
    dto2AcceptableSortingDbFieldMap.put("swVersion", "sysSwVer");
    dto2AcceptableSortingDbFieldMap.put("ipAddress", "ipAddress");
  }


  /**
   * Returns all optical nodes (filtered by name) logged user has access to
   *
   * @return list of optical nodes
   */
  public List<NodeDto> getOpticalNodes(String name, Integer size, Integer page, List<String> sortingParams,
                                       FilterConditionDto filterCriteria) {
    if ((name != null && !name.isEmpty()) || (filterCriteria != null)) {
      return getOpticalNodesByName(name, size, page, sortingParams, filterCriteria);
    } else {
      return getOpticalNodes(size, page, sortingParams);
    }
  }

  /**
   * Returns all optical nodes logged user has access to
   *
   * @return list of optical nodes
   */
  private List<NodeDto> getOpticalNodes(Integer size, Integer page, List<String> sortingParams) {

    PagingAndSortingOptions psOpts = PagingAndSortingHelper.createPagingRestriction(
            size, page, sortingParams, defaultSortParams,
            dto2AcceptableSortingDbFieldMap.keySet().stream().toList());

    Set<Integer> restrictedSubnets =
            securityCtrl.isNetworkViewRestricted() ? securityCtrl.getNetworkView() : Collections.emptySet();
    List<NetworkElementDBImpl> opticalNodes = networkElementHandler.getOpticalNodes(
            restrictedSubnets, psOpts, dto2AcceptableSortingDbFieldMap);

    return transformToNeNodes(opticalNodes);
  }

  private List<NodeDto> getOpticalNodesByName(String name, Integer size, Integer page, List<String> sortingParams,
                                              FilterConditionDto filterCriteria) {
    PagingAndSortingOptions psOpts = PagingAndSortingHelper.createPagingRestriction(
            size, page, sortingParams, defaultSortParams,
            dto2AcceptableSortingDbFieldMap.keySet().stream().toList());

    Set<Integer> restrictedSubnets =
            securityCtrl.isNetworkViewRestricted() ? securityCtrl.getNetworkView() : Collections.emptySet();
    List<NetworkElementDBImpl> opticalNodes = networkElementHandler.getOpticalNodesByName(
            restrictedSubnets, name, psOpts, dto2AcceptableSortingDbFieldMap, filterCriteria);

    return transformToNeNodes(opticalNodes);
  }

  /**
   * Returns all optical nodes with the requested IDs
   *
   * @return list of optical nodes
   */
  public List<NodeDto> getOpticalNodesByUUIDs(List<UUID> uuids, Integer size, Integer page, List<String> sortingParams) {
    PagingAndSortingOptions psOpts = PagingAndSortingHelper.createPagingRestriction(
            size, page, sortingParams, defaultSortParams,
            dto2AcceptableSortingDbFieldMap.keySet().stream().toList());

    Set<Integer> restrictedSubnets =
            securityCtrl.isNetworkViewRestricted() ? securityCtrl.getNetworkView() : Collections.emptySet();
    List<NetworkElementDBImpl> opticalNodes = networkElementHandler.getNodesByUUIDs(
            restrictedSubnets, uuids, psOpts, dto2AcceptableSortingDbFieldMap);

    return transformToNeNodes(opticalNodes);
  }

  /**
   * Returns all optical nodes in compact format
   *
   * @return list of optical nodes
   */

  public List<NodeDto> getAllOpticalNodes() {
    List<NetworkElementHandler.NodeUuidNameSubnetId> opticalNodes = networkElementHandler.getAllOpticalNodes();
    return transformToNeNodesCompact(opticalNodes);
  }


  private List<NodeDto> transformToNeNodes(List<NetworkElementDBImpl> opticalNodes) {
    List<NodeDto> nodeDtos = opticalNodes.stream().
            map(networkElementDB -> nodeProvider.provideNode(networkElementDB.getId())).toList();
    List<NodeDto> neNodes = new ArrayList<>();
    for (NodeDto nodeDto : nodeDtos) {
      neNodes.add(mapDto(nodeDto));
    }
    return neNodes;
  }

  private List<NodeDto> transformToNeNodesCompact(List<NetworkElementHandler.NodeUuidNameSubnetId> opticalNodes) {
    return opticalNodes.stream()
            .map(n -> new NodeDto(n.id(), n.name(), n.subnetId()))
            .toList();
  }

  private NodeDto mapDto(NodeDto nodeDto) {
    return new NodeDto(nodeDto.id(), nodeDto.name(), nodeDto.neId(), nodeDto.neType(), nodeDto.neTypeId(), nodeDto.subnetId(),
            nodeDto.reachabilityStatus(), nodeDto.serialNumber(), nodeDto.swVersion(), nodeDto.cpMode(), nodeDto.lifecycleState(),
            nodeDto.ipAddress(), nodeDto.operationalState(), nodeDto.path());
  }

  /**
   * Returns node filtered by UUID
   *
   * @return NodeDto that has the input UUID
   */
  public NodeDto getNodeByUUID(UUID uuid) {
    Set<Integer> restrictedSubnets =
            securityCtrl.isNetworkViewRestricted() ? securityCtrl.getNetworkView() : Collections.emptySet();
    NetworkElementDBImpl element = networkElementHandler.getNodeByUUID(restrictedSubnets, uuid);
    if (element != null) {
      return mapDto(nodeProvider.provideNode(element.getId()));
    } else {
      throw new NotFoundException();
    }
  }

  public String getNodeNameByUUID(UUID uuid) {
    Set<Integer> restrictedSubnets =
            securityCtrl.isNetworkViewRestricted() ? securityCtrl.getNetworkView() : Collections.emptySet();
    NetworkElementDBImpl element = networkElementHandler.getNodeByUUID(restrictedSubnets, uuid);
    if (element != null) {
      return nodeProvider.provideNode(element.getId()).name()
              .stream()
              .filter(nameDto -> nameDto.valueName().equals(NameRepresentation.USER_LABEL))
              .findFirst()
              .map(NameDto::value)
              .orElseThrow(NotFoundException::new);
    }
    throw new NotFoundException();
  }

  /**
   * Retrieve NodeDto by its NE id
   *
   * @param neId database id
   * @return NodeDto or null
   */
  public NodeDto getNodeByNeId(int neId) {
    NetworkElementDBImpl element = NetworkElementDAO.getInstance().getNEDBImplOptional(neId).orElse(null);
    if (element != null) {
      return mapDto(nodeProvider.provideNode(element.getId()));
    } else {
      throw new NotFoundException();
    }
  }

  public UUID getNodeUUIDByNeId(int neId) {
    return NetworkElementDAO.getInstance().getNEDBImplOptional(neId)
      .map(NetworkElementDBImpl::getUuid)
      .orElseThrow(NotFoundException::new);
  }

  public List<UUID> getChildSubnetUuids(UUID subnetId) {
    return SubnetDAO.getInstance().getChildSubnetUuids(subnetId);
  }

  public UUID getSubnetUuidByName(String subnetName) {
    return SubnetDAO.getInstance().getSubnetUuidByName(subnetName);
  }
}
