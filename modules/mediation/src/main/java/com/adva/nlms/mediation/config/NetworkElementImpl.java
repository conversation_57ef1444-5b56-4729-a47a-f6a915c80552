/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: benjamint
 */

package com.adva.nlms.mediation.config;

import com.adva.common.StopWatch;
import com.adva.common.util.IntHashMap;
import com.adva.common.util.Resources;
import com.adva.device_inventory.license_manager.api.in.DeviceLicenseManager;
import com.adva.nlms.common.AdministrationStateType;
import com.adva.nlms.common.AlarmTypeHandler;
import com.adva.nlms.common.AlarmTypeHandlerImpl;
import com.adva.nlms.common.HTTPPropertiesDTO;
import com.adva.nlms.common.NEUtils;
import com.adva.nlms.common.NO_TIMESTAMP;
import com.adva.nlms.common.TopologyNodeType;
import com.adva.nlms.common.annotation.RemoteInterfaceMethod;
import com.adva.nlms.common.benchmark.Benchmark;
import com.adva.nlms.common.capabilities.api.CapabilitiesRegistry;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.config.IdentificationKey;
import com.adva.nlms.common.config.ModuleType;
import com.adva.nlms.common.config.NetworkElementDTO;
import com.adva.nlms.common.config.NodeIdentityType;
import com.adva.nlms.common.config.ResponseStatus;
import com.adva.nlms.common.config.netypes.NEType;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.event.EventSeverity;
import com.adva.nlms.common.event.EventType;
import com.adva.nlms.common.event.types.CategoryType;
import com.adva.nlms.common.messages.ConfigChangeType;
import com.adva.nlms.common.messages.TopologyChange;
import com.adva.nlms.common.necomm.SmartProtocolsType;
import com.adva.nlms.common.networktree.ObjectAlarmState;
import com.adva.nlms.common.networktree.TopologyDTO;
import com.adva.nlms.common.networktree.TreeNetworkElement;
import com.adva.nlms.common.networktree.types.APSStatusType;
import com.adva.nlms.common.performance.types.NetworkElementType;
import com.adva.nlms.common.polling.PollingDomainType;
import com.adva.nlms.common.property.FNMPropertyConstants;
import com.adva.nlms.common.property.FNMPropertyFactory;
import com.adva.nlms.common.redundancy.WorkMode;
import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.common.snmp.MIBHelper;
import com.adva.nlms.common.snmp.ManagementStatus;
import com.adva.nlms.common.snmp.NoSuchMDObjectException;
import com.adva.nlms.common.snmp.SNMPTrapsinkStatus;
import com.adva.nlms.common.snmp.SNMPWriteAccessStatus;
import com.adva.nlms.common.traps.FSPGenericTrap;
import com.adva.nlms.common.traps.FSP_NMTraps;
import com.adva.nlms.common.util.ClassUtils;
import com.adva.nlms.common.util.DeviceVersion;
import com.adva.nlms.mediation.bean.provider.api.BeanProvider;
import com.adva.nlms.mediation.common.DiscoveryStateMismatchException;
import com.adva.nlms.mediation.common.ElementManagerSupportResolver;
import com.adva.nlms.mediation.common.MDOperationFailedRuntimeException;
import com.adva.nlms.mediation.common.MDOperationNotSupportedException;
import com.adva.nlms.mediation.config.opticalrouter.config.NetworkElementOpticalRouterDBImpl;
import com.adva.nlms.mediation.infrastructure.concurrent.AdvaExecutors;
import com.adva.nlms.mediation.infrastructure.concurrent.NamedThreadFactory;
import com.adva.nlms.mediation.common.housekeeping.RepositoryManagerException;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.common.persistence.MDPersistenceHelper;
import com.adva.nlms.mediation.common.persistence.MDPersistenceManager;
import com.adva.nlms.mediation.common.persistence.MDTransactional;
import com.adva.nlms.mediation.common.persistence.model.AbstractPersistentObject;
import com.adva.nlms.mediation.common.persistence.model.PersistentObjectHelper;
import com.adva.nlms.mediation.common.persistence.querybuilder.JPAQueryParam;
import com.adva.nlms.mediation.common.serviceProvisioning.NetworkElementSPProperties;
import com.adva.nlms.mediation.common.transactions.CommunicationException;
import com.adva.nlms.mediation.common.transactions.InvalidPollingException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.util.MODefinitionHelper;
import com.adva.nlms.mediation.config.dbconsistency.DBConsistencyCheckFactory;
import com.adva.nlms.mediation.config.entity.IInvPostProcessEventHandler;
import com.adva.nlms.mediation.config.entity.LegacyGeneralPostProcessEventHandlerImpl;
import com.adva.nlms.mediation.config.entity.ManagedObjectSearchStrategy;
import com.adva.nlms.mediation.config.entity.factory.DBObjectFactory;
import com.adva.nlms.mediation.config.entity.factory.NEDBObjectFactory;
import com.adva.nlms.mediation.config.entity.gpsreceiver.GPSReceiverPollingParameters;
import com.adva.nlms.mediation.config.f3_efm.NetworkElementF3_EFMDBImpl;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CM_CP_EFMDBImpl;
import com.adva.nlms.mediation.config.model.ModelManager;
import com.adva.nlms.mediation.config.model.controller.DataPollingController;
import com.adva.nlms.mediation.config.model.controller.DataPollingControllerFactory;
import com.adva.nlms.mediation.config.model.controller.IInvDataPollingController;
import com.adva.nlms.mediation.config.mtosi.NetworkElementMTOSIOperations;
import com.adva.nlms.mediation.config.neconfig.CommunicationProtocolsConfiguration;
import com.adva.nlms.mediation.config.neconfig.impl.CommunicationProtocolsConfigurationImpl;
import com.adva.nlms.mediation.config.peermgr.PeerMgr;
import com.adva.nlms.mediation.config.polling.InventoryPollingParameters;
import com.adva.nlms.mediation.config.polling.NeColdStartTimeCheckPollingParameter;
import com.adva.nlms.mediation.config.polling.ScanPeersCommand;
import com.adva.nlms.mediation.config.polling.ScanPeersPollingParameters;
import com.adva.nlms.mediation.config.polling.status.PollingStatusWorker;
import com.adva.nlms.mediation.config.protection.ModuleOSDBExtension;
import com.adva.nlms.mediation.config.protection.ProtectionStatusChangeNotifier;
import com.adva.nlms.mediation.config.sr.SRLogger;
import com.adva.nlms.mediation.config.sr.SRWorker;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.config.transientobjects.TransientObjectHolder;
import com.adva.nlms.mediation.config.transientobjects.TransientObjectHolderBasis;
import com.adva.nlms.mediation.config.trapsink.TrapsinkUtils;
import com.adva.nlms.mediation.config.util.event.EventToDBObjectLinker;
import com.adva.nlms.mediation.config.util.event.EventToDBObjectLinkerImpl;
import com.adva.nlms.mediation.event.AlarmDAO;
import com.adva.nlms.mediation.event.EventCtrl;
import com.adva.nlms.mediation.event.EventDAO;
import com.adva.nlms.mediation.event.EventDBChangeHdlrImpl;
import com.adva.nlms.mediation.event.EventDTO;
import com.adva.nlms.mediation.event.EventMoNotificationHdlr;
import com.adva.nlms.mediation.event.correlation.CorrelTriggerType;
import com.adva.nlms.mediation.event.correlation.CorrelationScheduler;
import com.adva.nlms.mediation.event.message.MessageManager;
import com.adva.nlms.mediation.event.networkElement.NeEventsDBImpl;
import com.adva.nlms.mediation.evtProc.MissedEventEnumContext;
import com.adva.nlms.mediation.evtProc.api.EventProcNotificationHdlr;
import com.adva.nlms.mediation.evtProc.api.EvtProcCtrl;
import com.adva.nlms.mediation.evtProc.definition.Properties;
import com.adva.nlms.mediation.housekeeping.nebackup.core.NEBackupWorker;
import com.adva.nlms.mediation.housekeeping.nebackup.core.RepositoryManagerProvider;
import com.adva.nlms.mediation.housekeeping.nebackup.core.UpdatesRepositoryManager;
import com.adva.nlms.mediation.housekeeping.swupgrade.SoftwareVersionComparator;
import com.adva.nlms.mediation.ne_comm.BadValueException;
import com.adva.nlms.mediation.ne_comm.CliPropertiesService;
import com.adva.nlms.mediation.ne_comm.HTTPPropertiesService;
import com.adva.nlms.mediation.ne_comm.NetworkElementTypeValidator;
import com.adva.nlms.mediation.ne_comm.SNMPCtrl;
import com.adva.nlms.mediation.ne_comm.TrapsinkStatus;
import com.adva.nlms.mediation.ne_comm.cmd.Commander;
import com.adva.nlms.mediation.ne_comm.configuration.snmp.SNMPHandler;
import com.adva.nlms.mediation.ne_comm.configuration.snmp.SNMPPropertiesHdlr;
import com.adva.nlms.mediation.ne_comm.kap.KapCommandParams;
import com.adva.nlms.mediation.ne_comm.snmp.api.CLIPropertiesData;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPAdapter;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPAuthenticationTypeEnum;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCapabilitiesData;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommDownException;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPError;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPPrivacyTypeEnum;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPPropertiesData;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPSecurityLevelEnum;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPTimeOutException;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPVersionEnum;
import com.adva.nlms.mediation.ni.api.NIEODFacade;
import com.adva.nlms.mediation.ni.entity.NetworkElementNIDBImpl;
import com.adva.nlms.mediation.polling.MDPollingFailedException;
import com.adva.nlms.mediation.polling.OwnerType;
import com.adva.nlms.mediation.polling.PollingCtrl;
import com.adva.nlms.mediation.polling.PollingManager;
import com.adva.nlms.mediation.polling.PollingManagerImpl;
import com.adva.nlms.mediation.polling.PollingManagersController;
import com.adva.nlms.mediation.polling.PollingPerformer;
import com.adva.nlms.mediation.polling.PollingType;
import com.adva.nlms.mediation.polling.SimplePollingLogger;
import com.adva.nlms.mediation.polling.api.PF;
import com.adva.nlms.mediation.polling.api.PollingFramework;
import com.adva.nlms.mediation.polling.api.PollingParameters;
import com.adva.nlms.mediation.polling.conf.PollingWaitConfig;
import com.adva.nlms.mediation.polling.monitoring.NetworkElementMonitor;
import com.adva.nlms.mediation.polling.queue.PollingStarter;
import com.adva.nlms.mediation.redundancy.api.HAController;
import com.adva.nlms.mediation.security.api.SecurityCtrl;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import com.adva.nlms.mediation.security.api.event.SystemEventLogging;
import com.adva.nlms.mediation.security.api.session.SessionRestHdlr;
import com.adva.nlms.mediation.server.ServerCtrl;
import com.adva.nlms.mediation.server.StatusNotificationBroker;
import com.adva.nlms.mediation.server.spring.IDependencyFactory;
import com.adva.nlms.mediation.server.state.api.ServerState;
import com.adva.nlms.mediation.server.state.api.ServerStateAPI;
import com.adva.nlms.mediation.sm.SMNotificationHelper;
import com.adva.nlms.mediation.sm.ServiceManagerFacade;
import com.adva.nlms.mediation.sm.model.AbstractConnectionDBImpl;
import com.adva.nlms.mediation.sm.prov.OpticalChannelService;
import com.adva.nlms.mediation.sm.prov.TransportService;
import com.adva.nlms.mediation.sm.utils.connections.ConnectionUtil;
import com.adva.nlms.mediation.synchronization.discovery.SyncDiscovery;
import com.adva.nlms.mediation.synchronization.discovery.SyncEventBundle;
import com.adva.nlms.mediation.synchronization.discovery.gnss.GnssAssuranceHdlr;
import com.adva.nlms.mediation.synchronization.discovery.trigger.SyncDiscoveryTriggerProp;
import com.adva.nlms.mediation.synchronization.discovery.trigger.SyncDiscoveryTriggerType;
import com.adva.nlms.mediation.synchronization.topology.SyncTopologyGraphChangeListener;
import com.adva.nlms.mediation.synchronization.util.SyncEventUtil;
import com.adva.nlms.mediation.topology.ExtLayersHdlr;
import com.adva.nlms.mediation.topology.LineDBImpl;
import com.adva.nlms.mediation.topology.LineDao;
import com.adva.nlms.mediation.topology.LineHdlrLocal;
import com.adva.nlms.mediation.topology.MDNEDeletionNotAllowedException;
import com.adva.nlms.mediation.topology.ProtLineDBImpl;
import com.adva.nlms.mediation.topology.SubnetHdlrLocal;
import com.adva.nlms.mediation.topology.TimingLineDBImpl;
import com.adva.nlms.mediation.topology.TopologyNodeHdlrLocal;
import com.adva.nlms.mediation.topology.TopologyNodeImpl;
import com.adva.nlms.mediation.topology.UnsupportedNetworkElementsHandler;
import com.adva.nlms.mediation.topology.core.notifications.TopologyChangeEventSender;
import com.adva.nlms.mediation.topology.eod.NetworkElementHandler;
import com.adva.nlms.mediation.topology.ethernet.ring.model.EthernetRingPathElementDBImplDAO;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterDeviceInfo;
import com.adva.topology.manager.api.dto.TopologyNodeDTO;
import jakarta.persistence.EntityExistsException;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityNotFoundException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.snmp4j.smi.VariableBinding;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import static com.adva.nlms.mediation.polling.PollingType.SW_VERSION_CHECK;

/**
 * Servant for network element class, including database transaction control.
 * <br>For documentation
 *
 * @see NetworkElementImpl
 */
public abstract class NetworkElementImpl extends TopologyNodeImpl implements NetworkElement {

  private static final Logger log = LoggerFactory.getLogger(NetworkElementImpl.class);
  private static final Logger logWarn = LoggerFactory.getLogger("com.adva.nlms.mediation.configWarn");
  private static final Logger dcnLogger = LoggerFactory.getLogger("dcnfeature");
  private static final String SNMP_COMMUNICATION_DOWN_MSG = "SNMP communication down for ";
  private static final boolean HTTP_PROPERTIES_EFFECTIVE = true;
  private static final long _15SEC = 15000; // 15 * 1000ms
  private static final long _3MIN = 180000; // 3 * 60 * 1000ms
  private static final long _5MIN = 300000; // 5 * 60 * 1000ms
  private static final long _15MIN = 900000; // 15 * 60 * 1000ms

  protected static final boolean LICENSE_CHECK_ENABLED =
      FNMPropertyFactory.getPropertyAsBoolean(FNMPropertyConstants.LICENSE_CHECK_ENABLED, FNMPropertyConstants.LICENSE_CHECK_ENABLED_DEFAULT);

  protected static final boolean EOD_EVOLUTION_ENABLED = FNMPropertyFactory.getPropertyAsBoolean(FNMPropertyConstants.EOD_EVOLUTION_ENABLED,
                                                                         FNMPropertyConstants.EOD_EVOLUTION_ENABLED_DEFAULT);
  private TopologyChangeEventSender topologyChangeEventSender;

  public enum SNMPCtrlInitStatus {
    NOTRUN(0),
    INPROGRESS(1),
    INIT_FALSE(2),
    INIT_TRUE(3);

    private final int value;

    SNMPCtrlInitStatus(final int value) {
      this.value = value;
    }

    public int getValue() {
      return value;
    }
  }
  public ServerCtrl getServerCtrl() {
    return serverCtrl;
  }

  private TrapsinkUtils trapsinkUtils;
  protected NetworkElementHdlrLocal networkElementHdlrLocal;
  private SubnetHdlrLocal subnetHdlrLocal;
  protected TopologyNodeHdlrLocal topologyNodeHdlrLocal;
  private ServiceManagerFacade serviceManagerFacade;
  private ExtLayersHdlr extLayersHdlr;
  protected LineHdlrLocal lineHdlrLocal;
  protected NetworkElementFactory networkElementFactory;
  private NetworkElementDBImplFactory networkElementDBImplFactory;
  protected ModelManager modelManager;
  protected EntityDAO entityDAO;
  private EntityCRUD entityCRUD;
  protected ManagedObjectDAO managedObjectDAO;
  protected ModuleDAO moduleDAO;
  protected LineDao lineDao;
  protected SecurityCtrl securityCtrl;
  SystemEventLogging systemEventLogg;
  protected StatusNotificationBroker statusNotificationBroker;
  protected EventProcNotificationHdlr eventProcNotificationHdlr;
  protected EventDBChangeHdlrImpl eventDBChangeHdlr;
  private PollingManagersController pollingManagersController;
  private ConnectionUtil connectionUtil;
  private RepositoryManagerProvider repositoryManagerProvider;
  private GnssAssuranceHdlr gnssAssuranceHdlr;
  private UnsupportedNetworkElementsHandler unsupportedNetworkElementsHandler;
  private CapabilitiesRegistry capabilitiesRegistry;
  private SNMPPropertiesHdlr snmpPropertiesHdlr;
  private SNMPHandler snmpHandler;
  protected HTTPPropertiesService httpPropertiesService;
  private ServerStateAPI serverState;
  private String cachedCurrentSW = "";
  private EthernetRingPathElementDBImplDAO ethernetRingPathElementDBImplDAO;
  private CliPropertiesService cliPropertiesService;

  /**
   * SNMP response status.
   */
  protected ResponseStatus responseStatusType = ResponseStatus.RESPONSESTATUS_UNKNOWN;

  /**
   * Object to synchronize access to the SNMP control.
   */
  private final Object snmpCtrlSynchronizer = new Object();

  /**
   * The ID of the containing subnet.
   */
  protected int subnetID;

  /**
   * Indicates whether the network element was already discovered. This is a cached database value!
   */
  private boolean isDiscovered = false;

  /**
   * Indicates whether the network element is removing exactly now.
   */
  private boolean deletionInProgress = false;

  /**
   * Indicator for cold start time check.
   */
  private boolean coldStartTimeCheckDone = false;

  /**
   * Reference to the polling Manager of this network element
   */
  protected PollingManager pollingManager;

  /**
   * Generic SNMP control.
   */
  private SNMPCtrl privateSNMPCTRL = null;  // XXX always use method getSNMPCtrl() to access this!

  protected SynchronizationContainer createSynchronizationContainer() {
    return new SynchronizationContainer();
  }

  /**
   * map of peer network element for EFM managed devices (e.g. f3, HN, CP).
   */
  private final PeerMgr peerMgr;

  private NEIdentity neIdentity = null;

  //Cached DB index. This is a representation of snmp NE index. For CM->neINdex and for HN->deviceID.
  private int neIndex;

  private int initSnmpState = SNMPCtrlInitStatus.NOTRUN.getValue();

  /**
   * Maximum difference between network element time and system time.
   */
  public static final int MAX_NE_TIME_DIFFERENCE;


  private final Object createPeerSyncObject = new Object();

  /**
   * Object to synchronize resync.
   */
  private final Lock resyncLock = new ReentrantLock();

  /* Default value for save config time period  */
  private static final int DEFAULT_SAVECONFIG_TIME_PERIOD = 60;

  /**
   * Extended time of saveConfig() operation. Used only for test purpose. *
   */
  public static final int EXTENDED_SAVECONFIG_TIME = FNMPropertyFactory.getPropertyAsInt(FNMPropertyConstants.EXTENDED_SAVECONFIG_TIME, 0);

  /**
   * Indicator for suppressing initial polling during server start
   */
  private static final boolean INITIAL_POLLING_SUPPRESS = FNMPropertyFactory.getPropertyAsBoolean(FNMPropertyConstants.INITIAL_POLLING_SUPPRESS, false);

  /**
   * Indicator for using saving configuration functionality on the device.
   */
  public static final boolean USE_SAVE_CONFIG = FNMPropertyFactory.getPropertyAsBoolean(FNMPropertyConstants.SAVE_CONFIG, true);

  /**
   * Name of the tab/queue where messages will written during discovery process (in message bar)
   */
  public static final String DISCOVERY_MSG_QUEUE = "Discovery";

  public static final String INVENTORY_MSG_QUEUE = "Inventory";

  private boolean firstDiscoveryInProgress;


  //flag indicates whether save config currently running
  private boolean isSaveConfigRunning = false;

  private PollingStatusWorker pollingStatusWorker;

  private NEBackupWorker neBackupWorker;

  private NetworkElementPersistenceHelper persistenceHelper;

  private EventToDBObjectLinker eventHelper;
  private CorrelationScheduler correlationScheduler;
  protected ServerCtrl serverCtrl;
  protected IDependencyFactory dependencyFactory;
  protected PortDAO portDAO;
  private DBConsistencyCheckFactory dbConsistencyCheckFactory;
  private NeResponseStateHdlr neResponseStateHdlr;
  private NEDBObjectFactory nedbObjectFactory;
  public PortDbImplInstance portFactory;

  private CommunicationProtocolsConfiguration communicationProtocolsConfiguration;

  /**
   * A controller responsible for handling changed data polling (bulk polling and event handling)
   */
  protected DataPollingController dataController;

  private DBObjectFactory dbObjectFactory;

  /**
   * Chain of post process event handlers
   */
  private List<IInvPostProcessEventHandler> postProcessEventHandlers;

  private NetworkElementMTOSIOperations mtosiWorker;
  private NeEntityDescWorker neEntityDescWorker;
  private NetworkElementPropertiesWorker propertiesWorker;
  private NetworkElementPollingWorker pollingWorker;
  private NetworkElementTypeValidator neTypeValidator;
  private NetworkElementNameHdlr neNameHdl;
  private NetworkElementConfigurationHdl neConfigHdl;
  private SRWorker srWorker;
  private EventMoNotificationHdlr eventMoNotificationHdlr;
  protected EvtProcCtrl evtProcCtrl;
  private MessageManager messageManager;
  protected EventCtrl eventCtrl;

  /**
   * cached ne type from NE DB impl
   */
  private int networkElementType;

  private final ReentrantLock responseStatusLock = new ReentrantLock();

  private PollingWaitConfig pollingWaitConfig;

  private AlarmDAO alarmDao;

  private long lastInitSnmpCtrlTimestamp = 0;

  private long lastResponseStatusUpdateToRespondingTimestamp = 0;

  private long lastDcnClearTimestamp = 0;

  private final DeviceLicenseHelper deviceLicenseHelper = new DeviceLicenseHelper();

  private NIEODFacade niEodFacade;

  static {
    int maxNeTimeDiffDefault = 60000;
    try {
      maxNeTimeDiffDefault = Integer.parseInt(Resources.get("MAX_NE_TIME_DIFFERENCE"));
    }catch(Exception e){
    }
    MAX_NE_TIME_DIFFERENCE = maxNeTimeDiffDefault;
  }

  private NetworkElementImpl(ConfigCtrl configCtrl, final int id, final NetworkElementBeanContainer networkElementBeanContainer) {
    super(configCtrl, id);
    initData();
    initBeans(networkElementBeanContainer);
    peerMgr = new PeerMgr();
  }

  /**
   * Sets all needed references
   *  @param id       The identifier of persistent network element implementation.
   * @param subnetID The subnet.
   * @param networkElementBeanContainer
   */
  protected NetworkElementImpl(ConfigCtrl configCtrl, final int id, final int subnetID,
                               final SubnetHdlrLocal subnetHdlrLocal,
                               final ServiceManagerFacade serviceManagerFacade,
                               final NetworkElementPersistenceHelper networkElementPersistenceHelper,
                               NetworkElementBeanContainer networkElementBeanContainer) {

    this(configCtrl, id, networkElementBeanContainer);
    this.subnetID = subnetID;
    this.subnetHdlrLocal = subnetHdlrLocal;
    this.serviceManagerFacade = serviceManagerFacade;
    this.persistenceHelper = networkElementPersistenceHelper;
  }

  private void initData(){
    portFactory = DefaultPortFactory.getInstance().getPortDBImplFactory(this);
    trapsinkUtils = new TrapsinkUtils();
  }

  private void initBeans(NetworkElementBeanContainer networkElementBeanContainer){
    if(networkElementBeanContainer != null) {
      networkElementHdlrLocal = networkElementBeanContainer.getNetworkElementHdlrLocal();
      topologyNodeHdlrLocal = networkElementBeanContainer.getTopologyNodeHdlrLocal();
      extLayersHdlr = networkElementBeanContainer.getExtLayersHdlr();
      lineHdlrLocal = networkElementBeanContainer.getLineHdlrLocal();
      networkElementFactory = networkElementBeanContainer.getNetworkElementFactory();
      networkElementDBImplFactory = networkElementBeanContainer.getNetworkElementDBImplFactory();
      modelManager = networkElementBeanContainer.getModelManager();
      entityDAO = networkElementBeanContainer.getEntityDAO();
      entityCRUD = networkElementBeanContainer.getEntityCRUD();
      managedObjectDAO = networkElementBeanContainer.getManagedObjectDAO();
      moduleDAO = networkElementBeanContainer.getModuleDAO();
      lineDao = networkElementBeanContainer.getLineDao();
      securityCtrl = networkElementBeanContainer.getSecurityCtrl();
      systemEventLogg = networkElementBeanContainer.getSystemEventLogg();
      statusNotificationBroker = networkElementBeanContainer.getStatusNotificationBroker();
      eventProcNotificationHdlr = networkElementBeanContainer.getEventProcNotificationHdlr();
      eventDBChangeHdlr = networkElementBeanContainer.getEventDBChangeHdlr();
      pollingManagersController = networkElementBeanContainer.getPollingManagersController();
      connectionUtil = networkElementBeanContainer.getConnectionUtil();
      repositoryManagerProvider = networkElementBeanContainer.getRepositoryManagerProvider();
      gnssAssuranceHdlr = networkElementBeanContainer.getGnssAssuranceHdlr();
      unsupportedNetworkElementsHandler = networkElementBeanContainer.getUnsupportedNetworkElementsHandler();
      capabilitiesRegistry = networkElementBeanContainer.getCapabilitiesRegistry();
      snmpPropertiesHdlr = networkElementBeanContainer.getSnmpPropertiesHdlr();
      snmpHandler = networkElementBeanContainer.getSnmpHandler();
      httpPropertiesService = networkElementBeanContainer.getHttpPropertiesService();
      serverState = networkElementBeanContainer.getServerState();
      correlationScheduler = networkElementBeanContainer.getCorrelationScheduler();
      serverCtrl = networkElementBeanContainer.getServerCtrl();
      dependencyFactory = networkElementBeanContainer.getDependencyFactory();
      portDAO = networkElementBeanContainer.getPortDAO();
      dbConsistencyCheckFactory = networkElementBeanContainer.getDbConsistencyCheckFactory();
      neResponseStateHdlr = networkElementBeanContainer.getNeResponseStateHdlr();
      nedbObjectFactory = networkElementBeanContainer.getNedbObjectFactory();
      alarmDao = networkElementBeanContainer.getAlarmDao();
      this.pollingWaitConfig = networkElementBeanContainer.getPollingWaitConfig();
      ethernetRingPathElementDBImplDAO = networkElementBeanContainer.getEthernetRingPathElementDBImplDAO();
      cliPropertiesService = networkElementBeanContainer.getCliPropertiesService();

      //prototype scope
      eventHelper = createEventToDBObjectLinker();
      communicationProtocolsConfiguration = createCommunicationProtocolsConfiguration(networkElementBeanContainer);
      eventMoNotificationHdlr = networkElementBeanContainer.getEventMoNotificationHdlr();
      evtProcCtrl = networkElementBeanContainer.getEvtProcCtrl();
      messageManager = networkElementBeanContainer.getMessageManager();
      eventCtrl = networkElementBeanContainer.getEventCtrl();

      niEodFacade = networkElementBeanContainer. getNiEodFacade();

      topologyChangeEventSender = networkElementBeanContainer.getTopologyChangeEventSender();
    }
  }

  private EventToDBObjectLinker createEventToDBObjectLinker(){
    return new EventToDBObjectLinkerImpl();
  }

  protected CommunicationProtocolsConfiguration createCommunicationProtocolsConfiguration(NetworkElementBeanContainer networkElementBeanContainer){
    return new CommunicationProtocolsConfigurationImpl(networkElementBeanContainer.getSecureProtocolsWorker());
  }

  @MDPersistenceContext
  public void postInit(PollingCtrl.PollingManagerInitialization pollingManagerInitialization) {
    NetworkElementDBImpl neDBImpl = neDBImpl();
    //-----PM v.2.0-----// perfObjHdlr = new PerformanceObjectHdlr(this);
    // cache DB values!
    this.isDiscovered = neDBImpl.isDiscovered();
    this.neIndex = neDBImpl.getNeIndex();


    neTypeValidator.initialize();
    trapsinkUtils.resetTrapsinkRegistrationStatus(this);
    initializePeerManager(neDBImpl);

    // create or recreate polling manager!
    synchronized (this) {
      if (pollingManager == null) {
        final long[] pollingTypes = getPollingWorker().getConfigurablePollings();
        final PollingManager superDomainPollingManager = subnetHdlrLocal.getPollingManager(subnetID);
        int pollingManagerID = neDBImpl.getPollingManagerID();

        if (pollingManagerID == 0) {
          setPollingManagerInNe(pollingManagerInitialization, neDBImpl, pollingTypes, superDomainPollingManager);
        } else {
          // recreate polling manager!
          pollingManager = pollingManagersController.recreatePollingManager(pollingManagerID,
                  pollingTypes,
                  PollingDomainType.NE.getValue(),
                  this,
                  getID(),
                  superDomainPollingManager, pollingWaitConfig);
        }
      }
    }
    //Be aware that order of DbObjectFactory creation and postProcessEventHandlers initialization is important.
    //Some of IPostProcessEventHandler needs reference to DBObjectFactory
    this.dbObjectFactory = nedbObjectFactory.getDBObjectFactory(neDBImpl().getNetworkElementType());
    this.postProcessEventHandlers = initPostProcessEventHandlers();

    getPollingWorker().initializePollingCommands();

    this.dataController = DataPollingControllerFactory.getInstance(this);

    communicationProtocolsConfiguration.refresh(this);
  }

  @Override
  public void activate() {
    log.info("NetworkElement.{}.activate()", this);

    // 1.) do database consistency check!
    boolean dbConsitencyCheck = FNMPropertyFactory.getPropertyAsBoolean(FNMPropertyConstants.DB_CONSISTENCY_CHECK_ON_STARTUP, false);
    if (dbConsitencyCheck) {
      dbConsistencyCheckFactory.getInstance(this).fixDBConsistency(this);
    }

    // 2.) special activation after recreation from database!
    //noinspection IfStatementWithNegatedCondition
    if (serverState.getState().before(ServerState.RUNNING))  // just recreating?
    {
      if (hasIpAddress()) {
        PollingFramework.commission(this, PollingType.INIT_SNMP_CTRL);
          if (!isPeer()) {
          PollingFramework.commission(this, PollingType.NE_COLD_START_TIME_CHECK, new NeColdStartTimeCheckPollingParameter(false));
          }
      }
    } else {
      // creating/initializing new network element via SNMP!
      coldStartTimeCheckDone = true;  // already done in discovery!
    }

    // start polling timers!
    if (log.isInfoEnabled()) log.info("Activating polling managers...");
    pollingManager.activate();

    if (hasIpAddress()) {
      // 3.) general activation!
      // register for traps
      evtProcCtrl.register(getIPAddress());
    }
  }

  /**
   * Returns the XML defined network tree node identity
   */
  @Override
  @MDPersistenceContext
  public final TopologyDTO getTopologyDTO(boolean complete) {

    TreeNetworkElement treeNE = new TreeNetworkElement();  // the return value
    NetworkElementDBImpl neDBImpl = neDBImpl();
    treeNE.setId(id);
    treeNE.setParentId(getParentID());
    treeNE.setTopologyNodeType(getType());
    //if ne is peer we don't want gui to have ip address for it. In SubnetHdl->setTopology we have code which depending on this empty peer ip address
    treeNE.setIpAddress(isPeer() ? "" : getIPAddress());
    treeNE.setType(getNetworkElementTypeForMTOSI());
    treeNE.setProductType(neDBImpl.getNetworkElementTypeString());
    checkOptRrtAndAssignSpecificType(treeNE, neDBImpl);

    treeNE.setName(getName());
    treeNE.setDeviceName(getDeviceName());
    treeNE.setObjectAlarmState(complete ? getObjectAlarmState() : new ObjectAlarmState());
    treeNE.setAlarmState(complete ? getAlarmState() : EventSeverity.OK);

    treeNE.setLabel(geNetworkElementLabel());

    treeNE.setSynchronizationState(isDiscovered());
    treeNE.setResponseStatus(
            com.adva.nlms.common.networktree.types.ResponseStatusType.valueOf(getSNMPResponseStatus().getIntValue()));
    if (complete) {
      treeNE.setGroupID(getConfigCtrl().getSecurityCtrl().getGroupIDsForNetworkTreeElement(id));
    }
    treeNE.setAPSStatus(getAPSStatus());
    treeNE.setSwVersion(neDBImpl.getSwUpgradeNeInfo().getCurrentSWVersion());
    treeNE.setSwVersionStandby(neDBImpl.getSwUpgradeNeInfo().getPreviousSWVersion());
    treeNE.setMibVariant(neDBImpl.getMibVariant());

    if(getNetworkElementType() == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_R7){
      if (StringUtils.isBlank(treeNE.getSwVersion())) {
        treeNE.setSupportEMS(true);
      } else {
        treeNE.setSupportEMS(SoftwareVersionComparator.INSTANCE.compare(treeNE.getSwVersion(), "13.1.1") < 1);
      }
    }
      treeNE.setDiscovered(neDBImpl.isDiscovered());
    return treeNE;
  }

  private void checkOptRrtAndAssignSpecificType(TreeNetworkElement treeNE, NetworkElementDBImpl neDBImpl) {
    if (neDBImpl instanceof NetworkElementOpticalRouterDBImpl neOptRtrDBImpl) {
      treeNE.setSpecificType(neOptRtrDBImpl.getSpecificTypeString());
    }
  }

  /**
   * Returns the XML defined network tree node identity
   *
   * @param type
   */
  @Override
  public final TopologyDTO getNTIdentity4UpdateEvent(ConfigChangeType type) {
      TopologyDTO dto;
      if(neDBImpl().isNI()){
        dto = getNIUpdateEvent(type);
      } else {
        dto = new TreeNetworkElement();
        buildTreeNetworkElement(type, (TreeNetworkElement) dto);
      }

      return dto;
  }


  public final TopologyDTO getNIUpdateEvent(ConfigChangeType type) {
    try {
      TreeNetworkElement treeNE = new TreeNetworkElement();
      buildTreeNetworkElement(type, treeNE);
      return treeNE;
    } catch (Exception ex) {
      throw new RuntimeException(ex.getMessage(), ex);
    }
  }

  @MDPersistenceContext
  private void buildTreeNetworkElement(ConfigChangeType type, TreeNetworkElement treeNE) {
    try {
      NetworkElementDBImpl neDBImpl = neDBImpl();
      treeNE.setId(id);
      treeNE.setParentId(getParentID());
      treeNE.setTopologyNodeType(getType());
      treeNE.setIpAddress(isPeer()?"":getIPAddress());
      treeNE.setType(getNetworkElementTypeForMTOSI());
      treeNE.setProductType(neDBImpl.getNetworkElementTypeString());
      checkOptRrtAndAssignSpecificType(treeNE, neDBImpl);
      treeNE.setName(getName());
      treeNE.setDeviceName(getDeviceName());
      treeNE.setNameID(getNameID());
      treeNE.setIdentityType(getIdentityType());
      treeNE.setNodeIdentityType(getNodeIdentityType());
      treeNE.setCustomIconForMap(getCustomIcon());
      treeNE.setContact(getSysContact());
      treeNE.setUserDescr(getUserDescr());
      treeNE.setUserText(getUserText());
      treeNE.setLocation(getSysLocation());
      treeNE.setIdentificationKey(neDBImpl().getIdentificationKey());
      treeNE.setMacAddress(neDBImpl().getMacAddress());
      treeNE.setSerialNumber(neDBImpl().getSerialNumber());
      treeNE.setAlarmState(getAlarmState());
      treeNE.setSynchronizationState(isDiscovered());
      treeNE.setResponseStatus(
        com.adva.nlms.common.networktree.types.ResponseStatusType.valueOf(
          getSNMPResponseStatus().getIntValue()));
      if (type != ConfigChangeType.CHANGED) {
        treeNE.setGroupID(getConfigCtrl().getSecurityCtrl().getGroupIDsForNetworkTreeElement(id));
      }
      treeNE.setAPSStatus(getAPSStatus());
      treeNE.setSwVersion(neDBImpl.getSwUpgradeNeInfo().getCurrentSWVersion());
      treeNE.setSwVersionStandby(neDBImpl.getSwUpgradeNeInfo().getPreviousSWVersion());
      treeNE.setMibVariant(neDBImpl.getMibVariant());
      treeNE.setSupportEMS(ElementManagerSupportResolver.isSupported(getNetworkElementType(), treeNE.getSwVersion()));

      treeNE.setDiscovered(neDBImpl.isDiscovered());
      treeNE.setHiddenModel(NEHiddeManager.getModel(getNetworkElementType(), isHidden()));
      treeNE.setLabel(geNetworkElementLabel());
      if (neDBImpl.getNiSync() != null) {
          treeNE.setNetworkIntelligenceId(neDBImpl.getNiSync().getNiId());
      }
      treeNE.setSuppressEventProcessing(neDBImpl.isEventProcessSuppressed());
    } catch (Exception ex) {
      log.error(String.format("Exception during removal of %s", getIPAddress()));
      throw new RuntimeException(ex.getMessage(), ex);
    }
  }

  /**
   * Cleans up on removal.
   */
  @Override
  public void cleanUp() {
    super.cleanUp();

    if (!isPeer()) {
      if(trapRegistrationSupported())
        unregisterSNMPTrapsink();
      deinitSNMPCtrl();
    }

    getPeerMgr().clearPeers();
    getTopologyNodeHdlrLocal().deleteGraphLayoutForNE(TopologyNodeType.SUBNET, id);
    getSRLogger().removeSRLogs();
  }


  /**
   * Unregisters as SNMP trapsink.
   */
  protected void unregisterSNMPTrapsink() {
    log.info("NetworkElement.{}.unregisterSNMPTrapsink()", this);
    // Make sure, that the SNMPCtrl is not initialized here (SNMP settings are needed). This method
    // is called, after the persistent objects are deleted.
    if (!hasSNMPCtrl()) {
      logWarn.warn("* NetworkElement.{}({}).unregisterSNMPTrapsink(): SNMP control is not present", getIPAddress(), this.getName());
      return;
    }
    try {
      Commander commander = getSNMPCtrl().getCommander();
      if (commander != null) {
        commander.startMonitoringThread();
      } else if (getSNMPCtrl() instanceof com.adva.nlms.mediation.config.driver.DriverSnmpCtrl) {
        log.debug("Driver not supported");
      } else {
        log.debug("Commander not present");
      }
      getSNMPCtrl().unregisterSNMPTrapsink();
    } catch (SNMPCommFailure e) {
      String warnString = "NetworkElement.checkSNMPTrapsinkRegistration() ignored SNMPCommFailure: {}";
      log.info(warnString, e.getErrMessage(), e);
      log.warn(warnString);
    }
  }


  /**
   * Removes the NE, if no connection terminates and if not more than two lines are connected.
   * Two lines will be merged to one and the connections rerouted. A single line will be
   * deleted as well.
   */
  @Override
  @RemoteInterfaceMethod
  public final void remove() throws MDOperationFailedException {

    if (isFirstDiscoveryInProgress())
      throw new MDNEDeletionNotAllowedException("Network Element's " + getName() + " discovery in progress. Cannot delete network element during discovery.");

    // check if this NE isn't currenlty removing
    if (isDeletionInProgress()) {
      log.error("Network element " + getName() + " is already being removed");
      return;
    }

    // check if any peer isn't currenlty removing
    checkIfPeersAreBeingDeleted();

    // set NE and its peers as being deleted, cancel queued and disallow new pollings
    setDeletionInProgress(true);

    try {

      // set possible discovered peers as being deleted, cancel queued and disallow new pollings
      setDeletionInProgress(true);

      if(this.isServiceConnected()) {
        if(this.isEthRingConnected()){
          throw new MDOperationFailedException("Network element " + getName()
                  + " is part of the ring, removing is not allowed");
        }
        throw new MDOperationFailedException("Network element " + getName()
                + " contains a service, removing is not allowed");
      }

      checkNiEodGhostServicePresence();

      //todo delete when lifecyclestate feature will be implemented
      // Skip this for now for pv devices since pv deletes these asynchronously and there is a lot of race conditions involved.
      if (this.isMLorPDServiceConnected()) {
        throw new MDOperationFailedException("Network element " + getName()
                + " contains a service, removing is not allowed");
      }
      // remove Peers
      doRemovePeers();
      // remove NE
      doRemove();

    } finally {
      setDeletionInProgress(false);
    }
  }

  @Override
  public Optional<Boolean> isNiEodGhostServicePresent() {
    return EOD_EVOLUTION_ENABLED && ((neDBImpl() instanceof NetworkElementNIDBImpl neNiDb) && neNiDb.isManagedByNiController())
            ? niEodFacade.isConnectivityServicePresent(neNiDb) : Optional.of(Boolean.FALSE);
  }

  protected void checkNiEodGhostServicePresence() throws MDOperationFailedException {
    Optional<Boolean> optBool = isNiEodGhostServicePresent();
    if (optBool.isPresent()) {
      if (Boolean.TRUE.equals(optBool.get())) {
        throw new MDOperationFailedException(("Network element %s is part of an Installed/Installed Incomplete connectivity service," +
                " removing is not allowed").formatted(getName()));
      }
    } else {
      throw new MDOperationFailedException(("Network element %s is managed by NI but it was not possible to check" +
              " if it is part of an Installed/Installed Incomplete connectivity service. Removing is not allowed.").formatted(getName()));
    }
  }

  protected boolean returnLicensesForDevice() {
    if (NEType.isCustomNEType(this.getNetworkElementType())) return true;
    DeviceLicenseManager licenseManager = BeanProvider.get().getBean(DeviceLicenseManager.class);
    return licenseManager.returnLicensesForDevice(NEType.valueOf(this.getNetworkElementType()), this.getIpAddressNeNameIdentifier());
  }

  protected boolean returnLicensesForEquipment() {
    List<EntityDBImpl> entitiesOnDBList = EntityDAO.getAllEntitiesForNetworkElement(this.getID());
    Map<String, Integer> equipmentCountOnDb = deviceLicenseHelper.getEquipmentCount(entitiesOnDBList, NEType.valueOf(this.getNetworkElementType()));
    DeviceLicenseManager licenseManager = BeanProvider.get().getBean(DeviceLicenseManager.class);
    return licenseManager.returnLicensesForEquipment(equipmentCountOnDb, NEType.valueOf(this.getNetworkElementType()), this.getIpAddressNeNameIdentifier());
  }

  protected boolean doReturnLicenses() {
    return returnLicensesForDevice();
  }

  public boolean returnLicenses() {
    if (LICENSE_CHECK_ENABLED) {
      if (this.isDiscovered) {
        try {
          return doReturnLicenses();
        } catch (Exception ex) {
          String message = MessageFormat.format("Failed to return license {0}.", this.getIpAddressNeNameIdentifier());
          sendNotifyErrorToMessageBar(message);
          log.error(message, ex);
          return false;
        }
      }
    }
    //if property is disabled or ne not yet discovered, then we shouldn't block network element deletion
    return true;
  }

  public boolean isMLorPDServiceConnected(){
    boolean isServiceConnected;
    if(NEUtils.isF3orF4Device(networkElementType)) {
      isServiceConnected = serviceManagerFacade.isPDServiceConnected(getID());
    } else {
      isServiceConnected = serviceManagerFacade.isMLServiceConnected(getID());
    }
    return isServiceConnected;
  }

  private void checkIfPeersAreBeingDeleted() throws MDOperationFailedException {
    if (!isPeer()) {
      final Set<NetworkElement> peers = getPeerMgr().getPeers();

      for (NetworkElement peer : peers) {
        if (peer.isDeletionInProgress())
          throw new MDOperationFailedException("Peer network element " + peer.getName() +
                  " is being removed. Please wait for this operation to finish.");
      }
    }
  }


  protected void doRemovePeers() throws MDOperationFailedException {
    // overrided
  }

  public final void doRemove() throws MDOperationFailedException {
    try  // protect user notification!
    {
      if (!returnLicenses()) {
        throw new MDOperationFailedException("License return to ELS failed");
      }
        SubnetHdlrLocal subnetHdlr = getSubnetHdlrLocal();
      TopologyNodeDTO topologyNodeDTO = NetworkElementHandler.mapToTopologyNodeDTO(neDBImpl());
      //Prepare to update directory structure on the FTP server
      UpdatesRepositoryManager repositoryManager = getAndPrepareForUpdateRepositoryManager();
      UpdatesRepositoryManager repositoryManagerCBM = getAndPrepareForUpdateRepositoryManagerCBM();

      if (log.isInfoEnabled()) log.info(getName() + ".remove()");


      // don't remove the only network element from a subnet
//       if (subnetHdlr.getNEs(subnetID).size() == 1) {
//         throw new RequestFailed(ntProperties.name +
//              " is the only network element, removing is not allowed");
//       }
      // create the update object before deleting
        final List<TopologyChange> topologyChanges = new ArrayList<>();
      // if it gets too complex, update the whole subnet
      doRemovePortReferencesFromLine();
      doRemovePersistence(topologyChanges);
      notifyClientsAboutNERemoved(topologyChanges);
      topologyChangeEventSender.sendNEDeleteEvent(topologyNodeDTO);
      removeLines(topologyChanges, subnetHdlr);
      updateRepositoryManager(repositoryManager);
      updateRepositoryManager(repositoryManagerCBM);

      // update the number of discovered network elements next time this value is requested by mbean
      BeanProvider.getContext().publishEvent(new NEDiscoveredEvent(this.getClass().getName()));

    } catch (RuntimeException e) {
      log.error("", e);
      throw new MDOperationFailedException("Internal error during NE removal: Please see mediation.log for more details", e);
    }
  }

  //Method to remove references from the NE on other line, such as in case of ALM port references on physical lines
  protected void doRemovePortReferencesFromLine() throws MDOperationFailedException {
    //do nothing in the standard case
  }

  @MDPersistenceContext
  private void doRemovePersistence(List<TopologyChange> topologyChanges) throws MDOperationFailedException {
    boolean updateSubnet;
    try {
      final NetworkElementDBImpl neDBImpl = neDBImpl();
      checkIfIsUsedInService();
      // add security event ModTop (Modify Subnet Topology)
      systemEventLogg.addSubnetEvent(neDBImpl.getName(), getParentID(), SystemAction.DeleteNetworkElement);

      updateSubnet = remove(neDBImpl, topologyChanges);

      // update the whole subnet?
      if (updateSubnet) {
        topologyChanges.add(getTopologyNodeHdlrLocal().getTopologyChange(getParentID(), ConfigChangeType.RELOAD));
      }
    } catch (EntityNotFoundException enf) {
      log.warn("EntityNotFoundException has been ignored.", enf);
    } catch (NoSuchMDObjectException e) {
      log.warn("NoSuchMDObjectException has been ignored.", e);
    }
  }

  private void notifyClientsAboutNERemoved(List<TopologyChange> topologyChanges) {
    // client updates
    for (TopologyChange topologyChange : topologyChanges) {
      getTopologyNodeHdlrLocal().pushTopologyChange(topologyChange);
    }

    // remove events and alarms
    eventMoNotificationHdlr.notifyNERemoved(id, getName());
  }


  private UpdatesRepositoryManager getAndPrepareForUpdateRepositoryManager() {
    UpdatesRepositoryManager repositoryManager = null;

    try {
      repositoryManager = repositoryManagerProvider.createUpdatesRepositoryManager();
      repositoryManager.prepareUpdateDeleteNE(this);
    } catch (RepositoryManagerException rme) {
      log.error("Can not prepare for update repository while network element delete" + rme.getMessage(), rme);
    } catch (EntityNotFoundException enf) {
      log.warn("EntityNotFoundException has been ignored.", enf);
    } catch (Throwable t) {
      log.error("NetworkElementImpl.remove() ignored Throwable:", t);
    }

    return repositoryManager;
  }

  private UpdatesRepositoryManager getAndPrepareForUpdateRepositoryManagerCBM() {
    UpdatesRepositoryManager repositoryManagerCBM = null;

    try {
      repositoryManagerCBM = repositoryManagerProvider.createUpdatesRepositoryManagerCBM();
      repositoryManagerCBM.prepareUpdateDeleteNE(this);
    } catch (RepositoryManagerException rme) {
      log.error("Can not prepare for update repository while network element delete" + rme.getMessage(), rme);
    } catch (EntityNotFoundException enf) {
      log.warn("EntityNotFoundException has been ignored.", enf);
    } catch (Throwable t) {
      log.error("NetworkElementImpl.remove() ignored Throwable:", t);
    }

    return repositoryManagerCBM;
  }

  private void updateRepositoryManager(UpdatesRepositoryManager repositoryManager) {
    //update ftp repository directory structure
    if (!isPeer() && repositoryManager != null) {
      try {
        repositoryManager.updateDelete();
      } catch (RepositoryManagerException rme) {
        log.error("Can not prepare for update repository while network element delete" + rme.getMessage(), rme);
      } catch (Throwable t) {
        log.error("NetworkElementImpl.remove() ignored Throwable:", t);
      }
    }
  }

  private void checkIfIsUsedInService() throws MDOperationFailedException {

    //9778 - do not want to delete ne if node is intermediate module...
    if (getConfigCtrl().getServiceManagerCtrl().getServiceManagerFacade().isNetworkElementUsedInService(getID())) {
      throw new MDOperationFailedException("Network element " + getName() +
              " contains a service, removing is not allowed");
    }
  }

  private void removeLines(List<TopologyChange> topologyChanges, SubnetHdlrLocal subnetHdlr) throws MDOperationFailedException {
    for (final TopologyChange topologyChange : topologyChanges) {
      if (topologyChange.type == ConfigChangeType.DELETED &&
              ( topologyChange.nodeType == TopologyNodeType.LINE ||
                topologyChange.nodeType == TopologyNodeType.TIMING_LINE )
              ) {
        subnetHdlr.removeLine(topologyChange.nodeId);
      }
    }
  }

  /**
   * Removes the NE, if no connection terminates and if not more than two lines are connected.
   * Two lines will be merged to one and the connections rerouted. A single line will be
   * deleted as well.
   *
   * @param neDBImpl        The persistent network element.
   * @param topologyChanges The list of topology changes.
   * @return true, if the subnet was updated.
   * @throws MDOperationFailedException The request failed.
   */
  private boolean remove(final NetworkElementDBImpl neDBImpl,
                         final List<TopologyChange> topologyChanges)
          throws MDOperationFailedException {
    RemoveHelper helper;
    TopologyNodeHdlrLocal treeNodeHdlr = getTopologyNodeHdlrLocal();
    SubnetHdlrLocal subnetHdlr = getSubnetHdlrLocal();
    // create update before the NE is deletecd
    final TopologyChange neDeleted = treeNodeHdlr.getTopologyChange(neDBImpl, ConfigChangeType.DELETED);
    // lock sync discovery - so the handling of the discovery will be done exclusively
    // this method can be blocked until current handling of sync discovery trigger is done
    SyncDiscovery.instance().lock();

    // get rid of all pending pollings and wait for running ones to finish
    ((PollingManagerImpl) (networkElementHdlrLocal.getNetworkElement(neDBImpl.getId()).getPollingManager())).discardAllPollings();

    try {
      helper = removeTransaction(neDBImpl, topologyChanges, treeNodeHdlr, neDeleted);

      // Remove transient stuff
      // Problem: trapsink removal needs persistent data -[LJ] this is not actual any more. Remove outside transaction.
      // Method subnetHdlr.removeNE(id) would remove nodeID from treeNodeMap. This need be done at the end (outside transaction) to support rollback loop.
      subnetHdlr.removeNE(id);

    } catch (RuntimeException t) {
      final String errorMessage = "Rollback!! Cannot remove NE. Please see mediation.log for more details or contact with tech support.";
      log.error(errorMessage, t);
      throw t;
    } finally {
      // unlock sync discovery
      // release any other thread waiting to perform ne deleted or other Async synchronization trigger handling
      SyncDiscovery.instance().unlock();
    }

    if (helper.changedLine != null && (helper.changedLine = MDPersistenceHelper.refind(helper.changedLine, helper.changedLine.getId())) != null) {
      try {
        MDPersistenceManager.current().refresh(helper.changedLine);
        extLayersHdlr.discoverOtsAndOms(helper.changedLine);
      } catch (RuntimeException x) {
        log.error(x.toString(), x);
      }
    }

    SyncEventUtil.notifySyncEvent(helper.getDiscoveryEvents());

    return helper.isUpdateSubnet();
  }

  @MDTransactional()
  private RemoveHelper removeTransaction(NetworkElementDBImpl neDB,
                                         List<TopologyChange> topologyChanges,
                                         TopologyNodeHdlrLocal treeNodeHdlr,
                                         TopologyChange neDeleted) throws MDOperationFailedException {
    SyncEventBundle discoveryEvents;
    boolean updateSubnet = false;
    NetworkElementDBImpl neDBImpl = MDPersistenceHelper.find(NetworkElementDBImpl.class, neDB.getId());
    LineDBImpl line0 = null;

      // all lines will be deleted
        final Collection<LineDBImpl> lineDBCollection = neDBImpl.getLines();
        final LineDBImpl[] lines = lineDBCollection.toArray(new LineDBImpl[0]);
        // create the updates before deleting!
        for (final LineDBImpl lineDBImpl : lines) {
          topologyChanges.add(treeNodeHdlr.getTopologyChange(lineDBImpl, ConfigChangeType.DELETED));
          lineHdlrLocal.remove(lineDBImpl);
            PersistentObjectHelper.destroy(lineDBImpl);
        }

      //Delete timing lines associated
      Collection<TimingLineDBImpl> timingLines = lineDao.getAllTimingLinesForNE(neDBImpl.getId());
      if (timingLines !=null && !timingLines.isEmpty() )
      {
        if (log.isInfoEnabled()) {
          log.info("Deleting {} timing line(s)", timingLines.size());
        }
        for (TimingLineDBImpl timingLine : timingLines) {
          topologyChanges.add(treeNodeHdlr.getTopologyChange(timingLine, ConfigChangeType.DELETED));
            PersistentObjectHelper.destroy(timingLine);
        }
      }

      // update was created earlier
      topologyChanges.add(neDeleted);

      // handle Synchronization discovery for deleted NE (handle in the same transaction and by the same thread)
      // the SyncNodes Topology should be effected before NE (and all related objects are actually deleted from DB)
      discoveryEvents =
              SyncDiscovery.instance().trigger(SyncDiscoveryTriggerType.NEDeleted, new SyncDiscoveryTriggerProp(neDBImpl));
      // remove from database.
        PersistentObjectHelper.destroy(neDBImpl);


    return new RemoveHelper(discoveryEvents, updateSubnet, line0);
  }

  //Private class helper for removeTransaction() method
  private class RemoveHelper {
    RemoveHelper(SyncEventBundle discoveryEvents, boolean updateSubnet, LineDBImpl changedLine) {
      this.discoveryEvents = discoveryEvents;
      this.updateSubnet = updateSubnet;
      this.changedLine = changedLine;
    }

    private final boolean updateSubnet;
    private final SyncEventBundle discoveryEvents;
    LineDBImpl changedLine;

    public boolean isUpdateSubnet() {
      return updateSubnet;
    }

    public SyncEventBundle getDiscoveryEvents() {
      return discoveryEvents;
    }
  }

  /**
   * Sets the discovery state.
   */
  public synchronized void setDiscovered() {
    if (!this.isDiscovered()) {
      getPersistenceHelper().updateNetworkElementDBImplEntity(this::markNeAsDiscovered);
      this.isDiscovered = true;
      BeanProvider.getContext().publishEvent(new NEDiscoveredEvent(this.getClass().getName()));
    }
    SyncTopologyGraphChangeListener.instance().addAffectedSubnetIDs(subnetID);
  }
  public void setOpticalRouterDeviceInfo(OpticalRouterDeviceInfo info) {
    getPersistenceHelper().updateNetworkElementDBImplEntity(neDBImpl -> {
      // Update name ID
      if (info.name() != null && !info.name().equals(neDBImpl.getNameID())) {
        neDBImpl.setNameID(info.name());
      }
      // Update serial number
      if (info.serialNumber() != null && !info.serialNumber().equals(neDBImpl.getSerialNumber())) {
        neDBImpl.setSerialNumber(info.serialNumber());
      }

      // Update software version
      if (info.semanticSoftwareVersion() != null && !info.semanticSoftwareVersion().equals(neDBImpl.getSysSwVer())) {
        neDBImpl.setSysSwVer(info.semanticSoftwareVersion());
      }
    });
  }

  protected void markNeAsDiscovered(NetworkElementDBImpl neDb) {
    neDb.setDiscovered(true);
  }

  /**
   * Get NE specific SNMP properties.
   */
  @Override
  public SNMPPropertiesData getSNMPProperties() {
    try {
      return new SNMPPropertiesData(snmpPropertiesHdlr.getSNMPProperties(getID()));
    } catch (NoSuchMDObjectException e) {
      log.error("No SNMP settings for {} defined", this.getName());
      return null;
    }
  }


  /**
   * Returns the effective SNMP settings for the NE.
   *
   * @return the effective SNMP settings for the NE.
   */
  @Override
  public SNMPPropertiesData getEffectiveSNMPProperties() {
    SNMPPropertiesData effectiveSNMPProperties = getSNMPProperties();
    if (!effectiveSNMPProperties.inUse) {
      SmartProtocolsType smartProtocols = SmartProtocolsType.getCurrent();
      boolean smartProtocolsEnabled = smartProtocols == SmartProtocolsType.Enabled;
      if (smartProtocolsEnabled && effectiveSNMPProperties.version == SNMPVersionEnum.VERSION3 && isDiscovered()) {
        effectiveSNMPProperties.version = getCommunicationProtocolsConf().getSupportedSnmpVersion();
      }
    }

    return effectiveSNMPProperties;
  }


  /**
   * Initializes the SNMP properties. Checks the capabilities and sets default values if needed.
   * SNMP controls have to be rebuild next time.
   *
   * @param snmpProperties The SNMP properties.
   * @throws BadValueException Bad value defined in the SNMP properties.
   */
  public void initSNMPProperties(final SNMPPropertiesData snmpProperties)
          throws BadValueException {
    // 1.) validate capabilities!
    final SNMPVersionEnum[] snmpVersionCaps = getNeConfigHdl().getSNMPCapabilities().getSnmpVersions();
    boolean snmpVersionValid = false;

    for (final SNMPVersionEnum snmpVersion : snmpVersionCaps) {
      if (snmpProperties.version == snmpVersion) {
        snmpVersionValid = true;
        break;
      }
    }

    if (!snmpVersionValid)
      snmpProperties.version = snmpVersionCaps[0];


    final SNMPSecurityLevelEnum[] snmpSecurityLevelCaps = getNeConfigHdl().getSNMPCapabilities().getSnmpSecurityLevels();
    boolean snmpSecurityLevelValid = false;

    for (final SNMPSecurityLevelEnum snmpSecurityLevel : snmpSecurityLevelCaps) {
      if (snmpProperties.securityLevel == snmpSecurityLevel) {
        snmpSecurityLevelValid = true;
        break;
      }
    }

    if (!snmpSecurityLevelValid)
      snmpProperties.securityLevel = snmpSecurityLevelCaps[0];


    final SNMPAuthenticationTypeEnum[] snmpAuthenticationTypeCaps = getNeConfigHdl().getSNMPCapabilities().getSnmpAuthenticationTypes();
    boolean snmpAuthenticationTypeValid = false;

    for (final SNMPAuthenticationTypeEnum snmpAuthenticationType : snmpAuthenticationTypeCaps) {
      if (snmpProperties.authType == snmpAuthenticationType) {
        snmpAuthenticationTypeValid = true;
        break;
      }
    }

    if (!snmpAuthenticationTypeValid)
      snmpProperties.authType = snmpAuthenticationTypeCaps[0];


    final SNMPPrivacyTypeEnum[] snmpPrivacyTypeCaps = getNeConfigHdl().getSNMPCapabilities().getSnmpPrivacyTypes();
    boolean snmpPrivacyTypeValid = false;

    for (final SNMPPrivacyTypeEnum snmpPrivacyType : snmpPrivacyTypeCaps) {
      if (snmpProperties.privType == snmpPrivacyType) {
        snmpPrivacyTypeValid = true;
        break;
      }
    }

    if (!snmpPrivacyTypeValid)
      snmpProperties.privType = snmpPrivacyTypeCaps[0];


    // 2.) set properties!
    setSNMPProperties(snmpProperties);
  }


  /**
   * Sets the SNMP properties.
   * SNMP controls have to be rebuild next time.
   */
  public void setSNMPProperties(final SNMPPropertiesData snmpProperties)
          throws BadValueException {
    if (log.isDebugEnabled()) log.debug(snmpProperties.toString());
    final boolean propertiesChanged;
    propertiesChanged = setSNMPPropertiesTransaction(snmpProperties);

    if (propertiesChanged)
    {
      gnssAssuranceHdlr.saveNeDataAssync(neDBImpl(), !getSNMPResponseStatus().equals(ResponseStatus.RESPONDING));
    }

    // Notify SNMP stack.
    if (propertiesChanged && hasSNMPCtrl()) {
      try {
        SNMPPropertiesData snmpPropertiesData = getEffectiveSNMPProperties();
        updateSNMPPropertiesData(snmpPropertiesData);
        getSNMPAdapter().notifySnmpSettingsChanged(snmpPropertiesData);
        clearSNMPCtrlMap();
        if (log.isInfoEnabled())
          log.info("new communities: {\"" + snmpProperties.snmpGetCommunity + "\", \"" + snmpProperties.snmpSetCommunity + "\"} for " + this);
      } catch (SNMPCommFailure snmpCommFailure) {
        snmpCommFailure.printStackTrace();
        log.error("* SNMPCommFailure: " + snmpCommFailure.getErrMessage() + " *");
      }
    }
    if (propertiesChanged){
      getTopologyNodeHdlrLocal().pushTopologyChange(getID(), ConfigChangeType.CHANGED);
    }
  }

  protected void updateSNMPPropertiesData(SNMPPropertiesData snmpPropertiesData)
  {
    //we do not need to do nothing in most cases
  }

  @MDTransactional
  protected boolean setSNMPPropertiesTransaction(final SNMPPropertiesData snmpProperties) throws BadValueException {
    final boolean propertiesChanged;
    try {
      if (neConfigHdl == null) {
        neConfigHdl = new NetworkElementConfigurationHdl(this);
      }
      final SNMPCapabilitiesData snmpCapabilities = neConfigHdl.getSNMPCapabilities();
      propertiesChanged = neDBImpl().setSNMPProperties(snmpProperties, snmpCapabilities);
      if(FNMPropertyFactory.getPropertyAsBoolean(FNMPropertyConstants.USE_SNMP_FOR_REST, FNMPropertyConstants.USE_SNMP_FOR_REST_DEFAULT) && neDBImpl().getNetworkElementType() == 7)
        setHTTPPropertiesOnSNMPPropertiesChange(snmpProperties);
      return propertiesChanged;
    } catch (BadValueException bv) {
      log.warn("* NetworkElement.setSNMPProperties(): " + bv.getMessage() + " *");
      throw bv;
    }
  }

  private void setHTTPPropertiesOnSNMPPropertiesChange(SNMPPropertiesData snmpProperties) {
    if(snmpProperties.version.equals(SNMPVersionEnum.VERSION3) && !snmpProperties.user.isEmpty() && !snmpProperties.authPassword.isEmpty()) {
      HTTPPropertiesDTO httpDTO = httpPropertiesService.getHTTPProperties(neDBImpl().getId(), HTTP_PROPERTIES_EFFECTIVE);
      httpDTO.setUseGlobal(false);
      httpDTO.setUsername(snmpProperties.user);
      httpDTO.setPassword(snmpProperties.authPassword);
      httpPropertiesService.updateHTTPProperties(neDBImpl().getId(), httpDTO);
    }
  }

  /**
   * Sets the SNMP properties.
   * SNMP controls have to be rebuild next time.
   *
   * @throws MDOperationFailedException throw exception if request fails.
   */
  public void resetSNMPSession() throws MDOperationFailedException {
    try {
      getSNMPAdapter().resetSNMPSession(getIPAddress(), getSNMPProperties());
    } catch (SNMPCommFailure snmpCommFailure) {
      throw new MDOperationFailedException(snmpCommFailure.getErrMessage(), snmpCommFailure); //SNMP not yet initialized.
    }

    //Inform the GNSS assurance service in order to clear the SNMP session cache
    gnssAssuranceHdlr.neResetSNMPSessionAssync(neDBImpl());
  }


  protected List<IInvPostProcessEventHandler> initPostProcessEventHandlers() {
    List<IInvPostProcessEventHandler> iPostProcessEventHandlers = new ArrayList<>();
    iPostProcessEventHandlers.add(new LegacyGeneralPostProcessEventHandlerImpl(getConfigCtrl()));
    return iPostProcessEventHandlers;
  }

  protected void registerEventHandlersToDBObjectLinker(List<? extends IInvPostProcessEventHandler> iPostProcessEventHandlers) {
    // register event handlers in event to db object linker to make possible searching for those events objects in db.
    for (IInvPostProcessEventHandler eventHdlr : iPostProcessEventHandlers) {
      if (eventHdlr instanceof ManagedObjectSearchStrategy) {
        getEventToDBObjLinker().registerManagedObjectSearchStrategy((ManagedObjectSearchStrategy) eventHdlr);
      }
    }
  }

  /**
   * Sets a value from an event in the database.
   *
   * @param event The event.
   * @throws NoSuchMDObjectException The entity doesn't exist.
   */
  @Override
  @MDPersistenceContext
  public void setEntityValueInDB(final EventDTO event) throws NoSuchMDObjectException {

    if (!event.isCreatedByPolling()) {
      final EntityImpl entityImpl = getEntityImpl(event.objectIndex);
      dataController.setDataInDB(entityImpl, event);
    }
  }

  /**
   * Returns The mib variant.
   *
   * @return mib variant.
   */
  @Override
  public int getMIBVariantFromSNMP() {
    final int mibVariantFromDB = getPersistenceHelper().getMIBVariantFromDB();
    try {
      final int mibVariantFromSNMP = getSNMPCtrl().getMIBVariant();
      if (mibVariantFromDB != mibVariantFromSNMP)//check if database mibVariant is different then on NE.
        getPersistenceHelper().setMIBVariantInDB(mibVariantFromSNMP);
      return mibVariantFromSNMP; //return actual mibVariant.
    } catch (SNMPCommFailure | MDOperationFailedException e) {
      // unrecognized mibVariant return 0
    }
    return mibVariantFromDB; //return database mibVariant when sth goes wrong(SNMPCommFailure, RequestFailed).
  }

  @Override
  public int getMIBVariantFromDB() {
    return getPersistenceHelper().getMIBVariantFromDB();
  }

  /**
   * Resynchronizes the data of an entity. If some values have changed, an event
   * will be send to the GUI.
   * XXX Never use the resync method of an entity directly, use this method
   * instead!
   *
   * @param entityDBImpl       The persistent entity.
   * @param equippedTypeString The string representation of the equipped type.
   * @param dataMap            The data map.
   */
  protected void resync(final EntityDBImpl entityDBImpl, final String equippedTypeString, final Map<String, Object> dataMap) {

    EntityUpdaterFactory.getInstance().getEntityUpdater(entityDBImpl).resync(entityDBImpl,new SnmpEntityAbstract( dataMap));

    // resync equippedTypeString!
    if (equippedTypeString != null) {
      if (entityDBImpl instanceof EquipmentDBImpl) {
        final EquipmentDBImpl equipmentDBImpl = (EquipmentDBImpl) entityDBImpl;

        if (!equippedTypeString.equals(equipmentDBImpl.getEquippedTypeString())) {
          if (log.isInfoEnabled())
            log.info(this + ".resync(" + entityDBImpl + ", dataMap[" + dataMap.size() + "]): equippedTypeString = \"" + equipmentDBImpl.getEquippedTypeString() + "\" -> \"" + equippedTypeString + "\"");

          equipmentDBImpl.setEquippedTypeString(equippedTypeString);
        }
      }
    }
  }

  /**
   * Updates an existing entity object. If not existing a new one will be
   * created.
   */
  @Override
  public void resyncEntity(final EntityIndex index, final String serialNum, final Map<String, Object> dataMap, SynchronizationContainer synchronizationContainer) {

    EntityDBImpl entityDBImpl;

    try {
      entityDBImpl = entityDAO.getEntityDBImplOrThrowException(getID(), index);

      // update already existing entity!
      resync(entityDBImpl, null /*typeString*/, dataMap);
    } catch (NoSuchEntityException e) {
      // entity not yet existing -> create new!
      final NetworkElementDBImpl neDBImpl = neDBImpl();

      entityDBImpl = new EntityDBImpl(neDBImpl.getId());
      EntityUpdaterFactory.getInstance().getEntityUpdater(entityDBImpl).initialize(entityDBImpl,new SnmpEntityAbstract( dataMap));
    }

    // add discovered entity to list!
    synchronizationContainer.add(entityDBImpl);
  }


  /**
   * Updates an existing rack object. If not existing a new one will be
   * created.
   */
  @Override
  public void resyncRack(final EntityIndex index, final String serialNum, final Map dataMap, SynchronizationContainer synchronizationContainer) {

    final NetworkElementDBImpl neDBImpl = neDBImpl();
    final int entityClass = MIBHelper.getEntityClass(dataMap);
    RackDBImpl rack = (RackDBImpl) entityDAO.getByEntityIdx(getID(), index, entityClass, serialNum);

    if (rack == null) {
      // create new rack!
      rack = new RackDBImpl(neDBImpl.getId(), "Rack " + MIBHelper.getEntityIndex(dataMap));
      EntityUpdaterFactory.getInstance().getEntityUpdater(rack).initialize(rack,new SnmpEntityAbstract( dataMap));
    } else {
      // nothing to do yet!
      resync(rack, null /*typeString*/, dataMap);
    }

    // add discovered entity to list!
    synchronizationContainer.add(rack);
  }


  /**
   * Updates an existing shelf object. If not existing a new one will be
   * created.
   */
  @Override
  public void resyncShelf(final EntityIndex index, final String serialNum, final Map<String, Object> dataMap, SynchronizationContainer synchronizationContainer) {

    final NetworkElementDBImpl neDBImpl = neDBImpl();
    final int entityClass = MIBHelper.getEntityClass(dataMap);
    ShelfDBImpl shelf = (ShelfDBImpl) entityDAO.getByEntityIdx(getID(), index, entityClass, serialNum);

    if (shelf == null) {
      // create new shelf!
      shelf = new ShelfDBImpl(neDBImpl.getId());
      EntityUpdaterFactory.getInstance().getEntityUpdater(shelf).initialize(shelf,new SnmpEntityAbstract( dataMap));
    } else {
      resync(shelf, null /*typeString*/, dataMap);
    }

    // add discovered entity to list!
    synchronizationContainer.add(shelf);
  }


  /**
   * Updates an existing slot object. If not existing a new one will be
   * created.
   */
  @Override
  public void resyncSlot(final EntityIndex index, final String serialNum, final Map<String, Object> dataMap, SynchronizationContainer synchronizationContainer) {
    EntityDBImpl entityDBImpl;

    try {
      entityDBImpl = entityDAO.getEntityDBImplOrThrowException(getID(), index);

      // XXX check classes of DB objects; if different, then discovered entity is not entityDBImpl but new Slot(neDBImpl(), dataMap)

      // update already existing entity!
      resync(entityDBImpl, null /*typeString*/, dataMap);
    } catch (NoSuchEntityException e) {
      // entity not yet existing -> create new!
      entityDBImpl = new SlotDBImpl(getID());
      EntityUpdaterFactory.getInstance().getEntityUpdater(entityDBImpl).initialize(entityDBImpl,new SnmpEntityAbstract( dataMap));
    }

    // add discovered entity to list!
    synchronizationContainer.add(entityDBImpl);
  }


  /**
   * Updates an existing fan object. If not existing a new one will be
   * created.
   */
  @Override
  public void
  resyncFan(final EntityIndex index, final String serialNum, final Map<String, Object> dataMap, SynchronizationContainer synchronizationContainer) {

    final EntityIndex containedIn = MIBHelper.getEntityContainedIn(dataMap);
    final EntityDBImpl containigEntityDBImpl = synchronizationContainer.get(containedIn);
    String typeString = "Fan Unit";

    if (containigEntityDBImpl instanceof SlotDBImpl) {
      final SlotDBImpl slot = (SlotDBImpl) synchronizationContainer.get(containedIn);
      final ShelfDBImpl shelf = (ShelfDBImpl) synchronizationContainer.get(slot.getContainedIn());

      typeString += " in " + shelf.getEquippedTypeString();
    }

    resyncFan(index, serialNum, dataMap, typeString, synchronizationContainer);
  }

  public final void
  resyncFan(final EntityIndex index, final String serialNum, final Map<String, Object> dataMap, final String typeString, SynchronizationContainer synchronizationContainer) {
    final int entityClass = MIBHelper.getEntityClass(dataMap);
    FanDBImpl fan = (FanDBImpl) entityDAO.getByEntityIdx(getID(), index, entityClass, serialNum);

    if (fan == null) {
      // create new fan!
      fan = new FanDBImpl(getID(), typeString);
      EntityUpdaterFactory.getInstance().getEntityUpdater(fan).initialize(fan,new SnmpEntityAbstract( dataMap));
    } else {
      resync(fan, typeString, dataMap);
    }

    // add discovered entity to list!
    synchronizationContainer.add(fan);
  }


  /**
   * Updates an existing power supply object. If not existing a new one will be
   * created.
   */
  @Override
  public void resyncPowerSupply(final EntityIndex index,
                                final String serialNum,
                                final Map dataMap,
                                final int powerSupplyNo, SynchronizationContainer synchronizationContainer) {

    final NetworkElementDBImpl neDBImpl = neDBImpl();
    final int entityClass = MIBHelper.getEntityClass(dataMap);
    PowerSupplyDBImpl powerSupply = (PowerSupplyDBImpl) entityDAO.getByEntityIdx(getID(), index, entityClass, serialNum);
    final EntityIndex containedIn = MIBHelper.getEntityContainedIn(dataMap);
    final EntityDBImpl containingEntityDBImpl = synchronizationContainer.get(containedIn);
    final String typeString;

    if (containingEntityDBImpl instanceof SlotDBImpl) {
      final SlotDBImpl slot = (SlotDBImpl) synchronizationContainer.get(containedIn);
      final ShelfDBImpl shelf = (ShelfDBImpl) synchronizationContainer.get(slot.getContainedIn());
      final int parentRelPos = MIBHelper.getEntityRelPos(dataMap);

      typeString = ((parentRelPos > 2) ? "Fan " : "") + "Power Supply " + powerSupplyNo + " in " + shelf.getEquippedTypeString();
    } else {
      typeString = "Power Supply " + powerSupplyNo;
    }

    if (powerSupply == null) {
      // create power supply!

      powerSupply = createPowerSupply(neDBImpl, dataMap, typeString);
    } else {
      resync(powerSupply, typeString, dataMap);
    }

    // add discovered entity to list!
    synchronizationContainer.add(powerSupply);
  }

  protected PowerSupplyDBImpl createPowerSupply(final NetworkElementDBImpl neDBImpl, final Map dataMap, final String typeString) {
    PowerSupplyDBImpl powerSupply = new PowerSupplyDBImpl(neDBImpl.getId(), typeString);
    EntityUpdaterFactory.getInstance().getEntityUpdater(powerSupply).initialize(powerSupply,new SnmpEntityAbstract( dataMap));
    return powerSupply;
  }

  /**
   * Updates an existing port object or creates a new one.
   */
  @Override
  public void resyncPort(final EntityIndex index, final String serialNum, final Map dataMap, SynchronizationContainer synchronizationContainer) {

    final NetworkElementDBImpl neDBImpl = neDBImpl();
    final int entityClass = MIBHelper.getEntityClass(dataMap);
    PortDBImpl portDBImpl = (PortDBImpl) entityDAO.getByEntityIdx(getID(), index, entityClass, serialNum);

    if (portDBImpl == null) {
      // not yet existing -> create new!

      portDBImpl = portFactory.createDbImpl(this, dataMap);

    } else {
      // already exists -> update!
      resync(portDBImpl, null /*typeString*/, dataMap);
    }

    // add discovered entity to list!
    synchronizationContainer.add(portDBImpl);
  }

  /**
   * Returns the actual NEMI SW version.
   */
  @Override
  public String getCurrentNemiSoftwareVersion() {
    return NetworkElementDAO.getActualNEMI_SWVersion(id);
  }

  public String getCachedCurrentSWVersion() {
    if (isPeer()) {
      NetworkElementImpl local = peerMgr.getPeer();
      if (local != null) {
        return local.getCachedCurrentSWVersion();
      }
      log.error("There was no Local for Peer NE[{}]", this);
      return "";
    }
    return cachedCurrentSW;
  }

  public void setCachedCurrentSWVersion(String cachedCurrentSW) {
    this.cachedCurrentSW = cachedCurrentSW;
  }

  protected final ResyncType getResyncType() {
    return !isDiscovered() ? ResyncType.FIRST : ResyncType.SUBSEQUENT;
  }

  protected abstract void runSynchronizationProcess(ResyncType resyncType, InventoryPollingParameters invParams) throws MDOperationFailedException, SNMPCommFailure, InvalidPollingException;

  /**
   * Update trapsink registration date and error message in management status on NE. After that sends
   * message to message window
   *
   * @param errMessage error message to be written into DB and displayed in message
   * @param isHaMode   HA mode
   */
  @Override
  public void setTrapsinkRegistrationError(String errMessage, final boolean isHaMode) {
    ManagementStatus mngtStatus = getPersistenceHelper().getPrimaryManagementStatus();
    mngtStatus.setTrapsinkRegistrationDate(new Date().getTime());
    mngtStatus.setTrapsinkRegistrationErrMsg(errMessage);
    getPersistenceHelper().setPrimaryManagementStatusInDB(mngtStatus, isHaMode);
    trapsinkUtils.sendTrapsinkStatusToMessageBar(this, mngtStatus);
  }

  /**
   * Sets/updates primary management status on NE. In case of change of trapsink status, appropriate
   * message is sent to message window
   *
   * @param newManagementStatus new/updated management status
   * @param isHaMode            HA mode
   */
  @Override
  public synchronized void setPrimaryManagementStatusInDB(final ManagementStatus newManagementStatus, final boolean isHaMode) {
    //check if registration should be updated and message should be send
    getPersistenceHelper().refreshNeManagementStatus();
    final ManagementStatus curManagementStatus = getPersistenceHelper().getPrimaryManagementStatus();

    if (log.isInfoEnabled()) {
      StringBuilder sb = new StringBuilder().append("setPrimaryManagementStatusInDB:");
      sb.append("[curManagementStatus] trapsinkStatus=").append(curManagementStatus.getTrapsinkStatus().getValue()).append(",regDate=").append(new Date(curManagementStatus.getTrapsinkRegistrationDate()));
      sb.append(",regErrMesg=").append(curManagementStatus.getTrapsinkRegistrationErrMsg()).append(",writeAccess=").append(curManagementStatus.getWriteAccessStatus().getValue());
      sb.append(" | [newManagementStatus] trapsinkStatus=").append(newManagementStatus.getTrapsinkStatus().getValue()).append(",regDate=").append(new Date(newManagementStatus.getTrapsinkRegistrationDate()));
      sb.append(",regErrMesg=").append(newManagementStatus.getTrapsinkRegistrationErrMsg()).append(",writeAccess=").append(newManagementStatus.getWriteAccessStatus().getValue());
      log.info(sb.toString());
    }

    //send message in case of changed trapsink status
    if (newManagementStatus.getTrapsinkStatus().getValue() != curManagementStatus.getTrapsinkStatus().getValue()) {
      //trapsink registration date - update
      newManagementStatus.setTrapsinkRegistrationDate(new Date().getTime());
      try {
        //prepare correct error message
        if (curManagementStatus.getTrapsinkStatus() == SNMPTrapsinkStatus.IS_TRAPSINK && newManagementStatus.getTrapsinkStatus() == SNMPTrapsinkStatus.NOT_TRAPSINK) {
          newManagementStatus.setTrapsinkRegistrationErrMsg(
                  getSNMPCtrl().getTrapsinkRegistrationErrorMsg(null, SNMPCtrl.TrapsinkManualActions.TRAPSINK_MANUALLY_DELETED));
        } else if (curManagementStatus.getTrapsinkStatus() == SNMPTrapsinkStatus.NOT_TRAPSINK && newManagementStatus.getTrapsinkStatus() == SNMPTrapsinkStatus.IS_TRAPSINK) {
          newManagementStatus.setTrapsinkRegistrationErrMsg("");
        }
      } catch (SNMPCommFailure e) {
        log.error("setPrimaryManagementStatusInDB: Error getting SNMPCtrl. Trapsink registration message will not be updated.", e);
      }

      if (log.isInfoEnabled()) {
        StringBuilder sb = new StringBuilder().append("setPrimaryManagementStatusInDB: Update trapsink status(").append(newManagementStatus.getTrapsinkStatus().getValue());
        sb.append("), registration date (").append(new Date(newManagementStatus.getTrapsinkRegistrationDate())).append(") and error message (").
                append(newManagementStatus.getTrapsinkRegistrationErrMsg()).append(").");
        log.info(sb.toString());
      }
      //send status message to message window
      trapsinkUtils.sendTrapsinkStatusToMessageBar(this, newManagementStatus);
    }
    //write all management status data, including updated date and error message
    getPersistenceHelper().setPrimaryManagementStatusInDB(newManagementStatus, isHaMode);

  }

  /**
   * Sends trapsink registration message to message bar
   */
  public void sendUnsupportedTrapsinkInfoToMessageBar() {
    log.info("sendUnsupportedTrapsinkInfoToMessageBar - entry");
    StringBuilder msgText = new StringBuilder();
    int severity = MessageManager.Info;

    msgText.append(getIPAddress()).append(" (").append(getName()).append("): ");
    msgText.append("Automatic trapsink not supported. Please set the trapsink manually on the NE craft interface.");

    if (log.isInfoEnabled()) log.info("sendUnsupportedTrapsinkInfoToMessageBar - send message [" + severity + "," + msgText.toString() + "]");
    String msgId = "Trapsink";
      messageManager.addMessage(MessageManager.NO_LOGFILE, DISCOVERY_MSG_QUEUE, this, severity, msgText.toString(), msgId);
  }

  public void sendTrapsinkFullInfoToMessageBar() {
    log.info("sendTrapsinkFullInfoToMessageBar - entry");
    StringBuilder msgText = new StringBuilder();
    int severity = MessageManager.Info;

    msgText.append(getIPAddress()).append(" (").append(getName()).append("): ");
    msgText.append("Unable to register trapsink entry - trapsink full.");

    if (log.isInfoEnabled()) log.info("sendTrapsinkFullInfoToMessageBar - send message [" + severity + "," + msgText.toString() + "]");
    String msgId = "Trapsink";
      int userId = SessionRestHdlr.getInstance().getCurrentUserId();
    messageManager.addMessage(MessageManager.NO_LOGFILE, DISCOVERY_MSG_QUEUE, this, severity, msgText.toString(), msgId, userId, userId == 0);
  }

  @Override
  public void sendNotifyInfoToMessageBar(String cause) {
    log.info("sendNotifyInfoToMessageBar - entry");
    StringBuilder msgText = new StringBuilder();

    msgText.append(getIPAddress()).append(" (").append(getName()).append("): ");
    msgText.append("Unable to add trap tag to notify table: ");
    msgText.append(cause);

    String msgId = createMsgId();
    messageManager.addMessage(MessageManager.NO_LOGFILE, DISCOVERY_MSG_QUEUE, this, MessageManager.Info, msgText.toString(), msgId);
  }

  private String createMsgId() {
    return String.valueOf(getID()) + "-" + new Date();
  }

  @Override
  public void sendShortTrapNotifyInfoToMessageBar(String cause) {
    log.info("sendNotifyInfoToMessageBar - entry");
    StringBuilder msgText = new StringBuilder();

    msgText.append(getIPAddress()).append(" (").append(getName()).append("): ");
    msgText.append(cause);

    String msgId = createMsgId();
    messageManager.addMessage(MessageManager.NO_LOGFILE, DISCOVERY_MSG_QUEUE, this, MessageManager.Info, msgText.toString(), msgId);
  }

  @Override
  public void sendNotifyErrorToMessageBar(String cause) {
    log.info("sendNotifyInfoToMessageBar - entry");
    StringBuilder msgText = new StringBuilder();

    msgText.append(getIPAddress()).append(" (").append(getName()).append("): ");
    msgText.append(cause);

    String msgId = createMsgId();
    messageManager.addMessage(MessageManager.NO_LOGFILE, DISCOVERY_MSG_QUEUE, this, MessageManager.Error, msgText.toString(), msgId);
  }

  @Override
  public void sendNotifyWarnToMessageBar(String cause) {
    log.info("sendNotifyInfoToMessageBar - entry");
    StringBuilder msgText = new StringBuilder();

    msgText.append(getIPAddress()).append(" (").append(getName()).append("): ");
    msgText.append(cause);

    String msgId = createMsgId();
    messageManager.addMessage(MessageManager.NO_LOGFILE, DISCOVERY_MSG_QUEUE, this, MessageManager.Warning, msgText.toString(), msgId);
  }
  @Override
  public void sendNotifySuccessToMessageBar(String cause) {
    log.info("sendNotifySuccessToMessageBar - entry");
    StringBuilder msgText = new StringBuilder();

    msgText.append(getIPAddress()).append(" (").append(getName()).append("): ");
    msgText.append(cause);

    String msgId = createMsgId();
    messageManager.addMessage(MessageManager.NO_LOGFILE, DISCOVERY_MSG_QUEUE, this, MessageManager.Ok, msgText.toString(), msgId);
  }

  @Override
  public void sendGeneralNotifyInfoToMessageBar(String cause) {
    log.info("sendNotifySuccessToMessageBar - entry");
    StringBuilder msgText = new StringBuilder();

    msgText.append(getIPAddress()).append(" (").append(getName()).append("): ");
    msgText.append(cause);

    String msgId = createMsgId();
    messageManager.addMessage(MessageManager.NO_LOGFILE, DISCOVERY_MSG_QUEUE, this, MessageManager.Info, msgText.toString(), msgId);
  }

  @Override
  public void sendInventoryErrorToMessageBar(String cause) {
    log.info("sendInventoryErrorToMessageBar - entry");

    messageManager.addMessage(MessageManager.NO_LOGFILE, INVENTORY_MSG_QUEUE, this, MessageManager.Error, cause);
  }

  /**
   * Check Service Reconciliation (SR) status and log it.
   */
  public void checkSRStatus() {
    //dummy method.
  }


  /**
   * TODO msteiner it is overridden only for F7 should be removed from NEImpl
   *
   * @param port
   * @return
   */
  public boolean isPortRemovable(final PortDBImpl port) {
    //supported in sub classes
    return false;
  }


  /**
   * Checks whether a contained discovered entity is assigned.
   *
   * @param discoveredEntityIndex The index of the containing discovered entity.
   * @return true, if a contained discovered entity is assigned.
   */
  public boolean isContainedDiscoveredEntityAssigned(final EntityIndex discoveredEntityIndex) {
    boolean isContainedEntityAssigned = false;  // the return value

    for (final EntityDBImpl containedDiscoveredEntityDBImpl : getSynchronizationContainer().getEntitiesTree()) {
      final EntityIndex containedIn = containedDiscoveredEntityDBImpl.getContainedIn();

      if (containedIn.equals(discoveredEntityIndex)) {
        if (containedDiscoveredEntityDBImpl.isAssigned() || isContainedDiscoveredEntityAssigned(containedDiscoveredEntityDBImpl.getEntityIndex())) {
          isContainedEntityAssigned = true;
          break;
        }
      }
    }

    return isContainedEntityAssigned;
  }

  //=============================== PERFORMANCE methods ==============================



  /**
   * Checks whether the network element was already discovered.
   *
   * @param pollingType The polling type.
   * @throws MDOperationFailedException The network element was not discovered.
   */
  @Override
  public void checkIsDiscovered(long pollingType)
          throws MDOperationFailedException {
    boolean preventPolling = false;
    String failureMsg = null;

    final boolean discovered = isDiscovered();

    if (discovered) {
      if (pollingType == PollingType.CONTINUOUS_DISCOVERY.getType()) {
        preventPolling = true;
        failureMsg = "already discovered";
      }
    } else {
      if (pollingType != PollingType.INVENTORY.getType() &&
              pollingType != PollingType.INVENTORY_SCAN.getType() &&
              pollingType != PollingType.INVENTORY_DB.getType() &&
              pollingType != PollingType.CONTINUOUS_DISCOVERY.getType() &&
              pollingType != PollingType.INIT_SNMP_CTRL.getType() &&
              pollingType != PollingType.FIND_AND_DISCOVER_PLANNED_NODE.getType() &&
              pollingType != PollingType.SYNC_EC_DATA_MODEL.getType() &&
              pollingType != PollingType.INIT_COMMUNICATION.getType()) {
        preventPolling = true;
        failureMsg = "not discovered";
      }
    }

    if (preventPolling) {
      final String ipAddress = getIPAddress();
      final String errMessage = (ipAddress.length() > 0) ? "Network element " + getName() + " (" + ipAddress + ") is " + failureMsg + "!"
              : "Network element " + getName() + " is " + failureMsg + "!";
      throw new DiscoveryStateMismatchException(errMessage);
    }
  }


  /**
   * Does polling for inventory data.
   *
   * @param invParams Inventory Polling Parameters
   * @throws SNMPCommFailure: Method failed because of SNMP communication problems.
   */
  @Override
  @Benchmark
  public void doPollingInventory(final InventoryPollingParameters invParams)
          throws SNMPCommFailure, InvalidPollingException, MDOperationFailedException {
    if (log.isInfoEnabled()) log.info("{ " + this + ".doPollingInventory()");

    try  // protect debug output!
    {
      // Resynchronizes network element inventory and detects new entities.
      // Called from inside the server.

      runSynchronizationProcess(getResyncType(), invParams);
      setCachedCurrentSWVersion(getCurrentNemiSoftwareVersion());

      PollingFramework.inline(this, SW_VERSION_CHECK);
    } finally {
      if (log.isInfoEnabled())
        log.info("} " + this + ".doPollingInventory()");
    }
    if (invParams.getOnAfterSccessfullInventory() != null) {
      invParams.getOnAfterSccessfullInventory().run();
    }
  }

  @Override
  public void pollManagementStatus() throws SNMPCommFailure {
    final SNMPCtrl snmpCtrl = getSNMPCtrl();
    final TrapsinkStatus trapsinkStatus = snmpCtrl.getTrapsinkStatus();

    final SNMPWriteAccessStatus newSNMPWriteAccess = snmpCtrl.getSNMPWriteAccessStatus();
    pollManagementStatus(trapsinkStatus.isTrapsink(), trapsinkStatus.isSecondaryTrapsink(), newSNMPWriteAccess);
  }

  @Override
  public void pollManagementStatus(final boolean isTrapsink, final boolean isSecondaryTrapsink, final SNMPWriteAccessStatus newSNMPWriteAccess) {
    final ManagementStatus managementStatus = getPersistenceHelper().getPrimaryManagementStatus();
    final boolean wasTrapsink = (managementStatus.getTrapsinkStatus() == SNMPTrapsinkStatus.IS_TRAPSINK);
    final boolean wasSecondaryTrapsink = (getPersistenceHelper().getSecondaryManagementStatus().getTrapsinkStatus() == SNMPTrapsinkStatus.IS_TRAPSINK);
    final SNMPWriteAccessStatus oldSNMPWriteAccess = managementStatus.getWriteAccessStatus();
    final boolean isHaMode = !serverCtrl.isHaStandalone();

    if (log.isInfoEnabled()) {
      StringBuilder sb = new StringBuilder().append("pollManagementStatus:");
      sb.append("HA=").append(isHaMode).append(",wasTrapsink=").append(wasTrapsink).append(",isTrapsink=").append(isTrapsink);
      sb.append(",wasSecTrapsink=").append(wasSecondaryTrapsink).append(",isSecTrapsink=").append(isSecondaryTrapsink);
      sb.append(",oldWriteAccess=").append(oldSNMPWriteAccess.getValue()).append(",newWriteAccess=").append(newSNMPWriteAccess.getValue());
      log.info(sb.toString());
    }
    //noinspection ConstantConditions
    final String identifier = (log.isInfoEnabled()) ? getIdentifierStr() : null;

    //update primary trapsink
    if ((wasTrapsink != isTrapsink) || (oldSNMPWriteAccess != newSNMPWriteAccess)) {
      final SNMPTrapsinkStatus newSNMPTrapsinkStatus = (isTrapsink) ? SNMPTrapsinkStatus.IS_TRAPSINK : SNMPTrapsinkStatus.NOT_TRAPSINK;
      long trapsinkRegDate = managementStatus.getTrapsinkRegistrationDate();
      String trapsinkErrMsg = managementStatus.getTrapsinkRegistrationErrMsg() == null ? "" : managementStatus.getTrapsinkRegistrationErrMsg();
      final ManagementStatus newManagementStatus = new ManagementStatus(newSNMPWriteAccess, newSNMPTrapsinkStatus, trapsinkRegDate, trapsinkErrMsg);

      setPrimaryManagementStatusInDB(newManagementStatus, isHaMode);
    } else {
      //in case of system configuration change like HA on/off
      if (isTrapsink && getPersistenceHelper().doSNMPTrapsinkRegistration() && !isHaMode) {
        getPersistenceHelper().setDoSNMPTrapsinkRegistration(false);
      }
      //in case device is in trapsink and there was error text from previous operation (like reregistration)
      if (isTrapsink && managementStatus.getTrapsinkRegistrationErrMsg() != null && !"".equalsIgnoreCase(managementStatus.getTrapsinkRegistrationErrMsg())) {
        managementStatus.setTrapsinkRegistrationErrMsg("");
        setPrimaryManagementStatusInDB(managementStatus, isHaMode);
      }
    }
    //update secondary trapsink
    if ((oldSNMPWriteAccess != newSNMPWriteAccess) || (isHaMode && (wasSecondaryTrapsink != isSecondaryTrapsink))) {
      // todo sibmar this join trapsink status is not very transparent, it may be worth to change it.
      // in ha mode we get here join trapsink status so we use same for secondary mgm status.
      final SNMPTrapsinkStatus newSNMPTrapsinkStatus = (isSecondaryTrapsink) ? SNMPTrapsinkStatus.IS_TRAPSINK : SNMPTrapsinkStatus.NOT_TRAPSINK;
      getPersistenceHelper().setSecondaryManagementStatusInDB(new ManagementStatus(newSNMPWriteAccess, newSNMPTrapsinkStatus, 0, ""), isHaMode);
    }
    // make primary trapsink registration notifications!
    if (wasTrapsink != isTrapsink) {
      if (log.isInfoEnabled()) log.info(identifier + ": Primary Trapsink Status changed to: " + isTrapsink + "!");
      // create event!
      trapsinkUtils.createTrapsinkInfoServerTrap(this, isTrapsink, evtProcCtrl.getEventProcFacade());
    }
    // make secondary trapsink registration notifications!
    if (isHaMode && (wasSecondaryTrapsink != isSecondaryTrapsink)) {
      if (log.isInfoEnabled()) log.info(identifier + ": Secondary Trapsink Status changed to: " + isSecondaryTrapsink + "!");
      // create event!
      trapsinkUtils.createTrapsinkInfoServerTrap(this, isSecondaryTrapsink, evtProcCtrl.getEventProcFacade());
    }
    if (oldSNMPWriteAccess != newSNMPWriteAccess) {
      if (log.isInfoEnabled()) log.info(identifier + ": SNMP Write Access changed to: " + newSNMPWriteAccess.getValue() + "!");

      // create event!
      createSNMPWriteAccessServerTrapAttributes(newSNMPWriteAccess);
    }
  }

  /**
   * Does polling for status data (port and equipment alarms).
   *
   * @param dataManagerSet      Set of specific dataManagers used for scanning only specific objects. Used during Status and Configuration polling.
   * @param includeStatusFields True if status polling should check State fields.
   *                            False if state fields are not needed and only alarms are checked.
   *                            False value is mainly used during initial discovery (optimization).
   * @throws SNMPCommFailure                                                     The SNMP communication failed.
   * @throws com.adva.nlms.mediation.common.transactions.InvalidPollingException Throw InvalidPollingException when given polling has been run while NetTransaction is active.
   */
  @Override
  @Benchmark
  public void doPollingStatus(final Set<DataManager> dataManagerSet, final boolean includeStatusFields)
          throws SNMPCommFailure, InvalidPollingException {
    pollingStatusWorker.doPollingStatus(dataManagerSet, includeStatusFields);
  }

  /**
   * Performs polling for check of communication to network element.
   *
   * @throws MDOperationFailedException   The request failed.
   * @throws SNMPCommFailure Error during SNMP communication.
   */
  @Override
  public void doPollingKeepAlive()
          throws MDOperationFailedException, InvalidPollingException, SNMPCommFailure {
    if (log.isInfoEnabled()) log.info("{ NetworkElement." + this + ".doPollingKeepAlive()");
    try  // protect debug output!
    {
      // 0.) wait for cold start time check to be finished!
      if (!coldStartTimeCheckDone) {
        log.warn("* {}: Cold start time check not done, doing it again! *", this);
        if (!isPeer()) {
          PollingParameters pollingParameters = new NeColdStartTimeCheckPollingParameter(false);
          if (!getPollingManager().isRunning(PollingType.NE_COLD_START_TIME_CHECK.getType(), pollingParameters)) {
            PollingFramework.inline(this, PollingType.NE_COLD_START_TIME_CHECK, pollingParameters);
          }
        }
        evtProcCtrl.getEventProcessingManager().onPollingFailure(this);
        throw new MDOperationFailedException("Cold start time check not done!");
      }

      // NE specific actions (should be performed asynchronously)
      runKeepAliveRelatedOpers();
      //keep alive polling
      getSNMPCtrl().doPollingKeepAlive(evtProcCtrl.getEventProcessingManager());
    } finally {
      if (log.isInfoEnabled()) log.info("} NetworkElement." + this + ".doPollingKeepAlive()");
    }
  }

  /**
   * Helper for doPollingKeepAlive(). Runs NE specific actions related with keep alive polling.
   */
  protected void runKeepAliveRelatedOpers() {
    if(!TrapsinkUtils.isTrapsinkDisabled()) {
      PollingFramework.commission(this, PollingType.TRAPSINK_REGISTRATION);
    }
  }

  /**
   * Performs asynchronous trapsink registration.
   */
  protected void doCheckSNMPTrapsinkRegistration() {
    if (log.isInfoEnabled()) log.info("{ NetworkElement." + this + ".doCheckSNMPTrapsinkRegistration()");

    try {
      // disable trapink registration on slave if HA mode is turned on
      HAController haController = serverCtrl.getHaController();
      if ((haController.getCurrentHAConfiguration().isHaEnabled && WorkMode.SLAVE.equals(haController.getWorkModeEnum()))) {
        if (log.isInfoEnabled()) log.info("{ NetworkElement." + this + ".doCheckSNMPTrapsinkRegistration() ignored on slave.");
      } else {
        /**
         *  isAutomaticTrapsinkRegistrationEnabled  // re-register in trapsink if entry no present
         */
        if (isAutomaticTrapsinkRegistrationEnabled() || doSNMPTrapsinkRegistration()) {
          getSNMPCtrl().executeSNMPTrapsinkRegistration();
        }
        getSNMPCtrl().executeSNMPTrapsinkStatusCheck();
      }
    } catch (SNMPCommFailure e) {
      final String warnString = "NetworkElement.doCheckSNMPTrapsinkRegistration() ignored SNMPCommFailure: " + e.getErrMessage();
      if (log.isDebugEnabled())
        log.debug(warnString, e);
      else
        log.warn(warnString);
    } catch (Throwable th) {
      log.error("Problem during Trapsink Registration", th);
    } finally {
      if (log.isInfoEnabled()) log.info("} NetworkElement." + this + ".doCheckSNMPTrapsinkRegistration()");
    }
  }

  protected boolean isAutomaticTrapsinkRegistrationEnabled() {
    return ConfigPropertiesHelper.isAutomaticTrapsinkRegistrationEnabled() &&
            getNetworkElement().getEffectiveSNMPProperties().isAutomaticTrapsinkRegistrationEnabled &&
        !TrapsinkUtils.isTrapsinkDisabled();
  }

  @Override
  public void checkSNMPTrapsinkRegistration() {
    if (log.isInfoEnabled()) log.info("{ NetworkElement." + this + ".checkSNMPTrapsinkRegistration()");

    try {
      if (doSNMPTrapsinkRegistration()) {
        getSNMPCtrl().executeSNMPTrapsinkRegistration();
      }
    } catch (SNMPCommFailure e) {
      String errMessage = "";
      try {
        errMessage = getSNMPCtrl().getTrapsinkRegistrationErrorMsg(e, SNMPCtrl.TrapsinkManualActions.TRAPSINK_NO_MANUAL_ACTION);
      } catch (SNMPCommFailure snmpCommFailure) {
        log.warn("Error getting SNMPCtrl. Trapsink registration message will not be updated.", e);
      }
      setTrapsinkRegistrationError(errMessage, !serverCtrl.isHaStandalone());
      final String warnString = "NetworkElement.checkSNMPTrapsinkRegistration() ignored SNMPCommFailure: " + e.getErrMessage();
      if (log.isDebugEnabled())
        log.info(warnString, e);
      else
        log.warn(warnString);
    } finally {
      if (log.isInfoEnabled()) log.info("} NetworkElement." + this + ".checkSNMPTrapsinkRegistration()");
    }
  }


  //=============================== TRAP LOG methods ==============================

  /**
   * Returns the sum of all received events (trap counter).
   *
   * @return the sum of all received events (trap counter).
   */
  @Override
  @MDPersistenceContext
  public int getNEEventsReceived() {
    return neEventsDBImpl().getNEEventsReceived();
  }

  @Override
  @MDPersistenceContext
  public long getNeRebootTimestamp() {
    return neEventsDBImpl().getNeRebootTimestamp();
  }

  @Override
  @MDPersistenceContext
  public boolean checkAndSetNeRebootTimestamp(long currentRebootTimestamp, long sysUptime, KapCommandParams params) {
    final long maxValidTimeFrame = (sysUptime < _3MIN) ? _15SEC :    // 15s = timeout * (retries+1) --- timeout=5s, retries=2
                                   (sysUptime < _5MIN) ?  _3MIN
                                                       :  _15MIN;
    NeEventsDBImpl neEventsDB = !isPeer() ? neEventsDBImpl() : getPeerMgr().getPeer().neEventsDBImpl();
    if (neEventsDB == null)
      return false;

    long lastTimestamp = neEventsDBImpl().getNeRebootTimestamp();
      log.info("[checkAndSetNeRebootTimestamp] ne={}/{}, currentTimestamp={}, sysUptime={}, lastTimestamp={}, maxValidTimeFrame={}",
            this.getID(), this.getIPAddress(), currentRebootTimestamp, sysUptime, lastTimestamp, maxValidTimeFrame);
    if (currentRebootTimestamp > (lastTimestamp - maxValidTimeFrame) && currentRebootTimestamp < (lastTimestamp + maxValidTimeFrame))
      return false;

    if(areAdditionalConditionsMatchedForSettingNeRebootTimestamp(params)) {
      synchronized (eventDBChangeHdlr.getEventsDBLockObject()) {
        setNeRebootTimestamp(neEventsDB, currentRebootTimestamp);
      }
    }
    return true;
  }
  @MDTransactional
  private void setNeRebootTimestamp(NeEventsDBImpl neEventsDB, long currentTimestamp) {
    neEventsDB.setNeRebootTimestamp(currentTimestamp);
  }

  public boolean areAdditionalConditionsMatchedForSettingNeRebootTimestamp(KapCommandParams params){
// checked for specific NEs like HN or EGM
    return true;
  }

  /**
   * Initializes the last received event trap counter to the specified value (after NE discovery).
   */
  @Override
  public void initNEEventsReceived(final int newNEEventsReceived) {
    synchronized (eventDBChangeHdlr.getEventsDBLockObject()) {
      initNEEventsReceivedTransactional(newNEEventsReceived);
    }
    eventProcNotificationHdlr.notifyNeCounterChange(getNetworkElement(), MissedEventEnumContext.EVENT);
  }

  @MDTransactional(restart = true)
  private void initNEEventsReceivedTransactional(final int newNEEventsReceived) {
    neEventsDBImpl().initNEEventsReceived(newNEEventsReceived);
  }

  @Override
  @MDPersistenceContext
  public long getLastProcessedEvent() {
    return neDBImpl().getMoEventsProcessed();
  }

  @Override
  @MDTransactional(restart = true)
  public void setLastProcessedEvent(long eventId) {
    neDBImpl().setMoEventsProcessed(eventId);
  }

  @Override
  @MDTransactional(restart = true)
  public boolean setNEEventsReceived(final int neEventsReceived, boolean unconditional) {
    return neEventsDBImpl().setNEEventsReceived(neEventsReceived, hasPeers(), unconditional);
  }

  @Override
  @MDTransactional(restart = true)
  public boolean setNEEventsReceivedFromBulk(int newNeStartLogIndex, int newNeEndLogIndex, boolean unconditional) {
    return neEventsDBImpl().setNeEventsReceivedFromBulk(newNeStartLogIndex, newNeEndLogIndex, hasPeers(), unconditional);
  }

  /**
   * Resets the last received event trap counter to the specified value (after ne startup). Clears the list of trap
   * counters of all missed events.
   */
  @Override
  public void resetNEEventsReceived(final int neEventsReceived) {
    synchronized (eventDBChangeHdlr.getEventsDBLockObject()) {
      resetNeEventsReceivedInDB(neEventsReceived);
    }
    eventProcNotificationHdlr.notifyNeCounterReset(getNetworkElement(), MissedEventEnumContext.EVENT);
  }

  @MDTransactional(restart = true)
  private void resetNeEventsReceivedInDB(int neEventsReceived) {
    neEventsDBImpl().resetNEEventsReceived(neEventsReceived);
  }

  /**
   * Get the currently active port.
   *
   * @param moduleIndex Module ID of the RSM/HST module
   * @return Status values defined in
   * @throws SNMPCommFailure The SNMP communication failed.
   */
  public int getLineProtStatusViaSNMP(EntityIndex moduleIndex) throws SNMPCommFailure {
    throw new SNMPCommFailure("getProtectionStatus() not supported for NE " +
            getIPAddress(), SNMPError.INTERNAL_ERROR);
  }

  /**
   * Switch to port A.
   *
   * @param moduleIndex Module ID of the RSM/HST module
   * @throws SNMPCommFailure The SNMP communication failed.
   */
  protected void switchToPortAViaSNMP(EntityIndex moduleIndex) throws SNMPCommFailure {
    throw new SNMPCommFailure("switchToPortA() not supported for NE " +
            getIPAddress(), SNMPError.INTERNAL_ERROR);
  }

  /**
   * Switch to port B.
   *
   * @param moduleIndex Module ID of the RSM/HST module
   * @throws SNMPCommFailure The SNMP communication failed.
   */
  protected void switchToPortBViaSNMP(EntityIndex moduleIndex) throws SNMPCommFailure {
    throw new SNMPCommFailure("switchRSMToLineB() not supported for NE " +
            getIPAddress(), SNMPError.INTERNAL_ERROR);
  }

  /**
   * Switch the RSM to the specified direction.
   *
   * @param moduleIndex Module ID of the RSM/HST module
   * @param direction   <br> True: Port A/ network east port
   *                    <br> False: Port B/ network west port
   * @throws SNMPCommFailure The SNMP communication failed.
   */
  public void switchToRSMPortViaSNMP(EntityIndex moduleIndex, boolean direction) throws SNMPCommFailure {
    if (direction) {
      switchToPortAViaSNMP(moduleIndex);
    } else {
      switchToPortBViaSNMP(moduleIndex);
    }
  }

  /**
   * Returns the database implementation of an entity. If not existing, null
   * will be returned.
   *
   * @param entityIndex The index of the entity.
   * @return the database implementation of an entity.
   */
  @MDPersistenceContext
  public EntityDBImpl getEntityByIndex(final EntityIndex entityIndex) {

    EntityDBImpl entityDBImpl = getSynchronizationContainer().get(entityIndex);
    if (entityDBImpl == null) {

      try {
        entityDBImpl = entityDAO.getEntityDBImplOrThrowException(getID(), entityIndex);
      } catch (NoSuchEntityException e) {
        // entity not in database!
      }
    }

    return entityDBImpl;
  }

  /**
   * Returns a set of entities which are contained in this entity.
   *
   * @param containerIndex The index of the container.
   * @return a set of entities which are contained in this entity.
   */
  public final Set<EntityImpl> getContainedEntityImpls(final EntityIndex containerIndex) {
    //noinspection unchecked
    return getContainedEntityImplObjects(containerIndex);
  }


  /**
   * Returns a set of entities which are contained in this entity.
   *
   * @param containerIndex The index of the container.
   * @return a set of entities which are contained in this entity.
   */
  public LinkedHashSet getContainedEntityImplObjects(final EntityIndex containerIndex) {
    return (LinkedHashSet) getContainedEntityImpls(new LinkedHashSet<EntityImpl>(), containerIndex);
  }


  /**
   * Returns a set of entities which are contained in this entity
   *
   * @param containedEntityImplCollection The collection of contained entities.
   * @param containerIndex                The index of the container.
   * @return Set (value = EntityImpl)
   */
  public final Collection<EntityImpl> getContainedEntityImpls(final Collection<EntityImpl> containedEntityImplCollection, final EntityIndex containerIndex) {
    return getNearestChilds(containerIndex);
  }

  /**
   * Method return nearest childrens under parent. (one level depth)
   *
   * @param containerIndex Entity Index of the parent.
   * @return Collection<EntityImpl> All nearest childs.
   */
  private Collection<EntityImpl> getNearestChilds(final EntityIndex containerIndex) {
    final Collection<EntityImpl> result = new LinkedHashSet<>();

    final List<EntityDBImpl> entityDBImpls = entityDAO.getByContainedIn(getID(), containerIndex);

    for (EntityDBImpl entityDBImpl : entityDBImpls) {
      final EntityImpl entityImpl = DataManagerFactory.getInstance().recreateImpl(this, entityDBImpl);
      result.add(entityImpl);
    }
    return result;
  }

  /**
   * Checks the startup time got via SNMP from the network element. Polling is started, if necessary.
   *
   * @param snmpColdStartTime The cold start time got via SNMP.
   * @param errorMessage      An optional error message.
   * @param forcePolling      force full swync
   */
  @Override
  public void processColdStartTime(final long snmpColdStartTime, final String errorMessage, boolean forcePolling, long pollingUniqueId) {
    // this is part of cold start time polling, note that on thread
    // TODO a bit ugly, internals exposed
    PollingStarter.makeCurrent(pollingManager.getPollingCore().getPollingIndex().get(pollingUniqueId));

    // 1.) check whether polling is needed!
    boolean doPolling = true;

    try  // ensure finishing of check without exception!
    {
      try {
        if (errorMessage == null) {
          final long dbColdStartTime = this.getColdStartTime();
          final SNMPPropertiesData snmpProperties = getEffectiveSNMPProperties();
          final long maxValidTimeFrame = (snmpProperties.snmpTimeout * 100) * (snmpProperties.snmpRetryCount + 1);

          if ((snmpColdStartTime >= (dbColdStartTime - maxValidTimeFrame)) && (snmpColdStartTime <= (dbColdStartTime + maxValidTimeFrame))) {
            // everything's okay!
            doPolling = forcePolling; //false;
          } else {
            // seems that we've missed something...
            if (log.isInfoEnabled()) log.info(this + ": Detected cold start at " + new Date(snmpColdStartTime) + "!");

            // XXX always set new cold start time, because maybe we won't find the cold start trap in the event log!
            setColdStartTime(snmpColdStartTime);

            if (snmpColdStartTime > dbColdStartTime) {
              // missed startup of network element -> poll and resynchronize all traps/events!
              resetNEEventsReceived(0);
              PollingFramework.commission(this, PollingType.KEEP_ALIVE);
            }
          }
        } else {
          log.warn("* " + this + ": " + errorMessage + " *");
        }
      } catch (Throwable t) {
        final String errorString = "NetworkElement.processColdStartTime() ignored Throwable:";
        log.error(errorString, t);
      }
    } catch (Throwable t) {
      // XXX no code here!
      log.error("processColdStartTime", t);
    }

    coldStartTimeCheckDone = true;

    Throwable throwable = null;
    if (errorMessage != null) {
      throwable = new MDPollingFailedException(errorMessage);
    }

    // 2.) do polling!
    performColdStartPollings(doPolling);

    // unregister after pollings are executed so they can be treated as children of this one
    PF.getPollingService().unregister(pollingUniqueId, throwable);
  }

  public void refreshNeViaPollings() {
    // execute necessary pollings one by one per NE to minimize the number of dependant pollings in queue
    PollingFramework.commission(this, PollingType.INVENTORY,
            (e1) -> PollingFramework.commission(this, PollingType.CONFIGURATION,
                    (e2) -> PollingFramework.commission(this, PollingType.STATUS)
            ));
  }

  protected void performColdStartPollings(boolean doPolling) {
    if (doPolling && !INITIAL_POLLING_SUPPRESS) {
      if (!hasPeers()) {
        // this may result in a huge number of pollings queued and executed if many NEs are recognized as having been
        // cold started (i.e. scaling DB restore scenario or DB upgrade with forced cold start time reset)
        refreshNeViaPollings();
      } else {
        //if peers exist
        final ScanPeersPollingParameters parameters = new ScanPeersPollingParameters(ScanPeersCommand.KapNeeded.YES);
        PollingFramework.commission(this, PollingType.SCAN_PEERS, parameters);
      }
    }
  }

  /**
   * Writes he current the active port (A/B/no port) into the database and
   * updates the line (RSM)/connection (CM) config.
   *
   * @param moduleIndex Index of the entity.
   * @param value       New MIB defined state.
   * @param neTimeStamp Time of the event.
   * @throws NoSuchMDObjectException: The module doesn't exist or is not a switch module.
   */
  @Override
  @MDTransactional
  public final void setOSActivePort(EntityIndex moduleIndex, int value, long neTimeStamp)
          throws NoSuchMDObjectException {

    if (log.isInfoEnabled()) log.info("setOSActivePort(" + moduleIndex + ", " +
            value + ", " + neTimeStamp + ") NE-ID " + id);
    final ModuleDBImpl moduleDBImpl = moduleDAO.getModuleDBImplOrThrowException(getID(), moduleIndex);
    if (!(moduleDBImpl instanceof ModuleOSDBExtension)) {
      log.warn("* Index " + moduleIndex + ": Not an OS module (" + moduleDBImpl.getClass() + ")! *");
      throw new NoSuchEntityException("Not an OS module. Index " + moduleIndex);
    }
    ModuleOSDBExtension osModule = (ModuleOSDBExtension) moduleDBImpl;
    setActivePort(value, osModule);
    checkLineRSConnectionCM(moduleDBImpl, moduleIndex);
  }

  private void checkLineRSConnectionCM(ModuleDBImpl moduleDBImpl, EntityIndex moduleIndex) throws NoSuchMDObjectException {
    // check the line(RSM)/connection/(CM) config
    // CM
    // XXX instanceof doesn't work?
    if (moduleDBImpl.getModuleType() == ModuleType.TYPE_HST) {
      OpticalChannelService oChConnection = connectionUtil.getOChConnection(moduleDBImpl, ((ModuleCMDBExtension) moduleDBImpl).getRemotePort());
      // trigger alarm de-escalation for HST modules
      if (oChConnection != null)
        correlationScheduler.addTrigger(CorrelTriggerType.PROT_STATUS_CHANGED, oChConnection.getId());
      // Checks the configuration and rebuild references.
      if ((oChConnection != null) && oChConnection.reset()) {
        SMNotificationHelper.getInstance().notifyServiceChangeViaTopologyNodeHdlr(ConfigChangeType.CHANGED, (AbstractConnectionDBImpl) oChConnection);
      }
      // RSM
    } else {
      // get one of the protecting lines
      try {
        ProtLineDBImpl protLine = getLineHdlrLocal().getLineByRSM(id, moduleIndex);
        Set<Integer> lineIDs = new HashSet<>();
        lineIDs.add(protLine.getId());
        correlationScheduler.addTrigger(CorrelTriggerType.PROT_STATUS_CHANGED, lineIDs);
        if (protLine.checkConfiguration()) {
          TopologyNodeHdlrLocal treeNodeHdlr = getTopologyNodeHdlrLocal();
          treeNodeHdlr.pushTopologyChange(protLine, ConfigChangeType.CHANGED);
          treeNodeHdlr.pushTopologyChange(protLine.getProtectingLine(), ConfigChangeType.CHANGED);
        }
      } catch (NoSuchLineException e) {
        if (log.isInfoEnabled()) log.info("No line defined for RSM, " + e.getMessage());
      }
    }
  }

  /**
   * Writes he current the locked port (A/B/no port) into the database.
   *
   * @param moduleIndex Index of the entity.
   * @param value       New MIB defined state.
   * @throws NoSuchEntityException: The module doesn't exist or is not a switch module.
   */
  @Override
  @MDTransactional(restart = true)
  public final void setOSLocked(EntityIndex moduleIndex, int value) throws NoSuchEntityException {

    if (log.isInfoEnabled()) log.info("setOSLocked(" + moduleIndex + ", " + value + ")");
    final ModuleDBImpl moduleDBImpl = moduleDAO.getModuleDBImplOrThrowException(getID(), moduleIndex);
    if (!(moduleDBImpl instanceof ModuleOSDBExtension)) {
      log.warn("* Index " + moduleIndex + ": Not an OS module (" + moduleDBImpl.getClass() + ")! *");
      throw new NoSuchEntityException("Not an OS module. Index " + moduleIndex);
    }
    ModuleOSDBExtension osModule = (ModuleOSDBExtension) moduleDBImpl;
    osModule.setLocked(value);
  }

  /**
   * Changes the protection state (direction) of a protected channel
   * card via SNMP.
   *
   * @param connectionId The connection which is switched.
   * @param moduleId     The start or end module.
   * @param direction    Direction east (true)/ west (false). @throws SNMPCommFailure SNMP request failed.
   * @throws SNMPCommFailure SNMP communication failure.
   */
  public boolean setProtectionStatusViaSNMP(int connectionId,
                                            int moduleId,
                                            boolean direction)
          throws SNMPCommFailure {
    throw new RuntimeException("setProtectionStatusViaSNMP() not implemented for FSP" +
            getNetworkElementType());

  }

  /**
   * Changes the protection state (direction) of a protection group or an optical switch in the
   * database, after it has been switched. The implementation can be found in the appropriate subclasses
   *
   * @param connectionId The Connection if available.
   * @param moduleId     The RSM/HST/channel module
   * @param direction    Direction east (true)/ west (false). @return true if the status has changed.
   * @return True when the protection state has changed in the DB.
   */
  @MDTransactional(restart = true)
  public boolean setProtectionStatus(int connectionId, final int moduleId, boolean direction) {
    if (moduleId == 0)
      return false;
    ModuleDBImpl module = MDPersistenceHelper.find(ModuleDBImpl.class, moduleId);
    boolean changed = false;
    if (module instanceof ModuleOSDBExtension) {
      ModuleOSDBExtension osModuleDBImpl = (ModuleOSDBExtension) module;
      int newActivePort = osModuleDBImpl.getPortNumberForDirection(direction);
      if (log.isInfoEnabled()) log.info(getIPAddress() + ".setProtectionStatus(): " + module +
              ", newActivePort(1,2): " + newActivePort);
      changed = osModuleDBImpl.clearLock();
      changed |= setActivePort(newActivePort, osModuleDBImpl);
    } else {
      log.error("Not an optical switch module. Index " + module);
    }
    return changed;
  }

  private boolean setActivePort(int operState, ModuleOSDBExtension osModuleDBImpl) {
    boolean result = osModuleDBImpl.setActivePort(operState, NO_TIMESTAMP.value);
    // for subsequent notification of the event mdoule.
    ProtectionStatusChangeNotifier.storeChangedPG(osModuleDBImpl.getProtChgProperties());
    return result;
  }

  /**
   * Reads the protection state (direction) via SNMP and compares it
   * with the parameter 'direction'. Used for making shure that changeDirection()
   * was successful.
   *
   * @param moduleId  database id of the module
   * @param direction Direction east (true)/ west (false). @return True if the current state equals specified direction. @throws SNMPCommFailure SNMP request failed.
   */
  public boolean checkProtectionStatus(int moduleId, boolean direction) throws SNMPCommFailure {
    throw new RuntimeException("checkProtectionStatus() not implemented for FSP " +
            getNetworkElementType());
  }

  /**
   * Returns the optical switch module for the specified index.
   *
   * @param osModuleIndex optical switch module index.
   * @return the optical switch module for the specified index.
   * @throws NoSuchEntityException The optical switch module index doesn't exist.
   */
  public final ModuleOSDBExtension getOSDBImpl(EntityIndex osModuleIndex)
          throws NoSuchEntityException {

    final EntityDBImpl entity = entityDAO.getEntityDBImplOrThrowException(getID(), osModuleIndex);

    if (!(entity instanceof ModuleOSDBExtension)) {
      log.warn("* Index " + osModuleIndex + ": Not an optical switch module (" + entity.getClass() + ")! *");
      throw new NoSuchEntityException("Not an optical switch module. Index " +
              osModuleIndex);
    }

    return (ModuleOSDBExtension) entity;
  }


  /**
   * Returns the startup time of the network element.
   */
  @Override
  @MDPersistenceContext
  public long getColdStartTime() {
    return neDBImpl().getColdStartTime();
  }

  @Override
  @MDTransactional(restart = true)
  public void setColdStartTime(final long coldStartTime) {
    neDBImpl().setColdStartTime(coldStartTime);
  }

  @Override
  public long getColdStartTimeFromDevice(){
    log.error("Must be implemented per NE type! NE="+this);
    return 0;
  }

  /**
   * Checks whether the network element is trapsink.
   *
   * @return true, if the network element is trapsink.
   */
  @Override
  @MDPersistenceContext
  public boolean isTrapsink() {
    NetworkElementDBImpl neDBImpl = neDBImpl();
    if (!serverCtrl.isHaStandalone()) {
      return (neDBImpl.isPrimaryTrapsink() && neDBImpl.isSecondaryTrapsink());
    }
    return neDBImpl.isPrimaryTrapsink();
  }

  /**
   * Sets the administration state of an entity via SNMP.
   * The process is reported via the ProgressStatus event channel.
   *
   * @param entityIndex:  Index of the entity.
   * @param newAdminState The new administration state.
   *                      The request can fail because of
   *                      <li> The entity doesn't exist.
   *                      <li> The SNMP request fails.
   * @param recursive
   */
  public void setEntityAdminState(final EntityIndex entityIndex, final int newAdminState, final boolean recursive)
          throws SNMPCommFailure, MDOperationFailedException {
    throw new MDOperationNotSupportedException("Not yet supported!");
  }

  /**
   * Checks whether a port has currently the LOS alarm.
   *
   * @param portIndex Index of the port.
   * @return True if the port has currently the LOS alarm.
   * @throws NoSuchEntityException There is no such object.
   * @throws SNMPCommFailure       The SNMP communication failed.
   */
  public boolean portHasLOS(final int portIndex)
          throws SNMPCommFailure, NoSuchEntityException {
    boolean portHasLOS;  // the return value

    // 1.) get alarms via SNMP!
    final IntHashMap alarmMap = getIfCurrentAlarmTypes(portIndex);

    // 2.) check for LOS!
    final AlarmTypeHandler alarmTypeHandler = AlarmTypeHandlerImpl.getInstance();
    int alarmType = MIB.FSP.IfCurrAlarm.TYPE_LOSS_OF_SIGNAL;
      int raisingTrapID = alarmTypeHandler.getTrapIdByAlarmTypeCategory(getNetworkElementType(), alarmType, CategoryType.INTERFACE_EVENT);

    portHasLOS = alarmMap.containsKey(raisingTrapID);

    if (!portHasLOS) {
      final PortDBImpl portDBImpl = portDAO.getPortDBImplOrThrowException(getID(), new EntityIndex(portIndex));

      if (portDBImpl.getType() == MIB.If.TYPE_OB_TTP)  // EDFA port?
      {
        alarmType = MIB.FSP.IfCurrAlarm.TYPE_LOSS_OF_OIP;
          raisingTrapID = alarmTypeHandler.getTrapIdByAlarmTypeCategory(getNetworkElementType(), alarmType, CategoryType.INTERFACE_EVENT);
        portHasLOS = alarmMap.containsKey(raisingTrapID);  // the return value
      }
    }

    return portHasLOS;
  }

  protected IntHashMap getIfCurrentAlarmTypes(int portIndex)
    throws SNMPCommFailure
  {
    throw new RuntimeException("Not supported!");
  }

  /**
   * Returns the laser TX state of a port from the network element via SNMP.
   *
   * @param portIndex The index of the port.
   * @return the laser TX state of a port from the network element via SNMP.
   * @throws SNMPCommFailure The SNMP communication failed.
   */
  public int getPortLaserTXStateViaSNMP(EntityIndex portIndex) throws SNMPCommFailure {
    throw new RuntimeException("Not supported by this network element type!");
  }

  /**
   * Sets the laser TX state of a port from the network element via SNMP.
   *
   * @param portIndex    The index of the port.
   * @param laserTXState The laser TX state.
   * @throws SNMPCommFailure The SNMP communication failed.
   */
  public void setPortLaserTXStateViaSNMP(EntityIndex portIndex, int laserTXState) throws SNMPCommFailure {
    throw new RuntimeException("Not supported by this network element type!");
  }

  /**
   * Returns the MIB constant for laser TX state forced on.
   *
   * @return the MIB constant for laser TX state forced on.
   */
  public int getMIBIfTTLaserTXStateForcedOn() {
    throw new RuntimeException("Not supported by this network element type!");
  }

  /**
   * Returns the MIB constant for laser TX state auto.
   *
   * @return the MIB constant for laser TX state auto.
   */
  public int getMIBIfTTLaserTXStateAuto() {
    throw new RuntimeException("Not supported by this network element type!");
  }

  /**
   * Returns the MIB constant for loss of signal.
   *
   * @return the MIB constant for loss of signal.
   */
  public int getMIBIfCurrAlarmTypeLossOfSignal() {
    throw new RuntimeException("Not supported by this network element type!");
  }

  /**
   * Returns the type of network element.
   */
  @Override
  public int getNetworkElementTypeForMTOSI() {
    return getNetworkElementType();
  }

  @Override
  public String getDefaultNetworkElementTypeString() {
    return NEType.getNETypeFor(getNetworkElementType()).getBrandedName();
  }

  @Override
  public boolean isPeer() {
    return false;
  }

  public boolean isLinkUpDownEvent(final EventDTO event) {
    return event.getTrapID() == FSPGenericTrap.LINK_UP || event.getTrapID() == FSPGenericTrap.LINK_DOWN;
  }

  protected void updateNetworkElementPropertiesForEvent(EventDTO event, NetworkElementDTO networkElementDTO) {
    event.sourceNE_ID = networkElementDTO.id;
    event.sourceNEType = networkElementDTO.type;
    event.subnetID = networkElementDTO.subnet_ID;
  }

  public boolean checkEventSource(final EventDTO event) throws NoSuchEntityException {
    final NetworkElement sourceNEPeer = getPeerNetworkElement();
    if (sourceNEPeer != null) {
      try {
        // try to get the entity from source network element!
        getEntityImpl(event.objectIndex);

        // okay, if we are here, then we got the entity -> the source network element is correct!
      } catch (NoSuchEntityException e) {
        // entity was not in source network element -> try to get the entity from peer network element!

        sourceNEPeer.getEntityImpl(event.objectIndex);

        updateNetworkElementPropertiesForEvent(event, getPeerMgr().getPeer().getMOPropertiesWorker().getProperties());
      }
    }
    return false;
  }

  @MDTransactional(restart = true)
  protected void persistAndRevalueTransactional(final AbstractPersistentObject revalueObject, final Map<String, Object> dataMap) {
    AbstractPersistentObject ro = MDPersistenceManager.current().merge(revalueObject);
    EntityUpdaterFactory.getInstance().getEntityUpdater(ro).revalue(ro,new SnmpEntityAbstract( dataMap));
  }

  @MDTransactional(restart = true)
  protected void revalueTransactional(final ManagedObjectDBImpl revalueObject, final Map<String, Object> dataMap) {
    ManagedObjectDBImpl revalueObjectRefreshed = MDPersistenceHelper.refind(revalueObject, revalueObject.getId());
    EntityUpdaterFactory.getInstance().getEntityUpdater(revalueObjectRefreshed).revalue(revalueObjectRefreshed,new SnmpEntityAbstract( dataMap));
  }

  @MDTransactional(restart = true)
  protected void persistTransactional(final ManagedObjectDBImpl revalueObject) {
    MDPersistenceManager.current().merge(revalueObject);
  }

  @MDTransactional(restart = true)
  protected void persistTransactional(Collection<? extends ManagedObjectDBImpl> newManagedObjects) {
    for (ManagedObjectDBImpl managedObjectDB : newManagedObjects) {
      MDPersistenceManager.current().merge(managedObjectDB);
    }
  }

  @MDTransactional(restart = true)
  protected void loadAndRevalueTransactional(Class<? extends ManagedObjectDBImpl> clazz,
                                             int objectId,
                                             final Map<String, Object> dataMap) {
    ManagedObjectDBImpl managedObjectDBImpl = MDPersistenceHelper.find(clazz, objectId);
    if (managedObjectDBImpl != null) {
      EntityUpdaterFactory.getInstance().getEntityUpdater(managedObjectDBImpl).revalue(managedObjectDBImpl,new SnmpEntityAbstract( dataMap));
    } else {
      log.error("Cannot find given object: class: {}, id: {}", clazz, objectId);
    }
  }

  @MDTransactional(restart = true)
  public void deletePersistentObjectTransactional(Class<? extends ManagedObjectDBImpl> clazz, int objectId) {
    log.debug("deletePersistentObjectTransactional: start");
    ManagedObjectDBImpl moObjectDBImpl = MDPersistenceHelper.find(clazz, objectId);
      PersistentObjectHelper.destroy(moObjectDBImpl);
    log.debug("deletePersistentObjectTransactional: ensureEnd");
  }

  /**
   * Methods informs is save config operation supported by current NE
   *
   * @return true if saveConfig is supported, otherwise false
   */
  @Override
  public boolean isSaveConfigSupported() {
    return false;
  }

  /**
   * Methods informs is save config running
   *
   * @return boolean
   */
  @Override
  public boolean isSaveConfigRunning() {
    return isSaveConfigRunning;
  }

  @Override
  public void setSaveConfigRunning(boolean saveConfigRunning) {
    isSaveConfigRunning = saveConfigRunning;
  }

  /**
   * Method returns time period for save config operation.
   * This value is used for scheduling and rescheduling saveConfg operation
   *
   * @return save config time period
   */
  @Override
  public int getSaveConfigTimePeriod() {
    return DEFAULT_SAVECONFIG_TIME_PERIOD;
  }

  @Override
  public void clearSNMPCtrlMap() {
    //dummy method
  }

  public boolean isAdminStateSupported(EntityIndex portIndex, AdministrationStateType newAdminState) {
    return true;
  }

  @Override
  public NEBackupWorker getNeBackupWorker() {
    return neBackupWorker;
  }

  public void setNeBackupWorker(NEBackupWorker neBackupWorker) {
    this.neBackupWorker = neBackupWorker;
  }

  public APSStatusType getAPSStatus() {
    return APSStatusType.APS_STATUS_NOT_APPLICABLE;
  }

  @Override
  public EventToDBObjectLinker getEventToDBObjLinker() {
    return eventHelper;
  }

  @Override
  public boolean isConnectionSupported() {
    return true;
  }

  @Override
  public DBObjectFactory getDbObjectFactory() {
    return dbObjectFactory;
  }

  @Override
  public NetworkElementPersistenceHelper getPersistenceHelper() {
    return persistenceHelper;
  }

  public void setPollingStatusWorker(PollingStatusWorker pollingStatusWorker) {
    this.pollingStatusWorker = pollingStatusWorker;
  }

  @Override
  public TransientObjectHolder initTransientObjectHolder() {
    return new TransientObjectHolderBasis(this).initialize();
  }

  @Override
  public <T extends IInvDataPollingController> T getDataController() {
    return (T) dataController;
  }

  public boolean isFirstDiscoveryInProgress() {
    return firstDiscoveryInProgress;
  }

  public void setFirstDiscoveryInProgress(boolean firstDiscoveryInProgress) {
    this.firstDiscoveryInProgress = firstDiscoveryInProgress;
  }

  public List<IInvPostProcessEventHandler> getPostProcessEventHandlers() {
    return postProcessEventHandlers;
  }

  public CommunicationProtocolsConfiguration getCommunicationProtocolsConf() {
    return communicationProtocolsConfiguration;
  }

  protected void setCommunicationProtocolsConfiguration(CommunicationProtocolsConfiguration communicationProtocolsConf) {
    this.communicationProtocolsConfiguration = communicationProtocolsConf;
  }

  public void resyncLock() {
    resyncLock.lock();
  }

  public void resyncUnlock() {
    resyncLock.unlock();
  }

  /**
   * Return PeerMgr container.
   *
   * @return peerNEImplMap contains to tables order by neIndex and neID.
   */
  @Override
  public PeerMgr getPeerMgr() {
    return peerMgr;
  }

  /**
   * Check to see if the NE has any peers.
   */
  @Override
  public boolean hasPeers() {
    return peerMgr.hasPeers();
  }

  /**
   * This map is a little tricky, the overloaded getPeers() changes the map to
   * have different keys depending on the device type.
   * But, the addPeer method uses the getIndex() which is simply the ID.
   * So, this method should be used with caution.
   * For CP: - not used yet.
   * For CM: neIndex
   * For HN: deviceId
   */
  @Override
  public Map<Integer, NetworkElement> getPeers() {
    return getPeerMgr().getPeersByNeIndex();
  }

  /**
   * Return index of NE.
   *
   * @return int Index.
   */
  @Override
  public int getNeIndex() {
    return neIndex;
  }

  /**
   * This method performs NE specific actions related with ne name change.
   * Currently it is used only for ol SR implementation. Dont use this method.
   *
   * @param newName
   *
   */
  @Deprecated
  protected void neNameChangedActions(final String newName) {
    //do nothing
  }

  public void setNetworkElementType(int networkElementType) {
    this.networkElementType = networkElementType;
  }

  public TopologyNodeHdlrLocal getTopologyNodeHdlrLocal() {
    return topologyNodeHdlrLocal;
  }

  public SubnetHdlrLocal getSubnetHdlrLocal() {
    return subnetHdlrLocal;
  }

  public NetworkElementHdlrLocal getNetworkElementHdlrLocal() {
    return networkElementHdlrLocal;
  }

  public SNMPHandler getSnmpHandler() {
    return snmpHandler;
  }

  //currently used only by fsp1500, fsp3000.
  public ServiceManagerFacade getServiceManagerFacade() {
    return serviceManagerFacade;
  }

  public LineHdlrLocal getLineHdlrLocal() {
    return lineHdlrLocal;
  }

  public int getInitSnmpState() {
    return initSnmpState;
  }

  @Override
  public void setInitSnmpState(int snmpState) {
    this.initSnmpState = snmpState;
  }

  public void setPollingWaitConfig(PollingWaitConfig pollingWaitConfig) {
    this.pollingWaitConfig = pollingWaitConfig;
  }

  public ModelManager getModelManager() {
    return modelManager;
  }

  public NetworkElementPropertiesWorker getNetworkElementPropertiesWorker() {
    return propertiesWorker;
  }

  @Override
  public NetworkElementMOPropertiesWorker getMOPropertiesWorker() {
    return propertiesWorker;
  }

  @Override
  public NetworkElementIDLPropertiesWorker getIDLPropertiesWorker() {
    return propertiesWorker;
  }

  @Override
  public NetworkElementReportPropertiesWorker getReportPropertiesWorker() {
    return propertiesWorker;
  }

  final void setPropertiesWorker(NetworkElementPropertiesWorker propertiesWorker) {
    this.propertiesWorker = propertiesWorker;
  }

  private NetworkElementPollingWorker getPollingWorker() {
    return pollingWorker;
  }

  public void setPollingWorker(NetworkElementPollingWorker pollingWorker) {
    this.pollingWorker = pollingWorker;
  }

  public void setNeTypeValidator(NetworkElementTypeValidator neTypeValidator) {
    this.neTypeValidator = neTypeValidator;
  }

  @Override
  public void validateNEType(int snmpNeType) throws NoSuchNetworkElementException {
    neTypeValidator.validateNEType(getIPAddress(), getNetworkElementType(), snmpNeType);
  }

  @Override
  public NetworkElementNameHdlr getNeNameHdl() {
    return neNameHdl;
  }

  public void setNeNameHdl(NetworkElementNameHdlr neNameHdl) {
    this.neNameHdl = neNameHdl;
  }

  @Override
  public SRWorker getSrWorker() {
    return srWorker;
  }

  public void setSRWorker(final SRWorker srWorker) {
    this.srWorker = srWorker;
  }

  @Override
  public NetworkElementConfigurationHdl getNeConfigHdl() {
    return neConfigHdl;
  }

  public final void setNeConfigHdl(NetworkElementConfigurationHdl neConfigHdl) {
    this.neConfigHdl = neConfigHdl;
  }

  @Override
  public int getNetworkElementType() {
    return networkElementType;
  }

  @Override
  public boolean isDeletionInProgress() {
    return deletionInProgress;
  }


  @Override
  public void setDeletionInProgress(final boolean deletionInProgress) {
    this.deletionInProgress = deletionInProgress;

    getPollingManager().setExecutionAllowed(!deletionInProgress);

    if (deletionInProgress)
      getPollingManager().cancelQueuedPollingTasks();

    if (!isPeer()) {
      for (NetworkElement peer : getPeerMgr().getPeers()) {
        peer.getNetworkElement().setDeletionInProgress(deletionInProgress);
        log.info("setDeletionInProgress value: " + deletionInProgress + ", for peer: " + peer.getNetworkElement().getName());
      }
    }
  }

  @Override
  public void waitForAllPollingsFinished() {
    try {
      getPollingManager().waitForAllPollingsFinished();
    } catch (TimeoutException e) {
      //TODO: do something more?
      log.error(getIdentifierStr(), e);
    }

    if (!isPeer()) {
      for (NetworkElement peer : getPeerMgr().getPeers()) {
        peer.getNetworkElement().waitForAllPollingsFinished();
        log.info("waitForAllPollingsFinished for peer: " + peer.getNetworkElement().getName());
      }
    }
  }

  @Override
  public void logSROperation(SROperationState operationState, String... affectedService) {
    getSrWorker().logSROperation(operationState, affectedService);
  }

  @Override
  public SRLogger getSRLogger() {
    //sr log for ne's which are not sr capable, it doesn't log anything.
    return getSrWorker().getSRLogger();
  }

  public void setMTOSIWorker(NetworkElementMTOSIOperations mtosiWorker) {
    this.mtosiWorker = mtosiWorker;
  }

  @Override
  public <T extends NetworkElementMTOSIOperations> T getMTOSIWorker() {
    return (T) mtosiWorker;
  }

  @Override
  public NeEntityDescWorker getNeEntityDescWorker() {
    return neEntityDescWorker;
  }

  public void setNeEntityDescWorker(NeEntityDescWorker neEntityDescWorker) {
    this.neEntityDescWorker = neEntityDescWorker;
  }

  /**
   * Returns a reference to the belonging network element.
   *
   * @return a reference to the belonging network element.
   */
  public NetworkElementImpl getNetworkElement() {
    return this;
  }

  /**
   * Reads NE name from cache
   *
   * @return NE name
   */
  @Override
  public String getName() {
    return getNeIdentityOrInitializeIfNull().getName();
  }

  public String getDeviceName() {
    return getNeIdentityOrInitializeIfNull().getDeviceName();
  }

  public String getNameID() {
    return getNeIdentityOrInitializeIfNull().getNameID();
  }

  public NodeIdentityType getIdentityType() {
    return getNeIdentityOrInitializeIfNull().getIdentityType();
  }

  public NodeIdentityType getNodeIdentityType() {
    return NodeIdentityType.valueOf(getConfigCtrl().getNEIconLabelSettings().getTreeIconLabelSetting());
  }

  /**
   * Returns the short name of the object.
   *
   * @return the short name of the object.
   */
  public String getShortName() {
    return getName();
  }


  /**
   * Returns the config control object for the NE.
   *
   * @return config control object for the NE.
   */
  @Override
  @Deprecated
  public ConfigCtrl getConfigCTRL() {
    return super.getConfigCtrl();
  }

  /**
   * return entityImpl for a specific entityDBImpl
   *
   * @param entityDBImpl The entity database implementation.
   * @return a single entity entity.
   */
  protected final EntityImpl getEntityImplFromDBImpl(EntityDBImpl entityDBImpl) {
    EntityImpl entityImpl = null;
    if (entityDBImpl != null) {
      entityImpl = DataManagerFactory.getInstance().recreateImpl(this, entityDBImpl);
    }

    return entityImpl;
  }


  /**
   * Sets the system information. This may fail if synchronization with the network element fails.
   *
   * @param location The physical location of this node (e.g., `telephone closet, 3rd floor').
   * @param contact  The textual identification of the contact person for this managed node, together with information on how to contact this person.
   * @return true if a property has changed
   * @throws MDOperationFailedException The request failed.
   */

  public boolean setSysInfo(final String location, final String contact)
    throws MDOperationFailedException, SNMPTimeOutException {
    return setSysInfo(location, contact, false);
  }

  /**
   * Sets the system information. This may fail if synchronization with the network element fails.
   *
   * @param location The physical location of this node (e.g., `telephone closet, 3rd floor').
   * @param contact  The textual identification of the contact person for this managed node, together with information on how to contact this person.
   * @param forceSet Forces set values on device using SNMP
   * @return true if a property has changed
   * @throws MDOperationFailedException The request failed.
   */
  @MDPersistenceContext
  public boolean setSysInfo(final String location, final String contact, final boolean forceSet)
          throws MDOperationFailedException, SNMPTimeOutException, CouldNotSetAttrOnNEException {
    boolean toRet = false;
    final NetworkElementSPProperties sysInfo = getSysInfo();
    final String dbLocation = sysInfo.get(NetworkElementSPProperties.VS.SysLocation);
    final String dbContact = sysInfo.get(NetworkElementSPProperties.VS.SysContact);

    if (forceSet || !location.equals(dbLocation) || !contact.equals(dbContact)) {
      // 1.) set via SNMP!
      if(isSNMPCtrlInitialized()) {
        try {
          if (isSaveConfigSupported() && isSaveConfigRunning()) {
            log.info("Save config operation is currenty running. Locked NEs list:");
            log.info(NetTransactionManager.getLockedNeTable());
            throw new ObjectInUseException("Network element [" + getName() + "] is temporarily reserved for save config operation.");//display correct string for save config proccess.
          }

          if (forceSet || !location.equals(dbLocation))
            setSysLocationViaSNMP(location);

          if (forceSet || !contact.equals(dbContact))
            setSysContactViaSNMP(contact);

          if (isSaveConfigSupported()) {
            getSNMPCtrl().saveConfig(false);
          }
        } catch (SNMPTimeOutException snmptoe) {
          throw snmptoe;
        } catch (SNMPCommFailure snmpCommFailure) {
          throw new MDOperationFailedException(snmpCommFailure.getErrMessage(), snmpCommFailure);
        } catch (ObjectInUseException e) {
          throw new MDOperationFailedException(e.toString(), e);
        }
      } else {
        throw new CouldNotSetAttrOnNEException("Could not update location/contact values on " + this.getIPAddress() + " NE");
      }
      toRet = setLocationAndContactInDB(location, contact, forceSet, dbLocation, dbContact);
    }
    return toRet;
  }

  @MDTransactional(restart = true)
  protected boolean setLocationAndContactInDB(String location, String contact, boolean forceSet, String dbLocation, String dbContact) {
    boolean toRet;// 2.) set in database!
    NetworkElementDBImpl neDBImpl = neDBImpl();
    MDPersistenceManager.current().refresh(neDBImpl);

    if (forceSet || !location.equals(dbLocation))
      neDBImpl.setSysLocation(location);

    if (forceSet || !contact.equals(dbContact))
      neDBImpl.setSysContact(contact);
    toRet = true;
    return toRet;
  }

  /**
   * Method is used for setting system location on NE.
   *
   * @param location String value for location.
   * @throws MDOperationNotSupportedException Throw exception if not aplicable.
   * @throws SNMPCommFailure                  Throw exception if SNMP Communication Failed.
   */
  protected void setSysLocationViaSNMP(final String location) throws MDOperationNotSupportedException, SNMPCommFailure {
    getSNMPCtrl().setStringValue(MIB.System.OID_LOCATION_FOR_SINGLE_REQUEST, location);
  }

  /**
   * Method is used for setting system location on NE.
   *
   * @param contact String value for contact.
   * @throws MDOperationNotSupportedException Throw exception if not aplicable.
   * @throws SNMPCommFailure                  Throw exception if SNMP Communication Failed.
   */
  protected void setSysContactViaSNMP(final String contact) throws MDOperationNotSupportedException, SNMPCommFailure {
    getSNMPCtrl().setStringValue(MIB.System.OID_CONTACT_FOR_SINGLE_REQUEST, contact);
  }

  /**
   * Returns the system information.
   *
   * @return the system information.
   */
  @Override
  @MDPersistenceContext
  public NetworkElementSPProperties getSysInfo() {
    NetworkElementDBImpl neDBImpl = neDBImpl();
    return new NetworkElementSPProperties(neDBImpl.getSysDescr(), neDBImpl.getSysContact(),
            neDBImpl.getName(), neDBImpl.getSysLocation()).setSyncState(isInSyncState());
  }

  /**
   * Checks whether the SNMP trapsink registration shall be done.
   * if  once succeeded - never try to register again
   * @return true, if the SNMP trapsink registration shall be done.
   */
  @MDPersistenceContext
  public boolean doSNMPTrapsinkRegistration() {
    return neDBImpl().doSNMPTrapsinkRegistration();
  }

  @MDTransactional(restart = true)
  public void setNETypeStringInDB(final String neTypeString) {
    neDBImpl().setTypeString(neTypeString);
  }

  /**
   * Writes the name to the network element via SNMP.
   *
   * @param name The new name for the network element.
   * @throws SNMPCommFailure The SNMP communication failed.
   */
  public void setNameViaSNMP(final String name, final boolean synchronously) throws SNMPCommFailure {
    if (synchronously) {
      SNMPCtrl snmpCtrl = getSNMPCtrl();
      snmpCtrl.setStringValue(MIB.System.OID_NAME_FOR_SINGLE_REQUEST, name);
      if (isSaveConfigSupported())
        snmpCtrl.saveConfig(false);//save configuration on the device.
    } else {
      getSNMPCtrl().setNameAsync(MIB.System.OID_NAME_FOR_SINGLE_REQUEST, name);
    }
  }

  /**
   * Returns a string that identifies this polling manager owner.
   * <p>
   */
  @Override
  public String getIdentifierStr() {
    return getNeIdentity().toString();
  }

  @Override
  public PollingPerformer getPollingPerformer() {
    return pollingWorker;
  }


  /**
   * Returns a single entity entity.<br>
   * If the thread is registered as transient object user, then the entity will be got from the entity map. Else from the database.<br>
   * No check for registration as transient object user will be made.
   *
   * @param entityIndex The entity index.
   * @return a single entity entity.
   * @throws NoSuchEntityException There is no such entity.
   */
  @Override
  public EntityImpl getEntityImpl(final EntityIndex entityIndex)
          throws NoSuchEntityException {
    EntityImpl entityImpl = null;  // the return value

    final EntityDBImpl entityDBImpl = EntityDAO.get(id, entityIndex);

    if (entityDBImpl != null) {
      entityImpl = DataManagerFactory.getInstance().recreateImpl(this, entityDBImpl);
    }

    if (entityImpl == null) {
        throw new NoSuchEntityException("No entity with index " + entityIndex + "!");
    }

    return entityImpl;
  }

  /**
   * Checks whether this network element is the start of the connection.
   *
   * @param connectionDB The connection.
   * @return True, if this network element is the start of the connection.
   */
  public final boolean isStartOf(final TransportService connectionDB) {
    return (connectionDB.getStartNEID() == this.getID());
  }


  /**
   * Deletes an entity and removes all related data from database.
   */
  @MDPersistenceContext
  public void deleteEntity(EntityIndex entityIndex) throws MDOperationFailedException {
    if (log.isInfoEnabled()) log.info("{ deleteEntity(" + entityIndex + ")");

    try  // protect debug output!
    {
      // 1.) check whether entity is part of a connection!
      final EntityDBImpl entityDBImpl = EntityDAO.get(this.id, entityIndex);

      if (entityDBImpl == null)
        throw new NoSuchEntityException("No entity with index " + entityIndex + "!");

      entityCRUD.deleteEntity(entityDBImpl,this);

    } catch (MDOperationFailedException e) {
      if (log.isInfoEnabled()) {
        log.info("} deleteEntity(" + entityIndex + ") throws MDOperationFailedException (" + e.getMessage() + ")");
      }
      throw e;
    }
    // notifiy event-ctrl to clear standing alarms
    eventMoNotificationHdlr.notifyEntityRemoved(this.id, new EntityIndex(entityIndex));

    EntityDBImpl entityDBImpl = this.getEntityByIndex(entityIndex);
    final String slotName = "slot " + MODefinitionHelper.getSlotName(entityDBImpl);
    final String shortDescription = ShortDescriptionAttributeDecorator.getInstance().getOrCreate(entityDBImpl);
    // add security event "DelMod" (Delete Module)
    systemEventLogg.addNeEvent(this.getName(), this.getID(), SystemAction.DeleteModule, slotName, shortDescription);


  }

  /**
   * Notifies the server about new traps.
   *
   * @param serverNotificationSet The set of server notifications.
   */
  public void addServerTraps(final Set<EventDTO> serverNotificationSet) {
    for (final EventDTO serverTrapAttributes : serverNotificationSet) {
      evtProcCtrl.getEventProcFacade().addEventDTOServerTrap(serverTrapAttributes);
    }
  }

  /**
   * Returns a reference to the local polling manager.
   */
  @Override
  public PollingManager getPollingManager() {
    return pollingManager;
  }

  /**
   * Creates a server trap attributes object for SNMP write access changes.
   *
   * @param newSNMPWriteAccess new SNMP write access state
   */
  protected void createSNMPWriteAccessServerTrapAttributes(final SNMPWriteAccessStatus newSNMPWriteAccess) {
    // no generic ServerTrapAttributes available!
    if (log.isDebugEnabled()) log.debug("(ignored)", new RuntimeException("no generic ServerTrapAttributes available!"));
  }

  /**
   * Returns the primary or alternative IP address.
   */
  @Override
  public String getIPAddress() {
    return getNeIdentityOrInitializeIfNull().getIpAddressInUse();
  }

  protected NEIdentity getNeIdentityOrInitializeIfNull() {
    if (neIdentity == null) {
      getNeIdentity();
    }
    return neIdentity;
  }

  @MDPersistenceContext
  public synchronized NEIdentity getNeIdentity() {
    NetworkElementDBImpl neDBImpl = neDBImpl();
    if (neIdentity == null) {
      neIdentity = new NEIdentity(neDBImpl);
    } else {
      neIdentity.update(neDBImpl);
    }
    return neIdentity;
  }

  /**
   * Checks whether the IP addresses can be used for this network element.
   *
   * @param primaryIPAddress     The primary IP address.
   * @param alternativeIPAddress The alternative IP address.
   * @param primaryIpAddressChangedNEs set of NEs to be changed.
   * @throws MDOperationFailedException The IP addresses cannot be used for this network element.
   */
  @MDPersistenceContext
  public void checkIPAddresses(String primaryIPAddress, String alternativeIPAddress, Set<String> primaryIpAddressChangedNEs) throws MDOperationFailedException {

    if (networkElementHdlrLocal.isUnmanagedType(networkElementType, primaryIPAddress))
      return;

    final NetworkElementDBImpl neDBImpl = neDBImpl();
    final String oldPrimaryIPAddress = neDBImpl.getPrimaryIPAddress();
    final boolean isNewPrimaryIPAddress = !primaryIPAddress.equals(oldPrimaryIPAddress);
    final boolean isNewAlternativeIPAddress = !alternativeIPAddress.equals(neDBImpl.getAlternativeIPAddress());

    if (isNewPrimaryIPAddress || isNewAlternativeIPAddress) {
      // 1.) check non-existence of IP address!
      if (isNewPrimaryIPAddress) {

        if(primaryIpAddressChangedNEs != null && !primaryIpAddressChangedNEs.isEmpty() ) {
          if(primaryIpAddressChangedNEs.contains(oldPrimaryIPAddress)) {
            return;
          }
        }

        Set<NetworkElementDBImpl> results =
                MDPersistenceHelper.queryByParam(NetworkElementDBImpl.class, new JPAQueryParam("ipAddress", primaryIPAddress));
        if (results != null) {
          final Iterator iterator = results.iterator();
          if (iterator.hasNext()) {
            NetworkElementDBImpl tmpNEDBImpl = (NetworkElementDBImpl) iterator.next();
            throw new MDOperationFailedException("IP address " + primaryIPAddress +
                    " already exists in subnet " + tmpNEDBImpl.getSubnet().getName() +
                    " (" + tmpNEDBImpl.getName() + ")");
          }
        }
      }
    }
  }

  /**
   * Checks whether the IP addresses can be used for this network element.
   *
   * @param primaryIPAddress     The primary IP address.
   * @param alternativeIPAddress The alternative IP address.
   * @throws MDOperationFailedException The IP addresses cannot be used for this network element.
   */
  @MDPersistenceContext
  public void checkIPAddresses(String primaryIPAddress, String alternativeIPAddress) throws MDOperationFailedException {
    checkIPAddresses(primaryIPAddress, alternativeIPAddress, null);
  }

  /**
   * Checks, if a NE's SNMP control object is present.
   *
   * @return true if the SNMP control is present.
   */
  @Override
  public boolean hasSNMPCtrl() {
    return privateSNMPCTRL != null;
  }


  /**
   * Checks whether the network element was already discovered.
   *
   * @return true, if the network element was already discovered.
   */
  @Override
  public boolean isDiscovered() {
    return isDiscovered;
  }


  /**
   * Checks whether the network element is in sync state.
   * In case of not in-sync-state (InSyncState = false) additional information will be logged.
   *
   * @return true, if the network element is in sync state.
   */
  public boolean isInSyncState() {
    boolean isInSyncState = false;  // the return value

    if (isDiscovered()) {
      if (getSNMPResponseStatus() == ResponseStatus.RESPONDING) {
        isInSyncState = true;

        // check for polling!
        for (final long pollingType : pollingManager.getAllPollingTypes()) {
          if (pollingManager.isRunning(pollingType)) {
            if (log.isInfoEnabled())
              log.info("The network element (" + getNetworkElement() + ") is not in sync state (InSyncState=false). Reason: there is a running polling for this network element, pollingType = " + pollingType);
            isInSyncState = false;
            break;
          }
        }

      } else {
        if (log.isInfoEnabled())
          log.info("The network element (" + getNetworkElement() + ") is not in sync state (InSyncState=false). Reason: the network element is not responding, respondingStatus = " + getSNMPResponseStatus().getName());
      }

    } else {
      if (log.isInfoEnabled())
        log.info("The network element (" + getNetworkElement() + ") is not in sync state (InSyncState=false). Reason: the network element is not discovered yet.");
    }

    return isInSyncState;
  }

  /*
  * Returns the IP address of the NE. For peer NE returns ip adrress with peer id.
  */
  @Override
  public String getTransactionKey() {
    return getIPAddress();
  }

  /**
   * @return <code>true</code> if SNMP control is initialized.
   */
  @Override
  public boolean isSNMPCtrlInitialized() {
    return isPeer() ? getPeerMgr().getPeer().isSNMPCtrlInitialized() : privateSNMPCTRL != null;
  }

  /**
   * Returns a reference to a SNMP control object. If this object doesn't exists
   * yet, it will be created.
   */
  @Override
  public SNMPCtrl getSNMPCtrl()
          throws SNMPCommDownException {
    synchronized (snmpCtrlSynchronizer) {
      if (privateSNMPCTRL == null) {
        startInitSNMPCtrl();
        final String ipAddress = getIPAddress();
        final String neIdentifier = (ipAddress.length() > 0) ? (getName() + "(" + ipAddress + ")") : getName();
        //noinspection ConstantConditions
        final String errorMessage = SNMP_COMMUNICATION_DOWN_MSG + neIdentifier + "!";
        throw new SNMPCommDownException(errorMessage);
      }

      return privateSNMPCTRL;
    }
  }

  @Override
  public SNMPAdapter getSNMPAdapter() throws SNMPCommFailure {
    return getSNMPCtrl().getSnmpAdapter();
  }

  /**
   * Check if NE is planned. Planned network elements don't have IP (is ip="")
   *  SFS 9.3.1 FNMS-8590
   * @return
   */
  @Override
  public boolean hasIpAddress() {
    return getIPAddress() != null && !getIPAddress().isEmpty();
  }

  @Override
  public void setSnmpCtrl(SNMPCtrl snmpCtrl) {
    synchronized (snmpCtrlSynchronizer) {
      privateSNMPCTRL = snmpCtrl;
    }
  }

  /**
   * Starts the initialization of the SNMP control.
   * If present already, do nothing.
   */
  public void startInitSNMPCtrl() {
    if (!hasIpAddress()) {
      return;
    }
    synchronized (snmpCtrlSynchronizer) {
      if (privateSNMPCTRL == null && pollingManager.isPollingTypeSupported(PollingType.INIT_SNMP_CTRL.getType())) {
        //noinspection IfStatementWithNegatedCondition
        if (!pollingManager.isRunning(PollingType.INIT_SNMP_CTRL.getType())) {
          PollingFramework.commission(this, PollingType.INIT_SNMP_CTRL);
        } else {
          if (log.isDebugEnabled()) log.debug("NetworkElementBasis." + this + ".startInitSNMPCtrl(): POLLING_TYPE_INIT_SNMP_CTRL already running!");
        }
      } else {
        if (log.isDebugEnabled()) log.debug("NetworkElementBasis." + this + ".startInitSNMPCtrl(): SNMP control already initialized!");
      }
    }
  }

  /**
   * Create and initialize the reference to NE's SNMP control object.
   * If present already, do nothing.
   */
  void deinitSNMPCtrl() {
    AdvaExecutors.newSingleThreadExecutor(new NamedThreadFactory("deinit-snmp"),false)
      .submit(()->{
        synchronized (snmpCtrlSynchronizer) {

          var limit_minutes = FNMPropertyFactory.getPropertyAsInt(
            FNMPropertyConstants.DELETION_TRAPSINK_TIMER_MINUTES,
            FNMPropertyConstants.DELETION_TRAPSINK_TIMER_MINUTES_DEFAULT);
          var limit_milis = TimeUnit.MINUTES.toMillis(limit_minutes);

          if (privateSNMPCTRL != null) {
            var stopwatch = new StopWatch();
            var commander = privateSNMPCTRL.getCommander();
            while (commander != null && commander.unfinishedCommandPending()) {
              try {
                log.debug("Waiting");
                snmpCtrlSynchronizer.wait(1000);
                if (stopwatch.split() > limit_milis) {
                  log.error("Command finish took more than " + limit_minutes + "minutes. Deinitializing SNMP control anyway");
                  break;
                }
              } catch (InterruptedException e) {
                log.error("deinitSNMPCtrl Interrupted");
                Thread.currentThread().interrupt();
              }
            }

            Commander commander1 = privateSNMPCTRL.getCommander();
            if(commander1 != null) {
              commander1.stopMonitoringThread();
            }
            privateSNMPCTRL.getSnmpAdapter().removeUsmUser();
            privateSNMPCTRL = null;
          }
        }
        NetworkElementMonitor.offline(this);
      });

  }

  /**
   * Creates the SNMP control and checks the type of the network element asynchronous.
   *
   * @throws MDOperationFailedException The request failed.
   * @throws SNMPCommFailure            The SNMP communication failed
   */
  @Override
  public void initSNMPCtrl()
          throws SNMPCommFailure, MDOperationFailedException {
    if (!hasIpAddress()) {
      return;
    }
    synchronized (snmpCtrlSynchronizer) {
      if (privateSNMPCTRL == null) {
        this.initSnmpState = NetworkElementImpl.SNMPCtrlInitStatus.INPROGRESS.getValue();
        final SNMPCtrl snmpCTRL = getConfigCtrl().getNECommCtrl().createSNMPCtrl(this);
        snmpCTRL.checkNEType();  // XXX method processNEType(...) will be called asynchronously!
      } else {
        this.initSnmpState = NetworkElementImpl.SNMPCtrlInitStatus.INIT_TRUE.getValue();
        final String warningMessage = "Network element type check already done!";
        log.warn("* NetworkElementBasis.initSNMPCtrl(): " + warningMessage + " *");
        throw new MDOperationNotSupportedException(warningMessage);
      }
    }
  }

  private void setPollingManagerInNe(PollingCtrl.PollingManagerInitialization pollingManagerInitialization,
                                     NetworkElementDBImpl neDBImpl, long[] pollingTypes, PollingManager superDomainPollingManager) {
    pollingManager = pollingManagersController.createPollingManager(pollingTypes, PollingDomainType.NE.getValue(),
            this, getID(), superDomainPollingManager, pollingManagerInitialization, pollingWaitConfig);
    // sometimes during large network discovery on scaling this was updated someplace else and caused concurrent modification exception
    EntityManager entityManager = MDPersistenceManager.current();
    entityManager.refresh(neDBImpl);
    storePollingManagerId(neDBImpl, pollingManager.getID());
  }

  @MDTransactional
  private void storePollingManagerId(NetworkElementDBImpl neDb, int pollingManagerId) {
      neDb.setPollingManagerID(pollingManagerId);
  }

  private void initializePeerManager(NetworkElementDBImpl networkElementDBImpl) {
    if (isPeer()) {
      try {
        NetworkElementDBImpl localNEDBImpl = networkElementDBImpl.getLocal();
        NetworkElementImpl localNetworkElementImpl = TopologyNodeImpl.getNEByID(localNEDBImpl.getId());
        getPeerMgr().addPeer(localNetworkElementImpl);
        localNetworkElementImpl.getPeerMgr().addPeer(this);
      } catch (NoSuchMDObjectException e) {
        log.debug("Given NE:" + networkElementDBImpl.getId() + ", does not exist in registered NEs map", e);
      }
    }
  }

  /**
   * {@inheritDoc}
   */
  @Override
  @MDPersistenceContext
  public String getNetworkElementTypeString() {
    return neDBImpl().getNetworkElementTypeString();
  }

  @MDPersistenceContext
  public String getCustomIcon() {
    return neDBImpl().getCustomIcon();
  }

  @MDPersistenceContext
  public String getSysContact() {
    return neDBImpl().getSysContact();
  }

  @MDPersistenceContext
  public String getUserDescr() {
    return neDBImpl().getUserDescr();
  }

  @MDPersistenceContext
  public String getUserText() {
    return neDBImpl().getUserText();
  }

  @MDPersistenceContext
  public boolean isHidden() {
    return neDBImpl().isHidden();
  }

  @MDPersistenceContext
  public String getSysLocation() {
    return neDBImpl().getSysLocation();
  }

  /**
   * Returns session local DB object.
   *
   * @return session local DB object.
   */
  @Override
  @MDPersistenceContext
  public NetworkElementDBImpl neDBImpl() {
    return MDPersistenceHelper.getObjectById(NetworkElementDBImpl.class, id);
  }

  /**
   * Returns session local DB object.
   *
   * @return session local DB object.
   */
  private NeEventsDBImpl neEventsDBImpl() {
    return MDPersistenceHelper.getObjectById(NeEventsDBImpl.class, id);
  }

  /**
   * Parent of NE is Subnet
   *
   * @return subnet id
   */
  @Override
  public int getParentID() {
    return subnetID;
  }

  /**
   * Returns the type of this tree node.
   */
  @Override
  public com.adva.nlms.common.TopologyNodeType getType() {
    return TopologyNodeType.NETWORK_ELEMENT;
  }


  /**
   * Returns the network element type as defind in the performance XML file.
   */
  @Override
  public NetworkElementType getXMLNetworkElementType() {
    return NetworkElementType.valueOf(String.valueOf(getNetworkElementType()));
  }

  /**
   * Returns the peer network element, or null if not existing.
   *
   * @return the peer network element, or null if not existing.
   */
  @Override
  public NetworkElement getPeerNetworkElement() {
    return getPeerMgr().getPeer();
  }


  @Override
  @MDPersistenceContext
  public NetworkElement findExistingPeerOrCreateNewOne(final int neIndex, final int peerType, String name) {
    if (isPeer()) {
      throw new MDOperationFailedRuntimeException("NetworkElementImpl.getExistingPeerOrCreateNewIfNotExist() cannot be run for peer NE!!!",
              new Throwable());
    }
    synchronized (createPeerSyncObject) {
      NetworkElement peerNe = findExistingPeer(neIndex);
      PeerMgr.peerLog.debug("NEB->findExistingPeerOrCreateNewOne({}), existing peer[{}]", neIndex, peerNe);
      if (peerNe == null) {
        peerNe = createPeerNetworkElement(neIndex, peerType, name);
      }
      return peerNe;
    }
  }

  protected NetworkElement findExistingPeer(final int neIndex) {
    Map<Integer, NetworkElement> peersMap = getPeers();
    if (peersMap.containsKey(neIndex)) {
      return peersMap.get(neIndex);
    }
    return null;
  }

  private NetworkElement createPeerNetworkElement(final int neIndex, final int peerType, final String name) {
    NetworkElementDBImpl peerNEDBImpl;
    try {
      peerNEDBImpl = createPeerInDB(neIndex, peerType, name);
    }catch(EntityExistsException e){
      //in scaling it is possible that many locals has the same peer names - to make them unique, adding local name
      String updatedUniqueName = name + " of " + getName();
      log.warn("[createPeerNetworkElement] Not unique peer {}, trying to update name to {}", name, updatedUniqueName);
      peerNEDBImpl = createPeerInDB(neIndex, peerType, updatedUniqueName);
    }
    NetworkElementImpl peerNEImpl = networkElementFactory.create(peerNEDBImpl, subnetID);
    peerNEImpl.postInit(PollingCtrl.PollingManagerInitialization.MANUAL);
    getPollingManager().setExecutionAllowed(true);
    peerNEImpl.activate();
    peerNEImpl.setDeletionInProgress(isDeletionInProgress());
    topologyNodeHdlrLocal.pushTopologyChange(peerNEImpl.id, ConfigChangeType.NEW);
    topologyChangeEventSender.sendNECreateEvent(NetworkElementHandler.mapToTopologyNodeDTO(peerNEDBImpl));
    return peerNEImpl;
  }

  @MDTransactional(restart = true)
  private NetworkElementDBImpl createPeerInDB(final int neIndex, final int peerType, final String name) {
    //workaround for artf218143
    final Optional<NetworkElementDBImpl> peerNe = NetworkElementDAO.getPeerNe(getID(), neIndex);
    if(peerNe.isPresent()){
      return peerNe.get();
    }
    var peerName = "";
    if (name!= null && !name.isEmpty()) {
      peerName = NetworkElementNameHdlr.checkNEName(name, "", 0); //do it before peer dbimpl creation
    }
    NetworkElementDBImpl peerNEDBImpl;
    if(networkElementType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM){
      Class<? extends NetworkElementDBImpl> peerClass = peerType == 50 ? NetworkElementF3_EFMDBImpl.class : NetworkElementFSP150CM_CP_EFMDBImpl.class;
      int peerId = peerType == 50 ? NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM : NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR;
      peerNEDBImpl = ClassUtils.newInstance(peerClass, "", peerId, neIndex, neDBImpl());
    } else
      peerNEDBImpl = networkElementDBImplFactory.createPeer(neDBImpl(), neIndex);
      if(peerName != null && !peerName.isEmpty()) {
      peerNEDBImpl.setName(peerName);
    }
    return peerNEDBImpl;
  }


    /**
   * Returns the SNMP response status and initializes the state upon the first usage.
   */
  @Override
  public synchronized ResponseStatus getSNMPResponseStatus() {

    if (responseStatusType == ResponseStatus.RESPONSESTATUS_UNKNOWN) {
      boolean hasRaisedAlarm = new AlarmDAO().hasAlarm(getID(), getDCNAlarm());
      responseStatusType = hasRaisedAlarm ? ResponseStatus.NOT_RESPONDING : ResponseStatus.RESPONDING;
      if (responseStatusType == ResponseStatus.RESPONDING) {
        NetworkElementMonitor.online(this);
      } else {
        NetworkElementMonitor.offline(this);
      }
    }

    return responseStatusType;
  }

  /**
   * Updates the response status.
   *
   * @param isResponding true if NE responded, false if not.
   * @return True if the response status has changed.
   */
  @Override
  @MDPersistenceContext
  public boolean updateResponseStatus(final boolean isResponding) {

    lastResponseStatusUpdateToRespondingTimestamp = System.currentTimeMillis();

    ResponseStatus oldResponseStatusType = getSNMPResponseStatus();
    ResponseStatus newResponseStatusType = (isResponding) ? ResponseStatus.RESPONDING : ResponseStatus.NOT_RESPONDING;

    if (dcnLogger.isWarnEnabled()) {
      if (newResponseStatusType == ResponseStatus.NOT_RESPONDING && oldResponseStatusType == ResponseStatus.RESPONDING) {
        dcnLogger.warn(String.format("%s was unreachable while executing %s", getIPAddress(), SimplePollingLogger.getCallerStackTrace()));
      }
    }

    if (dcnLogger.isInfoEnabled()) {
      dcnLogger.info("NE[" + getIPAddress() + "][" + getID() + "] responseStatusType[" + oldResponseStatusType + "], newResponseStatusType[" + newResponseStatusType + "]");
      if (newResponseStatusType == ResponseStatus.NOT_RESPONDING && oldResponseStatusType != ResponseStatus.NOT_RESPONDING) {
        dcnLogger.info("NE[" + getIPAddress() + "][" + getID() + "]");
      }
    }

    responseStatusLock.lock();
    try {
      if ((newResponseStatusType == oldResponseStatusType)) {
        return false;   // no update needed
      }

      // response status has changed! Set new responseStatusType
      this.responseStatusType = newResponseStatusType;

      if (!isPeer()) {
        eventProcNotificationHdlr.notifyNeRespondStatusChange(this, newResponseStatusType);
        neResponseStateHdlr.responseStatusChanged(getID(), newResponseStatusType);
      }

      // create event!
      raiseOrClearDcnAlarm(!isResponding);
    }
    finally {
      responseStatusLock.unlock();
    }

    if (dcnLogger.isInfoEnabled()) {
      dcnLogger.info("NEBImpl->updateResponseStatus: NE[" + getIPAddress() + "][" + getID() + "], isResponding[" + isResponding + "]");
    }

    if (!isResponding)
      checkAlternativeIPAddress();

    // notify clients!
    pushTopologyChange(ConfigChangeType.CHANGED);
    //notify sync manager
    SyncDiscovery.instance().triggerAssync(SyncDiscoveryTriggerType.NEUpdated, new SyncDiscoveryTriggerProp(getNeIdentity().getNeId(),newResponseStatusType, null ));

    SyncTopologyGraphChangeListener.instance().addAffectedNeID(getNeIdentity().getNeId());
    SyncTopologyGraphChangeListener.instance().addAffectedGnssNetworkNEId(getNeIdentity().getNeId());
    updateResponseStatusForPeers(isResponding);

    unsupportedNetworkElementsHandler.handle(this.neDBImpl());

    if (isResponding) {
      NetworkElementMonitor.online(this);
    } else {
      NetworkElementMonitor.offline(this);
    }

    return true;
  }

  public void raiseOrClearDcnAlarm(boolean raise) {
    eventCtrl.getEventProcFacade().addServerAlarm(getDCNAlarm(),
            raise ? EventType.RAISED : EventType.CLEARED, this);
    if (!raise) {
      lastDcnClearTimestamp = System.currentTimeMillis();
    }
  }

  public void clearDcnAlarmIfExists() {
    if (alarmDao.hasAlarm(id, getDCNAlarm())) {
      raiseOrClearDcnAlarm(false);
    }
  }

protected int getDCNAlarm(){
  return FSP_NMTraps.NE_DCN_NO_RESPONSE;
  }
  /**
   * Performs actions related with updating response status on peers connected to this NE
   */
  protected void updateResponseStatusForPeers(final boolean isLocalNEResponding) {
    for (NetworkElement ne : getPeers().values()) {
      ne.pushTopologyChange(ConfigChangeType.CHANGED);
    }
  }

  /**
   * Checks the usage of the alternative IP address.
   */
  private void checkAlternativeIPAddress() {
    final NetworkElementDBImpl neDBImpl = neDBImpl();
    final boolean oldAlternativeIPAddressUsage = neDBImpl.useAlternativeIPAddress();
    boolean newAlternativeIPAddressUsage = oldAlternativeIPAddressUsage;

    // 1.) check usage!
    if (oldAlternativeIPAddressUsage) {
      // alternative -> primary!
      newAlternativeIPAddressUsage = false;
    } else {
      // primary -> alternative!
      if (neDBImpl.hasAlternativeIPAddress()) {
        // alternative -> primary!
        newAlternativeIPAddressUsage = true;
      }
    }

    // 2.) set usage!
    if (newAlternativeIPAddressUsage != oldAlternativeIPAddressUsage) {
      setIsNewAlternativeIPUsed(neDBImpl, newAlternativeIPAddressUsage);
      // 2.2) handle SNMP CTRL!
      deinitSNMPCtrl();
      startInitSNMPCtrl();
    }
  }

  @MDTransactional(restart = true)
  private void setIsNewAlternativeIPUsed(NetworkElementDBImpl neDBImpl, boolean newAlternativeIPAddressUsage) {
    neDBImpl = MDPersistenceHelper.refind(neDBImpl, neDBImpl.getId());
    neDBImpl.useAlternativeIPAddress(newAlternativeIPAddressUsage);
    getNeIdentityOrInitializeIfNull().refresh();
  }

  /**
   * Object toString() method
   */
  public final String toString() {
    StringBuilder sb = new StringBuilder(this.getClass().getSimpleName());
    sb.append(" Id[").append(getID()).append("] ");
    sb.append("Name[").append(neIdentity == null ? "null" : getName()).append("] ");
    sb.append("Ip[").append(neIdentity == null ? "null" : getIPAddress()).append("] ");
    sb.append("Type[").append(getNetworkElementType()).append("] ");
    return sb.toString();
  }

  @Override
  public final String getIpAddressNeNameIdentifier() {
    return getIPAddress() + " (" + getName() + ")";
  }

  // ------------------------------------------------------------------------------------------------------------------

  @Override
  public abstract SynchronizationContainer getSynchronizationContainer();

  public DBConsistencyCheckFactory getDbConsistencyCheckFactory() {
    return dbConsistencyCheckFactory;
  }

  @Override
  public OwnerType getOwnerType() {
    return OwnerType.NE;
  }

  @Override
  public boolean hasLimitations(long pollingType) {
    if (pollingType == PollingType.CONTINUOUS_DISCOVERY.getType()) {
      return isDiscovered();
    } else if (pollingType == PollingType.KEEP_ALIVE.getType()) {
      return !isDiscovered();
    }
    return false;
  }

  @Override
  public Integer getSubOwnerId() {
    if (isPeer()) {
      NetworkElementDBImpl localDBImpl = neDBImpl().getLocal();
      return (localDBImpl != null) ? localDBImpl.getId() : null;
    }
    return null;
  }

  private NetTransactionDomainContext netTransactionDomainContext = new NetTransactionDomainContext();

  @Override
  public void onNetTransactionOpen() {
    netTransactionDomainContext.onNetTransactionOpen(this);
  }

  @Override
  public void onNetTransactionClose() {
    netTransactionDomainContext.onNetTransactionClose(this);
  }

  @Override
  public void saveConfig(boolean synchronously) throws CommunicationException {
    try {
      getSNMPCtrl().saveConfig(synchronously);
    } catch (SNMPCommFailure snmpCommFailure) {
      throw new CommunicationException(snmpCommFailure);
    }
  }

  /**
   * Gives information if specified NE supports automatic trapsink registering during NE discovery
   *
   * @return true if supported
   */
  public boolean isTrapsinkRegisterSupported() {
    return true;
  }

  public boolean isServiceConnected() {
    return serviceManagerFacade.isNetworkElementUsedInService(getID());
  }

  public boolean isEthRingConnected(){
    return !ethernetRingPathElementDBImplDAO.getByNeId(getID()).isEmpty();
  }

  public void enableBulkTrap() {
    //do nothing
  }
  @Override
  public boolean isBulkTrapSupportedForNE() {
    boolean neTypeSupported = Properties.Property.BULK_TRAP_DEVICE_TYPES.getSet().contains(getNetworkElementType());
    int currentNeVersion = DeviceVersion.instance.getVersionNumber(getCurrentNemiSoftwareVersion());
    int supportedNeVersion = DeviceVersion.instance.getVersionNumber("8.5.1-000");
    return neTypeSupported && currentNeVersion >= supportedNeVersion;
  }

  @Override
  public VariableBinding prepareBulkTrapVB(String ipAddress) {
    //do nothing
    return null;
  }

  @Override
  public void setBulkTrapStatusInDB() {
    //do nothing
  }

  public boolean trapRegistrationSupported(){
    return true;
  }

  @Override
  public long getLastStartTime() {
    // check all over the place to find the most recent NE start time
    EventDAO eventDao = eventCtrl.getEventHelper().getEventDAO();
    long startTime = eventDao.getLastColdOrWarmStart(getID()), // warm / cold start event
         coldStartTime = getColdStartTime(), // time on NE
         rebootTimestamp = getNeRebootTimestamp(); // time from event framework
    if (coldStartTime > startTime) {
      startTime = coldStartTime;
    }
    if (rebootTimestamp > startTime) {
      startTime = rebootTimestamp;
    }
    return startTime;
  }


  /**
   * Get NE specific CLI properties.
   */
  @Override
  @MDPersistenceContext
  public CLIPropertiesData getCLIProperties() {
    return cliPropertiesService.getCLIPropertiesByNeId(getID());
  }



  /**
   * Sets the CLI properties.
   * CLI controls have to be rebuild next time.
   */
  public void setCLIProperties(final CLIPropertiesData cliProperties)
          throws BadValueException {
    if (log.isDebugEnabled()) log.debug(cliProperties.toString());
    final boolean propertiesChanged;
    propertiesChanged = setCLIPropertiesTransaction(cliProperties);

    if (getPollingManager().isPollingTypeSupported(PollingType.DISCOVER_CLI_INVENTORY.getType()) && propertiesChanged)
    {
      getTopologyNodeHdlrLocal().pushTopologyChange(getID(), ConfigChangeType.CHANGED);
      //Run Polling on the GPS Receiver
      PollingFramework.commission(this, PollingType.DISCOVER_CLI_INVENTORY, new GPSReceiverPollingParameters(
                      1,   //neIndex
                      1,   //shelfIndex
                      1,   //slotIndex
                      1));

      gnssAssuranceHdlr.saveNeDataAssync(neDBImpl(),!getSNMPResponseStatus().equals(ResponseStatus.RESPONDING));

    }
    else if(NEUtils.isCustomGNSSDevice(neDBImpl().getNetworkElementType()) && propertiesChanged)
      gnssAssuranceHdlr.saveNeDataAssync(neDBImpl(), false);
  }

  protected void updateCLIPropertiesData(SNMPPropertiesData snmpPropertiesData)
  {
    //we do not need to do nothing in most cases
  }

  @MDTransactional(restart = true)
  protected boolean setCLIPropertiesTransaction(final CLIPropertiesData cliProperties) throws BadValueException {
    final boolean propertiesChanged;
    try {
      propertiesChanged = neDBImpl().setCLIProperties(cliProperties);
      return propertiesChanged;
    } catch (BadValueException bv) {
      log.warn("* NetworkElement.setCLIProperties(): " + bv.getMessage() + " *");
      throw bv;
    }
  }

  @Override
  public CapabilitiesRegistry getCapabilityRegistry() {
    return capabilitiesRegistry;
  }

  @Override
  public void unregisterIpFromTrapsink(String ipAddress) throws MDOperationFailedException {
    try {
      getSNMPCtrl().unregisterIpFromTrapsink(ipAddress);
    } catch (Exception e) {
      throw new MDOperationFailedException(String.format("Unable to unregister %s from trap sink of %s", ipAddress,
              getIPAddress()), e);
    }
  }

  public long getLastInitSnmpCtrlTimestamp() {
    return lastInitSnmpCtrlTimestamp;
  }

  public void setLastInitSnmpCtrlTimestamp(long lastInitSnmpCtrlTimestamp) {
    this.lastInitSnmpCtrlTimestamp = lastInitSnmpCtrlTimestamp;
  }

  public long getLastResponseStatusUpdateToRespondingTimestamp() {
    return lastResponseStatusUpdateToRespondingTimestamp;
  }

  public long getLastDcnClearTimestamp() {
    return lastDcnClearTimestamp;
  }

  public boolean isColdStartTimeCheckDone() {
    return coldStartTimeCheckDone;
  }

  @Override
  public void setColdStartTimeCheckDone(boolean coldStartTimeCheckDone) {
    this.coldStartTimeCheckDone = coldStartTimeCheckDone;
  }

  public ServerStateAPI getServerState() {
    return serverState;
  }

  @Override
  public IdentificationKey getIdentificationKey() {
    return getNeIdentity().getIdentificationKey();
  }

  // ------------------------------------------------------------------------------------------------------------------

  private Lock pollingDomainLock = new ReentrantLock();

  @Override
  public void acquireLock() {
    pollingDomainLock.lock();
  }

  @Override
  public void releaseLock() {
    pollingDomainLock.unlock();
  }

  // ------------------------------------------------------------------------------------------------------------------

}
