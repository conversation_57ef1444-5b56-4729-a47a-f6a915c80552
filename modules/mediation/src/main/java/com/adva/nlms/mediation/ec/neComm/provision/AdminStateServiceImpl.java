/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: asowa
 */

package com.adva.nlms.mediation.ec.neComm.provision;

import com.adva.apps.sm.Definition;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.util.StringUtils;
import com.adva.nlms.mediation.config.dto.RestAttributeWithConversion;
import com.adva.nlms.mediation.config.dto.attr.EcStateManageEntityAttr;
import com.adva.nlms.mediation.config.f8.croma.api.CromaMOService;
import com.adva.nlms.mediation.config.f8.croma.provision.api.SlcEqualizationDirection;
import com.adva.nlms.mediation.config.f8.croma.slc.api.Slc;
import com.adva.nlms.mediation.config.f8.sm.EntityAttributesEcUpdater;
import com.adva.nlms.mediation.ec.model.EcModel;
import com.adva.nlms.mediation.ec.neComm.provision.common.EcProvisionException;
import com.adva.nlms.mediation.ec.neComm.provision.mapping.F8AdminStateMapper;
import com.adva.nlms.mediation.ec.neComm.provision.mapping.model.F8AdminState;
import com.adva.nlms.mediation.ec.support.EcEntityIndex;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionException;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Service
public class AdminStateServiceImpl implements AdminStateService {

  private static final String SLC_ENTITY_INDEX_PREFIX = EcEntityIndex.getEcEntityIndex("/mit/me/1/croma/slc/").toStringIndex();
  private final CromaMOService cromaMOService;
  private final EntityAttributesEcUpdater attibutesEcUpdater;

  public AdminStateServiceImpl(final CromaMOService cromaMOService, EntityAttributesEcUpdater attibutesEcUpdater) {
    this.cromaMOService = cromaMOService;
    this.attibutesEcUpdater = attibutesEcUpdater;
  }

  @Override
  public void setSlcAdminState(int neId, int slcId, AdminState adminState, SlcEqualizationDirection direction) throws EcProvisionException {
    if (direction != null) {
      setAdminStateForSlcEndpoint(neId, adminState, slcId, direction);
    } else {
      // if direction is not set, change admin state for both endpoints
      setAdminStateForSlcEndpoint(neId, adminState, slcId, SlcEqualizationDirection.PATH_AZ);
      setAdminStateForSlcEndpoint(neId, adminState, slcId, SlcEqualizationDirection.PATH_ZA);
    }
  }


  private void setAdminStateForSlcEndpoint(int neId, AdminState adminState, int slcId,
                                           SlcEqualizationDirection direction) throws EcProvisionException {
    String path = null;
    try {
      path = getPathForSlcEnpoint(slcId, direction);
      setSlcAdminStateDirectly(neId, path, adminState);
    } catch (ProvisionException e) {
      throw new EcProvisionException(StringUtils.format("Cannot change admin state on slc {} : {} to {}",
        slcId, path, adminState), e);
    }
  }

  private Optional<Slc> getSlcForNeAndSlcId(int neId, int slcIdentifier) {
    return cromaMOService.getSlcForNeAndSlcId(neId, slcIdentifier);
  }

  private String getPathForSlcEnpoint(int slcId, SlcEqualizationDirection direction) {
    StringBuilder path = new StringBuilder(SLC_ENTITY_INDEX_PREFIX);
    path.append(slcId);
    if (direction == SlcEqualizationDirection.PATH_AZ) {
      path.append(EcModel.MocSlc.A_ENDPOINT.getPath());
    } else if (direction == SlcEqualizationDirection.PATH_ZA) {
      path.append(EcModel.MocSlc.Z_ENDPOINT.getPath());
    } else {
      throw new IllegalArgumentException("Invalid direction == null");
    }
    return path.toString();
  }

  private void setSlcAdminStateDirectly(int neId, String path, AdminState adminState) {
    if (AdminState.UP == adminState || AdminState.DOWN == adminState) {
      F8AdminState f8AdminState = F8AdminStateMapper.toF8AdminState(adminState);

      Map<RestAttributeWithConversion, Object> attributes = new HashMap<>();
      attributes.put(EcStateManageEntityAttr.SM_ADMIN_STATE, f8AdminState.getAdminState());
      attributes.put(EcStateManageEntityAttr.SM_ISST, f8AdminState.getIsst());
      try {
        attibutesEcUpdater.updateAttributesDirectly(neId, path, EcStateManageEntityAttr.class, attributes);
      } catch (Exception e) {
        throw new ProvisionException(StringUtils.format("Couldn't change admin state for entity {}", path), e);
      }
    } else {
      throw new ProvisionException("Given admin state value is not supported for SLC. Possible admin states are UP or DOWN.");
    }
  }

  @Override
  public void setAdminState(int neId, String entityAid, Definition.AdminState adminState) {
    F8AdminState f8AdminState = F8AdminStateMapper.mapAdminStateAttrs(adminState);

    Map<RestAttributeWithConversion, Object> attributes = new HashMap<>();
    attributes.put(EcStateManageEntityAttr.SM_ADMIN_STATE, f8AdminState.getAdminState());
    attributes.put(EcStateManageEntityAttr.SM_ISST, f8AdminState.getIsst());

    try {
      attibutesEcUpdater.updateAttributes(neId, entityAid, EcStateManageEntityAttr.class, attributes);
      attibutesEcUpdater.syncEncEntity(neId, entityAid, F8AdminStateMapper.mapAdminStateValueAttr(adminState));
    } catch (Exception e) {
      throw new ProvisionException(StringUtils.format("Couldn't change admin state for entity {}", entityAid), e);
    }
  }
}
