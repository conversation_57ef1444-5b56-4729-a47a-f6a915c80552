/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: smuller
 */

package com.adva.nlms.mediation.topology;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.property.FNMPropertyConstants;
import com.adva.nlms.common.property.FNMPropertyFactory;
import com.adva.nlms.common.snmp.NoSuchMDObjectException;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.common.persistence.MDPersistenceHelper;
import com.adva.nlms.mediation.common.persistence.MDPersistenceManager;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementDAO;
import com.adva.nlms.mediation.config.NetworkElementDBImpl;
import com.adva.nlms.mediation.config.NetworkElementImpl;
import com.adva.nlms.mediation.config.NoSuchNetworkElementException;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import jakarta.inject.Singleton;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Singleton
public class RegisteredTreeNodesHdlr implements NEDataProvider{

  public static final int UNRECOGNIZED_NE = Integer.MAX_VALUE;
  private static final Logger log = LoggerFactory.getLogger(RegisteredTreeNodesHdlr.class);
  private static final int NE_CACHE_SIZE = FNMPropertyFactory.getPropertyAsInt(FNMPropertyConstants.LAST_NE_BUFFER_SIZE, 10000);
  private static final int NE_CACHE_EXPIRE = FNMPropertyFactory.getPropertyAsInt(FNMPropertyConstants.NE_CACHE_EXPIRE_IN_HOURS, 24);

  private static RegisteredTreeNodesHdlr instance;

  @Autowired
  private NetworkElementDAO networkElementDAO;

  private LoadingCache<String, NetworkElement> neCacheByIp = CacheBuilder
      .newBuilder()
      .maximumSize(NE_CACHE_SIZE)
      .expireAfterWrite(NE_CACHE_EXPIRE, TimeUnit.HOURS)
      .build(new CacheLoader<String, NetworkElement>() {
        @Override
        public NetworkElement load(String ipAddress) throws NoSuchNetworkElementException {
          NetworkElement sourceNE;
          log.debug("Trying to find and cache NE with ip[{}].", ipAddress);
          int neID = networkElementDAO.getNEIdByIPAddress(ipAddress);
          sourceNE = instance.getNEByID(neID);
          if( ! Objects.equals(sourceNE.getIPAddress(), ipAddress)){
            log.warn("We are putting in cache NE[{}] under ip[{}] " +
                "which is not its primary ip under which nms performs communication", sourceNE, ipAddress);
          }
          log.debug("Caching under ip[{}] NE[{}]", ipAddress, sourceNE);
          return sourceNE;
        }
      });

  private RegisteredTreeNodesHdlr() {}

  public static RegisteredTreeNodesHdlr getInstance() {
// Please FindBugs (http://www.cs.umd.edu/~pugh/java/memoryModel/DoubleCheckedLocking.html)
//    if(instance != null) {
//      return instance;
//    }

    synchronized(RegisteredTreeNodesHdlr.class){
      if(instance == null){
        instance = new RegisteredTreeNodesHdlr();
      }
      return instance;
    }
  }

  // Map of all nodes, registered with the ORB
  private Map<Integer, TopologyNodeImpl> regTreeNodes = new ConcurrentHashMap<>();


  public void addTreeNode(final int id, TopologyNodeImpl node) {
    regTreeNodes.put(id, node);
    logCacheChange("Added", node, id);
  }
  
  public void removeTreeNode(final int id) {
    invalidateIpCache(id);
    TopologyNodeImpl removedNode = regTreeNodes.remove(id);
    logCacheChange("Removed", removedNode, id);
  }

  private void logCacheChange(String operation, TopologyNodeImpl node, int id) {
    log.info(operation+"Id[{}] NE[{}] to cache[{}]", id, node, this.hashCode());
    if(log.isTraceEnabled()) {
      log.trace("", new Exception("Stacktrace of logCacheChange."));
    }
  }

  private void invalidateIpCache(int id) {
    try {
      final NetworkElement ne = getNEByID(id);
      neCacheByIp.invalidate(ne.getIPAddress());
    } catch (NoSuchNetworkElementException e) {
      log.debug("Didn't find ", e);
    }
  }

  /**
   * Returns the tree node by ID.
   *
   * @param id of node
   * @return NetworkTreeNodeImpl object with speciefied id
   * @throws NoSuchMDObjectException
   *          when node with specified id doesn't exist
   */
  public TopologyNodeImpl getByID (int id) throws NoSuchMDObjectException {
    TopologyNodeImpl treeNode = regTreeNodes.get(id);
    if (treeNode == null) {
      throw new NoSuchMDObjectException("Network tree node ID " + id + " is unknown.");
    }
    return treeNode;
  }

  /**
   * Returns a reference the network element implementation.
   *
   * @param id The tree node ID.
   * @return a reference the network element implementation.
   * @throws com.adva.nlms.mediation.config.NoSuchNetworkElementException The tree node doesn't exist or it's not a NE.
   */
  public NetworkElement getNEByID (final int id) throws NoSuchNetworkElementException {
    TreeNode treeNode = null;
    try {
      treeNode = getByID(id);
    } catch (NoSuchMDObjectException e) {
      throw new NoSuchNetworkElementException(e);
    }
    if (treeNode.getType() != com.adva.nlms.common.TopologyNodeType.NETWORK_ELEMENT) {
      throw new NoSuchNetworkElementException("NE ID " + id + " is unknown.");
    }
    return (NetworkElementImpl) treeNode;
  }

  public NetworkElement getNEImpl(final String ipAddress, boolean checkWithSystemIpAlso) throws NoSuchNetworkElementException {
    //get ID of NE from database for specific IPAddress
    int neID = networkElementDAO.getNEIdByIPAddress(ipAddress);
    if (neID == 0) {
      if (checkWithSystemIpAlso) { //IPV6/IPV4 F7 case
        neID = networkElementDAO.getNEIdBySystemIPAddress(ipAddress);
        if (neID == 0) {
          throw new NoSuchNetworkElementException("No network element with IP " + ipAddress + "!");
        }
      } else {
        throw new NoSuchNetworkElementException("No network element with IP " + ipAddress + "!");
      }
    }
    return getNEByID(neID);
  }
  /**
   * Returns type of parent NE for give NE (described by neId).
   * In case we cannot find base NE we return {@link #UNRECOGNIZED_NE} as parent.
   * In case there is not peer for the give NE we return base NE type.
   * @param neId db id of NE.
   * @return type of NE.
   */

  public int getParentNeTypeForNe(final int neId){
    NetworkElement baseNe;
    try {
      baseNe = getNEByID(neId);
    } catch (NoSuchNetworkElementException e) {
      log.error(e.getMessage());
      return UNRECOGNIZED_NE;
    }
    return baseNe.getPeerNetworkElement() == null ? baseNe.getNetworkElementType() : baseNe.getPeerNetworkElement().getNetworkElementType();
  }

  /**
   * Returns type of NE based on db id.
   * We search for NE first in cache on TopologyNodeImpl and if we don't find it there
   * we search in db if we also don't find it in db we will return ne type unknown {@link #UNRECOGNIZED_NE}.
   *
   * @param neId db id of NE.
   * @return type of NE.
   */
  @MDPersistenceContext
  public int getNeType(int neId) {
    try {
      return getNEByID(neId).getNetworkElementType();
    } catch (NoSuchNetworkElementException e) {

      //Entity manager not initialized, we assume this situation as normal because of unit tests.
      if(MDPersistenceManager.current() == null){
        return UNRECOGNIZED_NE;
      }

      //if we got this during normal work of nms it means that ne was probably already removed (or during deletion) or wasn't yet discovered.
      // this block is used mainly for call of isMtosiNamingEnabled() from context where cache on TopologyNodeImpl is not yet initialized, i.e. DBVersionHandler;
      try {
        NetworkElementDBImpl ne = MDPersistenceHelper.find(NetworkElementDBImpl.class, neId);
        if(ne == null){
          log.warn("Couldn't get network element[" + neId + "] network element returned as unrecognized", new Exception());
          return UNRECOGNIZED_NE;
        }
        return Integer.parseInt(ne.getType());
      } catch (NumberFormatException re) {
        log.error("Couldn't get int type of network element[" + neId + "] network element returned as unrecognized", re);
      }
    }
    return UNRECOGNIZED_NE;
  }

  public void removeNEFromCache(String ip){
    neCacheByIp.invalidate(ip);
  }

  public NetworkElement getByIpAddress(String ipAddress){
    try {
      final NetworkElement ne = neCacheByIp.get(ipAddress);
      //if we have different ip in ne than key ip we should invalidate cache and search again.
      //temporary workaround for FNMD-18279 - [euNetworks] Service provisioning fails with SNMP down error for wrong IP address
      if(Objects.equals(ne.getIPAddress(), ipAddress)){
        return ne;
      } else {
        neCacheByIp.invalidate(ipAddress);
        return neCacheByIp.get(ipAddress);
      }
    } catch (ExecutionException | RuntimeException e) {
      if( e.getCause() instanceof NoSuchNetworkElementException){
        log.debug("Didn't find ne with ip[{}]", ipAddress);
      } else if (ipAddress != null) {
        log.error("Unexpected error during NE cache update", e);
      }
    }
    return null;
  }

  public boolean isNEAvailable(int neId) {
    return regTreeNodes.containsKey(neId);
  }

  @Override
  public NEData getNeData(int neId) {
    NetworkElement cachedNe = null;
    try {
      cachedNe = getNEByID(neId);
    } catch (NoSuchNetworkElementException e) {
      //nothing special happened, ne already removed, or didn't go to cache
    }
    if(cachedNe == null) {
      log.warn("Network element[{}] not found in cache. Searching in DB.", neId);
      NetworkElementDBImpl neDBImpl = networkElementDAO.getById(neId);
      if(neDBImpl == null) {
        log.warn("Network element[{}] was not found, probably we are in NE deletion phase returning empty NE.",neId);
        return createEmptyNEData(neId);
      }
      return createNEData(neId, neDBImpl);
    }
    return createNEData(neId, cachedNe);
  }

  public NEData getNeData(String ipAddress) {
    NetworkElement cachedNe = getByIpAddress(ipAddress);
    if(cachedNe != null) {
      return createNEData(cachedNe.getID(), cachedNe);
    } else {
      log.warn("Network element[{}] not found in cache. Searching in DB.", ipAddress);
      NetworkElementDBImpl neDBImpl = networkElementDAO.getNEByIPAddress(ipAddress);
      if(neDBImpl == null) {
        log.warn("Network element[{}] was not found, probably we are in NE deletion phase.",ipAddress);
        return null;
      }
      return createNEData(neDBImpl.getId(), neDBImpl);
    }
  }

  private NEData createEmptyNEData(int neId){
    return new NEData(neId, NeTypeIds.NETWORK_ELEMENT_TYPE_ANY, "", "", "", "");
  }

  private NEData createNEData(int neId, NetworkElement ne) {
    return new NEData(neId, ne.getNetworkElementType(), ne.getNetworkElementTypeString(), ne.getName(), ne.getIPAddress(), ne.getCurrentNemiSoftwareVersion());
  }

  private NEData createNEData(int neId, NetworkElementDBImpl ne) {
    return new NEData(neId, ne.getNetworkElementType(), ne.getNetworkElementTypeString(), ne.getName(), ne.getIPAddress(), ne.getCurrentSoftwareVersion());
  }
}
