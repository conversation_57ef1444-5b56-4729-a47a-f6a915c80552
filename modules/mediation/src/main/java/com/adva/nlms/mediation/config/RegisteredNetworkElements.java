/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.mediation.config;

public interface RegisteredNetworkElements {

  NetworkElement getNeById(int neId) throws NoSuchNetworkElementException;

  NetworkElementImpl getNEImpl(final String ipAddress, boolean checkWithSystemIpAlso) throws NoSuchNetworkElementException;

  boolean isNEAvailable(int neId);

  int getNeType(int neId);

  void removeNeFromCache(String ip);

  NetworkElement getByIpAddress(String ipAddress);

  int getSubnetId(int neId) throws NoSuchNetworkElementException;
}
