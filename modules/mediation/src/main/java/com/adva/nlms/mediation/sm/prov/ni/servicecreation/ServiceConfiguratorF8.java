/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: spyrosm
 */

package com.adva.nlms.mediation.sm.prov.ni.servicecreation;

import com.adva.common.model.template.Keyword;
import com.adva.apps.sm.Definition;
import com.adva.nlms.common.sm.ProtMechanism;
import com.adva.nlms.common.yp.Parameters;
import com.adva.nlms.mediation.bean.provider.api.BeanProvider;
import com.adva.nlms.mediation.common.serviceProvisioning.ProvFSPR7Properties;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mltopologymodel.helpers.MLF8EntityHelper;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLConnectionPointDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLTrailDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLConnectionPointFacility;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLTEPropertyKey;
import com.adva.nlms.mediation.mltopologymodel.service.intent.implementation.db.ServiceIntentDBImpl;
import com.adva.nlms.mediation.sm.prov.SMProvException;
import com.adva.nlms.mediation.sm.prov.SMProvParams;
import com.adva.nlms.mediation.sm.prov.SMProvParamsF8;
import com.adva.nlms.mediation.sm.prov.cp.OCSProcessingParameters;
import com.adva.nlms.mediation.sm.prov.ni.NIOCSProcessingParameters;
import com.adva.nlms.mediation.sm.prov.ni.ServiceEnumMaps;
import com.adva.nlms.mediation.sm.prov.ni.model.NITunnelDAO;
import com.adva.nlms.mediation.sm.prov.ni.model.NITunnelDBImpl;
import com.adva.nlms.mediation.sm.servicediscovery.TunnelParams;
import com.adva.nlms.mediation.topology.LineDBImpl;
import com.adva.nlms.opticalparameters.yp.api.OpticalParametersYP;
import ni.proto.external.common.PortParamsOuterClass;
import ni.proto.external.common.map.MapOuterClass;
import ni.proto.external.common.signal_description.FacilityType;
import ni.proto.external.common.signal_description.FlexgridParams;
import ni.proto.external.common.signal_description.SignalDescription;
import ni.proto.external.common.signal_description.SignalWdm;
import ni.proto.external.services.path.PathOuterClass;
import ni.proto.external.services.service.ServiceOuterClass;
import ni.proto.inet.InterfaceId;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ServiceConfiguratorF8 extends AbstractServiceConfiguratorF8 {

  public ServiceConfiguratorF8() {
    super();
  }

  public ServiceOuterClass.ServiceConfiguration getServiceConfiguration(SMProvParams smProvParams, TunnelParams parameters, boolean isECH) throws SMProvException {
    if (parameters instanceof OCSProcessingParameters ocsProcParams) {
      ServiceParameters serviceParameters = mapProvParamsToServiceParameters(smProvParams, ocsProcParams, isECH);
      return ServiceOuterClass.ServiceConfiguration.newBuilder()
              .setName(serviceParameters.getName())
              .setSource(serviceParameters.getSource())
              .setDestination(serviceParameters.getDestination())
              .setWorkingPath(serviceParameters.getWorkingPath())
              .setRestoration(serviceParameters.getRestoration())
              //.addAllDiversities(serviceParameters.getDiversities())
              .setSignal(serviceParameters.getSignalDescription())
              .build();
    } else {
      throw new SMProvException("Failed to process tunnel parameters");
    }
  }

  private ServiceParameters mapProvParamsToServiceParameters(SMProvParams smProvParams, OCSProcessingParameters parameters,
                                                             boolean isECH) {
    PathOuterClass.ProvisionedPath excludedPath = getExcludedPath(smProvParams);
    return new ServiceParameters.Builder()
            .name(((NIOCSProcessingParameters) parameters).getServiceName())
            .source(helper.getSourceEdge(parameters))
            .destination(helper.getDestinationEdge(parameters))
            .workingPath(getWorkingPath(smProvParams, excludedPath))
            .restoration(getRestoration(smProvParams, excludedPath))
            //.diversities(getDiversities(smProvParams))
            .signalDescription(isECH ? getWDMSignalDescription(smProvParams)
                                     : getWDMSignalDescriptionNonECH(smProvParams, ((NIOCSProcessingParameters) parameters)))
            .build();
  }

  protected ServiceOuterClass.ProvisionedPathsSet getWorkingPath(SMProvParams provParams, PathOuterClass.ProvisionedPath excludedPath) {
    LineDBImpl[] lineDbs = provParams.getWorkRoute();
    int startNeId = provParams.getStart().getNetworkElement().getID();
    int destNeId = provParams.getPeer().getNetworkElement().getID();
    List<PathOuterClass.PathElement> pathElements = populatePathElements(startNeId, destNeId, Arrays.asList(lineDbs));
    return createPaths(pathElements, excludedPath);
  }

  protected PathOuterClass.ProvisionedPath getExcludedPath(SMProvParams provParams) {
    List<PathOuterClass.PathElement> pathElements = new ArrayList<>();
    if (provParams.hasXROs()) {
      List<LineDBImpl> lineDbs = provParams.getExcludedLines();
      if (lineDbs != null && !lineDbs.isEmpty()) {
        int startNeId = provParams.getStart().getNetworkElement().getID();
        int destNeId = provParams.getPeer().getNetworkElement().getID();
        pathElements = populatePathElements(startNeId, destNeId, lineDbs);
      }
      // Assume F8 values, create Node and PathElement directly, without classic/F7's OCSPathHandler or PathDefinition.
      List<NetworkElement> excludedNEs = provParams.getExcludedNEs();
      if (excludedNEs != null) {
        PathOuterClass.Node node;
        PathOuterClass.PathElement pathElem;
        for (NetworkElement ne : excludedNEs) {
          node = PathOuterClass.Node.newBuilder().setIp(ne.neDBImpl().getNiSync().getNiId()).build();
          pathElem = PathOuterClass.PathElement.newBuilder().setNode(node).setStrict(false).build();
          pathElements.add(pathElem);
        }
      }
    }

    return PathOuterClass.ProvisionedPath.newBuilder()
            .addAllObjects(pathElements)
            .setScope(PathOuterClass.ProvisionedPath.Scope.PATH_SCOPE_SETUP)
            .build();
  }

  private List<PathOuterClass.PathElement> getRestorationPathElements(SMProvParams provParams) {
    LineDBImpl[] lineDbs = provParams.getRestorationRoute();
    int startNeId = provParams.getStart().getNetworkElement().getID();
    int destNeId = provParams.getPeer().getNetworkElement().getID();
    return populatePathElements(startNeId, destNeId, Arrays.asList(lineDbs));
  }

  /****
   * This method intended to create the pathElement object for a particular endPoint of the LineDbImpl
   * @param neId   NetworkElement Id
   * @param lineEndPointLabel label of the endPoint of LineDBImpl
   */
  protected PathOuterClass.PathElement createPathElement(int neId, String lineEndPointLabel) {
    PathOuterClass.Node node = PathOuterClass.Node.newBuilder()
            .setIp(String.valueOf(neId))
            .build();
    InterfaceId interfaceName = InterfaceId.newBuilder().setName(lineEndPointLabel).build();
    PathOuterClass.InterfaceId interfaceId = PathOuterClass.InterfaceId.newBuilder().setId(interfaceName).build();

    PathOuterClass.UnnumberedLink unnumberedLink = PathOuterClass.UnnumberedLink.newBuilder()
            .setRouter(node)
            .setInterface(interfaceId).build();

    PathOuterClass.Link link = PathOuterClass.Link.newBuilder().setUnnumbered(unnumberedLink).build();

    return PathOuterClass.PathElement.newBuilder().setLink(link).setStrict(false).build();
  }

  @Override
  public FlexgridParams getFlexGridParams(SMProvParams smProvParams) {
    double cf = convertFrequencyToGHz(smProvParams.getCenterFrequency());
    int slotWith = smProvParams.getSlotWidth();
    FlexgridParams.SlotWidth sw = ServiceEnumMaps.mapSlotWidth(slotWith);
    return FlexgridParams.newBuilder()
            .setCenterFrequency(FlexgridParams.Frequency.newBuilder().setValue(cf).build())
            .setSlotWidth(sw)
            .setFrequencySlotRetention(FlexgridParams.FrequencySlotRetention.RETENTION_CENTER_FREQ_AND_SLOT_WIDTH)
            .build();
  }


  // idea is to return a list of pathElements
  protected List<PathOuterClass.PathElement> populatePathElements(int startNeId, int destNeId, List<LineDBImpl> lineDBList) {
    List<PathOuterClass.PathElement> pathElements = new ArrayList<>();
    Map<LineDBImpl, Boolean> isFlippedMap = new HashMap<>();
    LineDBImpl previousLine = null;
    //Selection always include a path from start to some point of the path or from some point of the path to the end point
    //Selection is ordered but end points maybe flipped
    // try to order them
    for (LineDBImpl lineDB : lineDBList) {
      previousLine = populateIsFlippedMap(previousLine, lineDB, isFlippedMap, startNeId, destNeId);
    }
    lineDBList.forEach(lineDB -> determineLineDirectionAndCreatePathElements(pathElements, isFlippedMap, lineDB));
    return pathElements;
  }

  protected void determineLineDirectionAndCreatePathElements(List<PathOuterClass.PathElement> pathElements, Map<LineDBImpl, Boolean> isFlippedMap, LineDBImpl lineDB) {
    int aEndNeId;
    String aEndLabel;
    int zendNeId;
    String zEndLabel;

    // Determine the aEnd and zEnd based on whether the link is flipped
    if (Boolean.TRUE.equals(isFlippedMap.getOrDefault(lineDB, false))) {
      // Link is flipped
      aEndNeId = lineDB.getZEndNEId();
      aEndLabel = lineDB.getZEndpoint().getLabel();
      zendNeId = lineDB.getAEndNEId();
      zEndLabel = lineDB.getAEndpoint().getLabel();
    } else {
      // link is not flipped
      aEndNeId = lineDB.getAEndNEId();
      aEndLabel = lineDB.getAEndpoint().getLabel();
      zendNeId = lineDB.getZEndNEId();
      zEndLabel = lineDB.getZEndpoint().getLabel();
    }

    // create 1 path element for each end of the link
    pathElements.add(createPathElement(aEndNeId, aEndLabel));
    pathElements.add(createPathElement(zendNeId, zEndLabel));
  }

  private LineDBImpl populateIsFlippedMap(
          LineDBImpl previousLine, LineDBImpl lineDB,
          Map<LineDBImpl, Boolean> isFlippedMap, int startNeId, int destNeId) {
    if (lineDB.getAEndNEId() == startNeId) {
      previousLine = lineDB;
      isFlippedMap.put(lineDB, false);
    } else if (lineDB.getZEndNEId() == startNeId) {
      previousLine = lineDB;
      isFlippedMap.put(lineDB, true);
    } else if (lineDB.getZEndNEId() == destNeId) {
      if (previousLine != null && previousLine.getAEndNEId() == lineDB.getAEndNEId()) {
        isFlippedMap.put(previousLine, true);
      }
    } else if (lineDB.getAEndNEId() == destNeId) {
      if (previousLine != null && previousLine.getAEndNEId() == lineDB.getZEndNEId()) {
        isFlippedMap.put(previousLine, true);
      }
      isFlippedMap.put(lineDB, true);
    } else if (previousLine != null) {
      previousLine = handlePreviousLineWhenNotNull(previousLine, lineDB, isFlippedMap);
    } else {
      previousLine = lineDB;
      isFlippedMap.put(lineDB, false);
    }
    return previousLine;
  }

  private LineDBImpl handlePreviousLineWhenNotNull(LineDBImpl previousLine,
                                                   LineDBImpl lineDB,
                                                   Map<LineDBImpl, Boolean> isFlippedMap) {
    if (previousLine.getAEndNEId() == lineDB.getAEndNEId()) {
      isFlippedMap.put(previousLine, true);
      isFlippedMap.put(lineDB, false);
    } else if (previousLine.getAEndNEId() == lineDB.getZEndNEId()) {
      isFlippedMap.put(previousLine, true);
      isFlippedMap.put(lineDB, true);
    } else if (previousLine.getZEndNEId() == lineDB.getAEndNEId()) {
      isFlippedMap.put(previousLine, false);
      isFlippedMap.put(lineDB, false);
    } else if (previousLine.getZEndNEId() == lineDB.getZEndNEId()) {
      isFlippedMap.put(previousLine, false);
      isFlippedMap.put(lineDB, true);
    }
    previousLine = lineDB;
    return previousLine;
  }
  protected SignalDescription getWDMSignalDescriptionNonECH(SMProvParams smProvParams, NIOCSProcessingParameters parameters) {
    SignalWdm signalWdm = getSignalWdm(smProvParams);
    FacilityType facilityType = getFacilityType(smProvParams);
    PortParamsOuterClass.PortParams nearEndPortParams = getPortParams(parameters.getStartPortParamsMap());
    PortParamsOuterClass.PortParams farEndPortParams = getPortParams(parameters.getPeerPortParamsMap());
    return SignalDescription.newBuilder()
            .setWdm(signalWdm)
            .setFacilityType(facilityType)
            .setPortParams(nearEndPortParams)
            .setFarEndPortParams(farEndPortParams)
            .build();
  }

  @Override
  protected FacilityType getFacilityType(SMProvParams smProvParams) {
    ProvFSPR7Properties properties = helper.getNetworkPortPropsF8((SMProvParamsF8) smProvParams);
    return ServiceEnumMaps.mapServiceType(properties.get(Parameters.Parameter.TYPE__FACILITY.getKeyword()));
  }

  private PortParamsOuterClass.PortParams getPortParams(Map<String, String> portParamsMap) {
    MapOuterClass.Map.Builder mapBuilder = MapOuterClass.Map.newBuilder();
    portParamsMap.forEach((key, value) -> mapBuilder.addKvp(MapOuterClass.KeyValuePair.newBuilder().setK(key).setV(value).build()));
    return PortParamsOuterClass.PortParams.newBuilder().setParams(mapBuilder.build()).build();
  }
  public ServiceOuterClass.ServiceConfiguration getServiceConfiguration(MLTrailDBImpl mlTrailDB) throws SMProvException{
    ServiceParameters serviceParameters = mapMLTrailToServiceParameters(mlTrailDB);
    return ServiceOuterClass.ServiceConfiguration.newBuilder()
            .setName(serviceParameters.getName())
            .setSource(serviceParameters.getSource())
            .setDestination(serviceParameters.getDestination())
            .setSignal(serviceParameters.getSignalDescription())
            .setRestoration(serviceParameters.getRestoration())
            .setProtection(serviceParameters.getProtection())
            .build();
  }

  private ServiceParameters mapMLTrailToServiceParameters(MLTrailDBImpl mlTrailDB) throws SMProvException{

    ServiceIntentDBImpl serviceIntentDB = (ServiceIntentDBImpl)mlTrailDB.getServiceIntent();
    NITunnelDBImpl niTunnel = BeanProvider.get().getBean(NITunnelDAO.class).getTunnelById(serviceIntentDB.getNiServiceId());
    String niTunnelName = StringUtils.isNoneEmpty(niTunnel.getTunnelName()) ? niTunnel.getTunnelName() : serviceIntentDB.getName();
    return new ServiceParameters.Builder()
            .name(niTunnelName)
            .source(getSourceEdge(niTunnel))
            .destination(getDestinationEdge(niTunnel))
            .signalDescription(getWDMSignalDescription(mlTrailDB, niTunnel))
            .restoration(getDefaultRestoration())   // for now - no restoration
            .protection(getDefaultProtection())     // for now - no protection
            .build();
  }
  private ServiceOuterClass.Restoration getDefaultRestoration() {
    ServiceOuterClass.Restoration.RestorationMode restorationMode =
            ServiceEnumMaps.mapRestorationType(Definition.RestorationType.NONE);
    ServiceOuterClass.Restoration.RestorationType restorationType =
            ServiceEnumMaps.mapRestorationMode(Definition.RestorationMode.NONE);
    ServiceOuterClass.Restoration.ReversionType reversionType = ServiceEnumMaps.mapReversionType(Definition.ReversionMode.NONE);

    return ServiceOuterClass.Restoration.newBuilder()
            .setRestorationMode(restorationMode)
            .setRestorationType(restorationType)
            .setReversionType(reversionType)
            .build();
  }

  private ServiceOuterClass.Restoration getRestoration(SMProvParams smProvParams, PathOuterClass.ProvisionedPath excludedPath) {
    ServiceOuterClass.Restoration.RestorationMode restorationMode = ServiceEnumMaps.mapRestorationType(smProvParams.getRestorationType());
    ServiceOuterClass.Restoration.RestorationType restorationType = ServiceEnumMaps.mapRestorationMode(smProvParams.getRestorationMode());
    ServiceOuterClass.Restoration.ReversionType reversionType = ServiceEnumMaps.mapReversionType(smProvParams.getReversionMode());

    List<PathOuterClass.PathElement> pathElements;
    if (restorationType == ServiceOuterClass.Restoration.RestorationType.RESTORATION_TYPE_PREPLANNED ||
        restorationType == ServiceOuterClass.Restoration.RestorationType.RESTORATION_TYPE_PREPLANNED_DYNAMIC) {
      pathElements = getRestorationPathElements(smProvParams);
    } else {
      pathElements = new ArrayList<>();
    }

    ServiceOuterClass.ProvisionedPathsSet provisionedPaths = createPaths(pathElements, excludedPath);

    return ServiceOuterClass.Restoration.newBuilder()
            .setProvisionedPaths(provisionedPaths)
            .setRestorationMode(restorationMode)
            .setRestorationType(restorationType)
            .setReversionType(reversionType)
            .build();
  }

  private ServiceOuterClass.Protection getDefaultProtection() {
    ServiceOuterClass.Protection.ProtectionType protectionType = getProtection(ProtMechanism.NO_PROTECTION);
    return getProtectionBuilder(protectionType).build();
  }

  private SignalDescription getWDMSignalDescription(MLTrailDBImpl mlTrailDB, NITunnelDBImpl niTunnel) throws SMProvException {
    if (MLF8EntityHelper.isF8Service(mlTrailDB)) {
      return getWDMSignalDescriptionF8WithPortFacility(mlTrailDB, niTunnel);
    }
    else {
      throw new SMProvException("getWDMSignalDescription not supported from Non-F8 ML trail " +  mlTrailDB);
    }
  }

  private SignalDescription getWDMSignalDescriptionF8WithPortFacility(MLTrailDBImpl mlTrailDB, NITunnelDBImpl niTunnel) {
    // F8 NFC adopt function - requested by NI: to add one facility type param for portParameter and farendPortParameter
    MLConnectionPointDBImpl aEnd = mlTrailDB.getAEndConnectionPoint();
    MLConnectionPointDBImpl zEnd = mlTrailDB.getZEndConnectionPoint();
    if (niTunnel.getFromNeId() != aEnd.getMoNeId()) {
      // ni tunnel and trail could be opposite endpoint, use NI as the base
      MLConnectionPointDBImpl temp = aEnd;
      aEnd = zEnd;
      zEnd = temp;
    }
    PortParamsOuterClass.PortParams nearEndPortParams = getPortParamsMap(aEnd);
    PortParamsOuterClass.PortParams farEndPortParams = getPortParamsMap(zEnd);

    FacilityType facilityType = FacilityType.forNumber(niTunnel.getFacilityType());
    SignalWdm signalWdm = getSignalWdm(niTunnel);
    SignalDescription.Builder builder = SignalDescription.newBuilder().setWdm(signalWdm)
            .setFacilityType(facilityType);
    if (nearEndPortParams != null)
      builder.setPortParams(nearEndPortParams);
    if (farEndPortParams != null)
      builder.setFarEndPortParams(farEndPortParams);
    return builder.build();
  }
  private PortParamsOuterClass.PortParams getPortParamsMap(MLConnectionPointDBImpl endCP){
    if(endCP == null)
      return null;
    MLConnectionPointFacility facility = endCP.getPropertyOrNull(MLTEPropertyKey.FACILITY, MLConnectionPointFacility.class);
    if(facility != null) {
      Map<String, String> prop = new HashMap<>();
      prop.put(Keyword.TYPE__FACILITY.name(), facility.getYpFacility().getKeyword()); // use F7 facility keyword, map it to F8 next
      // mapping to F8 KVP
      Map<String, String> commonParamsMap = new HashMap<>(prop.size());
      OpticalParametersYP.toCommon(prop).forEach(p -> commonParamsMap.put(p.key(), p.value()));
      // build KVP
      MapOuterClass.Map.Builder mapBuilder = MapOuterClass.Map.newBuilder();
      commonParamsMap.forEach((key, value) -> mapBuilder.addKvp(MapOuterClass.KeyValuePair.newBuilder().setK(key).setV(value).build()));
      return PortParamsOuterClass.PortParams.newBuilder().setParams(mapBuilder.build()).build();
    }
    return null;
  }

}
