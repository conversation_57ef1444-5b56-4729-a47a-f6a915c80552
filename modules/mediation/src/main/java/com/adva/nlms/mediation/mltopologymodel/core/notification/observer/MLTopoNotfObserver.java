/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: andred
 */
package com.adva.nlms.mediation.mltopologymodel.core.notification.observer;

import com.adva.eod.nrim.api.dto.LinkOperationalState;
import com.adva.nlms.common.mltopologymodel.dto.MLOperStateEventBean;
import com.adva.nlms.common.mltopologymodel.enums.MLNewPrimaryLifecycleState;
import com.adva.nlms.common.mltopologymodel.enums.MLNewSecondaryLifecycleState;
import com.adva.nlms.common.sm.ReasonCode;
import com.adva.nlms.common.sm.ServiceOperationalState;
import com.adva.nlms.inf.api.DestinationArea;
import com.adva.nlms.inf.api.notification.AbstractInternalNotification;
import com.adva.nlms.inf.api.notification.AbstractUpdateNotification;
import com.adva.nlms.inf.api.notification.NotificationAttribute;
import com.adva.nlms.mediation.infrastructure.concurrent.AdvaExecutors;
import com.adva.nlms.mediation.infrastructure.concurrent.NamedThreadFactory;
import com.adva.nlms.mediation.common.mltopologymodel.dto.MLConnectionDTO;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.common.persistence.MDPersistenceHelper;
import com.adva.nlms.mediation.common.persistence.MDPersistenceManager;
import com.adva.nlms.mediation.common.persistence.MDTransactional;
import com.adva.nlms.mediation.event.EventDAO;
import com.adva.nlms.mediation.event.EventDBChangeHdlrImpl;
import com.adva.nlms.mediation.evtProc.api.EventProcFacade;
import com.adva.nlms.mediation.messaging.inf.MLTopoCreationNotification;
import com.adva.nlms.mediation.messaging.inf.MLTopoDeletionNotification;
import com.adva.nlms.mediation.messaging.inf.MLTopoNotification;
import com.adva.nlms.mediation.messaging.inf.MLTopoUpdateNotification;
import com.adva.nlms.mediation.messaging.inf.impl.notifications.MLTopoUpdateNotificationImpl;
import com.adva.nlms.mediation.mltopologymodel.core.concurrent.MLTaskExecutorService2;
import com.adva.nlms.mediation.mltopologymodel.core.notification.api.MLNotificationProxy;
import com.adva.nlms.mediation.mltopologymodel.diagnostic.log.DebugIdMgr;
import com.adva.nlms.mediation.mltopologymodel.diagnostic.log.DebugIdMgr.Area;
import com.adva.nlms.mediation.mltopologymodel.model.converter.inf.MLTopoAttributes;
import com.adva.nlms.mediation.mltopologymodel.model.converter.inf.MLTopoConnAttributes;
import com.adva.nlms.mediation.mltopologymodel.model.converter.inf.MLTopoConnPointAttributes;
import com.adva.nlms.mediation.mltopologymodel.model.converter.inf.MLTopoMtpConnAttributes;
import com.adva.nlms.mediation.mltopologymodel.model.converter.inf.MLTopoPathMonSectionAttributes;
import com.adva.nlms.mediation.mltopologymodel.model.converter.inf.MLTopoTrailObjectAttributes;
import com.adva.nlms.mediation.mltopologymodel.model.dao.MLTopologyElementDAO;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLConnectionDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLConnectionPointDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLLayerAdaptationDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLModuleDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLMonitoringSectionDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLMtpConnPointEntryDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLMtpConnectionDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLNodeDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLPathMonitoringSectionDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLPtpConnectionDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLPtpServiceDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLSegmentAdaptationDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLServiceDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLTopologyElementDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.db.MLTrailDBImpl;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLConnection;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLConnectionPoint;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLLayerAdaptation;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLMonitoringSection;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLSegmentAdaptation;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLService;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLTopologyMOReference;
import com.adva.nlms.mediation.mltopologymodel.model.interfaces.MLTransport;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLConnectionPointCategory;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLConnectionPointType;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLConnectionType;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLModuleType;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLNetworkLayer;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLObjectType;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLPortLocation;
import com.adva.nlms.mediation.mltopologymodel.model.type.MLTEPropertyKey;
import com.adva.nlms.mediation.mltopologymodel.mofacade.MLMoReferenceHelper;
import com.adva.nlms.mediation.mltopologymodel.mofacade.MLTopologyResyncFacade;
import com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.MLOperStateEventCache;
import com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.MLServiceResyncTaskHelper;
import com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.service.ClearConnectivityServiceAlarmTask;
import com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.service.MLServiceCreateNotificationHelperFactory;
import com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.service.MLServiceUpdateNotificationHelperFactory;
import com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.service.RaiseConnectivityServiceAlarmTask;
import com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.tasks.MLServicePowerLevelTask;
import com.adva.nlms.mediation.mltopologymodel.mofacade.notfhelpers.tasks.MLServiceViewTask;
import com.adva.nlms.mediation.mltopologymodel.mofacade.resync.MLConnectionNameParamHandler;
import com.adva.nlms.mediation.mltopologymodel.mofacade.resync.MLCustomNameHandler;
import com.adva.nlms.mediation.mltopologymodel.momediation.proc.MLTopologyMoFacade;
import com.adva.nlms.mediation.mltopologymodel.resources.MLServiceHelper;
import com.adva.nlms.mediation.mltopologymodel.service.intent.ServiceIntentDAO;
import com.adva.nlms.mediation.mltopologymodel.service.intent.implementation.db.ServiceIntentDBImpl;
import com.adva.nlms.mediation.mltopologymodel.stateproc.adminstate.api.ServiceCreationAdminStateHandler;
import com.adva.nlms.mediation.mltopologymodel.stateproc.operstate.api.MLOperStateOperation;
import com.adva.nlms.mediation.mltopologymodel.sync.entity.factory.NrimKafkaNotificationTaskFactory;
import com.adva.nlms.mediation.mltopologymodel.sync.entity.factory.ResyncNrimLogicalLinkTaskFactory;
import com.adva.nlms.mediation.mltopologymodel.sync.entity.task.MLConnectionPointCreationResyncLinkTask;
import com.adva.nlms.mediation.mltopologymodel.sync.entity.task.PostFiberDeletionResyncTask;
import com.adva.nlms.mediation.mltopologymodel.sync.extension.event.UpdateServiceCreationSecurityEventTask;
import com.adva.nlms.mediation.mltopologymodel.sync.extension.name.MLServiceNameHelper;
import com.adva.nlms.mediation.mltopologymodel.sync.lifecycle.MLEntityLifecycleStateTask;
import com.adva.nlms.mediation.mltopologymodel.sync.lifecycle.MLServiceLifecycleStateTask;
import com.adva.nlms.mediation.mltopologymodel.sync.path.task.MLServiceTask;
import com.adva.nlms.mediation.mltopologymodel.sync.path.task.ResyncServiceProtectionStateTask;
import com.adva.nlms.mediation.sm.nbi.FmNbiStateHdlr;
import com.adva.nlms.mediation.sm.notification.Notifier;
import com.adva.nlms.mediation.sm.operationalStatus.ServiceStateCounterHdlrImpl;
import com.adva.nlms.mediation.sm.operationalStatus.utils.OperationalStatusNotifier;
import com.adva.nlms.mediation.sm.operationalStatus.utils.OperationalStatusNotifierImpl;
import com.adva.sm.tag.api.TagApi;
import com.google.common.collect.ImmutableSet;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Singleton;
import jakarta.persistence.EntityManager;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.text.TextStringBuilder;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import static com.adva.nlms.mediation.mltopologymodel.mofacade.MLTopologyConfiguration.*;

@Component
@Singleton
public class MLTopoNotfObserver extends AbstractMLObserver<MLTopoNotification> {
  private final MLTopologyElementDAO accessHelper;
  private final MLTopologyResyncFacade mlTopologyResyncFacade;
  private final MLMoReferenceHelper mlMoReferenceHelper;
  private final MLTaskExecutorService2 mlTaskExecutorService;
  private final MLServiceResyncTaskHelper serviceResyncTaskHelper;
  private final MLOperStateEventCache mlOperStateEventCache;
  private final FmNbiStateHdlr fmNbiStateHdlr;
  private final MLServiceHelper mlServiceHelper;
  private final MLServiceNameHelper mlServiceNameHelper;
  private final MLServiceUpdateNotificationHelperFactory mlServiceUpdateNotificationHelperFactory;
  private final MLServiceCreateNotificationHelperFactory mlServiceCreateNotificationHelperFactory;
  private final ServiceStateCounterHdlrImpl serviceStateCounterHdlr;
  private final MLOperStateOperation mlOperStateOperation;
  private final EventProcFacade eventProcFacade;
  private final EventDBChangeHdlrImpl eventDBChangeHdlr;
  private final EventDAO eventDAO;
  private final ServiceCreationAdminStateHandler serviceCreationAdminStateHandler;
  private final ServiceIntentDAO serviceIntentDAO;
  private final ResyncNrimLogicalLinkTaskFactory resyncNrimLogicalLinkTaskFactory;
  private final NrimKafkaNotificationTaskFactory nrimKafkaNotificationTaskFactory;
  private final TagApi tagApi;
  private final Notifier notifier;

  private static final Logger log = LogManager.getLogger(MLTopoNotfObserver.class);
  private final String observerId = getClass().getSimpleName();
  private final ExecutorService observerExecutor =
      AdvaExecutors.newSingleThreadScheduledExecutor(new NamedThreadFactory("MLTopoNotfObsExc"), true);
  private OperationalStatusNotifier operationalStatusNotifier;

  @Autowired
  public MLTopoNotfObserver(MLTaskExecutorService2 mlTaskExecutorService, MLTopologyElementDAO accessHelper,
                            MLTopologyResyncFacade mlTopologyResyncFacade, EventProcFacade eventProcFacade,
                            MLMoReferenceHelper mlMoReferenceHelper, MLServiceResyncTaskHelper serviceResyncTaskHelper,
                            @Qualifier("MLOperStateEventCache") MLOperStateEventCache mlOperStateEventCache,
                            FmNbiStateHdlr fmNbiStateHdlr, ResyncNrimLogicalLinkTaskFactory resyncNrimLogicalLinkTaskFactory,
                            MLServiceHelper mlServiceHelper, MLServiceNameHelper mlServiceNameHelper,
                            MLServiceUpdateNotificationHelperFactory mlServiceUpdateNotificationHelperFactory,
                            EventDAO eventDAO, ServiceCreationAdminStateHandler serviceCreationAdminStateHandler,
                            ServiceStateCounterHdlrImpl serviceStateCounterHdlr,
                            MLServiceCreateNotificationHelperFactory mlServiceCreateNotificationHelperFactory,
                            EventDBChangeHdlrImpl eventDBChangeHdlr, ServiceIntentDAO serviceIntentDAO,
                            MLOperStateOperation mlOperStateOperation,
                            NrimKafkaNotificationTaskFactory nrimKafkaNotificationTaskFactory,
                            @Qualifier("tagApi")TagApi tagApi,
                            Notifier notifier) {
    this.mlTaskExecutorService = mlTaskExecutorService;
    this.accessHelper = accessHelper;
    this.mlTopologyResyncFacade = mlTopologyResyncFacade;
    this.eventProcFacade = eventProcFacade;
    this.mlMoReferenceHelper = mlMoReferenceHelper;
    this.serviceResyncTaskHelper = serviceResyncTaskHelper;
    this.mlOperStateEventCache = mlOperStateEventCache;
    this.fmNbiStateHdlr = fmNbiStateHdlr;
    this.resyncNrimLogicalLinkTaskFactory = resyncNrimLogicalLinkTaskFactory;
    this.mlServiceHelper = mlServiceHelper;
    this.mlServiceNameHelper = mlServiceNameHelper;
    this.mlServiceUpdateNotificationHelperFactory = mlServiceUpdateNotificationHelperFactory;
    this.eventDAO = eventDAO;
    this.serviceCreationAdminStateHandler = serviceCreationAdminStateHandler;
    this.serviceStateCounterHdlr = serviceStateCounterHdlr;
    this.mlServiceCreateNotificationHelperFactory = mlServiceCreateNotificationHelperFactory;
    this.eventDBChangeHdlr = eventDBChangeHdlr;
    this.serviceIntentDAO = serviceIntentDAO;
    this.mlOperStateOperation = mlOperStateOperation;
    this.nrimKafkaNotificationTaskFactory = nrimKafkaNotificationTaskFactory;
    this.tagApi = tagApi;
    this.notifier = notifier;
  }

  @PostConstruct
  public void init() {
    operationalStatusNotifier = new OperationalStatusNotifierImpl(serviceStateCounterHdlr, notifier, eventProcFacade, eventDBChangeHdlr);
  }

  @Override
  public void handleMLNotification(MLTopoNotification notification) {
    logNotification(notification);
    MLNotificationProxy.notifyAllListeners(notification);

    //Notification is a link notification
    Class notificationClass = notification.getNotifiedObjectClass();

    try {
      handleLifcycleStateChange(notification);

      if (MLConnectionPoint.class.isAssignableFrom(notificationClass))
        handleConnPointNotification(notification);
      
      else if (MLConnection.class.isAssignableFrom(notificationClass))
        //object is a connection (link or cross connect)
        handleConnectionNotification(notification);
      
      else if (MLService.class.isAssignableFrom(notificationClass))
        //object is a Service
        handleServiceNotification(notification);
      
      else if (MLMonitoringSection.class.isAssignableFrom(notificationClass))
        handleMonitoringSectionNotification(notification);
      
    }
    catch (Exception e) {
      log.error("MLTopoNotifObserver failed. notif={}, id={}, class={}", notification.getClass().getSimpleName(),
          notification.getNotifiedObjectId(), notification.getNotifiedObjectClass().getSimpleName(), e);
    }
  }

  @Override
  public ExecutorService getExecutorService() {
    return observerExecutor;
  }

  private void handleLifecycleStateChangeForMLTopoCreationNotification(MLTopoCreationNotification notification) {
    int dbId = notification.getNotifiedObjectId();
    Class<?> dbClass = notification.getNotifiedObjectClass();
    // some additional logging for creation
    if (MLServiceDBImpl.class.isAssignableFrom(dbClass) ||
        MLPtpConnectionDBImpl.class.isAssignableFrom(dbClass) ||
        MLLayerAdaptationDBImpl.class.isAssignableFrom(dbClass) ||
        MLSegmentAdaptationDBImpl.class.isAssignableFrom(dbClass) ||
        MLPathMonitoringSectionDBImpl.class.isAssignableFrom(dbClass))
      logObjectCreation(dbId, dbClass);

    // detect stitched cases: set DS-Mismatch
    Map<Integer, MLNetworkLayer> dataServiceIds = getDataServiceIdsTerminatingAtNewConnection(dbId, dbClass);
    for (Map.Entry<Integer, MLNetworkLayer> entry : dataServiceIds.entrySet()) {
      DebugIdMgr.add(Area.Lifecycle, entry.getKey());
      mlTaskExecutorService.submitTask(new MLServiceLifecycleStateTask(entry.getValue(), MLServiceLifecycleStateTask.Trigger.Set,
          entry.getKey(), MLTrailDBImpl.class, 0, MLNewSecondaryLifecycleState.DS_MISMATCH.getBitValue()));
    }
    if (MLServiceDBImpl.class.isAssignableFrom(dbClass)) {
      MLNetworkLayer layer = notification.getValue(MLTopoTrailObjectAttributes.LAYER);
      mlTaskExecutorService.submitTask(new MLServiceLifecycleStateTask(layer, MLServiceLifecycleStateTask.Trigger.UpdateMismatchState,
          dbId, dbClass, 0, MLNewSecondaryLifecycleState.SGTE_DAMAGED.getBitValue()));
    }
  }

  private void handleStitchingLifecycleStateChangeForMLTopoUpdateNotification(int dbId, Class<?> dbClass,
                                                                              Integer oldSecondaryLifecycleState,
                                                                              Integer newSecondaryLifecycleState) {
    Map<Integer, MLNetworkLayer> dataServiceIds = getDataServiceIdsTerminatingAtNewConnection(dbId, dbClass);
    for (Map.Entry<Integer, MLNetworkLayer> entry : dataServiceIds.entrySet()) {
      MLTrailDBImpl mlTrailDB = MLTopologyElementDAO.getInstance().getTrailByID(entry.getKey());
      if (mlTrailDB != null && mlTrailDB.getNewSecondaryLifecycleStateSet().contains(MLNewSecondaryLifecycleState.DS_MISMATCH)
          && MLNewSecondaryLifecycleState.valueOf(oldSecondaryLifecycleState) != MLNewSecondaryLifecycleState.MODIFY_IND
          && MLNewSecondaryLifecycleState.valueOf(newSecondaryLifecycleState) != MLNewSecondaryLifecycleState.MODIFY_IND) {
        // when MODIFY_IND state is being set the DS_MISMATCH is being overwritten. To prevent this, the previous check was added
        clearNewSecondaryLifecycleState(mlTrailDB.getId(), MLNewSecondaryLifecycleState.DS_MISMATCH);
        mlTaskExecutorService.submitTask(new MLServiceLifecycleStateTask(entry.getValue(), MLServiceLifecycleStateTask.Trigger.Set,
            entry.getKey(), MLTrailDBImpl.class, 0, MLNewSecondaryLifecycleState.RESYNC_REQ.getBitValue()));
      }
    }
  }

  private void handleLifecycleStateChangeForMLTopoUpdateNotification(MLTopoUpdateNotification notification) {
    if (!notification.containsKey(MLTopoAttributes.NEW_SECONDARY_LIFECYCLE_STATES)) {
      return;
    }
    int dbId = notification.getNotifiedObjectId();
    Class<?> dbClass = notification.getNotifiedObjectClass();
    Integer oldSecondaryLifecycleState = ((AbstractUpdateNotification) notification).getOldValue(MLTopoAttributes.NEW_SECONDARY_LIFECYCLE_STATES);
    Integer newSecondaryLifecycleState = notification.getValue(MLTopoAttributes.NEW_SECONDARY_LIFECYCLE_STATES);

    // undo case for stitched scenario - clear mismatch and trigger resync
    handleStitchingLifecycleStateChangeForMLTopoUpdateNotification(dbId, dbClass, oldSecondaryLifecycleState, newSecondaryLifecycleState);

    int neMoId = getNeMoId(dbId, dbClass);
    MLNetworkLayer layer = (neMoId >= 0) ? null : getLayer(dbId, dbClass);
    if (neMoId > 0 && nonNull(oldSecondaryLifecycleState, newSecondaryLifecycleState)) {
      mlTaskExecutorService.submitTask(new MLEntityLifecycleStateTask(neMoId, MLEntityLifecycleStateTask.Trigger.Process, dbId, dbClass, oldSecondaryLifecycleState, newSecondaryLifecycleState));
    }else if (layer != null && nonNull(oldSecondaryLifecycleState, newSecondaryLifecycleState)) {
      mlTaskExecutorService.submitTask(new MLServiceLifecycleStateTask(layer, MLServiceLifecycleStateTask.Trigger.Process, dbId, dbClass, oldSecondaryLifecycleState, newSecondaryLifecycleState));
    }else {
      // log debug-level if object has been deleted
      Level level = (getTeDB(dbId) == null) ? Level.DEBUG : Level.ERROR;
      log.log(level, LIFECYCLE_LOG_PREFIX + "MLTopoNotifObserver missing attributes id={} ({}) {} {} {} {}", notification.getNotifiedObjectId(), notification.getNotifiedObjectClass().getSimpleName(),
          neMoId, layer, oldSecondaryLifecycleState, newSecondaryLifecycleState);
    }
  }

  private void handleLifcycleStateChange(MLTopoNotification notification) {
    if (notification instanceof MLTopoCreationNotification mlTopoCreationNotification) {
      handleLifecycleStateChangeForMLTopoCreationNotification(mlTopoCreationNotification);
    }
    else if (notification instanceof MLTopoUpdateNotification mlTopoUpdateNotification) {
      handleLifecycleStateChangeForMLTopoUpdateNotification(mlTopoUpdateNotification);
    }
  }

  @MDTransactional
  public void clearNewSecondaryLifecycleState(int dbId, MLNewSecondaryLifecycleState secondaryLifecycleState) {
    MLTopologyElementDBImpl teDB = MDPersistenceHelper.find(MLTopologyElementDBImpl.class, dbId);
    if (teDB != null && teDB.getNewSecondaryLifecycleStateSet().contains(secondaryLifecycleState)) {
      teDB.clearNewSecondaryLifecycleState(secondaryLifecycleState);
    }
  }

  private static final Set<MLNetworkLayer> fiberLayers = ImmutableSet.of(MLNetworkLayer.OPTICAL, MLNetworkLayer.FIBER);

  @MDPersistenceContext
  private Map<Integer, MLNetworkLayer> getDataServiceIdsTerminatingAtNewConnection(int mlId, Class<?> clazz) {
    EntityManager em = MDPersistenceManager.current();
    if (MLPtpConnectionDBImpl.class.isAssignableFrom(clazz)) {
      MLPtpConnectionDBImpl connDB = em.find(MLPtpConnectionDBImpl.class, mlId);
      if (connDB != null && fiberLayers.contains(connDB.getLayer())) {
        List<Integer> edgePortIds = getDsCpIDs(connDB.getAEnd(), connDB.getZEnd());
        List<MLTrailDBImpl> dataServiceIds =
        em.createQuery("SELECT p from MLTrailDBImpl p JOIN p.teProperties q WHERE (p.aEnd.id IN :endpoint OR p.zEnd.id IN :endpoint) AND q.key = :key AND q.value = :value", MLTrailDBImpl.class)
          .setParameter("endpoint", edgePortIds)
          .setParameter("key", MLTEPropertyKey.DATA_SERVICE.getID())
          .setParameter("value", MLTEPropertyKey.convertToDatabaseColumn(Boolean.TRUE))
          .getResultList();
        return dataServiceIds.stream().collect(Collectors.toMap(MLTrailDBImpl::getId, MLTrailDBImpl::getLayer));
      }
    }

    return Collections.emptyMap();
  }

  @MDPersistenceContext
  private void logObjectCreation(int mlId, Class<?> clazz) {
    MLTopologyElementDBImpl teDB = MDPersistenceHelper.find(MLTopologyElementDBImpl.class, mlId);
    if (teDB == null || !NEW_LIFECYCLE_LOGGING)
      return;

    // don't log connections from inventory
    if (MLPtpConnectionDBImpl.class.isAssignableFrom(clazz)) {
      MLPtpConnectionDBImpl connDB = (MLPtpConnectionDBImpl) teDB;
      if (connDB.getConnectionType() != MLConnectionType.PtpLink || fiberLayers.contains(connDB.getLayer()))
        return;
    }
    TextStringBuilder sb = new TextStringBuilder();
    if (teDB instanceof MLTrailDBImpl) {
      List<MLConnection> mlConnDBs = ((MLTrailDBImpl) teDB).getWorkingForwardPath().connectionSequence();
      for (MLConnection mlConnDB : mlConnDBs) {
        sb.appendAll((sb.isEmpty() ? "" : ", "), mlConnDB.getId(),
          " [", mlConnDB.getLabel(), "]",
          " (", mlConnDB.getNewSecondaryLifecycleStateSet().getSet().stream().map(MLNewSecondaryLifecycleState::getShortName).collect(Collectors.joining(",")), ")");
      }
      sb.appendAll(" moRefs=[",
          teDB.getRelatedMOObjects().stream().map(MLTopologyMOReference::getDescription).collect(Collectors.joining(",")), "]");
    }

    String logMsg = sb.get();
    log.info(SYNC_LOG_PREFIX + "Created {} id={} ({}) {}", clazz.getSimpleName(), teDB.getId(), teDB.getLabel(), logMsg);
  }

  @MDPersistenceContext
  private int getNeMoId(int mlId, Class<?> clazz) {
    Optional<Integer> neMoId = Optional.empty();
    try {
      if (MLNodeDBImpl.class.isAssignableFrom(clazz)) {
        neMoId = MDPersistenceHelper.find(MLNodeDBImpl.class, mlId).getMoNeID();
      }
      else if (MLModuleDBImpl.class.isAssignableFrom(clazz)) {
        int mlNodeId = MDPersistenceHelper.find(MLModuleDBImpl.class, mlId).getParent().getId();
        if (mlNodeId > 0)
          neMoId = MDPersistenceHelper.find(MLNodeDBImpl.class, mlNodeId).getMoNeID();
      }
      else if (MLConnectionPointDBImpl.class.isAssignableFrom(clazz)) {
        int mlNodeId = MDPersistenceHelper.find(MLConnectionPointDBImpl.class, mlId).getParentNodeID();
        if (mlNodeId > 0)
          neMoId = MDPersistenceHelper.find(MLNodeDBImpl.class, mlNodeId).getMoNeID();
      }
      else if (MLConnectionDBImpl.class.isAssignableFrom(clazz)) {
        MLConnectionDBImpl connDB = MDPersistenceHelper.find(MLConnectionDBImpl.class, mlId);
        if (connDB.isCrossConnect() || connDB.getLayer() == MLNetworkLayer.OPTICAL) {
          MLConnectionPointDBImpl cpDB = connDB.getConnectionPointEntries().iterator().next().getConnectionPoint();
          int mlNodeId = cpDB.getParentNodeID();
          if (mlNodeId > 0)
            neMoId = MDPersistenceHelper.find(MLNodeDBImpl.class, mlNodeId).getMoNeID();
        }
      }
    } catch (NullPointerException e) {
      // log debug-level if object has been deleted
      Level level = (MDPersistenceHelper.find(MLTopologyElementDBImpl.class, mlId) == null) ? Level.DEBUG : Level.ERROR;
      log.log(level, "MLTopoNotfObserver.getNeMoId failed for {} ({})", mlId, clazz);
    }

    return neMoId.orElse(-1);
  }

  @MDPersistenceContext
  private MLNetworkLayer getLayer(int mlId, Class<?> clazz) {
    MLNetworkLayer layer = null;
    try {
      MLTopologyElementDBImpl teDB = MDPersistenceHelper.find(MLTopologyElementDBImpl.class, mlId);
      if (teDB instanceof MLTransport) {
        layer = ((MLTransport) teDB).getLayer();
      }
      else if (teDB instanceof MLLayerAdaptationDBImpl &&
              ((MLLayerAdaptationDBImpl) teDB).getServerEntitiesList() != null &&
              !((MLLayerAdaptationDBImpl) teDB).getServerEntities().isEmpty()) {
        layer = ((MLLayerAdaptationDBImpl) teDB).getServerEntities().iterator().next().getLayer();
      }
      else if (teDB instanceof MLSegmentAdaptationDBImpl) {
        layer = ((MLSegmentAdaptationDBImpl) teDB).getLayer();
      }
    } catch (NullPointerException e) {
      // log debug-level if object has been deleted
      Level level = (MDPersistenceHelper.find(MLTopologyElementDBImpl.class, mlId) == null) ? Level.DEBUG : Level.ERROR;
      log.log(level, "MLTopoNotfObserver.getLayer failed for {} ({})", mlId, clazz);
    }

    return layer;
  }

  private boolean nonNull(Object... objects) {
    for (Object object : objects) {
      if (object == null)
        return false;
    }
    return true;
  }

  @MDPersistenceContext
  private void handleConnPointNotification(MLTopoNotification notification) {
    try {
      if ((notification instanceof MLTopoUpdateNotification || notification instanceof MLTopoCreationNotification) &&
          notification.containsKey(MLTEPropertyKey.PROVISIONED.getNotificationAttribute()) &&
          (Boolean) notification.getValue(MLTEPropertyKey.PROVISIONED.getNotificationAttribute())) {
        MLConnectionPointDBImpl mlConnectionPointDB = accessHelper.getConnPointByID(notification.getNotifiedObjectId());
        if (mlConnectionPointDB != null) {
          // Fiber Map Resync is needed here when channel module port being provisioned (during cccp/ccp provision creation),
          // then through fibermap resync mechanism to update the connected side PM/YCable's ports with CM's facility type and
          // trigger the service discovery from PM/YCable. This would also to trigger the ML discovery for connected cascaded
          // modules side's ML service discovery.
          Set<MLConnectionPointDBImpl> cps = new HashSet<>();
          cps.add(mlConnectionPointDB);
          mlServiceHelper.resyncRelatedIntraConnectionsByCPs(cps);
        }
      }

      if (notification instanceof MLTopoUpdateNotification && notification.containsKey(MLTopoConnPointAttributes.OPER_STATE)) {
        MLConnectionPointDBImpl cpDB = accessHelper.getConnPointByID(notification.getNotifiedObjectId());
        if (cpDB == null) {
          log.warn("MLTopoNotfObserver.handleConnPointNotification ConnectionPoint missing: {}", notification);
          return;
        }
        MLOperStateEventBean operStateEvent = mlOperStateEventCache.retrieveLastOperStateEvent(cpDB)
            .orElse(null);
        if (operStateEvent == null) {
          log.debug("MLTopoNotfObserver.handleConnPointNotification missing oper-state Trap information for {}", cpDB);
          operStateEvent = new MLOperStateEventBean();
          operStateEvent.setNmsTimeStamp(System.currentTimeMillis());
        }
        mlOperStateOperation.processCpOperStateChange(cpDB.generateDTO(), operStateEvent.getNmsTimeStamp());
      }

      if (notification instanceof MLTopoCreationNotification mlTopoCreationNotification) {
        /* When the endpoint of a line is recreated after being deleted, the MLConnection that has a reference to the line
        needs to be created. Since the line endpoints are not updated when the endpoint is deleted, there is no line
        updated notification to trigger this ML connection resync, so the connection point creation notification is used
        for that purpose (given a list of MO references, find if any terminate a line and trigger a ResyncLineTask).
        This can be removed if the line update behavior changes, i.e. line is updated when endpoints are deleted, forcing
        the user to reconfigure the line, thus generating a line update notification */
        handleLinkResyncAfterConnectionPointCreation(mlTopoCreationNotification);
      }

      if (notification instanceof MLTopoDeletionNotification) {
        handleConnectionPointDeletion(notification);
      }
    } catch (Exception e) {
      log.error("Error while processing notifications for handling connection points in ML", e);
    }
  }

  private void handleConnectionPointDeletion(MLTopoNotification notification) {
    if (notification.containsKey(MLTopoConnPointAttributes.LOCATION)
        && notification.containsKey(MLTopoConnPointAttributes.PARENT_NODE_ID)
        && notification.containsKey(MLTopoConnPointAttributes.CP_TYPE)
        && notification.containsKey(MLTopoAttributes.LABEL)) {
      String portLocationString = notification.getValue(MLTopoConnPointAttributes.LOCATION);
      MLPortLocation portLocation = MLPortLocation.getLocationByLocString(portLocationString);
      int parentNodeId = notification.getValue(MLTopoConnPointAttributes.PARENT_NODE_ID);
      String portLabel = notification.getValue(MLTopoAttributes.LABEL);
      MLConnectionPointType cpType = notification.getValue(MLTopoConnPointAttributes.CP_TYPE);
      if (cpType == MLConnectionPointType.PORT) {
        /* Only consider PORT connection points to trigger physical logical link removal - other logical links will be
        removed following CS lifecycle events */
        mlTaskExecutorService.submitTask(resyncNrimLogicalLinkTaskFactory.deletePhysicalLogicalLinkAfterEndpointDeletionTask(parentNodeId, portLabel, portLocation));
      }
    }
  }

  private void handleLinkResyncAfterConnectionPointCreation(MLTopoCreationNotification notification) {
    if (notification.containsKey(MLTopoConnPointAttributes.CP_TYPE) &&
        notification.getValue(MLTopoConnPointAttributes.CP_TYPE) == MLConnectionPointType.PORT) {
      // Only applicable to PORT connection points, as they are the ones that refer to the PTPs, i.e. line endpoints
      mlTaskExecutorService.submitTask(new MLConnectionPointCreationResyncLinkTask(notification.getNotifiedObjectId(),
          accessHelper, MLTopologyMoFacade.getInstance(), mlTopologyResyncFacade, resyncNrimLogicalLinkTaskFactory));
    }
  }

  private void handleServiceUpdateNotification(MLTopoUpdateNotification notification, MLTrailDBImpl trailDB) {
    // for post service creation adminstate handling: check if this notification means, a service was set to managed
    if (notification instanceof MLTopoUpdateNotificationImpl){
      MLTopoUpdateNotificationImpl update = (MLTopoUpdateNotificationImpl) notification;
      if (update.containsKey(MLTopoAttributes.NEW_PRIMARY_LIFECYCLE_STATE)) {
        MLNewPrimaryLifecycleState newPrimaryLifecycleState = update.getValue(MLTopoAttributes.NEW_PRIMARY_LIFECYCLE_STATE);
        MLNewPrimaryLifecycleState oldPrimaryLifecycleState = update.getOldValue(MLTopoAttributes.NEW_PRIMARY_LIFECYCLE_STATE);
        if (newPrimaryLifecycleState != MLNewPrimaryLifecycleState.DISCOVERED && oldPrimaryLifecycleState == MLNewPrimaryLifecycleState.DISCOVERED) {
          serviceCreationAdminStateHandler.handleTrailSetToManaged(update.getNotifiedObjectId());
          // notifyNBI , service creation and create connectivity service (DISCOVERED - MANAGED)
          if (trailDB != null) {
            boolean isDataService = trailDB.isDataService();
            operationalStatusNotifier.notifyNBIOnOperationalStatusChange(trailDB, trailDB.getOperationalStatus().getOperState(), trailDB.getOperationalStatus().getReasonCode(),
                    ServiceOperationalState.NA, ReasonCode.NR, trailDB.getOperationalStatus().getTimestamp(), trailDB.getOperationalStatus().getSecondaryState());
            // send notification to FM-NBI
            fmNbiStateHdlr.notifyServiceStateChange(FmNbiStateHdlr.ChangeType.SetManaged, trailDB.generateDTO(), isDataService);

            updateCSAndLLOperState(trailDB, trailDB.getOperationalStatus().getOperState());

            MLCustomNameHandler.notifyCustomNameUpdated(trailDB.getId(), null, MLObjectType.TRAIL);
          }
        }
        // notifyNBI , service deletion and set service to discovered (MANAGED - DISCOVERED)
        if (newPrimaryLifecycleState == MLNewPrimaryLifecycleState.DISCOVERED && oldPrimaryLifecycleState != MLNewPrimaryLifecycleState.DISCOVERED) {
          if (trailDB != null) {
            boolean isDataService = trailDB.isDataService();
            operationalStatusNotifier.notifyNBIOnOperationalStatusChange(trailDB, ServiceOperationalState.OK, ReasonCode.NR, trailDB.getOperationalStatus().getOperState(),
                    trailDB.getOperationalStatus().getReasonCode(), -1, "");
            // send notification to FM-NBI
            fmNbiStateHdlr.notifyServiceStateChange(FmNbiStateHdlr.ChangeType.SetDiscovered, trailDB.generateDTO(), isDataService);
            // if a connection is changed from Managed to Discovered delete custom name entry, if applicable
            MLCustomNameHandler.notifyCustomNameUpdated(trailDB.getId(), null, MLObjectType.TRAIL);
          }
        }
      }

      if (update.containsKey(MLTopoTrailObjectAttributes.OPER_STATE) || update.containsKey(MLTopoTrailObjectAttributes.REASON_CODE)) {
        ServiceOperationalState newOperState = update.getValue(MLTopoTrailObjectAttributes.OPER_STATE);
        ServiceOperationalState oldOperState = update.getOldValue(MLTopoTrailObjectAttributes.OPER_STATE);
        ReasonCode newReasonCode = update.getValue(MLTopoTrailObjectAttributes.REASON_CODE);
        ReasonCode oldReasonCode = update.getOldValue(MLTopoTrailObjectAttributes.REASON_CODE);
        String secondaryState = update.getValue(MLTopoTrailObjectAttributes.SECONDARY_STATE);
        long faultedTS = update.getValue(MLTopoTrailObjectAttributes.FAULTED_TIMESTAMP);
        long degradedTS = update.getValue(MLTopoTrailObjectAttributes.DEGRADED_TIMESTAMP);
        long timestamp = (faultedTS != -1) ? faultedTS : degradedTS;

        // notifyNBI -- when there is a change in oper state or reason code (only for managed services)
        if (trailDB != null && trailDB.getNewPrimaryLifecycleState() == MLNewPrimaryLifecycleState.MANAGED) {
          boolean isDataService = trailDB.isDataService();
          operationalStatusNotifier.notifyNBIOnOperationalStatusChange(trailDB, newOperState, newReasonCode, oldOperState, oldReasonCode, timestamp, secondaryState);
          // send notification to FM-NBI
          fmNbiStateHdlr.notifyServiceStateChange(FmNbiStateHdlr.ChangeType.OperationalStateChange, trailDB.generateDTO(), isDataService);
        }

        updateCSAndLLOperState(trailDB, newOperState);
      }
    }

    if (trailDB == null) {
      // notify counters on service deletion
      if (notification.containsKey(MLTopoTrailObjectAttributes.SERVICE_GROUP) && ((AbstractUpdateNotification) notification).getOldValues().containsKey(MLTopoTrailObjectAttributes.SERVICE_GROUP)) {
        Integer oldServiceGroupId = ((AbstractUpdateNotification) notification).getOldValue(MLTopoTrailObjectAttributes.SERVICE_GROUP);
        if (oldServiceGroupId != null) {
          serviceStateCounterHdlr.notifyServiceDeleted_transaction(notification.getNotifiedObjectId(), oldServiceGroupId);
        } else {
          log.warn("MLTopoNotfObserver.handleServiceNotification Old service group ID set to null: {}", notification);
        }
      }
      log.warn("MLTopoNotfObserver.handleServiceNotification Trail missing: {}", notification);
      return;
    }

    if (notification.containsKey(MLTopoAttributes.NEW_PRIMARY_LIFECYCLE_STATE) ||
        notification.containsKey(MLTopoAttributes.NEW_SECONDARY_LIFECYCLE_STATES)) {
      mlTaskExecutorService.submitTask(new MLServiceViewTask(MLServiceViewTask.TriggerType.UpdateLifecycleState, trailDB.getId(), trailDB.getClass(), ((AbstractInternalNotification) notification).getNewValues()));
      mlTaskExecutorService.submitTask(new MLServiceViewTask(MLServiceViewTask.TriggerType.UpdateStates, trailDB.getId(), trailDB.getClass(), ((AbstractInternalNotification) notification).getNewValues()));
    }

    if (notification.containsKey(MLTopoTrailObjectAttributes.SERVICE_GROUP)) {
      mlServiceUpdateNotificationHelperFactory.getServiceUpdateNotificationHelper(trailDB.getLayer()).onServiceGroupChange(notification);
    }

    // Label has been changed: update conection-names attribute of Service Intent
    if (notification.containsKey(MLTopoAttributes.LABEL)) {
      String oldLabel = ((AbstractUpdateNotification) notification).getOldValue(MLTopoTrailObjectAttributes.LABEL);
      MLConnectionNameParamHandler.notifyNameUpdated(trailDB, oldLabel);
      // if the oldLabel and newLabel are different then update customname table, if necessary
      MLCustomNameHandler.notifyCustomNameUpdated(trailDB.getId(), oldLabel, MLObjectType.TRAIL);

    }

    if (notification.containsKey(MLTopoTrailObjectAttributes.CONTRACT)) {
      handleServiceUpdateContractNotification(notification, trailDB);
    }
  }

  private void updateCSAndLLOperState(MLTrailDBImpl trailDB, ServiceOperationalState operState) {
    String aEndCep = trailDB.getAEndCep();
    String zEndCep = trailDB.getZEndCep();
    if (shouldNotifyCSM(trailDB)) {
      mlTaskExecutorService.submitTask(nrimKafkaNotificationTaskFactory.createServiceUpdatedNotificationTask(trailDB));
    }
    // Also update the Logical Link
    if (aEndCep != null && zEndCep != null) {
      mlTaskExecutorService.submitTask(resyncNrimLogicalLinkTaskFactory.createCsUpdateLogicalLinkOperStateTask(aEndCep, zEndCep,
              transformServiceOperStateToLLOperState(operState)));
    }
  }

  // top connection only
  private boolean shouldNotifyCSM(MLTrailDBImpl trailDB) {
    return EOD_EVOLUTION_ENABLED && trailDB.getUuid() != null &&
            trailDB.getAEndCep() != null && trailDB.getZEndCep() != null &&
            trailDB.getNewPrimaryLifecycleState() == MLNewPrimaryLifecycleState.MANAGED;
  }

  private LinkOperationalState transformServiceOperStateToLLOperState(ServiceOperationalState operState) {
    if (operState != null) {
      return LinkOperationalState.valueOf(operState.name());
    }
    return LinkOperationalState.NIS;
  }

  private void handleServiceUpdateContractNotification(MLTopoUpdateNotification notification, MLTrailDBImpl trailDB) {
    if (notification.getValue(MLTopoTrailObjectAttributes.CONTRACT) != null
        && trailDB.getServiceIntent() instanceof ServiceIntentDBImpl serviceIntentDB) {
      MLConnectionNameParamHandler.notifyServiceIntentRefUpdated(trailDB);
      mlTaskExecutorService.submitTask(new UpdateServiceCreationSecurityEventTask(mlServiceHelper.getSmServiceHelper(),
          MLTopologyElementDAO.getInstance(), eventDAO, serviceIntentDB.getId(), trailDB.getId(), trailDB.getLabel()));
      triggerClearConnectivityServiceAlarms(serviceIntentDB);
    } else if (notification instanceof MLTopoUpdateNotificationImpl mlTopoUpdateNotification
        && mlTopoUpdateNotification.getOldValue(MLTopoTrailObjectAttributes.CONTRACT) != null) {
      triggerRaiseConnectivityServiceAlarms(mlTopoUpdateNotification.getOldValue(MLTopoTrailObjectAttributes.CONTRACT));
    }

    // update ServiceView: connectivity-span
    mlTaskExecutorService.submitTask(new MLServiceViewTask(MLServiceViewTask.TriggerType.CreateOrUpdateServiceView, trailDB.getId(), true, true));
  }

  private void handleServiceCreationNotification(MLTopoCreationNotification notification, MLTrailDBImpl trailDB) {
    if (trailDB != null) {
      mlServiceCreateNotificationHelperFactory.getServiceCreateNotificationHelper(trailDB.getLayer()).onServiceCreate(notification);

      if (trailDB.getServiceIntent() instanceof ServiceIntentDBImpl serviceIntentDB && notification.getValue(MLTopoTrailObjectAttributes.CONTRACT) != null) {
        MLConnectionNameParamHandler.notifyServiceIntentRefUpdated(trailDB);
        triggerClearConnectivityServiceAlarms(serviceIntentDB);
      }

      // if the trail (CFC/NFC) is discovered, check for mismatch state on the associated classic service. If the classic service is in mismatch, then trigger re-explore of classic and ml
      // if the connectivity service, then re-explore links the classic and ml connections to CS, else the ml shows up only under discovered folder
      if (CONNECTION_CLEANUP && trailDB.getNewPrimaryLifecycleState() == MLNewPrimaryLifecycleState.DISCOVERED
          && mlServiceHelper.isF7Service(trailDB)) {
        mlServiceHelper.triggerF7ConnectionCleanUp(trailDB);
      }

      if (EOD_EVOLUTION_ENABLED && trailDB.getUuid() != null && trailDB.getAEndCep() != null && trailDB.getZEndCep() != null) {
        log.debug("Creating SERVICE_CREATED NrimKafkaNotificationTask for trail {}", trailDB);
        mlTaskExecutorService.submitTask(nrimKafkaNotificationTaskFactory.createServiceCreatedNotificationTask(trailDB));
      }
    } else {
      log.error("handleServiceCreationNotification: Null trail mapped to notification {}", notification);
    }
  }

  private void handleServiceDeletionNotification(MLTopoDeletionNotification notification) {
    int trailId = notification.getNotifiedObjectId();
    MLNetworkLayer layer = notification.getValue(MLTopoTrailObjectAttributes.LAYER);
    mlTaskExecutorService.submitTask(new MLServicePowerLevelTask(layer, trailId));
    tagApi.untagBySystem(String.valueOf(trailId));
    fmNbiStateHdlr.notifyServiceDeletion(trailId);

    Integer contractId = notification.getValue(MLTopoTrailObjectAttributes.CONTRACT);
    if (contractId != null) {
      triggerRaiseConnectivityServiceAlarms(contractId);
    }
    if (notification.containsKey(MLTopoAttributes.LABEL)) {
      String label = notification.getValue(MLTopoAttributes.LABEL);
      MLCustomNameHandler.notifyCustomNameDeleted(trailId, label, MLObjectType.TRAIL);
    }

    if (EOD_EVOLUTION_ENABLED) {
      UUID uuid = notification.getValue(MLTopoTrailObjectAttributes.UUID);
      String aEndCep = notification.getValue(MLTopoTrailObjectAttributes.AEND_CEP);
      String zEndCep = notification.getValue(MLTopoTrailObjectAttributes.ZEND_CEP);
      if (aEndCep != null && zEndCep != null) {
        mlTaskExecutorService.submitTask(nrimKafkaNotificationTaskFactory.createServiceDeletedNotificationTask(uuid, aEndCep, zEndCep));
      }
    }
  }

  @MDPersistenceContext
  private void handleServiceNotification(MLTopoNotification notification) {
    MLTrailDBImpl trailDB = accessHelper.getTrailByID(notification.getNotifiedObjectId());

    if (notification instanceof MLTopoUpdateNotification mlTopoUpdateNotification) {
      handleServiceUpdateNotification(mlTopoUpdateNotification, trailDB);
    } else if(notification instanceof MLTopoCreationNotification mlTopoCreationNotification) {
      handleServiceCreationNotification(mlTopoCreationNotification, trailDB);
    } else if(notification instanceof MLTopoDeletionNotification mlTopoDeletionNotification) {
      handleServiceDeletionNotification(mlTopoDeletionNotification);
    }
  }

  @MDPersistenceContext
  private void handleConnectionNotification(MLTopoNotification notification) {
    if (notification instanceof MLTopoDeletionNotification deletionNotification) {
      PostFiberDeletionResyncTask task = new PostFiberDeletionResyncTask(deletionNotification, mlTaskExecutorService, accessHelper);
      mlTaskExecutorService.submitTask(task);
      return;
    }

    MLConnectionDBImpl connectionDB = accessHelper.getConnectionByID(notification.getNotifiedObjectId());
    if (connectionDB == null) {
      log.warn("MLTopoNotfObserver.handleConnectionNotification Connection missing {}", notification);
      return;
    }

    AEndAndZEnd endPoints = getAEndAndZEnd(connectionDB);
    boolean serviceResyncRequired;
    if (notification instanceof MLTopoUpdateNotification updateNotification) {
      serviceResyncRequired = isServiceResyncRequiredForConnectionUpdateNotification(notification, connectionDB);
      handleConnectionUpdateNotification(updateNotification, connectionDB);
    } else { // Connection creation notification
      serviceResyncRequired = true;
      handleConnectionCreationNotification(connectionDB, endPoints.aEnd, endPoints.zEnd);
    }

    if (endPoints.aEnd != null && serviceResyncRequired) {
      if (log.isDebugEnabled())
        log.debug("Initializing Service Traversal for connection Notification: Label {} Layer: {} Endpoint: {}",
            connectionDB.getLabel(), connectionDB.getLayer(),
          endPoints.aEnd.getPortLocation().map(MLPortLocation::toString).orElse(""));
      serviceResyncTaskHelper.initResyncTasks((MLConnectionPointDBImpl) endPoints.aEnd, connectionDB.getLayer());
    }
  }

  private record AEndAndZEnd(MLConnectionPoint aEnd, MLConnectionPoint zEnd) {}

  private AEndAndZEnd getAEndAndZEnd(MLConnectionDBImpl connectionDB) {
    if (connectionDB instanceof MLPtpConnectionDBImpl ptpConnectionDB) {
      boolean revert = ptpConnectionDB.getAEndConnectionPoint() == null || ptpConnectionDB.getAEndConnectionPoint().isUnmanaged();
      return revert ?
        new AEndAndZEnd(ptpConnectionDB.getZEndConnectionPoint(), ptpConnectionDB.getAEndConnectionPoint()) :
        new AEndAndZEnd(ptpConnectionDB.getAEndConnectionPoint(), ptpConnectionDB.getZEndConnectionPoint());
    }

    // multipoint connection: only aEnd matters
    if (connectionDB.getAEndConnectionPoint() != null)
      return new AEndAndZEnd(connectionDB.getAEndConnectionPoint(), null);

    // No specific aEnd for multipoint connection. Return empty, if connectionDB.connPointEntries is null.
    if (connectionDB.getConnectionPointEntries() == null)
      return new AEndAndZEnd(null, null);

    // No specific aEnd for multipoint connection. Find any potential endpoint from connectionDB.connPointEntries.
    MLConnectionPointDBImpl any = connectionDB.getConnectionPointEntries().stream()
      .map(MLMtpConnPointEntryDBImpl::getConnectionPoint)
      .filter(Objects::nonNull)
      .findAny()
      .orElse(null);
    return new AEndAndZEnd(any, null);
  }

  private void handleConnectionUpdateNotification(MLTopoUpdateNotification notification, MLConnectionDBImpl connectionDB) {
    if (notification.containsKey(MLTEPropertyKey.ZEND_ACTIVE.getNotificationAttribute()) ) {
      mlTaskExecutorService.submitTask(new ResyncServiceProtectionStateTask(connectionDB.getId(), connectionDB.getLayer(),
          accessHelper, mlMoReferenceHelper, mlServiceHelper, mlServiceNameHelper, mlTaskExecutorService));
    }

    if (notification.containsKey(MLTEPropertyKey.PROTECTIONSTATE.getNotificationAttribute())) {
      mlTaskExecutorService.submitTask(nrimKafkaNotificationTaskFactory.createProtectionSwitchNotificationTask(connectionDB));
    }

    if (notification.containsKey(MLTopoTrailObjectAttributes.NEW_PRIMARY_LIFECYCLE_STATE)) {
      FmNbiStateHdlr.ChangeType changeType = getLifeCycleStateChangeType(notification);
      if (changeType != null) {
        MLConnectionDTO dto = connectionDB.generateDTO();
        fmNbiStateHdlr.notifyServiceStateChange(changeType, dto, false);
      }

      if (connectionDB.getConnectionType().isNetworkConnection() && connectionDB.getNewPrimaryLifecycleState().isDiscovered()) {
        MLCustomNameHandler.notifyCustomNameUpdated(connectionDB.getId(), null, MLObjectType.NETWORK_CONNECTION);
      }

      if (connectionDB.getConnectionType().isNetworkConnection() && connectionDB.getNewPrimaryLifecycleState().isManagedOrUsed()) {
        MLConnectionNameParamHandler.notifyNetworkConnectionUpdated(connectionDB);
        MLCustomNameHandler.notifyCustomNameUpdated(connectionDB.getId(), null, MLObjectType.NETWORK_CONNECTION);
      }
    }

    // Label has been changed: update connection-names attribute of Service Intent
    if (notification.containsKey(MLTopoAttributes.LABEL)) {
      String oldLabel = ((AbstractUpdateNotification) notification).getOldValue(MLTopoAttributes.LABEL);
      MLConnectionNameParamHandler.notifyNameUpdated(connectionDB, oldLabel);
      if (connectionDB.getConnectionType() == MLConnectionType.NetworkConnection)
        MLCustomNameHandler.notifyCustomNameUpdated(connectionDB.getId(), oldLabel, MLObjectType.NETWORK_CONNECTION);
    }
  }

  private void handleConnectionCreationNotification(MLConnectionDBImpl connectionDB, MLConnectionPoint aEnd, MLConnectionPoint zEnd) {
    if (aEnd != null && zEnd != null &&
        (connectionDB.getLayer() == MLNetworkLayer.OPTICAL || connectionDB.getLayer() == MLNetworkLayer.FIBER)) {
      // check for services associated with endpoint and schedule resync
      List<Integer> endCPIDs = getDsCpIDs((MLConnectionPointDBImpl) aEnd, (MLConnectionPointDBImpl) zEnd);
      List<MLPtpServiceDBImpl> trailDBs = accessHelper.getPtpDataServicesByEndpointIn(endCPIDs);
      scheduleMoDeletedResyncTasks(trailDBs);
    }
    if (connectionDB.getConnectionType().isNetworkConnection() && connectionDB.getNewPrimaryLifecycleState().isManagedOrUsed()) {
      MLConnectionNameParamHandler.notifyNetworkConnectionUpdated(connectionDB);
      MLCustomNameHandler.notifyCustomNameUpdated(connectionDB.getId(), null, MLObjectType.NETWORK_CONNECTION);
    }
  }

  private void scheduleMoDeletedResyncTasks(List<MLPtpServiceDBImpl> trailDBs) {
    trailDBs.forEach(trailDB -> {
      final int trailId = trailDB.getId();
      final MLNetworkLayer layer = trailDB.getLayer();
      mlTaskExecutorService.submitTask(new MLServiceTask() {
        @Override
        public MLNetworkLayer getNetworkLayer() {
          return layer;
        }

        @Override
        @MDPersistenceContext
        public void run() {
          MLTrailDBImpl trailDB = MDPersistenceHelper.find(MLTrailDBImpl.class, trailId);
          if (trailDB!=null && (trailDBs.size() < 2 || trailDB.getNewSecondaryLifecycleStates() != 0))
            mlTopologyResyncFacade.getServiceResyncHelper(trailDB).moDeleted(trailDB);
        }
      });
    });
  }

  private boolean isServiceResyncRequiredForConnectionUpdateNotification(MLTopoNotification notification,
                                                                         MLConnectionDBImpl connectionDB) {
    boolean isLifecycleNotification = notification.containsKey(MLTopoAttributes.NEW_PRIMARY_LIFECYCLE_STATE) ||
        notification.containsKey(MLTopoAttributes.NEW_SECONDARY_LIFECYCLE_STATES);

    /* No need to trigger service resync tasks after lifecycle notifications.
    Lifecycle handling notifications and respective workflows are processed separately */
    if (isLifecycleNotification) {
      log.debug("Life cycle affected notification - re-sync service will be skipped for connection id={}, connection={}, aEnd={}, zEnd={} Notification received {}",
              connectionDB.getId(), connectionDB.getLabel(), connectionDB.getAEndConnectionPoint(), connectionDB.getZEndConnectionPoint(), notification);
      return false;
    }

    return isUpdateNotificationForMtpConnection(notification, connectionDB) ||
        isUpdateNotificationForHandover(notification, connectionDB) ||
        notification.containsKey(MLTEPropertyKey.OSC_PORTS.getNotificationAttribute());
  }

  /**
   * get a list of potential DS endpoint IDs
   * @param aEnd
   * @param zEnd
   * @return
   */
  private List<Integer> getDsCpIDs(MLConnectionPointDBImpl aEnd, MLConnectionPointDBImpl zEnd){
    List<MLConnectionPoint> endCPs = getPotentialDsCPs(aEnd);
    endCPs.addAll(getPotentialDsCPs(zEnd));
    List<Integer> endCPIDs = new ArrayList<>();
    endCPs.stream().forEach(cp->endCPIDs.add(cp.getId()));

    return endCPIDs;
  }
  private List<MLConnectionPoint> getPotentialDsCPs(MLConnectionPointDBImpl cp){
    List<MLConnectionPoint> cps = new ArrayList<>();
    cps.add(cp);
    MLModuleDBImpl m;
    if (cp.getType() == MLConnectionPointType.PORT && !cp.containedConnectionPoints().isEmpty() &&
            (m = cp.parentModule()) != null && m.getModuleType() == MLModuleType.CHANNELMOD &&
            cp.getCategory() == MLConnectionPointCategory.CLIENT) {
      // channel module client port to check vch endpoint (when client port and VCH has same matched OTUx/ODUx, DS endpoints on VCH)
      MLNetworkLayer layer = cp.getLayerFromFacility();
      if (layer != null && MLNetworkLayer.getAllOtuLayers().contains(layer)) {
        MLNetworkLayer oduLayer = MLNetworkLayer.getEquivalentOduFromOtu(layer);
        cp.containedConnectionPoints().forEach(childCp->{
          if(childCp.getLayerFromFacility() == oduLayer){
            cps.add(childCp);
          }
        });
      }
    }
    return cps;
  }

  @MDPersistenceContext
  private void handleMonitoringSectionNotification(MLTopoNotification notification) {
    if (notification instanceof MLTopoCreationNotification || notification instanceof MLTopoUpdateNotification) {
      MLMonitoringSectionDBImpl monitoringSectionDB = accessHelper.getElementByID(notification.getNotifiedObjectId(), MLMonitoringSectionDBImpl.class);
      if (monitoringSectionDB == null) {
        log.warn("MLTopoNotfObserver.handleMonitoringSectionNotification Monitoring-Section missing {}", notification);
        return;
      }

      if (notification instanceof MLTopoCreationNotification) {
        // resync MLServiceView
        mlTaskExecutorService.submitTask(new MLServiceViewTask(MLServiceViewTask.TriggerType.ResyncUpperLayers, monitoringSectionDB.getId()));
        mlTaskExecutorService.submitTask(new MLServiceViewTask(MLServiceViewTask.TriggerType.CreateMonitoringSection, monitoringSectionDB.getId()));
      }
      else {
        if (notification.containsKey(MLTopoTrailObjectAttributes.NEW_PRIMARY_LIFECYCLE_STATE)) {
          FmNbiStateHdlr.ChangeType changeType = getLifeCycleStateChangeType(notification);
          if (changeType != null) {
            fmNbiStateHdlr.notifyServiceStateChange(changeType, monitoringSectionDB.generateDTO(), false);
          }
          if (monitoringSectionDB.getNewPrimaryLifecycleState().isDiscovered() || monitoringSectionDB.getNewPrimaryLifecycleState().isManagedOrUsed())
            MLCustomNameHandler.notifyCustomNameUpdated(monitoringSectionDB.getId(), null, MLObjectType.MONITORING_SECTION);
        }

        // Label has been changed: update conection-names attribute of Service Intent
        if (notification.containsKey(MLTopoAttributes.LABEL)) {
          String oldLabel = ((AbstractUpdateNotification) notification).getOldValue(MLTopoTrailObjectAttributes.LABEL);
          MLConnectionNameParamHandler.notifyNameUpdated(monitoringSectionDB, oldLabel);
          MLCustomNameHandler.notifyCustomNameUpdated(monitoringSectionDB.getId(), oldLabel, MLObjectType.MONITORING_SECTION);
        }
      }
    }
  }

  @Override
  public String getObserverID() {
    return observerId;
  }

  @Override
  public DestinationArea getDestination() {
    return DestinationArea.SM;
  }

  @MDPersistenceContext
  private MLTopologyElementDBImpl getTeDB(int dbId) {
    return MDPersistenceHelper.find(MLTopologyElementDBImpl.class, dbId);
  }

  private FmNbiStateHdlr.ChangeType getLifeCycleStateChangeType(MLTopoNotification notification) {
    if (notification instanceof AbstractUpdateNotification) {
      MLNewPrimaryLifecycleState oldPrimaryLCState = ((AbstractUpdateNotification) notification).getOldValue(MLTopoTrailObjectAttributes.NEW_PRIMARY_LIFECYCLE_STATE);
      MLNewPrimaryLifecycleState newPrimaryLCState = notification.getValue(MLTopoTrailObjectAttributes.NEW_PRIMARY_LIFECYCLE_STATE);

      if (oldPrimaryLCState == null || newPrimaryLCState == null)
        return null;

      if (oldPrimaryLCState == MLNewPrimaryLifecycleState.DISCOVERED  && newPrimaryLCState != MLNewPrimaryLifecycleState.DISCOVERED)
        return FmNbiStateHdlr.ChangeType.SetManaged;
      if (oldPrimaryLCState != MLNewPrimaryLifecycleState.DISCOVERED  && newPrimaryLCState == MLNewPrimaryLifecycleState.DISCOVERED)
        return FmNbiStateHdlr.ChangeType.SetDiscovered;
    }

    return null;
  }


  private boolean isUpdateNotificationForMtpConnection(MLTopoNotification notification, MLConnectionDBImpl connectionDB) {
    if (connectionDB instanceof MLMtpConnectionDBImpl) {
      if (connectionDB.getAEndConnectionPoint() != null &&
          connectionDB.getAEndConnectionPoint().parentModule() != null
          && connectionDB.getAEndConnectionPoint().parentModule().getModuleType() == MLModuleType.SWITCH) { // FNMD-70083
        return true;
      }
      return notification.containsKey(MLTEPropertyKey.PROTECTIONTYPE.getNotificationAttribute())
          || notification.containsKey(MLTopoMtpConnAttributes.APSGROUP);
    }
    return false;
  }

  private boolean isUpdateNotificationForHandover(MLTopoNotification notification, MLConnectionDBImpl connectionDB) {
    if(notification instanceof MLTopoUpdateNotification && connectionDB instanceof MLPtpConnectionDBImpl) {
      MLConnectionPointDBImpl aEnd = ((MLPtpConnectionDBImpl) connectionDB).getAEnd();
      MLConnectionPointDBImpl zEnd = ((MLPtpConnectionDBImpl) connectionDB).getZEnd();
      return (aEnd != null && aEnd.isUnmanaged()) || (zEnd != null && zEnd.isUnmanaged());
    }
    return false;
  }

  private static final String ATTRIBUT_ID_VERSION = "version";
  private static final String ATTRIBUT_ID_TE_PROPERTIES = "teProperties";
  private static final Set<NotificationAttribute> suppressedKeys = ImmutableSet.<NotificationAttribute>builder().add(
    new NotificationAttribute<Integer>(ATTRIBUT_ID_VERSION),
    MLTopoConnAttributes.OPER_STATE, MLTopoConnAttributes.REASON_CODE, MLTopoConnAttributes.SECONDARY_STATE,
    MLTopoConnAttributes.FAULTED_TIMESTAMP, MLTopoConnAttributes.DEGRADED_TIMESTAMP, MLTopoConnAttributes.ADMIN_STATE,
    MLTopoTrailObjectAttributes.OPER_STATE, MLTopoTrailObjectAttributes.REASON_CODE, MLTopoTrailObjectAttributes.SECONDARY_STATE,
    MLTopoTrailObjectAttributes.FAULTED_TIMESTAMP, MLTopoTrailObjectAttributes.DEGRADED_TIMESTAMP, MLTopoTrailObjectAttributes.ADMIN_STATE,
    MLTopoPathMonSectionAttributes.OPER_STATE, MLTopoPathMonSectionAttributes.REASON_CODE, MLTopoPathMonSectionAttributes.SECONDARY_STATE,
    MLTopoPathMonSectionAttributes.FAULTED_TIMESTAMP, MLTopoPathMonSectionAttributes.DEGRADED_TIMESTAMP, MLTopoPathMonSectionAttributes.ADMIN_STATE).build();

  private void logNotification(MLTopoNotification notification) {
    if (!log.isDebugEnabled())
      return;

    TextStringBuilder sb = new TextStringBuilder("MLTopoNotificationObserver");
    try {
      Class<?> notificationClass = notification.getNotifiedObjectClass();
      int id = notification.getNotifiedObjectId();
      String label = notification.getNotifiedObjectAid();

      if (notification instanceof MLTopoCreationNotification)
        sb.append(" CREATE");
      else if (notification instanceof MLTopoUpdateNotification)
        sb.append(" UPDATE");
      else if (notification instanceof MLTopoDeletionNotification)
        sb.append(" DELETE");

      if (MLService.class.isAssignableFrom(notificationClass))
        sb.append(" SERVICE");
      else if (MLConnection.class.isAssignableFrom(notificationClass) && notification.getValue(MLTopoConnAttributes.TYPE) == MLConnectionType.NetworkConnection)
        sb.append(" NET-CONN");
      else if (MLMonitoringSection.class.isAssignableFrom(notificationClass))
        sb.append(" MON-SEC");
      else if (!log.isTraceEnabled()) // log other objects only if Tracing is enabled
        return;

      if (MLConnection.class.isAssignableFrom(notificationClass))
        sb.append(" CONN");
      else if (MLLayerAdaptation.class.isAssignableFrom(notificationClass))
        sb.append(" Layer-Adapt");
      else if (MLSegmentAdaptation.class.isAssignableFrom(notificationClass))
        sb.append(" Segment-Adapt");
      else if (MLConnectionPoint.class.isAssignableFrom(notificationClass))
        sb.append(" CP");
      else
        sb.appendAll(" ", notification.getClass().getSimpleName());

      sb.appendAll(" id=", id, " (", label, ")");

      if (notification instanceof MLTopoUpdateNotificationImpl) {
        Map<? extends NotificationAttribute, ?> newValues = ((MLTopoUpdateNotificationImpl) notification).getNewValues();
        Map<? extends NotificationAttribute, ?> oldValues = ((MLTopoUpdateNotificationImpl) notification).getOldValues();
        if (MapUtils.isEmpty(newValues) || MapUtils.isEmpty(oldValues)) {
          // TE properties showing up here when they are all removed
          if(!isTeProperties(oldValues)) {
           sb.appendAll(" Error: empty values ", notification);
           String logMsg = sb.get();
           log.error(logMsg);
         }
         return;
        }
        List<Map.Entry<? extends NotificationAttribute, ?>> filteredValues = newValues.entrySet().stream()
          .filter(e-> !suppressedKeys.contains(e.getKey()))
          .filter(e-> !(e.getKey().getName().equals(ATTRIBUT_ID_TE_PROPERTIES) && e.getValue() == null))
          .collect(Collectors.toList());
        List<Map.Entry<? extends NotificationAttribute, ?>> filteredOldValues = oldValues.entrySet().stream()
          .filter(e-> !suppressedKeys.contains(e.getKey()))
          .filter(e-> !(e.getKey().getName().equals(ATTRIBUT_ID_TE_PROPERTIES) && e.getValue() == null))
          .collect(Collectors.toList());
        if (filteredValues.isEmpty())
          return;
        sb.append(" oldValues:");
        for (Map.Entry<? extends NotificationAttribute, ?> entry : filteredOldValues) {
          sb.appendAll(",", entry.getKey().getId(), "=", entry.getValue());
        }
        sb.append(" newValues:");
        for (Map.Entry<? extends NotificationAttribute, ?> entry : filteredValues) {
          sb.appendAll(",", entry.getKey().getId(), "=", entry.getValue());
        }
      }
      String logMsg = sb.get();
      log.log(log.getLevel(), logMsg);
    }
    catch (Exception e) {
      log.error("MLTopoNotificationObserver logging failed", e);
    }
  }

  private boolean isTeProperties(Map<? extends NotificationAttribute, ?> oldValues) {
    return (oldValues.keySet().stream().filter(p -> p.getName().equals("teProperties")).findFirst().orElse(null) != null);
  }

  private void triggerClearConnectivityServiceAlarms(ServiceIntentDBImpl serviceIntentDB) {
    mlTaskExecutorService.submitTask(new ClearConnectivityServiceAlarmTask(serviceIntentDB.getId(),
        serviceIntentDB.getParentId(), serviceIntentDB.getName(), eventDAO, eventProcFacade));
  }

  private void triggerRaiseConnectivityServiceAlarms(int contractId) {
    mlTaskExecutorService.submitTask(new RaiseConnectivityServiceAlarmTask(contractId, serviceIntentDAO, mlServiceHelper.getSmServiceHelper(), eventProcFacade));
  }
}
