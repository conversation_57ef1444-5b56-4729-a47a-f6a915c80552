/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: v<PERSON><PERSON><PERSON>
 */

package com.adva.nlms.frontend.comm.service;

import com.adva.common.workbench.login.ConnectionBean;
import com.adva.nlms.common.da.DataAccessDto;
import com.adva.nlms.common.rest.WebTargetMapping;
import com.adva.nlms.frontend.comm.service.rest.*;
import com.adva.nlms.frontend.comm.service.rest.core.RestWebResource;
import com.adva.nlms.frontend.comm.service.security.SSOElsCtrlRestClient;
import com.adva.nlms.frontend.comm.service.security.SecurityCtrlRestClient;
import com.adva.nlms.frontend.comm.service.security.UserCtrlRestClient;
import com.adva.nlms.frontend.comm.service.security.UserDataRestClient;
import com.adva.nlms.frontend.comm.service.security.UserSessionDataRestClient;
import com.adva.nlms.frontend.comm.service.security.approval.PrivilegeApprovalRestClient;
import com.adva.nlms.frontend.comm.service.security.fallback.FallbackUserNERestClient;
import com.adva.nlms.frontend.event.thresholdCrossingAlert.rest.TCARestClient;
import com.adva.nlms.frontend.gistransfer.FiberRestClient;
import com.adva.nlms.frontend.network.AlienDwdmRestClient;
import com.adva.nlms.frontend.network.CustomDeviceRestClient;
import com.adva.nlms.frontend.network.DataAccessRESTClient;
import com.adva.nlms.frontend.network.InventoryRestClient;
import com.adva.nlms.frontend.network.NetworkElementCapabilitiesRestClient;
import com.adva.nlms.frontend.network.NetworkElementOperationRestClient;
import com.adva.nlms.frontend.ni.config.NIConfigRestClient;
import com.adva.nlms.frontend.ni.config.NIWebSocketRestClient;
import com.adva.nlms.frontend.performance.PerformanceManagerRestClient;

public interface ServerService
{

  /**
   * Checks the server certificate and shows Certificate Dialog if the sertificate for that server is not yet accepted
   * @throws java.security.cert.CertificateException if the user does not accept certificate
   */
  void acceptServerCertificate() throws java.security.cert.CertificateException;

  public ConnectionBean getConnectionBean();

  /**
   * Returns the server event controll which is responsable
   * for all operations with events.
   *
   * @return Control object.
   */
  public EventCtrlRestClient getEventControl();

  /**
   * Returns the server search control which is responsible
   * for all operations with search.
   *
   * @return Control object.
   */
  WebTargetMapping getWebTargetMapping();

  /**
   * Returns the server controll which is responsable
   * for all operations with server.
   *
   * @return Control object.
   */
  public ServerCtrlRestClientIF getServerControl();

  /**
   * Returns the server security controll which is responsable
   * for all operations with security
   *
   * @return Control object.
   */

  public SecurityCtrlRestClient getSecurityControlRestClient();

  public SSOElsCtrlRestClient getElsCtrlRestClient();

  public UserCtrlRestClient getUserControlRestClient();

  public UserDataRestClient getUserDataRestClient();

  public UserSessionDataRestClient getUserSessionDataRestClient();


  /**
   * Returns the server report controll which is responsable
   * for all operations with reports
   *
   * @return Control object.
   */
  public ReportRestClient getReportCtrlRest();

  TagRestClient getTagRestClient();

  public TCARestClient getTCACtrlRest();

  ServiceManagerRestClient getServiceManagerRestClient();

  ShadowServiceManagerRestClient getShadowServiceManagerRestClient();

  ServiceAdminStateRestController getPDServiceManagerRestClient();

  ServiceDetailsRestClient getServiceDetailsRestClient();

  CapabilityBrokerRestClient getCapabilityBrokerRestClient();

  public CPPolicyRestClient getCPPolicyRestClient();

  /**
   * Returns the monitoring control which is responsible
   * for operations with monitoring
   *
   * @return Control object.
   */
  public MonitoringCtrlRestClient getMonitoringControl();

  /**
   * Returns the Ethernet NE Configuration control, a REST API.
   * @return Control object.
   */
  EthNEConfigRestClient getEthNEConfigRestClient();


  String getHostName();

  /**
   * Returns the MultiServerConnector used for the multi connect GUI.
   *
   * @return Control object.
   */
  MultiServerConnectorClient getMultiServerConnector();

  /**
   * @return the web resources for REST API
   */
  RestWebResource getRestWebResource();

  /**
   * @return the Rest client for ServerPreferences>DisplaySettings page
   */
  DisplaySettingsRestClient getDisplaySettingsRestClient();

  /**
   * Returns the client update interface which is responsible
   * for all operations with client update
   *
   * @return Control object.
   */
  public ClientUpdateRestClient getClientUpdateRestClient();


  MapViewConfigurationRestClient getMapViewConfigurationRestClient();
  TopologyConfigurationRestClient getTopologyConfigurationRestClient();


  /**
   * Return the paging service for handle Paging related REST APIs;
   * @return PagingHandlerRestClient
   */
  PagingHandlerRestClient getPagingHandlerService();

  /**
   * Returns the NE Backup interface which is responsible for all operations with NE Backup
   *
   * @return Control object.
   */
  NEBackupRestClient getNEBackupRestClient();

  /**
   * Returns the SubnetHdlr rest interface
   *
   * @return Control object.
   */
  SubnetHdlrRestClient getSubnetHdlrRestClient();

  TopologyManagerRestClient getTopologyManagerRestClient();

  WebManagerRestClient getWebManagerRestClient();

  WebHelpRestClient getWebHelpRestClient();

  CustomDeviceRestClient getCustomDeviceRestClient ();

  NeResourcesRestClient getNeResourcesRestClient();

  NetworkElementOperationRestClient getNetworkElementOperationRestClient();

  FormModelRestClient getFormModelDataProvider();

  FibermapDiagnosticsRestClient getFibermapDiagnosticsRestClient();

  AlienDwdmRestClient getAlienDwdmRestClient();

  PerformanceManagerRestClient getPerformanceManagerRestClient();

  /**
   * Returns the snmpRestClient control which is responsible
   * for operations with snmp
   *
   * @return Control object.
   */
  public SNMPRestClient getSNMPRestClient();

  public CLIRestClient getCLIRestClient();

  InventoryRestClient getInventoryRestClient();

  LinePropertiesEditorRestClient getLinePropertiesEditorRestClient();

  LineHdlrRestClient getLineHldLineHdlrRestClient();

  <T extends DataAccessDto> DataAccessRESTClient<T> getDataAccessClient(String servicePath, Class<T> daDtoClass);

  public TrapForwarderRestClient getTrapForwarderRestClient();

  CSVFileTransferRestClient getCsvFileTransferRestClient();

  FunctionManagerRestClient getFunctionManagerRestClient();

  SwUpgradeRestClient getSwUpgradeRestClient();

  FAMRestClient getFAMRestClient();

  NTPRestClient getNTPRestClient();

  PollingOperationsRestClient getPollingOperationsRestClient();

  DataExportServiceRestClient getSimpleReportsRestClient();

  SyncManagerRestClient getSyncManagerRestClient();

  SNTManagerRestClient getSNTManagerRestClient();

  RCAManagerRestClient getRCAManagerRestClient();

  BlocklistRestClient getBlocklistRestClient();

  LinkTrafficRestClient getLinkTrafficRestClient();

  PCARestClient getPCARestClient ();

  CUARestClient getCUARestClient ();

  PrivilegeApprovalRestClient getPrivilegeApprovalRestClient();

  FallbackUserNERestClient getFallbackUserNERestClient();

  SpanLossRestClient getSpanLossRestClient();

  boolean isFallbackUserNERestClientInstantiated();

  LinePropertiesVlanRestClient getLinePropertiesVlanRestClient();

  LineDetailsRestClient getLineDetailsRestClient();

  SMEthernetCryptoRestClient getSmEthernetCryptoRestClient();
  PDL3RestClient getPDL3RestClient();
  PDEvpnRestClient getEvpnRestClient();
  PDDiscoveredServiceRestClient getPDDiscoveredServiceRestClient();
  PDEntitySwapRestClient getPDEntitySwapRestClient();
  DriverManagerRestClient getDriverManagerRestClient();

  NetworkElementCapabilitiesRestClient getNetworkElementCapabilitiesRestClient();

  NECommCtrlRestClient getNECommCtrlRestClient();

  /**
   * Gets TopologyDiscoveryManagerRest service's client
   * @return TopologyDiscoveryManagerRestClient
   */
  TopologyDiscoveryManagerRestClient getTopologyDiscoveryManagerRestClient();

  /**
   * Gets TopologyNodeHdlrRest service's client
   * @return TopologyNodeHdlrRestClient
   */
  TopologyNodeHdlrRestClient getTopologyNodeHdlrRestClient();

  SoundRestClient getSoundRestClient();

  UserNotificationRestClient getUserNotificationRestClient();

  IHAControllerRestClient getHAControllerRestClient();

  NIConfigRestClient getNiControllerRestClient();

  NIWebSocketRestClient getNiWebSocketRestClient();

  CryptoOfficerRestClient getCryptoOfficerRestClient();

  FiberRestClient getFiberRestClient();

  EthEncryptionSettingsRestClient getEthEncryptionSettingsRestClient();

  NeProfileRestClient getNeProfileRestClient();

  MasterProfileRestClient getMasterProfileRestClient();

  ServerMonitoringRestClient getServerMonitoringRestClient();

  PropertiesRestClient getPropertiesRestClient();

  MLPathDiagnosticsRestClient getPathDiagnosticsRestClient();

  ServiceProvisioningEndpointClient getServiceProvisioningEndpointClient();

  CfmMonitoringRestClient getCfmMonitoringRestClient();

  SatResultRestClient getSatResultsRestClient();

  VirtualOtnNodeRestClient getVirtualOtnNodeRestClient();

  ProfileSnmpRestClient getProfileSnmpRestClient();

  HealthMonitoringRestClient getHealthMonitoringRestClient();

  HealthAnalysisRestClient getHealthAnalysisRestClient();

  EthernetRingRestClient getEthernetRingRestClient();

  BandwidthRestrictionRestClient getBandwidthRestrictionRestClient();

  ExportToPlannerRestClient getExportToPlannerClient();
}
