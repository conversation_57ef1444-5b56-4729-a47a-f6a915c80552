/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: svh
 */

package com.adva.nlms.frontend.pdentities.view;

import com.adva.common.util.ErrorLog;
import com.adva.common.util.Resources;
import com.adva.common.util.filter.FilterOperator;
import com.adva.common.util.lifecycle.Disposable;
import com.adva.common.workbench.Insets;
import com.adva.common.workbench.PartInitException;
import com.adva.common.workbench.WorkbenchPage;
import com.adva.common.workbench.ViewPart;
import com.adva.common.workbench.binding.data.DataProvider;
import com.adva.common.workbench.binding.model.BindingColumn;
import com.adva.common.workbench.viewer.TableViewer;
import com.adva.common.workbench.viewer.VBoxViewer;
import com.adva.common.workbench.viewer.WorkbenchSplitViewer;
import com.adva.common.workbench.viewer.ViewFactory;
import com.adva.nlms.common.topology.EthEntityType;
import com.adva.nlms.frontend.pdentities.editor.PDEntitySwapGeneralEditor;
import com.adva.nlms.frontendfx.workbench.binding.data.DataProviderFactory;

public class PDEntitySwapGeneralView implements Disposable {

    private final WorkbenchPage page;
    private final PDEntitySwapGeneralEditor parent;

    private WorkbenchSplitViewer splitViewer;
    private TableViewer tableViewer;
    private DataProvider dataProvider;

    private static final String SERVICE_INTERFACES = "serviceInterfaces";
    private static final String NE_NAME_PROP_KEY = "neName";
    private static final String ENTITY_NAME_PROP_KEY = "entityName";
    private static final String ETH_ENTITY_TYPE_PROP_KEY = "ethEntityType";
    private static final String NE_NAME_BEAN = "neName";
    private static final String ENTITY_NAME_BEAN = "entityName";
    private static final String ETH_ENTITY_TYPE_BEAN = "ethEntityType";

    public PDEntitySwapGeneralView(WorkbenchPage page, PDEntitySwapGeneralEditor parent) {
        this.page = page;
        this.parent = parent;
        init();
    }

    private void init() {
        Insets margins = new Insets(5);
        tableViewer = createTable();

        VBoxViewer vboxRight = ViewFactory.getInstance().createVBoxViewer(new VBoxViewer.Options(page).title(Resources.get(SERVICE_INTERFACES)).id(SERVICE_INTERFACES)
                .viewPart(tableViewer, false, margins)
                .setScrollPaneAsParentNeeded(true));
        try {
            vboxRight.init(page);
        } catch (PartInitException e) {
            ErrorLog.error(e);
        }

        splitViewer = ViewFactory.getInstance().createSplitViewer(
                new WorkbenchSplitViewer.Options()
                        .direction(WorkbenchSplitViewer.Direction.HORIZONTAL)
                        .page(page)
                        .ratio(0.5)
                        .disableSlider(true)
                        .secondPart(vboxRight));

    }

    private TableViewer createTable() {
        BindingColumn[] bindingColumns = new BindingColumn[3];
        bindingColumns[0] = new BindingColumn(Resources.get(NE_NAME_PROP_KEY), NE_NAME_BEAN, false, true, BindingColumn.SortingType.ASCENDING).setFilterClassType(FilterOperator.ClassType.STRING).setWidth(280);
        bindingColumns[1] = new BindingColumn(Resources.get(ENTITY_NAME_PROP_KEY), ENTITY_NAME_BEAN, false, true, BindingColumn.SortingType.ASCENDING).setFilterClassType(FilterOperator.ClassType.STRING).setWidth(280);
        bindingColumns[2] = new BindingColumn(Resources.get(ETH_ENTITY_TYPE_PROP_KEY), ETH_ENTITY_TYPE_BEAN, true).setColumnType(BindingColumn.Type.COMBOBOX).setFilterClassType(FilterOperator.ClassType.ENUM).setEnumValues(EthEntityType.values()).setWidth(280);

        dataProvider = DataProviderFactory.create(true, bindingColumns);
        return ViewFactory.getInstance()
                .createTableView(SERVICE_INTERFACES, page, Resources.get(SERVICE_INTERFACES), dataProvider, null,
                        TableViewer.Options.GUI_SORTING_ENABLED, TableViewer.Options.SHOW_DEFAULT_TITLE_BAR,
                        TableViewer.Options.FILTERING_ENABLED, TableViewer.Options.GUI_FILTERING);
    }

    @Override
    public void dispose() {
        // not applicable for this view
    }

    public PDEntitySwapGeneralEditor getParent() {
        return parent;
    }

    public ViewPart getViewParts() {
        return splitViewer;
    }

    public TableViewer getTableViewer() {
        return tableViewer;
    }

    public DataProvider getDataProvider() {
        return dataProvider;
    }
}
