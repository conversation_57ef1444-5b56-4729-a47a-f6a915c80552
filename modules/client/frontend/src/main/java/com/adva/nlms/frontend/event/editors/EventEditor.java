/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: v<PERSON><PERSON><PERSON>
 */
package com.adva.nlms.frontend.event.editors;

import com.adva.common.model.navigation.TabName;
import com.adva.common.model.network.NetworkElement;
import com.adva.common.model.topology.TopologyElement;
import com.adva.common.model.topology.property.values.types.CommonNodeType;
import com.adva.common.model.tree.TreeItem;
import com.adva.common.util.ErrorLog;
import com.adva.common.util.PropertyFactory;
import com.adva.common.util.PropertyIdentifier;
import com.adva.common.util.filter.EnumOperator;
import com.adva.common.util.filter.FilterOperator;
import com.adva.common.workbench.EditorStopException;
import com.adva.common.workbench.PartInitException;
import com.adva.common.workbench.Selection;
import com.adva.common.workbench.WorkbenchPage;
import com.adva.common.workbench.binding.model.BindingColumn;
import com.adva.nlms.common.property.FNMPropertyConstants;
import com.adva.nlms.common.sm.model.ServiceObjectDTO;
import com.adva.nlms.frontend.ethernetring.EthernetRingUtil;
import com.adva.nlms.frontend.gui.tree.NodeTreeItem;
import com.adva.nlms.frontend.sm.tree.ServiceTreeItem;
import com.adva.nlms.frontendfx.workbench.binding.validator.UIPostValidatorObjectImpl;
import com.adva.common.workbench.binding.validator.UIValidatorObject;
import com.adva.common.workbench.viewer.TableViewer;
import com.adva.nlms.infrastucture.security.permission.api.PermissionAction;
import com.adva.nlms.common.TopologyNodeType;
import com.adva.nlms.common.event.EventProperties;
import com.adva.nlms.common.event.EventType;
import com.adva.nlms.common.paging.PageArea;
import com.adva.nlms.common.paging.PagingProperties;
import com.adva.nlms.common.paging.PagingRestriction;
import com.adva.nlms.infrastucture.security.CommonSecurityManager;
import com.adva.nlms.common.webhelp.NmsManualReference;
import com.adva.nlms.frontend.SessionManagerImpl;
import com.adva.nlms.frontend.common.paging.PagingSupportEditor;
import com.adva.nlms.frontend.event.AlertEvent;
import com.adva.nlms.frontend.event.AlertListener;
import com.adva.nlms.frontend.event.AlertState;
import com.adva.nlms.frontend.event.Event;
import com.adva.nlms.frontend.event.EventBuilder;
import com.adva.nlms.frontend.gistransfer.FiberTreeElement;
import com.adva.nlms.frontend.gui.tree.ErrorNetworkElement;
import com.adva.nlms.frontend.network.tree.LinkImpl;
import com.adva.nlms.frontend.synchronization.tree.SyncTreeNode;

import org.apache.commons.collections4.CollectionUtils;

import javax.swing.Timer;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.beans.PropertyChangeEvent;
import java.beans.PropertyChangeListener;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class EventEditor extends PagingSupportEditor<Event> {
  protected NetworkElement network;
  //handle notifications from server
  private EventHandlerPaging eventHandlerPaging;
  private String toolbarID;

  private Timer refreshTimer;
  public static final PropertyIdentifier ENABLE_BACKGROUND_COLORS = new PropertyIdentifier(EventEditor.class, "enableEventBackgroundColors");

  /**
   * The delay in mills after which the refresh will occur
   */
  protected static final int REFRESH_TIMER_DELAY = 3000;

  public static class SecurityEventEditor extends EventEditor {
    public SecurityEventEditor() {
      super(true, "secEvent-toolbar-context");
    }

    @Override
    public PageArea getPageArea() {
      return PageArea.SECURITY;
    }

    @Override
    protected boolean pass(Event event) {
      return super.pass(event) && event.isSecurity();
    }

    @Override
    public boolean isEnabled(Selection selection) {
      if (!CommonSecurityManager.isAllowed("GetSyE")) {
        return false;
      }
      Object object = selection.getObject();
      if (EthernetRingUtil.isEthernetRingTreeItem(object)) {
        return false;
      }
      if (object instanceof ServiceTreeItem && ((ServiceTreeItem)object).getDTOObject() != null && ((ServiceTreeItem)object).getDTOObject().isPDL3())
        return false;
      if(object instanceof ErrorNetworkElement) return false;
      else if(object instanceof TopologyElement){
        //enable for service graph selection only if user selects NE
        TopologyElement te = (TopologyElement) object;
        return te.isType(CommonNodeType.Value.NE);
      }
      else if (object instanceof SyncTreeNode || object instanceof FiberTreeElement) {
        return false;
      } else if (object instanceof NodeTreeItem<?> nodeTreeItem && nodeTreeItem.getTopologyNodeType() == TopologyNodeType.PD_SERVICE_INTENT
       && nodeTreeItem.getDTOObject() instanceof ServiceObjectDTO serviceObjectDTO && serviceObjectDTO.getLifeCycleState().isPlanned()) {
        return false;
      }
      else {
        return super.isEnabled(selection);
      }
    }
  }

  public static class AlarmHistEditor extends EventEditor {
    private final boolean eventHistoryEnabled;
    public AlarmHistEditor() {
      super(true, "alarm-hist-toolbar-context");
      eventHistoryEnabled = !SessionManagerImpl.getInstance().getCommManager().getServerService().getServerControl()
                                               .getServerProperty(FNMPropertyConstants.DISABLE_EVENT_HISTORY, FNMPropertyConstants.DISABLE_EVENT_HISTORY_DEFAULT);
    }

    @Override
    public PageArea getPageArea() {
      return  PageArea.ALARM_HIST;
    }

    @Override
    protected boolean isRelevant(AlertEvent alertEvent) {
      // only accept UpdateState
      return alertEvent.getType() == AlertEvent.ALERT_STATE_UPDATED;
    }

    @Override
    public boolean isEnabled(Selection selection){
        if (!super.isEnabled(selection)) {
            return false;
        }

      if(selection.getObject() instanceof SyncTreeNode)
      {
        SyncTreeNode sn = (SyncTreeNode)selection.getObject();
        if(sn.getTopologyNodeType() == TopologyNodeType.CLOCK_PROBE_NODE )
            return false;
      }
      if(selection.getObject() instanceof TopologyElement || selection.getObject() instanceof FiberTreeElement) {
        return false;
      } else if (selection.getObject() instanceof ServiceTreeItem &&
              ((ServiceTreeItem)selection.getObject()).getDTOObject() != null &&
                      ((ServiceTreeItem)selection.getObject()).getDTOObject().isPDL3()) {
        return false;
      } else if (selection.getObject() instanceof NodeTreeItem<?> nodeTreeItem && nodeTreeItem.getTopologyNodeType() == TopologyNodeType.PD_SERVICE_INTENT
              && nodeTreeItem.getDTOObject() instanceof ServiceObjectDTO serviceObjectDTO && serviceObjectDTO.getLifeCycleState().isPlanned()) {
        return false;
      } else {
        return eventHistoryEnabled && CommonSecurityManager.isAllowed("GetEvt");
      }
    }
  }

  public static class EntityEventEditor extends AlarmsEditor {
    final FilterOperator<Object> moduleFilter;
    private String staticTitle;

    public EntityEventEditor(WorkbenchPage page, String entityIndex) {
      this(page, entityIndex, null);
    }
    public EntityEventEditor(WorkbenchPage page, String entityIndex, Selection selection) {
      this(page, null, PagingProperties.Event.OBJECTINDEX, selection, entityIndex);
    }

    public EntityEventEditor(WorkbenchPage page, String [] entityDescription, Selection selection) {
      this(page, null, PagingProperties.Event.ENTITYDESCRIPTION, selection, entityDescription);
    }

    public EntityEventEditor(WorkbenchPage page, String staticTitle, PagingProperties.Event eventType, Selection selection, String ... entityIndex) {
      this(page, staticTitle, selection, new EnumOperator(FilterOperator.Type.EQUALS, eventType.getBeanName(), entityIndex));
    }

    public EntityEventEditor(WorkbenchPage page, String staticTitle, Selection selection, FilterOperator moduleFilter) {
      super();
      this.showTotalCounterPanel = false;
      this.staticTitle = staticTitle;

      this.moduleFilter = moduleFilter;
      setUserObject("isRestoreSession", true);
      if (selection != null) {
        setSelection(selection);
      }
      try {
        init(page);
      } catch (PartInitException e) {
        ErrorLog.log(e);
      }
    }

    @Override
    protected void updateFrameTitle(Selection selection) {
      if (staticTitle != null) {
        setTitle(staticTitle);
        setTabTitle(staticTitle);
      } else {
        super.updateFrameTitle(selection);
      }
    }

    @Override
    public PageArea getPageArea() {
      return PageArea.ALARMS;
    }

    public String getToolbarID() {
      return "correlated-alarms-toolbar-context";
    };

    @Override
    public boolean isClonedEditor() {
      return true;
    }

    @Override
    public List<FilterOperator<Object>> getStaticFilters() {
      return Collections.singletonList(moduleFilter);
    }

    @Override
    public String getHelpID() {
      return NmsManualReference.CORRELATED_ALARMS.toString();
    }
  }

  @Override
  public boolean isSyncRelated() {
    return getSelection()!= null && getSelection().getObject() != null && getSelection().getObject() instanceof SyncTreeNode;
  }

  protected void addFilter(String name, String... entityIndex) {
    //get current filters
    List<FilterOperator> filters = pagingRestriction.getFilterOperators();

    //create new filter list if necessary
    if (filters == null || filters.isEmpty()) {
      filters = new ArrayList<>();
    }

    //add or replace filter if needed
    updateFilterOperator(filters, new EnumOperator(FilterOperator.Type.EQUALS, name, entityIndex));

    //update filters in paging restriction object
    pagingRestriction.setFilterOperators(filters);
  }

  private void updateFilterOperator(List<FilterOperator> filters, FilterOperator newFilter) {
    List<FilterOperator> currentFilters = new ArrayList<>();

    //try to find existing filters for object or module index
    for (FilterOperator filter : filters) {
      if ((filter.getName().compareTo(PagingProperties.Event.MODULEINDEX.getBeanName()) == 0) ||
        (filter.getName().compareTo(PagingProperties.Event.OBJECTINDEX.getBeanName()) == 0)) {
        currentFilters.add(filter);
      }
    }

    //remove if same filter exists
    if (!currentFilters.isEmpty()) {
      filters.removeAll(currentFilters);
    }

    //add new filter
    filters.add(newFilter);
  }


  public EventEditor(boolean showTotalCounter, String toolbarID) {
    super(showTotalCounter);
    this.toolbarID = toolbarID;

    setTitle(getPagingFacilities().getName());
    setTabTitle(getPagingFacilities().getName());

    this.eventHandlerPaging = new EventHandlerPaging(this);

    //timer against multiple refresh notifications
    refreshTimer = new Timer(REFRESH_TIMER_DELAY, new ActionListener() {
      @Override
      public void actionPerformed(ActionEvent e) {
        refreshTimer.stop();
        refresh();
      }
    });

    PropertyFactory.addPropertyChangeListener(backColorPropertyListener);
  }

  PropertyChangeListener backColorPropertyListener = new PropertyChangeListener() {
    @Override
    public void propertyChange(PropertyChangeEvent evt) {
      if (evt.getPropertyName().equalsIgnoreCase(ENABLE_BACKGROUND_COLORS.getPropertyName())) {
        if (tableViewer != null) {
          tableViewer.setRowBackgroundColoring(Boolean.parseBoolean((String) evt.getNewValue()));
        }
      }
    }
  };

  @Override
  public void start(Selection selection) {

    this.network = (selection.getObject() instanceof TreeItem) ? (NetworkElement) selection.getObject() : null;

    ids = getIDs(selection);
    addAlertStateListeners();

    pagingRestriction.setPageOffset(-1);
    pagingRestriction.setPacketServiceTabSelected(((NodeTreeItem<?>) selection.getObject()).getParent().getTreeTabName().equals(TabName.PACKET_SERVICES_TREE));
    super.start(selection);
  }

  @Override
  public void stop() throws EditorStopException {
    super.stop();
    ids = null;
    removeAlertStateListeners();
  }

  protected AlertListener eventNotificationListener = new AlertListener() {
    @Override
    public void stateChanged(final AlertEvent event) {

      if (event.getType() == AlertEvent.UPDATE_STATE) {
        refreshAfterTimer();
        return;
      }

      //no handling for alarm counter state update
      if (event.getType() == AlertEvent.ALERT_STATE_UPDATED) {
        return;
      }

      if ((event.getAlerts() == null || event.getAlerts().isEmpty())) {
        return;
      }

      //we need ask isStarted again as the code is invoked in EDT and might happen
      //that between stateChanged and invokeLater the editor is stopped
      if (isStarted()) {
        getSite().getPage().getContextManager().invokeLater(()->
          eventHandlerPaging.handleStateChanged(event)
        );
      }
    }
  };

  //TODO added during refactoring as temporary solution
  protected void addAlertStateListeners() {
    getSessionManager().getAlertManager().removeAlertStateListener(eventNotificationListener);
    getSessionManager().getAlertManager().addAlertStateListener(eventNotificationListener);
  }

  protected void removeAlertStateListeners() {
    getSessionManager().getAlertManager().removeAlertStateListener(eventNotificationListener);
  }


  @Override
  public void init(WorkbenchPage page) throws PartInitException {
    super.init(page);
    TableViewer tableViewer = getTableViewer();
    tableViewer.setRowBackgroundColoring(PropertyFactory.getBooleanProperty(EventEditor.ENABLE_BACKGROUND_COLORS));
  }

  @Override
  public void addActionListener() {
    if (PermissionAction.AcknowledgeEvent.isGranted()) {
      UIValidatorObject<Event, Boolean> aValidator = new UIPostValidatorObjectImpl<Event, Boolean>(true, Collections.EMPTY_LIST) {
        @Override
        public boolean validate(Event bean, Boolean value, String[] error) {
          getSessionManager().getAlertManager().acknowledge(new long[]{bean.getEventId()}, value);
          return true;
        }

        @Override
        public void postValidation(Event bean, Boolean value) {}

        @Override
        public void onMultiselectionValidationEnd() {}
      };

      ((BindingColumn)dataProvider.getBindingColumn("acknowledge")).addValidator(aValidator);
    }
  }

  @Override
  public PageArea getPageArea() {
    return PageArea.EVENTS;
  }

  private static final Event emptyEvent = new Event(true);
  @Override
  public Object createEmptyBean() {
    return emptyEvent;
  }

  @Override
  public String getToolbarID() {
    return toolbarID;
  }


  public void removeEvents(Collection<Event> eventsToDelete) {
    for (Event event : eventsToDelete) {
      removeDTOInModel(event);
    }
    //eventSortingTimer.restart();
  }

  public void updateEvents(Collection<Event> eventsToUpdate, boolean isNew) {
    if (isNew) {
      //sync does not support new evetns/alarms
      if (getSelection().getObject() instanceof SyncTreeNode) {
        return;
      }
    }
    for (Event event : eventsToUpdate) {

      if (pass(event)) {
        //!important dont remove this because then AlertHandleThread handles update,
        // leading to focus change by the JavaFX Thread
//        Utilities.invokeLater(() -> );
        updateDTOInModel(event, isNew);
      }
    }
  }


  protected boolean pass(Event event) {
    return isRelevantEvent(event) || getBeanByID(event.getId()) != null;
  }

  protected boolean isAlarm(Event event) {
    return event.getEventType() == EventType.RAISED;
  }

  @Override
  protected int getIDFor(Event event) {
    return event.getId();
  }

  @Override
  protected Collection<Event> convertData(Collection dtos){
    if (CollectionUtils.isNotEmpty(dtos) && dtos.iterator().next() instanceof EventProperties) {
      dtos = EventBuilder.createEventList(dtos);
    }
    return dtos;
  }

  @Override
  protected PagingRestriction convertData(PagingRestriction restriction){
    if (restriction.getPageRefDTO() instanceof Event) {
      restriction.setPageRefDTO(EventBuilder.createEventProperties((Event) restriction.getPageRefDTO()));
    }
    return restriction;
  }

  /*
  * Check in overall if notification needs to be added to the current eventEditor.
  * isRelevantEvent() will check it again more in detail, upon add/update methods for each event.
  * Method can be overwritten by sub-classes
  * */
  protected boolean isRelevant(AlertEvent alertEvent) {
    return this.network != null;
  }

  /*
  * Check if notification applies to the current eventEditor.
  *
  * */
  protected boolean isRelevantEvent(Event newBean) {
    boolean isRelevantNode = false;
    int[] parents = newBean.getParentsIDs();
    int[] serviceIDs = newBean.getServiceIDs();

    if (ids == null)
      return false;

    for (int i : ids) {
      if (newBean.getSyncRouteID() == i) {
        return true;
      }
      if (newBean.getSyncNodeIds().length > 0) {
        for (int j : newBean.getSyncNodeIds()) if (i == j) return true;
      }
      if (newBean.getSyncNcdIds().length > 0) {
        for (int j : newBean.getSyncNcdIds()) if (i == j) return true;
      }
      if (parents.length > 0) {
        for (int j : parents) if (i == j) return true;
      }
      if (serviceIDs.length > 0)
        for (int j : serviceIDs) if (i == j) return true;
      if (i == newBean.getSubnetID() || i == newBean.getNeID() ||
        i == newBean.getLineID()) isRelevantNode = true;
    }
    //consider pair protected line too
    if (network instanceof LinkImpl && newBean.getLineID() == ((LinkImpl) network).getProtectedLineID())
      isRelevantNode = true;
    return isRelevantNode;
  }

  protected void updateLabels() { }

  protected void updateLabels(AlertState alertState) { }

  @Override
  protected int compareElements(Event o1, Event o2, String sortingCriteria, BindingColumn.SortingType sortingType, boolean isReversed) {

    if (!sortingCriteria.equals("timeFormatted") && !sortingCriteria.equals("nmsTimeFormatted"))
      return super.compareElements(o1, o2, sortingCriteria, sortingType, isReversed);

    int result = 0;
    if (sortingCriteria.equals("timeFormatted")) {
      //special case for TIME column

      //get NE/NMS Timestamp from beans
      Long nmsTimeStamp1 = (Long) getBeanFieldValue(o1, "nmsTimeStamp");
      Long nmsTimeStamp2 = (Long) getBeanFieldValue(o2, "nmsTimeStamp");
      Long neTimeStamp1 = (Long) getBeanFieldValue(o1, "neTimeStamp");
      Long neTimeStamp2 = (Long) getBeanFieldValue(o2, "neTimeStamp");


      final BindingColumn currentSortedColumn = getDataProvider().getCurrentSortedColumn();
      BindingColumn.SortingType timeSortingType = currentSortedColumn.getBeanName().equals("timeFormatted")
        ? currentSortedColumn.getSortingType()
        : getPagingFacilities().getBindingColumn("timeFormatted").getDefaultSortingType();

      result = timeSortingType == BindingColumn.SortingType.ASCENDING
        ? (neTimeStamp2 > 0 ? neTimeStamp2 : nmsTimeStamp2).compareTo(neTimeStamp1 > 0 ? neTimeStamp1 : nmsTimeStamp1)
        : (neTimeStamp1 > 0 ? neTimeStamp1 : nmsTimeStamp1).compareTo(neTimeStamp2 > 0 ? neTimeStamp2 : nmsTimeStamp2);
    } else if (sortingCriteria.equals("nmsTimeFormatted")) {
      final BindingColumn currentSortedColumn = getDataProvider().getCurrentSortedColumn();
      Long nmsTimeStamp1 = (Long) getBeanFieldValue(o1, "nmsTimeStamp");
      Long nmsTimeStamp2 = (Long) getBeanFieldValue(o2, "nmsTimeStamp");

      result = currentSortedColumn.getSortingType() == BindingColumn.SortingType.ASCENDING
        ? nmsTimeStamp2.compareTo(nmsTimeStamp1)
        : nmsTimeStamp1.compareTo(nmsTimeStamp2);
      if (result == 0) {
        result = compareElements(o1, o2, "timeFormatted", sortingType, isReversed);
      }
    }

    //still the same?
    if (result == 0) {
      return getPagingFacilities().getBindingColumn("eventId").getDefaultSortingType() == BindingColumn.SortingType.ASCENDING
        ? Long.compare(o2.getEventId(), o1.getEventId())
        : Long.compare(o1.getEventId(), o2.getEventId());
    }

    return result;
  }

  @Override
  protected boolean doPassFilter(Event newBean) {
    boolean passFilter = super.doPassFilter(newBean);
    //check Managed/Discovered filter in case selection is Customer/Folder
    if (newBean.getServiceIDs().length > 0) {
      TopologyNodeType nodeType = getNetworkTreeNodeType(getSelection());
      if (nodeType == TopologyNodeType.SERVICE_FOLDER ||
        nodeType == TopologyNodeType.CUSTOMER ||
        nodeType == TopologyNodeType.CUSTOMER_FOLDER ||
        nodeType == TopologyNodeType.TRAIL_FOLDER) {
        //only now check filter
        FilterOperator managedDiscoveredFilter = null;
        for (FilterOperator filterOperator : pagingRestriction.getFilterOperators()) {
          if (filterOperator.getName().equals(PagingProperties.Service.MANAGED_STATE.getBeanName())) {
            managedDiscoveredFilter = filterOperator;
            break;
          }
        }
        if (managedDiscoveredFilter != null) {
          //if there is at least one ConnID matching the filter, return true
          for (Map.Entry<Integer, Boolean> entry : newBean.getConnectionManagementState().entrySet()) {
            if (entry.getValue().equals(managedDiscoveredFilter.getValue()))
              return passFilter;
          }
          return false;
        }
      }
    }
    return passFilter;
  }

  private void refreshAfterTimer() {
    refreshTimer.restart();
  }

  @Override
  protected List<TableViewer.Options> getTableOptions() {
    final List<TableViewer.Options> tableOptions = super.getTableOptions();
    if (!PermissionAction.ModifyEventFilterSettings.isGranted())
      tableOptions.remove(TableViewer.Options.FILTERING_ENABLED);
    return tableOptions;
  }

  @Override
  public void dispose() {
    removeAlertStateListeners();
    PropertyFactory.removePropertyChangeListener(backColorPropertyListener);
    super.dispose();
  }
}

