/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: svh
 */

package com.adva.nlms.frontend.pdentities.dto;

import com.adva.common.workbench.binding.Binding;
import com.adva.nlms.common.topology.EthEntityType;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class EntityTypeSwapDTO {
    private String neName;
    private String entityName;
    private EthEntityType ethEntityType;
    private List<EthEntityType> ethEntityTypeList = new ArrayList<>();

    public EntityTypeSwapDTO(String neName,
                             String entityName,
                             EthEntityType ethEntityType,
                             List<EthEntityType> ethEntityTypeList) {
        this.neName = neName;
        this.entityName = entityName;
        this.ethEntityType = ethEntityType;
        this.ethEntityTypeList = ethEntityTypeList;
    }

    public String getNeName() {
        return neName;
    }

    public void setNeName(String neName) {
        this.neName = neName;
    }

    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    @Binding(valuesProperty = "ethEntityTypeList")
    public EthEntityType getEthEntityType() {
        return ethEntityType;
    }

    public void setEthEntityType(EthEntityType ethEntityType) {
        this.ethEntityType = ethEntityType;
    }

    public List<EthEntityType> getEthEntityTypeList() {
        return ethEntityTypeList;
    }

    public void setEthEntityTypeList(List<EthEntityType> ethEntityTypeList) {
        this.ethEntityTypeList = ethEntityTypeList;
    }

    @Override
    public String toString() {
        return "EntityTypeSwapDTO{" +
                "neName='" + neName + '\'' +
                ", entityName='" + entityName + '\'' +
                ", ethEntityType=" + ethEntityType +
                ", ethEntityTypeList=" + ethEntityTypeList +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        EntityTypeSwapDTO that = (EntityTypeSwapDTO) o;
        return Objects.equals(neName, that.neName)
                && Objects.equals(entityName, that.entityName)
                && Objects.equals(ethEntityType, that.ethEntityType)
                && Objects.equals(ethEntityTypeList, that.ethEntityTypeList);
    }

    @Override
    public int hashCode() {
        return Objects.hash(neName, entityName, ethEntityType, ethEntityTypeList);
    }
}
