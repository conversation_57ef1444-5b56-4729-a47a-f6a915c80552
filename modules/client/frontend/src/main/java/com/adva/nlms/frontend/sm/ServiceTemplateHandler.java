/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: blee
 */
package com.adva.nlms.frontend.sm;

import com.adva.common.model.template.Group;
import com.adva.common.model.template.Keyword;
import com.adva.common.model.template.Parameter;
import com.adva.common.model.template.ParameterGroup;
import com.adva.common.model.template.ParameterProperty;
import com.adva.apps.sm.Definition;
import com.adva.apps.sm.ServiceLayer;
import com.adva.nlms.common.sm.ServiceTemplateHelper;
import com.adva.nlms.common.sm.enums.TeraFlexProvisionProfile;
import com.adva.nlms.common.yp.db.CardType;
import com.adva.nlms.frontend.SessionManagerImpl;
import com.adva.nlms.frontend.comm.service.rest.ServiceManagerRestClient;
import com.adva.nlms.opticalparameters.api.enums.ParameterName;

import java.util.ArrayList;
import java.util.Collections;
import java.util.EnumSet;
import java.util.List;

/**
 * It is used at the client to get service templates and additional info.
 *
 * @see ServiceTemplateHelper
 */
public class ServiceTemplateHandler {

    private ServiceTemplateHandler() {
    }

    public static ParameterGroup getTemplate(Group name, CardType ct, ServiceLayer sl) {
        return getServiceManagerRestClient().getTemplate(name, ct, sl); // the first call is cached.
    }

    private static ServiceManagerRestClient getServiceManagerRestClient() {
        return SessionManagerImpl.getInstance().getServiceManager().getServiceManagerRestClient();
    }

    public static List<CardType> getCardTypes(ServiceLayer sl) {
        return getServiceManagerRestClient().getCardTypes(sl);
    }

    public static List<Definition.ServiceType> getServiceTypes(Definition.ServiceCreationMode mode, ServiceLayer sl, boolean externalChannel) {
        return getServiceManagerRestClient().getServiceTypes(mode, sl, externalChannel);
    }

    public static void updateValues(Parameter p) {
        ServiceTemplateHelper.updateValues(p, true, getServiceManagerRestClient()::getValues);
    }

    public static List<String> getUpdatedValues(Parameter p) {
        updateValues(p);
        return p.getValues() == null ? Collections.emptyList() : p.getValues();
    }

    public static boolean isCardTypeValid(CardType ct, ServiceLayer sl, Definition.ServiceType st) {
        return getServiceManagerRestClient().isCardTypeValid(ct, sl, st);
    }

    public static boolean isAdvanced(Parameter p) {
        return EnumSet.of(
            Keyword.SDHT_L,
            Keyword.SDHT_MS,
            Keyword.SDHT_OTU,
            Keyword.SDHT_PCSL,
            Keyword.SDPER_MS,
            Keyword.SDPER_OTU,
            Keyword.SDPER_PCS,
            Keyword.TIMMODE_ODU,
            Keyword.TIMMODE_OTU,
            Keyword.TIMMODE_S,
            Keyword.TRC_FORM_S
        ).contains(Keyword.valueOfString(p.getKeyword()));
    }

    public static boolean hasAdvancedParameters(ParameterGroup pg) {
        if (pg == null) {
            return false;
        }
        for (Parameter p : pg.getParameters()) {
            if (isAdvanced(p)) {
                return true;
            }
        }
        for (ParameterGroup sub : pg.getParameterGroups()) {
            if (hasAdvancedParameters(sub)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Filters out unchanged parameters.
     *
     * @param template the template to filter.
     */
    public static void filterUnchangedParameters(ParameterGroup template) {
        EnumSet<Keyword> ignoredKeys = EnumSet.of(
            Keyword.WIZARD,
            Keyword.ACTION,
            Keyword.SERVICE_LAYER,
            Keyword.SERVICE_TYPE,
            Keyword.FLOW_POINT,
            Keyword.ERP,
            Keyword.LINK_KEY,
            Keyword.SERVICE_DIRECTION_REVERSED,
            Keyword.EXTERNAL_WDM_CHANNEL,
            Keyword.CUSTOMER,
            Keyword.SERVICE_FOLDER,
            Keyword.DIVERSITY_TYPE,
            Keyword.DIVERSITY_TRAIL_ID,
            Keyword.DIVERSITY_CREATION,
            Keyword.DIVERSITY_DELETION,
            Keyword.SERVICE_NAME
        );

        for (Parameter p : template.getParameters()) {
            Keyword k = Keyword.valueOfString(p.getKeyword());
            boolean unchanged = !p.isChanged() || !p.getVisible() || !p.getEditable();
            if (!ignoredKeys.contains(k) && unchanged) {
                template.removeParameter(p);
            }
        }

        for (ParameterGroup pg : template.getParameterGroups()) {
            if (pg.getVisible()) {
                filterUnchangedParameters(pg);
            } else {
                template.removeParameterGroup(pg);
            }
        }
    }

    public static ParameterProperty createParameterProperty(ParameterGroup pg, boolean editable) {
        if (!pg.getVisible()) {
            return null;
        }

        List<ParameterProperty> list = new ArrayList<>();
        for (Parameter p : pg.getParameters()) {
            if (!p.getVisible()) {
                continue;
            }

            Definition.DisplayType dt = getDisplayType(p);
            ParameterProperty pp = new ParameterProperty(p, editable, dt);
            pp.setSourceObject(p);
            list.add(pp);
        }

        for (ParameterGroup g : pg.getParameterGroups()) {
            ParameterProperty pp = createParameterProperty(g, editable);
            if (pp != null) {
                list.add(pp);
            }
        }

        ParameterProperty pp = new ParameterProperty(new Parameter(pg.getGroup(), "", false));
        pp.setSourceObject(pg);
        pp.addSubProperties(list);
        return pp;
    }

    public static Definition.DisplayType getDisplayType(Parameter p) {
        boolean isCapabilityBrokerParameter = p.getParent() != null &&
                Group.CAPABILITY_BROKER_ATTRIBUTES.toString().equals(p.getParent().getGroup());
        return isCapabilityBrokerParameter ? getDisplayTypeByParameterName(p) : getDisplayTypeByKeyword(p);
    }

    private static Definition.DisplayType getDisplayTypeByParameterName(Parameter p){
        ParameterName parameterName = ParameterName.fromString(p.getKeyword()).orElse(null);
        if(parameterName == null){
            throw new IllegalArgumentException("Couldn't resolve keyword to ParameterName");
        }

        return switch (parameterName) {
            case CHANNEL, CHANNEL_RX,
                    MODULATION, FEC,
                    SETPOINT, FILTER_SHAPE,
                    FREQUENCY_DETUNE, CONSTELLATION_MAPPING,
                    POLARIZATION_TRACKING, CDC_RANGE,
                    GLQ, CHANNEL_BANDWIDTH,
                    FTS_CONTROL, TERMINATION_MODE ->
                    Definition.DisplayType.COMBO_BOX;
            default -> Definition.DisplayType.TEXT_FIELD;
        };
    }

    private static Definition.DisplayType getDisplayTypeByKeyword(Parameter p) {
        Keyword keyword = Keyword.valueOfString(p.getKeyword());

        switch (keyword) {
//            Sonar required to merge the previous cases into this one using comma-separated label
            case ADMIN,
                 ALSMODE,
                 APS_HOLDOFF,
                 BAND__PROVISION,
                 BEHAVE,
                 BIP,
                 BUNDLE,
                 BUNDLEN,
                 CAP__PROVISION,
                 CDC_MODE,
                 CDC_OPR,
                 CDC_RANGE,
                 CHANNEL_PLAN,
                 CHAN_BW,
                 CHARANGE__PROVISION,
                 CHA__SPC,
                 CIPHER,
                 CODEGAIN,
                 DEPLOY,
                 DISCO,
                 ENCODE,
                 EOC_CRY,
                 ERRFWD,
                 FEC,
                 FENDCOM,
                 JNX_FEC,
                 JNX_WAVELENGTH,
                 LANE1__PROVISION,
                 LANEGROUP__PROVISION,
                 LSROFFDLY,
                 LSROFFONTM,
                 MAP,
                 MODE,
                 MODNETFEC,
                 MUXMETHOD,
                 ODTU_TYPE,
                 ODU_TRIB_PORT,
                 ODU_TRIB_SLOT,
                 PATHID,
                 PAUSE_RCV,
                 PAUSE_TRMT,
                 PLUGTYP__PROVISION,
                 PMODE__PROVISION,
                 PORTROLE__PROVISION,
                 PRIO__PROVISION,
                 PT,
                 RATELIMIT,
                 REACH__PROVISION,
                 SDHT_L,
                 SDHT_MS,
                 SDHT_OTU,
                 SDHT_PCSL,
                 SDPER_MS,
                 SDPER_OTU,
                 SDPER_PCS,
                 SIGDEF,
                 STUFF,
                 TERM,
                 TIMING,
                 TIMMODE_ODU,
                 TIMMODE_OTU,
                 TIMMODE_S,
                 TRC_FORM_S,
                 TYPE__EQUIPMENT,
                 TYPE__FACILITY,
                 UTAG__PROVISION,
                 VCGTYPE,
                 CHANNEL_LANE_1,
                 CHANNEL_LANE_2,
                 CHANNEL__PROVISION,
                 CHANNEL_RX__PROVISION,
                 CONSTELLATION,
                 FLTSP,
                 FREQ_OFFSET:
                return ServiceTemplateHelper.isF8Native(p)
                        ? Definition.DisplayType.TEXT_FIELD
                        : Definition.DisplayType.COMBO_BOX;

            case RATE__PROVISION:
                return ServiceTemplateHelper.isF8Native(p)
                        ? Definition.DisplayType.AREA_BOX
                        : Definition.DisplayType.COMBO_BOX;
            case VETH_AID:
                return Definition.DisplayType.COMBO_BOX;
            default:
                return Definition.DisplayType.TEXT_FIELD;
        }
    }

    /**
     * Only TeraFlex NFC case should have DisplayType TEXT_FIELD due to the bug in applying TeraFlexProfile on ParameterPage
     */
    private static boolean isFixedTeraflexParam(Parameter p){
        boolean isFixedTeraflexParam = false;
        ParameterGroup cardParameters = p.getParent().getParent();
        if(cardParameters != null && Group.CARD_PARAMETERS.toString().equals(cardParameters.getGroup())){
            Parameter ctParam = cardParameters.getParameter(Keyword.CARD_TYPE);
            CardType ct = ctParam == null ? null : CardType.valueOfString(ctParam.getValue());
            ParameterGroup serviceDefinition = cardParameters.getRoot();
            Parameter serviceLayer = serviceDefinition.getParameter(Keyword.SERVICE_LAYER);
            isFixedTeraflexParam = TeraFlexProvisionProfile.isValidCardType(ct) &&
                                   serviceLayer != null &&
                                   ServiceLayer.OCS.getLabel().equals(serviceLayer.getValue());
        }
        return isFixedTeraflexParam;
    }

    public static int getNodeMibVersion(Parameter p) {
        ParameterGroup node = p.getParent();
        while (node != null) {
            if (node.getGroup().equals(Group.SOURCE_NODE.toString()) ||
                node.getGroup().equals(Group.DESTINATION_NODE.toString())) {
                break;
            }
            node = node.getParent();
        }

        try {
            Parameter vp = node == null ? null : node.getParameter(Keyword.MIB_VERSION);
            return vp == null ? -1 : Integer.parseInt(vp.getValue());

        } catch (NumberFormatException e) {
            return -1;
        }
    }
}
