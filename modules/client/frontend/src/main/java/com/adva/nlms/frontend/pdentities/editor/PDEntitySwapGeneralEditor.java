/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: svh
 */

package com.adva.nlms.frontend.pdentities.editor;

import com.adva.common.util.ErrorLog;
import com.adva.common.workbench.PartInitException;
import com.adva.common.workbench.Selection;
import com.adva.common.workbench.ViewPart;
import com.adva.common.workbench.WorkbenchPage;
import com.adva.common.workbench.WorkbenchSiteImpl;
import com.adva.nlms.common.discover.common.dto.PortDTO;
import com.adva.nlms.common.pdentityswap.dto.PDEntitySwapDTO;
import com.adva.nlms.common.sm.model.ServiceObjectDTO;
import com.adva.nlms.common.topology.EthEntityType;
import com.adva.nlms.frontend.comm.service.rest.PDEntitySwapRestClient;
import com.adva.nlms.frontend.common.OperationFailedException;
import com.adva.nlms.frontend.gui.CommonWorkbenchPage;
import com.adva.nlms.frontend.gui.tree.NodeTreeItem;
import com.adva.nlms.frontend.pdentities.dto.EntityTypeSwapDTO;
import com.adva.nlms.frontend.pdentities.view.PDEntitySwapGeneralView;
import com.adva.nlms.frontendfx.workbenchfx.dialog.AbstractDialogEditorPart;
import com.adva.nlms.frontendfx.workbenchfx.viewer.tab.MultiTabEditorPart;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class PDEntitySwapGeneralEditor extends AbstractDialogEditorPart {
    private PDEntitySwapGeneralView pdEntitySwapGeneralView;

    @Override
    public void dispose() {
        super.dispose();
        pdEntitySwapGeneralView.dispose();
    }

    @Override
    public void init(WorkbenchPage page) throws PartInitException {

        PDEntitySwapRestClient pdEntitySwapRestClient = ((CommonWorkbenchPage) page).getSessionManager().getCommManager().getServerService()
                .getPDEntitySwapRestClient();

        ViewPart viewComponent = buildView(page);
        if (viewComponent == null){
            ErrorLog.error("Editor ["+this.getClass().getName()+"] does not define a view component -> method buildView() should be implemented in editor.");
        }
        setSite(new WorkbenchSiteImpl(page, "AbstractConfigPageEditorPart::" + getTabTitle(), viewComponent));


        try {
            Selection selection = page.getTreeSelectionProvider().getSelection();
            if(selection.getObjects().size() == 1) {
                Object selectedObject = selection.getObject();
                if(selectedObject instanceof NodeTreeItem<?> nodeTreeItem
                        && nodeTreeItem.getDTOObject() instanceof ServiceObjectDTO serviceObjectDTO) {
                    int serviceId = serviceObjectDTO.getId();
                    PDEntitySwapDTO pdEntitySwapDTO = pdEntitySwapRestClient.search(String.valueOf(serviceId));
                    List<EntityTypeSwapDTO> entityTypeSwapDTOList = new ArrayList<>();
                    populateDTO(entityTypeSwapDTOList, pdEntitySwapDTO);
                    pdEntitySwapGeneralView.getDataProvider().clearAddAllRows(entityTypeSwapDTOList);
                }
            }
        } catch (OperationFailedException e) {
            ErrorLog.error("PD Entity search failed: " + e.getMessage());
        }

    }

    private void populateDTO(List<EntityTypeSwapDTO> entityTypeSwapDTOList, PDEntitySwapDTO pdEntitySwapDTO) {
        List<PortDTO> portDTOS = pdEntitySwapDTO.getPortDTOS();

        for( PortDTO portDTO : portDTOS) {
            EntityTypeSwapDTO entityTypeSwapDTO = new EntityTypeSwapDTO(
                    portDTO.getNetworkElementDTO().name + " (" + portDTO.getNetworkElementDTO().ipAddress + ")",
                    portDTO.getShortDescription(),
                    portDTO.getEthEntityType(),
                    Arrays.asList(EthEntityType.UNI, EthEntityType.INNI));
            entityTypeSwapDTOList.add(entityTypeSwapDTO);
        }

    }

    @Override
    public void start(Selection selection) {
        super.start(selection);
        super.isStarted.set(true);
    }

    void close() {
        ((MultiTabEditorPart) getParent()).firePropertyChange(PROP_CLOSEABLE, false, true);
    }

    @Override
    public String getTabTitle() {
        return "General";
    }

    private ViewPart buildView(WorkbenchPage page) {
        pdEntitySwapGeneralView = new PDEntitySwapGeneralView(page, this);
        return pdEntitySwapGeneralView.getViewParts();
    }

    public PDEntitySwapGeneralView getPdEntitySwapGeneralView() {
        return pdEntitySwapGeneralView;
    }
}
