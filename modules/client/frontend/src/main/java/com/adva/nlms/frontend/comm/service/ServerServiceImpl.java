/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: v<PERSON><PERSON><PERSON>
 */

package com.adva.nlms.frontend.comm.service;

import com.adva.common.util.net.WebResource;
import com.adva.common.workbench.dialog.DialogFactory;
import com.adva.common.workbench.login.ConnectionBean;
import com.adva.nlms.common.da.DataAccessDto;
import com.adva.nlms.common.rest.MDRestPath;
import com.adva.nlms.common.rest.WebTargetMapping;
import com.adva.nlms.common.security.CertificateDataDTO;
import com.adva.nlms.frontend.comm.service.rest.*;
import com.adva.nlms.frontend.comm.service.rest.core.RestClientBuilder;
import com.adva.nlms.frontend.comm.service.rest.core.RestClientRequestFilter;
import com.adva.nlms.frontend.comm.service.rest.core.RestWebResource;
import com.adva.nlms.frontend.comm.service.rest.security.CompositeX509TrustManager;
import com.adva.nlms.frontend.comm.service.rest.security.SSLHandler;
import com.adva.nlms.frontend.comm.service.rest.security.TrustStoreManager;
import com.adva.nlms.frontend.comm.service.security.SSOElsCtrlRestClient;
import com.adva.nlms.frontend.comm.service.security.SecurityCtrlRestClient;
import com.adva.nlms.frontend.comm.service.security.UserCtrlRestClient;
import com.adva.nlms.frontend.comm.service.security.UserDataRestClient;
import com.adva.nlms.frontend.comm.service.security.UserSessionDataRestClient;
import com.adva.nlms.frontend.comm.service.security.approval.PrivilegeApprovalRestClient;
import com.adva.nlms.frontend.comm.service.security.fallback.FallbackUserNERestClient;
import com.adva.nlms.frontend.common.OperationFailedException;
import com.adva.nlms.frontend.event.thresholdCrossingAlert.rest.TCACtrlRestClientImpl;
import com.adva.nlms.frontend.event.thresholdCrossingAlert.rest.TCARestClient;
import com.adva.nlms.frontend.gistransfer.FiberRestClient;
import com.adva.nlms.frontend.network.AlienDwdmRestClient;
import com.adva.nlms.frontend.network.CustomDeviceRestClient;
import com.adva.nlms.frontend.network.DataAccessRESTClient;
import com.adva.nlms.frontend.network.InventoryRestClient;
import com.adva.nlms.frontend.network.NetworkElementCapabilitiesRestClient;
import com.adva.nlms.frontend.network.NetworkElementOperationRestClient;
import com.adva.nlms.frontend.ni.config.NIConfigRestClient;
import com.adva.nlms.frontend.ni.config.NIWebSocketRestClient;
import com.adva.nlms.frontend.performance.PerformanceManagerRestClient;
import com.adva.nlms.frontend.security.dialogs.ExamineServerCertificateDialog;
import jakarta.ws.rs.client.Client;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;

public class ServerServiceImpl implements ServerService {

  private static final Logger log = LogManager.getLogger();
  private final TrustStoreManager trustStoreManager;
  private class ServerServiceCtrl {

    // vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
    // vv REST clients vv (please keep sorted alphabetically for convenience) vv
    AlienDwdmRestClient alienDwdmRestClient;
    BandwidthRestrictionRestClient bandwidthRestrictionRestClient;
    BlocklistRestClient blocklistRestClient;
    CapabilityBrokerRestClient capabilityBrokerRestClient;
    CfmMonitoringRestClient cfmMonitoringRestClient;
    ClientUpdateRestClient clientUpdateRestClient;
    CLIRestClient cliRestClient;
    CPPolicyRestClient cpPolicyRestClient;
    CryptoOfficerRestClient cryptoOfficerRestClient;
    CSVFileTransferRestClient csvFileTransferRestClient;
    CUARestClient cuaRestClient;
    CustomDeviceRestClient customDeviceRestClient;
    DataExportServiceRestClient simpleReportsRestClient;
    DisplaySettingsRestClient displaySettingsRestClient;
    DriverManagerRestClient driverManagerRestClient;
    EthEncryptionSettingsRestClient ethEncryptionSettingsRestClient;
    EthernetRingRestClient ethernetRingRestClient;
    EthNEConfigRestClient ethNEConfigRestClient;
    EventCtrlRestClient eventControl;
    ExportToPlannerRestClient exportToPlannerRestClient;
    FallbackUserNERestClient fallbackUserNERestClient;
    FAMRestClient famRestClient;
    FibermapDiagnosticsRestClient fibermapDiagnosticsRestClient;
    FiberRestClient fiberRestClient;
    FormModelRestClient formModelRestClient;
    FunctionManagerRestClient functionManagerRestClient;
    HealthAnalysisRestClient healthAnalysisRestClient;
    HealthMonitoringRestClient healthMonitoringRestClient;
    IHAControllerRestClient haControllerRestClient;
    InventoryRestClient inventoryRestClient;
    LineDetailsRestClient lineDetailsRestClient;
    LineHdlrRestClient lineHdlrRestClient;
    LinePropertiesEditorRestClient linePropertiesEditorRestClient;
    LinePropertiesVlanRestClient linePropertiesVlanRestClient;
    LinkTrafficRestClient linkTrafficRestClient;
    MapViewConfigurationRestClient mapViewConfigurationRestClient;
    MasterProfileRestClient masterProfileRestClient;
    MLPathDiagnosticsRestClient mlPathDiagnosticsRestClient;
    MonitoringCtrlRestClient monitControl;
    MultiServerConnectorClient multiServerConnector;
    NEBackupRestClient neBackupRestClient;
    NECommCtrlRestClient nECommCtrlRestClient;
    NeProfileRestClient neProfileRestClient;
    NeResourcesRestClient neResourcesRestClient;
    NetworkElementCapabilitiesRestClient networkElementCapabilitiesRestClient;
    NetworkElementOperationRestClient networkElementOperationRestClient;
    NIConfigRestClient niConfigRestClient;
    NIWebSocketRestClient niWebSocketRestClient;
    NTPRestClient ntpRestClient;
    PagingHandlerRestClient pagingHandlerRestClient;
    PCARestClient pcaRestClient;
    PDEvpnRestClient pdEvpnRestClient;
    PDDiscoveredServiceRestClient pdDiscoveredServiceRestClient;
    PDEntitySwapRestClient pdEntitySwapRestClient;
    PDL3RestClient pdl3RestClient;
    PerformanceManagerRestClient performanceManagerRestClient;
    PollingOperationsRestClient pollingOperationsRestClient;
    PrivilegeApprovalRestClient privilegeApprovalRestClient;
    ProfileSnmpRestClient profileSnmpRestClient;
    PropertiesRestClient propertiesRestClient;
    RCAManagerRestClient rcaManagerRestClient;
    ReportRestClient reportCtrlRest;
    RestWebResource restWebResource;
    SatResultRestClient satResultRestClient;
    SecurityCtrlRestClient securityControlRestClient;
    ServerCtrlRestClientIF serverControl;
    ServerMonitoringRestClient serverMonitoringRestClient;
    ServiceAdminStateRestController pdServiceAdminStateController;
    ServiceDetailsRestClient serviceDetailsRestClient;
    ServiceManagerRestClient serviceManagerRestClient;
    ServiceProvisioningEndpointClient serviceProvisioningEndpointClient;
    ShadowServiceManagerRestClient shadowServiceManagerRestClient;
    SMEthernetCryptoRestClient smEthernetCryptoRestClient;
    SNMPRestClient snmpRestClient;
    SNTManagerRestClient sntManagerRestClient;
    SoundRestClient soundRestClient;
    SpanLossRestClient spanLossRestClient;
    SSOElsCtrlRestClient SSOElsCtrlRestClient;
    SubnetHdlrRestClient subnetHdlrRestClient;
    SwUpgradeRestClient swUpgradeRestClient;
    SyncManagerRestClient syncManagerRestClient;
    TagRestClient tagRestClient;
    TCARestClient tcaqCtrlRest;
    TopologyConfigurationRestClient topologyConfigurationRestClient;
    TopologyDiscoveryManagerRestClient tdmRestClient;
    TopologyManagerRestClient topologyManagerRestClient;
    TopologyNodeHdlrRestClient topologyNodeHdlrRestClient;
    TrapForwarderRestClient trapForwarderRestClient;
    UserCtrlRestClient userControlRestClient;
    UserDataRestClient userDataRestClient;
    UserNotificationRestClient userNotificationRestClient;
    UserSessionDataRestClient userSessionDataRestClient;
    VirtualOtnNodeRestClient virtualOtnNodeRestClient;
    WebHelpRestClient webHelpRestClient;
    WebManagerRestClient webManagerRestClient;
    WebTargetMapping webTargetMapping = new WebTargetMapping();
    // ^^ REST clients ^^ (please keep sorted alphabetically for convenience) ^^
    // ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  }

  private ConnectionBean connectionBean;
  private String hostName;
  private ServerServiceCtrl ctrl = new ServerServiceCtrl();

  /**
   * This is an implementation of NetworkService interface.
   */
  public ServerServiceImpl(String certificateStoreDir){
    trustStoreManager = new TrustStoreManager(certificateStoreDir);
  }

  public void reconnect(ConnectionBean connectionBean)
  {
    this.connectionBean = connectionBean;
    this.hostName = connectionBean.getServer();
    // All REST clients will be recreated when switching the server.
    ctrl = new ServerServiceCtrl();
  }

  @Override
  public void acceptServerCertificate() throws CertificateException {
    try {
      final CompositeX509TrustManager.ServerServerTrusted serverServerTrusted = new CompositeX509TrustManager.ServerServerTrusted() {
        @Override
        public void checkServerTrusted(X509Certificate[] certs, String authType) throws CertificateException {
          try {
            trustStoreManager.getPermanentTrustManager().checkServerTrusted(certs, authType);
          } catch (Exception e) {
            try {
              trustStoreManager.getDefaultTrustManager().checkServerTrusted(certs, authType);
            } catch (Exception e1) {
              handleUntrustedCertificate(new CertificateDataDTO(certs[0], null), certs[0]);
            }
          }
        }
      };
      final Client client = new RestClientBuilder().buildClient(RestClientRequestFilter.class, serverServerTrusted);
      final WebResource resource = getRestWebResource().getWebResource();
     client.target(resource.getURI()).path(MDRestPath.HIGH_AVAILABILITY.PATH).path(MDRestPath.HIGH_AVAILABILITY.WORKMODE).request().get();
    } catch (Exception e) {
      if (e.getCause().getCause() instanceof CertificateException) {
        throw (CertificateException) e.getCause().getCause();
      }
      throw e;
    }
  }

  public ConnectionBean getConnectionBean() {
    return connectionBean;
  }

  private void handleUntrustedCertificate(CertificateDataDTO certificateData, X509Certificate x509Certificate) throws CertificateException {

    {
      ExamineServerCertificateDialog.ActionToTake actionToTake = ExamineServerCertificateDialog.showDialog(certificateData, hostName);
      if (actionToTake == null)
        throw new CertificateException(SSLHandler.CERTIFICATE_NOT_ACCEPTED_MSG);

      switch (actionToTake) {
        case ACCEPT:
          trustStoreManager.addToTrustStore(x509Certificate);
          break;
        case ACCEPT_TEMPORARY:
          trustStoreManager.addToTemporaryTrustStore(x509Certificate);
          break;

        default:
          throw new CertificateException(SSLHandler.CERTIFICATE_NOT_ACCEPTED_MSG);
      }
//    } else {
//      addMessageToAcceptCertificate(certificateData, x509Certificate);
//    }
      //throw new CertificateException(SSLHandler.CERTIFICATE_NOT_ACCEPTED_MSG);

    }
  }


  @Override
  public WebTargetMapping getWebTargetMapping() {
    return ctrl.webTargetMapping;
  }
  /**
   * @return the RESTWebResource for REST API
   */
  @Override
  public RestWebResource getRestWebResource(){
    if(ctrl.restWebResource == null)
      ctrl.restWebResource = new RestWebResource(hostName, connectionBean.getPort(), connectionBean.isSecureMode());
    return ctrl.restWebResource;
  }

  /**
   * Returns the server event controll which is responsable
   * for all operations with events.
   *
   * @return Control object.
   */
  @Override
  public EventCtrlRestClient getEventControl() {
    if (ctrl.eventControl == null)
      ctrl.eventControl = new EventCtrlRestClientImpl(getRestWebResource());
    return ctrl.eventControl;
  }

  /**
   * Returns the server control which is responsible
   * for all operations with server.
   *
   * @return Control object.
   */
  @Override
  public ServerCtrlRestClientIF getServerControl() {
    if (ctrl.serverControl == null)
      ctrl.serverControl = new ServerCtrlRestClient(getRestWebResource());
    return ctrl.serverControl;
  }

  @Override
  public MultiServerConnectorClient getMultiServerConnector() {
    if (ctrl.multiServerConnector == null)
    {
      //REST API for multi server connect has been introduced by NMS 8.4.1, Corba is no longer supported (>=9.5)
      ctrl.multiServerConnector = new MultiServerConnectorClientImpl(getRestWebResource());
      try
      {
        ctrl.multiServerConnector.getVersionInfo();
      }
      catch (OperationFailedException e)
      {
        String message = new StringBuilder().append("Server ")
                .append(hostName!=null?hostName:"")
                .append(" that you want to connect is in not compatible version. Please check Multi-server Management configuration").toString();
        log.error(message,e);
        DialogFactory.getInstance().showErrorMessage(message);
        //TODO-TC: ctrl.multiServerConnector = new MultiServerConnectorCorbaClientImpl(MultiServerConnectorHelper.narrow(orb.string_to_object(corbalocStr + "/MultiServerConnector")), this);
      }
    }
    return ctrl.multiServerConnector;
  }


  @Override
  public SecurityCtrlRestClient getSecurityControlRestClient()
  {
    if (ctrl.securityControlRestClient == null) {
      ctrl.securityControlRestClient = new SecurityCtrlRestClient(getRestWebResource());
    }
    return ctrl.securityControlRestClient;
  }

  @Override
  public SSOElsCtrlRestClient getElsCtrlRestClient()
  {
    if (ctrl.SSOElsCtrlRestClient == null) {
      ctrl.SSOElsCtrlRestClient = new SSOElsCtrlRestClient(getRestWebResource());
    }
    return ctrl.SSOElsCtrlRestClient;
  }

  @Override
  public UserCtrlRestClient getUserControlRestClient()
  {
    if (ctrl.userControlRestClient == null) {
      ctrl.userControlRestClient = new UserCtrlRestClient(getRestWebResource());
    }
    return ctrl.userControlRestClient;
  }


  @Override
  public ReportRestClient getReportCtrlRest() {
    if (ctrl.reportCtrlRest == null)
      ctrl.reportCtrlRest = new ReportRestClientImpl(getRestWebResource());       //new ReportCtrlRestImpl(getWebResource());
    return ctrl.reportCtrlRest;
  }

  @Override
  public TagRestClient getTagRestClient() {
    if (ctrl.tagRestClient == null)
      ctrl.tagRestClient = new TagRestClientImpl(getRestWebResource());
    return ctrl.tagRestClient;
  }

  @Override
  public TCARestClient getTCACtrlRest() {
    if (ctrl.tcaqCtrlRest == null)
      ctrl.tcaqCtrlRest = new TCACtrlRestClientImpl(getRestWebResource());
    return ctrl.tcaqCtrlRest;
  }

  @Override
  public ClientUpdateRestClient getClientUpdateRestClient() {
    if(ctrl.clientUpdateRestClient == null)
      ctrl.clientUpdateRestClient = new ClientUpdateRestClientImpl(getRestWebResource()) ;
    return ctrl.clientUpdateRestClient;
  }

  @Override
  public MapViewConfigurationRestClient getMapViewConfigurationRestClient() {
    if (ctrl.mapViewConfigurationRestClient == null)
      ctrl.mapViewConfigurationRestClient = new MapViewConfigurationRestClient(getRestWebResource());
    return ctrl.mapViewConfigurationRestClient;
  }
  @Override
  public TopologyConfigurationRestClient getTopologyConfigurationRestClient() {
    if (ctrl.topologyConfigurationRestClient == null)
      ctrl.topologyConfigurationRestClient = new TopologyConfigurationRestClient(getRestWebResource());
    return ctrl.topologyConfigurationRestClient;
  }

  /**
   * Returns the server monitoring control which is responsible
   * for all monitoring on demand request and related actions
   *
   * @return Control object.
   */
  @Override
  public MonitoringCtrlRestClient getMonitoringControl() {
    if (ctrl.monitControl == null)
      ctrl.monitControl = new MonitoringCtrlRestClient(getRestWebResource());
    return ctrl.monitControl;
  }

  /**
   * Returns the server security controll which is responsable
   * for all operations with service manager
   *
   * @return Control object.
   */
  @Override
  public ServiceManagerRestClient getServiceManagerRestClient() {
    if (ctrl.serviceManagerRestClient == null)
      ctrl.serviceManagerRestClient = new ServiceManagerRestClientImpl(getRestWebResource());
    return ctrl.serviceManagerRestClient;
  }

  @Override
  public ShadowServiceManagerRestClient getShadowServiceManagerRestClient() {
    if (ctrl.shadowServiceManagerRestClient == null)
      ctrl.shadowServiceManagerRestClient = new ShadowServiceManagerRestClientImpl(getRestWebResource());
    return ctrl.shadowServiceManagerRestClient;
  }

  @Override
  public ServiceAdminStateRestController getPDServiceManagerRestClient() {
    if (ctrl.pdServiceAdminStateController == null)
      ctrl.pdServiceAdminStateController = new PDServiceAdminStateRestControllerImpl(getRestWebResource());
    return ctrl.pdServiceAdminStateController;
  }

  @Override
  public ServiceDetailsRestClient getServiceDetailsRestClient() {
    if (ctrl.serviceDetailsRestClient == null)
      ctrl.serviceDetailsRestClient = new ServiceDetailsRestClientImpl(getRestWebResource());
    return ctrl.serviceDetailsRestClient;
  }

  @Override
  public CapabilityBrokerRestClient getCapabilityBrokerRestClient() {
    if (ctrl.capabilityBrokerRestClient == null)
      ctrl.capabilityBrokerRestClient = new CapabilityBrokerRestClientImpl(getRestWebResource());
    return ctrl.capabilityBrokerRestClient;
  }
  @Override
  public PagingHandlerRestClient getPagingHandlerService() {
    if (ctrl.pagingHandlerRestClient == null)
      ctrl.pagingHandlerRestClient = new PagingHandlerServiceImpl(getRestWebResource());
    return ctrl.pagingHandlerRestClient;
  }

  /**
   * Returns the NE Backup interface which is responsible for all operations with NE Backup
   *
   * @return Control object.
   */
  @Override
  public NEBackupRestClient getNEBackupRestClient() {
    if (ctrl.neBackupRestClient == null) {
      ctrl.neBackupRestClient = new NEBackupRestClient(getRestWebResource());
    }
    return ctrl.neBackupRestClient;
  }

  @Override
  public SubnetHdlrRestClient getSubnetHdlrRestClient() {
    if(ctrl.subnetHdlrRestClient == null){
      ctrl.subnetHdlrRestClient = new SubnetHdlrRestClient(getRestWebResource());
    }
    return ctrl.subnetHdlrRestClient;
  }

  public TopologyManagerRestClient getTopologyManagerRestClient() {
    if(ctrl.topologyManagerRestClient == null){
      ctrl.topologyManagerRestClient = new TopologyManagerRestClient(getRestWebResource());
    }
    return ctrl.topologyManagerRestClient;
  }

  @Override
  public WebManagerRestClient getWebManagerRestClient() {
    if (ctrl.webManagerRestClient == null) {
      ctrl.webManagerRestClient = new WebManagerRestClientImpl(getRestWebResource());
    }
    return ctrl.webManagerRestClient;
  }

  @Override
  public WebHelpRestClient getWebHelpRestClient() {
    if (ctrl.webHelpRestClient == null) {
      ctrl.webHelpRestClient = new WebHelpRestClient(getRestWebResource());
    }
    return ctrl.webHelpRestClient;
  }

  @Override
  public DisplaySettingsRestClient getDisplaySettingsRestClient() {
    if (ctrl.displaySettingsRestClient == null) {
      ctrl.displaySettingsRestClient = new DisplaySettingsRestClient(getRestWebResource());
    }
    return ctrl.displaySettingsRestClient;
  }

  @Override
  public CustomDeviceRestClient getCustomDeviceRestClient () {
    if(ctrl.customDeviceRestClient == null) {
      ctrl.customDeviceRestClient = new CustomDeviceRestClient(getRestWebResource());
    }
    return ctrl.customDeviceRestClient;
  }

  @Override
  public NeResourcesRestClient getNeResourcesRestClient() {
    if(ctrl.neResourcesRestClient == null){
      ctrl.neResourcesRestClient = new NeResourcesRestClient(getRestWebResource());
    }
    return ctrl.neResourcesRestClient;
  }

  @Override
  public PerformanceManagerRestClient getPerformanceManagerRestClient() {
    if(ctrl.performanceManagerRestClient == null){
      ctrl.performanceManagerRestClient = new PerformanceManagerRestClient(getRestWebResource());
    }
    return ctrl.performanceManagerRestClient;
  }

  @Override
  public SpanLossRestClient getSpanLossRestClient() {
    if(ctrl.spanLossRestClient == null){
      ctrl.spanLossRestClient = new SpanLossRestClient(getRestWebResource());
    }
    return ctrl.spanLossRestClient;
  }

  @Override
  public EthNEConfigRestClient getEthNEConfigRestClient() {
    if (ctrl.ethNEConfigRestClient == null)
      ctrl.ethNEConfigRestClient = new EthNEConfigRestClient(getRestWebResource());
    return ctrl.ethNEConfigRestClient;
  }

  public String getHostName() {
    return hostName;
  }

  @Override
  public NetworkElementOperationRestClient getNetworkElementOperationRestClient() {
    if (ctrl.networkElementOperationRestClient == null) {
      ctrl.networkElementOperationRestClient = new NetworkElementOperationRestClient(getRestWebResource());
      ctrl.webTargetMapping.add(ctrl.networkElementOperationRestClient.getSubPath(), ctrl.networkElementOperationRestClient);
    }
    return ctrl.networkElementOperationRestClient;
  }

  @Override
  public FormModelRestClient getFormModelDataProvider() {
    if (ctrl.formModelRestClient == null) {
      ctrl.formModelRestClient = new FormModelRestClient(getRestWebResource());
      ctrl.webTargetMapping.add(ctrl.formModelRestClient.getSubPath(), ctrl.formModelRestClient);
    }
    return ctrl.formModelRestClient;
  }

  @Override
  public FibermapDiagnosticsRestClient getFibermapDiagnosticsRestClient() {
    if (ctrl.fibermapDiagnosticsRestClient == null)
      ctrl.fibermapDiagnosticsRestClient = new FibermapDiagnosticsRestClient(getRestWebResource());
    return ctrl.fibermapDiagnosticsRestClient;
  }

  @Override
  public FiberRestClient getFiberRestClient() {
    if(ctrl.fiberRestClient == null){
      ctrl.fiberRestClient = new FiberRestClient(getRestWebResource());
    }
    return ctrl.fiberRestClient;
  }

  @Override
  public AlienDwdmRestClient getAlienDwdmRestClient() {
    if (ctrl.alienDwdmRestClient == null)
      ctrl.alienDwdmRestClient = new AlienDwdmRestClient(getRestWebResource());
    return ctrl.alienDwdmRestClient;
  }

  /**
   * Returns the snmpRestClient control which is responsible
   * for operations with snmp
   *
   * @return Control object.
   */
  @Override
  public SNMPRestClient getSNMPRestClient() {
    if (ctrl.snmpRestClient == null)
      ctrl.snmpRestClient = new SNMPRestClientImpl(getRestWebResource());
    return ctrl.snmpRestClient;
  }

  @Override
  public CLIRestClient getCLIRestClient() {
    if (ctrl.cliRestClient == null)
      ctrl.cliRestClient = new CLIRestClientImpl(getRestWebResource());
    return ctrl.cliRestClient;
  }

  @Override
  public TrapForwarderRestClient getTrapForwarderRestClient() {
    if (ctrl.trapForwarderRestClient == null)
      ctrl.trapForwarderRestClient = new TrapForwarderRestClientImpl(getRestWebResource());
    return ctrl.trapForwarderRestClient;
  }

  @Override
  public InventoryRestClient getInventoryRestClient() {
    if (ctrl.inventoryRestClient == null)
      ctrl.inventoryRestClient = new InventoryRestClient(getRestWebResource());
    return ctrl.inventoryRestClient;
  }

  @Override
  public LinePropertiesEditorRestClient getLinePropertiesEditorRestClient() {
    if (ctrl.linePropertiesEditorRestClient == null)
      ctrl.linePropertiesEditorRestClient = new LinePropertiesEditorRestClient(getRestWebResource());
    return ctrl.linePropertiesEditorRestClient;
  }

  @Override
  public LinePropertiesVlanRestClient getLinePropertiesVlanRestClient() {
    if (ctrl.linePropertiesVlanRestClient == null) {
      ctrl.linePropertiesVlanRestClient = new LinePropertiesVlanRestClient(getRestWebResource());
    }
    return ctrl.linePropertiesVlanRestClient;
  }

  @Override
  public LineDetailsRestClient getLineDetailsRestClient() {
    if (ctrl.lineDetailsRestClient == null) {
      ctrl.lineDetailsRestClient = new LineDetailsRestClient(getRestWebResource());
    }
    return ctrl.lineDetailsRestClient;
  }

  @Override
  public SMEthernetCryptoRestClient getSmEthernetCryptoRestClient() {
    if (ctrl.smEthernetCryptoRestClient == null)
      ctrl.smEthernetCryptoRestClient = new SMEthernetCryptoRestClient(getRestWebResource());
    return ctrl.smEthernetCryptoRestClient;
  }

  @Override
  public DriverManagerRestClient getDriverManagerRestClient() {
    if (ctrl.driverManagerRestClient == null)
      ctrl.driverManagerRestClient = new DriverManagerRestClient(getRestWebResource());
    return ctrl.driverManagerRestClient;
  }

  @Override
  public NetworkElementCapabilitiesRestClient getNetworkElementCapabilitiesRestClient(){
    if (ctrl.networkElementCapabilitiesRestClient == null)
      ctrl.networkElementCapabilitiesRestClient = new NetworkElementCapabilitiesRestClient(getRestWebResource());
    return ctrl.networkElementCapabilitiesRestClient;
  }

  @Override
  public LineHdlrRestClient getLineHldLineHdlrRestClient() {
    if (ctrl.lineHdlrRestClient == null)
      ctrl.lineHdlrRestClient = new LineHdlrRestClientImpl(getRestWebResource());
    return ctrl.lineHdlrRestClient;
  }

  @Override
  public CSVFileTransferRestClient getCsvFileTransferRestClient() {
    if (ctrl.csvFileTransferRestClient == null)
      ctrl.csvFileTransferRestClient = new CSVFileTransferRestClient(getRestWebResource());
    return ctrl.csvFileTransferRestClient;
  }

  public <T extends DataAccessDto> DataAccessRESTClient<T> getDataAccessClient(String servicePath, Class<T> daDtoClass) {
    return new DataAccessRESTClient(getRestWebResource(), servicePath, daDtoClass);
  }

  @Override
  public FunctionManagerRestClient getFunctionManagerRestClient() {
    if (ctrl.functionManagerRestClient == null)
      ctrl.functionManagerRestClient = new FunctionManagerRestClient(getRestWebResource());
    return ctrl.functionManagerRestClient;
  }

  @Override
  public SwUpgradeRestClient getSwUpgradeRestClient() {
    if (ctrl.swUpgradeRestClient == null)
      ctrl.swUpgradeRestClient = new SwUpgradeRestClient(getRestWebResource());
    return ctrl.swUpgradeRestClient;
  }

  @Override
  public FAMRestClient getFAMRestClient() {
    if (ctrl.famRestClient == null)
      ctrl.famRestClient = new FAMRestClient(getRestWebResource());
    return ctrl.famRestClient;
  }

  @Override
  public NTPRestClient getNTPRestClient() {
    if (ctrl.ntpRestClient == null)
      ctrl.ntpRestClient = new NTPRestClient(getRestWebResource());
    return ctrl.ntpRestClient;
  }

  @Override
  public PollingOperationsRestClient getPollingOperationsRestClient() {
    if (ctrl.pollingOperationsRestClient == null)
      ctrl.pollingOperationsRestClient = new PollingOperationsRestClient(getRestWebResource());
    return ctrl.pollingOperationsRestClient;
  }

  @Override
  public DataExportServiceRestClient getSimpleReportsRestClient() {
    if (ctrl.simpleReportsRestClient == null) {
      ctrl.simpleReportsRestClient = new DataExportServiceRestClient(getRestWebResource());
    }
    return ctrl.simpleReportsRestClient;
  }

  @Override
  public SyncManagerRestClient getSyncManagerRestClient() {
    if (ctrl.syncManagerRestClient == null)
      ctrl.syncManagerRestClient = new SyncManagerRestClientImpl(getRestWebResource());
    return ctrl.syncManagerRestClient;
  }

  @Override
  public SNTManagerRestClient getSNTManagerRestClient() {
    if (ctrl.sntManagerRestClient == null)
      ctrl.sntManagerRestClient = new SNTManagerRestClientImpl(getRestWebResource());
    return ctrl.sntManagerRestClient;
  }

  @Override
  public RCAManagerRestClient getRCAManagerRestClient() {
    if (ctrl.rcaManagerRestClient == null)
      ctrl.rcaManagerRestClient = new RCAManagerRestClientImpl(getRestWebResource());
    return ctrl.rcaManagerRestClient;
  }

  @Override
  public BlocklistRestClient getBlocklistRestClient() {
    if (ctrl.blocklistRestClient == null) {
      ctrl.blocklistRestClient = new BlocklistRestClient(getRestWebResource());
    }
    return ctrl.blocklistRestClient;
  }

  @Override
  public LinkTrafficRestClient getLinkTrafficRestClient() {
    if (ctrl.linkTrafficRestClient == null)
      ctrl.linkTrafficRestClient = new LinkTrafficRestClientImpl(getRestWebResource());
    return ctrl.linkTrafficRestClient;
  }

  @Override
  public PCARestClient getPCARestClient () {
    if(ctrl.pcaRestClient == null) {
      ctrl.pcaRestClient = new PCARestClient(getRestWebResource());
    }
    return ctrl.pcaRestClient;
  }

  @Override
  public CUARestClient getCUARestClient() {
    if(ctrl.cuaRestClient == null) {
      ctrl.cuaRestClient = new CUARestClient(getRestWebResource());
    }
    return ctrl.cuaRestClient;
  }

  @Override
  public PrivilegeApprovalRestClient getPrivilegeApprovalRestClient() {
    if (ctrl.privilegeApprovalRestClient == null)
      ctrl.privilegeApprovalRestClient = new PrivilegeApprovalRestClient(getRestWebResource());
    return ctrl.privilegeApprovalRestClient;
  }

  @Override
  public FallbackUserNERestClient getFallbackUserNERestClient() {
    if (ctrl.fallbackUserNERestClient == null) {
      ctrl.fallbackUserNERestClient = new FallbackUserNERestClient(getRestWebResource());
    }
    return ctrl.fallbackUserNERestClient;
  }

  @Override
  public boolean isFallbackUserNERestClientInstantiated() {
    return (ctrl.fallbackUserNERestClient != null);
  }

  @Override
  public NECommCtrlRestClient getNECommCtrlRestClient() {
    if(ctrl.nECommCtrlRestClient == null) {
      ctrl.nECommCtrlRestClient = new NECommCtrlRestClientImpl(getRestWebResource());
    }
    return ctrl.nECommCtrlRestClient;
  }

  @Override
  public TopologyDiscoveryManagerRestClient getTopologyDiscoveryManagerRestClient() {
    if (ctrl.tdmRestClient == null) {
      ctrl.tdmRestClient = new TopologyDiscoveryManagerRestClient(getRestWebResource());
    }
    return ctrl.tdmRestClient;
  }

  @Override
  public TopologyNodeHdlrRestClient getTopologyNodeHdlrRestClient() {
    if (ctrl.topologyNodeHdlrRestClient == null) {
      ctrl.topologyNodeHdlrRestClient = new TopologyNodeHdlrRestClient(getRestWebResource());
    }
    return ctrl.topologyNodeHdlrRestClient;
  }

  @Override
  public CPPolicyRestClient getCPPolicyRestClient() {
    if (ctrl.cpPolicyRestClient == null)
      ctrl.cpPolicyRestClient = new CPPolicyRestClientImpl(getRestWebResource());
    return ctrl.cpPolicyRestClient;
  }

  @Override
  public SoundRestClient getSoundRestClient() {
    if (ctrl.soundRestClient == null)
      ctrl.soundRestClient = new SoundRestClientImpl(getRestWebResource());
    return ctrl.soundRestClient;
  }

  @Override
  public UserNotificationRestClient getUserNotificationRestClient() {
    if (ctrl.userNotificationRestClient == null)
      ctrl.userNotificationRestClient = new UserNotificationRestClient(getRestWebResource());
    return ctrl.userNotificationRestClient;
  }

  @Override
  public IHAControllerRestClient getHAControllerRestClient() {
    if (ctrl.haControllerRestClient == null) {
      ctrl.haControllerRestClient = new HAControllerRestClient(getRestWebResource());
    }
    return ctrl.haControllerRestClient;
  }

  @Override
  public NIConfigRestClient getNiControllerRestClient() {
    if (ctrl.niConfigRestClient == null) {
      ctrl.niConfigRestClient = new NIConfigRestClient(getRestWebResource());
    }
    return ctrl.niConfigRestClient;
  }

  @Override
  public NIWebSocketRestClient getNiWebSocketRestClient() {
    if (ctrl.niWebSocketRestClient == null) {
      ctrl.niWebSocketRestClient = new NIWebSocketRestClient(getRestWebResource());
    }
    return ctrl.niWebSocketRestClient;
  }

  @Override
  public CryptoOfficerRestClient getCryptoOfficerRestClient() {
    if (ctrl.cryptoOfficerRestClient == null) {
      ctrl.cryptoOfficerRestClient = new CryptoOfficerRestClientImpl(getRestWebResource());
    }
    return ctrl.cryptoOfficerRestClient;
  }

  @Override
  public UserDataRestClient getUserDataRestClient() {
    if (ctrl.userDataRestClient == null) {
      ctrl.userDataRestClient = new UserDataRestClient(getRestWebResource());
    }
    return ctrl.userDataRestClient;
  }

  @Override
  public UserSessionDataRestClient getUserSessionDataRestClient() {
    if (ctrl.userSessionDataRestClient == null) {
      ctrl.userSessionDataRestClient = new UserSessionDataRestClient(getRestWebResource());
    }
    return ctrl.userSessionDataRestClient;
  }

  @Override
  public EthEncryptionSettingsRestClient getEthEncryptionSettingsRestClient() {
    if (ctrl.ethEncryptionSettingsRestClient == null) {
      ctrl.ethEncryptionSettingsRestClient = new EthEncryptionSettingsRestClient(getRestWebResource());
    }
    return ctrl.ethEncryptionSettingsRestClient;
  }

  @Override
  public NeProfileRestClient getNeProfileRestClient() {
    if (ctrl.neProfileRestClient == null)
      ctrl.neProfileRestClient = new NeProfileRestClient(getRestWebResource());
    return ctrl.neProfileRestClient;
  }

  @Override
  public MasterProfileRestClient getMasterProfileRestClient() {
    if (ctrl.masterProfileRestClient == null) {
      ctrl.masterProfileRestClient = new MasterProfileRestClient(getRestWebResource());
    }
    return ctrl.masterProfileRestClient;
  }

  @Override
  public ServerMonitoringRestClient getServerMonitoringRestClient() {
    if (ctrl.serverMonitoringRestClient == null) {
      ctrl.serverMonitoringRestClient = new ServerMonitoringRestClient(getRestWebResource());
    }
    return ctrl.serverMonitoringRestClient;
  }

  @Override
  public PropertiesRestClient getPropertiesRestClient() {
    if(ctrl.propertiesRestClient == null) {
      ctrl.propertiesRestClient = new PropertiesRestClient(getRestWebResource());
    }
    return ctrl.propertiesRestClient;
  }

  @Override
  public MLPathDiagnosticsRestClient getPathDiagnosticsRestClient() {
    if (ctrl.mlPathDiagnosticsRestClient == null) {
      ctrl.mlPathDiagnosticsRestClient = new MLPathDiagnosticsRestClient(getRestWebResource());
    }
    return (ctrl.mlPathDiagnosticsRestClient);
  }

  @Override
  public ServiceProvisioningEndpointClient getServiceProvisioningEndpointClient() {
    if(ctrl.serviceProvisioningEndpointClient == null)
      ctrl.serviceProvisioningEndpointClient = new ServiceProvisioningEndpointClient(getRestWebResource());

    return ctrl.serviceProvisioningEndpointClient;
  }

  @Override
  public CfmMonitoringRestClient getCfmMonitoringRestClient() {
    if(ctrl.cfmMonitoringRestClient == null) {
      ctrl.cfmMonitoringRestClient = new CfmMonitoringRestClientImpl(getRestWebResource()) ;
    }
    return ctrl.cfmMonitoringRestClient;
  }

  @Override
  public SatResultRestClient getSatResultsRestClient() {
    if (ctrl.satResultRestClient == null) {
      ctrl.satResultRestClient = new SatResultRestClient(getRestWebResource());
    }
    return ctrl.satResultRestClient;
  }

  @Override
  public VirtualOtnNodeRestClient getVirtualOtnNodeRestClient() {
    if (ctrl.virtualOtnNodeRestClient == null) {
      ctrl.virtualOtnNodeRestClient = new VirtualOtnNodeRestClient(getRestWebResource());
    }
    return ctrl.virtualOtnNodeRestClient;
  }

  @Override
  public ProfileSnmpRestClient getProfileSnmpRestClient() {
    if (ctrl.profileSnmpRestClient == null) {
      ctrl.profileSnmpRestClient = new ProfileSnmpRestClientImpl(getRestWebResource());
    }
    return ctrl.profileSnmpRestClient;
  }

  @Override
  public HealthMonitoringRestClient getHealthMonitoringRestClient() {
    if (ctrl.healthMonitoringRestClient == null) {
      ctrl.healthMonitoringRestClient = new HealthMonitoringRestClient(getRestWebResource());
    }
    return ctrl.healthMonitoringRestClient;
  }

  @Override
  public HealthAnalysisRestClient getHealthAnalysisRestClient() {
    if (ctrl.healthAnalysisRestClient == null) {
      ctrl.healthAnalysisRestClient = new HealthAnalysisRestClient(getRestWebResource());
    }
    return ctrl.healthAnalysisRestClient;
  }

  @Override
  public EthernetRingRestClient getEthernetRingRestClient() {
    if (ctrl.ethernetRingRestClient == null) {
      ctrl.ethernetRingRestClient = new EthernetRingRestClientImpl(getRestWebResource());
    }
    return ctrl.ethernetRingRestClient;
  }

  @Override
  public BandwidthRestrictionRestClient getBandwidthRestrictionRestClient(){
    if(ctrl.bandwidthRestrictionRestClient == null) {
      ctrl.bandwidthRestrictionRestClient = new BandwidthRestrictionRestClientImpl(getRestWebResource());
    }
    return ctrl.bandwidthRestrictionRestClient;
  }

  @Override
  public PDEvpnRestClient getEvpnRestClient() {
    if (ctrl.pdEvpnRestClient == null) {
      ctrl.pdEvpnRestClient = new PDEvpnRestClient(getRestWebResource());
    }
    return ctrl.pdEvpnRestClient;
  }

  @Override
  public PDDiscoveredServiceRestClient getPDDiscoveredServiceRestClient() {
    if (ctrl.pdDiscoveredServiceRestClient == null) {
      ctrl.pdDiscoveredServiceRestClient = new PDDiscoveredServiceRestClient(getRestWebResource());
    }
    return ctrl.pdDiscoveredServiceRestClient;
  }

  @Override
  public PDEntitySwapRestClient getPDEntitySwapRestClient() {
    if (ctrl.pdEntitySwapRestClient == null) {
      ctrl.pdEntitySwapRestClient = new PDEntitySwapRestClient(getRestWebResource());
    }
    return ctrl.pdEntitySwapRestClient;
  }

  @Override
  public PDL3RestClient getPDL3RestClient() {
    if (ctrl.pdl3RestClient == null) {
      ctrl.pdl3RestClient = new PDL3RestClient(getRestWebResource());
    }
    return ctrl.pdl3RestClient;
  }

  @Override
  public ExportToPlannerRestClient getExportToPlannerClient() {
    if (ctrl.exportToPlannerRestClient == null) {
      ctrl.exportToPlannerRestClient = new ExportToPlannerRestClientImpl(getRestWebResource());
    }
    return ctrl.exportToPlannerRestClient;
  }

}

