/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 */

package com.adva.nlms.tfwsm.serviceview;

import com.adva.common.model.DataTransferObjectHelper;
import com.adva.common.model.form.FormElement;
import com.adva.common.model.navigation.DisplayContext;
import com.adva.common.util.collect.Lists;
import com.adva.nlms.common.sm.Item;
import com.adva.nlms.common.sm.model.TrailGroupDTO;
import com.adva.nlms.frontend.gui.tree.NodeTreeItem;
import com.adva.nlms.mediation.paging.PagingRestClientTest;
import com.adva.common.workbench.binding.model.BindingColumn;
import com.adva.nlms.common.TopologyNodeType;
import com.adva.nlms.common.networktree.TopologyDTO;
import com.adva.nlms.common.sm.model.ServiceDTO;
import com.adva.nlms.common.paging.PageArea;
import com.adva.nlms.common.paging.PagingRestriction;
import com.adva.nlms.common.sm.ServiceView;
import com.adva.nlms.common.sm.model.CustomerGroupDTO;
import com.adva.nlms.frontend.common.OperationFailedException;
import com.adva.nlms.tfwsm.GenerateErrorHealthReport;
import com.adva.nlms.tfwsm.common.pagearea.PageAreaView;
import com.adva.nlms.tfwsm.common.tcreport.TcReportHdlr;
import com.adva.nlms.tfwsm.dboperations.DBConsistency;
import com.adva.nlms.tfwsm.resttonms.InitiateIDL;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.File;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ServiceViewHdlr {

    InitiateIDL nmsInterface;
    public boolean isOldMlVerificationToo = true;
    public boolean isOldModelService = false;
    public boolean isNewModelService = false;
    public boolean doNotPublishToTC = false;
    public String tcTestName;
    public TopologyDTO topologyDTO;
    public TopologyDTO requestedTreeNode;
    public boolean isServiceViewMock = false;
    List<String> leftoverIdsTableAfterPreviousSteps = new ArrayList<>();

    public boolean onlyCustomerIds = false;

    static final String SERVICE_FOUND = "Service found: ";
    static final String SERVICE_NOT_FOUND = "Not sure there is a requested name: ";

    private static Logger mainLog = LogManager.getLogger("TFWProcess");

    public void setNodeType(TopologyNodeType nodeType) {
        this.nodeType = nodeType;
    }

    public void setIncludeConnectionsNode(boolean includeConnectionsNode) {
        this.includeConnectionsNode = includeConnectionsNode;
    }

    public boolean includeConnectionsNode = false;
    private TopologyNodeType nodeType = TopologyNodeType.CUSTOMER;

    public ServiceViewHdlr(InitiateIDL nmsInterface) {
        this.nmsInterface = nmsInterface;
    }

    public List<ServiceView> getServices() {
        List<ServiceView> list = new LinkedList<>();
        try {
            List<Integer> ids = new ArrayList<>();
            CustomerGroupDTO treeSubnetCustomer = (CustomerGroupDTO) nmsInterface.getTopologyHdlr().getTreeModel(TopologyNodeType.CUSTOMER);
            TopologyDTO serviceTreeGroup = null;
            if (!includeConnectionsNode) {
                serviceTreeGroup = treeSubnetCustomer.getTreeCustomerGroup()[0]; //[0] means only "services" group
            } else {
                serviceTreeGroup = treeSubnetCustomer.getTreeTrailGroup()[0];
            }
            ids.add(serviceTreeGroup.getId());
            getAllChildresnIds(serviceTreeGroup, ids, true);
            for (ServiceView service : getServiceViewsForServiceTree(ids, serviceTreeGroup.getTopologyNodeType())) {
                list.add(service);
            }
        } catch (NullPointerException | OperationFailedException e) {
            mainLog.error(e);
        }
        return list;
    }

    /**
     * Gets service for given name on specified network element id
     *
     * @param serviceName
     * @param neId
     * @return
     */
    public ServiceView getServiceView(String serviceName, int neId) {
        try {
            mainLog.info("Searching for given service name: {}", serviceName);
            for (ServiceView service : getServiceViews(neId)) {
                mainLog.info("Searching service: {}", service.getResourceName());
                if (service.getResourceName().equals(serviceName)) {
                    mainLog.info("{}{}", SERVICE_FOUND, service.getResourceName());
                    return service;
                }
            }
            for (ServiceView service : getServiceViews(neId)) {
                if (service.getResourceName().contains(serviceName)) {
                    mainLog.info("Found probably the service {}", service.getResourceName());
                    return service;
                }
            }
        } catch (NullPointerException e) {
            mainLog.error("{}{} ?!", SERVICE_NOT_FOUND, serviceName);
        }
        mainLog.info("{}{}", SERVICE_FOUND, serviceName);
        return null;
    }

    /**
     * Gets service for given name on specified network element id
     *
     * @param serviceName
     * @return
     */
    public ServiceView getServiceView(String serviceName) {
        mainLog.info("Searching instance for service: {}", serviceName);
        try {
            CustomerGroupDTO treeSubnetCustomer;
            TopologyDTO serviceTreeGroup;
            List<Integer> ids = new ArrayList<>();
            treeSubnetCustomer = (CustomerGroupDTO) nmsInterface.getTopologyHdlr().getTreeModel(nodeType);
            if (!includeConnectionsNode) {
                serviceTreeGroup = treeSubnetCustomer.getTreeCustomerGroup()[0]; //[0] means only "services" group
            } else {
                serviceTreeGroup = treeSubnetCustomer.getTreeTrailGroup()[0];
            }

            ids.add(serviceTreeGroup.getId());
            getAllChildresnIds(serviceTreeGroup, ids, true); //[0] means only "services" group
            for (ServiceView service : getServiceViewsForServiceTree(ids, serviceTreeGroup.getTopologyNodeType())) {
                if (service.getResourceName().equals(serviceName) || service.getServiceName().equals(serviceName)) {
                    mainLog.info("{}{}", SERVICE_FOUND, service.getResourceName());
                    return service;
                }
            }
            for (ServiceView service : getServiceViewsForServiceTree(ids, serviceTreeGroup.getTopologyNodeType())) {
                if (service.getResourceName().contains(serviceName) || service.getServiceName().contains(serviceName)) {
                    mainLog.info("{}{}", SERVICE_FOUND, service.getResourceName());
                    return service;
                }
            }
        } catch (NullPointerException e) {
            mainLog.error("Not sure there is a requested name: {}?!", serviceName);
        } catch (OperationFailedException e) {
            mainLog.error(e);
        }
        mainLog.error("There is no any service for given name: {}", serviceName);
        return null;
    }

    /**
     * Gets service for given entTP set or service name - searching it for all service tree, but first only for new model services as a priority
     *
     * @param serviceNameOrEndTP
     * @return
     */
    public ServiceView getServiceViewByTPsOrName(String serviceNameOrEndTP) {
        isNewModelService = false;
        isOldModelService = false;
        ServiceView service = null;
        ServiceView classicService = null;
        mainLog.info("Searching for new model service: {}", serviceNameOrEndTP);
        service = getServiceViewByTPsOrName(serviceNameOrEndTP, true);

        if (service != null) {
            isNewModelService = true;
        }
        if (service == null && isOldMlVerificationToo) { //ls temporary solution to verify if service was not discovered in new and classic model in the same time
                mainLog.info("Searching for old model service: {}", serviceNameOrEndTP);
                classicService = getServiceViewByTPsOrName(serviceNameOrEndTP, false);
                if (classicService != null) {
                    mainLog.info("Found service {} in old model", classicService.getResourceName());
                    isOldModelService = true;
                    String tcName = serviceNameOrEndTP;
                    if (this.tcTestName != null) tcName = this.tcTestName;
                    if (!doNotPublishToTC) {
                        TcReportHdlr.publishTestStatus("SEARCHING_FOR_ML_SERVICE", tcName, false, "Service was not found in new model");
                       }
                }
        }
        if (service==null && isOldModelService) return classicService;
        return service;
    }

    /**
     * Gets service for given entTP set or service name - searching it for all service tree, but first only for new model services as a priority
     *
     * @param serviceNameOrEndTP
     * @return
     */
    public ServiceView getServiceViewByTPsOrName(String serviceNameOrEndTP, String parentNodeName) {
        isNewModelService = false;
        isOldModelService = false;
        ServiceView service = null;
        ServiceView classicService = null;
        mainLog.info("Searching for new model service: {}", serviceNameOrEndTP);
        service = getServiceViewByTPsOrNameAndByTreeNode(serviceNameOrEndTP, true, parentNodeName);

        if (service != null) {
            isNewModelService = true;
        }
        if (service == null && isOldMlVerificationToo) { //ls temporary solution to verify if service was not discovered in new and classic model in the same time
                mainLog.info("Searching for old model service: {}", serviceNameOrEndTP);
                classicService = getServiceViewByTPsOrNameAndByTreeNode(serviceNameOrEndTP, false, parentNodeName);
                if (classicService != null) {
                    mainLog.info("Found service {} is old model", classicService.getResourceName());
                    isOldModelService = true;
                    String tcName = serviceNameOrEndTP;
                    if (this.tcTestName != null) tcName = this.tcTestName;
                    if (!doNotPublishToTC) {
                        TcReportHdlr.publishTestStatus("SEARCHING_FOR_ML_SERVICE", tcName, false, "Service was not found in new model");
                    }
                }
        }
        if (service==null && isOldModelService) return classicService;
        return service;
    }

    /**
     * searching for service for given name and tree node location
     * @param serviceNameOrEndTP
     * @param parentNode name of parent node
     * @return
     */
    public ServiceView getServiceViewByTPsOrNameAndByTreeNode(String serviceNameOrEndTP, String parentNode) {
        ServiceView service = null;
        if (parentNode == null) {
            service = getServiceViewByTPsOrName(serviceNameOrEndTP, true, true);
        } else {
            service = getServiceViewByTPsOrName(serviceNameOrEndTP, true, true, parentNode);
            if (service == null) service = getServiceViewByTPsOrName(serviceNameOrEndTP, false, true, parentNode);
        }
        return service;
    }

    /**
     * searching for service for given name and tree node location
     * @param serviceNameOrEndTP
     * @param isML
     * @param parentNode name of parent node
     * @return
     */
    public ServiceView getServiceViewByTPsOrNameAndByTreeNode(String serviceNameOrEndTP, boolean isML, String parentNode) {
        if (parentNode == null) {
            return getServiceViewByTPsOrName(serviceNameOrEndTP, isML, true);
        } else {
            return getServiceViewByTPsOrName(serviceNameOrEndTP, isML, true, parentNode);
        }
    }

    /**
     * searching for service in two steps : Services folder first and Connections folder later to reduce ids like it is on UI
     * @param serviceNameOrEndTP
     * @param isML
     * @return
     */
    public ServiceView getServiceViewByTPsOrName(String serviceNameOrEndTP, boolean isML) {
        ServiceView service = getServiceViewByTPsOrName(serviceNameOrEndTP, isML, true, TopologyNodeType.SERVICES_ROOT.getName());
        if (service == null) service = getServiceViewByTPsOrName(serviceNameOrEndTP, isML, true, "Connections");
        return service;
    }

    public ServiceView getServiceViewByTPsOrName(String serviceNameOrEndTP, boolean isML, boolean allNodesOnTree) {
        ServiceView service = getServiceViewByTPsOrName(serviceNameOrEndTP, isML, allNodesOnTree, TopologyNodeType.SERVICES_ROOT.getName());
        if (service == null) service = getServiceViewByTPsOrName(serviceNameOrEndTP, isML, allNodesOnTree, "Connections");
        return service;
    }

    /**
     * Gets service for given entTP set or service name - searching it for all service tree
     */
    private ServiceView getServiceViewByTPsOrName(String nameOrTP, boolean isMl, boolean allNodes, String folderName) {
        try {
            List<Integer> ids = new ArrayList<>();
            boolean isComplexName = isComplexFormat(nameOrTP);

            TopologyDTO tree = buildServiceTree(ids, allNodes, isMl, folderName);
            Collection<ServiceView> services = getServiceViewsForServiceTree(ids, tree.getTopologyNodeType());

            for (ServiceView service : services) {
                if (!matchesML(service, isMl)) continue;
                if (matchesDirectly(service, nameOrTP)) return service;
            }

            for (ServiceView service : services) {
                if (!matchesML(service, isMl)) continue;
                if (matchesPartially(service, nameOrTP)) return service;
            }

            if (isComplexName) {
                ServiceView match = findMatchByTPs(services, nameOrTP, isMl);
                if (match != null) return match;
            }

            return searchByPaths(services, nameOrTP, isMl);

        } catch (Exception e) {
            mainLog.error("Service search failed for '{}': {}", nameOrTP, e.toString());
            return null;
        }
    }

    private boolean isComplexFormat(String name) {
        return name.contains("_") && name.contains(":") && name.contains("_AND_");
    }

    private boolean matchesML(ServiceView service, boolean isMl) {
        return service.isML() == isMl;
    }

    private boolean matchesDirectly(ServiceView service, String target) {
        String lastPath = (service.getPath().length > 0) ? service.getPath(service.getPath().length - 1).getName() : "NA";
        return service.getResourceName().equals(target) || service.getServiceName().equals(target) || lastPath.equals(target);
    }

    private boolean matchesPartially(ServiceView service, String target) {
        return service.getResourceName().contains(target) || service.getServiceName().contains(target);
    }

    private ServiceView findMatchByTPs(Collection<ServiceView> services, String target, boolean isMl) {
        for (ServiceView service : services) {
            String[] params = prepareNameFromLayerAndTPs(service.getId(), service.getResourceName(), isMl);
            if (!matchesML(service, isMl) || params == null) continue;

            String start = params[3] + "/" + params[1];
            String end = params[4] + "/" + params[2];
            String reverse = service.getResourceName().replace(end, "tmp").replace(start, end).replace("tmp", start);

            if (isDesiredService(params[0] + "_" + params[1] + ":" + params[2], target)
                    || isDesiredService(params[0] + "_" + start + ":" + end, target)
                    || isDesiredService(service.getResourceName() + "_" + start + ":" + end, target)
                    || reverse.equals(target)) {
                return service;
            }
        }
        return null;
    }

    private ServiceView searchByPaths(Collection<ServiceView> services, String nameOrTP, boolean isMl) {
        for (ServiceView service : services) {
            if (!matchesML(service, isMl) || !pathContainsRequestedNode(service)) continue;

            if (isDesiredService(service.getResourceName() + "_" + service.getStartTP() + ":" + service.getEndTP(), nameOrTP)
                    || isDesiredService(service.getServiceName() + "_" + service.getStartTP() + ":" + service.getEndTP(), nameOrTP)) {
                return service;
            }

            ServiceView found = searchInTree(nameOrTP, service, isMl);
            if (verifyIfContainedInPaths(found) != null) return found;
        }
        return null;
    }

    private boolean pathContainsRequestedNode(ServiceView service) {
        if (this.requestedTreeNode == null) return false;
        return Arrays.stream(service.getPath())
                .anyMatch(p -> p.getID() == this.requestedTreeNode.getId()
                        && p.getName().equals(this.requestedTreeNode.getName()));
    }

    private ServiceView searchInTree(String name, ServiceView service, boolean isMl) {
        ServiceView found = searchServiceTreeForServiceName(name, service.getId(), TopologyNodeType.SERVICE, service.getStartTP() + ":" + service.getEndTP(), isMl);
        if (found == null) {
            found = searchServiceTreeForServiceName(name, service.getId(), TopologyNodeType.PD_SERVICE, service.getStartTP() + ":" + service.getEndTP(), isMl);
        }
        return found;
    }

    private TopologyDTO buildServiceTree(List<Integer> ids, boolean allNodes, boolean isMl, String folderName) throws OperationFailedException {
        CustomerGroupDTO root = (CustomerGroupDTO) nmsInterface.getTopologyHdlr().getTreeModel(TopologyNodeType.TRAIL_FOLDER);

        if (allNodes) {
            getAllChildresnIds(root, ids, folderName, isMl);
            return this.requestedTreeNode != null ? this.requestedTreeNode : root;
        } else {
            TopologyDTO group = root.getTreeCustomerGroup()[0];
            group.setML(isMl);
            ids.add(group.getId());
            getAllChildresnIds(group, ids, isMl);
            return group;
        }
    }

    ServiceView verifyIfContainedInPaths(ServiceView serviceFound) {
        if (serviceFound == null) return null;
        if (serviceFound.getPath().length == 0) return serviceFound;
        for (Item pathElement : serviceFound.getPath()) {
            if (pathElement.getName().equals(this.requestedTreeNode.getName()) && pathElement.getID()==this.requestedTreeNode.getId()) {
                mainLog.info("{}{}", SERVICE_FOUND, serviceFound.getServiceName());
                return serviceFound;
            }
        }
        return null;
    }

    /**
     * Gets service for given entTP set or service name - searching it for all service tree
     *
     * @param ocsId ocs id
     * @return
     */
    public ServiceView getServiceViewByAssociatedOCS(int ocsId, boolean isMl) {
        mainLog.info("Searching instance for tp set: {}", ocsId);
        try {
            CustomerGroupDTO treeSubnetCustomer;
            CustomerGroupDTO serviceTreeGroup;
            List<Integer> list = new ArrayList<>();
            list.add(0);
            treeSubnetCustomer = (CustomerGroupDTO) nmsInterface.getTopologyHdlr().getTreeModel(TopologyNodeType.CUSTOMER);
            treeSubnetCustomer.setML(isMl);
            serviceTreeGroup = treeSubnetCustomer.getTreeCustomerGroup()[0];
            serviceTreeGroup.setML(isMl);
            list.add(serviceTreeGroup.getId());
            getAllChildresnIds(serviceTreeGroup, list, isMl);
            Collection<ServiceView> services;
            services = searchAllServicesInServiceTree(new ArrayList<>(), treeSubnetCustomer.getId(), treeSubnetCustomer.getTopologyNodeType(), "");
            for (ServiceView service : services) {
                if (service.isML() != isMl) continue;
                ServiceDTO treeService = new ServiceDTO(service.getId(), service.getResourceName(), null, null, null, null, false, false, null, null);
                if (treeService.getAssociatedOCSId() != 0 && treeService.getAssociatedOCSId() == ocsId) {
                    mainLog.info("Found service: {}", service.getResourceName());
                    return service;
                }
            }
        } catch (NullPointerException e) {
            mainLog.error("Not sure there is a requested ocs id?!");
        } catch (OperationFailedException e) {
            mainLog.error(e);
        }
        mainLog.error("There is no any service for given ocs id!");
        return null;
    }

    /**
     * makes deep searching for given layer id for all children to be same in name or endTPs
     *
     * @param name endTPs or service name
     * @param id   id of given layer to search into
     * @param type type of tree node
     * @return
     */
    ServiceView searchServiceTreeForServiceName(String name, int id, TopologyNodeType type, String parentTPs, boolean isMl) {
        for (TopologyDTO dto : getChildServiceTreeForServiceLayer(id, type)) {
            ServiceDTO serviceDTO = (ServiceDTO) dto;
            ServiceView view = serviceDTO.getServiceView();
            String[] overview = prepareNameFromLayerAndTPs(serviceDTO.getId(), serviceDTO.getLabel(), isMl);

            if (view != null) {
                if (matchesServiceView(view, serviceDTO.getLabel(), name, overview)) {
                    this.topologyDTO = dto;
                    return view;
                }
                parentTPs = view.getStartTP() + ":" + view.getEndTP();
            } else {
                ServiceView mock = buildMockView(serviceDTO, parentTPs);
                if (matchesMockView(mock, name)) {
                    this.topologyDTO = dto;
                    this.isServiceViewMock = true;
                    return mock;
                }
            }

            // Rekurencja
            ServiceView recursiveResult = searchServiceTreeForServiceName(name, dto.getId(), type, parentTPs, isMl);
            if (recursiveResult != null) return recursiveResult;
        }
        return null;
    }

    private boolean matchesServiceView(ServiceView view, String label, String name, String[] overview) {
        List<String> candidates = List.of(
                label,
                getResourceName(view),
                view.getServiceName(),
                buildPattern(view.getResourceName(), view.getStartTP(), view.getEndTP()),
                buildPattern(view.getServiceName(), view.getStartTP(), view.getEndTP()),
                label + "_" + overview[1] + ":" + overview[2],
                label + "_" + overview[3] + "/" + overview[1] + ":" + overview[4] + "/" + overview[2],
                overview[0] + "_" + overview[1] + ":" + overview[2],
                overview[0] + "_" + overview[3] + "/" + overview[1] + ":" + overview[4] + "/" + overview[2]
        );
        return candidates.stream().anyMatch(c -> isDesiredService(c, name));
    }

    private boolean matchesMockView(ServiceView mock, String name) {
        List<String> candidates = List.of(
                getResourceName(mock),
                buildExtendedPattern(mock)
        );
        return candidates.stream().anyMatch(c -> isDesiredService(c, name) || c.equals(name));
    }

    private String buildPattern(String base, String startTP, String endTP) {
        return base + "_" +
                startTP.replace("PTP", "CH") + ":" + endTP.replace("PTP", "CH") + "_" +
                startTP.replace("PTP", "OM") + ":" + endTP.replace("PTP", "OM") + "_" +
                startTP.replace("C3", "N4") + ":" + endTP.replace("C3", "N4");
    }

    private String buildExtendedPattern(ServiceView view) {
        return getResourceName(view) + "_" +
                tp(view.getStartTP(), "CH", "NE") + ":" + tp(view.getEndTP(), "CH", "NE") + "_" +
                tp(view.getStartTP(), "CH", "NW") + ":" + tp(view.getEndTP(), "CH", "NW") + "_" +
                tp(view.getStartTP(), "OM", "NE") + ":" + tp(view.getEndTP(), "OM", "NE") + "_" +
                tp(view.getStartTP(), "OM", "NW") + ":" + tp(view.getEndTP(), "OM", "NW");
    }

    private String tp(String tp, String type, String replaceSuffix) {
        return tp.replaceAll("C$", replaceSuffix).replace("PTP", type);
    }

    private ServiceView buildMockView(ServiceDTO dto, String parentTPs) {
        ServiceView mock = new ServiceView();
        mock.setServiceName(dto.getName());
        mock.setResourceName(dto.getName());
        mock.setServiceId(dto.getId());
        mock.setAdminState(dto.getAdministrationStateType());
        mock.setOperationalState(dto.getOperationalState());
        mock.setServiceLayer(dto.getServiceLayer());
        mock.setPath(dto.getPath());
        mock.setStartTP(parentTPs.split(":")[0]);
        mock.setEndTP(parentTPs.split(":")[1]);
        return mock;
    }

    /**
     * makes deep searching for all possible services visible n service tree
     *
     * @param services recursively incremented collection of service views
     * @param id       id of given layer to search into
     * @param type     type of tree node
     * @return
     */
    public Collection<ServiceView> searchAllServicesInServiceTree(Collection<ServiceView> services, int id, TopologyNodeType type, String parentTPs) {

        String parentTP = parentTPs;
        ServiceView serviceView = null;
        Collection<TopologyDTO> topologies = getChildServiceTreeForServiceLayer(id, type);
        for (TopologyDTO dto : topologies) {
            type = dto.getTopologyNodeType();

            if (dto instanceof TrailGroupDTO) {
                type = TopologyNodeType.TRAIL_FOLDER;
            }
            if (dto.getTopologyNodeType() == TopologyNodeType.SERVICE || dto.getTopologyNodeType() == TopologyNodeType.PD_SERVICE) {
                serviceView = ((ServiceDTO) dto).getServiceView();
                if (serviceView != null) services.add(serviceView);
            }
            searchAllServicesInServiceTree(services, dto.getId(), type, parentTP);
        }
        return services;
    }

    /**
     * gets service views for all service tree
     *
     * @return
     */
    public Collection<ServiceView> getServiceViewsForServiceTree(List<Integer> ids, TopologyNodeType type) {
        try {
            PagingRestriction restriction = new PagingRestriction(PageArea.SERVICES, PagingRestriction.PageCmd.DOWN, null, 5000, "", BindingColumn.SortingType.ASCENDING, new ArrayList<>());
            restriction.setNodeIds(type, ids);
            restriction.setSortedFieldName("serviceName");
            restriction.setPageOffset(-1);
            restriction.setPageCmd(PagingRestriction.PageCmd.TOP);
            return nmsInterface.getCommonManager().getServerService().getPagingHandlerService().getPageDataList(PageArea.SERVICES, type, restriction);
        } catch (Exception e) {
            mainLog.error(e);
            return Lists.emptyList();
        }
    }

    /**
     * gets service views for all service tree
     *
     * @return
     */
    public Collection<ServiceView> getServiceViewsForServiceTree(List<Integer> ids, TopologyNodeType type, NodeTreeItem nodeFromTree) {
        try {
            PagingRestriction restriction = new PagingRestriction(PageArea.SERVICES, PagingRestriction.PageCmd.DOWN, null, 5000, "", BindingColumn.SortingType.ASCENDING, new ArrayList<>());
            restriction.setNodeIds(type, ids);
            restriction.setSortedFieldName("serviceName");
            DisplayContext displayContext = new DisplayContext();
            displayContext.setClientObject(nodeFromTree);
            restriction.setDisplayContext(displayContext);
            return nmsInterface.getCommonManager().getServerService().getPagingHandlerService().getPageDataList(PageArea.SERVICES, type, restriction);
        } catch (Exception e) {
            mainLog.error(e);
            return Lists.emptyList();
        }
    }

    /**
     * Gets Service Tree objects for any service layer with id and type=SERVICES
     *
     * @param serviceLayerId
     * @param type
     * @return
     */
    public Collection<TopologyDTO> getChildServiceTreeForServiceLayer(int serviceLayerId, TopologyNodeType type) {
        List<TopologyDTO> structure;
        try {
            structure = nmsInterface.getCommonManager().getServerService().getTopologyNodeHdlrRestClient().getSubTreeModel(type, serviceLayerId);
            return !structure.isEmpty() ? structure : Collections.<TopologyDTO>emptyList();
        } catch (OperationFailedException e) {
            mainLog.error(e);
            return Collections.emptyList();
        }
    }

    /**
     * gets service views from network tree ne node
     *
     * @param id ne node
     * @return collection of services
     */
    Collection<ServiceView> getServiceViews(int id) {
        PagingRestClientTest pagingRestClientTest = new PagingRestClientTest();
        try {
            PagingRestriction restriction = new PagingRestriction(PageArea.SERVICES, PagingRestriction.PageCmd.TOP, null, 50, "", BindingColumn.SortingType.ASCENDING, new ArrayList<>());
            restriction.setNodeIds(TopologyNodeType.NETWORK_ELEMENT, Collections.singletonList(id));
            byte[] res = pagingRestClientTest.getPageData(com.adva.nlms.common.paging.PageArea.SERVICES, TopologyNodeType.NETWORK_ELEMENT, restriction);
            @SuppressWarnings("unchecked") final Collection<ServiceView> services = DataTransferObjectHelper.toPageResult(res).getData();

            return services;
        } catch (Exception e) {

            return Lists.emptyList();
        }
    }


    /**
     * gets all ids from customer groups
     *
     * @param treeCustomerGroup
     * @param ids
     * @return
     */
    public List<Integer> getAllChildresnIds(TopologyDTO treeCustomerGroup, List<Integer> ids, boolean isMl) {
        for (TopologyDTO dto : treeCustomerGroup.getChildren()) {
            if ((onlyCustomerIds && dto.getName().equals("Classic Trails")) || (includeConnectionsNode && dto.getName().equals("Discovered")))
                continue; //this is case when only customer folders are taken under consideration. Maybe should refer rather to tnt than name???
            ids.add(dto.getId());
            List<TopologyDTO> listChildDtos = null;
            try {
                listChildDtos = nmsInterface.getTopologyHdlr().getSubTreeModel(nodeType, dto.getId());
            } catch (OperationFailedException e) {
                mainLog.error(e);
            }
            for (TopologyDTO intentDto : listChildDtos) {
                if (intentDto.getTopologyNodeType() == TopologyNodeType.SERVICE_INTENT) {
                    ids.add(intentDto.getId());
                }
            }
            getAllChildresnIds(dto, ids, isMl);
        }
        return ids;
    }

    /**
     * gets all ids from customer groups
     *
     * @param treeCustomerGroup
     * @param ids
     * @param topologyNodeName  name of dto object which will be the starting point for counting dto object ids counted
     * @return
     */
    public List<Integer> getAllChildresnIds(TopologyDTO currentNode, List<Integer> ids, String topologyNodePath, boolean isMl) {
        String[] pathSegments = topologyNodePath.split("\\\\");
        traverseAndCollect(currentNode, ids, pathSegments, 0, isMl);
        return ids;
    }

    private void traverseAndCollect(TopologyDTO node, List<Integer> ids, String[] pathSegments, int level, boolean isMl) {
        for (TopologyDTO child : node.getChildren()) {
            if (!child.getName().equals(pathSegments[level])) continue;

            // dotarliśmy do końca ścieżki
            if (level == pathSegments.length - 1) {
                ids.add(child.getId());
                requestedTreeNode = child;

                addServiceIntentIds(child, ids);
                collectAllChildIds(child, ids, isMl);
            } else {
                // rekurencyjnie w dół po ścieżce
                traverseAndCollect(child, ids, pathSegments, level + 1, isMl);
            }

            break; // nie szukamy dalej, bo znaleźliśmy właściwą gałąź
        }
    }

    private void addServiceIntentIds(TopologyDTO node, List<Integer> ids) {
        try {
            List<TopologyDTO> subtree = nmsInterface.getTopologyHdlr().getSubTreeModel(node.getTopologyNodeType(), node.getId());
            for (TopologyDTO child : subtree) {
                if (child.getTopologyNodeType() == TopologyNodeType.SERVICE_INTENT) {
                    ids.add(child.getId());
                }
            }
        } catch (Exception e) {
            mainLog.error("Error retrieving subtree model for node: {}", node.getName(), e);
        }
    }

    private void collectAllChildIds(TopologyDTO node, List<Integer> ids, boolean isMl) {
        for (TopologyDTO child : node.getChildren()) {
            ids.add(child.getId());
            collectAllChildIds(child, ids, isMl);
        }
    }

    /**
     * gets service name or resource name depends what is not empty
     *
     * @param serviceView
     * @return service or resource name
     */
    public String getResourceName(ServiceView serviceView) {
        return (serviceView.getResourceName() != null && !serviceView.getResourceName().equals("")) ? serviceView.getResourceName() : serviceView.getServiceName();
        }


    /**
     * checks if service naming details contains all desired parts
     *
     * @param serviceEntity service name details
     * @param desiredName   parts of names we expected to have in service details
     * @return true if all parts included in service naming details
     */
    public boolean isDesiredService(String serviceEntity, String desiredName) {
        if (serviceEntity == null || desiredName == null) return false;
        String[] conditions = desiredName.split("_AND_");
        for (String cond : conditions) {
            // Dopasowanie do formatu x_Y:Z
            Matcher matcher = Pattern.compile("^(.+?)_([^:]+):(.+)$").matcher(cond);
            if (matcher.matches()) {
                String part1 = Pattern.quote(matcher.group(1)); // x
                String part2 = Pattern.quote(matcher.group(2)); // Y
                String part3 = Pattern.quote(matcher.group(3)); // Z

                String regex = ".*" + part1 + ".*" + part2 + ".*" + part3 + ".*";
                if (!serviceEntity.matches(regex)) return false;
            } else {
                // Jeśli nie pasuje do formatu x_Y:Z, traktujemy jako zwykły fragment
                if (!serviceEntity.contains(cond)) return false;
            }
        }
        mainLog.info("found it");
        return true;
    }


    public String[] prepareNameFromLayerAndTPs(int serviceId, String serviceName, boolean isMl) {
        PageAreaView page = new PageAreaView(nmsInterface, TopologyNodeType.SERVICE, PageArea.ENTITIES_WITHOUT_POWER_LEVEL);
        List<FormElement> elements = page.getServiceTabByGroup(serviceId, serviceName, isMl, "OVERVIEW", 0);
        if (elements == null) return new String[0];
        String layerProtocol = page.getFieldFromTab(elements, "Layer");
        layerProtocol = layerProtocol.replace((char) 8211, (char) 45); //replacing strange character of "-" from gui
        //first tries to find CPs, if not found then TPs
        String startCP = page.getFieldFromTab(elements, "Start CP");
        String endCP = page.getFieldFromTab(elements, "End CP");
        if (startCP == null || startCP.equals("NA")) startCP = page.getFieldFromTab(elements, "Start TP");
        if (endCP == null || endCP.equals("NA")) endCP = page.getFieldFromTab(elements, "End TP");
        String startNE = page.getFieldFromTab(elements, "Start NE");
        String endNE = page.getFieldFromTab(elements, "End NE");
        if (startNE == null) startNE = page.getFieldFromTab(elements, "Start NE");
        if (endNE == null) endNE = page.getFieldFromTab(elements, "End NE");
        return new String[]{layerProtocol, startCP, endCP, startNE, endNE};
    }

    public String getOverviewFieldByName(int serviceId, String serviceName, String fieldName, boolean isMl, TopologyNodeType nodeType) {
        PageAreaView page = new PageAreaView(nmsInterface, nodeType, PageArea.ENTITIES_WITHOUT_POWER_LEVEL);
        List<FormElement> elements = page.getServiceTabByGroup(serviceId, serviceName, isMl, "OVERVIEW", 0);
        if (elements == null) return null;
        return page.getFieldFromTab(elements, fieldName);
    }

    public String getSelectedTabFieldByName(int serviceId, String serviceName, String fieldName, boolean isMl, String tab, TopologyNodeType nodeType) {
        PageAreaView page = new PageAreaView(nmsInterface, nodeType, PageArea.ENTITIES_WITHOUT_POWER_LEVEL);
        List<FormElement> elements = page.getServiceTabByGroup(serviceId, serviceName, isMl, tab, 0);
        if (elements == null) return null;
        return page.getFieldFromTab(elements, fieldName);
    }


    /**
     * checks leftovers during deletion action. If number of leftovers is decreasing in the loop and finally reduced to 0 then it means success
     */
    public boolean waitForLeftoversOnDB() {
        DBConsistency db = new DBConsistency("c:" + File.separator, "fnm", "root", "ChgMeNOW");
        db.connect();
        boolean noLeftovers = db.waitForClearingLeftoversByTime(leftoverIdsTableAfterPreviousSteps);
        if (!noLeftovers) {
            String errMsg = "Unexpected leftovers in db after deletion";
            mainLog.error(errMsg);
            db.getAndPrintLeftoversTableAfterDeletion();
            leftoverIdsTableAfterPreviousSteps = db.getAndPrintLeftoversIdsTableAfterDeletion();
            GenerateErrorHealthReport.main(errMsg);
        }
        db.disconnect();
        return noLeftovers;
    }


}
