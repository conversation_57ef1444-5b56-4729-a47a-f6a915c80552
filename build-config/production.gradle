/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 */

import org.apache.tools.ant.taskdefs.condition.Os

project.afterEvaluate {
    // Libraries that must have version information stripped since they are command line arguments to java
    task prepareArtifactsMediationMandatory(type: Copy) {
        from(configurations.mandatoryLibs)
        into("$fspRootPackage/mediation/lib")
        rename mandatoryLibsNamingFilter

        duplicatesStrategy = 'exclude'
    }

    task prepareArtifactsAdvaToolsJar(type: Copy) {
        doFirst {
            from(tasks['advaToolsJar'])
        }

        into("$fspRootPackage/mediation/lib")

        fileMode = 0644
    }

    task prepareArtifactsPropupJar(type: Copy) {
        from(mod_propup.project.jar.outputs.files)

        into("$fspRootPackage/mediation/lib")

        fileMode = 0644
    }

    task prepareArtifactsJmsServer(type: Copy) {
        doFirst {
            from(tasks['jmsServerProductionWrapper'])
        }
        from(configurations.jmsServerProduction) {
            exclude("**/yfiles-for-javafx*")
        }

        into("$fspRootPackage/mediation/lib")

        fileMode = 0644

        duplicatesStrategy = 'exclude'
    }

    task prepareArtifactsObfuscatePassword(type: Copy) {
        doFirst {
            from(tasks['obfuscatePasswordProductionWrapper'])
        }

        into("$fspRootPackage/mediation/lib")

        fileMode = 0644
    }

    task prepareArtifactsEncryptPassphrase(type: Copy) {
        doFirst {
            from(tasks['encryptPassphraseProductionWrapper'])
        }

        into("$fspRootPackage/mediation/lib")

        fileMode = 0644
    }

    // Install the ADVA forwarder.jar into the mediation production bundle.
    // This artifacts is obtained from artifactory (not built) and must have the version number stripped from the jar name.
    task prepareArtifactsForwarder(type: Copy) {
        from(tasks['snmpForwarderJarWrapper'])
        from(configurations.snmpForwarderArtifacts)

        into("$fspRootPackage/mediation/lib")

        fileMode = 0644
    }

    task packageClientUpdaterArtifacts(type: Zip) {
        dependsOn([ getTasksByName( 'prepareArtifactsClientUpdater', true ) ])

        from("$fspRootPackage/client-updater")

        destinationDirectory = new File(fspRootArchives)
        archiveBaseName = "production-client-updater"
    }

    task packageFrontendArtifacts(type: Zip) {
        dependsOn([ getTasksByName( 'prepareArtifactsFrontend', true ) ])

        from("$fspRootPackage/frontend")

        destinationDirectory = new File(fspRootArchives)
        archiveBaseName = "production-frontend"
    }

    task prepareArtifactsMediation(type: Copy) {
        dependsOn(mod_target(mod_frontend, 'installFrontendWrappers2Lib'))
        dependsOn(installDocs)
        dependsOn(installYpDb)
        dependsOn(installYpDbCommons)
        dependsOn(installCmsw)
        dependsOn('installElementManagerJars')
        dependsOn(prepareArtifactsMediationMandatory)
        dependsOn(prepareArtifactsAdvaToolsJar)
        dependsOn(prepareArtifactsPropupJar)
        dependsOn(prepareArtifactsJmsServer)
        dependsOn(prepareArtifactsObfuscatePassword)
        dependsOn(prepareArtifactsEncryptPassphrase)
        dependsOn(prepareArtifactsForwarder)
        dependsOn('platformLibs')
        dependsOn('installMandatoryLibs')

        from(fileTree(serverRoot) {
            include("var/web/data/**")
            include("lib/report/*.properties")
            include("docs/**")
        })

        into("$fspRootPackage/mediation")

        fileMode = 0644
    }

    task packageMediationLibArtifacts(type: Zip) {
        dependsOn([ getTasksByName( 'installJars', true ) ])
        dependsOn([ getTasksByName( 'prepareArtifactsMediation', true ) ])
        dependsOn(prepareWarArtifacts)

        from("$fspRootPackage/mediation") {
            include "lib/**"
        }

        destinationDirectory = new File(fspRootArchives)
        archiveBaseName = "production-mediation-lib"
    }

    task packageMediationWarArtifacts(type: Zip) {
        dependsOn([ getTasksByName( 'prepareArtifactsMediation', true ) ])
        dependsOn(prepareWarArtifacts)

        from("$fspRootPackage/mediation") {
            include "ws/deploy/**"
            // Exclude development only debug web applications
            exclude "ws/deploy/mltopologyui*.war"
        }

        destinationDirectory = new File(fspRootArchives)
        archiveBaseName = "production-mediation-war"
    }

    task packageMediationConfigArtifacts(type: Zip) {
        dependsOn([ getTasksByName( 'prepareArtifactsMediation', true ) ])
        dependsOn(prepareWarArtifacts)

        from("$fspRootPackage/mediation") {
            exclude "lib/**"
            exclude "ws/deploy/**"
        }

        destinationDirectory = new File(fspRootArchives)
        archiveBaseName = "production-mediation-config"
    }

    task packageArtifacts() {
        group('enc.build')
        description('Package ENC artifacts required to create a production image')

        dependsOn(packageClientUpdaterArtifacts)
        dependsOn(packageFrontendArtifacts)
        dependsOn(packageMediationLibArtifacts)
        dependsOn(packageMediationWarArtifacts)
        dependsOn(packageMediationConfigArtifacts)

        dependsOn(mod_target(mod_frontend, 'publishFrontendPublicationToNmsartifactoryRepository'))

        if (Os.isFamily(Os.FAMILY_UNIX)) {
            dependsOn(rootProject.tasks['patchJarsWithBom'])
        }
    }

    task updateTCPackageArtifactsSuccessMessage() {
        doLast {
            print("##teamcity[buildStatus status='SUCCESS' text='Success: published java artifacts for build $Version $BuildNumber']\n")
        }
    }

    /**
     * Collect build artifacts to use in production build
     * This Task is distilled down to collect just the artifacts produced by the build
     * that is required for the production build.
     */
    task production() {
        dependsOn(packageArtifacts)
    }
}

mod_frontend.project.afterEvaluate {
    // Include all javafx platform jars in packaging
    task prepareFrontendProduction(type: Copy) {
        from(mod_frontend.project.tasks.windowsFrontendJarWrapper)
        from(mod_frontend.project.tasks.linuxFrontendJarWrapper)
        from(mod_frontend.project.configurations.production)
        into("$fspRootPackage/frontend/lib")

        fileMode = 0644
        rename { String filename ->
            return stripVersion(filename)
        }
    }

    mod_frontend.project.tasks.prepareArtifactsFrontend.dependsOn(prepareFrontendProduction)
}

mod_mediation.project.afterEvaluate {
    task prepareMonitoringConfigFiles(type: Copy) {
        dependsOn( mod_mediation.project.tasks['createMonitoringConfigFiles'] )

        from("$serverRoot/monitoringConfig")

        into("$fspRootPackage/mediation/monitoringConfig")

        fileMode = 0644
    }

    // Include all javafx platform jars in packaging
    task prepareMediationProduction(type: Copy) {
        from(mod_mediation.project.configurations.production)
        into("$fspRootPackage/mediation/lib")

        fileMode = 0644
        rename { String filename ->
            return stripVersion(filename)
        }
    }

    rootProject.afterEvaluate {
        // Ensure monitoring config files are included in the production-mediation.zip bundle
        rootProject.tasks.prepareArtifactsMediation.dependsOn(prepareMonitoringConfigFiles)
        rootProject.tasks.prepareArtifactsMediation.dependsOn(prepareMediationProduction)

        Set<Task> patchJarTasks = rootProject.getTasksByName('patchJarWithBom', true)
        mod_frontend.project.tasks['prepareAdvaFrontendArtifacts'].dependsOn( patchJarTasks )
        mod_mediation.project.tasks['prepareAdvaMediationArtifacts'].dependsOn( patchJarTasks )
    }
}
