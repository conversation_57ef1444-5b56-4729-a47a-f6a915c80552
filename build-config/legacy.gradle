/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 */

import java.util.stream.Collectors

List<Project> targetProjects = [project, mod_nms_admin.project]

// Apply to root project and /modules/mediation/nms-admin so that legacyNmsAdmin classpath is available for BOM creation
targetProjects.each { Project p ->
    p.afterEvaluate {
        p.configurations {
            legacyCommonCompile
            legacyCommonRuntime

            legacyClientCompile
            legacyClientRuntime

            legacyServerCompile
            legacyServerRuntime

            legacyJmsBroker
            legacyGuiBuild
            legacyNmsAdmin
            legacyWorkarounds

            legacyTestsCompile
        }

        p.dependencies {
            legacyCommonCompile dgroup_common_compile
            legacyCommonRuntime dgroup_common_runtime

            legacyClientCompile dgroup_client_compile
            legacyClientRuntime dgroup_client_runtime
            // Required by frontend for now until it can be properly removed
            legacyClientRuntime libs.org.openide.util

            legacyServerCompile dgroup_server_compile
            legacyServerRuntime dgroup_server_runtime

            legacyJmsBroker dgroup_jms_broker

            legacyGuiBuild libs.yguard

            legacyNmsAdmin libs.tink
            legacyNmsAdmin dgroup_nms_admin
            legacyNmsAdmin(modep(mod_security_api)) {
                transitive = false
            }
            legacyNmsAdmin(modep(mod_security_impl)) {
                transitive = false
            }

            legacyTestsCompile dgroup_tests_compile
        }

    }
}

// Define required closures to execute legacyProperties task

// Simple method for building up the properties for ant but is problematic on Windows as the gradle paths
// are quite long and blow up the Window path limits.
def String buildClassPathPropertyString(String name, Configuration config) {
    return "$name=" + config.files.stream()
            .map({f -> f.toURI().toString()})
            .sorted()
            .collect(Collectors.joining(";\\\n"))
}

def String buildClassPathLibPropertyString(String name, Configuration config, Map<String, String> renameMap) {
    return "$name=" + config.files.stream()
            .map({f -> '${lib.dir}/' + stripVersion(f.getName())})
            .sorted()
            .collect(Collectors.joining(";\\\n"))
}

def String buildClassPathTestPropertyString(String name, Configuration config, Map<String, String> renameMap) {
    return "$name=" + config.files.stream()
            .map({f -> '${test.lib.dir}/' + stripVersion(f.getName())})
            .sorted()
            .collect(Collectors.joining(";\\\n"))
}

def String buildClassPathDistPropertyString(String name, Configuration config, Map<String, String> renameMap) {
    return "$name=" + config.files.stream()
            .map({f -> stripVersion(f.getName())})
            .sorted()
            .collect(Collectors.joining(";"))
}

project.afterEvaluate { Project p ->
    // Logic for root project only
    p.tasks.create("legacyProperties") {
        inputs.files(p.configurations.legacyCommonCompile)
        inputs.files(p.configurations.legacyCommonRuntime)
        inputs.files(p.configurations.legacyClientCompile)
        inputs.files(p.configurations.legacyClientRuntime)
        inputs.files(p.configurations.legacyServerCompile)
        inputs.files(p.configurations.legacyServerRuntime)
        inputs.files(p.configurations.legacyJmsBroker)
        inputs.files(p.configurations.legacyGuiBuild)
        inputs.files(p.configurations.legacyNmsAdmin)
        inputs.files(p.configurations.legacyWorkarounds)
        inputs.files(p.configurations.legacyTestsCompile)

        outputs.files(
                "$serverRoot/lib/report/ivy_classpaths.properties",
                "$serverRoot/lib/report/ivy_dist_classpaths.properties",
                "$serverRoot/test/lib/report/ivy_test_classpath.properties"
        )

        // Create the legacy 'path' properties files used for compile and runtime classpath definitions
        // in ant properties file format.
        doLast {

            // ensure path exists
            mkdir("$serverRoot/lib/report")

            // Create lib/report/ivy_classpaths.properties classpaths
            String classpaths = buildClassPathLibPropertyString("ivy.classpath.common-compile", p.configurations.legacyCommonCompile, customRenameMap)
            classpaths += "\n\n" + buildClassPathLibPropertyString("ivy.classpath.common-runtime", p.configurations.legacyCommonRuntime, customRenameMap)

            classpaths += "\n\n" + buildClassPathLibPropertyString("ivy.classpath.client-compile", p.configurations.legacyClientCompile, customRenameMap)
            classpaths += "\n\n" + buildClassPathLibPropertyString("ivy.classpath.client-runtime", p.configurations.legacyClientRuntime, customRenameMap)

            classpaths += "\n\n" + buildClassPathLibPropertyString("ivy.classpath.server-compile", p.configurations.legacyServerCompile, customRenameMap)
            classpaths += "\n\n" + buildClassPathLibPropertyString("ivy.classpath.server-runtime", p.configurations.legacyServerRuntime, customRenameMap)

            classpaths += "\n\n" + buildClassPathLibPropertyString("ivy.classpath.jms-broker", p.configurations.legacyJmsBroker, customRenameMap)
            classpaths += "\n\n" + buildClassPathLibPropertyString("vy.classpath.gui-build", p.configurations.legacyGuiBuild, customRenameMap)
            classpaths += "\n\n" + buildClassPathLibPropertyString("ivy.classpath.nms-admin", p.configurations.legacyNmsAdmin, customRenameMap)
            classpaths += "\n\n" + buildClassPathLibPropertyString("ivy.classpath.workarounds", p.configurations.legacyWorkarounds, customRenameMap)

            File f = new File("$serverRoot/lib/report/ivy_classpaths.properties")
            f.write(classpaths)

            String properties = buildClassPathDistPropertyString("ivy.classpath.common-compile", p.configurations.legacyCommonCompile, customRenameMap)
            properties += "\n" + buildClassPathDistPropertyString("ivy.classpath.common-runtime", p.configurations.legacyCommonRuntime, customRenameMap)

            properties += "\n" + buildClassPathDistPropertyString("ivy.classpath.client-compile", p.configurations.legacyClientCompile, customRenameMap)
            properties += "\n" + buildClassPathDistPropertyString("ivy.classpath.client-runtime", p.configurations.legacyClientRuntime, customRenameMap)

            properties += "\n" + buildClassPathDistPropertyString("ivy.classpath.server-compile", p.configurations.legacyServerCompile, customRenameMap)
            properties += "\n" + buildClassPathDistPropertyString("ivy.classpath.server-runtime", p.configurations.legacyServerRuntime, customRenameMap)

            properties += "\n" + buildClassPathDistPropertyString("ivy.classpath.jms-broker", p.configurations.legacyJmsBroker, customRenameMap)
            properties += "\n" + buildClassPathDistPropertyString("vy.classpath.gui-build", p.configurations.legacyGuiBuild, customRenameMap)
            properties += "\n" + buildClassPathDistPropertyString("ivy.classpath.nms-admin", p.configurations.legacyNmsAdmin, customRenameMap)
            properties += "\n" + buildClassPathDistPropertyString("ivy.classpath.workarounds", p.configurations.legacyWorkarounds, customRenameMap)

            f = new File("$serverRoot/lib/report/ivy_dist_classpaths.properties")
            f.write(properties)

            // ensure path exists
            mkdir("$serverRoot/test/lib/report")

            // Create test/lib/report/ivy_test_classpath.properties classpaths
            String testClasspaths = buildClassPathTestPropertyString("ivy.classpath.tests-compile", p.configurations.legacyTestsCompile, customRenameMap)

            f = new File("$serverRoot/test/lib/report/ivy_test_classpath.properties")
            f.write(testClasspaths)
        }
    }

    // Silence gradle warnings if legacy targets are also run.
    // These tasks are used for old ant driven tests so they should not be full dependencies
    if (gradle.startParameter.getTaskNames().contains('packageArtifacts')) {
        // The 'prepareArtifacts' tasks only are used when running 'packageArtifacts'
        p.tasks['prepareArtifactsMediation'].dependsOn('legacyProperties')
    }
}