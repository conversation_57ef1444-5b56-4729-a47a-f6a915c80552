/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 */

allprojects {
    // Apply dependency analysis plugins
    apply plugin: 'project-report'
    apply plugin: 'com.autonomousapps.dependency-analysis'
}

// Define task relationships for dependency-analysis, required for gradle 8
// Impacts generated code sources.

mod_nmscommon.project.afterEvaluate { Project p ->
    p.tasks.explodeCodeSourceMain.mustRunAfter( p.tasks.generationCodeCopy )
}

mod_mediation.project.afterEvaluate { Project p ->
    p.tasks.explodeCodeSourceMain.mustRunAfter( p.tasks.generateMtosiNaming )
    p.tasks.explodeByteCodeSourceTest.mustRunAfter( p.tasks.prepareTestSchemaResourcesForMediation )
}
