/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 */

optional {
    matchTask('publishSourceAnalysis')
    matchTask('sourceAnalysis')

    buildFragment("source-analysis.gradle")
}

optional {
    matchTask('production')
    matchTask('packageArtifacts')
    matchTask('packageProductionImage')
    matchTask('updateTCPackageArtifactsSuccessMessage')
    matchTask('assembleProductionImage')

    // Some platform build tasks use production build tasks
    matchExtraProperty('buildPlatform', true)

    buildFragment('production.gradle')
}

optional {
    // nmsadmin BOM need 'legacyNmsAdmin' configuration defined here so include legacy build fragment when creating BOMs
    matchTask('generateBoms')
    matchTask('legacyProperties')

    buildFragment("legacy.gradle")
}

optional {
    // Package artifacts needs BOM generation so bom.json can be included in MNC jar artifacts
    matchTask('packageArtifacts')
    matchTask('packageProductionImage')
    matchTask('updateTCPackageArtifactsSuccessMessage')
    matchTask('assembleProductionImage')

    matchTask('generateBoms')
    matchTask('combineBoms')
    matchTask('cyclonedxBom')
    matchTask('enhanceBom')
    matchTask('validateBom')
    matchTask('patchJarWithBom')
    matchTask('patchJarsWithBom')

    // Common BOM generation logic
    buildFragment("bom.gradle")
}

optional {
    matchTask('collectOpenapiManifests')
    matchTask('collectAsyncapiManifests')
    matchTask('generateOpenapi')
    matchTask('generateAsyncapi')
    matchTask('generateAdvaBaseOpenapi')
    matchExtraProperty('PluginDeveloper', true)

    buildFragment(mod_mediation, "mediation-api.gradle")
}

optional {
    matchTask('collectOpenapiManifests')
    matchTask('collectAsyncapiManifests')
    matchTask('generateOpenapi')
    matchTask('generateAsyncapi')
    matchExtraProperty('PluginDeveloper', true)
    buildFragment(mod_job_manager_app, "job-manager-api.gradle")
}

optional {
    matchTask('collectOpenapiManifests')
    matchTask('collectAsyncapiManifests')
    matchTask('generateOpenapi')
    matchTask('generateAsyncapi')
    matchExtraProperty('PluginDeveloper', true)
    buildFragment(mod_notification_manager_app, "notification-manager-api.gradle")
}

optional {
    matchTask('collectOpenapiManifests')
    matchTask('generateOpenapi')
    matchExtraProperty('PluginDeveloper', true)
    buildFragment(mod_cap_prov_rest_server, "cap-prov-api.gradle")
}


optional {
    matchTask('collectOpenapiManifests')
    matchTask('generateOpenapi')
    matchExtraProperty('PluginDeveloper', true)
    buildFragment(mod_csm_rest_server, "csm-api.gradle")
}

optional {
    matchTask('collectAsyncapiManifests')
    matchTask('generateAsyncapi')
    matchExtraProperty('PluginDeveloper', true)
    buildFragment(mod_csm_messaging, "csm-api.gradle")
}

optional {
    matchTask('collectOpenapiManifests')
    matchTask('generateOpenapi')
    matchExtraProperty('PluginDeveloper', true)
    buildFragment(mod_service_sync_rest_server, 'service_sync-api.gradle')
}

optional {
    matchTask('collectOpenapiManifests')
    matchTask('generateOpenapi')
    matchExtraProperty('PluginDeveloper', true)
    buildFragment(mod_service_migration_tool_rest_server, 'service_migration-api.gradle')
}

optional {
    matchTask('collectAsyncapiManifests')
    matchTask('generateAsyncapi')
    matchExtraProperty('PluginDeveloper', true)
    buildFragment(mod_service_migration_tool_messaging, 'service-migraion-tool-api.gradle')
}

optional {
    matchTask('collectAsyncapiManifests')
    matchTask('generateAsyncapi')
    matchExtraProperty('PluginDeveloper', true)
    buildFragment(mod_service_sync_messaging, 'service-synchronizer-api.gradle')
}

/* Currently these kafka API's use protobuf data structures which causes the
 * document generation to hang attempting to generate the schema for "Notification"
optional {
    matchTask('collectAsyncapiManifests')
    matchTask('generateAsyncapi')
    matchExtraProperty('PluginDeveloper', true)
    buildFragment(mod_ni_client, 'ni-client-api.gradle')
}
 */

optional {
    matchTask('collectAsyncapiManifests')
    matchTask('generateAsyncapi')
    buildFragment(mod_inf_impl, 'inf-impl-api.gradle')
}

optional {
    matchTask('collectAsyncapiManifests')
    matchTask('asyncapiReport')
    matchExtraProperty('PluginDeveloper', true)
    buildFragment('asyncapi.gradle')
}

optional {
    matchTask('collectOpenapiManifests')
    matchExtraProperty('PluginDeveloper', true)
    buildFragment('openapi.gradle')
}

optional {
    matchExtraProperty('buildPlatform', true)
    buildFragment('platform.gradle')
}

optional {
    // project-report tasks
    matchTask('dependencyReport')
    matchTask('htmlDependencyReport')
    matchTask('propertyReport')
    matchTask('taskReport')
    matchTask('projectReport')

    // dependency-analysis tasks
    matchTask('buildHealth')
    matchTask('projectHealth')
    matchTask('reason')

    buildFragment("build-report.gradle")
}

optional {
    matchTask('idea')
    matchTask('cleanIdea')
    matchTask('cleanIdeaProject')
    matchTask('cleanIdeaModule')
    matchTask('cleanIdeaWorkspace')
    matchTask('ideaProject')
    matchTask('ideaModule')
    matchTask('ideaWorkspace')

    buildFragment("idea.gradle")
}

optional {
    matchExtraProperty('codeCoverage', true)

    matchTask('generateCodeCoverageReport')

    buildFragment('code-coverage.gradle')
}
