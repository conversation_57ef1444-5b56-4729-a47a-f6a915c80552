/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 */

tasks.register('collectAsyncapiManifests') {
    File asyncapiPublicationManifestFile = layout.buildDirectory.file('asyncapi/publication-manfest.yaml').get().asFile
    group('asyncapi')
    description("Create asyncapi documents for all project in the build and collect the artifactory publication info in $asyncapiPublicationManifestFile")

    outputs.files( asyncapiPublicationManifestFile )

    // Ensure generateAsyncapi tasks are run before creating the archive of documents
    dependsOn( getTasksByName('publishAsyncapi', true) )

    doLast {
        // Collect the publication data and combine into single manifest
        def mapper = jacksonObjectMapper

        // Create asyncapi publication yaml summary file
        Map<String, List<Map<String, String>>> asyncapiManifestMap = new HashMap<>()
        List<Map<String, String>> asyncapiArtifactList = new LinkedList<>()
        asyncapiManifestMap.put('publications', asyncapiArtifactList)

        fileTree(projectDir) {
            include '**/build/asyncapi/publication-manifest.yaml'
        }.files.each { File f ->
            Map<String, String> asyncapiManifestFragment = mapper.readValue(f, Map.class)
            asyncapiArtifactList.addAll(asyncapiManifestFragment.get("publications"))
            logger.info("Added {} to asyncapi publication manifest", asyncapiManifestFragment.get("publications"))
        }

        asyncapiPublicationManifestFile.parentFile.mkdirs()
        mapper.writeValue(asyncapiPublicationManifestFile, asyncapiManifestMap)
    }
}

rootProject.configurations {
    asyncapiReport
}

rootProject.dependencies {
    asyncapiReport modep(mod_AsyncapiTools)
}

rootProject.tasks.register('asyncapiReport', JavaExec) {
    group('asyncapi')
    description("Create a report on the topics, consumers, and producers described in generated asyncapi documents")

    mustRunAfter([ rootProject.getTasksByName( 'generateAsyncapi', true ) ])

    mainClass = 'com.adtran.asyncapi.tools.AsyncapiReport'

    classpath = configurations.asyncapiReport

    doFirst {
        rootProject.fileTree( rootProject.projectDir ) {
            include 'libraries/**/build/asyncapi/*/*.yaml'
            include 'layers/**/build/asyncapi/*/*.yaml'
            include 'modules/**/build/asyncapi/*/*.yaml'
        }.files.each { File f ->
            args(f)
        }
    }
}