/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 */

apply plugin: 'application'

mkdir('codecoverage/xml')

// Apply jacoco plugin to all projects so that jacoco data will be collected
// The plugin will automatically collect data for 'Test' tasks
subprojects { Project subproject ->
    apply plugin: 'jacoco'
    subproject.jacoco {
        toolVersion = libs.versions.ver.jacoco.get()
    }
}

// Create a configuration for holding the artifacts that are needed to run jacoco report
configurations {
    jacocoReport
}

dependencies {
    jacocoReport libs.jacoco.cli
}

// Short term solution to duplicated classes that break coverage analysis
task removeDuplicatedClasses(type: Delete) {
    delete 'modules/mediation/build/classes/java/main/com/adva/nlms/mediation/common/housekeeping/auth/AuthConfig.class'
    delete 'modules/mediation/build/classes/java/main/com/adva/nlms/mediation/common/housekeeping/auth/AuthenticationClient.class'
    delete 'modules/mediation/build/classes/java/main/com/adva/nlms/mediation/common/housekeeping/auth/AuthPrompt.class'
    delete 'modules/mediation/build/classes/java/main/com/adva/nlms/mediation/common/housekeeping/auth/AuthType.class'
    delete 'modules/mediation/build/classes/java/main/com/adva/nlms/mediation/common/housekeeping/auth/HostKeyChangedException.class'
    delete 'modules/mediation/build/classes/java/main/com/adva/nlms/mediation/common/housekeeping/auth/HostKeyEvent.class'
    delete 'modules/mediation/build/classes/java/main/com/adva/nlms/mediation/common/housekeeping/auth/HostKeyUnknownException.class'
    delete 'modules/mediation/build/classes/java/main/com/adva/nlms/mediation/common/housekeeping/auth/Messages.class'
}

Collection<String> ccIncludeProjects = codeCoverageProjectInclusions.isEmpty() ? [] : Arrays.asList(codeCoverageProjectInclusions.split(','))
Collection<String> ccExcludeProjects = codeCoverageProjectExclusions.isEmpty() ? [] : Arrays.asList(codeCoverageProjectExclusions.split(','))

def isCodeCoverageProject = { Project p ->
    if (ccExcludeProjects.contains(p.path)) {
        // This project is explicitly excluded
        return false
    }
    if (ccIncludeProjects.isEmpty()) {
        // There is no include list so allow this project
        return true
    } else if (ccIncludeProjects.contains(p.path)) {
        // this project is explicitly included
        return true
    }
    // Not explicitly included but include list exists so exclude this project
    return false
}

task generateCodeCoverageReport(type: JavaExec) {
    group('enc.test')
    description('Collect all the code coverage data in the workspace and create a coverage report into the codecoverage directory')

    dependsOn(removeDuplicatedClasses)
    mainClass = 'org.jacoco.cli.internal.Main'
    classpath = configurations.jacocoReport
    workingDir = project.projectDir

    doFirst {
        // Build up the arguments for creating the jacoco code coverage report(s)
        Collection arguments = ['report']
        Integer basePathLength = rootProject.projectDir.getCanonicalPath().length() + 1  // add 1 for '/'
        arguments.addAll(rootProject.fileTree(dir: '.', include: '**/*.exec', exclude: '**/postgres/**')
                .getFiles()
                .collect({ File f ->
                    f.getCanonicalPath().substring(basePathLength)
                }))

        rootProject.allprojects { Project p ->
            if (isCodeCoverageProject(p)) {
                p.sourceSets.main.output.classesDirs.collect({ File d ->
                    if (d.exists()) {
                        arguments.add('--classfiles')
                        if (d.getCanonicalPath().endsWith('/compile/java/main')) {
                            // This is a project that weaves, use the classes directory instead
                            d = new File(d.getParentFile().getParentFile().getParentFile(), 'classes/java/main')
                        }
                        arguments.add(d.getCanonicalPath().substring(basePathLength))
                    }
                })
                p.sourceSets.main.java.srcDirs.collect({ File d ->
                    if (d.exists()) {
                        arguments.add('--sourcefiles')
                        arguments.add(d.getCanonicalPath().substring(basePathLength))
                    }
                })
            }
        }
        arguments.add('--html')
        arguments.add('codecoverage/html')
        arguments.add('--xml')
        arguments.add('codecoverage/xml/code-coverage-report.xml')
        arguments.add('--name')
        arguments.add(codeCoverageTitle)

        args = arguments

        logger.info("JaCoCo report command line arguments: {}",
                arguments.join(' ')
        )
    }

    doLast {
        print("##teamcity[buildStatus text='{build.status.text} + code coverage']\n")
    }
}

task codeCoverageProjectList() {
    doLast {
        logger.quiet("Dumping projects included in code coverage reports")
        rootProject.allprojects.sort().each { Project p ->
            // Only consider projects that have sources
            if (new File(p.projectDir, 'src').exists() && isCodeCoverageProject(p)) {
                logger.quiet("  $p.path")
            }
        }
    }
}
