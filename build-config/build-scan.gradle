/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 */

import org.apache.tools.ant.taskdefs.condition.Os

String tcVersion = System.getenv("TEAMCITY_VERSION");
String buildID = System.getenv("TEAMCITY_BUILD_ID");

def tags = new ArrayList<>();
Map<String, String> values = new HashMap<>();
Map<String, String> links = new HashMap<>();

tags.add(getProject().getRootProject().getName())

if (tcVersion != null) {
    // Team city build
    tags.add("TC")

    if (buildID != null) {
        // Build id must me manually added to each teamcity job so only include it if present
        links.put("Link to Team City Build", "https://tc.advaoptical.com/viewLog.html?buildId="+buildID);
    }

    String tcJobName = System.getenv("TEAMCITY_BUILDCONF_NAME");
    if (tcJobName != null && !tcJobName.isEmpty()) {
        values.put("tcJobName", tcJobName);
    }
} else {
    // Developer build
    tags.add("LOCAL")
}

String os
if (Os.isFamily(Os.FAMILY_UNIX)) {
    tags.add("LINUX")
} else {
    tags.add("WINDOWS")
}

if (getProject().getRootProject().getGradle().getStartParameter().isParallelProjectExecutionEnabled()) {
    tags.add("parallel")
}

if (getProject().getGradle().getStartParameter().isBuildCacheEnabled()) {
    tags.add("buildcache")
}

ExtraPropertiesExtension props = getProject().getRootProject().getExtensions().getExtraProperties()

if (props.has("ProductionBuild") && props.get("ProductionBuild") == true) {
    tags.add("ProductionBuild")
}

if (props.has("weaveEnc") && props.get("weaveEnc") == true) {
    tags.add("weaveEnc")
}

if (props.has("buildGNSS") && props.get("buildGNSS") == true) {
    tags.add("buildGNSS")
}

if (props.has("buildPlatform") && props.get('buildPlatform') == true) {
    tags.add("buildPlatform")
}

if (props.has("PluginDeveloper") && props.get("PluginDeveloper") == true) {
    tags.add("PluginDeveloper")
}

if (props.has("prioritizeTasks") && props.get("prioritizeTasks") == true) {
    tags.add("prioritizeTasks")
}

if (props.has("configureFnmProperties") && props.get("configureFnmProperties") == true) {
    tags.add("configureFnmProperties")
}

values.put("branch", getProject().getProviders().exec(spec ->
        spec.commandLine("git", "rev-parse", "--abbrev-ref", "HEAD")
).getStandardOutput().getAsText().get().trim());

if (props.has("cacheServer")  && !((String) props.get("cacheServer")).isEmpty()) {
    values.put("cacheServer", (String) props.get("cacheServer"));
}

values.put("bypassLicenses", (String) props.get("bypassLicenses"));
values.put("flexeraServer", (String) props.get("flexeraServer"));
values.put("backupFlexeraServer", (String) props.get("backupFlexeraServer"));

values.put("JDK", props.has('JDK') ? (String) props.get('JDK') : 'UNKNOWN');
values.put("IDE", props.has('IDE') ? (String) props.get("IDE") : 'UNKNOWN');

if (props.has('fnmPropertiesOverride') && !((String) props.get('fnmPropertiesOverride')).isEmpty()) {
    values.put("fnmPropertiesOverride", (String) props.get("fnmPropertiesOverride"));
}

define('buildTags', tags, 'Definition of tags to include in the build scan')
define('buildValues', values, 'Definition of values to include in the build scan')
define('buildLinks', links, 'Definition of links to include in the build scan')
