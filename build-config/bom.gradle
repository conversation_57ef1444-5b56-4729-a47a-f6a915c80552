/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 */


import groovy.json.JsonOutput
import groovy.json.JsonSlurper

import java.util.stream.Collectors

// Group name to use for locally generated artifacts included in generated BOM's
String groupName = 'com.adtran.enc'

// Common additional data to supply for local artifacts
String licenseName = 'Adtran Software License Name Here'
String licenseText = 'Adtran license text goes here'.bytes.encodeBase64().toString()
String licenseUrl = 'https://adtran.com/software/license/url/here'
String organizationName = 'Adtran Networks SE'
String organizationUrl = 'https://www.adtran.com'

List<Map<String, Object>> adtranLicense = [[
    license: [
        name: licenseName,
        text: [ content: licenseText ],
        url: licenseUrl
    ]
]]


// File to accumulate BOM validation reporting
File reportFile = project.rootProject.file("build-gradle/bom-validation-report.txt")

// Simple customization of some module specific configuration
Map<String, Map<String, Object>> bomCustomization = [:]
bomCustomization.put(mod_mediation.getPath(),  [
        includeConfigs: [ 'runtimeClasspath' ],
        // Don't include any subprojects in the mediation BOM, this will result in many duplicate artifacts
        skipProjects: mod_mediation.project.subprojects*.name,
        projectType: 'application'
])
bomCustomization.put(mod_jms.getPath(),  [
        includeConfigs: [ 'jmsLaunch' ],
        projectType: 'application'
])
bomCustomization.put(mod_nms_admin.getPath(), [
        includeConfigs: [ 'nmsAdminExtra', 'legacyNmsAdmin' ],
        projectType: 'application',
])
bomCustomization.put(mod_csm_app.getPath(), [ projectType: 'application'])
bomCustomization.put(mod_cap_prov_app.getPath(), [ projectType: 'application'])
bomCustomization.put(mod_frontend.getPath(), [ projectType: 'application'])

/**
 * Add mount command line argument into docker command for the passed file
 */
def dockerMount = { String dockerCmd, File target ->
    // Find index of the start of the docker image to run string
    int index = dockerCmd.lastIndexOf(" ")
    String cmdStart = dockerCmd.substring(0, index)
    String cmdEnd = dockerCmd.substring(index)
    return "$cmdStart --volume $target:$target $cmdEnd"
}

tasks.register('generateBoms') { Task task ->
    group = 'project-module'
    description = 'Generates and validates BOMs for all gradle projects in the workspace'

    task.dependsOn(getTasksByName('cyclonedxBom', true))
    task.dependsOn(getTasksByName('enhanceBom', true))
    task.dependsOn(getTasksByName('validateBom', true))

    doLast {
        if (reportFile.exists()) {
            logger.quiet("\n\nBOM validation errors found, see report file $reportFile\n")
        }
    }
}

tasks.register('patchJarsWithBom') { Task task ->
    group = 'project-module'
    description = 'Adds generated SBOM files to jars build for each gradle project'

    task.dependsOn(getTasksByName('cyclonedxBom', true))
    task.dependsOn(getTasksByName('enhanceBom', true))
    task.dependsOn(getTasksByName('validateBom', true))
    task.dependsOn(getTasksByName('patchJarWithBom', true))
}

def cycloneCliMerge = { File outputFile, boolean hierarchical ->
    // Ensure output directory exists
    File outputDir = outputFile.getParentFile()
    outputDir.mkdirs()

    // List of top level MNC applications that define 'BOM'
    String files = mod_mediation.project.file('build/reports/mediation-bom.json').getCanonicalPath() + " " +
            mod_frontend.project.file('build/reports/frontend-bom.json').getCanonicalPath() + " " +
            mod_csm_app.project.file('build/reports/csm_app-bom.json').getCanonicalPath()

    def cmd = cyclonedxCli
    if (cmd.contains('docker')) {
        // When running in docker must mount the file to process into the container
        cmd = dockerMount(cmd, project.projectDir)
    }
    cmd += " merge --input-files $files --input-format json " +
            "--output-file $outputFile --output-format json " +
            "--group $groupName --name enc --version $Version"

    if (hierarchical) {
        cmd += ' --hierarchical'
    }

    logger.quiet("Executing '$cmd'")

    Process process = cmd.execute()

    logger.quiet(process.text)

    // Add manufacturing and license info into combined BOM
    def jsonSlurper = new JsonSlurper()
    Map<String, Object> bomMap = jsonSlurper.parse(outputFile)

    Map<String, Object> metadata = bomMap.get('metadata')

    metadata.put('manufacture', [
            name: organizationName,
            url : [organizationUrl]
    ])
    metadata.put('licenses', adtranLicense)

    outputFile.text = JsonOutput.prettyPrint(JsonOutput.toJson(bomMap))
}

tasks.register('combineBoms') { Task task ->
    group = 'project-module'
    description = 'Combines the primary ENC application BOMs together'

    mustRunAfter(generateBoms)

    doLast {
        cycloneCliMerge project.file('build-gradle/mnc-bom.json'), false
    }
}

tasks.register('combineBomsHierarchical') { Task task ->
    group = 'project-module'
    description = 'Combines the primary ENC application BOMs together hierarchically'

    mustRunAfter(generateBoms)

    doLast {
        cycloneCliMerge project.file('build-gradle/mnc-bom-hierarchical.json'), true
    }
}

/**
 * Process regex match extracted from 'purl'
 */
def extractBomJars = { List<List<String>> match, List<Map<String, String>> bomArtifacts, Set<String> bomJars ->
    String group = match[0][1]
    String name = match[0][2]
    String version = match[0][3]
    String remainder = match[0][4]

    if (version.isEmpty() || version.equals("unspecified")) {
        version = ''
    } else {
        version = "-${version}"
    }

    String extension = '.jar'
    match = (remainder =~/type=([^\&]+)/).findAll()
    if (match.size == 1 && ((List) match[0]).size() >= 1) {
        extension = '.' + match[0][1]
    }

    String classifier = ''
    match = (remainder =~/classifier=([^\&]+)/).findAll()
    if (match.size == 1 && ((List) match[0]).size() >= 1) {
        classifier = '-' + match[0][1]
    }

    bomArtifacts.add([group: group, name: name, version: version, qualifier: classifier, ext: extension])
    // Note must use 'toString' to get a java string or 'contains' checks won't work on the native 'gstring'
    bomJars.add("${name}${version}${classifier}${extension}".toString())
}

def compareArtifacts = {Map<String, String> a, Map<String, String> b ->
    return a.get('group').toString().equals(b.get('group').toString()) &&
            a.get('name').toString().equals(b.get('name').toString()) &&
            a.get('version').toString().equals(b.get('version').toString())
}

def calculateArtifactDifferences = { Project project, Collection<String> includeConfigsValue, List<Map<String, String>> bomArtifacts,
                                     List<String> localJars, List<Map<String, String>> missingArtifacts, List<Map<String, String>> addedArtifacts ->
    List<Map<String, String>> artifacts = []
    for (String config : includeConfigsValue) {
        project.configurations[config].resolvedConfiguration.each { ResolvedConfiguration rc ->
           rc.resolvedArtifacts.each { ResolvedArtifact ra ->
               artifacts.add([
                       group: ra.moduleVersion.id.group,
                       name: ra.moduleVersion.id.name,
                       version: ra.moduleVersion.id.version,
                       classifier: ra.classifier,
                       extension: ra.extension,
               ])
           }
        }
    }

    for (Map<String, String> ba : bomArtifacts) {
        def match = artifacts.stream().anyMatch { a -> return compareArtifacts(a, ba) }

        Boolean manualPatch = ba.get("manualPatch")
        if (manualPatch == null) manualPatch = false

        // Don't consider it an error if the artifact added is the project creating the BOM
        if (!match && !manualPatch && !compareArtifacts(ba, [group: project.group, name: project.name, version: project.version])) {
            addedArtifacts.add(ba)
        }
    }

    for (Map<String, String> a : artifacts) {
        def match = bomArtifacts.stream().anyMatch { ba -> return compareArtifacts(a, ba) }

        // Don't consider it an error if the artifact missing is the project creating the BOM
        if (!match && !compareArtifacts(a, [group: project.group, name: project.name, version: project.version])) {
            missingArtifacts.add(a)
        }
    }
}

/**
 * Fill in the added and missing artifacts into report for difference between BOM and actual runtime classpath
 */
def buildArtifactDifferenceReport = { Project project, StringBuilder report, List<Map<String, String>> missingArtifacts, List<Map<String, String>> addedArtifacts ->
    if (!addedArtifacts.isEmpty() || !missingArtifacts.isEmpty()) {
        report.append("BOM Discrepency Report for $project.name\n\n")
    }

    if (!addedArtifacts.isEmpty()) {
        report.append("\n").append(addedArtifacts.size()).append(" spurious artifacts found in BOM").append('\n\n')
        int i=1;
        for (Map<String, String> artifact : addedArtifacts) {
            report.append(i++).append(" ")
                    .append(artifact.get('group')).append(":")
                    .append(artifact.get('name')).append(":")
                    .append(artifact.get('version'))
                    .append('\n')
        }
    }

    if (!missingArtifacts.isEmpty()) {
        report.append("\n").append(missingArtifacts.size()).append(" artifacts missing from BOM").append('\n\n')
        int i=1;
        for (Map<String, String> artifact : missingArtifacts) {
            report.append(i++).append(" ")
                    .append(artifact.get('group')).append(":")
                    .append(artifact.get('name')).append(":")
                    .append(artifact.get('version'))
                    .append('\n')
        }
    }
}

/**
 * Create duplicate artifact report by using cyclonedx cli to process the BOM file.
 */
def buildArtifactDuplicationReport = { Project project, StringBuilder report, File bomFile, boolean continueReport ->
    def jsonSlurper = new JsonSlurper()

    // Analyze BOM file for duplicates
    def cmd = cyclonedxCli
    if (cmd.contains('docker')) {
        // When running in docker must mount the file to process into the container
        cmd = dockerMount(cmd, bomFile.getParentFile())
    }
    cmd += " analyze --input-file $bomFile --input-format json --output-format json --multiple-component-versions"

    logger.quiet("Executing '$cmd'")
    String duplicateReportJson = cmd.execute().text
    if (duplicateReportJson == null || duplicateReportJson.isEmpty()) {
        return
    }

    Map<String, Object> duplicateReportMap = jsonSlurper.parse(duplicateReportJson.bytes)

    Map<String, List<Map<String, String>>> duplicatedItemMap = duplicateReportMap.get('multipleComponentVersions')
    if (!duplicatedItemMap.isEmpty()) {
        if (continueReport) {
            report.append('\n\n====================\n\n')
        }

        // Calculate max widths for report
        int groupMaxWidth = 20
        int nameMaxWidth = 20

        duplicatedItemMap.values().stream().forEach { item ->
            int groupLen = item.get(0).get('group').length()
            if (groupLen > groupMaxWidth) groupMaxWidth = groupLen
            int nameLen = item.get(0).get('name').length()
            if (nameLen > nameMaxWidth) nameMaxWidth = nameLen
        }

        report.append('Duplicate Artifact Report for ')
                .append(project.name).append('\n\n')
                .append(String.format("%-${groupMaxWidth}s %-${nameMaxWidth}s %s\n", 'Group', 'Name', 'Versions'))
                .append('_'.repeat(groupMaxWidth + nameMaxWidth + 'Versions'.length() + 2))
                .append('\n')

        duplicatedItemMap.values().stream()
                .sorted((a,b) ->
                        a.get(0).get("name").compareTo(b.get(0).get("name")))
                .forEach {item ->
                    List<Map<String, Object>> items = item

                    report.append(String.format("%-${groupMaxWidth}s %-${nameMaxWidth}s %s\n",
                            items.get(0).get('group'),
                            items.get(0).get('name'),
                            items.stream()
                                    .map(i -> ((Map) i).get('version'))
                                    .collect(Collectors.joining(', '))
                    ))
                }
    }
}

def isManualPatch = { Map<String, Object> component ->

    List<String> tags = component.get("tags")
    if (tags == null) return false;
    return tags.stream().anyMatch { tag -> tag.equals("MANUAL_PATCH")}
}

/**
 * Generate BOM validation report for the passe project and BOM file
 */
def buildBomValidationReport = { Project project, File bomFile, Collection<String> includeConfigsValue ->
    def jsonSlurper = new JsonSlurper()
    Map bomMap = jsonSlurper.parse(bomFile)

    List components = bomMap.get('components')

    // Create a list of jars created by the build
    List<String> localJars = []
    project.rootProject.subprojects.each { Project p ->
        localJars.add("${p.name}-${Version}.jar".toString())
    }

    // Extract data from BOM json
    List<Map<String, String>> bomArtifacts = []
    Set<String> bomJars = [] as Set
    for (Map component : components) {
        String groupValue = component.get('group')
        String nameValue = component.get('name')
        String versionValue = component.get('version')
        // components like Postgres server are added to BOM but will not be found in validation
        // (i.e. Postgres server is not found on the runtimte classpath).
        // These manunally added components must be tagged with "MANUAL_PATCH" so they can be
        // ignored by BOM content validation.
        boolean manualPatch = isManualPatch(component)

        if (groupValue != null && nameValue != null && versionValue != null) {
            bomArtifacts.add([group: groupValue, name: nameValue, version: versionValue, manualPatch: manualPatch])
        } else {
            String purl = component.get('purl')
            List<List<String>> match = (purl =~/pkg\:maven\/([^\/]+)\/([^@]+)@([^\?]+)\?(.*)/).findAll()
            if (match.size() == 1 && ((List) match[0]).size() >= 4) {
                extractBomJars(match, bomArtifacts, bomJars)
            } else if (purl != null && !purl.startsWith('pkg:generic/')) { // Note: some components may be identified by cpe
                logger.quiet("  unexpected BOM processing result {}", match)
            }
        }

    }

    StringBuilder report = new StringBuilder()
    List<Map<String, String>> missingArtifacts = []
    List<Map<String, String>> addedArtifacts = []

    calculateArtifactDifferences(project, includeConfigsValue, bomArtifacts, localJars, missingArtifacts, addedArtifacts)

    buildArtifactDifferenceReport(project, report, missingArtifacts, addedArtifacts)

    buildArtifactDuplicationReport(project, report, bomFile, (!missingArtifacts.isEmpty() || !addedArtifacts.isEmpty()))

    return report.toString()
}

// Projects contain no sources but represent applications used by MNC solution that must have BoM generated for them.
String[] nonJavaBomProjects = [ 'jms', 'nms-admin']

subprojects { Project project ->
    // Only process projects with sources
    // Note for GNSS there is a protobuf project with only generated sources
    // Note for capability provider resources there are only resources provided
    if (project.file('src/main/java').exists() ||
            project.file('src/main/proto').exists() ||
            project.file('src/resources').exists() ||
            nonJavaBomProjects.contains(project.getName())) {
        apply plugin: 'org.cyclonedx.bom'

        ext.versionArtifacts = true

        afterEvaluate {
            // Default every project to use specific group and version when creating BOM
            project.group = groupName
            project.version = Version

            // Setting the version above is needed to include version information in SBOM file generated
            // BUT doing so changes the jar name created for MNC artifacts.
            // When patching the MNC jars with the SBOM the jar generated must be named without the version
            // to match the original jar creation when the SBOM build logic is not present.
            // Some projects specifically force the jar file name so these projects do not need to be adjusted.
            // This checks to see if the version information is included in the jar file name and if it is
            // the configuration will be changed so that the version is omitted from the filename.
            if (jar.archiveFileName.get().contains(project.getVersion().toString())) {
                // Omit version information in generated artifacts for historic mnc artifact compatibility.
                // Many scripts and processes rely on jar names without version information.
                jar.archiveFileName = "${jar.archiveBaseName.get()}.jar"
            }
        }

        Map<String, Object> overrides = bomCustomization.get(project.path)

        // Defaults
        List<String> includeConfigsValue = [ 'runtimeClasspath' ]
        List<String> skipProjectsValue = project.subprojects*.name
        String projectTypeValue = 'library'
        String outputNameValue = "$project.name-bom"
        File destinationValue = file("build/reports")

        // Set overrides if configured
        if (overrides != null) {
            if (overrides.get('includeConfigs')) {
                includeConfigsValue = overrides.get('includeConfigs')
            }
            if (overrides.get('skipProjects')) {
                skipProjectsValue = overrides.get('skipProjects')
            }
            if (overrides.get('projectType')) {
                projectTypeValue = overrides.get('projectType')
            }
            if (overrides.get('outputName')) {
                outputNameValue = overrides.get('outputName')
            }
            if (overrides.get('destination')) {
                destinationValue = overrides.get('destination')
            }
        }

        cyclonedxBom {
            // includeConfigs is the list of configuration names to include when generating the BOM (leave empty to include every configuration)
            includeConfigs = includeConfigsValue
            // skipConfigs is a list of configuration names to exclude when generating the BOM
            // skipConfigs = [ "testCompileClasspath", "testFixturesImplementation", "testFixturesApi", "testFixturesCompileClasspath" ]
            // skipProjects is a list of project names to exclude when generating the BOM
            skipProjects = skipProjectsValue
            // Specified the type of project being built. Defaults to 'library'
            projectType = projectTypeValue
            // Specified the version of the CycloneDX specification to use. Defaults to '1.5'
            // schemaVersion = "1.5"
            // Boms destination directory. Defaults to 'build/reports'
            destination = destinationValue
            // The file name for the generated BOMs (before the file format suffix). Defaults to 'bom'
            outputName = outputNameValue
            // The file format generated, can be xml, json or all for generating both. Defaults to 'all'
            outputFormat = "json"
            // Exclude BOM Serial Number. Defaults to 'true'
            includeBomSerialNumber = false
            // Exclude License Text. Defaults to 'true'
            // includeLicenseText = false
            // Override component version. Defaults to the project version
            componentVersion = Version

            afterEvaluate {
                // Inject custom Adtran license data
                def attachmentText = newCyclonedxAttachmentText()
                attachmentText.setText(licenseText)
                def license = newCyclonedxLicense()
                license.setName(licenseName)
                license.setLicenseText(attachmentText)
                license.setUrl(licenseUrl)
                licenseChoice { lc ->
                    lc.addLicense(license)
                }
                organizationalEntity { entity ->
                    entity.name = organizationName
                    entity.url = [ organizationUrl ]
                }
            }
        }

        tasks.register('enhanceBom') {
            group = 'project-module'
            description = 'Enhance the generated BOM to include additional details for Adtran software'

            mustRunAfter('cyclonedxBom')

            doFirst {
                fileTree(projectDir) { include('build/**/*-bom.json') }.getFiles().each { File bomFile ->
                    logger.quiet("Enhancing BOM ", bomFile.name)

                    def jsonSlurper = new JsonSlurper()
                    Map<String, Object> bomMap = jsonSlurper.parse(bomFile)

                    List<Map<String, Object>> dependencies = bomMap.get("dependencies")
                    // Collect list of Adtran components in dependencies, some may be missing from components list
                    Set<String> adtranPurls = new HashSet<>()
                    for (Map<String, Object> item : dependencies) {
                        String key = item.get('ref')
                        if (key != null && key.startsWith('pkg:maven/com.adtran.enc/')) {
                            adtranPurls.add(key.toString())
                        }
                    }

                    List<Map<String, Object>> components = bomMap.get("components")

                    for (Map<String, Object> component : components) {
                        if (component.get('group').equals(groupName)) {
                            // LoadPress does not like using 'pkg:maven/' for identifying local artifacts
                            // since they can't be found on extern maven repository, so change to 'pkg:generic/'
                            // NOTE: the PURL format is used for references as well, but these references are not changed.
                            String purl = component.get('purl')
                            adtranPurls.remove(component.get('bom-ref').toString())
                            component.put('purl', purl.replace('pkg:maven/', 'pkg:generic/'))
                            component.put('licenses', adtranLicense)
                        } else {
                            // Loadpress wants license URL's to start with HTTP protocol
                            // Cyclonedx gradle plugin may leave this out for some licenses so add it in if missing
                            List<Map<String, Object>> licenses = component.get('licenses')
                            for (Map<String, Object> license : licenses) {
                                Map<String, Object> l = license.get('license')
                                if (l != null) {
                                    String url = l.get('url')
                                    if (url != null) {
                                        List<List<String>> match = (url =~/^https?\:\/\//).findAll()
                                        if (match.size() == 0) {
                                            // URL does not start with "https://" or "http://", so add "http://"
                                            l.put('url', "http://$url")
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (components == null || components.isEmpty()) {
                        components = []
                        bomMap.put("components", components)
                    }

                    // Add into components local artifacts that are included in dependencies but missing in components
                    for (String purlString : adtranPurls) {
                        List<List<String>> match = (purlString =~/pkg\:maven\/([^\/]+)\/([^@]+)@([^\?]+)\?(.*)/).findAll()
                        if (match.size() == 1 && ((List) match[0]).size() >= 4) {
                            String groupString = match[0][1]
                            String nameString = match[0][2]
                            String versionString = match[0][3]
                            String remainder = match[0][4]

                            Map<String, Object> component = [
                                    group: groupString,
                                    name: nameString,
                                    version: versionString,
                                    type: 'library',
                                    purl: purlString,
                                    'bom-ref': purlString,
                                    licenses: adtranLicense,
                            ] as Map

                            components.add(component)
                        }
                    }

                    File patch = project.file('bom-patch.json')
                    if (patch.exists()) {
                        Map<String, Object> patchMap = jsonSlurper.parse(patch)
                        // Currently only support addition of missing components
                        List<Map<String, Object>> patchComponents = patchMap.get('components')
                        if (patchComponents != null) {
                            components.addAll(patchComponents)
                        }
                    }

                    // Write out changes
                    bomFile.text = JsonOutput.prettyPrint(JsonOutput.toJson(bomMap))
                }
            }
        }

        // Remove file before running to accumulate results
        reportFile.delete()

        /**
         * Compare artifacts listed in BOM file to runtime classpath for the gradle project.
         */
        tasks.register('validateBom') {
            group = 'project-module'
            description = 'Validate BOM generated for consistency against runtime classpath and for duplicate artifacts'

            mustRunAfter('cyclonedxBom')
            mustRunAfter('enhanceBom')

            doFirst {
                fileTree(projectDir) { include('build/**/*-bom.json') }.getFiles().each { File bomFile ->
                    logger.quiet("Running BOM Validation on {}", bomFile.name)

                    String report = buildBomValidationReport(getProject(), bomFile, includeConfigsValue)

                    if (!report.isEmpty()) {
                        logger.quiet(report.toString())
                        if (reportFile.exists()) {
                            reportFile << '\n\n====================\n\n' << report
                        } else {
                            reportFile.getParentFile().mkdirs()
                            reportFile.text = report
                        }

                        if (failBuildOnInvalidBom) {
                            throw new GradleException("BOM validation failed, see "+reportFile)
                        }
                    } else {
                        logger.quiet("No issues found in {} BOM", project)
                    }
                }
            }
        }

        tasks.register('patchJarWithBom', Zip) {
            // Must run after SBOM is created and validated (if run concurrently)
            mustRunAfter('validateBom')

            // Requires project jar is present.
            dependsOn('jar')

            // Property name the jar to make it follow standard jar convention and make it unique
            destinationDirectory = project.layout.buildDirectory.file('patched').get().asFile
            if (project.path.startsWith(":modules:provision:")) {
                // Provision artifact names traditionally include version
                archiveFileName = "${jar.archiveBaseName.get()}-${project.version}.jar"
            } else {
                // MNC artifact names traditionally do not include version
                doFirst {
                    archiveFileName = "${jar.archiveFileName.get()}"
                }
            }

            // Include the original jar content.
            from (zipTree(jar.archiveFile))

            from (fileTree(project.layout.buildDirectory.file('reports').get().asFile.getCanonicalPath())) {
                // Only care about bom file, this directory can contain other files and directories
                include("**/*-bom.json")
                // Include in META-INF directory of jar
                into("META-INF/")
                // rename the file to just 'bom.json'
                rename { String name -> 'bom.json' }
            }
        }
    }
}
