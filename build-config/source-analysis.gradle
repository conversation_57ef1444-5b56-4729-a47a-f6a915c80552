/*
 *   Copyright 2023 Adtran Networks SE. All rights reserved.
 */

layout.buildDirectory.set(file('build-gradle'))

apply plugin: 'com.adva.gradle.plugin.source-analyzer'
apply plugin: 'com.adva.gradle.plugin.image-publisher'

afterEvaluate {
    task compressSourceAnalysis(type: Zip) {
        archiveFileName = "source-analysis.zip"
        destinationDirectory = layout.buildDirectory.get().asFile
        from (layout.buildDirectory.file("source.analysis"))
    }

    String publicationTargetName = 'publication-target'
    String baseSourceAnalysisInstallDir = "/opt/ftp/enc/architecture"
    String sourceAnalysisInstallDir = "$baseSourceAnalysisInstallDir/$Version"

    task publishSourceAnalysisToProductionFtp (type: IMAGE_PUBLISHER_TASK_CLASS) {
        dependsOn(compressSourceAnalysis)

        target = publicationTargetName
        destination = sourceAnalysisInstallDir
        updateSymlink = false
        segmentByDay = false
        cleanup = true
        extractZip = true
        files {
            layout.buildDirectory.file("source-analysis.zip").get().asFile
        }
    }

    task configureProductionFtp {
        dependsOn publishSourceAnalysisToProductionFtp

        doLast {
            String result = "git symbolic-ref --short HEAD".execute().text.trim()
            print("Source analysis was run on branch $result\n")
            // Only update the 'current' symlink when building on master branch
            if ("master".equals(result)) {
                // Use the setup from image publisher plugin to tweak the install of the source analysis
                print("Attempting to create source analysis symlink ...\n")
                ssh.run {
                    session(remotes.role(publicationTargetName)) {
                        String output = execute "ln -sf \"$sourceAnalysisInstallDir\" \"$baseSourceAnalysisInstallDir/current\""
                        print("Created symlink from install to current ($output)\n")
                    }
                }
            }
        }
    }

    task publishSourceAnalysis() {
        dependsOn(configureProductionFtp)
    }
}

// Configuration for gradle source analysis plugin
// To run analysis use sourceAnalysis gradle task
analysis {
    // Generate report on package dependencies
    packageReport = true
    // Generate report on project dependencies
    projectReport = true
    // Generate report on component dependencies
    componentReport = true

    // Generate text based report files
    outputText = false
    // Generate local HTML report files
    outputHtml = true

    errorLevel = "errors"

    afterEvaluate {
        outputDirectory = layout.buildDirectory.file('source.analysis').get().asFile
    }

    // Define component and layer names
    String comp_Approval_Proxy = "Approval Proxy"
    String comp_Common_Utilities = "Common Utilities"
    String comp_Common_Types = "Common Types"
    String comp_ALM_Device_Driver = "ALM Device Driver"
    String comp_AOS_Device_Driver = "AOS Device Driver"
    String comp_Bandwidth_Manager_API = "Bandwidth Manager API"
    String comp_Bandwidth_Manager = "Bandwidth Manager"
    String comp_Bookmark_API = "Bookmark API"
    String comp_Bookmarks = "Bookmarks"
    String comp_Create_User_Action = "Create User Action"
    String comp_Documentation = "Documentation"
    String comp_Paging_API = "Paging API"
    String comp_Paging_Framework = "Paging Framework"
    String comp_Capabilities_Registry_API = "Capabilities Registry API"
    String comp_Capabilities_Registry = "Capabilities Registry"
    String comp_Client_Updater_Endpoint = "Client Updater Endpoint"
    String comp_Client_Updater = "Client Updater"
    String comp_GUI_User_Data_Endpoint = "GUI User Data Endpoint"
    String comp_Capabilities_Registry_Endpoint = "Capabilities Registry Endpoint"
    String comp_Driver_Manager_Endpoint = "Driver Manager Endpoint"
    String comp_NTP = "NTP"
    String comp_REST_Infrastructure = "REST Infrastructure"
    String comp_Jetty_Infrastructure = "Jetty Infrastructure"
    String comp_Custom_Device_Driver = "Custom Device Driver"
    String comp_Database_Consolodation = "Database Consolodation"
    String comp_Common_Driver_REST_API = "Common Driver REST API"
    String comp_Core_Driver_API = "Core Driver API"
    String comp_Device_Inventory_Driver_API = "Device Inventory API"
    String comp_Ethernet_Driver_API = "Ethernet API"
    String comp_Ethernet_Delegate = "Ethernet Delegate"
    String comp_Notification_Driver_API = "Notification API"
    String comp_Housekeeping_Driver_API = "Housekeeping API"
    String comp_Optical_Router_Driver_Driver_API = "Optical Router Driver API"
    String comp_Registry_Driver_API = "Registry API"
    String comp_Driver_Delegate_Core = "Core Delegate"
    String comp_Housekeeping_Delegate = "Housekeeping Delegate"
    String comp_License_Converter_Delegate = "License Converter Delegate"
    String comp_Resource_Mediator_Delegate = "Resource Mediator Delegate"
    String comp_Core_Driver = "Core Driver"
    String comp_Legacy_Driver = "Legacy Driver"
    String comp_Juniper_Driver = "Juniper Driver"
    String comp_F3_Driver = "F3 Driver"
    String comp_F4_Driver = "F4 Driver"
    String comp_F7_Driver = "F7 Driver"
    String comp_F8_Driver = "F8 Driver"
    String comp_OSA_Driver = "OSA Driver"
    String comp_Third_Party_Monitor_Driver = "Third Party Monitor Driver"
    String comp_FSP1500_Driver = "FSP1500 Driver"
    String comp_Optical_Router_Adapter = "Optical Router Adapter"
    String comp_Optical_Router_Driver_API = "Optical Router API"
    String comp_Optical_Router_Driver_Impl = "Optical Router Impl"
    String comp_Optical_Router_Driver_Kafka_Adapter = "Optical Router Kafka Adapater"
    String comp_Optical_Router_Driver_Persistence = "Optical Router Persistence"
    String comp_Optical_Router_Driver_Rest_Client_Adapter = "Optical Router Rest Client"
    String comp_Optical_Router_Driver_Rest_Server_Adapter = "Optical Router Rest Server"
    String comp_Juniper_Ptx_App = "Juniper Ptx App"
    String comp_Optical_Router_Test_Driver = "Test Driver"
    String comp_Driver_Registry = "Driver Registry"
    String comp_Driver_Notification = "Driver Notification"
    String comp_Device_Driver = "Device Driver"
    String comp_Device_Inventory_API = "Device Inventory API"
    String comp_Device_Inventory = "Device Inventory"
    String comp_Ethernet_Rings = "Ethernet Rings"
    String comp_External_Tools = "External Tools"
    String comp_ENC_Utils = "ENC Utilities"
    String comp_Kafka_Utils = "Kafka Utilities"
    String comp_F3_Device_Driver = "F3 Device Driver"
    String comp_F4_Device_Driver = "F4 Device Driver"
    String comp_F7_Device_Driver = "F7 Device Driver"
    String comp_F8_Device_Driver = "F8 Device Driver"
    String comp_Resource_Advertiser_API = "Resource Advertiser API"
    String comp_Resource_Mediator_API = "Resource Mediator API"
    String comp_Resource_Mediator_Impl = "Resource Mediator Implementation"
    String comp_Resource_Advertiser_Delegate = "Resource Advertiser Delegate"
    String comp_Resource_Advertiser_Common = "Resource Advertiser Common"
    String comp_F7_Resource_Advertiser = "F7 Resource Advertiser"
    String comp_F8_Resource_Advertiser = "F8 Resource Advertiser"
    String comp_Resource_Mediator_Provision_API = "Resource Request Mediator Provision API"
    String comp_MRV_Driver = "MRV Driver"
    // Legacy interface added in 'interfaces' section of diagram.
    // Fault manager also has a new style API defined as part of the component.
    String comp_Fault_Management_API = "Fault Management Interface"
    String comp_Fault_Management = "Fault Management"
    String comp_Crypto_Manager_API = " Crypto Manager API"
    String comp_Fiber_Assurance_API = "Fiber Assurance API"
    String comp_Fiber_Assurance = "Fiber Assurance"
    String comp_Fiber_Director_Core = "Fiber Director Core"
    String comp_Fiber_Director_App = "Fiber Director App"
    String comp_Frontend_Client = "Frontend Client"
    String comp_Custom_Widgets = "Custom Widgets"
    String comp_Frontend_Workbench_API = "Workbench Framework API"
    String comp_Frontend_Workbench = "Workbench Framework"
    String comp_FSP1500_Device_Driver = "FSP1500 Device Driver"
    String comp_FSP150CC_Device_Driver = "FSP150CC Device Driver"
    String comp_FSP150cm_Device_Driver = "FSP150cm Device Driver"
    String comp_FSP150cp_Device_Driver = "FSP150cp Device Driver"
    String comp_FSP150egm_Device_Driver = "FSP150egm Device Driver"
    String comp_FSP150egx_Device_Driver = "FSP150egx Device Driver"
    String comp_FSP1XX_Device_Driver = "FSP1XX Device Driver"
    String comp_FSP20X_Device_Driver = "FSP20X Device Driver"
    String comp_FSP210_Device_Driver = "FSP210 Device Driver"
    String comp_FSP_ea_Device_Driver = "Mosiac Activator"
    String comp_FSP_xg1xx_Device_Driver = "FSP_xg1xx Device Driver"
    String comp_FSP_xg_mrv_Device_Driver = "FSP_xg_mrv Device Driver"
    String comp_FSP_z4806_Device_Driver = "FSP_z4806 Device Driver"
    String comp_FSPsyncprob_Device_Driver = "FSPsyncprobe Device Driver"
    String comp_FSPTxx04_Device_Driver = "FSPTxx04 Device Driver"
    String comp_Health_Endpoint = "Health Center Endpoint"
    String comp_Health_API = "Health API"
    String comp_Health = "Health Center"
    String comp_Hatteras_Device_Driver = "Hatteras Device Driver"
    String comp_House_Keeping_API = "House Keeping API"
    String comp_House_Keeping = "House Keeping"
    String comp_HTTP_Proxy = "HTTP Proxy"
    String comp_Integration_Test = "Integration Test"
    String comp_Juniper_Device_Driver = "Juniper Device Driver"
    String comp_Licensing = "Licensing"
    String comp_Logging = "Logging"
    String comp_MTOSI_NBI = "MTOSI Agent"
    String comp_Network_Intelligence = "Network Intelligence"
    String comp_NI_Configuration = "NI Configuration"
    String comp_NI_Config_API = "NI Config API"
    String comp_NI_Links = "NI Links"
    String comp_NI_Nodes = "NI Nodes"
    String comp_NI_Nodes_System = "NI Nodes System"
    String comp_Control_Plane_App = "Control Plane App"
    String comp_Control_Plane_Impl = "Control Plane Impl"
    String comp_NI_Controller_Config_Provider = "NI Controller Config Provider"
    String comp_Control_Plane_Persistence = "Control Plane Persistence"
    String comp_Control_Plane_Messaging = "Control Plane Messaging"
    String comp_Control_Plane_Rest = "Control Plane Rest"
    String comp_Control_Plane_NI_Adapter = "NI Adapter"
    String comp_Path_Computation_Engine_API = "PCE API"
    String comp_Service_Implementation_Engine_API = "SIE API"
    String comp_CPC_Manager_API = "CPC Manager API"
    String comp_CPC_Manager_Rest_Adapter = "CPC Manager Rest Adapter"
    String comp_Node_Translation_Client = "Node Translation Client"
    String comp_Network_Resource_Locator = "Network Resource Locator"
    String comp_Notification_API = "Notification API"
    String comp_notification_core = "Notification Core"
    String comp_notification_authorization = "Notification Authorization"
    String comp_concurrent_tracing = "Concurrent Tracing"
    String comp_notification_tracing = "Notification Tracing"
    String comp_REST_Communication = "REST Communication"
    String comp_Optical_Parameters = "Optical Parameters"
    String comp_Async_Operation_Library = "Async Operation Library"
    String comp_Common_Definition_Library = "Common Definition Library"
    String comp_Notifications_Library = "Notifications Library"
    String comp_Transactional_Provisioning_Library = "Transactional Provisioning Library"
    String comp_Postman_Utils = "Postman Utils Lib"
    String comp_OSA3230b_Device_Driver = "OSA3230b Device Driver"
    String comp_OSA3350_Device_Driver = "OSA3350 Device Driver"
    String comp_OSA3351_Device_Driver = "OSA3351 Device Driver"
    String comp_OSA5331_Device_Driver = "OSA5331 Device Driver"
    String comp_OSA540X_Device_Driver = "OSA540X Device Driver"
    String comp_OSA542x_Device_Driver = "OSA542x Device Driver"
    String comp_OSA54CR_Device_Driver = "OSA54CR Device Driver"
    String comp_OSA5548c_Device_Driver = "OSA5548c Device Driver"
    String comp_OTS1000_Device_Driver = "OTS1000 Device Driver"
    String comp_Performance_Management_API = "Performance Management API"
    String comp_Performance_Management = "Performance Management"
    String comp_PM = "PM"
    String comp_Alarm_NBI = "Alarm NBI"
    String comp_Persistency_Endpoint = "Persistency Endpoint"
    String comp_Persistency_API = "Persistency API"
    String comp_Persistency = "Persistency"
    String comp_PV_Device_Driver = "PV Device Driver"
    String comp_Report_Endpoint = "Report Endpoint"
    String comp_Report_API = "Report API"
    String comp_Report = "Report"
    String comp_Resilience_API = "Resilience API"
    String comp_Resilience = "Resilience"
    String comp_REST_Device_Driver = "REST Device Driver"
    String comp_SNMP_Common = "SNMP Common"
    String comp_REST_Common = "REST Common"
    String comp_Alarm_Event_NE_Endpoint = "Alarm,Event,NE Endpoint"
    String comp_Scheduling_API = "Scheduling API"
    String comp_Scheduling = "Scheduling"
    String comp_TAPI_Interface = "SDN"
    String comp_Security_API = "Security API"
    String comp_Sabotage_Protection = "Sobotage Protection"
    String comp_Security = "Security"
    String comp_App_Security = "App Security"
    String comp_Server_Backup = "Server Backup"
    String comp_Streaming_Telemetry_Client = "Streaming Telemetry Client"
    String comp_Bean_Management = "Bean Management"
    String comp_Server_State = "Server State"
    String comp_Server_UI_Framework_API = "Server UI Framework API"
    String comp_Server_UI_Framework = "Server UI Framework"
    String comp_Server_Common_Types = "Server Common Types"
    String comp_Server_Common_Utilities = "Server Common Utilities"
    String comp_Concurrency = "Concurrency"
    String comp_XML_Processing = "XML Processing"
    String comp_Server_Modules = "Server Modules"
    String comp_Server_Search = "Server Search"
    String comp_Service_Manager_Endpoint = "Service Manager Endpoint"
    String comp_Service_Manager_API = "Service Manager API"
    String comp_Service_Manager = "Service Manager"
    String comp_Packet_Director_API = "Packet Director API"
    String comp_Packet_Director_Mediation_Adapter = "Packet Director Mediation Adapter"
    String comp_Packet_Director_Inventory = "Packet Director Inventory"
    String comp_Packet_Layer3 = "Packet Layer 3"
    String comp_Packet_Layer3_Common = "Packet Layer 3 Common"
    String comp_Packet_Layer3_REST = "Packet Layer 3 REST Adapter"
    String comp_Packet_Layer3_Mediation = "Packet Layer 3 Mediation Adapter"
    String comp_Packet_Layer3_Persistence = "Packet Layer 3 Persistence Adapter"
    String comp_Network_Resource_Inventory_Manager = "Network Resource Inventory Manager"
    String comp_Capability_File_Generator = "Capability File Generator"
    String comp_Capabilities_Broker = "Capability Broker"
    String comp_Capabilities_Broker_REST_Server = "Capability Broker REST Server"
    String comp_Capabilities_Broker_REST_Client = "Capability Broker REST Client"
    String comp_Capabilities_Provider = "Capability Provider"
    String comp_Capabilities_Provider_App = "Capability Provider App"
    String comp_Capabilities_Provider_Adapters = "Capability Provider Adapters"
    String comp_Capabilities_Provider_Properties = "Capability Provider Properties"
    String comp_EOD_ENC_Client = "EOD ENC Client"
    String comp_Connectivity_Service_Manager = "Connectivity Service Manager"
    String comp_Provisioning_Orchestrator = "Provisioning Orchestrator"
    String comp_Provisioning_Orchestrator_REST_Server = "Provisioning Orchestrator REST Server"
    String comp_Provisioning_Orchestrator_Messaging = "Provisioning Orchestrator Messaging"
    String comp_Service_Topology_API = "Service Topology API"
    String comp_Service_Topology = "Service Topology"
    String comp_Service_Synchronizer_App = "Service Synchronizer App"
    String comp_Service_Synchronizer = "Service Synchronizer"
    String comp_Service_Synchronizer_Messaging = "Service Synchronizer Messaging"
    String comp_Service_Synchronizer_Persistence = "Service Synchronizer Persistence"
    String comp_Service_Synchronizer_REST_Client = "Service Synchronizer REST Client"
    String comp_Service_Synchronizer_REST_Server = "Service Synchronizer REST Server"

    String comp_ntp = "Network Time Protocol"
    String comp_ntp_rest_service = "NTP REST"

    String comp_Service_Migration_Tool_App = "Service Migration Tool App"
    String comp_Service_Migration_Tool = "Service Migration Tool"
    String comp_Service_Migration_Tool_REST_Client = "Service Migration Tool REST Client"
    String comp_Service_Migration_Tool_REST_Server = "Service Migration Tool REST Server"
    String comp_Service_Migration_Tool_Messaging = "Service Migration Tool Messaging"
    String comp_Service_Migration_Tool_Persistence = "Service Migration Tool Peristence"
    String comp_SMT_NI_Mediator_API = "SMT NI Mediator API"
    String comp_SMT_NI_Mediator_REST_Client = "SMT NI Mediator REST Client"
    String comp_SMT_NI_Mediator_REST_Server = "SMT NI Mediator REST Server"
    String comp_Job_Manager = "Job Manager"
    String comp_Notification_Manager = "Notification Manager"
    String comp_SNMP_Device_Driver = "SNMP Device Driver"
    String comp_SNMP_Profile_Endpoint = "SNMP Profile Endpoint"
    String comp_SNMP_Profile_API = "SNMP Profile API"
    String comp_Status_Messenger = "Status Messenger"
    String comp_NE_Communication_Service = "NE Communication Service"
    String comp_Symmetricom_Device_Driver = "Symmetricom Device Driver"
    String comp_Synchronization_Manager = "Synchronization Manager"
    String comp_Synchronization_Assurance_Interface = "Synchronization Assurance Interface"
    String comp_gnss_collector = "GNSS Collector"
    String comp_gnss_common_enc_mtls = "GNSS Common MNC MTLS"
    String comp_gnss_common_grpc = "GNSS Common GRPC"
    String comp_gnss_cli_worker = "GNSS CLI Worker"
    String comp_gnss_data_access = "GNSS Data Access"
    String comp_gnss_machine_learning = "GNSS Machine Learning"
    String comp_gnss_rca_analysis = "GNSS RCA Analysis"
    String comp_gnss_snt_data_access = "GNSS SNT Data Access"
    String comp_gnss_snt_gnmi_collector = "GNSS SNT GNMI Collector"
    String comp_gnss_tpa_collector = "GNSS TPA Collector"
    String comp_gnss_tpa_data_access = "GNSS TPA Data Access"
    String comp_gnss_tpa_online_qm = "GNSS TPA Online QM"
    String comp_gnss_guard_ml = "GNSS Guard ML"
    String comp_Test = "Test"
    String comp_Test_Framework = "Test Framework"
    String comp_Topology_Manager = "Topology Manager"
    String comp_Topology_Manager_Rest_Service = "Topology Manager REST"
    String comp_Unmanaged_Device_Driver = "Unmanaged Device Driver"
    String comp_User_Profile = "User Profile"
    String comp_YPDB = "YPDB"
    String comp_Planner_Export = "Planner Export"
    String comp_DEAD = "DEAD"

    // Top level diagram layers
    String layer_shared = "Shared"
    String layer_interfaces = "Interfaces"
    String layer_support = "Support Applications"
    String layer_basic_applications = "Core Applications"
    String layer_advanced_applications = "Advanced Applications"
    String layer_device_drivers = "Drivers"
    String layer_device_communication = "Device Communication"
    String layer_infrastructure = "Infrastructure"
    String layer_device_driver_api = "Device Driver API"
    String layer_device_driver_support = "Device Driver Support"
    String layer_device_driver_delegates = "Device Driver Delegates"
    String layer_device_driver_families = "Device Driver Families"
    String layer_driver_core = "Core Driver"

    String layer_frontend_api = "Server"
    String layer_capability_file_generator = "Capability File Generator"
    String layer_frontend = "Java Client"
    String layer_client_updater = "Client Launcher"

    String layer_packet_director = "Mosiac Packet Director"
    String layer_packet_layer3 = "Mosiac Packet Layer3"
    String layer_control_plane = "Control Plane"
    String layer_cpc_manager = "CPC Manager"
    String layer_resource_advertiser = "Resource Advertiser"
    String layer_resource_mediator = "F8 Resource Mediator"
    String layer_capability_provider = "Capability Provider"
    String layer_capability_broker = "Capability Broker"
    String layer_provisioning_orchestrator = "Provisioning Orchestrator"
    String layer_mosiac_optical_director = "Mosiac Optical Director"
    String layer_service_synchronizer = "Service Synchronizer"
    String layer_service_migration_tool = "Service Migration Tool"
    String layer_service_migration_tool_ni_mediator = "Service Migration Tool NI Mediator"
    String layer_mosiac_fiber_director = "Mosiac Fiber Director"
    String layer_mosaic_sync_director = "Mosiac Sync Director"

    String layer_ntp = "Network Time Protocol"

    String layer_topology_manager = "Topology Manager"

    // Client Interface diagram layers
    String layer_rest_endpoints = "REST Endpoints"
    String layer_general_apis = "General APIs"
    String layer_support_apis = "Support APIs"
    String layer_basic_application_apis = "Core App APIs"
    String layer_advanced_application_apis = "Advanced App APIs"
    String layer_infrastructure_apis = "Infrastructure APIs"

    // Server Infrastrcuture diagram
    String layer_server_utilities = "Server Utilities"

    // Service manager diagram layers
    String layer_sm_editing = "Service Editing"
    String layer_sm_inventory = "Service Inventory"
    String layer_sm_network_intelligence = "Network Intelligence"

    String layer_legacy_driver = "Legacy Drivers"
    String layer_third_party_driver = "3rd party Drivers"
    String layer_osa_driver = "OSA Drivers"
    String layer_aos_driver = "AOS Drivers"
    String layer_f3_driver = "F3 Drivers"
    String layer_pv_driver = "PV Drivers"

    String layer_web_services = "Web Services"
    String layer_performance_management = "Performance Management"

    String layer_process_components = "Process Components"

    // Optical libraries layer
    String layer_optical_libraries = "Optical Libraries"

    String layer_notification = "Notification"
    String layer_communication_libraries = "Communication Libraries"

    String layer_driver_adapters = "Driver Adapters"
    String layer_optical_router_driver = "Optical Router"
    String layer_optical_router_driver_applications = "Optical Router Applications"

    // Top level diagram
    String diagram_enc_server = "ENC System Component Diagram"
    String diagram_enc_client = "ENC Client Diagram"
    String diagram_enc_all_components = "ENC All Components Diagram"

    // Client interface diagram, contains components related to communication between client and server
    String diagram_client_interface = "Client Interface"

    // Capabilities file generator diagram - tool for generation of capability files used in production
    String diagram_capability_file_generator = "Capability File Generator"

    // Server infrastructure diagram showing services available
    String diagram_server_infrastructure = "Server Infrastructure"

    // Driver sub-diagrams
    String diagram_legacy_driver = "Legacy Driver Diagram"
    String diagram_third_party_driver = "Third Party Driver Diagram"
    String diagram_osa_driver = "OSA Driver Diagram"
    String diagram_aos_driver = "AOS Driver Diagram"
    String diagram_f3_driver = "F3 Driver Diagram"
    String diagram_pv_driver = "PV Driver Diagram"
    String diagram_web_services = "Web Services Diagram"
    String diagram_driver_core = "Core Driver Diagram"
    String diagram_device_driver = "Device Driver API & Framework"
    String diagram_performance_management = "Performance Management Diagram"

    // Application diagrams
    String diagram_service_manager = "Service Manager Diagram"
    String diagram_packet_director = "Mosiac Packet Director"
    String diagram_packet_layer3 = "Mosiac Packet Layer3"
    String diagram_control_plane = "Control plane Diagram"
    String diagram_cpc_manager = "CPC Manager Diagram"
    String diagram_resource_advertiser = "Resource Advertiser"
    String diagram_resource_mediator = "F8 Resource Mediator"
    String diagram_capability_provider = "Capability Provider Diagram"
    String diagram_capability_broker = "Capability Broker Diagram"
    String diagram_provisioning_orchestrator = "Provisioning Orchestrator Diagram"
    String diagram_mosaic_optical_director = "Mosiac Optical Director"
    String diagram_service_synchronizer = "Service Synchronizer Diagram"
    String diagram_service_migration_tool = " Service Migration Tool Diagram"
    String diagram_service_migration_tool_ni_mediator = "Service Migration Tool NI Mediator Diagram Diagram"
    String diagram_mosaic_fiber_director = "Mosiac Fiber Director"
    String diagram_mosaic_sync_director = "Mosiac Sync Director"

    String diagram_ntp = "Network Time Protocol Diagram"

    String diagram_topology_manager = "Topology Manager Diagram"

    String diagram_notification = "Notification diagram"
    String diagram_communication_libraries = "Communication Libraries"
    String diagram_optical_libraries = "Optical Libraries"

    String diagram_optical_router_driver = "Optical Router Driver"
    String diagram_optical_router_driver_applications = "Optical Router Driver Applications"

    component {
        name = comp_Approval_Proxy
        rulePath 'layers/infrastructure/approval_proxy'
    }

    component {
        name = comp_Common_Types
        ruleName 'com.adva.common.util.exception'
        ruleName 'com.adva.common.util.lang'
        ruleName 'com.adva.common.util.net'
        ruleName 'com.adva.nlms.common.exception'
        ruleName 'com.adva.nlms.common.unsupne'
        ruleName 'com.adva.nlms.common.webhelp'
    }

    component {
        name = comp_Common_Utilities
        ruleName 'com.adva.common.util'
        ruleName 'com.adva.common.util.io'
        rulePrefix 'com.adva.common.util.collect'
        ruleName 'com.adva.nlms.common.file'
        ruleName 'com.adva.nlms.common.property'
        ruleName 'com.adva.nlms.common.model.topology'
        rulePrefix 'com.adva.nlms.common.util'
        ruleName 'com.adva.nlms.common.jms'
    }

    component {
        name = comp_House_Keeping_API
        rulePrefix 'com.adva.nlms.common.nebackup'
        rulePrefix 'com.adva.nlms.common.housekeeping'
        ruleName 'com.adva.nlms.common.swupgrade'
    }

    component {
        name = comp_House_Keeping
        rulePrefix 'com.adva.nlms.mediation.housekeeping'
        rulePrefix 'com.adva.nlms.mediation.common.housekeeping'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.swVersion'
        ruleName 'com.adva.nlms.mediation.util'
    }

    component {
        name = comp_Persistency_Endpoint
        ruleName 'com.adva.nlms.mediation.da'
    }

    component {
        name = comp_Persistency_API
        ruleName 'com.adva.nlms.common.da'
    }

    // Rules that map packages to system components for analysis purposes
    component {
        name = comp_Persistency
        rulePrefix 'com.adva.nlms.mediation.common.transactions'
        rulePrefix 'com.adva.nlms.inf.api.persistentobject'
        // PV DB connection provider
        rulePrefix 'com.adva.nlms.pv.db'
        // WEASEL: Logic to ensure DB data is consistent. For NE data but should it be in persistency???
        rulePrefix 'com.adva.nlms.mediation.config.dbconsistency'
        // Relational mapping logic
        rulePrefix 'com.adva.nlms.mediation.config.persistence'
        rulePrefix 'com.adva.nlms.mediation.server.upgrade'
        rulePrefix 'com.adva.nlms.mediation.common.persistence'
    }

    component {
        name = comp_Notification_API
        rulePrefix 'com.adva.nlms.common.notification'
        rulePrefix 'com.adva.nlms.common.messages'
        rulePrefix 'com.adva.nlms.common.messaging.inf'
    }

    component {
        name = comp_notification_core
        rulePrefix 'com.adva.nlms.inf.api.notification'
        ruleName 'com.adva.nlms.inf.api'
        rulePrefix 'com.adva.nlms.mediation.messaging'
        rulePrefix 'com.adva.nlms.inf.impl'
        rulePrefix 'com.adva.nlms.inf.mo'
        rulePrefix 'com.adva.nlms.mediation.config.moobservers'
        rulePrefix 'com.adva.nlms.mediation.notifications'
        rulePrefix 'com.adva.nlms.mediation.usernotification'
        rulePath 'layers/infrastructure/notification'
    }

    component {
        name = comp_concurrent_tracing
        rulePath 'layers/infrastructure/concurrent/tracing'
    }

    component {
        name = comp_notification_authorization
        rulePath 'layers/infrastructure/notification/authorize_headers'
    }

    component {
        name = comp_notification_tracing
        rulePath 'external', 'layers/infrastructure/notification/tracing/api'
        rulePath 'internal', 'layers/infrastructure/notification/tracing/impl'
    }

    component {
        name = comp_REST_Communication
        rulePrefix 'com.adva.nlms.mediation.common.eod'
        rulePath 'libraries/rest-common'
        rulePath 'layers/infrastructure/rest/tracing'
        rulePathPrefix 'layers/infrastructure/rest_communication'
    }

    component {
        name = comp_Client_Updater_Endpoint
        rule { include prefix: 'com.adva.nlms.mediation.clientUpdate' }
    }

    component {
        name = comp_Client_Updater
        rule { include prefix: 'com.adva.nlms.clientupdater' }
    }

    component {
        name = comp_Custom_Widgets
        rulePrefix 'com.adva.common.awt'
    }

    component {
        name = comp_Frontend_Workbench_API
        ruleName 'com.adva.common.workbench.binding.model'
        ruleName 'com.adva.common.workbench.message.action'
        ruleName 'com.adva.common.workbenchfx.util'
        ruleName 'com.adva.nlms.frontendfx.workbenchfx.formviewer'
    }

    component {
        name = comp_Frontend_Workbench
        rule {
            include prefix: 'com.adva.common.workbench'
            exclude name: 'com.adva.common.workbench.binding.model'
            exclude name: 'com.adva.common.workbench.message.action'
            exclude name: 'com.adva.common.workbenchfx.util'
        }
        ruleName 'com.adva.nlms.common.gui'
    }

    component {
        name = comp_Frontend_Client
        rule {
            include prefix: 'com.adva.nlms.frontend'
            exclude name: 'com.adva.nlms.frontendfx.workbenchfx.formviewer'
        }

        // These items are in nmscommon but are only used in frontend
        ruleName 'com.adva.common.util.icon'
        ruleName 'com.adva.common.util.lifecycle'
        ruleName 'com.adva.common.util.service'
    }

    component {
        name = comp_GUI_User_Data_Endpoint
        ruleName 'com.adva.nlms.mediation.guidata'
    }

    component {
        name = comp_Capabilities_Registry_Endpoint
        ruleName 'com.adva.nlms.mediation.config.capability'
    }

    component {
        name = comp_Driver_Manager_Endpoint
        ruleName 'com.adva.nlms.mediation.config.driver.rest'
        ruleName 'com.adva.nlms.common.driver'
    }

    component {
        name = comp_NTP
        ruleName 'com.adva.nlms.common.ntp'
    }

    component {
        name = comp_Fault_Management_API
        ruleName 'com.adva.nlms.common.traps'
        rulePrefix 'com.adva.nlms.common.event'
        rulePrefix 'com.adva.nlms.common.sound'
    }

    component {
        name = comp_Fault_Management
        rulePrefix 'internal', 'com.adva.nlms.mediation.event'
        rulePrefix 'internal', 'com.adva.nlms.mediation.ne_comm.kap'
        rulePrefix 'internal', 'com.adva.nlms.mediation.config.arc'
        rulePrefix 'internal', 'com.adva.nlms.mediation.sound'

        rulePath "external", "layers/apps/core/fm/api"
        rulePath 'external', 'layers/apps/core/fm/adapters/rest/client'
    }

    component {
        name = comp_Health_Endpoint
        rulePrefix 'com.adva.nlms.mediation.health.resource'
    }

    component {
        name = comp_Health_API
        rulePrefix "com.adva.nlms.common.health"
    }

    component {
        name = comp_Health
        rulePath "external", "modules/mediation/health_center/api"
        rule {
            scope 'internal'
            include prefix: 'com.adva.nlms.mediation.health'
            exclude prefix: 'com.adva.nlms.mediation.health.resource'
        }
        // Task to monitor disk space available/used
        rulePrefix 'external', 'com.adva.nlms.mediation.server.disk.monitoring'
        // JMS api for making data externally visible
        rulePrefix 'external', 'com.adva.nlms.mediation.server.monitoring'
    }

    component {
        name = comp_MTOSI_NBI
        rulePrefix 'com.adva.nlms.mediation.mtosi'
        rulePrefix 'com.adva.nlms.common.mtosisupport'
        rulePrefix 'com.adva.nlms.mediation.config.sr'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.sr'
        rulePrefix 'com.adva.nlms.common.cxfrealm'
        rulePrefix 'com.adva.nlms.mediation.config.mtosi'
    }

    component {
        name = comp_Alarm_Event_NE_Endpoint
        rule { include prefix: 'com.adva.nlms.mediation.restnbi' }
    }

    component {
        name = comp_NI_Configuration
        rulePath "modules/ni/ni-config"
    }

    component {
        name = comp_NI_Config_API
        rulePath "modules/ni/ni-config-api"
    }

    component {
        name = comp_NI_Links
        rulePath "modules/ni/ni-links"
    }

    component {
        name = comp_NI_Nodes
        rulePath 'external', 'modules/ni/ni-nodes/api'
        rulePath 'internal', 'modules/ni/ni-nodes/impl'
    }

    component {
        name = comp_NI_Nodes_System
        rulePath 'external', 'modules/ni/nodes-system/api'
        rulePath 'internal', 'modules/ni/nodes-system/impl'
    }

    // Catch all for NI packages found in mediation
    component {
        name = comp_Network_Intelligence
        rule { include name: 'com.adva.nlms.mediation.ni' }
        rule { include prefix: 'com.adva.nlms.mediation.ni.adapter' }
        rule { include prefix: 'com.adva.nlms.mediation.ni.api' }
        rule { include prefix: 'com.adva.nlms.mediation.ni.dao' }
        rule { include prefix: 'com.adva.nlms.mediation.ni.diff' }
        rule { include prefix: 'com.adva.nlms.mediation.ni.entity' }
        rule { include prefix: 'com.adva.nlms.mediation.ni.events' }
        rule { include prefix: 'com.adva.nlms.mediation.ni.migration' }
        rule { include prefix: 'com.adva.nlms.mediation.ni.node' }
    }

    component {
        name = comp_Control_Plane_App

        rulePath "layers/apps/eod/control_plane/app"
    }

    component {
        name = comp_Control_Plane_Impl

        rulePath "layers/apps/eod/control_plane/impl"
    }

    component {
        name = comp_NI_Controller_Config_Provider

        rulePath "layers/apps/eod/control_plane/adapters/rest/ni_controller_config_provider"
    }

    component {
        name = comp_Control_Plane_Persistence

        rulePath "layers/apps/eod/control_plane/adapters/persistence"
    }

    component {
        name = comp_Control_Plane_Messaging

        rulePath "layers/apps/eod/control_plane/adapters/cp_messaging"
    }

    component {
        name = comp_Control_Plane_Rest

        rulePath "layers/apps/eod/control_plane/adapters/rest/server"
    }

    component {
        name = comp_Control_Plane_NI_Adapter

        rulePath "layers/apps/eod/control_plane/adapters/rest/ni_client"
    }

    component {
        name = comp_Path_Computation_Engine_API

        rulePath "layers/apps/eod/control_plane/path_computation_engine/api"
    }

    component {
        name = comp_Service_Implementation_Engine_API

        rulePath "layers/apps/eod/control_plane/service_implementation_engine/api"
    }

    component {
        name = comp_CPC_Manager_API

        rulePath "layers/apps/eod/cpc_manager/api"
    }

    component {
        name = comp_CPC_Manager_Rest_Adapter
        rulePath "layers/apps/eod/cpc_manager/adapters/rest/server"
    }

    component {
        name = comp_Node_Translation_Client

        rulePath "layers/apps/eod/control_plane/adapters/rest/node_translation_client"
    }

    component {
        name = comp_Performance_Management_API
        rulePrefix 'com.adva.nlms.common.performance'
        ruleName 'com.adva.nlms.common.monitoring'
        ruleName 'com.adva.nlms.common.monitoring.server'
        ruleName 'com.adva.nlms.common.monitoring.server.status'
    }

    component {
        name = comp_Performance_Management
        rule { include prefix: 'com.adva.nlms.mediation.performance' }
        rule {
            include prefix: 'com.adva.nlms.mediation.monitoring'
            // Logging framework
            exclude prefix: 'com.adva.nlms.mediation.monitoring.logging'
        }
        ruleName 'com.adva.nlms.common.monitoring.api'
    }

    component {
        name = comp_PM
        rulePath 'external', 'layers/apps/core/pm/api'
        rulePath 'internal', 'layers/apps/core/pm/impl'
    }

    component {
        name = comp_Alarm_NBI
        rulePath 'external', 'layers/apps/core/alarm-nbi/api'
        rulePath 'internal', 'layers/apps/core/alarm-nbi/app'
    }

    component {
        name = comp_Streaming_Telemetry_Client
        rulePath 'external', 'modules/mediation/streaming_telemetry/api'
        rulePath 'internal', 'modules/mediation/streaming_telemetry/impl'
    }

    component {
        name = comp_Scheduling_API
        rulePrefix 'com.adva.nlms.common.polling'
    }

    component {
        name = comp_Scheduling
        rulePrefix 'com.adva.nlms.mediation.polling'
        ruleName 'com.adva.common'
    }

    component {
        name = comp_SNMP_Profile_API
        rulePrefix 'com.adva.enc.profile.snmp.api'
    }

    component {
        name = comp_SNMP_Profile_Endpoint
        rulePrefix 'com.adva.enc.profile.snmp.impl'
    }

    component {
        name = comp_NE_Communication_Service
        rule {
            include prefix: 'com.adva.enc.profile.snmp'
            exclude prefix: 'com.adva.enc.profile.snmp.api'
            exclude prefix: 'com.adva.enc.profile.snmp.impl'
        }
        rule { include prefix: 'com.adva.nlms.mediation.profile.snmp' }
    }

    component {
        name = comp_Common_Driver_REST_API
        rulePath 'layers/drivers/api/common/common_rest_api'
    }

    component {
        name = comp_Core_Driver_API
        rulePath 'layers/drivers/api/core'
    }

    component {
        name = comp_Ethernet_Driver_API
        rulePath 'layers/drivers/api/eth_moaccess'
    }


    component {
        name = comp_Device_Inventory_Driver_API
        rulePath 'layers/drivers/api/device_inventory'
    }

    component {
        name = comp_Housekeeping_Driver_API
        rulePath 'layers/drivers/api/housekeeping'
    }

    component {
        name = comp_Optical_Router_Driver_Driver_API
        rulePath 'layers/drivers/api/optical_router'
    }

    component {
        name = comp_Notification_Driver_API
        rulePath 'layers/drivers/api/notification'
    }

    component {
        name = comp_Registry_Driver_API
        rulePath 'layers/drivers/api/registry'
    }

    component {
        name = comp_Driver_Delegate_Core
        rulePath 'layers/drivers/delegates/core'
    }

    component {
        name = comp_Ethernet_Delegate
        rulePath 'layers/drivers/delegates/eth_moaccess'
    }

    component {
        name = comp_Housekeeping_Delegate
        rulePath 'layers/drivers/delegates/housekeeping'
    }

    component {
        name = comp_Resource_Mediator_Delegate
        rulePath 'layers/drivers/delegates/resource_mediator'
    }

    component {
        name = comp_License_Converter_Delegate
        rulePath 'layers/drivers/delegates/license_converter'
    }

    component {
        name = comp_Core_Driver
        rulePath 'layers/drivers/implementations/core'
    }

    component {
        name = comp_F3_Driver
        rulePath 'layers/drivers/implementations/F3'
    }

    component {
        name = comp_F4_Driver
        rulePath 'layers/drivers/implementations/F4'
    }

    component {
        name = comp_F7_Driver
        rulePath 'layers/drivers/implementations/F7'
    }

    component {
        name = comp_F8_Driver
        rule {
            include path: 'layers/drivers/implementations/F8'
            exclude name:  'com.adva.nlms.mediation.housekeeping.nebackup.rest'
        }
        rulePath 'layers/drivers/implementations/resource/crm_model/f8'
    }

    component {
        name = comp_FSP1500_Driver
        rulePath 'layers/drivers/implementations/FSP1500'
    }

    component {
        name = comp_Optical_Router_Adapter
        rulePrefix 'com.adva.nlms.mediation.config.opticalrouter'
    }

    component {
        name = comp_Optical_Router_Driver_API
        rulePath 'layers/drivers/implementations/optical_router/drivers/api'
    }

    component {
        name = comp_Optical_Router_Driver_Impl
        rulePath 'layers/drivers/implementations/optical_router/impl'
    }

    component {
        name = comp_Optical_Router_Driver_Kafka_Adapter
        rulePath 'layers/drivers/implementations/optical_router/adapters/kafka'
    }

    component {
        name = comp_Optical_Router_Driver_Persistence
        rulePath 'layers/drivers/implementations/optical_router/adapters/persistence'
    }

    component {
        name = comp_Optical_Router_Driver_Rest_Client_Adapter
        rulePath 'layers/drivers/implementations/optical_router/adapters/rest/driver_client'
    }

    component {
        name = comp_Optical_Router_Driver_Rest_Server_Adapter
        rulePath 'layers/drivers/implementations/optical_router/adapters/rest/server'
    }

    component {
        name = comp_Juniper_Ptx_App
        rulePath 'layers/drivers/implementations/optical_router/drivers/juniper_ptx/app'
    }

    component {
        name = comp_Optical_Router_Test_Driver
        rulePath 'layers/drivers/implementations/optical_router/drivers/test_driver/app'
    }

    component {
        name = comp_MRV_Driver
         rulePath 'layers/drivers/implementations/MRV'
    }

    component {
        name = comp_Juniper_Driver
        rulePath 'layers/drivers/implementations/juniper'
    }

    component {
        name = comp_Legacy_Driver
        rulePath 'layers/drivers/implementations/legacy'
    }

    component {
        name = comp_OSA_Driver
        rulePath 'layers/drivers/implementations/OSA'
    }

    component {
        name = comp_Third_Party_Monitor_Driver
        rulePath 'layers/drivers/implementations/ThirdPartyMonitor'
    }

    component {
        name = comp_Driver_Registry
        documentLink = "https://polarion.advaoptical.com/polarion/#/project/FNM_Dev/wiki/Device%20Driver/Device%20Driver%20Framework"

        rulePath 'layers/drivers/registration'
    }

    component {
        name = comp_Driver_Notification

        rulePath 'layers/drivers/notification'
    }

    component {
        name = comp_Device_Driver

        rulePath 'modules/driver/driver_common'
        rule {
            include prefix: 'com.adva.nlms.mediation.config.driver'
            exclude name: 'com.adva.nlms.mediation.config.driver.rest'
        }
        rulePrefix 'com.adva.nlms.mediation.config.discovery'
        rulePrefix 'com.adva.nlms.mediation.config.dto'
        rulePrefix 'com.adva.nlms.mediation.config.entity'
        rulePrefix 'com.adva.nlms.mediation.config.event'
        rulePrefix 'com.adva.nlms.mediation.config.factory'
        rulePrefix 'com.adva.nlms.mediation.config.fallback'
        rulePrefix 'com.adva.nlms.mediation.config.lldp'
        rulePrefix 'com.adva.nlms.mediation.config.messaging'
        rulePrefix 'com.adva.nlms.mediation.config.moDesc'
        ruleName 'com.adva.nlms.mediation.config.peermgr'
        ruleName 'com.adva.nlms.mediation.config.protection'
        ruleName 'com.adva.nlms.mediation.ne_comm.ec.nedata'
        rule {
            include prefix: 'com.adva.nlms.mediation.config.model'
            // This belongs to fsp150egm device driver
            exclude name: 'com.adva.nlms.mediation.config.model.definition.ovn'
            // This belongs to SNMP device driver
            exclude prefix: 'com.adva.nlms.mediation.config.model.scanobject'
            exclude prefix: 'com.adva.nlms.mediation.config.model.definition.f3'
            exclude name: 'com.adva.nlms.mediation.config.model.definition.fsp150cm'
            exclude name: 'com.adva.nlms.mediation.config.model.definition.fsp150egx'
            exclude prefix: 'com.adva.nlms.mediation.config.model.definition.fsp20X'
            exclude prefix: 'com.adva.nlms.mediation.config.model.definition.fsp210'
            exclude name: 'com.adva.nlms.mediation.config.model.definition.fsp11X'
            exclude name: 'com.adva.nlms.mediation.config.model.definition.fspTxx04'
            exclude prefix: 'com.adva.nlms.mediation.config.model.definition.fsp_xg_mrv'
            exclude name: 'com.adva.nlms.mediation.config.model.definition.juniper'
            exclude name: 'com.adva.nlms.mediation.config.model.definition.osa3350'
            exclude name: 'com.adva.nlms.mediation.config.model.definition.osa5331'
            exclude name: 'com.adva.nlms.mediation.config.model.definition.osa5548c'
        }
        rulePrefix 'com.adva.nlms.mediation.config.neIdentification'
        rulePrefix 'com.adva.nlms.mediation.config.neconfig'
        rulePrefix 'com.adva.nlms.mediation.config.nefactory'
        rulePrefix 'com.adva.nlms.mediation.config.neprofile'
        rulePrefix 'com.adva.nlms.mediation.config.nettransaction'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.transaction.snmp'
        ruleName 'com.adva.nlms.mediation.ne_comm'
        ruleName 'com.adva.nlms.mediation.ne_comm.configuration.cli'
        ruleName 'com.adva.nlms.mediation.ne_comm.discovery'
        ruleName 'com.adva.nlms.mediation.ne_comm.ec.kap.missedtraps'
        ruleName 'com.adva.nlms.mediation.ne_comm.fallback'
        ruleName 'com.adva.nlms.mediation.ne_comm.netConf'
        ruleName 'com.adva.nlms.mediation.ne_comm.tasks'
        // Logic to decode country of origin on ADVA devices
        rulePrefix 'com.adva.nlms.mediation.config.countryOfOrigin'
        rule {
            include prefix: 'com.adva.nlms.mediation.model.mo'
            exclude prefix: 'com.adva.nlms.mediation.model.mo.ec'
        }
        // NE resynchronization logic
        rulePrefix 'com.adva.nlms.mediation.config.resync'
        rulePrefix 'com.adva.nlms.mediation.config.resyncWorkers'
        rulePrefix 'com.adva.nlms.mediation.config.security'
        ruleName 'com.adva.nlms.mediation.config'
        ruleName 'com.adva.nlms.mediation.config.shelflocation'
        ruleName 'com.adva.nlms.mediation.config.transientobjects'
        ruleName 'com.adva.nlms.mediation.config.trapsink'
        ruleName 'com.adva.nlms.mediation.config.traverse.common'
        ruleName 'com.adva.nlms.mediation.config.userlabels'
        rulePrefix 'com.adva.nlms.mediation.config.util'
        ruleName 'com.adva.nlms.mediation.config.xg.capabilities'
        // Event processing (traps) from NE
        rule {
            include prefix: 'com.adva.nlms.mediation.evtProc'
            exclude name: 'com.adva.nlms.mediation.evtProc.F3'
            exclude prefix: 'com.adva.nlms.mediation.evtProc.ec'
        }

        // (common for F4+F8) Element Controller
        rulePrefix 'com.adva.nlms.mediation.thresholdCrossingAlert'
        rulePrefix 'com.adva.nlms.mediation.ssl'
        rulePrefix 'com.adva.nlms.mediation.unsupne'
        rulePrefix 'com.adva.nlms.mediation.mo.inventory'
        ruleName 'com.adva.nlms.mediation.common.testfacade.neprofiles'
    }

    component {
        name = comp_ALM_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.alm'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.alm'
        rule { include pathPrefix: 'layers/drivers/implementations/ALM' }
    }

    component {
        name = comp_F3_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.f3'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.f3'
        rulePrefix 'com.adva.nlms.mediation.neResources.config.components.f3'
        rulePrefix 'com.adva.nlms.mediation.neResources.config.decorator.f3'
        rulePrefix 'com.adva.nlms.mediation.neResources.db.strategy.f3'
        rulePrefix 'com.adva.nlms.mediation.config.model.definition.f3'
        ruleName 'com.adva.nlms.common.config.f3'
        ruleName 'com.adva.nlms.mediation.evtProc.F3'
    }

    component {
        name = comp_F4_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.f4'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.f4'
        rulePrefix 'com.adva.nlms.mediation.neResources.config.components.F4'
        rulePrefix 'com.adva.nlms.mediation.neResources.config.decorator.f4'
        rulePrefix 'com.adva.nlms.mediation.neResources.db.strategy.f4'
        rulePrefix 'com.adva.nlms.mediation.f4.neComm'
        rulePrefix 'com.adva.nlms.mediation.f4'
        ruleName 'com.adva.nlms.common.config.f4'
    }

    component {
        name = comp_F8_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.f8'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.f8.rest'
        rulePrefix 'com.adva.nlms.mediation.config.sm.f8'
        rulePrefix 'com.adva.nlms.mediation.f8.neComm'
        rulePrefix 'com.adva.nlms.mediation.f8'
        ruleName 'com.adva.nlms.mediation.swver.api'
        ruleName 'com.adva.nlms.mediation.swver.impl'
        rulePath 'modules/ec_model_generator'
    }

    component {
        name = comp_FSP1500_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.fsp1500'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.legacy.fsp1500'
    }

    component {
        name = comp_FSP150CC_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.fsp150cc'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.discovery.fsp150cc'
    }

    component {
        name = comp_FSP150cm_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.fsp150cm'
        ruleName 'com.adva.nlms.mediation.config.model.definition.fsp150cm'
    }

    component {
        name = comp_FSP150cp_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.fsp150cp'
    }

    component {
        name = comp_FSP150egx_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.fsp150egx'
        ruleName 'com.adva.nlms.mediation.config.model.definition.fsp150egx'
    }

    component {
        name = comp_FSP1XX_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.fsp1XX'
        ruleName 'com.adva.nlms.mediation.config.model.definition.fsp11X'
    }

    component {
        name = comp_FSP20X_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.fsp20X'
        rulePrefix 'com.adva.nlms.mediation.config.model.definition.fsp20X'
    }

    component {
        name = comp_FSP210_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.fsp210'
        rulePath 'modules/driver/driver_xg210'
        ruleName 'com.adva.nlms.mediation.config.model.definition.fsp210'
    }

    component {
        name = comp_FSPTxx04_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.fspTxx04'
        ruleName 'com.adva.nlms.mediation.config.model.definition.fspTxx04'
    }

    component {
        name = comp_FSP_ea_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.fsp_ea'
    }

    component {
        name = comp_F7_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.fsp_r7'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.f7'
        rulePrefix 'com.adva.nlms.mediation.config.sm.f7'
        ruleName 'com.adva.nlms.mediation.config.pwr'
    }

    component {
        name = comp_FSP_xg1xx_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.fsp_xg1xx'
    }

    component {
        name = comp_FSP_xg_mrv_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.fsp_xg_mrv'
        rulePrefix 'com.adva.nlms.mediation.config.model.definition.fsp_xg_mrv'
    }

    component {
        name = comp_FSP_z4806_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.fsp_z4806'
    }

    component {
        name = comp_FSPsyncprob_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.fspsyncprob'
    }

    component {
        name = comp_Hatteras_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.hn400'
    }

    component {
        name = comp_Juniper_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.juniper'
        ruleName 'com.adva.nlms.mediation.config.model.definition.juniper'
    }

    component {
        name = comp_OSA3230b_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.osa3230b'
    }

    component {
        name = comp_OSA3350_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.osa3350'
        ruleName 'com.adva.nlms.mediation.config.model.definition.osa3350'
    }

    component {
        name = comp_OSA3351_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.osa3351'
        ruleName 'com.adva.nlms.mediation.config.model.definition.osa5331'
    }

    component {
        name = comp_OSA540X_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.osa540X'
    }

    component {
        name = comp_OSA542x_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.osa542x'
    }

    component {
        name = comp_OSA54CR_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.osa54CR'
    }

    component {
        name = comp_OSA5548c_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.osa5548c'
        ruleName 'com.adva.nlms.mediation.config.model.definition.osa5548c'
    }

    component {
        name = comp_OTS1000_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.ots1000'
    }

    component {
        name = comp_OSA5331_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.osa5331'
    }

    component {
        name = comp_FSP150egm_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.neResources.config.components.egm'
        rulePrefix 'com.adva.nlms.mediation.config.ovn'
        rulePrefix 'com.adva.nlms.mediation.config.model.definition.ovn'
    }

    component {
        name = comp_PV_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.ne_comm.pv'
        rulePath 'modules/driver/driver_pv'
    }

    component {
        name = comp_REST_Common
        rulePrefix 'com.adva.rest.common.api'
        rulePrefix 'com.adva.nlms.common.nbi.rest'
        rulePrefix 'com.adva.nlms.common.rest'
        ruleName 'com.adva.nlms.common.arcstate'
    }

    component {
        name = comp_SNMP_Common
        // There are a few snmp packages that are used by the frontend.
        rulePrefix 'com.adva.nlms.common.snmp'
        rulePrefix 'com.adva.nlms.common.snmp.f3'
        rulePrefix 'com.adva.nlms.common.snmp.f3.enums'
    }

    component {
        name = comp_REST_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.ne_comm.nerest'
        rule {
            include prefix: 'com.adva.nlms.mediation.ec'
            // Belongs to logging
            exclude name: 'com.adva.nlms.mediation.eclipse.link.debug'
        }
        rulePrefix 'com.adva.nlms.mediation.config.mofacade.rest'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.rest'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.ec.websocket'
        rulePrefix 'modules/communication/rest'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.websocket'
    }

    component {
        name = comp_SNMP_Device_Driver
        rulePath 'modules/driver/driver_snmp_common'
        rulePrefix 'com.adva.nlms.necom.snmp'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.snmp'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.backup'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.cmd'
        rulePrefix 'com.adva.nlms.mediation.config.model.scanobject'
        rulePrefix 'com.adva.nlms.mediation.config.mofacade.snmp'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.monitoring.snmp'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.configuration.snmp'
        rulePath 'modules/mediation/support/connection-service'
    }

    component {
        name = comp_AOS_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.ec'
        rulePrefix 'com.adva.nlms.mediation.model.mo.ec'
        rulePrefix 'com.adva.nlms.mediation.evtProc.ec'
        ruleName 'com.adva.nlms.mediation.config.mo.ec.scan'
    }

    component {
        name = comp_Symmetricom_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.symmetricom'
    }

    component {
        name = comp_Unmanaged_Device_Driver
        rulePrefix 'com.adva.nlms.mediation.config.unmanaged'
    }

    component {
        name = comp_Custom_Device_Driver
        rule {
            include prefix: 'com.adva.nlms.mediation.config.custom'
            // Belongs to Synchronization Manager
            exclude prefix: 'com.adva.nlms.mediation.config.customgnss'
        }
    }

    component {
        name = comp_Device_Inventory_API

        rule {
            include prefix: 'com.adva.nlms.common.config'
            exclude name: 'com.adva.nlms.common.config.f3'
            exclude name: 'com.adva.nlms.common.config.f4'
        }
        ruleName 'com.adva.nlms.common.ne'
        ruleName 'com.adva.nlms.common.ne.inventory'
        ruleName 'com.adva.nlms.common.fam'
        ruleName 'com.adva.nlms.common.mib'
        ruleName 'com.adva.nlms.common.rawinterfacedata'
        rulePrefix 'com.adva.nlms.common.necomm'
        rulePrefix 'com.adva.nlms.common.neprofile'
        ruleName 'com.adva.nlms.common.customdevice'
        ruleName 'com.adva.nlms.common.network.model.topology.property.keys'
        ruleName 'com.adva.nlms.common'
        ruleName 'com.adva.nlms.common.network'
        ruleName 'com.adva.nlms.common.neResources.model'
        ruleName 'com.adva.nlms.common.neResources.config'
        rulePath 'layers/apps/device_inventory/api'
    }

    component {
        name = comp_Device_Inventory
        ruleName 'com.adva.nlms.mediation.config.dao'
        ruleName 'com.adva.nlms.mediation.config.eth.entity.shelf'
        rule {
            include prefix: 'com.adva.nlms.mediation.config.mofacade'
            // Belongs to REST device driver
            exclude prefix: 'com.adva.nlms.mediation.config.mofacade.rest'
            // Belongs to SNMP device driver
            exclude prefix: 'com.adva.nlms.mediation.config.mofacade.snmp'
        }
        rule {
            include prefix: 'com.adva.nlms.mediation.config.sm'
            // Belongs to F7 device driver
            exclude prefix: 'com.adva.nlms.mediation.config.sm.f7'
            // Belongs to F8 device driver
            exclude prefix: 'com.adva.nlms.mediation.config.sm.f8'
        }
        ruleName 'com.adva.nlms.mediation.neResources'
        ruleName 'com.adva.nlms.mediation.neResources.config'
        ruleName 'com.adva.nlms.mediation.neResources.config.components'
        ruleName 'com.adva.nlms.mediation.neResources.config.decorator'
        ruleName 'com.adva.nlms.mediation.neResources.config.dev'
        ruleName 'com.adva.nlms.mediation.neResources.config.remember'
        rule { include prefix: 'com.adva.nlms.mediation.neResources.csv' }
        rule {
            include prefix: 'com.adva.nlms.mediation.neResources.db'
            // cc825 device driver
            exclude prefix: 'com.adva.nlms.mediation.neResources.db.strategy.cc825'
            // f3 device driver
            exclude prefix: 'com.adva.nlms.mediation.neResources.db.strategy.f3'
            // f4 device driver
            exclude prefix: 'com.adva.nlms.mediation.neResources.db.strategy.f4'
        }
        rulePrefix 'com.adva.nlms.mediation.neResources.handler'
        rulePrefix 'com.adva.nlms.mediation.neResources.logging'
        rulePrefix 'com.adva.nlms.mediation.neResources.mapper'
        rulePrefix 'com.adva.nlms.mediation.ypFacade'
        rule {
            include prefix: 'com.adva.nlms.common.neResources'
            exclude name: 'com.adva.nlms.common.neResources.config'
            exclude name: 'com.adva.nlms.common.neResources.model'
        }
        rulePath 'layers/apps/device_inventory/impl'
    }

    component {
        name = comp_Service_Topology_API
        rulePrefix 'com.adva.nlms.common.mltopologymodel'
        rulePrefix 'com.adva.nlms.common.topology'
        ruleName 'com.adva.nlms.common.model.topology.request'
        ruleName 'com.adva.nlms.common.webmanager'
        ruleName 'com.adva.nlms.common.tca'
        rulePrefix 'com.adva.nlms.common.interfaces.mo'
    }

    component {
        name = comp_Service_Synchronizer_App
        rulePath 'layers/apps/eod/service_synchronizer/app'
    }

    component {
        name = comp_Service_Synchronizer
        rulePath 'internal', 'layers/apps/eod/service_synchronizer/impl'
        rulePath 'external', 'layers/apps/eod/service_synchronizer/api'
    }

    component {
        name = comp_Service_Synchronizer_Messaging
        rulePath 'layers/apps/eod/service_synchronizer/adapters/messaging'
    }

    component {
        name = comp_Service_Synchronizer_Persistence
        rulePath 'layers/apps/eod/service_synchronizer/adapters/persistence'
    }

    component {
        name = comp_Service_Synchronizer_REST_Client
        rulePath 'layers/apps/eod/service_synchronizer/adapters/rest/client'
    }

    component {
        name = comp_Service_Synchronizer_REST_Server
        rulePath 'layers/apps/eod/service_synchronizer/adapters/rest/server'
    }

    component {
        name = comp_Service_Migration_Tool_App
        rulePath 'layers/apps/eod/service_migration_tool/app'
    }

    component {
        name = comp_Service_Migration_Tool
        rulePath 'internal', 'layers/apps/eod/service_migration_tool/impl'
        rulePath 'external', 'layers/apps/eod/service_migration_tool/api'
    }

    component {
        name = comp_Service_Migration_Tool_REST_Client
        rulePath 'layers/apps/eod/service_migration_tool/adapters/rest/client'
    }

    component {
        name = comp_Service_Migration_Tool_REST_Server
        rulePath 'layers/apps/eod/service_migration_tool/adapters/rest/server'
    }

    component {
        name = comp_Service_Migration_Tool_Messaging
        rulePath 'layers/apps/eod/service_migration_tool/adapters/messaging'
    }

    component {
        name = comp_Service_Migration_Tool_Persistence
        rulePath 'layers/apps/eod/service_migration_tool/adapters/persistence'
    }

    component {
        name = comp_SMT_NI_Mediator_API
        rulePath 'layers/apps/eod/service_migration_tool/mediator_ni/api'
    }
    component {
        name = comp_SMT_NI_Mediator_REST_Client
        rulePath 'layers/apps/eod/service_migration_tool/mediator_ni/adapters/rest/client'
    }
    component {
        name = comp_SMT_NI_Mediator_REST_Server
        rulePath 'layers/apps/eod/service_migration_tool/mediator_ni/adapters/rest/server'
    }

    component {
        name = comp_ntp
        rulePath 'internal', 'layers/apps/msd/ntp/impl'
        rulePath 'external', 'layers/apps/msd/ntp/api'
    }

    component {
        name = comp_ntp_rest_service
        rulePath 'layers/apps/msd/ntp/adapters/rest/server'
    }

    component {
        name = comp_Service_Topology
        rulePrefix 'com.adva.nlms.mediation.mltopologymodel'
        rulePrefix 'com.adva.nlms.mediation.common.mltopologymodel'
        ruleName 'com.adva.nlms.mediation.ddm.mofacade.notfhelpers.f3'
    }

    component {
        name = comp_Bookmark_API
        rulePrefix 'com.adva.nlms.common.bookmark'
    }

    component {
        name = comp_Bookmarks
        rulePrefix 'com.adva.nlms.mediation.bookmark'
    }

    component {
        name = comp_Create_User_Action
        rulePrefix 'com.adva.nlms.common.cua'
        rulePrefix 'com.adva.nlms.mediation.cua'
        rulePrefix 'com.adva.nlms.mediation.mailsender'
    }

    component {
        name = comp_Documentation
        rulePrefix 'com.adva.nlms.mediation.manual'
    }

    component {
        name = comp_Paging_API
        rulePrefix 'com.adva.nlms.common.paging'
    }

    component {
        name = comp_Paging_Framework
        rulePrefix 'com.adva.nlms.mediation.common.paging'
    }

    component {
        name = comp_Topology_Manager
        rulePrefix 'internal', 'com.adva.nlms.mediation.topology'
        ruleName 'internal', 'com.adva.nlms.idl.mediation.topology'
        //  WEASEL: Gets updates to topology and notifies frontend (should this be under notification???)
        rulePrefix 'internal', 'com.adva.nlms.mediation.reload'
        rulePrefix 'internal', 'com.adva.nlms.mediation.mo.app.mltopo'
        rulePrefix 'internal', 'com.adva.nlms.mediation.mo.app.demo'
        rulePath 'internal', 'layers/apps/core/topology-manager/adapters/rest/client'

        rulePath 'external', 'layers/apps/core/topology-manager/api'
        // Some topology DTO's defined for ethernet frontend widgets
        ruleName 'external', 'com.adva.nlms.common.ethernet'
    }

    component {
        name = comp_Topology_Manager_Rest_Service
        rulePath 'layers/apps/core/topology-manager/adapters/rest/server'
    }

    component {
        name = comp_TAPI_Interface
        rulePath 'external', 'modules/sdn_model'
        rulePath 'external', 'modules/nms_ml_client'
        rulePath 'external', 'layers/interfaces/sdn/api'
        rulePath 'internal', 'layers/interfaces/sdn/impl'
    }

    component {
        name = comp_Resilience_API
        rulePrefix 'com.adva.nlms.common.redundancy'
        rulePrefix 'com.adva.nlms.mediation.redundancy.api'
        ruleName 'com.adva.nlms.common.server'
    }

    component {
        name = comp_Resilience
        rule {
            include prefix: 'com.adva.nlms.mediation.redundancy'
            exclude prefix: 'com.adva.nlms.mediation.redundancy.api'
        }
    }

    component {
        name = comp_Report_Endpoint
        rulePrefix 'com.adva.nlms.mediation.report.resources'
        ruleName 'com.adva.nlms.common.ne.sync'
    }

    component {
        name = comp_Report_API
        rulePrefix 'com.adva.nlms.common.report'
    }

    component {
        name = comp_Report
        rule {
            include prefix: 'com.adva.nlms.mediation.report'
            exclude prefix: 'com.adva.nlms.mediation.report.resources'
        }
    }

    component {
        name = comp_Licensing
        rulePrefix 'com.adva.nlms.mediation.security.flexera'
        rulePrefix 'com.adva.nlms.common.elssso'
        rule { include pathPrefix: 'layers/infrastructure/licensing' }
    }

    component {
        name = comp_Network_Resource_Locator
        rulePath 'external', 'libraries/network_resource_locator/nrl_access'
        rulePath 'external', 'libraries/network_resource_locator/nrl_creator'
        rulePath 'external', 'libraries/network_resource_locator/nrl_common'
    }

    library {
        name = comp_Optical_Parameters
        rulePathPrefix 'external', 'libraries/optical_parameters'
    }

    library {
        name = comp_Async_Operation_Library
        rulePathPrefix 'external', 'libraries/communication/async-operation'
    }

    library {
        name = comp_Common_Definition_Library
        rulePathPrefix 'external', 'libraries/common_definition'
    }

    library {
        name = comp_Transactional_Provisioning_Library
        rulePath 'libraries/transactional_provisioning'
    }

    library {
        name = comp_Notifications_Library
        rulePathPrefix 'external', 'libraries/communication/notifications'
    }

    library {
        name = comp_Postman_Utils
        rulePath 'external', 'libraries/postman_test_utils'
    }

    component {
        name = comp_Security_API
        rulePrefix 'com.adva.nlms.mediation.security.api'
        rulePrefix 'com.adva.nlms.common.security'
        rulePrefix 'com.adva.nlms.common.ssl'
        rulePrefix 'com.adva.nlms.common.permission'
        ruleName 'com.adva.nlms.common.sso'
        ruleName 'com.adva.nlms.common.pca'
    }

    component {
        name = comp_App_Security
        rulePath 'internal', 'layers/infrastructure/app_security/impl'
        rulePath 'external', 'layers/infrastructure/app_security/api'
    }

    component {
        name = comp_Sabotage_Protection
        ruleName 'com.adva.nlms.mediation.common.sabotage'
        rulePath 'internal', 'layers/infrastructure/sabotage_protection/impl'
        rulePath 'external', 'layers/infrastructure/sabotage_protection/api'
    }

    component {
        name = comp_Security
        rule {
            include prefix: 'com.adva.nlms.mediation.security'
            exclude prefix: 'com.adva.nlms.mediation.security.api'
            exclude prefix: 'com.adva.nlms.mediation.security.flexera'
        }
        rule { include prefix: 'com.adva.nlms.mediation.pca' }
        ruleName 'com.adva.nlms.infrastucture.security.permission.api'
        ruleName 'com.adva.nlms.mediation.ne_comm.session'
        rulePath 'external', 'layers/infrastructure/security_api'
    }

    component {
        name = comp_Synchronization_Assurance_Interface
        rulePrefix 'com.adva.nlms.common.synchronization'
        rulePath 'modules/gnss/tpa-common'
        rulePath 'modules/gnss/common'
    }

    component {
        name = comp_Synchronization_Manager
        rule { include prefix: 'com.adva.nlms.mediation.synchronization' }
        rule { include prefix: 'com.adva.nlms.mediation.config.customgnss' }
    }

    component {
        name = comp_gnss_collector
        rulePath 'modules/gnss/collector'
    }

    component {
        name = comp_gnss_common_enc_mtls
        rulePath 'modules/gnss/common-enc-mtls'
    }

    component {
        name = comp_gnss_common_grpc
        rulePath 'modules/gnss/common-grpc'
    }

    component {
        name = comp_gnss_cli_worker
        rulePath 'modules/gnss/gnss-cli-worker'
    }

    component {
        name = comp_gnss_data_access
        rulePath 'modules/gnss/data-access'
    }

    component {
        name = comp_gnss_machine_learning
        rulePath 'modules/gnss/machine-learning'
    }

    component {
        name = comp_gnss_rca_analysis
        rulePath 'modules/gnss/rca-analysis'
    }

    component {
        name = comp_gnss_snt_data_access
        rulePath 'modules/gnss/snt-data-access'
    }

    component {
        name = comp_gnss_snt_gnmi_collector
        rulePath 'modules/gnss/snt-gnmi-collector'
    }

    component {
        name = comp_gnss_tpa_collector
        rulePath 'modules/gnss/tpa-collector'
    }

    component {
        name = comp_gnss_tpa_data_access
        rulePath 'modules/gnss/tpa-data-access'
    }

    component {
        name = comp_gnss_tpa_online_qm
        rulePath 'modules/gnss/tpa-online-qm'
    }

    component {
        name = comp_gnss_guard_ml
        rulePath 'modules/gnss/guard-ml'
    }

    component {
        name = comp_Service_Manager_Endpoint
        rulePrefix 'com.adva.nlms.mediation.sm.prov.cp.ese'
    }

    component {
        name = comp_Service_Manager_API
        rulePrefix 'com.adva.nlms.common.sm'
        rulePrefix 'com.adva.nlms.common.ethernetconfig'
        rulePrefix 'com.adva.nlms.common.service'
        rulePrefix 'com.adva.nlms.common.cp.policymodel'
    }

    component {
        name = comp_Service_Manager
        rule {
            include prefix: 'com.adva.nlms.mediation.sm'
            // This package tree belongs to bandwidth manager
            exclude prefix: 'com.adva.nlms.mediation.sm.bandwidthManager'
            exclude prefix: 'com.adva.nlms.mediation.sm.bandwidthrestriction'
            exclude prefix: 'com.adva.nlms.mediation.sm.prov.cp.ese'
        }
        rule {
            include prefix: 'com.adva.nlms.common.cp'
            exclude name: 'com.adva.nlms.common.cp.policymodel'
        }
        rulePrefix 'com.adva.nlms.mediation.common.serviceProvisioning'
        rulePathPrefix 'layers/apps/sm'
    }

    component {
        name = comp_Packet_Director_API
        rulePathPrefix 'external', 'layers/apps/packet_director/api'
        rulePrefix 'external', 'com.adva.nlms.common.provisioning'
        ruleName 'external', 'com.adva.nlms.common.discover.common.dto'
        ruleName 'external', 'com.adva.nlms.common.discover.common.facade'
    }

    component {
        name = comp_Packet_Director_Mediation_Adapter
        rulePrefix 'com.adva.nlms.mediation.layer.apps.ethsm'
    }

    component {
        name = comp_Packet_Director_Inventory
        rulePathPrefix 'internal', 'layers/apps/packet_director/inventory'
    }

    component {
        name = comp_Packet_Layer3
        rulePathPrefix 'internal', 'layers/apps/packet_layer3/ipvpn_service_manager/impl'
        rulePrefix 'internal', 'com.adva.nlms.common.discover.evpn'
        rulePathPrefix 'external', 'layers/apps/packet_layer3/ipvpn_service_manager/api'
    }

    component {
        name = comp_Packet_Layer3_Common
        rulePathPrefix 'layers/apps/packet_layer3/ipvpn_service_manager/common'
    }

    component {
        name = comp_Packet_Layer3_REST
        rulePath 'layers/apps/packet_layer3/ipvpn_service_manager/adapters/rest'
    }

    component {
        name = comp_Packet_Layer3_Mediation
        rulePath 'external', 'layers/apps/packet_layer3/ipvpn_service_manager/adapters/mediation/impl'
        rulePath 'internal', 'layers/apps/packet_layer3/ipvpn_service_manager/adapters/mediation/api'
    }

    component {
        name = comp_Packet_Layer3_Persistence
        rulePath 'external', 'layers/apps/packet_layer3/ipvpn_service_manager/adapters/persistence/impl'
        rulePath 'internal', 'layers/apps/packet_layer3/ipvpn_service_manager/adapters/persistence/api'
    }

    component {
        name = comp_Network_Resource_Inventory_Manager
        rulePath 'external', 'layers/apps/eod/network_resource_inventory_manager/api'
        rulePathPrefix 'internal', 'layers/apps/eod/network_resource_inventory_manager/adapters'
    }

    component {
        name = comp_Capability_File_Generator
        rulePathPrefix 'layers/infrastructure/capability/mo_capability_files_generator'
    }

    component {
        name = comp_Capabilities_Broker
        rulePath 'external', 'layers/apps/eod/capability_broker/api'
        rulePathPrefix 'internal', 'layers/apps/eod/capability_broker/impl'
    }

    component {
        name = comp_Capabilities_Broker_REST_Server
        rulePathPrefix 'internal', 'layers/apps/eod/capability_broker/adapters/rest/server'
    }

    component {
        name = comp_Capabilities_Broker_REST_Client
        rulePathPrefix 'internal', 'layers/apps/eod/capability_broker/adapters/rest/client'
    }

    component {
        name = comp_Capabilities_Provider
        rulePath 'external', 'layers/infrastructure/capability/provider/api'
        rulePathPrefix 'internal', 'layers/infrastructure/capability/provider/impl'
    }

    component {
        name = comp_Capabilities_Provider_App
        rulePathPrefix 'layers/infrastructure/capability/provider/app'
    }

    component {
        name = comp_Capabilities_Provider_Adapters
        rulePathPrefix 'layers/infrastructure/capability/provider/adapters'
    }

    component {
        name = comp_Capabilities_Provider_Properties
        rulePathPrefix 'layers/infrastructure/capability/provider/properties'
    }

    component {
        name = comp_EOD_ENC_Client
        rulePathPrefix 'internal', 'layers/apps/eod/enc_rest_client'
    }

    component {
        name = comp_Connectivity_Service_Manager
        rulePath 'external', 'layers/apps/eod/connectivity_service_manager/api'
        rulePath 'internal', 'layers/apps/eod/connectivity_service_manager/app'
        rulePath 'internal', 'layers/apps/eod/connectivity_service_manager/impl'
        rulePathPrefix 'internal', 'layers/apps/eod/connectivity_service_manager/adapters'
    }

    component {
        name = comp_Provisioning_Orchestrator
        rulePath 'external', 'layers/apps/eod/provisioning_orchestrator/api'
        rulePath 'internal', 'layers/apps/eod/provisioning_orchestrator/impl'
    }

    component {
        name = comp_Provisioning_Orchestrator_REST_Server
        rulePathPrefix 'internal', 'layers/apps/eod/provisioning_orchestrator/adapters/rest/server'
    }

    component {
        name = comp_Provisioning_Orchestrator_Messaging
        rulePathPrefix 'layers/apps/eod/provisioning_orchestrator/adapters/kafka'
    }

    component {
        name = comp_Resource_Advertiser_API
        rulePath 'layers/drivers/api/resource_advertiser'
    }

    component {
        name = comp_Resource_Mediator_API
        rulePath 'layers/drivers/api/resource_mediator'
    }

    component {
        name = comp_Resource_Mediator_Impl
        rulePath 'layers/drivers/implementations/resource/request_mediator/impl'
    }

    component {
        name = comp_Resource_Mediator_Provision_API
        rulePath 'layers/drivers/implementations/resource/request_mediator/provision_api'
    }

    component {
        name = comp_Resource_Advertiser_Delegate
        rulePathPrefix 'layers/drivers/delegates/resource_advertiser'
    }

    component {
        name = comp_Resource_Advertiser_Common
        rulePathPrefix 'layers/drivers/implementations/resource/advertiser/common'
    }

    component {
        name = comp_F7_Resource_Advertiser
        rulePathPrefix 'layers/drivers/implementations/resource/advertiser/f7'
    }

    component {
        name = comp_F8_Resource_Advertiser
        rulePathPrefix 'layers/drivers/implementations/resource/advertiser/f8'
    }

    component {
        name = comp_Job_Manager
        rulePath 'external', 'layers/apps/core/job-manager/api'
        rulePath 'internal', 'layers/apps/core/job-manager/app'
        rulePath 'internal', 'layers/apps/core/job-manager-test/app'
    }

    component {
        name = comp_Notification_Manager
        rulePath 'external', 'layers/infrastructure/notification/notification-manager/api'
        rulePath 'internal', 'layers/infrastructure/notification/notification-manager/app'
    }

    component {
        name = comp_Bandwidth_Manager_API
        rulePrefix 'com.adva.nlms.common.bandwidthrestriction'
    }

    component {
        name = comp_Bandwidth_Manager
        rulePrefix 'com.adva.nlms.mediation.sm.bandwidthManager'
        rulePrefix 'com.adva.nlms.mediation.sm.bandwidthrestriction'
        rulePrefix 'com.adva.nlms.mediation.config.bandwidthrestriction.paging'
    }

    component {
        name = comp_Crypto_Manager_API
        rulePrefix 'com.adva.nlms.common.cryptoOfficer'
        ruleName 'com.adva.nlms.common.connectguard'
        ruleName 'com.adva.nlms.common.ne.swversion.f3'
        ruleName 'com.adva.nlms.common.quantumKeyDistribution'
    }

    component {
        name = comp_Fiber_Assurance_API
        rulePrefix 'com.adva.nlms.common.gistransfer'
    }

    component {
        name = comp_Fiber_Assurance
        // Contains fiber plant and geo server logic
        rulePrefix 'com.adva.nlms.mediation.gistransfer'
        rulePrefix 'com.adva.nlms.mediation.fam'
        rulePrefix 'com.adva.nlms.mediation.common.segment'
    }

    component {
        name = comp_Fiber_Director_Core
        rulePath 'layers/apps/efd/efd_core'
    }

    component {
        name = comp_Fiber_Director_App
        rulePath 'layers/apps/efd/fiber_director'
    }

    component {
        name = comp_Scheduling

        rule { include prefix: 'com.adva.nlms.mediation.config.polling' }
    }

    component {
        name = comp_Bean_Management

        rulePath 'external', 'modules/mediation/support/bean-provider/api'
        rulePath 'internal', 'modules/mediation/support/bean-provider/impl'
        rulePrefix 'external', 'com.adva.nlms.mediation.server.spring'
        rulePrefix 'external', 'com.adva.nlms.mediation.servicelocator'
        ruleName 'external', 'com.adva.nlms.mediation'
    }

    component {
        name = comp_Status_Messenger
        rulePrefix 'com.adva.nlms.mediation.server.kafka.broadcast'
        rulePrefix 'com.adva.nlms.mediation.server.kafka.messages'
    }

    component {
        name = comp_Server_State
        ruleName 'external', 'com.adva.nlms.mediation.server.state.api'
        ruleName 'internal', 'com.adva.nlms.mediation.server.state.impl'
    }

    component {
        name = comp_Server_UI_Framework_API
        rulePrefix 'com.adva.common.model'
        rulePrefix 'com.adva.nlms.common.form'
        ruleName 'com.adva.common.util.filter'
        rulePrefix 'com.adva.nlms.common.actions'
        rulePrefix 'com.adva.nlms.common.visual'
        rulePrefix 'com.adva.nlms.common.networktree'
        ruleName 'com.adva.nlms.common.interfaces.filtering'
        ruleName 'com.adva.nlms.common.product'
    }

    component {
        name = comp_Server_Common_Types
        ruleName "com.adva.nlms.mediation.common"
        ruleName "com.adva.nlms.common.annotation"
        ruleName "com.adva.nlms.mediation.common.cacheableMethod"
        ruleName "com.adva.nlms.mediation.common.concurrent"
        ruleName "com.adva.nlms.mediation.common.util.collection"
        ruleName "com.adva.nlms.mediation.common.csv"
        ruleName "com.adva.nlms.mediation.common.dao"
        ruleName "com.adva.nlms.mediation.common.discovery"
        ruleName "com.adva.nlms.mediation.common.ec.util.time"
        ruleName "com.adva.nlms.mediation.common.monitoring"
        ruleName "com.adva.nlms.mediation.common.event"
        ruleName "com.adva.nlms.mediation.common.config"
        ruleName "com.adva.nlms.mediation.infrastructure.common_types.util.treeModel"
    }

    component {
        name = comp_Server_Common_Utilities
        ruleName "com.adva.nlms.mediation.common.util"
        ruleName "com.adva.nlms.mediation.common.validation"
    }

    component {
        name = comp_Concurrency
        rulePath "layers/infrastructure/server_infra/concurrent"
        ruleName "com.adva.nlms.mediation.ne_comm.concurrent"
    }

    component {
        name = comp_Server_UI_Framework
        ruleName 'com.adva.nlms.mediation.server'
        ruleName 'com.adva.nlms.mediation.server.autocomplete'
        ruleName 'com.adva.nlms.mediation.common.formmodel'
    }

    component {
        name = comp_XML_Processing
        ruleName 'com.adva.nlms.mediation.xml'
    }

    component {
        name = comp_Server_Modules

        rule { include prefix: 'com.adva.nlms.mediation.module' }
        rule { include prefix: 'com.adva.nlms.mediation.webui' }
        rulePath 'external', 'layers/infrastructure/server_infra/server_modules/api'
        rule {
            scope 'internal'
            include path: 'layers/infrastructure/server_infra/server_modules/impl'
            // The launcher is included in the component and is in this package but is
            // the only real thing here. This package is widely used so just don't associate
            // it to this component for now.
            exclude name: 'com.adva.nlms.mediation'
        }
    }

    component {
        name = comp_Server_Backup
        rule { include prefix: 'com.adva.nlms.mediation.server.backup' }
    }

    component {
        name = comp_HTTP_Proxy
        rule { include prefix: 'com.adva.nlms.mediation.server.proxy' }
    }

    component {
        name = comp_REST_Infrastructure
        rulePrefix 'com.adva.nlms.mediation.common.rest'
    }

    component {
        name = comp_Jetty_Infrastructure
        rulePrefix 'com.adva.nlms.mediation.server.jetty'
    }

    component {
        name = comp_Server_Search
        // Lucene based searches, optional functionality that can be enabled via fnm.properties
        rule { include prefix: 'com.adva.nlms.mediation.search' }
    }

    component {
        name = comp_User_Profile
        // WEASEL: Defines a properties service which I am not sure is actually used
        rule { include prefix: 'com.adva.nlms.mediation.properties' }
    }

    component {
        name = comp_YPDB
        rulePrefix 'com.adva.nlms.common.yp'
        rulePath 'libraries/java17/ypdb-bridge'
    }

    component {
        name = comp_Planner_Export
        rulePath 'external',  'modules/mediation/planner_export/api'
        rulePath 'internal',  'modules/mediation/planner_export/impl'
    }

    component {
        name = comp_Capabilities_Registry_API
        rulePrefix 'com.adva.nlms.common.capabilities'
    }

    component {
        name = comp_Capabilities_Registry
        rulePrefix 'com.adva.nlms.mediation.common.capabilities'
        rulePrefix 'modules/mediation/mo/capabilities'
    }

    library {
        name = comp_ENC_Utils
        rulePath 'libraries/enc_utils'
    }

    library {
        name = comp_Kafka_Utils
        rulePath 'libraries/kafka_utils'
    }

    component {
        name = comp_External_Tools
        // Java applications used in relation to ENC as a system
        rulePathPrefix 'modules/tools'
        rulePath 'modules/provision/Tools'
        rulePath 'modules/provision/Src/PvCommon/ModelConstantGenerator'
    }

    component {
        name = comp_Ethernet_Rings
        rulePathPrefix 'layers/apps/eth_ring'
    }

    component {
        name = comp_Database_Consolodation
        rulePath 'internal', 'modules/db-consolidation-tool/config'

        rulePath 'external', 'modules/db-consolidation-tool/export/api'
        rulePath 'internal', 'modules/db-consolidation-tool/export/impl'

        rulePath 'external', 'modules/db-consolidation-tool/file-operation/api'
        rulePath 'internal', 'modules/db-consolidation-tool/file-operation/impl'

        rulePath 'external', 'modules/db-consolidation-tool/import/api'
        rulePath 'internal', 'modules/db-consolidation-tool/import/impl'

        rulePath 'external', 'modules/db-consolidation-tool/serialization/api'
        rulePath 'external', 'modules/db-consolidation-tool/serialization/custom-field/api'
        rulePath 'external', 'modules/db-consolidation-tool/serialization/http/api'
        rulePath 'external', 'modules/db-consolidation-tool/serialization/link/api'
        rulePath 'external', 'modules/db-consolidation-tool/serialization/networkelement/api'
        rulePath 'external', 'modules/db-consolidation-tool/serialization/service/api'
        rulePath 'external', 'modules/db-consolidation-tool/serialization/servicetree/api'
        rulePath 'external', 'modules/db-consolidation-tool/serialization/snmp/api'
        rulePath 'external', 'modules/db-consolidation-tool/serialization/subnetwork/api'
        rulePath 'internal', 'modules/db-consolidation-tool/serialization/subnetwork/impl'

        rulePrefix 'external', 'com.adva.nlms.mediation.importExport'
        ruleName 'external', 'com.adva.nlms.common.importExport'
    }

    component {
        name = comp_Logging
        rulePrefix 'com.adva.nlms.mediation.eclipse.link.debug'
        rulePrefix 'com.adva.nlms.mediation.monitoring.logging'
        rulePrefix 'com.adva.nlms.mediation.logging'
        ruleName 'com.adva.nlms.common.logging'
        ruleName 'com.adva.nlms.common.logger'
        ruleName 'com.adva.nlms.mediation.diagnostics'
        ruleName 'com.adva.nlms.mediation.config.traverse.utils'
    }

    component {
        name = comp_Test_Framework
        rule { include prefix: 'com.adva.nlms.tfw' }
        rule { include prefix: 'com.adva.nlms.mediation.tfw' }
        rule { include path: 'modules/tfw_sit' }
        ruleName 'com.adva.nlms.mediation.service.test'
    }

    component {
        name = comp_Integration_Test
        rule {
            include prefix: 'com.adva.nlms.idl.mediation'
            // Belongs to topology manager
            exclude name: 'com.adva.nlms.idl.mediation.topology'
        }
        ruleName 'com.adva.nlms.mediation.common.testfacade.ctpprov'
    }

    component {
        name = comp_Test
        rulePrefix 'com.adva.nlms.mdcli'
        rulePrefix 'com.adva.nlms.mediation.circdeplib'
        rulePrefix 'com.adva.nlms.mediation.codescan'
        rulePrefix 'com.adva.nlms.tools.mockito.injection'
        rulePrefix 'com.adva.nlms.test'
        rulePrefix 'com.adva.nlms.mediation.test.facade'
        rulePrefix 'com.adva.nlms.rest'
        rulePrefix 'com.adva.nlms.tools'
        rulePrefix 'com.adva.nlms.util'
        rulePrefix 'com.adva.nlms.migration'
        rulePrefix 'com.adva.nlms.mediation.paging'
        rulePrefix 'com.adva.nlms.mediation.config.cfm'
        // REST API for testing
        rulePath 'modules/mo2csm_test'
        // This comes from a class with a package name in the source that does not match is path and is not used?
        ruleName 'junit.src.com.adva.nlms.mediation.synchronization.topology'
        ruleName 'com.adva.sdn.rest.mosync'
        ruleName 'com.adva.nlms.workbenchapi.api'
        ruleName 'com.adva.nlms.mediation.pysim'
        ruleName 'com.adva.nlms.mediation.scanTable'
        ruleName 'com.adva.nlms.mediation.network'
        ruleName 'com.adva.nlms.mediation.neResources.pysim'
        ruleName 'com.adva.nlms.mediation.server.jms'
        ruleName 'com.adva.nlms.mediation.common.config.f3'
        ruleName 'com.adva.nlms.mediation.common.testfacade'
        ruleName 'com.adva.nlms.common.filter'
        ruleName 'com.adva.nlms.common.model.graph'
        ruleName 'com.adva.nlms.common.graph.model'
        ruleName 'com.adva.nlms.common.preferences'
        ruleName 'com.adva.nlms.common.interfaces.fibermap'
        ruleName 'com.adva.common.transactions.sample'
        ruleName 'com.adva.nlms.mediation.mdpersistencetests'
        ruleName 'com.adva.nlms.mediation.serviceintent'
        ruleName 'com.adva.nlms.poc'
    }

    component {
        name = comp_DEAD
        // ECFM - Ethernet Configuration File Manager (thid functionality is obsolete)
        rulePrefix 'com.adva.nlms.mediation.ethNEConfig'
        rulePrefix 'com.adva.nlms.mediation.ne_comm.voyager.websocket'
        rulePrefix 'com.adva.nlms.mediation.tnms'
    }

    // Define system component diagram

    layer {
        name = layer_support
        addComponents comp_Bookmarks, comp_Server_Search, comp_NE_Communication_Service
        addComponents comp_Database_Consolodation
        addComponents comp_User_Profile
    }

    layer {
        name = layer_shared
        addComponents comp_Common_Utilities, comp_Common_Types, comp_YPDB
    }
    layer {
        name = layer_interfaces
        addComponents comp_MTOSI_NBI, diagram_client_interface, comp_TAPI_Interface
        addComponents comp_HTTP_Proxy, comp_Paging_Framework
    }
    layer {
        name = layer_basic_applications
        addComponents comp_Fault_Management, comp_Device_Inventory, comp_Health, comp_House_Keeping
        addComponents diagram_performance_management, comp_Report, diagram_topology_manager, comp_Job_Manager
        addComponents comp_Create_User_Action
        addComponents comp_Planner_Export
        addComponents comp_PM
        addComponents comp_Alarm_NBI
    }
    layer {
        name = layer_advanced_applications
        addComponents comp_Fiber_Assurance, comp_Bandwidth_Manager
        addComponents diagram_service_manager, diagram_mosaic_optical_director
        addComponents diagram_mosaic_fiber_director, diagram_mosaic_sync_director, diagram_packet_director, diagram_packet_layer3
        addComponents comp_Ethernet_Rings
    }
    layer {
        name = layer_device_drivers

        addComponents diagram_device_driver
        addComponents diagram_legacy_driver, diagram_third_party_driver, diagram_osa_driver, diagram_pv_driver
        addComponents diagram_aos_driver, diagram_f3_driver, comp_Capabilities_Registry, comp_ALM_Device_Driver
        addComponents comp_Device_Driver, comp_F7_Device_Driver, comp_FSP_ea_Device_Driver
    }
    layer {
        name = layer_device_communication

        addComponents comp_REST_Device_Driver, comp_SNMP_Device_Driver
    }
    layer {
        name = layer_infrastructure
        addComponents diagram_web_services, comp_Logging, diagram_notification, comp_REST_Communication
        addComponents comp_Persistency, comp_Resilience, comp_Scheduling, comp_Security, comp_App_Security, comp_Server_Backup
        addComponents diagram_server_infrastructure, comp_Licensing, comp_Network_Resource_Locator
        addComponents diagram_optical_libraries, diagram_communication_libraries, diagram_capability_provider
        addComponents comp_Notification_Manager
        addComponents comp_Sabotage_Protection
        addComponents comp_Approval_Proxy
    }

    diagram {
        name = diagram_enc_server

        defaultLayerSpacing = 10

        defaultNodeWidth = 55
        defaultNodeHeight = 10

        bind {
            layerName = layer_shared
            maxStacking = 4
            orientation = "horizontal"
            layout = "right"
            textSize = "4px"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
        }

        bind {
            layerName = layer_interfaces
            maxStacking = 6
            orientation = "horizontal"
            layout = "right-top"

            borderWidth = "0.5"
            borderColor = "black"

            textColor = "black"
            backgroundColor = "green"
            textSize = "4px"

            nodeColor = "green"
            nodeTextColor = "white"
            nodeTextSize = "6px"

            // Uncomment to override defaults
            // nodeWidth = 50
            // nodeHeight = 10
            // layerSpacing = 10
        }

        bind {
            layerName = layer_support
            layerSpacing = 15
            maxStacking = 14
            orientation = "vertical"
            layout = "below-left"

            borderWidth = "0.5"
            borderColor = "black"

            textColor = "black"
            backgroundColor = "navajowhite"
            textSize = "4px"

            nodeColor = "navajowhite"
            nodeTextColor = "black"
            nodeTextSize = "6px"

            renameComponent(comp_Database_Consolodation, "DB Consolidation")
            renameComponent(comp_NE_Communication_Service, "NE Comm. Service")
        }

        bind {
            layerName = layer_basic_applications
            maxStacking = 3
            orientation = "horizontal"
            layout = "right"

            borderWidth = "0.5"
            borderColor = "black"

            textColor = "black"
            backgroundColor = "blue"
            textSize = "4px"

            nodeColor = "blue"
            nodeTextColor = "white"
            nodeTextSize = "6px"
            renameComponent(diagram_topology_manager, "Topology Manager")
            renameComponent(diagram_performance_management, "Performance Mgr.")
        }

        bind {
            layerName = layer_advanced_applications
            maxStacking = 3
            orientation = "horizontal"
            layout = "right"
            layerSpacing = 3

            borderWidth = "0.5"
            borderColor = "black"

            textColor = "black"
            backgroundColor = "blue"
            textSize = "4px"

            nodeColor = "blue"
            nodeTextColor = "white"
            nodeTextSize = "6px"

            renameComponent(comp_Bandwidth_Manager, "Bandwidth Mgr.")
            renameComponent(diagram_service_manager, "Service Manager")
            renameComponent(diagram_mosaic_optical_director, "MOD")
            renameComponent(diagram_mosaic_fiber_director, "MFD")
            renameComponent(diagram_mosaic_sync_director, "MSD")
            renameComponent(diagram_packet_director, "MPD")
            renameComponent(diagram_packet_layer3, "Layer3")
        }

        bind {
            layerName = layer_device_drivers
            maxStacking = 6
            orientation = "horizontal"
            layout = "below"

            borderWidth = "0.5"
            borderColor = "black"

            textColor = "black"
            backgroundColor = "sienna"
            textSize = "4px"

            nodeColor = "sienna"
            nodeTextColor = "white"
            nodeTextSize = "6px"

            // Shorten component display names to remove the ' Device Driver' suffix.
            renameRule({ name ->
                String diagramName = name;
                if (name.endsWith(" Device Driver")) {
                    diagramName = name.substring(0, name.length() - " Device Driver".length())
                }
                return diagramName;
            })
            renameComponent(diagram_device_driver, "Driver Framework")
            renameComponent(comp_Device_Driver, "General")
            renameComponent(comp_Capabilities_Registry, "Capability Reg.")
            renameComponent(diagram_legacy_driver, "Legacy")
            renameComponent(diagram_third_party_driver, "3rd Party")
            renameComponent(diagram_osa_driver, "OSA")
            renameComponent(diagram_aos_driver, "AOS")
            renameComponent(diagram_f3_driver, "F3")
            renameComponent(diagram_pv_driver, "PV")
        }

        bind {
            layerName = layer_device_communication
            maxStacking = 6
            orientation = "horizontal"
            layout = "below"

            borderWidth = "0.5"
            borderColor = "black"

            textColor = "black"
            backgroundColor = "maroon"
            textSize = "4px"

            nodeColor = "maroon"
            nodeTextColor = "white"
            nodeTextSize = "6px"

            // Shorten component display names to remove the ' Device Driver' suffix.
            renameRule({ name ->
                String diagramName = name;
                if (name.endsWith(" Device Driver")) {
                    diagramName = name.substring(0, name.length() - " Device Driver".length())
                }
                return diagramName;
            })
        }

        bind {
            layerName = layer_infrastructure
            maxStacking = 8
            orientation = "vertical"
            layout = "right"

            borderWidth = "0.5"
            borderColor = "black"

            textColor = "black"
            backgroundColor = "gold"
            textSize = "4px"

            nodeColor = "gold"
            nodeTextColor = "black"
            nodeTextSize = "6px"

            renameComponent(diagram_web_services, "Web Services")
            renameComponent(diagram_server_infrastructure, "Server Infra")
            renameComponent(comp_REST_Communication, "REST Comm.")
            renameComponent(comp_Network_Resource_Locator, "Network Res. Loc.")
            renameComponent(diagram_notification, "Notification")
            renameComponent(diagram_communication_libraries, "Communication Libs")
            renameComponent(diagram_capability_provider, "Capability Provider")
        }
    }

    layer {
        name = layer_frontend_api
        addComponents diagram_client_interface
    }

    layer {
        name = layer_client_updater
        addComponents comp_Client_Updater
    }

    layer {
        name = layer_frontend
        addComponents comp_Frontend_Client, comp_Frontend_Workbench, comp_Custom_Widgets
    }

    diagram {
        name = diagram_enc_client

        defaultLayerSpacing = 10
        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_frontend_api
            maxStacking = 8
            orientation = "horizontal"
            layout = "right-top"
            borderColor = "black"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
        }

        bind {
            layerName = layer_client_updater
            maxStacking = 4
            orientation = "horizontal"
            layout = "below"
            borderColor = "black"
            textColor = "black"
            backgroundColor = "cadetblue"
            nodeColor = "cadetblue"
            nodeTextColor = "black"
        }

        bind {
            layerName = layer_frontend
            maxStacking = 4
            orientation = "horizontal"
            layout = "right"
            borderColor = "black"
            textColor = "black"
            backgroundColor = "gray"
            nodeColor = "gray"
            nodeTextColor = "black"

            renameComponent(comp_Frontend_Workbench, "Workbench")
        }
    }

    layer {
        name = layer_capability_file_generator
        addComponents comp_Capability_File_Generator
    }

    diagram {
        name = diagram_capability_file_generator

        defaultLayerSpacing = 10
        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_capability_file_generator
            maxStacking = 8
            orientation = "horizontal"
            layout = "right-top"
            borderColor = "black"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"

            renameComponent(comp_Capability_File_Generator, "Cap. File Gen.")
        }
    }

    layer {
        name = layer_driver_core
        addComponents comp_Core_Driver
    }

    diagram {
        name = diagram_driver_core

        defaultLayerSpacing = 10
        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_driver_core
            maxStacking = 8
            orientation = "horizontal"
            layout = "right-top"
            borderColor = "black"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
        }
    }

    layer {
        name = layer_device_driver_api
        addComponents comp_Core_Driver_API
        addComponents comp_Common_Driver_REST_API
        addComponents comp_Device_Inventory_Driver_API
        addComponents comp_Ethernet_Driver_API
        addComponents comp_Housekeeping_Driver_API
        addComponents comp_Notification_Driver_API
        addComponents comp_Registry_Driver_API
        addComponents comp_Resource_Advertiser_API
        addComponents comp_Resource_Mediator_API
        addComponents comp_Optical_Router_Driver_Driver_API
    }

    layer {
        name = layer_device_driver_delegates
        addComponents comp_Driver_Delegate_Core
        addComponents comp_Ethernet_Delegate
        addComponents comp_Housekeeping_Delegate
        addComponents comp_License_Converter_Delegate
        addComponents comp_Resource_Advertiser_Delegate
        addComponents comp_Resource_Mediator_Delegate
    }

    layer {
        name = layer_device_driver_support
        addComponents comp_Driver_Registry, comp_Driver_Notification
    }

    layer {
        name = layer_device_driver_families
        addComponents diagram_driver_core, comp_F3_Driver, comp_F4_Driver, comp_F7_Driver, comp_F8_Driver
        addComponents comp_FSP1500_Driver, comp_MRV_Driver
        addComponents comp_Juniper_Driver, comp_Legacy_Driver, comp_OSA_Driver
        addComponents comp_Third_Party_Monitor_Driver
        addComponents diagram_resource_advertiser, diagram_resource_mediator
        addComponents diagram_optical_router_driver
    }

    diagram {
        name = diagram_device_driver
        documentLink = "https://polarion.advaoptical.com/polarion/#/project/FNM_Dev/wiki/Device%20Driver/Device%20Driver%20Framework"

        defaultLayerSpacing = 10
        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_device_driver_api
            maxStacking = 8
            orientation = "horizontal"
            layout = "right-top"
            borderColor = "black"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"

            renameComponent(comp_Core_Driver_API, "Core Driver")
            renameComponent(comp_Device_Inventory_Driver_API, "Device Inventory")
            renameComponent(comp_Housekeeping_Driver_API, "Housekeeping")
            renameComponent(comp_Notification_Driver_API, "Notification")
            renameComponent(comp_Resource_Advertiser_API, "Res. Advertiser")
            renameComponent(comp_Resource_Mediator_API, "Res. Mediator")
            renameComponent(comp_Registry_Driver_API, "Registry")
            renameComponent(comp_Common_Driver_REST_API, "Comm. REST API")
            renameComponent(comp_Optical_Router_Driver_Driver_API, "Optical Router")
        }

        bind {
            layerName = layer_device_driver_support
            maxStacking = 8
            orientation = "horizontal"
            layout = "below"
            borderColor = "black"
            textColor = "black"
            backgroundColor = "cadetblue"
            nodeColor = "cadetblue"
            nodeTextColor = "black"
        }

        bind {
            layerName = layer_device_driver_delegates
            maxStacking = 8
            orientation = "horizontal"
            layout = "below"
            borderColor = "black"
            textColor = "black"
            backgroundColor = "blue"
            nodeColor = "blue"
            nodeTextColor = "white"

            renameComponent(comp_Driver_Delegate_Core, "Core")
            renameComponent(comp_Housekeeping_Delegate, "Housekeeping")
            renameComponent(comp_License_Converter_Delegate, "License Converter")
            renameComponent(comp_Resource_Advertiser_Delegate, "Res. Advertiser")
            renameComponent(comp_Resource_Mediator_Delegate, "Res. Mediator")
        }

        bind {
            layerName = layer_device_driver_families
            maxStacking = 8
            orientation = "horizontal"
            layout = "below"
            borderColor = "black"
            textColor = "black"
            backgroundColor = "gray"
            nodeColor = "gray"
            nodeTextColor = "black"

            renameComponent(diagram_driver_core, "Core Driver")
            renameComponent(diagram_resource_advertiser, "Res. Advertiser")
            renameComponent(diagram_resource_mediator, "F8 Res. Mediator")
            renameComponent(comp_Third_Party_Monitor_Driver, "3rd Party Monitor")
            renameComponent(diagram_optical_router_driver, "Optical Router")
        }
    }

    layer {
        name = layer_process_components
        addComponents comp_Test_Framework, comp_Test, comp_Integration_Test
        addComponents comp_External_Tools, comp_DEAD
    }

    /**
     * Kitchen sink diagram that has all the top level components (server and client)
     */
    diagram {
        name = diagram_enc_all_components

        defaultLayerSpacing = 10
        defaultNodeWidth = 55
        defaultNodeHeight = 10

        bind {
            layerName = layer_process_components
            orientation = "horizontal"
            layout = "right-top"
            textColor = "black"
            backgroundColor = "purple"
            nodeColor = "purple"
            nodeTextColor = "white"
        }

        bind {
            layerName = layer_client_updater
            maxStacking = 4
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "cadetblue"
            nodeColor = "cadetblue"
            nodeTextColor = "black"
        }

        bind {
            layerName = layer_frontend
            maxStacking = 4
            orientation = "horizontal"
            layout = "right-top"
            borderColor = "black"
            textColor = "black"
            backgroundColor = "gray"
            nodeColor = "gray"
            nodeTextColor = "black"

            renameComponent(comp_Frontend_Workbench, "Workbench")
        }

        bind {
            layerName = layer_shared
            maxStacking = 4
            orientation = "horizontal"
            layout = "below"
            textSize = "4px"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
        }

        bind {
            layerName = layer_interfaces
            maxStacking = 6
            orientation = "horizontal"
            layout = "right-top"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
        }

        bind {
            layerName = layer_support
            layerSpacing = 15
            maxStacking = 14
            orientation = "vertical"
            layout = "below-left"
            textColor = "black"
            backgroundColor = "navajowhite"
            nodeColor = "navajowhite"
            nodeTextColor = "black"

            renameComponent(comp_Database_Consolodation, "DB Consolidation")
        }

        bind {
            layerName = layer_basic_applications
            maxStacking = 3
            orientation = "horizontal"
            layout = "right"
            borderColor = "black"
            textColor = "black"
            backgroundColor = "blue"
            nodeColor = "blue"
            nodeTextColor = "white"
            renameComponent(comp_Performance_Management, "Performance Mgr.")
        }

        bind {
            layerName = layer_advanced_applications
            maxStacking = 3
            orientation = "horizontal"
            layout = "right"
            layerSpacing = 3
            textColor = "black"
            backgroundColor = "blue"
            nodeColor = "blue"
            nodeTextColor = "white"

            renameComponent(comp_Bandwidth_Manager, "Bandwidth Mgr.")
            renameComponent(diagram_service_manager, "Service Manager")
        }

        bind {
            layerName = layer_device_drivers
            maxStacking = 6
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "sienna"
            nodeColor = "sienna"
            nodeTextColor = "white"

            // Shorten component display names to remove the ' Device Driver' suffix.
            renameRule({ name ->
                String diagramName = name;
                if (name.endsWith(" Device Driver")) {
                    diagramName = name.substring(0, name.length() - " Device Driver".length())
                }
                return diagramName;
            })
            renameComponent(comp_ALM_Device_Driver, "ALM")
            renameComponent(comp_Device_Driver, "General")
            renameComponent(comp_Capabilities_Registry, "Capability Reg.")
            renameComponent(diagram_legacy_driver, "Legacy")
            renameComponent(diagram_third_party_driver, "3rd Party")
            renameComponent(diagram_osa_driver, "OSA")
            renameComponent(diagram_pv_driver, "PV")
        }

        bind {
            layerName = layer_device_communication
            maxStacking = 6
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "maroon"
            nodeColor = "maroon"
            nodeTextColor = "white"

            // Shorten component display names to remove the ' Device Driver' suffix.
            renameRule({ name ->
                String diagramName = name;
                if (name.endsWith(" Device Driver")) {
                    diagramName = name.substring(0, name.length() - " Device Driver".length())
                }
                return diagramName;
            })
        }

        bind {
            layerName = layer_infrastructure
            maxStacking = 8
            orientation = "vertical"
            layout = "right"
            textColor = "black"
            backgroundColor = "gold"
            nodeColor = "gold"
            nodeTextColor = "black"

            renameComponent(diagram_web_services, "Web Services")
            renameComponent(diagram_server_infrastructure, "Server Infra")
        }
    }

    layer {
        name = layer_rest_endpoints
        addComponents comp_Alarm_Event_NE_Endpoint, comp_Client_Updater_Endpoint
        addComponents comp_Health_Endpoint
        addComponents comp_Service_Manager_Endpoint, comp_SNMP_Profile_Endpoint
        addComponents comp_Report_Endpoint
        addComponents comp_Persistency_Endpoint, comp_GUI_User_Data_Endpoint
        addComponents comp_Capabilities_Registry_Endpoint, comp_Driver_Manager_Endpoint
        addComponents comp_NTP
    }

    layer {
        name = layer_support_apis
        addComponents comp_Bookmark_API, comp_SNMP_Profile_API
        addComponents comp_Paging_API, comp_Frontend_Workbench_API
    }
    layer {
        name = layer_infrastructure_apis
        addComponents comp_Notification_API, comp_Capabilities_Registry_API, comp_Resilience_API
        addComponents comp_Security_API, comp_Server_UI_Framework_API, comp_Scheduling_API
        addComponents comp_Persistency_API
    }
    layer {
        name = layer_basic_application_apis
        addComponents comp_Fault_Management_API, comp_Report_API, comp_House_Keeping_API
        addComponents comp_Performance_Management_API, comp_Health_API, comp_Device_Inventory_API
    }
    layer {
        name = layer_advanced_application_apis
        addComponents comp_Fiber_Assurance_API, comp_Bandwidth_Manager_API, comp_Crypto_Manager_API
        addComponents comp_Synchronization_Assurance_Interface
        addComponents comp_Service_Manager_API, comp_Service_Topology_API
    }
    layer {
        name = layer_general_apis
        addComponents comp_REST_Common, comp_SNMP_Common
    }

    diagram {
        name = diagram_client_interface

        bind {
            layerName = layer_rest_endpoints
            maxStacking = 8
            orientation = "horizontal"
            layout = "right-top"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            renameComponent(comp_Alarm_Event_NE_Endpoint, "Alarm,Event,NE")
            renameComponent(comp_Client_Updater_Endpoint, "Client Updater")
            renameComponent(comp_Health_Endpoint, "Health Center")
            renameComponent(comp_Service_Manager_Endpoint, "Service Manager")
            renameComponent(comp_SNMP_Profile_Endpoint, "SNMP Profile")
            renameComponent(comp_Report_Endpoint, "Reports")
            renameComponent(comp_Persistency_Endpoint, "Persistency")
            renameComponent(comp_GUI_User_Data_Endpoint, "GUI User Data")
            renameComponent(comp_Capabilities_Registry_Endpoint, "Capabilities Reg.")
            renameComponent(comp_Driver_Manager_Endpoint, "Driver Manager")
        }

        bind {
            layerName = layer_support_apis
            maxStacking = 2
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "navajowhite"
            nodeColor = "navajowhite"
            nodeTextColor = "black"
            renameComponent(comp_Frontend_Workbench_API, "Workbench API")
        }
        bind {
            layerName = layer_basic_application_apis
            maxStacking = 2
            // orientation = "horizontal-sibling"
            orientation = "horizontal"
            layout = "right"
            textColor = "black"
            backgroundColor = "blue"
            nodeColor = "blue"
            nodeTextColor = "white"

            renameComponent(comp_Device_Inventory_API, "Device Inv. API")
            renameComponent(comp_Fault_Management_API, "Fault Mgt. API")
            renameComponent(comp_House_Keeping_API, "House Keep. API")
            renameComponent(comp_Performance_Management_API, "Perform. Mgr. API")
        }
        bind {
            layerName = layer_advanced_application_apis
            maxStacking = 2
            orientation = "horizontal"
            layout = "right"
            textColor = "black"
            backgroundColor = "blue"
            nodeColor = "blue"
            nodeTextColor = "white"

            renameComponent(comp_Fiber_Assurance_API, "Fiber Assur. API")
            renameComponent(comp_Bandwidth_Manager_API, "Bandwidth M. API")
            renameComponent(comp_Crypto_Manager_API, "Crypto Mgr. API")
            renameComponent(comp_Synchronization_Assurance_Interface, "Sync. Assur. Intf.")
            renameComponent(comp_Service_Manager_API, "Service Mgr. API")
            renameComponent(comp_Service_Topology_API, "Service Topo. API")
        }
        bind {
            layerName = layer_infrastructure_apis
            maxStacking = 2
            orientation = "horizontal"
            layout = "right"
            textColor = "black"
            backgroundColor = "gold"
            nodeColor = "gold"
            nodeTextColor = "black"

            renameComponent(comp_Server_UI_Framework_API, "UI Framework API")
            renameComponent(comp_Capabilities_Registry_API, "Cap. Reg. API")
        }
        bind {
            layerName = layer_general_apis
            maxStacking = 2
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "maroon"
            nodeColor = "maroon"
            nodeTextColor = "white"
        }
    }

    layer {
        name = layer_server_utilities
        addComponents comp_Bean_Management, comp_Server_State, comp_Server_Modules
        addComponents comp_Server_UI_Framework, comp_XML_Processing
        addComponents comp_Server_Common_Types, comp_Server_Common_Utilities
        addComponents comp_Documentation, comp_ENC_Utils, comp_Kafka_Utils
        addComponents comp_Status_Messenger
        addComponents comp_Concurrency
        addComponents comp_concurrent_tracing
    }

    diagram {
        name = diagram_server_infrastructure
        defaultNodeWidth = 52

        bind {
            layerName = layer_server_utilities
            maxStacking = 5
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"

            renameComponent(comp_Bean_Management, "Bean Mgmt.")
            renameComponent(comp_Server_UI_Framework, "UI Framework")
            renameComponent(comp_Server_Common_Utilities, "Common Utilities")
            renameComponent(comp_Server_Common_Types, "Common Types")
        }

    }

    layer {
        name = layer_sm_editing
        addComponents comp_Service_Manager
        addComponents comp_Network_Intelligence
    }

    layer {
        name = layer_sm_inventory
        addComponents comp_Service_Topology
    }

    layer {
        name = layer_driver_adapters
        addComponents comp_Optical_Router_Adapter
    }

    layer {
        name = layer_sm_network_intelligence
        addComponents comp_NI_Configuration
        addComponents comp_NI_Config_API
        addComponents comp_NI_Links
        addComponents comp_NI_Nodes
        addComponents comp_NI_Nodes_System
    }

    diagram {
        name = diagram_service_manager

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_sm_editing
            orientation = "vertical"
            layout = "right"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            renameComponent(comp_Network_Intelligence, "NI Core")
        }

        bind {
            layerName = layer_sm_inventory
            orientation = "vertical"
            layout = "right"
            textColor = "black"
            backgroundColor = "blue"
            nodeColor = "blue"
            nodeTextColor = "white"
        }

        bind {
            layerName = layer_driver_adapters
            orientation = "vertical"
            layout = "right"
            textColor = "black"
            backgroundColor = "gold"
            nodeColor = "gold"
            nodeTextColor = "black"

            renameComponent(comp_Optical_Router_Adapter, "Optical Router")
        }


        bind {
            layerName = layer_sm_network_intelligence
            maxStacking = 3
            orientation = "vertical"
            layout = "right"
            textColor = "black"
            backgroundColor = "navajowhite"
            nodeColor = "navajowhite"
            nodeTextColor = "black"
            renameComponent(comp_NI_Configuration, "Configuration")
            renameComponent(comp_NI_Config_API, "Config API")
            renameComponent(comp_NI_Links, "Links")
            renameComponent(comp_NI_Nodes, "Nodes")
            renameComponent(comp_NI_Nodes_System, "Nodes System")
        }
    }

    layer {
        name = layer_notification
        addComponents comp_notification_core
        addComponents comp_notification_authorization
        addComponents comp_notification_tracing
    }

    diagram {
        name = diagram_notification

        defaultNodeWidth = 60
        defaultNodeHeight = 10

        bind {
            layerName = layer_notification
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"

            renameComponent(comp_notification_authorization, "Authorize Headers")
            renameComponent(comp_notification_tracing, "Tracing")
        }
    }

    layer {
        name = layer_optical_libraries
        addComponents comp_Optical_Parameters
        addComponents comp_Common_Definition_Library
        addComponents comp_Transactional_Provisioning_Library
    }

    diagram {
        name = diagram_optical_libraries

        defaultNodeWidth = 60
        defaultNodeHeight = 10

        bind {
            layerName = layer_optical_libraries
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            renameComponent(comp_Optical_Parameters, "Opt. Parameters")
            renameComponent(comp_Common_Definition_Library, "Common Defs")
            renameComponent(comp_Transactional_Provisioning_Library, "Trans. Provisioning")
        }
    }

    layer {
        name = layer_communication_libraries
        addComponents comp_Async_Operation_Library
        addComponents comp_Notifications_Library
    }

    diagram {
        name = diagram_communication_libraries

        defaultNodeWidth = 60
        defaultNodeHeight = 10

        bind {
            layerName = layer_communication_libraries
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            renameComponent(comp_Notifications_Library, "Notifications")
            renameComponent(comp_Async_Operation_Library, "Async Operations")
        }
    }

    layer {
        name = layer_capability_provider
        addComponents comp_Capabilities_Provider
        addComponents comp_Capabilities_Provider_App
        addComponents comp_Capabilities_Provider_Adapters
        addComponents comp_Capabilities_Provider_Properties
    }

    diagram {
        name = diagram_capability_provider

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_capability_provider
            maxStacking = 5
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"

            renameComponent(comp_Capabilities_Provider_App, "App")
            renameComponent(comp_Capabilities_Provider_Adapters, "Adapters")
            renameComponent(comp_Capabilities_Provider_Properties, "Properties")
        }
    }

    layer {
        name = layer_capability_broker
        addComponents comp_Capabilities_Broker
        addComponents comp_Capabilities_Broker_REST_Server
        addComponents comp_Capabilities_Broker_REST_Client
    }

    diagram {
        name = diagram_capability_broker

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_capability_broker
            maxStacking = 5
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"

            renameComponent(comp_Capabilities_Broker_REST_Server, "REST Server")
            renameComponent(comp_Capabilities_Broker_REST_Client, "REST Client")
        }
    }

    layer {
        name = layer_provisioning_orchestrator
        addComponents comp_Provisioning_Orchestrator
        addComponents comp_Provisioning_Orchestrator_REST_Server
        addComponents comp_Provisioning_Orchestrator_Messaging
    }

    diagram {
        name = diagram_provisioning_orchestrator

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_provisioning_orchestrator
            maxStacking = 5
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"

            renameComponent(comp_Provisioning_Orchestrator, "Prov. Orch.")
            renameComponent(comp_Provisioning_Orchestrator_REST_Server, "REST Server")
            renameComponent(comp_Provisioning_Orchestrator_Messaging, "Messaging")
        }
    }

    layer {
        name = layer_mosiac_optical_director
        addComponents comp_Connectivity_Service_Manager
        addComponents comp_Network_Resource_Inventory_Manager
        addComponents diagram_provisioning_orchestrator
        addComponents diagram_control_plane, comp_EOD_ENC_Client
        addComponents diagram_capability_broker
        addComponents diagram_service_synchronizer
        addComponents diagram_service_migration_tool
        addComponents diagram_cpc_manager
    }

    diagram {
        name = diagram_mosaic_optical_director

        documentLink 'https://polarion.adtran.com/polarion/#/project/FNM_Dev/workitem?id=FNMD-104996'

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_mosiac_optical_director
            maxStacking = 5
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            renameComponent(comp_Connectivity_Service_Manager, "CSM")
            renameComponent(diagram_control_plane, "Control Plane")
            renameComponent(comp_Network_Resource_Inventory_Manager, "NRIM")
            renameComponent(comp_Provisioning_Orchestrator, "Prov. Orch.")
            renameComponent(comp_EOD_ENC_Client, "MNC Client")
            renameComponent(diagram_service_synchronizer, "Service Sync.")
            renameComponent(diagram_service_migration_tool, "Migration Tool")
            renameComponent(diagram_capability_broker, "Capability Broker")
            renameComponent(diagram_provisioning_orchestrator, "Prov. Orch.")
            renameComponent(diagram_cpc_manager, "CPC Manager")
        }
    }

    layer {
        name = layer_service_synchronizer
        addComponents comp_Service_Synchronizer_App
        addComponents comp_Service_Synchronizer
        addComponents comp_Service_Synchronizer_Messaging
        addComponents comp_Service_Synchronizer_Persistence
        addComponents comp_Service_Synchronizer_REST_Client
        addComponents comp_Service_Synchronizer_REST_Server
    }

    diagram {
        name =  diagram_service_synchronizer

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_service_synchronizer
            maxStacking = 5
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            renameComponent(comp_Service_Synchronizer_App, "App")
            renameComponent(comp_Service_Synchronizer, "Service Sync.")
            renameComponent(comp_Service_Synchronizer_Messaging, "Messaging")
            renameComponent(comp_Service_Synchronizer_Persistence, "Persistence")
            renameComponent(comp_Service_Synchronizer_REST_Client, "REST Client")
            renameComponent(comp_Service_Synchronizer_REST_Server, "REST Server")
        }
    }

    layer {
        name = layer_service_migration_tool
        addComponents comp_Service_Migration_Tool_App
        addComponents comp_Service_Migration_Tool
        addComponents comp_Service_Migration_Tool_REST_Client
        addComponents comp_Service_Migration_Tool_REST_Server
        addComponents comp_Service_Migration_Tool_Messaging
        addComponents comp_Service_Migration_Tool_Persistence
        addComponents diagram_service_migration_tool_ni_mediator
    }

    diagram {
        name =  diagram_service_migration_tool

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_service_migration_tool
            maxStacking = 5
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            renameComponent(comp_Service_Migration_Tool_App, "App")
            renameComponent(comp_Service_Migration_Tool, "Migration Tool")
            renameComponent(comp_Service_Migration_Tool_REST_Client, "REST Client")
            renameComponent(comp_Service_Migration_Tool_REST_Server, "REST Server")
            renameComponent(comp_Service_Migration_Tool_Messaging, "Messaging")
            renameComponent(comp_Service_Migration_Tool_Persistence, "Persistence")
            renameComponent(diagram_service_migration_tool_ni_mediator, "NI Mediator")
        }
    }

    layer {
        name = layer_service_migration_tool_ni_mediator
        addComponents comp_SMT_NI_Mediator_API
        addComponents comp_SMT_NI_Mediator_REST_Client
        addComponents comp_SMT_NI_Mediator_REST_Server
    }

    diagram {
        name =  diagram_service_migration_tool_ni_mediator

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_service_migration_tool_ni_mediator
            maxStacking = 5
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            renameComponent(comp_SMT_NI_Mediator_API, "API")
            renameComponent(comp_SMT_NI_Mediator_REST_Client, "REST Client")
            renameComponent(comp_SMT_NI_Mediator_REST_Server, "REST Service")
        }
    }

    layer {
        name = layer_packet_director
        addComponents comp_Packet_Director_API
        addComponents comp_Packet_Director_Mediation_Adapter
        addComponents comp_Packet_Director_Inventory
    }

    layer {
        name = layer_packet_layer3
        addComponents comp_Packet_Layer3
        addComponents comp_Packet_Layer3_Common
        addComponents comp_Packet_Layer3_REST
        addComponents comp_Packet_Layer3_Mediation
        addComponents comp_Packet_Layer3_Persistence
    }

    layer {
        name = layer_control_plane
        addComponents comp_Control_Plane_App
        addComponents comp_Control_Plane_Impl
        addComponents comp_NI_Controller_Config_Provider
        addComponents comp_Control_Plane_Persistence
        addComponents comp_Control_Plane_Messaging
        addComponents comp_Control_Plane_Rest
        addComponents comp_Control_Plane_NI_Adapter
        addComponents comp_Path_Computation_Engine_API
        addComponents comp_Service_Implementation_Engine_API
        addComponents comp_Node_Translation_Client
    }

    layer {
        name = layer_cpc_manager
        addComponents comp_CPC_Manager_API, comp_CPC_Manager_Rest_Adapter
    }

    diagram {
        name = diagram_packet_director

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_packet_director
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            renameComponent(comp_Packet_Director_API, "MPD API")
            renameComponent(comp_Packet_Director_Mediation_Adapter, "Mediation Adapter")
            renameComponent(comp_Packet_Director_Inventory, "MPD Inventory")
        }
    }

    diagram {
        name = diagram_packet_layer3

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_packet_layer3
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            renameComponent(comp_Packet_Layer3, "Layer 3")
            renameComponent(comp_Packet_Layer3_REST, "REST Adapter")
            renameComponent(comp_Packet_Layer3_Mediation, "Mediation Adapter")
            renameComponent(comp_Packet_Layer3_Persistence, "Persist Adapter")
            renameComponent(comp_Packet_Layer3_Common, "Common")
        }
    }

    layer {
        name = layer_mosiac_fiber_director
        addComponents comp_Fiber_Director_Core
        addComponents comp_Fiber_Director_App
        addComponents comp_Fiber_Assurance
    }

    diagram {
        name = diagram_mosaic_fiber_director

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_mosiac_fiber_director
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"

            renameComponent(comp_Fiber_Director_Core, "Core")
            renameComponent(comp_Fiber_Director_App, "App")
        }
    }

    layer {
        name = layer_mosaic_sync_director
        addComponents diagram_ntp
        addComponents comp_Synchronization_Manager
        addComponents comp_gnss_collector
        addComponents comp_gnss_common_enc_mtls
        addComponents comp_gnss_common_grpc
        addComponents comp_gnss_data_access
        addComponents comp_gnss_cli_worker
        addComponents comp_gnss_machine_learning
        addComponents comp_gnss_rca_analysis
        addComponents comp_gnss_snt_data_access, comp_gnss_snt_gnmi_collector
        addComponents comp_gnss_tpa_collector, comp_gnss_tpa_data_access, comp_gnss_tpa_online_qm
        addComponents comp_gnss_guard_ml
    }

    diagram {
        name = diagram_mosaic_sync_director

        defaultNodeWidth = 55
        defaultNodeHeight = 10

        bind {
            layerName = layer_mosaic_sync_director
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            maxStacking = 5

            renameComponent(diagram_ntp, "NTP")
            renameComponent(comp_Synchronization_Manager, "Sync Manager")
            renameComponent(comp_gnss_collector, "Collector")
            renameComponent(comp_gnss_common_enc_mtls, "MNC MTLS")
            renameComponent(comp_gnss_common_grpc, "Common GRPC")
            renameComponent(comp_gnss_data_access, "Data Access")
            renameComponent(comp_gnss_cli_worker, "CLI Worker")
            renameComponent(comp_gnss_machine_learning, "Machine Learning")
            renameComponent(comp_gnss_rca_analysis, "RCA Analysis")
            renameComponent(comp_gnss_snt_data_access, "SNT Data Access")
            renameComponent(comp_gnss_snt_gnmi_collector, "SNT GNMI Collector")
            renameComponent(comp_gnss_tpa_collector, "TPA Collector")
            renameComponent(comp_gnss_tpa_data_access, "TPA Data Access")
            renameComponent(comp_gnss_tpa_online_qm, "TPA Online QM")
            renameComponent(comp_gnss_guard_ml, "Guard ML")
        }
    }

    diagram {
        name = diagram_control_plane

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_control_plane
            maxStacking = 4
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"

            renameComponent(comp_Control_Plane_Persistence, "CP Persistence")
            renameComponent(comp_Control_Plane_Messaging, "CP Messaging")
            renameComponent(comp_Control_Plane_Rest, "CP Rest")
            renameComponent(comp_Node_Translation_Client, "Node Trans Client")
            renameComponent(comp_NI_Controller_Config_Provider, "NI Config Provider")
        }
    }

    diagram {
        name = diagram_cpc_manager

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_cpc_manager
            maxStacking = 4
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"

            renameComponent(comp_CPC_Manager_API, "API")
            renameComponent(comp_CPC_Manager_Rest_Adapter, "Rest Adapter")
        }
    }

    layer {
        name = layer_resource_advertiser
        addComponents comp_Resource_Advertiser_Common
        addComponents comp_F7_Resource_Advertiser
        addComponents comp_F8_Resource_Advertiser
    }

    diagram {
        name = diagram_resource_advertiser

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_resource_advertiser
            maxStacking = 4
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"

            renameComponent(comp_Resource_Advertiser_Common, "Common")
            renameComponent(comp_F7_Resource_Advertiser, "F7 Advertiser")
            renameComponent(comp_F8_Resource_Advertiser, "F8 Advertiser")
        }
    }

    layer {
        name = layer_resource_mediator
        addComponents comp_Resource_Mediator_Impl
        addComponents comp_Resource_Mediator_Provision_API
    }

    diagram {
        name = diagram_resource_mediator

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_resource_mediator
            maxStacking = 4
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"

            renameComponent(comp_Resource_Mediator_Impl, "Impl")
            renameComponent(comp_Resource_Mediator_Provision_API, "Provision API")
        }
    }

    layer {
        name = layer_ntp
        addComponents comp_ntp, comp_ntp_rest_service
    }

    diagram {
        name =  diagram_ntp

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_ntp
            maxStacking = 5
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"

            renameComponent(comp_ntp, "NTP")
        }
    }

    layer {
        name = layer_topology_manager
        addComponents comp_Topology_Manager, comp_Topology_Manager_Rest_Service
    }

    diagram {
        name =  diagram_topology_manager

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_topology_manager
            maxStacking = 5
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"

            renameComponent(comp_Topology_Manager_Rest_Service, "Rest Service")
        }
    }

    layer {
        name = layer_legacy_driver
        addComponents comp_FSP1500_Device_Driver, comp_FSP150CC_Device_Driver
        addComponents comp_FSP150cp_Device_Driver, comp_FSP150egm_Device_Driver
        addComponents comp_Hatteras_Device_Driver
        addComponents comp_OTS1000_Device_Driver
    }

    diagram {
        name = diagram_legacy_driver

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_legacy_driver
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            maxStacking = 4

            // Shorten component display names to remove the ' Device Driver' suffix.
            renameRule({ name ->
                String diagramName = name;
                if (name.endsWith(" Device Driver")) {
                    diagramName = name.substring(0, name.length() - " Device Driver".length())
                }
                return diagramName;
            })
        }
    }

    layer {
        name = layer_third_party_driver
        addComponents comp_Juniper_Device_Driver, comp_Custom_Device_Driver, comp_Symmetricom_Device_Driver
        addComponents comp_Unmanaged_Device_Driver
    }

    diagram {
        name = diagram_third_party_driver

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_third_party_driver
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"

            // Shorten component display names to remove the ' Device Driver' suffix.
            renameRule({ name ->
                String diagramName = name;
                if (name.endsWith(" Device Driver")) {
                    diagramName = name.substring(0, name.length() - " Device Driver".length())
                }
                return diagramName;
            })
        }
    }

    layer {
        name = layer_osa_driver
        addComponents comp_OSA3230b_Device_Driver, comp_OSA3350_Device_Driver, comp_OSA3351_Device_Driver
        addComponents comp_OSA5331_Device_Driver, comp_OSA540X_Device_Driver, comp_OSA542x_Device_Driver
        addComponents comp_OSA54CR_Device_Driver, comp_OSA5548c_Device_Driver, comp_FSPsyncprob_Device_Driver
        addComponents comp_OSA_Driver
    }

    diagram {
        name = diagram_osa_driver

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_osa_driver
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            maxStacking = 4

            // Shorten component display names to remove the ' Device Driver' suffix.
            renameRule({ name ->
                String diagramName = name;
                if (name.endsWith(" Device Driver")) {
                    diagramName = name.substring(0, name.length() - " Device Driver".length())
                }
                return diagramName;
            })
        }
    }

    layer {
        name = layer_aos_driver
        addComponents comp_F8_Device_Driver, comp_F4_Device_Driver, comp_AOS_Device_Driver
    }

    diagram {
        name = diagram_aos_driver

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_aos_driver
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            maxStacking = 4

            // Shorten component display names to remove the ' Device Driver' suffix.
            renameRule({ name ->
                String diagramName = name;
                if (name.endsWith(" Device Driver")) {
                    diagramName = name.substring(0, name.length() - " Device Driver".length())
                }
                return diagramName;
            })
        }
    }

    layer {
        name = layer_f3_driver
        addComponents comp_F3_Device_Driver, comp_FSP150cm_Device_Driver, comp_FSP1XX_Device_Driver
        addComponents comp_FSP150egx_Device_Driver, comp_FSP210_Device_Driver
        addComponents comp_FSPTxx04_Device_Driver, comp_FSP20X_Device_Driver, comp_FSP_xg1xx_Device_Driver
    }

    diagram {
        name = diagram_f3_driver

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_f3_driver
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            maxStacking = 4

            // Shorten component display names to remove the ' Device Driver' suffix.
            renameRule({ name ->
                String diagramName = name;
                if (name.endsWith(" Device Driver")) {
                    diagramName = name.substring(0, name.length() - " Device Driver".length())
                }
                return diagramName;
            })
        }
    }

    layer {
        name = layer_pv_driver
        addComponents comp_PV_Device_Driver, comp_FSP_xg_mrv_Device_Driver, comp_FSP_z4806_Device_Driver
    }

    diagram {
        name = diagram_pv_driver

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_pv_driver
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            maxStacking = 4

            // Shorten component display names to remove the ' Device Driver' suffix.
            renameRule({ name ->
                String diagramName = name;
                if (name.endsWith(" Device Driver")) {
                    diagramName = name.substring(0, name.length() - " Device Driver".length())
                }
                return diagramName;
            })
        }
    }

    layer {
        name = layer_web_services
        addComponents comp_Jetty_Infrastructure, comp_REST_Infrastructure
    }

    layer {
        name = layer_performance_management
        addComponents comp_Performance_Management, comp_Streaming_Telemetry_Client
    }

    diagram {
        name = diagram_web_services

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_web_services
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            maxStacking = 4

            renameComponent(comp_Jetty_Infrastructure, "Jetty Infra")
            renameComponent(comp_REST_Infrastructure, "REST Infra")
        }
    }

    diagram {
        name = diagram_performance_management

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_performance_management
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            maxStacking = 4

            renameComponent(comp_Performance_Management, "Perf. Mgr. Core")
            renameComponent(comp_Streaming_Telemetry_Client, "Stream. Tel. Client")
        }
    }

    layer {
        name = layer_optical_router_driver_applications
        addComponents comp_Optical_Router_Driver_API
        addComponents comp_Juniper_Ptx_App, comp_Optical_Router_Test_Driver
    }

    diagram {
        name = diagram_optical_router_driver_applications

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_optical_router_driver_applications
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            maxStacking = 4

            renameComponent(comp_Juniper_Ptx_App, "Juniper Ptx")
        }
    }

    layer {
        name = layer_optical_router_driver
        addComponents comp_Optical_Router_Driver_Impl
        addComponents comp_Optical_Router_Driver_Kafka_Adapter
        addComponents comp_Optical_Router_Driver_Persistence
        addComponents comp_Optical_Router_Driver_Rest_Server_Adapter, comp_Optical_Router_Driver_Rest_Client_Adapter
        addComponents diagram_optical_router_driver_applications
    }

    diagram {
        name = diagram_optical_router_driver

        defaultNodeWidth = 50
        defaultNodeHeight = 10

        bind {
            layerName = layer_optical_router_driver
            orientation = "horizontal"
            layout = "below"
            textColor = "black"
            backgroundColor = "green"
            nodeColor = "green"
            nodeTextColor = "white"
            maxStacking = 4

            renameComponent(comp_Optical_Router_Driver_API, "API")
            renameComponent(comp_Optical_Router_Driver_Impl, "Impl")
            renameComponent(comp_Optical_Router_Driver_Kafka_Adapter, "Kafka")
            renameComponent(comp_Optical_Router_Driver_Persistence, "Persistence")
            renameComponent(comp_Optical_Router_Driver_Rest_Client_Adapter, "REST Client")
            renameComponent(comp_Optical_Router_Driver_Rest_Server_Adapter, "REST Server")
            renameComponent(diagram_optical_router_driver_applications, "Applications")
        }
    }
}
