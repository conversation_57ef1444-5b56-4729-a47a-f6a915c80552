/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 */

/*
 * Build fragment containing all the tasks needed for creation of the platform directory
 * that mirrors production build.
 */

import org.apache.tools.ant.taskdefs.condition.Os

import java.util.regex.Matcher

project.afterEvaluate {
    task setupServerPlatform() {
        // Enable only if building to platform directory
        dependsOn([ getTasksByName( 'installWar', true ) ])
        dependsOn([getTasksByName('replaceWarFile', true)])
        dependsOn(setupPlatformOS)
        dependsOn(setupLibs)
        dependsOn(setupDirs)

        List<String> serverDirs = [
                "activemq", "certs", "CustomProducts", "dat", "db", "Examples",
                "mappings", "mibs", "monitoringConfig",
                "NMHeapDumps", "templates", "test", "var", "ws",
        ]

        List<String> serverFiles = [
                "APACHE.txt", "BSD.txt", "CDDL.txt", "EPL.txt", "bin/fem", "bin/fnm", "fnmclientinstall.properties",
                "fnmclient.properties", "fnm.snmp.properties", "fnmtest.properties", "fnm.properties",
                "GPL.txt", "jtrace.cfg", "LGPL.txt", "LICENSE.txt", "log", "log4j2_client.xml", "log4j2.xml",
                "log4j_proxy.xml", "logging.properties", "bin/MakePDF",
                "migration.properties", "MPL.txt", "mtosi.properties", "pmEthernetTemplate.xml", "prunsrv32.exe",
                "prunsrv64.exe", "modules/nmscommon/src/main/resources/com/adva/nlms/common/version.properties", "limits.json"
        ]

        // Create a copy task for each directory to copy.
        // This avoids having to copy all directories when a single directory changes.
        serverDirs.each { d ->
            project.tasks.create(name: "setupDir_$d", type: Copy) {
                from(fileTree("$fspRoot/$d"))
                into("$serverRoot/$d")
                // Force read only files to be writable so that second copy will not fail
                fileMode = 0644
                enabled = buildPlatform
            }

            dependsOn("setupDir_$d")
            // Ensure copies are complete before building production artifacts
            project.tasks['prepareArtifactsMediation'].dependsOn("setupDir_$d")
        }

        // Create a copy task for files to copy.
        String taskName = "setupRootFiles"
        project.tasks.create(name: taskName, type: Copy) {
            serverFiles.each { f ->
                from("$fspRoot/$f")
            }
            into("$serverRoot")
            // Force read only files to be writable so that second copy will not fail
            fileMode = 0644
            enabled = buildPlatform
        }

        dependsOn(taskName)
        // Ensure copies are complete before building production artifacts
        tasks.prepareArtifactsMediation.dependsOn(taskName)
    }

    task setupClientPlatform(type: Copy) {
        List<String> clientDirs = [
                "clientupdater"
        ]
        List<String> clientFiles = [
                "fnmclientinstall.properties", "log4j2_client.xml"
        ]

        from(fspRoot)

        clientDirs.each { d ->
            include("$d/**")
        }

        clientFiles.each { f ->
            include("$f")
        }

        into(clientRoot)

        // Force read only files to be writable so that second copy will not fail
        fileMode = 0644
    }

// Method for creating/registering a service with windows
    def createWindowsService = { String prunsrv, String name, String mainClass, String start, String stop,
                                 String cp, String logpath, String stdoutFile, String stderrFile,
                                 String level, String lprefix, List<String> jvmOpts ->
        String jOpts = ""

        if (jvmOpts != null && !jvmOpts.isEmpty()) {
            jOpts += " --JvmOptions=" + jvmOpts.get(0)

            for(int i=1; i<jvmOpts.size(); i++) {
                jOpts += " ++JvmOptions=" + jvmOpts.get(i)
            }
        }


        String cmd = "$serverRoot\\$prunsrv //IS//$name --DisplayName=\"ADVA: development service $name\" " +
                "--Description=\"ADVA: development service $name\" --Startup=auto --StartPath=$serverRoot " +
                "--StartClass=$mainClass --StartMethod=$start --StartMode=jvm " +
                "--Jvm=$JDK\\jre\\bin\\server\\jvm.dll --Classpath=$cp "

        if (logpath != null) {
            cmd += "--LogPath=$logpath --LogLevel=$level "

            if (lprefix != null) {
                cmd += "--LogPrefix=$lprefix "
            }
            if (stdoutFile != null) {
                cmd += "--StdOutput=$stdoutFile "
            }
            if (stderrFile != null) {
                cmd += "--StdError=$logpath\\$stderrFile "
            }
            if (logpath != null) {
                cmd += "--LogPath=$logpath "
            }
            if (level != null) {
                cmd += "--LogLevel=$level "
            }
            if (lprefix != null) {
                cmd += "--LogPrefix=$lprefix "
            }
            if (stdoutFile != null) {
                cmd += "--StdOutput=$logpath\\$stdoutFile "
            }
            if (stderrFile != null) {
                cmd += "--StdError=$logpath\\$stderrFile "
            }
        }

        cmd += "$jOpts"

        def proc = cmd.execute()
        def output = new StringBuffer()
        proc.consumeProcessErrorStream(output)

        print("$proc.text\n")
        print("$output\n")
    }

    task setupLinuxPlatform() {
        if (buildPlatform) {
            dependsOn(setupServerPlatform)
        }

        File setenv = new File("$platformDir/$serverParentDirName/etc/setenv.sh")
        File shareDir = new File("$platformDir/$serverParentDirName/share")

        doLast {
            // Ensure directory is created
            setenv.getParentFile().mkdirs()

            setenv.write("#!/bin/sh\nNMS_HOME=$platformDir/$serverParentDirName\nNMS_NAME=\"Mosaic Network Controller\"\nexport NMS_HOME\nexport NMS_NAME")

            // Link in JRE to share directory
            shareDir.mkdirs()

            // create symbolic link from JDK jre to share/jre
            if (!new File("$shareDir/jre").exists() && new File("$JDK/jre").exists()) {
                ant.symlink(resource: "$JDK/jre", link: "$shareDir/jre")
            }

            // Make postrgres tools executable
            ant.chmod(perm: "+x") {
                fileset(dir: "$serverRoot/postgres/bin")
            }

            // create symbolic links for common scripts
            if (!new File("$serverRoot/fnm").exists()) {
                ant.symlink(resource: "$serverRoot/bin/fnm", link: "$serverRoot/fnm")
            }
            if (!new File("$serverRoot/fem").exists()) {
                ant.symlink(resource: "$serverRoot/bin/fem", link: "$serverRoot/fem")
            }
            if (!new File("$clientRoot/fnm").exists()) {
                ant.symlink(resource: "$serverRoot/bin/fnm", link: "$clientRoot/fnm")
            }

        }

        outputs.file(setenv)
        outputs.dir(shareDir)
    }

    task setupWindowsPlatform() {
        if (buildPlatform) {
            dependsOn(setupServerPlatform)
        }

        doLast {
            String prunsrv = "prunsrv32.exe"
            if (Os.isArch("amd64")) {
                prunsrv = "prunsrv64.exe"
            }

            boolean install = true
            // Install 'Development' mediation server service for use in batch scripts
            // First check to see if we need to install
            String servicesOutput = "wmic service get name,pathname".execute().text
            String[] serviceLines = servicesOutput.split("\n")
            serviceLines.each { String line ->
                // Get the pathname from this line
                Matcher m = line =~ /(?s)advamsDev\s+(.*)/
                if (m.matches()) {
                    String pathname = m.group(1)

                    if (pathname.startsWith(serverRoot.getCanonicalPath())) {
                        install=false
                        return
                    } else {
                        install=true
                        if (!briefOutput) {
                            logger.quiet("Uninstalling advamsDev service from another workspace\n")
                        }

                        // First delete the service then add it below
                        def proc = "$prunsrv //DS//advamsDev".execute()
                        def output = new StringBuffer()
                        proc.consumeProcessErrorStream(output)
                        print("$proc.text\n")
                        print("$output\n")
                        return
                    }
                }
            }

            if (install) {
                if (!briefOutput) {
                    logger.quiet("Installing advamsDev service\n")
                }
                try {
                    createWindowsService(prunsrv,
                            "advamsDev",           // Service Name
                            "com.adva.nlms.mediation.Launcher", // Main Class
                            "main",                             // Start method
                            "shutdown",                         // Stop method
                            "lib/mediation.jar",                // Classpath
                            "$serverRoot/var/log",              // logging path
                            "nmsservices-stdout.log",           // standard error log file
                            "nmsservices-stderr.log",           // standard error log file
                            "WARNING",                          // Logging level
                            "nmsservices-daemon",               // Loging prefix
                            // JVM Args
                            [ "-Xmx4000M", "-XX:HeapDumpPath=NMHeapDumps", "-XX:+HeapDumpOnOutOfMemoryError",
                              "-Xrs", "-Dcom.sun.xml.bind.v2.runtime.JAXBContextImpl.fastBoot=true",
                              "-Djtrace.logdir=var/log", "-Djava.awt.headless=true",
                              "-Dfile.encoding=UTF-8",
                              "-Djava.util.logging.config.file=./logging.properties",
                              "-Dorg.apache.activemq.SERIALIZABLE_PACKAGES=\"java.lang,java.util,org.apache.activemq,org.fusesource.hawtbuf,com.thoughtworks.xstream.mapper,ni,java.time,org.apache.commons.lang3.tuple,com.google.common.collect,java.beans,com.adva\"" ])

                } catch (Throwable t) {
                    logger.error("Unable to create windows service 'advamsDev', continuing with windows platform setup")
                    logger.debug("Error discovered while creating windows service", t)
                }
            } else {
                logger.info("advamsDev service already installed")
            }
        }
    }

    task setupPlatforms() {
        dependsOn(setupServerPlatform)
        dependsOn(setupClientPlatform)

        // Copy in new war projects outputs
        dependsOn(setupDeploy)
        dependsOn(setupEmptyDirs)

        if (Os.isFamily(Os.FAMILY_UNIX)) {
            // Configure for linux if running on linux
            dependsOn(setupLinuxPlatform)
        } else {
            dependsOn(setupWindowsPlatform)
        }
    }

    mod_mediation.project.tasks['createMonitoringConfigFiles'].dependsOn( ':setupDir_monitoringConfig' )
}
