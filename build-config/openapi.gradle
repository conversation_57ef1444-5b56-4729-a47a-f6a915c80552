/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 */

tasks.register('collectOpenapiManifests') {
    File openapiPublicationManifestFile = layout.buildDirectory.file('openapi/publication-manfest.yaml').get().asFile
    group('openapi')
    description("Create openapi documents for all project in the build and collect the artifactory publication info in $openapiPublicationManifestFile")

    outputs.files( openapiPublicationManifestFile )

    // Ensure generateOpenapi tasks are run before creating the archive of documents
    dependsOn( getTasksByName('publishOpenapi', true) )

    doLast {
        // Collect the publication data and combine into single manifest
        def mapper = jacksonObjectMapper

        // Create openapi publication yaml summary file
        Map<String, List<Map<String, String>>> openapiManifestMap = new HashMap<>()
        List<Map<String, String>> openapiArtifactList = new LinkedList<>()
        openapiManifestMap.put('publications', openapiArtifactList)

        fileTree(projectDir) {
            include '**/build/openapi/publication-manifest.yaml'
        }.files.each { File f ->
            Map<String, String> openapiManifestFragment = mapper.readValue(f, Map.class)
            openapiArtifactList.addAll(openapiManifestFragment.get("publications"))
            logger.info("Added {} to openapi publication manifest", openapiManifestFragment.get("publications"))
        }

        openapiPublicationManifestFile.parentFile.mkdirs()
        mapper.writeValue(openapiPublicationManifestFile, openapiManifestMap)
    }
}