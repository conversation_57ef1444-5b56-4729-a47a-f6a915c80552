/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 */
allprojects { Project p ->
    p.apply plugin: 'idea'

    idea {
        // Configure idea modules
        module {
            inheritOutputDirs = false
            outputDir = file('build/classes/java/main')
            if (p.file("src/test/java").exists()) {
                // Add unit test output dir
                testOutputDir = file('build/classes/java/test')
            }

            downloadJavadoc = true
            downloadSources = true
        }
    }
}

afterEvaluate {

    task ideaPreImport() {
        // If FSP has not yet been built, build it
        // Once built the mediation build directory will be created by gradle
        // There are two variations for the build dir--gradle style (build) and idea 2016.3+ style (out)
        if (!new File(mod_mediation.project.buildDir, mod_mediation.project.libsDirName).exists()) {
            dependsOn( all )
        }

        // By default go ahead and build the idea project files for this FSP build
        dependsOn('idea')

        // Copy aspectjweaver.jar to lib dir -- required for launching mediation server.
        dependsOn(installMandatoryLibs)

        // pre-create the idea wrapper launcher jars so idea launchers will be valid
        dependsOn("$path_mediation:createMediationLaunchWrapper")
        dependsOn("$path_mediation:createJmsLaunchWrapper")
        dependsOn("$path_frontend:createFrontendLaunchWrapper")
    }

    task ideaSetup() {
        dependsOn(updateLaunchers)
    }

// project specific idea configuration
    idea {
        project {
            // Configure the JDK to use for the generated project - can be changed through build flag IdeaJDK
            jdkName = IdeaJDK

            languageLevel = targetLevel

            if (ConfigProjectVcs) {
                // Use subversion as version control so you don't have to enable manually
                // Off by default since it often prohibits diffs from showing in version control window.
                // WARNING: If the project is closed, the project file is regenerated with gradle, and the project is
                // re-opened then you may not see any changes that were made previously in the VCS window.
                vcs = 'svn'
            }


            if (smallIdea != "") {
                // generate a partial idea project
                def ideaMods = []
                switch(smallIdea) {
                    case "frontend-web": // limit modules to those needed by frontend-web
                        ideaMods = [mod_nmscommon, mod_yfiles_web,
                                    mod_frontend, mod_gnss_common,  mod_webclient_launch,
                                    mod_solo_war, mod_tomcat_launcher ]
                        break;
                    default:
                        throw new RuntimeException("Unknown smallIdea configuration: '$smallIdea'")
                }
                if (ideaMods != []) {
                    modules = ideaMods.collect{m -> m.idea.module};
                }
            }

            // Raw XML Project Customizations
            ipr {
                withXml { XmlProvider xml ->
                    // Increase Compiler memory (in mbytes)
                    Node p = xml.node // 'project. node
                    Node c = p.find { component ->
                        component.@name == "CompilerConfiguration"
                    }

                    if (c != null) {
                        // Set proper heap size for compile in IDEA
                        Node o = c.find { option ->
                            option.@name == "BUILD_PROCESS_HEAP_SIZE"
                        }

                        if (o == null) {
                            new Node(c, "option",                                                      // Tag
                                    [ "name": "BUILD_PROCESS_HEAP_SIZE", "value": IdeaCompileMemory ]) // attributes

                        } else {
                            o.@value = IdeaCompileMemory
                        }

                        // No annotation processing for now as is not needed.
//                    NodeList annProcList = c.annotationProcessing
//
//                    if (annProcList == null || annProcList.size() == 0) {
//                        // Create annotation processing entry and enable processing
//                        Node annProc = new Node(c, "annotationProcessing")
//                        new Node(annProc, "profile", //Tag
//                                ["default": "true", "name": "default", "enabled": "true"] // Attributes
//                        )
//                    } else {
//                        // Ensure annotation processing is enabled
//                        Node annProc = annProcList.get(0)
//                        NodeList profList = annProc.profile
//                        if (profList == null || profList.size() == 0) {
//                            annProc.@enabled = "true"
//                            new Node(annProc, "profile", //Tag
//                                    ["default": "true", "name": "default", "enabled": "true"]) // Attributes
//                        } else {
//                            Node prof = profList.get(0)
//                            // Ensure enabled
//                            prof.@enabled = "true"
//                        }
//                    }
                    }

                    c = p.find { component ->
                        component.@name == "CopyrightManager"
                    }

                    if (c != null) {
                        // Add default ADVA copyright header settings if not already present
                        c.@default = "ADVA"

                        NodeList copyrightList = c.copyright

                        if (copyrightList == null || copyrightList.size() == 0) {
                            Node cr = new Node(c, "copyright")
                            new Node(cr, "option",
                                    [name: "notice",
                                     value: " Copyright \$today.year Adtran Networks SE. All rights reserved." +
                                             "\n\n Owner: $copyrightUserName"])
                            new Node(cr, "option", [name: "myName", value: "ADVA"])
                        }
                    }

                    // Add default launchers
                    c = p.find { component ->
                        component.@name == "ProjectRunConfigurationManager"
                    }

                    if (c == null) {
                        String activemqDir = "\$PROJECT_DIR\$/$serverPath/activemq"
                        String serverDir = "\$PROJECT_DIR\$/$serverPath"
                        String clientDir = "\$PROJECT_DIR\$/$clientPath"
                        // Not yet configured, add launchers
                        Node prcm = new Node(p, "component")
                        prcm.@name = "ProjectRunConfigurationManager"

                        if (smallIdea == "frontend-web") {
                            // Create an artifact copy
                            Node aconfig = new Node(p, "component", [name: "ArtifactManager"])
                            // aconfig.@name="ArtifactManager"
                            Node artifact = new Node(aconfig, "artifact", [name: "Copy styles"])
                            // artifact.@name = "Copy styles"
                            Node outputPath = new Node(artifact, "output-path", mod_webclient_launch.file("build/webapp/frontend/styles").getCanonicalPath())
                            // outputPath.text = mod_webclient_launch.file("build/webapp/frontend/styles").getCanonicalPath()
                            Node root = new Node(artifact, "root", [id: "root"])

                            createAppLauncher(prcm, "webui-launcher",
                                    // Main Class
                                    "com.adva.nlms.frontendweb.weblaunch.WebUiJetty",
                                    // Module name
                                    "webclient-launch",
                                    // VM Args
                                    "-Xmx2G",
                                    // App Args
                                    null,
                                    // Working dir
                                    "\$MODULE_WORKING_DIR\$",
                                    // Artifacts
                                    "Copy styles"
                            )

                        }

                        createJarLauncher(prcm, "JMS Server Wrapper",
                                "Messaging Log", "$serverDir/var/log/messaging.log",
                                // Main Class
                                "\$PROJECT_DIR\$/build/launchers/MainJmsJarWrapper.jar",
                                // VM Args
                                "-Xmx1G -Dactivemq.home=$activemqDir " +
                                        "-Dactivemq.base=$activemqDir " +
                                        "-Dactivemq.conf=$activemqDir/conf " +
                                        "-Dactivemq.data=$activemqDir/data " +
                                        "-Djava.rmi.server.hostname=localhost " +
                                        "-Dorg.apache.activemq.SERIALIZABLE_PACKAGES=\"java.lang,java.util,org.apache.activemq,org.fusesource.hawtbuf,com.thoughtworks.xstream.mapper,ni,java.time,org.apache.commons.lang3.tuple,com.google.common.collect,java.beans,com.adva\" " +
                                        "-Dlog4j.configurationFile=file:$activemqDir/conf/log4j2.xml",
                                // App Args
                                "start",
                                // Working dir
                                serverDir)

                        createJarLauncher(prcm, "Javafx Client Wrapper",
                                "Frontend Log", "\$USER_HOME\$/FSP Network Manager/log/frontend.log",
                                // Main Class
                                "\$PROJECT_DIR\$/build/launchers/DefaultContextManagerJarWrapper.jar",
                                // VM Args
                                "-Xmx500M " +
                                        "-Dorg.apache.activemq.SERIALIZABLE_PACKAGES=\"java.lang,java.util,org.apache.activemq,org.fusesource.hawtbuf,com.thoughtworks.xstream.mapper,ni,java.time,org.apache.commons.lang3.tuple,com.google.common.collect,java.beans,com.adva\" " ,
                                // App Args
                                "-application frontend -server localhost -user admin -password ChgMeNOW ",
                                // Working dir
                                clientDir)

                        String agentArgs = ''
                        if (!weaveEnc) {
                            configurations.aspectjWeaver.each {File f ->
                                agentArgs += " -javaagent:"+f.getCanonicalPath()
                            }
                            configurations.eclipseLinkWeaver.each {File f ->
                                agentArgs += " -javaagent:"+f.getCanonicalPath()
                            }
                        }

                        createJarLauncher(prcm, "Mediation Server Wrapper",
                                "Mediation Log", "$serverDir/var/log/mediation.log",
                                // Main Class
                                "\$PROJECT_DIR\$/build/launchers/LauncherMediationJarWrapper.jar",
                                // VM Args
                                "-Xmx$MediationRuntimeMemory " +
                                        "-Djava.util.logging.config.file=./logging.properties " +
                                        "-Djakarta.xml.bind.JAXBContextFactory=org.glassfish.jaxb.runtime.v2.ContextFactory " +
                                        "-Dorg.apache.activemq.SERIALIZABLE_PACKAGES=\"java.lang,java.util,org.apache.activemq,org.fusesource.hawtbuf,com.thoughtworks.xstream.mapper,ni,java.time,org.apache.commons.lang3.tuple,com.google.common.collect,java.beans,com.adva\" " +
                                        "-Djavax.net.ssl.keyStore=activemq/conf/client.ks " +
                                        "-Djavax.net.ssl.keyStorePassword=nmsServer " +
                                        "-Djavax.net.ssl.trustStore=activemq/conf/client.ts " +
                                        "-Djava.library.path=lib" +
                                        "--add-opens=java.base/java.net=ALL-UNNAMED" +
                                        "--add-opens=java.base/sun.net.www.protocol.https=ALL-UNNAMED" +
                                        agentArgs,
                                // App Args
                                null,
                                // Working dir
                                serverDir)

                        // Create default gradle launchers for new project
                        createGradleLauncher(prcm, "ENC Build", "\$PROJECT_DIR\$",
                                ["all"], ["--parallel"])
                        createGradleLauncher(prcm, "ENC Build with Test", "\$PROJECT_DIR\$",
                                ["allCompiles"], ["--parallel"])
                        createGradleLauncher(prcm, "ENC Weave", "\$PROJECT_DIR\$/modules/mediation",
                                ["weaveOnly"], ["--parallel"])
                        createGradleLauncher(prcm, "ENC DB create", "\$PROJECT_DIR\$",
                                ["createDatabaseOnly"], ["--parallel --offline"])
                        createGradleLauncher(prcm, "ENC RE-Build", "\$PROJECT_DIR\$",
                                ["clean", "all"], ["--parallel"])
                        createGradleLauncher(prcm, "ENC Frontend Build", "\$PROJECT_DIR\$",
                                [":modules:client:frontend:_build_", ":modules:client:frontend:installReports"], ["--parallel"])
//                    createGradleLauncher(prcm, "FSP-NM Wfe Build", "\$PROJECT_DIR\$",
//                            [":modules:client:frontend-web:_build"], ["--parallel"])
                        createGradleLauncher(prcm, "Idea Project Update", "\$PROJECT_DIR\$",
                                ["idea"], ["--parallel"])
                        createGradleLauncher(prcm, "Idea Project Clean and Create", "\$PROJECT_DIR\$",
                                ["cleanIdea", "idea"], ["--parallel"])
                        createGradleLauncher(prcm, "ENC Refresh Resources", "\$PROJECT_DIR\$",
                                ["processResources", "processTestResources", "copyResources"], ["--parallel"])
                        def launchers = [
                                ":modules:mediation:createMediationLaunchWrapper",
                                ":modules:mediation:createJmsLaunchWrapper",
                                ":modules:client:frontend:createFrontendLaunchWrapper",
                        ]
                        createGradleLauncher(prcm, "Launch Wrappers Update", "\$PROJECT_DIR\$",
                                launchers,
                                ["--parallel"])
                    }
                }
            }

        }
    }

}

mod_mdtest.project.idea {
    module {
        testOutputDir = file('build/classes/java/test')
    }
}

mod_event_api.project.idea {
    // Override idea module structure for mediation as it has weaving to contend with
    module {
        sourceDirs += file('src/generator/java')
        sourceDirs += file('src/generator/resources')
        // resourceDirs += file('src/generator/resources')
        generatedSourceDirs +=  layout.buildDirectory.file("generatedSources").get().asFile
//        outputDir = file(finalOutputDir)
    }
}

mod_mediation.project.afterEvaluate {
    idea {
        // Override idea module structure for mediation as it has weaving to contend with
        module {
            sourceDirs += file('src/generator/java')
            sourceDirs += file('src/generator/resources')
            // resourceDirs += file('src/generator/resources')
            generatedSourceDirs += layout.buildDirectory.file("generatedSources").get().asFile
            outputDir = layout.buildDirectory.file( "classes/java/main" ).get().asFile
        }
    }
}

mod_nmscommon.project.afterEvaluate {
    idea {
        module {
            // give hint for generated source dirs
            generatedSourceDirs += layout.buildDirectory.file("generatedSources").get().asFile
        }
    }
}

mod_polling_api.project.afterEvaluate {
    idea {
        // Override idea module structure for mediation as it has weaving to contend with
        module {
            generatedSourceDirs += layout.buildDirectory.file("generatedSources").get().asFile
        }
    }
}

mod_polling_impl.project.afterEvaluate {
    idea {
        // Override idea module structure for mediation as it has weaving to contend with
        module {
            generatedSourceDirs += layout.buildDirectory.file("generatedSources").get().asFile
        }
    }
}

mod_planner_export_api.project.afterEvaluate {
    idea {
        // Override idea module structure for mediation as it has weaving to contend with
        module {
            generatedSourceDirs += layout.buildDirectory.file("generatedSources").get().asFile
        }
    }
}

mod_planner_export_impl.project.afterEvaluate {
    idea {
        // Override idea module structure for mediation as it has weaving to contend with
        module {
            generatedSourceDirs += layout.buildDirectory.file("generatedSources").get().asFile
        }
    }
}

mod_eth_f3_inv_api.project.afterEvaluate {
    idea {
        // Override idea module structure for mediation as it has weaving to contend with
        module {
            sourceDirs += file('src/generator/java')
            sourceDirs += file('src/generator/resources')
            // resourceDirs += file('src/generator/resources')
            generatedSourceDirs += layout.buildDirectory.file("generatedSources").get().asFile
//        outputDir = file(finalOutputDir)
        }
    }
}

