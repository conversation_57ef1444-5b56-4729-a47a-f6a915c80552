/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 */

import org.apache.tools.ant.taskdefs.condition.Os

define('cyclonedxCli', 'docker run --rm cyclonedx/cyclonedx-cli',
        'The cyclonex cli command to use with BOM generation')

define('configureFnmProperties', false,
        'If set to true the values of fnmPropertiesOverride will be added to fnm.custom.properties')

define('fnmPropertiesOverride', '',
       'String of name=value pairs separated by "\\n" defining items to add to fnm.custom.properties')

define('flexeraServer', 'https://172.27.3.126:7071',
        'The primary flexera server to add to fnm.custom.properties when configureFnmProperties=true')
define('backupFlexeraServer', 'https://172.27.3.127:7071',
        'The backup flexera server to add to fnm.custom.properties when configureFnmProperties=true')

// No bypass by default but can be set to...
// ENC-HA-STD, ENC-EOD, ENC-CBM, ENC-CRYPTO, ENC-ESD, ENC-BWM, ENC-MTOSI, ENC-EPD, ENC-EFD, ENC-ESAMG, ENC-ESAMP, ENC-SDN-TAPI, ENC-SDN-PRESTO, ENC-SERVER-R12.X
define('bypassLicenses', '',
        'Add the comma separated list of bypass licenses to fnm.custom.properties when configureFnmProperties=true')

define('ibuildEnable', false,
        "If set to true then include external gradle projects as maven local dependencies instead of as gradle projects for independent builds")

define('ibuildGroupName', "com.adtran.mnc",
        "The group name to use for common MNC artifacts shared across independent builds")

// Build versioning and related settings
def versionProperties = new Properties()

// For now only process version.properties if it can be found.
// There are cases where this is not needed but need other information defined in version.properties is required
// such as initializing settings.gradle to read from artifactory repositories.
File versionPropertiesFile = file("$project.repositoryRoot/modules/nmscommon/src/main/resources/com/adva/nlms/common/version.properties")
if (versionPropertiesFile.exists()) {
    versionPropertiesFile.withInputStream { versionProperties.load(it) }

    // Add version info as EXT properties
    define('RawBuildNumber', versionProperties.get('BUILD_NUMBER'),
            'Raw build number as defined in version.properties')
    define('BuildNumber', "B" + versionProperties.get('BUILD_NUMBER'),
            'The build release number used in the production build process')
    define('MajorVersion', versionProperties.get('MAJOR_VERSION'),
            'ENC major release version')
    define('MinorVersion', versionProperties.get('MINOR_VERSION'),
            'ENC minor release version')
    define('PatchVersion', versionProperties.get('PATCH_VERSION'),
            'ENC patch version as specified in version.properties')
    define('CompatibleVersions', versionProperties.get('COMPATIBLE_VERSIONS'),
            'ENC compatibile versions value as specified in version.properties')
    define('IsBeta', versionProperties.get('IS_BETA'),
            'True if this is a BETA release for ENC production build')
    define('BetaString', versionProperties.get('BETA_STRING'),
            'ENC BETA string as specified in version.properties')
    define('IsPatch', versionProperties.get('IS_PATCH'),
            'True if ENC production build is a patch as specified in version.properties')
    define('PatchDescription', versionProperties.get('PATCH_DESCRIPTION'),
            'Description of ENC patch production build as specified in version.properties')
    define('BuildNumberX2', versionProperties.get('BUILD_NUMBERx2'),
            'ENC build number times 2 as specified in version.properties')
    define('BuildTag', versionProperties.get('BUILD_TAG', ''),
            'ENC production build tag as specified in version.properties')

    String ENC_VERSION = versionProperties.get('MAJOR_VERSION')+'.'+versionProperties.get('MINOR_VERSION')+'.'+versionProperties.get('PATCH_VERSION')

    define('Version', ENC_VERSION, 'The ENC production version number (without build number)')

    define('SnapshotVersion', ENC_VERSION + '-SNAPSHOT', 'ENC production SNAPSHOT version number')

    define('ibuildVersion', "$ENC_VERSION-SNAPSHOT",
            "Version to use for common artifacts shared with independent spring boot application builds")
} else {
    throw new GradleException("Unable to find version.properties file ("+versionPropertiesFile+")")
}

define('publicationHost', 'gdn-s-sitnms1.advaoptical.com',
        'Server repository for development production builds')
define('publicationUser', 'publisher',
        'User for publishing development production builds')

define('releasePublicationHost', 'ares.advaoptical.com', 'Server for storing release builds')
define('releasePublicationUser', 'nms-usr', 'User id used for publication of release builds to release server')

define('publicationPrivateCertificate', 'Unknown',
        'The private certificate used for publication of production build artifacts. Supplied in automated TC jobs.')

// sonar parameters
define('sonarServer', 'http://muc-vs-stgsonar:9000',
        'The address of the Sonar server to publish static analysis results')
define('sonarToken', 'NOT DEFINED',
        'Sonar token used for server authentication to publish static analysis results')
define('sonarProject', 'ENC',
        'The Sonar project name to use when publishing static analysis results')
define('sonarProjectName', 'ENC',
        'The Sonar project name to use when publishing static analysis results')
define('sonarProfile', 'Sonar way',
        'The Sonar profile used for static analysis')
define('sonarOrganization', 'adtran.ensemble.controller',
        'Sonar setting to use for Sonar static analysis')

define('sonarScannerEngineJavaOpts', '-Xmx30G', 'Java options for sonar-scanner-engine')

define('cacheServer', '', 'The gradle build cache server to use to avoid building objects locally')

define('SnmpTrapPort', 162, 'The SNMP trap port to use when running ENC mediation server')

define('enableSDN', false, 'Enables SDN module')

if (Os.isFamily(Os.FAMILY_WINDOWS)) {
    define('isWindows', true, 'True if the build is running on windows')
    define('osFamily', 'windows', 'OS family string of the build: windows, unix, mac')
} else {
    define('isWindows', false, 'True if the build is running on windows')
}

if (Os.isFamily(Os.FAMILY_UNIX)) {
    define('isUnix', true, 'True if the build is running on linux/unix')
    define('osFamily', 'unix', 'OS family string of the build: windows, unix, mac')
} else {
    define('isUnix', false, 'True if the build is running on linux/unix')
}

if (Os.isFamily(Os.FAMILY_MAC)) {
    define('isMac', true, 'True if the build is running on mac/apple')
    define('osFamily', 'mac', 'OS family string of the build: windows, unix, mac')
} else {
    define('isMac', false, 'True if the build is running on mac/apple')
}

define('cleanFilePatterns', '*.hprof',
        'comma separated file system wildcard patterns used for cleaning up files on team city agents')

define('cleanFileAgeDays', '7', 'The number of days beyond which files should be removed on team city agents')

define('failBuildOnInvalidBom', true,
        'If true the build will fail on BOM validation error, if false build will succeed')
