<?xml version="1.0" encoding="UTF-8"?>
<!--
  -  Copyright 2023 Adtran Networks SE. All rights reserved.
  -
  -  Owner: __OWNER_NOT_YET_ASSIGNED__
  -->

<project name="Handling CUBiT PBL API from ant">

  <!-- load the build properties (ant task related) -->
  <property file="build.properties" />

<!--** General targets **************************************************************-->
  <target name="upload" description="Upload file (${file}) to P<PERSON> with given type (${path_type}), path (${pbl_path}) and description (${pbl_description})">
    <exec executable="${pbl}" failonerror="true">
      <arg value="upload"/>
      <arg value="--api-user=${pbl_user}"/>
      <arg value="--api-key=${pbl_key}"/>
      <arg value="--api-url=${pbl_url}"/>
      <arg value="--project=${pbl_project}"/>
      <arg value="--type=pub"/>
      <arg value="--remotepath=${pbl_path}"/>
      <arg value="--desc=${pbl_description}"/>
      <arg value="--force"/>
      <arg value="${file}"/>
    </exec>
  </target>

  <target name="delete" description="Delete file in PBL with given type (${path_type}) and path (${pbl_path})">
    <exec executable="${pbl}">
      <arg value="delete"/>
      <arg value="--api-user=${pbl_user}"/>
      <arg value="--api-key=${pbl_key}"/>
      <arg value="--api-url=${pbl_url}"/>
      <arg value="--project=${pbl_project}"/>
      <arg value="--type=pub"/>
      <arg value="--remotepath=${pbl_path}"/>
      <arg value="--force"/>
    </exec>
  </target>

</project>
