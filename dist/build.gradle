/*
 *   Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *   Owner:
 */

import org.apache.tools.ant.taskdefs.condition.Os

import java.time.LocalDateTime

plugins {
    id 'java'
    id 'com.adva.gradle.plugin.image-publisher'
}
apply from: "$rootProject.projectDir/parameters.gradle"

imagePublisher {
    // Use closure to identify files to be uploaded
    imageFiles = {
        logger.quiet("\nStarting production image publication...\n")

        String localImagePath = "dist/"
        String imageFileName

        if (Os.isFamily(Os.FAMILY_UNIX)) {
            localImagePath += "lnx"
            imageFileName = "Mosaic_Network_Controller_for_Linux_v${Version}-${BuildNumber}.tar"
        } else if (Os.isFamily(Os.FAMILY_WINDOWS)) {
            localImagePath += "win/extras"
            imageFileName = "Mosaic_Network_Controller_for_Windows_v${Version}-${BuildNumber}-*.exe"
        } else {
            throw RuntimeException("Unsupported operating system for image publication")
        }

        FileTree images = rootProject.fileTree(".") {
            include "$localImagePath/$imageFileName"
        }

        return images
    }
}

Properties setEnvSh = new Properties()
file("$fspRoot/dist/lnx/setenv.sh").withInputStream { setEnvSh.load(it) }

def getProductVersion = { String product ->
    String PRODUCT_VERSION = setEnvSh.get(product)

    if ( PRODUCT_VERSION == null) {
        // last effort fallback - Default to same as ENC
        PRODUCT_VERSION = "${MajorVersion}.${MinorVersion}.${PatchVersion}-SNAPSHOT"
    } else {
        // Remove any quotes that may be on the value
        PRODUCT_VERSION =  PRODUCT_VERSION.replaceAll('"', '')
    }
    return PRODUCT_VERSION
}

// Define some constants for directories and files used in production build
// These ext variables are available by default in child projects
ext {
    // If built locally the production bundles are found in build/archives,
    // for Team City the files are placed in dist/archives so it is
    // not impacted by 'spotless' target
    artifactsDir = rootProject.file("dist/archives")

    if (!artifactsDir.exists()) {
        artifactsDir = rootProject.file("build/archives")
    }

    // These artifacts are created by the root build 'packageArtifacts' task
    // These artifacts will need to be obtained somehow since the night build TC job
    // will create these artifacts and may run on a separate agent.
    mediationLibBundle = project.file("$artifactsDir/production-mediation-lib.zip")
    mediationConfigBundle = project.file("$artifactsDir/production-mediation-config.zip")
    mediationWarBundle = project.file("$artifactsDir/production-mediation-war.zip")
    frontendBundle = project.file("$artifactsDir/production-frontend.zip")
    clientUpdaterBundle = project.file("$artifactsDir/production-client-updater.zip")

    BNUM = BuildNumber.substring(1)

    assembleClientDir = project.file("$project.buildDir/production/assemble/client")
    packageClientDir = project.file("$project.buildDir/production/package/client")

    NI_VERSION = System.getenv('NI_VERSION')

    if (NI_VERSION == null) {
        NI_VERSION = getProductVersion("NI_VERSION")
    }

    TAPI_VERSION = System.getenv('TAPI_VERSION')

    if (TAPI_VERSION == null) {
        TAPI_VERSION = getProductVersion("TAPI_VERSION")
    }

    CSM_VERSION = System.getenv('CSM_VERSION')

    if (CSM_VERSION == null) {
        CSM_VERSION = getProductVersion("CSM_VERSION")
    }

    // Ensure report dir exists
    mkdir(project.rootProject.buildDir)

    // Report a production build problem.
    // The implementation will write to a file that can be added to TC job as an artifact
    reportError = { String message ->
        File f = file("$project.rootProject.buildDir/production-error.log")
        f.append("\n")
        f.append(LocalDateTime.now())
        f.append(": ")
        f.append(message)
        f.append("\n")
    }

    requireFile = { File file ->
        if (!file.exists() || !file.isFile()) {
            reportError("Required file $file is missing from the ENC workspace")
        }
    }

    requireDirectory = { File file ->
        if (!file.exists() || !file.isDirectory()) {
            reportError("Required directory $file is missing from the ENC workspace")
        }
    }

    // Settings for ELS artifactory repository to fetch ELS artifacts
    elsReleaseVersion = 'R7.1.1'
    elsReleaseVersionSuffix = 'FINAL3'

    elsRepositoryURL = "https://gdn-artifactory.rd.advaoptical.com/artifactory/ELS/$elsReleaseVersion/$elsReleaseVersionSuffix"

    elsLinuxArtifact = "Adtran_Embedded_License_Server_${elsReleaseVersion}_unix64.tgz"
    elsWindowsArtifact = "Adtran_Embedded_License_Server_${elsReleaseVersion}_win64.zip"

     elsLinuxArtifactURL = "$elsRepositoryURL/$elsLinuxArtifact"
    // --------------------------------------------------------------------------------------
    // ELS for Windows hardcoded due lack of support ELS package for Windows
    //elsWindowsArtifactURL = "$elsRepositoryURL/$elsWindowsArtifact"
    elsWindowsArtifactURL ="https://gdn-artifactory.rd.advaoptical.com/artifactory/ELS/R6.1.1/RC01/Adtran_Embedded_License_Server_R6.1.1_win64.zip"
    //---------------------------------------------------------------------------------------
}

String LNX_INSTALL_DIRECTORY = System.getenv('LNX_INSTALL_DIR')

if (LNX_INSTALL_DIRECTORY == null || LNX_INSTALL_DIRECTORY.isEmpty()) {
    LNX_INSTALL_DIRECTORY = "$project.projectDir/lnx"
}

configurations {
    filezilla
    win32_libraries
    docs
    docsOther
    cmsw
    svp
    ni_snapshot
    jreWin
}

dependencies {
    filezilla dep_win_filezilla

    docs dep_enc_docs

    docsOther dep_enc_docs_other

    svp dep_svp

    cmsw dep_enc_cmsw

    win32_libraries dep_win32_libraries

    jreWin group: 'com.adva.enc.production.distribution.windows', name: 'jre', version: '**********', ext: 'zip'

    ni_snapshot group: "com.adva", name: "ni", version: NI_VERSION, ext: "tgz"
}

// Remove night build files copied into the workspace
task cleanDist(type: Delete) {
    delete(artifactsDir)
}

// This task is deprecated with newer lnx/build.gradle.
// The gradle linux production build script builds the linux install without need for this intermediate copy.
task fetchTarArtifacts(type: Copy) {
    from configurations.ni_snapshot

    into LNX_INSTALL_DIRECTORY
}

// prepare empty directories that must be included in the client upload zip file
task prepareClientUpload() {
    doFirst {
        mkdir "$assembleClientDir/lib/endorsed"
        mkdir "$assembleClientDir/certs"
        mkdir "$assembleClientDir/log"
        mkdir "$assembleClientDir/var/log"
        mkdir "$assembleClientDir/backup"
    }
}

task packageClientUpload(type: Zip) {
    group('enc.production')
    description('Create the client upload bundle used to upgrade the ENC client')

    dependsOn(prepareClientUpload)
    dependsOn(':modules:client:frontend:yfilesCopy')
    dependsOn(':modules:client:frontend:yguardComplete')
    // Set soft dependencies to quiet gradle errors.
    mustRunAfter("win:assembleProductionBundles")
    mustRunAfter(":packageFrontendArtifacts")

    archiveBaseName = "${Version}-${BNUM}"
    archiveExtension = "zip"

    destinationDirectory = packageClientDir

    String ziproot = "${Version}-${BNUM}"

    from(zipTree(frontendBundle)) {
        into ziproot

        doLast {
            requireFile(frontendBundle)
        }
    }

    from(fileTree(fspRoot)) {
        include "mibs/**"
        into(ziproot)

        doLast {
            requireDirectory(rootProject.file("mibs"))
        }
    }

    from(fileTree(fspRoot)) {
        include "activemq/**"
        into(ziproot)

        doLast {
            requireDirectory(rootProject.file("activemq"))
        }
    }

    from { zipTree { configurations.svp.singleFile } } {
        into("$ziproot/svp")
    }

    from { zipTree { configurations.cmsw.singleFile } } {
        into("$ziproot/cmsw")
    }

    from(fileTree(fspRoot)) {
        include "dat/**"
        into(ziproot)

        doLast {
            requireDirectory(rootProject.file("dat"))
        }
    }

    from { zipTree { configurations.win32_libraries.singleFile } } {
        into(ziproot)
    }

    from("$fspRoot/launcher/fem/Release/fem.exe") {
        into(ziproot)

        doLast {
            requireFile(rootProject.file("launcher/fem/Release/fem.exe"))
        }
    }

    from { configurations.filezilla.singleFile } {
        into("$ziproot/filezilla-install")

        rename { String name ->
            // Install anywhere needs a very specific name for the installer, change it here
            // If filezilla is upgraded, maybe install anywhere can use the name from artifactory
            return "FileZilla_Server-0_9_60_2.exe"
        }
    }

    from(fileTree(fspRoot)) {
        include "*.txt"
        include "log4j2_client.xml"
        into(ziproot)

        doLast {
            requireFile(rootProject.file("APACHE.txt"))
            requireFile(rootProject.file("BSD.txt"))
            requireFile(rootProject.file("CDDL.txt"))
            requireFile(rootProject.file("EPL.txt"))
            requireFile(rootProject.file("GPL.txt"))
            requireFile(rootProject.file("LGPL.txt"))
            requireFile(rootProject.file("LICENSE.txt"))
            requireFile(rootProject.file("MPL.txt"))
            requireFile(rootProject.file("log4j2_client.xml"))
        }
    }

    from { zipTree { configurations.jreWin.singleFile } } {
        into("$ziproot/jre")
    }

    from { zipTree { configurations.docs.singleFile } } {
        exclude "MNC_Administrator_Manual.pdf"

        into("$ziproot/docs")
    }

    from { zipTree { configurations.docsOther.singleFile } } {
        into("$ziproot/docs")
    }

    from(fileTree(assembleClientDir)) {
        into(ziproot)
    }

    duplicatesStrategy DuplicatesStrategy.EXCLUDE
}
