User_Alias	ENCINSTALLER = USER_NAME

Cmnd_Alias      INSTALL_ENC_CMDS_SYS = ENC_HOME/bin/fnm.server, \
                        ENC_HOME/postgres/support-files/postgres.server, \
                        /usr/bin/systemctl start fnmserver.service, \
                        /usr/bin/systemctl stop fnmserver.service, \
                        /usr/bin/systemctl restart fnmserver.service, \
                        /usr/bin/systemctl start postgres.service, \
                        /usr/bin/systemctl stop postgres.service, \
                        /usr/bin/systemctl restart postgres.service, \
                        /usr/bin/systemctl start patroni, \
                        /usr/bin/systemctl stop patroni, \
                        /opt/adva/fsp_nm_ni/ni-ctl.sh start, \
                        /opt/adva/fsp_nm_ni/ni-ctl.sh stop


Cmnd_Alias      INSTALL_ENC_CMDS2 = ENC_HOME/bin/snmpforwarder.sh, \
                                    /usr/sbin/setcap cap_net_bind_service+ep NMS_HOME/share/jre/bin/java, \
                                    /sbin/ldconfig, \
                                    /bin/su USER_NAME, \
                                    /bin/su - USER_NAME, \
                                    /usr/bin/systemctl daemon-reload, \
                                    /usr/bin/systemctl enable /usr/lib/systemd/system/postgres.service, \
                                    /usr/bin/systemctl enable /usr/lib/systemd/system/fnmserver.service, \
                                    /usr/bin/systemctl disable /usr/lib/systemd/system/postgres.service, \
                                    /usr/bin/systemctl disable /usr/lib/systemd/system/fnmserver.service, \
                                    /bin/chmod 644 /usr/lib/systemd/system/postgres.service, \
                                    /bin/chmod 644 /usr/lib/systemd/system/fnmserver.service, \
                                    /usr/sbin/userdel postgres, \
                                    /usr/sbin/groupdel postgres, \
                                    /usr/bin/rm, \
                                    NMS_HOME/ni_install_files/installni.sh, \
                                    NI_DIR/sbin/uninstallni.sh

Cmnd_Alias      INSTALL_ENC_CMD_TEE = /usr/bin/tee /proc/sys/kernel/shmmax, \
                                      /usr/bin/tee -a /etc/sysctl.conf, \
                                      /usr/bin/tee /etc/ld.so.conf.d/java.conf

Cmnd_Alias      INSTALL_ENC_CMD_SED = /bin/sed -i /*/ /etc/sysctl.conf, \
                                      /bin/sed -i s\#*\# /usr/lib/systemd/system/fnmserver.service, \
                                      /bin/sed -i [/|s]* /usr/lib/systemd/system/fnmserver.service, \
                                      /bin/sed -i [/|s]* /usr/lib/systemd/system/postgres.service, \
                                      /bin/sed -i [/|s]* /usr/lib/systemd/system/patroni.service

Cmnd_Alias      INSTALL_ENC_CMD_LN = /usr/bin/ln -s NMS_HOME/fsp_nm/postgres/support-files/postgres.server /etc/rc0.d/K07postgres, \
                                     /usr/bin/ln -s NMS_HOME/fsp_nm/postgres/support-files/postgres.server /etc/rc3.d/S96postgres, \
                                     /usr/bin/ln -s NMS_HOME/fsp_nm/postgres/support-files/postgres.server /etc/rc5.d/S96postgres, \
                                     /usr/bin/ln -s NMS_HOME/fsp_nm/postgres/support-files/postgres.server /usr/lib/systemd/system/postgres, \
                                     /usr/bin/ln -s /usr/lib64/libreadline.so.7 /usr/lib64/libreadline.so.6, \
                                     /usr/bin/ln -s NMS_HOME/fsp_nm/bin/fnm.server /etc/rc0.d/K05fnm.server, \
                                     /usr/bin/ln -s NMS_HOME/fsp_nm/bin/fnm.server /etc/rc3.d/S98fnm.server, \
                                     /usr/bin/ln -s NMS_HOME/fsp_nm/bin/fnm.server /etc/rc5.d/S98fnm.server, \
                                     /usr/bin/ln -s NMS_HOME/fsp_nm/bin/fnm.server /usr/lib/systemd/system/fnmserver, \
                                     /usr/bin/ln -s NMS_HOME/fsp_nm/bin/fnm /usr/bin

Cmnd_Alias      INSTALL_ENC_CMD_CP = /usr/bin/cp NMS_HOME/fsp_nm/postgres/support-files/postgres.service /usr/lib/systemd/system/, \
                                     /usr/bin/cp setenv.sh /etc/setenv.sh, \
                                     /usr/bin/cp NMS_HOME/fsp_nm/postgres/support-files/fnmserver.service /usr/lib/systemd/system/

Cmnd_Alias       INSTALL_ENC_CMD_YUM = /usr/bin/yum -C -y localinstall make*.rpm, \
                                       /usr/bin/yum -C -y localinstall compat-openssl*.rpm, \
                                       /usr/bin/rpm -i make*.rpm, \
                                       /usr/bin/rpm -i compat-openssl*.rpm


ENCINSTALLER    ALL = SETENV:INSTALL_ENC_CMDS2, \
                             INSTALL_ENC_CMD_TEE, \
                             INSTALL_ENC_CMD_SED, \
                             INSTALL_ENC_CMD_LN, \
                             INSTALL_ENC_CMD_CP, \
                             INSTALL_ENC_CMD_YUM

ENCINSTALLER    ALL = NOPASSWD:SETENV:INSTALL_ENC_CMDS_SYS
