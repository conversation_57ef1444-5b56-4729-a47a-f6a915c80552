#!/bin/bash
. ./setenv.sh
MGR_STR="${NMS_NAME} ${MNC_VERSION}"
AF=fsp_nm.tar
AD=`pwd`

install_user=$(id -u -n)
install_group=$(id -g -n)

serviceFNM()
{
  ACTION=$1
  if [ $ACTION != "start" ] && [ $ACTION != "stop" ] && [ $ACTION != "restart" ]
  then
    echo "Unknown Mosaic Network Controller service action"
  else
    if [ "$install_user" = "root" ]
    then
      if [ -d "/usr/lib/systemd/system" ]
      then
        /usr/bin/systemctl -q is-active fnmserver.service
        RET=$?
        /usr/bin/systemctl $ACTION fnmserver.service
        #handle migration cases from non-systemd to systemd usage
        if [ $ACTION = "stop" ] && [ $RET -ne 0 ]
        then
          ${NMS_HOME}/fsp_nm/bin/fnm.server stop
        fi
      else
        ${NMS_HOME}/fsp_nm/bin/fnm.server $ACTION
      fi
    else
      if [ -d "/usr/lib/systemd/system" ]
      then
        /usr/bin/systemctl -q is-active fnmserver.service
        RET=$?
        sudo /usr/bin/systemctl $ACTION fnmserver.service
        #handle migration cases from non-systemd to systemd usage
        if [ $ACTION = "stop" ] && [ $RET -ne 0 ]
        then
          sudo ${NMS_HOME}/fsp_nm/bin/fnm.server stop
        fi
      else
        sudo ${NMS_HOME}/fsp_nm/bin/fnm.server $ACTION
      fi
    fi
  fi
}

servicePostgres()
{
  ACTION=$1
  MESSAGE=$2
  if [ $ACTION != "start" ] && [ $ACTION != "stop" ] && [ $ACTION != "restart" ]
  then
    echo "Unknown Postgres service action"
  else
    if [ "$install_user" = "root" ]
    then
      if [ -d "/usr/lib/systemd/system" ]
      then
        echo "$MESSAGE"
        /usr/bin/systemctl -q is-active postgres.service
        RET=$?
        /usr/bin/systemctl $ACTION postgres.service
        #handle migration cases from non-systemd to systemd usage
        if [ $ACTION = "stop" ] && [ $RET -ne 0 ]
        then
          ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server stop
        fi
      #
      else
        if [ -f "${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server" ]
        then
          echo "$MESSAGE"
 	          ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server $ACTION
 	      fi
      fi
    else
      if [ -d "/usr/lib/systemd/system" ]
      then
        echo "$MESSAGE"
        /usr/bin/systemctl -q is-active postgres.service
        RET=$?
        sudo /usr/bin/systemctl $ACTION postgres.service
        #handle migration cases from non-systemd to systemd usage
        if [ $ACTION = "stop" ] && [ $RET -ne 0 ]
        then
          sudo ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server stop
        fi
        #
      else
        if [ -f "${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server" ]
        then
          echo "$MESSAGE"
          sudo ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server $ACTION
        fi
      fi
    fi
  fi
}

stopServicePostgres()
{
 servicePostgres stop "Shutting down Postgres"
}

startServicePostgres()
{
 servicePostgres start "Starting Postgres"
}

restartServicePostgres()
{
 #servicePostgres restart "Restarting Postgres"
 #nbove line eeds to be replaced with below two-liner because of systemd defect, that starts fnmserver during restart of postgres
 #it was discovered on RHEL 7.3 with systemd 219-30.el7
 stopServicePostgres
 startServicePostgres
}
saveUserName()
{
echo "#!/bin/bash" > ${NMS_HOME}/fsp_nm/bin/fnm_user.sh
#install_user=$(id -u -n)
#install_group=$(id -g -n)
echo "fnmuser=$install_user" >> ${NMS_HOME}/fsp_nm/bin/fnm_user.sh
echo "export fnmuser" >> ${NMS_HOME}/fsp_nm/bin/fnm_user.sh
}
setPortPermission()
{
     sudo /usr/sbin/setcap cap_net_bind_service+ep ${NMS_HOME}/share/jre/bin/java
     export NMS_HOME
     echo ${NMS_HOME}/share/jre/lib/jli | sudo tee /etc/ld.so.conf.d/java.conf > /dev/null
     sudo /sbin/ldconfig
}
updateServiceFile()
{
for SERVICEFILE in /usr/lib/systemd/system/fnmserver.service /usr/lib/systemd/system/postgres.service /usr/lib/systemd/system/patroni.service ;
do
 if [ -f "$SERVICEFILE" ]; then
  grep -q "User=" $SERVICEFILE
  if [ $? -eq 0 ]; then
    sudo sed -i s/User=.*/User=$install_user/ $SERVICEFILE
  else
    sudo sed -i "/\[Service\].*/a User=$install_user" $SERVICEFILE
  fi
  grep -q "Group=" $SERVICEFILE
  if [ $? -eq 0 ]; then
    sudo sed -i s/Group=.*/Group=$install_group/ $SERVICEFILE
  else
    sudo sed -i "/\[Service\].*/a Group=$install_group" $SERVICEFILE
  fi
sudo /usr/bin/systemctl daemon-reload
fi
done

}

setupPostGres() {

  echo "Setting a password for the postgres root user."
  oldmodes=`stty -g`
  password=1
  repass=2
  while [ -z $password ] || [ -z $repass ] || [ $password != $repass ]
  do
        echo "Please enter password: "
        stty -echo
        read password
        stty $oldmodes
         if [ -z $password ]
         then
         echo "Password can not be empty: "
         continue;
         fi

        echo "Please retype password: "
        stty -echo
        read repass
        stty $oldmodes
         if [ -z $repass ]
         then
         echo "Password can not be empty. Please try again. "
         continue;
         fi

        if [ $password != $repass ]
        then
        echo "Passwords doesn't match. Please try again."
        fi
  done


  if [ "$PGUPGRADE" = "YES" ]
  then
    cp -p ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf.bak
    sed 's/md5/trust/g' ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf.bak > ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf
    chown -R postgres ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf
    chgrp -R postgres ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf
    cd ${NMS_HOME}/fsp_nm/postgres
    chown -R postgres .
    chgrp -R postgres .
    su postgres -c "bin/initdb -U root -E UTF8 --locale=C -D data"
    cp -f ${NMS_HOME}/fsp_nm/postgres.old/data/server.key ${NMS_HOME}/fsp_nm/postgres/data
    cp -f ${NMS_HOME}/fsp_nm/postgres.old/data/server.crt ${NMS_HOME}/fsp_nm/postgres/data
    chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/data/server.crt  ${NMS_HOME}/fsp_nm/postgres/data/server.key
    su postgres -c "bin/pg_upgrade -d ../postgres.old/data/ -D data/ -b ../postgres.old/bin  -B bin -U root"
    startServicePostgres
    #su postgres -c "bin/pg_ctl -w -D data -l logfile start"
    su postgres -c "bin/psql --quiet fnm -U root -c \"alter user root with password '$password';\""
    cp -p ${NMS_HOME}/fsp_nm/postgres/support-files/pg_hba.conf-lnx ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf
    cp -p ${NMS_HOME}/fsp_nm/postgres/support-files/postgresql.conf-lnx ${NMS_HOME}/fsp_nm/postgres/data/postgresql.conf
    chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf
    chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/data/postgresql.conf
    restartServicePostgres
    export PGPASSWORD=$password
    su postgres -c "bin/psql --quiet fnm -U root -c \"alter user root with password '$password';\""
    unset PGPASSWORD
    #su postgres -c "bin/pg_ctl -w -D data -l logfile -m fast restart"
  else
    # Register PostGres server for start at boot time
    ln -s ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server /etc/rc0.d/K07postgres
    ln -s ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server /etc/rc3.d/S96postgres
    ln -s ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server /etc/rc5.d/S96postgres

    #support for systemd
    if [ -d "/usr/lib/systemd/system" ]
    then
      ln -s ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server /usr/lib/systemd/system/postgres
      cp ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.service /usr/lib/systemd/system/
      chmod 644 /usr/lib/systemd/system/postgres.service
      /usr/bin/systemctl enable /usr/lib/systemd/system/postgres.service
    fi

    echo "Initializing and starting ${MGR_STR} database..."
    /usr/sbin/useradd -m --system --user-group --base-dir /opt/adva/fsp_nm/postgres postgres

    # Set proper dir owner
    cd ${NMS_HOME}/fsp_nm/var/db.backup
    chown -R postgres .
    chgrp -R postgres .

    cd ${NMS_HOME}/fsp_nm/postgres
    chown -R postgres .
    chgrp -R postgres .

    #set shm max memory value
    default_shmmax=18446744073692774399
    current_shmmax=`cat /proc/sys/kernel/shmmax`

    # if default shmmax is not this same as in system, set to default value
    if [ $default_shmmax !=  $current_shmmax ]
    then
        echo "Set SHMMAX to: $default_shmmax (previously was: $current_shmmax)"
        # set shmmax in system
        echo $default_shmmax > /proc/sys/kernel/shmmax

        grep -Eq "^kernel\.shmmax=" /etc/sysctl.conf
        if [ $? -ne 0 ]
        then
            # add to sysctl/conf
            echo "kernel.shmmax=$default_shmmax" >>  /etc/sysctl.conf
        else
            # update in sysctl/conf
            sed -i "/kernel\.shmmax=/ s/[0-9]\+/$default_shmmax/" /etc/sysctl.conf
        fi
    fi


    echo "Creating Mosaic Network Controller database"
    su postgres -c "bin/initdb -U root -E UTF8 --locale=C -D data"
    startServicePostgres
    #su postgres -c "bin/pg_ctl -w -D data -l logfile start"
    su postgres -c "bin/createdb -U root fnm"

    chown postgres:postgres ${NMS_HOME}/fsp_nm/scripts/createUser.sql
    chown postgres:postgres ${NMS_HOME}/fsp_nm/scripts/createDB.sql
    chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/scripts/postgres_copy_from.sql
    chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/scripts/postgres_csv_restore.sql

    su postgres -c "bin/psql --quiet -U root -f ${NMS_HOME}/fsp_nm/scripts/createUser.sql fnm"
    su postgres -c "bin/psql --quiet -U root -f ${NMS_HOME}/fsp_nm/scripts/createDB.sql fnm"
    su postgres -c "bin/psql --quiet -U root -f ${NMS_HOME}/fsp_nm/postgres/scripts/postgres_copy_from.sql fnm"
    su postgres -c "bin/psql --quiet -U root -f ${NMS_HOME}/fsp_nm/postgres/scripts/postgres_csv_restore.sql fnm"

    su postgres -c "bin/psql --quiet fnm -U root -c \"alter user root with password '$password';\""
    cp -p ${NMS_HOME}/fsp_nm/postgres/support-files/pg_hba.conf-lnx ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf
    cp -p ${NMS_HOME}/fsp_nm/postgres/support-files/postgresql.conf-lnx ${NMS_HOME}/fsp_nm/postgres/data/postgresql.conf
    chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf
    chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/data/postgresql.conf
    restartServicePostgres
    export PGPASSWORD=$password
    su postgres -c "bin/psql --quiet fnm -U root -c \"alter user root with password '$password';\""
    su postgres -c "bin/psql --quiet fnm -U root -c \"alter user adva with password 'NeverChange';\""
    unset PGPASSWORD
    #su postgres -c "bin/pg_ctl -w -D data -l logfile -m fast restart"
 fi
    echo "done."
}

setupPostGresNonRoot() {

  echo "Setting a password for the postgres root user."
  oldmodes=`stty -g`
  password=1
  repass=2
  while [ -z $password ] || [ -z $repass ] || [ $password != $repass ]
  do
        echo "Please enter password: "
        stty -echo
        read password
        stty $oldmodes
         if [ -z $password ]
         then
         echo "Password can not be empty: "
         continue;
         fi

        echo "Please retype password: "
        stty -echo
        read repass
        stty $oldmodes
         if [ -z $repass ]
         then
         echo "Password can not be empty. Please try again. "
         continue;
         fi

        if [ $password != $repass ]
        then
        echo "Passwords doesn't match. Please try again."
        fi
  done


  if [ "$PGUPGRADE" = "YES" ]
  then
    cp -p ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf.bak
    sed 's/md5/trust/g' ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf.bak > ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf
    #chown -R postgres ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf
    #chgrp -R postgres ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf
    cd ${NMS_HOME}/fsp_nm/postgres
    #chown -R postgres .
    #chgrp -R postgres .
    bin/initdb -U root -E UTF8 --locale=C -D data
    cp -f ${NMS_HOME}/fsp_nm/postgres.old/data/server.key ${NMS_HOME}/fsp_nm/postgres/data
    cp -f ${NMS_HOME}/fsp_nm/postgres.old/data/server.crt ${NMS_HOME}/fsp_nm/postgres/data
    #chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/data/server.crt  ${NMS_HOME}/fsp_nm/postgres/data/server.key
    bin/pg_upgrade -d ../postgres.old/data/ -D data/ -b ../postgres.old/bin  -B bin -U root
    startServicePostgres
    #su postgres -c "bin/pg_ctl -w -D data -l logfile start"
    bin/psql --quiet fnm -U root -c "alter user root with password '$password';"
    cp -p ${NMS_HOME}/fsp_nm/postgres/support-files/pg_hba.conf-lnx ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf
    cp -p ${NMS_HOME}/fsp_nm/postgres/support-files/postgresql.conf-lnx ${NMS_HOME}/fsp_nm/postgres/data/postgresql.conf
    #sudo chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf
    #sudo chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/data/postgresql.conf
    restartServicePostgres
    export PGPASSWORD=$password
    bin/psql --quiet fnm -U root -c "alter user root with password '$password';"
    unset PGPASSWORD
    #su postgres -c "bin/pg_ctl -w -D data -l logfile -m fast restart"
  else
    # Register PostGres server for start at boot time
    sudo ln -s ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server /etc/rc0.d/K07postgres
    sudo ln -s ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server /etc/rc3.d/S96postgres
    sudo ln -s ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server /etc/rc5.d/S96postgres

    #support for systemd
    if [ -d "/usr/lib/systemd/system" ]
    then
      sudo ln -s ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server /usr/lib/systemd/system/postgres
      sudo cp ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.service /usr/lib/systemd/system/
      updateServiceFile
      sudo chmod 644 /usr/lib/systemd/system/postgres.service
      sudo /usr/bin/systemctl enable /usr/lib/systemd/system/postgres.service

    fi

    echo "Initializing and starting ${MGR_STR} database..."
    #sudo /usr/sbin/useradd -m --system --user-group --base-dir /opt/adva/fsp_nm/postgres postgres

    # Set proper dir owner
    cd ${NMS_HOME}/fsp_nm/var/db.backup
    #sudo chown -R postgres .
    #sudo chgrp -R postgres .

    cd ${NMS_HOME}/fsp_nm/postgres
    #sudo chown -R postgres .
    #sudo chgrp -R postgres .

        #set shm max memory value
    default_shmmax=18446744073692774399
    current_shmmax=`cat /proc/sys/kernel/shmmax`

    # if default shmmax is not this same as in system, set to default value
    if [ $default_shmmax !=  $current_shmmax ]
    then
        echo "Set SHMMAX to: $default_shmmax (previously was: $current_shmmax)"
        # set shmmax in system
        echo $default_shmmax | sudo tee /proc/sys/kernel/shmmax > /dev/null

        grep -Eq "^kernel\.shmmax=" /etc/sysctl.conf
        if [ $? -ne 0 ]
        then
            # add to sysctl/conf
            echo "kernel.shmmax=$default_shmmax" | sudo tee -a /etc/sysctl.conf > /dev/null
        else
            # update in sysctl/conf
            sudo sed -i "/kernel\.shmmax=/ s/[0-9]\+/$default_shmmax/" /etc/sysctl.conf
        fi
    fi


    echo "Creating Mosaic Network Controller database"
    bin/initdb -U root -E UTF8 --locale=C -D data
    startServicePostgres
    #su postgres -c "bin/pg_ctl -w -D data -l logfile start"
    bin/createdb -U root fnm

    #sudo chown postgres:postgres ${NMS_HOME}/fsp_nm/scripts/createUser.sql
    #sudo chown postgres:postgres ${NMS_HOME}/fsp_nm/scripts/createDB.sql
    #sudo chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/scripts/postgres_copy_from.sql
    #sudo chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/scripts/postgres_csv_restore.sql

    bin/psql --quiet -U root -f ${NMS_HOME}/fsp_nm/scripts/createUser.sql fnm
    bin/psql --quiet -U root -f ${NMS_HOME}/fsp_nm/scripts/createDB.sql fnm
    bin/psql --quiet -U root -f ${NMS_HOME}/fsp_nm/postgres/scripts/postgres_copy_from.sql fnm
    bin/psql --quiet -U root -f ${NMS_HOME}/fsp_nm/postgres/scripts/postgres_csv_restore.sql fnm

    bin/psql --quiet fnm -U root -c "alter user root with password '$password';"
    cp -p ${NMS_HOME}/fsp_nm/postgres/support-files/pg_hba.conf-lnx ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf
    cp -p ${NMS_HOME}/fsp_nm/postgres/support-files/postgresql.conf-lnx ${NMS_HOME}/fsp_nm/postgres/data/postgresql.conf
    #sudo chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf
    #sudo chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/data/postgresql.conf
    restartServicePostgres
    export PGPASSWORD=$password
    bin/psql --quiet fnm -U root -c "alter user root with password '$password';"
    echo "test"
    bin/psql --quiet fnm -U root -c "alter user adva with password 'NeverChange';"
    unset PGPASSWORD
    #su postgres -c "bin/pg_ctl -w -D data -l logfile -m fast restart"
 fi
    echo "done."
}

# update the postgres user if needed
updatePostgresUser()
{
  POTRGES_USER_IS_NOT_SYSTEM=`awk -F: '$3>=1000 && $3!=65534 {print $1}' /etc/passwd | grep postgres`
  if [ -z "$POTRGES_USER_IS_NOT_SYSTEM" ]
  then
    echo "postgres user exists and is a system user"
  else
    echo "Updating the postgres user to make it system user"
    PG_RETURN_TO_DIR=`pwd`

    # Delete the non-system user and create a system one
    if [ "$install_user" = "root" ]
    then
      userdel --remove postgres
      useradd -m --system --user-group --base-dir /opt/adva/fsp_nm/postgres postgres
    #else
    #  sudo userdel --remove postgres
    #  sudo useradd -m --system --user-group --base-dir /opt/adva/fsp_nm/postgres postgres
    fi
    # Set the ownership in the needed directories for the new postgres user
    cd ${NMS_HOME}/fsp_nm/var/db.backup
    chown -R postgres .
    chgrp -R postgres .

    cd ${NMS_HOME}/fsp_nm/postgres
    chown -R postgres .
    chgrp -R postgres .

    cd $PG_RETURN_TO_DIR
    echo "postgres user is now a system user"
  fi
}

preservefnmprop()
{
if [ -f "${NMS_HOME}/fsp_nm/backup/fnm.properties.$ACT_DATE_FNMPROP" ]
then
 if grep -Fq "com.adva.nlms.mediation.sm.prov.ni.controller=false" ${NMS_HOME}/fsp_nm/backup/fnm.properties."$ACT_DATE_FNMPROP"
    then
     echo ""
 elif grep -Fq "com.adva.nlms.mediation.sm.prov.ni.controller=true" ${NMS_HOME}/fsp_nm/backup/fnm.properties."$ACT_DATE_FNMPROP"
    then
    mv ${NMS_HOME}/fsp_nm/fnm.properties ${NMS_HOME}/fsp_nm/fnm.properties.tmp
    sed s:com.adva.nlms.mediation.sm.prov.ni.controller=false:com.adva.nlms.mediation.sm.prov.ni.controller=true:g ${NMS_HOME}/fsp_nm/fnm.properties.tmp > ${NMS_HOME}/fsp_nm/fnm.properties
    rm -f ${NMS_HOME}/fsp_nm/fnm.properties.tmp
 fi
fi
}
preservesecprop()
{
  if [ -f "${NMS_HOME}/fsp_nm/backup/sec.properties" ]
  then
	cp -p ${NMS_HOME}/fsp_nm/backup/sec.properties ${NMS_HOME}/fsp_nm/certs/sec.properties
  fi
}

installFNM_uncompress()
{
    if `gzip -df "$AD/$AF.gz" > "$AD/$AF"`
    then
      echo ""
    else
      echo "Could not uncompress the $MGR_STR archive - installation incomplete."
      exit 1
    fi
}

install_rhel8_pgdep()
{
   if [ -f /etc/redhat-release ]
   then
     grep -Fq "release 8" /etc/redhat-release
     RETCODE=$?
     if [ $RETCODE -eq 0 ]; then
        ln -s /usr/lib64/libreadline.so.7 /usr/lib64/libreadline.so.6
        yum list installed compat-openssl10
        if [ $? -ne 0 ]; then
          echo "Installing postgres dependencies"
          tar -zxvf "$AD/rhel8-pgdep.tgz"
          cd "$AD"
          yum -C -y localinstall make*.rpm
          yum -C -y localinstall compat-openssl*.rpm
           if [ $? -eq 0 ]
           then
                rm -f "$AD"/compat-openssl*.rpm
                rm -f "$AD"/make*.rpm
           else
                rpm -i make*.rpm
                rpm -i compat-openssl*.rpm
                if [ $? -eq 0 ]; then
                  rm -f "$AD"/compat-openssl*.rpm
                  rm -f "$AD"/make*.rpm
                fi
           fi
          echo " "
         fi
     fi
   fi
}

install_rhel8_pgdep_nonroot()
{
   if [ -f /etc/redhat-release ]
   then
     grep -Fq "release 8" /etc/redhat-release
     RETCODE=$?
     if [ $RETCODE -eq 0 ]; then
        sudo ln -s /usr/lib64/libreadline.so.7 /usr/lib64/libreadline.so.6
        yum list installed compat-openssl10
        if [ $? -ne 0 ]; then
          echo "Installing postgres dependencies"
          tar -zxvf "$AD/rhel8-pgdep.tgz"
          cd "$AD"
          sudo yum -C -y localinstall make*.rpm
          sudo yum -C -y localinstall compat-openssl*.rpm
           if [ $? -eq 0 ]
           then
                rm -f "$AD"/compat-openssl*.rpm
                rm -f "$AD"/make*.rpm
           else
                sudo rpm -i make*.rpm
                sudo rpm -i compat-openssl*.rpm
                if [ $? -eq 0 ]; then
                  rm -f "$AD"/compat-openssl*.rpm
                  rm -f "$AD"/make*.rpm
                fi
           fi
          echo " "
         fi
     fi
   fi
}


installFNM()
{
# Check for installed version
if [ -f "$NMS_HOME/fsp_nm/lib/mediation.jar" ]
then
  echo "This script will upgrade your workstation to $MGR_STR and upgrade your database."
  echo "During the upgrade process the Mosaic Network Controller Server will be shut down."
  echo ""
  echo "CAUTION! Please ensure that the ENC database is backed up before proceeding with the upgrade."
  echo "Direct upgrades are only supported from ENC version ${UPGRADE_VERSION} and above. Please validate your database using the scripts (on the CD) and instructions provided in the release notes."
  echo "In case the database validation using the scripts fails, please contact Adva tech support for further assistance."
  echo ""
  UPGRADE=YES
else
  echo "This script will install the $MGR_STR for Linux on your system."
fi

echo ""
echo "Continue? [y/n] "

read reply

if [ "$reply" != "y" -a "$reply" != "Y" -a "$reply" != "yes" -a "$reply" != "YES" ]
then
  exit 0
fi
sudo cp setenv.sh /etc/setenv.sh

if [ "$UPGRADE" = "YES" ]
then
  # check user
  . $NMS_HOME/fsp_nm/bin/fnm_user.sh

  FNM_USER=$fnmuser
  export FNM_USER
  cd $NMS_HOME
  FNM_GROUP=`ls -l | awk '$9 == "fsp_nm"' | awk '{print $4}'`
  export FNM_GROUP

  if [ "$fnmuser" != "root" ]
  then
   echo
   #We are allowing non root user to upgrade. comment following
   #echo "ENC User must be root to upgrade. Changing $FNM_USER to root."
   #$NMS_HOME/fsp_nm/bin/changeUser.sh root root
  fi
fi

if [ -d "${NMS_HOME}/fsp_nm/postgres/data/base" -a ! -f "${NMS_HOME}/fsp_nm/postgres/support-files/postgres12.4.1.txt"  ]
then
 PGUPGRADE=YES
fi

echo Creating ${NMS_HOME}/fsp_nm/var/db.backup directory.
if [ -d "${NMS_HOME}/fsp_nm/var/db.backup" ]
then
	echo ""
else
	mkdir -p  ${NMS_HOME}/fsp_nm/var/db.backup
	chmod 755 ${NMS_HOME}
fi


if [ -f "${NMS_HOME}/fsp_nm/fnm.properties" ]
then
	ACT_DATE_FNMPROP=`date +%Y-%m-%d-%H.%M`
	echo Copy fnm.properties file to ${NMS_HOME}/fsp_nm/backup/fnm.properties."$ACT_DATE_FNMPROP"

	if [ -d "${NMS_HOME}/fsp_nm/backup" ]
	then
		echo ""
	else
		mkdir -p  ${NMS_HOME}/fsp_nm/backup

	fi

	cp -p  ${NMS_HOME}/fsp_nm/fnm.properties ${NMS_HOME}/fsp_nm/backup/fnm.properties."$ACT_DATE_FNMPROP"
fi

if [ -f "${NMS_HOME}/fsp_nm/ws/etc/jetty.xml" ]
then
	ACT_DATE=`date +%Y-%m-%d-%H.%M`
	echo Copy jetty.xml file to ${NMS_HOME}/fsp_nm/backup/jetty.xml."$ACT_DATE"
	cp -p  ${NMS_HOME}/fsp_nm/ws/etc/jetty.xml ${NMS_HOME}/fsp_nm/backup/jetty.xml."$ACT_DATE"
fi

if [ -f "${NMS_HOME}/fsp_nm/certs/sec.properties" ]
then
	echo Copy sec.properties file to ${NMS_HOME}/fsp_nm/backup/sec.properties
	cp -p  ${NMS_HOME}/fsp_nm/certs/sec.properties ${NMS_HOME}/fsp_nm/backup/sec.properties
fi

# Shut down running FNM Server and clean old stuff
if [ "$UPGRADE" = "YES" ]
then
  serviceFNM stop
  if [ -f "${NMS_HOME}/fsp_nm/bin/snmpforwarder.sh" ]
  then
        {NMS_HOME}/fsp_nm/bin/snmpforwarder.sh stop &>/dev/null
  fi
  stopServicePostgres

  rm -r ${NMS_HOME}/fsp_nm/lib/*
  rm -rf ${NMS_HOME}/fsp_nm/lib.old/*
  rm -rf ${NMS_HOME}/share/jre/*
  rm -rf ${NMS_HOME}/share/jre.old/*
  rm -f ${NMS_HOME}/fsp_nm/bin/*
  rm -f ${NMS_HOME}/fsp_nm/*.pdf
  #remove following generated files as the old format is not supported
  rm -f ${NMS_HOME}/fsp_nm/FTPRepositoryUpdates.xml
  rm -f ${NMS_HOME}/fsp_nm/SCPRepositoryUpdates.xml
  #removing to support OSA5401 as native device on upgrade
  rm -f ${NMS_HOME}/fsp_nm/CustomProducts/osa5401.xml

  # Remove old sjdataanalyzer
  if [ -d "${NMS_HOME}/sjdataanalyzer" ]
  then
    ${NMS_HOME}/sjdataanalyzer/bin/stopsj.sh
    ${NMS_HOME}/sjdataanalyzer/stop_mongo.sh
    ${NMS_HOME}/sjdataanalyzer/remove_mongo_service.sh

    rm -rf ${NMS_HOME}/sjdataanalyzer
    rm -rf /etc/init.d/sj-adva
    rm -rf /etc/init.d/mongod-adva
    rm -f /etc/rc?.d/???sj-adva
    rm -f /etc/rc?.d/???mongod-adva
  fi

  if [ "$PGUPGRADE" = "YES" ]
  then
     rm -r -f  ${NMS_HOME}/fsp_nm/postgres.old
     mv  ${NMS_HOME}/fsp_nm/postgres ${NMS_HOME}/fsp_nm/postgres.old
  fi
fi

################################################################################
# Determine installation settings

# Check for Linux
if [ ! -n "`uname -ps | grep Linux`" ]
then
  echo "This software runs on Linux only but this system"
  echo "seems to be of different type (`uname -ps`). Aborting."
  exit 1
fi

# Determine Manager installation directory
#echo "Enter the Mosaic Network Controller installation directory"
#echo "[${NMS_HOME}] \c"
#read reply
#if [ -n "$reply" ]
#then
#  NMS_HOME=$reply
#fi
#echo ""

if [ -x "${NMS_HOME}" ]
then
  echo "Using existing directory ${NMS_HOME}"
  rm -rf ${NMS_HOME}/fsp_nm/lib
 rm -rf ${NMS_HOME}/fsp_nm/log
else
  echo "Creating installation directory ${NMS_HOME} ..."
  mkdir -p ${NMS_HOME}
  if [ -d ${NMS_HOME} ]
  then
    echo Done.
  else
    echo "Could not create ${NMS_HOME}. Installation failed."
    exit 1
  fi
fi

################################################################################
# Start installation
#install postgres dependecies
if [ "$install_user" = "root" ]
then
  install_rhel8_pgdep
else
  install_rhel8_pgdep_nonroot
fi

# Unpack software to directory ${NMS_HOME}

echo "Uncompressing $MGR_STR archive..."


if `gzip -df "$AD/$AF.gz" > "$AD/$AF"`
then
  echo "done."
  echo ""
else
  echo "Could not uncompress the $MGR_STR archive - installation incomplete."
  exit 1
fi

echo "Unpacking $MGR_STR archive to ${NMS_HOME}..."
cd ${NMS_HOME}

if `tar xf "$AD/$AF" --exclude="fsp_nm/els"`
then
  echo "done."
  echo ""
else
  echo "Could not untar the $MGR_STR archive - installation incomplete."
  exit 1
fi

cd fsp_nm

# Create endorsed folder
#mkdir lib/endorsed
#ln lib/OB.jar lib/endorsed

# Update Installation Path
for FILE in bin/fnm.server \
            bin/fnm \
            bin/fem \
            bin/MakePDF \
            bin/mdlog \
            bin/cleanDB \
            bin/restoreDB \
            postgres/support-files/postgres.server
do
  mv ${FILE} ${FILE}.org
  sed s:/opt/adva:${NMS_HOME}:g ${FILE}.org > ${FILE}
  chmod 755 ${FILE}
  rm -f ${FILE}.org
done
# should be read and executable by root or owner only:
chmod 700 bin/restoreDB
chmod 700 bin/cleanDB
chmod 700 bin/fnm.server

if [ "$install_user" != "root" ]
then
	saveUserName
	setPortPermission
fi

cd ${NMS_HOME}
OLD_TMP=$TMP
OLD_TMPDIR=$TMPDIR
TMP="/tmp"
TMPDIR="/tmp"
export TMP TMPDIR

if [ -z "$UPGRADE" ]
then
  if [ "$install_user" = "root" ]
  then
    # Register server for start at boot time
    ln -s ${NMS_HOME}/fsp_nm/bin/fnm.server /etc/rc0.d/K05fnm.server
    ln -s ${NMS_HOME}/fsp_nm/bin/fnm.server /etc/rc3.d/S98fnm.server
    ln -s ${NMS_HOME}/fsp_nm/bin/fnm.server /etc/rc5.d/S98fnm.server
    #support for systemd
    if [ -d "/usr/lib/systemd/system" ]
    then
      ln -s ${NMS_HOME}/fsp_nm/bin/fnm.server /usr/lib/systemd/system/fnmserver
      cp ${NMS_HOME}/fsp_nm/postgres/support-files/fnmserver.service /usr/lib/systemd/system/
      chmod 644 /usr/lib/systemd/system/fnmserver.service
      /usr/bin/systemctl enable /usr/lib/systemd/system/fnmserver.service
    fi
      # Set link in standard path
      ln -s ${NMS_HOME}/fsp_nm/bin/fnm /usr/bin
      setupPostGres
  else
    # Register server for start at boot time
    sudo ln -s ${NMS_HOME}/fsp_nm/bin/fnm.server /etc/rc0.d/K05fnm.server
    sudo ln -s ${NMS_HOME}/fsp_nm/bin/fnm.server /etc/rc3.d/S98fnm.server
    sudo ln -s ${NMS_HOME}/fsp_nm/bin/fnm.server /etc/rc5.d/S98fnm.server
    #support for systemd
    if [ -d "/usr/lib/systemd/system" ]
    then
      sudo ln -s ${NMS_HOME}/fsp_nm/bin/fnm.server /usr/lib/systemd/system/fnmserver
      sudo cp ${NMS_HOME}/fsp_nm/postgres/support-files/fnmserver.service /usr/lib/systemd/system/
      sudo chmod 644 /usr/lib/systemd/system/fnmserver.service
      sudo /usr/bin/systemctl enable /usr/lib/systemd/system/fnmserver.service
    fi
      # Set link in standard path
      sudo ln -s ${NMS_HOME}/fsp_nm/bin/fnm /usr/bin
      #saveUserName
      setupPostGresNonRoot
      #setPortPermission
  fi
else
  if [ "$install_user" = "root" ]
  then
    if [ -d "/usr/lib/systemd/system" ]
    then
      grep -q "Environment=LAUNCHED_BY_SYSTEMD=yes" /usr/lib/systemd/system/fnmserver.service
      if [ $? -ne 0 ]; then
        sed -i "/\[Service\].*/a Environment=LAUNCHED_BY_SYSTEMD=yes" /usr/lib/systemd/system/fnmserver.service
        /usr/bin/systemctl daemon-reload
      fi
      grep -q "PIDFile=/var/run/fnmserver.pid" /usr/lib/systemd/system/fnmserver.service
      if [ $? -eq 0 ]; then
        sed -i "s#PIDFile=/var/run/fnmserver.pid#PIDFile=/opt/adva/fsp_nm/fnmserver.pid#" /usr/lib/systemd/system/fnmserver.service
        /usr/bin/systemctl daemon-reload
      fi
      grep -q "Type=simple" /usr/lib/systemd/system/fnmserver.service
      if [ $? -eq 0 ]; then
        sed -i "s#Type=simple#Type=forking#" /usr/lib/systemd/system/fnmserver.service
        /usr/bin/systemctl daemon-reload
      fi
    fi
  else
    if [ -d "/usr/lib/systemd/system" ]
    then
      grep -q "Environment=LAUNCHED_BY_SYSTEMD=yes" /usr/lib/systemd/system/fnmserver.service
      if [ $? -ne 0 ]; then
        sudo sed -i "/\[Service\].*/a Environment=LAUNCHED_BY_SYSTEMD=yes" /usr/lib/systemd/system/fnmserver.service
        sudo /usr/bin/systemctl daemon-reload
      fi
      grep -q "PIDFile=/var/run/fnmserver.pid" /usr/lib/systemd/system/fnmserver.service
      if [ $? -eq 0 ]; then
        sudo sed -i "s#PIDFile=/var/run/fnmserver.pid#PIDFile=/opt/adva/fsp_nm/fnmserver.pid#" /usr/lib/systemd/system/fnmserver.service
        sudo /usr/bin/systemctl daemon-reload
      fi
      grep -q "Type=simple" /usr/lib/systemd/system/fnmserver.service
      if [ $? -eq 0 ]; then
        sudo sed -i "s#Type=simple#Type=forking#" /usr/lib/systemd/system/fnmserver.service
        sudo /usr/bin/systemctl daemon-reload
      fi
      fi
  fi
  if [ ! -d "${NMS_HOME}/fsp_nm/postgres/data/base" ]
  then
    if [ "$install_user" = "root" ]
    then
      setupPostGres
    else
      setupPostGresNonRoot
    fi
  else
    if [ "$install_user" = "root" ]
    then
      updatePostgresUser
      cp -p ${NMS_HOME}/fsp_nm/postgres/support-files/pg_hba.conf-lnx ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf
      cp -p ${NMS_HOME}/fsp_nm/postgres/support-files/postgresql.conf-lnx ${NMS_HOME}/fsp_nm/postgres/data/postgresql.conf
      # Set proper dir owner
      cd ${NMS_HOME}/fsp_nm/var/db.backup
      chown -R postgres .
      chgrp -R postgres .
      cd ${NMS_HOME}/fsp_nm/postgres
      chown -R postgres .
      chgrp -R postgres .
      chmod 700 data
      startServicePostgres
    else
      cp -p ${NMS_HOME}/fsp_nm/postgres/support-files/pg_hba.conf-lnx ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf
      cp -p ${NMS_HOME}/fsp_nm/postgres/support-files/postgresql.conf-lnx ${NMS_HOME}/fsp_nm/postgres/data/postgresql.conf
      cd ${NMS_HOME}/fsp_nm/postgres
      chmod 700 data
      startServicePostgres
    fi
  fi
  cd $NMS_HOME/fsp_nm/bin
  ./propup.sh -f
  preservefnmprop
  preservesecprop
fi

cd ${NMS_HOME}
# Certificate generation
mkdir -p ${NMS_HOME}/fsp_nm/certs
if [ ! -f ${NMS_HOME}/fsp_nm/certs/fnmserver.ks ]
then
  # We need to be able to set different passwords for keystore and private key.
  # Therefore we need to stick to JKS format, since PKCS12 does not support this:
  # https://bugs.openjdk.java.net/browse/JDK-8008292
	${NMS_HOME}/share/jre/bin/keytool -genkey -alias nms-server-key -storetype jks -dname "CN=adtran.com,OU=Mosaic Network Controller,O=Adtran Networks SE,L=,ST=,C=US" -keyalg RSA -validity 3650 --storepass NeverChange --keypass NeverChange -keystore ${NMS_HOME}/fsp_nm/certs/fnmserver.ks 2>/dev/null
	chmod 600 ${NMS_HOME}/fsp_nm/certs/fnmserver.ks
fi
chmod 600 $NMS_HOME/fsp_nm/certs/fnmserver.ks

if [ -f "${NMS_HOME}/fsp_nm/ssocerts/ssotruststore.jks" ]
then
  chmod 600 $NMS_HOME/fsp_nm/ssocerts/ssotruststore.jks
fi

if [ "$UPGRADE" = "YES" ]
then
 if [ "$FNM_USER" != "root" ]
 then
  echo "Changing FNM user back to $FNM_USER"
  #$NMS_HOME/fsp_nm/bin/changeUser.sh $FNM_USER $FNM_GROUP
 fi
fi

}

installELS_dbBackupCreate()
{
    # Backup existing databases
    local upgrade_timestamp=$( date +"%Y%m%d%H%M%S" )
    if [[ -f "/var/opt/flexnetls/advaoptc/flexnetls_licenses.mv.db" ]] ; then
        echo ""
        echo Creating ELS DB backup
        mkdir -p "$NMS_HOME/fsp_nm/els/backup"
        cd "$NMS_HOME/fsp_nm/els"
        mv /var/opt/flexnetls/advaoptc backup/local_${upgrade_timestamp}
    elif [[ -d "$NMS_HOME/fsp_nm/els/local" ]] ; then
        echo ""
        echo Creating ELS DB backup
        mkdir -p "$NMS_HOME/fsp_nm/els/backup"
        cd "$NMS_HOME/fsp_nm/els"
        mv local backup/local_${upgrade_timestamp}
    fi
}

installELS_dbBackupRestore()
{
# Restore newest backup
    if [[ -d "$NMS_HOME/fsp_nm/els/backup" ]] ; then
        cd "$NMS_HOME/fsp_nm/els/backup"
        # list and sort by date | get 2'nd line and display 9th column
        local newest_backup=$( ls -lt | awk '{ if (NR==2) print $9 }' )
        if [[ -d "${newest_backup}" ]] ; then
            cp -fR "${newest_backup}" "$NMS_HOME/fsp_nm/els/local"
        fi
    fi
}

installELS_unpack()
{
    echo "Unpacking Embedded License Server archive to ${NMS_HOME}/fsp_nm/els..."
    mkdir -p ${NMS_HOME}/fsp_nm/els
    cd ${NMS_HOME}
    if [[ ! -f "$AD/$AF" ]] ; then
        installFNM_uncompress
    fi
    if `tar xf "$AD/$AF" "fsp_nm/els"` ; then
      echo "done."
      echo ""
    else
      echo "Could not untar the ELS archive - installation incomplete."
      exit 1
    fi
}

installELS_cleanupServices()
{
    for flexnetls_service in $( ls /etc/rc.d/init.d |grep flexnetls ) ; do
        service ${flexnetls_service} stop &>/dev/null
        chkconfig ${flexnetls_service} off &>/dev/null
        chkconfig --del ${flexnetls_service} &>/dev/null
    done

    for flexnetls_config in $( ls /etc/default |grep flexnetls ) ; do
        rm -f ${flexnetls_config} &>/dev/null
    done
}

installELS_updateUninstaller()
{
    if [[ -f "$NMS_HOME/fsp_nm/lib/mediation.jar" ]] ; then
        if [[ ! -f "$AD/$AF" ]] ; then
            installFNM_uncompress
        fi
        cd $NMS_HOME
        if `tar --overwrite -xf "$AD/$AF" "uninstall-fsp_nm"` ; then
          echo "Uninstall script updated"
        else
          echo "Could not update uninstall script - installation incomplete."
          exit 1
        fi
    else
        cat <<EOF > ${NMS_HOME}/uninstall-fsp_nm
#!/bin/bash
if [[ -d "${NMS_HOME}/fsp_nm/els" ]] ; then
  echo "This script will uninstall Embedded License Server"
  echo "from your system."
  echo ""
  echo "Do you really want to remove the Embedded License Server installation from"
  echo "${NMS_HOME}/fsp_nm/els [n]? "
  read reply
  if [ "\$reply" != "y" -a "\$reply" != "Y" -a "\$reply" != "yes" -a "\$reply" != "YES" ]
  then
    echo "Uninstall aborted."
    exit 1
  fi
else
  echo "Embedded License Server directory could not be found in"
  echo "${NMS_HOME}/fsp_nm/els. Terminating script session."
  exit 1
fi
if [[ -f ${NMS_HOME}/fsp_nm/els/els ]] ; then
    ${NMS_HOME}/fsp_nm/els/els uninstall
fi
if [[ -d ${NMS_HOME}/fsp_nm ]] ; then
    rm -rf ${NMS_HOME}/fsp_nm ${NMS_HOME}/uninstall-fsp_nm
fi
EOF
        chmod u+x ${NMS_HOME}/uninstall-fsp_nm
    fi
}

installELS()
{
# This function installs and upgrades Embedded License Server
    echo Setting Embedded License Server installation...
    local upgrade_els="no"
    local els_home="$NMS_HOME/fsp_nm/els"
    local flexnetls_home="$NMS_HOME/fsp_nm/flexnetls"
    local els_internals_list=( "apache-tomcat" "flexnetls" "jre" )
    # check for previous Flexnet License Server versions
    if [[ -f "$flexnetls_home/server/fne.sh" ]] ; then
        echo Upgrading Flexnet License Server to Embedded License Server...
        cd "$flexnetls_home/server"
        ./fne.sh stop &>/dev/null
        #installELS_dbBackupCreate
        cd "$flexnetls_home/server"
        ./fne.sh uninstall
        cd "$NMS_HOME/fsp_nm"
        rm -rf flexnetls 2>/dev/null
        installELS_cleanupServices
    fi
    if [[ -f "$els_home/els" ]] || [[ -f "$els_home/els.sh" ]] ; then
        echo Upgrading Embedded License Server...
        local upgrade_els="yes"
        cd "$els_home"
        if [[ -f "$els_home/els" ]] ; then
          ./els stop &>/dev/null
        else
          ./els.sh stop &>/dev/null
        fi
        #installELS_dbBackupCreate
    fi
    for detached_folder in ${els_internals_list[@]} ; do
        [[ -d "${els_home}" ]] \
        && cd "${els_home}"
        [[ -d "${detached_folder}" ]] \
        && rm -rf "${detached_folder}"
    done

    installELS_unpack
    #installELS_dbBackupRestore

    if [[ -f "$els_home/els"  ]] ; then
        cd "$els_home"
        if [[ "$upgrade_els" == "yes" ]] ; then
            ./els update
            if [[ -f "$els_home/els.sh"  ]] ; then
              rm -f "$els_home/els.sh"
            fi
            echo ""
        else
            ./els install
            echo ""
        fi
    else
        echo "Could not find Embedded License Server els installation script"
        echo " "
        exit 0
    fi
}

changefnmprop()
{
if [ -f "${NMS_HOME}/fsp_nm/fnm.properties" ]
then
    mv ${NMS_HOME}/fsp_nm/fnm.properties ${NMS_HOME}/fsp_nm/fnm.properties.tmp
    sed s:com.adva.nlms.mediation.sm.prov.ni.controller=false:com.adva.nlms.mediation.sm.prov.ni.controller=true:g ${NMS_HOME}/fsp_nm/fnm.properties.tmp > ${NMS_HOME}/fsp_nm/fnm.properties
    rm -f ${NMS_HOME}/fsp_nm/fnm.properties.tmp
fi
}

checkOS()
{
if [[ `ps -p 1 -o cmd=` = *"systemd"* ]] ; then
               systemd_on=true
else
               systemd_on=false
               echo "Centralized CP is not supported by this Operating System."
               exit 1
fi
}

installNI()
{
echo "Installing Centralized CP software"
cd "$AD"
mkdir -p "$NMS_HOME/ni_install_files"
rm -rf "$NMS_HOME"/ni_install_files/*
tar -zxvf "$AD/ni-${NI_VERSION}.tgz" -C  "$NMS_HOME/ni_install_files"
if [ "$install_user" = "root" ]
then
 "$NMS_HOME"/ni_install_files/installni.sh
else
 sudo "$NMS_HOME"/ni_install_files/installni.sh
fi
RETCODE=$?
if [ $RETCODE -eq 0 ]; then
    rm -f "$AD/ni-${NI_VERSION}.tgz"
fi
rm -rf "$NMS_HOME"/ni_install_files/*
echo " "
}


startFNM()
{
echo "Do you want to start the MNC server application now?"
echo "Type 'y' to start the server, or 'n' if you need to modify properties files first.  [y/n]?"
read reply
if [ "$reply" = "y" -o "$reply" = "Y" -o "$reply" = "yes" -o "$reply" = "YES" ]
then
  # Start Mosaic Network Controller Server
  serviceFNM start
fi
}



if [ ! -r ${AF}.gz ]
then
  echo "Please execute this script from the directory where the"
  echo "fsp_nm.tar.gz archive resides."
  exit 1
fi

change=c
  while [ "$change" = "c" ] || [ "$change" = "C" ]
  do
        clear
        echo "Choose one of the following options:"
        echo
        echo "1) ENC"
        echo "2) CPc"
        echo "3) Embedded License Server"
        echo "4) ENC/CPc"
        echo "5) ENC/Embedded License Server"
        echo "6) ENC/CPc/Embedded License Server"
        echo "7) Quit"

read answer
echo "You have selected option $answer. Do you want to continue(y/Y) or change option (c/C)?"
read change
done
case "$answer" in

  "1")
        installFNM
    	startFNM
        ;;
  "2")
        checkOS
        installNI
        changefnmprop
        ;;
  "3")
        installELS
        installELS_updateUninstaller
        ;;
  "4")
        checkOS
        installFNM
	    installNI
	    changefnmprop
	    startFNM
        ;;
  "5")
        installFNM
        installELS
	    startFNM
        ;;
   "6")
         checkOS
         installFNM
         installELS
         installNI
         changefnmprop
         startFNM
        ;;
   "7")
        exit 1
        ;;
    *)
        echo "invalid option"
        ;;
esac



TMP="$OLD_TMP"
TMPDIR="$OLD_TMPDIR"
export TMP TMPDIR

# Clean up
echo "Cleaning up..."
rm -f "$AD"/$AF
echo ""

echo ""
echo "Installation complete."
echo ""
