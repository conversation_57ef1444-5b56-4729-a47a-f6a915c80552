#!/bin/bash
# Owner: ssingam
#


################################################################################
#
# Network Manager Server Upgrade
#
# Changes user who runs Network Manager Server.
# User has to have privilages to port 162, assigned by adimin.
#
################################################################################
. ./setenv.sh

if [ ! "$1" ]; then
 echo "Usage: $0 <username> "
 echo "User name who is going to run ${NMS_NAME} Server should be given."
 exit
fi
if [ ! "$2" ]; then
 echo "Usage: $0 <username> <groupname>"
 echo "Group name of User who is going to run ${NMS_NAME} Server should be given."
 exit
fi
if [ -d "${NMS_HOME}" ]
then
  echo "Using existing directory ${NMS_HOME}"
  chown $1:$2 ${NMS_HOME}/
  chown $1:$2 ${NMS_HOME}/uninstall-fsp_nm
else
  echo "Creating installation directory ${NMS_HOME} ..."
  mkdir -p ${NMS_HOME}
  chown $1:$2 ${NMS_HOME}/
  chmod 755 ${NMS_HOME}/
fi
cp encsudo /etc/sudoers.d/encsudo
for SUDOFILE in /etc/sudoers.d/encsudo
do
 if [ -f "$SUDOFILE" ]; then
    sed -i s:USER_NAME:$1:g $SUDOFILE
    sed -i s:ENC_HOME:${MGR_DIR}:g $SUDOFILE
    sed -i s:NMS_HOME:${NMS_HOME}:g $SUDOFILE
    sed -i s:NI_DIR:${NI_DIR}:g $SUDOFILE
 fi
done
