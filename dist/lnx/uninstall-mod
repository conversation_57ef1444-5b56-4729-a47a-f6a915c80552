#!/bin/bash
################################################################################
#
# Network Manager deinstallation script
################################################################################

. /etc/setenv.sh

CWD=`pwd`
MGR_DIR_FILE="${MGR_DIR}/bin/fnm.server"
ELS_DIR_FILE="${ELS_DIR}/els"
NI_DIR_FILE="${NI_DIR}/uninstallni.sh"
MOD_DIR_FILE="${MOD_DIR}/csm/uninstall-csm.sh"
COMPANY_SOFTWARE_FILES=("${MGR_DIR_FILE}" "${ELS_DIR_FILE}" "${MOD_DIR_FILE}")

uninstall_fspnm(){

#####################################a###########################################
# Shutdown servers

if [ -d "/usr/lib/systemd/system" ]
then
  systemctl -q is-active fnmserver.service
  RET=$?
  systemctl stop fnmserver.service
  #handle migration cases from non-systemd to systemd usage
  if [ $RET -ne 0 ]
  then
    ${MGR_DIR}/bin/fnm.server stop
  fi
  ${MGR_DIR}/bin/snmpforwarder.sh stop
  #
  systemctl -q is-active postgres.service
  RET=$?
  systemctl stop postgres.service
  #handle migration cases from non-systemd to systemd usage
  if [ $RET -ne 0 ]
  then
    ${MGR_DIR}/postgres/support-files/postgres.server stop
  fi
else
  ${MGR_DIR}/bin/fnm.server stop
  ${MGR_DIR}/bin/snmpforwarder.sh stop
  ${MGR_DIR}/postgres/support-files/postgres.server stop
fi

# sleep for 15 as systemd process occupies postgres user
sleep 15
/usr/sbin/userdel postgres
/usr/sbin/groupdel postgres

################################################################################
# Start deletion

echo "Uninstalling..."

USER_HOME=$(echo $(getent passwd $USER )| cut -d : -f 6)
rm -rf ${USER_HOME}/var/log/adva

# Remove links
rm -f /usr/bin/fnm
rm -f /etc/rc?.d/???fnm.server
rm -f /etc/rc?.d/???postgres
if [ -d "/usr/lib/systemd/system" ]
 then
   /usr/bin/systemctl disable /usr/lib/systemd/system/fnmserver.service
   /usr/bin/systemctl disable /usr/lib/systemd/system/postgres.service
    rm -f /usr/lib/systemd/system/fnmserver
    rm -f /usr/lib/systemd/system/postgres
    rm -f /usr/lib/systemd/system/fnmserver.service
    rm -f /usr/lib/systemd/system/postgres.service
 fi

rm -f /usr/lib/tmpfiles.d/postgres.conf

# Remove directories and files"
for item in $(cd ${MGR_DIR} ; ls -A) ; do
    if [[ "$item" != "els" ]] && [[ "$item" != "flexnetls" ]] ; then
        rm -rf ${MGR_DIR}/${item}
    fi
done

# remove MGR_DIR directory if empty
if [[ -e "${MGR_DIR}" ]] && [[ ! "$(ls -A "${MGR_DIR}")" ]] ; then
    echo "Removing ${MGR_DIR}..."
    rm -rf "${MGR_DIR}"
fi

echo "Done."
echo ""

echo "Uninstall completed."
echo ""

}

uninstall_els(){

if [[ -f ${ELS_DIR}/els ]] ; then
    ${ELS_DIR}/els uninstall
fi

if [[ -d ${ELS_DIR} ]] ; then
    rm -rf ${ELS_DIR}
fi

}

uninstall_ni(){

"${NI_DIR}"/uninstallni.sh

}

uninstall_csm(){

"${MOD_DIR}"/csm/uninstall-csm.sh
"${MOD_DIR}"/core/uninstall-core.sh
"${MOD_DIR}"/mnc-rproxy/uninstall-mnc-rproxy.sh
docker secret rm mnc-postgresql-db-user
docker secret rm mnc-postgresql-db-pw
docker config rm mod-kafka-log4j
docker config rm mod-zookeeper-log4j
rm -rf $MOD_DIR
}

sys_cleanup(){
    echo
    echo "No company software found in local directories"
    echo
    echo "Cleaning obsolete files..."

    [[ -e "${NMS_HOME}/share" ]] && rm -rf "${NMS_HOME}/share"
    [[ -e "/etc/setenv.sh" ]] && rm -f /etc/setenv.sh
    [[ -e "${NMS_HOME}/uninstall-fsp_nm" ]] && rm -f uninstall-fsp_nm
    # remove fsp_nm directory if empty
    if [[ -e "${MGR_DIR}" ]] && [[ ! "$(ls -A "${MGR_DIR}")" ]] ; then
        rm -rf "${MGR_DIR}"
    fi

    echo "done."
    cd "$CWD"
    exit 0
}

get_software_name_from_file(){
    if [[ "${1}" == "${MGR_DIR_FILE}" ]] ; then
        echo "${NMS_NAME}"
    elif [[ "${1}" == "${ELS_DIR_FILE}" ]] ; then
        echo "${ELS_NAME}"
    elif [[ "${1}" == "${MOD_DIR_FILE}" ]] ; then
        echo "${MOD_NAME}"
    fi
}

get_software_dir_from_name(){
    if [[ "${1}" == "${NMS_NAME}" ]] ; then
        echo "${MGR_DIR}"
    elif [[ "${1}" == "${ELS_NAME}" ]] ; then
        echo "${ELS_DIR}"
    elif [[ "${1}" == "${MOD_NAME}" ]] ; then
        echo "${MOD_DIR}"
    fi
}

script_menu(){
    menu_nr=0
    menu_array=()

    for local_file in "${COMPANY_SOFTWARE_FILES[@]}"; do
        if [[ ! -e "${local_file}" ]] ; then
            continue
        fi
        software_name="$(get_software_name_from_file "${local_file}")"
        menu_nr=$(( menu_nr + 1 ))
        menu_array[${menu_nr}]="${software_name}"
    done

    if [[ "${menu_array[1]}" == "" ]] ; then
        sys_cleanup
    fi

    menu_nr=$(( menu_nr + 1 ))
    menu_array[${menu_nr}]="Exit."

    echo "Please type software number (1-${#menu_array[@]}) to uninstall:"
    echo
    for row in $(seq 1 ${menu_nr}) ; do
        echo "    ${row})   ${menu_array[$row]}"
    done
    echo
    read menu_reply


    if (( menu_reply == menu_nr )) ; then
        cd "$CWD"
        exit 0
    elif (( menu_reply < 1 )) && (( menu_reply > menu_nr )) ; then
        script_menu
    elif [[ "${menu_reply}" == "" ]] ; then
        script_menu
    fi

    echo "This script will uninstall ${menu_array[$menu_reply]}"
    echo "from your system."
    echo ""
    echo "Do you really want to remove the ${menu_array[$menu_reply]} installation from"
    echo "$(get_software_dir_from_name "${menu_array[$menu_reply]}") [y/n]? "
    read reply
    if [ "$reply" != "y" -a "$reply" != "Y" -a "$reply" != "yes" -a "$reply" != "YES" ] ; then
        echo
        echo "Uninstall aborted."
        echo
        script_menu
    fi

    if [[ "${menu_array[$menu_reply]}" == "${NMS_NAME}" ]]; then
        uninstall_fspnm
    elif [[ "${menu_array[$menu_reply]}" == "${ELS_NAME}" ]] ; then
        uninstall_els
    elif [[ "${menu_array[$menu_reply]}" == "${MOD_NAME}" ]] ; then
        uninstall_ni
        uninstall_csm
    fi

    script_menu
}

if [ -z "`id | grep uid=0`" ]
then
  echo "You must be root to execute this script."
  exit 1
fi

clear
script_menu