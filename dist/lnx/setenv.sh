#!/bin/bash
NMS_HOME=/opt/adva
NMS_NAME="Mosaic Network Controller"
MGR_DIR="${NMS_HOME}/fsp_nm"
ELS_NAME="Embedded License Server"
ELS_DIR="${NMS_HOME}/els"
NI_NAME="Centralized CP"
NI_DIR="${NMS_HOME}/fsp_nm_ni"
MOD_NAME="MOD"
MNCWEB_NAME="MNC-WEB"
MOD_DIR="${NMS_HOME}/stacks"
MNC_VERSION="17.2.1"
NI_VERSION="17.2.1-DOCKER-SNAPSHOT"
TAPI_VERSION="17.2.1-SNAPSHOT"
CSM_VERSION="17.2.1-SNAPSHOT"
ELS_VERSION="R7.1.1"
UPGRADE_VERSION="15.3.1"
export NMS_HOME
export NMS_NAME
