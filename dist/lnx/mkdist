#!/bin/bash

. ./setenv.sh

ADVADIR=./../../..
FNMDIR=${ADVADIR}/fsp_nm
FNMDIST=${FNMDIR}/dist/release
LNXDIR=${FNMDIR}/dist/lnx
JREDIR=/opt
POSTGRESDIR=/opt
FLEXNETLSDIR=/opt
NIDIR=/opt

echo Removing old distribution...
rm -rf ${FNMDIST}/*
rm -f ${LNXDIR}/fsp_nm.tar.Z
rm -f ${LNXDIR}/$2_for_Linux_v$1.tar

#
# Copy linux shell scripts required for linux platform
#
echo Copying files from ${FNMDIR}/bin...
mkdir -p ${FNMDIST}/fsp_nm/bin
cp -p ${FNMDIR}/bin/fnm           ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/fem           ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/fnm.server    ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/changeUser.sh   ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/fnm_user.sh    ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/restoreDB     ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/mdlog         ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/cleanDB       ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/MakePDF       ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/createKeystore       ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/deleteKeystoreEntry       ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/displayKeyentries       ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/importCACertificate       ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/importp12container      ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/importSignedCertificate      ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/encrypt      ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/obfuscate      ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/obfuscate_ssl_password.sh      ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/encrypt_passphrase.sh      ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/generateCSR      ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/exportCertificate      ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/healthcheck_nms.sh    ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/cleanPostgresAfterUpgrade    ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/nmsadmin.sh    ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/activemq.sh    ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/activemqjmx.sh    ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/snmpforwarder.sh   ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/proxyserver.sh   ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/ivy_classpath.sh    ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/migrateENC.sh    ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/propup.sh    ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/serializable.packages    ${FNMDIST}/fsp_nm/bin/

#
# Ensure linux platform scripts are executable
#
chmod 755 ${FNMDIST}/fsp_nm/bin/*
chmod 700 ${FNMDIST}/fsp_nm/bin/restoreDB
chmod 700 ${FNMDIST}/fsp_nm/bin/cleanDB
chmod 700 ${FNMDIST}/fsp_nm/bin/cleanPostgresAfterUpgrade

#
# Create directories and copy libraries that are committed to source control
# Ensure libraries copied have the correct file permissions
#
echo Copying files from ${FNMDIR}/lib...
mkdir -p ${FNMDIST}/fsp_nm/lib
mkdir -p ${FNMDIST}/fsp_nm/lib/report
mkdir -p ${FNMDIST}/fsp_nm/lib/em

cp -p ${FNMDIR}/lib/ypdb-commons.jar           ${FNMDIST}/fsp_nm/lib/

echo Copying files from ${FNMDIR}/lib/em...
cp -p ${FNMDIR}/lib/em/jh.jar		   	   	   ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/jsnmp.jar		   	   ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/advaem.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/log4j.jar	           ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/advalic.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/providerproxy.jar       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/emcore.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/SNMP4J.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/emhelp.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/f7.jar		           ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/f7emhelp.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/jgoodies-forms.jar	   ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/jgoodies-common.jar     ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/fsp150.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/fsp1500.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/fsp2000.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/swingx.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/fsp3000.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/ftp.jar		           ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/itext.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/jcommon.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/jfreechart.jar	       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/bcprov.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/commons-logging.jar	   ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/gnu-crypto.jar		   ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/jgraphx.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/jradius-core.jar	       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/jradius-dictionary.jar  ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/guava.jar  			   ${FNMDIST}/fsp_nm/lib/em/

echo Copying files from ${FNMDIR}/lib...
cp -p ${FNMDIR}/lib/*.jar	   ${FNMDIST}/fsp_nm/lib/

chmod -R 644 ${FNMDIST}/fsp_nm/lib/*.jar
chmod -R 644 ${FNMDIST}/fsp_nm/lib/em/*.jar

echo Copying files from ${FNMDIR}/activemq/...
mkdir -p ${FNMDIST}/fsp_nm/activemq
cp -pr ${FNMDIR}/activemq/*    ${FNMDIST}/fsp_nm/activemq

echo Copying files from ${FNMDIR}/mibs/...
mkdir -p ${FNMDIST}/fsp_nm/mibs
cp -pr ${FNMDIR}/mibs/*    ${FNMDIST}/fsp_nm/mibs

#
# Create web services directory and copy over web service files committed to source control
#
echo Copying files from ${FNMDIR}/ws...
mkdir -p ${FNMDIST}/fsp_nm/ws
mkdir -p ${FNMDIST}/fsp_nm/ws/etc
cp -p  ${FNMDIR}/ws/etc/jetty.xml ${FNMDIST}/fsp_nm/ws/etc
cp -p  ${FNMDIR}/ws/etc/jetty_proxy.xml ${FNMDIST}/fsp_nm/ws/etc
cp -p  ${FNMDIR}/ws/etc/webdefault.xml ${FNMDIST}/fsp_nm/ws/etc
cp -p  ${FNMDIR}/ws/etc/cxf-crypto.properties ${FNMDIST}/fsp_nm/ws/etc
cp -p  ${FNMDIR}/ws/etc/cxfrealm.xml ${FNMDIST}/fsp_nm/ws/etc
cp -p  ${FNMDIR}/ws/etc/realm.properties ${FNMDIST}/fsp_nm/ws/etc

cp -pr ${FNMDIR}/ws/resources/tmf854 ${FNMDIST}/fsp_nm/ws/resources
cp -pr ${FNMDIR}/ws/resources/adva ${FNMDIST}/fsp_nm/ws/resources
echo Deleting CVS directories frow ws directory
rm -rf  ${FNMDIST}/fsp_nm/ws/resources/wsdl/CVS
rm -rf  ${FNMDIST}/fsp_nm/ws/resources/xsd/CVS
rm -rf  ${FNMDIST}/fsp_nm/ws/webapps/CVS

mkdir -p ${FNMDIST}/fsp_nm/ws/webapps
mkdir -p ${FNMDIST}/fsp_nm/ws/webapps/clientUpdate
mkdir -p ${FNMDIST}/fsp_nm/ws/webapps/customimages
mkdir -p ${FNMDIST}/fsp_nm/ws/webapps/stylesheet
mkdir -p ${FNMDIST}/fsp_nm/ws/webapps/proxy
mkdir -p ${FNMDIST}/fsp_nm/ws/webapps/map
mkdir -p ${FNMDIST}/fsp_nm/ws/webapps/charts
mkdir -p ${FNMDIST}/fsp_nm/ws/webapps_proxy
mkdir -p ${FNMDIST}/fsp_nm/ws/deploy
mkdir -p ${FNMDIST}/fsp_nm/ws/reportdb
mkdir -p ${FNMDIST}/fsp_nm/ws/healthreport
cp -pr ${FNMDIR}/ws/webapps/clientUpdate ${FNMDIST}/fsp_nm/ws/webapps/
cp -pr ${FNMDIR}/ws/webapps/customimages ${FNMDIST}/fsp_nm/ws/webapps/
cp -pr ${FNMDIR}/ws/webapps/stylesheet ${FNMDIST}/fsp_nm/ws/webapps/
cp -pr ${FNMDIR}/ws/webapps/map ${FNMDIST}/fsp_nm/ws/webapps/
cp -pr ${FNMDIR}/ws/webapps/charts ${FNMDIST}/fsp_nm/ws/webapps/
cp -p  ${FNMDIR}/ws/webapps/proxy/nmsproxy.pac ${FNMDIST}/fsp_nm/ws/webapps/proxy

#
# Create and fill templates directory from source repository content
#
echo Copying files from ${FNMDIR}/templates
mkdir -p ${FNMDIST}/fsp_nm/templates
cp -pr	${FNMDIR}/templates			${FNMDIST}/fsp_nm/

#
# Create and fill mappings directory from source repository content
#
echo Copying files from ${FNMDIR}/mappings
mkdir -p ${FNMDIST}/fsp_nm/mappings
cp -pr	${FNMDIR}/mappings			${FNMDIST}/fsp_nm/

#
# Create and fill security certificates directory from source repository content
#
echo Copying files from ${FNMDIR}/ssocerts
mkdir -p ${FNMDIST}/fsp_nm/ssocerts
cp -pr	${FNMDIR}/ssocerts			${FNMDIST}/fsp_nm/


#
# Create and fill postgres database application directory from source repository content
#
echo Copying postgres files from ${POSTGRESDIR} ...
cp -pr ${POSTGRESDIR}/postgres/12.4-postgres ${FNMDIST}/fsp_nm/postgres

echo Copying files from ${FNMDIR}/postgres...
cp -pr ${FNMDIR}/postgres/scripts ${FNMDIST}/fsp_nm/postgres
cp -pr ${FNMDIR}/postgres/support-files ${FNMDIST}/fsp_nm/postgres
cp -p ${FNMDIR}/postgres/support-files/postgres.server-lnx ${FNMDIST}/fsp_nm/postgres/support-files/postgres.server
rm -rf ${FNMDIST}/fsp_nm/postgres/data/*
mkdir -p ${FNMDIST}/fsp_nm/postgres/data
chmod 755 ${FNMDIST}/fsp_nm/postgres/support-files/postgres.server
chmod 755 ${FNMDIST}/fsp_nm/postgres/bin/*

#
# Create and fill examples directory from source repository content
#
echo Copying files from ${FNMDIR}/Examples/...
mkdir -p ${FNMDIST}/fsp_nm/Examples
mkdir -p ${FNMDIST}/fsp_nm/Examples/ECM-Templates
cp -pr ${FNMDIR}/Examples/ECM-Templates/*.xml ${FNMDIST}/fsp_nm/Examples/ECM-Templates

#
# Create and fill MNC DB scripts directory from source repository content
#
echo Copying files from ${FNMDIR}/scripts...
mkdir -p ${FNMDIST}/fsp_nm/scripts
cp -pr ${FNMDIR}/scripts/*.sql ${FNMDIST}/fsp_nm/scripts
cp -pr ${FNMDIR}/scripts/*.sh ${FNMDIST}/fsp_nm/scripts
cp -pr ${FNMDIR}/scripts/*.config ${FNMDIST}/fsp_nm/scripts
chmod 644 ${FNMDIST}/fsp_nm/scripts/*
chmod 600 ${FNMDIST}/fsp_nm/scripts/createUser.sql
chmod 755 ${FNMDIST}/fsp_nm/scripts/*.sh
mkdir -p ${FNMDIST}/fsp_nm/scripts/redundancy
cp -pr ${FNMDIR}/scripts/redundancy/*.sh ${FNMDIST}/fsp_nm/scripts/redundancy
chmod 755 ${FNMDIST}/fsp_nm/scripts/redundancy/*.sh
chmod 700 ${FNMDIST}/fsp_nm/scripts/redundancy/rr.sh

#
# Create and fill custom products directory from source repository content
#
echo Copying files from ${FNMDIR}/CustomProducts/...
mkdir -p ${FNMDIST}/fsp_nm/CustomProducts
cp -p ${FNMDIR}/CustomProducts/unmanaged.xml    ${FNMDIST}/fsp_nm/CustomProducts
cp -p ${FNMDIR}/CustomProducts/osa-proxy.xml    ${FNMDIST}/fsp_nm/CustomProducts
cp -p ${FNMDIR}/CustomProducts/osa-proxy.alarms ${FNMDIST}/fsp_nm/CustomProducts

#
# Create and fill dat directory from source repository content
#
echo Copying files from ${FNMDIR}/dat/...
mkdir -p ${FNMDIST}/fsp_nm/dat
cp -p ${FNMDIR}/dat/*.xml    ${FNMDIST}/fsp_nm/dat

#
# Create and fill db directory from source repository content
#
echo Copying yp db files from ${FNMDIR}/db/...
mkdir -p ${FNMDIST}/fsp_nm/db
cp -pr ${FNMDIR}/db/yp    ${FNMDIST}/fsp_nm/db

#
# Create and fill test directory from source repository content
#
echo Copying files from ${FNMDIR}/test/...
mkdir -p ${FNMDIST}/fsp_nm/test
cp -p ${FNMDIR}/test/log4j2.xml ${FNMDIST}/fsp_nm/test
chmod 640 ${FNMDIST}/fsp_nm/test/log4j2.xml

#
# Copy key MNC root files and give proper permissions
#
cp ${FNMDIR}/modules/nmscommon/src/main/resources/com/adva/nlms/common/version.properties ${FNMDIST}/fsp_nm/

echo Copying files from ${FNMDIR}/...
cp ${FNMDIR}/*.txt \
   ${FNMDIR}/jtrace.cfg \
   ${FNMDIR}/log4j2.xml \
   ${FNMDIR}/log4j2_client.xml \
   ${FNMDIR}/log4j_proxy.xml \
   ${FNMDIR}/log4j2AdvaTools.xml \
   ${FNMDIR}/logging.properties \
   ${FNMDIR}/fnm.properties \
   ${FNMDIR}/ne.versions \
   ${FNMDIR}/fnmclientinstall.properties \
   ${FNMDIR}/fnm.snmp.properties \
   ${FNMDIR}/fnmclient.properties \
   ${FNMDIR}/fnmtest.properties \
   ${FNMDIR}/mtosi.properties \
   ${FNMDIR}/migration.properties \
   ${FNMDIR}/pmEthernetTemplate.xml \
   ${FNMDIR}/limits.json \
   ${FNMDIST}/fsp_nm/

chmod 644 ${FNMDIST}/fsp_nm/*.txt \
          ${FNMDIST}/fsp_nm/jtrace.cfg \
          ${FNMDIST}/fsp_nm/fnm.properties \
          ${FNMDIST}/fsp_nm/ne.versions \
          ${FNMDIST}/fsp_nm/fnm.snmp.properties \
          ${FNMDIST}/fsp_nm/fnmclient.properties \
          ${FNMDIST}/fsp_nm/fnmclientinstall.properties \
          ${FNMDIST}/fsp_nm/logging.properties \
          ${FNMDIST}/fsp_nm/migration.properties \
          ${FNMDIST}/fsp_nm/fnmtest.properties \
          ${FNMDIST}/fsp_nm/version.properties \
          ${FNMDIST}/fsp_nm/limits.json

#
# Setup logging configuration
#
chmod 640 ${FNMDIST}/fsp_nm/log4j2.xml
chmod 640 ${FNMDIST}/fsp_nm/log4j2AdvaTools.xml
chmod 640 ${FNMDIST}/fsp_nm/log4j_proxy.xml
chmod 640 ${FNMDIST}/fsp_nm/log4j2_client.xml

echo Copying files from ${FNMDIR}/certs/...
mkdir -p ${FNMDIST}/fsp_nm/certs
cp -p ${FNMDIR}/certs/sec.properties ${FNMDIST}/fsp_nm/certs
chmod 644 ${FNMDIST}/fsp_nm/certs/sec.properties

mkdir -p ${FNMDIST}/fsp_nm/var/log
mkdir -p ${FNMDIST}/fsp_nm/log
chmod 777 ${FNMDIST}/fsp_nm/log

#
# Copy web files needed by web services
#
cp -pr ${FNMDIR}/var/web ${FNMDIST}/fsp_nm/var

#
# Copy JRE to use at runtime
#
echo Copying jre files from ${JREDIR} ....
mkdir -p ${FNMDIST}/share
chmod 751 ${FNMDIST}/share
cp -pr ${JREDIR}/jdk-********-1-jre ${FNMDIST}/share/jre

mkdir -p ${FNMDIST}/fsp_nm/els
cp -pr ${FLEXNETLSDIR}/els-3.1.1/* ${FNMDIST}/fsp_nm/els

chmod -R o-r ${FNMDIST}/share

cp ${LNXDIR}/uninstall-fsp_nm ${FNMDIST}/
chmod 700 ${FNMDIST}/uninstall-fsp_nm

#
# Extract production zip bundles containing build artifacts
#
echo Expanding production zip bundles ....
# For mediation server on linux want all the mediation and frontend libraries
unzip -o ${FNMDIR}/dist/lnx/extras/archives/production-frontend.zip -d ${FNMDIST}/fsp_nm
unzip -o ${FNMDIR}/dist/lnx/extras/archives/production-mediation-lib.zip -d ${FNMDIST}/fsp_nm
unzip -o ${FNMDIR}/dist/lnx/extras/archives/production-mediation-war.zip -d ${FNMDIST}/fsp_nm
unzip -o ${FNMDIR}/dist/lnx/extras/archives/production-mediation-config.zip -d ${FNMDIST}/fsp_nm

# Ensure permissions on all lib jar files
find ${FNMDIST}/fsp_nm/lib -name "*.jar" | xargs chmod 644
# Ensure permissions on other installed files
chmod 644 ${FNMDIST}/fsp_nm/docs/* \

if [ "$2" != "Mosaic_Network_Controller" ]
then
    echo Removing pdf files for branding
    rm -f ${FNMDIST}/fsp_nm/*.pdf
fi

#
# Create symbolic links to commonly used scripts to install root
#
cd ${FNMDIST}/fsp_nm
ln -s ./bin/fnm .
ln -s ./bin/fem .
ln -s ./bin/MakePDF .
cd ..

echo Changing user/group to root.root
chown -R root.root *

#
# Create TAR package of the MNC artifacts to install
#
echo Archiving...
/bin/tar chf ${LNXDIR}/fsp_nm.tar *

echo Compressing...
rm -f ${LNXDIR}/fsp_nm.tar.gz
/bin/gzip -9 ${LNXDIR}/fsp_nm.tar


#
# Include supplimental libraries required for postgres
#
echo Copying postgres dependency files from ${POSTGRESDIR} ....
cp -p ${POSTGRESDIR}/rhel8-pgdep/rhel8-pgdep.tgz ${LNXDIR}

cd ${LNXDIR}
chmod +x install
chmod +x setenv.sh

#
# Create distribution TAR package for linux platform install
#
/bin/tar cvf $2_for_Linux_v$1.tar install setenv.sh ni-${NI_VERSION}.tgz rhel8-pgdep.tgz fsp_nm.tar.gz

echo Done.
