#!/bin/bash
################################################################################
#
# Network Manager deinstallation script
################################################################################

. /etc/setenv.sh

CWD=`pwd`
MGR_DIR_FILE="${MGR_DIR}/bin/fnm.server"
ELS_DIR_FILE="${ELS_DIR}/els"
NI_DIR_FILE="${NI_DIR}/uninstallni.sh"
MOD_DIR_FILE="${MOD_DIR}/csm/uninstall-csm.sh"
MNCWEB_DIR_FILE="${MOD_DIR}/core/uninstall-core.sh"
COMPANY_SOFTWARE_FILES=("${MGR_DIR_FILE}" "${ELS_DIR_FILE}" "${MOD_DIR_FILE}" "${MNCWEB_DIR_FILE}")

uninstall_fspnm(){

#####################################a###########################################
# Shutdown servers

if [ -d "/usr/lib/systemd/system" ]
then
  systemctl -q is-active fnmserver.service
  RET=$?
  systemctl stop fnmserver.service
  #handle migration cases from non-systemd to systemd usage
  if [ $RET -ne 0 ]
  then
    ${MGR_DIR}/bin/fnm.server stop
  fi
  ${MGR_DIR}/bin/snmpforwarder.sh stop
  #
  systemctl -q is-active postgres.service
  RET=$?
  systemctl stop postgres.service
  #handle migration cases from non-systemd to systemd usage
  if [ $RET -ne 0 ]
  then
    ${MGR_DIR}/postgres/support-files/postgres.server stop
  fi
else
  ${MGR_DIR}/bin/fnm.server stop
  ${MGR_DIR}/bin/snmpforwarder.sh stop
  ${MGR_DIR}/postgres/support-files/postgres.server stop
fi

# sleep for 15 as systemd process occupies postgres user
sleep 15
/usr/sbin/userdel postgres
/usr/sbin/groupdel postgres

################################################################################
# Start deletion

echo "Uninstalling..."

USER_HOME=$(echo $(getent passwd $USER )| cut -d : -f 6)
rm -rf ${USER_HOME}/var/log/adva

# Remove links
rm -f /usr/bin/fnm
rm -f /etc/rc?.d/???fnm.server
rm -f /etc/rc?.d/???postgres
if [ -d "/usr/lib/systemd/system" ]
 then
   /usr/bin/systemctl disable /usr/lib/systemd/system/fnmserver.service
   /usr/bin/systemctl disable /usr/lib/systemd/system/postgres.service
    rm -f /usr/lib/systemd/system/fnmserver
    rm -f /usr/lib/systemd/system/postgres
    rm -f /usr/lib/systemd/system/fnmserver.service
    rm -f /usr/lib/systemd/system/postgres.service
 fi

rm -f /usr/lib/tmpfiles.d/postgres.conf

# Remove directories and files"
for item in $(cd ${MGR_DIR} ; ls -A) ; do
    if [[ "$item" != "els" ]] && [[ "$item" != "flexnetls" ]] ; then
        rm -rf ${MGR_DIR}/${item}
    fi
done

# remove MGR_DIR directory if empty
if [[ -e "${MGR_DIR}" ]] && [[ ! "$(ls -A "${MGR_DIR}")" ]] ; then
    echo "Removing ${MGR_DIR}..."
    rm -rf "${MGR_DIR}"
fi

echo "Done."
echo ""

echo "Uninstall completed."
echo ""

}

changefnmprop()
{
if [ -f "${NMS_HOME}/fsp_nm/fnm.properties" ]
then
     sed -i 's:com.adva.nlms.eod.evolution.enabled=true:com.adva.nlms.eod.evolution.enabled=false:g' "${NMS_HOME}/fsp_nm/fnm.properties"
fi
}

removesecrets()
{
for secret in mnc-postgresql-db-user mnc-postgresql-db-pw mnc-core-kafka-user mnc-core-kafka-pw; do
  if docker secret ls | grep -qw "$secret"; then
    docker secret rm "$secret"
  else
    echo "Docker secret $secret does not exist. Skipping."
  fi
done
}

removeconfigs()
{
 for config in mod-kafka-log4j mod-zookeeper-log4j; do
   if docker config ls | grep -qw "$config"; then
     docker config rm "$config"
   else
     echo "Docker config $config does not exist. Skipping."
   fi
 done
}

uninstall_els(){

if [[ -f ${ELS_DIR}/els ]] ; then
    ${ELS_DIR}/els uninstall
fi

if [[ -d ${ELS_DIR} ]] ; then
    rm -rf ${ELS_DIR}
fi

}

uninstall_ni(){

"${NI_DIR}"/uninstallni.sh

}

uninstall_csm(){
"${MOD_DIR}"/service-migration/uninstall-service-migration.sh
"${MOD_DIR}"/csm/uninstall-csm.sh
CSM_RETCODE=$?
"${MOD_DIR}"/core/uninstall-core.sh
CORE_RETCODE=$?
"${MOD_DIR}"/mnc-rproxy/uninstall-mnc-rproxy.sh
RPROXY_RETCODE=$?

if [ $CSM_RETCODE -eq 1 ] || [ $CORE_RETCODE -eq 1 ] || [ $RPROXY_RETCODE -eq 1 ]; then
  echo "MOD uninstallation failed for one or more components"
else
  removesecrets
  removeconfigs
  changefnmprop
  if [ -d "/usr/lib/systemd/system" ]
  then
   /usr/bin/systemctl disable /usr/lib/systemd/system/ncstacks.service
   rm -f /usr/lib/systemd/system/ncstacks
   rm -f /usr/lib/systemd/system/ncstacks.service
  fi
  rm -rf "${MOD_DIR}"
fi
}

uninstall_mncweb(){
"${MOD_DIR}"/core/uninstall-core.sh
CORE_RETCODE=$?
"${MOD_DIR}"/mnc-rproxy/uninstall-mnc-rproxy.sh
RPROXY_RETCODE=$?

if [ $CORE_RETCODE -eq 1 ] || [ $RPROXY_RETCODE -eq 1 ]; then
  echo "MNC WEB uninstallation failed for one or more components"
else
  removesecrets
  removeconfigs
  rm -rf "${MOD_DIR}"
fi
}

sys_cleanup(){
    echo
    echo "No company software found in local directories"
    echo
    echo "Cleaning obsolete files..."

    [[ -e "${NMS_HOME}/share" ]] && rm -rf "${NMS_HOME}/share"
    [[ -e "/etc/setenv.sh" ]] && rm -f /etc/setenv.sh
    [[ -e "${NMS_HOME}/uninstall-fsp_nm" ]] && rm -f uninstall-fsp_nm
    # remove fsp_nm directory if empty
    if [[ -e "${MGR_DIR}" ]] && [[ ! "$(ls -A "${MGR_DIR}")" ]] ; then
        rm -rf "${MGR_DIR}"
    fi

    echo "done."
    cd "$CWD"
    exit 0
}

get_software_name_from_file(){
    if [[ "${1}" == "${MGR_DIR_FILE}" ]] ; then
        echo "${NMS_NAME}"
    elif [[ "${1}" == "${ELS_DIR_FILE}" ]] ; then
        echo "${ELS_NAME}"
    elif [[ "${1}" == "${MOD_DIR_FILE}" ]] ; then
        echo "${MOD_NAME}"
    elif [[ "${1}" == "${MNCWEB_DIR_FILE}" ]] ; then
        echo "${MNCWEB_NAME}"
    fi
}

get_software_dir_from_name(){
    if [[ "${1}" == "${NMS_NAME}" ]] ; then
        echo "${MGR_DIR}"
    elif [[ "${1}" == "${ELS_NAME}" ]] ; then
        echo "${ELS_DIR}"
    elif [[ "${1}" == "${MOD_NAME}" ]] ; then
        echo "${MOD_DIR}"
    elif [[ "${1}" == "${MNCWEB_NAME}" ]] ; then
        echo "${MOD_DIR}"
    fi
}

script_menu() {
    menu_nr=0
    menu_array=()

    for local_file in "${COMPANY_SOFTWARE_FILES[@]}"; do
        if [[ ! -e "${local_file}" ]]; then
            continue
        fi
        software_name="$(get_software_name_from_file "${local_file}")"
        if [ "${software_name}" == "${MOD_NAME}" ]
        then
          modinstalled=true
        fi
        if [ "${modinstalled}" == "true" ] && [ "${software_name}" == "${MNCWEB_NAME}" ]
        then
          continue
        fi
        menu_nr=$((menu_nr + 1))
        menu_array[menu_nr]="${software_name}"
    done

    if [[ -z "${menu_array[1]}" ]]; then
        sys_cleanup
    fi

    menu_nr=$((menu_nr + 1))
    menu_array[menu_nr]="Exit."

    while true; do
        echo "Please type software number (1-${menu_nr}) to uninstall:"
        echo
        for row in $(seq 1 ${menu_nr}); do
            echo "    ${row})   ${menu_array[$row]}"
        done
        echo

        read -p "Selection: " menu_reply

        # Check if input is a number
        if ! [[ "$menu_reply" =~ ^[0-9]+$ ]]; then
            echo "Invalid input. Please enter a number."
            echo
            continue
        fi

        # Check if input is within range
        if (( menu_reply < 1 || menu_reply > menu_nr )); then
            echo "Invalid selection. Please choose a number from the list."
            echo
            continue
        fi

        break
    done

    if (( menu_reply == menu_nr )); then
        cd "$CWD"
        exit 0
    fi

    selected_software="${menu_array[$menu_reply]}"
    echo "This script will uninstall ${selected_software} from your system."
    echo
    echo "Do you really want to remove the ${selected_software} installation from"
    echo "$(get_software_dir_from_name "${selected_software}") [y/n]? "
    read reply

    case "$reply" in
        y|Y|yes|YES)
            if [[ "$selected_software" == "${NMS_NAME}" ]]; then
                uninstall_fspnm
            elif [[ "$selected_software" == "${ELS_NAME}" ]]; then
                uninstall_els
            elif [[ "$selected_software" == "${MOD_NAME}" ]]; then
                uninstall_ni
                uninstall_csm
            elif [[ "$selected_software" == "${MNCWEB_NAME}" ]]; then
                uninstall_mncweb
            fi
            ;;
        *)
            echo
            echo "Uninstall aborted."
            echo
            ;;
    esac

    script_menu
}


if [ -z "`id | grep uid=0`" ]
then
  echo "You must be root to execute this script."
  exit 1
fi

clear
script_menu