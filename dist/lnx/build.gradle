/*
 *   Copyright 2023 Adtran Networks SE. All rights reserved.
 */


import com.adva.gradle.imagepublisher.ImagePublisherTask
import groovy.json.JsonBuilder

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.regex.Matcher

plugins {
    id 'java'
    id 'com.adva.gradle.plugin.image-publisher'
}

apply from: "$rootProject.projectDir/parameters.gradle"

File assembleRoot = project.file("$project.buildDir/production/assemble")
File assembleServerDir = project.file("$assembleRoot/server")
File assembleDir = project.file("$assembleServerDir/fsp_nm")
File assembleShareDir = project.file("$assembleServerDir/share")
String packageDirString = "$project.buildDir/production/package"
File packageDir = project.file(packageDirString)
String imageFileName = "Mosaic_Network_Controller_for_Linux_v${Version}-${BuildNumber}"
String imageModFileName = "MOD_for_Linux_v${Version}-${BuildNumber}"
String tapiFileName = "tapi-${Version}-SNAPSHOT_MNC_${BuildNumber}"
String jsonFileName = "enc_${Version}_${BuildNumber}_components.json"
String modCsmFileName = "mod_csm-${CSM_VERSION}.tar.gz"
String tapiVersion = "${TAPI_VERSION}"
String tapiBuildNumber = "${BuildNumber}"
String niVerBuild = ""
String niVersion = "${NI_VERSION}"
String niBuildNumber = ""

// Determine the build type
String buildType = getStringPropWithDefault('PRODUCTION_BUILD_TYPE', 'Official')

boolean onDemandBuild = buildType != null && buildType.equals('OnDemand')
if (onDemandBuild) {
    // Adjust the image name to make it clear that is is an 'On Demand' image and not an official image
    String builder = System.getenv('TRIGGEREDBY')
    if (builder == null) builder = 'unknown'

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
    String timestamp = LocalDateTime.now().format(formatter)
    imageFileName = "MNC_for_Lnx_v${Version}-${BuildNumber}_${builder}_${timestamp}-JAVA17"
    print("Image publication configured for 'On Demand' build\n")
}
var List<String> encImages =  [
        project.file("${packageDir}/${imageFileName}.tar"),
        project.file("${packageDir}/tapi/${tapiFileName}.tgz"),
        project.file("${packageDir}/${jsonFileName}")
]
// Default image publisher configuration used for publishing official builds
imagePublisher {
    // Must use a closure for setting image files since the name of the tapi file
    // is not known until the upload task starts execution
    setImageFiles {
        encImages
    }

    addTarget publicationTargetName, publicationTarget
    addTarget releasePublicationTargetName, releasePublicationTarget
}

// Ensure that image publication occurs after the production image is built if both tasks are specified in the same execution
tasks.publishImage.mustRunAfter('packageProductionImage')

// Change success message on team city to include the image produced
tasks.publishImage.doLast {
    print("##teamcity[buildStatus status='SUCCESS' text='Success: published ${imageFileName}.tar']\n")
}


task publishToSitHost (type: ImagePublisherTask) {

    mustRunAfter('packageProductionImage')
    dependsOn('extractManifestJsonAndRenameTapi')
    dependsOn('generateJSON')
    target = publicationTargetName
    destination = '/opt/ftp/enc/builds/production'
    updateSymlink = true
    segmentByDay = true
    cleanup = false
    extractZip = false

    setFiles  {

        return encImages
    }
    doLast {
        String BuildNumberFinal = BuildNumber.replace("B","")
        print("##teamcity[buildStatus status='SUCCESS' text='Success: published ${imageFileName}.tar']\n")
        print("##teamcity[setParameter name='PRODUCT_BUILD_NUMBER' value='${BuildNumberFinal}']")
    }
}

task publishSyncToSitHost (type: ImagePublisherTask) {

    target = publicationTargetName
    destination = '/opt/ftp/enc/builds/production'
    updateSymlink = true
    segmentByDay = true
    cleanup = false
    extractZip = false

    setFiles  {
        rootProject.file("modules/gnss/docker/deploy/SyncAssurance_v${Version}-${BuildNumber}.tar.gz")
    }

}

task publishEncDsToSitHost (type: ImagePublisherTask) {
    target = publicationTargetName
    destination = '/opt/ftp/enc/builds/production'
    updateSymlink = true
    segmentByDay = true
    cleanup = false
    extractZip = false

    setFiles  {
        rootProject.file("docker/deploy/enc-ds-w/dist/enc-ds-${Version}-${BuildNumber}.tar.gz")
    }
}

task publishEncDsK8sToSitHost (type: ImagePublisherTask) {
    target = publicationTargetName
    destination = '/opt/ftp/enc/builds/production'
    updateSymlink = true
    segmentByDay = true
    cleanup = false
    extractZip = false

    setFiles  {
        rootProject.file("docker/deploy/enc-ds-w/dist/mnc-ds-k8s-${Version}-${BuildNumber}.tar.gz")
    }
}

task publishToReleaseHost (type: ImagePublisherTask) {

    //mustRunAfter('packageProductionImage')
    //dependsOn('extractManifestJsonAndRenameTapi')
    target = releasePublicationTargetName
    destination = "/projects/nms/FNM/versions/NM$Version/dist/$BuildNumber"
    if(IsPatch)
        destination += "-patch"
    updateSymlink = false
    segmentByDay = false
    cleanup = false
    extractZip = false

    setFiles  {
        [
                project.file("${packageDir}/${imageFileName}.tar"),
                //project.file("${packageDir}/tapi/${tapiFileName}.tgz")
                (
                    project.fileTree("${packageDir}/tapi") {
                        include 'tapi-B*.tgz'
                    }
                ).getSingleFile()

        ]
    }

}

task publishSyncToReleaseHost (type: ImagePublisherTask) {

    target = releasePublicationTargetName
    destination = "/projects/nms/FNM/versions/NM$Version/dist/$BuildNumber"
    if(IsPatch)
        destination += "-patch"
    updateSymlink = false
    segmentByDay = false
    cleanup = false
    extractZip = false

    setFiles  {
        rootProject.file("modules/gnss/docker/deploy/SyncAssurance_v${Version}-${BuildNumber}.tar.gz")
    }

}

task publishOnDemandImage (type:ImagePublisherTask) {
    mustRunAfter('packageProductionImage')
    dependsOn('extractManifestJsonAndRenameTapi')
    dependsOn('generateJSON')
    destination =  '/opt/FNMInst/ad-hoc-dev-builds'
    updateSymlink = false
    segmentByDay = true
    cleanup = false
    extractZip = false
    setFiles  {
        [
                project.file("${packageDir}/${imageFileName}.tar"),
                project.file("${packageDir}/tapi/${tapiFileName}.tgz"),
                project.file("${packageDir}/${jsonFileName}")
        ]
    }
    doLast {
        if (onDemandBuild) {
            // Notify the user of the image published via Team City API
            String targetDir =  LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))

            // Create simple html file to add to build artifacts on Team City
            File resultHtml = file("$projectDir/result.html")

            resultHtml.withWriter('UTF8') {BufferedWriter writer ->
                writer.write("<HTML><BODY><P>" +
                        "On demand image <a href=\"https://gdn-s-sitnms/FNMInst/ad-hoc-dev-builds/$targetDir/${imageFileName}.tar\">" +
                        "${imageFileName}.tar</a> generated</P><P>Full URL is https://gdn-s-sitnms/FNMInst/ad-hoc-dev-builds/$targetDir/${imageFileName}.tar</P>"+
                        "<P>All on demand images can be found at <a href=\"http://gdn-s-sitnms/FNMInst/ad-hoc-dev-builds\">" +
                        "https://gdn-s-sitnms/FNMInst/ad-hoc-dev-builds</a></P>" +
                        "<BODY></HTML>")
            }
        }
    }
}

task publishOnDemandImageToSitHost (type:ImagePublisherTask) {
    mustRunAfter('packageProductionImage')
    dependsOn('extractManifestJsonAndRenameTapi')
    dependsOn('generateJSON')
    target = publicationTargetName
    destination =  '/opt/ftp/enc/builds/ad-hoc'
    updateSymlink = false
    segmentByDay = true
    cleanup = false
    extractZip = false
    setFiles  {
        [
                project.file("${packageDir}/${imageFileName}.tar"),
                project.file("${packageDir}/tapi/${tapiFileName}.tgz"),
                project.file("${packageDir}/${jsonFileName}")
        ]
    }
    doLast {
        if (onDemandBuild) {
            // Notify the user of the image published via Team City API
            String targetDir =  LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))

            // Create simple html file to add to build artifacts on Team City
            File resultHtml = file("$projectDir/result.html")

            resultHtml.withWriter('UTF8') {BufferedWriter writer ->
                writer.write("<HTML><BODY><P>" +
                        "On demand image <a href=\"https://gdn-s-sitnms1/enc/builds/ad-hoc/$targetDir/${imageFileName}.tar\">" +
                        "${imageFileName}.tar</a> generated</P><P>Full URL is https://gdn-s-sitnms1/enc/builds/ad-hoc/$targetDir/${imageFileName}.tar</P>"+
                        "<P>All on demand images can be found at <a href=\"https://gdn-s-sitnms1/enc/builds/ad-hoc/\">" +
                        "https://gdn-s-sitnms1/enc/builds/ad-hoc</a></P>" +
                        "<BODY></HTML>")
            }
        }
    }
}

configurations {
    jre
    jreWin
    // NOTE: This configuration is no longer used.  The ELS image is pulled directly from the
    // ELS artifactory repository using ant 'get' task since this repository does not follow
    // maven conventions. This logic is retained in the case that the ELS repository may one
    // day follow maven conventions.
    els
    postgres
    postgres_contrib
    rhel_postgres_rpms
    ni_snapshot
    tapi_snapshot
    mod_csm_snapshot
}

dependencies {
    jre group: 'com.adva.enc.production.distribution.linux', name: 'jre', version: '**********', ext: 'zip'
    jreWin group: 'com.adva.enc.production.distribution.windows', name: 'jre', version: '**********', ext: 'zip'
    postgres group: 'com.adva.enc.production.distribution.linux', name: 'postgres', version: '17.4', ext: 'zip'
    postgres_contrib group: 'com.adva.enc.production.distribution.linux', name: 'postgres-contrib', version: '17.4', ext: 'zip'
    rhel_postgres_rpms group: 'com.adva.enc.production.distribution.linux', name: 'postgres-rhel-rpm', version: '9.2', ext: 'tgz'

    ni_snapshot group: "com.adva", name: "ni", version: NI_VERSION, ext: "tgz"
    tapi_snapshot group: "com.adva", name: "tapi", version: TAPI_VERSION, ext: "tgz"
    mod_csm_snapshot group: "com.adtran.enc", name: "mod_csm", version: CSM_VERSION, ext: "tar.gz"
}

// Insure targets for symlinks are in place
task prepareForLinks(type: Copy) {
    from(fileTree(fspRoot)) {
        include "bin/fnm"
        include "bin/fem"
        include "bin/MakePDF"
    }
    into(assembleDir)
}

task createFnmLink(type: Exec) {
    dependsOn(prepareForLinks)
    workingDir assembleDir
    commandLine 'ln', '-s', 'bin/fnm', 'fnm'

    enabled = !file("$assembleDir/bin/fnm").exists()
}

task createFemLink(type: Exec) {
    dependsOn(prepareForLinks)
    workingDir assembleDir
    commandLine 'ln', '-s', 'bin/fem', 'fem'

    enabled = !file("$assembleDir/bin/fem").exists()
}

task createMakePDFLink(type: Exec) {
    dependsOn(prepareForLinks)
    workingDir assembleDir
    commandLine 'ln', '-s', 'bin/MakePDF', 'MakePDF'

    enabled = !file("$assembleDir/bin/MakePDF").exists()
}

task createSymlinks() {
    dependsOn(createFnmLink)
    dependsOn(createFemLink)
    dependsOn(createMakePDFLink)
}

// There are a few items that are not copied.
// These items must be handled differently and setup here
// in order to be included in the tar archive.
task assembleEnc() {
    dependsOn(createSymlinks)
    doFirst{
        mkdir "$assembleDir/ws/webapps/proxy"
        mkdir "$assembleDir/ws/webapps_proxy"
        mkdir "$assembleDir/ws/deploy"
        mkdir "$assembleDir/ws/reportdb"
        mkdir "$assembleDir/ws/healthreport"
        mkdir "$assembleDir/ssocerts"
        mkdir "$assembleDir/postgres/data"
        mkdir "$assembleDir/var/log"
        mkdir "$assembleDir/log"
        ant.chmod(dir: "$assembleDir/var/log", perm: "777")
    }
}

String elsArtifact = "$buildDir/$elsLinuxArtifact"

/*
 * Task to fetch the ELS artifact from artifactory.
 * The ELS artifactory repository does not follow maven conventions so use ant get task to fetch the image.
 */
task fetchEls() {
    doLast {
        // Ensure build directory exists
        mkdir(buildDir)
        ant.get(src: elsLinuxArtifactURL,
                dest: elsArtifact,
                username: artifactoryReadUser,
                password: artifactoryReadPassword,
                verbose: 'on')
    }
}

task packageCoreEnc(type: Tar) {
    group('enc.production')
    description('Create the tar file that contains all the MNC resources needed by the installer')

    dependsOn(assembleEnc)
    dependsOn(fetchEls)
    dependsOn(":dist:packageClientUpload")
    // dependsOn(setOwnership)

    // Dependency relationships required for gradle 8 but not really required for production build
    mustRunAfter(":installYpDb")
    mustRunAfter(mod_target(mod_advabase, 'webappWar'))
    mustRunAfter(mod_target(mod_jolokia, 'webappWar'))
    mustRunAfter(mod_target(mod_advabase, 'webappWar'))
    mustRunAfter(mod_target(mod_ethernet, 'webappWar'))
    mustRunAfter(mod_target(mod_fiber, 'webappWar'))
    mustRunAfter(mod_target(mod_mltopologyui2, 'replaceWarFile'))
    mustRunAfter(mod_target(mod_sync, 'webappWar'))
    mustRunAfter(mod_target(mod_wdm, 'webappWar'))
    mustRunAfter(mod_target(mod_user, 'webappWar'))
    mustRunAfter(':packageMediationLibArtifacts')
    mustRunAfter(':packageMediationConfigArtifacts')
    mustRunAfter(':packageMediationWarArtifacts')
    // FNMD-111985: Removal of PV UI from build
    // mustRunAfter(mod_target(mod_pvhelp, 'webappWar'))

    destinationDirectory = packageDir

    archiveBaseName = 'fsp_nm'
    archiveExtension = 'tar'

    // There are duplicate libs spanning the Frontend and Mediation production lib zip bundles.
    // These bundles are combined into a single lib directory for linux for the installer.
    // The 'duplicate' libraries are the same, so just exclude them.
    duplicatesStrategy DuplicatesStrategy.EXCLUDE

    compression = Compression.NONE

    String tarroot = "fsp_nm"

    // Night build production bundles
    from(zipTree(mediationLibBundle)) {
        exclude "**/javafx-*-win.jar"

        into(tarroot)

        doLast {
            requireFile(mediationLibBundle)
        }
    }
    from(zipTree(mediationConfigBundle)) {
        into(tarroot)

        doLast {
            requireFile(mediationConfigBundle)
        }
    }
    from(zipTree(mediationWarBundle)) {
        into(tarroot)

        doLast {
            requireFile(mediationWarBundle)
        }
    }
    from(zipTree(frontendBundle)) {
        exclude "**/javafx-*-win.jar"

        into(tarroot)

        doLast {
            requireFile(frontendBundle)
        }
    }

    from(fileTree(fspRoot)) {
        include "bin/**"
        exclude "bin/**/*.bat"
        exclude "bin/**/*.exe"
        exclude "bin/**/*.dll"

        // Exclude these files directly -- not currently included in linux mkdist script
        exclude "bin/cleanMysqlAfterUpgrade"
        exclude "bin/cosnaming"
        exclude "bin/dbimport"
        exclude "bin/getpwd.ps1"
        exclude "bin/input.txt"
        exclude "bin/interfacecheck"
        exclude "bin/isAdmin.vbs"
        exclude "bin/javaevent.config"
        exclude "bin/jmxClient.sh"
        exclude "bin/junit"
        exclude "bin/mdcli"
        exclude "bin/migrate.sh"
        exclude "bin/run_provider"
        exclude "bin/startServer"
        exclude "bin/stopServer"
        exclude "bin/workbench"
        exclude "bin/restoreDB"

        into(tarroot)
        fileMode = 0755

        doLast {
            requireDirectory(rootProject.file("bin"))
        }
    }

    from(fileTree(fspRoot)) {
        include "activemq/**"
        into(tarroot)

        doLast {
            requireDirectory(rootProject.file("activemq"))
        }
    }

    from(fileTree(fspRoot)) {
        include "mibs/**"
        into(tarroot)
        fileMode = 0644

        doLast {
            requireDirectory(rootProject.file("mibs"))
        }
    }

    from(fileTree(fspRoot)) {
        include "ws/etc/jetty.xml"
        include "ws/etc/jetty_proxy.xml"

        into(tarroot)
        fileMode = 0644

        filter { String line ->
            // Disable redeployment of web applications in production images.
            if (line.contains("Set name=\"scanInterval\"><Property name=\"jetty.deploy.scanInterval\"")) {
                line = line.replace("default=\"10\"", "default=\"10\"");
            }
            return line
        }

        doLast {
            requireFile(rootProject.file("ws/etc/jetty.xml"))
            requireFile(rootProject.file("ws/etc/jetty_proxy.xml"))
        }
    }


    from(fileTree(fspRoot)) {
        include "ws/etc/webdefault.xml"
        include "ws/etc/cxf-crypto.properties"
        include "ws/etc/cxfrealm.xml"
        include "ws/etc/realm.properties"

        include "ws/resources/adva/**"

        include "ws/webapps/customimages/**"
        include "ws/webapps/stylesheet/**"
        include "ws/webapps/map/**"
        include "ws/webapps/charts/**"
        include "ws/webapps/proxy/nmsproxy.pac"
        include "ws/webapps/client/**"

        into(tarroot)
        fileMode = 0644

        doLast {
            requireFile(rootProject.file("ws/etc/webdefault.xml"))
            requireFile(rootProject.file("ws/etc/cxf-crypto.properties"))
            requireFile(rootProject.file("ws/etc/cxfrealm.xml"))
            requireFile(rootProject.file("ws/etc/realm.properties"))

            requireDirectory(rootProject.file("ws/resources/adva"))

            requireDirectory(rootProject.file("ws/webapps/customimages"))
            requireDirectory(rootProject.file("ws/webapps/stylesheet"))
            requireDirectory(rootProject.file("ws/webapps/map"))
            requireDirectory(rootProject.file("ws/webapps/charts"))
            requireFile(rootProject.file("ws/webapps/proxy/nmsproxy.pac"))
            requireDirectory(rootProject.file("ws/webapps/client"))
        }
    }

    from(fileTree("$fspRoot/ws/resources/tmf854")) {
        into("$tarroot/ws/resources")
        fileMode = 0644

        doLast {
            requireDirectory(rootProject.file("ws/resources/tmf854"))
        }
    }

    from(fileTree(fspRoot)) {
        include "templates/**"
        into(tarroot)
        fileMode = 0644

        doLast {
            requireDirectory(rootProject.file("templates"))
        }
    }

    from(fileTree(fspRoot)) {
        include "mappings/**"
        into(tarroot)
        fileMode = 0644

        doLast {
            requireDirectory(rootProject.file("mappings"))
        }
    }

    from(fileTree(fspRoot)) {
        include "Examples/ECM-Templates/*.xml"
        into(tarroot)
        fileMode = 0644

        doLast {
            requireDirectory(rootProject.file("Examples/ECM-Templates"))
        }
    }

    from(fileTree(fspRoot)) {
        include "scripts/**/*.sql"
        include "scripts/**/*.config"
        exclude "**/checkDBSchemaConsistency.sql"
        exclude "**createUser.sql"
        into(tarroot)
        fileMode = 0644

        doLast {
            requireDirectory(rootProject.file("scripts"))
        }
    }

    // Split out createUser to set a specific permission
    from(fileTree(fspRoot)) {
        include "scripts/**/createUser.sql"
        into(tarroot)
        fileMode = 0600

        doLast {
            requireFile(rootProject.file("scripts/createUser.sql"))
        }
    }

    from(fileTree(fspRoot)) {
        include "scripts/**/*.sh"
        exclude "**/rr.sh"
        into(tarroot)
        fileMode = 0755

        doLast {
            requireDirectory(rootProject.file("scripts"))
        }
    }

    from(fileTree(fspRoot)) {
        include "scripts/**/rr.sh"
        into(tarroot)
        fileMode = 0700
    }

    from(fileTree(fspRoot)) {
        include "CustomProducts/unmanaged.xml"
        include "CustomProducts/osa-proxy.xml"
        include "CustomProducts/osa-proxy.alarms"
        into(tarroot)
        fileMode = 0644

        doLast {
            requireFile(rootProject.file("CustomProducts/unmanaged.xml"))
            requireFile(rootProject.file("CustomProducts/osa-proxy.xml"))
            requireFile(rootProject.file("CustomProducts/osa-proxy.alarms"))
        }
    }

    from(fileTree(fspRoot)) {
        include "dat/*.xml"
        into(tarroot)
        fileMode = 0644

        doLast {
            requireDirectory(rootProject.file("dat"))
        }
    }

    from(fileTree(fspRoot)) {
        include "db/yp/**"
        into(tarroot)
        fileMode = 0644
    }

    from(fileTree(fspRoot)) {
        include "test/log4j2.xml"
        into(tarroot)
        fileMode = 0666

        doLast {
            requireDirectory(rootProject.file("db/yp"))
        }
    }

    from("$fspRoot/modules/nmscommon/src/main/resources/com/adva/nlms/common/version.properties") {
        into(tarroot)

        doLast {
            requireFile(rootProject.file("modules/nmscommon/src/main/resources/com/adva/nlms/common/version.properties"))
        }
    }

    from(fileTree(fspRoot)) {
        include "*.txt"
        include "jtrace.cfg"
        include "log4j2.xml"
        include "log4j2_client.xml"
        include "log4j_proxy.xml"
        include "log4j2AdvaTools.xml"
        include "logging.properties"
        include "fnm.properties"
        include "ne.versions"
        include "fnmclientinstall.properties"
        include "fnm.snmp.properties"
        include "fnmclient.properties"
        include "fnmtest.properties"
        include "mtosi.properties"
        include "migration.properties"
        include "pmEthernetTemplate.xml"
        include "limits.json"
        into(tarroot)
        fileMode = 0644

        doLast {
            requireFile(rootProject.file("jtrace.cfg"))
            requireFile(rootProject.file("log4j2.xml"))
            requireFile(rootProject.file("log4j2_client.xml"))
            requireFile(rootProject.file("log4j_proxy.xml"))
            requireFile(rootProject.file("log4j2AdvaTools.xml"))
            requireFile(rootProject.file("logging.properties"))
            requireFile(rootProject.file("fnm.properties"))
            requireFile(rootProject.file("ne.versions"))
            requireFile(rootProject.file("fnmclientinstall.properties"))
            requireFile(rootProject.file("fnm.snmp.properties"))
            requireFile(rootProject.file("fnmclient.properties"))
            requireFile(rootProject.file("fnmtest.properties"))
            requireFile(rootProject.file("mtosi.properties"))
            requireFile(rootProject.file("migration.properties"))
            requireFile(rootProject.file("pmEthernetTemplate.xml"))
            requireFile(rootProject.file("limits.json"))
        }
    }

    from(fileTree(fspRoot)) {
        include "log4j2.xml"
        include "log4j2AdvaTools.xml"
        include "log4j_proxy.xml"
        into(tarroot)
        fileMode = 0666

        doLast {
            requireFile(rootProject.file("log4j2.xml"))
            requireFile(rootProject.file("log4j2AdvaTools.xml"))
            requireFile(rootProject.file("log4j_proxy.xml"))
        }
    }

    from(fileTree(fspRoot)) {
        include "certs/sec.properties"
        into(tarroot)
        fileMode = 0644

        doLast {
            requireFile(rootProject.file("certs/sec.properties"))
        }
    }

    from(fileTree(fspRoot)) {
        include "var/web/**"
        // These are taken from the production mediation config bundle
        exclude "var/web/data/CompiledMibs/**"
        into(tarroot)
        fileMode = 0644

        doLast {
            requireDirectory(rootProject.file("var/web/data/CompiledMibs"))
        }
    }

    from("uninstall-fsp_nm") {
        fileMode = 0700

        doLast {
            requireFile(file("uninstall-fsp_nm"))
        }
    }

    from { zipTree { configurations.jre.singleFile } } {
        into("share/jre")
    }

    from(fileTree(fspRoot)) {
        include "postgres/scripts/**"
        include "postgres/support-files/**"
        into(tarroot)

        doLast {
            requireDirectory(rootProject.file("postgres/scripts"))
            requireDirectory(rootProject.file("postgres/support-files"))
        }
    }

    from { zipTree { configurations.postgres.singleFile } } {
        exclude "bin/**"
        exclude "scripts/**"
        into("$tarroot/postgres")
    }

    // Must split the bin files out into a separate extraction to apply proper file mode
    // Zip files don't carry the correct file permissions
    from { zipTree { configurations.postgres.singleFile } } {
        include "bin/**"
        include "scripts/**"
        into("$tarroot/postgres")
        fileMode = 0755
    }

    from { zipTree { configurations.postgres_contrib.singleFile } } {
        into("$tarroot/postgres")
    }

    // Package everything but the symlinks
    // Gradle does not handle symlinks properly and these will be added separately
    from(fileTree(assembleServerDir)) {
        exclude "fsp_nm/fem"
        exclude "fsp_nm/fnm"
        exclude "fsp_nm/MakePDF"
    }

    doLast {
        requireDirectory(assembleServerDir)
    }

    from(packageClientDir) {
        into("$tarroot/ws/webapps/clientUpdate/${Version}-${BNUM}-WIN")

        doLast {
            requireDirectory(packageClientDir)
        }
    }
}

// Work around for gradle bug where creating TAR files does not handle symlinks properly.
// paackageEncCore creates the base (uncompressed) tar file and this task appends to that tar file the symlinks.
// NOTE you can update a compressed tar file.
task packageEncSymlinks(type: Exec) {
    // First create the tar file with the bulk of MNC content
    dependsOn(packageCoreEnc)

    workingDir assembleServerDir

    commandLine 'tar', '--update', '-f', "$packageDir/fsp_nm.tar", 'fsp_nm/fem', 'fsp_nm/fnm', 'fsp_nm/MakePDF'
}

task packageEnc(type: Exec) {
    group('enc.production')
    description('Compress the MNC tar file used by installer that contains all MNC resources used by the installer')

    // create the MNC zip file with symlinks
    dependsOn(packageEncSymlinks)

    // Compress the tar file created.  Due to a gradle bug, compression is done last.
    workingDir packageDir

    commandLine 'gzip', '-9', "$packageDir/fsp_nm.tar"
}

task setScriptPermissions() {
    doFirst {
        ant.chmod(file: "install", perm: "+x")
        ant.chmod(file: "setenv.sh", perm: "+x")
    }
}

task setModScriptPermissions() {
    doFirst {
        ant.chmod(file: "installmod", perm: "+x")
        ant.chmod(file: "setenv.sh", perm: "+x")
        ant.chmod(file: "uninstall-mod", perm: "+x")
    }
}

/**
 * Download the TAPI artifact from artifact
 * This is the first step in the renaming process to make it easier to understand where this artifact comes from
 */
task downloadTapiTar(type: Copy) {
    from { configurations.tapi_snapshot.singleFile }
    into("${packageDir}/tapi")

    rename { String filename ->
        return "tapi.tgz"
    }
}

/**
 * Extract the tapi.tgz file from the downloaded artifact so we have access to manifest.json
 * which has information about the TAPI artifact build number
 */
task extractEncTapiTar(type: Copy) {
    dependsOn(downloadTapiTar)
    from( tarTree("${packageDir}/tapi/tapi.tgz") ) {
        include('images/enc-tapi.tgz')
    }
    into("${packageDir}/tapi")

    rename { String filename ->
        // This file is not really gziped even though the extension says it is
        // Using 'gzip' extension causes extraction of manifest.json to fail
        return "enc-tapi.tar"
    }
}

/**
 * Extract the manifest.json file and extract the TAPI build number from it.
 */
task extractManifestJsonAndRenameTapi(type: Copy) {
    dependsOn(extractEncTapiTar)
    from( tarTree("${packageDir}/tapi/images/enc-tapi.tar") ) {
        include('manifest.json')
    }
    into("$packageDir/tapi")

    doLast {
        String manifestContent = project.file("${packageDir}/tapi/manifest.json").text
        Matcher matcher = manifestContent =~ /enc-tapi:\d+\.\d+\.\d+\-(\d+)\"/
        if (matcher.find()) {
            tapiBuildNumber = matcher.group(1)
            // Generate the name to use for the downloaded TAPI artifact
            tapiFileName = "tapi-B${tapiBuildNumber}_MNC_${Version}-${BuildNumber}"
        } else {
            logger.error("Failed to obtain TAPI build number from manifest.json")
            // Fallback to use prior tapi naming
            tapiFileName = "tapi-${Version}-SNAPSHOT_MNC_${BuildNumber}"
        }
        Matcher versionmatcher = manifestContent =~ /enc-tapi:(\d+\.\d+\.\d+)\-/
        //tapiVersion to use in JSON file
        if (versionmatcher.find()) {
            tapiVersion = versionmatcher.group(1)
        }

        project.file("${packageDir}/tapi/tapi.tgz").renameTo("${packageDir}/tapi/${tapiFileName}.tgz")
        //set the list with new tapi name
        encImages.set(1, project.file("${packageDir}/tapi/${tapiFileName}.tgz"))
        logger.quiet("TAPI artifact renamed to $tapiFileName")
    }
}
task downloadNItar(type: Copy) {
    from { configurations.ni_snapshot.singleFile }
    into("${packageDir}/scratch")
}

task extractNIVersion(type: Copy) {
    dependsOn(downloadNItar)
    from(tarTree("${packageDir}/scratch/ni-${NI_VERSION}.tgz")) {
            include('NIVersion.txt')
    }
    into("$packageDir/ni")
}
task extractNIBuildNumber {
    dependsOn(extractNIVersion)
    //docker ni package has new format and data in version file
    doLast {
        def file = new File("${packageDir}/ni/NIVersion.txt")
        String data= file.filterLine { line ->
            line.contains('enc/ni/ni:')
        }
        niVerBuild = data.takeAfter(":").trim()
        niVersion = niVerBuild.takeBefore("-")
        niBuildNumber = niVerBuild.takeAfter("-")
        println("niVersion:$niVersion")
        println("niBuildNumber:$niBuildNumber")
        //Following is for non NI docker package NIVersion format
        //niVersion = project.file("${packageDir}/ni/NIVersion.txt").text.takeBefore("-")
        //niBuildNumber = project.file("${packageDir}/ni/NIVersion.txt").text.takeAfter("-")
    }
}

task generateJSON() {
    dependsOn(extractNIBuildNumber)
    String BuildNumberFinal = BuildNumber.replace("B","")

    doLast {
        def jsonBuilder = new JsonBuilder()
        jsonBuilder {
            enc (
                    release : "${Version}",
                    build : "${BuildNumberFinal}",
                    components : {
                        ni(
                                release: "${niVersion}",
                                build: "${niBuildNumber}"
                        )
                        tapi(
                                release: "${tapiVersion}",
                                build: "${tapiBuildNumber}"
                        )
                        els(
                                release: "${elsReleaseVersion}",
                                build: "${elsReleaseVersionSuffix}"
                        )
                        SyncAssurance(
                                release: "${Version}",
                                build: "${BuildNumberFinal}"
                        )
                    }
            )
        }
        File file = new File("$packageDir/${jsonFileName}")
        file.write(jsonBuilder.toPrettyString())
    }

}

// Prior to publishing MNC file, also download and prepare the TAPI artifact as it will also be uploaded
tasks.publishImage.dependsOn(extractManifestJsonAndRenameTapi)
tasks.publishImage.dependsOn(generateJSON)

task generateOnDemandModCSM()
{
    dependsOn(mod_target(mod_eod_deploy, 'extractMODPackage'))
    dependsOn(mod_target(mod_eod_deploy, 'regenerateJobAndNotificationManager'))
    dependsOn(mod_target(mod_eod_deploy, 'regenerateCSM'))
}

task packageModCSM(type: Tar)
{
    dependsOn(generateOnDemandModCSM)
    destinationDirectory = packageDir
    archiveFileName = modCsmFileName
    compression = Compression.GZIP
    from(mod_eod_deploy.file.getCanonicalPath()+"/build/production/package/mod/MOD_CSM") {
        include '**/*'
        into 'MOD_CSM'
    }
}


// main/top level task for creating production tar image file
task packageProductionImage(type: Tar) {
    group('enc.production')
    description('Create the tar file containing the linux MNC installer')

    dependsOn(packageEnc)
    dependsOn(setScriptPermissions)

    destinationDirectory = packageDir

    archiveBaseName = imageFileName
    archiveExtension = 'tar'

    from("$packageDir/fsp_nm.tar.gz")
    from("install")
    from("setenv.sh")
    from { elsArtifact  } {
        rename { String filename ->
            return "els-${elsReleaseVersion}.tgz"
        }
    }
    from { configurations.ni_snapshot.singleFile }
    from { configurations.rhel_postgres_rpms.singleFile }
    if(onDemandBuild) {
        dependsOn(packageModCSM)
        from {"$packageDir/$modCsmFileName"}
    }
    else
    {
        from { configurations.mod_csm_snapshot.singleFile }
    }

    doLast {
        print("\nComplete: packaged $packageDir/$imageFileName\n")
    }

    rename { String filename ->
        if (filename.startsWith("postgres-rhel-rpm-")) {
            return filename.replace("postgres-rhel-rpm-", "rhel").replace(".tgz", "-pgdep.tgz");
        } else {
            return filename
        }
    }

    doLast {
        reportError("----- Build of linux production image complete -----")
    }
}

task packageMODProductionImage(type: Tar) {
    group('enc.production')
    description('Create the tar file containing the linux MNC installer')

    dependsOn(packageEnc)
    dependsOn(setModScriptPermissions)

    destinationDirectory = packageDir

    archiveBaseName = imageModFileName
    archiveExtension = 'tar'

    from("$packageDir/fsp_nm.tar.gz")
    from("installmod")
    from("uninstall-mod")
    from("setenv.sh")
    from { elsArtifact  } {
        rename { String filename ->
            return "els-${elsReleaseVersion}.tgz"
        }
    }
    from { configurations.ni_snapshot.singleFile }
    from { configurations.rhel_postgres_rpms.singleFile }
    from { configurations.mod_csm_snapshot.singleFile }

    doLast {
        print("\nComplete: packaged $packageDir/$imageModFileName\n")
    }

    rename { String filename ->
        if (filename.startsWith("postgres-rhel-rpm-")) {
            return filename.replace("postgres-rhel-rpm-", "rhel").replace(".tgz", "-pgdep.tgz");
        } else {
            return filename
        }
    }

    doLast {
        reportError("----- Build of MOD linux production image complete -----")
    }
}

task publishMODToSitHost (type: ImagePublisherTask) {
    target = publicationTargetName
    destination = '/opt/ftp/enc/builds/production'
    updateSymlink = true
    segmentByDay = true
    cleanup = false
    extractZip = false

    setFiles  {
         rootProject.file("${packageDir}/${imageModFileName}.tar")
    }
    doLast {
        print("##teamcity[buildStatus status='SUCCESS' text='Success: published ${imageModFileName}.tar']\n")
    }

}