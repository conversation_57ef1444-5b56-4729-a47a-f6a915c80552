import com.adva.gradle.imagepublisher.ImagePublisherTask
import org.apache.tools.ant.taskdefs.condition.Os

/*
 *   Copyright 2023 Adtran Networks SE. All rights reserved.
 */

plugins {
    id 'com.adva.gradle.plugin.image-publisher'
}

apply from: "$rootProject.projectDir/parameters.gradle"

// Directory where windows production files should be assembled
File assembleWindowsRoot = project.file(rootProject.projectDir)

String localImagePath = "dist/win/extras"
String imageFileName = "Mosaic_Network_Controller_for_Windows_v${Version}-${BuildNumber}-64bit.exe"
String winClientFileName = "Mosaic_Network_Controller_for_Windows_v${Version}-${BuildNumber}-Client-64bit.exe"

FileTree images = rootProject.fileTree(".") {
    include "$localImagePath/$imageFileName"
    include "$localImagePath/$winClientFileName"
}

imagePublisher {
    // Use closure to identify files to be uploaded
    imageFiles = {
        logger.quiet("\nStarting production image publication...\n")

        return images
    }
    addTarget publicationTargetName, publicationTarget
    addTarget releasePublicationTargetName, releasePublicationTarget
}

configurations {
    jreWin
    // NOTE: This configuration is no longer used.  The ELS image is pulled directly from the
    // ELS artifactory repository using ant 'get' task since this repository does not follow
    // maven conventions. This logic is retained in the case that the ELS repository may one
    // day follow maven conventions.
    els
    postgres
    postgres_contrib
    timescaledb
    // Contains all windows specific libraries needed by install anywhere for install
    windowsDlls
    // Contains all windows specific executables needed by install anywhere for install
    windowsExes
    // Contains all windows specific (root) executables needed by install anywhere for install
    windowsRootExes
    // Contains all windows specific (Visual C) executables needed by install anywhere for install
    windowsVCExes
}

dependencies {
    jreWin group: 'com.adva.enc.production.distribution.windows', name: 'jre', version: '**********', ext: 'zip'
    postgres group: 'com.adva.enc.production.distribution.windows', name: 'postgres', version: '17.4', ext: 'zip'
    postgres_contrib group: 'com.adva.enc.production.distribution.windows', name: 'postgres-contrib', version: '17.4', ext: 'zip'
    timescaledb group: 'com.adva.enc.production.distribution.windows', name: 'timescaledb', version: '2.17.2', ext: 'zip'

    windowsDlls group: 'com.adva.enc.production.distribution.windows', name: 'archive2', version: '1.0', ext: 'dll'
    windowsDlls group: 'com.adva.enc.production.distribution.windows', name: 'zlib1', version: '1.0', ext: 'dll'
    windowsDlls group: 'com.adva.enc.production.distribution.windows', name: 'bzip2', version: '1.0', ext: 'dll'

    windowsExes group: 'com.adva.enc.production.distribution.windows', name: 'bsdtar', version: '1.0', ext: 'exe'
    windowsExes group: 'com.adva.enc.production.distribution.windows', name: 'editv32', version: '1.0', ext: 'exe'
    windowsExes group: 'com.adva.enc.production.distribution.windows', name: 'editv64', version: '1.0', ext: 'exe'

    windowsRootExes group: 'com.adva.enc.production.distribution.windows', name: 'prunsrv32', version: '1.3.3', ext: 'exe'
    windowsRootExes group: 'com.adva.enc.production.distribution.windows', name: 'prunsrv64', version: '1.3.3', ext: 'exe'
    windowsRootExes group: 'com.adva.enc.production.distribution.windows', name: 'regupdate', version: '1.0', ext: 'exe'
    windowsRootExes group: 'com.adva.enc.production.distribution.windows', name: 'sleep', version: '1.0', ext: 'exe'

    windowsVCExes group: 'com.adva.enc.production.distribution.windows', name: 'vcredist32', version: '1.0', ext: 'exe'
    windowsVCExes group: 'com.adva.enc.production.distribution.windows', name: 'vcredist64', version: '1.0', ext: 'exe'
}

task updateInstallAnywhereScripts() {
    File installAnywhereWinClientFile = project.file("FSP Network Manager-Client-64bit.iap_xml")
    File installAnywhereWin64File = project.file("FSP Network Manager-64bit.iap_xml")
    String regex = "(\\Wproperty name=\"productVersion\"\\W\\n.*\\n\\s*"+
                "\\Wproperty name=\"major\"\\W\\n\\s*\\Wint\\W)\\d*"+
            "(\\W/int\\W\\n\\s*\\W/property\\W\\n\\s*"+
                "\\Wproperty name=\"minor\"\\W\\n\\s*\\Wint\\W)\\d*"+
            "(\\W/int\\W\\n\\s*\\W/property\\W\\n\\s*"+
                "\\Wproperty name=\"revision\"\\W\\n\\s*\\Wint\\W)\\d*"+
            "(\\W/int\\W\\n\\s*\\W/property\\W\\n\\s*"+
                "\\Wproperty name=\"subRevision\"\\W\\n\\s*\\Wint\\W)\\d*"+
            "(\\W/int\\W\\n\\s*\\W/property\\W\\n\\s*\\W/object\\W\\n\\s*\\W/property\\W)"

    doLast {
        //Windows Client
        ant.replace(file: installAnywhereWinClientFile) {
            replacefilter(token: "_BUILD_NUMBER_", value: BuildNumberX2)
            replacefilter(token: "_VERSION_NUMBER_", value: Version)
        }

        ant.replaceregexp(file: installAnywhereWinClientFile, match: regex,
                replace: "\\1${MajorVersion}\\2${MinorVersion}\\3${PatchVersion}\\40\\5")
        //Windows Server
        ant.replace(file: installAnywhereWin64File) {
            replacefilter(token: "_BUILD_NUMBER_", value: BuildNumberX2)
            replacefilter(token: "_VERSION_NUMBER_", value: Version)
            replacefilter(token: "_BUILD_BY2_NUMBER_", value: BNUM)
        }

        ant.replaceregexp(file: installAnywhereWin64File, match: regex,
                replace: "\\1${MajorVersion}\\2${MinorVersion}\\3${PatchVersion}\\40\\5")
    }
}

task runInstallAnywhere(type: Exec) {
    dependsOn('runInstallAnywhereWinClient')
    workingDir rootProject.projectDir
    // A note on windows quoting. For the command to work, what is sent to the cmd interpreter
    // must have the entire command quoted and the arguments with spaces must also be quoted. This
    // results in nested quotes like: cmd /c ""some executable" "some arg 1" "some arg 2""
     commandLine 'cmd', '/c',
            '""C:\\Program Files\\InstallAnywhere 2021\\build.exe" ' +
            '"C:\\Projects\\fnm-inst\\nms\\dist\\win\\FSP Network Manager-64bit.iap_xml" ' +
            'windows -WV"'
}

task runInstallAnywhereWinClient(type: Exec) {
    workingDir rootProject.projectDir

    // A note on windows quoting. For the command to work, what is sent to the cmd interpreter
    // must have the entire command quoted and the arguments with spaces must also be quoted. This
    // results in nested quotes like: cmd /c ""some executable" "some arg 1" "some arg 2""
    commandLine 'cmd', '/c',
            '""C:\\Program Files\\InstallAnywhere 2021\\build.exe" ' +
                    '"C:\\Projects\\fnm-inst\\nms\\dist\\win\\FSP Network Manager-Client-64bit.iap_xml" ' +
                    'windows -WV"'
}

task assembleProductionBundles() {
    doLast {
        copy {
            // Night build production bundles
            from(zipTree(mediationLibBundle))
            from(zipTree(mediationConfigBundle))
            from(zipTree(mediationWarBundle))
            from(zipTree(clientUpdaterBundle))
            from(zipTree(frontendBundle))

            into(assembleWindowsRoot)

            duplicatesStrategy DuplicatesStrategy.EXCLUDE
        }
    }
}

task assembleJre() {
    doLast {
        copy {
            from { zipTree { configurations.jreWin.getSingleFile() } }
            into("$assembleWindowsRoot/jre64")
        }
    }
}

String elsArtifact = "$buildDir/$elsWindowsArtifact"

/*
 * Task to fetch the ELS artifact from artifactory.
 * The ELS artifactory repository does not follow maven conventions so use ant get task to fetch the image.
 */
task fetchEls() {
    doLast {
        // Ensure build directory exists
        mkdir(buildDir)
        ant.get(src: elsWindowsArtifactURL,
                dest: elsArtifact,
                username: artifactoryReadUser,
                password: artifactoryReadPassword,
                verbose: 'on')
    }
}

task assembleEls() {
    dependsOn(fetchEls)

    doLast {
        copy {
            from { zipTree elsArtifact }
            into(assembleWindowsRoot)
        }
    }
}

task assemblePostgres() {
    doLast {
        copy {
            from { zipTree { configurations.postgres.getSingleFile() } }
            into("$assembleWindowsRoot/postgres")
        }
        copy {
            from { zipTree { configurations.postgres_contrib.getSingleFile() } }
            into("$assembleWindowsRoot/postgres")
        }
        copy {
            from { zipTree { configurations.timescaledb.getSingleFile() } }
            into("$assembleWindowsRoot/postgres")
        }
    }
}

task assembleClientUpload(type: Copy) {
    dependsOn(":dist:packageClientUpload")
    // Set soft dependencies to quiet gradle errors.
    mustRunAfter("assembleProductionBundles")

    from(packageClientDir)
    into("$assembleWindowsRoot/ws/webapps/clientUpdate/${Version}-${BNUM}-WIN")
}

task assembleClientUploadLocal(type: Copy) {
    dependsOn(":dist:packageClientUpload")
    from(packageClientDir)
    into("$assembleWindowsRoot/clientupdater/localclient")
}

task assembleWindowsBinBinaries(type: Copy) {
    from {
        configurations.windowsDlls
    }
    from {
        configurations.windowsExes
    }

    into("$assembleWindowsRoot/bin")

    rename { name ->
        if (name.startsWith("archive2")) {
            return "libarchive2.dll"
        } else if (name.startsWith("bsdtar")) {
            return "bsdtar.exe"
        } else if (name.startsWith("bzip2")) {
            return "bzip2.dll"
        } else if (name.startsWith("editv32")) {
            return "EditV32.exe"
        } else if (name.startsWith("editv64")) {
            return "EditV64.exe"
        } else if (name.startsWith("zlib1")) {
            return "zlib1.dll"
        }
        return name
    }
}

task assembleWindowsRootBinaries() {
    doLast {
        copy {
            from {
                configurations.windowsRootExes
            }

            into(assembleWindowsRoot)

            rename { name ->
                if (name.startsWith("prunsrv32")) {
                    return "prunsrv32.exe"
                } else if (name.startsWith("prunsrv64")) {
                    return "prunsrv64.exe"
                } else if (name.startsWith("regupdate")) {
                    return "regupdate.exe"
                } else if (name.startsWith("sleep")) {
                    return "Sleep.exe"
                }
                return name
            }
        }
    }
}

task assembleWindowsVCBinaries(type: Copy) {
    from {
        configurations.windowsVCExes
    }

    into("$assembleWindowsRoot/vcredist")

    rename { name ->
        if (name.startsWith("vcredist32")) {
            return "vcredist_x86.exe"
        } else if (name.startsWith("vcredist64")) {
            return "vcredist_x64.exe"
        }
        return name
    }
}

task assembleJettyXml() {
    doLast {
        // Disable redeployment of web applications in windows production image
        ant.replace(file: "$fspRoot/ws/etc/jetty.xml", token: "<Property name=\"jetty.deploy.scanInterval\" " +
                "default=\"10\"/></Set>", value: "<Property name=\"jetty.deploy.scanInterval\" default=\"10\"/></Set>")
    }
}

tasks.register("createLogDir") {
    doLast {
        mkdir "$serverRoot/var/log"
    }
}

task assembleProductionImage(type: Copy) {
    dependsOn("createLogDir")
    dependsOn(assembleProductionBundles)
    dependsOn(assembleJre)
    dependsOn(assembleEls)
    dependsOn(assemblePostgres)
    dependsOn(assembleClientUpload)
    dependsOn(assembleClientUploadLocal)
    dependsOn(assembleWindowsBinBinaries)
    dependsOn(assembleWindowsRootBinaries)
    dependsOn(assembleWindowsVCBinaries)
    dependsOn(assembleJettyXml)

    [
            ":configureWindowsFilezilla",
            ":installCmsw",
            ":installDocs",
            ":installCopSSH",
            ":installElementManagerJars",
            ":installYpDb",
            ":configureWindowsBin",
    ].each {String t ->
        dependsOn(t)
    }
}

task renameWindowsProductionImage() {
    doLast {
        // Move the file from where IA places it into a location where it can be uploaded
        ant.move(file:"FSP_Network_Manager-Client-64bit_Build_Output/Web_Installers/InstData/Windows/VM/MNCClient.exe",
                tofile: "extras/$winClientFileName")
        ant.move(file:"FSP_Network_Manager-64bit_Build_Output/Web_Installers/InstData/Windows/VM/FNM.exe",
                tofile: "extras/$imageFileName")
    }
}

// Change success message on team city to include the image produced
tasks.publishImage.doLast {
    print("##teamcity[buildStatus status='SUCCESS' text='Success: published ${imageFileName}.tar']\n")
}

tasks.publishImage.dependsOn('renameWindowsProductionImage')

task publishToSitHost (type: ImagePublisherTask) {
    dependsOn(renameWindowsProductionImage) // uncomment this when sitnms upload is disabled
    target = publicationTargetName
    destination = '/opt/ftp/enc/builds/production'
    updateSymlink = true
    segmentByDay = true
    cleanup = false
    extractZip = false

    setFiles  {
        return images
    }

}
// Change success message on team city to include the image produced
tasks.publishToSitHost.doLast {
    print("##teamcity[buildStatus status='SUCCESS' text='Success: published ${imageFileName}']\n")
}

task publishToReleaseHost (type: ImagePublisherTask) {
    target = releasePublicationTargetName
    destination = "/projects/nms/FNM/versions/NM$Version/dist/$BuildNumber"
    if(IsPatch)
        destination += "-patch"
    updateSymlink = false
    segmentByDay = false
    cleanup = false
    extractZip = false

    setFiles  {
        return images
    }
}

task spotless(type: Delete) {
    // Cleanup files placed in workspace from artifactory
    delete fileTree("$assembleWindowsRoot/bin/") {
        include("*.dll")
        include("*.exe")
    }

    delete 'vcredist'
    delete 'prunsrv32.exe'
    delete 'prunsrv64.exe'
    delete 'regupdate.exe'
    delete 'Sleep.exe'
}
