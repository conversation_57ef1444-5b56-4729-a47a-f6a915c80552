cd C:\Projects\fnm-inst\nms\dist\win\extras
rem copy to Projects directory
copy "archives\*.*" c:\Projects\fnm-inst\nms\build\archives
copy "archives\mtosi.war" c:\Projects\fnm-inst\nms\ws\webapps

7z x "archives\monitor.jar" -oc:\Projects\fnm-inst\nms -y
cd C:\Projects\fnm-inst\nms
call ant -Dprodname=$%PDNAME% make.fnmclient.windows
cd C:\Projects\fnm-inst\nms\dist\win\extras

call buildnm.bat
@echo on
echo "remove old files"

del "%PDNAME%_for_Windows_v%VERSION%-%BUILD%.exe" /F /S /Q
copy "C:\Projects\fnm-inst\nms\dist\win\Network_Manager_Build_Output\Web_Installers\InstData\Windows\VM\NM.exe" "%PDNAME%_for_Windows_v"%VERSION%-%BUILD%.exe

call ant -Dfile=%PDNAME%_for_Windows_v%VERSION%-%BUILD%.exe scp2sitnms

call buildnm64bit.bat
@echo on
echo "remove old files"

del "%PDNAME%_for_Windows_v%VERSION%-%BUILD%-64bit.exe" /F /S /Q
copy "C:\Projects\fnm-inst\nms\dist\win\Network_Manager-64bit_Build_Output\Web_Installers\InstData\Windows\VM\NM.exe" "%PDNAME%_for_Windows_v"%VERSION%-%BUILD%-64bit.exe

call ant -Dfile=%PDNAME%_for_Windows_v%VERSION%-%BUILD%-64bit.exe scp2sitnms

