cd C:\Projects\fnm-inst\nms\dist\win\extras
rem copy to Projects directory
rem Needed to build client from build.xml (make.fnmclient.windows)
copy "archives\*.zip" c:\Projects\fnm-inst\nms\build\archives
rem 7z x "archives\monitor.jar" -oc:\Projects\fnm-inst\nms -y
7z x "archives\production-client-updater.zip" -oc:\Projects\fnm-inst\nms -y
7z x "archives\production-mediation-lib.zip" -oc:\Projects\fnm-inst\nms -y
7z x "archives\production-mediation-war.zip" -oc:\Projects\fnm-inst\nms -y
7z x "archives\production-mediation-config.zip" -oc:\Projects\fnm-inst\nms -y
cd C:\Projects\fnm-inst\nms
call ant -Dprodname="Mosaic_Network_Controller" make.fnmclient.windows
cd C:\Projects\fnm-inst\nms\dist\win\extras

call buildfnm64bit.bat
@echo on
echo "remove old files"

del "Mosaic_Network_Controller_for_Windows_v%VERSION%-%BUILD%-64bit.exe" /F /S /Q
copy "C:\Projects\fnm-inst\nms\dist\win\FSP_Network_Manager-64bit_Build_Output\Web_Installers\InstData\Windows\VM\FNM.exe" "Mosaic_Network_Controller_for_Windows_v"%VERSION%-%BUILD%-64bit.exe

rem Now done via gradle
rem call ant -Dfile=Mosaic_Network_Controller_for_Windows_v%VERSION%-%BUILD%-64bit.exe scp2sitnms

