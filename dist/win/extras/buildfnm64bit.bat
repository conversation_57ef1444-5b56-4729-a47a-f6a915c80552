@echo off


rem ===============================================
rem Set up tools.
rem ===============================================

set BUILDER=C:\Program Files\InstallAnywhere 2021\build.exe

rem ===============================================
rem Set the environment for the compiler
rem ===============================================

set INSTALLPROJECT=C:\Projects\fnm-inst\nms\dist\win\FSP Network Manager-64bit.iap_xml
set BUILDCONFIG=windows

rem ==================================================
rem build
rem ==================================================
"%BUILDER%" "%INSTALLPROJECT%" "%BUILDCONFIG%" -WV

