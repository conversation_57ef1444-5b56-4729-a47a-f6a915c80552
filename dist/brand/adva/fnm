#!/bin/bash
# Owner: __OWNER_NOT_YET_ASSIGNED__
#
################################################################################
. /etc/setenv.sh
JAVA="$NMS_HOME/share/jre/bin/java"

NLMS=$NMS_HOME/fsp_nm
export NLMS

if [ ! -d ${HOME}/.fsp_nm ]
then
  mkdir -p ${HOME}/.fsp_nm/log
fi

cd $NLMS

if [ -d build/archives ]
then
  CLASSPATH=`ls build/archives/*.jar lib/*.jar | grep -v 'lib/quartz.jar' | tr '\n' :`
else
  CLASSPATH=lib/frontend-linux.jar
fi
export CLASSPATH

$JAVA -Xmx1000M -Djtrace.logdir=${HOME}/.fsp_nm/log -Dproperties.dir=${HOME}/.fsp_nm com.adva.common.workbench.DefaultContextManager -application frontend -productName "FSP Network Manager"

