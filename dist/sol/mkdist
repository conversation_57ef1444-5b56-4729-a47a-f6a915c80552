#!/bin/sh

ADVADIR=./../../..
FNMDIR=${ADVADIR}/fsp_nm
FNMDIST=${FNMDIR}/dist/release
SOLDIR=${FNMDIR}/dist/sol
JREDIR=/opt
POSTGRESDIR=/opt

echo Removing old distribution...
rm -rf ${FNMDIST}/*
rm -f ${SOLDIR}/fsp_nm.tar.Z
rm -f ${SOLDIR}/$2_for_Solaris_v$1.tar

echo Copying files from ${FNMDIR}/bin...
mkdir -p ${FNMDIST}/fsp_nm/bin
cp -p ${FNMDIR}/bin/fnm           ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/fem           ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/fnm.server    ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/changeUser.sh   ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/fnm_user.sh    ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/restoreDB     ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/mdlog         ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/cleanDB       ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/MakePDF       ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/createKeystore       ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/deleteKeystoreEntry       ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/displayKeyentries       ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/importCACertificate       ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/importp12container      ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/importSignedCertificate      ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/encrypt      ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/obfuscate      ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/obfuscate_ssl_password.sh      ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/encrypt_passphrase.sh      ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/generateCSR      ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/exportCertificate      ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/healthcheck_nms.sh    ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/cleanPostgresAfterUpgrade    ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/nmsadmin.sh    ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/activemq.sh    ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/activemqjmx.sh    ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/proxyserver.sh   ${FNMDIST}/fsp_nm/bin/
cp -p ${FNMDIR}/bin/ivy_classpath.sh    ${FNMDIST}/fsp_nm/bin/

chmod 755 ${FNMDIST}/fsp_nm/bin/*

chmod 700 ${FNMDIST}/fsp_nm/bin/restoreDB
chmod 700 ${FNMDIST}/fsp_nm/bin/cleanDB
chmod 700 ${FNMDIST}/fsp_nm/bin/cleanPostgresAfterUpgrade

echo Copying files from ${FNMDIR}/lib...
mkdir -p ${FNMDIST}/fsp_nm/lib
mkdir -p ${FNMDIST}/fsp_nm/lib/report
mkdir -p ${FNMDIST}/fsp_nm/lib/em

cp -p ${FNMDIR}/lib/report/ivy_dist_classpaths.properties		   	   	   ${FNMDIST}/fsp_nm/lib/report/ivy_dist_classpaths.properties

cp -p ${FNMDIR}/lib/ypdb-commons.jar           ${FNMDIST}/fsp_nm/lib/
#Client libraries
./copyivyconf.sh ivy.classpath.client-runtime ${FNMDIST}/fsp_nm/lib/

#Server libraries
./copyivyconf.sh ivy.classpath.server-runtime ${FNMDIST}/fsp_nm/lib/

# cp -p ${FNMDIR}/lib/jfxrt-linux.jar  ``          ${FNMDIST}/fsp_nm/lib/jfxrt.jar

echo Copying files from ${FNMDIR}/lib/em...
cp -p ${FNMDIR}/lib/em/jh.jar		   	   	   ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/jsnmp.jar		   	   ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/advaem.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/log4j.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/advalic.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/providerproxy.jar       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/emcore.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/SNMP4J.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/emhelp.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/f7.jar		           ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/f7emhelp.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/jgoodies-forms.jar	   ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/jgoodies-common.jar     ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/fsp150.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/fsp1500.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/fsp2000.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/swingx.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/fsp3000.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/ftp.jar		           ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/itext.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/jcommon.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/jfreechart.jar	       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/bcprov.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/commons-logging.jar	   ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/gnu-crypto.jar		   ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/jgraphx.jar		       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/jradius-core.jar	       ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/jradius-dictionary.jar  ${FNMDIST}/fsp_nm/lib/em/
cp -p ${FNMDIR}/lib/em/guava.jar  			   ${FNMDIST}/fsp_nm/lib/em/


echo Copying files from ${FNMDIR}/build/archives...
cp -p ${FNMDIR}/build/archives/frontend.jar    ${FNMDIST}/fsp_nm/lib/
cp -p ${FNMDIR}/build/archives/mediation.jar   ${FNMDIST}/fsp_nm/lib/
cp -p ${FNMDIR}/build/archives/nmscommon.jar   ${FNMDIST}/fsp_nm/lib/
cp -p ${FNMDIR}/build/archives/mdtest.jar	   ${FNMDIST}/fsp_nm/lib/
cp -p ${FNMDIR}/build/archives/adva_tools.jar  ${FNMDIST}/fsp_nm/lib/
cp -p ${FNMDIR}/build/archives/yfiles-for-javafx.jar  ${FNMDIST}/fsp_nm/lib/
cp -p ${FNMDIR}/build/archives/drivercommon.jar	   ${FNMDIST}/fsp_nm/lib/
cp -p ${FNMDIR}/build/archives/gnss-common.jar	   ${FNMDIST}/fsp_nm/lib/
cp -p ${FNMDIR}/build/archives/netconf.jar	   ${FNMDIST}/fsp_nm/lib/
cp -p ${FNMDIR}/build/archives/trapbridge.jar ${FNMDIST}/fsp_nm/lib/
cp -p ${FNMDIR}/build/archives/snmp.jar ${FNMDIST}/fsp_nm/lib/
cp -p ${FNMDIR}/build/archives/ni-config.jar ${FNMDIST}/fsp_nm/lib/
cp -p ${FNMDIR}/build/archives/persistence-common.jar ${FNMDIST}/fsp_nm/lib/
cp -p ${FNMDIR}/build/archives/adva-concurrent.jar ${FNMDIST}/fsp_nm/lib/
cp -p ${FNMDIR}/build/archives/sdn_model.jar ${FNMDIST}/fsp_nm/lib/

chmod -R 644 ${FNMDIST}/fsp_nm/lib/*.jar
chmod -R 644 ${FNMDIST}/fsp_nm/lib/em/*.jar

echo Copying files from ${FNMDIR}/activemq/...
mkdir -p ${FNMDIST}/fsp_nm/activemq
cp -pr ${FNMDIR}/activemq/*    ${FNMDIST}/fsp_nm/activemq
chmod -R 644 ${FNMDIST}/fsp_nm/activemq/lib/*.jar

echo Copying files from ${FNMDIR}/mibs/...
mkdir -p ${FNMDIST}/fsp_nm/mibs
cp -pr ${FNMDIR}/mibs/*    ${FNMDIST}/fsp_nm/mibs

echo Copying files from ${FNMDIR}/monitoringConfig/...
mkdir -p ${FNMDIST}/fsp_nm/monitoringConfig
cp -pr ${FNMDIR}/monitoringConfig/*    ${FNMDIST}/fsp_nm/monitoringConfig

echo Copying files from ${FNMDIR}/ws...
mkdir -p ${FNMDIST}/fsp_nm/ws
mkdir -p ${FNMDIST}/fsp_nm/ws/etc
cp -p  ${FNMDIR}/ws/etc/jetty.xml ${FNMDIST}/fsp_nm/ws/etc
cp -p  ${FNMDIR}/ws/etc/jetty_proxy.xml ${FNMDIST}/fsp_nm/ws/etc
cp -p  ${FNMDIR}/ws/etc/webdefault.xml ${FNMDIST}/fsp_nm/ws/etc
cp -p  ${FNMDIR}/ws/etc/cxf-crypto.properties ${FNMDIST}/fsp_nm/ws/etc
cp -p  ${FNMDIR}/ws/etc/cxfrealm.xml ${FNMDIST}/fsp_nm/ws/etc
cp -p  ${FNMDIR}/ws/etc/realm.properties ${FNMDIST}/fsp_nm/ws/etc

#mkdir -p ${FNMDIST}/fsp_nm/ws/resources/certs
#cp -pr ${FNMDIR}/ws/resources/certs/ssl-keystore ${FNMDIST}/fsp_nm/ws/resources/certs
cp -pr ${FNMDIR}/ws/resources/tmf854 ${FNMDIST}/fsp_nm/ws/resources
cp -pr ${FNMDIR}/ws/resources/adva ${FNMDIST}/fsp_nm/ws/resources
echo Deleting CVS directories frow ws directory
rm -rf  ${FNMDIST}/fsp_nm/ws/resources/wsdl/CVS
rm -rf  ${FNMDIST}/fsp_nm/ws/resources/xsd/CVS
rm -rf  ${FNMDIST}/fsp_nm/ws/webapps/CVS

mkdir -p ${FNMDIST}/fsp_nm/ws/webapps
mkdir -p ${FNMDIST}/fsp_nm/ws/webapps/clientUpdate
mkdir -p ${FNMDIST}/fsp_nm/ws/webapps/customimages
mkdir -p ${FNMDIST}/fsp_nm/ws/webapps/stylesheet
mkdir -p ${FNMDIST}/fsp_nm/ws/webapps/proxy
mkdir -p ${FNMDIST}/fsp_nm/ws/webapps/map
mkdir -p ${FNMDIST}/fsp_nm/ws/webapps/gnss-charts
mkdir -p ${FNMDIST}/fsp_nm/ws/webapps_proxy
mkdir -p ${FNMDIST}/fsp_nm/ws/deploy
mkdir -p ${FNMDIST}/fsp_nm/ws/reportdb
mkdir -p ${FNMDIST}/fsp_nm/ws/healthreport
cp -pr ${FNMDIR}/ws/webapps/clientUpdate ${FNMDIST}/fsp_nm/ws/webapps/
cp -pr ${FNMDIR}/ws/webapps/customimages ${FNMDIST}/fsp_nm/ws/webapps/
cp -pr ${FNMDIR}/ws/webapps/stylesheet ${FNMDIST}/fsp_nm/ws/webapps/
cp -pr ${FNMDIR}/ws/webapps/map ${FNMDIST}/fsp_nm/ws/webapps/
cp -pr ${FNMDIR}/ws/webapps/gnss-charts ${FNMDIST}/fsp_nm/ws/webapps/
cp -p  ${FNMDIR}/ws/webapps/proxy/nmsproxy.pac ${FNMDIST}/fsp_nm/ws/webapps/proxy
cp -p ${FNMDIR}/build/archives/mtosi.war	${FNMDIST}/fsp_nm/ws/deploy/
cp -p ${FNMDIR}/build/archives/advabase.war  ${FNMDIST}/fsp_nm/ws/deploy/
cp -p ${FNMDIR}/build/archives/advaproxy.war  ${FNMDIST}/fsp_nm/ws/deploy/
cp -p ${FNMDIR}/build/archives/manual.war  ${FNMDIST}/fsp_nm/ws/deploy/
cp -p ${FNMDIR}/build/archives/jolokia.war  ${FNMDIST}/fsp_nm/ws/deploy/

echo Copying files from ${FNMDIR}/mappings
mkdir -p ${FNMDIST}/fsp_nm/mappings
cp -pr	${FNMDIR}/mappings			${FNMDIST}/fsp_nm/

echo Copying files from ${FNMDIR}/ssocerts
mkdir -p ${FNMDIST}/fsp_nm/ssocerts
cp -pr	${FNMDIR}/ssocerts			${FNMDIST}/fsp_nm/

echo Copying files from ${FNMDIR}/templates
mkdir -p ${FNMDIST}/fsp_nm/templates
cp -pr	${FNMDIR}/templates			${FNMDIST}/fsp_nm/


echo Copying postgres files from ${POSTGRESDIR} ...
cp -pr ${POSTGRESDIR}/postgres/10.4-pgdg ${FNMDIST}/fsp_nm/postgres

echo Copying files from ${FNMDIR}/postgres...
cp -pr ${FNMDIR}/postgres/scripts ${FNMDIST}/fsp_nm/postgres
cp -pr ${FNMDIR}/postgres/support-files ${FNMDIST}/fsp_nm/postgres
rm -rf ${FNMDIST}/fsp_nm/postgres/data/*
mkdir -p ${FNMDIST}/fsp_nm/postgres/data
#cp -p ${FNMDIR}/postgres/support-files/pg_hba.conf ${FNMDIST}/fsp_nm/postgres/data
#cp -p ${FNMDIR}/postgres/support-files/postgresql.conf ${FNMDIST}/fsp_nm/postgres/data
chmod 755 ${FNMDIST}/fsp_nm/postgres/support-files/postgres.server
chmod 755 ${FNMDIST}/fsp_nm/postgres/bin/64/*

echo Copying files from ${FNMDIR}/Examples/...
mkdir -p ${FNMDIST}/fsp_nm/Examples
mkdir -p ${FNMDIST}/fsp_nm/Examples/ECM-Templates
cp -pr ${FNMDIR}/Examples/ECM-Templates/*.xml ${FNMDIST}/fsp_nm/Examples/ECM-Templates

echo Copying files from ${FNMDIR}/scripts...
mkdir -p ${FNMDIST}/fsp_nm/scripts
cp -pr ${FNMDIR}/scripts/*.sql ${FNMDIST}/fsp_nm/scripts
cp -pr ${FNMDIR}/scripts/*.sh ${FNMDIST}/fsp_nm/scripts
chmod 644 ${FNMDIST}/fsp_nm/scripts/*
chmod 600 ${FNMDIST}/fsp_nm/scripts/createUser.sql
chmod 755 ${FNMDIST}/fsp_nm/scripts/*.sh
mkdir -p ${FNMDIST}/fsp_nm/scripts/redundancy
cp -pr ${FNMDIR}/scripts/redundancy/*.sh ${FNMDIST}/fsp_nm/scripts/redundancy
chmod 755 ${FNMDIST}/fsp_nm/scripts/redundancy/*.sh
chmod 700 ${FNMDIST}/fsp_nm/scripts/redundancy/rr.sh

echo Copying files from ${FNMDIR}/CustomProducts/...
mkdir -p ${FNMDIST}/fsp_nm/CustomProducts
cp -p ${FNMDIR}/CustomProducts/unmanaged.xml    ${FNMDIST}/fsp_nm/CustomProducts
cp -p ${FNMDIR}/CustomProducts/osa-proxy.xml    ${FNMDIST}/fsp_nm/CustomProducts
cp -p ${FNMDIR}/CustomProducts/osa-proxy.alarms ${FNMDIST}/fsp_nm/CustomProducts

echo Copying files from ${FNMDIR}/dat/...
mkdir -p ${FNMDIST}/fsp_nm/dat
cp -p ${FNMDIR}/dat/*.xml    ${FNMDIST}/fsp_nm/dat

echo Copying yp db files from ${FNMDIR}/db/...
mkdir -p ${FNMDIST}/fsp_nm/db
cp -pr ${FNMDIR}/db/yp    ${FNMDIST}/fsp_nm/db

echo Copying files from ${FNMDIR}/test/...
mkdir -p ${FNMDIST}/fsp_nm/test
cp -p ${FNMDIR}/test/lib/junit.jar ${FNMDIST}/fsp_nm/lib
cp -p ${FNMDIR}/test/log4j2.xml ${FNMDIST}/fsp_nm/test
chmod  666 ${FNMDIST}/fsp_nm/test/log4j2.xml

cp ${FNMDIR}/modules/nmscommon/src/main/resources/com/adva/nlms/common/version.properties ${FNMDIST}/fsp_nm/

echo Copying files from ${FNMDIR}/...
cp ${FNMDIR}/*.pdf \
   ${FNMDIR}/*.txt \
   ${FNMDIR}/jtrace.cfg \
   ${FNMDIR}/log4j2.xml \
   ${FNMDIR}/log4j_client.properties \
   ${FNMDIR}/log4j2_client.xml \
   ${FNMDIR}/log4j_proxy.xml \
   ${FNMDIR}/logging.properties \
   ${FNMDIR}/fnm.properties \
   ${FNMDIR}/fnm.snmp.properties \
   ${FNMDIR}/fnmclient.properties \
   ${FNMDIR}/fnmclientinstall.properties \
   ${FNMDIR}/fnmtest.properties \
   ${FNMDIR}/mtosi.properties \
   ${FNMDIR}/migration.properties \
   ${FNMDIR}/pmEthernetTemplate.xml \
   ${FNMDIR}/limits.json \
     ${FNMDIST}/fsp_nm/

chmod 644 ${FNMDIST}/fsp_nm/*.pdf \
          ${FNMDIST}/fsp_nm/*.txt \
          ${FNMDIST}/fsp_nm/jtrace.cfg \
          ${FNMDIST}/fsp_nm/fnm.properties \
          ${FNMDIST}/fsp_nm/fnmclientinstall.properties \
          ${FNMDIST}/fsp_nm/fnm.snmp.properties \
          ${FNMDIST}/fsp_nm/fnmclient.properties \
          ${FNMDIST}/fsp_nm/log4j_client.properties \
          ${FNMDIST}/fsp_nm/log4j2_client.xml \
          ${FNMDIST}/fsp_nm/logging.properties \
          ${FNMDIST}/fsp_nm/migration.properties \
          ${FNMDIST}/fsp_nm/fnmtest.properties \
          ${FNMDIST}/fsp_nm/version.properties

chmod  666 ${FNMDIST}/fsp_nm/log4j2.xml
chmod  666 ${FNMDIST}/fsp_nm/log4j_proxy.xml


mkdir -p ${FNMDIST}/fsp_nm/var/log
mkdir -p ${FNMDIST}/fsp_nm/log
chmod 777 ${FNMDIST}/fsp_nm/log

cp -pr ${FNMDIR}/var/web ${FNMDIST}/fsp_nm/var

echo Copying jre files from ${JREDIR} ....
mkdir -p ${FNMDIST}/share
chmod 751 ${FNMDIST}/share
cp -pr ${JREDIR}/jre1.8.0_162 ${FNMDIST}/share/jre

#rm -f ${FNMDIST}/share/jre/lib/ext/jfxrt.jar
# cp -p ${FNMDIR}/lib/jfxrt-linux.jar         ${FNMDIST}/share/jre/lib/ext/jfxrt.jar

chmod -R o-r ${FNMDIST}/share

cp ${SOLDIR}/uninstall-fsp_nm ${FNMDIST}/
chmod 700 ${FNMDIST}/uninstall-fsp_nm

if [ "$2" != "FSP_Network_Manager" ]
then
    echo Removing pdf files for branding
    rm -f ${FNMDIST}/fsp_nm/*.pdf
fi

cd ${FNMDIST}/fsp_nm
ln -s ./bin/fnm .
ln -s ./bin/fem .
ln -s ./bin/MakePDF .
cd ..

echo Changing user/group to root.root
chown -R root.root *

echo Archiving...
/bin/tar chf ${SOLDIR}/fsp_nm.tar *

echo Compressing...
rm -f ${SOLDIR}/fsp_nm.tar.Z
/bin/compress ${SOLDIR}/fsp_nm.tar

cd ${SOLDIR}
chmod +x install
chmod +x setenv.sh
/bin/tar cvf $2_for_Solaris_v$1.tar install setenv.sh fsp_nm.tar.Z

echo Done.
