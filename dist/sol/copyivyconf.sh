#!/bin/sh

# usage 1 argument ivy config name, 2 output directory i.e.:
# ./copyivyconf.sh ivy.classpath.common-compile output/directory/

CONF=$1
TO_DIR=$2
FNMDIR=./../..
LIB_DIR=${FNMDIR}/lib
#FNMDIR=${ADVADIR}/fsp_nm
IVY_CLASSPATH_FILE=${LIB_DIR}/report/ivy_dist_classpaths.properties

# finds configuration we want to copy,
# creates list of pure jar names separated with next_line sign.
# which are later copied to given output directory.
cat ${IVY_CLASSPATH_FILE} \
| grep ${CONF} \
| sed "s/${CONF}=//g" \
| tr ';' '\n' \
| while read varname; do cp -p ${LIB_DIR}/$varname ${TO_DIR}/ ; done

