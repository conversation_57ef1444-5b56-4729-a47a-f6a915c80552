#!/bin/sh
################################################################################
#
# Network Manager deinstallation script
################################################################################

. /etc/setenv.sh
MGR_DIR="fsp_nm"

CWD=`pwd`

if [ -x "${MGR_DIR}" ]
then
  echo "This script will uninstall ${NMS_NAME}"
  echo "from your system."
  echo ""
  echo "Do you really want to remove the ${NMS_NAME} installation from"
  echo "${CWD} [n]? \c"
  read reply
  if [ "$reply" != "y" -a "$reply" != "Y" -a "$reply" != "yes" -a "$reply" != "YES" ]
  then
    echo "Uninstall aborted."
    exit 1
  fi
else
  echo "Please execute this script from the ${NMS_NAME}"
  echo "installation directory."
  exit 1
fi

################################################################################
# Shutdown servers

${MGR_DIR}/bin/fnm.server stop


${MGR_DIR}/postgres/support-files/postgres.server stop
/usr/sbin/userdel postgres
/usr/sbin/groupdel postgres

################################################################################
# Start deletion

echo "Uninstalling..."

# Remove links
rm -f /usr/bin/fnm
rm -f /etc/rc?.d/???fnm.server

rm -f /etc/rc?.d/???postgres

# Remove directories and files"
rm -rf ${MGR_DIR}
rm -rf share
rm -f /etc/setenv.sh
rm -f uninstall-fsp_nm

echo "Done."
echo ""

echo "Uninstall completed."
echo ""

