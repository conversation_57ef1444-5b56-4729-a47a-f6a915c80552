#!/bin/sh
. ./setenv.sh
MGR_STR="${NMS_NAME} 11.1.1"
AF=fsp_nm.tar
AD=`pwd`

setupPostGres() {

  echo "Setting a password for the postgres root user."
  oldmodes=`stty -g`
  password=1
  repass=2
  while [ $password != $repass ]
  do
        echo "Please enter password: "
        stty -echo
        read password
        stty $oldmodes
        echo "Please retype password: "
        stty -echo
        read repass
        stty $oldmodes
        if [ $password != $repass ]
        then
        echo "Passwords doesn't match. Please try again."
        fi
  done

  if [ "$PGUPGRADE" = "YES" ]
  then
    cp -p ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf.bak
    sed 's/md5/trust/g' ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf.bak > ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf
    chown -R postgres ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf
    chgrp -R postgres ${NMS_HOME}/fsp_nm/postgres.old/data/pg_hba.conf
    cd ${NMS_HOME}/fsp_nm/postgres
    chown -R postgres .
    chgrp -R postgres .
    su postgres -c "bin/64/initdb -U root -E UTF8 --locale=C -D data"
    su postgres -c "bin/64/pg_upgrade -d ../postgres.old/data/ -D data/ -b ../postgres.old/bin/64  -B bin/64 -U root"
    su postgres -c "bin/64/pg_ctl -w -D data -l logfile start"
    su postgres -c "bin/64/psql --quiet fnm -U root -c \"ALTER USER adva WITH SUPERUSER;\""
  else
    # Register PostGres server for start at boot time
    ln -s ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server /etc/rc0.d/K07postgres
    ln -s ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server /etc/rc3.d/S96postgres

    echo "Initializing and starting ${MGR_STR} database..."
    /usr/sbin/groupadd postgres
    /usr/sbin/useradd -g postgres postgres

    # Set proper dir owner
    cd ${NMS_HOME}/fsp_nm/var/db.backup
    chown -R postgres .
    chgrp -R postgres .

    cd ${NMS_HOME}/fsp_nm/postgres
    chown -R postgres .
    chgrp -R postgres .

    #set shm max memory value
    /usr/sbin/projadd -c "PostgreSQL DB User" -K "project.max-shm-memory=(privileged,4GB,deny)" -U postgres -G postgres user.postgres

    echo "Creating FSP Network Manager database"
    su postgres -c "bin/64/initdb -U root -E UTF8 --locale=C -D data"
    su postgres -c "bin/64/pg_ctl -w -D data -l logfile start"
    su postgres -c "bin/64/createdb -U root fnm"

    chown postgres:postgres ${NMS_HOME}/fsp_nm/scripts/createUser.sql
    chown postgres:postgres ${NMS_HOME}/fsp_nm/scripts/createDB.sql
    chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/scripts/postgres_copy_from.sql
    chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/scripts/postgres_csv_restore.sql

    su postgres -c "bin/64/psql --quiet -U root -f ${NMS_HOME}/fsp_nm/scripts/createUser.sql fnm"
    su postgres -c "bin/64/psql --quiet -U root -f ${NMS_HOME}/fsp_nm/scripts/createDB.sql fnm"
    su postgres -c "bin/64/psql --quiet -U root -f ${NMS_HOME}/fsp_nm/postgres/scripts/postgres_copy_from.sql fnm"
    su postgres -c "bin/64/psql --quiet -U root -f ${NMS_HOME}/fsp_nm/postgres/scripts/postgres_csv_restore.sql fnm"
 fi
    su postgres -c "bin/64/psql --quiet fnm -U root -c \"alter user root with password '$password';\""
    cp -p ${NMS_HOME}/fsp_nm/postgres/support-files/pg_hba.conf-sol ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf
    cp -p ${NMS_HOME}/fsp_nm/postgres/support-files/postgresql.conf-sol ${NMS_HOME}/fsp_nm/postgres/data/postgresql.conf
    chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf
    chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/data/postgresql.conf
    su postgres -c "bin/64/pg_ctl -w -D data -l logfile -m fast restart"
    ln -s ${NMS_HOME}/fsp_nm/postgres/bin/64/psql ${NMS_HOME}/fsp_nm/postgres/bin/psql
    ln -s ${NMS_HOME}/fsp_nm/postgres/bin/64/pg_dump ${NMS_HOME}/fsp_nm/postgres/bin/pg_dump
    ln -s ${NMS_HOME}/fsp_nm/postgres/bin/64/pg_basebackup ${NMS_HOME}/fsp_nm/postgres/bin/pg_basebackup
    echo "done."
}
  

if [ -z "`id | grep uid=0`" ]
then
  echo "You must be root to execute this script."
  exit 1
fi

if [ ! -r ${AF}.Z ]
then
  echo "Please execute this script from the directory where the"
  echo "fsp_nm.tar.Z archive resides."
  exit 1
fi

# Check for installed version
if [ -f "$NMS_HOME/fsp_nm/lib/mediation.jar" ]
then
  echo "This script will upgrade your workstation to $MGR_STR and upgrade your database."
  echo "During the upgrade process the FSP Network Manager Server will be shut down."
  echo ""
  echo "CAUTION! Please ensure that the FSPNM database is backed up before proceeding with the upgrade."
  echo "Please make sure that libncurses.so.5 and libreadline.so.6 libraries are present in /lib/64 directory. They are required by PostgreSQL database. Otherwise, copy from the CD to /lib/64 directory."
  echo "Direct upgrades are only supported from FSPNM version 8.2.1 and above. Please validate your database using the scripts (on the CD) and instructions provided in the release notes."
  echo "In case the database validation using the scripts fails, please contact Adva tech support for further assistance."
  echo ""
  UPGRADE=YES
else
  echo "This script will install the $MGR_STR for Solaris on your system."
  echo "Please make sure that libncurses.so.5 and libreadline.so.6 libraries are present in /lib/64 directory. They are required by PostgreSQL database. Otherwise, copy from the CD to /lib/64 directory."
fi

echo ""
echo "Continue? [y/n] \c"

read reply

if [ "$reply" != "y" -a "$reply" != "Y" -a "$reply" != "yes" -a "$reply" != "YES" ]
then
  exit 0
fi
cp -p setenv.sh /etc/setenv.sh

if [ "$UPGRADE" = "YES" ]
then
  # check user
  . $NMS_HOME/fsp_nm/bin/fnm_user.sh

  FNM_USER=$fnmuser
  export FNM_USER
  cd $NMS_HOME
  FNM_GROUP=`ls -l | awk '$9 == "fsp_nm"' | awk '{print $4}'`
  export FNM_GROUP

  if [ "$fnmuser" != "root" ]
  then
   echo "FSP NM User must be root to upgrade. Changing $FNM_USER to root."
   $NMS_HOME/fsp_nm/bin/changeUser.sh root root
  fi
fi


if [ -d "${NMS_HOME}/fsp_nm/postgres/data/base" -a ! -f "${NMS_HOME}/fsp_nm/postgres/support-files/postgres10.4.1.txt"  ]
then
 PGUPGRADE=YES
fi

echo Creating ${NMS_HOME}/fsp_nm/var/db.backup directory.
if [ -d "${NMS_HOME}/fsp_nm/var/db.backup" ]
then
	echo ""
else
	mkdir -p  ${NMS_HOME}/fsp_nm/var/db.backup
	chmod 755 ${NMS_HOME}
fi

if [ -f "${NMS_HOME}/fsp_nm/fnm.properties" ]
then
	ACT_DATE=`date +%Y-%m-%d-%H.%M`
	echo Copy fnm.properties file to ${NMS_HOME}/fsp_nm/backup/fnm.properties."$ACT_DATE"

	if [ -d "${NMS_HOME}/fsp_nm/backup" ]
	then
		echo ""
	else
		mkdir -p  ${NMS_HOME}/fsp_nm/backup

	fi

	cp -p  ${NMS_HOME}/fsp_nm/fnm.properties ${NMS_HOME}/fsp_nm/backup/fnm.properties."$ACT_DATE"
fi

if [ -f "${NMS_HOME}/fsp_nm/ws/etc/jetty.xml" ]
then
	ACT_DATE=`date +%Y-%m-%d-%H.%M`
	echo Copy jetty.xml file to ${NMS_HOME}/fsp_nm/backup/jetty.xml."$ACT_DATE"
	cp -p  ${NMS_HOME}/fsp_nm/ws/etc/jetty.xml ${NMS_HOME}/fsp_nm/backup/jetty.xml."$ACT_DATE"
fi


# Shut down running FNM Server and clean old stuff
if [ "$UPGRADE" = "YES" ]
then
  ${NMS_HOME}/fsp_nm/bin/fnm.server stop

  if [ -f "${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server" ]
  then
 		 echo "Shutting down PostGres"
 		 ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server stop
  fi

  rm -r ${NMS_HOME}/fsp_nm/lib/*
  rm -rf ${NMS_HOME}/fsp_nm/lib.old/*
  rm -r ${NMS_HOME}/share/jre/*
  rm -rf ${NMS_HOME}/share/jre.old/*
  rm -f ${NMS_HOME}/fsp_nm/bin/*
  rm -f ${NMS_HOME}/fsp_nm/*.pdf
  #remove following generated files as the old format is not supported 
  rm -f ${NMS_HOME}/fsp_nm/FTPRepositoryUpdates.xml
  rm -f ${NMS_HOME}/fsp_nm/SCPRepositoryUpdates.xml
  #removing to support OSA5401 as native device on upgrade
  rm -f ${NMS_HOME}/fsp_nm/CustomProducts/osa5401.xml

  if [ "$PGUPGRADE" = "YES" ]
  then
     mv  ${NMS_HOME}/fsp_nm/postgres ${NMS_HOME}/fsp_nm/postgres.old
  fi
fi

################################################################################
# Determine installation settings

# Check for Solaris
if [ "`uname -ps`" != "SunOS sparc" ]
then
  echo "This software runs on Sparc Solaris only but this system"
  echo "seems to be of different type (`uname -ps`). Aborting."
  exit 1
fi

# Determine Manager installation directory
#echo "Enter the FSP Network Manager installation directory"
#echo "[${NMS_HOME}] \c"
#read reply
#if [ -n "$reply" ]
#then
#  NMS_HOME=$reply
#fi
#echo ""

if [ -x "${NMS_HOME}" ]
then
  echo "Using existing directory ${NMS_HOME}"
  rm -rf ${NMS_HOME}/fsp_nm/lib
 rm -rf ${NMS_HOME}/fsp_nm/log
else
  echo "Creating installation directory ${NMS_HOME} ..."
  mkdir -p ${NMS_HOME}
  if [ -d ${NMS_HOME} ]
  then
    echo Done.
  else
    echo "Could not create ${NMS_HOME}. Installation failed."
    exit 1
  fi
fi

################################################################################
# Start installation

# Unpack software to directory ${NMS_HOME}

echo "Uncompressing $MGR_STR archive..."


if `uncompress -c $AD/$AF.Z > $AD/$AF`
then
  echo "done."
  echo ""
else
  echo "Could not uncompress the $MGR_STR archive - installation incomplete."
  exit 1
fi

echo "Unpacking $MGR_STR archive to ${NMS_HOME}..."
cd ${NMS_HOME}

if `tar xf $AD/$AF`
then
  echo "done."
  echo ""
else
  echo "Could not untar the $MGR_STR archive - installation incomplete."
  exit 1
fi

cd fsp_nm

# Create endorsed folder
#mkdir lib/endorsed
#ln lib/OB.jar lib/endorsed

# Update Installation Path
for FILE in bin/fnm.server \
            bin/fnm \
            bin/fem \
            bin/MakePDF \
            bin/mdlog \
            bin/cleanDB \
            bin/restoreDB \
            postgres/support-files/postgres.server
do
  mv ${FILE} ${FILE}.org
  sed s:/opt/adva:${NMS_HOME}:g ${FILE}.org > ${FILE}
  chmod 755 ${FILE}
  rm -f ${FILE}.org
done
# should be read and executable by root or owner only:
chmod 700 bin/restoreDB
chmod 700 bin/cleanDB
chmod 700 bin/fnm.server

cd ${NMS_HOME}
OLD_TMP=$TMP
OLD_TMPDIR=$TMPDIR
TMP="/tmp"
TMPDIR="/tmp"
export TMP TMPDIR

if [ -z "$UPGRADE" ]
then
  
  # Register server for start at boot time
  ln -s ${NMS_HOME}/fsp_nm/bin/fnm.server /etc/rc0.d/K05fnm.server
  ln -s ${NMS_HOME}/fsp_nm/bin/fnm.server /etc/rc3.d/S98fnm.server

  # Set link in standard path
  ln -s ${NMS_HOME}/fsp_nm/bin/fnm /usr/bin
  setupPostGres
else
  if [ ! -d "${NMS_HOME}/fsp_nm/postgres/data/base" ]
  then
    setupPostGres
  else
    # Set proper dir owner
    cd ${NMS_HOME}/fsp_nm/var/db.backup
    chown -R postgres .
    chgrp -R postgres .
    cd ${NMS_HOME}/fsp_nm/postgres
    chown -R postgres .
    chgrp -R postgres .
    chmod 700 data
    cp -p ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf.bak
    sed 's/md5/trust/g' ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf.bak > ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf
    chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf
    su postgres -c "bin/64/pg_ctl -w -D data -l logfile start"
    su postgres -c "bin/64/psql --quiet fnm -U root -c \"ALTER USER adva WITH SUPERUSER;\""
    su postgres -c "bin/64/pg_ctl -w -D data -m fast stop"
    cp -p ${NMS_HOME}/fsp_nm/postgres/support-files/pg_hba.conf-sol ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf
    cp -p ${NMS_HOME}/fsp_nm/postgres/support-files/postgresql.conf-sol ${NMS_HOME}/fsp_nm/postgres/data/postgresql.conf
    chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/data/pg_hba.conf
    chown postgres:postgres ${NMS_HOME}/fsp_nm/postgres/data/postgresql.conf
    if [ -f "${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server" ]
 	then
 		 echo "Starting PostGres"
 		 ${NMS_HOME}/fsp_nm/postgres/support-files/postgres.server start
  	fi
  fi
fi

# Certificate generation
mkdir -p ${NMS_HOME}/fsp_nm/certs
if [ ! -f ${NMS_HOME}/fsp_nm/certs/fnmserver.ks ] 
then
	${NMS_HOME}/share/jre/bin/keytool -genkeypair -alias nms-server-key -storetype JKS -dname "CN=adtran.com,OU=Mosaic Network Controller,O=Adtran Networks SE,L=,ST=,C=US" -keyalg EC -validity 3650 --storepass NeverChange --keypass NeverChange -keystore ${NMS_HOME}/fsp_nm/certs/fnmserver.ks -groupname secp384r1 -sigalg SHA256withECDSA

fi

echo "Do you want to start the NM server application now?"
echo "Type 'y' to start the server, or 'n' if you need to modify properties files first.  [y/n]?"
read reply
if [ "$reply" = "y" -o "$reply" = "Y" -o "$reply" = "yes" -o "$reply" = "YES" ]
then
  # Start FSP Network Manager Server
  ${NMS_HOME}/fsp_nm/bin/fnm.server start
fi

TMP="$OLD_TMP"
TMPDIR="$OLD_TMPDIR"
export TMP TMPDIR

# Clean up
echo "Cleaning up..."
rm -f $AD/$AF
echo ""


if [ "$UPGRADE" = "YES" ]
then
 if [ "$FNM_USER" != "root" ]
 then
  echo "Changing FNM user back to $FNM_USER"
  $NMS_HOME/fsp_nm/bin/changeUser.sh $FNM_USER $FNM_GROUP
 fi
fi

echo ""
echo "Installation complete."
echo ""
