#!/bin/sh
. ./export_env.sh
cd ../../..
./gradlew-solaris installExternalArtifacts
#. /opt/adva/fsp_nm/dist/sol/extras/build_properties.sh
cp dist/sol/extras/archives/frontend.jar build/archives
cp dist/sol/extras/archives/yfiles-for-javafx.jar build/archives
cp dist/sol/extras/archives/mediation.jar build/archives
cp dist/sol/extras/archives/mdtest.jar build/archives
cp dist/sol/extras/archives/mtosi.war build/archives
cp dist/sol/extras/archives/manual.war build/archives
cp dist/sol/extras/archives/jolokia.war build/archives
cp dist/sol/extras/archives/advabase.war build/archives
cp dist/sol/extras/archives/advaproxy.war build/archives
cp dist/sol/extras/archives/nmscommon.jar build/archives
cp dist/sol/extras/archives/adva_tools.jar build/archives
cp dist/sol/extras/archives/drivercommon.jar build/archives
cp dist/sol/extras/archives/gnss-common.jar build/archives
cp dist/sol/extras/archives/netconf.jar build/archives
cp dist/sol/extras/archives/snmp.jar build/archives
cp dist/sol/extras/archives/ni-config.jar build/archives
cp dist/sol/extras/archives/persistence-common.jar build/archives
cp dist/sol/extras/archives/adva-concurrent.jar build/archives
cp dist/sol/extras/archives/sdn_model.jar build/archives
unzip -o dist/sol/extras/archives/monitor.jar
ant -Dprodname=${PDNAME} make.fnmclient.windows
cd dist/sol
chmod 700 mkdist
./mkdist $VERSION-$BUILD $PDNAME
cd extras
ant -Dfile=${PDNAME}_for_Solaris_v$VERSION-$BUILD.tar scp2sitnms
