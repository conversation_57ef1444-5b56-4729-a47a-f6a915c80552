::
:: Copyright 2023 Adtran Networks SE. All rights reserved.
::
:: Owner: ssingam
::

@echo off

SET COUNT=0
SET WAITING_TIME=300
set CURRENTDIR=%~dp0
echo %CURRENTDIR%
set STARTPATH="%CURRENTDIR:~0,-1%"
set NMS_LIB=lib
set ACTIVEMQ_HOME=activemq
set ACTIVEMQ_LIB=activemq\lib
set ACTIVEMQ_DATA=%ACTIVEMQ_HOME%\data
set ACTIVEMQ_CONF=%ACTIVEMQ_HOME%\conf
set LOGPATH="%CURRENTDIR%var\log"
SET LOGFILE="%CURRENTDIR%"\var\log\Install.log
SET FNMPROP="%CURRENTDIR%"\fnm.properties
set service_name=postgres
set display_name="ADVA: PostgreSQL Server"

del /F /Q "%CURRENTDIR%"FTPRepositoryUpdates.xml >%LOGFILE% 2>&1
del /F /Q "%CURRENTDIR%"SCPRepositoryUpdates.xml >>%LOGFILE% 2>&1
del /F /Q "%CURRENTDIR%""FSP Network Manager.exe" >>%LOGFILE% 2>&1
del /F /Q "%CURRENTDIR%""FSP Network Manager.LAX" >>%LOGFILE% 2>&1
del /F /Q "%CURRENTDIR%""FSP_NM_Administrator_Manual.pdf" >>%LOGFILE% 2>&1
del /F /Q "%CURRENTDIR%""FSP_NM_User_Manual.pdf" >>%LOGFILE% 2>&1
del /F /Q "%CURRENTDIR%"CustomProducts\osa5401.xml >>%LOGFILE% 2>&1
rd /Q /S "%CURRENTDIR%"failureprediction >>%LOGFILE% 2>&1
del /F /Q "%CURRENTDIR%""NM_Administrator_Manual.pdf" >>%LOGFILE% 2>&1
del /F /Q "%CURRENTDIR%""NM_User_Manual.pdf" >>%LOGFILE% 2>&1
del /F /Q "%CURRENTDIR%""*.pdf" >>%LOGFILE% 2>&1

del /F /Q "%CURRENTDIR%""FSP Mediation Server.exe" >>%LOGFILE% 2>&1
del /F /Q "%CURRENTDIR%""FSP Mediation Server.LAX" >>%LOGFILE% 2>&1
del /F /Q "%CURRENTDIR%""FSP JMS Server.exe" >>%LOGFILE% 2>&1
del /F /Q "%CURRENTDIR%""FSP JMS Server.LAX" >>%LOGFILE% 2>&1

if exist "%CURRENTDIR%jre64\bin\java.exe" (
set JVM_DLL="%CURRENTDIR%jre64\bin\server\jvm.dll"
set KEYTOOL_HOME="%CURRENTDIR%jre64\bin\keytool"
set PRUN_HOME="%CURRENTDIR%prunsrv64.exe"
set XMX="-Xmx4000M"
) else (
set JVM_DLL="%CURRENTDIR%jre\bin\client\jvm.dll"
set KEYTOOL_HOME="%CURRENTDIR%jre\bin\keytool"
set PRUN_HOME="%CURRENTDIR%prunsrv32.exe"
set XMX="-Xmx1000M"
)

%KEYTOOL_HOME% -genkeypair -alias nms-server-key -dname "CN=adtran.com,OU=Mosaic Network Controller,O=Adtran Networks SE,L=,ST=,C=US" -keyalg EC -validity 3650 --storepass NeverChange --keypass NeverChange -keystore "%CURRENTDIR%certs\fnmserver.ks" -storetype JKS -groupname secp384r1 -sigalg SHA256withECDSA >>%LOGFILE% 2>&1

set NMS_BIN="%CURRENTDIR%bin"

set PROXY_XMX="-Xmx512M"
set JMS_XMX="-Xmx1024M"
set ACTIVEMQ_CLASSPATH=%NMS_LIB%\activemqLauncher.jar
echo "Registering jms service"
%PRUN_HOME% //US//advajms --DisplayName="ADVA: JMS Server" --Description="ADVA: JMS Server" --Startup=auto --ServiceUser=LocalSystem --LogPath=%LOGPATH% --LogLevel=WARNING --LogPrefix=nmsservices-deamon --StdOutput= --StdError=%LOGPATH%\nmsservices-stderr.log --StartPath=%STARTPATH% --StartClass=org.apache.activemq.console.Main --StartMethod=main --StartParams=start --StartMode=jvm --StopClass=org.apache.activemq.console.Main --StopMethod=main --StopParams=shutdown --StopMode=jvm --Jvm=%JVM_DLL% --Classpath="%ACTIVEMQ_CLASSPATH%" --JvmOptions=%JMS_XMX% ++JvmOptions=-Dorg.apache.activemq.SERIALIZABLE_PACKAGES="java.lang,java.util,org.apache.activemq,org.fusesource.hawtbuf,com.thoughtworks.xstream.mapper,ni,java.time,org.apache.commons.lang3.tuple,com.google.common.collect,java.beans,com.adva" ++JvmOptions=-Dactivemq.home=activemq ++JvmOptions=-Dactivemq.base=activemq ++JvmOptions=-Dactivemq.conf=%ACTIVEMQ_CONF% ++JvmOptions=-Dactivemq.data=%ACTIVEMQ_DATA% ++JvmOptions=-Djava.rmi.server.hostname=localhost ++JvmOptions=-Dlog4j.configurationFile=file:"%ACTIVEMQ_HOME%/conf/log4j2.xml"

set CLASSPATH=lib/mediation.jar
echo "Registering proxy service"
%PRUN_HOME% //US//advaproxy --DisplayName="ADVA: Http Proxy" --Description="ADVA: Http Proxy" --Startup=manual --ServiceUser=LocalSystem --LogPath=%LOGPATH% --LogLevel=WARNING --LogPrefix=nmsservices-deamon --StdOutput= --StdError=%LOGPATH%\nmsservices-stderr.log --StartPath=%STARTPATH% --StartClass=com.adva.nlms.mediation.server.proxy.ProxyService --StartMethod=main --StartMode=jvm --StopClass=com.adva.nlms.mediation.server.proxy.ProxyService --StopMethod=stopService --StopMode=jvm --Jvm=%JVM_DLL% --Classpath="%CLASSPATH%" --JvmOptions=%PROXY_XMX%

echo "Registering mediation service"
%PRUN_HOME% //US//advams --DisplayName="ADVA: Mediation Server" --Description="ADVA: Mediation Server" --Startup=auto --ServiceUser=LocalSystem --LogPath=%LOGPATH% --LogLevel=WARNING --LogPrefix=nmsservices-deamon --StdOutput= --StdError=%LOGPATH%\nmsservices-stderr.log --StartPath=%STARTPATH% --StartClass=com.adva.nlms.mediation.Launcher --StartMethod=main --StartMode=jvm --StopClass=com.adva.nlms.mediation.Launcher --StopMethod=shutdown --StopMode=jvm --Jvm=%JVM_DLL% --Classpath="%CLASSPATH%" --JvmOptions=%XMX% ++JvmOptions=-XX:HeapDumpPath=NMHeapDumps ++JvmOptions=-XX:+HeapDumpOnOutOfMemoryError ++JvmOptions=-Xrs ++JvmOptions=-Djakarta.xml.bind.JAXBContextFactory=org.glassfish.jaxb.runtime.v2.ContextFactory ++JvmOptions=-Dcom.sun.xml.bind.v2.runtime.JAXBContextImpl.fastBoot=true ++JvmOptions=-Djtrace.logdir=var/log ++JvmOptions=-Djava.awt.headless=true ++JvmOptions=-Dfile.encoding=UTF-8 ++JvmOptions=-Djava.util.logging.config.file=./logging.properties ++JvmOptions=-Dorg.apache.activemq.SERIALIZABLE_PACKAGES="java.lang,java.util,org.apache.activemq,org.fusesource.hawtbuf,com.thoughtworks.xstream.mapper,ni,java.time,org.apache.commons.lang3.tuple,com.google.common.collect,java.beans,com.adva" ++JvmOptions=-Djava.library.path=lib ++JvmOptions=--add-opens=java.base/java.net=ALL-UNNAMED ++JvmOptions=--add-opens=java.base/sun.net.www.protocol.https=ALL-UNNAMED


if exist "%CURRENTDIR%"postgres\data\base GOTO yesPostGres

if exist "%SystemRoot%\system32\icacls.exe" (
  %SystemRoot%\system32\icacls "%CURRENTDIR%postgres\data" /grant "*S-1-5-32-545:(CI)(OI)(F)" >>%LOGFILE% 2>&1
  mkdir "%CURRENTDIR%postgres\upgrade"
  %SystemRoot%\system32\icacls "%CURRENTDIR%postgres\upgrade" /grant "*S-1-5-32-545:(CI)(OI)(F)" >>%LOGFILE% 2>&1
) else (
  %SystemRoot%\system32\cacls "%CURRENTDIR%postgres\data" /e /g Users:F >>%LOGFILE% 2>&1
  mkdir "%CURRENTDIR%postgres\upgrade"
  %SystemRoot%\system32\cacls "%CURRENTDIR%postgres\upgrade" /e /g Users:F >>%LOGFILE% 2>&1
)

if exist "%CURRENTDIR%"postgres.old (

if exist "%SystemRoot%\system32\icacls.exe" (
  %SystemRoot%\system32\icacls "%CURRENTDIR%postgres.old\data" /grant "*S-1-5-32-545:(CI)(OI)(F)" >>%LOGFILE% 2>&1
) else (
  %SystemRoot%\system32\cacls "%CURRENTDIR%postgres.old\data" /t /e /g Users:F >>%LOGFILE% 2>&1
)

echo "Initilizing database cluster"
"%CURRENTDIR%"postgres\bin\pg_ctl.exe -s -D "%CURRENTDIR%postgres\data" initdb -o "-U root -E UTF8 --locale=C" >>%LOGFILE% 2>&1
copy "%CURRENTDIR%"postgres\support-files\postgresql.conf "%CURRENTDIR%"postgres\data\postgresql.conf /Y >>%LOGFILE% 2>&1

echo "Running postgres upgrade"
cd "%CURRENTDIR%"postgres\upgrade
"%CURRENTDIR%"postgres\bin\pg_upgrade.exe -d "%CURRENTDIR%postgres.old\data" -D "%CURRENTDIR%postgres\data" -b "%CURRENTDIR%postgres.old\bin" -B "%CURRENTDIR%postgres\bin" -U root >>%LOGFILE% 2>&1
cd "%CURRENTDIR%"
echo "Starting PostgreSQL database server..."
%SystemRoot%\system32\net start postgres
"%CURRENTDIR%"postgres\bin\psql --quiet -U root -c "alter user root with password 'ChgMeNOW';" fnm
"%CURRENTDIR%"postgres\bin\psql --quiet -U root -c "ALTER USER adva WITH SUPERUSER;" fnm

copy "%CURRENTDIR%"postgres\support-files\pg_hba.conf-win "%CURRENTDIR%"postgres\data\pg_hba.conf /Y >>%LOGFILE% 2>&1
set PGPASSWORD=ChgMeNOW
"%CURRENTDIR%"postgres\bin\psql --quiet -U root -c "SELECT pg_reload_conf();" fnm
"%CURRENTDIR%"postgres\bin\psql --quiet -U root -c "alter user root with password 'ChgMeNOW';" fnm
"%CURRENTDIR%"postgres\bin\psql --quiet -U root -c "alter user adva with password 'NeverChange';" fnm
set PGPASSWORD=

echo "Stopping PostgreSQL database server..."
%SystemRoot%\system32\net stop postgres
goto cleanandstart
)

:yesPostGres
copy "%CURRENTDIR%"postgres\support-files\postgresql.conf "%CURRENTDIR%"postgres\data\postgresql.conf /Y >>%LOGFILE% 2>&1
echo "Starting PostgreSQL database server..."
%SystemRoot%\system32\net start postgres
"%CURRENTDIR%"postgres\bin\psql --quiet -U root -c "ALTER USER adva WITH SUPERUSER;" fnm
echo "Stopping PostgreSQL database server..."
%SystemRoot%\system32\net stop postgres

:cleanandstart
echo "Unregistering service"
"%CURRENTDIR%"postgres\bin\pg_ctl.exe unregister -N %service_name%

echo "Registering service"
"%CURRENTDIR%"postgres\bin\pg_ctl.exe register -N %service_name% -D "%CURRENTDIR%postgres\data" -w -t 180
sc config postgres DisplayName= %display_name%
sc config advams depend= postgres

copy "%CURRENTDIR%"postgres\support-files\pg_hba.conf-win "%CURRENTDIR%"postgres\data\pg_hba.conf /Y >>%LOGFILE% 2>&1

%SystemRoot%\system32\sc delete advaes 1> NUL
%SystemRoot%\system32\sc delete advass 1> NUL

cd %NMS_BIN%
call propup.bat -f

echo.
echo.
echo --------------------------------------------------------------
echo "You must reboot your system for the upgrade to be completed."
echo --------------------------------------------------------------
