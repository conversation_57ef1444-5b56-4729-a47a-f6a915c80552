ENC Build Documentation
=======================

PREREQUISITES
  64 bit Java 11 JDK must be installed to build ENC.
  Java 11 can be obtained from https://adoptium.net/temurin/archive/
  Set the JAVA_HOME environment variable to your Java 11 JDK installation.
  The exact java version used can be found on the ENC project home page in polarion
  https://polarion.advaoptical.com/polarion/#/project/FNM_Dev/home

  64 bit Postgres database server and command line tools must be installed.
  The easiest way to obtain the correct postgres installation is to install
  a contemporary ENC production image. ENC installers can be found at
  http://gdn-s-sitnms.advaoptical.com/FNMInst/FNM%206.0%20install%20package/

CONFIGURATION
  The ENC build system supports configuration through setting of environment
  variables or through configuration of 'settings.gradle' file.  The parameters
  below can be set in the environment or 'settings.gradle' to control build
  behavior. Note that in 'settings.gradle' you will need to wrap your configuration
  in a 'ext {  }' block.

  PARAMETER              | DEFAULT | DESCRIPTION
  =================================================================================
  cacheServer            |         | The gradle enterprise cache server to use.
                         |         | The gradle build cache allows the build system
                         |         | to reuse prior build artifacts rather than
                         |         | creating them again.
                         |         | Known build cache hosts in ADVA:
                         |         |   atl-gradle.advaoptical.com (Atlanta)
                         |         |   gdn-gradle.advaoptical.com (Gdynia)
                         |         |   gradle.advaoptical.com (Munich)
  _________________________________________________________________________________
  codeCoverage           |  false  | If set to true code coverage data will be
                         |         | collected when running ENC development tests.
  _________________________________________________________________________________
  configureFnmProperties |  false  | If true generate fnm.custom.properties to
                         |         | further customize the ENC server runtime.
  _________________________________________________________________________________
  fnmPropertiesOverride  |         | A string containing fnm.properties key+value
                         |         | pairs to include in fnm.custom.properties
                         |         | separated by '\n'.
  _________________________________________________________________________________
  flexeraServer          |         | The URL of the flexera server to use with
                         |         | ENC server.
                         |         | Known development flexera servers.
                         |         |   https://************:7071
                         |         |   https://************:7071
  _________________________________________________________________________________
  weaveEnc               |  true   | Perform eclipse link and aspectj byte code
                         |         | weaving when compiling ENC.
                         |         | If set to false then weaving must be done
                         |         | in the ENC server java runtime environment.
                         |         | Weaving at runtime can improve development time
                         |         | at the expense of test execution failures.
  _________________________________________________________________________________

TASKS
  The main build tasks are documented in the build.gradle files. The gradle 'tasks' command
  can be used be used to list and describe the ENC related build tasks. The format of this
  command is as follows:

      gradlew tasks --group GROUP_NAME

  where GROUP_NAME is one of the following...
     enc.build = Tasks related to building ENC
     enc.test = Tasks related to testing
     enc.exec = Tasks related to execution of ENC applications
     enc.production = Production build related tasks
