###################################################################
# TOML migration
###################################################################
# The migration is done with help of
# https://gitlab.rd.advaoptical.com/kamilku/gen-version-catalog-raku
#
# The `transitive` property is ignored
# The `ext` property is ignored
# The `classifier` property is ignored
#
#

[versions]
ver_activemq = "6.1.2"
ver_asm="9.7.1"
ver_aspectj = "1.9.22.1"
ver_castor_xml="1.4.1"
ver_cglib_nodep = "3.1"
ver_chart = "1.5.4"
ver_classmexer = "classmexer"
ver_classycle = "classycle"
ver_commons_beanutils = "1.10.0"
ver_commons_compress = "1.27.1"
ver_commons_codec = "1.17.1"
ver_commons_collections = "4.4"
ver_commons_digester = "2.1"
ver_commons_io = "2.16.1"
ver_commons_lang3 = "3.17.0"
ver_commons_logging = "1.3.4"
ver_commons_net  = "3.6"
ver_commons_pool2 = "2.12.0"
ver_commons_text = "1.13.0"
ver_controlsfx = "11.0.0"
ver_cxf = "4.1.0"
ver_diffutils = "1.2.1"
ver_emplugin = "emplugin"
ver_eclipselink = "4.0.5"
ver_externalsortinginjava = "0.1.1"
ver_fest_swing="1.2.1"
ver_findbugs = "3.0.0"
ver_findbugs_jsr = "3.0.2"
ver_flexnet = "2022.02"
ver_forms = "1.0.3"
ver_forwarder = "2.1"
ver_guava = "33.4.0-jre"
ver_gson  = "2.11.0"
ver_geronimo_j2ee_management_spec = "1.0.1"
ver_h2 = "2.3.232"
ver_hamcrest = "*******"
ver_hamcrest_core = "1.3"
ver_hamcrest_library = "1.3"
ver_hawtbuf = "1.11"
ver_hikaricp ="6.2.1"
ver_ipaddress = "4.2.0"
ver_jackson = "2.17.1"
ver_jacoco = "0.8.10"
ver_jacocoagent_enc = "0.7.5.201505241946"
ver_jakarta_activation_api = '2.1.3'
ver_jasper = "7.0.1"
ver_jasperreports_fonts = "jasperreports-fonts"
ver_jasperreports_pdf = "7.0.1"
ver_jasperreports_charts = "7.0.1"
ver_jasperreports_ant = "7.0.1"
ver_javafx = "17.0.7"
ver_javaparser = "1.0.11"
ver_java_parser = "2.5.1"
ver_javassist ="3.30.2-GA"
ver_jakarta_websocket = "2.2.0"
ver_jaxb = "4.0.5"
ver_jcip_annotations = "1.0"
ver_jcommon ="1.0.23"
ver_jakarta_xml_ws_api = "4.0.2"
ver_jdom2 = "*******"
ver_jersey = "3.1.8"
ver_jettison = "1.5.4"
ver_jetty = "11.0.21"
ver_jgoodies_binding = "2.0.6"
ver_jolokia = "2.1.2"
ver_jradius = "1.1.5"
ver_json = "20250107"
ver_jsonassert="1.5.3"
ver_jsonpath="2.9.0"
ver_json_smart="2.5.2"
ver_json_schema_validator="1.14.4"
ver_jta = "2.0.1"
ver_jul_to_slf4j = "1.7.36"
ver_junit = "5.10.3"
ver_jxbrowser = "7.41.6"
ver_jxl ="jxl"
ver_jxpath = "1.0"
ver_l2fprod_common_all = "1.0"
ver_log4j = "2.23.1"
ver_log4j_web = "2.23.1"
ver_mapstruct = "1.5.5.Final"
# micrometer is used only as a transient dependecy, below definition should be removed when update to spring context 6.2.x is in place, for now it enforces usning the same version in frontend and mediation
ver_micrometer_observation = "1.13.9"
ver_nimbus_jose_jwt = "9.48"
ver_junixsocket = "2.10.0"
ver_jgoodies_common = "1.8.1"
ver_metrics_core = "4.2.30"
ver_metrics_jmx = "4.2.30"
ver_mail = "2.1.3"
ver_mockito_jupiter = "5.5.0"
ver_mockito_core = "5.5.0"
ver_mock_server = "5.5.1"
var_nms_ml_client = "11.3.1.16"
ver_nms_ni_model = "17.2.1-20250711.100055-2"
ver_objenesis = "3.0.1"
ver_opencsv = "3.8"
ver_openpdf = "1.3.32"
ver_org_netbeans_api_visual = "1.0"
ver_org_openide_util = "1.0"
ver_org_sadun_util = "org.sadun.util"
ver_parser = "1.0.0"
ver_pdfbox="2.0.27"
ver_postgresql = "42.7.3"
ver_poi = "5.2.5"
ver_protobuf = "4.29.1"
ver_py4j="0.10.9.7"
ver_radclient = "4.06"
ver_rxjava = "2.1.1"
ver_saxon="8.7"
ver_saxonsa = "1.0"
#ver_servlet_api = "3.1.0"
ver_selenium = "3.12.0"
ver_slf4j = "1.7.36"
ver_slf4j_simple = "1.7.36"
ver_snmp4j = "2.8.18"
ver_snmp4j_log4j = "2.8.11"
ver_spring = "6.1.16"
ver_spring_security = "6.2.8"
ver_spring_kafka = "3.2.6"
ver_sshd = "2.11.0"
ver_svnkit="1.7.8"
ver_swagger = "2.2.20"
ver_tacclient = "2.01"
ver_testng = "6.9.13.6"
ver_tika_parsers="1.1"
ver_tink="1.16.0"
ver_tomcat_el_api = "9.0.93"
ver_unbescape = "1.1.0.RELEASE"
ver_unitils = "3.4.6"
# woodstox-core is used only as a transient dependecy, below definition should be removed
# after update to jackson-dataformat-xml  which has  woodstox-core 7.1.0 or higher in deps list
ver_woodstox-core = "7.1.0"
ver_xmlunit = "xmlunit"
ver_xstream = "1.4.20"
ver_yfiles_license = "3.5"

[libraries]
aopalliance = 'aopalliance:aopalliance:1.0'
jfoenix_javafx = { group = "com.jfoenix", name = "jfoenix", version = "9.0.8" }
hikaricp = { group = "com.zaxxer", name = "HikariCP", version.ref = "ver_hikaricp" }
activeio_core = { group = "org.apache.activemq", name = "activeio-core", version = "3.1.4" }
activemq_console = { group = "org.apache.activemq", name = "activemq-console", version.ref = "ver_activemq" }
activemq_jaas = { group = "org.apache.activemq", name = "activemq-jaas", version.ref = "ver_activemq" }
activemq_broker = { group = "org.apache.activemq", name = "activemq-broker", version.ref = "ver_activemq" }
activemq_client = { group = "org.apache.activemq", name = "activemq-client", version.ref = "ver_activemq" }
activemq_jms_pool = { group = "org.apache.activemq", name = "activemq-jms-pool", version.ref = "ver_activemq" }
activemq_kahadb_store = { group = "org.apache.activemq", name = "activemq-kahadb-store", version.ref = "ver_activemq" }
activemq_openwire_legacy = { group = "org.apache.activemq", name = "activemq-openwire-legacy", version.ref = "ver_activemq" }
activemq_pool = { group = "org.apache.activemq", name = "activemq-pool", version.ref = "ver_activemq" }
activemq_protobuf = { group = "org.apache.activemq.protobuf", name = "activemq-protobuf", version = "1.1" }
activemq_spring = { group = "org.apache.activemq", name = "activemq-spring", version.ref = "ver_activemq" }
assertJ = { group = "org.assertj", name = "assertj-core", version = "3.18.1" }
nimbus_jose_jwt = { group = "com.nimbusds", name = "nimbus-jose-jwt", version.ref = "ver_nimbus_jose_jwt" }
ehcache = { group = "net.sf.ehcache", name = "ehcache", version = "1.2.3" }
enc_cmsw = { group = "com.adva.enc.cmsw", name = "cmsw", version = "4.01.05" }
enc_docs = { group = "com.adva.enc.docs", name = "enc-docs", version = "14.2.1.220913" }
enc_ypdb = { group = "com.adva.ypdb", name = "yp", version = "17.2.1-20250801.123219-7" }
enc_ypdb_commons = { group = "com.adva.ypdb", name = "ypdb-commons", version = "17.2.1-20250801.123219-7" }
enc_docs_other = { group = "com.adva.enc.docs", name = "enc-docs-other", version = "14.1.1" }
enc_eth_help = { group = "com.adva.enc.docs", name = "enc-eth-help", version = "14.2.1.220909" }
enc_fiber_help = { group = "com.adva.enc.docs", name = "enc-fiber-help", version = "14.2.1.220909" }
enc_sync_help = { group = "com.adva.enc.docs", name = "enc-sync-help", version = "14.2.1.220909" }
enc_wdm_help = { group = "com.adva.enc.docs", name = "enc-wdm-help", version = "14.2.1.220909" }
enc_um_help = { group = "com.adva.enc.docs", name = "enc-um-help", version = "14.2.1.220909" }
enc_quick_help = { group = "com.adva.enc.docs", name = "enc-quick-help", version = "14.2.1.220909" }
adva_mtosi = { group = "com.adva.nlms.lib.adva_mtosi", name = "adva_mtosi", version = "3.0.0" }
antlr = { group = "antlr", name = "antlr", version = "2.7.7" }
asm= { group = "org.ow2.asm", name="asm", version.ref = "ver_asm" }
aspectjrt = { group = "org.aspectj", name = "aspectjrt", version.ref = "ver_aspectj" }
aspectjweaver = { group = "org.aspectj", name = "aspectjweaver", version.ref = "ver_aspectj" }
aspectjtools = { group = "org.aspectj", name = "aspectjtools", version.ref = "ver_aspectj" }
awaitility = { group = "com.jayway.awaitility", name = "awaitility", version = "1.7.0" }
castor = { group = "com.adva.nlms.lib.castor", name = "castor", version = "0.9.7" }
castor_xml= { group = "org.codehaus.castor", name="castor-xml", version.ref = "ver_castor_xml" }
cglib_nodep = { group = "cglib", name = "cglib-nodep", version.ref = "ver_cglib_nodep" }
chart = { group = "org.jfree", name = "jfreechart", version.ref = "ver_chart" }
classmexer = { group = "com.adva.nlms.testlib.classmexer", name = "classmexer", version.ref = "ver_classmexer" }
classycle = { group = "com.adva.nlms.testlib.classycle", name = "classycle", version.ref = "ver_classycle" }
commons_beanutils = { group = "commons-beanutils", name = "commons-beanutils", version.ref = "ver_commons_beanutils" }
commons_codec = { group = "commons-codec", name = "commons-codec", version.ref = "ver_commons_codec" }
commons_collections4 = { group = "org.apache.commons", name = "commons-collections4", version.ref = "ver_commons_collections" }
commons_compress = { group = "org.apache.commons", name = "commons-compress", version.ref = "ver_commons_compress" }
commons_digester = { group = "commons-digester", name = "commons-digester", version.ref = "ver_commons_digester" }
commons_io = { group = "commons-io", name = "commons-io", version.ref = "ver_commons_io" }
commons_lang3 = { group = "org.apache.commons", name = "commons-lang3", version.ref = "ver_commons_lang3" }
commons_logging = { group = "commons-logging", name = "commons-logging", version.ref = "ver_commons_logging" }
commons_net = { group = "commons-net", name = "commons-net", version.ref = "ver_commons_net" }
commons_pool2 = { group = "org.apache.commons", name = "commons-pool2", version.ref = "ver_commons_pool2" }
commons_text = { group = "org.apache.commons", name = "commons-text", version.ref = "ver_commons_text" }
commons_jcs_core = { group = 'org.apache.commons', name = 'commons-jcs-core', version = '2.2.1'}
controlsfx = { group = "org.controlsfx", name = "controlsfx", version.ref = "ver_controlsfx" }
cxf_core = { group = "org.apache.cxf", name = "cxf-core", version.ref = "ver_cxf" }
cxf_rt_bindings_soap = { group = "org.apache.cxf", name = "cxf-rt-bindings-soap", version.ref = "ver_cxf" }
cxf_rt_frontend_jaxws = { group = "org.apache.cxf", name = "cxf-rt-frontend-jaxws", version.ref = "ver_cxf" }
cxf_rt_transports_http = { group = "org.apache.cxf", name = "cxf-rt-transports-http", version.ref = "ver_cxf" }
cxf_rt_ws_security = { group = "org.apache.cxf", name = "cxf-rt-ws-security", version.ref = "ver_cxf" }
cxf_tools_common = { group = "org.apache.cxf", name = "cxf-tools-common", version.ref = "ver_cxf" }
cxf_tools_validator = { group = "org.apache.cxf", name = "cxf-tools-validator", version.ref = "ver_cxf" }
cxf_tools_wsdlto_core = { group = "org.apache.cxf", name = "cxf-tools-wsdlto-core", version.ref = "ver_cxf" }
cxf_tools_wsdlto_databinding_jaxb = { group = "org.apache.cxf", name = "cxf-tools-wsdlto-databinding-jaxb", version.ref = "ver_cxf" }
cxf_tools_wsdlto_frontend_jaxws = { group = "org.apache.cxf", name = "cxf-tools-wsdlto-frontend-jaxws", version.ref = "ver_cxf" }
diffutils = { group = "com.googlecode.java-diff-utils", name = "diffutils", version.ref = "ver_diffutils" }
eclipselink = { group = "org.eclipse.persistence", name = "eclipselink", version.ref = "ver_eclipselink" }
emplugin = { group = "com.adva.nlms.lib.emplugin", name = "emplugin", version.ref = "ver_emplugin" }
externalsortinginjava = { group = "com.adva.nlms.lib.com.google.code", name = "externalsortinginjava", version.ref = "ver_externalsortinginjava" }
fest_swing= { group = "org.easytesting", name="fest-swing", version.ref = "ver_fest_swing" }
findbugs_annots = { group = "com.google.code.findbugs", name = "annotations", version.ref = "ver_findbugs" }
findbugs_jsr305 = { group = "com.google.code.findbugs", name = "jsr305", version.ref = "ver_findbugs_jsr" }
flexnet_eccpresso = { group = "com.flexnet", name = "EccpressoAll", version.ref = "ver_flexnet" }
flexnet_flxBinary = { group = "com.flexnet", name = "flxBinary", version.ref = "ver_flexnet" }
flexnet_flxClient = { group = "com.flexnet", name = "flxClient", version.ref = "ver_flexnet" }
flexnet_flxClientNative = { group = "com.flexnet", name = "flxClientNative", version.ref = "ver_flexnet" }
forms = { group = "com.adva.nlms.lib.forms", name = "forms", version.ref = "ver_forms" }
forwarder = { group = "com.adva.nlms.lib.forwarder", name = "forwarder", version.ref = "ver_forwarder" }
geronimo_j2ee_management_spec = { group = "org.apache.geronimo.specs", name = "geronimo-j2ee-management_1.1_spec", version.ref = "ver_geronimo_j2ee_management_spec" }
guava = { group = "com.google.guava", name = "guava", version.ref = "ver_guava" }
gson = { group = "com.google.code.gson", name = "gson", version.ref = "ver_gson" }
h2 = { group = "com.h2database", name = "h2", version.ref = "ver_h2" }
hamcrest = { group = "org.hamcrest", name = "java-hamcrest", version.ref = "ver_hamcrest" }
hamcrest_core = { group = "org.hamcrest", name = "hamcrest-core", version.ref = "ver_hamcrest_core" }
hamcrest_library = { group = "org.hamcrest", name = "hamcrest-library", version.ref = "ver_hamcrest_library" }
hawtbuf = { group = "org.fusesource.hawtbuf", name = "hawtbuf", version.ref = "ver_hawtbuf" }
http_client5 = { group = "org.apache.httpcomponents.client5", name = "httpclient5", version = "5.3.1" }
ipaddress = { group = "com.github.seancfoley", name = "ipaddress", version.ref = "ver_ipaddress" }
jackson_annotations = { group = "com.fasterxml.jackson.core", name = "jackson-annotations", version.ref = "ver_jackson" }
jackson_core = { group = "com.fasterxml.jackson.core", name = "jackson-core", version.ref = "ver_jackson" }
jackson_databind = { group = "com.fasterxml.jackson.core", name = "jackson-databind", version.ref = "ver_jackson" }
jackson_dataformat_yaml = { group = "com.fasterxml.jackson.dataformat", name = "jackson-dataformat-yaml", version.ref = "ver_jackson" }
jackson_xml_provider = { group = "com.fasterxml.jackson.jakarta.rs", name = "jackson-jakarta-rs-xml-provider", version.ref = "ver_jackson" }
jackson_jdk8 = { group = "com.fasterxml.jackson.datatype", name = "jackson-datatype-jdk8", version.ref = "ver_jackson" }
jackson_json_provider = { group = "com.fasterxml.jackson.jakarta.rs", name = "jackson-jakarta-rs-json-provider", version.ref = "ver_jackson" }
jackson_jsr310 = { group = "com.fasterxml.jackson.datatype", name = "jackson-datatype-jsr310", version.ref = "ver_jackson" }
jacocoagent_enc = { group = "com.adva.nlms.testlib.org.jacoco", name = "jacocoagent", version.ref = "ver_jacocoagent_enc" }
jacoco_agent = { group = "org.jacoco", name = "org.jacoco.agent", version.ref = "ver_jacoco" }
jacoco_cli = { group = "org.jacoco", name = "org.jacoco.cli", version.ref = "ver_jacoco" }
jasper = { group = "net.sf.jasperreports", name = "jasperreports", version.ref = "ver_jasper" }
jasperreports_fonts = { group = "com.adva.nlms.lib.jasperreports-fonts", name = "jasperreports-fonts", version.ref = "ver_jasperreports_fonts" }
jasperreports_pdf = { group = "net.sf.jasperreports", name = "jasperreports-pdf", version.ref = "ver_jasperreports_pdf" }
jasperreports_charts = { group = "net.sf.jasperreports", name = "jasperreports-charts", version.ref = "ver_jasperreports_charts" }
jasperreports_ant = { group = "net.sf.jasperreports", name = "jasperreports-ant", version.ref = "ver_jasperreports_ant" }
java_parser = { group = "com.github.javaparser", name = "javaparser-core", version.ref = "ver_java_parser" }
javaparser = { group = "com.adva.nlms.testlib.com.google.code.javaparser", name = "javaparser", version.ref = "ver_javaparser" }
javassist = { group = "org.javassist", name = "javassist", version.ref = "ver_javassist" }
jakarta_annotation_api = { group = "jakarta.annotation", name = "jakarta.annotation-api", version = "2.1.1" } # TO CHECK: 3.0.0 is available
jakarta_ejb_api = { group = "jakarta.ejb", name = "jakarta.ejb-api", version = "4.0.1" }
jakarta_inject = { group = "jakarta.inject", name = "jakarta.inject-api", version = "2.0.1" }
jakarta_jms_api = { group = "jakarta.jms", name = "jakarta.jms-api", version = "3.1.0" }
jakarta_jws_api = { group = "jakarta.jws", name = "jakarta.jws-api", version = "3.0.0" }
jakarta_persistence = { group = "jakarta.persistence", name = "jakarta.persistence-api", version = "3.1.0" }
jakarta_activation = { group = "jakarta.activation", name = "jakarta.activation-api", version.ref ="ver_jakarta_activation_api" }
angus_activation = { group = "org.eclipse.angus", name = "angus-activation", version = "2.0.2" }
jakarta_websocket = { group = "jakarta.websocket", name = "jakarta.websocket-api", version.ref = "ver_jakarta_websocket" }
jakarta_ws_rs_api = { group = "jakarta.ws.rs", name = "jakarta.ws.rs-api", version = "4.0.0" }
jakarta_xml_bind_api = { group = "jakarta.xml.bind", name = "jakarta.xml.bind-api", version = "4.0.2" }
jaxb_impl = { group = "com.sun.xml.bind", name = "jaxb-impl", version.ref = "ver_jaxb" }
jaxb_xjc = { group = "com.sun.xml.bind", name = "jaxb-xjc", version.ref = "ver_jaxb" }
jaxws_api = { group = "jakarta.xml.ws", name = "jakarta.xml.ws-api", version.ref = "ver_jakarta_xml_ws_api" }
jcip_annotations = { group = "net.jcip", name ="jcip-annotations", version.ref = "ver_jcip_annotations"}
jcommon = { group = "org.jfree", name = "jcommon", version.ref = "ver_jcommon" }
jdom2 = { group = "org.jdom", name = "jdom2", version.ref = "ver_jdom2" }
jersey_client = { group = "org.glassfish.jersey.core", name = "jersey-client", version.ref = "ver_jersey" }
jersey_common = { group = "org.glassfish.jersey.core", name = "jersey-common", version.ref = "ver_jersey" }
jersey_hk2 = { group = "org.glassfish.jersey.inject", name = "jersey-hk2", version.ref = "ver_jersey" }
jersey_container_servlet = { group = "org.glassfish.jersey.containers", name = "jersey-container-servlet", version.ref = "ver_jersey" }
jersey_container_servlet_core = { group = "org.glassfish.jersey.containers", name = "jersey-container-servlet-core", version.ref = "ver_jersey" }
jersey_guava = { group = "org.glassfish.jersey.bundles.repackaged", name = "jersey-guava", version = "2.25.1" }
jersey_media_jaxb = { group = "org.glassfish.jersey.media", name = "jersey-media-jaxb", version.ref = "ver_jersey" }
jersey_media_json_jackson = { group = "org.glassfish.jersey.media", name = "jersey-media-json-jackson", version.ref = "ver_jersey" }
jersey_media_multipart = { group = "org.glassfish.jersey.media", name = "jersey-media-multipart", version.ref = "ver_jersey" }
jersey_server = { group = "org.glassfish.jersey.core", name = "jersey-server", version.ref = "ver_jersey" }
jersey_test_framework_core = { group = "org.glassfish.jersey.test-framework", name = "jersey-test-framework-core", version.ref = "ver_jersey" }
jersey_test_framework_provider_inmemory = { group = "org.glassfish.jersey.test-framework.providers", name = "jersey-test-framework-provider-inmemory", version.ref = "ver_jersey" }
jersey_test_framework_provider_grizzly2 = { group = "org.glassfish.jersey.test-framework.providers", name = "jersey-test-framework-provider-grizzly2", version.ref = "ver_jersey" }
jersey_apache5_connector = { group = "org.glassfish.jersey.connectors", name = "jersey-apache5-connector", version.ref = "ver_jersey" }
jettison = { group = "org.codehaus.jettison", name = "jettison", version.ref = "ver_jettison" }
jetty_server = { group = "org.eclipse.jetty", name = "jetty-server", version.ref = "ver_jetty" }
jetty_deploy = { group = "org.eclipse.jetty", name = "jetty-deploy", version.ref = "ver_jetty" }
jetty_webapp = { group = "org.eclipse.jetty", name = "jetty-webapp", version.ref = "ver_jetty" }
jetty_http = { group = "org.eclipse.jetty", name = "jetty-http", version.ref = "ver_jetty" }
jetty_proxy = { group = "org.eclipse.jetty", name = "jetty-proxy", version.ref = "ver_jetty" }
jetty_servlet = { group = "org.eclipse.jetty", name = "jetty-servlet", version.ref = "ver_jetty" }
jetty_slf4j = { group = "org.eclipse.jetty", name = "jetty-slf4j-impl", version.ref = "ver_jetty" }
jetty_websocket_client = { group = "org.eclipse.jetty.websocket", name = "websocket-jetty-client", version.ref = "ver_jetty" }
jetty_websocket_server = { group = "org.eclipse.jetty.websocket", name = "websocket-jetty-server", version.ref = "ver_jetty" }
jfrog_artifactory_java_client = { group = "org.jfrog.artifactory.client", name = "artifactory-java-client-services", version = "2.6.0" }
jgoodies_common = { group="com.jgoodies", name = "jgoodies-common", version.ref = "ver_jgoodies_common"}
jolokia_jvm_agent = { group = "org.jolokia", name = "jolokia-agent-jvm", version.ref = "ver_jolokia" }
jolokia_server_core = { group = "org.jolokia", name = "jolokia-server-core", version.ref = "ver_jolokia" }
jolokia_service_serializer = { group = "org.jolokia", name = "jolokia-service-serializer", version.ref = "ver_jolokia" }
jolokia_service_jmx = { group = "org.jolokia", name = "jolokia-service-jmx", version.ref = "ver_jolokia" }
jolokia_service_jsr160 = { group = "org.jolokia", name = "jolokia-service-jsr160", version.ref = "ver_jolokia" }
jolokia_json = { group = "org.jolokia", name = "jolokia-json", version.ref = "ver_jolokia" }
jradius_core = { group = "net.jradius", name = "jradius-core", version.ref = "ver_jradius" }
jradius_dictionary = { group = "net.jradius", name = "jradius-dictionary", version.ref = "ver_jradius" }
jsch = { group = "com.github.mwiede", name = "jsch", version = "0.2.23" }
jakarta_transaction_api = { group = "jakarta.transaction", name = "jakarta.transaction-api", version.ref = "ver_jta" }
json = { group = "org.json", name = "json", version.ref = "ver_json" }
jsonassert= { group = "org.skyscreamer", name="jsonassert", version.ref = "ver_jsonassert" }
json_patch = { group = 'com.github.java-json-tools', name = 'json-patch', version = '1.13' }
jsonpath= { group = "com.jayway.jsonpath", name="json-path", version.ref = "ver_jsonpath" }
json_schema_validator = { group = "com.github.erosb", name = "everit-json-schema", version.ref = "ver_json_schema_validator"}
json_smart= { group = "net.minidev", name="json-smart", version.ref = "ver_json_smart" }
jul_to_slf4j = { group = "org.slf4j", name = "jul-to-slf4j", version.ref = "ver_jul_to_slf4j" }
jxbrowser = { group = "com.teamdev.jxbrowser", name = "jxbrowser", version.ref = "ver_jxbrowser" }
jxbrowser_javafx = { group = "com.teamdev.jxbrowser", name = "jxbrowser-javafx", version.ref = "ver_jxbrowser" }
jxbrowser_linux64 = { group = "com.teamdev.jxbrowser", name = "jxbrowser-linux64", version.ref = "ver_jxbrowser" }
jxbrowser_win64 = { group = "com.teamdev.jxbrowser", name = "jxbrowser-win64", version.ref = "ver_jxbrowser" }
jxbrowser_win32 = { group = "com.teamdev.jxbrowser", name = "jxbrowser-win32", version.ref = "ver_jxbrowser" }
jxl = { group = "com.adva.nlms.lib.jxl", name = "jxl", version.ref = "ver_jxl" }
jxpath = { group = "com.adva.common.lib", name = "jxpath", version.ref = "ver_jxpath" }
l2fprod_common_all = { group = "com.adva.common.lib", name = "l2fprod-common-all", version.ref = "ver_l2fprod_common_all" }
log4j12_api = { group = "org.apache.logging.log4j", name = "log4j-1.2-api", version.ref = "ver_log4j" }
log4j_api = { group = "org.apache.logging.log4j", name = "log4j-api", version.ref = "ver_log4j" }
log4j_core = { group = "org.apache.logging.log4j", name = "log4j-core", version.ref = "ver_log4j" }
log4j_iostreams = { group = "org.apache.logging.log4j", name = "log4j-iostreams", version.ref = "ver_log4j" }
log4j_jcl = { group = "org.apache.logging.log4j", name = "log4j-jcl", version.ref = "ver_log4j" }
log4j_slf4j_impl = { group = "org.apache.logging.log4j", name = "log4j-slf4j-impl", version.ref = "ver_log4j" }
log4j_slf4j2_impl = { group = "org.apache.logging.log4j", name = "log4j-slf4j2-impl", version.ref = "ver_log4j" }
log4j_web = { group = "org.apache.logging.log4j", name = "log4j-web", version.ref = "ver_log4j_web" }
jakarta_mail = { group = "jakarta.mail", name = "jakarta.mail-api", version.ref = "ver_mail" }
angus_mail = { group = "org.eclipse.angus", name = "angus-mail", version = "2.0.3" }
mapstruct = { group = "org.mapstruct", name = "mapstruct", version.ref = "ver_mapstruct" }
junixsocket_common = { group = "com.kohlschutter.junixsocket", name = "junixsocket-common", version.ref = "ver_junixsocket" }
junixsocket_common_native = { group = "com.kohlschutter.junixsocket", name = "junixsocket-native-common", version.ref = "ver_junixsocket" }
mapstruct_processor = { group = "org.mapstruct", name = "mapstruct-processor", version.ref = "ver_mapstruct" }
metrics_core = { group = "io.dropwizard.metrics", name = "metrics-core", version.ref = "ver_metrics_core" }
metrics_jmx = { group = "io.dropwizard.metrics", name = "metrics-jmx", version.ref = "ver_metrics_jmx" }
micrometer_observation = { group = "io.micrometer", name = "micrometer-observation", version.ref = "ver_micrometer_observation"}
mockito_jupiter = { group = "org.mockito", name = "mockito-junit-jupiter", version.ref = "ver_mockito_jupiter" }
mock_server_netty = { group = "org.mock-server", name = "mockserver-netty", version.ref = "ver_mock_server" }
mock_server_client = { group = "org.mock-server", name = "mockserver-client-java", version.ref = "ver_mock_server" }
mockito_core = { group = "org.mockito", name = "mockito-core", version.ref = "ver_mockito_core" }
nms_ni_model = { group = "com.adva.nlms", name = "nms-ni-model", version.ref = "ver_nms_ni_model" }
objenesis = { group = "org.objenesis", name = "objenesis", version.ref = "ver_objenesis" }
opencsv = { group = "com.opencsv", name = "opencsv", version.ref = "ver_opencsv" }
openpdf = { group = "com.github.librepdf", name = "openpdf", version.ref = "ver_openpdf" }
org_netbeans_api_visual = { group = "com.adva.common.lib", name = "org-netbeans-api-visual", version.ref = "ver_org_netbeans_api_visual" }
org_openide_util = { group = "com.adva.common.lib", name = "org-openide-util", version.ref = "ver_org_openide_util" }
org_sadun_util = { group = "com.adva.nlms.testlib.org.sadun.util", name = "org.sadun.util", version.ref = "ver_org_sadun_util" }
parser = { group = "com.adva.nlms.lib.parser", name = "parser", version.ref = "ver_parser" }
pdfbox = { group = "org.apache.pdfbox", name = "pdfbox", version.ref = "ver_pdfbox" }
postgresql = { group = "org.postgresql", name = "postgresql", version.ref = "ver_postgresql" }
poi_ooxml = { group = "org.apache.poi", name = "poi-ooxml", version.ref = "ver_poi" }
poi = { group = "org.apache.poi", name = "poi", version.ref = "ver_poi" }
protobuf_java = { group = "com.google.protobuf", name = "protobuf-java", version.ref = "ver_protobuf" }
protobuf_java_util = { group = "com.google.protobuf", name = "protobuf-java-util", version.ref = "ver_protobuf" }
py4j= { group = "net.sf.py4j", name="py4j", version.ref = "ver_py4j" }
quartz = { group = "org.quartz-scheduler", name = "quartz", version = "2.5.0" }
radclient = { group = "com.adva.nlms.lib.com.axlradius.radclient4", name = "radclient", version.ref = "ver_radclient" }
rxjava = { group = "io.reactivex.rxjava2", name = "rxjava", version.ref = "ver_rxjava" }
saxon= { group = "net.sf.saxon", name="saxon", version.ref = "ver_saxon" }
saxonsa = { group = "com.adva.common.lib", name = "saxonsa", version.ref = "ver_saxonsa" }
jakarta_servlet_api = { group = "jakarta.servlet", name = "jakarta.servlet-api", version = "5.0.0" }
selenium_api = { group = "org.seleniumhq.selenium", name = "selenium-api", version.ref = "ver_selenium" }
selenium_java = { group = "org.seleniumhq.selenium", name = "selenium-java", version.ref = "ver_selenium" }
selenium_server = { group= "org.seleniumhq.selenium", name = "selenium-server", version.ref = "ver_selenium" }
selenium_remote_driver = { group = "org.seleniumhq.selenium", name = "selenium-remote-driver", version.ref = "ver_selenium" }
slf4j_api = { group = "org.slf4j", name = "slf4j-api", version.ref = "ver_slf4j" }
slf4j_jdk = { group = "org.slf4j", name = "slf4j-jdk14", version.ref = "ver_slf4j" }
slf4j_simple = { group = "org.slf4j", name = "slf4j-simple", version.ref = "ver_slf4j_simple" }
snmp4j = { group = "org.snmp4j", name = "snmp4j", version.ref = "ver_snmp4j" }
snmp4j_log4j = {group= "org.snmp4j", name= "snmp4j-log4j", version.ref= "ver_snmp4j_log4j"}
snmp4j_smi_pro = { group = "org.snmp4j", name = "snmp4j-smi-pro", version = "1.9.0" }
spring_aop = { group = "org.springframework", name = "spring-aop", version.ref = "ver_spring" }
spring_beans = { group = "org.springframework", name = "spring-beans", version.ref = "ver_spring" }
spring_context = { group = "org.springframework", name = "spring-context", version.ref = "ver_spring" }
spring_core = { group = "org.springframework", name = "spring-core", version.ref = "ver_spring" }
spring_expression = { group = "org.springframework", name = "spring-expression", version.ref = "ver_spring" }
spring_jdbc = { group = "org.springframework", name = "spring-jdbc", version.ref = "ver_spring" }
spring_jms = { group = "org.springframework", name = "spring-jms", version.ref = "ver_spring" }
spring_orm = { group = "org.springframework", name = "spring-orm", version.ref = "ver_spring" }
spring_security_aspects = { group = "org.springframework.security", name = "spring-security-aspects", version.ref = "ver_spring_security" }
spring_security_config = { group = "org.springframework.security", name = "spring-security-config", version.ref = "ver_spring_security" }
spring_security_web = { group = "org.springframework.security", name = "spring-security-web", version.ref = "ver_spring_security" }
spring_test = { group = "org.springframework", name = "spring-test", version.ref = "ver_spring" }
spring_tx = { group = "org.springframework", name = "spring-tx", version.ref = "ver_spring" }
spring_web = { group = "org.springframework", name = "spring-web", version.ref = "ver_spring" }
spring_webmvc = { group = "org.springframework", name = "spring-webmvc", version.ref = "ver_spring" }
spring_kafka = { group = "org.springframework.kafka", name = "spring-kafka", version.ref = "ver_spring_kafka" }
springwolf_annotations = { group = 'io.github.springwolf', name = 'springwolf-annotations', version = '1.12.0' }
sshd = { group = "org.apache.sshd", name = "sshd-core", version.ref = "ver_sshd" }
svnkit= { group = "org.tmatesoft.svnkit", name="svnkit", version.ref = "ver_svnkit" }
swagger_annotations = { group = 'io.swagger.core.v3', name = 'swagger-annotations-jakarta', version.ref = "ver_swagger" }
swagger_core = { group = "io.swagger.core.v3", name = "swagger-core-jakarta", version.ref = "ver_swagger" }
swagger_jaxrs2 = { group = "io.swagger.core.v3", name = "swagger-jaxrs2-jakarta", version.ref = "ver_swagger" }
swagger_models = { group = "io.swagger.core.v3", name = "swagger-models-jakarta", version.ref = "ver_swagger" }
svp = { group = "com.adva.enc.svp", name = "svp", version = "1.0" }
tacclient = { group = "com.adva.nlms.lib.com.axlradius.tacacs", name = "tacclient", version.ref = "ver_tacclient" }
testng = { group = "org.testng", name = "testng", version.ref = "ver_testng" }
tika_parsers= { group = "org.apache.tika", name="tika-parsers", version.ref = "ver_tika_parsers" }
tink = { group = "com.google.crypto.tink", name = "tink", version.ref = "ver_tink" }
tomcat_el_api = { group = "org.apache.tomcat", name = "tomcat-el-api", version.ref = "ver_tomcat_el_api" }
unbescape = { group = "org.unbescape", name = "unbescape", version.ref = "ver_unbescape" }
unitils = { group = "org.unitils", name = "unitils-core", version.ref = "ver_unitils" }
validation = { group = "com.adva.nlms.lib.com.jgoodies.validation", name = "validation", version = "1.0" }
jgoodies_binding = { group = "com.jgoodies", name = "binding", version.ref = "ver_jgoodies_binding" }
jakarta_validation_api = { group = "jakarta.validation", name = "jakarta.validation-api", version = "3.1.0" }
velocity = { group = "org.apache.velocity", name = "velocity-engine-core", version = "2.3" }
woodstox-core = { group = "com.fasterxml.woodstox", name="woodstox-core", version.ref="ver_woodstox-core"}
xalan = { group = "xalan", name = "xalan", version = "2.7.3" }
xalan_serializer = { group = "xalan", name = "serializer", version = "2.7.3" }
xercesImpl = { group = "xerces", name = "xercesImpl", version = "2.12.2" }
xml_apis = { group = "xml-apis", name = "xml-apis", version = "1.4.01" }
xmlunit = { group = "com.adva.nlms.testlib.xmlunit", name = "xmlunit", version.ref = "ver_xmlunit" }
xstream = { group = "com.thoughtworks.xstream", name = "xstream", version.ref = "ver_xstream" }
yfiles_for_javafx = { group = "com.adva.common.lib", name = "yfiles-for-javafx", version = "3.2" }
yfiles_license = { group = "com.adva.common.lib", name = "yfiles-license", version.ref = "ver_yfiles_license" }
yguard = { group = "com.yworks", name = "yguard", version = "4.0.0" }
jnc = { group = "com.tailf.jnc", name = "JNC", version = "1.1.0-adtran-20250618.1" }
sshj = { group = "com.hierynomus", name = "sshj", version = "0.39.0" }

pact_jupiter = { group = 'au.com.dius.pact.consumer', name = 'junit5', version = '4.6.17' }
org_junit_jupiter_api = { group = "org.junit.jupiter", name = "junit-jupiter-api", version.ref = "ver_junit" }
org_junit_jupiter_engine = { group = "org.junit.jupiter", name = "junit-jupiter-engine", version.ref = "ver_junit" }
org_junit_jupiter_vintage-engine = { group  = "org.junit.vintage", name = "junit-vintage-engine", version.ref ="ver_junit" }
org_junit_jupiter_params = { group  = "org.junit.jupiter", name = "junit-jupiter-params", version.ref = "ver_junit" }
org_junit_jupiter_migrationsupport = { group = "org.junit.jupiter", name = "junit-jupiter-migrationsupport", version.ref = "ver_junit" }
httpclient = 'commons-httpclient:commons-httpclient:3.1'

telstra_tapi_database = { group = "telstra_tapi", name = "telstra_tapi", version = "15.1"}

[bundles]
junit_jupiter = ["org_junit_jupiter_api", "org_junit_jupiter_engine",
    "org_junit_jupiter_vintage-engine", "org_junit_jupiter_params", "org_junit_jupiter_migrationsupport"]

junit = [
    "org.junit.jupiter.api",
    "org.junit.jupiter.engine",
    "org.junit.jupiter.vintage-engine",
    "org.junit.jupiter.params",
    "org.junit.jupiter.migrationsupport"
]
