#
# Owner: ssingam

com.adva.nlms.mediation.common.persistence.isFlushEnabled=false

#com.adva.nlms.mediation.mtosi.startModule=true
###### Server Access Options ######
### Server with multiple IP interfaces ###
#
# The IP address used for server to client and server to server communication
# It can be IPv4 or IPv6 type
# Proper IPv6 examples that mean the same:
# 2001:0db8:0000:0000:0000:0000:1428:57ab
# 2001:0db8:0:0:0:0:1428:57ab
# 2001:0db8:0:0::1428:57ab
# 2001:0db8::1428:57ab
# 2001:db8::1428:57ab
# Please don't provide IPv6 address in square brackets!
# Please uncomment if the server has multiple IP interfaces.
#com.adva.fnm.option.serverIP=
#
# The IP 4 address used for snmp trap registration
# Please uncomment if the server has multiple IP interfaces.
#com.adva.fnm.option.trapsink=
# The port to listen for snmp traps when using non-default port
#com.adva.fnm.option.trapsinkport=
#
# The IPv6 address used for snmp trap registration
# Proper IPv6 examples that mean the same:
# 2001:0db8:0000:0000:0000:0000:1428:57ab
# 2001:0db8:0:0:0:0:1428:57ab
# 2001:0db8:0:0::1428:57ab
# 2001:0db8::1428:57ab
# 2001:db8::1428:57ab
# Please don't provide IPv6 address in square brackets!
# Please uncomment if the server has multiple IP interfaces and uses IP 6 protocol to communicate with NEs.
# Note that local link addresses are not supported by the network elements
#com.adva.fnm.option.trapsink.ip6=
#
# SNMP communication of the Element Manager. That is, select an snmpProviderHost IP address
# facing NM server clients.
#com.adva.fnm.option.snmpProviderHost=
#
## MTOSI ##
# The host name that will be displayed in the response in case of multiple IP interfaces
#com.adva.nlms.mediation.mtosi.hostName=
#
## SNMP-NBI ##
# In case FSP-NM is configured to send out SNMP NBI traps, the source IP will be reported
# accordingly as varbind inside of the event.
#com.adva.fnm.option.snmpNBISource=
###

# Web Server Port
# Beside others the NM client uses the Jetty web server. The default port is set to 8080,
# which is commonly used for web services and presumably not blocked in customer firewalls.
# The client will try to connect by default to 80, 8080 and 9000.
# In the case of MTOSI: Should you prefer using a different Web server port (other than the
# default value of 8080 for MTOSI), then edit this value.
# Set 'none' if server shouldn't listen on this port
com.adva.fnm.option.webserver.port=none

# Secure port used by server and the clients
# Set 'none' if server shouldn't listen on this port
com.adva.fnm.option.rest.securePort=8443

# Secure port used by server and the clients that requires mutual authentication
# Set 'none' if server shouldn't listen on this port
com.adva.fnm.option.rest.securePortWithMutualAuth=9543

#This determine if Http Proxy server should start with NMS
com.adva.nlms.mediation.server.proxy.startModule=no
#Defines Http Proxy port
#com.adva.nlms.mediation.server.proxy.port=9090

#JMS SECTION START
#jms connection properties
jms.transportProtocol=nio+ssl
jms.url=0.0.0.0
jms.brokerName=nmsServer
jms.port=33028
jms.additional.args=&socket.verifyHostName=false

#broker configuration
activemq.useJMX=true
activemq.jmx.port=33092
#JMS SECTION END

# RMI port used from Command Line Interface to trigger NM functionality.
com.adva.fnm.mediation.monitoring.commandLineInterfacePORT=33091

# Session idle time
# - Number of elapsed seconds after which idle sessions (no heart beat from
#   client) will be closed.
com.adva.fnm.option.server_timeout=300

#Polling delay until GUI client shows "disconnected from server" status.
#Default is 15s. Max is 60s.
com.adva.nlms.frontend.comm.CONNECTION_MONITORING_DELAY=15

# Max Client Connections Alarm Threshold allowed. Values greater or equal 1 are valid, default value is 20.
#com.adva.fnm.option.maxClientConnectionAlarmThreshold=20

# Max Client Connections allowed, Values greater or equal 1 are valid, default value is 20.
# === Caution! ===
# More than 60 clients are not permitted for a NM server running on Windows
#com.adva.fnm.option.maxClientConnectionAllowed=20

# Max Client Connections per user group allowed, Values between 0 to 60 are valid
# com.adva.fnm.option.maxClientConnectionAllowedPerUserGroup=
# valid format for the value:  ([a-zA-Z0-9_-]+:(60|[1-5]?[0-9]))(,[a-zA-Z0-9_-]+:(60|[1-5]?[0-9]))*
# example configuration:
# com.adva.fnm.option.maxClientConnectionAllowedPerUserGroup=Administrator:0,Configurator:7,Monitor:2,Operator:0,group_temp:59

###### Email notification options ######

# Allow using startTLS to upgrade an existing insecure connection to a secure one for SMTP server.
com.adva.nlms.common.notification.agent.smtpStartTLSEnabled=true

###### Password Change Action Manager Options ######

# Email address(es) for log receiver (comma separated)
#com.adva.fnm.option.pcaLogReceiver=

# Maximum PCA threads
#com.adva.fnm.option.pcaMaxThreadCount=10

###### Create User Action Manager Options ######
# Email address(es) for log receiver (comma separated)
#com.adva.fnm.option.cuaLogReceiver=


###### High Availability Options ######

# name of the file containing list of known sftp hosts (primary server)
com.adva.fnm.ssl.knownHosts=.ssh/known_hosts

# Automatic switch to active mode if no contact with remote active server.
# Typical delay between connection lost and server switch is 1 minute.
# Can be 'enabled' or 'disabled'. By default disabled.
com.adva.fnm.option.automaticSwitchover=disabled

# If polling (e.g. Pm polling) is allowed in standby mode
# Can be 'enabled' or 'disabled'. By default disabled.
com.adva.fnm.option.standbyPolling=disabled

# Number of attempt during connection test
com.adva.fnm.ssl.connectionAttempts=1

# Path to key file for public key method ssl connection (preferred rsa)
#com.adva.fnm.ssl.keyfile=.ssh/id_rsa
# and optional passphrase
#com.adva.fnm.ssl.passphrase=

#Full path to script ran after completed switchover to the Active state
#com.adva.fnm.option.afterSwitchoverPrimaryScript=/opt/usr/bin/primary.sh
#Full path to script ran after completed switchover to the Standby state
#com.adva.fnm.option.afterSwitchoverSecondaryScript=/opt/usr/bin/secondary.sh

# this property determines how many minutes the secondary server needs
# to unpack the received database from the primary and start
com.adva.fnm.option.HA.standbyDBStartupTimeout=5

###### Self-Monitoring Options ######

# Monitoring intervals
# Value 0 means no monitoring
# Long Term Interval for monitoring parameters (in minutes)
# Default value: 60 minutes
# Allowed values: #hours * 60 where #hours is an integer from 0 to 24
com.adva.fnm.mediation.monitoring.longTermInterval=60
# Short Term Interval for monitoring parameters (in minutes)
# Default value: 5 minutes
# Allowed values: 0, 5, 10, 15, 20, 30, 60
com.adva.fnm.mediation.monitoring.shortTermInterval=5
# Rapid Term Interval for monitoring parameters (in seconds)
# Default value: 2 seconds
# Allowed values: 1 second to 299 seconds
com.adva.fnm.mediation.monitoring.rapidTermInterval=2
# End monitoring intervals
# Monitoring options
# Starts Rapid Term Monitoring on NM server start up
# Allowed values : true or false
com.adva.fnm.mediation.monitoring.rapidStartAtSystemStartUp=false


###### Flexera settings ######
#Flexera servers uri in pattern <protocol>://<address>:<port>
#com.adva.fnm.option.flexeraServer.ipaddress=https://127.0.0.1:7071
#com.adva.fnm.option.backupFlexeraServer.ipaddress=

###### ELS from ENC GUI settings ######
#ELS GUI uri in pattern <protocol>://<address>:<port>
#com.adva.fnm.option.elsgui.ipaddress=https://127.0.0.1:8444
#com.adva.fnm.option.backupElsgui.ipaddress=


###### Authentication Access Options ######

# RADIUS #

#the authentication sequence is:
#1. Try first Radius server if configured
#2. Try second Radius server if configured
#3. Try third Radius server if configured
#4. Authenticate locally
# It is possible to configure radius timeouts for both primary and backup radius server.
# Total value of timeouts should not be bigger then 60 s.

# 1st RADIUS server IP address or host name
#com.adva.fnm.option.radiushost=
# RADIUS server port
#com.adva.fnm.option.radiusport=1812
# RADIUS server timeout in seconds - if not set default of 8s is used
#com.adva.fnm.option.radiustimeout=8

# 2nd RADIUS server IP address or host name
#com.adva.fnm.option.radiushost2=
# RADIUS server port
#com.adva.fnm.option.radiusport2=1812
# RADIUS server timeout in seconds - if not set default of 8s is used
#com.adva.fnm.option.radiustimeout2=8

# 3rd RADIUS server IP address or host name
#com.adva.fnm.option.radiushost3=
# RADIUS server port
#com.adva.fnm.option.radiusport3=1812
# RADIUS server timeout in seconds - if not set default of 8s is used
#com.adva.fnm.option.radiustimeout3=8

#RADIUS client library
#Values: axl (default), jradius
com.adva.fnm.option.radiusclient=axl

# TACACS+ #

# the authentication sequence is:
# 1. Try first Tacacs+ server if configured
# 2. Try second Tacacs+ server if configured
# 3. Try third Tacacs+ server if configured
# 4. Authenticate locally
# It is possible to configure timeouts for all Tacacs+ server.
# Total value of timeouts should not be bigger then 60 s.

# 1st TACACS+ server
# TACACS+ server IP address or host name
#com.adva.fnm.option.tacacshost1=
# TACACS+ server port
#com.adva.fnm.option.tacacsport1=49
# TACACS+ server timeout in seconds
#com.adva.fnm.option.tacacstimeout1=8

# 2nd TACACS+ server
# TACACS+ server IP address or host name
#com.adva.fnm.option.tacacshost2=
# TACACS+ server port
#com.adva.fnm.option.tacacsport2=49
# TACACS+ server timeout in seconds
#com.adva.fnm.option.tacacstimeout2=8

# 3rd TACACS+ server
# TACACS+ server IP address or host name
#com.adva.fnm.option.tacacshost3=
# TACACS+ server port
#com.adva.fnm.option.tacacsport3=49
# TACACS+ server timeout in seconds
#com.adva.fnm.option.tacacstimeout3=8

# Common TACACS+ configuration
# Authentication type, possible values: PAP, CHAP, MSCHAP, ASCII
#com.adva.fnm.option.tacacsauthentication=PAP

# LDAP #

#the authentication sequence is:
#1. Try first LDAP server if configured
#2. Try second LDAP server if configured
#3. Try third LDAP server if configured
#4. Authenticate locally

# 1st LDAP server IP address or host name
#com.adva.fnm.option.ldaphost1=
# LDAP server port, default value is 389
#com.adva.fnm.option.ldapport1=389
# LDAP server timeout in seconds, default is 8 seconds
#com.adva.fnm.option.ldaptimeout1=8
# LDAP security protocol behavior option. In case that port is 389, protocol behavior option either should be specified (StartTLS or Unencrypted),
# or StartTLS will be used
# if port is 636, LDAPS will be used
# If other port is specified, security protocol behavior option either should be specified (StartTLS, LDAPS, Unencrypted),
# or StartTLS will be used
#com.adva.fnm.option.ldapsecprot1=StartTLS

# 2nd LDAP server IP address or host name
#com.adva.fnm.option.ldaphost2=
# LDAP server port, default value is 389
#com.adva.fnm.option.ldapport2=389
# LDAP server timeout in seconds, default is 8 seconds
#com.adva.fnm.option.ldaptimeout2=8
# LDAP security protocol behavior option. In case that port is 389, protocol behavior option either should be specified (StartTLS or Unencrypted),
# or StartTLS will be used
# if port is 636, LDAPS will be used
# If other port is specified, security protocol behavior option either should be specified (StartTLS, LDAPS, Unencrypted),
# or StartTLS will be used
#com.adva.fnm.option.ldapsecprot2=StartTLS

# 3rd LDAP server IP address or host name
#com.adva.fnm.option.ldaphost3=
# LDAP server port, default value is 389
#com.adva.fnm.option.ldapport3=389
# LDAP server timeout in seconds, default is 8 seconds
#com.adva.fnm.option.ldaptimeout3=8
# LDAP security protocol behavior option. In case that port is 389, protocol behavior option either should be specified (StartTLS or Unencrypted),
# or StartTLS will be used
# if port is 636, LDAPS will be used
# If other port is specified, security protocol behavior option either should be specified (StartTLS, LDAPS, Unencrypted),
# or StartTLS will be used
#com.adva.fnm.option.ldapsecprot3=StartTLS

# LDAP Search Base
# This property shall be used to configure the distinguished name (DN) of the node within the
# Directory Information Tree (DIT) where the search for users should start.
#com.adva.fnm.option.ldapsearchbase=

# LDAP Search User
# This property shall be used to configure the distinguished name (DN) of a node within the DIT corresponding to an account with
# read access to use when initially connecting to the LDAP Server to make a search for users.
# If this property is missing or commented out, then the implementation attempt to access the directory using anonymous bind
#com.adva.fnm.option.ldapsearchuser=

# LDAP User Object Class
# This property shall be used to configure the name of the directory schema class which holds user information.
# The default value is "user". For Active Directory, use the value "user". For other LDAP servers, use the value "person".
#com.adva.fnm.option.ldapuserobjectclass=

# LDAP Login Attribute
# This property shall be used to configure the name of the directory schema attribute which holds the username value.
# It is used when forming the query to match the provided username to a user entry in the directory.
# The default value is "sAMAccountName". For Active Directory, use the value "sAMAccountName".
# For other LDAP servers, use the value "uid". In principle any valid attribute name can be used.
#com.adva.fnm.option.ldaploginattribute=

# LDAP Validate Certificate
# Defines if the certificate from the LDAP Servers is to be checked for validity against certificates previously imported
# into the ENC truststore. In order to use this option, the certificate from each LDAP server (plus any PKI Root and/or
# Subordinate certificates must be previously imported into the truststore on each ENC server.
# The default value is false.
#com.adva.fnm.option.ldapvalidatecertificate=false

# LDAP Authorization Attribute
# This property shall be used to configure the name of the directory schema attribute to be used for authorization.
# The default value is "memberOf". Other valid values are "advaUserGroups", "isMemberOf".
#com.adva.fnm.option.ldapauthorizationattribute=

# LDAP Group Base Property
# This property shall be used to configure the distinguished name (DN) of the node within the
# Directory Information Tree (DIT) which is one level above where the specific directory groups for ENC
# authorization are located. This property will only be used if the feature is enabled
# and Authorization Attribute = memberOf.
#com.adva.fnm.option.ldapgroupbase=

# LDAP Group Name Prefix Property
# This property shall be used to configure a string which is used to identify relevant ENC groups matching as a prefix.
# The default value is "" indicating that the property is not to be used.
#com.adva.fnm.option.ldapgroupnameprefix=

######  Cluster Access settings - how ENC Mediation clients are accessing the Docker containers cluster node ####
com.adva.nlms.mediation.rest.cluster.access.host=
com.adva.nlms.mediation.rest.cluster.access.port=8093
#additional supported cluster type: K8sIngress, K8sNodePort, K8sInternalRevProxy
com.adva.nlms.mediation.rest.cluster.type=Swarm

#EOD evolution property is set to true for MOD by installer
com.adva.nlms.eod.evolution.enabled=false

###### Oscillating Alarms Suppression Options ######

# Oscillating alarms soak period
# - Event logging is inhibited after 3 occurrences of the same alarm within
#   'disableLoggingPeriod' seconds.
com.adva.fnm.option.disableLoggingPeriod=10

# Oscillating alarms blocking period
# - Event logging is enabled again after 'enableLoggingPeriod' seconds silence
#   about the oscillating alarm.
com.adva.fnm.option.enableLoggingPeriod=60

###### Scaling Options ######

# Number of simultaneous workers accepting requests from clients
com.adva.fnm.option.threadPoolSize=14

# Maximum number of simultaneous network element polling actions
#com.adva.nlms.mediation.polling.MAX_RUNNING_POLLING_TASKS=30

#Maximum number of PMO for short term interval
#com.adva.nlms.mediation.performance.watchdog.max15minPmo=50000

#Maximum number of PMO for long term interval
#com.adva.nlms.mediation.performance.watchdog.max24minPmo=200000

#Automatic disable of PM collection after number of PMO exceeded
com.adva.nlms.mediation.performance.watchdog.olp=true

###### Internal Options ######

# WARNING: Options below should not be modified unless advised by ADVA support!

#Maximum waiting time for initializing connection to DB server (in minutes)
com.adva.nlms.mediation.common.persistence.initDbCOnnectionTimeout=15

# Enable event counter recalculation on server startup (true/false)
# - To be reset to false after server has been started.
com.adva.fnm.option.recalculateCounter=false

# Maximum amount of events, which are queued for processing. When
# this number is reached, all events are discarded.
com.adva.nlms.mediation.evtProc.maxEventQueueSize=10000

# Properties for handling event processing suspension. In order to keep the server responsive in
# case of a big trap rate, the processing is suspended based on the processing queue level. There are three stages.
# Stage 1: blocking of live-traps of most chatty NEs
# Stage 2: blocking of live- and regenerated traps of most chatty traps
# Stage 3: blocking of all traps

# defines the upper and lower thresholds of the 3 protection stages (in percent)
com.adva.nlms.mediation.evtProc.EventQueueThresholdsStage1=50,30
com.adva.nlms.mediation.evtProc.EventQueueThresholdsStage2=60,40
com.adva.nlms.mediation.evtProc.EventQueueThresholdsStage3=70,50
# defines the suspend/resume interval for stage 1 and 2 (in seconds)
com.adva.nlms.mediation.evtProc.EventQueueSuspendResumeInterval=30,10
# defines the ratio of affected NEs for suppression during stage 1 and 2 (in percent)
com.adva.nlms.mediation.evtProc.EventQueueSuspendedRatio=30

# Properties for handling SNMP trap flood
# if number of snmp traps received from one ne exceeded _MaxTrapCountPerSample_ in every
# _SamplePeriod_ seconds for last _TotalSampleTime_ seconds, system recognizes it as trap flood

#if SNMP trap flood mechanism is enabled (default value = true)
com.adva.nlms.mediation.evtProc.TrapFloodDetectorEnabled=true
# number of traps per second which is considered as trap flood
com.adva.nlms.mediation.evtProc.TrapFloodSampleThreshold=5
# Length of sample period in seconds
com.adva.nlms.mediation.evtProc.TrapFloodSamplePeriodTime=10
# Number of consecutive sample periods
com.adva.nlms.mediation.evtProc.TrapFloodSamplePeriodAmount=18


#Maximum number of events to start resync.
com.adva.nlms.mediation.config.EVENT_RESYNC_THRESHOLD=100
com.adva.nlms.mediation.config.EVENT_RESYNC_THRESHOLD_F8=300
com.adva.nlms.mediation.config.EVENT_RESYNC_THRESHOLD_FSP150CM=300
com.adva.nlms.mediation.config.EVENT_RESYNC_THRESHOLD_HN4000=300
com.adva.nlms.mediation.config.ALARM_RESYNC_THRESHOLD_EGM=50
com.adva.nlms.mediation.config.EVENT_RESYNC_THRESHOLD_EGM=10
com.adva.nlms.mediation.config.EVENT_RESYNC_THRESHOLD_OSA540x=50
# the maximum event log size the user can set in the "Server Preferences/Event Log" dialog (default is 200,000 events)
com.adva.nlms.mediation.event.maxEventLogSize=200000

# FSP 500, Juniper - Keep Alive Polling
# Inventory, Configuration and Status update are implicitly invoked from Keep Alive polling but
# (for performance reasons) only every n-th execution of Keep Alive polling. The parameter n defaults to:
# A value of -1 disables the corresponding polling action ie., the polling action is switched off.
# A value of "" indicates default and the corresponding values will be overridden
# For FSP5000 ::::::    A value of "" defaults to Status update :: 3 (15 min) , Configuration update :: 72 (6h) , Inventory update :: 288 (24h)
# For Juniper ::::::    A value of "" defaults to Status update :: 72 (6h) , Configuration update :: 288 (24h) , Inventory update :: 1152 (4 days)
#com.adva.nlms.mediation.config.nThExecutionForStatusPolling=
#com.adva.nlms.mediation.config.nThExecutionForConfigurationPolling=
#com.adva.nlms.mediation.config.nThExecutionForInventoryPolling=

# If enabled, Oper-state change notifications of Data Services will be send to the NBI
com.adva.nlms.mediation.event.sendServiceOperStateChangeNotifications=no
com.adva.nlms.mediation.event.sendServiceOperStateChangeAlarms=no
com.adva.nlms.mediation.event.storeServiceOperStateChangeAlarms=no

# Properties to filter out Alarms and/or Events on the Trap/CSV-NBI. Any matching value in brackets is filtered out. Examples: Severity[WN,I]  Severity[I]
#   Severity values: CR, MJ, MN, WN, I
#com.adva.nlms.mediation.event.SnmpNbiAlarmFilter=Severity[I]
#com.adva.nlms.mediation.event.CsvNbiAlarmFilter=Severity[I]
#com.adva.nlms.mediation.event.SnmpNbiEventFilter=Severity[I]
#com.adva.nlms.mediation.event.CsvNbiEventFilter=Severity[I]

#If enabled, then as part of the initialization of the event module, before any new events are processed
#from the NEs, the complete set of standing alarms would be re-written to the eventlog.csv file.
com.adva.nlms.mediation.event.initCSVLogOnStartup=no

#this option locates the insertion of the Line Break at the start-of-line (no, default) or at the end-of-line (yes)
com.adva.nlms.mediation.event.CSVLogLineBreakAtEOL=no

# Port which is used by an OSS client to trigger the Alarm NBI synchronization. The function is disabled if the port is not specified.
#com.adva.nlms.mediation.event.syncAlarmsListenerPort=

com.adva.nlms.mediation.event.notification.allowExternalScripts=false

#If enabled and set to true, opening Fiber Assurance Measurement Details dialog is disabled
#com.adva.fnm.option.hideFAMDetails=false

# NE Versions Checking feature switch
com.adva.unsupported.ne.versions.check.enabled=false

###### GUI Options ######

##Welcome text for login dialog
#com.adva.fnm.option.server_welcome_text=Authorized Access Only. This is a private system. Unauthorized access or use may lead to prosecution.

##Post log-in text to be shown after UI is launched
# Insert new line (\n).
#com.adva.fnm.option.server_postLogonText=FSP Network Manager \
#\n \
#\n Log out immediately \
#\n if you are not an authorized user!

#Define the format date to be used on client for presentation any date
#The following applicable variants can be used:
#YYYY-MM-dd   -> 2014-07-21
#dd.MM.YY     -> 21.07.14
#dd MMM YY    -> 21 Jul 14
#dd MMM YYYY  -> 21 Jul 2014
#M/d/YY       -> 7/21/14
#MM/d/YY      -> 07/21/14
#MM/d/YYY     -> 07/21/2014
#com.adva.fnm.option.date_format=YYYY-MM-dd

#Command for launching CLI for NE under Windows
#com.adva.fnm.security.CLI_WINDOWS=cmd /K start telnet
#Command for launching SSH CLI for NE under Windows
#com.adva.fnm.security.ssh.CLI_WINDOWS=C:\\Program Files (x86)\\PuTTY\\putty.exe
#Command for launching CLI for NE under Linux
#com.adva.fnm.security.CLI_LINUX=/usr/bin/xterm -e /usr/kerberos/bin/telnet
#Command for launching SSH CLI for NE under Linux
#com.adva.fnm.security.ssh.CLI_LINUX=/usr/bin/xterm -e /usr/kerberos/bin/putty

# Devices that are to use the insecure Telnet client when accessing
# the command line interface.
#com.adva.fnm.option.useCLIOverTelnet=JUNIPER_MX

#Command for launching  for Browser under Windows
#com.adva.fnm.security.browser_WINDOWS=
#Command for launching browser for NE under Solaris
#com.adva.fnm.security.browser_SOLARIS=
#Command for launching browser for NE under Linux
#com.adva.fnm.security.browser_LINUX=

#Command for launching  for PDF under Windows
com.adva.fnm.security.pdf_WINDOWS=C:\\Program Files (x86)\\Adobe\\Acrobat Reader DC\\Reader\\AcroRd32.exe
#Command for launching PDF for NE under Solaris
#com.adva.fnm.security.pdf_SOLARIS=
#Command for launching PDF for NE under Linux
#com.adva.fnm.security.pdf_LINUX=

# The maximum number of label characters on the map
com.adva.fnm.option.maxMapLabelLength=100

# Trapsink registration duration
#
# 0 - trapsink disabled
# 1 - duration1hour(1)
# 2 - duration1day(2) (default value)
# 3 - duration3days(3)
# 4 - duration1week(4)
# 5 - duration1month(5)
# 6 - unlimited(6)
#com.adva.fnm.option.trapsink.aging=2

###### Security Options #####

# The name of the user used to login to NE in the fallback case or SSO.
#com.adva.fnm.option.FallbackNEUserID=

# LCT write access fallback solution
# if true then FallbackNEUserID must be set!
com.adva.fnm.option.FallbackPasswordManagement=false

# Single-Sign-On (SSO) via NE Fallback Password
# if true then FallbackNEUserID must be set!
com.adva.fnm.option.SSOviaFBP=false

# Single-Sign-On (SSO) via Ad Hoc Local NE Account
com.adva.fnm.option.SSOviaAHA=false
# Given in milliseconds. Period while which SNMP agent on NE need to rise up. By default we set it to 2 seconds(2000 milis)
com.adva.nlms.mediation.config.neconfig.ssoaha.delayAfterAdHocUserCreation=2000

# Disable WEB UI SSO login procedure per NE Type
#com.adva.fnm.option.ssoDisabled.device.types=

# FTP password max length
# Values up to 64 are acceptable. Any value greater than 64 will be reset to 64
#com.adva.fnm.option.maxFtpPasswordLength=64

# Enabling/Disabling authorization of rest calls on server side. Enabled by default.
# com.adva.fnm.security.authorization.aspect=enabled

###### Performance Monitoring Options #####

#how many days should be CSV files kept before they are deleted
com.adva.nlms.mediation.performance.CSVvalidTime=3

#Delay in milliseconds between performance monitoring requests in case of 150CC devices
com.adva.nlms.mediation.neComm.150ccSnmpDelay=120
#Delay in milliseconds between performance monitoring requests in case of F7 devices
#com.adva.nlms.mediation.neComm.F7SnmpDelay=10

###### Backup Options #####

#This property specifies how many of database backup files will be made.
# values>= 1 are valid
com.adva.fnm.option.databasebackupfilesnumber=4

# how long lasts long transaction
com.adva.nlms.mediation.common.persistence.LONG_RUNNING_TRANSACTION_TIMEOUT=4000

# how long lasts long context
com.adva.nlms.mediation.common.persistence.LONG_RUNNING_CONTEXT_TIMEOUT=10000

# how often check for very long open context
com.adva.nlms.mediation.common.persistence.VERY_LONG_RUNNING_OPEN_CONTEXT_TIMEOUT=60000

# enable/disable persistence monitoring
com.adva.nlms.mediation.common.persistence.persistenceMonitoringEnabled=false

# used connection pool library: c3p0 / bonecp / hikaricp (default: bonecp)
com.adva.nlms.mediation.common.persistence.CONNECTION_POOL_LIBRARY=hikaricp

# time between two Heart Beat events (in seconds) which are sent on the SNMP-NBI and CSV-NBI
# can be configured with minimum value of 5 seconds
# com.adva.fnm.option.HeartBeatInterval=300
# com.adva.fnm.option.HeartBeatInterval.CSV_NBI=300
# com.adva.fnm.option.HeartBeatInterval.SNMP_NBI=300
# com.adva.fnm.option.HeartBeatInterval.MTOSI_NBI=300


# timeout for SW Upgrade wait methods override ( in milliseconds ).
# [classname].[wait method name].timeout=1000
#SWUpgradeFSP_R7.installWait.timeout=800000

###### TCA Monitoring Options ######

# Delay in seconds that is applied at the 15-minute boundary before TCAs raised
# during the previous 15-minute interval are cleared
com.adva.nlms.mediation.thresholdCrossingAlert.tcaClearDelay=30

# Boolean property that indicates whether latency-related TCAs are detected
# using 'parameterId' value in internal events. If the value is set to 'false'
# 'newStringValue' property is used to detect latency-related TCAs
com.adva.nlms.mediation.thresholdCrossingAlert.tcaDetectionByParamId=false

###### Miscellaneous Options ######
#
# Disables the client updater regarding updates. When set to true the server
# version check is skipped and the installed client is started. This allows
# the use of an existing GUI with a patched server.
com.adva.fnm.option.disableClientUpdates=false

#Switches on/off use of the host name.
#Value "true" means that host name is used, "false" that it is not.
com.adva.fnm.option.iphostnameenabled=true

#maximum number of network elements which single inventory report can handle
com.adva.nlms.mediation.report.NeCountInventoryThreshold=200

#maximum number of alarms in a single report
com.adva.nlms.mediation.report.AlarmCountThreshold=3000

#Separator used in CSV files.
com.adva.fnm.option.CSVSeparator=|

#This property specifies the number of csv report files that can be created in total
# values>= 1 are valid
com.adva.nlms.mediation.report.keptfilesnumber=4
#Property indicates the number of manual report files of each type that will be stored in filesystem locations.
#value = 0 indicates automatic reports deletion is disabled
com.adva.nlms.mediation.report.keptfilesnumber.manual=0

#maximum pages of performance report
com.adva.nlms.mediation.report.performance.PmReportPagesLimit=1000

# Report files external storage option. If true reports are
# gererate into specified paths.
com.adva.nlms.mediation.report.reportExternalStorage=false

# Specified external paths for manual reports. For example in Windows: C:\\reports\\inventory .
#com.adva.nlms.mediation.report.manualInventoryReportPath=
#com.adva.nlms.mediation.report.manualFaultReportPath=
#com.adva.nlms.mediation.report.manualPerformanceReportPath=
#com.adva.nlms.mediation.report.manualSecurityReportPath=

# Specified external paths for scheduled reports. For example in Windows: C:\\reports\\inventory .
#com.adva.nlms.mediation.report.scheduledInventoryReportPath=
#com.adva.nlms.mediation.report.scheduledTopologyReportPath=
#com.adva.nlms.mediation.report.scheduledSyncPerformanceReportPath=
#com.adva.nlms.mediation.report.scheduledBandwidthUtilizationReportPath=
#com.adva.nlms.mediation.report.scheduledResourceReportPath=var/resources

#File name (with %DATE_TIME% pattern which will be replaced by current date_time) to regular report
com.adva.nlms.mediation.neResources.csv.NE_RESOURCES_REGULAR_REPORT_FILE_PATTERN=Resource_%DATE_TIME%.csv

#How many days report files should retain
com.adva.nlms.mediation.neResources.csv.NE_RESOURCES_REGULAR_REPORT_DAYS_TO_RETAIN_FILES=10

#Size of single report file in log4j format
com.adva.nlms.mediation.neResources.csv.NE_RESOURCES_REGULAR_REPORT_MAX_FILE_SIZE=50MB

#Enables the transfer PM, Inventory and Sync CSV reports to a SFTP server
com.adva.nlms.mediation.CSV_FILE_TRANSFER=false

#To support single or multiple Proxy Nodes IP address/es setting
#Valid IPv4 address list format is: A.B.C.D,E.F.G.H,W.X.Y.Z (Each IPv4 should be separated by COMMA)
#CP Policy can be applied to proxy nodes with 16.1.1 software version or higher
com.adva.nlms.mediation.sm.prov.cp.CP_POLICY_PROXY_NODES_IP=

#how many seconds SM should wait for start monitoring equalization after initiate the action
com.adva.nlms.mediation.sm.prov.cp.waitForMonitorEqualizationTimeInSecs=2

#how many seconds SM should wait for equalization to complete for each CNX
com.adva.nlms.mediation.sm.prov.cp.waitForEqualizationTimeInSecs=900

#To Enable Locked Links feature set this value to true
com.adva.nlms.mediation.sm.prov.cp.LOCKED_LINKS_ENABLED=false

# Property switch for XRO method and lifetime diversity
com.adva.nlms.mediation.sm.prov.cp.CCCP_LIFETIME_DIVERSITY_ENABLED=true

# Property to enable using CP REST to provide a list of paths during pre path computation during service creation
com.adva.nlms.mediation.sm.prov.cp.UseCPRestForPrePathComputation=yes

# Property to limit the number of paths displayed for selection during pre path computation during service creation
com.adva.nlms.mediation.sm.prov.cp.MaxNumberOfComputedPaths=5

# if set the top-level connection inherits the service name with the given suffix
#com.adva.nlms.mediation.sm.DigitalSignalSuffix=_DS
#com.adva.nlms.mediation.sm.EthernetDigitalSignalSuffix=_DS

# Property to define the template to generate connection names
# You can use any combination of placeholders. The default is: ${layer}-${timestamp}
# The following placeholders are supported:
#   ${layer}: layer protocol; ${a.ne}: start NE name; ${a.cp}: start TP name; ${z.ne}: end NE name; ${z.cp}: end TP name; ${timestamp}: epoch time in ms at creation
com.adva.nlms.mediation.sm.ServiceNameTemplate = ${layer}: ${a.ne}/${a.cp} --- ${z.ne}/${z.cp}
com.adva.nlms.mediation.eth.EthServiceNameTemplate = ${layer}: ${servicename}
# Previous setting:
#com.adva.nlms.mediation.sm.ServiceNameTemplate = ${layer} ${a.ne} ${a.cp} ${z.ne} ${z.cp}

# define bandwidth usage thresholds by percentage points, e.g. BANDWIDTH_USAGE_LOW=25, BANDWIDTH_USAGE_HIGH=75,
# then bandwidth usage: low (0 - 25); normal (26 - 74); high (75 - 99); full (100)
com.adva.nlms.common.visual.BANDWIDTH_USAGE_LOW=25
com.adva.nlms.common.visual.BANDWIDTH_USAGE_HIGH=75

#Max size of a template file used in Ethernet Config File Manager
com.adva.nlms.mediation.ethNEConfig.maxTemplateSizeInKB=1024

# If this property is set to 'true' NMS will assume that all F7 serial numbers start with 'LBADVA' instead of 'FA'.
# Serial numbers will be updated during server startup if this property changes and serial numbers scanned from
# F7 devices will be altered accordingly.
#com.adva.nlms.mediation.config.fsp_r7.useAdvaSpecificSerialNumbers=true


# Enabled setting and receiving changes of a shelf property: location (alias)
com.adva.nlms.mediation.config.shelfLocationInfoSettable=false

#com.adva.nlms.mediation.housekeeping.swupgrade.MAX_PARALLEL_THREADS=1

com.adva.nlms.mediation.housekeeping.transferclient.timeout=5
com.adva.nlms.mediation.neComm.kap.trapsink.BulkTrapEnabled=true
#If this property is set to 'true' NMS will check if Master Shelf Serial number has change and mark NE as 'dirty' in database. If the NE is
#'dirty' it will refresh current SNMPv3 user to maintain the communication with NE.
com.adva.nlms.mediation.config.fsp_r7.polling.CheckMasterShelfSerialNumberAndRefreshNeUser=true

# NI controller
com.adva.nlms.mediation.sm.prov.ni.controller=false
# Property to limit the number of paths returned by NI during pre path computation during service creation
com.adva.nlms.mediation.sm.prov.ni.NumberOfPathsForPrePathComputation=5
# Timeout in seconds for waiting NI notifications
com.adva.nlms.mediation.sm.prov.ni.WaitForNiNotificationInSecs=300

# Control whether the validation of certificates of servers that the ENC mediation connects to, is on or off.
# The supported areas are:
# geoserver,els,fne,cpc,rproxy,gnss_rproxy
# For areas that are not included in the property value below, the default is off (no verification is performed)
com.adva.nlms.mediation.http.client.certs.verification=geoserver:off,els:off,fne:off,cpc:off,rproxy:off,gnss_rproxy:off

# Specifies if the planner data collection should be active.
com.adva.fnm.mediation.planner.dataCollection=false

###### SNMP SBI Options ######

# Minimum time [ms] between sending two consecutive requests to the SNMP provider
com.adva.nlms.mediation.sbi.snmp.shaper.min.delay.millis=1


###### Sync Manager Options ######

# Sync Discovery events Queue worker size
com.adva.nlms.mediation.synchronization.discovery.SyncDiscoveryQueueSize=100000

# NCD to subnetwork
com.adva.nlms.mediation.synchronization.ncd.auto.align.with.subnet=false
com.adva.nlms.mediation.synchronization.ncd.auto.align.with.subnet.separator=/

# TCP connection settings for OSA3230B device
com.adva.nlms.mediation.synchronization.osa3230b.tcp.connection.port=8000
com.adva.nlms.mediation.synchronization.osa3230b.tcp.connection.timeoutMS=2000
com.adva.nlms.mediation.synchronization.osa3230b.tcp.read.timeoutMS=2000

######  Sync Assurance Access settings - how ENC Mediation Sync Assurance clients are accessing the Docker containers cluster node ####
# if not set will take configuration from general Cluster Access settings properties
com.adva.nlms.mediation.synchronization.assurance.cluster.host=
com.adva.nlms.mediation.synchronization.assurance.cluster.port=
#supported cluster type: Swarm, K8sIngress, K8sNodePort, K8sInternalRevProxy
com.adva.nlms.mediation.synchronization.assurance.cluster.type=

# GNSS sub map will be refreshed each GnssSubMapRefreshPeriod seconds (10 seconds is the minimum)
com.adva.fnm.option.GnssSubMapRefreshPeriod=10
# GNSS sub map maximum time range in days
com.adva.fnm.option.GnssHistoryMaxTimeRangeDays=2
# GNSS Receivers Status Report maximum time range in days (1..300)
com.adva.fnm.option.GnssReceiversStatusReportMaxDays=30
# NTP Server Activity View will be refreshed each NtpActivityViewRefreshPeriod seconds. Default=60 (60..3600)
com.adva.fnm.option.NtpActivityViewRefreshPeriod=60

# GNSS server configuration
com.adva.nlms.mediation.synchronization.gnss.assurance.use.live.collector.for.live.views=true
com.adva.nlms.mediation.synchronization.gnss.assurance.http.connect.timeout=3000
com.adva.nlms.mediation.synchronization.gnss.assurance.http.read.timeout=20000
com.adva.nlms.mediation.synchronization.gnss.assurance.machine.learning.alarms.enabled=false

# GNSS NE Blocklist
com.adva.nlms.mediation.synchronization.gnss.blocklist.maxNumNEs=1000

# RCA Analysis - use NMS time or NE time
com.adva.fnm.option.RcaAnalysisUseAlarmNmsTime=true

# TPA configuration
com.adva.nlms.mediation.synchronization.timephase.assurance.http.connect.timeout=3000
com.adva.nlms.mediation.synchronization.timephase.assurance.http.read.timeout=120000

# SNT configuration
com.adva.nlms.mediation.synchronization.snt.http.connect.timeout=3000
com.adva.nlms.mediation.synchronization.snt.http.read.timeout=300000

# NTP
# NTP Monitoring Data Time To Live In Days - Define how long the NTP monitoring data is kept in the database. Default=1 (1..5)
com.adva.nlms.mediation.synchronization.ntp.ntpDataTtlInDays=1
# Retry count - sets the number of NTP raw data collection retries for a specific file on a specific NE ID and NTP Clock ID. Default=3 (1..5)
com.adva.nlms.mediation.synchronization.ntp.collection.failed.retryCount=3
# Remove old files - controls whether to delete old and/or invalid NTP raw data files from the file server or not. Default=true
com.adva.nlms.mediation.synchronization.ntp.collection.cleanup.removeFiles=true
#NTP Monitoring Server Activity API Filter Extreme Points Standard Deviation Threshold Factor. Default=0 (0..20)
com.adva.nlms.mediation.synchronization.ntp.server.activity.api.filter.extreme.points.sd.threshold.factor=0

# TPA View - data polling period in seconds (minimum 1 sec)
com.adva.fnm.option.TestResultsPollingPeriodInSec=3

# TPA Online QM (minimum is 100)
com.adva.fnm.option.OnlineQM.maxSelectionForAssign=1000

# Sync Graphs
com.adva.fnm.option.syncNetGraph.maxNEsForLayout=50

#com.adva.nlms.mediation.config.fsp_xg_mrv.neComm.delegateDiscoveryToPV=true

# Tile Server settings
com.adva.fnm.option.TileServerLayer.street=https://{s}.tile.osm.org/{z}/{x}/{y}.png
com.adva.fnm.option.TileServerAttribution.street=&copy; <a href="http://osm.org/copyright">OpenStreetMap</a> contributors
com.adva.fnm.option.maxZoom.street=19
com.adva.fnm.option.TileServerLayer.satellite=https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}
com.adva.fnm.option.TileServerAttribution.satellite =&copy; <a href="http://www.esri.com/">Esri</a>, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community
com.adva.fnm.option.maxZoom.satellite=19

# Health Center setting
#In Linux what keys from free -b command to take to calc the free PhysicalMemory coma separated default ex:(free,buff/cache)
com.adva.fnm.option.HealthCenter.LinuxFreeMemoryColumnsNames=free,buff/cache
#In Linux what line from free -b command to take to calc the free PhysicalMemory default ex:(Mem:)
com.adva.fnm.option.HealthCenter.LinuxFreeMemoryDataPrefix=Mem:

#Linux Monitored Disk Partitions coma separated ex:(/,/opt)
com.adva.fnm.option.HealthCenter.LinuxMonitoredDiskPartitions=/,/opt/adva,/var/lib/docker
#WindowsMonitoredDiskPartitions coma separated ex:(c,d)
com.adva.fnm.option.HealthCenter.WindowsMonitoredDiskPartitions=c
# min value 1 max value 60
com.adva.fnm.option.HealthCenter.SampleRateInMinutes=1
# min value 1 max value 23
com.adva.fnm.option.HealthCenter.GaugeMonitoredHours=1
# min value 30 max value 365
com.adva.fnm.option.HealthCenter.DBRetentionDays=120

# Health center UtilizationThreshold value  0 - 100
com.adva.fnm.option.HealthCenter.CpuUtilizationThreshold=85
com.adva.fnm.option.HealthCenter.PhysicalMemoryUtilizationThreshold=85
com.adva.fnm.option.HealthCenter.SwapMemoryUtilizationThreshold=85
com.adva.fnm.option.HealthCenter.PageVsPhysicalMemoryThreshold =20

# apply for cpu/memory/disk
# Health center UI DegradedThreshold - valid range(15..99). A value set higher than 99 will be reset to 99. A value set lower than 15 will be reset to 15.
# Health center UI UnHealthyThreshold - valid range(0..84). A value set higher than (DegradedThreshold - 15) will be reset to (DegradedThreshold - 15). A value set lower then 0 will be reset to 0.
com.adva.fnm.option.HealthCenter.CpuDegradedThreshold=70
com.adva.fnm.option.HealthCenter.CpuUnHealthyThreshold=30
com.adva.fnm.option.HealthCenter.DiskDegradedThreshold=30
com.adva.fnm.option.HealthCenter.DiskUnHealthyThreshold=15
com.adva.fnm.option.HealthCenter.MemoryDegradedThreshold=70
com.adva.fnm.option.HealthCenter.MemoryUnHealthyThreshold=30

# Health center view auto refresh - in seconds (minimum is 60 seconds)
com.adva.fnm.option.HealthCenter.ViewRefreshPeriodInSec=300

#SNMP communication properties
# max value of snmp engineId cache in SNMP4J library, used for V3 communication
com.adva.nlms.mediation.neComm.snmp.maxEngineIdCacheSize=80000

com.adva.nlms.mediation.mltopologymodel.startModule=true
com.adva.nlms.mediation.mltopologymodel.channel.mismatch.enabled=true

#SNMP settings overwrite REST settings
#Boolean
#Default value is true
#If true then any change to SNMPv3 credentials is applied to REST
com.adva.fnm.option.UseSnmpForRest=true

# com.adva.nlms.sdn.enabled=true

# Start ENC Web UI
# com.adva.nlms.client.webui.startModule=true

# SNMP dying gasp disable configuration
#com.adva.nlms.medation.config.dyingGaspDisabled.device.types = FSP 150CC-XG210

# This is a property to specify user list to ignore the auto logout settings
# The comma separated user (case sensitive) list
# like Admin, admin are two different users
#com.adva.fnm.security.auto_logout_user_disable=

#Disk Space Monitoring properties
#Disk Space Low property value must be higher than Disk Space Critical property value.

#Disk Space Polling Frequency
#Unsigned Integer (indicating a value in hours)
#The default value is 24 (hours).
#The allowable range is 1 hour through 168 hours (1 week).
#Specifying a value of 0 shall disable the disk space monitoring feature altogether.
com.adva.fnm.option.diskSpacePollingFrequency=24

#Disk Space Low Threshold
#Unsigned Integer (indicating a percentage from 0..99)
#The default value is 30 (percent).
#Specifying a value of 0 shall disable the alarm related this threshold.
com.adva.fnm.option.diskSpaceLowThreshold=30

#Disk Space Critical Threshold
#Unsigned Integer (indicating a percentage from 0..99)
#The default value is 15 (percent)
#Specifying a value of 0 shall disable the alarm related this threshold.
com.adva.fnm.option.diskSpaceCriticalThreshold=15

#Disable functionality of discovery snmp engineId for any network element which uses SnmpV3
#for communication. This functionality is needed when ENC communicates with devices using the same
#user name but with different passwords.
#User also can change default timeout for this operation using following property: com.adva.nlms.mediation.neComm.snmp.discoveryOfSnmpEngineIdTimeoutMs
com.adva.nlms.mediation.neComm.snmp.disableDiscoveryOfSnmpEngineId=true

#Property to skip ECH entities deletion on NE
#com.adva.nlms.mediation.sm.prov.TapiSkipEchEntitiesDeletionOnNE=true

# Time delay expressed in seconds for the hist segment update process trigger (default value 24h -> 86400)
com.adva.apps.efd.fiberdirector.histsegment.PERIOD_TIME=86400

com.adva.nlms.mediation.NE_CBM_Configuration_Backup_Delta=true

services.job-manager.environment.KAFKA_JOB_TOPIC=v1.cor.jm.jobs

com.adva.nlms.mediation.config.ResolvingMODataIntegrityEnabled=true
############################################################
#  PLEASE DO NOT ADD ANY PROPERTIES OR TEXT BELOW THIS LINE  #
#############################################################

