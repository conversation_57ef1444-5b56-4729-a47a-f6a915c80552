<?xml version="1.0" encoding="UTF-8"?>
<!--
  -  Copyright 2023 Adtran Networks SE. All rights reserved.
  -
  -  Owner: adaemmig
  -->

<device xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="MI 4040S" id="10900" sysOid="*******.4.1.12765.1.2.1">
	<configuration>
		<LCT>
			<web>
				<protocol>http</protocol>
			</web>
		</LCT>
	</configuration>
	<operation>
  <event-list>
    <network-event>
      <name>Generic Alarm</name>
      <short-name>ALARM</short-name>
      <message>{1}</message>
      <severity-no-service>WARNING</severity-no-service>
      <help/>
      <service-affecting/>
      <vb-number-value>5</vb-number-value> <!-- miAlmTableCondtype -->
      <vb-number-entity>2</vb-number-entity> <!-- AID not found -->
      <vb-number-severity>4</vb-number-severity> <!-- correct: covaroAlmNotifCode-->
      <trap>
            <name>miNotifyFaultAlm</name>
            <number>4</number>
            <varbind>
              <name>miNotifyFaultAlm</name>
              <oid>.*******.4.1.12765.3.4.4.1.1.5</oid>
              <value id="1">Hard Reset</value>
              <value id="2">Soft Reset</value>
              <value id="3">Card type mismatch</value>
              <value id="4">Module missing or removed</value>
              <value id="5">Config change</value>
              <value id="6">Scheduled PM Reports are inhibited</value>
              <value id="7">My clock is off</value>
              <value id="8">Romote card clock off</value>
              <value id="9">DS2155 receive LOS or framer RCL</value>
              <value id="10">ACS8515 free run, holdover, locked or phase lost</value>
              <value id="11">Remote MTC present</value>
              <value id="12">Remote MTC not present</value>
              <value id="13">Remote MTC active</value>
              <value id="14">Remote MTC not active</value>
              <value id="15">Local Power fault</value>
              <value id="16">M48V fault</value>
              <value id="17">Fan fault</value>
              <value id="18">Phy link down</value>
              <value id="19">Card down event</value>
              <value id="20">Card up event</value>
              <value id="21">Laser Failure, Shutdown, Wavelength/Power/Modulator Out of Lock, etc</value>
              <value id="22">Laser Temperature out of range</value>
              <value id="23">Laser High Bias</value>
              <value id="24">Wavelength out of range</value>
              <value id="25">Receiver Power Out of Range</value>
              <value id="26">Starting RX turnup</value>
              <value id="27">Ending RX turnup success</value>
              <value id="28">Ending RX turnup fail</value>
              <value id="29">Turnup Failed</value>
              <value id="30">DX/MX FPGA, MUX/DMUX PLL Not Locked, Mux failed to lock</value>
              <value id="31">Clock Module Not Locked, RX EDFA Bias out of range, etc</value>
              <value id="32">PRBS Errors</value>
              <value id="33">Dmux rotation failed, TX EDFA Loss Of Light</value>
              <value id="34">Modulation power not sufficient</value>
              <value id="35">TX/RX Temp out of range</value>
              <value id="36">TX Ouput Power out of range</value>
              <value id="37">DW Alarm Indication Signal</value>
              <value id="38">DW Loss of Frame</value>
              <value id="39">DW Out of Multiframe</value>
              <value id="40">DW Incoming Align Error</value>
              <value id="41">DW Trace ID MisMatch</value>
              <value id="42">DW Signal Degrade</value>
              <value id="43">DW Signal Failure</value>
              <value id="44">DW Loss of Multiframe</value>
              <value id="45">DW Out of Frame</value>
              <value id="46">DW FEC Signal Degrade</value>
              <value id="47">DW FEC Signal Failure</value>
              <value id="48">DW TX FIFO Error</value>
              <value id="49">Loss of Signal</value>
              <value id="50">Loss of Frame</value>
              <value id="51">Signal Fail BER Threshold (B1) Crossed (BER >= 1e-6)</value>
              <value id="52">Signal Fail BER Threshold (B2) Crossed</value>
              <value id="53">Signal Degrade BER Threshold (B1) Crossed (1e-6 > BER > 1e-10)</value>
              <value id="54">Signal Degrade BER Threshold (B2) Crossed</value>
              <value id="55">Line AIS</value>
              <value id="56">Line RDI</value>
              <value id="57">Xpdr configurable alarm</value>
              <value id="58">MX FPGA OOF</value>
              <value id="59">DX FPGA OOF</value>
            </varbind>
      </trap>
    </network-event>
    <network-event>
      <name>Generic Alarm</name>
      <short-name>ALARM</short-name>
      <message>{1}</message>
      <severity-no-service>WARNING</severity-no-service>
      <help/>
      <service-affecting/>
      <vb-number-value>5</vb-number-value> <!-- miPM15MinTableMontype -->
      <vb-number-entity>4</vb-number-entity> <!-- AID -->
      <trap>
        <name>miNotifyFaultPMon</name>
        <number>5</number>
        <varbind>
          <name>miNotifyFaultPMon</name>
          <oid>.*******.4.1.12765.*******.1.4</oid>
          <value id="1">ES-L</value>
          <value id="2">SES-L</value>
          <value id="3">UAS-L</value>
          <value id="4">FC-L</value>
          <value id="5">CV-L</value>
          <value id="6">ES-LFE</value>
          <value id="7">SES-LFE</value>
          <value id="8">UAS-LFE</value>
          <value id="9">FC-LFE</value>
          <value id="10">CV-LFE</value>
          <value id="11">MS-ES</value>
          <value id="12">MS-SES</value>
          <value id="13">MS-UAS</value>
          <value id="14">MS-EB</value>
          <value id="15">MS-BBE</value>
          <value id="16">SEFS-S</value>
          <value id="17">ES-S</value>
          <value id="18">SES-S</value>
          <value id="19">CV-S</value>
          <value id="20">RS-ES</value>
          <value id="21">RS-SES</value>
          <value id="22">RS-EB</value>
          <value id="23">RS-BBE</value>
        </varbind>
      </trap>
    </network-event>
    <network-event>
      <name>Generic Alarm</name>
      <short-name>Alarm</short-name>
      <message>{1}</message>
      <severity-no-service>WARNING</severity-no-service>
      <help/>
      <service-affecting/>
      <vb-number-value>5</vb-number-value> <!--  -->
      <vb-number-entity>4</vb-number-entity> <!-- AID -->
      <trap>
        <name>miNotifySurveillanceCond</name>
        <number>6</number>
        <varbind>
          <name>NotifySurveillanceCond</name>
          <oid>.*******.4.1.12765.3.4.4.1.1.5</oid>
          <value id="1">Hard Reset</value>
          <value id="2">Soft Reset</value>
          <value id="3">Card type mismatch</value>
          <value id="4">Module missing or removed</value>
          <value id="5">Config change</value>
          <value id="6">Scheduled PM Reports are inhibited</value>
          <value id="7">My clock is off</value>
          <value id="8">Romote card clock off</value>
          <value id="9">DS2155 receive LOS or framer RCL</value>
          <value id="10">ACS8515 free run, holdover, locked or phase lost</value>
          <value id="11">Remote MTC present</value>
          <value id="12">Remote MTC not present</value>
          <value id="13">Remote MTC active</value>
          <value id="14">Remote MTC not active</value>
          <value id="15">Local Power fault</value>
          <value id="16">M48V fault</value>
          <value id="17">Fan fault</value>
          <value id="18">Phy link down</value>
          <value id="19">Card down event</value>
          <value id="20">Card up event</value>
          <value id="21">Laser Failure, Shutdown, Wavelength/Power/Modulator Out of Lock, etc</value>
          <value id="22">Laser Temperature out of range</value>
          <value id="23">Laser High Bias</value>
          <value id="24">Wavelength out of range</value>
          <value id="25">Receiver Power Out of Range</value>
          <value id="26">Starting RX turnup</value>
          <value id="27">Ending RX turnup success</value>
          <value id="28">Ending RX turnup fail</value>
          <value id="29">Turnup Failed</value>
          <value id="30">DX/MX FPGA, MUX/DMUX PLL Not Locked, Mux failed to lock</value>
          <value id="31">Clock Module Not Locked, RX EDFA Bias out of range, etc</value>
          <value id="32">PRBS Errors</value>
          <value id="33">Dmux rotation failed, TX EDFA Loss Of Light</value>
          <value id="34">Modulation power not sufficient</value>
          <value id="35">TX/RX Temp out of range</value>
          <value id="36">TX Ouput Power out of range</value>
          <value id="37">DW Alarm Indication Signal</value>
          <value id="38">DW Loss of Frame</value>
          <value id="39">DW Out of Multiframe</value>
          <value id="40">DW Incoming Align Error</value>
          <value id="41">DW Trace ID MisMatch</value>
          <value id="42">DW Signal Degrade</value>
          <value id="43">DW Signal Failure</value>
          <value id="44">DW Loss of Multiframe</value>
          <value id="45">DW Out of Frame</value>
          <value id="46">DW FEC Signal Degrade</value>
          <value id="47">DW FEC Signal Failure</value>
          <value id="48">DW TX FIFO Error</value>
          <value id="49">Loss of Signal</value>
          <value id="50">Loss of Frame</value>
          <value id="51">Signal Fail BER Threshold (B1) Crossed (BER >= 1e-6)</value>
          <value id="52">Signal Fail BER Threshold (B2) Crossed</value>
          <value id="53">Signal Degrade BER Threshold (B1) Crossed (1e-6 > BER > 1e-10)</value>
          <value id="54">Signal Degrade BER Threshold (B2) Crossed</value>
          <value id="55">Line AIS</value>
          <value id="56">Line RDI</value>
          <value id="57">Xpdr configurable alarm</value>
          <value id="58">MX FPGA OOF</value>
          <value id="59">DX FPGA OOF</value>
        </varbind>
      </trap>
    </network-event>
  </event-list>
  </operation>
</device>
