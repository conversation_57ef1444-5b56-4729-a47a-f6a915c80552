<?xml version="1.0" encoding="UTF-8"?>
<!--
  -  Copyright 2023 Adtran Networks SE. All rights reserved.
  -
  -  Owner: adaemmig
  -->

<device xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="FSP-I " id="10001" sysOid="1.3.6.1.4.1.2544.1.1">
	<configuration>
		<LCT/>
	</configuration>
	<operation>
		<event-list>
			<network-event>
				<name>Loss of Synchronization</name>
				<short-name>SYNC</short-name>
				<message>Clock synchronization lost</message>
				<severity-no-service>CRITICAL</severity-no-service>
				<service-affecting/>
				<vb-number-entity>1</vb-number-entity>
				<help>Wrong bit rate or no clock detection. Either traffic with the wrong bitrate is applied  or there is a fault on the module.</help>
				<trap>
					<name>chanClockrecFail</name>
					<number>12</number>
					<varbind/>
				</trap>
			</network-event>
			<network-event>
				<name>Loss of Signal on Remote Port</name>
				<short-name>LOS-R</short-name>
				<message>Loss of signal on remote port (A)</message>
				<severity-no-service>CRITICAL</severity-no-service>
				<service-affecting/>
				<vb-number-entity>1</vb-number-entity>
				<help>Remote port receiver (port A on hot standby modules) has lost signal</help>
				<trap>
					<name>chanRecRemLOS</name>
					<number>14</number>
					<varbind/>
				</trap>
			</network-event>
			<network-event>
				<name>Loss of Signal on Local Port</name>
				<short-name>LOS-L</short-name>
				<message>Loss of signal on local port.</message>
				<severity-no-service>CRITICAL</severity-no-service>
				<service-affecting/>
				<vb-number-entity>1</vb-number-entity>
				<help>Local port receiver has lost signal.</help>
				<trap>
					<name>chanRecLocLOS</name>
					<number>15</number>
					<varbind/>
				</trap>
			</network-event>
			<network-event>
				<name>FAN Failure</name>
				<short-name>FAN</short-name>
				<message>{1}</message>
				<severity-no-service>MINOR</severity-no-service>
				<vb-number-entity>1</vb-number-entity>
				<vb-number-value>1</vb-number-value>
				<help>Fan has failed.</help>
				<trap>
					<name>fanFail</name>
					<number>16</number>
					<varbind>
						<name>fanFail</name>
						<oid>.1.3.6.1.4.1.2544.1.1.7.0.16</oid>
						<value id="501">Fan #1 failed</value>
						<value id="502">Fan #2 failed</value>
					</varbind>
				</trap>
			</network-event>
			<network-event>
				<name>Power Supply Fail</name>
				<short-name>PWR</short-name>
				<message>{1}</message>
				<severity-no-service>MINOR</severity-no-service>
				<vb-number-entity>1</vb-number-entity>
				<vb-number-value>1</vb-number-value>
				<help>Power supply or fan power supply has failed.</help>
				<trap>
					<name>psFail</name>
					<number>18</number>
					<varbind>
						<name>fanFail</name>
						<oid>.1.3.6.1.4.1.2544.1.1.7.0.18</oid>
						<value id="1">Power supply 1 failure</value>
						<value id="2">Power supply 2 failure</value>
						<value id="3">Fan power supply 1 failure</value>
						<value id="4">Fan power supply 2 failure</value>
					</varbind>
				</trap>
			</network-event>
			<network-event>
				<name>Power Supply Ok</name>
				<short-name>PWR-CLEAR</short-name>
				<message>Alarm cleared: {1} </message>
				<severity-no-service>MINOR</severity-no-service>
				<vb-number-entity>1</vb-number-entity>
				<vb-number-value>1</vb-number-value>
				<help>Clearing power supply or fan power supply alarm</help>
				<trap>
					<name>psOk</name>
					<number>19</number>
					<varbind>
						<name>fanOk</name>
						<oid>.1.3.6.1.4.1.2544.1.1.7.0.19</oid>
						<value id="1">Power supply 1 fail</value>
						<value id="2">Power supply 2 fail</value>
						<value id="3">Fan power supply 1 fail</value>
						<value id="4">Fan power supply 2 fail</value>
					</varbind>
				</trap>
			</network-event>
			<network-event>
				<name>Loss of Signal on Remote Port</name>
				<short-name>LOS-R-CLEAR</short-name>
				<message>Alarm cleared: Loss of signal on remote port (A)</message>
				<severity-no-service>CRITICAL</severity-no-service>
				<service-affecting/>
				<vb-number-entity>1</vb-number-entity>
				<help>Clearing alarm of remote port receiver (port A on hot standby modules) has lost signal</help>
				<trap>
					<name>chanRecRemNoLOS</name>
					<number>20</number>
					<varbind/>
				</trap>
			</network-event>
			<network-event>
				<name>Loss of Signal on Local Port</name>
				<short-name>LOS-L-CLEAR</short-name>
				<message>Alarm cleared: Loss of signal on local port </message>
				<severity-no-service>CRITICAL</severity-no-service>
				<service-affecting/>
				<vb-number-entity>1</vb-number-entity>
				<help>Clearing alarm local port receiver has lost signal</help>
				<trap>
					<name>chanRecLocNoLOS</name>
					<number>21</number>
					<varbind/>
				</trap>
			</network-event>
			<network-event>
				<name>Module Insertion Notification</name>
				<short-name>EQ-ADD</short-name>
				<message>New module installed</message>
				<severity-no-service>WARNING</severity-no-service>
				<vb-number-entity>1</vb-number-entity>
				<help>Hardware was added to the system</help>
				<trap>
					<name>chanHardwareAdd</name>
					<number>22</number>
					<varbind/>
				</trap>
			</network-event>
			<network-event>
				<name>Module Removal Notification</name>
				<short-name>EQ-RMVD</short-name>
				<message>Module removed</message>
				<severity-no-service>WARNING</severity-no-service>
				<vb-number-entity>1</vb-number-entity>
				<help>Hardware was deleted from the system</help>
				<trap>
					<name>chanHardwareDel</name>
					<number>23</number>
					<varbind/>
				</trap>
			</network-event>
			<network-event>
				<name>Loss of Synchronization</name>
				<short-name>SYNC-CLEAR</short-name>
				<message>Alarm cleard: Clock synchronization lost</message>
				<severity-no-service>CRITICAL</severity-no-service>
				<service-affecting/>
				<vb-number-entity>1</vb-number-entity>
				<help>Clearing alarm of wrong bit rate or no clock detection.</help>
				<trap>
					<name>chanClockrecNoFail</name>
					<number>24</number>
					<varbind/>
				</trap>
			</network-event>
			<network-event>
				<name>FAN Failure</name>
				<short-name>FAN-CLEAR</short-name>
				<message>Alarm cleared: {1}</message>
				<severity-no-service>MINOR</severity-no-service>
				<vb-number-entity>1</vb-number-entity>
				<vb-number-value>1</vb-number-value>
				<help>Clearing fan alarm.</help>
				<trap>
					<name>fanOk</name>
					<number>25</number>
					<varbind>
						<name>fanOk</name>
						<oid>.1.3.6.1.4.1.2544.1.1.7.0.25</oid>
						<value id="501">Fan 1 failed</value>
						<value id="502">Fan 2 failed</value>
					</varbind>
				</trap>
			</network-event>
			<network-event>
				<name>Loss of Signal on Remote Port B</name>
				<short-name>LOS-RB</short-name>
				<message>Loss of signal on remote port B</message>
				<severity-no-service>CRITICAL</severity-no-service>
				<service-affecting/>
				<vb-number-entity>1</vb-number-entity>
				<help>Remote port B receiver has lost signal</help>
				<trap>
					<name>chan2ndRecRemLOS</name>
					<number>26</number>
					<varbind/>
				</trap>
			</network-event>
			<network-event>
				<name>Loss of Signal on Remote Port</name>
				<short-name>LOS-RB-CLEAR</short-name>
				<message>Alarm cleared: Loss of signal on remote port B</message>
				<severity-no-service>CRITICAL</severity-no-service>
				<service-affecting/>
				<vb-number-entity>1</vb-number-entity>
				<help>Clearing alarm of remote port B receiver has lost signal</help>
				<trap>
					<name>chan2ndRecRemNoLOS</name>
					<number>27</number>
					<varbind/>
				</trap>
			</network-event>
			<network-event>
				<name>Protection Status Change Notification</name>
				<short-name>SWITCH-STAT</short-name>
				<message>{2} = {1}</message>
				<severity-no-service>WARNING</severity-no-service>
				<service-affecting/>
				<vb-number-entity>1</vb-number-entity>
				<vb-number-value>2</vb-number-value>
				<help>Hot standby converter card changes the active line</help>
				<trap>
					<name>chanChangeLine</name>
					<number>28</number>
					<varbind>
						<name>Active line</name>
						<oid>.1.3.6.1.4.1.2544.1.1.4.1.1.21</oid>
						<value id="1">A</value>
						<value id="2">B</value>
					</varbind>
				</trap>
			</network-event>
			<network-event>
				<name>Protection Configuration Change Notification</name>
				<short-name>SWITCH-CONF</short-name>
				<message>{2} is {1}</message>
				<severity-no-service>MINOR</severity-no-service>
				<service-affecting/>
				<vb-number-entity>1</vb-number-entity>
				<vb-number-value>2</vb-number-value>
				<help>Hot standby converter card changes its state</help>
				<trap>
					<name>chanChangeState</name>
					<number>29</number>
					<varbind>
						<name>Protection</name>
						<oid>.1.3.6.1.4.1.2544.1.1.4.1.1.22</oid>
						<value id="1">active</value>
						<value id="2">locked</value>
					</varbind>
				</trap>
			</network-event>
			<network-event>
				<name>Buffer Overflow</name>
				<short-name>OVFL</short-name>
				<message>Internal trap-buffer has got an overflow</message>
				<severity-no-service>WARNING</severity-no-service>
				<service-affecting/>
				<vb-number-entity>1</vb-number-entity>
				<help>Internal trap-buffer has got an overflow</help>
				<trap>
					<name>chanBufferOverflow</name>
					<number>30</number>
					<varbind/>
				</trap>
			</network-event>
		</event-list>
	</operation>
</device>
