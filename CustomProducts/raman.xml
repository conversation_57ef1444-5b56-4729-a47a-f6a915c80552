<?xml version="1.0" encoding="UTF-8"?>
<!--
  -  Copyright 2023 Adtran Networks SE. All rights reserved.
  -
  -  Owner: adaemmig
  -->

<device xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="..\src\com\adva\nlms\common\NetworkEventMap.xsd"
        name="Red-C Raman" id="15000" sysOid="1.3.6.1.4.1.22468.1">
	<configuration>
    <LCT>
      <web>
        <protocol>http</protocol>
      </web>
    </LCT>
  </configuration>
  <operation>
    <neStatus>
      <valueCritical>2</valueCritical>
			<valueMajor>6</valueMajor>
			<valueMinor>4</valueMinor>
			<valueWarning>0</valueWarning>
			<valueNormal>1</valueNormal>
    </neStatus>
    <event-list>

      
      <network-event>
        <name>Loss of Input power on the Line Interface (Backward mode Raman).</name>
        <short-name>Inp Loss (bwd)</short-name>
        <message>Loss of Input power on the Line Interface (Backward mode Raman). {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanBwdInLosAlarm</name>
          <number>1</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>
        </trap>
      </network-event>
      <network-event>
        <name>Loss of Input power on the EDFA Interface (Foreward mode Raman).</name>
        <short-name>Inp Loss (fwd)</short-name>
        <message>Loss of Input power on the EDFA Interface (Foreward mode Raman). {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanFwdInLosAlarm</name>
          <number>2</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>
        </trap>
      </network-event>
      <network-event>
        <name>Loss of Dithering on OSC(Supervisory Channel) Input power (Foreward mode Raman).</name>
        <short-name>Fwd OSC Loss</short-name>
        <message>Loss of Dithering on OSC(Supervisory Channel) Input power (Foreward mode Raman). {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanFwdOSCDitherLosAlarm</name>
          <number>3</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>
        </trap>
      </network-event>
      <network-event>
        <name>Loss of Dithering on OSC(Supervisory Channel) Input power (Backward mode Raman)</name>
        <short-name>Bwd OSC Loss</short-name>
        <message>Loss of Dithering on OSC(Supervisory Channel) Input power (Backward mode Raman). {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanBwdOSCDitherLosAlarm</name>
          <number>4</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>          
        </trap>
      </network-event>
      <network-event>
        <name>Low Input Power on the EDFA interface - only a warning (Foreward mode Raman)</name>
        <short-name>Low Edfa In</short-name>
        <message>Low Input Power on the EDFA interface - only a warning (Foreward mode Raman). {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanLowEdfaInAlarm</name>
          <number>5</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>
        </trap>
      </network-event>
      <network-event>
        <name>High Back Reflection Power Alarm</name>
        <short-name>HBR</short-name>
        <message>High Back Reflection Power Alarm. {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanHiBackRAlarm</name>
          <number>6</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>
        </trap>
      </network-event>
      <network-event>
        <name>High Line Output Power - Pumps power decreased so that combined Output Power is reduced to 27dBm or less.</name>
        <short-name>Hi Line Out</short-name>
        <message>High Line Output Power - Pumps power decreased so that combined Output Power is reduced to 27dBm or less. {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanHiLineOutAlarm</name>
          <number>7</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>
        </trap>
      </network-event>
      <network-event>
        <name>Low Line OutputPower Alarm - only a warning.</name>
        <short-name>Lo Line Out</short-name>
        <message>Low Line OutputPower Alarm - only a warning. {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanLowLineOutAlarm</name>
          <number>8</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>
        </trap>
      </network-event>
      <network-event>
        <name>Raman Amplifier's ambient temperature has reached shutdown threshold.</name>
        <short-name>Shdn ATemp</short-name>
        <message>Raman Amplifier's ambient temperature has reached shutdown threshold. {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanShdATempAlarm</name>
          <number>9</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>
        </trap>
      </network-event>
      <network-event>
        <name>Raman Amplifier's ambient temperature has reached Max Allowed Level - only a warning.</name>
        <short-name>Warn ATemp</short-name>
        <message>Raman Amplifier's ambient temperature has reached Max Allowed Level - only a warning. {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanWarnATempAlarm</name>
          <number>10</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>
        </trap>
      </network-event>
      <network-event>
        <name>One (or both) of the pumps' current has reached End Of Life threshold - only a warning.</name>
        <short-name>Pumps Eol</short-name>
        <message>One (or both) of the pumps' current has reached End Of Life threshold - only a warning. {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanPumpsEolAlarm</name>
          <number>11</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>
        </trap>
      </network-event>
      <network-event>
        <name>One (or both) of the pumps has reached Shutdown Temperature.</name>
        <short-name>Pumps Shdn Temp</short-name>
        <message>One (or both) of the pumps has reached Shutdown Temperature. {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanPumpsShdTempAlarm</name>
          <number>12</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>
        </trap>
      </network-event>
      <network-event>
        <name>One (or both) of the pumps is out of allowed Temperature range (Min. Temperature, Max. Temperature)</name>
        <short-name>Pumps Warn Temp</short-name>
        <message>One (or both) of the pumps is out of allowed Temperature range (Min. Temperature, Max. Temperature). {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanPumpsWarnTempAlarm</name>
          <number>13</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>
        </trap>
      </network-event>
      <network-event>
        <name>Power supply failure Alarm - The voltage exceeded the supply range - only a warning.</name>
        <short-name>Pwr Supply Fail</short-name>
        <message>Power supply failure Alarm - The voltage exceeded the supply range - only a warning. {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanPwrSupplyFailAlarm</name>
          <number>14</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>          
        </trap>
      </network-event>      
      <network-event>
        <name>Pumps shutdown as result of Automatic Power Reduction caused by a detected eye-safety hazard of any of the APR Scenarios.</name>
        <short-name>APR Shdn</short-name>
        <message>Pumps shutdown as result of Automatic Power Reduction caused by a detected eye-safety hazard of any of the APR Scenarios. {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanAprShdAlarm</name>
          <number>15</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>
        </trap>
      </network-event>
      <network-event>
        <name>Line Fiber quality (attenuation) has changed (deteriorated) - only a warning.</name>
        <short-name>Fiber Deteriorate</short-name>
        <message>Line Fiber quality (attenuation) has changed (deteriorated) - only a warning. {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanFiberDeteriorateAlarm</name>
          <number>16</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>
        </trap>
      </network-event>
      <network-event>
        <name>One or more of the Built-In-Tests has failed - only a warning.</name>
        <short-name>BiT Fail</short-name>
        <message>One or more of the Built-In-Tests has failed - only a warning. {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanBITFailedAlarm</name>
          <number>17</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>
        </trap>
      </network-event>
      <network-event>
        <name>Auto Laser Shutdown in OSC Band, OSC band Input power Loss Alarm.</name>
        <short-name>OSC Band Loss</short-name>
        <message>Auto Laser Shutdown in OSC Band, OSC band Input power Loss Alarm. {2} = {1}</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-value>1</vb-number-value>
        <vb-number-severity>2</vb-number-severity>
        <trap>
          <name>ramanOSCbandLosAlarm</name>
          <number>18</number>
			<varbind>
				<name>Pumps Status</name>
				<oid>.1.3.6.1.4.1.22468.1</oid>
				<trap-param-id>customTrap</trap-param-id>
				<service-affecting/>
				<value id="1">Shutdown</value>
				<value id="2">Active</value>
			</varbind>
        </trap>
      </network-event>
      <network-event>
        <name>1U (Pizza Box) Power supply failure Alarm - one or more of the 5V power suppliers voltage range is less than 5V.</name>
        <short-name>1U Pwr Supply Fail</short-name>
        <message>1U (Pizza Box) Power supply failure Alarm - one or more of the 5V power suppliers voltage range is less than 5V.</message>
        <severity-no-service>NE_DEFINED</severity-no-service>
        <service-affecting/>
        <help/>
        <vb-number-severity>1</vb-number-severity>
        <trap>
          <name>raman1UPwrSupplyFailAlarm</name>
          <number>19</number>
          <enterprise>1.3.6.1.4.1.22468.1</enterprise>
        </trap>
      </network-event>

                  
    </event-list>
	</operation>
</device>