<?xml version="1.0" encoding="UTF-8"?>
<!--
  -  Copyright 2023 Adtran Networks SE. All rights reserved.
  -
  -  Owner: adaemmig
  -
  -  $Id: AlarmMap_NM.xml 87703 2015-03-13 10:28:35Z askarzycki $
  -->
<alarms xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="../src/com/adva/nlms/common/AlarmMap.xsd">

    <category name="ne-event">
        <alarm>
            <name>Adapter Error</name>
            <short-name>adapterError</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>1</alarm-id>
                <raise-clear>
                    <name>adapterError</name>
                    <number>1</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Application Subsystem Failure</name>
            <short-name>applicationSubsystemFailure</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>2</alarm-id>
                <raise-clear>
                    <name>applicationSubsystemFailure</name>
                    <number>2</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Bandwidth Reduced</name>
            <short-name>bandwidthReduced</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>3</alarm-id>
                <raise-clear>
                    <name>bandwidthReduced</name>
                    <number>3</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Call Establishment Error</name>
            <short-name>callEstablishmentError</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>4</alarm-id>
                <raise-clear>
                    <name>callEstablishmentError</name>
                    <number>4</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Communications Protocol Error</name>
            <short-name>communicationsProtocolError</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>5</alarm-id>
                <raise-clear>
                    <name>communicationsProtocolError</name>
                    <number>5</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Communications Subsystem Failure</name>
            <short-name>communicationsSubsystemFailure</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>6</alarm-id>
                <raise-clear>
                    <name>communicationsSubsystemFailure</name>
                    <number>6</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Configuration Or Customization Error</name>
            <short-name>configurationOrCustomizationErr</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>7</alarm-id>
                <raise-clear>
                    <name>configurationOrCustomizationErr</name>
                    <number>7</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Congestion</name>
            <short-name>congestion</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>8</alarm-id>
                <raise-clear>
                    <name>congestion</name>
                    <number>8</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Corrupt Data</name>
            <short-name>corruptData</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>9</alarm-id>
                <raise-clear>
                    <name>corruptData</name>
                    <number>9</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Cpu Cycles Limit Exceeded</name>
            <short-name>cpuCyclesLimitExceeded</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>10</alarm-id>
                <raise-clear>
                    <name>cpuCyclesLimitExceeded</name>
                    <number>10</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Dataset Or Modem Error</name>
            <short-name>datasetOrModemError</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>11</alarm-id>
                <raise-clear>
                    <name>datasetOrModemError</name>
                    <number>11</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Degraded Signal</name>
            <short-name>degradedSignal</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>12</alarm-id>
                <raise-clear>
                    <name>degradedSignal</name>
                    <number>12</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>DTE-DCE Interface Error</name>
            <short-name>dTE-DCEInterfaceError</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>13</alarm-id>
                <raise-clear>
                    <name>dTE-DCEInterfaceError</name>
                    <number>13</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Enclosure Door Open</name>
            <short-name>enclosureDoorOpen</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>14</alarm-id>
                <raise-clear>
                    <name>enclosureDoorOpen</name>
                    <number>14</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Equipment Malfunction</name>
            <short-name>equipmentMalfunction</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>15</alarm-id>
                <raise-clear>
                    <name>equipmentMalfunction</name>
                    <number>15</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Excessive Vibration</name>
            <short-name>excessiveVibration</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>16</alarm-id>
                <raise-clear>
                    <name>excessiveVibration</name>
                    <number>16</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>File Error</name>
            <short-name>fileError</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>17</alarm-id>
                <raise-clear>
                    <name>fileError</name>
                    <number>17</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Fire Detected</name>
            <short-name>fireDetected</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>18</alarm-id>
                <raise-clear>
                    <name>fireDetected</name>
                    <number>18</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Flood Detected</name>
            <short-name>floodDetected</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>19</alarm-id>
                <raise-clear>
                    <name>floodDetected</name>
                    <number>19</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Framing Error</name>
            <short-name>framingError</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>20</alarm-id>
                <raise-clear>
                    <name>framingError</name>
                    <number>20</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Heating Or Ventilation Or Cooling Sys</name>
            <short-name>heatingOrVentilationOrCoolingSys</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>21</alarm-id>
                <raise-clear>
                    <name>heatingOrVentilationOrCoolingSys</name>
                    <number>21</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Humidity Unacceptable</name>
            <short-name>humidityUnacceptable</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>22</alarm-id>
                <raise-clear>
                    <name>humidityUnacceptable</name>
                    <number>22</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Input Output Device Error</name>
            <short-name>inputOutputDeviceError</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>23</alarm-id>
                <raise-clear>
                    <name>inputOutputDeviceError</name>
                    <number>23</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Input Device Error</name>
            <short-name>inputDeviceError</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>24</alarm-id>
                <raise-clear>
                    <name>inputDeviceError</name>
                    <number>24</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>LAN Error</name>
            <short-name>lANError</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>25</alarm-id>
                <raise-clear>
                    <name>lANError</name>
                    <number>25</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Leak Detected</name>
            <short-name>leakDetected</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>26</alarm-id>
                <raise-clear>
                    <name>leakDetected</name>
                    <number>26</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Local Node Transmission Error</name>
            <short-name>localNodeTransmissionError</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>27</alarm-id>
                <raise-clear>
                    <name>localNodeTransmissionError</name>
                    <number>27</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Loss Of Frame</name>
            <short-name>lossOfFrame</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>28</alarm-id>
                <raise-clear>
                    <name>lossOfFrame</name>
                    <number>28</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Loss Of Signal</name>
            <short-name>lossOfSignal</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>29</alarm-id>
                <raise-clear>
                    <name>lossOfSignal</name>
                    <number>29</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Material Supply Exhausted</name>
            <short-name>materialSupplyExhausted</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>30</alarm-id>
                <raise-clear>
                    <name>materialSupplyExhausted</name>
                    <number>30</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Multiplexer Problem</name>
            <short-name>multiplexerProblem</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>31</alarm-id>
                <raise-clear>
                    <name>multiplexerProblem</name>
                    <number>31</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Out Of Memory</name>
            <short-name>outOfMemory</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>32</alarm-id>
                <raise-clear>
                    <name>outOfMemory</name>
                    <number>32</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Output Device Error</name>
            <short-name>outputDeviceError</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>33</alarm-id>
                <raise-clear>
                    <name>outputDeviceError</name>
                    <number>33</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Performance Degraded</name>
            <short-name>performanceDegraded</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>34</alarm-id>
                <raise-clear>
                    <name>performanceDegraded</name>
                    <number>34</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Power Problem</name>
            <short-name>powerProblem</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>35</alarm-id>
                <raise-clear>
                    <name>powerProblem</name>
                    <number>35</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Pressure Unacceptable</name>
            <short-name>pressureUnacceptable</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>36</alarm-id>
                <raise-clear>
                    <name>pressureUnacceptable</name>
                    <number>36</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Processor Problem</name>
            <short-name>processorProblem</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>37</alarm-id>
                <raise-clear>
                    <name>processorProblem</name>
                    <number>37</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Pump Failure</name>
            <short-name>pumpFailure</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>38</alarm-id>
                <raise-clear>
                    <name>pumpFailure</name>
                    <number>38</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Queue Size Exceeded</name>
            <short-name>queueSizeExceeded</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>39</alarm-id>
                <raise-clear>
                    <name>queueSizeExceeded</name>
                    <number>39</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Receive Failure</name>
            <short-name>receiveFailure</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>40</alarm-id>
                <raise-clear>
                    <name>receiveFailure</name>
                    <number>40</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Receiver Failure</name>
            <short-name>receiverFailure</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>41</alarm-id>
                <raise-clear>
                    <name>receiverFailure</name>
                    <number>41</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Remote Node Transmission Error</name>
            <short-name>remoteNodeTransmissionError</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>42</alarm-id>
                <raise-clear>
                    <name>remoteNodeTransmissionError</name>
                    <number>42</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Resource At Or Nearing Capacity</name>
            <short-name>resourceAtOrNearingCapacity</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>43</alarm-id>
                <raise-clear>
                    <name>resourceAtOrNearingCapacity</name>
                    <number>43</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Response Time Excessive</name>
            <short-name>responseTimeExcessive</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>44</alarm-id>
                <raise-clear>
                    <name>responseTimeExcessive</name>
                    <number>44</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Retransmission Rate Excessive</name>
            <short-name>retransmissionRateExcessive</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>45</alarm-id>
                <raise-clear>
                    <name>retransmissionRateExcessive</name>
                    <number>45</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Sofware Error</name>
            <short-name>sofwareError</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>46</alarm-id>
                <raise-clear>
                    <name>sofwareError</name>
                    <number>46</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Sofware Program Abnormally Terminated</name>
            <short-name>sofwareProgramAbnormallyTermin</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>47</alarm-id>
                <raise-clear>
                    <name>sofwareProgramAbnormallyTermin</name>
                    <number>47</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Software Program Error</name>
            <short-name>softwareProgramError</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>48</alarm-id>
                <raise-clear>
                    <name>softwareProgramError</name>
                    <number>48</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Storage Capacity Problem</name>
            <short-name>storageCapacityProblem</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>49</alarm-id>
                <raise-clear>
                    <name>storageCapacityProblem</name>
                    <number>49</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Temperature Unacceptable</name>
            <short-name>temperatureUnacceptable</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>50</alarm-id>
                <raise-clear>
                    <name>temperatureUnacceptable</name>
                    <number>50</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Threshold Crossed</name>
            <short-name>thresholdCrossed</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>51</alarm-id>
                <raise-clear>
                    <name>thresholdCrossed</name>
                    <number>51</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Timing Problem</name>
            <short-name>timingProblem</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>52</alarm-id>
                <raise-clear>
                    <name>timingProblem</name>
                    <number>52</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Toxic Leak Detected</name>
            <short-name>toxicLeakDetected</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>53</alarm-id>
                <raise-clear>
                    <name>toxicLeakDetected</name>
                    <number>53</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Transmit Failure</name>
            <short-name>transmitFailure</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>54</alarm-id>
                <raise-clear>
                    <name>transmitFailure</name>
                    <number>54</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Transmitter Failure</name>
            <short-name>transmitterFailure</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>55</alarm-id>
                <raise-clear>
                    <name>transmitterFailure</name>
                    <number>55</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Underlying Resource Unavailable</name>
            <short-name>underlyingResourceUnavailable</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>56</alarm-id>
                <raise-clear>
                    <name>underlyingResourceUnavailable</name>
                    <number>56</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Version Mismatch</name>
            <short-name>versionMismatch</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>57</alarm-id>
                <raise-clear>
                    <name>versionMismatch</name>
                    <number>57</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Snmp Trap Cold Start</name>
            <short-name>snmpTrapColdStart</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>58</alarm-id>
                <raise-clear>
                    <name>snmpTrapColdStart</name>
                    <number>58</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Snmp Trap Warm Start</name>
            <short-name>snmpTrapWarmStart</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>59</alarm-id>
                <raise-clear>
                    <name>snmpTrapWarmStart</name>
                    <number>59</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Snmp Trap Link Down</name>
            <short-name>snmpTrapLinkDown</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>60</alarm-id>
                <raise-clear>
                    <name>snmpTrapLinkDown</name>
                    <number>60</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Snmp Trap Link Up</name>
            <short-name>snmpTrapLinkUp</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>61</alarm-id>
                <raise-clear>
                    <name>snmpTrapLinkUp</name>
                    <number>61</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Snmp Trap Authentication Failure</name>
            <short-name>snmpTrapAuthenticationFailure</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>62</alarm-id>
                <raise-clear>
                    <name>snmpTrapAuthenticationFailure</name>
                    <number>62</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Snmp Trap Egp Neighborloss</name>
            <short-name>snmpTrapEgpNeighborloss</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>63</alarm-id>
                <raise-clear>
                    <name>snmpTrapEgpNeighborloss</name>
                    <number>63</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Snmp Trap Enterprise Specific</name>
            <short-name>snmpTrapEnterpriseSpecific</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>64</alarm-id>
                <raise-clear>
                    <name>snmpTrapEnterpriseSpecific</name>
                    <number>64</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Snmp Trap Link Up Down</name>
            <short-name>snmpTrapLinkUpDown</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>65</alarm-id>
                <raise-clear>
                    <name>snmpTrapLinkUpDown</name>
                    <number>65</number>
                </raise-clear>
            </trap>
        </alarm>
        <alarm>
            <name>Unknown</name>
            <short-name>unknown</short-name>
            <message/>
            <severity-working>CRITICAL</severity-working>
            <severity-protecting>MINOR</severity-protecting>
            <severity-no-service>NE_DEFINED</severity-no-service>
            <trap>
                <alarm-id>66</alarm-id>
                <raise-clear>
                    <name>unknown</name>
                    <number>66</number>
                </raise-clear>
            </trap>
        </alarm>
    </category>
</alarms>
