<?xml version="1.0" encoding="UTF-8"?>
<!--
*  Copyright 2023 Adtran Networks SE. All rights reserved.
*
*  Owner: askar<PERSON><PERSON>
-->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">

    <xs:element name="mapping">
        <xs:complexType>
            <xs:all>
                <xs:element ref="layers"/>
                <xs:element ref="columns"/>
            </xs:all>
        </xs:complexType>
    </xs:element>

    <xs:element name="layers">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="layer" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="columns">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="column" maxOccurs="unbounded"/>
            </xs:sequence>
        </xs:complexType>
    </xs:element>

    <xs:element name="layer">
        <xs:complexType>
            <xs:sequence maxOccurs="unbounded" minOccurs="0">
                <xs:any/>
            </xs:sequence>
            <xs:attribute name="name" type="xs:string"/>
            <xs:attribute name="mapsTo" type="mapsTo"/>
        </xs:complexType>
    </xs:element>

    <xs:element name="column">
        <xs:complexType>
            <xs:attribute name="id" type="xs:integer"/>
            <xs:attribute name="order" type="xs:string"/>
            <xs:attribute name="name" type="xs:string"/>
        </xs:complexType>
    </xs:element>

    <xs:element name="PerformanData">
        <xs:complexType>
            <xs:all>
                <xs:element ref="column-ref"/>
            </xs:all>
            <xs:attribute name="index" type="xs:integer"/>
            <xs:attribute name="divisor" type="xs:integer"/>
            <xs:attribute name="type" type="xs:string"/>
        </xs:complexType>
    </xs:element>

    <xs:element name="column-ref">
        <xs:complexType>
            <xs:attribute name="id"/>
        </xs:complexType>
    </xs:element>

    <xs:simpleType name="mapsTo">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ODUx"/>
            <xs:enumeration value="OTUx"/>
            <xs:enumeration value="OCH"/>
        </xs:restriction>
    </xs:simpleType>

</xs:schema>