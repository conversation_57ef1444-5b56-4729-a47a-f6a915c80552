<!--
  -  Copyright 2023 Adtran Networks SE. All rights reserved.
  -
  -  Owner: tomaszm
  -->
<project name="Common classpath properties">

    <property file="dir.properties"/>
    <property file="${dir_NLMS}/build.properties"/>
    <property file="${dir_NLMS}/lib/report/ivy_classpaths.properties"/>
    <property file="${dir_NLMS}/test/lib/report/ivy_test_classpath.properties"/>

    <!--<property file="${dir_NLMS}/classpaths.properties"/>-->

    <path id="common-path">
        <pathelement path="${classes.dir}"/>
        <pathelement path="${classes.dir}/META-INF/"/>
        <pathelement path="${lib.dir}/ypdb-commons.jar"/>
    </path>

    <path id="dbupgrade-common-path">
        <pathelement path="${lib.dir}/ypdb-commons.jar"/>
    </path>

    <path id="server-compile-path">
        <path refid="common-path"/>
        <pathelement path="${ivy.classpath.server-compile}"/>
    </path>

    <path id="server-runtime-path">
        <path refid="common-path"/>
        <path path="${dir_NLMS}/lib/nmscommon.jar"/>
        <path path="${dir_NLMS}/lib/adva_tools.jar"/>
        <path path="${dir_NLMS}/lib/drivercommon.jar"/>
        <pathelement path="${ivy.classpath.server-runtime}"/>
    </path>

    <path id="server-runtime-path-linux">
        <path refid="common-path"/>
        <path path="${dir_NLMS}/lib/nmscommon.jar"/>
        <path path="${dir_NLMS}/lib/adva_tools.jar"/>
        <path path="${dir_NLMS}/lib/jfxrt-linux.jar"/>
        <pathelement path="${ivy.classpath.server-runtime}"/>
    </path>


    <!-- this is mediation common classpath not com-adva-common-->
    <path id="common-compile-path">
        <path refid="common-path"/>
        <pathelement path="${ivy.classpath.common-compile}"/>
    </path>

    <path id="client-compile-path">
        <path refid="common-path"/>
        <pathelement path="${ivy.classpath.client-compile}"/>
    </path>

    <path id="client-runtime-path">
        <path refid="common-path"/>
        <path path="${dir_NLMS}/lib/nmscommon.jar"/>
        <path path="${dir_NLMS}/lib/adva_tools.jar"/>
        <pathelement path="${ivy.classpath.client-runtime}"/>
    </path>

    <path id="client-runtime-path-linux">
        <path refid="common-path"/>
        <path path="${dir_NLMS}/lib/nmscommon.jar"/>
        <path path="${dir_NLMS}/lib/adva_tools.jar"/>
        <path path="${dir_NLMS}/lib/jfxrt-linux.jar"/>
        <pathelement path="${ivy.classpath.client-runtime}"/>
    </path>

    <path id="client-common-server-compile-path">
        <path refid="common-path"/>
        <pathelement path="${ivy.classpath.common-compile}"/>
        <pathelement path="${ivy.classpath.server-compile}"/>
        <pathelement path="${ivy.classpath.client-compile}"/>
    </path>

    <!-- Workaround for idea to properly show references, idea doesn't recognize refid values if referred id is defined in included ant file-->
    <!-- But have no problems with properties. :)-->
    <property name="server-classpath" refid="server-compile-path"/>
    <property name="server-runtime-classpath" refid="server-runtime-path"/>

    <!-- this is mediation common classpath not com-adva-common-->
    <property name="common-classpath" refid="common-compile-path"/>

    <property name="client-classpath" refid="client-compile-path"/>
    <property name="client-runtime-classpath" refid="client-runtime-path"/>
    <!--<if>-->
        <!--&lt;!&ndash; workaround for jfxrt jar overwrite &ndash;&gt;-->
        <!--<os family="windows"/>-->
        <!--<then>-->
            <!--&lt;!&ndash;<echo message="Using classpath for windows."/>&ndash;&gt;-->
            <!--<property name="server-runtime-classpath" refid="server-runtime-path"/>-->
            <!--<property name="client-runtime-classpath" refid="client-compile-path"/>-->
        <!--</then>-->
        <!--<else>-->
            <!--&lt;!&ndash;<echo message="Using classpath for linux."/>&ndash;&gt;-->
            <!--<property name="server-runtime-classpath" refid="server-runtime-path-linux"/>-->
            <!--<property name="client-runtime-classpath" refid="client-runtime-path-linux"/>-->
        <!--</else>-->
    <!--</if>-->

    <!-- used during eclipse link waving, todo this need to be optimized-->

    <property name="all-compile-classpath" refid="client-common-server-compile-path"/>

    <macrodef name="compile-module" description="Macro for simple module compilation.">
        <attribute name="module-path"/>
        <attribute name="module-classpath"/>

        <sequential>
            <echo message="Compiling module @{module-path} with classpath @{module-classpath}"/>
            <!--<tstamp>-->
                <!--<format property="DSTAMP" pattern="MMM d yyyy" />-->
            <!--</tstamp>-->
            <javac destdir="${dir_NLMS}/build/classes"
                   debug="${debug}"
                   nowarn="${nowarn}"
                   encoding="${encoding}"
                   source="${sourcelevel}"
                   target="${sourcelevel}">
                <src path="${dir_NLMS}/src/@{module-path}"/>
                <classpath path="@{module-classpath}"/>
            </javac>
        </sequential>
    </macrodef>

    <macrodef name="create-jar" description="Macro for simple jar creation.">
        <attribute name="module-path"/>
        <attribute name="jar-name"/>

        <sequential>
            <property file="${version.info.file}" />
            <property name="VERSION" value="${MAJOR_VERSION}.${MINOR_VERSION}.${PATCH_VERSION}"/>
            <property name="BUILD" value="B${BUILD_NUMBER}"/>

            <delete file="${dir_NLMS}/build/archives/@{jar-name}"/>

            <jar destfile="${dir_NLMS}/build/archives/@{jar-name}" basedir="${dir_NLMS}/build/classes">
                <include name="**/@{module-path}/**/*"/>
                <manifest>
                    <attribute name="Description" value="Common driver module"/>
                    <attribute name="FNM-Version" value="${VERSION}"/>
                    <attribute name="FNM-Build" value="${BUILD}"/>
                    <attribute name="Copyright" value="ADVA Optical Networking SE"/>
                </manifest>
            </jar>
        </sequential>
    </macrodef>

    <macrodef name="clean-module" description="Macro for cleaning the module.">
        <attribute name="module-path"/>

        <sequential>
            <delete includeEmptyDirs="true" >
                <!-- delete all class files -->
                <fileset dir="${dir_NLMS}/build/classes"
                         includes="**/@{module-path}/**/*," />
            </delete>
        </sequential>
    </macrodef>



    <!--<property name="test-runtime" refid="tests-compile-path"/>-->
</project>
