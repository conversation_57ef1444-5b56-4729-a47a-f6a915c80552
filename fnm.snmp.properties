# File contains snmp configuration, changing properties here will affect
# all snmp communication done by NMS for F3 and F7 devices.
#
# Properties are in following format:
# nt[2660].v[all].st[1.3.6.1.4.1.2544.1.12.4.1.4].p[max_request_size]=123
# where:
# nt - integer code of network element type, so nt[2060] means NE type GE 206F
# v  - version of device software, so v[all] means all box software version will be affected,
# WARNING in nms 8.5 only v[all] is supported, if you will try to use specific version it will not work.
# st - snmp table oid so st[1.3.6.1.4.1.2544.1.12.4.1.4] is snmp flow table:
# p  - property which you want to set so p[max_request_size] means we want set max_request_size

# Possible properties:
# max_request_size - Indicates max size of request, so when performing snmp get column size * row size will always be less than max_request_size.

