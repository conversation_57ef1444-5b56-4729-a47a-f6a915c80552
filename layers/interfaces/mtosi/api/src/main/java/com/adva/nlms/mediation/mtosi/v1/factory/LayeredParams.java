/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.factory;

public class LayeredParams
{
  public final static String LR_PHYSICAL_OPTICAL = "LR_PHYSICAL_OPTICAL";
  public final static String LR_PHYSICAL_ELECTRICAL = "LR_PHYSICAL_ELECTRICAL";
  public final static String LR_DSR_GIGABIT_ETHERNET = "LR_DSR_Gigabit_Ethernet";
  public final static String LR_DSR_FAST_ETHERNET = "LR_DSR_Fast_Ethernet";
  public final static String LR_ETHERNET = "LR_Ethernet";
  public final static String PROP_ADVA = "PROP_ADVA";
  public final static String PROP_ADVA_SFP = "PROP_ADVA_SFP";
  public final static String PROP_ADVA_SNMP = "PROP_ADVA_SNMP";
  public final static String PROP_ADVA_SNMP_V1V2C = "PROP_ADVA_SNMPv1v2c";
  public final static String PROP_ADVA_TOPOLOGY = "PROP_ADVA_Topology";
  public final static String PROP_ADVA_ETHERNET = "PROP_ADVA_Ethernet";
  public final static String PROP_ADVA_ETHERNET_EFMOAM = "PROP_ADVA_Ethernet_EFMOAM";
	public final static String PROP_ADVA_REMOTECPE = PROP_ADVA+"_RemoteCPE";
  public final static String PROP_ADVA_PROTECTION_FSP150_1PLUS1 = "PROP_ADVA_ProtectionFSP1501Plus1";
  public final static String PROP_ADVA_PROTECTION_FSP150_CMNTU = "PROP_ADVA_ProtectionFSP150CMNTU";
  public final static String PROP_ADVA_PHYSICAL_OPTICAL = "PROP_ADVA_PHYSICAL_OPTICAL";
  public final static String PROP_ADVA_GIGABIT_ETHERNET = "PROP_ADVA_Gigabit_Ethernet";
  public final static String PROP_ADVA_ETHERNET_QUEUE = "PROP_ADVA_Ethernet_Queue";
  public final static String PROP_ADVA_ETHERNET_POLICER = "PROP_ADVA_Ethernet_Policer";
  public final static String PROP_ADVA_ETHERNET_SHAPER = "PROP_ADVA_Ethernet_Shaper";
  public final static String PROP_ADVA_LAG = "PROP_ADVA_LAG";
  public final static String PROP_ADVA_LAG_FRAGMENT = "PROP_ADVA_LAG_Fragment";
  public final static String LR_LAG_FRAGMENT = "LR_LAG_Fragment";
  public final static String LR_LAG = "LR_LAG";
  public final static String PROP_ADVA_PROTECTION150CP = "PROP_ADVA_Protection150CP";
  public final static String ACTIVE_PARAM = "ACTIVE";
  public final static String DISABLED_PARAM = "Disabled";
  public final static String INACTIVE_PARAM = "INACTIVE";
  public final static String PROP_ADVA_CFMCCM               = "PROP_ADVA_CFMCCM";
  public final static String PROP_ADVA_RemoteCPE = "PROP_ADVA_RemoteCPE";
  public final static String PROP_ADVA_Tunnel = "PROP_ADVA_Tunnel";

  
  public final static String PROP_HATTERAS_2BPME = "PROP_HATTERAS_2BPME";
	public final static String PROP_HATTERAS_Ethernet = "PROP_HATTERAS_Ethernet";
	public final static String PROP_HATTERAS_Ethernet_EFMOAM = "PROP_HATTERAS_Ethernet_EFMOAM";
	public final static String PROP_HATTERAS_LAG_Fragment = "PROP_HATTERAS_LAG_Fragment";
	public final static String PROP_HATTERAS_Bonding = "PROP_HATTERAS_Bonding";
	public final static String PROP_HATTERAS_RemoteCPE = "PROP_HATTERAS_RemoteCPE";
	public final static String PROP_HATTERAS_LAG = "PROP_HATTERAS_LAG";
	public final static String PROP_HATTERAS_Ethernet_UNI = "PROP_HATTERAS_Ethernet_UNI";

	public final static String PROP_HATTERAS_Ethernet_Shaper = "PROP_HATTERAS_Ethernet_Shaper";
	public final static String PROP_HATTERAS_Ethernet_Policer = "PROP_HATTERAS_Ethernet_Policer";
	// New 2BPME Layers
	public final static String LR_DIGIAL_SIGNAL_RATE = "LR_DIGITAL_SIGNAL_RATE";
	public final static String LR_DSL = "LR_DSL";
	public final static String DEFAULT_DSL_TYPE = "SHDSL";
	public final static String LR_SHDSL = "LR_SHDSL";
	public final static String PROP_HATTERAS_SHDSL = "PROP_HATTERAS_SHDSL";
	// New GE20X Layers
	public final static String LR_Optical_Channel = "LR_Optical_Channel";
	public final static String PROP_ADVA_SyncEthernet = "PROP_ADVA_SyncEthernet";
	public final static String PROP_ADVA_BITS = "PROP_ADVA_BITS";
	public final static String PROP_ADVA_TIMING_DOMAIN = "PROP_ADVA_TIMING_DOMAIN";
	public final static String PROP_ADVA_SYNC_REFERENCE = "PROP_ADVA_Sync_Reference";

  public static class PropAdva {
    public final static String IP_ADDRESS = "IpAddress";
  }

  public static class PropAdvaTopology {
    public final static String SubnetPath = "SubnetPath";
  }

  public static class PropAdvaSnmp {
    public final static String USE_GLOBAL_SNMP_SETTINGS = "UseGlobalSNMPSettings";
    public final static String SNMP_PROTOCOL_VERSION = "SNMPProtocolVersion";
  }

  public static class PropAdvaSnmpV1V2c {
    public final static String READ_COMMUNITY = "ReadCommunity";
    public final static String WRITE_COMMUNITY = "WriteCommunity";
  }

	public static class LrDsl {
		public final static String DSL_TYPE_PARAM = "DSLType";
		public final static String ADMINISTRATION_CONTROL_PARAM = "AdministrationControl";
		public final static String SERVICE_STATE_PARAM = "ServiceState";
	}

	public static class LrShdsl {
		public final static String DATA_RATE_PARAM = "DataRate";
		public final static String SNR_MGN_PARAM = "SnrMgn";
		public final static String ATTENUATION_PARAM = "Attenuation";
		public final static String LINE_STATUS_PARAM = "LineStatus";
	}

	public static class PropHatterasShdsl {
		public final static String SECONDARY_STATE_PARAM = "SecondaryState";
		public final static String DESCRIPTION_PARAM = "Description";
		public final static String PEER_SNR_MGN_PARAM = "PeerSnrMgn";
		public final static String PEER_ATTENUATION_PARAM = "PeerAttenuation";
		public final static String SPAN_UPTIME_PARAM = "SpanUptime";
		public final static String RETRAIN_REASON_PARAM = "RetrainReason";
		public final static String REMOTE_2BPME_PARAM = "Remote2BPME";
		public final static String SPAN_PROFILE_ID_PARAM = "SpanProfileId";
		public final static String SPAN_ALARM_PROFILE_ID_PARAM = "SpanAlarmProfileId";
		public final static String AFDI_ENABLED_PARAM = "AFDIEnabled";
		public final static String AFDI_DELAY_PARAM = "AFDIDelay";
	}

	public static class PropHatterasLagFragment {
		public final static String LACP_SERVICE_STATE_PARM = "LACPServiceState";
	}
	public static class PropHatterasLag {
		public final static String ADMINISTRATION_CONTROL_PARAM = "AdministrationControl";
		public final static String SERVICE_STATE_PARAM = "ServiceState";
		public final static String SECONDARY_STATE_PARAM = "SecondaryState";
//		public final static String LAG_FRAGEMENT_PORT_LIST_PARAM = "LAGFragementPortList";
		public final static String LAG_LOAD_BALANCE_PARAM = "LAGLoadBalance";
	}
	public static class PropHatterasBonding {
		public final static String PME_VALIDATION_PARM = "PMEValidation";
		public final static String DOT3_AH_AGGREGATION_PARM = "Dot3AHAggregation";
		public final static String MINIMUM_RATE_ALARM_THRESHOLD_PARM = "MinimumRateAlarmThreshold";
		public final static String AGGREGATE_RATE_PARM = "AggregateRate";
		public final static String BEST_ETHER_RATE_PARM = "BestEtherRate";
		public final static String WORST_ETHER_RATE_PARM = "WorstEtherRate";
		public final static String AFDI_ENABLED_PARAM = "AFDIEnabled";
		public final static String AFDI_DELAY_PARAM = "AFDIDelay";
		public final static String MAX_FRAGMENT_SIZE_PARM = "MaxFragmentSize";
		public final static String ACTUAL_PME_PORT_LIST_PARM = "ActualPMEPortList";
	}
	
	public static class PropHatterasRemoteCPE {
		public final static String CPE_HOSTNAME_PARM = "CPEHostname";
		public final static String REMOTE_TYPE_PARM = "RemoteType";
		public final static String ACTUAL_REMOTE_TYPE_PARM = "ActualRemoteType";
		public final static String ACTUAL_REMOTE_MODEL_PARM = "ActualRemoteModel";
		public final static String REMOTE_MAC_ADDRESS_PARM = "RemoteMACAddress";
		public final static String ACTUAL_REMOTE_MAC_ADDRESS_PARM = "ActualRemoteMACAddress";
		public final static String CPE_TEMPLATE_ID_PARM = "CPETemplateID";
		public final static String CPE_SERIAL_PORT_ADMINISTRATION_CONTROL_PARM = "CPESerialPortAdministrationControl";
	}

	public static class PropHatterasEthernetUni {
		public final static String THIS_LAYER_ACTIVE_PARAM = "ThisLayerActive";
		public final static String NAME_PARAM = "Name";
		public final static String DESCRIPTION_PARAM = "Description";
		public final static String SECONDARY_STATE_PARAM = "SecondaryState";
		public final static String DEFAULT_VLANID_PARAM = "DefaultVLANId";
		public final static String STP_ADMINISTRATIONCONTROL_PARAM = "STPAdministrationControl";
		public final static String PORT_TYPE_PARAM = "PortType";
		public final static String TAG_ETHER_TYPE_PARAM = "TagEtherType";
		public final static String RX_UNTAGGED_PARAM = "RxUntagged";
		public final static String RX_UNKNOWN_PARAM = "RxUnknown";
		public final static String COS_MAP_TYPE_PARAM = "COSMapType";
		public final static String DEFAULT_COS_PARAM = "DefaultCOS";
		//Extra params for HN400
		public final static String DESCRIPTION1_PARAM = "Description1";
		public final static String DESCRIPTION2_PARAM = "Description2";
		public final static String EGRESS_RATE_LIMITING_ENABLED_PARAM = "EgressRateLimitingEnabled";
		public final static String TX_UNTAGGED_PARAM = "TxUntagged";
		
	}
	public static class PropHatterasEthernetShaper {
		public final static String EGRESS_CIR_PARAM = "EgressCIR";
		public final static String EGRESS_CIR_QUEUE_PARAM = "EgressCIRQueue";
		
	}
	public static class PropHatterasEthernetPolicer {
		public final static String INGRESS_CIR_PARAM = "IngressCIR";
		public final static String INGRESS_CBS_PARAM = "IngressCBS";
		
	}

	/**
	 * Parameters of PHYSICAL_OPTICAL and PHYSICAL_ELECTRICAL layers.
	 */
	public static class LrElectricalAndOptical {
		public final static String THIS_LAYER_ACTIVE_PARAM = "ThisLayerActive";
		public final static String CONNECTOR_TYPE_PARAM = "ConnectorType";
	}

	/**
	 * Parameters of DSR_GIGABIT_ETHERNET and DSR_FAST_ETHERNET layers.
	 */
	public static class LrDsrGigabitAndFastEthernet {
		public final static String ADMINISTRATION_CONTROL_PARAM = "AdministrationControl";
		public final static String SERVICE_STATE_PARAM = "ServiceState";
		public final static String AUTO_NEGOTIATION_PARAM = "AutoNegotiation";
		public final static String ADMINISTRATIVE_SPEED_RATE_PARAM = "AdministrativeSpeedRate";
		public final static String ACTUAL_SPEED_RATE_PARAM = "ActualSpeedRate";
		public final static String DUPLEX_MODE_PARAM = "DuplexMode";
        public final static String SPEED_MODE_PARAM = "SpeedMode";
		public final static String ACTUAL_DUPLEX_MODE_PARAM = "ActualDuplexMode";
		public final static String MAXIMUM_FRAME_SIZE_PARAM = "MaximumFrameSize";
		public final static String AUTO_NEG_REM_SIGNALLING_DETECTED_PARAM = "AutoNegRemSignallingDetected";
		public final static String AUTO_NEG_STATUS_PARAM = "AutoNegStatus";
		public final static String AUTO_NEG_LOCAL_TECHNOLOGY_AVAILABILITY_PARAM = "AutoNegLocalTechnologyAvailability";
		public final static String AUTO_NEG_REMOTE_TECHNOLOGY_AVAILABILITY_PARAM = "AutoNegRemoteTechnologyAvailability";
		//New attribute for GE20X Access Ports
		public final static String SECONDARY_STATE_PARAM = "SecondaryState";
	}

	/**
	 * Parameters of PROP_ADVA_2BPME
	 */
	public static class PropADVA2BPME {
		public final static String ADMINISTRATION_CONTROL_PARAM = "AdministrationControl";
		public final static String SERVICE_STATE_PARAM = "ServiceState";
	}

	/**
	 * Parameters of PROP_HATTERAS_Ethernet
	 */
	public static class PropHatterasEthernet {
		public final static String DESCRIPTION = "Description";
		public final static String SECONDARY_STATE = "SecondaryState";
		public final static String TRANSMIT_PAUSE_FRAMES = "TransmitPauseFramesEnabled";
		public final static String RECEIVE_PAUSE_FRAMES = "ReceivePauseFramesEnabled";
		public final static String LOOPBACK_STATUS_TYPE = "LoopbackStatusType";
		public final static String LOOPBACK_STATUS_LOCAL = "LoopbackStatusLocalLoopbackMode";
		//Extras for FDFr Layer
		public final static String ADMINISTRATION_CONTROL_PARAM = "AdministrationControl";
		public final static String SERVICE_STATE_PARAM = "ServiceState";
		public final static String TYPE_PARAM = "Type";
		public final static String NUM_ACTIVE_UNIS_PARAM = "NumActiveUnis";
		public final static String NUM_TOTAL_UNIS_PARAM = "NumTotalUnis";
		//Extras for Flow Point CTPs
		public final static String VLAN_MEMBERS_PARAM = "VLANMembers";
		public final static String VLAN_PRESERVATION_PARAM = "VLANPreservation";
		public final static String EVC_TYPE = "Type";
		//Extras for HN400
		public final static String LINK_LOSS_FORWARDING_PARAM = "LinkLossForwarding";
		public final static String MDI_MODE_PARAM = "MdiMode";
		public final static String ACTUAL_MDI_MODE_PARAM = "ActualMdiMode";
		//Extras for L2 Loopback
		public static final String LOOPBACK_STATUS_LOOPBACK_PHYS_ADDR_PARM = "LoopbackStatusLoopbackPhyAddress";
		
		
	}

	/**
	 * Parameters of Prop Hatteras Ethernet EFMOAM layer.
	 */
	public static class PropHatterasEthernetEFMOAM {

		public final static String EFM_OAM_ADMINISTRATION_CONTROL_PARAM = "EfmOamAdministrationControl";
		public final static String EFM_OAM_SERVICE_STATE_PARAM = "EfmOamServiceState";
		public final static String EFM_OAM_MODE_PARAM = "EfmOamMode";
		public final static String EFM_OAM_MAX_PDU_SIZE_PARAM = "EfmOamMaxPDUSize";
		public final static String EFM_OAM_FUNCTIONS_SUPPORTED_LIST_PARAM = "EfmOamFunctionsSupportedList";
		public final static String EFM_OAM_SUSPEND_PARAM = "EfmOamSuspend";
	}

	/**
	 * Parameters of ETHERNET layer.
	 */
	public static class LrEthernet {
		public final static String SERVICE_MUXING_INDICATOR_PARAM = "ServiceMuxingIndicator";
		public final static String ALL_IN_ONE_INDICATOR_PARAM = "AllToOneIndicator";
		public final static String PORT_ACCEPTABLE_FRAME_TYPES_PARAM = "PortAcceptableFrameTypes";
		public final static String CONNECTIONLESS_PORT_PARAM = "ConnectionlessPort";
		public final static String INTERFACE_TYPE_PARAM = "InterfaceType";
		public final static String PORT_TP_ROLE_STATE_PARAM = "PortTPRoleState";
		public final static String NUMBER_OF_TRAFFIC_CLASSES_PARAM = "NumberOfTrafficClasses";
		public final static String PHYS_ADDRESS_PARAM = "PhysAddress";
		public final static String MAX_NUM_FDFRS_PARAM = "MaxNumFDFrs";
		public final static String NUM_CONFIGURED_FDFRS_PARAM = "NumConfiguredFDFrs";
	}

  /**
   * Parameters of PROP_ADVA_ETHERNET layer.
   */
  public static class LrPropAdvaEthernet{

    public final static String ASSIGNED_STATE_PARAM                  = "AssignedState";
    public final static String PORT_MODE_PARAM                       = "PortMode";
    public final static String MEDIA_TYPE_PARAM                      = "MediaType";
    public final static String ACCEPTABLE_FRAME_POLICY_PARAM         = "AcceptableFramePolicy";
    public final static String VLAN_TRUNKING_ENABLED_PARAM           = "VLANTrunkingEnabled";
    public final static String INGRESS_PUSH_PVID_ENABLED_PARAM       = "IngressPushPVIDEnabled";
    public final static String EGRESS_POP_PVID_ENABLED_PARAM         = "EgressPopPVIDEnabled";
    public final static String PORT_VLAN_ID_PARAM                    = "PortVLANId";
    public final static String PORT_PRIORITY_PARAM                   = "PortPriority";
    public final static String PRIORITY_VLAN_ID_PARAM                = "PriorityVLANId";
    public final static String PRIORITY_MAP_MODE_PARAM               = "PriorityMapMode";
    public final static String TAGGED_FRAMES_ENABLED_PARAM           = "TaggedFramesEnabled";
    public final static String UNTAGGED_FRAMES_ENABLED_PARAM         = "UntaggedFramesEnabled";
    public final static String VLAN_ETHER_TYPE_PARAM                 = "VLANEtherType";
    public final static String TRANSMIT_PAUSE_FRAMES_ENABLED_PARAM   = "TransmitPauseFramesEnabled";
    public final static String RECEIVE_PAUSE_FRAMES_ENABLED_PARAM    = "ReceivePauseFramesEnabled";
    public final static String SVLAN_ETHER_TYPE_PARAM                = "SVLANEtherType";
    public final static String CVLAN_ETHER_TYPE_PARAM                = "CVLANEtherType";
    public final static String LOOPBACK_STATUS_TYPE_PARAM            = "LoopbackStatusType";
    public final static String LOOPBACK_STATUS_TIME_PARAM            = "LoopbackStatusTime";
    public final static String LOOPBACK_STATUS_SWAP_SADA_PARAM       = "LoopbackStatusSwapSADA";
    public final static String LOOPBACK_STATUS_VLAN_LIST_PARAM       = "LoopbackStatusVLANList";
    public final static String LOOPBACK_STATUS_INNER_VLAN_LIST_PARAM = "LoopbackStatusInnerVLANList";
    public final static String LOOPBACK_TYPE_PARAM            		 = "LoopbackType";
    public final static String LOCAL_LOOPBACK_MODE            		 = "LocalLoopbackMode";
    public final static String LOOPBACK_TIME_PARAM            		 = "LoopbackTime";
    public final static String LOOPBACK_SWAP_SADA_PARAM       		 = "SwapSADA";
    public final static String WAN_FAULT_PROPAGATION_PARAM           = "WANFaultPropagation";
    public final static String LAN_FAULT_PROPAGATION_PARAM           = "LANFaultPropagation";
    public final static String FAULT_PROPAGATION_DELAY_PARAM         = "FaultPropagationDelay";
    public final static String LOCAL_LINK_ID_PARAM                   = "LocalLinkId";
    public final static String REMOTE_LINK_IDS_PARAM                 = "RemoteLinkIds";
    public final static String LINK_LOSS_FORWARDING_PARAM            = "LinkLossForwarding";
    public final static String LINK_LOSS_FORWARDING_ACTIVE_PARAM     = "LinkLossForwardingActive";
    public final static String EFMOAM_ENABLED_PARAM                  = "EFMOAMEnabled";
    public final static String EFMOAM_ACTIVE_PARAM                   = "EFMOAMActive";
    public final static String EFMOAM_INFO_ENABLE_PARAM              = "EFMOAMInfoEnable";
    public final static String SHAPING_ENABLED_PARAM                 = "ShapingEnabled";
    public final static String SHAPING_BANDWIDTH_PARAM               = "ShapingBandwidth";
    //New Attributes for the 825
    public final static String PORT_ACCESS_ENTITY_ROLE_PARAM         = "8021xPortAccessEntityRole";
    public final static String SUPP_USER_STAGE1_PARAM         		 = "8021xSuppUserStage1";
    public final static String SUPP_USER_STAGE2_PARAM         		 = "8021xSuppUserStage2";
    
    //New Attribute for Hatteras Bandwidth Testing
    public final static String LOOPBACK_PHYS_ADDRESS_PARAM			 = "LoopbackPhysAddress";
    
    //flows
		public final static String FLOW_TYPE_PARAM                       = "FlowType";
		public final static String VLAN_MEMBERS_PARAM                    = "VLANMembers";
		public final static String MULTI_COS_PARAM                       = "MultiCOS";
		public final static String INGRESS_CIR_PARAM                     = "IngressCIR";
		public final static String INGRESS_EIR_PARAM                     = "IngressEIR";
		public final static String EGRESS_RATE_LIMITING_ENABLED_PARAM    = "EgressRateLimitingEnabled";
		public final static String EGRESS_CIR_PARAM                      = "EgressCIR";
		public final static String EGRESS_EIR_PARAM                      = "EgressEIR";
		public final static String CTAG_CONTROL_PARAM                    = "CTagControl";
		public final static String CTAG_VLAN_ID_PARAM                    = "CTagVLANId";
		public final static String CTAG_VLAN_PRIORITY_PARAM              = "CTagVLANPriority";
		public final static String CTAG_MATCH_RX_PRIORITY_PARAM          = "CTagMatchRxPriority";
		public final static String STAG_CONTROL_PARAM                    = "STagControl";
		public final static String STAG_VLAN_ID_PARAM                    = "STagVLANId";
		public final static String STAG_VLAN_PRIORITY_PARAM              = "STagVLANPriority";
    public final static String STAG_MATCH_RX_PRIORITY_PARAM          = "STagMatchRxPriority";
		//chassis
		public final static String ACCESS_TO_ACCESS_LINK_LOSS_FORWARDING_PARAM     = "AccessToAccessLinkLossForwarding";
    public final static String NETWORK_TO_ACCESS_LINK_LOSS_FORWARDING_PARAM    = "NetworkToAccessLinkLossForwarding";
    public final static String PAUSE_FRAMES_ENABLED_PARAM                      = "PauseFramesEnabled";
		public final static String MAC_ADDRESS_LEARNING_PARAM                      = "MACAddressLearning";
		public final static String BPDU_FORWARDING_FILTER_PARAM                    = "BPDUForwardingFilter";
    // cm
    public final static String ADMINISTRATION_CONTROL_PARAM           = "AdministrationControl";
    public final static String SERVICE_STATE_PARAM                    = "ServiceState";
    public final static String SECONDARY_STATE_PARAM                  = "SecondaryState";
    public final static String JUMBO_FRAMES_ENABLED_PARAM             = "JumboFramesEnabled";
    public final static String LINK_LOSS_FWD_ENABLED_PARAM            = "LinkLossFwdEnabled";
    public final static String LINK_LOSS_FWD_SIGNAL_TYPE_PARAM        = "LinkLossFwdSignalType";
    public final static String LINK_LOSS_FWD_TRIGGER_TYPES_PARAM      = "LinkLossFwdTriggerTypes";
    public final static String LINK_LOSS_FWD_DELAY_PARAM              = "LinkLossFwdDelay";
    public final static String LINK_LOSS_FWD_ACTIVE_PARAM             = "LinkLossFwdActive";
    public final static String LINK_LOSS_FWD_PARTNER_ENABLED_PARAM    = "LinkLossFwdPartnerEnabled";
    public final static String TRAFFIC_MANAGEMENT_ENABLED_PARAM       = "TrafficManagementEnabled";
    public final static String SNMP_DYING_GASP_ENABLED_PARAM          = "SNMPDyingGaspEnabled";
    public final static String VOLTAGE                                = "Voltage";
    public final static String TEMPERATURE                            = "Temperature";
    public final static String RECEIVE_PAUSE_FRAMES_DISPOSITION_PARAM = "ReceivePauseFramesDisposition";
	public static final String ALARM_REPORT_CONTROL 				  = "AlarmReportControl";
	//New attrs for the GE20X && CM4.1
	public static final String LINK_LOSS_FWD_TX_ACTION_TYPE_PARAM 	  = "LinkLossFwdTxActionType";
	public static final String LINK_LOSS_FWD_RX_RLD_LINK_IDS_PARAM	  = "LinkLossFwdRxRLDLinkIds"; 
	public final static String MDI_MODE_PARAM 						  = "MdiMode";
	public final static String ACTUAL_MDI_MODE_PARAM 				  = "ActualMdiMode";
	// New Attrs for GE20X Flows
	public static final String TRAFFIC_MANAGEMENT_TYPE_PARAM		  = "TrafficManagementType";
	public static final String ES_FRAMES_LOSS_THRESHOLD_PARAM		  = "ESFramesLossThreshold";
	public static final String SES_FRAMES_LOSS_THRESHOLD_PARAM		  = "SESFramesLossThresholdRatio";
	public static final String FLOW_POLICING_ENABLED_PARAM		  	  = "FlowPolicingEnabled";
    // New Attrs for GX210 Ports
    public static final String PORT_XGE_PHY_TYPE = "XgePhyType";
    // New Attr for Master/Slave clock mode.
    public static final String AUTONEG_CLOCK_MODE = "AutoNegotiationClockMode";
    public static final String AUTO_DIAG_ENABLED = "AutoDiagnostic";
  }

  /**
	 * Parameters of Prop Adva Ethernet EFMOAM layer.
	 */
  public static class LrPropAdvaEthernetEFMOAM {

    public final static String EFM_OAM_ID_PARAM = "EfmOamId";
    public final static String EFM_OAM_ENABLED = "EfmOamEnabled";
    public final static String EFM_OAM_ADMINISTRATION_CONTROL_PARAM = "EfmOamAdministrationControl";
    public final static String EFM_OAM_DISCOVERY_STATE_PARAM = "EfmOamDiscoveryState";
    public final static String EFM_OAM_LOCAL_VAR_RTRVS_ENABLED_PARAM = "EfmOamLocalVarRtrvsEnabled";
    public final static String EFM_OAM_LOCAL_LINK_EVENTS_ENABLED_PARAM = "EfmOamLocalLinkEventsEnabled";
    public final static String EFM_OAM_LOCAL_OAM_LOOPBACKS_SUPPORTED_PARAM = "EfmOamLocalOamLoopbacksSupported";
    public final static String EFM_OAM_LOCAL_UNIDIR_SUPPORT_ENABLED_PARAM = "EfmOamLocalUnidirSupportEnabled";
    public final static String EFM_OAM_LOCAL_MAX_PDU_SIZE_PARAM = "EfmOamLocalMaxPDUSize";
    public final static String EFM_OAM_LOCAL_MODE_PARAM = "EfmOamLocalMode";
    public final static String EFM_OAM_REMOTE_PHYS_ADDRESS_PARAM = "EfmOamRemotePhysAddress";
    public final static String EFM_OAM_REMOTE_VAR_RTRVS_ENABLED_PARAM = "EfmOamRemoteVarRtrvsEnabled";
    public final static String EFM_OAM_REMOTE_LINK_EVENTS_ENABLED_PARAM = "EfmOamRemoteLinkEventsEnabled";
    public final static String EFM_OAM_REMOTE_OAM_LOOPBACKS_SUPPORTED_PARAM = "EfmOamRemoteOamLoopbacksSupported";
    public final static String EFM_OAM_REMOTE_UNIDIR_SUPPORT_ENABLED_PARAM = "EfmOamRemoteUnidirSupportEnabled";
    public final static String EFM_OAM_REMOTE_MAX_PDU_SIZE_PARAM = "EfmOamRemoteMaxPDUSize";
    public final static String EFM_OAM_REMOTE_MODE_PARAM = "EfmOamRemoteMode";
  }

  /**
	 * Parameters of Prop Adca Ethernet Queue layer.
	 */
	public static class LrPropAdvaEthernetQueue {
		public final static String EGRESS_BUFFER_SIZE_PARAM  = "EgressBufferSize";
		public final static String INGRESS_BUFFER_SIZE_PARAM = "IngressBufferSize";
	}

	/**
	 * Parameters of Prop Adca Ethernet Queue layer.
	 */
	public static class LrPropAdvaEthernetPolicer {
		public final static String ADMINISTRATION_CONTROL_PARAM = "AdministrationControl";
		public final static String SERVICE_STATE_PARAM     = "ServiceState";
		public final static String SECONDARY_STATE_PARAM   = "SecondaryState";
		public final static String INGRESS_CIR_PARAM       = "IngressCIR";
		public final static String INGRESS_EIR_PARAM       = "IngressEIR";
		public final static String INGRESS_CBS_PARAM       = "IngressCBS";
		public final static String INGRESS_EBS_PARAM       = "IngressEBS";
		public final static String INGRESS_QUEUE_REF_PARAM = "IngressQueueRef";
		public final static String EGRESS_CIR_PARAM        = "EgressCIR";
		public final static String EGRESS_EIR_PARAM        = "EgressEIR";
		public final static String EGRESS_CBS_PARAM        = "EgressCBS";
		public final static String EGRESS_EBS_PARAM        = "EgressEBS";
		public final static String EGRESS_QUEUE_REF_PARAM  = "EgressQueueRef";
		public final static String POLICING_ALGORITHM_PARAM = "PolicingAlgorithm";
		public final static String COLOR_MODE_PARAM        = "ColorMode";
		public final static String COLOR_MARKING_FLAG_PARAM = "ColorMarkingFlag";
		public final static String COUPLING_FLAG_PARAM     = "CouplingFlag";
	}

  /**
	 * Parameters of Prop Adva Ethernet Shaper layer.
	 */
	public static class LrPropAdvaEthernetShaper {
    public final static String ADMINISTRATION_CONTROL_PARAM = "AdministrationControl";
    public final static String SERVICE_STATE_PARAM          = "ServiceState";
    public final static String SECONDARY_STATE_PARAM        = "SecondaryState";
    public final static String INGRESS_CIR_PARAM            = "IngressCIR";
		public final static String INGRESS_EIR_PARAM            = "IngressEIR";
		public final static String INGRESS_CBS_PARAM            = "IngressCBS";
		public final static String INGRESS_EBS_PARAM            = "IngressEBS";
		public final static String INGRESS_BUFFER_SIZE_PARAM    = "IngressBufferSize";
		public final static String EGRESS_CIR_PARAM             = "EgressCIR";
		public final static String EGRESS_EIR_PARAM             = "EgressEIR";
		public final static String EGRESS_CBS_PARAM             = "EgressCBS";
		public final static String EGRESS_EBS_PARAM             = "EgressEBS";
		public final static String EGRESS_BUFFER_SIZE_PARAM     = "EgressBufferSize";
	}

  /**
	 * Parameters of Lag layer.
	 */
	public static class LrLag{
		public final static String ALLOCATED_NUMBER_PARAM      = "AllocatedNumber";
		public final static String ALLOCATION_MAXIMUM_PARAM    = "AllocationMaximum";
		public final static String FRAGMENT_SERVER_LAYER_PARAM = "FragmentServerLayer";
	}

	/**
	 * Parameters of lag fragment layer.
	 */
	public static class LrLagFragment{
		public final static String LAG_MEMBER_PARAM = "LagMember";
	}

	/**
	 * Parameters of Properties Adva Protection150CP layer.
	 */
	public static class LrPropAdvaProtection150CP{
		public final static String PROTECTION_TYPE_PARAM   = "ProtectionType";
		public final static String PROTECTION_STATUS_PARAM = "ProtectionStatus";
		public final static String ACTIVE_NETWORK_PARAM    = "ActiveNetwork";
	}

  /**
   * Parameters of Properties Adva LAG layer.
   */
  public static class LrPropAdvaLAG {
    public final static String ADMINISTRATION_CONTROL = "AdministrationControl";
    public final static String SERVICE_STATE = "ServiceState";
    public final static String LACP_CONTROL = "LACPControl";
    public final static String ACTOR_SYSTEM_MAC_ADDRESS = "ActorSystemMACAddress";
    public final static String ACTOR_SYSTEM_ID = "ActorSystemID";
    public final static String ACTOR_SYSTEM_PRIORITY = "ActorSystemPriority";
    public final static String ACTOR_ADMINISTRATION_SYSTEM_KEY = "ActorAdministrationSystemKey";
    public final static String ACTOR_OPERATIONAL_SYSTEM_KEY = "ActorOperationalSystemKey";
    public final static String ACTOR_AGGREGATE_CAPABLE = "ActorAggregateCapable";
    public final static String SYSTEM_COLLECTOR_MAX_DELAY = "SystemCollectorMaxDelay";
    public final static String COLLECTOR_MAX_DELAY = "CollectorMaxDelay";
    public final static String PARTNER_SYSTEM_ID = "PartnerSystemID";
    public final static String PARTNER_SYSTEM_PRIORITY = "PartnerSystemPriority";
    public final static String PARTNER_OPERATIONAL_SYSTEM_KEY = "PartnerOperationalSystemKey";
    public final static String PARTNER_COLLECTOR_MAX_DELAY = "PartnerCollectorMaxDelay";    
  }

  /**
   * Parameters of Properties Adva LAG Fragment layer.
   */
  public static class LrPropAdvaLAGFragment {
    public final static String ACTOR_SYSTEM_ID = "ActorSystemID";
    public final static String ACTOR_SYSTEM_PRIORITY = "ActorSystemPriority";
    public final static String ACTOR_ADMINISTRATION_SYSTEM_KEY = "ActorAdministrationSystemKey";
    public final static String ACTOR_OPERATIONAL_SYSTEM_KEY = "ActorOperationalSystemKey";
    public final static String PARTNER_ADMINISTRATION_SYSTEM_ID = "PartnerAdministrationSystemID";
    public final static String PARTNER_OPERATIONAL_SYSTEM_ID = "PartnerOperationalSystemID";
    public final static String PARTNER_ADMINISTRATION_SYSTEM_PRIORITY = "PartnerAdministrationSystemPriority";
    public final static String PARTNER_OPERATIONAL_SYSTEM_PRIORITY = "PartnerOperationalSystemPriority";
    public final static String PARTNER_ADMINISTRATION_SYSTEM_KEY = "PartnerAdministrationSystemKey";
    public final static String PARTNER_OPERATIONAL_SYSTEM_KEY = "PartnerOperationalSystemKey";
    public final static String ACTOR_LACP_CONTROL = "ActorLACPControl";
    public final static String ACTOR_STATE = "ActorState";
    public final static String SELECTED_AGGEGATOR_ID = "SelectedAggegatorID";
    public final static String ATTACHED_AGGREGATOR_ID = "AttachedAggregatorID";
    public final static String ACTOR_PORT_PRIORITY = "ActorPortPriority";
    public final static String ACTOR_LACP_ACTIVITY = "ActorLACPActivity";
    public final static String ACTOR_LACP_TIMEOUT = "ActorLACPTimeout";
    public final static String ACTOR_SERVICE_STATE = "ActorServiceState";
    public final static String PARTNER_ADMINISTRATION_PORT_PRIORITY = "PartnerAdministrationPortPriority";
    public final static String PARTNER_OPERATIONAL_PORT_PRIORITY = "PartnerOperationalPortPriority";
    public final static String PARTNER_ADMINISTRATION_PORT_NUMBER = "PartnerAdministrationPortNumber";
    public final static String PARTNER_OPERATIONAL_PORT_NUMBER = "PartnerOperationalPortNumber";
    public final static String PARTNER_LACP_ACTIVITY = "PartnerLACPActivity";
    public final static String PARTNER_LACP_TIMEOUT = "PartnerLACPTimeout";
    public final static String PARTNER_SERVICE_STATE = "PartnerServiceState";
    public final static String AGGREGATE_OR_INDIVIDUAL = "AggregateOrIndividual";
    public final static String PARTNER_COLLECTOR_MAX_DELAY = "PartnerCollectorMaxDelay";
  }

  /**
   * Parameters of Properties Adva LAG PROTECTION FSP150 1PLUS1.
   */
  public static class LrPropAdvaProtectionFSP1501PLUS1 {
    public final static String MEMBER_TYPE_PARAM = "MemberType";
    public final static String MEMBER_STATE_PARAM = "MemberState";
  }

  /**
   * Parameters of PROP_ADVA_ProtectionFSP150CMNTU layer.
   */
  public static class LrPropAdvaProtectionFSP150CMNTU {
    public static final String PROTECTION_SWITCH_MODE = "ProtectionSwitchMode";
    public static final String REVERTIVE = "Revertive";
    public static final String WAIT_TO_RESTORE = "WaitToRestore";
    public static final String SWITCH_DIRECTION = "SwitchDirection";
    public static final String PROTECTION_STATUS = "ProtectionStatus";
    public static final String MACADDRESS = "MACAddress";
  }

  /**
   * Parameters for the PROP_ADVA_RemoteCPE layer in the
   * provisionEquipment request.
   * <AUTHOR>
   *
   */
  public static class LrPropAdvaRemoteCPE {
    public final static String CPE_HOSTNAME_PARM = "CPEHostname";
    public final static String IP_ADDRESS = "IpAddress";
    public final static String SUBNET_MASK="SubnetMask";
    public final static String REMOTE_TYPE_PARM  = "RemoteType";
    public final static String DEFAULT_GATEWAY = "DefaultGateway";
    public final static String CREATE_LINK_PARM  = "CreateLink";
    public final static String LINK_NAME_PARM    = "LinkName";
  }

  public static class LrPropAdvaTunnel {
    public final static String  TUNNEL_INDEX="TunnelIndex";
    public final static String  TUNNEL_TYPE ="TunnelType";
    public final static String  IP_ADDRESS="IPAddress";
    public final static String  SUBNET_MASK="SubnetMask";
    public final static String  DEFAULT_GATEWAY="DefaultGateway";
    public final static String  VLANID="VLANID";
    public final static String  CIR="CIR";
    public final static String  EIR="EIR";
    public final static String  COS="COS";
    public final static String  BUFFER_SIZE="BufferSize";
    public final static String  S_VLANID_ENABLED="SVLANIDEnabled";
    public final static String  S_TAG_VLANID="STagVLANID";
    public final static String  RIP_2_PKTS_ENABLED="Rip2PktsEnabled";
    public final static String  ENCAPSULATION_TYPE="EncapsulationType";
    public final static String  MTU="MTU";


  }
  /**
   * Parameters of PROP_ADVA_CFMCCM layer.
   */
  public static class LrPropAdvaCFMCCM
  {
    public final static String THIS_LAYER_ACTIVE_PARAM               = "ThisLayerActive";
    public final static String CFM_MD_NAME_PARAM                     = "MdName";
    public final static String CFM_MD_LEVEL_PARAM                    = "MdLevel";
    public final static String CFM_MD_MIP_CREATION                   = "MdMipCreationControl";
    public final static String CFM_MA_NAME_PARAM                     = "MaName";
    public final static String CFM_MA_CCM_INTERVAL_PARAM             = "MaCcmInterval";
    public final static String CFM_MA_NUMBER_OF_VIDS_PARAM           = "MaNumberOfVids";
    public final static String CFM_MA_PRIMARY_VID_PARAM              = "MaPrimaryVid";
    public final static String CFM_MA_MIP_CONTORL_PARAM              = "MaMipCreationControl";
    public final static String CFM_MA_VLAN_TABLE_PARAM               = "MaVLANList";
    public final static String CFM_MA_MEP_TABLE_PARAM                = "MaMepList";
    public final static String CFM_MEP_IDENTIFIER_PARAM              = "MepIdentifier";
    public final static String CFM_MEP_DIRECTION_PARAM               = "MepDirection";
    public final static String CFM_MEP_ADMIN_STATE_PARAM             = "MepAdministrationControl";
    public final static String CFM_MEP_SERVICE_STATE_PARAM           = "MepServiceState";
    public final static String CFM_MEP_PRIMARY_VID_PARAM             = "MepPrimaryVid";
    public final static String CFM_MEP_ACTIVE_PARAM                  = "MepActive";
    public final static String CFM_MEP_CCI_ENABLED_PARAM             = "MepCcmGeneration";
    public final static String CFM_MEP_CCM_LTM_PRIORITY_PARAM        = "MepVlanPriority";
    public final static String CFM_MEP_LOWEST_PRIORITY_DEFECT_PARAM  = "MepLowestPriorityDefect";
    public final static String CFM_MEP_HIGHEST_PRIORITY_DEFECT       = "MepHighestPriorityDefect";
    public final static String CFM_MEP_MAC_ADDRESS_PARAM             = "MepMacAddress";
    public final static String CFM_MEP_XCON_CCM_DEFECT               = "MepCcmMisconnectionDefectReceived";
    public final static String CFM_MEP_RDI_DEFECT_RECEIVED           = "MepRdiDefectReceived";
    public final static String CFM_MEP_MAC_DEFECT_RECEIVED           = "MepMacDefectReceived";
    public final static String CFM_MEP_RMEP_CCM_DEFECT_RECEIVED      = "MepRemoteMepCcmDefectReceived";
    public final static String CFM_MEP_CCM_ERROR_DEFECT_RECEIVED     = "MepCcmErrorDefectReceived";
    public final static String CFM_MEP_LLF_TRIGGER                   = "MepLlfTrigger";
    public final static String CFM_MEP_AIS_GENERATION                = "MepAisGeneration";
    public final static String CFM_MEP_AIS_TRIGGER                   = "MepAisTrigger";
    public final static String CFM_MEP_AIS_CLIENT_MD_LEVEL           = "MepAisClientMdLevel";
    public final static String CFM_MEP_AIS_PRIORITY                  = "MepAisPriority";
    public final static String CFM_MEP_AIS_TRANSMISSION_INTERVAL     = "MepAisTransmissionInterval";
    public final static String CFM_MEP_ERROR_CCM_LAST_FAILURE        = "MepLastErrorCcmReceived";
    public final static String CFM_MEP_XCON_CCM_LAST_FAILURE         = "MepLastXConCcmReceived";
    public final static String CFM_MEP_SOME_RDI_DEFECT               = "MepRdiDefectReceived";
    public final static String CFM_MEP_ERR_MAC_STATUS                = "MepMacDefectReceived";
    public final static String CFM_MEP_SOME_R_MEP_CCM                = "MepRemoteMepCcmDefectReceived";
    public final static String CFM_MEP_ERROR_CCM_DEFECT              = "MepCcmErrorDefectReceived";
    public final static String CFM_MEP_DEFECTS                       = "MepAisDefectReceived";

    public final static String CFM_RMEP_IDENTIFIER_PARAM              = "RMepIdentifier";
    public final static String CFM_RMEP_STATE                         = "RMepState";
    public final static String CFM_RMEP_FAILED_OK_TIME                = "RMepFailedOkTime";
    public final static String CFM_RMEP_MAC_ADDRESS                   = "RMepMacAddress";
    public final static String CFM_RMEP_RDI                           = "RMepRDI";
    public final static String CFM_RMEP_PORT_STATUS_TLV               = "RMepPortStatusTlv";
    public final static String CFM_RMEP_INTERFACE_STATUS_TLV          = "RMepInterfaceStatusTlv";
  }
  /**
	 * Parameters of LR_Optical_Channel Layer.
	 * 
	 */
	public static class LROpticalChannel {
		public final static String SFP_REACH_PARAM 					= "SfpReach";
		public final static String SFP_LASER_WAVE_LENGTH_PARAM 		= "SfpLaserWaveLength";
		public final static String SFP_MEDIA_TYPE_PARAM 			= "SfpMediaType";
		public final static String SFP_VENDOR_NAME_PARAM 			= "SfpVendorName";
		public final static String SFP_PART_NUMBER_PARAM 			= "SfpPartNumber";
		public final static String SFP_SERIAL_NUMBER_PARAM 			= "SfpSerialNumber";
		public final static String SFP_DATE_OF_MANUFACTURE_PARAM 			= "SfpDateOfManufacture";
	}
	  /**
	 * Parameters of PROP_ADVA_SyncEthernet Layer.
	 * 
	 */
	public static class PropAdvaSyncEthernet {
		public final static String SYNCE_ADMINISTRATION_CONTROL_PARAM 	= "SyncEAdministrationControl";
		public final static String QL_MODE_ADMINISTRATION_CONTROL_PARAM = "QLModeAdministrationControl";
		public final static String EXPECTED_QL_PARAM 					= "ExpectedQL";
		public final static String ASSUMED_QL_PARAM 					= "AssumedQL";
		public final static String RECEIVED_QL_PARAM 					= "ReceivedQL";
		public final static String TRANSMIT_QL_PARAM 					= "TransmitQL";
		public final static String SQUELCH_QL_PARAM 					= "SquelchQL";
		public final static String SYNCE_AUTO_NEGOTIATION_CLOCK_PARAM	= "SyncEAutoNegotiationClockMode";
		}
	
	/**
	 * Parameters of PROP_ADVA_BITS Layer.
	 * 
	 */
	public static class PropAdvaBits {
		public static final String ADMINISTRATION_CONTROL_PARAM 	= "AdministrationControl";
		public static final String SERVICE_STATE_PARAM 				= "ServiceState";
		public static final String SECONDARY_STATE_PARAM 			= "SecondaryState";
		public static final String LINE_TYPE_PARAM 					= "LineType";
		public static final String LINE_CODE_PARAM 					= "LineCode";
		public static final String FRAME_FORMAT_PARAM 				= "FrameFormat";
		public static final String SA_BIT_DESIGNATION_PARAM 		= "SABitDesignation";
    public static final String LINE_BUILD_OUT_PARAM        = "LineBuildOut";
    public static final String IMPEDANCE_PARAM        = "Impedance";
	}

  /**
   * Parameters of PROP_ADVA_TIMING_DOMAIN Layer.
   *
   */
  public static class PropAdvaTimingDomain {
    public static final String ALIAS                  = "Alias";
    public static final String ALLOCATED_NUMBER       = "AllocatedNumber";
    public static final String ALLOCATED_MAXIMUM      = "AllocatedMaximum";
    public static final String ADMINISTRATION_CONTROL = "AdministrationControl";
    public static final String SERVICE_STATE          = "ServiceState";
    public static final String SECONDARY_STATE        = "SecondaryState";
    public static final String NETWORK_CLOCK_TYPE     = "NetworkClockType";
    public static final String SYNC_DOMAIN_TYPE       = "SyncDomainType";
    public static final String SELECTED_REFERENCE     = "SelectedReference";
    public static final String SYNC_CLOCK_MODE        = "SyncClockMode";
    public static final String SSM_QUALITY_LEVEL      = "SSMQualityLevel";
    public static final String SYNC_SELECTION_MODE    = "SyncSelectionMode";
    public static final String SYNC_WTR_TIME          = "SyncWTRTime";
  }

  /**
   * Parameters of PROP_ADVA_Sync_Reference Layer.
   *
   */
  public static class PropAdvaSyncReference {
    public static final String SYNC_REFERENCE         = "SyncReference";
    public static final String ALIAS                  = "Alias";
    public static final String REF_PRIORITY           = "RefPriority";
    public static final String REF_STATUS             = "RefStatus";
    public static final String REF_STATE              = "RefState";
    public static final String REF_RECEIVED_QL        = "RefReceivedQL";
    public static final String REF_EFFECTIVE_QL       = "RefEffectiveQL";
  }

	public static class PropAdvaRemoteCPE {
		public static final String CPE_HOST_NAME = "CPEHostname";
		public static final String REMOTE_TYPE = "RemoteType";
		public static final String CREATE_LINK = "CreateLink";
		public static final String LINK_NAME = "LinkName";
	}
}
