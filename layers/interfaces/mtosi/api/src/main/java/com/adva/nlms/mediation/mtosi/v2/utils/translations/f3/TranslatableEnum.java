/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v2.utils.translations.f3;

public interface TranslatableEnum {
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue();

  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString();
}
