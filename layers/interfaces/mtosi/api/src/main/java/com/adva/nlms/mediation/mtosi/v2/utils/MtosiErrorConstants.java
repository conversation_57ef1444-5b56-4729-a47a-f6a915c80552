/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v2.utils;

/**
 * Class providing exception reason strings used by mtosi layer when an exception is thrown.
 */
public class MtosiErrorConstants {
  public static final String CANNOT_MODIFY_EXISTING_FLOW = " cannot be modified on an existing Flow Point CTP.";
  public static final String MANDATORY = " is a mandatory parameter.";
  public static final String MANDATORY_FOR_NEW_FLOW = " is mandatory for a new Flow Point CTP.";

  public static final String CONTAINS_ILLEGAL_VALUE = " contains an illegal value.";
  public final static String STANDBY_SERVER_OPERATION_NOT_SUPPORTED = "Operation not supported on the standby server.";
  public final static String JAXB_MARSHALLING_PROBLEM = "Failed to successfully unmarshall SOAP request (JAXB Unmarshalling).";
  public static final String MTOSI_OPERATION_NOT_SUPPORTED_FOR_DEVICE_TYPE = "The requested operation is not supported for this Managed Element type.";
  public static final String MTOSI_OPERATION_NOT_SUPPORTED_XML_IS_INVALID = "The requested operation is not supported. The XML configuration file is not valid.";
  public static final String MTOSI_OPERATION_NOT_SUPPORTED_XML_WAS_NOT_FOUND = "The requested operation is not supported. The XML configuration file is not found.";
  public static final String MTOSI_OPERATION_NOT_SUPPORTED = "The requested operation is not implemented."; //implies NO entry in the xml file for the specified operation.
  public static final String MD_NAME_MISSING = "The Management Domain name must be specified in the request.";
  public static final String CPE_HOST_NAME_MISSING = "The host name must be specified in the request.";
  public static final String EFM_REMOTE_ME_NOT_FOUND   = "The specified Remote Managed Element was not found.";
  public static final String EFM_REMOTE_TYPE_NOT_VALID   = "The specified Remote Type is not valid.";
  public static final String OS_NAME_MISSING = "The Operation System name must be specified in the request.";
  public static final String TL_NAME_MISSING = "The Topological Link name must be specified in the request.";
  public static final String MD_NOT_FOUND = "The specified Management Domain was not found.";
  public static final String OS_NOT_FOUND = "The specified Operation System was not found.";
  public static final String ME_NAME_MISSING = "The Managed Element name must be specified in the request.";
  public static final String MLNS_NAME_MISSING = "The MultiLayer Subnetwork name must be specified in the request.";
  public static final String MDFR_NAME_MISSING = "The Maintenance Domain name must be specified in the request.";
  public static final String BC_NAME_MISSING = "The Boundary Clock name must be specified in the request.";
  public static final String MCI_NAME_MISSING = "The Master Clock Interface name must be specified in the request.";
  public static final String MVP_NAME_MISSING = "The Master Virtual Port name must be specified in the request.";
  public static final String SRS_NAME_MISSING = "The Static Remote Slave name must be specified in the request.";
  public static final String MDFR_PTP_NAME_MISSING = "The Physical Termination Point name of the Maintenance Association must be specified in the request.";
  public static final String MDFR_PTP_INVALID = "The specified Maintenance Association name is invalid.";
  public static final String BC_NAME_INVALID = "The specified Boundary Clock name is invalid.";
  public static final String MCI_NAME_INVALID = "The specified Master Clock Interface name is invalid.";
  public static final String MVP_NAME_INVALID = "The specified Master Virtual Port name is invalid.";
  public static final String SRS_NAME_INVALID = "The specified Static Remote Slave name is invalid.";
  public static final String MAFR_NAME_MISSING = "The Maintenance Association name must be specified in the request.";
  public static final String MEPFR_MIPFR_NAME_MISSING = "The Maintenance End Point name must be specified in the request.";
  public static final String MEP_TABLE_MISSING = "The MaMepList list must be specified in the request.";
  public static final String MEP_TABLE_IVALID = "The MaMepList list is invalid.";
  public static final String ME_NAME_IS_DIFFERENT = "The Managed Element must be the same for every pmObjectSelect.";
  public static final String PTP_NAME_IS_DIFFERENT = "The specified Physical Termination Point must be the same for every pmObjectSelect.";
  public static final String CTP_TYPE_INVALID = "The Connection Termination Point Type is invalid.";
  public static final String FDRF_NAME_MISSING = "The Flow Domain Fragment name must be specified in the request.";
  public static final String CTP_NAME_MISSING = "The Connection Termination Point name must be specified in the request.";
  public static final String FTP_NAME_MISSING = "The Floating Termination Point name must be specified in the request.";
  public static final String FTP_LAG_NAME_MISSING = "The Floating Termination Point or Link Aggregation Group name must be specified in the request.";
  public static final String TP_NAME_MISSING = "The Termination Point name must be specified in the request.";
  public static final String TCP_NAME_MISSING = "The Traffic Conditioning Profile name must be specified in the request.";
  public static final String PTPC_NAME_MISSING = "The PTP Clock name must be specified in the request.";
  public static final String PTPP_NAME_MISSING = "The PTP Port name must be specified in the request.";
  public static final String PTPFP_NAME_MISSING = "The PTP Flow Point name must be specified in the request.";
  public static final String FLOW_ID_MISSING = "Service parameter is missing in the request.";
  public static final String FDRF_NOT_FOUND = "The specified Flow Domain Fragment was not found.";
  public static final String MDRF_NOT_FOUND = "The specified Maintenance Domain was not found.";
  public static final String BC_NOT_FOUND = "The specified Boundary Clock was not found.";
  public static final String PTPFP_NOT_FOUND = "The specified PTP Flow Point was not found.";
  public static final String PTPC_NOT_FOUND = "The specified PTP Clock was not found.";
  public static final String PTPP_NOT_FOUND = "The specified PTP Port was not found.";
  public static final String MCI_NOT_FOUND = "The specified Master Clock Interface was not found.";
  public static final String MVP_NOT_FOUND = "The specified Master Virtual Port was not found.";
  public static final String SRS_NOT_FOUND = "The specified Static Remote Slave was not found.";
  public static final String ME_NOT_FOUND = "The specified Managed Element was not found.";
  public static final String PTP_NOT_FOUND = "The specified Physical Termination Point was not found.";
  public static final String TP_NOT_FOUND = "The specified Termination Point was not found.";
  public static final String PTP_NOT_FOUND_INVALID = "The specified Physical Termination Point was not found or is invalid.";
  public static final String CTP_NOT_FOUND = "The specified Connection Termination Point was not found.";
  public static final String CTP_NOT_VALID = "The specified Flow is not valid.";
  public static final String VENDOR_NOT_SPECIFIED = "The vendor extensions has not been specified.";
  public static final String PTP_INVALID = "The specified Physical Termination Point is invalid.";
  public static final String TCPProfile_INVALID = "The specified Traffic Conditioning Profile was not found.";
  public static final String NOT_DISCOVERED = "The Managed Element has not yet been discovered.";
  public static final String ME_NOT_SUPPORTED = "The requested operation is not supported for this Managed Element type.";
  public static final String ITERATOR_NOT_FOUND = "The specified iterator was not found or is no longer active.";
  public static final String MANUFACTURER_NOT_FOUND = "The specified manufacturer was not found.";
  public static final String PRODUCT_NAME_NOT_FOUND = "The specified product was not found.";
  public static final String MD_INVALID_NAME = "The specified Management Domain value is not available.";
  public static final String REQUESTED_BATCH_SIZE_MISSING = "Requested Batch Size is missing.This is mandatory attribute.";
  public static final String ITERATOR_URI_MISSING = "Iterator URI is missing. This is mandatory attribute.";
  public static final String INVALID_FILTER = "An invalid filter definition was encountered. NamingAttributes type are not specified.";
  public static final String EH_AND_EQ_NAME_MISSING = "The Equipment Holder or Equipment name must be specified in the request.";
  public static final String EQ_NAME_MISSING = "The Equipment name must be specified in the request.";
  public static final String EH_NAME_MISSING = " The Equipment Holder name must be specified in the request.";
  public static final String EH_NOT_SUPPORTED = "The specified Equipment Holder is not supported.";
  public static final String EQ_NOT_SUPPORTED = "The specified Equipment is not supported.";
  public static final String INVALID_EQ_VALUE = "The specified Equipment value is not valid.";
  public static final String STATE_FILTER_NOT_SUPPORTED = "This state filter value is not supported.";
  public static final String STATE_FILTER_VALUE_NOT_VALID = "This state filter value is not valid.";
  public static final String MANDATORY_PTP_CTP_FTP = "A properly formatted PTP, CTP, or FTP must be specified.";
  public static final String MTOSI_NOT_SUPPORTED = "The specified entity is not supported by MTOSI.";
  public static final String TDFR_NAME_INVALID        = "The TDFr name is invalid.";
  public static final String CTP_NAME_INVALID        = "The Connection Termination Point name is invalid.";
  public static final String ME_NAME_NOT_CORRECT        = "The specified Managed Element is not correct.";
  public static final String SYNC_REF_NAME_INVALID        = "The SyncReference name is invalid.";
  public static final String SYNC_REF_NAME_MISSING        = "The SyncReference name is missing.";
  public static final String SYNC_REF_MISSING        = "The SyncReference is missing.";
  public static final String TDFR_NAME_MISSING        = "The Time Domain Fragment name must be specified in the request.";
  public static final String TDFR_NOT_EXIST        = "The specified Time Domain Fragment does not exist";
  public static final String PTPFP_NOT_EXIST        = "The specified PTP Flow Point does not exist";
  public static final String PTPFP_NOT_EXIST_INVALID        = "The specified PTP Flow Point does not exist or is invalid";
  public static final String TDFR_DATA_MISSING        = "The Time Domain Fragment Modify Data are missing.";
  public static final String CTP_DATA_MISSING        = "The Connection Termination Point Data are missing.";
  public static final String SYNCE_NOT_SUPPORTED        = "The specified Managed Element does not support SyncE.";
  public static final String TDFR_NOT_FOUND        = "The specified Timing Domain Fragment was not found.";
  public static final String MDFR_NOT_FOUND        = "The specified Maintenance Domain was not found.";
  public static final String MANETFR_NOT_FOUND        = "The specified Maintenance Association Network was not found for the specified Maintenance Domain.";
  public static final String MANET_HAS_MACOMP        = "The specified Maintenance Association Network already has a Maintenance Association Component.";
  public static final String MACOMPFR_NOT_FOUND        = "The specified Maintenance Association Component was not found for the specified Maintenance Association Network.";
  public static final String MACOMPFR_FULL_NOT_FOUND        = "The specified Maintenance Association was not found.";
  public static final String MEP_LIST_MISMATCH        = "The specified MEP id was not found in the Maintenance Association list.";
  public static final String MEP_LIST_NOT_FOUND        = "The specified MEP was not found.";
  public static final String TO_MANY_PARAMETERS_DEFINED = "To many parameters are defined in the request.";
  public static final String PTP_NAME_MISSING = "The Physical Termination Point name must be specified in the request.";
  public static final String PTP_FTP_NAME_MISSING = "The PTP or FTP name must be specified in the request.";
  public static final String PTPsMISSING =      "The PTPs must be specified in the tpDataListToModify.";
  public static final String ENTITY_MISSING = "The specified entity does not exist.";
  public static final String NE_MISSING = "The specified Network Element does not exist.";
  public static final String SNC_IS_MISSING = "SNC mtosi name is not defined.";
  public static final String SNC_NAME_IS_MISSING = "The subnetowrk connection name must be specified in the request.";
  public static final String MLSN_NAME_IS_MISSING = "The MLSN name must be specified in the request.";
  public static final String MLSN_NOT_FOUND = "The specified MLSN was not found.";
  public static final String TL_NOT_FOUND = "The specified Topological Link was not found.";
  public static final String TPL_LIST_EMPTY = "The termination point layer list is empty.";
  public static final String SHELF_NOT_FOUND = "The specified shelf was not found.";
  public static final String SLOT_NOT_FOUND = "The specified slot was not found.";
  public static final String SUB_SLOT_NOT_FOUND = "The specified sub slot was not found.";
  public static final String SHELF_NAME_WRONG = "Shelf name cant have equipment part.";
  public static final String EQ_NOT_FOUND = "The specified equipment was not found.";
  public static final String PSU_NOT_FOUND = "The specified PSU has not been provisioned.";
  public static final String FAN_NOT_FOUND = "The specified FAN has not been provisioned.";
  public static final String BI_NOT_FOUND = "The Base Instance refers to an entity that does not exist.";
  public static final String EQ_NOT_ALLOWED = "The Equipment is not allowed in this operation.";
  public static final String VERSION_INFO_NOT_SUPPORTED="Version info not supported";
  public static final String ENTITY_NOT_SUPPORTED="Not supported MTOSI v2 entity: ";
  public static final String PM_PARAM_NOT_SUPPORTED="Supported PM parameter names are PMP_RPL and PMP_TPL";
  public static final String EH_PORT_INVALID="Port name is not allowed at Equipment Holder";
  public static final String MISSING_FTPNM = "The protection group name has not been specified.";
  public static final String INVALID_FTPNM = "The specified protection group name is invalid.";
  public static final String FTP_NOT_FOUND = "The specified protection group was not found.";
  public static final String MISSING_LAGNM = "The Link Aggregation Group name has not been specified.";
  public static final String INVALID_LAGNM = "The specified Link Aggregation Group name is invalid.";
  public static final String LAGNM_NOT_FOUND = "The specified Link Aggregation Group was not found.";
  public static final String INVALID_COMBINATION_CTP_AND_PTP = "Invalid combination between CTP and PTP.";
  public static final String MISSING_OR_INVALID_ALLOCATED_NUMBER = "The AllocatedNumber has not been specified or is invalid.";
  public static final String MISSING_OR_INVALID_FRAGMENT_SERVER_LAYER = "The FragmentServerLayer parameter specified is invalid or missing.";
  public static final String MISSING_OR_INVALID_PROTECTION_SWITCH_MODE = "The ProtectionSwitchMode parameter specified is invalid or missing.";
  public static final String INVALID_REVERTIVE_ATTRIBUTE = "The Revertive parameter specified is invalid." ;
  public static final String MISSING_PG_PORTS = "One or more protection group ports are not specified." ;
  public static final String TooMany_PG_PORTS = "More than two protection group ports are specified." ;
  public static final String MISSING_OR_INVALID_PORT_ME_NAME = "The protection group port's Managed element Name is invalid or missing.";
  public static final String MISSING_OR_INVALID_PORT_NAME = "The protection group port's Name is invalid or missing.";
  public static final String PORT_NOT_FOUND = "Specified port has not been provisioned.";
  public static final String MISSING_OR_INVALID_PORT_MD_NAME = "The protection group port's specified Management Domain was not found.";
  public static final String PROTECTION_GROUP_EXISTS = "Protection Group already exists.";
  public static final String FDFR_EXISTS = "Floating domain fragment already exists.";
  public static final String PTPFP_FDFR_EXISTS = "The specified PTP Flow Point already exists.";
  public static final String PTPC_EXISTS = "The specified PTP Clock already exists.";
  public static final String PTPP_EXISTS = "The specified PTP Port already exists.";
  public static final String PTPC_NOT_EXIST = "The specified PTP Clock does not exist";
  public static final String PTPP_NOT_EXIST = "The specified PTP Port does not exist";
  public static final String MDFR_EXISTS = "The specified Maintenance Domain already exists.";
  public static final String BC_EXISTS = "The specified Boundary Clock already exists.";
  public static final String MCI_EXISTS = "The specified Master Clock Interface already exists.";
  public static final String MVP_EXISTS = "The specified Master Virtual Port already exists.";
  public static final String MANETFR_EXISTS = "The specified Maintenance Association Network already exists.";
  public static final String MACOMPFR_EXISTS = "The specified Maintenance Association Component already exists.";
  public static final String MEPPFR_EXISTS = "The specified Maintenance Association Endpoint already exists.";
  public static final String EQ_EXISTS = "The specified Equipment already exists.";
  public static final String MISSING_TRANSMISSION_PARAMETER = "Required transmission parameter(s) missing";
  public static final String WORKING_PROTECTION_PORT_MATCH = "Working and protection port cannot be the same.";
  public static final String FLOATING_TERMINATION_POINT_DISCOVERY_FAILURE = "The specified Floating Termination Point does not exist.";
  public static final String TERMINATION_POINT_DISCOVERY_FAILURE = "Failed to update termination point data.";
  public static final String CPD_NOT_FOUND = "The specified associated Cpd profile was not found.";
  public final static String ADMINISTRATION_CONTROL_ILLEGAL_VALUE = "AdministrationControl contains an illegal value.";
  public final static String NETWORK_CLOCK_TYPE_ILLEGAL_VALUE = "NetworkClockType contains an empty or illegal value.";
  public final static String SYNC_DOMAIN_TYPE_ILLEGAL_VALUE = "SyncDomainType contains an illegal value.";
  public final static String SYNC_SELECTION_MODE_ILLEGAL_VALUE = "SyncSelectionMode contains empty or illegal value.";
  public final static String SYNC_WRT_TIME_ILLEGAL_VALUE = "SyncWTRTime contains empty or illegal value.";
  public final static String LOCAL_PRIORITY_ILLEGAL_VALUE = "LocalPriority contains empty or illegal value";
  public final static String PRIORITY2_ILLEGAL_VALUE = "Priority2 contains empty or illegal value";
  public final static String CIR_ILLEGAL_VALUE = "CIR contains empty or illegal value";
  public final static String DEF_GATE_ILLEGAL_VALUE = "Error with Default Gateway value";
  public final static String EIR_ILLEGAL_VALUE = "EIR contains empty or illegal value";
  public final static String COS_ILLEGAL_VALUE = "COS contains empty or illegal value";
  public final static String VLAN_ID_ILLEGAL_VALUE = "VLANId contains empty or illegal value";
  public final static String BUFFER_SIZE_ILLEGAL_VALUE = "BufferSize contains empty or illegal value";
  public final static String STAG_VLAN_ID_ILLEGAL_VALUE = "STagVLANId contains empty or illegal value";
  public final static String DOMAIN_NUMBER_ILLEGAL_VALUE = "DomainNumber contains empty or illegal value";
  public final static String MAX_STATIC_SLAVES_SUPPORTED_ILLEGAL_VALUE = "MaxStaticSlavesSupported contains empty or illegal value";
  public final static String MAX_SLAVES_SUPPORTED_ILLEGAL_VALUE = "MaxSlavesSupported contains empty or illegal value";
  public final static String MASTER_IP_V4_ADDRESS_ILLEGAL_VALUE = "MasterIpV4Address contains empty or illegal value";
  public final static String IP_V4_ADDRESS_ILLEGAL_VALUE = "IPV4Address contains empty or illegal value";
  public final static String MASTER_IP_V4_SUBNET_MASK_ILLEGAL_VALUE = "MasterIpV4SubnetMask contains empty or illegal value";
  public final static String CLOCK_SYNC_ILLEGAL_VALUE = "ClockSync contains empty or illegal value";
  public final static String ANNOUNCE_RECEIPT_TIMEOUT_ILLEGAL_VALUE = "AnnounceReceiptTimeout contains empty or illegal value";
  public final static String SYNC_RECEIPT_TIMEOUT_ILLEGAL_VALUE = "SyncReceiptTimeout contains empty or illegal value";
  public final static String DELAY_RESP_TIMEOUT_ILLEGAL_VALUE = "DelayRespTimeout contains empty or illegal value";
  public final static String SYNC_REF_NOT_FOUND = "The specified SyncReference was not found.";
  public final static String CTP_EXIST = "The specified Connection Termination Point already exists.";
  public final static String CTP_NOT_EXIST = "The specified Connection Termination Point do not exist.";
  public static final String CTAG_VLAN_ID_MANDATORY = "CTagVLANId is mandatory when CTagControl is not Push";
  public static final String CTAG_VLAN_PRIORITY_MANDATORY = "CTagVLANPriority is mandatory when CTagControl is Push";
  public static final String STAG_VLAN_ID_MANDATORY = "STagVLANId is mandatory when STagControl is not Push";
  public static final String STAG_VLAN_PRIORITY_MANDATORY = "STagVLANPriority is mandatory when STagControl is Push";
  public final static String SPEED_PORT_INVALID_VALUE = "Invalid port speed combination(AdministrativeSpeedRate, DuplexMode).";
  public final static String MAXIMUM_FRAME_SIZE_INVALID_VALUE = "MaximumFrameSize contains an invalid value.";
  public final static String ADMINISTRATION_CONTROL_INVALID_VALUE = "AdministrationControl contains an invalid value.";
  public final static String MULTI_COS_INVALID_VALUE = "MultiCOS contains an invalid value.";
  public final static String COS_INVALID_VALUE = "COS contains an invalid value.";
  public final static String CTAG_CONTROL_INVALID_VALUE = "CTagControl contains an invalid value.";
  public final static String STAG_CONTROL_INVALID_VALUE = "STagControl contains an invalid value.";
  public final static String VLAN_MEMBERS_INVALID_VALUE = "VLANMembers contains an invalid value.";
  public static final String MISSING_OR_INVALID_TP_NAME = "The termination port name is invalid or missing.";
  public static final String INVALID_WAIT_TO_RESTORE_ATTRIBUTE = "The waitToRestore parameter has not been specified or is invalid.";
  public static final String INVALID_SWITCH_TO_RESTORE_ATTRIBUTE = "The switchToRestore parameter is invalid.";
  public static final String READ_COMMUNITY_NOT_ALLOWED   = "Read Community parameter is not allowed when UseGlobalSNMPSettings is not present";
  public static final String WRITE_COMMUNITY_NOT_ALLOWED   = "Write Community parameter is not allowed when UseGlobalSNMPSettings is not present";
  public static final String PROTOCOL_VERSION_NOT_ALLOWED   = "Protocol Version parameter is not allowed when UseGlobalSNMPSettings is not present";
  public static final String PROTOCOL_VERSION_NOT_ALLOWED_WITH_USE_GLOBAL   = "Protocol Version parameter is not allowed when UseGlobalSNMPSettings is beeing set to True";
  public static final String READ_COMMUNITY_NOT_ALLOWED_WITH_USE_GLOBAL   = "Read Community parameter is not allowed when UseGlobalSNMPSettings is beeing set to True";
  public static final String WRITE_COMMUNITY_NOT_ALLOWED_WITH_USE_GLOBAL   = "Write Community parameter is not allowed when UseGlobalSNMPSettings is beeing set to True";

  public static final String TC_PROFILE_NAME_MISSING = "The TC Profile name must be specified in the request.";
  public static final String TC_PROFILE_NAME_NOT_VALID = "The TCProfile name is invalid.";
  public static final String TC_PROFILE_NOT_FOUND = "The specified Traffic Condition Profile was not found.";

  public static final String PTP_ILLEGAL = "Specified PTP is illegal for that operation.";

  public final static String INVALID_EMPTY_VALUE = " contains an invalid or empty value.";
  public final static String INVALID_VALUE = " contains an invalid value";
  public final static String INVALID_VALUE_COMBINATION = "Invalid value combination for fields";
  public static final String INVALID_PTP_NAME = "Mtosi name is not a valid PTP name.";
  public static final String LAYER_RATE_MISSING = "layerRate must be specified in the request.";
  public static final String TS_NAME_MISSING = "The Telecom Slave name must be specified in the request.";
  public static final String TS_EXISTS = "The specified Telecom Slave already exists.";
  public static final String TS_NOT_FOUND = "The specified Telecom Slave was not found.";
  public static final String SOOC_NAME_MISSING = "The SOOC name must be specified in the request.";
  public static final String SOOC_EXISTS = "The specified SOOC already exists.";
  public static final String SOOC_NOT_FOUND = "The specified SOOC was not found.";
  public final static String SLAVE_IP_ILLEGAL_VALUE = "SlaveIPAddress contains empty or illegal value";
  public final static String SLAVE_SUBNET_ILLEGAL_VALUE = "SlaveSubnetMask contains empty or illegal value";
  public final static String MASTER_CLOCK_IP_ILLEGAL_VALUE = "MasterClockIPAddress contains empty or illegal value";
  public static final String OCSP_EXISTS = "The specified OCS Port already exists.";
  public static final String OCSP_NAME_MISSING = "The OCS Port name must be specified in the request.";
  public static final String OCSP_NOT_FOUND = "The specified OCS Port was not found.";

  public static final String PROBE_NAME_MISSING = "The Probe Name must be specified in the request.";
  public static final String PROBE_NAME_INVALID = "The specified Probe Name is invalid.";
  public static final String PROBE_EXISTS = "Probe with given ProbeName already exists.";
  public static final String PROBE_NOT_FOUND = "Probe with given ProbeName not found.";
  public static final String SRC_MEP_NOT_FOUND = "The specified ProbeSrcMepID on specified SourcePort was not found.";
  public static final String SRC_PORT_NOT_FOUND = "The specified SourcePort was not found.";
  public static final String SRC_PORT_NOT_SUPPORTED = "The specified SourcePort is not supported.";
  public static final String AMP_NAME_MISSING = "AMP name is missing";
  public static final String MT_NAME_MISSING = "ManagementTunnel name is missing";
  public static final String AMP_NOT_FOUND = "The specified AMP was not found";
  public static final String MT_NOT_FOUND = "The specified ManagementTunnel was not found";
  public static final String AMP_EXISTS = "The specified AMP already exists.";
  public static final String LINK_CREATION_FAILED = "Link creation between NEs failed";
  public static final String REMOTE_TYPE_NOT_FOUND = "Please specify RemoteType in the request";
  public static final String REMOTE_TYPE_NOT_VALID = "The specified RemoteType is invalid";
  public static final String PM_TEMPLATE_NOT_FOUND = "The specified PMTemplate was not found";
  public static final String ST_NAME_MISSING = "The Static Route name must be specified in the request.";
  public static final String ST_SUBNET_MASK_INVALID = "The specified StaticRouteSubnetMask is improper. Please use ***************";
  public static final java.lang.String AMP_INVALID = "The specified AMP index is invalid";
  public static final java.lang.String ST_EXISTS = "The specified Static Route already exists";
  public static final String ST_NOT_FOUND = "The specified Static Route was not found";
  public static final String SNC_NOT_FOUND = "The specified Subnetwork Connection was not found";
  public static final String ST_CREATION_FAILURE = "Could not create Static Route with given destination IP address as it collides with other IP address in your network.";
  public static final String LINK_NAME_CANNOT_BE_EMPTY = "LinkName cannot be empty";
  public static final String LINK_NAME_NOT_UNIQUE = "Link with specified LinkName already exists. LinkName must be unique.";
  public static final String LINK_ALREADY_EXISTS = "The specified PTP is already assigned to another Link.";

  public static final String NET_PORTS_NOT_SUPPORTED = "Network Ports are not supported for this operation.";

  public static String getInvalidOREmptyFieldValueErrorMessage(String fieldName){
    return fieldName+INVALID_EMPTY_VALUE;
  }

  public static String getInvalidFieldValueErrorMessage(String fieldName,String condition){
    return fieldName+INVALID_VALUE+" "+condition;
  }

  public static String getInvalidValueCombinationErrorMessage(String... fieldNames){
    String fields=".";
    for (String fieldName : fieldNames) {
      fields=", "+fieldName+fields;
    }
    return INVALID_VALUE_COMBINATION+fields;
  }

  public static String getInvalidValueCombinationErrorMessageWithExtraComment(String comment, String... fieldNames){
    String fields="";
    for (String fieldName : fieldNames) {
      fields=", "+fieldName+fields;
    }
    return INVALID_VALUE_COMBINATION+fields+" "+comment+".";
  }

  public static String getMessageCannotBeModifiedExistingFlow(String param) {
    return param + CANNOT_MODIFY_EXISTING_FLOW;
  }

  public static String getMessageMandatory(String param) {
    return param + MANDATORY;
  }

  public static String getMessageIllegal(String param) {
    return param + CONTAINS_ILLEGAL_VALUE;
  }

  public static String getMessageMandatoryNewFlow(String param) {
    return param + MANDATORY_FOR_NEW_FLOW;
  }

}