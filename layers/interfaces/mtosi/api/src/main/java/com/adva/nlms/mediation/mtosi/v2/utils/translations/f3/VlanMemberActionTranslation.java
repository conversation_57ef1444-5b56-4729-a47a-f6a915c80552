/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v2.utils.translations.f3;

public enum VlanMemberActionTranslation implements TranslatableEnum {
  NO_ACTION                 (1, "NoAction"),
  ADD_VLAN                  (2, "AddVlan"),
  REMOVE_VLAN               (3, "RemoveVlan"),
  NOT_APPLICABLE            (0, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private VlanMemberActionTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  public static VlanMemberActionTranslation getByMtosiString(String mtosiString){
    for(VlanMemberActionTranslation action : values()){
      if(action.getMtosiString().equals(mtosiString))
        return action;
    }
    return VlanMemberActionTranslation.NOT_APPLICABLE;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}