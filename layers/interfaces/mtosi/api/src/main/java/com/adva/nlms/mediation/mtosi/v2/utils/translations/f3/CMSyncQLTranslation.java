/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v2.utils.translations.f3;

public enum CMSyncQLTranslation implements TranslatableEnum {
  NOT_APPLICABLE  (0, "NotAvailable"),
  QL_DNU    (1, "QL-DNU"),
  QL_EEC1   (2, "QL-EEC1"),
  QL_EEC2   (3, "QL-EEC2"),
  QL_INV    (4, "QL-INV"),
  QL_INV0   (5, "QL-INV0"),
  QL_INV1   (6, "QL-INV1"),
  QL_INV2   (7, "QL-INV2"),
  QL_INV3   (8, "QL-INV3"),
  QL_INV5   (9, "QL-INV5"),
  QL_INV7   (10, "QL-INV7"),
  QL_INV8   (11, "QL-INV8"),
  QL_INV9   (12, "QL-INV9"),
  QL_INV10  (13, "QL-INV10"),
  QL_INV11  (14, "QL-INV11"),
  QL_INV12  (15, "QL-INV12"),
  QL_NONE   (16, "QL-NONE"),
  QL_NSUPP  (17, "QL-NSUPP"),
  QL_PRC    (18, "QL-PRC"),
  QL_PROV   (19, "QL-PROV"),
  QL_PRS    (20, "QL-PRS"),
  QL_SMC    (21, "QL-SMC"),
  QL_SSU_A  (22, "QL-SSU-A"),
  QL_SSU_B  (23, "QL-SSU-B"),
  QL_ST2    (24, "QL-ST2"),
  QL_ST3E   (25, "QL-ST3E"),
  QL_STU    (26, "QL-STU"),
  QL_TNC    (27, "QL-TNC"),
  QL_UNC    (28, "QL-UNC"),
  QL_FAILED (29, "QL-FAILED"),
  QL_INV6   (30, "QL-INV6"),
  QL_INV13  (31, "QL-INV13"),
  QL_INV14  (32, "QL-INV14"),
  QL_DUS    (33, "QL-DUS"),
  QL_NA     (34, "QL-NA"),
  QL_PRTC   (35, "QL-PRTC"),
  QL_EPRTC  (36, "QL-EPRTC"),
  QL_EPRC   (37, "QL-EPRC"),
  QL_EEEC   (38, "QL-EEEC"),
  QL_ST4    (39, "QL-ST4");

  private final int    mibValue;
  private final String mtosiString;

  private CMSyncQLTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}
