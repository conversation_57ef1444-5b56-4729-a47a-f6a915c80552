/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: mm<PERSON><PERSON><PERSON><PERSON>
 */
package com.adva.nlms.mediation.mtosi.v2.utils.translations.f3;

public enum CMBmcaDecisionCode implements TranslatableEnum {
  M1(1,"M1"),
  M2(2,"M2"),
  M3(3,"M3"),
  S1(4,"S1"),
  P1(5,"P1"),
  P2(6,"P2"),
  NOT_APPLICABLE(7,"N/A");

  CMBmcaDecisionCode(int mibValue, String mtosiString) {
    this.mibValue = mibValue;
    this.mtosiString = mtosiString;
  }

  private final int mibValue;
  private final String mtosiString;

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}
