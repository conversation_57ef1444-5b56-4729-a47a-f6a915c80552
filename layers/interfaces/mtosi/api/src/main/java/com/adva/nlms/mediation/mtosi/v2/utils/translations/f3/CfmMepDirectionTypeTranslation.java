/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v2.utils.translations.f3;

/**
 * Created by IntelliJ IDEA. User: mariuszg Date: 2007-06-25 Time: 10:13:25 To change this template use File | Settings
 * | File Templates.
 */
public enum CfmMepDirectionTypeTranslation
{
  DOWN           (1, "Down"),
  UP             (2, "Up"),
  NOT_APPLICABLE (3, "n/a");

  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;
  //------------------------------------------------------------------------------------------------------------------

  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private CfmMepDirectionTypeTranslation (final int mibValue, final String mtosiString)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }

  public static int getMIBValue(final String mtosiString) {
    int cfmTypeTranslation = 3;
    
    for (CfmMepDirectionTypeTranslation tmpCfmTypeTranslation : values())
    {
      if (mtosiString.equals(tmpCfmTypeTranslation.getMtosiString()))
      {
        cfmTypeTranslation = tmpCfmTypeTranslation.getMIBValue();
        break;
      }
    }
    
    return cfmTypeTranslation;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int mibValue)
  {
    CfmMepDirectionTypeTranslation cfmTypeTranslation = NOT_APPLICABLE;  // the return value

    for (CfmMepDirectionTypeTranslation tmpCfmTypeTranslation : values())
    {
      if (mibValue == tmpCfmTypeTranslation.getMIBValue())
      {
        cfmTypeTranslation = tmpCfmTypeTranslation;
        break;
      }
    }
    return cfmTypeTranslation.getMtosiString();
  }

}
