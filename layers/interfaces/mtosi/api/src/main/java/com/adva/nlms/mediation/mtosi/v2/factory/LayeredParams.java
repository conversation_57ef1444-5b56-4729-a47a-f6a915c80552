/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v2.factory;

/**
 * Class for all layered parameters found in some responses.
 */
public class LayeredParams {

  public final static String PROP_ADVA_ETHERNET_EFMOAM = "PROP_ADVA_Ethernet_EFMOAM";
  public final static String LR_PHYSICAL_OPTICAL = "LR_PHYSICAL_OPTICAL";
  public final static String LR_ETHERNET = "LR_Ethernet";
  public final static String LR_lAG = "LR_LAG";
  public final static String LR_lAG_FRAGMENT = "LR_LAG_Fragment";
  public final static String PROP_ADVA_PHYSICAL_ELECTRICAL="PROP_ADVA_PHYSICAL_ELECTRICAL";
  public final static String PROP_ADVA_PHYSICAL_OPTICAL = "PROP_ADVA_PHYSICAL_OPTICAL";
  public final static String PROP_ADVA_SYNC_REFERENCE = "PROP_ADVA_Sync_Reference";
  public final static String PROP_ADVA_DSR_FAST_ETHERNET="PROP_ADVA_DSR_Fast_Ethernet";
  public final static String LR_DSR_FAST_ETHERNET="LR_DSR_Fast_Ethernet";
  public final static String LR_DSR_GIGABIT_ETHERNET="LR_DSR_Gigabit_Ethernet";
  public final static String PROP_ADVA_SFP = "PROP_ADVA_SFP";
  public final static String LR_DSR_10Gigabit_Ethernet_LAN = "LR_DSR_10Gigabit_Ethernet_LAN";
  public final static String LR_DSR_100Gigabit_Ethernet = "LR_DSR_100Gigabit_Ethernet";
  public final static String LR_FC_1000_10520M = "LR_FC_1000_10520M";
  public final static String LR_FC_800_8500M = "LR_FC_800_8500M";
  public static final String LR_FC_1600_17000M = "LR_FC_1600_17000M";
  public final static String PROP_ADVA_IB_5000M  = "PROP_ADVA_IB_5000M";
  public final static String PROP_ADVA_IB_10000M  = "PROP_ADVA_IB_10000M";
  public final static String LR_FC_400_4250M = "LR_FC_400_4250M";
  public final static String LR_FC_100_1063M = "LR_FC_100_1063M";
  public final static String LR_FC_200_2126M = "LR_FC_200_2126M";
  public final static String LR_DSR_OC3_STM1 = "LR_DSR_OC3_STM1";
  public final static String LR_DSR_OC12_STM4 = "LR_DSR_OC12_STM4";
  public final static String LR_DSR_OC192_and_STM64 = "LR_DSR_OC192_and_STM64";
  public final static String LLR_DSR_OC192_STS192 = "LR_DSR_OC192_STS192";
  public final static String LR_DSR_OC48_and_STM16 = "LR_DSR_OC48_and_STM16";
  public final static String LR_OPTICAL_TRANSMISSION_SECTION="LR_Optical_Transmission_Section";
  public final static String LR_OPTICAL_MULTIPLEX_SECTION="LR_Optical_Multiplex_Section";
  public final static String LR_OPTICAL_CHANNEL="LR_Optical_Channel";
  public final static String LR_OPTICAL_SECTION="LR_OPTICAL_SECTION";
  public final static String LR_OCH_Transport_Unit_1="LR_OCH_Transport_Unit_1";
  public final static String LR_OCH_Transport_Unit_2="LR_OCH_Transport_Unit_2";
  public final static String LR_OCH_Transport_Unit_3="LR_OCH_Transport_Unit_3";
  public final static String LR_OCH_Transport_Unit_4="LR_OCH_Transport_Unit_4";
  public final static String LR_OCH_Transport_Unit_2e="LR_OCH_Transport_Unit_2e";
  public final static String LR_OCH_Transport_Unit_3e1="LR_OCH_Transport_Unit_3e1";
  public final static String LR_OCH_Transport_Unit_3e2="LR_OCH_Transport_Unit_3e2";
  public final static String LR_DSR_OTU1 = "LR_DSR_OTU1";
  public final static String LR_DSR_OTU2 = "LR_DSR_OTU2";
  public final static String LR_DSR_OTU3 = "LR_DSR_OTU3";
  public final static String LR_DSR_OTU4 = "LR_DSR_OTU4";
  public final static String LR_DSR_OTU2E = "LR_DSR_OTU2e";
  public final static String LR_DSR_OTU3E1 = "LR_DSR_OTU3e1";
  public final static String LR_DSR_OTU3E2 = "LR_DSR_OTU3e2";
  public final static String LR_OCH_Data_Unit_2e="LR_OCH_Data_Unit_2e";
  public final static String LR_OCH_Data_Unit_2="LR_OCH_Data_Unit_2";
  public final static String LR_OCH_Data_Unit_3e2="LR_OCH_Data_Unit_3e2";
  public final static String LR_OCH_Data_Unit_0="LR_OCH_Data_Unit_0";
  public final static String LR_OCH_Data_Unit="LR_OCH_Data_Unit";
  public final static String LR_OCH_Data_Unit_1="LR_OCH_Data_Unit_1";
  public final static String LR_OCH_Data_Unit_1e="PROP_ADVA_OCH_Data_Unit_1e";
  public final static String LR_OCH_Data_Unit_3e1="LR_OCH_Data_Unit_3e1";
  public final static String LR_OCH_Data_Unit_4="LR_OCH_Data_Unit_4";
  public final static String LR_OCH_Data_Unit_Flexible="LR_OCH_Data_Unit_Flexible";
  public final static String LR_OCH_Data_Unit_3="LR_OCH_Data_Unit_3";
  public final static String LR_DIGITAL_SIGNAL_RATE="LR_DIGITAL_SIGNAL_RATE";
  public final static String LR_PHYSICAL_ELECTRICAL="LR_Physical_Electrical";
  public final static String PROP_ADVA_SyncEthernet = "PROP_ADVA_SyncEthernet";
  public final static String PROP_ADVA_BITS = "PROP_ADVA_BITS";
  public final static String PROP_ADVA_OPTICAL_CHANNEL="PROP_ADVA_Optical_Channel";
  public final static String PROP_ADVA = "PROP_ADVA";
  public final static String PROP_ADVA_DCN = "PROP_ADVA_DCN";
  public final static String PROP_ADVA_LAG = "PROP_ADVA_LAG";
  public final static String PROP_ADVA_LAG_PORT = PROP_ADVA_LAG+"_PORT";
  public final static String PROP_ADVA_SNMP = PROP_ADVA+"_SNMP";
  public final static String PROP_ADVA_SNMP_V1V2C = PROP_ADVA+"_SNMPv1v2c";
  public final static String PROP_ADVA_ETHERNET = PROP_ADVA+"_Ethernet";
  public final static String PROP_ADVA_ETHERNET_POLICER = PROP_ADVA+"_Ethernet_Policer";
  public final static String PROP_ADVA_ETHERNET_SHAPER = PROP_ADVA+"_Ethernet_Shaper";
  public final static String PROP_ADVA_EFMOAM = PROP_ADVA+"_Ethernet_EFMOAM";
  public final static String PROP_ADVA_TOPOLOGY = PROP_ADVA+"_Topology";
  public final static String PROP_ADVA_EQUIPMENT = PROP_ADVA+"_Equipment";
  public final static String PROP_ADVA_REMOTECPE = PROP_ADVA+"_RemoteCPE";
  public final static String PROP_ADVA_PROTECTION_PLUS = PROP_ADVA+"_ProtectionFSP1501Plus1";
  public final static String PROP_ADVA_OPTICAL_MULTIPLEX_SECTION="PROP_ADVA_Optical_Multiplex_Section";
  public final static String PROP_ADVA_TIMING_DOMAIN = "PROP_ADVA_TIMING_DOMAIN";
  public final static String PROP_ADVA_CFMCCM    = "PROP_ADVA_CFMCCM";
  public final static String PROP_ADVA_1588   = "PROP_ADVA_1588";
  public final static String LOCATION   = "Location";
  public final static String LR_OCH_TRANSPORT_UNIT_PREFIX  = "LR_OCH_Transport_Unit_";
  public final static String LR_OCH_DATA_UNIT_PREFIX  = "LR_OCH_Data_Unit_";
  public final static String LR_DSR_OTU_PREFIX  = "LR_DSR_OTU";
  public final static String PROP_ADVA_AMP = "PROP_ADVA_AMP";
  public final static String PROP_ADVA_MANAGEMENT_TUNNEL = "PROP_ADVA_Tunnel";
  public final static String PROP_ADVA_ST = "PROP_ADVA_StaticRoute";
  public final static String PROP_ADVA_ESA_PROBE  = "PROP_ADVA_ESAProbe";
  public final static String PROP_ADVA_ESA_SCHEDULE  = "PROP_ADVA_ESASchedule";
  public final static String LR_OCH_DATA_UNIT_K  = "LR_OCH_Data_Unit_k";

  public static class PropAdvaEquipment{
    public final static String ADMIN_STATE = "AdminState";
    public final static String ADMIN_STATE_FULL = "AdministrationControl";
    public final static String SECONDARY_STATE = "SecondaryState";
    public final static String HARDWARE_VERSION = "HardwareVersion";
    public final static String PSU_OUTPUT_POWER ="PSUOutputPower";
    public final static String TRANSMISSION_MODE="TransmissionMode";
    public final static String OPERATIONAL_STATE="OperationalState";
    public final static String REACH="Reach";
    public final static String DEPLOYMENT_SCENARIO="DeploymentScenario";
    public final static String DISPERSION_COMPENSATION="DispersionCompensation";
    public final static String DC_FIBER_TYPE="DCFiberType";
    public final static String EDFA_GAIN_RANGE="EDFAGainRange";
    public final static String OMBand="OMBand";
    public final static String PROVISIONED_CAPABILITY_LEVEL="ProvisionedCapabilityLevel";
    public final static String ROADM_NUMBER="ROADMNumber";
    public final static String LAN_AID="Lan-Aid";
    public final static String CHANNEL="Channel";
    public final static String OUTPUT_POWER_RATING="OutputPowerRating";
    public final static String OUTPUT_CURRENT="OutputCurrent";
    public final static String TEMPERATURE="Temperature";
    public final static String OUTPUT_VOLTAGE="OutputVoltage";
    public final static String CHANNEL_GROUP="ChannelGroup";
    public final static String CHANNEL_SPACE="ChannelSpace";
    public final static String SUPPLY_TYPE="SupplyType";
    public final static String THIRD_PARTY_PLUGS_USAGE="ThirdPartyPlugsUsage";
    public final static String RATE="Rate";
    public final static String LANEGROUP="LaneGroup";
  }

  /**
   * Parameters of ETHERNET layer.
   */
  public static class LrEthernet {
    public final static String SERVICE_MUXING_INDICATOR_PARAM = "ServiceMuxingIndicator";
    public final static String ALL_IN_ONE_INDICATOR_PARAM = "AllToOneIndicator";
    public final static String PORT_ACCEPTABLE_FRAME_TYPES_PARAM = "PortAcceptableFrameTypes";
    public final static String CONNECTIONLESS_PORT_PARAM = "ConnectionlessPort";
    public final static String INTERFACE_TYPE_PARAM = "InterfaceType";
    public final static String PORT_TP_ROLE_STATE_PARAM = "PortTPRoleState";
    public final static String NUMBER_OF_TRAFFIC_CLASSES_PARAM = "NumberOfTrafficClasses";
    public final static String PHYS_ADDRESS_PARAM = "PhysAddress";
    public final static String MAX_NUM_FDFRS_PARAM = "MaxNumFDFrs";
    public final static String NUM_CONFIGURED_FDFRS_PARAM = "NumConfiguredFDFrs";
  }

  /**
   * Parameters of Prop Adva Ethernet EFMOAM layer.
   */
  public static class LrPropAdvaEthernetEFMOAM {

    public final static String EFM_OAM_ID_PARAM = "EfmOamId";
    public final static String EFM_OAM_ENABLED = "EfmOamEnabled";
    public final static String EFM_OAM_ADMINISTRATION_CONTROL_PARAM = "EfmOamAdministrationControl";
    public final static String EFM_OAM_DISCOVERY_STATE_PARAM = "EfmOamDiscoveryState";
    public final static String EFM_OAM_LOCAL_VAR_RTRVS_ENABLED_PARAM = "EfmOamLocalVarRtrvsEnabled";
    public final static String EFM_OAM_LOCAL_LINK_EVENTS_ENABLED_PARAM = "EfmOamLocalLinkEventsEnabled";
    public final static String EFM_OAM_LOCAL_OAM_LOOPBACKS_SUPPORTED_PARAM = "EfmOamLocalOamLoopbacksSupported";
    public final static String EFM_OAM_LOCAL_UNIDIR_SUPPORT_ENABLED_PARAM = "EfmOamLocalUnidirSupportEnabled";
    public final static String EFM_OAM_LOCAL_MAX_PDU_SIZE_PARAM = "EfmOamLocalMaxPDUSize";
    public final static String EFM_OAM_LOCAL_MODE_PARAM = "EfmOamLocalMode";
    public final static String EFM_OAM_REMOTE_PHYS_ADDRESS_PARAM = "EfmOamRemotePhysAddress";
    public final static String EFM_OAM_REMOTE_VAR_RTRVS_ENABLED_PARAM = "EfmOamRemoteVarRtrvsEnabled";
    public final static String EFM_OAM_REMOTE_LINK_EVENTS_ENABLED_PARAM = "EfmOamRemoteLinkEventsEnabled";
    public final static String EFM_OAM_REMOTE_OAM_LOOPBACKS_SUPPORTED_PARAM = "EfmOamRemoteOamLoopbacksSupported";
    public final static String EFM_OAM_REMOTE_UNIDIR_SUPPORT_ENABLED_PARAM = "EfmOamRemoteUnidirSupportEnabled";
    public final static String EFM_OAM_REMOTE_MAX_PDU_SIZE_PARAM = "EfmOamRemoteMaxPDUSize";
    public final static String EFM_OAM_REMOTE_MODE_PARAM = "EfmOamRemoteMode";
  }

  public static class PropAdvaTimingDomain {
    public static final String ALIAS                  = "Alias";
    public static final String ALLOCATED_NUMBER       = "AllocatedNumber";
    public static final String ALLOCATED_MAXIMUM      = "AllocationMaximum";
    public static final String ADMINISTRATION_CONTROL = "AdministrationControl";
    public static final String SERVICE_STATE          = "ServiceState";
    public static final String SECONDARY_STATE        = "SecondaryState";
    public static final String NETWORK_CLOCK_TYPE     = "NetworkClockType";
    public static final String SYNC_DOMAIN_TYPE       = "SyncDomainType";
    public static final String SELECTED_REFERENCE     = "SelectedReference";
    public static final String SYNC_CLOCK_MODE        = "SyncClockMode";
    public static final String SSM_QUALITY_LEVEL      = "SSMQualityLevel";
    public static final String SYNC_SELECTION_MODE    = "SyncSelectionMode";
    public static final String SYNC_WTR_TIME          = "SyncWTRTime";
  }

  public static class LrLagFragment{
    public final static String ADMINISTRATION_CONTROL_PARAM       = "AdministrationControl";
    public final static String SERVICE_STATE_PARAM                = "ServiceState";
    public final static String SECONDARY_STATE_PARAM              = "SecondaryState";
    public final static String FRAGMENT_SERVER_LAYER              = "FragmentServerLayer";
    public final static String CONNECTIONLESS_PORT                = "ConnectionlessPort";
    public final static String INTERFACE_TYPE                     = "InterfaceType";
    public final static String PORT_TP_ROLE_STATE                 = "PortTPRoleState";
    public final static String NUM_CONFIGURED_FDFRS               = "NumConfiguredFDFrs";
    public final static String MAX_NUM_FDFRS_PARAM                = "MaxNumFDFrs";
    public final static String LAG_MEMBER                         = "LagMember";
    public final static String ALLOCATION_MAXIMUM                 = "AllocationMaximum";
    public final static String ALIAS                              = "Alias";
    public final static String PROTOCOLS                          = "Protocols";
    public final static String LACP_CONTROL                       = "LacpControl";
    public final static String MODE                               = "Mode";
    public final static String CCM_DEFECTS_DETECTION_ENABLED      = "CcmDefectsDetectionEnabled";
    public final static String COLLECTOR_MAX_DELAY                = "CollectorMaxDelay";
    public final static String IGNORE_PARTNER_COLLECTOR_MAX_DELAY = "IgnorePartnerCollectorMaxDelay";
    public final static String ACTOR_SYSTEM_PRIORITY              = "ActorSystemPriority";
    public final static String ACTOR_SYSTEM_ID                    = "ActorSystemID";
    public final static String ACTOR_ADMIN_KEY                    = "ActorAdminKey";
    public final static String ACTOR_OPER_KEY                     = "ActorOperKey";
    public final static String PARTNER_SYSTEM_PRIORITY            = "PartnerSystemPriority";
    public final static String PARTNER_SYSTEM_ID                  = "PartnerSystemID";
    public final static String PARTNER_OPER_KEY                   = "PartnerOperKey";

    public final static String TRAFFIC_PORT_MODE_PARAM            = "TrafficModelMode";
    public final static String PORT_MODE_PARAM                    = "PortMode";
    public final static String MAXIMUM_FRAME_SIZE                 = "MaximumFrameSize";
    public final static String TAGGED_FRAMES_ENABLED              = "TaggedFramesEnabled";
    public final static String UNTAGGED_FRAMES_ENABLED            = "UntaggedFramesEnabled";
    public final static String LINK_LOSS_FWD_ENABLED              = "LinkLossFwdEnabled";
    public final static String LINK_LOSS_FWD_DELAY                = "LinkLossFwdDelay";
    public final static String LINK_LOSS_FWD_TX_ACTION_TYPE       = "LinkLossFwdTxActionType";
    public final static String LOCAL_LINK_ID                      = "LocalLinkId";
    public final static String REMOTE_LINK_IDS                    = "RemoteLinkIds";
    public final static String LINK_LOSS_FWD_TRIGGER_TYPES        = "LinkLossFwdTriggerTypes";
    public final static String LINK_LOSS_FWD_ACTIVE               = "LinkLossFwdActive";
    public final static String LINK_LOSS_FWD_PARTNER_ENABLED      = "LinkLossFwdPartnerEnabled";
    public static final String N2A_VLAN_TRUNKING_ENABLED          = "N2AVlanTrunkingEnabled";
    public static final String A2N_PUSH_PORT_VID_ENABLED          = "A2NPushPortVIDEnabled";
    public static final String N2A_POP_PORT_VID_ENABLED           = "N2APopPortVIDEnabled";
    public final static String LOOPBACK_STATUS_TYPE               = "LoopbackStatusType";
    public final static String LOOPBACK_STATUS_TIME               = "LoopbackStatusTime";
    public final static String LOOPBACK_STATUS_SWAP_SADA          = "LoopbackStatusSwapSADA";
    public final static String LOOPBACK_STATUS_OUTER_VLAN_LIST    = "LoopbackStatusOuterVLANList";
    public final static String LOOPBACK_STATUS_INNER_VLAN_LIST    = "LoopbackStatusInnerVLANList";
  }

  public static class LrLagPortFragment{
    public final static String ACTION_MODE                        = "ActionMode";
    public final static String LAG_MEMBER                         = "LagMember";
    public final static String MEMBER_STATE                       = "MemberState";
    public final static String AGGREGATE_OR_INDIVIDUAL            = "AggregateOrIndividual";
    public final static String LACP_FORCE_OUT_OF_SYNC             = "LacpForceOutOfSync";
    public final static String ACTOR_PORT_PRIORITY                = "ActorPortPriority";
    public final static String ACTOR_SYSTEM_PRIORITY              = "ActorSystemPriority";
    public final static String ACTOR_SYSTEM_ID                    = "ActorSystemID";
    public final static String ACTOR_ADMIN_KEY                    = "ActorAdminKey";
    public final static String ACTOR_PORT                         = "ActorPort";
    public final static String ACTOR_ADMIN_ACTIVITY               = "ActorAdminActivity";
    public final static String ACTOR_ADMIN_TIMEOUT                = "ActorAdminTimeout";
    public final static String ACTOR_ADMIN_AGGREGATION            = "ActorAdminAggregation";
    public final static String ACTOR_ADMIN_SYNCHRONIZATION        = "ActorAdminSynchronization";
    public final static String ACTOR_ADMIN_COLLECTING             = "ActorAdminCollecting";
    public final static String ACTOR_ADMIN_DISTRIBUTING           = "ActorAdminDistributing";
    public final static String ACTOR_ADMIN_DEFAULTED              = "ActorAdminDefaulted";
    public final static String ACTOR_ADMIN_EXPIRED                = "ActorAdminExpired";
    public final static String ACTOR_OPER_KEY                     = "ActorOperKey";
    public final static String ACTOR_OPER_ACTIVITY                = "ActorOperActivity";
    public final static String ACTOR_OPER_TIMEOUT                 = "ActorOperTimeout";
    public final static String ACTOR_OPER_AGGREGATION             = "ActorOperAggregation";
    public final static String ACTOR_OPER_SYNCHRONIZATION         = "ActorOperSynchronization";
    public final static String ACTOR_OPER_COLLECTING              = "ActorOperCollecting";
    public final static String ACTOR_OPER_DISTRIBUTING            = "ActorOperDistributing";
    public final static String ACTOR_OPER_DEFAULTED               = "ActorOperDefaulted";
    public final static String ACTOR_OPER_EXPIRED                 = "ActorOperExpired";
  }

  public static class PropAdvaProtect{
    public final static String MEMBER_TYPE                  = "MemberType";
    public final static String MEMBER_STATE                 = "MemberState";
  }
    public static class LrPropAdvaEthernet{

        public static final String ALIAS                                 = "Alias";
        public final static String ASSIGNED_STATE_PARAM                  = "AssignedState";
        public final static String PORT_MODE_PARAM                       = "PortMode";
        public final static String MEDIA_TYPE_PARAM                      = "MediaType";
        public final static String ACCEPTABLE_FRAME_POLICY_PARAM         = "AcceptableFramePolicy";
        public final static String VLAN_TRUNKING_ENABLED_PARAM           = "VLANTrunkingEnabled";
        public final static String INGRESS_PUSH_PVID_ENABLED_PARAM       = "IngressPushPVIDEnabled";
        public final static String EGRESS_POP_PVID_ENABLED_PARAM         = "EgressPopPVIDEnabled";
        public final static String PORT_VLAN_ID_PARAM                    = "PortVLANId";
        public final static String PORT_PRIORITY_PARAM                   = "PortPriority";
        public final static String PRIORITY_VLAN_ID_PARAM                = "PriorityVLANId";
        public final static String PRIORITY_MAP_MODE_PARAM               = "PriorityMapMode";
        public final static String TAGGED_FRAMES_ENABLED_PARAM           = "TaggedFramesEnabled";
        public final static String UNTAGGED_FRAMES_ENABLED_PARAM         = "UntaggedFramesEnabled";
        public final static String DEFAULT_MEMBER_ENABLED_PARAM          = "DefaultMemberEnabled";
        public final static String VLAN_ETHER_TYPE_PARAM                 = "VLANEtherType";
        public final static String TRANSMIT_PAUSE_FRAMES_ENABLED_PARAM   = "TransmitPauseFramesEnabled";
        public final static String RECEIVE_PAUSE_FRAMES_ENABLED_PARAM    = "ReceivePauseFramesEnabled";
        public final static String PORT_SHAPING_ENABLED_PARAM            = "PortShapingEnabled";
        public final static String PORT_SHAPING_SPEED_PARAM              = "PortShapingSpeed";
        public final static String Q_IN_Q_ETHER_TYPE_PARAM               = "QinQEtherType";
        public final static String SVLAN_ETHER_TYPE_PARAM                = "SVLANEtherType";
        public final static String CVLAN_ETHER_TYPE_PARAM                = "CVLANEtherType";
        public final static String LOOPBACK_STATUS_TYPE_PARAM            = "LoopbackStatusType";
        public final static String LOOPBACK_STATUS_TIME_PARAM            = "LoopbackStatusTime";
        public final static String LOOPBACK_STATUS_SWAP_SADA_PARAM       = "LoopbackStatusSwapSADA";
        public final static String LOOPBACK_STATUS_OUTER_VLAN_LIST_PARAM = "LoopbackStatusOuterVLANList";
        public final static String LOOPBACK_STATUS_INNER_VLAN_LIST_PARAM = "LoopbackStatusInnerVLANList";
        public final static String LOOPBACK_TYPE_PARAM            		 = "LoopbackType";
        public final static String LOCAL_LOOPBACK_MODE            		 = "LocalLoopbackMode";
        public final static String LOOPBACK_TIME_PARAM            		 = "LoopbackTime";
        public final static String LOOPBACK_SWAP_SADA_PARAM       		 = "LoopbackStatusSwapSADA ";
        public final static String WAN_FAULT_PROPAGATION_PARAM           = "WANFaultPropagation";
        public final static String LAN_FAULT_PROPAGATION_PARAM           = "LANFaultPropagation";
        public final static String FAULT_PROPAGATION_DELAY_PARAM         = "FaultPropagationDelay";
        public final static String LOCAL_LINK_ID_PARAM                   = "LocalLinkId";
        public final static String REMOTE_LINK_IDS_PARAM                 = "RemoteLinkIds";
        public final static String LINK_LOSS_FORWARDING_PARAM            = "LinkLossForwarding";
        public final static String LINK_LOSS_FORWARDING_ACTIVE_PARAM     = "LinkLossForwardingActive";
        public final static String EFMOAM_ENABLED_PARAM                  = "EFMOAMEnabled";
        public final static String EFMOAM_ACTIVE_PARAM                   = "EFMOAMActive";
        public final static String EFMOAM_INFO_ENABLE_PARAM              = "EFMOAMInfoEnable";
        public final static String EFMOAM_LOCAL_MODE_PARAM              = "EfmOamLocalMode";
        public final static String SHAPING_ENABLED_PARAM                 = "ShapingEnabled";
        public final static String SHAPING_BANDWIDTH_PARAM               = "ShapingBandwidth";
        //New Attributes for the 825
        public final static String PORT_ACCESS_ENTITY_ROLE_PARAM         = "8021xPortAccessEntityRole";
        public final static String SUPP_USER_STAGE1_PARAM         		 = "8021xSuppUserStage1";
        public final static String SUPP_USER_STAGE2_PARAM         		 = "8021xSuppUserStage2";

        //New Attribute for Hatteras Bandwidth Testing
        public final static String LOOPBACK_PHYS_ADDRESS_PARAM			 = "LoopbackPhysAddress";

        public final static String LLDP_PORT_ADMIN_STATUS_NEAREST			   = "LLDPPortAdminStatusNearest";
        public final static String LLDP_PORT_ADMIN_STATUS_NON_TPMR			 = "LLDPPortAdminStatusNonTpmr";
        public final static String LLDP_PORT_ADMIN_STATUS_CUSTOMER			 = "LLDPPortAdminStatusCustomer";

        //flows
        public final static String FLOW_TYPE_PARAM                       = "FlowType";
        public final static String VLAN_MEMBERS_PARAM                    = "VLANMembers";
        public final static String MULTI_COS_PARAM                       = "MultiCOS";
        public final static String COS_PARAM                             = "COS";
        public final static String INGRESS_CIR_PARAM                     = "IngressCIR";
        public final static String INGRESS_EIR_PARAM                     = "IngressEIR";
        public final static String INGRESS_CBS_PARAM                     = "IngressCBS";
        public final static String INGRESS_EBS_PARAM                     = "IngressEBS";
        public final static String INGRESS_BUFFER_SIZE_PARAM             = "IngressBufferSize";
        public final static String EGRESS_BUFFER_SIZE_PARAM              = "EgressBufferSize";
        public final static String GRESS_CIR_PARAM                       = "CIR";
        public final static String GRESS_EIR_PARAM                       = "EIR";
        public final static String GRESS_CBS_PARAM                       = "CBS";
        public final static String GRESS_EBS_PARAM                       = "EBS";
        public final static String GRESS_BUFFER_SIZE_PARAM               = "BufferSize";
//        public final static String GRESS_BUFFER_SIZE_PARAM               = "gressBufferSize";
        public final static String CIRHI_PARAM                           = "CirHi";
        public final static String EIRHI_PARAM                           = "EirHi";
        public final static String POLICING_ALGORITHM_PARAM              = "PolicingAlgorithm";
        public final static String INGRESS_COUPLING_FLAG_PARAM           = "IngressCouplingFlag";
        public final static String COUPLING_FLAG_PARAM                   = "CouplingFlag";
        public final static String ASSOCIATED_SHAPER_PARAM               = "AssociatedShaper";
        public final static String INGRESS_COLOR_MODE_PARAM              = "IngressColorMode";
        public final static String COLOR_MODE_PARAM                      = "ColorMode";
        public final static String COLOR_MAKING_FLAG_PARAM               = "ColorMarkingFlag";
        public final static String EGRESS_RATE_LIMITING_ENABLED_PARAM    = "EgressRateLimitingEnabled";
        public final static String EGRESS_CIR_PARAM                      = "EgressCIR";
        public final static String EGRESS_EIR_PARAM                      = "EgressEIR";
        public final static String EGRESS_CBS_PARAM                      = "EgressCBS";
        public final static String EGRESS_EBS_PARAM                      = "EgressEBS";
        public final static String CTAG_CONTROL_PARAM                    = "CTagControl";
        public final static String CTAG_VLAN_ID_PARAM                    = "CTagVLANId";
        public final static String CTAG_VLAN_PRIORITY_PARAM              = "CTagVLANPriority";
        public final static String CTAG_MATCH_RX_PRIORITY_PARAM          = "CTagMatchRxPriority";
        public final static String STAG_CONTROL_PARAM                    = "STagControl";
        public final static String STAG_VLAN_ID_PARAM                    = "STagVLANId";
        public final static String STAG_VLAN_PRIORITY_PARAM              = "STagVLANPriority";
        public final static String STAG_MATCH_RX_PRIORITY_PARAM          = "STagMatchRxPriority";
        public final static String ASSOCIATED_ACL_PROFILE_PARAM          = "AssociatedAclProfile";
        public final static String ASSOCIATED_CPD_PROFILE_PARAM          = "AssociatedCpdProfile";
        public final static String INGRESS_MULTICOS_ENABLED              = "IngressMultiCOSEnabled";
        public final static String EGRESS_SHAPING_TYPE                   = "EgressShapingType";
        public final static String VLAN_MEMBER_ACTION                   = "VlanMemberAction";
        public final static String VLAN_MEMBER_ACTION_VLAN              = "VlanMemberActionVlan";
        public final static String MCAST_RATE_LIMIT_ENABLED             = "MulticastRateLimitEnabled";
        public final static String MCAST_RATE_LIMIT_SPEED               = "MulticastRateLimitSpeed";
        public final static String BCAST_RATE_LIMIT_ENABLED             = "BroadcastRateLimitEnabled";
        public final static String BCAST_RATE_LIMIT_SPEED               = "BroadcastRateLimitSpeed";
        public final static String CCAST_RATE_LIMIT_ENABLED             = "CombinedRateLimitEnabled";
        public final static String CCAST_RATE_LIMIT_SPEED               = "CombinedRateLimitSpeed";
        public final static String AUTO_CIR_PERCENTAGE                  = "AutoCIRPercentage";
        public final static String AUTO_BANDWIDTH_CONFIG_ENABLED        = "AutoBandwidthConfigEnabled";
        public final static String FRAME_FWD_ENABLED                    = "FrameFwdEnabled";
        //chassis
        public final static String ACCESS_TO_ACCESS_LINK_LOSS_FORWARDING_PARAM     = "AccessToAccessLinkLossForwarding";
        public final static String NETWORK_TO_ACCESS_LINK_LOSS_FORWARDING_PARAM    = "NetworkToAccessLinkLossForwarding";
        public final static String PAUSE_FRAMES_ENABLED_PARAM                      = "PauseFramesEnabled";
        public final static String MAC_ADDRESS_LEARNING_PARAM                      = "MACAddressLearning";
        public final static String BPDU_FORWARDING_FILTER_PARAM                    = "BPDUForwardingFilter";
        // cm
        public final static String ADMINISTRATION_CONTROL_PARAM           = "AdministrationControl";
        public final static String SERVICE_STATE_PARAM                    = "ServiceState";
        public final static String SECONDARY_STATE_PARAM                  = "SecondaryState";
        public final static String TRAFFIC_PORT_MODE_PARAM                = "TrafficModelMode";
        public final static String SYSTEM_POLICER_PROFILE_PARAM           = "SystemPolicerProfile";
        public final static String RED_ENABLED_PARAM                      = "REDEnabled";
        public final static String BUFFER_SIZE_PARAM                      = "BufferSize";
        public final static String SYSTEM_SHAPER_PROFILE_PARAM            = "SystemShaperProfile";
        public final static String POLICING_ENABLED_PARAM                 = "PolicingEnabled";
        public final static String JUMBO_FRAMES_ENABLED_PARAM             = "JumboFramesEnabled";
        public final static String LINK_LOSS_FWD_ENABLED_PARAM            = "LinkLossFwdEnabled";
        public final static String LINK_LOSS_FWD_SIGNAL_TYPE_PARAM        = "LinkLossFwdSignalType";
        public final static String LINK_LOSS_FWD_TRIGGER_TYPES_PARAM      = "LinkLossFwdTriggerTypes";
        public final static String LINK_LOSS_FWD_DELAY_PARAM              = "LinkLossFwdDelay";
        public final static String LINK_LOSS_FWD_ACTIVE_PARAM             = "LinkLossFwdActive";
        public final static String LINK_LOSS_FWD_PARTNER_ENABLED_PARAM    = "LinkLossFwdPartnerEnabled";
        public final static String TRAFFIC_MANAGEMENT_ENABLED_PARAM       = "TrafficManagementEnabled";
        public final static String SNMP_DYING_GASP_ENABLED_PARAM          = "SNMPDyingGaspEnabled";
        public final static String VOLTAGE                                = "Voltage";
        public final static String TEMPERATURE                            = "Temperature";
        public final static String RECEIVE_PAUSE_FRAMES_DISPOSITION_PARAM = "ReceivePauseFramesDisposition";
        public static final String ALARM_REPORT_CONTROL 				  = "AlarmReportControl";
        //New attrs for the GE20X && CM4.1
        public static final String LINK_LOSS_FWD_TX_ACTION_TYPE_PARAM 	  = "LinkLossFwdTxActionType";
        public static final String LINK_LOSS_FWD_RX_RLD_LINK_IDS_PARAM	  = "LinkLossFwdRxRLDLinkIds";
        public final static String MDI_MODE_PARAM 						  = "MdiMode";
        public final static String ACTUAL_MDI_MODE_PARAM 				  = "ActualMdiMode";
        // New Attrs for GE20X Flows
        public static final String TRAFFIC_MANAGEMENT_TYPE_PARAM		  = "TrafficManagementType";
        public static final String ES_FRAMES_LOSS_THRESHOLD_PARAM		  = "ESFramesLossThreshold";
        public static final String A2N_SHAPING_PARAM		              = "A2NShaping";
        public static final String N2A_SHAPING_PARAM		              = "N2AShaping";
        public static final String SES_FRAMES_LOSS_THRESHOLD_PARAM		  = "SESFramesLossThresholdRatio";
        public static final String FLOW_POLICING_ENABLED_PARAM		  	  = "FlowPolicingEnabled";
        // New Attrs for GX210 Ports
        public static final String PORT_XGE_PHY_TYPE = "XgePhyType";
        // New Attr for Master/Slave clock mode.
        public static final String AUTONEG_CLOCK_MODE = "AutoNegotiationClockMode";
        public static final String AUTO_DIAG_ENABLED = "AutoDiagnostic";
        public static final String N2A_VLAN_TRUNKING_ENABLED = "N2AVlanTrunkingEnabled";
        public static final String A2N_PUSH_PORT_VID_ENABLED = "A2NPushPortVIDEnabled";
        public static final String N2A_POP_PORT_VID_ENABLED = "N2APopPortVIDEnabled";
        public static final String POLICING_CONTROL = "PolicingControl";
    }



    public static class PropAdva {
    public final static String IP_ADDRESS = "IpAddress";
    public final static String PROXY_ARP = "proxyARPEnabled";
    public final static String PM_TEMPLATE = "pmTemplate";

  }

    public static class PropAdvaDcn {
        public static final String DHCP = "DHCPEnabled";
        public static final String DHCP_MASK = "DHCPMask";
        public static final String DHCP_IP_ADDRESS = "DHCPIpAddress";
        public static final String DEFAULT_GATEWAY = "DefaultGateway";
    }

    public static class PropAdvaTopology {
    public final static String SubnetPath = "SubnetPath";
  }

  public static class PropAdvaTechnology {
    public final static String SubnetPath = "SubnetPath";
  }

  public static class PropAdvaSnmp {
    public final static String USE_GLOBAL_SNMP_SETTINGS = "UseGlobalSNMPSettings";
    public final static String SNMP_PROTOCOL_VERSION = "SNMPProtocolVersion";
  }

  public static class PropAdvaSnmpV1V2c {
    public final static String READ_COMMUNITY = "ReadCommunity";
    public final static String WRITE_COMMUNITY = "WriteCommunity";
  }

  /**
   * Parameters of PHYSICAL_OPTICAL layers.
   */
  public static class LrOptical {
    public final static String THIS_LAYER_ACTIVE_PARAM = "ThisLayerActive";
  }

  public static class LrPropADVAOptical {



  }

  public static class LrPropAdvaDSRFastEthernet{

    public final static String AUTO_NEGOTIATION="AutoNegotiation";
    public final static String ADMINISTRATIVE_SPEED_RATE="AdministrativeSpeedRate";
    public final static String ACTUAL_SPEED_RATE="ActualSpeedRate";
    public final static String DUPLEX_MODE="DuplexMode";
    public final static String ACTUAL_DUPLEX_MODE="ActualDuplexMode";

  }


  public static class LrPropADVAOpticalChannel {
    public final static String CHANNEL="Channel";
    public final static String WAVE_LENGTH="WaveLength";
  }

  public static class PropADVAOpticalChannel {
    public final static String CHANNEL_NUMBER="ChannelNumber";
    public final static String WAVE_LENGTH="WaveLength";
  }

  public static class LrOpticalTransmissionSection {
    public final static String OCS_CENTRAL_FREQUENCY="OscCentralFrequency";
    public final static String OCS_FREQUENCY_SPACING="OscFrequencySpacing";
    public final static String OCS_FREQUENCY_SPREAD="OscFrequencySpread";
  }

  public static class LrOchTransportUnitK {
    public final static String TRAIL_TRACE_ACTUAL_TX ="TrailTraceActualTx";
    public final static String TRAIL_TRACE_ACTUAL_RX ="TrailTraceActualRx";
    public final static String TRAIL_TRACE_EXPECTED_RX ="TrailTraceExpectedRx";
    public final static String TRAIL_TRACE_MONITOR ="TrailTraceMonitor";
  }

  public static class LrDigitalRate {
    public final static String CLIENT_TYPE="ClientType";
    public final static String CLIENT_RATE="ClientRate";
  }

  public static class LrOchDataUnitK {
    //ODUK
    public final static String TRAIL_TRACE_ACTUAL_RX="TrailTraceActualRx";
    public final static String TRAIL_TRACE_ACTUAL_TX="TrailTraceActualTx";
    public final static String TRAIL_TRACE_EXPECTED_RX="TrailTraceExpectedRx";
    public final static String TRAIL_TRACE_MONITOR="TrailTraceMonitor";
    //TCM
    public final static String TCM_LEVEL="TCMLevel";
    public final static String TCM_SUPERVISION ="Supervision";
    public final static String TCM_TRAIL_TRACE_ACTUAL_RX="TrailTraceActualRx";
    public final static String TCM_TRAIL_TRACE_ACTUAL_TX="TrailTraceActualTx";
    public final static String TCM_TRAIL_TRACE_MONITOR_SAPI ="TrailTraceMonitorSAPI";
    public final static String TCM_TRAIL_TRACE_EXPECTED_SAPI_Rx ="TrailTraceExpectedSAPIRx";
    public final static String TCM_TRAIL_TRACE_MONITOR_DAPI="TrailTraceExpectedSAPIRx";
    public final static String TCM_TRAIL_TRACE_EXPECTED_DAPI_Rx="TrailTraceExpectedSAPIRx";

    public final static String RAYLOAD_TYPE="PayloadType";
    public final static String MSI="MSI";
    public final static String ALLOCATE_NUMBER="AllocatedNumber";
    public final static String ALLOCATED_SUPPORTING_CTPS="AllocatedSupportingCTPs";
    public final static String TX_TIME_SLOT="TX_TimeSlot";
    public final static String RX_TIME_SLOT="RX_TimeSlot";
    public final static String PM_TIM_DETECTION_MODE="PMTIMDetectionMode";


    public final static String TCM_LEVEL1_SUPERVISION ="TCMLevel1Supervision";
    public final static String TCM_LEVEL1_TRAIL_TRACE_ACTUAL_RX="TCMLevel1TrailTraceActualRx";
    public final static String TCM_LEVEL1_TRAIL_TRACE_ACTUAL_TX="TCMLevel1TrailTraceActualTx";
    public final static String TCM_LEVEL1_TRAIL_TRACE_MONITOR_SAPI ="TCMLevel1TrailTraceMonitorSAPI";
    public final static String TCM_LEVEL1_TRAIL_TRACE_EXPECTED_SAPI_Rx ="TCMLevel1TrailTraceExpectedSAPIRx";
    public final static String TCM_LEVEL1_TRAIL_TRACE_MONITOR_DAPI="TCMLevel1TrailTraceExpectedSAPIRx";
    public final static String TCM_LEVEL1_TRAIL_TRACE_EXPECTED_DAPI_Rx="TCMLevel1TrailTraceExpectedSAPIRx";

    public final static String TCM_LEVEL2_SUPERVISION ="TCMLevel2Supervision";
    public final static String TCM_LEVEL2_TRAIL_TRACE_ACTUAL_RX="TCMLevel2TrailTraceActualRx";
    public final static String TCM_LEVEL2_TRAIL_TRACE_ACTUAL_TX="TCMLevel2TrailTraceActualTx";
    public final static String TCM_LEVEL2_TRAIL_TRACE_MONITOR_SAPI ="TCMLevel2TrailTraceMonitorSAPI";
    public final static String TCM_LEVEL2_TRAIL_TRACE_EXPECTED_SAPI_Rx ="TCMLevel2TrailTraceExpectedSAPIRx";
    public final static String TCM_LEVEL2_TRAIL_TRACE_MONITOR_DAPI="TCMLevel2TrailTraceExpectedSAPIRx";
    public final static String TCM_LEVEL2_TRAIL_TRACE_EXPECTED_DAPI_Rx="TCMLevel2TrailTraceExpectedSAPIRx";

    public final static String TCM_LEVEL3_SUPERVISION ="TCMLevel3Supervision";
    public final static String TCM_LEVEL3_TRAIL_TRACE_ACTUAL_RX="TCMLevel3TrailTraceActualRx";
    public final static String TCM_LEVEL3_TRAIL_TRACE_ACTUAL_TX="TCMLevel3TrailTraceActualTx";
    public final static String TCM_LEVEL3_TRAIL_TRACE_MONITOR_SAPI ="TCMLevel3TrailTraceMonitorSAPI";
    public final static String TCM_LEVEL3_TRAIL_TRACE_EXPECTED_SAPI_Rx ="TCMLevel3TrailTraceExpectedSAPIRx";
    public final static String TCM_LEVEL3_TRAIL_TRACE_MONITOR_DAPI="TCMLevel3TrailTraceExpectedSAPIRx";
    public final static String TCM_LEVEL3_TRAIL_TRACE_EXPECTED_DAPI_Rx="TCMLevel3TrailTraceExpectedSAPIRx";

    public final static String TCM_LEVEL4_SUPERVISION ="TCMLevel4Supervision";
    public final static String TCM_LEVEL4_TRAIL_TRACE_ACTUAL_RX="TCMLevel4TrailTraceActualRx";
    public final static String TCM_LEVEL4_TRAIL_TRACE_ACTUAL_TX="TCMLevel4TrailTraceActualTx";
    public final static String TCM_LEVEL4_TRAIL_TRACE_MONITOR_SAPI ="TCMLevel4TrailTraceMonitorSAPI";
    public final static String TCM_LEVEL4_TRAIL_TRACE_EXPECTED_SAPI_Rx ="TCMLevel4TrailTraceExpectedSAPIRx";
    public final static String TCM_LEVEL4_TRAIL_TRACE_MONITOR_DAPI="TCMLevel4TrailTraceExpectedSAPIRx";
    public final static String TCM_LEVEL4_TRAIL_TRACE_EXPECTED_DAPI_Rx="TCMLevel4TrailTraceExpectedSAPIRx";

    public final static String TCM_LEVEL5_SUPERVISION ="TCMLevel5Supervision";
    public final static String TCM_LEVEL5_TRAIL_TRACE_ACTUAL_RX="TCMLevel5TrailTraceActualRx";
    public final static String TCM_LEVEL5_TRAIL_TRACE_ACTUAL_TX="TCMLevel5TrailTraceActualTx";
    public final static String TCM_LEVEL5_TRAIL_TRACE_MONITOR_SAPI ="TCMLevel5TrailTraceMonitorSAPI";
    public final static String TCM_LEVEL5_TRAIL_TRACE_EXPECTED_SAPI_Rx ="TCMLevel5TrailTraceExpectedSAPIRx";
    public final static String TCM_LEVEL5_TRAIL_TRACE_MONITOR_DAPI="TCMLevel5TrailTraceExpectedSAPIRx";
    public final static String TCM_LEVEL5_TRAIL_TRACE_EXPECTED_DAPI_Rx="TCMLevel5TrailTraceExpectedSAPIRx";

    public final static String TCM_LEVEL6_SUPERVISION ="TCMLevel6Supervision";
    public final static String TCM_LEVEL6_TRAIL_TRACE_ACTUAL_RX="TCMLevel6TrailTraceActualRx";
    public final static String TCM_LEVEL6_TRAIL_TRACE_ACTUAL_TX="TCMLevel6TrailTraceActualTx";
    public final static String TCM_LEVEL6_TRAIL_TRACE_MONITOR_SAPI ="TCMLevel6TrailTraceMonitorSAPI";
    public final static String TCM_LEVEL6_TRAIL_TRACE_EXPECTED_SAPI_Rx ="TCMLevel6TrailTraceExpectedSAPIRx";
    public final static String TCM_LEVEL6_TRAIL_TRACE_MONITOR_DAPI="TCMLevel6TrailTraceExpectedSAPIRx";
    public final static String TCM_LEVEL6_TRAIL_TRACE_EXPECTED_DAPI_Rx="TCMLevel6TrailTraceExpectedSAPIRx";
    //TODO:[PC]delete the below
//    public final static String TRAIL_TRACE_EXPECTED_DAPI_RX="TrailTraceExpectedDAPIRx";
//    public final static String TRAIL_TRACE_EXPECTED_SAPI_RX="TrailTraceExpectedSAPIRx";
//    public final static String TRAIL_TRACE_MONITOR_DAPI="TrailTraceMonitorDAPI";

//    public final static String PM_TRAIL_TRACE_ACTUAL_RX="PMTrailTraceActualRx";
//    public final static String PM_TRAIL_TRACE_ACTUAL_TX="PMTrailTraceActualTx";
//    public final static String PM_TRAIL_TRACE_EXPECTED_RX="PMTrailTraceExpectedRx";

  }

  public static class LrPropAdvaPhysicalElectrical{
    public final static String CONNECTOR="Connector";
    public final static String AdminState = "AdminState";
    public final static String OperationalState = "OperationalState";
    public final static String SecondaryState = "SecondaryState";
    public final static String FACILITY_TYPE="FacilityType";

  }
  public static class LrPropADVAPhysicalOptical{

    public final static String AdminState = "AdminState";
    public final static String OperationalState = "OperationalState";
    public final static String SecondaryState = "SecondaryState";
    public final static String CONNECTOR="Connector";
    public final static String FIBER_TYPE="FiberType";
    public final static String CONNECTIVITY_TYPE="ConnectivityType";
    public final static String ALS_MODE="ALSMode";
    public final static String DISPARITY_CORRECTION="DisparityCorrection";
    public final static String ERROR_FORWARDING_MODE="ErrorForwardingMode";
    public final static String LASER_OFF_DELAY="LaserOffDelay";
    public final static String FACILITY_TYPE="FacilityType";
    public final static String PROTECTION_ROLE="ProtectionRole";
    public final static String BEHAVE="Behaviour";
    public final static String REACH="Reach";
  }

  public static class LrPropAdva1588 {

    //COMMON
    public final static String ADMIN_STATE_PARAM = "AdministrationControl";
    public final static String SERVICE_STATE_PARAM = "ServiceState";
    public final static String OPER_STATE_PARAM = "OperationalState";
    public final static String SECONDARY_STATE_PARAM = "SecondaryState";

    //BC + MCI
    public final static String DOMAIN_NUMBER_PARAM = "DomainNumber";

    //BC
    public final static String CLOCK_IDENTITY_PARAM = "ClockIdentity";
    public final static String TIMING_SOURCE_PARAM = "TimingSource";
    public final static String CLOCK_CLASS_PARAM = "ClockClass";
    public final static String MEDIATION_CONTROL = "MediationControl";

    //MCI
    public final static String PORT_IDENTITY_PARAM= "PortIdentity";
    public final static String MASTER_CLOCK_TYPE_PARAM = "ClockType";
//    public final static String CLOCK_TYPE_PARAM = "ClockType";
    public final static String DELAY_MECHANISM_PARAM = "DelayMechanism";
    public final static String INTERFACE_NAME_PARAM = "IfName";
    public final static String IP_VERSION_PARAM = "IpVersion";
    public final static String IP_PROTOCOL_PARAM = "IpProtocol";
    public final static String IP_ADDRESS_PARAM = "MasterIpV4Address";
    public final static String SUBNET_MASK_PARAM = "MasterIpV4SubnetMask";
    public final static String PRIORITY_MODE_PARAM = "IpPriorityMapMode";
    public final static String PRIORITY_PARAM = "IpPriority";
    public final static String MAX_LEASE_DURATION_TYPE = "MaxLeaseDuration";
    public final static String MAX_SLAVE_SUPPORTED_TYPE = "MaxSlavesSupported";
    public final static String MAX_STATIC_SLAVES_TYPE = "MaxStaticSlavesSupported";
    public final static String MAX_SYNC_MSG_RATE_TYPE = "MaxSyncMsgRate";
    public final static String MAX_DELAY_RESP_MSG_RATE_TYPE = "MaxDelayRespMsgRate";
    public final static String MAX_ANNOUNCE_MSG_RATE_TYPE = "MaxAnnounceMsgRate";
    public final static String SERVICE_FLOW_TYPE = "ServiceFlow";
    public final static String CLOCK_CLASS_PROFILE = "ClockClassProfile";
    public final static String ANNOUNCE_EXT_TLV_ENABLED = "AnnounceExtTLVEnable";
    public final static String PTP_TRANSPORT = "PTPTransport";
    public final static String PTP_TRANSPORT_MODE = "PTPTransportMode";

    //MVP
    public final static String PTP_FLOW_POINT_TYPE = "PTPFlowPoint";

    //SRS
    public final static String UMN_CONTROL_TYPE = "UmnControl";
    public final static String SYNC_MSG_RATE_TYPE = "SyncMsgRate";
    public final static String DELAY_RESP_MSG_RATE_TYPE = "DelayRspMsgRate";
    public final static String ANNOUNCE_MSG_RATE_TYPE = "AnnounceMsgRate";

    public final static String NEG_SYNC_LEASE_DUR_TYPE = "NegSyncLeaseDur";
    public final static String NEG_DELAY_RSP_LEASE_TYPE = "NegDelayRspLeaseDur";
    public final static String NEG_ANNOUNCE_LEACE_DUR_TYPE = "NegAnnounceLeaseDur";
    public final static String SYNC_DUR_REM_TYPE = "SyncDurRemTime";
    public final static String DELAY_RSP_DUR_REM_TIME_TYPE = "DelayRspDurRemTime";
    public final static String ANNOUNCE_DUR_REM_TIME_TYPE = "AnnounceDurRemTime";

    public final static String IPv4_ADDRESS_TYPE = "IPV4Address";
    public final static String TIME_CREATED_TYPE = "TimeCreated";

    //OTHER
    public final static String MASTER_IP = "MasterIp";
    public final static String MASTER_SUBNET = "MasterSubnet";
    public final static String FLOW_POINT_TYPE = "FlowPointType";
    public final static String FLOW_ID = "Service";
    public final static String OUTER_VLAN_ETHER_TYPE = "OuterVlanEtherType";
    public final static String OUTER_VLAN_TAG = "OuterVlanMemberList";
    public final static String OUTER_UNTAGGED_ENABLED= "OuterUntaggedEnabled";
    public final static String INNER_1_VLAN_ETHER_TYPE = "Inner1VlanEtherType";
    public final static String INNER_1_VLAN_TAG = "Inner1VlanMemberList";
    public final static String INNER_1_UNTAGGED_ENABLED= "Inner1UntaggedEnabled";
    public final static String INNER_2_VLAN_ETHER_TYPE = "Inner2VlanEtherType";
    public final static String INNER_2_VLAN_TAG = "Inner2VlanMemberList";
    public final static String INNER_2_UNTAGGED_ENABLED= "Inner2UntaggedEnabled";
    public final static String SERVICE = "Service";
    public final static String CLOCK = "Clock";
    public final static String COS = "COS";
    public final static String CIR = "CIR";
    public final static String EIR = "EIR";
    public final static String BUFFER_SIZE = "BufferSize";
    public final static String CTP = "CTP";

    //PTP Clock
    public final static String CLOCK_PROFILE = "ClockProfile";
    public final static String CLOCK_TYPE = "ClockType";
    public final static String OPERATIONAL_MODE = "OperationalMode";
    public final static String TIME_SOURCE = "TimeSource";
    public final static String PRIORITY_1 = "Priority1";
    public final static String PRIORITY_2 = "Priority2";
    public final static String LOCAL_PRIORITY = "LocalPriority";
    public final static String CLOCK_ACCURACY = "ClockAccuracy";
    public final static String SCALED_LOG_VARIANCE = "ScaledLogVariance";
    public final static String CLOCK_SYNC = "ClockSync";
    public final static String CURRENT_TOD = "CurrentTimeOfDay";
    public final static String ACTIVE_SLAVE_PORT = "ActiveSlavePort";
    public final static String CLOCK_RECOVERY_STATE = "ClockRecoveryState";
    public final static String PHASE_RECOVERY_STATE = "PhaseRecoveryState";
    public final static String TIME_TRACEABILITY_STATUS = "TimeTraceabilityStatus";
    public final static String TIME_TRACEABILITY_CHANGED = "TimeSinceTimeTraceabilityChanged";
    public final static String FREQ_TRACEABILITY_STATUS = "FreqTraceabilityStatus";
    public final static String FREQ_TRACEABILITY_CHANGED = "TimeSinceFreqTraceabilityChanged";
    public final static String SYNCE_ADMINISTRATION_CONTROL = "SyncEAdministrationControl";
    public final static String QL_MODE_ADMINISTRATION_CONTROL = "QLModeAdministrationControl";
    public final static String EXPECTED_QL = "ExpectedQL";
    public final static String ASSUMED_QL = "AssumedQL";
    public final static String RECEIVED_QL = "ReceivedQL";
    public final static String CLOCK_CLASS = "ClockClass";

    //PTP Port
    public final static String PORT_NOT_SLAVE = "PortNotSlave";
    public final static String DEST_MAC_ADDRESS_TYPE = "DestMacAddressType";
    public final static String SYNC_MESSAGE_RATE = "SyncMessageRate";
    public final static String DELAY_REQ_MESSAGE_RATE = "DelayReqMessageRate";
    public final static String ANNOUNCE_MSG_RATE = "AnnounceMsgRate";
    public final static String ANNOUNCE_RECEIPT_TIMEOUT = "AnnounceReceiptTimeout";
    public final static String SYNC_RECEIPT_TIMEOUT = "SyncReceiptTimeout";
    public final static String DELAY_RESP_TIMEOUT = "DelayRespTimeout";
    public final static String PORT_STATE = "PortState";
    public final static String BMCA_DECISION_CODE = "BmcaDecisionCode";
    public final static String PEER_PORT_MAC_ADDRESS = "PeerPortMacAddres";
    public final static String PEER_PORT_IDENTITY = "PeerPortIdentity";
    public final static String PORT_MASTER_ONLY = "PortMasterOnly";

    //TS
    public final static String CLOCK_RECOVERY_MODE = "ClockRecoveryMode";
    public final static String TIME_HOLDOVER_ACCURACY = "TimeHoldoverAccuracy";

    //SOOC
    public static final String SLAVE_IP_ADDRESS = "SlaveIPAddress";
    public static final String SLAVE_SUBNET_MASK = "SlaveSubnetMask";
    public static final String MASTER_CLOCK_IP_ADDRESS = "MasterClockIPAddress";
    public final static String MASTER_DELAY_MECHANISM_PARAM = "MasterDelayMechanism";
    public final static String MASTER_SYNC_MESSAGE_RATE = "MasterSyncMessageRate";
    public final static String MASTER_DELAY_MESSAGE_RATE = "MasterDelayMessageRate";

    //OCSP
    public static final String PTP_FLOW_POINT = "PTPFlowPoint";
  }

  public static class LrPropAdvaCFMCCM
  {
    public final static String THIS_LAYER_ACTIVE_PARAM               = "ThisLayerActive";
    public final static String CFM_MD_NAME_PARAM                     = "CfmMdName";
    public final static String CFM_MD_LEVEL_PARAM                    = "MdLevel";
    public final static String CFM_MD_MIP_CREATION                   = "MdMipCreationControl";
    public final static String CFM_MA_CCM_INTERVAL_PARAM             = "MaCcmInterval";
    public final static String CFM_MA_PRIMARY_VID_PARAM              = "MaPrimaryVid";
    public final static String CFM_MA_MIP_CONTORL_PARAM              = "MaMipCreationControl";
    public final static String CFM_MA_MEP_TABLE_PARAM                = "MaMepList";
    public final static String CFM_MEP_IDENTIFIER_PARAM              = "MepIdentifier";
    public final static String CFM_MEP_DIRECTION_PARAM               = "MepDirection";
    public final static String CFM_MEP_ADMIN_STATE_PARAM             = "MepAdministrationControl";
    public final static String CFM_MEP_SERVICE_STATE_PARAM           = "MepServiceState";
    public final static String CFM_MEP_PRIMARY_VID_PARAM             = "MepPrimaryVid";
    public final static String CFM_MEP_CCI_ENABLED_PARAM             = "MepCcmGeneration";
    public final static String CFM_MEP_CCM_LTM_PRIORITY_PARAM        = "MepVlanPriority";
    public final static String CFM_MEP_LOWEST_PRIORITY_DEFECT_PARAM  = "MepLowestPriorityDefect";
    public final static String CFM_MEP_LLF_TRIGGER                   = "MepLlfTrigger";
    public final static String LM_TX_COUNT_ALL_PRIORITIES            = "LmTxCountAllPriorities";
    public final static String LM_RX_COUNT_ALL_PRIORITIES            = "LmRxCountAllPriorities";
    public final static String DUAL_ENDED_COUNT_ALL_PRIORITIES       = "DualEndedCountAllPriorities";
    public final static String LM_COUNT_IN_PROFILE_ONLY              = "LmCountInProfileOnly";
    public final static String CFM_MEP_AIS_GENERATION                = "MepAisGeneration";
    public final static String CFM_MEP_AIS_TRIGGER                   = "MepAisTrigger";
    public final static String CFM_MEP_ERROR_CCM_LAST_FAILURE        = "MepLastErrorCcmReceived";
    public final static String CFM_MEP_XCON_CCM_LAST_FAILURE         = "MepLastXConCcmReceived";
    public final static String CFM_MEP_DEFECTS                       = "MepAisDefectReceived";
    public final static String CFM_MEP_AIS_CLIENT_MD_LEVEL           = "MepAisClientMdLevel";
    public final static String CFM_MEP_AIS_PRIORITY                  = "MepAisPriority";
    public final static String CFM_MEP_AIS_TRANSMISSION_INTERVAL     = "MepAisTransmissionInterval";
    public final static String CFM_MEP_MAC_ADDRESS_PARAM             = "MepMacAddress";
    public final static String CFM_MEP_HIGHEST_PRIORITY_DEFECT       = "MepHighestPriorityDefect";
    public final static String CFM_MEP_XCON_CCM_DEFECT               = "MepCcmMisconnectionDefectReceived";
    public final static String CFM_MEP_RDI_DEFECT_RECEIVED           = "MepRdiDefectReceived";
    public final static String CFM_MEP_MAC_DEFECT_RECEIVED           = "MepMacDefectReceived";
    public final static String CFM_MEP_RMEP_CCM_DEFECT_RECEIVED      = "MepRemoteMepCcmDefectReceived";
    public final static String CFM_MEP_CCM_ERROR_DEFECT_RECEIVED     = "MepCcmErrorDefectReceived";

    public final static String CFM_RMEP_IDENTIFIER_PARAM              = "RMepIdentifier";
    public final static String CFM_RMEP_STATE                         = "RMepState";
    public final static String CFM_RMEP_FAILED_OK_TIME                = "RMepFailedOkTime";
    public final static String CFM_RMEP_MAC_ADDRESS                   = "RMepMacAddress";
    public final static String CFM_RMEP_RDI                           = "RMepRDI";
    public final static String CFM_RMEP_PORT_STATUS_TLV               = "RMepPortStatusTlv";
    public final static String CFM_RMEP_INTERFACE_STATUS_TLV          = "RMepInterfaceStatusTlv";
  }

  public static class LrPropADVAOpticalMultiplexSelection{

    public final static String FREQUENCY_SPACING="FrequencySpacing";
    public final static String FREQUENCY_SPREAD="FrequencySpread";
    public final static String MAX_NUMBER_OCH="MaxNumberOCh";
    public final static String OMBAND="OMBand";
  }

  public static class LrOpticalChannel{

    public final static String SFP_REACH_PARAM 					= "SfpReach";
    public final static String SFP_LASER_WAVE_LENGTH_PARAM 		= "SfpLaserWaveLength";
    public final static String SFP_MEDIA_TYPE_PARAM 			= "SfpMediaType";
    public final static String CLIENT_RATE="ClientRate";
    public final static String TUNED_FREQUENCY="TunedFrequency";
    public final static String TUNEDABLE_BASE_FREQUENCY="TunableBaseFrequency";
    public final static String TUNEDABLE_FREQUENCY_SPACING="TunableFrequencySpacing";
    public final static String NUMBER_OF_TUNEDABLE_FREQUENCIES="NumberOfTunableFrequencies";
    public final static String AUTO_TUNE="AutoTune";
    public final static String FREQUENCY="Frequency";
    public final static String SFP_VENDOR_NAME_PARAM 			= "SfpVendorName";
    public final static String SFP_PART_NUMBER_PARAM 			= "SfpPartNumber";
    public final static String SFP_SERIAL_NUMBER_PARAM 			= "SfpSerialNumber";
    public final static String SFP_DATE_OF_MANUFACTURE_PARAM 			= "SfpDateOfManufacture";
  }

  /**
   * Parameters of PROP_ADVA_SyncEthernet Layer.
   *
   */
  public static class PropAdvaSyncEthernet {
    public final static String SYNCE_ADMINISTRATION_CONTROL_PARAM 	= "SyncEAdministrationControl";
    public final static String QL_MODE_ADMINISTRATION_CONTROL_PARAM = "QLModeAdministrationControl";
    public final static String EXPECTED_QL_PARAM 					= "ExpectedQL";
    public final static String ASSUMED_QL_PARAM 					= "AssumedQL";
    public final static String RECEIVED_QL_PARAM 					= "ReceivedQL";
    public final static String TRANSMIT_QL_PARAM 					= "TransmitQL";
    public final static String SQUELCH_QL_PARAM 					= "SquelchQL";
    public final static String SYNCE_AUTO_NEGOTIATION_CLOCK_PARAM	= "SyncEAutoNegotiationClockMode";
  }

  /**
   * Parameters of PROP_ADVA_BITS Layer.
   *
   */
  public static class PropAdvaBits {
    public static final String ADMINISTRATION_CONTROL_PARAM 	= "AdministrationControl";
    public static final String SERVICE_STATE_PARAM 				= "ServiceState";
    public static final String SECONDARY_STATE_PARAM 			= "SecondaryState";
    public static final String LINE_TYPE_PARAM 					= "LineType";
    public static final String LINE_CODE_PARAM 					= "LineCode";
    public static final String FRAME_FORMAT_PARAM 				= "FrameFormat";
    public static final String SA_BIT_DESIGNATION_PARAM 		= "SABitDesignation";
    public static final String LINE_BUILD_OUT_PARAM        = "LineBuildOut";
    public static final String IMPEDANCE_PARAM        = "Impedance";
  }

  /**
   * Parameters of PHYSICAL_OPTICAL and PHYSICAL_ELECTRICAL layers.
   */
  public static class LrElectricalAndOptical {
    public final static String THIS_LAYER_ACTIVE_PARAM = "ThisLayerActive";
    public final static String CONNECTOR_TYPE_PARAM = "ConnectorType";
  }

  /**
   * Parameters of PROP_ADVA_Sync_Reference Layer.
   *
   */

  public static class LrDsrGigabitAndFastEthernet {
    public final static String ADMINISTRATION_CONTROL_PARAM = "AdministrationControl";
    public final static String SERVICE_STATE_PARAM = "ServiceState";
    public final static String AUTO_NEGOTIATION_PARAM = "AutoNegotiation";
    public final static String ADMINISTRATIVE_SPEED_RATE_PARAM = "AdministrativeSpeedRate";
    public final static String ACTUAL_SPEED_RATE_PARAM = "ActualSpeedRate";
    public final static String DUPLEX_MODE_PARAM = "DuplexMode";
    public final static String SPEED_MODE_PARAM = "SpeedMode";
    public final static String ACTUAL_DUPLEX_MODE_PARAM = "ActualDuplexMode";
    public final static String MAXIMUM_FRAME_SIZE_PARAM = "MaximumFrameSize";
    public final static String AUTO_NEG_REM_SIGNALLING_DETECTED_PARAM = "AutoNegRemSignallingDetected";
    public final static String AUTO_NEG_STATUS_PARAM = "AutoNegStatus";
    public final static String AUTO_NEG_LOCAL_TECHNOLOGY_AVAILABILITY_PARAM = "AutoNegLocalTechnologyAvailability";
    public final static String AUTO_NEG_REMOTE_TECHNOLOGY_AVAILABILITY_PARAM = "AutoNegRemoteTechnologyAvailability";
    //New attribute for GE20X Access Ports
    public final static String SECONDARY_STATE_PARAM = "SecondaryState";
  }

  public static class PropAdvaSyncReference {
    public static final String SYNC_REFERENCE         = "SyncReference";
    public static final String ALIAS                  = "Alias";
    public static final String REF_PRIORITY           = "RefPriority";
    public static final String REF_STATUS             = "RefStatus";
    public static final String REF_STATE              = "RefState";
    public static final String REF_RECEIVED_QL        = "RefReceivedQL";
    public static final String REF_EFFECTIVE_QL       = "RefEffectiveQL";
    public static final String OPERATION_TYPE         = "RefOperationType";
  }

  public static class LrPropAdvaESAProbe {
    public static final String PROBE_INDEX                  = "ProbeIndex";
    public static final String PROBE_PROTOCOL               = "ProbeProtocol";
    public static final String PROBE_SRC_MEP_ID             = "ProbeSrcMepID";
    public static final String PROBE_SRC_PORT               = "SourcePort";
    public static final String PROBE_DEST_MEP_TYPE          = "ProbeDestMepType";
    public static final String PROBE_DEST_MEP_ID            = "ProbeDestMepID";
    public static final String PROBE_MULTI_COS_ENABLED      = "ProbeMultiCOSEnabled";
    public static final String PROBE_COS_TYPE               = "ProbeCOSType";
    public static final String PROBE_DMM_PKT_SIZE           = "ProbeDMMPktSize";
    public static final String PROBE_DMM_INTERVAL           = "ProbeDMMInterval";
    public static final String PROBE_SOAM_INTERVAL          = "ProbeSOAMInterval";
    public static final String PROBE_SOAM_PKT_SIZE          = "ProbeSOAMPktSize";
    public static final String PROBE_AVAIL_FLR_THSLD        = "AvailabilityFLRThreshold";
    public static final String PROBE_FLR_DELTA_T            = "FLRDeltaTLMIntervals";
    public static final String PROBE_CON_DELTA_TS           = "ConsecDeltaTSAvailability";
    public static final String PROBE_HISTORY_BINS           = "ProbeHistoryBins";
    public static final String PROBE_HISTORY_INTERVAL       = "ProbeHistoryInterval";
    public static final String PROBE_DIST_HISTORY_BINS      = "ProbeDistHistoryBins";
    public static final String PROBE_DIST_HISTORY_INTERVAL  = "ProbeDistHistoryInterval";
    public static final String PROBE_ALIAS                  = "ProbeAlias";
  }

  public static class LrPropAdvaESASchedule {
    public static final String PROBE_SCHEDULE_INDEX         = "ProbeScheduleIndex";
    public static final String PROBE_SCHEDULE_DESC          = "ProbeScheduleDescription";
    public static final String PROBE_SCHEDULE_TYPE          = "ProbeScheduleType";
    public static final String PROBE_SCHEDULE_START_MODE    = "ProbeScheduleStartMode";
    public static final String PROBE_SCHEDULE_START_TIME    = "ProbeScheduleStartTime";
    public static final String PROBE_SCHEDULE_DURATION_TYPE = "ProbeScheduleDurationType";
    public static final String PROBE_SCHEDULE_DURATION      = "ProbeScheduleDuration";
    public static final String PROBE_SCHEDULE_INTERVAL      = "ProbeScheduleInterval";
    public static final String PROBE_SCHEDULE_STATUS        = "ProbeScheduleStatus";
  }

  public static class LrPropAdvaRemoteCPE {
    //AMP
    public static final String ROLE = "Role";
    public static final String PROTOCOL = "Protocol";
    public static final String ENABLED = "Enabled";
    public static final String STATUS = "Status";
    public static final String PORT_OID = "AssociatedPort";
    public static final String REM_SYS_NAME = "RemoteSysName";
    public static final String REM_SYS_IP_ADDR = "RemoteSysIpAddress";
    public static final String REM_SYS_IP_MASK = "RemoteSubnetMask";
    public static final String REM_SYS_DEF_GATEWAY = "RemoteDefaultGateway";
    public static final String REM_TUNNEL_INDEX = "RemoteTunnelIndex";
    public static final String REM_TUNNEL_NAME = "RemoteTunnelName";
    public static final String REM_TUNNEL_TYPE = "RemoteTunnelType";
    public static final String REM_TUNNEL_IP_ADDR = "RemoteTunnelIpAddress";
    public static final String REM_TUNNEL_IP_MASK = "RemoteTunnelSubnetMask";
    public static final String REM_TUNNEL_VLAN_ID = "RemoteTunnelVLANId";
    public static final String REM_TUNNEL_SVLAN_ID_ENABLED = "RemoteTunnelSVLANIdEnabled";
    public static final String REM_TUNNEL_SVLAN_ID = "RemoteTunnelSVLANId";
    public static final String REM_TUNNEL_RIP2PKTS_ENABLED = "RemoteTunnelRip2PktsEnabled";
    public static final String REM_TUNNEL_COS = "RemoteTunnelCOS";
    public static final String REM_TUNNEL_CIR = "RemoteTunnelCIR";
    public static final String REM_TUNNEL_EIR = "RemoteTunnelEIR";
    public static final String REM_TUNNEL_BUFFER_SIZE = "RemoteTunnelBufferSize";
    public static final String REM_TUNNEL_ENCAP_TYPE = "RemoteTunnelEncapsulationType";
    public static final String REM_TUNNEL_MTU = "RemoteTunnelMTU";
    public static final String REM_SYS_SRC_IP_ADDR_TYPE = "RemSysSrcIpAddrType";
    public static final String REM_SYS_SRC_IP_ADDR_IF_NAME = "RemSysSrcIpAddrIfName";
    public static final String REM_SYS_SNMP_V1_IF_NAME = "RemSysSnmpV1IfName";
    public static final String REMOTE_TYPE = "RemoteType";
    public static final String CREATE_LINK = "CreateLink";
    public static final String LINK_NAME = "LinkName";

    //Management Tunnel

    public static final String  MT_INDEX ="TunnelIndex";
    public static final String  MT_TYPE ="TunnelType";
    public static final String  MT_IPMODE ="IPMode";
    public static final String  MT_IP_ADDRESS ="IPAddress";
    public static final String  MT_SUBNET_MASK ="SubnetMask";
    public static final String  MT_CVLAN_ID_ENABLED ="CVLANIdEnabled";
    public static final String  MT_VLAN_ID ="CTagVLANId";
    public static final String  MT_CIR ="CIR";
    public static final String  MT_EIR ="EIR";
    public static final String  MT_COS ="COS";
    public static final String  MT_BUFFERSIZE ="BufferSize";
    public static final String  MT_SVLAN_ID_ENABLED ="SVLANIdEnabled";
    public static final String  MT_STAG_VLAN_ID ="STagVLANId";
    public static final String  MT_RIP2PKTS_ENABLED ="Rip2PktsEnabled";
    public static final String  MT_ENCAPSULATION_TYPE ="EncapsulationType";
    public static final String  MT_MTU ="MTU";
    public static final String  MT_DHCP_ENABLED ="DHCPEnabled";
    public static final String  MT_MAC_ADDRESS ="MACAddress";
    public static final String  MT_DEFAULT_GATEWAY ="DefaultGateway";

    //Static Route
//    public static final String DEST = "Dest";
    public static final String MASK = "StaticRouteSubnetMask";
    public static final String NEXT_HOP = "GatewayIPAddress";
    public static final String ROUTE_INTERFACE = "StaticRouteInterface";
    public static final String METRIC = "StaticRouteMetric";
    public static final String ADVERTISE = "StaticRouteAdvertise";


//    public final static String CPE_HOSTNAME_PARM = "CPEHostname";
//    public final static String IP_ADDRESS = "IpAddress";
//    public final static String SUBNET_MASK="SubnetMask";
//    public final static String REMOTE_TYPE_PARM  = "RemoteType";
//    public final static String DEFAULT_GATEWAY = "DefaultGateway";
//    public final static String CREATE_LINK_PARM  = "CreateLink";
//    public final static String LINK_NAME_PARM    = "LinkName";
  }

  public static class PropAdvaRemoteCPE {
    public static final String CPE_HOST_NAME = "CPEHostname";
    public static final String REMOTE_TYPE = "RemoteType";
    public static final String CREATE_LINK = "CreateLink";
    public static final String LINK_NAME = "LinkName";
  }

}
