/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;


public enum GenericEnabledDisabledTranslation {

  ENABLED   (1, "Enabled"),
  DISABLED  (2, "Disabled");

  private final int id;
  private final String mtosiString;

  private GenericEnabledDisabledTranslation (final int id, final String mtosiString)
  {
    this.id   = id;
    this.mtosiString = mtosiString;
  }

  @SuppressWarnings("unchecked")
  public static GenericEnabledDisabledTranslation getEnumByString (final String mtosiString) {
    for (GenericEnabledDisabledTranslation enumValue : values()) {
      if (mtosiString.equals(enumValue.getMtosiString())) {
        return enumValue;
      }
    }
    return null;
  }

  public String getMtosiString() {
    return mtosiString;
  }

  public int getId() {
    return id;
  }
}
