/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: <PERSON><PERSON><PERSON><PERSON><PERSON>
 */
package com.adva.nlms.mediation.mtosi.v2.utils.translations.f3;

public enum CMPtpPortStateTranslation implements TranslatableEnum {
  NOT_APPLICABLE(0,"n/a"),
  INITIALIZING(1,"Initializing"),
  FAULTY(2,"Faulty"),
  DISABLED(3,"Disabled"),
  LISTENING(4,"Listening"),
  UNCALIBRATED(5,"Uncalibrated"),
  SLAVE(6,"Slave"),
  PREMASTER(7,"Premaster"),
  MASTER(8,"Master"),
  PASSIVE(9,"Passive");

  CMPtpPortStateTranslation(int mibValue, String mtosiString) {
    this.mibValue = mibValue;
    this.mtosiString = mtosiString;
  }

  private final int mibValue;
  private final String mtosiString;

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}
