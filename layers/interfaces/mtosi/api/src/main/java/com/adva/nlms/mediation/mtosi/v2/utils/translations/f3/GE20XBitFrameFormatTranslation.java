/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v2.utils.translations.f3;

public enum GE20XBitFrameFormatTranslation implements TranslatableEnum {
    none(1, "None"),
    t1_sf(2, "T1-SF"),
    t1_esf(3, "T1-ESF"),
    e1_unframed(4, "E1-UNFRAMED"),
    e1_crc4(5, "E1-CRC4"),
    e1_dualframe(6, "E1-DUALFRAME"),
    NOT_APPLICABLE	(-1, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private GE20XBitFrameFormatTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}