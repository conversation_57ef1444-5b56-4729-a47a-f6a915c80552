/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v2.utils.translations.f3;

public enum CMAdministrationControlTranslation implements TranslatableEnum {
  IN_SERVICE      (1, "Enabled"),
  MANAGEMENT      (2, "PROP_ADVA_Management"),
  MAINTENANCE     (3, "PROP_ADVA_Maintenance"),
  DISABLED        (4, "Disabled"),
  UNASSIGNED      (5, "PROP_ADVA_Unassigned"),
  NOT_APPLICABLE  (6, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private CMAdministrationControlTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }

  public static CMAdministrationControlTranslation getAdminControlFromMtosiValue(String mtosiValue){
    for(CMAdministrationControlTranslation ac :values()){
      if(ac.getMtosiString().equals(mtosiValue)) return ac;
    }
    return NOT_APPLICABLE;
  }
}