/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1;

import java.io.File;

public class MtosiConstants
{
	public final static String VERSION = "1.1";
	public final static String MANUFACTURER = "ADVA Optical Networking";

  public final static String PRODUCTNAME = "FSP Network Manager";
	
	public final static String TRUE = "True";
	public final static String FALSE = "False";
	
	public final static String NOT_APPLICABLE = "n/a";
	public final static String UNKNOWN = "unknown";
	
	public final static String SHELF_TEXT = "/shelf=";
	public final static String DEFAULT_SHELF_NAME = SHELF_TEXT + "1";

	public final static String SLOT_TEXT = "/slot=";
	public final static String DEFAULT_SLOT_NAME = SLOT_TEXT + "1";
	public final static String SUBSLOT_TEXT = "/sub_slot=";
	public final static String PORT_TEXT = "/port=";
	public final static String PSU_TEXT = "PSU";
	public final static String FAN_TEXT = "FAN";

	public final static String EG_QUEUE_TEXT    = "/egqueue=";
	public final static String ING_QUEUE_TEXT   = "/ingqueue=";
	public final static String ING_POLICER_TEXT = "/ingpolicer=";
	public final static String EG_POLICER_TEXT  = "/egpolicer=";
  public final static String ING_SHAPER_TEXT = "/ingshaper=";
  public final static String EG_SHAPER_TEXT = "/egshaper=";
  public final static String LAG_FRAGMENT_TEXT  = "lag_fragment=";
  public final static String TDFR_REFERENCE_TEXT  = "/TDFr_reference=";
  public final static String LAG_FTP_TEXT  = "/port=LAG-";
  public final static String BONDED_FTP_TEXT  = "/port=ETH-";
  public final static String PME_TEXT = "/port=2BPME-";
  public final static String HN_ETH_TEXT = "/port=ETH-";

  public final static int TDFR_SYNC_REFS_MAX = 10;
  public final static int SCU_INDEX = 1;
	
	public final static String POLICER_TEXT     = "policer=";
  public final static String SHAPER_TEXT     = "shaper=";
  public final static String QUEUE_TEXT       = "queue=";

  public final static String PORT_NET = "NET";
  public final static String PORT_NETA = "NET-A";
  public final static String PORT_NETB = "NET-B";
  public final static String PORT_NET1 = "NET-1";
  public final static String PORT_NET2 = "NET-2";
  public final static String PORT_WAN1 = "WAN-1";
  public final static String PORT_WAN2 = "WAN-2";

	public final static String PORT_ACC = "ACC";
  public final static String PORT_ACC1 = "ACC-1";
  public final static String PORT_ACC2 = "ACC-2";
  public final static String PORT_ACC3 = "ACC-3";
  public final static String PORT_ACC4 = "ACC-4";
  public final static String PORT_ACC5 = "ACC-5";
  public final static String PORT_ACC6 = "ACC-6";
  public final static String PORT_LAN1 = "LAN-1";
	public final static String PORT_LAN2 = "LAN-2";
  public final static String PORT_LAN3 = "LAN-3";
  public final static String PORT_LAN4 = "LAN-4";
  public final static String PORT_LAN5 = "LAN-5";

	
	public final static String SLOT_PSUA = "PSU-A";
	public final static String SLOT_PSUB = "PSU-B";
	public final static String SLOT_PSU1 = "PSU-1";
	public final static String SLOT_PSU2 = "PSU-2";
  public final static String SLOT_FAN = "FAN";
  public final static String SLOT_FAN1 = "FAN-1";
  public final static String SLOT_FAN2 = "FAN-2";
		
	public final static String EQUIPMENT_SFP = "SFP";
	public final static String EQUIPMENT_PSU = "PSU";
  public final static String EQUIPMENT_150CC_GE201 = "FSP150CC-GE201";
  public final static String EQUIPMENT_150CC_GE201SE = "FSP150CC-GE201se";
  public final static String EQUIPMENT_150CC_GE206 = "FSP150CC-GE206";
	public final static String EQUIPMENT_150CP = "FSP150CP";
	public final static String EQUIPMENT_EFM = "FSP150CC-GE102ProHEFM";
  public final static String EQUIPMENT_HN4000 = "HN4000";
  public final static String EQUIPMENT_HN4000I = "HN4000-I";
  public final static String EQUIPMENT_HN4000e = "HN4000e";
  public final static String EQUIPMENT_HN400 = "HN400";
  public final static String EQ_HN4000_1000_X2  = "Module1000x2";
  public final static String EQ_HN4000_1000_TX2 = "Module1000tx2";
  public final static String EQ_HN4000_100_X2   = "Module100x2";
  public final static String EQ_HN4000_100_TX2  = "Module100tx2";
  public final static String EQ_HN4000_TE_3     = "ModuleTe3";
  public final static String EQUIPMENT_NEMI = "NEMI";
  public final static String EQUIPMENT_STU = "STU";
  public final static String EQUIPMENT_NTU_GE = "NTU-GE";
  public final static String EQUIPMENT_NTE_GE = "NTE-GE";
  public final static String EQUIPMENT_NTE_GE_SYNC = "NTE-GE-SYNC";
  public final static String EQUIPMENT_FAN = "FAN";
  public final static String EQUIPMENT_AMI = "AMI";
  public final static String EQUIPMENT_STI = "STI";
  public final static String EQUIPMENT_SWF = "SWF";
  public final static String EQUIPMENT_PSU_DC = "PSU DC";
  public final static String EQUIPMENT_PSU_DC24 = "PSU DC24";
  public final static String EQUIPMENT_PSU_AC = "PSU AC";
  public final static String EQUIPMENT_SCU = "SCU";
  public final static String EQUIPMENT_SCU_T = "SCU-T";
		
	public final static String HOLDER_SHELF = "shelf";
	public final static String HOLDER_SLOT = "slot";
	public final static String HOLDER_SUBSLOT = "sub_slot";
	public final static String HOLDER_SUBSLOT_ETH = "/sub_slot=ETH-";
	
	public final static String NAMING_ADVA = "ADVA/";
	public final static String NAMING_ADVA_FNM = "ADVA/FNM/";
	
	public final static String DEFAULT_EQUIPMENT_NAME = "1";
	public final static String DEFAULT_FIXED_SLOT_NAME = DEFAULT_SHELF_NAME + DEFAULT_SLOT_NAME;

	public final static String VENDOR_IPADDRESS = "ipValue";
	public final static String VENDOR_SUBNET_MASK = "subNetworkMask";
	public final static String VENDOR_DISCOVERY_STATE = "discoveryState";
  public final static String VENDOR_RESOURCE_STATE = "resourceState";
  public final static String VENDOR_EFM_MGR_PRODUCT_NAME = "efmMgrProductName";
  public final static String VENDOR_EFM_MGR_ME_NAME = "efmMgrMEName";
  public final static String VENDOR_TMF_NAMESPACE = "tmf854.v1";
  public final static String VENDOR_LAYERED_PARAMETERS = "layeredParameters";
  public final static String VENDOR_LAYER = "layer";
  public final static String VENDOR_NVS = "nvs";
  public final static String VENDOR_NAME = "name";
  public final static String VENDOR_VALUE = "value";
  public final static String VENDOR_NAMESPACE = "adva.tmf854ext.v1";
  public final static String VENDOR_ADMINISTRATION_CONTROL = "administrationControl";
  public final static String VENDOR_SECONDARY_STATE = "secondaryState";
  public final static String VENDOR_IS_MASTER = "isMaster";
  public final static String VENDOR_TRANSMISSION_PARAMS = "transmissionParams";
  public final static String VENDOR_WORKING_MODE = "workingMode";
  public final static String VENDOR_WORKING_MODE_MASTER = "Master";
  public final static String VENDOR_WORKING_MODE_SLAVE = "Slave";

  public final static String NATIVE_FID_PREFIX = "FID-";
	public final static char FDFR_SEP = '^';

  public final static String SLOT_NAME_PSUA = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_PSUA;
	public final static String SLOT_NAME_PSUB = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_PSUB;
  public final static String SLOT_NAME_PSU1 = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_PSU1;
	public final static String SLOT_NAME_PSU2 = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_PSU2;
  public final static String SLOT_NAME_FAN1 = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_FAN1;
	public final static String SLOT_NAME_FAN2 = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_FAN2;
	public final static String SLOT_NAME_WAN1 = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_WAN1;
	public final static String SLOT_NAME_WAN2 = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_WAN2;
	public final static String SLOT_NAME_LAN1 = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_LAN1;
	public final static String SLOT_NAME_LAN2 = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_LAN2;
	public final static String SLOT_NAME_LAN3 = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_LAN3;
	public final static String SLOT_NAME_LAN4 = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_LAN4;
	public final static String SLOT_NAME_LAN5 = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_LAN5;
	public final static String SLOT_NAME_ACC = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_ACC;
  public final static String SLOT_NAME_ACC4 = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_ACC4;
  public final static String SLOT_NAME_ACC5 = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_ACC5;
  public final static String SLOT_NAME_ACC6 = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_ACC6;
	public final static String SLOT_NAME_NETA = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_NETA;
	public final static String SLOT_NAME_NETB = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_NETB;
	public final static String SLOT_NAME_NET1 = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_NET1;
	public final static String SLOT_NAME_NET2 = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_NET2;

  public final static String SLOT_HN_PSUA = MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_PSUA;
  public final static String SLOT_HN_PSUB = MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_PSUB;
  public final static String SLOT_HN_FAN = MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_FAN;
  public final static String SUBSLOT_HN_SFP = MtosiConstants.SLOT_TEXT + 2 + SUBSLOT_TEXT + "ETH-";
  public final static String SUBSLOT_HN400_SFP = MtosiConstants.SLOT_TEXT + 1 + SUBSLOT_TEXT + "ETH-";
  public final static String PORT_HN_SFP = MtosiConstants.SLOT_TEXT + 2 + PORT_TEXT + "ETH-";

  public final static String FDFRT_POINT_TO_POINT = "FDFRT_POINT_TO_POINT";
  public final static String FDFR_TYPE_PARAM = "fdfrType";
  public final static String EVC_TYPE_ELINE = "ELINE";

  public final static String MODULE_INSERTED_REMOVED="Module inserted ore removed";
  public final static String MODULE_INSERTED="Module inserted";
  public final static String MODULE_REMOVED="Module removed";

  public final static String WSDL_TMF_PATH = "ws"+ File.separator+"resources"+File.separator+"adva"+File.separator+"AllTMFServices.wsdl";

  public final static String ENCAPSULATION_TYPE_ETH="Ethernet";
  public final static String LAG  = "lag";
}