/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v2.utils.translations.f3;

public enum BooleanTypeTranslation implements TranslatableEnum {
  ENABLED        (1, "True"),
  DISABLED       (2, "False"),
  NOT_APPLICABLE (3, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private BooleanTypeTranslation (final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}