/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: arongas
 */
package com.adva.nlms.mediation.mtosi.common.doclet;

import java.lang.annotation.ElementType;
import java.lang.annotation.RetentionPolicy;

@java.lang.annotation.Target({ElementType.METHOD})
@java.lang.annotation.Retention(RetentionPolicy.RUNTIME)
public @interface ErrorCode {
  String[] errorTypes() default {};
  String[] texts() default {};
  String[] constants() default {};
  String[] variables() default {};
  String[] operations() default {};
  String[] details() default {};
}