/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 */

plugins {
    id 'com.adva.gradle.plugin.aspectj-weaver'
    id 'com.adva.gradle.plugin.eclipse-link-weaver'
}

setupModule(project)

aspectjWeave {
    if (briefOutput) {
        lintLevel = 'ignore'
    }
}

eclipseLinkWeave {
    masterModule mod_mediation.project
}

dependencies {
    implementation libs.log4j.api
    implementation libs.aspectjrt
    implementation libs.jaxws.api
    implementation libs.commons.lang3
    implementation libs.guava
    implementation libs.snmp4j
    implementation libs.xalan
    implementation libs.jakarta.servlet.api
    implementation libs.castor
    implementation libs.commons.codec
    implementation libs.xercesImpl
    implementation libs.jakarta.validation.api
    implementation libs.jakarta.inject
    implementation libs.commons.collections4
    implementation libs.jakarta.jms.api
    implementation libs.jetty.http
    implementation libs.cxf.core
    implementation(libs.cxf.rt.ws.security) {
        exclude group: 'org.opensaml', module: 'opensaml-xacml-saml-impl'
        exclude group: 'org.opensaml', module: 'opensaml-xacml-impl'
        exclude group: 'org.opensaml', module: 'opensaml-saml-impl'
    }

    implementation modep(mod_interfaces_mtosi_api)
    implementation modep(mod_persistence_api)
    implementation modep(mod_mediation)
    implementation modep(mod_nmscommon)
    implementation modep(mod_yellow_pages)
    implementation modep(mod_ui_framework_api)
    implementation modep(mod_enc_utils)
    implementation modep(mod_apps_sm_api)
    implementation modep(mod_support_server_state_api)
    implementation modep(mod_rest_api)
    implementation modep(mod_global_security_api)
    implementation modep(mod_server_infra_server_modules_api)
    implementation modep(mod_rest_infra)
    implementation modep(mod_interfaces_mtosi_api)
    implementation modep(mod_infrastructure_licensing_api)

    aspectjpath modep( mod_persistence_api )
    aspectjpath modep(mod_enc_utils)
    aspectjpath modep(mod_rest_infra)

    eclipseLinkWeave libs.nms.ni.model

    testImplementation libs.bundles.junit
    testImplementation libs.mockito.core

    runtimeOnly libs.cxf.tools.common
    runtimeOnly libs.cxf.tools.validator
    runtimeOnly libs.cxf.tools.wsdlto.core
    runtimeOnly libs.cxf.tools.wsdlto.databinding.jaxb
    runtimeOnly libs.cxf.tools.wsdlto.frontend.jaxws
    runtimeOnly libs.cxf.rt.bindings.soap
    runtimeOnly libs.cxf.rt.frontend.jaxws
    runtimeOnly libs.cxf.rt.transports.http
}

test {
    useJUnitPlatform()
}