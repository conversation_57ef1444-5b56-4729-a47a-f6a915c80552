/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import jakarta.xml.ws.Holder;

import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import v1.tmf854.ConnectionTerminationPointT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.PhysicalTerminationPointT;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.f3.entity.port.MTOSIPortF3;
import com.adva.nlms.mediation.config.f3.entity.port.acc.MTOSIPortF3Acc;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
//import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiSupported;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;

public class GetTPWorkerCM extends GetTPWorker {
  public GetTPWorkerCM (Holder<HeaderT> mtosiHeader, NamingAttributesT tpName, NetworkElement ne) {
    super(mtosiHeader, tpName, ne);
  }

  /**
   * Handles PTP requests.
   * @throws Exception When something goes wrong
   */
  @Override
  protected void executeGetPTP() throws Exception {
    final Port port = ManagedElementFactory.getPort(tpName);
    PhysicalTerminationPointT ptp;

//    if (!(port instanceof MtosiSupported)) {
//      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
//              MtosiErrorConstants.PTP_NOT_FOUND);
//    }
    //Need to update the Loopback Status and possibly other Status attributes.
    if (port instanceof MTOSIPortF3) {
    	((MTOSIPortF3)port).doVolatilePolling();
    }
//    ptp = ((MtosiSupported)port).getMtosiTranslator().toMtosiPTP();
    ptp = new MtosiTranslatorFacade(ne,getMtosiHeader(),  MtosiErrorConstants.PTP_NOT_FOUND).getMtosiTranslator(port).toMtosiPTP();

    response.setPtp(ptp);
  }
  /**
   * Handles CTP requests.
   * @throws Exception When something goes wrong
   */
  @Override
  protected void executeGetCTP() throws Exception {
    ConnectionTerminationPointT ctp = null;
    final Port port = ManagedElementFactory.getPort(tpName);

    if(port instanceof MTOSIPortF3Acc) {
      final MTOSIFlowF3 flow = ManagedElementFactory.getCMFlow(tpName);
      if(flow==null) {
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
                MtosiErrorConstants.CTP_NOT_FOUND);
      }
      ctp = new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(flow).toMtosiCTP();
//      ctp = flow.getMtosiTranslator().toMtosiCTP();
    }

    if(ctp == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.CTP_NOT_FOUND);
    }

    response.setCtp(ctp);
  }
}
