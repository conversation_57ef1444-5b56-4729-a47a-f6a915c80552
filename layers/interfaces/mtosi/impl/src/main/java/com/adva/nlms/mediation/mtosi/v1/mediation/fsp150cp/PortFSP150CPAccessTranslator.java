/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cp;

import com.adva.nlms.common.snmp.MIBFSP150CP;
import v1.tmf854.DirectionalityT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ResourceStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.mediation.common.serviceProvisioning.ServiceSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ServiceSPPropertiesFSP150CP;
import com.adva.nlms.mediation.common.serviceProvisioning.ShelfSPProperties;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.fsp150cp_mx.BitFieldHelper;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.NetworkElementFSP150CP;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.PortFSP150CPAccess;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrativeSpeedRateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AutoNegStatusTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CPArcStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.ControlTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.DuplexModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.LoopbackStatusSwapSADATranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.NetLoopbackStatusTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.ServiceStateTranslation;

/**
 * This class is an FSP 150 CP Access Port MTOSI Translator.
 */
public class PortFSP150CPAccessTranslator extends MtosiTranslator {
  private PortFSP150CPAccess port;

  public PortFSP150CPAccessTranslator(PortFSP150CPAccess port) {
    this.port = port;
  }

  @Override
  public PhysicalTerminationPointT toMtosiPTP() throws ProcessingFailureException {
    ServiceSPPropertiesFSP150CP serviceSPPropertiesFSP150CP = port.getServicePortSPProperties();
    ObjectFactory objFactory = new ObjectFactory();
    PhysicalTerminationPointT physicalTerminationPointT = objFactory.createPhysicalTerminationPointT();

    // PTP element name
    NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributes(port);
    physicalTerminationPointT.setName(objFactory.createPhysicalTerminationPointTName(namingAttributes));

    // discoverdName
    final String ptpNm = namingAttributes.getPtpNm();
    if (ptpNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
    }
    physicalTerminationPointT.setDiscoveredName(objFactory.createPhysicalTerminationPointTDiscoveredName(ptpNm));

    // namingOS
    physicalTerminationPointT.setNamingOS(objFactory.createPhysicalTerminationPointTNamingOS(OSFactory.getNmsName()));

    // source
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    physicalTerminationPointT.setSource(objFactory.createPhysicalTerminationPointTSource(source));

    // resource state
    ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ResourceStateEnumT.INSTALLED);
    physicalTerminationPointT.setResourceState(objFactory.createPhysicalTerminationPointTResourceState(resourceState));

    // direction
    physicalTerminationPointT.setDirection(objFactory.createPhysicalTerminationPointTDirection(DirectionalityT.D_BIDIRECTIONAL));

    // edgePoint
    physicalTerminationPointT.setEdgePoint(objFactory.createPhysicalTerminationPointTEdgePoint(Boolean.TRUE));

    // layers
    LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    // -------start Layer--------
    final int mediaType = serviceSPPropertiesFSP150CP.get(ServiceSPPropertiesFSP150CP.VI.Type);
    if (mediaType == MIBFSP150CP.If.TYPE_EL_ACC) {
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL,
              LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.ACTIVE_PARAM);

      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL,
              LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.INACTIVE_PARAM);
    } else if (mediaType == MIBFSP150CP.If.TYPE_OPT_ACC) {
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL,
              LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.INACTIVE_PARAM);

      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL,
              LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.ACTIVE_PARAM);

      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_PHYSICAL_OPTICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_PHYSICAL_OPTICAL,
              LayeredParams.LrElectricalAndOptical.CONNECTOR_TYPE_PARAM, serviceSPPropertiesFSP150CP
              .get(ServiceSPPropertiesFSP150CP.VS.ConnectorType));
    }
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.ADMINISTRATION_CONTROL_PARAM,
            MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, serviceSPPropertiesFSP150CP.get(ServiceSPProperties.VI.IfAdminStatus)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.SERVICE_STATE_PARAM, ServiceStateTranslation.getMtosiString(
            serviceSPPropertiesFSP150CP.get(ServiceSPProperties.VI.IfAdminStatus), serviceSPPropertiesFSP150CP.get(ServiceSPProperties.VI.IfOperStatus)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.AUTO_NEGOTIATION_PARAM,
            MtosiUtils.getMtosiString(ControlTypeTranslation.NOT_APPLICABLE, serviceSPPropertiesFSP150CP.get(ServiceSPPropertiesFSP150CP.VI.AutoNeg)));

    int speedRate = 0;
    if (serviceSPPropertiesFSP150CP.get(ServiceSPPropertiesFSP150CP.VI.AutoNeg) == MIB.RFC1253.TRUTH_VALUE_FALSE ||
        mediaType == MIBFSP150CP.If.TYPE_OPT_ACC) {
      speedRate = serviceSPPropertiesFSP150CP.get(ServiceSPProperties.VI.PortSpeed);
    }
    else if (serviceSPPropertiesFSP150CP.get(ServiceSPPropertiesFSP150CP.VI.AutoNeg) == MIB.RFC1253.TRUTH_VALUE_TRUE) {
      speedRate = AdministrativeSpeedRateTranslation.getRateOfAdvertisedTechnologyAbility(BitFieldHelper
              .convertToTechnologyAbility(serviceSPPropertiesFSP150CP.get(ServiceSPPropertiesFSP150CP.Vb.AdvertisedTechnologyAbility)));
    }
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.ADMINISTRATIVE_SPEED_RATE_PARAM, Integer.toString(speedRate));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.ACTUAL_SPEED_RATE_PARAM, Integer
            .toString(serviceSPPropertiesFSP150CP.get(ServiceSPProperties.VI.PortNegotiatedSpeed) == -1 ? 0
            : serviceSPPropertiesFSP150CP.get(ServiceSPProperties.VI.PortNegotiatedSpeed)));

    String duplex = DuplexModeTranslation.getMtosiString(serviceSPPropertiesFSP150CP.get(ServiceSPPropertiesFSP150CP.VI.FullDuplex));
    if (mediaType == MIBFSP150CP.If.TYPE_OPT_ACC) {
      duplex = DuplexModeTranslation.getMtosiString(MIB.RFC1253.TRUTH_VALUE_TRUE);//true because on optical access port duplex is on
    }

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.DUPLEX_MODE_PARAM, duplex);

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.ACTUAL_DUPLEX_MODE_PARAM, duplex);
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_GIGABIT_ETHERNET);
    if (mediaType == MIBFSP150CP.If.TYPE_EL_ACC) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_GIGABIT_ETHERNET,
              LayeredParams.LrDsrGigabitAndFastEthernet.AUTO_NEG_REM_SIGNALLING_DETECTED_PARAM,
              MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, serviceSPPropertiesFSP150CP.get(ServiceSPPropertiesFSP150CP.VI.RemSignallingDetected)));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_GIGABIT_ETHERNET,
              LayeredParams.LrDsrGigabitAndFastEthernet.AUTO_NEG_STATUS_PARAM, AutoNegStatusTranslation
              .getMtosiString(serviceSPPropertiesFSP150CP.get(ServiceSPPropertiesFSP150CP.VI.Status)));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_GIGABIT_ETHERNET,
              LayeredParams.LrDsrGigabitAndFastEthernet.AUTO_NEG_LOCAL_TECHNOLOGY_AVAILABILITY_PARAM,
              AdministrativeSpeedRateTranslation.getAutoNegLocalTechnologyAvailability(BitFieldHelper
                      .convertToTechnologyAbility(serviceSPPropertiesFSP150CP.get(ServiceSPPropertiesFSP150CP.Vb.LocalTechnologyAbility))));

      if (BitFieldHelper.convertToTechnologyAbility(serviceSPPropertiesFSP150CP.get(ServiceSPPropertiesFSP150CP.Vb.ReceivedTechnologyAbility)).length != 0) {
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_GIGABIT_ETHERNET,
                LayeredParams.LrDsrGigabitAndFastEthernet.AUTO_NEG_REMOTE_TECHNOLOGY_AVAILABILITY_PARAM,
                Integer.toString(AdministrativeSpeedRateTranslation.getRateOfAdvertisedTechnologyAbility(BitFieldHelper.convertToTechnologyAbility(serviceSPPropertiesFSP150CP.get(ServiceSPPropertiesFSP150CP.Vb.ReceivedTechnologyAbility)))));
      }
    }
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.CONNECTIONLESS_PORT_PARAM, MtosiConstants.TRUE);

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.INTERFACE_TYPE_PARAM, "UNI");

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.PORT_TP_ROLE_STATE_PARAM, "fdEdge");// always have assigned state.

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.NUMBER_OF_TRAFFIC_CLASSES_PARAM, "1");

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.MAX_NUM_FDFRS_PARAM, "1");

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.NUM_CONFIGURED_FDFRS_PARAM, Integer.toString(serviceSPPropertiesFSP150CP
            .get(ServiceSPProperties.VI.NumConfiguredFDFrs)));
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FORWARDING_PARAM,
            MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, serviceSPPropertiesFSP150CP.get(ServiceSPPropertiesFSP150CP.VI.LinkLossFwd)));

    Integer accessLinkLossForwardME = ((NetworkElementFSP150CP) port.getNE()).getShelfFSP150CP().getShelfSPProperties().get(ShelfSPProperties.VI.LinkLossFwd);
    if (serviceSPPropertiesFSP150CP.get(ServiceSPPropertiesFSP150CP.VI.LinkLossFwd) != MIB.RFC1253.TRUTH_VALUE_FALSE || accessLinkLossForwardME==1) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
              LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FORWARDING_ACTIVE_PARAM,
              MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, serviceSPPropertiesFSP150CP.get(ServiceSPPropertiesFSP150CP.VI.LinkLossFwdActive)));
    }

    NetworkElement ne = port.getNE();
    boolean isPeer = ne.isPeer();
    if ((!isPeer && ne.getPersistenceHelper().getMIBVariantFromDB() >= MIBFSP150CP.MibVariant.VER_260) ||
            (isPeer && ne.getPeerNetworkElement() != null && ne.getPeerNetworkElement().getPersistenceHelper().getMIBVariantFromDB() >= MIBFSP150CP.MibVariant.VER_260) ) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
              LayeredParams.LrPropAdvaEthernet.LOOPBACK_STATUS_TYPE_PARAM, NetLoopbackStatusTypeTranslation
              .getMtosiString(serviceSPPropertiesFSP150CP.get(ServiceSPPropertiesFSP150CP.VI.LoopbackStatus)));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
              LayeredParams.LrPropAdvaEthernet.LOOPBACK_SWAP_SADA_PARAM,
              MtosiUtils.getMtosiString(LoopbackStatusSwapSADATranslation.NOT_APPLICABLE, serviceSPPropertiesFSP150CP.get(ServiceSPPropertiesFSP150CP.VI.DaSaSwapping)));
    }

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.ALARM_REPORT_CONTROL,
            CPArcStateTranslation.getMtosiString(serviceSPPropertiesFSP150CP.get(ServiceSPPropertiesFSP150CP.VI.ArcState )));

    
    // -------end of Layer-------

    physicalTerminationPointT.setTransmissionParams(objFactory.createPhysicalTerminationPointTTransmissionParams(layeredParametersListT));
    return physicalTerminationPointT;
  }
}
