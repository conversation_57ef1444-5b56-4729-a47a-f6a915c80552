/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

/**
 * Created by IntelliJ IDEA. User: Lukasz Date: 2007-05-30 Time: 13:14:09 To change this template use File | Settings |
 * File Templates.
 */
public enum AutoNegStatusTranslation{
  OTHER                (1, "Other"),
  CONFIGURING          (2, "Configuring"),
  COMPLETED            (3, "Completed"),
  DISABLED             (4, "Disabled"),
  PARALLEL_DETECT_FAIL (5, "ParallelDetectFail"),
  UNKNOWN              (6, "Unknown"),
  NOT_APPLICABLE       (0, "n/a");

  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private AutoNegStatusTranslation (final int mibValue, final String mtosiString)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int mibValue)
  {
    AutoNegStatusTranslation enumType = NOT_APPLICABLE;  // the return value

    for (AutoNegStatusTranslation tmpEnumType : values())
    {
      if (mibValue == tmpEnumType.getMIBValue())
      {
        enumType = tmpEnumType;
        break;
      }
    }
    return enumType.getMtosiString();
  }
  /**
   * Returns the string representation used in MTOSI layer.
   * @param mtosiString  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static int getMIBValue (final String mtosiString)
  {
    AutoNegStatusTranslation enumType = NOT_APPLICABLE;  // the return value

    for (AutoNegStatusTranslation tmpEnumType : values())
    {
      if (mtosiString.equals(tmpEnumType.getMtosiString()))
      {
        enumType = tmpEnumType;
        break;
      }
    }
    return enumType.getMIBValue();

  }
}
