/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.equipment;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.EquipmentFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import v1.tmf854.EquipmentOrHolderT;
import v1.tmf854.GetEquipmentResponseT;
import v1.tmf854.GetEquipmentT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

/**
 * main class for the MTOSI operation:  g e t E q u i p m e n t
 */
public class GetEquipmentWorker extends AbstractMtosiWorker {
  protected GetEquipmentT mtosiBody;
  protected GetEquipmentResponseT response = new GetEquipmentResponseT();
  protected NamingAttributesT naming;
  protected MtosiAddress mtosiAddr;
  protected EquipmentOrHolderT holder;

  public GetEquipmentWorker(GetEquipmentT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getEquipment", "getEquipment", "getEquipmentResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    naming = mtosiBody.getEquipmentOrHolderName();
    mtosiAddr = new MtosiAddress(naming);

    if (naming == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              MtosiErrorConstants.INVALID_FILTER);
    }

    if(naming.getMdNm()==null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if(naming.getMeNm()==null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              MtosiErrorConstants.ME_NAME_MISSING);
    }

    if (naming.getEhNm() == null && naming.getEqNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              MtosiErrorConstants.EH_AND_EQ_NAME_MISSING);
    }

    if(!MtosiUtils.existsEntity(mtosiAddr)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The specified entity does not exist.");
    }
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(mtosiAddr.getNE().getDefaultNetworkElementTypeString());
  }
  
  @Override
  protected void mediate() throws Exception {
    if ((holder = EquipmentFactory.getEquipmentOrHolder(mtosiAddr)) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The requested entity was not found.");
    }
  }

  @Override
  protected void response() throws Exception {
    response.setEquip(holder);
  }

  @Override
  public GetEquipmentResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
