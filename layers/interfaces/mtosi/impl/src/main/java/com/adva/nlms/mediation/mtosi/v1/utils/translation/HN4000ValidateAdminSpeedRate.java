/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.common.snmp.MIBHN4000;

/**
 * Valid combinations of  values for AutoNeg, DuplexMode and Speed
 */
public enum HN4000ValidateAdminSpeedRate{
	//                              PortType                                            AutoNeg       Duplex           Speed
	  HN4000GbT1     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_100BASE_T, "Enabled", "PROP_HATTERAS_AUTO", "0",false),
	  HN4000GbT2     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_100BASE_T, "Enabled", "PROP_HATTERAS_AUTO", "100",false),
	  HN4000GbT3     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_100BASE_T, "Enabled", "PROP_HATTERAS_AUTO", "1000",false),
	  HN4000GbT4     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_100BASE_T, "Enabled", "FULL", "0",false),
	  HN4000GbT5     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_100BASE_T, "Enabled", "FULL", "100",false),
	  HN4000GbT6     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_100BASE_T, "Enabled", "FULL", "1000",false),
	  HN4000GbT7     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_100BASE_T, "Disabled", "FULL", "100",false),

	  HN4000GbT10     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_1000BASE_T, "Enabled", "PROP_HATTERAS_AUTO", "0",false),
	  HN4000GbT20     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_1000BASE_T, "Enabled", "PROP_HATTERAS_AUTO", "100",false),
	  HN4000GbT30     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_1000BASE_T, "Enabled", "PROP_HATTERAS_AUTO", "1000",false),
	  HN4000GbT40     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_1000BASE_T, "Enabled", "FULL", "0",false),
	  HN4000GbT50     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_1000BASE_T, "Enabled", "FULL", "100",false),
	  HN4000GbT60     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_1000BASE_T, "Enabled", "FULL", "1000",false),
	  HN4000GbT70     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_1000BASE_T, "Disabled", "FULL", "100",false),

	  HN4000GbX1     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_1000BASE_FX, "Enabled", "PROP_HATTERAS_AUTO", "0",false),
	  HN4000GbX2     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_1000BASE_FX, "Enabled", "PROP_HATTERAS_AUTO", "1000",false),
	  HN4000GbX4     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_1000BASE_FX, "Enabled", "FULL", "0",false),
	  HN4000GbX5     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_1000BASE_FX, "Enabled", "FULL", "1000",false),
	  HN4000GbX6     (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_1000BASE_FX, "Disabled", "FULL", "1000",false),
	  HN400_2 (0, "Enabled", "PROP_HATTERAS_Auto", "0", true),
	  HN400_3 (0, "Enabled", "PROP_HATTERAS_Auto", "10", true),
	  HN400_4 (0, "Enabled", "PROP_HATTERAS_Auto", "100", true),
	  HN400_5 (0, "Enabled", "Full", "0", true),
	  HN400_6 (0, "Enabled", "Full", "10", true),
	  HN400_7 (0, "Enabled", "Full", "100", true),
	  HN400_8 (0, "Enabled", "Half", "0", true),
	  HN400_9 (0, "Enabled", "Half", "10", true),
	  HN400_10 (0, "Enabled", "Half", "100", true),
	  HN400_11 (0, "Disabled", "Full", "10", true),
	  HN400_12 (0, "Disabled", "Full", "100", true),
	  HN400_13 (0, "Disabled", "Half", "10", true),
	  HN400_14 (0, "Disabled", "Half", "100", true);
  //--------------------------------------------------------------------------
	// ----------------------------------------
	private final int portType;
	private final String autoNeg;
	private final String duplexMode;
	private final String speed;
	private final boolean hn400;

	//--------------------------------------------------------------------------
	// ----------------------------------------
	private HN4000ValidateAdminSpeedRate(int portType, String autoNeg, String duplexMode, String speed, boolean hn400) {
		this.autoNeg = autoNeg;
		this.duplexMode = duplexMode;
		this.portType = portType;
		this.speed = speed;
		this.hn400 = hn400;
	}

	public static boolean isValid(int portType, String autoNeg, String duplexMode, String speed, boolean hn400) {
		for (HN4000ValidateAdminSpeedRate value : values()) {
			if (value.portType == portType 
					&& value.autoNeg.equals(autoNeg) 
					&& value.duplexMode.equals(duplexMode) 
					&& value.speed.equals(speed)
					&& value.hn400 == hn400) {
				return true;
			}
		}
		return false;
	}
}
