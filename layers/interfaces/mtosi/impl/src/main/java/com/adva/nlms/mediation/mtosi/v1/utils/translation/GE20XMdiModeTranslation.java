/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum GE20XMdiModeTranslation implements TranslatableEnum {
    NOT_APPLICABLE 	(1, "NotApplicable"),
    auto_mdix 		(2, "Auto"),
    crossed   		(3, "Crossover"),
    uncrossed 		(4, "StraightThrough");

  private final int    mibValue;
  private final String mtosiString;

  private GE20XMdiModeTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}