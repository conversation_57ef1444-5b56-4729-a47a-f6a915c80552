/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.pm;

import java.util.Iterator;
import java.util.List;

import jakarta.xml.ws.Holder;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.GetAllHistoricalPMDataResponseT;
import v1.tmf854ext.adva.GetAllHistoricalPMDataT;
import v1.tmf854ext.adva.PMDataListT;
import v1.tmf854ext.adva.PMParameterNameListT;
import v1.tmf854ext.adva.PMTPSelectListT;
import v1.tmf854ext.adva.PMTPSelectT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.PMFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;


public class GetAllHistoricalPMDataWorker extends AbstractMtosiWorker {
  protected GetAllHistoricalPMDataT mtosiBody;
  protected GetAllHistoricalPMDataResponseT response = new GetAllHistoricalPMDataResponseT();
  protected PMTPSelectListT entityList = null;
  protected String endTime = null;
  protected String startTime = null;
  protected PMParameterNameListT parameterList = null;
  protected PMDataListT pmDataList;

  public GetAllHistoricalPMDataWorker(GetAllHistoricalPMDataT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getAllHistoricalPMData", "getAllHistoricalPMData", "getAllHistoricalPMDataResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    if ((entityList = mtosiBody.getPmTPSelectList()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The pmTPSelectList has not been specified.");
    }
    if ((startTime = mtosiBody.getStartTime()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The startTime has not been specified.");
    }
    if ((endTime = mtosiBody.getEndTime()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The endTime has not been specified.");
    }

    parameterList = mtosiBody.getPmParameterNameList();
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
		List<PMTPSelectT> list = entityList.getPmtpsel();
		for (Iterator<PMTPSelectT> iter = list.iterator(); iter.hasNext();) {
			PMTPSelectT element = iter.next();
			NamingAttributesT naming = element.getName();
			NetworkElement ne = ManagedElementFactory.getAndValidateNE(naming);
			validator.validate(ne.getDefaultNetworkElementTypeString());
		}

  }

  @Override
  protected void mediate() throws Exception {
    pmDataList = PMFactory.getHistoricalPMDataList(startTime, endTime, entityList, parameterList);
  }

  @Override
  protected void response() throws Exception {
    response.setPmDataList(pmDataList);
  }

  @Override
  public GetAllHistoricalPMDataResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}