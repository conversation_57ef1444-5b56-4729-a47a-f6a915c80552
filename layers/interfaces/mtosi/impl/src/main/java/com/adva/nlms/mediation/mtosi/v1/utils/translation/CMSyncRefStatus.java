/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum CMSyncRefStatus implements TranslatableEnum {
  NOT_APPLICABLE  (0, "NotAvailable"),
  OK              (1, "OK"),
  FAILED          (2, "Failed");

  private final int    mibValue;
  private final String mtosiString;

  private CMSyncRefStatus(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}
