/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;

/**
  *
  */
public enum HNSTPAdminControlTranslation {
	Enabled   	    (1),
	Disabled 		(2),
	PROP_HATTERAS_Testing			(3);
    
    private int mibValue;
    
    private int getMibValue() {
		return mibValue;
	}

	private HNSTPAdminControlTranslation(int code) {
    	this.mibValue = code;
    }

    public static String getMtosiString(final Integer mibValue) {
    	if (mibValue == null) {
    		return MtosiConstants.NOT_APPLICABLE;
    	}
    	for (HNSTPAdminControlTranslation value: values() ) {
    		if (value.getMibValue() == mibValue.intValue()) {
    			return value.name(); 
    		}
    	}
    	return String.valueOf(mibValue);
    }
    
    public static int getMibValue(final String name) {
    	for (HNSTPAdminControlTranslation value: values() ) {
    		if (value.name().equals(name)) {
    			return value.getMibValue(); 
    		}
    	}
    	return -1;
    }
}
