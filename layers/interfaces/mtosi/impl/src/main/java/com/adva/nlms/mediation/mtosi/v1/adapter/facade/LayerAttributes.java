/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.adapter.facade;

import java.util.HashMap;
import java.util.Map;

public enum LayerAttributes {
  NONE("","",Object.class),

  ALLOCATED_NUMBER("AllocatedNumber","LR_LAG",Integer.class),
  FRAGMENT_SERVER_LAYER("FragmentServerLayer","LR_LAG",String.class),
  REVERTIVE("Revertive","PROP_ADVA_ProtectionFSP1501Plus1" ,String.class),
  PROTECTIONSWITCHMODE("ProtectionSwitchMode", "PROP_ADVA_ProtectionFSP1501Plus1",String.class ) ,
  WAITTORESTORE("WaitToRestore", "PROP_ADVA_ProtectionFSP1501Plus1",String.class ) ,
  PROTECTIONSWITCHMODE_CM("ProtectionSwitchMode","PROP_ADVA_ProtectionFSP150CMNTU",String.class),
  REVERTIVE_CM("Revertive","PROP_ADVA_ProtectionFSP150CMNTU",String.class),
  SWITCHDIRECTION_CM("SwitchDirection","PROP_ADVA_ProtectionFSP150CMNTU",String.class),

  //MD
  MDLEVEL("MdLevel","PROP_ADVA_CFMCCM",Integer.class),
  MIPCREATIONCCONTROL("MdMipCreationControl","PROP_ADVA_CFMCCM",String.class),

  //MANET
  MACCMINTERVAL("MaCcmInterval","PROP_ADVA_CFMCCM",String.class),
  MANUMBEROFVIDS("MaNumberOfVids","PROP_ADVA_CFMCCM",Integer.class),
  MAMEPLIST("MaMepList","PROP_ADVA_CFMCCM",String.class),

  //MACOMP
  MACOMPMHFCREATION("MaMipCreationControl","PROP_ADVA_CFMCCM",String.class),
  MAPRIMARYVID("MaPrimaryVid","PROP_ADVA_CFMCCM",String.class),

  //MEP
  MEPDIRECTION("MepDirection","PROP_ADVA_CFMCCM",String.class),
  MEPPRIMARYVID("MepPrimaryVid","PROP_ADVA_CFMCCM",Integer.class),
  MEPACTIVE("MepActive","PROP_ADVA_CFMCCM",Integer.class),
  MEPCCIENABLED("MepCcmGeneration","PROP_ADVA_CFMCCM",String.class),
  MEPVLANPRIORITY("MepVlanPriority","PROP_ADVA_CFMCCM",Integer.class),
  MEPLOWESTPRIORITYDEFECT("MepLowestPriorityDefect","PROP_ADVA_CFMCCM",String.class),
  MEPADMINISTRATIONSTATE("MepAdministrationControl","PROP_ADVA_CFMCCM",String.class),

  MEPHIGHESTPRIORITYDEFECT("MepHighestPriorityDefect","PROP_ADVA_CFMCCM",String.class),
  LLFTRIGGER("MepLlfTrigger","PROP_ADVA_CFMCCM",String.class),
  AISGENERATION("MepAisGeneration","PROP_ADVA_CFMCCM",String.class),
  AISTRANSMISSIONINTERVAL("MepAisTransmissionInterval","PROP_ADVA_CFMCCM",String.class),
  AISPRIORITY("MepAisPriority","PROP_ADVA_CFMCCM",String.class),
  AISCLIENTMDLEVEL("MepAisClientMdLevel","PROP_ADVA_CFMCCM",String.class),
  AISTRIGGER("MepAisTrigger","PROP_ADVA_CFMCCM",String.class);


  private final Class _paramType;

  private String _attributeName;
  private String _layerName;
  private static final Map<String, LayerAttributes> stringMap;

  private LayerAttributes(String attributeName, String layerName, Class paramType){
    this._attributeName=attributeName;
    this._layerName=layerName;
    this._paramType=paramType;
  }


   static {
     stringMap = new HashMap<>();
     for (LayerAttributes v : values()) {
       stringMap.put(v.toString(), v);
       stringMap.put(String.valueOf(v._layerName+"."+v._attributeName), v);
     }
   }

  public static LayerAttributes valueOfString(String layerName, String attributeName) {
    return stringMap.get(layerName + "." + attributeName);
  }

  public static LayerAttributes valueOfString(String fieldFQN) {
    return stringMap.get(fieldFQN);
  }

  public String getMappedValue() {
    return this._layerName + "." + this._attributeName;
  }

  public Class get_paramType() {
    return _paramType;
  }
}
