/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum GE20XSfpMediaTypeTranslation implements TranslatableEnum, com.adva.nlms.mediation.mtosi.v2.utils.translations.f3.TranslatableEnum {
    NOT_APPLICABLE	(0, "Unknown"),
    singlemode		(1, "SingleMode"),
    multimode		(2, "Multimode"),
    multimode62_5	(3, "Multimode62-5"),
    copper			(4, "Copper"); 

  private final int    mibValue;
  private final String mtosiString;

  private GE20XSfpMediaTypeTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}