/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils;

import com.adva.nlms.common.performance.VALUE_NOT_AVAILABLE;
import com.adva.nlms.mediation.performance.data.PMMeasurement;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

public class PMUtils
{

	private static SimpleDateFormat getsdfZ() {
		return new SimpleDateFormat("yyyyMMddHHmmss.sZ");
	}

	private static SimpleDateFormat getsdfGMT() {
		return new SimpleDateFormat("yyyyMMddHHmmss.s");
	}

	private static SimpleDateFormat getsdfLocal() {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss.s");
		simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
		return simpleDateFormat;
	}

	Logger LOG = LogManager.getLogger(this.getClass().getName());

	public static final String INTERVAL_VALID = "Valid";
	public static final String INTERVAL_UNAVAILABLE = "Unavailable";

	public static Date dateFromPMTime(String time)
	{
		if (time.indexOf('Z') > 0)
		{
			time = time.replaceAll("Z", "");
			return dateFromPMTimeGMT(time);
		}
		else
		{
			if (time.indexOf('+') > -1 || time.indexOf('-') > -1)
			{
				return dateFromPMTimeZ(time);
			}
			return dateFromPMTimePlain(time);
		}
	}

	private static Date dateFromPMTimeGMT(String time)
	{
		try
		{
			return getsdfGMT().parse(time);
		}
		catch (Exception e)
		{
			return null;
		}
	}

	private static Date dateFromPMTimePlain(String time)
	{
		try
		{
			return getsdfLocal().parse(time);
		}
		catch (Exception e)
		{
			return null;
		}
	}

	private static Date dateFromPMTimeZ(String time)
	{
		try
		{
			return getsdfZ().parse(time);
		}
		catch (Exception e)
		{
			return null;
		}
	}

	public static String stringFromDate(Date date)
	{
		return getsdfZ().format(date);
	}

	public static void main(String args[]) throws Exception
	{

		PMUtils utils = new PMUtils();
		utils.test();

	}

	public void test()
	{
		try
		{

			Date date = PMUtils.dateFromPMTime("20080204000000.0-0500");
			String dateString = getsdfZ().format(date);
			LOG.debug("Time is: " + date.toString());
			

		}
		catch (Exception ex)
		{
			//String message = ex.toString();
		}
	}

	public static String intervalStatusForMeasurement(PMMeasurement measurement)
	{
		// ToDo: Need to find list of values to translate
		long checkValue = Double.valueOf(measurement.getValue()).longValue();
		if (checkValue == VALUE_NOT_AVAILABLE.value)
		{
			return INTERVAL_UNAVAILABLE;
		}
		else
		{
			return INTERVAL_VALID;
		}
	}

}
