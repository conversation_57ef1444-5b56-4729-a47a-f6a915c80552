/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.equipment;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress.NamingField;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.EqVendorExtensionsT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.EQDataT;
import v1.tmf854ext.adva.SetEquipmentDataResponseT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

/**
 * main class for the MTOSI operation:  s e t E q u i p m e n t D a t a
 */
public abstract class SetEquipmentDataWorker extends AbstractMtosiWorker {
	Logger LOG = LogManager.getLogger(this.getClass().getName());

  protected EQDataT equipmentData;
  protected NamingAttributesT equipmentName;
  protected MtosiAddress mtosiAddr;
  protected NetworkElement ne;
  protected EqVendorExtensionsT vendorExtensions;
  protected SetEquipmentDataResponseT response = new SetEquipmentDataResponseT();

  public SetEquipmentDataWorker(Holder<HeaderT> mtosiHeader, EQDataT equipmentData, NamingAttributesT equipmentName, NetworkElement ne) {
    super(mtosiHeader, "setEquipmentData", "setEquipmentData", "setEquipmentDataResponse");
    this.equipmentData = equipmentData;
    this.equipmentName = equipmentName;
    this.mtosiAddr = new MtosiAddress(equipmentName);
    this.ne = ne;
  }

  @Override
  protected void parse() throws Exception {
    // validate mandatory address-fields: MD, ME, EH, EQ
    mtosiAddr.validateAddressFields(NamingField.MD, NamingField.ME, NamingField.EH, NamingField.EQ);

    vendorExtensions = MtosiUtils.checkElement(equipmentData.getVendorExtensions(), "vendorExtensions").getValue();
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
   validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  @Override
  public SetEquipmentDataResponseT getSuccessResponse()
	{
		if (response == null)
			return null;
		response.setTmf854Version(MtosiConstants.VERSION);
		return response;
	}
}
