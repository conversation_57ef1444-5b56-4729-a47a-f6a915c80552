/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * Simple Timer class to time operations. 
 * Usage: Timer t = new Timer(<Class to Log against>, "Timer reason");
 	t.start(0..4);
 	<wrap code in timers>
 	t.stop(0..4);
 	t.report(); - report the times.
 * <AUTHOR>
 *
 */

public class Timer {

    private static Timer instance = null;

    public static Timer getInstance() {
        if (instance == null)
            instance = new Timer(null, "Static");
        return instance;
    }
    public static String startI(int i) {

        getInstance().start(i);
        return "";
    }
    public static String stopI(int i) {
        getInstance().stop(i);
        return "";
    }
    public static String reportI() {
        return getInstance().reportIt();
    }

    public Logger logger = null;
    public static final int MAX_TIMERS = 5;
    public Object source = null;
    public long[] startTimes = new long[MAX_TIMERS];
    public long[] stopTimes = new long[MAX_TIMERS];
    public String[] ids = new String[MAX_TIMERS];
    public String title = "Timer Report";

    public Timer(String t) {
        this(null, t);
    }
    /**
     * For Logging to sysout.
     * @param t
     * @param sysout
     */
    public Timer(String t,boolean sysout) {
        this(null, t);
        if (sysout)
            logger = null;
    }

    public Timer(Object source, String t) {
        title = t;
        this.source = source;
        //This way, the logger for the timer is base on the Class
        //that the timer is set for and not the Timer class...
        // 6 and half dozen, which way is best...
        if (source != null) {
            logger = LogManager.getLogger(source.getClass());
        } else {
            logger = LogManager.getLogger(this.getClass());
        }
        startTimes = new long[MAX_TIMERS];
        stopTimes = new long[MAX_TIMERS];
        ids = new String[MAX_TIMERS];
    }

    public void start(int i) {
        //Trust the programmer...
        startTimes[i] = System.currentTimeMillis();
    }
    public void start(int i, String timerId) {
        //Trust the programmer...
        startTimes[i] = System.currentTimeMillis();
        ids[i] = timerId;
    }
    public void stop(int i) {
        //Trust the programmer...
        stopTimes[i] = System.currentTimeMillis();
    }
    /**
     * Sometimes you just want to report on a single timer.
     * @param i
     * @param msg
     */
    public void report(int i, String msg) {
        //Trust the programmer...
        long delta = stopTimes[i] - startTimes[i];
        System.out.println("!!!!!!!!!: " + delta + " is the millisecond time for " + msg);
    }
    public void report() {
    	StringBuilder output = new StringBuilder("\n!!!!!!!!!! ").append(title).append("!!!!!!!\n");
        for (int i = 0; i < MAX_TIMERS; i++) {
            if (stopTimes[i] > 0 && startTimes[i] > 0) {
                long delta = stopTimes[i] - startTimes[i];
                output.append("!!!!!!!!!!: ").append(delta).append(" ms for ");
                if (ids[i] == null)
                    output.append("timer ").append(i).append("\n");
                else
                    output.append(ids[i]).append("\n");
            }
        }
        if (logger != null)
            logger.debug(output);
        else
        	System.out.println(output.toString());
    }
    public String reportIt() {
    	StringBuilder output = new StringBuilder("\n!!!!!!!!!! ").append(title).append("!!!!!!!\n");
        for (int i = 0; i < MAX_TIMERS; i++) {
            if (stopTimes[i] > 0 && startTimes[i] > 0) {
                long delta = stopTimes[i] - startTimes[i];
                output.append("!!!!!!!!!!: ").append(delta).append(" ms for ");
                if (ids[i] == null)
                    output.append("timer ").append(i).append("\n");
                else
                    output.append(ids[i]).append("\n");
            }
        }
        return output.toString();
    }
}
