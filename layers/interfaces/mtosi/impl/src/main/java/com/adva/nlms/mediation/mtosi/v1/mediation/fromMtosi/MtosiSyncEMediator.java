/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi;

import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.F3SyncRefSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.F3SyncSPProperties;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.f3.entity.port.MTOSIPortF3;
import com.adva.nlms.mediation.config.f3.entity.sync.F3Sync;
import com.adva.nlms.mediation.config.f3.entity.sync.F3SyncImpl;
import com.adva.nlms.mediation.config.f3.entity.syncref.F3SyncRefImpl;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMNetworkClockTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSyncSelectionModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.F3SyncDomainTypeTranslation;

import org.w3c.dom.Node;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.TPDataListT;
import v1.tmf854.TPDataT;
import v1.tmf854.TPVendorExtensionsT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class MtosiSyncEMediator {

  public static F3SyncSPProperties mtosiTDFrModifyDataTToF3SyncProperties(F3SyncImpl f3Sync, LayeredParametersListT paramaterList)
    throws ProcessingFailureException
  {
    // get AdministrationControl
    String adminCtrl = LayeredParameterUtils.getLayeredParameter(paramaterList,
                                LayeredParams.PROP_ADVA_TIMING_DOMAIN,
                                LayeredParams.PropAdvaTimingDomain.ADMINISTRATION_CONTROL);

    // get NetworkClockType
    String netClockType = LayeredParameterUtils.getLayeredParameter(paramaterList,
                                LayeredParams.PROP_ADVA_TIMING_DOMAIN,
                                LayeredParams.PropAdvaTimingDomain.NETWORK_CLOCK_TYPE);

    // get SyncDomainType
    String syncDomainType = LayeredParameterUtils.getLayeredParameter(paramaterList,
                                LayeredParams.PROP_ADVA_TIMING_DOMAIN,
                                LayeredParams.PropAdvaTimingDomain.SYNC_DOMAIN_TYPE);

    // get SyncSelectionMode
    String syncSelectionMode = LayeredParameterUtils.getLayeredParameter(paramaterList,
                                LayeredParams.PROP_ADVA_TIMING_DOMAIN,
                                LayeredParams.PropAdvaTimingDomain.SYNC_SELECTION_MODE);

    // get SyncWTRTime
    String syncWTRTime = LayeredParameterUtils.getLayeredParameter(paramaterList,
                                LayeredParams.PROP_ADVA_TIMING_DOMAIN,
                                LayeredParams.PropAdvaTimingDomain.SYNC_WTR_TIME);

    if (adminCtrl == null && netClockType == null && syncDomainType == null && syncSelectionMode ==null && syncWTRTime == null)
      return null;

    F3SyncSPProperties f3SyncProps = f3Sync.getF3SyncSPProperties();
    F3SyncSPProperties f3SyncPropsToModify = new F3SyncSPProperties(
                                                      f3SyncProps.get(F3SyncSPProperties.VI.NeIndex),
                                                      f3SyncProps.get(F3SyncSPProperties.VI.ShelfIndex),
                                                      f3SyncProps.get(F3SyncSPProperties.VI.SlotIndex),
                                                      f3SyncProps.get(F3SyncSPProperties.VI.F3SyncIndex),
                                                      f3SyncProps.get(F3SyncSPProperties.VS.Name),
                                                      f3SyncProps.get(F3SyncSPProperties.VS.MTOSIName));

    if (adminCtrl != null) {
      Integer adminControlInt = MtosiUtils.getMIBValue(CMAdministrationControlTranslation.NOT_APPLICABLE, adminCtrl);
      if (adminControlInt != CMAdministrationControlTranslation.NOT_APPLICABLE.getMIBValue()) {
        f3SyncPropsToModify.set(F3SyncSPProperties.VI.AdminState, adminControlInt );
      }
      else {
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          "The parameter AdministrationControl is wrong.");
      }
    }
    if (netClockType != null) {
      Integer netClockTypeInt = MtosiUtils.getMIBValue(CMNetworkClockTypeTranslation.NOT_APPLICABLE, netClockType);
      if (netClockTypeInt != CMNetworkClockTypeTranslation.NOT_APPLICABLE.getMIBValue()) {
        f3SyncPropsToModify.set(F3SyncSPProperties.VI.NetworkClockType, netClockTypeInt );
      }
      else {
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          "The parameter NetworkClockType is wrong.");
      }
    }
    if (syncDomainType != null) {
      // GE201SE only supports Chassis.
      //SCU-T card only supports Chassis
      //NTE card in CM 4.1 only support Chassis.
      //This code will need to be enhanced to handle Linecard, when a Mib version handles it... TODO some day.
      int syncDomainTypeInt = MtosiUtils.getMIBValue(F3SyncDomainTypeTranslation.NOT_APPLICABLE,syncDomainType);
      if (syncDomainTypeInt != F3SyncDomainTypeTranslation.Chassis.getMIBValue()) {
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.PropAdvaTimingDomain.SYNC_DOMAIN_TYPE));
      }
    }

    if (syncSelectionMode != null) {
      Integer syncSelectionModeInt = MtosiUtils.getMIBValue(CMSyncSelectionModeTranslation.NOT_APPLICABLE, syncSelectionMode);
      if (syncSelectionModeInt != CMSyncSelectionModeTranslation.NOT_APPLICABLE.getMIBValue()) {
        f3SyncPropsToModify.set(F3SyncSPProperties.VI.SyncSelectionMode, syncSelectionModeInt );
      }
      else {
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.PropAdvaTimingDomain.SYNC_SELECTION_MODE));
      }
    }
    
    if (syncWTRTime != null) {
      Integer syncWTRTimeInt = null;
      try {
        syncWTRTimeInt = Integer.valueOf(syncWTRTime);
      } catch (NumberFormatException n) {
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.PropAdvaTimingDomain.SYNC_WTR_TIME), n);
      }
      if (syncWTRTimeInt < 0  || syncWTRTimeInt > 12) {
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.PropAdvaTimingDomain.SYNC_WTR_TIME));
      }
      f3SyncPropsToModify.set(F3SyncSPProperties.VI.SyncWTRTime, syncWTRTimeInt );
    }
    return f3SyncPropsToModify;
  }

  public static List<F3SyncRefSPProperties> mtosiTpsToAddListTToF3SyncRefPropertyList(F3SyncImpl f3Sync, TPVendorExtensionsT extensions)
      throws ProcessingFailureException
  {
    List<F3SyncRefSPProperties> tpsToAddListProps = new ArrayList<F3SyncRefSPProperties>();

    Set<Integer> refIndices = new HashSet<Integer>();
    for (F3SyncRefImpl syncRef : f3Sync.getAllF3SyncRefs()) {
      refIndices.add(syncRef.getF3SyncRefProperties().get(F3SyncRefSPProperties.VI.RefIndex));
    }

		List list = extensions.getAny();
    Unmarshaller unmarshaller = null;

    try
    {
      unmarshaller = JAXBContext.newInstance("v1.tmf854").createUnmarshaller();
    }
    catch (JAXBException ex)
    {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.JAXB_MARSHALLING_PROBLEM, ex);
    }

    for (Iterator iter = list.iterator(); iter.hasNext();)
    {
      Node element = (Node) iter.next();
      TPDataListT tpDataList;
      try
      {
        JAXBElement<TPDataListT> root = unmarshaller.unmarshal(element, TPDataListT.class);
        tpDataList = root.getValue();

        for (TPDataT tpData : tpDataList.getTpData()) {
          NamingAttributesT tpName = tpData.getTpName();

          // check CTP name
          int refIndex = MtosiUtils.getRegexGroupInt(tpName.getCtpNm(),
                                                     "^"+ MtosiConstants.TDFR_REFERENCE_TEXT+"(\\d+)$",
                                                     1, MtosiConstants.TDFR_SYNC_REFS_MAX,
                                                     MtosiErrorConstants.CTP_NAME_MISSING);

          if (refIndices.contains(refIndex)) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.TDFR_REFERENCE_EXISTS);
          }

          LayeredParametersListT transmissionParams = tpData.getTransmissionParams().getValue();
          // get SyncReference
          String syncRef = LayeredParameterUtils.getLayeredParameter(transmissionParams,
                                      LayeredParams.PROP_ADVA_SYNC_REFERENCE,
                                      LayeredParams.PropAdvaSyncReference.SYNC_REFERENCE);

          // get Alias
          String alias = LayeredParameterUtils.getLayeredParameter(transmissionParams,
                                      LayeredParams.PROP_ADVA_SYNC_REFERENCE,
                                      LayeredParams.PropAdvaSyncReference.ALIAS);

          // get RefPriority
          String refPriority = LayeredParameterUtils.getLayeredParameter(transmissionParams,
                                      LayeredParams.PROP_ADVA_SYNC_REFERENCE,
                                      LayeredParams.PropAdvaSyncReference.REF_PRIORITY);

          Port port = ManagedElementFactory.getPort(f3Sync.getNetworkElement(), syncRef);
          if (port == null) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "SyncReference wrong.");
          }

          F3SyncSPProperties syncProps = f3Sync.getF3SyncSPProperties();

          F3SyncRefSPProperties props = new F3SyncRefSPProperties(syncProps.get(F3SyncSPProperties.VI.NeIndex),      /*neIndex*/
                                                                  syncProps.get(F3SyncSPProperties.VI.ShelfIndex),   /*shelfIndex*/
                                                                  syncProps.get(F3SyncSPProperties.VI.SlotIndex),    /*slotIndex*/
                                                                  syncProps.get(F3SyncSPProperties.VI.F3SyncIndex),  /*syncIndex*/
                                                                  refIndex);                    /*refIndex*/

          props.set(F3SyncRefSPProperties.VS.SyncReference, ((MTOSIPortF3)port).getObjectOID());
          props.set(F3SyncRefSPProperties.VI.RowStatus, MIBFSP150CM.Const.ROW_STATUS_CREATE_AND_GO);

          if (alias != null) {
            props.set(F3SyncRefSPProperties.VS.Alias, alias);
          }
          if (refPriority != null) {
            int refPriorityInt = Integer.parseInt(refPriority);
            props.set(F3SyncRefSPProperties.VI.Priority, refPriorityInt);
          }

          tpsToAddListProps.add(props);
        }

      }
      catch (JAXBException ex)
      {
        // not right element so skip it
      }
    }

    return tpsToAddListProps;
  }

  public static Map<F3SyncRefImpl, F3SyncRefSPProperties> mtosiTpsToModifyListTToF3SyncRefPropertyList(F3Sync f3Sync, TPVendorExtensionsT extensions)
      throws ProcessingFailureException
  {
    Map<F3SyncRefImpl, F3SyncRefSPProperties> tpsToModifyMap = new HashMap<F3SyncRefImpl, F3SyncRefSPProperties>();

    Map<Integer, F3SyncRefImpl> f3SyncRefMap = new HashMap<Integer, F3SyncRefImpl>();
    for (F3SyncRefImpl f3SyncRef : f3Sync.getAllF3SyncRefs()) {
      f3SyncRefMap.put(f3SyncRef.getF3SyncRefProperties().get(F3SyncRefSPProperties.VI.RefIndex), f3SyncRef);
    }

		List list = extensions.getAny();
    Unmarshaller unmarshaller = null;

    try
    {
      unmarshaller = JAXBContext.newInstance("v1.tmf854").createUnmarshaller();
    }
    catch (JAXBException ex)
    {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.JAXB_MARSHALLING_PROBLEM, ex);
    }

    for (Iterator iter = list.iterator(); iter.hasNext();)
    {
      Node element = (Node) iter.next();
      TPDataListT tpDataList;
      try
      {
        JAXBElement<TPDataListT> root = unmarshaller.unmarshal(element, TPDataListT.class);
        tpDataList = root.getValue();

        for (TPDataT tpData : tpDataList.getTpData()) {
          NamingAttributesT tpName = tpData.getTpName();

          // check CTP name
          int refIndex = MtosiUtils.getRegexGroupInt(tpName.getCtpNm(),
                                                     "^"+ MtosiConstants.TDFR_REFERENCE_TEXT+"(\\d+)$",
                                                     1, MtosiConstants.TDFR_SYNC_REFS_MAX,
                                                     MtosiErrorConstants.CTP_NAME_MISSING);

          if (!f3SyncRefMap.containsKey(refIndex)) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.TDFR_REFERENCE_NOT_EXIST);
          }

          LayeredParametersListT transmissionParams = tpData.getTransmissionParams().getValue();
          // get Alias
          String alias = LayeredParameterUtils.getLayeredParameter(transmissionParams,
                                      LayeredParams.PROP_ADVA_SYNC_REFERENCE,
                                      LayeredParams.PropAdvaSyncReference.ALIAS);

          // get RefPriority
          String refPriority = LayeredParameterUtils.getLayeredParameter(transmissionParams,
                                      LayeredParams.PROP_ADVA_SYNC_REFERENCE,
                                      LayeredParams.PropAdvaSyncReference.REF_PRIORITY);

          F3SyncRefImpl f3SyncRef = f3SyncRefMap.get(refIndex);
          F3SyncRefSPProperties f3SyncRefProps = f3SyncRef.getF3SyncRefProperties();
          F3SyncRefSPProperties newF3SyncRefProps = new F3SyncRefSPProperties(
                                      f3SyncRefProps.get(F3SyncRefSPProperties.VI.NeIndex),
                                      f3SyncRefProps.get(F3SyncRefSPProperties.VI.ShelfIndex),
                                      f3SyncRefProps.get(F3SyncRefSPProperties.VI.SlotIndex),
                                      f3SyncRefProps.get(F3SyncRefSPProperties.VI.SyncIndex),
                                      f3SyncRefProps.get(F3SyncRefSPProperties.VI.RefIndex));

          if (alias != null) {
            newF3SyncRefProps.set(F3SyncRefSPProperties.VS.Alias, alias);
          }
          if (refPriority != null) {
            int refPriorityInt = Integer.parseInt(refPriority);
            newF3SyncRefProps.set(F3SyncRefSPProperties.VI.Priority, refPriorityInt);
          }

          tpsToModifyMap.put(f3SyncRef, newF3SyncRefProps);
        }

      }
      catch (JAXBException ex)
      {
        // not right element so skip it
      }
    }

    return tpsToModifyMap;
  }

  public static List<F3SyncRefSPProperties> mtosiTpsToRemoveListTToF3SyncRefPropertyList(F3Sync f3Sync, TPVendorExtensionsT extensions)
      throws ProcessingFailureException
  {
    List<F3SyncRefSPProperties> tpsToRemoveListProps = new ArrayList<F3SyncRefSPProperties>();

    Map<Integer, F3SyncRefSPProperties> f3SyncRefMap = new HashMap<Integer, F3SyncRefSPProperties>();
    for (F3SyncRefImpl syncRef : f3Sync.getAllF3SyncRefs()) {
      F3SyncRefSPProperties f3SyncRefPorps = syncRef.getF3SyncRefProperties();
      f3SyncRefMap.put(f3SyncRefPorps.get(F3SyncRefSPProperties.VI.RefIndex), f3SyncRefPorps);
    }

		List list = extensions.getAny();
    Unmarshaller unmarshaller = null;

    try
    {
      unmarshaller = JAXBContext.newInstance("v1.tmf854").createUnmarshaller();
    }
    catch (JAXBException ex)
    {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.JAXB_MARSHALLING_PROBLEM, ex);
    }

    for (Iterator iter = list.iterator(); iter.hasNext();)
    {
      Node element = (Node) iter.next();
      NamingAttributesListT tpNameList;
      try
      {
        JAXBElement<NamingAttributesListT> root = unmarshaller.unmarshal(element, NamingAttributesListT.class);
        tpNameList = root.getValue();

        for (NamingAttributesT tpName : tpNameList.getName()) {
          // check CTP name
          int refIndex = MtosiUtils.getRegexGroupInt(tpName.getCtpNm(),
                                                     "^"+ MtosiConstants.TDFR_REFERENCE_TEXT+"(\\d+)$",
                                                     1, MtosiConstants.TDFR_SYNC_REFS_MAX,
                                                     MtosiErrorConstants.CTP_NAME_MISSING);

          if (!f3SyncRefMap.containsKey(refIndex)) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
                MtosiErrorConstants.TDFR_REFERENCE_NOT_EXIST);
          }

          F3SyncRefSPProperties f3SyncRefProps = f3SyncRefMap.get(refIndex);

          f3SyncRefProps.set(F3SyncRefSPProperties.VI.RowStatus, MIBFSP150CM.Const.ROW_STATUS_DESTROY);
          tpsToRemoveListProps.add(f3SyncRefProps);
        }

      }
      catch (JAXBException ex)
      {
        // not right element so skip it
      }
    }

    return tpsToRemoveListProps;
  }
}
