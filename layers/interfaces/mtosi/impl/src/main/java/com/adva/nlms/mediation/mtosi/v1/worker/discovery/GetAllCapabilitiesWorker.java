/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.discovery;

import jakarta.xml.ws.Holder;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import v1.tmf854.CapabilitiesListT;
import v1.tmf854.GetAllCapabilitiesResponseT;
import v1.tmf854.GetAllCapabilitiesT;
import v1.tmf854.HeaderT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.mtosi.v1.factory.CapabilitiesFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;

/**
 * Created by IntelliJ IDEA.
 * User: gfilip
 * Date: 2007-01-11
 * Time: 15:10:32
 */
public class GetAllCapabilitiesWorker extends AbstractMtosiWorker {
  protected GetAllCapabilitiesResponseT response = new GetAllCapabilitiesResponseT();
  protected CapabilitiesListT capabilitiesList;

  public GetAllCapabilitiesWorker(GetAllCapabilitiesT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getAllCapabilities", "getAllCapabilities", "getAllCapabilitiesResponse");
  }

  @Override
  protected void parse () throws Exception {
    // empty method
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  //Nada, no NE to validate
  }

  @Override
  protected void mediate() throws Exception {
    capabilitiesList = CapabilitiesFactory.getCapabilitiesListT();
  }

  @Override
  protected void response() throws Exception {
    response.setCapabilities(capabilitiesList);
  }

  @Override
  public GetAllCapabilitiesResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
