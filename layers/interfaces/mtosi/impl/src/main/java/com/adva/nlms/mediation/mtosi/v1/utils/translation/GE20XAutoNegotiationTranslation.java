/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

/**
 * EthernetPortSpeed ::= TEXTUAL-CONVENTION
 STATUS       current
 DESCRIPTION
 "Describes the Ethernet Port Speed.
 speed-unknown    : speed unknown
 speed-10MB-full  : fixed speed 10MB full duplex
 speed-10MB-half  : fixed speed 10MB half duplex
 speed-100MB-full : fixed speed 100MB full duplex
 speed-100MB-half : fixed speed 100MB half duplex
 speed-1000MB-full    : fixed speed 1000MB full duplex
 speed-1000MB-half    : fixed speed 1000MB half duplex
 speed-auto       : Auto negotiation, advertise all speeds
 speed-auto-10MB-full : Auto negotiation, advertise 10MB full duplex
 speed-auto-10MB-half : Auto negotiation, advertise 10MB half duplex
 speed-auto-100MB-full: Auto negotiation, advertise 100MB full duplex
 speed-auto-100MB-half: Auto negotiation, advertise 100MB half duplex
 speed-auto-1000MB-full: Auto negotiation, advertise 1000MB full duplex
 speed-auto-1000MB-half: Auto negotiation, advertise 1000MB half duplex
 speed-negotiating    : Auto negotiating, transient state
 speed-auto-1000MB-full-master : Auto negotiation, advertise 1000MB full duplex, sync master
 speed-auto-1000MB-full-slave : Auto negotiation, advertise 1000MB full duplex, sync slave
 speed-none :                   Used to denote speed, when negotiating
 speed-auto-1000MB-full-master-preferred : Auto negotiation,
 advertise 1000MB full duplex, preferred sync master
 speed-auto-1000MB-full-slave-preferred : Auto negotiation,
 advertise 1000MB full duplex, preferred sync slave
 speed-10G-full  : fixed speed 10G full duplex
 speed-auto-detect  : Auto detect speed; iterate through available speeds and test
 the link with remote end - if link is up at given speed, this speed is configured

 MO-MIB:

 0=speed-unknown

 1=speed-10MB-full

 2=speed-10MB-half

 3=speed-100MB-full

 4=speed-100MB-half

 5=speed-1000MB-full

 6=speed-1000MB-half

 7=speed-auto

 8=speed-auto-10MB-full

 9=speed-auto-10MB-half

 10=speed-auto-100MB-full

 11=speed-auto-100MB-half

 12=speed-auto-1000MB-full

 13=speed-auto-1000MB-half

 14=speed-negotiating

 15=speed-auto-1000MB-full-master

 16=speed-auto-1000MB-full-slave

 17=speed-none

 18=speed-auto-1000MB-full-master-preferred

 19=speed-auto-1000MB-full-slave-preferred

 20=speed-10G-full

 21=speed-auto-detect

 */

public enum GE20XAutoNegotiationTranslation {
  NOT_APPLICABLE         (-1, "n/a", "n/a", "n/a", "None"),
  speed_unknown		     (0, "Disabled", "", "0", "None"),
  DISABLED_10MB_FULL     (1, "Disabled", "Full", "10", "None"),
  DISABLED_10MB_HALF     (2, "Disabled", "Half", "10", "None"),
  DISABLED_100MB_FULL    (3, "Disabled", "Full", "100", "None"),
  DISABLED_100MB_HALF    (4, "Disabled", "Half", "100", "None"),
  DISABLED_1000MB_FULL   (5, "Disabled", "Full", "1000", "None"),
  DISABLED_1000MB_HALF   (6, "Disabled", "Half", "1000", "None"),
  ENABLED            (7, "Enabled", "","0","None"),
  ENABLED_10MB_FULL   (8, "Enabled", "Full","10","None"),
  ENABLED_10MB_HALF   (9, "Enabled", "Half","10","None"),
  ENABLED_100MB_FULL  (10, "Enabled", "Full","100","None"),
  ENABLED_100MB_HALF  (11, "Enabled", "Half","100","None"),
  ENABLED_1000MB_FULL (12, "Enabled", "Full","1000","None"),
  ENABLED_1000MB_HALF (13, "Enabled", "Half","1000","None"),
  DISABLED     (14, "Disabled","Full","0","None"),
  ENABLED_1000MB_FULL_MASTER (15, "Enabled", "Full","1000","Master"),
  ENABLED_1000MB_FULL_SLAVE (16, "Enabled", "Full","1000","Slave"),
  speed_none			       (17, "Disabled", "", "", "None"),

  ENABLED_1000MB_FULL_MASTER_PREFERED    (18, "Enabled", "Full", "1000", "Master"),
  ENABLED_1000MB_FULL_SLAVE_PREFERED    (19, "Enabled", "Full", "1000", "Slave"),

  DISABLED_10G_FULL(20, "Disabled", "Full", "10000", "None"),
  ENABLED_auto_detect(21, "Enabled", "Full", "1", "None");

  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;
  private final String duplexMode;
  private final String speedRate;
  private final String masterOrSlave;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   * @param duplexMode The duplex mode.
   * @param speedRate The current speed rate.
   */
  private GE20XAutoNegotiationTranslation (final int mibValue, final String mtosiString, final String duplexMode, final String speedRate, final String slave)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
    this.duplexMode = duplexMode;
    this.speedRate = speedRate;
    this.masterOrSlave = slave;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the duplex mode string representation.
   * @return the duplex mode string representation.
   */
  public String getDuplexMode() {
    return duplexMode;
  }

  /**
   * Returns the speed rate string representation.
   * @return the speed rate string representation.
   */
  public String getSpeedRate() {
    return speedRate;
  }

  /**
   * Returns the speed rate string representation.
   * @return the speed rate string representation.
   */
  public String getMasterOrSlave() {
    return masterOrSlave;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int mibValue)
  {
    GE20XAutoNegotiationTranslation autoNegTranslation = NOT_APPLICABLE;  // the return value

    for (GE20XAutoNegotiationTranslation tmpAutoNegTranslation : values())
    {
      if (mibValue == tmpAutoNegTranslation.getMIBValue())
      {
        autoNegTranslation = tmpAutoNegTranslation;
        break;
      }
    }
    return autoNegTranslation.getMtosiString();
  }

  /**
   * Returns the speed rate corresponding to the mib value.
   * @param mibValue  The MIB defined value
   * @return the speed rate corresponding to the mib value.
   */
  public static String getSpeedRate(final int mibValue)
  {
    GE20XAutoNegotiationTranslation autoNegTranslation = NOT_APPLICABLE;  // the return value

    for (GE20XAutoNegotiationTranslation tmpAutoNegTranslation : values())
    {
      if (mibValue == tmpAutoNegTranslation.getMIBValue())
      {
        autoNegTranslation = tmpAutoNegTranslation;
        break;
      }
    }
    return autoNegTranslation.getSpeedRate();
  }

  /**
   * Returns the masterOrSlave corresponding to the mib value.
   * @param mibValue  The MIB defined value
   * @return the masterOrSlave corresponding to the mib value.
   */
  public static String getMasterOrSlave(final int mibValue)
  {
    GE20XAutoNegotiationTranslation autoNegTranslation = NOT_APPLICABLE;  // the return value

    for (GE20XAutoNegotiationTranslation tmpAutoNegTranslation : values())
    {
      if (mibValue == tmpAutoNegTranslation.getMIBValue())
      {
        autoNegTranslation = tmpAutoNegTranslation;
        break;
      }
    }
    return autoNegTranslation.getMasterOrSlave();
  }

  /**
   * Returns the duplex mode corresponding to the mib value.
   * @param mibValue  The MIB defined value
   * @return the duplex mode corresponding to the mib value.
   */
  public static String getDuplexMode(final int mibValue)
  {
    GE20XAutoNegotiationTranslation autoNegTranslation = NOT_APPLICABLE;  // the return value

    for (GE20XAutoNegotiationTranslation tmpAutoNegTranslation : values())
    {
      if (mibValue == tmpAutoNegTranslation.getMIBValue())
      {
        autoNegTranslation = tmpAutoNegTranslation;
        break;
      }
    }
    return autoNegTranslation.getDuplexMode();
  }

  /**
   * Returns if the mib value corresponds to 'Enabled'
   * @param mibValue  The MIB defined value
   * @return if the mib value corresponds to 'Enabled'
   */
  public static boolean isEnabled(final int mibValue)
  {
    GE20XAutoNegotiationTranslation autoNegTranslation = NOT_APPLICABLE;  // the return value

    for (GE20XAutoNegotiationTranslation tmpAutoNegTranslation : values())
    {
      if (mibValue == tmpAutoNegTranslation.getMIBValue())
      {
        autoNegTranslation = tmpAutoNegTranslation;
        break;
      }
    }
    return autoNegTranslation.getMtosiString().equals("Enabled");
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mtosiString  The MIB defined value
   * @return the string representation used in MTOSI layer.
   * @param duplexMode The duplex mode.
   * @param speedRate The current speed rate.
   */
  public static int getMIBValue (final String mtosiString, final String duplexMode, final String speedRate, String slaveOrMaster)
  {
    GE20XAutoNegotiationTranslation autoNegTranslation = NOT_APPLICABLE;  // the return value

    for (GE20XAutoNegotiationTranslation tmpAutoNegTranslation : values())
    {
      if (tmpAutoNegTranslation == DISABLED) {
        continue;
      }
      if (mtosiString.equals(tmpAutoNegTranslation.getMtosiString()) &&
              duplexMode.equals(tmpAutoNegTranslation.getDuplexMode()) &&
              speedRate.equals(tmpAutoNegTranslation.getSpeedRate()) &&
              slaveOrMaster.equals(tmpAutoNegTranslation.getMasterOrSlave()))
      {
        autoNegTranslation = tmpAutoNegTranslation;
        break;
      }
    }
    return autoNegTranslation.getMIBValue();
  }

  public static GE20XAutoNegotiationTranslation getEnum (final int mibValue)
  {
    GE20XAutoNegotiationTranslation autoNegTranslation = NOT_APPLICABLE;  // the return value

    for (GE20XAutoNegotiationTranslation tmpAutoNegTranslation : values())
    {
      if (mibValue == tmpAutoNegTranslation.getMIBValue())
      {
        autoNegTranslation = tmpAutoNegTranslation;
        break;
      }
    }
    return autoNegTranslation;
  }
}
