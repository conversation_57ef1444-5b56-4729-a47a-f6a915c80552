/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;

/**
  *
  */
public enum HNPMEValidationTranslation {
    Unknown       (0),
    HNBDP         (1),
    None          (2);
    
    private int mibValue;
    
    private int getMibValue() {
		return mibValue;
	}

	private HNPMEValidationTranslation(int code) {
    	this.mibValue = code;
    }

    public static String getMtosiString(final Integer mibValue) {
    	if (mibValue == null) {
    		return MtosiConstants.NOT_APPLICABLE;
    	}
    	for (HNPMEValidationTranslation value: values() ) {
    		if (value.getMibValue() == mibValue.intValue()) {
    			return value.name(); 
    		}
    	}
    	//the value was not found, return the value passed in
    	return String.valueOf(mibValue);
    }
    
    public static int getMibValue(final String name) {
    	for (HNPMEValidationTranslation value: values() ) {
    		if (value.name().equals(name)) {
    			return value.getMibValue(); 
    		}
    	}
    	//Returning -1, the value passed in was not found.
    	return -1;
    }
}
