/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */
package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.common.config.netypes.NEType;
import com.adva.nlms.mediation.common.MDOperationNotSupportedException;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.mtosi.ManagementCTPProperties;
import com.adva.nlms.mediation.mtosi.common.remotetypectp.RemoteTypeDAOImpl;
import com.adva.nlms.mediation.mtosi.common.remotetypectp.RemoteTypeDBImpl;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.ManagementTunnelMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NVSListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.TerminationPointT;
import v1.tmf854ext.adva.CTPCreateDataT;
import v1.tmf854ext.adva.CreateCTPT;
import v1.tmf854ext.adva.CreateManagementCTPResponseT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.ArrayList;

public class CreateManagementCTPWorker extends AbstractMtosiWorker {
  Logger LOG = LogManager.getLogger(CreateManagementCTPWorker.class);

  protected CreateCTPT mtosiBody;
  protected CTPCreateDataT ctpCreateData;
  protected NamingAttributesT namingAttributes;
  protected NetworkElement networkElement;
  protected ManagementCTPProperties ctpPropsResponse;
  protected CreateManagementCTPResponseT response=new CreateManagementCTPResponseT();
  protected String ctpName;
  protected String ptpName;
  protected java.util.List<String> remoteDataPersistList;

  public CreateManagementCTPWorker(CreateCTPT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "createManagementCTP", "createManagementCTP", "createManagementCTPResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    validator.validate(networkElement.getDefaultNetworkElementTypeString());
  }

  @Override
  protected void parse() throws Exception {    //TODO LOG
    ctpCreateData = mtosiBody.getCreateData();

    if (ctpCreateData == null || (namingAttributes = ctpCreateData.getName()) == null) {
      LOG.debug("ctpCreateData == null || ctpCreateData.getName()) => null");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.PTP_NAME_MISSING);
    }

    if ((ptpName = namingAttributes.getPtpNm()) == null ) {
      LOG.debug("namingAttributes == null");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.PTP_NAME_MISSING);
    }

    if (ctpCreateData.getTransmissionParams() == null ||
            ctpCreateData.getTransmissionParams().getValue() == null) {
      LOG.debug("(ctpCreateData == null || ctpCreateData.getTransmissionParams() == null ||" +
              "            ctpCreateData.getTransmissionParams().getValue()==null)");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING);
    }

    if (!NamingTranslationFactory.isManagementDomain(namingAttributes)) {
      LOG.debug("NamingTranslationFactory.isManagementDomain(namingAttributesT)=>false");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (namingAttributes.getMeNm()==null) {
      LOG.debug("namingAttributesT.getMeNm()==null");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.ME_NAME_MISSING);
    }

    if (!namingAttributes.getMdNm().equals(OSFactory.getMDNm())) {
      LOG.debug("namingAttributesT.getMdNm().equals(OSFactory.getMDNm())=>false");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MD_NOT_FOUND);
    }

    networkElement = this.getMtosiCtrl().getLegacyMtosiMOFacade().getNEByName(namingAttributes.getMeNm());
    MtosiUtils.validateNE(networkElement);

    if (!NamingTranslationFactory.isPtpNameValid(ptpName)) {
      LOG.debug("namingAttributesT.getPtpNm() invalid");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.PTP_NAME_NOT_VALID);
    }

    if ((ctpName = namingAttributes.getCtpNm()) == null || ctpName.isEmpty()) {
      LOG.debug("namingAttributes.getCtpNm() invalid");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.CTP_NAME_MISSING);
    }

  }

  @Override
  protected void mediate() throws Exception {
    String cpeHostName, remote, ipAddress;
    LayeredParametersListT layeredParametersListT = ctpCreateData.getTransmissionParams().getValue();

    try{
        NVSListT propAdvaRemoteCPE = LayeredParameterUtils.findLayer(layeredParametersListT, LayeredParams.PROP_ADVA_RemoteCPE);
        if(propAdvaRemoteCPE == null){
            LOG.debug("Missing mandatory layer PROP_ADVA_RemoteCPE");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.CTP_MANDATORY_LAYER_MISSING);
        }
    } catch(NullPointerException ex){
          LOG.debug("Missing mandatory layer PROP_ADVA_RemoteCPE");
          throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.CTP_MANDATORY_LAYER_MISSING,ex);
    }

    try{
        cpeHostName = LayeredParameterUtils.getLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_RemoteCPE,
                LayeredParams.LrPropAdvaRemoteCPE.CPE_HOSTNAME_PARM);
        remote = LayeredParameterUtils.getLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_RemoteCPE,
                LayeredParams.LrPropAdvaRemoteCPE.REMOTE_TYPE_PARM);//it might be needed for validation TODO
        ipAddress = LayeredParameterUtils.getLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_RemoteCPE,
                LayeredParams.PropAdva.IP_ADDRESS);
        remoteDataPersistList = new ArrayList<String>();
        remoteDataPersistList.add(cpeHostName);
        remoteDataPersistList.add(remote);
        remoteDataPersistList.add(networkElement.getIPAddress());
        remoteDataPersistList.add(ipAddress);
    }catch (NullPointerException ex){
        LOG.debug("Missing mandatory key CPE_HOSTNAME_PARM || REMOTE_TYPE_PARM || IP_ADDRESS");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.CTP_MANDATORY_PARAMS_MISSING, ex);
    }

    if (cpeHostName == null || cpeHostName.isEmpty() ||
            remote == null || remote.isEmpty() ||
            ipAddress == null || ipAddress.isEmpty()) {
      LOG.debug("cpeHostName == null || cpeHostName.isEmpty() ||" +
              "remote == null || remote.isEmpty() ||" +
              "ipAddress == null || ipAddress.isEmpty()");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.CTP_MANDATORY_PARAMS_MISSING);
    }

    if ((!remote.equals(NEType.GE201.getBrandedName())) && (!remote.equals(NEType.GE201SE.getBrandedName()))) {
      LOG.debug("Remote type is invalid. Only 201 and 201SE are supported as remote types.");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.ME_NOT_SUPPORTED);
    }

    int shelfNum = NamingTranslationFactory.shelfNumberFromShelfCombo(ptpName);
    if (shelfNum == -1) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "Invalid shelf index.");
    }
    int slotNum = NamingTranslationFactory.slotNumberFromShelfCombo(ptpName);
    if (slotNum == -1) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "Invalid slot index.");
    }
    String portName = NamingTranslationFactory.portNameFromShelfCombo(ptpName);
    int portType = MtosiUtils.getPortTypeTunnel(portName);
    if (portType == -1) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.PTP_PORT_TYPE_WRONG);
    }
    int portIndex = MtosiUtils.getPortIndexTunnel(portName);
    if (portIndex == -1) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.PTP_PORT_INDEX_WRONG);
    }

    //rest of the properties
    int tunnelIndex = slotNum;
    int tunnelIndexInput = -1;
    try{
        String tunnelIndexS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_Tunnel,
                LayeredParams.LrPropAdvaTunnel.TUNNEL_INDEX);
        if (tunnelIndexS != null) {
          try {
            tunnelIndex = Integer.parseInt(tunnelIndexS);
            tunnelIndexInput =  tunnelIndex;
          } catch (NumberFormatException nex) {
            LOG.debug("tunnelIndex not an integer");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.TUNNELINDEX_NOT_INT);
          }
        }
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }
    String tunnelName = ctpName;

    int vLanId = -1;
    try{
        String vlanIdS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.VLANID);
        if (vlanIdS != null) {
          try {
            vLanId = Integer.parseInt(vlanIdS);
          } catch (NumberFormatException nex) {
            LOG.debug("vlanid not an integer");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.VLANID_INDEX_NOT_INT);
          }
        }
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    long cir = -1;
    try{
        String cirS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.CIR);
        if (cirS != null) {
          try {
            cir = Long.parseLong(cirS);
          } catch (NumberFormatException nex) {
            LOG.debug("cir not an integer");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.CIR_INDEX_NOT_INT);
          }
        }
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    long eir = -1;
    try{
        String eirS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.EIR);
        if (eirS != null) {
          try {
            eir = Long.parseLong(eirS);
          } catch (NumberFormatException nex) {
            LOG.debug("eir not an integer");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.EIR_INDEX_NOT_INT);
          }
        }
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    int cos = -1;
    try{
        String cosS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.COS);
        if (cosS != null) {
          try {
            cos = Integer.parseInt(cosS);
          } catch (NumberFormatException nex) {
            LOG.debug("cos not an integer");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.COS_INDEX_NOT_INT);
          }
        }
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    long bufferSize = -1;
    try{
        String bufferSizeS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.BUFFER_SIZE);
        if (bufferSizeS != null) {
          try {
            bufferSize = Long.parseLong(bufferSizeS);
          } catch (NumberFormatException nex) {
            LOG.debug("bufferSize not an long");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.BUFFERSIZE_NOT_LONG);
          }
        }
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    int sVlanIdEnabled = -1;
    try{
        String sVlanIdEnabledS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.S_VLANID_ENABLED);
        if (sVlanIdEnabledS != null) {
          try {
            if(! (sVlanIdEnabledS.toLowerCase().equals("true") || sVlanIdEnabledS.toLowerCase().equals("false")) ){
              LOG.debug("sVlanIdEnabledS not a boolean");
              throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                      MtosiErrorConstants.SVLANIDENABLED_NOT_BOOLEAN);
            }
            boolean value = Boolean.parseBoolean(sVlanIdEnabledS);
            if (value) {
              sVlanIdEnabled = 1;
            } else sVlanIdEnabled = 2;
          } catch (NumberFormatException nex) {
            LOG.debug("sVlanIdEnabled not a boolean");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.SVLANIDENABLED_NOT_BOOLEAN);
          }
        }
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    int sTagVLanId = -1;
    try{
        String sTagVLanIdS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.S_TAG_VLANID);
        if (sTagVLanIdS != null) {
          try {
            sTagVLanId = Integer.parseInt(sTagVLanIdS);
          } catch (NumberFormatException nex) {
            LOG.debug("sTagVLanId not an int");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.STAGVLANID_NOT_INT);
          }
        }
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    int rip2PktsEnabled = -1;
    try{
        String rip2PktsEnabledS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.RIP_2_PKTS_ENABLED);
        if (rip2PktsEnabledS != null) {
          try {
            if(! (rip2PktsEnabledS.toLowerCase().equals("true") || rip2PktsEnabledS.toLowerCase().equals("false")) ){
                LOG.debug("rip2PktsEnabled not a boolean");
                throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                        MtosiErrorConstants.RIP2PKTSENABLED_NOT_BOOLEAN);
            }
            boolean value = Boolean.parseBoolean(rip2PktsEnabledS);
            if (value) {
              rip2PktsEnabled = 1;
            } else rip2PktsEnabled = 2;
          } catch (NumberFormatException nex) {
            LOG.debug("rip2PktsEnabled not an int");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.RIP2PKTSENABLED_NOT_BOOLEAN);
          }
        }
    } catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    int encapsulationType = -1;
    String encapsulationTypeS;
    try{
        encapsulationTypeS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.ENCAPSULATION_TYPE);
        if (encapsulationTypeS != null) {
          if (encapsulationTypeS.equals(MtosiConstants.ENCAPSULATION_TYPE_ETH))    //TODO enum...
            encapsulationType=1;
          else {
            LOG.debug("encapsulationType not Ethernet");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.ENCAPSULATIONTYPE_NOT_Ethernet);
          }
        }
    }catch (NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }


    String subnetMask;
   // Pattern subnetMaskPattern = Pattern.compile("^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$");
    try{
        subnetMask = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_RemoteCPE, LayeredParams.LrPropAdvaRemoteCPE.SUBNET_MASK);
       /* if (subnetMask == null){ // || ! subnetMaskPattern.matcher(subnetMask).matches()) {
            LOG.debug("subnetMask is not valid");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.SUBNET_MASK_NAME);
        }*/
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    int createLink = -1;
    try{
        String createLinkS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_RemoteCPE,
                LayeredParams.LrPropAdvaRemoteCPE.CREATE_LINK_PARM);
        if (createLinkS != null) {
           if(! (createLinkS.toLowerCase().equals("true") || createLinkS.toLowerCase().equals("false")) ){
                LOG.debug("createLink not a boolean");
                throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                        MtosiErrorConstants.CREATELINK_NOT_BOOLEAN);
           }
          try {
            boolean value = Boolean.parseBoolean(createLinkS);
            if (value) {
              createLink = 1;
            } else createLink = 2;
          } catch (NumberFormatException nex) {
            LOG.debug("createLink not an int");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.CREATELINK_NOT_BOOLEAN);
          }
        }
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    String linkName;
    try{
        linkName = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
            LayeredParams.PROP_ADVA_RemoteCPE, LayeredParams.LrPropAdvaRemoteCPE.LINK_NAME_PARM);
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    if( tunnelIndexInput > -1 || vLanId >-1 ||  cir > -1 || eir > -1 ||  cos > -1 || bufferSize > -1 || sVlanIdEnabled > -1 || sTagVLanId > -1 || rip2PktsEnabled > -1 || encapsulationType > -1){
        try{
            NVSListT propAdvaTunnel = LayeredParameterUtils.findLayer(layeredParametersListT, LayeredParams.PROP_ADVA_Tunnel);
            if(propAdvaTunnel == null){
                LOG.debug("Missing mandatory layer PROP_ADVA_Tunnel");
                throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                        MtosiErrorConstants.CTP_MANDATORY_LAYER_MISSING);
            }
        } catch(NullPointerException ex){
            LOG.debug("Missing mandatory layer PROP_ADVA_Tunnel");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.CTP_MANDATORY_LAYER_MISSING, ex);
        }
    }

    ManagementCTPProperties ctpProps = new ManagementCTPProperties(networkElement.getID(), shelfNum, slotNum, portType, portIndex,
            tunnelIndex, tunnelName, vLanId, cir, eir, cos, bufferSize, sVlanIdEnabled, sTagVLanId, rip2PktsEnabled,
            encapsulationType, cpeHostName, ipAddress, subnetMask, remote, createLink, linkName);

    NetworkElement locks[] = new NetworkElement[] {};
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "CreateManagementCTPWorker");
    try
    {
      ctpPropsResponse=this.getMtosiCtrl().getLegacyMtosiMOFacade().createManagementCTP(ctpProps);
      NetTransactionManager.commitNetTransaction(id);
    }
    catch (SNMPCommFailure | SPValidationException | MDOperationNotSupportedException | NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally
    {
      NetTransactionManager.ensureEnd(id);
    }
  }


  @Override
  protected void response() throws Exception {
    if (ctpPropsResponse != null) {
      persistTunnelData( ctpPropsResponse, namingAttributes);
      TerminationPointT tp = new TerminationPointT();
      PhysicalTerminationPointT ptp = ManagementTunnelMediator.getManagementTunnel(ctpPropsResponse, namingAttributes);
      tp.setPtp(ptp);
      response.setTheCTP(tp);
    }
  }

  @Override
  public CreateManagementCTPResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }

   private boolean persistTunnelData(ManagementCTPProperties ctpProps,NamingAttributesT namingAttributes){
       RemoteTypeDAOImpl remoteDAOImpl = new RemoteTypeDAOImpl();
       RemoteTypeDBImpl entity = new RemoteTypeDBImpl();

       entity.setHeadMtosiName(namingAttributes.getPtpNm());
       entity.setHeadTunnelName(namingAttributes.getCtpNm());
       entity.setHeadName(namingAttributes.getMeNm());
       entity.setRemoteName(remoteDataPersistList.get(0));
       entity.setRemoteType(remoteDataPersistList.get(1));
       entity.setHeadIpAddress(remoteDataPersistList.get(2));
       entity.setRemoteIpAddress(remoteDataPersistList.get(3));
       try{
           remoteDAOImpl.addRemoteType(entity);
           return true;
       }catch(Exception ex){
           LOG.debug("Failed to persist tunnel data " + ex.getMessage());
           return false;
       }
   }
}
