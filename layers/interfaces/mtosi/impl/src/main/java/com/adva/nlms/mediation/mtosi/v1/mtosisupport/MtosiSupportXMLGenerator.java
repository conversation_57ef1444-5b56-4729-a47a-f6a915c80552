/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.mtosisupport;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Enumeration;
import java.util.List;

import org.exolab.castor.xml.MarshalException;
import org.exolab.castor.xml.Marshaller;
import org.exolab.castor.xml.Unmarshaller;
import org.exolab.castor.xml.ValidationException;

import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.common.mtosisupport.MTOSIOperation;
import com.adva.nlms.common.mtosisupport.MTOSISupport;
import com.adva.nlms.common.mtosisupport.NetworkElement;
import com.adva.nlms.common.mtosisupport.types.OperationName;
import com.adva.nlms.common.mtosisupport.types.OperationName_BT;
import com.adva.nlms.common.mtosisupport.types.OperationName_DB;
import com.adva.nlms.common.mtosisupport.types.OperationName_FSPNM;
import com.adva.nlms.common.mtosisupport.types.SupportedNEs;
import com.adva.nlms.common.mtosisupport.types.SupportedNEs_BT;

/**
 * This class generates the an xml file.
 * 
 * If this parameter is not present, the default mtosisupport.xml is used. This
 * file identifies which mtosi operations are valid for a given NE Type.
 * 
 * The NetworkElement type "FSP NM" can be used for operations that do not refer
 * to a Network Element, or are available for ALL Network Elements.
 * <NetworkElement type="FSP NM"> <MTOSIOperation name="getAllCapabilities"
 * supported="true"/> <MTOSIOperation name="getAllMDNames" supported="true"/>
 * <MTOSIOperation name="getAllMDs" supported="true"/> <MTOSIOperation
 * name="getAllMENamesPassingFilter" supported="true"/> <MTOSIOperation
 * name="getAllMENamesWrtOS" supported="true"/> <MTOSIOperation
 * name="getAllOSNames" supported="true"/> <MTOSIOperation name="getAllOSs"
 * supported="true"/> </NetworkElement>
 * 
 * 
 * 
 * <AUTHOR>
 * 
 */
public class MtosiSupportXMLGenerator {

	private static final int BT = 1;
	private static final int DB = 2;

	private MtosiSupportXMLGenerator() {
	}

	public static void main(String[] args) throws ProcessingFailureException {
		MtosiSupportXMLGenerator ms = new MtosiSupportXMLGenerator();

		MTOSISupport btSupport = ms.generateMTOSISupport(BT);
		MTOSISupport dbSupport = ms.generateMTOSISupport(DB);
		File btFile = new File("mtosisupport_bt_gen.xml");
		File dbFile = new File("mtosisupport_db_gen.xml");

		ms.writeObject(btSupport, btFile);

		ms.writeObject(dbSupport, dbFile);
	}

	/**
	 * Load the specified xml file. This file must validate to the
	 * mtosisupport.xsd. If the filename is not specified, the default
	 * "mtosisupport.xml" is used.
	 * 
	 * @param filename
	 * @return an MTOSISupport Document structure (created by Castor).
	 */

	private MTOSISupport loadXml(String filename) {
		InputStream inputStream = null;
		try {
			if (filename != null) {
				inputStream = new FileInputStream(filename);
			} else {
				inputStream = new FileInputStream("../../common/mtosisupport/mtosisupport.xml");
			}
		} catch (FileNotFoundException e) {
		}
		return (MTOSISupport) readObject(inputStream, MTOSISupport.class);

	}

	private Object readObject(InputStream inputStream, final Class clazz) {
		try {

			InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
			Unmarshaller unmarshaller = new Unmarshaller(clazz);

			unmarshaller.setValidation(false);

			return unmarshaller.unmarshal(inputStreamReader);
		} catch (Exception e) {
			e.printStackTrace();
		}

		return null;
	}

	private void writeObject(MTOSISupport support, File file) {

		try {
			PrintWriter pw = new PrintWriter(file,"UTF-8");
			Marshaller m = new Marshaller(pw);
			m.setNoNamespaceSchemaLocation("src/com/adva/nlms/common/mtosisupport.xsd");
			m.marshal(support);
		} catch (MarshalException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (ValidationException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (FileNotFoundException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}

	private MTOSISupport generateMTOSISupport(int type) {
		MTOSISupport result = new MTOSISupport();
		List<SupportedNEs> list = getSortedNEList();
		for (SupportedNEs neType : list) {
			if (isValidNETypeForType(type, neType.toString())) {
				NetworkElement networkElement = new NetworkElement();
				networkElement.setType(neType);
				List<OperationName> opList = getSortedOpList();
				for (OperationName opName : opList) {
					if (isValidForType(type, opName, networkElement)) {
						MTOSIOperation op = new MTOSIOperation();
						op.setName(opName);
						networkElement.addMTOSIOperation(op);
					}
				}
				result.addNetworkElement(networkElement);
			}
		}

		return result;

	}

	private List<SupportedNEs> getSortedNEList() {
		ArrayList<SupportedNEs> list = new ArrayList<SupportedNEs>();
		Enumeration<SupportedNEs> enumeration = SupportedNEs.enumerate();
		while (enumeration.hasMoreElements()) {
			list.add(enumeration.nextElement());
		}
		Comparator<SupportedNEs> c = new Comparator<SupportedNEs>() {
			@Override
			public int compare(SupportedNEs o1, SupportedNEs o2) {
				if (o1 == null) {
					return -1;
				}
				if (o2 == null) {
					return 1;
				}
				// Lets Bubble this one to the top... Bit of a hack!
				if ("FSP NM".equals(o1.toString())) {
					return -1;
				}
				if ("FSP NM".equals(o2.toString())) {
					return 1;
				}
				return o1.toString().compareTo(o2.toString());
			}
		};
		Collections.sort(list, c);
		return list;
	}

	private List<OperationName> getSortedOpList() {
		ArrayList<OperationName> list = new ArrayList<OperationName>();
		Enumeration<OperationName> enumeration = OperationName.enumerate();
		while (enumeration.hasMoreElements()) {
			list.add(enumeration.nextElement());
		}
		Comparator<OperationName> c = new Comparator<OperationName>() {
			@Override
			public int compare(OperationName o1, OperationName o2) {
				if (o1 == null) {
					return -1;
				}
				if (o2 == null) {
					return 1;
				}
				return o1.toString().compareTo(o2.toString());
			}
		};
		Collections.sort(list, c);
		return list;
	}

	private boolean isValidNETypeForType(int type, String neType) {
		if (type == BT) {
			return isContainedInList(neType.toString(), SupportedNEs_BT.enumerate());
		}
		return true;
	}

	private boolean isValidForType(int type, OperationName opName, NetworkElement networkElement) {
		if (type == BT) {
			if (networkElement.getType().toString().equals("FSP NM")) {
				return isContainedInList(opName.toString(), OperationName_FSPNM.enumerate())
						&& isContainedInList(opName.toString(), OperationName_BT.enumerate());
			} else {
				return isContainedInList(opName.toString(), OperationName_BT.enumerate())
						&& !isContainedInList(opName.toString(), OperationName_FSPNM.enumerate());
			}
		}
		if (type == DB) {
			if (networkElement.getType().toString().equals("FSP NM")) {
				return isContainedInList(opName.toString(), OperationName_FSPNM.enumerate())
						&& isContainedInList(opName.toString(), OperationName_DB.enumerate());
			} else {
				return isContainedInList(opName.toString(), OperationName_DB.enumerate())
						&& !isContainedInList(opName.toString(), OperationName_FSPNM.enumerate());
			}
		}
		return false;
	}

	private boolean isContainedInList(String operName, Enumeration enumerate) {
		while (enumerate.hasMoreElements()) {
			Object foo = enumerate.nextElement();
			if (operName.equals(foo.toString())) {
				return true;
			}
		}
		return false;
	}

}
