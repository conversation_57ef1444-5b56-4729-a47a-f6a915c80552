/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

/**
  *
  */
public enum HNAdminSpeedRateTranslation {
    unknown       (0,"PROP_HATTERAS_Unknown"),
    auto          (1,"0"),
    mode10        (2,"10"),
    mode100       (3,"100"),
    mode1000      (4,"1000"),
    variable      (5,"PROP_HATTERAS_Variable");
    
    private int mibValue;
    private String stringValue;
    
    private int getMibValue() {
		return mibValue;
	}

	private HNAdminSpeedRateTranslation(int code, String value) {
    	this.mibValue = code;
    	this.stringValue = value;
    }

    public static String getMtosiString(final Integer mibValue) {
    	if (mibValue == null) {
    		return unknown.stringValue;
    	}
    	for (HNAdminSpeedRateTranslation value: values() ) {
    		if (value.getMibValue() == mibValue.intValue()) {
    			return value.stringValue; 
    		}
    	}
    	//Probably should throw something...
		return unknown.stringValue;
    }
    
    public static int getMibValue(final String name) {
    	for (HNAdminSpeedRateTranslation value: values() ) {
    		if (value.stringValue.equals(name)) {
    			return value.getMibValue(); 
    		}
    	}
    	//Probably should throw something...
    	return -1;
    }
}
