/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */
package com.adva.nlms.mediation.mtosi.v1.worker.cfm;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.MACompAttr;
import com.adva.nlms.mediation.config.dto.attr.MANetAttr;
import com.adva.nlms.mediation.config.dto.attr.MDAttr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3AccAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3NetAttr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854ext.adva.DeleteMaintenanceAssociationResponseT;
import v1.tmf854ext.adva.DeleteMaintenanceAssociationT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.HashMap;
import java.util.Map;

public class DeleteMaintenanceAssociationWorker extends AbstractMtosiWorker {

  private final DeleteMaintenanceAssociationT mtosiBody;
  private MtosiAddress mtosiAddress;
  protected DeleteMaintenanceAssociationResponseT response = new DeleteMaintenanceAssociationResponseT();
  private NetworkElement ne;
  private MtosiMOFacade facade;
  private DTO<MANetAttr> manet;
  Logger LOG = LogManager.getLogger(this.getClass().getName());
  private DTO ptp;
  private DTO<MACompAttr> macomp;

  private static Map<Integer, String> namePatterns = new HashMap<>();
  private static final String patternPTPForGE = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=((NET-(\\d+))|(ACC))";
  public static final String patternACC = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=(ACC)";
  public static final String patternNET = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=(NET)-(\\d+)";

  static {
    namePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201, patternPTPForGE);
    namePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE, patternPTPForGE);
  }

  public DeleteMaintenanceAssociationWorker(DeleteMaintenanceAssociationT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader,"deleteMaintenanceAssociation", "deleteMaintenanceAssociation", "deleteMaintenanceAssociationResponse");
    this.mtosiBody = mtosiBody;
    this.mtosiHeader = mtosiHeader;

  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    validator.validate(mtosiAddress.getNE().getDefaultNetworkElementTypeString());
  }

  @Override
  protected void parse() throws ProcessingFailureException {
    NamingAttributesT meName = mtosiBody.getMaName();

    if(meName == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "MeName has not been specified or is invalid.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    mtosiAddress = new MtosiAddress(meName);

    if(mtosiAddress.getNaming().getMdNm()== null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MD_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    if (!mtosiAddress.getNaming().getMdNm().equals(OSFactory.getMDNm())) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MD_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if(mtosiAddress.getNaming().getMeNm()== null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.ME_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    MtosiUtils.validateNE(mtosiAddress.getNE());
    ne = mtosiAddress.getNE();
    if(mtosiAddress.getNaming().getCfmMdNm() == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "The Maintenance Domain name must be specified in the request.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    if(mtosiAddress.getNaming().getMaNm() == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "The Maintenance Association name must be specified in the request.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    if(mtosiAddress.getNaming().getPtpNm() == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MDFR_PTP_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if(!mtosiAddress.getNaming().getPtpNm().matches(namePatterns.get(ne.getNetworkElementType()))){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MDFR_PTP_INVALID);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    facade = getMtosiCtrl().getMoFacadeManager().getFacadeViaNeId(MtosiMOFacade.class, ne.getID());

    ptp = facade.findDTOViaMtosiName(ne.getID(), mtosiAddress.getNaming().getPtpNm(), getPtpEntityPerNe(mtosiAddress.getNaming().getPtpNm()));

    if(ptp == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.PTP_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    DTO<MDAttr> md = facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+mtosiAddress.getNaming().getCfmMdNm(), MDAttr.class);
    if(md == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MDFR_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    manet = facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+mtosiAddress.getNaming().getCfmMdNm() +" && /ma="+mtosiAddress.getNaming().getMaNm(), MANetAttr.class);
    if(manet == null)  {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MANETFR_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    macomp = facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+mtosiAddress.getNaming().getCfmMdNm() +" && /ma="+mtosiAddress.getNaming().getMaNm() + " && " + mtosiAddress.getNaming().getPtpNm(), MACompAttr.class);
    if(macomp == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MACOMPFR_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }

  @Override
  protected void mediate() throws Exception {
    transact();
  }

  private void transact() throws ObjectInUseException, SNMPCommFailure, ProcessingFailureException {
    try {
      facade.openNetTransaction(ne.getID());
      facade.deleteObjectOnDevice(ne.getID(), macomp);
      ne.logSROperation(SROperationState.SERVICE_DELETION_SUCCESS, macomp.getValue(ManagedObjectAttr.MTOSI_NAME));
      boolean hasOtherMacomps = facade.findChildrenDTOsViaParentEntityIndex(ne.getID(),manet.getValue(MANetAttr.ENTITY_INDEX), MACompAttr.class).size() > 0;
      if(!hasOtherMacomps){
        facade.deleteObjectOnDevice(ne.getID(), manet);
        ne.logSROperation(SROperationState.SERVICE_DELETION_SUCCESS, manet.getValue(ManagedObjectAttr.MTOSI_NAME));
      }
    } catch (ObjectInUseException | SNMPCommFailure e) {
      ne.logSROperation(SROperationState.SERVICE_DELETION_FAILURE, manet.getValue(ManagedObjectAttr.MTOSI_NAME));
      throw e;
    } finally {
      facade.closeNetTransaction(ne.getID());
    }
  }
  @Override
  protected void response() {

  }

  @Override
  public Object getSuccessResponse() {
    if (response == null)
      return null;
    return response;
  }

  private Class getPtpEntityPerNe(String portType) throws ProcessingFailureException{
    if (portType.matches(patternNET)) {
      return PortF3NetAttr.class;
    } else if (portType.matches(patternACC)) {
      return PortF3AccAttr.class;
    }else{
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "Port type cannot be identified for the specified Termination Point.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }
}
