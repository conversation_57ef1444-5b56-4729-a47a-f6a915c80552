/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.mediation.mtosi.v2.utils.translations.f3.TranslatableEnum;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

public enum GE20XSecondaryStateTranslation implements TranslatableEnum {
  NOT_APPLICABLE            (0, "n/a"),
  ACTIVE                    (1, "Active"),
  AUTOMATIC_IN_SERVICE      (2, "AutomaticInService"),
  FACILITY_FAILURE          (3, "FacilityFailure"),
  FAULT                     (4, "Fault"),
  LOOP<PERSON><PERSON>K                  (5, "Loopback"),
  MAIN<PERSON><PERSON><PERSON><PERSON>               (6, "Maintenance"),
  MISMATCHED_EQPT           (7, "MismatchedEquipment"),
  STANDBY_HOT               (8, "StandbyHot"),
  SUPPORTING_ENTITY_OUTAGE  (9, "SupportingEntityOutage"),
  UNASSIGNED                (10, "Unassigned"),
  UNEQUIPPED                (11, "Unequipped"),
  DISABLED                  (12, "Disabled"),
  FORCED_OFFLINE            (13, "ForcedOffline"),
  INITIALIZING              (14, "Initializing"),
  PRTCL                     (15, "Protocol"),
  BLCKD                     (16, "Blocked");


  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private GE20XSecondaryStateTranslation(final int mibValue, final String mtosiString)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param bitFieldValue  The bitfield value.
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int bitFieldValue) {
    List<String> states = new ArrayList<>();
    for (GE20XSecondaryStateTranslation secondaryState : values()) {
      if (((bitFieldValue >> secondaryState.mibValue) & 1) == 1) {
        states.add(secondaryState.getMtosiString());
      }
    }
    return StringUtils.collectionToDelimitedString(states, ", ");
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param bitFieldValue  The bitfield value.
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiStringForMemberState(final int bitFieldValue) {
    if (((bitFieldValue >> GE20XSecondaryStateTranslation.ACTIVE.getMIBValue() ) & 1) == 1) {
      return "Active";
    }else{
      return "StandBy";
    }
  }
}
