/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker;

import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.mtosi.NetworkElementFSP150CMMTOSIOperations;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CM;
import com.adva.nlms.mediation.mtosi.MtosiCtrl;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.HeaderUtils;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import com.adva.nlms.mediation.security.api.event.SystemEventLogging;
import v1.tmf854.ActivityStatusEnumT;
import v1.tmf854.ActivityStatusT;
import v1.tmf854.HeaderT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

abstract public class AbstractMtosiWorker
{
	private boolean success = false;
	protected Holder<HeaderT> mtosiHeader;
	private String activityName = null;
	private String requestMsg = null;
	private String responseMsg = null;
	private String ipAddress = "Unknown";
  //Get the mtosiCtrl in order to have access to the MTOSI Facade
  private MtosiCtrl mtosiCtrl;


  public AbstractMtosiWorker(Holder<HeaderT> mtosiHeader, String activityName, String requestMsg, String responseMsg)
	{
		this.mtosiHeader = mtosiHeader;
		this.activityName = activityName;
		this.requestMsg = requestMsg;
		this.responseMsg = responseMsg;
	}

	public void validateRequestHeader() throws Exception
	{
		HeaderUtils.validateRequestHeader(mtosiHeader, getActivityName(), getReqestMsg());
	}

//	public void validateNE(NetworkElement ne) throws Exception
//	{
//    ServiceUtils.validateNE(ne, mtosiHeader);
//	}

	public void formatResponseHeader() throws Exception
	{
		HeaderUtils.formatResponseHeader(mtosiHeader, getActivityName(), getResponseMsg());
	}

  @MDPersistenceContext
	public void doWork(MtosiSupportValidator validator,MtosiCtrl mtosiCtrl) throws Exception
	{
			validateRequestHeader();
			formatResponseHeader();
            this.mtosiCtrl = mtosiCtrl;
			execute(validator);
	}

	public final void execute(MtosiSupportValidator validator) throws Exception {
		parse();
		if (!validator.isUnconditional()) {
			validate(validator);
		}
    mediate();
    response();
    setSuccess(true);
  }

  abstract protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException;

  abstract protected void parse() throws Exception;

  abstract protected void mediate() throws Exception;

  abstract protected void response() throws Exception;

  abstract public Object getSuccessResponse();

  public boolean isSuccess()
	{
		return success;
	}

	public void logSecurity(NetworkElement ne, SystemAction logType, String entity)
	{
		mtosiCtrl.getSecurityCtrl().getSystemEventLogging()
                .addNeEvent(SystemEventLogging.Context.MTOSI, ne.getName(), ne.getID(), logType, entity, entity);
	}

	public void setSuccess(boolean success)
	{
		this.success = success;
		if (isSuccess())
		{
			ActivityStatusT status = new ActivityStatusT();
			status.setValue(ActivityStatusEnumT.SUCCESS);
			mtosiHeader.value.setActivityStatus(status);
		}
	}

	public Holder<HeaderT> getMtosiHeader()
	{
		return mtosiHeader;
	}

//	public void setMtosiHeader(Holder<HeaderT> mtosiHeader)
//	{
//		this.mtosiHeader = mtosiHeader;
//	}

//	public void setActivityName(String activityName)
//	{
//		this.activityName = activityName;
//	}

	public String getActivityName()
	{
		return this.activityName;
	}

//	public void setRequestMsg(String requestMsg)
//	{
//		this.requestMsg = requestMsg;
//	}

	public String getReqestMsg()
	{
		return this.requestMsg;
	}

//	public void setResponseMsg(String responseMsg)
//	{
//		this.responseMsg = responseMsg;
//	}

	public String getResponseMsg()
	{
		return this.responseMsg;
	}

	public String getIpAddress()
	{
		return ipAddress;
	}

	public void setIpAddress(String ipAddress)
	{
		this.ipAddress = ipAddress;
	}
	
	protected static NetworkElementFSP150CMMTOSIOperations getFSP150CMMTOSIWorker(NetworkElementFSP150CM ne) {
    return (NetworkElementFSP150CMMTOSIOperations)ne.getMTOSIWorker();
  }

  public MtosiCtrl getMtosiCtrl() {
    return mtosiCtrl;
  }

  public void setMtosiCtrl(MtosiCtrl mtosiCtrl) {
    this.mtosiCtrl = mtosiCtrl;
  }
}
