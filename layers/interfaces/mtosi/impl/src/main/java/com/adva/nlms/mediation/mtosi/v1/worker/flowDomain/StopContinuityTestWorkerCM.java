/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.f3.entity.port.acc.MTOSIPortF3Acc;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;

import jakarta.xml.ws.Holder;

public class StopContinuityTestWorkerCM extends StopContinuityTestWorker {
  private String ctpName;

  public StopContinuityTestWorkerCM(Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne)
  {
    super(mtosiHeader, namingAttributes, ne);
  }

  @Override
  protected void parse() throws Exception {
    super.parse();
    if ((this.ctpName = namingAttributes.getCtpNm()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ctpNm has not been specified.");
    }
  }

  @Override
  protected void mediate() throws Exception {
    super.mediate();
    if (!(port instanceof MTOSIPortF3Acc)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "Continuity test should be run on ACC Port.");
    }
    MTOSIPortF3Acc portACC = (MTOSIPortF3Acc) port;
    MTOSIFlowF3 flow;
    if ((flow = portACC.getFlowFSP150CMByName(ctpName)) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.CTP_NOT_FOUND);
    }
    transact(flow);
  }

  @Override
  protected void response () throws Exception {
    // empty method
  }

  private void transact(MTOSIFlowF3 flow) throws ObjectInUseException, NetTransactionException, SNMPCommFailure
  {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, getClass().getSimpleName());
    try {
      flow.stopContinuityTest();
      NetTransactionManager.commitNetTransaction(id);
    }
    catch (NetTransactionException e)
    {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    catch (SNMPCommFailure e)
    {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    finally{
      NetTransactionManager.ensureEnd(id);
    }
  }
}
