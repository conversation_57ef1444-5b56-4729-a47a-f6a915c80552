/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */
package com.adva.nlms.mediation.mtosi.v1.worker.cfm;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.MACompAttr;
import com.adva.nlms.mediation.config.dto.attr.MANetAttr;
import com.adva.nlms.mediation.config.dto.attr.MDAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3AccAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3NetAttr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp20X.FspGE20XCFMMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import v1.tmf854ext.adva.GetMaintenanceAssociationResponseT;
import v1.tmf854ext.adva.GetMaintenanceAssociationT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.HashMap;
import java.util.Map;

public class GetMaintenanceAssociationWorker extends AbstractMtosiWorker {

  private static Map<Integer, String> namePatterns = new HashMap<>();
  private static final String patternPTPForGE = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=((NET-(\\d+))|(ACC))";
  public static final String patternACC = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=(ACC)";
  public static final String patternNET = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=(NET)-(\\d+)";

  static {
    namePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201, patternPTPForGE);
    namePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE, patternPTPForGE);
  }

  private final GetMaintenanceAssociationT mtosiBody;
  private MtosiAddress mtosiAddress;
  protected GetMaintenanceAssociationResponseT response = new GetMaintenanceAssociationResponseT();
  Logger LOG = LogManager.getLogger(this.getClass().getName());
  private MtosiMOFacade facade;
  private DTO<MDAttr> md;
  private DTO<MANetAttr> manet;
  private DTO<MACompAttr> macomp;
  private NetworkElement ne;
  private DTO ptp;

  public GetMaintenanceAssociationWorker(GetMaintenanceAssociationT mtosiBody,  Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getMaintenanceAssociation", "getMaintenanceAssociation", "getMaintenanceAssociationResponse");
    this.mtosiBody = mtosiBody;
    this.mtosiHeader = mtosiHeader;
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    validator.validate(mtosiAddress.getNE().getDefaultNetworkElementTypeString());
  }

  @Override
  protected void parse() throws Exception {
    NamingAttributesT meName = mtosiBody.getMaName();

    if(meName == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "MeName has not been specified or is invalid.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    mtosiAddress = new MtosiAddress(meName);

    if(mtosiAddress.getNaming().getMdNm()== null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MD_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    if (!mtosiAddress.getNaming().getMdNm().equals(OSFactory.getMDNm())) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MD_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if(mtosiAddress.getNaming().getMeNm()== null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.ME_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if(mtosiAddress.getNaming().getCfmMdNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MDFR_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if(mtosiAddress.getNaming().getMaNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MAFR_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    MtosiUtils.validateNE(mtosiAddress.getNE());
    ne = mtosiAddress.getNE();
    facade = getMtosiCtrl().getMoFacadeManager().getFacadeViaNeId(MtosiMOFacade.class, ne.getID());
    if(mtosiAddress.getNaming().getPtpNm() == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MDFR_PTP_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if(!mtosiAddress.getNaming().getPtpNm().matches(namePatterns.get(ne.getNetworkElementType()))){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MDFR_PTP_INVALID);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }

  @Override
  protected void mediate() throws Exception {
    ptp = facade.findDTOViaMtosiName(ne.getID(), mtosiAddress.getNaming().getPtpNm(), getPtpEntityPerNe(mtosiAddress.getNaming().getPtpNm()));

    if(ptp == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.PTP_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if((md = facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+ mtosiAddress.getNaming().getCfmMdNm(), MDAttr.class)) == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MDFR_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    manet = facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+mtosiAddress.getNaming().getCfmMdNm() +" && /ma="+ mtosiAddress.getNaming().getMaNm(), MANetAttr.class);
//    isManetExist = manet != null;
    macomp = facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+mtosiAddress.getNaming().getCfmMdNm() +" && /ma="+ mtosiAddress.getNaming().getMaNm()+ " && " + mtosiAddress.getNaming().getPtpNm(), MACompAttr.class);
    if(macomp  == null)  {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_CAPACITY_EXCEEDED,
          MtosiErrorConstants.MACOMPFR_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }

  @Override
  protected void response() {
    v1.tmf854ext.adva.ObjectFactory factory = new v1.tmf854ext.adva.ObjectFactory();
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    NamingAttributesT nm = NamingTranslationFactory.createNamingAttributesMaintenanceAssociation(mtosiAddress.getNaming().getMeNm(), mtosiAddress.getNaming().getCfmMdNm(),
        mtosiAddress.getNaming().getMaNm() + " && " + mtosiAddress.getNaming().getPtpNm());
    response.setMaName(factory.createCreateMaintenanceAssociationResponseTMaName(nm));
    response.setDiscoveredName(factory.createCreateMaintenanceDomainResponseTDiscoveredName(macomp.getValue(MACompAttr.MTOSI_NAME)));
    response.setNamingOS(factory.createCreateMaintenanceDomainResponseTNamingOS(OSFactory.getNmsName()));
    response.setSource(factory.createCreateMaintenanceDomainResponseTSource(source));
    response.setTransmissionParams(factory.createCreateMaintenanceAssociationResponseTTransmissionParams(new FspGE20XCFMMediator().toMtosiMA(manet, macomp)));
  }

  @Override
  public Object getSuccessResponse() {
    if (response == null)
      return null;
    return response;
  }

  private Class getPtpEntityPerNe(String portType) throws ProcessingFailureException{
    if (portType.matches(patternNET)) {
      return PortF3NetAttr.class;
    } else if (portType.matches(patternACC)) {
      return PortF3AccAttr.class;
    } else{
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "Port type cannot be identified for the specified Termination Point.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }
}
