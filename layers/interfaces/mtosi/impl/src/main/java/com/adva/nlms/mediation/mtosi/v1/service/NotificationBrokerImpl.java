/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;

import java.util.logging.Logger;

import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.NotificationBroker;
import ws.v1.tmf854.ProcessingFailureException;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.HeaderUtils;


/**
 * This class was generated by the Celtix 1.1-SNAPSHOT
 * Fri Dec 22 10:48:24 EST 2006
 * Generated source version: 1.1-SNAPSHOT
 * 
 */

@jakarta.jws.WebService(name = "NotificationBroker", serviceName = "NotificationService", portName = "NotificationBrokerHttp", targetNamespace = "tmf854.v1.ws")

public class NotificationBrokerImpl implements NotificationBroker {

    private static final Logger LOG = Logger.getLogger(NotificationBrokerImpl.class.getPackage().getName());

    /* (non-Javadoc)
     * @see ws.v1.tmf854.NotificationBroker#subscribe(v1.tmf854.SubscribeT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.SubscribeResponseT subscribe(
        v1.tmf854.SubscribeT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "subscribe", "subscribeResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.SubscribeResponseT subscribe(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.SubscribeT mtosiBody) throws ProcessingFailureException
    {
    	return subscribe(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.NotificationBroker#unsubscribe(v1.tmf854.UnsubscribeT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.UnsubscribeResponseT unsubscribe(
        v1.tmf854.UnsubscribeT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "unsubscribe", "unsubscribeResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.UnsubscribeResponseT unsubscribe(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.UnsubscribeT mtosiBody) throws ProcessingFailureException
    {
    	return unsubscribe(mtosiBody, mtosiHeader);
    }

  @Override
  public void notify(v1.tmf854.HeaderT mtosiHeader, v1.tmf854.NotifyT mtosiBody)
    {
    	LOG.info("Executing operation notify");
    }
}
