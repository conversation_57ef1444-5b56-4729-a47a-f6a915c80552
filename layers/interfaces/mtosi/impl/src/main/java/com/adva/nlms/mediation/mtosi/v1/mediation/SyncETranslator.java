/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation;

import com.adva.nlms.mediation.config.f3.entity.sync.F3Sync;

/**
 * Translator for SyncE (Timing Domain Fragment)
 */
public class SyncETranslator extends MtosiTranslator {

  private F3Sync f3Sync;

  public SyncETranslator(F3Sync f3Sync) {
    this.f3Sync = f3Sync;
  }
}
