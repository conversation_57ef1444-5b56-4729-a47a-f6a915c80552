/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v2.utils.feature.interceptor;

import org.apache.cxf.binding.soap.SoapMessage;
import org.apache.cxf.binding.soap.interceptor.AbstractSoapInterceptor;
import org.apache.cxf.binding.soap.saaj.SAAJOutInterceptor;
import org.apache.cxf.interceptor.Fault;
import org.apache.cxf.phase.Phase;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.XMLConstants;
import javax.xml.namespace.NamespaceContext;
import jakarta.xml.soap.SOAPBody;
import jakarta.xml.soap.SOAPMessage;
import javax.xml.xpath.XPathConstants;
import java.util.Iterator;

public class RemoveNillFieldsInterceptor extends AbstractSoapInterceptor {

  private SAAJOutInterceptor saajIn = new SAAJOutInterceptor();

  public RemoveNillFieldsInterceptor() {
    super(Phase.PRE_PROTOCOL);
    getAfter().add(SAAJOutInterceptor.class.getName());
  }

  private SOAPMessage getSOAPMessage(SoapMessage smsg) {
    SOAPMessage soapMessage = smsg.getContent(SOAPMessage.class);
    if (soapMessage == null) {
      saajIn.handleMessage(smsg);
      soapMessage = smsg.getContent(SOAPMessage.class);
    }
    return soapMessage;
  }

  @Override
  public void handleMessage(SoapMessage message) throws Fault {
    SOAPMessage soapMessage = message.getContent(SOAPMessage.class);
    if (soapMessage == null) {
      getSOAPMessage(message);
    }
    message.getInterceptorChain().add(new EndingInterceptor());
  }

  static class EndingInterceptor extends AbstractSoapInterceptor {
    private String xpathString = "//*[@xsi:nil='true']";
    private SAAJOutInterceptor.SAAJOutEndingInterceptor saajIn = new SAAJOutInterceptor.SAAJOutEndingInterceptor();

    public EndingInterceptor() {
      super(Phase.PRE_PROTOCOL_ENDING);
      addBefore(SAAJOutInterceptor.SAAJOutEndingInterceptor.class.getName());
    }

    public void handleMessage(SoapMessage message) throws Fault {
      SOAPMessage soapMessage = message.getContent(SOAPMessage.class);
      if (soapMessage == null) {
        soapMessage = getSOAPMessage(message);
      }
      try {
        SOAPBody body = soapMessage.getSOAPBody();
        javax.xml.xpath.XPathFactory factory = javax.xml.xpath.XPathFactory.newInstance();
        javax.xml.xpath.XPath xpath = factory.newXPath();
        xpath.setNamespaceContext(new XSINameSpaceContext());
        javax.xml.xpath.XPathExpression expression = xpath.compile(xpathString);
        Object result = expression.evaluate(body, XPathConstants.NODESET);
        NodeList nodeList = (NodeList) result;
        for (int i = 0; i < nodeList.getLength(); i++) {
          Node element = (Node)nodeList.item(i);
          element.getParentNode().removeChild(nodeList.item(i));
        }
      } catch (Exception e) {
        throw new Fault(e);
      }
      message.getInterceptorChain().doIntercept(message);
    }

    private SOAPMessage getSOAPMessage(SoapMessage smsg) {
      SOAPMessage soapMessage = smsg.getContent(SOAPMessage.class);
      if (soapMessage == null) {
        saajIn.handleMessage(smsg);
        soapMessage = smsg.getContent(SOAPMessage.class);
      }
      return soapMessage;
    }

    class XSINameSpaceContext implements NamespaceContext {
      public String getNamespaceURI(String prefix) {
        if (prefix.equals("xsi")) {
          return "http://www.w3.org/2001/XMLSchema-instance";
        } else {
          return XMLConstants.NULL_NS_URI;
        }
      }

      public String getPrefix(String namespace) {
        return null;
      }

      public Iterator getPrefixes(String namespace) {
        return null;
      }
    }
  }


}
