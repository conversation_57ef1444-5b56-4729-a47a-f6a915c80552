/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation;

import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.ProtectionGroupF3Attr;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import v1.tmf854.DirectionalityT;
import v1.tmf854.FloatingTerminationPointT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ResourceStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.common.serviceProvisioning.FTPSPProperties;
import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;

/**
 * This class is a generic FTP MTOSI Translator.
 */
public class FTPTranslator extends MtosiTranslator {
  private FTP ftp;

  public FTPTranslator(FTP ftp) {
    this.ftp = ftp;
  }

  public FTPTranslator() {
  }

  @Override
  public FloatingTerminationPointT toMtosiFTP() throws ProcessingFailureException {
    final ObjectFactory objFactory = new ObjectFactory();
    final FloatingTerminationPointT floatingTerminationPointT = objFactory.createFloatingTerminationPointT();
    final FTPSPProperties ftpSPProperties = ftp.getFTPSPProperties();
    final int moduleType = getModuleType();
    NamingAttributesT namingAttribute = NamingTranslationFactory.getNamingAttributes(ftp);

    fillCommonInformation(objFactory, floatingTerminationPointT,namingAttribute);

    // layers
    final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_LAG_FRAGMENT);
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_LAG);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_LAG,
            LayeredParams.LrLag.ALLOCATED_NUMBER_PARAM, "2");

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_LAG,
            LayeredParams.LrLag.ALLOCATION_MAXIMUM_PARAM, "2");

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_LAG,
            LayeredParams.LrLag.FRAGMENT_SERVER_LAYER_PARAM, LayeredParams.LR_ETHERNET);
    // -------end of Layer-------

    fillProtection150cpLayer(layeredParametersListT);

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.CONNECTIONLESS_PORT_PARAM, MtosiConstants.TRUE);

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.INTERFACE_TYPE_PARAM, "NNI");
    
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.PORT_TP_ROLE_STATE_PARAM, "internal");

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.MAX_NUM_FDFRS_PARAM, getMaxNumFdfrs());

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.NUM_CONFIGURED_FDFRS_PARAM, Integer.toString(ftpSPProperties.get(FTPSPProperties.VI.NumOfFDFrs)));
    // -------end of Layer-------

    fillAdvaLagLayer(layeredParametersListT);

    fillProtection150cmLayer(layeredParametersListT);

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET);
    fillAdvaEthernetLayer(layeredParametersListT, moduleType);
    // -------end of Layer-------

    floatingTerminationPointT.setTransmissionParams(objFactory.createConnectionTerminationPointTTransmissionParams(layeredParametersListT));
    return floatingTerminationPointT;
  }

  protected void fillCommonInformation(final ObjectFactory objFactory, final FloatingTerminationPointT floatingTerminationPointT, final NamingAttributesT namingAttributes) throws ProcessingFailureException {
	// TCProfile Name
    floatingTerminationPointT.setName(objFactory.createFloatingTerminationPointTName(namingAttributes));

    // discoveredName
    final String ftpNm = namingAttributes.getFtpNm();
    if (ftpNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
    }
    floatingTerminationPointT.setDiscoveredName(objFactory.createFloatingTerminationPointTDiscoveredName(ftpNm));

    // namingOS
    floatingTerminationPointT.setNamingOS(objFactory.createFloatingTerminationPointTNamingOS(OSFactory.getNmsName()));

    // source
    final SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    floatingTerminationPointT.setSource(objFactory.createFloatingTerminationPointTSource(source));

    // resource state
    final ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ResourceStateEnumT.INSTALLED);
    floatingTerminationPointT.setResourceState(objFactory.createFloatingTerminationPointTResourceState(resourceState));

    // direction
    floatingTerminationPointT.setDirection(objFactory.createFloatingTerminationPointTDirection(DirectionalityT.D_BIDIRECTIONAL));

    // edgePoint
    floatingTerminationPointT.setEdgePoint(objFactory.createFloatingTerminationPointTEdgePoint(Boolean.FALSE));
}

  protected void fillAdvaEthernetLayer (LayeredParametersListT layeredParametersListT, int moduleType) {}

  protected void fillProtection150cmLayer (LayeredParametersListT layeredParametersListT) {}

  protected void fillAdvaLagLayer (LayeredParametersListT layeredParametersListT) {}

  protected void fillProtection150cpLayer (LayeredParametersListT layeredParametersListT) {}

  protected String getMaxNumFdfrs () {
    return "1";
  }

  protected int getModuleType() {
    return 0;
  }

  public FloatingTerminationPointT toMtosiFloatingTerminationPoint(DTO<ProtectionGroupF3Attr> ftpMO, NamingAttributesT tpName) throws ProcessingFailureException {
    final ObjectFactory objFactory = new ObjectFactory();
    final FloatingTerminationPointT floatingTerminationPointT = objFactory.createFloatingTerminationPointT();
//		final FTPSPProperties ftpSPProperties = ftp.getFTPSPProperties();
    final int moduleType = getModuleType();
    NamingAttributesT namingAttribute = tpName;

    fillCommonInformation(objFactory, floatingTerminationPointT,namingAttribute);

    // layers
    final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_LAG_FRAGMENT);
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_LAG);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_LAG,
        LayeredParams.LrLag.ALLOCATED_NUMBER_PARAM, "2");

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_LAG,
        LayeredParams.LrLag.ALLOCATION_MAXIMUM_PARAM, "2");

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_LAG,
        LayeredParams.LrLag.FRAGMENT_SERVER_LAYER_PARAM, LayeredParams.LR_ETHERNET);
    // -------end of Layer-------

    fillProtection150cpLayer(layeredParametersListT);

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
        LayeredParams.LrEthernet.CONNECTIONLESS_PORT_PARAM, MtosiConstants.TRUE);

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
        LayeredParams.LrEthernet.INTERFACE_TYPE_PARAM, "NNI");

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
        LayeredParams.LrEthernet.PORT_TP_ROLE_STATE_PARAM, "internal");

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
        LayeredParams.LrEthernet.MAX_NUM_FDFRS_PARAM, getMaxNumFdfrs());

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
        LayeredParams.LrEthernet.NUM_CONFIGURED_FDFRS_PARAM, "0");//Integer.toString(ftpSPProperties.get(FTPSPProperties.VI.NumOfFDFrs)));
    // -------end of Layer-------

    fillAdvaLagLayer(layeredParametersListT);

    fillProtection150cmLayer(layeredParametersListT);

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET);
    fillAdvaEthernetLayer(layeredParametersListT, moduleType);
    // -------end of Layer-------

    floatingTerminationPointT.setTransmissionParams(objFactory.createConnectionTerminationPointTTransmissionParams(layeredParametersListT));
    return floatingTerminationPointT;
  }
}
