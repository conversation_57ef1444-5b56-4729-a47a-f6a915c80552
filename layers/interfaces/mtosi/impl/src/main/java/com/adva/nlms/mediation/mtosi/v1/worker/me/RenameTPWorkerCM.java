/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.RenameTPT;

import jakarta.xml.ws.Holder;

public class RenameTPWorkerCM extends RenameTPWorker {
  public RenameTPWorkerCM (RenameTPT mtosiBody, Holder<HeaderT> mtosiHeader, NamingAttributesT currentName, NetworkElement ne) {
    super(mtosiBody, mtosiHeader, currentName, ne);
  }

  @Override
  protected void mediate() throws Exception {
    if(NamingTranslationFactory.isFlow(currentName)) {
      mediateFlow();
    } else if(NamingTranslationFactory.isFtp(currentName)) {
      mediateFtp();
    } else {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The specified TP Name is for an entity type that cannot be renamed.");
    }
  }

  @Override
  protected void response () throws Exception {
    // empty method
  }

  private void mediateFlow() throws Exception {
    MTOSIFlowF3 flow = ManagedElementFactory.getCMFlow(currentName);
    if (flow == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The specified TP does not exist.");
    }

    if(!currentName.getPtpNm().equals(newName.getPtpNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The existing and new Name reference different PTPs.");
    }

    if (ManagedElementFactory.getCMFlow(newName) != null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "A CTP already exists with the specified new Name.");
    }

    String newTPName = newName.getCtpNm();
    if (newTPName == null || newTPName.length()==0) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The new TP Name has not been specified.");
    }

    String newCircuitName = NamingTranslationFactory.getRenamedFlowCircuitName(flow.getFlowSPProperties().get(FlowSPPropertiesFSP150CM.VS.CircuitName), newTPName);

    FlowSPPropertiesFSP150CM oldProps = flow.getFlowSPProperties();
    FlowSPPropertiesFSP150CM newProps = new FlowSPPropertiesFSP150CM(oldProps.get(FlowSPPropertiesFSP150CM.VI.NeIndex), oldProps.get(FlowSPPropertiesFSP150CM.VI.ShelfIndex),
            oldProps.get(FlowSPPropertiesFSP150CM.VI.SlotIndex), oldProps.get(FlowSPPropertiesFSP150CM.VI.AccPortIndex), oldProps.get(FlowSPPropertiesFSP150CM.VI.FlowIndex), newCircuitName);
    transactFlow(flow, newProps);
  }

  private void transactFlow(MTOSIFlowF3 flow, FlowSPPropertiesFSP150CM flowProps)
          throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "RenameTPWorker");
    try {
      logSecurity(ne, SystemAction.ModifyNetwork, flow.getMtosiName());
      flow.setFlowSettings(flowProps);
      NetTransactionManager.commitNetTransaction(id);
    } catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      NetTransactionManager.ensureEnd(id);
    }
  }
}
