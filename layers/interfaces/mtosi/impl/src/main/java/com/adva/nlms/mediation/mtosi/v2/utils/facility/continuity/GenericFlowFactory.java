/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */
package com.adva.nlms.mediation.mtosi.v2.utils.facility.continuity;

import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.F3FpQosPolicerAttr;
import com.adva.nlms.mediation.config.dto.attr.F3FpQosShaperAttr;
import com.adva.nlms.mediation.config.dto.attr.FlowF3Attr;
import com.adva.nlms.mediation.config.dto.attr.FlowPointAccF3Attr;
import com.adva.nlms.mediation.config.dto.attr.FlowPointF3Attr;
import com.adva.nlms.mediation.config.dto.attr.FlowPointNetF3Attr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.dto.attr.QOSAccFlowPointPolicerF3Attr;
import com.adva.nlms.mediation.config.dto.attr.QOSAccFlowPointShaperF3Attr;
import com.adva.nlms.mediation.config.dto.attr.QOSFlowPolicerAttr;
import com.adva.nlms.mediation.config.dto.attr.QOSNetFlowPointPolicerF3Attr;
import com.adva.nlms.mediation.config.dto.attr.QOSNetFlowPointShaperF3Attr;
import com.adva.nlms.mediation.config.dto.attr.QOSShaperF3Attr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.v2.utils.MtosiAddress;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;

public class GenericFlowFactory {
  private static Logger log = LogManager.getLogger(GenericFlowFactory.class);
  private static String TCP_TYPE = "TCP";
  private static String CTP_TYPE = "CTP";

  private static List ctpSupportedClasses = new ArrayList(){{
    add(FlowF3Attr.class);
    add(FlowPointAccF3Attr.class);
    add(FlowPointNetF3Attr.class);
    add(FlowPointF3Attr.class);
  }};

  private static List tcpPolicerSupportedClasses = new ArrayList(){{
    add(QOSFlowPolicerAttr.class);
    add(QOSAccFlowPointPolicerF3Attr.class);
    add(QOSNetFlowPointPolicerF3Attr.class);
    add(F3FpQosPolicerAttr.class);
  }};

  private static List tcpShaperSupportedClasses = new ArrayList(){{
    add(QOSShaperF3Attr.class);
    add(QOSAccFlowPointShaperF3Attr.class);
    add(QOSNetFlowPointShaperF3Attr.class);
    add(F3FpQosShaperAttr.class);
  }};

  @SuppressWarnings("unchecked")
  public static <T extends ManagedObjectAttr> BaseFlowForContinuityTest getFlow(MtosiMOFacade facade, int neId, String mtosiName){
    DTO<T> flow = null;
    boolean flowFound=false;
    for(Object tClass  : ctpSupportedClasses){
      DTO<T> tempFlow = facade.findDTOViaMtosiName(neId, mtosiName,(Class<T>)tClass);
      if(flowFound && tempFlow != null){
        log.warn("Flow name " + mtosiName + " is not unique. First Port found will be returned...");
      }else if(!flowFound && tempFlow != null){
        flow = tempFlow;
        flowFound = true;
      }
    }
    return createBaseFlowContinuityTestFromDTO(flow, neId);
  }

  public static <T extends ManagedObjectAttr> DTO<T> getFlowDTO(MtosiMOFacade facade, MtosiAddress mtosiAddress){
    DTO<T> flow = null;
    boolean flowFound=false;
    String mtosiName = getMtosiName(mtosiAddress, CTP_TYPE);
    for(Object tClass  : ctpSupportedClasses){
      DTO<T> tempFlow = facade.findDTOViaMtosiName(mtosiAddress.getNE().getID(), mtosiName,(Class<T>)tClass);
      if(flowFound && tempFlow != null){
        log.warn("Flow name " + mtosiName + " is not unique. First Port found will be returned...");
      }else if(!flowFound && tempFlow != null){
        flow = tempFlow;
        flowFound = true;
      }
    }
    return flow;
  }

  public static DTO<ManagedObjectAttr> getPolicerDTO(MtosiMOFacade facade, MtosiAddress mtosiAddress) {
    DTO<ManagedObjectAttr> flow = null;
    boolean flowFound=false;
    String mtosiName = getMtosiName(mtosiAddress, TCP_TYPE);
    for(Object tClass  : tcpPolicerSupportedClasses){
      DTO<ManagedObjectAttr> tempFlow = facade.findDTOViaMtosiName(mtosiAddress.getNE().getID(), mtosiName,(Class<ManagedObjectAttr>)tClass);
      if(flowFound && tempFlow != null){
        log.warn("Policer name " + mtosiName + " is not unique. First Policer found will be returned...");
      }else if(!flowFound && tempFlow != null){
        flow = tempFlow;
        flowFound = true;
      }
    }
    return flow;
  }

  public static DTO<ManagedObjectAttr> getShaperDTO(MtosiMOFacade facade, MtosiAddress mtosiAddress) {
    DTO<ManagedObjectAttr> flow = null;
    boolean flowFound=false;
    String mtosiName = getMtosiName(mtosiAddress, TCP_TYPE);
    for(Object tClass  : tcpShaperSupportedClasses){
      DTO<ManagedObjectAttr> tempFlow = facade.findDTOViaMtosiName(mtosiAddress.getNE().getID(), mtosiName,(Class<ManagedObjectAttr>)tClass);
      if(flowFound && tempFlow != null){
        log.warn("Shaper name " + mtosiName + " is not unique. First Shaper found will be returned...");
      }else if(!flowFound && tempFlow != null){
        flow = tempFlow;
        flowFound = true;
      }
    }
    return flow;
  }

  private static <T extends ManagedObjectAttr> BaseFlowForContinuityTest createBaseFlowContinuityTestFromDTO(DTO<T> flow, int neId){
    if(flow != null) {
      if (flow.getAttributesGroupClass().equals(FlowF3Attr.class)) {
        return new FlowF3Cont(neId, (DTO<FlowF3Attr>) flow);
      } else if (flow.getAttributesGroupClass().equals(FlowPointAccF3Attr.class)) {
        return new FlowPointAccCont(neId, (DTO<FlowPointAccF3Attr>) flow);
      }else if (flow.getAttributesGroupClass().equals(FlowPointNetF3Attr.class)) {
        return new FlowPointNetCont(neId, (DTO<FlowPointNetF3Attr>) flow);
      }else if (flow.getAttributesGroupClass().equals(FlowPointF3Attr.class)) {
        return new FlowPointEthCont(neId, (DTO<FlowPointF3Attr>) flow);
      }
    }
    return null;
  }

  private static String getMtosiName(MtosiAddress mtosiAddress, String type){
    String mtosiTpName = mtosiAddress.getHasFtp() && mtosiAddress.getFtpName().matches("/lag=\\d+") ? mtosiAddress.getFtpName() : mtosiAddress.getPtpName();
    if(type.equals(TCP_TYPE))
      return mtosiTpName + " && " + mtosiAddress.getCtpName() + " && " + mtosiAddress.getTcpProfileName();
    else
      return mtosiTpName + " && " + mtosiAddress.getCtpName();
  }
}
