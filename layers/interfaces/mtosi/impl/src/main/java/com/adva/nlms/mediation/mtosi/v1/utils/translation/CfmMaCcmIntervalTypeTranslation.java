/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

/**
 * Created by IntelliJ IDEA. User: mariuszg Date: 2007-06-25 Time: 10:13:25 To change this template use File | Settings
 * | File Templates.
 */
public enum CfmMaCcmIntervalTypeTranslation
{
  INTERVAL_INVALID (0, "IntervalInvalid"),
  INTERVAL_3MS     (1, "Interval3.3ms"),
  INTERVAL_10MS    (2, "Interval10ms"),
  INTERVAL_100MS   (3, "Interval100ms"),
  INTERVAL_1S      (4, "Interval1s"),
  INTERVAL_10S     (5, "Interval10s"),
  INTERVAL_1MIN    (6, "Interval1min"),
  INTERVAL_10MIN   (7, "Interval10min"),
  NOT_APPLICABLE   (8, "n/a");



  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;
  //------------------------------------------------------------------------------------------------------------------

  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private CfmMaCcmIntervalTypeTranslation (final int mibValue, final String mtosiString)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    
    return mibValue;
  }

  public static int getMIBValue(final String mtosiString) {
    int cfmTypeTranslation = 8;
    
    for (CfmMaCcmIntervalTypeTranslation tmpCfmTypeTranslation : values())
    {
      if (mtosiString.equals(tmpCfmTypeTranslation.getMtosiString()))
      {
        cfmTypeTranslation = tmpCfmTypeTranslation.getMIBValue();
        break;
      }
    }
    
    return cfmTypeTranslation;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int mibValue)
  {
    CfmMaCcmIntervalTypeTranslation cfmTypeTranslation = NOT_APPLICABLE;  // the return value

    for (CfmMaCcmIntervalTypeTranslation tmpCfmTypeTranslation : values())
    {
      if (mibValue == tmpCfmTypeTranslation.getMIBValue())
      {
        cfmTypeTranslation = tmpCfmTypeTranslation;
        break;
      }
    }
    return cfmTypeTranslation.getMtosiString();
  }

}
