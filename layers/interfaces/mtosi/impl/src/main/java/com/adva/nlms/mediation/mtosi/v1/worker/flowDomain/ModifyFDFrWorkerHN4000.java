/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.MDRequestFailedException;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.FTPSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.LAGSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN40002BpmeSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000BondingSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000EthernetSProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.UNISPPropertiesHN4000;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.hn4000.FlowHN4000;
import com.adva.nlms.mediation.config.hn4000.NetworkElementHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN40002Bpme;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet2BASE_TL;
import com.adva.nlms.mediation.config.hn4000.mtosi.FDFrHN4000;
import com.adva.nlms.mediation.config.hn4000.mtosi.FTPHN4000;
import com.adva.nlms.mediation.config.mtosi.FDFr;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiFDFrMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.PMEPortPair;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.TPDataListT;
import v1.tmf854.TPDataT;
import v1.tmf854ext.adva.ModifyFDFrT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.Iterator;
import java.util.List;

//import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiSupported;

public class ModifyFDFrWorkerHN4000 extends ModifyFDFrWorker
{
	Logger LOG = LogManager.getLogger(this.getClass().getName());

	private FDFrSPPropertiesHN4000 propsFDFr = null;

	private UNISPPropertiesHN4000 eth1UniProps = null;
	private UNISPPropertiesHN4000 eth2UniProps = null;
	private UNISPPropertiesHN4000 lagUniProps = null;
	private UNISPPropertiesHN4000 bondedUniProps = null;

	// MO Objects
	private FTPHN4000 ftp = null;
	private FlowHN4000 flowBond = null;
	private FlowHN4000 flowUplink = null;
	private PortHN4000Ethernet eth1 = null;
	private PortHN4000Ethernet eth2 = null;
	private PortHN4000Ethernet2BASE_TL bondedPort = null;

	// tpData objects
	private TPDataT tpDataLag = null;
	private TPDataT tpDataFlowBond = null;
	private TPDataT tpDataFlowUplink = null;
	private TPDataT tpDataEth1 = null;
	private TPDataT tpDataEth2 = null;
	private TPDataT tpDataBonded = null;
	private List<TPDataT> tpDataPMEList = null;

	// properties objects
	private FTPSPProperties propsFTP = null;
	private FlowSPPropertiesHN4000 propsFlowBond = null;
	private FlowSPPropertiesHN4000 propsFlowUplink = null;
	private PortHN4000EthernetSProperties propsEth1 = null;
	private PortHN4000EthernetSProperties propsEth2 = null;
	private PortHN4000BondingSPProperties propsBondedPort = null;

	private List<PMEPortPair> pmePairList = null;

	//private boolean isABonded = true;
	private boolean isLag = true;
	//private NamingAttributesT namingFlowBond = null;
	//private NamingAttributesT namingFlowUplink = null;

	private FDFrHN4000 hnFDFr = null;

	public ModifyFDFrWorkerHN4000(ModifyFDFrT mtosiBody, Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, FDFr fdfr, NetworkElement ne)
	{
		super(mtosiBody, mtosiHeader, namingAttributes, fdfr, ne);
		hnFDFr = (FDFrHN4000) fdfr;
	}

	private void transact() throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure, MDRequestFailedException,
			ProcessingFailureException
	{
		NetworkElement locks[] = new NetworkElement[]
		{ ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "CreateAndActivateFDFrWorker");
    ne.getMTOSIWorker().setFDFrOperationInProgress(propsFDFr.get(FDFrSPProperties.VS.FDFrName), true);
    try
		{
			logSecurity(ne, SystemAction.ModifyNetwork, fdfr.getFDFrSPProperties().get(FDFrSPProperties.VS.FDFrName));
			hnFDFr.setFdfrProperties(propsFDFr);

			if (propsBondedPort != null)
			{
				if (propsBondedPort.size() > 0) {
					bondedPort.setSettings(propsBondedPort);
				}
				if (bondedUniProps != null)
				{
					if (bondedPort.hasUni())
					{
						if (bondedUniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null)
						{
							bondedPort.deleteUNI();
						} else
						{
							bondedPort.setUniProperties(bondedUniProps, bondedPort.getUni().getUniSPProperties());
						}
					} else
					{
						bondedPort.createUni(bondedUniProps);
					}
				}

			} else
			{
				if (bondedUniProps != null)
				{
					if (bondedPort.hasUni())
					{
						if (bondedUniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null)
						{
							bondedPort.deleteUNI();
						} else
						{
							bondedPort.setUniProperties(bondedUniProps, bondedPort.getUni().getUniSPProperties());
						}
					} else
					{
						bondedPort.createUni(bondedUniProps);
					}
				}

			}

			if (propsFTP != null)
			{
				ftp.setFTPSPProperties(propsFTP);
				if (lagUniProps != null)
				{
					if (ftp.getLag().hasUni())
					{
						if (lagUniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null)
						{
							ftp.getLag().deleteUNI();
						} else
						{
							ftp.getLag().setUniProperties(lagUniProps, ftp.getLag().getUni().getUniSPProperties());
						}
					} else
					{
						ftp.getLag().createUni(lagUniProps);
					}
				}
			}

			if (propsEth1 != null)
			{
				logSecurity(ne, SystemAction.ModifyNetwork, "ptpNm=" + eth1.getMtosiName());
				if (propsEth1.size() > 0) {
					eth1.setSettings(propsEth1);
				}
				if (eth1UniProps != null)
				{
					if (eth1.hasUni())
					{
						if (eth1UniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null)
						{
							eth1.deleteUNI();
						} else
						{
							eth1.setUniProperties(eth1UniProps, eth1.getUni().getUniSPProperties());
						}
					} else
					{
						eth1.createUni(eth1UniProps);
					}
				}
			}
			if (propsEth2 != null)
			{
				logSecurity(ne, SystemAction.ModifyNetwork, "ptpNm=" + eth2.getMtosiName());
				if (propsEth2.size() > 0) {
					eth2.setSettings(propsEth2);
				}
				if (eth2UniProps != null)
				{
					if (eth2.hasUni())
					{
						if (eth2UniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null)
						{
							eth2.deleteUNI();
						} else
						{
							eth2.setUniProperties(eth2UniProps, eth2.getUni().getUniSPProperties());
						}
					} else
					{
						eth2.createUni(eth2UniProps);
					}
				}
			}

			if (flowBond != null && propsFlowBond != null)
			{
				bondedPort.setFlowProperties(propsFlowBond, flowBond.getFlowSPProperties());
			}

			if (flowUplink != null && propsFlowUplink != null)
			{
				if (isLag)
				{
					ftp.getLag().setFlowProperties(propsFlowUplink, flowUplink.getFlowSPProperties());
				} else
				{
					String ptpName = tpDataFlowUplink.getTpName().getPtpNm();
					if (ptpName.contains("ETH-1"))
					{
						eth1.setFlowProperties(propsFlowUplink, flowUplink.getFlowSPProperties());
					} else
						if (ptpName.contains("ETH-2"))
						{
							eth2.setFlowProperties(propsFlowUplink, flowUplink.getFlowSPProperties());
						} else
						{
							throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
						}
				}
			}

			for (Iterator iterator = pmePairList.iterator(); iterator.hasNext();)
			{
				PMEPortPair nextPair = (PMEPortPair) iterator.next();
				PortHN40002Bpme nextPort = nextPair.getPort();
				PortHN40002BpmeSPProperties nextProps = nextPair.getProps();
				nextPort.setSettings(nextProps);
			}

			NetTransactionManager.commitNetTransaction(id);

		}
		catch (NetTransactionException e)
		{
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (ProcessingFailureException e)
		{
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (SPValidationException e)
		{
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (SNMPCommFailure e)
		{
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (MDRequestFailedException e)
		{
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		finally
		{
      ne.getMTOSIWorker().setFDFrOperationInProgress(propsFDFr.get(FDFrSPProperties.VS.FDFrName), false);
			NetTransactionManager.ensureEnd(id);
		}
	}

	@Override
  protected void mediate() throws Exception
	{

		NetworkElementHN4000 hnNE = (NetworkElementHN4000) ne;
		boolean hn400 = hnNE.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400;

		if (tpsToModify != null)
		{
			if (!MtosiTPMediator.checkTPToModifySameNE(ne, tpsToModify))
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
						"The specified TPs must be on the same Network Element.");
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}

			tpDataBonded = MtosiTPMediator.getTPDataTForHNBonded(tpsToModify);
			tpDataLag = MtosiTPMediator.getTPDataTForHNFTP(tpsToModify);
			tpDataFlowBond = MtosiTPMediator.getTPDataTForHNFlowBonding(tpsToModify);
			tpDataFlowUplink = MtosiTPMediator.getTPDataTForHNFlowUplink(tpsToModify);
			tpDataEth1 = MtosiTPMediator.getTPDataTForHNPhysicalEth(tpsToModify, 1);
			tpDataEth2 = MtosiTPMediator.getTPDataTForHNPhysicalEth(tpsToModify, 2);
			tpDataPMEList = MtosiTPMediator.getTPDataListForHN2BPME(tpsToModify);
		} else
		{
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NAME_MISSING);
		}

		if (tpDataLag != null)
		{
			ftp = (FTPHN4000) ManagedElementFactory.getFtp(tpDataLag.getTpName());
			if (ftp == null)
			{
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
			}
			propsFTP = MtosiTPMediator.mtosiTPDataTToHN4000FTPProperties(tpDataLag, tpDataLag.getTpName().getFtpNm());
		}

		if (tpDataFlowBond != null)
		{
			propsFlowBond = MtosiTPMediator.mtosiTPDataTToHNCTPProperties(tpDataFlowBond, null);
			bondedPort = hnNE.getBondedPort(tpDataFlowBond.getTpName().getFtpNm());
			if (bondedPort == null)
			{
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
			}
			flowBond = getFirst(bondedPort.getFlowsByName(tpDataFlowBond.getTpName().getCtpNm())); //TODONE LARRY
			if (flowBond == null)
			{
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
			}

		}
		if (tpDataFlowUplink != null)
		{
			propsFlowUplink = MtosiTPMediator.mtosiTPDataTToHNCTPProperties(tpDataFlowUplink, null);
			computeUplink();
			if (isLag)
			{
				ftp = (FTPHN4000) hnNE.getMTOSIWorker().getFTP(tpDataFlowUplink.getTpName().getFtpNm());
				if (ftp == null)
				{
					throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
				}
				flowUplink = getFirst(ftp.getLag().getFlowsByName(tpDataFlowUplink.getTpName().getCtpNm())); //TODONE LARRY
				if (flowUplink == null)
				{
					throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
				}
			} else
			{
				String ptpName = tpDataFlowUplink.getTpName().getPtpNm();
				if (ptpName.contains("ETH-1"))
				{
					eth1 = (PortHN4000Ethernet) ManagedElementFactory.getPort(tpDataFlowUplink.getTpName());
					flowUplink = getFirst(eth1.getFlowsByName(tpDataFlowUplink.getTpName().getCtpNm()));//TODONE LARRY
					if (flowUplink == null)
					{
						throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
					}
				} else
					if (ptpName.contains("ETH-2"))
					{
						eth2 = (PortHN4000Ethernet) ManagedElementFactory.getPort(tpDataFlowUplink.getTpName());
						flowUplink = getFirst(eth2.getFlowsByName(tpDataFlowUplink.getTpName().getCtpNm())); //TODONE LARRY
						if (flowUplink == null)
						{
							throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
						}
					} else
					{
						throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
					}
			}
		} 
		
		if (tpDataEth1 != null)
		{
			if (eth1 == null)
			{
				Port port = ManagedElementFactory.getPort(tpDataEth1.getTpName());
				eth1 = (PortHN4000Ethernet) port;
				if (eth1 == null)
				{
					throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
				}
			}

			propsEth1 = MtosiTPMediator.mtosiTPDataTToHNEthernetProperties(tpDataEth1, eth1, hn400);
			NetworkElement ne = eth1.getNE();
			eth1UniProps = MtosiTPMediator.mtosiTPDataTToHNUniProperties(tpDataEth1, ne.getMTOSIWorker().getNetworkElementTypeForMTOSI(), eth1.hasUni());
			if (eth1UniProps != null && eth1.hasUni())
			{
				UNISPPropertiesHN4000 props = eth1.getUni().getUniSPProperties();
				eth1UniProps.set(UNISPPropertiesHN4000.VL.DeviceId, props.get(UNISPPropertiesHN4000.VL.DeviceId));
				eth1UniProps.set(UNISPPropertiesHN4000.VL.PortId, props.get(UNISPPropertiesHN4000.VL.PortId));
			}
		}
		if (tpDataEth2 != null)
		{
			if (eth2 == null)
			{
				Port port = ManagedElementFactory.getPort(tpDataEth2.getTpName());
				eth2 = (PortHN4000Ethernet) port;
				if (eth2 == null)
				{
					throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
				}
			}

			propsEth2 = MtosiTPMediator.mtosiTPDataTToHNEthernetProperties(tpDataEth2, eth2, hn400);
			NetworkElement ne = eth2.getNE();
			eth2UniProps = MtosiTPMediator.mtosiTPDataTToHNUniProperties(tpDataEth2, ne.getMTOSIWorker().getNetworkElementTypeForMTOSI(), eth2.hasUni());
			if (eth2UniProps != null && eth2.hasUni())
			{
				UNISPPropertiesHN4000 props = eth2.getUni().getUniSPProperties();
				eth2UniProps.set(UNISPPropertiesHN4000.VL.DeviceId, props.get(UNISPPropertiesHN4000.VL.DeviceId));
				eth2UniProps.set(UNISPPropertiesHN4000.VL.PortId, props.get(UNISPPropertiesHN4000.VL.PortId));
			}
		}
		if (tpDataBonded != null)
		{
			if (bondedPort == null)
			{
				bondedPort = hnNE.getBondedPort(tpDataBonded.getTpName().getFtpNm());
				if (bondedPort == null)
				{
					throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
				}

			}

			propsBondedPort = MtosiTPMediator.mtosiTPDataTToHN4000FTPBondProperties(tpDataBonded, null, hn400, bondedPort.getPortSPProperties(), true);
			NetworkElement ne = bondedPort.getNE();
			bondedUniProps = MtosiTPMediator.mtosiTPDataTToHNUniProperties(tpDataBonded, ne.getMTOSIWorker().getNetworkElementTypeForMTOSI(), bondedPort
					.hasUni());
			if (bondedUniProps != null && bondedPort.hasUni())
			{
				UNISPPropertiesHN4000 props = bondedPort.getUni().getUniSPProperties();
				bondedUniProps.set(UNISPPropertiesHN4000.VL.DeviceId, props.get(UNISPPropertiesHN4000.VL.DeviceId));
				bondedUniProps.set(UNISPPropertiesHN4000.VL.PortId, props.get(UNISPPropertiesHN4000.VL.PortId));
			}
		}
		if (tpDataLag != null)
		{
			if (ftp == null)
			{
				ftp = (FTPHN4000) hnNE.getMTOSIWorker().getFTP(tpDataLag.getTpName().getFtpNm());
				if (ftp == null)
				{
					throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
				}
			}

			propsFTP = MtosiTPMediator.mtosiTPDataTToHN4000FTPProperties(tpDataLag, null, (LAGSPPropertiesHN4000) ftp
					.getFTPSPProperties());
			NetworkElement ne = ftp.getLag().getNE();
			lagUniProps = MtosiTPMediator.mtosiTPDataTToHNUniProperties(tpDataLag, ne.getMTOSIWorker().getNetworkElementTypeForMTOSI(), ftp.getLag().hasUni());
			if (lagUniProps != null && ftp.getLag().hasUni())
			{
				UNISPPropertiesHN4000 props = ftp.getLag().getUni().getUniSPProperties();
				lagUniProps.set(UNISPPropertiesHN4000.VL.DeviceId, props.get(UNISPPropertiesHN4000.VL.DeviceId));
				lagUniProps.set(UNISPPropertiesHN4000.VL.PortId, props.get(UNISPPropertiesHN4000.VL.PortId));
			}

		}
		if (tpDataPMEList != null)
		{
			pmePairList = MtosiTPMediator.mtosiTPDataListToPMEPortPair(tpDataPMEList, hn400);
		}

		propsFDFr = (FDFrSPPropertiesHN4000) fdfr.getFDFrSPProperties();
		propsFDFr = MtosiFDFrMediator.mtosiModifyFDFRToFDFRSPProperties4000(propsFDFr, modifyData);

		transact();
	}

	/**
	 * It is technically possible to have 2 flows with the same name on the same port,
	 * but we will be preventing MTOSI from creating these anomalies.
	 * Therefore, it is OK, to grab the first Flow from the list.
	 * @param flowsByName
	 * @return
	 */
	private FlowHN4000 getFirst(List<FlowHN4000> flowsByName) {
		if (flowsByName == null || flowsByName.isEmpty()) {
			return null;
		} else {
			return flowsByName.get(0);
		}
	}

	private void computeUplink()
	{
		if (propsFlowUplink == null)
			return;
		if (NamingTranslationFactory.isFlowLag(tpDataFlowUplink.getTpName()))
		{
			isLag = true;
		} else
		{
			isLag = false;
		}

	}

	@Override
  protected TPDataListT getUpdatedTPs() throws ProcessingFailureException
	{
		ObjectFactory objectFactory = new ObjectFactory();
		TPDataListT tpsToModify = objectFactory.createTPDataListT();

		if (tpDataEth1 != null && eth1 != null)
		{
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataEth1.getTpName())).toMtosiPTPasTPDataT());
//			tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataEth1.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
		if (tpDataEth2 != null && eth2 != null)
		{
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataEth2.getTpName())).toMtosiPTPasTPDataT());
//			tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataEth2.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
		if (tpDataBonded != null && bondedPort != null)
		{
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ManagedElementFactory.getHN4000Ftp(tpDataBonded.getTpName())).toMtosiFTPasTPDataT());
//			tpsToModify.getTpData().add(ManagedElementFactory.getHN4000Ftp(tpDataBonded.getTpName()).getMtosiTranslator().toMtosiFTPasTPDataT());
		}
		if (tpDataLag != null && ftp != null)
		{
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ManagedElementFactory.getHN4000Ftp(tpDataLag.getTpName())).toMtosiFTPasTPDataT());
//			tpsToModify.getTpData().add(ManagedElementFactory.getHN4000Ftp(tpDataLag.getTpName()).getMtosiTranslator().toMtosiFTPasTPDataT());
		}
		if (tpDataFlowUplink != null && flowUplink != null)
		{
			FlowHN4000 newFlow = ManagedElementFactory.getHNFlow(tpDataFlowUplink.getTpName());
			Object supported =newFlow;
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(supported).toMtosiCTPasTPDataT());
//			tpsToModify.getTpData().add(supported.getMtosiTranslator().toMtosiCTPasTPDataT());
		}
		if (tpDataFlowBond != null && flowBond != null)
		{
			FlowHN4000 newFlow = ManagedElementFactory.getHNFlow(tpDataFlowBond.getTpName());
			Object supported =  newFlow;
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(supported).toMtosiCTPasTPDataT());
//			tpsToModify.getTpData().add(supported.getMtosiTranslator().toMtosiCTPasTPDataT());
		}
		if (tpDataPMEList != null)
		{
			for (Iterator iterator = tpDataPMEList.iterator(); iterator.hasNext();)
			{
				TPDataT nextTP = (TPDataT) iterator.next();
				tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(nextTP.getTpName())).toMtosiPTPasTPDataT());
//				tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(nextTP.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
			}
		}

		return tpsToModify;
	}
}