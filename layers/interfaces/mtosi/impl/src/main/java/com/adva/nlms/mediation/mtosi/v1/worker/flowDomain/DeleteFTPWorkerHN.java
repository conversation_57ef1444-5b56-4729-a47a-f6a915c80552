/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN40002BpmeSPProperties;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.hn4000.NetworkElementHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN40002Bpme;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet2BASE_TL;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.PMEPortPair;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.FTPVendorExtensionsT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.TPDataT;
import v1.tmf854ext.adva.DeleteFTPT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.ws.Holder;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class DeleteFTPWorkerHN extends DeleteFTPWorker
{
	Logger LOG = LogManager.getLogger(this.getClass().getName());

	protected DeleteFTPT mtosiBody;
	

	public DeleteFTPWorkerHN(Holder<HeaderT> mtosiHeader, DeleteFTPT mtosiBody, NamingAttributesT namingAttributes, NetworkElement ne)
	{
		super(mtosiHeader, namingAttributes, ne);
		this.mtosiBody = mtosiBody;
	}
	
	@Override
  protected void mediate() throws Exception
	{
		JAXBElement<FTPVendorExtensionsT> extensionsJAXB = mtosiBody.getVendorExtensions();
		
		List<PMEPortPair> pmePairList = new ArrayList<PMEPortPair>();
		if(extensionsJAXB!=null)
		{
			FTPVendorExtensionsT vendorExtensions = extensionsJAXB.getValue();
			if(vendorExtensions!=null)
			{
				boolean hn400 = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400;
				List<TPDataT> tpDataList = MtosiUtils.getTpsToModify(vendorExtensions);
				pmePairList = MtosiTPMediator.mtosiTPDataListToPMEPortPair(tpDataList, hn400);
			}
			
		}
		
		
		transact(pmePairList);
	}

	private void transact(List<PMEPortPair> pmePairList) throws ObjectInUseException, MDOperationFailedException, NetTransactionException, SPValidationException, SNMPCommFailure, ProcessingFailureException
	{
		NetworkElement locks[] = new NetworkElement[]
		{ ne };
		Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "DeleteFTPWorker");

		try
		{
			logSecurity(ne, SystemAction.DeleteNetwork, "ftpNm=" + ftpName);
			if (ftpName.indexOf("port=ETH") > 0)
			{
				NetworkElementHN4000 ne4000 = (NetworkElementHN4000) ne;
				PortHN4000Ethernet2BASE_TL bondedPort = ne4000.getBondedPort(ftpName);
				if(bondedPort==null)
				{
					throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, "The specified FTP does not exist.");
					
				}
				if(bondedPort.hasUni())
				{
					bondedPort.deleteUNI();
				}
				ne4000.deleteBonding(bondedPort.getPortSPProperties());
				for (Iterator iterator = pmePairList.iterator(); iterator.hasNext();)
				{
					PMEPortPair nextPair = (PMEPortPair) iterator.next();
					PortHN40002Bpme nextPort=nextPair.getPort();
					PortHN40002BpmeSPProperties nextProps = nextPair.getProps();
					nextPort.setSettings(nextProps);
				}
				
			}
			else if (ftpName.indexOf("port=LAG") > 0)
			{
				
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, "Deletion of LAG FTP is not supported.");
				
			}
			else
			{
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, "The specified FTP does not exist.");
			}

			NetTransactionManager.commitNetTransaction(id);
      ne.logSROperation(SROperationState.FTP_DELETION_SUCCESS, ftpName);
		}
		catch (NetTransactionException e)
		{
			ne.logSROperation(SROperationState.FTP_DELETION_FAILURE, ftpName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (SPValidationException e)
		{
			ne.logSROperation(SROperationState.FTP_DELETION_FAILURE, ftpName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (SNMPCommFailure e)
		{
			ne.logSROperation(SROperationState.FTP_DELETION_FAILURE, ftpName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (ProcessingFailureException e)
		{
			ne.logSROperation(SROperationState.FTP_DELETION_FAILURE, ftpName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
    catch (MDOperationFailedException e) {
      ne.logSROperation(SROperationState.FTP_DELETION_FAILURE, ftpName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
		finally
		{
			NetTransactionManager.ensureEnd(id);
		}
	}

}