/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;

import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.cfm.CreateMaintenanceAssociationWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.cfm.CreateMaintenanceDomainWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.cfm.CreateMaintenanceEndpointWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.cfm.DeleteMaintenanceAssociationWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.cfm.DeleteMaintenanceDomainWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.cfm.DeleteMaintenanceEndpointWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.cfm.GetMaintenanceAssociationWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.cfm.GetMaintenanceDomainWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.cfm.GetMaintenanceEndpointWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.cfm.ModifyMaintenanceEndpointWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854ext.adva.CreateMaintenanceAssociationResponseT;
import v1.tmf854ext.adva.CreateMaintenanceAssociationT;
import v1.tmf854ext.adva.CreateMaintenanceDomainResponseT;
import v1.tmf854ext.adva.CreateMaintenanceDomainT;
import v1.tmf854ext.adva.CreateMaintenanceEndpointResponseT;
import v1.tmf854ext.adva.CreateMaintenanceEndpointT;
import v1.tmf854ext.adva.DeleteMaintenanceAssociationResponseT;
import v1.tmf854ext.adva.DeleteMaintenanceAssociationT;
import v1.tmf854ext.adva.DeleteMaintenanceDomainResponseT;
import v1.tmf854ext.adva.DeleteMaintenanceDomainT;
import v1.tmf854ext.adva.DeleteMaintenanceEndpointResponseT;
import v1.tmf854ext.adva.DeleteMaintenanceEndpointT;
import v1.tmf854ext.adva.GetMaintenanceAssociationResponseT;
import v1.tmf854ext.adva.GetMaintenanceAssociationT;
import v1.tmf854ext.adva.GetMaintenanceDomainResponseT;
import v1.tmf854ext.adva.GetMaintenanceDomainT;
import v1.tmf854ext.adva.GetMaintenanceEndpointResponseT;
import v1.tmf854ext.adva.GetMaintenanceEndpointT;
import v1.tmf854ext.adva.ModifyMaintenanceEndpointResponseT;
import v1.tmf854ext.adva.ModifyMaintenanceEndpointT;
import ws.v1.tmf854.ProcessingFailureException;
import ws.v1.tmf854ext.adva.ADVAConnectivityFaultMaintenance;

import jakarta.annotation.Resource;
import jakarta.xml.ws.Holder;
import jakarta.xml.ws.WebServiceContext;

/**
 * This class was generated by the CXF 2.0-incubator-RC Thu Jun 28 08:59:00 CEST
 * 2007 Generated source version: 2.0-incubator-RC
 *
 */
@jakarta.jws.WebService(name = "ADVAConnectivityFaultMaintenance", serviceName = "ADVAConfigurationService", portName = "ADVAConnectivityFaultMaintenanceHttp", targetNamespace = "adva.tmf854ext.v1.ws", endpointInterface = "ws.v1.tmf854ext.adva.ADVAConnectivityFaultMaintenance")
public class ADVAConnectivityFaultMaintenanceImpl implements ADVAConnectivityFaultMaintenance
{
	private static final Logger LOG = LogManager.getLogger(ADVAConnectivityFaultMaintenanceImpl.class.getPackage().getName());
	@Resource
	private WebServiceContext context;


  @Override
  public GetMaintenanceEndpointResponseT getMaintenanceEndpoint(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, GetMaintenanceEndpointT mtosiBody) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetMaintenanceEndpointWorker.class, mtosiBody, mtosiHeader,
        "getMaintenanceEndpoint", context, LOG);
  }

  @Override
  public DeleteMaintenanceAssociationResponseT deleteMaintenanceAssociation(Holder<HeaderT> mtosiHeader, DeleteMaintenanceAssociationT mtosiBody) throws ProcessingFailureException {
    return ServiceUtils.runMethod(DeleteMaintenanceAssociationWorker.class, mtosiBody, mtosiHeader,
        "deleteMaintenanceAssociation", context, LOG);
  }

  @Override
  public DeleteMaintenanceDomainResponseT deleteMaintenanceDomain(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, DeleteMaintenanceDomainT mtosiBody) throws ProcessingFailureException {
    return ServiceUtils.runMethod(DeleteMaintenanceDomainWorker.class, mtosiBody, mtosiHeader,
        "deleteMaintenanceDomain", context, LOG);
  }

  @Override
  public GetMaintenanceAssociationResponseT getMaintenanceAssociation(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, GetMaintenanceAssociationT mtosiBody) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetMaintenanceAssociationWorker.class, mtosiBody, mtosiHeader,
        "getMaintenanceAssociation", context, LOG);
  }

  @Override
  public CreateMaintenanceDomainResponseT createMaintenanceDomain(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, CreateMaintenanceDomainT mtosiBody) throws ProcessingFailureException {
    return ServiceUtils.runMethod(CreateMaintenanceDomainWorker.class, mtosiBody, mtosiHeader,
        "createMaintenanceDomain", context, LOG);
  }

  @Override
  public GetMaintenanceDomainResponseT getMaintenanceDomain(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, GetMaintenanceDomainT mtosiBody) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetMaintenanceDomainWorker.class, mtosiBody, mtosiHeader,
        "getMaintenanceDomain", context, LOG);
  }

  @Override
  public CreateMaintenanceAssociationResponseT createMaintenanceAssociation(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, CreateMaintenanceAssociationT mtosiBody) throws ProcessingFailureException {
    return ServiceUtils.runMethod(CreateMaintenanceAssociationWorker.class, mtosiBody, mtosiHeader,
        "createMaintenanceAssociation", context, LOG);
  }

  @Override
  public ModifyMaintenanceEndpointResponseT modifyMaintenanceEndpoint(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, ModifyMaintenanceEndpointT mtosiBody) throws ProcessingFailureException {
    return ServiceUtils.runMethod(ModifyMaintenanceEndpointWorker.class, mtosiBody, mtosiHeader,
        "modifyMaintenanceEndpoint", context, LOG);
  }

  @Override
  public CreateMaintenanceEndpointResponseT createMaintenanceEndpoint(Holder<HeaderT> mtosiHeader, CreateMaintenanceEndpointT mtosiBody) throws ProcessingFailureException {
    return ServiceUtils.runMethod(CreateMaintenanceEndpointWorker.class, mtosiBody, mtosiHeader,
        "createMaintenanceEndpoint", context, LOG);
  }

  @Override
  public DeleteMaintenanceEndpointResponseT deleteMaintenanceEndpoint(Holder<HeaderT> mtosiHeader, DeleteMaintenanceEndpointT mtosiBody) throws ProcessingFailureException {
        return ServiceUtils.runMethod(DeleteMaintenanceEndpointWorker.class, mtosiBody, mtosiHeader,
        "deleteMaintenanceEndpoint", context, LOG);
  }
}
