/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils;

import com.adva.nlms.common.NEUtils;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.common.config.EntityType;
import com.adva.nlms.mediation.config.ConfigCtrlImpl;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.attr.FlowF3Attr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3AccAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3NetAttr;
import com.adva.nlms.mediation.config.dto.attr.ProtectionGroupF3Attr;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FTPFSP150CM;
import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.mtosi.common.mtosisupport.ManagedElementTypes;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.MtosiInventoryType;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.NamingAttributesT;
import ws.v1.tmf854.ProcessingFailureException;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Address Conversion class
 * Convertes MTOSI addresses (NamingAttribute_T) into the address scheme of the MO-Layer (xxxSPProperty)
 *
 * <AUTHOR>
 */
public class MtosiAddress {

  // mapping table for NMS-slot-numbers to MTOSI-names (for fixed slots only)
  private static Map<Integer, String[]> fixedSlotNbr2MtosiName = new  HashMap<Integer, String[]>();
  private static Map<Integer, String> slotNamePatterns = new  HashMap<Integer, String>();
  private static final String patternPSU =  "/shelf=(1)/slot=PSU-([1-2])";
  private static final String patternFAN =  "/shelf=(1)/slot=FAN-([1-2])";
  private static final String patternMOD =  "/shelf=(1)/slot=\\d+";

  static {
    // initialize fixedSlotNbr2MtosiName
    // FSP150CP
    fixedSlotNbr2MtosiName.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP, new String[] {
      // 1=slot-1; 0=unused
      null, "1"
    });
    // EFM
    fixedSlotNbr2MtosiName.put(NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM, new String[] {
      // 2=slot-1; 0,1=unused
      null, null, "1"
    });
    // FSP150CP-MR
    fixedSlotNbr2MtosiName.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR, new String[] {
      // 2=slot-1; 0,1=unused
      null, null, "1"
    });
    // FSP150CM
    fixedSlotNbr2MtosiName.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM, new String[] {
      // 1=SCU; 2..17=slot-1..16; 18,19=PSU-1/2; 20,21=FAN-1/2; 0
      null, MtosiConstants.EQUIPMENT_SCU,
      "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16",
      MtosiConstants.SLOT_PSU1, MtosiConstants.SLOT_PSU2,
      MtosiConstants.SLOT_FAN1, MtosiConstants.SLOT_FAN2
    });
    // FSP150CC-GE206
    fixedSlotNbr2MtosiName.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206, new String[] {
      // 1=slot-1; 2,3=PSU-1/2 5.4 softw version; 4,5=PSU-1/2 5.2 softw version; 0=unused
      null, "1",  MtosiConstants.SLOT_PSU1, MtosiConstants.SLOT_PSU2, MtosiConstants.SLOT_PSU1, MtosiConstants.SLOT_PSU2
    });
    // FSP150CC-GE201
    fixedSlotNbr2MtosiName.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201, new String[] {
      // 1=slot-1;  2,3=PSU-1/2 5.4 softw version; 4,5=PSU-1/2 5.2 softw version; 0=unused
      null, "1", MtosiConstants.SLOT_PSU1, MtosiConstants.SLOT_PSU2, MtosiConstants.SLOT_PSU1, MtosiConstants.SLOT_PSU2
    });
    // FSP150CC-GE201SE
    fixedSlotNbr2MtosiName.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE, new String[] {
      // 1=slot-1; 2,3=PSU-1/2 5.4 softw version; 4,5=PSU-1/2 5.2 softw version; 0=unused
      null, "1",  MtosiConstants.SLOT_PSU1, MtosiConstants.SLOT_PSU2, MtosiConstants.SLOT_PSU1, MtosiConstants.SLOT_PSU2
    });
    // HN400
    fixedSlotNbr2MtosiName.put(NeTypeIds.NETWORK_ELEMENT_TYPE_HN400, new String[] {
      // 1=slot-1; 0=unused
      null, "1"
    });
    // HN4000
    fixedSlotNbr2MtosiName.put(NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000, new String[] {
      // 1..2=slot-1..2; 0=unused
      null, "1", "2"
    });

    // initialize slotNamePatterns
    // FSP150CP
    slotNamePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP,
      "(/shelf=1/slot=(PSU-[12]|FAN-[12])" +
      "|/shelf=1/slot=1/sub_slot=(NET-[AB]|ACC)" +
      ")" );
    // FSP150CP-MR
    slotNamePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR,
      "(/shelf=1/slot=(PSU-[12]|FAN-[12])" +
      "|/shelf=1/slot=1/sub_slot=(NET-[12]|ACC)" +
      ")" );
    // EFM
    slotNamePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM,
      "(/shelf=1/slot=(PSU-[12]|FAN-[12])" +
      "|/shelf=1/slot=1/sub_slot=(NET-[12]|ACC)" +
      ")" );
    // FSP150CM
    slotNamePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM,
      "(/shelf=1/slot=(SCU|PSU-[12]|FAN-[12])" +
      "|/shelf=1/slot=(1|2|3|4|5|6|7|8|9|10|11|12|13|14|15|16)/sub_slot=(NET-[12]|ACC)" +
      ")" );
    // FSP150CC-GE206
    slotNamePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206,
      "(/shelf=1/slot=PSU-[12]" +
      "|/shelf=1/slot=1/sub_slot=(NET-[12]|ACC-[456])" +
      ")" );
    // FSP150CC-GE201
    slotNamePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201,
      "(/shelf=1/slot=PSU-[12]" +
      "|/shelf=1/slot=1/sub_slot=(NET-[12]|ACC)" +
      ")" );
    // FSP150CC-GE201SE
    slotNamePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE,
      "(/shelf=1/slot=PSU-[12]" +
      "|/shelf=1/slot=1/sub_slot=(NET-[12]|ACC)" +
      ")" );
    // HN400
    slotNamePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_HN400,
      "(/shelf=1/slot=(PSU-[AB]|FAN)" +
      //"|/shelf=1/slot=1/sub_slot=(ETH-[12]|2BPME-[1-8])" +
      ")" );
    // HN4000
    slotNamePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000,
      "(/shelf=[1-5]/slot=(PSU-[AB]|FAN)" +
      //"|/shelf=[1-5]/slot=1/sub_slot=2BPME-[1-9][0-9]?" +
      "|/shelf=[1-5]/slot=2/sub_slot=ETH-[12]" +
      ")" );
  }

  private static final Logger LOG = LogManager.getLogger(MtosiAddress.class.getPackage().getName());

  // *** Attributes ***
  private NetworkElement ne;
  private int neType = -1;
  private NamingAttributesT naming;

  //private boolean hasOs;
  private boolean hasMd;
  private boolean hasMe;
  private boolean hasEh;
  private boolean hasEq;
  private boolean hasPtp;
  private boolean hasMA;
  private boolean hasFtp;
  private boolean hasCtp;
  //private boolean hasFdfr;
  //private boolean hasTcp;
  //private boolean hasTDFr;

  private String ehName;
  private String ptpName;
  private String ftpName;

  private String shelfName;
  private String slotName;
  private String subSlotName;
  private String portName;

  private int shelfIndex;
  private int slotIndex;

  private String tlName;

  private static Map<Integer, String> namePatterns = new HashMap<Integer, String>();
  private static final String PATTERN = "(/shelf=(1)/slot=\\d+)|(/shelf=(1)/slot=FAN-(1))|(/shelf=(1)/slot=PSU-([1-2]))";

  static {
    //initialize mtosi name patterns for R7: EH and ports covered according to ADVA mtosi specs
    namePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM, PATTERN);
  }

  // *** contructors ***

  public boolean isHasMA() {
    return hasMA;
  }


  /**
   * Used to convert from NamingAttributesT
   * @param peerNeName
   */
  public MtosiAddress(String peerNeName) {
      final String neName = peerNeName;
      if (neName != null) {
        ne = ConfigCtrlImpl.get().getHandlers().getNeHdlr().getNEByName(neName);
      }
      if (ne != null) {
        neType = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI();
      }

    }

      /**
       * Used to convert from NamingAttributesT
       * @param name
       */
  public MtosiAddress(NamingAttributesT name) {
    this.naming = name;

    if (naming != null) {
      final String neName = naming.getMeNm();
      if (neName != null) {
        ne = ConfigCtrlImpl.get().getHandlers().getNeHdlr().getNEByName(neName);
      }
      if (ne != null) {
        neType = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI();
      }

      //hasOs  = (naming.getOsNm() != null);
      hasMd  = (naming.getMdNm() != null);
      hasMe  = (naming.getMeNm() != null);
      hasEh  = (naming.getEhNm() != null);
      hasEq  = (naming.getEqNm() != null);
      hasPtp = (naming.getPtpNm() != null);
      hasMA  = (naming.getMaNm() != null);
      hasFtp = (naming.getFtpNm() != null);
      hasCtp = (naming.getCtpNm() != null);
      //hasFdfr = (naming.getFdfrNm() != null);
      //hasTcp = (naming.getTcpNm() != null);
      //hasTDFr = (naming.getTdfrNm() != null);

      // parse eh-/ ptp-Name
      // elements of a EH-name
      final String regexpShelf   = "/(shelf)=(\\d+)";
      final String regexpSlot    = "/(slot)=([\\w-]+)";
      final String regexpSlotNum = "/(slot)=(\\d+)";
      final String regexpSubSlot = "/(sub_slot)=([\\w-]+)";
      final String regexpPort    = "/(port)=([\\w-]+)";

      Matcher matcher = null;

      if(hasFtp){
        ftpName = naming.getFtpNm();
      }

      if (hasEh) {
        // setup pattern for EH-Names (and EQ-Names)
        ehName = naming.getEhNm();
        // pattern for valid variants of EH-names
        Pattern pattern = Pattern.compile(
                  "^(" +                                             // Begin of Line
                  regexpShelf + "|" +                                // matches /shelf=x
                  regexpShelf + regexpSlot + "|" +                   // matches /shelf=x/slot=y
                  regexpShelf + regexpSlotNum + regexpSubSlot + "|" +   // matches /shelf=x/slot=y/sub_slot=z
                  ")$");                                             // End of Line
        matcher = pattern.matcher(ehName);
      }
      else if (hasPtp) {
        // setup pattern for PTP-Names
        ptpName = naming.getPtpNm();
        // pattern for valid variants of EH-names
        Pattern pattern = Pattern.compile(
                  "^(" +                                             // Begin of Line
                  regexpShelf + regexpSlotNum + regexpPort + "|" +      // matches /shelf=x/slot=y/port=z
                  regexpShelf + regexpPort +                         // matches /shelf=x/port=y
                  ")$");                                             // End of Line
        matcher = pattern.matcher(ptpName);
      }
      if(hasMA){
        String[] manetAndPTP = name.getMaNm().split("&&");
        naming.setMaNm(manetAndPTP[0].trim());
        if(manetAndPTP.length == 2)
          naming.setPtpNm(manetAndPTP[1].trim());
        else
          naming.setPtpNm("");
//        this.hasMafr = (this.getMaName() != null);
      }

      // determine shelf, slot, sub-slot and port
      if (matcher != null) {
        if ( matcher.find()) {
          String key;
          int grpCnt =  matcher.groupCount();
          for (int idx=1; idx <= grpCnt; idx++) {
            key =  matcher.group(idx);
            if (key == null || key.startsWith("/"))
              continue;
            else if (key.equals("shelf"))
              shelfName =  matcher.group(++idx);
            else if (key.equals("slot"))
              slotName =  matcher.group(++idx);
            else if (key.equals("sub_slot"))
              subSlotName =  matcher.group(++idx);
            else if (key.equals("port"))
              portName =  matcher.group(++idx);
          }
        }
        else {
          LOG.error("MtosiAddress(): parse failed. name=" + (hasEh ? ehName : ptpName));
        }
      }
    }

  }

  // possible address-fields
  // Notes: Flow (PTP and CTP), CTPLAG (FTP and CTP)
  public static enum NamingField {
    NONE,
    OS, MD, ME, EH, EQ, PTP, FTP, TDFR, CTP, FDFR, TCP,
    Flow, CTPLAG,
    PTP_or_FTP,
    PTP_or_FTP_or_Flow,
    PTP_or_FTP_or_Flow_or_CTPLAG
  }


  /**
   * Checks and validates the given address-fields. It throws an exception upon the first error.
   * @param fields  possible address fields
   * @throws ProcessingFailureException
   */
  public void validateAddressFields(NamingField... fields)  throws ProcessingFailureException
  {
    if (naming == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, "The Naming Attributes must be specified in the request");
    }

    // validate the address-fields
    for (NamingField field : fields) {
      switch (field) {
        case OS:
          if (naming.getOsNm() == null) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.OS_NAME_MISSING);
          }
          if (!naming.getOsNm().equals(OSFactory.getNmsName())) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.OS_NOT_FOUND);
          }
          break;
        case MD:
          if (!hasMd /*naming.getMdNm() == null*/) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.MD_NAME_MISSING);
          }
          if (!naming.getMdNm().equals(OSFactory.getMDNm())) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.MD_NOT_FOUND);
          }
          break;
        case ME:
          if (!hasMe /*naming.getMeNm() == null*/) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.ME_NAME_MISSING);
          }
          // corresponds to MtosiUtils.validateNE(ne);
          if (ne == null) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.ME_NOT_FOUND);
          }
          if(!ne.isDiscovered()) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.NOT_DISCOVERED);
          }
          if (!(ManagedElementTypes.isManageElement(ne.getNetworkElementType()))) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.ME_NOT_SUPPORTED);
          }
          break;
        case EH:
          if (!hasEh /*naming.getEhNm() == null*/) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.EH_NAME_MISSING);
          }
          break;
        case EQ:
          if (!hasEq /*naming.getEqNm() == null*/) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.EQ_NAME_MISSING);
          }
          if (!naming.getEqNm().equals("1")) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageIllegal("eqNm"));
          }
          break;
        case PTP:
          if (!hasPtp /*naming.getPtpNm() == null*/) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.PTP_NAME_MISSING);
          }
          break;
        case FTP:
          if (naming.getFtpNm() == null) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.FTP_NAME_MISSING);
          }
          break;
        case TDFR:
          if (naming.getTdfrNm() == null) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.TDFR_NAME_MISSING);
          }
          break;
        case CTP:
          if (naming.getCtpNm() == null) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.CTP_NAME_MISSING);
          }
          break;
        case FDFR:
          if (naming.getFdfrNm() == null) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.FDFR_NAME_MISSING);
          }
          break;
        case TCP:
          if (naming.getTcpNm() == null) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.TC_PROFILE_NAME_MISSING);
          }
          break;
        case PTP_or_FTP:
          if (!((naming.getPtpNm() != null && naming.getFtpNm() == null && naming.getCtpNm() == null) ||
                (naming.getFtpNm() != null && naming.getPtpNm() == null && naming.getCtpNm() == null))) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, "The PTP or FTP name must be specified in the request.");
          }
          break;
        case PTP_or_FTP_or_Flow:
          if (!((naming.getPtpNm() != null && naming.getFtpNm() == null && naming.getCtpNm() == null) ||
                (naming.getFtpNm() != null && naming.getPtpNm() == null && naming.getCtpNm() == null) ||
                (naming.getPtpNm() != null && naming.getCtpNm() != null && naming.getCtpNm() == null))) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, "The PTP or FTP or Flow name must be specified in the request.");
          }
          break;
        case PTP_or_FTP_or_Flow_or_CTPLAG:
          if (!((naming.getPtpNm() != null && naming.getFtpNm() == null && naming.getCtpNm() == null) ||
                (naming.getFtpNm() != null && naming.getPtpNm() == null && naming.getCtpNm() == null) ||
                (naming.getPtpNm() != null && naming.getCtpNm() != null && naming.getFtpNm() == null) ||
                (naming.getFtpNm() != null && naming.getCtpNm() != null && naming.getPtpNm() == null))) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, "The PTP or FTP or Flow or CTP-LAG name must be specified in the request.");
          }
          break;
      }
    }
  }

  /**
   * Returns the specific TP-type in case of multiple possible TE-types.
   * @param compoundType  TE-type list
   * @return              specific TE-type
   */
  public NamingField getObjectType(NamingField compoundType)
  {
    switch (compoundType) {
      case PTP_or_FTP:
        return (naming.getPtpNm() != null) ? NamingField.PTP : NamingField.FTP;
      case PTP_or_FTP_or_Flow:
        return (naming.getPtpNm() != null && naming.getCtpNm() != null) ? NamingField.Flow :
               (naming.getPtpNm() != null) ? NamingField.PTP : NamingField.FTP;
      case PTP_or_FTP_or_Flow_or_CTPLAG:
        return (naming.getPtpNm() != null && naming.getCtpNm() != null) ? NamingField.Flow :
               (naming.getFtpNm() != null && naming.getCtpNm() != null) ? NamingField.CTPLAG :     
               (naming.getPtpNm() != null) ? NamingField.PTP : NamingField.FTP;
    }

    return NamingField.NONE;
  }

  /**
   * Returns the FTP-object and throws an exception if the object does not exist.
   * @return FTP
   * @throws ProcessingFailureException
   */
  public FTP getAndValidateFTP()   throws ProcessingFailureException
  {
    FTP ftp = ne.getMTOSIWorker().getFTP(naming.getFtpNm());
    if(ftp == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
    }
    if (NEUtils.isF3Device(neType)) {
      ((FTPFSP150CM)ftp).doVolatilePolling();
    }
    return ftp;
  }

  public NetworkElement getNE() {
    return ne;
  }

  public int getNeType() {
    return neType;
  }

  public NamingAttributesT getNaming() {
    return naming;
  }

  // checks and getter
  public boolean isShelf() {
    // check if MD, ME and EH are present
    if (!hasMd || !hasMe || !hasEh)
      return false;

    // syntax check already done (see constructor)
    return (shelfName != null) && (slotName == null) && (subSlotName == null) && (portName == null);
  }

  /**
   *
   * @return true if it is a sub_slot, FAN or PSU
   */
  public boolean isSlot()
  {
    // check if MD, ME and EH are present
    if (!hasMd || !hasMe || !hasEh)
      return false;

    if (ehName.indexOf(MtosiConstants.SUBSLOT_TEXT) != -1)
      return true;

    if (neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM ||
        neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201 ||
        neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE ||
        neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206)
      return false;

    if (ehName.indexOf(MtosiConstants.FAN_TEXT) != -1)
      return true;

    if (ehName.indexOf(MtosiConstants.PSU_TEXT) != -1)
      return true;

    return false;
  }

  public boolean isSlotName() {

    return hasMd && hasMe && hasEh &&
        (shelfName != null) && (slotName != null) && (subSlotName == null) && (portName == null)&& (tlName==null);

  }

  /**
   * copy from MtosiUtils.isValidSlot
   *
   * @return true if the ehNm matches a slot/sub-slot defined in slotNamePatterns
   */
  public boolean isValidSlot()
  {
    String slotNamePattern = slotNamePatterns.get(neType);
    if (ehName == null || slotNamePattern == null)
      return false;

    return ehName.matches(slotNamePattern);
  }

  // copied from NamingAddressTranslation.isManagedElement
  public boolean isManagedElement()
  {
    if (hasMd && hasMe && !hasEh && !hasEq)
      return true;

    return false;
  }

  // copied from NamingAddressTranslation.isEquipmentHolder
  public boolean isEquipmentHolder()
  {
    if (hasMd && hasMe && hasEh && !hasEq)
      return true;

    return false;
  }

  /**
   * Check if the ehName identifies a fixed slot.
   *   NE-Type                 fixed-slots "/shelf=1/slot=" + ...
   *   150CM,                  "n", "PSU-m", "FAN-m"
   *   150CC-GE20x             "n", "PSU-m"
   *   HN400                   "n", "1" && "sub_slot=ETH-m"
   *   HN4000                  "n", "2" && "sub_slot=ETH-m"
   *   other                   "n"
   *        n = (see fixedSlotArray)
   *        m = 1..2
   *
   * @return true if it is a fixed slot (see description above)
   */
  public boolean isFixedSlot()
  {
    // special check
    switch (neType) {
      case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
        String slot = (neType == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400) ? "1" : "2";
        if (slotName != null && subSlotName != null) {
          if (slotName.equals(slot) && subSlotName.matches("ETH-[12]"))
            return true;
        }
      case NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
        if(slotName != null && getNaming().getEhNm().matches(patternMOD))
          return true;
    }

    return (getNmsFixedSlotNumber() != -1);
  }

  /**
   * copy from NamingTranslationFactory.getNmsEntityType
   * Determines the entity-type (EntityType.xxx).
   *   For 150CM, 150CC-GE20x:
   *   - SLOT     any slot=n, PSU, FAN, SCU
   *   - MODULE   any sub_slot
   *   - SHELF    shelf-1
   *   Other devices:
   *   - PSU      PSU
   *   - FAN      FAN
   *   - SLOT     any slot=n
   *   - MODULE   any sub_slot
   *   - SHELF    shelf-1
   *
   * @return EntityType.xxx (see table above)
   */
  public int getNmsEntityType()
  {
    // check for Network-Element, ME and EH or EQ
    if (ne == null || !hasMe || !(hasEh || hasEq))
      return -1;

    String ehName = hasEh ? naming.getEhNm() : "";
    // removed check for EqNm, because it will always be 1 now, and ehNm will be included
    // even if it is an equipment (whereas before there was only one of them)

    if (ehName.indexOf(MtosiConstants.SHELF_TEXT) == -1) {
      return -1; // for now
    }
    if (isFixedSlot())
    {
      return EntityType.SLOT;
    }
    if (ehName.indexOf(MtosiConstants.PSU_TEXT) != -1) {
      // PSU (equipment) or PSU Slot (holder)
      return EntityType.POWER_SUPPLY;
    }
    if (ehName.indexOf(MtosiConstants.FAN_TEXT) != -1) {
      // SFP (equipment) or SFP Slot (holder)
      return EntityType.FAN;
    }
    if (ehName.indexOf(MtosiConstants.SUBSLOT_TEXT) != -1)
    {
      return EntityType.MODULE;
    }
    // Top Level Chassis, so either Shelf (holder) or System (equipment)
    return EntityType.SHELF;
  }

  // copy from NamingTranslationFactory.shelfNumberFromShelfCombo
  public int getShelfNumber() {
    if (shelfName != null) {
      try {
        return Integer.parseInt(shelfName);
      }
      catch (NumberFormatException e) {
        LOG.error("getShelfNumber(): wrong shelf number format");
      }
    }

    return -1;
  }

  public int getSlotNumber() {
    if (slotName != null) {
      try {
        return Integer.parseInt(slotName);
      }
      catch (NumberFormatException e) {
        LOG.error("getShelfNumber(): wrong shelf number format");
      }
    }

    return -1;
  }

    public String getPsuNameForGE() {
        return slotName;
    }

  /**
   * copy from NamingTranslationFactory.fixedSlotToNumber
   * Determines the slot number for fixed-slots as defined in table fixedSlotNbr2MtosiName
   *
   * @return slot-number or -1 (if not a fixed slot)
   */
  public int getNmsFixedSlotNumber() {
    if (slotName == null || subSlotName != null || portName != null)
      return -1;

    String [] slotNames = fixedSlotNbr2MtosiName.get(neType);
    if (slotNames == null)
      return -1;

    for (int slotNbr = 0; slotNbr < slotNames.length; slotNbr++) {
      if (slotNames[slotNbr] != null && slotNames[slotNbr].equals(slotName)) {
        return slotNbr;
      }
    }

    return -1;
  }

  /**
   * copy from MtosiUtils.getCardTypeFromSlotIndex
   * Determines the card-type for the given slot-name: SCU, PSU, FAN, Generic
   *
   * @return MIB.FSP150CM.Entity.SlotTable.TYPE_xxx_INDEX
   */
  public int getFixedSlotCardType() {
    if (getNmsFixedSlotNumber() == -1)
      return -1;

    if (slotName.indexOf(MtosiConstants.EQUIPMENT_SCU) == 0)
      return MIBFSP150CM.Entity.SlotTable.TYPE_SCU_INDEX;
    if (slotName.indexOf(MtosiConstants.EQUIPMENT_PSU) == 0)
      return MIBFSP150CM.Entity.SlotTable.TYPE_PSU_INDEX;
    if (slotName.indexOf(MtosiConstants.EQUIPMENT_FAN) == 0)
      return MIBFSP150CM.Entity.SlotTable.TYPE_FAN_INDEX;

    try {
      Integer.parseInt(slotName);
      return MIBFSP150CM.Entity.SlotTable.TYPE_GENERIC_INDEX;
    }
    catch (NumberFormatException e) {
      LOG.error("getFixedSlotCardType(): wrong slot number format");
    }

    return -1;
  }
  private int getCardType(int slotIndex) {
    String [] slotNames = fixedSlotNbr2MtosiName.get(neType);
    if (slotNames == null || slotNames.length <= slotIndex || slotNames[slotIndex] == null)
      return -1;

    if (slotNames[slotIndex].indexOf(MtosiConstants.EQUIPMENT_SCU) == 0)
      return MIBFSP150CM.Entity.SlotTable.TYPE_SCU_INDEX;
    if (slotNames[slotIndex].indexOf(MtosiConstants.EQUIPMENT_PSU) == 0)
      return MIBFSP150CM.Entity.SlotTable.TYPE_PSU_INDEX;
    if (slotNames[slotIndex].indexOf(MtosiConstants.EQUIPMENT_FAN) == 0)
      return MIBFSP150CM.Entity.SlotTable.TYPE_FAN_INDEX;

    try {
      Integer.parseInt(slotNames[slotIndex]);
      return MIBFSP150CM.Entity.SlotTable.TYPE_GENERIC_INDEX;
    }
    catch (NumberFormatException e) {
      LOG.error("getFixedSlotCardType(slotIndex): wrong slotIndex");
    }

    return -1;
  }

  /**
   * copy from NamingTranslationFactory.getMtosiInventoryTypeForNaming
   * @return enum (EQPT, EQPT_HOLDER) depending on naming-attributes
   */
  public MtosiInventoryType getMtosiInventoryType()
  {
    if (hasEq)
      return MtosiInventoryType.EQUIPMENT;

    if (hasEh)
      return MtosiInventoryType.EQUIPMENT_HOLDER;

    return MtosiInventoryType.EQUIPMENT;
  }

  /**
   * returns the SlotName for a give slotIndex
   * @param neType
   * @param slotIndex
   * @return slotName
   */
  public static String getSlotName(int neType, int slotIndex) {
    String [] slotNames = fixedSlotNbr2MtosiName.get(neType);
    if (slotNames == null || slotIndex >= slotNames.length || slotNames[slotIndex] == null)
      return null;

    return slotNames[slotIndex];
  }

  public int getShelfIndex() {
    return shelfIndex;
  }

  public void setShelfIndex(int shelfIndex) {
    this.shelfIndex = shelfIndex;
  }

  public int getSlotIndex() {
    return slotIndex;
  }

  public void setSlotIndex(int slotIndex) {
    this.slotIndex = slotIndex;
  }

  public String getTlName() {
    return tlName;
  }

  public void setTlName(String tlName) {
    this.tlName = tlName;
  }

  public boolean isPort()
  {
    if(portName!=null && portName.contains("OL")  &&   shelfName==null && slotName==null && subSlotName==null && tlName==null)
      return true;

    return hasMd && hasMe  &&
        shelfName != null && ((slotName != null && subSlotName == null)
        || (slotName != null && subSlotName != null)) && portName != null && tlName == null;

  }

  public boolean getHasCtp() {
    if (getNaming().getCtpNm() != null){
      return true;
    }
    return false;
  }


  public boolean getHasFtp() {
    if (getNaming().getFtpNm() != null){
      return true;
    }
    return false;
  }

  public boolean getHasEq() {
    if (getNaming().getEqNm() != null){
      return true;
    }
    return false;
  }

  public boolean getHasEh() {
    if (getNaming().getEhNm() != null){
      return true;
    }
    return false;
  }

  public boolean getHasPtp() {
    if (getNaming().getPtpNm() != null){
      return true;
    }
    return false;
  }

  public boolean isValidName() {
    String slotNamePattern = namePatterns.get(neType);
    return !(ehName == null || slotNamePattern == null) && ehName.matches(slotNamePattern);

  }

  public boolean isSubSlot() {

    return hasMd && hasMe && hasEh &&
        (shelfName != null) && (slotName != null) && (subSlotName != null) && (portName == null)&& (tlName==null);

  }

  public enum RDNType{
    ACC(PortF3AccAttr.class),
    NET1(PortF3NetAttr.class),
    NET2(PortF3NetAttr.class),
    FLOW(FlowF3Attr.class),
    FTP(ProtectionGroupF3Attr.class),
    N_A(ManagedObjectAttr.class);

    private Class<? extends ManagedObjectAttr> aClass;

    RDNType(Class<? extends ManagedObjectAttr> aClass) {
      this.aClass = aClass;
    }

    public Class<? extends ManagedObjectAttr> getaClass() {
      return aClass;
    }
  }

  public RDNType getTypeOf(){
    if(hasPtp && hasCtp && ptpName.contains("ACC")) return RDNType.FLOW;
    if(hasPtp && !hasCtp && ptpName.contains("NET-1")) return RDNType.NET1;
    if(hasPtp && !hasCtp && ptpName.contains("NET-2")) return RDNType.NET2;
    if(hasPtp && !hasCtp && ptpName.contains("ACC")) return RDNType.ACC;
    if(hasPtp && !hasCtp && hasFtp) return RDNType.FTP;
    return RDNType.N_A;
  }

  public boolean isAccPort(String mtosiname) {
    if(mtosiname!=null && mtosiname.contains("ACC"))
      return true;

    return false;
  }

  public boolean isNetPort(String mtosiname) {
    if(mtosiname!=null && mtosiname.contains("NET"))
      return true;

    return false;
  }

}
