/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.factory;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.common.config.EntityType;
import com.adva.nlms.common.snmp.NoSuchMDObjectException;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.PsuSPPropertiesFSP150CM;
import com.adva.nlms.mediation.config.Entity;
import com.adva.nlms.mediation.config.Equipment;
import com.adva.nlms.mediation.config.Module;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.SlotF3SPProperties;
import com.adva.nlms.mediation.config.f3.entity.module.MTOSICardModuleF3;
import com.adva.nlms.mediation.config.f3.entity.module.MTOSIEthernetModuleF3;
import com.adva.nlms.mediation.config.f3.entity.module.fan.MTOSIFanF3;
import com.adva.nlms.mediation.config.f3.entity.module.nte.MTOSINTEF3;
import com.adva.nlms.mediation.config.f3.entity.module.psu.PowerSupplyF3;
import com.adva.nlms.mediation.config.f3.entity.module.sfp.MTOSISfpModuleF3;
import com.adva.nlms.mediation.config.f3.entity.slot.SlotF3;
import com.adva.nlms.mediation.config.f3.mtosi.NetworkElementFSP150CMMTOSIOperations;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.entity.module.ntu.MTOSINTUFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.entity.module.scut.ScuTFSP150CMImpl;
import com.adva.nlms.mediation.config.fsp20X.NetworkElementFSPGE20X;
import com.adva.nlms.mediation.config.hn4000.ModuleHN4000;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.mediation.NetworkElementMediatorFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.EquipmentMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.config.hn4000.necomm.NetworkElementDiscoveryHN4000;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.EquipmentOrHolderListT;
import v1.tmf854.EquipmentOrHolderT;
import v1.tmf854.EquipmentT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import ws.v1.tmf854.ProcessingFailureException;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class EquipmentFactory {
  private static Logger logger = LogManager.getLogger(EquipmentFactory.class.getPackage().getName());

  public static EquipmentOrHolderListT getAllEquipment(MtosiAddress mtosiAddress) throws Exception
  {
    // Given a NamingAttributesT, get all contained equipment and holders
    /*
     * If its ME, then get all for ME, if its shelf, then get all for the
     * given shelf, and if its a slot, get the contained equipment for the
     * shelf.
     */
    EquipmentOrHolderListT list = new EquipmentOrHolderListT();
    List<EquipmentOrHolderT> retrievedList = new ArrayList<EquipmentOrHolderT>();
    NetworkElement ne = mtosiAddress.getNE();
    int type = mtosiAddress.getNeType();
    switch (type) {
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
        if (mtosiAddress.isManagedElement()) {
          retrievedList = getAllEquipmentCP(ne);
        }
        else if (mtosiAddress.isSlot()) {
          retrievedList = getAllEquipmentForSlot(mtosiAddress);
        }
        else if (mtosiAddress.isFixedSlot()) {
          retrievedList = getAllEquipmentForFixedSlotCP(mtosiAddress);
        }
        else if (mtosiAddress.isShelf()) {
          retrievedList = getAllEquipmentForOneShelfCP(mtosiAddress);
        }
        break;
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
        if (mtosiAddress.isManagedElement()) {
          retrievedList = getAllEquipmentCM(mtosiAddress);
        }
        else if (mtosiAddress.isSlot()) {
          retrievedList = getAllEquipmentForSlot(mtosiAddress);
        }
        else if (mtosiAddress.isFixedSlot()) {
          retrievedList = getAllEquipmentForFixedSlotCM(mtosiAddress);
        }
        else if (mtosiAddress.isShelf()) {
          retrievedList = getAllEquipmentForOneShelfCM(mtosiAddress);
        }
        break;
      case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
        if (mtosiAddress.isManagedElement()) {
          retrievedList = getAllEquipmentHN(ne);
        }
        else if (mtosiAddress.isSlot()) {
          retrievedList = getAllEquipmentForSlot(mtosiAddress);
        }
        else if (mtosiAddress.isFixedSlot()) {
          retrievedList = getAllEquipmentForFixedSlotHN(mtosiAddress);
        }
        else if (mtosiAddress.isShelf()) {
          retrievedList = getAllEquipmentForOneShelfHN(mtosiAddress);
        }
        break;
    }
		list.getEoh().addAll(retrievedList);
		return list;
	}


	// this gets all equipment for a fixed slot that matches the naming for
	// shelf component
	public static List<EquipmentOrHolderT> getAllEquipmentForFixedSlotCP(MtosiAddress mtosiAddress)
	{
		NetworkElement ne = mtosiAddress.getNE();
		NamingAttributesT naming = mtosiAddress.getNaming();
		List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
		if (naming.getEhNm() == null)
			return list;
		Set<EquipmentSPProperties> shelfSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.SHELF);
		if (shelfSet.size() < 1)
			return list;
		for (EquipmentSPProperties properties : shelfSet) {
			list.addAll(getAllEquipmentForFixedSlotCP(ne, properties));
		}
		return list;
	}

  public static List<EquipmentOrHolderT> getAllEquipmentForFixedSlotHN(MtosiAddress mtosiAddress)
	{
		NetworkElement ne = mtosiAddress.getNE();
		NamingAttributesT naming = mtosiAddress.getNaming();
		List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
		if (naming.getEhNm() == null)
			return list;
    int shelfIndex = mtosiAddress.getShelfNumber();
    int slotIndex = mtosiAddress.getSlotNumber();
    Module slot = ne.getMTOSIWorker().getModule(shelfIndex, slotIndex);
    EquipmentOrHolderT eqOrEh = null;
    if (slot != null) {
      eqOrEh = NetworkElementMediatorFactory.createNetworkElementMediator(ne).shelfToMtosiEquipment(slot.getEquipmentSPProperties());
      list.add(eqOrEh);
    }
    if (slotIndex == 2 && ne.getMTOSIWorker().getShelf(shelfIndex) != null) {
      if (eqOrEh == null) {
        EquipmentSPProperties props = new EquipmentSPProperties();
        props.set(EquipmentSPProperties.VI.ChassisId, shelfIndex);
        props.set(EquipmentSPProperties.VI.RelativePosition, slotIndex);
        eqOrEh = NetworkElementMediatorFactory.createNetworkElementMediator(ne).shelfToMtosiEquipment(props);
      }
      ObjectFactory objFactory = new ObjectFactory();
      EquipmentT equipment = eqOrEh.getEq();
      String expectedEquipment = "";
      String installedEquipment = "";
      if (slot != null) {
        ModuleHN4000 hnModule = (ModuleHN4000) slot;
        expectedEquipment = NetworkElementDiscoveryHN4000.ModuleTypeEnum.getModuleName(hnModule.getAssignedType());
        installedEquipment = NetworkElementDiscoveryHN4000.ModuleTypeEnum.getModuleName(hnModule.getEquippedType());
      }
      equipment.setExpectedEquipmentObjectType(objFactory.
              createEquipmentTExpectedEquipmentObjectType(expectedEquipment));
      equipment.setInstalledEquipmentObjectType(objFactory
              .createEquipmentTInstalledEquipmentObjectType(installedEquipment));
    }
    // SFP
    List<Module> sfpSet = ne.getMTOSIWorker().getSFPs(shelfIndex, slotIndex);
    Set<EquipmentSPProperties> sfpProps = new HashSet<EquipmentSPProperties>();
    for (Module sfp : sfpSet) {
      sfpProps.add(sfp.getEquipmentSPProperties());
    }
    List<EquipmentOrHolderT> allSFP = EquipmentMediator.nmsSFPArrayToMtosiEquipmentAndHolders(ne, sfpProps);
    list.addAll(allSFP);
    return list;
	}

  public static List<EquipmentOrHolderT> getAllEquipmentForFixedSlotCM(MtosiAddress mtosiAddress)
  {
    NetworkElement ne = mtosiAddress.getNE();
    NamingAttributesT naming = mtosiAddress.getNaming();
    List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
    if (naming.getEhNm() == null)
      return list;
    int shelfIndex = mtosiAddress.getShelfNumber();
    int slotIndex = mtosiAddress.getNmsFixedSlotNumber();
    List<EquipmentOrHolderT> slotEquipment = getSlotEquipment(ne, new SlotF3SPProperties(-1, shelfIndex, slotIndex, -1));
    list.addAll(slotEquipment);
    return list;
  }


  public static List<EquipmentOrHolderT> getAllEquipmentForOneShelfCP(MtosiAddress mtosiAddress)
	{
		NetworkElement ne = mtosiAddress.getNE();
		NamingAttributesT naming = mtosiAddress.getNaming();
		List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
		Set<EquipmentSPProperties> propertiesSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.SHELF);
		for (EquipmentSPProperties nextProperties : propertiesSet) {
			NamingAttributesT nextNaming = NamingTranslationFactory.createNamingAttributesHolder(ne, nextProperties);
			if (MtosiUtils.isNamingAttributesMatch(nextNaming, naming)) {
				return getAllEquipmentForShelfCP(ne, nextProperties);
			}
		}
		return list;
	}

  public static List<EquipmentOrHolderT> getAllEquipmentForOneShelfHN(MtosiAddress mtosiAddress)
	{
		NetworkElement ne = mtosiAddress.getNE();
		NamingAttributesT naming = mtosiAddress.getNaming();
    List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
		if (naming.getEhNm() == null)
			return list;
    int shelfIndex = mtosiAddress.getShelfNumber();
    final Equipment shelf = ne.getMTOSIWorker().getShelf(shelfIndex);
    if (shelf != null) {
      EquipmentSPProperties shelfProps = new EquipmentSPProperties();
      shelfProps.set(EquipmentSPProperties.VI.ChassisId, shelfIndex);
      list.addAll(getAllEquipmentForShelfHN(ne, shelfProps));
    }
    return list;
	}

  public static List<EquipmentOrHolderT> getAllEquipmentForOneShelfCM(MtosiAddress mtosiAddress)
	{
		NetworkElement ne = mtosiAddress.getNE();
		NamingAttributesT naming = mtosiAddress.getNaming();
		List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
		Set<EquipmentSPProperties> propertiesSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.SHELF);
		for (EquipmentSPProperties nextProperties : propertiesSet) {
			NamingAttributesT nextNaming = NamingTranslationFactory.createNamingAttributesHolder(ne, nextProperties);
			if (MtosiUtils.isNamingAttributesMatch(nextNaming, naming)) {
				return getAllEquipmentForShelfCM(ne, nextProperties);
			}
		}
		return list;
	}

  public static List<EquipmentOrHolderT> getAllEquipmentForShelfCP(NetworkElement ne, EquipmentSPProperties properties)
	{
		// this needs to be more like cc, where we get fixed slot and psu/fan
		// get fixed slot, card, and sfp/psu
		List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
		list.addAll(getAllEquipmentForFixedSlotCP(ne, properties));
		Set<EquipmentSPProperties> psuSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.POWER_SUPPLY);
		List<EquipmentOrHolderT> allPSU = EquipmentMediator.nmsPSUArrayToMtosiEquipmentAndHolders(ne, psuSet);
		list.addAll(allPSU);
		Set<EquipmentSPProperties> fanSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.FAN);
		List<EquipmentOrHolderT> allFan = EquipmentMediator.nmsFanArrayToMtosiEquipmentAndHolders(ne, fanSet);
		list.addAll(allFan);
		// add the fixedSlot holder
		Set<EquipmentSPProperties> shelfSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.SHELF);
		List<EquipmentOrHolderT> allShelf = EquipmentMediator.nmsShelfArrayToMtosiFixedSlots(ne, shelfSet);
		list.addAll(allShelf);
		return list;
	}

  public static List<EquipmentOrHolderT> getAllEquipmentForShelfHN(NetworkElement ne, EquipmentSPProperties properties)
	{
		List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
		list.addAll(getAllEquipmentAndHoldersForFixedSlotHN(ne, properties.get(EquipmentSPProperties.VI.ChassisId), true));
    // PSU
    List<Equipment> psuSet = ne.getMTOSIWorker().getPSUs(properties.get(EquipmentSPProperties.VI.ChassisId));
    Set<EquipmentSPProperties> psuProps = new HashSet<EquipmentSPProperties>();
    for (Equipment psu : psuSet) {
      psuProps.add(psu.getEquipmentSPProperties());
    }
    List<EquipmentOrHolderT> allPSU = EquipmentMediator.nmsPSUArrayToMtosiEquipmentAndHolders(ne, psuProps);
		list.addAll(allPSU);
    // FAN
    List<Equipment> fanSet = ne.getMTOSIWorker().getFANs(properties.get(EquipmentSPProperties.VI.ChassisId));
    Set<EquipmentSPProperties> fanProps = new HashSet<EquipmentSPProperties>();
    for (Equipment fan : fanSet) {
      fanProps.add(fan.getEquipmentSPProperties());
    }
    List<EquipmentOrHolderT> allFan = EquipmentMediator.nmsFanArrayToMtosiEquipmentAndHolders(ne, fanProps);
		list.addAll(allFan);
    // SFP
    List<Module> sfpSet = ne.getMTOSIWorker().getSFPs(properties.get(EquipmentSPProperties.VI.ChassisId), 2);
    Set<EquipmentSPProperties> sfpProps = new HashSet<EquipmentSPProperties>();
    for (Module sfp : sfpSet) {
      sfpProps.add(sfp.getEquipmentSPProperties());
    }
    List<EquipmentOrHolderT> allSFP = EquipmentMediator.nmsSFPArrayToMtosiEquipmentAndHolders(ne, sfpProps);
    list.addAll(allSFP);
    return list;
	}

  public static List<EquipmentOrHolderT> getAllEquipmentForShelfCM(NetworkElement ne, EquipmentSPProperties shelfProperties)
  {
    List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();

    Set<SlotF3SPProperties> slotProperties = new HashSet<SlotF3SPProperties>();
    Set<Entity> slots = ne.getMTOSIWorker().getEntitiesByType(MIB.Entity.CONTAINER);
    for (Entity slot : slots) {
      slotProperties.add(((SlotF3)slot).getSlotSPProperties());
    }
    int chassisId = shelfProperties.get(EquipmentSPProperties.VI.ChassisId); // CHASSISID
    for (SlotF3SPProperties properties : slotProperties)
    {
      if (properties.get(SlotF3SPProperties.VI.ShelfIndex) == chassisId)
      {
        List<EquipmentOrHolderT> slotEquipment = getSlotEquipment(ne, properties);
        list.addAll(slotEquipment);
      }
    }
		return list;
  }

  public static List<EquipmentOrHolderT> getAllEquipmentForFixedSlotCP(NetworkElement ne, EquipmentSPProperties properties)
	{
		// getSFP and Card and below
		List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
		// get all SFP equipment and holders
		Set<EquipmentSPProperties> sfpSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.MODULE);
		List<EquipmentOrHolderT> allSFP = EquipmentMediator.nmsSFPArrayToMtosiEquipmentAndHolders(ne, sfpSet);
		list.addAll(allSFP);
		// add the card
    // TODO (FNM2226): Workaround for messy fixed-slot handling
    if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400) {
      for (EquipmentSPProperties props : ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.MODULE)) {
        if (!props.isSFP()) {
          properties = props;
          break;
        }
      }
    }
    list.add(NetworkElementMediatorFactory.createNetworkElementMediator(ne).shelfToMtosiEquipment(properties));
		return list;
	}

  private static List<EquipmentOrHolderT> getAllEquipmentAndHoldersForFixedSlotHN(NetworkElement ne, int shelfIndex, boolean includeEq)
	{
		List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
    Module slot1 = ne.getMTOSIWorker().getModule(shelfIndex, 1);
    EquipmentSPProperties props = slot1.getEquipmentSPProperties();
    if (includeEq) {
      list.add(NetworkElementMediatorFactory.createNetworkElementMediator(ne).shelfToMtosiEquipment(props));
    }
    list.add(EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipmentHolder(ne, props));
    Module slot2 = ne.getMTOSIWorker().getModule(shelfIndex, 2);
    if (slot2 != null) {
      props = slot2.getEquipmentSPProperties();
      if (includeEq) {
        EquipmentOrHolderT eqOrEh = NetworkElementMediatorFactory.createNetworkElementMediator(ne).shelfToMtosiEquipment(props);
        ObjectFactory objFactory = new ObjectFactory();
        EquipmentT equipment = eqOrEh.getEq();
        ModuleHN4000 hnModule = (ModuleHN4000) slot2;
        String expectedEquipment = NetworkElementDiscoveryHN4000.ModuleTypeEnum.getModuleName(hnModule.getAssignedType());
        String installedEquipment = NetworkElementDiscoveryHN4000.ModuleTypeEnum.getModuleName(hnModule.getEquippedType());
        equipment.setExpectedEquipmentObjectType(objFactory.
                createEquipmentTExpectedEquipmentObjectType(expectedEquipment));
        equipment.setInstalledEquipmentObjectType(objFactory
                .createEquipmentTInstalledEquipmentObjectType(installedEquipment));
        list.add(eqOrEh);
      }
      list.add(EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipmentHolder(ne, props));
    }
    return list;
	}

  private static NetworkElementFSP150CMMTOSIOperations getFSP150CMMTOSIWorker(NetworkElementFSP150CM ne) {
    return (NetworkElementFSP150CMMTOSIOperations)ne.getMTOSIWorker();
  }

  public static List<EquipmentOrHolderT> getSlotEquipment(NetworkElement ne, SlotF3SPProperties slotProps) {
    NetworkElementFSP150CM neFsp150CM = (NetworkElementFSP150CM)ne;
    NetworkElementFSP150CMMTOSIOperations mtosiWorker = getFSP150CMMTOSIWorker(neFsp150CM);
    List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
    List<EquipmentOrHolderT> sfpList = new ArrayList<EquipmentOrHolderT>();
    EquipmentSPPropertiesFSP150CM eqProperties = new EquipmentSPPropertiesFSP150CM();
    eqProperties.set(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex, slotProps.get(SlotF3SPProperties.VI.ShelfIndex));
    eqProperties.set(EquipmentSPPropertiesFSP150CM.VI.SlotIndex, slotProps.get(SlotF3SPProperties.VI.SlotIndex));
    eqProperties.set(EquipmentSPProperties.VL.ManufactureDate, ne.getMTOSIWorker().getShelf(slotProps.get(SlotF3SPProperties.VI.ShelfIndex)).getEquipmentSPProperties().get(EquipmentSPProperties.VL.ManufactureDate));
    String equipmentType = null;
    String mismatchType = "";
    EquipmentSPPropertiesFSP150CM props = null;
    switch (MtosiUtils.getCardTypeFromSlotIndex(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI(), slotProps.get(SlotF3SPProperties.VI.SlotIndex))) {
      case MIBFSP150CM.Entity.SlotTable.TYPE_GENERIC_INDEX: {
        MTOSICardModuleF3 card = mtosiWorker.getModule(slotProps.get(SlotF3SPProperties.VI.ShelfIndex), slotProps.get(SlotF3SPProperties.VI.SlotIndex));
        if (card != null) {
          props = (EquipmentSPPropertiesFSP150CM) card.getEquipmentSPProperties();
          if (card instanceof MTOSIEthernetModuleF3) {
            Set<MTOSISfpModuleF3> sfps = ((MTOSIEthernetModuleF3)card).getAllSFP();
            Set<EquipmentSPProperties> sfpProps = new HashSet<EquipmentSPProperties>();
            for (MTOSISfpModuleF3 sfp : sfps) {
              sfpProps.add(sfp.getEquipmentSPProperties());
            }
            sfpList.addAll(EquipmentMediator.nmsSFPArrayToMtosiEquipmentAndHolders(ne, sfpProps));
            if (card instanceof MTOSINTEF3 && ne instanceof NetworkElementFSPGE20X)
              equipmentType = MtosiConstants.EQUIPMENT_NTE_GE;
            else if (card instanceof MTOSINTEF3) {
              equipmentType = MtosiConstants.EQUIPMENT_NTE_GE_SYNC;
              mismatchType = MtosiConstants.EQUIPMENT_NTU_GE;
            } else {
              equipmentType = MtosiConstants.EQUIPMENT_NTU_GE;
              mismatchType = MtosiConstants.EQUIPMENT_NTE_GE_SYNC;
              }
          }
          else {
            equipmentType = MtosiConstants.EQUIPMENT_NEMI;
          }
        }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_SCU_INDEX: {
        MTOSICardModuleF3 card = mtosiWorker.getModule(slotProps.get(SlotF3SPProperties.VI.ShelfIndex), slotProps.get(SlotF3SPProperties.VI.SlotIndex));
        if (card != null) {
          props = (EquipmentSPPropertiesFSP150CM) card.getEquipmentSPProperties();
          if (card instanceof ScuTFSP150CMImpl) {
            equipmentType = MtosiConstants.EQUIPMENT_SCU_T;
            mismatchType = MtosiConstants.EQUIPMENT_SCU;
          } else {
            equipmentType = MtosiConstants.EQUIPMENT_SCU;
            mismatchType = MtosiConstants.EQUIPMENT_SCU_T;
          }
        }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_PSU_INDEX: {
        PowerSupplyF3 psu = mtosiWorker.getPSU(slotProps.get(SlotF3SPProperties.VI.ShelfIndex), slotProps.get(SlotF3SPProperties.VI.SlotIndex));
        if (psu != null) {
          props = (EquipmentSPPropertiesFSP150CM) psu.getEquipmentSPProperties();
          Integer psuType = ((PsuSPPropertiesFSP150CM)psu.getEquipmentSPProperties()).get(PsuSPPropertiesFSP150CM.VI.PsuType);
		  if (psuType == MIBFSP150CM.Entity.PsuTable.TYPE_AC_INDEX) {
            equipmentType = MtosiConstants.EQUIPMENT_PSU_AC;
          }
          else if (psuType == MIBFSP150CM.Entity.PsuTable.TYPE_DC_INDEX) {
        	equipmentType = MtosiUtils.getGE20XPsuType(props.get(EquipmentSPProperties.VS.ModelName));
          } else {
        	equipmentType = MtosiConstants.EQUIPMENT_PSU;
          }
        }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_FAN_INDEX: {
        MTOSIFanF3 fan = mtosiWorker.getFAN(slotProps.get(SlotF3SPProperties.VI.ShelfIndex), slotProps.get(SlotF3SPProperties.VI.SlotIndex));
        if (fan != null) {
          props = (EquipmentSPPropertiesFSP150CM) fan.getEquipmentSPProperties();
          equipmentType = MtosiConstants.EQUIPMENT_FAN;
        }
        break;
      }
    }
    if (props != null) {
      Long manufactureDate = props.get(EquipmentSPProperties.VL.ManufactureDate);
      props.set(EquipmentSPProperties.VL.ManufactureDate, ne.getMTOSIWorker().getShelf(props.get(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex)).getEquipmentSPProperties().get(EquipmentSPProperties.VL.ManufactureDate));
      EquipmentOrHolderT eqOrEh = EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipmentHolder(neFsp150CM, props, true);
      props.set(EquipmentSPProperties.VL.ManufactureDate, manufactureDate);
      list.add(eqOrEh);
      list.add(EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipment(neFsp150CM, props, equipmentType, mismatchType));
      list.addAll(sfpList);
    }
    else {
      EquipmentOrHolderT eqOrEh = EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipmentHolder(neFsp150CM, eqProperties, false);
      list.add(eqOrEh);
    }

    return list;
  }

  public static List<EquipmentOrHolderT> getContainedSlotEquipment(NetworkElementFSP150CM ne, SlotF3SPProperties slotProps, final boolean fromShelf) {
    List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
    EquipmentSPPropertiesFSP150CM eqProperties = new EquipmentSPPropertiesFSP150CM();
    eqProperties.set(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex, slotProps.get(SlotF3SPProperties.VI.ShelfIndex));
    eqProperties.set(EquipmentSPPropertiesFSP150CM.VI.SlotIndex, slotProps.get(SlotF3SPProperties.VI.SlotIndex));
    String equipmentType = null;
    String mismatchType = "";
    EquipmentSPPropertiesFSP150CM props = null;
    NetworkElementFSP150CMMTOSIOperations mtosiWorker = getFSP150CMMTOSIWorker(ne);
    switch (MtosiUtils.getCardTypeFromSlotIndex(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI(), slotProps.get(SlotF3SPProperties.VI.SlotIndex))) {
      case MIBFSP150CM.Entity.SlotTable.TYPE_GENERIC_INDEX: {
        MTOSICardModuleF3 card = mtosiWorker.getModule(slotProps.get(SlotF3SPProperties.VI.ShelfIndex), slotProps.get(SlotF3SPProperties.VI.SlotIndex));
        if (card != null) {
          props = (EquipmentSPPropertiesFSP150CM) card.getEquipmentSPProperties();
          if (card instanceof MTOSINTUFSP150CM) {
            equipmentType = MtosiConstants.EQUIPMENT_NTU_GE;
            mismatchType = MtosiConstants.EQUIPMENT_NTE_GE_SYNC;
          } else if (card instanceof MTOSINTEF3 && ne instanceof NetworkElementFSPGE20X) {
            equipmentType = MtosiConstants.EQUIPMENT_NTE_GE;
          } else if (card instanceof MTOSINTEF3) {
              equipmentType = MtosiConstants.EQUIPMENT_NTE_GE_SYNC;
              mismatchType = MtosiConstants.EQUIPMENT_NTU_GE;
          } else {
            equipmentType = MtosiConstants.EQUIPMENT_NEMI;
          }
        }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_SCU_INDEX: {
        MTOSICardModuleF3 card = mtosiWorker.getModule(slotProps.get(SlotF3SPProperties.VI.ShelfIndex), slotProps.get(SlotF3SPProperties.VI.SlotIndex));
        if (card != null) {
          props = (EquipmentSPPropertiesFSP150CM) card.getEquipmentSPProperties();
          if (card instanceof ScuTFSP150CMImpl) {
            equipmentType = MtosiConstants.EQUIPMENT_SCU_T;
            mismatchType = MtosiConstants.EQUIPMENT_SCU;
          } else {
            equipmentType = MtosiConstants.EQUIPMENT_SCU;
            mismatchType = MtosiConstants.EQUIPMENT_SCU_T;
          }
        }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_PSU_INDEX: {
        PowerSupplyF3 psu = mtosiWorker.getPSU(slotProps.get(SlotF3SPProperties.VI.ShelfIndex), slotProps.get(SlotF3SPProperties.VI.SlotIndex));
        if (psu != null) {
          props = (EquipmentSPPropertiesFSP150CM) psu.getEquipmentSPProperties();
          int psuType = ((PsuSPPropertiesFSP150CM)psu.getEquipmentSPProperties()).get(PsuSPPropertiesFSP150CM.VI.PsuType);
          if (psuType == MIBFSP150CM.Entity.PsuTable.TYPE_AC_INDEX) {
            equipmentType = MtosiConstants.EQUIPMENT_PSU_AC;
          }
          else if (psuType == MIBFSP150CM.Entity.PsuTable.TYPE_DC_INDEX) {
        	  equipmentType = MtosiUtils.getGE20XPsuType(props.get(EquipmentSPProperties.VS.ModelName));
          } else {
              equipmentType = MtosiConstants.EQUIPMENT_PSU;
          }
        }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_FAN_INDEX: {
        MTOSIFanF3 fan = mtosiWorker.getFAN(slotProps.get(SlotF3SPProperties.VI.ShelfIndex), slotProps.get(SlotF3SPProperties.VI.SlotIndex));
        if (fan != null) {
          props = (EquipmentSPPropertiesFSP150CM) fan.getEquipmentSPProperties();
          equipmentType = MtosiConstants.EQUIPMENT_FAN;
        }
        break;
      }
    }
    if (props != null) {
      if (fromShelf) {
        list.add(EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipmentHolder(ne, props, true));
      }
      else {
        list.add(EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipment(ne, props, equipmentType,mismatchType));
      }
    }
    else if (fromShelf) {
      list.add(EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipmentHolder(ne, eqProperties, false));
    }

    return list;
  }

  public static List<EquipmentOrHolderT> getAllEquipmentForSlot(MtosiAddress mtosiAddress)
	{
		// figure out what kind of slot it is, then get equipment for it based
		// on properties
		// so this is simple one
		List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
		int entityType = mtosiAddress.getNmsEntityType();
		EquipmentOrHolderT eoh;
		switch (entityType)
		{
			case EntityType.POWER_SUPPLY:
				eoh = getPSUEquipmentForNaming(mtosiAddress);
				if (eoh != null)
				{
					list.add(eoh);
				}
				break;
			case EntityType.MODULE:
				eoh = getSFPEquipmentForNaming(mtosiAddress);
				if (eoh != null)
				{
					list.add(eoh);
				}
				break;
			case EntityType.FAN:
				eoh = getFanEquipmentForNaming(mtosiAddress);
				if (eoh != null)
				{
					list.add(eoh);
				}
				break;
			case EntityType.SHELF:
				eoh = getShelfEquipmentForNaming(mtosiAddress);
				if (eoh != null)
				{
					list.add(eoh);
				}
				break;
			default:
		}
		return list;
	}


  public static List<EquipmentOrHolderT> getAllEquipmentCP(NetworkElement ne)
  {
    List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
    Set<EquipmentSPProperties> propertiesSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.SHELF);
    for (EquipmentSPProperties nextProperties : propertiesSet) {
      list.addAll(getAllEquipmentForShelfCP(ne, nextProperties));
    }
    List<EquipmentOrHolderT> allShelf = EquipmentMediator.nmsShelfArrayToMtosiHolders(ne, propertiesSet);
    list.addAll(allShelf);
    return list;
  }

  public static List<EquipmentOrHolderT> getAllEquipmentHN(NetworkElement ne)
  {
    List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
    Set<EquipmentSPProperties> propertiesSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.SHELF);
    for (EquipmentSPProperties nextProperties : propertiesSet) {
      list.addAll(getAllEquipmentForShelfHN(ne, nextProperties));
    }
    List<EquipmentOrHolderT> allShelf = EquipmentMediator.nmsShelfArrayToMtosiHolders(ne, propertiesSet);
    list.addAll(allShelf);
    return list;
  }

  public static List<EquipmentOrHolderT> getAllEquipmentCM(MtosiAddress mtosiAddress)
	{
    NetworkElement ne = mtosiAddress.getNE();
    List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
		Set<EquipmentSPProperties> propertiesSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.SHELF);
		for (EquipmentSPProperties nextProperties : propertiesSet) {
      list.add(EquipmentMediator.nmsShelfPropertiesToMTOSIEquipmentHolder(ne, nextProperties));
      list.addAll(getAllEquipmentForShelfCM(ne, nextProperties));
		}
		return list;
	}

  public static NamingAttributesListT getAllEquipmentNames(MtosiAddress mtosiAddr) throws Exception
	{
		NamingAttributesListT list = new NamingAttributesListT();
		EquipmentOrHolderListT eohList = getAllEquipment(mtosiAddr);
		List<EquipmentOrHolderT> eohEntries = eohList.getEoh();
		List<NamingAttributesT> namesOnly = getNamesFromEoHList(eohEntries);
		list.getName().addAll(namesOnly);
		return list;
	}

	public static EquipmentT getSystemEquipmentForNE(NetworkElement ne) {
    EquipmentSPProperties properties = null;
    // TODO: temporary solution for CPMR cards
    if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR) {
      for (EquipmentSPProperties props : ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.MODULE)) {
        if (!props.isSFP()) {
          properties = props;
          break;
        }
      }
    }
    else {
      Set<EquipmentSPProperties> propertiesSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.SHELF);
      if (propertiesSet.size() < 1) {
        return null;
      }
      properties = propertiesSet.iterator().next();
    }
    if (properties == null) {
      return null;
    }
    return NetworkElementMediatorFactory.createNetworkElementMediator(ne).shelfToMtosiEquipment(properties).getEq();
	}

	public static EquipmentOrHolderT getCardForFixedSlot(NetworkElement ne)
	{
		EquipmentOrHolderT eoh = new EquipmentOrHolderT();
		eoh.setEq(getSystemEquipmentForNE(ne));
		return eoh;
	}

	public static Set<EquipmentSPProperties> getEquipmentSPPropertiesForChassis(Set<EquipmentSPProperties> set, int chassisId)
  {
    Set<EquipmentSPProperties> newSet = new HashSet<EquipmentSPProperties>();
    for (EquipmentSPProperties element : set) {
      if (element.get(EquipmentSPProperties.VI.ChassisId) == chassisId)
      {
        newSet.add(element);
      }
    }
    return newSet;
  }

	public static List<NamingAttributesT> getNamesFromEoHList(List<EquipmentOrHolderT> list)
	{
		List<NamingAttributesT> namesList = new ArrayList<NamingAttributesT>();
		for (EquipmentOrHolderT holder : list) {
			if (holder.getEh() != null)
			{
				namesList.add(holder.getEh().getName().getValue());
			}
			else
			{
				namesList.add(holder.getEq().getName().getValue());
			}
		}
		return namesList;
	}

	public static EquipmentOrHolderListT getContainedEquipment(MtosiAddress mtosiAddress)
			throws ProcessingFailureException
	{
		// if shelf EH, then get shelf EQ and get PSU, FAN, SFP Holders only
		// if Slot, then get contained EQ only
		// NEEDS TO CHANGE, if shelf, getEquipment for shelf
		// if fixed slot, get equipment for fixed slot
		// if slot, get contained eq only, so should be able to use
		// the existing methods, but check what it is first.
		EquipmentOrHolderListT list = new EquipmentOrHolderListT();
		int entityType = mtosiAddress.getNmsEntityType();
		EquipmentOrHolderT eoh;
		switch (entityType)
		{
			case EntityType.POWER_SUPPLY:
				eoh = getPSUEquipmentForNaming(mtosiAddress);
				list.getEoh().add(eoh);
				break;
			case EntityType.MODULE:
				eoh = getSFPEquipmentForNaming(mtosiAddress);
				if (eoh == null) {
				      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, "The specified entity does not exist.");
				}
				list.getEoh().add(eoh);
				break;
			case EntityType.FAN:
				eoh = getFanEquipmentForNaming(mtosiAddress);
				list.getEoh().add(eoh);
				break;
			case EntityType.SHELF:
				List<EquipmentOrHolderT> allForShelf = getContainedEquipmentForShelf(mtosiAddress);
				list.getEoh().addAll(allForShelf);
				break;
			case EntityType.SLOT:
				List<EquipmentOrHolderT> allForSlot = getContainedForFixedSlot(mtosiAddress);
				list.getEoh().addAll(allForSlot);
				break;
			default:
				return list;
		}
		return list;
	}

	public static List<EquipmentOrHolderT> getContainedEquipmentForShelf(MtosiAddress mtosiAddress)
	{
    NetworkElement ne = mtosiAddress.getNE();
		// get Shelf EQ and the PSU, SFP, FAN holders
		// Naming will be shelf, so need to get contained PSU, SFP, FAN
		List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
		// EquipmentOrHolderT shelfEQ = getShelfEquipmentForNaming(mtosiAddress);
		// list.add(shelfEQ);
		final int type = mtosiAddress.getNeType();
		switch (type)
		{
			case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
        list.addAll(getContainedSlotsForShelfCP(ne));
				break;
      case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
        list.addAll(getContainedSlotsForShelfHN(mtosiAddress));
        break;
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
				list.addAll(getContainedSlotsForShelfCM(mtosiAddress));
				break;
      default:
				return list;
		}
		return list;
	}

	public static EquipmentOrHolderT getFixedSlotForNaming(MtosiAddress mtosiAddress)
	{
		final int type = mtosiAddress.getNeType();
		switch (type)
		{
			case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
        return getFixedSlotForNamingCP(mtosiAddress);
      case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
        return getFixedSlotForNamingHN(mtosiAddress);
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
          return getFixedSlotForNamingCM(mtosiAddress);
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
				return getFixedSlotForNamingGE(mtosiAddress);
      default:
				return null;
		}
	}


	public static EquipmentOrHolderT getFixedSlotForNamingCP(MtosiAddress mtosiAddress)
  {
    NetworkElement ne = mtosiAddress.getNE();
    EquipmentSPProperties properties = null;
    Set<EquipmentSPProperties> modulePropsSet;
    if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP) {
      modulePropsSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.SHELF);
    }
    else {
      modulePropsSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.MODULE);
    }
    // we have to find the system module
    for (EquipmentSPProperties props : modulePropsSet) {
      if (!props.isSFP()) {
        properties = props;
        break;
      }
    }
    if (properties == null) {
      return null;
    }
    MtosiInventoryType inventoryType = mtosiAddress.getMtosiInventoryType();
    if (inventoryType == MtosiInventoryType.EQUIPMENT)
    {
      return  NetworkElementMediatorFactory.createNetworkElementMediator(ne).shelfToMtosiEquipment(properties);
    }
    return EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipmentHolder(ne, properties);
  }

  public static EquipmentOrHolderT getFixedSlotForNamingHN(MtosiAddress mtosiAddress) {
    NetworkElement ne = mtosiAddress.getNE();
    NamingAttributesT naming = mtosiAddress.getNaming();
    if (naming.getEhNm() == null)
      return null;
    int shelfIndex = mtosiAddress.getShelfNumber();
    int slotIndex = mtosiAddress.getSlotNumber();
    Module slot = ne.getMTOSIWorker().getModule(shelfIndex, slotIndex);
    if (slot != null) {
      EquipmentSPProperties properties = slot.getEquipmentSPProperties();
      MtosiInventoryType inventoryType = mtosiAddress.getMtosiInventoryType();
      if (inventoryType == MtosiInventoryType.EQUIPMENT) {
        EquipmentOrHolderT eqOrEh = NetworkElementMediatorFactory.createNetworkElementMediator(ne).shelfToMtosiEquipment(properties);
        if (slotIndex == 2) {
          ObjectFactory objFactory = new ObjectFactory();
          EquipmentT equipment = eqOrEh.getEq();
          ModuleHN4000 hnModule = (ModuleHN4000) slot;
          String expectedEquipment = NetworkElementDiscoveryHN4000.ModuleTypeEnum.getModuleName(hnModule.getAssignedType());
          String installedEquipment = NetworkElementDiscoveryHN4000.ModuleTypeEnum.getModuleName(hnModule.getEquippedType());
          equipment.setExpectedEquipmentObjectType(objFactory.
                  createEquipmentTExpectedEquipmentObjectType(expectedEquipment));
          equipment.setInstalledEquipmentObjectType(objFactory
                  .createEquipmentTInstalledEquipmentObjectType(installedEquipment));
        }
        return eqOrEh;
      }
      return EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipmentHolder(ne, properties);
    }
    return null;
  }

  public static EquipmentOrHolderT getFixedSlotForNamingCM(MtosiAddress mtosiAddress)
  {
    NetworkElementFSP150CM ne = (NetworkElementFSP150CM)mtosiAddress.getNE();
    NetworkElementFSP150CMMTOSIOperations mtosiWorker = getFSP150CMMTOSIWorker(ne);
    int shelfIndex = mtosiAddress.getShelfNumber();
    int slotIndex = mtosiAddress.getNmsFixedSlotNumber();
    String equipmentType = null;
    String mismatchType = "";
    EquipmentSPPropertiesFSP150CM props = null;
    switch (mtosiAddress.getFixedSlotCardType()) {
      case MIBFSP150CM.Entity.SlotTable.TYPE_GENERIC_INDEX: {
        MTOSICardModuleF3 card = mtosiWorker.getModule(shelfIndex, slotIndex);
        if (card != null) {
          props = (EquipmentSPPropertiesFSP150CM) card.getEquipmentSPProperties();
          if (card instanceof MTOSINTUFSP150CM) {
            equipmentType = MtosiConstants.EQUIPMENT_NTU_GE;
            mismatchType = MtosiConstants.EQUIPMENT_NTE_GE_SYNC;
          } else if(card instanceof MTOSINTEF3 && ne instanceof NetworkElementFSPGE20X) {
            equipmentType = MtosiConstants.EQUIPMENT_NTE_GE;
          } else if (card instanceof MTOSINTEF3) {
              equipmentType = MtosiConstants.EQUIPMENT_NTE_GE_SYNC;
            mismatchType = MtosiConstants.EQUIPMENT_NTU_GE;
          } else {
            equipmentType = MtosiConstants.EQUIPMENT_NEMI;
          }
        }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_SCU_INDEX: {
          MTOSICardModuleF3 card = mtosiWorker.getModule(shelfIndex, slotIndex);
          if (card != null) {
            props = (EquipmentSPPropertiesFSP150CM) card.getEquipmentSPProperties();
            if (card instanceof ScuTFSP150CMImpl) {
              equipmentType = MtosiConstants.EQUIPMENT_SCU_T;
              mismatchType = MtosiConstants.EQUIPMENT_SCU;
            } else {
              equipmentType = MtosiConstants.EQUIPMENT_SCU;
              mismatchType = MtosiConstants.EQUIPMENT_SCU_T;
            }
          }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_PSU_INDEX: {
        PowerSupplyF3 psu = mtosiWorker.getPSU(shelfIndex, slotIndex);
        if (psu != null) {
          props = (EquipmentSPPropertiesFSP150CM) psu.getEquipmentSPProperties();
          
          int psuType = ((PsuSPPropertiesFSP150CM)psu.getEquipmentSPProperties()).get(PsuSPPropertiesFSP150CM.VI.PsuType);
		  if (psuType == MIBFSP150CM.Entity.PsuTable.TYPE_AC_INDEX) {
            equipmentType = MtosiConstants.EQUIPMENT_PSU_AC;
          } else if (psuType == MIBFSP150CM.Entity.PsuTable.TYPE_DC_INDEX){
        	equipmentType = MtosiUtils.getGE20XPsuType(props.get(EquipmentSPProperties.VS.ModelName));
          } else {
        	equipmentType = MtosiConstants.EQUIPMENT_PSU;
          }
        }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_FAN_INDEX: {
        MTOSIFanF3 fan = mtosiWorker.getFAN(shelfIndex, slotIndex);
        if (fan != null) {
          props = (EquipmentSPPropertiesFSP150CM) fan.getEquipmentSPProperties();
          equipmentType = MtosiConstants.EQUIPMENT_FAN;
        }
        break;
      }
      default:
        return null;
    }
    if (mtosiAddress.getMtosiInventoryType() == MtosiInventoryType.EQUIPMENT)
    {
      if (props != null) {
        return EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipment(ne, props, equipmentType,mismatchType);
      }
      return null;
    }
    else
    {
      boolean installed = true;
      if (props == null) {
        props = new EquipmentSPPropertiesFSP150CM();
        props.set(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex, shelfIndex);
        props.set(EquipmentSPPropertiesFSP150CM.VI.SlotIndex, slotIndex);
        installed = false;
      }
      props.set(EquipmentSPProperties.VL.ManufactureDate, ne.getMTOSIWorker().getShelf(shelfIndex).getEquipmentSPProperties().get(EquipmentSPProperties.VL.ManufactureDate));
      return EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipmentHolder(ne, props, installed);
    }
  }

    public static EquipmentOrHolderT getFixedSlotForNamingGE(MtosiAddress mtosiAddress)
    {
        NetworkElementFSP150CM ne = (NetworkElementFSP150CM)mtosiAddress.getNE();
        NetworkElementFSP150CMMTOSIOperations mtosiWorker = getFSP150CMMTOSIWorker(ne);
        int shelfIndex = mtosiAddress.getShelfNumber();
        int slotIndex = mtosiAddress.getNmsFixedSlotNumber();
        String psuName = mtosiAddress.getPsuNameForGE();
        String equipmentType = null;
        String mismatchType = "";
        EquipmentSPPropertiesFSP150CM props = null;
        int slotCardType = mtosiAddress.getFixedSlotCardType();
        switch (slotCardType) {
            case MIBFSP150CM.Entity.SlotTable.TYPE_GENERIC_INDEX: {
                MTOSICardModuleF3 card = mtosiWorker.getModule(shelfIndex, slotIndex);
                if (card != null) {
                    props = (EquipmentSPPropertiesFSP150CM) card.getEquipmentSPProperties();
                    if (card instanceof MTOSINTUFSP150CM) {
                        equipmentType = MtosiConstants.EQUIPMENT_NTU_GE;
                        mismatchType = MtosiConstants.EQUIPMENT_NTE_GE_SYNC;
                    } else if(card instanceof MTOSINTEF3 && ne instanceof NetworkElementFSPGE20X) {
                        equipmentType = MtosiConstants.EQUIPMENT_NTE_GE;
                    } else if (card instanceof MTOSINTEF3) {
                        equipmentType = MtosiConstants.EQUIPMENT_NTE_GE_SYNC;
                        mismatchType = MtosiConstants.EQUIPMENT_NTU_GE;
                    } else {
                        equipmentType = MtosiConstants.EQUIPMENT_NEMI;
                    }
                }
                break;
            }
            case MIBFSP150CM.Entity.SlotTable.TYPE_SCU_INDEX: {
                MTOSICardModuleF3 card = mtosiWorker.getModule(shelfIndex, slotIndex);
                if (card != null) {
                    props = (EquipmentSPPropertiesFSP150CM) card.getEquipmentSPProperties();
                    if (card instanceof ScuTFSP150CMImpl) {
                        equipmentType = MtosiConstants.EQUIPMENT_SCU_T;
                        mismatchType = MtosiConstants.EQUIPMENT_SCU;
                    } else {
                        equipmentType = MtosiConstants.EQUIPMENT_SCU;
                        mismatchType = MtosiConstants.EQUIPMENT_SCU_T;
                    }
                }
                break;
            }
            case MIBFSP150CM.Entity.SlotTable.TYPE_PSU_INDEX: {
                if(ne.getNetworkElementType() == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201 ||
                    ne.getNetworkElementType() == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE)  {
                  psuName = psuName.replace("PSU-","PSU-1-1-");
                }
                PowerSupplyF3 psu = mtosiWorker.getPSU(psuName);
                if (psu != null) {
                    props = (EquipmentSPPropertiesFSP150CM) psu.getEquipmentSPProperties();

                    int psuType = ((PsuSPPropertiesFSP150CM)psu.getEquipmentSPProperties()).get(PsuSPPropertiesFSP150CM.VI.PsuType);
                    if (psuType == MIBFSP150CM.Entity.PsuTable.TYPE_AC_INDEX) {
                        equipmentType = MtosiConstants.EQUIPMENT_PSU_AC;
                    } else if (psuType == MIBFSP150CM.Entity.PsuTable.TYPE_DC_INDEX){
                        equipmentType = MtosiUtils.getGE20XPsuType(props.get(EquipmentSPProperties.VS.ModelName));
                    } else {
                        equipmentType = MtosiConstants.EQUIPMENT_PSU;
                    }
                }
                break;
            }
            case MIBFSP150CM.Entity.SlotTable.TYPE_FAN_INDEX: {
                MTOSIFanF3 fan = mtosiWorker.getFAN(shelfIndex, slotIndex);
                if (fan != null) {
                    props = (EquipmentSPPropertiesFSP150CM) fan.getEquipmentSPProperties();
                    equipmentType = MtosiConstants.EQUIPMENT_FAN;
                }
                break;
            }
            default:
                return null;
        }
        if (mtosiAddress.getMtosiInventoryType() == MtosiInventoryType.EQUIPMENT)
        {
            if (props != null) {
                return EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipment(ne, props, equipmentType,mismatchType);
            }
            return null;
        }
        else
        {
            boolean installed = true;
            if (props == null) {
                props = new EquipmentSPPropertiesFSP150CM();
                props.set(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex, shelfIndex);
                props.set(EquipmentSPPropertiesFSP150CM.VI.SlotIndex, slotIndex);
                installed = false;
            }
            props.set(EquipmentSPProperties.VL.ManufactureDate, ne.getMTOSIWorker().getShelf(shelfIndex).getEquipmentSPProperties().get(EquipmentSPProperties.VL.ManufactureDate));
            return EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipmentHolder(ne, props, installed);
        }
    }


	public static List<EquipmentOrHolderT> getContainedForFixedSlot(MtosiAddress mtosiAddress)
	{
		// get Shelf EQ and the PSU, SFP, FAN holders
		// Naming will be shelf, so need to get contained PSU, SFP, FAN
		List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
    NetworkElement ne = mtosiAddress.getNE();
		int type = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI();
		switch (type)
		{
			case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
			case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
				list.addAll(getContainedForFixedSlotCP(ne));
				break; 
			case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
			case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
				list.addAll(getContainedForFixedSlotHN(mtosiAddress));
				break;
			case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
			case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
			case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
			case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
				list.addAll(getContainedForFixedSlotCM(mtosiAddress));
				break;
      default:
				return list;
		}
		return list;
	}


	public static List<EquipmentOrHolderT> getContainedForFixedSlotCP(NetworkElement ne)
	{
		List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
		// get all SFP equipment and holders
		Set<EquipmentSPProperties> sfpSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.MODULE);
		List<EquipmentOrHolderT> allSFP = EquipmentMediator.nmsSFPArrayToMtosiEquipmentHolders(ne, sfpSet);
		list.addAll(allSFP);
		// add the card
		EquipmentOrHolderT eoh = getCardForFixedSlot(ne);
		list.add(eoh);
		return list;
	}

  public static List<EquipmentOrHolderT> getContainedForFixedSlotHN(MtosiAddress mtosiAddress)
  {
    NetworkElement ne = mtosiAddress.getNE();
    NamingAttributesT naming = mtosiAddress.getNaming();
    List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
    if (naming.getEhNm() == null)
      return list;
    int shelfIndex = mtosiAddress.getShelfNumber();
    int slotIndex = mtosiAddress.getSlotNumber();
    Module slot = ne.getMTOSIWorker().getModule(shelfIndex, slotIndex);
    if (slot != null) {
      list.add(NetworkElementMediatorFactory.createNetworkElementMediator(ne).shelfToMtosiEquipment(slot.getEquipmentSPProperties()));
      // SFP
      List<Module> sfpSet = ne.getMTOSIWorker().getSFPs(shelfIndex, slotIndex);
      Set<EquipmentSPProperties> sfpProps = new HashSet<EquipmentSPProperties>();
      for (Module sfp : sfpSet) {
        sfpProps.add(sfp.getEquipmentSPProperties());
      }
      List<EquipmentOrHolderT> allSFP = EquipmentMediator.nmsSFPArrayToMtosiEquipmentHolders(ne, sfpProps);
      list.addAll(allSFP);
    }
    return list;
  }

  public static List<EquipmentOrHolderT> getContainedForFixedSlotCM(MtosiAddress mtosiAddress)
	{
    NetworkElementFSP150CM ne = (NetworkElementFSP150CM)mtosiAddress.getNE();
    int shelfIndex = mtosiAddress.getShelfNumber();
    int slotIndex = mtosiAddress.getNmsFixedSlotNumber();
    return getContainedSlotEquipment(ne, new SlotF3SPProperties(ne.getNeIndex(), shelfIndex, slotIndex, -1), false);
	}

  public static List<EquipmentOrHolderT> getContainedSlotsForShelfCP(NetworkElement ne)
	{
		List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
		Set<EquipmentSPProperties> shelfSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.SHELF);
		List<EquipmentOrHolderT> allFixedSlots = EquipmentMediator.nmsShelfArrayToMtosiFixedSlots(ne, shelfSet);
		list.addAll(allFixedSlots);
		Set<EquipmentSPProperties> psuSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.POWER_SUPPLY);
		List<EquipmentOrHolderT> allPSU = EquipmentMediator.nmsPSUArrayToMtosiEquipmentHolders(ne, psuSet);
		list.addAll(allPSU);
		Set<EquipmentSPProperties> fanSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.FAN);
		List<EquipmentOrHolderT> allFan = EquipmentMediator.nmsFanArrayToMtosiEquipmentHolders(ne, fanSet);
		list.addAll(allFan);
		return list;
	}

  public static List<EquipmentOrHolderT> getContainedSlotsForShelfHN(MtosiAddress mtosiAddress)
	{
    NetworkElement ne = mtosiAddress.getNE();
    NamingAttributesT naming = mtosiAddress.getNaming();
		List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();
    if (naming.getEhNm() == null)
			return list;
    int shelfIndex = mtosiAddress.getShelfNumber();
    final Equipment shelf = ne.getMTOSIWorker().getShelf(shelfIndex);
    if (shelf != null) {
      list.addAll(getAllEquipmentAndHoldersForFixedSlotHN(ne, shelfIndex, false));
      // PSU
      List<Equipment> psuSet = ne.getMTOSIWorker().getPSUs(shelfIndex);
      Set<EquipmentSPProperties> psuProps = new HashSet<EquipmentSPProperties>();
      for (Equipment psu : psuSet) {
        psuProps.add(psu.getEquipmentSPProperties());
      }
      List<EquipmentOrHolderT> allPSU = EquipmentMediator.nmsPSUArrayToMtosiEquipmentHolders(ne, psuProps);
      list.addAll(allPSU);
      // FAN
      List<Equipment> fanSet = ne.getMTOSIWorker().getFANs(shelfIndex);
      Set<EquipmentSPProperties> fanProps = new HashSet<EquipmentSPProperties>();
      for (Equipment fan : fanSet) {
        fanProps.add(fan.getEquipmentSPProperties());
      }
      List<EquipmentOrHolderT> allFan = EquipmentMediator.nmsFanArrayToMtosiEquipmentHolders(ne, fanProps);
      list.addAll(allFan);
    }
		return list;
	}

  public static List<EquipmentOrHolderT> getContainedSlotsForShelfCM(MtosiAddress mtosiAddress)
	{
    NetworkElement ne = mtosiAddress.getNE();
    List<EquipmentOrHolderT> list = new ArrayList<EquipmentOrHolderT>();

    Set<SlotF3SPProperties> slotProperties = new HashSet<SlotF3SPProperties>();
    Set<Entity> slots = ne.getMTOSIWorker().getEntitiesByType(MIB.Entity.CONTAINER);
    for (Entity slot : slots) {
      slotProperties.add(((SlotF3)slot).getSlotSPProperties());
    }
    int shelfIndex = mtosiAddress.getShelfNumber();
    for (SlotF3SPProperties properties : slotProperties)
    {
      if (properties.get(SlotF3SPProperties.VI.ShelfIndex) == shelfIndex)
      {
        List<EquipmentOrHolderT> slotEquipment = getContainedSlotEquipment((NetworkElementFSP150CM)ne, properties, true);
        list.addAll(slotEquipment);
      }
    }
		return list;
	}

  public static EquipmentOrHolderT getEquipmentOrHolder(MtosiAddress mtosiAddress) throws Exception
	{
		int entityType = mtosiAddress.getNmsEntityType();
		switch (entityType)
		{
			case EntityType.POWER_SUPPLY:
				return getPSUForNaming(mtosiAddress);
			case EntityType.MODULE:
				return getSFPForNaming(mtosiAddress);
			case EntityType.FAN:
				return getFanForNaming(mtosiAddress);
			case EntityType.SHELF:
				return getShelfForNaming(mtosiAddress);
			case EntityType.SLOT:
				return getFixedSlotForNaming(mtosiAddress);
			default:
				return null;
		}
	}

	public static EquipmentOrHolderT getPSUForNaming(MtosiAddress mtosiAddress)
	{
    NetworkElement ne = mtosiAddress.getNE();
    NamingAttributesT naming = mtosiAddress.getNaming();
		Set<EquipmentSPProperties> propertiesSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.POWER_SUPPLY);
		for (EquipmentSPProperties properties : propertiesSet) {
			NamingAttributesT nextNaming;
			MtosiInventoryType inventoryType = mtosiAddress.getMtosiInventoryType();
			if (inventoryType == MtosiInventoryType.EQUIPMENT)
			{
				nextNaming = NamingTranslationFactory.createNamingAttributesEquipment(ne, properties);
				if (MtosiUtils.isNamingAttributesMatch(naming, nextNaming))
				{
					// not checking installed here so provisioned/unplugged shows up in response
					return NetworkElementMediatorFactory.createNetworkElementMediator(ne).psuToMtosiEquipment(properties);
					
				}
			}
			else
			{
				nextNaming = NamingTranslationFactory.createNamingAttributesHolder(ne, properties);
				if (MtosiUtils.isNamingAttributesMatch(naming, nextNaming))
				{
          try {
            properties.set(EquipmentSPProperties.VL.ManufactureDate, ne.getMTOSIWorker().getEquipmentSPProperties(properties.get(EquipmentSPProperties.VE.ContainedIn)).get(EquipmentSPProperties.VL.ManufactureDate));
          }
          catch (ClassCastException e) {
            // this case is possible, because some entities are not equipment
            logger.debug(e, e);
          }
          catch (NoSuchMDObjectException e) {
            logger.warn(e, e);
          }
          return NetworkElementMediatorFactory.createNetworkElementMediator(ne).psuToMtosiEquipmentHolder(properties);
				}
			}
		}
		return null;
	}

	// regarless of whether the naming has eq or eh, get it as equipment
	public static EquipmentOrHolderT getPSUEquipmentForNaming(MtosiAddress mtosiAddress)
	{
    NetworkElement ne = mtosiAddress.getNE();
    NamingAttributesT naming = mtosiAddress.getNaming();
		Set<EquipmentSPProperties> propertiesSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.POWER_SUPPLY);
    for (EquipmentSPProperties properties : propertiesSet) {
      NamingAttributesT nextNaming;
      MtosiInventoryType inventoryType = mtosiAddress.getMtosiInventoryType();
      if (inventoryType == MtosiInventoryType.EQUIPMENT)
      {
        nextNaming = NamingTranslationFactory.createNamingAttributesEquipment(ne, properties);
        if (MtosiUtils.isNamingAttributesMatch(naming, nextNaming))
        {
          return NetworkElementMediatorFactory.createNetworkElementMediator(ne).psuToMtosiEquipment(properties);
        }
      }
      else
      {
        nextNaming = NamingTranslationFactory.createNamingAttributesHolder(ne, properties);
        if (MtosiUtils.isNamingAttributesMatch(naming, nextNaming))
        {
          return NetworkElementMediatorFactory.createNetworkElementMediator(ne).psuToMtosiEquipment(properties);
        }
      }
    }
		return null;
	}

	public static EquipmentOrHolderT getSFPForNaming(MtosiAddress mtosiAddress)
	{
    NetworkElement ne = mtosiAddress.getNE();
    NamingAttributesT naming = mtosiAddress.getNaming();
		Set<EquipmentSPProperties> propertiesSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.MODULE);
		for (EquipmentSPProperties properties : propertiesSet) {
			NamingAttributesT nextNaming;
			MtosiInventoryType inventoryType = mtosiAddress.getMtosiInventoryType();
			if (inventoryType == MtosiInventoryType.EQUIPMENT)
			{
				nextNaming = NamingTranslationFactory.createNamingAttributesEquipment(ne, properties);
				if (MtosiUtils.isNamingAttributesMatch(naming, nextNaming))
				{
					if (MtosiUtils.isInstalled(properties))
					{
						return NetworkElementMediatorFactory.createNetworkElementMediator(ne).sfpToMtosiEquipment(properties);
					}
				}
			}
			else
			{
				nextNaming = NamingTranslationFactory.createNamingAttributesHolder(ne, properties);
				if (MtosiUtils.isNamingAttributesMatch(naming, nextNaming))
        {
          try {
            properties.set(EquipmentSPProperties.VL.ManufactureDate, ne.getMTOSIWorker().getEquipmentSPProperties(properties.get(EquipmentSPProperties.VE.ContainedIn)).get(EquipmentSPProperties.VL.ManufactureDate));
          }
          catch (ClassCastException e) {
            // this case is possible, because some entities are not equipment
            logger.debug(e, e);
          }
          catch (NoSuchMDObjectException e) {
            logger.warn(e, e);
          }
          return NetworkElementMediatorFactory.createNetworkElementMediator(ne).sfpToMtosiEquipmentHolder(properties);
				}
			}
		}
		return null;
	}

	public static EquipmentOrHolderT getSFPEquipmentForNaming(MtosiAddress mtosiAddress)
	{
    NetworkElement ne = mtosiAddress.getNE();
    NamingAttributesT naming = mtosiAddress.getNaming();
		Set<EquipmentSPProperties> propertiesSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.MODULE);
		for (EquipmentSPProperties properties : propertiesSet) {
			if (MtosiUtils.isInstalled(properties) && properties.isSFP()) {
				NamingAttributesT nextNaming;
				MtosiInventoryType inventoryType = mtosiAddress.getMtosiInventoryType();
				if (inventoryType == MtosiInventoryType.EQUIPMENT)
				{
					nextNaming = NamingTranslationFactory.createNamingAttributesEquipment(ne, properties);
					if (MtosiUtils.isNamingAttributesMatch(naming, nextNaming))
					{
						return NetworkElementMediatorFactory.createNetworkElementMediator(ne).sfpToMtosiEquipment(properties);
					}
				}
				else
				{
					nextNaming = NamingTranslationFactory.createNamingAttributesHolder(ne, properties);
					if (MtosiUtils.isNamingAttributesMatch(naming, nextNaming))
					{
						return NetworkElementMediatorFactory.createNetworkElementMediator(ne).sfpToMtosiEquipment(properties);
					}
				}
			}
		}
		return null;
	}

	public static EquipmentOrHolderT getFanForNaming(MtosiAddress mtosiAddress)
	{
    NetworkElement ne = mtosiAddress.getNE();
    NamingAttributesT naming = mtosiAddress.getNaming();
		Set<EquipmentSPProperties> propertiesSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.FAN);
		for (EquipmentSPProperties properties : propertiesSet) {
			NamingAttributesT nextNaming;
			MtosiInventoryType inventoryType = mtosiAddress.getMtosiInventoryType();
			if (inventoryType == MtosiInventoryType.EQUIPMENT)
			{
				nextNaming = NamingTranslationFactory.createNamingAttributesEquipment(ne, properties);
				if (MtosiUtils.isNamingAttributesMatch(naming, nextNaming))
				{
					return NetworkElementMediatorFactory.createNetworkElementMediator(ne).fanToMtosiEquipment(properties);
				}
			}
			else
			{
				nextNaming = NamingTranslationFactory.createNamingAttributesHolder(ne, properties);
				if (MtosiUtils.isNamingAttributesMatch(naming, nextNaming))
				{
          try {
            properties.set(EquipmentSPProperties.VL.ManufactureDate, ne.getMTOSIWorker().getEquipmentSPProperties(properties.get(EquipmentSPProperties.VE.ContainedIn)).get(EquipmentSPProperties.VL.ManufactureDate));
          }
          catch (ClassCastException e) {
            // this case is possible, because some entities are not equipment
            logger.debug(e, e);
          }
          catch (NoSuchMDObjectException e) {
            logger.warn(e, e);
          }
          return NetworkElementMediatorFactory.createNetworkElementMediator(ne).fanToMtosiEquipmentHolder(properties);
				}
			}
		}
		return null;
	}

	public static EquipmentOrHolderT getFanEquipmentForNaming(MtosiAddress mtosiAddress)
	{
    NetworkElement ne = mtosiAddress.getNE();
    NamingAttributesT naming = mtosiAddress.getNaming();
		Set<EquipmentSPProperties> propertiesSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.FAN);
		for (EquipmentSPProperties properties : propertiesSet) {
			NamingAttributesT nextNaming;
			MtosiInventoryType inventoryType = mtosiAddress.getMtosiInventoryType();
			if (inventoryType == MtosiInventoryType.EQUIPMENT)
			{
				nextNaming = NamingTranslationFactory.createNamingAttributesEquipment(ne, properties);
				if (MtosiUtils.isNamingAttributesMatch(naming, nextNaming))
				{
					return NetworkElementMediatorFactory.createNetworkElementMediator(ne).fanToMtosiEquipment(properties);
				}
			}
			else
			{
				nextNaming = NamingTranslationFactory.createNamingAttributesHolder(ne, properties);
				if (MtosiUtils.isNamingAttributesMatch(naming, nextNaming))
				{
					return NetworkElementMediatorFactory.createNetworkElementMediator(ne).fanToMtosiEquipment(properties);
				}
			}
		}
		return null;
	}

	public static EquipmentOrHolderT getShelfForNaming(MtosiAddress mtosiAddress)
	{
		NamingAttributesT naming = mtosiAddress.getNaming();
		NetworkElement ne = mtosiAddress.getNE();
		Set<EquipmentSPProperties> propertiesSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.SHELF);
		for (EquipmentSPProperties properties : propertiesSet) {
			NamingAttributesT nextNaming;
			MtosiInventoryType inventoryType = mtosiAddress.getMtosiInventoryType();
			if (inventoryType == MtosiInventoryType.EQUIPMENT)
			{
				nextNaming = NamingTranslationFactory.createNamingAttributesEquipment(ne, properties);
				if (MtosiUtils.isNamingAttributesMatch(naming, nextNaming))
				{
					return NetworkElementMediatorFactory.createNetworkElementMediator(ne).shelfToMtosiEquipment(properties);
				}
			}
			else
			{
				nextNaming = NamingTranslationFactory.createNamingAttributesHolder(ne, properties);
				if (MtosiUtils.isNamingAttributesMatch(naming, nextNaming))
				{
					return EquipmentMediator.nmsShelfPropertiesToMTOSIEquipmentHolder(ne, properties);
				}
			}
		}
		return null;
	}

	public static EquipmentOrHolderT getShelfEquipmentForNaming(MtosiAddress mtosiAddress)
	{
		NetworkElement ne = mtosiAddress.getNE();
    NamingAttributesT naming = mtosiAddress.getNaming();
    Set<EquipmentSPProperties> propertiesSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.SHELF);
		for (EquipmentSPProperties properties : propertiesSet) {
			NamingAttributesT nextNaming;
			MtosiInventoryType inventoryType = mtosiAddress.getMtosiInventoryType();
			if (inventoryType == MtosiInventoryType.EQUIPMENT)
			{
				nextNaming = NamingTranslationFactory.createNamingAttributesEquipment(ne, properties);
				if (MtosiUtils.isNamingAttributesMatch(naming, nextNaming))
				{
					return NetworkElementMediatorFactory.createNetworkElementMediator(ne).shelfToMtosiEquipment(properties);
				}
			}
			else
			{
				nextNaming = NamingTranslationFactory.createNamingAttributesHolder(ne, properties);
				if (MtosiUtils.isNamingAttributesMatch(naming, nextNaming))
				{
					return NetworkElementMediatorFactory.createNetworkElementMediator(ne).shelfToMtosiEquipment(properties);
				}
			}
		}
		return null;
	}
}
