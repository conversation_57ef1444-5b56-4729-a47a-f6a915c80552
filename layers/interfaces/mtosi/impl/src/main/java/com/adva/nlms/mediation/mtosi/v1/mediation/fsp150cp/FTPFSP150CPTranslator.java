/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cp;

import v1.tmf854.LayeredParametersListT;

import com.adva.nlms.mediation.common.serviceProvisioning.FTPFSP150CPSPProperties;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.FTPFSP150CPImpl;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.mediation.FTPTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.ActiveNetworkTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.ProtectionTranslation;

/**
 * This class is an FSP 150 CP FTP MTOSI Translator.
 */
public class FTPFSP150CPTranslator extends FTPTranslator {
  private FTPFSP150CPImpl ftp;

  public FTPFSP150CPTranslator (FTPFSP150CPImpl ftp) {
    super(ftp);
    this.ftp = ftp;
  }

  @Override
  protected void fillProtection150cpLayer (LayeredParametersListT layeredParametersListT) {
    FTPFSP150CPSPProperties ftpProps = ftp.getFTPSPProperties();
    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_PROTECTION150CP);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_PROTECTION150CP,
            LayeredParams.LrPropAdvaProtection150CP.PROTECTION_TYPE_PARAM, ProtectionTranslation
            .getMtosiString(ftpProps.get(FTPFSP150CPSPProperties.VI.ProtectionType)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_PROTECTION150CP,
            LayeredParams.LrPropAdvaProtection150CP.PROTECTION_STATUS_PARAM, ProtectionTranslation
            .getMtosiString(ftpProps.get(FTPFSP150CPSPProperties.VI.ProtectionStatus)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_PROTECTION150CP,
            LayeredParams.LrPropAdvaProtection150CP.ACTIVE_NETWORK_PARAM, ActiveNetworkTranslation
            .getMtosiString(ftpProps.get(FTPFSP150CPSPProperties.VI.ActiveNetwork)));
    // -------end of Layer-------
  }
}
