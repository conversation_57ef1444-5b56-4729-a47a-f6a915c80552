/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.DeleteCTPResponseT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public abstract class DeleteCTPWorker extends AbstractMtosiWorker
{
	Logger LOG = LogManager.getLogger(this.getClass().getName());

	protected DeleteCTPResponseT response = new DeleteCTPResponseT();
	protected String ctpName;
	protected String ptpName;
	protected NamingAttributesT namingAttributes;
  protected NetworkElement ne;

  public DeleteCTPWorker(Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne)
	{
    super(mtosiHeader, "deleteCTP", "deleteCTP", "deleteCTPResponse");
    this.namingAttributes = namingAttributes;
    this.ne = ne;
  }

  @Override
  protected void parse() throws Exception
  {
    if ((this.ctpName = namingAttributes.getCtpNm()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ctpNm has not been specified.");
    }
    if ((this.ptpName = namingAttributes.getPtpNm()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ptpNm has not been specified.");
    }
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(ne.getDefaultNetworkElementTypeString());
  }

  @Override
  public DeleteCTPResponseT getSuccessResponse()
	{
		if (response == null)
			return null;
		response.setTmf854Version(MtosiConstants.VERSION);
		return response;
	}
}