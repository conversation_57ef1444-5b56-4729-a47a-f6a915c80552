/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker;

import jakarta.xml.ws.Holder;

import jakarta.xml.bind.JAXBElement;
import v1.tmf854.HeaderT;
import v1.tmf854.TPDataListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.DeactivateAndDeleteFDFrResponseT;
import v1.tmf854ext.adva.DeactivateAndDeleteFDFrT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.common.validation.SPCondition;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.mtosi.FDFr;

abstract public class  DeactivateAndDeleteFDFrWorker extends AbstractMtosiWorker
{
	protected DeactivateAndDeleteFDFrResponseT response = new DeactivateAndDeleteFDFrResponseT();
  protected DeactivateAndDeleteFDFrT mtosiBody;
  protected TPDataListT tpsToModify;
  protected NamingAttributesT namingAttributes;
  protected NetworkElement ne;
  protected FDFr fdfr;

  public DeactivateAndDeleteFDFrWorker (DeactivateAndDeleteFDFrT mtosiBody, Holder<HeaderT> mtosiHeader, NetworkElement ne, FDFr fdfr)
	{
    super(mtosiHeader, "deactivateAndDeleteFDFr", "deactivateAndDeleteFDFr", "deactivateAndDeleteFDFrResponse");
    this.mtosiBody = mtosiBody;
    this.ne = ne;
    this.fdfr = fdfr;
  }

  @Override
  public void parse() throws Exception {
    JAXBElement<TPDataListT> jaxTPList = mtosiBody.getTpsToModify();
    if (jaxTPList != null)
    {
      this.tpsToModify = jaxTPList.getValue();
    }
    if ((namingAttributes = mtosiBody.getFdfrName()) == null) {
      throw new SPValidationException("FDFr name is missing.", SPCondition.NAME_PARSING_FAILED);
    }
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(ne.getDefaultNetworkElementTypeString());
  }

  @Override
  public DeactivateAndDeleteFDFrResponseT getSuccessResponse()
	{
		if (response == null)
			return null;
		response.setTmf854Version(MtosiConstants.VERSION);
		return response;
	}
}
