/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>riz<PERSON>s
 */
package com.adva.nlms.mediation.mtosi.v1.worker.cfm;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.MDAttr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp20X.FspGE20XCFMMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import v1.tmf854ext.adva.GetMaintenanceDomainResponseT;
import v1.tmf854ext.adva.GetMaintenanceDomainT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public class GetMaintenanceDomainWorker extends AbstractMtosiWorker {
  private final GetMaintenanceDomainT mtosiBody;
  private MtosiAddress mtosiAddress;
  protected GetMaintenanceDomainResponseT response = new GetMaintenanceDomainResponseT();
  private MtosiMOFacade facade;
  private NetworkElement ne;
  private DTO<MDAttr> md;
  Logger LOG = LogManager.getLogger(this.getClass().getName());

  public GetMaintenanceDomainWorker(GetMaintenanceDomainT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getMaintenanceDomain", "getMaintenanceDomain", "getMaintenanceDomainResponse");
    this.mtosiBody = mtosiBody;
    this.mtosiHeader = mtosiHeader;
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    validator.validate(mtosiAddress.getNE().getDefaultNetworkElementTypeString());
  }

  @Override
  protected void parse() throws Exception {
    NamingAttributesT meName = mtosiBody.getMdName();

    if (meName == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "MeName has not been specified or is invalid.");
    }
    mtosiAddress = new MtosiAddress(meName);
    ne = mtosiAddress.getNE();
    if (mtosiAddress.getNaming().getMdNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MD_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    if (!mtosiAddress.getNaming().getMdNm().equals(OSFactory.getMDNm())) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MD_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if (mtosiAddress.getNaming().getMeNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.ME_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    MtosiUtils.validateNE(ne);

    if (mtosiAddress.getNaming().getCfmMdNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MDFR_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }

  @Override
  protected void mediate() throws Exception {
    facade = getMtosiCtrl().getMoFacadeManager().getFacadeViaNeId(MtosiMOFacade.class, ne.getID());
    md = facade.findDTOViaMtosiName(ne.getID(),  "/cfmmd="+ mtosiAddress.getNaming().getCfmMdNm(), MDAttr.class);
    if (md == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_CAPACITY_EXCEEDED,
          MtosiErrorConstants.MDFR_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }

  @Override
  protected void response() {
    v1.tmf854ext.adva.ObjectFactory factory = new v1.tmf854ext.adva.ObjectFactory();
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    response.setMdName(factory.createCreateMaintenanceDomainResponseTMdName(mtosiBody.getMdName()));
    response.setDiscoveredName(factory.createCreateMaintenanceDomainResponseTDiscoveredName(mtosiAddress.getNaming().getCfmMdNm()));
    response.setNamingOS(factory.createCreateMaintenanceDomainResponseTNamingOS(OSFactory.getNmsName()));
    response.setSource(factory.createCreateMaintenanceDomainResponseTSource(source));
    response.setTransmissionParams(factory.createCreateMaintenanceDomainResponseTTransmissionParams(new FspGE20XCFMMediator().toMtosiMD(md)));
  }

  @Override
  public Object getSuccessResponse() {
    if (response == null)
      return null;
    return response;
  }
}
