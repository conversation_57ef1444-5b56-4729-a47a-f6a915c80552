/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */
package com.adva.nlms.mediation.mtosi.v1.worker.notification;

import com.adva.nlms.mediation.mtosi.common.notification.MtosiNotificationConstants;
import com.adva.nlms.mediation.mtosi.common.notification.MtosiNotificationHelper;
import com.adva.nlms.mediation.mtosi.common.notification.NotificationEnum;
import com.adva.nlms.mediation.mtosi.common.notification.NotificationException;
import com.adva.nlms.mediation.mtosi.common.notification.SubscribeUnsubscribeOperations;
import com.adva.nlms.mediation.mtosi.common.notification.Subscriber;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import v1.tmf854.HeaderT;
import v1.tmf854.SubscribeResponseT;
import v1.tmf854.SubscribeT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.NoSuchAlgorithmException;

public class SubscribeWorker extends AbstractMtosiWorker {

  protected SubscribeT mtosiBody;
  protected SubscribeResponseT response = new SubscribeResponseT();
  private Subscriber subscriber = new Subscriber();
  private String topic;
  private SubscribeUnsubscribeOperations subscribeUnsubscribeOperations;
  private static Logger LOG = LoggerFactory.getLogger(SubscribeWorker.class);

  public SubscribeWorker(SubscribeT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "subscribe", "subscribe", "subscribeResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    //To change body of implemented methods use File | Settings | File Templates.
  }

  @Override
  protected void parse() throws Exception {
    try {
    //will throw an exception if topic does not exist
      MtosiNotificationHelper.topicExists(topic = this.mtosiBody.getTopic());
   } catch (NotificationException nex) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_TOPIC, nex.getMessage());
    }
    try {
      URL url=new URL(mtosiBody.getConsumerEPR()); //check validity of the URL, if this throws an exception the URL is invalid
      if (url.getHost() == null|| url.getHost().isEmpty() || url.getHost().matches("\\s+")){
        LOG.debug("url.getHost() == null|| url.getHost().isEmpty() || url.getHost().matches(\\s+)");
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiNotificationConstants.ERROR_MALFORMED_ENDPOINT);
      }
      if (url.getPort()<0 || url.getPort()>65535){
        LOG.debug("url.getPort()<0 || url.getPort()>65535");
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiNotificationConstants.ERROR_PORT_OOR);
      }
      subscriber.setEndpoint(mtosiBody.getConsumerEPR());
      subscriber.setId(MtosiNotificationHelper.messageDigest(mtosiBody.getConsumerEPR() + mtosiBody.getTopic()));
      subscriber.setTopic(mtosiBody.getTopic());
      subscriber.setMtosiVersion(NotificationEnum.V1.getId());
    }
    catch (NoSuchAlgorithmException nsa){
      LOG.debug(MtosiNotificationConstants.ERROR_WRONG_ALGORITHM,nsa);
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INTERNAL_ERROR, MtosiNotificationConstants.ERROR_WRONG_ALGORITHM);
    }
    catch (MalformedURLException mal) {
      LOG.debug(MtosiNotificationConstants.ERROR_MALFORMED_ENDPOINT,mal);
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiNotificationConstants.ERROR_MALFORMED_ENDPOINT);
    }
  }

  @Override
  protected void mediate() throws Exception {
    try {
      //will throw an exception if user is already subscribed
      setSubscribeUnsubscribeOperations(SubscribeUnsubscribeOperations.getInstance());
      subscribeUnsubscribeOperations.subscribe(subscriber, getMtosiCtrl(),false);
      response.setSubscriptionID(String.valueOf(subscriber.getId()));
    } catch (NotificationException nex) {
      LOG.debug(nex.getMessage());
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, nex.getMessage());
    }

  }

  public SubscribeUnsubscribeOperations getSubscribeUnsubscribeOperations() {
    return subscribeUnsubscribeOperations;
  }

  public void setSubscribeUnsubscribeOperations(SubscribeUnsubscribeOperations subscribeUnsubscribeOperations) {
    this.subscribeUnsubscribeOperations = subscribeUnsubscribeOperations;
  }

  @Override
  protected void response() throws Exception {
    //To change body of implemented methods use File | Settings | File Templates.
  }

  @Override
  public SubscribeResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }

}
