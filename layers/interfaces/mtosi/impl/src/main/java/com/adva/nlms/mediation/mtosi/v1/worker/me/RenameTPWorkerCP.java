/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import jakarta.xml.ws.Holder;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.RenameTPT;

import com.adva.nlms.common.NEUtils;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;

public class RenameTPWorkerCP extends RenameTPWorker {
  public RenameTPWorkerCP (RenameTPT mtosiBody, Holder<HeaderT> mtosiHeader, NamingAttributesT currentName, NetworkElement ne) {
    super(mtosiBody, mtosiHeader, currentName, ne);
  }

  @Override
  protected void mediate() throws Exception {
    if (!NamingTranslationFactory.isFtp(currentName)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The specified TP Name is for an entity type that cannot be renamed.");
    }
    if (!NamingTranslationFactory.isFtp(newName)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The specified new TP Name is for an entity type that cannot be renamed.");
    }
    //
    final String newFtpName = newName.getFtpNm();
    if (NEUtils.reconcileFtpName(1, 1, newFtpName, ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) != null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
        "The specified new TP Name is invalid.");
    }
    

    mediateFtp();
  }

  @Override
  protected void response () throws Exception {
    // empty method
  }
}
