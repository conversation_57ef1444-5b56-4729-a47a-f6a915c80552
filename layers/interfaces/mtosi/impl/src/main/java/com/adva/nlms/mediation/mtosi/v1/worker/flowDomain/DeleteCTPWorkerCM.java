/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CM;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;

import jakarta.xml.ws.Holder;

public class DeleteCTPWorkerCM extends DeleteCTPWorker
{
  public DeleteCTPWorkerCM(Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne) {
    super(mtosiHeader, namingAttributes, ne);
  }

  @Override
  protected void mediate() throws Exception {
    Port port = ManagedElementFactory.getPort(ne, ptpName);
    if (port == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The specified Port was not found.");
    }

    MTOSIFlowF3 flow = ManagedElementFactory.getCMFlow(namingAttributes);

    if (flow == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The specified Flow was not found.");
    }

    NetworkElementFSP150CM cmNE = (NetworkElementFSP150CM) ne;
    if (flow.getFDFr() != null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The specified Flow is an FDFr endpoint and cannot be deleted.");
    }
    transact(cmNE, flow);
  }

  @Override
  protected void response () throws Exception {
    // empty method
  }

  private void transact(NetworkElementFSP150CM cmNE, MTOSIFlowF3 flow)
          throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { cmNE };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "DeleteCTPWorker");
    try {
      logSecurity(cmNE, SystemAction.DeleteNetwork, ptpName + "&&" + ctpName);
      FlowSPPropertiesFSP150CM props = flow.getFlowSPProperties();
//      props.set(FlowSPPropertiesFSP150CM.VB.Active, Boolean.FALSE);
      flow.setFlowSettings(props);
      NetTransactionManager.commitNetTransaction(id);
    } catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      NetTransactionManager.ensureEnd(id);
    }
  }
}
