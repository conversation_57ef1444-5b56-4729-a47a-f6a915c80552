/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

/**
  *
  */
public enum HNMdiModeTranslation {
    StraightThrough     (0),
    CrossOver			(1),
    Auto          		(2),
    unknown				(3);
    
    private int mibValue;
    
    private int getMibValue() {
		return mibValue;
	}

	private HNMdiModeTranslation(int code) {
    	this.mibValue = code;
    }

    public static String getMtosiString(final Integer mibValue) {
    	if (mibValue == null) {
    		return unknown.name();
    	}
    	for (HNMdiModeTranslation value: values() ) {
    		if (value.getMibValue() == mibValue.intValue()) {
    			return value.name(); 
    		}
    	}
		return unknown.name();
    	//the value was not found, return the value passed in
		//return String.valueOf(mibValue);
    }
    
    public static int getMibValue(final String name) {
    	for (HNMdiModeTranslation value: values() ) {
    		if (value.name().equals(name)) {
    			return value.getMibValue(); 
    		}
    	}
    	//Returning -1, the value passed in was not found.
    	return -1;
    }
}
