/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON><PERSON>
 */
package com.adva.nlms.mediation.mtosi.v1.mediation.f3;

import com.adva.nlms.mediation.config.f3.NetworkElementF3;
import com.adva.nlms.mediation.mtosi.v1.mediation.NetworkElementMediator;

public abstract class FspF3Mediator extends NetworkElementMediator {
  protected NetworkElementF3 ne;

  public FspF3Mediator(NetworkElementF3 ne) {
    super(ne);
    this.ne = ne;
  }
}
