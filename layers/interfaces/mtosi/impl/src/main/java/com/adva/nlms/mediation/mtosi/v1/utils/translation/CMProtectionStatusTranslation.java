/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum CMProtectionStatusTranslation implements TranslatableEnum, com.adva.nlms.mediation.mtosi.v2.utils.translations.f3.TranslatableEnum {
  NoOutstandingRequest (1, "NoOutstandingRequest"),
  SignalFailProtect    (2, "SignalFailProtect"),
  SignalFailWorking    (3, "SignalFailWorking"),
  SignalDegradeProtect (4, "SignalDegradeProtect"),
  SignalDegradeWorking (5, "SignalDegradeWorking"),
  ManualProtect        (6, "ManualProtect"),
  ManualWorking        (7, "ManualWorking"),
  ForcedProtect        (8, "ForcedProtect"),
  ForcedWorking        (9, "ForcedWorking"),
  LockoutProtect       (10, "LockoutProtect"),
  WaitToRestore        (11, "WaitToRestore"),
  NOT_APPLICABLE       (12, "Unknown");

  private final int    mibValue;
  private final String mtosiString;

  private CMProtectionStatusTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}