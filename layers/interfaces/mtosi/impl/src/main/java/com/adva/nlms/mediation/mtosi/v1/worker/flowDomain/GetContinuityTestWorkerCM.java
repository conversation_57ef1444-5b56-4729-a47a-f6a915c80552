/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.serviceProvisioning.ContinuityTestResultPropertiesFSP150CM;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.f3.entity.port.acc.MTOSIPortF3Acc;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.common.snmp.TestResultTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.TestStatusTranslationFSP150CM;
import v1.tmf854.HeaderT;
import v1.tmf854.NVSListT;
import v1.tmf854.NameAndStringValueT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;

import jakarta.xml.ws.Holder;

public class GetContinuityTestWorkerCM extends GetContinuityTestWorker {
  private String ctpName;
  private ContinuityTestResultPropertiesFSP150CM result;

  public GetContinuityTestWorkerCM (Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne) {
    super(mtosiHeader, namingAttributes, ne);
  }

  @Override
  protected void parse() throws Exception {
    super.parse();
    if ((this.ctpName = namingAttributes.getCtpNm()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ctpNm has not been specified.");
    }
  }

  @Override
  protected void mediate() throws Exception {
    super.mediate();
    if (!(port instanceof MTOSIPortF3Acc)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "Continuity test should be run on ACC Port.");
    }
    MTOSIPortF3Acc portACC = (MTOSIPortF3Acc) port;
    MTOSIFlowF3 flow;
    if ((flow = portACC.getFlowFSP150CMByName(ctpName)) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.CTP_NOT_FOUND);
    }
    result = flow.getContinuityTest();
  }

  @Override
  protected void response() throws Exception {
    response.setTestResults(getTestResults());
    response.setTestStatus(TestStatusTranslationFSP150CM.getMtosiString(result.get(ContinuityTestResultPropertiesFSP150CM.VI.TestStatus)));
  }

  private NVSListT getTestResults() {
    final ObjectFactory objFactory = new ObjectFactory();
    final NVSListT nvsListT = objFactory.createNVSListT();

    NameAndStringValueT nameAndStringValueT = objFactory.createNameAndStringValueT();
    nameAndStringValueT.setName("TestResult");
    if (result.get(ContinuityTestResultPropertiesFSP150CM.VL.MonTxFrames) == 0) {
      nameAndStringValueT.setValue(TestResultTranslation.getMtosiString(2/*Fail*/));
    } else {
      nameAndStringValueT.setValue(TestResultTranslation.getMtosiString(result.get(ContinuityTestResultPropertiesFSP150CM.VI.TestResult)));
    }
    nvsListT.getNvs().add(nameAndStringValueT);

    nameAndStringValueT = objFactory.createNameAndStringValueT();
    nameAndStringValueT.setName("TxFrames");
    nameAndStringValueT.setValue(Long.toString(result.get(ContinuityTestResultPropertiesFSP150CM.VL.MonTxFrames)));
    nvsListT.getNvs().add(nameAndStringValueT);

    nameAndStringValueT = objFactory.createNameAndStringValueT();
    nameAndStringValueT.setName("RxTotalFrames");
    nameAndStringValueT.setValue(Long.toString(result.get(ContinuityTestResultPropertiesFSP150CM.VL.MonRxFrames)));
    nvsListT.getNvs().add(nameAndStringValueT);

    nameAndStringValueT = objFactory.createNameAndStringValueT();
    nameAndStringValueT.setName("RxNonEcpaFrames");
    nameAndStringValueT.setValue(Long.toString(result.get(ContinuityTestResultPropertiesFSP150CM.VL.MonRxNonEcpaFrames)));
    nvsListT.getNvs().add(nameAndStringValueT);

    nameAndStringValueT = objFactory.createNameAndStringValueT();
    nameAndStringValueT.setName("RxMinDelay");
    nameAndStringValueT.setValue(Long.toString(result.get(ContinuityTestResultPropertiesFSP150CM.VL.MonRxMinDelay)));
    nvsListT.getNvs().add(nameAndStringValueT);

    nameAndStringValueT = objFactory.createNameAndStringValueT();
    nameAndStringValueT.setName("RxAvgDelay");
    nameAndStringValueT.setValue(Long.toString(result.get(ContinuityTestResultPropertiesFSP150CM.VL.MonRxAvgDelay)));
    nvsListT.getNvs().add(nameAndStringValueT);

    nameAndStringValueT = objFactory.createNameAndStringValueT();
    nameAndStringValueT.setName("RxMaxDelay");
    nameAndStringValueT.setValue(Long.toString(result.get(ContinuityTestResultPropertiesFSP150CM.VL.MonRxMaxDelay)));
    nvsListT.getNvs().add(nameAndStringValueT);

    return nvsListT;
  }
}
