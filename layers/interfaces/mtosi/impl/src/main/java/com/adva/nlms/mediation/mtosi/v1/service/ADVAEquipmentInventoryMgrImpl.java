/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;

import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.equipment.ProvisionEquipmentWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.equipment.ResetEquipmentWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.equipment.SetEquipmentDataWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.equipment.UnprovisionEquipmentWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import ws.v1.tmf854ext.adva.ADVAEquipmentInventoryMgr;

import jakarta.annotation.Resource;
import jakarta.xml.ws.WebServiceContext;

/**
 * This class was generated by the CXF 2.0.1-incubator
 * Wed Dec 12 11:28:33 CET 2007
 * Generated source version: 2.0.1-incubator
 * 
 */

@jakarta.jws.WebService(name = "ADVAEquipmentInventoryMgr", serviceName = "ADVAConfigurationService",
                      portName = "ADVAEquipmentInventoryMgrHttp",
                      targetNamespace = "adva.tmf854ext.v1.ws",
		      endpointInterface = "ws.v1.tmf854ext.adva.ADVAEquipmentInventoryMgr")

public class ADVAEquipmentInventoryMgrImpl implements ADVAEquipmentInventoryMgr {

  private static final Logger LOG = LogManager.getLogger(ADVAEquipmentInventoryMgrImpl.class.getName());
  @Resource
  private WebServiceContext context;

  /* (non-Javadoc)
  * @see ws.v1.tmf854ext.adva.ADVAEquipmentInventoryMgr#unprovisionEquipment(v1.tmf854.HeaderT  mtosiHeader ,)v1.tmf854ext.adva.UnprovisionEquipmentT  mtosiBody )*
  */
  @Override
  public v1.tmf854ext.adva.UnprovisionEquipmentResponseT unprovisionEquipment(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,v1.tmf854ext.adva.UnprovisionEquipmentT mtosiBody)
          throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(UnprovisionEquipmentWorker.class, mtosiBody, mtosiHeader,
            "unprovisionEquipment", context, LOG);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854ext.adva.ADVAEquipmentInventoryMgr#setEquipmentData(v1.tmf854.HeaderT  mtosiHeader ,)v1.tmf854ext.adva.SetEquipmentDataT  mtosiBody )*
  */
  @Override
  public v1.tmf854ext.adva.SetEquipmentDataResponseT setEquipmentData(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,v1.tmf854ext.adva.SetEquipmentDataT mtosiBody)
          throws ws.v1.tmf854.ProcessingFailureException    {
    return ServiceUtils.runMethod(SetEquipmentDataWorker.class, mtosiBody, mtosiHeader,
            "setEquipmentData", context, LOG);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854ext.adva.ADVAEquipmentInventoryMgr#resetEquipment(v1.tmf854.HeaderT  mtosiHeader ,)v1.tmf854ext.adva.ResetEquipmentT  mtosiBody )*
  */
  @Override
  public v1.tmf854ext.adva.ResetEquipmentResponseT resetEquipment(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,v1.tmf854ext.adva.ResetEquipmentT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException    {
    return ServiceUtils.runMethod(ResetEquipmentWorker.class, mtosiBody, mtosiHeader,
            "resetEquipment", context, LOG);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854ext.adva.ADVAEquipmentInventoryMgr#provisionEquipment(v1.tmf854.HeaderT  mtosiHeader ,)v1.tmf854ext.adva.ProvisionEquipmentT  mtosiBody )*
  */
  @Override
  public v1.tmf854ext.adva.ProvisionEquipmentResponseT provisionEquipment(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,v1.tmf854ext.adva.ProvisionEquipmentT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException    {
    return ServiceUtils.runMethod(ProvisionEquipmentWorker.class, mtosiBody, mtosiHeader,
            "provisionEquipment", context, LOG);
  }

}
