/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;
 /**
  *
  */
public enum HNDot3OamOperStatusTranslation {
    Disabled					(1),
    LinkFault					(2),
    PassiveWait					(3),
    ActiveSendLocal				(4),
    SendLocalAndRemote			(5),
    SendLocalAndRemoteOk		(6),
    OamPeeringLocallyRejected	(7),
    OamPeeringRemotelyRejected	(8),
    Operational					(9),
    NonOperHalfDuplex			(10);
    
    private int mibValue;
    
    private int getMibValue() {
		return mibValue;
	}

	private HNDot3OamOperStatusTranslation(int code) {
    	this.mibValue = code;
    }

    public static String getMtosiString(final Integer mibValue) {
    	if (mibValue == null) {
    		return "null";
    	}
    	for (HNDot3OamOperStatusTranslation value: values() ) {
    		if (value.getMibValue() == mibValue.intValue()) {
    			return value.name(); 
    		}
    	}
    	//Probably should throw something...
    	return String.valueOf(mibValue);
    }
    
    public static int getMibValue(final String name) {
    	for (HNDot3OamOperStatusTranslation value: values() ) {
    		if (value.name().equals(name)) {
    			return value.getMibValue(); 
    		}
    	}
    	//Probably should throw something...
    	return -1;
    }
}
