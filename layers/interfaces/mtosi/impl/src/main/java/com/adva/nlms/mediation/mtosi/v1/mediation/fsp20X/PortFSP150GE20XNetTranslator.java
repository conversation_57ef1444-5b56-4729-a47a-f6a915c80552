/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp20X;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.mtosi.NetworkElementMTOSIWorkerF3Impl;
import v1.tmf854.ConnectionTerminationPointT;
import v1.tmf854.DirectionalityT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ResourceStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.common.serviceProvisioning.ProtectionGroupPortF3SPProperties;
import com.adva.nlms.mediation.config.f3.entity.port.net.MTOSIPortF3Net;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMLAGPortStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMLAGPortTypeTranslation;

/**
 * This class is an FSP 150 CM network port MTOSI Translator.
 */
public class PortFSP150GE20XNetTranslator extends PortFSP150GE20XTranslator {
  MTOSIPortF3Net port;
  NetworkElementMTOSIWorkerF3Impl mtosiWorker;

  public PortFSP150GE20XNetTranslator (MTOSIPortF3Net port, NetworkElementMTOSIWorkerF3Impl mtosiWorker) {
    super(port);
    this.port = port;
    this.mtosiWorker = mtosiWorker;
  }

  @Override
  public PhysicalTerminationPointT toMtosiPTP() throws ProcessingFailureException {
    return toMtosiPTP(false);
  }

	@Override
	protected String getInterfaceType() {
		return "NNI";
	}

	@Override
	protected String getTpRoleState() {
		return "internal";
	}

	/**
	 * This parameter describes the static maximum number of virtual connections (FDFrs/EVCs) that may be supported on this port.
	 * @return
	 */
	@Override
	protected String getMaxNumFDFrs() {
		return "1";//String.valueOf(port.getPortSPProperties().get(NETPortSPPropertiesFSP150CM.VI.MaxNumFDFrs));
		
	}

  @Override
  public ConnectionTerminationPointT toMtosiCTP() throws ProcessingFailureException {
    final ObjectFactory objFactory = new ObjectFactory();
    final ConnectionTerminationPointT connectionTerminationPoint = objFactory.createConnectionTerminationPointT();

    // CTP Name
    final NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributesCTPonFTP(port);
    connectionTerminationPoint.setName(objFactory.createConnectionTerminationPointTName(namingAttributes));

    // discoveredName
    final String ctpNm = namingAttributes.getCtpNm();
    if (ctpNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
    }
    connectionTerminationPoint.setDiscoveredName(objFactory.createConnectionTerminationPointTDiscoveredName(ctpNm));

    // namingOS
    connectionTerminationPoint.setNamingOS(objFactory.createConnectionTerminationPointTNamingOS(OSFactory.getNmsName()));

    // source
    final SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    connectionTerminationPoint.setSource(objFactory.createConnectionTerminationPointTSource(source));

    // resource state
    final ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ResourceStateEnumT.INSTALLED);
    connectionTerminationPoint.setResourceState(objFactory.createConnectionTerminationPointTResourceState(resourceState));

    // direction
    connectionTerminationPoint.setDirection(objFactory.createConnectionTerminationPointTDirection(DirectionalityT.D_BIDIRECTIONAL));

    // layers
    final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_LAG_FRAGMENT);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_LAG_FRAGMENT,
            LayeredParams.LrLagFragment.LAG_MEMBER_PARAM, port.getMtosiName());
    // -------end of Layer-------

    // -------start Layer--------
    ProtectionGroupPortF3SPProperties pgPortF3Properties = mtosiWorker.getProtectionGroupPortSPProperties(port.getID());

    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_PROTECTION_FSP150_1PLUS1);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_PROTECTION_FSP150_1PLUS1,
            LayeredParams.LrPropAdvaProtectionFSP1501PLUS1.MEMBER_TYPE_PARAM,
            MtosiUtils.getMtosiString(CMLAGPortTypeTranslation.NOT_APPLICABLE, pgPortF3Properties
                    .get(ProtectionGroupPortF3SPProperties.VI.UnitType)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_PROTECTION_FSP150_1PLUS1,
            LayeredParams.LrPropAdvaProtectionFSP1501PLUS1.MEMBER_STATE_PARAM,
            MtosiUtils.getMtosiString(CMLAGPortStateTranslation.NOT_APPLICABLE, pgPortF3Properties
                    .get(ProtectionGroupPortF3SPProperties.VI.UnitState)));
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
    // Empty layer indicating support for Ethernet.
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET);
    // Empty layer indicating support for ADVA Ethernet.
    // -------end of Layer-------

      NetworkElement ne = port.getNE();
    if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE) {
        LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet);
   	
    }
    connectionTerminationPoint.setTransmissionParams(objFactory.createConnectionTerminationPointTTransmissionParams(layeredParametersListT));
    return connectionTerminationPoint;
  }
}
