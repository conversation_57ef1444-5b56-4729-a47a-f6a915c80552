/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;
 /**
  *
  */
public enum HNAutoNegotiationTranslation {
	Enabled   	    (1),
	Disabled 		(2);
    
    private int mibValue;
    
    private int getMibValue() {
		return mibValue;
	}

	private HNAutoNegotiationTranslation(int code) {
    	this.mibValue = code;
    }

    public static String getMtosiString(final Integer mibValue) {
    	if (mibValue == null) {
    		return "null";
    	}
    	for (HNAutoNegotiationTranslation value: values() ) {
    		if (value.getMibValue() == mibValue.intValue()) {
    			return value.name(); 
    		}
    	}
    	//Probably should throw something...
    	return String.valueOf(mibValue);
    }
    
    public static int getMibValue(final String name) {
    	for (HNAutoNegotiationTranslation value: values() ) {
    		if (value.name().equals(name)) {
    			return value.getMibValue(); 
    		}
    	}
    	//Probably should throw something...
    	return -1;
    }
}
