/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.hn4000;

import jakarta.xml.bind.JAXBElement;
import javax.xml.namespace.QName;

import com.adva.nlms.mediation.config.NetworkElementDAO;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import v1.tmf854.EqVendorExtensionsT;
import v1.tmf854.MEVendorExtensionsT;
import v1.tmf854.ObjectFactory;

import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.SFPSPProperties;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.hn4000.NetworkElementHN4000;
import com.adva.nlms.mediation.config.hn4000.ObjectStateFieldHN4000;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrationControlTranslation;

/**
 * This class is a Network Element Mediator for the HN 400 boxes (HN Remote
 * boxes).
 */
public class Hn4000EfmMediator extends Hn4000Mediator {
	public Hn4000EfmMediator(NetworkElementHN4000 ne, NetworkElementDAO networkElementDAO) {
		super(ne, networkElementDAO);
	}

	@Override
  protected void populateIpAddress(MEVendorExtensionsT extensions) {
		NetworkElement peerNe = ne.getPeerNetworkElement();
		JAXBElement jeProductName = new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE, MtosiConstants.VENDOR_EFM_MGR_PRODUCT_NAME),
				String.class, peerNe.getMTOSIWorker().getProductName());
		extensions.getAny().add(jeProductName);
		JAXBElement jeMeName = new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE, MtosiConstants.VENDOR_EFM_MGR_ME_NAME), String.class, peerNe
				.getName());
		extensions.getAny().add(jeMeName);
	}

	@Override
  protected String getShelfEquipment(EquipmentSPProperties properties) {
		return ne.getMTOSIWorker().getProductName();
	}

	/**
	 * Over ride HN4000 Vendor extensions to skip the "isMaster" Parameter.
	 */
	@Override
  protected JAXBElement<EqVendorExtensionsT> getVendorExtensions(EquipmentSPProperties props) {
		ObjectFactory objFactory = new ObjectFactory();
		EqVendorExtensionsT extensions = new EqVendorExtensionsT();
		String adminControl = "";
		if (props.get(EquipmentSPProperties.VI.AdminState) < 4) {
			adminControl = MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, props.get(EquipmentSPProperties.VI.AdminState));
		}
		extensions.getAny().add(
				new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE, MtosiConstants.VENDOR_ADMINISTRATION_CONTROL), String.class, adminControl));

		Integer objectState = props.get(EquipmentSPProperties.VI.OperState);
		/*
		 * The Object State is stored in the operState field .
		 */
		if (objectState != null) {
			String secondaryState = ObjectStateFieldHN4000.getSecondaryStateString(objectState);
			extensions.getAny().add(
					new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE, MtosiConstants.VENDOR_SECONDARY_STATE), String.class, secondaryState));

		}
		return objFactory.createEquipmentTVendorExtensions(extensions);
	}
	@Override
	  protected String getRelativeNameModule(EquipmentSPProperties properties) {
		    if (!(properties instanceof SFPSPProperties)) {
		      throw new IllegalArgumentException("getRelativeNameModule is accepting only SFPSPProperties!");
		    }
		    SFPSPProperties propertiesSFP = (SFPSPProperties) properties;

		    return MtosiConstants.SHELF_TEXT + propertiesSFP.get(EquipmentSPProperties.VI.ChassisId) +
		            MtosiConstants.SUBSLOT_HN400_SFP + propertiesSFP.get(SFPSPProperties.VI.SFPNumber);
		  }

}
