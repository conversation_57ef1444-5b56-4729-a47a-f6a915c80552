/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;

public enum GE20XRemoteLinkIdsTranslation implements TranslatableEnum {
    none			(0,  "0"),
    set 			(1,  "1"),
    NOT_APPLICABLE	(-1, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private GE20XRemoteLinkIdsTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  public static String getMtosiString(final int size, final byte[] mibValue) {
	    StringBuilder toReturn = new StringBuilder();
//	    toReturn.append(mibValue[0]).append(" = ");
	    int x = 64;
	    int y;
	    for (int i = 0; i < size; i++) {
	    	y = x >> i;
	    	if ((mibValue[0] & y) == y) {
		    	if (toReturn.length() > 0)
		    		toReturn.append(',');
	    		toReturn.append(i+1);
//	    	} else {
//	    		toReturn.append('0');
//
	    	}
	    }
	    return toReturn.toString();
	  }

	  /**
	   * Returns the byte array representing given string value.
	   * @param mtosiString String value to parse.
	   * @return byte array
	   * @throws ProcessingFailureException when string value is invalid
	   */
	  public static byte[] getMIBValue(final int size,final String mtosiString) throws ProcessingFailureException {
	    byte[] toReturn = {0};
	    if (mtosiString != null && mtosiString.length() > 0) {
		    String[] remoteIds = mtosiString.split(",");
		    if (remoteIds.length > size) {
		        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
		                ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageIllegal(
		                LayeredParams.LrPropAdvaEthernet.REMOTE_LINK_IDS_PARAM));
		        throw new ProcessingFailureException(pfet.getReason(), pfet);
		    }
		    try {
		    for (String remoteId : remoteIds) {
		    	Integer x = Integer.valueOf(remoteId);
		      if (x > size) {
			        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
			                ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageIllegal(
			                LayeredParams.LrPropAdvaEthernet.REMOTE_LINK_IDS_PARAM));
			        throw new ProcessingFailureException(pfet.getReason(), pfet);
		      }
		        toReturn[0] |= (int)Math.pow(2,7 - x);  // For some reason it is the middle 6 bits... from Left to right. ie RemoteID 1 is [0100 0000]
		    }
		    } catch (Exception e) {
		        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
		                ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageIllegal(
		                LayeredParams.LrPropAdvaEthernet.REMOTE_LINK_IDS_PARAM));
		        throw new ProcessingFailureException(pfet.getReason(), pfet);
		    }
	    }
	    return toReturn;
	  }

	@Override
	public int getMIBValue() {
		return mibValue;
	}

	@Override
	public String getMtosiString() {
		return mtosiString;
	}
}