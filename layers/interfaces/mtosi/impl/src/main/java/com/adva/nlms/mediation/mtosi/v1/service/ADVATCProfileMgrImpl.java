/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;

import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.tcProfile.GetTCProfileWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.tcProfile.GetTCProfilesWithTPWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.tcProfile.ModifyTCProfileWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import ws.v1.tmf854ext.adva.ADVATCProfileMgr;

import jakarta.annotation.Resource;
import jakarta.xml.ws.WebServiceContext;

/**
 * This class was generated by the CXF 2.0-incubator-RC
 * Thu Jun 28 08:59:00 CEST 2007
 * Generated source version: 2.0-incubator-RC
 *
 */

@jakarta.jws.WebService(name = "ADVATCProfileMgr", serviceName = "ADVAConfigurationService",
        portName = "ADVATCProfileMgrHttp",
        targetNamespace = "adva.tmf854ext.v1.ws",
        endpointInterface = "ws.v1.tmf854ext.adva.ADVATCProfileMgr")

public class ADVATCProfileMgrImpl implements ADVATCProfileMgr {

  Logger LOG  = LogManager.getLogger(this.getClass().getName());
  @Resource
  private WebServiceContext context;

  @Override
  public v1.tmf854ext.adva.GetTCProfileResponseT getTCProfile(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854ext.adva.GetTCProfileT mtosiBody
  ) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(GetTCProfileWorker.class, mtosiBody, mtosiHeader,
            "getTCProfile", context, LOG);
  }


  @Override
  public v1.tmf854ext.adva.GetTCProfilesWithTPResponseT getTCProfilesWithTP(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854ext.adva.GetTCProfilesWithTPT mtosiBody
  ) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(GetTCProfilesWithTPWorker.class, mtosiBody, mtosiHeader,
            "getTCProfilesWithTP", context, LOG);
  }


  @Override
  public v1.tmf854ext.adva.ModifyTCProfileResponseT modifyTCProfile(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854ext.adva.ModifyTCProfileT mtosiBody
  )	throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(ModifyTCProfileWorker.class, mtosiBody, mtosiHeader,
            "modifyTCProfile", context, LOG);
  }
}
