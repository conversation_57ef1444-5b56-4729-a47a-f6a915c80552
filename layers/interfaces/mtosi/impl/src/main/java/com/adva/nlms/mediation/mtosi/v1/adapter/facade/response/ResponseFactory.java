/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.adapter.facade.response;

import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.LayeredParametersT;
import v1.tmf854.NVSListT;
import v1.tmf854.NameAndStringValueT;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ResponseFactory {
  Map<Integer, Map<String, LayeredParametersT>> cache = new HashMap<>();
  private static Logger logger = LogManager.getLogger(ResponseFactory.class);

  public ResponseFactory() {
  }

  public LayeredParametersListT createParameterListType(int multipartId, String... layerRate) {
    LayeredParametersListT paramList = new LayeredParametersListT();
    printLayers(layerRate);
    for (String rate : layerRate) {
      paramList.getLayeredParameters().add(createLayeredParametersType(multipartId, rate));
    }
    return paramList;
  }

  private void printLayers(String[] layerRate) {
    if (logger.isDebugEnabled()) {
      if (layerRate == null) return;
      String layers = "";
      for (String rate : layerRate) {
        layers += rate + " ";
      }
      logger.debug("Adding layers: " + layers);
    }
  }

  public LayeredParametersT createLayeredParametersType(Integer multipartDomainId, String rate) {
    LayeredParametersT layeredParametersT = new LayeredParametersT();
    layeredParametersT.setLayer(rate);
    getDomain(multipartDomainId).put(rate, layeredParametersT);
    return layeredParametersT;
  }

  public void addParams(Integer multipartDomainId, String rate, List<Pair<String, Object>> pairs) {
    printPair(rate, pairs);
    if (getDomain(multipartDomainId).containsKey(rate)) {
      LayeredParametersT layeredParametersType = getDomain(multipartDomainId).get(rate);
      if (layeredParametersType.getTransmissionParams() == null || layeredParametersType.getTransmissionParams().getNvs() == null) {
        layeredParametersType.setTransmissionParams((new NVSListT()));
      }
      createNameAndValueStringListType(layeredParametersType.getTransmissionParams().getNvs(), pairs);
    }
  }

  public void createNameAndValueStringListType(List<NameAndStringValueT> nvs, List<Pair<String, Object>> pairs) {
    for (Pair<String, Object> pair : pairs) {
      if (pair != null && pair.getLeft() != null && pair.getRight() != null) {
        nvs.add(createNameAndStringValueT(pair.getLeft(), pair.getRight()));
      } else {
        if (pair == null || (pair.getRight() == null && pair.getLeft() == null)) {
          logger.error("Null nvs pair found");
        } else if (pair.getRight() == null) {
          logger.warn("Null nvs pair value found with key:" + pair.getLeft() + " ignoring..");
        } else if (pair.getLeft() == null) {
          logger.warn("Null nvs pair key found with value:" + pair.getRight() + " ignoring..");
        }
      }
    }
  }

  public NameAndStringValueT createNameAndStringValueT(String name, Object value) {
    NameAndStringValueT nsvt = new NameAndStringValueT();
    nsvt.setName(name);
    nsvt.setValue(value.toString());
    return nsvt;
  }

  private void printPair(String rate, List<Pair<String, Object>> pairs) {
    if (logger.isDebugEnabled()) {
      logger.debug("Adding layer " + rate + " parameters:");
      for (Pair<String, Object> pair : pairs) {
        logger.debug("param: " + (pair.getLeft() == null ? " - ," : pair.getLeft()) + " value: " + (pair.getRight() == null ? " - " : pair.getRight()));
      }
    }
  }

  private Map<String, LayeredParametersT> getDomain(Integer multipartDomainId) {
    if (cache.containsKey(multipartDomainId)) {
      return cache.get(multipartDomainId);
    } else {
      cache.put(multipartDomainId, new HashMap<String, LayeredParametersT>());
      return cache.get(multipartDomainId);
    }
  }

}
