/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */
package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.dto.attr.ProtectionGroupF3Attr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.adapter.facade.MtosiProtectionGroupEFMDTO;
import com.adva.nlms.mediation.mtosi.v1.adapter.facade.MtosiProtectionGroupEFMDTOImpl;
import com.adva.nlms.mediation.mtosi.v1.adapter.facade.MtosiTerminationPointEFMDTOImpl;
import com.adva.nlms.mediation.mtosi.v1.factory.CreateFloatingTerminationPointFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.CreateFTPResponseT;
import v1.tmf854ext.adva.FTPCreateDataT;

import jakarta.xml.ws.Holder;
import java.util.List;

public class CreateFTPWorkerEFM<T extends ManagedObjectAttr> extends CreateFTPWorkerCM{

  private static Logger logger = LogManager.getLogger(CreateFTPWorkerEFM.class);

  private CreateFloatingTerminationPointFactory createFloatingTerminationPointFactory = new CreateFloatingTerminationPointFactory();
  private MtosiAddress mtosiAddress;
  private MtosiProtectionGroupEFMDTO floatingTerminationPoint;
  private DTO<ProtectionGroupF3Attr> ftpMO;
  private MtosiTerminationPointEFMDTOImpl mtosiTerminationPointEFMDTO;

  public CreateFTPWorkerEFM(Holder<HeaderT> mtosiHeader, FTPCreateDataT ftpCreateData, NamingAttributesT namingAttributes, NetworkElement ne) {
    super(mtosiHeader, ftpCreateData, namingAttributes, ne);
    mtosiTerminationPointEFMDTO = new MtosiTerminationPointEFMDTOImpl();
  }

  @Override
  protected void parse() throws Exception {
    mtosiAddress = new MtosiAddress(ftpCreateData.getName());

//    int shelf = NamingTranslationFactory.shelfNumberFromShelfCombo(mtosiAddress.getNaming().getFtpNm());
//    int slot = NamingTranslationFactory.slotNumberFromShelfCombo(mtosiAddress.getNaming().getFtpNm());
//    if (ManagedElementFactory.needsSlotIncrement(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
//      slot = slot + 1;
//    }

//    String[] parts =  mtosiAddress.getNaming().getFtpNm().split("/");
//    String newFtpNm = "/"+parts[1]+"/slot="+slot+"/"+parts[3];
//    mtosiAddress.getNaming().setFtpNm(newFtpNm);

    if (mtosiAddress.getNaming().getFtpNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "The ftpNm has not been specified.");
    }

    if (mtosiAddress.getNaming().getMdNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "The Management Domain name must be specified in the request.");
    }

    floatingTerminationPoint = createFloatingTerminationPointFactory.validateAndParse(mtosiAddress, ftpCreateData, getMtosiCtrl().getMoFacadeManager());
    floatingTerminationPoint.validate();
    List<DTO<ProtectionGroupF3Attr>> pgs = floatingTerminationPoint.getMtosiFacade().queryDTO(floatingTerminationPoint.getNetworkElement().getID(),ProtectionGroupF3Attr.class);
    if(pgs != null && pgs.size() > 0){
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_CAPACITY_EXCEEDED,
          "An FTP already exists on this slot.");
    }

//    mtosiAddress.setShelfIndex(shelf);
//    mtosiAddress.setSlotIndex(slot);
  }

  @Override
  protected void mediate() throws Exception {
    DTO<ProtectionGroupF3Attr> moDTO = ((MtosiProtectionGroupEFMDTOImpl)floatingTerminationPoint).getMODTOForCM(mtosiAddress);
    NetworkElement networkElement = floatingTerminationPoint.getNetworkElement();
    MtosiMOFacade facade = floatingTerminationPoint.getMtosiFacade();
    transact(networkElement, moDTO, facade);
//    TimeUnit.SECONDS.sleep(2);
    ftpMO = facade.findDTOViaMtosiName(floatingTerminationPoint.getNetworkElement().getID(), mtosiAddress.getNaming().getFtpNm(), ProtectionGroupF3Attr.class);
    if (ftpMO == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(),ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.FLOATING_TERMINATION_POINT_DISCOVERY_FAILURE);
    }
  }

  private void transact(NetworkElement networkElement, DTO<ProtectionGroupF3Attr> moDTO,MtosiMOFacade facade)
      throws SNMPCommFailure, ObjectInUseException {
    String mtosiname = moDTO.getValue(ManagedObjectAttr.MTOSI_NAME);
    try {
      if (logger.isDebugEnabled()) {
        logger.debug("CreateFloatingTerminationPointWorker transact started");
      }
      int neId = networkElement.getID();
      try {
        facade.openNetTransaction(neId);
        logSecurity(networkElement, SystemAction.AddNetwork, mtosiname);
        facade.createObjectOnDevice(neId, moDTO);

      } catch (ObjectInUseException ex) {
        logger.error("Exception during transaction: " + ex);
        networkElement.logSROperation(SROperationState.FTP_CREATION_FAILURE, mtosiname);
        throw ex;
      } catch (SNMPCommFailure ex) {
        networkElement.logSROperation(SROperationState.FTP_CREATION_FAILURE, mtosiname);
        throw ex;
      } finally {
        facade.closeNetTransaction(neId);
      }
    } finally {
      if (logger.isDebugEnabled()) {
        logger.debug("CreateFloatingTerminationPointWorker transact completed");
      }
    }
  }


  @Override
  protected void response() throws Exception {
    try{
      if (logger.isDebugEnabled()) {
        logger.debug("CreateFloatingTerminationPointWorker build response started");
      }
      createFloatingTerminationPointFactory.buildResponse(response, ftpMO, floatingTerminationPoint);
    }finally {
      if (logger.isDebugEnabled()) {
        logger.debug("CreateFloatingTerminationPointWorker build response completed");
      }
    }
  }

  @Override
  public CreateFTPResponseT getSuccessResponse()
  {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }


}
