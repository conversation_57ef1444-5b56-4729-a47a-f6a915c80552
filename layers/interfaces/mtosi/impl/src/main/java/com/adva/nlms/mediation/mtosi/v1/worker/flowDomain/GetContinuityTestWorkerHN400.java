/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.common.snmp.TestResultTranslation;
import com.adva.nlms.mediation.common.serviceProvisioning.HnTrafficGenProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.HnTrafficMonProperties;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HnTestStatusTranslation;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import v1.tmf854.HeaderT;
import v1.tmf854.NVSListT;
import v1.tmf854.NameAndStringValueT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public class GetContinuityTestWorkerHN400 extends GetContinuityTestWorkerHN {
	HnTrafficMonProperties results;

	public GetContinuityTestWorkerHN400(Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne) {
		super(mtosiHeader, namingAttributes, ne);
	}

	@Override
  protected void mediate() throws Exception {
		super.mediate();
//		if (!(port instanceof PortHN4000Ethernet2BASE_TL)) {
//			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, "The port must be an FTP.");
//		}
		transact();
	}

	@Override
  protected void response() throws Exception {
		response.setTestResults(getTestResults());
		// this value will always be stopped
		response.setTestStatus(HnTestStatusTranslation.getMtosiString(results.get(HnTrafficGenProperties.VI.Status)));
	}

	private void transact() throws SNMPCommFailure {
		results = flow.getTrafficGenTest();
	}

	private NVSListT getTestResults() throws ProcessingFailureException {
		if (!results.contains(HnTrafficMonProperties.VL.Stream1RxFrames)) {
		throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, "Test Results are not available.");
	}

		
		final ObjectFactory objFactory = new ObjectFactory();
		final NVSListT nvsListT = objFactory.createNVSListT();
		NameAndStringValueT nameAndStringValueT = objFactory.createNameAndStringValueT();
		nameAndStringValueT.setName("TestResult");

		
		final long rxFrames = results.get(HnTrafficMonProperties.VL.Stream1RxFrames);
		final long txFrames = results.get(HnTrafficGenProperties.VL.Stream1TxFrames);
		if (rxFrames == 0) {
			nameAndStringValueT.setValue(TestResultTranslation.FAIL.getMtosiString());
		} else {
			if (rxFrames == txFrames) {
				nameAndStringValueT.setValue(TestResultTranslation.PASS.getMtosiString());
			} else {
				nameAndStringValueT.setValue(TestResultTranslation.FAIL.getMtosiString());
			}
		}
		nvsListT.getNvs().add(nameAndStringValueT);

		setParameter(objFactory, nvsListT, "TxFrames", String.valueOf(txFrames));

		setParameter(objFactory, nvsListT, "RxFrames", String.valueOf(rxFrames));

		setParameter(objFactory, nvsListT, "RxMinDelay", 
				String.valueOf(results.get(HnTrafficMonProperties.VL.Stream1LatencyMin)));

		setParameter(objFactory, nvsListT, "RxAvgDelay", 
				String.valueOf(results.get(HnTrafficMonProperties.VL.Stream1LatencyAvg)));

		setParameter(objFactory, nvsListT, "RxMaxDelay", 
				String.valueOf(results.get(HnTrafficMonProperties.VL.Stream1LatencyMax)));

		setParameter(objFactory, nvsListT, "RxMinJitter", 
				String.valueOf(results.get(HnTrafficMonProperties.VI.Stream1JitterMin)));

		setParameter(objFactory, nvsListT, "RxAvgJitter", 
				String.valueOf(results.get(HnTrafficMonProperties.VI.Stream1JitterAvg)));

		setParameter(objFactory, nvsListT, "RxMaxJitter", 
				String.valueOf(results.get(HnTrafficMonProperties.VI.Stream1JitterMax)));

		return nvsListT;
	}

	private void setParameter(final ObjectFactory objFactory, final NVSListT nvsListT, String name, final String value) {
		NameAndStringValueT nameAndStringValueT;
		nameAndStringValueT = objFactory.createNameAndStringValueT();
		nameAndStringValueT.setName(name);
		nameAndStringValueT.setValue(value);
		nvsListT.getNvs().add(nameAndStringValueT);
	}
}
