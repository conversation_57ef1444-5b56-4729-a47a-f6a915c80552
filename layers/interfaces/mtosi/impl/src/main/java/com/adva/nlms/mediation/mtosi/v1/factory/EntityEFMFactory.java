/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.factory;

import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.DTOBuilder;
import com.adva.nlms.mediation.config.dto.attr.FlowF3Attr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3AccAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3NetAttr;
import com.adva.nlms.mediation.config.dto.attr.ProtectionGroupF3Attr;
import com.adva.nlms.mediation.config.mofacade.MOFacadeManager;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.MtosiCtrl;
import com.adva.nlms.mediation.mtosi.v1.adapter.facade.MtosiTerminationPointEFMDTOImpl;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiCTPMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.TPDataListT;
import v1.tmf854.TPDataT;
import ws.v1.tmf854.ProcessingFailureException;

import java.util.List;

public class EntityEFMFactory {
  private final MtosiMOFacade facade;
  private final TPDataListT tpDataList;
  private final SetTerminationPointDataFactory factory;
  private final MOFacadeManager facadeManager;
  private final int neId;
  private MtosiTerminationPointEFMDTOImpl mtosiTerminationPointEFMDTO;

  public EntityEFMFactory(MtosiMOFacade facade, TPDataListT tpDataList, MtosiCtrl mtosiCtrl, int neId)
      throws ProcessingFailureException {
    if (tpDataList == null || tpDataList.getTpData() == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          "Mandatory tpDataListToModify has not been specified.");
    }
    this.facade=facade;
    this.tpDataList=tpDataList;
    this.factory=new SetTerminationPointDataFactory();
    this.facadeManager = mtosiCtrl.getMoFacadeManager();
    this.neId = neId;
    this.mtosiTerminationPointEFMDTO = new MtosiTerminationPointEFMDTOImpl(facade);
  }

  public EntityEFMFactory(MtosiMOFacade facade, MtosiCtrl mtosiCtrl, int neId)
      throws ProcessingFailureException {

    this.facade=facade;
    this.tpDataList=null;
    this.factory=new SetTerminationPointDataFactory();
    this.facadeManager = mtosiCtrl.getMoFacadeManager();
    this.neId = neId;
    this.mtosiTerminationPointEFMDTO = new MtosiTerminationPointEFMDTOImpl(facade);
  }

  public DTO<PortF3AccAttr> getPortAccF3DTO(TPDataT tpDataT) throws ProcessingFailureException{
    TPDataT tpData = getLayeredParameterList(MtosiAddress.RDNType.ACC);
    if(tpData!=null){
      MtosiAddress mtosiAddress = new MtosiAddress(tpData.getTpName());
      DTO<PortF3AccAttr> dbDTO = facade.findDTOViaMtosiName(neId, mtosiAddress.getNaming().getPtpNm(), PortF3AccAttr.class);

      DTO<PortF3AccAttr> dto = MtosiTPMediator.getPortAccForUpdateDTO(tpDataT, dbDTO);

      dto.putOrReplace(ManagedObjectAttr.ENTITY_INDEX, dbDTO.getValue(ManagedObjectAttr.ENTITY_INDEX));
      dto.putOrReplace(ManagedObjectAttr.MTOSI_NAME, dbDTO.getValue(ManagedObjectAttr.MTOSI_NAME));
      return dto;
    }
    return null;
  }

  public DTO<PortF3NetAttr> getPortNetF3DTO(TPDataT tpDataT) throws ProcessingFailureException{
    TPDataT tpData = getLayeredParameterList(MtosiAddress.RDNType.NET1);
    if(tpData!=null){
      MtosiAddress mtosiAddress = new MtosiAddress(tpData.getTpName());
      DTO<PortF3NetAttr> dbDTO = facade.findDTOViaMtosiName(neId, mtosiAddress.getNaming().getPtpNm(), PortF3NetAttr.class);

      DTO<PortF3NetAttr> dto = MtosiTPMediator.getPortNetForUpdateDTO(tpDataT, dbDTO);

      dto.putOrReplace(ManagedObjectAttr.ENTITY_INDEX, dbDTO.getValue(ManagedObjectAttr.ENTITY_INDEX));
      dto.putOrReplace(ManagedObjectAttr.MTOSI_NAME, dbDTO.getValue(ManagedObjectAttr.MTOSI_NAME));
      return dbDTO;
    }
    return null;
  }

  public static DTO<ProtectionGroupF3Attr> getFloatingTerminationPointDTO(TPDataT tpData, DTO<ProtectionGroupF3Attr> ftp)
      throws ProcessingFailureException
  {
    DTO<ProtectionGroupF3Attr> ftpAttrForUpdate = DTOBuilder.getInstance().newDTO(ProtectionGroupF3Attr.class);
    LayeredParametersListT transmissionParams = tpData.getTransmissionParams().getValue();
    if (transmissionParams == null) {
      return ftpAttrForUpdate;
    }
    String allocatedNumber = LayeredParameterUtils.getLayeredParameter(transmissionParams, LayeredParams.LR_LAG,
        LayeredParams.LrLag.ALLOCATED_NUMBER_PARAM);
    if (allocatedNumber != null) {
      if (!MtosiUtils.isInteger(allocatedNumber)) {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
            ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.ALLOCATED_NUMBER_ILLEGAL_VALUE);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      Integer allocatedNumberValue = Integer.valueOf(allocatedNumber);
      if (allocatedNumberValue != 2) {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
            ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.ALLOCATED_NUMBER_ILLEGAL_VALUE);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
    }
    String fragmentServerLayer = LayeredParameterUtils.getLayeredParameter(transmissionParams,
        LayeredParams.LR_LAG, LayeredParams.LrLag.FRAGMENT_SERVER_LAYER_PARAM);
    if (fragmentServerLayer != null) {
      if (!fragmentServerLayer.equals(LayeredParams.LR_ETHERNET)) {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
            ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.FRAGMENT_SERVER_LAYER_ILLEGAL_VALUE);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
    }
    String protectionSwitchMode = LayeredParameterUtils.getLayeredParameter(transmissionParams,
        LayeredParams.PROP_ADVA_PROTECTION_FSP150_CMNTU, LayeredParams.LrPropAdvaProtectionFSP150CMNTU.PROTECTION_SWITCH_MODE);
    if (protectionSwitchMode != null) {
      if (!protectionSwitchMode.equals("OnePlusOne")) {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
            ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaProtectionFSP150CMNTU.PROTECTION_SWITCH_MODE));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      ftpAttrForUpdate.putOrReplace(ProtectionGroupF3Attr.SWITCH_MODE, 1); // todo: use constants
    }
    String revertive = LayeredParameterUtils.getLayeredParameter(transmissionParams,
        LayeredParams.PROP_ADVA_PROTECTION_FSP150_CMNTU, LayeredParams.LrPropAdvaProtectionFSP150CMNTU.REVERTIVE);
    if (revertive != null) {
      Integer revertiveValue = MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE, revertive);
      if (revertiveValue != MIB.RFC1253.TRUTH_VALUE_FALSE)
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
            ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaProtectionFSP150CMNTU.REVERTIVE));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      ftpAttrForUpdate.putOrReplace(ProtectionGroupF3Attr.REVERTIVE, Boolean.FALSE); // todo: use constants
    }
    String switchDirection = LayeredParameterUtils.getLayeredParameter(transmissionParams,
        LayeredParams.PROP_ADVA_PROTECTION_FSP150_CMNTU, LayeredParams.LrPropAdvaProtectionFSP150CMNTU.SWITCH_DIRECTION);
    if (switchDirection != null) {
      if (!switchDirection.equals("Unidirectional")) {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
            ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaProtectionFSP150CMNTU.SWITCH_DIRECTION));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      ftpAttrForUpdate.putOrReplace(ProtectionGroupF3Attr.DIRECTION, 1); // todo: use constants
    }

    ftpAttrForUpdate.putOrReplace(ProtectionGroupF3Attr.MTOSI_NAME, ftp.getValue(ProtectionGroupF3Attr.MTOSI_NAME));
    return ftpAttrForUpdate;
  }

  public DTO<ProtectionGroupF3Attr> getFtpF3DTO(TPDataT tpDataT) throws ProcessingFailureException{
    TPDataT tpData = getLayeredParameterList(MtosiAddress.RDNType.FTP);
    if(tpData!=null){
      MtosiAddress mtosiAddress = new MtosiAddress(tpData.getTpName());
      DTO<ProtectionGroupF3Attr> dbDTO = facade.findDTOViaMtosiName(neId, mtosiAddress.getNaming().getFtpNm(), ProtectionGroupF3Attr.class);

      DTO<ProtectionGroupF3Attr> dto = MtosiTPMediator.getFtpForUpdateDTO(tpDataT);

      dto.putOrReplace(ManagedObjectAttr.ENTITY_INDEX, dbDTO.getValue(ManagedObjectAttr.ENTITY_INDEX));
      dto.putOrReplace(ManagedObjectAttr.MTOSI_NAME, dbDTO.getValue(ManagedObjectAttr.MTOSI_NAME));
      return dbDTO;
    }
    return null;
  }


  private TPDataT getLayeredParameterList(MtosiAddress.RDNType rdnType){
    for (TPDataT tpData : tpDataList.getTpData()) {
      MtosiAddress mtosiAddress = new MtosiAddress(tpData.getTpName());
      if(mtosiAddress.getTypeOf().equals(rdnType)){
        return tpData;
      }
    }
    return null;
  }

  public DTO<FlowF3Attr> getFlowF3DTO(String fdfrName, String ctpName) throws ProcessingFailureException{
    TPDataT tpData = getLayeredParameterList(MtosiAddress.RDNType.FLOW);
    List<DTO<FlowF3Attr>> dbDTOs = facade.queryDTO(neId,FlowF3Attr.class);
    if(dbDTOs.isEmpty()){
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INTERNAL_ERROR, "EFM flow has not been discovered.");
    }
    DTO<FlowF3Attr> dbDTO = dbDTOs.get(0);

    DTO<FlowF3Attr> dto = MtosiCTPMediator.getFlowEFMForUpdateDTO(tpData.getTransmissionParams().getValue());

    dto.putOrReplace(FlowF3Attr.ENTITY_INDEX,dbDTO.getValue(FlowF3Attr.ENTITY_INDEX));
    dto.putOrReplace(FlowF3Attr.CIRCUIT_NAME, NamingTranslationFactory.getFlowNameWithFDFr(fdfrName, ctpName));

    return dto;
  }

}
