/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v2.utils.feature.interceptor;

import org.apache.cxf.binding.soap.SoapHeader;
import org.apache.cxf.binding.soap.SoapMessage;
import org.apache.cxf.binding.soap.interceptor.AbstractSoapInterceptor;
import org.apache.cxf.binding.soap.interceptor.MustUnderstandInterceptor;
import org.apache.cxf.headers.Header;
import org.apache.cxf.interceptor.Fault;
import org.apache.cxf.phase.Phase;

import javax.xml.namespace.QName;

public class HeaderUpdateInterceptor extends AbstractSoapInterceptor {


  public HeaderUpdateInterceptor() {
    super(Phase.PRE_PROTOCOL);
    addBefore(MustUnderstandInterceptor.class.getName());
  }

  @Override
  public void handleMessage(SoapMessage message) throws Fault {
    boolean replaced = false;
    //for(Object m : message.)
    for (Header header : message.getHeaders()) {
      if (((SoapHeader) header).getName().getLocalPart().equals("mtopHeader")) {
        QName name = new QName("http://www.tmforum.org/mtop/fmw/xsd/hdr/v1", "header", "m1");
        ((SoapHeader) header).setName(name);
        header.getName();
      }
    }
    message.getInterceptorChain().doIntercept(message);
  }

}