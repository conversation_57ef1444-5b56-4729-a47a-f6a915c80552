/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum CMServiceStateTranslation {
  IN_SERVICE        (1, 1, "IN_SERVICE"),  //ok
  MANAGEMENT        (2, 1, "IN_SERVICE"),  //ok
  MAINTENANCE       (3, 1, "IN_SERVICE"),  //ok
  DISABLED          (4, 1, "OUT_OF_SERVICE_BY_MAINTENANCE"),  //ok
  UNASSIGNED        (5, 1, "OUT_OF_SERVICE_BY_MAINTENANCE"),   //ok
  IN_DOWN           (1, 2, "OUT_OF_SERVICE"),  //ok
  MANAGEMENT_DOWN   (2, 2, "OUT_OF_SERVICE"),  //ok
  MAINTENANCE_DOWN  (3, 2, "OUT_OF_SERVICE"), //ok
  DISABLED_DOWN     (4, 2, "OUT_OF_SERVICE_BY_MAINTENANCE"), //ok
  UNASSIGNED_DOWN   (5, 2, "OUT_OF_SERVICE_BY_MAINTENANCE"), //ok
  NOT_APPLICABLE    (6, 3 , "OUT_OF_SERVICE");

  //------------------------------------------------------------------------------------------------------------------
  private final int    adminState;
  private final int    operState;
  private final String mtosiString;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param adminState   The MIB defined value
   * @param operState    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private CMServiceStateTranslation (final int adminState, final int operState, final String mtosiString)
  {
    this.adminState   = adminState;
    this.operState    = operState;
    this.mtosiString = mtosiString;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getAdminStateValue() {
    return adminState;
  }

  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getOperStateValue () {
    return operState;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in ManagedElementMediator class.
   * @param adminState  The MIB defined value
   * @param operState   The MIB defined value
   * @return the string representation used in ManagedElementMediator class.
   */
  public static String getMtosiString(final int adminState, final int operState)
  {
    CMServiceStateTranslation serviceStateTranslation = NOT_APPLICABLE;  // the return value

    for (CMServiceStateTranslation tmpServiceStateTranslation : values())
    {
      if (adminState == tmpServiceStateTranslation.getAdminStateValue() && operState == tmpServiceStateTranslation.getOperStateValue())
      {
        serviceStateTranslation = tmpServiceStateTranslation;
        break;
      }
    }
    return serviceStateTranslation.getMtosiString();
  }

  /**
   * Returns the string representation used in ManagedElementMediator class.
   * @param mtosiString  The MIB defined value
   * @param operState Operational State
   * @return the string representation used in ManagedElementMediator class.
   */
  public static int getAdminStateValue(final String mtosiString, final int operState)
  {
    CMServiceStateTranslation serviceStateTranslation = NOT_APPLICABLE;  // the return value

    for (CMServiceStateTranslation tmpServiceStateTranslation : values())
    {
      if (mtosiString.equals(tmpServiceStateTranslation.getMtosiString()) && operState == tmpServiceStateTranslation.getOperStateValue())
      {
        serviceStateTranslation = tmpServiceStateTranslation;
        break;
      }
    }
    return serviceStateTranslation.getAdminStateValue();
  }

  /**
   * Returns the string representation used in ManagedElementMediator class.
   * @param mtosiString  The MIB defined value
   * @param adminState Administrative state
   * @return the string representation used in ManagedElementMediator class.
   */
  public static int getOperStateValue(final String mtosiString, final int adminState)
  {
    CMServiceStateTranslation serviceStateTranslation = NOT_APPLICABLE;  // the return value

    for (CMServiceStateTranslation tmpServiceStateTranslation : values())
    {
      if (mtosiString.equals(tmpServiceStateTranslation.getMtosiString()) && adminState == tmpServiceStateTranslation.getAdminStateValue())
      {
        serviceStateTranslation = tmpServiceStateTranslation;
        break;
      }
    }
    return serviceStateTranslation.getOperStateValue();
  }
}
