/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.adapter.facade;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.DTOBuilder;
import com.adva.nlms.mediation.config.dto.attr.PortF3NetAttr;
import com.adva.nlms.mediation.config.dto.attr.ProtectionGroupF3Attr;
import com.adva.nlms.mediation.config.mofacade.MOFacadeManager;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiProcessingFailureException;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.ProtectionSwitchMode;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import ws.v1.tmf854.ProcessingFailureException;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class MtosiProtectionGroupEFMDTOImpl implements MtosiProtectionGroupEFMDTO{

  private static Logger logger = LogManager.getLogger(MtosiProtectionGroupEFMDTOImpl.class);
  private static final Map<String, String> fieldMap = new HashMap<>();

  private static final String pattern = "/(shelf)=(\\d+)/(slot)=(\\d+)/.+";


  @MtosiFacadeAttribute(value = LayerAttributes.ALLOCATED_NUMBER)
  protected Integer _AllocatedNumber = null;

  @MtosiFacadeAttribute(value = LayerAttributes.FRAGMENT_SERVER_LAYER)
  protected String _FragmentServerLayer = null;

  @MtosiFacadeAttribute(value = LayerAttributes.REVERTIVE)
  protected String _Revertive;

  @MtosiFacadeAttribute(value = LayerAttributes.PROTECTIONSWITCHMODE)
  protected String _ProtectionSwitchMode = null;

  @MtosiFacadeAttribute(value = LayerAttributes.WAITTORESTORE)
  protected String _WaitToRestore = null;

  @MtosiFacadeAttribute(value = LayerAttributes.PROTECTIONSWITCHMODE_CM)
  protected String _ProtectionSwitchMode_CM = null;

  @MtosiFacadeAttribute(value = LayerAttributes.REVERTIVE_CM)
  protected String _Revertive_CM = null;

  @MtosiFacadeAttribute(value = LayerAttributes.SWITCHDIRECTION_CM)
  protected String _SwitchDirection_CM = null;

  private MOFacadeManager moFacadeManager;
  private static final Collection<String> expectedLayers = new ArrayList<>();
  private List<MtosiTerminationPointDTOImpl> ports = new ArrayList<>();
  private MtosiMOFacade mtosiMOFacade = null;
  private Set<String> layers;
  private String pgName;
  private NetworkElement networkElement;
  private String meName;
  private String mdName;
  private int pgNumber;

  private int revertive;

  static {
//    expectedLayers.add(LayeredParams.LR_LAG_FRAGMENT);
    expectedLayers.removeAll(expectedLayers); //clear layers
    expectedLayers.add(LayeredParams.LR_LAG);
    expectedLayers.add(LayeredParams.PROP_ADVA_PROTECTION_FSP150_CMNTU);
//    expectedLayers.add(LayeredParams.PROP_ADVA_ETHERNET);
  }

  public List<MtosiTerminationPointDTOImpl> getPorts() {
    return ports;
  }

  public MtosiProtectionGroupEFMDTOImpl(MOFacadeManager moFacadeManager) {
    this.moFacadeManager = moFacadeManager;
  }

  public void setLayers(Set<String> layers) {
    this.layers = layers;
  }

  public String setPGName() {
    return pgName;
  }

  public void setPGName(String pgName) {
    this.pgName = pgName;
  }

  @Override
  public NetworkElement getNetworkElement() {
    return networkElement;
  }

  @Override
  public Integer get_AllocatedNumber() {
    return _AllocatedNumber;
  }

  @Override
  public String get_FragmentServerLayer() {
    return _FragmentServerLayer;
  }

  @Override
  public String getPGName() {
    return pgName;
  }

  public void setNetworkElement(NetworkElement networkElement) {
    this.networkElement = networkElement;
  }

  @Override
  public String getMeName() {
    return meName;
  }

  public void setMeName(String meName) {
    this.meName = meName;
  }

  @Override
  public String getMdName() {
    return mdName;
  }

  public void setMdName(String mdName) {
    this.mdName = mdName;
  }

  @Override
  public int getPgNumber() {
    return pgNumber;
  }

  public void setPgNumber(int pgNumber) {
    this.pgNumber = pgNumber;
  }

  public void setRevertive(int revertive) {
    this.revertive = revertive;
  }

  public void set_AllocatedNumber(Integer _AllocatedNumber) {
    this._AllocatedNumber = _AllocatedNumber;
  }

  public void set_FragmentServerLayer(String _FragmentServerLayer) {
    this._FragmentServerLayer = _FragmentServerLayer;
  }

  @Override
  public String get_Revertive() {
    return _Revertive;
  }

  public void set_Revertive(String _Revertive) {
    this._Revertive = _Revertive;
  }

  @Override
  public String get_ProtectionSwitchMode() {
    return _ProtectionSwitchMode;
  }

  public void set_ProtectionSwitchMode(String _ProtectionSwitchMode) {
    this._ProtectionSwitchMode = _ProtectionSwitchMode;
  }

  public String get_WaitToRestore() {
    return _WaitToRestore;
  }

  public void set_WaitToRestore(String _WaitToRestore) {
    this._WaitToRestore = _WaitToRestore;
  }

  public String get_ProtectionSwitchMode_CM() {
    return _ProtectionSwitchMode_CM;
  }

  public void set_ProtectionSwitchMode_CM(String _ProtectionSwitchMode_CM) {
    this._ProtectionSwitchMode_CM = _ProtectionSwitchMode_CM;
  }

  public String get_Revertive_CM() {
    return _Revertive_CM;
  }

  public void set_Revertive_CM(String _Revertive_CM) {
    this._Revertive_CM = _Revertive_CM;
  }

  public String get_SwitchDirection_CM() {
    return _SwitchDirection_CM;
  }

  public void set_SwitchDirection_CM(String _SwitchDirection_CM) {
    this._SwitchDirection_CM = _SwitchDirection_CM;
  }

  public void validate() throws ProcessingFailureException {
    validateLayers();
    if (getMeName() == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.ME_NAME_MISSING);
    }

    MtosiUtils.existsMDOrMEEntity(getMdName(), getMeName(), getNetworkElement());
    if (getPGName() == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MISSING_FTPNM);
    }
    if (get_AllocatedNumber() == null || get_AllocatedNumber() != 2) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MISSING_OR_INVALID_ALLOCATED_NUMBER);
    }
    if (get_FragmentServerLayer() == null || get_FragmentServerLayer().compareTo(LayeredParams.LR_ETHERNET) != 0) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MISSING_OR_INVALID_FRAGMENT_SERVER_LAYER);
    }

    if (get_ProtectionSwitchMode() != null &&  !ProtectionSwitchMode.valueOfString(get_ProtectionSwitchMode()).equals(ProtectionSwitchMode.ONEPLUSONE)) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MISSING_OR_INVALID_PROTECTION_SWITCH_MODE);
    }

    if (get_Revertive() != null &&  BooleanTypeTranslation.valueOfString(get_Revertive()) != null && !BooleanTypeTranslation.valueOfString(get_Revertive()).equals(BooleanTypeTranslation.DISABLED)) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.INVALID_REVERTIVE_ATTRIBUTE);
    }

//    if (get_SwitchDirection() != null) {
//      if (!get_SwitchDirection().equals("Unidirectional")) {
//        throw ExceptionUtils.createNewMPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
//            MtosiErrorConstants.INVALID_SWITCH_TO_RESTORE_ATTRIBUTE);
//      }
//    }
    validatePG();
    // validatePorts();
  }

  @Override
  public DTO<ProtectionGroupF3Attr> getMODTO() {
    return null;
  }

  @Override
  public MtosiMOFacade getMtosiFacade() {
    if (mtosiMOFacade == null) {
      mtosiMOFacade = moFacadeManager.getFacadeViaNeId(MtosiMOFacade.class, getNetworkElement().getID());
    }
    return mtosiMOFacade;
  }

  protected void validateLayers() throws ProcessingFailureException {
    if (layers != null) {
      if (!layers.containsAll(expectedLayers)) {
        String missingLayers = "";
        ArrayList<String> list = new ArrayList<>(expectedLayers);
        list.removeAll(layers);
        for (String layer : list) {
          missingLayers += layer + ", ";
        }
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.MISSING_TRANSMISSION_PARAMETER + ": " + replaceLast(missingLayers, ", ", "."));
      }
    }
  }

  protected String replaceLast(String string, String toReplace, String replacement) {
    int pos = string.lastIndexOf(toReplace);
    if (pos > -1) {
      return string.substring(0, pos)
          + replacement
          + string.substring(pos + toReplace.length(), string.length());
    } else {
      return string;
    }
  }

  protected void validatePG() throws ProcessingFailureException {
    if (!getPGName().matches(pattern)) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.INVALID_FTPNM);
    }
    //pgNumber = com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory.pgNumberFromName(getPGName());
    DTO pgDTO = getMtosiFacade().findDTOViaMtosiName(networkElement.getID(), getPGName(), ProtectionGroupF3Attr.class);
    if (pgDTO != null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_CAPACITY_EXCEEDED, MtosiErrorConstants.PROTECTION_GROUP_EXISTS);
    }
  }

  public DTO<ProtectionGroupF3Attr> getMODTOForCM(MtosiAddress mtosiAddress) throws MtosiProcessingFailureException {
    DTO<ProtectionGroupF3Attr> protectionGroupDTO = DTOBuilder.getInstance().newDTO(ProtectionGroupF3Attr.class);
    DTO<PortF3NetAttr> net1 = getMtosiFacade().findDTOViaMtosiName(networkElement.getID(), "/shelf=1/slot=1/port=NET-1", PortF3NetAttr.class);
    DTO<PortF3NetAttr> net2 = getMtosiFacade().findDTOViaMtosiName(networkElement.getID(), "/shelf=1/slot=1/port=NET-2", PortF3NetAttr.class);
    protectionGroupDTO.putOrReplace(ProtectionGroupF3Attr.MTOSI_NAME, getPGName());
//    if(!getPGName().equals("/shelf=1/slot=1/port=NET")){
//      String label = getPGName().replace("/shelf=1/slot=1/","");
      protectionGroupDTO.putOrReplace(ProtectionGroupF3Attr.USER_LABEL, getPGName());
//    }
    if (get_ProtectionSwitchMode() != null)
      protectionGroupDTO.putOrReplace(ProtectionGroupF3Attr.SWITCH_MODE, ProtectionSwitchMode.valueOfString(get_ProtectionSwitchMode()).getId());
    if (get_Revertive() != null) protectionGroupDTO.putOrReplace(ProtectionGroupF3Attr.REVERTIVE, BooleanTypeTranslation.valueOfString(get_Revertive()).getBooleanValue());
    //if (get_WaitToRestore() != null) protectionGroupDTO.putOrReplace(ProtectionGroupF3Attr.WAIT_TO_RESTORE, Integer.parseInt(get_WaitToRestore()));
    protectionGroupDTO.putOrReplace(ProtectionGroupF3Attr.WORK_PORT_ENTITY_INDEX, net1.getValue(PortF3NetAttr.ENTITY_INDEX));
    protectionGroupDTO.putOrReplace(ProtectionGroupF3Attr.PROT_PORT_ENTITY_INDEX, net2.getValue(PortF3NetAttr.ENTITY_INDEX));
    protectionGroupDTO.putOrReplace(ProtectionGroupF3Attr.DIRECTION, 1);
    protectionGroupDTO.putOrReplace(ProtectionGroupF3Attr.PG_INDEX, 1);
    return protectionGroupDTO;
  }

  public void setValue(String fieldFQN, String value) {
    Method method = null;
    try {
      if (getFieldMap().containsKey(fieldFQN)) {
        method = MtosiProtectionGroupEFMDTOImpl.class.getDeclaredMethod(getFieldMap().get(fieldFQN), LayerAttributes.valueOfString(fieldFQN).get_paramType());
        method.invoke(this, fixValueType(LayerAttributes.valueOfString(fieldFQN).get_paramType(), value));
      }
    } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
      logger.error("Failed to find proper setter or set value", e);
    } catch (Exception ex) {
      logger.error("Generic failure to find proper setter or set value", ex);
    }
  }

  //reflection utils
  protected static Map<String, String> getFieldMap() {
    return fieldMap;
  }

  @SuppressWarnings("unchecked")
  private static Object fixValueType(Class clazz, String value) {
    if (clazz.isAssignableFrom(Integer.class)) {
      return Integer.valueOf(value);
    } else if (clazz.isAssignableFrom(Boolean.class)) {
      return Boolean.valueOf(value);
    } else {
      return value;
    }
  }

  static {
    for (Field field : MtosiProtectionGroupEFMDTOImpl.class.getDeclaredFields()) {
      String name = field.getName();
      LayerAttributes mappedFieldValue = null;
      Annotation[] annotations = field.getDeclaredAnnotations();
      for (Annotation annotation : annotations) {
        if (annotation.annotationType().isAssignableFrom(MtosiFacadeAttribute.class)) {
          mappedFieldValue = ((MtosiFacadeAttribute) annotation).value();
        }
      }
      if (mappedFieldValue != null) {
        getFieldMap().put(mappedFieldValue.getMappedValue(), "set" + name);
      }
    }
  }

}
