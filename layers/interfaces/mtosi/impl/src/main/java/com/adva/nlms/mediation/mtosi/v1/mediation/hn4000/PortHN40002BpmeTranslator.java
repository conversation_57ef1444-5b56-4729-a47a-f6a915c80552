/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.hn4000;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN40002BpmeSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000BondingSPProperties;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.hn4000.NetworkElementHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN40002Bpme;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet2BASE_TL;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiHNUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNLineStatusTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNObjectStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNRetrainReasonTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.ServiceStateTranslation;
import v1.tmf854.DirectionalityT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ResourceStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import ws.v1.tmf854.ProcessingFailureException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map.Entry;
import java.util.Set;

/**
 * This class is a HN4000 2Bpme port MTOSI Translator.
 */
public class PortHN40002BpmeTranslator extends MtosiTranslator {
  private PortHN40002Bpme port;

  public PortHN40002BpmeTranslator(PortHN40002Bpme port) {
    this.port = port;
  }

  @Override
  public PhysicalTerminationPointT toMtosiPTP() throws ProcessingFailureException {
	 final PortHN40002BpmeSPProperties properties = port.getPortSPProperties();
    ObjectFactory objFactory = new ObjectFactory();
    PhysicalTerminationPointT physicalTerminationPointT = objFactory.createPhysicalTerminationPointT();

    // PTP element name
    NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributes(port);
    physicalTerminationPointT.setName(objFactory.createPhysicalTerminationPointTName(namingAttributes));

    // discoveredName
    final String ptpNm = namingAttributes.getPtpNm();
    if (ptpNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
    }
    physicalTerminationPointT.setDiscoveredName(objFactory.createPhysicalTerminationPointTDiscoveredName(ptpNm));

    // namingOS
    physicalTerminationPointT.setNamingOS(objFactory.createPhysicalTerminationPointTNamingOS(OSFactory.getNmsName()));

    // source
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    physicalTerminationPointT.setSource(objFactory.createPhysicalTerminationPointTSource(source));

    // resource state
    ResourceStateT resourceState = new ResourceStateT();
    if (port.getOperState() == ServiceStateTranslation.UP_NOT_PRESENT.getOperStateValue()) {
      resourceState.setValue(ResourceStateEnumT.PLANNED);
    }
    else {
      resourceState.setValue(ResourceStateEnumT.INSTALLED);
    }
    physicalTerminationPointT.setResourceState(objFactory.createPhysicalTerminationPointTResourceState(resourceState));

    // direction
    physicalTerminationPointT.setDirection(objFactory.createPhysicalTerminationPointTDirection(DirectionalityT.D_BIDIRECTIONAL));

    // edgePoint
    physicalTerminationPointT.setEdgePoint(objFactory.createPhysicalTerminationPointTEdgePoint(true));

    // layers
    LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    NetworkElement ne = port.getNE();
	boolean hn400 = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400;

	//-----------Start Layers---------------
	LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL,
			LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.ACTIVE_PARAM);
	//------------Empty LR_DIGIAL_SIGNAL_RATE Layer ---------------------
	LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_DIGIAL_SIGNAL_RATE);
	// -----------Start LR_DSL Layer -------------
	LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_DSL);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSL,LayeredParams.LrDsl.DSL_TYPE_PARAM,
			LayeredParams.DEFAULT_DSL_TYPE);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSL,LayeredParams.LrDsl.ADMINISTRATION_CONTROL_PARAM,
			MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, port.getAdminState()));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSL,LayeredParams.LrDsl.SERVICE_STATE_PARAM,
			ServiceStateTranslation.getMtosiString(port.getAdminState(), port.getOperState()));
	// -----------Start LR_SHDSL Layer -------------
	LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_SHDSL);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_SHDSL, LayeredParams.LrShdsl.DATA_RATE_PARAM,
			MtosiHNUtils.bytesToKbs(properties.get(PortHN40002BpmeSPProperties.VL.Speed)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_SHDSL, LayeredParams.LrShdsl.SNR_MGN_PARAM, 
			marginToMtosi(properties.get(PortHN40002BpmeSPProperties.VI.SnrMgn)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_SHDSL, LayeredParams.LrShdsl.ATTENUATION_PARAM, 
			marginToMtosi(properties.get(PortHN40002BpmeSPProperties.VI.Attenuation)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_SHDSL, LayeredParams.LrShdsl.LINE_STATUS_PARAM, 
			HNLineStatusTranslation.getLineStatesFromIntValue(properties.get(PortHN40002BpmeSPProperties.VI.LineStatus)));
	
	// -----------Start PROP_HATTERAS_SHDSL Layer -------------
	LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_SHDSL);
	Integer secondaryState = properties.get(PortHN40002BpmeSPProperties.VI.SecondaryState);
	if (secondaryState != null) 
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_SHDSL, LayeredParams.PropHatterasShdsl.SECONDARY_STATE_PARAM,
				MtosiUtils.getMtosiString(HNObjectStateTranslation.NOT_APPLICABLE,secondaryState));
	else 
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_SHDSL, LayeredParams.PropHatterasShdsl.SECONDARY_STATE_PARAM,
				"");
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_SHDSL, LayeredParams.PropHatterasShdsl.DESCRIPTION_PARAM, 
			String.valueOf(properties.get(PortHN40002BpmeSPProperties.VS.Desc)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_SHDSL, LayeredParams.PropHatterasShdsl.PEER_SNR_MGN_PARAM, 
			marginToMtosi(properties.get(PortHN40002BpmeSPProperties.VI.PeerSnrMgn)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_SHDSL, LayeredParams.PropHatterasShdsl.PEER_ATTENUATION_PARAM, 
			marginToMtosi(properties.get(PortHN40002BpmeSPProperties.VI.PeerAttenuation)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_SHDSL, LayeredParams.PropHatterasShdsl.SPAN_UPTIME_PARAM, 
			upTimeToMtosi(properties.get(PortHN40002BpmeSPProperties.VL.SpanUptime)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_SHDSL, LayeredParams.PropHatterasShdsl.RETRAIN_REASON_PARAM, 
			HNRetrainReasonTranslation.getMtosiString(properties.get(PortHN40002BpmeSPProperties.VI.RetrainReason)));
	String remotePort;
	remotePort = calculateRemote2BPMEPort(properties, hn400);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_SHDSL, LayeredParams.PropHatterasShdsl.REMOTE_2BPME_PARAM, 		
			remotePort);
	if (!hn400) {
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_SHDSL, LayeredParams.PropHatterasShdsl.SPAN_PROFILE_ID_PARAM, 
				String.valueOf(properties.get(PortHN40002BpmeSPProperties.VI.SpanProfileID)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_SHDSL, LayeredParams.PropHatterasShdsl.SPAN_ALARM_PROFILE_ID_PARAM, 
				String.valueOf(properties.get(PortHN40002BpmeSPProperties.VI.SpanAlarmProfile)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_SHDSL, LayeredParams.PropHatterasShdsl.AFDI_ENABLED_PARAM, 
				MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, properties.get(PortHN40002BpmeSPProperties.VI.AfdiEnabled)== null? 3: properties.get(PortHN40002BpmeSPProperties.VI.AfdiEnabled)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_SHDSL, LayeredParams.PropHatterasShdsl.AFDI_DELAY_PARAM, 
				String.valueOf(properties.get(PortHN40002BpmeSPProperties.VL.AfdiDelay)));
	}
	
    physicalTerminationPointT.setTransmissionParams(objFactory.createPhysicalTerminationPointTTransmissionParams(layeredParametersListT));
    return physicalTerminationPointT;
  }
  /**
   * The remote2BPMEPort returned via SNMP and the MO layer is simply the Integer 2BPME number.  It does not contain the shelf
   * information.  We need to return /shelf=#/slot=1/2BPME-#.
   * On the HN4000, the solution is simple, the remote 2BPME port is always shelf=1/slot=1.
   * On the HN400, the shelf could be from 1 to 5, in a stack configuration.
   * This method, uses the ActualPME ports from the Bonded port, to determine the shelf.
   * When there are more than one 2BPME port in the bonding with the matching remote number, we must lookup those 2BPME ports on the HN4000.
   * and compare it's remote2BPMEPort to this one.
   * @param properties
   * @param hn400
   * @returns the correct /shelf=_/slot=1/port=2BPME-# or an empty string if it is 0 or not found.
   */
	
	private String calculateRemote2BPMEPort(final PortHN40002BpmeSPProperties properties, boolean hn400) {
		String remotePort = "";
		Integer remote2bmpePort = properties.get(PortHN40002BpmeSPProperties.VI.Rmt2bpmeNo);
		if (remote2bmpePort != null && remote2bmpePort != 0) {
				if (hn400) {
					NetworkElementHN4000 peer = (NetworkElementHN4000) (port.getNE()).getPeerNetworkElement();
					String ftpName = "/port=ETH-" + port.getNE().getNeIndex();
					PortHN4000Ethernet2BASE_TL bondedPort = peer.getBondedPort(ftpName);
					if (bondedPort != null) {
						HashMap<Integer, Set<Integer>> map = bondedPort.getPortSPProperties().get(PortHN4000BondingSPProperties.VH.PmePortMap);
						ArrayList<String> result = findPME(map, properties.get(PortHN40002BpmeSPProperties.VI.Rmt2bpmeNo));
						if (result.size() == 1) {
							remotePort = result.get(0);
						} else if (result.size() > 1) {
							remotePort = findRemotePort(peer, result);
						}
					}
				} else {
					remotePort = "/shelf=1/slot=1/port=2BPME-" + remote2bmpePort;
				}
			}
		return remotePort;
	}
	
  /**
   * The results ArrayList contains the mtosiName for each of the HN4000 2Bpme ports with the matching remote2bpmePort.
   *  retrieve each port and compare it's remote2BPMEPort to "myPort"
 * @param peer 
   * @param results
   * @return
   */
	private String findRemotePort(NetworkElementHN4000 peer, ArrayList<String> results) {
		String myPortName = port.getMtosiName();
		Integer myPort = Integer.valueOf(myPortName.substring(27));
		for (String pmePortString: results) {
			// /shelf=#/slot=1/port=2BPME-$
			int shelf = Integer.parseInt(pmePortString.substring(7,8));
			String portName = pmePortString.substring(21);
			PortHN40002Bpme pmeport = (PortHN40002Bpme) peer.getMTOSIWorker().getPortByName(portName, shelf,1);
			if (myPort.equals(pmeport.getPortSPProperties().get(PortHN40002BpmeSPProperties.VI.Rmt2bpmeNo))) {
				return pmePortString;
			}
		}
		return ""; //In cases where the bonding does not contain all the 2BPME ports, we will NOT be able to return the correct value.
				   //This case is an invalid configuration.
		}

	private ArrayList<String> findPME(HashMap<Integer, Set<Integer>> map, Integer pmeNum) {
			  ArrayList<String> result = new ArrayList<String>();
				for (Entry<Integer, Set<Integer>> entries : map.entrySet()) {
					int shelf = entries.getKey();
					for (Integer portNum : entries.getValue()) {
						if (portNum != null && portNum.equals(pmeNum)) {
							String lagMember = "/shelf=" + shelf + "/slot=1/port=2BPME-" + portNum;
							result.add(lagMember);
						}
					}
				}
				return result;
			}
	


	/**
	   * Convert Time Ticks to Seconds
	   * @param uptime
	   * @return
	   */
	private String upTimeToMtosi(Long uptime) {
		return uptime == null ? "" : String.valueOf(uptime/100);
	}
	
	/**
	 *  Convert the Margins and the Attenuation to an Mtosi String
	 *  -127 to 128 should be displayed as is,  65535 should be notApplicable and 
	 *  anything else is "invalid" 
	 * @param value of the margin.
	 * @return
	 */
	private String marginToMtosi(Integer value) {
		if (value != null) {
			if (value >= -127 && value <= 128) {
				return String.valueOf(value);
			} else if (value.intValue() == 65535) {
				return "notApplicable"; // Is this a constant somewhere?
			}
		} 
		return String.valueOf(value);
		
	}
}
