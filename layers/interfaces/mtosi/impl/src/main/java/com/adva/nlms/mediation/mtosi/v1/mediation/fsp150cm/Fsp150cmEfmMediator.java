/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm;

import com.adva.nlms.common.config.EntityClass;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ModuleCPMRSPPropertiesFSP150CM;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.module.cpmr.MTOSIModuleCPMRFSP150CM;
import com.adva.nlms.mediation.config.f3_efm.NetworkElementF3_EFM;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAdministrationControlTranslation;
import v1.tmf854.EqVendorExtensionsT;
import v1.tmf854.EquipmentHolderT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.MEVendorExtensionsT;
import v1.tmf854.ObjectFactory;

import jakarta.xml.bind.JAXBElement;
import javax.xml.namespace.QName;

public class Fsp150cmEfmMediator extends Fsp150cmMediator {
  private NetworkElementF3_EFM ne;

  public Fsp150cmEfmMediator(NetworkElementF3_EFM ne) {
    super(ne);
    this.ne = ne;
  }

  @Override
  protected String getShelfEquipment (EquipmentSPProperties properties) {
    return MtosiConstants.EQUIPMENT_EFM;
  }

  @Override
  protected void populateIpAddress (MEVendorExtensionsT extensions) {
    NetworkElement peerNe = ne.getPeerNetworkElement();
    JAXBElement jeProductName = new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
        MtosiConstants.VENDOR_EFM_MGR_PRODUCT_NAME), String.class, peerNe.getMTOSIWorker().getProductName());
    extensions.getAny().add(jeProductName);
    JAXBElement jeMeName = new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
        MtosiConstants.VENDOR_EFM_MGR_ME_NAME), String.class, peerNe.getName());
    extensions.getAny().add(jeMeName);
  }

  @Override
  protected JAXBElement<EqVendorExtensionsT> getVendorExtensions(EquipmentSPProperties properties) {
    properties = ne.getModule().getEquipmentSPProperties();
    if (!(properties instanceof ModuleCPMRSPPropertiesFSP150CM)) {
      throw new IllegalArgumentException("getVendorExtensions is accepting only ModuleCPMRSPPropertiesFSP150CM!");
    }
    ModuleCPMRSPPropertiesFSP150CM props = (ModuleCPMRSPPropertiesFSP150CM) properties;
    ObjectFactory objFactory = new ObjectFactory();
    EqVendorExtensionsT extensions = new EqVendorExtensionsT();
    String adminControl = "";
    if (props.get(EquipmentSPProperties.VI.AdminState) < 6) {
      adminControl = MtosiUtils.getMtosiString(CMAdministrationControlTranslation.NOT_APPLICABLE, props.get(EquipmentSPProperties.VI.AdminState));
    }
    extensions.getAny().add(new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
        MtosiConstants.VENDOR_ADMINISTRATION_CONTROL), String.class, adminControl));
    extensions.getAny().add(new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
        MtosiConstants.VENDOR_SECONDARY_STATE), String.class, props.get(ModuleCPMRSPPropertiesFSP150CM.VS.SecondaryState)));
    setVendorExtensionsForEFM(ne.getModule(), extensions);
    return objFactory.createEquipmentTVendorExtensions(extensions);
  }

  private void setVendorExtensionsForEFM(MTOSIModuleCPMRFSP150CM cpModule, EqVendorExtensionsT extensions) {
    ObjectFactory objFactory = new ObjectFactory();
    ModuleCPMRSPPropertiesFSP150CM props = (ModuleCPMRSPPropertiesFSP150CM) cpModule.getEquipmentSPProperties();
    LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET);
    // this translation is based on the following description:
    // ethernetCPMRCardLLFMode:
    // 1=llfmode-none 2=llfmode-acc2acc 3=llfmode-net2acc 4=llfmode-both
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
        LayeredParams.LrPropAdvaEthernet.NETWORK_TO_ACCESS_LINK_LOSS_FORWARDING_PARAM,
        MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, props.get(ModuleCPMRSPPropertiesFSP150CM.VI.LlfMode) == 3 || props.get(ModuleCPMRSPPropertiesFSP150CM.VI.LlfMode) == 4 ?
            MIB.RFC1253.TRUTH_VALUE_TRUE : MIB.RFC1253.TRUTH_VALUE_FALSE));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
        LayeredParams.LrPropAdvaEthernet.ACCESS_TO_ACCESS_LINK_LOSS_FORWARDING_PARAM,
        MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, props.get(ModuleCPMRSPPropertiesFSP150CM.VI.LlfMode) == 2 || props.get(ModuleCPMRSPPropertiesFSP150CM.VI.LlfMode) == 4 ?
            MIB.RFC1253.TRUTH_VALUE_TRUE : MIB.RFC1253.TRUTH_VALUE_FALSE));
    // FSP 150CPMR traffic management features are always disabled, therefore this parameter always returns False
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
        LayeredParams.LrPropAdvaEthernet.TRAFFIC_MANAGEMENT_ENABLED_PARAM,
        MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, MIB.RFC1253.TRUTH_VALUE_FALSE));
    JAXBElement<LayeredParametersListT> transmissionParams = objFactory
        .createConnectionTerminationPointTTransmissionParams(layeredParametersListT);
    extensions.getAny().add(transmissionParams);
  }

  @Override
  protected JAXBElement<EqVendorExtensionsT> getEquipmentVendorExtensions(EquipmentSPProperties props) {
    ObjectFactory objFactory = new ObjectFactory();
    EqVendorExtensionsT extensions = new EqVendorExtensionsT();
    String adminControl = "";
    if (props.get(EquipmentSPProperties.VI.AdminState) < 4) {
      adminControl = MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, props.get(EquipmentSPProperties.VI.AdminState));
    }
    extensions.getAny().add(new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
        MtosiConstants.VENDOR_ADMINISTRATION_CONTROL), String.class, adminControl));
    extensions.getAny().add(new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
        MtosiConstants.VENDOR_SECONDARY_STATE), String.class, "Active"));
    return objFactory.createEquipmentTVendorExtensions(extensions);
  }

  @Override
  public String getRelativeNameForProperties(EquipmentSPProperties properties) {
    final int neType = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI();
    int classType = properties.get(EquipmentSPProperties.VI.ClassType);
    String relativeName = "";
    switch (classType) {
      case EntityClass.SHELF:
        relativeName = MtosiConstants.DEFAULT_SHELF_NAME;
        break;
      case EntityClass.MODULE:
        if (properties.isSFP()) {
          relativeName = getRelativeNameModule(properties);
        } else {
          relativeName = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT +
              properties.get(EquipmentSPProperties.VI.RelativePosition);
        }
        break;
      case EntityClass.POWER_SUPPLY:
        if (neType == NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM) {
          return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT +
              (properties.get(EquipmentSPProperties.VI.RelativePosition) == 1 ? MtosiConstants.SLOT_PSU1 : MtosiConstants.SLOT_PSU2);
        }
        relativeName = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT +
            (properties.get(EquipmentSPProperties.VI.RelativePosition) == 3 ? MtosiConstants.SLOT_PSU1 : MtosiConstants.SLOT_PSU2);
        break;
      case EntityClass.PORT:
        relativeName = Fsp150cmMediator.getRelativeNamePort(properties);
        break;
      case EntityClass.FAN:
        if (neType == NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM) {
          return  MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT +
              (properties.get(EquipmentSPProperties.VI.RelativePosition) == 1 ? MtosiConstants.SLOT_FAN1 : MtosiConstants.SLOT_FAN2);
        }
        relativeName = MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT +
            (properties.get(EquipmentSPProperties.VI.RelativePosition) == 3 ? MtosiConstants.SLOT_FAN1 : MtosiConstants.SLOT_FAN2);
        break;
      default:
    }
    return relativeName;
  }

  @Override
  protected JAXBElement<String> getSfpManufacturerDate (EquipmentSPProperties properties) {
    ObjectFactory objFactory = new ObjectFactory();
    return objFactory.createEquipmentTManufacturerDate("");
  }

  @Override
  protected void getAcceptablePSUEquipmentTypes(ObjectFactory objFactory, EquipmentHolderT equipmentHolder, EquipmentSPProperties properties) {
    String productName = ne.getMTOSIWorker().getProductName();

    EquipmentHolderT.AcceptableEquipmentTypeList list = objFactory.createEquipmentHolderTAcceptableEquipmentTypeList();
    if (productName.indexOf("AC") > 0) {
      list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_PSU_AC);
    } else if (productName.indexOf("DC") > 0) {
      list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_PSU_DC);
    } else {
      list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_PSU_AC);
      list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_PSU_DC);
    }
    equipmentHolder.setAcceptableEquipmentTypeList(objFactory.createEquipmentHolderTAcceptableEquipmentTypeList(list));
  }

  @Override
  protected String getPsuType(EquipmentSPProperties properties) {
    String productName = ne.getMTOSIWorker().getProductName();
    if (productName.indexOf("AC") > 0) {
      return MtosiConstants.EQUIPMENT_PSU_AC;
    } else if (productName.indexOf("DC") > 0){
      return MtosiConstants.EQUIPMENT_PSU_DC;
    } else {
      return MtosiConstants.EQUIPMENT_PSU;

    }
  }

}
