/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;

/**
HnLagLoadBalanceValue ::= TEXTUAL-CONVENTION
     STATUS      current
     DESCRIPTION "Enumerated list of possible methods for balancing traffic load
                 among ports in a LAG, one of {srcDstMac*,evcId}."
     SYNTAX      INTEGER {
                     invalid      (0),
                     srcDstMac    ('0AC1'h),
                     evcId        ('E0C2'h)
                 }
  */
public enum HNLAGLoadBalanceTranslation {
    invalid      (0),
    SrcDstMac    (2753), 	//('0AC1'h),  
    EvcId        (57538);	//('E0C2'h);
    
    private int mibValue;
    
    private int getMibValue() {
		return mibValue;
	}

	private HNLAGLoadBalanceTranslation(int code) {
    	this.mibValue = code;
    }

    public static String getMtosiString(final Integer mibValue) {
    	if (mibValue == null) {
    		return MtosiConstants.NOT_APPLICABLE;
    	}
    	for (HNLAGLoadBalanceTranslation value: values() ) {
    		if (value.getMibValue() == mibValue.intValue()) {
    			return value.name(); 
    		}
    	}
    	//the value was not found, return the value passed in
    	return String.valueOf(mibValue);
    }
    
    public static int getMibValue(final String name) {
    	for (HNLAGLoadBalanceTranslation value: values() ) {
    		if (value.name().equals(name)) {
    			return value.getMibValue(); 
    		}
    	}
    	//Returning -1, the value passed in was not found.
    	return -1;
    }
}
