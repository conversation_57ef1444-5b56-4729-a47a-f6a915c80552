/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.mediation.polling.PollingType;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.StartDiscoveryPollingResponseT;
import v1.tmf854ext.adva.StartDiscoveryPollingT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public class StartDiscoveryPollingWorker extends AbstractMtosiWorker {

  protected StartDiscoveryPollingT mtosiBody;
  protected StartDiscoveryPollingResponseT response = new StartDiscoveryPollingResponseT();
  protected NamingAttributesT meName;
  private NetworkElement ne;

  public StartDiscoveryPollingWorker(StartDiscoveryPollingT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "startDiscoveryPolling", "startDiscoveryPolling", "startDiscoveryPollingResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    meName = mtosiBody.getMeName();
    if (meName == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "The ME name has not been specified.");
    }

    if (meName.getMdNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
        MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!meName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        MtosiErrorConstants.MD_NOT_FOUND);
    }

    if (meName.getMeNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ME name has not been specified.");
    }
    ne = getMtosiCtrl().getLegacyMtosiMOFacade().getNEByName(meName.getMeNm());

    if (ne == null)
    {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        MtosiErrorConstants.ME_NOT_FOUND);
    }
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(ne.getDefaultNetworkElementTypeString());
  }

  @Override
  protected void mediate() throws Exception {

    ne.getPollingManager().preparePolling()
            .forType(PollingType.CONTINUOUS_DISCOVERY.getType())
            .execute();
  }


  @Override
  protected void response() throws Exception {
  }

  @Override
  public StartDiscoveryPollingResponseT getSuccessResponse()
  {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }

}