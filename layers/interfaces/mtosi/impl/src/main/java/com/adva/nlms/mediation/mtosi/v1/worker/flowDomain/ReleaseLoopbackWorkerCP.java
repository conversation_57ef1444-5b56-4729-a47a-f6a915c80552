/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.fsp150cp_mx.PortFSP150CP_MXAccess;
import com.adva.nlms.mediation.config.fsp150cp_mx.PortFSP150CP_MXNetwork;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;

import jakarta.xml.ws.Holder;

public class ReleaseLoopbackWorkerCP extends ReleaseLoopbackWorker
{
  public ReleaseLoopbackWorkerCP(Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne)
  {
    super(mtosiHeader, namingAttributes, ne);
  }

  @Override
  protected void parse() throws Exception {
    if (!NamingTranslationFactory.isPort(namingAttributes)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ptpNm has not been specified.");
    }
  }

  @Override
  protected void mediate() throws Exception {
    Port port = ManagedElementFactory.getPort(ne, namingAttributes.getPtpNm());
    if(port==null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(),
              ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, "The specified Port was not found.");
    }

    if(port instanceof PortFSP150CP_MXNetwork) {
      transactWAN((PortFSP150CP_MXNetwork)port);
    } else {
      transactLAN((PortFSP150CP_MXAccess)port);
    }
  }

  @Override
  protected void response () throws Exception {
    // empty method
  }

  private void transactLAN(PortFSP150CP_MXAccess port) throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure
  {
    NetworkElement locks[] = new NetworkElement[] { ne };

    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "releaseLoopback");
    try {
      logSecurity(ne, SystemAction.ReleaseLoopback, port.getMtosiName());
      port.releaseLoopback();
      NetTransactionManager.commitNetTransaction(id);
    }
    catch (NetTransactionException e)
    {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    catch (SPValidationException e)
    {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    catch (SNMPCommFailure e)
    {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    finally
    {
      NetTransactionManager.ensureEnd(id);
    }
  }

  private void transactWAN(PortFSP150CP_MXNetwork port)
          throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "releaseLoopback");
    try {
      logSecurity(ne, SystemAction.ReleaseLoopback, port.getMtosiName());
      port.releaseLoopback();
      NetTransactionManager.commitNetTransaction(id);
    } catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      NetTransactionManager.ensureEnd(id);
    }
  }
}