/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

/**
  *
  */
public enum HNDuplexModeTranslation {
    PROP_HATTERAS_Unknown       (1),
    Half          				(2),
    Full          				(3),
    PROP_HATTERAS_Auto          (4);
    
    private int mibValue;
    
    private int getMibValue() {
		return mibValue;
	}

	private HNDuplexModeTranslation(int code) {
    	this.mibValue = code;
    }

    public static String getMtosiString(final Integer mibValue) {
    	if (mibValue == null) {
        	return PROP_HATTERAS_Unknown.name();
    	}
    	for (HNDuplexModeTranslation value: values() ) {
    		if (value.getMibValue() == mibValue.intValue()) {
    			return value.name(); 
    		}
    	}
    	//the value was not found, return the value passed in
//		return MtosiConstants.UNKNOWN;
//    	return String.valueOf(mibValue);
    	return PROP_HATTERAS_Unknown.name();
    }
    
    public static int getMibValue(final String name) {
    	for (HNDuplexModeTranslation value: values() ) {
    		if (value.name().equals(name)) {
    			return value.getMibValue(); 
    		}
    	}
    	//Returning -1, the value passed in was not found.
    	return -1;
    }
}
