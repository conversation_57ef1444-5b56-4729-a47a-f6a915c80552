/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */

package com.adva.nlms.mediation.mtosi.v1.factory;

import com.adva.nlms.common.performance.types.Location;
import com.adva.nlms.common.performance.types.PerformanDataType;
import com.adva.nlms.common.performance.types.TimeType;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiPMMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.PerformanceMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.PMUtils;
import com.adva.nlms.mediation.performance.data.PerformanceManagerMtosi;
import com.adva.nlms.mediation.performance.data.PMData;
import com.adva.nlms.mediation.performance.data.PMFilter;
import com.adva.nlms.mediation.performance.data.PMInterval;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854ext.adva.GranularityListT;
import v1.tmf854ext.adva.PMDataListT;
import v1.tmf854ext.adva.PMDataT;
import v1.tmf854ext.adva.PMLocationListT;
import v1.tmf854ext.adva.PMParameterNameListT;
import v1.tmf854ext.adva.PMTPSelectListT;
import v1.tmf854ext.adva.PMTPSelectT;
import ws.v1.tmf854.ProcessingFailureException;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class PMFactory
{

	public static Object getEntityForNaming(NamingAttributesT namingAttributes) throws Exception
	{

		if (NamingTranslationFactory.isPort(namingAttributes))
		{
			return ManagedElementFactory.getPort(namingAttributes);
		}
		else if (NamingTranslationFactory.isFlow(namingAttributes))
		{
			if(ManagedElementFactory.isF3NE(namingAttributes))
			{
				return ManagedElementFactory.getCMFlow(namingAttributes);
			}
			else
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
						"Specified entity does not exist or does not support Performance Monitoring.");
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
			}
		}
		else if (NamingTranslationFactory.isFtpBonding(namingAttributes) || NamingTranslationFactory.isFtpLag(namingAttributes))
		{
			return ManagedElementFactory.getHN4000Ftp(namingAttributes);
		}
    else if (NamingTranslationFactory.isTCP(namingAttributes))
    {
      return ManagedElementFactory.getTCP(namingAttributes);
    }
		else if (NamingTranslationFactory.isManagedElement(namingAttributes))
		{
			return ManagedElementFactory.getAndValidateNE(namingAttributes);
		}
		else
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
					"Specified entity does not exist or does not support Performance Monitoring.");
			ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
			throw pfe;
		}

	}

	public static PMDataListT getHistoricalPMDataList(String startTime, String endTime, PMTPSelectListT entityList, PMParameterNameListT parameterList)
			throws Exception
	{
		Date startDate = PMUtils.dateFromPMTime(startTime);
		if (startDate == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
					"Invalid format for startDate.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);

		}

		Date endDate = PMUtils.dateFromPMTime(endTime);
		if (endDate == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
					"Invalid format for endDate.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);

		}

		if (startDate.after(endDate))
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
					"startDate cannot be after endDate.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}

		PMDataListT dataList = new PMDataListT();

		List<PMTPSelectT> list = entityList.getPmtpsel();
		for (Iterator iter = list.iterator(); iter.hasNext();)
		{
			PMTPSelectT element = (PMTPSelectT) iter.next();
			NamingAttributesT naming = element.getName();
			GranularityListT granularity = element.getGranularityList();
			ArrayList<TimeType> granList = MtosiPMMediator.mtosiGranularityListToTimeTypes(granularity, true);
			PMLocationListT location = element.getPmLocationList();
			ArrayList<Location> locationList = MtosiPMMediator.mtosiLocationListToNMSLocation(location);
			// MtosiPMMediator.mtosiLocationListToLocations(location);
			ArrayList<PerformanDataType> parameterListNMS = MtosiPMMediator.mtosiParamsToNMSParams(parameterList);
			PMFilter filter = new PMFilter(granList, locationList, parameterListNMS);
			PMInterval interval = new PMInterval(startDate, endDate);
			Object entity = getEntityForNaming(naming);
			if(entity==null)
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
				"Specified entity does not exist or does not support Performance Monitoring.");
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
			}
			ArrayList<PMDataT> pmDataList = getHistoricalPMDataForEntity(entity, naming, filter, interval);
			for (Iterator iterResults = pmDataList.iterator(); iterResults.hasNext();)
			{
				PMDataT nextPMData = (PMDataT) iterResults.next();
				if (nextPMData != null)
				{
					dataList.getPmdata().add(nextPMData);
				}
			}
		}
		return dataList;
	}

	public static PMDataListT getCurrentPMDataList(PMTPSelectListT entityList, PMParameterNameListT parameterList) throws Exception
	{
		PMDataListT dataList = new PMDataListT();

		List<PMTPSelectT> list = entityList.getPmtpsel();
		for (Iterator iter = list.iterator(); iter.hasNext();)
		{
			PMTPSelectT element = (PMTPSelectT) iter.next();
			NamingAttributesT naming = element.getName();
			GranularityListT granularity = element.getGranularityList();
			ArrayList<TimeType> granList = MtosiPMMediator.mtosiGranularityListToTimeTypes(granularity, false);
      ArrayList<Location> locationList = new ArrayList<>();
//			PMLocationListT location = element.getPmLocationList();
//			ArrayList<Location> locationList = MtosiPMMediator.mtosiLocationListToNMSLocation(location);
			// MtosiPMMediator.mtosiLocationListToLocations(location);
			ArrayList<PerformanDataType> parameterListNMS = MtosiPMMediator.mtosiParamsToNMSParams(parameterList);
			PMFilter filter = new PMFilter(granList, locationList, parameterListNMS);

			Object entity = getEntityForNaming(naming);
			if(entity==null)
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
				"Specified entity does not exist or does not support Performance Monitoring.");
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
			}
			
			ArrayList<PMDataT> pmDataList = getCurrentPMDataForEntity(entity, naming, filter);
			for (Iterator iterResults = pmDataList.iterator(); iterResults.hasNext();)
			{
				PMDataT nextPMData = (PMDataT) iterResults.next();
				if (nextPMData != null)
				{
					dataList.getPmdata().add(nextPMData);
				}
			}
		}
		return dataList;
	}

	public static ArrayList<PMDataT> getCurrentPMDataForEntity(Object entity, NamingAttributesT naming, PMFilter filter) throws Exception

	{
		ArrayList<PMDataT> mtosiList = new ArrayList<PMDataT>();

		// Will really call PerformanceManagerImpl or PerformanceCtrlImpl here
		// Temporarily we'll just get some fake data
		List<PMData> nmsList = PerformanceManagerMtosi.newInstance().getPMCurrentDataForEntity(entity, filter);

		for (Iterator iter = nmsList.iterator(); iter.hasNext();)
		{
			PMData nextData = (PMData) iter.next();
			PMDataT nextMtosiData = PerformanceMediator.nmsPMDataToMtosiPMData(nextData);
			nextMtosiData.setTpName(naming);
			mtosiList.add(nextMtosiData);
		}
		return mtosiList;
	}

	public static ArrayList<PMDataT> getHistoricalPMDataForEntity(Object entity, NamingAttributesT naming, PMFilter filter, PMInterval interval)
			throws Exception

	{
		ArrayList<PMDataT> mtosiList = new ArrayList<PMDataT>();

		// Will really call PerformanceManagerImpl or PerformanceCtrlImpl here
		// Temporarily we'll just get some fake data
		List<PMData> nmsList = PerformanceManagerMtosi.newInstance().getPMHistoryDataForEntity(entity, filter, interval);

		for (Iterator iter = nmsList.iterator(); iter.hasNext();)
		{
			PMData nextData = (PMData) iter.next();
			PMDataT nextMtosiData = PerformanceMediator.nmsPMDataToMtosiPMData(nextData);
			nextMtosiData.setTpName(naming);
			mtosiList.add(nextMtosiData);
		}
		return mtosiList;
	}
}
