/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum GE20XLlfTxActionTypeTranslation implements TranslatableEnum {
    llf_tx_no_action	(1, "NoAction"),  	// perform no link loss forwarding action
    llf_tx_efm_signal 	(2, "EfmSignal"),	// EFM signal
    llf_tx_link_down	(3, "LinkDown"),   	// Link Down
    NOT_APPLICABLE		(-1,"N/A");

  private final int    mibValue;
  private final String mtosiString;

  private GE20XLlfTxActionTypeTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}