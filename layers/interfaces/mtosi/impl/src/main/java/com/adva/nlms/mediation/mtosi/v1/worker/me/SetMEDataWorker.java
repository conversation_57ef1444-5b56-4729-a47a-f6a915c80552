/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.MDRequestFailedException;
import com.adva.nlms.common.snmp.NoSuchMDObjectException;
import com.adva.nlms.mediation.common.serviceProvisioning.ManagedElementSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ShelfSPProperties;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.ConfigCtrlImpl;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementHdlrLocal;
import com.adva.nlms.mediation.config.NoSuchSubnetException;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.NetworkElementFSP150CP;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.ShelfFSP150CP;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiMEMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.ManagedElementMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ManagedElementUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.BadValueException;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPPropertiesData;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import com.adva.nlms.mediation.topology.SubnetHdlrLocal;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Node;
import v1.tmf854.HeaderT;
import v1.tmf854.MEVendorExtensionsT;
import v1.tmf854.ManagedElementT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854ext.adva.MEDataT;
import v1.tmf854ext.adva.SetMEDataResponseT;
import v1.tmf854ext.adva.SetMEDataT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.ArrayList;
import java.util.List;

public class SetMEDataWorker extends AbstractMtosiWorker {
  Logger LOG = LogManager.getLogger(this.getClass().getName());

  protected SetMEDataResponseT response = new SetMEDataResponseT();
  protected SetMEDataT mtosiBody;
  protected MEDataT meDataT;
  protected NamingAttributesT namingAttributes;

  private NetworkElement ne;

  public SetMEDataWorker(SetMEDataT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "setMEData", "setMEData", "setMEDataResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    if ((meDataT = mtosiBody.getMeInfo()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ME data has not been specified.");
    }

    if ((namingAttributes = meDataT.getMeName()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ME name has not been specified.");
    }

    if (namingAttributes.getMdNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
        MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!namingAttributes.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        MtosiErrorConstants.MD_NOT_FOUND);
    }

    if (namingAttributes.getMeNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ME name has not been specified.");
    }
    ne = ConfigCtrlImpl.get().getHandlers().getNeHdlr().getNEByName(namingAttributes.getMeNm());
    if (ne == null)
    {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        MtosiErrorConstants.ME_NOT_FOUND);
    }
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(ne.getDefaultNetworkElementTypeString());
  }

  @Override
  protected void mediate() throws Exception {
    MEVendorExtensionsT vendorExtensions = meDataT.getVendorExtensions().getValue();
    Node node = (Node) vendorExtensions.getAny().get(0); 

    // handle transmissionParams (FSP 150 CP only)
    if (node.getLocalName().equals("transmissionParams")) {
      if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() != NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP) {
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.ME_NOT_SUPPORTED);
      }

      NetworkElementFSP150CP neCP = (NetworkElementFSP150CP) ne;
      final ShelfSPProperties shelfSPProperties = neCP.getShelfFSP150CP()
              .getShelfSPProperties();

      ShelfSPProperties newProps = MtosiMEMediator.mtosiMEDataTToCPShelfProperties(meDataT, shelfSPProperties);
      if (newProps == null)
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(),
                ExceptionUtils.EXCPT_INVALID_INPUT, "No settable parameters were provided in the request.");
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      transactTransmissionParams(neCP, newProps);
    }
    // handle managementParams
    else if (node.getLocalName().equals("managementParams")) {
      ManagedElementSPProperties props = MtosiMEMediator.mtosiMEDataTToMEProperties(vendorExtensions);
      if (props == null) {
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                "No Vendor Extensions are specified.");
      }

      // compare community-strings
      // the community-strings are set to null if there is no change.
      // this is implemented to avoid unnecessary DB updates
      SNMPPropertiesData snmpProps = ne.getSNMPProperties();
      if (props.get(ManagedElementSPProperties.VI.SNMPProtocolVersion) == null ||
          props.get(ManagedElementSPProperties.VI.SNMPProtocolVersion) == snmpProps.version.getId()) {
        
        if (props.get(ManagedElementSPProperties.VS.ReadCommunity) != null) {
          if (props.get(ManagedElementSPProperties.VS.ReadCommunity).equals(snmpProps.snmpGetCommunity))
            props.set(ManagedElementSPProperties.VS.ReadCommunity, null);
        }
        if (props.get(ManagedElementSPProperties.VS.WriteCommunity) != null) {
          if (props.get(ManagedElementSPProperties.VS.WriteCommunity).equals(snmpProps.snmpSetCommunity))
            props.set(ManagedElementSPProperties.VS.WriteCommunity, null);
        }
      }

      String subnetPath = props.get(ManagedElementSPProperties.VS.SubnetPath);
      int parentId = 0;
      List<String> subnetsToCreate = null;
      // determine subnets to create
      if (subnetPath != null) {
        subnetsToCreate = new ArrayList<String>();
        parentId = ManagedElementUtils.getSubnetsToCreate(subnetPath, subnetsToCreate);
      }

      transactManagementParams(ne, props, parentId, subnetsToCreate);
    }
    else {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The Vendor Extensions are invalid.");
    }

  }

  private void transactTransmissionParams(NetworkElementFSP150CP ne, ShelfSPProperties props) throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "SetMEDataWorker");
    try
    {

      ShelfFSP150CP cpShelf = ne.getShelfFSP150CP();
      logSecurity(ne, SystemAction.ModifyNetwork, cpShelf.getMtosiName());
      cpShelf.setSettings(props);
      NetTransactionManager.commitNetTransaction(id);
    }
    catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally
    {
      NetTransactionManager.ensureEnd(id);
    }
  }

  private void transactManagementParams(NetworkElement ne, ManagedElementSPProperties meProps, int parentId, List<String> subnetsToCreate)
          throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure,
          MDRequestFailedException, NoSuchMDObjectException, BadValueException, ProcessingFailureException, NoSuchSubnetException {
    NetworkElementHdlrLocal neHdlr = ConfigCtrlImpl.get().getHandlers().getNeHdlr();
    SubnetHdlrLocal subnetHdlr = ConfigCtrlImpl.get().getHandlers().getSubnetHdlr();

    NetworkElement locks[] = new NetworkElement[] { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "SetMEDataWorker");
    try
    {

      logSecurity(ne, SystemAction.ModifyNetwork, ne.getName());

      // update IP-address and SNMP-settings
      ne.getMTOSIWorker().setMESettings(meProps);

      // update subnetPath
      if (subnetsToCreate != null) {
        // create missing subnets
        for (String subnet : subnetsToCreate) {
          parentId = subnetHdlr.createSubnet(parentId, subnet);
        }

        // if parent subnet id has been changed -> move NE
        if(ne.getParentID() != parentId)
          neHdlr.move(ne.getID(), parentId);
      }
      

      NetTransactionManager.commitNetTransaction(id);
    }
    catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (NoSuchSubnetException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (BadValueException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (MDRequestFailedException e) {
      e.printStackTrace();
    } finally
    {
      NetTransactionManager.ensureEnd(id);
    }
  }

  @Override
  protected void response() throws Exception {
    NetworkElement modifiedNE = ManagedElementFactory.getAndValidateAnyNE(namingAttributes);

    if (modifiedNE != null) {
      ManagedElementT modifiedME = ManagedElementMediator.nmsNeToManagedElementT(modifiedNE);
      response.setModifiedME(modifiedME);
    }
  }

  @Override
  public SetMEDataResponseT getSuccessResponse()
  {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}