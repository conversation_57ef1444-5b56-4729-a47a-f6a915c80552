/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.common.config.EntityClass;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.DTOBuilder;
import com.adva.nlms.mediation.config.dto.attr.FlowF3Attr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3AccAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3NetAttr;
import com.adva.nlms.mediation.config.dto.attr.ProtectionGroupF3Attr;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.adapter.facade.MtosiTerminationPointEFMDTOImpl;
import com.adva.nlms.mediation.mtosi.v1.factory.EntityEFMFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.f3.FspEFMMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMPortModeTranslation;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.TPDataListT;
import v1.tmf854.TPDataT;
import v1.tmf854ext.adva.CreateAndActivateFDFrResponseT;
import v1.tmf854ext.adva.CreateAndActivateFDFrT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.ws.Holder;
import java.util.List;

public class CreateAndActivateFDFrWorkerEFM extends CreateAndActivateFDFrWorkerCM{

  private MtosiMOFacade facade;
  private MtosiTerminationPointEFMDTOImpl mtosiTerminationPointEFMDTO;
  private TPDataListT tpDataListToModify;
  private DTO<PortF3NetAttr> net1, net2;
  private DTO<PortF3AccAttr> acc;
  private DTO<ProtectionGroupF3Attr> ftp;
  private DTO<FlowF3Attr> flow, fdfrForResponse, ctpDTO;
  private EntityEFMFactory factory;
  private DTO<ProtectionGroupF3Attr> ftpDTO;
  private boolean isACtp = false;
  private String flowName;
  private TPDataT tpDataACC;
  private TPDataT tpDataNET;
  private TPDataT tpDataNET2;
  private TPDataT tpDataFlow;
  private TPDataT tpDataFTP;
  private DTO<PortF3AccAttr> portACC;
  private DTO<PortF3NetAttr> portNET;
  private DTO<PortF3NetAttr> portNET2;
  private DTO<PortF3NetAttr> portN;
  private DTO<PortF3AccAttr> portA;
  private MtosiAddress mtosiAddress;

  private String accPortName = null;
  private String ctpName = null;


  public CreateAndActivateFDFrWorkerEFM(CreateAndActivateFDFrT mtosiBody, Holder<HeaderT> mtosiHeader,
                                           NetworkElement ne, NamingAttributesListT aEnd, NamingAttributesListT zEnd)
  {
    super(mtosiBody, mtosiHeader, ne, aEnd, zEnd);
    this.mtosiTerminationPointEFMDTO = new MtosiTerminationPointEFMDTOImpl();
  }


  @Override
  protected void parse() throws Exception {
    if ((this.createData = mtosiBody.getCreateData()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "The createData has not been specified.");
    }

    // it is an optional parameter
    this.tpsToModify = mtosiBody.getTpsToModify();

    if((this.namingAttributes = createData.getName()) == null ||
        namingAttributes.getFdfrNm()==null || namingAttributes.getFdfrNm().length()==0) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "FDFr name is missing.");
    }

    if (!MtosiUtils.isSameNetworkElement(aEnd.getName().get(0), zEnd.getName().get(0))) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(),
          ExceptionUtils.EXCPT_INVALID_INPUT, "aEnd and zEnd are not on the same Managed Element.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    String userLabel = null;
    String name = createData.getName().getFdfrNm();
    JAXBElement<String> userJAX = createData.getUserLabel();
    if(userJAX!=null)
    {
      userLabel = userJAX.getValue();
    }

    if (name == null)
    {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
          ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.FDFR_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    String fdfrType = null;

    JAXBElement<String> typeJAX = createData.getFdfrType();
    if(typeJAX!=null)
    {
      fdfrType = typeJAX.getValue();
      if(fdfrType==null)
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
            ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageMandatory(MtosiConstants.FDFR_TYPE_PARAM));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      if(!fdfrType.equals(MtosiConstants.FDFRT_POINT_TO_POINT))
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
            ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageIllegal(MtosiConstants.FDFR_TYPE_PARAM));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }

    }
    else
    {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
          ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageMandatory(MtosiConstants.FDFR_TYPE_PARAM));
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    tpDataListToModify = mtosiBody.getTpsToModify();

    facade = getMtosiCtrl().getMoFacadeManager().getFacadeViaNeId(MtosiMOFacade.class, ne.getID());
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    super.validate(validator);
  }

  @Override
  protected void mediate() throws Exception {
    String ptpName = aEnd.getName().get(0).getPtpNm();
    if (ptpName == null) {
      ptpName = zEnd.getName().get(0).getPtpNm();
    }
    if (ptpName == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, "ptp name is missing.");
    }

    factory = new EntityEFMFactory(facade,tpDataListToModify,getMtosiCtrl(),ne.getID());

    List<DTO<ProtectionGroupF3Attr>> pgs =  facade.queryDTO(ne.getID(),ProtectionGroupF3Attr.class);
    if (!pgs.isEmpty()) {
      mediate_LAG();
    } else {
      mediate_NoLAG();
    }

    transact();

    fdfrForResponse = facade.findDTOViaEntityIndex(ne.getID(), flow.getValue(ManagedObjectAttr.ENTITY_INDEX),FlowF3Attr.class);
  }

  private void transact()
      throws ObjectInUseException, SNMPCommFailure {
//    String mtosiflowname = mtosiTerminationPointEFMDTO.createMtosiNameToDisplay(mtosiAddress.getNE(),fdfrForResponse.getValue(ManagedObjectAttr.MTOSI_NAME));
    try {
      facade.openNetTransaction(ne.getID());
      if(net1 != null){
        logSecurity(ne, SystemAction.ModifyNetwork, net1.getValue(ManagedObjectAttr.MTOSI_NAME));
        facade.modifyObjectOnDevice(ne.getID(),net1);
      }
      if(net2 != null){
        logSecurity(ne, SystemAction.ModifyNetwork, net2.getValue(ManagedObjectAttr.MTOSI_NAME));
        facade.modifyObjectOnDevice(ne.getID(),net2);
      }
      if(acc != null){
        logSecurity(ne, SystemAction.ModifyNetwork, acc.getValue(ManagedObjectAttr.MTOSI_NAME));
        facade.modifyObjectOnDevice(ne.getID(),acc);
      }

      logSecurity(ne, SystemAction.ModifyNetwork, flow.getValue(ManagedObjectAttr.MTOSI_NAME));
      facade.modifyObjectOnDevice(ne.getID(),flow);

    } finally {
      facade.closeNetTransaction(ne.getID());
    }
  }

  private void mediate_NoLAG() throws Exception {
    NamingAttributesT aName = aEnd.getName().get(0);
    NamingAttributesT zName = zEnd.getName().get(0);

    if (NamingTranslationFactory.isFtp(aName)) {
      if (aName.getFtpNm() == null)
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NAME_MISSING);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      //checkFTPExistence
      ftpDTO = facade.findDTOViaMtosiName(ne.getID(), aName.getFtpNm(), ProtectionGroupF3Attr.class);
      if(ftpDTO == null)
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
    }
    if (NamingTranslationFactory.isFtp(zName)) {
      if (zName.getFtpNm() == null)
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NAME_MISSING);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      //checkFTPExistence
      ftpDTO = facade.findDTOViaMtosiName(ne.getID(), zName.getFtpNm(), ProtectionGroupF3Attr.class);
      if(ftpDTO == null)
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
    }
    if (!MtosiUtils.isSameShelf(aEnd.getName().get(0), zEnd.getName().get(0))) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "aEnd and zEnd are not on the same Shelf.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    if (!MtosiUtils.isSameSlot(aEnd.getName().get(0), zEnd.getName().get(0))) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "aEnd and zEnd are not on the same Slot.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if (NamingTranslationFactory.isFlow(aName) && NamingTranslationFactory.isPort(zName)) {
      isACtp = true;

      //get Port Net entity from DB
//      String name = NamingTranslationFactory.portNameFromShelfCombo(zName.getPtpNm());
//      if (name.equals(MtosiConstants.PORT_NET1)){
        portN = facade.findDTOViaMtosiName(ne.getID(), zName.getPtpNm(), PortF3NetAttr.class);
//      }else{
//        portN = facade.findDTOViaMtosiName(ne.getID(), zName.getPtpNm(), PortF3NetAttr.class);
//      }
      //get Port ACC entity from DB
      portA = facade.findDTOViaMtosiName(ne.getID(), aName.getPtpNm(), PortF3AccAttr.class);
      ctpName =  aName.getCtpNm();

      //get Flow entity from DB
      List<DTO<FlowF3Attr>> dbDTOs = facade.queryDTO(ne.getID(),FlowF3Attr.class);
      if(dbDTOs.isEmpty()){
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INTERNAL_ERROR, "EFM flow has not been discovered.");
      }
      ctpDTO = dbDTOs.get(0);

      flowName = aEnd.getName().get(0).getCtpNm();
      final String portName = aEnd.getName().get(0).getPtpNm();
      accPortName = NamingTranslationFactory.portNameFromShelfCombo(portName);
    } else if (NamingTranslationFactory.isFlow(zName) && NamingTranslationFactory.isPort(aName)) {
      //get Port Net entity from DB
//      String name = NamingTranslationFactory.portNameFromShelfCombo(aName.getPtpNm());
//      if (name.equals(MtosiConstants.PORT_NET1)){
        portN = facade.findDTOViaMtosiName(ne.getID(), aName.getPtpNm(), PortF3NetAttr.class);
//      }else{
//        portN = facade.findDTOViaMtosiName(ne.getID(), aName.getPtpNm(), PortF3NetAttr.class);
//      }
      // get Port ACC entity from DB
      portA = facade.findDTOViaMtosiName(ne.getID(), zName.getPtpNm(), PortF3AccAttr.class);
      ctpName =  zName.getCtpNm();
      // get Flow entity from DB
      List<DTO<FlowF3Attr>> dbDTOs = facade.queryDTO(ne.getID(),FlowF3Attr.class);
      if(dbDTOs.isEmpty()){
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INTERNAL_ERROR, "EFM flow has not been discovered.");
      }
      ctpDTO = dbDTOs.get(0);

      flowName = zEnd.getName().get(0).getCtpNm();
      final String portName = zEnd.getName().get(0).getPtpNm();
      accPortName = NamingTranslationFactory.portNameFromShelfCombo(portName);
    } else {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_TP_INVALID_ENDPOINT,
          "The specified endpoints must be a PTP and a CTP.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    //check if FDFr already exists
    if (ctpDTO.getValue(FlowF3Attr.CIRCUIT_NAME).equals(mtosiBody.getCreateData().getName().getFdfrNm()+"^"+ctpName)){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(),
          ExceptionUtils.EXCPT_INVALID_INPUT, "An FDFr already exists with the specified name.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    boolean newFlow = false;
    portACC = portA;
    if (portACC.getValue(PortF3AccAttr.SVC_TYPE).equals(CMPortModeTranslation.CO_EPL.getPortSvcTypeValue())) {
      if (!ctpDTO.getValue(FlowF3Attr.CIRCUIT_NAME).isEmpty()){
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_OBJECT_IN_USE,
            "An EPL Port cannot support more than one Flow.");
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }

    } else {
      // todo: EVPL !!!
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_NOT_IMPLEMENTED,
          "EVPL Mode is not supported.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

//    if (!(portN instanceof DTO<PortF3NetAttr>)) {
//      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
//          "Specified Port is not a NET Port.");
//      throw new ProcessingFailureException(pfet.getReason(), pfet);
//    }
    portNET = portN;
    if (portNET.getValue(ManagedObjectAttr.MTOSI_NAME).indexOf(MtosiConstants.PORT_NET2) > -1) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_TP_INVALID_ENDPOINT,
          "NET-2 Port cannot be the endpoint of an FDFr.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    if (tpsToModify != null) {
      int shelfIndex = getIndexFromMtosiname(portACC.getValue(ManagedObjectAttr.MTOSI_NAME), EntityClass.SHELF);
      int modifiedSlotIndex = getIndexFromMtosiname(portACC.getValue(ManagedObjectAttr.MTOSI_NAME), EntityClass.SLOT);
//      if (ManagedElementFactory.needsSlotIncrement(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
//        modifiedSlotIndex -= 1; // Need to decrement it for CM, since
//        // the GUI and the NE index's don't
//        // match.
//      }
      if (!MtosiTPMediator.checkTPToModifySameCard(ne, tpsToModify, shelfIndex, modifiedSlotIndex, null)) {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
            "The specified TPs must be on the same slot.");
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      tpDataACC = MtosiTPMediator.getTPDataTForCMACC(tpsToModify);
      tpDataNET = MtosiTPMediator.getTPDataTForCMNET(tpsToModify, 1);
      tpDataFlow = MtosiTPMediator.getTPDataTForCTP(tpsToModify);

    }
    if (tpDataFlow != null) {
      validateFlowName(flowName,tpDataFlow);

      mtosiAddress = new MtosiAddress(mtosiBody.getTpsToModify().getTpData().get(0).getTpName());
      String ctp= mtosiAddress.getNaming().getPtpNm();
      flow = factory.getFlowF3DTO(mtosiAddress.getNaming().getCtpNm(), ctp);
    }

    if (tpDataACC != null) {
      acc = factory.getPortAccF3DTO(tpDataACC);
    }
    if (tpDataNET != null) {
      net1 = factory.getPortNetF3DTO(tpDataNET);
    }

    if(flow == null) {
      flow =  DTOBuilder.getInstance().newDTO(FlowF3Attr.class);
      List<DTO<FlowF3Attr>> dbDTOs = facade.queryDTO(ne.getID(),FlowF3Attr.class);
      if(dbDTOs.isEmpty()){
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INTERNAL_ERROR, "EFM flow has not been discovered.");
      }
      DTO<FlowF3Attr> dbDTO = dbDTOs.get(0);
      flow.putOrReplace(FlowF3Attr.ENTITY_INDEX,dbDTO.getValue(FlowF3Attr.ENTITY_INDEX));
    } else {
      EntityIndex entityFlowIndex = flow.getValue(FlowF3Attr.ENTITY_INDEX);
      flow =  DTOBuilder.getInstance().newDTO(FlowF3Attr.class);
      flow.putOrReplace(FlowF3Attr.ENTITY_INDEX, entityFlowIndex);
    }

    flow.putOrReplace(FlowF3Attr.CIRCUIT_NAME, NamingTranslationFactory.getFlowNameWithFDFr(mtosiBody.getCreateData().getName().getFdfrNm(), ctpName));
  }

  private void mediate_LAG() throws Exception {
    NamingAttributesT aName = aEnd.getName().get(0);
    NamingAttributesT zName = zEnd.getName().get(0);
//    Port portA;
    String ftpName;
    if (NamingTranslationFactory.isFlow(aName) && NamingTranslationFactory.isFtp(zName)) {
      isACtp = true; // flow is the A side
      if (zName.getFtpNm() == null)
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NAME_MISSING);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      //checkFTPExistence
      ftpDTO = facade.findDTOViaMtosiName(ne.getID(), zName.getFtpNm(), ProtectionGroupF3Attr.class);
      if(ftpDTO == null)
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }

      //get Port ACC entity from DB
      portA = facade.findDTOViaMtosiName(ne.getID(), aName.getPtpNm(), PortF3AccAttr.class);
      ctpName =  aName.getCtpNm();
      List<DTO<FlowF3Attr>> dbDTOs = facade.queryDTO(ne.getID(),FlowF3Attr.class);
      if(dbDTOs.isEmpty()){
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INTERNAL_ERROR, "EFM flow has not been discovered.");
      }
      flow = dbDTOs.get(0);

      flowName = aEnd.getName().get(0).getCtpNm();
      ftpName = zEnd.getName().get(0).getFtpNm();
      final String portName = aEnd.getName().get(0).getPtpNm();
      accPortName = NamingTranslationFactory.portNameFromShelfCombo(portName);

    } else if (NamingTranslationFactory.isFlow(zName) && NamingTranslationFactory.isFtp(aName)) {
      if (aName.getFtpNm() == null)
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NAME_MISSING);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      //checkFTPExistence
      ftpDTO = facade.findDTOViaMtosiName(ne.getID(), aName.getFtpNm(), ProtectionGroupF3Attr.class);
      if(ftpDTO == null)
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      //get Port ACC entity from DB
      portA = facade.findDTOViaMtosiName(ne.getID(), zName.getPtpNm(), PortF3AccAttr.class);
      ctpName =  zName.getCtpNm();
      List<DTO<FlowF3Attr>> dbDTOs = facade.queryDTO(ne.getID(),FlowF3Attr.class);
      if(dbDTOs.isEmpty()){
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INTERNAL_ERROR, "EFM flow has not been discovered.");
      }
      flow = dbDTOs.get(0);
      flowName = zEnd.getName().get(0).getCtpNm();
      ftpName = aEnd.getName().get(0).getFtpNm();
      final String portName = zEnd.getName().get(0).getPtpNm();
      accPortName = NamingTranslationFactory.portNameFromShelfCombo(portName);
    } else {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_TP_INVALID_ENDPOINT,
          "The specified endpoints must be a FTP and a CTP.");
    }

    //check if FDFr already exists
    if (flow.getValue(FlowF3Attr.CIRCUIT_NAME).equals(mtosiBody.getCreateData().getName().getFdfrNm()+"^"+ctpName)){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(),
          ExceptionUtils.EXCPT_INVALID_INPUT, "An FDFr already exists with the specified name.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    portACC = portA;
    portNET = facade.findDTOViaEntityIndex(ne.getID(), ftpDTO.getValue(ProtectionGroupF3Attr.WORK_PORT_ENTITY_INDEX), PortF3NetAttr.class);

    if (getIndexFromMtosiname(portACC.getValue(ManagedObjectAttr.MTOSI_NAME), EntityClass.SHELF) != getIndexFromMtosiname(portNET.getValue(ManagedObjectAttr.MTOSI_NAME), EntityClass.SHELF)){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "aEnd and zEnd are not on the same Shelf.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if (getIndexFromMtosiname(portACC.getValue(ManagedObjectAttr.MTOSI_NAME), EntityClass.SLOT) != getIndexFromMtosiname(portNET.getValue(ManagedObjectAttr.MTOSI_NAME), EntityClass.SLOT)){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "aEnd and zEnd are not on the same Slot.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    Integer flowIndex = 1;
    if (portACC.getValue(PortF3AccAttr.SVC_TYPE).equals(CMPortModeTranslation.CO_EPL.getPortSvcTypeValue())) {
      if (!flow.getValue(FlowF3Attr.CIRCUIT_NAME).isEmpty()){
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_OBJECT_IN_USE,
            "An EPL Port cannot support more than one Flow.");
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }

    } else {
      // todo: EVPL !!!
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_NOT_IMPLEMENTED,
          "EVPL Mode is not supported.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    int shelfIndex = getIndexFromMtosiname(portACC.getValue(ManagedObjectAttr.MTOSI_NAME), EntityClass.SHELF);
    int slotIndex = getIndexFromMtosiname(portACC.getValue(ManagedObjectAttr.MTOSI_NAME), EntityClass.SLOT);

    if (tpsToModify != null) {
      int modifiedSlotIndex = slotIndex;
//      if (ManagedElementFactory.needsSlotIncrement(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
//        modifiedSlotIndex -= 1;
//      }
      if (!MtosiTPMediator.checkTPToModifySameCard(ne, tpsToModify, shelfIndex, modifiedSlotIndex, ftpName)) {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
            "The specified TPs must be on the same slot.");
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      tpDataACC = MtosiTPMediator.getTPDataTForCMACC(tpsToModify);
      tpDataNET = MtosiTPMediator.getTPDataTForCMNET(tpsToModify, 1);
      tpDataNET2 = MtosiTPMediator.getTPDataTForCMNET(tpsToModify, 2);
      tpDataFlow = MtosiTPMediator.getTPDataTForCTP(tpsToModify);
      tpDataFTP = MtosiTPMediator.getTPDataTForFTP(tpsToModify);
    }

    if (tpDataFlow != null) {
      validateFlowName(flowName,tpDataFlow);

      MtosiAddress mtosiAddress = new MtosiAddress(mtosiBody.getTpsToModify().getTpData().get(0).getTpName());
      String ctp= mtosiAddress.getNaming().getPtpNm();
      flow = factory.getFlowF3DTO(mtosiAddress.getNaming().getCtpNm(), ctp);
    }
    if (tpDataACC != null){
      acc = factory.getPortAccF3DTO(tpDataACC);
    }
    if (tpDataNET != null){
      net1 = factory.getPortNetF3DTO(tpDataNET);
    }
    if (tpDataNET2 != null){
      net2 = factory.getPortNetF3DTO(tpDataNET2);
    }
    if (tpDataFTP != null){
      ftp = factory.getFtpF3DTO(tpDataFTP);
    }

    EntityIndex entityFlowIndex = flow.getValue(FlowF3Attr.ENTITY_INDEX);
//    if (flow == null) {
      flow =  DTOBuilder.getInstance().newDTO(FlowF3Attr.class);
//      List<DTO<FlowF3Attr>> dbDTOs = facade.queryDTO(ne.getID(),FlowF3Attr.class);
//      if(dbDTOs.isEmpty()){
//        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INTERNAL_ERROR, "EFM flow has not been discovered.");
//      }
//      DTO<FlowF3Attr> dbDTO = dbDTOs.get(0);
    flow.putOrReplace(FlowF3Attr.ENTITY_INDEX,entityFlowIndex);
//    }

    flow.putOrReplace(FlowF3Attr.CIRCUIT_NAME, NamingTranslationFactory.getFlowNameWithFDFr(mtosiBody.getCreateData().getName().getFdfrNm(), ctpName));

  }

  private  int getIndexFromMtosiname(String mtosiName, int entity){
    String[] parts = mtosiName.split("/");
    int index = 1;
    if (entity == EntityClass.SHELF){
      index = Integer.parseInt(parts[1].replace("shelf=",""));
    }else if (entity == EntityClass.SLOT){
      index = Integer.parseInt(parts[2].replace("slot=",""));
    }
    return index;
  }

  @Override
  protected TPDataListT getUpdatedTPs() throws ProcessingFailureException {
    // Get updated objects
    ObjectFactory objectFactory = new ObjectFactory();
    TPDataListT tpsToModify = objectFactory.createTPDataListT();
    if (tpDataACC != null && portACC != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataACC.getTpName())).toMtosiPTPasTPDataT());
    }
    if (tpDataNET != null && portNET != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataNET.getTpName())).toMtosiPTPasTPDataT());
    }
    if (tpDataNET2 != null && portNET2 != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataNET2.getTpName())).toMtosiPTPasTPDataT());
    }
    if (tpDataFlow != null && flow != null) {
      MTOSIFlowF3 flowUpdated = ManagedElementFactory.getCMFlow(tpDataFlow.getTpName());
      if (flowUpdated != null) {
        tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  null).getMtosiTranslator(flowUpdated).toMtosiCTPasTPDataT());
      }
    }
    if (tpDataFTP != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  null).getMtosiTranslator(ManagedElementFactory.getFtp(tpDataFTP.getTpName())).toMtosiFTPasTPDataT());
    }
    return tpsToModify;
  }

  @Override
  protected void response() throws Exception {
    FspEFMMediator fdfrMediator = new FspEFMMediator(new MtosiAddress(namingAttributes), facade, fdfrForResponse);
    response.setTheFDFr(fdfrMediator.toMtosiFDFr(aEnd, zEnd));
    v1.tmf854ext.adva.ObjectFactory factory = new v1.tmf854ext.adva.ObjectFactory();
    JAXBElement<NamingAttributesListT> aEndReturn = factory.createFlowDomainFragmentTAEnd(aEnd);
    response.setAEnd(aEndReturn);
    JAXBElement<NamingAttributesListT> zEndReturn = factory.createFlowDomainFragmentTZEnd(zEnd);
    response.setZEnd(zEndReturn);
    // have to get updated Ports that were in the request tpsToModify
    JAXBElement<TPDataListT> modifiedTPs = factory.createCreateAndActivateFDFrResponseTTpsToModify(getUpdatedTPs());
    response.setTpsToModify(modifiedTPs);
  }

  @Override
  public CreateAndActivateFDFrResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }

}
