/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import v1.tmf854.HeaderT;
import v1.tmf854.NVSListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.OperateVLANLoopbackResponseT;
import v1.tmf854ext.adva.OperateVLANLoopbackT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.ws.Holder;

public class OperateVlanLoopbackWorker extends AbstractMtosiWorker
{
  protected OperateVLANLoopbackT mtosiBody;
  protected OperateVLANLoopbackResponseT response = new OperateVLANLoopbackResponseT();
  protected NamingAttributesT namingAttributes;
  protected NetworkElement ne;
  protected NVSListT loopbackParameters;
  protected String innerVidList;
  protected String outerVidList;

  public OperateVlanLoopbackWorker(OperateVLANLoopbackT mtosiBody, Holder<HeaderT> mtosiHeader)
  {
    super(mtosiHeader, "operateVLANLoopback", "operateVLANLoopback", "operateVLANLoopbackResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    if ((namingAttributes = mtosiBody.getTpName()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The TP name has not been specified.");
    }

    JAXBElement<NVSListT> params = mtosiBody.getLoopbackParameters();
    if (params != null) {
      this.loopbackParameters = params.getValue();
    }

    JAXBElement<String> innerVidListJax = mtosiBody.getInnerVidOptPrioList();
    if (innerVidListJax != null) {
      this.innerVidList = innerVidListJax.getValue();
    }

    JAXBElement<String> outerVidListJax = mtosiBody.getOuterVidOptPrioList();
    if (outerVidListJax != null) {
      this.outerVidList = outerVidListJax.getValue();
    }
    ne = ManagedElementFactory.getAndValidateNE(namingAttributes);

  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
   validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  @Override
  protected void mediate() throws Exception {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The specified ME does not support VLAN Loopback.");
  }

  @Override
  protected void response () throws Exception {
    // empty method
  }

  @Override
  public OperateVLANLoopbackResponseT getSuccessResponse()
  {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}