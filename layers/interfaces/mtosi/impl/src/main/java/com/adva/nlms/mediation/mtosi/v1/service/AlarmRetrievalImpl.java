/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;

import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.AlarmRetrieval;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.HeaderUtils;


/**
 * This class was generated by the Celtix 1.1-SNAPSHOT
 * Fri Dec 22 10:48:24 EST 2006
 * Generated source version: 1.1-SNAPSHOT
 *
 */

@jakarta.jws.WebService(name = "AlarmRetrieval", serviceName = "FaultService", portName = "AlarmRetrievalHttp", targetNamespace = "tmf854.v1.ws")

public class AlarmRetrievalImpl implements AlarmRetrieval {
  /* (non-Javadoc)
  * @see ws.v1.tmf854.AlarmRetrieval#getActiveAlarmsCount(v1.tmf854.GetActiveAlarmsCountT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetActiveAlarmsCountResponseT getActiveAlarmsCount(
          v1.tmf854.GetActiveAlarmsCountT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader ) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getActiveAlarmsCount", "getActiveAlarmsCountResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetActiveAlarmsCountResponseT getActiveAlarmsCount(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetActiveAlarmsCountT mtosiBody ) throws ProcessingFailureException {
    return getActiveAlarmsCount(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.AlarmRetrieval#getActiveAlarms(v1.tmf854.GetActiveAlarmsT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetActiveAlarmsResponseT getActiveAlarms(
          v1.tmf854.GetActiveAlarmsT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader ) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getActiveAlarms", "getActiveAlarmsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetActiveAlarmsResponseT getActiveAlarms(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader ,
          v1.tmf854.GetActiveAlarmsT mtosiBody) throws ProcessingFailureException {
    return getActiveAlarms(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.AlarmRetrieval#getActiveAlarmsIterator(v1.tmf854.GetActiveAlarmIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetActiveAlarmsResponseT getActiveAlarmsIterator(
          v1.tmf854.GetActiveAlarmIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader ) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getActiveAlarmsIterator", "getActiveAlarmsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetActiveAlarmsResponseT getActiveAlarmsIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetActiveAlarmIteratorT mtosiBody ) throws ProcessingFailureException {
    return  getActiveAlarmsIterator(mtosiBody, mtosiHeader);
  }
}
