/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.equipment;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.EquipmentFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import v1.tmf854.EquipmentOrHolderListT;
import v1.tmf854.GetAllEquipmentResponseT;
import v1.tmf854.GetAllEquipmentT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

/**
 * main class for the MTOSI operation:  g e t A l l E q u i p m e n t
 */
public class GetAllEquipmentWorker extends AbstractMtosiWorker {
  protected GetAllEquipmentT mtosiBody;
  protected GetAllEquipmentResponseT response = new GetAllEquipmentResponseT();
  protected NamingAttributesT naming;
  protected MtosiAddress mtosiAddr;
  protected EquipmentOrHolderListT list;

  public GetAllEquipmentWorker(GetAllEquipmentT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getAllEquipment", "getAllEquipment", "getAllEquipmentResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    naming = mtosiBody.getMeOrHolderName();
    mtosiAddr = new MtosiAddress(naming);

    if (naming == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              MtosiErrorConstants.INVALID_FILTER);
    }

    if(naming.getMdNm()==null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if(naming.getMeNm()==null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              MtosiErrorConstants.ME_NAME_MISSING);
    }

    if (!(mtosiAddr.isEquipmentHolder() || mtosiAddr.isManagedElement())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              "A Managed Element or Equipment Holder must be specified.");
    }

    if(!MtosiUtils.existsEntity(mtosiAddr)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The specified entity does not exist.");
    }
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
  	validator.validate(mtosiAddr.getNE().getDefaultNetworkElementTypeString());
  }

  @Override
  protected void mediate() throws Exception {
    list = EquipmentFactory.getAllEquipment(mtosiAddr);
  }

  @Override
  protected void response() throws Exception {
    response.setEqList(list);
  }

  @Override
  public GetAllEquipmentResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
