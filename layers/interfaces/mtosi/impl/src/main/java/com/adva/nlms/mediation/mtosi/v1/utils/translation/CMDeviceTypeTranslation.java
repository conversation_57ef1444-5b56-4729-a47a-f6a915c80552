/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum CMDeviceTypeTranslation implements TranslatableEnum {
  NOT_APPLICABLE  (0, "n/a"),
  //GE201           (1, "GE 201"), //This is just a guess at the moment.
  FSP_150CPMR        (5, "FSP 150CPMR");

  private final int    mibValue;
  private final String mtosiString;

  private CMDeviceTypeTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}
