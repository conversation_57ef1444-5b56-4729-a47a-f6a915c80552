/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum FullHalfTranslation {
  FULL   (1, "Full"),
  HALF  (2, "Half"),
  EMPTY     (3, "");

  private final int id;
  private final String mtosiString;

  private FullHalfTranslation (final int id, final String mtosiString)
  {
    this.id   = id;
    this.mtosiString = mtosiString;
  }

  @SuppressWarnings("unchecked")
  public static FullHalfTranslation getEnumByString (final String mtosiString) {
    for (FullHalfTranslation enumValue : values()) {
      if (mtosiString.equals(enumValue.getMtosiString())) {
        return enumValue;
      }
    }
    return null;
  }

  public String getMtosiString() {
    return mtosiString;
  }
}
