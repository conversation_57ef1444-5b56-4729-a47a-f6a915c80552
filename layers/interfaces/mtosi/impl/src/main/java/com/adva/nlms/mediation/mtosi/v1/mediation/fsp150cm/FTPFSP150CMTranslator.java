/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm;

import com.adva.nlms.common.NEUtils;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.mediation.common.serviceProvisioning.ProtectionGroupF3SPProperties;
import com.adva.nlms.mediation.config.Module;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3Impl;
import com.adva.nlms.mediation.config.f3.mtosi.NetworkElementFSP150CMMTOSIOperations;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FTPFSP150CM;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.mediation.FTPTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMProtectionStatusTranslation;
import v1.tmf854.LayeredParametersListT;

/**
 * This class is an FSP 150 CM FTP MTOSI Translator.
 */
public class FTPFSP150CMTranslator extends FTPTranslator {

  
  private static final int NteModule = 1;
  private static final int NtuModule = 0;
  private FTPFSP150CM ftp;

	public FTPFSP150CMTranslator(FTPFSP150CM ftp) {
		super(ftp);
		this.ftp = ftp;
	}

	@Override
  protected void fillProtection150cmLayer(LayeredParametersListT layeredParametersListT) {
		ProtectionGroupF3SPProperties ftpProps = ftp.getFTPSPProperties();
		int neType = ((NetworkElement)ftp.getNetworkElement()).getMTOSIWorker().getNetworkElementTypeForMTOSI();
		final String layerName = NEUtils.isLocalF3Device(neType) ? LayeredParams.PROP_ADVA_PROTECTION_FSP150_CMNTU
				: LayeredParams.PROP_ADVA_PROTECTION_FSP150_1PLUS1;
		// -------start Layer--------
		LayeredParameterUtils.addLayer(layeredParametersListT, layerName);
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, layerName, LayeredParams.LrPropAdvaProtectionFSP150CMNTU.PROTECTION_SWITCH_MODE,
				"OnePlusOne");

		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, layerName, LayeredParams.LrPropAdvaProtectionFSP150CMNTU.REVERTIVE, MtosiUtils
				.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, MIB.RFC1253.TRUTH_VALUE_FALSE));

		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, layerName, LayeredParams.LrPropAdvaProtectionFSP150CMNTU.SWITCH_DIRECTION,
				"Unidirectional");

		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, layerName, LayeredParams.LrPropAdvaProtectionFSP150CMNTU.PROTECTION_STATUS,
				MtosiUtils.getMtosiString(CMProtectionStatusTranslation.NOT_APPLICABLE, ftpProps.get(
                ProtectionGroupF3SPProperties.VI.ProtectionGroupStatus)));
		// -------end of Layer-------
	}

	@Override
	protected int getModuleType() {
	  ProtectionGroupF3SPProperties props = ftp.getFTPSPProperties();
    int shelfIndex = props.get(ProtectionGroupF3SPProperties.VI.ShelfIndex);
    int slotIndex = props.get(ProtectionGroupF3SPProperties.VI.SlotIndex);
		NetworkElement ne = ftp.getAPort().getNE();
	  Module module = ((NetworkElementFSP150CMMTOSIOperations)ne.getMTOSIWorker()).getModule(shelfIndex,slotIndex);
	  if (module instanceof NteF3Impl)
	      return NteModule;
	  return NtuModule;

	}

	@Override
  protected void fillAdvaEthernetLayer(LayeredParametersListT layeredParametersListT, int moduleType) {
		int neType = ((NetworkElement)ftp.getNetworkElement()).getMTOSIWorker().getNetworkElementTypeForMTOSI();
		boolean isNTEPort = moduleType == NteModule;
		if (isNTEPort || neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE) {
			LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet);
		}
	}
}
