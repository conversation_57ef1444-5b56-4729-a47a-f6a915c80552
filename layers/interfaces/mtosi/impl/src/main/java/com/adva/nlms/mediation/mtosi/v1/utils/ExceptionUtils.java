/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.utils;

import com.adva.nlms.mediation.common.MDRequestFailedException;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPCondition;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.ne_comm.BadValueException;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPError;
import com.adva.nlms.mediation.topology.MDNEDeletionNotAllowedException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.ActivityStatusEnumT;
import v1.tmf854.ActivityStatusT;
import v1.tmf854.HeaderT;
import v1.tmf854.MsgTypeT;
import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public class ExceptionUtils
{
	public static final String EXCPT_NOT_IMPLEMENTED = "EXCPT_NOT_IMPLEMENTED";
	public static final String EXCPT_INTERNAL_ERROR = "EXCPT_INTERNAL_ERROR";
	public static final String EXCPT_INVALID_INPUT = "EXCPT_INVALID_INPUT";
	public static final String EXCPT_OBJECT_IN_USE = "EXCPT_OBJECT_IN_USE";
	public static final String EXCPT_TP_INVALID_ENDPOINT = "EXCPT_TP_INVALID_ENDPOINT";
	public static final String EXCPT_ENTITY_NOT_FOUND = "EXCPT_ENTITY_NOT_FOUND";
	public static final String EXCPT_UNABLE_TO_COMPLY = "EXCPT_UNABLE_TO_COMPLY";
	public static final String EXCPT_CAPACITY_EXCEEDED = "EXCPT_CAPACITY_EXCEEDED";
	public static final String EXCPT_COMM_FAILURE = "EXCPT_COMM_FAILURE";
	public static final String EXCPT_INVALID_FILTER_DEFINITION = "EXCPT_INVALID_FILTER_DEFINITION";
  public static final String EXCPT_BAD_VALUE = "BAD_VALUE";
  public static final String EXCPT_INVALID_TOPIC = "EXCPT_INVALID_TOPIC";

  private static final Logger LOG = LogManager.getLogger(ExceptionUtils.class);
	
	/*
	 * This method takes a Java exception and formats it as an MTOSI PFE for
	 * sending.
	 */
	public static ProcessingFailureExceptionT formatException(Holder<HeaderT> mtosiHeader, Exception ex)
	{
		ProcessingFailureExceptionT info = new ProcessingFailureExceptionT();
		/* Clone the header - however far we got. */
		HeaderT pfeHeader = HeaderUtils.cloneHeader(mtosiHeader.value);
		/* Per the standard - the message name is ProcessingFailureException. */
		pfeHeader.setMsgName("ProcessingFailureException");
		/* Per the standard - the message type is ERROR. */
		pfeHeader.setMsgType(MsgTypeT.ERROR);
		/* Per the standard - the activityStatus is FAILURE. */
		ActivityStatusT activityStatus = new ActivityStatusT();
		activityStatus.setValue(ActivityStatusEnumT.FAILURE);
		pfeHeader.setActivityStatus(activityStatus);
		info.setTmf854Version(MtosiConstants.VERSION);
		info.setHeader(pfeHeader);
		/* Set defaults - sometimes these are overridden. */
		info.setException(EXCPT_INTERNAL_ERROR);
		info.setReason("An internal error has occurred.");
		/* Override for specific exception types. */
		if (ex instanceof ObjectInUseException)
		{
			info.setException(EXCPT_OBJECT_IN_USE);
			info.setReason(ex.getMessage());
		}
    else if (ex instanceof MDNEDeletionNotAllowedException)
    {
      info.setException(EXCPT_OBJECT_IN_USE);
      info.setReason(ex.getMessage());
    }
		else if (ex instanceof MDRequestFailedException)
		{
			info.setException(EXCPT_UNABLE_TO_COMPLY);
			String errMessage = ((MDRequestFailedException)ex).getMessage();
			if (errMessage.isEmpty() || errMessage.length() == 0)
				info.setReason(ex.getMessage());
			else
				info.setReason(errMessage);
		}
		else if (ex instanceof NetTransactionException)
		{
			info.setException(EXCPT_INTERNAL_ERROR);
			String message = ex.getMessage();
			if(message==null || message.length()==0)
			{
				info.setReason("An internal error occurred with the network transaction.");
			}
			else
			{
				info.setReason(message);
			}
			
		}
		else if (ex instanceof NumberFormatException)
		{
			info.setException(EXCPT_INVALID_INPUT);
			info.setReason("An invalid input was received - bad number format.");
		}
		else if (ex instanceof SPValidationException)
		{
			info.setException(EXCPT_INVALID_INPUT);
			SPValidationException spe = (SPValidationException) ex;
			// could handle each of the conditions differently, for example
			// missing mandatory parameters could be internal error
			// and not display the reason to the caller
			SPCondition condition = spe.getSpCondition();
			switch (condition)
			{
				case MANDATORY_PARAMETER_MISSING:
					// parameter missing is an internal error
					info.setReason(spe.getMessage());
					break;
				default:
					info.setReason(spe.getMessage());
			}
		}
		else if (ex instanceof SNMPCommFailure)
		{
			info.setException(EXCPT_COMM_FAILURE);
			SNMPCommFailure scf = (SNMPCommFailure) ex;
			String text = "The requested operation was rejected by the Managed Element \n(";
			String detail = SNMPError.describeSNMPErrorStatus(scf);
      text = text + detail + " - " + scf.getErrMessage() + ").";
			//text = text + detail + ").";
			info.setReason(text);
			
		}
    else if (ex instanceof BadValueException) {
      //prepare additional info about BadValue exception
      info.setException(EXCPT_BAD_VALUE);
      info.setReason(ex.getMessage());
    }
    return info;
	}

  public static ProcessingFailureExceptionT formatProcessingFailureException(String exception, String reason)
	{
		ProcessingFailureExceptionT info = new ProcessingFailureExceptionT();
		info.setTmf854Version(MtosiConstants.VERSION);
		info.setException(exception);
		info.setReason(reason);
		return info;
	}

	public static ProcessingFailureExceptionT formatProcessingFailureException(Holder<HeaderT> mtosiHeader,
			String exception, String reason)
	{
		ProcessingFailureExceptionT info = new ProcessingFailureExceptionT();
		/* Clone the header - however far we got. */
		HeaderT pfeHeader = HeaderUtils.cloneHeader(mtosiHeader.value);
		/* Per the standard - the message name is ProcessingFailureException. */
		pfeHeader.setMsgName("ProcessingFailureException");
		/* Per the standard - the message type is ERROR. */
		pfeHeader.setMsgType(MsgTypeT.ERROR);
		/* Per the standard - the activityStatus is FAILURE. */
		ActivityStatusT activityStatus = new ActivityStatusT();
		activityStatus.setValue(ActivityStatusEnumT.FAILURE);
		pfeHeader.setActivityStatus(activityStatus);
		info.setTmf854Version(MtosiConstants.VERSION);
		info.setHeader(pfeHeader);
		info.setException(exception);
		info.setReason(reason);
		return info;
	}

	/*
	 * This must be called at the outermost layer before a PFE is raised back
	 * into the middleware to populate the MTOSI header in the exception if it
	 * was not done in lower layers. In the lower layers, it is possible to need
	 * to throw a PFE but not be aware of the MTOSI header at the time.
	 */
	public static void updateProcessingFailureException(Holder<HeaderT> mtosiHeader, ProcessingFailureException pfe)
	{
		ProcessingFailureExceptionT info = pfe.getFaultInfo();
		if (info.getHeader() == null)
		{
			/* Clone the header - however far we got. */
			HeaderT pfeHeader = HeaderUtils.cloneHeader(mtosiHeader.value);
			/*
			 * Per the standard - the message name is
			 * ProcessingFailureException.
			 */
			pfeHeader.setMsgName("ProcessingFailureException");
			/* Per the standard - the message type is ERROR. */
			pfeHeader.setMsgType(MsgTypeT.ERROR);
			/* Per the standard - the activityStatus is FAILURE. */
			ActivityStatusT activityStatus = new ActivityStatusT();
			activityStatus.setValue(ActivityStatusEnumT.FAILURE);
			pfeHeader.setActivityStatus(activityStatus);
			info.setTmf854Version(MtosiConstants.VERSION);
			info.setHeader(pfeHeader);
		}
	}

	/*
	 * In each unimplemented method, the following boilerplate code should be
	 * used:
	 * 
	 * HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSNNamesWrtOS",
	 * "getAllSNNamesWrtOSResponse"); ** Format the response header. **
	 * ProcessingFailureExceptionT mtosiPFE =
	 * ExceptionUtils.formatUnimplementedOperationException(mtosiHeader); **
	 * Format the PFE based on the response header. ** mtosiHeader.value = null; **
	 * Nuke the actual header - should nuke the SOAP header, but doesn't due to
	 * a Celtix bug. ** throw new
	 * ProcessingFailureException(mtosiPFE.getReason(), mtosiPFE); ** Throw the
	 * PFE. **
	 */
	public static ProcessingFailureExceptionT formatUnimplementedOperationException(Holder<HeaderT> mtosiHeader)
	{
		ProcessingFailureExceptionT info = new ProcessingFailureExceptionT();
		/* Clone the header - however far we got. */
		HeaderT pfeHeader = HeaderUtils.cloneHeader(mtosiHeader.value);
		/* Per the standard - the message name is ProcessingFailureException. */
		pfeHeader.setMsgName("ProcessingFailureException");
		/* Per the standard - the message type is ERROR. */
		pfeHeader.setMsgType(MsgTypeT.ERROR);
		/* Per the standard - the activityStatus is FAILURE. */
		ActivityStatusT activityStatus = new ActivityStatusT();
		activityStatus.setValue(ActivityStatusEnumT.FAILURE);
		pfeHeader.setActivityStatus(activityStatus);
		info.setTmf854Version(MtosiConstants.VERSION);
		info.setHeader(pfeHeader);
		info.setException(EXCPT_NOT_IMPLEMENTED);
		info.setReason("The requested operation is not implemented.");
		return info;
	}

	/*
	 * This wrapper will encapsulate setting the MTOSI header to null when a PFE
	 * is raised so as to clear out the SOAP header. Right now, there is a bug
	 * in Celtix which causes an old (?cached?) version of the header to be used
	 */
	public static void nukeMtosiHeader(Holder<HeaderT> mtosiHeader, ProcessingFailureException pfe)
	{
		boolean celtixBug = true;
		/*
		 * If we cannot nuke the MTOSI header, then we should clone the
		 * exception header back into the MTOSI header so that they are
		 * consistent.
		 */
		if (celtixBug)
		{
			HeaderUtils.copyHeader(mtosiHeader.value, pfe.getFaultInfo().getHeader());
		}
		else
		{
			mtosiHeader.value = null;
		}
	}

	/*
	 * This method handles exceptions which can appear in method doWork of
	 * workers. (formats it and returns as an Object of required type)
	 */
	public static ProcessingFailureException formatDoWorkException(Exception ex,
			jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
	{
		// If we have an existing PFE, then just rethrow it, after updating the
		// header if needed.
		if (ex instanceof ProcessingFailureException)
		{
			ProcessingFailureException pfe = (ProcessingFailureException) ex;
			ExceptionUtils.updateProcessingFailureException(mtosiHeader, pfe);
			ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
			return pfe;
		} else if(ex instanceof NullPointerException){
            ProcessingFailureExceptionT pfet = ExceptionUtils.formatException(mtosiHeader, ex);
            ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
            ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
            LOG.error("Error executing request: " + pfe.getMessage() + "\n" + org.apache.commons.lang3.exception.ExceptionUtils.getStackTrace(ex));
            return pfe;
        }
		else
		{
			// Otherwise, map the exception into a PFE and throw it.
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatException(mtosiHeader, ex);
			ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
			ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
			LOG.debug("Error executing request: " + pfe.getMessage() + "\n" + org.apache.commons.lang3.exception.ExceptionUtils.getStackTrace(ex));
			return pfe;
		}
	}

	/**
	 * Helper method - creates an MtosiProcessingFailureException
	 * @param exception the excetpion type
	 * @see ExceptionUtils
	 * @param reason exception reason string
	 * @see com.adva.nlms.mediation.mtosi.v2.utils.MtosiErrorConstants etc
	 * @return the MtosiProcessingFailure exception created
	 */
	public static MtosiProcessingFailureException createNewMPFE(String exception, String reason) {

		MtosiProcessingFailureException ex = new MtosiProcessingFailureException(reason);
		ex.setException(exception);
		return ex;
	}


}
