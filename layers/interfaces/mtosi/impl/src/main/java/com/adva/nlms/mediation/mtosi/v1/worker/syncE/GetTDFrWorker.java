/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.syncE;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiF3F3SyncNameException;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiF3SyncName;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.common.NEUtils;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.GetTDFrResponseT;
import v1.tmf854ext.adva.GetTDFrT;
import v1.tmf854ext.adva.TimingDomainFragmentT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

/**
 * Worker class for the MTOSI operation getTDFr
 */
public class GetTDFrWorker extends AbstractMtosiWorker {
  protected GetTDFrT mtosiBody;
  protected GetTDFrResponseT response = new GetTDFrResponseT();
  protected NamingAttributesT tdfrName;
  protected MtosiAddress mtosiAddr;
  protected TimingDomainFragmentT tdfr;

  //Constructor
  public GetTDFrWorker(final GetTDFrT mtosiBody, final Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getTDFr", "getTDFr", "getTDFrResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    tdfrName = mtosiBody.getTdfrName();
    mtosiAddr = new MtosiAddress(tdfrName);

    if (tdfrName == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
        MtosiErrorConstants.INVALID_FILTER);
    }

    if (tdfrName.getMdNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
        MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!tdfrName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        MtosiErrorConstants.MD_NOT_FOUND);
    }

    if(tdfrName.getTdfrNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        "TDFr name is missing.");
    }

    MtosiUtils.validateNE(mtosiAddr.getNE());

  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(mtosiAddr.getNE().getDefaultNetworkElementTypeString());
  }

  @Override
  protected void mediate() throws Exception {
    // only allowed for FSP150CC-GE201SE && FSP150CM
    if (!NEUtils.isSyncEDevice(mtosiAddr.getNeType())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The specified ME does not support SyncE.");
    }
    MtosiF3SyncName mtosiName = null;
    
    try {
      mtosiName = new MtosiF3SyncName(tdfrName.getTdfrNm(), true, mtosiAddr.getNeType());
    } catch (MtosiF3F3SyncNameException e) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
      e.getMessage(),e);
      
    }

    tdfr = ManagedElementFactory.getTDFr(mtosiAddr.getNE(), mtosiName.getShelfIndex(),mtosiName.getSlotIndex(), mtosiName.getAlias());
    if(tdfr == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.TDFR_NOT_FOUND);
    }
  }

  @Override
  protected void response() throws Exception {
    response.setTdfr(tdfr);
  }

  @Override
  public GetTDFrResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
