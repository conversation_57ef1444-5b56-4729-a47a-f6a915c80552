/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: benjamint
 */

package com.adva.nlms.mediation.mtosi.v1.factory;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.F3PolicerSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.F3SyncSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.ShaperSPProperties;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.f3.NetworkElementF3;
import com.adva.nlms.mediation.config.f3.entity.SlotF3SPProperties;
import com.adva.nlms.mediation.config.f3.entity.flow.FlowF3Impl;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.f3.entity.policer.qospolicer.QOSFlowPolicerImpl;
import com.adva.nlms.mediation.config.f3.entity.port.acc.MTOSIPortF3Acc;
import com.adva.nlms.mediation.config.f3.entity.port.net.MTOSIPortF3Net;
import com.adva.nlms.mediation.config.f3.entity.shaper.qosshaper.QOSShaperF3;
import com.adva.nlms.mediation.config.f3.entity.sync.F3SyncImpl;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.model.FDFrACCEndFSP150CMIDs;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.model.FDFrNETEndFSP150CMIDs;
import com.adva.nlms.mediation.config.fsp150cp_mx.NetworkElementFSP150CP_MX;
import com.adva.nlms.mediation.config.fsp150cp_mx.PortFSP150CP_MX;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.FDFrPortEndFSP150CPIDs;
import com.adva.nlms.mediation.config.hn4000.FlowHN4000;
import com.adva.nlms.mediation.config.hn4000.LAGHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet2BASE_TL;
import com.adva.nlms.mediation.config.hn4000.ShaperHN4000;
import com.adva.nlms.mediation.config.mtosi.FDFr;
import com.adva.nlms.mediation.config.mtosi.FDFrFTPEndIDs;
import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.mediation.NetworkElementMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.NetworkElementMediatorFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.PtpNmTranslation;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectAcronymT;
import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.ProcessingFailureException;

/**
 * collection of static utility functions to check and convert MTOSI addresses 
 */
public class NamingTranslationFactory
{

	public static String getFlowNameWithFDFr(String fdfrName, String flowName)
	{
		// handle case of flowName already including fdfrName
		int pos = flowName.indexOf(MtosiConstants.FDFR_SEP);
		if (pos == -1)
		{
			return fdfrName + MtosiConstants.FDFR_SEP + flowName;
		}
		else
		{
			// already had fdfr and flow, so leave flow part
			String flowPart = flowName.substring(pos + 1);
			return fdfrName + MtosiConstants.FDFR_SEP + flowPart;
		}
	}

	public static String getRenamedFlowCircuitName(String startName, String newCircuitName)
	{
		int pos = startName.indexOf(MtosiConstants.FDFR_SEP);
		if (pos == -1)
			return newCircuitName;
		String fdfrPart = startName.substring(0, pos);
		return getFlowNameWithFDFr(fdfrPart, newCircuitName);
	}

	public static NamingAttributesT cloneNamingAttributes(NamingAttributesT naming)
	{
		NamingAttributesT newNaming = new NamingAttributesT();
		newNaming.setAidNm(naming.getAidNm());
		newNaming.setCtpNm(naming.getCtpNm());
		newNaming.setEhNm(naming.getEhNm());
		newNaming.setEpgpNm(naming.getEpgpNm());
		newNaming.setEqNm(naming.getEqNm());
		newNaming.setExtAuthor(naming.getExtAuthor());
		newNaming.setExtVersion(naming.getExtVersion());
		newNaming.setFtpNm(naming.getFtpNm());
		newNaming.setMdNm(naming.getMdNm());
		newNaming.setMeNm(naming.getMeNm());
		newNaming.setMlsnNm(naming.getMlsnNm());
		newNaming.setOsNm(naming.getOsNm());
		newNaming.setPgpNm(naming.getPgpNm());
		newNaming.setPropNm(naming.getPropNm());
		newNaming.setPtpNm(naming.getPtpNm());
		newNaming.setSncNm(naming.getSncNm());
		newNaming.setTlNm(naming.getTlNm());
		newNaming.setTmdNm(naming.getTmdNm());
		newNaming.setTmf854Version(naming.getTmf854Version());
		newNaming.setTppoolNm(naming.getTppoolNm());
		newNaming.setTcpNm(naming.getTcpNm());
		return newNaming;
	}


	public static Integer indexFromName(String name)
  {
    int i = name.indexOf('-');
    if (i == -1)
      return null;
    String indexString = name.substring(i + 1);
    try {
      return Integer.valueOf(indexString);
    }
    catch (NumberFormatException e) {
      return null;
    }
  }


	public static boolean isManagementDomain(NamingAttributesT name)
	{
		if (name != null)
		{
			String mdName = name.getMdNm();
			if (mdName != null)
			{
				return true;
			}
		}
		return false;
	}

	public static boolean isManagedElement(NamingAttributesT name)
	{
		if (name != null)
		{
			String mdName = name.getMdNm();
			String meName = name.getMeNm();
			if (name.getEhNm() != null)
				return false;
			if (name.getEqNm() != null)
				return false;
			if (mdName != null && meName != null)
			{
				return true;
			}
		}
		return false;
	}

  public static boolean isTCP(NamingAttributesT name)
	{
		if (name != null)
		{
			final String mdNm = name.getMdNm();
			final String meNm = name.getMeNm();
			final String ptpNm = name.getPtpNm();
			final String tcpNm = name.getTcpNm();
			if (mdNm != null && meNm != null && ptpNm != null && tcpNm != null)
			{
				return true;
			}
		}
		return false;
	}

	public static boolean isPort(NamingAttributesT name)
	{
		if (name != null)
		{
			final String portName = name.getPtpNm();
			final String flowName = name.getCtpNm();
			final String ftpName = name.getFtpNm();
			if (portName != null && flowName == null && ftpName == null)
			{
				return true;
			}
		}
		return false;
	}

	public static boolean isFlow(NamingAttributesT name)
	{
		if (name != null)
		{
			final String portName = name.getPtpNm();
			final String flowName = name.getCtpNm();
			final String ftpName = name.getFtpNm();
			final String tcpName = name.getTcpNm();
			if (portName != null && flowName != null && ftpName == null && tcpName==null)
			{
				return true;
			}
		}
		return false;
	}
	
	public static boolean isFlowHN(NamingAttributesT name)
	{
		if (name != null)
		{
			final String portName = name.getPtpNm();
			final String flowName = name.getCtpNm();
			final String ftpName = name.getFtpNm();
			final String tcpName = name.getTcpNm();
			if (portName != null && flowName != null && ftpName == null && tcpName==null)
			{
				return true;
			}
			if(portName==null && ftpName !=null && flowName !=null && tcpName==null)
			{
				return true;
			}
		}
		return false;
	}

	public static boolean isFlowOnFtp(NamingAttributesT name)
	{
		if (name != null)
		{
			final String portName = name.getPtpNm();
			final String flowName = name.getCtpNm();
			final String ftpName = name.getFtpNm();
			if (portName == null && flowName != null && ftpName != null)
			{
				return true;
			}
		}
		return false;
	}

	public static boolean isFtp(final NamingAttributesT name)
	{
		if (name != null)
		{
			final String portName = name.getPtpNm();
			final String ctpName = name.getCtpNm();
			final String ftpName = name.getFtpNm();
			if (ftpName != null && portName == null && ctpName == null)
				return true;
		}
		return false;
	}

	public static boolean isNamingAttributesDefinedForTP(final NamingAttributesT name)
	{
		if (name != null)
		{
			final String portName = name.getPtpNm();
			final String ctpName = name.getCtpNm();
			final String ftpName = name.getFtpNm();
			if (ftpName != null || portName != null || ctpName != null)
				return true;
		}
		return false;
	}
	
	public static boolean isCtpBonding(final NamingAttributesT name)
	{
		if (name != null)
		{
			final String portName = name.getPtpNm();
			final String ctpName = name.getCtpNm();
			final String ftpName = name.getFtpNm();
			if (ftpName != null && portName == null && ctpName != null)
			{
				if(ctpName.contains(MtosiConstants.LAG_FRAGMENT_TEXT))
				{
					return true;
				}
			}
				
		}
		return false;
	}

	public static boolean isCtpLag(final NamingAttributesT name)
	{
		if (name != null)
		{
			final String portName = name.getPtpNm();
			final String ctpName = name.getCtpNm();
			final String ftpName = name.getFtpNm();
			if (ftpName != null && portName == null && ctpName != null)
				return true;
		}
		return false;
	}


  /**
   * Creates a NamingAttributsT object
   * @param ne    Network Element
   * @return NamingAttributes object with MD and ME
   */
  public static NamingAttributesT createNamingAttributes(NetworkElement ne) {
    NamingAttributesT namingAttributes = new NamingAttributesT();

    namingAttributes.setMdNm(OSFactory.getMDNm());
    namingAttributes.setMeNm(ne.getName());

    return namingAttributes;
  }

  /**
   * Creates a NamingAttributsT object
   * @param me    String Element
   * @param cfmmd    String Element
   * @param ma    String Element
   * @return NamingAttributes object with MD and ME
   */
  public static NamingAttributesT createNamingAttributesMaintenanceAssociation(String me, String cfmmd, String ma) {
    NamingAttributesT namingAttributes = new NamingAttributesT();

    namingAttributes.setMdNm(OSFactory.getMDNm());
    namingAttributes.setMeNm(me);
    namingAttributes.setCfmMdNm(cfmmd);
    namingAttributes.setMaNm(ma);

    return namingAttributes;
  }

  /**
   * Creates a NamingAttributsT object
   * @param me    String Element
   * @param cfmmd    String Element
   * @param ma    String Element
   * @return NamingAttributes object with MD and ME
   */
  public static NamingAttributesT createNamingAttributesMaintenanceEndPoint(String me, String cfmmd, String ma, String mep) {
    NamingAttributesT namingAttributes = new NamingAttributesT();

    namingAttributes.setMdNm(OSFactory.getMDNm());
    namingAttributes.setMeNm(me);
    namingAttributes.setCfmMdNm(cfmmd);
    namingAttributes.setMaNm(ma);
    namingAttributes.setMepNm(mep);

    return namingAttributes;
  }

  /**
   * Creates a NamingAttributsT object (EH) based on EquipmentSPProperties
   * copy from NamingTranslationFactory.getNamingAttributesFixedSlotHolder
   * @param ne    Network Element
   * @param type  Type (EH, PTP, ...) of given name
   * @param name  Name of a Naming-attribute
   * @return NamingAttributes object with MD, ME and name of given type
   */
  private static NamingAttributesT createNamingAttributes(NetworkElement ne, ObjectAcronymT type, String name) {
    NamingAttributesT namingAttributes = new NamingAttributesT();

    namingAttributes.setMdNm(OSFactory.getMDNm());
    namingAttributes.setMeNm(ne.getName());

    if (type == ObjectAcronymT.EH) {
      namingAttributes.setEhNm(name);
    }
    else if (type == ObjectAcronymT.PTP) {
      namingAttributes.setPtpNm(name);
    }

    return namingAttributes;
  }

	public static NamingAttributesT createNamingAttributesHolder(NetworkElement ne, EquipmentSPProperties properties)
	{
		NetworkElementMediator neMediator = NetworkElementMediatorFactory.createNetworkElementMediator(ne);
		NamingAttributesT namingAttributes = neMediator.createNamingAttributesMe();
		String relativeName = neMediator.getRelativeNameForProperties(properties);
		namingAttributes.setEhNm(relativeName);
		return namingAttributes;
	}

  /**
   * Creates a NamingAttributsT object for fixed-slots ("/shelf=x/slot=y") based on EquipmentSPProperties
   * Applicable for all NE-Types except: 150CM, 150CC-GE20x
   * @param ne
   * @param properties
   */
  public static NamingAttributesT createNamingAttributesFixedSlotHolder(NetworkElement ne, EquipmentSPProperties properties) {
    return createNamingAttributes(ne, ObjectAcronymT.EH, getRelativeSlotNameForProperties(ne, properties));
  }

  /**
   * Creates a NamingAttributsT object for fixed-slots ("/shelf=x/slot=y") based on EquipmentSPPropertiesFSP150CM
   * Applicable for the NE-Types: 150CM, 150CC-GE20x
   * @param ne
   * @param properties
   */
  public static NamingAttributesT createNamingAttributesFixedSlotHolderCM(NetworkElement ne, EquipmentSPPropertiesFSP150CM properties) {
    int  neType = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI();
    Integer shelfIndex = properties.get(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex);
    Integer slotIndex = properties.get(EquipmentSPPropertiesFSP150CM.VI.SlotIndex);
    String ehName = MtosiConstants.SHELF_TEXT + String.valueOf(shelfIndex) + MtosiConstants.SLOT_TEXT + MtosiAddress.getSlotName(neType, slotIndex);

    return createNamingAttributes(ne, ObjectAcronymT.EH, ehName);
  }

	public static NamingAttributesT createNamingAttributesEquipment(NetworkElement ne, EquipmentSPProperties properties)
	{
		NetworkElementMediator neMediator = NetworkElementMediatorFactory.createNetworkElementMediator(ne);
		NamingAttributesT namingAttributes = neMediator.createNamingAttributesMe();
		String relativeName = neMediator.getRelativeNameForProperties(properties);
		String equipmentRelativeName = getEquipmentRelativeNameForProperties(ne, properties);
		// namingAttributes.setEqNm(relativeName);
		namingAttributes.setEhNm(relativeName);
		namingAttributes.setEqNm(equipmentRelativeName);
		return namingAttributes;
	}

	public static String getRelativeSlotNameForProperties(NetworkElement ne, EquipmentSPProperties properties)
	{
		int type = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI();
		switch (type)
		{
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CC:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
        return MtosiConstants.DEFAULT_FIXED_SLOT_NAME;

      case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
        return MtosiConstants.SHELF_TEXT + properties.get(EquipmentSPProperties.VI.ChassisId) +
                MtosiConstants.SLOT_TEXT + properties.get(EquipmentSPProperties.VI.RelativePosition);

      case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
        return MtosiConstants.SHELF_TEXT + properties.get(EquipmentSPProperties.VI.ChassisId) +
                MtosiConstants.DEFAULT_SLOT_NAME;

      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
        throw new RuntimeException("SlotIndex is missing in EquipmentSPProperties");

      default:
				return null;
		}
	}

	public static String getRelativeNameForProperties(NetworkElementFSP150CM ne, SlotF3SPProperties properties)
	{
		int  neType = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI();
		Integer shelfIndex = properties.get(SlotF3SPProperties.VI.ShelfIndex);
		Integer slotIndex = properties.get(SlotF3SPProperties.VI.SlotIndex);
		return MtosiConstants.SHELF_TEXT + String.valueOf(shelfIndex) + MtosiConstants.SLOT_TEXT + MtosiAddress.getSlotName(neType, slotIndex);
	}

	public static String getEquipmentRelativeNameForProperties(NetworkElement ne, EquipmentSPProperties properties)
	{
		return MtosiConstants.DEFAULT_EQUIPMENT_NAME; // for now, equipment is
		// always hardcoded to 1
	}

	// given: /shelf=1/slot=1/port=LAN-5, must return LAN-5
	public static String portNameFromShelfCombo(final String name) throws ProcessingFailureException
	{
		if (!NamingTranslationFactory.isPtpNameValid(name))
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
					MtosiErrorConstants.PTP_NAME_NOT_VALID);
			ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
			throw pfe;
		}
		return name.split(MtosiConstants.PORT_TEXT)[1];
	}

	
	public static Integer pmeNoFromShelfCombo(final String name)
	{
		
		String num = name.split(MtosiConstants.PME_TEXT)[1];
		try
		{
			return Integer.valueOf(num);
		}
		catch(NumberFormatException nfe)
		{
			return Integer.valueOf(-1);
		}
	}
	
	// given /shelf=1/port=LAN-5
	// given /shelf=1/port=NET-1-A
	// new convension /shelf=1/slot=1/port=LAN-5
	// new conesnsion /shelf=1/slot=1/port=NET-A
	// return self index
	public static int shelfNumberFromShelfCombo(String name)
	{
		int result;
		int i = name.indexOf('=');
		if (i == -1)
			return i;
		try
		{
			result = name.charAt(i + 1) - '0';
		}
		catch (NumberFormatException ex)
		{
			result = -1;
		}
		return result;
	}

public static int pgNumberFromName(String name)
	{
    String[] tokens=name.split("=");
    if (tokens.length<2) {
			return -1;
		}else if (tokens.length==4){
				return Integer.parseInt(tokens[3]); // XG120 case
    }else {
      return Integer.parseInt(tokens[1]); //EGX case
    }
	}

	/**
	 * Extract the slot index from the text after slot=
	 * We now support slot=SCU, for Bits ports 
	 * @param name
	 * @return
	 */

	public static int slotNumberFromShelfCombo(String name)
	{
		int result;
		int i = name.indexOf("slot=");
		if (i == -1)
			return i;
		try
		{
		  if (name.length() > i+8 && MtosiConstants.EQUIPMENT_SCU.equals(name.substring(i+5, i+8))) {
		    result = 0;  //Sadly it gets incremented outside this method...
		  } else {
  			result = name.charAt(i + 5) - '0';
  			if (name.length() > 15 && name.charAt(i + 6) != '/')
  			{
  				result *= 10;
  				result += name.charAt(i + 6) - '0';
  			}
		  }
		}
		catch (NumberFormatException ex)
		{
			result = -1;
		}
		return result;
	}

	public static NamingAttributesT getNamingAttributes(Port port)
	{
		NamingAttributesT namingAttributesT = new NamingAttributesT();
		namingAttributesT.setMdNm(OSFactory.getMDNm());
		namingAttributesT.setMeNm(port.getNE().getName());
		namingAttributesT.setPtpNm(port.getMtosiName());
		return namingAttributesT;
	}

  public static NamingAttributesT getNamingAttributesCTPonFTP(Port port)
	{
    NamingAttributesT namingAttributesT = new NamingAttributesT();
		namingAttributesT.setMdNm(OSFactory.getMDNm());
		namingAttributesT.setMeNm(port.getNE().getName());
    if (port instanceof MTOSIPortF3Net) {
      namingAttributesT.setFtpNm(((MTOSIPortF3Net)port).getContainingFTP().getFTPName());
    }
    else {
		NetworkElement ne = port.getNE();
      namingAttributesT.setFtpNm(ne.getMTOSIWorker().getFTP().getFTPName());
    }
    namingAttributesT.setCtpNm(port.getCTPName());
    return namingAttributesT;
	}


	public static NamingAttributesT getNamingAttributes(final MTOSIFlowF3 flow)
	{
		NamingAttributesT namingAttributes = new NamingAttributesT();
		namingAttributes.setMdNm(OSFactory.getMDNm());
		final String neName = flow.getNetworkElement().getName();
		namingAttributes.setMeNm(neName);
		MTOSIPortF3Acc port = flow.getPortFSP150CMAcc();
		final String ptpName = port.getMtosiName();
		namingAttributes.setPtpNm(ptpName);
		final FlowSPPropertiesFSP150CM flowSPProperties = flow.getFlowSPProperties();
		namingAttributes.setCtpNm(flowSPProperties.get(FlowSPPropertiesFSP150CM.VS.FlowName)); // name
		// specified
		// by OS
		return namingAttributes;
	}

	public static NamingAttributesT getNamingAttributes(final QOSShaperF3 shaper)
	{
		NamingAttributesT namingAttributes = new NamingAttributesT();
		namingAttributes.setMdNm(OSFactory.getMDNm());
		final String neName = shaper.getNetworkElement().getName();
		namingAttributes.setMeNm(neName);
		MTOSIPortF3Acc port = shaper.getMTOSIPortF3Acc();
		final String ptpName = port.getMtosiName();
		namingAttributes.setPtpNm(ptpName);
		final ShaperSPProperties props = shaper.getShaperSPProperties();
		final StringBuilder shaperNameBuf = new StringBuilder();
		final MTOSIFlowF3 flow = shaper.getMTOSIFlowF3();
		final FlowSPPropertiesFSP150CM flowSPProperties = flow.getFlowSPProperties();
		final String ctpNm = flowSPProperties.get(FlowSPPropertiesFSP150CM.VS.FlowName);
		namingAttributes.setCtpNm(ctpNm);
		if (props.get(ShaperSPProperties.VI.TypeIndex)  == MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_A2N)
		{
			shaperNameBuf.append(MtosiConstants.ING_SHAPER_TEXT);
		}
		else
		{
			shaperNameBuf.append(MtosiConstants.EG_SHAPER_TEXT);
		}
    // Shapers have indexes +1 in SNMP/DB
    shaperNameBuf.append(props.get(ShaperSPProperties.VI.Index) -1);
		namingAttributes.setTcpNm(shaperNameBuf.toString()); // name
																// specified by
		// OS
		return namingAttributes;
	}

  public static NamingAttributesT getNamingAttributes(final QOSFlowPolicerImpl policer)
  {
		final MTOSIFlowF3 flow = (MTOSIFlowF3)FlowF3Impl.recreate(policer.getNetworkElement(), policer.getPolicerDBImpl().getFlowFSP150CMDBImpl());
    MTOSIPortF3Acc port =  flow.getPortFSP150CMAcc();
    final FlowSPPropertiesFSP150CM flowProps = flow.getFlowSPProperties();
    final F3PolicerSPProperties policerProps = policer.getPolicerSPProperties();

    NamingAttributesT namingAttributes = new NamingAttributesT();
    namingAttributes.setMdNm(OSFactory.getMDNm());
    final String neName = policer.getNetworkElement().getName();
    namingAttributes.setMeNm(neName);
    final String ptpName = port.getMtosiName();
    namingAttributes.setPtpNm(ptpName);
    final StringBuilder policerNameBuf = new StringBuilder();
    final String ctpNm = flowProps.get(FlowSPPropertiesFSP150CM.VS.FlowName);
    namingAttributes.setCtpNm(ctpNm);
    if (policerProps.get(ShaperSPProperties.VI.TypeIndex)  == MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_A2N)
    {
      policerNameBuf.append(MtosiConstants.ING_POLICER_TEXT);
    }
    else
    {
      policerNameBuf.append(MtosiConstants.EG_POLICER_TEXT);
    }
    // Policers have indexes +1 in SNMP/DB
    policerNameBuf.append(policerProps.get(ShaperSPProperties.VI.Index) -1);
    namingAttributes.setTcpNm(policerNameBuf.toString()); // name
                                // specified by
    // OS
    return namingAttributes;
  }

	/**
	 * Return required naming for FTP.
	 *
	 * @param ftp
	 * @return NamingAttributesT
	 */
	public static NamingAttributesT getNamingAttributes(final FTP ftp)
	{
		NamingAttributesT namingAttributes = new NamingAttributesT();
		namingAttributes.setMdNm(OSFactory.getMDNm());
		final String neName = ftp.getNetworkElement().getName();
		namingAttributes.setMeNm(neName);
		final String ftpMtosiName = ftp.getFTPName();
		namingAttributes.setFtpNm(ftpMtosiName);
		return namingAttributes;
	}

	public static NamingAttributesT getNamingAttributes(String meName, String pgName)
	{
		NamingAttributesT namingAttributes = new NamingAttributesT();
		namingAttributes.setMdNm(OSFactory.getMDNm());
		namingAttributes.setMeNm(meName);
		namingAttributes.setFtpNm(pgName);
		return namingAttributes;
	}
	/**
	 * Return required naming for an HN4000 FTP.
	 * /port=LAG-901
	 *
	 *
	 * @param ftp
	 * @return NamingAttributesT
	 */
	public static NamingAttributesT getNamingAttributesHN4000(final FTP ftp)
	{
		NamingAttributesT namingAttributes = new NamingAttributesT();
		namingAttributes.setMdNm(OSFactory.getMDNm());
		final String neName = ftp.getNetworkElement().getName();
		namingAttributes.setMeNm(neName);
		final String ftpMtosiName = MtosiConstants.PORT_TEXT+ftp.getFTPName();
		namingAttributes.setFtpNm(ftpMtosiName);
		return namingAttributes;
	}

	/**
	 * Return required naming for FDFr.
	 *
	 * @param fdfr
	 * @return NamingAttributesT
	 */
	public static NamingAttributesT getNamingAttributes(final FDFr fdfr)
	{
		NamingAttributesT namingAttributes = new NamingAttributesT();
		namingAttributes.setMdNm(OSFactory.getMDNm());
		FDFrSPProperties fdfrSPProperties = fdfr.getFDFrSPProperties();
		final String fdfrName = fdfrSPProperties.get(FDFrSPProperties.VS.FDFrName);
		namingAttributes.setFdfrNm(fdfrName);
		return namingAttributes;
	}



	public static NamingAttributesT getNamingAttributes(final FDFrACCEndFSP150CMIDs endIDs, final NetworkElementF3 ne) throws ProcessingFailureException
	{
		final int shelfIndex = endIDs.getShelfIndex();
		final int slotIndex = endIDs.getSlotIndex();
		final MTOSIPortF3Acc port = (MTOSIPortF3Acc) ne.getMTOSIWorker().getPortByName(endIDs.getPortName(), shelfIndex, slotIndex);
		if (port == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
					MtosiErrorConstants.PTP_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		final MTOSIFlowF3 flow = port.getFlowFSP150CM(1);
		if (flow == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
					MtosiErrorConstants.CTP_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		return getNamingAttributes(flow);
	}


	public static NamingAttributesT getNamingAttributes(final FDFrNETEndFSP150CMIDs endIDs, final NetworkElementF3 ne) throws ProcessingFailureException
	{
		final int shelfIndex = endIDs.getShelfIndex();
		final int slotIndex = endIDs.getSlotIndex();
		final MTOSIPortF3Net port = (MTOSIPortF3Net) ne.getMTOSIWorker().getPortByName(MtosiConstants.PORT_NET1, shelfIndex, slotIndex);
		if (port == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
					MtosiErrorConstants.PTP_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		return getNamingAttributes(port);
	}

	public static NamingAttributesT getNamingAttributes(final FDFrFTPEndIDs fdfrFTPEndIDs, final NetworkElement ne) throws ProcessingFailureException
	{
		final String ftpName = fdfrFTPEndIDs.getFTPName();
		final FTP ftp = ne.getMTOSIWorker().getFTP(ftpName);
		if (ftp == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
					MtosiErrorConstants.FTP_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		return getNamingAttributes(ftp);
	}

	/**
	 * Return required naming for FDFr.
	 *
	 * @param fdfrPortEndFSP150CPIDs
	 * @param ne
	 * @return NamingAttributesT
	 */
	public static NamingAttributesT getNamingAttributes(final FDFrPortEndFSP150CPIDs fdfrPortEndFSP150CPIDs, final NetworkElementFSP150CP_MX ne)
			throws ProcessingFailureException
	{
		final int portIndex = fdfrPortEndFSP150CPIDs.getPortIndex();
		final PortFSP150CP_MX portFSP150CP_MX = ne.getPortFSP150CP_MX(portIndex);
		if (portFSP150CP_MX == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
					MtosiErrorConstants.PTP_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		return getNamingAttributes(portFSP150CP_MX);
	}

	/*
	 * public static NamingAttributesT getNamingAttributes(EthernetFlowFSP150CP
	 * flow) { NamingAttributesT namingAttributes = new NamingAttributesT();
	 * namingAttributes.setCtpNm(MtosiConstants.FLOW_PREFIX +
	 * flow.getFlowIndex()); String neName = flow.getNEFSP150CC825().getName();
	 * namingAttributes.setMeNm(neName);
	 * namingAttributes.setMdNm(MtosiConstants.MANAGEMENT_DOMAIN_NAME);
	 * PortFSP150CPDBImpl portDB = flow.getFlowDBImpl().getPortFSP150CPDBImpl();
	 *
	 * String name = NamingTranslationFactory.getPortName(portDB); // need
	 * method on flow to get its port, then can get port name // for now, assume
	 * its this one namingAttributes.setPtpNm(name); return namingAttributes; }
	 */
	/**
	 * Check if mandatory elements are presents.
	 *
	 * @param name
	 * @return boolean
	 */
	public static boolean isMandatoryElementsPresentForTP(final NamingAttributesT name)
	{
		if (name != null)
		{
			final String mdName = name.getMdNm();
			final String meName = name.getMeNm();
			final String ptpName = name.getPtpNm();
			final String ctpName = name.getCtpNm();
			final String ftpName = name.getFtpNm();
			//return mdName != null && meName != null && (ptpName != null || ctpName != null || ftpName != null);
			return mdName != null && meName != null &&
							(ptpName != null && ctpName == null && ftpName == null) || //ptp
							(ptpName != null && ctpName != null && ftpName == null) || //ptp and ctp
							(ptpName == null && ctpName == null && ftpName != null) || // ftp
              (ptpName == null && ctpName != null && ftpName != null);   //ftp and ctp
		}
		return false;
	}

	public static boolean isPtpNameValid(final String ptpName)
	{
		return PtpNmTranslation.validName(ptpName);
	}

	// tcProfileName: /egqueue=2; (0..7)
	// tcProfileName: /ingqueue=2; (0..7)
	// tcProfileName: /egpolicer=2; (0..7)
	// tcProfileName: /ingpolicer=2; (0..7)
	public static boolean isTCProfileNameValid(final String tcProfileName)
	{
		if (tcProfileName == null)
			return false;
		if (tcProfileName.startsWith(MtosiConstants.EG_QUEUE_TEXT))
			return true;
		if (tcProfileName.startsWith(MtosiConstants.ING_QUEUE_TEXT))
			return true;
		if (tcProfileName.startsWith(MtosiConstants.EG_POLICER_TEXT))
			return true;
		if (tcProfileName.startsWith(MtosiConstants.ING_POLICER_TEXT))
			return true;
		if (tcProfileName.startsWith(MtosiConstants.EG_SHAPER_TEXT))
			return true;
		return tcProfileName.startsWith(MtosiConstants.ING_SHAPER_TEXT);
	}

	// lag_fragment=#number#
	public static int getLAGFragmentIndex(final String ctpNm)
	{
		//String plain = ctpNm.replaceAll("#", ""); // get rid of # signs
		final String resultString = ctpNm.split(MtosiConstants.LAG_FRAGMENT_TEXT)[1];
		int result;
		try
		{
			result = Integer.parseInt(resultString);
		}
		catch (NumberFormatException nfe)
		{
			return -1;
		}
		return result;
	}
	
	public static int get2BaseTLPortNo(final String ftpNm)
	{
		final String resultString = ftpNm.split(MtosiConstants.BONDED_FTP_TEXT)[1];
		int result;
		try
		{
			result = Integer.parseInt(resultString);
		}
		catch (NumberFormatException nfe)
		{
			return -1;
		}
		return result;
	}
	
	public static int getEthPortNo(final String ftpNm)
	{
		final String resultString = ftpNm.split(MtosiConstants.HN_ETH_TEXT)[1];
		int result;
		try
		{
			result = Integer.parseInt(resultString);
		}
		catch (NumberFormatException nfe)
		{
			return -1;
		}
		return result;
	}

	/**
	 * /egpolicer=2; (0..7) /ingpolicer=2; (0..7)
	 *
	 * @param tcpNm
	 * @return int
	 */
	public static int getPolicerIndex(final String tcpNm) throws ProcessingFailureException
	{
		if (!NamingTranslationFactory.isTCProfileNameValid(tcpNm))
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
					MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		final String arrayString[] = tcpNm.split(MtosiConstants.POLICER_TEXT);
		if (arrayString.length <= 1)
		{
			final ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
					MtosiErrorConstants.TC_PROFILE_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		final String resultString = arrayString[1]; // support
		// ingress
		// and
		// egress
		// policer.
		int result;
		try
		{
			result = Integer.parseInt(resultString);
		}
		catch (NumberFormatException nfe)
		{
			result = -1;
		}
		if (result >= 0 && result <= 7)
			return result;
		else
		{
			final ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
					MtosiErrorConstants.TC_PROFILE_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
	}

	/**
	 * /egqueue=2; (0..7) /ingqueue=2; (0..7)
	 *
	 * @param propNm
	 * @return int
	 */
	public static int getQOSIndex(final String propNm) throws ProcessingFailureException
	{
		if (!NamingTranslationFactory.isTCProfileNameValid(propNm))
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
					MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		final String arrayString[] = propNm.split(MtosiConstants.QUEUE_TEXT);
		if (arrayString.length <= 1)
		{
			final ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
					MtosiErrorConstants.TC_PROFILE_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}

		final String resultString = arrayString[1];
		int result;
		try
		{
			result = Integer.parseInt(resultString);
		}
		catch (NumberFormatException nfe)
		{
			result = -1;
		}
		if (result >= 0 && result <= 7)
			return result;
		else
		{
			final ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
					MtosiErrorConstants.TC_PROFILE_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}

	}

	public static int getShaperIndex(final String propNm) throws ProcessingFailureException
	{
		if (!NamingTranslationFactory.isTCProfileNameValid(propNm))
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
					MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		final String arrayString[] = propNm.split(MtosiConstants.SHAPER_TEXT);
		if (arrayString.length <= 1)
		{
			final ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
					MtosiErrorConstants.TC_PROFILE_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}

		final String resultString = arrayString[1];
		int result;
		try
		{
			result = Integer.parseInt(resultString);
		}
		catch (NumberFormatException nfe)
		{
			result = -1;
		}
		if (result == 0)
			return result+1;
		else
		{
			final ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
					MtosiErrorConstants.TC_PROFILE_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
	}

	public static NamingAttributesT getNamingAttributesFTP(PortHN4000Ethernet2BASE_TL port) {
		NamingAttributesT namingAttributesT = null;
		namingAttributesT = new NamingAttributesT();
		namingAttributesT.setMdNm(OSFactory.getMDNm());
		namingAttributesT.setMeNm(port.getNE().getName());
		namingAttributesT.setFtpNm(port.getMtosiName());
		return namingAttributesT;
	}

	

	public static NamingAttributesT getNamingAttributes(FlowHN4000 flow) {
		NamingAttributesT namingAttributesT = null;
		Port port = flow.getPortHN4000();
		try {
			if ( port  instanceof PortHN4000Ethernet2BASE_TL || port instanceof LAGHN4000) {
				namingAttributesT = new NamingAttributesT();
				namingAttributesT.setMdNm(OSFactory.getMDNm());
				namingAttributesT.setMeNm(port.getNE().getName());
				namingAttributesT.setFtpNm(port.getMtosiName());
				
			} else {
				namingAttributesT = getNamingAttributes(port);
			}
			String flowName = flow.getFlowSPProperties().get(FlowSPPropertiesHN4000.VS.Desc);
			namingAttributesT.setCtpNm(String.valueOf(flowName));
		} catch (Exception e) { 
			//DW TODO - remove.
			//Had some issues with Ports, so this is temporary.
			namingAttributesT = new NamingAttributesT();
			namingAttributesT.setPtpNm("Exception: "+e);
			namingAttributesT.setCtpNm(flow.toString());
		}
		return namingAttributesT;
	}
	
	public static boolean isFtpBonding(final NamingAttributesT name)
	{
		if (name != null)
		{
			final String portName = name.getPtpNm();
			final String ctpName = name.getCtpNm();
			final String ftpName = name.getFtpNm();
			if (ftpName != null && portName == null && ctpName == null)
			{
				if(ftpName.contains(MtosiConstants.BONDED_FTP_TEXT))
				{
					return true;
				}
			}
				
		}
		return false;
	}
	
	public static boolean isFtpLag(final NamingAttributesT name)
	{
		if (name != null)
		{
			final String portName = name.getPtpNm();
			final String ctpName = name.getCtpNm();
			final String ftpName = name.getFtpNm();
			if (ftpName != null && portName == null && ctpName == null)
			{
				if(ftpName.contains(MtosiConstants.LAG_FTP_TEXT))
				{
					return true;
				}
			}
				
		}
		return false;
	}
	
	public static boolean isFlowBonding(final NamingAttributesT name)
	{
		if (name != null)
		{
			final String portName = name.getPtpNm();
			final String ctpName = name.getCtpNm();
			final String ftpName = name.getFtpNm();
			if (ftpName != null && portName == null && ctpName != null)
			{
				if(ftpName.contains(MtosiConstants.BONDED_FTP_TEXT))
				{
					return true;
				}
			}
				
		}
		return false;
	}

	public static boolean isFlowLag(final NamingAttributesT name)
	{
		if (name != null)
		{
			final String portName = name.getPtpNm();
			final String ctpName = name.getCtpNm();
			final String ftpName = name.getFtpNm();
			if (ftpName != null && portName == null && ctpName != null)
			{
				if(ftpName.contains(MtosiConstants.LAG_FTP_TEXT))
				{
					return true;
				}
			}
				
		}
		return false;
	}

	public static boolean isFlowUplink(final NamingAttributesT name)
	{
		if (isFlowEth(name) || isFlowLag(name))
		{
			return true;
		}
		
		return false;
	}

	public static boolean isFlowEth(final NamingAttributesT name)
	{
		if (name != null)
		{
			final String portName = name.getPtpNm();
			final String ctpName = name.getCtpNm();
			final String ftpName = name.getFtpNm();
			if (ftpName == null && portName != null && ctpName != null)
			{
				if(portName.contains(MtosiConstants.HN_ETH_TEXT))
				{
					return true;
				}
			}
				
		}
		return false;
	}

	public static NamingAttributesT getNamingAttributes(ShaperHN4000 shaper, String type) {
		NamingAttributesT namingAttributes = new NamingAttributesT();
		namingAttributes.setMdNm(OSFactory.getMDNm());
		final String neName = shaper.getNetworkElement().getName();
		namingAttributes.setMeNm(neName);
		PortHN4000 port = shaper.getUni4xxHN4000().getPortEthernet(); 
		// PTP or FTP
		final String name = port.getMtosiName();
		if (name.startsWith("/port")) {
			namingAttributes.setFtpNm(name);
		} else {
			namingAttributes.setPtpNm(name);
		}
		StringBuilder shaperNameBuf = new StringBuilder(type);

		shaperNameBuf.append(0);
		namingAttributes.setTcpNm(shaperNameBuf.toString()); // name
		return namingAttributes;

	}

  /**
   * Return required naming for TDFr.
   *
   * @param tdfr
   * @return NamingAttributesT
   */
  public static NamingAttributesT getNamingAttributes(final F3SyncImpl tdfr)
  {
    NamingAttributesT namingAttributes = new NamingAttributesT();
    F3SyncSPProperties tdfrSPProperties = tdfr.getF3SyncSPProperties();

    namingAttributes.setMdNm(OSFactory.getMDNm());
    namingAttributes.setMeNm(tdfr.getNetworkElement().getName());
    namingAttributes.setTdfrNm(tdfrSPProperties.get(F3SyncSPProperties.VS.MTOSIName));
    return namingAttributes;
  }

	public static NamingAttributesT createNamingAttributesPTP(String mdName, String meName, String ptpName){

		NamingAttributesT namingAttributes = new NamingAttributesT();
		namingAttributes.setMdNm(mdName);
		namingAttributes.setMeNm(meName);
		namingAttributes.setPtpNm(ptpName);

		return namingAttributes;
	}


	public static NamingAttributesT createNamingAttributesCTPFromMtosiAddress(MtosiAddress ptpMtosiAddress, String ctpName){

		NamingAttributesT namingAttributes = new NamingAttributesT();
		String flowName = "";
		String[] flowNameParts = ctpName.split(" && ");
		if(flowNameParts.length == 2){
			flowName = flowNameParts[1];
		}
		namingAttributes.setMdNm(ptpMtosiAddress.getNaming().getMdNm());
		namingAttributes.setMeNm(ptpMtosiAddress.getNaming().getMeNm());

		if(ptpMtosiAddress.getHasFtp()){
			namingAttributes.setFtpNm(ptpMtosiAddress.getNaming().getFtpNm());
		}else {
			namingAttributes.setPtpNm(ptpMtosiAddress.getNaming().getPtpNm());
		}
		namingAttributes.setCtpNm(flowName);

		return namingAttributes;
	}

	public static NamingAttributesT createNamingAttributesCTPWithFTPFromMtosiAddress(MtosiAddress ptpMtosiAddress){

		NamingAttributesT namingAttributes = new NamingAttributesT();

		namingAttributes.setMdNm(ptpMtosiAddress.getNaming().getMdNm());
		namingAttributes.setMeNm(ptpMtosiAddress.getNaming().getMeNm());
		namingAttributes.setFtpNm(ptpMtosiAddress.getNaming().getFtpNm());
		namingAttributes.setCtpNm(ptpMtosiAddress.getNaming().getCtpNm());

		return namingAttributes;
	}

}