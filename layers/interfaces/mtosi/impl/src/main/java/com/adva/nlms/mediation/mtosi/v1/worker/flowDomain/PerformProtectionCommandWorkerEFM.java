/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.ProtectionGroupF3Attr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.v1.adapter.facade.ProtectionCommandFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import v1.tmf854.HeaderT;
import v1.tmf854ext.adva.PerformProtectionCommandT;

import jakarta.xml.ws.Holder;

public class PerformProtectionCommandWorkerEFM extends PerformProtectionCommandWorker{

  private DTO<ProtectionGroupF3Attr> ftpDTO;
  private MtosiMOFacade facade;

  public PerformProtectionCommandWorkerEFM(PerformProtectionCommandT mtosiBody, Holder<HeaderT> mtosiHeader)
  {
    super(mtosiBody, mtosiHeader);
  }

  @Override
  protected void parse() throws Exception {
    super.parse();

    facade = getMtosiCtrl().getMoFacadeManager().getFacadeViaNeId(MtosiMOFacade.class, ne.getID());
    ftpDTO = facade.findDTOViaMtosiName(ne.getID(),tpNameAddress.getNaming().getFtpNm(), ProtectionGroupF3Attr.class);
    if(ftpDTO == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
    }
  }

  @Override
  protected void mediate() throws Exception {
    DTO<ProtectionGroupF3Attr> ftpUpdateDTO = ProtectionCommandFactory.getProtectionGroupDTO(mtosiBody.getSwitchMode());
    ftpUpdateDTO.putOrReplace(ProtectionGroupF3Attr.MTOSI_NAME, ftpDTO.getValue(ProtectionGroupF3Attr.MTOSI_NAME));
    ftpUpdateDTO.putOrReplace(ProtectionGroupF3Attr.ENTITY_INDEX, ftpDTO.getValue(ProtectionGroupF3Attr.ENTITY_INDEX));
    transact(ftpUpdateDTO);
  }

  private void transact(DTO<ProtectionGroupF3Attr> ftpUpdateDTO) throws SNMPCommFailure,ObjectInUseException {
    try{
      facade.openNetTransaction(ne.getID());
      facade.modifyObjectOnDevice(ne.getID(), ftpUpdateDTO);
      logSecurity(ne, SystemAction.ModifyNetwork,ftpUpdateDTO.getValue(ProtectionGroupF3Attr.MTOSI_NAME));
    }finally {
      facade.closeNetTransaction(tpNameAddress.getNE().getID());
    }
  }

}
