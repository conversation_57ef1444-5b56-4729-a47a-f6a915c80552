/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.common.config.EntityClass;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.ModuleCardSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.NTUSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.PsuSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.SFPSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.SFPSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.ShelfSPPropertiesFSP150CM;
import com.adva.nlms.mediation.config.f3.NetworkElementF3;
import com.adva.nlms.mediation.config.f3.entity.module.MTOSICardModuleF3;
import com.adva.nlms.mediation.config.f3.entity.module.fan.MTOSIFanF3;
import com.adva.nlms.mediation.config.f3.entity.module.nte.MTOSINTEF3;
import com.adva.nlms.mediation.config.f3.entity.module.psu.PowerSupplyF3;
import com.adva.nlms.mediation.config.f3.mtosi.NetworkElementFSP150CMMTOSIOperations;
import com.adva.nlms.mediation.config.fsp150cm.entity.module.ntu.MTOSINTUFSP150CM;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.f3.FspF3Mediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.ManagedElementMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.HeaderUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAdministrationControlTranslation;
import v1.tmf854.EqVendorExtensionsT;
import v1.tmf854.EquipmentHolderT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.MEVendorExtensionsT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;

import jakarta.xml.bind.JAXBElement;
import javax.xml.namespace.QName;

/**
 * This class is a Network Element Mediator for the FSP 150 CM boxes.
 */
public class Fsp150cmMediator extends FspF3Mediator {

  public Fsp150cmMediator(NetworkElementF3 ne) {
    super(ne);
    this.ne = ne;
  }

  @Override
  protected JAXBElement<MEVendorExtensionsT> getVendorExtensions() {
    ObjectFactory objFactory = new ObjectFactory();
    MEVendorExtensionsT extensions = new MEVendorExtensionsT();
    populateIpAddress(extensions);
    ManagedElementMediator.populateManagementParameters(ne, extensions);
    //Populate Discover State 
    ManagedElementMediator.populateDiscoveryState(ne, extensions);
    
    return objFactory.createManagedElementTVendorExtensions(extensions);
  }

  protected void populateIpAddress (MEVendorExtensionsT extensions) {
    String ipAddress = ne.getIPAddress();
    JAXBElement<? extends String> je = new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_IPADDRESS), String.class, ipAddress);
    extensions.getAny().add(je);
    JAXBElement jeMask = new JAXBElement<String>(new
            QName(MtosiConstants.VENDOR_NAMESPACE,MtosiConstants.VENDOR_SUBNET_MASK), String.class, getFSP150CMMTOSIWorker(ne).getInterfaceMask());
    extensions.getAny().add(jeMask);
  }

  @Override
  public String getRelativeNameForProperties(EquipmentSPProperties properties) {
    int classType = properties.get(EquipmentSPProperties.VI.ClassType);
    String relativeName = "";
    switch (classType) {
      case EntityClass.SHELF:
        relativeName = MtosiConstants.SHELF_TEXT + properties.get(EquipmentSPProperties.VI.ChassisId);
        break;
      case EntityClass.MODULE:
        if (properties.isSFP()) {
          relativeName = getRelativeNameModule(properties);
        } else {
          relativeName = MtosiConstants.SHELF_TEXT + properties.get(EquipmentSPProperties.VI.ChassisId);
        }
        break;
      case EntityClass.POWER_SUPPLY:
        relativeName = getRelativeNamePSU(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI(), properties);
        break;
      case EntityClass.PORT:
        relativeName = getRelativeNamePort(properties);
        break;
      case EntityClass.FAN:
        relativeName = getRelativeNameFan(properties);
        break;
      default:
    }
    return relativeName;
  }

  protected String getRelativeNameModule(EquipmentSPProperties properties) {
    if (!(properties instanceof SFPSPPropertiesFSP150CM)) {
      throw new IllegalArgumentException("getRelativeNameModule is accepting only SFPSPPropertiesFSP150CM!");
    }
    SFPSPPropertiesFSP150CM propertiesSFP = (SFPSPPropertiesFSP150CM) properties;
    int sfpNumber = propertiesSFP.get(SFPSPProperties.VI.SFPNumber);
    final int neType = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI();
    int slotIndex = propertiesSFP.get(SFPSPPropertiesFSP150CM.VI.SlotIndex);
    if (ManagedElementFactory.needsSlotIncrement(neType)) {
    	slotIndex--;
    }
    
	switch (sfpNumber) {
      case 1:
        return MtosiConstants.SHELF_TEXT + propertiesSFP.get(SFPSPPropertiesFSP150CM.VI.ShelfIndex) + MtosiConstants.SLOT_TEXT + slotIndex + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_NET1;
      case 2:
        return MtosiConstants.SHELF_TEXT + propertiesSFP.get(SFPSPPropertiesFSP150CM.VI.ShelfIndex) + MtosiConstants.SLOT_TEXT + slotIndex + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_NET2;
      case 3:
        if (neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206) {
          return MtosiConstants.SHELF_TEXT + propertiesSFP.get(SFPSPPropertiesFSP150CM.VI.ShelfIndex) + MtosiConstants.SLOT_TEXT + slotIndex + MtosiConstants.SUBSLOT_TEXT + "ACC-" + sfpNumber;

        }
        return MtosiConstants.SHELF_TEXT + propertiesSFP.get(SFPSPPropertiesFSP150CM.VI.ShelfIndex) + MtosiConstants.SLOT_TEXT + slotIndex + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_ACC;
      // for GE206
      case 4:
        // SFP-Nbr of GE201(se) access port is "4"
        if (neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201 ||
            neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE  ||
            neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR ||
            neType == NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM ||
            neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM) {
          return MtosiConstants.SHELF_TEXT + propertiesSFP.get(SFPSPPropertiesFSP150CM.VI.ShelfIndex) + MtosiConstants.SLOT_TEXT + slotIndex + MtosiConstants.SUBSLOT_TEXT + MtosiConstants.PORT_ACC;
        }
      case 5:
      case 6:
        if (neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206) {
          return MtosiConstants.SHELF_TEXT + propertiesSFP.get(SFPSPPropertiesFSP150CM.VI.ShelfIndex) + MtosiConstants.SLOT_TEXT + slotIndex + MtosiConstants.SUBSLOT_TEXT + "ACC-" + sfpNumber;
        }
    }
    return null;
  }

  private static String getRelativeNamePSU(int neType, EquipmentSPProperties properties) {
    if (!(properties instanceof EquipmentSPPropertiesFSP150CM)) {
      throw new IllegalArgumentException("getRelativeNamePSU is accepting only EquipmentSPPropertiesFSP150CM!");
    }
    EquipmentSPPropertiesFSP150CM eqCMProperties = (EquipmentSPPropertiesFSP150CM) properties;
    if (neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201 ||
        neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE ||
        neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206) {
      if (eqCMProperties.get(EquipmentSPPropertiesFSP150CM.VI.SlotIndex) == 4) {
        return MtosiConstants.SHELF_TEXT + eqCMProperties.get(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex) + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_PSU1;
      } else {
        return MtosiConstants.SHELF_TEXT + eqCMProperties.get(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex) + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_PSU2;
      }
    }
    else {
      if (eqCMProperties.get(EquipmentSPPropertiesFSP150CM.VI.SlotIndex) == 18) {
        return MtosiConstants.SHELF_TEXT + eqCMProperties.get(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex) + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_PSU1;
      } else {
        return MtosiConstants.SHELF_TEXT + eqCMProperties.get(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex) + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_PSU2;
      }
    }
  }

  protected static String getRelativeNamePort(EquipmentSPProperties properties) {
    if (!(properties instanceof SFPSPPropertiesFSP150CM)) {
      throw new IllegalArgumentException("getRelativeNamePort is accepting only SFPSPPropertiesFSP150CM!");
    }
    SFPSPPropertiesFSP150CM propertiesSFP = (SFPSPPropertiesFSP150CM) properties;
    int sfpNumber = propertiesSFP.get(SFPSPProperties.VI.SFPNumber);

    switch (sfpNumber) {
      case 1:
        return MtosiConstants.SHELF_TEXT + propertiesSFP.get(SFPSPPropertiesFSP150CM.VI.ShelfIndex) + MtosiConstants.SLOT_TEXT + (propertiesSFP.get(SFPSPPropertiesFSP150CM.VI.SlotIndex)-1) + MtosiConstants.PORT_TEXT + MtosiConstants.PORT_NET1;
      case 2:
        return MtosiConstants.SHELF_TEXT + propertiesSFP.get(SFPSPPropertiesFSP150CM.VI.ShelfIndex) + MtosiConstants.SLOT_TEXT + (propertiesSFP.get(SFPSPPropertiesFSP150CM.VI.SlotIndex)-1) + MtosiConstants.PORT_TEXT + MtosiConstants.PORT_NET2;
      case 3:
        return MtosiConstants.SHELF_TEXT + propertiesSFP.get(SFPSPPropertiesFSP150CM.VI.ShelfIndex) + MtosiConstants.SLOT_TEXT + (propertiesSFP.get(SFPSPPropertiesFSP150CM.VI.SlotIndex)-1) + MtosiConstants.PORT_TEXT + MtosiConstants.PORT_ACC;
    }
    return null;
  }

  private static String getRelativeNameFan(EquipmentSPProperties properties) {
    if (!(properties instanceof EquipmentSPPropertiesFSP150CM)) {
      throw new IllegalArgumentException("getRelativeNameFan is accepting only EquipmentSPPropertiesFSP150CM!");
    }
    EquipmentSPPropertiesFSP150CM eqCMProperties = (EquipmentSPPropertiesFSP150CM) properties;
    if (eqCMProperties.get(EquipmentSPPropertiesFSP150CM.VI.SlotIndex) == 20) {
      return MtosiConstants.SHELF_TEXT + eqCMProperties.get(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex) + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_FAN1;
    } else {
      return MtosiConstants.SHELF_TEXT + eqCMProperties.get(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex) + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_FAN2;
    }
  }

  @Override
  protected void setExpectedOrInstalledEquipment (EquipmentHolderT equipmentHolder, NamingAttributesT namingEquipment, EquipmentSPProperties properties) {
    ObjectFactory objFactory = new ObjectFactory();
    equipmentHolder.setExpectedOrInstalledEquipment(objFactory.createEquipmentHolderTExpectedOrInstalledEquipment(namingEquipment));
  }

  @Override
  protected JAXBElement<EqVendorExtensionsT> getVendorExtensions(EquipmentSPProperties properties) {
    if (!(properties instanceof ShelfSPPropertiesFSP150CM)) {
      throw new IllegalArgumentException("getVendorExtensions is accepting only ShelfSPPropertiesFSP150CM!");
    }
    ShelfSPPropertiesFSP150CM props = (ShelfSPPropertiesFSP150CM) properties;
    ObjectFactory objFactory = new ObjectFactory();
    EqVendorExtensionsT extensions = new EqVendorExtensionsT();
    String adminControl = "";
    if (props.get(EquipmentSPProperties.VI.AdminState) < 6) {
      adminControl = MtosiUtils.getMtosiString(CMAdministrationControlTranslation.NOT_APPLICABLE, props.get(EquipmentSPProperties.VI.AdminState));
    }
    extensions.getAny().add(new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_ADMINISTRATION_CONTROL), String.class, adminControl));
    extensions.getAny().add(new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
              MtosiConstants.VENDOR_SECONDARY_STATE), String.class, props.get(ShelfSPPropertiesFSP150CM.VS.SecondaryState)));
    return objFactory.createEquipmentTVendorExtensions(extensions);
  }

  @Override
  protected JAXBElement<EqVendorExtensionsT> getEquipmentVendorExtensions(EquipmentSPProperties properties) {
    if (!(properties instanceof EquipmentSPPropertiesFSP150CM)) {
      throw new IllegalArgumentException("getEquipmentVendorExtensions is accepting only EquipmentSPPropertiesFSP150CM!");
    }
    EquipmentSPPropertiesFSP150CM props = (EquipmentSPPropertiesFSP150CM) properties;
    ObjectFactory objFactory = new ObjectFactory();
    EqVendorExtensionsT extensions = new EqVendorExtensionsT();
    extensions.getAny().add(new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_ADMINISTRATION_CONTROL), String.class,
            MtosiUtils.getMtosiString(CMAdministrationControlTranslation.NOT_APPLICABLE, props.get(EquipmentSPProperties.VI.AdminState))));
    String secondaryState = null;
    NetworkElementFSP150CMMTOSIOperations mtosiWorker = (NetworkElementFSP150CMMTOSIOperations)getFSP150CMMTOSIWorker(ne);

    switch (MtosiUtils.getCardTypeFromSlotIndex(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI(), props.get(EquipmentSPPropertiesFSP150CM.VI.SlotIndex))) {
      case MIBFSP150CM.Entity.SlotTable.TYPE_GENERIC_INDEX: {
        MTOSICardModuleF3 card = mtosiWorker.getModule(props.get(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex), props.get(EquipmentSPPropertiesFSP150CM.VI.SlotIndex));
        if (card != null) {
          ModuleCardSPPropertiesFSP150CM cardProps = (ModuleCardSPPropertiesFSP150CM)card.getEquipmentSPProperties();
          if (card instanceof MTOSINTUFSP150CM ||
              card instanceof MTOSINTEF3) { //todo dw - Replace
            LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();
            LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET);
            LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                    LayeredParams.LrPropAdvaEthernet.TRAFFIC_MANAGEMENT_ENABLED_PARAM,
                    MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, MIB.RFC1253.TRUTH_VALUE_TRUE));

            LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                    LayeredParams.LrPropAdvaEthernet.SNMP_DYING_GASP_ENABLED_PARAM,
                    MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, ((NTUSPPropertiesFSP150CM)cardProps).get(NTUSPPropertiesFSP150CM.VI.SNMPDyingGaspEnabled)));

            JAXBElement<LayeredParametersListT> transmissionParams = objFactory
                    .createConnectionTerminationPointTTransmissionParams(layeredParametersListT);
            extensions.getAny().add(transmissionParams);
          }
          secondaryState = cardProps.get(EquipmentSPPropertiesFSP150CM.VS.SecondaryState);
        }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_SCU_INDEX: {
        MTOSICardModuleF3 card = mtosiWorker.getModule(props.get(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex), props.get(EquipmentSPPropertiesFSP150CM.VI.SlotIndex));
        if (card != null) {
          secondaryState = ((ModuleCardSPPropertiesFSP150CM)card.getEquipmentSPProperties()).get(EquipmentSPPropertiesFSP150CM.VS.SecondaryState);
        }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_PSU_INDEX: {
        PowerSupplyF3 psu = mtosiWorker.getPSU(props.get(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex), props.get(EquipmentSPPropertiesFSP150CM.VI.SlotIndex));
        if (psu != null) {
          secondaryState = ((PsuSPPropertiesFSP150CM)psu.getEquipmentSPProperties()).get(EquipmentSPPropertiesFSP150CM.VS.SecondaryState);
        }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_FAN_INDEX: {
        MTOSIFanF3 fan = mtosiWorker.getFAN(props.get(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex), props.get(EquipmentSPPropertiesFSP150CM.VI.SlotIndex));
        if (fan != null) {
          secondaryState = ((EquipmentSPPropertiesFSP150CM)fan.getEquipmentSPProperties()).get(EquipmentSPPropertiesFSP150CM.VS.SecondaryState);
        }
        break;
      }
    }
    if (secondaryState != null) {
      extensions.getAny().add(new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
              MtosiConstants.VENDOR_SECONDARY_STATE), String.class, secondaryState));
    }
    return objFactory.createEquipmentTVendorExtensions(extensions);
  }

  @Override
  protected JAXBElement<EqVendorExtensionsT> getSFPVendorExtensions() {
    ObjectFactory objFactory = new ObjectFactory();
    EqVendorExtensionsT extensions = new EqVendorExtensionsT();
    String adminControl = AdministrationControlTranslation.ENABLED.getMtosiString();
    extensions.getAny().add(new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_ADMINISTRATION_CONTROL), String.class, adminControl));
    extensions.getAny().add(new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_SECONDARY_STATE), String.class, "Active"));
    return objFactory.createEquipmentTVendorExtensions(extensions);
  }

  @Override
  protected JAXBElement<String> getSfpManufacturerDate (EquipmentSPProperties properties) {
    ObjectFactory objFactory = new ObjectFactory();
    return objFactory.createEquipmentTManufacturerDate(HeaderUtils.formatDate(properties.get(EquipmentSPProperties.VL.ManufactureDate)));
  }

  @Override
  protected String getShelfEquipment (EquipmentSPProperties properties) {
    throw new RuntimeException("Not supported!");
  }

  @Override
  protected void setExpectedOrInstalledSfpEquipment (EquipmentHolderT equipmentHolder, NamingAttributesT namingEquipment, EquipmentSPProperties properties) {
    ObjectFactory objFactory = new ObjectFactory();
    if (MtosiUtils.isInstalled(properties)) {
      equipmentHolder.setExpectedOrInstalledEquipment(objFactory.createEquipmentHolderTExpectedOrInstalledEquipment(namingEquipment));
    } else {
      equipmentHolder.setExpectedOrInstalledEquipment(objFactory.createEquipmentHolderTExpectedOrInstalledEquipment(new NamingAttributesT()));
    }
  }

  /**
   * The CM does things a little differently, so this "should not" be called?
   * 
   */
  @Override
  protected String getPsuType(EquipmentSPProperties properties) {
	return MtosiConstants.EQUIPMENT_PSU;
  }

  private static NetworkElementFSP150CMMTOSIOperations getFSP150CMMTOSIWorker(NetworkElementF3 ne) {
    return (NetworkElementFSP150CMMTOSIOperations)ne.getMTOSIWorker();
  }
}
