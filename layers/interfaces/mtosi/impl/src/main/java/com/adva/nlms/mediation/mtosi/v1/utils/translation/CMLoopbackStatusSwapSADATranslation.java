/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum CMLoopbackStatusSwapSADATranslation implements TranslatableEnum {
  NOT_APPLICABLE      (0, "None"),
  NO_SWAP_SADA        (1, "None"),
  SWAP_SADA           (2, "SwapSADA"),
  SWAP_DA_OVERRIDE_SA (3, "SwapDAOverrideSA"),
  SWAP_SADA_MAC_ONLY (4, "SwapSADAMacOnly"),
  SWAP_DA_OVERRIDES_SA_MAC_ONLY (5, "SwapDAOverridesSAMacOnly"),
  SWAP_IP(6, "swapIP"),
  SWAP_TCP_UDP(7, "swapTCP/UDP"),
  ;

  private final int    mibValue;
  private final String mtosiString;

  private CMLoopbackStatusSwapSADATranslation (final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
  
  /**
   * Return the Mib value, filtering out the NOT_APPLICABLE 
   * Since None is in there twice.
   * @param name
   * @return
   */
  public static int getMibValue(final String name) {
  	for (CMLoopbackStatusSwapSADATranslation value: values() ) {
  		if (value.getMtosiString().equals(name) && value.getMIBValue() != 0) {
  			return value.getMIBValue(); 
  		}
  	}
  	return 0;
  }

}