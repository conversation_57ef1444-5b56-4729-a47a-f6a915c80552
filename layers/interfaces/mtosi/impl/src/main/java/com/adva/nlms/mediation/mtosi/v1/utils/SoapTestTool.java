/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils;

	/** 
	 * SOAPClient4XG. Read the SOAP envelope file passed as the second
	 * parameter, pass it to the SOAP endpoint passed as the first parameter, and
	 * print out the SOAP envelope passed as a response.  with help from <PERSON>
	 <PERSON> 03/09/01
	 * 
	 *
	 * <AUTHOR>
	 * @version 1.1
	 * @param   SOAPUrl      URL of SOAP Endpoint to send request.
	 * @param   xmlFile2Send A file with an XML document of the request.  
	 *
	 * 5/23/01 revision: SOAPAction added
	*/

	import java.io.*;
import java.net.*;

public class SoapTestTool {

      private final static String endf = "UTF-8";

	    public static void main(String[] args) throws Exception {

	        if (args.length  < 2) {
	            System.err.println("Usage:  java SOAPTestTool " +
	                               "http://soapURL soapEnvelopefile.xml" +
	                               " [SOAPAction]");
				System.err.println("SOAPAction is optional.");
	            System.exit(1);
	        }

	        if ("-f".equals(args[0]) ) {
	        	processFile(args[1]);
	        } else {
		        String SOAPUrl      = args[0];
		        String xmlFile2Send = args[1];
	
				  String SOAPAction = "";
		        if (args.length  > 2) 
						SOAPAction = args[2];
						
		        sendRequest(SOAPUrl, xmlFile2Send, SOAPAction);
	        }
	    }
	    
	    /**
	     * Another method mostly stolen from google...
	     * @param filename
	     */
			private static void processFile(String filename) {
				// Open the file that is the first
				// command line parameter
				if(filename.contains(File.separator)){
					System.err.println("File name should not include directories. ");
					System.exit(1);
				}
				try (FileInputStream fstream = new FileInputStream(filename);
						 DataInputStream in = new DataInputStream(fstream);
						 BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(String.valueOf(in)), java.nio.charset.StandardCharsets.UTF_8));
				) {
					String strLine;
					// Read File Line By Line
					int count = 1;
					while ((strLine = br.readLine()) != null) {
						// Print the content on the console
//				System.out.println(strLine.length()+":"+strLine);
						if (strLine.trim().length() > 0) {
							processLine(strLine);
							System.out.println("ZZzzz" + count++);
							Thread.sleep(10000);
						}
					}

				} catch (Exception e) {// Catch exception if any
					System.err.println("Error: " + e.getMessage());
				}
			}


		private static void processLine(String strLine) {
			String[] args = strLine.split(" ");
			if (args.length < 2) {
				System.err.println("Error in line: "+strLine);
				return;
			}
	        String SOAPUrl      = args[0];
	        String xmlFile2Send = args[1];

			  String SOAPAction = "";
	        if (args.length  > 2) 
					SOAPAction = args[2];
					
	        try {
				sendRequest(SOAPUrl, xmlFile2Send, SOAPAction);
			} catch (Exception e) {
				System.err.println("Error performing: "+strLine);
//				e.printStackTrace();
			}
		}

		private static void sendRequest(String SOAPUrl, String xmlFile2Send, String SOAPAction) throws MalformedURLException, IOException,
				FileNotFoundException, ProtocolException {
			// Create the connection where we're going to send the file.
	        URL url = new URL(SOAPUrl);
	        URLConnection connection = url.openConnection();
	        HttpURLConnection httpConn = (HttpURLConnection) connection;

	        // Open the input file. After we copy it to a byte array, we can see
	        // how big it is so that we can set the HTTP Cotent-Length
	        // property. (See complete e-mail below for more on this.)

	        FileInputStream fin = new FileInputStream(xmlFile2Send);

	        ByteArrayOutputStream bout = new ByteArrayOutputStream();
	    
	        // Copy the SOAP file to the open connection.
	        copy(fin,bout);
	        fin.close();

	        byte[] b = bout.toByteArray();

	        // Set the appropriate HTTP parameters.
	        httpConn.setRequestProperty( "Content-Length",
	                                     String.valueOf( b.length ) );
	        httpConn.setRequestProperty("Content-Type","text/xml; charset=utf-8");
//	        httpConn.addRequestProperty("Transfer-Encoding", "chunked");
			  httpConn.setRequestProperty("SOAPAction","\""+SOAPAction+"\"");
	        httpConn.setRequestMethod( "POST" );
	        httpConn.setDoOutput(true);
	        httpConn.setDoInput(true);

	        // Everything's set up; send the XML that was read in to b.
	        OutputStream out = httpConn.getOutputStream();
	        out.write( b );    
	        out.close();

	        // Read the response and write it to standard out.
	        
	        InputStream is = httpConn.getInputStream();
					BufferedReader in = null;
	        try{
						InputStreamReader isr = new InputStreamReader(new FileInputStream(String.valueOf(is)), java.nio.charset.StandardCharsets.UTF_8);
//	        BufferedReader in = new BufferedReader(isr);
						in = new BufferedReader(new InputStreamReader(new FileInputStream(String.valueOf(isr)), java.nio.charset.StandardCharsets.UTF_8));

						String inputLine;

						while ((inputLine = in.readLine()) != null)
							System.out.println(inputLine);

					} finally{
	        	if(in != null){
							in.close();
						}
					}


		}

	  // copy method from From E.R. Harold's book "Java I/O"
	  public static void copy(InputStream in, OutputStream out) 
	   throws IOException {

	    // do not allow other threads to read from the
	    // input or write to the output while copying is
	    // taking place

	    synchronized (in) {
	      synchronized (out) {

	        byte[] buffer = new byte[256];
	        while (true) {
	          int bytesRead = in.read(buffer);
	          if (bytesRead == -1) break;
	          out.write(buffer, 0, bytesRead);
	        }
	      }
	    }
	  } 
	}


