/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum CMEfmOamModeTranslation implements TranslatableEnum, com.adva.nlms.mediation.mtosi.v2.utils.translations.f3.TranslatableEnum {
  ACTIVE                   (1, "Active"),
  PASSIVE                  (2, "Passive"),
  NOT_APPLICABLE           (3, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private CMEfmOamModeTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}