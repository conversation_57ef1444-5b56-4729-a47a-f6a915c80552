/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.os;


import jakarta.xml.ws.Holder;
import v1.tmf854.GetOSResponseT;
import v1.tmf854.GetOST;
import v1.tmf854.HeaderT;
import v1.tmf854.OperationsSystemT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;


public class GetOSWorker extends AbstractMtosiWorker {
  protected GetOST mtosiBody;
  protected GetOSResponseT response = new GetOSResponseT();
  protected OperationsSystemT os;

  public GetOSWorker(GetOST mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getOS", "getOS", "getOSResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse () throws Exception {
    // empty method
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
  	//Nothing to see here!
  }

  @Override
  protected void mediate() throws Exception {
    os = OSFactory.getMtosiOS();
  }

  @Override
  protected void response() throws Exception {
    response.setOs(os);
  }

  @Override
  public GetOSResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
