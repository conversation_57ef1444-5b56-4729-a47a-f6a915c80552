/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */
package com.adva.nlms.mediation.mtosi.v2.utils.facility.continuity;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;

public abstract class BaseFlowForContinuityTest<T extends ManagedObjectAttr> {

  protected DTO<T> flowPointDTO;
  protected final int neId;

  public BaseFlowForContinuityTest(int neId, DTO<T> flowPointDTO) {
    this.flowPointDTO = flowPointDTO;
    this.neId = neId;
  }

  public DTO<T> getDto(){
    return flowPointDTO;
  }


  public abstract EntityIndex getEntityIndex();
  public abstract int getCtagControl();
  public abstract int getStagControl();

  public abstract String getPolicerSuffix();
  public abstract <Y extends ManagedObjectAttr> Class<Y> getPolicerClass();
  public abstract <Y extends ManagedObjectAttr> Long getCirValue(DTO<Y> dto);
}
