/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */
package com.adva.nlms.mediation.mtosi.v1.adapter.facade;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.AbstractEthernetTrafficPortAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3AccAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3NetAttr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v2.adapter.facade.ParseException;
import com.adva.nlms.mediation.mtosi.v2.utils.MtosiErrorConstants;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.LayeredParametersT;
import v1.tmf854.NVSListT;
import v1.tmf854.NameAndStringValueT;
import v1.tmf854.TPDataT;
import ws.v1.tmf854.ProcessingFailureException;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MtosiTerminationPointEFMDTOImpl {

  private static Logger logger = LogManager.getLogger(MtosiTerminationPointEFMDTOImpl.class);

  public static final String patternPTP = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=((ACC)||(ACC-\\d+)||(NET-\\d+))";
  public static final String patternACC = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=(ACC||ACC-\\d)";
  public static final String patternNET2 = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=(NET-2)";
  public static final String patternNET = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=(NET)-(\\d+)";
  public static final String patternNET1 = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=(NET-1)";

  public static final String NET_PORT_TYPE = "NET_PORT";
  public static final String ACC_PORT_TYPE = "ACC_PORT";
  public static final String UNKNOWN_PORT_TYPE = "UNKNOWN";

  private MtosiMOFacade mtosiMOFacade = null;
  private NetworkElement networkElement;
  private String meName;
  private String mdName;
  private String tpName;
  private EntityIndex entityIndex;

  protected static final Map<String, String> fieldMap = new HashMap<>();

  public MtosiTerminationPointEFMDTOImpl(MtosiMOFacade mtosiMOFacade) {
    this.mtosiMOFacade = mtosiMOFacade;
  }

  public MtosiTerminationPointEFMDTOImpl() {
  }

//  public DTO validateOnSet() throws ProcessingFailureException {
//    validate();
//    return validateOnSetPTP();
//  }

//  public DTO validateOnSetPTP() throws ProcessingFailureException {
////    DTO dto = getCmPtpDTO();
//    DTO db_dto = getCmDbPtpDTO();
//
//    return db_dto;
//  }

//  private DTO getCmDbPtpDTO() throws ProcessingFailureException {
//    if (NET_PORT_TYPE.equals(getPortType())) {
//      return mtosiMOFacade.findDTOViaMtosiName(getNetworkElement().getID(), createMtosiNameForDB(getNetworkElement(),getTpName()), PortF3NetAttr.class);
//    } else if (ACC_PORT_TYPE.equals(getPortType())) {
//      return mtosiMOFacade.findDTOViaMtosiName(getNetworkElement().getID(), createMtosiNameForDB(getNetworkElement(),getTpName()), PortF3AccAttr.class);
//    } else {
//      throw ServiceUtils.createNewPFE(com.adva.nlms.mediation.mtosi.v2.utils.exceptions.ExceptionUtils.EXCPT_INVALID_INPUT,
//          "Port type cannot be identified for the specified Termination Point.");
//    }
//  }

  public String getPortType() {
    if (getTpName().matches(patternACC)) {
      return ACC_PORT_TYPE;
    } else if (getTpName().matches(patternNET)) {
      return NET_PORT_TYPE;
    }
    return UNKNOWN_PORT_TYPE;
  }

  public String createMtosiNameForDB(NetworkElement ne, String name) {
    int slotIndex = NamingTranslationFactory.slotNumberFromShelfCombo(name);
    if (ManagedElementFactory.needsSlotIncrement(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
      slotIndex = slotIndex + 1;
    }
    String[] parts =  name.split("/");
    String newName = "/"+parts[1]+"/slot="+slotIndex+"/"+parts[3];

    return newName;
  }

  public NetworkElement getNetworkElement() {
    return networkElement;
  }

  public void setNetworkElement(NetworkElement networkElement) {
    this.networkElement = networkElement;
  }

  public String getTpName() {
    return tpName;
  }

  public void setTpName(String tpName) {
    this.tpName = tpName;
  }

  public String getMeName() {
    return meName;
  }

  public void setMeName(String meName) {
    this.meName = meName;
  }

  public String getMdName() {
    return mdName;
  }

  public void setMdName(String mdName) {
    this.mdName = mdName;
  }

  public EntityIndex getEntityIndex() {
    return entityIndex;
  }

  public void setEntityIndex(EntityIndex entityIndex) {
    this.entityIndex = entityIndex;
  }



  public void parse(TPDataT tpData) throws ProcessingFailureException {
    if (tpData != null) {
      LayeredParametersListT layeredParametersListType = tpData.getTransmissionParams().getValue();
      if (layeredParametersListType != null) {
        List<LayeredParametersT> layeredParametersTypeList = layeredParametersListType.getLayeredParameters();
        if (layeredParametersTypeList != null) {
          for (LayeredParametersT layeredParametersType : layeredParametersTypeList) {
            if (layeredParametersType.getLayer() == null) continue;
            String layer = layeredParametersType.getLayer();
            NVSListT nameAndValueStringListType = layeredParametersType.getTransmissionParams();
            if (layer != null && nameAndValueStringListType != null) {
              for (NameAndStringValueT valueType : nameAndValueStringListType.getNvs()) {
                String name = valueType.getName();
                setValue(layer, name, valueType.getValue());
              }
            }
          }
        }
      }
    }
  }

  public void setValue(String layer, String name, String value) throws ProcessingFailureException {
    String fieldFQN = layer + "." + name;
    Method method = null;
    try {
      if (getFieldMap().containsKey(fieldFQN)) {
        method = MtosiTerminationPointEFMDTOImpl.class.getDeclaredMethod(getFieldMap().get(fieldFQN), LayerAttributes.valueOfString(fieldFQN).get_paramType());
        method.invoke(this, value);
      }
    } catch (InvocationTargetException e) {
      if (e.getCause() instanceof ParseException){
        logger.error("Failed to find set " + name + " with value " + value + " with reason:"+ e.getCause().getMessage());
      }else {
        logger.error("Failed to find set " + name + " with value " + value + " with reason:", e);
      }
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.getInvalidOREmptyFieldValueErrorMessage(name));
    } catch (NoSuchMethodException | IllegalAccessException e) {
      logger.error("Failed to find proper setter or set value", e);
    } catch (Exception ex) {
      logger.error("Generic failure to find proper setter or set value", ex);
    }
  }

  protected static Map<String, String> getFieldMap() {
    return fieldMap;
  }

  public void validate() throws ProcessingFailureException {
    if (getMdName() == null || getMdName().isEmpty()) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MD_NAME_MISSING);
    }
    MtosiUtils.validateMD(getMdName());

    if (getMeName() == null || getMeName().isEmpty()) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.ME_NAME_MISSING);
    }

    if (getNetworkElement() == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.ME_NOT_FOUND);
    }
    MtosiUtils.validateNE(getNetworkElement());
    if (getTpName() == null || !getTpName().matches(patternPTP) ) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MISSING_OR_INVALID_TP_NAME);
    }
    if (isPTP()) {
      DTO port = mtosiMOFacade.findDTOViaMtosiName(getNetworkElement().getID(), getTpName(), getCpmrClass(getTpName()));
      if (port == null) {
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
            MtosiErrorConstants.PORT_NOT_FOUND
        );
      }
      setEntityIndex((EntityIndex)port.getValue(AbstractEthernetTrafficPortAttr.ENTITY_INDEX));
    } else {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MISSING_OR_INVALID_TP_NAME);
    }

  }

  public boolean isPTP() {
    return getTpName().matches(patternPTP);
  }

  private Class getCpmrClass(String type) throws ProcessingFailureException {
    if (type.matches(patternNET)) {
      return PortF3NetAttr.class;
    } else if (type.matches(patternACC)) {
      return PortF3AccAttr.class;
    } else {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          "Port type cannot be identified for the specified Termination Point.");
    }
  }
}
