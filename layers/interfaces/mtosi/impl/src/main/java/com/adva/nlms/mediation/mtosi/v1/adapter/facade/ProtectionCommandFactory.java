/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.adapter.facade;

import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.DTOBuilder;
import com.adva.nlms.mediation.config.dto.attr.ProtectionGroupF3Attr;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSwitchActionTranslation;
import ws.v1.tmf854.ProcessingFailureException;

public class ProtectionCommandFactory {

  public static DTO<ProtectionGroupF3Attr> getProtectionGroupDTO(String commandType)
      throws ProcessingFailureException {
    DTO<ProtectionGroupF3Attr> dto = DTOBuilder.getInstance().newDTO(ProtectionGroupF3Attr.class);

    Integer typeInt = CMSwitchActionTranslation.getMIBValue(commandType);
    if (typeInt == CMSwitchActionTranslation.NONE.getMIBValue() ||
        typeInt == CMSwitchActionTranslation.NOT_APPLICABLE.getMIBValue())
    {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.FTP_NOT_FOUND);
    }
    dto.putOrReplace(ProtectionGroupF3Attr.ACTION, typeInt);
    return dto;
  }
}
