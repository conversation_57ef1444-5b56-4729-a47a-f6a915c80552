/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */
package com.adva.nlms.mediation.mtosi.v1.worker.cfm;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.DTOBuilder;
import com.adva.nlms.mediation.config.dto.attr.CfmMepDbAttr;
import com.adva.nlms.mediation.config.dto.attr.MACompAttr;
import com.adva.nlms.mediation.config.dto.attr.MANetAttr;
import com.adva.nlms.mediation.config.dto.attr.MDAttr;
import com.adva.nlms.mediation.config.dto.attr.MEPAttr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3AccAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3NetAttr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp20X.FspGE20XCFMMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import v1.tmf854ext.adva.GetMaintenanceEndpointResponseT;
import v1.tmf854ext.adva.GetMaintenanceEndpointT;
import v1.tmf854ext.adva.RemoteMepListTypeT;
import v1.tmf854ext.adva.RemoteMepTypeT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GetMaintenanceEndpointWorker extends AbstractMtosiWorker {

  private static Map<Integer, String> namePatterns = new HashMap<>();
  private static final String patternPTPForGE = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=((NET-(\\d+))|(ACC))";
  public static final String patternACC = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=(ACC)";
  public static final String patternNET = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=(NET)-(\\d+)";

  static {
    namePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201, patternPTPForGE);
    namePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE, patternPTPForGE);
  }

  private final GetMaintenanceEndpointT mtosiBody;
  private MtosiAddress mtosiAddress;
  protected GetMaintenanceEndpointResponseT response = new GetMaintenanceEndpointResponseT();
  Logger LOG = LogManager.getLogger(this.getClass().getName());
  private MtosiMOFacade facade;
  private DTO<MANetAttr> manet;
  private DTO<MDAttr> md;
  private DTO<MEPAttr> mep;
  private NetworkElement ne;
  private DTO ptp;
  List<DTO<CfmMepDbAttr>> rmepList;

  public GetMaintenanceEndpointWorker(GetMaintenanceEndpointT mtosiBody,  Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getMaintenanceEndpoint", "getMaintenanceEndpoint", "getMaintenanceEndpointResponse");
    this.mtosiBody = mtosiBody;
    this.mtosiHeader = mtosiHeader;
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    validator.validate(mtosiAddress.getNE().getDefaultNetworkElementTypeString());
  }

  @Override
  protected void parse() throws Exception {
    NamingAttributesT meName = mtosiBody.getMepName();

    if(meName == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "MeName has not been specified or is invalid.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    mtosiAddress = new MtosiAddress(meName);

    if(mtosiAddress.getNaming().getMdNm()== null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MD_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    if (!mtosiAddress.getNaming().getMdNm().equals(OSFactory.getMDNm())) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MD_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if(mtosiAddress.getNaming().getMeNm()== null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.ME_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if(mtosiAddress.getNaming().getCfmMdNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MDFR_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if(mtosiAddress.getNaming().getMaNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MAFR_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if(mtosiAddress.getNaming().getMepNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
         "The Maintenance End Point name must be specified in the request.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    MtosiUtils.validateNE(mtosiAddress.getNE());
    ne = mtosiAddress.getNE();
    facade = getMtosiCtrl().getMoFacadeManager().getFacadeViaNeId(MtosiMOFacade.class, ne.getID());
    if(mtosiAddress.getNaming().getPtpNm() == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MDFR_PTP_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if(!mtosiAddress.getNaming().getPtpNm().matches(namePatterns.get(ne.getNetworkElementType()))){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MDFR_PTP_INVALID);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if(!(mtosiAddress.isHasMA())){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MEPFR_MIPFR_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    ptp = facade.findDTOViaMtosiName(ne.getID(), mtosiAddress.getNaming().getPtpNm(), getPtpEntityPerNe(mtosiAddress.getNaming().getPtpNm()));

    if(ptp == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.PTP_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    md = facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+ mtosiAddress.getNaming().getCfmMdNm(), MDAttr.class);
    if( md == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MDFR_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    if( (manet = facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+mtosiAddress.getNaming().getCfmMdNm() +" && /ma="+ mtosiAddress.getNaming().getMaNm(), MANetAttr.class))== null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MANETFR_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if(  facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+mtosiAddress.getNaming().getCfmMdNm() +" && /ma="+ mtosiAddress.getNaming().getMaNm()+ " && " + mtosiAddress.getNaming().getPtpNm() , MACompAttr.class) == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MACOMPFR_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }

  @Override
  protected void mediate() throws Exception {
    mep = facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+mtosiAddress.getNaming().getCfmMdNm() +" && /ma="+ mtosiAddress.getNaming().getMaNm()+ " &&" +
            " " + mtosiAddress.getNaming().getPtpNm() + " && /mep=" + mtosiAddress.getNaming().getMepNm() , MEPAttr.class);
    if(/*mtosiAddress.isHasMepfr() &&*/ mep == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          "The specified Maintenance End Point was not found for the specified Maintenance Association Network.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    mep = facade.refreshDTO(ne.getID(),mep);
    transact();
  }

  private void transact() throws ObjectInUseException, SNMPCommFailure, ProcessingFailureException {
    try {
      facade.openNetTransaction(ne.getID());
//      mepAttrDTO = facade.createObjectOnDevice(ne.getID(), mepAttrDTO);
      DTO<CfmMepDbAttr> dto = DTOBuilder.getInstance().newDTO(CfmMepDbAttr.class);

      dto.putOrReplace(CfmMepDbAttr.ENTITY_INDEX, new EntityIndex(new int [] {manet.getValue(MANetAttr.MD_INDEX),manet.getValue(MANetAttr.MA_INDEX), mep.getValue(MEPAttr.IDENTIFIER)}));
      dto.putOrReplace(CfmMepDbAttr.RMEP_IDENTIFIER, 0);
      dto.putOrReplace(CfmMepDbAttr.RMEP_STATE, 0);
      dto.putOrReplace(CfmMepDbAttr.RMEP_FAILED_OK_TIME, Long.valueOf(0));
      dto.putOrReplace(CfmMepDbAttr.MAC_ADDRESS, "");
      dto.putOrReplace(CfmMepDbAttr.RDI, 0);
      dto.putOrReplace(CfmMepDbAttr.PORT_STATUS_TLV, 0);
      dto.putOrReplace(CfmMepDbAttr.INTERFACE_STATUS_TLV, 0);
//      dto.putOrReplace(CfmMepDbAttr.CHASSIS_ID_SUBTYPE, 0);
//      dto.putOrReplace(CfmMepDbAttr.CHASSIS_ID, "");
      dto.putOrReplace(CfmMepDbAttr.MAN_ADDRESS_DOMAIN, "");
      dto.putOrReplace(CfmMepDbAttr.MAN_ADDRESS, "");

//      rmepList = facade.getObjectFromDevice(ne.getID(), dto);
      rmepList = facade.getObjectFromDevice(ne.getID(), dto);
      ne.logSROperation(SROperationState.SERVICE_CREATION_SUCCESS, mep.getValue(ManagedObjectAttr.MTOSI_NAME));
    } catch (ObjectInUseException | SNMPCommFailure e) {
      ne.logSROperation(SROperationState.SERVICE_CREATION_FAILURE, mep.getValue(ManagedObjectAttr.MTOSI_NAME));
      throw e;
    } finally {
      facade.closeNetTransaction(ne.getID());
    }
  }

  @Override
  protected void response() {
    v1.tmf854ext.adva.ObjectFactory factory = new v1.tmf854ext.adva.ObjectFactory();
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    NamingAttributesT nm = NamingTranslationFactory.createNamingAttributesMaintenanceEndPoint(mtosiAddress.getNaming().getMeNm(), mtosiAddress.getNaming().getCfmMdNm(),
        mtosiAddress.getNaming().getMaNm() + " && " + mtosiAddress.getNaming().getPtpNm(), mtosiAddress.getNaming().getMepNm());
    response.setMepName(factory.createCreateMaintenanceEndpointResponseTMepName(nm));
    response.setDiscoveredName(factory.createCreateMaintenanceDomainResponseTDiscoveredName(mtosiAddress.getNaming().getMepNm()));
    response.setNamingOS(factory.createCreateMaintenanceDomainResponseTNamingOS(OSFactory.getNmsName()));
    response.setSource(factory.createCreateMaintenanceDomainResponseTSource(source));
    response.setTransmissionParams(factory.createCreateMaintenanceDomainResponseTTransmissionParams(new FspGE20XCFMMediator().toMtosiMEP(mep)));

    RemoteMepListTypeT rmepListType = factory.createRemoteMepListTypeT();
    if (rmepList != null) {
      for (DTO<CfmMepDbAttr> rmep : rmepList) {
        RemoteMepTypeT rmepType = factory.createRemoteMepTypeT();
        rmepType.setRmepName(createRMepNameFromMtosiName(mtosiAddress, rmep));
        rmepType.setTransmissionParams(factory.createCreateMaintenanceEndpointResponseTTransmissionParams(new FspGE20XCFMMediator().toMtosiRMEP(rmep, mep)));
        rmepListType.getRMepData().add(rmepType);
      }
    }
    response.setRMepDataList(rmepListType);
  }

  @Override
  public Object getSuccessResponse() {
    if (response == null)
      return null;
    return response;
  }

  private Class getPtpEntityPerNe(String portType) throws ProcessingFailureException{
    if (portType.matches(patternNET)) {
      return PortF3NetAttr.class;
    } else if (portType.matches(patternACC)) {
      return PortF3AccAttr.class;
    } else{
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "Port type cannot be identified for the specified Termination Point.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }


  private static NamingAttributesT createRMepNameFromMtosiName(MtosiAddress mtosiAddress, DTO<CfmMepDbAttr> rmep ){
    NamingAttributesT name = new NamingAttributesT();

    name.setMdNm(mtosiAddress.getNaming().getMdNm());
    name.setMeNm(mtosiAddress.getNaming().getMeNm());
    name.setCfmMdNm(mtosiAddress.getNaming().getCfmMdNm());
    name.setMaNm(mtosiAddress.getNaming().getMaNm() + " && " + mtosiAddress.getNaming().getPtpNm());
    name.setMepNm(mtosiAddress.getNaming().getMepNm());
    name.setRmepNm(String.valueOf(rmep.getValue(CfmMepDbAttr.RMEP_IDENTIFIER)));

    return name;
  }
}
