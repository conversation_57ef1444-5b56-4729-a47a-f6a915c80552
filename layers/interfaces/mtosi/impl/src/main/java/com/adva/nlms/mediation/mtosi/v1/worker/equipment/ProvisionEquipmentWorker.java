/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.equipment;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.ModuleCardSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.NTUSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.NetworkElementSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.module.MTOSICardModuleF3;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.entity.module.ntu.MTOSINTUFSP150CM;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.EquipmentFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiEquipmentMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.EquipmentMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSecondaryStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.EqVendorExtensionsT;
import v1.tmf854.EquipmentHolderT;
import v1.tmf854.EquipmentOrHolderT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.EQCreateDataT;
import v1.tmf854ext.adva.ProvisionEquipmentResponseT;
import v1.tmf854ext.adva.ProvisionEquipmentT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.ws.Holder;

/**
 * main class for the MTOSI operation:  p r o v i s i o n E q u i p m e n t
 */
public class ProvisionEquipmentWorker extends AbstractMtosiWorker {
  Logger LOG = LogManager.getLogger(this.getClass().getName());

  protected ProvisionEquipmentT mtosiBody;
  protected EQCreateDataT createData;
  protected NamingAttributesT holderName;
  protected MtosiAddress mtosiAddr;
  protected EqVendorExtensionsT vendorExtensions;
  protected ProvisionEquipmentResponseT response = new ProvisionEquipmentResponseT();
  protected NetworkElement ne;
  protected ModuleCardSPPropertiesFSP150CM cardProperties;

  public ProvisionEquipmentWorker(ProvisionEquipmentT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "provisionEquipment", "provisionEquipment", "provisionEquipmentResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    if ((createData = mtosiBody.getEquipmentCreatedata()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.getMessageMandatory("equipmentCreatedata"));
    }
    if ((holderName = createData.getEquipmentHolderName()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.getMessageMandatory("equipmentHolderName"));
    }
    if (holderName.getMdNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }
    if (holderName.getMeNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.ME_NAME_MISSING);
    }
    if (holderName.getEhNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.EH_NAME_MISSING);
    }
    if (holderName.getEqNm() != null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The Equipment name cannot be specified in the request.");
    }

    JAXBElement<EqVendorExtensionsT> vendorExt = createData.getVendorExtensions();
    if (vendorExt != null) {
      vendorExtensions = vendorExt.getValue();
    }

    mtosiAddr = new MtosiAddress(holderName);
    ne = ManagedElementFactory.getAndValidateNE(holderName);

  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
   validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  @Override
  protected void mediate() throws Exception {
    if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() != NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.ME_NOT_SUPPORTED);
    }

    if(!MtosiUtils.existsEntity(mtosiAddr)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The specified entity does not exist.");
    }

    EquipmentOrHolderT eqOrHo = EquipmentFactory.getEquipmentOrHolder(mtosiAddr);
    if (eqOrHo == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The requested entity was not found.");
    }
    EquipmentHolderT holder = eqOrHo.getEh();
    if (holder == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The requested entity was not found.");
    }
    boolean isAcceptable = false;

    String objectType = createData.getExpectedEquipmentObjectType();
    if (objectType == null || objectType.length() == 0) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.getMessageMandatory("expectedEquipmentObjectType"));
    }

    NetworkElementSPPropertiesFSP150CM networkElementProperties = null;
    if (objectType.equals(MtosiConstants.EQUIPMENT_NTU_GE) ||
        objectType.equals(MtosiConstants.EQUIPMENT_NTE_GE_SYNC)) {
      if (vendorExtensions != null) {
        cardProperties = MtosiEquipmentMediator.mtosiVendorExtensionsTToNTUProperties(vendorExtensions);
        networkElementProperties = MtosiEquipmentMediator.mtosiVendorExtensionsTToNEProperties(vendorExtensions, objectType);
      } else {
        cardProperties = new NTUSPPropertiesFSP150CM();
      }
      String ehName = holderName.getEhNm();
      int shelfIndex = NamingTranslationFactory.shelfNumberFromShelfCombo(ehName);
      if (shelfIndex == -1) {
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                "Invalid shelf index.");
      }
      cardProperties.set(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex, shelfIndex);
      int slotIndex = NamingTranslationFactory.slotNumberFromShelfCombo(ehName);
      if (slotIndex == -1) {
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                "Invalid slot index.");
      }
      cardProperties.set(EquipmentSPPropertiesFSP150CM.VI.SlotIndex, slotIndex+1);
    } else  {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_UNABLE_TO_COMPLY,
              objectType + " is not supported.");
    }

    if (holder.getAcceptableEquipmentTypeList().getValue().getAcceptableEquipmentType().contains(objectType)) {
      isAcceptable = true;
    }

    if (!isAcceptable) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_UNABLE_TO_COMPLY,
              objectType + " is not acceptable.");
    }

    MTOSICardModuleF3 card = ManagedElementFactory.getModuleCard(mtosiAddr);
    if (card != null) {
      ModuleCardSPPropertiesFSP150CM properties = (ModuleCardSPPropertiesFSP150CM)card.getEquipmentSPProperties();
      if (!properties.get(ModuleCardSPPropertiesFSP150CM.VS.SecondaryState).contains(CMSecondaryStateTranslation.UNASSIGNED.getMtosiString())) {
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_OBJECT_IN_USE,
                "Equipment holder already has an expected equipment provisioned.");
      }
    }

    transact(objectType, networkElementProperties);
  }

  private void transact(final String objectType,NetworkElementSPPropertiesFSP150CM networkElementProperties) throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "ProvisionEquipmentWorker");
    try
    {
      logSecurity(ne, SystemAction.AddNetwork, objectType);
      //NTU Provisioning
      if (objectType.equals(MtosiConstants.EQUIPMENT_NTU_GE)) {
        getFSP150CMMTOSIWorker((NetworkElementFSP150CM)ne).provisionNTU((NTUSPPropertiesFSP150CM) cardProperties);
        if (networkElementProperties.size() != 0) {
          getFSP150CMMTOSIWorker((NetworkElementFSP150CM)ne).provisionCPMR(networkElementProperties,(NTUSPPropertiesFSP150CM) cardProperties);
          String linkName = networkElementProperties.get(NetworkElementSPPropertiesFSP150CM.VS.LinkName);
          if (linkName  != null) {
            getFSP150CMMTOSIWorker((NetworkElementFSP150CM)ne).createLine(linkName, networkElementProperties);
          }
        }
      //NTE Provisioning
      } else if (objectType.equals(MtosiConstants.EQUIPMENT_NTE_GE_SYNC)) {
        getFSP150CMMTOSIWorker((NetworkElementFSP150CM)ne).provisionNTE((NTUSPPropertiesFSP150CM) cardProperties);
      }
      NetTransactionManager.commitNetTransaction(id);
    }
    catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally
    {
      NetTransactionManager.ensureEnd(id);
    }
  }

  @Override
  protected void response() throws Exception {
    MTOSICardModuleF3 card = ManagedElementFactory.getModuleCard(mtosiAddr);
    EquipmentSPPropertiesFSP150CM props = (EquipmentSPPropertiesFSP150CM) card.getEquipmentSPProperties();
    String equipmentType = null;
    String mismatchType = null;
    if (card instanceof MTOSINTUFSP150CM) {
      equipmentType = MtosiConstants.EQUIPMENT_NTU_GE;
      mismatchType = MtosiConstants.EQUIPMENT_NTE_GE_SYNC;
    } else {
      equipmentType = MtosiConstants.EQUIPMENT_NTE_GE_SYNC;
      mismatchType = MtosiConstants.EQUIPMENT_NTU_GE;
    }
    response.setCreatedEquipment(EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipment((NetworkElementFSP150CM)ne, props, equipmentType, mismatchType).getEq());
  }

  @Override
  public ProvisionEquipmentResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }

}
