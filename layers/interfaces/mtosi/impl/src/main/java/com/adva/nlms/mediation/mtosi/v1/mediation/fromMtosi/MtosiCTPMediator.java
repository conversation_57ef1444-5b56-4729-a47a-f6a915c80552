/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi;

import com.adva.nlms.common.NEUtils;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.DTOBuilder;
import com.adva.nlms.mediation.config.dto.attr.FlowF3Attr;
import com.adva.nlms.mediation.config.dto.attr.PortF3NetAttr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMFlowTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSecondaryStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMServiceStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.GE20XFlowTrafficTypeTranslation;
import v1.tmf854.ConnectionTerminationPointT;
import v1.tmf854.DirectionalityT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ResourceStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import ws.v1.tmf854.ProcessingFailureException;

public class MtosiCTPMediator {

  private final MtosiAddress mtosiAddress;
  private final MtosiMOFacade facade;
  private DTO dto;


  public MtosiCTPMediator(MtosiAddress fdfrMtosiAddress, MtosiMOFacade facade, DTO dto) {
    this.mtosiAddress = fdfrMtosiAddress;
    this.facade = facade;
    this.dto = dto;
  }

  public static DTO<FlowF3Attr> getFlowEFMForUpdateDTO(LayeredParametersListT layeredParametersListType) throws ProcessingFailureException {
    DTO<FlowF3Attr> flowF3AttrForUpdate = DTOBuilder.getInstance().newDTO(FlowF3Attr.class);
    if(layeredParametersListType==null)
      return flowF3AttrForUpdate;

    String adminState = LayeredParameterUtils.getLayeredParameter(layeredParametersListType, LayeredParams.PROP_ADVA_ETHERNET,
        LayeredParams.LrPropAdvaEthernet.ADMINISTRATION_CONTROL_PARAM);
    if (adminState != null) {
      Integer adminStateValue = MtosiUtils.getMIBValue(CMAdministrationControlTranslation.NOT_APPLICABLE, adminState);
      if (adminStateValue == CMAdministrationControlTranslation.NOT_APPLICABLE.getMIBValue()) {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.ADMINISTRATION_CONTROL_ILLEGAL_VALUE);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      flowF3AttrForUpdate.putOrReplace(FlowF3Attr.ADMIN_STATE, adminStateValue);
    }
    String flowType = LayeredParameterUtils.getLayeredParameter(layeredParametersListType, LayeredParams.PROP_ADVA_ETHERNET,
        LayeredParams.LrPropAdvaEthernet.FLOW_TYPE_PARAM);
    if (flowType != null && !flowType.equals("PortBased")) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.FLOW_TYPE_ILLEGAL_VALUE);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    return flowF3AttrForUpdate;
  }


  public ConnectionTerminationPointT toMtosiCTP() throws ProcessingFailureException {
    DTO<FlowF3Attr> flow = dto;
    final ObjectFactory objFactory = new ObjectFactory();
    final ConnectionTerminationPointT connectionTerminationPoint = objFactory.createConnectionTerminationPointT();
    boolean isGEDevice = NEUtils.isGEDevice(mtosiAddress.getNeType());
    boolean isVersion41OrHigher = mtosiAddress.getNE().getPersistenceHelper().getMIBVariantFromDB() >= MIBFSP150CM.MibVariant.VER_4_1_1;

    // CTP Name
    String[] parts = flow.getValue(FlowF3Attr.MTOSI_NAME).split("&&");
    final String ctpNm = parts[1].trim();
    mtosiAddress.getNaming().setCtpNm(ctpNm);
    connectionTerminationPoint.setName(objFactory.createConnectionTerminationPointTName(mtosiAddress.getNaming()));

    // discoveredName
    if (ctpNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
    }
    connectionTerminationPoint.setDiscoveredName(objFactory.createConnectionTerminationPointTDiscoveredName(ctpNm));

    // namingOS
    connectionTerminationPoint.setNamingOS(objFactory.createConnectionTerminationPointTNamingOS(OSFactory.getNmsName()));

    // source
    final SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    connectionTerminationPoint.setSource(objFactory.createConnectionTerminationPointTSource(source));

    // resource state
    final ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ResourceStateEnumT.INSTALLED);
    connectionTerminationPoint.setResourceState(objFactory.createConnectionTerminationPointTResourceState(resourceState));

    // direction
    connectionTerminationPoint.setDirection(objFactory.createConnectionTerminationPointTDirection(DirectionalityT.D_BIDIRECTIONAL));

    // layers
    final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
    // Empty layer indicating that this Flow Point supports Ethernet.
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
        LayeredParams.LrPropAdvaEthernet.ADMINISTRATION_CONTROL_PARAM,
        MtosiUtils.getMtosiString(CMAdministrationControlTranslation.NOT_APPLICABLE, flow.getValue(FlowF3Attr.ADMIN_STATE)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
        LayeredParams.LrPropAdvaEthernet.SERVICE_STATE_PARAM, CMServiceStateTranslation.getMtosiString(
            flow.getValue(FlowF3Attr.ADMIN_STATE), flow.getValue(FlowF3Attr.OP_STATE)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
        LayeredParams.LrPropAdvaEthernet.SECONDARY_STATE_PARAM, CMSecondaryStateTranslation.getMtosiString(flow.getValue(FlowF3Attr.SEC_STATE)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
        LayeredParams.LrPropAdvaEthernet.FLOW_TYPE_PARAM,
        MtosiUtils.getMtosiString(CMFlowTypeTranslation.NOT_APPLICABLE, flow.getValue(FlowF3Attr.TYPE)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
        LayeredParams.LrPropAdvaEthernet.MULTI_COS_PARAM, MtosiConstants.FALSE);

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
        LayeredParams.LrPropAdvaEthernet.INGRESS_CIR_PARAM, Long.toString(flow.getValue(FlowF3Attr.A2N_CIR_COMPOSITE)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
        LayeredParams.LrPropAdvaEthernet.INGRESS_EIR_PARAM, Long.toString(flow.getValue(FlowF3Attr.A2N_EIR_COMPOSITE)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
        LayeredParams.LrPropAdvaEthernet.EGRESS_RATE_LIMITING_ENABLED_PARAM,
        MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, flow.getValue(FlowF3Attr.N2A_RATE_LIMITING_ENABLED)));

    if (flow.getValue(FlowF3Attr.N2A_RATE_LIMITING_ENABLED) == MIB.RFC1253.TRUTH_VALUE_TRUE) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
          LayeredParams.LrPropAdvaEthernet.EGRESS_CIR_PARAM, Long.toString(flow.getValue(FlowF3Attr.N2A_CIR_COMPOSITE)));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
          LayeredParams.LrPropAdvaEthernet.EGRESS_EIR_PARAM, Long.toString(flow.getValue(FlowF3Attr.N2A_EIR_COMPOSITE)));
    }
    //New Attributes for GE20X Flows
    //TODONE: CM4.1 is here
    if(isGEDevice || isVersion41OrHigher ) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
          LayeredParams.LrPropAdvaEthernet.TRAFFIC_MANAGEMENT_TYPE_PARAM,
          MtosiUtils.getMtosiString(GE20XFlowTrafficTypeTranslation.NOT_APPLICABLE,(flow.getValue(FlowF3Attr.TRAFFIC_TYPE))));

    }

    connectionTerminationPoint.setTransmissionParams(objFactory.createConnectionTerminationPointTTransmissionParams(layeredParametersListT));
    return connectionTerminationPoint;
  }


  public ConnectionTerminationPointT toMtosiWorkingProtectCTP(String mtosiname, String type, String state) throws ProcessingFailureException {
    DTO<PortF3NetAttr> port = dto;
    final ObjectFactory objFactory = new ObjectFactory();
    final ConnectionTerminationPointT connectionTerminationPoint = objFactory.createConnectionTerminationPointT();

    // CTP Name
    final String ctpNm = port.getValue(PortF3NetAttr.MTOSI_NAME);
    mtosiAddress.getNaming().setCtpNm(ctpNm);
    connectionTerminationPoint.setName(objFactory.createConnectionTerminationPointTName(mtosiAddress.getNaming()));

    // discoveredName
    if (ctpNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
    }
    connectionTerminationPoint.setDiscoveredName(objFactory.createConnectionTerminationPointTDiscoveredName(ctpNm));

    // namingOS
    connectionTerminationPoint.setNamingOS(objFactory.createConnectionTerminationPointTNamingOS(OSFactory.getNmsName()));

    // source
    final SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    connectionTerminationPoint.setSource(objFactory.createConnectionTerminationPointTSource(source));

    // resource state
    final ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ResourceStateEnumT.INSTALLED);
    connectionTerminationPoint.setResourceState(objFactory.createConnectionTerminationPointTResourceState(resourceState));

    // direction
    connectionTerminationPoint.setDirection(objFactory.createConnectionTerminationPointTDirection(DirectionalityT.D_BIDIRECTIONAL));

    // layers
    final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_LAG_FRAGMENT);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_LAG_FRAGMENT,
        LayeredParams.LrLagFragment.LAG_MEMBER_PARAM, mtosiname);
    // -------end of Layer-------

    // -------start Layer--------
//    ProtectionGroupPortF3SPProperties pgPortF3Properties = mtosiWorker.getProtectionGroupPortSPProperties(port.getID());

    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_PROTECTION_FSP150_1PLUS1);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_PROTECTION_FSP150_1PLUS1,
        LayeredParams.LrPropAdvaProtectionFSP1501PLUS1.MEMBER_TYPE_PARAM,type);

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_PROTECTION_FSP150_1PLUS1,
        LayeredParams.LrPropAdvaProtectionFSP1501PLUS1.MEMBER_STATE_PARAM, state);
//        MtosiUtils.getMtosiString(CMLAGPortStateTranslation.NOT_APPLICABLE, port.getValue(PortF3NetAttr.SECONDARY_STATE)));
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
    // Empty layer indicating support for Ethernet.
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET);
    // Empty layer indicating support for ADVA Ethernet.
    // -------end of Layer-------

    connectionTerminationPoint.setTransmissionParams(objFactory.createConnectionTerminationPointTTransmissionParams(layeredParametersListT));
    return connectionTerminationPoint;
  }}
