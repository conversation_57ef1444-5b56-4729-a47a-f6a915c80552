/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.hn4000;

import java.util.Collection;

import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.SNCStateT;
import v1.tmf854ext.adva.FlowDomainFragmentT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrEndIDs;
import com.adva.nlms.mediation.config.hn4000.mtosi.FDFrEndHN4000;
import com.adva.nlms.mediation.config.hn4000.mtosi.FDFrHN4000;
import com.adva.nlms.mediation.config.hn4000.FlowHN4000;
import com.adva.nlms.mediation.config.hn4000.ObjectStateFieldHN4000;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.FDFrTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNEvcTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.ServiceStateTranslation;

/**
 * This class is an FSP 150 CP FDFr MTOSI Translator.
 */
public class FDFrHN4000Translator extends FDFrTranslator {
	private FDFrHN4000 fdfr;

	public FDFrHN4000Translator(FDFrHN4000 fdfr) {
		super(fdfr);
		this.fdfr = fdfr;
	}

	/**
	 * Returns list of corresponding naming attributes of the specific
	 * multiEndPoint.
	 * 
	 * @param frEndHN4000
	 *            The collection of FDFr's end points
	 * @return JAXBElement<NamingAttributesListT>
	 * @throws ws.v1.tmf854.ProcessingFailureException
	 *             When the naming attributes are invalid
	 */
	protected NamingAttributesListT getEndPointNamingAttributesListT(final FDFrEndHN4000 frEndHN4000) throws ProcessingFailureException {
		
		final ObjectFactory objFactory = new ObjectFactory();
		final NamingAttributesListT namingAttributesListT = objFactory.createNamingAttributesListT();
		FlowHN4000 flow = frEndHN4000.getFlow();
		NamingAttributesT namingAttributesT = null;
		if (flow != null) {
			namingAttributesT = NamingTranslationFactory.getNamingAttributes(flow);
			namingAttributesListT.getName().add(namingAttributesT);
		}
		
		return namingAttributesListT;
	}

	/**
	 * Create the Layered Parameters for the HN4000 devices
	 */
	@Override
	protected void createLayeredParameters(final LayeredParametersListT layeredParametersListT) {
//		FDFrSPPropertiesHN4000 props = (FDFrSPPropertiesHN4000)fdfr.getFDFrSPProperties();
		FDFrSPPropertiesHN4000 properties = (FDFrSPPropertiesHN4000)fdfr.getFDFrSPProperties();
		// -------start Layer--------
		LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
		// -------end of Layer-------
		// -------start PROP_HATTERAS_Ethernet layer ----------------
		LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet);
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasEthernet.ADMINISTRATION_CONTROL_PARAM,
				properties.get(FDFrSPProperties.VI.AdministrativeState) == null? "null" : MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, properties.get(FDFrSPProperties.VI.AdministrativeState)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasEthernet.SERVICE_STATE_PARAM,
				ServiceStateTranslation.getMtosiString(properties.get(FDFrSPProperties.VI.AdministrativeState) != null? properties.get(FDFrSPProperties.VI.AdministrativeState) :-1, properties.get(FDFrSPPropertiesHN4000.VI.ServiceState) != null?properties.get(FDFrSPPropertiesHN4000.VI.ServiceState):-1));
		Integer secondaryState = properties.get(FDFrSPPropertiesHN4000.VI.SecondaryState);
		if (secondaryState != null) 
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasEthernet.SECONDARY_STATE,
					ObjectStateFieldHN4000.getSecondaryStateString(secondaryState));
		else 
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasEthernet.SECONDARY_STATE,
					"");
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasEthernet.TYPE_PARAM,
				HNEvcTypeTranslation.getMtosiString(properties.get(FDFrSPPropertiesHN4000.VI.Type)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasEthernet.NUM_ACTIVE_UNIS_PARAM, 
				String.valueOf(properties.get(FDFrSPPropertiesHN4000.VI.ActiveUnis)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasEthernet.NUM_TOTAL_UNIS_PARAM, 
				String.valueOf(properties.get(FDFrSPPropertiesHN4000.VI.TotalUnis)));
		// -------end of Layer-------

	}
  	/**
  	 * 
  	 * use the AEnd and zEnd on the FDFr to name the End points.
  	 * @param objFactoryEx
  	 * @param flowDomainFragmentT
  	 * @param fdfrSPProperties
  	 * @throws ProcessingFailureException
  	 */
	@Override
	protected void createFDFrEndPoints(final v1.tmf854ext.adva.ObjectFactory objFactoryEx, final FlowDomainFragmentT flowDomainFragmentT,
			FDFrSPProperties fdfrSPProperties) throws ProcessingFailureException {
		// aEnd
	    flowDomainFragmentT.setAEnd(objFactoryEx.createFlowDomainFragmentTAEnd(getEndPointNamingAttributesListT(fdfr.getAEnd())));
	
	    // zEnd
	    flowDomainFragmentT.setZEnd(objFactoryEx.createFlowDomainFragmentTZEnd(getEndPointNamingAttributesListT(fdfr.getZEnd())));
	}

	@Override
	protected NamingAttributesListT getEndPointNamingAttributesListT(Collection<FDFrEndIDs> endPointsCollection) throws ProcessingFailureException {
		//Since I am overriding createFDFrEndPoints, this method is not needed.
		return null;
	}

	@Override
  protected SNCStateT getFDFrState() {
		if (fdfr.getAEnd() == null ||  fdfr.getAEnd().getFlow() == null || fdfr.getZEnd() == null || fdfr.getZEnd().getFlow() == null) {
			return SNCStateT.SNCS_PARTIAL;
		} else {
			return SNCStateT.SNCS_ACTIVE;
		}
	}



}
