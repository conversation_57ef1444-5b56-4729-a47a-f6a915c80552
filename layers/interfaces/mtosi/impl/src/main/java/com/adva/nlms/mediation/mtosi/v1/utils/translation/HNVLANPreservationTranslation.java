/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;

/**
HnVlanPreservationValue ::= TEXTUAL-CONVENTION
     STATUS      current
     DESCRIPTION "Enumerated list of possible VLAN tag preservation values.
                 If 'on' then always preserve VLAN IDs on the EVC Binding.
                 If 'auto' then only preserve VLAN IDs if the EVC Binding
                 is configured to handle unknown VLAN IDs or a VLAN list
                 consisting of more than one VLAN ID. If 'off' then do not
                 preserve VLAN IDs on the EVC Binding. Cannot be set to
                 'off' if the EVC Binding is configured to handle unknown
                 VLAN IDs or a VLAN list consisting of more than one VLAN
                 ID."
     SYNTAX      INTEGER {
                     auto          (1),
                     on            (2),
                     off           (3)
                 }
  */
public enum HNVLANPreservationTranslation {
    Auto    	      (1),
    Enabled           (2),
    Disabled          (3);
    
    private int mibValue;
    
    private int getMibValue() {
		return mibValue;
	}

	private HNVLANPreservationTranslation(int code) {
    	this.mibValue = code;
    }

    public static String getMtosiString(final Integer mibValue) {
    	if (mibValue == null) {
    		return MtosiConstants.NOT_APPLICABLE;
    	}
    	for (HNVLANPreservationTranslation value: values() ) {
    		if (value.getMibValue() == mibValue.intValue()) {
    			return value.name(); 
    		}
    	}
    	//the value was not found, return the value passed in
    	return String.valueOf(mibValue);
    }
    
    public static int getMibValue(final String name) {
    	for (HNVLANPreservationTranslation value: values() ) {
    		if (value.name().equals(name)) {
    			return value.getMibValue(); 
    		}
    	}
    	//Returning -1, the value passed in was not found.
    	return -1;
    }
}
