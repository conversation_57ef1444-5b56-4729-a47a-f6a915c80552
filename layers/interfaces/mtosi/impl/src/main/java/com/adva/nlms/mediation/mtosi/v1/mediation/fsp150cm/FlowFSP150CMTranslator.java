/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm;

import com.adva.nlms.common.NEUtils;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.common.snmp.f3.TagControlEnum;
import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesFSP150CM;
import com.adva.nlms.mediation.config.Module;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3Impl;
import com.adva.nlms.mediation.config.f3.mtosi.NetworkElementMTOSIWorkerF3Impl;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.*;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.*;
import v1.tmf854.*;
import ws.v1.tmf854.ProcessingFailureException;

/**
 * This class is an FSP 150 CM flow MTOSI Translator.
 */
public class FlowFSP150CMTranslator extends MtosiTranslator {
  private MTOSIFlowF3 flow;

  public FlowFSP150CMTranslator(MTOSIFlowF3 flow) {
    this.flow = flow;
  }

  @Override
  public ConnectionTerminationPointT toMtosiCTP() throws ProcessingFailureException {
    final ObjectFactory objFactory = new ObjectFactory();
    final ConnectionTerminationPointT connectionTerminationPoint = objFactory.createConnectionTerminationPointT();
    final FlowSPPropertiesFSP150CM flowSPProperties = flow.getFlowSPProperties();
    NetworkElement ne = this.flow.getPortFSP150CMAcc().getNE();
    boolean isGEDevice = NEUtils.isGEDevice(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) ;
    boolean isVersion41OrHigher = ne.getPersistenceHelper().getMIBVariantFromDB() >= MIBFSP150CM.MibVariant.VER_4_1_1;
    boolean isNTEPort = isNteModule();

    // CTP Name
    final NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributes(flow);
    connectionTerminationPoint.setName(objFactory.createConnectionTerminationPointTName(namingAttributes));

    // discoveredName
    final String ctpNm = namingAttributes.getCtpNm();
    if (ctpNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
    }
    connectionTerminationPoint.setDiscoveredName(objFactory.createConnectionTerminationPointTDiscoveredName(ctpNm));

    // namingOS
    connectionTerminationPoint.setNamingOS(objFactory.createConnectionTerminationPointTNamingOS(OSFactory.getNmsName()));

    // source
    final SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    connectionTerminationPoint.setSource(objFactory.createConnectionTerminationPointTSource(source));

    // resource state
    final ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ResourceStateEnumT.INSTALLED);
    connectionTerminationPoint.setResourceState(objFactory.createConnectionTerminationPointTResourceState(resourceState));

    // direction
    connectionTerminationPoint.setDirection(objFactory.createConnectionTerminationPointTDirection(DirectionalityT.D_BIDIRECTIONAL));

    // layers
    final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
    // Empty layer indicating that this Flow Point supports Ethernet.
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.ADMINISTRATION_CONTROL_PARAM,
            MtosiUtils.getMtosiString(CMAdministrationControlTranslation.NOT_APPLICABLE, flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.AdminState)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.SERVICE_STATE_PARAM, CMServiceStateTranslation.getMtosiString(
            flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.AdminState), flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.OperationalState)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.SECONDARY_STATE_PARAM, flowSPProperties.get(FlowSPPropertiesFSP150CM.VS.SecondaryState));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.FLOW_TYPE_PARAM,
            MtosiUtils.getMtosiString(CMFlowTypeTranslation.NOT_APPLICABLE, flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.Type)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.MULTI_COS_PARAM, MtosiConstants.FALSE);

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.INGRESS_CIR_PARAM, Long.toString(flowSPProperties.get(FlowSPPropertiesFSP150CM.VL.A2NCIR)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.INGRESS_EIR_PARAM, Long.toString(flowSPProperties.get(FlowSPPropertiesFSP150CM.VL.A2NEIR)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.EGRESS_RATE_LIMITING_ENABLED_PARAM,
            MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.N2ARateLimitingEnabled)));

    if (flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.N2ARateLimitingEnabled) == MIB.RFC1253.TRUTH_VALUE_TRUE) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
              LayeredParams.LrPropAdvaEthernet.EGRESS_CIR_PARAM, Long.toString(flowSPProperties.get(FlowSPPropertiesFSP150CM.VL.N2ACIR)));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
              LayeredParams.LrPropAdvaEthernet.EGRESS_EIR_PARAM, Long.toString(flowSPProperties.get(FlowSPPropertiesFSP150CM.VL.N2AEIR)));
    }
    //New Attributes for GE20X Flows
    //TODONE: CM4.1 is here 
    if(isGEDevice || isVersion41OrHigher ) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
          LayeredParams.LrPropAdvaEthernet.TRAFFIC_MANAGEMENT_TYPE_PARAM, 
          MtosiUtils.getMtosiString(GE20XFlowTrafficTypeTranslation.NOT_APPLICABLE,(flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.TrafficType)))); 

    }
    if(isGEDevice || isNTEPort ) {

        Integer cTagControl = flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.CtagControl);
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.CTAG_CONTROL_PARAM,
                TagControlEnum.findByMibValue(cTagControl, TagControlEnum.NOT_APPLICABLE).getMtosiString());

        if (cTagControl.intValue() != TagControlEnum.NONE.getMIBValue()) {
        	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.CTAG_VLAN_ID_PARAM, 
                String.valueOf(flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.CtagVlanID)));

        	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.CTAG_VLAN_PRIORITY_PARAM, 
                String.valueOf(flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.CtagVlanPrio)));
        }

        if (cTagControl.intValue() == TagControlEnum.PUSH.getMIBValue()) {
        	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.CTAG_MATCH_RX_PRIORITY_PARAM, 
                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE,
                		flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.CtagMatchRxPrio)));
        }
        
        Integer sTagControl = flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.StagControl);
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.STAG_CONTROL_PARAM, 
                TagControlEnum.findByMibValue(sTagControl, TagControlEnum.NOT_APPLICABLE).getMtosiString());

        if (sTagControl.intValue() != TagControlEnum.NONE.getMIBValue()) {
        	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.STAG_VLAN_ID_PARAM, 
                String.valueOf(flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.StagVlanID)));

        	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.STAG_VLAN_PRIORITY_PARAM, 
                String.valueOf(flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.StagVlanPrio)));
        }
    }
    if (isNTEPort) {
      Integer sTagControl = flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.StagControl);
      if (sTagControl.intValue() == TagControlEnum.PUSH.getMIBValue()) {
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
              LayeredParams.LrPropAdvaEthernet.STAG_MATCH_RX_PRIORITY_PARAM, 
              "False");
//              MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE,
//                  flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.StagMatchRxPrio)));
      }
    }

    if (isGEDevice) {
        
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.ES_FRAMES_LOSS_THRESHOLD_PARAM, 
                String.valueOf(flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.ESFramesLossThreshold)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.SES_FRAMES_LOSS_THRESHOLD_PARAM, 
                String.valueOf(flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.SESFramesLossThresholdRatio)));

        if (flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.FlowPolicingEnabled) != null) {
	        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
	                LayeredParams.LrPropAdvaEthernet.FLOW_POLICING_ENABLED_PARAM, 
	                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE,
	                		flowSPProperties.get(FlowSPPropertiesFSP150CM.VI.FlowPolicingEnabled)));
        }
        // -------end of Layer-------

//          // -------end of Layer-------
//        }
    }
//    // -------end of Layer-------

    connectionTerminationPoint.setTransmissionParams(objFactory.createConnectionTerminationPointTTransmissionParams(layeredParametersListT));
    return connectionTerminationPoint;
  }

  private boolean isNteModule() {
    FlowSPPropertiesFSP150CM props = flow.getFlowSPProperties();
    int shelfIndex = props.get(FlowSPPropertiesFSP150CM.VI.ShelfIndex);
    int slotIndex = props.get(FlowSPPropertiesFSP150CM.VI.SlotIndex);
    NetworkElement ne = flow.getPortFSP150CMAcc().getNE();
    Module module = ((NetworkElementMTOSIWorkerF3Impl) ne.getMTOSIWorker()).getModule(shelfIndex, slotIndex);
    if (module instanceof NteF3Impl)
      return true;
    return false;
  }
  
}
