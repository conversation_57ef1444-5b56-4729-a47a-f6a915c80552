/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.os;

import jakarta.xml.ws.Holder;
import v1.tmf854.CommunicationPatternT;
import v1.tmf854.GetAllOSsResponseT;
import v1.tmf854.GetAllOSsT;
import v1.tmf854.HeaderT;
import v1.tmf854.OperationsSystemListT;
import v1.tmf854.ObjectFactory;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;


public class GetAllOSsWorker extends AbstractMtosiWorker {
  protected GetAllOSsT mtosiBody;
  protected GetAllOSsResponseT response = new GetAllOSsResponseT();
  protected OperationsSystemListT osList;

  public GetAllOSsWorker(GetAllOSsT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getAllOSs", "getAllOSs", "getAllOSsResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  public void validateRequestHeader() throws Exception {
    super.validateRequestHeader();

    /* We do not support iterators for GetAllOSs. */
    if (this.getMtosiHeader().value.getCommunicationPattern() != CommunicationPatternT.SIMPLE_RESPONSE) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_NOT_IMPLEMENTED,
              "Unsupported communicationPattern requested.");
    }
  }

  @Override
  protected void parse () throws Exception {
    // empty method
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
  	//No validation required.
  }

  @Override
  protected void mediate() throws Exception {
    ObjectFactory objFactory = new ObjectFactory();
    osList = objFactory.createOperationsSystemListT();
    osList.getOs().add(OSFactory.getMtosiOS());
  }

  @Override
  protected void response() throws Exception {
    response.setOsList(osList);
  }

  @Override
  public GetAllOSsResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
