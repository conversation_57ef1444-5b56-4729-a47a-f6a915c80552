/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.tcProfile;


import jakarta.xml.ws.Holder;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.GetTCProfileResponseT;
import v1.tmf854ext.adva.GetTCProfileT;
import v1.tmf854ext.adva.TCProfileT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;


public class GetTCProfileWorker extends AbstractMtosiWorker {
  protected GetTCProfileT mtosiBody;
  protected GetTCProfileResponseT response = new GetTCProfileResponseT();
  protected NamingAttributesT tcProfileName;
  protected TCProfileT tcProfileT;
private NetworkElement ne;

  public GetTCProfileWorker (final GetTCProfileT mtosiBody, final Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getTCProfile", "getTCProfile", "getTCProfileResponseT");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    if ((tcProfileName = mtosiBody.getTcProfileName()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              MtosiErrorConstants.INVALID_FILTER);
    }

    if (!NamingTranslationFactory.isManagedElement(tcProfileName)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.ME_NAME_MISSING);
    }

    if (!tcProfileName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MD_NOT_FOUND);
    }
	ne = ManagedElementFactory.getAndValidateNE(tcProfileName);
	  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  @Override
  protected void mediate() throws Exception {
    tcProfileT = ManagedElementFactory.getTCProfile(tcProfileName, ne);
  }

  @Override
  protected void response() throws Exception {
    response.setTcProfile(tcProfileT);
  }

  @Override
  public GetTCProfileResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
