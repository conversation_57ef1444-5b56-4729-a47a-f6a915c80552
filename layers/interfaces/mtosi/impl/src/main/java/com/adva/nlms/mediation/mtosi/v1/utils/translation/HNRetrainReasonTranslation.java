/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;
 /**
  *                 unknown             (1),
                    none                (2),
                    gsLossOfFraming     (3),
                    upSnrMargin         (4),
                    downSnrMargin       (5),
                    spanProfileChange   (6),
                    gsCrcErrors10e5     (7),
                    gsCrcErrors10e7     (8),
                    gsActivateTimeout   (9),
                    gsDriver           (10),
                    gsEocTimeout       (11),
                    adminChange        (12),
                    createBonding      (13),
                    deleteBonding      (14),
                    configBonding      (15),
                    autoBonding        (16),
                    hnbdpRemove        (17),
                    hnbdpStartTimeout  (18),
                    hnbdpTimeout       (19),
                    gsAbortRetry       (20),
                    gsActivateRetry    (21),
                    gs6465LockError    (22),
                    pmeFsm             (23),
                    failover           (24),
                    gsHandshakeFailed  (25),
                    gsHsModeOrAnnex    (26),
                    linkLossForward    (27),
                    dgpProbeRetrain    (28),
                    dgpConfigChange    (29)

  */
public enum HNRetrainReasonTranslation {
    Unknown             (1),
    None                (2),
    GsLossOfFraming     (3),
    UpSnrMargin         (4),
    DownSnrMargin       (5),
    SpanProfileChange   (6),
    GsCrcErrors10e5     (7),
    GsCrcErrors10e7     (8),
    GsActivateTimeout   (9),
    GsDriver           (10),
    GsEocTimeout       (11),
    AdminChange        (12),
    CreateBonding      (13),
    DeleteBonding      (14),
    ConfigBonding      (15),
    AutoBonding        (16),
    HnbdpRemove        (17),
    HnbdpStartTimeout  (18),
    HnbdpTimeout       (19),
    GsAbortRetry       (20),
    GsActivateRetry    (21),
    Gs6465LockError    (22),
    PmeFsm             (23),
    Failover           (24),
    GsHandshakeFailed  (25),
    GsHsModeOrAnnex    (26),
    LinkLossForward    (27),
    DgpProbeRetrain    (28),
    DgpConfigChange    (29);
    
    private int mibValue;
    
    private int getMibValue() {
		return mibValue;
	}

	private HNRetrainReasonTranslation(int code) {
    	this.mibValue = code;
    }

    public static String getMtosiString(final Integer mibValue) {
    	if (mibValue == null) {
    		return "null";
    	}
    	for (HNRetrainReasonTranslation value: values() ) {
    		if (value.getMibValue() == mibValue.intValue()) {
    			return value.name(); 
    		}
    	}
    	//Probably should throw something...
    	return String.valueOf(mibValue);
    }
    
    public static int getMibValue(final String name) {
    	for (HNRetrainReasonTranslation value: values() ) {
    		if (value.name().equals(name)) {
    			return value.getMibValue(); 
    		}
    	}
    	//Probably should throw something...
    	return -1;
    }
}