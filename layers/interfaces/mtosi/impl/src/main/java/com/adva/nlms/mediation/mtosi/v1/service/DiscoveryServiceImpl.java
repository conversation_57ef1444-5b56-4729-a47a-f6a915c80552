/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;


import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.discovery.GetAllCapabilitiesWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import ws.v1.tmf854.DiscoveryService;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.annotation.Resource;
import jakarta.xml.ws.WebServiceContext;


/**
 * This class was generated by the Celtix 1.1-SNAPSHOT
 * Fri Dec 22 10:48:24 EST 2006
 * Generated source version: 1.1-SNAPSHOT
 *
 */

@jakarta.jws.WebService(name = "DiscoveryService", serviceName = "DiscoveryService", portName = "DiscoveryServiceHttp", targetNamespace = "tmf854.v1.ws")

public class DiscoveryServiceImpl implements DiscoveryService {
  Logger LOG = LogManager.getLogger(this.getClass().getName());
  @Resource
  private WebServiceContext context;

  /* (non-Javadoc)
	 * @see ws.v1.tmf854.DiscoveryService#getAllCapabilities(v1.tmf854.GetAllCapabilitiesT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
	 */
  public v1.tmf854.GetAllCapabilitiesResponseT getAllCapabilities(
          v1.tmf854.GetAllCapabilitiesT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllCapabilitiesWorker.class, mtosiBody, mtosiHeader,
            "getAllCapabilities", context, LOG);
  }

  @Override
  public v1.tmf854.GetAllCapabilitiesResponseT getAllCapabilities(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllCapabilitiesT mtosiBody) throws ProcessingFailureException {
    return getAllCapabilities(mtosiBody, mtosiHeader);
  }
}
