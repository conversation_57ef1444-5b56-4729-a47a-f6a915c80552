/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FTPSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ServiceSPPropertiesFSP150CP;
import com.adva.nlms.mediation.common.serviceProvisioning.WANPortSPPropertiesFSP150CP;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.NetworkElementFSP150CP;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.PortFSP150CPAccess;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.PortFSP150CPNetwork;
import com.adva.nlms.mediation.config.mtosi.FDFr;
import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiFDFrMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.TPDataListT;
import v1.tmf854.TPDataT;
import v1.tmf854ext.adva.ModifyFDFrT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public class ModifyFDFrWorkerCP extends ModifyFDFrWorker
{
  private PortFSP150CPAccess portACC;
	private FTP ftp;
	private PortFSP150CPNetwork portNETA;
	private PortFSP150CPNetwork portNETB;
	private TPDataT tpDataAccess;
	private TPDataT tpDataNetworkA;
	private TPDataT tpDataNetworkB;
	private TPDataT tpDataFtp;

  public ModifyFDFrWorkerCP (ModifyFDFrT mtosiBody, Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, FDFr fdfr, NetworkElement ne)
  {
    super(mtosiBody, mtosiHeader, namingAttributes, fdfr, ne);
  }

  @Override
  protected void mediate() throws Exception {
    ServiceSPPropertiesFSP150CP propsACC = null;
    WANPortSPPropertiesFSP150CP propsNETA = null;
    WANPortSPPropertiesFSP150CP propsNETB = null;
    FTPSPProperties propsFTP = null;

    if (tpsToModify != null) {
      if (!MtosiTPMediator.checkTPToModifySameNE(ne, tpsToModify)) {
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                "The specified TPs must be on the same Network Element.");
      }

      tpDataAccess = MtosiTPMediator.getTPDataTForCPAccess(tpsToModify);
      tpDataNetworkA = MtosiTPMediator.getTPDataTForCPNetworkA(tpsToModify);
      tpDataNetworkB = MtosiTPMediator.getTPDataTForCPNetworkB(tpsToModify);
      tpDataFtp = MtosiTPMediator.getTPDataTForFTP(tpsToModify);
    }
    if (tpDataAccess != null) {
      Port portAccess = ManagedElementFactory.getPort(tpDataAccess.getTpName());
      portACC = (PortFSP150CPAccess) portAccess;
      propsACC = MtosiTPMediator.mtosiTPDataTToCPAccessProperties(tpDataAccess, portACC);
    }
    if (tpDataNetworkA != null) {
      Port portA = ManagedElementFactory.getPort(tpDataNetworkA.getTpName());
      portNETA = (PortFSP150CPNetwork) portA;
      propsNETA = MtosiTPMediator.mtosiTPDataTToCPNetworkProperties(tpDataNetworkA, portNETA);
    }
    if (tpDataNetworkB != null) {
      Port portB = ManagedElementFactory.getPort(tpDataNetworkB.getTpName());
      portNETB = (PortFSP150CPNetwork) portB;
      propsNETB = MtosiTPMediator.mtosiTPDataTToCPNetworkProperties(tpDataNetworkB, portNETB);
    }
    if (tpDataFtp != null) {
      ftp = ManagedElementFactory.getFtp(tpDataFtp.getTpName());
      propsFTP = MtosiTPMediator.mtosiTPDataTToCPFTPProperties(tpDataFtp, ftp);
    }

    NetworkElementFSP150CP cpNE = (NetworkElementFSP150CP) ne;
    String fdfrName = namingAttributes.getFdfrNm();
    FDFrSPProperties propsFDFr = MtosiFDFrMediator.mtosiModifyFDFRToFDFRSPProperties(fdfrName, modifyData);
    transact(cpNE, propsACC, propsNETA, propsNETB, propsFTP, propsFDFr);
  }

  private void transact(NetworkElementFSP150CP ne, ServiceSPPropertiesFSP150CP propsACC, WANPortSPPropertiesFSP150CP propsNETA,
                        WANPortSPPropertiesFSP150CP propsNETB, FTPSPProperties propsFTP, FDFrSPProperties fdfrProps)
          throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "ModifyFDFrWorker");
    if(fdfrProps != null){
      ne.getMTOSIWorker().setFDFrOperationInProgress(fdfr.getFDFrSPProperties().get(FDFrSPProperties.VS.FDFrName), true);
    }
    try {
      if (fdfrProps != null) {
    	  logSecurity(ne, SystemAction.ModifyNetwork, fdfr.getFDFrSPProperties().get(FDFrSPProperties.VS.FDFrName));
    	  fdfr.setFDFrSPProperties(fdfrProps);
      }
      if (propsACC != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, portACC.getMtosiName());
        portACC.setSettings(propsACC);
      }
      if (propsNETA != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, portNETA.getMtosiName());
        portNETA.setSettings(propsNETA);
      }
      if (propsNETB != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, portNETB.getMtosiName());
        portNETB.setSettings(propsNETB);
      }
      if (propsFTP != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, "ftpNm=" + ftp.getFTPName());
        ftp.setFTPSPProperties(propsFTP);
      }
      NetTransactionManager.commitNetTransaction(id);
    }
    catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally
    {
      if(fdfrProps != null){
        ne.getMTOSIWorker().setFDFrOperationInProgress(fdfr.getFDFrSPProperties().get(FDFrSPProperties.VS.FDFrName), false);
      }
      NetTransactionManager.ensureEnd(id);
    }
  }

  @Override
  protected TPDataListT getUpdatedTPs() throws ProcessingFailureException {
    // Get updated objects
    ObjectFactory factory = new ObjectFactory();
    TPDataListT tpsToModify = factory.createTPDataListT();
    if (tpDataAccess != null && portACC != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataAccess.getTpName())).toMtosiPTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataAccess.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
    }
    if (tpDataNetworkA != null && portNETA != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataNetworkA.getTpName())).toMtosiPTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataNetworkA.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
    }
    if (tpDataNetworkB != null && portNETB != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataNetworkB.getTpName())).toMtosiPTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataNetworkB.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
    }
    if (tpDataFtp != null && ftp != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ManagedElementFactory.getFtp(tpDataFtp.getTpName())).toMtosiFTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getFtp(tpDataFtp.getTpName()).getMtosiTranslator().toMtosiFTPasTPDataT());
    }
    return tpsToModify;
  }
}
