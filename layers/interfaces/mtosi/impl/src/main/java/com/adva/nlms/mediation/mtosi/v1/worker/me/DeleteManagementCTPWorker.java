/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */
package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.mediation.common.MDOperationNotSupportedException;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.common.remotetypectp.RemoteTypeDAOImpl;
import com.adva.nlms.mediation.mtosi.common.remotetypectp.RemoteTypeDBImpl;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.DeleteManagementCTPResponseT;
import v1.tmf854ext.adva.DeleteManagementCTPT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.HashMap;
import java.util.List;

public class DeleteManagementCTPWorker extends AbstractMtosiWorker {
  Logger LOG = LogManager.getLogger(DeleteManagementCTPWorker.class);

  protected DeleteManagementCTPResponseT response = new DeleteManagementCTPResponseT();
  protected DeleteManagementCTPT mtosiBody;
  protected NamingAttributesT namingAttributes;
  protected String ptpName;
  protected String ctpName;
  protected NetworkElement networkElement;


  public DeleteManagementCTPWorker(DeleteManagementCTPT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "deleteManagementCTP", "deleteManagementCTP", "deleteManagementCTPResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    validator.validate(networkElement.getDefaultNetworkElementTypeString());
  }

  @Override
  protected void parse() throws Exception {
    namingAttributes=mtosiBody.getPtpName();
    if (namingAttributes == null || (ptpName = namingAttributes.getPtpNm()) == null ) {
      LOG.debug("namingAttributes == null");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.PTP_NAME_MISSING);

    }
    if (!NamingTranslationFactory.isManagementDomain(namingAttributes)) {
      LOG.debug("NamingTranslationFactory.isManagementDomain(namingAttributes)=>false");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (namingAttributes.getMeNm()==null) {
      LOG.debug("namingAttributesT.getMeNm()==null");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.ME_NAME_MISSING);
    }

    if (!namingAttributes.getMdNm().equals(OSFactory.getMDNm())) {
      LOG.debug("namingAttributes.getMdNm().equals(OSFactory.getMDNm())=>false");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MD_NOT_FOUND);
    }

    networkElement = this.getMtosiCtrl().getLegacyMtosiMOFacade().getNEByName(namingAttributes.getMeNm());
    MtosiUtils.validateNE(networkElement);

    if (!NamingTranslationFactory.isPtpNameValid(ptpName)) {
      LOG.debug("namingAttributes.getPtpNm() invalid");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.PTP_NAME_NOT_VALID);
    }

    if ((ctpName = namingAttributes.getCtpNm()) == null || ctpName.isEmpty()) {
      LOG.debug("namingAttributes.getCtpNm() invalid");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.CTP_NAME_MISSING);
    }

  }

  @Override
  protected void mediate() throws Exception {
    int shelfNum = NamingTranslationFactory.shelfNumberFromShelfCombo(ptpName);
    if (shelfNum == -1) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "Invalid shelf index.");
    }
    int slotNum = NamingTranslationFactory.slotNumberFromShelfCombo(ptpName);
    if (slotNum == -1) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "Invalid slot index.");
    }
    String portName = NamingTranslationFactory.portNameFromShelfCombo(ptpName);
    int portType = MtosiUtils.getPortTypeTunnel(portName);
    if (portType == -1) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.PTP_PORT_TYPE_WRONG);
    }
    int portIndex = MtosiUtils.getPortIndexTunnel(portName);
    if (portIndex == -1) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.PTP_PORT_INDEX_WRONG);
    }

    NetworkElement locks[] = new NetworkElement[] {networkElement};
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "DeleteManagementCTPWorker");
    try
    {
      this.getMtosiCtrl().getLegacyMtosiMOFacade().deleteManagementCTP(networkElement.getID(),
              shelfNum, slotNum, portType, portIndex, ctpName);
      NetTransactionManager.commitNetTransaction(id);
    } catch (SNMPCommFailure | SPValidationException | MDOperationNotSupportedException | NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      NetTransactionManager.ensureEnd(id);
    }

  }

  @Override
  protected void response() throws Exception {
    //empty
     removePersist();
  }

  private boolean removePersist(){
      RemoteTypeDAOImpl remoteDAOImpl = new RemoteTypeDAOImpl();
      try{
          RemoteTypeDBImpl entity = remoteDAOImpl.getRemoteType(namingAttributes.getMeNm(), namingAttributes.getCtpNm(), namingAttributes.getPtpNm());
          RemoteTypeDAOImpl rem = new RemoteTypeDAOImpl();
          if(entity != null){
              rem.deleteMtosiTunnelPK(entity);
          }else{
              updateCtpPropsResponse(networkElement.getIPAddress(), namingAttributes.getMeNm(),namingAttributes.getPtpNm(),namingAttributes.getCtpNm());
              entity = remoteDAOImpl.getRemoteType(namingAttributes.getMeNm(), namingAttributes.getCtpNm(), namingAttributes.getPtpNm());
              if(entity != null){
                  rem.deleteMtosiTunnelPK(entity);
              }
          }
          return true;
      }catch(Exception ex){
          LOG.debug("Failed to persist tunnel data " + ex.getMessage());
          return false;
      }
  }

    private boolean updateCtpPropsResponse(String ipAddress, String meNm, String mtosiName, String tunnelName){
        RemoteTypeDAOImpl remoteDAOImpl = new RemoteTypeDAOImpl();
        try{
            HashMap<String, List<RemoteTypeDBImpl>> remoteTypes = remoteDAOImpl.getRemoteTypeByIpAddress(ipAddress,meNm,mtosiName,tunnelName);
            if (!remoteTypes.isEmpty()){
                if(remoteTypes.get("Head") != null && !remoteTypes.get("Head").isEmpty()){
                    List<RemoteTypeDBImpl> headList =   remoteTypes.get("Head");
                    for(RemoteTypeDBImpl entity : headList) {
                        remoteDAOImpl.updateHeadEnd(entity.getTunnelId(),meNm);
                    }
                } else if(remoteTypes.get("Remote") != null && !remoteTypes.get("Remote").isEmpty()){
                    List<RemoteTypeDBImpl> remoteList =   remoteTypes.get("Remote");
                    for(RemoteTypeDBImpl entity : remoteList) {
                        remoteDAOImpl.updateRemoteEnd(entity.getTunnelId(),meNm);
                    }
                }
            }
            return true;
        }catch(Exception ex){
            LOG.debug("Failed to persist tunnel data " + ex.getMessage());
            return false;
        }
    }

  @Override
  public DeleteManagementCTPResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
  }

