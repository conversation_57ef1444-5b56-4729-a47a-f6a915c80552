/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp20X;

import v1.tmf854.DirectionalityT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ResourceStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.common.serviceProvisioning.BITSPortSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.NETPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.WANPortSPProperties;
import com.adva.nlms.mediation.config.f3.entity.port.bits.PortBitsF3Impl;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMServiceStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.F3SyncLineBuildOutTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.GE20XBitFrameFormatTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.GE20XBitLineCodeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.GE20XBitLineTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.GE20XBitSABitDesignationTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSyncQLTranslation;

/**
 * This class is an FSP 150 CM network port MTOSI Translator.
 */
public class PortFSP150GE20XBitsTranslator extends MtosiTranslator {
	PortBitsF3Impl port;

	public PortFSP150GE20XBitsTranslator(PortBitsF3Impl portBitsFSPGE20XImpl) {
		this.port = portBitsFSPGE20XImpl;
	}

  @Override
  public PhysicalTerminationPointT toMtosiPTP() throws ProcessingFailureException {

	  BITSPortSPProperties portProperties = (BITSPortSPProperties) port.getPortSPProperties();
	    ObjectFactory objFactory = new ObjectFactory();
	    PhysicalTerminationPointT physicalTerminationPointT = objFactory.createPhysicalTerminationPointT();

	    // PTP element name
	    NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributes(port);
	    physicalTerminationPointT.setName(objFactory.createPhysicalTerminationPointTName(namingAttributes));

	    // discoverdName
	    final String ptpNm = namingAttributes.getPtpNm();
	    if (ptpNm == null) {
	      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
	    }
	    physicalTerminationPointT.setDiscoveredName(objFactory.createPhysicalTerminationPointTDiscoveredName(ptpNm));

	    // namingOS
	    physicalTerminationPointT.setNamingOS(objFactory.createPhysicalTerminationPointTNamingOS(OSFactory.getNmsName()));

	    // source
	    SourceT source = new SourceT();
	    source.setValue(SourceEnumT.NETWORK_EMS);
	    physicalTerminationPointT.setSource(objFactory.createPhysicalTerminationPointTSource(source));

	    // resource state
	    ResourceStateT resourceState = new ResourceStateT();
	    resourceState.setValue(ResourceStateEnumT.INSTALLED);
	    physicalTerminationPointT.setResourceState(objFactory.createPhysicalTerminationPointTResourceState(resourceState));

	    // direction
	    boolean bitsIn = ptpNm.indexOf("BITS-IN") >= 0; // Hmm, if "type" is redundant based on portName, perhaps this isn't a hack.
	    DirectionalityT direction = null;
	    if (bitsIn) {
	    	direction = DirectionalityT.D_SINK;
	    } else {
	    	direction = DirectionalityT.D_SOURCE;
	    }
	    
	    physicalTerminationPointT.setDirection(objFactory.createPhysicalTerminationPointTDirection(direction));

	    // layers
	    LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

	    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL);
	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL,
	           LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.ACTIVE_PARAM);

	    // ----- Start PROP_ADVA_BITS Layer
	    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_BITS);
	    
	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_BITS,
		           LayeredParams.PropAdvaBits.ADMINISTRATION_CONTROL_PARAM, 
		           MtosiUtils.getMtosiString(CMAdministrationControlTranslation.NOT_APPLICABLE, portProperties.get(WANPortSPProperties.VI.IfAdminStatus)));
		    
	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_BITS,
		           LayeredParams.PropAdvaBits.SERVICE_STATE_PARAM, 
		            CMServiceStateTranslation.getMtosiString(portProperties.get(WANPortSPProperties.VI.IfAdminStatus), portProperties.get(WANPortSPProperties.VI.IfOperStatus)));
		    
	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_BITS,
		           LayeredParams.PropAdvaBits.SECONDARY_STATE_PARAM, 
		           portProperties.get(NETPortSPPropertiesFSP150CM.VS.SecondaryState));

	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_BITS,
		           LayeredParams.PropAdvaBits.LINE_TYPE_PARAM, 
		           MtosiUtils.getMtosiString(GE20XBitLineTypeTranslation.NOT_APPLICABLE, portProperties.get(BITSPortSPProperties.VI.LineType)));
		    
	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_BITS,
		           LayeredParams.PropAdvaBits.LINE_CODE_PARAM, 
		           MtosiUtils.getMtosiString(GE20XBitLineCodeTranslation.NOT_APPLICABLE, portProperties.get(BITSPortSPProperties.VI.LineCode)));
		    
	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_BITS,
		           LayeredParams.PropAdvaBits.FRAME_FORMAT_PARAM, 
		           MtosiUtils.getMtosiString(GE20XBitFrameFormatTranslation.NOT_APPLICABLE, portProperties.get(BITSPortSPProperties.VI.FrameFormat)));
	    
	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_BITS,
		           LayeredParams.PropAdvaBits.SA_BIT_DESIGNATION_PARAM, 
		           MtosiUtils.getMtosiString(GE20XBitSABitDesignationTranslation.NOT_APPLICABLE, portProperties.get(BITSPortSPProperties.VI.SABitDesignation)));
		    
	    if (!bitsIn) {// && GE20XBitLineTypeTranslation.t1.getMIBValue() ==  portProperties.get(BITSPortSPProperties.VI.LineType)) {
	      String buildOut = "TODO";  //TODO
	      if (portProperties.get(BITSPortSPProperties.VI.LineBuildOut) != null)
	        buildOut = MtosiUtils.getMtosiString(F3SyncLineBuildOutTranslation.NOT_APPLICABLE,portProperties.get(BITSPortSPProperties.VI.LineBuildOut));
	      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_BITS,
            LayeredParams.PropAdvaBits.LINE_BUILD_OUT_PARAM, buildOut);
            
     
	    }
	    // -------start PROP_ADVA_SyncEthernet Layer--------
	    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet);

	    String qlModeAdminControl = MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, portProperties.get(BITSPortSPProperties.VI.QLModeAdministrationControl));
	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
	            LayeredParams.PropAdvaSyncEthernet.QL_MODE_ADMINISTRATION_CONTROL_PARAM, 
	            qlModeAdminControl);
	    
	    if (bitsIn) {
	    	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
	            LayeredParams.PropAdvaSyncEthernet.EXPECTED_QL_PARAM, 
	            MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE, portProperties.get(BITSPortSPProperties.VI.ExpectedQL)));
	    
	    	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
	            LayeredParams.PropAdvaSyncEthernet.ASSUMED_QL_PARAM, 
	            MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE, portProperties.get(BITSPortSPProperties.VI.AssumedQL)));

	    	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
	            LayeredParams.PropAdvaSyncEthernet.RECEIVED_QL_PARAM, 
	            MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE, portProperties.get(BITSPortSPProperties.VI.ReceivedQL)));
	    } else {
	    	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
	            LayeredParams.PropAdvaSyncEthernet.TRANSMIT_QL_PARAM, 
	            MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE, portProperties.get(BITSPortSPProperties.VI.TransmitQL)));
	    
	    	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
	            LayeredParams.PropAdvaSyncEthernet.SQUELCH_QL_PARAM, 
	            MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE, portProperties.get(BITSPortSPProperties.VI.SquelchQL)));
	    }
	    
	    // -------end of Layer-------
	    physicalTerminationPointT.setTransmissionParams(objFactory.createPhysicalTerminationPointTTransmissionParams(layeredParametersListT));
	    return physicalTerminationPointT;
  }

}
