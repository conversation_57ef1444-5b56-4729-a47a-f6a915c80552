/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;

import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.HeaderUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.me.GetAllFTPsWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.GetAllManagedElementNamesWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.GetAllManagedElementsWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.GetAllPTPsWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.GetContainedCurrentCTPsWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.GetManagedElementWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.GetTPWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.ManagedElementMgr;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.annotation.Resource;
import jakarta.xml.ws.WebServiceContext;

/**
 * This class was generated by the Celtix 1.1-SNAPSHOT Fri Dec 22 10:48:24 EST
 * 2006 Generated source version: 1.1-SNAPSHOT
 *
 */
@jakarta.jws.WebService(name = "ManagedElementMgr", serviceName = "ConfigurationService", portName = "ManagedElementMgrHttp", targetNamespace = "tmf854.v1.ws")
public class ManagedElementMgrImpl implements ManagedElementMgr {
  Logger LOG = LogManager.getLogger(this.getClass().getName());
  @Resource
  private WebServiceContext context;

  /*
    * (non-Javadoc)
    *
    * @see ws.v1.tmf854.ManagedElementMgr#getAllActiveAlarms(v1.tmf854.GetAllActiveAlarmsT
    *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
    */
  public v1.tmf854.GetAllActiveAlarmsResponseT getAllActiveAlarms(v1.tmf854.GetAllActiveAlarmsT mtosiBody,
                                                                  jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllActiveAlarms", "getAllActiveAlarmsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllActiveAlarmsResponseT getAllActiveAlarms(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                  v1.tmf854.GetAllActiveAlarmsT mtosiBody) throws ProcessingFailureException {
    return getAllActiveAlarms(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllActiveAlarmsIterator(v1.tmf854.GetActiveAlarmIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllActiveAlarmsResponseT getAllActiveAlarmsIterator(
          v1.tmf854.GetActiveAlarmIteratorT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllActiveAlarmsIterator", "getAllActiveAlarmsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllActiveAlarmsResponseT getAllActiveAlarmsIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetActiveAlarmIteratorT mtosiBody)
          throws ProcessingFailureException {
    return getAllActiveAlarmsIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllCrossConnections(v1.tmf854.GetAllCrossConnectionsT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllCrossConnectionsResponseT getAllCrossConnections(
          v1.tmf854.GetAllCrossConnectionsT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllCrossConnections", "getAllCrossConnectionsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllCrossConnectionsResponseT getAllCrossConnections(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetAllCrossConnectionsT mtosiBody)
          throws ProcessingFailureException {
    return getAllCrossConnections(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllCrossConnectionsIterator(v1.tmf854.GetCrossConnectionIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllCrossConnectionsResponseT getAllCrossConnectionsIterator(
          v1.tmf854.GetCrossConnectionIteratorT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllCrossConnectionsIterator", "getAllCrossConnectionsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllCrossConnectionsResponseT getAllCrossConnectionsIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetCrossConnectionIteratorT mtosiBody)
          throws ProcessingFailureException {
    return getAllCrossConnectionsIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllFixedCrossConnections(v1.tmf854.GetAllFixedCrossConnectionsT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllFixedCrossConnectionsResponseT getAllFixedCrossConnections(
          v1.tmf854.GetAllFixedCrossConnectionsT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllFixedCrossConnections", "getAllFixedCrossConnectionsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllFixedCrossConnectionsResponseT getAllFixedCrossConnections(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetAllFixedCrossConnectionsT mtosiBody)
          throws ProcessingFailureException {
    return getAllFixedCrossConnections(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllFixedCrossConnectionsIterator(v1.tmf854.GetCrossConnectionIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllFixedCrossConnectionsResponseT getAllFixedCrossConnectionsIterator(
          v1.tmf854.GetCrossConnectionIteratorT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllFixedCrossConnectionsIterator", "getAllFixedCrossConnectionsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllFixedCrossConnectionsResponseT getAllFixedCrossConnectionsIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetCrossConnectionIteratorT mtosiBody)
          throws ProcessingFailureException {
    return getAllFixedCrossConnectionsIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllFTPNames(v1.tmf854.GetAllFTPNamesT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getAllFTPNames(v1.tmf854.GetAllFTPNamesT mtosiBody,
                                                             jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllFTPNames", "getAllFTPNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllFTPNames(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                             v1.tmf854.GetAllFTPNamesT mtosiBody) throws ProcessingFailureException {
    return getAllFTPNames(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllFTPNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getAllFTPNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
                                                                     jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllFTPNamesIterator", "getAllFTPNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllFTPNamesIterator(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                     v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllFTPNamesIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllFTPs(v1.tmf854.GetAllFTPsT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllFTPsResponseT getAllFTPs(v1.tmf854.GetAllFTPsT mtosiBody,
                                                  jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllFTPsWorker.class, mtosiBody, mtosiHeader,
            "getAllFTPs", context, LOG);
  }

  @Override
  public v1.tmf854.GetAllFTPsResponseT getAllFTPs(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                  v1.tmf854.GetAllFTPsT mtosiBody) throws ProcessingFailureException {
    return getAllFTPs(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllFTPsIterator(v1.tmf854.GetFtpIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllFTPsResponseT getAllFTPsIterator(v1.tmf854.GetFtpIteratorT mtosiBody,
                                                          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllFTPsIterator", "getAllFTPsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllFTPsResponseT getAllFTPsIterator(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                          v1.tmf854.GetFtpIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllFTPsIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllManagedElementNames(v1.tmf854.GetAllManagedElementNamesT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getAllManagedElementNames(v1.tmf854.GetAllManagedElementNamesT mtosiBody,
                                                                        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllManagedElementNamesWorker.class, mtosiBody, mtosiHeader,
            "getAllManagedElementNames", context, LOG);
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllManagedElementNames(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                        v1.tmf854.GetAllManagedElementNamesT mtosiBody) throws ProcessingFailureException {
    return getAllManagedElementNames(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllManagedElementNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getAllManagedElementNamesIterator(
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllManagedElementNamesIterator", "getAllManagedElementNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllManagedElementNamesIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetAllObjectNamesIteratorT mtosiBody)
          throws ProcessingFailureException {
    return getAllManagedElementNamesIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllManagedElements(v1.tmf854.GetAllManagedElementsT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllManagedElementsResponseT getAllManagedElements(v1.tmf854.GetAllManagedElementsT mtosiBody,
                                                                        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllManagedElementsWorker.class, mtosiBody, mtosiHeader,
            "getAllManagedElements", context, LOG);
  }

  @Override
  public v1.tmf854.GetAllManagedElementsResponseT getAllManagedElements(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                        v1.tmf854.GetAllManagedElementsT mtosiBody) throws ProcessingFailureException {
    return getAllManagedElements(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllManagedElementsIterator(v1.tmf854.GetMeIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllManagedElementsResponseT getAllManagedElementsIterator(v1.tmf854.GetMeIteratorT mtosiBody,
                                                                                jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllManagedElementsIterator", "getAllManagedElementsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllManagedElementsResponseT getAllManagedElementsIterator(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                                v1.tmf854.GetMeIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllManagedElementsIterator(mtosiBody, mtosiHeader);
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllPTPNames(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                             v1.tmf854.GetAllPTPNamesT mtosiBody) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllPTPNames", "getAllPTPNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllPTPNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getAllPTPNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
                                                                     jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllPTPNamesIterator", "getAllPTPNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllPTPNamesIterator(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                     v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllPTPNamesIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllPTPs(v1.tmf854.GetAllPTPsT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllPTPsResponseT getAllPTPs(v1.tmf854.GetAllPTPsT mtosiBody,
                                                  jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllPTPsWorker.class, mtosiBody, mtosiHeader,
            "getAllPTPs", context, LOG);
  }

  @Override
  public v1.tmf854.GetAllPTPsResponseT getAllPTPs(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                  v1.tmf854.GetAllPTPsT mtosiBody) throws ProcessingFailureException {
    return getAllPTPs(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllPTPsIterator(v1.tmf854.GetTpIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllPTPsResponseT getAllPTPsIterator(v1.tmf854.GetTpIteratorT mtosiBody,
                                                          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllPTPsIterator", "getAllPTPsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllPTPsResponseT getAllPTPsIterator(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                          v1.tmf854.GetTpIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllPTPsIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllPTPNamesWithoutFTPs(v1.tmf854.GetAllPTPNamesWithoutFTPsT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getAllPTPNamesWithoutFTPs(
          v1.tmf854.GetAllPTPNamesWithoutFTPsT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllPTPNamesWithoutFTPs", "getAllPTPNamesWithoutFTPsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllPTPNamesWithoutFTPs(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetAllPTPNamesWithoutFTPsT mtosiBody)
          throws ProcessingFailureException {
    return getAllPTPNamesWithoutFTPs(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllPTPNamesWithoutFTPsIterator(v1.tmf854.GetPtpIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getAllPTPNamesWithoutFTPsIterator(v1.tmf854.GetPtpIteratorT mtosiBody,
                                                                                jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllPTPNamesWithoutFTPsIterator", "getAllPTPNamesWithoutFTPsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllPTPNamesWithoutFTPsIterator(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                                v1.tmf854.GetPtpIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllPTPNamesWithoutFTPsIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllPTPsWithoutFTPs(v1.tmf854.GetAllPTPsWithoutFTPsT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllPTPsWithoutFTPsResponseT getAllPTPsWithoutFTPs(v1.tmf854.GetAllPTPsWithoutFTPsT mtosiBody,
                                                                        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllPTPsWithoutFTPs", "getAllPTPsWithoutFTPsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllPTPsWithoutFTPsResponseT getAllPTPsWithoutFTPs(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                        v1.tmf854.GetAllPTPsWithoutFTPsT mtosiBody) throws ProcessingFailureException {
    return getAllPTPsWithoutFTPs(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllPTPsWithoutFTPsIterator(v1.tmf854.GetPtpIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllPTPsWithoutFTPsResponseT getAllPTPsWithoutFTPsIterator(v1.tmf854.GetPtpIteratorT mtosiBody,
                                                                                jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllPTPsWithoutFTPsIterator", "getAllPTPsWithoutFTPsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllPTPsWithoutFTPsResponseT getAllPTPsWithoutFTPsIterator(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                                v1.tmf854.GetPtpIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllPTPsWithoutFTPsIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllUnacknowledgedActiveAlarms(v1.tmf854.GetAllUnacknowledgedActiveAlarmsT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllUnacknowledgedActiveAlarmsResponseT getAllUnacknowledgedActiveAlarms(
          v1.tmf854.GetAllUnacknowledgedActiveAlarmsT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllUnacknowledgedActiveAlarms", "getAllUnacknowledgedActiveAlarmsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllUnacknowledgedActiveAlarmsResponseT getAllUnacknowledgedActiveAlarms(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetAllUnacknowledgedActiveAlarmsT mtosiBody)
          throws ProcessingFailureException {
    return getAllUnacknowledgedActiveAlarms(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getAllUnacknowledgedActiveAlarmsIterator(v1.tmf854.GetActiveAlarmIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllUnacknowledgedActiveAlarmsResponseT getAllUnacknowledgedActiveAlarmsIterator(
          v1.tmf854.GetActiveAlarmIteratorT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllUnacknowledgedActiveAlarmsIterator", "getAllUnacknowledgedActiveAlarmsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllUnacknowledgedActiveAlarmsResponseT getAllUnacknowledgedActiveAlarmsIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetActiveAlarmIteratorT mtosiBody)
          throws ProcessingFailureException {
    return getAllUnacknowledgedActiveAlarmsIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getContainedCurrentCTPNames(v1.tmf854.GetContainedCurrentCTPNamesT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getContainedCurrentCTPNames(
          v1.tmf854.GetContainedCurrentCTPNamesT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getContainedCurrentCTPNames", "getContainedCurrentCTPNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getContainedCurrentCTPNames(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetContainedCurrentCTPNamesT mtosiBody)
          throws ProcessingFailureException {
    return getContainedCurrentCTPNames(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getContainedCurrentCTPNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getContainedCurrentCTPNamesIterator(
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getContainedCurrentCTPNamesIterator", "getContainedCurrentCTPNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getContainedCurrentCTPNamesIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetAllObjectNamesIteratorT mtosiBody)
          throws ProcessingFailureException {
    return getContainedCurrentCTPNamesIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getContainedCurrentCTPs(v1.tmf854.GetContainedCurrentCTPsT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetContainedCurrentCTPsResponseT getContainedCurrentCTPs(
          v1.tmf854.GetContainedCurrentCTPsT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetContainedCurrentCTPsWorker.class, mtosiBody, mtosiHeader,
            "getContainedCurrentCTPs", context, LOG);
  }

  @Override
  public v1.tmf854.GetContainedCurrentCTPsResponseT getContainedCurrentCTPs(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetContainedCurrentCTPsT mtosiBody)
          throws ProcessingFailureException {
    return getContainedCurrentCTPs(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getContainedCurrentCTPsIterator(v1.tmf854.GetCtpIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetContainedCurrentCTPsResponseT getContainedCurrentCTPsIterator(
          v1.tmf854.GetCtpIteratorT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getContainedCurrentCTPsIterator", "getContainedCurrentCTPsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetContainedCurrentCTPsResponseT getContainedCurrentCTPsIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetCtpIteratorT mtosiBody)
          throws ProcessingFailureException {
    return getContainedCurrentCTPsIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getContainedInUseCTPNames(v1.tmf854.GetContainedInUseCTPNamesT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getContainedInUseCTPNames(
          v1.tmf854.GetContainedInUseCTPNamesT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getContainedInUseCTPNames", "getContainedInUseCTPNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getContainedInUseCTPNames(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetContainedInUseCTPNamesT mtosiBody)
          throws ProcessingFailureException {
    return getContainedInUseCTPNames(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getContainedInUseCTPNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getContainedInUseCTPNamesIterator(
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getContainedInUseCTPNamesIterator", "getContainedInUseCTPNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getContainedInUseCTPNamesIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetAllObjectNamesIteratorT mtosiBody)
          throws ProcessingFailureException {
    return getContainedInUseCTPNamesIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getContainedInUseCTPs(v1.tmf854.GetContainedInUseCTPsT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetContainedInUseCTPsResponseT getContainedInUseCTPs(v1.tmf854.GetContainedInUseCTPsT mtosiBody,
                                                                        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getContainedInUseCTPs", "getContainedInUseCTPsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetContainedInUseCTPsResponseT getContainedInUseCTPs(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                        v1.tmf854.GetContainedInUseCTPsT mtosiBody) throws ProcessingFailureException {
    return getContainedInUseCTPs(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getContainedInUseCTPsIterator(v1.tmf854.GetCtpIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetContainedInUseCTPsResponseT getContainedInUseCTPsIterator(v1.tmf854.GetCtpIteratorT mtosiBody,
                                                                                jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getContainedInUseCTPsIterator", "getContainedInUseCTPsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetContainedInUseCTPsResponseT getContainedInUseCTPsIterator(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                                v1.tmf854.GetCtpIteratorT mtosiBody) throws ProcessingFailureException {
    return getContainedInUseCTPsIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getContainedPotentialCTPNames(v1.tmf854.GetContainedPotentialCTPNamesT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getContainedPotentialCTPNames(
          v1.tmf854.GetContainedPotentialCTPNamesT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getContainedPotentialCTPNames", "getContainedPotentialCTPNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getContainedPotentialCTPNames(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetContainedPotentialCTPNamesT mtosiBody)
          throws ProcessingFailureException {
    return getContainedPotentialCTPNames(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getContainedPotentialCTPNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getContainedPotentialCTPNamesIterator(
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getContainedPotentialCTPNamesIterator", "getContainedPotentialCTPNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getContainedPotentialCTPNamesIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetAllObjectNamesIteratorT mtosiBody)
          throws ProcessingFailureException {
    return getContainedPotentialCTPNamesIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getContainedPotentialCTPs(v1.tmf854.GetContainedPotentialCTPsT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetContainedPotentialCTPsResponseT getContainedPotentialCTPs(
          v1.tmf854.GetContainedPotentialCTPsT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getContainedPotentialCTPs", "getContainedPotentialCTPsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetContainedPotentialCTPsResponseT getContainedPotentialCTPs(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetContainedPotentialCTPsT mtosiBody)
          throws ProcessingFailureException {
    return getContainedPotentialCTPs(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getContainedPotentialCTPsIterator(v1.tmf854.GetCtpIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetContainedPotentialCTPsResponseT getContainedPotentialCTPsIterator(
          v1.tmf854.GetCtpIteratorT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getContainedPotentialCTPsIterator", "getContainedPotentialCTPsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetContainedPotentialCTPsResponseT getContainedPotentialCTPsIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetCtpIteratorT mtosiBody)
          throws ProcessingFailureException {
    return getContainedPotentialCTPsIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getContainingSubnetworkNames(v1.tmf854.GetContainingSubnetworkNamesT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getContainingSubnetworkNames(
          v1.tmf854.GetContainingSubnetworkNamesT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getContainingSubnetworkNames", "getContainingSubnetworkNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getContainingSubnetworkNames(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetContainingSubnetworkNamesT mtosiBody)
          throws ProcessingFailureException {
    return getContainingSubnetworkNames(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getContainingTPNames(v1.tmf854.GetContainingTPNamesT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getContainingTPNames(v1.tmf854.GetContainingTPNamesT mtosiBody,
                                                                   jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getContainingTPNames", "getContainingTPNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getContainingTPNames(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                   v1.tmf854.GetContainingTPNamesT mtosiBody) throws ProcessingFailureException {
    return getContainingTPNames(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getContainingTPs(v1.tmf854.GetContainingTPsT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetContainingTPsResponseT getContainingTPs(v1.tmf854.GetContainingTPsT mtosiBody,
                                                              jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getContainingTPs", "getContainingTPsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetContainingTPsResponseT getContainingTPs(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                              v1.tmf854.GetContainingTPsT mtosiBody) throws ProcessingFailureException {
    return getContainingTPs(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getManagedElement(v1.tmf854.GetManagedElementT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetManagedElementResponseT getManagedElement(v1.tmf854.GetManagedElementT mtosiBody,
                                                                jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetManagedElementWorker.class, mtosiBody, mtosiHeader,
            "getManagedElement", context, LOG);
  }

  @Override
  public v1.tmf854.GetManagedElementResponseT getManagedElement(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                v1.tmf854.GetManagedElementT mtosiBody) throws ProcessingFailureException {
    return getManagedElement(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getPotentialFixedCCs(v1.tmf854.GetPotentialFixedCCsT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetPotentialFixedCCsResponseT getPotentialFixedCCs(v1.tmf854.GetPotentialFixedCCsT mtosiBody,
                                                                      jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getPotentialFixedCCs", "getPotentialFixedCCsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetPotentialFixedCCsResponseT getPotentialFixedCCs(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                      v1.tmf854.GetPotentialFixedCCsT mtosiBody) throws ProcessingFailureException {
    return getPotentialFixedCCs(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.ManagedElementMgr#getTP(v1.tmf854.GetTPT mtosiBody
	 *      ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetTPResponseT getTP(v1.tmf854.GetTPT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetTPWorker.class, mtosiBody, mtosiHeader,
            "getTP", context, LOG);
  }

  @Override
  public v1.tmf854.GetTPResponseT getTP(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetTPT mtosiBody)
          throws ProcessingFailureException {
    return getTP(mtosiBody, mtosiHeader);
  }
}
