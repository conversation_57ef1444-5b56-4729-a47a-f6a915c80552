/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesHN4000;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.hn4000.FlowHN4000;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import v1.tmf854.ConnectionTerminationPointT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.TPDataT;
import v1.tmf854.TerminationPointT;
import v1.tmf854ext.adva.SetTPDataResponseT;

import jakarta.xml.ws.Holder;

//import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiSupported;

public class SetCTPDataWorkerHN extends SetTPDataWorker {
	FlowHN4000 hnFlow = null;

	public SetCTPDataWorkerHN(Holder<HeaderT> mtosiHeader, TPDataT tpInfo, NamingAttributesT namingAttributes, NetworkElement ne) {
		super(mtosiHeader, tpInfo, namingAttributes, ne);
	}

  @Override
  protected void parse () throws Exception {
    // empty method
  }

  @Override
  protected void mediate() throws Exception {

		hnFlow = ManagedElementFactory.getHNFlow(namingAttributes);
		FlowSPPropertiesHN4000 newFlowProps = null;
		newFlowProps = MtosiTPMediator.mtosiTPDataTToHNCTPProperties(tpInfo, hnFlow);
		transact(hnFlow, newFlowProps);
	}

	private void transact(FlowHN4000 flow, FlowSPPropertiesHN4000 props) throws ObjectInUseException, NetTransactionException, SPValidationException,
			SNMPCommFailure {
		NetworkElement locks[] = new NetworkElement[] { ne };
		Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "SetTPDataWorker");
		try {
			logSecurity(ne, SystemAction.ModifyNetwork, flow.getMtosiName());
			flow.setFlowSettings(props);
			NetTransactionManager.commitNetTransaction(id);
		} catch (NetTransactionException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SPValidationException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SNMPCommFailure e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} finally {
			NetTransactionManager.ensureEnd(id);
		}
	}

	@Override
  protected void response() throws Exception {
		// Reload the flow
		hnFlow = ManagedElementFactory.getHNFlow(namingAttributes);
		hnFlow.doPollingVolatile(); 
		ObjectFactory objectFactory = new ObjectFactory();
		TerminationPointT tp = objectFactory.createTerminationPointT();
		ConnectionTerminationPointT connectionTerminationPointT = null;
		connectionTerminationPointT = (new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator( hnFlow)).toMtosiCTP();
//		connectionTerminationPointT = ((MtosiSupported) hnFlow).getMtosiTranslator().toMtosiCTP();

		tp.setCtp(connectionTerminationPointT);
		response.setModifiedTP(tp);
	}

	@Override
  public SetTPDataResponseT getSuccessResponse() {
		if (response == null)
			return null;
		response.setTmf854Version(MtosiConstants.VERSION);
		return response;
	}
}