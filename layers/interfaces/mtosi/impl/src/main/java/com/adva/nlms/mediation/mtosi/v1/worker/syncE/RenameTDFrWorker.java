/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.syncE;

import com.adva.nlms.common.NEUtils;
import com.adva.nlms.mediation.common.serviceProvisioning.F3SyncSPProperties;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.sync.F3Sync;
import com.adva.nlms.mediation.config.f3.entity.sync.F3SyncImpl;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CMImpl;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiF3F3SyncNameException;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiF3SyncName;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.RenameTDFrResponseT;
import v1.tmf854ext.adva.RenameTDFrT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

/**
 * Worker class for the MTOSI operation RenameTDFr
 */
public class RenameTDFrWorker extends AbstractMtosiWorker {
  Logger LOG = LogManager.getLogger(this.getClass().getName());

  protected RenameTDFrT mtosiBody;
  protected RenameTDFrResponseT response = new RenameTDFrResponseT();
  protected NamingAttributesT tdfrName;
  protected NamingAttributesT newTdfrName;
  protected MtosiAddress mtosiAddr;

  //Constructor
  public RenameTDFrWorker(final RenameTDFrT mtosiBody, final Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "renameTDFr", "renameTDFr", "renameTDFrResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    tdfrName = mtosiBody.getTdfrName();
    newTdfrName = mtosiBody.getNewTDFrName();
    mtosiAddr = new MtosiAddress(tdfrName);

    if (tdfrName == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
        MtosiErrorConstants.INVALID_FILTER);
    }
    if (newTdfrName == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
        "New TDFr name is missing.");
    }

    // check MD Name - TODO also for newTdfrName ???
    if (tdfrName.getMdNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
        MtosiErrorConstants.MD_NAME_MISSING);
    }
    if (!tdfrName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        MtosiErrorConstants.MD_NOT_FOUND);
    }

    if(tdfrName.getTdfrNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        "TDFr name is missing.");
    }
    if(newTdfrName.getTdfrNm() == null || newTdfrName.getTdfrNm().length() == 0) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        "New TDFr name is missing.");
    }

    MtosiUtils.validateNE(mtosiAddr.getNE());

  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(mtosiAddr.getNE().getDefaultNetworkElementTypeString());
  }

  @Override
  protected void mediate() throws Exception {
    // only allowed for FSP150CC-GE201SE && FSP150CM
    if (!NEUtils.isSyncEDevice(mtosiAddr.getNeType())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The specified ME does not support SyncE.");
    }

    NetworkElementFSP150CMImpl ne = (NetworkElementFSP150CMImpl)mtosiAddr.getNE();
    MtosiF3SyncName oldName = null;
    MtosiF3SyncName newName = null;
    
    try {
      oldName = new MtosiF3SyncName(tdfrName.getTdfrNm(), true, mtosiAddr.getNeType());
      newName = new MtosiF3SyncName(newTdfrName.getTdfrNm(), mtosiAddr.getNeType());
    } catch (MtosiF3F3SyncNameException e) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
      e.getMessage(),e);
      
    }
    // validate F3Sync-objects for TDFr-name and new-TDFR-Name
    final F3SyncImpl f3Sync = getFSP150CMMTOSIWorker(ne).getF3Sync(oldName.getShelfIndex(), oldName.getSlotIndex(), oldName.getAlias());
    if (f3Sync == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The specified TDFr does not exist.");
    }
    final F3SyncImpl newF3Sync = getFSP150CMMTOSIWorker(ne).getF3Sync(newName.getShelfIndex(), newName.getSlotIndex(), newName.getAlias());
    if (newF3Sync != null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "An TDFr already exists with the specified name.");
    }

    F3SyncSPProperties f3SyncProps = f3Sync.getF3SyncSPProperties();
    F3SyncSPProperties newF3SyncProps = new F3SyncSPProperties(
               f3SyncProps.get(F3SyncSPProperties.VI.NeIndex),
               newName.getShelfIndex(),
               newName.getSlotIndex(),
               f3SyncProps.get(F3SyncSPProperties.VI.F3SyncIndex),
               newName.getAlias(),
               newTdfrName.getTdfrNm());

    transact(ne, f3Sync, newF3SyncProps, oldName.toString());
  }

  private void transact(NetworkElement ne, F3Sync f3Sync, F3SyncSPProperties newF3SyncProps, String tdfrName)
          throws NetTransactionException, SNMPCommFailure, SPValidationException, ObjectInUseException {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "RenameTDFrWorker");
    try {
      logSecurity(ne, SystemAction.ModifyNetwork, tdfrName);
      f3Sync.setSettings(newF3SyncProps);
      NetTransactionManager.commitNetTransaction(id);
    } catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      NetTransactionManager.ensureEnd(id);
    }
  }

  @Override
  protected void response() throws Exception {
  }

  @Override
  public RenameTDFrResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
