/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cp;

import java.util.Collection;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.ObjectFactory;

import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.FDFrFSP150CP;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.FDFrPortEndFSP150CPIDs;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.FDFrFTPEndFSP150CPIDs;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.NetworkElementFSP150CP;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrEndIDs;
import com.adva.nlms.mediation.mtosi.v1.mediation.FDFrTranslator;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;

import ws.v1.tmf854.ProcessingFailureException;

/**
 * This class is an FSP 150 CP FDFr MTOSI Translator.
 */
public class FDFrFSP150CPTranslator extends FDFrTranslator {
  private FDFrFSP150CP fdfr;

  public FDFrFSP150CPTranslator(FDFrFSP150CP fdfr) {
    super(fdfr);
    this.fdfr = fdfr;
  }

  /**
   * Returns list of coresponding naming attributes of the specific multiEndPoint.
   *
   * @param endPointsCollection The collection of FDFr's endpoints
   * @return JAXBElement<NamingAttributesListT>
   * @throws ws.v1.tmf854.ProcessingFailureException When the naming attributes are invalid
   */
  @Override
  protected NamingAttributesListT getEndPointNamingAttributesListT (final Collection<FDFrEndIDs> endPointsCollection) throws ProcessingFailureException {
    final ObjectFactory objFactory = new ObjectFactory();
    final NamingAttributesListT namingAttributesListT = objFactory.createNamingAttributesListT();
    NetworkElementFSP150CP ne = fdfr.getNetworkElement();
    for (FDFrEndIDs fdFrEndIDs : endPointsCollection) {
      if (fdFrEndIDs instanceof FDFrPortEndFSP150CPIDs) {
        namingAttributesListT.getName().add(NamingTranslationFactory.getNamingAttributes((FDFrPortEndFSP150CPIDs) fdFrEndIDs, ne));
      } else if (fdFrEndIDs instanceof FDFrFTPEndFSP150CPIDs) {
        namingAttributesListT.getName().add(NamingTranslationFactory.getNamingAttributes((FDFrFTPEndFSP150CPIDs) fdFrEndIDs, ne));
      } else {
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_TP_INVALID_ENDPOINT, "Unknown FDFr end point.");
      }
    }
    return namingAttributesListT;
  }
}
