/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.hn4000;

import java.util.HashMap;
import java.util.Map.Entry;
import java.util.Set;

import jakarta.xml.bind.JAXBElement;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import v1.tmf854.DirectionalityT;
import v1.tmf854.FloatingTerminationPointT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ResourceStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000BondingSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000EthernetSProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.WANPortSPProperties;
import com.adva.nlms.mediation.config.hn4000.ObjectStateFieldHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet2BASE_TL;
import com.adva.nlms.mediation.config.hn4000.UniHN4000;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiHNUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNAdminSpeedRateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNDot3OamOperStatusTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNDuplexModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNEfmAdminControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNLinkLossForwardingTranslation;
import com.adva.nlms.common.snmp.hn.HNLoopbackStatusTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNOamModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNOamSupportedFunctionsTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNPMEValidationTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNRemoteTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNRoleStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.ServiceStateTranslation;

/**
 * This class is a HN4000 Ethernet port MTOSI Translator.
 */
public class PortHN4000Ethernet2BaseTLTranslator extends PortHN4000EthernetTranslator {
	/**
	 * Per the NBI spec, the value is always 1600
	 */
	private static final String MAXIMUM_FRAME_SIZE = "1600";
private PortHN4000Ethernet2BASE_TL port;

  
  public PortHN4000Ethernet2BaseTLTranslator(PortHN4000Ethernet2BASE_TL port) {
	  super();
    this.port = port;
    NetworkElement ne = port.getNE();
	hn400 = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400;
    
  }
  
  
  @Override
  public PhysicalTerminationPointT toMtosiPTP() throws ProcessingFailureException {
	  return null;
  }

  @Override
  public FloatingTerminationPointT toMtosiFTP() throws ProcessingFailureException {
    ObjectFactory objFactory = new ObjectFactory();
    FloatingTerminationPointT floatingTerminationPointT = objFactory.createFloatingTerminationPointT();
    PortHN4000BondingSPProperties properties = port.getPortSPProperties();

    // FTP element name
    NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributesFTP(port);
    floatingTerminationPointT.setName(objFactory.createFloatingTerminationPointTName(namingAttributes));

    // discoveredName
    final String ftpNm = namingAttributes.getFtpNm();
    if (ftpNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
    }
    floatingTerminationPointT.setDiscoveredName(objFactory.createPhysicalTerminationPointTDiscoveredName(ftpNm));

    // namingOS
    floatingTerminationPointT.setNamingOS(objFactory.createPhysicalTerminationPointTNamingOS(OSFactory.getNmsName()));

    // source
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    floatingTerminationPointT.setSource(objFactory.createPhysicalTerminationPointTSource(source));

    // resource state
    ResourceStateT resourceState = new ResourceStateT();
    if (port.getOperState() == ServiceStateTranslation.UP_NOT_PRESENT.getOperStateValue()) {
      resourceState.setValue(ResourceStateEnumT.PLANNED);
    }
    else {
      resourceState.setValue(ResourceStateEnumT.INSTALLED);
    }
    floatingTerminationPointT.setResourceState(objFactory.createPhysicalTerminationPointTResourceState(resourceState));

    // direction
    floatingTerminationPointT.setDirection(objFactory.createPhysicalTerminationPointTDirection(DirectionalityT.D_BIDIRECTIONAL));

    // edgePoint
    floatingTerminationPointT.setEdgePoint(objFactory.createPhysicalTerminationPointTEdgePoint(false));

    // layers
    LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    // -------start Layer--------
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_LAG_FRAGMENT);
      // --------------LR_LAG  Layer
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_LAG);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_LAG, LayeredParams.LrLag.ALLOCATED_NUMBER_PARAM, 
      String.valueOf(properties.get(PortHN4000BondingSPProperties.VI.AllocatedNumber)));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_LAG, LayeredParams.LrLag.ALLOCATION_MAXIMUM_PARAM, 
      "8");
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_LAG, LayeredParams.LrLag.FRAGMENT_SERVER_LAYER_PARAM, 
      LayeredParams.LR_SHDSL); 
      
      // ------------ Start PROP_HATTERAS_Bonding
  	LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_Bonding);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Bonding, LayeredParams.PropHatterasBonding.PME_VALIDATION_PARM,
			HNPMEValidationTranslation.getMtosiString(properties.get(PortHN4000BondingSPProperties.VI.Validation)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Bonding, LayeredParams.PropHatterasBonding.DOT3_AH_AGGREGATION_PARM,
			MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, properties.get(PortHN4000BondingSPProperties.VI.Dot3AhAgg) == null ? 3 : properties.get(PortHN4000BondingSPProperties.VI.Dot3AhAgg)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Bonding, LayeredParams.PropHatterasBonding.MINIMUM_RATE_ALARM_THRESHOLD_PARM,
			String.valueOf(properties.get(PortHN4000BondingSPProperties.VL.MinRateAlarm)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Bonding, LayeredParams.PropHatterasBonding.AGGREGATE_RATE_PARM,
			MtosiHNUtils.bytesToKbs(properties.get(PortHN4000BondingSPProperties.VL.AggrRate)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Bonding, LayeredParams.PropHatterasBonding.BEST_ETHER_RATE_PARM,
			MtosiHNUtils.bytesToKbs(properties.get(PortHN4000BondingSPProperties.VL.BestEtherRate)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Bonding, LayeredParams.PropHatterasBonding.WORST_ETHER_RATE_PARM,
			MtosiHNUtils.bytesToKbs(properties.get(PortHN4000BondingSPProperties.VL.WorstEtherRate)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Bonding, LayeredParams.PropHatterasBonding.AFDI_ENABLED_PARAM, 
			MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, properties.get(PortHN4000BondingSPProperties.VI.AfdiEnabled)== null? 3: properties.get(PortHN4000BondingSPProperties.VI.AfdiEnabled)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Bonding, LayeredParams.PropHatterasBonding.AFDI_DELAY_PARAM, 
			String.valueOf(properties.get(PortHN4000BondingSPProperties.VL.AfdiDelay)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Bonding, LayeredParams.PropHatterasBonding.MAX_FRAGMENT_SIZE_PARM,
			String.valueOf(properties.get(PortHN4000BondingSPProperties.VL.MaxFragmentSize)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Bonding, LayeredParams.PropHatterasBonding.ACTUAL_PME_PORT_LIST_PARM,
			String.valueOf(buildPMEPortList(properties.get(PortHN4000BondingSPProperties.VH.PmePortMap))));

    // ------------ Start PROP_HATTERAS_RemoteCPE
	//Only on hn4000
	if (!hn400) {
	  	LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_RemoteCPE);
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_RemoteCPE, LayeredParams.PropHatterasRemoteCPE.CPE_HOSTNAME_PARM,
				String.valueOf(properties.get(PortHN4000BondingSPProperties.VS.CpeHostName)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_RemoteCPE, LayeredParams.PropHatterasRemoteCPE.REMOTE_TYPE_PARM,
				HNRemoteTypeTranslation.getMtosiString(properties.get(PortHN4000BondingSPProperties.VI.RmtType)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_RemoteCPE, LayeredParams.PropHatterasRemoteCPE.ACTUAL_REMOTE_TYPE_PARM,
				HNRemoteTypeTranslation.getMtosiString(properties.get(PortHN4000BondingSPProperties.VI.RmtTypeAct)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_RemoteCPE, LayeredParams.PropHatterasRemoteCPE.ACTUAL_REMOTE_MODEL_PARM,
				String.valueOf(properties.get(PortHN4000BondingSPProperties.VS.RmtModelAct)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_RemoteCPE, LayeredParams.PropHatterasRemoteCPE.REMOTE_MAC_ADDRESS_PARM,
				String.valueOf(properties.get(PortHN4000BondingSPProperties.VS.RmtMacAddr)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_RemoteCPE, LayeredParams.PropHatterasRemoteCPE.ACTUAL_REMOTE_MAC_ADDRESS_PARM,
				String.valueOf(properties.get(PortHN4000BondingSPProperties.VS.RmtMacAddrAct)));
		//Valid on create or set. Returns zero on read."
		//This parameter is an input-only parameter and should be suppressed on output.
		//	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_RemoteCPE, LayeredParams.PropHatterasRemoteCPE.CPE_TEMPLATE_ID_PARM,
		//			String.valueOf(properties.getCpeTemplateId()));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_RemoteCPE, LayeredParams.PropHatterasRemoteCPE.CPE_SERIAL_PORT_ADMINISTRATION_CONTROL_PARM,
				MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, properties.get(PortHN4000BondingSPProperties.VI.CPESerialPortAdminCtrl)));
	}
	// ----- start DSR Fast Ethernet Layer --------------
	LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_DSR_FAST_ETHERNET);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_FAST_ETHERNET, LayeredParams.PropADVA2BPME.ADMINISTRATION_CONTROL_PARAM,
			MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, port.getAdminState()));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_FAST_ETHERNET, LayeredParams.PropADVA2BPME.SERVICE_STATE_PARAM,
			ServiceStateTranslation.getMtosiString(port.getAdminState(), port.getOperState()));
	//The only supported value for a bonded port is Enabled
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_FAST_ETHERNET, LayeredParams.LrDsrGigabitAndFastEthernet.AUTO_NEGOTIATION_PARAM,
			"Enabled"); //HNAutoNegotiationTranslation.getMtosiString(properties.getAutoNeg()));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_FAST_ETHERNET, LayeredParams.LrDsrGigabitAndFastEthernet.ADMINISTRATIVE_SPEED_RATE_PARAM,
			HNAdminSpeedRateTranslation.getMtosiString(properties.get(WANPortSPProperties.VI.PortSpeed)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_FAST_ETHERNET, LayeredParams.LrDsrGigabitAndFastEthernet.ACTUAL_SPEED_RATE_PARAM,
			bitsToMbps(properties.get(PortHN4000EthernetSProperties.VL.ActualSpeed)));
	//The only supported value for a bonded port is Full.
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_FAST_ETHERNET, LayeredParams.LrDsrGigabitAndFastEthernet.DUPLEX_MODE_PARAM,
			"Full"); //HNDuplexModeTranslation.getMtosiString(properties.get(PortHN4000BondingSPProperties.VI.DuplexMode)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_FAST_ETHERNET, LayeredParams.LrDsrGigabitAndFastEthernet.ACTUAL_DUPLEX_MODE_PARAM,
			HNDuplexModeTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.ActualDuplexMode)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_FAST_ETHERNET, LayeredParams.LrDsrGigabitAndFastEthernet.MAXIMUM_FRAME_SIZE_PARAM, 
			MAXIMUM_FRAME_SIZE);
	// -------end of DSR Layer Fast Ethernet -------
	// -------start LR Ethernet Layer--------
	LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET, LayeredParams.LrEthernet.CONNECTIONLESS_PORT_PARAM,
			MtosiConstants.TRUE);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET, LayeredParams.LrEthernet.INTERFACE_TYPE_PARAM,
			properties.get(PortHN4000EthernetSProperties.IT.InterfaceType) == null ? MtosiConstants.NOT_APPLICABLE : properties.get(PortHN4000EthernetSProperties.IT.InterfaceType).getIfTypeString());
  LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET, LayeredParams.LrEthernet.PORT_TP_ROLE_STATE_PARAM,
			HNRoleStateTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.PortTPRoleState)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET, LayeredParams.LrEthernet.NUMBER_OF_TRAFFIC_CLASSES_PARAM,
			"1");
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET, LayeredParams.LrEthernet.PHYS_ADDRESS_PARAM,
			String.valueOf(properties.get(WANPortSPProperties.VS.IfPhysAddress).length() > 10 ? properties.get(WANPortSPProperties.VS.IfPhysAddress): ""));
	String maxNumFDFrs = "256";
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET, LayeredParams.LrEthernet.MAX_NUM_FDFRS_PARAM, maxNumFDFrs);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET, LayeredParams.LrEthernet.NUM_CONFIGURED_FDFRS_PARAM,
			String.valueOf(properties.get(PortHN4000EthernetSProperties.VL.ConfiguredFDFrsNum)));
	// -------end of Layer-------

	// -------start PROP_HATTERAS_Ethernet layer ----------------
	LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasEthernet.DESCRIPTION,
			String.valueOf(properties.get(PortHN4000EthernetSProperties.VS.Description)));
	Integer secondaryState = properties.get(PortHN4000EthernetSProperties.VI.SecondaryState);
	if (secondaryState != null) 
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasShdsl.SECONDARY_STATE_PARAM,
				ObjectStateFieldHN4000.getSecondaryStateString(secondaryState));
	else 
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasShdsl.SECONDARY_STATE_PARAM,
				"");
	//Not applicable for Bonded Ports
	//	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasEthernet.TRANSMIT_PAUSE_FRAMES, 
	//			HNPauseFramesTranslation.getMtosiString(properties.getTransmitPauseFrames())); 
	//	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasEthernet.RECEIVE_PAUSE_FRAMES, 
	//			HNPauseFramesTranslation.getMtosiString(properties.getReceivePauseFrames()));
	int loopbackStatus = properties.get(PortHN4000EthernetSProperties.VI.LoopbackStatus);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, 
			LayeredParams.PropHatterasEthernet.LOOPBACK_STATUS_TYPE, HNLoopbackStatusTypeTranslation.fromInt(loopbackStatus));
	//Only applicable for status = RemoteEFMOAM 
	if (loopbackStatus == HNLoopbackStatusTypeTranslation.RemoteEFMOAM.getMIBValue() ||
			loopbackStatus == HNLoopbackStatusTypeTranslation.RemoteEFMOAMTailEnd.getMIBValue() ||
			loopbackStatus == HNLoopbackStatusTypeTranslation.RemoteEFMOAMWithFacilityMAC.getMIBValue() ||
			loopbackStatus == HNLoopbackStatusTypeTranslation.RemoteEFMOAMTailEndWithFacilityMAC.getMIBValue() 
		) {
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, 
			LayeredParams.PropHatterasEthernet.LOOPBACK_STATUS_LOCAL, 
			MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, properties.get(PortHN4000EthernetSProperties.VI.LoopbackMode)));
	}
	if (hn400) {
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, 
				LayeredParams.PropHatterasEthernet.LINK_LOSS_FORWARDING_PARAM, 
				HNLinkLossForwardingTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.LinkLossForwarding)));
	}
	//Note: The loopbackStatus is > 10 if FacilityMAC is "enabled" 
	if (loopbackStatus >= 10) {
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, 
				LayeredParams.PropHatterasEthernet.LOOPBACK_STATUS_LOOPBACK_PHYS_ADDR_PARM, 
				properties.get(PortHN4000EthernetSProperties.VS.LoopbackPhysAddress));
		
	}
	// -------end of PROP_HATTERAS_Ethernet Layer-------

	// -------start PROP_HATTERAS_Ethernet_EFMOAM layer ----------------
	LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_EFMOAM);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_EFMOAM, 
			LayeredParams.PropHatterasEthernetEFMOAM.EFM_OAM_ADMINISTRATION_CONTROL_PARAM,
			HNEfmAdminControlTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.OamAdminState)));
	if (properties.get(PortHN4000EthernetSProperties.VI.OamSuspend) != null && properties.get(PortHN4000EthernetSProperties.VI.OamSuspend) != 0) {
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_EFMOAM, 
				LayeredParams.PropHatterasEthernetEFMOAM.EFM_OAM_SUSPEND_PARAM,
				MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, properties.get(PortHN4000EthernetSProperties.VI.OamSuspend)));
	}
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_EFMOAM, 
			LayeredParams.PropHatterasEthernetEFMOAM.EFM_OAM_SERVICE_STATE_PARAM, 
			HNDot3OamOperStatusTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.OamServiceState)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_EFMOAM, LayeredParams.PropHatterasEthernetEFMOAM.EFM_OAM_MODE_PARAM, 
			HNOamModeTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.OamMode)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_EFMOAM, LayeredParams.PropHatterasEthernetEFMOAM.EFM_OAM_MAX_PDU_SIZE_PARAM, 
			String.valueOf(properties.get(PortHN4000EthernetSProperties.VI.OamMaxPduSize)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_EFMOAM, LayeredParams.PropHatterasEthernetEFMOAM.EFM_OAM_FUNCTIONS_SUPPORTED_LIST_PARAM, 
			HNOamSupportedFunctionsTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VS.OamFunctionsSupported))); //Need to translate

	// -------end of PROP_HATTERAS_Ethernet_EFMOAM Layer-------
	
	fillUNILayer(layeredParametersListT);
	
	JAXBElement<LayeredParametersListT> transmissionParams = objFactory.createPhysicalTerminationPointTTransmissionParams(layeredParametersListT);
	floatingTerminationPointT.setTransmissionParams(transmissionParams);

	return floatingTerminationPointT;
	}

  private String bitsToMbps(Long actualSpeed) {
	  if (actualSpeed != null && actualSpeed > 0) {
		  double d = actualSpeed;
		  d /=1000000;
		  return String.valueOf(d);
	  }
	  return String.valueOf(actualSpeed);
	  
}


private String buildPMEPortList(HashMap<Integer, Set<Integer>> pmePortMap) {
	  StringBuilder buff = new StringBuilder();
		for (Entry<Integer, Set<Integer>> entries : pmePortMap.entrySet()) {
			int shelf = entries.getKey();
			for (Integer portNum : entries.getValue()) {
				String lagMember = "/shelf=" + shelf + "/slot=1/port=2BPME-" + portNum;
				buff.append(buff.length() > 0 ? ", ":"").append(lagMember);
			}
		}
		return buff.toString();
	}

	/**
	 * Return the UNI attached to the Port
	 * @return
	 */
	@Override
	protected UniHN4000 getUni() {
		return port.getUni();
	}
	
	/**
	 * Check to see if the port has a UNI
	 * @return
	 */
	@Override
	protected boolean hasUni() {
		return port.hasUni();
	}
	
	/**
	 * Return the UNI attached to the Port
	 * @return
	 */
	@Override
	protected PortHN4000Ethernet getPort() {
		return port;
	}

}
