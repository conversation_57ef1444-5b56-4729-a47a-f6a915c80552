/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */
package com.adva.nlms.mediation.mtosi.v2.utils.facility.flowPointF3;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.mediation.config.dto.AttributesGroup;
import com.adva.nlms.mediation.config.dto.ConvertibleAttribute;
import com.adva.nlms.mediation.config.dto.attr.AbstractFlowPointF3Attr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;

public interface FlowPointEth<T extends AbstractFlowPointF3Attr> {

  default <GT extends AttributesGroup> boolean contains(ConvertibleAttribute<? super GT, ?, ?> attribute){
    return false;
  }

  EntityIndex getEnityIndex();
  void setEnityIndex(EntityIndex value);
  String getMtosiName();
  void setMtosiName(String value);

  int getAdministrationControl();
  void setAdministrationControl(int value);
  int getOperState();
  void setOperState(int value);
  int getSecondaryState();
  void setSecondaryState(int value);

  String getCuircuitName();
  void setCircuitName(String value);
  int getCos();
  void setCos(int value);
  int getMultiCos();
  void setMultiCos(int value);

  int getEgressShapingType();
  void setEgressShapingType(int value);
  String getVlanMemberList();
  void setVlanMemberList(String value);
  String getVlanMemberActionVlan();
  void setVlanMemberActionVlan(String value);
  int getVlanMemberAction();
  void setVlanMemberAction(int value);
  int getUntaggedFrameEnabled();
  void setUntaggedFrameEnabled(int value);

  int getNeId();

  int getCtagControl();
  void setCtagControl(int value);
  int getCtagVlanId();
  void setCtagVlanId(int value);
  int getCtagVlanPriority();
  void setCtagVlanPriority(int value);

  int getStagControl();
  void setStagControl(int value);
  int getStagVlanId();
  void setStagVlanId(int value);
  int getStagVlanPriority();
  void setStagVlanPriority(int value);

  int getEgressOuterPrioMapEnabled();
  void setEgressOuterPrioMapEnabled(int value);
  int getEgressInnerPrioMapEnabled();
  void setEgressInnerPrioMapEnabled(int value);
  int getSesFrameLossRatio();
  void setSesFrameLossRatio(int value);
  int getDefaultMemberEnabled();
  void setDefaultMemberEnabled(int value);
  EntityIndex getSplitHorizontalOid();
  void setSplitHorizontalOid(EntityIndex value);
  int getHierarchicalCos();
  void setHierarchicalCos(int value);


  String getLoopAvoidadnce();
  void setLoopAvoidadnce(String value);

  int getAutoBandwidthConfigEnabled();
  void setAutoBandwidthConfigEnabled(int value);
  int getAutoCirPercentage();
  void setAutoCirPercentage(int value);
  int getFrameFwdEnabled();
  void setFrameFwdEnabled(int value);

  int getMcastRateLimitEnabled();
  void setMcastRateLimitEnabled(int value);
  long getMcastRateLimit();
  void setMcastRateLimit(long value);

  int getBcastRateLimitEnabled();
  void setBcastRateLimitEnabled(int value);
  long getBcastRateLimitSpeed();
  void setBcastRateLimitSpeed(long value);
  int getCombinedRateLimitEnabled();
  void setCombinedRateLimitEnabled(int value);
  long getCombinedRateLimitSpeed();
  void setCombinedRateLimitSpeed(long value);
  long getGuarantedFlowBandwidth();
  void setGuarantedFlowBandwidth(long value);
  long getMaxFlowBandwidth();
  void setMaxFlowBandwidth(long value);

  EntityIndex getAssociatedFlowId();
  void setAssociatedFlowId(EntityIndex value);

  void getForResponse(MtosiMOFacade facade, boolean refreshed);

  String getFlowMemberOid();
}
