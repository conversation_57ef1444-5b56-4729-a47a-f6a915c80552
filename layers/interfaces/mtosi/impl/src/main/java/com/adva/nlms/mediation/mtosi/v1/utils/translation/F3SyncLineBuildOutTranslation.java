/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

/**
 *     SYNTAX   INTEGER {
               none(0),
               lbo-0-133ft(1),
               lbo-133-266ft(2),
               lbo-266-399ft(3),
               lbo-399-533ft(4),
               lbo-533-655ft(5),
               lbo-7dot5db(6),
               lbo-15db(7),
               lbo-22dot5db(8)
             }

 * <AUTHOR>
 *
 */
public enum F3SyncLineBuildOutTranslation implements TranslatableEnum {
  NOT_APPLICABLE  (-1,"n/a"),
  none            (0,"NONE"),
  LBO_0_133FT     (1,"0-133FT"),
  LBO_133_266FT   (2,"133-266FT"),
  LBO_266_399FT   (3,"266-399FT"),
  LBO_399_533FT   (4,"399-533FT"),
  LBO_533_655FT   (5,"533-655FT"),
  LBO_7DOT5DB     (6,"7DOT5DB"),
  LBO_15DB        (7,"15DB"),
  LBO_22DOT5DB    (8,"22DOT5DB");

  private final int    mibValue;
  private final String mtosiString;

  private F3SyncLineBuildOutTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}
