/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi;

import com.adva.nlms.common.InstallationState;
import com.adva.nlms.common.NEUtils;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.common.snmp.NoSuchMDObjectException;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.ModuleCardSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.NTUSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.PsuSPPropertiesFSP150CM;
import com.adva.nlms.mediation.config.Equipment;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.module.MTOSICardModuleF3;
import com.adva.nlms.mediation.config.f3.entity.module.MTOSIEthernetModuleF3;
import com.adva.nlms.mediation.config.f3.entity.module.fan.MTOSIFanF3;
import com.adva.nlms.mediation.config.f3.entity.module.psu.PowerSupplyF3;
import com.adva.nlms.mediation.config.f3.mtosi.NetworkElementFSP150CMMTOSIOperations;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CM;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.NetworkElementMediatorFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.HeaderUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMDeviceTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSecondaryStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMServiceStateTranslation;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.EqVendorExtensionsT;
import v1.tmf854.EquipmentHolderT;
import v1.tmf854.EquipmentHolderT.AcceptableEquipmentTypeList;
import v1.tmf854.EquipmentOrHolderT;
import v1.tmf854.EquipmentT;
import v1.tmf854.HolderStateT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ResourceStateT;
import v1.tmf854.ServiceStateEnumT;
import v1.tmf854.ServiceStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;

import jakarta.xml.bind.JAXBElement;
import javax.xml.namespace.QName;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class EquipmentMediator {
  private static Logger logger = LogManager.getLogger(EquipmentMediator.class.getPackage().getName());

  public static List<EquipmentOrHolderT> nmsSFPArrayToMtosiEquipmentAndHolders(NetworkElement ne, Set<EquipmentSPProperties> propertiesSet)
  {
    // This will not have inventory filter, so needs too know how to come up
    // with
    // mtosi name given a specific module, also needs to know that the top
    // level
    // module is a System equipment and not an SFP
    // so, needs to mediate without an InventoryFilter, so just with the
    // entity properties alone
    List<EquipmentOrHolderT> results = new ArrayList<EquipmentOrHolderT>();
    for (EquipmentSPProperties properties : propertiesSet) {
      if (properties.get(EquipmentSPProperties.VE.ContainedIn).isReal() && properties.isSFP())
      {
        // SFP Equipment
        if (MtosiUtils.isInstalled(properties))
        {
          EquipmentOrHolderT equipmentOrHolderEquipment = NetworkElementMediatorFactory.createNetworkElementMediator(ne).sfpToMtosiEquipment(properties);
          results.add(equipmentOrHolderEquipment);
        }
        // SFP Equipment Holder (Slot)
        try {
          properties.set(EquipmentSPProperties.VL.ManufactureDate, ne.getMTOSIWorker().getEquipmentSPProperties(properties.get(EquipmentSPProperties.VE.ContainedIn)).get(EquipmentSPProperties.VL.ManufactureDate));
        }
        catch (ClassCastException e) {
          // this case is possible, because some entities are not equipment
          logger.debug(e, e);
        }
        catch (NoSuchMDObjectException e) {
          logger.warn(e, e);
        }
        EquipmentOrHolderT equipmentOrHolderHolder = NetworkElementMediatorFactory.createNetworkElementMediator(ne).sfpToMtosiEquipmentHolder(properties);
        results.add(equipmentOrHolderHolder);
      }
    }
    return results;
  }

  public static List<EquipmentOrHolderT> nmsShelfArrayToMtosiEquipment(NetworkElement ne, Set<EquipmentSPProperties> propertiesSet) {
    List<EquipmentOrHolderT> results = new ArrayList<EquipmentOrHolderT>();
    for (EquipmentSPProperties properties : propertiesSet) {
      results.add(NetworkElementMediatorFactory.createNetworkElementMediator(ne).shelfToMtosiEquipment(properties));
    }
    return results;
  }

  public static List<EquipmentOrHolderT> nmsShelfArrayToMtosiFixedSlots(NetworkElement element, Set<EquipmentSPProperties> propertiesSet) {
    List<EquipmentOrHolderT> results = new ArrayList<EquipmentOrHolderT>();
    for (EquipmentSPProperties properties : propertiesSet) {
      results.add(EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipmentHolder(element, properties));
    }
    return results;
  }

  public static List<EquipmentOrHolderT> nmsShelfArrayToMtosiHolders(NetworkElement element, Set<EquipmentSPProperties> propertiesSet) {
    List<EquipmentOrHolderT> results = new ArrayList<EquipmentOrHolderT>();
    for (EquipmentSPProperties properties : propertiesSet) {
      results.add(EquipmentMediator.nmsShelfPropertiesToMTOSIEquipmentHolder(element, properties));
    }
    return results;
  }

  public static List<EquipmentOrHolderT> nmsPSUArrayToMtosiEquipmentAndHolders(NetworkElement ne, Set<EquipmentSPProperties> psuSet) {
    List<EquipmentOrHolderT> results = new ArrayList<EquipmentOrHolderT>();
    for (EquipmentSPProperties properties : psuSet) {
      results.add(NetworkElementMediatorFactory.createNetworkElementMediator(ne).psuToMtosiEquipment(properties));
      results.add(NetworkElementMediatorFactory.createNetworkElementMediator(ne).psuToMtosiEquipmentHolder(properties));
    }
    return results;
  }

  public static List<EquipmentOrHolderT> nmsPSUArrayToMtosiEquipmentHolders(NetworkElement ne, Set<EquipmentSPProperties> psuSet) {
    List<EquipmentOrHolderT> results = new ArrayList<EquipmentOrHolderT>();
    for (EquipmentSPProperties properties : psuSet) {
      results.add(NetworkElementMediatorFactory.createNetworkElementMediator(ne).psuToMtosiEquipmentHolder(properties));
    }
    return results;
  }

  public static List<EquipmentOrHolderT> nmsSFPArrayToMtosiEquipmentHolders(NetworkElement ne, Set<EquipmentSPProperties> set) {
    List<EquipmentOrHolderT> results = new ArrayList<EquipmentOrHolderT>();
    for (EquipmentSPProperties properties : set) {
      EntityIndex entityIndex = properties.get(EquipmentSPProperties.VE.ContainedIn);
      if ((EntityIndex.isZero(entityIndex) || entityIndex.isReal()) && properties.isSFP()) { // don't take top level chassis module
        results.add(NetworkElementMediatorFactory.createNetworkElementMediator(ne).sfpToMtosiEquipmentHolder(properties));
      }
    }
    return results;
  }

  public static EquipmentOrHolderT nmsShelfPropertiesToMTOSIEquipmentHolder(NetworkElement ne, EquipmentSPProperties properties) {
    String nameShelf = NetworkElementMediatorFactory.createNetworkElementMediator(ne).getRelativeNameForProperties(properties);
    NamingAttributesT namingAttributes = NamingTranslationFactory.createNamingAttributesHolder(ne, properties);
    ObjectFactory objFactory = new ObjectFactory();
    EquipmentHolderT equipmentHolder = objFactory.createEquipmentHolderT();
    EquipmentOrHolderT holder = objFactory.createEquipmentOrHolderT();
    // need to set name to mtosi name
    JAXBElement<String> name = objFactory.createEquipmentTDiscoveredName(nameShelf);
    equipmentHolder.setDiscoveredName(name);
    JAXBElement<NamingAttributesT> namingAttributesJax = objFactory.createEquipmentTName(namingAttributes);
    equipmentHolder.setName(namingAttributesJax);
    JAXBElement<Boolean> alarmReporting = objFactory.createEquipmentTAlarmReportingIndicator(Boolean.FALSE);
    equipmentHolder.setAlarmReportingIndicator(alarmReporting);
    JAXBElement<String> manufacturer = objFactory.createEquipmentTManufacturer(ne.getMTOSIWorker().getManufacturer());
    equipmentHolder.setManufacturer(manufacturer);
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    JAXBElement<SourceT> equipmentHolderSource = objFactory.createEquipmentTSource(source);
    equipmentHolder.setSource(equipmentHolderSource);
    JAXBElement<String> namingOS = objFactory.createEquipmentTNamingOS(OSFactory.getNmsName());
    equipmentHolder.setNamingOS(namingOS);
    ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ResourceStateEnumT.INSTALLED);
    JAXBElement<ResourceStateT> equipmentHolderResourceState = objFactory
            .createEquipmentTResourceState(resourceState);
    equipmentHolder.setResourceState(equipmentHolderResourceState);
    holder.setEh(equipmentHolder);
    JAXBElement<String> holderType = objFactory.createEquipmentHolderTHolderType(MtosiConstants.HOLDER_SHELF);
    equipmentHolder.setHolderType(holderType);
    JAXBElement<HolderStateT> holderState = objFactory.createEquipmentHolderTHolderState(MtosiUtils
            .getHolderState(properties.get(EquipmentSPProperties.VI.InstallState)));
    equipmentHolder.setHolderState(holderState);
    AcceptableEquipmentTypeList list = objFactory.createEquipmentHolderTAcceptableEquipmentTypeList();
    JAXBElement<AcceptableEquipmentTypeList> acceptableList = objFactory
            .createEquipmentHolderTAcceptableEquipmentTypeList(list);
    equipmentHolder.setAcceptableEquipmentTypeList(acceptableList);
    JAXBElement<NamingAttributesT> installedEquipmentEmpty = objFactory
            .createEquipmentHolderTExpectedOrInstalledEquipment(new NamingAttributesT());
    equipmentHolder.setExpectedOrInstalledEquipment(installedEquipmentEmpty);
    JAXBElement<String> manufacturerDate = objFactory.createEquipmentTManufacturerDate(HeaderUtils
            .formatDate(properties.get(EquipmentSPProperties.VL.ManufactureDate)));
    equipmentHolder.setManufacturerDate(manufacturerDate);
    return holder;
  }

  public static EquipmentOrHolderT nmsFixedSlotPropertiesToMTOSIEquipmentHolder(NetworkElement element,
                                                                                EquipmentSPProperties properties)
  {
    NamingAttributesT namingAttributes = NamingTranslationFactory.createNamingAttributesFixedSlotHolder(element, properties);
    String ehName = namingAttributes.getEhNm();
    ObjectFactory objFactory = new ObjectFactory();
    EquipmentHolderT equipmentHolder = objFactory.createEquipmentHolderT();
    EquipmentOrHolderT holder = objFactory.createEquipmentOrHolderT();
    // need to set name to mtosi name
    JAXBElement<String> name = objFactory.createEquipmentTDiscoveredName(ehName);
    equipmentHolder.setDiscoveredName(name);
    JAXBElement<NamingAttributesT> namingAttributesJax = objFactory.createEquipmentTName(namingAttributes);
    equipmentHolder.setName(namingAttributesJax);
    JAXBElement<Boolean> alarmReporting = objFactory.createEquipmentTAlarmReportingIndicator(Boolean.FALSE);
    equipmentHolder.setAlarmReportingIndicator(alarmReporting);
    JAXBElement<String> manufacturer = objFactory.createEquipmentTManufacturer(element.getMTOSIWorker().getManufacturer());
    equipmentHolder.setManufacturer(manufacturer);
    // manufactureDate is not supported by equipment holders on CC825/CP/HN
    JAXBElement<String> manufacturerDate = objFactory.createEquipmentTManufacturerDate("");
    equipmentHolder.setManufacturerDate(manufacturerDate);
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    JAXBElement<SourceT> equipmentHolderSource = objFactory.createEquipmentTSource(source);
    equipmentHolder.setSource(equipmentHolderSource);
    JAXBElement<String> namingOS = objFactory.createEquipmentTNamingOS(OSFactory.getNmsName());
    equipmentHolder.setNamingOS(namingOS);
    ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ResourceStateEnumT.INSTALLED);
    JAXBElement<ResourceStateT> equipmentHolderResourceState = objFactory
            .createEquipmentTResourceState(resourceState);
    equipmentHolder.setResourceState(equipmentHolderResourceState);
    holder.setEh(equipmentHolder);
    JAXBElement<String> holderType = objFactory.createEquipmentHolderTHolderType(MtosiConstants.HOLDER_SLOT);
    equipmentHolder.setHolderType(holderType);
    NamingAttributesT namingEquipment = NamingTranslationFactory.cloneNamingAttributes(namingAttributes);
    namingEquipment.setEqNm(MtosiConstants.DEFAULT_EQUIPMENT_NAME);
    JAXBElement<NamingAttributesT> installedEquipment = objFactory
            .createEquipmentHolderTExpectedOrInstalledEquipment(namingEquipment);
    equipmentHolder.setExpectedOrInstalledEquipment(installedEquipment);
    AcceptableEquipmentTypeList list = objFactory.createEquipmentHolderTAcceptableEquipmentTypeList();
    if (element.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000) {
      if (properties.get(EquipmentSPProperties.VI.RelativePosition) == 1) {
         Equipment shelf = element.getMTOSIWorker().getShelf(properties.get(EquipmentSPProperties.VI.ChassisId));
        String productType = shelf.toString(); //The fulldescription contains the Switch Module type.
        if(productType!=null && productType.length() > 0)
        {
        	list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_HN4000e);
        } else {
        	list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_HN4000I);
        	list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_HN4000e);
        	
        }
    	  
      }
      else {
        list.getAcceptableEquipmentType().add(MtosiConstants.EQ_HN4000_1000_X2);
        list.getAcceptableEquipmentType().add(MtosiConstants.EQ_HN4000_1000_TX2);
        list.getAcceptableEquipmentType().add(MtosiConstants.EQ_HN4000_100_X2);
        list.getAcceptableEquipmentType().add(MtosiConstants.EQ_HN4000_100_TX2);
        list.getAcceptableEquipmentType().add(MtosiConstants.EQ_HN4000_TE_3);
      }
    }
    else if (element.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400) {
      list.getAcceptableEquipmentType().add(element.getMTOSIWorker().getProductName());
    }
    else if (element.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM) {
      list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_EFM);
    }
    else {
      list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_150CP);
    }
    JAXBElement<AcceptableEquipmentTypeList> acceptableList = objFactory
            .createEquipmentHolderTAcceptableEquipmentTypeList(list);
    equipmentHolder.setAcceptableEquipmentTypeList(acceptableList);
    JAXBElement<HolderStateT> holderState = objFactory.createEquipmentHolderTHolderState(MtosiUtils
            .getHolderState(properties.get(EquipmentSPProperties.VI.InstallState)));
    equipmentHolder.setHolderState(holderState);
    return holder;
  }

  public static EquipmentOrHolderT nmsFixedSlotPropertiesToMTOSIEquipmentHolder(NetworkElementFSP150CM element,
                                                                                EquipmentSPPropertiesFSP150CM properties, boolean installed)
  {
    NamingAttributesT namingAttributes = NamingTranslationFactory.createNamingAttributesFixedSlotHolderCM(element, properties);
    String ehName = namingAttributes.getEhNm();
    ObjectFactory objFactory = new ObjectFactory();
    EquipmentHolderT equipmentHolder = objFactory.createEquipmentHolderT();
    EquipmentOrHolderT holder = objFactory.createEquipmentOrHolderT();
    // need to set name to mtosi name
    JAXBElement<String> name = objFactory.createEquipmentTDiscoveredName(ehName);
    equipmentHolder.setDiscoveredName(name);
    JAXBElement<NamingAttributesT> namingAttributesJax = objFactory.createEquipmentTName(namingAttributes);
    equipmentHolder.setName(namingAttributesJax);
    JAXBElement<Boolean> alarmReporting = objFactory.createEquipmentTAlarmReportingIndicator(Boolean.FALSE);
    equipmentHolder.setAlarmReportingIndicator(alarmReporting);
    JAXBElement<String> manufacturer = objFactory.createEquipmentTManufacturer(element.getMTOSIWorker().getManufacturer());
    equipmentHolder.setManufacturer(manufacturer);
    JAXBElement<String> manufacturerDate = objFactory.createEquipmentTManufacturerDate(HeaderUtils
            .formatDate(properties.get(EquipmentSPProperties.VL.ManufactureDate)));
    equipmentHolder.setManufacturerDate(manufacturerDate);
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    JAXBElement<SourceT> equipmentHolderSource = objFactory.createEquipmentTSource(source);
    equipmentHolder.setSource(equipmentHolderSource);
    JAXBElement<String> namingOS = objFactory.createEquipmentTNamingOS(OSFactory.getNmsName());
    equipmentHolder.setNamingOS(namingOS);
    // need to get real resource state (ReportProperties?)
    ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ResourceStateEnumT.INSTALLED);
    JAXBElement<ResourceStateT> equipmentHolderResourceState = objFactory
            .createEquipmentTResourceState(resourceState);
    equipmentHolder.setResourceState(equipmentHolderResourceState);
    holder.setEh(equipmentHolder);
    JAXBElement<String> holderType = objFactory.createEquipmentHolderTHolderType(MtosiConstants.HOLDER_SLOT);
    equipmentHolder.setHolderType(holderType);
    NamingAttributesT namingEquipment = NamingTranslationFactory.cloneNamingAttributes(namingAttributes);
    namingEquipment.setEqNm(MtosiConstants.DEFAULT_EQUIPMENT_NAME);
    if (installed) {
      equipmentHolder.setExpectedOrInstalledEquipment(objFactory.
              createEquipmentHolderTExpectedOrInstalledEquipment(namingEquipment));
    } else {
      JAXBElement<NamingAttributesT> installedEquipmentEmpty = objFactory
              .createEquipmentHolderTExpectedOrInstalledEquipment(new NamingAttributesT());
      equipmentHolder.setExpectedOrInstalledEquipment(installedEquipmentEmpty);
    }
    AcceptableEquipmentTypeList list = objFactory.createEquipmentHolderTAcceptableEquipmentTypeList();
    int slotIndex = properties.get(EquipmentSPPropertiesFSP150CM.VI.SlotIndex);
    int neType = element.getMTOSIWorker().getNetworkElementTypeForMTOSI();
    int cardType = MtosiUtils.getCardTypeFromSlotIndex(neType, slotIndex);
    if (cardType == MIBFSP150CM.Entity.SlotTable.TYPE_SCU_INDEX) {
      if (! NEUtils.isGEDevice(neType) ) {
        list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_SCU);
        list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_SCU_T);
      }
    }
    else if (cardType == MIBFSP150CM.Entity.SlotTable.TYPE_GENERIC_INDEX) {
      // todo how to distinguish between 150CM 3.1 and 4.1 (NTE-GE for 4.1 only)
      // todo what is required for GE20x
      if (! NEUtils.isGEDevice(neType) ){
        list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_NEMI);
        list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_NTU_GE);
        list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_NTE_GE_SYNC);
      }
      // else { list.getAcceptableEquipmentType().add(NEUtils.IDL_NE_DEFINITION_MAP.get(neType)); }
      else if (neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206){
        list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_150CC_GE206);
      }
      else if (neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201){
        list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_150CC_GE201);
      }
      else if (neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE){
        list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_150CC_GE201SE);
      }
    }
    else if (cardType == MIBFSP150CM.Entity.SlotTable.TYPE_PSU_INDEX) {
      list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_PSU_AC);
      list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_PSU_DC);
      if (NEUtils.isGEDevice(neType) ) {
          list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_PSU_DC24);
        }
    }
    else if (cardType == MIBFSP150CM.Entity.SlotTable.TYPE_FAN_INDEX) {
      if (! NEUtils.isGEDevice(neType) ) {
        list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_FAN);
      }
    }
    JAXBElement<AcceptableEquipmentTypeList> acceptableList = objFactory
            .createEquipmentHolderTAcceptableEquipmentTypeList(list);
    equipmentHolder.setAcceptableEquipmentTypeList(acceptableList);
    HolderStateT state = HolderStateT.EMPTY;
    if (installed) {
      if (properties.get(EquipmentSPPropertiesFSP150CM.VS.SecondaryState).contains(CMSecondaryStateTranslation.UNEQUIPPED.getMtosiString())) {
        state = HolderStateT.EXPECTED_AND_NOT_INSTALLED;
      }
      else if (properties.get(EquipmentSPPropertiesFSP150CM.VS.SecondaryState).contains(CMSecondaryStateTranslation.MISMATCHED_EQPT.getMtosiString())) {
        state = HolderStateT.MISMATCH_OF_INSTALLED_AND_EXPECTED;
      }
      else if (properties.get(EquipmentSPPropertiesFSP150CM.VS.SecondaryState).contains(CMSecondaryStateTranslation.UNASSIGNED.getMtosiString())) {
        state = HolderStateT.INSTALLED_AND_NOT_EXPECTED;
      }
      else  {
        state = HolderStateT.INSTALLED_AND_EXPECTED;
      }
    }
    JAXBElement<HolderStateT> holderState = objFactory.createEquipmentHolderTHolderState(state);
    equipmentHolder.setHolderState(holderState);
    return holder;
  }

  public static EquipmentOrHolderT nmsFixedSlotPropertiesToMTOSIEquipment(NetworkElementFSP150CM ne,
                                                                          EquipmentSPPropertiesFSP150CM properties,
                                                                          String equipmentType,
                                                                          String mismatchType)
  {
    if (equipmentType == null) {
      return null;
    }
    if (logger.isInfoEnabled()) logger.info("Equipment Properties: "+properties.toString());
    NamingAttributesT namingAttributes = NamingTranslationFactory.createNamingAttributesFixedSlotHolderCM(ne, properties);
    namingAttributes.setEqNm(MtosiConstants.DEFAULT_EQUIPMENT_NAME);
    ObjectFactory objFactory = new ObjectFactory();
    EquipmentT equipment = objFactory.createEquipmentT();
    EquipmentOrHolderT holder = objFactory.createEquipmentOrHolderT();
    // need to set name to mtosi name
    JAXBElement<String> name = objFactory.createEquipmentTDiscoveredName(MtosiConstants.DEFAULT_EQUIPMENT_NAME);
    equipment.setDiscoveredName(name);
    JAXBElement<NamingAttributesT> namingAttributesJax = objFactory.createEquipmentTName(namingAttributes);
    equipment.setName(namingAttributesJax);
    JAXBElement<Boolean> alarmReporting = objFactory.createEquipmentTAlarmReportingIndicator(Boolean.FALSE);
    equipment.setAlarmReportingIndicator(alarmReporting);
    // for System (top level module), partNumber is the modelName
    JAXBElement<String> partNumber = objFactory.createEquipmentTInstalledPartNumber(properties.get(EquipmentSPProperties.VS.PartNumber));
    equipment.setInstalledPartNumber(partNumber);
    JAXBElement<String> serialNumber = objFactory.createEquipmentTInstalledSerialNumber(properties
            .get(EquipmentSPProperties.VS.SerialNumber));
    equipment.setInstalledSerialNumber(serialNumber);
    JAXBElement<String> version = objFactory.createEquipmentTInstalledVersion(properties.get(EquipmentSPProperties.VS.HardwareVersion));
    equipment.setInstalledVersion(version);
    JAXBElement<String> manufacturer = objFactory.createEquipmentTManufacturer(ne.getMTOSIWorker().getManufacturer());
    equipment.setManufacturer(manufacturer);
    JAXBElement<String> manufacturerDate = objFactory.createEquipmentTManufacturerDate(HeaderUtils
            .formatDate(properties.get(EquipmentSPProperties.VL.ManufactureDate)));
    equipment.setManufacturerDate(manufacturerDate);
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    JAXBElement<SourceT> equipmentSource = objFactory.createEquipmentTSource(source);
    equipment.setSource(equipmentSource);
    JAXBElement<String> namingOS = objFactory.createEquipmentTNamingOS(OSFactory.getNmsName());
    equipment.setNamingOS(namingOS);
    // need to get real resource state (ReportProperties?)
    ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(MtosiUtils.getResourceState(properties.get(EquipmentSPProperties.VI.InstallState)));
    JAXBElement<ResourceStateT> equipmentResourceState = objFactory.createEquipmentTResourceState(resourceState);
    equipment.setResourceState(equipmentResourceState);
    ServiceStateT serviceState = new ServiceStateT();
    if (properties.get(EquipmentSPProperties.VI.OperState) < 3 && properties.get(EquipmentSPProperties.VI.AdminState) < 6)
    {
      serviceState.setValue(ServiceStateEnumT.fromValue(CMServiceStateTranslation.getMtosiString(properties.get(EquipmentSPProperties.VI.AdminState), properties.get(EquipmentSPProperties.VI.OperState))));
    }
    else {
      serviceState.setValue(ServiceStateEnumT.fromValue(CMServiceStateTranslation.NOT_APPLICABLE.getMtosiString()));
    }
    JAXBElement<ServiceStateT> equipmentServiceState = objFactory.createEquipmentTServiceState(serviceState);
    equipment.setServiceState(equipmentServiceState);

    int installState = properties.get(EquipmentSPProperties.VI.InstallState);
    String installedEquipment = "";
    String expectedEquipment = equipmentType;
    if (properties.get(EquipmentSPPropertiesFSP150CM.VS.SecondaryState).contains(CMSecondaryStateTranslation.UNASSIGNED.getMtosiString())) {
      expectedEquipment = "";
    }
    switch (installState)
    {
      case InstallationState.REMOVED:
        break;
      case InstallationState.PRESENT:
      case InstallationState.REINSERTED:
        if (properties.get(EquipmentSPPropertiesFSP150CM.VS.SecondaryState).contains(CMSecondaryStateTranslation.MISMATCHED_EQPT.getMtosiString())) {
          installedEquipment = mismatchType;
        } else {
          installedEquipment = equipmentType;
        }
        break;
      default:
        break;
    }
    JAXBElement<String> expectedEquipmentType = objFactory
            .createEquipmentTExpectedEquipmentObjectType(expectedEquipment);
    equipment.setExpectedEquipmentObjectType(expectedEquipmentType);
    JAXBElement<String> installedEquipmentType = objFactory
            .createEquipmentTInstalledEquipmentObjectType(installedEquipment);
    equipment.setInstalledEquipmentObjectType(installedEquipmentType);
    equipment.setVendorExtensions(getVendorExtensionsCM(ne, properties));
    holder.setEq(equipment);
    return holder;
  }

  private static EqVendorExtensionsT getBasicVendorExtensionsCM(EquipmentSPProperties props) {
    EqVendorExtensionsT extensions = new EqVendorExtensionsT();
    extensions.getAny().add(new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_ADMINISTRATION_CONTROL), String.class,
            MtosiUtils.getMtosiString(CMAdministrationControlTranslation.NOT_APPLICABLE, props.get(EquipmentSPProperties.VI.AdminState))));
    return extensions;
  }

  public static JAXBElement<EqVendorExtensionsT> getVendorExtensionsCM(NetworkElementFSP150CM ne, EquipmentSPPropertiesFSP150CM props)
  {
    ObjectFactory objFactory = new ObjectFactory();
    EqVendorExtensionsT extensions = getBasicVendorExtensionsCM(props);
    String secondaryState = null;
    NetworkElementFSP150CMMTOSIOperations mtosiWorker = (NetworkElementFSP150CMMTOSIOperations)getFSP150CMMTOSIWorker(ne);
    switch (MtosiUtils.getCardTypeFromSlotIndex(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI(), props.get(EquipmentSPPropertiesFSP150CM.VI.SlotIndex))) {
      case MIBFSP150CM.Entity.SlotTable.TYPE_GENERIC_INDEX: {
        MTOSICardModuleF3 card = mtosiWorker.getModule(props.get(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex), props.get(EquipmentSPPropertiesFSP150CM.VI.SlotIndex));
        if (card != null) {
          ModuleCardSPPropertiesFSP150CM cardProps = (ModuleCardSPPropertiesFSP150CM)card.getEquipmentSPProperties();
          if (card instanceof MTOSIEthernetModuleF3) {
            LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();
            LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET);
            LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                    LayeredParams.LrPropAdvaEthernet.TRAFFIC_MANAGEMENT_ENABLED_PARAM,
                    MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, MIB.RFC1253.TRUTH_VALUE_TRUE));
            LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                    LayeredParams.LrPropAdvaEthernet.SNMP_DYING_GASP_ENABLED_PARAM,
                    MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, ((NTUSPPropertiesFSP150CM)cardProps).get(NTUSPPropertiesFSP150CM.VI.SNMPDyingGaspEnabled)));
            if (((NTUSPPropertiesFSP150CM)cardProps).get(NTUSPPropertiesFSP150CM.VI.Voltage) != null) {
              LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                      LayeredParams.LrPropAdvaEthernet.VOLTAGE,
                      Integer.toString(((NTUSPPropertiesFSP150CM)cardProps).get(NTUSPPropertiesFSP150CM.VI.Voltage)));
            }
            if (((NTUSPPropertiesFSP150CM)cardProps).get(NTUSPPropertiesFSP150CM.VI.Temperature) != null) {
              LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                      LayeredParams.LrPropAdvaEthernet.TEMPERATURE,
                      Integer.toString(((NTUSPPropertiesFSP150CM)cardProps).get(NTUSPPropertiesFSP150CM.VI.Temperature)));
            }
            String cpeHostName = ((NTUSPPropertiesFSP150CM)cardProps).get(NTUSPPropertiesFSP150CM.VS.cpeHostName);
            if (cpeHostName != null) {
              LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_RemoteCPE);
              LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_RemoteCPE,
                      LayeredParams.LrPropAdvaRemoteCPE.CPE_HOSTNAME_PARM, cpeHostName);
              Integer remoteType = ((NTUSPPropertiesFSP150CM)cardProps).get(NTUSPPropertiesFSP150CM.VI.RemoteType);
              LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_RemoteCPE,
                      LayeredParams.LrPropAdvaRemoteCPE.REMOTE_TYPE_PARM,
                      MtosiUtils.getMtosiString(CMDeviceTypeTranslation.FSP_150CPMR, remoteType != null ? remoteType.intValue() : 5));
            }
            JAXBElement<LayeredParametersListT> transmissionParams = objFactory
                    .createConnectionTerminationPointTTransmissionParams(layeredParametersListT);
            extensions.getAny().add(transmissionParams);
          }
          secondaryState = cardProps.get(EquipmentSPPropertiesFSP150CM.VS.SecondaryState);
        }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_SCU_INDEX: {
        MTOSICardModuleF3 card = mtosiWorker.getModule(props.get(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex), props.get(EquipmentSPPropertiesFSP150CM.VI.SlotIndex));
        if (card != null) {
          secondaryState = ((ModuleCardSPPropertiesFSP150CM)card.getEquipmentSPProperties()).get(EquipmentSPPropertiesFSP150CM.VS.SecondaryState);
        }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_PSU_INDEX: {
        PowerSupplyF3 psu = mtosiWorker.getPSU(props.get(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex), props.get(EquipmentSPPropertiesFSP150CM.VI.SlotIndex));
        if (psu != null) {
          secondaryState = ((PsuSPPropertiesFSP150CM)psu.getEquipmentSPProperties()).get(EquipmentSPPropertiesFSP150CM.VS.SecondaryState);
        }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_FAN_INDEX: {
        MTOSIFanF3 fan = mtosiWorker.getFAN(props.get(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex), props.get(EquipmentSPPropertiesFSP150CM.VI.SlotIndex));
        if (fan != null) {
          secondaryState = ((EquipmentSPPropertiesFSP150CM)fan.getEquipmentSPProperties()).get(EquipmentSPPropertiesFSP150CM.VS.SecondaryState);
        }
        break;
      }
    }
    if (secondaryState != null) {
      JAXBElement<? extends String> je = new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
              MtosiConstants.VENDOR_SECONDARY_STATE), String.class, secondaryState);
      extensions.getAny().add(je);
    }
    return objFactory.createEquipmentTVendorExtensions(extensions);
  }

  public static List<EquipmentOrHolderT> nmsFanArrayToMtosiEquipmentAndHolders(NetworkElement ne, Set<EquipmentSPProperties> set) {
    List<EquipmentOrHolderT> results = new ArrayList<EquipmentOrHolderT>();
    for (EquipmentSPProperties properties : set) {
      results.add(NetworkElementMediatorFactory.createNetworkElementMediator(ne).fanToMtosiEquipment(properties));
      results.add(NetworkElementMediatorFactory.createNetworkElementMediator(ne).fanToMtosiEquipmentHolder(properties));
    }
    return results;
  }

  public static List<EquipmentOrHolderT> nmsFanArrayToMtosiEquipmentHolders(NetworkElement ne, Set<EquipmentSPProperties> set) {
    List<EquipmentOrHolderT> results = new ArrayList<EquipmentOrHolderT>();
    for (EquipmentSPProperties properties : set) {
      results.add(NetworkElementMediatorFactory.createNetworkElementMediator(ne).fanToMtosiEquipmentHolder(properties));
    }
    return results;
  }

  private static NetworkElementFSP150CMMTOSIOperations getFSP150CMMTOSIWorker(NetworkElementFSP150CM ne) {
    return (NetworkElementFSP150CMMTOSIOperations)ne.getMTOSIWorker();
  }
}
