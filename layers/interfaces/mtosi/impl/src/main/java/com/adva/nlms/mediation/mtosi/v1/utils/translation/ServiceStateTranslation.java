/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

/**
 * Created by IntelliJ IDEA. User: Lukasz Date: 2007-05-30 Time: 13:14:09 To change this template use File | Settings |
 * File Templates.
 */
public enum ServiceStateTranslation{
  UP_UP            (1, 1, "IN_SERVICE"),
  UP_DOWN          (1, 2, "OUT_OF_SERVICE"),
  UP_TEST          (1, 3, "OUT_OF_SERVICE_BY_MAINTENANCE"),
  UP_UNKNOWN       (1, 4, "OUT_OF_SERVICE"),
  UP_DORMANT       (1, 5, "OUT_OF_SERVICE"),
  UP_NOT_PRESENT   (1, 6, "OUT_OF_SERVICE"),
  UP_LAYERDOWN     (1, 7, "OUT_OF_SERVICE"),

  DOWN_UP          (2, 1, "OUT_OF_SERVICE_BY_MAINTENANCE"),
  DOWN_DOWN        (2, 2, "OUT_OF_SERVICE_BY_MAINTENANCE"),
  DOWN_TEST        (2, 3, "OUT_OF_SERVICE_BY_MAINTENANCE"),
  DOWN_UNKNOWN     (2, 4, "OUT_OF_SERVICE_BY_MAINTENANCE"),
  DOWN_DORMANT     (2, 5, "OUT_OF_SERVICE_BY_MAINTENANCE"),
  DOWN_NOT_PRESENT (2, 6, "OUT_OF_SERVICE_BY_MAINTENANCE"),
  DOWN_LAYERDOWN   (2, 7, "OUT_OF_SERVICE_BY_MAINTENANCE"),

  TEST_UP          (3, 1, "OUT_OF_SERVICE_BY_MAINTENANCE"),
  TEST_DOWN        (3, 2, "OUT_OF_SERVICE_BY_MAINTENANCE"),
  TEST_TEST        (3, 3, "OUT_OF_SERVICE_BY_MAINTENANCE"),
  TEST_UNKNOWN     (3, 4, "OUT_OF_SERVICE_BY_MAINTENANCE"),
  TEST_DORMANT     (3, 5, "OUT_OF_SERVICE_BY_MAINTENANCE"),
  TEST_NOT_PRESENT (3, 6, "OUT_OF_SERVICE_BY_MAINTENANCE"),
  TEST_LAYERDOWN   (3, 7, "OUT_OF_SERVICE_BY_MAINTENANCE"),

  NOT_APPLICABLE   (4, 8, "OUT_OF_SERVICE");
  
  /*
  operStatus
  up(1),            -- ready to pass packets
  down(2),
  testing(3),       -- in some test mode
  unknown(4),       -- status can not be determined for some reason.
  dormant(5),
  notPresent(6),    -- some component is missing
  lowerLayerDown(7) -- down due to state of lower-layer interface(s)
  */

  //------------------------------------------------------------------------------------------------------------------
  private final int    adminState;
  private final int    operState;
  private final String mtosiString;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param adminState   The MIB defined value
   * @param operState    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private ServiceStateTranslation (final int adminState, final int operState, final String mtosiString)
  {
    this.adminState   = adminState;
    this.operState    = operState;
    this.mtosiString = mtosiString;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getAdminStateValue() {
    return adminState;
  }

  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getOperStateValue () {
    return operState;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in ManagedElementMediator class.
   * @param adminState  The MIB defined value
   * @param operState   The MIB defined value
   * @return the string representation used in ManagedElementMediator class.
   */
  public static String getMtosiString(final int adminState, final int operState)
  {
    ServiceStateTranslation serviceStateTranslation = NOT_APPLICABLE;  // the return value

    for (ServiceStateTranslation tmpServiceStateTranslation : values())
    {
      if (adminState == tmpServiceStateTranslation.getAdminStateValue() && operState == tmpServiceStateTranslation.getOperStateValue())
      {
        serviceStateTranslation = tmpServiceStateTranslation;
        break;
      }
    }
    return serviceStateTranslation.getMtosiString();
  }

  /**
   * Returns the string representation used in ManagedElementMediator class.
   * @param mtosiString  The MTOSI representation
   * @param operState The MIB defined value
   * @return the string representation used in ManagedElementMediator class.
   */
  public static int getAdminStateValue(final String mtosiString, final int operState)
  {
    ServiceStateTranslation serviceStateTranslation = NOT_APPLICABLE;  // the return value

    for (ServiceStateTranslation tmpServiceStateTranslation : values())
    {
      if (mtosiString.equals(tmpServiceStateTranslation.getMtosiString()) && operState == tmpServiceStateTranslation.getOperStateValue())
      {
        serviceStateTranslation = tmpServiceStateTranslation;
        break;
      }
    }
    return serviceStateTranslation.getAdminStateValue();
  }

  /**
   * Returns the string representation used in ManagedElementMediator class.
   * @param mtosiString  The MTOSI representation
   * @param adminState The MIB defined value
   * @return the string representation used in ManagedElementMediator class.
   */
  public static int getOperStateValue(final String mtosiString, final int adminState)
  {
    ServiceStateTranslation serviceStateTranslation = NOT_APPLICABLE;  // the return value

    for (ServiceStateTranslation tmpServiceStateTranslation : values())
    {
      if (mtosiString.equals(tmpServiceStateTranslation.getMtosiString()) && adminState == tmpServiceStateTranslation.getAdminStateValue())
      {
        serviceStateTranslation = tmpServiceStateTranslation;
        break;
      }
    }
    return serviceStateTranslation.getOperStateValue();
  }
}
