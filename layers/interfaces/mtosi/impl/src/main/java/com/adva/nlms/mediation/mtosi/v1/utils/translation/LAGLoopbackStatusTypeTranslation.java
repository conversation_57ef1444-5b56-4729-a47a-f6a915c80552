/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.mediation.common.serviceProvisioning.LAGSPPropertiesFSP150CC825;

public enum LAGLoopbackStatusTypeTranslation implements TranslatableEnum {
  NONE                    (0, "None"),
  TERMINAL                (1, "Terminal"),
  TERMINAL_TIMED          (2, "TerminalTimed"),
  FACILITY                (3, "Facility"),
  FACILITY_TIMED          (4, "FacilityTimed"),
  VLAN_TERMINAL           (5, "VLANTerminal"),
  VLAN_FACILITY           (6, "VLANFacility"),
  REMOTE_EFMOAM           (7, "RemoteEFMOAM"),
  REMOTE_EFMOAM_TAIL_END  (8, "RemoteEFMOAMTailEnd"),
  NOT_APPLICABLE          (9, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private LAGLoopbackStatusTypeTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }

  public static String getLoopbackList(LAGSPPropertiesFSP150CC825 lagspPropertiesFSP150CC825) {
    int bitField = 0x1; //001
    StringBuilder resultBuffer = new StringBuilder();
    for(int i = 1; i <= 3; i++) {
      if((lagspPropertiesFSP150CC825.get(LAGSPPropertiesFSP150CC825.VI.VLanLoopbackMask) & bitField) != 0) {
        if(resultBuffer.length() > 0) {
          resultBuffer.append(",");
        }
        switch(i) {
          case 1:
            resultBuffer.append(lagspPropertiesFSP150CC825.get(LAGSPPropertiesFSP150CC825.VS.VLanLoopback1));
            break;
          case 2:
            resultBuffer.append(lagspPropertiesFSP150CC825.get(LAGSPPropertiesFSP150CC825.VS.VLanLoopback2));
            break;
          case 3:
            resultBuffer.append(lagspPropertiesFSP150CC825.get(LAGSPPropertiesFSP150CC825.VS.VLanLoopback3));
            break;
        }
      }
      bitField <<=1;
    }
    return resultBuffer.toString();
  }

  public static String getInnerLoopbackList(LAGSPPropertiesFSP150CC825 lagspPropertiesFSP150CC825) {
    int bitField = 0x1; //001
    StringBuilder resultBuffer = new StringBuilder();
    for(int i = 1; i <= 3; i++) {
      if((lagspPropertiesFSP150CC825.get(LAGSPPropertiesFSP150CC825.VI.InnerVLanLoopbackMask) & bitField) != 0) {
        if(resultBuffer.length() > 0) {
          resultBuffer.append(",");
        }
        switch(i) {
          case 1:
            resultBuffer.append(lagspPropertiesFSP150CC825.get(LAGSPPropertiesFSP150CC825.VS.InnerVLanLoopback1));
            break;
          case 2:
            resultBuffer.append(lagspPropertiesFSP150CC825.get(LAGSPPropertiesFSP150CC825.VS.InnerVLanLoopback2));
            break;
          case 3:
            resultBuffer.append(lagspPropertiesFSP150CC825.get(LAGSPPropertiesFSP150CC825.VS.InnerVLanLoopback3));
            break;
        }
      }
      bitField <<=1;
    }
    return resultBuffer.toString();
  }
}
