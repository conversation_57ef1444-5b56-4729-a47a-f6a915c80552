/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.mediation.common.serviceProvisioning.FTPFSP150CPSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FTPSPProperties;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.RenameTPResponseT;
import v1.tmf854ext.adva.RenameTPT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public abstract class RenameTPWorker extends AbstractMtosiWorker {
	Logger LOG = LogManager.getLogger(this.getClass().getName());

  protected RenameTPT mtosiBody;
	protected RenameTPResponseT response = new RenameTPResponseT();
	protected NetworkElement ne;
	protected NamingAttributesT currentName;
	protected NamingAttributesT newName;
	protected FTP ftp;

	public RenameTPWorker(RenameTPT mtosiBody, Holder<HeaderT> mtosiHeader, NamingAttributesT currentName, NetworkElement ne)
	{
    super(mtosiHeader, "renameTP", "renameTP", "renameTPResponse");
    this.mtosiBody = mtosiBody;
    this.currentName = currentName;
    this.ne = ne;
  }

  @Override
  protected void parse() throws Exception {
    if ((this.newName = mtosiBody.getNewTPName()) == null ||
            newName.getMeNm() == null || newName.getMdNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The new TP Name has not been specified.");
    }
    if(!newName.getMeNm().equals(currentName.getMeNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The existing and new Name reference different Managed Elements.");
    }
    if(!newName.getMdNm().equals(currentName.getMdNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The existing and new Name reference different Management Domains.");
    }
  }


  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
   validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  protected void mediateFtp() throws Exception {
    if ((ftp = ManagedElementFactory.getFtp(currentName)) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The specified TP does not exist.");
    }

    FTP ftpWithNewName;
    try {
      ftpWithNewName = ManagedElementFactory.getFtp(newName);
    } catch (ProcessingFailureException e) {
      ftpWithNewName = null;
    }
    if (ftpWithNewName != null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "An FTP already exists with the specified new Name.");
    }

    String newFTPName = newName.getFtpNm();
    if (newFTPName == null || newFTPName.length()==0) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The new FTP Name has not been specified.");
    }

    FTPSPProperties newProps = new FTPFSP150CPSPProperties(newFTPName);
    transactFtp(newProps);
  }

  private void transactFtp(FTPSPProperties ftpProps) throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "RenameTPWorker");
    try
    {
      logSecurity(ne, SystemAction.ModifyNetwork, "ftpNm=" + ftp.getFTPName());
      ftp.setFTPSPProperties(ftpProps);
      NetTransactionManager.commitNetTransaction(id);
      ne.logSROperation(SROperationState.FTP_RENAME_SUCCESS, currentName.getFtpNm(), newName.getFtpNm());
    }
    catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally
    {
      NetTransactionManager.ensureEnd(id);
    }
  }

  @Override
  public RenameTPResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}