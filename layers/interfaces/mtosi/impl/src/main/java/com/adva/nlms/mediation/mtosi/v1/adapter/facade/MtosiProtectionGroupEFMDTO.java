/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.adapter.facade;

import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.ProtectionGroupF3Attr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiProcessingFailureException;
import ws.v1.tmf854.ProcessingFailureException;
import com.adva.nlms.mediation.config.NetworkElement;

import java.util.List;

public interface MtosiProtectionGroupEFMDTO {
  public void validate() throws ProcessingFailureException, MtosiProcessingFailureException;
  public DTO<ProtectionGroupF3Attr> getMODTO();
  public MtosiMOFacade getMtosiFacade();
  public NetworkElement getNetworkElement();
  public Integer get_AllocatedNumber();
  public String get_FragmentServerLayer();
  public String getPGName();
  public String getMdName();
  public String getMeName();
  public String get_Revertive();
  public String get_ProtectionSwitchMode();
  public int getPgNumber();
  public List<MtosiTerminationPointDTOImpl> getPorts();

}
