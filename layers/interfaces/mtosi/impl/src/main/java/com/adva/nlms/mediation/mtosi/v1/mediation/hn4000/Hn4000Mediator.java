/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.hn4000;

import com.adva.nlms.common.config.EntityClass;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.SFPSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ShelfSPPropertiesHN4000;
import com.adva.nlms.mediation.config.NetworkElementDAO;
import com.adva.nlms.mediation.config.NoSuchEntityException;
import com.adva.nlms.mediation.config.hn4000.ModuleHN4000DBImpl;
import com.adva.nlms.mediation.config.hn4000.NetworkElementHN4000;
import com.adva.nlms.mediation.config.hn4000.ObjectStateFieldHN4000;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.mediation.NetworkElementMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.ManagedElementMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import v1.tmf854.EqVendorExtensionsT;
import v1.tmf854.EquipmentHolderT;
import v1.tmf854.EquipmentOrHolderT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.MEVendorExtensionsT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ServiceStateT;

import jakarta.xml.bind.JAXBElement;
import javax.xml.namespace.QName;

/**
 * This class is a Network Element Mediator for the HN 4000 boxes.
 */
public class Hn4000Mediator extends NetworkElementMediator {
  protected NetworkElementHN4000 ne;
  private NetworkElementDAO networkElementDAO;

  public Hn4000Mediator(NetworkElementHN4000 ne, NetworkElementDAO networkElementDAO) {
    super(ne);
    this.ne = ne;
    this.networkElementDAO = networkElementDAO;
  }
  @Override
  protected JAXBElement<EqVendorExtensionsT> getVendorExtensions(EquipmentSPProperties props) {
	    ObjectFactory objFactory = new ObjectFactory();
	    EqVendorExtensionsT extensions = new EqVendorExtensionsT();
	    String adminControl = "";
	    if (props.get(EquipmentSPProperties.VI.AdminState) < 4) {
	      adminControl = MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, props.get(EquipmentSPProperties.VI.AdminState));
	    }
	    extensions.getAny().add(new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
	            MtosiConstants.VENDOR_ADMINISTRATION_CONTROL), String.class, adminControl));

	    Integer objectState = props.get(EquipmentSPProperties.VI.OperState); //The Object State is stored in the operState field.
	    if (objectState != null) {
	    	String secondaryState = ObjectStateFieldHN4000.getSecondaryStateString(objectState);
		    extensions.getAny().add(new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
		            MtosiConstants.VENDOR_SECONDARY_STATE), String.class, secondaryState));
	    	
	    }
	    if (props instanceof SFPSPProperties) {
	    	if (((SFPSPProperties) props).get(SFPSPProperties.VI.SFPNumber) == 1) {
	    		
	    	ShelfSPPropertiesHN4000 shelfProps = (ShelfSPPropertiesHN4000) ne.getMTOSIWorker().getShelf(props.get(EquipmentSPProperties.VI.ChassisId)).getEquipmentSPProperties();
	        extensions.getAny().add(new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
	        		MtosiConstants.VENDOR_IS_MASTER), String.class,
                  MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, shelfProps.get(ShelfSPPropertiesHN4000.VI.IsMaster))));
	    	}
	    }
	    return objFactory.createEquipmentTVendorExtensions(extensions);
	  }


  @Override
  protected JAXBElement<MEVendorExtensionsT> getVendorExtensions() {
    ObjectFactory objFactory = new ObjectFactory();
    MEVendorExtensionsT extensions = new MEVendorExtensionsT();
    populateIpAddress(extensions);
    ManagedElementMediator.populateManagementParameters(ne, extensions);
    populateDiscoveryState(extensions);
    populateLoopbackPhysAddress(extensions);
    return objFactory.createManagedElementTVendorExtensions(extensions);
  }

  private void populateDiscoveryState(MEVendorExtensionsT extensions) {
	    String discoveryState = networkElementDAO.getDiscoveryState(ne.getID()).getGUIString();
	    JAXBElement<? extends String> je = new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
	            MtosiConstants.VENDOR_DISCOVERY_STATE), String.class, discoveryState);
	    extensions.getAny().add(je);
}

  private void populateLoopbackPhysAddress(MEVendorExtensionsT extensions) {
    String loopbackAddr = ne.getLoopbackPhysAddress();
    if (loopbackAddr == null) {
      loopbackAddr = "";
    }

    ObjectFactory objFactory = new ObjectFactory();
    // layers
    LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet,
            LayeredParams.LrPropAdvaEthernet.LOOPBACK_PHYS_ADDRESS_PARAM, loopbackAddr);

    JAXBElement<LayeredParametersListT> transmissionParams = objFactory.createPhysicalTerminationPointTTransmissionParams(layeredParametersListT);

    extensions.getAny().add(transmissionParams);
  }

protected void populateIpAddress (MEVendorExtensionsT extensions) {
    String ipAddress = ne.getIPAddress();
    JAXBElement<? extends String> je = new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_IPADDRESS), String.class, ipAddress);
    extensions.getAny().add(je);
    JAXBElement jeMask = new JAXBElement<String>(new
            QName(MtosiConstants.VENDOR_NAMESPACE,MtosiConstants.VENDOR_SUBNET_MASK), String.class, ne.getInterfaceMask());
    extensions.getAny().add(jeMask);
  }

  @Override
  public String getRelativeNameForProperties(EquipmentSPProperties properties) {
    int classType = properties.get(EquipmentSPProperties.VI.ClassType);
    String relativeName = "";
    switch (classType) {
      case EntityClass.SHELF:
        relativeName = MtosiConstants.SHELF_TEXT + properties.get(EquipmentSPProperties.VI.ChassisId);
        break;
      case EntityClass.MODULE:
        if (properties.isSFP()) {
          relativeName = getRelativeNameModule(properties);
        } else {
          relativeName = MtosiConstants.SHELF_TEXT + properties.get(EquipmentSPProperties.VI.ChassisId) + MtosiConstants.SLOT_TEXT +
                  properties.get(EquipmentSPProperties.VI.RelativePosition);
        }
        break;
      case EntityClass.POWER_SUPPLY:
        relativeName = MtosiConstants.SHELF_TEXT + properties.get(EquipmentSPProperties.VI.ChassisId) + MtosiConstants.SLOT_TEXT +
                (properties.get(EquipmentSPProperties.VI.RelativePosition) == 1 ? MtosiConstants.SLOT_PSUA : MtosiConstants.SLOT_PSUB);
        break;
      case EntityClass.PORT:
        relativeName = getRelativeNamePort(properties);
        break;
      case EntityClass.FAN:
        relativeName = MtosiConstants.SHELF_TEXT + properties.get(EquipmentSPProperties.VI.ChassisId) + MtosiConstants.SLOT_TEXT +
                MtosiConstants.SLOT_FAN;
        break;
      default:
    }
    return relativeName;
  }

  protected String getRelativeNameModule(EquipmentSPProperties properties) {
    if (!(properties instanceof SFPSPProperties)) {
      throw new IllegalArgumentException("getRelativeNameModule is accepting only SFPSPProperties!");
    }
    SFPSPProperties propertiesSFP = (SFPSPProperties) properties;

    return MtosiConstants.SHELF_TEXT + propertiesSFP.get(EquipmentSPProperties.VI.ChassisId) +
            MtosiConstants.SUBSLOT_HN_SFP + propertiesSFP.get(SFPSPProperties.VI.SFPNumber);
  }

  private static String getRelativeNamePort(EquipmentSPProperties properties) {
    if (!(properties instanceof SFPSPProperties)) {
      throw new IllegalArgumentException("getRelativeNamePort is accepting only SFPSPProperties!");
    }
    SFPSPProperties propertiesSFP = (SFPSPProperties) properties;

    return MtosiConstants.SHELF_TEXT + propertiesSFP.get(EquipmentSPProperties.VI.ChassisId) +
            MtosiConstants.PORT_HN_SFP + propertiesSFP.get(SFPSPProperties.VI.SFPNumber);
  }

  @Override
  protected ServiceStateT getServiceState (EquipmentSPProperties properties, String objectType) {
	  if(objectType.equals(MtosiConstants.EQUIPMENT_PSU))
	  {
		  return MtosiUtils.getHnServiceStateFromOper(properties.get(EquipmentSPProperties.VI.OperState));
	  }
	  else
	  {
		  return MtosiUtils.getHnServiceStateFromObject(properties.get(EquipmentSPProperties.VI.OperState));
	  }
	  
	  
  }

  @Override
  protected JAXBElement<EqVendorExtensionsT> getEquipmentVendorExtensions(EquipmentSPProperties properties) {
    if (!(properties instanceof EquipmentSPPropertiesHN4000)) {
      throw new IllegalArgumentException("getEquipmentVendorExtensions is accepting only EquipmentSPPropertiesHN4000!");
    }
    EquipmentSPPropertiesHN4000 props = (EquipmentSPPropertiesHN4000) properties;
    ObjectFactory objFactory = new ObjectFactory();
    EqVendorExtensionsT extensions = new EqVendorExtensionsT();
    String adminControl = "";
    if (props.get(EquipmentSPProperties.VI.AdminState) < 4) {
      adminControl = MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, props.get(EquipmentSPProperties.VI.AdminState));
    }
    extensions.getAny().add(new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_ADMINISTRATION_CONTROL), String.class, adminControl));
    final String secondaryState = props.get(EquipmentSPPropertiesHN4000.VS.SecondaryState);
    if (secondaryState != null) {
      extensions.getAny().add(new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
              MtosiConstants.VENDOR_SECONDARY_STATE), String.class, secondaryState));
    }
    return objFactory.createEquipmentTVendorExtensions(extensions);
  }

  @Override
  @SuppressWarnings("unchecked")
  protected String getShelfEquipment(EquipmentSPProperties properties) {

		if (properties.get(EquipmentSPProperties.VI.RelativePosition) == 1) {
			String neEquipmentType = MtosiConstants.EQUIPMENT_HN4000;
//			Equipment shelf = ne.getMTOSIWorker().getShelf(properties.get(EquipmentSPProperties.VI.ChassisId));
      ModuleHN4000DBImpl moduleHN4000DB = null;
      try {
        moduleHN4000DB = (ModuleHN4000DBImpl)ne.getEntityImpl(properties.get(EquipmentSPProperties.VE.Index)).getEntityDBImpl();
      } catch (NoSuchEntityException e) {
          //e.printStackTrace();
      }

      if (moduleHN4000DB == null)
        return neEquipmentType;

      String productType = moduleHN4000DB.getEquippedTypeString();
//      String productType = shelf.toString();
			if (productType != null && productType.length() > 0) {
				neEquipmentType = productType;
			}
			return neEquipmentType;
		}
		return ne.getMTOSIWorker().getProductName();
		// return properties.get(EquipmentSPProperties.VS.ModelName);
	}

  @Override
  protected ServiceStateT getShelfServiceState (EquipmentSPProperties properties) {
    return MtosiUtils.getHnServiceStateFromObject(properties.get(EquipmentSPProperties.VI.OperState));
  }

  @Override
  public EquipmentOrHolderT sfpToMtosiEquipment(EquipmentSPProperties properties) {
    EquipmentOrHolderT equipment = super.sfpToMtosiEquipment(properties);

    ObjectFactory objFactory = new ObjectFactory();
    // serviceState
    equipment.getEq().setServiceState(objFactory.createEquipmentTServiceState(getServiceState(properties, MtosiConstants.EQUIPMENT_SFP)));

    return equipment;
  }

	/**
	 * Hatteras only has one PSU type PSU DC
	 **/
	@Override
	protected void getAcceptablePSUEquipmentTypes(ObjectFactory objFactory, EquipmentHolderT equipmentHolder, EquipmentSPProperties properties) {

		EquipmentHolderT.AcceptableEquipmentTypeList list = objFactory.createEquipmentHolderTAcceptableEquipmentTypeList();
		list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_PSU_DC);
		equipmentHolder.setAcceptableEquipmentTypeList(objFactory.createEquipmentHolderTAcceptableEquipmentTypeList(list));
	}

	/**
	 * Hatteras only has one PSU type - PSU DC
	 */
	@Override
	protected String getPsuType(EquipmentSPProperties properties) {
		return MtosiConstants.EQUIPMENT_PSU_DC;
	}


}
