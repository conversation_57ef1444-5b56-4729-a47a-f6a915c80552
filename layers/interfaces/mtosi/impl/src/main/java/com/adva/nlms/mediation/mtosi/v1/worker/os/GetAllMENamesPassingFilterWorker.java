/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.os;

import java.util.Set;
import jakarta.xml.ws.Holder;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import v1.tmf854.GetAllMENamesPassingFilterT;
import v1.tmf854.GetAllObjectNamesResponseT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.NamingAttributesT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.config.ConfigCtrlImpl;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.ManagedElementMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;

public class GetAllMENamesPassingFilterWorker extends AbstractMtosiWorker {
  protected GetAllMENamesPassingFilterT mtosiBody;
  protected GetAllObjectNamesResponseT response = new GetAllObjectNamesResponseT();
  protected NamingAttributesT mdName;
  protected NamingAttributesListT namesList;

  public GetAllMENamesPassingFilterWorker(GetAllMENamesPassingFilterT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getAllMENamesPassingFilter", "getAllMENamesPassingFilter", "getAllMENamesPassingFilterResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    if ((mdName = mtosiBody.getMdName()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!NamingTranslationFactory.isManagementDomain(mdName)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!mdName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MD_NOT_FOUND);
    }
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
  	//No validation required.
  }

  @Override
  protected void mediate() throws Exception {
    final Set<NetworkElement> neSet = ConfigCtrlImpl.get().getHandlers().getNeHdlr().getNetworkElements();
    namesList = ManagedElementMediator.nmsNeListToMtosiManagedElementNamesList(MtosiUtils.getSupportedNE(neSet), mtosiBody.getFilter());
  }

  @Override
  protected void response() throws Exception {
    response.setNames(namesList);
  }

  @Override
  public GetAllObjectNamesResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
