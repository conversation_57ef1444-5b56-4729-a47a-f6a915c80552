/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp20X;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.common.snmp.f3.LinkLossFwdTriggerTypes;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.port.MTOSIPortF3;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSyncEAutoNegTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSyncQLTranslation;
import v1.tmf854.DirectionalityT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ResourceStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.common.util.BitFields;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.mediation.common.serviceProvisioning.NETPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.WANPortSPProperties;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAcceptableFramePolicyTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMConnectorTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMDispositionTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMEfmOamDiscoveryStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMEfmOamModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMLoopbackStatusSwapSADATranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMLoopbackStatusTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMServiceStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.DuplexModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.GE20XAutoNegotiationTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.GE20XLlfTxActionTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.GE20XMdiModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.GE20XRemoteLinkIdsTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.GE20XSfpMediaTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.MediaTypeTranslation;

/**
 * This class is an FSP 150 GE20X generic port MTOSI Translator.
 * It is subtly different enough from the CM translator to warrant it's own class
 */
public abstract class PortFSP150GE20XTranslator extends MtosiTranslator {
  protected MTOSIPortF3 port;

  public PortFSP150GE20XTranslator(MTOSIPortF3 port) {
    this.port = port;
  }

  protected PhysicalTerminationPointT toMtosiPTP(boolean isACCPort) throws ProcessingFailureException {
    NETPortSPPropertiesFSP150CM portProperties = port.getPortSPProperties();
    NetworkElement ne = port.getNE();
    int neType = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI();
    ObjectFactory objFactory = new ObjectFactory();
    PhysicalTerminationPointT physicalTerminationPointT = objFactory.createPhysicalTerminationPointT();

    // PTP element name
    NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributes(port);
    physicalTerminationPointT.setName(objFactory.createPhysicalTerminationPointTName(namingAttributes));

    // discoverdName
    final String ptpNm = namingAttributes.getPtpNm();
    if (ptpNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
    }
    physicalTerminationPointT.setDiscoveredName(objFactory.createPhysicalTerminationPointTDiscoveredName(ptpNm));

    // namingOS
    physicalTerminationPointT.setNamingOS(objFactory.createPhysicalTerminationPointTNamingOS(OSFactory.getNmsName()));

    // source
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    physicalTerminationPointT.setSource(objFactory.createPhysicalTerminationPointTSource(source));

    // resource state
    ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ResourceStateEnumT.INSTALLED);
    physicalTerminationPointT.setResourceState(objFactory.createPhysicalTerminationPointTResourceState(resourceState));

    // direction
    physicalTerminationPointT.setDirection(objFactory.createPhysicalTerminationPointTDirection(DirectionalityT.D_BIDIRECTIONAL));

    // edgePoint
    physicalTerminationPointT.setEdgePoint(objFactory.createPhysicalTerminationPointTEdgePoint(Boolean.TRUE));

    // layers
    LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    // -------start Layer--------
    boolean isCopper = false;
    final int mediaType = portProperties.get(NETPortSPPropertiesFSP150CM.VI.MediaType);
    if (mediaType == MIBFSP150CM.Facility.EthernetNetPortTable.MEDIA_TYPE_COPPER ||
    		mediaType == MIBFSP150CM.Facility.EthernetNetPortTable.MEDIA_TYPE_COPPERSFP ) {
    	isCopper = true;
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL,
              LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.ACTIVE_PARAM);

      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL,
              LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.INACTIVE_PARAM);
    } else if (mediaType == MIBFSP150CM.Facility.EthernetNetPortTable.MEDIA_TYPE_FIBER) {
        LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL);
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL,
                LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.INACTIVE_PARAM);

      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL,
              LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.ACTIVE_PARAM);

      // -------end of Layer-------

      // -------start Layer--------
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_PHYSICAL_OPTICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_PHYSICAL_OPTICAL,
              LayeredParams.LrElectricalAndOptical.CONNECTOR_TYPE_PARAM,
              MtosiUtils.getMtosiString(CMConnectorTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.ConnectorType)));
      
      // -------end of Layer-------

      // -------start Layer--------
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_Optical_Channel);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_Optical_Channel,
              LayeredParams.LROpticalChannel.SFP_REACH_PARAM,
              String.valueOf(portProperties.get(NETPortSPPropertiesFSP150CM.VI.SFPReach)));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_Optical_Channel,
              LayeredParams.LROpticalChannel.SFP_LASER_WAVE_LENGTH_PARAM,
              String.valueOf(portProperties.get(NETPortSPPropertiesFSP150CM.VI.SfpLaserWaveLength)));
      

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_Optical_Channel,
              LayeredParams.LROpticalChannel.SFP_MEDIA_TYPE_PARAM,
              MtosiUtils.getMtosiString(GE20XSfpMediaTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.SfpMediaType)));

    }
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.ADMINISTRATION_CONTROL_PARAM,
            MtosiUtils.getMtosiString(CMAdministrationControlTranslation.NOT_APPLICABLE, portProperties.get(WANPortSPProperties.VI.IfAdminStatus)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.SERVICE_STATE_PARAM,
            CMServiceStateTranslation.getMtosiString(portProperties.get(WANPortSPProperties.VI.IfAdminStatus), portProperties.get(WANPortSPProperties.VI.IfOperStatus)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.AUTO_NEGOTIATION_PARAM, 
            GE20XAutoNegotiationTranslation.getMtosiString(portProperties.get(WANPortSPProperties.VI.PortSpeed)));

    String speedRate = GE20XAutoNegotiationTranslation.getSpeedRate(portProperties.get(WANPortSPProperties.VI.PortSpeed));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.ADMINISTRATIVE_SPEED_RATE_PARAM, speedRate);

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.ACTUAL_SPEED_RATE_PARAM, 
            GE20XAutoNegotiationTranslation.getMtosiString(portProperties.get(WANPortSPProperties.VI.PortSpeed)).equals("Enabled") ?
            	portProperties.get(WANPortSPProperties.VI.PortNegotiatedSpeed) == 0 ? 
            		"0" : GE20XAutoNegotiationTranslation.getSpeedRate(portProperties.get(WANPortSPProperties.VI.PortNegotiatedSpeed)) : speedRate);

    String duplex = GE20XAutoNegotiationTranslation.getDuplexMode(portProperties.get(WANPortSPProperties.VI.PortSpeed));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.DUPLEX_MODE_PARAM, duplex);

    String actualDuplex;
    if (portProperties.get(WANPortSPProperties.VI.PortNegotiatedSpeed) == 0) {
      if (GE20XAutoNegotiationTranslation.getMtosiString(portProperties.get(WANPortSPProperties.VI.PortSpeed)).equals("Enabled")) {
        actualDuplex = DuplexModeTranslation.getMtosiString(MIB.RFC1253.TRUTH_VALUE_TRUE);
      } else {
        actualDuplex = duplex;
      }
    } else {
      actualDuplex = GE20XAutoNegotiationTranslation.getDuplexMode(portProperties.get(WANPortSPProperties.VI.PortNegotiatedSpeed));
    }

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.ACTUAL_DUPLEX_MODE_PARAM, actualDuplex);

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.MAXIMUM_FRAME_SIZE_PARAM, Integer.toString(portProperties.get(WANPortSPProperties.VI.IfMtu)));
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.CONNECTIONLESS_PORT_PARAM, MtosiConstants.TRUE);  //TODO fix me for Protected Network ports

	// INTERFACE_TYPE_PARAM delegated to specific methods
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.INTERFACE_TYPE_PARAM, getInterfaceType());

    // PORT_TP_ROLE_STATE_PARAM delegated to specific methods
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.PORT_TP_ROLE_STATE_PARAM, getTpRoleState());

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.NUMBER_OF_TRAFFIC_CLASSES_PARAM, "1");

 
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.PHYS_ADDRESS_PARAM, port.getPortSPProperties().get(WANPortSPProperties.VS.IfPhysAddress));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.MAX_NUM_FDFRS_PARAM, getMaxNumFDFrs());
    
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.NUM_CONFIGURED_FDFRS_PARAM, Integer.toString(portProperties.get(WANPortSPProperties.VI.NumConfiguredFDFrs)));
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.SECONDARY_STATE_PARAM, portProperties.get(NETPortSPPropertiesFSP150CM.VS.SecondaryState));

    // PORT_MODE_PARAM delegated to specific methods

    addPortMode(layeredParametersListT);

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.MEDIA_TYPE_PARAM,
            MtosiUtils.getMtosiString(MediaTypeTranslation.NOT_APPLICABLE, mediaType));

    // JumboFramesEnabled 
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.JUMBO_FRAMES_ENABLED_PARAM,
            MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.JumboFramesEnabled)));

	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
	        LayeredParams.LrPropAdvaEthernet.TRANSMIT_PAUSE_FRAMES_ENABLED_PARAM,
	        MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.TxPauseEnabled)));
//deprecated	
//	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
//	        LayeredParams.LrPropAdvaEthernet.RECEIVE_PAUSE_FRAMES_DISPOSITION_PARAM,
//	        MtosiUtils.getMtosiString(CMDispositionTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.RxPauseDisposition)));
//
      if (isACCPort) {
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.TAGGED_FRAMES_ENABLED_PARAM, 
            CMAcceptableFramePolicyTranslation.getTagged(portProperties.get(NETPortSPPropertiesFSP150CM.VI.AfpType)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.UNTAGGED_FRAMES_ENABLED_PARAM, 
            CMAcceptableFramePolicyTranslation.getUnTagged(portProperties.get(NETPortSPPropertiesFSP150CM.VI.AfpType)));
      }

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.LOOPBACK_STATUS_TYPE_PARAM,
            MtosiUtils.getMtosiString(CMLoopbackStatusTypeTranslation.NOT_APPLICABLE, portProperties.get(WANPortSPProperties.VI.PortLoopback)));

    if (portProperties.get(WANPortSPProperties.VI.PortLoopback) == MIBFSP150CM.Facility.EthernetNetPortTable.LOOPBACK_CONFIG_EFM_OAM_REMOTE_TIMED
            || portProperties.get(WANPortSPProperties.VI.PortLoopback) == MIBFSP150CM.Facility.EthernetNetPortTable.LOOPBACK_CONFIG_FACILITY_PORT_TIMED
            ||  portProperties.get(WANPortSPProperties.VI.PortLoopback) == MIBFSP150CM.Facility.EthernetNetPortTable.LOOPBACK_CONFIG_TERMINAL_PORT_TIMED) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
              LayeredParams.LrPropAdvaEthernet.LOOPBACK_STATUS_TIME_PARAM, Integer.toString(portProperties.get(NETPortSPPropertiesFSP150CM.VI.LoopbackTime)));
    }

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.LOOPBACK_STATUS_SWAP_SADA_PARAM,
            MtosiUtils.getMtosiString(CMLoopbackStatusSwapSADATranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.LoopbackSwapSADA)));

    if (portProperties.get(WANPortSPProperties.VI.PortLoopback) == MIBFSP150CM.Facility.EthernetNetPortTable.LOOPBACK_CONFIG_FACILITY_VLAN
            || portProperties.get(WANPortSPProperties.VI.PortLoopback) == MIBFSP150CM.Facility.EthernetNetPortTable.LOOPBACK_CONFIG_TERMINAL_VLAN) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
              LayeredParams.LrPropAdvaEthernet.LOOPBACK_STATUS_VLAN_LIST_PARAM, CMLoopbackStatusTypeTranslation.getOuterLoopbackList(portProperties));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
              LayeredParams.LrPropAdvaEthernet.LOOPBACK_STATUS_INNER_VLAN_LIST_PARAM, CMLoopbackStatusTypeTranslation.getInnerLoopbackList(portProperties));
      }

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_ENABLED_PARAM,
            MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdEnabled)));

    if (portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdEnabled) == MIB.RFC1253.TRUTH_VALUE_TRUE) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
              LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_TRIGGER_TYPES_PARAM,
              LinkLossFwdTriggerTypes.getMtosiString(portProperties.get(NETPortSPPropertiesFSP150CM.Vb.LinkLossFwdTriggerTypes)));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
              LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_DELAY_PARAM, Integer.toString(portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdDelay)));
    }

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_ACTIVE_PARAM,
            MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdActive)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_PARTNER_ENABLED_PARAM,
            MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdPartnerEnabled)));

    if (portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdEnabled) == MIB.RFC1253.TRUTH_VALUE_TRUE) {
    	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.LOCAL_LINK_ID_PARAM, Integer.toString(portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdLocalLinkId)));
    }

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.REMOTE_LINK_IDS_PARAM,
            GE20XRemoteLinkIdsTranslation.getMtosiString( isACCPort ? 6 : 2 ,portProperties.get(NETPortSPPropertiesFSP150CM.Vb.LinkLossFwdRemoteLinkIds)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_TX_ACTION_TYPE_PARAM,
            MtosiUtils.getMtosiString(GE20XLlfTxActionTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdTxActionType)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_RX_RLD_LINK_IDS_PARAM,
            GE20XRemoteLinkIdsTranslation.getMtosiString(2 ,BitFields.intToBytes(portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdRxRLDLinkIds),1)));
    
    if (isCopper) {
    	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.MDI_MODE_PARAM,
                MtosiUtils.getMtosiString(GE20XMdiModeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.MDIType)));
    	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.ACTUAL_MDI_MODE_PARAM,
                MtosiUtils.getMtosiString(GE20XMdiModeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.MDIMode)));
        String autoNegClockMode =  MtosiUtils.getMtosiString(CMSyncEAutoNegTranslation.NOT_APPLICABLE,portProperties.get(WANPortSPProperties.VI.PortSpeed) );
        if(autoNegClockMode.equals(CMSyncEAutoNegTranslation.Master.getMtosiString()) || autoNegClockMode.equals(CMSyncEAutoNegTranslation.Slave.getMtosiString()) )  {
            LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                    LayeredParams.LrPropAdvaEthernet.AUTONEG_CLOCK_MODE,
                    autoNegClockMode);
        }
    }

    if(portProperties.get(NETPortSPPropertiesFSP150CM.VI.AutoDiagEnabled) != null){
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
          LayeredParams.LrPropAdvaEthernet.AUTO_DIAG_ENABLED,
          MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.AutoDiagEnabled)));
    }
    // -------end of Layer-------

    if (neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE) {
	    // -------start PROP_ADVA_SyncEthernet Layer--------
	    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet);
	    String syncEAdminControl = MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.SyncEAdministrationControl));
	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
	            LayeredParams.PropAdvaSyncEthernet.SYNCE_ADMINISTRATION_CONTROL_PARAM, syncEAdminControl);
	    if (!syncEAdminControl.equals(AdministrationControlTranslation.DISABLED.getMtosiString())) {
		    String qlModeAdminControl = MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.QLModeAdministrationControl));
		    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
		            LayeredParams.PropAdvaSyncEthernet.QL_MODE_ADMINISTRATION_CONTROL_PARAM, 
		            qlModeAdminControl);
		    
		    if (qlModeAdminControl.equals("Enabled")) {
		    	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
		            LayeredParams.PropAdvaSyncEthernet.EXPECTED_QL_PARAM, 
		            MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.ExpectedQL)));
		    }
		    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
		            LayeredParams.PropAdvaSyncEthernet.ASSUMED_QL_PARAM, 
		            MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.AssumedQL)));
		    
		    if (!qlModeAdminControl.equals("Disabled")) {
		    	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
		            LayeredParams.PropAdvaSyncEthernet.RECEIVED_QL_PARAM, 
		            MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.ReceivedQL)));
		    
		    	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
		            LayeredParams.PropAdvaSyncEthernet.TRANSMIT_QL_PARAM, 
		            MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.TransmitQL)));
		    }
	    }
    // -------end of Layer-------
    }
    // -------start Layer--------
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
              LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_ID_PARAM, Integer.toString(portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamId)));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
              LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_ADMINISTRATION_CONTROL_PARAM,
              MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamEnabled)));

      if (portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamAdminDisposition) == CMDispositionTranslation.Peer.getMIBValue()) {
       // if (portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamDiscoveryState) != 0) { // TODO: this is only a workaround for bug on device
          LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                  LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_DISCOVERY_STATE_PARAM,
                  MtosiUtils.getMtosiString(CMEfmOamDiscoveryStateTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamDiscoveryState)));
        //}

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_LOCAL_VAR_RTRVS_ENABLED_PARAM,
                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamLocalVarRtrvsEnabled)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_LOCAL_LINK_EVENTS_ENABLED_PARAM,
                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamLocalLinkEventsEnabled)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_LOCAL_OAM_LOOPBACKS_SUPPORTED_PARAM,
                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamLocalOamLoopbacksSupported)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_LOCAL_UNIDIR_SUPPORT_ENABLED_PARAM,
                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamLocalUnidirSupportEnabled)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_LOCAL_MAX_PDU_SIZE_PARAM, Integer.
                toString(portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamLocalMaxPDUSize) < portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamRemoteMaxPDUSize) ?
                portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamLocalMaxPDUSize) : portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamRemoteMaxPDUSize)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_LOCAL_MODE_PARAM,
                MtosiUtils.getMtosiString(CMEfmOamModeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamLocalMode)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_REMOTE_PHYS_ADDRESS_PARAM, portProperties.get(NETPortSPPropertiesFSP150CM.VS.OamRemoteMacAddress));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_REMOTE_VAR_RTRVS_ENABLED_PARAM,
                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamRemoteVarRtrvsEnabled)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_REMOTE_LINK_EVENTS_ENABLED_PARAM,
                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamRemoteLinkEventsEnabled)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_REMOTE_OAM_LOOPBACKS_SUPPORTED_PARAM,
                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamRemoteOamLoopbacksSupported)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_REMOTE_UNIDIR_SUPPORT_ENABLED_PARAM,
                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamRemoteUnidirSupportEnabled)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_REMOTE_MAX_PDU_SIZE_PARAM, Integer.
                toString(portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamRemoteMaxPDUSize)));

        if (portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamRemoteMode) != 0) { // TODO: this is only a workaround for bug on device
          LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                  LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_REMOTE_MODE_PARAM,
                  MtosiUtils.getMtosiString(CMEfmOamModeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamRemoteMode)));
        }
      
    }
    // -------end of Layer-------

    physicalTerminationPointT.setTransmissionParams(objFactory.createPhysicalTerminationPointTTransmissionParams(layeredParametersListT));
    return physicalTerminationPointT;
  }


  protected abstract String getInterfaceType();

  protected abstract String getTpRoleState();	
  
  protected abstract String getMaxNumFDFrs(); 
  
  /**
   * An ACC port only parameter.
   * @param layeredParametersListT
   */
  protected void addPortMode(LayeredParametersListT layeredParametersListT) {
  }



}
