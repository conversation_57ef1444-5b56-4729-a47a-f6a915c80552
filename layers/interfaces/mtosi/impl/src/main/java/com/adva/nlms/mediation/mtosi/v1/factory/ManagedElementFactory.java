/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.factory;


import com.adva.nlms.common.NEUtils;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.common.serviceProvisioning.F3PolicerSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.NETPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.ShaperSPProperties;
import com.adva.nlms.mediation.config.ConfigCtrlImpl;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.f3.entity.flow.FlowF3Impl;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.f3.entity.module.MTOSICardModuleF3;
import com.adva.nlms.mediation.config.f3.entity.pg.ProtectionGroupF3;
import com.adva.nlms.mediation.config.f3.entity.policer.qospolicer.QOSFlowPolicer;
import com.adva.nlms.mediation.config.f3.entity.policer.qospolicer.QOSFlowPolicerImpl;
import com.adva.nlms.mediation.config.f3.entity.port.acc.MTOSIPortF3Acc;
import com.adva.nlms.mediation.config.f3.entity.port.net.MTOSIPortF3Net;
import com.adva.nlms.mediation.config.f3.entity.shaper.qosshaper.QOSShaperF3;
import com.adva.nlms.mediation.config.f3.entity.sync.F3SyncImpl;
import com.adva.nlms.mediation.config.f3.mtosi.NetworkElementFSP150CMMTOSIOperations;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CMImpl;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FTPFSP150CM;
import com.adva.nlms.mediation.config.hn4000.FlowHN4000;
import com.adva.nlms.mediation.config.hn4000.LAGHN4000;
import com.adva.nlms.mediation.config.hn4000.NetworkElementHN4000;
import com.adva.nlms.mediation.config.hn4000.PolicerHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN40002Bpme;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet2BASE_TL;
import com.adva.nlms.mediation.config.hn4000.ShaperHN4000;
import com.adva.nlms.mediation.config.hn4000.Uni4xxHN4000;
import com.adva.nlms.mediation.config.hn4000.UniHN4000;
import com.adva.nlms.mediation.config.hn4000.mtosi.FDFrHN4000;
import com.adva.nlms.mediation.config.hn4000.mtosi.FTPHN4000;
import com.adva.nlms.mediation.config.mtosi.FDFr;
import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.config.mtosi.LegacyMtosiMOFacadeImpl;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.Timer;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidationHelper;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import v1.tmf854.FloatingTerminationPointListT;
import v1.tmf854.FloatingTerminationPointT;
import v1.tmf854.LayerRateListT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.LayeredParametersT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.TerminationPointListT;
import v1.tmf854.TerminationPointT;
import v1.tmf854ext.adva.FDFrListT;
import v1.tmf854ext.adva.FlowDomainFragmentT;
import v1.tmf854ext.adva.TCProfileListT;
import v1.tmf854ext.adva.TCProfileT;
import v1.tmf854ext.adva.TDFrListT;
import v1.tmf854ext.adva.TimingDomainFragmentT;
import ws.v1.tmf854.ProcessingFailureException;

import java.util.HashSet;
import java.util.List;
import java.util.Set;


public class ManagedElementFactory
{
	public static boolean isF3NE(NamingAttributesT namingAttributes) throws ProcessingFailureException
	{
		NetworkElement ne = getAndValidateNE(namingAttributes);
		int type = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI();
		return NEUtils.isF3Device(type);
	}
	
  public static NetworkElement getAndValidateAnyNE(NamingAttributesT namingAttributes) throws ProcessingFailureException
  {
    final String neName = namingAttributes.getMeNm();
    NetworkElement ne = null;
    if (neName != null) {
      ne = ConfigCtrlImpl.get().getHandlers().getNeHdlr().getNEByName(neName);
    }

    if (ne == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.ME_NOT_FOUND);
    }

    if(!MtosiSupportValidationHelper.canCreateManagedElements()) {
      MtosiUtils.validateNE(ne);
    }

    return ne;
  }

	public static NetworkElement getAndValidateNE(NamingAttributesT namingAttributes) throws ProcessingFailureException
  {
    final String neName = namingAttributes.getMeNm();
    NetworkElement ne = null;
    if (neName != null) {
      ne = ConfigCtrlImpl.get().getHandlers().getNeHdlr().getNEByName(neName);
    }

    MtosiUtils.validateNE(ne);

    return ne;
  }


  public static Port getPort(NamingAttributesT namingAttributes) throws ProcessingFailureException {
    final NetworkElement ne = getAndValidateNE(namingAttributes);

    final String portName = namingAttributes.getPtpNm();
    String name = NamingTranslationFactory.portNameFromShelfCombo(portName);
    int shelfIndex = NamingTranslationFactory.shelfNumberFromShelfCombo(portName);
    int slotIndex = NamingTranslationFactory.slotNumberFromShelfCombo(portName);
    if (needsSlotIncrement(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
      slotIndex += 1;
    }

    if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE) {
      if (name.contains("BITS")){
        name = name.replace("-"," ").concat("-1-1-1-1");
      }
    }

    Port port = ne.getMTOSIWorker().getPortByName(name, shelfIndex, slotIndex);

    if(port == null)
    {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    return port;
  }
  /**
   * This needs to be moved somewhere...
   * The slot index comes from SNMP and for CM and 825, and Most, it is not in sync with the GUI or Mtosi, so it needs to be incremented by 1
   * But HN and GE20X do not have this issue, so they do NOT need to be incremented.
   * This method needs a better name and a better location... TODO move me...
   * @param networkElementTypeForMTOSI
   * @return
   */
  public static boolean needsSlotIncrement(int networkElementTypeForMTOSI) {
	return 	   networkElementTypeForMTOSI != NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000
			&& networkElementTypeForMTOSI != NeTypeIds.NETWORK_ELEMENT_TYPE_HN400
			&& !NEUtils.isGEDevice(networkElementTypeForMTOSI)
      && !NEUtils.isXG210(networkElementTypeForMTOSI);
}

/**
	 * Get the HN4000 Port that is either a PTP or FTP.
	 * 
	 * @param namingAttributes
	 * @return a Port or you get an exception
	 * @throws ProcessingFailureException
	 *             if the port does not exist!
	 */
	public static Port getHN4000Port(NamingAttributesT namingAttributes) throws ProcessingFailureException {
		Port port = null;
		if (namingAttributes.getPtpNm() != null) {
			port = getPort(namingAttributes);
			if (port == null) {
				throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
			}
		} else if (namingAttributes.getFtpNm() != null) {
			String ftpName = namingAttributes.getFtpNm();
			final NetworkElementHN4000 ne4000 = (NetworkElementHN4000) getAndValidateNE(namingAttributes);
			if (ftpName.indexOf("ETH") > 0) {
				PortHN4000Ethernet2BASE_TL ftp = ne4000.getBondedPort(ftpName);
				if (ftp == null) {
					throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
				}
				port = (Port)ftp;
			} else if (ftpName.indexOf("LAG") > 0) {
				FTPHN4000 ftp = (FTPHN4000) ne4000.getMTOSIWorker().getFTP(ftpName);
				if (ftp == null) {
					throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
				}
				port = (Port)ftp;
			}
		} else {
			throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PORT_NAME_MISSING);
		}
		return port;
	}

  /**
	 * Get the Flow that is hanging off the PTP or FTP.
	 * 
	 * @param namingAttributes
	 * @return a FlowHN4000 or you get an exception
	 * @throws ProcessingFailureException
	 *             if the flow does not exist!
	 */
	public static FlowHN4000 getHNFlow(NamingAttributesT namingAttributes) throws ProcessingFailureException {
		Set<FlowHN4000> flows = null;
		String ctpName = namingAttributes.getCtpNm();
		if (namingAttributes.getPtpNm() != null) {
			final Port port = getPort(namingAttributes);
			if (port instanceof PortHN4000Ethernet) {
				flows = ((PortHN4000Ethernet) port).getFlows();
			}
		} else {
			String ftpName = namingAttributes.getFtpNm();
		    final NetworkElementHN4000 ne4000 = (NetworkElementHN4000) getAndValidateNE(namingAttributes);
			if (ftpName.indexOf("ETH") > 0) {
				PortHN4000Ethernet2BASE_TL ftp = ne4000.getBondedPort(ftpName);
				if (ftp == null) {
					throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
				}
				flows = ftp.getFlows();
			} else if (ftpName.indexOf("LAG") > 0) {
				FTPHN4000 ftp = (FTPHN4000) ne4000.getMTOSIWorker().getFTP(ftpName);
				if (ftp == null) {
					throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
				}
				flows = ftp.getFlows();
			}
		}
		if (flows == null || flows.isEmpty()) {
			throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
		}
		for (FlowHN4000 flow : flows) {
			if (ctpName.equals(((FlowSPPropertiesHN4000) flow.getFlowSPProperties()).get(FlowSPPropertiesHN4000.VS.Desc))) {
				return flow;
			}
		}
		throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
	}
	
  public static Object getMtosiSupportedPort(NamingAttributesT namingAttributes) throws ProcessingFailureException {
    Port port = getPort(namingAttributes);
//    if (!(port instanceof MtosiSupported)) {
//      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.MTOSI_NOT_SUPPORTED);
//    }
    return port;
  }

  /**
	 * Returns port from specific ne.
	 * @param ne Instance of Network Element.
	 * @param ptpName name of the port.
	 * @return Port object.
	 * @throws ws.v1.tmf854.ProcessingFailureException throw excetpion when is lack of parameters or request fail.
	 */
  @MDPersistenceContext
	public static Port getPort(final NetworkElement ne, final String  ptpName) throws ProcessingFailureException
	{
		String name = NamingTranslationFactory.portNameFromShelfCombo(ptpName);
    int shelfIndex = NamingTranslationFactory.shelfNumberFromShelfCombo(ptpName);
    int slotIndex = NamingTranslationFactory.slotNumberFromShelfCombo(ptpName);
    if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM ||
            ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR ||
						ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM) {
      slotIndex += 1;
    }

    if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE) {
      if (name.contains("BITS")){
        name = name.replace("-"," ").concat("-1-1-1-1");
      }
    }
    Port port =  null;
		port =  ne.getMTOSIWorker().getPortByName(name, shelfIndex, slotIndex);
		return port;

	}
	/**
	 * Get the MtosiSupported FTP from then NE based on the type in the ftpName
	 * @param namingAttributes
	 * @return
	 * @throws ProcessingFailureException
	 */

	public static FTPHN4000 getHN4000Ftp(NamingAttributesT namingAttributes) throws ProcessingFailureException {
		String ftpName = namingAttributes.getFtpNm();
		if (ftpName == null) {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
					MtosiErrorConstants.FTP_NAME_MISSING);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		FTPHN4000 ftp = null;
		NetworkElementHN4000 ne4000 = (NetworkElementHN4000) getAndValidateNE(namingAttributes);
		if (ftpName.indexOf("port=ETH") > 0) {
			ftp = ne4000.getBondedPort(ftpName);
		} else if (ftpName.indexOf("port=LAG") > 0) {
			ftp = (FTPHN4000) ne4000.getMTOSIWorker().getFTP(ftpName);
		}
		if (ftp == null) {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
					MtosiErrorConstants.FTP_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		return ftp;
	}

	/**
	 * Return specific ftp by name.
	 * @param namingAttributes
	 * @return FTP
	 * @throws ProcessingFailureException
	 */
	public static FTP getFtp(final NamingAttributesT namingAttributes) throws ProcessingFailureException
	{
		final String ftpNm = namingAttributes.getFtpNm();
		if (ftpNm == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NAME_MISSING);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		final NetworkElement ne = getAndValidateNE(namingAttributes);

		final FTP ftp = ne.getMTOSIWorker().getFTP(ftpNm);
		if(ftp == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}

		if (NEUtils.isF3Device(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
			((FTPFSP150CM)ftp).doVolatilePolling();
		}
		return ftp;
	}





  public static MTOSIFlowF3 getCMFlow(final NamingAttributesT namingAttributes) throws ProcessingFailureException
	{
		final String flowName = namingAttributes.getCtpNm();
		if(flowName == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NAME_MISSING);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		final Port port = getPort(namingAttributes);
		return getCMFlow(port, flowName);
  }

  public static MTOSICardModuleF3 getModuleCard(MtosiAddress mtosiAddress) throws ProcessingFailureException
	{
    NetworkElementFSP150CM ne = (NetworkElementFSP150CM)mtosiAddress.getNE();
    int shelfIndex = mtosiAddress.getShelfNumber();
    int slotIndex = mtosiAddress.getNmsFixedSlotNumber();
    return getFSP150CMMTOSIWorker(ne).getModule(shelfIndex, slotIndex);
  }



  public static MTOSIFlowF3 getCMFlow(final Port port, final String flowName) throws ProcessingFailureException
	{
		if(flowName == null || flowName.equals(""))
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NAME_MISSING);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}

		if(!(port instanceof MTOSIPortF3Acc))
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_ILLEGAL);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    return ((MTOSIPortF3Acc)port).getFlowFSP150CMByName(flowName);
	}

  /**
	 * Main method for collect PTP from device.
	 * @param namingAttributes
	 * @param tpLayerRateListT
	 * @param connectionLayerRateList
	 * @return TerminationPointListT
	 * @throws ProcessingFailureException
	 */
	public static TerminationPointListT getAllPTPs(NamingAttributesT namingAttributes, final NetworkElement ne, LayerRateListT tpLayerRateListT, LayerRateListT connectionLayerRateList)
					throws ProcessingFailureException
	{

		String ehName = namingAttributes.getEhNm();
		if(tpLayerRateListT != null && connectionLayerRateList != null)
			return getAllTPAndConnectionLayerRatePTPs(ne, tpLayerRateListT, connectionLayerRateList);
		else if (tpLayerRateListT != null)
			return getAllTPLayerRatePTPs(ne, tpLayerRateListT);
		else if (connectionLayerRateList != null)
			return getAllConnectionLayerRatePTPs(ne, connectionLayerRateList);
		else
			return getAllPTPs(ne,ehName);
	}

	/**
	 * Return all ports from ne with all layers.
	 * @param ne
	 * @return TerminationPointListT
	 * @throws ProcessingFailureException
	 */
  private static TerminationPointListT getAllPTPs (NetworkElement ne, String ehName)
          throws ProcessingFailureException {
    TerminationPointListT terminationPointListT = new TerminationPointListT();
    //Should this be in the getPorts method... will we ever want getPorts() without
    //wanting the most up to date.
    final int neType = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI();
    switch (neType) {
	case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
	case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
    	Timer t = new Timer("Polling");
    	t.start(0);
    	((NetworkElementHN4000)ne).doNEEquipPortPolling(new Class[]{
                UniHN4000.class,
                PortHN40002Bpme.class,
                PortHN4000Ethernet.class
                });
    	t.stop(0);
    	t.report();
		
		break;

	case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
	case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
		  ((NetworkElementFSP150CM)ne).doNEEquipPortPolling( new Class[] {
			      MTOSIPortF3Net.class,
			      MTOSIPortF3Acc.class
			      });
		
		break;
	}
    	
    final Set retrievedList = ne.getMTOSIWorker().getPorts();
    final ObjectFactory objFactory = new ObjectFactory();
    for (Object port : retrievedList){
      TerminationPointT terminationPointT = objFactory.createTerminationPointT();
			MtosiTranslator mtosiTranslator;
      if ((mtosiTranslator = new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(port)) != null) {
//      if (port instanceof MtosiSupported) {
		try {
		  PhysicalTerminationPointT point = mtosiTranslator.toMtosiPTP();
			  //    	  PhysicalTerminationPointT point = ((MtosiSupported)port).getMtosiTranslator().toMtosiPTP();
		  if (point == null) {
		    continue;
		  }

    	  //IF the filter contains an ehNm, then we need to only return the PTPs for that eh.
    	  //ie if ehNm = "/shelf=1" then we only return the ptps from "/shelf=1"
    	  if (ehName == null || point.getName().getValue().getPtpNm().startsWith(ehName)) {
    	    terminationPointT.setPtp(point);
    	  }else {
    	    continue;
    	  }
	    }catch(ProcessingFailureException ex){
		   continue;
	    }
      }
      else {
        continue;
      }

      terminationPointListT.getTp().add(terminationPointT);
    }
    return terminationPointListT;
  }

	/**
	 * List of PTP layer rates for which the PTPs are to be fetched.
	 * A PTP must contain at least one of the layer rates specified to be reported.
	 * If the list is empty then all PTPs (of all rates) are returned.
	 * @param ne
	 * @param tpLayerRateListT
	 * @return TerminationPointListT
	 */
	private static TerminationPointListT getAllTPLayerRatePTPs(NetworkElement ne, LayerRateListT tpLayerRateListT)
					throws ProcessingFailureException
	{
		List <String> layerList = tpLayerRateListT.getLayerRate();
		TerminationPointListT terminationPointListT = getAllPTPs(ne,null);
		TerminationPointListT result = new TerminationPointListT();
			for(TerminationPointT terminationPointT : terminationPointListT.getTp())
			{
				LayeredParametersListT layeredParametersListT = terminationPointT.getPtp().getTransmissionParams().getValue();
				for(LayeredParametersT layerdParameters : layeredParametersListT.getLayeredParameters())
				{
					if(layerList.contains(layerdParameters.getLayer()))
					{
						result.getTp().add(terminationPointT);
						break;
					}
				}
			}
			return result;
	}

	/**
	 * List of connection layer rates for which the PTPs are to be fetched.
	 * A PTP must support connections for at least one of the layer rates specified to be reported.
	 * If the list is empty then all PTPs (for all connection rates) are returned.
	 * @param ne
	 * @param connectionLayerRateList
	 * @return Set of PTP and FTP with connection layer rate.
	 */
	private static TerminationPointListT getAllConnectionLayerRatePTPs(NetworkElement ne, LayerRateListT connectionLayerRateList)
	{
		return null; //not supported
	}

	/**
	 * Initial version
	 * @param ne
	 * @param connectionLayerRateList
	 * @return Set of PTP and FTP with connection layer rate.
	 */
	private static TerminationPointListT getAllTPAndConnectionLayerRatePTPs(NetworkElement ne, LayerRateListT tpLayerRateListT, LayerRateListT connectionLayerRateList)
	{
		return null;//not supported
	}
	
	public static Object getShaper(final NamingAttributesT tcProfileName) throws ProcessingFailureException
	{
		final NetworkElement ne = ManagedElementFactory.getAndValidateNE(tcProfileName);
		if(ne==null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.ME_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
	    if (ne instanceof NetworkElementFSP150CM)
	    {
	    	return getCMShaper(ne, tcProfileName);
	    }
	    return null;
    
	}
	
	public static Object getPolicer(final NamingAttributesT tcProfileName) throws ProcessingFailureException
	{
		final NetworkElement ne = ManagedElementFactory.getAndValidateNE(tcProfileName);
		if(ne==null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.ME_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
	    if (NEUtils.isGEDevice(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()))
	    {
	    	return getGEPolicer(ne, tcProfileName);
	    }
	    return null;
    
	}

	
	public static QOSShaperF3 getCMShaper(NetworkElement ne, final NamingAttributesT tcProfileName) throws ProcessingFailureException
	{
						
    
      final String portName = tcProfileName.getPtpNm();
		  final String name = NamingTranslationFactory.portNameFromShelfCombo(portName);
      int shelfIndex = NamingTranslationFactory.shelfNumberFromShelfCombo(portName);
      int slotIndex = NamingTranslationFactory.slotNumberFromShelfCombo(portName);
      if (needsSlotIncrement(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
        slotIndex += 1;
      }
      final Port port = ((NetworkElementFSP150CM)ne).getMTOSIWorker().getPortByName(name, shelfIndex, slotIndex);

      if(port == null) //avoid null pointer exception
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }

      if(port instanceof MTOSIPortF3Acc)
      {
        final String tcpNm = tcProfileName.getTcpNm();
        final String ctpNm = tcProfileName.getCtpNm();

        if(tcpNm == null)
        {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.TC_PROFILE_NAME_MISSING);
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }

        if(ctpNm == null)
        {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NAME_MISSING);
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }

        MTOSIFlowF3 flow = ((MTOSIPortF3Acc) port).getFlowFSP150CMByName(ctpNm);
        if(flow == null) //avoid null pointer exception
        {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }
        //handle ingress shaper
        if(tcpNm.startsWith(MtosiConstants.ING_SHAPER_TEXT))
        {
          final int shaperIndex = NamingTranslationFactory.getShaperIndex(tcpNm);

          final QOSShaperF3 shaper = flow.getShaperFSP150CM(MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_A2N, shaperIndex);

          if(shaper == null) //specific object not found in DB. Return empty response.
            return null;

          return shaper;
        }
        //handle egress shaper (but not 5.2 since not supported per NBI)
        //Leaving out the CM because that is the way it was... and per chat with Larry on Sept 9, 2010 for FNM7074
        if (NEUtils.isGEDevice(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
	        if(tcpNm.startsWith(MtosiConstants.EG_SHAPER_TEXT))
	        {
	          final int shaperIndex = NamingTranslationFactory.getShaperIndex(tcpNm);
	
	          final QOSShaperF3 shaper = flow.getShaperFSP150CM(MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_N2A, shaperIndex);
	
	          if(shaper == null) //specific object not found in DB. Return empty response.
	            return null;
	
	          return shaper;
	        }
        }
      }
    
      return null;
		
	}

	private static QOSFlowPolicer getGEPolicer(NetworkElement ne, final NamingAttributesT tcProfileName) throws ProcessingFailureException
	{
						
    
      final String portName = tcProfileName.getPtpNm();
		  final String name = NamingTranslationFactory.portNameFromShelfCombo(portName);
      int shelfIndex = NamingTranslationFactory.shelfNumberFromShelfCombo(portName);
      int slotIndex = NamingTranslationFactory.slotNumberFromShelfCombo(portName);
      if (needsSlotIncrement(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
        slotIndex += 1;
      }
      final Port port = ((NetworkElementFSP150CM)ne).getMTOSIWorker().getPortByName(name, shelfIndex, slotIndex);

      if(port == null) //avoid null pointer exception
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }

      if(port instanceof MTOSIPortF3Acc)
      {
        final String tcpNm = tcProfileName.getTcpNm();
        final String ctpNm = tcProfileName.getCtpNm();

        if(tcpNm == null)
        {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.TC_PROFILE_NAME_MISSING);
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }

        if(ctpNm == null)
        {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NAME_MISSING);
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }

        MTOSIFlowF3 flow = ((MTOSIPortF3Acc) port).getFlowFSP150CMByName(ctpNm);
        if(flow == null) //avoid null pointer exception
        {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }
        //handle Policers
          final int  typeOfPolicer= (tcpNm.startsWith(MtosiConstants.EG_POLICER_TEXT)) ? MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_N2A
                  : MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_A2N;
          
          final int policerIndex = MtosiUtils.getRegexGroupInt(tcpNm, "policer=(\\d+)$", 0, 0, MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID );              

          final QOSFlowPolicerImpl policer = (QOSFlowPolicerImpl)flow.getPolicer(typeOfPolicer, policerIndex+1);
          if(policer == null) {
            final ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.TC_PROFILE_NOT_FOUND);
            throw new ProcessingFailureException(pfet.getReason(), pfet);
          }
          return policer;
      }
      return null;
		
	}
	/**
	 * Retrurn policer or queue.
	 * @param tcProfileName
	 * @return TCProfileT
	 * @throws ProcessingFailureException
	 */
	public static TCProfileT getTCProfile(final NamingAttributesT tcProfileName, final NetworkElement ne) throws ProcessingFailureException
	{

    if (ne instanceof NetworkElementFSP150CM)
    {
      final String portName = tcProfileName.getPtpNm();
		  final String name = NamingTranslationFactory.portNameFromShelfCombo(portName);
      int shelfIndex = NamingTranslationFactory.shelfNumberFromShelfCombo(portName);
      int slotIndex = NamingTranslationFactory.slotNumberFromShelfCombo(portName);
      if (needsSlotIncrement(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
        slotIndex += 1;
      }
      final Port port = ((NetworkElementFSP150CM)ne).getMTOSIWorker().getPortByName(name, shelfIndex, slotIndex);

      if(port == null) //avoid null pointer exception
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }

      if(port instanceof MTOSIPortF3Acc)
      {
        final String tcpNm = tcProfileName.getTcpNm();
        final String ctpNm = tcProfileName.getCtpNm();

        if(tcpNm == null)
        {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.TC_PROFILE_NAME_MISSING);
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }

        if(ctpNm == null)
        {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NAME_MISSING);
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }

        MTOSIFlowF3 flow = ((MTOSIPortF3Acc) port).getFlowFSP150CMByName(ctpNm);
        if(flow == null) //avoid null pointer exception
        {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }
        //handle ingress shaper
        if(tcpNm.startsWith(MtosiConstants.ING_SHAPER_TEXT))
        {
          final int shaperIndex = NamingTranslationFactory.getShaperIndex(tcpNm);

          final QOSShaperF3 shaper = flow.getShaperFSP150CM(MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_A2N, shaperIndex);

          if(shaper == null) //specific object not found in DB. Return empty response.
            return null;

          return new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(shaper).toMtosiTCProfile();
//          return shaper.getMtosiTranslator().toMtosiTCProfile();
        }
        //handle egress shaper
        if(tcpNm.startsWith(MtosiConstants.EG_SHAPER_TEXT))
        {
          final int shaperIndex = NamingTranslationFactory.getShaperIndex(tcpNm);

          final QOSShaperF3 shaper = flow.getShaperFSP150CM(MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_N2A, shaperIndex);

          if(shaper == null) //specific object not found in DB. Return empty response.
            return null;

          return new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(shaper).toMtosiTCProfile();
//          return shaper.getMtosiTranslator().toMtosiTCProfile();
        }
        // policers are only supported by GE20x
        if (NEUtils.isGEDevice(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()))
        {
          //handle ingress policer
          if(tcpNm.startsWith(MtosiConstants.ING_POLICER_TEXT))
          {
            final int policerIndex = MtosiUtils.getRegexGroupInt(tcpNm,
                                                                 "^"+MtosiConstants.ING_POLICER_TEXT+"(\\d+)$",
                                                                 0, 0, MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID);

            QOSFlowPolicerImpl policer = (QOSFlowPolicerImpl)flow.getPolicer(MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_A2N, policerIndex+1);

            if(policer == null) //specific object not found in DB. Return empty response.
              return null;

            return new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(policer).toMtosiTCProfile();
//            return policer.getMtosiTranslator().toMtosiTCProfile();
          }
          //handle egress policer
          if(tcpNm.startsWith(MtosiConstants.EG_POLICER_TEXT))
          {
            final int policerIndex = MtosiUtils.getRegexGroupInt(tcpNm,
                                                                 "^"+MtosiConstants.EG_POLICER_TEXT+"(\\d+)$",
                                                                 0, 0, MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID);

            QOSFlowPolicerImpl policer = (QOSFlowPolicerImpl)flow.getPolicer(MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_N2A, policerIndex+1);

            if(policer == null) //specific object not found in DB. Return empty response.
              return null;

            return new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(policer).toMtosiTCProfile();
//            return policer.getMtosiTranslator().toMtosiTCProfile();
          }
        }
      }
    } else if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400) {
    	return getTCProfileHN(ne, tcProfileName);
    }
    return null;
  }
	private static TCProfileT getTCProfileHN(NetworkElement ne, NamingAttributesT tcProfileName) throws ProcessingFailureException {
		String ptpName = tcProfileName.getPtpNm();
		String ftpName = tcProfileName.getFtpNm();
		
		if (ptpName != null && ftpName == null) {
			PortHN4000Ethernet port =(PortHN4000Ethernet) ManagedElementFactory.getPort(tcProfileName);
			return getTPProfileHN(port,  tcProfileName);
			
		} else if (ptpName == null && ftpName != null) {
			PortHN4000Ethernet port =(PortHN4000Ethernet) ManagedElementFactory.getHN4000Ftp(tcProfileName);
			return getTPProfileHN(port,  tcProfileName);
			
		} else {
	          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID);
	          throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
	}

	private static TCProfileT getTPProfileHN(PortHN4000Ethernet port, NamingAttributesT tcProfileName) throws ProcessingFailureException {
		String tcpName = tcProfileName.getTcpNm();
		if (tcpName == null) {
	          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID);
	          throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		if (port.hasUni() == false) {
	          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.TC_PROFILE_NOT_FOUND);
	          throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		
		if (tcpName.startsWith("/egshaper=")) {
			ShaperHN4000 shaper = ((Uni4xxHN4000)port.getUni()).getShaper();
			if (shaper != null)
				return (new MtosiTranslatorFacade(getAndValidateNE(tcProfileName), null, null).getMtosiTranslator(shaper)).toMtosiTCProfile();
//				return ((MtosiSupported)shaper).getMtosiTranslator().toMtosiTCProfile();
		} else if (tcpName.startsWith("/ingpolicer=")) {
			PolicerHN4000 policer = ((Uni4xxHN4000)port.getUni()).getPolicer();
			if (policer != null)
				return (new MtosiTranslatorFacade(getAndValidateNE(tcProfileName), null, null).getMtosiTranslator(policer)).toMtosiTCProfile();
//				return ((MtosiSupported)policer).getMtosiTranslator().toMtosiTCProfile();
		}
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.TC_PROFILE_NOT_FOUND);
        throw new ProcessingFailureException(pfet.getReason(), pfet);

	}

	/**
	 * This service returns the structures of the Traffic Conditioning Profiles associated with a Termination Point.
	 * @param tpName
	 * @return TCProfileListT
	 * @throws ProcessingFailureException
	 */
	public static TCProfileListT getTCProfilesWithTPHN(final NamingAttributesT tpName) throws ProcessingFailureException {
		final v1.tmf854ext.adva.ObjectFactory objFactoryEx = new v1.tmf854ext.adva.ObjectFactory(); 
		final TCProfileListT tcProfileListT = objFactoryEx.createTCProfileListT();
		String ptpName = tpName.getPtpNm();
		String ftpName = tpName.getFtpNm();
		PortHN4000Ethernet port = null;
		if (ptpName != null && ftpName == null) {
			port = (PortHN4000Ethernet) ManagedElementFactory.getPort(tpName);
			if (port == null) {
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
						MtosiErrorConstants.PTP_NOT_FOUND);
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
		} else if (ptpName == null && ftpName != null) {
			port = (PortHN4000Ethernet) ManagedElementFactory.getHN4000Ftp(tpName);
			if (port == null) {
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
						MtosiErrorConstants.FTP_NOT_FOUND);
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
		} else {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
					MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		if (port.hasUni()) {
			ShaperHN4000 shaper = ((Uni4xxHN4000) port.getUni()).getShaper();
			TCProfileT tc = null;
			if (shaper != null) {
				tc = (new MtosiTranslatorFacade(getAndValidateNE(tpName), null, null).getMtosiTranslator(shaper)).toMtosiTCProfile();
//				tc = ((MtosiSupported) shaper).getMtosiTranslator().toMtosiTCProfile();
				if (tc != null) {
					tcProfileListT.getTcp().add(tc);
				}
			}
			PolicerHN4000 policer = ((Uni4xxHN4000) port.getUni()).getPolicer();
			if (policer != null) {
				tc = (new MtosiTranslatorFacade(getAndValidateNE(tpName), null, null).getMtosiTranslator( policer)).toMtosiTCProfile();
//				tc = ((MtosiSupported) policer).getMtosiTranslator().toMtosiTCProfile();
				tcProfileListT.getTcp().add(tc);
			}
		}

		return tcProfileListT;
	}
	/**
	 * This service returns the structures of the Traffic Conditioning Profiles associated with a Termination Point.
	 * @param tpName
	 * @return TCProfileListT
	 * @throws ProcessingFailureException
	 */
	public static TCProfileListT getTCProfilesWithTPOnFlow(final NamingAttributesT tpName) throws ProcessingFailureException
	{
		final NetworkElement ne = getAndValidateNE(tpName);
		final v1.tmf854ext.adva.ObjectFactory objFactoryEx = new v1.tmf854ext.adva.ObjectFactory(); //objectFactory from extended wsdls
		final TCProfileListT tcProfileListT = objFactoryEx.createTCProfileListT();

    if (ne instanceof NetworkElementFSP150CM)
    {
      final String portName = tpName.getPtpNm();
		  final String name = NamingTranslationFactory.portNameFromShelfCombo(portName);
      int shelfIndex = NamingTranslationFactory.shelfNumberFromShelfCombo(portName);
      int slotIndex = NamingTranslationFactory.slotNumberFromShelfCombo(portName);
      if (needsSlotIncrement(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
        slotIndex += 1;
      }
      final Port port = ne.getMTOSIWorker().getPortByName(name, shelfIndex, slotIndex);

      if(port == null) //avoid null pointer exception
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }

      if(port instanceof MTOSIPortF3Acc)
      {
        final String ctpNm = tpName.getCtpNm();


        if(ctpNm == null)
        {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NAME_MISSING);
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }

        MTOSIFlowF3 flow = ((MTOSIPortF3Acc) port).getFlowFSP150CMByName(ctpNm);
        if(flow == null) //avoid null pointer exception
        {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }
        boolean egressRateLimitingEnabled = (flow.getFlowSPProperties().get(FlowSPPropertiesFSP150CM.VI.N2ARateLimitingEnabled) == MIB.RFC1253.TRUTH_VALUE_TRUE);

        final QOSShaperF3 ingShaper = flow.getShaperFSP150CM(MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_A2N, 1);
        if (ingShaper != null) {
          tcProfileListT.getTcp().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ingShaper).toMtosiTCProfile());
//          tcProfileListT.getTcp().add(ingShaper.getMtosiTranslator().toMtosiTCProfile());

        }
        // egressShaper is only created if egressRateLimitingEnabled is set to true
        final QOSShaperF3 egShaper = flow.getShaperFSP150CM(MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_N2A, 1);
        if (egShaper != null && egressRateLimitingEnabled) {
          tcProfileListT.getTcp().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(egShaper).toMtosiTCProfile());
//          tcProfileListT.getTcp().add(egShaper.getMtosiTranslator().toMtosiTCProfile());
        }
        final QOSFlowPolicerImpl ingPolicer = (QOSFlowPolicerImpl)flow.getPolicer(MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_A2N, 1);
        if (ingPolicer != null) {
					tcProfileListT.getTcp().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ingPolicer).toMtosiTCProfile());
//          tcProfileListT.getTcp().add(ingPolicer.getMtosiTranslator().toMtosiTCProfile());
        }
        // egressPolicer is only created if egressRateLimitingEnabled is set to true
        final QOSFlowPolicerImpl egPolicer = (QOSFlowPolicerImpl)flow.getPolicer(MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_N2A, 1);
        if (egPolicer != null && egressRateLimitingEnabled) {
          tcProfileListT.getTcp().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(egPolicer).toMtosiTCProfile());
//          tcProfileListT.getTcp().add(egPolicer.getMtosiTranslator().toMtosiTCProfile());
        }
      }
    }
    return tcProfileListT;
	}

	/**
	 * This service returns the structures of the Traffic Conditioning Profiles associated with a Termination Point.
	 * @param tpName
	 * @return TCProfileListT
	 * @throws ProcessingFailureException
	 */
	public static TCProfileListT getTCProfilesWithTPOnPort(final NamingAttributesT tpName) throws ProcessingFailureException
	{
		final NetworkElement ne = getAndValidateNE(tpName);
		final v1.tmf854ext.adva.ObjectFactory objFactoryEx = new v1.tmf854ext.adva.ObjectFactory(); //objectFactory from extended wsdls
		final TCProfileListT tcProfileListT = objFactoryEx.createTCProfileListT();

    if (ne instanceof NetworkElementFSP150CM) {
      final String portName = tpName.getPtpNm();
		  final String name = NamingTranslationFactory.portNameFromShelfCombo(portName);
      int shelfIndex = NamingTranslationFactory.shelfNumberFromShelfCombo(portName);
      int slotIndex = NamingTranslationFactory.slotNumberFromShelfCombo(portName);
      if (needsSlotIncrement(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
        slotIndex += 1;
      }
      final Port port = ne.getMTOSIWorker().getPortByName(name, shelfIndex, slotIndex);

      if(port == null) //avoid null pointer exception
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }

      if(port instanceof MTOSIPortF3Acc)
      {
        for (MTOSIFlowF3 flow : ((MTOSIPortF3Acc)port).getFlowFSP150CMs()) {
          boolean egressRateLimitingEnabled = (flow.getFlowSPProperties().get(FlowSPPropertiesFSP150CM.VI.N2ARateLimitingEnabled) == MIB.RFC1253.TRUTH_VALUE_TRUE); 
          for (QOSShaperF3 shaper : flow.getShapers()) {
            ShaperSPProperties shaperProps = shaper.getShaperSPProperties();
            // egressShaper is only created if egressRateLimitingEnabled is set to true
            if (shaperProps.get(ShaperSPProperties.VI.TypeIndex) == MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_A2N ||
                egressRateLimitingEnabled) {
              tcProfileListT.getTcp().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(shaper).toMtosiTCProfile());
//              tcProfileListT.getTcp().add(shaper.getMtosiTranslator().toMtosiTCProfile());
            }
          }
          for (QOSFlowPolicer policer : flow.getPolicers()) {
            F3PolicerSPProperties policerProps = ((QOSFlowPolicerImpl) policer).getPolicerSPProperties();
            // egressPolicer is only created if egressRateLimitingEnabled is set to true
            if (policerProps.get(ShaperSPProperties.VI.TypeIndex) == MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_A2N ||
                egressRateLimitingEnabled) {
							tcProfileListT.getTcp().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(policer).toMtosiTCProfile());
//              tcProfileListT.getTcp().add(((QOSFlowPolicerImpl) policer).getMtosiTranslator().toMtosiTCProfile());
            }
          }
        }
      }
    }
    return tcProfileListT;
	}

  /**
	 * Initial version
	 * @param ne
	 * @param connectionLayerRateList
	 * @return Set of FTP with connection layer rate.
	 */
	private static FloatingTerminationPointListT getAllTPAndConnectionLayerRateFTPs(NetworkElement ne, LayerRateListT tpLayerRateListT, LayerRateListT connectionLayerRateList)
	{
		return null;//not supported
	}

  /**
	 * List of FTP layer rates for which the FTPs are to be fetched.
	 * A FTP must contain at least one of the layer rates specified to be reported.
	 * If the list is empty then empty list is returned.
	 * @param ne
	 * @param tpLayerRateListT
	 * @return FloatingTerminationPointListT
	 */
	private static FloatingTerminationPointListT getAllTPLayerRateFTPs(NetworkElement ne, LayerRateListT tpLayerRateListT)
					throws ProcessingFailureException
	{
		List <String> layerList = tpLayerRateListT.getLayerRate();
		FloatingTerminationPointListT floatingTerminationPointListT = getAllFTPs(ne);
		FloatingTerminationPointListT result = new FloatingTerminationPointListT();
			for(FloatingTerminationPointT floatingTerminationPointT : floatingTerminationPointListT.getFTP())
			{
				LayeredParametersListT layeredParametersListT = floatingTerminationPointT.getTransmissionParams().getValue();
				for(LayeredParametersT layerdParameters : layeredParametersListT.getLayeredParameters())
				{
					if(layerList.contains(layerdParameters.getLayer()))
					{
						result.getFTP().add(floatingTerminationPointT);
						break;
					}
				}
			}
			return result;
	}

  /**
	 * List of connection layer rates for which the FTPs are to be fetched.
	 * A FTP must support connections for at least one of the layer rates specified to be reported.
	 * If the list is empty then all FTPs (for all connection rates) are returned.
	 * @param ne
	 * @param connectionLayerRateList
	 * @return Set of FTP with connection layer rate.
	 */
	private static FloatingTerminationPointListT getAllConnectionLayerRateFTPs(NetworkElement ne, LayerRateListT connectionLayerRateList)
	{
		return null; //not supported
	}

  public static FloatingTerminationPointListT getAllFTPs(NamingAttributesT naming, final NetworkElement ne,LayerRateListT tpLayerRateListT, LayerRateListT connectionLayerRateList)
  throws ProcessingFailureException
  {
 
    if(tpLayerRateListT != null && connectionLayerRateList != null)
      return getAllTPAndConnectionLayerRateFTPs(ne, tpLayerRateListT, connectionLayerRateList);
    else if (tpLayerRateListT != null)
      return getAllTPLayerRateFTPs(ne, tpLayerRateListT);
    else if (connectionLayerRateList != null)
      return getAllConnectionLayerRateFTPs(ne, connectionLayerRateList);
    else
      return getAllFTPs(ne);
  }

  /**
	 * Return all floating termination points from ne with all layers.
	 * @param ne
	 * @return FloatingTerminationPointListT
	 * @throws ProcessingFailureException
	 */
	private static FloatingTerminationPointListT getAllFTPs(NetworkElement ne) throws ProcessingFailureException {
		final FloatingTerminationPointListT floatingTerminationPointListT = new FloatingTerminationPointListT();
		if (ne instanceof NetworkElementHN4000) {
			
			((NetworkElementHN4000)ne).doNEEquipPortPolling(new Class[]{
                    PortHN4000Ethernet2BASE_TL.class, 
                    LAGHN4000.class
                    });

			final Set<PortHN4000Ethernet2BASE_TL> bondedPorts = ((NetworkElementHN4000)ne).getLogicalPorts();
			for (PortHN4000Ethernet2BASE_TL ftp : bondedPorts) {
				floatingTerminationPointListT.getFTP().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ftp).toMtosiFTP());
//				floatingTerminationPointListT.getFTP().add(ftp.getMtosiTranslator().toMtosiFTP());
			}
			final Set<FTP> ftps = ne.getMTOSIWorker().getFTPs();
			for (FTP ftp : ftps) {
				floatingTerminationPointListT.getFTP().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ftp).toMtosiFTP());
//				floatingTerminationPointListT.getFTP().add(ftp.getMtosiTranslator().toMtosiFTP());
			}
		} else {
			if (NEUtils.isF3Device(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
				((NetworkElementFSP150CM)ne).doNEEquipPortPolling(new Class[] {
						ProtectionGroupF3.class
				});
			}
			final Set<FTP> ftps = ne.getMTOSIWorker().getFTPs();
			for (FTP ftp : ftps) {
				floatingTerminationPointListT.getFTP().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ftp).toMtosiFTP());
//				floatingTerminationPointListT.getFTP().add(ftp.getMtosiTranslator().toMtosiFTP());
			}
		}
		return floatingTerminationPointListT;
	}

	public static FDFr getFDFr (final String fdfrName) throws ProcessingFailureException {
		final FDFr fdfr = LegacyMtosiMOFacadeImpl.getFDFr(fdfrName);
		if (fdfr instanceof FDFrHN4000) {
			((FDFrHN4000) fdfr).doPolling();
		}

		return fdfr;

	}

	public static FlowF3Impl getFDFrByName (final String fdfrName) throws ProcessingFailureException {
		final FlowF3Impl fdfr = LegacyMtosiMOFacadeImpl.getFDFrByName(fdfrName);

		return fdfr;
	}

	/**
	 * Return FlowDomainFragment
	 * @param fdfrName Name of the FDFr
	 * @return FlowDomainFragmentT
	 * @throws ProcessingFailureException when the fdfrNm is null
	 */
  public static FlowDomainFragmentT getFDFr (final NamingAttributesT fdfrName) throws ProcessingFailureException {
    final String fdfrNm = fdfrName.getFdfrNm();
    if(fdfrNm==null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, "FDFr name is missing.");
    }
    final FDFr fdfr = LegacyMtosiMOFacadeImpl.getFDFr(fdfrNm);
    if(fdfr == null) {
      return null;
    }
    return new MtosiTranslatorFacade( null, null, null).getMtosiTranslator(fdfr).toMtosiFDFr();
//    return fdfr.getMtosiTranslator().toMtosiFDFr();
  }

	/**
	 * Method return all fdfrs on specific ME.
	 * @param meName
	 * @return FDFrListT
	 * @throws ProcessingFailureException
	 */
	public static FDFrListT getAllFDFrs (final NamingAttributesT meName, NetworkElement ne) throws ProcessingFailureException {
		final v1.tmf854ext.adva.ObjectFactory objFactoryEx = new v1.tmf854ext.adva.ObjectFactory();
		final Set<FDFr> fdfrSet = ne.getMTOSIWorker().getFDFrs();
		final FDFrListT fdFrListT = objFactoryEx.createFDFrListT();
		for(FDFr fdfr : fdfrSet) {
			MtosiTranslator mtosiTranslator = new MtosiTranslatorFacade(null, null, null).getMtosiTranslator(fdfr);
			if(mtosiTranslator != null)
      	fdFrListT.getFdfr().add(mtosiTranslator.toMtosiFDFr());
//      fdFrListT.getFdfr().add(fdfr.getMtosiTranslator().toMtosiFDFr());
    }
		return fdFrListT;
	}

	/**
	 * Returns list of FDFrs on specific NE.
	 * @param tpName the name of termination point.
	 * @return FDFrListT
   * @throws ws.v1.tmf854.ProcessingFailureException when something went wrong
	 */
	public static FDFrListT getFDFrsWithTP(final NamingAttributesT tpName,NetworkElement ne) throws ProcessingFailureException {
		final v1.tmf854ext.adva.ObjectFactory objFactoryEx = new v1.tmf854ext.adva.ObjectFactory();
		final FDFrListT fdFrListT = objFactoryEx.createFDFrListT();
		Set<FDFr> fdfrSet = new HashSet<FDFr>(); // return value;
		if (ne instanceof NetworkElementHN4000) {
			getFDFrsWithTPonHN(ne, tpName, fdfrSet);

		} else {
			if (NamingTranslationFactory.isPort(tpName)) {
				final Port port = ManagedElementFactory.getPort(tpName);
				fdfrSet = port.getFDFrs();
			} else if (NamingTranslationFactory.isFlow(tpName)) {
				final Port port = getPort(tpName);
					final MTOSIFlowF3 flow = ManagedElementFactory.getCMFlow(tpName);
					if (flow == null) {
						ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
								MtosiErrorConstants.CTP_NOT_FOUND);
						throw new ProcessingFailureException(pfet.getReason(), pfet);
					}
					FDFr fdfr = flow.getFDFr();
					if (fdfr != null) {
						fdfrSet.add(fdfr);
					}
			} else if (NamingTranslationFactory.isFtp(tpName)) {
				final FTP ftp = ManagedElementFactory.getFtp(tpName);
				fdfrSet = ftp.getFDFrs();
			} else {
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
						MtosiErrorConstants.MANDATORY_PTP_CTP_FTP);
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
		}
		for (FDFr fdfr : fdfrSet) {
			fdFrListT.getFdfr().add(new MtosiTranslatorFacade(null, null, null).getMtosiTranslator(fdfr).toMtosiFDFr());
//			fdFrListT.getFdfr().add(fdfr.getMtosiTranslator().toMtosiFDFr());
		}
		return fdFrListT;
	}

	private static void getFDFrsWithTPonHN(NetworkElement ne, final NamingAttributesT tpName, Set<FDFr> fdfrSet) throws ProcessingFailureException {
		String ctpName = tpName.getCtpNm();

		if (NamingTranslationFactory.isPort(tpName)) {
			final Port port = ManagedElementFactory.getPort(tpName);
			
			pollAndAddFDFrs(fdfrSet,port.getFDFrs());
		} else if (NamingTranslationFactory.isFtp(tpName)) {
			final FTP ftp = ManagedElementFactory.getHN4000Ftp(tpName);
			pollAndAddFDFrs(fdfrSet,ftp.getFDFrs());
		} else if (NamingTranslationFactory.isFlow(tpName)) {
			final Port port = ManagedElementFactory.getPort(tpName);
			if (port instanceof PortHN4000Ethernet) {
				final Set<FlowHN4000> flows = ((PortHN4000Ethernet) port).getFlows();
				goWithTheFlow(fdfrSet, ctpName, flows);
			} else {
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
						MtosiErrorConstants.CTP_NOT_FOUND);
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
		} else if (NamingTranslationFactory.isFlowOnFtp(tpName)) {
			final String ftpName = tpName.getFtpNm();
			if (ctpName.indexOf("lag_fragment=") > 0) {
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
						MtosiErrorConstants.CTP_NOT_FOUND);
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
			NetworkElementHN4000 ne4000 = (NetworkElementHN4000) ne;
			if (ftpName.indexOf("ETH") > 0) {

				PortHN4000Ethernet2BASE_TL ftp = ne4000.getBondedPort(ftpName);
				if (ftp == null) {
					ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
							MtosiErrorConstants.FTP_NOT_FOUND);
					throw new ProcessingFailureException(pfet.getReason(), pfet);
				}
				Set<FlowHN4000> flows = ftp.getFlows();
				goWithTheFlow(fdfrSet, ctpName, flows);
			} else if (ftpName.indexOf("LAG") > 0) {
				FTPHN4000 ftp = (FTPHN4000) ne4000.getMTOSIWorker().getFTP(ftpName);
				if (ftp == null) {
					ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
							MtosiErrorConstants.FTP_NOT_FOUND);
					throw new ProcessingFailureException(pfet.getReason(), pfet);
				}
				 Set<FlowHN4000> flows = ftp.getFlows();
				 goWithTheFlow(fdfrSet, ctpName, flows);
			}
		} else {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
					MtosiErrorConstants.MANDATORY_PTP_CTP_FTP);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
	}
	private static void pollAndAddFDFrs(Set<FDFr> fdfrSet, Set<FDFr> frs) {
		for (FDFr fdfr : frs) {
			if (fdfr != null ) {
				if (fdfr instanceof FDFrHN4000) {
					((FDFrHN4000) fdfr).doPolling();
				}
			fdfrSet.add(fdfr);
			}
		}
	}

	private static void goWithTheFlow(Set<FDFr> fdfrSet, String ctpName, final Set<FlowHN4000> flows) throws ProcessingFailureException {
		if (flows == null || flows.isEmpty()) {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
					MtosiErrorConstants.CTP_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		boolean found = false;
		for (FlowHN4000 flow : flows) {
			if (ctpName.equals(flow.getFlowSPProperties().get(FlowSPPropertiesHN4000.VS.Desc))) {
				FDFr fdfr = flow.getFDFr();
				if (fdfr != null ) {
					if (fdfr instanceof FDFrHN4000) {
						((FDFrHN4000) fdfr).doPolling();
					}
					fdfrSet.add(fdfr);
				}
				found = true;
			}
		}
		if (!found) {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
					MtosiErrorConstants.CTP_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
	}

	@SuppressWarnings("unchecked")
	public static TerminationPointListT getAllCPTPs (final NamingAttributesT meName, NetworkElement ne) throws ProcessingFailureException
	{
		final ObjectFactory objFactory = new ObjectFactory();
		final TerminationPointListT resultList = objFactory.createTerminationPointListT();
		if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000
				|| ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400) {
			((NetworkElementHN4000)ne).doNEEquipPortPolling(new Class[]{
                    UniHN4000.class,
                    PortHN4000Ethernet.class
//                    PortHN4000Ethernet2BASE_TL.class, //Handled in getFTPs
//                    LAGHN4000.class
                    });

			Set<PortHN4000> ports = ((NetworkElementHN4000)ne).getPhysicalPortsByProtection(false);
//			Set<PortHN4000> ports = ne.getPorts();
			for (PortHN4000 port : ports) {
				if (port instanceof PortHN4000Ethernet) {
					TerminationPointT terminationPointT = objFactory.createTerminationPointT();
					terminationPointT.setPtp(new MtosiTranslatorFacade(ne,null,null).getMtosiTranslator(port).toMtosiPTP());
//					terminationPointT.setPtp(((MtosiSupported) port).getMtosiTranslator().toMtosiPTP());
					resultList.getTp().add(terminationPointT);
				}
			}
		} else {
			final Set retrievedList = ne.getMTOSIWorker().getPorts();

			for (Object port : retrievedList) {
				MtosiTranslator mtosiTranslator;
				TerminationPointT terminationPointT = objFactory.createTerminationPointT();
				if (port instanceof MTOSIPortF3Net) {
					NETPortSPPropertiesFSP150CM portProps = ((MTOSIPortF3Net) port).getPortSPProperties();
					if (!getFSP150CMMTOSIWorker(((NetworkElementFSP150CM) ne)).isPGEnabled(portProps.get(NETPortSPPropertiesFSP150CM.VI.ShelfIndex), portProps.get(NETPortSPPropertiesFSP150CM.VI.SlotIndex))) {
						terminationPointT.setPtp(new MtosiTranslatorFacade(ne,null,null).getMtosiTranslator(port).toMtosiPTP());
//						terminationPointT.setPtp(((MTOSIPortF3Net) port).getMtosiTranslator().toMtosiPTP());
						resultList.getTp().add(terminationPointT);
					}
				} else if ((mtosiTranslator = new MtosiTranslatorFacade(ne,null,null).getMtosiTranslator(port)) != null) {
					terminationPointT.setPtp(mtosiTranslator.toMtosiPTP());
//					terminationPointT.setPtp(((MtosiSupported) port).getMtosiTranslator().toMtosiPTP());
					resultList.getTp().add(terminationPointT);
				}
			}
		}
		 // When LAG is enabled, then the LAG FTP would be the CPTP and not the WAN ports anymore.
		final FloatingTerminationPointListT floatingTerminationPointListT = getAllFTPs(meName, ne,null, null);	
		for(FloatingTerminationPointT floatingTerminationPointT : floatingTerminationPointListT.getFTP())
		{
			TerminationPointT result = objFactory.createTerminationPointT();
			result.setFtp(floatingTerminationPointT);
			resultList.getTp().add(result);
		}

		return resultList;
	}

  /**
   * Method return TDFrT for the given NE and TDFr-Name.
   * @param ne                      The NetworkElementImpl
   * @param tdfrNm                  The TimingDomainFragment-Name
   * @return TimingDomainFragmentT
   * @throws ProcessingFailureException
   */
  public static TimingDomainFragmentT getTDFr(NetworkElement ne, final int shelfIndex, final int slotIndex, final String tdfrNm) throws ProcessingFailureException
  {
    final F3SyncImpl f3Sync = getFSP150CMMTOSIWorker(((NetworkElementFSP150CMImpl)ne)).getF3Sync(shelfIndex, slotIndex, tdfrNm);
    if(f3Sync == null) {
      return null;
    }
    f3Sync.doPollingVolatile();
    return new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(f3Sync).toMtosiTDFr();
//    return f3Sync.getMtosiTranslator().toMtosiTDFr();
  }

  /**
   * Method return TDFrListT (all TDFrT) for the given NE.
   * @param ne                      The NetworkElementImpl
   * @param layerRateList           List of layer rates if specified
   * @return List of TDFrT
   * @throws ProcessingFailureException
   */
  public static TDFrListT getAllTDFrs(NetworkElement ne, LayerRateListT layerRateList) throws ProcessingFailureException
  {
    final v1.tmf854ext.adva.ObjectFactory objFactoryEx = new v1.tmf854ext.adva.ObjectFactory();
    TDFrListT tdfrList = objFactoryEx.createTDFrListT();

    // with LayerRateList
    if(layerRateList != null && layerRateList.getLayerRate() != null && layerRateList.getLayerRate().size() > 0) {
      return null;

    // without LayerRateList
    } else {
      for (F3SyncImpl f3Sync : getFSP150CMMTOSIWorker(((NetworkElementFSP150CMImpl)ne)).getAllF3Syncs()) {
        f3Sync.doPollingVolatile();
        tdfrList.getTdfr().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(f3Sync).toMtosiTDFr());
//        tdfrList.getTdfr().add(f3Sync.getMtosiTranslator().toMtosiTDFr());
      }
    }

    return tdfrList;
  }
	/**
	 * Determine if the namingAttribute is looking for a Policer or a shaper and return the appropriate Object.
	 * @param namingAttributes
	 * @return
	 * @throws ProcessingFailureException 
	 */
	public static Object getTCP(NamingAttributesT namingAttributes) throws ProcessingFailureException {
		String tcpNm = namingAttributes.getTcpNm();
		if (tcpNm.startsWith(MtosiConstants.ING_POLICER_TEXT) ||
				tcpNm.startsWith(MtosiConstants.EG_POLICER_TEXT)) {
			return getPolicer(namingAttributes);
		} else {
			return getShaper(namingAttributes);
		}
	}

  private static NetworkElementFSP150CMMTOSIOperations getFSP150CMMTOSIWorker(NetworkElementFSP150CM ne) {
    return (NetworkElementFSP150CMMTOSIOperations)ne.getMTOSIWorker();
  }
}