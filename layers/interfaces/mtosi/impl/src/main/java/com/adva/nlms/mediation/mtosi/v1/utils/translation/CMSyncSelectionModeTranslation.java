/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum CMSyncSelectionModeTranslation implements TranslatableEnum {
  NOT_APPLICABLE  (0, "n/a"),
  QL_MODE         (1, "QL-Mode"),
  PRIORITY_MODE   (2, "Priority-Mode");

  private final int    mibValue;
  private final String mtosiString;

  private CMSyncSelectionModeTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}
