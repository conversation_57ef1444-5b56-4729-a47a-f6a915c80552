/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.PortFSP150CPNetwork;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;

import jakarta.xml.ws.Holder;

public class StopContinuityTestWorkerCP extends StopContinuityTestWorker
{
  public StopContinuityTestWorkerCP(Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne)
  {
    super(mtosiHeader, namingAttributes, ne);
  }

  @Override
  protected void mediate() throws Exception {
    super.mediate();
    if (!(port instanceof PortFSP150CPNetwork)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "Continuity test should be run on Network Port.");
    }
    PortFSP150CPNetwork portNET = (PortFSP150CPNetwork) port;
    transact(portNET);
  }

  @Override
  protected void response () throws Exception {
    // empty method
  }

  private void transact(PortFSP150CPNetwork port) throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    final NetworkElement locks[] = new NetworkElement[]
            { ne };
    final Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "StopContinuityTestWorkerCP");
    try {
      port.stopContinuityTest();
      NetTransactionManager.commitNetTransaction(id);
    } catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      NetTransactionManager.ensureEnd(id);
    }
  }
}
