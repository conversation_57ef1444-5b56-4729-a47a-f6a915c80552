/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.serviceProvisioning.FDFrEndIDs;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FTPSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ServiceSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ServiceSPPropertiesFSP150CP;
import com.adva.nlms.mediation.common.serviceProvisioning.WANPortSPPropertiesFSP150CP;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.fsp150cp_mx.PortFSP150CP_MXAccess;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.FDFrFTPEndFSP150CPIDs;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.FDFrPortEndFSP150CPIDs;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.NetworkElementFSP150CP;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.PortFSP150CPAccess;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.PortFSP150CPNetwork;
import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiFDFrMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.CreateAndActivateFDFrWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.TPDataListT;
import v1.tmf854.TPDataT;
import v1.tmf854ext.adva.CreateAndActivateFDFrT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.ArrayList;
import java.util.Set;

public class CreateAndActivateFDFrWorkerCP extends CreateAndActivateFDFrWorker
{
	Logger LOG = LogManager.getLogger(this.getClass().getName());

  private PortFSP150CPAccess portACC;
	private FTP ftp;
	private PortFSP150CPNetwork portNETA;
	private PortFSP150CPNetwork portNETB;
	private TPDataT tpDataAccess;
	private TPDataT tpDataNetworkA;
	private TPDataT tpDataNetworkB;
	private TPDataT tpDataFtp;
	private boolean isAFtp = false;

  public CreateAndActivateFDFrWorkerCP(CreateAndActivateFDFrT mtosiBody, Holder<HeaderT> mtosiHeader,
                                       NetworkElement ne, NamingAttributesListT aEnd, NamingAttributesListT zEnd)
  {
    super(mtosiBody, mtosiHeader, ne, aEnd, zEnd);
  }

	@Override
  protected TPDataListT getUpdatedTPs() throws ProcessingFailureException {
		// Get updated objects
		ObjectFactory factory = new ObjectFactory();
		TPDataListT tpsToModify = factory.createTPDataListT();
		if (tpDataAccess != null && portACC != null) {
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataAccess.getTpName())).toMtosiPTPasTPDataT());
			//			tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataAccess.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
		if (tpDataNetworkA != null && portNETA != null) {
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataNetworkA.getTpName())).toMtosiPTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataNetworkA.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
		if (tpDataNetworkB != null && portNETB != null) {
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataNetworkB.getTpName())).toMtosiPTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataNetworkB.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
		if (tpDataFtp != null && ftp != null) {
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  null).getMtosiTranslator(ManagedElementFactory.getFtp(tpDataFtp.getTpName())).toMtosiFTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getFtp(tpDataFtp.getTpName()).getMtosiTranslator().toMtosiFTPasTPDataT());
		}
		return tpsToModify;
	}

  private void transact(NetworkElementFSP150CP ne, ServiceSPPropertiesFSP150CP propsACC,
                        WANPortSPPropertiesFSP150CP propsNETA, WANPortSPPropertiesFSP150CP propsNETB, FTPSPProperties propsFTP,
                        FDFrSPProperties fdfrProps)
          throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "CreateAndActivateFDFrWorker");
    ne.getMTOSIWorker().setFDFrOperationInProgress(fdfrProps.get(FDFrSPProperties.VS.FDFrName), true);
    try {
      if (propsACC == null) {
        propsACC = new ServiceSPPropertiesFSP150CP();
        propsACC.set(ServiceSPProperties.VI.SvcIndex, portACC.getIndex().toInt());
      }
      if (propsACC != null) {
        propsACC.set(ServiceSPProperties.VS.CircuitName, fdfrProps.get(FDFrSPProperties.VS.FDFrName));
        logSecurity(ne, SystemAction.ModifyNetwork, portACC.getMtosiName());
        portACC.setSettings(propsACC);
      }
      if (propsNETA != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, portNETA.getMtosiName());
        portNETA.setSettings(propsNETA);
      }
      if (propsNETB != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, portNETB.getMtosiName());
        portNETB.setSettings(propsNETB);
      }
      if (propsFTP != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, "ftpNm=" + ftp.getFTPName());
        ftp.setFTPSPProperties(propsFTP);
      }
      ne.getMTOSIWorker().createFDFr(fdfrProps);
      NetTransactionManager.commitNetTransaction(id);
      ne.logSROperation(SROperationState.SERVICE_CREATION_SUCCESS, fdfrProps.get(FDFrSPProperties.VS.FDFrName));
    } catch (NetTransactionException e) {
      ne.logSROperation(SROperationState.SERVICE_CREATION_FAILURE, fdfrProps.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ne.logSROperation(SROperationState.SERVICE_CREATION_FAILURE, fdfrProps.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ne.logSROperation(SROperationState.SERVICE_CREATION_FAILURE, fdfrProps.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      ne.getMTOSIWorker().setFDFrOperationInProgress(fdfrProps.get(FDFrSPProperties.VS.FDFrName), false);
      NetTransactionManager.ensureEnd(id);
    }
  }

	@Override
  protected void mediate() throws Exception {
		// check to make sure this FDFr doesn't exist yet
		Set set = ne.getMTOSIWorker().getFDFrs();
		if (set.size() > 0)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(),
					ExceptionUtils.EXCPT_INVALID_INPUT, "An FDFr already exists on this device.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		// get the zEnd and zEnd, and determine which is ACC and which is FTP
		NamingAttributesT aName = aEnd.getName().get(0);
		NamingAttributesT zName = zEnd.getName().get(0);
		Port port;
		if (NamingTranslationFactory.isFtp(aName) && NamingTranslationFactory.isPort(zName))
		{
			isAFtp = true;
			port = ManagedElementFactory.getPort(zEnd.getName().get(0));
			ftp = ManagedElementFactory.getFtp(aEnd.getName().get(0));
		}
		else if (NamingTranslationFactory.isFtp(zName) && NamingTranslationFactory.isPort(aName))
		{
			port = ManagedElementFactory.getPort(aEnd.getName().get(0));
			ftp = ManagedElementFactory.getFtp(zEnd.getName().get(0));
		}
		else
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(),
					ExceptionUtils.EXCPT_TP_INVALID_ENDPOINT, "The specified endpoints must be a PTP and an FTP.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		if (!(port instanceof PortFSP150CP_MXAccess))
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(),
					ExceptionUtils.EXCPT_INVALID_INPUT, "Specified Port is not an Access Port.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		portACC = (PortFSP150CPAccess) port;
		NetworkElementFSP150CP cpNE = (NetworkElementFSP150CP) ne;
		ServiceSPPropertiesFSP150CP propsACC = null;
		WANPortSPPropertiesFSP150CP propsNETA = null;
		WANPortSPPropertiesFSP150CP propsNETB = null;
		FTPSPProperties propsFTP = null;
		if (tpsToModify != null)
		{
			if (!MtosiTPMediator.checkTPToModifySameNE(ne, tpsToModify))
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(),
						ExceptionUtils.EXCPT_INVALID_INPUT, "The specified TPs must be on the same Network Element.");
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
			tpDataAccess = MtosiTPMediator.getTPDataTForCPAccess(tpsToModify);
			tpDataNetworkA = MtosiTPMediator.getTPDataTForCPNetworkA(tpsToModify);
			tpDataNetworkB = MtosiTPMediator.getTPDataTForCPNetworkB(tpsToModify);
			tpDataFtp = MtosiTPMediator.getTPDataTForFTP(tpsToModify);
		}
		if (tpDataAccess != null)
		{
			propsACC = MtosiTPMediator.mtosiTPDataTToCPAccessProperties(tpDataAccess, portACC);
		}
		if (tpDataNetworkA != null)
		{
			Port portA = ManagedElementFactory.getPort(tpDataNetworkA.getTpName());
			portNETA = (PortFSP150CPNetwork) portA;
			propsNETA = MtosiTPMediator.mtosiTPDataTToCPNetworkProperties(tpDataNetworkA, portNETA);
		}
		if (tpDataNetworkB != null)
		{
			Port portB = ManagedElementFactory.getPort(tpDataNetworkB.getTpName());
			portNETB = (PortFSP150CPNetwork) portB;
			propsNETB = MtosiTPMediator.mtosiTPDataTToCPNetworkProperties(tpDataNetworkB, portNETB);
		}
		if (tpDataFtp != null)
		{
			// ftp = ManagedElementFactory.getFtp(tpDataFtp.getTpName());
			propsFTP = MtosiTPMediator.mtosiTPDataTToCPFTPProperties(tpDataFtp, ftp);
		}
		int aIndex = ftp.getAPort().getIndex().toInt();
		int bIndex = ftp.getBPort().getIndex().toInt();
		FDFrFTPEndFSP150CPIDs wanEnds = new FDFrFTPEndFSP150CPIDs(ftp.getFTPSPProperties().get(FTPSPProperties.VS.FtpName), aIndex, bIndex);
		FDFrPortEndFSP150CPIDs accEnds = new FDFrPortEndFSP150CPIDs(portACC.getServicePortSPProperties().get(ServiceSPProperties.VI.SvcIndex));
		ArrayList<FDFrEndIDs> wanEndsIDs = new ArrayList<FDFrEndIDs>();
		wanEndsIDs.add(wanEnds);
		ArrayList<FDFrEndIDs> accEndsIDs = new ArrayList<FDFrEndIDs>();
		accEndsIDs.add(accEnds);
		FDFrSPProperties fdfrProps;
		if (isAFtp)
		{
			fdfrProps = MtosiFDFrMediator.mtosiCreateFDFRToFDFRSPProperties(createData, wanEndsIDs, accEndsIDs);
		}
		else
		{
			fdfrProps = MtosiFDFrMediator.mtosiCreateFDFRToFDFRSPProperties(createData, accEndsIDs, wanEndsIDs);
		}
		transact(cpNE, propsACC, propsNETA, propsNETB, propsFTP, fdfrProps);
	}
}