/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import jakarta.xml.ws.Holder;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.TerminationPointListT;
import v1.tmf854ext.adva.GetAllCPTPsResponseT;
import v1.tmf854ext.adva.GetAllCPTPsT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;

public class GetAllCPTPsWorker extends AbstractMtosiWorker {
  protected GetAllCPTPsT mtosiBody;
	protected GetAllCPTPsResponseT response = new GetAllCPTPsResponseT();
  protected NamingAttributesT meName;
  protected TerminationPointListT terminationPointListT;
  protected NetworkElement ne;


  public GetAllCPTPsWorker (final GetAllCPTPsT mtosiBody, final Holder<HeaderT> mtosiHeader) {
		super(mtosiHeader, "getAllCPTPs", "getAllCPTPs", "getAllCPTPsResponse");
		this.mtosiBody = mtosiBody;
		
	}

  @Override
  protected void parse() throws Exception {
    if ((meName = mtosiBody.getMeName()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              MtosiErrorConstants.INVALID_FILTER);
    }

    if (!NamingTranslationFactory.isManagementDomain(meName)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!meName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MD_NOT_FOUND);
    }
    // It is a little ugly setting the ne here...
    //There is an assumption, that the ne is set before validate() and execute()
	 ne = ManagedElementFactory.getAndValidateNE(meName);
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(ne.getDefaultNetworkElementTypeString());
  }


  @Override
  protected void mediate() throws Exception {
    terminationPointListT = ManagedElementFactory.getAllCPTPs(meName, ne);
  }

  @Override
  protected void response() throws Exception {
    response.setTpList(terminationPointListT);
  }

  @Override
  public GetAllCPTPsResponseT getSuccessResponse() {
		if (response == null)
			return null;

		response.setTmf854Version(MtosiConstants.VERSION);
		return response;
	}
}