/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp20X;

import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPProperties;
import com.adva.nlms.mediation.config.f3.NetworkElementF3;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm.Fsp150cmMediator;
import v1.tmf854.EquipmentHolderT;
import v1.tmf854.ObjectFactory;

/**
 * Network Element Mediator for GE20X boxes. Inherit most behaviour from the CM,
 * but we need an extra DC PSU Type for DC24
 * 
 */

public class FspGE20XMediator extends Fsp150cmMediator {

	public FspGE20XMediator(NetworkElementF3 ne) {
		super(ne);
	}

	@Override
  protected void getAcceptablePSUEquipmentTypes(ObjectFactory objFactory, EquipmentHolderT equipmentHolder, EquipmentSPProperties properties) {
		EquipmentHolderT.AcceptableEquipmentTypeList list = objFactory.createEquipmentHolderTAcceptableEquipmentTypeList();
		list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_PSU_AC);
		list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_PSU_DC);
		list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_PSU_DC24);
		equipmentHolder.setAcceptableEquipmentTypeList(objFactory.createEquipmentHolderTAcceptableEquipmentTypeList(list));
	}

}
