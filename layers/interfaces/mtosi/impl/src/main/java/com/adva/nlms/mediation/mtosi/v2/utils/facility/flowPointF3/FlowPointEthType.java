/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */

package com.adva.nlms.mediation.mtosi.v2.utils.facility.flowPointF3;

import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.DTOBuilder;
import com.adva.nlms.mediation.config.dto.attr.AbstractEthernetTrafficPortAttr;
import com.adva.nlms.mediation.config.dto.attr.AbstractFlowPointF3Attr;
import com.adva.nlms.mediation.config.dto.attr.EthernetTrafficPortF3Attr;
import com.adva.nlms.mediation.config.dto.attr.FlowPointAccF3Attr;
import com.adva.nlms.mediation.config.dto.attr.FlowPointF3Attr;
import com.adva.nlms.mediation.config.dto.attr.FlowPointNetF3Attr;
import com.adva.nlms.mediation.config.dto.attr.PortF3AccAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3NetAttr;

public enum FlowPointEthType {
  NA("N/A", null, null),
  ACC("ACC", FlowPointAccF3Attr.class, PortF3AccAttr.class),
  NET("NET", FlowPointNetF3Attr.class, PortF3NetAttr.class),
  LAG("/lag=", FlowPointF3Attr.class, EthernetTrafficPortF3Attr.class),
  ETH("ETH", FlowPointF3Attr.class, EthernetTrafficPortF3Attr.class);

  private final String suffix;
  private final Class flowClazz;
  private final Class portClazz;

  FlowPointEthType(String suffix, Class flowClazz, Class portClazz) {
    this.suffix = suffix;
    this.flowClazz = flowClazz;
    this.portClazz = portClazz;
  }

  public synchronized static FlowPointEthType getTypeByName(String portName){
    if(portName.contains(ACC.suffix))
      return ACC;
    else if(portName.contains(NET.suffix))
      return NET;
    else if(portName.contains(ETH.suffix))
      return ETH;
    else if(portName.contains(LAG.suffix))
      return LAG;
    else
      return NA;
  }

  public synchronized static FlowPointEthType getTypeByClass(Class clazz){
    if(clazz.equals(ACC.flowClazz) || clazz.equals(ACC.portClazz))
      return ACC;
    else if(clazz.equals(NET.flowClazz) || clazz.equals(NET.portClazz))
      return NET;
    else if(clazz.equals(ETH.flowClazz) || clazz.equals(ETH.portClazz))
      return ETH;
    else
      return NA;
  }

  @SuppressWarnings("unchecked")
  public synchronized static AbstractFlowPointEth createFlowPointF3(int neId, DTO dto){
    if(dto.getAttributesGroupClass().equals(ACC.getFlowClazz())) return new FlowPointAccImpl(neId,dto);
    if(dto.getAttributesGroupClass().equals(NET.getFlowClazz())) return new FlowPointNetImpl(neId,dto);
    if(dto.getAttributesGroupClass().equals(ETH.getFlowClazz())) return new FlowPointF3Impl(neId,dto);
    return null;
  }

  @SuppressWarnings("unchecked")
  public synchronized static AbstractPortF3Eth createPortF3Eth(int neId,DTO dto){
    if(dto.getAttributesGroupClass().equals(ACC.getPortClazz())) return new PortAccF3Impl(neId,dto);
    if(dto.getAttributesGroupClass().equals(NET.getPortClazz())) return new PortNetF3Impl(neId,dto);
    if(dto.getAttributesGroupClass().equals(ETH.getPortClazz())) return new EthernetTrafficPortF3Impl(neId,dto);
    return null;
  }

  @SuppressWarnings("unchecked")
  public synchronized static AbstractPortF3Eth createEmptyPortF3Eth(int neId,DTO dto){
    if(dto.getAttributesGroupClass().equals(ACC.getPortClazz())) return new PortAccF3Impl(neId, DTOBuilder.getInstance().newDTO(PortF3AccAttr.class));
    if(dto.getAttributesGroupClass().equals(NET.getPortClazz())) return new PortNetF3Impl(neId, DTOBuilder.getInstance().newDTO(PortF3NetAttr.class));
    if(dto.getAttributesGroupClass().equals(ETH.getPortClazz())) return new EthernetTrafficPortF3Impl(neId, DTOBuilder.getInstance().newDTO(EthernetTrafficPortF3Attr.class));
    return null;
  }

  @SuppressWarnings("unchecked")
  public synchronized <Y extends AbstractFlowPointF3Attr> Class<Y> getFlowClazz() {
    return flowClazz;
  }

  @SuppressWarnings("unchecked")
  public synchronized <Y extends AbstractEthernetTrafficPortAttr> Class<Y> getPortClazz() {
    return portClazz;
  }
}
