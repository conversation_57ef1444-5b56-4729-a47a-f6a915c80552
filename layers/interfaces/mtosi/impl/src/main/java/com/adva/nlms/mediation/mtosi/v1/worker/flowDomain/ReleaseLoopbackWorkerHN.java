/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.common.snmp.hn.HNLoopbackStatusTypeTranslation;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000EthernetSProperties;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

/**
 * Created by IntelliJ IDEA.
 * User: PawelP
 * Date: 2008-10-13
 * Time: 14:45:10
 * Release loopback worker
 */
public class ReleaseLoopbackWorkerHN  extends ReleaseLoopbackWorker {
   public ReleaseLoopbackWorkerHN (Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne)
  {
    super(mtosiHeader, namingAttributes, ne);
  }

  @Override
  protected void mediate() throws Exception {
	  Port port = ManagedElementFactory.getHN4000Port(namingAttributes);
	  
    if (port == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The specified Port was not found.");
    }
    if (port instanceof PortHN4000Ethernet) {
      PortHN4000Ethernet ethPort = (PortHN4000Ethernet) port;
      int loopbackStatus = ethPort.getPortSPProperties().get(PortHN4000EthernetSProperties.VI.LoopbackStatus);
      if(loopbackStatus == HNLoopbackStatusTypeTranslation.None.getMIBValue())
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
                ExceptionUtils.EXCPT_INVALID_INPUT, "Loopback is already released.");
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      transactEthPort(ethPort);
    } else {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The specified Port type or entity does not support Loopback.");
    }
  }

  @Override
  protected void response () throws Exception {
    // empty method
  }

  private void transactEthPort(PortHN4000Ethernet port) throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure
  {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "releaseLoopback");
    try
    {
      logSecurity(ne, SystemAction.ReleaseLoopback, port.getMtosiName());
      port.releaseLoopback();
      NetTransactionManager.commitNetTransaction(id);
    }
    catch (NetTransactionException e)
    {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    catch (SPValidationException e)
    {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    catch (SNMPCommFailure e)
    {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    finally
    {
      NetTransactionManager.ensureEnd(id);
    }
  }
}
