/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;

/**
  *
  */
public class HNEgressRateTranslation {

    public static String getMtosiString(final Long mibValue) {
    	if (mibValue == null) {
    		return MtosiConstants.NOT_APPLICABLE;
    	}
    	return mibValue != 0 ? "True" : "False";
    }
    
    public static long getMibValue(final String name) {
    	if ("True".equals(name)) {
    		return 1;
    	} else if ("False".equals(name)) {
    		return 0;
    	}
    	return -1;
    }
}
