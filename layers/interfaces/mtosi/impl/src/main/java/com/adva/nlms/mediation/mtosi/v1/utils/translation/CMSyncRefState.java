/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum CMSyncRefState implements TranslatableEnum {
  NOT_APPLICABLE  (0, "NotAvailable"),
  ACTIVE          (1, "Active"),
  STANDBY         (2, "Standby"),
  UNAVAILABLE     (3, "Unavailable"),
  LOCKEDOUT       (4, "LockedOut");
  
  private final int    mibValue;
  private final String mtosiString;

  private CMSyncRefState(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}
