/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;

/**
      efmCuPmeFltStatus  OBJECT-TYPE
        SYNTAX      BITS {
          lossOfFraming(0),
          snrMgnDefect(1),
          lineAtnDefect(2),
          deviceFault(3),
          configInitFailure(4),
          protocolInitFailure(5)
        }
  */
public enum HNLineStatusTranslation {
    lossOfFraming(0),
    PROP_HATTERAS_snrMgnDefect(1),
    PROP_HATTERAS_lineAtnDefect(2),
    PROP_HATTERAS_deviceFault(3),
    configInitFailure(4),
    protocolInitFailure(5);
    
    private int mibValue;
    private int getMibValue() {
		return mibValue;
	}

	private HNLineStatusTranslation(int code) {
    	this.mibValue = code;
    }

    public static String getMtosiString(final Integer mibValue) {
    	if (mibValue == null) {
    		return MtosiConstants.NOT_APPLICABLE;
    	}
    	for (HNLineStatusTranslation value: values() ) {
    		if (value.getMibValue() == mibValue.intValue()) {
    			return value.name(); 
    		}
    	}
    	//the value was not found, return the value passed in
    	return String.valueOf(mibValue);
    }
    
    public static int getMibValue(final String name) {
    	for (HNLineStatusTranslation value: values() ) {
    		if (value.name().equals(name)) {
    			return value.getMibValue(); 
    		}
    	}
    	//Returning -1, the value passed in was not found.
    	return -1;
    }

  public static String getLineStatesFromIntValue (int value) {
    String binaryValue=String.format("%8s", Integer.toBinaryString(value & 0xFF)).replace(' ', '0');
    binaryValue=new StringBuilder(binaryValue).reverse().toString();
    StringBuilder builder = new StringBuilder();
    for (int i=0; i<binaryValue.length();i++) {
      if (binaryValue.substring(i,i+1).equals("1")) {
        builder.append(getMtosiString(i));
        builder.append(",");
      }
    }
    if (builder.toString().length()!=0)
      return builder.toString().substring(0,builder.toString().length()-1);
    else
      return "noDefect";
  }

}
