/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.serviceProvisioning.LAGSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000BondingSPProperties;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.f3.entity.port.acc.MTOSIPortF3Acc;
import com.adva.nlms.mediation.config.f3.entity.port.net.MTOSIPortF3Net;
import com.adva.nlms.mediation.config.hn4000.FlowHN4000;
import com.adva.nlms.mediation.config.hn4000.LAGHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet2BASE_TL;
import com.adva.nlms.mediation.config.hn4000.mtosi.FTPHN4000;
import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.hn4000.FlowHN4000Translator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import v1.tmf854.ConnectionTerminationPointListT;
import v1.tmf854.GetContainedCurrentCTPsResponseT;
import v1.tmf854.GetContainedCurrentCTPsT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.HashMap;
import java.util.Map.Entry;
import java.util.Set;

//import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiSupported;

public class GetContainedCurrentCTPsWorker extends AbstractMtosiWorker {
	protected GetContainedCurrentCTPsT mtosiBody;
	protected GetContainedCurrentCTPsResponseT response = new GetContainedCurrentCTPsResponseT();
	protected NamingAttributesT namingAttributes;
	protected ConnectionTerminationPointListT connectionTerminationPointListT;
	protected NetworkElement ne;

	// TODO: refactor this worker by using ne type!
	public GetContainedCurrentCTPsWorker(GetContainedCurrentCTPsT mtosiBody, Holder<HeaderT> mtosiHeader) {
		super(mtosiHeader, "getContainedCurrentCTPs", "getContainedCurrentCTPs", "getContainedCurrentCTPsResponse");
		this.mtosiBody = mtosiBody;
	}

	@Override
  protected void parse() throws Exception {
		if ((namingAttributes = mtosiBody.getTpName()) == null) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, "The TP name has not been specified.");
		}

		if (!NamingTranslationFactory.isMandatoryElementsPresentForTP(namingAttributes)) { // ME
																							// MD
																							// PTP
																							// CTP
																							// FTP
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.MANDATORY_PTP_CTP_FTP);
		}

		if (!namingAttributes.getMdNm().equals(OSFactory.getMDNm())) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.MD_NOT_FOUND);
		}
	}

	  @Override
	  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
		  ne = ManagedElementFactory.getAndValidateNE(namingAttributes);
		  validator.validate(ne.getDefaultNetworkElementTypeString());
	  }

	@Override
  protected void mediate() throws Exception {
		if (NamingTranslationFactory.isPort(namingAttributes)) {
			executeContainedCurrentCTPsOnPTP();
		} else if (NamingTranslationFactory.isFtp(namingAttributes)) {
			executeContainedCurrentCTPsOnFTP();
		} else {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, "TP must be a PTP or FTP. CTPs are not supported.");
		}
	}

	private void executeContainedCurrentCTPsOnPTP() throws Exception {
		final Port port = ManagedElementFactory.getPort(namingAttributes);
		final ObjectFactory objectFactory = new ObjectFactory();
		connectionTerminationPointListT = objectFactory.createConnectionTerminationPointListT();

        if (port instanceof MTOSIPortF3Acc) {
			final Set<MTOSIFlowF3> set = ((MTOSIPortF3Acc) port).getFlowFSP150CMs();
			for (MTOSIFlowF3 flow : set) {
				connectionTerminationPointListT.getCTP().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(flow).toMtosiCTP());
//				connectionTerminationPointListT.getCTP().add(flow.getMtosiTranslator().toMtosiCTP());
			}
		} else if (port instanceof PortHN4000Ethernet) {
			Set<FlowHN4000> flows = ((PortHN4000Ethernet) port).getFlows();
			for (FlowHN4000 flow : flows) {
				connectionTerminationPointListT.getCTP().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(flow).toMtosiCTP());
//				connectionTerminationPointListT.getCTP().add(((MtosiSupported) flow).getMtosiTranslator().toMtosiCTP());
			}
		}else if(port instanceof MTOSIPortF3Net){
            //Valid port but not supported => return empty response
        }else {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, "The specified PTP is not supported.");
		}
	}

	private void executeContainedCurrentCTPsOnFTP() throws Exception {
		final ObjectFactory objectFactory = new ObjectFactory();
		connectionTerminationPointListT = objectFactory.createConnectionTerminationPointListT();
		NetworkElement ne = ManagedElementFactory.getAndValidateNE(namingAttributes);
		if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400
				|| ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000) {
			FTPHN4000 ftp = ManagedElementFactory.getHN4000Ftp(namingAttributes);
			for (FlowHN4000 flow : ftp.getFlows()) {
				connectionTerminationPointListT.getCTP().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(flow).toMtosiCTP());
//				connectionTerminationPointListT.getCTP().add(((MtosiSupported) flow).getMtosiTranslator().toMtosiCTP());
			}
			getFragmentCTPs(connectionTerminationPointListT, ftp);
		} else {
			final FTP floatingTerminationPoint = ManagedElementFactory.getFtp(namingAttributes);
			connectionTerminationPointListT.getCTP().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(floatingTerminationPoint.getAPort()).toMtosiCTP());
//			connectionTerminationPointListT.getCTP().add(((MtosiSupported) floatingTerminationPoint.getAPort()).getMtosiTranslator().toMtosiCTP());
			connectionTerminationPointListT.getCTP().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(floatingTerminationPoint.getBPort()).toMtosiCTP());
//			connectionTerminationPointListT.getCTP().add(((MtosiSupported) floatingTerminationPoint.getBPort()).getMtosiTranslator().toMtosiCTP());
		}
	}

	private void getFragmentCTPs(ConnectionTerminationPointListT connectionTerminationPointListT2, FTPHN4000 ftp) throws ProcessingFailureException {
		HashMap<Integer, Set<Integer>> portMap = null;

		if (ftp instanceof LAGHN4000) {
			portMap = ((LAGSPPropertiesHN4000) ftp.getFTPSPProperties()).get(LAGSPPropertiesHN4000.VH.EthPortMap);
			for (Entry<Integer, Set<Integer>> entries : portMap.entrySet()) {
				int shelf = entries.getKey();
				for (Integer portNum : entries.getValue()) {
					int fragmentNumber;
					String lagMember, ctpName;
					if (shelf > 0) {
						fragmentNumber = ((shelf - 1) * 2) + portNum;
						lagMember = "/shelf=" + shelf + "/slot=2/port=ETH-" + portNum;
						ctpName = "/lag_fragment=" + fragmentNumber;
						connectionTerminationPointListT2.getCTP().add(FlowHN4000Translator.toMtosiSimpleCTP(ftp, ctpName, lagMember, null));
					} else {
						//Not supported for BT...
						lagMember = "/port=ETH-" + portNum;
						ctpName = "/lag_fragment=" + portNum;
						connectionTerminationPointListT2.getCTP().add(FlowHN4000Translator.toMtosiSimpleCTP(ftp, ctpName, lagMember, null));
					}
				}
			}
		} else if (ftp instanceof PortHN4000Ethernet2BASE_TL) {
			portMap = ((PortHN4000Ethernet2BASE_TL) ftp).getPortSPProperties().get(PortHN4000BondingSPProperties.VH.PmePortMap);
			for (Entry<Integer, Set<Integer>> entries : portMap.entrySet()) {
				int shelf = entries.getKey();
				for (Integer portNum : entries.getValue()) {
					int fragmentNumber = ((shelf - 1) * 40) + portNum;
					String lagMember = "/shelf=" + shelf + "/slot=1/port=2BPME-" + portNum;
					String ctpName = "/lag_fragment=" + fragmentNumber;

					connectionTerminationPointListT2.getCTP().add(FlowHN4000Translator.toMtosiSimpleCTP(ftp, ctpName, lagMember, null));
				}

			}
		}
	}

	@Override
  protected void response() throws Exception {
		response.setCtpList(connectionTerminationPointListT);
	}

	@Override
  public GetContainedCurrentCTPsResponseT getSuccessResponse() {
		if (response == null)
			return null;

		response.setTmf854Version(MtosiConstants.VERSION);
		return response;
	}
}
