/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.mediation.common.serviceProvisioning.NETPortSPPropertiesFSP150CM;

public enum CMLoopbackStatusTypeTranslation implements TranslatableEnum {
  LPBK_NONE                 (1, "None"),
  EFM_OAM_REMOTE            (2, "RemoteEFMOAM"),
  EFM_OAM_REMOTE_TIMED      (3, "RemoteEFMOAMTimed"),
  FACILITY_PORT             (4, "Facility"),
  FACILITY_PORT_TIMED       (5, "FacilityTimed"),
  FACILITY_VLAN             (6, "VLANFacility"),
  TERMINAL_PORT             (7, "Terminal"),
  TERMINAL_PORT_TIMED       (8, "TerminalTimed"),
  TERMINAL_VLAN             (9, "VLANTerminal"),
  EFM_OAM_TAILEND           (10, "RemoteEFMOAMTailEnd"),
  NOT_APPLICABLE            (11, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private CMLoopbackStatusTypeTranslation (final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }

  public static String getOuterLoopbackList(NETPortSPPropertiesFSP150CM props) {
    int bitField = 0x1; //001
    StringBuilder resultBuffer = new StringBuilder();
    for(int i = 1; i <= 3; i++) {
      if((props.get(NETPortSPPropertiesFSP150CM.VI.OuterVlanLoopbackMask) & bitField) != 0) {
        if(resultBuffer.length() > 0) {
          resultBuffer.append(",");
        }
        switch(i) {
          case 1:
            resultBuffer.append(props.get(NETPortSPPropertiesFSP150CM.VS.OuterVlanLoopback1));
            break;
          case 2:
            resultBuffer.append(props.get(NETPortSPPropertiesFSP150CM.VS.OuterVlanLoopback2));
            break;
          case 3:
            resultBuffer.append(props.get(NETPortSPPropertiesFSP150CM.VS.OuterVlanLoopback3));
            break;
        }
      }
      bitField <<=1;
    }
    return resultBuffer.toString();
  }

  public static String getInnerLoopbackList(NETPortSPPropertiesFSP150CM props)
  {
    int bitField = 0x1; //001
    StringBuilder resultBuffer = new StringBuilder();
    for(int i = 1; i <= 3; i++) {
      if((props.get(NETPortSPPropertiesFSP150CM.VI.InnerVlanLoopbackMask) & bitField) != 0) {
        if(resultBuffer.length() > 0) {
          resultBuffer.append(",");
        }
        switch(i) {
          case 1:
            resultBuffer.append(props.get(NETPortSPPropertiesFSP150CM.VS.InnerVlanLoopback1));
            break;
          case 2:
            resultBuffer.append(props.get(NETPortSPPropertiesFSP150CM.VS.InnerVlanLoopback2));
            break;
          case 3:
            resultBuffer.append(props.get(NETPortSPPropertiesFSP150CM.VS.InnerVlanLoopback3));
            break;
        }
      }
      bitField <<=1;
    }
    return resultBuffer.toString();
  }
}