/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */
package com.adva.nlms.mediation.mtosi.v1.mediation;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementDAO;
import com.adva.nlms.mediation.config.f3.NetworkElementF3;
import com.adva.nlms.mediation.config.f3_efm.NetworkElementF3_EFM;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CM_CP_EFM;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.NetworkElementFSP150CP;
import com.adva.nlms.mediation.config.hn4000.NetworkElementHN4000;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm.Fsp150cmEfmCPMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm.Fsp150cmEfmMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm.Fsp150cmMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cp.Fsp150cpEfmMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cp.Fsp150cpMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp20X.FspGE20XMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fspEgx.FspEgxMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fspGE11X.FspGE11XMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.hn4000.Hn4000EfmMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.hn4000.Hn4000Mediator;

public class NetworkElementMediatorFactory {

  public static NetworkElementMediator createNetworkElementMediator(NetworkElement ne){

    switch (ne.getNetworkElementType()){
      //NetworkElementFSP150CMImpl
      //NetworkElementFSP150CM_CP_EFMImpl
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
        if (ne.isPeer() || NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM == ne.getNetworkElementType()){
          return new Fsp150cmEfmMediator((NetworkElementF3_EFM) ne);
        }
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
        if (ne.isPeer() || NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR == ne.getNetworkElementType()){
          return new Fsp150cmEfmCPMediator((NetworkElementFSP150CM_CP_EFM) ne);
        }
        return new Fsp150cmMediator((NetworkElementF3)ne);
      //NetworkElementFSP150CPImpl
      //NetworkElementFSP150CP_EFMImpl
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
        if (ne.isPeer()){
          return new Fsp150cpEfmMediator( (NetworkElementFSP150CP) ne);
        }
        return new Fsp150cpMediator( (NetworkElementFSP150CP) ne);
      //NetworkElementFSP150EGXImpl
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150EGX:
//      case  NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
        return new FspEgxMediator( (NetworkElementF3) ne);
      //NetworkElementFSPGE1XXImpl
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE112:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE104:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114G:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114H:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114PH:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114S:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114SH:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE112PRO:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE112PRO_M:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE112PRO_H:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GO102PRO_S:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GO102PRO_SM:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GO102PRO_SP:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE102PRO_H:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114PRO:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114PRO_C:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114PRO_SH:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114PRO_CSH:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE114PRO_HE:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE101PRO:

        return new FspGE11XMediator( (NetworkElementF3) ne);
      //NetworkElementFSPGE20XImpl,
      //NetworkElementFSPXG210Impl
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206F:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206V:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG210:
      case  NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG210C:
        return new FspGE20XMediator( (NetworkElementF3) ne);
      //NetworkElementHN4000Impl
      //NetworkElementHN4000_EFMImpl
      case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
        if(ne.isPeer()){
          return  new Hn4000EfmMediator((NetworkElementHN4000) ne, new NetworkElementDAO());
        }
        return new Hn4000Mediator((NetworkElementHN4000) ne, new NetworkElementDAO());
    }
    return null;
  }
}
