/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.serviceProvisioning.HnTrafficGenProperties;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.hn4000.NetworkElementHN4000;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiFDFrMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.StartContinuityTestT;

import jakarta.xml.ws.Holder;

public class StartContinuityTestWorkerHN400 extends StartContinuityTestWorkerHN {

  public StartContinuityTestWorkerHN400(StartContinuityTestT mtosiBody, Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne) {
    super(mtosiBody, mtosiHeader, namingAttributes, ne);
  }

  @Override
  protected void mediate() throws Exception {
	  super.mediate();
		if (!testType.equals("Bandwidth"))
	     throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
	              testType + " is not a valid test type for the specified device.");

//	  if (! (port instanceof PortHN4000Ethernet2BASE_TL)) {
//	      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
//          "The port must be an FTP.");
//	  }
	  HnTrafficGenProperties properties = MtosiFDFrMediator.mtosiContinuityTestParametersToTrafficGenProperties(testParameters);
	  properties.set(HnTrafficGenProperties.VI.DeviceId, ((NetworkElementHN4000)port.getNE()).getNeIndex());
	  transact(properties);
  }

  private void transact( HnTrafficGenProperties properties)
          throws ObjectInUseException, NetTransactionException, SNMPCommFailure, SPValidationException {

    NetworkElement locks[] = new NetworkElement[]{ne};
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, getClass().getSimpleName());
    try{
   	  flow.startTrafficGenTest(properties);
      NetTransactionManager.commitNetTransaction(id);
    } catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally{
      NetTransactionManager.ensureEnd(id);
    }
  }
}
