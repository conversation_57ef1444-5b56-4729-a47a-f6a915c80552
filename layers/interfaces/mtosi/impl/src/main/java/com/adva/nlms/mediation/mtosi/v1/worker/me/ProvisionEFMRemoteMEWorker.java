/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */
package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.common.snmp.MIBFSP150CP;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.DTOBuilder;
import com.adva.nlms.mediation.config.dto.attr.PeerNetworkElementAttr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.common.helpers.MtosiLinkCreator;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.ManagedElementMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiProcessingFailureException;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.mtosi.v2.utils.translations.BooleanEnum;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Node;
import v1.tmf854.HeaderT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.LayeredParametersT;
import v1.tmf854.ManagedElementT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.RemoteMEVendorExtensionsT;
import v1.tmf854ext.adva.ProvisionEFMRemoteMECreateDataT;
import v1.tmf854ext.adva.ProvisionEFMRemoteMEResponseT;
import v1.tmf854ext.adva.ProvisionEFMRemoteMET;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;
import jakarta.xml.ws.Holder;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Stream;

public class ProvisionEFMRemoteMEWorker extends AbstractMtosiWorker {
  private static final Logger log = LogManager.getLogger(ProvisionEFMRemoteMEWorker.class);

  private ProvisionEFMRemoteMET mtosiBody;
  private ProvisionEFMRemoteMECreateDataT createData;
  private NamingAttributesT tpName;
  private ProvisionEFMRemoteMEResponseT response = new ProvisionEFMRemoteMEResponseT();
  private NetworkElement ne;
  private JAXBElement<RemoteMEVendorExtensionsT> vendorExtensions;
  private MtosiMOFacade facade;
  private DTO<PeerNetworkElementAttr> peerNetworkElementDTO;
  private ManagedElementT managedElement;
  private String ptpName;
  public static final String pattern = "/(shelf)=(\\d+)/(slot)=(\\d+)/port=NET-1";

  public ProvisionEFMRemoteMEWorker(ProvisionEFMRemoteMET mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "provisionEFMRemoteME", "provisionEFMRemoteME", "provisionEFMRemoteMEResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    validator.validate(ne.getDefaultNetworkElementTypeString());
  }

  @Override
  protected void parse() throws Exception {
    createData = mtosiBody.getCreateData();
    if (createData == null) {
      throw ExceptionUtils.createNewMPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MD_NAME_MISSING);
    }
    tpName = createData.getTpName();
    if (tpName == null) {
      throw ExceptionUtils.createNewMPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MD_NAME_MISSING);
    }

    this.ne = this.getMtosiCtrl().getLegacyMtosiMOFacade().getNEByName(tpName.getMeNm());

    if (tpName.getMdNm() == null) {
      throw ExceptionUtils.createNewMPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!tpName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ExceptionUtils.createNewMPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MD_NOT_FOUND);
    }

    ptpName = tpName.getPtpNm();
    if (!ptpName.matches(pattern)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          "The specified ptpNm is invalid.");
    }

    MtosiUtils.existsMDOrMEEntity(tpName.getMdNm(), tpName.getMeNm(), ne);
    vendorExtensions = createData.getVendorExtensions();
    if(vendorExtensions == null || vendorExtensions.getValue()==null){
      throw ExceptionUtils.createNewMPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          "VendorExtensions Element has not been specified.");
    }
    facade = getMtosiCtrl().getMoFacadeManager().getFacadeViaNeId(MtosiMOFacade.class,ne.getID());
  }

  @Override
  protected void mediate() throws Exception {
    peerNetworkElementDTO = DTOBuilder.getInstance().newDTO(PeerNetworkElementAttr.class);
    String ptpName = tpName.getPtpNm();

    int slotIndex = getIndexForGivenField(ptpName, "slot");
    int portIndex = getIndexForGivenField(ptpName, "port");

    peerNetworkElementDTO.putOrReplace(PeerNetworkElementAttr.PEER_NE_INDEX, slotIndex + 101);
    peerNetworkElementDTO.putOrReplace(PeerNetworkElementAttr.IS_FROM_PORT_ACC, false);
    peerNetworkElementDTO.putOrReplace(PeerNetworkElementAttr.SLOT_INDEX, slotIndex + 1);
    peerNetworkElementDTO.putOrReplace(PeerNetworkElementAttr.FROM_PORT_INDEX, portIndex);

    List<Object> list = vendorExtensions.getValue().getAny();
    Unmarshaller unmarshaller = null;
    try {
      unmarshaller = JAXBContext.newInstance("v1.tmf854").createUnmarshaller();
    }catch (JAXBException ex)    {
      throw ExceptionUtils.createNewMPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.JAXB_MARSHALLING_PROBLEM);
    }

    for (Iterator iter = list.iterator(); iter.hasNext();) {
      Node element = (Node) iter.next();
      LayeredParametersListT params;
      try {
        JAXBElement<LayeredParametersListT> root = unmarshaller.unmarshal(element, LayeredParametersListT.class);
        params = root.getValue();
        for (LayeredParametersT layer : params.getLayeredParameters()) {
          validateLayer(layer);
        }

        String cpeHostName = LayeredParameterUtils.getLayeredParameter(params, LayeredParams.PROP_ADVA_REMOTECPE,
            LayeredParams.PropAdvaRemoteCPE.CPE_HOST_NAME);
        if (cpeHostName != null) {
          peerNetworkElementDTO.putOrReplace(PeerNetworkElementAttr.SYS_NAME, cpeHostName);
        } else {
          throw ExceptionUtils.createNewMPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.CPE_HOST_NAME_MISSING);
        }
        String remoteType = LayeredParameterUtils.getLayeredParameter(params, LayeredParams.PROP_ADVA_REMOTECPE,
            LayeredParams.PropAdvaRemoteCPE.REMOTE_TYPE);
        if (remoteType != null && (MIBFSP150CP.MODEL_NAME_FSP_150_GE102Pro_EFMH.equals(remoteType) || MIBFSP150CP.MODEL_NAME_FSP_150CPMR.equals(remoteType))) {
          int peerType = MIBFSP150CM.Entity.NetworkElementTable.TYPE_CPMR;
          if (MIBFSP150CP.MODEL_NAME_FSP_150_GE102Pro_EFMH.equals(remoteType))
            peerType = MIBFSP150CM.Entity.NetworkElementTable.TYPE_GE102PROEFMH;

          peerNetworkElementDTO.putOrReplace(PeerNetworkElementAttr.PEER_TYPE, peerType);
        } else {
          throw ExceptionUtils.createNewMPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.EFM_REMOTE_TYPE_NOT_VALID);
        }
        String createLink = LayeredParameterUtils.getLayeredParameter(params, LayeredParams.PROP_ADVA_REMOTECPE,
            LayeredParams.PropAdvaRemoteCPE.CREATE_LINK);
        if (createLink != null) {
          if (BooleanEnum.valueOfString(createLink) != BooleanEnum.UNKNOWN && BooleanEnum.valueOfString(createLink) != BooleanEnum.NOT_APPLICABLE) {
            peerNetworkElementDTO.putOrReplace(PeerNetworkElementAttr.CREATE_LINK, Boolean.parseBoolean(createLink));
          } else {
            peerNetworkElementDTO.putOrReplace(PeerNetworkElementAttr.CREATE_LINK, BooleanEnum.FALSE.getBooleanValue());
          }
        }

        if (BooleanEnum.TRUE.getLabel().equals(createLink)) {
          String linkName = LayeredParameterUtils.getLayeredParameter(params, LayeredParams.PROP_ADVA_REMOTECPE,
              LayeredParams.PropAdvaRemoteCPE.LINK_NAME);
          if (linkName != null) {
            peerNetworkElementDTO.putOrReplace(PeerNetworkElementAttr.LINK_NAME, linkName);
          }
        }
      } catch (JAXBException ex) {
        // not right element so skip it
      }
    }

    transact();
    managedElement = ManagedElementMediator.nmsNeToManagedElementT(getMtosiCtrl().getLegacyMtosiMOFacade().getNEByName(peerNetworkElementDTO.getValue(PeerNetworkElementAttr.SYS_NAME)));

    new MtosiLinkCreator(peerNetworkElementDTO, ne, ptpName).createLink();
  }

  private void transact() throws Exception {
    try {
      facade.openNetTransaction(ne.getID());
      facade.createPeerOnDevice(ne.getID(), peerNetworkElementDTO);
      facade.getNetworkElementDTO(ne.getID());
      logSecurity(ne, SystemAction.AddNetwork, tpName.getPtpNm());
    }catch (SNMPCommFailure ex) {
      log.error("Exception during transaction: " + ex);
      throw ex;
    }  finally {
      facade.closeNetTransaction(ne.getID());
    }
  }

  @Override
  protected void response()  {
    response.setTheManagedElement(managedElement);
  }

  @Override
  public ProvisionEFMRemoteMEResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }

  private int getIndexForGivenField(String ptpName, String field) {
    String[] parts = ptpName.split("/");
    String index = "0";
    for (String part : parts){
      if (part.startsWith(field)){
        if (part.contains("-"))
          index = part.split("-")[1];
        else
          index = part.split("=")[1];
      }
    }
    return Integer.parseInt(index);
  }

  private static void validateLayer(LayeredParametersT layer) throws MtosiProcessingFailureException {

    boolean isPresent = Stream.of(validLayers).anyMatch(f -> f.equals(layer.getLayer()));
    if(!isPresent){
      throw ExceptionUtils.createNewMPFE(ExceptionUtils.EXCPT_INVALID_INPUT, layer.getLayer() + " layer rate contains an illegal value");
    }

  }

  private static final String[] validLayers = new String[]{
      LayeredParams.PROP_ADVA_REMOTECPE
  };

}
