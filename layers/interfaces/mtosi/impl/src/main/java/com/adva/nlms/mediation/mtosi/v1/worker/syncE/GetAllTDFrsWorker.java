/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.syncE;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.common.NEUtils;
import v1.tmf854.HeaderT;
import v1.tmf854.LayerRateListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.GetAllTDFrsResponseT;
import v1.tmf854ext.adva.GetAllTDFrsT;
import v1.tmf854ext.adva.TDFrListT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

/**
 * Worker class for the MTOSI operation getAllTDFrs
 */
public class GetAllTDFrsWorker extends AbstractMtosiWorker {
  protected GetAllTDFrsT mtosiBody;
  protected GetAllTDFrsResponseT response = new GetAllTDFrsResponseT();
  protected NamingAttributesT meName;
  protected MtosiAddress mtosiAddr;
  protected TDFrListT tdfrList;

  //Constructor
  public GetAllTDFrsWorker(final GetAllTDFrsT mtosiBody, final Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getAllTDFrs", "getAllTDFrs", "getAllTDFrsResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    meName = mtosiBody.getMeName();
    mtosiAddr = new MtosiAddress(meName);

    if (meName == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
        MtosiErrorConstants.INVALID_FILTER);
    }

    if (meName.getMdNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
        MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!meName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        MtosiErrorConstants.MD_NOT_FOUND);
    }

    MtosiUtils.validateNE(mtosiAddr.getNE());

  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(mtosiAddr.getNE().getDefaultNetworkElementTypeString());
  }

  @Override
  protected void mediate() throws Exception {
    // only allowed for FSP150CC-GE201SE && FSP150CM
    if (!NEUtils.isSyncEDevice(mtosiAddr.getNeType())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The specified ME does not support SyncE.");
    }

    final LayerRateListT layerRateList = mtosiBody.getConnectivityRateList();
    tdfrList = ManagedElementFactory.getAllTDFrs(mtosiAddr.getNE(), layerRateList);
  }

  @Override
  protected void response() throws Exception {
    response.setTDFrList(tdfrList);
  }

  @Override
  public GetAllTDFrsResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
