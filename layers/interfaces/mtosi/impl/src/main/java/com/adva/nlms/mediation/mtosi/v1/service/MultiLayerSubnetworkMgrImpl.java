/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;

import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.MultiLayerSubnetworkMgr;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.HeaderUtils;


/**
 * This class was generated by the Celtix 1.1-SNAPSHOT
 * Fri Dec 22 10:48:24 EST 2006
 * Generated source version: 1.1-SNAPSHOT
 * 
 */

@jakarta.jws.WebService(name = "MultiLayerSubnetworkMgr", serviceName = "ConfigurationService", portName = "MultiLayerSubnetworkMgrHttp", targetNamespace = "tmf854.v1.ws")

public class MultiLayerSubnetworkMgrImpl implements MultiLayerSubnetworkMgr {
    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllEdgePointNames(v1.tmf854.GetAllEdgePointNamesT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllEdgePointNames(
        v1.tmf854.GetAllEdgePointNamesT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllEdgePointNames", "getAllEdgePointNamesResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }

      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllEdgePointNames(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllEdgePointNamesT mtosiBody) throws ProcessingFailureException
    {
      return getAllEdgePointNames(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllEdgePointNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllEdgePointNamesIterator(
        v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllEdgePointNamesIterator", "getAllEdgePointNamesResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllEdgePointNamesIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException
    {
      return getAllEdgePointNamesIterator(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllEdgePoints(v1.tmf854.GetAllEdgePointsT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllEdgePointsResponseT getAllEdgePoints(
        v1.tmf854.GetAllEdgePointsT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllEdgePoints", "getAllEdgePointsResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllEdgePointsResponseT getAllEdgePoints(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllEdgePointsT mtosiBody) throws ProcessingFailureException
    {
    	return getAllEdgePoints(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllEdgePointsIterator(v1.tmf854.GetTpIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllEdgePointsResponseT getAllEdgePointsIterator(
        v1.tmf854.GetTpIteratorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllEdgePointsIterator", "getAllEdgePointsResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }

      @Override
      public v1.tmf854.GetAllEdgePointsResponseT getAllEdgePointsIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetTpIteratorT mtosiBody) throws ProcessingFailureException
    {
      return getAllEdgePointsIterator(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllManagedElementNamesWrtMLSN(v1.tmf854.GetAllManagedElementNamesWrtMLSNT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllManagedElementNamesWrtMLSN(
        v1.tmf854.GetAllManagedElementNamesWrtMLSNT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllManagedElementNamesWrtMLSN", "getAllManagedElementNamesWrtMLSNResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }

      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllManagedElementNamesWrtMLSN(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllManagedElementNamesWrtMLSNT mtosiBody) throws ProcessingFailureException
    {
      return getAllManagedElementNamesWrtMLSN(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllManagedElementNamesWrtMLSNIterator(v1.tmf854.GetAllObjectNamesIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllManagedElementNamesWrtMLSNIterator(
        v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
		HeaderUtils.formatResponseHeader(mtosiHeader, "getAllManagedElementNamesWrtMLSNIterator", "getAllManagedElementNamesWrtMLSNResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllManagedElementNamesWrtMLSNIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException
    {
      return getAllManagedElementNamesWrtMLSNIterator(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllManagedElementsWrtMLSN(v1.tmf854.GetAllManagedElementsWrtMLSNT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllManagedElementsWrtMLSNResponseT getAllManagedElementsWrtMLSN(
        v1.tmf854.GetAllManagedElementsWrtMLSNT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllManagedElementsWrtMLSN", "getAllManagedElementsWrtMLSNResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllManagedElementsWrtMLSNResponseT getAllManagedElementsWrtMLSN(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllManagedElementsWrtMLSNT mtosiBody) throws ProcessingFailureException
    {
    	return getAllManagedElementsWrtMLSN(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllManagedElementsWrtMLSNIterator(v1.tmf854.GetMeIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllManagedElementsWrtMLSNResponseT getAllManagedElementsWrtMLSNIterator(
        v1.tmf854.GetMeIteratorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllManagedElementsWrtMLSNIterator", "getAllManagedElementsWrtMLSNResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }

      @Override
      public v1.tmf854.GetAllManagedElementsWrtMLSNResponseT getAllManagedElementsWrtMLSNIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetMeIteratorT mtosiBody) throws ProcessingFailureException
    {
    	return getAllManagedElementsWrtMLSNIterator(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllSubnetworkConnectionNames(v1.tmf854.GetAllSubnetworkConnectionNamesT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllSubnetworkConnectionNames(
        v1.tmf854.GetAllSubnetworkConnectionNamesT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSubnetworkConnectionNames", "getAllSubnetworkConnectionNamesResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllSubnetworkConnectionNames(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllSubnetworkConnectionNamesT mtosiBody) throws ProcessingFailureException
    {
    	return getAllSubnetworkConnectionNames(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllSubnetworkConnectionNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllSubnetworkConnectionNamesIterator(
        v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSubnetworkConnectionNamesIterator", "getAllSubnetworkConnectionNamesResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }

      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllSubnetworkConnectionNamesIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException
    {
    	return getAllSubnetworkConnectionNamesIterator(mtosiBody, mtosiHeader);
    }



    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllSubnetworkConnectionNamesWithTP(v1.tmf854.GetAllSubnetworkConnectionNamesWithTPT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllSubnetworkConnectionNamesWithTP(
        v1.tmf854.GetAllSubnetworkConnectionNamesWithTPT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSubnetworkConnectionNamesWithTP", "getAllSubnetworkConnectionNamesWithTPResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllSubnetworkConnectionNamesWithTP(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllSubnetworkConnectionNamesWithTPT mtosiBody) throws ProcessingFailureException
    {
    	return getAllSubnetworkConnectionNamesWithTP(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllSubnetworkConnectionNamesWithTPIterator(v1.tmf854.GetAllObjectNamesIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllSubnetworkConnectionNamesWithTPIterator(
        v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSubnetworkConnectionNamesWithTPIterator", "getAllSubnetworkConnectionNamesWithTPResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllSubnetworkConnectionNamesWithTPIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException
    {
    	return getAllSubnetworkConnectionNamesWithTPIterator(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllSubnetworkConnections(v1.tmf854.GetAllSubnetworkConnectionsT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllSubnetworkConnectionsResponseT getAllSubnetworkConnections(
        v1.tmf854.GetAllSubnetworkConnectionsT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSubnetworkConnections", "getAllSubnetworkConnectionsResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllSubnetworkConnectionsResponseT getAllSubnetworkConnections(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllSubnetworkConnectionsT mtosiBody) throws ProcessingFailureException
    {
    	return getAllSubnetworkConnections(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllSubnetworkConnectionsIterator(v1.tmf854.GetSncIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllSubnetworkConnectionsResponseT getAllSubnetworkConnectionsIterator(
        v1.tmf854.GetSncIteratorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSubnetworkConnectionsIterator", "getAllSubnetworkConnectionsResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllSubnetworkConnectionsResponseT getAllSubnetworkConnectionsIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetSncIteratorT mtosiBody) throws ProcessingFailureException
    {
    	return getAllSubnetworkConnectionsIterator(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllSubnetworkConnectionsWithTP(v1.tmf854.GetAllSubnetworkConnectionsWithTPT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllSubnetworkConnectionsWithTPResponseT getAllSubnetworkConnectionsWithTP(
        v1.tmf854.GetAllSubnetworkConnectionsWithTPT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSubnetworkConnectionsWithTP", "getAllSubnetworkConnectionsWithTPResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllSubnetworkConnectionsWithTPResponseT getAllSubnetworkConnectionsWithTP(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllSubnetworkConnectionsWithTPT mtosiBody) throws ProcessingFailureException
    {
    	return getAllSubnetworkConnectionsWithTP(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllSubnetworkConnectionsWithTPIterator(v1.tmf854.GetSncIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllSubnetworkConnectionsWithTPResponseT getAllSubnetworkConnectionsWithTPIterator(
        v1.tmf854.GetSncIteratorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSubnetworkConnectionsWithTPIterator", "getAllSubnetworkConnectionsWithTPResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllSubnetworkConnectionsWithTPResponseT getAllSubnetworkConnectionsWithTPIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetSncIteratorT mtosiBody) throws ProcessingFailureException
    {
    	return getAllSubnetworkConnectionsWithTPIterator(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllTopologicalLinkNames(v1.tmf854.GetAllTopologicalLinkNamesT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllTopologicalLinkNames(
        v1.tmf854.GetAllTopologicalLinkNamesT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTopologicalLinkNames", "getAllTopologicalLinkNamesResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }

      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllTopologicalLinkNames(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllTopologicalLinkNamesT mtosiBody) throws ProcessingFailureException
    {
    	return getAllTopologicalLinkNames(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllTopologicalLinkNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllTopologicalLinkNamesIterator(
        v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTopologicalLinkNamesIterator", "getAllTopologicalLinkNamesResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }

      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllTopologicalLinkNamesIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException
    {
    	return getAllTopologicalLinkNamesIterator(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllTopologicalLinks(v1.tmf854.GetAllTopologicalLinksT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllTopologicalLinksResponseT getAllTopologicalLinks(
        v1.tmf854.GetAllTopologicalLinksT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTopologicalLinks", "getAllTopologicalLinksResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }

      @Override
      public v1.tmf854.GetAllTopologicalLinksResponseT getAllTopologicalLinks(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllTopologicalLinksT mtosiBody) throws ProcessingFailureException
    {
    	return getAllTopologicalLinks(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllTopologicalLinksIterator(v1.tmf854.GetTlIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllTopologicalLinksResponseT getAllTopologicalLinksIterator(
        v1.tmf854.GetTlIteratorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTopologicalLinksIterator", "getAllTopologicalLinksResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllTopologicalLinksResponseT getAllTopologicalLinksIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetTlIteratorT mtosiBody) throws ProcessingFailureException
    {
    	return getAllTopologicalLinksIterator(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllTPPoolNames(v1.tmf854.GetAllTPPoolNamesT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllTPPoolNames(
        v1.tmf854.GetAllTPPoolNamesT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTPPoolNames", "getAllTPPoolNamesResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllTPPoolNames(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllTPPoolNamesT mtosiBody) throws ProcessingFailureException
    {
    	return getAllTPPoolNames(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllTPPoolNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllTPPoolNamesIterator(
        v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTPPoolNamesIterator", "getAllTPPoolNamesResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


     @Override
     public v1.tmf854.GetAllObjectNamesResponseT getAllTPPoolNamesIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException
    {
    	return getAllTPPoolNamesIterator(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllTPPools(v1.tmf854.GetAllTPPoolsT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllTPPoolsResponseT getAllTPPools(
        v1.tmf854.GetAllTPPoolsT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTPPools", "getAllTPPoolsResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllTPPoolsResponseT getAllTPPools(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllTPPoolsT mtosiBody) throws ProcessingFailureException
    {
    	return getAllTPPools(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAllTPPoolsIterator(v1.tmf854.GetTpPoolIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllTPPoolsResponseT getAllTPPoolsIterator(
        v1.tmf854.GetTpPoolIteratorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTPPoolsIterator", "getAllTPPoolsResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }

      @Override
      public v1.tmf854.GetAllTPPoolsResponseT getAllTPPoolsIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetTpPoolIteratorT mtosiBody) throws ProcessingFailureException
    {
    	return getAllTPPoolsIterator(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getAssociatedTP(v1.tmf854.GetAssociatedTPT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAssociatedTPResponseT getAssociatedTP(
        v1.tmf854.GetAssociatedTPT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAssociatedTP", "getAssociatedTPResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAssociatedTPResponseT getAssociatedTP(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAssociatedTPT mtosiBody) throws ProcessingFailureException
    {
    	return getAssociatedTP(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getMultiLayerSubnetwork(v1.tmf854.GetMultiLayerSubnetworkT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetMultiLayerSubnetworkResponseT getMultiLayerSubnetwork(
        v1.tmf854.GetMultiLayerSubnetworkT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getMultiLayerSubnetwork", "getMultiLayerSubnetworkResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetMultiLayerSubnetworkResponseT getMultiLayerSubnetwork(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetMultiLayerSubnetworkT mtosiBody) throws ProcessingFailureException
    {
    	return getMultiLayerSubnetwork(mtosiBody, mtosiHeader);
    }



    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getRoute(v1.tmf854.GetRouteT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetRouteResponseT getRoute(
        v1.tmf854.GetRouteT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getRoute", "getRouteResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetRouteResponseT getRoute(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetRouteT mtosiBody) throws ProcessingFailureException
    {
    	return getRoute(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getRouteAndTopologicalLinks(v1.tmf854.GetRouteAndTopologicalLinksT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetRouteAndTopologicalLinksResponseT getRouteAndTopologicalLinks(
        v1.tmf854.GetRouteAndTopologicalLinksT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getRouteAndTopologicalLinks", "getRouteAndTopologicalLinksResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetRouteAndTopologicalLinksResponseT getRouteAndTopologicalLinks(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetRouteAndTopologicalLinksT mtosiBody) throws ProcessingFailureException
    {
    	return getRouteAndTopologicalLinks(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getSNC(v1.tmf854.GetSNCT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetSNCResponseT getSNC(
        v1.tmf854.GetSNCT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getSNC", "getSNCResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetSNCResponseT getSNC(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetSNCT mtosiBody) throws ProcessingFailureException
    {
    	return getSNC(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getSNCsByUserLabel(v1.tmf854.GetSNCsByUserLabelT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetSNCsByUserLabelResponseT getSNCsByUserLabel(
        v1.tmf854.GetSNCsByUserLabelT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getSNCsByUserLabel", "getSNCsByUserLabelResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }

      @Override
      public v1.tmf854.GetSNCsByUserLabelResponseT getSNCsByUserLabel(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetSNCsByUserLabelT mtosiBody) throws ProcessingFailureException
    {
    	return getSNCsByUserLabel(mtosiBody, mtosiHeader);
    }



    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getTopologicalLink(v1.tmf854.GetTopologicalLinkT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetTopologicalLinkResponseT getTopologicalLink(
        v1.tmf854.GetTopologicalLinkT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getTopologicalLink", "getTopologicalLinkResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


     @Override
     public v1.tmf854.GetTopologicalLinkResponseT getTopologicalLink(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetTopologicalLinkT mtosiBody) throws ProcessingFailureException
    {
    	return getTopologicalLink(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getTPGroupingRelationships(v1.tmf854.GetTPGroupingRelationshipsT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getTPGroupingRelationships(
        v1.tmf854.GetTPGroupingRelationshipsT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getTPGroupingRelationships", "getTPGroupingRelationshipsResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getTPGroupingRelationships(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetTPGroupingRelationshipsT mtosiBody) throws ProcessingFailureException
    {
    	return getTPGroupingRelationships(mtosiBody, mtosiHeader);
    }


    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getTPGroupingRelationshipsIterator(v1.tmf854.GetAllObjectNamesIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getTPGroupingRelationshipsIterator(
        v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getTPGroupingRelationshipsIterator", "getTPGroupingRelationshipsResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }



  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getTPGroupingRelationshipsIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException
    {
    	return getTPGroupingRelationshipsIterator(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.MultiLayerSubnetworkMgr#getTPPool(v1.tmf854.GetTPPoolT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetTPPoolResponseT getTPPool(
        v1.tmf854.GetTPPoolT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getTPPool", "getTPPoolResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetTPPoolResponseT getTPPool(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetTPPoolT mtosiBody) throws ProcessingFailureException
    {
    	return getTPPool(mtosiBody, mtosiHeader);
    }
}
