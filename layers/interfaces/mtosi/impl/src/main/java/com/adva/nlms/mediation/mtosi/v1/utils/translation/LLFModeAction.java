/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.utils.translation;


public enum  LLFModeAction implements TranslatableEnum {
  // 1=llfmode-none 2=llfmode-acc2acc 3=llfmode-net2acc 4=llfmode-both
  LLF_MODE_NODE       (1, 2, 2),
  LLF_MODE_ACC_TO_ACC (2, 1, 2),
  LLF_MODE_NET_TO_ACC (3, 2, 1),
  LLF_MODE_MODE       (4, 1, 1);



  private final int    mibValue;
  private final int accToAccValue;
  private final int  netToAccValue;

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return "";
  }

  public int getAccToAccValue() {
    return accToAccValue;
  }

  public int getNetToAccValue() {
    return netToAccValue;
  }

  private LLFModeAction(final int mibValue, final int accToAccValue, int netToAccValue)
  {
    this.mibValue=mibValue;
    this.accToAccValue=accToAccValue;
    this.netToAccValue=netToAccValue;
  }

  public static int getLLFModeActionValue(final int acc, int net)
  {
    LLFModeAction enumType = LLF_MODE_NODE;  // the return value

    for (LLFModeAction tmpEnumType : values())
    {
      if (acc == tmpEnumType.getAccToAccValue() && net == tmpEnumType.getNetToAccValue())
      {
        enumType = tmpEnumType;
        break;
      }
    }
    return enumType.getMIBValue();

  }

  public static int getACCtoACCValue(final int llfModeActionValue)
  {
    LLFModeAction enumType = LLF_MODE_NODE;  // the return value

    for (LLFModeAction tmpEnumType : values())
    {
      if (llfModeActionValue == tmpEnumType.getMIBValue())
      {
        enumType = tmpEnumType;
        break;
      }
    }
    return enumType.getAccToAccValue();

  }

  public static int getNETtoACCValue(final int llfModeActionValue)
  {
    LLFModeAction enumType = LLF_MODE_NODE;  // the return value

    for (LLFModeAction tmpEnumType : values())
    {
      if (llfModeActionValue == tmpEnumType.getMIBValue())
      {
        enumType = tmpEnumType;
        break;
      }
    }
    return enumType.getNetToAccValue();

  }


  }
