/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

/**
 * Created by IntelliJ IDEA. User: Lukasz Date: 2007-05-30 Time: 13:14:09 To change this template use File | Settings |
 * File Templates.
 */
public enum ProtectionTranslation{
	AUTO           (1, "Auto"),
  L2WITHOUT_ST   (2, "L2WithoutST"),
  L2WITH_ST      (3, "L2WithST"),
  L3             (4, "L3"),
  POINT_TO_POINT (5, "PointToPoint"),
  FORCE_A        (6, "ForceA"),
  FORCE_B        (7, "ForceB"),
  DETECTING      (8, "Detecting"),
  LINK_AGGREGATE (9, "LinkAggregate"),
  NONE           (10, "None"),
	NOT_APPLICABLE (0, "n/a");

  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private ProtectionTranslation (final int mibValue, final String mtosiString)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int mibValue)
  {
    ProtectionTranslation anEnum = NOT_APPLICABLE;  // the return value

    for (ProtectionTranslation tmpEnum : values())
    {
      if (mibValue == tmpEnum.getMIBValue())
      {
        anEnum = tmpEnum;
        break;
      }
    }
    return anEnum.getMtosiString();
  }
  /**
   * Returns the string representation used in MTOSI layer.
   * @param mtosiString  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static int getMIBValue (final String mtosiString)
  {
    ProtectionTranslation anEnum = NOT_APPLICABLE;  // the return value

    for (ProtectionTranslation tmpEnum : values())
    {
      if (mtosiString.equals(tmpEnum.getMtosiString()))
      {
        anEnum = tmpEnum;
        break;
      }
    }
    return anEnum.getMIBValue();

  }
}
