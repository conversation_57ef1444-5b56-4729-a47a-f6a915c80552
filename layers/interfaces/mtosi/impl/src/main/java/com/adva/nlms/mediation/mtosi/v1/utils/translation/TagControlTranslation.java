/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

/**
 * Created by IntelliJ IDEA. User: Lukasz Date: 2007-05-30 Time: 13:14:09 To change this template use File | Settings |
 * File Templates.
 */
public enum TagControlTranslation{
	NONE           (0, "None"),
  PUSH           (1, "Push"),
  SWAPVID        (2, "SwapVID"),
  SWAPTAG        (3, "SwapTag"),
  PUSHVID        (4, "PushVID"),
  NOT_APPLICABLE (5, "n/a");

  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private TagControlTranslation (final int mibValue, final String mtosiString)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int mibValue)
  {
    TagControlTranslation enumType = NOT_APPLICABLE;  // the return value

    for (TagControlTranslation tmpEnumType : values())
    {
      if (mibValue == tmpEnumType.getMIBValue())
      {
        enumType = tmpEnumType;
        break;
      }
    }
    return enumType.getMtosiString();
  }
  /**
   * Returns the string representation used in MTOSI layer.
   * @param mtosiString  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static int getMIBValue (final String mtosiString)
  {
    TagControlTranslation enumType = NOT_APPLICABLE;  // the return value

    for (TagControlTranslation tmpEnumType : values())
    {
      if (mtosiString.equals(tmpEnumType.getMtosiString()))
      {
        enumType = tmpEnumType;
        break;
      }
    }
    return enumType.getMIBValue();

  }
}
