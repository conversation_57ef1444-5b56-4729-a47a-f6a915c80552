/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.factory;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.BaseDiscoveredCapabilityT;
import v1.tmf854.CapabilitiesListT;
import v1.tmf854.ObjectFactory;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * Created by IntelliJ IDEA.
 * User: gfilip
 * Date: 2007-01-12
 * Time: 08:48:20
 * To change this template use File | Settings | File Templates.
 */
public class CapabilitiesFactory {
  Logger log = LogManager.getLogger(this.getClass().getPackage().getName());

  /* singleton */
  private static CapabilitiesFactory instance;

  public static CapabilitiesFactory getInstance(){
    if (instance==null){
      instance = new CapabilitiesFactory();
    }

    return instance;
  }

  private CapabilitiesListT capabilitiesList;
  private ObjectFactory objFactory;

  public CapabilitiesFactory(){
    objFactory = new ObjectFactory();
    capabilitiesList = objFactory.createCapabilitiesListT();
    initSupportedCapabilitiesLists();
  }

  public static CapabilitiesListT getCapabilitiesListT(){
//    CapabilitiesListT.ConfigurationService cs = new CapabilitiesListT.ConfigurationService();
//    CapabilitiesListT.ConfigurationService.ManagedElementMgr csManagedElementMgr = new CapabilitiesListT.ConfigurationService.ManagedElementMgr();
//
//    BaseDiscoveredCapabilityT getAllManagedElement = objFactory.createBaseDiscoveredCapabilityT();
//    getAllManagedElement.setTmf854Version(MtosiConstants.VERSION);
//
//    csManagedElementMgr.setGetAllManagedElements(getAllManagedElement);
//
//    cs.setManagedElementMgr(csManagedElementMgr);
//
//    capabilitiesList.setConfigurationService(cs);

    return getInstance().capabilitiesList;
  }

  /**
   * sets operation from selected port and service as supported
   * @param service
   * @param port
   * @param operation
   * */
    private void setSupported(String service,String port,String operation){
//    "discoveryService", "discoveryService","getAllCapabilities"){

    try {
      Object o = capabilitiesList;
      /* get or create service */
      Field field = o.getClass().getDeclaredField(service);
      o = createFieldInstance(o,field);

      /* get or create port */
      field = o.getClass().getDeclaredField(port);
      o = createFieldInstance(o,field);

      /* get or create operation */
      field = o.getClass().getDeclaredField(operation);
      Object o1 = getFieldValue(o,field);
      if(o1==null){
        o1 = createBaseDiscoveredCapabilityT();
        setFieldValue(o,field,o1);
      }

//      o = createFieldInstance(o,field);

    } catch (Exception e){
      log.error(e);
    }
  }

  /**
   * gets instance of object assiciated with selected field in specified class instance. If object is null
   * this function creates it.
   * @param instance - object for which we want to instatiate field value
   * @param field - selected field of object instance class
   * @return instance of object assiciated with selected field
   * @throws IllegalArgumentException - if object instance class doesn't contain setter for selected field
   * */
  private Object createFieldInstance(Object instance,Field field) throws IllegalArgumentException, IllegalAccessException, InvocationTargetException, InstantiationException {
    Object o = getFieldValue(instance,field);
    if(o==null){
      o = instantiateField(instance,field);
    }

    return o;
  }

  /**
   * get value of selected field in selected class
   * @param instance - instance of class for which we check field value
   * @param field - selected field of object instance class
   * @return value of selected field in selected class
   * @throws IllegalArgumentException - if object instance class doesn't contain no parameter getter for selected field
   * */
  private Object getFieldValue(Object instance,Field field) throws IllegalArgumentException, IllegalAccessException, InvocationTargetException {
    if(log.isDebugEnabled()) log.debug("getFieldValue\t"+field.getName());
    Method[] methods = instance.getClass().getMethods();
    for(int i=0;i<methods.length;i++){
      if(log.isDebugEnabled()) log.debug("method:\t"+methods[i]);
      if(methods[i].getName().toLowerCase().equals("get"+field.getName().toLowerCase())){

        Class[] parameters = methods[i].getParameterTypes();
        if(parameters.length==0){
          try {
            return methods[i].invoke(instance);
          } catch (IllegalAccessException e) {
            log.error(e);
            throw e;
          } catch (InvocationTargetException e) {
            log.error(e);
            throw e;
          }
        } else {
          throw new IllegalArgumentException("Getter should have no arguments");
        }
      }
    }

    return null;
  }

  /**
   * sets value of selected field in selected class
   * @param instance - instance of class for which we set field value
   * @param field - selected field of the class
   * @param fieldValue - field value
   * @throws IllegalArgumentException - if object instance class doesn't contain no parameter getter for selected field
   * */
  private void setFieldValue(Object instance,Field field,Object fieldValue) throws IllegalArgumentException, InvocationTargetException, IllegalAccessException {
    if(log.isDebugEnabled()) log.debug("setFieldValue\t"+field.getName());
    if(field.getType().equals(fieldValue.getClass())){
      Method[] methods = instance.getClass().getMethods();
      for(int i=0;i<methods.length;i++){
        if(log.isDebugEnabled()) log.debug("method:\t"+methods[i]);
        if(methods[i].getName().toLowerCase().equals("set"+field.getName().toLowerCase())){

          Class[] parameters = methods[i].getParameterTypes();
          if(parameters.length==1){
            if(parameters[0].equals(fieldValue.getClass())){
              try {
                methods[i].invoke(instance,fieldValue);

                return;// o;
              } catch (IllegalAccessException e) {
                log.error(e);
                throw e;
              } catch (InvocationTargetException e) {
                log.error(e);
                throw e;
              }
            }
          } else {
            throw new IllegalArgumentException("Setter should have one argument");
          }
        }
      }
    }
  }


  /**
   * instantiates specified field of object instance
   * @param instance - object for which we want to instatiate field value
   * @param field - selected field of object instance class
   * @return instance of field class
   * @throws IllegalArgumentException - if object instance class doesn't contain setter for selected field
   * */
  private Object instantiateField(Object instance,Field field) throws IllegalArgumentException, IllegalAccessException, InstantiationException, InvocationTargetException {
      Object myInst = field.getType().newInstance();
      setFieldValue(instance,field,myInst);

      return myInst;
  }

  /**
   * creates BaseDiscoveredCapabilityT for operation
   * */
  private BaseDiscoveredCapabilityT createBaseDiscoveredCapabilityT(){
    BaseDiscoveredCapabilityT obj = objFactory.createBaseDiscoveredCapabilityT();
    obj.setTmf854Version(MtosiConstants.VERSION);

    return obj;
  }

  /**
   * initializes list of supported operations
   * */
  private void initSupportedCapabilitiesLists()
  {
	  /* DiscoveryService */
	  
	  /* ConfigurationService - EquipmentInventoryMgr */
	  setSupported("configurationService", "equipmentInventoryMgr", "getAllEquipment");
	  setSupported("configurationService", "equipmentInventoryMgr", "getContainedEquipment");
	  setSupported("configurationService", "equipmentInventoryMgr", "getEquipment");

	  /* ConfigurationService - InventoryRetrieval */
	  setSupported("configurationService", "inventoryRetrieval", "getInventory");

	  /* ConfigurationService - ManagedElementMgr */
	  setSupported("configurationService", "managedElementMgr", "getAllManagedElements");
	  setSupported("configurationService", "managedElementMgr", "getManagedElement");
	  setSupported("configurationService", "managedElementMgr", "getAllPTPs");
	  setSupported("configurationService", "managedElementMgr", "getContainedCurrentCTPs");
	  setSupported("configurationService", "managedElementMgr", "getTP");
	  	  

	  /* ConfigurationService - MultiLayerSubnetworkMgr */
	  
	  /* ConfigurationService - OperationsSystemMgr */
	  setSupported("configurationService", "operationsSystemMgr", "getAllMDs");
	  setSupported("configurationService", "operationsSystemMgr", "getAllMEsPassingFilter");
	  setSupported("configurationService", "operationsSystemMgr", "getAllMEsWrtOS");
	  setSupported("configurationService", "operationsSystemMgr", "getAllOSs");
	  setSupported("configurationService", "operationsSystemMgr", "getMD");
	  setSupported("configurationService", "operationsSystemMgr", "getOS");
	  setSupported("configurationService", "operationsSystemMgr", "getSubordinateOS");

	  /* ConfigurationService - ProtectionMgr */
	  
	  /* ConfigurationService - TransmissionDescriptorMgr */
	  
	  /* FaultService - AlarmRetrieval */
	  
	  /* NotificationService - NotificationBroker */
	  
	  /* NotificationService - NotificationConsumer */
	  
	  /* NotificationService - NotificationProducer */

  }
}
