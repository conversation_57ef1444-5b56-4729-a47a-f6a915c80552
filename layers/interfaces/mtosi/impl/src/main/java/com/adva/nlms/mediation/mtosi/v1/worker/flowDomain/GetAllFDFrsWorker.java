/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import jakarta.xml.ws.Holder;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.FDFrListT;
import v1.tmf854ext.adva.GetAllFDFrsResponseT;
import v1.tmf854ext.adva.GetAllFDFrsT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;

public class GetAllFDFrsWorker extends AbstractMtosiWorker
{
	protected GetAllFDFrsT mtosiBody;
	protected GetAllFDFrsResponseT response = new GetAllFDFrsResponseT();
  protected NamingAttributesT meName;
  protected FDFrListT fdfrListT;
  private NetworkElement ne;
  

  //Constructor
	public GetAllFDFrsWorker (final GetAllFDFrsT mtosiBody, final Holder<HeaderT> mtosiHeader)
	{
		super(mtosiHeader, "getAllFDFrs", "getAllFDFrs", "getAllFDFrsResponse");
		this.mtosiBody = mtosiBody;
	}

  @Override
  protected void parse() throws Exception {
    if ((meName = mtosiBody.getMeName()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              MtosiErrorConstants.INVALID_FILTER);
    }

    if (!NamingTranslationFactory.isManagementDomain(meName)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!meName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MD_NOT_FOUND);
    }
    
    ne = ManagedElementFactory.getAndValidateNE(meName);
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	//Assumption: parse() is called before validate()
  	validator.validate(ne.getDefaultNetworkElementTypeString());
  }

  @Override
  protected void mediate() throws Exception
  {
    fdfrListT = ManagedElementFactory.getAllFDFrs(meName, ne);
  }

  @Override
  protected void response() throws Exception {
    response.setFdfrList(fdfrListT);
  }

	@Override
  public GetAllFDFrsResponseT getSuccessResponse()
	{
		if (response == null)
			return null;

		response.setTmf854Version(MtosiConstants.VERSION);
		return response;
	}
}
