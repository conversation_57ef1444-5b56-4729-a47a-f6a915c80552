/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.common.util.BitFields;

import java.io.UnsupportedEncodingException;

public enum CMSecondaryStateTranslation {
  ACTIVE                    (0, "Active"),
  AUTOMATIC_IN_SERVICE      (1, "AutomaticInService"),
  FACILITY_FAILURE          (2, "FacilityFailure"),
  FAULT                     (3, "Fault"),
  LOOPBACK                  (4, "Loopback"),
  MAINTENANCE               (5, "Maintenance"),
  MISMATCHED_EQPT           (6, "MismatchedEquipment"),
  STANDBY_HOT               (7, "StandbyHot"),
  SUPPORTING_ENTITY_OUTAGE  (8, "SupportingEntityOutage"),
  UNASSIGNED                (9, "Unassigned"),
  UNEQUIPPED                (10, "Unequipped"),
  DISABLED                  (11, "Disabled");

  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private CMSecondaryStateTranslation (final int mibValue, final String mtosiString)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param bitField  The bitfield value.
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final String bitField) {
    try {
      return getMtosiString(BitFields.bytesToInt(bitField.getBytes("UTF-8")));
    } catch (UnsupportedEncodingException e) {
      throw new Error("Unsupported encoding: UTF-8",e);
    }
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param bitFieldValue  The bitfield value.
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int bitFieldValue) {
    StringBuilder toReturn = new StringBuilder();
    boolean first = true;

    for (CMSecondaryStateTranslation tmpTranslation : values())
    {
      if ((bitFieldValue & (1 << tmpTranslation.getMIBValue())) != 0)
      {
        if (!first) {
          toReturn.append(", ");
        }
        toReturn.append(tmpTranslation.getMtosiString());
        first = false;
      }
    }
    return toReturn.toString();
  }

  /**
   * Returns if the bitField contains the specified bit
   * @param bitField the bitfield
   * @param bitNumber the specified bit
   * @return if the bitfield contains the specified bit
   */
  public static boolean isEnabled(final String bitField, CMSecondaryStateTranslation bitNumber) {
    final int bitFieldValue;
    try {
      bitFieldValue = BitFields.bytesToInt(bitField.getBytes("UTF-8"));
    } catch (UnsupportedEncodingException e) {
      throw new Error("Unsupported encoding: UTF-8",e);
    }
    return (bitFieldValue &  (1 << bitNumber.getMIBValue())) != 0;
  }
}
