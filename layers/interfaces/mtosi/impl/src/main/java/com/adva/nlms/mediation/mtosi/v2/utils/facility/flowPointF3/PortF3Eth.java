/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */
package com.adva.nlms.mediation.mtosi.v2.utils.facility.flowPointF3;


import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.mediation.config.dto.AttributesGroup;
import com.adva.nlms.mediation.config.dto.ConvertibleAttribute;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.AbstractEthernetTrafficPortAttr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;

import java.util.ArrayList;
import java.util.List;

public interface PortF3Eth<T extends AbstractEthernetTrafficPortAttr, Y extends ManagedObjectAttr> {

  default <GT extends AttributesGroup> boolean contains(ConvertibleAttribute<? super GT, ?, ?> attribute){
    return false;
  }

  String getMtosiName();
  void setMtosiName(String value);

  EntityIndex getEnityIndex();
  void setEnityIndex(EntityIndex value);

  boolean isEpl();
  int getAdministratorControl();
  void setAdministratorControl(int value);
  int getMaximumFrameSize();
  void setMaximumFrameSize(int value);
  int getAfpType();
  void setAfpType(int value);
  int getLinkLossFwdEnabled();
  void setLinkLossFwdEnabled(int value);
  int getLinkLossFwdDelay();
  void setLinkLossFwdDelay(int value);
  int getLinkLossFwdTxActionType();
  void setLinkLossFwdTxActionType(int value);
  int getLocalLinkId();
  void setLocalLinkId(int value);
  int getRemoteLinkIds();
  void setRemoteLinkIds(int value);
  String getRemoteLinkIdsString();
  void setRemoteLinkIdsString(String value);
  int getLinkLossFwdTriggerTypes();
  void setLinkLossFwdTriggerTypes(int value);
  int getLinkLossFwdActive();
  int getLinkLossPartnerEnabled();
  boolean getN2AVlanTrunkingEnabled();
  void setN2AVlanTrunkingEnabled(boolean value);
  boolean getA2NPushPortVIDEnabled();
  void setA2NPushPortVIDEnabled(boolean value);
  boolean getN2APopPortVIDEnabled();
  void setN2APopPortVIDEnabled(boolean value);
  int getLoopbackStatus();
  int getLoopbackTime();
  int getLoopbackSwapSada();
  int getOperState();
  int getSecondaryState();
  boolean isPortModeSet();
  int getTrafficModel();
  DTO<T> refresh(MtosiMOFacade facade);

  void getForResponse(MtosiMOFacade facade, boolean refreshed);

  default List<DTO<Y>> getExtensionDtos(){
    return new ArrayList<>();
  }

  default void addExtensionDto(List<DTO<Y>> extensionDto){}

  int getSvcType();

}
