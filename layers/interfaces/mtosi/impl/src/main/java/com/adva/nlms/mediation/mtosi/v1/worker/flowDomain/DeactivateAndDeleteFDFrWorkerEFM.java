/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.common.config.EntityClass;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.DTOBuilder;
import com.adva.nlms.mediation.config.dto.attr.FlowF3Attr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3AccAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3NetAttr;
import com.adva.nlms.mediation.config.dto.attr.ProtectionGroupF3Attr;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.adapter.facade.MtosiTerminationPointEFMDTOImpl;
import com.adva.nlms.mediation.mtosi.v1.factory.EntityEFMFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.TPDataListT;
import v1.tmf854.TPDataT;
import v1.tmf854ext.adva.DeactivateAndDeleteFDFrT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.ws.Holder;
import java.util.List;

public class DeactivateAndDeleteFDFrWorkerEFM extends DeactivateAndDeleteFDFrWorkerCM{
  Logger LOG = LogManager.getLogger(this.getClass().getName());
  private MtosiTerminationPointEFMDTOImpl mtosiTerminationPointEFMDTO;
  private EntityEFMFactory factory;
  private String fdfrName;
  private MtosiMOFacade facade;
  private DTO<FlowF3Attr> fdfrFlow;
  private DTO<PortF3AccAttr>fdfrAccMO;
  private TPDataListT tpDataListToModify;
  private DTO<PortF3NetAttr> portNET;
  private DTO<PortF3NetAttr> portNET2;
  private TPDataT tpDataACC;
  private TPDataT tpDataNET;
  private TPDataT tpDataNET2;
  private TPDataT tpDataFlow;
  private TPDataT tpDataFTP;
  private DTO<PortF3NetAttr> netForResponse, net2ForResponse;
  private DTO<PortF3AccAttr> accForResponse;
  private DTO<ProtectionGroupF3Attr> ftpForResponse;
  private DTO<ProtectionGroupF3Attr> ftp;
  private MtosiAddress mtosiAddress;
  private NetworkElement ne;


  public DeactivateAndDeleteFDFrWorkerEFM(DeactivateAndDeleteFDFrT mtosiBody, Holder<HeaderT> mtosiHeader, NetworkElement ne) {
    super(mtosiBody, mtosiHeader, ne, null);
    this.mtosiTerminationPointEFMDTO = new MtosiTerminationPointEFMDTOImpl();
    this.ne = ne;
  }

  public void parse() throws Exception {
    super.parse();
    mtosiAddress = new MtosiAddress(mtosiBody.getFdfrName());
    fdfrName=mtosiAddress.getNaming().getFdfrNm();

    facade = getMtosiCtrl().getMoFacadeManager().getFacadeViaNeId(MtosiMOFacade.class, ne.getID());
    List<DTO<FlowF3Attr>> tempFdfrFlows = facade.queryDTO(ne.getID(),FlowF3Attr.class);
    for(DTO<FlowF3Attr> flow : tempFdfrFlows){
      if(flow.getValue(FlowF3Attr.CIRCUIT_NAME) != null && !"".equals(flow.getValue(FlowF3Attr.CIRCUIT_NAME))){
        if(flow.getValue(FlowF3Attr.CIRCUIT_NAME).toUpperCase().contains(fdfrName.toUpperCase()+'^')){
          fdfrFlow= DTOBuilder.getInstance().newDTO(FlowF3Attr.class);
          fdfrFlow.putOrReplace(FlowF3Attr.MTOSI_NAME,flow.getValue(FlowF3Attr.MTOSI_NAME));
          fdfrFlow.putOrReplace(FlowF3Attr.ENTITY_INDEX,flow.getValue(FlowF3Attr.ENTITY_INDEX));
          fdfrFlow.putOrReplace(FlowF3Attr.CIRCUIT_NAME,"");
          break;
        }else{
          throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.FDFR_NOT_FOUND);
        }
      }else{
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
            MtosiErrorConstants.FDFR_NOT_FOUND);
      }
    }

    fdfrAccMO = facade.findDTOViaMtosiName(ne.getID(),fdfrFlow.getValue(ManagedObjectAttr.MTOSI_NAME).split(" && ")[0], PortF3AccAttr.class);
    fdfrAccMO = facade.refreshDTO(ne.getID(),fdfrAccMO);

  }

  @Override
  protected void mediate() throws Exception {
    boolean protection = false;

    factory = new EntityEFMFactory(facade,tpsToModify, getMtosiCtrl(), ne.getID());

    //check if ftp exists
    List<DTO<ProtectionGroupF3Attr>> pgs =  facade.queryDTO(ne.getID(),ProtectionGroupF3Attr.class);
    if (!pgs.isEmpty()) {
      protection = true;
      ftp = pgs.get(0);
    }
    portNET = facade.findDTOViaMtosiName(ne.getID(), "/shelf=1/slot=1/port=NET-1", PortF3NetAttr.class);
    portNET2 = facade.findDTOViaMtosiName(ne.getID(), "/shelf=1/slot=1/port=NET-2", PortF3NetAttr.class);

    String ftpName = null;
    if (protection) {
      ftpName =  pgs.get(0).getValue(ProtectionGroupF3Attr.MTOSI_NAME);
    }

    if (tpsToModify != null)
    {
      int shelfIndex = getIndexFromMtosiname(fdfrAccMO.getValue(ManagedObjectAttr.MTOSI_NAME), EntityClass.SHELF);
      int modifiedSlotIndex = getIndexFromMtosiname(fdfrAccMO.getValue(ManagedObjectAttr.MTOSI_NAME), EntityClass.SLOT);
      if (!MtosiTPMediator.checkTPToModifySameCard(ne, tpsToModify, shelfIndex, modifiedSlotIndex, ftpName))
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(),
            ExceptionUtils.EXCPT_INVALID_INPUT, "The specified TPs must be on the same slot.");
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      tpDataACC = MtosiTPMediator.getTPDataTForCMACC(tpsToModify);
      tpDataNET = MtosiTPMediator.getTPDataTForCMNET(tpsToModify, 1);
      tpDataFlow = MtosiTPMediator.getTPDataTForCTP(tpsToModify);
      if (protection) {
        tpDataFTP = MtosiTPMediator.getTPDataTForFTP(tpsToModify);
        tpDataNET2 = MtosiTPMediator.getTPDataTForCMNET(tpsToModify, 2);
      }
    }
    if (tpDataFlow != null)
    {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(),
          ExceptionUtils.EXCPT_INVALID_INPUT,
          "Flow cannot be modified as part of a DeactivateAndDelete request.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    if (tpDataACC != null)
    {
      accForResponse = factory.getPortAccF3DTO(tpDataACC);
    }
    if (tpDataNET != null)
    {
      netForResponse = factory.getPortNetF3DTO(tpDataNET);
    }
    if (tpDataNET2 != null)
    {
      net2ForResponse = factory.getPortNetF3DTO(tpDataNET2);
    }
    if (tpDataFTP != null)
    {
      if (ftp == null)
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(),
            ExceptionUtils.EXCPT_INVALID_INPUT, "The specified FTP does not exist.");
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      ftpForResponse = factory.getFloatingTerminationPointDTO(tpDataFTP, ftp);
    }

    transact();
  }


  private void transact() throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    try {

//      String mtosiflowname = mtosiTerminationPointEFMDTO.createMtosiNameToDisplay(mtosiAddress.getNE(),fdfrFlow.getValue(ManagedObjectAttr.MTOSI_NAME));
      facade.openNetTransaction(ne.getID());
      logSecurity(ne, SystemAction.DeleteNetwork, (String) fdfrFlow.getValue(ManagedObjectAttr.MTOSI_NAME));
      facade.modifyObjectOnDevice(ne.getID(), fdfrFlow);

      if(accForResponse != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, (String) accForResponse.getValue(ManagedObjectAttr.MTOSI_NAME));
        facade.modifyObjectOnDevice(ne.getID(), accForResponse);
      }

      if(netForResponse != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, (String) netForResponse.getValue(ManagedObjectAttr.MTOSI_NAME));
        facade.modifyObjectOnDevice(ne.getID(), netForResponse);
      }

      if(net2ForResponse != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, (String) net2ForResponse.getValue(ManagedObjectAttr.MTOSI_NAME));
        facade.modifyObjectOnDevice(ne.getID(), net2ForResponse);
      }

      if(ftpForResponse != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, (String) ftpForResponse.getValue(ManagedObjectAttr.MTOSI_NAME));
        facade.modifyObjectOnDevice(ne.getID(), ftpForResponse);
      }

      ne.logSROperation(SROperationState.SERVICE_DELETION_SUCCESS, fdfrName);
    } catch (SNMPCommFailure e) {
      ne.logSROperation(SROperationState.SERVICE_DELETION_FAILURE, fdfrName);
    } finally {
      facade.closeNetTransaction(ne.getID());
    }
  }

  @Override
  protected void response() throws Exception {
    v1.tmf854ext.adva.ObjectFactory factory = new v1.tmf854ext.adva.ObjectFactory();
    JAXBElement<TPDataListT> modifiedTPs = factory
        .createDeactivateAndDeleteFDFrResponseTTpsToModify(getUpdatedTPs());
    response.setTpsToModify(modifiedTPs);
  }
  private TPDataListT getUpdatedTPs() throws ProcessingFailureException {
    ObjectFactory factory = new ObjectFactory();
    TPDataListT tpsToModify = factory.createTPDataListT();
    if (tpDataACC != null && accForResponse != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataACC.getTpName())).toMtosiPTPasTPDataT());
    }
    if (tpDataNET != null && netForResponse != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataNET.getTpName())).toMtosiPTPasTPDataT());
    }
    if (tpDataNET2 != null && net2ForResponse != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataNET2.getTpName())).toMtosiPTPasTPDataT());
    }
    if (tpDataFlow != null && fdfrFlow != null) {
      MTOSIFlowF3 flowUpdated = ManagedElementFactory.getCMFlow(tpDataFlow.getTpName());
      if (flowUpdated != null && flowUpdated.getFDFr() != null){// && flowUpdated.getFlowSPProperties().get(FlowSPPropertiesFSP150CM.VB.Active)) {
        tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(flowUpdated).toMtosiCTPasTPDataT());
      }
    }
    if (tpDataFTP != null && ftp != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ManagedElementFactory.getFtp(tpDataFTP.getTpName())).toMtosiFTPasTPDataT());
    }
    return tpsToModify;
  }

  private  int getIndexFromMtosiname(String mtosiName, int entity){
    String[] parts = mtosiName.split("/");
    int index = 1;
    if (entity == EntityClass.SHELF){
      index = Integer.parseInt(parts[1].replace("shelf=",""));
    }else if (entity == EntityClass.SLOT){
      index = Integer.parseInt(parts[2].replace("slot=",""));
    }
    return index;
  }

}
