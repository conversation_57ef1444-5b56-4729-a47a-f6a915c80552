/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.mediation.common.serviceProvisioning.FTPSPProperties;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.FloatingTerminationPointT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.TerminationPointT;
import v1.tmf854ext.adva.CreateFTPResponseT;
import v1.tmf854ext.adva.FTPCreateDataT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public abstract class CreateFTPWorker extends AbstractMtosiWorker {
  Logger LOG = LogManager.getLogger(this.getClass().getName());

  protected CreateFTPResponseT response = new CreateFTPResponseT();
  protected FTPCreateDataT ftpCreateData;
  protected NamingAttributesT namingAttributes;
  protected NetworkElement ne;

  public CreateFTPWorker(Holder<HeaderT> mtosiHeader, FTPCreateDataT ftpCreateData, NamingAttributesT namingAttributes, NetworkElement ne) {
    super(mtosiHeader, "createFTP", "createFTP", "createFTPResponse");
    this.ftpCreateData = ftpCreateData;
    this.namingAttributes = namingAttributes;
    this.ne = ne;
  }

  @Override
  protected void parse() throws Exception {
    if (namingAttributes.getFtpNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ftpNm has not been specified.");
    }
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(ne.getDefaultNetworkElementTypeString());
  }

  protected void transact(NetworkElement ne, FTPSPProperties props) throws ObjectInUseException, NetTransactionException, SPValidationException, MDOperationFailedException, SNMPCommFailure
  {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "CreateFTPWorker");
    try
    {
      logSecurity(ne, SystemAction.AddNetwork, "ftpNm=" + ftpCreateData.getName().getFtpNm());
      ne.getMTOSIWorker().createFTP(props);
      NetTransactionManager.commitNetTransaction(id);
    }
    catch (NetTransactionException e)
    {
      ne.logSROperation(SROperationState.FTP_CREATION_FAILURE, ftpCreateData.getName().getFtpNm());
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    catch (SPValidationException e)
    {
      ne.logSROperation(SROperationState.FTP_CREATION_FAILURE, ftpCreateData.getName().getFtpNm());
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    catch (MDOperationFailedException e)
    {
      ne.logSROperation(SROperationState.FTP_CREATION_FAILURE, ftpCreateData.getName().getFtpNm());
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    catch (SNMPCommFailure e)
    {
      ne.logSROperation(SROperationState.FTP_CREATION_FAILURE, ftpCreateData.getName().getFtpNm());
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    finally
    {
      NetTransactionManager.ensureEnd(id);
    }
  }

  @Override
  protected void response() throws Exception {
    final FTP ftpMO = ManagedElementFactory.getFtp(namingAttributes);
    if (ftpMO != null)
    {
      ObjectFactory objectFactory = new ObjectFactory();
      FloatingTerminationPointT ftp = new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ftpMO).toMtosiFTP();
//      FloatingTerminationPointT ftp = ftpMO.getMtosiTranslator().toMtosiFTP();
      TerminationPointT tp = objectFactory.createTerminationPointT();
      tp.setFtp(ftp);
      response.setTheFTP(tp);
    }
  }

  @Override
  public CreateFTPResponseT getSuccessResponse()
  {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}