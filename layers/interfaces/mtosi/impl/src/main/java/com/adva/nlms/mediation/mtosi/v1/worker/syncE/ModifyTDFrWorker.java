/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.syncE;

import com.adva.nlms.common.NEUtils;
import com.adva.nlms.mediation.common.serviceProvisioning.F3SyncRefSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.F3SyncSPProperties;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.sync.F3Sync;
import com.adva.nlms.mediation.config.f3.entity.sync.F3SyncImpl;
import com.adva.nlms.mediation.config.f3.entity.syncref.F3SyncRefImpl;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CMImpl;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiSyncEMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiF3F3SyncNameException;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiF3SyncName;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Node;
import v1.tmf854.HeaderT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.TPDataListT;
import v1.tmf854.TPDataT;
import v1.tmf854.TPVendorExtensionsT;
import v1.tmf854ext.adva.ModifyTDFrResponseT;
import v1.tmf854ext.adva.ModifyTDFrT;
import v1.tmf854ext.adva.TimingDomainFragmentT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.ws.Holder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Worker class for the MTOSI operation modifyTDFr
 */
public class ModifyTDFrWorker extends AbstractMtosiWorker {
  private static Logger LOG = LogManager.getLogger(ModifyTDFrWorker.class.getName());

  protected ModifyTDFrT mtosiBody;
  protected ModifyTDFrResponseT response = new ModifyTDFrResponseT();
  protected NamingAttributesT tdfrName;
  protected MtosiAddress mtosiAddr;
  protected JAXBElement<TPDataListT> tpDataList;
  protected TimingDomainFragmentT newTDFr;

  //Constructor
  public ModifyTDFrWorker(final ModifyTDFrT mtosiBody, final Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "modifyTDFr", "modifyTDFr", "modifyTDFrResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    tdfrName = mtosiBody.getTdfrName();
    mtosiAddr = new MtosiAddress(tdfrName);

    if (tdfrName == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
        MtosiErrorConstants.INVALID_FILTER);
    }

    if (tdfrName.getMdNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
        MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!tdfrName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        MtosiErrorConstants.MD_NOT_FOUND);
    }

    if(tdfrName.getTdfrNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        "TDFr name is missing.");
    }

    MtosiUtils.validateNE(mtosiAddr.getNE());

  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(mtosiAddr.getNE().getDefaultNetworkElementTypeString());
  }

  @Override
  protected void mediate() throws Exception {
    // only allowed for FSP150CC-GE201SE && FSP150CM
    if (!NEUtils.isSyncEDevice(mtosiAddr.getNeType())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The specified ME does not support SyncE.");
    }

    NetworkElementFSP150CMImpl ne = (NetworkElementFSP150CMImpl)mtosiAddr.getNE();

    // validate F3Sync-objects for TDFr-name and new-TDFR-Name
    MtosiF3SyncName mtosiName = null;
    
    try {
      mtosiName = new MtosiF3SyncName(tdfrName.getTdfrNm(), true, mtosiAddr.getNeType());
    } catch (MtosiF3F3SyncNameException e) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
      e.getMessage(),e);
      
    }

    final F3SyncImpl f3Sync = getFSP150CMMTOSIWorker(ne).getF3Sync(mtosiName.getShelfIndex(),mtosiName.getSlotIndex(), mtosiName.getAlias());
    if (f3Sync == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The specified TDFr does not exist.");
    }

    F3SyncSPProperties f3SyncPropsToModify = null;
    Map<F3SyncRefImpl, F3SyncRefSPProperties> f3SyncRefPropsToModifyList = new HashMap<F3SyncRefImpl, F3SyncRefSPProperties>();
    List<F3SyncRefSPProperties> f3SyncRefPropsToAddList = new ArrayList<F3SyncRefSPProperties>();
    List<F3SyncRefSPProperties> f3SyncRefPropsToRemoveList = new ArrayList<F3SyncRefSPProperties>();

    // check TDFr Modify Data
    if (mtosiBody.getTDFrModifyData() != null && mtosiBody.getTDFrModifyData().getValue() != null &&
        mtosiBody.getTDFrModifyData().getValue().getTransmissionParams() != null) {

      if (mtosiBody.getTDFrModifyData().getValue().getTransmissionParams() == null ||
          mtosiBody.getTDFrModifyData().getValue().getTransmissionParams().getValue() == null ||
          mtosiBody.getTDFrModifyData().getValue().getTransmissionParams().getValue().getLayeredParameters() == null) {
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                "TDFr Modify Data are missing.");
      }

      LayeredParametersListT paramList = mtosiBody.getTDFrModifyData().getValue().getTransmissionParams().getValue();
      f3SyncPropsToModify = MtosiSyncEMediator.mtosiTDFrModifyDataTToF3SyncProperties(f3Sync, paramList);
      if (f3SyncPropsToModify == null) {
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                "TDFr Modify Data are missing.");
      }
    }

    // check TPs to Modify
    if (mtosiBody.getTpsToModify() != null && mtosiBody.getTpsToModify().getValue() != null &&
        mtosiBody.getTpsToModify().getValue().getTpData() != null &&
        !mtosiBody.getTpsToModify().getValue().getTpData().isEmpty()) {

      for (TPDataT tpData : mtosiBody.getTpsToModify().getValue().getTpData()) {
    	if (tpData != null && tpData.getVendorExtensions() != null) {
	        TPVendorExtensionsT vendorExtensions = tpData.getVendorExtensions().getValue();
	        if (vendorExtensions.getAny().size() > 0) {
		        Node node = (Node) vendorExtensions.getAny().get(0);
		
		        if (node.getLocalName().equals("syncRefsToModify")) {
		          f3SyncRefPropsToModifyList.putAll(MtosiSyncEMediator.mtosiTpsToModifyListTToF3SyncRefPropertyList(f3Sync, vendorExtensions));
		        }
		        else if (node.getLocalName().equals("syncRefsToAdd")) {
		          f3SyncRefPropsToAddList.addAll(MtosiSyncEMediator.mtosiTpsToAddListTToF3SyncRefPropertyList(f3Sync, vendorExtensions));
		        }
		        else if (node.getLocalName().equals("syncRefsToRemove")) {
		          f3SyncRefPropsToRemoveList.addAll(MtosiSyncEMediator.mtosiTpsToRemoveListTToF3SyncRefPropertyList(f3Sync, vendorExtensions));
		        }
		        else {
		          throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
		                  "The vendor extensions has not been specified.");
		        }
	        }
    	}
      }
    }

    transact(ne, f3Sync, f3SyncPropsToModify, f3SyncRefPropsToModifyList, f3SyncRefPropsToAddList, f3SyncRefPropsToRemoveList, mtosiName.toString());

    // get TDFr (modified data)
    newTDFr = ManagedElementFactory.getTDFr(mtosiAddr.getNE(), mtosiName.getShelfIndex(),mtosiName.getSlotIndex(), mtosiName.getAlias());

    // get Sync-refs (modified and new (added) sync-refs)
    final v1.tmf854ext.adva.ObjectFactory objFactoryEx = new v1.tmf854ext.adva.ObjectFactory();
    Set<F3SyncRefImpl> f3SyncRefs = new HashSet<F3SyncRefImpl>(f3SyncRefPropsToModifyList.keySet());
    for (F3SyncRefImpl f3SyncRef : f3Sync.getAllF3SyncRefs()) {
      for (F3SyncRefSPProperties f3SyncRefPropsToAdd : f3SyncRefPropsToAddList) {
        if (f3SyncRef.getF3SyncRefProperties().get(F3SyncRefSPProperties.VI.RefIndex) == f3SyncRefPropsToAdd.get(F3SyncRefSPProperties.VI.RefIndex))
          f3SyncRef.doPollingVolatile();
          f3SyncRefs.add(f3SyncRef);
      }
    }
    TPDataListT modifiedTpDataList =new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(f3Sync).toMtosiTPs(f3SyncRefs);
//    TPDataListT modifiedTpDataList = f3Sync.getMtosiTranslator().toMtosiTPs(f3SyncRefs);
    tpDataList = objFactoryEx.createModifyTDFrTTpsToModify(modifiedTpDataList);
  }

  private void transact(NetworkElement ne,
                        F3Sync f3Sync,
                        F3SyncSPProperties f3SyncPropsToModify,
                        Map<F3SyncRefImpl, F3SyncRefSPProperties> syncRefsToModifyList,
                        List<F3SyncRefSPProperties> syncRefsToAddList,
                        List<F3SyncRefSPProperties> syncRefsToRemoveList,
                        String tdfrName)
    throws ProcessingFailureException, ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure
  {
    final NetworkElement locks[] = new NetworkElement[] { ne };
    final Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "ModifyTDFrWorker");
    try {
      logSecurity(ne, SystemAction.ModifyNetwork, tdfrName);
      // modify TDFr parameters
      if (f3SyncPropsToModify != null) {
        f3Sync.setSettings(f3SyncPropsToModify);
      }
      // modify syncRefs
      for (Map.Entry<F3SyncRefImpl, F3SyncRefSPProperties> f3SyncRefEntry : syncRefsToModifyList.entrySet()) {
        f3SyncRefEntry.getKey().setSettings(f3SyncRefEntry.getValue());
      }
      // add syncRefs
      for (F3SyncRefSPProperties f3SyncRefProps : syncRefsToAddList) {
        f3Sync.createF3SyncRef(f3SyncRefProps);
      }
      // delete syncRefs
      for (F3SyncRefSPProperties f3SyncRefProps : syncRefsToRemoveList) {
        f3Sync.deleteF3SyncRef(f3SyncRefProps);
      }
      NetTransactionManager.commitNetTransaction(id);
    } catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      NetTransactionManager.ensureEnd(id);
    }
  }

  @Override
  protected void response() throws Exception {
    response.setNewTDFr(newTDFr);
    response.setTpsToModify(tpDataList);
  }

  @Override
  public ModifyTDFrResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
