/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */
package com.adva.nlms.mediation.mtosi.v2.utils.facility.flowPointF3;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.mediation.config.dto.ConvertibleAttribute;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.LldpPortConfigAdvaExtAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3AccAttr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.v2.utils.translations.f3.CMPortModeTranslation;


public class PortAccF3Impl extends AbstractPortF3Eth<PortF3AccAttr, LldpPortConfigAdvaExtAttr> {


  public PortAccF3Impl(int neId,DTO<PortF3AccAttr> dto) {
    super(neId,dto);
  }

  @Override
  public  boolean contains(ConvertibleAttribute attribute){
    return dto.contains(attribute);
  }

  @Override
  public boolean isEpl() {
    return CMPortModeTranslation.CO_EPL.getMtosiString().equals(CMPortModeTranslation.getMtosiString(dto.getValue(PortF3AccAttr.PORT_MODE),dto.getValue(PortF3AccAttr.SVC_TYPE)));
  }

  @Override
  public int getAdministratorControl() {
    try {
      return dto.getValue(PortF3AccAttr.ADMIN_STATE);
    }catch (NullPointerException ex){
      return 6;
    }
  }

  @Override
  public void setAdministratorControl(int value){
      dto.putOrReplace(PortF3AccAttr.ADMIN_STATE, value);
  }

  @Override
  public int getMaximumFrameSize() {
    return dto.getValue(PortF3AccAttr.IF_MTU);
  }

  @Override
  public void setMaximumFrameSize(int value){
    dto.putOrReplace(PortF3AccAttr.IF_MTU, value);
  }

  @Override
  public int getAfpType() {
    return dto.getValue(PortF3AccAttr.AFP_TYPE);
  }

  @Override
  public void setAfpType(int value){
    dto.putOrReplace(PortF3AccAttr.AFP_TYPE, value);
  }

  @Override
  public int getLinkLossFwdEnabled() {
    return dto.getValue(PortF3AccAttr.LINK_LOSS_FWD_ENABLED);
  }

  @Override
  public void setLinkLossFwdEnabled(int value){
    dto.putOrReplace(PortF3AccAttr.LINK_LOSS_FWD_ENABLED, value);
  }


  @Override
  public int getLinkLossFwdDelay() {
    return dto.getValue(PortF3AccAttr.LINK_LOSS_FWD_DELAY);
  }

  @Override
  public void setLinkLossFwdDelay(int value){
    dto.putOrReplace(PortF3AccAttr.LINK_LOSS_FWD_DELAY, value);
  }

  @Override
  public int getLinkLossFwdTxActionType() {
    return dto.getValue(PortF3AccAttr.LINK_LOSS_FWD_TX_ACTION_TYPE);
  }

  @Override
  public void setLinkLossFwdTxActionType(int value){
    dto.putOrReplace(PortF3AccAttr.LINK_LOSS_FWD_TX_ACTION_TYPE, value);
  }

  @Override
  public int getLocalLinkId() {
    return dto.getValue(PortF3AccAttr.LINK_LOSS_FWD_LOCAL_LINK_ID);
  }

  @Override
  public void setLocalLinkId(int value){
    dto.putOrReplace(PortF3AccAttr.LINK_LOSS_FWD_LOCAL_LINK_ID, value);
  }

  @Override
  public int getRemoteLinkIds() {
    return dto.getValue(PortF3AccAttr.LINK_LOSS_FWD_REMOTE_LINK_IDS);
  }

  @Override
  public void setRemoteLinkIds(int value){
    dto.putOrReplace(PortF3AccAttr.LINK_LOSS_FWD_REMOTE_LINK_IDS, value);
  }

  @Override
  public int getLinkLossFwdTriggerTypes() {
    return dto.getValue(PortF3AccAttr.LINK_LOSS_FWD_TRIGGER_TYPES);
  }

  @Override
  public void setLinkLossFwdTriggerTypes(int value){
    dto.putOrReplace(PortF3AccAttr.LINK_LOSS_FWD_TRIGGER_TYPES, value);
  }

  @Override
  public int getLinkLossFwdActive() {
    return dto.getValue(PortF3AccAttr.LINK_LOSS_FWD_ACTIVE);
  }

  @Override
  public int getLinkLossPartnerEnabled() {
    return dto.getValue(PortF3AccAttr.LINK_LOSS_FWD_PARTNER_ENABLED);
  }

  @Override
  public boolean getN2AVlanTrunkingEnabled() {
    return dto.getValue(PortF3AccAttr.N2A_VLAN_TRUNKING_ENABLED);
  }

  @Override
  public void setN2AVlanTrunkingEnabled(boolean value) {
    dto.putOrReplace(PortF3AccAttr.N2A_VLAN_TRUNKING_ENABLED, value);
  }

  @Override
  public boolean getA2NPushPortVIDEnabled() {
    return dto.getValue(PortF3AccAttr.A2N_PUSH_PORT_VID_ENABLED);
  }

  @Override
  public void setA2NPushPortVIDEnabled(boolean value) {
    dto.putOrReplace(PortF3AccAttr.A2N_PUSH_PORT_VID_ENABLED, value);
  }

  @Override
  public boolean getN2APopPortVIDEnabled() {
    return dto.getValue(PortF3AccAttr.N2A_POP_PORT_VID_ENABLED);
  }

  @Override
  public void setN2APopPortVIDEnabled(boolean value) {
    dto.putOrReplace(PortF3AccAttr.N2A_POP_PORT_VID_ENABLED, value);
  }

  @Override
  public int getLoopbackStatus() {
    return dto.getValue(PortF3AccAttr.LOOPBACK_STATUS);
  }

  @Override
  public int getLoopbackTime() {
    return dto.getValue(PortF3AccAttr.LOOPBACK_TIME);
  }

  @Override
  public int getLoopbackSwapSada() {
    return dto.getValue(PortF3AccAttr.LOOPBACK_SWAP_SADA);
  }

  @Override
  public int getOperState() {
    return dto.getValue(PortF3AccAttr.OPER_STATE);
  }

  @Override
  public int getSecondaryState(){
    return dto.getValue(PortF3AccAttr.SECONDARY_STATE);
  }

  @Override
  public boolean isPortModeSet() {
    return dto.getValue(PortF3AccAttr.PORT_MODE) != null;
  }

  @Override
  public int getTrafficModel() {
    return dto.getValue(PortF3AccAttr.TRAFFIC_MODEL);
  }

  @Override
  public String getMtosiName() {
    return dto.getValue(PortF3AccAttr.MTOSI_NAME);
  }

  @Override
  public EntityIndex getEnityIndex() {
    return dto.getValue(PortF3AccAttr.ENTITY_INDEX);
  }

  @Override
  public void setMtosiName(String value) {
    dto.putOrReplace(PortF3AccAttr.MTOSI_NAME, value);
  }

  @Override
  public void setEnityIndex(EntityIndex value) {
    dto.putOrReplace(PortF3AccAttr.ENTITY_INDEX, value);
  }

  @Override
  public int getSvcType(){
    return dto.getValue(PortF3AccAttr.SVC_TYPE);
  }

  @Override
  public DTO<PortF3AccAttr> refresh(MtosiMOFacade facade){
    return facade.refreshDTO(neId,dto);
  }

  @Override
  public void getForResponse(MtosiMOFacade facade, boolean refreshed){
    dto = facade.findDTOViaMtosiName(neId,dto.getValue(PortF3AccAttr.MTOSI_NAME),PortF3AccAttr.class);
    if(refreshed)
      facade.refreshDTO(neId,dto);

  }

}
