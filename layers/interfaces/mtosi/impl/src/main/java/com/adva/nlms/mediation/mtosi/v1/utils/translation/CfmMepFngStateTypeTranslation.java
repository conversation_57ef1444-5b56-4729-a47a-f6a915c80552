/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

/**
 * Created by IntelliJ IDEA. User: mariuszg Date: 2007-06-25 Time: 10:13:25 To change this template use File | Settings
 * | File Templates.
 */
public enum CfmMepFngStateTypeTranslation
{
  FNG_RESET           (1, "FngReset"),
  FNG_DEFECT          (2, "FngDefect"),
  FNG_REPORT_DEFECT   (3, "FngReportDefect"),
  FNG_DEFECT_REPORTED (4, "FngDefectReported"),
  FNG_DEFECT_CLEARING (5, "fngDefectClearing"),
  NOT_APPLICABLE      (6, "n/a");

  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;
  //------------------------------------------------------------------------------------------------------------------

  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private CfmMepFngStateTypeTranslation (final int mibValue, final String mtosiString)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }

  public int getMIBValue(final String mtosiString) {
    int cfmTypeTranslation = 6;
    
    for (CfmMepFngStateTypeTranslation tmpCfmTypeTranslation : values())
    {
      if (mtosiString.equals(tmpCfmTypeTranslation.getMtosiString()))
      {
        cfmTypeTranslation = tmpCfmTypeTranslation.getMIBValue();
        break;
      }
    }
    
    return cfmTypeTranslation;
  }
  
  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int mibValue)
  {
    CfmMepFngStateTypeTranslation cfmTypeTranslation = NOT_APPLICABLE;  // the return value

    for (CfmMepFngStateTypeTranslation tmpCfmTypeTranslation : values())
    {
      if (mibValue == tmpCfmTypeTranslation.getMIBValue())
      {
        cfmTypeTranslation = tmpCfmTypeTranslation;
        break;
      }
    }
    return cfmTypeTranslation.getMtosiString();
  }

}
