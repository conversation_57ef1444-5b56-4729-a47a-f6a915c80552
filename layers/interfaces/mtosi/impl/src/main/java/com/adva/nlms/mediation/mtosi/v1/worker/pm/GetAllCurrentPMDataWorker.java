/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.pm;

import java.util.Iterator;
import java.util.List;

import jakarta.xml.ws.Holder;

import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.GetAllCurrentPMDataResponseT;
import v1.tmf854ext.adva.GetAllCurrentPMDataT;
import v1.tmf854ext.adva.PMDataListT;
import v1.tmf854ext.adva.PMParameterNameListT;
import v1.tmf854ext.adva.PMTPSelectListT;
import v1.tmf854ext.adva.PMTPSelectT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.PMFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;

public class GetAllCurrentPMDataWorker extends AbstractMtosiWorker {
  protected GetAllCurrentPMDataT mtosiBody;
  protected GetAllCurrentPMDataResponseT response = new GetAllCurrentPMDataResponseT();
  protected PMTPSelectListT entityList = null;
  protected PMParameterNameListT parameterList = null;
  protected PMDataListT pmDataList;

  public GetAllCurrentPMDataWorker(GetAllCurrentPMDataT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getAllCurrentPMData", "getAllCurrentPMData", "getAllCurrentPMDataResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    if ((entityList = mtosiBody.getPmTPSelectList()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The pmTPSelectList has not been specified.");
    }

    parameterList = mtosiBody.getPmParameters();
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
		List<PMTPSelectT> list = entityList.getPmtpsel();
		for (Iterator<PMTPSelectT> iter = list.iterator(); iter.hasNext();) {
			PMTPSelectT element = iter.next();
			NamingAttributesT naming = element.getName();
      if(naming == null){
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
            "Naming must be present in request.");
      }
      if(naming.getMdNm() == null){
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.MD_NAME_MISSING);
      }
      if (!naming.getMdNm().equals(OSFactory.getMDNm())) {
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
            MtosiErrorConstants.MD_NOT_FOUND);
      }

			NetworkElement ne = ManagedElementFactory.getAndValidateNE(naming);
			validator.validate(ne.getDefaultNetworkElementTypeString());
		}

  }
  
  @Override
  protected void mediate() throws Exception {
    pmDataList = PMFactory.getCurrentPMDataList(entityList, parameterList);

  }

  @Override
  protected void response() throws Exception {
    response.setPmDataList(pmDataList);
  }

  @Override
  public GetAllCurrentPMDataResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }

}