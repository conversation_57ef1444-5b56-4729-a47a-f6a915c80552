/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum GE20XBitSABitDesignationTranslation implements TranslatableEnum {
    none			(1, "None"),
    bit4			(2, "Bit4"),
    bit5			(3, "Bit5"),
    bit6			(4, "Bit6"),
    bit7			(5, "Bit7"),
    bit8			(6, "Bit8"),
    NOT_APPLICABLE	(-1, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private GE20XBitSABitDesignationTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}