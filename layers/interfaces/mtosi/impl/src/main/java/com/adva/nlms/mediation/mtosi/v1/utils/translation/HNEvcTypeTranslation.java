/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;
 /**
  *
  *HnEvcTypeValue ::= TEXTUAL-CONVENTION
     STATUS      current
     DESCRIPTION "Enumerated list of possible EVC types."
     SYNTAX      INTEGER {
                     eline         (0),
                     elan          (1),
                     remoteLag     (2)
                 }

  */
public enum HNEvcTypeTranslation {
    ELINE         (0),
    ELAN          (1),
    REMOTELAG     (2);
    
    private int mibValue;
    
    private int getMibValue() {
		return mibValue;
	}

	private HNEvcTypeTranslation(int code) {
    	this.mibValue = code;
    }

    public static String getMtosiString(final Integer mibValue) {
    	if (mibValue == null) {
    		return "null";
    	}
    	for (HNEvcTypeTranslation value: values() ) {
    		if (value.getMibValue() == mibValue.intValue()) {
    			return value.name(); 
    		}
    	}
    	//Probably should throw something...
    	return String.valueOf(mibValue);
    }
    
    public static int getMibValue(final String name) {
    	for (HNEvcTypeTranslation value: values() ) {
    		if (value.name().equals(name)) {
    			return value.getMibValue(); 
    		}
    	}
    	//Probably should throw something...
    	return -1;
    }
}
