/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mtosisupport;

import java.util.List;

import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;

/**
 * This class is the Mtosi Supported Validator for a specific operation.
 * the supportedNes list contains the list of NEs that are supported for 
 * a specific operation.
 * 
 * An operation is unConditionally valid when it is specified in the "FSP NM" Network Element list.
 *  
 * <AUTHOR>
 *
 */
public class MtosiSupportValidator {
	
	private List<String> supportedNes;
	private String operation;

	public MtosiSupportValidator(String mtosiRequest, List<String> neList) {
		this.operation = mtosiRequest;
		this.supportedNes = neList;
	}
	
	public boolean isUnconditional() {
		return supportedNes.contains("FSP NM");
	}
	
	public void validate(String neType) throws ProcessingFailureException {
		if(!isUnconditional() && ! supportedNes.contains(neType)) {
			pfe(MtosiErrorConstants.MTOSI_OPERATION_NOT_SUPPORTED_FOR_DEVICE_TYPE);
		}
	}

	private void pfe( String var) throws ProcessingFailureException {
		ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,var);
		ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
		throw pfe;
	}
	
	public String toString() {
		return operation+" : "+supportedNes;
	}

}
