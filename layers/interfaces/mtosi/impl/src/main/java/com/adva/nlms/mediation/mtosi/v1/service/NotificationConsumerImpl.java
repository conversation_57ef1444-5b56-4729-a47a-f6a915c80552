/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;

import java.util.logging.Logger;
import ws.v1.tmf854.NotificationConsumer;


/**
 * This class was generated by the Celtix 1.1-SNAPSHOT
 * Fri Dec 22 10:48:24 EST 2006
 * Generated source version: 1.1-SNAPSHOT
 * 
 */

@jakarta.jws.WebService(name = "NotificationConsumer", serviceName = "NotificationService", portName = "NotificationConsumerHttp", targetNamespace = "tmf854.v1.ws")

public class NotificationConsumerImpl implements NotificationConsumer {
  private static final Logger LOG = Logger.getLogger(NotificationConsumerImpl.class.getPackage().getName());

  @Override
  public void notify(v1.tmf854.HeaderT mtosiHeader, v1.tmf854.NotifyT mtosiBody) {
    LOG.info("Executing operation notify");
  }
}
