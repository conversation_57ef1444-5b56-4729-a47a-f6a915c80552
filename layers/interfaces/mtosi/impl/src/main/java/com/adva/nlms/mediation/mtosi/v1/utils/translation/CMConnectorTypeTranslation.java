/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum CMConnectorTypeTranslation implements TranslatableEnum, com.adva.nlms.mediation.mtosi.v2.utils.translations.f3.TranslatableEnum {
  NOT_APPLICABLE     (0, "Unknown"),
  UNKNOWN            (1, "Unknown"),
  SC                 (2, "SC"),
  FCS1CU             (3, "FCS1CU"),
  FCS2CU             (4, "FCS2CU"),
  B<PERSON>_TNC            (5, "BNC-TNC"),
  FCCOAXHDR          (6, "FCCOAXHDR"),
  FJACK              (7, "FJACK"),
  LC                 (8, "LC"),
  MT_RJ              (9, "MT-RJ"),
  MU                 (10, "MU"),
  SG                 (11, "SG"),
  OPTPIGTAIL         (12, "OPTPIGTAIL"),
  HSSDC              (13, "HSSDC"),
  CUPIGTAIL          (14, "CUPIGTAIL"),
  VENDORSPECIFIC     (15, "VENDORSPECIFIC");

  private final int    mibValue;
  private final String mtosiString;

  private CMConnectorTypeTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}