/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import java.util.HashMap;
import java.util.Map;

public enum ProtectionSwitchMode {
  UNKNOWN(0, "N/A"),
  ONEPLUSONE(1, "OnePlusOne"),
  DUALACTIVERX(2, "DualActiveRx"),
  UNIVERSALRING(2, "UniversalRing");

  int id;
  String label;
  private static final Map<String, ProtectionSwitchMode> stringMap;

  static {
    stringMap = new HashMap<>();
    for (ProtectionSwitchMode v : values()) {
      stringMap.put(String.valueOf(v.label), v);
      stringMap.put(String.valueOf(v.id), v);
    }
  }

  private ProtectionSwitchMode(int id, String label) {
    this.id = id;
    this.label = label;
  }

  public static ProtectionSwitchMode valueOfString(String label) {
    if (label == null || !stringMap.containsKey(label)) return UNKNOWN;
    return stringMap.get(label);
  }

  public static ProtectionSwitchMode valueOfLabel(String label) {
    ProtectionSwitchMode enumType = UNKNOWN;
    for (ProtectionSwitchMode tmpEnumType : values()) {
      if (label.equals(tmpEnumType.getLabel())) {
        enumType = tmpEnumType;
        break;
      }
    }
    return enumType;
  }

  public static ProtectionSwitchMode valueOfId(int id) {
    return stringMap.get(String.valueOf(id));
  }

  public int getId() {
    return id;
  }

  public String getLabel() {
    return label;
  }

}