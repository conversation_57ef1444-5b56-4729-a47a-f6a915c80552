/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi;

import java.util.Iterator;
import java.util.List;

import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854ext.adva.ObjectFactory;
import v1.tmf854ext.adva.PMDataT;
import v1.tmf854ext.adva.PMMeasurementListT;
import v1.tmf854ext.adva.PMMeasurementT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.common.performance.types.TimeType;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiPMMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.PMUtils;
import com.adva.nlms.mediation.performance.data.PMData;
import com.adva.nlms.mediation.performance.data.PMMeasurement;

public class PerformanceMediator
{
		
	
	
	public static PMDataT nmsPMDataToMtosiPMData(PMData nmsData) throws Exception
	{
		ObjectFactory factory = new ObjectFactory();
		if (nmsData == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INTERNAL_ERROR,
					"Could not process data returned from the Managed Element.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);

		}
		PMDataT mtosiData = factory.createPMDataT();
		String gran = nmsTimeTypeToMtosiGranularity(nmsData.getTimeType());
		if (gran == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INTERNAL_ERROR,
					"An invalid Granularity was returned from the Managed Element.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		mtosiData.setGranularity(gran);
		mtosiData.setPmMeasurementList(nmsMeasurementListToMtosiMeasurementList(nmsData.getMeasurementList()));
		mtosiData.setRetrievalTime(PMUtils.stringFromDate(nmsData.getRetrievalTime()));
		mtosiData.setStartTime(PMUtils.stringFromDate(nmsData.getStartTime()));
		// we could translate Object entity here to get tpName, 
		// but we just add that in the Factory when data is returned on 
		// a per entity basis
				
		return mtosiData;
	}
	
	public static PMMeasurementListT nmsMeasurementListToMtosiMeasurementList(List<PMMeasurement> nmsList) throws Exception
	{
		
		ObjectFactory factory = new ObjectFactory();
		PMMeasurementListT mtosiList = factory.createPMMeasurementListT();
		if (nmsList == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INTERNAL_ERROR,
					"Measurement List from the Managed Element was empty.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		for (Iterator iterator = nmsList.iterator(); iterator.hasNext();)
		{
			PMMeasurement nextNMS = (PMMeasurement) iterator.next();
			PMMeasurementT nextMtosi = factory.createPMMeasurementT();
			nextMtosi = nmsMeasurementToMtosiMeasurement(nextNMS);
			if(nextMtosi!=null)
			{
				mtosiList.getPmmeasure().add(nextMtosi);
			}
			
		}
		
		return mtosiList;
	}

	public static String nmsTimeTypeToMtosiGranularity(TimeType timeType)
	{
		if (timeType.equals(TimeType._15_MIN) || timeType.equals(TimeType.CURRENT_15_MIN))
		{
			return MtosiPMMediator.GRANULARITY_15;
		}
		if (timeType.equals(TimeType._24_H) || timeType.equals(TimeType.CURRENT_24_H))
		{
			return MtosiPMMediator.GRANULARITY_24;
		}
		if (timeType.equals(TimeType.ROLLOVER) || timeType.equals(TimeType.CURRENT_TOTAL))
		{
			return MtosiPMMediator.GRANULARITY_NA;
		}
		else
		{
			return null;
		}
	}

	public static PMMeasurementT nmsMeasurementToMtosiMeasurement(PMMeasurement nmsMeasurement) throws Exception
	{
		if (nmsMeasurement == null)
			return null;

		ObjectFactory factory = new ObjectFactory();

		PMMeasurementT mtosiMeasurement = factory.createPMMeasurementT();
		mtosiMeasurement.setPmLocation(nmsMeasurement.getLocation().toString());
		mtosiMeasurement.setPmParameterName(nmsMeasurement.getDataType().getMtosiShort());
		if(nmsMeasurement.isUnavailable())
		{
			// value was not available, so return null - no measurement for this counter
			return null;
		}
		
		mtosiMeasurement.setValue(nmsMeasurement.getValue());
		String unit = nmsMeasurement.getUnit();
		if(unit!=null)
		{
			mtosiMeasurement.setUnit(String.valueOf(unit));
		}
		
		mtosiMeasurement.setIntervalStatus(PMUtils.intervalStatusForMeasurement(nmsMeasurement));

		return mtosiMeasurement;
	}
	
	

}
