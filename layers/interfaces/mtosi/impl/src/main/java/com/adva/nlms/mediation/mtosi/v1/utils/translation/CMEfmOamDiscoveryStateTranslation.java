/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum CMEfmOamDiscoveryStateTranslation implements TranslatableEnum, com.adva.nlms.mediation.mtosi.v2.utils.translations.f3.TranslatableEnum {
  NOT_AVAILABLE            (0, "NotAvailable"),
  FAULT                    (1, "Fault"),
  ACTIVE_SEND_LOCAL        (2, "ActiveSendLocal"),
  PASSIVE_WAIT             (3, "PassiveWait"),
  SEND_LOCAL_REMOTE        (4, "SendLocalRemote"),
  SEND_LOCAL_REMOTE_OK     (5, "SendLocalRemoteOk"),
  SEND_ANY                 (6, "SendAny"),
  NOT_APPLICABLE           (7, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private CMEfmOamDiscoveryStateTranslation (final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}