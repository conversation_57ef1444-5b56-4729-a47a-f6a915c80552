/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import v1.tmf854.NamingAttributesT;

import com.adva.nlms.mediation.config.NetworkElement;

/**
 * Created by IntelliJ IDEA. User: PawelP Date: 2008-11-17 Time: 15:20:57 To
 * change this template use File | Settings | File Templates.
 */
public class ConfAdjustmentHN4000 {

	public static void adjustBondedFtpToPortSyntax(
			final NamingAttributesT namingAttributes, NetworkElement ne) {
		final StringBuilder sb = new StringBuilder();
		if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400) {
			int deviceId = ne.getNeIndex();
			sb.append("/shelf=");
			sb.append(deviceId);
			sb.append("/slot=1");
		} else {
			sb.append("/shelf=0/slot=1");
		}

		sb.append(namingAttributes.getFtpNm());
		namingAttributes.setPtpNm(sb.toString());
	}
}
