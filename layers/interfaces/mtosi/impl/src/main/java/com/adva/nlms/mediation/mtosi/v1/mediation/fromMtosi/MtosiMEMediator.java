/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi;

import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ManagedElementSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ShelfSPProperties;
import com.adva.nlms.mediation.config.fsp150cp_mx.BitFieldHelper;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPVersionEnum;
import org.w3c.dom.Node;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.MEVendorExtensionsT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854ext.adva.MEDataT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;
import java.util.Iterator;
import java.util.List;

public class MtosiMEMediator extends MtosiTPMediator
{
	public static ShelfSPProperties mtosiMEDataTToCPShelfProperties(MEDataT meData, ShelfSPProperties props)
			throws ProcessingFailureException
	{
		Integer pauseEnable = null;
		Integer linkLossFwd = null;
    Integer MACAddressLearning = null;
    byte[]  bpduFilter = null;
    MEVendorExtensionsT extensions = meData.getVendorExtensions().getValue();
		List list = extensions.getAny();
		JAXBContext jc = null;
		Unmarshaller u = null;
		
		try
		{
			jc = JAXBContext.newInstance("v1.tmf854");
			u = jc.createUnmarshaller();
			
		}
		catch (JAXBException ex)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
					ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.JAXB_MARSHALLING_PROBLEM);
			ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
			throw pfe;
		}
		for (Iterator iter = list.iterator(); iter.hasNext();)
		{
			Node element = (Node) iter.next();
			LayeredParametersListT transmissionParams;
			try
			{
				// transmissionParams = (LayeredParametersListT) u.unmarshal(element); // this didn't work
				// Replace by two lines below, and it does work
				JAXBElement<LayeredParametersListT> root = u.unmarshal(element,LayeredParametersListT.class);
				transmissionParams = root.getValue();
				
												
				String linkLossFwdString = LayeredParameterUtils.getLayeredParameter(transmissionParams,
						LayeredParams.PROP_ADVA_ETHERNET, LayeredParams.LrPropAdvaEthernet.ACCESS_TO_ACCESS_LINK_LOSS_FORWARDING_PARAM);
				if (linkLossFwdString != null)
				{
					linkLossFwd = MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE, linkLossFwdString);
					if (linkLossFwd == 3)
					{
						ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
								ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.LINK_LOSS_FWD_ILLEGAL_VALUE);
						ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
						throw pfe;
					}
					
				}
				String pauseEnableString = LayeredParameterUtils.getLayeredParameter(transmissionParams,
						LayeredParams.PROP_ADVA_ETHERNET, LayeredParams.LrPropAdvaEthernet.PAUSE_FRAMES_ENABLED_PARAM);
				if (pauseEnableString != null)
				{
					pauseEnable = MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE, pauseEnableString);
					if (pauseEnable == 3)
					{
						ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
								ExceptionUtils.EXCPT_INVALID_INPUT,
								MtosiErrorConstants.PAUSE_FRAMES_ENABLED_ILLEGAL_VALUE);
						ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
						throw pfe;
					}
				
				}
        String MACAddressLearningString = LayeredParameterUtils.getLayeredParameter(transmissionParams,
						LayeredParams.PROP_ADVA_ETHERNET, LayeredParams.LrPropAdvaEthernet.MAC_ADDRESS_LEARNING_PARAM);
        if (MACAddressLearningString != null)
				{
					MACAddressLearning = MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE, MACAddressLearningString);
					if (MACAddressLearning == 3)
					{
						ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
								ExceptionUtils.EXCPT_INVALID_INPUT,
								MtosiErrorConstants.MAC_ADDRESS_LEARNING_ILLEGAL_VALUE);
            throw new ProcessingFailureException(pfet.getReason(), pfet);
					}

				}

        String BPDUForwardingFilterString = LayeredParameterUtils.getLayeredParameter(transmissionParams,
						LayeredParams.PROP_ADVA_ETHERNET, LayeredParams.LrPropAdvaEthernet.BPDU_FORWARDING_FILTER_PARAM);
        if (BPDUForwardingFilterString != null)
				{
          bpduFilter = BitFieldHelper.convertIntArrayToRightMostByteArray(BPDUForwardingFilterValidation(BPDUForwardingFilterString));
				}


      }
			catch (JAXBException ex)
			{
				// not right element so skip it
				
			}
		}
		if (linkLossFwd == null && pauseEnable == null && MACAddressLearning == null && bpduFilter == null)
			return null;
		ShelfSPProperties newProps = new ShelfSPProperties(props.get(EquipmentSPProperties.VE.Index));
		newProps.set(ShelfSPProperties.VI.LinkLossFwd, linkLossFwd);
		newProps.set(ShelfSPProperties.VI.PauseEnable, pauseEnable);
		newProps.set(ShelfSPProperties.VI.MACAddressLearning, MACAddressLearning);
    newProps.set(ShelfSPProperties.Vb.BpduFilter, bpduFilter);
    return newProps;
	}

  public static ManagedElementSPProperties mtosiMEDataTToMEProperties(MEVendorExtensionsT extensions)
      throws ProcessingFailureException, NullPointerException
  {
    String ipAddress = null;
    String subnetPath = null;
    String useGlobalSnmpString = null;
    String snmpProtocolVersion = null;
    String snmpReadCommunity = null;
    String snmpWriteCommunity = null;

		List list = extensions.getAny();
    Unmarshaller unmarshaller = null;

    try
    {
      unmarshaller = JAXBContext.newInstance("v1.tmf854").createUnmarshaller();
    }
    catch (JAXBException ex)
    {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.JAXB_MARSHALLING_PROBLEM, ex);
    }

    for (Iterator iter = list.iterator(); iter.hasNext();)
    {
      Node element = (Node) iter.next();
      LayeredParametersListT meParams;
      try
      {
        JAXBElement<LayeredParametersListT> root = unmarshaller.unmarshal(element,LayeredParametersListT.class);
        meParams = root.getValue();

        // parse parameter ipAddress
        ipAddress = LayeredParameterUtils.getLayeredParameter(meParams,
                                    LayeredParams.PROP_ADVA,
                                    LayeredParams.PropAdva.IP_ADDRESS);

        // parse parameter subnetPath
        subnetPath = LayeredParameterUtils.getLayeredParameter(meParams,
                                    LayeredParams.PROP_ADVA_TOPOLOGY,
                                    LayeredParams.PropAdvaTopology.SubnetPath);

        // parse parameter useGlobalSnmpSettings
        useGlobalSnmpString = LayeredParameterUtils.getLayeredParameter(meParams,
                                    LayeredParams.PROP_ADVA_SNMP,
                                    LayeredParams.PropAdvaSnmp.USE_GLOBAL_SNMP_SETTINGS);

        // parse parameter SNMPProtocolVersion
        snmpProtocolVersion = LayeredParameterUtils.getLayeredParameter(meParams,
                                    LayeredParams.PROP_ADVA_SNMP,
                                    LayeredParams.PropAdvaSnmp.SNMP_PROTOCOL_VERSION);

        // parse parameter ReadCommunity
        snmpReadCommunity = LayeredParameterUtils.getLayeredParameter(meParams,
                                    LayeredParams.PROP_ADVA_SNMP_V1V2C,
                                    LayeredParams.PropAdvaSnmpV1V2c.READ_COMMUNITY);

        // parse parameter WriteCommunity
        snmpWriteCommunity = LayeredParameterUtils.getLayeredParameter(meParams,
                                    LayeredParams.PROP_ADVA_SNMP_V1V2C,
                                    LayeredParams.PropAdvaSnmpV1V2c.WRITE_COMMUNITY);
      }
      catch (JAXBException ex)
      {
        // not right element so skip it
      }
    }

    if (ipAddress == null && subnetPath == null && useGlobalSnmpString == null &&
        snmpProtocolVersion == null && snmpReadCommunity == null && snmpWriteCommunity == null)
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
              "The following parameters are missing: ipAddress, subnetPath, useGlobalSnmpString, snmpProtocolVersion, " +
                      "snmpReadCommunity, snmpWriteCommunity");

    ManagedElementSPProperties meProps = new ManagedElementSPProperties();

    if (ipAddress != null) {
      if (ipAddress.matches("^\\d+\\.\\d+\\.\\d+\\.\\d+$")) {
        meProps.set(ManagedElementSPProperties.VS.IPAddress, ipAddress);
      }
      else {
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
                "The parameter IpAddress is wrong.");
      }
    }
    if (subnetPath != null) {
      meProps.set(ManagedElementSPProperties.VS.SubnetPath, subnetPath);
    }
    if (useGlobalSnmpString != null) {
      int useGlobalSnmpInt = MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE, useGlobalSnmpString);
      if (useGlobalSnmpInt != BooleanTypeTranslation.NOT_APPLICABLE.getMIBValue()) {
        meProps.set(ManagedElementSPProperties.VB.UseGlobalSNMPSettings,
                  (useGlobalSnmpInt == BooleanTypeTranslation.ENABLED.getMIBValue()) ? true : false);
      }
      else {
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
                "The parameter UseGlobalSNMPSettings is wrong.");
      }
    }
    if (snmpProtocolVersion != null) {
      if (snmpProtocolVersion.equals("SNMPv1")) {
        meProps.set(ManagedElementSPProperties.VI.SNMPProtocolVersion, SNMPVersionEnum.VERSION1.getId());
      }
      else if (snmpProtocolVersion.equals("SNMPv2c")) {
        meProps.set(ManagedElementSPProperties.VI.SNMPProtocolVersion, SNMPVersionEnum.VERSION2C.getId());
      }
      else if (snmpProtocolVersion.equals("SNMPv3")) {
        meProps.set(ManagedElementSPProperties.VI.SNMPProtocolVersion, SNMPVersionEnum.VERSION3.getId());
      }
      else {
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
                "The parameter SNMPProtocolVersion is wrong.");
      }
    }
    if (snmpReadCommunity != null) {
      meProps.set(ManagedElementSPProperties.VS.ReadCommunity, snmpReadCommunity);
    }
    if (snmpWriteCommunity != null) {
      meProps.set(ManagedElementSPProperties.VS.WriteCommunity, snmpWriteCommunity);
    }

    return meProps;
  }

  /**
   * Return int[] from given parameter.
   * example of parameter: 0000000000000001
   * @param BPDUForwardingFilter
   * @return int[]
   * @throws ProcessingFailureException
   */

  public static int[] BPDUForwardingFilterValidation(final String BPDUForwardingFilter)
          throws ProcessingFailureException
  {
    if (BPDUForwardingFilter.length() != 16)
    {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
								ExceptionUtils.EXCPT_INVALID_INPUT,
								MtosiErrorConstants.BPDU_FORWARDING_FILTER_ILLEGAL_VALUE);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    int[] bpduFilter = new int[16];
    char [] BPDUForwardingFilterArray = BPDUForwardingFilter.toCharArray();
    for (int i=0; i<BPDUForwardingFilter.length(); i++)
    {
      char value = BPDUForwardingFilterArray[i];
      String s = String.valueOf(value);
      if (MtosiUtils.isInteger(s))
      {
        Integer intValue = Integer.valueOf(s);
        if (intValue != 1 && intValue != 0)
        {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
                  ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.BPDU_FORWARDING_FILTER_ILLEGAL_VALUE);
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }
        bpduFilter[i] = intValue;
      } else
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
								ExceptionUtils.EXCPT_INVALID_INPUT,
								MtosiErrorConstants.BPDU_FORWARDING_FILTER_ILLEGAL_VALUE);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
    }
    return bpduFilter;
  }
}
