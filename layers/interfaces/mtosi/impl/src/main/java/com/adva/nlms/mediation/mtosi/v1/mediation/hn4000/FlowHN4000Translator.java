/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.hn4000;

import v1.tmf854.ConnectionTerminationPointT;
import v1.tmf854.DirectionalityT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ResourceStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.UNISPPropertiesHN4000;
import com.adva.nlms.mediation.config.hn4000.mtosi.FTPHN4000;
import com.adva.nlms.mediation.config.hn4000.FlowHN4000;
import com.adva.nlms.mediation.config.hn4000.ObjectStateFieldHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet;
import com.adva.nlms.mediation.config.hn4000.UniHN4000;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNVLANPreservationTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNuniPortTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.ServiceStateTranslation;

/**
 * This class is handling both FlowPoint CTPs and CTPs MTOSI Translator.
 */
public class FlowHN4000Translator extends MtosiTranslator {
  private FlowHN4000 flow;

  public FlowHN4000Translator(FlowHN4000 flow) {
    this.flow = flow;
  }
  /**
   * To be called to generate the Lag Fragment CTP...
   * Not called yet, but the code is mostly right, missing the fudging of the namingAttributes into
   	<mdNm>containing MD name</mdNm>
	<meNm>containing ME name</meNm>
	<ftpNm>containing FTP name</ftpNm>
	<ctpNm>/lag_fragment=#number#</ctpNm>
 
   * @return
   * @throws ProcessingFailureException
   */
  public static ConnectionTerminationPointT toMtosiSimpleCTP(FTPHN4000 port, String ctpName, String lagMember, String serviceState) throws ProcessingFailureException {
	    final ObjectFactory objFactory = new ObjectFactory();
	    final ConnectionTerminationPointT connectionTerminationPoint = objFactory.createConnectionTerminationPointT();

	    // CTP Name
	    final NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributesHN4000(port);
	    //	    The #number# component will correspond to the position of the member port in the device native port list.

	    namingAttributes.setCtpNm(ctpName);
	    
	    connectionTerminationPoint.setName(objFactory.createConnectionTerminationPointTName(namingAttributes));

	    connectionTerminationPoint.setDiscoveredName(objFactory.createConnectionTerminationPointTDiscoveredName(ctpName));

	    // namingOS
	    connectionTerminationPoint.setNamingOS(objFactory.createConnectionTerminationPointTNamingOS(OSFactory.getNmsName()));

	    // source
	    final SourceT source = new SourceT();
	    source.setValue(SourceEnumT.NETWORK_EMS);
	    connectionTerminationPoint.setSource(objFactory.createConnectionTerminationPointTSource(source));

	    // resource state
	    final ResourceStateT resourceState = new ResourceStateT();
	    resourceState.setValue(ResourceStateEnumT.INSTALLED);
	    connectionTerminationPoint.setResourceState(objFactory.createConnectionTerminationPointTResourceState(resourceState));

	    // direction
	    connectionTerminationPoint.setDirection(objFactory.createConnectionTerminationPointTDirection(DirectionalityT.D_BIDIRECTIONAL));

	    // layers
	    final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

	    // -------start Layer--------
	    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_LAG_FRAGMENT);
	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_LAG_FRAGMENT,
	            LayeredParams.LrLagFragment.LAG_MEMBER_PARAM,
	            lagMember);

	    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_LAG_Fragment);
	    if (serviceState != null) {
	   LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_LAG_Fragment,
	            LayeredParams.PropHatterasLagFragment.LACP_SERVICE_STATE_PARM,
	            serviceState);
	    }

	   LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
	   LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet);
	    // -------end of Layer-------

	    connectionTerminationPoint.setTransmissionParams(objFactory.createConnectionTerminationPointTTransmissionParams(layeredParametersListT));
	    return connectionTerminationPoint;
	  }


  @Override
  public ConnectionTerminationPointT toMtosiCTP() throws ProcessingFailureException {
    final ObjectFactory objFactory = new ObjectFactory();
    final ConnectionTerminationPointT connectionTerminationPoint = objFactory.createConnectionTerminationPointT();
    final FlowSPPropertiesHN4000 properties = flow.getFlowSPProperties();
    boolean trunk = false;
    try {
			UniHN4000 uni = ((PortHN4000Ethernet) flow.getPortHN4000()).getUni();
			if (uni != null) {
				trunk = HNuniPortTypeTranslation.Trunk.getMibValue() == uni.getUniSPProperties().get(UNISPPropertiesHN4000.VI.PortType);
			}
		} catch (Exception e) {
		// System.out.println(e.toString());
		}

    // CTP Name
    final NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributes(flow);
    connectionTerminationPoint.setName(objFactory.createConnectionTerminationPointTName(namingAttributes));

    // discoveredName
    final String ctpNm = namingAttributes.getCtpNm();
    if (ctpNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
    }
    connectionTerminationPoint.setDiscoveredName(objFactory.createConnectionTerminationPointTDiscoveredName(ctpNm));

    // namingOS
    connectionTerminationPoint.setNamingOS(objFactory.createConnectionTerminationPointTNamingOS(OSFactory.getNmsName()));

    // source
    final SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    connectionTerminationPoint.setSource(objFactory.createConnectionTerminationPointTSource(source));

    // resource state
    final ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ResourceStateEnumT.INSTALLED);
    connectionTerminationPoint.setResourceState(objFactory.createConnectionTerminationPointTResourceState(resourceState));

    // direction
    connectionTerminationPoint.setDirection(objFactory.createConnectionTerminationPointTDirection(DirectionalityT.D_BIDIRECTIONAL));

    // layers
    final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
    // Empty layer indicating that this Flow Point supports Ethernet.
    // -------end of Layer-------
	// -------start PROP_HATTERAS_Ethernet layer ----------------
	LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasEthernet.ADMINISTRATION_CONTROL_PARAM,
			properties.get(FlowSPPropertiesHN4000.VI.AdminState) == null? "null" : MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, properties.get(FlowSPPropertiesHN4000.VI.AdminState)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasEthernet.SERVICE_STATE_PARAM,
			ServiceStateTranslation.getMtosiString(properties.get(FlowSPPropertiesHN4000.VI.AdminState) != null? properties.get(FlowSPPropertiesHN4000.VI.AdminState) :-1, properties.get(FlowSPPropertiesHN4000.VI.OperState) != null?properties.get(FlowSPPropertiesHN4000.VI.OperState):-1));
	Integer secondaryState = properties.get(FlowSPPropertiesHN4000.VI.ObjState);
	if (secondaryState != null) 
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasEthernet.SECONDARY_STATE,
				ObjectStateFieldHN4000.getSecondaryStateString(secondaryState));
	else 
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasEthernet.SECONDARY_STATE,
				"");
	if (trunk) {
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, 
				LayeredParams.PropHatterasEthernet.VLAN_MEMBERS_PARAM,
				String.valueOf(properties.get(FlowSPPropertiesHN4000.VS.VlanList)));
	}
	String vlanPreservation;
	if (!trunk) {
		vlanPreservation = "Enabled";
	} else {
		vlanPreservation = HNVLANPreservationTranslation.getMtosiString(properties.get(FlowSPPropertiesHN4000.VI.VlanPreservation));
	}
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, 
			LayeredParams.PropHatterasEthernet.VLAN_PRESERVATION_PARAM, 
			vlanPreservation);
	// -------end of Layer-------

	connectionTerminationPoint.setTransmissionParams(objFactory.createConnectionTerminationPointTTransmissionParams(layeredParametersListT));
    return connectionTerminationPoint;
  }
}
