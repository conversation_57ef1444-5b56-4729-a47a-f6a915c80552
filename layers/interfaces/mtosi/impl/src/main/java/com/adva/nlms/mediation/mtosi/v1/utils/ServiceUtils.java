/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.utils;

import com.adva.nlms.mediation.common.MDRequestFailedException;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.mtosi.MtosiCtrl;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidationHelper;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.WorkerFactory;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import v1.tmf854.HeaderT;
import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import jakarta.xml.ws.WebServiceContext;
import jakarta.xml.ws.handler.MessageContext;

public class ServiceUtils  implements ApplicationContextAware
{

  private static ApplicationContext applicationContext;

  public static void setIPAddress(AbstractMtosiWorker worker, WebServiceContext context) {
    MessageContext mc = context.getMessageContext();
    String remoteHost = ((jakarta.servlet.http.HttpServletRequest) mc.get(MessageContext.SERVLET_REQUEST))
            .getRemoteHost();
    worker.setIpAddress(remoteHost);
  }

  public static ProcessingFailureException createNewPFE(String exception, String reason) {
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(exception, reason);
    return new ProcessingFailureException(pfet.getReason(), pfet);
  }

  public static ProcessingFailureException createNewPFE(Holder<HeaderT> mtosiHeader, String exception, String reason) {
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(mtosiHeader, exception, reason);
    return new ProcessingFailureException(pfet.getReason(), pfet);
  }

  public static ProcessingFailureException createNewPFE(String exception, String reason, Throwable throwable) {
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(exception, reason);
    return new ProcessingFailureException(pfet.getReason(), pfet, throwable);
  }

  public static ProcessingFailureException createNewPFE(Holder<HeaderT> mtosiHeader, String exception, String reason, Throwable throwable) {
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(mtosiHeader, exception, reason);
    return new ProcessingFailureException(pfet.getReason(), pfet, throwable);
  }

  public static <T extends Throwable> void rollbackNetTransaction(Integer id, T exception, Logger LOG)
          throws T, NetTransactionException {
    LOG.warn("Exception during transaction: " + exception);
    LOG.debug(exception);
    NetTransactionManager.rollbackNetTransaction(id);
    throw exception;
  }

  public static <R, W extends AbstractMtosiWorker> R runWorker(W worker, Holder<HeaderT> mtosiHeader, String name,
                                                               WebServiceContext context, Logger LOG)
          throws ProcessingFailureException
  {
    try {
      MtosiSupportValidator validator = MtosiSupportValidationHelper.getValidator(name);
      setIPAddress(worker, context);
      worker.doWork(validator,applicationContext.getBean(MtosiCtrl.class));
      LOG.info("MTOSI operation " + name + " finished successfully.");
      //noinspection unchecked
      return (R) worker.getSuccessResponse();
    } catch(SNMPCommFailure ex) {
        if(ex.getErrMessage() != null && ex.getErrMessage().indexOf("is temporarily reserved for") > 0) {
			 ObjectInUseException oiu = new ObjectInUseException(ex.getErrMessage());
			 LOG.info("SNMPCommFailure during operation: " + oiu);
			 throw ExceptionUtils.formatDoWorkException(oiu, mtosiHeader);
        } else {
            LOG.info("SNMPCommFailure during operation: " + ex);
            throw ExceptionUtils.formatDoWorkException(ex, mtosiHeader);
        }

    } catch(MDRequestFailedException ex) {
        if(ex.getMessage() != null && ex.getMessage().indexOf("is temporarily reserved for") > 0) {
			 ObjectInUseException oiu = new ObjectInUseException(ex.getMessage());
			 LOG.info("RequestFailed during operation: " + oiu);
			 throw ExceptionUtils.formatDoWorkException(oiu, mtosiHeader);
        } else {
            LOG.info("RequestFailed during operation: " + ex);
            throw ExceptionUtils.formatDoWorkException(ex, mtosiHeader);
        }

     } catch (Exception ex) {
      LOG.info("Exception during operation: ", ex);
      throw ExceptionUtils.formatDoWorkException(ex, mtosiHeader);
    }
  }

  public static <T, R, W extends AbstractMtosiWorker> R runMethod(Class<W> theClass, T mtosiBody, Holder<HeaderT> mtosiHeader, String name,
                                                               WebServiceContext context, Logger LOG)
          throws ProcessingFailureException
  {
    LOG.info("Executing MTOSI operation " + name + "...");
    MtosiUtils.validateWorkingMode();
    MtosiSupportValidator validator = MtosiSupportValidationHelper.getValidator(name);
    W worker = WorkerFactory.createWorker(theClass, mtosiBody, mtosiHeader);
    try {
      setIPAddress(worker, context);
      worker.doWork(validator,applicationContext.getBean(MtosiCtrl.class));
      LOG.info("MTOSI operation " + name + " finished successfully.");
      //noinspection unchecked
      return (R) worker.getSuccessResponse();
    } catch(SNMPCommFailure ex) {
        if(ex.getErrMessage() != null && ex.getErrMessage().indexOf("is temporarily reserved for") > 0) {
			 ObjectInUseException oiu = new ObjectInUseException(ex.getErrMessage());
			 LOG.info("SNMPCommFailure during operation: " + oiu);
			 throw ExceptionUtils.formatDoWorkException(oiu, mtosiHeader);
        } else {
            LOG.info("SNMPCommFailure during operation: " + ex);
            throw ExceptionUtils.formatDoWorkException(ex, mtosiHeader);
        }

    } catch(MDRequestFailedException ex) {
        if(ex.getMessage() != null && ex.getMessage().indexOf("is temporarily reserved for") > 0) {
			 ObjectInUseException oiu = new ObjectInUseException(ex.getMessage());
			 LOG.info("RequestFailed during operation: " + oiu);
			 throw ExceptionUtils.formatDoWorkException(oiu, mtosiHeader);
        } else {
            LOG.info("RequestFailed during operation: " + ex);
            throw ExceptionUtils.formatDoWorkException(ex, mtosiHeader);
        }

    } catch (Exception ex) {
      LOG.info("Exception during operation: ", ex);
      throw ExceptionUtils.formatDoWorkException(ex, mtosiHeader);
    }
  }

  @Override
  public synchronized void  setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext=applicationContext;
  }
}
