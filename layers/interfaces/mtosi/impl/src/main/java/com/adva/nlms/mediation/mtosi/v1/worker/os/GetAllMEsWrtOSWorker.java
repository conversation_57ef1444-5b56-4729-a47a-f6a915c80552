/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.os;

import com.adva.nlms.mediation.config.ConfigCtrlImpl;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.ManagedElementMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import v1.tmf854.GetAllMEsWrtOSResponseT;
import v1.tmf854.GetAllMEsWrtOST;
import v1.tmf854.HeaderT;
import v1.tmf854.ManagedElementListT;
import v1.tmf854.NamingAttributesT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.Set;

/**
 * main class for the MTOSI operation:  g e t A l l M E s W r t O S
 */
public class GetAllMEsWrtOSWorker extends AbstractMtosiWorker {
  protected GetAllMEsWrtOST mtosiBody;
  protected GetAllMEsWrtOSResponseT response = new GetAllMEsWrtOSResponseT();
  protected NamingAttributesT osName;
  protected NamingAttributesT mdName;
  protected ManagedElementListT list;

  public GetAllMEsWrtOSWorker(GetAllMEsWrtOST mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getAllMEsWrtOS", "getAllMEsWrtOS", "getAllMEsWrtOSResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    osName = mtosiBody.getOsName();

    if ((osName == null) || (osName.getOsNm() == null)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.OS_NAME_MISSING);
    }

    if (!osName.getOsNm().equals(OSFactory.getNmsName())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.OS_NOT_FOUND);
    }

    if (((mdName = mtosiBody.getMdName()) == null) || (!NamingTranslationFactory.isManagementDomain(mdName))) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!mdName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MD_NOT_FOUND);
    }
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
  	//No validation required.
  }

  @Override
  protected void mediate() throws Exception {
    final Set<NetworkElement> neSet = ConfigCtrlImpl.get().getHandlers().getNeHdlr().getNetworkElements();
    list = ManagedElementMediator.nmsNeListToMtosiManagedElementList(MtosiUtils.getSupportedNE(neSet));
  }

  @Override
  protected void response() throws Exception {
    response.setMeList(list);
  }

  @Override
  public GetAllMEsWrtOSResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
