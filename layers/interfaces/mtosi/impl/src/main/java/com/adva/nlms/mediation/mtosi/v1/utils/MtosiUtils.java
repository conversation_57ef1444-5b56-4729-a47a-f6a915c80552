/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils;

import com.adva.nlms.common.InstallationState;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.redundancy.WorkMode;
import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.common.snmp.MIBFSPGE20X;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ServiceSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ServiceSPPropertiesFSP150CC;
import com.adva.nlms.mediation.config.DiscoveryState;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.mtosi.MtosiCtrlImpl;
import com.adva.nlms.mediation.mtosi.common.mtosisupport.ManagedElementTypes;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrativeSpeedRateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAcceptableFramePolicyTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAutoNegotiationTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.GE20XAutoNegotiationTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.MediaTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.TranslatableEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Node;
import v1.tmf854.EHInventoryT;
import v1.tmf854.EQInventoryT;
import v1.tmf854.FTPVendorExtensionsT;
import v1.tmf854.HolderStateT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ServiceStateEnumT;
import v1.tmf854.ServiceStateT;
import v1.tmf854.TPDataListT;
import v1.tmf854.TPDataT;
import v1.tmf854.TPVendorExtensionsT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.StringTokenizer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * collection of static utility functions 
 */
public class MtosiUtils {
private static Logger logger = LogManager.getLogger(MtosiUtils.class.getName());

	public static boolean isSameNetworkElement(NamingAttributesT aEnd, NamingAttributesT zEnd)
	{
		if (aEnd == null || zEnd == null)
		{
			return false;
		}
		String meA = aEnd.getMeNm();
		String meZ = zEnd.getMeNm();
    return meA != null && meZ != null && meA.equals(meZ);
  }

  public static boolean isSameShelf(NamingAttributesT aEnd, NamingAttributesT zEnd) {
    if (aEnd == null || zEnd == null) {
			return false;
		}
    String ptpA = aEnd.getPtpNm();
		String ptpZ = zEnd.getPtpNm();
		if (ptpA == null || ptpZ == null) {
			return false;
		}
    int slotA = NamingTranslationFactory.shelfNumberFromShelfCombo(ptpA);
    int slotZ = NamingTranslationFactory.shelfNumberFromShelfCombo(ptpZ);
    return slotA != -1 && slotZ != -1 && slotA == slotZ;
  }

  public static boolean isSameSlot(NamingAttributesT aEnd, NamingAttributesT zEnd) {
    if (aEnd == null || zEnd == null) {
			return false;
		}
    String ptpA = aEnd.getPtpNm();
		String ptpZ = zEnd.getPtpNm();
		if (ptpA == null || ptpZ == null) {
			return false;
		}
    int slotA = NamingTranslationFactory.slotNumberFromShelfCombo(ptpA);
    int slotZ = NamingTranslationFactory.slotNumberFromShelfCombo(ptpZ);
    return slotA != -1 && slotZ != -1 && slotA == slotZ;
  }

  public static boolean isMtosiSupported(NetworkElement ne) {
    return ManagedElementTypes.isManageElement(ne.getNetworkElementType()) && ne.isDiscovered();
  }

	public static boolean isInstalled(EquipmentSPProperties properties)
	{
		Integer installState = properties.get(EquipmentSPProperties.VI.InstallState);
		if (installState == null)
			return false;
		switch (installState)
		{
			case InstallationState.PRESENT:
				return true;
			case InstallationState.REINSERTED:
				return true;
			case InstallationState.REMOVED:
				return false;
			default:
				return false;
		}
	}

	public static HolderStateT getHolderState(int installState)
	{
		switch (installState)
		{
			case InstallationState.PRESENT:
				return HolderStateT.INSTALLED_AND_EXPECTED;
			case InstallationState.REINSERTED:
				return HolderStateT.INSTALLED_AND_EXPECTED;
			case InstallationState.REMOVED:
				return HolderStateT.EXPECTED_AND_NOT_INSTALLED;
			default:
				return HolderStateT.UNAVAILABLE;
		}
	}

	public static ResourceStateEnumT getResourceState(int installState)
	{
		switch (installState)
		{
			case InstallationState.PRESENT:
				return ResourceStateEnumT.INSTALLED;
			case InstallationState.REINSERTED:
				return ResourceStateEnumT.INSTALLED;
			case InstallationState.REMOVED:
				return ResourceStateEnumT.PLANNED;
			default:
				return ResourceStateEnumT.PLANNED;
		}
	}

	public static String getLayerForCCLanPort(ServiceSPPropertiesFSP150CC service)
	{
		String actualLayer = LayeredParams.LR_DSR_FAST_ETHERNET;
		final int svcIndex = service.get(ServiceSPProperties.VI.SvcIndex);
		if (svcIndex == 5)
			actualLayer = LayeredParams.LR_DSR_GIGABIT_ETHERNET; // LAN 5
		return actualLayer;
	}


	public static ServiceStateT getServiceState(Integer adminState, Integer operState)
	{
		ServiceStateT serviceState = new ServiceStateT();
		
		if (adminState == null) {
			serviceState.setValue(ServiceStateEnumT.OUT_OF_SERVICE);
			return serviceState;
		}
    switch (adminState) {
			case 1:
				
				if(operState!=null)
				{
					if(operState==2)
					{
						serviceState.setValue(ServiceStateEnumT.OUT_OF_SERVICE);
					}
					else
					{
						serviceState.setValue(ServiceStateEnumT.IN_SERVICE);
					}
					
				}
				else
				{
					serviceState.setValue(ServiceStateEnumT.IN_SERVICE);
				}
				break;
			default:
				serviceState.setValue(ServiceStateEnumT.OUT_OF_SERVICE_BY_MAINTENANCE);
		}
		return serviceState;
	}

      public static ServiceStateT getHnServiceStateFromOper(Integer operState)
		{
			ServiceStateT serviceState = new ServiceStateT();
			if (operState == null) {
				serviceState.setValue(ServiceStateEnumT.OUT_OF_SERVICE);
				return serviceState;
			}
	    switch (operState) {
				case 1: // active(1)
					serviceState.setValue(ServiceStateEnumT.IN_SERVICE);
					break;
	      case 2: // inactive(2)
					serviceState.setValue(ServiceStateEnumT.OUT_OF_SERVICE);
					break;
	      case 3: // testing(3)
					serviceState.setValue(ServiceStateEnumT.OUT_OF_SERVICE_BY_MAINTENANCE);
					break;
	      case 4: // unknown(4)
					serviceState.setValue(ServiceStateEnumT.OUT_OF_SERVICE);
					break;
	      case 5: // dormant(5)
					serviceState.setValue(ServiceStateEnumT.OUT_OF_SERVICE);
					break;
	      case 6: // notPresent(6)
	        serviceState.setValue(ServiceStateEnumT.OUT_OF_SERVICE);
					break;
	      case 7: // lowerLayerDown(7)
	          serviceState.setValue(ServiceStateEnumT.OUT_OF_SERVICE);
	  				break;
	      default:
					serviceState.setValue(ServiceStateEnumT.OUT_OF_SERVICE);
			}
			return serviceState;
		}
      
      public static ServiceStateT getHnServiceStateFromObject(Integer operState)
		{
		// Note that objectState is stored as operState	
    	ServiceStateT serviceState = new ServiceStateT();
    	if (operState == null) {
			serviceState.setValue(ServiceStateEnumT.OUT_OF_SERVICE);
			return serviceState;
		}
	    switch (operState) {
			case 2: // active
			case 3: // partial
				serviceState.setValue(ServiceStateEnumT.IN_SERVICE);
				break;
			case 4: // disabled
			case 5: // testing
			case 34: //deviceSoftwareUpgrade
			case 35: //deviceApplyConfig
			case 36: //oamLoopBackLocal
			case 37: //oamLoopBackRemote
				serviceState.setValue(ServiceStateEnumT.OUT_OF_SERVICE_BY_MAINTENANCE);
				break;
			default:
				serviceState.setValue(ServiceStateEnumT.OUT_OF_SERVICE);
			}
			return serviceState;
		}
      

  public static boolean isBaseInstanceChildOrMatch(List<NamingAttributesT> baseInstanceList,
			NamingAttributesT namingEntity)
	{
    return baseInstanceList.isEmpty() ||
            isBaseInstanceMatch(baseInstanceList, namingEntity) ||
            isBaseInstanceChild(baseInstanceList, namingEntity);
  }

	public static boolean existsEntity(MtosiAddress mtosiAddress) throws Exception
	{
		NamingAttributesT naming = mtosiAddress.getNaming();
		NetworkElement ne = mtosiAddress.getNE();

		// validate MD (Management Domain) and NE (Managed Element)
		validateMD(naming);
		validateNE(ne);

    // check for valid shelf-index
    final int shelfIndex = mtosiAddress.getShelfNumber();
    if (shelfIndex != -1) {
      final Boolean isHN4000 = (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000);
      if ((isHN4000 && ne.getMTOSIWorker().getShelf(shelfIndex) == null) ||
          (!isHN4000 && shelfIndex != 1)) {
        return false;
      }
    }

    if (mtosiAddress.isManagedElement()) {
      return true;
    }
		else if (mtosiAddress.isShelf())
		{
      // there is no equipment directly in shelves for any of devices
      return naming.getEqNm() == null;
    }
		else if (mtosiAddress.isFixedSlot())
		{
      return naming.getEqNm() == null || naming.getEqNm().equals(MtosiConstants.DEFAULT_EQUIPMENT_NAME);
    }
		else if (mtosiAddress.isSlot())
		{
      return mtosiAddress.isValidSlot() &&
              (naming.getEqNm() == null || naming.getEqNm().equals(MtosiConstants.DEFAULT_EQUIPMENT_NAME));
    }
		return false;
	}

	public static boolean isBaseInstanceChild(List<NamingAttributesT> baseInstanceList, NamingAttributesT namingEntity)
	{
		if (baseInstanceList.isEmpty())
			return true;
    for (NamingAttributesT element : baseInstanceList)
    {
      if (isChildOfNamingAttributes(element, namingEntity))
      {
        return true;
      }
    }
    return false;
	}

	public static boolean isShelfIncludedInFilter(NetworkElement ne, EquipmentSPProperties properties,
			List<NamingAttributesT> baseInstanceList)
	{
		if (baseInstanceList.isEmpty())
		{
			return true;
		}
		// so far, only need this for shelf, in particular CP dual
		NamingAttributesT namingTreeEntity = NamingTranslationFactory.createNamingAttributesHolder(ne, properties);
		NamingAttributesT namingBase = baseInstanceList.get(0);

		return namingBase.getMeNm() != null &&
            namingBase.getMeNm().equals(namingTreeEntity.getMeNm()) &&
            isSubtreeValidForEntity(namingBase, namingTreeEntity);
	}

	public static boolean isBaseInstanceMatch(List<NamingAttributesT> baseInstanceList, NamingAttributesT namingEntity)
	{
		if (baseInstanceList.isEmpty())
			return true;
    for (NamingAttributesT element : baseInstanceList)
    {
      if (isNamingAttributesMatch(element, namingEntity))
      {
        return true;
      }
    }
    return false;
	}

	public static boolean isSameString(String one, String two)
	{
		if (one == null && two == null)
			return true;
		if (one != null && two != null)
		{
      return one.equals(two);
    }
		// one is null, so false
		return false;
	}

	public static boolean isNamingAttributesMatch(NamingAttributesT namingBase, NamingAttributesT namingEntity)
	{
		// if(!isSameString(namingBase.getOsNm(), namingEntity.getOsNm()))
		// {
		// return false;
		// }
		if (!isSameString(namingBase.getMdNm(), namingEntity.getMdNm()))
		{
			return false;
		}
		if (!isSameString(namingBase.getMeNm(), namingEntity.getMeNm()))
		{
			return false;
		}
		if (!isSameString(namingBase.getEhNm(), namingEntity.getEhNm()))
		{
			return false;
		}
		if (!isSameString(namingBase.getEqNm(), namingEntity.getEqNm()))
		{
			return false;
		}
		if (!isSameString(namingBase.getPtpNm(), namingEntity.getPtpNm()))
		{
			return false;
		}
		if (!isSameString(namingBase.getCtpNm(), namingEntity.getCtpNm()))
		{
			return false;
		}
		// strings all same, so return true
		return true;
	}

	public static boolean isSubtreeValidForEntity(NamingAttributesT namingBase, NamingAttributesT namingTreeEntry)
	{
		// if /shelf=1 for namingTreeEntry, and namingBase is
		// /shelf=1/port=ACC-1, then validsubtree exists
		// if /shelf=1 for namingTreeEntry, and namingBase is
		// /shelf=2/port=ACC-1, then not a valid subtree for this
		// namingTreeEntry
		// if only me for namingBase, and anything under me for entry, then
		// valid
		String baseEh = namingBase.getEhNm(); // the baseInstance, which could
		// be shelf or sfp/psu/fan eh or
		// eq
		String baseEq = namingBase.getEqNm();
		String namingTreeEntryEh = namingTreeEntry.getEhNm(); // shelf name
		if (baseEh != null)
		{
			// check if entry is found in base
			if (baseEh.indexOf(namingTreeEntryEh) > -1)
			{
				return true;
			}
		}
		// if (baseEq != null)
		// {
		// // check if entry is found in base
		// if (baseEq.indexOf(namingTreeEntryEh) > -1)
		// {
		// return true;
		// }
		// }
		if (baseEq == null && baseEh == null && namingBase.getMeNm() != null)
		{
			// check if entry is found in base
			if (namingBase.getMeNm().equals(namingTreeEntry.getMeNm()))
			{
				return true;
			}
		}
		return false;
	}

	public static boolean isChildOfNamingAttributes(NamingAttributesT namingBase, NamingAttributesT namingChild)
	{
		// Check OS
		// if (namingChild.getOsNm() == null)
		// return false; // should have at least osNm in Child
		// if (namingBase.getOsNm() == null)
		// return false; // should have at least osNm in Base
		// if (!namingChild.getOsNm().equals(namingBase.getOsNm()))
		// {
		// return false;
		// }
		// Check MD
		if (namingChild.getMdNm() == null)
			return false; // must have at least mdNm to be child
		// what if parent is os, and child is just os and md, then parent has
		// null md
		if (namingBase.getMdNm() == null)
			return true; // matched till now, so it is a child
		if (!namingChild.getMdNm().equals(namingBase.getMdNm()))
		{
			// both have mdNm, so if not equal, then not child of base
			return false;
		}
		// Check ME
		if (namingChild.getMeNm() == null)
			return false; // must have at least meNm to be child
		// what if parent is md, and child is just os/md/me, then parent has
		// null me
		if (namingBase.getMeNm() == null)
			return true; // matched till now, so it is a child
		if (!namingChild.getMeNm().equals(namingBase.getMeNm()))
		{
			// both have meNm, so if not equal, then not child of base
			return false;
		}
		// at this point, could be ptp path or eh/eq path
		if (namingChild.getPtpNm() == null && namingChild.getEqNm() == null && namingChild.getEhNm() == null)
		{
			return false;
		}
		// has either ptpNm or eqNm or ehNm
		if (namingChild.getPtpNm() != null)
		{
			// child does have ptpNm
			if (namingBase.getPtpNm() == null)
				return true; // matched till now, so it is a child
			if (!namingChild.getPtpNm().equals(namingBase.getPtpNm()))
			{
				// both have ptpNm, so if not equal, then not child of base
				return false;
			}
			// now check CTP
			// Check ctpNm level now and if no ctp, then not child
			if (namingChild.getCtpNm() == null)
				return false;
			// child does have ctpNm
			if (namingBase.getCtpNm() == null)
				return true; // matched till now, so it is a child
			// the both have Ctp, so since thats the bottom of the chain
			// by definition we don't have a child
			return false;
		}
		// what if parent is me, and child is just os/md/me, then parent has
		// null eh
		if (namingBase.getEhNm() == null)
			return true; // matched till now, so it is a child
		// Now check ehNm
		if (namingChild.getEhNm() != null)
		{
			// if namingBase doesn't have ehNm, we still need to check eqNm
			// before concluding
			// that we are child or match
			if (!namingChild.getEhNm().equals(namingBase.getEhNm()))
			{
				// ehNm is strange, because recursive holders are rolled up
				// into
				// one ehNm, so for example, holder could be /shelf=1, or it
				// could
				// be /shelf=1/port=LAN-1
				// This means we need to compare by checking whether part of
				// string
				// is match,
				// not the whole ehNm string as with os/md/me
        String[] baseSubNames = namingBase.getEhNm().split("/");
        String[] childSubNames = namingChild.getEhNm().split("/");
        if (childSubNames.length < baseSubNames.length) {
          return false;
        }
        for (int i = 0; i < baseSubNames.length; ++i) {
          if (!baseSubNames[i].equals(childSubNames[i])) {
            return false;
          }
        }
				// base instance ehNm is found within namingChild, so based
				// on ehNm
				// we have a child of the base instance, now need to look at
				// next
				// level
			}
		}
		// getting here means if we did have ehNm in namingChild, its equal
		if (namingBase.getEqNm() == null)
			return true; // matched till now, so it is a child
		// if base has eq, then since eq is bottom of inventory chain, by
		// definition
		// we can't have a child of a base eq so return false
		return false;
	}

  public static Set<NetworkElement> getSupportedNE(Set<NetworkElement> neSet) {
    Set<NetworkElement> set = new HashSet<NetworkElement>();
    for (NetworkElement element : neSet) {
      if (isMtosiSupported(element)) {
        set.add((NetworkElement)element);
      }
    }
    return set;
  }

	public static boolean hasData(EHInventoryT ehInventory)
	{
		if (ehInventory == null)
			return false;
		if (ehInventory.getEhAttrs() != null)
		{
			return true;
		}
		if (ehInventory.getEhList() != null)
		{
			if (ehInventory.getEhList().getEhInv().size() > 0)
			{
				return true;
			}
		}
		if (ehInventory.getEhNm() != null)
		{
			return true;
		}
		if (ehInventory.getEqInv() != null)
		{
			if (hasData(ehInventory.getEqInv()))
			{
				return true;
			}
		}
		return false;
	}

	public static boolean hasData(EQInventoryT eqInventory)
	{
    return eqInventory != null &&
            (eqInventory.getEqAttrs() != null || eqInventory.getEqNm() != null);
  }

	public static boolean isInteger(String value)
	{
		try
		{
			Integer.valueOf(value);
			return true;
		}
		catch (Exception e)
		{
			return false;
		}
	}
	/**
	 * 
	 * Validate a mac address, ensuring it follows this pattern.
	 *  xx:xx:xx:xx:xx:xx  where x is a valid hex digit.
	 * @param macAddr
	 * @return
	 */
	
	public static boolean isValidMacAddress(String macAddr) {
		String[] bits = macAddr.split(":");
		if (bits.length == 6) {
			for (int i = 0; i < 6; i++) {
				if (!bits[i].matches("[0-9a-fA-F]{2}"))
					return false;
			}
			return true;
		}
		return false;
	}

	/**
	 * Validate the speed for CP Access ports.
	 * Per FNM1136 - 10 is not valid for Optical ports.
	 * @param speed
	 * @param isOptical
	 * @return
	 */
	public static boolean isValidPortSpeedCPAccess(Integer speed, boolean isOptical)
	{
    return speed == 0 || (isOptical == false && speed == 10) || speed == 100 || speed == 1000;
  }

	public static boolean isValidPortSpeedCPNetwork(Integer speed)
	{
    return speed == 0 || speed == 10 || speed == 100 || speed == 1000;
  }

	/**
	 * Actually, Hatteras uses an Enum, so 1 = 0, 2 = 10, 3 = 100 and 4 = 1000
	 * @param speed
	 * @return
	 */
	public static boolean isValidPortSpeedHNGbNetwork(int speed)
	{
    return speed == 1 || speed == 3 || speed == 4;
  }

	public static boolean isValidPortSpeedHN400Network(int speed)
	{
    return speed == 1 || speed == 2 || speed == 3;
  }


	public static boolean isValidPortSpeedCCWan(Integer speed)
	{
    // 0 is not valid - must specify proper combination now for autoneg
		// and full duples, and highest rate supported
    return speed == 10 || speed == 100 || speed == 1000;
  }

	public static boolean isValidPortSpeedCCLan(Integer speed)
	{
    return speed == 0 || speed == 10 || speed == 100 || speed == 1000;
  }


  public static boolean isValidPortSpeedCMAcc(Integer speed, boolean isGEDevice)
	{
    return speed == 0 || speed == 10 || speed == 100 || speed == 1000 || (speed == 1 && isGEDevice);
  }

  /*
	 * Returns PortSpeed MIB value which maps to input parameters Parameters not
	 * passed in are obtained from current settings
	 */
	public static int getPortSpeed(String mtosiString, String mediaType, String duplexMode, String autoNeg,
			int portSpeed)
	{
		// current enum
		AdministrativeSpeedRateTranslation currentSpeedEnum = AdministrativeSpeedRateTranslation.getEnum(portSpeed);
		// current individual values
		String currentMtosiString = currentSpeedEnum.getMtosiString();
		int currentMedia = currentSpeedEnum.getMediaTypeValue();
		String currentDuplex = currentSpeedEnum.getDuplexMode();
		String currentAutoNeg = currentSpeedEnum.getAutoNegotiation();
		// check if any changed
		if (mtosiString == null && mediaType == null && duplexMode == null && autoNeg == null)
		{
			return portSpeed;
		}
		if (mtosiString != null)
		{
			currentMtosiString = mtosiString; // new value
		}
		if (duplexMode != null)
		{
			currentDuplex = duplexMode;
		}
		if (autoNeg != null)
		{
			currentAutoNeg = autoNeg;
		}
		if (mediaType != null)
		{
      currentMedia = getMIBValue(MediaTypeTranslation.NOT_APPLICABLE, mediaType);
		}
    return AdministrativeSpeedRateTranslation.getMIBValue(currentMtosiString, currentDuplex,
        currentAutoNeg, currentMedia);
	}

  /*
	 * Returns PortSpeed MIB value which maps to input parameters Parameters not
	 * passed in are obtained from current settings
	 */
	public static int getCMPortSpeed(final int mediaType, String autoNeg, String duplexMode, String speedRate,
			int portSpeed)
	{
		// current enum
		CMAutoNegotiationTranslation autoNegEnum = CMAutoNegotiationTranslation.getEnum(portSpeed);
		// current individual values
		String currentAutoNeg = autoNegEnum.getMtosiString();
		String currentDuplex = autoNegEnum.getDuplexMode();
		String currentSpeedRate = autoNegEnum.getSpeedRate();
		// check if any changed
		if (autoNeg == null && duplexMode == null && speedRate == null)
		{
			return portSpeed;
		}
		if (autoNeg != null)
		{
			currentAutoNeg = autoNeg; // new value
		}
		if (duplexMode != null)
		{
			currentDuplex = duplexMode;
		}
		if (speedRate != null)
		{
			currentSpeedRate = speedRate;
    }
    int toReturn = CMAutoNegotiationTranslation.getMIBValue(currentAutoNeg, currentDuplex, currentSpeedRate);
    if (toReturn == CMAutoNegotiationTranslation.ENABLED.getMIBValue() &&
            mediaType == MIBFSP150CM.Facility.EthernetNetPortTable.MEDIA_TYPE_FIBER) {
      toReturn = currentDuplex.equals("Full") ? CMAutoNegotiationTranslation.ENABLED_1000MB_FULL.getMIBValue() :
              CMAutoNegotiationTranslation.ENABLED_1000MB_HALF.getMIBValue();
    }
    return toReturn;
  }

    public static int getCMPortSpeedFull(final int mediaType, String autoNeg, String duplexMode, String speedRate,
                                     int portSpeed, String speedMode){
        CMAutoNegotiationTranslation autoNegEnum = CMAutoNegotiationTranslation.getEnum(portSpeed);
        // current individual values
        String currentAutoNeg = autoNegEnum.getMtosiString();
        String currentDuplex = autoNegEnum.getDuplexMode();
        String currentSpeedRate = autoNegEnum.getSpeedRate();
        String currentSpeedMode = autoNegEnum.getSpeedMode();
        // check if any changed
        if (autoNeg == null && duplexMode == null && speedRate == null)
        {
            return portSpeed;
        }
        if (autoNeg != null)
        {
            currentAutoNeg = autoNeg; // new value
        }
        if (duplexMode != null)
        {
            currentDuplex = duplexMode;
        }
        if (speedRate != null)
        {
            currentSpeedRate = speedRate;
        }
        if(speedMode != null){
            currentSpeedMode = speedMode;
        }
        int toReturn = CMAutoNegotiationTranslation.getMIBValue(currentAutoNeg, currentDuplex, currentSpeedRate, currentSpeedMode);
        if (toReturn == CMAutoNegotiationTranslation.ENABLED.getMIBValue() &&
                mediaType == MIBFSP150CM.Facility.EthernetNetPortTable.MEDIA_TYPE_FIBER) {
            toReturn = currentDuplex.equals("Full") ? CMAutoNegotiationTranslation.ENABLED_1000MB_FULL.getMIBValue() :
                    CMAutoNegotiationTranslation.ENABLED_1000MB_HALF.getMIBValue();
        }
        return toReturn;
    }

  public static int getCardTypeFromSlotIndex(int neType, int slotIndex) {
    switch (neType) {
      // for FSP150CC-GE20x
    case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
    case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
    case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
        if (slotIndex == 1) {
          return MIBFSP150CM.Entity.SlotTable.TYPE_GENERIC_INDEX;
        }
        if (slotIndex == 4 || slotIndex == 5) {
          return MIBFSP150CM.Entity.SlotTable.TYPE_PSU_INDEX;
        }
        break;

      // for FSP150CM
      default:
        if (slotIndex < 1) {
          return -1;
        }
        if (slotIndex == 1) {
          return MIBFSP150CM.Entity.SlotTable.TYPE_SCU_INDEX;
        }
        if (slotIndex < 18) {
          return MIBFSP150CM.Entity.SlotTable.TYPE_GENERIC_INDEX;
        }
        if (slotIndex < 20) {
          return MIBFSP150CM.Entity.SlotTable.TYPE_PSU_INDEX;
        }
        if (slotIndex < 22) {
          return MIBFSP150CM.Entity.SlotTable.TYPE_FAN_INDEX;
        }
    }
    return -1;
  }


  /*
	 * Returns PortSpeed MIB value which maps to input parameters Parameters not
	 * passed in are obtained from current settings
	 */
	public static int getAFP(String taggedFramesEnabled, String untaggedFramesEnabled, int afp)
	{
		// current enum
		CMAcceptableFramePolicyTranslation afpEnum = CMAcceptableFramePolicyTranslation.getEnum(afp);
		// current individual values
		String currentTF = afpEnum.getTagged();
		String currentUF = afpEnum.getUnTagged();
		// check if any changed
		if (taggedFramesEnabled == null && untaggedFramesEnabled == null)
		{
			return afp;
		}
		if (taggedFramesEnabled != null)
		{
			currentTF = taggedFramesEnabled; // new value
		}
		if (untaggedFramesEnabled != null)
		{
			currentUF = untaggedFramesEnabled;
		}
		return CMAcceptableFramePolicyTranslation.getMIBValue(currentTF, currentUF);
	}

  public static boolean isValidWriteFTP_CPProtection(Integer prot)
	{
    return prot == 1 || prot == 2 || prot == 5 || prot == 6 || prot == 7;
  }

	public static List<String> getTokens(String input, String separator)
	{
		if (input.length() == 0)
			return new ArrayList<String>();
		StringTokenizer st = new StringTokenizer(input, separator);
		List<String> list = new ArrayList<String>();
		while (st.hasMoreTokens())
		{
			String nextValue = st.nextToken();
			list.add(nextValue);
		}
		if (list.size() < 1)
		{
			list.add(input);
		}
		return list;
	}

	public static List<String> getLoopbackList(String vidString)
	{
    return getTokens(vidString, ",");
	}
	
	public static void validateNE(NetworkElement ne) throws ProcessingFailureException
	{
		if (ne == null)
		{
			throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.ME_NOT_FOUND);
		}
		if(!ne.isDiscovered())
		{
			throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.NOT_DISCOVERED);
		}
		if (!(ManagedElementTypes.isManageElement(ne.getNetworkElementType())))
		{
			throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.ME_NOT_SUPPORTED);
		}
	}

	public static boolean existsMDOrMEEntity(String mdName,String meName,NetworkElement ne) throws ProcessingFailureException {
		// validate NE (Managed Element)
		if(mdName==null)
		{
			throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.MD_NOT_FOUND);
		}

		if(meName==null)
		{
			throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.ME_NOT_FOUND);
		}

		validateNE(ne);
		validateMD(mdName);

		return true;
	}

	public static void validateMD(String mdName) throws ProcessingFailureException {
		final String theMdName = OSFactory.getMDNm();
		if (!mdName.equals(theMdName)) {
			throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
					MtosiErrorConstants.MD_NOT_FOUND);
		}
	}

  /**
   * This method checks if the given object is present.
   * @param element      The object to be checked
   * @param elementName  The name of the object (used for error message)
   * @param <T>          The class-type of the object
   * @return             The object if the check was successful
   * @throws ProcessingFailureException
   */
  public static <T> T checkElement(T element, String elementName)  throws ProcessingFailureException
  {
    // check for null
    if ((element == null) ||
        (element instanceof JAXBElement && ((JAXBElement) element).getValue() == null)) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, "The " + elementName + " has not been specified.");
    }
    // check for isEmpty
    Object tmpElement = (element instanceof JAXBElement) ? ((JAXBElement) element).getValue() : element;
    if ((tmpElement instanceof String && ((String)tmpElement).isEmpty()) ||
        (tmpElement instanceof Collection && ((Collection)tmpElement).isEmpty())) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, "The " + elementName + " is invalid.");
    }
    return element;
  }

	/**
	 * Confirm that all naming attributes in the array have the correct md name.
	 * Enhanced to check multiple namings with one call to OSFactory.getMDNm()
	 * @param namingArray array of naming attributes
	 * @throws ProcessingFailureException when something goes wrong
	 */
	public static void validateMD(NamingAttributesT... namingArray) throws ProcessingFailureException
	{
		if (namingArray != null) {
			final String theMdName = OSFactory.getMDNm();
			for (NamingAttributesT naming : namingArray) 
			{
				if (NamingTranslationFactory.isManagementDomain(naming)) 
				{
					/**
					 * The ManagementDomain name must exist.
					 */
					final String mdName = naming.getMdNm();
					if (!mdName.equals(theMdName)) 
					{
						ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
										ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.MD_NOT_FOUND);
						throw new ProcessingFailureException(pfet.getReason(),
								pfet);
					}
				} 
				else 
				{
					ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
									ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.MD_NAME_MISSING);
					throw new ProcessingFailureException(pfet.getReason(), pfet);
				}
			}
		} else 
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
							ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.MD_NAME_MISSING);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
	}
	
	/**
	 * Validate that we are talking to the Master server.
	 * @throws ProcessingFailureException if we are NOT talking to the master.
	 */

	public static void validateWorkingMode() throws ProcessingFailureException {
		WorkMode workingMode = MtosiCtrlImpl.getServerCtrlImpl().getWorkMode();
		if (workingMode != WorkMode.MASTER) {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
					ExceptionUtils.EXCPT_UNABLE_TO_COMPLY, MtosiErrorConstants.STANDBY_SERVER_OPERATION_NOT_SUPPORTED);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
			
		}
	}
	
	public static List<TPDataT> getTpsToAdd(TPVendorExtensionsT extensions) throws ProcessingFailureException
	{
		return getTpsToHandle(extensions.getAny(), "tpsToAdd");
	}
	
	public static List<TPDataT> getTpsToModify(FTPVendorExtensionsT extensions) throws ProcessingFailureException
	{
		return getTpsToHandle(extensions.getAny(), "tpsToModify");
	}
	
	public static int countLagFragmentCTPs(List<TPDataT> list)
	{
		int count = 0;
		for (Iterator iterator = list.iterator(); iterator.hasNext();) {
			TPDataT dataT = (TPDataT) iterator.next();
			NamingAttributesT nextName = dataT.getTpName();
			if(NamingTranslationFactory.isCtpBonding(nextName))
			{
				count++;
			}
		}
		return count;
	}
	
	private static List<TPDataT> getTpsToHandle(List list, String name) throws ProcessingFailureException
	{
		JAXBContext jc = null;
		Unmarshaller u = null;

		try
		{
			jc = JAXBContext.newInstance("v1.tmf854");
			u = jc.createUnmarshaller();

		}
		catch (JAXBException ex)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
					MtosiErrorConstants.JAXB_MARSHALLING_PROBLEM);
			ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
			throw pfe;
		}
		for (Iterator iter = list.iterator(); iter.hasNext();)
		{
			Node element = (Node) iter.next();
			String elementName = element.getLocalName();
			if(elementName.equals(name))
			{
				TPDataListT tpDataListT;
				try
				{
					JAXBElement<TPDataListT> root = u.unmarshal(element, TPDataListT.class);
					tpDataListT = root.getValue();
					return tpDataListT.getTpData();
				}
				catch (JAXBException ex)
				{
					// not right element so skip it
				}
			}
		}
		return new ArrayList<TPDataT>();
	}
	
	public static List<NamingAttributesT> getTpsToRemove(TPVendorExtensionsT extensions) throws ProcessingFailureException
	{
		List list = extensions.getAny();
		
		JAXBContext jc = null;
		Unmarshaller u = null;

		try
		{
			jc = JAXBContext.newInstance("v1.tmf854");
			u = jc.createUnmarshaller();

		}
		catch (JAXBException ex)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
					MtosiErrorConstants.JAXB_MARSHALLING_PROBLEM);
			ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
			throw pfe;
		}
		for (Iterator iter = list.iterator(); iter.hasNext();)
		{
			Node element = (Node) iter.next();
			String elementName = element.getLocalName();
			if(elementName.equals("tpsToRemove"))
			{
				NamingAttributesListT namingAttributesListT;
				try
				{
					JAXBElement<NamingAttributesListT> root = u.unmarshal(element, NamingAttributesListT.class);
					namingAttributesListT = root.getValue();
					return namingAttributesListT.getName();
				}
				catch (JAXBException ex)
				{
					// not right element so skip it
				}
			}
		}
		return new ArrayList<NamingAttributesT>();
	}

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static <E extends TranslatableEnum> String getMtosiString(final E translation, final int mibValue) {
    for (E enumValue : ((Class<E>)translation.getClass()).getEnumConstants()) {
      if (mibValue == enumValue.getMIBValue()) {
        return enumValue.getMtosiString();
      }
    }
    return translation.getMtosiString();
  }
  /**
   * Returns the string representation used in MTOSI layer.
   * @param mtosiString  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static <E extends TranslatableEnum> int getMIBValue (final E translation, final String mtosiString) {
    for (E enumValue : ((Class<E>)translation.getClass()).getEnumConstants()) {
      if (mtosiString.equals(enumValue.getMtosiString())) {
        return enumValue.getMIBValue();
      }
    }
    return translation.getMIBValue();
    
  }



  /**
   * returns one int value in the given range if found in the str based on the regex
   * @param str    input string
   * @param regex  regular expression
   * @param min    lower bound
   * @param max    upper bound
   * @param error  error text for the exception 
   * @return       group (string)
   */
  public static int getRegexGroupInt(String str, String regex, int min, int max, String error)
     throws ProcessingFailureException
  {
    if (str != null && regex != null) {
      Matcher matcher = Pattern.compile(regex).matcher(str);
      if (matcher.find() && matcher.groupCount() == 1) {
        try {
          int value = Integer.parseInt(matcher.group(1));
          if (value >= min && value <= max) {
            return value;
          }
        } catch (NumberFormatException nfe) {
          // throw ProcessingFailureException below
        }
      }
    }

    throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, error);
  }

  /**
   * parses the given string and returns a int-value.
   * Throws a PFE with the given reason if the parsing failed
   * @param str        input string to be parsed
   * @param reason     reason for PFE
   * @return parsed int value
   * @throws ProcessingFailureException
   */
  public static int validateIntParameter(String str, String reason)
    throws ProcessingFailureException
 {
   try {
     return Integer.parseInt(str);
   } catch (NumberFormatException nfe) {
     throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, reason, nfe);
   }
 }


  /**
   * checks if the given string matches any of the given string-enums.
   * Throws a PFE with the given reason if the check failed
   * @param str        input string to be checked
   * @param strEnums   string-enums
   * @param reason     reason for PFE
   * @return index of the found enum
   * @throws ProcessingFailureException
   */
  public static int validateEnumParameter(String str, List<String> strEnums, String reason)
    throws ProcessingFailureException
 {
   for (int idx=0; idx < strEnums.size(); idx++) {
     if (strEnums.get(idx).equals(str))
       return idx;
   }
   throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, reason);
 }

  /**
   * Validation method to check this statement: If SyncEAdministrationControl is Enabled, then a value of 10 is not permitted.
   * The value of 10, refers to the portSpeed
   * @param newSyncEAdminControl
   * @param oldSyncEAdmin
   * @param newPortSpeed - GE20XAutoNegotiationTranslation enumerated value if the value is changing
   * @param oldPortSpeed - GE20XAutoNegotiationTranslation enumerated value of the old port speed
   * @throws ProcessingFailureException
   */
	public static void validateSyncEPortSpeed(String newSyncEAdminControl, Integer oldSyncEAdmin, Integer newPortSpeed, Integer oldPortSpeed)
			throws ProcessingFailureException {
		Integer theSpeed = newPortSpeed == null ? oldPortSpeed : newPortSpeed;
		if (theSpeed == GE20XAutoNegotiationTranslation.DISABLED_10MB_FULL.getMIBValue() ||
			theSpeed == GE20XAutoNegotiationTranslation.DISABLED_10MB_HALF.getMIBValue() ||
			theSpeed == GE20XAutoNegotiationTranslation.ENABLED_10MB_FULL.getMIBValue() ||
			theSpeed == GE20XAutoNegotiationTranslation.ENABLED_10MB_HALF.getMIBValue() ) {
			// The Speed is 10... 
			if (newSyncEAdminControl == null) {
				if (oldSyncEAdmin == CMAdministrationControlTranslation.IN_SERVICE.getMIBValue()) {
					ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
							MtosiErrorConstants.SYNCE_ADMIN_CONTROL_WITH_INVALID_SPEED);
					ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
					throw pfe;

				}
			} else if (newSyncEAdminControl.equals(CMAdministrationControlTranslation.IN_SERVICE.getMtosiString())) {
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
						MtosiErrorConstants.SYNCE_ADMIN_CONTROL_WITH_INVALID_SPEED);
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
				
			}
		}
	}
	public static String getGE20XPsuType(String modelName) {
		if (modelName != null && modelName.matches(MIBFSPGE20X.MODEL_NAME_FSP_GE20X_PSU_DC24_REGEX)) {
			return MtosiConstants.EQUIPMENT_PSU_DC24;
		} else {
			return MtosiConstants.EQUIPMENT_PSU_DC;
		}
	}

  public enum CtpPorts {
    ACC(0, "ACC(-[456])?"),
    NET(1, "NET-[12]");

    //------------------------------------------------------------------------------------------------------------------
    private final int portType;
    private final String mtosiString;

    //------------------------------------------------------------------------------------------------------------------

    /**
     * Constructor.
     *
     * @param portType    0 for ACC, 1 for NET
     * @param mtosiString The string representation used in MTOSI naming.
     */
    private CtpPorts(final int portType, final String mtosiString) {
      this.portType = portType;
      this.mtosiString = mtosiString;
    }
  }

  public static int getPortTypeTunnel(String portName) throws ProcessingFailureException {
    portName=(portName.trim()).split("-")[0];
    if (portName==null)  return -1;
    switch (portName) {
      case MtosiConstants.PORT_ACC:
        return 1;
      case MtosiConstants.PORT_NET:
        return 0;
    }
    return -1;
  }

  /**
   * It seems that the only accepted ACC ports are 4,5 and 6
   * and NET ports 1,2
   * @param portName
   * @return
   * @throws ProcessingFailureException
   */
  public static int getPortIndexTunnel(String portName) {
    String[] portNameSplit = (portName.trim()).split("-");
    if (portNameSplit.length < 2) {
      switch (portNameSplit[0]) {
        case MtosiConstants.PORT_ACC:
          return 1;
        case MtosiConstants.PORT_NET:
          return -1;
      }
    }
    int port = Integer.parseInt(portNameSplit[1]);
    if (port>0 && port<16) return port;
    return -1;
  }

  /**
   * Used in mtosi notifications to see if the NE has been successfully discovered
   * @param discoveryState
   * @return
   */
  public static boolean isCompleted(int discoveryState) {
    DiscoveryState ds = DiscoveryState.valueOf(discoveryState);
    switch (ds) {
      case COMPLETED_SUCCESS:
      case COMPLETED_WITH_ANOMALY:
      case COMPLETED_WITH_EXCEPTION:
        return true;
      default:
        return false;
    }
  }

  /**
   * Get the GUI string for the discoveryState
   * @param discoveryState
   * @return
   */
  public static String getDiscoveryStateString(int discoveryState){
     return DiscoveryState.valueOf(discoveryState).getGUIString();
  }

	public static <T extends ManagedObjectAttr> DTO setRefreshedDTOs(List<DTO<T>> refreshedDTOs,
																																	 DTO dto) throws ProcessingFailureException {
		for(DTO refreshed : refreshedDTOs){
//      if(refreshed==null || refreshed.getAttributesGroupClass() == null || refreshed.getValue(ManagedObjectAttr.ENTITY_INDEX) == null ){
//        logger.error("Refreshed DTO error:\n" +
//            "\t Refreshed DTO is " + (refreshed==null ? "null " : "not null") +
//            "\t Refreshed DTO class attributes is " +(refreshed==null ? "" :refreshed.getAttributesGroupClass()) +
//            "\t Refreshed DTO entity index is " + (refreshed==null ? "" : refreshed.getValue(ManagedObjectAttr.ENTITY_INDEX))
//            );
//      }
			try {
				if (dto != null && refreshed.getAttributesGroupClass().equals(dto.getAttributesGroupClass()) &&
						refreshed.getValue(ManagedObjectAttr.ENTITY_INDEX).equals(dto.getValue(ManagedObjectAttr.ENTITY_INDEX))) {
					return refreshed;
				}
			}catch(NullPointerException ex){
				logger.error("Error while retrieving refreshed object "+ex.getMessage());
				throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INTERNAL_ERROR,
						"Operation failure due to polling/event synchronization. Adding or increasing delay threshold between create/delete operations might resolve this issue.");
			}
		}
		return null;
	}

}
