/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi;

import com.adva.nlms.mediation.common.serviceProvisioning.PortHN40002BpmeSPProperties;
import com.adva.nlms.mediation.config.hn4000.PortHN40002Bpme;

public class PMEPortPair
{
	private PortHN40002Bpme port=null;
	private PortHN40002BpmeSPProperties props = null;
	
	public PMEPortPair(PortHN40002Bpme port, PortHN40002BpmeSPProperties props)
	{
		this.port = port;
		this.props = props;
	}

	public PortHN40002Bpme getPort()
	{
		return port;
	}

	public PortHN40002BpmeSPProperties getProps()
	{
		return props;
	}
	
	
	
	
}
