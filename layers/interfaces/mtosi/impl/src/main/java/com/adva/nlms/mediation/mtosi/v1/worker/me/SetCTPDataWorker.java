/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.Module;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3Impl;
import com.adva.nlms.mediation.config.f3.mtosi.NetworkElementF3MTOSIOperations;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import v1.tmf854.ConnectionTerminationPointT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.TPDataT;
import v1.tmf854.TerminationPointT;
import v1.tmf854ext.adva.SetTPDataResponseT;

import jakarta.xml.ws.Holder;


public class SetCTPDataWorker extends SetTPDataWorker {
  private MTOSIFlowF3 flowCM;

  public SetCTPDataWorker (Holder<HeaderT> mtosiHeader, TPDataT tpInfo, NamingAttributesT namingAttributes, NetworkElement ne) {
    super(mtosiHeader, tpInfo, namingAttributes, ne);
  }

  @Override
  protected void parse () throws Exception {
    // empty method
  }

  @Override
  protected void mediate() throws Exception {
    switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
          flowCM = ManagedElementFactory.getCMFlow(namingAttributes);
          if(flowCM==null) {
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
                    "The specified CTP was not found.");
          }
          mediateGE_CTP();
          break;
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
        flowCM = ManagedElementFactory.getCMFlow(namingAttributes);
        if(flowCM==null) {
          throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
                  "The specified CTP was not found.");
        }
        mediateCM_CTP();
        break;
      default:
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                "The specified ME is not supported.");
    }
  }


  private void mediateCM_CTP() throws Exception {
	    FlowSPPropertiesFSP150CM newFlowProps = null;
	    boolean isNteModule = isNteModule();
	    // todo: CFMCCMSPPropertiesFSP150CM newCfmProps = null;
	    if (MtosiTPMediator.hasCMFlowProperties(tpInfo,isNteModule,false)) {
	      newFlowProps = MtosiTPMediator.mtosiTPDataTToCM_CTPProperties(tpInfo, flowCM,isNteModule);
	    }
	    /* todo: if (MtosiTPMediator.hasCfmProperties(tpInfo))
	      {
	        newCfmProps = MtosiTPMediator.mtosiTPDataTToCC_CFMProperties(tpInfo, flow);
	      }*/
	    transact(flowCM, newFlowProps /*, null*/);
	  }

  private boolean isNteModule() {
    FlowSPPropertiesFSP150CM props = flowCM.getFlowSPProperties();
    int shelfIndex = props.get(FlowSPPropertiesFSP150CM.VI.ShelfIndex);
    int slotIndex = props.get(FlowSPPropertiesFSP150CM.VI.SlotIndex);
    Module module = ((NetworkElementF3MTOSIOperations) ((NetworkElement)flowCM.getPortFSP150CMAcc().getNE()).getMTOSIWorker()).getModule(shelfIndex, slotIndex);
    if (module instanceof NteF3Impl)
      return true;
    return false;
  }

  private void mediateGE_CTP() throws Exception {
	    FlowSPPropertiesFSP150CM newFlowProps = null;
//	    CFMCCMSPPropertiesFSP150CC newCfmProps = null;
	    if (MtosiTPMediator.hasGEFlowProperties(tpInfo)) {
	      newFlowProps = MtosiTPMediator.mtosiTPDataTToGE_CTPProperties(tpInfo, flowCM);
	    }
//	    if (MtosiTPMediator.hasCfmProperties(tpInfo))
//	      {
//	        newCfmProps = MtosiTPMediator.mtosiTPDataTToCC_CFMProperties(tpInfo, false);
//	      }
	    transact(flowCM, newFlowProps /*, newCfmProps*/ );
	  }


  private void transact(MTOSIFlowF3 flow, FlowSPPropertiesFSP150CM props /*, CFMCCMSPPropertiesFSP150CC cfmProps*/)
          throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "SetTPDataWorker");
    try {
      if(props!=null) {
        logSecurity(ne, SystemAction.ModifyNetwork, flow.getMtosiName());
        flow.setFlowSettings(props);
      }
//      if (cfmProps != null) {
//				final StringBuilder stringBuffer = new StringBuilder(flow.getMtosiName());
//				stringBuffer.append(" (CFM)");
//				logSecurity(ne, SystemAction.ModifyNetwork, stringBuffer.toString());
//		    	cfmProps.set(CFMCCMSPPropertiesFSP150CC.VB.GEDeviceType, Boolean.TRUE);
//				flow.setCFMCCMSettings(cfmProps);
//	 }

      NetTransactionManager.commitNetTransaction(id);
    } catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      NetTransactionManager.ensureEnd(id);
    }
  }

  @Override
  protected void response() throws Exception {
    ObjectFactory objectFactory = new ObjectFactory();
    TerminationPointT tp = objectFactory.createTerminationPointT();
    ConnectionTerminationPointT connectionTerminationPointT = null;
    if (flowCM != null) {
      connectionTerminationPointT = new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(flowCM).toMtosiCTP();
//      connectionTerminationPointT = flowCM.getMtosiTranslator().toMtosiCTP();
    } else {
      assert false: "Both CC and CM flows cannot be null!";
    }

    tp.setCtp(connectionTerminationPointT);
    response.setModifiedTP(tp);
  }

  @Override
  public SetTPDataResponseT getSuccessResponse()
  {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}