/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;


public enum CFMCreationMhfControlTranslation implements TranslatableEnum {
  NONE            (1, "None"),
  DEFAULT         (2, "Default"),
  EXPLICIT        (3, "Explicit"),
  DEFER           (4, "Defer"),
  NOT_APPLICABLE  (5, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private CFMCreationMhfControlTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }

  public static int getMibValue(String value){
    if (value == null) return  CFMCreationMhfControlTranslation.NOT_APPLICABLE.getMIBValue();
    for(CFMCreationMhfControlTranslation control : values()){
      if(control.getMtosiString().equals(value)){
        return control.getMIBValue();
      }
    }
    return CFMCreationMhfControlTranslation.NOT_APPLICABLE.getMIBValue();
  }

  public static String getMtosiString(int value){
    if (value < 1) return  CFMCreationMhfControlTranslation.NOT_APPLICABLE.getMtosiString();
    for(CFMCreationMhfControlTranslation control : values()){
      if(control.getMIBValue() == value){
        return control.getMtosiString();
      }
    }
    return CFMCreationMhfControlTranslation.NOT_APPLICABLE.getMtosiString();
  }
}