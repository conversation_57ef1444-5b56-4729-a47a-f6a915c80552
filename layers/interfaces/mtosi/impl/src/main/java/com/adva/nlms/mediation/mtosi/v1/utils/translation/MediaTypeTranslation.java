/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum MediaTypeTranslation implements TranslatableEnum {
  NOT_APPLICABLE (0, "n/a"),
  COPPER         (1, "Copper"),
  FIBER          (2, "Fiber"),
  COPPERSFP      (3, "CopperSFP");

  private final int    mibValue;
  private final String mtosiString;

  private MediaTypeTranslation (final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}