/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.common.snmp.f3.LinkLossFwdTriggerTypes;
import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;

public class LinkLossFwdTriggerTypesHelper {

  /**
   * Returns the byte array representing given string value.
   * @param mtosiString String value to parse.
   * @return byte array
   * @throws ProcessingFailureException when string value is invalid
   */
  public static byte[] getMIBValue (final String mtosiString) throws ProcessingFailureException {
    try {
      return LinkLossFwdTriggerTypes.getMibValue(mtosiString);
    } catch (IllegalArgumentException e) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
              ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageIllegal(
              LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_TRIGGER_TYPES_PARAM));
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }
}
