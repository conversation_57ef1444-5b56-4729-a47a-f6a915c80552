/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;


import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.HeaderUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.inventory.GetInventoryWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.InventoryRetrieval;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.annotation.Resource;
import jakarta.xml.ws.WebServiceContext;


/**
 * This class was generated by the Celtix 1.1-SNAPSHOT
 * Fri Dec 22 10:48:24 EST 2006
 * Generated source version: 1.1-SNAPSHOT
 *
 */

@jakarta.jws.WebService(name = "InventoryRetrieval", serviceName = "ConfigurationService", portName = "InventoryRetrievalHttp", targetNamespace = "tmf854.v1.ws")

public class InventoryRetrievalImpl implements InventoryRetrieval {
  Logger LOG = LogManager.getLogger(this.getClass().getName());
  @Resource
  private WebServiceContext context;

  /* (non-Javadoc)
  * @see ws.v1.tmf854.InventoryRetrieval#getInventory(v1.tmf854.GetInventoryT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetInventoryResponseT getInventory(
          v1.tmf854.GetInventoryT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetInventoryWorker.class, mtosiBody, mtosiHeader,
            "getInventory", context, LOG);
  }


  @Override
  public v1.tmf854.GetInventoryResponseT getInventory(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetInventoryT mtosiBody) throws ProcessingFailureException {
    return getInventory(mtosiBody, mtosiHeader);
  }


  /* (non-Javadoc)
  * @see ws.v1.tmf854.InventoryRetrieval#getInventoryIterator(v1.tmf854.GetInventoryIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetInventoryResponseT getInventoryIterator(
          v1.tmf854.GetInventoryIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getInventoryIterator", "getInventoryResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }


  @Override
  public v1.tmf854.GetInventoryResponseT getInventoryIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetInventoryIteratorT mtosiBody) throws ProcessingFailureException {
    return getInventoryIterator(mtosiBody, mtosiHeader);
  }

}
