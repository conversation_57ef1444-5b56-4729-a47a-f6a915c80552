/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */
package com.adva.nlms.mediation.mtosi.v2.utils.facility.continuity;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.FlowPointNetF3Attr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.dto.attr.QOSNetFlowPointPolicerF3Attr;

public class FlowPointNetCont extends BaseFlowForContinuityTest<FlowPointNetF3Attr> {

  public FlowPointNetCont(int neId, DTO<FlowPointNetF3Attr> flowPointDTO) {
    super(neId, flowPointDTO);
  }

  @Override
  public EntityIndex getEntityIndex() {
    return flowPointDTO.getValue(FlowPointNetF3Attr.ENTITY_INDEX);
  }

  @Override
  public int getCtagControl() {
    return flowPointDTO.getValue(FlowPointNetF3Attr.CTAG_CONTROL);
  }

  @Override
  public int getStagControl() {
    return flowPointDTO.getValue(FlowPointNetF3Attr.STAG_CONTROL);
  }

  @Override
  public String getPolicerSuffix() {
    return "";
  }

  @Override
  public <Y extends ManagedObjectAttr> Class<Y> getPolicerClass(){
    return (Class<Y>)QOSNetFlowPointPolicerF3Attr.class;
  }

  @Override
  public <Y extends ManagedObjectAttr> Long getCirValue(DTO<Y> dto ){
    return ((DTO<QOSNetFlowPointPolicerF3Attr>)dto).getValue(QOSNetFlowPointPolicerF3Attr.CIR_COMPOSITE);
  }
}
