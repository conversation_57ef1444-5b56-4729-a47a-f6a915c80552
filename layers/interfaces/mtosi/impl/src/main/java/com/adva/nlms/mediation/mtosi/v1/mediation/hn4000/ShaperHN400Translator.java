/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.hn4000;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import v1.tmf854ext.adva.TCProfileT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.common.serviceProvisioning.ShaperSPPropertiesHN4000;
import com.adva.nlms.mediation.config.hn4000.ShaperHN4000;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;

/**
 * This class is a Hatteras shaper MTOSI Translator.
 */
public class ShaperHN400Translator extends MtosiTranslator {
	private ShaperHN4000 shaper;

	public ShaperHN400Translator(ShaperHN4000 shaper) {
		this.shaper = shaper;
	}

	@Override
  public TCProfileT toMtosiTCProfile() throws ProcessingFailureException {
		ShaperSPPropertiesHN4000 props = shaper.getSPProperties();
		//Possibly not the perfect place to handle this, but this way I only need to load the properties once.
		//Per email from Larry, if the EgressCIR is 0, then we should NOT return a Shaper.
		//I am assuming at this time that an empty response it preferable to a PFE.
    //artf17840905/02/2015  Allow responses to be retrieved when CIR == 0 per Larry suggestion.
//		if (props.get(ShaperSPPropertiesHN4000.VL.EgCIR) == 0){
//			return null;
//		}
		final v1.tmf854ext.adva.ObjectFactory objFactoryEx = new v1.tmf854ext.adva.ObjectFactory();
		final ObjectFactory objFactory = new ObjectFactory();
		final TCProfileT tcProfileT = objFactoryEx.createTCProfileT();

		// TCProfile Name
		final NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributes(shaper, MtosiConstants.EG_SHAPER_TEXT);
		tcProfileT.setName(objFactoryEx.createTCProfileTName(namingAttributes));

		// discoveredName
		final String tcpNm = namingAttributes.getTcpNm();
		if (tcpNm == null) {
			throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID);
		}
		tcProfileT.setDiscoveredName(objFactoryEx.createTCProfileTDiscoveredName(tcpNm));

		// namingOS
		tcProfileT.setNamingOS(objFactoryEx.createTCProfileTNamingOS(OSFactory.getNmsName()));

		// source
		final SourceT source = new SourceT();
		source.setValue(SourceEnumT.NETWORK_EMS);
		tcProfileT.setSource(objFactoryEx.createTCProfileTSource(source));

		// layers
		final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

		// -------start Layer--------
		LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_Shaper);
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_Shaper,
				LayeredParams.PropHatterasEthernetShaper.EGRESS_CIR_PARAM, String.valueOf(props.get(ShaperSPPropertiesHN4000.VL.EgCIR)));

		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_Shaper,
				LayeredParams.PropHatterasEthernetShaper.EGRESS_CIR_QUEUE_PARAM, String.valueOf(props.get(ShaperSPPropertiesHN4000.VL.EgCIRque)));

		// -------end of Layer-------

		tcProfileT.setTransmissionParams(objFactoryEx.createTCProfileTTransmissionParams(layeredParametersListT));
		return tcProfileT;
	}
}
