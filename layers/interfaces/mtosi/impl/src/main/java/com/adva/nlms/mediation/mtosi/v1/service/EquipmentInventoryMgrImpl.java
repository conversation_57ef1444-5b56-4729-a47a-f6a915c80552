/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;


import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.HeaderUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.equipment.GetAllEquipmentNamesWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.equipment.GetAllEquipmentWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.equipment.GetContainedEquipmentWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.equipment.GetEquipmentWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.EquipmentInventoryMgr;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.annotation.Resource;
import jakarta.xml.ws.WebServiceContext;

/**
 * This class was generated by the Celtix 1.1-SNAPSHOT Fri Dec 22 10:48:24 EST
 * 2006 Generated source version: 1.1-SNAPSHOT
 *
 */
@jakarta.jws.WebService(name = "EquipmentInventoryMgr", serviceName = "ConfigurationService", portName = "EquipmentInventoryMgrHttp", targetNamespace = "tmf854.v1.ws")
public class EquipmentInventoryMgrImpl implements EquipmentInventoryMgr {
  Logger LOG = LogManager.getLogger(this.getClass().getName());
  @Resource
  private WebServiceContext context;

  /*
    * (non-Javadoc)
    *
    * @see ws.v1.tmf854.EquipmentInventoryMgr#getAllEquipmentNames(v1.tmf854.GetAllEquipmentNamesT
    *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
    */
  public v1.tmf854.GetAllObjectNamesResponseT getAllEquipmentNames(v1.tmf854.GetAllEquipmentNamesT mtosiBody,
                                                                   jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllEquipmentNamesWorker.class, mtosiBody, mtosiHeader,
            "getAllEquipmentNames", context, LOG);
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllEquipmentNames(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                   v1.tmf854.GetAllEquipmentNamesT mtosiBody) throws ProcessingFailureException {
    return getAllEquipmentNames(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.EquipmentInventoryMgr#getAllEquipmentNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getAllEquipmentNamesIterator(
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllEquipmentNamesIterator", "getAllEquipmentNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllEquipmentNamesIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetAllObjectNamesIteratorT mtosiBody)
          throws ProcessingFailureException {
    return getAllEquipmentNamesIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.EquipmentInventoryMgr#getAllEquipment(v1.tmf854.GetAllEquipmentT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllEquipmentResponseT getAllEquipment(v1.tmf854.GetAllEquipmentT mtosiBody,
                                                            jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllEquipmentWorker.class, mtosiBody, mtosiHeader,
            "getAllEquipment", context, LOG);
  }


  @Override
  public v1.tmf854.GetAllEquipmentResponseT getAllEquipment(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                            v1.tmf854.GetAllEquipmentT mtosiBody) throws ProcessingFailureException {
    return getAllEquipment(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.EquipmentInventoryMgr#getAllEquipmentIterator(v1.tmf854.GetEquipmentOrHolderIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllEquipmentResponseT getAllEquipmentIterator(
          v1.tmf854.GetEquipmentOrHolderIteratorT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllEquipmentIterator", "getAllEquipmentResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllEquipmentResponseT getAllEquipmentIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetEquipmentOrHolderIteratorT mtosiBody)
          throws ProcessingFailureException {
    return getAllEquipmentIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.EquipmentInventoryMgr#getAllSupportedPTPNames(v1.tmf854.GetAllSupportedPTPNamesT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getAllSupportedPTPNames(v1.tmf854.GetAllSupportedPTPNamesT mtosiBody,
                                                                      jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSupportedPTPNames", "getAllSupportedPTPNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllSupportedPTPNames(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                      v1.tmf854.GetAllSupportedPTPNamesT mtosiBody) throws ProcessingFailureException {
    return getAllSupportedPTPNames (mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.EquipmentInventoryMgr#getAllSupportedPTPNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getAllSupportedPTPNamesIterator(
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSupportedPTPNamesIterator", "getAllSupportedPTPNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllSupportedPTPNamesIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetAllObjectNamesIteratorT mtosiBody)
          throws ProcessingFailureException {
    return getAllSupportedPTPNamesIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.EquipmentInventoryMgr#getAllSupportedPTPs(v1.tmf854.GetAllSupportedPTPsT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllSupportedPTPsResponseT getAllSupportedPTPs(v1.tmf854.GetAllSupportedPTPsT mtosiBody,
                                                                    jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSupportedPTPs", "getAllSupportedPTPsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllSupportedPTPsResponseT getAllSupportedPTPs(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                    v1.tmf854.GetAllSupportedPTPsT mtosiBody) throws ProcessingFailureException {
    return getAllSupportedPTPs(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.EquipmentInventoryMgr#getAllSupportedPTPsIterator(v1.tmf854.GetPtpIteratorT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllSupportedPTPsResponseT getAllSupportedPTPsIterator(v1.tmf854.GetPtpIteratorT mtosiBody,
                                                                            jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSupportedPTPsIterator", "getAllSupportedPTPsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllSupportedPTPsResponseT getAllSupportedPTPsIterator(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                            v1.tmf854.GetPtpIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllSupportedPTPsIterator(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.EquipmentInventoryMgr#getAllSupportingEquipmentNames(v1.tmf854.GetAllSupportingEquipmentNamesT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getAllSupportingEquipmentNames(
          v1.tmf854.GetAllSupportingEquipmentNamesT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSupportingEquipmentNames",	"getAllSupportingEquipmentNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllSupportingEquipmentNames(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetAllSupportingEquipmentNamesT mtosiBody)
          throws ProcessingFailureException {
    return getAllSupportingEquipmentNames(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.EquipmentInventoryMgr#getAllSupportingEquipment(v1.tmf854.GetAllSupportingEquipmentT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllSupportingEquipmentResponseT getAllSupportingEquipment(
          v1.tmf854.GetAllSupportingEquipmentT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSupportingEquipment", "getAllSupportingEquipmentResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllSupportingEquipmentResponseT getAllSupportingEquipment(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetAllSupportingEquipmentT mtosiBody)
          throws ProcessingFailureException {
    return getAllSupportingEquipment(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.EquipmentInventoryMgr#getContainedEquipment(v1.tmf854.GetContainedEquipmentT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetContainedEquipmentResponseT getContainedEquipment(v1.tmf854.GetContainedEquipmentT mtosiBody,
                                                                        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetContainedEquipmentWorker.class, mtosiBody, mtosiHeader,
            "getContainedEquipment", context, LOG);
  }

  @Override
  public v1.tmf854.GetContainedEquipmentResponseT getContainedEquipment(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                        v1.tmf854.GetContainedEquipmentT mtosiBody) throws ProcessingFailureException {
    return getContainedEquipment(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.EquipmentInventoryMgr#getEquipment(v1.tmf854.GetEquipmentT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetEquipmentResponseT getEquipment(v1.tmf854.GetEquipmentT mtosiBody,
                                                      jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetEquipmentWorker.class, mtosiBody, mtosiHeader,
            "getEquipment", context, LOG);
  }

  @Override
  public v1.tmf854.GetEquipmentResponseT getEquipment(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                      v1.tmf854.GetEquipmentT mtosiBody) throws ProcessingFailureException {
    return getEquipment(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.EquipmentInventoryMgr#getSupportedEquipmentNames(v1.tmf854.GetSupportedEquipmentNamesT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getSupportedEquipmentNames(
          v1.tmf854.GetSupportedEquipmentNamesT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getSupportedEquipmentNames", "getSupportedEquipmentNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getSupportedEquipmentNames(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetSupportedEquipmentNamesT mtosiBody)
          throws ProcessingFailureException {
    return getSupportedEquipmentNames(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.EquipmentInventoryMgr#getSupportedEquipment(v1.tmf854.GetSupportedEquipmentT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetSupportedEquipmentResponseT getSupportedEquipment(v1.tmf854.GetSupportedEquipmentT mtosiBody,
                                                                        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getSupportedEquipment", "getSupportedEquipmentResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetSupportedEquipmentResponseT getSupportedEquipment(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                                        v1.tmf854.GetSupportedEquipmentT mtosiBody) throws ProcessingFailureException {
    return getSupportedEquipment(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.EquipmentInventoryMgr#getSupportingEquipmentNames(v1.tmf854.GetSupportingEquipmentNamesT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetAllObjectNamesResponseT getSupportingEquipmentNames(
          v1.tmf854.GetSupportingEquipmentNamesT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getSupportingEquipmentNames", "getSupportingEquipmentNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getSupportingEquipmentNames(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetSupportingEquipmentNamesT mtosiBody)
          throws ProcessingFailureException {
    return getSupportingEquipmentNames(mtosiBody, mtosiHeader);
  }

  /*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854.EquipmentInventoryMgr#getSupportingEquipment(v1.tmf854.GetSupportingEquipmentT
	 *      mtosiBody ,)v1.tmf854.HeaderT mtosiHeader )*
	 */
  public v1.tmf854.GetSupportingEquipmentResponseT getSupportingEquipment(
          v1.tmf854.GetSupportingEquipmentT mtosiBody, jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader)
          throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getSupportingEquipment", "getSupportingEquipmentResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetSupportingEquipmentResponseT getSupportingEquipment(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854.GetSupportingEquipmentT mtosiBody)
          throws ProcessingFailureException {
    return getSupportingEquipment(mtosiBody, mtosiHeader);
  }
}
