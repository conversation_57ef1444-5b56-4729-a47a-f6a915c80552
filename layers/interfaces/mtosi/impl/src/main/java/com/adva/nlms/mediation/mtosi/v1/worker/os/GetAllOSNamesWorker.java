/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.os;


import jakarta.xml.ws.Holder;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import v1.tmf854.CommunicationPatternT;
import v1.tmf854.GetAllOSNamesT;
import v1.tmf854.GetAllObjectNamesResponseT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;


public class GetAllOSNamesWorker extends AbstractMtosiWorker {
  protected GetAllOSNamesT mtosiBody;
  protected GetAllObjectNamesResponseT response = new GetAllObjectNamesResponseT();
  protected NamingAttributesListT osNamesList;

  public GetAllOSNamesWorker(GetAllOSNamesT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getAllOSNames", "getAllOSNames", "getAllOSNamesResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  public void validateRequestHeader() throws Exception {
    super.validateRequestHeader();

    /* We do not support iterators for getAllOSNames. */
    if (this.getMtosiHeader().value.getCommunicationPattern() != CommunicationPatternT.SIMPLE_RESPONSE) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_NOT_IMPLEMENTED,
              "Unsupported communicationPattern requested.");
    }
  }

  @Override
  protected void parse () throws Exception {
    // empty method
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
  	//No validation required.
  }
  @Override
  protected void mediate() throws Exception {
    ObjectFactory objFactory = new ObjectFactory();
    osNamesList = objFactory.createNamingAttributesListT();
    NamingAttributesT osName = new NamingAttributesT();
    osName.setOsNm(OSFactory.getNmsName());
    osNamesList.getName().add(osName);
  }

  @Override
  protected void response() throws Exception {
    response.setNames(osNamesList);
  }

  @Override
  public GetAllObjectNamesResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
