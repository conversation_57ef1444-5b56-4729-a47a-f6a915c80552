/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.os;


import jakarta.xml.ws.Holder;
import v1.tmf854.GetMDResponseT;
import v1.tmf854.GetMDT;
import v1.tmf854.HeaderT;
import v1.tmf854.ManagementDomainT;
import v1.tmf854.NamingAttributesT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;


public class GetMDWorker extends AbstractMtosiWorker {
  protected GetMDT mtosiBody;
  protected GetMDResponseT response = new GetMDResponseT();
  protected NamingAttributesT mdName;
  protected ManagementDomainT md;

  public GetMDWorker(GetMDT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getMD", "getMD", "getMDResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    if ((mdName = mtosiBody.getMdName()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!NamingTranslationFactory.isManagementDomain(mdName)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!mdName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MD_NOT_FOUND);
    }
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
  	//Nothing to see here!
  }
  @Override
  protected void mediate() throws Exception {
    md = OSFactory.getMtosiMD();
  }

  @Override
  protected void response() throws Exception {
    response.setMd(md);
  }

  @Override
  public GetMDResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }

}
