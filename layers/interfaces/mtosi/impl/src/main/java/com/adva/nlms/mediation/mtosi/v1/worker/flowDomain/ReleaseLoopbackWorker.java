/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.ReleaseLoopbackResponseT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public abstract class ReleaseLoopbackWorker extends AbstractMtosiWorker
{
	Logger LOG = LogManager.getLogger(this.getClass().getName());

	protected ReleaseLoopbackResponseT response = new ReleaseLoopbackResponseT();
	protected NamingAttributesT namingAttributes;
	protected NetworkElement ne;

  public ReleaseLoopbackWorker(Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne)
	{
    super(mtosiHeader, "releaseLoopback", "releaseLoopback", "releaseLoopbackResponse");
    this.namingAttributes = namingAttributes;
    this.ne = ne;
  }

	@Override
  protected void parse() throws Exception {
    if (!NamingTranslationFactory.isFtp(namingAttributes) && !NamingTranslationFactory.isPort(namingAttributes)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ftpNm/ptpNm has not been specified.");
    }
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
   validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  @Override
  public ReleaseLoopbackResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}