/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v2.utils.feature;

import com.adva.nlms.mediation.mtosi.v2.utils.feature.interceptor.RemoveNillFieldsInterceptor;
import org.apache.cxf.Bus;
import org.apache.cxf.feature.AbstractFeature;
import org.apache.cxf.interceptor.InterceptorProvider;


public class RemoveNilFeature extends AbstractFeature {

  private static final RemoveNillFieldsInterceptor OUT = new RemoveNillFieldsInterceptor();

  @Override
  protected void initializeProvider(InterceptorProvider provider, Bus bus) {

    provider.getOutInterceptors().add(OUT);

  }


}