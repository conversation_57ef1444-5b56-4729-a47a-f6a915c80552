/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;


import jakarta.xml.ws.Holder;

import com.adva.nlms.common.NEUtils;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.ProtectionGroupF3Attr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.FTPTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import v1.tmf854.ConnectionTerminationPointT;
import v1.tmf854.GetTPResponseT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;


public class GetTPWorker extends AbstractMtosiWorker {
  protected GetTPResponseT response = new GetTPResponseT();
  protected NamingAttributesT tpName;
  protected NetworkElement ne;

  public GetTPWorker(Holder<HeaderT> mtosiHeader, NamingAttributesT tpName, NetworkElement ne) {
    super(mtosiHeader, "getTP", "getTP", "getTPResponse");
    this.tpName = tpName;
    this.ne = ne;
  }

  @Override
  protected void parse() throws Exception {
    if (!NamingTranslationFactory.isMandatoryElementsPresentForTP(tpName)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MANDATORY_PTP_CTP_FTP);
    }

    if (!NamingTranslationFactory.isManagementDomain(tpName)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!tpName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MD_NOT_FOUND);
    }
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
   validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  @Override
  protected void mediate() throws Exception {
    if (NamingTranslationFactory.isPort(tpName)) {
      executeGetPTP();
    } else if(NamingTranslationFactory.isFlow(tpName)) {
      executeGetCTP();
    } else if(NamingTranslationFactory.isFlowOnFtp(tpName)) {
      executeGetCTPOnFTP();
    } else if(NamingTranslationFactory.isFtp(tpName)) {
      executeGetFTP();
    } else if(NamingTranslationFactory.isNamingAttributesDefinedForTP(tpName)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.TO_MUCH_PARAMETERS_DEFINED);
    } else {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MANDATORY_PTP_CTP_FTP);
    }
  }

  @Override
  protected void response () throws Exception {
    // empty method
  }

  /**
   * Handles FTP requests
   * @throws Exception When something goes wrong
   */
  protected void executeGetFTP() throws Exception {
    if (NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM == ne.getNetworkElementType()){
      final String ftpNm = tpName.getFtpNm();
      if (ftpNm == null)
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NAME_MISSING);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      final NetworkElement ne = ManagedElementFactory.getAndValidateNE(tpName);

      MtosiMOFacade facade = getMtosiCtrl().getMoFacadeManager().getFacadeViaNeId(MtosiMOFacade.class, ne.getID());
      DTO<ProtectionGroupF3Attr> ftpMO = facade.findDTOViaMtosiName(ne.getID(), ftpNm, ProtectionGroupF3Attr.class);

      if(ftpMO == null)
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }

      if (NEUtils.isF3Device(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
        facade.refreshDTO(ne.getID(),ftpMO);
      }

      response.setFtp(new FTPTranslator().toMtosiFloatingTerminationPoint(ftpMO, tpName));

    }else {
      final FTP floatingTerminationPoint = ManagedElementFactory.getFtp(tpName);
      if (floatingTerminationPoint != null) {
        response.setFtp(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(floatingTerminationPoint).toMtosiFTP());
//      response.setFtp(floatingTerminationPoint.getMtosiTranslator().toMtosiFTP());
      } else {
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
            MtosiErrorConstants.FTP_NOT_FOUND);
      }
    }
  }

  /**
   * Handles CTP requests.
   * @throws Exception When something goes wrong
   */
  protected void executeGetCTP() throws Exception {
    throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
            "CTPs are not supported on this ME.");
  }

  /**
   * Handles CTP requests for FTP.
   * @throws Exception When something goes wrong
   */
  protected void executeGetCTPOnFTP () throws Exception {
    final FTP ftp = ManagedElementFactory.getFtp(tpName);
    ConnectionTerminationPointT ctp = null;
    if(ftp.getAPortCTPName().startsWith(tpName.getCtpNm())) {
      ctp = (new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ftp.getAPort())).toMtosiCTP();
//      ctp = ((MtosiSupported)ftp.getAPort()).getMtosiTranslator().toMtosiCTP();
    }
    else if(ftp.getBPortCTPName().startsWith(tpName.getCtpNm())) {
      ctp = (new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ftp.getBPort())).toMtosiCTP();
//      ctp = ((MtosiSupported)ftp.getBPort()).getMtosiTranslator().toMtosiCTP();
    }

    if(ctp == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.CTP_NOT_FOUND);
    }

    response.setCtp(ctp);
  }

  /**
   * Handles PTP requests.
   * @throws Exception When something goes wrong
   */
  protected void executeGetPTP() throws Exception {
    final Port port = ManagedElementFactory.getPort(tpName);
    PhysicalTerminationPointT ptp;

//    if (!(port instanceof MtosiSupported)) {
//      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
//              MtosiErrorConstants.PTP_NOT_FOUND);
//    }
//    ptp = ((MtosiSupported)port).getMtosiTranslator().toMtosiPTP();
    ptp = new MtosiTranslatorFacade(ne,mtosiHeader,MtosiErrorConstants.PTP_NOT_FOUND).getMtosiTranslator(port).toMtosiPTP();
    response.setPtp(ptp);
  }

  @Override
  public GetTPResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
