/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */
package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.mtosi.ManagementCTPProperties;
import com.adva.nlms.mediation.mtosi.common.remotetypectp.RemoteTypeDAOImpl;
import com.adva.nlms.mediation.mtosi.common.remotetypectp.RemoteTypeDBImpl;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.ManagementTunnelMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.TerminationPointT;
import v1.tmf854ext.adva.GetContainedCurrentManagementCTPsResponseT;
import v1.tmf854ext.adva.GetContainedCurrentManagementCTPsT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public class GetContainedCurrentManagementCTPsWorker extends AbstractMtosiWorker {
  Logger LOG = LogManager.getLogger(GetContainedCurrentManagementCTPsWorker.class);

  protected GetContainedCurrentManagementCTPsT mtosiBody;
  protected GetContainedCurrentManagementCTPsResponseT response = new GetContainedCurrentManagementCTPsResponseT();
  protected NamingAttributesT namingAttributes;
  protected String ptpName;
  protected NetworkElement networkElement;
  protected ManagementCTPProperties ctpPropsResponse;

  public GetContainedCurrentManagementCTPsWorker(GetContainedCurrentManagementCTPsT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getContainedCurrentManagementCTPs", "getContainedCurrentManagementCTPs", "getContainedCurrentManagementCTPsResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    validator.validate(networkElement.getDefaultNetworkElementTypeString());
  }

  @Override
  protected void parse() throws Exception {
    namingAttributes=mtosiBody.getPtpName();
    if (namingAttributes == null || (ptpName = namingAttributes.getPtpNm()) == null ) {
      LOG.debug("namingAttributes == null");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.PTP_NAME_MISSING);

    }
    if (!NamingTranslationFactory.isManagementDomain(namingAttributes)) {
      LOG.debug("NamingTranslationFactory.isManagementDomain(namingAttributes)=>false");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (namingAttributes.getMeNm()==null) {
      LOG.debug("namingAttributesT.getMeNm()==null");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.ME_NAME_MISSING);
    }

    if (!namingAttributes.getMdNm().equals(OSFactory.getMDNm())) {
      LOG.debug("namingAttributes.getMdNm().equals(OSFactory.getMDNm())=>false");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MD_NOT_FOUND);
    }

    networkElement = this.getMtosiCtrl().getLegacyMtosiMOFacade().getNEByName(namingAttributes.getMeNm());
    MtosiUtils.validateNE(networkElement);

    if (!NamingTranslationFactory.isPtpNameValid(ptpName)) {
      LOG.debug("namingAttributes.getPtpNm() invalid");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.PTP_NAME_NOT_VALID);
    }
  }

  @Override
  protected void mediate() throws Exception {
    int shelfNum = NamingTranslationFactory.shelfNumberFromShelfCombo(ptpName);
    if (shelfNum == -1) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "Invalid shelf index.");
    }
    int slotNum = NamingTranslationFactory.slotNumberFromShelfCombo(ptpName);
    if (slotNum == -1) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "Invalid slot index.");
    }
    String portName = NamingTranslationFactory.portNameFromShelfCombo(ptpName);
    int portType = MtosiUtils.getPortTypeTunnel(portName);
    if (portType == -1) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.PTP_PORT_TYPE_WRONG);
    }
    int portIndex = MtosiUtils.getPortIndexTunnel(portName);
    if (portIndex == -1) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.PTP_PORT_INDEX_WRONG);
    }

    ctpPropsResponse = this.getMtosiCtrl().getLegacyMtosiMOFacade().getManagementCTP(networkElement.getID(),
        shelfNum, slotNum, portType, portIndex, null);
  }

  @Override
  protected void response() throws Exception {
    if (ctpPropsResponse != null) {
      updateCtpPropsResponse();
      TerminationPointT tp = new TerminationPointT();
      PhysicalTerminationPointT ptp = ManagementTunnelMediator.getManagementTunnel(ctpPropsResponse,namingAttributes);
      tp.setPtp(ptp);
      response = new GetContainedCurrentManagementCTPsResponseT();
      response.setTheCTP(tp);
    }
  }

    private boolean updateCtpPropsResponse(){
        if( ctpPropsResponse != null && ctpPropsResponse.getResponse() != null && ctpPropsResponse.getResponse().getRemoteCpeRemoteType() ==  null){
            RemoteTypeDAOImpl remoteDAOImpl = new RemoteTypeDAOImpl();
            try{
                RemoteTypeDBImpl entity = remoteDAOImpl.getRemoteType(namingAttributes.getMeNm(), null, namingAttributes.getPtpNm());
               if(entity != null){
                    ctpPropsResponse.setRemoteType(entity.getRemoteType());
                }/*else{    //REMOVE this as updating without tunnel name is very dangerous.
                    updateCtpPropsResponse(networkElement.getIPAddress(), namingAttributes.getMeNm(),namingAttributes.getPtpNm(),namingAttributes.getCtpNm());
                    entity = remoteDAOImpl.getRemoteType(namingAttributes.getMeNm(), null, namingAttributes.getPtpNm());
                    if(entity != null){
                        ctpPropsResponse.setRemoteType(entity.getRemoteType());
                    }
                }*/
                return true;
            }catch(Exception ex){
                LOG.debug("Failed to persist tunnel data " + ex.getMessage());
                return false;
            }
        }
        return true;
    }

  @Override
  public GetContainedCurrentManagementCTPsResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
