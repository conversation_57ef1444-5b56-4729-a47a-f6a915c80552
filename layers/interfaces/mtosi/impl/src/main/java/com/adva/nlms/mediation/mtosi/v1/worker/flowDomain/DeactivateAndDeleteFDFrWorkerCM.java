/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.serviceProvisioning.ACCPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.NETPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.ProtectionGroupF3SPProperties;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.f3.entity.port.acc.MTOSIPortF3Acc;
import com.adva.nlms.mediation.config.f3.entity.port.net.MTOSIPortF3Net;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FDFrACCEndFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FDFrEndFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FDFrFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FDFrFTPEndFSP150CM;
import com.adva.nlms.mediation.config.mtosi.FDFr;
import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMPortModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.worker.DeactivateAndDeleteFDFrWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.TPDataListT;
import v1.tmf854.TPDataT;
import v1.tmf854ext.adva.DeactivateAndDeleteFDFrT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.ws.Holder;

public class DeactivateAndDeleteFDFrWorkerCM extends DeactivateAndDeleteFDFrWorker
{
	Logger LOG = LogManager.getLogger(this.getClass().getName());
	private MTOSIFlowF3 flow;
	private MTOSIPortF3Net portNET;
  private MTOSIPortF3Net portNET2 = null;
  private MTOSIPortF3Acc portACC;
  private FTP ftp = null;
  private TPDataT tpDataACC;
	private TPDataT tpDataNET;
  private TPDataT tpDataNET2;
  private TPDataT tpDataFlow;
  private TPDataT tpDataFTP;

	public DeactivateAndDeleteFDFrWorkerCM(DeactivateAndDeleteFDFrT mtosiBody, Holder<HeaderT> mtosiHeader, NetworkElement ne, FDFr fdfr) {
    super(mtosiBody, mtosiHeader, ne, fdfr);
	}

  @Override
  protected void response() throws Exception {
        v1.tmf854ext.adva.ObjectFactory factory = new v1.tmf854ext.adva.ObjectFactory();
		JAXBElement<TPDataListT> modifiedTPs = factory
				.createDeactivateAndDeleteFDFrResponseTTpsToModify(getUpdatedTPs());
		response.setTpsToModify(modifiedTPs);
	}

	private TPDataListT getUpdatedTPs() throws ProcessingFailureException {
		ObjectFactory factory = new ObjectFactory();
		TPDataListT tpsToModify = factory.createTPDataListT();
		if (tpDataACC != null && portACC != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataACC.getTpName())).toMtosiPTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataACC.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
		if (tpDataNET != null && portNET != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataNET.getTpName())).toMtosiPTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataNET.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
    if (tpDataNET2 != null && portNET2 != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataNET2.getTpName())).toMtosiPTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataNET2.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
    if (tpDataFlow != null && flow != null) {
      MTOSIFlowF3 flowUpdated = ManagedElementFactory.getCMFlow(tpDataFlow.getTpName());
      if (flowUpdated != null && flow.getFDFr() != null){// && flowUpdated.getFlowSPProperties().get(FlowSPPropertiesFSP150CM.VB.Active)) {
        tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(flowUpdated).toMtosiCTPasTPDataT());
//        tpsToModify.getTpData().add(flowUpdated.getMtosiTranslator().toMtosiCTPasTPDataT());
      }
    }
    if (tpDataFTP != null && ftp != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ManagedElementFactory.getFtp(tpDataFTP.getTpName())).toMtosiFTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getFtp(tpDataFTP.getTpName()).getMtosiTranslator().toMtosiFTPasTPDataT());
		}
    return tpsToModify;
	}

  private void transact(ACCPortSPPropertiesFSP150CM propsACC,
                        NETPortSPPropertiesFSP150CM propsNET1, NETPortSPPropertiesFSP150CM propsNET2, String fdfrName/*,
			CFMCCMSPPropertiesFSP150CM propsCfm*/, ProtectionGroupF3SPProperties propsFTP)
          throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "DeactivateAndDeleteFDFrWorker");
    ne.getMTOSIWorker().setFDFrOperationInProgress(fdfrName, true);
    try {
      if (propsACC != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, portACC.getMtosiName());
        portACC.setSettings(propsACC);
      }
      if (propsNET1 != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, portNET.getMtosiName());
        portNET.setSettings(propsNET1);
      }
      if (propsNET2 != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, portNET2.getMtosiName());
        portNET2.setSettings(propsNET2);
      }
      if (propsFTP != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, "ftpNm=" + ftp.getFTPName());
        ftp.setFTPSPProperties(propsFTP);
      }
     
      logSecurity(ne, SystemAction.DeleteNetwork, fdfrName);
      
      ne.getMTOSIWorker().deleteFDFr(fdfrName);
      
      if (flow != null) {
        logSecurity(ne, SystemAction.DeleteNetwork, flow.getMtosiName());
        if (((ACCPortSPPropertiesFSP150CM) portACC.getPortSPProperties()).get(ACCPortSPPropertiesFSP150CM.VI.SvcType).equals(
                CMPortModeTranslation.CO_EPL.getPortSvcTypeValue())) {
          flow.deactivate();
        } else {
          // todo: EVPL !!!
        }
      }

      NetTransactionManager.commitNetTransaction(id);
      ne.logSROperation(SROperationState.SERVICE_DELETION_SUCCESS, fdfrName);
    } catch (NetTransactionException e) {
      ne.logSROperation(SROperationState.SERVICE_DELETION_FAILURE, fdfrName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ne.logSROperation(SROperationState.SERVICE_DELETION_FAILURE, fdfrName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ne.logSROperation(SROperationState.SERVICE_DELETION_FAILURE, fdfrName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      ne.getMTOSIWorker().setFDFrOperationInProgress(fdfrName, false);
      NetTransactionManager.ensureEnd(id);
    }
  }

	@Override
  protected void mediate() throws Exception {
    boolean protection;
    NETPortSPPropertiesFSP150CM props;
    if (((FDFrFSP150CM) fdfr).getAEnd() instanceof FDFrACCEndFSP150CM) {
       props = ((FDFrACCEndFSP150CM) ((FDFrFSP150CM) fdfr).getAEnd()).getPort().getPortSPProperties();
    }
    else {
      props = ((FDFrACCEndFSP150CM) ((FDFrFSP150CM) fdfr).getZEnd()).getPort().getPortSPProperties();
    }
    int shelfIndex = props.get(NETPortSPPropertiesFSP150CM.VI.ShelfIndex);
    int slotIndex = props.get(NETPortSPPropertiesFSP150CM.VI.SlotIndex);
    protection = getFSP150CMMTOSIWorker((NetworkElementFSP150CM)ne).isPGEnabled(shelfIndex, slotIndex);
    portNET = (MTOSIPortF3Net) ne.getMTOSIWorker().getPortByName(MtosiConstants.PORT_NET1, shelfIndex, slotIndex);
		portNET2 = (MTOSIPortF3Net) ne.getMTOSIWorker().getPortByName(MtosiConstants.PORT_NET2, shelfIndex, slotIndex);
    ACCPortSPPropertiesFSP150CM propsACC = null;
		NETPortSPPropertiesFSP150CM propsNET = null;
    NETPortSPPropertiesFSP150CM propsNET2 = null;
		ProtectionGroupF3SPProperties propsFTP = null;
    String ftpName = null;
    if (protection) {
      if (((FDFrFSP150CM) fdfr).getAEnd() instanceof FDFrFTPEndFSP150CM) {
        ftpName = ((FDFrFTPEndFSP150CM) ((FDFrFSP150CM) fdfr).getAEnd()).getFTP().getFTPName();
      }
      else if (((FDFrFSP150CM) fdfr).getZEnd() instanceof FDFrFTPEndFSP150CM) {
        ftpName = ((FDFrFTPEndFSP150CM) ((FDFrFSP150CM) fdfr).getZEnd()).getFTP().getFTPName();
      }
      else {
        LOG.warn("There is protection on the card, but FDFr has no FTP end-point!");
        protection = false;
      }
    }

    if (tpsToModify != null)
		{
    	int modifiedSlotIndex = ManagedElementFactory.needsSlotIncrement(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) ? slotIndex -1 :slotIndex;
      if (!MtosiTPMediator.checkTPToModifySameCard(ne, tpsToModify, shelfIndex, modifiedSlotIndex, ftpName))
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(),
						ExceptionUtils.EXCPT_INVALID_INPUT, "The specified TPs must be on the same slot.");
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
      tpDataACC = MtosiTPMediator.getTPDataTForCMACC(tpsToModify);
			tpDataNET = MtosiTPMediator.getTPDataTForCMNET(tpsToModify, 1);
      tpDataFlow = MtosiTPMediator.getTPDataTForCTP(tpsToModify);
      if (protection) {
        tpDataFTP = MtosiTPMediator.getTPDataTForFTP(tpsToModify);
        tpDataNET2 = MtosiTPMediator.getTPDataTForCMNET(tpsToModify, 2);
      }
    }
    if (tpDataFlow != null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(),
					ExceptionUtils.EXCPT_INVALID_INPUT,
					"Flow cannot be modified as part of a DeactivateAndDelete request.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
    if (tpDataACC != null)
		{
      Port portL = ManagedElementFactory.getPort(tpDataACC.getTpName());
			portACC = (MTOSIPortF3Acc) portL;
      propsACC = MtosiTPMediator.mtosiTPDataTToCMACCProperties(tpDataACC, portACC);
		}
		if (tpDataNET != null)
		{
			propsNET = MtosiTPMediator.mtosiTPDataTToCMNETProperties(tpDataNET, portNET);
		}
    if (tpDataNET2 != null)
		{
			propsNET2 = MtosiTPMediator.mtosiTPDataTToCMNETProperties(tpDataNET2, portNET2);
		}
		if (tpDataFTP != null)
		{
			ftp = ne.getMTOSIWorker().getFTP(ftpName);
			if (ftp == null)
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(),
						ExceptionUtils.EXCPT_INVALID_INPUT, "The specified FTP does not exist.");
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
      propsFTP = MtosiTPMediator.mtosiTPDataTToCMFTPProperties(tpDataFTP, ftp);
		}

		FDFrFSP150CM fdfrCM = (FDFrFSP150CM) fdfr;

		FDFrACCEndFSP150CM accEnd;
    FDFrEndFSP150CM aEnd = fdfrCM.getAEnd();
    if (aEnd instanceof FDFrACCEndFSP150CM) {
      accEnd = (FDFrACCEndFSP150CM) aEnd;
    } else {
      accEnd = (FDFrACCEndFSP150CM) fdfrCM.getZEnd();
		}
		if (portACC == null)
		{
			portACC = accEnd.getPort();
		}
		flow = accEnd.getFlow();
		transact(propsACC, propsNET, propsNET2, namingAttributes.getFdfrNm(), propsFTP);
  }
}
