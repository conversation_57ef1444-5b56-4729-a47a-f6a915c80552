/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.factory;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.config.mofacade.MOFacadeManager;
import com.adva.nlms.mediation.mtosi.v1.adapter.facade.ConnectivityFaultMaintenanceDTOImpl;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.LayeredParametersT;
import v1.tmf854.NVSListT;
import v1.tmf854.NameAndStringValueT;
import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class ConnectivityFaultMaintenanceFactory {
  private static Logger logger = LogManager.getLogger(ConnectivityFaultMaintenanceFactory.class);
  public final static String NOT_FOUND = "notFound";
  public final static String FOUND = "found";


  private void processTheParameters(LayeredParametersT theParameters, ConnectivityFaultMaintenanceDTOImpl cfmDTO) throws Exception {
    if (theParameters != null) {
      String layer = theParameters.getLayer();
      validateLayer(layer);
      NVSListT nameAndValueStringListType = theParameters.getTransmissionParams();
      if (nameAndValueStringListType != null) {
        for (NameAndStringValueT valueType : nameAndValueStringListType.getNvs()) {
          String name = valueType.getName();
          cfmDTO.setValue(layer + "." + name, valueType.getValue());
        }
      }
    }
  }

  private void validateLayer(String layer) throws ProcessingFailureException {
    if (!(getCfmLayersMap().containsKey(layer))){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
          "Layer Rate contains an illegal value.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }


  private Map<String, LayerListHelper> getCfmLayersMap(){
    return  new HashMap<String, LayerListHelper>(){{
      put("PROP_ADVA_CFMCCM", new LayerListHelper(false,ConnectivityFaultMaintenanceFactory.NOT_FOUND) );
    }};
  }

  public ConnectivityFaultMaintenanceDTOImpl processRequestForMANet(JAXBElement<LayeredParametersListT> maParamList,
                                                                 MOFacadeManager moFacadeManager) throws Exception {

    ConnectivityFaultMaintenanceDTOImpl cfmDTO = new ConnectivityFaultMaintenanceDTOImpl(moFacadeManager);

    List<LayeredParametersT> parametersTypeList = maParamList.getValue().getLayeredParameters();
    for (LayeredParametersT theParameters : parametersTypeList) {
      processTheParameters(theParameters, cfmDTO);
    }
    return cfmDTO;
  }

  public ConnectivityFaultMaintenanceDTOImpl processRequestForMaComp(JAXBElement<LayeredParametersListT> maParamList,
                                                                    MOFacadeManager moFacadeManager) throws Exception {

    ConnectivityFaultMaintenanceDTOImpl cfmDTO = new ConnectivityFaultMaintenanceDTOImpl(moFacadeManager);

    List<LayeredParametersT> parametersTypeList = maParamList.getValue().getLayeredParameters();
    for (LayeredParametersT theParameters : parametersTypeList) {
      processTheParameters(theParameters, cfmDTO);
    }
    return cfmDTO;
  }

  public ConnectivityFaultMaintenanceDTOImpl processRequestForMD(JAXBElement<LayeredParametersListT> mdParamList,
                                                               MOFacadeManager moFacadeManager) throws Exception {

    ConnectivityFaultMaintenanceDTOImpl cfmDTO = new ConnectivityFaultMaintenanceDTOImpl(moFacadeManager);

    List<LayeredParametersT> parametersTypeList = mdParamList.getValue().getLayeredParameters();
    for (LayeredParametersT theParameters : parametersTypeList) {
      processTheParameters(theParameters, cfmDTO);
    }
    return cfmDTO;
  }

  public ConnectivityFaultMaintenanceDTOImpl processRequestForMEP(JAXBElement<LayeredParametersListT> mepParamList,
                                                                 MOFacadeManager moFacadeManager) throws Exception {

    ConnectivityFaultMaintenanceDTOImpl cfmDTO = new ConnectivityFaultMaintenanceDTOImpl(moFacadeManager);

    List<LayeredParametersT> parametersTypeList = mepParamList.getValue().getLayeredParameters();
    for (LayeredParametersT theParameters : parametersTypeList) {
      processTheParameters(theParameters, cfmDTO);
    }
    return cfmDTO;
  }

  public ConnectivityFaultMaintenanceDTOImpl parseMD(MtosiAddress mtosiAddr, JAXBElement<LayeredParametersListT> mdParamList,
                                                        MOFacadeManager moFacadeManager) throws Exception {

    int neType = mtosiAddr.getNE().getNetworkElementType();

    switch (neType) {
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
        if (logger.isTraceEnabled()) {
          logger.trace("GE201/GE201se parsing message body");
        }
        return processRequestForMD(mdParamList, moFacadeManager);
      default:
        return null;
    }
  }

  public ConnectivityFaultMaintenanceDTOImpl parseMANet(MtosiAddress mtosiAddr, JAXBElement<LayeredParametersListT> maParamList,
                                                     MOFacadeManager moFacadeManager) throws Exception {

    int neType = mtosiAddr.getNE().getNetworkElementType();

    switch (neType) {
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
        if (logger.isTraceEnabled()) {
          logger.trace("GE201/GE201se parsing message body");
        }
        return processRequestForMANet(maParamList, moFacadeManager);
      default:
        return null;
    }
  }

  public ConnectivityFaultMaintenanceDTOImpl parseMaComp(MtosiAddress mtosiAddr, JAXBElement<LayeredParametersListT> maParamList,
                                                        MOFacadeManager moFacadeManager) throws Exception {

    int neType = mtosiAddr.getNE().getNetworkElementType();

    switch (neType) {
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
        if (logger.isTraceEnabled()) {
          logger.trace("GE201/GE201se parsing message body");
        }
        return processRequestForMaComp(maParamList, moFacadeManager);
      default:
        return null;
    }
  }

  public ConnectivityFaultMaintenanceDTOImpl parseMEP(MtosiAddress mtosiAddr, JAXBElement<LayeredParametersListT> mepParamList,
                                                     MOFacadeManager moFacadeManager) throws Exception {

    int neType = mtosiAddr.getNE().getNetworkElementType();

    switch (neType) {
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
        if (logger.isTraceEnabled()) {
          logger.trace("GE201/GE201se parsing message body");
        }
        return processRequestForMEP(mepParamList, moFacadeManager);
      default:
        return null;
    }
  }


  private class LayerListHelper {

    private boolean mandatory;
    private String found;


    public LayerListHelper(boolean mandatory, String found) {
      this.mandatory = mandatory;
      this.found = found;
    }

    public boolean isMandatory() {
      return mandatory;
    }

    public String getFound() {
      return found;
    }

    public void setFound(String found) {
      this.found = found;
    }
  }
}
