/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum HNObjectStateTranslation implements TranslatableEnum {
    unknown                   (1, "Unknown"), // Error case, should never happen.
    active                    (2, "Active"), // Running at full capacity and passing traffic.
    partial                   (3, "Partial"), // Running at only partial capacity, e.g. 1 out of 2 ports in a LAG.
    disabled                  (4, "Disabled"), // Admin Disabled State, assigned by craft.
    testing                   (5, "Testing"), // Admin Test State, assigned by craft.
    unequipped                (6, "Unequipped"), // Hardware is not equipped (chassis/card/port).
    initializing              (7, "Initializing"), // In the process of coming up, not active, but not faulted.
    down                      (8, "Down"), // Down, no traffic passing.  Possible config mismatch.
    lowerLayerDown            (9, "LowerLayerDown"), // Lower Layer (or Lower Link) is down. No traffic passing.
    supportingEntityDown     (10, "SupportingEntityDown"), // Supporting entity is down/disabled/etc so no traffic passing.
    faulted                  (11, "Faulted"), // Faulted, most likely has an alarm associated with object.
    equipmentMismatch        (12, "EquipmentMismatch"), // Special fault, detected equipment does not match provisioned.
    configIncomplete         (13, "ConfigIncomplete"), // Object not fully provisioned (ie - LAG with no Ports).

    training                 (14, "Training"), // 2bpme port is Training.
    handshaking              (15, "Handshaking"), // 2bpme port is Handshaking with peer.
    optimizing               (16, "Optimizing"), // 2bpme port is Optimizing link.

    bonded                   (17, "Bonded"), // Device detected and in Bonded mode
    rsm                      (18, "Rsm"), // Device detected and in RSM mode
    nonbonded                (19, "NonBonded"), // Device detected and in NonBonded mode
    hello                    (20, "Hello"), // Device is attempting to contact peer
    listMismatch             (21, "ListMismatch"), // Device detected but excluded from configured list
    addressMismatch          (22, "AddressMismatch"), // Device detected but does not match configured address
    validationFailed         (23, "ValidationFailed"), // Device detected but failed validation
    learningDisabled         (24, "LearningDisabled"), // Unknown device detected but learning is disabled
    resourceUnavailable      (25, "ResourceUnavailable"), // Unknown device detected but no more resources are available

    stpDiscarding            (26, "STPDiscarding"), // STP has placed object into the discarding state
    stpLearning              (27, "STPLearning"), // STP has placed object into the learning state
    evcDisabled              (28, "EVCDisabled"), // Used for EVC binding when its EVC is Admin Disabled

    peerListMismatch         (29, "PeerListMismatch"), // Device detected but excluded from configured list on peer
    peerAddressMismatch      (30, "PeerAddressMismatch"), // Device detected but does not match configured address on peer
    peerValidationFailed     (31, "PeerValidationFailed"), // Device detected but failed validation on peer
    peerLearningDisabled     (32, "PeerLearningDisabled"), // Unknown device detected but is learning disabled on peer
    peerResourceUnavailable  (33, "PeerResourceUnavailable"), // Unknown device detected but no more resources are available on peer

    deviceSoftwareUpgrade    (34, "DeviceSoftwareUpgrade"), // Device not operational because it's software is being upgraded
    deviceApplyConfig        (35, "DeviceApplyConfig"), // Device not operational because configuration data is being applied

    oamLoopBackLocal         (36, "OAMLoopbackLocal"), // OAM port in local loopback
    oamLoopBackRemote        (37, "OAMLoopbackRemote"), // OAM port in remote loopback

    activeSwimageMisMatch    (38, "ActiveSwimageMismatch"), // Device not manageable because active swImage does not match CO/master's
    //New for 7_2_2
    sarResrcUnavailable      (39, "SarResrcUnavailable"), // Segmentation and Reassembly (SAR) resource unavailable
    peerSarResrcUnavailable  (40, "PeerSarResrcUnavailable"), // Peer Segmentation and Reassembly (SAR) resource unavailable

    manualLineLpbkActive     (41, "ManualLineLpbkActive"), // Manual Line Loopback Active
    manualPayloadLpbkActive  (42, "ManualPayloadLpbkActive"), // Manual Payload Loopback Active
    manualNetworkLpbkActive  (43, "ManualNetworkLpbkActive"), // Manual Network Loopback Active
    inbandLineLpbkActive     (44, "InbandLineLpbkActive"), // In-band Line Loopback Active
    bocLineLpbkActive        (45, "BocLineLpbkActive"), // Bit Oriented Code (BOC) Line Loopback Active
    bocPayloadLpbkActive     (46, "BocPayloadLpbkActive"), // Bit Oriented Code (BOC) Payload Loopback Active
   
    farEndPortAdminDisabled  (47, "FarEndPortAdminDisabled"), // Far-end T1/E1 port has been admin disabled
    farEndPortPhyAlarm       (48, "FarEndPortPhyAlarm"), // Far-end T1/E1 port has physical alarm (LOS,etc)

    errDisabled              (49, "ErrDisabled"), // System errDisable mechanism is holding interface down
    rai                      (50, "RAI"), // Remote alarm indication

    manLocalLpbk             (51, "ManLocalLpbk"), // Manually initiated local loopback
    manRemoteLpbk            (52, "ManRemoteLpbk"), // Manually initiated remote loopback
    feacRemoteLpbk           (53, "FeacRemoteLpbk"), // Remote Loopback initaited by FEAC command
    bertIsActive             (54, "BertIsActive"),  // Bit Error Rate Test is active
    NOT_APPLICABLE	(-1, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private HNObjectStateTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}