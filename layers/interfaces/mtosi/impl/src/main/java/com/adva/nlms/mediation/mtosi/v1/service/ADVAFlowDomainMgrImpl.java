/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;

import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.CreateAndActivateFDFrWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.DeactivateAndDeleteFDFrWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.CreateFTPWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.DeleteCTPWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.DeleteFTPWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.GetAllCPTPsWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.GetAllFDFrsWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.GetContinuityTestWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.GetFDFrWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.GetFDFrsWithTPWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.ModifyFDFrWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.OperateLoopbackWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.OperateVlanLoopbackWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.PerformProtectionCommandWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.ReleaseLoopbackWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.ReleaseVlanLoopbackWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.RenameFDFrWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.StartContinuityTestWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.StopContinuityTestWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854ext.adva.GetContinuityTestResponseT;
import v1.tmf854ext.adva.GetContinuityTestT;
import v1.tmf854ext.adva.PerformProtectionCommandResponseT;
import v1.tmf854ext.adva.PerformProtectionCommandT;
import v1.tmf854ext.adva.StartContinuityTestResponseT;
import v1.tmf854ext.adva.StartContinuityTestT;
import v1.tmf854ext.adva.StopContinuityTestResponseT;
import v1.tmf854ext.adva.StopContinuityTestT;
import ws.v1.tmf854.ProcessingFailureException;
import ws.v1.tmf854ext.adva.ADVAFlowDomainMgr;

import jakarta.annotation.Resource;
import jakarta.jws.WebMethod;
import jakarta.jws.WebParam;
import jakarta.jws.WebResult;
import jakarta.jws.soap.SOAPBinding;
import jakarta.xml.ws.Holder;
import jakarta.xml.ws.WebServiceContext;

/**
 * This class was generated by the CXF 2.0-incubator-RC Thu Jun 28 08:59:00 CEST
 * 2007 Generated source version: 2.0-incubator-RC
 *
 */
@jakarta.jws.WebService(name = "ADVAFlowDomainMgr", serviceName = "ADVAConfigurationService", portName = "ADVAFlowDomainMgrHttp", targetNamespace = "adva.tmf854ext.v1.ws", endpointInterface = "ws.v1.tmf854ext.adva.ADVAFlowDomainMgr")
public class ADVAFlowDomainMgrImpl implements ADVAFlowDomainMgr
{
	private static final Logger LOG = LogManager.getLogger(ADVAFlowDomainMgrImpl.class.getPackage().getName());
	@Resource
	private WebServiceContext context;


	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#renameFDFr(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.RenameFDFrT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.RenameFDFrResponseT renameFDFr(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
			v1.tmf854ext.adva.RenameFDFrT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(RenameFDFrWorker.class, mtosiBody, mtosiHeader,
            "renameFDFr", context, LOG);
  }

	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#deactivateAndDeleteFDFr(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.DeactivateAndDeleteFDFrT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.DeactivateAndDeleteFDFrResponseT deactivateAndDeleteFDFr(
			jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854ext.adva.DeactivateAndDeleteFDFrT mtosiBody)
			throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(DeactivateAndDeleteFDFrWorker.class, mtosiBody, mtosiHeader,
            "deactivateAndDeleteFDFr", context, LOG);
  }

	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#getAllCPTPs(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.GetAllCPTPsT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.GetAllCPTPsResponseT getAllCPTPs(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
			v1.tmf854ext.adva.GetAllCPTPsT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllCPTPsWorker.class, mtosiBody, mtosiHeader,
            "getAllCPTPs", context, LOG);
  }

	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#deleteCTP(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.DeleteCTPT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.DeleteCTPResponseT deleteCTP(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
			v1.tmf854ext.adva.DeleteCTPT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(DeleteCTPWorker.class, mtosiBody, mtosiHeader,
            "deleteCTP", context, LOG);
  }

	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#deleteFTP(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.DeleteFTPT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.DeleteFTPResponseT deleteFTP(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
			v1.tmf854ext.adva.DeleteFTPT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(DeleteFTPWorker.class, mtosiBody, mtosiHeader,
            "deleteFTP", context, LOG);
  }

	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#getAllFDFrs(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.GetAllFDFrsT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.GetAllFDFrsResponseT getAllFDFrs(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
			v1.tmf854ext.adva.GetAllFDFrsT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllFDFrsWorker.class, mtosiBody, mtosiHeader,
            "getAllFDFrs", context, LOG);
  }

	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#releaseLoopback(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.ReleaseLoopbackT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.ReleaseLoopbackResponseT releaseLoopback(
			jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854ext.adva.ReleaseLoopbackT mtosiBody)
			throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(ReleaseLoopbackWorker.class, mtosiBody, mtosiHeader,
            "releaseLoopback", context, LOG);
  }

	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#createAndActivateFDFr(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.CreateAndActivateFDFrT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.CreateAndActivateFDFrResponseT createAndActivateFDFr(
			jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854ext.adva.CreateAndActivateFDFrT mtosiBody)
			throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(CreateAndActivateFDFrWorker.class, mtosiBody, mtosiHeader,
            "createAndActivateFDFr", context, LOG);
  }

	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#getFDFr(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.GetFDFrT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.GetFDFrResponseT getFDFr(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
			v1.tmf854ext.adva.GetFDFrT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(GetFDFrWorker.class, mtosiBody, mtosiHeader,
            "getFDFr", context, LOG);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#stopContinuityTest(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.StopContinuityTestT mtosiBody )*
	 */
	@Override
  public StopContinuityTestResponseT stopContinuityTest(Holder<HeaderT> mtosiHeader, StopContinuityTestT mtosiBody)
			throws ProcessingFailureException {
    return ServiceUtils.runMethod(StopContinuityTestWorker.class, mtosiBody, mtosiHeader,
            "stopContinuityTest", context, LOG);
  }

	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#createFTP(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.CreateFTPT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.CreateFTPResponseT createFTP(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
			v1.tmf854ext.adva.CreateFTPT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(CreateFTPWorker.class, mtosiBody, mtosiHeader,
            "createFTP", context, LOG);
  }

	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#getFDFrsWithTP(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.GetFDFrsWithTPT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.GetFDFrsWithTPResponseT getFDFrsWithTP(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
			v1.tmf854ext.adva.GetFDFrsWithTPT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(GetFDFrsWithTPWorker.class, mtosiBody, mtosiHeader,
            "getFDFrsWithTP", context, LOG);
	}


  @Override
  @SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
  @WebResult(targetNamespace = "adva.tmf854ext.v1", partName = "mtosiBody", name = "performProtectionCommandResponse")
  @WebMethod(action = "performProtectionCommand")
  public PerformProtectionCommandResponseT performProtectionCommand(@WebParam(targetNamespace = "tmf854.v1", partName = "mtosiHeader", name = "header", header = true, mode = WebParam.Mode.INOUT) Holder<HeaderT> mtosiHeader, @WebParam(targetNamespace = "adva.tmf854ext.v1", partName = "mtosiBody", name = "performProtectionCommand") PerformProtectionCommandT mtosiBody) throws ProcessingFailureException {
    return ServiceUtils.runMethod(PerformProtectionCommandWorker.class, mtosiBody, mtosiHeader,
            "performProtectionCommand", context, LOG);
  }

  /*
    * (non-Javadoc)
    *
    * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#operateVLANLoopback(v1.tmf854.HeaderT
    *      mtosiHeader ,)v1.tmf854ext.adva.OperateVLANLoopbackT mtosiBody )*
    */
  @Override
  public v1.tmf854ext.adva.OperateVLANLoopbackResponseT operateVLANLoopback(
      jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854ext.adva.OperateVLANLoopbackT mtosiBody)
      throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(OperateVlanLoopbackWorker.class, mtosiBody, mtosiHeader,
            "operateVLANLoopback", context, LOG);
  }

	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#operateLoopback(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.OperateLoopbackT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.OperateLoopbackResponseT operateLoopback(
			jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854ext.adva.OperateLoopbackT mtosiBody)
			throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(OperateLoopbackWorker.class, mtosiBody, mtosiHeader,
            "operateLoopback", context, LOG);
  }

	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#modifyFDFr(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.ModifyFDFrT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.ModifyFDFrResponseT modifyFDFr(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
			v1.tmf854ext.adva.ModifyFDFrT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(ModifyFDFrWorker.class, mtosiBody, mtosiHeader,
            "modifyFDFr", context, LOG);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#releaseVLANLoopback(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.ReleaseVLANLoopbackT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.ReleaseVLANLoopbackResponseT releaseVLANLoopback(
			jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854ext.adva.ReleaseVLANLoopbackT mtosiBody)
			throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(ReleaseVlanLoopbackWorker.class, mtosiBody, mtosiHeader,
            "releaseVLANLoopback", context, LOG);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#getContinuityTest(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.GetContinuityTestT mtosiBody )*
	 */
	@Override
  public GetContinuityTestResponseT getContinuityTest(Holder<HeaderT> mtosiHeader, GetContinuityTestT mtosiBody)
			throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetContinuityTestWorker.class, mtosiBody, mtosiHeader,
            "getContinuityTest", context, LOG);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVAFlowDomainMgr#startContinuityTest(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.StartContinuityTestT mtosiBody )*
	 */
	@Override
  public StartContinuityTestResponseT startContinuityTest(Holder<HeaderT> mtosiHeader, StartContinuityTestT mtosiBody)
			throws ProcessingFailureException {
    return ServiceUtils.runMethod(StartContinuityTestWorker.class, mtosiBody, mtosiHeader,
            "startContinuityTest", context, LOG);
	}
}
