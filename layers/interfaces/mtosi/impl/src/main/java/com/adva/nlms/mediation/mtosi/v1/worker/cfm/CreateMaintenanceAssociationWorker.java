/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.cfm;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.DTOBuilder;
import com.adva.nlms.mediation.config.dto.attr.MACompAttr;
import com.adva.nlms.mediation.config.dto.attr.MANetAttr;
import com.adva.nlms.mediation.config.dto.attr.MDAttr;
import com.adva.nlms.mediation.config.dto.attr.MEPMemberAttr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3AccAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3NetAttr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.factory.ConnectivityFaultMaintenanceFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp20X.FspGE20XCFMMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import v1.tmf854.HeaderT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.LayeredParametersT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import v1.tmf854ext.adva.CreateMaintenanceAssociationResponseT;
import v1.tmf854ext.adva.CreateMaintenanceAssociationT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.ws.Holder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CreateMaintenanceAssociationWorker extends AbstractMtosiWorker {
  private static Map<Integer, String> namePatterns = new HashMap<>();
  private static final String patternPTPForGE = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=((NET-(\\d+))|(ACC))";
  public static final String patternACC = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=(ACC)";
  public static final String patternNET = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=(NET)-(\\d+)";

  static {
    namePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201, patternPTPForGE);
    namePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE, patternPTPForGE);
  }

  private final CreateMaintenanceAssociationT mtosiBody;
  private MtosiAddress mtosiAddress;
  private MtosiMOFacade facade;
  private NetworkElement ne;
  private CreateMaintenanceAssociationResponseT response = new CreateMaintenanceAssociationResponseT();
  private ConnectivityFaultMaintenanceFactory cfmFactory = new ConnectivityFaultMaintenanceFactory();
  private LayeredParametersT parameterListType;
  private JAXBElement<LayeredParametersListT> maParamList;
  private DTO ptp;
  private DTO<MDAttr> md;
  private DTO<MANetAttr> manet;
  private List<DTO<MEPMemberAttr>> mepList = new ArrayList<>();
  private DTO<MACompAttr> macomp;
  private boolean isManetExist;
  private DTO<MANetAttr> manetToUpdate;


  public CreateMaintenanceAssociationWorker(CreateMaintenanceAssociationT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "createMaintenanceAssociation", "createMaintenanceAssociation", "createMaintenanceAssociationResponse");
    this.mtosiBody = mtosiBody;
    this.mtosiHeader = mtosiHeader;
  }


  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    validator.validate(ne.getDefaultNetworkElementTypeString());
  }

  @Override
  protected void parse() throws Exception {
    NamingAttributesT meName = mtosiBody.getCreateData().getMaName();
    maParamList = mtosiBody.getCreateData().getTransmissionParams();

    if (meName == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "MeName has not been specified or is invalid.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    mtosiAddress = new MtosiAddress(meName);
    ne = mtosiAddress.getNE();

    if (mtosiAddress.getNaming().getMdNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MD_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if (!mtosiAddress.getNaming().getMdNm().equals(OSFactory.getMDNm())) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MD_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if (mtosiAddress.getNaming().getMeNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.ME_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    MtosiUtils.validateNE(ne);

    if (mtosiAddress.getNaming().getCfmMdNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MDFR_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);

    }

    if(mtosiAddress.getNaming().getMaNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MAFR_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    facade = getMtosiCtrl().getMoFacadeManager().getFacadeViaNeId(MtosiMOFacade.class, ne.getID());
    md = facade.findDTOViaMtosiName(ne.getID(), "/cfmmd="+mtosiAddress.getNaming().getCfmMdNm(), MDAttr.class);

    if(mtosiAddress.getNaming().getPtpNm() == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MDFR_PTP_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if(!mtosiAddress.getNaming().getPtpNm().matches(namePatterns.get(ne.getNetworkElementType()))){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MDFR_PTP_INVALID);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    ptp = facade.findDTOViaMtosiName(ne.getID(), mtosiAddress.getNaming(). getPtpNm(), getPtpEntityPerNe(mtosiAddress.getNaming().getPtpNm()));

    if(ptp == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.PTP_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if((md = facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+mtosiAddress.getNaming().getCfmMdNm(), MDAttr.class)) == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MDFR_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    manet = facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+mtosiAddress.getNaming().getCfmMdNm() +" && /ma="+mtosiAddress.getNaming().getMaNm(), MANetAttr.class);
    isManetExist = manet != null;
//    if(facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+mtosiAddress.getCfmMdName() +" && /ma="+mtosiAddress.getMaName(), MANetAttr.class) != null)  {
//      throw ExceptionUtils.createNewMPFE(ExceptionUtils.EXCPT_CAPACITY_EXCEEDED,
//          MtosiErrorConstants.MANETFR_EXISTS);
//    }
    if(facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+mtosiAddress.getNaming().getCfmMdNm() +" && /ma="+mtosiAddress.getNaming().getMaNm() + " && " + mtosiAddress.getNaming().getPtpNm(), MACompAttr.class) != null)  {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_CAPACITY_EXCEEDED,
          MtosiErrorConstants.MAFR_EXISTS);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if (mtosiBody.getCreateData().getTransmissionParams() != null &&
        mtosiBody.getCreateData().getTransmissionParams().getValue() != null &&
        mtosiBody.getCreateData().getTransmissionParams().getValue().getLayeredParameters() != null &&
        mtosiBody.getCreateData().getTransmissionParams().getValue().getLayeredParameters().get(0) != null){

      parameterListType = mtosiBody.getCreateData().getTransmissionParams().getValue().getLayeredParameters().get(0);
    } else{
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "Layered Parameter List has not been specified or is invalid.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }

  @Override
  protected void mediate() throws Exception {
    if(!isManetExist){
      manet = cfmFactory.parseMANet(mtosiAddress, maParamList,getMtosiCtrl().getMoFacadeManager()).getMANet(mtosiAddress, md);
      String mepTable = manet.getValue(MANetAttr.MEP_TABLE);
      if(mepTable == null){
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.MEP_TABLE_MISSING);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      String[] mepValues = mepTable.split(",");
      for(String mepValue : mepValues){
        try{
          Integer id = Integer.parseInt(mepValue);
          DTO<MEPMemberAttr> mepMemberAttrDTO = DTOBuilder.getInstance().newDTO(MEPMemberAttr.class);
          mepMemberAttrDTO.putOrReplace(MEPMemberAttr.MEP_INDEX, id);
          mepList.add(mepMemberAttrDTO);
        } catch(Exception ex){
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MEP_TABLE_IVALID);
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }
      }
    }  else {

      if (facade.findChildrenDTOsViaParentEntityIndex(ne.getID(), manet.getValue(MANetAttr.ENTITY_INDEX), MACompAttr.class).size() > 0) {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_CAPACITY_EXCEEDED,
            MtosiErrorConstants.MANET_HAS_MACOMP);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }

      manetToUpdate = cfmFactory.parseMANet(mtosiAddress, maParamList,getMtosiCtrl().getMoFacadeManager()).getMANet(mtosiAddress, md);
      String mepTable = manetToUpdate.getValue(MANetAttr.MEP_TABLE);
      if (mepTable == null) {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.MEP_TABLE_MISSING);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      String[] mepValues = mepTable.split(",");
      for (String mepValue : mepValues) {
        if (mepValue.equals("")) {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MEP_TABLE_MISSING);
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }
        try {
          Integer id = Integer.parseInt(mepValue);
          DTO<MEPMemberAttr> mepMemberAttrDTO = DTOBuilder.getInstance().newDTO(MEPMemberAttr.class);
          mepMemberAttrDTO.putOrReplace(MEPMemberAttr.MEP_INDEX, id);
          mepList.add(mepMemberAttrDTO);
        } catch (Exception ex) {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MEP_TABLE_IVALID);
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }
      }
    }
    macomp = cfmFactory.parseMaComp(mtosiAddress, maParamList,getMtosiCtrl().getMoFacadeManager()).getMAComp(mtosiAddress, manet,ptp);
    transact();
  }

  private void transact() throws Exception {
    try {
      facade.openNetTransaction(ne.getID());
      if(!isManetExist) {
        manet = facade.createObjectOnDevice(ne.getID(), manet);
        ne.logSROperation(SROperationState.SERVICE_CREATION_SUCCESS, manet.getValue(ManagedObjectAttr.MTOSI_NAME));
        for(DTO<MEPMemberAttr> mepMember : mepList){
          mepMember.putOrReplace(MEPMemberAttr.MD_INDEX, manet.getValue(MANetAttr.MD_INDEX));
          mepMember.putOrReplace(MEPMemberAttr.MA_INDEX, manet.getValue(MANetAttr.MA_INDEX));
          facade.createObjectOnDevice(ne.getID(), mepMember);
          ne.logSROperation(SROperationState.SERVICE_CREATION_SUCCESS, manet.getValue(ManagedObjectAttr.MTOSI_NAME) + "/mep=" + mepMember.getValue(MEPMemberAttr.MEP_INDEX));
        }
      } else{
        manetToUpdate.putOrReplace(MANetAttr.ENTITY_INDEX, manet.getValue(MANetAttr.ENTITY_INDEX));
        manetToUpdate = facade.modifyObjectOnDevice(ne.getID(), manetToUpdate);
        String mepTableToDelete = manet.getValue(MANetAttr.MEP_TABLE);
        if (mepTableToDelete != null){
          String[] mepValuesToDelete = mepTableToDelete.split(",");
          for(String mepValueToDelete : mepValuesToDelete){
            Integer id = Integer.parseInt(mepValueToDelete);
            DTO<MEPMemberAttr> mepMemberAttrToDeleteDTO = DTOBuilder.getInstance().newDTO(MEPMemberAttr.class);
            mepMemberAttrToDeleteDTO.putOrReplace(MEPMemberAttr.MEP_INDEX, id);
            mepMemberAttrToDeleteDTO.putOrReplace(MEPMemberAttr.MD_INDEX, manet.getValue(MANetAttr.MD_INDEX));
            mepMemberAttrToDeleteDTO.putOrReplace(MEPMemberAttr.MA_INDEX, manet.getValue(MANetAttr.MA_INDEX));
            int[] entityIndex = new int[]{manet.getValue(MANetAttr.MD_INDEX), manet.getValue(MANetAttr.MA_INDEX), id} ;
            mepMemberAttrToDeleteDTO.putOrReplace(MEPMemberAttr.ENTITY_INDEX, new EntityIndex(entityIndex));
            facade.deleteObjectOnDevice(ne.getID(), mepMemberAttrToDeleteDTO);
          }
        }

        for(DTO<MEPMemberAttr> mepMember : mepList){
          mepMember.putOrReplace(MEPMemberAttr.MD_INDEX, manet.getValue(MANetAttr.MD_INDEX));
          mepMember.putOrReplace(MEPMemberAttr.MA_INDEX, manet.getValue(MANetAttr.MA_INDEX));
          facade.createObjectOnDevice(ne.getID(), mepMember);
          ne.logSROperation(SROperationState.SERVICE_CREATION_SUCCESS, manet.getValue(ManagedObjectAttr.MTOSI_NAME) + "/mep=" + mepMember.getValue(MEPMemberAttr.MEP_INDEX));
        }

      }
      manet = facade.findDTOViaMtosiName(ne.getID(), manet.getValue(MANetAttr.MTOSI_NAME), MANetAttr.class);
      macomp.putOrReplace(MACompAttr.MD_INDEX,manet.getValue(MANetAttr.MD_INDEX));
      macomp.putOrReplace(MACompAttr.MA_INDEX, manet.getValue(MANetAttr.MA_INDEX));
      macomp = facade.createObjectOnDevice(ne.getID(), macomp);
      ne.logSROperation(SROperationState.SERVICE_CREATION_SUCCESS, macomp.getValue(ManagedObjectAttr.MTOSI_NAME));
    } catch (ObjectInUseException | SNMPCommFailure e) {
      ne.logSROperation(SROperationState.SERVICE_CREATION_FAILURE, manet.getValue(ManagedObjectAttr.MTOSI_NAME));
      throw e;
    } finally {
      facade.closeNetTransaction(ne.getID());
    }
  }

  @Override
  protected void response() throws Exception {
    v1.tmf854ext.adva.ObjectFactory factory = new v1.tmf854ext.adva.ObjectFactory();
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    NamingAttributesT nm = NamingTranslationFactory.createNamingAttributesMaintenanceAssociation(mtosiAddress.getNaming().getMeNm(), mtosiAddress.getNaming().getCfmMdNm(),
        mtosiAddress.getNaming().getMaNm()+ " && "+ mtosiAddress.getNaming().getPtpNm());
    response.setMaName(factory.createCreateMaintenanceAssociationResponseTMaName(nm));
    response.setDiscoveredName(factory.createCreateMaintenanceDomainResponseTDiscoveredName(macomp.getValue(MACompAttr.MTOSI_NAME)));
    response.setNamingOS(factory.createCreateMaintenanceDomainResponseTNamingOS(OSFactory.getNmsName()));
    response.setSource(factory.createCreateMaintenanceDomainResponseTSource(source));
    response.setTransmissionParams(factory.createCreateMaintenanceAssociationResponseTTransmissionParams(new FspGE20XCFMMediator().toMtosiMA(manet, macomp)));
  }

  @Override
  public Object getSuccessResponse() {
    if (response == null)
      return null;
    return response;
  }

  private Class getPtpEntityPerNe(String portType) throws ProcessingFailureException {
    if (portType.matches(patternNET)) {
      return PortF3NetAttr.class;
    } else if (portType.matches(patternACC)) {
      return PortF3AccAttr.class;
    }else{
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "Port type cannot be identified for the specified Termination Point.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }

}
