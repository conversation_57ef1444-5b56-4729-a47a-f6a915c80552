/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cp;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.config.EntityClass;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.SFPSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ShelfSPProperties;
import com.adva.nlms.mediation.config.fsp150cp_mx.BitFieldHelper;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.NetworkElementFSP150CP;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.ShelfFSP150CP;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.mediation.NetworkElementMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.ManagedElementMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import v1.tmf854.EquipmentHolderT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.MEVendorExtensionsT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;

import jakarta.xml.bind.JAXBElement;
import javax.xml.namespace.QName;

/**
 * This class is a Network Element Mediator for the FSP 150 CP boxes.
 */
public class Fsp150cpMediator extends NetworkElementMediator {
  protected NetworkElementFSP150CP ne;

  public Fsp150cpMediator(NetworkElementFSP150CP ne) {
    super(ne);
    this.ne = ne;
  }

  @Override
  protected JAXBElement<MEVendorExtensionsT> getVendorExtensions() {
    ObjectFactory objFactory = new ObjectFactory();
    MEVendorExtensionsT extensions = new MEVendorExtensionsT();
    populateIpAddress(extensions);
    ManagedElementMediator.populateManagementParameters(ne, extensions);
    JAXBElement<MEVendorExtensionsT> vendorExtensions = objFactory.createManagedElementTVendorExtensions(extensions);
    final ShelfFSP150CP shelf = ne.getShelfFSP150CP();
    final ShelfSPProperties shelfSPProperties = (shelf != null ? shelf.getShelfSPProperties() : null);
    setLayersViaVendorExtensions(vendorExtensions, shelfSPProperties);
    
    //Populate Discover State 
    ManagedElementMediator.populateDiscoveryState(ne, extensions);

    return vendorExtensions;
  }

  protected void populateIpAddress (MEVendorExtensionsT extensions) {
    String ipAddress = ne.getIPAddress();
    JAXBElement<? extends String> je = new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_IPADDRESS), String.class, ipAddress);
    extensions.getAny().add(je);
  }

  private static void setLayersViaVendorExtensions(final JAXBElement<MEVendorExtensionsT> jaxbElement,
                                                   final ShelfSPProperties shelfSPProperties)
  {
    ObjectFactory objFactory = new ObjectFactory();

    final MEVendorExtensionsT extensions = jaxbElement.getValue();
    LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.ACCESS_TO_ACCESS_LINK_LOSS_FORWARDING_PARAM,
            shelfSPProperties != null
                ? MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, shelfSPProperties.get(ShelfSPProperties.VI.LinkLossFwd))
                : "");

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.PAUSE_FRAMES_ENABLED_PARAM,
            shelfSPProperties != null
                ? MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, shelfSPProperties.get(ShelfSPProperties.VI.PauseEnable))
                : "");

    String MACAddressLearning = (shelfSPProperties != null ? MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, shelfSPProperties.get(ShelfSPProperties.VI.MACAddressLearning)) : "");
    if (MACAddressLearning.equals(BooleanTypeTranslation.NOT_APPLICABLE.toString())) {
      MACAddressLearning = "";
    }
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.MAC_ADDRESS_LEARNING_PARAM, MACAddressLearning);

    String bpduForwardFilter = (shelfSPProperties != null ? BitFieldHelper.convertToBpduFilterString(shelfSPProperties.get(ShelfSPProperties.Vb.BpduFilter)) : "");
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.BPDU_FORWARDING_FILTER_PARAM, bpduForwardFilter);

    JAXBElement<LayeredParametersListT> transmissionParams = objFactory
            .createPhysicalTerminationPointTTransmissionParams(layeredParametersListT);
    extensions.getAny().add(transmissionParams);
  }

  @Override
  public String getRelativeNameForProperties(EquipmentSPProperties properties) {
    int classType = properties.get(EquipmentSPProperties.VI.ClassType);
    String relativeName = "";
    switch (classType) {
      case EntityClass.SHELF:
        relativeName = MtosiConstants.DEFAULT_SHELF_NAME;
        break;
      case EntityClass.MODULE:
        // get the sfp slot and sfp below it
        EntityIndex containedIn = properties.get(EquipmentSPProperties.VE.ContainedIn);
        if (!EntityIndex.isSpecified(containedIn)) {
          relativeName = MtosiConstants.DEFAULT_SHELF_NAME;
        } else {
          relativeName = getRelativeNameModule(properties);
        }
        break;
      case EntityClass.POWER_SUPPLY:
        relativeName = getRelativeNamePSU(properties);
        break;
      case EntityClass.PORT:
        relativeName = getRelativeNamePort(properties);
        break;
      case EntityClass.FAN:
        relativeName = getRelativeNameFan(properties);
        break;
      default:
    }
    return relativeName;
  }

  private static String getRelativeNameModule(EquipmentSPProperties properties) {
    if (!(properties instanceof SFPSPProperties)) {
      throw new IllegalArgumentException("getRelativeNameModule is accepting only SFPSPProperties!");
    }
    switch (((SFPSPProperties) properties).get(SFPSPProperties.VI.SFPNumber)) {
      case 1:
        return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT
                + MtosiConstants.PORT_NETA;
      case 2:
        return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT
                + MtosiConstants.PORT_NETB;
      case 3:
        return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.SUBSLOT_TEXT
                + MtosiConstants.PORT_ACC;
    }
    return null;
  }

  private static String getRelativeNamePSU(EquipmentSPProperties properties) {
    int chassis = properties.get(EquipmentSPProperties.VI.ChassisId);
    int relativePosition = properties.get(EquipmentSPProperties.VI.RelativePosition);
    if (relativePosition == 34) {
      if (chassis == 1) {
        return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_PSU1;
      } else {
        return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_PSU1;
      }
    } else {
      if (chassis == 1) {
        return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_PSU2;
      } else {
        return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_PSU2;
      }
    }
  }

  private static String getRelativeNamePort(EquipmentSPProperties properties) {
    if (!(properties instanceof SFPSPProperties)) {
      throw new IllegalArgumentException("getRelativeNamePort is accepting only SFPSPProperties!");
    }
    SFPSPProperties propertiesSFP = (SFPSPProperties) properties;
    int chassis = properties.get(EquipmentSPProperties.VI.ChassisId);
    int sfpNumber = propertiesSFP.get(SFPSPProperties.VI.SFPNumber);
    if (chassis == 1) {
      switch (sfpNumber) {
        case 1:
          return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.PORT_TEXT + MtosiConstants.PORT_NETA;
        case 2:
          return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.PORT_TEXT + MtosiConstants.PORT_NETB;
        case 3:
          return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.PORT_TEXT + MtosiConstants.PORT_ACC;
      }
    } else {
      switch (sfpNumber) {
        case 1:
          return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.PORT_TEXT + MtosiConstants.PORT_ACC;
        case 2:
          return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.PORT_TEXT
                  + MtosiConstants.PORT_NETA;
        case 3:
          return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.DEFAULT_SLOT_NAME + MtosiConstants.PORT_TEXT
                  + MtosiConstants.PORT_NETB;
      }
    }
    return null;
  }

  private static String getRelativeNameFan(EquipmentSPProperties properties) {
    int chassis = properties.get(EquipmentSPProperties.VI.ChassisId);
    int relativePosition = properties.get(EquipmentSPProperties.VI.RelativePosition);
    if (relativePosition == 36) {
      if (chassis == 1) {
        return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_FAN1;
      } else {
        return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_FAN1;
      }
    } else {
      if (chassis == 1) {
        return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_FAN2;
      } else {
        return MtosiConstants.DEFAULT_SHELF_NAME + MtosiConstants.SLOT_TEXT + MtosiConstants.SLOT_FAN2;
      }
    }
  }

  @Override
  protected void setExpectedOrInstalledEquipment (EquipmentHolderT equipmentHolder, NamingAttributesT namingEquipment, EquipmentSPProperties properties) {
    ObjectFactory objFactory = new ObjectFactory();
    equipmentHolder.setExpectedOrInstalledEquipment(objFactory.createEquipmentHolderTExpectedOrInstalledEquipment(namingEquipment));
  }

  @Override
  protected String getShelfEquipment (EquipmentSPProperties properties) {
    return MtosiConstants.EQUIPMENT_150CP;
  }
  /**
   * Use the productName to determine which version of the NE this CP is...
   * The choices are:
   * FSP 150CPMR AC
   * FSP 150CPMR AC NEMI
   * FSP 150CPMR DC
   * FSP 150CPMR DC NEMI 
   * 
   * If it is AC, then the only allowed PSU is PSU AC
   * If it is DC, then the only allowed PSU is PSU DC
   */
  @Override
	protected void getAcceptablePSUEquipmentTypes(ObjectFactory objFactory, EquipmentHolderT equipmentHolder, EquipmentSPProperties properties) {
		String productName = ne.getMTOSIWorker().getProductName();

		EquipmentHolderT.AcceptableEquipmentTypeList list = objFactory.createEquipmentHolderTAcceptableEquipmentTypeList();
		if (productName.indexOf("AC") > 0) {
			list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_PSU_AC);
		} else if (productName.indexOf("DC") > 0){
			list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_PSU_DC);
		} else {
			list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_PSU_AC);
			list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_PSU_DC);
		}
		equipmentHolder.setAcceptableEquipmentTypeList(objFactory.createEquipmentHolderTAcceptableEquipmentTypeList(list));
	}
  /**
   * Use the productName to determine which version of the NE this CP is...
   * The choices are:
   * FSP 150CPMR AC
   * FSP 150CPMR AC NEMI
   * FSP 150CPMR DC
   * FSP 150CPMR DC NEMI 
   * Then use the productName to determine which PSU is expected or installed.
   */
  @Override
  protected String getPsuType(EquipmentSPProperties properties) {
		String productName = ne.getMTOSIWorker().getProductName();
		if (productName.indexOf("AC") > 0) {
			return MtosiConstants.EQUIPMENT_PSU_AC;
		} else if (productName.indexOf("DC") > 0){
			return MtosiConstants.EQUIPMENT_PSU_DC;
		} else {
			return MtosiConstants.EQUIPMENT_PSU;
		}
  }
}
