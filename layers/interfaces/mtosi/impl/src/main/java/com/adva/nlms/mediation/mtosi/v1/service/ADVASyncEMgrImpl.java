/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;

import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.syncE.GetAllTDFrsWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.syncE.GetContainedCurrentTimingCTPsWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.syncE.GetTDFrWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.syncE.GetTDFrsWithTPWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.syncE.ModifyTDFrWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.syncE.RenameTDFrWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import ws.v1.tmf854ext.adva.ADVASyncEMgr;

import jakarta.annotation.Resource;
import jakarta.xml.ws.WebServiceContext;

/**
 * This class was generated by the CXF 2.0-incubator-RC Thu Jun 28 08:59:00 CEST
 * 2007 Generated source version: 2.0-incubator-RC
 *
 */
@jakarta.jws.WebService(name = "ADVASyncEMgr", serviceName = "ADVAConfigurationService", portName = "ADVASyncEMgrHttp", targetNamespace = "adva.tmf854ext.v1.ws", endpointInterface = "ws.v1.tmf854ext.adva.ADVASyncEMgr")
public class ADVASyncEMgrImpl implements ADVASyncEMgr
{
	private static final Logger LOG = LogManager.getLogger(ADVASyncEMgrImpl.class.getPackage().getName());
	@Resource
	private WebServiceContext context;


	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVASyncEMgr#renameTDFr(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.RenameTDFrT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.RenameTDFrResponseT renameTDFr(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
			v1.tmf854ext.adva.RenameTDFrT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(RenameTDFrWorker.class, mtosiBody, mtosiHeader,
            "renameTDFr", context, LOG);
  }

	

	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVASyncEMgr#getAllTDFrs(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.GetAllTDFrsT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.GetAllTDFrsResponseT getAllTDFrs(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
			v1.tmf854ext.adva.GetAllTDFrsT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllTDFrsWorker.class, mtosiBody, mtosiHeader,
            "getAllTDFrs", context, LOG);
  }


	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVASyncEMgr#getTDFr(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.GetTDFrT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.GetTDFrResponseT getTDFr(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
			v1.tmf854ext.adva.GetTDFrT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(GetTDFrWorker.class, mtosiBody, mtosiHeader,
            "getTDFr", context, LOG);
	}


	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVASyncEMgr#getTDFrsWithTP(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.GetTDFrsWithTPT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.GetTDFrsWithTPResponseT getTDFrsWithTP(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
			v1.tmf854ext.adva.GetTDFrsWithTPT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(GetTDFrsWithTPWorker.class, mtosiBody, mtosiHeader,
            "getTDFrsWithTP", context, LOG);
	}



	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVASyncEMgr#modifyTDFr(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.ModifyTDFrT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.ModifyTDFrResponseT modifyTDFr(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
			v1.tmf854ext.adva.ModifyTDFrT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(ModifyTDFrWorker.class, mtosiBody, mtosiHeader,
            "modifyTDFr", context, LOG);
	}
	
	/*
	 * (non-Javadoc)
	 *
	 * @see ws.v1.tmf854ext.adva.ADVASyncEMgr#getContainedCurrentTimingCTPs(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.GetContainedCurrentTimingCTPsT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.GetContainedCurrentTimingCTPsResponseT getContainedCurrentTimingCTPs(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
			v1.tmf854ext.adva.GetContainedCurrentTimingCTPsT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(GetContainedCurrentTimingCTPsWorker.class, mtosiBody, mtosiHeader,
            "getContainedCurrentTimingCTPs", context, LOG);
	}


}
