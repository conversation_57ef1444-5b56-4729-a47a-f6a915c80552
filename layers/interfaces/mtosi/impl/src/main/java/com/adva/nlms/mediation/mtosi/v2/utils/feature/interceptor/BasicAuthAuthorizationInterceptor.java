/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */

package com.adva.nlms.mediation.mtosi.v2.utils.feature.interceptor;


import com.adva.nlms.common.cxfrealm.MTOSIOperation;
import com.adva.nlms.common.cxfrealm.UserData;
import com.adva.nlms.common.cxfrealm.types.AuthType;
import com.adva.nlms.common.cxfrealm.types.LoginMethod;
import com.adva.nlms.common.cxfrealm.types.PasswordStorage;
import com.adva.nlms.common.security.AuthenticationTypeDTO;
import com.adva.nlms.common.security.UserPropertiesDTO;
import com.adva.nlms.mediation.mtosi.common.CxfConfigExtension;
import com.adva.nlms.mediation.mtosi.common.MtosiAutowireHelper;
import com.adva.nlms.mediation.mtosi.common.security.MtosiUserPolicyDAOImpl;
import com.adva.nlms.mediation.mtosi.v2.mtosisupport.MtosiSupportValidationHelper;
import com.adva.nlms.mediation.mtosi.v2.utils.MtosiUtils;
import com.adva.nlms.mediation.security.api.SecurityCtrl;
import com.adva.nlms.mediation.security.api.user.UserService;
import com.adva.nlms.mediation.server.ServerCtrlImpl;
import org.apache.cxf.binding.soap.SoapMessage;
import org.apache.cxf.binding.soap.interceptor.SoapHeaderInterceptor;
import org.apache.cxf.configuration.security.AuthorizationPolicy;
import org.apache.cxf.endpoint.Endpoint;
import org.apache.cxf.interceptor.Fault;
import org.apache.cxf.message.Exchange;
import org.apache.cxf.message.Message;
import org.apache.cxf.transport.Conduit;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.wss4j.common.crypto.JasyptPasswordEncryptor;
import org.apache.wss4j.common.ext.WSSecurityException;
import org.eclipse.jetty.util.security.Password;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BasicAuthAuthorizationInterceptor extends SoapHeaderInterceptor {

  private static final Logger logger = LogManager.getLogger(BasicAuthAuthorizationInterceptor.class);

  private static HashMap<String, MtosiSupportValidationHelper.UserDataEnchancement> securityMap = MtosiSupportValidationHelper.getInstance().getSecurityMap();
  private static final CxfConfigExtension cxfConfigExtension = MtosiSupportValidationHelper.getInstance().getCxfConfig();
  private static final boolean isBasicAuthEnabled = cxfConfigExtension != null  && cxfConfigExtension.getInboundAuthenticationType().equals(AuthType.BASIC);
  private static boolean isDBBased= cxfConfigExtension != null  && cxfConfigExtension.getLoginMethod().equals(LoginMethod.DATABASE);
  private static MtosiSupportValidationHelper.AccountPolicyImpl accountPolicy = MtosiSupportValidationHelper.getInstance().getAccountPolicy();
  private static final MtosiUserPolicyDAOImpl userPolicyDAO = new MtosiUserPolicyDAOImpl();

  @Autowired
  private UserService userService;
  @Autowired
  private SecurityCtrl securityCtrl;
  @Autowired
  private ServerCtrlImpl serverCtrl;

  private void setUserBeans() {
    if(userService == null)
      MtosiAutowireHelper.getInstance().autowire(this, userService);
    if(securityCtrl == null)
      MtosiAutowireHelper.getInstance().autowire(this, securityCtrl);
    if(serverCtrl == null)
      MtosiAutowireHelper.getInstance().autowire(this, serverCtrl);

  }

  @Override
  public void handleMessage(Message message) throws Fault {

    if(!isBasicAuthEnabled) return;
    AuthorizationPolicy policy = message.get(AuthorizationPolicy.class);


    // If the policy is not set, the user did not specify credentials.
    // 401 is sent to the client to indicate that authentication is required.
    if (policy == null) {
      sendErrorResponse(message, HttpURLConnection.HTTP_UNAUTHORIZED);
      return;
    }

    String username = policy.getUserName();
    String password = policy.getPassword();
    if (username == null || password == null) {
      logger.error("Authentication failed as the username and/or password was not specified.");
      sendErrorResponse(message, HttpURLConnection.HTTP_UNAUTHORIZED);
    }
    if(cxfConfigExtension.getLoginMethod().equals(LoginMethod.FILE)) {

      securityMap = MtosiSupportValidationHelper.getInstance().getSecurityMap();
      accountPolicy = MtosiSupportValidationHelper.getInstance().getAccountPolicy();

      if (securityMap.get(username) == null) {
        logger.error("Authentication failed as the received username does not exist in Mtosi Security configuration: " + username);
        sendErrorResponse(message, HttpURLConnection.HTTP_UNAUTHORIZED);
      }
    }

    // CHECK USERNAME AND PASSWORD
    if (!checkLogin(username,password, message)) {
      logger.error("handleMessage: Invalid username or password for user: " +   policy.getUserName());
      sendErrorResponse(message, HttpURLConnection.HTTP_FORBIDDEN);
    }

  }

  private boolean checkLogin(String username, String password, Message message) {
    String origPassword;
    UserPropertiesDTO userDTO=null;
    UserData userData=null;
    setUserBeans();
    if(isDBBased) {
      userDTO = userService.getUserByNameOrNull(username, AuthenticationTypeDTO.LOCAL);
      if (userDTO != null) {
        try {
          byte[] passwordForValidation = password.getBytes(StandardCharsets.UTF_8);
          if(!userService.isPasswordValid(userDTO.getId(), passwordForValidation)){
            logger.error("The specified password did not match the one in the database.");
            throw new WSSecurityException(WSSecurityException.ErrorCode.FAILED_AUTHENTICATION);
          }
          origPassword = password;
        } catch (Exception e) {
          logger.error("Password for Mtosi user: " + username + " with error " + e.getMessage());
          return false;
        }
      } else{
        logger.error("The specified username was not found in the database.");
        return false;
      }
    }else{
      MtosiSupportValidationHelper.UserDataEnchancement enchancement = securityMap.get(username);
      if(enchancement.getStatus().equals(MtosiSupportValidationHelper.AccountStatus.INVALID)){
        return false;
      }else if(userPolicyDAO.hasPasswordExpired(enchancement.getMtosiUserPolicy(),accountPolicy.getPassWillExpireIn())){
        enchancement.setStatus(MtosiSupportValidationHelper.AccountStatus.INVALID);
        enchancement.setDetails("The configured password has expired.");
        logger.error("The configured password for user " + enchancement.getUserData().getUserName() + " has expired.");
      }
      userData = enchancement.getUserData();
      if (!userData.getPassword().startsWith(MtosiUtils.OBFUSCATE) && cxfConfigExtension.getPasswordStorage().equals(PasswordStorage.OBFUSCATE)){
        origPassword = Password.obfuscate(userData.getPassword());
      }else if(cxfConfigExtension.getPasswordStorage().equals(PasswordStorage.ENCRYPT)){
        JasyptPasswordEncryptor encryptor=new JasyptPasswordEncryptor("rf45FR%$","PBEWithMD5AndDES");
        origPassword  = encryptor.decrypt(userData.getPassword());
      }else{
        origPassword = userData.getPassword();
      }
    }
    if(!origPassword.equals(password)){
      logger.error("Callback supplied invalid password for: " + username);
      return false;
    }
    if(userDTO != null && !authorized(userDTO,message)){
      logger.error("Authorization failed as the received username does not have permission to execute the specified action.");
      return false;
    }
    if(userData != null && !authorized(userData,message)){
      logger.error("Authorization failed as the received username does not have permission to execute the specified action.");
      return false;
    }

    return true;
  }

  private boolean authorized(UserPropertiesDTO userDTO, Message data){
    boolean isAllowed=false;
    if (data instanceof SoapMessage &&  data.get("SOAPAction") != null){ //for requests only
      String soapAction = (String)data.get("SOAPAction");
      isAllowed = securityCtrl.isActionAllowed(soapAction, userDTO.getId());
      if(!isAllowed)
        logger.error("The specified user does not have permission to execute the operation.");
    }
    return isAllowed;
  }

  private boolean authorized(UserData userData, Message data){
    if (data instanceof SoapMessage &&  data.get("SOAPAction") != null){ //for requests only
      String soapAction = (String)data.get("SOAPAction");
      for(MTOSIOperation operation : userData.getOperationNamesList().getMTOSIOperation()){
        if(operation.getName().toString().equals(soapAction)) return true;
      }
    }
    return false;
  }


//  private String getPasswordByName(String userName){
//    for(MtosiSupportValidationHelper.UserData user : userData){
//      if(user.getUserName().equals(userName)) return user.getPassword();
//    }
//    return null;
//  }

  private void sendErrorResponse(Message message, int responseCode) {
    Message outMessage = getOutMessage(message);
    outMessage.put(Message.RESPONSE_CODE, responseCode);

    // Set the response headers
    @SuppressWarnings("unchecked")
    Map<String, List<String>> responseHeaders =  (Map<String, List<String>>)    message.get(Message.PROTOCOL_HEADERS);

    if (responseHeaders != null) {
      responseHeaders.put("WWW-Authenticate", Arrays.asList(new String[] { "Basic realm=realm" }));
      responseHeaders.put("Content-Length", Arrays.asList(new String[] { "0" }));
    }
    message.getInterceptorChain().abort();
    try {
      getConduit(message).prepare(outMessage);
      close(outMessage);
    } catch (IOException e) {
      e.printStackTrace();
    }
  }

  private Message getOutMessage(Message inMessage) {
    Exchange exchange = inMessage.getExchange();
    Message outMessage = exchange.getOutMessage();
    if (outMessage == null) {
      Endpoint endpoint = exchange.get(Endpoint.class);
      outMessage = endpoint.getBinding().createMessage();
      exchange.setOutMessage(outMessage);
    }
    outMessage.putAll(inMessage);
    return outMessage;
  }

  private Conduit getConduit(Message inMessage) throws IOException {
    Exchange exchange = inMessage.getExchange();
//    EndpointReferenceType target = exchange.get(EndpointReferenceType.class);
    Conduit conduit = exchange.getDestination().getBackChannel(inMessage);
    exchange.setConduit(conduit);
    return conduit;
  }

  private void close(Message outMessage) throws IOException {
    OutputStream os = outMessage.getContent(OutputStream.class);
    os.flush();
    os.close();
  }
}