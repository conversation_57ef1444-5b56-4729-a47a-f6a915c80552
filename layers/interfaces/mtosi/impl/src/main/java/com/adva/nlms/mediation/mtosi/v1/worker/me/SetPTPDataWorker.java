/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.mediation.common.serviceProvisioning.ACCPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.BITSPortSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.NETPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.ServiceSPPropertiesFSP150CP;
import com.adva.nlms.mediation.common.serviceProvisioning.WANPortSPPropertiesFSP150CP;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.f3.entity.port.acc.MTOSIPortF3Acc;
import com.adva.nlms.mediation.config.f3.entity.port.bits.PortBitsF3Impl;
import com.adva.nlms.mediation.config.f3.entity.port.net.MTOSIPortF3Net;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.PortFSP150CPAccess;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.PortFSP150CPNetwork;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.TPDataT;
import v1.tmf854.TerminationPointT;
import v1.tmf854ext.adva.SetTPDataResponseT;

import jakarta.xml.ws.Holder;


public class SetPTPDataWorker extends SetTPDataWorker {
  Logger LOG = LogManager.getLogger(this.getClass().getName());

  public SetPTPDataWorker (Holder<HeaderT> mtosiHeader, TPDataT tpInfo, NamingAttributesT namingAttributes, NetworkElement ne) {
    super(mtosiHeader, tpInfo, namingAttributes, ne);
  }

  @Override
  protected void parse () throws Exception {
    // empty method
  }

  @Override
  protected void mediate() throws Exception {
    Port port = ManagedElementFactory.getPort(namingAttributes);

    if (port instanceof PortBitsF3Impl) {
      mediateBits((PortBitsF3Impl) port);
    } else if (port instanceof MTOSIPortF3Acc) {
      mediateCMAcc((MTOSIPortF3Acc) port);
    } else if (port instanceof MTOSIPortF3Net) {
      mediateCMNet((MTOSIPortF3Net) port);
    } else if (port instanceof PortFSP150CPAccess) {
      mediateCPLAN((PortFSP150CPAccess) port);
    } else if (port instanceof PortFSP150CPNetwork) {
        mediateCPWAN((PortFSP150CPNetwork) port);
    }
  }

  private void mediateBits(PortBitsF3Impl port) throws Exception {
	  BITSPortSPProperties newProps = MtosiTPMediator.mtosiTPDataTToBitsProperties(tpInfo, port);
	  transactBits(port, newProps);
  }

  private void mediateCMAcc(MTOSIPortF3Acc port) throws Exception {
    ACCPortSPPropertiesFSP150CM newProps = MtosiTPMediator.mtosiTPDataTToCMACCProperties(tpInfo, port);
    transactCMAcc(port, newProps/*, flow, cfmProps*/);
  }

  private void mediateCMNet(MTOSIPortF3Net port) throws Exception {
    NETPortSPPropertiesFSP150CM newProps = MtosiTPMediator.mtosiTPDataTToCMNETProperties(tpInfo, port);
    transactCMNet(port, newProps);
  }

  private void mediateCPLAN(PortFSP150CPAccess port) throws Exception {
    ServiceSPPropertiesFSP150CP newProps = MtosiTPMediator.mtosiTPDataTToCPAccessProperties(tpInfo, port);
    transactCPLAN(port, newProps);
  }

  private void mediateCPWAN(PortFSP150CPNetwork port) throws Exception {
	    WANPortSPPropertiesFSP150CP newProps = MtosiTPMediator.mtosiTPDataTToCPNetworkProperties(tpInfo, port);
	    transactCPWAN(port, newProps);
	  }

  private void transactBits(PortBitsF3Impl port, BITSPortSPProperties newProps) throws ObjectInUseException, NetTransactionException,
			SPValidationException, SNMPCommFailure {
		NetworkElement locks[] = new NetworkElement[] { ne };
		Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "SetTPDataWorker");
		try {
			port.setSettings(newProps);
			NetTransactionManager.commitNetTransaction(id);
		} catch (NetTransactionException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SPValidationException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SNMPCommFailure e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} finally {
			NetTransactionManager.ensureEnd(id);
		}
	}

  private void transactCMAcc(MTOSIPortF3Acc port, ACCPortSPPropertiesFSP150CM props
                             /*, FlowFSP150CM flow, CFMCCMSPPropertiesFSP150CC propsCfm*/) throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "SetTPDataWorker");
    try {
        logSecurity(ne, SystemAction.ModifyNetwork, port.getMtosiName());
        port.setSettings(props);
      NetTransactionManager.commitNetTransaction(id);
    } catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      NetTransactionManager.ensureEnd(id);
    }
  }

  private void transactCPLAN(PortFSP150CPAccess port, ServiceSPPropertiesFSP150CP props)
          throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure
  {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "SetTPDataWorker");
    try {
      logSecurity(ne, SystemAction.ModifyNetwork, port.getMtosiName());
      port.setSettings(props);
      NetTransactionManager.commitNetTransaction(id);
    } catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      NetTransactionManager.ensureEnd(id);
    }
  }

  private void transactCMNet(MTOSIPortF3Net port, NETPortSPPropertiesFSP150CM props)
          throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "SetTPDataWorker");
    try {
      logSecurity(ne, SystemAction.ModifyNetwork, port.getMtosiName());
      port.setSettings(props);
      NetTransactionManager.commitNetTransaction(id);
    } catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      NetTransactionManager.ensureEnd(id);
    }
  }

  private void transactCPWAN(PortFSP150CPNetwork port, WANPortSPPropertiesFSP150CP props)
          throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "SetTPDataWorker");
    try {
      logSecurity(ne, SystemAction.ModifyNetwork, port.getMtosiName());
      port.setSettings(props);
      NetTransactionManager.commitNetTransaction(id);
    } catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      NetTransactionManager.ensureEnd(id);
    }
  }

  @Override
  protected void response() throws Exception {
    final Port port = ManagedElementFactory.getPort(namingAttributes);
    ObjectFactory objectFactory = new ObjectFactory();
    PhysicalTerminationPointT ptp;
    MtosiTranslator mtosiTranslator;
    if ((mtosiTranslator = new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(port)) != null) {
//    if (port instanceof MtosiSupported) {
      ptp = mtosiTranslator.toMtosiPTP();
//      ptp = ((MtosiSupported)port).getMtosiTranslator().toMtosiPTP();
    } else {
      ptp = objectFactory.createPhysicalTerminationPointT();
    }

    TerminationPointT tp = objectFactory.createTerminationPointT();
    tp.setPtp(ptp);
    response.setModifiedTP(tp);
  }

  @Override
  public SetTPDataResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}