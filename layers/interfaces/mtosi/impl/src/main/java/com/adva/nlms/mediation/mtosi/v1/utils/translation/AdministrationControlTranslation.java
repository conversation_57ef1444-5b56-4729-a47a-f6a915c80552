/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum AdministrationControlTranslation implements TranslatableEnum, com.adva.nlms.mediation.mtosi.v2.utils.translations.f3.TranslatableEnum {
  ENABLED        (1, "Enabled"),
  DISABLED       (2, "Disabled"),
  TEST           (3, "PROP_ADVA_Testing"),
  NOT_APPLICABLE (4, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private AdministrationControlTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}