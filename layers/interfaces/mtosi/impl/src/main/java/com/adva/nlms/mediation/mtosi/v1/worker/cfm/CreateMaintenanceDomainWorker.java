/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.cfm;

import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.MDAttr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.factory.ConnectivityFaultMaintenanceFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp20X.FspGE20XCFMMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import v1.tmf854.HeaderT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.LayeredParametersT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import v1.tmf854ext.adva.CreateMaintenanceDomainResponseT;
import v1.tmf854ext.adva.CreateMaintenanceDomainT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.ws.Holder;

public class CreateMaintenanceDomainWorker extends AbstractMtosiWorker {
   private final CreateMaintenanceDomainT mtosiBody;
  private MtosiAddress mtosiAddress;
  private MtosiMOFacade facade;
  private NetworkElement ne;
  private DTO<MDAttr> md;
  private CreateMaintenanceDomainResponseT response = new CreateMaintenanceDomainResponseT();
  private ConnectivityFaultMaintenanceFactory cfmFactory = new ConnectivityFaultMaintenanceFactory();
  private LayeredParametersT parameterListType;
  private JAXBElement<LayeredParametersListT> mdParamList;


  public CreateMaintenanceDomainWorker(CreateMaintenanceDomainT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "createMaintenanceDomain", "createMaintenanceDomain", "createMaintenanceDomainResponse");
    this.mtosiBody = mtosiBody;
    this.mtosiHeader = mtosiHeader;
  }


  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    validator.validate(ne.getDefaultNetworkElementTypeString());
  }

  @Override
  protected void parse() throws Exception {
    NamingAttributesT meName = mtosiBody.getCreateData().getMdName();
    mdParamList = mtosiBody.getCreateData().getTransmissionParams();

    if (meName == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "MeName has not been specified or is invalid.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    mtosiAddress = new MtosiAddress(meName);
    ne = mtosiAddress.getNE();

    if (mtosiAddress.getNaming().getMdNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MD_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if (!mtosiAddress.getNaming().getMdNm().equals(OSFactory.getMDNm())) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MD_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if (mtosiAddress.getNaming().getMeNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.ME_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    MtosiUtils.validateNE(ne);

    if (mtosiAddress.getNaming().getCfmMdNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MDFR_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);

    }

    facade = getMtosiCtrl().getMoFacadeManager().getFacadeViaNeId(MtosiMOFacade.class, ne.getID());
    md = facade.findDTOViaMtosiName(ne.getID(), "/cfmmd="+mtosiAddress.getNaming().getCfmMdNm(), MDAttr.class);
    if (md != null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_CAPACITY_EXCEEDED,
          MtosiErrorConstants.MDFR_EXISTS);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if (mdParamList != null && mdParamList.getValue() != null && mdParamList.getValue().getLayeredParameters() != null &&
        mdParamList.getValue().getLayeredParameters().size() > 0 &&
        mdParamList.getValue().getLayeredParameters().get(0) != null){

      parameterListType = mtosiBody.getCreateData().getTransmissionParams().getValue().getLayeredParameters().get(0);
    } else{
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "The request is not valid.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }

  @Override
  protected void mediate() throws Exception {
    md = cfmFactory.parseMD(mtosiAddress, mdParamList,getMtosiCtrl().getMoFacadeManager()).getMD(mtosiAddress.getNaming().getCfmMdNm());
    transact();
  }

  private void transact() throws Exception {
    try {
      facade.openNetTransaction(ne.getID());
      md = facade.createObjectOnDevice(ne.getID(),md);
      ne.logSROperation(SROperationState.SERVICE_CREATION_SUCCESS, md.getValue(ManagedObjectAttr.MTOSI_NAME));
    } catch (ObjectInUseException | SNMPCommFailure e) {
      ne.logSROperation(SROperationState.SERVICE_CREATION_FAILURE, md.getValue(ManagedObjectAttr.MTOSI_NAME));
      throw e;
    } finally {
      facade.closeNetTransaction(ne.getID());
    }
  }

  @Override
  protected void response() throws Exception {
    v1.tmf854ext.adva.ObjectFactory factory = new v1.tmf854ext.adva.ObjectFactory();
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    response.setMdName(factory.createCreateMaintenanceDomainResponseTMdName(mtosiBody.getCreateData().getMdName()));
    response.setDiscoveredName(factory.createCreateMaintenanceDomainResponseTDiscoveredName(mtosiAddress.getNaming().getCfmMdNm()));
    response.setNamingOS(factory.createCreateMaintenanceDomainResponseTNamingOS(OSFactory.getNmsName()));
    response.setSource(factory.createCreateMaintenanceDomainResponseTSource(source));
    response.setTransmissionParams(factory.createCreateMaintenanceDomainResponseTTransmissionParams(new FspGE20XCFMMediator().toMtosiMD(md)));
  }

  @Override
  public Object getSuccessResponse() {
    if (response == null)
      return null;
    return response;
  }
}
