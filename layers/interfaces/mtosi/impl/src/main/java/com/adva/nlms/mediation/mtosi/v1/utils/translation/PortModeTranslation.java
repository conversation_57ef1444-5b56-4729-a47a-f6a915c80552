/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.common.snmp.MIBFSP150CC825;

/**
 * Created by IntelliJ IDEA. User: Lukasz Date: 2007-05-30 Time: 13:14:09 To change this template use File | Settings |
 * File Templates.
 */
public enum PortModeTranslation{
	CO_TLS         (MIBFSP150CC825.EthernetPBSrvTable.PORT_MODE_CONNECTION_ORIENTED_VALUE, MIBFSP150CC825.EthernetPBSrvTable.TYPE_CO_TLS, "CO-TLS"), //connection-oriented
	CO_TVLS        (MIBFSP150CC825.EthernetPBSrvTable.PORT_MODE_CONNECTION_ORIENTED_VALUE, MIBFSP150CC825.EthernetPBSrvTable.TYPE_CO_TVLS, "CO-TVLS"),//connection-oriented
	CL             (MIBFSP150CC825.EthernetPBSrvTable.PORT_MODE_CONNECTION_LESS_VALUE, 1, "CL"),     //connection-less
	NOT_APPLICABLE (3, 3, "n/a");

	//------------------------------------------------------------------------------------------------------------------
	private final int    portMode;
	private final int    portType;
	private final String mtosiString;

	//------------------------------------------------------------------------------------------------------------------
	/**
	 * Constructor.
	 * @param portMode    The MIB defined value
	 * @param mtosiString  The string representation used in MTOSI layer.
	 */
	private PortModeTranslation (final int portMode, final int portType, final String mtosiString)
	{
		this.portMode    = portMode; //CL, CO
		this.portType    = portType; //TLS, TVLS
		this.mtosiString = mtosiString;
	}
	/**
	 * Returns the MIB defined value.
	 * @return the MIB defined value.
	 */
	public int getPortModeValue () {
		return portMode;
	}

	/**
	 * Returns the MIB defined value.
	 * @return the MIB defined value.
	 */
	public int getPortTypeValue () {
		return portType;
	}


	/**
	 * Returns the string representation used in MTOSI layer.
	 * @return the string representation used in MTOSI layer.
	 */
	public String getMtosiString() {
		return mtosiString;
	}

	/**
	 * Returns the string representation used in MTOSI layer.
	 * @param portMode  The MIB defined value
	 * @return the string representation used in MTOSI layer.
	 */
	public static String getMtosiString(final int portMode, final int portType)
	{
		PortModeTranslation portModeTranslation = NOT_APPLICABLE;  // the return value

		for (PortModeTranslation tmpPortModeTranslation : values())
		{
			if (portMode == tmpPortModeTranslation.getPortModeValue() && portType == tmpPortModeTranslation.getPortTypeValue())
			{
				portModeTranslation = tmpPortModeTranslation;
				break;
			}
		}
		return portModeTranslation.getMtosiString();
	}
	/**
	 * Returns the string representation used in MTOSI layer.
	 * @param mtosiString  The MIB defined value
	 * @return the string representation used in MTOSI layer.
	 */
	public static int getPortModeValue (final String mtosiString, final int portType)
	{
		PortModeTranslation portModeTranslation = NOT_APPLICABLE;  // the return value

		for (PortModeTranslation tmpPortModeTranslation : values())
		{
			if (mtosiString.equals(tmpPortModeTranslation.getMtosiString()) && portType == tmpPortModeTranslation.getPortTypeValue())
			{
				portModeTranslation = tmpPortModeTranslation;
				break;
			}
		}
		return portModeTranslation.getPortModeValue();

	}
	/**
	 * Returns the string representation used in MTOSI layer.
	 * @param mtosiString  The MIB defined value
	 * @return the string representation used in MTOSI layer.
	 */
	public static int getPortTypeValue (final String mtosiString, final int portMode)
	{
		PortModeTranslation portModeTranslation = NOT_APPLICABLE;  // the return value

		for (PortModeTranslation tmpPortModeTranslation : values())
		{
			if (mtosiString.equals(tmpPortModeTranslation.getMtosiString()) && portMode == tmpPortModeTranslation.getPortModeValue())
			{
				portModeTranslation = tmpPortModeTranslation;
				break;
			}
		}
		return portModeTranslation.getPortTypeValue();
	}
}
