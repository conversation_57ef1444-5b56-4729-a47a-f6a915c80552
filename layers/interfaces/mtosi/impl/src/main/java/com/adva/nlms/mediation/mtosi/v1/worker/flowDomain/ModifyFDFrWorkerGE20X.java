/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import jakarta.xml.ws.Holder;

import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.ModifyFDFrT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesFSP150CM;
import com.adva.nlms.mediation.config.mtosi.FDFr;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;

public class ModifyFDFrWorkerGE20X extends ModifyFDFrWorkerCM {

	public ModifyFDFrWorkerGE20X(ModifyFDFrT mtosi<PERSON>ody, Holder<HeaderT> mtos<PERSON><PERSON>eader, NamingAttributesT namingAttributes, FDFr fdfr, NetworkElement ne) {
		super(mtosi<PERSON><PERSON>, mtosiHeader, namingAttributes, fdfr, ne);
	}
	/**
	 * Extract the device specific properties
	 * @return
	 */
	@Override
  protected FlowSPPropertiesFSP150CM getCTPProperties()  throws ProcessingFailureException{
		return MtosiTPMediator.mtosiTPDataTToGE_CTPProperties(getTpDataFlowCM(), getFlowCM());
	}

}
