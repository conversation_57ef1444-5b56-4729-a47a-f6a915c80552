/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation;

import v1.tmf854.ConnectionTerminationPointT;
import v1.tmf854.FloatingTerminationPointT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.TPDataT;
import v1.tmf854.ConnectionTerminationPointListT;
import v1.tmf854.TPDataListT;
import v1.tmf854ext.adva.FlowDomainFragmentT;
import v1.tmf854ext.adva.TCProfileT;
import v1.tmf854ext.adva.TimingDomainFragmentT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.config.f3.entity.syncref.F3SyncRefImpl;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;

import java.util.Set;

/**
 * This class is a generic MTOSI Translator - all methods throws PFE.
 */
public abstract class MtosiTranslator {
  public ConnectionTerminationPointT toMtosiCTP() throws ProcessingFailureException {
    throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.MTOSI_NOT_SUPPORTED);
  }

  public ConnectionTerminationPointListT toMtosiCTPs() throws ProcessingFailureException {
    throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.MTOSI_NOT_SUPPORTED);
  }

  public TPDataListT toMtosiTPs(Set<F3SyncRefImpl> f3SyncRefs) throws ProcessingFailureException {
    throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.MTOSI_NOT_SUPPORTED);
  }

  public TPDataT toMtosiCTPasTPDataT() throws ProcessingFailureException {
    ObjectFactory factory = new ObjectFactory();
    ConnectionTerminationPointT ctp = toMtosiCTP();
    TPDataT tpData = factory.createTPDataT();
    tpData.setTpName(ctp.getName().getValue());
    tpData.setTransmissionParams(ctp.getTransmissionParams());
    return tpData;
  }

  public PhysicalTerminationPointT toMtosiPTP() throws ProcessingFailureException {
    throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.MTOSI_NOT_SUPPORTED);
  }
  public TPDataT toMtosiPTPasTPDataT() throws ProcessingFailureException {
    ObjectFactory factory = new ObjectFactory();
    PhysicalTerminationPointT ptp = toMtosiPTP();
    TPDataT tpData = factory.createTPDataT();
    tpData.setTpName(ptp.getName().getValue());
    tpData.setTransmissionParams(ptp.getTransmissionParams());
    return tpData;
  }

  public TCProfileT toMtosiTCProfile() throws ProcessingFailureException {
    throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.MTOSI_NOT_SUPPORTED);
  }

  public FloatingTerminationPointT toMtosiFTP() throws ProcessingFailureException {
    throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.MTOSI_NOT_SUPPORTED);
  }
  public TPDataT toMtosiFTPasTPDataT() throws ProcessingFailureException {
		ObjectFactory factory = new ObjectFactory();
    FloatingTerminationPointT ftp = toMtosiFTP();
    TPDataT tpData = factory.createTPDataT();
		tpData.setTpName(ftp.getName().getValue());
		tpData.setTransmissionParams(ftp.getTransmissionParams());
		return tpData;
	}

  public FlowDomainFragmentT toMtosiFDFr() throws ProcessingFailureException {
    throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.MTOSI_NOT_SUPPORTED);
  }

  public TimingDomainFragmentT toMtosiTDFr() throws ProcessingFailureException {
    throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.MTOSI_NOT_SUPPORTED);
  }
}
