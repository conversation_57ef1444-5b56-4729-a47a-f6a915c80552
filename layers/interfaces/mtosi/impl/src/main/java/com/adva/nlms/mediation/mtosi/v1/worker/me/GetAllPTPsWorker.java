/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;


import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import v1.tmf854.GetAllPTPsResponseT;
import v1.tmf854.GetAllPTPsT;
import v1.tmf854.HeaderT;
import v1.tmf854.LayerRateListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.TerminationPointListT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;


/**
 * main class for the MTOSI operation:  g e t A l l P T P s
 */
public class GetAllPTPsWorker extends AbstractMtosiWorker {
  protected GetAllPTPsT mtosiBody;
  protected GetAllPTPsResponseT response = new GetAllPTPsResponseT();
  protected NamingAttributesT meName;
  protected TerminationPointListT terminationPointListT;
  private NetworkElement ne;

  public GetAllPTPsWorker (final GetAllPTPsT mtosiBody, final Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getAllPTPs", "getAllPTPs", "getAllPTPsResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    meName = mtosiBody.getMeName();

    if (meName == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
        MtosiErrorConstants.ME_NAME_MISSING);
    }

    if (meName.getMdNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
        MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!meName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        MtosiErrorConstants.MD_NOT_FOUND);
    }
	ne = ManagedElementFactory.getAndValidateNE(meName);
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(ne.getDefaultNetworkElementTypeString());
  }

  @Override
  protected void mediate() throws Exception {
    final LayerRateListT tpLayerRateListT = mtosiBody.getTpLayerRateList();
    final LayerRateListT connectionLayerRateList = mtosiBody.getConnectionLayerRateList();
    terminationPointListT = ManagedElementFactory.getAllPTPs(meName, ne,tpLayerRateListT, connectionLayerRateList);
  }

  @Override
  protected void response() throws Exception {
    response.setTpList(terminationPointListT);
  }

  @Override
  public GetAllPTPsResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
