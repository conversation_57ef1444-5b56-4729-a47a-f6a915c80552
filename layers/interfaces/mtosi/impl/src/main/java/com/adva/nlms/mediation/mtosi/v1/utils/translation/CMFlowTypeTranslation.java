/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.common.snmp.MIBFSP150CM;

public enum CMFlowTypeTranslation implements TranslatableEnum {
  PORT_BASED     (MIBFSP150CM.Facility.FlowTable.FLOW_TYPE_PORT_BASED, "PortBased"),
  REGULAR        (MIBFSP150CM.Facility.FlowTable.FLOW_TYPE_REGULAR, "Regular"),
  DEFAULT        (MIBFSP150CM.Facility.FlowTable.FLOW_TYPE_DEFAULT, "Default"),
  NOT_APPLICABLE (4, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private CMFlowTypeTranslation (final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}