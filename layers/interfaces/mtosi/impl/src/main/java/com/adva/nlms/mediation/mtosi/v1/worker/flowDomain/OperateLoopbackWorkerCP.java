/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.serviceProvisioning.ServiceSPPropertiesFSP150CP;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.fsp150cp_mx.PortFSP150CP_MXAccess;
import com.adva.nlms.mediation.config.fsp150cp_mx.PortFSP150CP_MXNetwork;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiFDFrMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.OperateLoopbackT;

import jakarta.xml.ws.Holder;

public class OperateLoopbackWorkerCP extends OperateLoopbackWorker
{
  public OperateLoopbackWorkerCP (OperateLoopbackT mtosiBody, Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne)
  {
    super(mtosiBody, mtosiHeader, namingAttributes, ne);
  }

  @Override
  protected void parse() throws Exception {
    super.parse();
    if (!NamingTranslationFactory.isPort(namingAttributes)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ptpNm has not been specified.");
    }
  }

  @Override
  protected void mediate() throws Exception {
    Port port = ManagedElementFactory.getPort(ne, tpName);
    if (port == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The specified Port was not found.");
    }
    if (port instanceof PortFSP150CP_MXNetwork) {
      PortFSP150CP_MXNetwork wanPort = (PortFSP150CP_MXNetwork) port;
      MtosiFDFrMediator.mtosiLoopbackParametersToCPWANProperties(loopbackType, wanPort);
      transactWAN(wanPort);
    } else if (port instanceof PortFSP150CP_MXAccess) {
      PortFSP150CP_MXAccess accessPort = (PortFSP150CP_MXAccess) port;
      ServiceSPPropertiesFSP150CP props = MtosiFDFrMediator.mtosiLoopbackParametersToCPServiceProperties(
              loopbackType, loopbackParameters, accessPort);
      transactLAN(accessPort, props);
    } else {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The specified Port type does not support Loopback.");
    }
  }

  @Override
  protected void response () throws Exception {
    // empty method
  }

  private void transactWAN(PortFSP150CP_MXNetwork port) throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure
  {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "operateLoopback");
    try
    {
      logSecurity(ne, SystemAction.OperateLoopback, port.getMtosiName());
      port.operateLoopback();
      NetTransactionManager.commitNetTransaction(id);
    }
    catch (NetTransactionException e)
    {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    catch (SPValidationException e)
    {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    catch (SNMPCommFailure e)
    {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    finally
    {
      NetTransactionManager.ensureEnd(id);
    }
  }

  private void transactLAN(PortFSP150CP_MXAccess port, ServiceSPPropertiesFSP150CP props) throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure
  {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "operateLoopback");
    try
    {
      logSecurity(ne, SystemAction.OperateLoopback, port.getMtosiName());
      port.operateLoopback(props);
      NetTransactionManager.commitNetTransaction(id);
    }
    catch (NetTransactionException e)
    {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    catch (SPValidationException e)
    {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    catch (SNMPCommFailure e)
    {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    }
    finally
    {
      NetTransactionManager.ensureEnd(id);
    }
  }
}