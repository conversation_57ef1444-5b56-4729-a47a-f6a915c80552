/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi;

import com.adva.nlms.common.config.ResponseStatus;
import com.adva.nlms.mediation.common.serviceProvisioning.NetworkElementSPProperties;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementDAO;
import com.adva.nlms.mediation.mtosi.common.mtosisupport.ManagedElementTypes;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.NetworkElementMediatorFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ManagedElementUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPPropertiesData;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.CommunicationStateT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.MEFilterT;
import v1.tmf854.MEVendorExtensionsT;
import v1.tmf854.ManagedElementListT;
import v1.tmf854.ManagedElementT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ResourceStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import javax.xml.namespace.QName;
import java.util.Set;

/**
 * The mediator that collects information from the MO layer and composes the
 * responses for the ManagedElementRetrieval set of operations.
 */
public class ManagedElementMediator {
  private static Logger logger = LogManager.getLogger(ManagedElementMediator.class.getPackage().getName());

  private static NetworkElementDAO networkElementDAO = new NetworkElementDAO();

  public static ManagedElementListT nmsNeListToMtosiManagedElementList(Set<NetworkElement> set) {
    ManagedElementListT list = new ManagedElementListT();
    for (NetworkElement networkElement : set) {
      try {
        ManagedElementT nextME = NetworkElementMediatorFactory.createNetworkElementMediator((NetworkElement) networkElement).toManagedElement();
        list.getMe().add(nextME);
      } catch (Exception e) {
        logger.error(e);
      }
    }
    return list;
  }

  public static ManagedElementListT nmsNeListToMtosiManagedElementList(Set<NetworkElement>  set, MEFilterT filter)
          throws ProcessingFailureException {
    ManagedElementListT list = new ManagedElementListT();
    for (NetworkElement networkElement : set) {
      ManagedElementT nextME = NetworkElementMediatorFactory.createNetworkElementMediator((NetworkElement) networkElement).toManagedElement();
      if (managedElementPassesFilter(nextME, filter)) {
        try {
          list.getMe().add(nextME);
        } catch (Exception e) {
          logger.error(e);
        }
      }
    }
    return list;
  }

  public static NamingAttributesListT nmsNeListToMtosiManagedElementNamesList(Set<NetworkElement> set) {
    NamingAttributesListT list = new NamingAttributesListT();
    for (NetworkElement networkElement : set) {
      NamingAttributesT nextMEName = NetworkElementMediatorFactory.createNetworkElementMediator((NetworkElement) networkElement).createNamingAttributesMe();
      list.getName().add(nextMEName);
    }
    return list;
  }

  public static NamingAttributesListT nmsNeListToMtosiManagedElementNamesList(Set<NetworkElement> set, MEFilterT filter)
          throws ProcessingFailureException {
    NamingAttributesListT list = new NamingAttributesListT();
    for (NetworkElement networkElement : set) {
      ManagedElementT nextME = NetworkElementMediatorFactory.createNetworkElementMediator((NetworkElement) networkElement).toManagedElement();
      if (managedElementPassesFilter(nextME, filter)) {
        NamingAttributesT nextMEName = NetworkElementMediatorFactory.createNetworkElementMediator((NetworkElement) networkElement).createNamingAttributesMe();
        list.getName().add(nextMEName);
      }
    }
    return list;
  }

  public static ManagedElementT nmsNeToManagedElementT(NetworkElement ne) {
    // Managed-Elements contain device specific vendor extensions
    // use NetworkElementMediator object
    if (ManagedElementTypes.isManageElement(ne.getNetworkElementType())) {
      return NetworkElementMediatorFactory.createNetworkElementMediator(ne).toManagedElement();
    }

    ObjectFactory objFactory = new ObjectFactory();
    NetworkElementSPProperties props = ne.getSysInfo();
    ManagedElementT managedElement = objFactory.createManagedElementT();
    // Name
    NamingAttributesT namingAttributes = NamingTranslationFactory.createNamingAttributes(ne);
    JAXBElement<NamingAttributesT> managedElementName = objFactory.createManagedElementTName(namingAttributes);
    managedElement.setName(managedElementName);
    // Discovered Name
    managedElement.setDiscoveredName(props.get(NetworkElementSPProperties.VS.SysName));
    // Naming OS
    JAXBElement<String> namingOS = objFactory.createManagedElementTNamingOS(OSFactory.getNmsName());
    managedElement.setNamingOS(namingOS);
    // User Label
    // Not supported.
    // Source
    // Hardcoded - Discovered from an EMS (not OS and not directly from the ME).
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    JAXBElement<SourceT> managedElementSource = objFactory.createManagedElementTSource(source);
    managedElement.setSource(managedElementSource);
    // Owner
    JAXBElement<String> owner = objFactory.createManagedElementTOwner(props.get(NetworkElementSPProperties.VS.SysContact));
    managedElement.setOwner(owner);
    // Alias Name List
    // Not supported.
    // Resource State
    ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ne.isDiscovered() ? ResourceStateEnumT.INSTALLED : ResourceStateEnumT.PLANNED);
    JAXBElement<ResourceStateT> managedElementResourceState = objFactory.createManagedElementTResourceState(resourceState);
    managedElement.setResourceState(managedElementResourceState);
    // Location
    JAXBElement<String> location = objFactory.createManagedElementTLocation(props.get(NetworkElementSPProperties.VS.SysLocation));
    managedElement.setLocation(location);
    // Manufacturer
    JAXBElement<String> managedElementManufacturer = objFactory.createManagedElementTManufacturer(ne.getMTOSIWorker().getManufacturer());
    managedElement.setManufacturer(managedElementManufacturer);
    // Manufacturer date
    JAXBElement<String> manufacturerDate = objFactory.createEquipmentTManufacturerDate(ne.getMTOSIWorker().getManufactureDate());
    managedElement.setManufacturerDate(manufacturerDate);
    // Product Name
    JAXBElement<String> productName = objFactory.createManagedElementTProductName(ne.getMTOSIWorker().getProductName());
    managedElement.setProductName(productName);
    // Version
    JAXBElement<String> version = objFactory.createManagedElementTVersion(ne.getCurrentNemiSoftwareVersion());
    managedElement.setVersion(version);
    // Communication State
    CommunicationStateT commState;
    ResponseStatus snmpResponseStatus = ne.getSNMPResponseStatus();
    if (snmpResponseStatus == ResponseStatus.RESPONDING) {
      commState = CommunicationStateT.CS_AVAILABLE;
    } else {
      commState = CommunicationStateT.CS_UNAVAILABLE;
    }
    JAXBElement<CommunicationStateT> communicationState = objFactory.createManagedElementTCommunicationState(commState);
    managedElement.setCommunicationState(communicationState);
    // In Sync State
    JAXBElement<Boolean> syncState = objFactory.createManagedElementTInSyncState(props.get(NetworkElementSPProperties.VB.SyncState)
            && commState == CommunicationStateT.CS_AVAILABLE);
    managedElement.setInSyncState(syncState);

    // Vendor Extensions
    MEVendorExtensionsT extensions = new MEVendorExtensionsT();

    // populate IP-Address
    populateIpAddress(ne, extensions);
    //populate ManagementParameters
    populateManagementParameters(ne, extensions);
    // add Vendor Extensions
    managedElement.setVendorExtensions(objFactory.createManagedElementTVendorExtensions(extensions));

    return managedElement;
  }

  public static void populateIpAddress (NetworkElement ne, MEVendorExtensionsT extensions) {
    JAXBElement<? extends String> je = new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_IPADDRESS), String.class, ne.getIPAddress());
    extensions.getAny().add(je);
  }

  public static void populateManagementParameters(NetworkElement ne, MEVendorExtensionsT extensions)
  {
    String subnetPath = ManagedElementUtils.getSubnetPath(ne);
    SNMPPropertiesData snmpProps = ne.getSNMPProperties();
    String useGlobalSNMPSettings = snmpProps.inUse ? "False" : "True";
    //String snmpGetCommunity = snmpProps.snmpGetCommunity;
    //String snmpSetCommunity = snmpProps.snmpSetCommunity;
    String snmpVersion = "";
    switch (snmpProps.version) {
      case VERSION1:  snmpVersion = "SNMPv1";  break;
      case VERSION2C: snmpVersion = "SNMPv2c"; break;
      case VERSION3:  snmpVersion = "SNMPv3";  break;
    }

    ObjectFactory objFactory = new ObjectFactory();
    // layers
    LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_TOPOLOGY);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_TOPOLOGY,
                                              LayeredParams.PropAdvaTopology.SubnetPath, subnetPath);

    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_SNMP);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SNMP,
                                              LayeredParams.PropAdvaSnmp.USE_GLOBAL_SNMP_SETTINGS, useGlobalSNMPSettings);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SNMP,
                                              LayeredParams.PropAdvaSnmp.SNMP_PROTOCOL_VERSION, snmpVersion);
    if (!snmpVersion.equals("SNMPv3")) {
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_SNMP_V1V2C);
    }
// community strings are write-only, i.e. layer is empty for read
//    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SNMP_V1V2C,
//                                              LayeredParams.PropAdvaSnmpV1V2c.READ_COMMUNITY, snmpGetCommunity);
//    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SNMP_V1V2C,
//                                              LayeredParams.PropAdvaSnmpV1V2c.WRITE_COMMUNITY, snmpSetCommunity);

    QName _MEDataTManagementParams_QNAME = new QName("tmf854.v1", "managementParams");
    JAXBElement<LayeredParametersListT> managementParams = new JAXBElement<LayeredParametersListT>(
                                                                      _MEDataTManagementParams_QNAME,
                                                                      LayeredParametersListT.class,
                                                                      PhysicalTerminationPointT.class,
                                                                      layeredParametersListT);

    extensions.getAny().add(managementParams);
  }

  /**
   * Add the DiscoveryState to the vendor Extensions for SR Capable NEs
   * @param ne
   * @param extensions
   */
  public static void populateDiscoveryState(final NetworkElement ne, final MEVendorExtensionsT extensions) {
    String discoveryState = networkElementDAO.getDiscoveryState(ne.getID()).getGUIString();
    JAXBElement<? extends String> je = new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_DISCOVERY_STATE), String.class, discoveryState);
    extensions.getAny().add(je);
  }

  /*
  * An MEFilter has components location, manufacturer, product name and resource state. The result is
  * a logical AND of the match on each filter element. The result of a NULL filter is to match all.
  */
  private static boolean managedElementPassesFilter(ManagedElementT me, MEFilterT filter) throws ProcessingFailureException {
    if (filter != null) {
      boolean matchLocation = true;
      if (filter.getLocation() != null) {
        matchLocation = me.getLocation() != null && filter.getLocation().equals(me.getLocation().getValue());
      }

      boolean matchManufacturer = true;
      if (filter.getManufacturer() != null) {
        matchManufacturer = me.getManufacturer() != null && filter.getManufacturer().equals(me.getManufacturer().getValue());
      }

      boolean matchProductName = true;
      if (filter.getProductName() != null) {
        matchProductName = me.getProductName() != null && filter.getProductName().equals(me.getProductName().getValue());
      }

      boolean matchResourceState = true;
      if (filter.getResourceState() != null) {
        matchResourceState = me.getResourceState() != null && filter.getResourceState().getValue() != null && filter.getResourceState().getValue() == me.getResourceState().getValue().getValue();
      }

      return matchLocation && matchManufacturer && matchProductName && matchResourceState;
    }
    else
    {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
              "The Managed Element Filter must be specified in the request.");
    }
  }
}
