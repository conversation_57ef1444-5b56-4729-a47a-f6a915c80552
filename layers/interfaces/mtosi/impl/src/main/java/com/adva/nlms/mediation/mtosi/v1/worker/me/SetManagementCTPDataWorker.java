/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */
package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.mediation.common.MDOperationNotSupportedException;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.mtosi.ManagementCTPProperties;
import com.adva.nlms.mediation.mtosi.common.remotetypectp.RemoteTypeDAOImpl;
import com.adva.nlms.mediation.mtosi.common.remotetypectp.RemoteTypeDBImpl;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.ManagementTunnelMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.TerminationPointT;
import v1.tmf854ext.adva.CTPCreateDataT;
import v1.tmf854ext.adva.SetManagementCTPDataResponseT;
import v1.tmf854ext.adva.SetManagementCTPDataT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.HashMap;
import java.util.List;

//import v1.tmf854ext.adva.CreateManagementCTPResponseT;

public class SetManagementCTPDataWorker extends AbstractMtosiWorker {
  Logger LOG = LogManager.getLogger(SetManagementCTPDataWorker.class);

  protected SetManagementCTPDataT mtosiBody;
  protected CTPCreateDataT ctpSetData;
  protected String ctpName;
  protected String ptpName;
  protected NamingAttributesT namingAttributes;
  protected NetworkElement networkElement;
  protected MtosiAddress mtosiAddr;
  protected ManagementCTPProperties ctpPropsResponse;
  protected SetManagementCTPDataResponseT response=new SetManagementCTPDataResponseT();

  public SetManagementCTPDataWorker(SetManagementCTPDataT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "setManagementCTPData", "setManagementCTPData", "setManagementCTPDataResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    validator.validate(networkElement.getDefaultNetworkElementTypeString());
  }

  @Override
  protected void parse() throws Exception {
    ctpSetData = mtosiBody.getSetData();

    if (ctpSetData == null || (namingAttributes = ctpSetData.getName()) == null) {
      LOG.debug("ctpCreateData.getName()) => null");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.CTP_NAME_MISSING);
    }

    if ((ptpName = namingAttributes.getPtpNm()) == null ) {
      LOG.debug("namingAttributes == null");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.PTP_NAME_MISSING);
    }

    if (ctpSetData.getTransmissionParams() == null ||
            ctpSetData.getTransmissionParams().getValue() == null) {
      LOG.debug("(ctpCreateData == null || ctpCreateData.getTransmissionParams() == null ||" +
              "            ctpCreateData.getTransmissionParams().getValue()==null)");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING);
    }

    if (!NamingTranslationFactory.isManagementDomain(namingAttributes)) {
      LOG.debug("NamingTranslationFactory.isManagementDomain(namingAttributesT)=>false");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (namingAttributes.getMeNm()==null) {
      LOG.debug("namingAttributesT.getMeNm()==null");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.ME_NAME_MISSING);
    }

    if (!namingAttributes.getMdNm().equals(OSFactory.getMDNm())) {
      LOG.debug("namingAttributesT.getMdNm().equals(OSFactory.getMDNm())=>false");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MD_NOT_FOUND);
    }

    networkElement = this.getMtosiCtrl().getLegacyMtosiMOFacade().getNEByName(namingAttributes.getMeNm());
    MtosiUtils.validateNE(networkElement);


    if (!NamingTranslationFactory.isPtpNameValid(namingAttributes.getPtpNm())) {
      LOG.debug("namingAttributesT.getPtpNm() invalid");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.PTP_NAME_NOT_VALID);
    }

    if ((ctpName = namingAttributes.getCtpNm()) == null || ctpName.isEmpty()) {
      LOG.debug("namingAttributes.getCtpNm() invalid");
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.CTP_NAME_MISSING);
    }
  }

  @Override
  protected void mediate() throws Exception {
    LayeredParametersListT layeredParametersListT = ctpSetData.getTransmissionParams().getValue();

    int shelfNum = NamingTranslationFactory.shelfNumberFromShelfCombo(ptpName);
    if (shelfNum == -1) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "Invalid shelf index.");
    }
    int slotNum = NamingTranslationFactory.slotNumberFromShelfCombo(ptpName);
    if (slotNum == -1) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "Invalid slot index.");
    }
    String portName = NamingTranslationFactory.portNameFromShelfCombo(ptpName);
    int portType = MtosiUtils.getPortTypeTunnel(portName);
    if (portType == -1) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.PTP_PORT_TYPE_WRONG);
    }
    int portIndex = MtosiUtils.getPortIndexTunnel(portName);
    if (portIndex == -1) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.PTP_PORT_INDEX_WRONG);
    }
    //rest of the properties

    String tunnelName = ctpName;

    int vLanId = -1;
    try{
        String vlanIdS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.VLANID);
        if (vlanIdS != null) {
          try {
            vLanId = Integer.parseInt(vlanIdS);
          } catch (NumberFormatException nex) {
            LOG.debug("vlanid not an integer");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.VLANID_INDEX_NOT_INT);
          }
        }
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    long cir = -1;
    try{
        String cirS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.CIR);
        if (cirS != null) {
          try {
            cir = Long.parseLong(cirS);
          } catch (NumberFormatException nex) {
            LOG.debug("cir not an int");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.CIR_INDEX_NOT_INT);
          }
        }
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    long eir = -1;
    try{
        String eirS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.EIR);
        if (eirS != null) {
          try {
            eir = Long.parseLong(eirS);
          } catch (NumberFormatException nex) {
            LOG.debug("eir not an int");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.EIR_INDEX_NOT_INT);
          }
        }
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    int cos = -1;
    try{
        String cosS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.COS);
        if (cosS != null) {
          try {
            cos = Integer.parseInt(cosS);
          } catch (NumberFormatException nex) {
            LOG.debug("cos not an integer");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.COS_INDEX_NOT_INT);
          }
        }
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    long bufferSize = -1;
    try{
        String bufferSizeS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.BUFFER_SIZE);
        if (bufferSizeS != null) {
          try {
            bufferSize = Long.parseLong(bufferSizeS);
          } catch (NumberFormatException nex) {
            LOG.debug("bufferSize not an long");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.BUFFERSIZE_NOT_LONG);
          }
        }
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    int sVlanIdEnabled = -1;
    try{
        String sVlanIdEnabledS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.S_VLANID_ENABLED);
        if (sVlanIdEnabledS != null) {
          try {
            if(! (sVlanIdEnabledS.toLowerCase().equals("true") || sVlanIdEnabledS.toLowerCase().equals("false")) ){
                LOG.debug("sVlanIdEnabled not a boolean");
                throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                        MtosiErrorConstants.SVLANIDENABLED_NOT_BOOLEAN);
            }
            boolean value = Boolean.parseBoolean(sVlanIdEnabledS);
            if (value) {
              sVlanIdEnabled = 1;
            } else sVlanIdEnabled = 2;
          } catch (NumberFormatException nex) {
            LOG.debug("sVlanIdEnabled not a boolean");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.SVLANIDENABLED_NOT_BOOLEAN);
          }
        }
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    int sTagVLanId = -1;
    try{
        String sTagVLanIdS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.S_TAG_VLANID);
        if (sTagVLanIdS != null) {
          try {
            sTagVLanId = Integer.parseInt(sTagVLanIdS);
          } catch (NumberFormatException nex) {
            LOG.debug("sTagVLanId not an int");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.STAGVLANID_NOT_INT);
          }
        }
    }catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    int rip2PktsEnabled = -1;
    try{
        String rip2PktsEnabledS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.RIP_2_PKTS_ENABLED);
        if (rip2PktsEnabledS != null) {
          try {
            if(! (rip2PktsEnabledS.toLowerCase().equals("true") || rip2PktsEnabledS.toLowerCase().equals("false")) ){
              LOG.debug("rip2PktsEnabled not a boolean");
              throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                      MtosiErrorConstants.RIP2PKTSENABLED_NOT_BOOLEAN);
            }
            boolean value = Boolean.parseBoolean(rip2PktsEnabledS);
            if (value) {
              rip2PktsEnabled = 1;
            } else rip2PktsEnabled = 2;
          } catch (NumberFormatException nex) {
            LOG.debug("rip2PktsEnabled not an int");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.RIP2PKTSENABLED_NOT_BOOLEAN);
          }
        }
    } catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    int encapsulationType = -1;
    try{
        String encapsulationTypeS = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_Tunnel, LayeredParams.LrPropAdvaTunnel.ENCAPSULATION_TYPE);
        if (encapsulationTypeS != null) {
          if (encapsulationTypeS.equals(MtosiConstants.ENCAPSULATION_TYPE_ETH))    //TODO enum...
            encapsulationType=1;
          else {
            LOG.debug("encapsulationType not Ethernet");
            throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                    MtosiErrorConstants.ENCAPSULATIONTYPE_NOT_Ethernet);
          }
        }
    }catch (NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    String subnetMask;
    try{
        subnetMask = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
                LayeredParams.PROP_ADVA_RemoteCPE, LayeredParams.LrPropAdvaRemoteCPE.SUBNET_MASK);
    } catch(NullPointerException ex){
        LOG.debug("The transmission parameters are missing or are null");
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.TRANSMISSION_PARAMS_MISSING, ex);
    }

    int createLink = -1;
    String linkName = LayeredParameterUtils.getLayeredParameter(layeredParametersListT,
            LayeredParams.PROP_ADVA_RemoteCPE, LayeredParams.LrPropAdvaRemoteCPE.LINK_NAME_PARM);

    ManagementCTPProperties ctpProps = new ManagementCTPProperties(networkElement.getID(), shelfNum, slotNum, portType, portIndex,
            -1, tunnelName, vLanId, cir, eir, cos, bufferSize, sVlanIdEnabled, sTagVLanId, rip2PktsEnabled,
            encapsulationType, null, null, subnetMask, null, createLink, linkName,false);

    NetworkElement locks[] = new NetworkElement[] {};
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "SetManagementCTPDataWorker");
    try
    {
      ctpPropsResponse=this.getMtosiCtrl().getLegacyMtosiMOFacade().setManagementCTPData(ctpProps);
      NetTransactionManager.commitNetTransaction(id);
    }
    catch (SNMPCommFailure | SPValidationException | MDOperationNotSupportedException | NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally
    {
      NetTransactionManager.ensureEnd(id);
    }
  }

  @Override
  protected void response() throws Exception {
    if (ctpPropsResponse != null) {
      updateCtpPropsResponse();
      TerminationPointT tp = new TerminationPointT();
      PhysicalTerminationPointT ptp = ManagementTunnelMediator.getManagementTunnel(ctpPropsResponse, namingAttributes);
      tp.setPtp(ptp);
      response.setTheCTP(tp);
    }
  }

    private boolean updateCtpPropsResponse(){
        if(ctpPropsResponse != null && ctpPropsResponse.getResponse() != null && ctpPropsResponse.getResponse().getRemoteCpeRemoteType() ==  null){
            RemoteTypeDAOImpl remoteDAOImpl = new RemoteTypeDAOImpl();
            try{
                RemoteTypeDBImpl entity = remoteDAOImpl.getRemoteType(namingAttributes.getMeNm(), namingAttributes.getCtpNm(), namingAttributes.getPtpNm());
                if(entity != null){
                    ctpPropsResponse.setRemoteType(entity.getRemoteType());
                }else{
                    updateCtpPropsResponse(networkElement.getIPAddress(), namingAttributes.getMeNm(),namingAttributes.getPtpNm(),namingAttributes.getCtpNm());
                    entity = remoteDAOImpl.getRemoteType(namingAttributes.getMeNm(), namingAttributes.getCtpNm(), namingAttributes.getPtpNm());
                    if(entity != null){
                        ctpPropsResponse.setRemoteType(entity.getRemoteType());
                    }
                }
               // ctpPropsResponse.setRemoteType(entity.getRemoteType());
                return true;
            }catch(Exception ex){
                LOG.debug("Failed to persist tunnel data " + ex.getMessage());
                return false;
            }
        }
        return true;
    }

    private boolean updateCtpPropsResponse(String ipAddress, String meNm, String mtosiName, String tunnelName){
        RemoteTypeDAOImpl remoteDAOImpl = new RemoteTypeDAOImpl();
        try{
            HashMap<String, List<RemoteTypeDBImpl>> remoteTypes = remoteDAOImpl.getRemoteTypeByIpAddress(ipAddress,meNm,mtosiName,tunnelName);
            if (!remoteTypes.isEmpty()){
                if(remoteTypes.get("Head") != null && !remoteTypes.get("Head").isEmpty()){
                    List<RemoteTypeDBImpl> headList =   remoteTypes.get("Head");
                    for(RemoteTypeDBImpl entity : headList) {
                        remoteDAOImpl.updateHeadEnd(entity.getTunnelId(),meNm);
                    }
                } else if(remoteTypes.get("Remote") != null && !remoteTypes.get("Remote").isEmpty()){
                    List<RemoteTypeDBImpl> remoteList =   remoteTypes.get("Remote");
                    for(RemoteTypeDBImpl entity : remoteList) {
                        remoteDAOImpl.updateRemoteEnd(entity.getTunnelId(),meNm);
                    }
                }
            }
            return true;
        }catch(Exception ex){
            LOG.debug("Failed to persist tunnel data " + ex.getMessage());
            return false;
        }
    }

  @Override
  public SetManagementCTPDataResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
