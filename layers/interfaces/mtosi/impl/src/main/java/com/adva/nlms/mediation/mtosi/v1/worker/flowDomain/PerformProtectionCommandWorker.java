/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.common.NEUtils;
import com.adva.nlms.mediation.common.serviceProvisioning.ProtectionGroupF3SPProperties;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FTPFSP150CM;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiFDFrMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress.NamingField;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854ext.adva.PerformProtectionCommandResponseT;
import v1.tmf854ext.adva.PerformProtectionCommandT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public class PerformProtectionCommandWorker extends AbstractMtosiWorker
{
  Logger LOG = LogManager.getLogger(this.getClass().getName());

  protected PerformProtectionCommandT mtosiBody;
  protected PerformProtectionCommandResponseT response = new PerformProtectionCommandResponseT();
  protected MtosiAddress tpNameAddress;
  protected NetworkElement ne;
  protected String switchMode;

  public PerformProtectionCommandWorker(PerformProtectionCommandT mtosiBody, Holder<HeaderT> mtosiHeader)
  {
    super(mtosiHeader, "performProtectionCommand", "performProtectionCommand", "performProtectionCommandResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception
  {
    tpNameAddress = new MtosiAddress(mtosiBody.getTpName());

    // validate mandatory Address-elements: MD, ME, FTP
    tpNameAddress.validateAddressFields(NamingField.MD, NamingField.ME, NamingField.FTP);

    switchMode = MtosiUtils.checkElement(mtosiBody.getSwitchMode(), "switchMode");

    ne = tpNameAddress.getNE();
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
   validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  @Override
  protected void mediate() throws Exception {
    if (!NEUtils.isF3Device(tpNameAddress.getNeType())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.ME_NOT_SUPPORTED);
    }

    final FTPFSP150CM ftp = (FTPFSP150CM) tpNameAddress.getAndValidateFTP();

    ProtectionGroupF3SPProperties props = MtosiFDFrMediator.mtosiProtectionCommandToCMFTPProperties(switchMode, ftp);
    transact(ftp, props);
  }

  @Override
  protected void response () throws Exception {
    // empty method
  }

  private void transact(FTPFSP150CM ftp, ProtectionGroupF3SPProperties props) throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure
  {
    NetworkElement locks[] = new NetworkElement[]
              { ne };
      Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "performProtectionCommand");
      try
      {
        logSecurity(ne, SystemAction.ModifyNetwork, ftp.getFTPName());
        ftp.setFTPSPProperties(props);
        NetTransactionManager.commitNetTransaction(id);
      }
      catch (NetTransactionException e)
      {
        ServiceUtils.rollbackNetTransaction(id, e, LOG);
      }
      catch (SPValidationException e)
      {
        ServiceUtils.rollbackNetTransaction(id, e, LOG);
      }
      catch (SNMPCommFailure e)
      {
        ServiceUtils.rollbackNetTransaction(id, e, LOG);
      }
      finally
      {
        NetTransactionManager.ensureEnd(id);
      }
  }

  @Override
  public PerformProtectionCommandResponseT getSuccessResponse()
  {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}