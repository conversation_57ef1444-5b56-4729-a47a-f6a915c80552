/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import jakarta.xml.ws.Holder;

import v1.tmf854.HeaderT;
import v1.tmf854.NVSListT;
import v1.tmf854.NameAndStringValueT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;

import com.adva.nlms.mediation.common.serviceProvisioning.ContinuityTestResultPropertiesHN4000;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.common.snmp.TestResultTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.TestStatusTranslationFSP150CM;

public class GetContinuityTestWorkerHN4000 extends GetContinuityTestWorkerHN {
 private ContinuityTestResultPropertiesHN4000 result;

  public GetContinuityTestWorkerHN4000(Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne) {
    super(mtosiHeader, namingAttributes, ne);
  }

  @Override
  protected void mediate() throws Exception {
	  super.mediate();
    	result = flow.getContinuityTest();
  }

  @Override
  protected void response() throws Exception {
    response.setTestResults(getTestResults());
    //this value will always be stopped
    response.setTestStatus(TestStatusTranslationFSP150CM.STOPPED.getMtosiString()); 
  }

  private NVSListT getTestResults() {
    final ObjectFactory objFactory = new ObjectFactory();
    final NVSListT nvsListT = objFactory.createNVSListT();
    NameAndStringValueT testResultValueT = objFactory.createNameAndStringValueT();
    testResultValueT.setName("TestResult");
    testResultValueT.setValue(TestResultTranslation.getMtosiString(result.get(ContinuityTestResultPropertiesHN4000.VI.TestResult)));

    //NameAndStringValueT testStatusValueT = objFactory.createNameAndStringValueT();
    //testStatusValueT.setName("TestResult");
    //testStatusValueT.setValue(TestStatusTranslation.getMtosiString(result.getTestResult()));

    //nvsListT.getNvs().add(testStatusValueT);

    nvsListT.getNvs().add(testResultValueT);

    return nvsListT;
  }
}
