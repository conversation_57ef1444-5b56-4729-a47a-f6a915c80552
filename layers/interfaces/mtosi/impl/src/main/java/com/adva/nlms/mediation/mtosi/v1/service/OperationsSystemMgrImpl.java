/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;

import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.HeaderUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.os.GetAllMDNamesWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.os.GetAllMDsWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.os.GetAllMENamesPassingFilterWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.os.GetAllMENamesWrtOSWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.os.GetAllMEsPassingFilterWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.os.GetAllMEsWrtOSWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.os.GetAllOSNamesWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.os.GetAllOSsWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.os.GetMDWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.os.GetOSWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.OperationsSystemMgr;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.annotation.Resource;
import jakarta.xml.ws.WebServiceContext;


/**
 * This class was generated by the Celtix 1.1-SNAPSHOT
 * Fri Dec 22 10:48:24 EST 2006
 * Generated source version: 1.1-SNAPSHOT
 *
 */

@jakarta.jws.WebService(name = "OperationsSystemMgr", serviceName = "ConfigurationService", portName = "OperationsSystemMgrHttp", targetNamespace = "tmf854.v1.ws")

public class OperationsSystemMgrImpl implements OperationsSystemMgr {
  Logger LOG = LogManager.getLogger(this.getClass().getName());
  @Resource
  private WebServiceContext context;

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllMDNames(v1.tmf854.GetAllMDNamesT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllMDNames(v1.tmf854.GetAllMDNamesT mtosiBody,
                                                            jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllMDNamesWorker.class, mtosiBody, mtosiHeader,
            "getAllMDNames", context, LOG);
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllMDNames(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                            v1.tmf854.GetAllMDNamesT mtosiBody) throws ProcessingFailureException {
    return getAllMDNames(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllMDNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllMDNamesIterator(
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllMDNamesIterator", "getAllMDNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllMDNamesIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllMDNamesIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllMDs(v1.tmf854.GetAllMDsT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllMDsResponseT getAllMDs(v1.tmf854.GetAllMDsT mtosiBody,
                                                jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllMDsWorker.class, mtosiBody, mtosiHeader,
            "getAllMDs", context, LOG);
  }

  @Override
  public v1.tmf854.GetAllMDsResponseT getAllMDs(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                v1.tmf854.GetAllMDsT mtosiBody) throws ProcessingFailureException {
    return getAllMDs(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllMDsIterator(v1.tmf854.GetMdIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllMDsResponseT getAllMDsIterator(
          v1.tmf854.GetMdIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllMDsIterator", "getAllMDsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllMDsResponseT getAllMDsIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetMdIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllMDsIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllMENamesPassingFilter(v1.tmf854.GetAllMENamesPassingFilterT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllMENamesPassingFilter(
          v1.tmf854.GetAllMENamesPassingFilterT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllMENamesPassingFilterWorker.class, mtosiBody, mtosiHeader,
            "getAllMENamesPassingFilter", context, LOG);
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllMENamesPassingFilter(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllMENamesPassingFilterT mtosiBody) throws ProcessingFailureException {
    return getAllMENamesPassingFilter(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllMENamesPassingFilterIterator(v1.tmf854.GetAllObjectNamesIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllMENamesPassingFilterIterator(
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllMENamesPassingFilterIterator", "getAllMENamesPassingFilterResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllMENamesPassingFilterIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllMENamesPassingFilterIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllMEsPassingFilter(v1.tmf854.GetAllMEsPassingFilterT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllMEsPassingFilterResponseT getAllMEsPassingFilter(
          v1.tmf854.GetAllMEsPassingFilterT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllMEsPassingFilterWorker.class, mtosiBody, mtosiHeader,
            "getAllMEsPassingFilter", context, LOG);
  }

  @Override
  public v1.tmf854.GetAllMEsPassingFilterResponseT getAllMEsPassingFilter(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllMEsPassingFilterT mtosiBody) throws ProcessingFailureException {
    return getAllMEsPassingFilter(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllMEsPassingFilterIterator(v1.tmf854.GetMeIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllMEsPassingFilterResponseT getAllMEsPassingFilterIterator(
          v1.tmf854.GetMeIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllMEsPassingFilterIterator", "getAllMEsPassingFilterResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllMEsPassingFilterResponseT getAllMEsPassingFilterIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetMeIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllMEsPassingFilterIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllMENamesWrtOS(v1.tmf854.GetAllMENamesWrtOST  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllMENamesWrtOS(
          v1.tmf854.GetAllMENamesWrtOST mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllMENamesWrtOSWorker.class, mtosiBody, mtosiHeader,
            "getAllMENamesWrtOS", context, LOG);
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllMENamesWrtOS(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllMENamesWrtOST mtosiBody) throws ProcessingFailureException {
    return getAllMENamesWrtOS(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllMENamesWrtOSIterator(v1.tmf854.GetAllObjectNamesIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllMENamesWrtOSIterator(
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllMENamesWrtOSIterator", "getAllMENamesWrtOSResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllMENamesWrtOSIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllMENamesWrtOSIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllMEsWrtOS(v1.tmf854.GetAllMEsWrtOST  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllMEsWrtOSResponseT getAllMEsWrtOS(
          v1.tmf854.GetAllMEsWrtOST mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllMEsWrtOSWorker.class, mtosiBody, mtosiHeader,
            "getAllMEsWrtOS", context, LOG);
  }

  @Override
  public v1.tmf854.GetAllMEsWrtOSResponseT getAllMEsWrtOS(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllMEsWrtOST mtosiBody) throws ProcessingFailureException {
    return getAllMEsWrtOS(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllMEsWrtOSIterator(v1.tmf854.GetMeIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllMEsWrtOSResponseT getAllMEsWrtOSIterator(
          v1.tmf854.GetMeIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllMEsWrtOSIterator", "getAllMEsWrtOSResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllMEsWrtOSResponseT getAllMEsWrtOSIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetMeIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllMEsWrtOSIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllOSNames(v1.tmf854.GetAllOSNamesT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllOSNames(v1.tmf854.GetAllOSNamesT mtosiBody,
                                                            jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    final String name = "getAllOSNames";
    LOG.info("Executing MTOSI operation " + name + "...");
    GetAllOSNamesWorker worker = new GetAllOSNamesWorker(mtosiBody, mtosiHeader);
    return ServiceUtils.runWorker(worker, mtosiHeader, name, context, LOG);
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllOSNames(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
                                                            v1.tmf854.GetAllOSNamesT mtosiBody) throws ProcessingFailureException {
    return getAllOSNames(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllOSNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllOSNamesIterator(
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllOSNamesIterator", "getAllOSNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllOSNamesIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllOSNamesIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllOSs(v1.tmf854.GetAllOSsT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllOSsResponseT getAllOSs(
          v1.tmf854.GetAllOSsT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    final String name = "getAllOSs";
    LOG.info("Executing MTOSI operation " + name + "...");
    GetAllOSsWorker worker = new GetAllOSsWorker(mtosiBody, mtosiHeader);
    return ServiceUtils.runWorker(worker, mtosiHeader, name, context, LOG);
  }

  @Override
  public v1.tmf854.GetAllOSsResponseT getAllOSs(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllOSsT mtosiBody) throws ProcessingFailureException {
    return getAllOSs(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllOSsIterator(v1.tmf854.GetOsIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllOSsResponseT getAllOSsIterator(
          v1.tmf854.GetOsIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllOSsIterator", "getAlOSsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllOSsResponseT getAllOSsIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetOsIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllOSsIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllSNNamesWrtOS(v1.tmf854.GetAllSNNamesWrtOST  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllSNNamesWrtOS(
          v1.tmf854.GetAllSNNamesWrtOST mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSNNamesWrtOS", "getAllSNNamesWrtOSResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllSNNamesWrtOS(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllSNNamesWrtOST mtosiBody) throws ProcessingFailureException {
    return getAllSNNamesWrtOS(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllSNNamesWrtOSIterator(v1.tmf854.GetAllObjectNamesIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllSNNamesWrtOSIterator(
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSNNamesWrtOSIterator", "getAllSNNamesWrtOSResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllSNNamesWrtOSIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllSNNamesWrtOSIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllSNsWrtOSIterator(v1.tmf854.GetSncIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllSNsWrtOSResponseT getAllSNsWrtOSIterator(
          v1.tmf854.GetSncIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSNsWrtOSIterator", "getAllSNsWrtOSResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllSNsWrtOSResponseT getAllSNsWrtOSIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetSncIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllSNsWrtOSIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllSNsWrtOS(v1.tmf854.GetAllSNsWrtOST  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllSNsWrtOSResponseT getAllSNsWrtOS(
          v1.tmf854.GetAllSNsWrtOST mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllSNsWrtOS", "getAllSNsWrtOSResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllSNsWrtOSResponseT getAllSNsWrtOS(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllSNsWrtOST mtosiBody) throws ProcessingFailureException {
    return getAllSNsWrtOS(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTLNamesBetweenMDs(v1.tmf854.GetAllTLNamesBetweenMDsT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllTLNamesBetweenMDs(
          v1.tmf854.GetAllTLNamesBetweenMDsT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTLNamesBetweenMDs", "getAllTLNamesBetweenMDsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllTLNamesBetweenMDs(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllTLNamesBetweenMDsT mtosiBody) throws ProcessingFailureException {
    return getAllTLNamesBetweenMDs(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTLsBetweenMDs(v1.tmf854.GetAllTLsBetweenMDsT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllTLsBetweenMDsResponseT getAllTLsBetweenMDs(
          v1.tmf854.GetAllTLsBetweenMDsT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTLsBetweenMDs", "getAllTLsBetweenMDsResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllTLsBetweenMDsResponseT getAllTLsBetweenMDs(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllTLsBetweenMDsT mtosiBody) throws ProcessingFailureException {
    return getAllTLsBetweenMDs(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTLNamesWrtOS(v1.tmf854.GetAllTLNamesWrtOST  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllTLNamesWrtOS(
          v1.tmf854.GetAllTLNamesWrtOST mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTLNamesWrtOS", "getAllTLNamesWrtOSResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllTLNamesWrtOS(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllTLNamesWrtOST mtosiBody) throws ProcessingFailureException {
    return getAllTLNamesWrtOS(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTLNamesWrtOSIterator(v1.tmf854.GetAllObjectNamesIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllTLNamesWrtOSIterator(
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTLNamesWrtOSIterator", "getAllTLNamesWrtOSResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllTLNamesWrtOSIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllTLNamesWrtOSIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTLsWrtOS(v1.tmf854.GetAllTLsWrtOST  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllTLsWrtOSResponseT getAllTLsWrtOS(
          v1.tmf854.GetAllTLsWrtOST mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTLsWrtOS", "getAllTLsWrtOSResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllTLsWrtOSResponseT getAllTLsWrtOS(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllTLsWrtOST mtosiBody) throws ProcessingFailureException {
    return getAllTLsWrtOS(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTLsWrtOSIterator(v1.tmf854.GetTlIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllTLsWrtOSResponseT getAllTLsWrtOSIterator(
          v1.tmf854.GetTlIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTLsWrtOSIterator", "getAllTLsWrtOSResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllTLsWrtOSResponseT getAllTLsWrtOSIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetTlIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllTLsWrtOSIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTMDNamesWrtOS(v1.tmf854.GetAllTMDNamesWrtOST  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllTMDNamesWrtOS(
          v1.tmf854.GetAllTMDNamesWrtOST mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTMDNamesWrtOS", "getAllTMDNamesWrtOSResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllTMDNamesWrtOS(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllTMDNamesWrtOST mtosiBody) throws ProcessingFailureException {
    return getAllTMDNamesWrtOS(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTMDNamesWrtOSIterator(v1.tmf854.GetAllObjectNamesIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllTMDNamesWrtOSIterator(
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTMDNamesWrtOSIterator", "getAllTMDNamesWrtOSResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllTMDNamesWrtOSIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllTMDNamesWrtOSIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTMDsWrtOS(v1.tmf854.GetAllTMDsWrtOST  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllTMDsWrtOSResponseT getAllTMDsWrtOS(
          v1.tmf854.GetAllTMDsWrtOST mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTMDsWrtOS", "getAllTMDsWrtOSResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllTMDsWrtOSResponseT getAllTMDsWrtOS(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllTMDsWrtOST mtosiBody) throws ProcessingFailureException {
    return getAllTMDsWrtOS(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTMDsWrtOSIterator(v1.tmf854.GetTmdIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllTMDsWrtOSResponseT getAllTMDsWrtOSIterator(
          v1.tmf854.GetTmdIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTMDsWrtOSIterator", "getAllTMDsWrtOSResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllTMDsWrtOSResponseT getAllTMDsWrtOSIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetTmdIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllTMDsWrtOSIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTopLevelSubnetworkNames(v1.tmf854.GetAllTopLevelSubnetworkNamesT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllTopLevelSubnetworkNames(
          v1.tmf854.GetAllTopLevelSubnetworkNamesT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTopLevelSubnetworkNames", "getAllTopLevelSubnetworkNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllTopLevelSubnetworkNames(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllTopLevelSubnetworkNamesT mtosiBody) throws ProcessingFailureException {
    return getAllTopLevelSubnetworkNames(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTopLevelSubnetworkNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllTopLevelSubnetworkNamesIterator(
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTopLevelSubnetworkNamesIterator", "getAllTopLevelSubnetworkNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllTopLevelSubnetworkNamesIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllTopLevelSubnetworkNamesIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTopLevelSubnetworks(v1.tmf854.GetAllTopLevelSubnetworksT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllTopLevelSubnetworksResponseT getAllTopLevelSubnetworks(
          v1.tmf854.GetAllTopLevelSubnetworksT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTopLevelSubnetworks", "getAllTopLevelSubnetworksResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllTopLevelSubnetworksResponseT getAllTopLevelSubnetworks(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllTopLevelSubnetworksT mtosiBody) throws ProcessingFailureException {
    return getAllTopLevelSubnetworks(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTopLevelSubnetworksIterator(v1.tmf854.GetSncIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllTopLevelSubnetworksResponseT getAllTopLevelSubnetworksIterator(
          v1.tmf854.GetSncIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTopLevelSubnetworksIterator", "getAllTopLevelSubnetworksResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllTopLevelSubnetworksResponseT getAllTopLevelSubnetworksIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetSncIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllTopLevelSubnetworksIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTopLevelTopologicalLinkNames(v1.tmf854.GetAllTopLevelTopologicalLinkNamesT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllTopLevelTopologicalLinkNames(
          v1.tmf854.GetAllTopLevelTopologicalLinkNamesT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTopLevelTopologicalLinkNames", "getAllTopLevelTopologicalLinkNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllTopLevelTopologicalLinkNames(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllTopLevelTopologicalLinkNamesT mtosiBody) throws ProcessingFailureException {
    return getAllTopLevelTopologicalLinkNames(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTopLevelTopologicalLinkNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllObjectNamesResponseT getAllTopLevelTopologicalLinkNamesIterator(
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTopLevelTopologicalLinkNamesIterator", "getAllTopLevelTopologicalLinkNamesResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getAllTopLevelTopologicalLinkNamesIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllTopLevelTopologicalLinkNamesIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTopLevelTopologicalLinks(v1.tmf854.GetAllTopLevelTopologicalLinksT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllTopLevelTopologicalLinksResponseT getAllTopLevelTopologicalLinks(
          v1.tmf854.GetAllTopLevelTopologicalLinksT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTopLevelTopologicalLinks", "getAllTopLevelTopologicalLinksResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllTopLevelTopologicalLinksResponseT getAllTopLevelTopologicalLinks(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetAllTopLevelTopologicalLinksT mtosiBody) throws ProcessingFailureException {
    return getAllTopLevelTopologicalLinks(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getAllTopLevelTopologicalLinksIterator(v1.tmf854.GetTlIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetAllTopLevelTopologicalLinksResponseT getAllTopLevelTopologicalLinksIterator(
          v1.tmf854.GetTlIteratorT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTopLevelTopologicalLinksIterator", "getAllTopLevelTopologicalLinksResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetAllTopLevelTopologicalLinksResponseT getAllTopLevelTopologicalLinksIterator(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetTlIteratorT mtosiBody) throws ProcessingFailureException {
    return getAllTopLevelTopologicalLinksIterator(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getMD(v1.tmf854.GetMDT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetMDResponseT getMD(
          v1.tmf854.GetMDT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(GetMDWorker.class, mtosiBody, mtosiHeader,
            "getMD", context, LOG);
  }

  @Override
  public v1.tmf854.GetMDResponseT getMD(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetMDT mtosiBody) throws ProcessingFailureException {
    return getMD(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getOS(v1.tmf854.GetOST  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetOSResponseT getOS(
          v1.tmf854.GetOST mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    final String name = "getOS";
    LOG.info("Executing MTOSI operation " + name + "...");
    GetOSWorker worker = new GetOSWorker(mtosiBody, mtosiHeader);
    return ServiceUtils.runWorker(worker, mtosiHeader, name, context, LOG);
  }

  @Override
  public v1.tmf854.GetOSResponseT getOS(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetOST mtosiBody) throws ProcessingFailureException {
    return getOS(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getSubordinateOS(v1.tmf854.GetSubordinateOST  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetSubordinateOSResponseT getSubordinateOS(
          v1.tmf854.GetSubordinateOST mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getSubordinateOS", "getSubordinateOSResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetSubordinateOSResponseT getSubordinateOS(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetSubordinateOST mtosiBody) throws ProcessingFailureException {
    return getSubordinateOS(mtosiBody, mtosiHeader);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854.OperationsSystemMgr#getTopLevelTopologicalLink(v1.tmf854.GetTopLevelTopologicalLinkT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.GetTopLevelTopologicalLinkResponseT getTopLevelTopologicalLink(
          v1.tmf854.GetTopLevelTopologicalLinkT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    HeaderUtils.formatResponseHeader(mtosiHeader, "getTopLevelTopologicalLink", "getTopLevelTopologicalLinkResponse");
    ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    throw pfe;
  }

  @Override
  public v1.tmf854.GetTopLevelTopologicalLinkResponseT getTopLevelTopologicalLink(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.GetTopLevelTopologicalLinkT mtosiBody) throws ProcessingFailureException {
    return getTopLevelTopologicalLink(mtosiBody, mtosiHeader);
  }
}
