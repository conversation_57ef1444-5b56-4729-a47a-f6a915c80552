/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.factory;


import com.adva.nlms.common.VersionInfoDTO;
import com.adva.nlms.common.redundancy.WorkMode;
import com.adva.nlms.mediation.common.MTOSIProperty;
import com.adva.nlms.common.snmp.NoSuchMDObjectException;
import com.adva.nlms.mediation.config.ConfigCtrlImpl;
import com.adva.nlms.mediation.mtosi.MtosiCtrlImpl;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.server.ServerCtrl;
import v1.tmf854.ManagementDomainT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.OSVendorExtensionsT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.OperationsSystemT;
import v1.tmf854.ServiceStateEnumT;
import v1.tmf854.ServiceStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;

import jakarta.xml.bind.JAXBElement;
import javax.xml.namespace.QName;
import java.net.InetAddress;
import java.net.UnknownHostException;


public class OSFactory {
  private static String nmsName = null;
  private static UpdateNmsNameThread updateThread = new UpdateNmsNameThread();

  static class UpdateNmsNameThread extends Thread {
    private int samplingDelay;
    UpdateNmsNameThread() {
      samplingDelay = Integer.parseInt(new MTOSIProperty().getProperty("com.adva.nlms.mediation.mtosi.NMSNameSamplingTime","60")) * 1000;
    }
    public void updateNmsName() {
      String tmpNmsName = "localhost";
      try {
        InetAddress addr = InetAddress.getLocalHost();
        tmpNmsName = addr.getHostName();
      } catch (UnknownHostException e) {
        // do nothing
      }
      tmpNmsName = MtosiCtrlImpl.getHostName(tmpNmsName);

      nmsName = MtosiConstants.NAMING_ADVA_FNM + tmpNmsName;
    }

    @Override
    public void run() {
      //noinspection InfiniteLoopStatement
      while (true) {
        try { // protect thread against exceptions!
          Thread.sleep(samplingDelay);
        } catch (InterruptedException e) {
          // do nothing
        }
        updateNmsName();
      }
    }
  }

  /*
    * Note: We do not need to implement any filtering of special charcters in the hostname
    * because JAXB takes care of escaping them in the XML stream.
    */
  public static String getNmsName() {
    if (nmsName != null) {
      return nmsName;
    } else {
      updateThread.updateNmsName();
      try {
        updateThread.start();
      } catch (IllegalThreadStateException e) {
        // do nothing
      }
      return nmsName;
    }
  }

  public static OperationsSystemT getMtosiOS() {
    ObjectFactory objFactory = new ObjectFactory();
    OperationsSystemT os = objFactory.createOperationsSystemT();

    // Name
    NamingAttributesT namingAttributesT = new NamingAttributesT();
    namingAttributesT.setOsNm(OSFactory.getNmsName());
    JAXBElement<NamingAttributesT> osName = objFactory.createOperationsSystemTName(namingAttributesT);
    os.setName(osName);

    // Discovered Name
    JAXBElement<String> osDiscoveredName = objFactory.createOperationsSystemTDiscoveredName(OSFactory.getNmsName());
    os.setDiscoveredName(osDiscoveredName);

    // Naming OS
    JAXBElement<String> osNamingOS = objFactory.createOperationsSystemTNamingOS(OSFactory.getNmsName());
    os.setNamingOS(osNamingOS);

    // User Label
    // Not supported.

    // Source
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    JAXBElement<SourceT> osSource = objFactory.createOperationsSystemTSource(source);
    os.setSource(osSource);

    // Owner
    // Not supported.

    // Alias Name List
    // Not supported.

    // ResourceState
    // Not supported.

    // SoftwareVersion
    VersionInfoDTO version = ServerCtrl.getRef().getVersionInfoDTO();
    String versionString = version.majorVersion + "." + version.minorVersion + "." + version.patchVersion;
    JAXBElement<String> osSoftwareVersion = objFactory.createOperationsSystemTSoftwareVersion(versionString);
    os.setSoftwareVersion(osSoftwareVersion);

    // Product Name
    JAXBElement<String> osProductName = objFactory.createOperationsSystemTProductName(MtosiConstants.PRODUCTNAME);
    os.setProductName(osProductName);

    // Manufacturer
    String manufacturer = MtosiConstants.MANUFACTURER;
    JAXBElement<String> osManufacturer = objFactory.createOperationsSystemTManufacturer(manufacturer);
    os.setManufacturer(osManufacturer);

    // Service State
    ServiceStateT serviceState = new ServiceStateT();
    serviceState.setValue(ServiceStateEnumT.IN_SERVICE);
    JAXBElement<ServiceStateT> osServiceState = objFactory.createOperationsSystemTServiceState(serviceState);
    os.setServiceState(osServiceState);

    // Subordinate OS
    JAXBElement<Boolean> osSubordinateOS = objFactory.createOperationsSystemTSubordinateOS(Boolean.FALSE);
    os.setSubordinateOS(osSubordinateOS);

    // Vendor Extensions
	JAXBElement<OSVendorExtensionsT> osVendorExt = getVendorExtentions(objFactory);
	os.setVendorExtensions(osVendorExt);
	// Not supported.
	
	return os;
  }

	/**
	 * Construct the vendor extension <workingMode> for the getOS and getAllOSs mtosi commands.
	 * @return JAXBElement<OSVendorExtensionsT> containing the Vendor extension.
	 */
	private static JAXBElement<OSVendorExtensionsT> getVendorExtentions(ObjectFactory objFactory) {
	    OSVendorExtensionsT extensions = new OSVendorExtensionsT();
	    String workingMode = null;
	    WorkMode wMode = MtosiCtrlImpl.getServerCtrlImpl().getWorkMode();
	    if (wMode == WorkMode.MASTER) {
	    	workingMode = MtosiConstants.VENDOR_WORKING_MODE_MASTER;
	    } else { //Do I need to check for a different State?
	    	workingMode = MtosiConstants.VENDOR_WORKING_MODE_SLAVE;
	    }
	    
	    JAXBElement<String> mode = new JAXBElement<String>(new
                QName(MtosiConstants.VENDOR_NAMESPACE,MtosiConstants.VENDOR_WORKING_MODE), String.class, workingMode);
	    extensions.getAny().add(mode);
		return objFactory.createOperationsSystemTVendorExtensions(extensions);
	}
	

  public static ManagementDomainT getMtosiMD() {
    ObjectFactory objFactory = new ObjectFactory();
    ManagementDomainT md = objFactory.createManagementDomainT();

    // Name
    NamingAttributesT namingAttributesT = createNamingAttributesMd();
    JAXBElement<NamingAttributesT> mdName = objFactory.createManagementDomainTName(namingAttributesT);
    md.setName(mdName);

    // Discovered Name
    md.setDiscoveredName(getMDNm());

    // Naming OS
    JAXBElement<String> namingOS = objFactory.createManagedElementTNamingOS(OSFactory.getNmsName());
    md.setNamingOS(namingOS);

    // User Label
    // Not supported.

    // Source
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    JAXBElement<SourceT> osSource = objFactory.createOperationsSystemTSource(source);
    md.setSource(osSource);

    // Owner
    // Not supported.

    // Alias Name List
    // Not supported.

    // ResourceState
    // Not supported.

    // Network Access Domain
    // Not supported.

    // Vendor Extensions
    // Not supported.

    return md;
  }

  public static NamingAttributesT createNamingAttributesMd() {
    NamingAttributesT namingAttributes = new NamingAttributesT();
    namingAttributes.setMdNm(getMDNm());
    return namingAttributes;
  }

  public static String getMDNm() {
    try {
      return MtosiConstants.NAMING_ADVA + ConfigCtrlImpl.get().getHandlers().getSubnetHdlr().getName(ConfigCtrlImpl.get().getHandlers().getSubnetHdlr().getTopLevelSubnetID());
    } catch (NoSuchMDObjectException x) {
      return "???";
    }
  }
}

