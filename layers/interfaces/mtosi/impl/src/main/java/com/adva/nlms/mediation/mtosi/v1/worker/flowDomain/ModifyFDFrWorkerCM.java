/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.serviceProvisioning.ACCPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.NETPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.ProtectionGroupF3SPProperties;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.Module;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3Impl;
import com.adva.nlms.mediation.config.f3.entity.port.acc.MTOSIPortF3Acc;
import com.adva.nlms.mediation.config.f3.entity.port.net.MTOSIPortF3Net;
import com.adva.nlms.mediation.config.f3.mtosi.NetworkElementFSP150CMMTOSIOperations;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FDFrEndFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FDFrFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FDFrFTPEndFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FDFrNETEndFSP150CM;
import com.adva.nlms.mediation.config.mtosi.FDFr;
import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiFDFrMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.TPDataListT;
import v1.tmf854.TPDataT;
import v1.tmf854ext.adva.ModifyFDFrT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

//import com.adva.nlms.mediation.common.serviceProvisioning.CFMCCMSPPropertiesFSP150CC;

public class ModifyFDFrWorkerCM extends ModifyFDFrWorker
{
  private MTOSIFlowF3 flowCM;
  private MTOSIPortF3Net portNET;
  private MTOSIPortF3Net portNET2 = null;
  private int fdfrSlot = -1;
  private FTP ftp;
//  private TPDataT cfmData;
  private MTOSIPortF3Acc portACC_CM;
  private TPDataT tpDataACC;
  private TPDataT tpDataNET;
  private TPDataT tpDataNET2;
  private TPDataT tpDataFlowCM;
  private TPDataT tpDataFTPCM;

  public ModifyFDFrWorkerCM (ModifyFDFrT mtosiBody, Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, FDFr fdfr, NetworkElement ne)
  {
    super(mtosiBody, mtosiHeader, namingAttributes, fdfr, ne);
  }

  @Override
  protected void mediate() throws Exception {
    FDFrFSP150CM cmFDFr = (FDFrFSP150CM) fdfr;

    FDFrEndFSP150CM aEnd = cmFDFr.getAEnd();
    FDFrEndFSP150CM zEnd = cmFDFr.getZEnd();

    boolean protection = false;

    if (aEnd instanceof FDFrFTPEndFSP150CM) {
      FDFrFTPEndFSP150CM ftpEnd = (FDFrFTPEndFSP150CM) aEnd;
      FTP ftp = ftpEnd.getFTP();
      portNET = (MTOSIPortF3Net) ftp.getAPort();
      portNET2 = (MTOSIPortF3Net) ftp.getBPort();
      protection = true;
    }
    if (zEnd instanceof FDFrFTPEndFSP150CM) {
      FDFrFTPEndFSP150CM ftpEnd = (FDFrFTPEndFSP150CM) zEnd;
      FTP ftp = ftpEnd.getFTP();
      portNET = (MTOSIPortF3Net) ftp.getAPort();
      portNET2 = (MTOSIPortF3Net) ftp.getBPort();
      protection = true;
    }
    if (protection) {
      fdfrSlot = NamingTranslationFactory.slotNumberFromShelfCombo(portNET.getMtosiName());
      if (fdfrSlot == -1) {
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
                "The slot of the NET port was not valid.");
      }
      mediate_LAG();
    } else {
      if (aEnd instanceof FDFrNETEndFSP150CM) {
        FDFrNETEndFSP150CM netEnd = (FDFrNETEndFSP150CM) aEnd;
        MTOSIPortF3Net netPort = netEnd.getPort();
        fdfrSlot = NamingTranslationFactory.slotNumberFromShelfCombo(netPort.getMtosiName());
        if (fdfrSlot == -1) {
          throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
                  "The slot of the NET port was not valid.");
        }
      }
      if (zEnd instanceof FDFrNETEndFSP150CM)
      {
        FDFrNETEndFSP150CM netEnd = (FDFrNETEndFSP150CM) zEnd;
        MTOSIPortF3Net netPort = netEnd.getPort();
        fdfrSlot = NamingTranslationFactory.slotNumberFromShelfCombo(netPort.getMtosiName());
        if (fdfrSlot == -1) {
          throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
                  "The slot of the NET port was not valid.");
        }

      }
      mediate_NoLAG();
    }
  }

  private void mediate_LAG() throws Exception {
    ACCPortSPPropertiesFSP150CM propsACC = null;
    NETPortSPPropertiesFSP150CM propsNET = null;
    NETPortSPPropertiesFSP150CM propsNET2 = null;
    FlowSPPropertiesFSP150CM propsFlow = null;
    ProtectionGroupF3SPProperties propsFTP = null;
//    CFMCCMSPPropertiesFSP150CC propsCfm = null;

    NetworkElementFSP150CM cmNE = (NetworkElementFSP150CM) ne;

    if (tpsToModify != null) {
      if (!MtosiTPMediator.checkTPToModifySameSlot(fdfrSlot, tpsToModify)) {
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                "The specified TPs must be on the same Slot as the FDFr.");
      }

      if (!MtosiTPMediator.checkTPToModifySameNE(ne, tpsToModify)) {
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                "The specified TPs must be on the same Network Element.");
      }

      tpDataACC = MtosiTPMediator.getTPDataTForCMACC(tpsToModify);
      tpDataNET = MtosiTPMediator.getTPDataTForCMNET(tpsToModify, 1);
      tpDataNET2 = MtosiTPMediator.getTPDataTForCMNET(tpsToModify, 2);
      tpDataFlowCM = MtosiTPMediator.getTPDataTForCTP(tpsToModify);
      tpDataFTPCM = MtosiTPMediator.getTPDataTForFTP(tpsToModify);
//      cfmData = MtosiTPMediator.getTPDataTForCCCFM(tpsToModify);
    }
    if (tpDataFlowCM != null) {
      flowCM = ManagedElementFactory.getCMFlow(tpDataFlowCM.getTpName());
      if (flowCM == null) {
          throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.CTP_NOT_FOUND);    	  
      }
      propsFlow = getCTPProperties();
//      if (cfmData != null) {
//          if (MtosiTPMediator.hasCfmProperties(cfmData)) {
//            propsCfm = MtosiTPMediator.mtosiTPDataTToCC_CFMProperties(cfmData,  false);
//          }
//        }
    }
    if (tpDataACC != null) {
      Port portL = ManagedElementFactory.getPort(tpDataACC.getTpName());
      portACC_CM = (MTOSIPortF3Acc) portL;
      propsACC = MtosiTPMediator.mtosiTPDataTToCMACCProperties(tpDataACC, portACC_CM);
    }
    if (tpDataNET != null) {
      propsNET = MtosiTPMediator.mtosiTPDataTToCMNETProperties(tpDataNET, portNET);
    }
    if (tpDataNET2 != null) {
      propsNET2 = MtosiTPMediator.mtosiTPDataTToCMNETProperties(tpDataNET2, portNET2);
    }
    if (tpDataFTPCM != null) {
      String ftpName = tpDataFTPCM.getTpName().getFtpNm();
      ftp = cmNE.getMTOSIWorker().getFTP(ftpName);
      if (ftp == null) {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                "The specified FTP does not exist.");
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      propsFTP = MtosiTPMediator.mtosiTPDataTToCMFTPProperties(tpDataFTPCM, ftp);
    }

    String fdfrName = namingAttributes.getFdfrNm();
    FDFrSPProperties propsFDFr = MtosiFDFrMediator.mtosiModifyFDFRToFDFRSPProperties(fdfrName, modifyData);
    transact(ne, propsACC, propsNET, propsNET2, propsFlow, propsFDFr, propsFTP /*, propsCfm*/);
  }

	/**
	 * Extract the device specific properties
	 * @return
	 */
	protected FlowSPPropertiesFSP150CM getCTPProperties()  throws ProcessingFailureException{
    boolean isNteModule = isNteModule(getFlowCM());
		return MtosiTPMediator.mtosiTPDataTToCM_CTPProperties(getTpDataFlowCM(), getFlowCM(),isNteModule);
	}
	
  private void mediate_NoLAG() throws Exception {
    ACCPortSPPropertiesFSP150CM propsACC = null;
    NETPortSPPropertiesFSP150CM propsNET = null;
    FlowSPPropertiesFSP150CM propsFlow = null;
//    CFMCCMSPPropertiesFSP150CC propsCfm = null;

    if (tpsToModify != null) {
      if (!MtosiTPMediator.checkTPToModifySameSlot(fdfrSlot, tpsToModify)) {
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                "The specified TPs must be on the same Slot as the FDFr.");
      }

      if (!MtosiTPMediator.checkTPToModifySameNE(ne, tpsToModify)) {
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                "The specified TPs must be on the same Network Element.");
      }

      tpDataACC = MtosiTPMediator.getTPDataTForCMACC(tpsToModify);
      tpDataNET = MtosiTPMediator.getTPDataTForCMNET(tpsToModify, 1);
      tpDataFlowCM = MtosiTPMediator.getTPDataTForCTP(tpsToModify);
//      cfmData = MtosiTPMediator.getTPDataTForCCCFM(tpsToModify);
    }
    if (tpDataFlowCM != null) {
      flowCM = ManagedElementFactory.getCMFlow(tpDataFlowCM.getTpName());
      if (flowCM == null) {
          throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.CTP_NOT_FOUND);    	  
      }
      propsFlow = getCTPProperties();
//      if (cfmData != null && flowCM != null) {
//          if (MtosiTPMediator.hasCfmProperties(cfmData)) {
//            propsCfm = MtosiTPMediator.mtosiTPDataTToCC_CFMProperties(cfmData, false);
//          }
//        }
    }
    if (tpDataACC != null) {
      Port portL = ManagedElementFactory.getPort(tpDataACC.getTpName());
      portACC_CM = (MTOSIPortF3Acc) portL;
      propsACC = MtosiTPMediator.mtosiTPDataTToCMACCProperties(tpDataACC, portACC_CM);

    }
    if (tpDataNET != null) {
      Port portN = ManagedElementFactory.getPort(tpDataNET.getTpName());
      portNET = (MTOSIPortF3Net) portN;
      propsNET = MtosiTPMediator.mtosiTPDataTToCMNETProperties(tpDataNET, portNET);
    }

    String fdfrName = namingAttributes.getFdfrNm();
    FDFrSPProperties propsFDFr = MtosiFDFrMediator.mtosiModifyFDFRToFDFRSPProperties(fdfrName, modifyData);
    transact(ne, propsACC, propsNET, null, propsFlow, propsFDFr, null /*, propsCfm*/);
  }

  private void transact(NetworkElement ne, ACCPortSPPropertiesFSP150CM propsACC_CM, NETPortSPPropertiesFSP150CM propsNET,
                         NETPortSPPropertiesFSP150CM propsNET2, FlowSPPropertiesFSP150CM propsFlow, FDFrSPProperties fdfrProps, ProtectionGroupF3SPProperties propsFTP /*,CFMCCMSPPropertiesFSP150CC propsCfm*/)
          throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure

  {
      NetworkElement locks[] = new NetworkElement[]
              { ne };
      Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "ModifyFDFrWorker");
      if (fdfrProps != null) {
        ne.getMTOSIWorker().setFDFrOperationInProgress(fdfrProps.get(FDFrSPProperties.VS.FDFrName), true);
      }
      try {
        if (fdfrProps != null) {
        	logSecurity(ne, SystemAction.ModifyNetwork, fdfr.getFDFrSPProperties().get(FDFrSPProperties.VS.FDFrName));
        	fdfr.setFDFrSPProperties(fdfrProps);
        }
        if (propsACC_CM != null) {
          logSecurity(ne, SystemAction.ModifyNetwork, portACC_CM.getMtosiName());
          portACC_CM.setSettings(propsACC_CM);
        }
        if (propsNET != null) {
          logSecurity(ne, SystemAction.ModifyNetwork, portNET.getMtosiName());
          portNET.setSettings(propsNET);
        }
        if (propsFlow != null) {
          logSecurity(ne, SystemAction.ModifyNetwork, flowCM.getMtosiName());
          flowCM.setFlowSettings(propsFlow);
        }
//        if (propsCfm != null && flowCM != null) {
//        	propsCfm.set(CFMCCMSPPropertiesFSP150CC.VB.GEDeviceType, Boolean.TRUE);
//            logSecurity(ne, SystemAction.ModifyNetwork, flowCM.getMtosiName() + " (CFM)");
//            flowCM.setCFMCCMSettings(propsCfm);
//          }
        if (propsFTP != null) {
          logSecurity(ne, SystemAction.ModifyNetwork, "ftpNm=" + ftp.getFTPName());
          ftp.setFTPSPProperties(propsFTP);
        }
        if (propsNET2 != null) {
          logSecurity(ne, SystemAction.ModifyNetwork, portNET2.getMtosiName());
          portNET2.setSettings(propsNET2);
        }

        NetTransactionManager.commitNetTransaction(id);
      }
      catch (NetTransactionException e) {
        ServiceUtils.rollbackNetTransaction(id, e, LOG);
      } catch (SPValidationException e) {
        ServiceUtils.rollbackNetTransaction(id, e, LOG);
      } catch (SNMPCommFailure e) {
        ServiceUtils.rollbackNetTransaction(id, e, LOG);
      } finally {
        if (fdfrProps != null) {
          ne.getMTOSIWorker().setFDFrOperationInProgress(fdfrProps.get(FDFrSPProperties.VS.FDFrName), false);
        }
        NetTransactionManager.ensureEnd(id);
      }
  }

  @Override
  protected TPDataListT getUpdatedTPs() throws ProcessingFailureException {
    ObjectFactory factory = new ObjectFactory();
    TPDataListT tpsToModify = factory.createTPDataListT();
    if (tpDataACC != null && portACC_CM != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataACC.getTpName())).toMtosiPTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataACC.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
    }
    if (tpDataNET != null && portNET != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataNET.getTpName())).toMtosiPTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataNET.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
    }
    if (tpDataFlowCM != null && flowCM != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ManagedElementFactory.getCMFlow(tpDataFlowCM.getTpName())).toMtosiCTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getCMFlow(tpDataFlowCM.getTpName()).getMtosiTranslator().toMtosiCTPasTPDataT());
    }
    if (tpDataNET2 != null && portNET2 != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataNET2.getTpName())).toMtosiPTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataNET2.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
    }
    if (tpDataFTPCM != null && ftp != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ManagedElementFactory.getFtp(tpDataFTPCM.getTpName())).toMtosiFTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getFtp(tpDataFTPCM.getTpName()).getMtosiTranslator().toMtosiFTPasTPDataT());
    }
    return tpsToModify;
  }
  /**
   * Checks if the Flow is on an NTE Card
   * @param flow
   * @return
   */
    private boolean isNteModule(MTOSIFlowF3 flow) {
      FlowSPPropertiesFSP150CM props = flow.getFlowSPProperties();
      int shelfIndex = props.get(FlowSPPropertiesFSP150CM.VI.ShelfIndex);
      int slotIndex = props.get(FlowSPPropertiesFSP150CM.VI.SlotIndex);
      NetworkElement ne = flow.getPortFSP150CMAcc().getNE();
      Module module = ((NetworkElementFSP150CMMTOSIOperations) ne.getMTOSIWorker()).getModule(shelfIndex, slotIndex);
      if (module instanceof NteF3Impl)
        return true;
      return false;
  }
  /*
   * Getters for the sub class access
   */
  protected MTOSIFlowF3 getFlowCM() {
		return flowCM;
	}

	protected TPDataT getTpDataFlowCM() {
		return tpDataFlowCM;
	}


}
