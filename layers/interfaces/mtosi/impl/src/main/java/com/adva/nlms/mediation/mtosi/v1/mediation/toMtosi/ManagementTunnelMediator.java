/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi;

import com.adva.nlms.mediation.config.mtosi.ManagementCTPProperties;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.PhysicalTerminationPointT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;


public class ManagementTunnelMediator {
  private static final Logger log = LogManager.getLogger(ManagementTunnelMediator.class);

  public static PhysicalTerminationPointT getManagementTunnel(ManagementCTPProperties ctpProperties,NamingAttributesT namingAttributes) throws ProcessingFailureException {
    if (ctpProperties.getErrorCode() == 1) {
      log.debug("Management Tunnel error: "+ ctpProperties.getErrorMsg());
       throw  ServiceUtils.createNewPFE( ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
               ctpProperties.getErrorMsg());
    }
    PhysicalTerminationPointT ptp = new PhysicalTerminationPointT();
    final ObjectFactory objFactory = new ObjectFactory();
    /**
     * <mdNm>containing MD name</mdNm>
     * <meNm>containing ME name</meNm>
     * <ptpNm>containing PTP name</ptpNm>
     * <ctpNm>#name specified by OS#</ctpNm>
     */
    namingAttributes.setCtpNm(ctpProperties.getResponse().getTunnelName());
    JAXBElement<NamingAttributesT> namingAttributesJax = objFactory.createEquipmentTName(namingAttributes);
    ptp.setName(namingAttributesJax);
    // layers
    final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();
    createLayeredParameters(layeredParametersListT,ctpProperties);
    ptp.setTransmissionParams(objFactory.createPhysicalTerminationPointTTransmissionParams(layeredParametersListT));
    return ptp;
  }

  protected static void createLayeredParameters(final LayeredParametersListT transmissionParameters,final ManagementCTPProperties ctpProperties) {
    // -------start Layer--------

    //---- add parameters ----
    LayeredParameterUtils.addLayer(transmissionParameters, LayeredParams.PROP_ADVA_Tunnel);

    LayeredParameterUtils.addLayeredParameter(transmissionParameters, LayeredParams.PROP_ADVA_Tunnel,
            LayeredParams.LrPropAdvaTunnel.TUNNEL_INDEX, String.valueOf(ctpProperties.getResponse().getTunnelIndex()));

    LayeredParameterUtils.addLayeredParameter(transmissionParameters,LayeredParams.PROP_ADVA_Tunnel,
            LayeredParams.LrPropAdvaTunnel.TUNNEL_TYPE,String.valueOf(ctpProperties.getResponse().getTunnelType()));

    LayeredParameterUtils.addLayeredParameter(transmissionParameters,LayeredParams.PROP_ADVA_Tunnel,
            LayeredParams.LrPropAdvaTunnel.IP_ADDRESS,ctpProperties.getResponse().getTunnelIpAddress());

    LayeredParameterUtils.addLayeredParameter(transmissionParameters,LayeredParams.PROP_ADVA_Tunnel,
            LayeredParams.LrPropAdvaTunnel.SUBNET_MASK,ctpProperties.getResponse().getTunnelSubnetMask());

    if( !String.valueOf(ctpProperties.getResponse().getTunnelDefaultGateway()).equals("null"))  {
    LayeredParameterUtils.addLayeredParameter(transmissionParameters,LayeredParams.PROP_ADVA_Tunnel,
            LayeredParams.LrPropAdvaTunnel.DEFAULT_GATEWAY,
            String.valueOf(ctpProperties.getResponse().getTunnelDefaultGateway()));
    }else{
        LayeredParameterUtils.addLayeredParameter(transmissionParameters,LayeredParams.PROP_ADVA_Tunnel,
                LayeredParams.LrPropAdvaTunnel.DEFAULT_GATEWAY,
                "");
    }

    LayeredParameterUtils.addLayeredParameter(transmissionParameters,LayeredParams.PROP_ADVA_Tunnel,
            LayeredParams.LrPropAdvaTunnel.VLANID,String.valueOf(ctpProperties.getResponse().getTunnelVlanId()));

    LayeredParameterUtils.addLayeredParameter(transmissionParameters,LayeredParams.PROP_ADVA_Tunnel,
            LayeredParams.LrPropAdvaTunnel.CIR,String.valueOf(ctpProperties.getResponse().getTunnelCIR()));

    LayeredParameterUtils.addLayeredParameter(transmissionParameters,LayeredParams.PROP_ADVA_Tunnel,
            LayeredParams.LrPropAdvaTunnel.EIR,String.valueOf(ctpProperties.getResponse().getTunnelEIR()));

    LayeredParameterUtils.addLayeredParameter(transmissionParameters,LayeredParams.PROP_ADVA_Tunnel,
            LayeredParams.LrPropAdvaTunnel.COS,String.valueOf(ctpProperties.getResponse().getTunnelCOS()));

    LayeredParameterUtils.addLayeredParameter(transmissionParameters,LayeredParams.PROP_ADVA_Tunnel,
            LayeredParams.LrPropAdvaTunnel.BUFFER_SIZE,String.valueOf(ctpProperties.getResponse().getTunnelBufferSize()));

    LayeredParameterUtils.addLayeredParameter(transmissionParameters,LayeredParams.PROP_ADVA_Tunnel,
            LayeredParams.LrPropAdvaTunnel.S_VLANID_ENABLED,String.valueOf(ctpProperties.getResponse().isTunnelSvlanIdEnabled()));

    if (ctpProperties.getResponse().isTunnelSvlanIdEnabled()) {
    LayeredParameterUtils.addLayeredParameter(transmissionParameters,LayeredParams.PROP_ADVA_Tunnel,
            LayeredParams.LrPropAdvaTunnel.S_TAG_VLANID,String.valueOf(ctpProperties.getResponse().getTunnelSTagVlanId()));
    }

    LayeredParameterUtils.addLayeredParameter(transmissionParameters,LayeredParams.PROP_ADVA_Tunnel,
            LayeredParams.LrPropAdvaTunnel.RIP_2_PKTS_ENABLED,String.valueOf(ctpProperties.getResponse().isTunnelRip2pktsEnabled()));

    LayeredParameterUtils.addLayeredParameter(transmissionParameters,LayeredParams.PROP_ADVA_Tunnel,
            LayeredParams.LrPropAdvaTunnel.ENCAPSULATION_TYPE,String.valueOf(ctpProperties.getResponse().getTunnelEncapsulationType()));

    LayeredParameterUtils.addLayeredParameter(transmissionParameters,LayeredParams.PROP_ADVA_Tunnel,
            LayeredParams.LrPropAdvaTunnel.MTU,String.valueOf(ctpProperties.getResponse().getTunnelMTU()));
     // finish adding parameters -----
    // -------end of Layer-------

    // -------start Layer--------
    if(ctpProperties.getResponse().getAmpRole() == 2){
        LayeredParameterUtils.addLayer(transmissionParameters, LayeredParams.PROP_ADVA_RemoteCPE);

        // ------ add parameters ----
        LayeredParameterUtils.addLayeredParameter(transmissionParameters, LayeredParams.PROP_ADVA_RemoteCPE,
                LayeredParams.LrPropAdvaRemoteCPE.CPE_HOSTNAME_PARM, ctpProperties.getResponse().getRemoteCpeHostname());

        LayeredParameterUtils.addLayeredParameter(transmissionParameters, LayeredParams.PROP_ADVA_RemoteCPE,
                LayeredParams.LrPropAdvaRemoteCPE.IP_ADDRESS, ctpProperties.getResponse().getRemoteCpeIpAddress());

        LayeredParameterUtils.addLayeredParameter(transmissionParameters, LayeredParams.PROP_ADVA_RemoteCPE,
                LayeredParams.LrPropAdvaRemoteCPE.SUBNET_MASK, ctpProperties.getResponse().getRemoteCpeSubnetMask());

        if(ctpProperties.getResponse().getRemoteCpeRemoteType() != null){
            LayeredParameterUtils.addLayeredParameter(transmissionParameters, LayeredParams.PROP_ADVA_RemoteCPE,
                    LayeredParams.LrPropAdvaRemoteCPE.REMOTE_TYPE_PARM, ctpProperties.getResponse().getRemoteCpeRemoteType());
        }else{
            LayeredParameterUtils.addLayeredParameter(transmissionParameters, LayeredParams.PROP_ADVA_RemoteCPE,
                    LayeredParams.LrPropAdvaRemoteCPE.REMOTE_TYPE_PARM, ctpProperties.getRemoteType());
        }

        if( !String.valueOf(ctpProperties.getResponse().getRemoteCpeDefaultGateway()).equals("null"))  {
            LayeredParameterUtils.addLayeredParameter(transmissionParameters, LayeredParams.PROP_ADVA_RemoteCPE,
                    LayeredParams.LrPropAdvaRemoteCPE.DEFAULT_GATEWAY,  String.valueOf(ctpProperties.getResponse().getRemoteCpeDefaultGateway()));
        }else{
            LayeredParameterUtils.addLayeredParameter(transmissionParameters, LayeredParams.PROP_ADVA_RemoteCPE,
                    LayeredParams.LrPropAdvaRemoteCPE.DEFAULT_GATEWAY,  "");
        }
    }


    // ----- finish adding parameters ----

    // -------end of Layer-------

  }

}
