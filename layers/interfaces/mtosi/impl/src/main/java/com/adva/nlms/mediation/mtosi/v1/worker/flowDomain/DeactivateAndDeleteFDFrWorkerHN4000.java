/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.MDRequestFailedException;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.FTPSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.LAGSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN40002BpmeSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000BondingSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000EthernetSProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.UNISPPropertiesHN4000;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.hn4000.NetworkElementHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN40002Bpme;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet2BASE_TL;
import com.adva.nlms.mediation.config.hn4000.mtosi.FTPHN4000;
import com.adva.nlms.mediation.config.mtosi.FDFr;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.PMEPortPair;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.DeactivateAndDeleteFDFrWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.TPDataListT;
import v1.tmf854.TPDataT;
import v1.tmf854ext.adva.DeactivateAndDeleteFDFrT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.Iterator;
import java.util.List;

public class DeactivateAndDeleteFDFrWorkerHN4000 extends DeactivateAndDeleteFDFrWorker
{
	Logger LOG = LogManager.getLogger(this.getClass().getName());

	private NetworkElementHN4000 hnNE = null;
	// private FDFrSPPropertiesHN4000 propsFDFr = null;
	// private FDFrHN4000 fdfrHN = null;

	private UNISPPropertiesHN4000 eth1UniProps = null;
	private UNISPPropertiesHN4000 eth2UniProps = null;
	private UNISPPropertiesHN4000 lagUniProps = null;
	private UNISPPropertiesHN4000 bondedUniProps = null;

	// MO Objects
	private FTPHN4000 ftp = null;
	private PortHN4000Ethernet eth1 = null;
	private PortHN4000Ethernet eth2 = null;
	private PortHN4000Ethernet2BASE_TL bondedPort = null;

	// tpData objects
	private TPDataT tpDataLag = null;
	private TPDataT tpDataEth1 = null;
	private TPDataT tpDataEth2 = null;
	private TPDataT tpDataBonded = null;
	private List<TPDataT> tpDataPMEList = null;

	// properties objects
	private FTPSPProperties propsFTP = null;
	private PortHN4000EthernetSProperties propsEth1 = null;
	private PortHN4000EthernetSProperties propsEth2 = null;
	private PortHN4000BondingSPProperties propsBondedPort = null;

	private List<PMEPortPair> pmePairList = null;

	public DeactivateAndDeleteFDFrWorkerHN4000(DeactivateAndDeleteFDFrT mtosiBody, Holder<HeaderT> mtosiHeader, NetworkElement ne, FDFr fdfr)
	{
		super(mtosiBody, mtosiHeader, ne, fdfr);
	}

	private void transact() throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure, MDRequestFailedException
	{
		NetworkElement locks[] = new NetworkElement[]
		{ ne };
    String deleteName = null;
		Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "CreateAndActivateFDFrWorker");
		try {
			// Need the FDFr (EVC) first, and also the UNI, in order to create
			// the Flows
			
			deleteName = fdfr.getFDFrSPProperties().get(FDFrSPProperties.VS.FDFrName);
      ne.getMTOSIWorker().setFDFrOperationInProgress(deleteName, true);
			if(deleteName!=null)
			{
				logSecurity(ne, SystemAction.DeleteNetwork, deleteName);
			}

			hnNE.getMTOSIWorker().deleteFDFRr_HN4000((FDFrSPPropertiesHN4000) fdfr.getFDFrSPProperties());

			if (propsBondedPort != null) {
				if (propsBondedPort.size() > 0) {
					bondedPort.setSettings(propsBondedPort);
				}
				if (bondedUniProps != null) {
					if (bondedPort.hasUni()) {
						if (bondedUniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null) {
							bondedPort.deleteUNI();
						} else {
							bondedPort.setUniProperties(bondedUniProps, bondedPort.getUni().getUniSPProperties());
						}
					} else {
						bondedPort.createUni(bondedUniProps);
					}
				}

			} else {
				if (bondedUniProps != null) {
					if (bondedPort.hasUni()) {
						if (bondedUniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null) {
							bondedPort.deleteUNI();
						} else {
							bondedPort.setUniProperties(bondedUniProps, bondedPort.getUni().getUniSPProperties());
						}
					} else {
						bondedPort.createUni(bondedUniProps);
					}
				}

			}

			if (propsFTP != null) {
				ftp.setFTPSPProperties(propsFTP);
				if (lagUniProps != null) {
					if (ftp.getLag().hasUni()) {
						if (lagUniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null) {
							ftp.getLag().deleteUNI();
						} else {
							ftp.getLag().setUniProperties(lagUniProps, ftp.getLag().getUni().getUniSPProperties());
						}
					} else {
						ftp.getLag().createUni(lagUniProps);
					}
				}
			}

			if (propsEth1 != null) {
				logSecurity(ne, SystemAction.ModifyNetwork, "ptpNm=" + eth1.getMtosiName());
				if (propsEth1.size() > 0) {
					eth1.setSettings(propsEth1);
				}
				if (eth1UniProps != null) {
					if (eth1.hasUni()) {
						if (eth1UniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null) {
							eth1.deleteUNI();
						} else {
							eth1.setUniProperties(eth1UniProps, eth1.getUni().getUniSPProperties());
						}
					} else {
						eth1.createUni(eth1UniProps);
					}
				}
			}
			if (propsEth2 != null) {
				logSecurity(ne, SystemAction.ModifyNetwork, "ptpNm=" + eth2.getMtosiName());
				if (propsEth2.size() > 0) {
					eth2.setSettings(propsEth2);
				}
				if (eth2UniProps != null) {
					if (eth2.hasUni()) {
						if (eth2UniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null) {
							eth2.deleteUNI();
						} else {
							eth2.setUniProperties(eth2UniProps, eth2.getUni().getUniSPProperties());
						}
					} else {
						eth2.createUni(eth2UniProps);
					}
				}
			}

			for (Iterator iterator = pmePairList.iterator(); iterator.hasNext();) {
				PMEPortPair nextPair = (PMEPortPair) iterator.next();
				PortHN40002Bpme nextPort = nextPair.getPort();
				PortHN40002BpmeSPProperties nextProps = nextPair.getProps();
				nextPort.setSettings(nextProps);
			}

			NetTransactionManager.commitNetTransaction(id);

		}
		catch (NetTransactionException e) {
			ne.logSROperation(SROperationState.SERVICE_DELETION_FAILURE, deleteName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (SPValidationException e) {
			ne.logSROperation(SROperationState.SERVICE_DELETION_FAILURE, deleteName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (SNMPCommFailure e) {
			ne.logSROperation(SROperationState.SERVICE_DELETION_FAILURE, deleteName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (MDRequestFailedException e) {
			ne.logSROperation(SROperationState.SERVICE_DELETION_FAILURE, deleteName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		finally {
      ne.getMTOSIWorker().setFDFrOperationInProgress(deleteName, false);
			NetTransactionManager.ensureEnd(id);
		}
	}

	@Override
  protected void mediate() throws Exception
	{

		hnNE = (NetworkElementHN4000) ne;
		boolean hn400 = hnNE.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400;

		if (tpsToModify != null) {
			if (!MtosiTPMediator.checkTPToModifySameNE(ne, tpsToModify)) {
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
						"The specified TPs must be on the same Network Element.");
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}

			tpDataBonded = MtosiTPMediator.getTPDataTForHNBonded(tpsToModify);
			tpDataLag = MtosiTPMediator.getTPDataTForHNFTP(tpsToModify);
			tpDataEth1 = MtosiTPMediator.getTPDataTForHNPhysicalEth(tpsToModify, 1);
			tpDataEth2 = MtosiTPMediator.getTPDataTForHNPhysicalEth(tpsToModify, 2);
			tpDataPMEList = MtosiTPMediator.getTPDataListForHN2BPME(tpsToModify);
		} else {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NAME_MISSING);
		}

		if (tpDataLag != null) {
			ftp = (FTPHN4000) ManagedElementFactory.getFtp(tpDataLag.getTpName());
			if (ftp == null) {
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
			}
			propsFTP = MtosiTPMediator.mtosiTPDataTToHN4000FTPProperties(tpDataLag, tpDataLag.getTpName().getFtpNm());
		}

		if (tpDataEth1 != null) {
			Port port = ManagedElementFactory.getPort(tpDataEth1.getTpName());
			eth1 = (PortHN4000Ethernet) port;
			propsEth1 = MtosiTPMediator.mtosiTPDataTToHNEthernetProperties(tpDataEth1, eth1, hn400);
			eth1UniProps = MtosiTPMediator.mtosiTPDataTToHNUniProperties(tpDataEth1, ((NetworkElement)eth1.getNE()).getMTOSIWorker().getNetworkElementTypeForMTOSI(), eth1.hasUni());
			if (eth1UniProps != null && eth1.hasUni()) {
				UNISPPropertiesHN4000 props = eth1.getUni().getUniSPProperties();
				eth1UniProps.set(UNISPPropertiesHN4000.VL.DeviceId, props.get(UNISPPropertiesHN4000.VL.DeviceId));
				eth1UniProps.set(UNISPPropertiesHN4000.VL.PortId, props.get(UNISPPropertiesHN4000.VL.PortId));
			}
		}
		if (tpDataEth2 != null) {
			Port port = ManagedElementFactory.getPort(tpDataEth2.getTpName());
			eth2 = (PortHN4000Ethernet) port;
			propsEth2 = MtosiTPMediator.mtosiTPDataTToHNEthernetProperties(tpDataEth2, eth2, hn400);
			eth2UniProps = MtosiTPMediator.mtosiTPDataTToHNUniProperties(tpDataEth2, ((NetworkElement)eth2.getNE()).getMTOSIWorker().getNetworkElementTypeForMTOSI(), eth2.hasUni());
			if (eth2UniProps != null && eth2.hasUni()) {
				UNISPPropertiesHN4000 props = eth2.getUni().getUniSPProperties();
				eth2UniProps.set(UNISPPropertiesHN4000.VL.DeviceId, props.get(UNISPPropertiesHN4000.VL.DeviceId));
				eth2UniProps.set(UNISPPropertiesHN4000.VL.PortId, props.get(UNISPPropertiesHN4000.VL.PortId));
			}
		}
		if (tpDataBonded != null) {
			if (bondedPort == null) {
				bondedPort = hnNE.getBondedPort(tpDataBonded.getTpName().getFtpNm());
			}

			propsBondedPort = MtosiTPMediator.mtosiTPDataTToHN4000FTPBondProperties(tpDataBonded, null, hn400, bondedPort.getPortSPProperties(), true);
			bondedUniProps = MtosiTPMediator.mtosiTPDataTToHNUniProperties(tpDataBonded, ((NetworkElement)bondedPort.getNE()).getMTOSIWorker().getNetworkElementTypeForMTOSI(), bondedPort
					.hasUni());
			if (bondedUniProps != null && bondedPort.hasUni()) {
				UNISPPropertiesHN4000 props = bondedPort.getUni().getUniSPProperties();
				bondedUniProps.set(UNISPPropertiesHN4000.VL.DeviceId, props.get(UNISPPropertiesHN4000.VL.DeviceId));
				bondedUniProps.set(UNISPPropertiesHN4000.VL.PortId, props.get(UNISPPropertiesHN4000.VL.PortId));
			}
		}
		if (tpDataLag != null) {
			if (ftp == null) {
				ftp = (FTPHN4000) hnNE.getMTOSIWorker().getFTP(tpDataLag.getTpName().getFtpNm());
				if (ftp == null) {
					throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
				}
			}

			propsFTP = MtosiTPMediator.mtosiTPDataTToHN4000FTPProperties(tpDataLag, null, (LAGSPPropertiesHN4000) ftp
					.getFTPSPProperties());
			lagUniProps = MtosiTPMediator.mtosiTPDataTToHNUniProperties(tpDataLag, ((NetworkElement)ftp.getLag().getNE()).getMTOSIWorker().getNetworkElementTypeForMTOSI(), ftp.getLag().hasUni());
			if (lagUniProps != null && ftp.getLag().hasUni()) {
				UNISPPropertiesHN4000 props = ftp.getLag().getUni().getUniSPProperties();
				lagUniProps.set(UNISPPropertiesHN4000.VL.DeviceId, props.get(UNISPPropertiesHN4000.VL.DeviceId));
				lagUniProps.set(UNISPPropertiesHN4000.VL.PortId, props.get(UNISPPropertiesHN4000.VL.PortId));
			}

		}
		if (tpDataPMEList != null) {
			pmePairList = MtosiTPMediator.mtosiTPDataListToPMEPortPair(tpDataPMEList, hn400);
		}

		transact();
	}

  @Override
  protected void response () throws Exception {
	  v1.tmf854ext.adva.ObjectFactory factory = new v1.tmf854ext.adva.ObjectFactory();
	  // have to get updated Ports that were in the request tpsToModify
	  response.setTpsToModify(factory.createDeactivateAndDeleteFDFrResponseTTpsToModify(getUpdatedTPs())); 
  }

  protected TPDataListT getUpdatedTPs() throws ProcessingFailureException
	{
		ObjectFactory objectFactory = new ObjectFactory();
		TPDataListT tpsToModify = objectFactory.createTPDataListT();

		if (tpDataEth1 != null && eth1 != null) {
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataEth1.getTpName())).toMtosiPTPasTPDataT());
//			tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataEth1.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
		if (tpDataEth2 != null && eth2 != null) {
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataEth2.getTpName())).toMtosiPTPasTPDataT());
//			tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataEth2.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
		if (tpDataBonded != null && bondedPort != null) {
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  null).getMtosiTranslator(ManagedElementFactory.getHN4000Ftp(tpDataBonded.getTpName())).toMtosiFTPasTPDataT());
//			tpsToModify.getTpData().add(ManagedElementFactory.getHN4000Ftp(tpDataBonded.getTpName()).getMtosiTranslator().toMtosiFTPasTPDataT());
		}
		if (tpDataLag != null && ftp != null) {
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  null).getMtosiTranslator(ManagedElementFactory.getHN4000Ftp(tpDataLag.getTpName())).toMtosiFTPasTPDataT());
//			tpsToModify.getTpData().add(ManagedElementFactory.getHN4000Ftp(tpDataLag.getTpName()).getMtosiTranslator().toMtosiFTPasTPDataT());
		}

		if (tpDataPMEList != null) {
			for (Iterator iterator = tpDataPMEList.iterator(); iterator.hasNext();) {
				TPDataT nextTP = (TPDataT) iterator.next();
				tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(nextTP.getTpName())).toMtosiPTPasTPDataT());
//				tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(nextTP.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
			}
		}

		return tpsToModify;
	}
}