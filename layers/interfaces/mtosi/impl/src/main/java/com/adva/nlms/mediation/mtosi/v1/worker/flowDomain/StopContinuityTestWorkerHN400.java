/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;

import jakarta.xml.ws.Holder;

public class StopContinuityTestWorkerHN400 extends StopContinuityTestWorkerHN {

	public StopContinuityTestWorkerHN400(Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne) {
		super(mtosiHeader, namingAttributes, ne);
	}

	@Override
  protected void mediate() throws Exception {
		super.mediate();
//		if (!(port instanceof PortHN4000Ethernet2BASE_TL)) {
//			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
//					"The port must be an FTP.");
//		}
		transact();
	}

	private void transact() throws ObjectInUseException, NetTransactionException, SNMPCommFailure, SPValidationException {
		NetworkElement locks[] = new NetworkElement[] { ne };
		Integer id = NetTransactionManager.beginUndoNetTransaction(locks, getClass().getSimpleName());
		try {
			flow.stopTrafficGenTest();
			NetTransactionManager.commitNetTransaction(id);
		} catch (NetTransactionException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SNMPCommFailure e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} finally {
			NetTransactionManager.ensureEnd(id);
		}
	}
}
