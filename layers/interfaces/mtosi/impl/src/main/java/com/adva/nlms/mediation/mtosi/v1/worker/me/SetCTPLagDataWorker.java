/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.TPDataT;
import v1.tmf854.TerminationPointT;
import v1.tmf854ext.adva.SetTPDataResponseT;

import jakarta.xml.ws.Holder;

public class SetCTPLagDataWorker extends SetTPDataWorker {

  public SetCTPLagDataWorker (Holder<HeaderT> mtosiHeader, TPDataT tpInfo, NamingAttributesT namingAttributes, NetworkElement ne) {
    super(mtosiHeader, tpInfo, namingAttributes, ne);
  }

  @Override
  protected void parse() throws Exception {
    if ( namingAttributes.getCtpNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ctpName has not been specified.");
    }
  }

  @Override
  protected void mediate() throws Exception {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.ME_NOT_SUPPORTED);

  }


  @Override
  protected void response() throws Exception {
    ObjectFactory objectFactory = new ObjectFactory();
    TerminationPointT tp = objectFactory.createTerminationPointT();
    response.setModifiedTP(tp);
  }

  @Override
  public SetTPDataResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}