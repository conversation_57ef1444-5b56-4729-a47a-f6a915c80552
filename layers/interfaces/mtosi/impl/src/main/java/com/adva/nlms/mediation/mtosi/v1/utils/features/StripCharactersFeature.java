/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.features;

import org.apache.cxf.Bus;
import org.apache.cxf.feature.AbstractFeature;
import org.apache.cxf.interceptor.InterceptorProvider;
import com.adva.nlms.mediation.mtosi.v1.utils.features.interceptor.StripCharactersOutInterceptor;


public class StripCharactersFeature extends AbstractFeature {

    private static final StripCharactersOutInterceptor OUT = new StripCharactersOutInterceptor();

    @Override
    protected void initializeProvider(InterceptorProvider provider, Bus bus) {

      provider.getOutInterceptors().add(OUT);

    }
  

}
