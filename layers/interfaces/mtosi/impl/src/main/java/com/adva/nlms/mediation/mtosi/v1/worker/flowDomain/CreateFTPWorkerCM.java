/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.serviceProvisioning.ProtectionGroupF3SPProperties;
import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.port.net.MTOSIPortF3Net;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CM;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.FTPCreateDataT;

import jakarta.xml.ws.Holder;

public class CreateFTPWorkerCM extends CreateFTPWorker
{
  public CreateFTPWorkerCM (Holder<HeaderT> mtosiHeader, FTPCreateDataT ftpCreateData, NamingAttributesT namingAttributes, NetworkElement ne) {
    super(mtosiHeader, ftpCreateData, namingAttributes, ne);
  }

  @Override
  protected void mediate() throws Exception {
    String pattern = "/(shelf)=(\\d+)/(slot)=(\\d+)/.+";
    NetworkElementFSP150CM neCM = (NetworkElementFSP150CM) ne;
    String ftpName = ftpCreateData.getName().getFtpNm();
    if (!ftpName.matches(pattern)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
      "The specified ftpNm is invalid.");
     
    }
    int shelf = NamingTranslationFactory.shelfNumberFromShelfCombo(ftpName);
    int slot = NamingTranslationFactory.slotNumberFromShelfCombo(ftpName);
     
    if (ManagedElementFactory.needsSlotIncrement(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
    	slot = slot + 1;
    }

    MTOSIPortF3Net portNET = (MTOSIPortF3Net) ne.getMTOSIWorker().getPortByName(MtosiConstants.PORT_NET1, shelf, slot);
    if(portNET==null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "Specified slot has not been provisioned.");
    }

    FTP ftp = neCM.getFTP(shelf, slot);
    if (ftp != null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_CAPACITY_EXCEEDED,
              "An FTP already exists on this slot.");
    }

    ProtectionGroupF3SPProperties newFTP = MtosiTPMediator.mtosiCreateFTPToNMSPropertiesCM(neCM, ftpCreateData);
    transact(ne, newFTP);
  }
}
