/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum CMAcceptableFramePolicyTranslation {
  TAGGED          (1, "True", "False"),
  UNTAGGED        (2, "False", "True"),
  ALL             (3, "True", "True"),
  NOT_APPLICABLE  (0, "n/a", "n/a");

  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String tagged;
  private final String untagged;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param tagged  The string representation used in MTOSI layer for tagged frames enabled.
   * @param untagged  The string representation used in MTOSI layer for untagged frames enabled.
   */
  private CMAcceptableFramePolicyTranslation (final int mibValue, final String tagged, final String untagged)
  {
    this.mibValue = mibValue;
    this.tagged   = tagged;
    this.untagged = untagged;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getTagged() {
    return tagged;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getUnTagged() {
    return untagged;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getTagged(final int mibValue)
  {
    CMAcceptableFramePolicyTranslation translation = NOT_APPLICABLE;  // the return value

    for (CMAcceptableFramePolicyTranslation tmpTranslation : values())
    {
      if (mibValue == tmpTranslation.getMIBValue())
      {
        translation = tmpTranslation;
        break;
      }
    }
    return translation.getTagged();
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getUnTagged(final int mibValue)
  {
    CMAcceptableFramePolicyTranslation translation = NOT_APPLICABLE;  // the return value

    for (CMAcceptableFramePolicyTranslation tmpTranslation : values())
    {
      if (mibValue == tmpTranslation.getMIBValue())
      {
        translation = tmpTranslation;
        break;
      }
    }
    return translation.getUnTagged();
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param tagged  The MIB defined value
   * @param untagged  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static int getMIBValue (final String tagged, final String untagged)
  {
    CMAcceptableFramePolicyTranslation translation = NOT_APPLICABLE;  // the return value

    for (CMAcceptableFramePolicyTranslation tmpTranslation : values())
    {
      if (tagged.equals(tmpTranslation.getTagged()) && untagged.equals(tmpTranslation.getUnTagged()))
      {
        translation = tmpTranslation;
        break;
      }
    }
    return translation.getMIBValue();

  }

  public static CMAcceptableFramePolicyTranslation getEnum(final int mibValue)
  {
    CMAcceptableFramePolicyTranslation translation = NOT_APPLICABLE;  // the return value

    for (CMAcceptableFramePolicyTranslation tmpTranslation : values())
    {
      if (mibValue == tmpTranslation.getMIBValue())
      {
        translation = tmpTranslation;
        break;
      }
    }
    return translation;
  }
}
