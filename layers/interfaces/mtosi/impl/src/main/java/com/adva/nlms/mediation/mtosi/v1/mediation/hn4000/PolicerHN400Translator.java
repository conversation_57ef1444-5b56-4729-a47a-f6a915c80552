/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.hn4000;

import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import v1.tmf854ext.adva.TCProfileT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.common.serviceProvisioning.ShaperSPPropertiesHN4000;
import com.adva.nlms.mediation.config.hn4000.PolicerHN4000;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;

/**
 * This class is a Hatteras shaper MTOSI Translator.
 */
public class PolicerHN400Translator extends MtosiTranslator {
	private PolicerHN4000 policer;

	public PolicerHN400Translator(PolicerHN4000 policer) {
		this.policer = policer;
	}

	@Override
  public TCProfileT toMtosiTCProfile() throws ProcessingFailureException {
		final v1.tmf854ext.adva.ObjectFactory objFactoryEx = new v1.tmf854ext.adva.ObjectFactory();
		final ObjectFactory objFactory = new ObjectFactory();
		final TCProfileT tcProfileT = objFactoryEx.createTCProfileT();
		ShaperSPPropertiesHN4000 props = policer.getSPProperties();

		// TCProfile Name
		final NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributes(policer,MtosiConstants.ING_POLICER_TEXT);
		tcProfileT.setName(objFactoryEx.createTCProfileTName(namingAttributes));

		// discoveredName
		final String tcpNm = namingAttributes.getTcpNm();
		if (tcpNm == null) {
			throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID);
		}
		tcProfileT.setDiscoveredName(objFactoryEx.createTCProfileTDiscoveredName(tcpNm));

		// namingOS
		tcProfileT.setNamingOS(objFactoryEx.createTCProfileTNamingOS(OSFactory.getNmsName()));

		// source
		final SourceT source = new SourceT();
		source.setValue(SourceEnumT.NETWORK_EMS);
		tcProfileT.setSource(objFactoryEx.createTCProfileTSource(source));

		// layers
		final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

		// -------start Layer--------
		LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_Policer);
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_Policer,
				LayeredParams.PropHatterasEthernetPolicer.INGRESS_CIR_PARAM, String.valueOf(props.get(ShaperSPPropertiesHN4000.VL.IngCIR)));

		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_Policer,
				LayeredParams.PropHatterasEthernetPolicer.INGRESS_CBS_PARAM, String.valueOf(props.get(ShaperSPPropertiesHN4000.VL.IngBC)));

		// -------end of Layer-------

		tcProfileT.setTransmissionParams(objFactoryEx.createTCProfileTTransmissionParams(layeredParametersListT));
		return tcProfileT;
	}
}
