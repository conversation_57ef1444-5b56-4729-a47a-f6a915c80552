/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */
package com.adva.nlms.mediation.mtosi.v2.utils.facility.continuity;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.FlowF3Attr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.dto.attr.QOSFlowPolicerAttr;

public class FlowF3Cont extends BaseFlowForContinuityTest<FlowF3Attr> {

  public FlowF3Cont(int neId, DTO<FlowF3Attr> flowPointDTO) {
    super(neId, flowPointDTO);
  }

  @Override
  public EntityIndex getEntityIndex() {
    return flowPointDTO.getValue(FlowF3Attr.ENTITY_INDEX);
  }

  @Override
  public int getCtagControl() {
    return flowPointDTO.getValue(FlowF3Attr.CTAG_CONTROL);
  }

  @Override
  public int getStagControl() {
    return flowPointDTO.getValue(FlowF3Attr.STAG_CONTROL);
  }

  @Override
  public String getPolicerSuffix() {
    return "ing";
  }

  @Override
  public <Y extends ManagedObjectAttr> Class<Y> getPolicerClass(){
    return (Class<Y>)QOSFlowPolicerAttr.class;
  }

  @Override
  public <Y extends ManagedObjectAttr> Long getCirValue(DTO<Y> dto ){
    return ((DTO<QOSFlowPolicerAttr>)dto).getValue(QOSFlowPolicerAttr.CIR_COMPOSITE);
  }
}
