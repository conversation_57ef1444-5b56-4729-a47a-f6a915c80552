/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum CMPolicerAlgorithmTranslation implements TranslatableEnum {
  SINGLE_RATE_TCM (1, "SingleRateTCM"),
  TWO_RATE_TCM    (2, "TwoRateTCM"),
  NOT_APPLICABLE  (3, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private CMPolicerAlgorithmTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}
