/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.tcProfile;


import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.GetTCProfilesWithTPResponseT;
import v1.tmf854ext.adva.GetTCProfilesWithTPT;
import v1.tmf854ext.adva.TCProfileListT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;


public class GetTCProfilesWithTPWorker extends AbstractMtosiWorker {
  protected GetTCProfilesWithTPT mtosiBody;
  protected GetTCProfilesWithTPResponseT response = new GetTCProfilesWithTPResponseT();
  protected NamingAttributesT tpName;
  protected TCProfileListT tcProfileListT;
  private NetworkElement ne;

  public GetTCProfilesWithTPWorker (final GetTCProfilesWithTPT mtosiBody, final Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getTCProfilesWithTP", "getTCProfilesWithTP", "getTCProfilesWithTPResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    if ((tpName = mtosiBody.getTpName()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              MtosiErrorConstants.INVALID_FILTER);
    }

    if (tpName.getMdNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
        MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!tpName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        MtosiErrorConstants.MD_NOT_FOUND);
    }

    if (tpName.getMeNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.ME_NAME_MISSING);
    }

    ne = ManagedElementFactory.getAndValidateNE(tpName);
  }
  
  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  @Override
  protected void mediate() throws Exception {

		if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400) {
			tcProfileListT = ManagedElementFactory.getTCProfilesWithTPHN(tpName);
		} else {
			if (NamingTranslationFactory.isPort(tpName))
				executeGetTCProfilesWithTPOnPort(tpName);
			else if (NamingTranslationFactory.isFlow(tpName))
				executeGetTCProfilesWithTPOnFlow(tpName);
			else {
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.TP_NAME_NOT_VALID);
			}
		}
	}

  private void executeGetTCProfilesWithTPOnPort(final NamingAttributesT tpName) throws ProcessingFailureException {
    tcProfileListT = ManagedElementFactory.getTCProfilesWithTPOnPort(tpName);
  }

  private void executeGetTCProfilesWithTPOnFlow(final NamingAttributesT tpName) throws ProcessingFailureException {
    tcProfileListT = ManagedElementFactory.getTCProfilesWithTPOnFlow(tpName);
  }

  @Override
  protected void response() throws Exception {
    response.setTcProfilesList(tcProfileListT);
  }

  @Override
  public GetTCProfilesWithTPResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
