/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.mediation.common.serviceProvisioning.ServiceSPPropertiesFSP150CC;

/**
 * Created by IntelliJ IDEA. User: Lukasz Date: 2007-05-30 Time: 13:14:09 To change this template use File | Settings |
 * File Templates.
 */
public enum LoopbackStatusTypeTranslation{
  NONE           (1, "None"), //status
  TERMINAL       (2, "Terminal"), //status + swapsada
  VLAN_TERMINAL  (3, "VLANTerminal"), //status + swapsada+vlanList
  LINE           (4, "Facility"), //status + swapsada
  VLAN_LINE      (5, "VLANFacility"), //status + swapsada+vlanList //lan = mask and 2 vlan list
  TERMINAL_TIMED (6, "TerminalTimed"),//Status. time swapsada
  LINE_TIMED     (7, "FacilityTimed"),    //Status. time swapsada
  REMOTE_EFMOAM  (8, "RemoteEFMOAM"), //status
  NOT_APPLICABLE (9, "n/a");

  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private LoopbackStatusTypeTranslation (final int mibValue, final String mtosiString)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int mibValue)
  {
    LoopbackStatusTypeTranslation enumType = NOT_APPLICABLE;  // the return value

    for (LoopbackStatusTypeTranslation tmpEnumType : values())
    {
      if (mibValue == tmpEnumType.getMIBValue())
      {
        enumType = tmpEnumType;
        break;
      }
    }
    return enumType.getMtosiString();
  }
  /**
   * Returns the string representation used in MTOSI layer.
   * @param mtosiString  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static int getMIBValue (final String mtosiString)
  {
    LoopbackStatusTypeTranslation enumType = NOT_APPLICABLE;  // the return value

    for (LoopbackStatusTypeTranslation tmpEnumType : values())
    {
      if (mtosiString.equals(tmpEnumType.getMtosiString()))
      {
        enumType = tmpEnumType;
        break;
      }
    }
    return enumType.getMIBValue();
  }

  /**
   * Return coma separested list of active loopbacks.
   * @param serviceSPPropertiesFSP150CC
   * @return String
   */
  public static String getLoopbackList (ServiceSPPropertiesFSP150CC serviceSPPropertiesFSP150CC)
  {
    int bitField = 0x1; //001
      final StringBuilder resultBuffer = new StringBuilder();
      for(int i = 1; i <= 3; i++)
      {
        if((serviceSPPropertiesFSP150CC.get(ServiceSPPropertiesFSP150CC.VI.VlanLoopbackMask) & bitField) != 0)
        {
          if(resultBuffer.length() > 0)
            resultBuffer.append(",");
          switch(i){
            case 1:
              resultBuffer.append(serviceSPPropertiesFSP150CC.get(ServiceSPPropertiesFSP150CC.VS.VlanLoopback1));
              break;
            case 2:
              resultBuffer.append(serviceSPPropertiesFSP150CC.get(ServiceSPPropertiesFSP150CC.VS.VlanLoopback2));
              break;
            case 3:
              resultBuffer.append(serviceSPPropertiesFSP150CC.get(ServiceSPPropertiesFSP150CC.VS.VlanLoopback3));
              break;
          }
        }
        bitField <<=1;
      }
    return resultBuffer.toString();
  }
}
