/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.inventory;

import com.adva.nlms.common.InstallationState;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.common.config.EntityClass;
import com.adva.nlms.common.config.EntityType;
import com.adva.nlms.common.snmp.NoSuchMDObjectException;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.PsuSPPropertiesFSP150CM;
import com.adva.nlms.mediation.config.Entity;
import com.adva.nlms.mediation.config.Module;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.f3.entity.SlotF3SPProperties;
import com.adva.nlms.mediation.config.f3.entity.module.MTOSICardModuleF3;
import com.adva.nlms.mediation.config.f3.entity.module.MTOSIEthernetModuleF3;
import com.adva.nlms.mediation.config.f3.entity.module.fan.MTOSIFanF3;
import com.adva.nlms.mediation.config.f3.entity.module.nte.MTOSINTEF3;
import com.adva.nlms.mediation.config.f3.entity.module.psu.PowerSupplyF3;
import com.adva.nlms.mediation.config.f3.entity.module.sfp.MTOSISfpModuleF3;
import com.adva.nlms.mediation.config.f3.entity.slot.SlotF3;
import com.adva.nlms.mediation.config.f3.mtosi.NetworkElementFSP150CMMTOSIOperations;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.entity.module.scut.ScuTFSP150CMImpl;
import com.adva.nlms.mediation.config.fsp20X.NetworkElementFSPGE20X;
import com.adva.nlms.mediation.config.hn4000.ModuleHN4000;
import com.adva.nlms.mediation.config.hn4000.NetworkElementHN4000;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.*;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.NetworkElementMediatorFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.EquipmentMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrationControlTranslation;
import com.adva.nlms.mediation.config.hn4000.necomm.NetworkElementDiscoveryHN4000;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.EHInventoryT;
import v1.tmf854.EQInventoryT;
import v1.tmf854.EquipmentHolderT;
import v1.tmf854.EquipmentOrHolderT;
import v1.tmf854.EquipmentT;
import v1.tmf854.HeaderT;
import v1.tmf854.InventoryDataT;
import v1.tmf854.InventoryDataT.MdList;
import v1.tmf854.InventoryDataT.OsList;
import v1.tmf854.MDInventoryT;
import v1.tmf854.MDInventoryT.MeList;
import v1.tmf854.MEInventoryT;
import v1.tmf854.MEInventoryT.EhList;
import v1.tmf854.MEInventoryT.PtpList;
import v1.tmf854.ManagementDomainT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.OSInventoryT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.PTPInventoryT;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.TerminationPointListT;
import v1.tmf854.TerminationPointT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class InventoryFactory {
  private static Logger logger = LogManager.getLogger(InventoryFactory.class.getPackage().getName());

  public static InventoryDataT getInventory(final InventoryRetrievalFilter filter, final Holder<HeaderT> mtosiHeader) throws Exception
	{
		InventoryDataT inventoryData = new InventoryDataT();
		MdList mdList = new MdList();
		OsList osList = new OsList();
		OSInventoryT osInventory = getOSInventory(filter);
		if (filter.isIncludeOs()) {
			if (MtosiUtils.isBaseInstanceMatch(filter.getSimpleFilter().getBaseInstance(), osInventory.getOsAttrs().getName().
              getValue())) {
				// only add the OS if object type included and in base instance list
				osList.getOs().add(osInventory);
				inventoryData.setOsList(osList);
			}
		}
		MDInventoryT mdInventory = getMDInventory(filter);
    NetworkElement element = ManagedElementFactory.getAndValidateNE(filter.getSimpleFilter().getBaseInstance().get(0));

    MEInventoryT meInventory = getMEInventoryRecursive(element, filter);
    getPTPInventory(element, filter, meInventory, mtosiHeader);
    mdInventory.getMeList().getMeInv().add(meInventory);

    mdList.getMd().add(mdInventory);
		inventoryData.setMdList(mdList);
		return inventoryData;
	}

	public static MDInventoryT getMDInventory(InventoryRetrievalFilter filter)
	{
		MDInventoryT mdInventory = new MDInventoryT();
		mdInventory.setMeList(new MeList());
		// check if this is the name, probably should be same method to figure
		// out as ManagedElementT
		if (filter.isIncludeMd()) {
			ManagementDomainT md = OSFactory.getMtosiMD();
			if (MtosiUtils.isBaseInstanceChildOrMatch(filter.getSimpleFilter().getBaseInstance(), md.getName()
					.getValue()))
			{
				if (filter.isIncludeMdName())
				{
					mdInventory.setMdNm(md.getName().getValue().getMdNm());
				}
				if (filter.isIncludeMdAttrs())
				{
					mdInventory.setMdAttrs(md);
				}
			}
		}
		return mdInventory;
	}

	public static List<EHInventoryT> getEHInventoryList(NetworkElement ne, InventoryRetrievalFilter filter)
	{
		List<EHInventoryT> inventoryList = new ArrayList<>();
		Set propertiesShelfSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.SHELF);
    for (Object aPropertiesShelfSet : propertiesShelfSet) {
      EquipmentSPProperties properties = (EquipmentSPProperties) aPropertiesShelfSet;
      if (MtosiUtils.isShelfIncludedInFilter(ne, properties, filter.getSimpleFilter().getBaseInstance())) {
        EHInventoryT nextInventory = getEHInventoryForShelf(ne, filter, properties);
        if (MtosiUtils.hasData(nextInventory)) {
          inventoryList.add(nextInventory);
        }
      }
    }
    return inventoryList;
	}

	public static EHInventoryT getEHInventoryForShelf(NetworkElement ne, InventoryRetrievalFilter filter,
			EquipmentSPProperties properties)
	{
		int type = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI();
		switch (type)
		{
			case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
	      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
	        return getEHInventoryForShelfCP(ne, filter, properties);
	      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
	      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
	      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
	      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
	        return getEHInventoryForShelfCM(ne, filter, properties);
	      case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
	      case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
	        return getEHInventoryForShelfHN(ne, filter, properties);
	      default:
	        return null;
		}
	}


  private static void setEhList (EHInventoryT.EhList ehList, EHInventoryT holder)
  {
    if (ehList.getEhInv().size() > 0)
		{
			holder.setEhList(ehList);
		}
  }

  private static void addEhInventory (EHInventoryT.EhList ehList, List<EHInventoryT> holders)
  {
    if (holders.size() > 0)
    {
      ehList.getEhInv().addAll(holders);
    }
  }

  private static void setNameAndAttributesEh (EHInventoryT ehInventory, EquipmentHolderT shelfHolder, InventoryRetrievalFilter filter, String shelfName)
  {
    if (MtosiUtils.isBaseInstanceChildOrMatch(filter.getSimpleFilter().getBaseInstance(), shelfHolder.getName()
        .getValue()))
    {
      if (filter.isIncludeEhName())
      {
        ehInventory.setEhNm(shelfName);
      }
      if (filter.isIncludeEhAttrs())
      {
        ehInventory.setEhAttrs(shelfHolder);
      }
    }
  }

  public static EHInventoryT getEHInventoryForShelfCP(NetworkElement ne, InventoryRetrievalFilter filter,
			EquipmentSPProperties properties)
	{
		EHInventoryT fixedSlotEhInventory = new EHInventoryT();
		EHInventoryT ehInventory = new EHInventoryT();
		populateInventoryIfIncluded(filter, ne, properties, ehInventory, fixedSlotEhInventory);
		List<EHInventoryT> sfpList = getSFPHolderInventoryListCP(ne, properties, filter);
		List<EHInventoryT> psuList = getPSUHolderInventoryList(ne, filter, properties.get(EquipmentSPProperties.VI.ChassisId));
		List<EHInventoryT> fanList = getFanHolderInventoryList(ne, filter, properties.get(EquipmentSPProperties.VI.ChassisId));
		v1.tmf854.EHInventoryT.EhList ehList = new v1.tmf854.EHInventoryT.EhList();
		v1.tmf854.EHInventoryT.EhList ehListForShelf = new v1.tmf854.EHInventoryT.EhList();
		addEhInventory(ehList, sfpList);
		addEhInventory(ehListForShelf, psuList);
		addEhInventory(ehListForShelf, fanList);
		setEhList(ehList, fixedSlotEhInventory);
		EQInventoryT systemInventory = getSystemEQInventory(ne, filter, properties);
		fixedSlotEhInventory.setEqInv(systemInventory);
		ehListForShelf.getEhInv().add(fixedSlotEhInventory);
		setEhList(ehListForShelf, ehInventory);
		return ehInventory;
	}

  public static EHInventoryT getEHInventoryForShelfHN(NetworkElement ne, InventoryRetrievalFilter filter,
			EquipmentSPProperties properties)
  {
    EHInventoryT ehInventory = new EHInventoryT();
    v1.tmf854.EHInventoryT.EhList ehListForShelf = new v1.tmf854.EHInventoryT.EhList();
    if (filter.isIncludeEh())
    {
      // the shelf
      String shelfName = NetworkElementMediatorFactory.createNetworkElementMediator(ne).getRelativeNameForProperties(properties);
      EquipmentOrHolderT holder = EquipmentMediator.nmsShelfPropertiesToMTOSIEquipmentHolder(ne, properties);
      EquipmentHolderT shelfHolder = holder.getEh();
      setNameAndAttributesEh(ehInventory, shelfHolder, filter, shelfName);
    }
    List<EHInventoryT> psuList = getPSUHolderInventoryList(ne, filter, properties.get(EquipmentSPProperties.VI.ChassisId));
		List<EHInventoryT> fanList = getFanHolderInventoryList(ne, filter, properties.get(EquipmentSPProperties.VI.ChassisId));
    ehListForShelf.getEhInv().addAll(getSlotHolderInventoryList((NetworkElementHN4000)ne, properties, filter));
    addEhInventory(ehListForShelf, psuList);
    addEhInventory(ehListForShelf, fanList);
    setEhList(ehListForShelf, ehInventory);
    return ehInventory;
  }

  private static void populateInventoryIfIncluded (InventoryRetrievalFilter filter, NetworkElement ne, EquipmentSPProperties properties, EHInventoryT ehInventory, EHInventoryT fixedSlotEhInventory)
  {
    if (filter.isIncludeEh())
    {
      // the shelf
      String shelfName = NetworkElementMediatorFactory.createNetworkElementMediator(ne).getRelativeNameForProperties(properties);
      EquipmentOrHolderT holder = EquipmentMediator.nmsShelfPropertiesToMTOSIEquipmentHolder(ne, properties);
      EquipmentHolderT shelfHolder = holder.getEh();
      setNameAndAttributesEh(ehInventory, shelfHolder, filter, shelfName);
      // the fixed slot
      String fixedSlotName = NamingTranslationFactory.getRelativeSlotNameForProperties(ne, properties);
      EquipmentOrHolderT holderSlot = EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipmentHolder(ne,
              properties);
      EquipmentHolderT equipmentHolder = holderSlot.getEh();
      setNameAndAttributesEh(fixedSlotEhInventory, equipmentHolder, filter, fixedSlotName);
    }
  }

  public static EHInventoryT getEHInventoryForShelfCM(NetworkElement ne, InventoryRetrievalFilter filter,
			EquipmentSPProperties properties)
  {
    EHInventoryT shelfEhInventory = new EHInventoryT();
    EHInventoryT ehInventory = new EHInventoryT();
    v1.tmf854.EHInventoryT.EhList ehListForShelf = new v1.tmf854.EHInventoryT.EhList();
    if (filter.isIncludeEh())
    {
      // the shelf
      String shelfName = NetworkElementMediatorFactory.createNetworkElementMediator(ne).getRelativeNameForProperties(properties);
      EquipmentOrHolderT holder = EquipmentMediator.nmsShelfPropertiesToMTOSIEquipmentHolder(ne, properties);
      EquipmentHolderT shelfHolder = holder.getEh();
      setNameAndAttributesEh(ehInventory, shelfHolder, filter, shelfName);
    }
    ehListForShelf.getEhInv().addAll(getSlotHolderInventoryList((NetworkElementFSP150CM)ne, properties, filter));
    ehListForShelf.getEhInv().add(shelfEhInventory);
    setEhList(ehListForShelf, ehInventory);
    return ehInventory;
  }


	public static List<EHInventoryT> getSFPHolderInventoryListCP(NetworkElement ne, EquipmentSPProperties shelfProperties,
			InventoryRetrievalFilter filter)
	{
		
		ArrayList<EHInventoryT> list = new ArrayList<>();
		Set<EquipmentSPProperties> propertiesModuleSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.MODULE);
		int chassisId = shelfProperties.get(EquipmentSPProperties.VI.ChassisId); // CHASSISID
		Set propertiesModuleSetChassis = EquipmentFactory.getEquipmentSPPropertiesForChassis(propertiesModuleSet, chassisId);
    for (Object propertiesModuleSetChassi : propertiesModuleSetChassis) {
      EquipmentSPProperties properties = (EquipmentSPProperties) propertiesModuleSetChassi;
      if (properties.get(EquipmentSPProperties.VI.ChassisId) == chassisId) {
        // same chassis, so include the SFP
        if (properties.isSFP()) {
          EHInventoryT nextInventory = getSFPEHInventory(ne, properties, filter);
          if (MtosiUtils.hasData(nextInventory)) {
            list.add(nextInventory);
          }
        }
      }
    }
    return list;
	}

  public static List<EHInventoryT> getSlotHolderInventoryList(NetworkElementFSP150CM ne, EquipmentSPProperties shelfProperties,
			InventoryRetrievalFilter filter)
	{
    List<EHInventoryT> list = new ArrayList<>();

    Set<SlotF3SPProperties> slotProperties = new HashSet<>();
    Set<Entity> slots = ne.getMTOSIWorker().getEntitiesByType(MIB.Entity.CONTAINER);
    for (Entity slot : slots) {
      slotProperties.add(((SlotF3)slot).getSlotSPProperties());
    }
    int chassisId = shelfProperties.get(EquipmentSPProperties.VI.ChassisId); // CHASSISID
    for (SlotF3SPProperties properties : slotProperties)
    {
      if (properties.get(SlotF3SPProperties.VI.ShelfIndex) == chassisId)
      {
        EHInventoryT nextInventory = getSlotEHInventory(ne, properties, filter);
        if (MtosiUtils.hasData(nextInventory)) {
          list.add(nextInventory);
        }
      }
    }
    return list;
  }

  public static List<EHInventoryT> getSlotHolderInventoryList(NetworkElementHN4000 ne, EquipmentSPProperties shelfProperties,
			InventoryRetrievalFilter filter) {
    List<EHInventoryT> list = new ArrayList<>();

    int chassisId = shelfProperties.get(EquipmentSPProperties.VI.ChassisId);
    addSlotInventory(chassisId, 1, ne, filter, list);
    //Need to do the extra slot=2 only for the HN4000
	if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000) {
		addSlotInventory(chassisId, 2, ne, filter, list);
	}
    return list;
  }

  private static void addSlotInventory (int chassisId, int slotId, NetworkElementHN4000 ne, InventoryRetrievalFilter filter, List<EHInventoryT> list) {
    Module slot = ne.getMTOSIWorker().getModule(chassisId, slotId);
    EHInventoryT nextInventory;
    if (slot != null) {
      nextInventory = getSlotEHInventory(ne, slot.getEquipmentSPProperties(), filter, true);
    }
    else {
      EquipmentSPProperties props = new EquipmentSPProperties();
      props.set(EquipmentSPProperties.VI.ChassisId, chassisId);
      props.set(EquipmentSPProperties.VI.RelativePosition, slotId);
      props.set(EquipmentSPProperties.VE.Index, EntityIndex.ZERO); //Set the index to 0, since there is no module there is no Index but, getSlotEHInventory will blow up without an index to look up the SFPs.
      props.set(EquipmentSPProperties.VI.ClassType, EntityClass.MODULE);
      props.set(EquipmentSPProperties.VB.SFPState, Boolean.FALSE);
      props.set(EquipmentSPProperties.VI.InstallState, InstallationState.REMOVED);
      props.set(EquipmentSPProperties.VI.AdminState, AdministrationControlTranslation.ENABLED.getMIBValue());
      props.set(EquipmentSPProperties.VI.OperState, 6/*unequipped*/);
      props.set(EquipmentSPProperties.VS.PartNumber, "");
      props.set(EquipmentSPProperties.VS.SerialNumber, "");
      props.set(EquipmentSPProperties.VS.HardwareVersion, "");
      nextInventory = getSlotEHInventory(ne, props, filter, true);
    }
    if (MtosiUtils.hasData(nextInventory)) {
      list.add(nextInventory);
    }
  }

  public static List<EHInventoryT> getPSUHolderInventoryList(NetworkElement ne, InventoryRetrievalFilter filter, final int shelfNo)
	{
		ArrayList<EHInventoryT> list = new ArrayList<>();
		Set<EquipmentSPProperties> propertiesPSUSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.POWER_SUPPLY);
    for (EquipmentSPProperties properties : propertiesPSUSet) {
      if (properties.get(EquipmentSPProperties.VI.ChassisId) == shelfNo) {
        EHInventoryT nextInventory = getPSUEHInventory(ne, properties, filter);
        if (MtosiUtils.hasData(nextInventory)) {
          list.add(nextInventory);
        }
      }
    }
    return list;
	}

	public static List<EHInventoryT> getFanHolderInventoryList(NetworkElement ne, InventoryRetrievalFilter filter, final int shelfNo)
	{
		// get all the psu modules, and create EHInventoryT for each
		ArrayList<EHInventoryT> list = new ArrayList<>();
		Set<EquipmentSPProperties> propertiesSet = ne.getMTOSIWorker().getEquipmentSPPropertiesByType(EntityType.FAN);
    for (EquipmentSPProperties properties : propertiesSet) {
      if (properties.get(EquipmentSPProperties.VI.ChassisId) == shelfNo) {
        EHInventoryT nextInventory = getFanEHInventory(ne, properties, filter);
        if (MtosiUtils.hasData(nextInventory)) {
          list.add(nextInventory);
        }
      }
    }
    return list;
	}

	public static EHInventoryT getFanEHInventory(NetworkElement ne, EquipmentSPProperties properties,
			InventoryRetrievalFilter filter)
	{
		EHInventoryT nextInventory = new EHInventoryT();
		if (filter.isIncludeEh())
		{
			String relativeName = NetworkElementMediatorFactory.createNetworkElementMediator(ne).getRelativeNameForProperties(properties);
      Long manufactureDate = properties.get(EquipmentSPProperties.VL.ManufactureDate);
      try {
        properties.set(EquipmentSPProperties.VL.ManufactureDate, ne.getMTOSIWorker().getEquipmentSPProperties(properties.get(EquipmentSPProperties.VE.ContainedIn)).get(EquipmentSPProperties.VL.ManufactureDate));
      }
      catch (ClassCastException e) {
        // this case is possible, because some entities are not equipment
        logger.debug(e, e);
      }
      catch (NoSuchMDObjectException e) {
        logger.warn(e, e);
      }
      EquipmentHolderT nextHolder = NetworkElementMediatorFactory.createNetworkElementMediator(ne).fanToMtosiEquipmentHolder(properties).getEh();
      setNameAndAttributesEh(nextInventory, nextHolder, filter, relativeName);
      properties.set(EquipmentSPProperties.VL.ManufactureDate, manufactureDate);
    }
		if (filter.isIncludeEq())
		{
			EQInventoryT equipmentInventory = getFanEQInventory(ne, properties, filter);
			if (equipmentInventory != null)
			{
				// only add the eqInventory if its in a requested tree
				// if
				// (MtosiUtils.isBaseInstanceChildOrMatch(filter.getSimpleFilter().getBaseInstance(),
				// equipmentInventory
				// .getEqAttrs().getName().getValue()))
				// {
				nextInventory.setEqInv(equipmentInventory);
				// }
			}
		}
		return nextInventory;
	}

	public static EHInventoryT getSFPEHInventory(NetworkElement ne, EquipmentSPProperties properties,
			InventoryRetrievalFilter filter)
	{
		EHInventoryT nextInventory = new EHInventoryT();
		if (filter.isIncludeEh())
		{
			String relativeName = NetworkElementMediatorFactory.createNetworkElementMediator(ne).getRelativeNameForProperties(properties);
      Long manufactureDate = properties.get(EquipmentSPProperties.VL.ManufactureDate);
      try {
        properties.set(EquipmentSPProperties.VL.ManufactureDate, ne.getMTOSIWorker().getEquipmentSPProperties(properties.get(EquipmentSPProperties.VE.ContainedIn)).get(EquipmentSPProperties.VL.ManufactureDate));
      }
      catch (ClassCastException e) {
        // this case is possible, because some entities are not equipment
        logger.debug(e, e);
      }
      catch (NoSuchMDObjectException e) {
        logger.warn(e, e);
      }
      EquipmentHolderT nextHolder = NetworkElementMediatorFactory.createNetworkElementMediator(ne).sfpToMtosiEquipmentHolder(properties).getEh();
      setNameAndAttributesEh(nextInventory, nextHolder, filter, relativeName);
      properties.set(EquipmentSPProperties.VL.ManufactureDate, manufactureDate);
    }
		// only both with equipment if its included in filter
		if (filter.isIncludeEq())
		{
			EQInventoryT equipmentInventory = getSFPEQInventory(ne, properties, filter);
			if (equipmentInventory != null)
			{
				// only add the eqInventory if its in a requested tree
				// if
				// (MtosiUtils.isBaseInstanceChildOrMatch(filter.getSimpleFilter().getBaseInstance(),
				// equipmentInventory
				// .getEqAttrs().getName().getValue()))
				// {
				nextInventory.setEqInv(equipmentInventory);
				// }
			}
		}
		return nextInventory;
	}

  public static EHInventoryT getSlotEHInventory(NetworkElementFSP150CM ne, SlotF3SPProperties properties,
                                                InventoryRetrievalFilter filter)
  {
    EHInventoryT nextInventory = new EHInventoryT();
    EquipmentSPPropertiesFSP150CM eqProperties = new EquipmentSPPropertiesFSP150CM();
    eqProperties.set(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex, properties.get(SlotF3SPProperties.VI.ShelfIndex));
    eqProperties.set(EquipmentSPPropertiesFSP150CM.VI.SlotIndex, properties.get(SlotF3SPProperties.VI.SlotIndex));
    eqProperties.set(EquipmentSPProperties.VL.ManufactureDate, ne.getMTOSIWorker().getShelf(properties.get(SlotF3SPProperties.VI.ShelfIndex)).getEquipmentSPProperties().get(EquipmentSPProperties.VL.ManufactureDate));
    String equipmentType = null;
    String mismatchType = "";
    EquipmentSPPropertiesFSP150CM props = null;
    NetworkElementFSP150CMMTOSIOperations mtosiWorker = getFSP150CMMTOSIWorker(ne);
    switch (MtosiUtils.getCardTypeFromSlotIndex(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI(), properties.get(SlotF3SPProperties.VI.SlotIndex))) {
      case MIBFSP150CM.Entity.SlotTable.TYPE_GENERIC_INDEX: {
        MTOSICardModuleF3 card = mtosiWorker.getModule(properties.get(SlotF3SPProperties.VI.ShelfIndex), properties.get(SlotF3SPProperties.VI.SlotIndex));
        if (card != null) {
          props = (EquipmentSPPropertiesFSP150CM) card.getEquipmentSPProperties();
          if (card instanceof MTOSIEthernetModuleF3) {
            Set<MTOSISfpModuleF3> sfps = ((MTOSIEthernetModuleF3)card).getAllSFP();
            ArrayList<EHInventoryT> list = new ArrayList<>();
            for (MTOSISfpModuleF3 sfp : sfps) {
              EquipmentSPProperties sfpProps = sfp.getEquipmentSPProperties();
              EHInventoryT sfpInventory = getSFPEHInventory(ne, sfpProps, filter);
              if (MtosiUtils.hasData(sfpInventory)) {
                list.add(sfpInventory);
              }
            }
            if (list.size() > 0)
            {
              EHInventoryT.EhList ehList = new EHInventoryT.EhList();
              ehList.getEhInv().addAll(list);
              nextInventory.setEhList(ehList);
            }
            if (card instanceof MTOSINTEF3 && ne instanceof NetworkElementFSPGE20X)
              equipmentType = MtosiConstants.EQUIPMENT_NTE_GE;
            else if (card instanceof MTOSINTEF3) {
              equipmentType = MtosiConstants.EQUIPMENT_NTE_GE_SYNC;
              mismatchType = MtosiConstants.EQUIPMENT_NTU_GE;
            } else {
              equipmentType = MtosiConstants.EQUIPMENT_NTU_GE;
              mismatchType = MtosiConstants.EQUIPMENT_NTE_GE_SYNC;
            }
          }
          else {
            equipmentType = MtosiConstants.EQUIPMENT_NEMI;
          }
        }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_SCU_INDEX: {
        MTOSICardModuleF3 card = mtosiWorker.getModule(properties.get(SlotF3SPProperties.VI.ShelfIndex), properties.get(SlotF3SPProperties.VI.SlotIndex));
        if (card != null) {
          props = (EquipmentSPPropertiesFSP150CM) card.getEquipmentSPProperties();
          if (card instanceof ScuTFSP150CMImpl) {
            equipmentType = MtosiConstants.EQUIPMENT_SCU_T;
            mismatchType = MtosiConstants.EQUIPMENT_SCU;
          } else {
            equipmentType = MtosiConstants.EQUIPMENT_SCU;
            mismatchType = MtosiConstants.EQUIPMENT_SCU_T;
          }
        }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_PSU_INDEX: {
        PowerSupplyF3 psu = mtosiWorker.getPSU(properties.get(SlotF3SPProperties.VI.ShelfIndex), properties.get(SlotF3SPProperties.VI.SlotIndex));
        if (psu != null) {
          props = (EquipmentSPPropertiesFSP150CM) psu.getEquipmentSPProperties();
          int psuType = ((PsuSPPropertiesFSP150CM)psu.getEquipmentSPProperties()).get(PsuSPPropertiesFSP150CM.VI.PsuType);
          if ( psuType == MIBFSP150CM.Entity.PsuTable.TYPE_AC_INDEX) {
            equipmentType = MtosiConstants.EQUIPMENT_PSU_AC;
          } else if ( psuType == MIBFSP150CM.Entity.PsuTable.TYPE_DC_INDEX) {
        	  equipmentType = MtosiUtils.getGE20XPsuType(props.get(EquipmentSPProperties.VS.ModelName));
          } else {
        	  equipmentType = MtosiConstants.EQUIPMENT_PSU;
          }
        }
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_FAN_INDEX: {
        MTOSIFanF3 fan = mtosiWorker.getFAN(properties.get(SlotF3SPProperties.VI.ShelfIndex), properties.get(SlotF3SPProperties.VI.SlotIndex));
        if (fan != null) {
          props = (EquipmentSPPropertiesFSP150CM) fan.getEquipmentSPProperties();
          equipmentType = MtosiConstants.EQUIPMENT_FAN;
        }
        break;
      }
    }
    if (filter.isIncludeEh())
    {
      String relativeName = NamingTranslationFactory.getRelativeNameForProperties(ne, properties);
      EquipmentHolderT nextHolder;
      if (props != null) {
        Long manufactureDate = props.get(EquipmentSPProperties.VL.ManufactureDate);
        props.set(EquipmentSPProperties.VL.ManufactureDate, ne.getMTOSIWorker().getShelf(props.get(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex)).getEquipmentSPProperties().get(EquipmentSPProperties.VL.ManufactureDate));
        nextHolder = EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipmentHolder(ne, props, true)
                .getEh();
        props.set(EquipmentSPProperties.VL.ManufactureDate, manufactureDate);
      }
      else {
        nextHolder = EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipmentHolder(ne, eqProperties, false)
                .getEh();
      }
      setNameAndAttributesEh(nextInventory, nextHolder, filter, relativeName);
    }
    if (filter.isIncludeEq() && props != null) {
      EquipmentT equipment = EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipment(ne, props, equipmentType, mismatchType).getEq();
      if (MtosiUtils.isBaseInstanceChildOrMatch(filter.getSimpleFilter().getBaseInstance(), equipment.getName()
              .getValue()))
      {
        EQInventoryT inventory = new EQInventoryT();
        if (filter.isIncludeEqName())
        {
          inventory.setEqNm(NamingTranslationFactory.getEquipmentRelativeNameForProperties(ne, props));
        }
        if (filter.isIncludeEqAttrs())
        {
          inventory.setEqAttrs(equipment);
        }
        nextInventory.setEqInv(inventory);
      }
    }
    return nextInventory;
  }

  public static EHInventoryT getSlotEHInventory(NetworkElementHN4000 ne, EquipmentSPProperties properties,
                                                InventoryRetrievalFilter filter, boolean installed) {
    EHInventoryT nextInventory = new EHInventoryT();
    List<Module> sfps = ne.getMTOSIWorker().getSFPs(properties.get(EquipmentSPProperties.VI.ChassisId), properties.get(EquipmentSPProperties.VI.RelativePosition), properties.get(EquipmentSPProperties.VE.Index).toInt());
    ArrayList<EHInventoryT> list = new ArrayList<>();
    for (Module sfp : sfps) {
      EquipmentSPProperties sfpProps = sfp.getEquipmentSPProperties();
      EHInventoryT sfpInventory = getSFPEHInventory(ne, sfpProps, filter);
      if (MtosiUtils.hasData(sfpInventory)) {
        list.add(sfpInventory);
      }
    }
    if (list.size() > 0)
    {
      EHInventoryT.EhList ehList = new EHInventoryT.EhList();
      ehList.getEhInv().addAll(list);
      nextInventory.setEhList(ehList);
    }
    if (filter.isIncludeEh()) {
      String relativeName = NetworkElementMediatorFactory.createNetworkElementMediator(ne).getRelativeNameForProperties(properties);
      EquipmentHolderT nextHolder;
      nextHolder = EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipmentHolder(ne, properties).getEh();
      setNameAndAttributesEh(nextInventory, nextHolder, filter, relativeName);
    }
    if (installed && filter.isIncludeEq()) {
      EquipmentT equipment = NetworkElementMediatorFactory.createNetworkElementMediator(ne).shelfToMtosiEquipment(properties).getEq();
      if (MtosiUtils.isBaseInstanceChildOrMatch(filter.getSimpleFilter().getBaseInstance(), equipment.getName().getValue())) {
        EQInventoryT inventory = new EQInventoryT();
        if (filter.isIncludeEqName()) {
          inventory.setEqNm(NamingTranslationFactory.getEquipmentRelativeNameForProperties(ne, properties));
        }
        if (filter.isIncludeEqAttrs()) {
          if (properties.get(EquipmentSPProperties.VI.RelativePosition) == 2) {
            ObjectFactory objFactory = new ObjectFactory();
            String expectedEquipment;
            String installedEquipment = "";
            Module slot = ne.getMTOSIWorker().getModule(properties.get(EquipmentSPProperties.VI.ChassisId), 2);
            if (slot != null) {
              ModuleHN4000 hnModule = (ModuleHN4000) slot;
              expectedEquipment = NetworkElementDiscoveryHN4000.ModuleTypeEnum.getModuleName(hnModule.getAssignedType());
              installedEquipment = NetworkElementDiscoveryHN4000.ModuleTypeEnum.getModuleName(hnModule.getEquippedType());
            }
            else {
              expectedEquipment = NetworkElementDiscoveryHN4000.ModuleTypeEnum.getModuleName(0/*NOT_APPLICABLE*/);
            }
            equipment.setExpectedEquipmentObjectType(objFactory.
                    createEquipmentTExpectedEquipmentObjectType(expectedEquipment));
            equipment.setInstalledEquipmentObjectType(objFactory
                    .createEquipmentTInstalledEquipmentObjectType(installedEquipment));
          }
          inventory.setEqAttrs(equipment);
        }
        nextInventory.setEqInv(inventory);
      }
    }
    return nextInventory;
  }

  public static EHInventoryT getPSUEHInventory(NetworkElement ne, EquipmentSPProperties properties,
			InventoryRetrievalFilter filter)
	{
		EHInventoryT nextInventory = new EHInventoryT();
		if (filter.isIncludeEh())
		{
			String relativeName = NetworkElementMediatorFactory.createNetworkElementMediator(ne).getRelativeNameForProperties(properties);
      Long manufactureDate = properties.get(EquipmentSPProperties.VL.ManufactureDate);
      try {
        properties.set(EquipmentSPProperties.VL.ManufactureDate, ne.getMTOSIWorker().getEquipmentSPProperties(properties.get(EquipmentSPProperties.VE.ContainedIn)).get(EquipmentSPProperties.VL.ManufactureDate));
      }
      catch (ClassCastException e) {
        // this case is possible, because some entities are not equipment
        logger.debug(e, e);
      }
      catch (NoSuchMDObjectException e) {
        logger.warn(e, e);
      }
      EquipmentHolderT nextHolder = NetworkElementMediatorFactory.createNetworkElementMediator(ne).psuToMtosiEquipmentHolder(properties).getEh();
      setNameAndAttributesEh(nextInventory, nextHolder, filter, relativeName);
      properties.set(EquipmentSPProperties.VL.ManufactureDate, manufactureDate);
    }
		if (filter.isIncludeEq())
		{
			EQInventoryT equipmentInventory = getPSUEQInventory(ne, properties, filter);
			// only add the eqInventory if its in a requested tree
			if (equipmentInventory != null)
			{
				// if
				// (MtosiUtils.isBaseInstanceChildOrMatch(filter.getSimpleFilter().getBaseInstance(),
				// equipmentInventory
				// .getEqAttrs().getName().getValue()))
				// {
				nextInventory.setEqInv(equipmentInventory);
				// }
			}
		}
		return nextInventory;
	}

	public static EQInventoryT getSFPEQInventory(NetworkElement ne, EquipmentSPProperties properties,
			InventoryRetrievalFilter filter)
	{
		EQInventoryT inventory = null;
		if (!MtosiUtils.isInstalled(properties))
			return null; // if it isn't installed, return null
		if (filter.isIncludeEq())
		{
			EquipmentT equipment = NetworkElementMediatorFactory.createNetworkElementMediator(ne).sfpToMtosiEquipment(properties).getEq();
      inventory = setNameAndAttributesEq(filter, equipment, inventory, ne, properties);
    }
		return inventory;
	}

  private static EQInventoryT setNameAndAttributesEq (InventoryRetrievalFilter filter, EquipmentT equipment, EQInventoryT inventory, NetworkElement ne, EquipmentSPProperties properties)
  {
    if (MtosiUtils.isBaseInstanceChildOrMatch(filter.getSimpleFilter().getBaseInstance(), equipment.getName()
        .getValue()))
    {
      inventory = new EQInventoryT();
      if (filter.isIncludeEqName())
      {
        inventory.setEqNm(NamingTranslationFactory.getEquipmentRelativeNameForProperties(ne, properties));
      }
      if (filter.isIncludeEqAttrs())
      {
        inventory.setEqAttrs(equipment);
      }
    }
    return inventory;
  }

  public static EQInventoryT getPSUEQInventory(NetworkElement ne, EquipmentSPProperties properties,
			InventoryRetrievalFilter filter)
	{
		EQInventoryT inventory = null;
		if (filter.isIncludeEq())
		{
			EquipmentT equipment = NetworkElementMediatorFactory.createNetworkElementMediator(ne).psuToMtosiEquipment(properties).getEq();
      inventory = setNameAndAttributesEq(filter, equipment, inventory, ne, properties);
    }
		return inventory;
	}

	public static EQInventoryT getFanEQInventory(NetworkElement ne, EquipmentSPProperties properties,
			InventoryRetrievalFilter filter)
	{
		EQInventoryT inventory = null;
		if (filter.isIncludeEq())
		{
			EquipmentT equipment = NetworkElementMediatorFactory.createNetworkElementMediator(ne).fanToMtosiEquipment(properties).getEq();
			if (MtosiUtils.isBaseInstanceChildOrMatch(filter.getSimpleFilter().getBaseInstance(), equipment.getName()
					.getValue()))
			{
				inventory = new EQInventoryT();
				if (filter.isIncludeEqName())
				{
					inventory.setEqNm(NamingTranslationFactory.getEquipmentRelativeNameForProperties(ne, properties));
				}
				if (filter.isIncludeEqAttrs())
				{
					inventory.setEqAttrs(equipment);
				}
			}
			else
			{
				// don't include it if its not a match
				return null;
			}
		}
		return inventory;
	}

	public static EQInventoryT getSystemEQInventory(NetworkElement ne, InventoryRetrievalFilter filter,
			EquipmentSPProperties properties)
	{
		EQInventoryT inventory = new EQInventoryT();
		// String relativeName = MtosiConstants.DEFAULT_SHELF_NAME;
		String relativeName = NamingTranslationFactory.getEquipmentRelativeNameForProperties(ne, properties);
		EquipmentT equipment = EquipmentFactory.getSystemEquipmentForNE(ne);
		if (filter.isIncludeEq())
		{
			if (MtosiUtils.isBaseInstanceChildOrMatch(filter.getSimpleFilter().getBaseInstance(), equipment.getName()
					.getValue()))
			{
				if (filter.isIncludeEqName())
				{
					inventory.setEqNm(relativeName);
				}
				if (filter.isIncludeEqAttrs())
				{
					inventory.setEqAttrs(equipment);
				}
			}
		}
		return inventory;
	}

  public static OSInventoryT getOSInventory(InventoryRetrievalFilter filter) {
    OSInventoryT osInventory = new OSInventoryT();
    if (filter.isIncludeOs()) {
      if (filter.isIncludeOsName()) {
        osInventory.setOsNm(OSFactory.getNmsName());
      }
      if (filter.isIncludeOsAttrs()) {
        osInventory.setOsAttrs(OSFactory.getMtosiOS());
      }
    }
    return osInventory;
  }

	public static MEInventoryT getMEInventoryRecursive(NetworkElement ne, InventoryRetrievalFilter filter) throws Exception
	  {
	    MEInventoryT meInventory = new MEInventoryT();
	    // check if this is the name, probably should be same method to figure
	    // out as ManagedElementT
	    if (filter.isIncludeMe())
	    {
	     // NamingAttributesT naming = ((ManagedElement)ne).getMtosiMediator().createNamingAttributesMe();
       // ne.getNetworkElementType();
	      NamingAttributesT naming = NetworkElementMediatorFactory.createNetworkElementMediator(ne).createNamingAttributesMe();
	      if (MtosiUtils.isBaseInstanceChildOrMatch(filter.getSimpleFilter().getBaseInstance(), naming))
	      {
	        if (filter.isIncludeMeName())
	        {
	          String neName = naming.getMeNm();
	          meInventory.setMeNm(neName);
	        }
	        if (filter.isIncludeMeAttrs())
	        {
	          meInventory.setMeAttrs(NetworkElementMediatorFactory.createNetworkElementMediator(ne).toManagedElement());
	        }
	      }
	    }
	    // shortcut in case only OS/MD/ME requested
	    if (!filter.isIncludeEh() && !filter.isIncludeEq())
	      return meInventory;
	    // handle the EH tree
	    EhList ehList = new EhList();
	    List<EHInventoryT> inventoryList = getEHInventoryList(ne, filter);
      for (Object anInventoryList : inventoryList) {
        EHInventoryT nextInventory = (EHInventoryT) anInventoryList;
        if (MtosiUtils.hasData(nextInventory)) {
          ehList.getEhInv().add(nextInventory);
        }
      }
      meInventory.setEhList(ehList);
	    return meInventory;
	  }

  private static void getPTPInventory(final NetworkElement ne, final InventoryRetrievalFilter filter,
                                      final MEInventoryT meInventory, final Holder<HeaderT> mtosiHeader) throws ProcessingFailureException {
    if (filter.isIncludePtp()) {
      PtpList ptpList = new PtpList();
      for (NamingAttributesT namingAttributes : filter.getSimpleFilter().getBaseInstance()) {
        final String ptpName = namingAttributes.getPtpNm();
        if (ptpName != null) { // get specific PTP.
          Port port = ManagedElementFactory.getPort(ne, ptpName);
          if (port == null) {
            ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(mtosiHeader, ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, "The Base Instance refers to an entity that does not exist.");
            throw new ProcessingFailureException(pfet.getReason(), pfet);
          }

//          if (!(port instanceof MtosiSupported)) {
//            throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
//                    "The Base Instance refers to an entity that is not supported.");
//          }
//          PhysicalTerminationPointT ptp = ((MtosiSupported)port).getMtosiTranslator().toMtosiPTP();
          PhysicalTerminationPointT ptp = new MtosiTranslatorFacade(ne,mtosiHeader,"The Base Instance refers to an entity that is not supported.").getMtosiTranslator(port).toMtosiPTP();

          PTPInventoryT ptpInventoryT = getPTPInventory(ptp, filter);
          ptpList.getPtpInv().add(ptpInventoryT);
        } else { // getAll PTP
          // Could have MD name only, but no NE
          TerminationPointListT terminationPointListT = ManagedElementFactory.getAllPTPs(namingAttributes, ne,
                  null, null);
          for (TerminationPointT terminationPointT : terminationPointListT.getTp())
          {
            PTPInventoryT ptpInventoryT = getPTPInventory(terminationPointT.getPtp(), filter);
            ptpList.getPtpInv().add(ptpInventoryT);
          }
        }
      }
      meInventory.setPtpList(ptpList);
    }
  }

	private static PTPInventoryT getPTPInventory(final PhysicalTerminationPointT physicalTerminationPoint,
			final InventoryRetrievalFilter filter)
	{
		PTPInventoryT ptpInventory = new PTPInventoryT();
		if (filter.isIncludePtpName())
		{
			// PhysicalTerminationPointT physTermPoint = new
			// PhysicalTerminationPointT();
			// physTermPoint.setName(physicalTerminationPoint.getName());
			ptpInventory.setPtpNm(physicalTerminationPoint.getName().getValue().getPtpNm());
		}
		if (filter.isIncludePtpAttrs())
		{
			ptpInventory.setPtpAttrs(physicalTerminationPoint);
		}
		return ptpInventory;
	}

  private static NetworkElementFSP150CMMTOSIOperations getFSP150CMMTOSIWorker(NetworkElementFSP150CM ne) {
    return (NetworkElementFSP150CMMTOSIOperations)ne.getMTOSIWorker();
  }
}
