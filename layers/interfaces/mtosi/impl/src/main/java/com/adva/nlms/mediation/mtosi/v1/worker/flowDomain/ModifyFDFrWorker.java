/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.mtosi.FDFr;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.TPDataListT;
import v1.tmf854ext.adva.FDFrModifyDataT;
import v1.tmf854ext.adva.FlowDomainFragmentT;
import v1.tmf854ext.adva.ModifyFDFrResponseT;
import v1.tmf854ext.adva.ModifyFDFrT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.ws.Holder;

abstract public class ModifyFDFrWorker extends AbstractMtosiWorker
{
  Logger LOG = LogManager.getLogger(this.getClass().getName());

  protected ModifyFDFrT mtosiBody;
  protected ModifyFDFrResponseT response = new ModifyFDFrResponseT();
  protected NetworkElement ne;
  protected NamingAttributesT namingAttributes;
  protected FDFrModifyDataT modifyData;
  protected TPDataListT tpsToModify;
  protected FDFr fdfr;

  public ModifyFDFrWorker(ModifyFDFrT mtosiBody, Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, FDFr fdfr, NetworkElement ne) {
    super(mtosiHeader, "modifyFDFr", "modifyFDFr", "modifyFDFrResponse");
    this.mtosiBody = mtosiBody;
    this.namingAttributes = namingAttributes;
    this.fdfr = fdfr;
    this.ne = ne;
  }

  @Override
  protected void parse() throws Exception {
    JAXBElement<FDFrModifyDataT> modifyJ = mtosiBody.getFdfrModifyData();
    if (modifyJ != null) {
      this.modifyData = modifyJ.getValue();
    }

    JAXBElement<TPDataListT> jaxTPList = mtosiBody.getTpsToModify();
    if (jaxTPList != null) {
      this.tpsToModify = jaxTPList.getValue();
    }

    if (modifyJ == null && jaxTPList == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "No data has been provided to modify.");
    }
  }


  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
   validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  @Override
  protected void response() throws Exception {
    final FlowDomainFragmentT fdfr = ManagedElementFactory.getFDFr(namingAttributes);
    response.setNewFDFr(fdfr);
    v1.tmf854ext.adva.ObjectFactory factory = new v1.tmf854ext.adva.ObjectFactory();
    // have to get updated Ports that were in the request tpsToModify
    JAXBElement<TPDataListT> modifiedTPs = factory.createModifyFDFrResponseTTpsToModify(getUpdatedTPs());
    response.setTpsToModify(modifiedTPs);
  }

  protected abstract TPDataListT getUpdatedTPs() throws ProcessingFailureException;

  @Override
  public ModifyFDFrResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}