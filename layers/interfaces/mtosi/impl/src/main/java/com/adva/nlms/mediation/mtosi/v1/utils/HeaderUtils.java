/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils;


import java.text.SimpleDateFormat;
import java.util.Date;
import jakarta.xml.ws.Holder;
import v1.tmf854.CommunicationPatternT;
import v1.tmf854.CommunicationStyleT;
import v1.tmf854.HeaderT;
import v1.tmf854.MsgTypeT;
import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.ProcessingFailureException;


public class HeaderUtils
{
	final public static String TIMESTAMP_FORMAT = "yyyyMMddhhmmss";
	final public static String DATE_FORMAT = "yyyyMMdd";

	public static String getCurrentTimestamp()
	{
		return new SimpleDateFormat(TIMESTAMP_FORMAT).format(new Date());
	}
	
	public static String formatDate(Long date)
	{
		if(date==null || date == 0) return "";
		return new SimpleDateFormat(DATE_FORMAT).format(new Date(date));
	}
	
	/*
	 * Confirm common fields in the request header.
	 * 
	 * The MTOSI standard seems to indicate that header errors should not be communicated back
	 * via the detail element in the SOAP fault, however it does not suggest the mechanism by
	 * which these should be communicated. So in absence of that information, we will go ahead
	 * and use the detail element.
	 */
	public static void validateRequestHeader(Holder<HeaderT> mtosiHeader, String activityName, String msgName) throws Exception
	{
		boolean result = false;
		
		result = ((mtosiHeader.value.getActivityName() != null) && (activityName.compareTo(mtosiHeader.value.getActivityName()) == 0));
		if (!result)
		{
			/* The header activityName does not match the expected activityName. */
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT, "The header activityName does not match the expected activityName.");
			ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
			throw pfe;
		}
		
		result = ((mtosiHeader.value.getMsgName() != null) && (msgName.compareTo(mtosiHeader.value.getMsgName()) == 0));
		if (!result)
		{
			/* The header msgName does not match the expected msgName. */
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT, "The header msgName does not match the expected msgName.");
			ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
			throw pfe;
		}

		result = ((mtosiHeader.value.getCommunicationStyle() != null) && ((CommunicationStyleT.MSG.compareTo(mtosiHeader.value.getCommunicationStyle()) == 0) || (CommunicationStyleT.RPC.compareTo(mtosiHeader.value.getCommunicationStyle()) == 0)));
		if (!result)
		{
			/* The header communicationStyle does not match the expected communicationStyle. */
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
				"The header communicationStyle does not match the expected communicationStyle (MSG) or (RPC).");
			ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
			throw pfe;
		}
	
		result = ((mtosiHeader.value.getMsgType() != null) && (MsgTypeT.REQUEST.compareTo(mtosiHeader.value.getMsgType()) == 0));
		if (!result)
		{
			/* The header msgType is not a request. */
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT, "The header msgType is not a request.");
			ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
			throw pfe;
		}
	}

	/*
	 * Update the header with relevant NMS settings.
	 */
	public static void formatResponseHeader(Holder<HeaderT> mtosiHeader, String activityName, String msgName)
	{
		mtosiHeader.value.setActivityName(activityName);
        mtosiHeader.value.setMsgName(msgName);
		mtosiHeader.value.setMsgType(MsgTypeT.RESPONSE);
		mtosiHeader.value.setCommunicationPattern(CommunicationPatternT.SIMPLE_RESPONSE);
		mtosiHeader.value.setCommunicationStyle(mtosiHeader.value.getCommunicationStyle());
		mtosiHeader.value.setCorrelationId(mtosiHeader.value.getCorrelationId());
		
		String tmpDestinationURI = mtosiHeader.value.getDestinationURI();
		mtosiHeader.value.setDestinationURI(mtosiHeader.value.getSenderURI());
		mtosiHeader.value.setSenderURI(tmpDestinationURI);

		mtosiHeader.value.setTimestamp(getCurrentTimestamp());
	}
	
	/*
	 * Clone fields from one header to another.
	 */
	public static HeaderT cloneHeader(HeaderT src)
	{
		HeaderT dst = new HeaderT();
		copyHeader(dst, src);

		return dst;
	}

	/*
	 * Copy fields from one header to another.
	 */
	public static void copyHeader(HeaderT dst, HeaderT src)
	{	
		dst.setActivityName(src.getActivityName());
        dst.setMsgName(src.getMsgName());
		dst.setMsgType(src.getMsgType());
		dst.setSenderURI(src.getSenderURI());
		dst.setDestinationURI(src.getDestinationURI());
		dst.setReplyToURI(src.getReplyToURI());
	    dst.setOriginatorURI(src.getOriginatorURI());
	    dst.setFailureReplytoURI(src.getFailureReplytoURI());
	    dst.setActivityStatus(src.getActivityStatus());
	    dst.setCorrelationId(src.getCorrelationId());
	    dst.setSecurity(src.getSecurity());
	    dst.setSecurityType(src.getSecurityType());
	    dst.setPriority(src.getPriority());
	    dst.setMsgSpecificProperties(src.getMsgSpecificProperties());
		dst.setCommunicationPattern(src.getCommunicationPattern());
		dst.setCommunicationStyle(src.getCommunicationStyle());
		/* requestedBatchSize - not supported. */
		/* batchSequenceNumber - not supported. */
		/* batchSequenceEndOfReply - not supported. */
		/* iteratorReferenceURI - not supported. */
		/* fileLocationURI - not supported. */
		/* compressionType - not supported. */
		/* packingType - not supported. */
		dst.setTimestamp(src.getTimestamp());
		/* vendorExtensions - not supported. */
		dst.setTmf854Version(src.getTmf854Version());
	}
}
