/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum GE20XBitLineCodeTranslation implements TranslatableEnum {
    none			(1,  "None"),
    t1_b8zs 		(2,  "T1-B8ZS"),
    t1_ami 			(3,  "T1-AMI"),
    e1_hdb3 		(4,  "E1-HDB3"),
    e1_ami 			(5,  "E1-AMI"),
    NOT_APPLICABLE	(-1, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private GE20XBitLineCodeTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}