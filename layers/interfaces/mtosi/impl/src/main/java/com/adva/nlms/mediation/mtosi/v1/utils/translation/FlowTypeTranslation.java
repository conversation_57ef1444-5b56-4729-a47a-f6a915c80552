/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.common.snmp.MIBFSP150CC825;

/**
 * Created by IntelliJ IDEA. User: Lukasz Date: 2007-05-30 Time: 13:14:09 To change this template use File | Settings |
 * File Templates.
 */
public enum FlowTypeTranslation{
	DEFAULT        (MIBFSP150CC825.EthernetPBFlowSrvTable.Type.VALUE_OF_DEFAULT_TYPE, "Default"),
	REGULAR        (MIBFSP150CC825.EthernetPBFlowSrvTable.Type.VALUE_OF_REGULAR_TYPE, "Regular"),
	NOT_APPLICABLE (2, "n/a");

	//------------------------------------------------------------------------------------------------------------------
	private final int    mibValue;
	private final String mtosiString;

	//------------------------------------------------------------------------------------------------------------------
	/**
	 * Constructor.
	 * @param mibValue    The MIB defined value
	 * @param mtosiString  The string representation used in MTOSI layer.
	 */
	private FlowTypeTranslation (final int mibValue, final String mtosiString)
	{
		this.mibValue   = mibValue;
		this.mtosiString = mtosiString;
	}
	/**
	 * Returns the MIB defined value.
	 * @return the MIB defined value.
	 */
	public int getMIBValue() {
		return mibValue;
	}


	/**
	 * Returns the string representation used in MTOSI layer.
	 * @return the string representation used in MTOSI layer.
	 */
	public String getMtosiString() {
		return mtosiString;
	}

	/**
	 * Returns the string representation used in MTOSI layer.
	 * @param mibValue  The MIB defined value
	 * @return the string representation used in MTOSI layer.
	 */
	public static String getMtosiString(final int mibValue)
	{
		FlowTypeTranslation enumType = NOT_APPLICABLE;  // the return value

		for (FlowTypeTranslation tmpEnumType : values())
		{
			if (mibValue == tmpEnumType.getMIBValue())
			{
				enumType = tmpEnumType;
				break;
			}
		}
		return enumType.getMtosiString();
	}
	/**
	 * Returns the string representation used in MTOSI layer.
	 * @param mtosiString  The MIB defined value
	 * @return the string representation used in MTOSI layer.
	 */
	public static int getMIBValue (final String mtosiString)
	{
		FlowTypeTranslation enumType = NOT_APPLICABLE;  // the return value

		for (FlowTypeTranslation tmpEnumType : values())
		{
			if (mtosiString.equals(tmpEnumType.getMtosiString()))
			{
				enumType = tmpEnumType;
				break;
			}
		}
		return enumType.getMIBValue();

	}
}
