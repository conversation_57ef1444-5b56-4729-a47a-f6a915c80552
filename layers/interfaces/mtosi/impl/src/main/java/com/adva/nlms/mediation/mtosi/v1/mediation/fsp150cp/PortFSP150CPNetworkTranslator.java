/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cp;

import com.adva.nlms.common.snmp.MIBFSP150CP;
import v1.tmf854.ConnectionTerminationPointT;
import v1.tmf854.DirectionalityT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ResourceStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.mediation.common.serviceProvisioning.WANPortSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.WANPortSPPropertiesFSP150CP;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.PortFSP150CPNetwork;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CPArcStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.ControlTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.DuplexModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.NetLoopbackStatusTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.ServiceStateTranslation;

/**
 * This class is an FSP 150 CP Network Port MTOSI Translator.
 */
public class PortFSP150CPNetworkTranslator extends MtosiTranslator {
  private PortFSP150CPNetwork port;

  public PortFSP150CPNetworkTranslator(PortFSP150CPNetwork port) {
    this.port = port;
  }

  @Override
  public PhysicalTerminationPointT toMtosiPTP() throws ProcessingFailureException {
    WANPortSPPropertiesFSP150CP wanSPPropertiesFSP150CP = port.getWANPortSPProperties();
    ObjectFactory objFactory = new ObjectFactory();
    PhysicalTerminationPointT physicalTerminationPointT = objFactory.createPhysicalTerminationPointT();

    // PTP element name
    NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributes(port);
    physicalTerminationPointT.setName(objFactory.createPhysicalTerminationPointTName(namingAttributes));

    // discoveredName
    final String ptpNm = namingAttributes.getPtpNm();
    if (ptpNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
    }
    physicalTerminationPointT.setDiscoveredName(objFactory.createPhysicalTerminationPointTDiscoveredName(ptpNm));

    // namingOS
    physicalTerminationPointT.setNamingOS(objFactory.createPhysicalTerminationPointTNamingOS(OSFactory.getNmsName()));

    // source
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    physicalTerminationPointT.setSource(objFactory.createPhysicalTerminationPointTSource(source));

    // resource state
    ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ResourceStateEnumT.INSTALLED);
    physicalTerminationPointT.setResourceState(objFactory.createPhysicalTerminationPointTResourceState(resourceState));

    // direction
    physicalTerminationPointT.setDirection(objFactory.createPhysicalTerminationPointTDirection(DirectionalityT.D_BIDIRECTIONAL));

    // edgePoint
    physicalTerminationPointT.setEdgePoint(objFactory.createPhysicalTerminationPointTEdgePoint(Boolean.TRUE));

    // layers
    LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    // -------start Layer--------
    final int mediaType = wanSPPropertiesFSP150CP.get(WANPortSPPropertiesFSP150CP.VI.Type);
    if (mediaType == MIBFSP150CP.If.TYPE_NET) {
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL,
              LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.ACTIVE_PARAM);

      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_PHYSICAL_OPTICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_PHYSICAL_OPTICAL,
              LayeredParams.LrElectricalAndOptical.CONNECTOR_TYPE_PARAM, wanSPPropertiesFSP150CP
              .get(WANPortSPPropertiesFSP150CP.VS.ConnectorType).equals("") ? "Unknown" : wanSPPropertiesFSP150CP.get(WANPortSPPropertiesFSP150CP.VS.ConnectorType));
    } else {
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL,
              LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.INACTIVE_PARAM);
    }
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.ADMINISTRATION_CONTROL_PARAM,
            MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, wanSPPropertiesFSP150CP.get(WANPortSPProperties.VI.IfAdminStatus)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.SERVICE_STATE_PARAM, ServiceStateTranslation.getMtosiString(
            wanSPPropertiesFSP150CP.get(WANPortSPProperties.VI.IfAdminStatus), wanSPPropertiesFSP150CP.get(WANPortSPProperties.VI.IfOperStatus)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.AUTO_NEGOTIATION_PARAM,
            MtosiUtils.getMtosiString(ControlTypeTranslation.NOT_APPLICABLE, wanSPPropertiesFSP150CP.get(WANPortSPPropertiesFSP150CP.VI.AutoNeg)));

    int speedRate = 0;
    if (wanSPPropertiesFSP150CP.get(WANPortSPPropertiesFSP150CP.VI.AutoNeg) == MIB.RFC1253.TRUTH_VALUE_FALSE) {
      speedRate = wanSPPropertiesFSP150CP.get(WANPortSPProperties.VI.PortSpeed);
    } else if (wanSPPropertiesFSP150CP.get(WANPortSPPropertiesFSP150CP.VI.AutoNeg) == MIB.RFC1253.TRUTH_VALUE_TRUE) {
      speedRate = wanSPPropertiesFSP150CP.get(WANPortSPProperties.VI.PortNegotiatedSpeed);
    }

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.ADMINISTRATIVE_SPEED_RATE_PARAM, Integer.toString(speedRate));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.ACTUAL_SPEED_RATE_PARAM, Integer
            .toString(wanSPPropertiesFSP150CP.get(WANPortSPProperties.VI.PortNegotiatedSpeed)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.DUPLEX_MODE_PARAM,
            DuplexModeTranslation.getMtosiString(MIB.RFC1253.TRUTH_VALUE_TRUE));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.ACTUAL_DUPLEX_MODE_PARAM,
            DuplexModeTranslation.getMtosiString(MIB.RFC1253.TRUTH_VALUE_TRUE));
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.CONNECTIONLESS_PORT_PARAM, MtosiConstants.FALSE);

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.INTERFACE_TYPE_PARAM, "NNI");

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.PORT_TP_ROLE_STATE_PARAM, "internal"); // always have assigned state.

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.NUMBER_OF_TRAFFIC_CLASSES_PARAM, "1");

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.MAX_NUM_FDFRS_PARAM, "1");

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.NUM_CONFIGURED_FDFRS_PARAM, Integer.toString(wanSPPropertiesFSP150CP
            .get(WANPortSPProperties.VI.NumConfiguredFDFrs)));
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.EFMOAM_ENABLED_PARAM,
            MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, wanSPPropertiesFSP150CP.get(WANPortSPPropertiesFSP150CP.VI.OAMEnable)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.EFMOAM_ACTIVE_PARAM,
            MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, wanSPPropertiesFSP150CP.get(WANPortSPPropertiesFSP150CP.VI.OAMActive)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.EFMOAM_INFO_ENABLE_PARAM,
            MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, wanSPPropertiesFSP150CP.get(WANPortSPPropertiesFSP150CP.VI.OAMInfoEnable)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.LOOPBACK_STATUS_TYPE_PARAM, NetLoopbackStatusTypeTranslation
            .getMtosiString(wanSPPropertiesFSP150CP.get(WANPortSPPropertiesFSP150CP.VI.LoopbackStatus)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.ALARM_REPORT_CONTROL,
            CPArcStateTranslation.getMtosiString(wanSPPropertiesFSP150CP.get(WANPortSPPropertiesFSP150CP.VI.ArcState )));

    // -------end of Layer-------

    physicalTerminationPointT.setTransmissionParams(objFactory.createPhysicalTerminationPointTTransmissionParams(layeredParametersListT));
    return physicalTerminationPointT;
  }

  @Override
  public ConnectionTerminationPointT toMtosiCTP() throws ProcessingFailureException {
    final ObjectFactory objFactory = new ObjectFactory();
    final ConnectionTerminationPointT connectionTerminationPoint = objFactory.createConnectionTerminationPointT();

    // CTP Name
    final NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributesCTPonFTP(port);
    connectionTerminationPoint.setName(objFactory.createConnectionTerminationPointTName(namingAttributes));

    // discoveredName
    final String ctpNm = namingAttributes.getCtpNm();
    if (ctpNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
    }
    connectionTerminationPoint.setDiscoveredName(objFactory.createConnectionTerminationPointTDiscoveredName(ctpNm));

    // namingOS
    connectionTerminationPoint.setNamingOS(objFactory.createConnectionTerminationPointTNamingOS(OSFactory.getNmsName()));

    // source
    final SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    connectionTerminationPoint.setSource(objFactory.createConnectionTerminationPointTSource(source));

    // resource state
    final ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ResourceStateEnumT.INSTALLED);
    connectionTerminationPoint.setResourceState(objFactory.createConnectionTerminationPointTResourceState(resourceState));

    // direction
    connectionTerminationPoint.setDirection(objFactory.createConnectionTerminationPointTDirection(DirectionalityT.D_BIDIRECTIONAL));

    // layers
    final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_LAG_FRAGMENT);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_LAG_FRAGMENT,
            LayeredParams.LrLagFragment.LAG_MEMBER_PARAM, port.getMtosiName());
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
    // Empty layer indicating support for Ethernet.
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET);
    // Empty layer indicating support for ADVA Ethernet.
    // -------end of Layer-------

    connectionTerminationPoint.setTransmissionParams(objFactory.createConnectionTerminationPointTTransmissionParams(layeredParametersListT));
    return connectionTerminationPoint;
  }
}
