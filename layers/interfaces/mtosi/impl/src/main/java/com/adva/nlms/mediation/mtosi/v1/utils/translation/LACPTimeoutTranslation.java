/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum LACPTimeoutTranslation implements TranslatableEnum {
  LONG           (0, "Long"),
  SHORT          (2, "Short"),
  NOT_APPLICABLE (4, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private LACPTimeoutTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}