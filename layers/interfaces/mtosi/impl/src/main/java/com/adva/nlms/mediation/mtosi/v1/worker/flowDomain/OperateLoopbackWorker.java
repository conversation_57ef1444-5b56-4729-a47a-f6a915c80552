/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NVSListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.OperateLoopbackResponseT;
import v1.tmf854ext.adva.OperateLoopbackT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.ws.Holder;

public abstract class OperateLoopbackWorker extends AbstractMtosiWorker
{
	Logger LOG = LogManager.getLogger(this.getClass().getName());

  protected OperateLoopbackT mtosiBody;
	protected OperateLoopbackResponseT response = new OperateLoopbackResponseT();
	protected NamingAttributesT namingAttributes;
	protected NetworkElement ne;
  protected String tpName;
  protected String loopbackType;
	protected NVSListT loopbackParameters;

  public OperateLoopbackWorker(OperateLoopbackT mtosiBody, Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne) {
    super(mtosiHeader, "operateLoopback", "operateLoopback", "operateLoopbackResponse");
    this.mtosiBody = mtosiBody;
    this.namingAttributes = namingAttributes;
    this.ne = ne;
  }

	@Override
  protected void parse() throws Exception {
    if (!NamingTranslationFactory.isFtp(namingAttributes) && !NamingTranslationFactory.isPort(namingAttributes)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ftpNm/ptpNm has not been specified.");
    }
    this.tpName = namingAttributes.getFtpNm() != null ? namingAttributes.getFtpNm() : namingAttributes.getPtpNm();

    if ((loopbackType = mtosiBody.getLoopbackType()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "LoopbackType parameter is mandatory.");
    }

    JAXBElement<NVSListT> params = mtosiBody.getLoopbackParameters();
    if (params != null) {
      this.loopbackParameters = params.getValue();
    }
  }

	  @Override
	  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	   validator.validate(ne.getDefaultNetworkElementTypeString());
	  }
	  
	@Override
  public OperateLoopbackResponseT getSuccessResponse()
	{
		if (response == null)
			return null;
		response.setTmf854Version(MtosiConstants.VERSION);
		return response;
	}
}