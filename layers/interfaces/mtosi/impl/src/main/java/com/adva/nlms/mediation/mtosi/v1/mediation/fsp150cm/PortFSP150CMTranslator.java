/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm;

import com.adva.nlms.common.util.BitFields;
import com.adva.nlms.common.NEUtils;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.snmp.MIB;
import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.common.snmp.f3.LinkLossFwdTriggerTypes;
import com.adva.nlms.mediation.common.serviceProvisioning.ACCPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.NETPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.WANPortSPProperties;
import com.adva.nlms.mediation.config.Module;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3Impl;
import com.adva.nlms.mediation.config.f3.entity.port.MTOSIPortF3;
import com.adva.nlms.mediation.config.f3.mtosi.NetworkElementMTOSIWorkerF3Impl;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.*;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.*;
import v1.tmf854.*;
import ws.v1.tmf854.ProcessingFailureException;

/**
 * This class is an FSP 150 CM generic port MTOSI Translator.
 */
public class PortFSP150CMTranslator extends MtosiTranslator {
  protected MTOSIPortF3 port;

  public PortFSP150CMTranslator(MTOSIPortF3 port) {
    this.port = port;
  }

  protected PhysicalTerminationPointT toMtosiPTP(boolean isACCPort) throws ProcessingFailureException {
    NetworkElement ne = port.getNE();
	  int neType = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI();
    boolean isRemoteCP = (neType == NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM || neType == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR);
    boolean isEFM = (neType == NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM);
    boolean isGEDevice = NEUtils.isGEDevice(neType);
    boolean isXGDevice = NEUtils.isXG210(neType);
    boolean isCopper = false;
    //port.
    boolean isVersion41OrHigher = ne.getPersistenceHelper().getMIBVariantFromDB() >= MIBFSP150CM.MibVariant.VER_4_1_1;
    boolean isNTEPort = isNteModule();
    NETPortSPPropertiesFSP150CM portProperties = port.getPortSPProperties();
    ObjectFactory objFactory = new ObjectFactory();
    PhysicalTerminationPointT physicalTerminationPointT = objFactory.createPhysicalTerminationPointT();

    // PTP element name
    NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributes(port);
    physicalTerminationPointT.setName(objFactory.createPhysicalTerminationPointTName(namingAttributes));

    // discoverdName
    final String ptpNm = namingAttributes.getPtpNm();
    if (ptpNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
    }
    physicalTerminationPointT.setDiscoveredName(objFactory.createPhysicalTerminationPointTDiscoveredName(ptpNm));

    // namingOS
    physicalTerminationPointT.setNamingOS(objFactory.createPhysicalTerminationPointTNamingOS(OSFactory.getNmsName()));

    // source
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    physicalTerminationPointT.setSource(objFactory.createPhysicalTerminationPointTSource(source));

    // resource state
    ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ResourceStateEnumT.INSTALLED);
    physicalTerminationPointT.setResourceState(objFactory.createPhysicalTerminationPointTResourceState(resourceState));

    // direction
    physicalTerminationPointT.setDirection(objFactory.createPhysicalTerminationPointTDirection(DirectionalityT.D_BIDIRECTIONAL));

    // edgePoint
    physicalTerminationPointT.setEdgePoint(objFactory.createPhysicalTerminationPointTEdgePoint(true));

    // layers
    LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    // -------start Layer--------
    final int mediaType = portProperties.get(NETPortSPPropertiesFSP150CM.VI.MediaType);
    if (mediaType == MIBFSP150CM.Facility.EthernetNetPortTable.MEDIA_TYPE_COPPER||
    		mediaType == MIBFSP150CM.Facility.EthernetNetPortTable.MEDIA_TYPE_COPPERSFP ) {
      isCopper = true;
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL,
              LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.ACTIVE_PARAM);

      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL,
              LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.INACTIVE_PARAM);
    } else if (mediaType == MIBFSP150CM.Facility.EthernetNetPortTable.MEDIA_TYPE_FIBER) {
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL,
              LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.ACTIVE_PARAM);

      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_PHYSICAL_OPTICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_PHYSICAL_OPTICAL,
              LayeredParams.LrElectricalAndOptical.CONNECTOR_TYPE_PARAM,
              MtosiUtils.getMtosiString(CMConnectorTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.ConnectorType)));

      // -------end of Layer-------

      if (isVersion41OrHigher && !isRemoteCP) {
        // -------start Layer--------
        LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_Optical_Channel);
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_Optical_Channel,
                LayeredParams.LROpticalChannel.SFP_REACH_PARAM,
                String.valueOf(portProperties.get(NETPortSPPropertiesFSP150CM.VI.SFPReach)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_Optical_Channel,
                LayeredParams.LROpticalChannel.SFP_LASER_WAVE_LENGTH_PARAM,
                String.valueOf(portProperties.get(NETPortSPPropertiesFSP150CM.VI.SfpLaserWaveLength)));

        Integer sfpMediaType = portProperties.get(NETPortSPPropertiesFSP150CM.VI.SfpMediaType);
        if (sfpMediaType != null) {
          LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_Optical_Channel,
              LayeredParams.LROpticalChannel.SFP_MEDIA_TYPE_PARAM,
              MtosiUtils.getMtosiString(GE20XSfpMediaTypeTranslation.NOT_APPLICABLE, sfpMediaType));
        } else {
          LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_Optical_Channel,
              LayeredParams.LROpticalChannel.SFP_MEDIA_TYPE_PARAM,
              "TODO"); //TODO, value missing from properties

        }

      }
      // -------end of Layer-------
    }
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.ADMINISTRATION_CONTROL_PARAM,
            MtosiUtils.getMtosiString(CMAdministrationControlTranslation.NOT_APPLICABLE, portProperties.get(WANPortSPProperties.VI.IfAdminStatus)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.SERVICE_STATE_PARAM, CMServiceStateTranslation.getMtosiString(
            portProperties.get(WANPortSPProperties.VI.IfAdminStatus), portProperties.get(WANPortSPProperties.VI.IfOperStatus)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.AUTO_NEGOTIATION_PARAM, GE20XAutoNegotiationTranslation
            .getMtosiString(portProperties.get(WANPortSPProperties.VI.PortSpeed)));

    String speedRate = GE20XAutoNegotiationTranslation.getSpeedRate(portProperties.get(WANPortSPProperties.VI.PortSpeed));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.ADMINISTRATIVE_SPEED_RATE_PARAM, speedRate);

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.ACTUAL_SPEED_RATE_PARAM, GE20XAutoNegotiationTranslation
            .getMtosiString(portProperties.get(WANPortSPProperties.VI.PortSpeed)).equals("Enabled") ? portProperties.get(WANPortSPProperties.VI.PortNegotiatedSpeed) == 0 ?
            "0" : GE20XAutoNegotiationTranslation.getSpeedRate(portProperties.get(WANPortSPProperties.VI.PortNegotiatedSpeed)) : speedRate);

    String duplex = GE20XAutoNegotiationTranslation.getDuplexMode(portProperties.get(WANPortSPProperties.VI.PortSpeed));
    if (mediaType == MIBFSP150CM.Facility.EthernetNetPortTable.MEDIA_TYPE_FIBER) {
      duplex = DuplexModeTranslation.getMtosiString(MIB.RFC1253.TRUTH_VALUE_TRUE);//true because on optical access port duplex is on
    }
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.DUPLEX_MODE_PARAM, duplex);

    String actualDuplex;
    if (portProperties.get(WANPortSPProperties.VI.PortNegotiatedSpeed) == 0) {
      if (GE20XAutoNegotiationTranslation.getMtosiString(portProperties.get(WANPortSPProperties.VI.PortSpeed)).equals("Enabled")) {
        actualDuplex = DuplexModeTranslation.getMtosiString(MIB.RFC1253.TRUTH_VALUE_TRUE);
      } else {
        actualDuplex = duplex;
      }
    } else {
      actualDuplex = GE20XAutoNegotiationTranslation.getDuplexMode(portProperties.get(WANPortSPProperties.VI.PortNegotiatedSpeed));
    }

    if (mediaType == MIBFSP150CM.Facility.EthernetNetPortTable.MEDIA_TYPE_FIBER) {
      actualDuplex = DuplexModeTranslation.getMtosiString(MIB.RFC1253.TRUTH_VALUE_TRUE);//true because on optical access port duplex is on
    }
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.ACTUAL_DUPLEX_MODE_PARAM, actualDuplex);

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_DSR_GIGABIT_ETHERNET,
            LayeredParams.LrDsrGigabitAndFastEthernet.MAXIMUM_FRAME_SIZE_PARAM, Integer.toString(portProperties.get(WANPortSPProperties.VI.IfMtu)));
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.CONNECTIONLESS_PORT_PARAM, MtosiConstants.TRUE);

    // INTERFACE_TYPE_PARAM delegated to specific methods

    // PORT_TP_ROLE_STATE_PARAM delegated to specific methods

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.NUMBER_OF_TRAFFIC_CLASSES_PARAM, "1");

    // PHYS_ADDRESS_PARAM delegated to specific methods

    // MAX_NUM_FDFRS_PARAM delegated to specific methods

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.NUM_CONFIGURED_FDFRS_PARAM, Integer.toString(portProperties.get(WANPortSPProperties.VI.NumConfiguredFDFrs)));
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.SECONDARY_STATE_PARAM, portProperties.get(NETPortSPPropertiesFSP150CM.VS.SecondaryState));

    // PORT_MODE_PARAM delegated to specific methods

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.MEDIA_TYPE_PARAM,
            MtosiUtils.getMtosiString(MediaTypeTranslation.NOT_APPLICABLE, mediaType));

    // JumboFramesEnabled on the remote CP is not editable and defaults to True
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.JUMBO_FRAMES_ENABLED_PARAM,
            MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, (isRemoteCP && !isEFM) ? MIB.RFC1253.TRUTH_VALUE_TRUE :portProperties.get(NETPortSPPropertiesFSP150CM.VI.JumboFramesEnabled)));

    if (!isRemoteCP) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
              LayeredParams.LrPropAdvaEthernet.TRANSMIT_PAUSE_FRAMES_ENABLED_PARAM,
              MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.TxPauseEnabled)));
        if(isACCPort){
            ACCPortSPPropertiesFSP150CM props = (ACCPortSPPropertiesFSP150CM) port.getPortSPProperties();
            LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                    LayeredParams.LrPropAdvaEthernet.RECEIVE_PAUSE_FRAMES_ENABLED_PARAM,
                    MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, props.get(ACCPortSPPropertiesFSP150CM.VI.RxPauseEnabled)));
        }

//deprecated
//      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
//              LayeredParams.LrPropAdvaEthernet.RECEIVE_PAUSE_FRAMES_DISPOSITION_PARAM,
//              MtosiUtils.getMtosiString(CMDispositionTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.RxPauseDisposition)));
    }
    //  The NTE does not support AFP on network port 
    //  - it is like GE201 (and it is not supported in that ASIC which NTE is derived from).
    //  NTU does support on network ports.
    //  Both NTU and NTE support AFP on access ports.
    if (!((isNTEPort || isGEDevice) && !isACCPort)) {

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.TAGGED_FRAMES_ENABLED_PARAM, CMAcceptableFramePolicyTranslation.
            getTagged(portProperties.get(NETPortSPPropertiesFSP150CM.VI.AfpType)));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.UNTAGGED_FRAMES_ENABLED_PARAM, CMAcceptableFramePolicyTranslation.
            getUnTagged(portProperties.get(NETPortSPPropertiesFSP150CM.VI.AfpType)));
    }
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
              LayeredParams.LrPropAdvaEthernet.LOOPBACK_STATUS_TYPE_PARAM,
              MtosiUtils.getMtosiString(CMLoopbackStatusTypeTranslation.NOT_APPLICABLE, portProperties.get(WANPortSPProperties.VI.PortLoopback)));

    if (!isRemoteCP) {
      if (portProperties.get(WANPortSPProperties.VI.PortLoopback) == MIBFSP150CM.Facility.EthernetNetPortTable.LOOPBACK_CONFIG_EFM_OAM_REMOTE_TIMED
              || portProperties.get(WANPortSPProperties.VI.PortLoopback) == MIBFSP150CM.Facility.EthernetNetPortTable.LOOPBACK_CONFIG_FACILITY_PORT_TIMED
              ||  portProperties.get(WANPortSPProperties.VI.PortLoopback) == MIBFSP150CM.Facility.EthernetNetPortTable.LOOPBACK_CONFIG_TERMINAL_PORT_TIMED) {
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.LOOPBACK_STATUS_TIME_PARAM, Integer.toString(portProperties.get(NETPortSPPropertiesFSP150CM.VI.LoopbackTime)));
      }
    }
    if (!isRemoteCP || isACCPort) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
              LayeredParams.LrPropAdvaEthernet.LOOPBACK_STATUS_SWAP_SADA_PARAM,
              MtosiUtils.getMtosiString(CMLoopbackStatusSwapSADATranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.LoopbackSwapSADA)));
    }
    if (!isRemoteCP) {
      if (portProperties.get(WANPortSPProperties.VI.PortLoopback) == MIBFSP150CM.Facility.EthernetNetPortTable.LOOPBACK_CONFIG_FACILITY_VLAN
              || portProperties.get(WANPortSPProperties.VI.PortLoopback) == MIBFSP150CM.Facility.EthernetNetPortTable.LOOPBACK_CONFIG_TERMINAL_VLAN) {
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.LOOPBACK_STATUS_VLAN_LIST_PARAM, CMLoopbackStatusTypeTranslation.getOuterLoopbackList(portProperties));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.LOOPBACK_STATUS_INNER_VLAN_LIST_PARAM, CMLoopbackStatusTypeTranslation.getInnerLoopbackList(portProperties));
      }
    }

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_ENABLED_PARAM,
            MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdEnabled)));

    if (isVersion41OrHigher && !isRemoteCP) {
      if (portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdEnabled) == MIB.RFC1253.TRUTH_VALUE_TRUE) {
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_TRIGGER_TYPES_PARAM,
                LinkLossFwdTriggerTypes.getMtosiString((portProperties.get(NETPortSPPropertiesFSP150CM.Vb.LinkLossFwdTriggerTypes))));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_DELAY_PARAM, 
            Integer.toString(portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdDelay)));
      }
    } else {
      //I am relucant to change this, for pre-CM4.1, and since SignalType is gone in 4.1 I can't have this
      if (portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdEnabled) == MIB.RFC1253.TRUTH_VALUE_TRUE && 
      		portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdSignalType) != CMLinkLossFwdSignalTypeTranslation.NOT_APPLICABLE.getMIBValue()) {
        if (!isVersion41OrHigher || isRemoteCP) { //LinkLossFwdSignalType is gone, as of CM 4.1 for the CM
          LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                  LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_SIGNAL_TYPE_PARAM,
                  MtosiUtils.getMtosiString(CMLinkLossFwdSignalTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdSignalType)));
  
        }
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_TRIGGER_TYPES_PARAM,
                LinkLossFwdTriggerTypes.getMtosiString(portProperties.get(NETPortSPPropertiesFSP150CM.Vb.LinkLossFwdTriggerTypes)));
  
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_DELAY_PARAM, Integer.toString(portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdDelay)));
      }
    }

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_ACTIVE_PARAM,
            MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdActive)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_PARTNER_ENABLED_PARAM,
            MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdPartnerEnabled)));

    if (isVersion41OrHigher && !isRemoteCP) {
      if (portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdEnabled) == MIB.RFC1253.TRUTH_VALUE_TRUE) {
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.LOCAL_LINK_ID_PARAM, Integer.toString(portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdLocalLinkId)));
      }
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
          LayeredParams.LrPropAdvaEthernet.REMOTE_LINK_IDS_PARAM,
          GE20XRemoteLinkIdsTranslation.getMtosiString(6 ,portProperties.get(NETPortSPPropertiesFSP150CM.Vb.LinkLossFwdRemoteLinkIds)));
    } else {
      if (portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdEnabled) == MIB.RFC1253.TRUTH_VALUE_TRUE &&
          portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdSignalType) == MIBFSP150CM.Facility.EthernetNetPortTable.SIGNAL_TYPE_LLF_EFM_SIGNAL) {
          LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
              LayeredParams.LrPropAdvaEthernet.LOCAL_LINK_ID_PARAM, Integer.toString(portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdLocalLinkId)));
      }
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
              LayeredParams.LrPropAdvaEthernet.REMOTE_LINK_IDS_PARAM,
              "1"); // TODO: remove this hard-coded value, when the NBI change
    }

    if (isVersion41OrHigher && !isRemoteCP) {
      try {  //TODO (dw) Remove
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
          LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_TX_ACTION_TYPE_PARAM,
          MtosiUtils.getMtosiString(GE20XLlfTxActionTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdTxActionType)));
      } catch (NullPointerException n) {
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_TX_ACTION_TYPE_PARAM,
            null);
      }
      
      try {
        
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_RX_RLD_LINK_IDS_PARAM,
                GE20XRemoteLinkIdsTranslation.getMtosiString(6 ,BitFields.intToBytes(portProperties.get(NETPortSPPropertiesFSP150CM.VI.LinkLossFwdRxRLDLinkIds),1)));
      } catch (Exception e) {
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_RX_RLD_LINK_IDS_PARAM,
            "null");
        } 
    }
    if (isCopper) { //include MDIMode in response also for remote NE (GE102ProH)
      //MDIMode is a parameter that should be gettable for 7.3 per FNM10783 
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.MDI_MODE_PARAM,
                MtosiUtils.getMtosiString(GE20XMdiModeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.MDIType)));
      if (isVersion41OrHigher ) {
        try {
          LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                    LayeredParams.LrPropAdvaEthernet.ACTUAL_MDI_MODE_PARAM,
                    MtosiUtils.getMtosiString(GE20XMdiModeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.MDIMode)));
        } catch (Exception e) {
          LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
              LayeredParams.LrPropAdvaEthernet.ACTUAL_MDI_MODE_PARAM,
              "null");
        }
      }
      if((isACCPort && !isXGDevice) || isGEDevice){
          String autoNegClockMode =  MtosiUtils.getMtosiString(CMSyncEAutoNegTranslation.NOT_APPLICABLE,portProperties.get(WANPortSPProperties.VI.PortSpeed) );
          if(autoNegClockMode.equals(CMSyncEAutoNegTranslation.Master.getMtosiString()) || autoNegClockMode.equals(CMSyncEAutoNegTranslation.Slave.getMtosiString()) )  {
              LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                      LayeredParams.LrPropAdvaEthernet.AUTONEG_CLOCK_MODE,
                      autoNegClockMode);
          }
      }
    }

    if((isXGDevice && !isACCPort) || ( isXGDevice && isACCPort && GE20XAutoNegotiationTranslation.getSpeedRate(portProperties.get(WANPortSPProperties.VI.PortSpeed)).equals("10000")) &&
                                       GE20XAutoNegotiationTranslation.getDuplexMode(portProperties.get(WANPortSPProperties.VI.PortSpeed)).equals("Full") ){   //TODO: Use speed constants
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
                LayeredParams.LrPropAdvaEthernet.PORT_XGE_PHY_TYPE,
                MtosiUtils.getMtosiString(XG210XgePhyTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.XgePhyType)));
    }
    
    // -------end of Layer-------
    if (isNTEPort || (isXGDevice && isACCPort) ) {
      // -------start PROP_ADVA_SyncEthernet Layer--------
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet);
      String syncEAdminControl = MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.SyncEAdministrationControl));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
              LayeredParams.PropAdvaSyncEthernet.SYNCE_ADMINISTRATION_CONTROL_PARAM, syncEAdminControl);
      if (!syncEAdminControl.equals(AdministrationControlTranslation.DISABLED.getMtosiString())) {
        String qlModeAdminControl = MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.QLModeAdministrationControl));
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
                LayeredParams.PropAdvaSyncEthernet.QL_MODE_ADMINISTRATION_CONTROL_PARAM, 
                qlModeAdminControl);
        
        if (qlModeAdminControl.equals("Enabled")) {
          LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
                LayeredParams.PropAdvaSyncEthernet.EXPECTED_QL_PARAM, 
                MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.ExpectedQL)));
        }
        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
                LayeredParams.PropAdvaSyncEthernet.ASSUMED_QL_PARAM, 
                MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.AssumedQL)));
        
        if (!qlModeAdminControl.equals("Disabled")) {
          LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
                LayeredParams.PropAdvaSyncEthernet.RECEIVED_QL_PARAM, 
                MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.ReceivedQL)));
        
          LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
                LayeredParams.PropAdvaSyncEthernet.TRANSMIT_QL_PARAM, 
                MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.TransmitQL)));
        }
      }
    /*  if(!isXGDevice){
          LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet,
              LayeredParams.PropAdvaSyncEthernet.SYNCE_AUTO_NEGOTIATION_CLOCK_PARAM,
              GE20XAutoNegotiationTranslation.getMasterOrSlave(portProperties.get(WANPortSPProperties.VI.PortSpeed)));
      }*/
    // -------end of Layer-------
    }

    // -------start Layer--------
    if (!isACCPort || !isRemoteCP) {
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
              LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_ID_PARAM, Integer.toString(portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamId)));

      String adminControlValue;
      if (isGEDevice || isXGDevice || isNTEPort ) {
    	  adminControlValue = MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamEnabled));
      } else {
    	  adminControlValue = MtosiUtils.getMtosiString(CMDispositionTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamAdminDisposition));
      }
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
              LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_ADMINISTRATION_CONTROL_PARAM, adminControlValue);

      if (portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamAdminDisposition) == CMDispositionTranslation.Peer.getMIBValue()) {
       // if (portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamDiscoveryState) != 0) { // TODO: this is only a workaround for bug on device
          LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                  LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_DISCOVERY_STATE_PARAM,
                  MtosiUtils.getMtosiString(CMEfmOamDiscoveryStateTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamDiscoveryState)));
        //}

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_LOCAL_VAR_RTRVS_ENABLED_PARAM,
                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamLocalVarRtrvsEnabled)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_LOCAL_LINK_EVENTS_ENABLED_PARAM,
                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamLocalLinkEventsEnabled)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_LOCAL_OAM_LOOPBACKS_SUPPORTED_PARAM,
                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamLocalOamLoopbacksSupported)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_LOCAL_UNIDIR_SUPPORT_ENABLED_PARAM,
                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamLocalUnidirSupportEnabled)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_LOCAL_MAX_PDU_SIZE_PARAM,
                Integer.toString(portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamLocalMaxPDUSize)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_LOCAL_MODE_PARAM,
                MtosiUtils.getMtosiString(CMEfmOamModeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamLocalMode)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_REMOTE_PHYS_ADDRESS_PARAM, portProperties.get(NETPortSPPropertiesFSP150CM.VS.OamRemoteMacAddress));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_REMOTE_VAR_RTRVS_ENABLED_PARAM,
                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamRemoteVarRtrvsEnabled)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_REMOTE_LINK_EVENTS_ENABLED_PARAM,
                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamRemoteLinkEventsEnabled)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_REMOTE_OAM_LOOPBACKS_SUPPORTED_PARAM,
                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamRemoteOamLoopbacksSupported)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_REMOTE_UNIDIR_SUPPORT_ENABLED_PARAM,
                MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamRemoteUnidirSupportEnabled)));

        LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_REMOTE_MAX_PDU_SIZE_PARAM, Integer.
                toString(portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamRemoteMaxPDUSize)));

        if (portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamRemoteMode) != 0) { // TODO: this is only a workaround for bug on device
          LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_EFMOAM,
                  LayeredParams.LrPropAdvaEthernetEFMOAM.EFM_OAM_REMOTE_MODE_PARAM,
                  MtosiUtils.getMtosiString(CMEfmOamModeTranslation.NOT_APPLICABLE, portProperties.get(NETPortSPPropertiesFSP150CM.VI.OamRemoteMode)));
        }

       // if(portProperties.get(NETPortSPPropertiesFSP150CM.VI.Oam))
      }
    }
    // -------end of Layer-------

    physicalTerminationPointT.setTransmissionParams(objFactory.createPhysicalTerminationPointTTransmissionParams(layeredParametersListT));
    return physicalTerminationPointT;
  }

  /**
   * 
   * @return
   */
  private boolean isNteModule() {
    NETPortSPPropertiesFSP150CM props = port.getPortSPProperties();
    int shelfIndex = props.get(NETPortSPPropertiesFSP150CM.VI.ShelfIndex);
    int slotIndex = props.get(NETPortSPPropertiesFSP150CM.VI.SlotIndex);
    NetworkElement ne = port.getNE();
    Module module = ((NetworkElementMTOSIWorkerF3Impl)ne.getMTOSIWorker()).getModule(shelfIndex,slotIndex);
    if (module instanceof NteF3Impl)
        return true;
    return false;
  }
}
