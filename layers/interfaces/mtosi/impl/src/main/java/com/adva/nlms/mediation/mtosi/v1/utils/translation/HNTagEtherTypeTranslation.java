/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
 /**
  * Note, Enums cannot start with a number so the number is appended and stripped in the getters.
  */
public enum HNTagEtherTypeTranslation {
    x600        	(1536),
    x8100         	(33024),
    xFFFE			(65534);
    
    private long mibValue;
    
    private long getMibValue() {
		return mibValue;
	}

	private HNTagEtherTypeTranslation(long code) {
    	this.mibValue = code;
    }

    public static String getMtosiString(final Long mibValue) {
    	if (mibValue == null) {
    		return MtosiConstants.NOT_APPLICABLE;
    	}
    	//OK, I need to add a bit of a hack here... I cannot
    	//have enums start with a number...
    	for (HNTagEtherTypeTranslation value: values() ) {
    		if (value.getMibValue() == mibValue.intValue()) {
    			return "0"+value.name(); 
    		}
    	}
    	return String.valueOf(mibValue);
    }
    
    public static long getMibValue(final String name) {
    	for (HNTagEtherTypeTranslation value: values() ) {
    		//Skip the leading 0
    		if (value.name().equals(name.substring(1))) {
    			return value.getMibValue(); 
    		}
    	}
    	return -1;
    }
}
