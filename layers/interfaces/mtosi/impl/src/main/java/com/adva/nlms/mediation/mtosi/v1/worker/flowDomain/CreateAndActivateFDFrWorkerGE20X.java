/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import jakarta.xml.ws.Holder;

import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854ext.adva.CreateAndActivateFDFrT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesFSP150CM;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;

public class CreateAndActivateFDFrWorkerGE20X extends CreateAndActivateFDFrWorkerCM {

	public CreateAndActivateFDFrWorkerGE20X(CreateAndActivateFDFrT mtosiBody, Holder<HeaderT> mtos<PERSON><PERSON>eader, NetworkElement ne, NamingAttributesListT end,
			NamingAttributesListT end2) {
		super(mtosiBody, mtosiHeader, ne, end, end2);
	}

	/**
	 * Extract the device specific properties
	 * @return
	 * @throws ProcessingFailureException 
	 */
	@Override
  protected FlowSPPropertiesFSP150CM getCTPProperties() throws ProcessingFailureException {
		return MtosiTPMediator.mtosiTPDataTToGE_CTPProperties(getTpDataFlow(), getFlow());
	}


}
