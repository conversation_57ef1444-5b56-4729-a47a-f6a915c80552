/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum CMSyncEAutoNegTranslation implements TranslatableEnum {
  NOT_APPLICABLE  (0, "n/a"),
  None            (12, "None"),
  Master          (15, "Master"),
  Slave           (16, "Slave");

  private final int    mibValue;
  private final String mtosiString;

  private CMSyncEAutoNegTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}
