/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.factory;

import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import v1.tmf854.GranularityT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectAcronymT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.SimpleFilterT;
import v1.tmf854.SimpleFilterT.IncludedObjectType;
import ws.v1.tmf854.ProcessingFailureException;

import java.util.Iterator;
import java.util.List;

public class InventoryRetrievalFilter
{
	private SimpleFilterT simpleFilter = null;
	// Include object types?
	private boolean includeMd = false;
	private boolean includeOs = false;
	private boolean includeMe = false;
	private boolean includeEh = false;
	private boolean includeEq = false;
	private boolean includePtp = false;
	// Include Object Name?
	private boolean includeMdName = false;
	private boolean includeOsName = false;
	private boolean includeMeName = false;
	private boolean includeEhName = false;
	private boolean includeEqName = false;
	private boolean includePtpName = false;
	// Include Object Attrs
	private boolean includeMdAttrs = false;
	private boolean includeOsAttrs = false;
	private boolean includeMeAttrs = false;
	private boolean includeEhAttrs = false;
	private boolean includeEqAttrs = false;
	private boolean includePtpAttrs = false;

	/*
	 * NOTE: Later we need to add includeXXRelationships as well // once we
	 * implement the relationship pointers stuff (ie. supportedBla) // but for
	 * now, when the granularity is FULL, its equivalent of ATTR
	 */
	public InventoryRetrievalFilter()
	{
		
		
	}
	
	
	private void initIncludeAllObjectTypes()
	{
		includeMd = true;
		includeOs = true;
		includeMe = true;
		includeEh = true;
		includeEq = true;
		includePtp = true;
		// Include Object Name?
		includeMdName = true;
		includeOsName = true;
		includeMeName = true;
		includeEhName = true;
		includeEqName = true;
		includePtpName = true;
		// Include Object Attrs
		includeMdAttrs = true;
		includeOsAttrs = true;
		includeMeAttrs = true;
		includeEhAttrs = true;
		includeEqAttrs = true;
		includePtpAttrs = true;
	}

	public void init(SimpleFilterT simpleFilter) throws Exception
	{
		this.simpleFilter = simpleFilter;
		
		List includedObjectType = simpleFilter.getIncludedObjectType();
		if(includedObjectType==null || includedObjectType.isEmpty())
		{
			initIncludeAllObjectTypes();
		    List<NamingAttributesT> list = simpleFilter.getBaseInstance();
		    if(list.size()==1) {
		      NamingAttributesT element = list.get(0);
		      if(element.getEhNm()==null && element.getPtpNm()!=null) {
		    	  includeEh = false;
		    	  includeEq = false;
		      }
		    }
			return;
		}
		for (Iterator iter = includedObjectType.iterator(); iter.hasNext();)
		{
			IncludedObjectType element = (IncludedObjectType) iter.next();
			ObjectAcronymT objectAcronym = element.getObjectType();
			GranularityT granularity = element.getGranularity();

			if (objectAcronym == null || granularity == null) {
				String reason;
				if (objectAcronym != null)
					reason = "Granularity missing for an ObjectType.";
				else if (granularity != null)
					reason = "ObjectType missing for a Granularity.";
				else
					reason = "An IncludedObjectType cannot be empty.  It must contain an ObjectType and a Granularity.";

				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION, reason);
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
			}

			if (objectAcronym == ObjectAcronymT.OS)
			{
				includeOs = true;
				includeOsName = true;
				if (granularity == GranularityT.ATTRS || granularity == GranularityT.FULL)
				{
					includeOsAttrs = true;
				}
			}
			if (objectAcronym == ObjectAcronymT.MD)
			{
				includeMd = true;
				includeMdName = true;
				if (granularity == GranularityT.ATTRS || granularity == GranularityT.FULL)
				{
					includeMdAttrs = true;
				}
			}
			if (objectAcronym == ObjectAcronymT.ME)
			{
				includeMe = true;
				includeMeName = true;
				if (granularity == GranularityT.ATTRS || granularity == GranularityT.FULL)
				{
					includeMeAttrs = true;
				}
			}
			if (objectAcronym == ObjectAcronymT.EH)
			{
				includeEh = true;
				includeEhName = true;
				if (granularity == GranularityT.ATTRS || granularity == GranularityT.FULL)
				{
					includeEhAttrs = true;
				}
			}
			if (objectAcronym == ObjectAcronymT.EQ)
			{
				includeEq = true;
				includeEqName = true;
				if (granularity == GranularityT.ATTRS || granularity == GranularityT.FULL)
				{
					includeEqAttrs = true;
				}
			}
      if (objectAcronym == ObjectAcronymT.PTP)
      {
        includePtp = true;
        includePtpName = true;
        if (granularity == GranularityT.ATTRS || granularity == GranularityT.FULL)
        {
          includePtpAttrs = true;
        }
      }
		}
	}

	public SimpleFilterT getSimpleFilter()
	{
		return simpleFilter;
	}

	public boolean isIncludeEh()
	{
		return includeEh;
	}

	public boolean isIncludeEq()
	{
		return includeEq;
	}

	public boolean isIncludeMd()
	{
		return includeMd;
	}

	public boolean isIncludeMe()
	{
		return includeMe;
	}

	public boolean isIncludeOs()
	{
		return includeOs;
	}

	public boolean isIncludeEhAttrs()
	{
		return includeEhAttrs;
	}

	public boolean isIncludeEhName()
	{
		return includeEhName;
	}

	public boolean isIncludeEqAttrs()
	{
		return includeEqAttrs;
	}

	public boolean isIncludeEqName()
	{
		return includeEqName;
	}

	public boolean isIncludeMdAttrs()
	{
		return includeMdAttrs;
	}

	public boolean isIncludeMdName()
	{
		return includeMdName;
	}

	public boolean isIncludeMeAttrs()
	{
		return includeMeAttrs;
	}

	public boolean isIncludeMeName()
	{
		return includeMeName;
	}

	public boolean isIncludeOsAttrs()
	{
		return includeOsAttrs;
	}

	public boolean isIncludeOsName()
	{
		return includeOsName;
	}

  public boolean isIncludePtp()
 {
  return includePtp;
 }

 public boolean isIncludePtpAttrs()
 {
  return includePtpAttrs;
 }

 public boolean isIncludePtpName()
 {
  return includePtpName;
 }
}
