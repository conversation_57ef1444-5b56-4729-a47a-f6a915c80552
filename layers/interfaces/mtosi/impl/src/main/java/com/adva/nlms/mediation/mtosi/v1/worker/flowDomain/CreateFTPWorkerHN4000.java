/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.common.property.FNMPropertyConstants;
import com.adva.nlms.common.property.FNMPropertyFactory;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.MDRequestFailedException;
import com.adva.nlms.mediation.common.serviceProvisioning.LAGSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN40002BpmeSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000BondingSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000EthernetSProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.UNISPPropertiesHN4000;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.ConfigCtrlImpl;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.hn4000.NetworkElementHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN40002Bpme;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet2BASE_TL;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.Timer;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.PMEPortPair;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import v1.tmf854.FTPVendorExtensionsT;
import v1.tmf854.FloatingTerminationPointT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.TPDataT;
import v1.tmf854.TerminationPointT;
import v1.tmf854ext.adva.FTPCreateDataT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.ws.Holder;
import java.util.Iterator;
import java.util.List;

//import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiSupported;

public class CreateFTPWorkerHN4000 extends CreateFTPWorker
{
	/** Time period for save config **/
  private static final int NUMBER_OF_REPETITIONS = FNMPropertyFactory.getPropertyAsInt(FNMPropertyConstants.NUMBER_OF_REPETITIONS, 60);
  private String ftpName = null;
	private PortHN4000BondingSPProperties bondCPESerialPortAdmin = null;
	private UNISPPropertiesHN4000 newUniProps = null;
			
	public CreateFTPWorkerHN4000(Holder<HeaderT> mtosiHeader, FTPCreateDataT ftpCreateData, NamingAttributesT namingAttributes, NetworkElement ne)
	{
		super(mtosiHeader, ftpCreateData, namingAttributes, ne);
	}

	@Override
  protected void mediate() throws Exception
	{
		NetworkElementHN4000 ne4000 = (NetworkElementHN4000) ne;
		ftpName = ftpCreateData.getName().getFtpNm();
		
		if (ftpName.indexOf("port=ETH") > 0)
		{
			boolean hn400 = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400;
			Object ftp = ne4000.getBondedPort(ftpName);
			if (ftp != null)
			{
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_CAPACITY_EXCEEDED, "Specified FTP already exists.");
			}
			JAXBElement<FTPVendorExtensionsT> extensionsJAXB = ftpCreateData.getVendorExtensions();
			if (extensionsJAXB == null)
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
						MtosiErrorConstants.VENDOR_EXTENSIONS_MISSING);
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
			
			FTPVendorExtensionsT vendorExtensions = extensionsJAXB.getValue();
			List<TPDataT> tpDataList = MtosiUtils.getTpsToModify(vendorExtensions);
			PortHN4000BondingSPProperties newFTP = MtosiTPMediator.mtosiCreateFTPToNMSPropertiesHN4000Bond(ftpCreateData, tpDataList, hn400);
			bondCPESerialPortAdmin = MtosiTPMediator.mtosiCreateFTPToNMSPropertiesHN4000BondCPESerial(ftpCreateData);
			TPDataT tpDataFTP = MtosiTPMediator.mtosiCreateFTPToTPDataT(ftpCreateData);
			newUniProps = MtosiTPMediator.mtosiTPDataTToHNUniProperties(tpDataFTP, ne.getMTOSIWorker().getNetworkElementTypeForMTOSI(), false);
				
			List<PMEPortPair> pmePairList = MtosiTPMediator.mtosiTPDataListToPMEPortPair(tpDataList, hn400);
			
			transact(ne4000, newFTP, pmePairList);
		}
		else if (ftpName.indexOf("port=LAG") > 0)
		{
			Object ftp = ne4000.getMTOSIWorker().getFTP(ftpName);
			if (ftp != null)
			{
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_CAPACITY_EXCEEDED, "Specified FTP already exists.");
			}
			LAGSPPropertiesHN4000 newFTP = MtosiTPMediator.mtosiCreateFTPToNMSPropertiesHN4000(ftpCreateData);
		    transact(ne, newFTP);
		}

		
	}

	protected void transact(NetworkElementHN4000 ne4000, PortHN4000BondingSPProperties props, List<PMEPortPair> pmePairList) throws ObjectInUseException, NetTransactionException,
			SPValidationException, MDRequestFailedException, SNMPCommFailure, ProcessingFailureException
	{
		NetworkElement locks[] = new NetworkElement[]
		{ ne4000 };
		Timer tCreate = new Timer("Creating bonding....");
		tCreate.start(1) ;
		Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "CreateFTPWorker");
		try
		{
			logSecurity(ne4000, SystemAction.AddNetwork, "ftpNm=" + ftpCreateData.getName().getFtpNm());
			ne4000.createBonding(props);
			tCreate.start(2);
			PortHN4000Ethernet2BASE_TL newBonding = ne4000.getBondedPort(ftpName);
			if (newBonding == null || newBonding.getPortSPProperties() == null) {
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, "Unable to create FTP.");
			}
			if (newUniProps != null) {
				newBonding.createUni(newUniProps);
			}
			tCreate.stop(2);
			tCreate.start(3);
			for (Iterator iterator = pmePairList.iterator(); iterator.hasNext();)
			{
				PMEPortPair nextPair = (PMEPortPair) iterator.next();
				PortHN40002Bpme nextPort=nextPair.getPort();
				PortHN40002BpmeSPProperties nextProps = nextPair.getProps();
				nextPort.setSettings(nextProps);
			}
			tCreate.stop(3);
			// if CPESerialPortAdministration included, check for CPE and once finished, can set value
			if(bondCPESerialPortAdmin!=null)
			{
				String cpeHostName = props.get(PortHN4000BondingSPProperties.VS.CpeHostName);
				if(cpeHostName!=null)
				{
					bondCPESerialPortAdmin.set(PortHN4000EthernetSProperties.VI.IfIndex, newBonding.getPortSPProperties().get(PortHN4000EthernetSProperties.VI.IfIndex));
					// now wait until the CPE is created, and then continue, else 
					NetworkElement cpeNE = null;
					Timer t = new Timer("Waiting for CPE Notification.");
			    	t.start(1) ;
					
					int count = 0;
					do
					{
						cpeNE = ConfigCtrlImpl.get().getHandlers().getNeHdlr().getNEByName(cpeHostName);
						if(cpeNE!=null && cpeNE.isDiscovered()) break;
						count++;
						try {
					          Thread.sleep(1000);

					        } catch (InterruptedException e) {
					        	throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_CAPACITY_EXCEEDED, "Interrupted waiting for notification that CPE was created.", e);
					        }
					}
					while (count<=NUMBER_OF_REPETITIONS);
					if(cpeNE==null)
					{
						throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_CAPACITY_EXCEEDED, "Timeout waiting for CPE created notification.");
					}
					else if (!cpeNE.isDiscovered()) 
					{
						throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_CAPACITY_EXCEEDED, "Timeout waiting for CPE discovery.");
						
					} else 
					{
						
						t.stop(1) ;
						t.start(2);
				    	
						newBonding.setSettings(bondCPESerialPortAdmin);
						t.stop(2);
						t.report();
					}
					
				}
				
			}
			
			NetTransactionManager.commitNetTransaction(id);
			tCreate.stop(1);
			tCreate.report();
//      ne.logSROperation(SROperationState.FTP_CREATION_SUCCESS, ftpName);
		}
		catch (NetTransactionException e)
		{
			ne.logSROperation(SROperationState.FTP_CREATION_FAILURE, ftpName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (SPValidationException e)
		{
			ne.logSROperation(SROperationState.FTP_CREATION_FAILURE, ftpName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (MDRequestFailedException e)
		{
			ne.logSROperation(SROperationState.FTP_CREATION_FAILURE, ftpName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (SNMPCommFailure e)
		{
			ne.logSROperation(SROperationState.FTP_CREATION_FAILURE, ftpName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (ProcessingFailureException e)
		{
			ne.logSROperation(SROperationState.FTP_CREATION_FAILURE, ftpName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		finally
		{
			NetTransactionManager.ensureEnd(id);
		}
	}
	
	@Override
  protected void response() throws Exception {
		final PortHN4000Ethernet2BASE_TL bondedPort = ((NetworkElementHN4000)ne).getBondedPort(ftpName);
		//final FTP ftpMO = ManagedElementFactory.getFtp(namingAttributes);
	    if (bondedPort != null)
	    {
	      ObjectFactory objectFactory = new ObjectFactory();
	      FloatingTerminationPointT ftp = new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(bondedPort).toMtosiFTP();
//	      FloatingTerminationPointT ftp = bondedPort.getMtosiTranslator().toMtosiFTP();
	      TerminationPointT tp = objectFactory.createTerminationPointT();
	      tp.setFtp(ftp);
	      response.setTheFTP(tp);
	    }
	  }
}
