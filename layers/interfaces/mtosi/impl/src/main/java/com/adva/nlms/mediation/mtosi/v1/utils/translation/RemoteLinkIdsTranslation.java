/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

/**
 * Created by IntelliJ IDEA. User: Lukasz Date: 2007-05-30 Time: 13:14:09 To change this template use File | Settings |
 * File Templates.
 */
public enum RemoteLinkIdsTranslation{
  NOT_APPLICABLE (1, "n/a");

  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private RemoteLinkIdsTranslation (final int mibValue, final String mtosiString)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int mibValue)
  {
    final StringBuilder resultBuilder = new StringBuilder();
    int bitField = 0x80;//10000000
    for(int i = 1; i <= 5; i++)
    {
     if ((mibValue & bitField) != 0)
     {
       if(resultBuilder.length() > 0)
         resultBuilder.append(",");
       resultBuilder.append(i);

     }
     bitField >>= 1;
    }
    return resultBuilder.toString();
  }
  /**
   * Returns the string representation used in MTOSI layer.
   * @param mtosiString  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static int getMIBValue (final String mtosiString)
  {
	if(mtosiString==null || mtosiString.length()==0) return 0;
	int result= 0; 
    String mtosiBitsArray [] = mtosiString.split(",");
    for (int i = 0; i < mtosiBitsArray.length; i++ )
    {
      try
      {
    	  int num = Integer.parseInt(mtosiBitsArray[i]);
    	  if (num < 1 || num > 5) {
    		  return -1;
    	  }
    	  result += Math.pow(2,8 - num);
      }
      catch(NumberFormatException ex)
      {
        return -1;
      }
    }
    return result;
  }
}