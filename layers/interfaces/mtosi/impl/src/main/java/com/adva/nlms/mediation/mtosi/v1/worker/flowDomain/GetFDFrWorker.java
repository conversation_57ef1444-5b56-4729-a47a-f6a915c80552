/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;


import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import jakarta.xml.ws.Holder;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.FlowDomainFragmentT;
import v1.tmf854ext.adva.GetFDFrResponseT;
import v1.tmf854ext.adva.GetFDFrT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;


public class GetFDFrWorker extends AbstractMtosiWorker
{
	protected GetFDFrT mtosiBody;
	protected GetFDFrResponseT response = new GetFDFrResponseT();
	protected NamingAttributesT fdfrName;
	protected FlowDomainFragmentT flowDomainFragmentT;

	//Constructor
	public GetFDFrWorker (final GetFDFrT mtosiBody, final Holder<HeaderT> mtosiHeader)
	{
		super(mtosiHeader, "getFDFr", "getFDFr", "getFDFrResponse");
		this.mtosiBody = mtosiBody;
	}
	
	@Override
  protected void parse() throws Exception {
	  if ((fdfrName = mtosiBody.getFdfrName()) == null) {
	    throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
	        MtosiErrorConstants.INVALID_FILTER);
	  }
	  
	  if (!NamingTranslationFactory.isManagementDomain(fdfrName)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }
	  
	  if (!fdfrName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MD_NOT_FOUND);
    }
	}

	@Override
	protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
		//getFDFr is not NE Specific...If you can do it for one NE, you can do it for all...
	}

	@Override
  protected void mediate() throws Exception {
	  if((flowDomainFragmentT = ManagedElementFactory.getFDFr(fdfrName))==null) {
	    throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
	        MtosiErrorConstants.FDFR_NOT_FOUND);
	  }
	}

	@Override
  protected void response() throws Exception {
	  response.setFdfr(flowDomainFragmentT);
	}

	@Override
  public GetFDFrResponseT getSuccessResponse()
	{
		if (response == null)
			return null;

		response.setTmf854Version(MtosiConstants.VERSION);
		return response;
	}
}
