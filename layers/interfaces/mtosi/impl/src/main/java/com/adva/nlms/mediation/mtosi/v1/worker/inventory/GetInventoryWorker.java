/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.inventory;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.InventoryRetrievalFilter;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import v1.tmf854.GetInventoryResponseT;
import v1.tmf854.GetInventoryT;
import v1.tmf854.HeaderT;
import v1.tmf854.InventoryDataT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.SimpleFilterT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.List;

public class GetInventoryWorker extends AbstractMtosiWorker {
  protected GetInventoryT mtosiBody;
  protected GetInventoryResponseT response = new GetInventoryResponseT();
  protected InventoryRetrievalFilter filter = new InventoryRetrievalFilter();
  protected InventoryDataT inventory;
  private MtosiAddress mtosiAddr;

  public GetInventoryWorker(GetInventoryT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getInventory", "getInventory", "getInventoryResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    SimpleFilterT simpleFilter = mtosiBody.getFilter();
    if(simpleFilter==null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              "Filter must be specified.");
    }

    filter.init(simpleFilter);

    List<NamingAttributesT> list = filter.getSimpleFilter().getBaseInstance();
    if(list.size()!=1) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              "One Base Instance must be included in a Filter.");
    }

    NamingAttributesT element = list.get(0);
    mtosiAddr = new MtosiAddress(element);
    if(element.getMdNm()==null || element.getMeNm()==null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              "The Base Instance must include at least MdNm and MeNm.");
    }

    if(element.getCtpNm() != null || element.getFdfrNm()!= null ||
            element.getFtpNm()!= null || element.getTcpNm()!= null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The Base Instance refers to an entity not supported for getInventory.");
    }

    if(element.getEhNm()==null && element.getEqNm()!=null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              "Equipment Holder name is missing.");
    }

    if(!MtosiUtils.existsEntity(mtosiAddr)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The Base Instance refers to an entity that does not exist.");
    }
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(mtosiAddr.getNE().getDefaultNetworkElementTypeString());
  }
  
  @Override
  protected void mediate() throws Exception {
    if ((inventory = InventoryFactory.getInventory(filter, getMtosiHeader())) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The requested entity was not found.");
    }
  }

  @Override
  protected void response() throws Exception {
    response.setInventoryData(inventory);
  }

  @Override
  public GetInventoryResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
