/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm;

import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.common.NEUtils;
import com.adva.nlms.mediation.config.NetworkElement;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.PhysicalTerminationPointT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.common.serviceProvisioning.ACCPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.NETPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.WANPortSPProperties;
import com.adva.nlms.mediation.config.f3.entity.port.acc.MTOSIPortF3Acc;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMPortModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSecondaryStateTranslation;

/**
 * This class is an FSP 150 CM access port MTOSI Translator.
 */
public class PortFSP150CMAccTranslator extends PortFSP150CMTranslator {
  protected MTOSIPortF3Acc port;

  public PortFSP150CMAccTranslator (MTOSIPortF3Acc port) {
    super(port);
    this.port = port;
  }

  @Override
  public PhysicalTerminationPointT toMtosiPTP() throws ProcessingFailureException {
    PhysicalTerminationPointT terminationPoint = toMtosiPTP(true);
    NetworkElement ne = port.getNE();
    int neType = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI();

    // set the specific values
    LayeredParametersListT layeredParametersListT = terminationPoint.getTransmissionParams().getValue();
    ACCPortSPPropertiesFSP150CM props = (ACCPortSPPropertiesFSP150CM) port.getPortSPProperties();

    if (props.get(NETPortSPPropertiesFSP150CM.VI.MediaType) == MIBFSP150CM.Facility.EthernetNetPortTable.MEDIA_TYPE_FIBER && !NEUtils.isXG210(neType)) {
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL,
              LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.INACTIVE_PARAM);
    }

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.INTERFACE_TYPE_PARAM, "UNI");

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.PORT_TP_ROLE_STATE_PARAM,
            (props.get(WANPortSPProperties.VI.IfAdminStatus) == CMAdministrationControlTranslation.UNASSIGNED.getMIBValue() ||
                    props.get(NETPortSPPropertiesFSP150CM.VS.SecondaryState).contains(CMSecondaryStateTranslation.UNASSIGNED.getMtosiString())) ?
                    "unassigned" : "fdEdge");

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
            LayeredParams.LrEthernet.MAX_NUM_FDFRS_PARAM, "1");

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
            LayeredParams.LrPropAdvaEthernet.PORT_MODE_PARAM, CMPortModeTranslation.getMtosiString(props.get(ACCPortSPPropertiesFSP150CM.VI.Mode), props.get(ACCPortSPPropertiesFSP150CM.VI.SvcType)));

    return terminationPoint;
  }
}
