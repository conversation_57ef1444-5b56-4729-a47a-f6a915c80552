/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.equipment;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.EquipmentFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import v1.tmf854.GetAllEquipmentNamesT;
import v1.tmf854.GetAllObjectNamesResponseT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.NamingAttributesT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

/**
 * main class for the MTOSI operation:  g e t A l l E q u i p m e n t N a m e s
 */
public class GetAllEquipmentNamesWorker extends AbstractMtosiWorker {
  protected GetAllEquipmentNamesT mtosiBody;
  protected GetAllObjectNamesResponseT response = new GetAllObjectNamesResponseT();
  protected NamingAttributesListT list;
  protected NamingAttributesT naming;
  protected MtosiAddress mtosiAddr;

  public GetAllEquipmentNamesWorker(GetAllEquipmentNamesT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getAllEquipmentNames", "getAllEquipmentNames", "getAllEquipmentNamesResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    naming = mtosiBody.getMeOrHolderName();
    mtosiAddr = new MtosiAddress(naming);

    if (naming == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              MtosiErrorConstants.INVALID_FILTER);
    }

    if(naming.getMdNm()==null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if(naming.getMeNm()==null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              MtosiErrorConstants.ME_NAME_MISSING);
    }
    
    if(!MtosiUtils.existsEntity(mtosiAddr)) {
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
                "The specified entity does not exist.");
      }
  }
  
  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
  	validator.validate(mtosiAddr.getNE().getDefaultNetworkElementTypeString());
  }

  @Override
  protected void mediate() throws Exception {
    list = EquipmentFactory.getAllEquipmentNames(mtosiAddr);
  }

  @Override
  protected void response() throws Exception {
    response.setNames(list);
  }

  @Override
  public GetAllObjectNamesResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }

}
