/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi;

import com.adva.nlms.mediation.common.serviceProvisioning.ACCPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.HnTrafficGenProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ProtectionGroupF3SPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.NETPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000EthernetSProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ServiceSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ServiceSPPropertiesFSP150CP;
import com.adva.nlms.mediation.common.serviceProvisioning.WANPortSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.WANPortSPPropertiesFSP150CP;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrEndIDs;
import com.adva.nlms.mediation.config.f3.entity.port.acc.MTOSIPortF3Acc;
import com.adva.nlms.mediation.config.f3.entity.port.net.MTOSIPortF3Net;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FTPFSP150CM;
import com.adva.nlms.mediation.config.fsp150cp_mx.PortFSP150CP_MXAccess;
import com.adva.nlms.mediation.config.fsp150cp_mx.PortFSP150CP_MXNetwork;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMLoopbackStatusSwapSADATranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMLoopbackStatusTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSwitchActionTranslation;
import com.adva.nlms.common.snmp.hn.HNLoopbackStatusTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.LoopbackStatusSwapSADATranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.NetLoopbackStatusTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.WANLoopbackStatusTypeTranslation;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NVSListT;
import v1.tmf854.NameAndStringValueT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854ext.adva.FDFrCreateDataT;
import v1.tmf854ext.adva.FDFrModifyDataT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import java.util.Collection;
import java.util.List;

public class MtosiFDFrMediator
{
	public static FDFrSPProperties mtosiCreateFDFRToFDFRSPProperties(FDFrCreateDataT createData,
			Collection<FDFrEndIDs> aEnds, Collection<FDFrEndIDs> zEnds) throws ProcessingFailureException
	{
		String userLabel = null;
		String name = createData.getName().getFdfrNm();
		JAXBElement<String> userJAX = createData.getUserLabel();
		if(userJAX!=null)
		{
			userLabel = userJAX.getValue();
		}
				
		if (name == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
					ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.FDFR_NAME_MISSING);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		String fdfrType = null;
				
		JAXBElement<String> typeJAX = createData.getFdfrType();
		if(typeJAX!=null)
		{
			fdfrType = typeJAX.getValue();
			if(fdfrType==null)
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
						ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageMandatory(MtosiConstants.FDFR_TYPE_PARAM));
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
			if(!fdfrType.equals(MtosiConstants.FDFRT_POINT_TO_POINT))
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
						ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageIllegal(MtosiConstants.FDFR_TYPE_PARAM));
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
			
		}
		else
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
					ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageMandatory(MtosiConstants.FDFR_TYPE_PARAM));
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		return new FDFrSPProperties(name, userLabel, aEnds, zEnds);
	}
	
	public static FDFrSPPropertiesHN4000 mtosiCreateFDFRToFDFRSPProperties4000(FDFrCreateDataT createData,
			Collection<FDFrEndIDs> aEnds, Collection<FDFrEndIDs> zEnds) throws ProcessingFailureException
	{
		String userLabel = null;
		String name = createData.getName().getFdfrNm();
		String fdfrType = null;
		JAXBElement<String> userJAX = createData.getUserLabel();
		if(userJAX!=null)
		{
			userLabel = userJAX.getValue();
		}
				
		if (name == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
					ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.FDFR_NAME_MISSING);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		
		JAXBElement<String> typeJAX = createData.getFdfrType();
		if(typeJAX!=null)
		{
			fdfrType = typeJAX.getValue();
			if(fdfrType==null)
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
						ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageMandatory(MtosiConstants.FDFR_TYPE_PARAM));
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
			if(!fdfrType.equals(MtosiConstants.FDFRT_POINT_TO_POINT))
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
						ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageIllegal(MtosiConstants.FDFR_TYPE_PARAM));
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
			
		}
		else
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
					ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageMandatory(MtosiConstants.FDFR_TYPE_PARAM));
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		
		FDFrSPPropertiesHN4000 props = new FDFrSPPropertiesHN4000();
		props.set(FDFrSPProperties.VC.AEnds, aEnds);
		props.set(FDFrSPProperties.VC.ZEnds, zEnds);
		props.set(FDFrSPProperties.VS.UserLabel, userLabel);
		props.set(FDFrSPProperties.VS.FDFrName, name);
		
		JAXBElement<LayeredParametersListT> layersJ = createData.getTransmissionParams();
		if (layersJ == null)
		{
			return props;
		}
		LayeredParametersListT transmissionParams = layersJ.getValue();
				
		if (transmissionParams == null)
		{
			return props;
		}
		
		String administrationControl = LayeredParameterUtils.getLayeredParameter(transmissionParams,
				LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasEthernet.ADMINISTRATION_CONTROL_PARAM);
		if (administrationControl != null)
		{
			Integer administrationControlValue = MtosiUtils.getMIBValue(AdministrationControlTranslation.NOT_APPLICABLE, administrationControl);
			if (administrationControlValue == 4)
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
						ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageIllegal(LayeredParams.PropHatterasEthernet.ADMINISTRATION_CONTROL_PARAM));
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
			props.set(FDFrSPProperties.VI.AdministrativeState, administrationControlValue);
		}
		
		String evcType = LayeredParameterUtils.getLayeredParameter(transmissionParams,
				LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasEthernet.EVC_TYPE);
		if (evcType != null)
		{
			if(!evcType.equals(MtosiConstants.EVC_TYPE_ELINE))
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
						ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageIllegal(LayeredParams.PropHatterasEthernet.EVC_TYPE));
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
			
		}
		
		return props;
	}

	public static FDFrSPProperties mtosiModifyFDFRToFDFRSPProperties(String fdfrName, FDFrModifyDataT modifyData)
			throws ProcessingFailureException
	{
		if (modifyData == null)
			return null;
		String userLabel = null;
		if (fdfrName == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
					ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.FDFR_NAME_MISSING);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		FDFrSPProperties props = new FDFrSPProperties(fdfrName);
		JAXBElement<String> userJ = modifyData.getUserLabel();
		if (userJ != null)
		{
			userLabel = userJ.getValue();
			if (userLabel != null)
			{
				props.set(FDFrSPProperties.VS.UserLabel, userLabel);
				return props;
			}
		}
		return null;
	}
	
	public static FDFrSPPropertiesHN4000 mtosiModifyFDFRToFDFRSPProperties4000(FDFrSPPropertiesHN4000 currentProps, FDFrModifyDataT modifyData)
	throws ProcessingFailureException
	{
		if (modifyData == null)
			return null;
		String userLabel = null;
		if (currentProps == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
					ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.FDFR_NAME_MISSING);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		FDFrSPPropertiesHN4000 props = new FDFrSPPropertiesHN4000(currentProps.get(FDFrSPPropertiesHN4000.VL.EvcID), currentProps.get(FDFrSPProperties.VS.FDFrName));
		JAXBElement<String> userJ = modifyData.getUserLabel();
		if (userJ != null)
		{
			userLabel = userJ.getValue();
			if (userLabel != null)
			{
				props.set(FDFrSPProperties.VS.UserLabel, userLabel);
			}
		}
		
		JAXBElement<LayeredParametersListT> layersJ = modifyData.getTransmissionParams();
		if (layersJ == null)
		{
			return props;
		}
		LayeredParametersListT transmissionParams = layersJ.getValue();
		if (transmissionParams == null)
		{
			return props;
		}
		
		String administrationControl = LayeredParameterUtils.getLayeredParameter(transmissionParams,
				LayeredParams.PROP_HATTERAS_Ethernet, LayeredParams.PropHatterasEthernet.ADMINISTRATION_CONTROL_PARAM);
		if (administrationControl != null)
		{
			Integer administrationControlValue = MtosiUtils.getMIBValue(AdministrationControlTranslation.NOT_APPLICABLE, administrationControl);
			if (administrationControlValue == 4)
			{
				pfeIllegal(LayeredParams.LrDsrGigabitAndFastEthernet.ADMINISTRATION_CONTROL_PARAM);
			}
			props.set(FDFrSPProperties.VI.AdministrativeState, administrationControlValue);
		}
		return props;
	}

	public static ACCPortSPPropertiesFSP150CM mtosiLoopbackParametersToCMACCProperties(String type,
			NVSListT loopbackParameters, MTOSIPortF3Acc lanPort) throws Exception
	{
		if (type == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
					ExceptionUtils.EXCPT_INVALID_INPUT, "LoopbackType parameter is mandatory.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
				
		ACCPortSPPropertiesFSP150CM newProps = new ACCPortSPPropertiesFSP150CM();
		ACCPortSPPropertiesFSP150CM props = (ACCPortSPPropertiesFSP150CM)lanPort.getPortSPProperties();
		newProps.set(NETPortSPPropertiesFSP150CM.VI.ShelfIndex, props.get(NETPortSPPropertiesFSP150CM.VI.ShelfIndex));
		newProps.set(WANPortSPProperties.VI.Index, props.get(WANPortSPProperties.VI.Index));
		newProps.set(NETPortSPPropertiesFSP150CM.VI.SlotIndex, props.get(NETPortSPPropertiesFSP150CM.VI.SlotIndex));
		newProps.set(WANPortSPProperties.VI.IfAdminStatus, props.get(WANPortSPProperties.VI.IfAdminStatus));
		newProps.set(ACCPortSPPropertiesFSP150CM.VI.SvcType, props.get(ACCPortSPPropertiesFSP150CM.VI.SvcType));
		/*if (type == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
					ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
							.getMessageMandatory(LayeredParams.LrPropAdvaEthernet.LOOPBACK_TYPE_PARAM));
			ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
			throw pfe;
		}*/
		// Valid for CM ACC are Terminal 7, Facility 4,	TerminalTimed 8, FacilityTimed 5, RemoteEFMOAM 2, RemoteEFMOAMTimed 3
		
		Integer typeInt = MtosiUtils.getMIBValue(CMLoopbackStatusTypeTranslation.NOT_APPLICABLE, type);
		if (!(typeInt == 2 || typeInt == 3 || typeInt == 4 || typeInt == 5 || typeInt == 7|| typeInt == 8))
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
					ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
							.getMessageIllegal(LayeredParams.LrPropAdvaEthernet.LOOPBACK_TYPE_PARAM));
			ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
			throw pfe;
		}
		
		boolean isRemoteEFMOAM = false;
		if(typeInt==2)
		{
			isRemoteEFMOAM = true;
		}
		newProps.set(WANPortSPProperties.VI.PortLoopback, typeInt);
		String time = LayeredParameterUtils.findParameter(loopbackParameters,
				LayeredParams.LrPropAdvaEthernet.LOOPBACK_TIME_PARAM);
		if (time != null)
		{
			if (typeInt != 3 && typeInt != 5 && typeInt != 8)
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
						ExceptionUtils.EXCPT_INVALID_INPUT, LayeredParams.LrPropAdvaEthernet.LOOPBACK_TIME_PARAM
								+ " is only applicable for timed loopbacks.");
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
			}
			if (!MtosiUtils.isInteger(time))
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
						ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
								.getMessageIllegal(LayeredParams.LrPropAdvaEthernet.LOOPBACK_TIME_PARAM));
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
			}
			Integer timedInteger = Integer.valueOf(time);
			newProps.set(NETPortSPPropertiesFSP150CM.VI.LoopbackTime, timedInteger);
		}
		else
		{
			if (typeInt == 3 || typeInt == 5 || typeInt == 8)
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
						ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
								.getMessageMandatory(LayeredParams.LrPropAdvaEthernet.LOOPBACK_TIME_PARAM));
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
			}
		}
		String swapSada = LayeredParameterUtils.findParameter(loopbackParameters,
				LayeredParams.LrPropAdvaEthernet.LOOPBACK_SWAP_SADA_PARAM);
		if (swapSada != null)
		{
			Integer swapSadaValue = CMLoopbackStatusSwapSADATranslation.getMibValue(swapSada);
			if(isRemoteEFMOAM  && ! (CMLoopbackStatusSwapSADATranslation.NO_SWAP_SADA.getMIBValue() == swapSadaValue))
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
						ExceptionUtils.EXCPT_INVALID_INPUT, LayeredParams.LrPropAdvaEthernet.LOOPBACK_SWAP_SADA_PARAM + "is not applicable for RemoteEFMOAM loopback.");
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
			}
			
			if (swapSadaValue == CMLoopbackStatusSwapSADATranslation.NOT_APPLICABLE.getMIBValue())
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
						ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
								.getMessageIllegal(LayeredParams.LrPropAdvaEthernet.LOOPBACK_SWAP_SADA_PARAM));
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
			}
			
			newProps.set(NETPortSPPropertiesFSP150CM.VI.LoopbackSwapSADA, swapSadaValue);
		}
		return newProps;
	}
	
	public static NETPortSPPropertiesFSP150CM mtosiLoopbackParametersToCMNETProperties(String type,
			NVSListT loopbackParameters, MTOSIPortF3Net netPort) throws Exception
	{
		if (type == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
					ExceptionUtils.EXCPT_INVALID_INPUT, "LoopbackType parameter is mandatory.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
				
		NETPortSPPropertiesFSP150CM newProps = new NETPortSPPropertiesFSP150CM();
		NETPortSPPropertiesFSP150CM props = netPort.getPortSPProperties();
		newProps.set(NETPortSPPropertiesFSP150CM.VI.ShelfIndex, props.get(NETPortSPPropertiesFSP150CM.VI.ShelfIndex));
		newProps.set(WANPortSPProperties.VI.Index, props.get(WANPortSPProperties.VI.Index));
		newProps.set(NETPortSPPropertiesFSP150CM.VI.SlotIndex, props.get(NETPortSPPropertiesFSP150CM.VI.SlotIndex));
		newProps.set(WANPortSPProperties.VI.IfAdminStatus, props.get(WANPortSPProperties.VI.IfAdminStatus));
		
		// Valid for CM NET are Terminal 7, Facility 4,	TerminalTimed 8, FacilityTimed 5, RemoteEFMOAM 2, RemoteEFMOAMTimed 3
	
		Integer typeInt = MtosiUtils.getMIBValue(CMLoopbackStatusTypeTranslation.NOT_APPLICABLE, type);
		if (!(typeInt == 2 || typeInt == 3 || typeInt == 4 || typeInt == 5 || typeInt == 7|| typeInt == 8))
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
					ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
							.getMessageIllegal(LayeredParams.LrPropAdvaEthernet.LOOPBACK_TYPE_PARAM));
			ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
			throw pfe;
		}
		
		boolean isRemoteEFMOAM = false;
		if(typeInt==2)
		{
			isRemoteEFMOAM = true;
		}
		newProps.set(WANPortSPProperties.VI.PortLoopback, typeInt);
		String time = LayeredParameterUtils.findParameter(loopbackParameters,
				LayeredParams.LrPropAdvaEthernet.LOOPBACK_TIME_PARAM);
		if (time != null)
		{
			if (typeInt != 3 && typeInt != 5 && typeInt != 8)
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
						ExceptionUtils.EXCPT_INVALID_INPUT, LayeredParams.LrPropAdvaEthernet.LOOPBACK_TIME_PARAM
								+ " is only applicable for timed loopbacks.");
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
			}
			if (!MtosiUtils.isInteger(time))
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
						ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
								.getMessageIllegal(LayeredParams.LrPropAdvaEthernet.LOOPBACK_TIME_PARAM));
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
			}
			Integer timedInteger = Integer.valueOf(time);
			newProps.set(NETPortSPPropertiesFSP150CM.VI.LoopbackTime, timedInteger);
		}
		else
		{
			if (typeInt == 3 || typeInt == 5 || typeInt == 8)
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
						ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
								.getMessageMandatory(LayeredParams.LrPropAdvaEthernet.LOOPBACK_TIME_PARAM));
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
			}
		}
		String swapSada = LayeredParameterUtils.findParameter(loopbackParameters,
				LayeredParams.LrPropAdvaEthernet.LOOPBACK_SWAP_SADA_PARAM);
		if (swapSada != null)
		{
			Integer swapSadaValue = CMLoopbackStatusSwapSADATranslation.getMibValue( swapSada);
			if(isRemoteEFMOAM && ! (CMLoopbackStatusSwapSADATranslation.NO_SWAP_SADA.getMIBValue() == swapSadaValue))
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
						ExceptionUtils.EXCPT_INVALID_INPUT, LayeredParams.LrPropAdvaEthernet.LOOPBACK_SWAP_SADA_PARAM + "is not applicable for RemoteEFMOAM loopback.");
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
			}
			
			if (swapSadaValue == CMLoopbackStatusSwapSADATranslation.NOT_APPLICABLE.getMIBValue())
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
						ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
								.getMessageIllegal(LayeredParams.LrPropAdvaEthernet.LOOPBACK_SWAP_SADA_PARAM));
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
			}
			
			newProps.set(NETPortSPPropertiesFSP150CM.VI.LoopbackSwapSADA, swapSadaValue);
		}
		return newProps;
	}

  /**
   * set properties for ETH2Base TL port
   * @param type loopback type
   * @return port properties
   * @throws Exception
   */
  public static PortHN4000EthernetSProperties mtosiLoopbackParametersToHN4kEth_Properties(String type, 
		  NVSListT loopbackParameters, final PortHN4000Ethernet port) throws Exception  {
	    if (type == null){
	      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
						ExceptionUtils.EXCPT_INVALID_INPUT, "LoopbackType parameter is mandatory.");
				throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
	    PortHN4000EthernetSProperties props = port.getPortSPProperties();
	    final int typeInt = HNLoopbackStatusTypeTranslation.fromLabel(type);
	    final PortHN4000EthernetSProperties newProps = new PortHN4000EthernetSProperties();
	    newProps.set(PortHN4000EthernetSProperties.VI.LoopbackType, typeInt);
	    
	    if (typeInt == HNLoopbackStatusTypeTranslation.RemoteEFMOAM.getMIBValue()) {
	    	int loopbackStatus = props.get(PortHN4000EthernetSProperties.VI.LoopbackStatus);
		    if( loopbackStatus == HNLoopbackStatusTypeTranslation.RemoteEFMOAM.getMIBValue() ||
		    	loopbackStatus == HNLoopbackStatusTypeTranslation.RemoteEFMOAMWithFacilityMAC.getMIBValue() 
				    		)
		    {
		      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
		              ExceptionUtils.EXCPT_INVALID_INPUT, "Loopback is already set.");
		      throw new ProcessingFailureException(pfet.getReason(), pfet);
		    }
		    //discard or pass traffic
		    String mode = LayeredParameterUtils.findParameter(loopbackParameters,
						LayeredParams.LrPropAdvaEthernet.LOCAL_LOOPBACK_MODE);
		    newProps.set(PortHN4000EthernetSProperties.VI.LoopbackMode, MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE, mode));
		    
			
		} else if (typeInt == HNLoopbackStatusTypeTranslation.FacilityMAC.getMIBValue() ) {
		    //physical Address
		    String physAddr = LayeredParameterUtils.findParameter(loopbackParameters,
						LayeredParams.LrPropAdvaEthernet.LOOPBACK_PHYS_ADDRESS_PARAM);
		    if (physAddr != null) {
		    	if (MtosiUtils.isValidMacAddress(physAddr)) {
				    newProps.set(PortHN4000EthernetSProperties.VS.LoopbackPhysAddress, physAddr);	    

		    	} else {
		    		pfeIllegal(LayeredParams.LrPropAdvaEthernet.LOOPBACK_PHYS_ADDRESS_PARAM);
		    	}
		    }
		} else  {
			pfeIllegal(LayeredParams.LrPropAdvaEthernet.LOOPBACK_TYPE_PARAM);
		}
    return newProps;
  }
	public static ProtectionGroupF3SPProperties mtosiProtectionCommandToCMFTPProperties(String type,
			FTPFSP150CM ftp) throws Exception
	{
		ProtectionGroupF3SPProperties newProps = new ProtectionGroupF3SPProperties();
		ProtectionGroupF3SPProperties props =  ftp.getFTPSPProperties();
		newProps.set(ProtectionGroupF3SPProperties.VI.Index, props.get(
            ProtectionGroupF3SPProperties.VI.Index));
		
		
		Integer typeInt = CMSwitchActionTranslation.getMIBValue(type);
		if (typeInt == CMSwitchActionTranslation.NONE.getMIBValue() ||
			typeInt == CMSwitchActionTranslation.NOT_APPLICABLE.getMIBValue())
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
					ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
							.getMessageIllegal("switchAction"));
			ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
			throw pfe;
		}
		newProps.set(ProtectionGroupF3SPProperties.VI.SwitchAction, typeInt);
		
		return newProps;
		
	}

  public static WANPortSPPropertiesFSP150CP mtosiLoopbackParametersToCPWANProperties(String type, PortFSP150CP_MXNetwork wanPort) throws Exception
	{
		if (type == null)
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
					ExceptionUtils.EXCPT_INVALID_INPUT, "LoopbackType parameter is mandatory.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		WANPortSPPropertiesFSP150CP newProps = new WANPortSPPropertiesFSP150CP();
		WANPortSPPropertiesFSP150CP props = wanPort.getWANPortSPProperties();
		newProps.set(WANPortSPProperties.VI.Index, props.get(WANPortSPProperties.VI.Index));
		// Valid for CP WAN is RemoteEFMOAM
		Integer typeInt = NetLoopbackStatusTypeTranslation.getMIBValue(type);
		if (!(typeInt == 3))
		{
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
					ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
							.getMessageIllegal(LayeredParams.LrPropAdvaEthernet.LOOPBACK_TYPE_PARAM));
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		newProps.set(WANPortSPProperties.VI.PortLoopback, typeInt);
		return newProps;
	}
	
	public static ServiceSPPropertiesFSP150CP mtosiLoopbackParametersToCPServiceProperties(String type,
                                                                                         NVSListT loopbackParameters,
                                                                                         PortFSP150CP_MXAccess wanPort)
          throws Exception
  {
    if (type == null)
    {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
              ExceptionUtils.EXCPT_INVALID_INPUT, "LoopbackType parameter is mandatory.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    ServiceSPPropertiesFSP150CP newProps = new ServiceSPPropertiesFSP150CP();
    ServiceSPPropertiesFSP150CP props = wanPort.getServicePortSPProperties();
    newProps.set(ServiceSPProperties.VI.SvcIndex, props.get(ServiceSPProperties.VI.SvcIndex));
    /*if (type == null)
    {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
              ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
              .getMessageMandatory(LayeredParams.LrPropAdvaEthernet.LOOPBACK_TYPE_PARAM));
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    } */
    // Valid for CP Access is Terminal
    Integer typeInt = NetLoopbackStatusTypeTranslation.getMIBValue(type);
    if (!(typeInt == NetLoopbackStatusTypeTranslation.TERMINAL.getMIBValue()))
    {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
              ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
              .getMessageIllegal(LayeredParams.LrPropAdvaEthernet.LOOPBACK_TYPE_PARAM));
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    newProps.set(ServiceSPPropertiesFSP150CP.VI.LoopbackCommand, typeInt);

    String parameter = LayeredParameterUtils.findParameter(loopbackParameters,
            LayeredParams.LrPropAdvaEthernet.LOOPBACK_SWAP_SADA_PARAM);
    if (parameter != null)
    {
      Integer swapSadaValue = MtosiUtils.getMIBValue(LoopbackStatusSwapSADATranslation.NOT_APPLICABLE, parameter);
      if (swapSadaValue == LoopbackStatusSwapSADATranslation.NOT_APPLICABLE.getMIBValue())
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
                ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
                .getMessageIllegal(LayeredParams.LrPropAdvaEthernet.LOOPBACK_SWAP_SADA_PARAM));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      newProps.set(ServiceSPPropertiesFSP150CP.VI.DaSaSwapping, swapSadaValue);
    }

    return newProps;
  }

  
  public static NETPortSPPropertiesFSP150CM mtosiReleaseLoopbackToCMNETProperties(MTOSIPortF3Net netPort)
	throws Exception
	{
	  NETPortSPPropertiesFSP150CM newProps = new NETPortSPPropertiesFSP150CM();
		NETPortSPPropertiesFSP150CM props = netPort.getPortSPProperties();
		newProps.set(NETPortSPPropertiesFSP150CM.VI.ShelfIndex, props.get(NETPortSPPropertiesFSP150CM.VI.ShelfIndex));
		newProps.set(WANPortSPProperties.VI.Index, props.get(WANPortSPProperties.VI.Index));
		newProps.set(NETPortSPPropertiesFSP150CM.VI.SlotIndex, props.get(NETPortSPPropertiesFSP150CM.VI.SlotIndex));
		newProps.set(WANPortSPProperties.VI.IfAdminStatus, props.get(WANPortSPProperties.VI.IfAdminStatus));
		// set to None
		newProps.set(WANPortSPProperties.VI.PortLoopback, WANLoopbackStatusTypeTranslation.NONE.getMIBValue());
		return newProps;
	}
  public static ACCPortSPPropertiesFSP150CM mtosiReleaseLoopbackToCMACCProperties(MTOSIPortF3Acc port)
	throws Exception
	{
	  	ACCPortSPPropertiesFSP150CM newProps = new ACCPortSPPropertiesFSP150CM();
	  	ACCPortSPPropertiesFSP150CM props = (ACCPortSPPropertiesFSP150CM)port.getPortSPProperties();
		newProps.set(NETPortSPPropertiesFSP150CM.VI.ShelfIndex, props.get(NETPortSPPropertiesFSP150CM.VI.ShelfIndex));
		newProps.set(WANPortSPProperties.VI.Index, props.get(WANPortSPProperties.VI.Index));
		newProps.set(NETPortSPPropertiesFSP150CM.VI.SlotIndex, props.get(NETPortSPPropertiesFSP150CM.VI.SlotIndex));
		newProps.set(WANPortSPProperties.VI.IfAdminStatus, props.get(WANPortSPProperties.VI.IfAdminStatus));
		// set to None
		newProps.set(WANPortSPProperties.VI.PortLoopback, WANLoopbackStatusTypeTranslation.NONE.getMIBValue());
		return newProps;
	}

  
  public static HnTrafficGenProperties mtosiContinuityTestParametersToTrafficGenProperties(JAXBElement<NVSListT> testParameters) throws ProcessingFailureException {
		HnTrafficGenProperties results = new HnTrafficGenProperties();
		String destinationAddr = null;
		String l2Loopback = null;
		if (testParameters != null) {
			List<NameAndStringValueT> list = testParameters.getValue().getNvs();
			for (NameAndStringValueT parameter : list) {
				if (parameter.getName().equals("DestinationPhysAddress")) {
					destinationAddr = parameter.getValue();
				} else if (parameter.getName().equals("L2Loopback")) {
					l2Loopback = parameter.getValue();
				}
			}
		}
		if (l2Loopback != null) {
			int loopValue = MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE, l2Loopback);
			if (loopValue == BooleanTypeTranslation.NOT_APPLICABLE.getMIBValue()) {
				pfeIllegal("L2Loopback");
			}
			results.set(HnTrafficGenProperties.VI.L2Loopback,loopValue);
		} else {
			pfeMandatory("L2Loopback");
		}
		//Here is where we are going to enforce the Stream = 2 when a Destination Address is provided
		//and Stream =1 when no address is provided.
		if (destinationAddr != null) {
			results.set(HnTrafficGenProperties.VI.Stream1, 2);
			results.set(HnTrafficGenProperties.VS.DestinationMAC,destinationAddr);
		} else {
			results.set(HnTrafficGenProperties.VI.Stream1, 1);

		}
		
		return results;
	}
  
  	private static void pfeIllegal( String var) throws ProcessingFailureException {
		 pfe(MtosiErrorConstants.getMessageIllegal(var));
	}

	private static void pfeMandatory( String var) throws ProcessingFailureException {
		 pfe(MtosiErrorConstants.getMessageMandatory(var));
	}

	private static void pfe( String var) throws ProcessingFailureException {
		ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,var);
		ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
		throw pfe;
	}
}
