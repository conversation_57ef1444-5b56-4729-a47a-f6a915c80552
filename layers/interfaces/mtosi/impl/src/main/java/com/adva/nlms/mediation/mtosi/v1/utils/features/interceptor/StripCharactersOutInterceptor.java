/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.features.interceptor;

import org.apache.cxf.binding.soap.interceptor.SoapPreProtocolOutInterceptor;
import org.apache.cxf.io.CachedOutputStream;
import org.apache.cxf.message.Message;
import org.apache.cxf.phase.AbstractPhaseInterceptor;
import org.apache.cxf.phase.Phase;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class StripCharactersOutInterceptor extends AbstractPhaseInterceptor<Message> {
  
  private static final Logger logger = LogManager.getLogger(StripCharactersOutInterceptor.class.getPackage().getName());
  private static int bufferSize = 4096;

    public StripCharactersOutInterceptor() {
        super(Phase.PRE_STREAM);
        addBefore(SoapPreProtocolOutInterceptor.class.getName());
    }
    
    @Override
    public void handleMessage(Message message) {

      OutputStream os = message.getContent(OutputStream.class);
      if (os == null) {
        return;
      }

      OutputStream newOutput = null;

      try {
        CachedOutputStream oldOutput = (CachedOutputStream) message.getContent(OutputStream.class);

        newOutput = new BufferedOutputStream(os);
        // replacing bytes during copy operation, opearting on the same Stream
        copyStream(oldOutput.getInputStream(), newOutput, new byte[bufferSize]);

        message.setContent(OutputStream.class, os);

      } catch (IOException ioe) {
        logger.error("Ignored: ", ioe);
      }finally {
        try {
          if (newOutput != null)
            newOutput.flush();
        } catch (IOException e) {
          logger.error("Ignored: ", e);
        }
        try {
          os.flush();
        } catch (IOException e) {
          logger.error("Ignored: ", e);
        }

      }

      message.getInterceptorChain().doIntercept(message);

    }


  private static void copyStream(InputStream in, OutputStream out, byte[] buffer) throws IOException {
    int bytes_read;
    while ((bytes_read = in.read(buffer)) != -1){
      boolean  wasChanged = newStripNonValidXMLCharacters(buffer);
      if(wasChanged){
        logger.warn("Non Valid XML Characters were replaced.");
      }
      out.write(buffer, 0, bytes_read);
    }

  }


  private static boolean newStripNonValidXMLCharacters(byte[] in) {
    boolean wasChanged = false;

    for (int i = 0; i < in.length; i++) {
      int cp = 0xff & in[i];

      if (!((cp == 0x9) ||
              (cp == 0xA) ||
              (cp == 0xD) ||
              (cp >= 0x20)
              )) {
        in[i] = '?';
        wasChanged = true;
      }
    }
    return wasChanged;
  }


}
