/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;


public enum CFMDefectsTranslation implements TranslatableEnum {
  B_DEF_AIS        (0, "False"),
  NOT_APPLICABLE   (1, "True");

  private final int    mibValue;
  private final String mtosiString;

  private CFMDefectsTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }

  public static int getMibValue(String value){
    if (value == null) return  CFMDefectsTranslation.NOT_APPLICABLE.getMIBValue();
    for(CFMDefectsTranslation control : values()){
      if(control.getMtosiString().equals(value)){
        return control.getMIBValue();
      }
    }
    return CFMDefectsTranslation.NOT_APPLICABLE.getMIBValue();
  }

  public static String getMtosiString(int value){
    if (value > 0) return  CFMDefectsTranslation.NOT_APPLICABLE.getMtosiString();
    for(CFMDefectsTranslation control : values()){
      if(control.getMIBValue() == value){
        return control.getMtosiString();
      }
    }
    return CFMDefectsTranslation.NOT_APPLICABLE.getMtosiString();
  }
}