/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.syncE;

import com.adva.nlms.mediation.config.f3.entity.sync.F3SyncImpl;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CMImpl;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiF3F3SyncNameException;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiF3SyncName;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.common.NEUtils;
import v1.tmf854.ConnectionTerminationPointListT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.GetContainedCurrentTimingCTPsResponseT;
import v1.tmf854ext.adva.GetContainedCurrentTimingCTPsT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

/**
 * Worker class for the MTOSI operation getContainedCurrentTimingCTPs
 */
public class GetContainedCurrentTimingCTPsWorker extends AbstractMtosiWorker {
  protected GetContainedCurrentTimingCTPsT mtosiBody;
  protected GetContainedCurrentTimingCTPsResponseT response = new GetContainedCurrentTimingCTPsResponseT();
  protected NamingAttributesT tdfrName;
  protected MtosiAddress mtosiAddr;
  protected ConnectionTerminationPointListT ctpList;

  //Constructor
  public GetContainedCurrentTimingCTPsWorker(final GetContainedCurrentTimingCTPsT mtosiBody, final Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getContainedCurrentTimingCTPs", "getContainedCurrentTimingCTPs", "getContainedCurrentTimingCTPsResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    tdfrName = mtosiBody.getTdfrName();
    mtosiAddr = new MtosiAddress(tdfrName);

    if (tdfrName == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
        MtosiErrorConstants.INVALID_FILTER);
    }

    if (!NamingTranslationFactory.isManagementDomain(tdfrName)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
        MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!tdfrName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        MtosiErrorConstants.MD_NOT_FOUND);
    }

    if(tdfrName.getTdfrNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        "TDFr name is missing.");
    }

    MtosiUtils.validateNE(mtosiAddr.getNE());

  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(mtosiAddr.getNE().getDefaultNetworkElementTypeString());
  }

  @Override
  protected void mediate() throws Exception {
    // only allowed for FSP150CC-GE201SE && FSP150CM
    if (!NEUtils.isSyncEDevice(mtosiAddr.getNeType())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The specified ME does not support SyncE.");
    }

    NetworkElementFSP150CMImpl ne = (NetworkElementFSP150CMImpl)mtosiAddr.getNE();
    
    MtosiF3SyncName mtosiName = null;
    
    try {
      mtosiName = new MtosiF3SyncName(tdfrName.getTdfrNm(), true, mtosiAddr.getNeType());
    } catch (MtosiF3F3SyncNameException e) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
      e.getMessage(),e);
      
    }

    final F3SyncImpl f3Sync = getFSP150CMMTOSIWorker(ne).getF3Sync(mtosiName.getShelfIndex(),mtosiName.getSlotIndex(), mtosiName.getAlias());
    if(f3Sync == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.TDFR_NOT_FOUND);
    }

    ctpList = new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(f3Sync).toMtosiCTPs();
//    ctpList = f3Sync.getMtosiTranslator().toMtosiCTPs();
  }

  @Override
  protected void response() throws Exception {
    response.setCtpList(ctpList);
  }

  @Override
  public GetContainedCurrentTimingCTPsResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
