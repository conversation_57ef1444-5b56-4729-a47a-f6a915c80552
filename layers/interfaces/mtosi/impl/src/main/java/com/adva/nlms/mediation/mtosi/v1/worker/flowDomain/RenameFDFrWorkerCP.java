/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ServiceSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ServiceSPPropertiesFSP150CP;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.FDFrEndFSP150CP;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.FDFrFSP150CP;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.FDFrPortEndFSP150CP;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.PortFSP150CPAccess;
import com.adva.nlms.mediation.config.mtosi.FDFr;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import v1.tmf854.HeaderT;

import jakarta.xml.ws.Holder;

public class RenameFDFrWorkerCP extends RenameFDFrWorker
{
  private String oldFdfrName;
  public RenameFDFrWorkerCP(Holder<HeaderT> mtosiHeader, NetworkElement ne, FDFr fdfr, String newFDFrName) {
    super(mtosiHeader, ne, fdfr, newFDFrName);
  }

  @Override
  protected void parse () throws Exception {
    // empty method
  }

  @Override
  protected void mediate() throws Exception
	{
    oldFdfrName = fdfr.getFDFrSPProperties().get(FDFrSPProperties.VS.FDFrName);
		final FDFrSPProperties newProps = new FDFrSPProperties(newFDFrName);
		final FDFrFSP150CP fdfrCP = (FDFrFSP150CP) fdfr;

		final FDFrEndFSP150CP aEnd = fdfrCP.getAEnd();
		final FDFrEndFSP150CP zEnd = fdfrCP.getZEnd();
		if (aEnd instanceof FDFrPortEndFSP150CP)
			transact(newProps, (FDFrPortEndFSP150CP) aEnd);
		else if (zEnd instanceof FDFrPortEndFSP150CP)
			transact(newProps, (FDFrPortEndFSP150CP) zEnd);
	}

  private void transact(FDFrSPProperties props, final FDFrPortEndFSP150CP portEnd)
          throws NetTransactionException, SPValidationException, SNMPCommFailure, ObjectInUseException {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "RenameFDFrWorker");
    ne.getMTOSIWorker().setFDFrOperationInProgress(props.get(FDFrSPProperties.VS.FDFrName), true);
    try {
      ServiceSPPropertiesFSP150CP portProperties = new ServiceSPPropertiesFSP150CP();
      portProperties.set(ServiceSPProperties.VS.CircuitName, props.get(FDFrSPProperties.VS.FDFrName));
      PortFSP150CPAccess accessPort = (PortFSP150CPAccess) portEnd.getPort();
      logSecurity(ne, SystemAction.ModifyNetwork, accessPort.getMtosiName());
      fdfr.setFDFrSPProperties(props);
      accessPort.setSettings(portProperties);
      NetTransactionManager.commitNetTransaction(id);
      ne.logSROperation(SROperationState.SERVICE_RENAME_SUCCESS, oldFdfrName, props.get(FDFrSPProperties.VS.FDFrName));
    } catch (NetTransactionException e) {
      ne.logSROperation(SROperationState.SERVICE_RENAME_FAILURE, props.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ne.logSROperation(SROperationState.SERVICE_RENAME_FAILURE, props.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ne.logSROperation(SROperationState.SERVICE_RENAME_FAILURE, props.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      ne.getMTOSIWorker().setFDFrOperationInProgress(props.get(FDFrSPProperties.VS.FDFrName), false);
      NetTransactionManager.ensureEnd(id);
    }
  }
}
