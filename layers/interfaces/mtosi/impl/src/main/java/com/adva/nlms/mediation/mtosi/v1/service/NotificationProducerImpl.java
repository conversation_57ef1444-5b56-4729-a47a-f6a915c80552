/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;

import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.notification.SubscribeWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.notification.UnsubscribeWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import ws.v1.tmf854.NotificationProducer;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.annotation.Resource;
import jakarta.xml.ws.WebServiceContext;


/**
 * This class was generated by the Celtix 1.1-SNAPSHOT
 * Fri Dec 22 10:48:24 EST 2006
 * Generated source version: 1.1-SNAPSHOT
 */

@jakarta.jws.WebService(name = "NotificationProducer", serviceName = "NotificationService", portName = "NotificationProducerHttp", targetNamespace = "tmf854.v1.ws")

public class NotificationProducerImpl implements NotificationProducer {
  Logger LOG = LogManager.getLogger(this.getClass().getName());
  @Resource
  private WebServiceContext context;


  /* (non-Javadoc)
     * @see ws.v1.tmf854.NotificationProducer#subscribe(v1.tmf854.SubscribeT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
  public v1.tmf854.SubscribeResponseT subscribe(
          v1.tmf854.SubscribeT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(SubscribeWorker.class, mtosiBody, mtosiHeader, "subscribe", context, LOG);
  }


  @Override
  public v1.tmf854.SubscribeResponseT subscribe(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.SubscribeT mtosiBody) throws ProcessingFailureException {
    return subscribe(mtosiBody, mtosiHeader);
  }


  /* (non-Javadoc)
  * @see ws.v1.tmf854.NotificationProducer#unsubscribe(v1.tmf854.UnsubscribeT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
  */
  public v1.tmf854.UnsubscribeResponseT unsubscribe(
          v1.tmf854.UnsubscribeT mtosiBody,
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException {
    return ServiceUtils.runMethod(UnsubscribeWorker.class, mtosiBody, mtosiHeader, "unsubscribe", context, LOG);
  }


  @Override
  public v1.tmf854.UnsubscribeResponseT unsubscribe(
          jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
          v1.tmf854.UnsubscribeT mtosiBody) throws ProcessingFailureException {
    return unsubscribe(mtosiBody, mtosiHeader);
  }


}
