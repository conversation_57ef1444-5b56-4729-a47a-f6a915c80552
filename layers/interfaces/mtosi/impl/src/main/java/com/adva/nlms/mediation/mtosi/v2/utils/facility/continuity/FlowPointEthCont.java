/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */
package com.adva.nlms.mediation.mtosi.v2.utils.facility.continuity;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.F3FpQosPolicerAttr;
import com.adva.nlms.mediation.config.dto.attr.FlowPointF3Attr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;

public class FlowPointEthCont extends BaseFlowForContinuityTest<FlowPointF3Attr> {

  public FlowPointEthCont(int neId, DTO<FlowPointF3Attr> flowPointDTO) {
    super(neId, flowPointDTO);
  }

  @Override
  public EntityIndex getEntityIndex() {
    return flowPointDTO.getValue(FlowPointF3Attr.ENTITY_INDEX);
  }

  @Override
  public int getCtagControl() {
    return flowPointDTO.getValue(FlowPointF3Attr.CTAG_CONTROL);
  }

  @Override
  public int getStagControl() {
    return flowPointDTO.getValue(FlowPointF3Attr.STAG_CONTROL);
  }

  @Override
  public String getPolicerSuffix() {
    return "";
  }
  @Override
  public <Y extends ManagedObjectAttr> Class<Y> getPolicerClass(){
    return (Class<Y>) F3FpQosPolicerAttr.class;
  }

  @Override
  public <Y extends ManagedObjectAttr> Long getCirValue(DTO<Y> dto ){
    return ((DTO<F3FpQosPolicerAttr>)dto).getValue(F3FpQosPolicerAttr.CIR_COMPOSITE);
  }

}
