/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.adapter.facade;

import com.adva.nlms.common.snmp.MANetInterval;
import com.adva.nlms.common.snmp.MANetNameFormat;
import com.adva.nlms.common.snmp.MDNameFormat;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.DTOBuilder;
import com.adva.nlms.mediation.config.dto.attr.EthernetTrafficPortF3Attr;
import com.adva.nlms.mediation.config.dto.attr.MACompAttr;
import com.adva.nlms.mediation.config.dto.attr.MANetAttr;
import com.adva.nlms.mediation.config.dto.attr.MDAttr;
import com.adva.nlms.mediation.config.dto.attr.MEPAttr;
import com.adva.nlms.mediation.config.mofacade.MOFacadeManager;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.common.doclet.ErrorCode;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CFMCreationMhfControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CfmMaCcmIntervalTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CfmMepAisIntervalTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CfmMepAisTriggerTypesTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CfmMepDirectionTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CfmMepLlfTriggerTypesTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CfmMepLowestPriorityDefectTypeTranslation;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.ProcessingFailureException;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import static java.lang.Integer.valueOf;

public class ConnectivityFaultMaintenanceDTOImpl implements ConnectivityFaultMaintenance {
  private static final Map<String, String> fieldMap = new HashMap<>();
  private static Logger logger = LogManager.getLogger(ConnectivityFaultMaintenanceDTOImpl.class);

  //****  MD ****//
  @MtosiFacadeAttribute(value = LayerAttributes.MDLEVEL)
  private Integer MDLevel = null;

  @MtosiFacadeAttribute(value = LayerAttributes.MIPCREATIONCCONTROL)
  private String MipCreationControl = null;

  @MtosiFacadeAttribute(value = LayerAttributes.MACCMINTERVAL)
  private String MaCcmInterval = null;

  @MtosiFacadeAttribute(value = LayerAttributes.MAPRIMARYVID)
  private String MaPrimaryVid = null;

  @MtosiFacadeAttribute(value = LayerAttributes.MANUMBEROFVIDS)
  private Integer MaNumberOfVids = null;

  @MtosiFacadeAttribute(value = LayerAttributes.MAMEPLIST)
  private String MaMepList = null;

  @MtosiFacadeAttribute(value = LayerAttributes.MACOMPMHFCREATION)
  private String MaCompMhfCreation = null;

  @MtosiFacadeAttribute(value = LayerAttributes.MEPDIRECTION)
  private String MepDirection = null;

  @MtosiFacadeAttribute(value = LayerAttributes.MEPPRIMARYVID)
  private Integer MepPrimaryVid = null;

  @MtosiFacadeAttribute(value = LayerAttributes.MEPACTIVE)
  private Integer MepActive = null;

  @MtosiFacadeAttribute(value = LayerAttributes.MEPCCIENABLED)
  private String MepCciEnabled = null;

  @MtosiFacadeAttribute(value = LayerAttributes.MEPVLANPRIORITY)
  private Integer MepVlanPriority = null;

  @MtosiFacadeAttribute(value = LayerAttributes.MEPLOWESTPRIORITYDEFECT)
  private String MepLowestPriorityDefect = null;

  @MtosiFacadeAttribute(value = LayerAttributes.MEPADMINISTRATIONSTATE)
  private String MepAdministrationState = null;

  @MtosiFacadeAttribute(value = LayerAttributes.MEPHIGHESTPRIORITYDEFECT)
  private String MepHighestPriorityDefect = null;

  @MtosiFacadeAttribute(value = LayerAttributes.LLFTRIGGER)
  private String MepLlfTrigger = null;

  @MtosiFacadeAttribute(value = LayerAttributes.AISTRANSMISSIONINTERVAL)
  private String MepTransmissionInternal = null;

  @MtosiFacadeAttribute(value = LayerAttributes.AISGENERATION)
  private String MepAisGeneration = null;

  @MtosiFacadeAttribute(value = LayerAttributes.AISPRIORITY)
  private String MepAisPriority = null;

  @MtosiFacadeAttribute(value = LayerAttributes.AISCLIENTMDLEVEL)
  private String MepAisClientMdLevel = null;

  @MtosiFacadeAttribute(value = LayerAttributes.AISTRIGGER)
  private String MepAisTtrigger = null;

  private NetworkElement networkElement;
  private MtosiMOFacade mtosiMOFacade = null;
  private MOFacadeManager moFacadeManager;

  public ConnectivityFaultMaintenanceDTOImpl(MOFacadeManager moFacadeManager) {
    this.moFacadeManager = moFacadeManager;
  }

  public void setNetworkElement(NetworkElement networkElement) {
    this.networkElement = networkElement;
  }

  public String getMaCcmInterval() {
    return MaCcmInterval;
  }

  public void setMaCcmInterval(String maCcmInterval) {
    MaCcmInterval = maCcmInterval;
  }

  public String getMaPrimaryVid() {
    return MaPrimaryVid;
  }

  public void setMaPrimaryVid(String maPrimaryVid) {
    MaPrimaryVid = maPrimaryVid;
  }

  public String getMepHighestPriorityDefect() {
    return MepHighestPriorityDefect;
  }

  public void setMepHighestPriorityDefect(String mepHighestPriorityDefect) {
    MepHighestPriorityDefect = mepHighestPriorityDefect;
  }

  public String getMepLlfTrigger() {
    return MepLlfTrigger;
  }

  public void setMepLlfTrigger(String mepLlfTrigger) {
    MepLlfTrigger = mepLlfTrigger;
  }

  public String getMepTransmissionInternal() {
    return MepTransmissionInternal;
  }

  public void setMepTransmissionInternal(String mepTransmissionInternal) {
    MepTransmissionInternal = mepTransmissionInternal;
  }

  public String getMepAisGeneration() {
    return MepAisGeneration;
  }

  public void setMepAisGeneration(String mepAisGeneration) {
    MepAisGeneration = mepAisGeneration;
  }

  public String getMepAisPriority() {
    return MepAisPriority;
  }

  public void setMepAisPriority(String mepAisPriority) {
    MepAisPriority = mepAisPriority;
  }

  public String getMepAisClientMdLevel() {
    return MepAisClientMdLevel;
  }

  public void setMepAisClientMdLevel(String mepAisClientMdLevel) {
    MepAisClientMdLevel = mepAisClientMdLevel;
  }

  public String getMepAisTtrigger() {
    return MepAisTtrigger;
  }

  public void setMepAisTtrigger(String mepAisTtrigger) {
    MepAisTtrigger = mepAisTtrigger;
  }

  public Integer getMaNumberOfVids() {
    return MaNumberOfVids;
  }

  public void setMaNumberOfVids(Integer maNumberOfVids) {
    MaNumberOfVids = maNumberOfVids;
  }

  public String getMaMepList() {
    return MaMepList;
  }

  public void setMaMepList(String maMepList) {
    MaMepList = maMepList;
  }

  public void setMepDirection(String mepDirection) {
    MepDirection = mepDirection;
  }

  public Integer getMepPrimaryVid() {
    return MepPrimaryVid;
  }

  public void setMepPrimaryVid(Integer mepPrimaryVid) {
    MepPrimaryVid = mepPrimaryVid;
  }

  public Integer getMepActive() {
    return MepActive;
  }

  public void setMepActive(Integer mepActive) {
    MepActive = mepActive;
  }

  public String getMepCciEnabled() {
    return MepCciEnabled;
  }

  public void setMepCciEnabled(String mepCciEnabled) {
    MepCciEnabled = mepCciEnabled;
  }

  public Integer getMepVlanPriority() {
    return MepVlanPriority;
  }

  public void setMepVlanPriority(Integer mepVlanPriority) {
    MepVlanPriority = mepVlanPriority;
  }

  public String getMepLowestPriorityDefect() {
    return MepLowestPriorityDefect;
  }

  public void setMepLowestPriorityDefect(String mepLowestPriorityDefect) {
    MepLowestPriorityDefect = mepLowestPriorityDefect;
  }

  public String getMepAdministrationState() {
    return MepAdministrationState;
  }

  public void setMepAdministrationState(String mepAdministrationState) {
    MepAdministrationState = mepAdministrationState;
  }

  public static Logger getLogger() {
    return logger;
  }

  public static void setLogger(Logger logger) {
    ConnectivityFaultMaintenanceDTOImpl.logger = logger;
  }

  public String getMaCompMhfCreation() {
    return MaCompMhfCreation;
  }

  public void setMaCompMhfCreation(String maCompMhfCreation) {
    MaCompMhfCreation = maCompMhfCreation;
  }

  public NetworkElement getNetworkElement() {
    return networkElement;
  }

  public String getMepDirection() {
    return MepDirection;
  }

  public Integer getMDLevel() {
    return MDLevel;
  }

  public void setMDLevel(Integer MDLevel) {
    this.MDLevel = MDLevel;
  }

  public String getMipCreationControl() {
    return MipCreationControl;
  }

  public void setMipCreationControl(String mipCreationControl) {
    MipCreationControl = mipCreationControl;
  }

  public MtosiMOFacade getMtosiMOFacade() {
    return mtosiMOFacade;
  }

  public void setMtosiMOFacade(MtosiMOFacade mtosiMOFacade) {
    this.mtosiMOFacade = mtosiMOFacade;
  }

  public MOFacadeManager getMoFacadeManager() {
    return moFacadeManager;
  }

  public void setMoFacadeManager(MOFacadeManager moFacadeManager) {
    this.moFacadeManager = moFacadeManager;
  }

  protected static Map<String, String> getFieldMap() {
    return fieldMap;
  }

  static {
    for (Field field : ConnectivityFaultMaintenanceDTOImpl.class.getDeclaredFields()) {
      String name = field.getName();
      LayerAttributes mappedFieldValue = null;
      Annotation[] annotations = field.getDeclaredAnnotations();
      for (Annotation annotation : annotations) {
        if (annotation.annotationType().isAssignableFrom(MtosiFacadeAttribute.class)) {
          mappedFieldValue = ((MtosiFacadeAttribute) annotation).value();
        }
      }
      if (mappedFieldValue != null) {
        getFieldMap().put(mappedFieldValue.getMappedValue(), "set" + name);
      }
    }
  }

  public MtosiMOFacade getMtosiFacade() {
    if (mtosiMOFacade == null) {
      mtosiMOFacade = moFacadeManager.getFacadeViaNeId(MtosiMOFacade.class, getNetworkElement().getID());
    }
    return mtosiMOFacade;
  }

  @ErrorCode(
      variables = {"$fieldFQN=soap request field"},
      operations = {"modifyTDFr"},
      errorTypes = {"EXCPT_INVALID_INPUT","EXCPT_INVALID_INPUT"},
      texts = {"Input for field $fieldFQN should be numerical.","Input for field $fieldFQN is invalid."}
  )
  public void setValue(String fieldFQN, String value) throws Exception {
    Method method;
    try {
      if (getFieldMap().containsKey(fieldFQN)) {
        method = ConnectivityFaultMaintenanceDTOImpl.class.getDeclaredMethod(getFieldMap().get(fieldFQN), LayerAttributes.valueOfString(fieldFQN).get_paramType());
        method.invoke(this, fixValueType(LayerAttributes.valueOfString(fieldFQN).get_paramType(), value, fieldFQN));
      }
    } catch (NoSuchMethodException | InvocationTargetException | IllegalAccessException e) {
      logger.error("Failed to find proper setter or set value", e);
    }
  }

  @ErrorCode(
      variables = {"$fieldFQN=soap request field"},
      operations = {"modifyTDFr"},
      errorTypes = {"EXCPT_INVALID_INPUT"},
      texts = {"$fieldFQN contains empty or illegal values."}
  )
  private static Object fixValueType(Class clazz, String value, String fieldFQN) throws Exception {
    try {
      if (clazz.isAssignableFrom(Integer.class)) {
        return valueOf(value);
      } else if (clazz.isAssignableFrom(Boolean.class)) {
        return Boolean.valueOf(value);
      } else {
        return value;
      }
    } catch (Exception ex) {
      String[] parts = fieldFQN.split("\\.");
      String part2 = parts[1];
      String output = part2.substring(0, 1).toUpperCase() + part2.substring(1);
      if (ex instanceof NumberFormatException) {
//        MtosiProcessingFailureException mtosiEx = new MtosiProcessingFailureException("Input for field " + fieldFQN + " should be numerical");
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException( ExceptionUtils.EXCPT_INVALID_INPUT,
            output + " contains empty or illegal value.");
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
//      MtosiProcessingFailureException mtosiEx = new MtosiProcessingFailureException("Input for field " + fieldFQN + " is invalid");
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException( ExceptionUtils.EXCPT_INVALID_INPUT,
          output + "contains empty or illegal value.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }

  @Override
  public DTO<MANetAttr> getMANet(MtosiAddress mtosiAddress, DTO<MDAttr> md) throws Exception {
    DTO<MANetAttr> manetDTO = DTOBuilder.getInstance().newDTO(MANetAttr.class);

    //MaCcmInterval
    manetDTO.putOrReplace(MANetAttr.NAME, mtosiAddress.getNaming().getMaNm());
    manetDTO.putOrReplace(MANetAttr.MD_INDEX, md.getValue(MDAttr.MDINDEX));

    // CfmMdNameFormat
    manetDTO.putOrReplace(MANetAttr.NAME_FORMAT, MANetNameFormat.valueOf(2));

    String maCcmInterval = getMaCcmInterval();
    if (maCcmInterval != null) {
      if(MANetInterval.valueOf(CfmMaCcmIntervalTypeTranslation.getMIBValue(maCcmInterval)) == null){
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MA_CCM_INTERVAL_PARAM));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      manetDTO.putOrReplace(MANetAttr.CCM_INTERVAL, MANetInterval.valueOf(CfmMaCcmIntervalTypeTranslation.getMIBValue(maCcmInterval)));
    }

    //maMepList
    String maMepList = getMaMepList();
    if (maMepList != null) {
      manetDTO.putOrReplace(MANetAttr.MEP_TABLE, maMepList);
    }  else{
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants
              .getMessageMandatory(LayeredParams.LrPropAdvaCFMCCM.CFM_MA_MEP_TABLE_PARAM));
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    return manetDTO;
  }

  @Override
  public DTO<MACompAttr> getMAComp(MtosiAddress mtosiAddress, DTO<MANetAttr> manet, DTO ptp) throws Exception {
    DTO<MACompAttr> macompDTO = DTOBuilder.getInstance().newDTO(MACompAttr.class);

    macompDTO.putOrReplace(MACompAttr.COMPONENT_INDEX, (Integer) ptp.getValue(EthernetTrafficPortF3Attr.IF_INDEX));

    // CfmMaPrimaryVid
    String primaryVid = getMaPrimaryVid();
    if (primaryVid != null)
    {
      if (MtosiUtils.isInteger(primaryVid))
      {
        Integer cfmMaPrimaryVid = Integer.valueOf(primaryVid);
        if (cfmMaPrimaryVid < 0 || cfmMaPrimaryVid > 4096)
        {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MA_PRIMARY_VID_PARAM));
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }
        macompDTO.putOrReplace(MACompAttr.PRIMARY_VID, cfmMaPrimaryVid);
      }
      else
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MA_PRIMARY_VID_PARAM));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
    }else{
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.getMessageMandatory(LayeredParams.LrPropAdvaCFMCCM.CFM_MA_PRIMARY_VID_PARAM));
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    // CfmMaCompMhfCreation
    String maCompMhfCreation = getMaCompMhfCreation();
    if (maCompMhfCreation != null)
    {
      if (CFMCreationMhfControlTranslation.getMibValue(maCompMhfCreation) == CFMCreationMhfControlTranslation.NOT_APPLICABLE.getMIBValue())
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MA_MIP_CONTORL_PARAM));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }else{
        macompDTO.putOrReplace(MACompAttr.MIP_CREATION_CONTROL,CFMCreationMhfControlTranslation.getMibValue(maCompMhfCreation) );
      }
    }

    return macompDTO;
  }

  @Override
  public DTO<MEPAttr> getMEP(MtosiAddress mtosiAddress, DTO<MANetAttr> manet, DTO ptp) throws Exception {
    DTO<MEPAttr> mepDTO = DTOBuilder.getInstance().newDTO(MEPAttr.class);

    mepDTO.putOrReplace(MEPAttr.IF_INDEX, (Integer) ptp.getValue(EthernetTrafficPortF3Attr.IF_INDEX));
    mepDTO.putOrReplace(MEPAttr.MD_INDEX, manet.getValue(MANetAttr.MD_INDEX));
    mepDTO.putOrReplace(MEPAttr.MA_INDEX, manet.getValue(MANetAttr.MA_INDEX));

    Integer i = 0;
    mepDTO.putOrReplace(MEPAttr.PRIMARY_VID, i.longValue());

    // CfmMepIdentifier
    String parameter = mtosiAddress.getNaming().getMepNm();
    if (parameter != null)
    {
      if (MtosiUtils.isInteger(parameter))
      {
        Integer cfmMepIdentifier = Integer.valueOf(parameter);
        if (cfmMepIdentifier < 1 || cfmMepIdentifier > 8191)
        {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_IDENTIFIER_PARAM));
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }
        mepDTO.putOrReplace(MEPAttr.IDENTIFIER, cfmMepIdentifier);
      }
      else
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_IDENTIFIER_PARAM));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
    }

    //CfmMepDirection
    String direction = getMepDirection();
    if (direction != null) {
      Integer cfmMepDirection = CfmMepDirectionTypeTranslation.getMIBValue(direction);
      if (cfmMepDirection == CfmMepDirectionTypeTranslation.NOT_APPLICABLE.getMIBValue())
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_DIRECTION_PARAM));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      mepDTO.putOrReplace(MEPAttr.DIRECTION, cfmMepDirection);
    }

    //CfmMepPrimaryVid
    Integer primaryVid = getMepPrimaryVid();
    if (primaryVid != null) {
      if (primaryVid < 0 || primaryVid > 4096)
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_PRIMARY_VID_PARAM));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      mepDTO.putOrReplace(MEPAttr.PRIMARY_VID, Long.valueOf(primaryVid));
    }


    //MepCcmGeneration
    String cciEnabled = getMepCciEnabled();
    if (cciEnabled != null) {
      Integer cci = MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE , cciEnabled);
      if (cci == BooleanTypeTranslation.NOT_APPLICABLE.getMIBValue()){
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_CCI_ENABLED_PARAM));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      mepDTO.putOrReplace(MEPAttr.CCI_ENABLED, cci);
    }


    parameter = getMepAisGeneration();
    if (parameter != null){
      Integer ais = MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE , parameter);
      if (ais != BooleanTypeTranslation.NOT_APPLICABLE.getMIBValue()){
        mepDTO.putOrReplace(MEPAttr.AIS_GEN_ENABLED, MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE , parameter));
      } else{
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_AIS_GENERATION));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
    }



    //CfmMepVlanPriority
    Integer mepVlanPriority = getMepVlanPriority();
    if (mepVlanPriority != null)
    {
        if (mepVlanPriority < 0 || mepVlanPriority > 7)
        {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
              "MepVlanPriority contains empty or illegal value.");
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }
      mepDTO.putOrReplace(MEPAttr.CCM_LTM_PRIORITY, Long.valueOf(mepVlanPriority));
    }

    //CfmMepLowestPriorityDefect
    String lowestPriorityDefect = getMepLowestPriorityDefect();
    if (lowestPriorityDefect != null)
    {
      Integer cfmMepLowestPriorityDefect = CfmMepLowestPriorityDefectTypeTranslation.getMIBValue(lowestPriorityDefect);
      if (cfmMepLowestPriorityDefect == CfmMepLowestPriorityDefectTypeTranslation.NOT_APPLICABLE.getMIBValue())
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_LOWEST_PRIORITY_DEFECT_PARAM));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      mepDTO.putOrReplace(MEPAttr.LOW_PR_DEF, cfmMepLowestPriorityDefect);
    }

    //MepAdministrationControl
    String administrationState = getMepAdministrationState();
    if (administrationState != null) {
      Integer adminState =  MtosiUtils.getMIBValue(CMAdministrationControlTranslation.NOT_APPLICABLE, administrationState);
      if(adminState == CMAdministrationControlTranslation.NOT_APPLICABLE.getMIBValue()){
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_ADMIN_STATE_PARAM));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      mepDTO.putOrReplace(MEPAttr.ADMIN_STATE, adminState);
    }

    //LLF_TRIGGER
    parameter = getMepLlfTrigger();
    if (parameter != null)
    {
      if(parameter.trim().equals("")) {
        mepDTO.putOrReplace(MEPAttr.LLF_TRIGGER_TYPES, 0);
      } else{
        int type = CfmMepLlfTriggerTypesTranslation.getBitset(parameter);
        if(type != 0){
          mepDTO.putOrReplace(MEPAttr.LLF_TRIGGER_TYPES, type);
        } else{
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_LLF_TRIGGER));
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }
      }
    }

    //MepAisGeneration
    parameter = getMepAisGeneration();
    if (parameter != null){
      Integer ais = MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE , parameter);
      if (ais != BooleanTypeTranslation.NOT_APPLICABLE.getMIBValue()){
        mepDTO.putOrReplace(MEPAttr.AIS_GEN_ENABLED, MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE , parameter));
      } else{
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_AIS_GENERATION));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
    }

    //MepAisTransmissionInterval
    parameter = getMepTransmissionInternal();
    if (parameter != null)
    {
      Integer aisInterval = CfmMepAisIntervalTranslation.getMIBValue(parameter);
      if (aisInterval != CfmMepAisIntervalTranslation.NOT_APPLICABLE.getMIBValue()){
        mepDTO.putOrReplace(MEPAttr.AIS_INTERVAL,aisInterval);
      } else{
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_AIS_TRANSMISSION_INTERVAL));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
    }

    //AIS_PRIORITY
    parameter = getMepAisPriority();
    if (parameter != null)
    {
      try{
          Integer aisPriority = Integer.parseInt(parameter);
          if (aisPriority < 0 || aisPriority > 7){
            ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_AIS_PRIORITY));
            throw new ProcessingFailureException(pfet.getReason(), pfet);
          } else{
            mepDTO.putOrReplace(MEPAttr.AIS_PRIORITY, aisPriority);
          }
      }catch (Exception e){
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_AIS_PRIORITY));
        throw new ProcessingFailureException(pfet.getReason(), pfet, e.getCause());
      }
    }

    //AIS_CLIENT_MD_LEVEL
    parameter = getMepAisClientMdLevel();
    if (parameter != null)
    {
      try{
        Integer aisClient = Integer.parseInt(parameter);
        if (aisClient < 1 || aisClient > 7){
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_AIS_CLIENT_MD_LEVEL));
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        } else{
          mepDTO.putOrReplace(MEPAttr.AIS_CLIENT_MD_LEVEL, aisClient);
        }
      } catch(Exception e){
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_AIS_CLIENT_MD_LEVEL));
        throw new ProcessingFailureException(pfet.getReason(), pfet, e.getCause());
      }
    }

    //AIS_TRIGGER
    parameter = getMepAisTtrigger();
    if (parameter != null)
    {
      if(parameter.trim().equals("")) {
        mepDTO.putOrReplace(MEPAttr.AIS_GEN_TRIGGER_TYPES, 0);
      } else{
        int type = CfmMepAisTriggerTypesTranslation.getBitset(parameter);
        if(type != 0){
          mepDTO.putOrReplace(MEPAttr.AIS_GEN_TRIGGER_TYPES, type);
        } else{
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_AIS_TRIGGER));
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }
      }
    }

    return mepDTO;
  }

//  @Override
//  @ErrorCode(
//      operations = {"modifyTDFr"},
//      errorTypes = {"EXCPT_INVALID_INPUT",
//          "EXCPT_INVALID_INPUT","EXCPT_INVALID_INPUT",
//          "EXCPT_INVALID_INPUT","EXCPT_INVALID_INPUT"
//         },
//      constants = {
//          "ADMINISTRATION_CONTROL_ILLEGAL_VALUE",
//          "NETWORK_CLOCK_TYPE_ILLEGAL_VALUE",
//          "SYNC_DOMAIN_TYPE_ILLEGAL_VALUE",
//          "SYNC_SELECTION_MODE_ILLEGAL_VALUE",
//          "SYNC_WRT_TIME_ILLEGAL_VALUE"
//      }
//  )
    @Override
    public DTO<MDAttr> getMD(String fdFrName) throws Exception {
    DTO<MDAttr> maDTO = DTOBuilder.getInstance().newDTO(MDAttr.class);

      maDTO.putOrReplace(MDAttr.NAME , fdFrName);

      maDTO.putOrReplace(MDAttr.NAME_FORMAT , MDNameFormat.valueOf(4));

    //MDLevel
      Integer mdLevel = getMDLevel();
      if (mdLevel != null){
          if (mdLevel < -1 || mdLevel > 7)
          {
            ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants
                    .getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MD_LEVEL_PARAM));
            throw new ProcessingFailureException(pfet.getReason(), pfet);
          }
          maDTO.putOrReplace(MDAttr.LEVEL, mdLevel);
        }
        else
        {
          ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants
                  .getMessageIllegal(LayeredParams.LrPropAdvaCFMCCM.CFM_MD_LEVEL_PARAM));
          throw new ProcessingFailureException(pfet.getReason(), pfet);
        }


    //MIP_CREATION_CONTROL
    String mipCreationControl = getMipCreationControl();
    if(mipCreationControl != null){
      Integer cfmCreateionCMhfControl = MtosiUtils.getMIBValue(CFMCreationMhfControlTranslation.NOT_APPLICABLE , mipCreationControl);
      if (cfmCreateionCMhfControl != CFMCreationMhfControlTranslation.NOT_APPLICABLE.getMIBValue()) {
        maDTO.putOrReplace(MDAttr.MIP_CREATION, cfmCreateionCMhfControl);
      }else{
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
            "MdMipCreationControl"+MtosiErrorConstants.CONTAINS_ILLEGAL_VALUE);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
     }
//    else{
//      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
//          LayerAttributes.MACOMPMHFCREATION.getMappedValue()+" "+MtosiErrorConstants.CONTAINS_ILLEGAL_VALUE);
//      throw new ProcessingFailureException(pfet.getReason(), pfet);
//    }


    return maDTO;
  }
}
