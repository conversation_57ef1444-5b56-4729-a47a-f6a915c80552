/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum CMSwitchActionTranslation {
	
	NONE	 (1, "None"),
	RELEASE    (2, "Release"),
	MANUAL_FROM_WORKING	 (3, "ManualFromWorking"),
	FORCED_FROM_WORKING	 (4, "ForcedFromWorking"),
	MANUAL_FROM_PROTECT	 (5, "ManualFromProtect"),
	FORCED_FROM_PROTECT	 (6, "ForcedFromProtect"),
	LOCKOUT_FROM_PROTECT	 (7, "LockoutFromProtect"),
	NOT_APPLICABLE     (8, "Unknown");
	
  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private CMSwitchActionTranslation(final int mibValue, final String mtosiString)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int mibValue)
  {
    CMSwitchActionTranslation translation = NOT_APPLICABLE;  // the return value

    for (CMSwitchActionTranslation tmpTranslation : values())
    {
      if (mibValue == tmpTranslation.getMIBValue())
      {
        translation = tmpTranslation;
        break;
      }
    }
    return translation.getMtosiString();
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mtosiString  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static int getMIBValue (final String mtosiString)
  {
    CMSwitchActionTranslation translation = NOT_APPLICABLE;  // the return value

    for (CMSwitchActionTranslation tmpTranslation : values())
    {
      if (mtosiString.equals(tmpTranslation.getMtosiString()))
      {
        translation = tmpTranslation;
        break;
      }
    }
    return translation.getMIBValue();

  }
}
