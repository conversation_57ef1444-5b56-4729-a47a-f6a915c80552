/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.MDRequestFailedException;
import com.adva.nlms.mediation.common.serviceProvisioning.LAGSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000BondingSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000EthernetSProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.UNISPPropertiesHN4000;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.hn4000.NetworkElementHN4000;
import com.adva.nlms.mediation.config.hn4000.PMEMap;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet2BASE_TL;
import com.adva.nlms.mediation.config.hn4000.mtosi.FTPHN4000;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.FloatingTerminationPointT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.TPDataT;
import v1.tmf854.TPVendorExtensionsT;
import v1.tmf854.TerminationPointT;
import v1.tmf854ext.adva.SetTPDataResponseT;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.ws.Holder;
import java.util.List;

public class SetFTPDataWorkerHN extends SetTPDataWorker {
	Logger LOG = LogManager.getLogger(this.getClass().getName());
	NetworkElementHN4000 ne4000 = (NetworkElementHN4000) ne;

	public SetFTPDataWorkerHN(Holder<HeaderT> mtosiHeader, TPDataT tpInfo, NamingAttributesT namingAttributes, NetworkElement ne) {
		super(mtosiHeader, tpInfo, namingAttributes, ne);
	}

  @Override
  protected void parse () throws Exception {
    // empty method
  }

  @Override
  protected void mediate() throws Exception {
		// I have to kind so FTPs
		// Bonded Ports and Lags
		String ftpName = namingAttributes.getFtpNm();
		if (ftpName.indexOf("port=ETH") > 0) {
			boolean hn400 = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400;
			PortHN4000Ethernet2BASE_TL ftp = ne4000.getBondedPort(ftpName);
			if (ftp == null) {
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
			}
			// we ask mediator to handle CPESerialPortAdmin, since it could be set via a setTPData
			PortHN4000BondingSPProperties newProps = MtosiTPMediator.mtosiTPDataTToHN4000FTPBondProperties(tpInfo, null, hn400, true);
			if (newProps.size() > 0) {
				newProps.set(PortHN4000EthernetSProperties.VI.IfIndex, ftp.getPortSPProperties().get(PortHN4000EthernetSProperties.VI.IfIndex));
			}
			UNISPPropertiesHN4000 newUniProps = MtosiTPMediator.mtosiTPDataTToHNUniProperties(tpInfo, ne4000.getMTOSIWorker().getNetworkElementTypeForMTOSI(), ftp.hasUni());
			if (newUniProps != null && ftp.hasUni()) {
				UNISPPropertiesHN4000 props = ftp.getUni().getUniSPProperties();
				newUniProps.set(UNISPPropertiesHN4000.VL.DeviceId, props.get(UNISPPropertiesHN4000.VL.DeviceId));
				newUniProps.set(UNISPPropertiesHN4000.VL.PortId, props.get(UNISPPropertiesHN4000.VL.PortId));
			}

			// handle tpsToAdd and tpsToRemove to set PmePortMap
			JAXBElement<TPVendorExtensionsT> extensionsJAXB = tpInfo.getVendorExtensions();
			//Are they Mandatory?  I usually do not provide vendor extensions.
			if (extensionsJAXB != null) {
				TPVendorExtensionsT vendorExtensions = extensionsJAXB.getValue();
				List<TPDataT> tpDataListAdd = MtosiUtils.getTpsToAdd(vendorExtensions);
				List<NamingAttributesT> namingAttributesListRemove = MtosiUtils.getTpsToRemove(vendorExtensions);
			 
				// add 2BPMEs to add to the map
				PMEMap pmeMapAdd = MtosiTPMediator.getPMEMapForTPListAdd(tpDataListAdd);	
				PMEMap pmeMapAll = MtosiTPMediator.getPMEMapForTPListRemove(namingAttributesListRemove, pmeMapAdd);
				if(pmeMapAll.getMap().size()>0) {
					newProps.set(PortHN4000EthernetSProperties.VI.IfIndex, ftp.getPortSPProperties().get(PortHN4000EthernetSProperties.VI.IfIndex));
					newProps.set(PortHN4000BondingSPProperties.VH.PmePortMap, pmeMapAll.getMap());
				}
							
			}
			// process actual 2BPME tpData entries for those to be added to bonding
			// Actually we don't allow this:  a separate setTPData is required for each 2BPME port to be modified
			// per agreement with BT
			//List<PMEPortPair> pmePairList = MtosiTPMediator.mtosiTPDataListToPMEPortPair(tpDataListAdd);
			

			transact(ftp, newProps, newUniProps);
		} else if (ftpName.indexOf("port=LAG") > 0) {
			FTPHN4000 ftp = (FTPHN4000) ne4000.getMTOSIWorker().getFTP(ftpName);
			if (ftp == null) {
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
			}
			LAGSPPropertiesHN4000 newProps = MtosiTPMediator.mtosiTPDataTToHN4000FTPProperties(tpInfo, ftpName);
			UNISPPropertiesHN4000 newUniProps = MtosiTPMediator.mtosiTPDataTToHNUniProperties(tpInfo, ne4000.getMTOSIWorker().getNetworkElementTypeForMTOSI(), ftp.getLag()
					.hasUni());
			if (newUniProps != null && ftp.getLag().hasUni()) {
				UNISPPropertiesHN4000 props = ftp.getLag().getUni().getUniSPProperties();
				newUniProps.set(UNISPPropertiesHN4000.VL.DeviceId, props.get(UNISPPropertiesHN4000.VL.DeviceId));
				newUniProps.set(UNISPPropertiesHN4000.VL.PortId, props.get(UNISPPropertiesHN4000.VL.PortId));
			}
			transact(ftp, newProps, newUniProps);
		}
	}

	private void transact(PortHN4000Ethernet2BASE_TL ftp, PortHN4000BondingSPProperties newProps, UNISPPropertiesHN4000 newUniProps)
			throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure, MDRequestFailedException {
		NetworkElement locks[] = new NetworkElement[] { ne };
		Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "SetTPDataWorker");
		try {
			logSecurity(ne, SystemAction.ModifyNetwork, "ftpNm=" + ftp.getMtosiName());
			if (newProps.size() > 0) {
				ftp.setSettings(newProps);
			}
			if (newUniProps != null) {
				if (ftp.hasUni()) {
					if (newUniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null) {
						ftp.deleteUNI();
					} else {
						ftp.setUniProperties(newUniProps, ftp.getUni().getUniSPProperties());
					}
				} else {
					ftp.createUni(newUniProps);
				}
			}
			NetTransactionManager.commitNetTransaction(id);
		} catch (NetTransactionException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SPValidationException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SNMPCommFailure e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (MDRequestFailedException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} finally {
			NetTransactionManager.ensureEnd(id);
		}
	}

	private void transact(FTPHN4000 ftp, LAGSPPropertiesHN4000 props, UNISPPropertiesHN4000 newUniProps) throws ObjectInUseException, NetTransactionException,
			SPValidationException, SNMPCommFailure, MDRequestFailedException {
		NetworkElement locks[] = new NetworkElement[] { ne };
		Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "SetTPDataWorker");
		try {
			logSecurity(ne, SystemAction.ModifyNetwork, "ftpNm=" + ftp.getFTPName());
			ftp.setFTPSPProperties(props);
			if (newUniProps != null) {
				if (ftp.getLag().hasUni()) {
					if (newUniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null) {
						ftp.getLag().deleteUNI();
					} else {
						ftp.getLag().setUniProperties(newUniProps, ftp.getLag().getUni().getUniSPProperties());
					}
				} else {
					ftp.getLag().createUni(newUniProps);
				}
			}
			NetTransactionManager.commitNetTransaction(id);
		} catch (NetTransactionException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SPValidationException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SNMPCommFailure e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (MDRequestFailedException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} finally {
			NetTransactionManager.ensureEnd(id);
		}
	}

	@Override
  protected void response() throws Exception {
		FTPHN4000 ftp = ManagedElementFactory.getHN4000Ftp(namingAttributes);
		ftp.doPollingVolatile();
		ObjectFactory objectFactory = new ObjectFactory();
		FloatingTerminationPointT floatingTerminationPointT = new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ftp).toMtosiFTP();
//		FloatingTerminationPointT floatingTerminationPointT = ftp.getMtosiTranslator().toMtosiFTP();

		TerminationPointT tp = objectFactory.createTerminationPointT();
		tp.setFtp(floatingTerminationPointT);
		response.setModifiedTP(tp);
	}

	@Override
  public SetTPDataResponseT getSuccessResponse() {
		if (response == null)
			return null;
		response.setTmf854Version(MtosiConstants.VERSION);
		return response;
	}
}