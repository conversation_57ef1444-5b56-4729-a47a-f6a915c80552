/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.mediation.common.serviceProvisioning.WANPortSPPropertiesFSP150CC;

/**
 * Created by IntelliJ IDEA. User: Lukasz Date: 2007-05-30 Time: 13:14:09 To change this template use File | Settings |
 * File Templates.
 */
public enum NetLoopbackStatusTypeTranslation{
  NONE           (1, "None"),
  UNKNOWN_1      (2, "Unknown"),
  REMOTE_EFMOAM  (3, "RemoteEFMOAM"),
  UNKNOWN_2      (4, "Unknown"),
  TERMINAL       (5, "Terminal"),
  UNKNOWN_3      (6, "Unknown"),
  NOT_APPLICABLE (0, "n/a");

  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private NetLoopbackStatusTypeTranslation (final int mibValue, final String mtosiString)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int mibValue)
  {
    NetLoopbackStatusTypeTranslation enumType = NOT_APPLICABLE;  // the return value

    for (NetLoopbackStatusTypeTranslation tmpEnumType : values())
    {
      if (mibValue == tmpEnumType.getMIBValue())
      {
        enumType = tmpEnumType;
        break;
      }
    }
    return enumType.getMtosiString();
  }
  /**
   * Returns the string representation used in MTOSI layer.
   * @param mtosiString  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static int getMIBValue (final String mtosiString)
  {
    NetLoopbackStatusTypeTranslation enumType = NOT_APPLICABLE;  // the return value

    for (NetLoopbackStatusTypeTranslation tmpEnumType : values())
    {
      if (mtosiString.equals(tmpEnumType.getMtosiString()))
      {
        enumType = tmpEnumType;
        break;
      }
    }
    return enumType.getMIBValue();

  }

  /**
   * Return list of loppback
   * @return String
   */
  public static String getLoopbackList(WANPortSPPropertiesFSP150CC wanPortSPPropertiesFSP150CC)
  {
    int bitField = 0x1; //001
      final StringBuilder resultBuilder = new StringBuilder();
      for(int i = 1; i <= 3; i++)
      {
        if((wanPortSPPropertiesFSP150CC.get(WANPortSPPropertiesFSP150CC.VI.VlanLoopbackMask) & bitField) != 0)
        {
          if(resultBuilder.length() > 0)
            resultBuilder.append(",");
          switch(i){
            case 1:
              resultBuilder.append(wanPortSPPropertiesFSP150CC.get(WANPortSPPropertiesFSP150CC.VS.VlanLoopback1));
              break;
            case 2:
              resultBuilder.append(wanPortSPPropertiesFSP150CC.get(WANPortSPPropertiesFSP150CC.VS.VlanLoopback2));
              break;
            case 3:
              resultBuilder.append(wanPortSPPropertiesFSP150CC.get(WANPortSPPropertiesFSP150CC.VS.VlanLoopback3));
              break;
          }
        }
        bitField <<=1;
      }
    return resultBuilder.toString();
  }

  public static String getInnerLoopbackList(WANPortSPPropertiesFSP150CC wanPortSPPropertiesFSP150CC)
  {
    int bitField = 0x1; //001
      final StringBuilder resultBuilder = new StringBuilder();
      for(int i = 1; i <= 3; i++)
      {
        if((wanPortSPPropertiesFSP150CC.get(WANPortSPPropertiesFSP150CC.VI.InnerVlanLoopbackMask) & bitField) != 0)
        {
          if(resultBuilder.length() > 0)
            resultBuilder.append(",");
          switch(i){
            case 1:
              resultBuilder.append(wanPortSPPropertiesFSP150CC.get(WANPortSPPropertiesFSP150CC.VS.InnerVlanLoopback1));
              break;
            case 2:
              resultBuilder.append(wanPortSPPropertiesFSP150CC.get(WANPortSPPropertiesFSP150CC.VS.InnerVlanLoopback2));
              break;
            case 3:
              resultBuilder.append(wanPortSPPropertiesFSP150CC.get(WANPortSPPropertiesFSP150CC.VS.InnerVlanLoopback3));
              break;
          }
        }
        bitField <<=1;
      }
    return resultBuilder.toString();
  }
}
