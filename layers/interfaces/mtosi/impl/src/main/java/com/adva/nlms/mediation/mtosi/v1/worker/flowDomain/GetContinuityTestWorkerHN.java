/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import java.util.List;

import jakarta.xml.ws.Holder;

import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.hn4000.FlowHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;

public abstract class GetContinuityTestWorkerHN extends GetContinuityTestWorker {
	private String ctpName;
	protected FlowHN4000 flow;

	public GetContinuityTestWorkerHN(Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne) {
		super(mtosiHeader, namingAttributes, ne);
	}

	@Override
  protected void parse() throws Exception {
		if (namingAttributes.getPtpNm() == null && namingAttributes.getFtpNm() == null) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, 
					"The ptpNm/ftpNm has not been specified.");
		}
		if ((this.ctpName = namingAttributes.getCtpNm()) == null) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, 
					"The ctpNm has not been specified.");
		}
	}

	@Override
  protected void mediate() throws Exception {
		if ((port = ManagedElementFactory.getHN4000Port(namingAttributes)) == null) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, 
					"The specified parent port was not found.");
		}
		if (!(port instanceof PortHN4000Ethernet)) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, 
					"Continuity test should be run on ethernet port.");
		}
		PortHN4000Ethernet bondedPort = (PortHN4000Ethernet) port;
		if (!bondedPort.hasUni()) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, 
					"Ethernet port does not belong to UNI.");
		}
		final List<FlowHN4000> flows = bondedPort.getFlowsByName(ctpName);
		if (flows == null) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, 
					MtosiErrorConstants.CTP_NOT_FOUND);
		} else if (flows.size() > 1) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, 
					MtosiErrorConstants.CTP_DUPLICATE_FOUND);
		}
		flow = flows.iterator().next();
	}

}
