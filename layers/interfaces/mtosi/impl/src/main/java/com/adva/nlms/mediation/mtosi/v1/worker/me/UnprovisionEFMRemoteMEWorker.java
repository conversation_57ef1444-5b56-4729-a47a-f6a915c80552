/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */
package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.DTOBuilder;
import com.adva.nlms.mediation.config.dto.attr.PeerNetworkElementAttr;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CMImpl;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.UnprovisionEFMRemoteMEResponseT;
import v1.tmf854ext.adva.UnprovisionEFMRemoteMET;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public class UnprovisionEFMRemoteMEWorker extends AbstractMtosiWorker {
  private static final Logger log = LogManager.getLogger(UnprovisionEFMRemoteMEWorker.class);

  private UnprovisionEFMRemoteMET mtosiBody;
  private NamingAttributesT tpName;
  private UnprovisionEFMRemoteMEResponseT response = new UnprovisionEFMRemoteMEResponseT();
  private NetworkElement ne;
  private MtosiMOFacade facade;
  private DTO<PeerNetworkElementAttr> peerNetworkElementDTO;
  private String ptpName;
  public static final String pattern = "/(shelf)=(\\d+)/(slot)=(\\d+)/port=NET-1";

  public UnprovisionEFMRemoteMEWorker(UnprovisionEFMRemoteMET mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "unprovisionEFMRemoteME", "unprovisionEFMRemoteME", "unprovisionEFMRemoteMEResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    validator.validate(ne.getDefaultNetworkElementTypeString());
  }

  @Override
  protected void parse() throws Exception {
    tpName = mtosiBody.getTpName();
    if (tpName == null) {
      throw ExceptionUtils.createNewMPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MD_NAME_MISSING);
    }
    this.ne = this.getMtosiCtrl().getLegacyMtosiMOFacade().getNEByName(tpName.getMeNm());

    if (tpName.getMdNm() == null) {
      throw ExceptionUtils.createNewMPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!tpName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ExceptionUtils.createNewMPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MD_NOT_FOUND);
    }

    ptpName = tpName.getPtpNm();
    if (!ptpName.matches(pattern)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          "The specified ptpNm is invalid.");
    }

    MtosiUtils.existsMDOrMEEntity(tpName.getMdNm(), tpName.getMeNm(), ne);
    facade = getMtosiCtrl().getMoFacadeManager().getFacadeViaNeId(MtosiMOFacade.class,ne.getID());
  }

  @Override
  protected void mediate() throws Exception {
    peerNetworkElementDTO = DTOBuilder.getInstance().newDTO(PeerNetworkElementAttr.class);

    int slotIndex = getIndexForGivenField(ptpName, "slot");
    int portIndex = getIndexForGivenField(ptpName, "port");
    int cmSlotIndex = slotIndex + 1;
    // if this does not return null then is CPMR otherwise is GE102ProH
    if(((NetworkElementFSP150CMImpl) ne).getPeerNetworkElementBySlotIndex(cmSlotIndex) != null){
      peerNetworkElementDTO.putOrReplace(PeerNetworkElementAttr.PEER_TYPE,
          MIBFSP150CM.Entity.NetworkElementTable.TYPE_CPMR);
    }else{
      peerNetworkElementDTO.putOrReplace(PeerNetworkElementAttr.PEER_TYPE,
          MIBFSP150CM.Entity.NetworkElementTable.TYPE_GE102PROEFMH);
    }

    peerNetworkElementDTO.putOrReplace(PeerNetworkElementAttr.PEER_NE_INDEX, cmSlotIndex + 100);
    peerNetworkElementDTO.putOrReplace(PeerNetworkElementAttr.IS_FROM_PORT_ACC, false);
    peerNetworkElementDTO.putOrReplace(PeerNetworkElementAttr.SLOT_INDEX, cmSlotIndex);
    peerNetworkElementDTO.putOrReplace(PeerNetworkElementAttr.FROM_PORT_INDEX, portIndex);

    transact();
  }

  private void transact() throws Exception {
    try {
      facade.openNetTransaction(ne.getID());
      facade.deletePeerOnDevice(ne.getID(), peerNetworkElementDTO);
      facade.getNetworkElementDTO(ne.getID());
      logSecurity(ne, SystemAction.AddNetwork, tpName.getPtpNm());
    }catch (SNMPCommFailure ex) {
      log.error("Exception during transaction: " + ex);
      throw ex;
    }  finally {
      facade.closeNetTransaction(ne.getID());
    }
  }

  private int getIndexForGivenField(String ptpName, String field) {
    String[] parts = ptpName.split("/");
    String index = "0";
    for (String part : parts){
      if (part.startsWith(field)){
        if (part.contains("-"))
          index = part.split("-")[1];
        else
          index = part.split("=")[1];
      }
    }
    return Integer.parseInt(index);
  }

  @Override
  protected void response() throws Exception {
  }

  @Override
  public UnprovisionEFMRemoteMEResponseT getSuccessResponse()
  {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }


}
