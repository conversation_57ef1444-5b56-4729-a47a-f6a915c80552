/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker;

import jakarta.xml.ws.Holder;

import jakarta.xml.bind.JAXBElement;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.TPDataListT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854ext.adva.CreateAndActivateFDFrResponseT;
import v1.tmf854ext.adva.FDFrCreateDataT;
import v1.tmf854ext.adva.CreateAndActivateFDFrT;
import v1.tmf854ext.adva.FlowDomainFragmentT;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.config.NetworkElement;

import ws.v1.tmf854.ProcessingFailureException;

abstract public class CreateAndActivateFDFrWorker extends AbstractMtosiWorker
{
  protected CreateAndActivateFDFrResponseT response = new CreateAndActivateFDFrResponseT();
  protected CreateAndActivateFDFrT mtosiBody;
  protected NetworkElement ne;
  protected NamingAttributesListT aEnd;
  protected NamingAttributesListT zEnd;
  protected NamingAttributesT namingAttributes;
	protected FDFrCreateDataT createData;
  protected TPDataListT tpsToModify;

  public CreateAndActivateFDFrWorker (CreateAndActivateFDFrT mtosiBody, Holder<HeaderT> mtosiHeader, NetworkElement ne, NamingAttributesListT aEnd, NamingAttributesListT zEnd)
	{
    super(mtosiHeader, "createAndActivateFDFr", "createAndActivateFDFr", "createAndActivateFDFrResponse");
    this.mtosiBody = mtosiBody;
    this.ne = ne;
    this.aEnd = aEnd;
    this.zEnd = zEnd;
  }

  @Override
  protected void parse() throws Exception {
    if ((this.createData = mtosiBody.getCreateData()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The createData has not been specified.");
    }

    // it is an optional parameter
    this.tpsToModify = mtosiBody.getTpsToModify();

    if((this.namingAttributes = createData.getName()) == null ||
            namingAttributes.getFdfrNm()==null || namingAttributes.getFdfrNm().length()==0) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "FDFr name is missing.");
    }

    if (!MtosiUtils.isSameNetworkElement(aEnd.getName().get(0), zEnd.getName().get(0))) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(),
              ExceptionUtils.EXCPT_INVALID_INPUT, "aEnd and zEnd are not on the same Managed Element.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if (ManagedElementFactory.getFDFr(namingAttributes.getFdfrNm()) != null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(),
              ExceptionUtils.EXCPT_INVALID_INPUT, "An FDFr already exists with the specified name.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }
  
  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(ne.getDefaultNetworkElementTypeString());
  }

  @Override
  protected void response() throws Exception {
		final FlowDomainFragmentT fdfr = ManagedElementFactory.getFDFr(namingAttributes);
		response.setTheFDFr(fdfr);
        v1.tmf854ext.adva.ObjectFactory factory = new v1.tmf854ext.adva.ObjectFactory();
		JAXBElement<NamingAttributesListT> aEndReturn = factory.createFlowDomainFragmentTAEnd(aEnd);
        response.setAEnd(aEndReturn);
		JAXBElement<NamingAttributesListT> zEndReturn = factory.createFlowDomainFragmentTZEnd(zEnd);
        response.setZEnd(zEndReturn);
		// have to get updated Ports that were in the request tpsToModify
		JAXBElement<TPDataListT> modifiedTPs = factory.createCreateAndActivateFDFrResponseTTpsToModify(getUpdatedTPs());
        response.setTpsToModify(modifiedTPs);
	}

  protected abstract TPDataListT getUpdatedTPs() throws ProcessingFailureException;

  @Override
  public CreateAndActivateFDFrResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
