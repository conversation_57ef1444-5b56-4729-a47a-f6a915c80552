/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum CMAutoNegotiationTranslation {
  DISABLED               (14, "Disabled", "Full", "0"),
  DISABLED_10MB_FULL     (1, "Disabled", "Full", "10"),
  DISABLED_10MB_HALF     (2, "Disabled", "Half", "10"),
  DISABLED_100MB_FULL    (3, "Disabled", "Full", "100"),
  DISABLED_100MB_HALF    (4, "Disabled", "Half", "100"),
  DISABLED_1000MB_FULL   (5, "Disabled", "Full", "1000"),
  DISABLED_1000MB_HALF   (6, "Disabled", "Half", "1000"),
  ENABLED                (7, "Enabled", "Full", "0"),
  ENABLED_HALF           (7, "Enabled", "Half", "0"),
  ENABLED_10MB_FULL      (8, "Enabled", "Full", "10"),
  ENABLED_10MB_HALF      (9, "Enabled", "Half", "10"),
  ENABLED_100MB_FULL     (10, "Enabled", "Full", "100"),
  ENABLED_100MB_HALF     (11, "Enabled", "Half", "100"),
  ENABLED_1000MB_FULL    (12, "Enabled", "Full", "1000"),
  ENABLED_1000MB_HALF    (13, "Enabled", "Half", "1000"),
  ENABLED_1000MB_FULL_MASTER    (15, "Enabled", "Full", "1000","Master"),
  ENABLED_1000MB_FULL_SLAVE   (16, "Enabled", "Full", "1000","Slave"),
  ENABLED_auto_detect(21, "Enabled", "Full", "1", "None"),
  NOT_APPLICABLE         (-1, "n/a", "n/a", "n/a");

  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;
  private final String duplexMode;
  private final String speedRate;
  private final String speedMode;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   * @param duplexMode The duplex mode.
   * @param speedRate The current speed rate.
   */
  private CMAutoNegotiationTranslation (final int mibValue, final String mtosiString, final String duplexMode, final String speedRate)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
    this.duplexMode = duplexMode;
    this.speedRate = speedRate;
    this.speedMode = null;
  }

    //------------------------------------------------------------------------------------------------------------------
    /**
     * Constructor.
     * @param mibValue    The MIB defined value
     * @param mtosiString  The string representation used in MTOSI layer.
     * @param duplexMode The duplex mode.
     * @param speedRate The current speed rate.
     * @param speedMode the current speed mode
     */
    private CMAutoNegotiationTranslation (final int mibValue, final String mtosiString, final String duplexMode, final String speedRate, final String speedMode)
    {
        this.mibValue   = mibValue;
        this.mtosiString = mtosiString;
        this.duplexMode = duplexMode;
        this.speedRate = speedRate;
        this.speedMode = speedMode;
    }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the duplex mode string representation.
   * @return the duplex mode string representation.
   */
  public String getDuplexMode() {
    return duplexMode;
  }

  /**
   * Returns the speed rate string representation.
   * @return the speed rate string representation.
   */
  public String getSpeedRate() {
    return speedRate;
  }

  public String getSpeedMode(){
      return speedMode;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int mibValue)
  {
    CMAutoNegotiationTranslation autoNegTranslation = NOT_APPLICABLE;  // the return value

    for (CMAutoNegotiationTranslation tmpAutoNegTranslation : values())
    {
      if (mibValue == tmpAutoNegTranslation.getMIBValue())
      {
        autoNegTranslation = tmpAutoNegTranslation;
        break;
      }
    }
    return autoNegTranslation.getMtosiString();
  }

  /**
   * Returns the speed rate corresponding to the mib value.
   * @param mibValue  The MIB defined value
   * @return the speed rate corresponding to the mib value.
   */
  public static String getSpeedRate(final int mibValue)
  {
    CMAutoNegotiationTranslation autoNegTranslation = NOT_APPLICABLE;  // the return value

    for (CMAutoNegotiationTranslation tmpAutoNegTranslation : values())
    {
      if (mibValue == tmpAutoNegTranslation.getMIBValue())
      {
        autoNegTranslation = tmpAutoNegTranslation;
        break;
      }
    }
    return autoNegTranslation.getSpeedRate();
  }

  /**
   * Returns the duplex mode corresponding to the mib value.
   * @param mibValue  The MIB defined value
   * @return the duplex mode corresponding to the mib value.
   */
  public static String getDuplexMode(final int mibValue)
  {
    CMAutoNegotiationTranslation autoNegTranslation = NOT_APPLICABLE;  // the return value

    for (CMAutoNegotiationTranslation tmpAutoNegTranslation : values())
    {
      if (mibValue == tmpAutoNegTranslation.getMIBValue())
      {
        autoNegTranslation = tmpAutoNegTranslation;
        break;
      }
    }
    return autoNegTranslation.getDuplexMode();
  }

  /**
   * Returns if the mib value corresponds to 'Enabled'
   * @param mibValue  The MIB defined value
   * @return if the mib value corresponds to 'Enabled'
   */
  public static boolean isEnabled(final int mibValue)
  {
    CMAutoNegotiationTranslation autoNegTranslation = NOT_APPLICABLE;  // the return value

    for (CMAutoNegotiationTranslation tmpAutoNegTranslation : values())
    {
      if (mibValue == tmpAutoNegTranslation.getMIBValue())
      {
        autoNegTranslation = tmpAutoNegTranslation;
        break;
      }
    }
    return autoNegTranslation.getMtosiString().equals("Enabled");
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mtosiString  The MIB defined value
   * @return the string representation used in MTOSI layer.
   * @param duplexMode The duplex mode.
   * @param speedRate The current speed rate.
   */
  public static int getMIBValue (final String mtosiString, final String duplexMode, final String speedRate)
  {
    CMAutoNegotiationTranslation autoNegTranslation = NOT_APPLICABLE;  // the return value

    for (CMAutoNegotiationTranslation tmpAutoNegTranslation : values())
    {
      if (tmpAutoNegTranslation == DISABLED) {
        continue;
      }
      if (mtosiString.equals(tmpAutoNegTranslation.getMtosiString()) &&
              duplexMode.equals(tmpAutoNegTranslation.getDuplexMode()) &&
              speedRate.equals(tmpAutoNegTranslation.getSpeedRate()))
      {
        autoNegTranslation = tmpAutoNegTranslation;
        break;
      }
    }
    return autoNegTranslation.getMIBValue();
  }

    public static int getMIBValue (final String mtosiString, final String duplexMode, final String speedRate, final String speedMode)
    {
        CMAutoNegotiationTranslation autoNegTranslation = NOT_APPLICABLE;  // the return value

        for (CMAutoNegotiationTranslation tmpAutoNegTranslation : values())
        {
            if (tmpAutoNegTranslation == DISABLED) {
                continue;
            }
            if (mtosiString.equals(tmpAutoNegTranslation.getMtosiString()) &&
                    duplexMode.equals(tmpAutoNegTranslation.getDuplexMode()) &&
                    speedRate.equals(tmpAutoNegTranslation.getSpeedRate()) &&
                    speedMode.equals(tmpAutoNegTranslation.getSpeedMode()))
            {
                autoNegTranslation = tmpAutoNegTranslation;
                break;
            }
        }
        return autoNegTranslation.getMIBValue();
    }

  public static CMAutoNegotiationTranslation getEnum (final int mibValue)
  {
    CMAutoNegotiationTranslation autoNegTranslation = NOT_APPLICABLE;  // the return value

    for (CMAutoNegotiationTranslation tmpAutoNegTranslation : values())
    {
      if (mibValue == tmpAutoNegTranslation.getMIBValue())
      {
        autoNegTranslation = tmpAutoNegTranslation;
        break;
      }
    }
    return autoNegTranslation;
  }
}
