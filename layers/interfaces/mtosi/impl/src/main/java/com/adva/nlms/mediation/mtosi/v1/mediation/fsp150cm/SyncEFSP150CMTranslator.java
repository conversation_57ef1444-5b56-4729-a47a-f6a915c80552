/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm;

import com.adva.nlms.mediation.common.serviceProvisioning.F3SyncRefSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.F3SyncSPProperties;
import com.adva.nlms.mediation.config.f3.entity.sync.F3SyncDAO;
import com.adva.nlms.mediation.config.f3.entity.sync.F3SyncDBImpl;
import com.adva.nlms.mediation.config.f3.entity.sync.F3SyncImpl;
import com.adva.nlms.mediation.config.f3.entity.syncref.F3SyncRefImpl;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.SyncETranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMNetworkClockTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMServiceStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSyncClockModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSyncQLTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSyncRefState;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSyncRefStatus;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSyncSelectionModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.F3SyncDomainTypeTranslation;
import org.snmp4j.smi.OID;
import v1.tmf854.ConnectionTerminationPointListT;
import v1.tmf854.ConnectionTerminationPointT;
import v1.tmf854.DirectionalityT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ResourceStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import v1.tmf854.TPDataListT;
import v1.tmf854.TPDataT;
import v1.tmf854ext.adva.TimingDomainFragmentT;
import ws.v1.tmf854.ProcessingFailureException;

import java.util.Set;

/**
 * Translator for TDFr
 */

public class SyncEFSP150CMTranslator extends SyncETranslator {
  private F3SyncImpl f3SyncImpl;

  public SyncEFSP150CMTranslator(F3SyncImpl f3SyncImpl) {
    super(f3SyncImpl);
    this.f3SyncImpl = f3SyncImpl;
  }

  @Override
  public TimingDomainFragmentT toMtosiTDFr() throws ProcessingFailureException {
    final ObjectFactory objFactory = new ObjectFactory();
    final v1.tmf854ext.adva.ObjectFactory objFactoryEx = new v1.tmf854ext.adva.ObjectFactory();
    final TimingDomainFragmentT tdfrT = objFactoryEx.createTimingDomainFragmentT();

    F3SyncSPProperties tdfrProperties = f3SyncImpl.getF3SyncSPProperties();
    Set<F3SyncRefImpl> f3SyncRefs = f3SyncImpl.getAllF3SyncRefs();

    //  NamingAttributesT name;
    NamingAttributesT namingAttr = NamingTranslationFactory.getNamingAttributes(f3SyncImpl);
    tdfrT.setName(objFactoryEx.createTimingDomainFragmentTName(namingAttr));

    //  String discoveredName;
    tdfrT.setDiscoveredName(objFactoryEx.createTimingDomainFragmentTDiscoveredName(namingAttr.getTdfrNm()));

    //  String namingOS;
    tdfrT.setNamingOS(objFactoryEx.createTimingDomainFragmentTNamingOS(OSFactory.getNmsName()));

    //  SourceT source;
    final SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    tdfrT.setSource(objFactoryEx.createTimingDomainFragmentTSource(source));

    //  String owner; NOT SUPPORTED
    //JAXBElement<String> owner = objFactoryEx.createTimingDomainFragmentTOwner(tdfrProperties.get(F3SyncSPProperties.VS.Owner));
    //tdfrT.setOwner(owner);

    //  String networkAccessDomain; NOT SUPPORTED
    //JAXBElement<String> networkAccDomain = objFactoryEx.createTimingDomainFragmentTNetworkAccessDomain("");
    //tdfrT.setNetworkAccessDomain(networkAccDomain);

    // layers
    final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_TIMING_DOMAIN);

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_TIMING_DOMAIN,
            LayeredParams.PropAdvaTimingDomain.ALLOCATED_NUMBER,
            String.valueOf(f3SyncRefs.size()));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_TIMING_DOMAIN,
            LayeredParams.PropAdvaTimingDomain.ALLOCATED_MAXIMUM,
            Integer.toString(MtosiConstants.TDFR_SYNC_REFS_MAX));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_TIMING_DOMAIN,
            LayeredParams.PropAdvaTimingDomain.ADMINISTRATION_CONTROL, 
            MtosiUtils.getMtosiString(CMAdministrationControlTranslation.NOT_APPLICABLE,
                                      tdfrProperties.get(F3SyncSPProperties.VI.AdminState)));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_TIMING_DOMAIN,
            LayeredParams.PropAdvaTimingDomain.SERVICE_STATE,
            CMServiceStateTranslation.getMtosiString(tdfrProperties.get(F3SyncSPProperties.VI.AdminState),
                                                     tdfrProperties.get(F3SyncSPProperties.VI.OperState)));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_TIMING_DOMAIN,
            LayeredParams.PropAdvaTimingDomain.SECONDARY_STATE,
            tdfrProperties.get(F3SyncSPProperties.VS.SecondaryState));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_TIMING_DOMAIN,
            LayeredParams.PropAdvaTimingDomain.NETWORK_CLOCK_TYPE,
            MtosiUtils.getMtosiString(CMNetworkClockTypeTranslation.NOT_APPLICABLE,
                                      tdfrProperties.get(F3SyncSPProperties.VI.NetworkClockType)));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT,
            LayeredParams.PROP_ADVA_TIMING_DOMAIN,
            LayeredParams.PropAdvaTimingDomain.SYNC_DOMAIN_TYPE, 
            MtosiUtils.getMtosiString(F3SyncDomainTypeTranslation.NOT_APPLICABLE,
                tdfrProperties.get(F3SyncSPProperties.VI.Domain)));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_TIMING_DOMAIN,
            LayeredParams.PropAdvaTimingDomain.SELECTED_REFERENCE,
        ((F3SyncDAO)f3SyncImpl.getNetworkElement().getDbObjectFactory().getDAO(F3SyncDBImpl.class))
            .createMTOSINameByOID(f3SyncImpl.getNetworkElement().getID(), new OID(tdfrProperties.get(F3SyncSPProperties.VS.SelectedReference))));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_TIMING_DOMAIN,
            LayeredParams.PropAdvaTimingDomain.SYNC_CLOCK_MODE,
            MtosiUtils.getMtosiString(CMSyncClockModeTranslation.NOT_APPLICABLE,
                                      tdfrProperties.get(F3SyncSPProperties.VI.ClockMode)));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_TIMING_DOMAIN,
            LayeredParams.PropAdvaTimingDomain.SSM_QUALITY_LEVEL,
            MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE,
                                      tdfrProperties.get(F3SyncSPProperties.VI.SyncQL)));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_TIMING_DOMAIN,
        LayeredParams.PropAdvaTimingDomain.SYNC_SELECTION_MODE,
        MtosiUtils.getMtosiString(CMSyncSelectionModeTranslation.NOT_APPLICABLE,
                                  tdfrProperties.get(F3SyncSPProperties.VI.SyncSelectionMode)));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_TIMING_DOMAIN,
        LayeredParams.PropAdvaTimingDomain.SYNC_WTR_TIME,
        String.valueOf(tdfrProperties.get(F3SyncSPProperties.VI.SyncWTRTime)));
   // -------end of Layer-------

    tdfrT.setTransmissionParams(objFactoryEx.createTimingDomainFragmentTTransmissionParams(layeredParametersListT));

    //  TDFrVendorExtensionsT vendorExtensions;

    return tdfrT;
  }

  @Override
  public ConnectionTerminationPointListT toMtosiCTPs() throws ProcessingFailureException {
    final ObjectFactory objFactory = new ObjectFactory();
    final ConnectionTerminationPointListT ctpListT = objFactory.createConnectionTerminationPointListT();

    Set<F3SyncRefImpl> f3SyncRefs = f3SyncImpl.getAllF3SyncRefs();

    for (F3SyncRefImpl f3SyncRef : f3SyncRefs) {
      f3SyncRef.doPollingVolatile();
      ConnectionTerminationPointT ctpT = objFactory.createConnectionTerminationPointT();
      F3SyncRefSPProperties refProps = f3SyncRef.getF3SyncRefProperties();

      // CTP Name
      final NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributes(f3SyncImpl);
      namingAttributes.setCtpNm(MtosiConstants.TDFR_REFERENCE_TEXT+Integer.toString(refProps.get(F3SyncRefSPProperties.VI.RefIndex)));
      ctpT.setName(objFactory.createConnectionTerminationPointTName(namingAttributes));

      // discoveredName
      final String ctpNm = namingAttributes.getCtpNm();
      ctpT.setDiscoveredName(objFactory.createConnectionTerminationPointTDiscoveredName(ctpNm));
      // TODO what is the correct discoveredName
      //final Port port = ManagedElementFactory.getPort(f3SyncImpl.getNe(),
      //                                                refProps.get(F3SyncRefSPProperties.VS.SyncReferenceForMTOSI));
      //ctpT.setDiscoveredName(objFactory.createConnectionTerminationPointTDiscoveredName(port.getCTPName()));

      // namingOS
      ctpT.setNamingOS(objFactory.createConnectionTerminationPointTNamingOS(OSFactory.getNmsName()));

      // source
      final SourceT source = new SourceT();
      source.setValue(SourceEnumT.NETWORK_EMS);
      ctpT.setSource(objFactory.createConnectionTerminationPointTSource(source));

      // resource state
      final ResourceStateT resourceState = new ResourceStateT();
      resourceState.setValue(ResourceStateEnumT.INSTALLED);
      ctpT.setResourceState(objFactory.createConnectionTerminationPointTResourceState(resourceState));

      // direction
      ctpT.setDirection(objFactory.createConnectionTerminationPointTDirection(DirectionalityT.D_BIDIRECTIONAL));

      // layers
      final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

      // -------start Layer--------
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_SYNC_REFERENCE);

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SYNC_REFERENCE,
              LayeredParams.PropAdvaSyncReference.SYNC_REFERENCE,
              refProps.get(F3SyncRefSPProperties.VS.SyncReferenceForMTOSI));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SYNC_REFERENCE,
              LayeredParams.PropAdvaSyncReference.ALIAS,
              refProps.get(F3SyncRefSPProperties.VS.Alias));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SYNC_REFERENCE,
              LayeredParams.PropAdvaSyncReference.REF_PRIORITY,
              Integer.toString(refProps.get(F3SyncRefSPProperties.VI.Priority)));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SYNC_REFERENCE,
              LayeredParams.PropAdvaSyncReference.REF_STATUS,
              MtosiUtils.getMtosiString(CMSyncRefStatus.NOT_APPLICABLE,
                                        refProps.get(F3SyncRefSPProperties.VI.Status)));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SYNC_REFERENCE,
              LayeredParams.PropAdvaSyncReference.REF_STATE,
              MtosiUtils.getMtosiString(CMSyncRefState.NOT_APPLICABLE,
                                        refProps.get(F3SyncRefSPProperties.VI.State)));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SYNC_REFERENCE,
              LayeredParams.PropAdvaSyncReference.REF_RECEIVED_QL,
              MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE,
                                        refProps.get(F3SyncRefSPProperties.VI.ReceivedQL)));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SYNC_REFERENCE,
              LayeredParams.PropAdvaSyncReference.REF_EFFECTIVE_QL,
              MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE,
                                        refProps.get(F3SyncRefSPProperties.VI.EffectiveQL)));
      // -------end of Layer-------

      ctpT.setTransmissionParams(objFactory.createConnectionTerminationPointTTransmissionParams(layeredParametersListT));

      ctpListT.getCTP().add(ctpT);
    }

    return ctpListT;
  }

  @Override
  public TPDataListT toMtosiTPs(Set<F3SyncRefImpl> f3SyncRefs) throws ProcessingFailureException {
    final ObjectFactory objFactory = new ObjectFactory();
    final TPDataListT tpDataListT = objFactory.createTPDataListT();

    for (F3SyncRefImpl f3SyncRef : f3SyncRefs) {
      TPDataT tpDataT = objFactory.createTPDataT();
      F3SyncRefSPProperties refProps = f3SyncRef.getF3SyncRefProperties();

      // CTP Name
      final NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributes(f3SyncImpl);
      namingAttributes.setCtpNm(MtosiConstants.TDFR_REFERENCE_TEXT+Integer.toString(refProps.get(F3SyncRefSPProperties.VI.RefIndex)));
      tpDataT.setTpName(namingAttributes);

      // layers
      final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

      // -------start Layer--------
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_SYNC_REFERENCE);

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SYNC_REFERENCE,
              LayeredParams.PropAdvaSyncReference.SYNC_REFERENCE,
              refProps.get(F3SyncRefSPProperties.VS.SyncReferenceForMTOSI));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SYNC_REFERENCE,
              LayeredParams.PropAdvaSyncReference.ALIAS,
              refProps.get(F3SyncRefSPProperties.VS.Alias));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SYNC_REFERENCE,
              LayeredParams.PropAdvaSyncReference.REF_PRIORITY,
              Integer.toString(refProps.get(F3SyncRefSPProperties.VI.Priority)));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SYNC_REFERENCE,
              LayeredParams.PropAdvaSyncReference.REF_STATUS,
              MtosiUtils.getMtosiString(CMSyncRefStatus.NOT_APPLICABLE,
                                        refProps.get(F3SyncRefSPProperties.VI.Status)));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SYNC_REFERENCE,
              LayeredParams.PropAdvaSyncReference.REF_STATE,
              MtosiUtils.getMtosiString(CMSyncRefState.NOT_APPLICABLE,
                                        refProps.get(F3SyncRefSPProperties.VI.State)));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SYNC_REFERENCE,
              LayeredParams.PropAdvaSyncReference.REF_RECEIVED_QL,
              MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE,
                                        refProps.get(F3SyncRefSPProperties.VI.ReceivedQL)));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_SYNC_REFERENCE,
              LayeredParams.PropAdvaSyncReference.REF_EFFECTIVE_QL,
              MtosiUtils.getMtosiString(CMSyncQLTranslation.NOT_APPLICABLE,
                                        refProps.get(F3SyncRefSPProperties.VI.EffectiveQL)));
      // -------end of Layer-------

      tpDataT.setTransmissionParams(objFactory.createConnectionTerminationPointTTransmissionParams(layeredParametersListT));

      tpDataListT.getTpData().add(tpDataT);
    }

    return tpDataListT;
  }

}
