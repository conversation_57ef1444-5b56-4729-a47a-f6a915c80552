/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.serviceProvisioning.ACCPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrEndIDs;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.NETPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.ProtectionGroupF3SPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.WANPortSPProperties;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.Module;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3Impl;
import com.adva.nlms.mediation.config.f3.entity.port.acc.MTOSIPortF3Acc;
import com.adva.nlms.mediation.config.f3.entity.port.net.MTOSIPortF3Net;
import com.adva.nlms.mediation.config.f3.mtosi.NetworkElementFSP150CMMTOSIOperations;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.model.FDFrACCEndFSP150CMIDs;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.model.FDFrFTPEndFSP150CMIDs;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.model.FDFrNETEndFSP150CMIDs;
import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiFDFrMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMPortModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.worker.CreateAndActivateFDFrWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.TPDataListT;
import v1.tmf854.TPDataT;
import v1.tmf854ext.adva.CreateAndActivateFDFrT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.ArrayList;

public class CreateAndActivateFDFrWorkerCM extends CreateAndActivateFDFrWorker
{
	Logger LOG = LogManager.getLogger(this.getClass().getName());

	private MTOSIFlowF3 flow;
	private MTOSIPortF3Net portNET;
	private MTOSIPortF3Net portNET2 = null;
	private FTP ftp = null;
	private MTOSIPortF3Acc portACC;
	private String flowName;
	private boolean isACtp = false;
	private TPDataT tpDataACC;
	private TPDataT tpDataNET;
	private TPDataT tpDataNET2;
	private TPDataT tpDataFlow;
	private TPDataT tpDataFTP;

//	private TPDataT cfmData;

  public CreateAndActivateFDFrWorkerCM(CreateAndActivateFDFrT mtosiBody, Holder<HeaderT> mtosiHeader,
                                       NetworkElement ne, NamingAttributesListT aEnd, NamingAttributesListT zEnd)
  {
    super(mtosiBody, mtosiHeader, ne, aEnd, zEnd);
	}

	@Override
  protected TPDataListT getUpdatedTPs() throws ProcessingFailureException {
		// Get updated objects
		ObjectFactory objectFactory = new ObjectFactory();
		TPDataListT tpsToModify = objectFactory.createTPDataListT();
		if (tpDataACC != null && portACC != null) {
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataACC.getTpName())).toMtosiPTPasTPDataT());
//			tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataACC.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
		if (tpDataNET != null && portNET != null) {
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataNET.getTpName())).toMtosiPTPasTPDataT());
//			tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataNET.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
		if (tpDataNET2 != null && portNET2 != null) {
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataNET2.getTpName())).toMtosiPTPasTPDataT());
//			tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataNET2.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
		if (tpDataFlow != null && flow != null) {
			MTOSIFlowF3 flowUpdated = ManagedElementFactory.getCMFlow(tpDataFlow.getTpName());
			if (flowUpdated != null) {
				tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  null).getMtosiTranslator(flowUpdated).toMtosiCTPasTPDataT());
//				tpsToModify.getTpData().add(flowUpdated.getMtosiTranslator().toMtosiCTPasTPDataT());
			}
		}
		if (tpDataFTP != null && ftp != null) {
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  null).getMtosiTranslator(ManagedElementFactory.getFtp(tpDataFTP.getTpName())).toMtosiFTPasTPDataT());
//			tpsToModify.getTpData().add(ManagedElementFactory.getFtp(tpDataFTP.getTpName()).getMtosiTranslator().toMtosiFTPasTPDataT());
		}
		return tpsToModify;
	}

  private void transact(ACCPortSPPropertiesFSP150CM propsACC, NETPortSPPropertiesFSP150CM propsNET1, NETPortSPPropertiesFSP150CM propsNET2,
			FlowSPPropertiesFSP150CM propsFlow, FDFrSPProperties fdfrProps/* ,CFMCCMSPPropertiesFSP150CM propsCfm*/, ProtectionGroupF3SPProperties propsFTP /*, CFMCCMSPPropertiesFSP150CC propsCfm*/) throws ObjectInUseException,
			NetTransactionException, SNMPCommFailure, SPValidationException {
		NetworkElement locks[] = new NetworkElement[] { ne };
		Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "CreateAndActivateFDFrWorker");
    ne.getMTOSIWorker().setFDFrOperationInProgress(fdfrProps.get(FDFrSPProperties.VS.FDFrName), true);
		try {
			if (propsACC != null) {
				logSecurity(ne, SystemAction.ModifyNetwork, portACC.getMtosiName());
				portACC.setSettings(propsACC);
			}
			if (propsNET1 != null) {
				logSecurity(ne, SystemAction.ModifyNetwork, portNET.getMtosiName());
				portNET.setSettings(propsNET1);
			}
			if (propsNET2 != null) {
				logSecurity(ne, SystemAction.ModifyNetwork, portNET2.getMtosiName());
				portNET2.setSettings(propsNET2);
			}
			if (flow == null) {
				// create the flow
				logSecurity(ne, SystemAction.AddNetwork, portACC.getMtosiName() + "&&" + flowName);
				// todo: portACC.createFlow(propsFlow);
			} else {
				logSecurity(ne, SystemAction.ModifyNetwork, flow.getMtosiName());
				flow.setFlowSettings(propsFlow);
			}
//		    if (propsCfm != null && flow!=null) {
//		    	propsCfm.set(CFMCCMSPPropertiesFSP150CC.VB.GEDeviceType, true);
//		        logSecurity(ne, SystemAction.ModifyNetwork, flow.getMtosiName() + " (CFM)");
//		        flow.setCFMCCMSettings(propsCfm);
//		    }
			if (propsFTP != null) {
				logSecurity(ne, SystemAction.ModifyNetwork, "ftpNm=" + ftp.getFTPName());
				ftp.setFTPSPProperties(propsFTP);
			}
      ne.getMTOSIWorker().createFDFr(fdfrProps);
			NetTransactionManager.commitNetTransaction(id);
      ne.logSROperation(SROperationState.SERVICE_CREATION_SUCCESS, fdfrProps.get(FDFrSPProperties.VS.FDFrName));
		} catch (NetTransactionException e) {
			ne.logSROperation(SROperationState.SERVICE_CREATION_FAILURE, fdfrProps.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SPValidationException e) {
			ne.logSROperation(SROperationState.SERVICE_CREATION_FAILURE, fdfrProps.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SNMPCommFailure e) {
			ne.logSROperation(SROperationState.SERVICE_CREATION_FAILURE, fdfrProps.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} finally {
      ne.getMTOSIWorker().setFDFrOperationInProgress(fdfrProps.get(FDFrSPProperties.VS.FDFrName), false);
			NetTransactionManager.ensureEnd(id);
		}
	}

  private void mediate_LAG() throws Exception {
		NamingAttributesT aName = aEnd.getName().get(0);
		NamingAttributesT zName = zEnd.getName().get(0);
		String accPortName = null;
		Port portA;
		String ftpName;
		if (NamingTranslationFactory.isFlow(aName) && NamingTranslationFactory.isFtp(zName)) {
			isACtp = true; // flow is the A side
			checkFTPExistence(zName);
			portA = ManagedElementFactory.getPort(aEnd.getName().get(0));
			flow = ManagedElementFactory.getCMFlow(aEnd.getName().get(0));
			flowName = aEnd.getName().get(0).getCtpNm();
			ftpName = zEnd.getName().get(0).getFtpNm();
			final String portName = aEnd.getName().get(0).getPtpNm();
			accPortName = NamingTranslationFactory.portNameFromShelfCombo(portName);

		} else if (NamingTranslationFactory.isFlow(zName) && NamingTranslationFactory.isFtp(aName)) {
			checkFTPExistence(aName);
			portA = ManagedElementFactory.getPort(zEnd.getName().get(0));
			flow = ManagedElementFactory.getCMFlow(zEnd.getName().get(0));
			flowName = zEnd.getName().get(0).getCtpNm();
			ftpName = aEnd.getName().get(0).getFtpNm();
			final String portName = zEnd.getName().get(0).getPtpNm();
			accPortName = NamingTranslationFactory.portNameFromShelfCombo(portName);
		} else {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_TP_INVALID_ENDPOINT,
					"The specified endpoints must be a FTP and a CTP.");
  			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		boolean newFlow = false;
		portACC = (MTOSIPortF3Acc) portA;
		MTOSIPortF3Net ftpAPort = (MTOSIPortF3Net) ftp.getAPort();
		if (portACC.getPortSPProperties().get(NETPortSPPropertiesFSP150CM.VI.ShelfIndex) != ftpAPort.getPortSPProperties().get(
				NETPortSPPropertiesFSP150CM.VI.ShelfIndex)) {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
					"aEnd and zEnd are not on the same Shelf.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		if (portACC.getPortSPProperties().get(NETPortSPPropertiesFSP150CM.VI.SlotIndex) != ftpAPort.getPortSPProperties().get(
				NETPortSPPropertiesFSP150CM.VI.SlotIndex)) {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
					"aEnd and zEnd are not on the same Slot.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		Integer flowIndex = 1;
		if (((ACCPortSPPropertiesFSP150CM) portACC.getPortSPProperties()).get(ACCPortSPPropertiesFSP150CM.VI.SvcType).equals(
				CMPortModeTranslation.CO_EPL.getPortSvcTypeValue())) {
			flow = portACC.getFlowFSP150CM(1);
			newFlow = true; // todo: mandatory parameters?
			if (flow.getFDFr() != null) {
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_OBJECT_IN_USE,
						"An EPL Port cannot support more than one Flow.");
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
		} else {
			// todo: EVPL !!!
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_NOT_IMPLEMENTED,
      "EVPL Mode is not supported.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
		}

		NETPortSPPropertiesFSP150CM props = portACC.getPortSPProperties();
		int shelfIndex = props.get(NETPortSPPropertiesFSP150CM.VI.ShelfIndex);
		int slotIndex = props.get(NETPortSPPropertiesFSP150CM.VI.SlotIndex);
		int portIndex = (props.get(WANPortSPProperties.VI.Index) != null) ? props.get(WANPortSPProperties.VI.Index) : 1;
		portNET = (MTOSIPortF3Net) ne.getMTOSIWorker().getPortByName(MtosiConstants.PORT_NET1, shelfIndex, slotIndex);
		portNET2 = (MTOSIPortF3Net) ne.getMTOSIWorker().getPortByName(MtosiConstants.PORT_NET2, shelfIndex, slotIndex);
		ACCPortSPPropertiesFSP150CM propsACC = null;
		NETPortSPPropertiesFSP150CM propsNET = null;
		NETPortSPPropertiesFSP150CM propsNET2 = null;
		FlowSPPropertiesFSP150CM propsFlow = null;
		ProtectionGroupF3SPProperties propsFTP = null;
//		CFMCCMSPPropertiesFSP150CC propsCfm = null;

		if (tpsToModify != null) {
			int modifiedSlotIndex = slotIndex;
			if (ManagedElementFactory.needsSlotIncrement(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
				modifiedSlotIndex -= 1;
			}
			if (!MtosiTPMediator.checkTPToModifySameCard(ne, tpsToModify, shelfIndex, modifiedSlotIndex, ftpName)) {
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
						"The specified TPs must be on the same slot.");
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
			tpDataACC = MtosiTPMediator.getTPDataTForCMACC(tpsToModify);
			tpDataNET = MtosiTPMediator.getTPDataTForCMNET(tpsToModify, 1);
			tpDataNET2 = MtosiTPMediator.getTPDataTForCMNET(tpsToModify, 2);
			tpDataFlow = MtosiTPMediator.getTPDataTForCTP(tpsToModify);
			tpDataFTP = MtosiTPMediator.getTPDataTForFTP(tpsToModify);
//			cfmData = MtosiTPMediator.getTPDataTForCCCFM(tpsToModify);
		}
		if (tpDataFlow != null) {
			validateFlowName(flowName,tpDataFlow);
			propsFlow = getCTPProperties();
//			if (cfmData != null) {
//				propsCfm = MtosiTPMediator.mtosiTPDataTToCC_CFMProperties(cfmData, newFlow);
//			}
		}
		if (tpDataACC != null) {
			propsACC = MtosiTPMediator.mtosiTPDataTToCMACCProperties(tpDataACC, portACC);
		}
		if (tpDataNET != null) {
			propsNET = MtosiTPMediator.mtosiTPDataTToCMNETProperties(tpDataNET, portNET);
		}
		if (tpDataNET2 != null) {
			propsNET2 = MtosiTPMediator.mtosiTPDataTToCMNETProperties(tpDataNET2, portNET2);
		}
		if (tpDataFTP != null) {
			propsFTP = MtosiTPMediator.mtosiTPDataTToCMFTPProperties(tpDataFTP, ftp);
		}
		FDFrFTPEndFSP150CMIDs netEnds = new FDFrFTPEndFSP150CMIDs(shelfIndex, slotIndex, ftpName, 1, 2);
		FDFrACCEndFSP150CMIDs accEnds = new FDFrACCEndFSP150CMIDs(shelfIndex, slotIndex, accPortName, portIndex);
		ArrayList<FDFrEndIDs> netEndsIDs = new ArrayList<FDFrEndIDs>();
		netEndsIDs.add(netEnds);
		ArrayList<FDFrEndIDs> accEndsIDs = new ArrayList<FDFrEndIDs>();
		accEndsIDs.add(accEnds);
		FDFrSPProperties fdfrProps;
		if (!isACtp) {
			fdfrProps = MtosiFDFrMediator.mtosiCreateFDFRToFDFRSPProperties(createData, netEndsIDs, accEndsIDs);
		} else {
			fdfrProps = MtosiFDFrMediator.mtosiCreateFDFRToFDFRSPProperties(createData, accEndsIDs, netEndsIDs);
		}

		if (propsFlow == null) {
			propsFlow = new FlowSPPropertiesFSP150CM();
//			propsFlow.set(FlowSPPropertiesFSP150CM.VB.Active, true);
		}
		propsFlow.set(FlowSPPropertiesFSP150CM.VI.ShelfIndex, shelfIndex);
		propsFlow.set(FlowSPPropertiesFSP150CM.VI.SlotIndex, slotIndex);
		propsFlow.set(FlowSPPropertiesFSP150CM.VI.AccPortIndex, portACC.getIndex().toIntArray()[portACC.getIndex().toIntArray().length-1]);
		propsFlow.set(FlowSPPropertiesFSP150CM.VI.FlowIndex, flowIndex);
		propsFlow.set(FlowSPPropertiesFSP150CM.VS.CircuitName, NamingTranslationFactory.getFlowNameWithFDFr(fdfrProps.get(FDFrSPProperties.VS.FDFrName),
				flowName));
		transact(propsACC, propsNET, propsNET2, propsFlow, fdfrProps, propsFTP /*, propsCfm*/);
	}

	/**
	 * Extract the device specific properties
	 * @return
	 */
	protected FlowSPPropertiesFSP150CM getCTPProperties()  throws ProcessingFailureException{
	  boolean isNteModule = isNteModule(getFlow());
		return MtosiTPMediator.mtosiTPDataTToCM_CTPProperties(getTpDataFlow(), getFlow(),isNteModule);
	}

	/**
	 * Checks if the Flow is on an NTE Card
	 * @param flow
	 * @return
	 */
  	private boolean isNteModule(MTOSIFlowF3 flow) {
      FlowSPPropertiesFSP150CM props = flow.getFlowSPProperties();
      int shelfIndex = props.get(FlowSPPropertiesFSP150CM.VI.ShelfIndex);
      int slotIndex = props.get(FlowSPPropertiesFSP150CM.VI.SlotIndex);
      Module module = ((NetworkElementFSP150CMMTOSIOperations) ((NetworkElement)flow.getPortFSP150CMAcc().getNE()).getMTOSIWorker()).getModule(shelfIndex, slotIndex);
      if (module instanceof NteF3Impl)
        return true;
      return false;
  }

    private void checkFTPExistence (NamingAttributesT ftpName)
          throws ProcessingFailureException
    {
  		ftp = ManagedElementFactory.getFtp(ftpName);
    }

  	private void mediate_NoLAG() throws Exception {
		if (NamingTranslationFactory.isFtp(aEnd.getName().get(0))) {
			checkFTPExistence(aEnd.getName().get(0));
		}
		if (NamingTranslationFactory.isFtp(zEnd.getName().get(0))) {
			checkFTPExistence(zEnd.getName().get(0));
		}
		if (!MtosiUtils.isSameShelf(aEnd.getName().get(0), zEnd.getName().get(0))) {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
					"aEnd and zEnd are not on the same Shelf.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		if (!MtosiUtils.isSameSlot(aEnd.getName().get(0), zEnd.getName().get(0))) {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
					"aEnd and zEnd are not on the same Slot.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		NamingAttributesT aName = aEnd.getName().get(0);
		NamingAttributesT zName = zEnd.getName().get(0);
		Port portN;
		Port portA;
		String accPortName = null;
		if (NamingTranslationFactory.isFlow(aName) && NamingTranslationFactory.isPort(zName)) {
			isACtp = true;
			portN = ManagedElementFactory.getPort(zEnd.getName().get(0));
			portA = ManagedElementFactory.getPort(aEnd.getName().get(0));
			flow = ManagedElementFactory.getCMFlow(aEnd.getName().get(0));
			flowName = aEnd.getName().get(0).getCtpNm();
			final String portName = aEnd.getName().get(0).getPtpNm();
			accPortName = NamingTranslationFactory.portNameFromShelfCombo(portName);
		} else if (NamingTranslationFactory.isFlow(zName) && NamingTranslationFactory.isPort(aName)) {
			portN = ManagedElementFactory.getPort(aEnd.getName().get(0));
			portA = ManagedElementFactory.getPort(zEnd.getName().get(0));
			flow = ManagedElementFactory.getCMFlow(zEnd.getName().get(0));
			flowName = zEnd.getName().get(0).getCtpNm();
			final String portName = zEnd.getName().get(0).getPtpNm();
			accPortName = NamingTranslationFactory.portNameFromShelfCombo(portName);
		} else {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_TP_INVALID_ENDPOINT,
					"The specified endpoints must be a PTP and a CTP.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		boolean newFlow = false;
		portACC = (MTOSIPortF3Acc) portA;
		Integer flowIndex = 1;
		if (((ACCPortSPPropertiesFSP150CM) portACC.getPortSPProperties()).get(ACCPortSPPropertiesFSP150CM.VI.SvcType).equals(
				CMPortModeTranslation.CO_EPL.getPortSvcTypeValue())) {
			flow = portACC.getFlowFSP150CM(1);
			newFlow = true; // todo: mandatory parameters?
			if (flow.getFDFr() != null) {
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_OBJECT_IN_USE,
						"An EPL Port cannot support more than one Flow.");
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
		} else {
			// todo: EVPL !!!
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_NOT_IMPLEMENTED,
      "EVPL Mode is not supported.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
		}

		if (!(portN instanceof MTOSIPortF3Net)) {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
					"Specified Port is not a NET Port.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		portNET = (MTOSIPortF3Net) portN;
		if (portNET.getMtosiName().indexOf(MtosiConstants.PORT_NET2) > -1) {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_TP_INVALID_ENDPOINT,
					"NET-2 Port cannot be the endpoint of an FDFr.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		ACCPortSPPropertiesFSP150CM propsACC = null;
		NETPortSPPropertiesFSP150CM propsNET = null;
		FlowSPPropertiesFSP150CM propsFlow = null;
//		CFMCCMSPPropertiesFSP150CC propsCfm = null;
		if (tpsToModify != null) {
			NETPortSPPropertiesFSP150CM props = portACC.getPortSPProperties();
			int shelfIndex = props.get(NETPortSPPropertiesFSP150CM.VI.ShelfIndex);
			int modifiedSlotIndex = props.get(NETPortSPPropertiesFSP150CM.VI.SlotIndex);
			if (ManagedElementFactory.needsSlotIncrement(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
				modifiedSlotIndex -= 1; // Need to decrement it for CM, since
				// the GUI and the NE index's don't
				// match.
			}
			if (!MtosiTPMediator.checkTPToModifySameCard(ne, tpsToModify, shelfIndex, modifiedSlotIndex, null)) {
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
						"The specified TPs must be on the same slot.");
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
			tpDataACC = MtosiTPMediator.getTPDataTForCMACC(tpsToModify);
			tpDataNET = MtosiTPMediator.getTPDataTForCMNET(tpsToModify, 1);
			tpDataFlow = MtosiTPMediator.getTPDataTForCTP(tpsToModify);
//			cfmData = MtosiTPMediator.getTPDataTForCCCFM(tpsToModify);
		}
		if (tpDataFlow != null) {
			validateFlowName(flowName,tpDataFlow);
			propsFlow = getCTPProperties();
//			if (cfmData != null) {
//				propsCfm = MtosiTPMediator.mtosiTPDataTToCC_CFMProperties(cfmData, newFlow);
//			}
		}
		if (tpDataACC != null) {
			propsACC = MtosiTPMediator.mtosiTPDataTToCMACCProperties(tpDataACC, portACC);
		}
		if (tpDataNET != null) {
			propsNET = MtosiTPMediator.mtosiTPDataTToCMNETProperties(tpDataNET, portNET);
		}
		NETPortSPPropertiesFSP150CM portNETprops = portNET.getPortSPProperties();
		FDFrNETEndFSP150CMIDs netEnds = new FDFrNETEndFSP150CMIDs(portNETprops.get(NETPortSPPropertiesFSP150CM.VI.ShelfIndex), portNETprops
				.get(NETPortSPPropertiesFSP150CM.VI.SlotIndex));
		ACCPortSPPropertiesFSP150CM portACCprops = (ACCPortSPPropertiesFSP150CM) portACC.getPortSPProperties();
		int portIndex = (portACCprops.get(WANPortSPProperties.VI.Index) != 0) ? portACCprops.get(WANPortSPProperties.VI.Index) : 1;
		FDFrACCEndFSP150CMIDs accEnds = new FDFrACCEndFSP150CMIDs(portACCprops.get(NETPortSPPropertiesFSP150CM.VI.ShelfIndex), portACCprops
				.get(NETPortSPPropertiesFSP150CM.VI.SlotIndex), accPortName, portIndex);
		ArrayList<FDFrEndIDs> netEndsIDs = new ArrayList<FDFrEndIDs>();
		netEndsIDs.add(netEnds);
		ArrayList<FDFrEndIDs> accEndsIDs = new ArrayList<FDFrEndIDs>();
		accEndsIDs.add(accEnds);
		FDFrSPProperties fdfrProps;
		if (!isACtp) {
			fdfrProps = MtosiFDFrMediator.mtosiCreateFDFRToFDFRSPProperties(createData, netEndsIDs, accEndsIDs);
		} else {
			fdfrProps = MtosiFDFrMediator.mtosiCreateFDFRToFDFRSPProperties(createData, accEndsIDs, netEndsIDs);
		}

		if (propsFlow == null) {
			propsFlow = new FlowSPPropertiesFSP150CM();
//			propsFlow.set(FlowSPPropertiesFSP150CM.VB.Active, true);
		}
		propsFlow.set(FlowSPPropertiesFSP150CM.VI.ShelfIndex, portACCprops.get(NETPortSPPropertiesFSP150CM.VI.ShelfIndex));
		propsFlow.set(FlowSPPropertiesFSP150CM.VI.SlotIndex, portACCprops.get(NETPortSPPropertiesFSP150CM.VI.SlotIndex));
		propsFlow.set(FlowSPPropertiesFSP150CM.VI.AccPortIndex, portACC.getIndex().toIntArray()[portACC.getIndex().toIntArray().length-1]);
		propsFlow.set(FlowSPPropertiesFSP150CM.VI.FlowIndex, flowIndex);
		propsFlow.set(FlowSPPropertiesFSP150CM.VS.CircuitName, NamingTranslationFactory.getFlowNameWithFDFr(fdfrProps.get(FDFrSPProperties.VS.FDFrName),
				flowName));
		transact(propsACC, propsNET, null, propsFlow, fdfrProps, null /*, propsCfm*/);
	}

  	protected void validateFlowName(String flowName, TPDataT tpDataFlow) throws ProcessingFailureException {
		if (flowName != null && !flowName.equals(tpDataFlow.getTpName().getCtpNm())) {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
					"End Point CTP name must match Flow Point CTP name in tpsToModify.");
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
	}

	@Override
  protected void mediate() throws Exception {
		String ptpName = aEnd.getName().get(0).getPtpNm();
		if (ptpName == null) {
			ptpName = zEnd.getName().get(0).getPtpNm();
		}
		if (ptpName == null) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, "ptp name is missing.");
		}
		int shelfIndex = NamingTranslationFactory.shelfNumberFromShelfCombo(ptpName);
		int slotIndex = NamingTranslationFactory.slotNumberFromShelfCombo(ptpName);
		if (ManagedElementFactory.needsSlotIncrement(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
			slotIndex += 1;
		}

    NetworkElementFSP150CMMTOSIOperations mtosiWorker = getFSP150CMMTOSIWorker((NetworkElementFSP150CM) ne);
		if (mtosiWorker.isPGEnabled(shelfIndex, slotIndex)) {
			mediate_LAG();
		} else {
			mediate_NoLAG();
		}
	}

	/*
	 * Getters  to help the subclass gain access.
	 */
	protected MTOSIFlowF3 getFlow() {
		return flow;
	}

	protected TPDataT getTpDataFlow() {
		return tpDataFlow;
	}

}
