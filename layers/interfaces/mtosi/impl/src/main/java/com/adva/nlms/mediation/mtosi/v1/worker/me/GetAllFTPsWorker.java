/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import jakarta.xml.ws.Holder;
import v1.tmf854.FloatingTerminationPointListT;
import v1.tmf854.GetAllFTPsResponseT;
import v1.tmf854.GetAllFTPsT;
import v1.tmf854.HeaderT;
import v1.tmf854.LayerRateListT;
import v1.tmf854.NamingAttributesT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;

public class GetAllFTPsWorker extends AbstractMtosiWorker {
  protected GetAllFTPsT mtosiBody;
  protected GetAllFTPsResponseT response = new GetAllFTPsResponseT();
  protected NamingAttributesT meName;
  protected FloatingTerminationPointListT floatingTerminationPointListT;
  private NetworkElement ne;

  public GetAllFTPsWorker(final GetAllFTPsT mtosiBody, final Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getAllFTPs", "getAllFTPs", "getAllFTPsResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    if ((meName = mtosiBody.getMeName()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.ME_NAME_MISSING);
    }

    if (!NamingTranslationFactory.isManagementDomain(meName)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!meName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MD_NOT_FOUND);
    }
    
    ne = ManagedElementFactory.getAndValidateNE(meName);
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	//Assumption: parse() is called before validate()
  	validator.validate(ne.getDefaultNetworkElementTypeString());
  }

  @Override
  protected void mediate() throws Exception {
    final LayerRateListT tpLayerRateListT = mtosiBody.getTpLayerRateList();
    final LayerRateListT connectionLayerRateList = mtosiBody.getConnectionLayerRateList();
    floatingTerminationPointListT = ManagedElementFactory.getAllFTPs(meName, ne, tpLayerRateListT, connectionLayerRateList);
  }

  @Override
  protected void response() throws Exception {
    response.setFtpList(floatingTerminationPointListT);
  }

  @Override
  public GetAllFTPsResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
