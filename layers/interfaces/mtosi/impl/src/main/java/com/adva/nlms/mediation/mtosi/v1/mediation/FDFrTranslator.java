/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation;

import java.util.Collection;
import v1.tmf854.ConnectionDirectionT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.SNCStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import v1.tmf854ext.adva.FlowDomainFragmentT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPProperties;
import com.adva.nlms.mediation.config.mtosi.FDFr;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrEndIDs;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;

/**
 * This class is a generic FDFr MTOSI Translator.
 */
public abstract class FDFrTranslator extends MtosiTranslator {
  private FDFr fdfr;

  public FDFrTranslator(FDFr fdfr) {
    this.fdfr = fdfr;
  }

  @Override
  public FlowDomainFragmentT toMtosiFDFr() throws ProcessingFailureException {
    final v1.tmf854ext.adva.ObjectFactory objFactoryEx = new v1.tmf854ext.adva.ObjectFactory();
    final ObjectFactory objFactory = new ObjectFactory();
    final FlowDomainFragmentT flowDomainFragmentT = objFactoryEx.createFlowDomainFragmentT();
    FDFrSPProperties fdfrSPProperties;
//	try {
		fdfrSPProperties = fdfr.getFDFrSPProperties();

//This is a handy mechanism to identify potential "internal Errors." so I am 
//going to leave this commented code here for now.
		//	} catch (Exception e) {
//		NamingAttributesT namingAttributes = new NamingAttributesT();
//		namingAttributes.setMdNm(OSFactory.getMDNm());
//		namingAttributes.setFdfrNm("Exception");
//		flowDomainFragmentT.setName(objFactoryEx.createFlowDomainFragmentTName(namingAttributes));
//	    flowDomainFragmentT.setUserLabel(objFactoryEx.createFlowDomainFragmentTUserLabel(e.toString()+" on fdfr.getFDFrSPProperties()"));
//
//		return flowDomainFragmentT;
//	}

    // Fdfr Name
    final NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributes(fdfr);
    flowDomainFragmentT.setName(objFactoryEx.createFlowDomainFragmentTName(namingAttributes));

    // discoveredName
    final String fdfrNm = namingAttributes.getFdfrNm();
    if (fdfrNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FDFR_NOT_FOUND);
    }
    flowDomainFragmentT.setDiscoveredName(objFactoryEx.createFlowDomainFragmentTDiscoveredName(fdfrNm));

    // namingOS
    flowDomainFragmentT.setNamingOS(objFactoryEx.createFlowDomainFragmentTNamingOS(OSFactory.getNmsName()));

    // userLabel
    flowDomainFragmentT.setUserLabel(objFactoryEx.createFlowDomainFragmentTUserLabel(fdfrSPProperties.get(FDFrSPProperties.VS.UserLabel)));

    // source
    final SourceT source = new SourceT();
    source.setValue(SourceEnumT.OS);
    flowDomainFragmentT.setSource(objFactoryEx.createFlowDomainFragmentTSource(source));

    // direction
    flowDomainFragmentT.setDirection(objFactoryEx.createFlowDomainFragmentTDirection(ConnectionDirectionT.CD_BI));

    createFDFrEndPoints(objFactoryEx, flowDomainFragmentT, fdfrSPProperties);

    // fdfrState
    flowDomainFragmentT.setFdfrState(objFactoryEx.createFlowDomainFragmentTFdfrState(getFDFrState()));

    // fdfrType - All FDFrs for all devices will have an FDFrType of FDFRT_POINT_TO_POINT     
    flowDomainFragmentT.setFdfrType(objFactoryEx.createFlowDomainFragmentTFdfrType("FDFRT_POINT_TO_POINT"));

    // layers
    final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    createLayeredParameters(layeredParametersListT);

    flowDomainFragmentT.setTransmissionParams(objFactoryEx.createFlowDomainFragmentTTransmissionParams(layeredParametersListT));
    return flowDomainFragmentT;
  }

	protected SNCStateT getFDFrState() {
		return SNCStateT.SNCS_ACTIVE;
	}
  
  
  	/**
  	 * Create and set the Endpoint information into the flowDomainFragementT
  	 * @param objFactoryEx
  	 * @param flowDomainFragmentT
  	 * @param fdfrSPProperties
  	 * @throws ProcessingFailureException
  	 */

	protected void createFDFrEndPoints(final v1.tmf854ext.adva.ObjectFactory objFactoryEx, final FlowDomainFragmentT flowDomainFragmentT,
			FDFrSPProperties fdfrSPProperties) throws ProcessingFailureException {
		// aEnd
	    flowDomainFragmentT.setAEnd(objFactoryEx.createFlowDomainFragmentTAEnd(getEndPointNamingAttributesListT(fdfrSPProperties.get(FDFrSPProperties.VC.AEnds))));
	
	    // zEnd
	    flowDomainFragmentT.setZEnd(objFactoryEx.createFlowDomainFragmentTZEnd(getEndPointNamingAttributesListT(fdfrSPProperties.get(FDFrSPProperties.VC.ZEnds))));
	}

  /**
   * Allow subclasses to override the createLayeredParameters.
   * Create Empty Ethernet and ADVA_Ethernet layers. For the NON-Hatteras devices
   * @param layeredParametersListT
   */
	protected void createLayeredParameters(final LayeredParametersListT layeredParametersListT) {
		// -------start Layer--------
	    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
	    // -------end of Layer-------
	
	    // -------start Layer--------
	    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET);
	    // -------end of Layer-------
	}

  /**
   * Returns list of corresponding naming attributes of the specific multiEndPoint.
   *
   * @param endPointsCollection The collection of FDFr's endpoints
   * @return JAXBElement<NamingAttributesListT>
   * @throws ws.v1.tmf854.ProcessingFailureException If something went wrong
   */
  abstract protected NamingAttributesListT getEndPointNamingAttributesListT(
          final Collection<FDFrEndIDs> endPointsCollection) throws ProcessingFailureException;
}
