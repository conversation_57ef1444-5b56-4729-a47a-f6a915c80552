/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.LAGSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000BondingSPProperties;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.hn4000.mtosi.FTPHN4000;
import com.adva.nlms.mediation.config.hn4000.FlowHN4000;
import com.adva.nlms.mediation.config.hn4000.NetworkElementHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet2BASE_TL;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
//import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiSupported;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.hn4000.FlowHN4000Translator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import v1.tmf854.ConnectionTerminationPointT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.PhysicalTerminationPointT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.HashMap;
import java.util.Set;

public class GetTPWorkerHN extends GetTPWorker {
	NetworkElementHN4000 ne4000;

	public GetTPWorkerHN(Holder<HeaderT> mtosiHeader, NamingAttributesT tpName, NetworkElement ne) {
		super(mtosiHeader, tpName, ne);
		ne4000 = (NetworkElementHN4000) ne;
	}

	/**
	 * Handles CTP requests.
	 * 
	 * @throws Exception
	 *             When something goes wrong
	 */
	@Override
  protected void executeGetCTP() throws Exception {
		final Port port = ManagedElementFactory.getPort(tpName);

		if (port instanceof PortHN4000Ethernet) {
			ConnectionTerminationPointT ctp = null;
			String ctpName = tpName.getCtpNm();
			final Set<FlowHN4000> flows = ((PortHN4000Ethernet) port).getFlows();
			if (flows == null || flows.isEmpty()) {
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
			}
			for (FlowHN4000 flow : flows) {

				if (ctpName.equals(flow.getFlowSPProperties().get(FlowSPPropertiesHN4000.VS.Desc))) {
					flow.doPollingVolatile();
					ctp = (new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator( flow)).toMtosiCTP();
//					ctp = ((MtosiSupported) flow).getMtosiTranslator().toMtosiCTP();
					break;
				}
			}
			if (ctp == null) {
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
			}

			response.setCtp(ctp);
		}
	}

	/**
	 * Handles FTP requests
	 * 
	 * @throws Exception
	 *             When something goes wrong
	 */
	@Override
	protected void executeGetFTP() throws Exception {
		FTPHN4000 ftp = ManagedElementFactory.getHN4000Ftp(tpName);
		if (ftp != null) {
			ftp.doPollingVolatile();
//			response.setFtp(ftp.getMtosiTranslator().toMtosiFTP());
			response.setFtp(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ftp).toMtosiFTP());
		} else {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
		}
	}

	/**
	 * Handles CTP requests for FTP.
	 * 
	 * @throws Exception
	 *             When something goes wrong
	 */
	@Override
  protected void executeGetCTPOnFTP() throws Exception {
		String ftpName = tpName.getFtpNm();
		ConnectionTerminationPointT ctp = null;
		int fragmentNum = -1;
		final String flowName = tpName.getCtpNm();
		if (flowName.indexOf("lag_fragment=") > 0) {
			fragmentNum = NamingTranslationFactory.getLAGFragmentIndex(flowName);
		}
		if (ftpName.indexOf("ETH") > 0) {

			PortHN4000Ethernet2BASE_TL ftp = ne4000.getBondedPort(ftpName);
			if (ftp == null) {
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
			}
			if (fragmentNum > 0) {
				ftp.getPortSPProperties().get(PortHN4000BondingSPProperties.VH.PmePortMap);
				ctp = executeGetBabyEthCTP(ftp, ftp.getPortSPProperties().get(PortHN4000BondingSPProperties.VH.PmePortMap),fragmentNum);
			} else {
				Set<FlowHN4000> flows = ftp.getFlows();
				for (FlowHN4000 flow : flows) {
					if (flowName.equals(flow.getFlowSPProperties().get(FlowSPPropertiesHN4000.VS.Desc))) {
						flow.doPollingVolatile();
						ctp = (new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator( flow)).toMtosiCTP();
//						ctp = ((MtosiSupported) flow).getMtosiTranslator().toMtosiCTP();
					}
				}
			}
		} else if (ftpName.indexOf("LAG") > 0) {
			FTPHN4000 ftp = (FTPHN4000) ne4000.getMTOSIWorker().getFTP(ftpName);
			if (ftp == null) {
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
			}
			if (fragmentNum > 0) {
				ctp = executeGetBabyLagCTP(ftp, ((LAGSPPropertiesHN4000) ftp.getFTPSPProperties()).get(LAGSPPropertiesHN4000.VH.EthPortMap), fragmentNum);
			} else {
				Set<FlowHN4000> flows = ftp.getFlows();
				for (FlowHN4000 flow : flows) {
					if (flowName.equals(flow.getFlowSPProperties().get(FlowSPPropertiesHN4000.VS.Desc))) {
						flow.doPollingVolatile();
						ctp = (new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(flow)).toMtosiCTP();
//						ctp = ((MtosiSupported) flow).getMtosiTranslator().toMtosiCTP();
					}
				}
			}
		}
		if (ctp == null) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
		}
		response.setCtp(ctp);
	}
	/**
	 * Create the Lag Fragment CTP for an Ethernet Port iff the numbers match. 
	 * @param ftp
	 * @param map
	 * @param fragmentNum
	 * @return
	 * @throws ProcessingFailureException 
	 */
	private ConnectionTerminationPointT executeGetBabyEthCTP(PortHN4000Ethernet2BASE_TL ftp, HashMap<Integer, Set<Integer>> map, int fragmentNum) throws ProcessingFailureException {
		int shelf = (fragmentNum / 40) + 1;
		int port = fragmentNum % 40;
		if (port == 0) {
			port = 40;
			shelf--;
		}
		Set<Integer> portList = map.get(shelf);
		if (portList == null || !portList.contains(port)) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
		}
		String lagMember = "/shelf=" + shelf + "/slot=1/port=2BPME-" + port;
		String ctpName = "/lag_fragment=" + fragmentNum;
		return FlowHN4000Translator.toMtosiSimpleCTP(ftp, ctpName, lagMember, null);

	}

	/**
	 * Create the Lag Fragment CTP for an Ethernet Port iff the numbers match. 
	 * @param ftp
	 * @param map
	 * @param fragmentNum
	 * @return
	 * @throws ProcessingFailureException 
	 */
	private ConnectionTerminationPointT executeGetBabyLagCTP(FTPHN4000 ftp, HashMap<Integer, Set<Integer>> map, int fragmentNum) throws ProcessingFailureException {
		int shelf = (fragmentNum / 2) + 1;
		int port = fragmentNum % 2;
		if (port == 0) {
			port = 2;
			shelf--;
		}
		Set<Integer> portList = map.get(shelf);
		if (portList == null || !portList.contains(port)) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NOT_FOUND);
		}
		String lagMember = "/shelf=" + shelf + "/slot=2/port=ETH-" + port;
		String ctpName = "/lag_fragment=" + fragmentNum;
		return FlowHN4000Translator.toMtosiSimpleCTP(ftp, ctpName, lagMember, null);

	}

	/**
	 * Handles PTP requests.
	 * 
	 * @throws Exception
	 *             When something goes wrong
	 */
	@Override
	protected void executeGetPTP() throws Exception {
		final Port port = ManagedElementFactory.getPort(tpName);
		PhysicalTerminationPointT ptp;

		if (!(port instanceof PortHN4000)) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
		}
		((PortHN4000) port).doPollingVolatile();
		ptp = (new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator( port)).toMtosiPTP();
//		ptp = ((MtosiSupported) port).getMtosiTranslator().toMtosiPTP();
		response.setPtp(ptp);
	}

}