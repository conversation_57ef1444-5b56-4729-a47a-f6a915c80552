/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.common.snmp.MIBFSP150CC825;
import com.adva.nlms.common.snmp.MIBFSP150CP;
import com.adva.nlms.mediation.config.fsp150cp_mx.BitFieldHelper;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;

/**
 * Created by IntelliJ IDEA. User: Lukasz Date: 2007-05-30 Time: 13:14:09 To change this template use File | Settings |
 * File Templates.
 */
public enum AdministrativeSpeedRateTranslation{
  A1000HD        (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_COPPER_VALUE, 15, "1000", "Half", "Enabled"),
  A1000FD        (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_COPPER_VALUE, 14, "1000", "Full", "Enabled"), // 0 not allowed in WAN anymore, OS must specify this combination.
  HD1000         (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_COPPER_VALUE, 13, "1000", "Half", "Disabled"),
  FD1000         (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_COPPER_VALUE, 12, "1000", "Full", "Disabled"),
  S_NONE         (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_COPPER_VALUE, 11, "0", "Full", "Enabled"),
  SN             (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_COPPER_VALUE, 10, "0", "Full", "Enabled"),
  A100HD         (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_COPPER_VALUE, 9, "100", "Half", "Enabled"),
  A100FD         (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_COPPER_VALUE, 8, "100", "Full", "Enabled"),
  AF             (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_COPPER_VALUE, 5, "0", "Full", "Enabled"),//LAN port, mapping for auto/full (which is MTOSI 0): Fast Ethernet 100 and GBE 1000
  AH			 (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_COPPER_VALUE, 5, "0", "Half", "Enabled"),//LAN port, mapping for auto/half (which is MTOSI 0): Fast Ethernet 100 and GBE 1000
  HD100          (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_COPPER_VALUE, 4, "100", "Half", "Disabled"),
  FD100          (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_COPPER_VALUE, 3, "100", "Full", "Disabled"),
  A10HD          (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_COPPER_VALUE, 7, "10", "Half", "Enabled"),
  A10FD          (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_COPPER_VALUE, 6, "10", "Full", "Enabled"),
  HD10           (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_COPPER_VALUE, 2, "10", "Half", "Disabled"),
  FD10           (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_COPPER_VALUE, 1, "10", "Full", "Disabled"),
  FIBER_A1000FD  (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_FIBER_VALUE, 14, "1000", "Full", "Enabled"),
  FIBER_1000FD  (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_FIBER_VALUE, 12, "1000", "Full", "Disabled"),
  FFS100FD       (MIBFSP150CC825.EthernetPBWANTable.MEDIA_TYPE_FIBER_VALUE, 3, "100", "Full", "Disabled"),
  F_EL           (MIBFSP150CP.If.TYPE_EL_ACC, 1, "", "Full", ""),
  H_EL           (MIBFSP150CP.If.TYPE_EL_ACC, 2, "", "Half", ""),
  F_OPT          (MIBFSP150CP.If.TYPE_OPT_ACC, 1, "", "Full", ""),
  H_OPT          (MIBFSP150CP.If.TYPE_OPT_ACC, 2, "", "Half", ""),
  NOT_APPLICABLE (0, 0 , "n/a", "n/a", "n/a");

  //------------------------------------------------------------------------------------------------------------------
  private final int    mediaType;
  private final int    portSpeed;
  private final String mtosiString;
  private final String duplexMode;
  private final String autoNegotiation;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param mediaType   The MIB defined value
   * @param portSpeed    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private AdministrativeSpeedRateTranslation (final int mediaType, final int portSpeed, final String mtosiString, final String duplexMode, final String autoNegotiation)
  {
    this.mediaType   = mediaType;
    this.portSpeed   = portSpeed;
    this.mtosiString = mtosiString;
    this.duplexMode = duplexMode;
    this.autoNegotiation = autoNegotiation;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMediaTypeValue () {
    return mediaType;
  }

  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getPortSpeedValue () {
    return portSpeed;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

    /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getDuplexMode() {
    return duplexMode;
  }
  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getAutoNegotiation () {
    return autoNegotiation;
  }

  /**
   * Returns the string representation used in ManagedElementMediator class.
   * @param mediaType  The MIB defined value
   * @param portSpeed   The MIB defined value
   * @return the string representation used in ManagedElementMediator class.
   */
  public static String getMtosiString(final int mediaType, final int portSpeed, final String actualLayer)
  {
    AdministrativeSpeedRateTranslation administrativeSpeedRateTranslation = NOT_APPLICABLE;  // the return value

    for (AdministrativeSpeedRateTranslation tmpAdministrativeSpeedRateTranslation : values())
    {
      if (mediaType == tmpAdministrativeSpeedRateTranslation.getMediaTypeValue() && portSpeed == tmpAdministrativeSpeedRateTranslation.getPortSpeedValue())
      {
        administrativeSpeedRateTranslation = tmpAdministrativeSpeedRateTranslation;
        break;
      }
    }
    if(portSpeed == 5 && actualLayer.equals(LayeredParams.LR_DSR_GIGABIT_ETHERNET)) //special case for Gigabit Ethernet.
      return "1000";
    return administrativeSpeedRateTranslation.getMtosiString();
  }

  /**
   * Returns the string representation used in ManagedElementMediator class.
   * @param mtosiString MTOSI defined value
   * @param portSpeed The MIB defined value
   * @return the string representation used in ManagedElementMediator class.
   */
  public static int getMediaTypeValue (final String mtosiString, final int portSpeed)
  {
    AdministrativeSpeedRateTranslation administrativeSpeedRateTranslation = NOT_APPLICABLE;  // the return value

    for (AdministrativeSpeedRateTranslation tmpAdministrativeSpeedRateTranslation : values())
    {
      if (mtosiString.equals(tmpAdministrativeSpeedRateTranslation.getMtosiString()) && portSpeed == tmpAdministrativeSpeedRateTranslation.getPortSpeedValue())
      {
        administrativeSpeedRateTranslation = tmpAdministrativeSpeedRateTranslation;
        break;
      }
    }
    return administrativeSpeedRateTranslation.getMediaTypeValue();
  }

  /**
   * Returns the string representation used in ManagedElementMediator class.
   * @param mtosiString MTOSI defined value
   * @param mediaType The MIB defined value
   * @return the string representation used in ManagedElementMediator class.
   */
  public static int getPortSpeedValue (final String mtosiString, final int mediaType)
  {
    AdministrativeSpeedRateTranslation administrativeSpeedRateTranslation = NOT_APPLICABLE;  // the return value

    for (AdministrativeSpeedRateTranslation tmpAdministrativeSpeedRateTranslation : values())
    {
      if (mtosiString.equals(tmpAdministrativeSpeedRateTranslation.getMtosiString()) && mediaType == tmpAdministrativeSpeedRateTranslation.getMediaTypeValue())
      {
        administrativeSpeedRateTranslation = tmpAdministrativeSpeedRateTranslation;
        break;
      }
    }
    return administrativeSpeedRateTranslation.getPortSpeedValue();
  }
  /*
   * This method is only applicable to acquire the MIB value corresponding to the set of attribute
   * values specified in an MTOSI request.  Therefore, it must ignore MIB values 10 and 11, since they
   * are for status only (negotiating or none).  They have same collection of other attributes and
   * overlap with the auto (5) settings, but since only for status, it is ok.
   */
  public static int getMIBValue(final String mtosiString, final String duplex, final String autoNeg,
			final int mediaType)
	{
		AdministrativeSpeedRateTranslation administrativeSpeedRateTranslation = NOT_APPLICABLE;

		for (AdministrativeSpeedRateTranslation tmpAdministrativeSpeedRateTranslation : values())
		{
			if (mtosiString.equals(tmpAdministrativeSpeedRateTranslation.getMtosiString())
					&& mediaType == tmpAdministrativeSpeedRateTranslation.getMediaTypeValue()
					&& autoNeg.equals(tmpAdministrativeSpeedRateTranslation.getAutoNegotiation())
					&& duplex.equals(tmpAdministrativeSpeedRateTranslation.getDuplexMode()))
			{
				// we do not include 10 and 11 as translations from MTOSI to MIB value
				if(!(tmpAdministrativeSpeedRateTranslation == S_NONE) && !(tmpAdministrativeSpeedRateTranslation == SN))
				{
					administrativeSpeedRateTranslation = tmpAdministrativeSpeedRateTranslation;
					break;
				}
				
			}
		}
		return administrativeSpeedRateTranslation.getPortSpeedValue();
	}

   /**
	 * Returns the string representation used in ManagedElementMediator class.
	 * 
	 * @param mediaType
	 *            The MIB defined value
	 * @param portSpeed
	 *            The MIB defined value
	 * @return the int representation of actual speed rate used in
	 *         ManagedElementMediator class.
	 */
  public static String getDuplexModeValue (final int mediaType, final int portSpeed)
  {
    AdministrativeSpeedRateTranslation administrativeSpeedRateTranslation = NOT_APPLICABLE;  // the return value

    for (AdministrativeSpeedRateTranslation tmpAdministrativeSpeedRateTranslation : values())
    {
      if (mediaType == tmpAdministrativeSpeedRateTranslation.getMediaTypeValue() && portSpeed == tmpAdministrativeSpeedRateTranslation.getPortSpeedValue())
      {
        administrativeSpeedRateTranslation = tmpAdministrativeSpeedRateTranslation;
        break;
      }
    }
    return administrativeSpeedRateTranslation.getDuplexMode();
  }

  /**
   * Returns the string representation used in ManagedElementMediator class.
   * @param mediaType  The MIB defined value
   * @param portSpeed   The MIB defined value
   * @return the int representation of actual speed rate used in ManagedElementMediator class.
   */
  public static String getAutoNegotiationValue (final int mediaType, final int portSpeed)
  {
    AdministrativeSpeedRateTranslation administrativeSpeedRateTranslation = NOT_APPLICABLE;  // the return value

    for (AdministrativeSpeedRateTranslation tmpAdministrativeSpeedRateTranslation : values())
    {
      if (mediaType == tmpAdministrativeSpeedRateTranslation.getMediaTypeValue() && portSpeed == tmpAdministrativeSpeedRateTranslation.getPortSpeedValue())
      {
        administrativeSpeedRateTranslation = tmpAdministrativeSpeedRateTranslation;
        break;
      }
    }
    return administrativeSpeedRateTranslation.getAutoNegotiation();
  }

  /**
   * Returns the int representation used in ManagedElementMediator class.
   * @param portSpeed   The MIB defined value
   * @return the int representation of actual speed rate used in ManagedElementMediator class.
   */
  public static int getRateOfAdvertisedTechnologyAbility (final String [] portSpeed)
  {
    int result = 0;
    for(int i=0; i < portSpeed.length; i++)
    {
      if(result >= 10 || portSpeed[i].contains("10"))
      {
        result = 10;
        if(result >= 100 || portSpeed[i].contains("100"))
        {
          result = 100;
          if(portSpeed[i].contains("1000")){
            result = 1000;
            break;
          }
        }
      }
    }
    return result;
  }

  /**
	 * Returns the int representation used in set bit field on the device.
	 * @param portSpeed   Speed of the port.
	 * @param duplexMode Half or Full duplex.
	 * @param currentBitArrayOfAdvertisedTechnologyAbility  Actual bit array of speed.
	 * @return the new byte array
	 */
	public static byte[] createBitArrayOfAdvertisedTechnologyAbility (final int portSpeed, final String duplexMode, final byte[] currentBitArrayOfAdvertisedTechnologyAbility)
	{
		int [] arrayResult = new int[16];
		final int [] currentIntArray = BitFieldHelper.resolvedBitFieldToIntArray(currentBitArrayOfAdvertisedTechnologyAbility);

		switch(portSpeed){
			case 10:
				if(duplexMode.startsWith("Half"))
					arrayResult[1] = 1;
				else
					arrayResult[2] = 1;
				break;
			case 100:
				if(duplexMode.startsWith("Half"))
					arrayResult[4] = 1;
				else
					arrayResult[5] = 1;
				break;
			case 1000:
				if(duplexMode.startsWith("Full"))
					arrayResult[15] = 1;
				break;
		}
		if(currentIntArray [8] == 1)
			arrayResult[8] = 1;

		if(currentIntArray [9] == 1)
			arrayResult[9] = 1;

		return BitFieldHelper.convertIntToByteArray(arrayResult);
	}

	/**
   * Returns the string representation used in ManagedElementMediator class.
   * @param portSpeed   The MIB defined value
   * @return the string representation of actual speed rate used in ManagedElementMediator class.
   */
  public static String getAutoNegLocalTechnologyAvailability (final String [] portSpeed)
  {
    final StringBuilder result = new StringBuilder();
    for(int i=0; i < portSpeed.length; i++)
    {
      if(i > 0)
        result.append(",");
      result.append(portSpeed[i].replaceFirst("tech", ""));
    }
    return result.toString();
  }

  public static AdministrativeSpeedRateTranslation getEnum(int portSpeedValue)
	{
		AdministrativeSpeedRateTranslation administrativeSpeedRateTranslation = NOT_APPLICABLE; // the return value
		for (AdministrativeSpeedRateTranslation tmpAdministrativeSpeedRateTranslation : values())
		{
			if (portSpeedValue == tmpAdministrativeSpeedRateTranslation.getPortSpeedValue())
			{
				administrativeSpeedRateTranslation = tmpAdministrativeSpeedRateTranslation;
				break;
			}
		}
		return administrativeSpeedRateTranslation;
	}
}
