/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils;

import java.util.List;

import v1.tmf854.LayeredParametersListT;
import v1.tmf854.LayeredParametersT;
import v1.tmf854.NVSListT;
import v1.tmf854.NameAndStringValueT;
import v1.tmf854.ObjectFactory;

public class LayeredParameterUtils
{
	
	
	
	public static String getLayeredParameter(LayeredParametersListT list, String layer, String name)
	{
		NVSListT theLayer = findLayer(list, layer);
		if (theLayer != null)
		{
			return findParameter(theLayer, name);
		}
		
		return null;
	}

	public static void addLayer(LayeredParametersListT list, String layer)
	{
		NVSListT theLayer = findLayer(list, layer);
		if (theLayer == null)
		{
			ObjectFactory objFactory = new ObjectFactory();
			LayeredParametersT theLayeredParameters = objFactory.createLayeredParametersT();
			theLayeredParameters.setLayer(layer);
			theLayeredParameters.setTransmissionParams(objFactory.createNVSListT());
			list.getLayeredParameters().add(theLayeredParameters);
		}
	}

	public static void addLayeredParameter(LayeredParametersListT list, String layer, String name, String value)
	{
		NVSListT theLayer = findLayer(list, layer);
		if (theLayer != null)
		{
			ObjectFactory objFactory = new ObjectFactory();
			NameAndStringValueT theNameAndValue = objFactory.createNameAndStringValueT();
			theNameAndValue.setName(name);
			theNameAndValue.setValue(value);
			theLayer.getNvs().add(theNameAndValue);
		}
	}

	public static NVSListT findLayer(LayeredParametersListT list, String layer)
	{
		List<LayeredParametersT> theLayeredParameters = list.getLayeredParameters();
		for(int i = 0; i < theLayeredParameters.size(); i++)
		{
			LayeredParametersT theParameters = theLayeredParameters.get(i);
			if (theParameters.getLayer().compareTo(layer) == 0)
			{
				return theParameters.getTransmissionParams();
			}
		}
		
		return null;
	}
	
	public static String findParameter(NVSListT list, String name)  throws NullPointerException
	{
    if (list == null) {
      return null;
    }
    List<NameAndStringValueT> theNvs = list.getNvs();
		for(int i = 0; i < theNvs.size(); i++)
		{
			NameAndStringValueT theParameter = theNvs.get(i);
			if (theParameter.getName().compareTo(name) == 0)
			{
				return theParameter.getValue();
			}
		}
		
		return null;
	}
}
