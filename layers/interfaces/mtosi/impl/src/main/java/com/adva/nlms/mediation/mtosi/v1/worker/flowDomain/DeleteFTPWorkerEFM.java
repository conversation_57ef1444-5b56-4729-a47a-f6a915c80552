/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */
package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.dto.attr.ProtectionGroupF3Attr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.adapter.facade.MtosiTerminationPointEFMDTOImpl;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.DeleteFTPResponseT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public class DeleteFTPWorkerEFM extends DeleteFTPWorker {

  private static Logger logger = LogManager.getLogger(DeleteFTPWorkerEFM.class);
  private DTO<ProtectionGroupF3Attr> ftpMO;
  private MtosiMOFacade facade;
  private MtosiAddress mtosiAddress;
  private MtosiTerminationPointEFMDTOImpl mtosiTerminationPointEFMDTO;

  public DeleteFTPWorkerEFM(Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne)
  {
    super(mtosiHeader, namingAttributes, ne);
    mtosiTerminationPointEFMDTO = new MtosiTerminationPointEFMDTOImpl();
  }

  @Override
  protected void parse() throws Exception
  {
    mtosiAddress = new MtosiAddress(namingAttributes);
//    int slot = NamingTranslationFactory.slotNumberFromShelfCombo(mtosiAddress.getNaming().getFtpNm());
//    if (ManagedElementFactory.needsSlotIncrement(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI())) {
//      slot = slot + 1;
//    }

//    String[] parts =  mtosiAddress.getNaming().getFtpNm().split("/");
//    String newFtpNm = "/"+parts[1]+"/slot="+slot+"/"+parts[3];
//    mtosiAddress.getNaming().setFtpNm(newFtpNm);

    if ((this.ftpName = namingAttributes.getFtpNm()) == null)
    {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, "The ftpNm has not been specified.");
    }

    MtosiUtils.validateNE(ne);
    facade = getMtosiCtrl().getMoFacadeManager().getFacadeViaNeId(MtosiMOFacade.class, ne.getID());
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    validator.validate(ne.getDefaultNetworkElementTypeString());
  }

  @Override
  protected void mediate() throws Exception
  {

    ftpMO = facade.findDTOViaMtosiName(ne.getID(), mtosiAddress.getNaming().getFtpNm() , ProtectionGroupF3Attr.class);
    if (ftpMO == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(),ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.FLOATING_TERMINATION_POINT_DISCOVERY_FAILURE);
    }

    transact();
  }

  private void transact() throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure, ProcessingFailureException
  {
    int neId = ne.getID();
    String mtosiname = ftpMO.getValue(ManagedObjectAttr.MTOSI_NAME);
    try {
      facade.openNetTransaction(neId);
      logSecurity(ne, SystemAction.DeleteNetwork, mtosiname);
      facade.deleteObjectOnDevice(neId, ftpMO);
    } catch (ObjectInUseException ex) {
      logger.error("Exception during transaction: " + ex);
      ne.logSROperation(SROperationState.FTP_DELETION_FAILURE, mtosiname);
      throw ex;
    } catch (SNMPCommFailure ex) {
      ne.logSROperation(SROperationState.FTP_DELETION_FAILURE, mtosiname);
      throw ex;
    } finally {
      facade.closeNetTransaction(neId);
    }
  }

  @Override
  protected void response () throws Exception {
    // empty method
  }


  @Override
  public DeleteFTPResponseT getSuccessResponse()
  {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }

}
