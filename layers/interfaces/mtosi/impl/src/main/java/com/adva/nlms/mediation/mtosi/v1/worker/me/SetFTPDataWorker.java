/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.serviceProvisioning.FTPSPProperties;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.FloatingTerminationPointT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.TPDataT;
import v1.tmf854.TerminationPointT;
import v1.tmf854ext.adva.SetTPDataResponseT;

import jakarta.xml.ws.Holder;

public class SetFTPDataWorker extends SetTPDataWorker {
  Logger LOG = LogManager.getLogger(this.getClass().getName());

  public SetFTPDataWorker (Holder<HeaderT> mtosiHeader, TPDataT tpInfo, NamingAttributesT namingAttributes, NetworkElement ne) {
    super(mtosiHeader, tpInfo, namingAttributes, ne);
  }

  @Override
  protected void parse () throws Exception {
    // empty method
  }

  @Override
  protected void mediate() throws Exception
  {
    FTP ftp = ManagedElementFactory.getFtp(namingAttributes);
    FTPSPProperties newProps;
    switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
        newProps = MtosiTPMediator.mtosiTPDataTToCPFTPProperties(tpInfo, ftp);
        break;
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
        newProps = MtosiTPMediator.mtosiTPDataTToCMFTPProperties(tpInfo, ftp);
        break;
      default:
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                "The specified ME is not supported.");
    }
    transact(ftp, newProps);
  }

  private void transact(FTP ftp, FTPSPProperties props)
          throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "SetTPDataWorker");
    try
    {
      logSecurity(ne, SystemAction.ModifyNetwork, "ftpNm=" + ftp.getFTPName());
      ftp.setFTPSPProperties(props);
      NetTransactionManager.commitNetTransaction(id);
    }
    catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally
    {
      NetTransactionManager.ensureEnd(id);
    }
  }

  @Override
  protected void response() throws Exception {
    final FTP ftp = ManagedElementFactory.getFtp(namingAttributes);
    ObjectFactory objectFactory = new ObjectFactory();
    FloatingTerminationPointT floatingTerminationPointT = new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(ftp).toMtosiFTP();
//    FloatingTerminationPointT floatingTerminationPointT = ftp.getMtosiTranslator().toMtosiFTP();

    TerminationPointT tp = objectFactory.createTerminationPointT();
    tp.setFtp(floatingTerminationPointT);
    response.setModifiedTP(tp);
  }

  @Override
  public SetTPDataResponseT getSuccessResponse()
  {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}