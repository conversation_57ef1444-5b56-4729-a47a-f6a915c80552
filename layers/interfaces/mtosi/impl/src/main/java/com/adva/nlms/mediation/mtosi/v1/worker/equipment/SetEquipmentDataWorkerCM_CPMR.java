/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.mediation.mtosi.v1.worker.equipment;

import com.adva.nlms.mediation.common.serviceProvisioning.ModuleCPMRSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.module.cpmr.MTOSIModuleCPMRFSP150CM;
import com.adva.nlms.mediation.config.f3_efm.NetworkElementF3_EFM;
import com.adva.nlms.mediation.mtosi.v1.mediation.NetworkElementMediatorFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiEquipmentMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854ext.adva.EQDataT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

/**
 * main class for the MTOSI operation:  s e t E q u i p m e n t D a t a (FSP150CPMR)
 */
public class SetEquipmentDataWorkerCM_CPMR extends SetEquipmentDataWorker {
  MTOSIModuleCPMRFSP150CM cpModule;

  public SetEquipmentDataWorkerCM_CPMR (Holder<HeaderT> mtosiHeader, EQDataT equipmentData, NamingAttributesT equipmentName, NetworkElement ne) {
    super(mtosiHeader, equipmentData, equipmentName, ne);
  }

  @Override
  protected void mediate() throws Exception {
    int shelfIndex = mtosiAddr.getShelfNumber();
    int slotIndex = mtosiAddr.getNmsFixedSlotNumber();
    if (shelfIndex != 1 || slotIndex != 2) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
              ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, "The requested entity was not found.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    if (vendorExtensions != null) {
      cpModule = ((NetworkElementF3_EFM) ne).getModule();
      transact(cpModule, MtosiEquipmentMediator.mtosiVendorExtensionsTToCPMRProperties(vendorExtensions));
    }
  }

  private void transact(MTOSIModuleCPMRFSP150CM cpModule, ModuleCPMRSPPropertiesFSP150CM properties)
          throws ObjectInUseException, NetTransactionException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "SetTPDataWorker");
    try {
      logSecurity(ne, SystemAction.ModifyNetwork, cpModule.getMtosiName());
      cpModule.setSettings(properties);

      NetTransactionManager.commitNetTransaction(id);
    }
    catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally
    {
      NetTransactionManager.ensureEnd(id);
    }
  }

  @Override
  protected void response() throws Exception {
    response.setModifiedEquipment(NetworkElementMediatorFactory.createNetworkElementMediator(ne).shelfToMtosiEquipment(cpModule.getEquipmentSPProperties()).getEq());
  }
}
