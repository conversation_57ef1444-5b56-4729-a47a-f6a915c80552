/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;

import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.ProcessingFailureException;
import ws.v1.tmf854.ProtectionMgr;

import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.HeaderUtils;


/**
 * This class was generated by the Celtix 1.1-SNAPSHOT
 * Fri Dec 22 10:48:24 EST 2006
 * Generated source version: 1.1-SNAPSHOT
 * 
 */

@jakarta.jws.WebService(name = "ProtectionMgr", serviceName = "ConfigurationService", portName = "ProtectionMgrHttp", targetNamespace = "tmf854.v1.ws")

public class ProtectionMgrImpl implements ProtectionMgr {
    /* (non-Javadoc)
     * @see ws.v1.tmf854.ProtectionMgr#getAllEProtectionGroups(v1.tmf854.GetAllEProtectionGroupsT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllEProtectionGroupsResponseT getAllEProtectionGroups(
        v1.tmf854.GetAllEProtectionGroupsT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllEProtectionGroups", "getAllEProtectionGroupsResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


     @Override
     public v1.tmf854.GetAllEProtectionGroupsResponseT getAllEProtectionGroups(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllEProtectionGroupsT mtosiBody) throws ProcessingFailureException
    {
    	return getAllEProtectionGroups(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.ProtectionMgr#getAllEProtectionGroupsIterator(v1.tmf854.GetEpgpIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllEProtectionGroupsResponseT getAllEProtectionGroupsIterator(
        v1.tmf854.GetEpgpIteratorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllEProtectionGroupsIterator", "getAllEProtectionGroupsResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllEProtectionGroupsResponseT getAllEProtectionGroupsIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetEpgpIteratorT mtosiBody) throws ProcessingFailureException
    {
    	return getAllEProtectionGroupsIterator(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.ProtectionMgr#getAllNUTTPNames(v1.tmf854.GetAllNUTTPNamesT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllNUTTPNames(
        v1.tmf854.GetAllNUTTPNamesT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllNUTTPNames", "getAllNUTTPNamesResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllNUTTPNames(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllNUTTPNamesT mtosiBody) throws ProcessingFailureException
    {
    	return getAllNUTTPNames(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.ProtectionMgr#getAllNUTTPNamesIterator(v1.tmf854.GetAllNUTTPNamesT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllNUTTPNamesIterator(
        v1.tmf854.GetAllNUTTPNamesT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllNUTTPNamesIterator", "getAllNUTTPNamesResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllNUTTPNamesIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllNUTTPNamesT mtosiBody) throws ProcessingFailureException
    {
    	return getAllNUTTPNamesIterator(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.ProtectionMgr#getAllPreemptibleTPNames(v1.tmf854.GetAllPreemptibleTPNamesT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllPreemptibleTPNames(
        v1.tmf854.GetAllPreemptibleTPNamesT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllPreemptibleTPNames", "getAllPreemptibleTPNamesResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllPreemptibleTPNames(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllPreemptibleTPNamesT mtosiBody) throws ProcessingFailureException
    {
    	return getAllPreemptibleTPNames(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.ProtectionMgr#getAllPreemptibleTPNamesIterator(v1.tmf854.GetAllPreemptibleTPNamesT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllPreemptibleTPNamesIterator(
        v1.tmf854.GetAllPreemptibleTPNamesT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllPreemptibleTPNamesIterator", "getAllPreemptibleTPNamesResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllPreemptibleTPNamesIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllPreemptibleTPNamesT mtosiBody) throws ProcessingFailureException
    {
    	return getAllPreemptibleTPNamesIterator(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.ProtectionMgr#getAllProtectedTPNames(v1.tmf854.GetAllProtectedTPNamesT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllProtectedTPNames(
        v1.tmf854.GetAllProtectedTPNamesT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllProtectedTPNames", "getAllProtectedTPNamesResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllProtectedTPNames(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllProtectedTPNamesT mtosiBody) throws ProcessingFailureException
    {
    	return getAllProtectedTPNames(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.ProtectionMgr#getAllProtectedTPNamesIterator(v1.tmf854.GetAllProtectedTPNamesT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllProtectedTPNamesIterator(
        v1.tmf854.GetAllProtectedTPNamesT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllProtectedTPNamesIterator", "getAllProtectedTPNamesResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllProtectedTPNamesIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllProtectedTPNamesT mtosiBody) throws ProcessingFailureException
    {
    	return getAllProtectedTPNamesIterator(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.ProtectionMgr#getAllProtectionGroups(v1.tmf854.GetAllProtectionGroupsT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllProtectionGroupsResponseT getAllProtectionGroups(
        v1.tmf854.GetAllProtectionGroupsT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllProtectionGroups", "getAllProtectionGroupsResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


  @Override
  public v1.tmf854.GetAllProtectionGroupsResponseT getAllProtectionGroups(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllProtectionGroupsT mtosiBody) throws ProcessingFailureException
    {
    	return getAllProtectionGroups(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.ProtectionMgr#getAllProtectionGroupsIterator(v1.tmf854.GetAllProtectionGroupsT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllProtectionGroupsResponseT getAllProtectionGroupsIterator(
        v1.tmf854.GetAllProtectionGroupsT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllProtectionGroupsIterator", "getAllProtectionGroupsResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllProtectionGroupsResponseT getAllProtectionGroupsIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllProtectionGroupsT mtosiBody) throws ProcessingFailureException
    {
    	return getAllProtectionGroupsIterator(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.ProtectionMgr#getContainingPGNames(v1.tmf854.GetContainingPGNamesT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getContainingPGNames(
        v1.tmf854.GetContainingPGNamesT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getContainingPGNames", "getContainingPGNamesResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


  @Override
  public v1.tmf854.GetAllObjectNamesResponseT getContainingPGNames(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetContainingPGNamesT mtosiBody) throws ProcessingFailureException
    {
    	return getContainingPGNames(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.ProtectionMgr#getEProtectionGroup(v1.tmf854.GetEProtectionGroupT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetEProtectionGroupResponseT getEProtectionGroup(
        v1.tmf854.GetEProtectionGroupT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getEProtectionGroup", "getEProtectionGroupResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


  @Override
  public v1.tmf854.GetEProtectionGroupResponseT getEProtectionGroup(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetEProtectionGroupT mtosiBody) throws ProcessingFailureException
    {
    	return getEProtectionGroup(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.ProtectionMgr#getProtectionGroup(v1.tmf854.GetProtectionGroupT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetProtectionGroupResponseT getProtectionGroup(
        v1.tmf854.GetProtectionGroupT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getProtectionGroup", "getProtectionGroupResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetProtectionGroupResponseT getProtectionGroup(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetProtectionGroupT mtosiBody) throws ProcessingFailureException
    {
    	return getProtectionGroup(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.ProtectionMgr#retrieveESwitchData(v1.tmf854.RetrieveESwitchDataT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.RetrieveESwitchDataResponseT retrieveESwitchData(
        v1.tmf854.RetrieveESwitchDataT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "retrieveESwitchData", "retrieveESwitchDataResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


  @Override
  public v1.tmf854.RetrieveESwitchDataResponseT retrieveESwitchData(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.RetrieveESwitchDataT mtosiBody) throws ProcessingFailureException
    {
    	return retrieveESwitchData(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.ProtectionMgr#retrieveSwitchData(v1.tmf854.RetrieveSwitchDataT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.RetrieveSwitchDataResponseT retrieveSwitchData(
        v1.tmf854.RetrieveSwitchDataT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    { 
		HeaderUtils.formatResponseHeader(mtosiHeader, "retrieveSwitchData", "retrieveSwitchDataResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.RetrieveSwitchDataResponseT retrieveSwitchData(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.RetrieveSwitchDataT mtosiBody) throws ProcessingFailureException
    {
		return retrieveSwitchData(mtosiBody, mtosiHeader);
    }
}
