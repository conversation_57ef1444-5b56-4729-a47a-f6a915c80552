/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum GE20XFlowTrafficTypeTranslation implements TranslatableEnum {
    shaped_only 	(1, "ShapedOnly"),
    policed_shaped 	(2, "PoliceShaped"),
    NOT_APPLICABLE	(-1, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private GE20XFlowTrafficTypeTranslation (final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}