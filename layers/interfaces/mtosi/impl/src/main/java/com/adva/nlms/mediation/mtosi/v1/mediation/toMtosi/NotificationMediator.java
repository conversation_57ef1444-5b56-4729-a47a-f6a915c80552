/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */
package com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi;


import com.adva.nlms.common.event.EventSeverity;
import com.adva.nlms.common.event.EventType;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementObjectAttributes;
import com.adva.nlms.mediation.config.f3.entity.module.AbstractModuleF3Attributes;
import com.adva.nlms.mediation.event.definition.Properties;
import com.adva.nlms.inf.mo.api.notification.MOCreationNotification;
import com.adva.nlms.inf.mo.api.notification.MODeletionNotification;
import com.adva.nlms.inf.mo.api.notification.MONotification;
import com.adva.nlms.mediation.messaging.inf.NEDeletionNotification;
import com.adva.nlms.mediation.messaging.inf.NEUpdateNotification;
import com.adva.nlms.mediation.mtosi.MtosiCtrl;
import com.adva.nlms.mediation.mtosi.common.notification.MtosiNotificationConstants;
import com.adva.nlms.mediation.mtosi.common.notification.MtosiNotifications;
import com.adva.nlms.mediation.mtosi.common.notification.NotificationEnum;
import com.adva.nlms.mediation.mtosi.common.notification.Subscriber;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.HeaderUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import v1.tmf854.AnyListT;
import v1.tmf854.CommunicationPatternT;
import v1.tmf854.CommunicationStyleT;
import v1.tmf854.EventInformationT;
import v1.tmf854.EventT;
import v1.tmf854.HeaderT;
import v1.tmf854.HolderStateT;
import v1.tmf854.MsgTypeT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.NotifyT;
import v1.tmf854.ObjectCreationExtT;
import v1.tmf854.ObjectCreationT;
import v1.tmf854.ObjectDeletionExtT;
import v1.tmf854.ObjectDeletionT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ObjectTypeT;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ResourceStateT;
import v1.tmf854.StateChangeExtT;
import v1.tmf854.StateChangeT;
import ws.v1.tmf854.NotificationConsumer;

import jakarta.xml.bind.JAXBElement;
import javax.xml.namespace.QName;
import jakarta.xml.ws.BindingProvider;
import jakarta.xml.ws.Service;
import java.util.List;
import java.util.Map;


/**
 * Create and send notifications to registered subscribers
 */
public class NotificationMediator implements MtosiNotifications {
  private static final QName SERVICE_NAME = new QName("tmf854.v1.ws", "NotificationService");
  private static Logger LOG = LoggerFactory.getLogger(NotificationMediator.class);

  public NotificationMediator() {
  }


  /**
   * Creates and sends the notification regarding the creation of a new Network Element, that
   * was created via NBI
   * @param notification The notification created from MO Layer
   * @param topic The topic this notification belongs to
   * @param subscribers  The list of the subscribers registered to this topic
   * @param mtosiCtrl  The Mtosi Controller
   */
  public void createNENotification(NEUpdateNotification notification, String topic,
                                   List<Subscriber> subscribers, MtosiCtrl mtosiCtrl) {
    NetworkElement ne = mtosiCtrl.getLegacyMtosiMOFacade().getNEById(notification.getNotifiedObjectId());
    HeaderT headerT = createHeader();
    EventT eventT = new EventT();
    ObjectCreationT objectCreationT = new ObjectCreationT();
    EventInformationT eventInformationT =
            getCommonEventInfoForNE(ne, "NE created", "ENT-ADD",ne.getName(),mtosiCtrl);
    objectCreationT.setEventInfo(eventInformationT);

    ObjectCreationExtT objectCreationExtT = new ObjectCreationExtT();
    objectCreationT.setVendorExtensions(objectCreationExtT);
    eventT.setObjectCreation(objectCreationT);
    NotifyT notifyT = new NotifyT();
    notifyT.setMessage(eventT);
    LOG.debug("create ne notification");
    sendNotifications(headerT, notifyT, topic, subscribers);


  }

  /**
   * This notifications is created when the NE is deleted
   * @param notification  The notification created from MO Layer
   * @param topic The topic this notification belongs to
   * @param subscribers  The list of the subscribers registered to this topic
   * @param mtosiCtrl The Mtosi Controller
   */
  public void deleteNENotification(NEDeletionNotification notification, String topic,
                                           List<Subscriber> subscribers,MtosiCtrl mtosiCtrl){
    HeaderT headerT = createHeader();
    NotifyT notifyT=new NotifyT();
    EventT eventT=new EventT();
    ObjectDeletionT objectDeletionT=new ObjectDeletionT();
    EventInformationT eventInformationT=
            getCommonEventInfoForNE(null,"NE deleted","ENT-DEL",notification.getNotifiedObjectAid(),mtosiCtrl);

    objectDeletionT.setEventInfo(eventInformationT);
    ObjectDeletionExtT objectDeletionExtT = new ObjectDeletionExtT();

    objectDeletionT.setVendorExtensions(objectDeletionExtT);
    eventT.setObjectDeletion(objectDeletionT);
    notifyT.setMessage(eventT);
    LOG.debug("delete ne notification");
    sendNotifications(headerT,notifyT,topic,subscribers);
  }

  /**
   * This notification is created when the NE is discovered
   * @param notification The update notification created from MO Layer (StateChange event in MTOSI)
   * @param topic The topic this notification belongs to
   * @param subscribers The list of the subscribers registered to this topic
   * @param mtosiCtrl he Mtosi Controller
   */
  public void updateNENotification(NEUpdateNotification notification, String topic,
                                         List<Subscriber> subscribers,MtosiCtrl mtosiCtrl) {
    HeaderT headerT = createHeader();
    NetworkElement ne = mtosiCtrl.getLegacyMtosiMOFacade().getNEById(notification.getNotifiedObjectId());
    NotifyT notifyT = new NotifyT();

    StateChangeT stateChangeT = new StateChangeT();
    EventInformationT eventInformationT=getCommonEventInfoForNE(ne,"NE discovered","ENT-STAT",notification.getNotifiedObjectAid(),mtosiCtrl);

    stateChangeT.setEventInfo(eventInformationT);

    StateChangeExtT objectCreationExtT = new StateChangeExtT();

    stateChangeT.setVendorExtensions(objectCreationExtT);

    AnyListT attributeList = new AnyListT();
    ObjectFactory objectFactory = new ObjectFactory();
    ResourceStateT resourceStateT =objectFactory.createResourceStateT();
    resourceStateT.setValue(ResourceStateEnumT.INSTALLED);
    JAXBElement<? extends ResourceStateT> resourceStateTJAXBElement = new JAXBElement(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_RESOURCE_STATE), ResourceStateT.class, resourceStateT);
    Integer discoveryState=notification.getValue(NetworkElementObjectAttributes.DISCOVERY_STATE);

    JAXBElement<? extends String> je = new JAXBElement(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_DISCOVERY_STATE), String.class, MtosiUtils.getDiscoveryStateString(discoveryState));

    attributeList.getAny().add(resourceStateTJAXBElement);
    attributeList.getAny().add(je);
    stateChangeT.setAttributeList(attributeList);
    EventT eventT = new EventT();
    eventT.setStateChange(stateChangeT);
    notifyT.setMessage(eventT);
    LOG.debug("update ne notification");
    sendNotifications(headerT,notifyT,topic,subscribers);
  }

  /**
   * Logs the action of the notification to the event subsystem. Event will be logged even if the notification
   * is not sent to the client
   * @param ne The network element
   * @param msg  The message to log to the event subsystem
   * @param evt either  ENT-ADD or ENT-DEL
   * @param mtosiCtrl  the mtosi controller
   * @return
   */
  public EventInformationT getCommonEventInfoForNE(NetworkElement ne, String msg, String evt, String neName,MtosiCtrl mtosiCtrl) {
    NamingAttributesT namingAttributesT = new NamingAttributesT();
    namingAttributesT.setMdNm(OSFactory.getMDNm());
    namingAttributesT.setMeNm(neName);

    EventInformationT eventInformationT = new EventInformationT();

    long id = mtosiCtrl.getConfigCtrl().getEventCtrl().getEventDBChangeHdlr().addStatusEventSynchronously(Properties.TRAP_ID_NULL,
        EventType.TRANSIENT, EventSeverity.WARNING, msg,
        evt, ne, neName, "", 0);
    eventInformationT.setNotificationId(Long.toString(id));

    eventInformationT.setNeTime("");
    //might contain other info like ehNm and eqNm
    eventInformationT.setObjectName(namingAttributesT);
    eventInformationT.setObjectType(ObjectTypeT.OT_MANAGED_ELEMENT);
    eventInformationT.setOsTime(HeaderUtils.getCurrentTimestamp());
    return eventInformationT;
  }

  public EventInformationT getCommonEventInfoForModule(NetworkElement ne, String msg, String evt, MtosiCtrl mtosiCtrl,String mtosiAid) {
    NamingAttributesT namingAttributesT = new NamingAttributesT();
    namingAttributesT.setMdNm(OSFactory.getMDNm());
    namingAttributesT.setMeNm(ne.getName());

    EventInformationT eventInformationT = new EventInformationT();

    long id = mtosiCtrl.getConfigCtrl().getEventCtrl().getEventDBChangeHdlr().addStatusEventSynchronously(Properties.TRAP_ID_NULL,
        EventType.TRANSIENT, EventSeverity.WARNING, msg,
        evt, ne, mtosiAid, "", 0);
    eventInformationT.setNotificationId(Long.toString(id));

    eventInformationT.setNeTime("");
    //might contain other info like ehNm and eqNm
    eventInformationT.setObjectName(namingAttributesT);
    eventInformationT.setObjectType(ObjectTypeT.OT_EQUIPMENT);
    eventInformationT.setOsTime(HeaderUtils.getCurrentTimestamp());
    return eventInformationT;
  }


  /**
   * A new module is provisioned in the NE
   * @param notification The update notification created from MO Layer (StateChange event in MTOSI)
   * @param topic The topic this notification belongs to
   * @param subscribers The list of the subscribers registered to this topic
   * @param mtosiCtrl he Mtosi Controller
   */
  public void createModuleNotification(MOCreationNotification notification,String topic,
                                       List<Subscriber> subscribers,MtosiCtrl mtosiCtrl){
    HeaderT headerT = createHeader();
    NetworkElement ne = mtosiCtrl.getLegacyMtosiMOFacade().getNEById(notification.getOrigin().getNeID());
    NotifyT notifyT = new NotifyT();
    ObjectCreationT objectCreationT=new ObjectCreationT();
    EventInformationT eventInformationT=
            getCommonEventInfoForModule(ne,"Module provisioned","ENT-ADD",mtosiCtrl,notification.getNotifiedObjectMtosiAid());
    eventInformationT.getObjectName().setEhNm(notification.getNotifiedObjectMtosiAid());
    eventInformationT.getObjectName().setEqNm("1"); //create module is provision so eq should be 1
    ObjectCreationExtT objectCreationExtT= new ObjectCreationExtT();
    objectCreationT.setVendorExtensions(objectCreationExtT);
    objectCreationT.setEventInfo(eventInformationT);
    EventT eventT = new EventT();
    eventT.setObjectCreation(objectCreationT);
    notifyT.setMessage(eventT);
    LOG.debug("create module notification for "+notification.getNotifiedObjectAid());
    sendNotifications(headerT, notifyT, topic, subscribers);
  }

  /**
   * This notification is created when a module is either inserted or removed from the NE. The installed state (which
   * maps to resource state in MTOSI) changes from Installed to Planned and vice versa.
   * @param notification The update notification created from MO Layer (StateChange event in MTOSI)
   * @param topic The topic this notification belongs to
   * @param subscribers The list of the subscribers registered to this topic
   * @param mtosiCtrl he Mtosi Controller
   */
  public void updateModuleNotification(MONotification notification, String topic,
                                       List<Subscriber> subscribers, MtosiCtrl mtosiCtrl) {
    HeaderT headerT = createHeader();
    int installationState = -1;
    String evtTxt = MtosiConstants.MODULE_INSERTED_REMOVED;
    if (notification.containsKey(AbstractModuleF3Attributes.INSTALL_STATE)) {
      if (notification.getValue(AbstractModuleF3Attributes.INSTALL_STATE) != null) {
        installationState = notification.getValue(AbstractModuleF3Attributes.INSTALL_STATE);
        if (MtosiUtils.getHolderState(installationState).equals(HolderStateT.INSTALLED_AND_EXPECTED)) {
          evtTxt = MtosiConstants.MODULE_INSERTED;
        } else if (MtosiUtils.getHolderState(installationState).equals(HolderStateT.EXPECTED_AND_NOT_INSTALLED)) {
          evtTxt = MtosiConstants.MODULE_REMOVED;
        }
      }
    }
    NetworkElement ne = mtosiCtrl.getLegacyMtosiMOFacade().getNEById(notification.getOrigin().getNeID());
    NotifyT notifyT = new NotifyT();
    StateChangeT stateChangeT = new StateChangeT();
    EventInformationT eventInformationT =
            getCommonEventInfoForModule(ne, evtTxt, "ENT-STAT", mtosiCtrl,notification.getNotifiedObjectMtosiAid());
    eventInformationT.getObjectName().setEhNm(notification.getNotifiedObjectMtosiAid());
    stateChangeT.setEventInfo(eventInformationT);

    StateChangeExtT objectCreationExtT = new StateChangeExtT();
    stateChangeT.setVendorExtensions(objectCreationExtT);

    AnyListT attributeList = new AnyListT();
    if (installationState > 0) {
      if (notification.getValue(AbstractModuleF3Attributes.INSTALL_STATE) != null) {
        ObjectFactory objectFactory = new ObjectFactory();
        ResourceStateT resourceStateT = objectFactory.createResourceStateT();
        JAXBElement<? extends ResourceStateT> resourceStateTJAXBElement = new JAXBElement(new QName(MtosiConstants.VENDOR_NAMESPACE,
                MtosiConstants.VENDOR_RESOURCE_STATE), ResourceStateT.class, resourceStateT);
        resourceStateT.setValue(MtosiUtils.getResourceState(installationState));
        eventInformationT.getObjectName().setEqNm("1"); //the module is always provisioned so eq should be 1
        attributeList.getAny().add(resourceStateTJAXBElement);
      }
    }

    stateChangeT.setAttributeList(attributeList);
    EventT eventT = new EventT();
    eventT.setStateChange(stateChangeT);
    notifyT.setMessage(eventT);
    LOG.debug("update module notification for " + notification.getNotifiedObjectAid());
    sendNotifications(headerT, notifyT, topic, subscribers);
  }

  /**
   * This notification is created when a module is unprovisioned. EqNm attribute will not be present because the module
   * is unprovisioned.
   * @param notification The update notification created from MO Layer (StateChange event in MTOSI)
   * @param topic The topic this notification belongs to
   * @param subscribers The list of the subscribers registered to this topic
   * @param mtosiCtrl he Mtosi Controller
   */
  public void deleteModuleNotification(MODeletionNotification notification,String topic,
                                          List<Subscriber> subscribers,MtosiCtrl mtosiCtrl){
    HeaderT headerT = createHeader();
    NetworkElement ne = mtosiCtrl.getLegacyMtosiMOFacade().getNEById(notification.getOrigin().getNeID());
    NotifyT notifyT = new NotifyT();
    ObjectDeletionT objectDeletionT=new ObjectDeletionT();
    EventInformationT eventInformationT=
            getCommonEventInfoForModule(ne,"Module unprovisioned","ENT-DEL",mtosiCtrl,notification.getNotifiedObjectMtosiAid());
    eventInformationT.getObjectName().setEhNm(notification.getNotifiedObjectMtosiAid());
    ObjectDeletionExtT objectDeletionExtT=new ObjectDeletionExtT();
    objectDeletionT.setEventInfo(eventInformationT);
    objectDeletionT.setVendorExtensions(objectDeletionExtT);
    EventT eventT=new EventT();
    eventT.setObjectDeletion(objectDeletionT);
    notifyT.setMessage(eventT);
    LOG.debug("delete module notification for "+notification.getNotifiedObjectAid());
    sendNotifications(headerT, notifyT, topic, subscribers);
  }


  /**
   * Creates the header for the SOAP message of the notification
   * @return
   */
  public static HeaderT createHeader() {
    HeaderT headerT = new HeaderT();
    headerT.setActivityName(MtosiNotificationConstants.NOTIFY);
    headerT.setMsgName(MtosiNotificationConstants.NOTIFY);
    headerT.setMsgType(MsgTypeT.NOTIFICATION);
    headerT.setSenderURI(MtosiNotificationConstants.SENDER_URI);
    headerT.setDestinationURI(MtosiNotificationConstants.DEST_URI);
    headerT.setCommunicationPattern(CommunicationPatternT.NOTIFICATION);
    headerT.setCommunicationStyle(CommunicationStyleT.MSG);
    return headerT;
  }

  /**
   * Send notifications to the subscribers registered to the specified topic
   * @param headerT   The header of the notification
   * @param notifyT  The body of the notification
   * @param topic   The topic this notifications is about
   * @param subscribers A list with the subscribers
   */
  public void sendNotifications(HeaderT headerT,NotifyT notifyT,
                                String topic, List<Subscriber> subscribers){
    notifyT.setTopic(topic);
    for (Subscriber subscriber : subscribers) {
      if(subscriber.getMtosiVersion() == NotificationEnum.V1.getId()) {
        try {
          notify(subscriber, headerT, notifyT);
          LOG.info("Notification sent to: " + subscriber.getEndpoint() + " for " + topic);
        } catch (Exception e) {
          LOG.error(MtosiNotificationConstants.ERROR_NOTIFY);
          LOG.debug(MtosiNotificationConstants.ERROR_NOTIFY + " : " + e.getMessage());
          LOG.info("Notification might not have been sent to: " + subscriber.getEndpoint() + " for " + topic);
        }
      }else{
        LOG.warn("Invalid notification mediator was invoked. Message is dropped.");
      }
    }

  }

  /**
   * Sends the notification to the client
   * @param subscriber  The client to send the notification
   * @param _notify_mtosiHeader The header of the message
   * @param _notify_mtosiBody  The body of the message
   * @throws Exception
   */
  public void notify(Subscriber subscriber, v1.tmf854.HeaderT _notify_mtosiHeader, v1.tmf854.NotifyT _notify_mtosiBody) throws Exception {
    Service srv=   Service.create(SERVICE_NAME);

    NotificationConsumer port = srv.getPort(NotificationConsumer.class);

    BindingProvider bp = (BindingProvider) port;
    Map<String, Object> context = bp.getRequestContext();
    context.put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY,subscriber.getEndpoint());
    port.notify(_notify_mtosiHeader, _notify_mtosiBody);
  }

}
