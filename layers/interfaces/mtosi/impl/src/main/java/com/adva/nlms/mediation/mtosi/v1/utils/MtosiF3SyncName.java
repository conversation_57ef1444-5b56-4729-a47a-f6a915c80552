/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.adva.nlms.common.NEUtils;

public class MtosiF3SyncName {
  private int shelfIndex = -1;
  private int slotIndex = -1;
  private String alias = null;
  private boolean isSCU = false;
  private int neType = -1;
  final String regexpShelf   = "/(shelf)=(\\d+)";
  final String regexpSlot    = "/(slot)=(\\d+)";
  final String regexpSlotSCU = "/(slot)=(SCU)";
  final String regexpTDFr    = "/(tdfr)=([\\w-]+)";
  final String regexpEmptyTDFr    = "/tdfr=";

  /**
   * Extract the shlefIndex, slotIndex and tdfr Alias from the MTOSI tdfrName
   *  
   * @param tdfrName
   * @throws MtosiF3F3SyncNameException
   */
  public MtosiF3SyncName(String tdfrName, int neType) throws MtosiF3F3SyncNameException {
    init(tdfrName, neType);
    validate();
  }

  /**
   * Extract the shlefIndex, slotIndex and tdfr Alias from the MTOSI tdfrName
   *  
   * @param tdfrName
   * @param allowEmpty
   * @param neType - Used to determine it the slot index needs incrementing
   * @throws MtosiF3F3SyncNameException
   */
  public MtosiF3SyncName(String tdfrName,boolean allowEmpty, int neType) throws MtosiF3F3SyncNameException {
    init(tdfrName, neType);
    if (allowEmpty && alias == null) {
      alias = "";
    }
    validate();
  }

  /**
   * @param tdfrName
   * @param neType - Used to determine it the slot index needs incrementing
   */
  private void init(String tdfrName, int neType) {
    this.neType = neType;
    Pattern pattern = Pattern.compile(
        "^(" +                                             // Begin of Line
        regexpShelf + regexpSlot + regexpTDFr +  "|" +    // matches /shelf=x/slot=y/tdfr=z
        regexpShelf + regexpSlotSCU + regexpTDFr +  "|" +    // matches /shelf=x/slot=SCU/tdfr=z
        regexpShelf + regexpSlot + regexpEmptyTDFr +"|" +    // matches /shelf=x/slot=y/tdfr=
        regexpShelf + regexpSlotSCU + regexpEmptyTDFr +    // matches /shelf=x/slot=SCU/tdfr=
        ")$");                                             // End of Line
    Matcher matcher = pattern.matcher(tdfrName);
   
    // determine shelf, slot, and tdfr alias
    if (matcher != null) {
      if ( matcher.find()) {
        String key;
        int grpCnt =  matcher.groupCount();
        for (int idx=1; idx <= grpCnt; idx++) {
          key =  matcher.group(idx);
          if (key == null || key.startsWith("/"))
            continue;
          else if (key.equals("shelf"))
            shelfIndex = Integer.parseInt(matcher.group(++idx));
          else if (key.equals("slot")) {
            String slot = matcher.group(++idx);
            if ("SCU".equals(slot)) {
              isSCU = true;
              slotIndex = 1;
            } else {
              slotIndex = Integer.parseInt(slot);
              if (!NEUtils.isGEDevice(neType)) {
                slotIndex ++;
              }
            }
          }          
          else if (key.equals("tdfr"))
            alias =  matcher.group(++idx);
        }
      }
    }
  }
  /**
   * Reverse engineer the tdfrName
   */
  public String toString() {
    StringBuilder b = new StringBuilder();
    b.append("/shelf=").append(shelfIndex);
    b.append("/slot=");
    if (isSCU) {
      b.append("SCU");
    } else if (!NEUtils.isGEDevice(neType)) {
      b.append(slotIndex -1);
    } else {
      b.append(slotIndex);
    }
    b.append("/tdfr=").append(alias);
    return b.toString();
  }

  private void validate() throws MtosiF3F3SyncNameException {
    if (shelfIndex <= 0 || slotIndex <= 0 || alias == null) {
      throw new MtosiF3F3SyncNameException(MtosiErrorConstants.TDFR_NAME_INVALID);
    }
  }

  public int getShelfIndex() {
    return shelfIndex;
  }

  public int getSlotIndex() {
    return slotIndex;
  }

  public String getAlias() {
    return alias;
  }
}
