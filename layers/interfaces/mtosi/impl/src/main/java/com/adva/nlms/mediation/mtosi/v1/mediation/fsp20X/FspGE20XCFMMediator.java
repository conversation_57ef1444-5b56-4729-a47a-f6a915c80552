/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.mediation.fsp20X;

import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.*;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.*;
import com.adva.nlms.mediation.mtosi.v2.utils.translations.f3.CMMaintenanceEndPointDefectTranslation;
import v1.tmf854.LayeredParametersListT;

public class FspGE20XCFMMediator {
  LayeredParametersListT layeredParametersListT  = new LayeredParametersListT();

  public FspGE20XCFMMediator() {
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM);
  }

  public LayeredParametersListT toMtosiMD(DTO<MDAttr> md){

   LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MD_LEVEL_PARAM,
        String.valueOf(md.getValue(MDAttr.LEVEL)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MD_MIP_CREATION,
        CFMCreationMhfControlTranslation.getMtosiString(md.getValue(MDAttr.MIP_CREATION)));

    return layeredParametersListT;
  }

  public LayeredParametersListT toMtosiMA(DTO<MANetAttr> manet, DTO<MACompAttr> macomp){
     //MaCcmInterval
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MA_CCM_INTERVAL_PARAM,
        CfmMaCcmIntervalTypeTranslation.getMtosiString(manet.getValue(MANetAttr.CCM_INTERVAL).getSnmpValue()));

    //MaMepList
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MA_MEP_TABLE_PARAM,
        String.valueOf(manet.getValue(MANetAttr.MEP_TABLE)));

    //MaPrimaryVid
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MA_PRIMARY_VID_PARAM,
        String.valueOf(macomp.getValue(MACompAttr.PRIMARY_VID)));

    //MaCompMhfCreation
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MA_MIP_CONTORL_PARAM,
        CFMCreationMhfControlTranslation.getMtosiString(macomp.getValue(MACompAttr.MIP_CREATION_CONTROL)));

    return layeredParametersListT;
  }

  public LayeredParametersListT toMtosiMEP(DTO<MEPAttr> mep){
    //MepDirection
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_DIRECTION_PARAM,
        CfmMepDirectionTypeTranslation.getMtosiString(mep.getValue(MEPAttr.DIRECTION)));

    //MepAdministrationState
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_ADMIN_STATE_PARAM,
        MtosiUtils.getMtosiString(CMAdministrationControlTranslation.NOT_APPLICABLE, mep.getValue(MEPAttr.ADMIN_STATE)));

    //MepServiceState
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_SERVICE_STATE_PARAM,
        CMServiceStateTranslation.getMtosiString(mep.getValue(MEPAttr.ADMIN_STATE), mep.getValue(MEPAttr.OPER_STATE)));

    //MepCciEnabled
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_CCI_ENABLED_PARAM,
        MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, mep.getValue(MEPAttr.CCI_ENABLED)));

    //MepVlanPriority
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_CCM_LTM_PRIORITY_PARAM,
        String.valueOf(mep.getValue(MEPAttr.CCM_LTM_PRIORITY)));

    //MepMacAddress
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_MAC_ADDRESS_PARAM,
        mep.getValue(MEPAttr.MAC_ADDRESS));

    //MepLowestPriorityDefect
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_LOWEST_PRIORITY_DEFECT_PARAM,
        CfmMepLowestPriorityDefectTypeTranslation.getMtosiString(mep.getValue(MEPAttr.LOW_PR_DEF)));

    //MepHighestPriorityDefect
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_HIGHEST_PRIORITY_DEFECT,
        CfmMepHighestPrDefectTypeTranslation.getMtosiString(mep.getValue(MEPAttr.HIGHEST_PR_DEFECT)));

    //MepLlfTrigger
    if (mep.getValue(MEPAttr.LLF_TRIGGER_TYPES)!=0) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_LLF_TRIGGER, getLlfTriggerTypes(mep.getValue(MEPAttr.LLF_TRIGGER_TYPES)));
    }else{
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_LLF_TRIGGER, "");
    }

    //MepAisGeneration
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_AIS_GENERATION,  MtosiUtils.getMtosiString(
            BooleanTypeTranslation.NOT_APPLICABLE, mep.getValue(MEPAttr.AIS_GEN_ENABLED)));

     //MepAisTransmissionInterval
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_AIS_TRANSMISSION_INTERVAL,
        CfmMepAisIntervalTranslation.getMtosiString(mep.getValue(MEPAttr.AIS_INTERVAL)));

    //MepAisPriority
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_AIS_PRIORITY, mep.getValue(MEPAttr.AIS_PRIORITY).toString());

    //MepAisClientMdLevel
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_AIS_CLIENT_MD_LEVEL, mep.getValue(MEPAttr.AIS_CLIENT_MD_LEVEL).toString());

    //MepAisTrigger
    if(mep.getValue(MEPAttr.AIS_GEN_TRIGGER_TYPES) != 0) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_AIS_TRIGGER, getAisTiggerTypes(mep.getValue(MEPAttr.AIS_GEN_TRIGGER_TYPES)));
    }else{
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_AIS_TRIGGER, "");
    }

    //MepAisDefectReceived
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_DEFECTS, CFMDefectsTranslation.getMtosiString(mep.getValue(MEPAttr.CFM_MEP_DEFECTS)));

    //MepLastErrorCcmReceived
    if (mep.getValue(MEPAttr.ERROR_CCM_LAST_FAILURE) != null && !mep.getValue(MEPAttr.ERROR_CCM_LAST_FAILURE).equals("")) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_ERROR_CCM_LAST_FAILURE, (mep.getValue(MEPAttr.ERROR_CCM_LAST_FAILURE)));
    }else{
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_ERROR_CCM_LAST_FAILURE, "");
    }

    //MepLastXConCcmReceived
    if (mep.getValue(MEPAttr.XCON_CCM_LAST_FAILURE) != null && !mep.getValue(MEPAttr.XCON_CCM_LAST_FAILURE).equals("")) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_XCON_CCM_LAST_FAILURE, mep.getValue(MEPAttr.XCON_CCM_LAST_FAILURE));
    } else {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_XCON_CCM_LAST_FAILURE, "");
    }

    addParametersRelativeWithMepDefectsParameter(layeredParametersListT, String.valueOf(mep.getValue(MEPAttr.MEP_DEFECTS)));

    return layeredParametersListT;
  }


  private void addParametersRelativeWithMepDefectsParameter(LayeredParametersListT layeredParametersListT, String mepDefects) {
    int decimalNumber = Integer.parseInt(mepDefects);
    //MepRdiDefectReceived
//    if ((decimalNumber >> 8)%2 == 1 ){
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_RDI_DEFECT_RECEIVED, CMMaintenanceEndPointDefectTranslation.hasDefectType(com.adva.nlms.mediation.mtosi.v2.factory.LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_RDI_DEFECT_RECEIVED, decimalNumber));
//    } else{
//      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
//          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_RDI_DEFECT_RECEIVED,  BooleanTypeTranslation.DISABLED.getMtosiString());
//    }

    //MepMacDefectReceived
//    if ((decimalNumber >> 7)%2 == 1 ){
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_MAC_DEFECT_RECEIVED, CMMaintenanceEndPointDefectTranslation.hasDefectType(com.adva.nlms.mediation.mtosi.v2.factory.LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_MAC_DEFECT_RECEIVED, decimalNumber));
//    } else{
//      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
//          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_MAC_DEFECT_RECEIVED,  BooleanTypeTranslation.DISABLED.getMtosiString());
//    }

    //MepRemoteMepCcmDefectReceived
//    if ((decimalNumber >> 6)%2 == 1 ){
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_RMEP_CCM_DEFECT_RECEIVED, CMMaintenanceEndPointDefectTranslation.hasDefectType(com.adva.nlms.mediation.mtosi.v2.factory.LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_RMEP_CCM_DEFECT_RECEIVED, decimalNumber));
//    } else{
//      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
//          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_RMEP_CCM_DEFECT_RECEIVED,  BooleanTypeTranslation.DISABLED.getMtosiString());
//    }

    //MepCcmErrorDefectReceived
//    if ((decimalNumber >> 5)%2 == 1 ){
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_CCM_ERROR_DEFECT_RECEIVED, CMMaintenanceEndPointDefectTranslation.hasDefectType(com.adva.nlms.mediation.mtosi.v2.factory.LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_CCM_ERROR_DEFECT_RECEIVED, decimalNumber));
//    } else{
//      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
//          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_CCM_ERROR_DEFECT_RECEIVED,  BooleanTypeTranslation.DISABLED.getMtosiString());
//    }

    //MepCcmMisconnectionDefectReceived
//    if ((decimalNumber >> 4)%2 == 1 ){
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_XCON_CCM_DEFECT, CMMaintenanceEndPointDefectTranslation.hasDefectType(com.adva.nlms.mediation.mtosi.v2.factory.LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_XCON_CCM_DEFECT, decimalNumber));
//    } else{
//      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
//          LayeredParams.LrPropAdvaCFMCCM.CFM_MEP_XCON_CCM_DEFECT,  BooleanTypeTranslation.DISABLED.getMtosiString());
//    }
  }

  private String getLlfTriggerTypes(Integer octetValue) {
    String llfTrigerTypes="";
    String binaryValue = Integer.toBinaryString(octetValue);
    String reverseBinaryValue = reverseBinaryValue(binaryValue);
    for (int i=0; i< reverseBinaryValue.length(); i++){

      if (reverseBinaryValue.substring(i,i+1).equals("1")){
        if(CfmMepLlfTriggerTypesTranslation.getMtosiString(i).equals("n/a")) continue;
        llfTrigerTypes = llfTrigerTypes.concat(CfmMepLlfTriggerTypesTranslation.getMtosiString(i)).concat(",");
      }
    }
    llfTrigerTypes = llfTrigerTypes.substring(0,llfTrigerTypes.length()-1);

    return llfTrigerTypes;
  }

  private String getAisTiggerTypes(Integer octetValue) {
    String aisTrigerTypes="";
    String binaryValue = Integer.toBinaryString(octetValue);
    String reverseBinaryValue = reverseBinaryValue(binaryValue);
    for (int i=0; i< reverseBinaryValue.length(); i++){
       if (reverseBinaryValue.substring(i,i+1).equals("1")){
        aisTrigerTypes = aisTrigerTypes.concat(CfmMepAisTriggerTypesTranslation.getMtosiString(i)).concat(",");
      }
    }
    aisTrigerTypes = aisTrigerTypes.substring(0,aisTrigerTypes.length()-1);

    return aisTrigerTypes;
  }

  private String reverseBinaryValue(String binaryValue) {
    String reverseBinaryValue = "";
    for (int i=0; i<binaryValue.length();i++){
      reverseBinaryValue = reverseBinaryValue.concat(binaryValue.substring(binaryValue.length()-i-1,binaryValue.length()- i));
    }
    return reverseBinaryValue;
  }

  public LayeredParametersListT toMtosiRMEP(DTO<CfmMepDbAttr> rmep, DTO<MEPAttr> mep){
    //RMepIdentifier
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_RMEP_IDENTIFIER_PARAM,
        mep.getValue(MEPAttr.SHORT_DESC).replace("MEP","RMEP").concat("-"+String.valueOf(rmep.getValue(CfmMepDbAttr.RMEP_IDENTIFIER))));

    //RMepState
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_RMEP_STATE,
        CfmRMepStateTranslation.getMtosiString(rmep.getValue(CfmMepDbAttr.RMEP_STATE)));

    //RMepFailedOkTime
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_RMEP_FAILED_OK_TIME,
        String.valueOf(rmep.getValue(CfmMepDbAttr.RMEP_FAILED_OK_TIME)/100));

    //RMepMacAddress
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_RMEP_MAC_ADDRESS,
        rmep.getValue(CfmMepDbAttr.MAC_ADDRESS));

    //RMepRDI
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_RMEP_RDI,
        MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE,(rmep.getValue(CfmMepDbAttr.RDI))));

    //RMepPortStatusTlv
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_RMEP_PORT_STATUS_TLV,
        CfmRMepPortStatusTlvTranslation.getMtosiString(rmep.getValue(CfmMepDbAttr.PORT_STATUS_TLV)));

    //RMepInterfaceStatusTlv
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_CFMCCM,
        LayeredParams.LrPropAdvaCFMCCM.CFM_RMEP_INTERFACE_STATUS_TLV,
        CfmRMepInterfaceStatusTlvTranslation.getMtosiString(rmep.getValue(CfmMepDbAttr.INTERFACE_STATUS_TLV)));

    return layeredParametersListT;
  }

}
