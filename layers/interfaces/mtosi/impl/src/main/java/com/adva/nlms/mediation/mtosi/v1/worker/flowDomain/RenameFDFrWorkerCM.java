/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.NETPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.f3.entity.port.acc.MTOSIPortF3Acc;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FDFrACCEndFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FDFrEndFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FDFrFSP150CM;
import com.adva.nlms.mediation.config.mtosi.FDFr;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import v1.tmf854.HeaderT;

import jakarta.xml.ws.Holder;

public class RenameFDFrWorkerCM extends RenameFDFrWorker
{
  private String oldFdfrName;

  public RenameFDFrWorkerCM(Holder<HeaderT> mtosiHeader, NetworkElement ne, FDFr fdfr, String newFDFrName) {
    super(mtosiHeader, ne, fdfr, newFDFrName);
  }

  @Override
  protected void parse () throws Exception {
    // empty method
  }

  @Override
  protected void mediate() throws Exception
	{
		FDFrSPProperties newProps = new FDFrSPProperties(newFDFrName);
		FDFrFSP150CM fdfrCM = (FDFrFSP150CM) fdfr;
		FDFrACCEndFSP150CM lanEnd;
    FDFrEndFSP150CM aEnd = fdfrCM.getAEnd();

    oldFdfrName = fdfr.getFDFrSPProperties().get(FDFrSPProperties.VS.FDFrName);

    if (aEnd instanceof FDFrACCEndFSP150CM) {
      lanEnd = (FDFrACCEndFSP150CM) aEnd;
    } else {
      lanEnd = (FDFrACCEndFSP150CM) fdfrCM.getZEnd();
    }

		MTOSIFlowF3 flow = lanEnd.getFlow();
		FlowSPPropertiesFSP150CM existingFlowProps = flow.getFlowSPProperties();
		FlowSPPropertiesFSP150CM flowProps = new FlowSPPropertiesFSP150CM();
		flowProps.set(FlowSPPropertiesFSP150CM.VI.FlowIndex, existingFlowProps.get(FlowSPPropertiesFSP150CM.VI.FlowIndex));

		MTOSIPortF3Acc accessPort = lanEnd.getPort();
		flowProps.set(FlowSPPropertiesFSP150CM.VI.ShelfIndex, accessPort.getPortSPProperties().get(NETPortSPPropertiesFSP150CM.VI.ShelfIndex));
		flowProps.set(FlowSPPropertiesFSP150CM.VI.SlotIndex, accessPort.getPortSPProperties().get(NETPortSPPropertiesFSP150CM.VI.SlotIndex));
		flowProps.set(FlowSPPropertiesFSP150CM.VI.AccPortIndex, accessPort.getIndex().toIntArray()[accessPort.getIndex().toIntArray().length-1]);

		String flowCircuitName = NamingTranslationFactory.getFlowNameWithFDFr(newFDFrName, flow.getFlowSPProperties().get(FlowSPPropertiesFSP150CM.VS.CircuitName));
		flowProps.set(FlowSPPropertiesFSP150CM.VS.CircuitName, flowCircuitName);

		transact(newProps, flow, flowProps);
	}

  private void transact(FDFrSPProperties props, MTOSIFlowF3 flow, FlowSPPropertiesFSP150CM flowProps)
          throws NetTransactionException, SNMPCommFailure, SPValidationException, ObjectInUseException {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "RenameFDFrWorker");
    ne.getMTOSIWorker().setFDFrOperationInProgress(props.get(FDFrSPProperties.VS.FDFrName), true);
    try {
      logSecurity(ne, SystemAction.ModifyNetwork, flow.getMtosiName());
      fdfr.setFDFrSPProperties(props);
      flow.setFlowSettings(flowProps);
      NetTransactionManager.commitNetTransaction(id);
      ne.logSROperation(SROperationState.SERVICE_RENAME_SUCCESS, oldFdfrName, props.get(FDFrSPProperties.VS.FDFrName));
    } catch (NetTransactionException e) {
      ne.logSROperation(SROperationState.SERVICE_RENAME_FAILURE, props.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ne.logSROperation(SROperationState.SERVICE_RENAME_FAILURE, props.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ne.logSROperation(SROperationState.SERVICE_RENAME_FAILURE, props.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      ne.getMTOSIWorker().setFDFrOperationInProgress(props.get(FDFrSPProperties.VS.FDFrName), false);
      NetTransactionManager.ensureEnd(id);
    }
  }
}
