/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.equipment;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.NTUSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.module.MTOSICardModuleF3;
import com.adva.nlms.mediation.config.f3.entity.module.nte.MTOSINTEF3;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.entity.module.ntu.MTOSINTUFSP150CM;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.UnprovisionEquipmentResponseT;
import v1.tmf854ext.adva.UnprovisionEquipmentT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

/**
 * main class for the MTOSI operation:  u n p r o v i s i o n E q u i p m e n t
 */
public class UnprovisionEquipmentWorker extends AbstractMtosiWorker {
	Logger LOG = LogManager.getLogger(this.getClass().getName());

  protected UnprovisionEquipmentT mtosiBody;
  protected NamingAttributesT equipmentName;
  protected UnprovisionEquipmentResponseT response = new UnprovisionEquipmentResponseT();
  protected MtosiAddress mtosiAddr;
  protected NetworkElement ne;
  protected MTOSICardModuleF3 card;

  public UnprovisionEquipmentWorker(UnprovisionEquipmentT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "unprovisionEquipment", "unprovisionEquipment", "unprovisionEquipmentResponse");
    this.mtosiBody = mtosiBody;
	}

  @Override
  protected void parse() throws Exception {
    equipmentName = mtosiBody.getEquipmentName();
    mtosiAddr = new MtosiAddress(equipmentName);

    if (equipmentName == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.getMessageMandatory("equipmentName"));
    }
    if (equipmentName.getMdNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }
    if (equipmentName.getMeNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.ME_NAME_MISSING);
    }
    if (equipmentName.getEhNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.EH_NAME_MISSING);
    }
    if (equipmentName.getEqNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.EQ_NAME_MISSING);
    }
    if (!equipmentName.getEqNm().equals("1")) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.getMessageIllegal("eqNm"));
    }
    ne = ManagedElementFactory.getAndValidateNE(equipmentName);

  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
   validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  @Override
  protected void mediate() throws Exception {
    if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() != NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.ME_NOT_SUPPORTED);
    }

    if(!MtosiUtils.existsEntity(mtosiAddr)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The specified entity does not exist.");
    }

    card = ManagedElementFactory.getModuleCard(mtosiAddr);
    if (card == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The requested entity was not found.");
		}
    if (!((card instanceof MTOSINTUFSP150CM) || (card instanceof MTOSINTEF3))) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "Card not supported.");
    }
    transact();
  }

  @Override
  protected void response () throws Exception {
    // empty method
  }

  private void transact() throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    final int subnetId = getMtosiCtrl().getLegacyMtosiMOFacade().getSubnetId(ne.getID());
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "UnprovisionEquipmentWorker");
    try
    {
      logSecurity(ne, SystemAction.DeleteNetwork, card.getMtosiName());
      NTUSPPropertiesFSP150CM cardProperties = new NTUSPPropertiesFSP150CM();
      cardProperties.set(EquipmentSPPropertiesFSP150CM.VI.ShelfIndex, card.getShelfIndex());
      cardProperties.set(EquipmentSPPropertiesFSP150CM.VI.SlotIndex, card.getSlotIndex());
      if (card instanceof MTOSINTUFSP150CM) {
        getFSP150CMMTOSIWorker(((NetworkElementFSP150CM)ne)).unprovisionNTU(cardProperties);
      } else {  //I have already checked, it must be an NTE
        getFSP150CMMTOSIWorker(((NetworkElementFSP150CM)ne)).unprovisionNTE(cardProperties);
      }

      NetTransactionManager.commitNetTransaction(id);
      //workaround for not refreshed map in nms gui: artf213585
      getMtosiCtrl().getLegacyMtosiMOFacade().notifySubnetChanged(subnetId);
    }
    catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally
    {
      NetTransactionManager.ensureEnd(id);
    }
  }

  @Override
  public UnprovisionEquipmentResponseT getSuccessResponse()
	{
		if (response == null)
			return null;
		response.setTmf854Version(MtosiConstants.VERSION);
		return response;
	}
}
