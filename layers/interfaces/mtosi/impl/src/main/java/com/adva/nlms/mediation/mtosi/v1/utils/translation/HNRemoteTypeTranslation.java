/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
 /**
  *
  */
public enum HNRemoteTypeTranslation {
    Unknown       (0),
    NotPresent    (1),
    Auto          (2),
    //PME40         (3),
    PME4E1        (4),
    PME8E1        (5),
    PME4E2        (6),
    PME8E2        (7),
    //PME4E2SFP1    (8),
    PME8E2SFP1    (9),
    //PME4E1SFP1    (10),
    //PME8E1SFP1    (11),
    PME2E1        (12),
    //PME2E2        (13),
    Unmanaged     (14);
    
    
    private int mibValue;
    
    private int getMibValue() {
		return mibValue;
	}

	private HNRemoteTypeTranslation(int code) {
    	this.mibValue = code;
    }

    public static String getMtosiString(final Integer mibValue) {
    	if (mibValue == null) {
    		return MtosiConstants.NOT_APPLICABLE;
    	}
    	for (HNRemoteTypeTranslation value: values() ) {
    		if (value.getMibValue() == mibValue.intValue()) {
    			return value.name(); 
    		}
    	}
    	//the value was not found, return the value passed in
    	return String.valueOf(mibValue);
    }
    
    public static int getMibValue(final String name) {
    	for (HNRemoteTypeTranslation value: values() ) {
    		if (value.name().equals(name)) {
    			return value.getMibValue(); 
    		}
    	}
    	//Returning -1, the value passed in was not found.
    	return -1;
    }
}
