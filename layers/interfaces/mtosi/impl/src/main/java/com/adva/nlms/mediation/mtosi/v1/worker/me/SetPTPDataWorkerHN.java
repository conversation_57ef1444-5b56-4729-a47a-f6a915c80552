/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.MDRequestFailedException;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN40002BpmeSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000EthernetSProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.UNISPPropertiesHN4000;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.hn4000.PortHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN40002Bpme;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.TPDataT;
import v1.tmf854.TerminationPointT;
import v1.tmf854ext.adva.SetTPDataResponseT;

import jakarta.xml.ws.Holder;

//import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiSupported;

public class SetPTPDataWorkerHN extends SetTPDataWorker {
	Logger LOG = LogManager.getLogger(this.getClass().getName());

	public SetPTPDataWorkerHN(Holder<HeaderT> mtosiHeader, TPDataT tpInfo, NamingAttributesT namingAttributes, NetworkElement ne) {
		super(mtosiHeader, tpInfo, namingAttributes, ne);
	}

  @Override
  protected void parse () throws Exception {
    // empty method
  }

  @Override
  protected void mediate() throws Exception {
		Port port = ManagedElementFactory.getPort(namingAttributes);

		if (port instanceof PortHN4000Ethernet) {
			mediateHNEthernet((PortHN4000Ethernet) port);
		} else if (port instanceof PortHN40002Bpme) {
			mediateHN2Bpme((PortHN40002Bpme) port);
		}
	}

	private void mediateHNEthernet(PortHN4000Ethernet port) throws Exception {
		boolean hn400 = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400;
		PortHN4000EthernetSProperties newProps = MtosiTPMediator.mtosiTPDataTToHNEthernetProperties(tpInfo, port, hn400);
		UNISPPropertiesHN4000 newUniProps = MtosiTPMediator.mtosiTPDataTToHNUniProperties(tpInfo, ((NetworkElement)port.getNE()).getMTOSIWorker().getNetworkElementTypeForMTOSI(),port.hasUni());
		if (newUniProps != null && port.hasUni()) {
			UNISPPropertiesHN4000 props = port.getUni().getUniSPProperties();
			newUniProps.set(UNISPPropertiesHN4000.VL.DeviceId, props.get(UNISPPropertiesHN4000.VL.DeviceId));
			newUniProps.set(UNISPPropertiesHN4000.VL.PortId, props.get(UNISPPropertiesHN4000.VL.PortId));
		}
		transactHNEthernet(port, newProps, newUniProps);
	}

	private void mediateHN2Bpme(PortHN40002Bpme port) throws Exception {
		boolean hn400 = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400;
		PortHN40002BpmeSPProperties newProps = MtosiTPMediator.mtosiTPDataTToHN2BpmeProperties(tpInfo, port, hn400);
		transactHN2Bpme(port, newProps);
	}

	private void transactHNEthernet(PortHN4000Ethernet port, PortHN4000EthernetSProperties newProps, UNISPPropertiesHN4000 newUniProps)
			throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure, MDRequestFailedException {
		NetworkElement locks[] = new NetworkElement[] { ne };
		Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "SetTPDataWorker");
		try {
			logSecurity(ne, SystemAction.ModifyNetwork, port.getMtosiName());
			if (newProps.size() > 0) {
				port.setSettings(newProps);
			}
			if (newUniProps != null) {
				if (port.hasUni()) {
					if (newUniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null) {
						port.deleteUNI();
					} else {
						port.setUniProperties(newUniProps, port.getUni().getUniSPProperties());
					}
				} else {
					port.createUni(newUniProps);
				}
			}
			NetTransactionManager.commitNetTransaction(id);
		} catch (NetTransactionException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SPValidationException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SNMPCommFailure e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (MDRequestFailedException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} finally {
			NetTransactionManager.ensureEnd(id);
		}
	}

	private void transactHN2Bpme(PortHN40002Bpme port, PortHN40002BpmeSPProperties newProps) throws ObjectInUseException, NetTransactionException,
			SPValidationException, SNMPCommFailure {
		NetworkElement locks[] = new NetworkElement[] { ne };
		Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "SetTPDataWorker");
		try {
			logSecurity(ne, SystemAction.ModifyNetwork, port.getMtosiName());
			port.setSettings(newProps);
			NetTransactionManager.commitNetTransaction(id);
		} catch (NetTransactionException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SPValidationException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SNMPCommFailure e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} finally {
			NetTransactionManager.ensureEnd(id);
		}
	}

	@Override
  protected void response() throws Exception {
		final Port port = ManagedElementFactory.getPort(namingAttributes);
		((PortHN4000)port).doPollingVolatile();
		ObjectFactory objectFactory = new ObjectFactory();
		PhysicalTerminationPointT ptp;
		MtosiTranslator mtosiTranslator;
		if ((mtosiTranslator = new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(port)) != null) {
			ptp = mtosiTranslator.toMtosiPTP();
//			ptp = ((MtosiSupported) port).getMtosiTranslator().toMtosiPTP();
		} else {
			ptp = objectFactory.createPhysicalTerminationPointT();
		}

		TerminationPointT tp = objectFactory.createTerminationPointT();
		tp.setPtp(ptp);
		response.setModifiedTP(tp);
	}

	@Override
  public SetTPDataResponseT getSuccessResponse() {
		if (response == null)
			return null;
		response.setTmf854Version(MtosiConstants.VERSION);
		return response;
	}
}