/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import java.util.BitSet;

/**
 * Created by IntelliJ IDEA. User: mariuszg Date: 2007-06-25 Time: 10:13:25 To change this template use File | Settings
 * | File Templates.
 */
public enum CfmMepLlfTriggerTypesTranslation
{

  B_AIS                 (0, "AISDefect"),
  B_CCM_IF_STATUS_TLV   (1, "InterfaceStatus"),
  B_REMOTE_CCM          (2, "RemoteMEPCCMFailure"),
  B_RDI                 (3, "RDI"),
  NOT_APPLICABLE        (4, "n/a");


  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;
  //------------------------------------------------------------------------------------------------------------------

  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private CfmMepLlfTriggerTypesTranslation(final int mibValue, final String mtosiString)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }

  public static int getMIBValue(final String mtosiString) {
    int cfmTypeTranslation = 3;
    
    for (CfmMepLlfTriggerTypesTranslation tmpCfmTypeTranslation : values())
    {
      if (mtosiString.equals(tmpCfmTypeTranslation.getMtosiString()))
      {
        cfmTypeTranslation = tmpCfmTypeTranslation.getMIBValue();
        break;
      }
    }
    
    return cfmTypeTranslation;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int mibValue)
  {
    CfmMepLlfTriggerTypesTranslation cfmTypeTranslation = NOT_APPLICABLE;  // the return value

    for (CfmMepLlfTriggerTypesTranslation tmpCfmTypeTranslation : values())
    {
      if (mibValue == tmpCfmTypeTranslation.getMIBValue())
      {
        cfmTypeTranslation = tmpCfmTypeTranslation;
        break;
      }
    }
    return cfmTypeTranslation.getMtosiString();
  }

  public static int getBitset(String AisTriggerTypes)
  {
    String [] types = AisTriggerTypes.split(",");

    BitSet bs = new BitSet();
    BitSet bsWrongValue = new BitSet();
    for (String type : types ){
      type = type.trim();
      if (type.equals(CfmMepLlfTriggerTypesTranslation.getMtosiString(0))){
        bs.set(0, true);
      } else if (type.equals(CfmMepLlfTriggerTypesTranslation.getMtosiString(1))){
        bs.set(1, true);
      } else if (type.equals(CfmMepLlfTriggerTypesTranslation.getMtosiString(2))){
        bs.set(2, true);
      } else if (type.equals(CfmMepLlfTriggerTypesTranslation.getMtosiString(3))){
        bs.set(3, true);
      } else{
        bsWrongValue.set(0, true);
      }
    }
    int bitInteger = 0;
    if (!bsWrongValue.get(0)){
      for(int i = 0 ; i < 32; i++)
        if(bs.get(i))
          bitInteger |= (1 << i);
    }

    return bitInteger;
  }


}
