/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;

import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.ProcessingFailureException;
import ws.v1.tmf854.TransmissionDescriptorMgr;

import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.HeaderUtils;


/**
 * This class was generated by the Celtix 1.1-SNAPSHOT
 * Fri Dec 22 10:48:24 EST 2006
 * Generated source version: 1.1-SNAPSHOT
 * 
 */

@jakarta.jws.WebService(name = "TransmissionDescriptorMgr", serviceName = "ConfigurationService", portName = "TransmissionDescriptorMgrHttp", targetNamespace = "tmf854.v1.ws")
                      
public class TransmissionDescriptorMgrImpl implements TransmissionDescriptorMgr {
    /* (non-Javadoc)
     * @see ws.v1.tmf854.TransmissionDescriptorMgr#getAllTransmissionDescriptorNames(v1.tmf854.GetAllTransmissionDescriptorNamesT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllTransmissionDescriptorNames(
        v1.tmf854.GetAllTransmissionDescriptorNamesT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTransmissionDescriptorNames", "getAllTransmissionDescriptorNamesResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }

      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllTransmissionDescriptorNames(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllTransmissionDescriptorNamesT mtosiBody) throws ProcessingFailureException
    {
    	return getAllTransmissionDescriptorNames(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.TransmissionDescriptorMgr#getAllTransmissionDescriptorNamesIterator(v1.tmf854.GetAllObjectNamesIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllObjectNamesResponseT getAllTransmissionDescriptorNamesIterator(
        v1.tmf854.GetAllObjectNamesIteratorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTransmissionDescriptorNamesIterator", "getAllTransmissionDescriptorNamesResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }



      @Override
      public v1.tmf854.GetAllObjectNamesResponseT getAllTransmissionDescriptorNamesIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllObjectNamesIteratorT mtosiBody) throws ProcessingFailureException
    {
    	return getAllTransmissionDescriptorNamesIterator(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.TransmissionDescriptorMgr#getAllTransmissionDescriptors(v1.tmf854.GetAllTransmissionDescriptorsT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllTransmissionDescriptorsResponseT getAllTransmissionDescriptors(
        v1.tmf854.GetAllTransmissionDescriptorsT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTransmissionDescriptors", "getAllTransmissionDescriptorsResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAllTransmissionDescriptorsResponseT getAllTransmissionDescriptors(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAllTransmissionDescriptorsT mtosiBody) throws ProcessingFailureException
    {
    	return getAllTransmissionDescriptors(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.TransmissionDescriptorMgr#getAllTransmissionDescriptorsIterator(v1.tmf854.GetTmdIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAllTransmissionDescriptorsResponseT getAllTransmissionDescriptorsIterator(
        v1.tmf854.GetTmdIteratorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAllTransmissionDescriptorsIterator", "getAllTransmissionDescriptorsResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }



      @Override
      public v1.tmf854.GetAllTransmissionDescriptorsResponseT getAllTransmissionDescriptorsIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetTmdIteratorT mtosiBody) throws ProcessingFailureException
    {
    	return getAllTransmissionDescriptorsIterator(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.TransmissionDescriptorMgr#getAssociatedTPs(v1.tmf854.GetAssociatedTPsT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAssociatedTPsResponseT getAssociatedTPs(
        v1.tmf854.GetAssociatedTPsT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
		HeaderUtils.formatResponseHeader(mtosiHeader, "getAssociatedTPs", "getAssocicatedTPsResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }



      @Override
      public v1.tmf854.GetAssociatedTPsResponseT getAssociatedTPs(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetAssociatedTPsT mtosiBody) throws ProcessingFailureException
    {
		return getAssociatedTPs(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.TransmissionDescriptorMgr#getAssociatedTPsIterator(v1.tmf854.GetTpIteratorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetAssociatedTPsResponseT getAssociatedTPsIterator(
        v1.tmf854.GetTpIteratorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    {
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getAssociatedTPsIterator", "getAssocicatedTPsResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetAssociatedTPsResponseT getAssociatedTPsIterator(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetTpIteratorT mtosiBody) throws ProcessingFailureException
    {
    	return getAssociatedTPsIterator(mtosiBody, mtosiHeader);
    }

    /* (non-Javadoc)
     * @see ws.v1.tmf854.TransmissionDescriptorMgr#getTransmissionDescriptor(v1.tmf854.GetTransmissionDescriptorT  mtosiBody ,)v1.tmf854.HeaderT  mtosiHeader )*
     */
    public v1.tmf854.GetTransmissionDescriptorResponseT getTransmissionDescriptor(
        v1.tmf854.GetTransmissionDescriptorT mtosiBody,
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader) throws ProcessingFailureException
    { 
    	HeaderUtils.formatResponseHeader(mtosiHeader, "getTransmissionDescriptor", "getTransmissionDescriptorResponse");
    	ProcessingFailureExceptionT pfet = ExceptionUtils.formatUnimplementedOperationException(mtosiHeader);
    	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
    	ExceptionUtils.nukeMtosiHeader(mtosiHeader, pfe);
    	throw pfe;
    }


      @Override
      public v1.tmf854.GetTransmissionDescriptorResponseT getTransmissionDescriptor(
        jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
        v1.tmf854.GetTransmissionDescriptorT mtosiBody) throws ProcessingFailureException
    {
    	return getTransmissionDescriptor(mtosiBody, mtosiHeader);
    }
}
