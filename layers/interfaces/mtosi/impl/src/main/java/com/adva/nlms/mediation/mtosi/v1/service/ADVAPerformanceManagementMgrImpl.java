/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;


import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.pm.GetAllCurrentPMDataWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.pm.GetAllHistoricalPMDataWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import ws.v1.tmf854ext.adva.ADVAPerformanceManagementMgr;

import jakarta.annotation.Resource;
import jakarta.xml.ws.WebServiceContext;

/**
 * This class was generated by the CXF 2.0.1-incubator Wed Dec 12 11:28:33 CET
 * 2007 Generated source version: 2.0.1-incubator
 * 
 */

@jakarta.jws.WebService(name = "ADVAPerformanceManagementMgr", serviceName = "ADVAConfigurationService", portName = "ADVAPerformanceManagementMgrHttp", targetNamespace = "adva.tmf854ext.v1.ws", endpointInterface = "ws.v1.tmf854ext.adva.ADVAPerformanceManagementMgr")
public class ADVAPerformanceManagementMgrImpl implements ADVAPerformanceManagementMgr
{

	private static final Logger LOG = LogManager.getLogger(ADVAPerformanceManagementMgrImpl.class.getName());
	// Inject the context
	@Resource
	private WebServiceContext context;

	/*
	 * (non-Javadoc)
	 * 
	 * @see ws.v1.tmf854ext.adva.ADVAPerformanceManagementMgr#clearPMData(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.ClearPMDataT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.ClearPMDataResponseT clearPMData(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader, v1.tmf854ext.adva.ClearPMDataT mtosiBody)
			throws ws.v1.tmf854.ProcessingFailureException
  {
    LOG.info("Executing MTOSI operation clearPMData...");
    return null;
  }

	/*
	 * (non-Javadoc)
	 * 
	 * @see ws.v1.tmf854ext.adva.ADVAPerformanceManagementMgr#getAllCurrentPMData(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.GetAllCurrentPMDataT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.GetAllCurrentPMDataResponseT getAllCurrentPMData(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
			v1.tmf854ext.adva.GetAllCurrentPMDataT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllCurrentPMDataWorker.class, mtosiBody, mtosiHeader,
            "getAllCurrentPMData", context, LOG);
  }

	/*
	 * (non-Javadoc)
	 * 
	 * @see ws.v1.tmf854ext.adva.ADVAPerformanceManagementMgr#getAllHistoricalPMData(v1.tmf854.HeaderT
	 *      mtosiHeader ,)v1.tmf854ext.adva.GetAllHistoricalPMDataT mtosiBody )*
	 */
	@Override
  public v1.tmf854ext.adva.GetAllHistoricalPMDataResponseT getAllHistoricalPMData(jakarta.xml.ws.Holder<v1.tmf854.HeaderT> mtosiHeader,
			v1.tmf854ext.adva.GetAllHistoricalPMDataT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException {
    return ServiceUtils.runMethod(GetAllHistoricalPMDataWorker.class, mtosiBody, mtosiHeader,
            "getAllHistoricalPMData", context, LOG);
	}
}
