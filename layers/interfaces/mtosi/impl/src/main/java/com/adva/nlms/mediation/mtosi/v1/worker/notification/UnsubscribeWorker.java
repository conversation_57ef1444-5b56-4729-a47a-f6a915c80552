/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */
package com.adva.nlms.mediation.mtosi.v1.worker.notification;

import com.adva.nlms.mediation.mtosi.common.notification.MtosiNotificationConstants;
import com.adva.nlms.mediation.mtosi.common.notification.MtosiNotificationHelper;
import com.adva.nlms.mediation.mtosi.common.notification.NotificationEnum;
import com.adva.nlms.mediation.mtosi.common.notification.NotificationException;
import com.adva.nlms.mediation.mtosi.common.notification.SubscribeUnsubscribeOperations;
import com.adva.nlms.mediation.mtosi.common.notification.Subscriber;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import v1.tmf854.HeaderT;
import v1.tmf854.UnsubscribeResponseT;
import v1.tmf854.UnsubscribeT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public class UnsubscribeWorker extends AbstractMtosiWorker {


  protected UnsubscribeT mtosiBody;
  protected UnsubscribeResponseT response=new UnsubscribeResponseT();

  private Subscriber subscriber = new Subscriber();
  private String topic;

  private SubscribeUnsubscribeOperations subscribeUnsubscribeOperations;

  private static Logger LOG = LoggerFactory.getLogger(SubscribeWorker.class);

  public UnsubscribeWorker(UnsubscribeT mtosiBody,Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "unsubscribe", "unsubscribe", "unsubscribeResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    //To change body of implemented methods use File | Settings | File Templates.
  }

  @Override
  protected void parse() throws Exception {
    try {
      //will throw an exception if topic does not exist
      MtosiNotificationHelper.topicExists(topic = this.mtosiBody.getTopic());
    } catch (NotificationException nex) {
      LOG.debug(nex.getMessage(), nex);
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_TOPIC, nex.getMessage());
    }

    if (this.mtosiBody.getSubscriptionID() == null) {
      LOG.debug(MtosiNotificationConstants.ERROR_ILLEGAL_SUBSCRIPTION_ID);
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiNotificationConstants.ERROR_ILLEGAL_SUBSCRIPTION_ID);
    }
    subscriber.setId(this.mtosiBody.getSubscriptionID());
    subscriber.setTopic(topic);
    subscriber.setMtosiVersion(NotificationEnum.V1.getId());
  }

  @Override
  protected void mediate() throws Exception {
    //will throw an exception if user is not subscribed
    try {
      setSubscribeUnsubscribeOperations(SubscribeUnsubscribeOperations.getInstance());
      subscribeUnsubscribeOperations.unsubscribe(subscriber,getMtosiCtrl());
    } catch (NotificationException nex) {
      LOG.debug(nex.getMessage(),nex);
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, nex.getMessage());
    }
  }

  public SubscribeUnsubscribeOperations getSubscribeUnsubscribeOperations() {
    return subscribeUnsubscribeOperations;
  }

  public void setSubscribeUnsubscribeOperations(SubscribeUnsubscribeOperations subscribeUnsubscribeOperations) {
    this.subscribeUnsubscribeOperations = subscribeUnsubscribeOperations;
  }

  @Override
  protected void response() throws Exception {
    //To change body of implemented methods use File | Settings | File Templates.
  }

  @Override
  public UnsubscribeResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
