/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import jakarta.xml.ws.Holder;
import v1.tmf854.HeaderT;
import v1.tmf854.NVSListT;
import v1.tmf854.NameAndStringValueT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;

import com.adva.nlms.mediation.common.serviceProvisioning.ContinuityTestResultPropertiesFSP150CP;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.PortFSP150CPNetwork;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.common.snmp.TestResultTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.TestStatusTranslation;

public class GetContinuityTestWorkerCP extends GetContinuityTestWorker {
  private ContinuityTestResultPropertiesFSP150CP result;
  public GetContinuityTestWorkerCP (Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne) {
    super(mtosiHeader, namingAttributes, ne);
  }

  @Override
  protected void mediate() throws Exception {
    super.mediate();
    if (!(port instanceof PortFSP150CPNetwork)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "Continuity test should be run on Network Port.");
    }
    PortFSP150CPNetwork portNET = (PortFSP150CPNetwork) port;
    result = portNET.getContiniutyTest();
  }

  @Override
  protected void response() throws Exception {
    response.setTestResults(getTestResults());
    response.setTestStatus(TestStatusTranslation.getMtosiString(result.get(ContinuityTestResultPropertiesFSP150CP.VI.TestStatus)));
  }

  private NVSListT getTestResults() {
    final ObjectFactory objFactory = new ObjectFactory();
    final NVSListT nvsListT = objFactory.createNVSListT();

    NameAndStringValueT nameAndStringValueT = objFactory.createNameAndStringValueT();
    nameAndStringValueT.setName("TestResult");
    if (result.get(ContinuityTestResultPropertiesFSP150CP.VL.OutTestFrames) == 0) {
      nameAndStringValueT.setValue(TestResultTranslation.getMtosiString(2/*Fail*/));
    } else {
      nameAndStringValueT.setValue(TestResultTranslation.getMtosiString(result.get(ContinuityTestResultPropertiesFSP150CP.VI.TestResult)));
    }
    nvsListT.getNvs().add(nameAndStringValueT);

    nameAndStringValueT = objFactory.createNameAndStringValueT();
    nameAndStringValueT.setName("OutTestFrames");
    nameAndStringValueT.setValue(Long.toString(result.get(ContinuityTestResultPropertiesFSP150CP.VL.OutTestFrames)));
    nvsListT.getNvs().add(nameAndStringValueT);

    nameAndStringValueT = objFactory.createNameAndStringValueT();
    nameAndStringValueT.setName("RemoteInTestFrames");
    nameAndStringValueT.setValue(Long.toString(result.get(ContinuityTestResultPropertiesFSP150CP.VL.RemoteInTestFrames)));
    nvsListT.getNvs().add(nameAndStringValueT);

    nameAndStringValueT = objFactory.createNameAndStringValueT();
    nameAndStringValueT.setName("RemoteOutTestFrames");
    nameAndStringValueT.setValue(Long.toString(result.get(ContinuityTestResultPropertiesFSP150CP.VL.RemoteOutTestFrames)));
    nvsListT.getNvs().add(nameAndStringValueT);

    nameAndStringValueT = objFactory.createNameAndStringValueT();
    nameAndStringValueT.setName("InTestFrames");
    nameAndStringValueT.setValue(Long.toString(result.get(ContinuityTestResultPropertiesFSP150CP.VL.InTestFrames)));
    nvsListT.getNvs().add(nameAndStringValueT);

    nameAndStringValueT = objFactory.createNameAndStringValueT();
    nameAndStringValueT.setName("FCSErrors");
    nameAndStringValueT.setValue(Integer.toString(result.get(ContinuityTestResultPropertiesFSP150CP.VI.FCSErrors)));
    nvsListT.getNvs().add(nameAndStringValueT);

    nameAndStringValueT = objFactory.createNameAndStringValueT();
    nameAndStringValueT.setName("RemoteFCSErrors");
    nameAndStringValueT.setValue(Integer.toString(result.get(ContinuityTestResultPropertiesFSP150CP.VI.RemoteFCSErrors)));
    nvsListT.getNvs().add(nameAndStringValueT);

    return nvsListT;
  }
}
