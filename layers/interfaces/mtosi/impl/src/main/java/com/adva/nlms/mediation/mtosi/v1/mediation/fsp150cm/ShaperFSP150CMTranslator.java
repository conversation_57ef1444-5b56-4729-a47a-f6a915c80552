/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm;

import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.ShaperSPProperties;
import com.adva.nlms.mediation.config.f3.entity.shaper.qosshaper.QOSShaperF3;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSecondaryStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMServiceStateTranslation;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import v1.tmf854ext.adva.TCProfileT;
import ws.v1.tmf854.ProcessingFailureException;

/**
 * This class is an FSP 150 CM shaper MTOSI Translator.
 */
public class ShaperFSP150CMTranslator extends MtosiTranslator {
  private QOSShaperF3 shaper;

  public ShaperFSP150CMTranslator(QOSShaperF3 shaper) {
    this.shaper = shaper;
  }

  @Override
  public TCProfileT toMtosiTCProfile() throws ProcessingFailureException {
    final v1.tmf854ext.adva.ObjectFactory objFactoryEx = new v1.tmf854ext.adva.ObjectFactory();
    final ObjectFactory objFactory = new ObjectFactory();
    final TCProfileT tcProfileT = objFactoryEx.createTCProfileT();
    final ShaperSPProperties props = shaper.getShaperSPProperties();

    // TCProfile Name
    final NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributes(shaper);
    tcProfileT.setName(objFactoryEx.createTCProfileTName(namingAttributes));

    // discoveredName
    final String tcpNm = namingAttributes.getTcpNm();
    if (tcpNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID);
    }
    tcProfileT.setDiscoveredName(objFactoryEx.createTCProfileTDiscoveredName(tcpNm));

    // namingOS
    tcProfileT.setNamingOS(objFactoryEx.createTCProfileTNamingOS(OSFactory.getNmsName()));

    // source
    final SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    tcProfileT.setSource(objFactoryEx.createTCProfileTSource(source));

    // layers
    final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_SHAPER);
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_SHAPER,
            LayeredParams.LrPropAdvaEthernetShaper.ADMINISTRATION_CONTROL_PARAM,
            MtosiUtils.getMtosiString(CMAdministrationControlTranslation.NOT_APPLICABLE, props.get(ShaperSPProperties.VI.AdminState)));

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_SHAPER,
            LayeredParams.LrPropAdvaEthernetShaper.SERVICE_STATE_PARAM,
            CMServiceStateTranslation.getMtosiString(props.get(ShaperSPProperties.VI.AdminState), props.get(ShaperSPProperties.VI.OperationalState)));

    if (props.get(ShaperSPProperties.VI.SecondaryState) != null) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_SHAPER,
              LayeredParams.LrPropAdvaEthernetShaper.SECONDARY_STATE_PARAM,
              CMSecondaryStateTranslation.getMtosiString(props.get(ShaperSPProperties.VI.SecondaryState)));
    }

    if (props.get(ShaperSPProperties.VI.TypeIndex) == MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_A2N) {
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_SHAPER,
              LayeredParams.LrPropAdvaEthernetShaper.INGRESS_CIR_PARAM, Long.toString(props.get(ShaperSPProperties.VL.CIR)));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_SHAPER,
              LayeredParams.LrPropAdvaEthernetShaper.INGRESS_EIR_PARAM, Long.toString(props.get(ShaperSPProperties.VL.EIR)));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_SHAPER,
              LayeredParams.LrPropAdvaEthernetShaper.INGRESS_CBS_PARAM, Long.toString(props.get(ShaperSPProperties.VL.CBS)));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_SHAPER,
              LayeredParams.LrPropAdvaEthernetShaper.INGRESS_EBS_PARAM, Long.toString(props.get(ShaperSPProperties.VL.EBS)));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_SHAPER,
              LayeredParams.LrPropAdvaEthernetShaper.INGRESS_BUFFER_SIZE_PARAM, Long.toString(props.get(ShaperSPProperties.VL.BufferSize)));
    } else { // egress case
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_SHAPER,
              LayeredParams.LrPropAdvaEthernetShaper.EGRESS_CIR_PARAM, Long.toString(props.get(ShaperSPProperties.VL.CIR)));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_SHAPER,
              LayeredParams.LrPropAdvaEthernetShaper.EGRESS_EIR_PARAM, Long.toString(props.get(ShaperSPProperties.VL.EIR)));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_SHAPER,
              LayeredParams.LrPropAdvaEthernetShaper.EGRESS_CBS_PARAM, Long.toString(props.get(ShaperSPProperties.VL.CBS)));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_SHAPER,
              LayeredParams.LrPropAdvaEthernetShaper.EGRESS_EBS_PARAM, Long.toString(props.get(ShaperSPProperties.VL.EBS)));

      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_SHAPER,
              LayeredParams.LrPropAdvaEthernetShaper.EGRESS_BUFFER_SIZE_PARAM, Long.toString(props.get(ShaperSPProperties.VL.BufferSize)));
    }
    // -------end of Layer-------

    tcProfileT.setTransmissionParams(objFactoryEx.createTCProfileTTransmissionParams(layeredParametersListT));
    return tcProfileT;
  }
}
