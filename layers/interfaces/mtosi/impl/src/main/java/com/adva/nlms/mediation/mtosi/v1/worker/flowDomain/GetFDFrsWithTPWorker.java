/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;


import jakarta.xml.ws.Holder;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.FDFrListT;
import v1.tmf854ext.adva.GetFDFrsWithTPResponseT;
import v1.tmf854ext.adva.GetFDFrsWithTPT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;


public class GetFDFrsWithTPWorker extends AbstractMtosiWorker
{
  protected GetFDFrsWithTPT mtosiBody;
  protected GetFDFrsWithTPResponseT response = new GetFDFrsWithTPResponseT();
  protected NamingAttributesT tpName;
  protected FDFrListT fdFrListT;
private NetworkElement ne;

  public GetFDFrsWithTPWorker (final GetFDFrsWithTPT mtosiBody, final Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getFDFrsWithTP", "getFDFrsWithTP", "getFDFrsWithTPResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    if ((tpName = mtosiBody.getTpName()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
              MtosiErrorConstants.INVALID_FILTER);
    }

    if (!NamingTranslationFactory.isManagementDomain(tpName)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!tpName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MD_NOT_FOUND);
    }
	ne = ManagedElementFactory.getAndValidateNE(tpName);
	
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  @Override
  protected void mediate() throws Exception {
    fdFrListT = ManagedElementFactory.getFDFrsWithTP(tpName, ne);
  }

  @Override
  protected void response() throws Exception {
    response.setFdfrList(fdFrListT);
  }

  @Override
  public GetFDFrsWithTPResponseT getSuccessResponse()
  {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }

}
