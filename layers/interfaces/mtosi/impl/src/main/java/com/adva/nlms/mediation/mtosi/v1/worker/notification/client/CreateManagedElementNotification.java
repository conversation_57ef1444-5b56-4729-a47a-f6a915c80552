/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.worker.notification.client;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.MtosiCtrl;
import com.adva.nlms.mediation.mtosi.common.notification.MtosiNotificationConstants;
import com.adva.nlms.mediation.mtosi.common.notification.MtosiNotificationHelper;
import com.adva.nlms.mediation.mtosi.common.notification.SubscribeUnsubscribeOperations;

import java.util.List;

/**
 * Create managed element notificaiton is handled via MTOSI because MO
 * does not have an NE name when the NE is created
 */
public class CreateManagedElementNotification implements Runnable {
  private List<String> topics;
  private SubscribeUnsubscribeOperations subscribeUnsubscribeOperations;
  private NetworkElement ne;
  private MtosiCtrl mtosiCtrl;

  public CreateManagedElementNotification(MtosiNotificationHelper mth,
                                          SubscribeUnsubscribeOperations subscribeUnsubscribeOperations,
                                          MtosiCtrl mtosiCtrl) {
    topics = mth.getTopicsFromEvents(MtosiNotificationConstants.OBJECT_CREATION_EVENT);
    this.subscribeUnsubscribeOperations = subscribeUnsubscribeOperations;
    this.mtosiCtrl = mtosiCtrl;
  }

  public void setNe(NetworkElement ne) {
    this.ne = ne;
  }

  public void setMtosiCtrl(MtosiCtrl mtosiCtrl) {
    this.mtosiCtrl = mtosiCtrl;
  }

  @Override
  public void run() {
//    MtosiNotifications notify = new MtosiNotificationsFacade();
//    for (String topic : topics) {
//      if (subscribeUnsubscribeOperations.getSubscriberListPerTopic(topic) != null &&
//              !subscribeUnsubscribeOperations.getSubscriberListPerTopic(topic).isEmpty()) {
//        notify.createNENotification(ne, topic,
//                subscribeUnsubscribeOperations.getSubscriberListPerTopic(topic), mtosiCtrl);
//      }
//    }
  }

}
