/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

/**
 * Created by IntelliJ IDEA. User: Lukasz Date: 2007-05-30 Time: 13:14:09 To change this template use File | Settings |
 * File Templates.
 */
public enum WANFaultPropagationTranslation{
  NONE           (0, "None"),
  DISABLED_LAN   (1, "DisableLAN"),
  EFM            (2, "RemoteEFMOAM"),
  NOT_APPLICABLE (3, "n/a");

  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private WANFaultPropagationTranslation (final int mibValue, final String mtosiString)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int mibValue)
  {
    WANFaultPropagationTranslation wanFaultPropagationTranslation = NOT_APPLICABLE;  // the return value

    for (WANFaultPropagationTranslation tmpWANFaultPropagationTranslation : values())
    {
      if (mibValue == tmpWANFaultPropagationTranslation.getMIBValue())
      {
        wanFaultPropagationTranslation = tmpWANFaultPropagationTranslation;
        break;
      }
    }
    return wanFaultPropagationTranslation.getMtosiString();
  }
  /**
   * Returns the string representation used in MTOSI layer.
   * @param mtosiString  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static int getMIBValue (final String mtosiString)
  {
    WANFaultPropagationTranslation wanFaultPropagationTranslation = NOT_APPLICABLE;  // the return value

    for (WANFaultPropagationTranslation tmpWANFaultPropagationTranslation : values())
    {
      if (mtosiString.equals(tmpWANFaultPropagationTranslation.getMtosiString()))
      {
        wanFaultPropagationTranslation = tmpWANFaultPropagationTranslation;
        break;
      }
    }
    return wanFaultPropagationTranslation.getMIBValue();

  }
}
