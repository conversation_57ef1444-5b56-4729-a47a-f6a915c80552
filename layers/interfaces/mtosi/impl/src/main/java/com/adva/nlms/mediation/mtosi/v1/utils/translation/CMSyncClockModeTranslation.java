/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum CMSyncClockModeTranslation implements TranslatableEnum {
  NOT_APPLICABLE  (6, "NotAvailable"),
  FREE_RUN        (1, "FreeRun"),
  HOLDOVER        (2, "Holdover"),
  TRACKING        (3, "Tracking"),
  LOSS_OF_LOCK    (4, "LossOfLock"),
  LOCKED          (5, "Locked");

  private final int    mibValue;
  private final String mtosiString;

  private CMSyncClockModeTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}
