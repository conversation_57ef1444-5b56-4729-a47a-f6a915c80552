/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.ReleaseVLANLoopbackResponseT;
import v1.tmf854ext.adva.ReleaseVLANLoopbackT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public class ReleaseVlanLoopbackWorker extends AbstractMtosiWorker
{

  protected ReleaseVLANLoopbackT mtosiBody;
  protected ReleaseVLANLoopbackResponseT response = new ReleaseVLANLoopbackResponseT();
  protected NamingAttributesT namingAttributes;
  protected NetworkElement ne;

  public ReleaseVlanLoopbackWorker(ReleaseVLANLoopbackT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "releaseVLANLoopback", "releaseVLANLoopback", "releaseVLANLoopbackResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    if ((namingAttributes = mtosiBody.getTpName()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The TP name has not been specified.");
    }
    ne = ManagedElementFactory.getAndValidateNE(namingAttributes);

  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
   validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  @Override
  protected void mediate() throws Exception {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The specified ME does not support VLAN Loopback.");
  }

  @Override
  protected void response () throws Exception {
    // empty method
  }

  @Override
  public ReleaseVLANLoopbackResponseT getSuccessResponse()
  {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}