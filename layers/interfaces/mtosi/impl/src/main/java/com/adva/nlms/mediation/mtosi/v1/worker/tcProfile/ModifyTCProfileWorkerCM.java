/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.tcProfile;

import com.adva.nlms.common.NEUtils;
import com.adva.nlms.mediation.common.serviceProvisioning.F3PolicerSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ShaperSPProperties;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.f3.entity.policer.qospolicer.QOSFlowPolicer;
import com.adva.nlms.mediation.config.f3.entity.policer.qospolicer.QOSFlowPolicerImpl;
import com.adva.nlms.mediation.config.f3.entity.shaper.qosshaper.QOSShaperF3;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTCProfileMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854ext.adva.TCProfileCreateDataT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.ws.Holder;

public class ModifyTCProfileWorkerCM extends ModifyTCProfileWorker {
  private static Logger LOG = LogManager.getLogger(ModifyTCProfileWorkerCM.class.getName());

  public ModifyTCProfileWorkerCM (Holder<HeaderT> mtosiHeader, TCProfileCreateDataT tcProfileCreateDataT, NamingAttributesT name, NetworkElement ne) {
    super(mtosiHeader, tcProfileCreateDataT, name, ne);
  }

  @Override
  protected void mediate() throws Exception {
    final JAXBElement<LayeredParametersListT> transmissionParams = tcProfileCreateDataT.getTransmissionParams();

    final Port port = ManagedElementFactory.getPort(name);

    int neType = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI();
    if (!(tcpNm.startsWith(MtosiConstants.ING_SHAPER_TEXT) ||
          tcpNm.startsWith(MtosiConstants.EG_SHAPER_TEXT) ||
          (tcpNm.startsWith(MtosiConstants.ING_POLICER_TEXT) && NEUtils.isGEDevice(neType)) ||
          (tcpNm.startsWith(MtosiConstants.EG_POLICER_TEXT) && NEUtils.isGEDevice(neType)))) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID);
    }

    final MTOSIFlowF3 flow = ManagedElementFactory.getCMFlow(port, name.getCtpNm());  //get flow from port
    if(flow==null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.CTP_NOT_FOUND);
    }

    if (tcpNm.contains(MtosiConstants.SHAPER_TEXT)) {
      final ShaperSPProperties shaperSPProperties = MtosiTCProfileMediator.createShaperSPProperties(name, transmissionParams, flow);
      final int typeOfShaper = shaperSPProperties.get(ShaperSPProperties.VI.TypeIndex, "Index cannot be null.");
      final int shaperIndex  = shaperSPProperties.get(ShaperSPProperties.VI.Index, "Index cannot be null.");
      final QOSShaperF3 shaper = flow.getShaperFSP150CM(typeOfShaper, shaperIndex);
      if(shaper == null) {
        final ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.TC_PROFILE_NOT_FOUND);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      transact(shaper, shaperSPProperties);
    }
    else {
      final F3PolicerSPProperties policerSPProperties = MtosiTCProfileMediator.createPolicerSPProperties(name, transmissionParams, flow);
      final int typeOfPolicer = policerSPProperties.get(ShaperSPProperties.VI.TypeIndex, "Index cannot be null.");
      final int policerIndex  = policerSPProperties.get(ShaperSPProperties.VI.Index, "Index cannot be null.");
      final QOSFlowPolicerImpl policer = (QOSFlowPolicerImpl)flow.getPolicer(typeOfPolicer, policerIndex);
      if(policer == null) {
        final ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.TC_PROFILE_NOT_FOUND);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
      transact(policer, policerSPProperties);
    }
  }

  private void transact(QOSShaperF3 shaper, final ShaperSPProperties shaperSPProperties) throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    final NetworkElement locks[] = new NetworkElement[]
            { ne };
    final Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "ModifyTCProfileWorkerCM");
    try {
      shaper.setSettings(shaperSPProperties);
      NetTransactionManager.commitNetTransaction(id);
    } catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      NetTransactionManager.ensureEnd(id);
    }
  }

  private void transact(QOSFlowPolicer policer, final F3PolicerSPProperties policerSPProperties) throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    final NetworkElement locks[] = new NetworkElement[]
            { ne };
    final Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "ModifyTCProfileWorkerCM");
    try {
      policer.setSettings(policerSPProperties);
      NetTransactionManager.commitNetTransaction(id);
    } catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      NetTransactionManager.ensureEnd(id);
    }
  }
}
