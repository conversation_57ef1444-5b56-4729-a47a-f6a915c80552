/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.equipment;

import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.ModuleCardSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.PsuSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.module.MTOSICardModuleF3;
import com.adva.nlms.mediation.config.f3.entity.module.MTOSIEthernetModuleF3;
import com.adva.nlms.mediation.config.f3.entity.module.fan.MTOSIFanF3;
import com.adva.nlms.mediation.config.f3.entity.module.nemi.MTOSINemiF3;
import com.adva.nlms.mediation.config.f3.entity.module.nte.MTOSINTEF3;
import com.adva.nlms.mediation.config.f3.entity.module.psu.PowerSupplyF3;
import com.adva.nlms.mediation.config.f3.mtosi.NetworkElementFSP150CMMTOSIOperations;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.entity.module.ntu.MTOSINTUFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.entity.module.scut.ScuTFSP150CMImpl;
import com.adva.nlms.mediation.config.fsp20X.NetworkElementFSPGE20X;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiEquipmentMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.EquipmentMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854ext.adva.EQDataT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

/**
 * main class for the MTOSI operation:  s e t E q u i p m e n t D a t a (FSP150CM)
 */
public class SetEquipmentDataWorkerCM extends SetEquipmentDataWorker {
  private MTOSICardModuleF3 card;
  private PowerSupplyF3 psu;
  private MTOSIFanF3 fan;

  public SetEquipmentDataWorkerCM (Holder<HeaderT> mtosiHeader, EQDataT equipmentData, NamingAttributesT equipmentName, NetworkElement ne) {
    super(mtosiHeader, equipmentData, equipmentName, ne);
  }

  @Override
  protected void mediate() throws Exception {
    NetworkElementFSP150CM neCM = (NetworkElementFSP150CM) ne;
    int shelfIndex = mtosiAddr.getShelfNumber();
    int slotIndex = mtosiAddr.getNmsFixedSlotNumber();
    NetworkElementFSP150CMMTOSIOperations mtosiWorker = getFSP150CMMTOSIWorker(neCM);
    switch (mtosiAddr.getFixedSlotCardType()) {
      case MIBFSP150CM.Entity.SlotTable.TYPE_GENERIC_INDEX:
      case MIBFSP150CM.Entity.SlotTable.TYPE_SCU_INDEX: {
        card = mtosiWorker.getModule(shelfIndex, slotIndex);
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_PSU_INDEX: {
        psu = mtosiWorker.getPSU(shelfIndex, slotIndex);
        break;
      }
      case MIBFSP150CM.Entity.SlotTable.TYPE_FAN_INDEX: {
        fan = mtosiWorker.getFAN(shelfIndex, slotIndex);
        break;
      }
    }
    if (card == null && psu == null && fan == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
              ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, "The requested entity was not found.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    if (vendorExtensions != null) {
      if (card != null) {
        if (card instanceof MTOSIEthernetModuleF3) {
          transact(MtosiEquipmentMediator.mtosiVendorExtensionsTToNTUProperties(vendorExtensions));
        }
        else {
          transact(MtosiEquipmentMediator.mtosiVendorExtensionsTToModuleCardProperties(vendorExtensions));
        }
      }
      else  {
        transact(MtosiEquipmentMediator.mtosiVendorExtensionsTToModuleCardProperties(vendorExtensions));
      }
    }
  }

  private void transact(ModuleCardSPPropertiesFSP150CM properties)
          throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "SetTPDataWorker");
    try {
      if (card != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, card.getMtosiName());
        card.setSettings(properties);
      } else if (psu != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, psu.getMtosiName());
        PsuSPPropertiesFSP150CM psuProps = new PsuSPPropertiesFSP150CM();
        psuProps.set(EquipmentSPProperties.VI.AdminState, properties.get(EquipmentSPProperties.VI.AdminState));
        psu.setSettings(psuProps);
      } else {
        logSecurity(ne, SystemAction.ModifyNetwork, fan.getMtosiName());
        EquipmentSPPropertiesFSP150CM fanProps = new EquipmentSPPropertiesFSP150CM();
        fanProps.set(EquipmentSPProperties.VI.AdminState, properties.get(EquipmentSPProperties.VI.AdminState));
        fan.setSettings(properties);
      }

      NetTransactionManager.commitNetTransaction(id);
    }
    catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally
    {
      NetTransactionManager.ensureEnd(id);
    }
  }

  @Override
  protected void response() throws Exception {
    EquipmentSPPropertiesFSP150CM props;
    String equipmentType;
    String mismatchType = "";
    if (card != null) {
      props = (EquipmentSPPropertiesFSP150CM) card.getEquipmentSPProperties();
      if (card instanceof MTOSINTUFSP150CM) {
        equipmentType = MtosiConstants.EQUIPMENT_NTU_GE;
        mismatchType = MtosiConstants.EQUIPMENT_NTE_GE_SYNC;
      } else if(card instanceof MTOSINTEF3 && ne instanceof NetworkElementFSPGE20X){
        equipmentType = MtosiConstants.EQUIPMENT_NTE_GE;
      } else if (card instanceof MTOSINTEF3) {
        equipmentType = MtosiConstants.EQUIPMENT_NTE_GE_SYNC;
        mismatchType = MtosiConstants.EQUIPMENT_NTU_GE;
      } else if (card instanceof MTOSINemiF3) {
        equipmentType = MtosiConstants.EQUIPMENT_NEMI;
      } else  if (card instanceof ScuTFSP150CMImpl) {
        equipmentType = MtosiConstants.EQUIPMENT_SCU_T;
        mismatchType = MtosiConstants.EQUIPMENT_SCU;
      } else {
        equipmentType = MtosiConstants.EQUIPMENT_SCU;
        mismatchType = MtosiConstants.EQUIPMENT_SCU_T;
      }
    }
    else if (psu != null) {
      props = (EquipmentSPPropertiesFSP150CM) psu.getEquipmentSPProperties();
      int psuType = ((PsuSPPropertiesFSP150CM)psu.getEquipmentSPProperties()).get(PsuSPPropertiesFSP150CM.VI.PsuType);
      if (psuType == MIBFSP150CM.Entity.PsuTable.TYPE_AC_INDEX) {
        equipmentType = MtosiConstants.EQUIPMENT_PSU_AC;
      } else if (psuType == MIBFSP150CM.Entity.PsuTable.TYPE_DC_INDEX){
    	  equipmentType = MtosiUtils.getGE20XPsuType(props.get(EquipmentSPProperties.VS.ModelName));
      } else {
    	  equipmentType = MtosiConstants.EQUIPMENT_PSU;
      }
    }
    else {
      assert fan != null;
      props = (EquipmentSPPropertiesFSP150CM) fan.getEquipmentSPProperties();
      equipmentType = MtosiConstants.EQUIPMENT_FAN;
    }

    response.setModifiedEquipment(EquipmentMediator.nmsFixedSlotPropertiesToMTOSIEquipment((NetworkElementFSP150CM)ne, props, equipmentType, mismatchType).getEq());
  }

}
