/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum F3SyncDomainTypeTranslation implements TranslatableEnum {
  NOT_APPLICABLE  (0, "n/a"),
  <PERSON>ssi<PERSON>         (1, "Chassi<PERSON>"),
  Linecard        (2, "Linecard");

  private final int    mibValue;
  private final String mtosiString;

  private F3SyncDomainTypeTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}
