/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */
package com.adva.nlms.mediation.mtosi.v2.utils.facility.continuity;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.FlowPointAccF3Attr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.dto.attr.QOSAccFlowPointPolicerF3Attr;

public class FlowPointAccCont extends BaseFlowForContinuityTest<FlowPointAccF3Attr> {

  public FlowPointAccCont(int neId, DTO<FlowPointAccF3Attr> flowPointDTO) {
    super(neId, flowPointDTO);
  }

  @Override
  public EntityIndex getEntityIndex() {
    return flowPointDTO.getValue(FlowPointAccF3Attr.ENTITY_INDEX);
  }

  @Override
  public int getCtagControl() {
    return flowPointDTO.getValue(FlowPointAccF3Attr.CTAG_CONTROL);
  }

  @Override
  public int getStagControl() {
    return flowPointDTO.getValue(FlowPointAccF3Attr.STAG_CONTROL);
  }

  @Override
  public String getPolicerSuffix() {
    return "";
  }
  @Override
  public <Y extends ManagedObjectAttr> Class<Y> getPolicerClass(){
    return (Class<Y>)QOSAccFlowPointPolicerF3Attr.class;
  }

  @Override
  public <Y extends ManagedObjectAttr> Long getCirValue(DTO<Y> dto ){
    return ((DTO<QOSAccFlowPointPolicerF3Attr>)dto).getValue(QOSAccFlowPointPolicerF3Attr.CIR_COMPOSITE);
  }

}
