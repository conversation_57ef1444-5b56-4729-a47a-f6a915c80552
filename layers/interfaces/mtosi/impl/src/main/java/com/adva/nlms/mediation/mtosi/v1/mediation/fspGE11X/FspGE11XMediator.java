/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */
package com.adva.nlms.mediation.mtosi.v1.mediation.fspGE11X;

import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPProperties;
import com.adva.nlms.mediation.config.f3.NetworkElementF3;
import com.adva.nlms.mediation.config.f3.mtosi.NetworkElementF3MTOSIOperations;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.mediation.f3.FspF3Mediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.ManagedElementMediator;
import v1.tmf854.MEVendorExtensionsT;
import v1.tmf854.ObjectFactory;

import jakarta.xml.bind.JAXBElement;
import javax.xml.namespace.QName;

public class FspGE11XMediator extends FspF3Mediator {

  public FspGE11XMediator(NetworkElementF3 ne) {
    super(ne);
    this.ne = ne;
  }
  @Override
  protected JAXBElement<MEVendorExtensionsT> getVendorExtensions() {
    ObjectFactory objFactory = new ObjectFactory();
    MEVendorExtensionsT extensions = new MEVendorExtensionsT();
    populateIpAddress(extensions);
    ManagedElementMediator.populateManagementParameters(ne, extensions);
    //Populate Discover State
    ManagedElementMediator.populateDiscoveryState(ne, extensions);

    return objFactory.createManagedElementTVendorExtensions(extensions);
  }

  protected void populateIpAddress (MEVendorExtensionsT extensions) {
    String ipAddress = ne.getIPAddress();
    JAXBElement<? extends String> je = new JAXBElement(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_IPADDRESS), String.class, ipAddress);
    extensions.getAny().add(je);
    JAXBElement jeMask = new JAXBElement(new
            QName(MtosiConstants.VENDOR_NAMESPACE,MtosiConstants.VENDOR_SUBNET_MASK), String.class, getFSPEgxMTOSIWorker(ne).getInterfaceMask());
    extensions.getAny().add(jeMask);
  }

  @Override
  public String getRelativeNameForProperties(EquipmentSPProperties properties) {
    return null;  //To change body of implemented methods use File | Settings | File Templates.
  }

  @Override
  protected String getPsuType(EquipmentSPProperties properties) {
    return null;  //To change body of implemented methods use File | Settings | File Templates.
  }

  @Override
  protected String getShelfEquipment(EquipmentSPProperties properties) {
    return null;  //To change body of implemented methods use File | Settings | File Templates.
  }

  private static NetworkElementF3MTOSIOperations getFSPEgxMTOSIWorker(NetworkElementF3 ne) {
    return (NetworkElementF3MTOSIOperations)ne.getMTOSIWorker();
  }
}
