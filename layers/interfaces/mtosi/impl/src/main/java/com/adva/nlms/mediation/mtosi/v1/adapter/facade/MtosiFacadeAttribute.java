/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */
package com.adva.nlms.mediation.mtosi.v1.adapter.facade;


@java.lang.annotation.Target({java.lang.annotation.ElementType.FIELD})
@java.lang.annotation.Retention(java.lang.annotation.RetentionPolicy.RUNTIME)
public @interface MtosiFacadeAttribute {
  com.adva.nlms.mediation.mtosi.v1.adapter.facade.LayerAttributes value();
  LayerAttributes[] values() default {};
}