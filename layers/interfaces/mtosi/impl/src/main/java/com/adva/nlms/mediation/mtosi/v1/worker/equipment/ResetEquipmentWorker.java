/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */

package com.adva.nlms.mediation.mtosi.v1.worker.equipment;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.module.MTOSICardModuleF3;
import com.adva.nlms.mediation.config.f3.entity.module.MTOSIEthernetModuleF3;
import com.adva.nlms.mediation.config.f3.entity.module.nemi.MTOSINemiF3;
import com.adva.nlms.mediation.config.fsp150cm.entity.module.scu.MTOSIScuFSP150CM;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.ResetEquipmentResponseT;
import v1.tmf854ext.adva.ResetEquipmentT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

/**
 * main class for the MTOSI operation:  r e s e t E q u i p m e n t
 */
public class ResetEquipmentWorker extends AbstractMtosiWorker {
  Logger LOG = LogManager.getLogger(this.getClass().getName());

  protected ResetEquipmentT mtosiBody;
  protected NamingAttributesT equipmentName;
  protected MtosiAddress mtosiAddr;
  protected ResetEquipmentResponseT response = new ResetEquipmentResponseT();
  protected NetworkElement ne;
  protected MTOSICardModuleF3 card;

  public ResetEquipmentWorker(ResetEquipmentT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "resetEquipment", "resetEquipment", "resetEquipmentResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    equipmentName = mtosiBody.getEquipmentName();
    mtosiAddr = new MtosiAddress(equipmentName);
    
    if (equipmentName == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.getMessageMandatory("equipmentName"));
    }
    if (equipmentName.getMdNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }
    if (equipmentName.getMeNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.ME_NAME_MISSING);
    }
    if (equipmentName.getEhNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.EH_NAME_MISSING);
    }
    if (equipmentName.getEqNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.EQ_NAME_MISSING);
    }
    if (!equipmentName.getEqNm().equals("1")) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.getMessageIllegal("eqNm"));
    }
    ne = ManagedElementFactory.getAndValidateNE(equipmentName);

  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
   validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  @Override
  protected void mediate() throws Exception {
    if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() != NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.ME_NOT_SUPPORTED);
    }

    card = ManagedElementFactory.getModuleCard(mtosiAddr);
    if (card == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The requested entity was not found.");
    }

    if (!(card instanceof MTOSIEthernetModuleF3) && !(card instanceof MTOSINemiF3) && !(card instanceof MTOSIScuFSP150CM)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "Card not supported.");
    }

    transact();
  }

  @Override
  protected void response () throws Exception {
    // empty method
  }

  private void transact()
          throws ObjectInUseException, NetTransactionException, SNMPCommFailure {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "ResetEquipmentWorker");
    try {
      logSecurity(ne, SystemAction.ModifyNetwork, card.getMtosiName());
      card.reset();

      NetTransactionManager.commitNetTransaction(id);
    } catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      NetTransactionManager.ensureEnd(id);
    }
  }

  @Override
  public ResetEquipmentResponseT getSuccessResponse()
  {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
