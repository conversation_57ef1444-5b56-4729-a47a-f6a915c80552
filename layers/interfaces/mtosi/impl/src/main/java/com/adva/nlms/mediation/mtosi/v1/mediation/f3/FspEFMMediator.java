/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.mediation.f3;

import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.FlowF3Attr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import v1.tmf854.ConnectionDirectionT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.SNCStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import v1.tmf854ext.adva.FlowDomainFragmentT;
import ws.v1.tmf854.ProcessingFailureException;

public class FspEFMMediator {
  private final MtosiAddress fdfrMtosiAddress;
  private final MtosiMOFacade facade;
  private DTO<FlowF3Attr> fdfrDTO;


  public FspEFMMediator(MtosiAddress fdfrMtosiAddress, MtosiMOFacade facade, DTO<FlowF3Attr> fdfrDTO) {
    this.fdfrMtosiAddress = fdfrMtosiAddress;
    this.facade = facade;
    this.fdfrDTO = fdfrDTO;
  }

  public FlowDomainFragmentT toMtosiFDFr(NamingAttributesListT aEnd, NamingAttributesListT zEnd) throws ProcessingFailureException {
    final v1.tmf854ext.adva.ObjectFactory objFactoryEx = new v1.tmf854ext.adva.ObjectFactory();
    final ObjectFactory objFactory = new ObjectFactory();
    final FlowDomainFragmentT flowDomainFragmentT = objFactoryEx.createFlowDomainFragmentT();
//    FDFrSPProperties fdfrSPProperties;
//	try {
//    fdfrSPProperties = fdfr.getFDFrSPProperties();

//This is a handy mechanism to identify potential "internal Errors." so I am
//going to leave this commented code here for now.
    //	} catch (Exception e) {
//		NamingAttributesT namingAttributes = new NamingAttributesT();
//		namingAttributes.setMdNm(OSFactory.getMDNm());
//		namingAttributes.setFdfrNm("Exception");
//		flowDomainFragmentT.setName(objFactoryEx.createFlowDomainFragmentTName(namingAttributes));
//	    flowDomainFragmentT.setUserLabel(objFactoryEx.createFlowDomainFragmentTUserLabel(e.toString()+" on fdfr.getFDFrSPProperties()"));
//
//		return flowDomainFragmentT;
//	}

    // Fdfr Name
//    final NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributes(fdfr);
    flowDomainFragmentT.setName(objFactoryEx.createFlowDomainFragmentTName(fdfrMtosiAddress.getNaming()));

    // discoveredName
    final String fdfrNm = fdfrMtosiAddress.getNaming().getFdfrNm();
    if (fdfrNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FDFR_NOT_FOUND);
    }
    flowDomainFragmentT.setDiscoveredName(objFactoryEx.createFlowDomainFragmentTDiscoveredName(fdfrNm));

    // namingOS
    flowDomainFragmentT.setNamingOS(objFactoryEx.createFlowDomainFragmentTNamingOS(OSFactory.getNmsName()));

    // userLabel
    flowDomainFragmentT.setUserLabel(objFactoryEx.createFlowDomainFragmentTUserLabel(fdfrDTO.getValue(FlowF3Attr.ENTITY_ALIAS)));
    //fdfrSPProperties.get(FDFrSPProperties.VS.UserLabel)));

    // source
    final SourceT source = new SourceT();
    source.setValue(SourceEnumT.OS);
    flowDomainFragmentT.setSource(objFactoryEx.createFlowDomainFragmentTSource(source));

    // direction
    flowDomainFragmentT.setDirection(objFactoryEx.createFlowDomainFragmentTDirection(ConnectionDirectionT.CD_BI));

//    createFDFrEndPoints(objFactoryEx, flowDomainFragmentT, fdfrSPProperties);
    // aEnd
    flowDomainFragmentT.setAEnd(objFactoryEx.createFlowDomainFragmentTAEnd(aEnd));

    // zEnd
    flowDomainFragmentT.setZEnd(objFactoryEx.createFlowDomainFragmentTAEnd(zEnd));


        // fdfrState
    flowDomainFragmentT.setFdfrState(objFactoryEx.createFlowDomainFragmentTFdfrState(getFDFrState()));

    // fdfrType - All FDFrs for all devices will have an FDFrType of FDFRT_POINT_TO_POINT
    flowDomainFragmentT.setFdfrType(objFactoryEx.createFlowDomainFragmentTFdfrType("FDFRT_POINT_TO_POINT"));

    // layers
    final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    createLayeredParameters(layeredParametersListT);

    flowDomainFragmentT.setTransmissionParams(objFactoryEx.createFlowDomainFragmentTTransmissionParams(layeredParametersListT));
    return flowDomainFragmentT;
  }

  protected void createLayeredParameters(final LayeredParametersListT layeredParametersListT) {
    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
    // -------end of Layer-------

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET);
    // -------end of Layer-------
//    final boolean isGE20X = fdfr.getNetworkElement().getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE;
//    final boolean isNteModule = isNteModule();
//    if (isGE20X || isNteModule) {
//      // -------start Layer--------
//      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet);
      // -------end of Layer-------
//    }
  }

  protected SNCStateT getFDFrState() {
    return SNCStateT.SNCS_ACTIVE;
  }
}
