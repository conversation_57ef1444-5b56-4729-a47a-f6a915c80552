/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesHN4000;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.hn4000.FlowHN4000;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.RenameTPT;

import jakarta.xml.ws.Holder;

public class RenameTPWorkerHN4000 extends RenameTPWorker
{
	public RenameTPWorkerHN4000(RenameTPT mtosiBody, Holder<HeaderT> mtosiHeader, NamingAttributesT currentName, NetworkElement ne)
	{
		super(mtosiBody, mtosiHeader, currentName, ne);
	}

	@Override
  protected void mediate() throws Exception
	{
		if (NamingTranslationFactory.isFlowHN(currentName))
		{
			mediateFlow();
		} else
		{
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
					"The specified TP Name is for an entity type that cannot be renamed.");
		}
	}

  @Override
  protected void response () throws Exception {
    // empty method
  }

  private void mediateFlow() throws Exception
	{
		FlowHN4000 flow = ManagedElementFactory.getHNFlow(currentName);
		if (flow == null)
		{
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, "The specified TP does not exist.");
		}

		if(NamingTranslationFactory.isFlowEth(currentName))
		{
			if (!currentName.getPtpNm().equals(newName.getPtpNm()))
			{
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, "The existing and new Name reference different PTPs.");
			}
		}
		else if(NamingTranslationFactory.isFlowBonding(currentName))
		{
			if (!currentName.getFtpNm().equals(newName.getFtpNm()))
			{
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, "The existing and new Name reference different FTPs.");
			}
		}
		else if(NamingTranslationFactory.isFlowLag(currentName))
		{
			if (!currentName.getFtpNm().equals(newName.getFtpNm()))
			{
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, "The existing and new Name reference different FTPs.");
			}
		}
		else
		{
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, "The specified TP does not support renaming.");
		}
		
		
		String newTPName = newName.getCtpNm();
		if (newTPName == null || newTPName.length() == 0)
		{
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, "The new TP Name has not been specified.");
		}
		
		FlowSPPropertiesHN4000 oldProps = flow.getFlowSPProperties();
		FlowSPPropertiesHN4000 newProps = new FlowSPPropertiesHN4000();
		newProps.set(FlowSPPropertiesHN4000.VL.DeviceID, oldProps.get(FlowSPPropertiesHN4000.VL.DeviceID));
		newProps.set(FlowSPPropertiesHN4000.VL.EvcID, oldProps.get(FlowSPPropertiesHN4000.VL.EvcID));
		newProps.set(FlowSPPropertiesHN4000.VL.UniID, oldProps.get(FlowSPPropertiesHN4000.VL.UniID));
		newProps.set(FlowSPPropertiesHN4000.VS.Desc, newTPName);
		transactFlow(flow, newProps);
	}

	private void transactFlow(FlowHN4000 flow, FlowSPPropertiesHN4000 flowProps) throws ObjectInUseException, NetTransactionException,
			SPValidationException, SNMPCommFailure
	{
		NetworkElement locks[] = new NetworkElement[]
		{ ne };
		Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "RenameTPWorker");
		try
		{
			logSecurity(ne, SystemAction.ModifyNetwork, flow.getMtosiName());
			flow.setFlowSettings(flowProps);
			NetTransactionManager.commitNetTransaction(id);
      ne.logSROperation(SROperationState.FLOW_RENAME, currentName.getCtpNm(), flowProps.get(FlowSPPropertiesHN4000.VS.Desc));
		}
		catch (NetTransactionException e)
		{
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (SPValidationException e)
		{
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (SNMPCommFailure e)
		{
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		finally
		{
			NetTransactionManager.ensureEnd(id);
		}
	}
}
