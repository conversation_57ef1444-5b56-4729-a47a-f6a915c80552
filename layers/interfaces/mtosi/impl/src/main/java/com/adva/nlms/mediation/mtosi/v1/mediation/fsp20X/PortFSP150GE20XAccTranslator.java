/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp20X;

import v1.tmf854.LayeredParametersListT;
import v1.tmf854.PhysicalTerminationPointT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.common.serviceProvisioning.ACCPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.NETPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.WANPortSPProperties;
import com.adva.nlms.mediation.config.f3.entity.port.acc.MTOSIPortF3Acc;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMPortModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSecondaryStateTranslation;

/**
 * This class is an FSP 150 CM access port MTOSI Translator.
 */
public class PortFSP150GE20XAccTranslator extends PortFSP150GE20XTranslator {
  protected MTOSIPortF3Acc port;
  protected ACCPortSPPropertiesFSP150CM props;

  public PortFSP150GE20XAccTranslator (MTOSIPortF3Acc port) {
    super(port);
    this.port = port;
    props = (ACCPortSPPropertiesFSP150CM) port.getPortSPProperties();
  }

  @Override
  public PhysicalTerminationPointT toMtosiPTP() throws ProcessingFailureException {
    return toMtosiPTP(true);
  }
  @Override
  protected void addPortMode(LayeredParametersListT layeredParametersListT) {
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET,
	            LayeredParams.LrPropAdvaEthernet.PORT_MODE_PARAM,
	            CMPortModeTranslation.getMtosiString(props.get(ACCPortSPPropertiesFSP150CM.VI.Mode), props.get(ACCPortSPPropertiesFSP150CM.VI.SvcType)));
	}

	@Override
	protected String getInterfaceType() {
		return "UNI";
	}

	@Override
	protected String getMaxNumFDFrs() {
		return "1";
	}

	@Override
	protected String getTpRoleState() {
		// TODO Auto-generated method stub
		return (props.get(WANPortSPProperties.VI.IfAdminStatus) == CMAdministrationControlTranslation.UNASSIGNED.getMIBValue() || 
				props.get(NETPortSPPropertiesFSP150CM.VS.SecondaryState).contains(CMSecondaryStateTranslation.UNASSIGNED.getMtosiString())) 
					? "unassigned" : "fdEdge";
	}

}
