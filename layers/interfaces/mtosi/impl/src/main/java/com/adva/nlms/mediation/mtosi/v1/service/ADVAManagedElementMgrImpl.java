/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.service;

import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.me.CreateManagedElementWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.CreateManagementCTPWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.DeleteManagedElementWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.DeleteManagementCTPWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.GetContainedCurrentManagementCTPsWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.GetManagementCTPWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.ProvisionEFMRemoteMEWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.RenameTPWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.SetMEDataWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.SetManagementCTPDataWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.SetTPDataWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.StartDiscoveryPollingWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.UnprovisionEFMRemoteMEWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854ext.adva.CreateCTPT;
import v1.tmf854ext.adva.CreateManagedElementResponseT;
import v1.tmf854ext.adva.CreateManagedElementT;
import v1.tmf854ext.adva.DeleteManagedElementResponseT;
import v1.tmf854ext.adva.DeleteManagedElementT;
import v1.tmf854ext.adva.DeleteManagementCTPResponseT;
import v1.tmf854ext.adva.DeleteManagementCTPT;
import v1.tmf854ext.adva.GetContainedCurrentManagementCTPsResponseT;
import v1.tmf854ext.adva.GetContainedCurrentManagementCTPsT;
import v1.tmf854ext.adva.GetManagementCTPResponseT;
import v1.tmf854ext.adva.GetManagementCTPT;
import v1.tmf854ext.adva.ProvisionEFMRemoteMEResponseT;
import v1.tmf854ext.adva.ProvisionEFMRemoteMET;
import v1.tmf854ext.adva.RenameTPResponseT;
import v1.tmf854ext.adva.RenameTPT;
import v1.tmf854ext.adva.SetMEDataResponseT;
import v1.tmf854ext.adva.SetMEDataT;
import v1.tmf854ext.adva.SetManagementCTPDataResponseT;
import v1.tmf854ext.adva.SetManagementCTPDataT;
import v1.tmf854ext.adva.StartDiscoveryPollingResponseT;
import v1.tmf854ext.adva.StartDiscoveryPollingT;
import v1.tmf854ext.adva.UnprovisionEFMRemoteMEResponseT;
import v1.tmf854ext.adva.UnprovisionEFMRemoteMET;
import ws.v1.tmf854.ProcessingFailureException;
import ws.v1.tmf854ext.adva.ADVAManagedElementMgr;

import jakarta.annotation.Resource;
import jakarta.jws.WebMethod;
import jakarta.jws.WebParam;
import jakarta.jws.WebResult;
import jakarta.jws.soap.SOAPBinding;
import jakarta.xml.ws.Holder;
import jakarta.xml.ws.WebServiceContext;

/**
 * This class was generated by the CXF 2.0-incubator-RC Fri Jun 15 15:08:56 CEST
 * 2007 Generated source version: 2.0-incubator-RC
 */
@jakarta.jws.WebService(name = "ADVAManagedElementMgr", serviceName = "ADVAConfigurationService", portName = "ADVAManagedElementMgrHttp", targetNamespace = "adva.tmf854ext.v1.ws", endpointInterface = "ws.v1.tmf854ext.adva.ADVAManagedElementMgr")
public class ADVAManagedElementMgrImpl implements ADVAManagedElementMgr {
  private static final Logger LOG = LogManager.getLogger(ADVAManagedElementMgrImpl.class.getPackage().getName());
  // Inject the context
  @Resource
  private WebServiceContext context;

  @Override
     @SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
     @WebResult(targetNamespace = "adva.tmf854ext.v1", partName = "mtosiBody", name = "renameTPResponse")
     @WebMethod(action = "renameTP")
     public RenameTPResponseT renameTP(
            @WebParam(targetNamespace = "tmf854.v1", partName = "mtosiHeader", name = "header", header = true, mode = WebParam.Mode.INOUT)
            Holder<HeaderT> mtosiHeader,
            @WebParam(targetNamespace = "adva.tmf854ext.v1", partName = "mtosiBody", name = "renameTP")
            RenameTPT mtosiBody) throws ProcessingFailureException {
        return ServiceUtils.runMethod(RenameTPWorker.class, mtosiBody, mtosiHeader,
                "renameTP", context, LOG);
    }

  /* (non-Javadoc)
  * @see ws.v1.tmf854ext.adva.ADVAManagedElementMgr#createManagementCTP(v1.tmf854.HeaderT  mtosiHeader ,)v1.tmf854ext.adva.CreateCTPT  mtosiBody )*
  */
  @Override
  public v1.tmf854ext.adva.CreateManagementCTPResponseT createManagementCTP(Holder<HeaderT> mtosiHeader,CreateCTPT mtosiBody) throws ProcessingFailureException    {
    //LOG.info("Executing operation createManagementCTP");

    return ServiceUtils.runMethod(CreateManagementCTPWorker.class, mtosiBody, mtosiHeader,
            "createManagementCTP", context, LOG);
  }
  /*
    * (non-Javadoc)
    *
    * @see ws.v1.tmf854ext.adva.ADVAManagedElementMgr#setTPData(v1.tmf854.HeaderT
    *      mtosiHeader ,)v1.tmf854ext.adva.SetTPDataT mtosiBody )*
    */
  @Override
  public v1.tmf854ext.adva.SetTPDataResponseT setTPData(Holder<HeaderT> mtosiHeader,
                                                        v1.tmf854ext.adva.SetTPDataT mtosiBody) throws ProcessingFailureException {
    return ServiceUtils.runMethod(SetTPDataWorker.class, mtosiBody, mtosiHeader,
        "setTPData", context, LOG);
  }

  @Override
    @SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
    @WebResult(targetNamespace = "adva.tmf854ext.v1", partName = "mtosiBody", name = "setMEDataResponse")
    @WebMethod(action = "setMEData")
    public SetMEDataResponseT setMEData(
            @WebParam(targetNamespace = "tmf854.v1", partName = "mtosiHeader", name = "header", header = true, mode = WebParam.Mode.INOUT)
            Holder<HeaderT> mtosiHeader,
            @WebParam(targetNamespace = "adva.tmf854ext.v1", partName = "mtosiBody", name = "setMEData")
            SetMEDataT mtosiBody) throws ProcessingFailureException {
        return ServiceUtils.runMethod(SetMEDataWorker.class, mtosiBody, mtosiHeader,
                "setMEData", context, LOG);
    }

  @Override
  public CreateManagedElementResponseT createManagedElement(
      Holder<HeaderT> mtosiHeader, CreateManagedElementT mtosiBody)
      throws ProcessingFailureException {
    return ServiceUtils.runMethod(CreateManagedElementWorker.class, mtosiBody, mtosiHeader,
        "createManagedElement", context, LOG);
  }

  @Override
  public StartDiscoveryPollingResponseT startDiscoveryPolling(Holder<HeaderT> mtosiHeader, StartDiscoveryPollingT mtosiBody) throws ProcessingFailureException {
    return ServiceUtils.runMethod(StartDiscoveryPollingWorker.class, mtosiBody, mtosiHeader,
        "startDiscoveryPolling", context, LOG);
  }

  @Override
  public DeleteManagedElementResponseT deleteManagedElement(
      Holder<HeaderT> mtosiHeader, DeleteManagedElementT mtosiBody)
      throws ProcessingFailureException {
    return ServiceUtils.runMethod(DeleteManagedElementWorker.class, mtosiBody, mtosiHeader,
        "deleteManagedElement", context, LOG);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854ext.adva.ADVAManagedElementMgr#getContainedCurrentManagementCTPs(v1.tmf854.HeaderT  mtosiHeader ,)v1.tmf854ext.adva.GetContainedCurrentManagementCTPsT  mtosiBody )*
  */
  public GetContainedCurrentManagementCTPsResponseT getContainedCurrentManagementCTPs(Holder<HeaderT> mtosiHeader,
                         GetContainedCurrentManagementCTPsT mtosiBody) throws ProcessingFailureException    {
    return ServiceUtils.runMethod(GetContainedCurrentManagementCTPsWorker.class, mtosiBody, mtosiHeader,
            "getContainedCurrentManagementCTPs", context, LOG);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854ext.adva.ADVAManagedElementMgr#setManagementCTPData(v1.tmf854.HeaderT  mtosiHeader ,)v1.tmf854ext.adva.SetManagementCTPDataT  mtosiBody )*
  */
  public SetManagementCTPDataResponseT setManagementCTPData(Holder<HeaderT> mtosiHeader,SetManagementCTPDataT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException    {
    return ServiceUtils.runMethod(SetManagementCTPDataWorker.class, mtosiBody, mtosiHeader,
            "setManagementCTPData", context, LOG);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854ext.adva.ADVAManagedElementMgr#getManagementCTP(v1.tmf854.HeaderT  mtosiHeader ,)v1.tmf854ext.adva.GetManagementCTPT  mtosiBody )*
  */
  public GetManagementCTPResponseT getManagementCTP(Holder<HeaderT> mtosiHeader,GetManagementCTPT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException    {
    return ServiceUtils.runMethod(GetManagementCTPWorker.class, mtosiBody, mtosiHeader,
           "getManagementCTP", context, LOG);
  }

  /* (non-Javadoc)
  * @see ws.v1.tmf854ext.adva.ADVAManagedElementMgr#deleteManagementCTP(v1.tmf854.HeaderT  mtosiHeader ,)v1.tmf854ext.adva.DeleteManagementCTPT  mtosiBody )*
  */
  public DeleteManagementCTPResponseT deleteManagementCTP(Holder<HeaderT> mtosiHeader,DeleteManagementCTPT mtosiBody) throws ws.v1.tmf854.ProcessingFailureException    {
    return ServiceUtils.runMethod(DeleteManagementCTPWorker.class, mtosiBody, mtosiHeader,
            "deleteManagementCTP", context, LOG);
    //throw new ws.v1.tmf854.ProcessingFailureException("ProcessingFailureException...");
  }

  /* (non-Javadoc)
   * @see ws.v1.tmf854ext.adva.ADVAManagedElementMgr#provisionEFMRemoteME(v1.tmf854.HeaderT  mtosiHeader ,)v1.tmf854ext.adva.ProvisionEFMRemoteMET  mtosiBody )*
   */
  @Override
  public ProvisionEFMRemoteMEResponseT provisionEFMRemoteME(Holder<HeaderT> mtosiHeader, ProvisionEFMRemoteMET mtosiBody) throws ProcessingFailureException {
    return ServiceUtils.runMethod(ProvisionEFMRemoteMEWorker.class, mtosiBody, mtosiHeader,
        "provisionEFMRemoteME", context, LOG);
  }

  /* (non-Javadoc)
   * @see ws.v1.tmf854ext.adva.ADVAManagedElementMgr#unprovisionEFMRemoteME(v1.tmf854.HeaderT  mtosiHeader ,)v1.tmf854ext.adva.UnprovisionEFMRemoteMET  mtosiBody )*
   */
  @Override
  public UnprovisionEFMRemoteMEResponseT unprovisionEFMRemoteME(Holder<HeaderT> mtosiHeader, UnprovisionEFMRemoteMET mtosiBody) throws ProcessingFailureException {
    return ServiceUtils.runMethod(UnprovisionEFMRemoteMEWorker.class, mtosiBody, mtosiHeader,
        "unprovisionEFMRemoteME", context, LOG);
  }
}
