/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */
package com.adva.nlms.mediation.mtosi.v1.mediation;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import v1.tmf854.HeaderT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.lang.reflect.Constructor;

public class MtosiTranslatorFacade {

  private final NetworkElement ne;
  private final Holder<HeaderT> mtosiHeader;
  private final String message;
  public MtosiTranslatorFacade(NetworkElement ne, Holder<HeaderT> mtosiHeader, String message){
    this.ne = ne;
    this.mtosiHeader = mtosiHeader;
    this.message = message;
  }

  public MtosiTranslator getMtosiTranslator(Object instance) throws ProcessingFailureException{
     for(MtosiTranslatorMap map : MtosiTranslatorMap.values()){
       if(map.getEntityClass().equals(instance.getClass())) {
         return instantiateTranslator(map, instance);
       }
     }
    if(message == null) return null;
    if(mtosiHeader == null) throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, message);
    throw ServiceUtils.createNewPFE( mtosiHeader,ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, message);
  }

  private MtosiTranslator instantiateTranslator(MtosiTranslatorMap map, Object instance) {
    try {
      Constructor<MtosiTranslator> ctor;
      if(map.isNeedsMtosiWorker() && ne != null) {
         ctor = map.getTranslatorClass().getConstructor(map.getClasses()[0], map.getClasses()[1]);
         return ctor.newInstance(instance, ne.getMTOSIWorker());
      }else {
        ctor = map.getTranslatorClass().getConstructor(map.getClasses()[0]);
        return ctor.newInstance(instance);
      }
   }catch(Exception ex){
      return null;
    }
  }

  public NetworkElement getNe() {
    return ne;
  }
}
