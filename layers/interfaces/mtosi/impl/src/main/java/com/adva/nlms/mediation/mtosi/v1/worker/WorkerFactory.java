/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.flow.FlowF3Impl;
import com.adva.nlms.mediation.config.mtosi.FDFr;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.equipment.SetEquipmentDataWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.equipment.SetEquipmentDataWorkerCM;
import com.adva.nlms.mediation.mtosi.v1.worker.equipment.SetEquipmentDataWorkerCM_CPMR;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.CreateAndActivateFDFrWorkerCM;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.CreateAndActivateFDFrWorkerCP;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.CreateAndActivateFDFrWorkerEFM;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.CreateAndActivateFDFrWorkerGE20X;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.CreateAndActivateFDFrWorkerHN4000;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.CreateFTPWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.CreateFTPWorkerCM;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.CreateFTPWorkerEFM;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.CreateFTPWorkerHN4000;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.DeactivateAndDeleteFDFrWorkerCM;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.DeactivateAndDeleteFDFrWorkerCP;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.DeactivateAndDeleteFDFrWorkerEFM;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.DeactivateAndDeleteFDFrWorkerHN4000;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.DeleteCTPWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.DeleteCTPWorkerCM;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.DeleteFTPWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.DeleteFTPWorkerEFM;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.DeleteFTPWorkerHN;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.GetContinuityTestWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.GetContinuityTestWorkerCM;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.GetContinuityTestWorkerCP;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.GetContinuityTestWorkerHN400;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.GetContinuityTestWorkerHN4000;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.ModifyFDFrWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.ModifyFDFrWorkerCM;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.ModifyFDFrWorkerCP;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.ModifyFDFrWorkerGE20X;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.ModifyFDFrWorkerHN4000;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.OperateLoopbackWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.OperateLoopbackWorkerCM;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.OperateLoopbackWorkerCP;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.OperateLoopbackWorkerHN;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.PerformProtectionCommandWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.PerformProtectionCommandWorkerEFM;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.ReleaseLoopbackWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.ReleaseLoopbackWorkerCM;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.ReleaseLoopbackWorkerCP;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.ReleaseLoopbackWorkerHN;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.RenameFDFrWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.RenameFDFrWorkerCM;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.RenameFDFrWorkerCP;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.RenameFDFrWorkerHN;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.StartContinuityTestWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.StartContinuityTestWorkerCM;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.StartContinuityTestWorkerCP;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.StartContinuityTestWorkerHN400;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.StartContinuityTestWorkerHN4000;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.StopContinuityTestWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.StopContinuityTestWorkerCM;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.StopContinuityTestWorkerCP;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.StopContinuityTestWorkerHN400;
import com.adva.nlms.mediation.mtosi.v1.worker.flowDomain.StopContinuityTestWorkerHN4000;
import com.adva.nlms.mediation.mtosi.v1.worker.me.GetContainedCurrentCTPsWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.GetContainedCurrentCTPsWorkerEFM;
import com.adva.nlms.mediation.mtosi.v1.worker.me.GetTPWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.GetTPWorkerCM;
import com.adva.nlms.mediation.mtosi.v1.worker.me.GetTPWorkerHN;
import com.adva.nlms.mediation.mtosi.v1.worker.me.RenameTPWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.RenameTPWorkerCM;
import com.adva.nlms.mediation.mtosi.v1.worker.me.RenameTPWorkerCP;
import com.adva.nlms.mediation.mtosi.v1.worker.me.RenameTPWorkerHN4000;
import com.adva.nlms.mediation.mtosi.v1.worker.me.SetCTPDataWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.SetCTPDataWorkerHN;
import com.adva.nlms.mediation.mtosi.v1.worker.me.SetCTPLagDataWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.SetFTPDataWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.SetFTPDataWorkerHN;
import com.adva.nlms.mediation.mtosi.v1.worker.me.SetPTPDataWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.me.SetPTPDataWorkerHN;
import com.adva.nlms.mediation.mtosi.v1.worker.me.SetTPDataWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.tcProfile.ModifyTCProfileWorker;
import com.adva.nlms.mediation.mtosi.v1.worker.tcProfile.ModifyTCProfileWorkerCM;
import com.adva.nlms.mediation.mtosi.v1.worker.tcProfile.ModifyTCProfileWorkerHN;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.GetContainedCurrentCTPsT;
import v1.tmf854.GetTPT;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.TPDataT;
import v1.tmf854ext.adva.CreateAndActivateFDFrT;
import v1.tmf854ext.adva.CreateFTPT;
import v1.tmf854ext.adva.DeactivateAndDeleteFDFrT;
import v1.tmf854ext.adva.DeleteCTPT;
import v1.tmf854ext.adva.DeleteFTPT;
import v1.tmf854ext.adva.EQDataT;
import v1.tmf854ext.adva.FTPCreateDataT;
import v1.tmf854ext.adva.GetContinuityTestT;
import v1.tmf854ext.adva.ModifyFDFrT;
import v1.tmf854ext.adva.ModifyTCProfileT;
import v1.tmf854ext.adva.OperateLoopbackT;
import v1.tmf854ext.adva.PerformProtectionCommandT;
import v1.tmf854ext.adva.ReleaseLoopbackT;
import v1.tmf854ext.adva.RenameFDFrT;
import v1.tmf854ext.adva.RenameTPT;
import v1.tmf854ext.adva.SetEquipmentDataT;
import v1.tmf854ext.adva.SetTPDataT;
import v1.tmf854ext.adva.StartContinuityTestT;
import v1.tmf854ext.adva.StopContinuityTestT;
import v1.tmf854ext.adva.TCProfileCreateDataT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * Worker Factory
 */
public class WorkerFactory
{
  private static final Logger LOG = LogManager.getLogger(WorkerFactory.class.getPackage().getName());

  public static <W extends AbstractMtosiWorker, T> W createWorker (Class<W> theClass, T mtosiBody, Holder<HeaderT> mtosiHeader) throws ProcessingFailureException {
    try {
      Method method = WorkerFactory.class.getMethod("create"+theClass.getSimpleName(), mtosiBody.getClass(), mtosiHeader.getClass());
      //noinspection unchecked
      return (W) method.invoke(null, mtosiBody, mtosiHeader);
    } catch (NoSuchMethodException e) {
      try {
        return theClass.getConstructor(mtosiBody.getClass(), mtosiHeader.getClass()).newInstance(mtosiBody, mtosiHeader);
      } catch (NoSuchMethodException e1) {
        LOG.error("Not supported Worker: " + theClass.getName(), e);
        LOG.error("Worker with unknown constructor: " + theClass.getName(), e1);
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, "Invalid input.", e1);
      } catch (InvocationTargetException e1) {
        LOG.error("Unknown Worker: " + theClass.getName(), e1);
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, "Invalid input.", e1);
      } catch (IllegalAccessException e1) {
        LOG.error("Unknown Worker: " + theClass.getName(), e1);
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, "Invalid input.", e1);
      } catch (InstantiationException e1) {
        LOG.error("Unknown Worker: " + theClass.getName(), e1);
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, "Invalid input.", e1);
      }
    } catch (InvocationTargetException e) {
      if (e.getCause() instanceof ProcessingFailureException) {
        LOG.info("Exception during worker creation: " + e.getCause());
        throw (ProcessingFailureException) e.getCause();
      }
      LOG.error("Not supported Worker: " + theClass.getName(), e);
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, "Invalid input.", e);
    } catch (IllegalAccessException e) {
      LOG.error("Not supported Worker: " + theClass.getName(), e);
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, "Invalid input.", e);
    }
  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static SetTPDataWorker createSetTPDataWorker(SetTPDataT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException
  {
    // TODO: refactor this method by using ne type!
      TPDataT tpInfo = mtosiBody.getTpInfo();
      if (tpInfo == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The TP info has not been specified.");
      }

      NamingAttributesT namingAttributes = tpInfo.getTpName();
      if (namingAttributes == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The TP name has not been specified.");
      }

      MtosiUtils.validateMD(namingAttributes);
      NetworkElement ne = ManagedElementFactory.getAndValidateNE(namingAttributes);

      if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400
              || ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000) {
        if (NamingTranslationFactory.isFtp(namingAttributes)) {
          return new SetFTPDataWorkerHN(mtosiHeader, tpInfo, namingAttributes, ne);
        } else if (NamingTranslationFactory.isFlow(namingAttributes)) {
          return new SetCTPDataWorkerHN(mtosiHeader, tpInfo, namingAttributes, ne);
        } else if (NamingTranslationFactory.isCtpLag(namingAttributes)) {
          return new SetCTPDataWorkerHN(mtosiHeader, tpInfo, namingAttributes, ne);
        } else if (namingAttributes.getPtpNm() != null) {
          return new SetPTPDataWorkerHN(mtosiHeader, tpInfo, namingAttributes, ne);
        }

      } else {
        if (NamingTranslationFactory.isFtp(namingAttributes)) {
          return new SetFTPDataWorker(mtosiHeader, tpInfo, namingAttributes, ne);
        } else if (NamingTranslationFactory.isFlow(namingAttributes)) {
          return new SetCTPDataWorker(mtosiHeader, tpInfo, namingAttributes, ne);
        } else if (NamingTranslationFactory.isCtpLag(namingAttributes)) {
          return new SetCTPLagDataWorker(mtosiHeader, tpInfo, namingAttributes, ne);
        } else if (namingAttributes.getPtpNm() != null) {
          return new SetPTPDataWorker(mtosiHeader, tpInfo, namingAttributes, ne);
        }
      }
      throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
              "Invalid input for setTPData.");
  }

  @SuppressWarnings({"unchecked"})
  public static CreateAndActivateFDFrWorker createCreateAndActivateFDFrWorker(CreateAndActivateFDFrT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException
  {
    NamingAttributesListT aEnd = mtosiBody.getAEnd();
    if(aEnd==null || aEnd.getName()==null || aEnd.getName().isEmpty()) {
      throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
              "The aEnd has not been specified.");
    }

    NamingAttributesListT zEnd = mtosiBody.getZEnd();
    if(zEnd==null || zEnd.getName()==null || zEnd.getName().isEmpty()) {
      throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
              "The zEnd has not been specified.");
    }
    //Additional validation to ensure the createData is not null.
    if (mtosiBody.getCreateData() == null) {
      throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
              "The createData has not been specified.");
    }
    NamingAttributesT fdfrName = mtosiBody.getCreateData().getName();

    //Validation added with Defect # 883.
    //Ensure all the MD names are set correctly.  Per: Defect # 889
    if (mtosiBody.getTpsToModify() != null && mtosiBody.getTpsToModify().getTpData() != null
            && mtosiBody.getTpsToModify().getTpData().size() > 0 ) {
      NamingAttributesT[] namings = new NamingAttributesT[mtosiBody.getTpsToModify().getTpData().size()+3];
      namings[0] = fdfrName;
      namings[1] = aEnd.getName().get(0);
      namings[2] = zEnd.getName().get(0);
      for (int i = 3,index=0; i < namings.length;i++,index++) {
        namings[i] = mtosiBody.getTpsToModify().getTpData().get(index).getTpName();
      }
      MtosiUtils.validateMD(namings);
    } else {
      //We have no tpsToModify...I am not sure if this is valid, but it was not checked here before, so I will leave it.
      //Just check the three naming attrs.
      MtosiUtils.validateMD(fdfrName,aEnd.getName().get(0),zEnd.getName().get(0));
    }

    NetworkElement ne = ManagedElementFactory.getAndValidateNE(aEnd.getName().get(0));

    switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
        return new CreateAndActivateFDFrWorkerCP(mtosiBody, mtosiHeader, ne, aEnd, zEnd);
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
          return new CreateAndActivateFDFrWorkerGE20X(mtosiBody, mtosiHeader, ne, aEnd, zEnd);
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
      case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
        return new CreateAndActivateFDFrWorkerCM(mtosiBody, mtosiHeader, ne, aEnd, zEnd);
      case NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
        return new CreateAndActivateFDFrWorkerEFM(mtosiBody, mtosiHeader, ne, aEnd, zEnd);
      case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
          return new CreateAndActivateFDFrWorkerHN4000(mtosiBody, mtosiHeader, ne, aEnd, zEnd);
      case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
          return new CreateAndActivateFDFrWorkerHN4000(mtosiBody, mtosiHeader, ne, aEnd, zEnd);
      default:
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.ME_NOT_SUPPORTED);
    }
  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static DeactivateAndDeleteFDFrWorker createDeactivateAndDeleteFDFrWorker(DeactivateAndDeleteFDFrT mtosiBody, Holder mtosiHeader)
          throws ProcessingFailureException
  {

      NamingAttributesT fdfrNaming = mtosiBody.getFdfrName();

      if(fdfrNaming==null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
                "FDFr name is missing.");
      }

      //Ensure the MD name is set correctly.  Per: Defect # 883
      MtosiUtils.validateMD(fdfrNaming);

      final String fdfrNm = fdfrNaming.getFdfrNm();
      if(fdfrNm==null || fdfrNm.length() == 0) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
                "FDFr name is missing or empty.");
      }

    //try to find fdfr for CM-GE102ProH - new implementation
    final FlowF3Impl fdFrByName = ManagedElementFactory.getFDFrByName(fdfrNm);

    //try to find fdfr for CM-CPMR - old implementation
    final FDFr fdfr = ManagedElementFactory.getFDFr(fdfrNm);
      if(fdfr==null && fdFrByName==null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
                "The specified FDFr does not exist.");
      }

    NetworkElement ne;
    if (fdfr!=null) {
      ne = fdfr.getNetworkElement();
    }else{
      ne = fdFrByName.getNetworkElement();
    }

      MtosiUtils.validateNE(ne);

    // catch the scenario wher and FDFr has been created from Mtosi v2.
    // FDFr entity in Mtosi v1 is different than Mtosi v2.
    // For tha reason we add the following validation.
    if(fdfr==null && fdFrByName!=null && ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() != NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM) {
      throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          "The specified FDFr does not exist.");
    }

      switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
          return new DeactivateAndDeleteFDFrWorkerCP(mtosiBody, mtosiHeader, ne, fdfr);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
          return new DeactivateAndDeleteFDFrWorkerCM(mtosiBody, mtosiHeader, ne, fdfr);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
            return new DeactivateAndDeleteFDFrWorkerHN4000(mtosiBody, mtosiHeader, ne, fdfr);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
            return new DeactivateAndDeleteFDFrWorkerHN4000(mtosiBody, mtosiHeader, ne, fdfr);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
          return new DeactivateAndDeleteFDFrWorkerEFM(mtosiBody, mtosiHeader, ne);
        default:
          throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.ME_NOT_SUPPORTED);
      }

  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static RenameFDFrWorker createRenameFDFrWorker(RenameFDFrT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException
  {
      NamingAttributesT newName = mtosiBody.getNewFDFrName();
      NamingAttributesT currentName = mtosiBody.getFdfrName();

      if (currentName == null || currentName.getFdfrNm() == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The current FDFr Name has not been specified.");
      }

      final FDFr fdfr = ManagedElementFactory.getFDFr(currentName.getFdfrNm());
      if (fdfr == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
                "The specified FDFr does not exist.");
      }

      if (newName == null || newName.getFdfrNm() == null || newName.getFdfrNm().length() == 0) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The new FDFr Name has not been specified.");
      }

      //Ensure the MD names are set correctly.  
      MtosiUtils.validateMD(currentName,newName);

      if (ManagedElementFactory.getFDFr(newName) != null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "An FDFr already exists with the specified name.");
      }

      NetworkElement ne = fdfr.getNetworkElement();
      MtosiUtils.validateNE(ne);

      switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
          return new RenameFDFrWorkerCP(mtosiHeader, ne, fdfr, newName.getFdfrNm());
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
          return new RenameFDFrWorkerCM(mtosiHeader, ne, fdfr, newName.getFdfrNm());
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
          return new RenameFDFrWorkerHN(mtosiHeader, ne, fdfr, newName.getFdfrNm());
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
          return new RenameFDFrWorkerHN(mtosiHeader, ne, fdfr, newName.getFdfrNm());
        default:
          throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.ME_NOT_SUPPORTED);
      }

  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static DeleteCTPWorker createDeleteCTPWorker(DeleteCTPT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException
  {

      NamingAttributesT namingAttributes = mtosiBody.getCtpName();
      if (namingAttributes == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The CTP name has not been specified.");
      }

      //Ensure the MD name is set correctly.  
      MtosiUtils.validateMD(namingAttributes);

      NetworkElement ne = ManagedElementFactory.getAndValidateNE(namingAttributes);

      switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
          return new DeleteCTPWorkerCM(mtosiHeader, namingAttributes, ne);
        default:
          throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.ME_NOT_SUPPORTED);
      }

  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static DeleteFTPWorker createDeleteFTPWorker (DeleteFTPT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException
  {

      NamingAttributesT namingAttributes = mtosiBody.getFtpName();
      if (namingAttributes == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The FTP name has not been specified.");
      }

      //Ensure the MD name is set correctly.  
      MtosiUtils.validateMD(namingAttributes);

      NetworkElement ne = ManagedElementFactory.getAndValidateNE(namingAttributes);

      switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
        	return new DeleteFTPWorker(mtosiHeader, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
          return new DeleteFTPWorkerEFM(mtosiHeader, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
          return new DeleteFTPWorkerHN(mtosiHeader, mtosiBody, namingAttributes, ne);
        default:
          throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.ME_NOT_SUPPORTED);
      }

  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static ReleaseLoopbackWorker createReleaseLoopbackWorker (ReleaseLoopbackT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException
  {

      NamingAttributesT namingAttributes = mtosiBody.getTpName();
      if (namingAttributes == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The TP name has not been specified.");
      }

      //Ensure the MD name is set correctly.  
      MtosiUtils.validateMD(namingAttributes);

      NetworkElement ne = ManagedElementFactory.getAndValidateNE(namingAttributes);

      switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
          return new ReleaseLoopbackWorkerCM(mtosiHeader, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
          return new ReleaseLoopbackWorkerCP(mtosiHeader, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
          return new ReleaseLoopbackWorkerHN(mtosiHeader, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
            return new ReleaseLoopbackWorkerHN(mtosiHeader, namingAttributes, ne);
        default:
          throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.ME_NOT_SUPPORTED);
      }

  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static StopContinuityTestWorker createStopContinuityTestWorker (StopContinuityTestT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException
  {

      NamingAttributesT namingAttributes = mtosiBody.getTpName();
      if (namingAttributes == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The TP name has not been specified.");
      }

      //Ensure the MD name is set correctly.  
      MtosiUtils.validateMD(namingAttributes);

      NetworkElement ne = ManagedElementFactory.getAndValidateNE(namingAttributes);

      switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
          return new StopContinuityTestWorkerCM(mtosiHeader, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
          return new StopContinuityTestWorkerCP(mtosiHeader, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
            return new StopContinuityTestWorkerHN4000(mtosiHeader, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
          return new StopContinuityTestWorkerHN400(mtosiHeader, namingAttributes, ne);
        default:
          throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.ME_NOT_SUPPORTED);
      }

  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static CreateFTPWorker createCreateFTPWorker (CreateFTPT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException
  {

      FTPCreateDataT ftpCreateData = mtosiBody.getCreateData();
      if (ftpCreateData == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The createData has not been specified.");
      }

      NamingAttributesT namingAttributes = ftpCreateData.getName();
      if (namingAttributes == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The FTP name has not been specified.");
      }

      //Ensure the MD name is set correctly.  
      MtosiUtils.validateMD(namingAttributes);

      NetworkElement ne = ManagedElementFactory.getAndValidateNE(namingAttributes);

      switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
          return new CreateFTPWorkerCM(mtosiHeader, ftpCreateData, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
          return new CreateFTPWorkerEFM(mtosiHeader, ftpCreateData, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
          return new CreateFTPWorkerHN4000(mtosiHeader, ftpCreateData, namingAttributes, ne);
        default:
          throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.ME_NOT_SUPPORTED);
      }

  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static OperateLoopbackWorker createOperateLoopbackWorker (OperateLoopbackT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException
  {

      NamingAttributesT namingAttributes = mtosiBody.getTpName();
      if (namingAttributes == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The TP name has not been specified.");
      }

      //Ensure the MD name is set correctly.  
      MtosiUtils.validateMD(namingAttributes);

      NetworkElement ne = ManagedElementFactory.getAndValidateNE(namingAttributes);

      switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
          return new OperateLoopbackWorkerCM(mtosiBody, mtosiHeader, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
          return new OperateLoopbackWorkerCP(mtosiBody, mtosiHeader, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
          return new OperateLoopbackWorkerHN(mtosiBody, mtosiHeader, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
            return new OperateLoopbackWorkerHN(mtosiBody, mtosiHeader, namingAttributes, ne);
        default:
          throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.ME_NOT_SUPPORTED);
      }

  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static ModifyFDFrWorker createModifyFDFrWorker (ModifyFDFrT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException
  {

      NamingAttributesT namingAttributes = mtosiBody.getFdfrName();
      if (namingAttributes == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The FDFr name has not been specified.");
      }

      //Ensure the MD name is set correctly.  
      MtosiUtils.validateMD(namingAttributes);

      FDFr fdfr = ManagedElementFactory.getFDFr(namingAttributes.getFdfrNm());
      if (fdfr == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
                "The specified FDFr does not exist.");
      }

      NetworkElement ne = fdfr.getNetworkElement();
      MtosiUtils.validateNE(ne);

      switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
            return new ModifyFDFrWorkerCM(mtosiBody, mtosiHeader, namingAttributes, fdfr, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
          return new ModifyFDFrWorkerGE20X(mtosiBody, mtosiHeader, namingAttributes, fdfr, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
          return new ModifyFDFrWorkerCP(mtosiBody, mtosiHeader, namingAttributes, fdfr, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
        	return new ModifyFDFrWorkerHN4000(mtosiBody, mtosiHeader, namingAttributes, fdfr, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
        	return new ModifyFDFrWorkerHN4000(mtosiBody, mtosiHeader, namingAttributes, fdfr, ne);
        default:
          throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.ME_NOT_SUPPORTED);
      }

  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static GetContinuityTestWorker createGetContinuityTestWorker (GetContinuityTestT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException {

      NamingAttributesT namingAttributes = mtosiBody.getTpName();
      if (namingAttributes == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The TP name has not been specified.");
      }

      //Ensure the MD name is set correctly.  
      MtosiUtils.validateMD(namingAttributes);

      NetworkElement ne = ManagedElementFactory.getAndValidateNE(namingAttributes);

      switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
            return new GetContinuityTestWorkerCM(mtosiHeader, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
          return new GetContinuityTestWorkerCP(mtosiHeader, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
            return new GetContinuityTestWorkerHN4000(mtosiHeader, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
          return new GetContinuityTestWorkerHN400(mtosiHeader, namingAttributes, ne);
        default:
          throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.ME_NOT_SUPPORTED);
      }

  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static StartContinuityTestWorker createStartContinuityTestWorker (StartContinuityTestT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException {

      NamingAttributesT namingAttributes = mtosiBody.getTpName();
      if (namingAttributes == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The TP name has not been specified.");
      }

      //Ensure the MD name is set correctly.  
      MtosiUtils.validateMD(namingAttributes);

      NetworkElement ne = ManagedElementFactory.getAndValidateNE(namingAttributes);

      switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
          return new StartContinuityTestWorkerCM(mtosiBody, mtosiHeader, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
          return new StartContinuityTestWorkerCP(mtosiBody, mtosiHeader, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
            return new StartContinuityTestWorkerHN4000(mtosiBody, mtosiHeader, namingAttributes, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
            return new StartContinuityTestWorkerHN400(mtosiBody, mtosiHeader, namingAttributes, ne);
        default:
          throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.ME_NOT_SUPPORTED);
      }

  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static SetEquipmentDataWorker createSetEquipmentDataWorker (SetEquipmentDataT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException {

      EQDataT equipmentData = mtosiBody.getEqInfo();
      if (equipmentData == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The eqInfo has not been specified.");
      }

      NamingAttributesT equipmentName = equipmentData.getEquipmentName();
      if (equipmentName == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The equipmentName has not been specified.");
      }

      NetworkElement ne = ManagedElementFactory.getAndValidateNE(equipmentName);

      switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
          return new SetEquipmentDataWorkerCM(mtosiHeader, equipmentData, equipmentName, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
          return new SetEquipmentDataWorkerCM_CPMR(mtosiHeader, equipmentData, equipmentName, ne);
        default:
          throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.ME_NOT_SUPPORTED);
      }

  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static GetContainedCurrentCTPsWorker createGetContainedCurrentCTPsWorker (GetContainedCurrentCTPsT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException {

    NetworkElement ne = ManagedElementFactory.getAndValidateNE(mtosiBody.getTpName());

    if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM){
      return new GetContainedCurrentCTPsWorkerEFM(mtosiBody, mtosiHeader);
    }else{
      return new GetContainedCurrentCTPsWorker(mtosiBody, mtosiHeader);
    }

  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static PerformProtectionCommandWorker createPerformProtectionCommandWorker (PerformProtectionCommandT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException {

    NetworkElement ne = ManagedElementFactory.getAndValidateNE(mtosiBody.getTpName());

    if (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM){
      return new PerformProtectionCommandWorkerEFM(mtosiBody, mtosiHeader);
    }else{
      return new PerformProtectionCommandWorker(mtosiBody, mtosiHeader);
    }

  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static RenameTPWorker createRenameTPWorker (RenameTPT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException {

      NamingAttributesT currentName = mtosiBody.getTpName();
      if (currentName==null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The current TP Name has not been specified.");
      }

      NamingAttributesT newName = mtosiBody.getNewTPName();
      if (newName==null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The new TP Name has not been specified.");
      }

      //Ensure the MD names are set correctly.  
      MtosiUtils.validateMD(currentName, newName);

      NetworkElement ne = ManagedElementFactory.getAndValidateNE(currentName);

      switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
          return new RenameTPWorkerCM(mtosiBody, mtosiHeader, currentName, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
          return new RenameTPWorkerCP(mtosiBody, mtosiHeader, currentName, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
            return new RenameTPWorkerHN4000(mtosiBody, mtosiHeader, currentName, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
            return new RenameTPWorkerHN4000(mtosiBody, mtosiHeader, currentName, ne);
        default:
          throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.ME_NOT_SUPPORTED);
      }

  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static ModifyTCProfileWorker createModifyTCProfileWorker (ModifyTCProfileT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException {

      TCProfileCreateDataT tcProfileCreateDataT = mtosiBody.getTcProfileModifyData();
      if (tcProfileCreateDataT == null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The tcProfileCreateData has not been specified.");
      }

      NamingAttributesT name = tcProfileCreateDataT.getName();
      if (name==null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The TCProfile Name has not been specified.");
      }

      //Ensure the MD name is set correctly.  
      MtosiUtils.validateMD(name);
      NetworkElement ne = ManagedElementFactory.getAndValidateNE(name);

      switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
          return new ModifyTCProfileWorkerCM(mtosiHeader, tcProfileCreateDataT, name, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
            return new ModifyTCProfileWorkerHN(mtosiHeader, tcProfileCreateDataT, name, ne);
        	
        default:
          throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.ME_NOT_SUPPORTED);
      }

  }

  @SuppressWarnings({"unchecked"})
  @MDPersistenceContext
  public static GetTPWorker createGetTPWorker (GetTPT mtosiBody, Holder mtosiHeader) throws ProcessingFailureException {

      NamingAttributesT tpName = mtosiBody.getTpName();
      if (tpName==null) {
        throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                "The TP Name has not been specified.");
      }

      //Ensure the MD name is set correctly.  
      MtosiUtils.validateMD(tpName);

      NetworkElement ne = ManagedElementFactory.getAndValidateNE(tpName);

      switch (ne.getMTOSIWorker().getNetworkElementTypeForMTOSI()) {
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM_CPMR:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CM:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE206:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG210:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_XG210C:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
          return new GetTPWorkerCM(mtosiHeader, tpName, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_150CP:
          return new GetTPWorker(mtosiHeader, tpName, ne);
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN4000:
        case NeTypeIds.NETWORK_ELEMENT_TYPE_HN400:
          return new GetTPWorkerHN(mtosiHeader, tpName, ne);
        default:
          throw ServiceUtils.createNewPFE(mtosiHeader, ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.ME_NOT_SUPPORTED);
      }
  }
}