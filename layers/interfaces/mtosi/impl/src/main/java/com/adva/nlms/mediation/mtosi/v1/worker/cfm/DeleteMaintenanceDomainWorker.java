/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */
package com.adva.nlms.mediation.mtosi.v1.worker.cfm;

import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.MDAttr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854ext.adva.DeleteMaintenanceDomainResponseT;
import v1.tmf854ext.adva.DeleteMaintenanceDomainT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;



public class DeleteMaintenanceDomainWorker extends AbstractMtosiWorker {
  private MtosiAddress mtosiAddress;
  private final DeleteMaintenanceDomainT mtosiBody;
  private final DeleteMaintenanceDomainResponseT response = new DeleteMaintenanceDomainResponseT();
  //  private LayeredParametersListType parameterListType;
  Logger LOG = LogManager.getLogger(this.getClass().getName());
  private MtosiMOFacade facade;
  private NetworkElement ne;
  private DTO<MDAttr> md;

  public DeleteMaintenanceDomainWorker(DeleteMaintenanceDomainT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "deleteMaintenanceDomain", "deleteMaintenanceDomain", "deleteMaintenanceDomainResponse");
    this.mtosiBody = mtosiBody;
    this.mtosiHeader = mtosiHeader;
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    validator.validate(mtosiAddress.getNE().getDefaultNetworkElementTypeString());
  }

  @Override
  protected void parse() throws ProcessingFailureException {
    NamingAttributesT meName = mtosiBody.getMdName();

    if (meName == null) {
            ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                "MeName has not been specified or is invalid.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    mtosiAddress = new MtosiAddress(meName);
    ne = mtosiAddress.getNE();
    if (mtosiAddress.getNaming().getMdNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MD_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    if (!mtosiAddress.getNaming().getMdNm().equals(OSFactory.getMDNm())) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MD_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if (mtosiAddress.getNaming().getMeNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.ME_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    MtosiUtils.validateNE(mtosiAddress.getNE());
    if (mtosiAddress.getNaming().getCfmMdNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MDFR_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    facade = getMtosiCtrl().getMoFacadeManager().getFacadeViaNeId(MtosiMOFacade.class, ne.getID());
    md = facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+mtosiAddress.getNaming().getCfmMdNm(), MDAttr.class);
    if (md == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MDRF_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
//    parameterListType = mtosiBody.getTransmissionParametersList();
//    if(parameterListType == null){
//      throw ExceptionUtils.createNewMPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
//          "Layered Parameter List has not been specified or is invalid.");
//    }
  }

  @Override
  protected void mediate() throws Exception {
    transact(md);
  }

  private void transact(DTO<MDAttr> md) throws ObjectInUseException, SNMPCommFailure, ProcessingFailureException {
    try {
      facade.openNetTransaction(ne.getID());
      facade.deleteObjectOnDevice(ne.getID(), md);
      ne.logSROperation(SROperationState.SERVICE_DELETION_SUCCESS, md.getValue(ManagedObjectAttr.MTOSI_NAME));
    } catch (ObjectInUseException | SNMPCommFailure e) {
      ne.logSROperation(SROperationState.SERVICE_DELETION_FAILURE, md.getValue(ManagedObjectAttr.MTOSI_NAME));
      throw e;
    } finally {
      facade.closeNetTransaction(ne.getID());
    }
  }

  @Override
  protected void response() {

  }

  @Override
  public Object getSuccessResponse() {
   if (response == null)
    return null;
    return response;
  }
}
