/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;
 /**
        DESCRIPTION
          "Current/Last PME link Fault Status. This is a bitmap of
          possible conditions. The various bit positions are:

            lossOfFraming       - Loss of Framing for 10P or
                                  Loss of Sync word for 2B PMD or
                                  Loss of 64/65B Framing
            snrMgnDefect        - SNR Margin dropped below the Threshold
            lineAtnDefect       - Line Attenuation exceeds the Threshold
            deviceFault         - Indicates a vendor-dependent
                                  diagnostic or self-test fault
                                  has been detected.
            configInitFailure   - Configuration initialization failure,
                                  due to inability of the PME link to
                                  support configuration profile,
                                  requested during initialization.
            protocolInitFailure - Protocol initialization failure,
                                  due to incompatible protocol used by
                                  the Peer PME during init (that could
                                  happen if a peer PMD is G.SDHSL/VDSL
                                  modem for 2BASE-TL/10PASS-TS PME
                                  respectively).

          This object is intended to supplement ifOperStatus in IF-MIB.
          The indications hold information about the last fault.
          efmCuPmeFltStatus is cleared by the device restart.
          In addition lossOfFraming, configInitFailure and
          protocolInitFailure are cleared by PME init.
          deviceFault is cleared by successful diagnostics/test.
          snrMgnDefect and lineAtnDefect are cleared by SNR Margin
          and line Attenuation respectively returning to norm and by
          PME init.

          This object partially maps to the Clause 30 attribute
          aPMEStatus.

          If a Clause 45 MDIO Interface to the PME is present, then this
          object consolidates information from various PMA/PMD
          registers, namely: Fault bit in PMA/PMD status 1 register,
          10P/2B PMA/PMD link loss register,
          10P outgoing indicator bits status register,
          10P incoming indicator bits status register,
          2B state defects register."

  * <AUTHOR>
  *
  */
public enum HNFaultStatusTranslation {
	noDefect(-1), 
    lossOfFraming(0),
    PROP_HATTERAS_snrMgnDefect(1),
    PROP_HATTERAS_lineAtnDefect(2),
    PROP_HATTERAS_deviceFault(3),
    configInitFailure(4),
    protocolInitFailure(5);
    
    private int mibValue;
    
    private int getMibValue() {
		return mibValue;
	}

	private HNFaultStatusTranslation(int code) {
    	this.mibValue = code;
    }

    public static String getMtosiString(final Integer mibValue) {
    	if (mibValue == null) {
    		return "null";
    	}
    	for (HNFaultStatusTranslation value: values() ) {
    		if (value.getMibValue() == mibValue.intValue()) {
    			return value.name(); 
    		}
    	}
    	//Probably should throw something...
    	return String.valueOf(mibValue);
    }
    
    public static int getMibValue(final String name) {
    	for (HNFaultStatusTranslation value: values() ) {
    		if (value.name().equals(name)) {
    			return value.getMibValue(); 
    		}
    	}
    	//Probably should throw something...
    	return -1;
    }
}
