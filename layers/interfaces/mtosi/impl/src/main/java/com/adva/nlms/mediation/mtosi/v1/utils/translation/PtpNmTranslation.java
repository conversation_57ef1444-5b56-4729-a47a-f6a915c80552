/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import java.util.regex.Pattern;
import java.util.regex.Matcher;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;

/**
 * Created by IntelliJ IDEA. User: Lukasz Date: 2007-05-30 Time: 13:14:09 To change this template use File | Settings |
 * File Templates.
 */
public enum PtpNmTranslation{
  LAN_1          (1, "LAN-1"),
  LAN_2          (2, "LAN-2"),
  LAN_3          (3, "LAN-3"),
  LAN_4          (4, "LAN-4"),
  LAN_5          (5, "LAN-5"),
  WAN_1          (6, "WAN-1"),
  WAN_2          (7, "WAN-2"),
  ACC            (6, "ACC"),  //index not reachable
  NET_A          (0, "NET-A"),//index not reachable
  NET_B          (0, "NET-B"),//index not reachable
  NET_1          (0, "NET-1"),//index not reachable
  NET_2          (0, "NET-2"),//index not reachable
  NOT_APPLICABLE (10, "n/a");

  //------------------------------------------------------------------------------------------------------------------
  private final int    portIndex;
  private final String mtosiString;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param portIndex    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private PtpNmTranslation (final int portIndex, final String mtosiString)
  {
    this.portIndex   = portIndex;
    this.mtosiString = mtosiString;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getPortIndex () {
    return portIndex;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int mibValue)
  {
    PtpNmTranslation enumType = NOT_APPLICABLE;  // the return value

    for (PtpNmTranslation tmpEnumType : values())
    {
      if (mibValue == tmpEnumType.getPortIndex())
      {
        enumType = tmpEnumType;
        break;
      }
    }
    return enumType.getMtosiString();
  }
  /**
   * Returns the string representation used in MTOSI layer.
   * @param mtosiString  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static int getPortIndex (final String mtosiString)
  {
    PtpNmTranslation enumType = NOT_APPLICABLE;  // the return value

    for (PtpNmTranslation tmpEnumType : values())
    {
      if (mtosiString.equals(tmpEnumType.getMtosiString()))
      {
        enumType = tmpEnumType;
        break;
      }
    }
    return enumType.getPortIndex();

  }

  public static boolean validName(final String ptpName) {
    if(ptpName == null)
      return false;
    String stdSlot = "^" + MtosiConstants.SHELF_TEXT + "\\d" + MtosiConstants.SLOT_TEXT + "\\d{1,2}" + MtosiConstants.PORT_TEXT + ".*";
    String scuSlot = "^/shelf=1/slot=SCU/port=BITS-(IN-[12]|OUT)";
    Pattern p = Pattern.compile(stdSlot+"|"+ scuSlot);
    Matcher m = p.matcher(ptpName);
    return m.matches();
  }
}
