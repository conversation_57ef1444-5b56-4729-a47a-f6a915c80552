/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NVSListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.StartContinuityTestResponseT;
import v1.tmf854ext.adva.StartContinuityTestT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public abstract class StartContinuityTestWorker extends AbstractMtosiWorker
{
  Logger LOG = LogManager.getLogger(this.getClass().getName());

  protected StartContinuityTestResponseT response = new StartContinuityTestResponseT();
  protected StartContinuityTestT mtosiBody;
  protected NamingAttributesT namingAttributes;
  protected NetworkElement ne;
  protected Port port;

  protected String testType;
  protected jakarta.xml.bind.JAXBElement<NVSListT> testParameters;

  public StartContinuityTestWorker(StartContinuityTestT mtosiBody, Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne){
    super(mtosiHeader, "startContinuityTest", "startContinuityTest", "startContinuityTestResponse");
    this.mtosiBody = mtosiBody;
    this.namingAttributes = namingAttributes;
    this.ne = ne;
  }

  @Override
  protected void parse() throws Exception {
    if (namingAttributes.getPtpNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ptpNm has not been specified.");
    }

    if ((testType = mtosiBody.getTestType()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.getMessageMandatory("testType"));
    }
    if (!testType.equals(/*ContinuityTestTypeT.Connectivity*/ "Connectivity") &&
            !testType.equals(/*ContinuityTestTypeT.*/ "Bandwidth")) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              testType + " is not a valid  test type.");
    }

    testParameters = mtosiBody.getTestParameters();
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
   validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  @Override
  protected void mediate() throws Exception {
    if ((port = ManagedElementFactory.getPort(namingAttributes)) == null){
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The specified parent PTP was not found.");
    }
  }

  @Override
  public StartContinuityTestResponseT getSuccessResponse(){
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
