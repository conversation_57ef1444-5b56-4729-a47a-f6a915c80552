/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi;

import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.common.NEUtils;
import com.adva.nlms.mediation.common.serviceProvisioning.F3PolicerSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.ShaperSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ShaperSPPropertiesHN4000;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMPolicerColorModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.LayeredParametersT;
import v1.tmf854.NameAndStringValueT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854ext.adva.TCProfileCreateDataT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;

public class MtosiTCProfileMediator
{



  public static F3PolicerSPProperties createPolicerSPProperties (final NamingAttributesT naming, final JAXBElement<LayeredParametersListT> transsmisionParams, final MTOSIFlowF3 flow) throws ProcessingFailureException
  {
    final String tcpNm = naming.getTcpNm();
    FlowSPPropertiesFSP150CM flowProps = flow.getFlowSPProperties();

    final F3PolicerSPProperties policerSPProperties = new F3PolicerSPProperties(
                flowProps.get(FlowSPPropertiesFSP150CM.VI.NeIndex),
                flowProps.get(FlowSPPropertiesFSP150CM.VI.ShelfIndex),
                flowProps.get(FlowSPPropertiesFSP150CM.VI.SlotIndex),
                flowProps.get(FlowSPPropertiesFSP150CM.VI.AccPortIndex),
                flowProps.get(FlowSPPropertiesFSP150CM.VI.FlowIndex),
                (tcpNm.startsWith(MtosiConstants.EG_POLICER_TEXT)) ? MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_N2A
                                                                   : MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_A2N,
                (tcpNm.startsWith(MtosiConstants.EG_POLICER_TEXT)) ?                                                                 
                    MtosiUtils.getRegexGroupInt(tcpNm, "^"+MtosiConstants.EG_POLICER_TEXT+"(\\d+)$", 0, 0, MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID)+1
                    :
                    MtosiUtils.getRegexGroupInt(tcpNm, "^"+MtosiConstants.ING_POLICER_TEXT+"(\\d+)$", 0, 0, MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID)+1
              );

    for(LayeredParametersT layeredParametersT : transsmisionParams.getValue().getLayeredParameters())
    {
      final String layerName = layeredParametersT.getLayer();
      if(layerName.startsWith(LayeredParams.PROP_ADVA_ETHERNET_POLICER))
      {
        for (NameAndStringValueT nameAndStringValueT : layeredParametersT.getTransmissionParams().getNvs())
        {
          if(nameAndStringValueT.getName().equals(LayeredParams.LrPropAdvaEthernetShaper.INGRESS_CIR_PARAM) ||
             nameAndStringValueT.getName().equals(LayeredParams.LrPropAdvaEthernetShaper.EGRESS_CIR_PARAM)) {
            policerSPProperties.set(ShaperSPProperties.VL.CIR, Long.valueOf(nameAndStringValueT.getValue()));
          }
          if(nameAndStringValueT.getName().equals(LayeredParams.LrPropAdvaEthernetShaper.INGRESS_EIR_PARAM) ||
             nameAndStringValueT.getName().equals(LayeredParams.LrPropAdvaEthernetShaper.EGRESS_EIR_PARAM)) {
            policerSPProperties.set(ShaperSPProperties.VL.EIR, Long.valueOf(nameAndStringValueT.getValue()));
          }
          if(nameAndStringValueT.getName().equals(LayeredParams.LrPropAdvaEthernetShaper.INGRESS_CBS_PARAM) ||
             nameAndStringValueT.getName().equals(LayeredParams.LrPropAdvaEthernetShaper.EGRESS_CBS_PARAM)) {
            policerSPProperties.set(F3PolicerSPProperties.VI.CBS, Integer.valueOf(nameAndStringValueT.getValue()));
          }
          if(nameAndStringValueT.getName().equals(LayeredParams.LrPropAdvaEthernetShaper.INGRESS_EBS_PARAM) ||
             nameAndStringValueT.getName().equals(LayeredParams.LrPropAdvaEthernetShaper.EGRESS_EBS_PARAM)) {
            policerSPProperties.set(F3PolicerSPProperties.VI.EBS, Integer.valueOf(nameAndStringValueT.getValue()));
          }
          if(nameAndStringValueT.getName().equals(LayeredParams.LrPropAdvaEthernetPolicer.COLOR_MODE_PARAM)) {
            int colorMode = MtosiUtils.getMIBValue(CMPolicerColorModeTranslation.NOT_APPLICABLE, nameAndStringValueT.getValue());
            if (colorMode != CMPolicerColorModeTranslation.NOT_APPLICABLE.getMIBValue()) {
              policerSPProperties.set(F3PolicerSPProperties.VI.ColorMode, colorMode);
            }
          }
          if(nameAndStringValueT.getName().equals(LayeredParams.LrPropAdvaEthernetPolicer.COLOR_MARKING_FLAG_PARAM)) {
            int colorMarkingFlag = MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE, nameAndStringValueT.getValue());
            if (colorMarkingFlag != BooleanTypeTranslation.NOT_APPLICABLE.getMIBValue()) {
              policerSPProperties.set(F3PolicerSPProperties.VI.ColorMarkingFlag, colorMarkingFlag);
            }
          }
          if(nameAndStringValueT.getName().equals(LayeredParams.LrPropAdvaEthernetPolicer.COUPLING_FLAG_PARAM)) {
            int couplingFlag = MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE, nameAndStringValueT.getValue());
            if (couplingFlag != BooleanTypeTranslation.NOT_APPLICABLE.getMIBValue()) {
              policerSPProperties.set(F3PolicerSPProperties.VI.CouplingFlag, couplingFlag);
            }
          }
        }
      }
    }

    return policerSPProperties;
  }

  public static ShaperSPProperties createShaperSPProperties (final NamingAttributesT naming, final JAXBElement<LayeredParametersListT> transsmisionParams, final MTOSIFlowF3 flow) throws ProcessingFailureException
  {
    int neType = ManagedElementFactory.getAndValidateNE(naming).getMTOSIWorker().getNetworkElementTypeForMTOSI();
    final String tcpNm = naming.getTcpNm();
    final ShaperSPProperties shaperSPProperties = getShaperSPProperties(neType, transsmisionParams);
    FlowSPPropertiesFSP150CM flowProps = flow.getFlowSPProperties();
    shaperSPProperties.set(ShaperSPProperties.VI.NeIndex, flowProps.get(FlowSPPropertiesFSP150CM.VI.NeIndex));
    shaperSPProperties.set(ShaperSPProperties.VI.ShelfIndex, flowProps.get(FlowSPPropertiesFSP150CM.VI.ShelfIndex));
    shaperSPProperties.set(ShaperSPProperties.VI.SlotIndex, flowProps.get(FlowSPPropertiesFSP150CM.VI.SlotIndex));
    shaperSPProperties.set(ShaperSPProperties.VI.FlowIndex, flowProps.get(FlowSPPropertiesFSP150CM.VI.FlowIndex));
    if(tcpNm.startsWith(MtosiConstants.EG_SHAPER_TEXT))
      shaperSPProperties.set(ShaperSPProperties.VI.TypeIndex, MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_N2A);
    else if(tcpNm.startsWith(MtosiConstants.ING_SHAPER_TEXT))
      shaperSPProperties.set(ShaperSPProperties.VI.TypeIndex, MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_A2N);
    shaperSPProperties.set(ShaperSPProperties.VI.Index, NamingTranslationFactory.getShaperIndex(tcpNm));

    return shaperSPProperties;
  }

  private static ShaperSPProperties getShaperSPProperties(int neType, JAXBElement<LayeredParametersListT> transsmisionParams)
  {
    final ShaperSPProperties shaperSPProperties = new ShaperSPProperties();

    for(LayeredParametersT layeredParametersT : transsmisionParams.getValue().getLayeredParameters())
    {
      final String layerName = layeredParametersT.getLayer();
      if(layerName.startsWith(LayeredParams.PROP_ADVA_ETHERNET_SHAPER))
      {
        for(NameAndStringValueT nameAndStringValueT : layeredParametersT.getTransmissionParams().getNvs())
        {
          /*if (nameAndStringValueT.getName().startsWith(LayeredParams.LrPropAdvaEthernetShaper.ADMINISTRATION_CONTROL_PARAM)) {
            int adminState = MtosiUtils.getMIBValue(CMAdministrationControlTranslation.NOT_APPLICABLE, nameAndStringValueT.getValue());
            if (adminState != CMAdministrationControlTranslation.NOT_APPLICABLE.getMIBValue()) {
              shaperSPProperties.set(ShaperSPProperties.VI.AdminState, adminState);
            }
          }
          // for GE20x: Ingress/Egress-CBS/EBS are currently not modifiable
          else*/ if (!NEUtils.isGEDevice(neType) &&
                   (nameAndStringValueT.getName().startsWith(LayeredParams.LrPropAdvaEthernetShaper.INGRESS_CBS_PARAM) ||
                    nameAndStringValueT.getName().startsWith(LayeredParams.LrPropAdvaEthernetShaper.EGRESS_CBS_PARAM)))
            shaperSPProperties.set(ShaperSPProperties.VL.CBS, Long.valueOf(nameAndStringValueT.getValue()));
          else if (!NEUtils.isGEDevice(neType) &&
                   (nameAndStringValueT.getName().startsWith(LayeredParams.LrPropAdvaEthernetShaper.INGRESS_EBS_PARAM) ||
                    nameAndStringValueT.getName().startsWith(LayeredParams.LrPropAdvaEthernetShaper.EGRESS_EBS_PARAM)))
            shaperSPProperties.set(ShaperSPProperties.VL.EBS, Long.valueOf(nameAndStringValueT.getValue()));
          else if (nameAndStringValueT.getName().startsWith(LayeredParams.LrPropAdvaEthernetShaper.INGRESS_BUFFER_SIZE_PARAM) ||
                   nameAndStringValueT.getName().startsWith(LayeredParams.LrPropAdvaEthernetShaper.EGRESS_BUFFER_SIZE_PARAM))
            shaperSPProperties.set(ShaperSPProperties.VL.BufferSize, Long.valueOf(nameAndStringValueT.getValue()));
        }
      }
    }
    return shaperSPProperties;
  }

	public static ShaperSPPropertiesHN4000 getPolicerSPPropertiesHN(TCProfileCreateDataT tcProfileCreateDataT) throws ProcessingFailureException {
		LayeredParametersListT transmissionParams = tcProfileCreateDataT.getTransmissionParams().getValue();
		ShaperSPPropertiesHN4000 newProps = new ShaperSPPropertiesHN4000();

		// IngressCIR
		// INTEGER (0..50000)
		String parameter = LayeredParameterUtils.getLayeredParameter(transmissionParams, LayeredParams.PROP_HATTERAS_Ethernet_Policer,
				LayeredParams.PropHatterasEthernetPolicer.INGRESS_CIR_PARAM);
		if (parameter != null) {

			try {
				Long cir = Long.valueOf(parameter);
				if (cir < 0 || cir > 50000) {
					pfeIllegal(LayeredParams.PropHatterasEthernetPolicer.INGRESS_CIR_PARAM);
				}
				newProps.set(ShaperSPPropertiesHN4000.VL.IngCIR, cir);
			} catch (NumberFormatException e) {
				pfeIllegal(LayeredParams.PropHatterasEthernetPolicer.INGRESS_CIR_PARAM);
			}
		}

		// IngressCBS
		// INTEGER (1536..100000)
		parameter = LayeredParameterUtils.getLayeredParameter(transmissionParams, LayeredParams.PROP_HATTERAS_Ethernet_Policer,
				LayeredParams.PropHatterasEthernetPolicer.INGRESS_CBS_PARAM);
		if (parameter != null) {

			try {
				Long cbs = Long.valueOf(parameter);
				if (cbs < 1536 || cbs > 100000) {
					pfeIllegal(LayeredParams.PropHatterasEthernetPolicer.INGRESS_CBS_PARAM);
				}
				newProps.set(ShaperSPPropertiesHN4000.VL.IngBC, cbs);
			} catch (NumberFormatException e) {
				pfeIllegal(LayeredParams.PropHatterasEthernetPolicer.INGRESS_CBS_PARAM);
			}
		}

		return newProps;
	}

	public static ShaperSPPropertiesHN4000 getShaperSPPropertiesHN(TCProfileCreateDataT tcProfileCreateDataT) throws ProcessingFailureException {
		LayeredParametersListT transmissionParams = tcProfileCreateDataT.getTransmissionParams().getValue();
		ShaperSPPropertiesHN4000 newProps = new ShaperSPPropertiesHN4000();

		// EgressCIR
		//INTEGER (0..6450000)
		String parameter = LayeredParameterUtils.getLayeredParameter(transmissionParams, LayeredParams.PROP_HATTERAS_Ethernet_Shaper,
				LayeredParams.PropHatterasEthernetShaper.EGRESS_CIR_PARAM);
		if (parameter != null) {

			try {
				Long cir = Long.valueOf(parameter);
				if (cir < 0 || cir > 6450000) {
					pfeIllegal(LayeredParams.PropHatterasEthernetShaper.EGRESS_CIR_PARAM);
				}
				newProps.set(ShaperSPPropertiesHN4000.VL.EgCIR, cir);
			} catch (NumberFormatException e) {
				pfeIllegal(LayeredParams.PropHatterasEthernetShaper.EGRESS_CIR_PARAM);
			}
		}

		// EgressCIRQueue
		// INTEGER (0..3)
		parameter = LayeredParameterUtils.getLayeredParameter(transmissionParams, LayeredParams.PROP_HATTERAS_Ethernet_Shaper,
				LayeredParams.PropHatterasEthernetShaper.EGRESS_CIR_QUEUE_PARAM);
		if (parameter != null) {

			try {
				Long queue = Long.valueOf(parameter);
				if (queue < 0 || queue > 3) {
					pfeIllegal(LayeredParams.PropHatterasEthernetShaper.EGRESS_CIR_QUEUE_PARAM);
				}
				newProps.set(ShaperSPPropertiesHN4000.VL.EgCIRque, queue);
			} catch (NumberFormatException e) {
				pfeIllegal(LayeredParams.PropHatterasEthernetShaper.EGRESS_CIR_QUEUE_PARAM);
			}
		}

		return newProps;
	}




private static void pfeIllegal( String var) throws ProcessingFailureException {
	 pfe(MtosiErrorConstants.getMessageIllegal(var));
}

private static void pfe( String var) throws ProcessingFailureException {
	ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,var);
	ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
	throw pfe;
}

}
