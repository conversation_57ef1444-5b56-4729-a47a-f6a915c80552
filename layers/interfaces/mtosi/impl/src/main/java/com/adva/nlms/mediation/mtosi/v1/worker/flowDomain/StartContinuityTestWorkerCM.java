/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.common.property.FNMPropertyConstants;
import com.adva.nlms.common.property.FNMPropertyFactory;
import com.adva.nlms.mediation.common.serviceProvisioning.ContinuityTestPropertiesFSP150CM;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.f3.entity.port.acc.MTOSIPortF3Acc;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import v1.tmf854.HeaderT;
import v1.tmf854.NameAndStringValueT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.StartContinuityTestT;

import jakarta.xml.ws.Holder;
import java.util.Arrays;
import java.util.List;

public class StartContinuityTestWorkerCM extends StartContinuityTestWorker {
  private static final int OVERHEAD_PERCENT_RATE = FNMPropertyFactory.getPropertyAsInt(FNMPropertyConstants.CONTINUITY_OVERHEAD_PERCENT_RATE, 95);
  private String ctpName;

  public StartContinuityTestWorkerCM (StartContinuityTestT mtosiBody, Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne) {
    super(mtosiBody, mtosiHeader, namingAttributes, ne);
  }

  @Override
  protected void parse() throws Exception {
    super.parse();
    if ((this.ctpName = namingAttributes.getCtpNm()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ctpNm has not been specified.");
    }
  }

  @Override
  protected void mediate() throws Exception {
    super.mediate();
    if (!(port instanceof MTOSIPortF3Acc)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "Continuity test should be run on ACC Port.");
    }
    MTOSIPortF3Acc portACC = (MTOSIPortF3Acc) port;
    MTOSIFlowF3 flow;
    if ((flow = portACC.getFlowFSP150CMByName(ctpName)) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.CTP_NOT_FOUND);
    }
    ContinuityTestPropertiesFSP150CM contTestProps = new ContinuityTestPropertiesFSP150CM();
    contTestProps.set(ContinuityTestPropertiesFSP150CM.VI.StreamOverheadRate, OVERHEAD_PERCENT_RATE);
    List<NameAndStringValueT> list;
    if (testParameters != null) {
      
      list = this.testParameters.getValue().getNvs();
      String testFrameType = null;
      for (NameAndStringValueT parameter : list) {
        if (parameter.getName().equals("TestFrameType")) {
          MtosiUtils.validateEnumParameter(parameter.getValue(), Arrays.asList("VLANTagged","Untagged"), "The parameter TestFrameType is wrong.");
          testFrameType = parameter.getValue();
        }
        else if (parameter.getName().equals("TestFrameVLANId")) {
          contTestProps.set(ContinuityTestPropertiesFSP150CM.VI.VlanId,
                            MtosiUtils.validateIntParameter(parameter.getValue(), "The parameter TestFrameVLANId is wrong."));
        }
        else if (parameter.getName().equals("TestFrameVLANPriority")) {
          contTestProps.set(ContinuityTestPropertiesFSP150CM.VI.VlanPrio,
                            MtosiUtils.validateIntParameter(parameter.getValue(), "The parameter TestFrameVLANPriority is wrong."));
        }
        else {
          throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
                  "Invalid test parameter.");
        }
      }
    }

    transact(flow, contTestProps);
  }

  @Override
  protected void response () throws Exception {
    // empty method
  }

  private void transact(MTOSIFlowF3 flow, ContinuityTestPropertiesFSP150CM contTestProps)
          throws ObjectInUseException, NetTransactionException, SNMPCommFailure {

    NetworkElement locks[] = new NetworkElement[]{ne};
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, getClass().getSimpleName());
    try{
      if (testType.equals(/*ContinuityTestTypeT.Connectivity*/ "Connectivity")) {
        flow.startConnectivityTest(contTestProps);
      } else {
        flow.startBandwidthTest(contTestProps);
      }
      NetTransactionManager.commitNetTransaction(id);
    } catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally{
      NetTransactionManager.ensureEnd(id);
    }
  }
}
