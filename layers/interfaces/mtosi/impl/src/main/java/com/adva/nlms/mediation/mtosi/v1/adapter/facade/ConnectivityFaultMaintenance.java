/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.adapter.facade;

import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.MACompAttr;
import com.adva.nlms.mediation.config.dto.attr.MANetAttr;
import com.adva.nlms.mediation.config.dto.attr.MDAttr;
import com.adva.nlms.mediation.config.dto.attr.MEPAttr;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;

public interface ConnectivityFaultMaintenance {

  public DTO<MDAttr> getMD(String mdFrName) throws Exception;
  public DTO<MANetAttr> getMANet(MtosiAddress mtosiAddress, DTO<MDAttr> md) throws Exception;
  public DTO<MACompAttr> getMAComp(MtosiAddress mtosiAddress, DTO<MANetAttr> manet, DTO ptp) throws Exception;
  public DTO<MEPAttr> getMEP(MtosiAddress mtosiAddress, DTO<MANetAttr> manet, DTO ptp) throws Exception;
  public String getMipCreationControl();
  public Integer getMDLevel();
  public String getMepDirection();
  public String getMaCompMhfCreation();
  public String getMepAdministrationState();
  public String getMepLowestPriorityDefect();
  public Integer getMepVlanPriority();
  public String getMepCciEnabled();
  public Integer getMepActive();
  public Integer getMepPrimaryVid();
  public String getMaMepList();
  public Integer getMaNumberOfVids();
  public String getMaPrimaryVid();
  public String getMaCcmInterval();
  public String getMepLlfTrigger();
  public String getMepTransmissionInternal();
  public String getMepAisGeneration();
  public String getMepAisPriority();
  public String getMepAisClientMdLevel();
  public String getMepAisTtrigger();

}
