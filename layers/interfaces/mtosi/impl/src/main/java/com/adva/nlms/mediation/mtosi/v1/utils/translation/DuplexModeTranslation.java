/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;


public enum DuplexModeTranslation{
  FULL        (1, "Full"),
  HALF        (2, "Half"),
  NOT_APPLICABLE (3, "n/a");

  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private DuplexModeTranslation (final int mibValue, final String mtosiString)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int mibValue)
  {
    DuplexModeTranslation duplexModeTranslation = NOT_APPLICABLE;  // the return value

    for (DuplexModeTranslation tmpDuplexModeTranslation : values())
    {
      if (mibValue == tmpDuplexModeTranslation.getMIBValue())
      {
        duplexModeTranslation = tmpDuplexModeTranslation;
        break;
      }
    }
    return duplexModeTranslation.getMtosiString();
  }
  /**
   * Returns the string representation used in MTOSI layer.
   * @param mtosiString  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static int getMIBValue (final String mtosiString)
  {
    DuplexModeTranslation duplexModeTranslation = NOT_APPLICABLE;  // the return value

    for (DuplexModeTranslation tmpDuplexModeTranslation : values())
    {
      if (mtosiString.equals(tmpDuplexModeTranslation.getMtosiString()))
      {
        duplexModeTranslation = tmpDuplexModeTranslation;
        break;
      }
    }
    return duplexModeTranslation.getMIBValue();

  }
}
