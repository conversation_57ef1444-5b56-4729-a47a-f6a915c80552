/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.hn4000;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import v1.tmf854.FloatingTerminationPointT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.common.serviceProvisioning.FTPSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.LAGSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.UNISPPropertiesHN4000;
import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.config.hn4000.mtosi.FTPHN4000;
import com.adva.nlms.mediation.config.hn4000.ObjectStateFieldHN4000;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.FTPTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNCosMapTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNEgressRateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNLAGLoadBalanceTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNRoleStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNSTPAdminControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNTagEtherTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNuniPortTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.ServiceStateTranslation;

/**
 * This class is a HN4000 FTP MTOSI Translator.
 */
public class FTPHN4000Translator extends FTPTranslator {
	FTPHN4000 ftp = null;
	
  public FTPHN4000Translator (FTP ftp) {
    super(ftp);
    this.ftp = (FTPHN4000) ftp;
  }

  /**
   * This toMtosiFTP only handles the LAG FTPs
   * The PortHN4000Ethernet2Base_TLTranslator handles the Bonded Ports FTPs
   * 
   */
  @Override
  public FloatingTerminationPointT toMtosiFTP() throws ProcessingFailureException {
	    final ObjectFactory objFactory = new ObjectFactory();
	    final FloatingTerminationPointT floatingTerminationPointT = objFactory.createFloatingTerminationPointT();
	    final LAGSPPropertiesHN4000 properties = (LAGSPPropertiesHN4000)ftp.getFTPSPProperties();

	    NamingAttributesT namingAttribute = NamingTranslationFactory.getNamingAttributesHN4000(ftp);

	    fillCommonInformation(objFactory, floatingTerminationPointT,namingAttribute);

	    // layers
	    final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

	    // -------start LR_LAG Layer--------
	    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_LAG);
	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_LAG,
	            LayeredParams.LrLag.ALLOCATED_NUMBER_PARAM, 
	            String.valueOf(properties.get(LAGSPPropertiesHN4000.VI.AllocatedNumber)));

	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_LAG,
	            LayeredParams.LrLag.ALLOCATION_MAXIMUM_PARAM, "16");

	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_LAG,
	            LayeredParams.LrLag.FRAGMENT_SERVER_LAYER_PARAM, LayeredParams.LR_ETHERNET);
	    // -------end of Layer-------

	    // -------start PROP_HATTERAS_LAG Layer--------
	    int adminState = properties.get(LAGSPPropertiesHN4000.VI.AdminState) == null?-1:properties.get(LAGSPPropertiesHN4000.VI.AdminState);
	    int operState = properties.get(LAGSPPropertiesHN4000.VI.OperState) == null?-1:properties.get(LAGSPPropertiesHN4000.VI.OperState);
		LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_LAG);
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_LAG, LayeredParams.PropHatterasLag.SERVICE_STATE_PARAM,
				ServiceStateTranslation.getMtosiString(adminState,operState));
		Integer secondaryState = properties.get(LAGSPPropertiesHN4000.VI.ObjectState);
		if (secondaryState != null) 
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_LAG, LayeredParams.PropHatterasLag.SECONDARY_STATE_PARAM,
					ObjectStateFieldHN4000.getSecondaryStateString(secondaryState));
		else 
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_LAG, LayeredParams.PropHatterasLag.SECONDARY_STATE_PARAM,
					"");
		
//		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_LAG, LayeredParams.PropHatterasLag.LAG_FRAGEMENT_PORT_LIST_PARAM,
//				String.valueOf(properties.getPortsList()));

		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_LAG, LayeredParams.PropHatterasLag.LAG_LOAD_BALANCE_PARAM,
				HNLAGLoadBalanceTranslation.getMtosiString(properties.get(LAGSPPropertiesHN4000.VI.LoadBalance)));
		// -------end of Layer-------
		
	    // -------start LR_LAG_FRAGMENT Layer--------
	    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_LAG_FRAGMENT);
	    // -------end of Layer-------

	    // -------start Layer--------
	    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,LayeredParams.LrEthernet.CONNECTIONLESS_PORT_PARAM, 
	    		MtosiConstants.TRUE);

	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET, LayeredParams.LrEthernet.INTERFACE_TYPE_PARAM,
	    		properties.get(LAGSPPropertiesHN4000.IT.InterfaceType) == null? MtosiConstants.NOT_APPLICABLE : properties.get(LAGSPPropertiesHN4000.IT.InterfaceType).getIfTypeString());

		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET, LayeredParams.LrEthernet.PORT_TP_ROLE_STATE_PARAM,
				HNRoleStateTranslation.getMtosiString(properties.get(LAGSPPropertiesHN4000.VI.PortTPRoleState)));

	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,LayeredParams.LrEthernet.MAX_NUM_FDFRS_PARAM, 
	    		getMaxNumFdfrs());

	    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,LayeredParams.LrEthernet.NUM_CONFIGURED_FDFRS_PARAM,
	    		String.valueOf(properties.get(FTPSPProperties.VI.NumOfFDFrs)));
	    // -------end of Layer-------

	    // -------start PROP_HATTERAS_Ethernet Layer--------
	    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet);
	    // -------end of Layer-------

	    fillUNILayer(layeredParametersListT);

	    floatingTerminationPointT.setTransmissionParams(objFactory.createConnectionTerminationPointTTransmissionParams(layeredParametersListT));
	    return floatingTerminationPointT;
	  }

	@Override
	protected String getMaxNumFdfrs() {
		return "256";
	}
	  protected void fillUNILayer(LayeredParametersListT layeredParametersListT) {
			 
			try {
//				System.out.println("ftp: "+ftp);
				if (ftp.getLag().hasUni()) {
//					System.out.println(ftp.getLag().getUni());
					if (ftp.getLag().getUni().getUniSPProperties() == null) {
						return;
					}
				} else {
//					System.out.println("No Uni");
					return;
				}
			} catch (Exception e) {
				// Exceptions are happening getting the UNI.
//				System.out.println(e);
				return;
			}
			NetworkElement ne = ftp.getLag().getNE();
			boolean hn400 = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400;

			UNISPPropertiesHN4000 properties = ftp.getLag().getUni().getUniSPProperties();
//			System.out.println(properties);
			LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI);
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
					LayeredParams.PropHatterasEthernetUni.THIS_LAYER_ACTIVE_PARAM, LayeredParams.ACTIVE_PARAM);
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
					LayeredParams.PropHatterasEthernetUni.NAME_PARAM, 
					String.valueOf(properties.get(UNISPPropertiesHN4000.VS.Name)));
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
					LayeredParams.PropHatterasEthernetUni.DESCRIPTION_PARAM, 
					String.valueOf(properties.get(UNISPPropertiesHN4000.VS.PortDesc)));
			Integer secondaryState = properties.get(UNISPPropertiesHN4000.VI.ObjState);
			if (secondaryState != null)
				LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
						LayeredParams.PropHatterasEthernetUni.SECONDARY_STATE_PARAM, ObjectStateFieldHN4000.getSecondaryStateString(secondaryState));
			else
				LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet,
						LayeredParams.PropHatterasShdsl.SECONDARY_STATE_PARAM, "");
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
					LayeredParams.PropHatterasEthernetUni.DEFAULT_VLANID_PARAM, 
					String.valueOf(properties.get(UNISPPropertiesHN4000.VL.DefVlanId)));
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
					LayeredParams.PropHatterasEthernetUni.STP_ADMINISTRATIONCONTROL_PARAM, 
					HNSTPAdminControlTranslation.getMtosiString(properties.get(UNISPPropertiesHN4000.VI.StpAdminState)));
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
					LayeredParams.PropHatterasEthernetUni.PORT_TYPE_PARAM, 
					HNuniPortTypeTranslation.getMtosiString(properties.get(UNISPPropertiesHN4000.VI.PortType)));
			if (hn400) {
				LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
						LayeredParams.PropHatterasEthernetUni.EGRESS_RATE_LIMITING_ENABLED_PARAM, 
						HNEgressRateTranslation.getMtosiString(properties.get(UNISPPropertiesHN4000.VL.EgressRate)));
			}
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
					LayeredParams.PropHatterasEthernetUni.TAG_ETHER_TYPE_PARAM, 
					HNTagEtherTypeTranslation.getMtosiString(properties.get(UNISPPropertiesHN4000.VL.TagEtherType)));
			//Suppress if PortType is Access
			if (properties.get(UNISPPropertiesHN4000.VI.PortType) == null || HNuniPortTypeTranslation.Access.getMibValue() != properties.get(UNISPPropertiesHN4000.VI.PortType)) {
				LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
						LayeredParams.PropHatterasEthernetUni.RX_UNTAGGED_PARAM, 
						MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, properties.get(UNISPPropertiesHN4000.VI.RxUntagged)));
				LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
						LayeredParams.PropHatterasEthernetUni.RX_UNKNOWN_PARAM, 
						MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, properties.get(UNISPPropertiesHN4000.VI.RxUnknown)));
				if (hn400) {
					LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
							LayeredParams.PropHatterasEthernetUni.TX_UNTAGGED_PARAM, 
							MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, properties.get(UNISPPropertiesHN4000.VI.TxUntagged)));
				}
			}
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
					LayeredParams.PropHatterasEthernetUni.COS_MAP_TYPE_PARAM, 
					HNCosMapTypeTranslation.getMtosiString(properties.get(UNISPPropertiesHN4000.VI.CosMapType)));
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
					LayeredParams.PropHatterasEthernetUni.DEFAULT_COS_PARAM, "1");
		}

}
