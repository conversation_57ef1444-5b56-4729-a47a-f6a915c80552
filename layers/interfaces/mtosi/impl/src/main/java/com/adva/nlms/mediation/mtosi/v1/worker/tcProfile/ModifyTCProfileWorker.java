/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.tcProfile;


import jakarta.xml.ws.Holder;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.ModifyTCProfileResponseT;
import v1.tmf854ext.adva.TCProfileCreateDataT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;


public abstract class ModifyTCProfileWorker extends AbstractMtosiWorker {
  protected ModifyTCProfileResponseT response = new ModifyTCProfileResponseT();
  protected TCProfileCreateDataT tcProfileCreateDataT;
  protected NamingAttributesT name;
  protected NetworkElement ne;
  protected String tcpNm;

  public ModifyTCProfileWorker (final Holder<HeaderT> mtosiHeader, TCProfileCreateDataT tcProfileCreateDataT, NamingAttributesT name, NetworkElement ne) {
    super(mtosiHeader, "modifyTCProfile", "modifyTCProfile", "modifyTCProfileResponse");
    this.tcProfileCreateDataT = tcProfileCreateDataT;
    this.name = name;
    this.ne = ne;
  }

  @Override
  protected void parse() throws Exception {
    if (!NamingTranslationFactory.isManagedElement(name)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.ME_NAME_MISSING);
    }

    if (!name.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MD_NOT_FOUND);
    }

    if (!NamingTranslationFactory.isTCProfileNameValid(tcpNm = name.getTcpNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID);
    }
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
   validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  @Override
  protected void response() throws ProcessingFailureException {
    response.setNewTCProfile(ManagedElementFactory.getTCProfile(tcProfileCreateDataT.getName(),ne));
  }

  @Override
  public ModifyTCProfileResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
