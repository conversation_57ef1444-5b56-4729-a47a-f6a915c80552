/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cp;

import jakarta.xml.bind.JAXBElement;
import javax.xml.namespace.QName;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import v1.tmf854.MEVendorExtensionsT;

import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.NetworkElementFSP150CP;
import com.adva.nlms.mediation.config.NetworkElement;

/**
 * This class is a Network Element Mediator for the FSP 150 CP EFM boxes (CP Remote boxes).
 */
public class Fsp150cpEfmMediator extends Fsp150cpMediator {
  public Fsp150cpEfmMediator (NetworkElementFSP150CP ne) {
    super(ne);
  }

  @Override
  protected void populateIpAddress (MEVendorExtensionsT extensions) {
    NetworkElement peerNe = ne.getPeerNetworkElement();
    JAXBElement jeProductName = new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_EFM_MGR_PRODUCT_NAME), String.class, peerNe.getMTOSIWorker().getProductName());
    extensions.getAny().add(jeProductName);
    JAXBElement jeMeName = new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_EFM_MGR_ME_NAME), String.class, peerNe.getName());
    extensions.getAny().add(jeMeName);
  }
}
