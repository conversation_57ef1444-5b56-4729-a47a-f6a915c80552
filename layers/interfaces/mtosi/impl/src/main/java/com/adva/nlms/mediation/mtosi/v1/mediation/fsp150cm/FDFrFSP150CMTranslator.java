/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrEndIDs;
import com.adva.nlms.mediation.common.serviceProvisioning.NETPortSPPropertiesFSP150CM;
import com.adva.nlms.mediation.config.Module;
import com.adva.nlms.mediation.config.f3.NetworkElementF3;
import com.adva.nlms.mediation.config.f3.entity.module.nte.NteF3Impl;
import com.adva.nlms.mediation.config.f3.mtosi.NetworkElementFSP150CMMTOSIOperations;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FDFrACCEndFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FDFrEndFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FDFrFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.model.FDFrACCEndFSP150CMIDs;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.model.FDFrFTPEndFSP150CMIDs;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.model.FDFrNETEndFSP150CMIDs;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.FDFrTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.ObjectFactory;
import ws.v1.tmf854.ProcessingFailureException;

import java.util.Collection;

/**
 * This class is an FSP 150 CM FDFr MTOSI Translator.
 */
public class FDFrFSP150CMTranslator extends FDFrTranslator {
  private FDFrFSP150CM fdfr;

  public FDFrFSP150CMTranslator (FDFrFSP150CM fdfr) {
    super(fdfr);
    this.fdfr = fdfr;
  }

  /**
   * Returns list of coresponding naming attributes of the specific multiEndPoint.
   *
   * @param endPointsCollection The collection of FDFr's endpoints
   * @return JAXBElement<NamingAttributesListT>
   * @throws ws.v1.tmf854.ProcessingFailureException If something went wrong
   */
  @Override
  protected NamingAttributesListT getEndPointNamingAttributesListT(
          final Collection<FDFrEndIDs> endPointsCollection) throws ProcessingFailureException {
    final ObjectFactory objFactory = new ObjectFactory();
    final NamingAttributesListT namingAttributesListT = objFactory.createNamingAttributesListT();
    NetworkElementF3 ne = fdfr.getNetworkElement();
    for (FDFrEndIDs fdFrEndIDs : endPointsCollection) {
      if (fdFrEndIDs instanceof FDFrACCEndFSP150CMIDs) {
        namingAttributesListT.getName().add(NamingTranslationFactory.getNamingAttributes((FDFrACCEndFSP150CMIDs) fdFrEndIDs, ne));
      } else if (fdFrEndIDs instanceof FDFrNETEndFSP150CMIDs) {
        namingAttributesListT.getName().add(NamingTranslationFactory.getNamingAttributes((FDFrNETEndFSP150CMIDs) fdFrEndIDs, ne));
      } else if (fdFrEndIDs instanceof FDFrFTPEndFSP150CMIDs) {
        namingAttributesListT.getName().add(NamingTranslationFactory.getNamingAttributes((FDFrFTPEndFSP150CMIDs) fdFrEndIDs, ne));
      } else {
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_TP_INVALID_ENDPOINT, "Unknown FDFr end point.");
      }
    }
    return namingAttributesListT;
  }
  /**
   * Allow subclasses to override the createLayeredParameters.
   * Create Empty Ethernet and ADVA_Ethernet layers. For the NON-Hatteras devices
   * @param layeredParametersListT
   */
  @Override
	protected void createLayeredParameters(final LayeredParametersListT layeredParametersListT) {
	  super.createLayeredParameters(layeredParametersListT);
    final boolean isGE20X = fdfr.getNetworkElement().getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE;
    final boolean isNteModule = isNteModule();
	  if (isGE20X || isNteModule) {
	    // -------start Layer--------
	    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_SyncEthernet);
	    // -------end of Layer-------
	  }
	}

  private boolean isNteModule() {
    int shelfIndex = -1;
    int slotIndex = -1;
    FDFrEndFSP150CM aEnd = fdfr.getAEnd();
    if (aEnd instanceof FDFrACCEndFSP150CM) {
      NETPortSPPropertiesFSP150CM props = ((FDFrACCEndFSP150CM)aEnd).getPort().getPortSPProperties();
      shelfIndex = props.get(NETPortSPPropertiesFSP150CM.VI.ShelfIndex); 
      slotIndex = props.get(NETPortSPPropertiesFSP150CM.VI.SlotIndex); 
      
    } else {
      FDFrEndFSP150CM zEnd = fdfr.getZEnd();
      if (zEnd instanceof FDFrACCEndFSP150CM) {
        NETPortSPPropertiesFSP150CM props = ((FDFrACCEndFSP150CM)zEnd).getPort().getPortSPProperties();
        shelfIndex = props.get(NETPortSPPropertiesFSP150CM.VI.ShelfIndex); 
        slotIndex = props.get(NETPortSPPropertiesFSP150CM.VI.SlotIndex); 
        
      }
    }
    if (shelfIndex == -1 || slotIndex == -1) {
      return false;
    }
    
    Module module = ((NetworkElementFSP150CMMTOSIOperations)fdfr.getNetworkElement().getMTOSIWorker()).getModule(shelfIndex,slotIndex);
    return module instanceof NteF3Impl;
  }

  
}
