/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.common.snmp.MIBFSP150CM;

public enum CMPortModeTranslation {
	CO_EPL         (MIBFSP150CM.Facility.EthernetAccPortTable.PORT_MODE_CO_TYPE, MIBFSP150CM.Facility.EthernetAccPortTable.SVC_TYPE_EPL, "CO-EPL"), //connection-oriented
	CO_EVPL        (MIBFSP150CM.Facility.EthernetAccPortTable.PORT_MODE_CO_TYPE, MIBFSP150CM.Facility.EthernetAccPortTable.SVC_TYPE_EVPL, "CO-EVPL"),//connection-oriented
	CL             (MIBFSP150CM.Facility.EthernetAccPortTable.PORT_MODE_CL_TYPE, 1, "CL"),     //connection-less
	NOT_APPLICABLE (3, 3, "n/a");

	//------------------------------------------------------------------------------------------------------------------
	private final int    portMode;
	private final int    portSvcType;
	private final String mtosiString;

	//------------------------------------------------------------------------------------------------------------------
	/**
	 * Constructor.
	 * @param portMode    The MIB defined value for port mode.
   * @param portSvcType The MIB defined value for port svc type.
	 * @param mtosiString  The string representation used in MTOSI layer.
	 */
	private CMPortModeTranslation(final int portMode, final int portSvcType, final String mtosiString)
	{
		this.portMode    = portMode; // CL, CO
		this.portSvcType = portSvcType; // EPL, EVPL
		this.mtosiString = mtosiString;
	}
	/**
	 * Returns the MIB defined value.
	 * @return the MIB defined value.
	 */
	public int getPortModeValue () {
		return portMode;
	}

	/**
	 * Returns the MIB defined value.
	 * @return the MIB defined value.
	 */
	public int getPortSvcTypeValue () {
		return portSvcType;
	}


	/**
	 * Returns the string representation used in MTOSI layer.
	 * @return the string representation used in MTOSI layer.
	 */
	public String getMtosiString() {
		return mtosiString;
	}

	/**
	 * Returns the string representation used in MTOSI layer.
   * @param portType The MIB defined value.
	 * @param portMode  The MIB defined value.
	 * @return the string representation used in MTOSI layer.
	 */
	public static String getMtosiString(final int portMode, final int portType)
	{
		CMPortModeTranslation portModeTranslation = NOT_APPLICABLE;  // the return value

		for (CMPortModeTranslation tmpPortModeTranslation : values())
		{
			if (portMode == tmpPortModeTranslation.getPortModeValue() && portType == tmpPortModeTranslation.getPortSvcTypeValue())
			{
				portModeTranslation = tmpPortModeTranslation;
				break;
			}
		}
		return portModeTranslation.getMtosiString();
	}
	/**
	 * Returns the string representation used in MTOSI layer.
	 * @param mtosiString  The MIB defined value
	 * @return the string representation used in MTOSI layer.
	 */
	public static int getPortModeValue (final String mtosiString)
	{
		CMPortModeTranslation portModeTranslation = NOT_APPLICABLE;  // the return value

		for (CMPortModeTranslation tmpPortModeTranslation : values())
		{
			if (mtosiString.equals(tmpPortModeTranslation.getMtosiString()))
			{
				portModeTranslation = tmpPortModeTranslation;
				break;
			}
		}
		return portModeTranslation.getPortModeValue();

	}
	/**
	 * Returns the string representation used in MTOSI layer.
	 * @param mtosiString  The MIB defined value
	 * @return the string representation used in MTOSI layer.
	 */
	public static int getPortTypeValue (final String mtosiString)
	{
		CMPortModeTranslation portModeTranslation = NOT_APPLICABLE;  // the return value

		for (CMPortModeTranslation tmpPortModeTranslation : values())
		{
			if (mtosiString.equals(tmpPortModeTranslation.getMtosiString()))
			{
				portModeTranslation = tmpPortModeTranslation;
				break;
			}
		}
		return portModeTranslation.getPortSvcTypeValue();
	}
}
