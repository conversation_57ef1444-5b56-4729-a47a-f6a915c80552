/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.serviceProvisioning.FTPSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ServiceSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ServiceSPPropertiesFSP150CP;
import com.adva.nlms.mediation.common.serviceProvisioning.WANPortSPPropertiesFSP150CP;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.FDFrEndFSP150CP;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.FDFrFSP150CP;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.FDFrPortEndFSP150CP;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.NetworkElementFSP150CP;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.PortFSP150CPAccess;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.PortFSP150CPNetwork;
import com.adva.nlms.mediation.config.mtosi.FDFr;
import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.DeactivateAndDeleteFDFrWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.TPDataListT;
import v1.tmf854.TPDataT;
import v1.tmf854ext.adva.DeactivateAndDeleteFDFrT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.ws.Holder;

public class DeactivateAndDeleteFDFrWorkerCP extends DeactivateAndDeleteFDFrWorker
{
	Logger LOG = LogManager.getLogger(this.getClass().getName());
	private PortFSP150CPAccess portACC;
	private FTP ftp;
	private PortFSP150CPNetwork portNETA;
	private PortFSP150CPNetwork portNETB;
	private TPDataT tpDataAccess;
	private TPDataT tpDataNetworkA;
	private TPDataT tpDataNetworkB;
	private TPDataT tpDataFtp;

	public DeactivateAndDeleteFDFrWorkerCP(DeactivateAndDeleteFDFrT mtosiBody, Holder<HeaderT> mtosiHeader, NetworkElement ne, FDFr fdfr) {
    super(mtosiBody, mtosiHeader, ne, fdfr);
	}

  @Override
  protected void response() throws Exception {
		v1.tmf854ext.adva.ObjectFactory factory = new v1.tmf854ext.adva.ObjectFactory();
		// have to get updated Ports that were in the request tpsToModify
		JAXBElement<TPDataListT> modifiedTPs = factory
				.createDeactivateAndDeleteFDFrResponseTTpsToModify(getUpdatedCPPorts());
		response.setTpsToModify(modifiedTPs);
	}

	private TPDataListT getUpdatedCPPorts() throws ProcessingFailureException
	{
		// Get updated objects
		ObjectFactory factory = new ObjectFactory();
		TPDataListT tpsToModify = factory.createTPDataListT();
		if (tpDataAccess != null && portACC != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataAccess.getTpName())).toMtosiPTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataAccess.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
		if (tpDataNetworkA != null && portNETA != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataNetworkA.getTpName())).toMtosiPTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataNetworkA.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
		if (tpDataNetworkB != null && portNETB != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataNetworkB.getTpName())).toMtosiPTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataNetworkB.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
		if (tpDataFtp != null && ftp != null) {
      tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  null).getMtosiTranslator(ManagedElementFactory.getFtp(tpDataFtp.getTpName())).toMtosiFTPasTPDataT());
//      tpsToModify.getTpData().add(ManagedElementFactory.getFtp(tpDataFtp.getTpName()).getMtosiTranslator().toMtosiFTPasTPDataT());
		}
		return tpsToModify;
	}

  private void transact(NetworkElementFSP150CP ne, ServiceSPPropertiesFSP150CP propsACC,
                        WANPortSPPropertiesFSP150CP propsNETA, WANPortSPPropertiesFSP150CP propsNETB, FTPSPProperties propsFTP,
                        String fdfrName)
          throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure, ProcessingFailureException {
    NetworkElement locks[] = new NetworkElement[]
            { ne };
    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "DeactivateAndDeleteFDFrWorker");
    ne.getMTOSIWorker().setFDFrOperationInProgress(fdfrName, true);
    try {
      if (portACC == null) {
        FDFrFSP150CP cpFDFr = (FDFrFSP150CP) fdfr;
        final FDFrEndFSP150CP aEnd = cpFDFr.getAEnd();
        final FDFrEndFSP150CP zEnd = cpFDFr.getZEnd();
        if (aEnd instanceof FDFrPortEndFSP150CP)
          portACC = (PortFSP150CPAccess) ((FDFrPortEndFSP150CP) aEnd).getPort();
        else if (zEnd instanceof FDFrPortEndFSP150CP)
          portACC = (PortFSP150CPAccess) ((FDFrPortEndFSP150CP) zEnd).getPort();
      }
      if (!ne.getMTOSIWorker().deleteFDFr(fdfrName)) {
        throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
                "The specified FDFr does not exist.");
      }
      else
      {
    	  logSecurity(ne, SystemAction.DeleteNetwork, fdfrName);
      }
      if (portACC != null && propsACC == null) {
        ServiceSPPropertiesFSP150CP tmpPropsACC = new ServiceSPPropertiesFSP150CP();
        tmpPropsACC.set(ServiceSPProperties.VI.SvcIndex, portACC.getIndex().toInt());
        tmpPropsACC.set(ServiceSPProperties.VS.CircuitName, "");
        portACC.setSettings(tmpPropsACC);
      }
      if (propsACC != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, portACC.getMtosiName());
        propsACC.set(ServiceSPProperties.VS.CircuitName, "");
        portACC.setSettings(propsACC);
      }
      if (propsNETA != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, portNETA.getMtosiName());
        portNETA.setSettings(propsNETA);
      }
      if (propsNETB != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, portNETB.getMtosiName());
        portNETB.setSettings(propsNETB);
      }
      if (propsFTP != null) {
        logSecurity(ne, SystemAction.ModifyNetwork, "ftpNm=" + ftp.getFTPName());
        ftp.setFTPSPProperties(propsFTP);
      }
      NetTransactionManager.commitNetTransaction(id);
      ne.logSROperation(SROperationState.SERVICE_DELETION_SUCCESS, fdfrName);
    } catch (NetTransactionException e) {
      ne.logSROperation(SROperationState.SERVICE_DELETION_FAILURE, fdfrName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ne.logSROperation(SROperationState.SERVICE_DELETION_FAILURE, fdfrName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SNMPCommFailure e) {
      ne.logSROperation(SROperationState.SERVICE_DELETION_FAILURE, fdfrName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (ProcessingFailureException e) {
      ne.logSROperation(SROperationState.SERVICE_DELETION_FAILURE, fdfrName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally {
      ne.getMTOSIWorker().setFDFrOperationInProgress(fdfrName, false);
      NetTransactionManager.ensureEnd(id);
    }
  }

	@Override
  protected void mediate() throws Exception {
		ServiceSPPropertiesFSP150CP propsACC = null;
		WANPortSPPropertiesFSP150CP propsNETA = null;
		WANPortSPPropertiesFSP150CP propsNETB = null;
		FTPSPProperties propsFTP = null;
		if (tpsToModify != null)
		{
			if (!MtosiTPMediator.checkTPToModifySameNE(ne, tpsToModify))
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(),
						ExceptionUtils.EXCPT_INVALID_INPUT, "The specified TPs must be on the same Network Element.");
        throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
			tpDataAccess = MtosiTPMediator.getTPDataTForCPAccess(tpsToModify);
			tpDataNetworkA = MtosiTPMediator.getTPDataTForCPNetworkA(tpsToModify);
			tpDataNetworkB = MtosiTPMediator.getTPDataTForCPNetworkB(tpsToModify);
			tpDataFtp = MtosiTPMediator.getTPDataTForFTP(tpsToModify);
		}
		if (tpDataAccess != null)
		{
			Port portAccess = ManagedElementFactory.getPort(tpDataAccess.getTpName());
			portACC = (PortFSP150CPAccess) portAccess;
			propsACC = MtosiTPMediator.mtosiTPDataTToCPAccessProperties(tpDataAccess, portACC);
		}
		if (tpDataNetworkA != null)
		{
			Port portA = ManagedElementFactory.getPort(tpDataNetworkA.getTpName());
			portNETA = (PortFSP150CPNetwork) portA;
			propsNETA = MtosiTPMediator.mtosiTPDataTToCPNetworkProperties(tpDataNetworkA, portNETA);
		}
		if (tpDataNetworkB != null)
		{
			Port portB = ManagedElementFactory.getPort(tpDataNetworkB.getTpName());
			portNETB = (PortFSP150CPNetwork) portB;
			propsNETB = MtosiTPMediator.mtosiTPDataTToCPNetworkProperties(tpDataNetworkB, portNETB);
		}
		if (tpDataFtp != null)
		{
			ftp = ManagedElementFactory.getFtp(tpDataFtp.getTpName());
			propsFTP = MtosiTPMediator.mtosiTPDataTToCPFTPProperties(tpDataFtp, ftp);
		}
		NetworkElementFSP150CP cpNE = (NetworkElementFSP150CP) ne;
		String fdfrName = namingAttributes.getFdfrNm();
		transact(cpNE, propsACC, propsNETA, propsNETB, propsFTP, fdfrName);
	}
}