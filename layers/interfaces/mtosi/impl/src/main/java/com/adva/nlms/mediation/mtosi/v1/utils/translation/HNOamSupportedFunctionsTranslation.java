/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
 /**
  *
  *   dot3OamFunctionsSupported OBJECT-TYPE
     SYNTAX      BITS {
                   unidirectionalSupport (0),
                   loopbackSupport(1),
                   eventSupport(2),
                   variableSupport(3)
                 }

  */
public enum HNOamSupportedFunctionsTranslation {
    UnidirectionalSupport 	(1, "UnidirectionalSupport"),
    LoopbackSupport			(2, "LoopbackSupport"),
    EventSupport			(4, "EventSupport"),
    VariableSupport			(8, "VariableSupport");
    
    private int mibValue;
    private String mtosiValue;
    
    private int getMibValue() {
		return mibValue;
	}

	private HNOamSupportedFunctionsTranslation(int code, String mtosiValue) {
    	this.mibValue = code;
    	this.mtosiValue = mtosiValue;
    	
    }

    public static String getMtosiString(final String mibValueString) {
    	if (mibValueString == null) {
			return MtosiConstants.NOT_APPLICABLE;
    	}
    	try {
			int mibValue = Integer.parseInt(mibValueString);
			StringBuilder result= new StringBuilder();
			for (HNOamSupportedFunctionsTranslation value: values() ) {
				if ((value.getMibValue() & mibValue) > 0) {
					result.append(result.length() > 0 ? ", ":"").append(value.mtosiValue);
				}
			}
			return result.toString();
		} catch (NumberFormatException e) {
			return MtosiConstants.NOT_APPLICABLE;
		}
    }
}
