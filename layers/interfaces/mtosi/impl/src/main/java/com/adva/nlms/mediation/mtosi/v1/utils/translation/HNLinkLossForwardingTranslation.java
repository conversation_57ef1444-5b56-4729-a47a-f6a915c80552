/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;

/**
  *
  */
public enum HNLinkLossForwardingTranslation {
    None  		(0,"None"),
    ETH1   		(1,"ETH-1"),
    ETH2        (2,"ETH-2"),
    ETH1001		(1001,"ETH-1001");
    
    private int mibValue;
    private String mtosiString;
    
    private int getMibValue() {
		return mibValue;
	}

	private HNLinkLossForwardingTranslation(int code,String mtosiString) {
    	this.mibValue = code;
    	this.mtosiString = mtosiString;
    }

    public static String getMtosiString(final Integer mibValue) {
    	if (mibValue == null) {
    		return MtosiConstants.NOT_APPLICABLE;
    	}
    	for (HNLinkLossForwardingTranslation value: values() ) {
    		if (value.getMibValue() == mibValue.intValue()) {
    			return value.mtosiString; 
    		}
    	}
    	//the value was not found, return the value passed in
    	return String.valueOf(mibValue);
    }
    
    public static int getMibValue(final String name) {
    	for (HNLinkLossForwardingTranslation value: values() ) {
    		if (value.mtosiString.equals(name)) {
    			return value.getMibValue(); 
    		}
    	}
    	//Returning -1, the value passed in was not found.
    	return -1;
    }
}
