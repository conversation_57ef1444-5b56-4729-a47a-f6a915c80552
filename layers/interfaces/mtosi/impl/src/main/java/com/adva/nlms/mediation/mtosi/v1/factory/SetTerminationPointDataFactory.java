/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.factory;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.v1.adapter.facade.MtosiTerminationPointEFMDTOImpl;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v2.adapter.facade.response.ResponseFactory;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.TPDataT;
import ws.v1.tmf854.ProcessingFailureException;

public class SetTerminationPointDataFactory extends ResponseFactory {

  private static Logger logger = LogManager.getLogger(SetTerminationPointDataFactory.class);

  public MtosiTerminationPointEFMDTOImpl parse(TPDataT ptDataType, MtosiMOFacade facade) throws ProcessingFailureException {
//    Map<String,Object> objectMap=preParse(ptDataType);
    MtosiAddress mtosiAddress = new MtosiAddress(ptDataType.getTpName());
    int neType = mtosiAddress.getNeType();//ptDataType.getTpName((NetworkElement)objectMap.get("networkElement")).getNetworkElementType();
    switch (neType) {
      case NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
        return processRequestForCM(ptDataType, mtosiAddress, facade);

      default:
        return null;
    }
  }

  public MtosiTerminationPointEFMDTOImpl processRequestForCM(TPDataT tpData,  MtosiAddress mtosiAddress, MtosiMOFacade facade) throws ProcessingFailureException {
    MtosiTerminationPointEFMDTOImpl mtosiTerminationPointDTO = new MtosiTerminationPointEFMDTOImpl(facade);
    parseBaseParameters(mtosiAddress, mtosiTerminationPointDTO);
    mtosiTerminationPointDTO.parse(tpData);
    return mtosiTerminationPointDTO;
  }

  public void parseBaseParameters(MtosiAddress mtosiAddress, MtosiTerminationPointEFMDTOImpl terminationPointDTO) {
    debug("parsing base parameters started");
    terminationPointDTO.setNetworkElement(mtosiAddress.getNE());
    terminationPointDTO.setTpName(mtosiAddress.getNaming().getPtpNm());
    terminationPointDTO.setMeName(mtosiAddress.getNaming().getMeNm());
    terminationPointDTO.setMdName(mtosiAddress.getNaming().getMdNm());
    debug("parsing base parameters completed");
  }



  private void debug(String message) {
    if (logger.isDebugEnabled()) {
      logger.debug(message);
    }
  }
}
