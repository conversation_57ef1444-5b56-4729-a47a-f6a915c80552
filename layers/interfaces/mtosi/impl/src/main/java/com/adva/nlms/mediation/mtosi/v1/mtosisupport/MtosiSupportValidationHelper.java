/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.mtosisupport;

import com.adva.common.util.Resources;
import com.adva.nlms.common.property.FNMPropertyConstants;
import com.adva.nlms.common.property.FNMPropertyFactory;
import com.adva.nlms.common.mtosisupport.MTOSIOperation;
import com.adva.nlms.common.mtosisupport.MTOSISupport;
import com.adva.nlms.common.mtosisupport.NetworkElement;
import com.adva.nlms.common.mtosisupport.types.SupportedMTOSIVersions;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.exolab.castor.xml.Unmarshaller;
import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.ProcessingFailureException;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * This class reads the an xml file specified in fnm.properties,
 * using this key: "com.adva.nlms.mediation.mtosi.MTOSISupport.File"
 * 
 * If this parameter is not present, the default mtosisupport.xml is used.
 * This file identifies which mtosi operations are valid for a given NE Type.
 * 
 * The NetworkElement type "FSP NM" can be used for operations that do not refer to a 
 * Network Element, or are available for ALL Network Elements.
 * 	<NetworkElement type="FSP NM">
		<MTOSIOperation name="getAllCapabilities" supported="true"/>
		<MTOSIOperation name="getAllMDNames" supported="true"/>
		<MTOSIOperation name="getAllMDs" supported="true"/>
		<MTOSIOperation name="getAllMENamesPassingFilter"  supported="true"/>
		<MTOSIOperation name="getAllMENamesWrtOS" supported="true"/>
		<MTOSIOperation name="getAllOSNames" supported="true"/>
		<MTOSIOperation name="getAllOSs" supported="true"/>
	</NetworkElement>

 * 
 * 
 * <AUTHOR>
 * 
 */
public class MtosiSupportValidationHelper {

	/**
	 * MTOSISupport and a number of other classes are generated from mtosisupport.xsd.
	 * A full ant build should resolve any compile issues.
	 * A quick fix, would be to run "compile.mtosisupport" task from the ../mediation/build.xml.
	 */
	private MTOSISupport mtosiSupport = null;
	private HashMap<String, List<String>> validationMap;
	private boolean missingFile = false;
	private boolean invalidFile = false;
	  /** Log4j logger   */
	  static Logger logger = LogManager.getLogger(MtosiSupportValidationHelper.class.getPackage().getName());
	
	private static MtosiSupportValidationHelper instance = null;

	public static MtosiSupportValidationHelper getInstance() {

		if (instance == null) {
			instance = new MtosiSupportValidationHelper();
		}
		return instance;

	}

	private MtosiSupportValidationHelper() {
		mtosiSupport = loadXml(FNMPropertyFactory.getProperty(FNMPropertyConstants.MTOSI_SUPPORT_XML_FILE, null));
		prepareValidator();
	}

	public static MtosiSupportValidator getValidator(String mtosiOperation) throws ProcessingFailureException {
		
		return getInstance().createValidator(mtosiOperation);
	}

	/**
	 * Boolean check to see if the system is configured to use the MTOSI createManagedElement requests.
	 * @return
	 */
	public static boolean canCreateManagedElements() {
		
		return getInstance().hasOperation("createManagedElement");
	}
	/**
	 * Create an MtosiValidor for a given mtosiRequest.
	 * This method will throw a PFE if the server could not parse the XML file,
	 * or, the specified request is not available for ANY NE type.
	 * This method can be called before the Worker is instantiated to weed out the unsupported methods.
	 * 
	 * @throws ProcessingFailureException if the request is not supported for ANY NE type.
	 */
	private MtosiSupportValidator createValidator(String mtosiRequest) throws ProcessingFailureException {
		if (missingFile) {
			pfe(ExceptionUtils.EXCPT_INTERNAL_ERROR, MtosiErrorConstants.MTOSI_OPERATION_NOT_SUPPORTED_XML_WAS_NOT_FOUND);
		}
		if (invalidFile || validationMap == null) {
			pfe(ExceptionUtils.EXCPT_INTERNAL_ERROR, MtosiErrorConstants.MTOSI_OPERATION_NOT_SUPPORTED_XML_IS_INVALID);
		}
		List<String> neList = validationMap.get(mtosiRequest);
		
		if (neList == null || neList.isEmpty()) {
			pfe(ExceptionUtils.EXCPT_NOT_IMPLEMENTED, MtosiErrorConstants.MTOSI_OPERATION_NOT_SUPPORTED);
		}
		return new MtosiSupportValidator(mtosiRequest,neList);
	}


	private boolean hasOperation(String operation) {
		if (validationMap == null) {
			return false;
		}
		List<String> neList = validationMap.get(operation);
		if (neList == null || neList.isEmpty())
			return false;
		return true;
	}

	private void pfe( String type,String reason) throws ProcessingFailureException {
		ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(type,reason);
		ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
		throw pfe;
	}
	

	/**
	 * This method takes the processed XML file and massages it into a validator
	 * that can be used to validate Mtosi requests. The validator will determine
	 * it this server is allowed to process a given Mtosi request for a given NE
	 * type.
	 * 
	 */
	private void prepareValidator() {

        validationMap = new HashMap<String, List<String>>();
        if (mtosiSupport != null) {
            for (NetworkElement ne : mtosiSupport.getNetworkElement()) {
                if (ne.getVersion().equals(SupportedMTOSIVersions.MTOSIV1) || ne.getVersion().equals(SupportedMTOSIVersions.MTOSIVALL)) {
                    String neName = ne.getType().toString();
                    for (MTOSIOperation operation : ne.getMTOSIOperation()) {
                        String operationName = operation.getName().toString();
                        List<String> neList = validationMap.get(operationName);
                        if (neList == null) {
                            neList = new ArrayList<String>();
                            validationMap.put(operationName, neList);
                        }
                        neList.add(neName);
                    }
                }
            }
        }
    }
	/**
	 * Load the specified xml file.
	 * This file must validate to the mtosisupport.xsd.
	 * If the filename is not specified, the default "mtosisupport.xml" is used.
	 * @param filename
	 * @return an MTOSISupport Document structure (created by Castor).
	 */

	private MTOSISupport loadXml(String filename) {
		InputStream inputStream = null;
		try {
		if (filename != null) {
//			inputStream = new FileInputStream(filename);
			inputStream = Resources.getStream(filename);
		} else {
//			inputStream = new FileInputStream("mtosisupport.xml");
	//		inputStream = Resources.getStream("../../common/mtosisupport/mtosisupport.xml");

       inputStream = Resources.getStream("/com/adva/nlms/mediation/mtosi/common/mtosisupport/mtosisupport.xml");
		}
		} catch (Exception e) {
			missingFile = true;
			logger.warn("mtosi support xml file not found: "+e);
			return null;
		}
		if (inputStream == null) {
			missingFile = true;
			logger.warn("mtosi support xml file not found.");
			return null;
		}
		return (MTOSISupport) readObject(inputStream, MTOSISupport.class);

	}

	private Object readObject(InputStream inputStream, final Class clazz) {
		try {

			InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "UTF-8");
			Unmarshaller unmarshaller = new Unmarshaller(clazz);

			unmarshaller.setValidation(false);

			return unmarshaller.unmarshal(inputStreamReader);
		} catch (Exception e) {
			invalidFile = true;
			logger.warn("Invalid mtosisupport File, "+e);
		}

		return null;
	}

	public String toString() {
		if (validationMap == null || validationMap.isEmpty()) {
			return "No validation map. File Not Found: "+missingFile+ " File Invalid: "+invalidFile;
		}
		return this.validationMap.toString();
	}
}
