/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */

package com.adva.nlms.mediation.mtosi.v1.mediation;

import com.adva.nlms.mediation.config.f3.entity.flow.FlowF3Impl;
import com.adva.nlms.mediation.config.f3.entity.flow.MTOSIFlowF3;
import com.adva.nlms.mediation.config.f3.entity.policer.qospolicer.QOSFlowPolicerImpl;
import com.adva.nlms.mediation.config.f3.entity.port.acc.MTOSIPortF3Acc;
import com.adva.nlms.mediation.config.f3.entity.port.acc.PortF3AccImpl;
import com.adva.nlms.mediation.config.f3.entity.port.bits.PortBitsF3Impl;
import com.adva.nlms.mediation.config.f3.entity.port.net.MTOSIPortF3Net;
import com.adva.nlms.mediation.config.f3.entity.port.net.PortF3NetImpl;
import com.adva.nlms.mediation.config.f3.entity.shaper.qosshaper.QOSShaperF3;
import com.adva.nlms.mediation.config.f3.entity.shaper.qosshaper.QOSShaperF3Impl;
import com.adva.nlms.mediation.config.f3.entity.sync.F3SyncImpl;
import com.adva.nlms.mediation.config.f3.mtosi.NetworkElementF3MTOSIOperations;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FDFrFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.FTPFSP150CM;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.model.FDFrFSP150CMImpl;
import com.adva.nlms.mediation.config.fsp150cm.mtosi.model.FTPFSP150CMImpl;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.FDFrFSP150CP;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.FDFrFSP150CPImpl;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.FTPFSP150CPImpl;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.PortFSP150CPAccess;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.PortFSP150CPAccessImpl;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.PortFSP150CPNetwork;
import com.adva.nlms.mediation.config.fsp150cp_mx.fsp150cp.PortFSP150CPNetworkImpl;
import com.adva.nlms.mediation.config.hn4000.FlowHN4000;
import com.adva.nlms.mediation.config.hn4000.FlowHN4000Impl;
import com.adva.nlms.mediation.config.hn4000.FlowHN4xxImpl;
import com.adva.nlms.mediation.config.hn4000.LAGHN4000Impl;
import com.adva.nlms.mediation.config.hn4000.PolicerHN4000;
import com.adva.nlms.mediation.config.hn4000.PolicerHN4000Impl;
import com.adva.nlms.mediation.config.hn4000.PortHN40002Bpme;
import com.adva.nlms.mediation.config.hn4000.PortHN40002BpmeImpl;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet2BASE_TL;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet2BASE_TLImpl;
import com.adva.nlms.mediation.config.hn4000.PortHN4000EthernetImpl;
import com.adva.nlms.mediation.config.hn4000.ShaperHN4000;
import com.adva.nlms.mediation.config.hn4000.ShaperHN4000Impl;
import com.adva.nlms.mediation.config.hn4000.mtosi.FDFrHN4000;
import com.adva.nlms.mediation.config.hn4000.mtosi.model.FDFrHN4000Impl;
import com.adva.nlms.mediation.config.mtosi.FTP;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm.FDFrFSP150CMTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm.FTPFSP150CMTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm.FlowFSP150CMTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm.PolicerFSP150CMTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm.PortFSP150CMAccTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm.PortFSP150CMNetTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm.ShaperFSP150CMTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm.SyncEFSP150CMTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cp.FDFrFSP150CPTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cp.FTPFSP150CPTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cp.PortFSP150CPAccessTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cp.PortFSP150CPNetworkTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp20X.PortFSP150GE20XBitsTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.hn4000.FDFrHN4000Translator;
import com.adva.nlms.mediation.mtosi.v1.mediation.hn4000.FTPHN4000Translator;
import com.adva.nlms.mediation.mtosi.v1.mediation.hn4000.FlowHN4000Translator;
import com.adva.nlms.mediation.mtosi.v1.mediation.hn4000.PolicerHN400Translator;
import com.adva.nlms.mediation.mtosi.v1.mediation.hn4000.PortHN40002BpmeTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.hn4000.PortHN4000Ethernet2BaseTLTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.hn4000.PortHN4000EthernetTranslator;
import com.adva.nlms.mediation.mtosi.v1.mediation.hn4000.ShaperHN400Translator;

public enum MtosiTranslatorMap{
  F3SyncEnum(F3SyncImpl.class,SyncEFSP150CMTranslator.class, false,F3SyncImpl.class),
  FDFrFSP150CMEnum(FDFrFSP150CMImpl.class,FDFrFSP150CMTranslator.class, false,FDFrFSP150CM.class),
  FDFrFSP150CPEnum(FDFrFSP150CPImpl.class,FDFrFSP150CPTranslator.class, false,FDFrFSP150CP.class),
  FDFrHN4000Enum(FDFrHN4000Impl.class,FDFrHN4000Translator.class, false,FDFrHN4000.class),
  FTPFSP150CMEnum(FTPFSP150CMImpl.class,FTPFSP150CMTranslator.class, false,FTPFSP150CM.class),
  FTPFSP150CPEnum(FTPFSP150CPImpl.class,FTPFSP150CPTranslator.class, false,FTPFSP150CPImpl.class),
  FlowF3Enum(FlowF3Impl.class,FlowFSP150CMTranslator.class, false,MTOSIFlowF3.class),
  FlowHN4000Enum(FlowHN4000Impl.class,FlowHN4000Translator.class, false,FlowHN4000.class),
  FlowHN4xxEnum(FlowHN4xxImpl.class,FlowHN4000Translator.class, false,FlowHN4000.class),
  LAGHN4000Enum(LAGHN4000Impl.class,FTPHN4000Translator.class, false,FTP.class),
  PolicerHN4000Enum(PolicerHN4000Impl.class,PolicerHN400Translator.class, false,PolicerHN4000.class),
  PortBitsF3Enum(PortBitsF3Impl.class,PortFSP150GE20XBitsTranslator.class, false,PortBitsF3Impl.class),
  PortF3AccEnum(PortF3AccImpl.class,PortFSP150CMAccTranslator.class, false,MTOSIPortF3Acc.class),
  PortFSP150CPAccessEnum(PortFSP150CPAccessImpl.class,PortFSP150CPAccessTranslator.class, false,PortFSP150CPAccess.class),
  PortFSP150CPNetworkEnum(PortFSP150CPNetworkImpl.class,PortFSP150CPNetworkTranslator.class, false,PortFSP150CPNetwork.class),
  PortF3NetEnum(PortF3NetImpl.class,PortFSP150CMNetTranslator.class, true,MTOSIPortF3Net.class, NetworkElementF3MTOSIOperations.class),
  PortHN40002BpmeEnum(PortHN40002BpmeImpl.class,PortHN40002BpmeTranslator.class, false,PortHN40002Bpme.class),
  PortHN4000Ethernet2BASE_TLEnum(PortHN4000Ethernet2BASE_TLImpl.class,PortHN4000Ethernet2BaseTLTranslator.class, false,PortHN4000Ethernet2BASE_TL.class),
  PortHN4000EthernetEnum(PortHN4000EthernetImpl.class,PortHN4000EthernetTranslator.class, false,PortHN4000Ethernet.class),
  QOSShaperF3Enum(QOSShaperF3Impl.class,ShaperFSP150CMTranslator.class, false,QOSShaperF3.class),
  ShaperHN4000Enum(ShaperHN4000Impl.class,ShaperHN400Translator.class, false,ShaperHN4000.class),
  QOSFlowPolicerEnum(QOSFlowPolicerImpl.class,PolicerFSP150CMTranslator.class, false,QOSFlowPolicerImpl.class);

  private Class entityClass;
  private Class translatorClass;
  private boolean extendsSuperClass;
  private Class[] classes;

  MtosiTranslatorMap(Class entityClass, Class translatorClass, boolean extendsSuperClass, Class... classes ){
    this.entityClass = entityClass;
    this.translatorClass = translatorClass;
    this.extendsSuperClass = extendsSuperClass;
    this.classes = classes;
  }

  public boolean isNeedsMtosiWorker() {
    return classes.length == 2;
  }

  public Class getEntityClass() {
    return entityClass;
  }

  public Class getTranslatorClass() {
    return translatorClass;
  }

  public Class[] getClasses() {
    return classes;
  }
}