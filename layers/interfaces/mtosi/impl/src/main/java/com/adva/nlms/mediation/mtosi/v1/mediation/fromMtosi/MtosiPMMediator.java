/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi;

import com.adva.nlms.common.performance.types.Location;
import com.adva.nlms.common.performance.types.PerformanDataType;
import com.adva.nlms.common.performance.types.TimeType;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854ext.adva.GranularityListT;
import v1.tmf854ext.adva.PMLocationListT;
import v1.tmf854ext.adva.PMParameterNameListT;
import ws.v1.tmf854.ProcessingFailureException;

import java.util.ArrayList;
import java.util.Iterator;

public class MtosiPMMediator
{

	public static final String GRANULARITY_15 = "15min";
	public static final String GRANULARITY_24 = "24h";
	public static final String GRANULARITY_NA = "NA";

	public static ArrayList<PerformanDataType> mtosiParamsToNMSParams(PMParameterNameListT list) throws Exception
	{
		
		
		ArrayList<PerformanDataType> typeList = new ArrayList<PerformanDataType>();
		
		if(list==null) return typeList;
		for (Iterator iter = list.getPmparam().iterator(); iter.hasNext();)
		{
			String nextParameter = (String) iter.next();
			// valueOf will not work, since that needs AdvaLongName
			// so we need way of lookup via MtosiShortName
			PerformanDataType nextType = PerformanDataType.getPerformanDataTypeByMTOSIShortName(nextParameter);
			if (nextType != null)
			{
				typeList.add(nextType);
			}
			else
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
						"Specified pmParameterName is not valid.");
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
			}

		}

		return typeList;

	}

	public static ArrayList<Location> mtosiLocationListToNMSLocation(PMLocationListT list) throws Exception
	{
		ArrayList<Location> locationList = new ArrayList<Location>();
		if(list==null) return null;
		for (Iterator iter = list.getPmlocation().iterator(); iter.hasNext();)
		{
			String nextMtosiLocation = (String) iter.next();
			Location nextLocation = MtosiPMMediator.mtosiLocationToNMSLocation(nextMtosiLocation);
			if (nextLocation != null)
			{
				locationList.add(nextLocation);
			}
			else
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
						"Specified pmLocation is not valid.");
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
			}

		}

		return locationList;
	}

	public static Location mtosiLocationToNMSLocation(String location)
	{
		if (location.equals(Location.PML_NEAR_END_RX.toString()))
		{
			return Location.PML_NEAR_END_RX;
		}
		else if (location.equals(Location.PML_NEAR_END_TX.toString()))
		{
			return Location.PML_NEAR_END_TX;
		}
		else if (location.equals(Location.PML_BIDIRECTIONAL.toString()))
		{
			return Location.PML_BIDIRECTIONAL;
		}
		else
		{
			return null;
		}
	}

    /**
     *
     * @param list
     * @param isHistorical <code>true</code> if the method is called for getHistoricalPMData(), otherwise <code>false</code>
     * @return
     * @throws Exception
     */
	public static ArrayList<TimeType> mtosiGranularityListToTimeTypes(GranularityListT list, boolean isHistorical) throws Exception
	{
		ArrayList<TimeType> granList = new ArrayList<TimeType>();
		if(list==null || list.getGranularity().isEmpty()) return null;

		for (Iterator iter = list.getGranularity().iterator(); iter.hasNext();)
		{
			String nextGran = (String) iter.next();
			TimeType nextBin = MtosiPMMediator.mtosiGranularityToNMSTimeType(nextGran, isHistorical);
			if (nextBin != null)
			{
				granList.add(nextBin);
			}
			else
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_INVALID_INPUT,
						"Specified Granularity is not valid.");
				ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
				throw pfe;
			}

		}

		return granList;

	}

	public static TimeType mtosiGranularityToNMSTimeType(String granularity, boolean isHistorical)
	{
		if (granularity.equals(GRANULARITY_15))
		{
			return isHistorical ? TimeType._15_MIN : TimeType.CURRENT_15_MIN;
		}
		else if (granularity.equals(GRANULARITY_24))
		{
			return isHistorical ? TimeType._24_H : TimeType.CURRENT_24_H;
		}
		else if (granularity.equals(GRANULARITY_NA))
		{
			return isHistorical ? TimeType.ROLLOVER : TimeType.CURRENT_TOTAL;
		}
		else
		{
			return null;
		}
	}

}
