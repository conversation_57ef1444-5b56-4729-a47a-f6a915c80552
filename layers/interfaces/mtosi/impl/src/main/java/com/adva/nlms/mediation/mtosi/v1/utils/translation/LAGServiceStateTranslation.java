/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum LAGServiceStateTranslation
{
  LACP_ACTIVITY     (1, "LACP Active, " , "LACP Passive, "),
  LACP_TIMEOUT      (2, "LACP Short, " , "LACP Long, "),
  AGGREGATION       (4, "Aggregation, " , "No Aggregation, "),
  SYNCHRONIZATION   (8, "Synchronization, " , "No Synchronization, "),
  COLLECTING        (16, "Collecting, " , "Not Collecting, "),
  DISTRIBUTING      (32, "Distributing, " , "Not Distributing, "),
  DEFAULTED         (64, "Defaulted, " , "Not Defaulted, "),
  EXPIRED           (128, "Expired" , "Not Expired");

  /**
   * Constructor.
   * @param bitValue   The bit value of the parameter
   * @param onText     Text displayed when parameter is enabled.
   * @param offText    Text displayed when parameter is disabled.
   */
  private LAGServiceStateTranslation (final int bitValue, final String onText, final String offText)
  {
    this.bitValue = bitValue;
    this.onText = onText;
    this.offText = offText;
  }

  /**
   * Returns the string representation used in ManagedElementMediator class.
   * @param operState  The value of operational state.
   * @return the string representation used in ManagedElementMediator class.
   */
  public static String getMtosiString(final int operState)
  {
    StringBuilder returnValue = new StringBuilder();
    for (LAGServiceStateTranslation param : values()) {
      returnValue.append(((operState & param.bitValue) == param.bitValue) ? param.onText : param.offText);
    }
    return returnValue.toString();
  }

  private final int    bitValue;
  private final String onText;
  private final String offText;
}
