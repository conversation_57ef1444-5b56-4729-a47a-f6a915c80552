/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import java.util.HashMap;
import java.util.Map;

public enum BooleanTypeTranslation implements TranslatableEnum {
  UNKNOWN(0,"n/a"),
  ENABLED        (1, "True"),
  DISABLED       (2, "False"),
  NOT_APPLICABLE (3, "n/a");

  private final int    mibValue;
  private final String mtosiString;
  private static final Map<String, BooleanTypeTranslation> stringMap;

  static {
    stringMap = new HashMap<>();
    for (BooleanTypeTranslation v : values()) {
      stringMap.put(String.valueOf(v.mtosiString), v);
      stringMap.put(String.valueOf(v.mibValue), v);
    }
  }

  private BooleanTypeTranslation (final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }

  public static BooleanTypeTranslation valueOfString(String label) {
    if (label==null) return UNKNOWN;
    return stringMap.get(label);
  }

  public static BooleanTypeTranslation valueOfLabel(String label) {
    BooleanTypeTranslation enumType = UNKNOWN;
    for (BooleanTypeTranslation tmpEnumType : values()) {
      if (label.equals(tmpEnumType.getLabel())) {
        enumType = tmpEnumType;
        break;
      }
    }
    return enumType;
  }

  public static String valueFromBoolean(Boolean value) {
    if (value) {
      return BooleanTypeTranslation.ENABLED.getLabel();
    }else{
      return BooleanTypeTranslation.DISABLED.getLabel();
    }
  }


  public static BooleanTypeTranslation valueOfId(int id) {
    return stringMap.get(String.valueOf(id));
  }

  public static BooleanTypeTranslation valueOfBoolean(boolean flag) {
    return flag?ENABLED:DISABLED;
  }

  public String getLabel() {
    return mtosiString;
  }

  public int getId() {
    return mibValue;
  }

  public Boolean getBooleanValue() {
    return mibValue == 1;
  }
}