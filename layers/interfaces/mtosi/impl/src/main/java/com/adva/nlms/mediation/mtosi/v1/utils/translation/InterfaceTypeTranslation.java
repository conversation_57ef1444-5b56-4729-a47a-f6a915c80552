/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

/**
 * Created by IntelliJ IDEA. User: Lukasz Date: 2007-05-30 Time: 13:14:09 To change this template use File | Settings |
 * File Templates.
 */
public enum InterfaceTypeTranslation{
  ASSIGNED       (1, "UNI", "fdEdge", "Assigned"),
  UNASSIGNED     (2, "Unconfigured", "unassigned", "Unassigned"),
  NOT_APPLICABLE (3, "n/a", "n/a", "n/a");

  //------------------------------------------------------------------------------------------------------------------
  private final int    assignValue;
  private final String mtosiString;
  private final String portTPRoleState;
  private final String assignedState;

  //------------------------------------------------------------------------------------------------------------------
  /**
   * Constructor.
   * @param assignValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private InterfaceTypeTranslation (final int assignValue, final String mtosiString, final String portTPRoleState, final String assignedState)
  {
    this.assignValue     = assignValue;
    this.mtosiString     = mtosiString;
    this.portTPRoleState = portTPRoleState;
    this.assignedState   = assignedState;
  }
  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getAssignValue () {
    return assignValue;
  }


  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation of PortTPRoleStateused.
   * @return the string representation of PortTPRoleStateused.
   */
  public String getPortTPRoleState() {
    return portTPRoleState;
  }


  /**
   * Returns the string representation of AssignedState.
   * @return the string representation of AssignedState.
   */
  public String getAssignedState() {
    return assignedState;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param assignValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int assignValue)
  {
    InterfaceTypeTranslation interfaceTypeTranslation = NOT_APPLICABLE;  // the return value

    for (InterfaceTypeTranslation tmpInterfaceTypeTranslation : values())
    {
      if (assignValue == tmpInterfaceTypeTranslation.getAssignValue())
      {
        interfaceTypeTranslation = tmpInterfaceTypeTranslation;
        break;
      }
    }
    return interfaceTypeTranslation.getMtosiString();
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param assignValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getPortTPRoleState(final int assignValue)
  {
    InterfaceTypeTranslation interfaceTypeTranslation = NOT_APPLICABLE;  // the return value

    for (InterfaceTypeTranslation tmpInterfaceTypeTranslation : values())
    {
      if (assignValue == tmpInterfaceTypeTranslation.getAssignValue())
      {
        interfaceTypeTranslation = tmpInterfaceTypeTranslation;
        break;
      }
    }
    return interfaceTypeTranslation.getPortTPRoleState();
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param assignValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getAssignedState(final int assignValue)
  {
    InterfaceTypeTranslation interfaceTypeTranslation = NOT_APPLICABLE;  // the return value

    for (InterfaceTypeTranslation tmpInterfaceTypeTranslation : values())
    {
      if (assignValue == tmpInterfaceTypeTranslation.getAssignValue())
      {
        interfaceTypeTranslation = tmpInterfaceTypeTranslation;
        break;
      }
    }
    return interfaceTypeTranslation.getAssignedState();
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mtosiString  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static int getAssignValue (final String mtosiString)
  {
    InterfaceTypeTranslation interfaceTypeTranslation = NOT_APPLICABLE;  // the return value

    for (InterfaceTypeTranslation tmpInterfaceTypeTranslation : values())
    {
      if (mtosiString.equals(tmpInterfaceTypeTranslation.getAssignedState()))
      {
        interfaceTypeTranslation = tmpInterfaceTypeTranslation;
        break;
      }
    }
    return interfaceTypeTranslation.getAssignValue();
  }
}
