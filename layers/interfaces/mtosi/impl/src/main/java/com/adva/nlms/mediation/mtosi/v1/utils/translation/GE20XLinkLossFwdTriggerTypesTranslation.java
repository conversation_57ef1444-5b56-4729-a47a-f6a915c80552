/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

import v1.tmf854.ProcessingFailureExceptionT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;

public enum GE20XLinkLossFwdTriggerTypesTranslation {
	  None 		(0, ""),
	  EFM_RLD 	(2, "EfmRld"),
	  LINK_DOWN (4, "LinkDown");

  private final int bitValue;
  private final String mtosiText;

  private GE20XLinkLossFwdTriggerTypesTranslation(final int bitValue, final String mtosiText) {
    this.bitValue = bitValue;
    this.mtosiText = mtosiText;
  }

  public int getBitValue () {
    return bitValue;
  }

  public String getMtosiText () {
    return mtosiText;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final byte[] mibValue) {
    String toReturn = "";

    if ((mibValue[0] & (EFM_RLD.getBitValue() << 5)) != 0) {
      toReturn += EFM_RLD.getMtosiText();
    }

    if ((mibValue[0] & (LINK_DOWN.getBitValue() << 3)) != 0) {
      if (toReturn.length() > 0) {
        toReturn += ",";
      }
      toReturn += LINK_DOWN.getMtosiText();
    }

    return toReturn;
  }

  /**
   * Returns the byte array representing given string value.
   * @param mtosiString String value to parse.
   * @return byte array
   * @throws ProcessingFailureException when string value is invalid
   */
  public static byte[] getMIBValue(final String mtosiString) throws ProcessingFailureException {
    byte[] toReturn = {0};
    String[] triggerTypes = mtosiString.split(",");
    for (String triggerType : triggerTypes) {
      if (triggerType.trim().equals(EFM_RLD.getMtosiText())) {
        toReturn[0] |= EFM_RLD.getBitValue() << 5;
      }
      else if (triggerType.trim().equals(LINK_DOWN.getMtosiText())) {
          toReturn[0] |= LINK_DOWN.getBitValue() << 3;
        }
      else if (triggerType.trim().equals("")) {
        }
      else {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
                ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageIllegal(
                LayeredParams.LrPropAdvaEthernet.LINK_LOSS_FWD_TRIGGER_TYPES_PARAM));
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
    }
    return toReturn;
  }
}
