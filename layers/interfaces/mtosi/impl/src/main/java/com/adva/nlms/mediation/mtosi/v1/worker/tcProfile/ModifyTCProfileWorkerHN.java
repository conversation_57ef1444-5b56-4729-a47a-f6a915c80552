/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */

package com.adva.nlms.mediation.mtosi.v1.worker.tcProfile;

import com.adva.nlms.mediation.common.serviceProvisioning.ShaperSPPropertiesHN4000;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet;
import com.adva.nlms.mediation.config.hn4000.Uni4xxHN4000;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTCProfileMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854ext.adva.TCProfileCreateDataT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public class ModifyTCProfileWorkerHN extends ModifyTCProfileWorker {
	private static Logger LOG = LogManager.getLogger(ModifyTCProfileWorkerHN.class.getName());

	public ModifyTCProfileWorkerHN(Holder<HeaderT> mtosiHeader, TCProfileCreateDataT tcProfileCreateDataT, NamingAttributesT name, NetworkElement ne) {
		super(mtosiHeader, tcProfileCreateDataT, name, ne);
	}

	@Override
  protected void mediate() throws Exception {
		String ptpName = name.getPtpNm();
		String ftpName = name.getFtpNm();
		PortHN4000Ethernet port = null;
		if (ptpName != null && ftpName == null) {
			port = (PortHN4000Ethernet) ManagedElementFactory.getPort(name);
			if (port == null) {
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
						MtosiErrorConstants.PTP_NOT_FOUND);
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
		} else if (ptpName == null && ftpName != null) {
			port = (PortHN4000Ethernet) ManagedElementFactory.getHN4000Ftp(name);
			if (port == null) {
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
						MtosiErrorConstants.FTP_NOT_FOUND);
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
		} else {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
					MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		if (!port.hasUni()) {
			ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
					MtosiErrorConstants.TC_PROFILE_NOT_FOUND);
			throw new ProcessingFailureException(pfet.getReason(), pfet);
		}
		if (tcpNm.startsWith(MtosiConstants.ING_POLICER_TEXT)) {
			mediatePolicer((Uni4xxHN4000) port.getUni());
		} else if (tcpNm.startsWith(MtosiConstants.EG_SHAPER_TEXT)) {
			mediateShaper((Uni4xxHN4000) port.getUni());
		} else {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID);
		}
	}

	private void mediateShaper(Uni4xxHN4000 uni) throws Exception {
		final ShaperSPPropertiesHN4000 shaperSPProperties = MtosiTCProfileMediator.getShaperSPPropertiesHN(tcProfileCreateDataT);
		if (shaperSPProperties == null) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.TC_PROFILE_NOT_FOUND);
		}
		transactShaper(uni, shaperSPProperties);
	}

	private void transactShaper(Uni4xxHN4000 uni, ShaperSPPropertiesHN4000 shaperSPProperties) throws ProcessingFailureException, ObjectInUseException,
			NetTransactionException, SPValidationException, SNMPCommFailure {
		final NetworkElement locks[] = new NetworkElement[] { ne };
		final Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "ModifyTCProfileWorkerHN");
		try {
			uni.getShaper().setSettings(shaperSPProperties);
			NetTransactionManager.commitNetTransaction(id);
		} catch (NetTransactionException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (ObjectInUseException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SPValidationException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SNMPCommFailure e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} finally {
			NetTransactionManager.ensureEnd(id);
		}
	}

	private void mediatePolicer(final Uni4xxHN4000 uni) throws Exception {
		final ShaperSPPropertiesHN4000 policerSPProperties = MtosiTCProfileMediator.getPolicerSPPropertiesHN(tcProfileCreateDataT);
		if (policerSPProperties == null) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.TC_PROFILE_NOT_FOUND);
		}
		transactPolice(uni, policerSPProperties);
	}

	private void transactPolice(Uni4xxHN4000 uni, ShaperSPPropertiesHN4000 policerSPProperties) throws ProcessingFailureException, ObjectInUseException,
			NetTransactionException, SPValidationException, SNMPCommFailure {
		final NetworkElement locks[] = new NetworkElement[] { ne };
		final Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "ModifyTCProfileWorkerHN");
		try {
			uni.getPolicer().setSettings(policerSPProperties);
			NetTransactionManager.commitNetTransaction(id);
		} catch (NetTransactionException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (ObjectInUseException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SPValidationException e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} catch (SNMPCommFailure e) {
			ServiceUtils.rollbackNetTransaction(id, e, LOG);
		} finally {
			NetTransactionManager.ensureEnd(id);
		}
	}
}
