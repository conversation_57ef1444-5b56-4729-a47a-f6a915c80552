/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.hn4000;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000EthernetSProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.UNISPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.WANPortSPProperties;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NoSuchEntityException;
import com.adva.nlms.mediation.config.hn4000.ObjectStateFieldHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet;
import com.adva.nlms.mediation.config.hn4000.UniHN4000;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiHNUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNAdminSpeedRateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNAutoNegotiationTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNCosMapTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNDot3OamOperStatusTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNDuplexModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNEfmAdminControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNEgressRateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNLinkLossForwardingTranslation;
import com.adva.nlms.common.snmp.hn.HNLoopbackStatusTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNMdiModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNOamModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNOamSupportedFunctionsTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNPauseFramesTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNRoleStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNSTPAdminControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNTagEtherTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.HNuniPortTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.ServiceStateTranslation;
import v1.tmf854.DirectionalityT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.PhysicalTerminationPointT;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ResourceStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;

/**
 * This class is a HN4000 Ethernet port MTOSI Translator.
 */
public class PortHN4000EthernetTranslator extends MtosiTranslator {
  private PortHN4000Ethernet port;
  protected boolean hn400;
  protected boolean hn41x;

  
  public PortHN4000EthernetTranslator() {}
  
  public PortHN4000EthernetTranslator(PortHN4000Ethernet port) {
    this.port = port;
    NetworkElement ne = port.getNE();
	hn400 = ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400;
	String neType = ne.getNetworkElementTypeString();
	hn41x = neType != null && neType.indexOf("HN41") >= 0;

  }

  @Override
  public PhysicalTerminationPointT toMtosiPTP() throws ProcessingFailureException {
    ObjectFactory objFactory = new ObjectFactory();
    PhysicalTerminationPointT physicalTerminationPointT = objFactory.createPhysicalTerminationPointT();

    // PTP element name
    NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributes(port);
    physicalTerminationPointT.setName(objFactory.createPhysicalTerminationPointTName(namingAttributes));

    // discoveredName
    final String ptpNm = namingAttributes.getPtpNm();
    if (ptpNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
    }
    physicalTerminationPointT.setDiscoveredName(objFactory.createPhysicalTerminationPointTDiscoveredName(ptpNm));

    // namingOS
    physicalTerminationPointT.setNamingOS(objFactory.createPhysicalTerminationPointTNamingOS(OSFactory.getNmsName()));

    // source
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    physicalTerminationPointT.setSource(objFactory.createPhysicalTerminationPointTSource(source));

    // resource state
    ResourceStateT resourceState = new ResourceStateT();
    if (port.getOperState() == ServiceStateTranslation.UP_NOT_PRESENT.getOperStateValue()) {
      resourceState.setValue(ResourceStateEnumT.PLANNED);
    }
    else {
      resourceState.setValue(ResourceStateEnumT.INSTALLED);
    }
    physicalTerminationPointT.setResourceState(objFactory.createPhysicalTerminationPointTResourceState(resourceState));

    // direction
    physicalTerminationPointT.setDirection(objFactory.createPhysicalTerminationPointTDirection(DirectionalityT.D_BIDIRECTIONAL));

    // edgePoint
    physicalTerminationPointT.setEdgePoint(objFactory.createPhysicalTerminationPointTEdgePoint(true));

    // layers
    LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    // -------start Layer--------
    PortHN4000EthernetSProperties properties = port.getPortSPProperties();
    
    //For now, all we have is the 408...
    int moduleAssignType = -1;
    try {
		  moduleAssignType = port.getModuleAssignedType();
	} catch (NoSuchEntityException e) {
		throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND,e);
	}
	
    if (hn400) {
    	if (hn41x) {
    	      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL);
    	      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL,
    	              LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.ACTIVE_PARAM);
    	} else {
    	      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL);
    	      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL,
    	              LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.ACTIVE_PARAM);
    	}
    }else if (MtosiHNUtils.isHN4000GbT(properties.get(PortHN4000EthernetSProperties.VI.PortType),moduleAssignType)) {
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_ELECTRICAL,
              LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.ACTIVE_PARAM);
    } else if (MtosiHNUtils.isHN4000FEX(properties.get(PortHN4000EthernetSProperties.VI.PortType),moduleAssignType) || MtosiHNUtils.isHN4000GbX(properties.get(PortHN4000EthernetSProperties.VI.PortType),moduleAssignType)) {
      LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL);
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_PHYSICAL_OPTICAL,
              LayeredParams.LrElectricalAndOptical.THIS_LAYER_ACTIVE_PARAM, LayeredParams.ACTIVE_PARAM);
    }
    String dsrLayer = null;
	if (hn400 || MtosiHNUtils.isHN4000FEX(properties.get(PortHN4000EthernetSProperties.VI.PortType),moduleAssignType)) {
		dsrLayer = LayeredParams.LR_DSR_FAST_ETHERNET;
	} else if (MtosiHNUtils.isHN4000GbT(properties.get(PortHN4000EthernetSProperties.VI.PortType),moduleAssignType) || MtosiHNUtils.isHN4000GbX(properties.get(PortHN4000EthernetSProperties.VI.PortType),moduleAssignType)) {
		dsrLayer = LayeredParams.LR_DSR_GIGABIT_ETHERNET;
	}
	if (dsrLayer != null) {
		// ----- start DSR Layer Fast Ethernet or Gigabit Ethernet Layer --------------
		LayeredParameterUtils.addLayer(layeredParametersListT, dsrLayer);
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, dsrLayer, 
				LayeredParams.PropADVA2BPME.ADMINISTRATION_CONTROL_PARAM,
				MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, port.getAdminState()));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, dsrLayer, 
				LayeredParams.PropADVA2BPME.SERVICE_STATE_PARAM,
				ServiceStateTranslation.getMtosiString(port.getAdminState(), port.getOperState()));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, dsrLayer, 
				LayeredParams.LrDsrGigabitAndFastEthernet.AUTO_NEGOTIATION_PARAM,
				HNAutoNegotiationTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.AutoNeg)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, dsrLayer, 
				LayeredParams.LrDsrGigabitAndFastEthernet.ADMINISTRATIVE_SPEED_RATE_PARAM,
				HNAdminSpeedRateTranslation.getMtosiString(properties.get(WANPortSPProperties.VI.PortSpeed)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, dsrLayer, 
				LayeredParams.LrDsrGigabitAndFastEthernet.ACTUAL_SPEED_RATE_PARAM,
				MtosiHNUtils.bytesToMbs(properties.get(PortHN4000EthernetSProperties.VL.ActualSpeed)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, dsrLayer, 
				LayeredParams.LrDsrGigabitAndFastEthernet.DUPLEX_MODE_PARAM,
				HNDuplexModeTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.DuplexMode)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, dsrLayer, 
				LayeredParams.LrDsrGigabitAndFastEthernet.ACTUAL_DUPLEX_MODE_PARAM,
				HNDuplexModeTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.ActualDuplexMode)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, dsrLayer, 
				LayeredParams.LrDsrGigabitAndFastEthernet.MAXIMUM_FRAME_SIZE_PARAM, 
				String.valueOf(properties.get(WANPortSPProperties.VI.IfMtu)));
		// -------end of DSR Layer Fast Ethernet or Gigabit Ethernet Layer-------
	}
	// -------start LR Ethernet Layer--------
	LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.LR_ETHERNET);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET, 
			LayeredParams.LrEthernet.CONNECTIONLESS_PORT_PARAM,
			MtosiConstants.TRUE);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET, 
			LayeredParams.LrEthernet.INTERFACE_TYPE_PARAM,
			properties.get(PortHN4000EthernetSProperties.IT.InterfaceType) == null
              ? "null"
              : properties.get(PortHN4000EthernetSProperties.IT.InterfaceType).getIfTypeString());
  LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET,
			LayeredParams.LrEthernet.PORT_TP_ROLE_STATE_PARAM,
			HNRoleStateTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.PortTPRoleState)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET, 
			LayeredParams.LrEthernet.NUMBER_OF_TRAFFIC_CLASSES_PARAM,
			"1");
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET, 
			LayeredParams.LrEthernet.PHYS_ADDRESS_PARAM,
			//I had some bad Addresses that failed in the SOAP layer, (they were 6 chars long...)
			String.valueOf(properties.get(WANPortSPProperties.VS.IfPhysAddress).length() > 10 ? properties.get(WANPortSPProperties.VS.IfPhysAddress): ""));
	String maxNumFDFrs = hn400? "1" : "256"; //1 for HN400, 256 for HN4000
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET, 
			LayeredParams.LrEthernet.MAX_NUM_FDFRS_PARAM, maxNumFDFrs);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.LR_ETHERNET, 
			LayeredParams.LrEthernet.NUM_CONFIGURED_FDFRS_PARAM,
			String.valueOf(properties.get(WANPortSPProperties.VI.NumConfiguredFDFrs)));
	// -------end of Layer-------

	// -------start PROP_HATTERAS_Ethernet layer ----------------
	LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, 
			LayeredParams.PropHatterasEthernet.DESCRIPTION,
			String.valueOf(properties.get(PortHN4000EthernetSProperties.VS.Description)));
	Integer secondaryState = properties.get(PortHN4000EthernetSProperties.VI.SecondaryState);
	if (secondaryState != null) 
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, 
				LayeredParams.PropHatterasShdsl.SECONDARY_STATE_PARAM,
				ObjectStateFieldHN4000.getSecondaryStateString(secondaryState));
	else 
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, 
				LayeredParams.PropHatterasShdsl.SECONDARY_STATE_PARAM,
				"");
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, 
			LayeredParams.PropHatterasEthernet.TRANSMIT_PAUSE_FRAMES, 
			HNPauseFramesTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.TransmitPauseFrames)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, 
			LayeredParams.PropHatterasEthernet.RECEIVE_PAUSE_FRAMES, 
			HNPauseFramesTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.ReceivePauseFrames)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, 
			LayeredParams.PropHatterasEthernet.LOOPBACK_STATUS_TYPE,
    HNLoopbackStatusTypeTranslation.fromInt(properties.get(PortHN4000EthernetSProperties.VI.LoopbackStatus)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, 
			LayeredParams.PropHatterasEthernet.LOOPBACK_STATUS_LOCAL,
			BooleanTypeTranslation.ENABLED.getMIBValue() != properties.get(PortHN4000EthernetSProperties.VI.LoopbackMode) ? BooleanTypeTranslation.DISABLED.getMtosiString() :
					BooleanTypeTranslation.ENABLED.getMtosiString());
	
	if (hn400) {
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, 
				LayeredParams.PropHatterasEthernet.LINK_LOSS_FORWARDING_PARAM, 
				HNLinkLossForwardingTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.LinkLossForwarding)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, 
				LayeredParams.PropHatterasEthernet.MDI_MODE_PARAM, 
				HNMdiModeTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.MdiMode)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet, 
				LayeredParams.PropHatterasEthernet.ACTUAL_MDI_MODE_PARAM, 
				HNMdiModeTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.ActualMdiMode)));
		
	}

	// -------end of PROP_HATTERAS_Ethernet Layer-------

	// -------start PROP_HATTERAS_Ethernet_EFMOAM layer ----------------
	LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_EFMOAM);
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_EFMOAM, 
			LayeredParams.PropHatterasEthernetEFMOAM.EFM_OAM_ADMINISTRATION_CONTROL_PARAM,
			HNEfmAdminControlTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.OamAdminState)));
	if (properties.get(PortHN4000EthernetSProperties.VI.OamSuspend) != null &&  properties.get(PortHN4000EthernetSProperties.VI.OamSuspend) != 0) {
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_EFMOAM, 
				LayeredParams.PropHatterasEthernetEFMOAM.EFM_OAM_SUSPEND_PARAM,
				MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE,properties.get(PortHN4000EthernetSProperties.VI.OamSuspend)));
	}
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_EFMOAM, 
			LayeredParams.PropHatterasEthernetEFMOAM.EFM_OAM_SERVICE_STATE_PARAM, 
			HNDot3OamOperStatusTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.OamServiceState)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_EFMOAM,
			LayeredParams.PropHatterasEthernetEFMOAM.EFM_OAM_MODE_PARAM, 
			HNOamModeTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VI.OamMode)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_EFMOAM, 
			LayeredParams.PropHatterasEthernetEFMOAM.EFM_OAM_MAX_PDU_SIZE_PARAM, 
			String.valueOf(properties.get(PortHN4000EthernetSProperties.VI.OamMaxPduSize)));
	LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_EFMOAM, 
			LayeredParams.PropHatterasEthernetEFMOAM.EFM_OAM_FUNCTIONS_SUPPORTED_LIST_PARAM, 
			HNOamSupportedFunctionsTranslation.getMtosiString(properties.get(PortHN4000EthernetSProperties.VS.OamFunctionsSupported)));

	// -------end of PROP_HATTERAS_Ethernet_EFMOAM Layer-------
	fillUNILayer(layeredParametersListT);
	
	JAXBElement<LayeredParametersListT> transmissionParams = objFactory.createPhysicalTerminationPointTTransmissionParams(layeredParametersListT);
	
	physicalTerminationPointT.setTransmissionParams(transmissionParams);

	return physicalTerminationPointT;
	}
  
  
	  protected void fillUNILayer(LayeredParametersListT layeredParametersListT) {
		 
		try {
			if (hasUni()) {
				if (getUni().getUniSPProperties() == null) {
					return;
				}
			} else {
				return;
			}
		} catch (Exception e) {
			// Exceptions are happening getting the UNI.
			System.out.println(e);
			return;
		}

		UNISPPropertiesHN4000 properties = (UNISPPropertiesHN4000) getUni().getUniSPProperties();
//		System.out.println(properties);
		LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI);
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
				LayeredParams.PropHatterasEthernetUni.THIS_LAYER_ACTIVE_PARAM, LayeredParams.ACTIVE_PARAM);
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
				LayeredParams.PropHatterasEthernetUni.NAME_PARAM, 
				String.valueOf(properties.get(UNISPPropertiesHN4000.VS.Name)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
				LayeredParams.PropHatterasEthernetUni.DESCRIPTION_PARAM, 
				String.valueOf(properties.get(UNISPPropertiesHN4000.VS.PortDesc)));
		Integer secondaryState = properties.get(UNISPPropertiesHN4000.VI.ObjState);
		if (secondaryState != null)
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
					LayeredParams.PropHatterasEthernetUni.SECONDARY_STATE_PARAM, ObjectStateFieldHN4000.getSecondaryStateString(secondaryState));
		else
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet,
					LayeredParams.PropHatterasShdsl.SECONDARY_STATE_PARAM, "");
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
				LayeredParams.PropHatterasEthernetUni.DEFAULT_VLANID_PARAM, 
				String.valueOf(properties.get(UNISPPropertiesHN4000.VL.DefVlanId)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
				LayeredParams.PropHatterasEthernetUni.STP_ADMINISTRATIONCONTROL_PARAM, 
				HNSTPAdminControlTranslation.getMtosiString(properties.get(UNISPPropertiesHN4000.VI.StpAdminState)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
				LayeredParams.PropHatterasEthernetUni.PORT_TYPE_PARAM, 
				HNuniPortTypeTranslation.getMtosiString(properties.get(UNISPPropertiesHN4000.VI.PortType)));
		if (hn400) {
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
					LayeredParams.PropHatterasEthernetUni.EGRESS_RATE_LIMITING_ENABLED_PARAM, 
					HNEgressRateTranslation.getMtosiString(properties.get(UNISPPropertiesHN4000.VL.EgressRate)));
		}
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
				LayeredParams.PropHatterasEthernetUni.TAG_ETHER_TYPE_PARAM, 
				HNTagEtherTypeTranslation.getMtosiString(properties.get(UNISPPropertiesHN4000.VL.TagEtherType)));
		//Suppress if PortType is Access
		if (properties.get(UNISPPropertiesHN4000.VI.PortType) == null || HNuniPortTypeTranslation.Access.getMibValue() != properties.get(UNISPPropertiesHN4000.VI.PortType)) {
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
					LayeredParams.PropHatterasEthernetUni.RX_UNTAGGED_PARAM, 
					MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, properties.get(UNISPPropertiesHN4000.VI.RxUntagged)));
			LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
					LayeredParams.PropHatterasEthernetUni.RX_UNKNOWN_PARAM, 
					MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, properties.get(UNISPPropertiesHN4000.VI.RxUnknown)));
			if (hn400) {
				LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
						LayeredParams.PropHatterasEthernetUni.TX_UNTAGGED_PARAM, 
						MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, properties.get(UNISPPropertiesHN4000.VI.TxUntagged)));
			}
		}
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
				LayeredParams.PropHatterasEthernetUni.COS_MAP_TYPE_PARAM, 
				HNCosMapTypeTranslation.getMtosiString(properties.get(UNISPPropertiesHN4000.VI.CosMapType)));
		LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_HATTERAS_Ethernet_UNI,
				LayeredParams.PropHatterasEthernetUni.DEFAULT_COS_PARAM, "1");
	}
	/**
		 * Need a bit map function
		 * 
		 * @param oamFunctionsSupported
		 * @return
		 */
	
		private String oamFunctionsToMtosi(String oamFunctionsSupported) {
			return String.valueOf(oamFunctionsSupported);
		}

	/**
	 * Check to see if the port has a UNI
	 * @return
	 */
	protected boolean hasUni() {
		return port.hasUni();
	}
	/**
	 * Return the UNI attached to the Port
	 * @return
	 */
	protected UniHN4000 getUni() {
		return port.getUni();
	}
	/**
	 * Return the UNI attached to the Port
	 * @return
	 */
	protected PortHN4000Ethernet getPort() {
		return port;
	}


}
