/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils;

import org.apache.wss4j.common.crypto.JasyptPasswordEncryptor;
import org.eclipse.jetty.util.security.Password;


public class PasswordUtils {

  public static void main(String [] arg){

    if(arg.length < 1){
      System.err.println("Usage - java com.adva.nlms.mediation.mtosi.v1.utils.PasswordUtils <password> <obf|enc>");
      System.exit(1);
    }else{
      String p = arg[0];
      String operation=null;
      if(arg.length > 1){
        operation = arg[1];
        if(operation.toLowerCase().equals("enc")){
          JasyptPasswordEncryptor encryptor=new JasyptPasswordEncryptor("rf45FR%$","PBEWithMD5AndDES");
          String value = encryptor.encrypt(p);
//          System.err.println(p);
          System.err.println(value);
        }else if(operation.toLowerCase().equals("obf")){
          Password pw = new Password(p);
//          System.err.println(pw.toString());
          System.err.println(Password.obfuscate(pw.toString()));
        }
      }else {
        Password pw = new Password(p);
//        System.err.println(pw.toString());
        System.err.println(Password.obfuscate(pw.toString()));
      }
    }


  }

}
