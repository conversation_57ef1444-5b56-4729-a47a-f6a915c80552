/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */
package com.adva.nlms.mediation.mtosi.v1.worker.cfm;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.MACompAttr;
import com.adva.nlms.mediation.config.dto.attr.MANetAttr;
import com.adva.nlms.mediation.config.dto.attr.MDAttr;
import com.adva.nlms.mediation.config.dto.attr.MEPAttr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3AccAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3NetAttr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.factory.ConnectivityFaultMaintenanceFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.fsp20X.FspGE20XCFMMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.LayeredParametersT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import v1.tmf854ext.adva.ModifyMaintenanceEndpointResponseT;
import v1.tmf854ext.adva.ModifyMaintenanceEndpointT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBElement;
import jakarta.xml.ws.Holder;
import java.util.HashMap;
import java.util.Map;

public class ModifyMaintenanceEndpointWorker extends AbstractMtosiWorker {
  private final ModifyMaintenanceEndpointT mtosiBody;
  private MtosiAddress mtosiAddress;
  private LayeredParametersT parameterListType;
  protected ModifyMaintenanceEndpointResponseT response = new ModifyMaintenanceEndpointResponseT();
  private ConnectivityFaultMaintenanceFactory cfmFactory = new ConnectivityFaultMaintenanceFactory();
  Logger LOG = LogManager.getLogger(this.getClass().getName());
  private JAXBElement<LayeredParametersListT> mepModifyData;
  private MtosiMOFacade facade;
  private DTO<MANetAttr> manet;
  private DTO<MEPAttr> mepAttrDTO;
  private DTO<MEPAttr> mep;
  private NetworkElement ne;
  private DTO ptp;
  private DTO<MDAttr> md;
  private DTO<MEPAttr> mepDto;

  private static Map<Integer, String> namePatterns = new HashMap<>();
  private static final String patternPTPForGE = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=((NET-(\\d+))|(ACC))";
  public static final String patternACC = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=(ACC)";
  public static final String patternNET = "/(shelf)=(\\d+)/(slot)=(\\d+)/(port)=(NET)-(\\d+)";

  static {
    namePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201, patternPTPForGE);
    namePatterns.put(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_GE201SE, patternPTPForGE);
  }

  public ModifyMaintenanceEndpointWorker(ModifyMaintenanceEndpointT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "modifyMaintenanceEndpoint", "modifyMaintenanceEndpoint", "modifyMaintenanceEndpointResponse");
    this.mtosiBody = mtosiBody;
    this.mtosiHeader = mtosiHeader;
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
    validator.validate(mtosiAddress.getNE().getDefaultNetworkElementTypeString());
  }
  @Override
  protected void parse() throws Exception {
    if ( mtosiBody.getMepInfo() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "MepInfo element is missing or is not valid.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    NamingAttributesT meName = mtosiBody.getMepInfo().getMepName();
    mepModifyData = mtosiBody.getMepInfo().getTransmissionParams();

    if (meName == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "MeName has not been specified or is invalid.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    mtosiAddress = new MtosiAddress(meName);
    ne = mtosiAddress.getNE();

    if (mtosiAddress.getNaming().getCfmMdNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MD_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if (!mtosiAddress.getNaming().getMdNm().equals(OSFactory.getMDNm())) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MD_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if (mtosiAddress.getNaming().getMeNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.ME_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    MtosiUtils.validateNE(ne);

    if (mtosiAddress.getNaming().getCfmMdNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MDFR_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);

    }

    if(mtosiAddress.getNaming().getMaNm() == null) {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MAFR_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    facade = getMtosiCtrl().getMoFacadeManager().getFacadeViaNeId(MtosiMOFacade.class, ne.getID());
    md = facade.findDTOViaMtosiName(ne.getID(), mtosiAddress.getNaming().getCfmMdNm(), MDAttr.class);

    if(mtosiAddress.getNaming().getPtpNm() == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MAFR_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if(!mtosiAddress.getNaming().getPtpNm().matches(namePatterns.get(ne.getNetworkElementType()))){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MDFR_PTP_INVALID);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    ptp = facade.findDTOViaMtosiName(ne.getID(), mtosiAddress.getNaming().getPtpNm(), getPtpEntityPerNe(mtosiAddress.getNaming().getPtpNm()));

    if(ptp == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.PTP_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if((facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+ mtosiAddress.getNaming().getCfmMdNm(), MDAttr.class)) == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MDFR_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if( (manet = facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+mtosiAddress.getNaming().getCfmMdNm() +" && /ma="+mtosiAddress.getNaming().getMaNm(), MANetAttr.class))== null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MANETFR_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if(  facade.findDTOViaMtosiName(ne.getID(),"/cfmmd="+mtosiAddress.getNaming().getCfmMdNm() +" && /ma="+mtosiAddress.getNaming().getMaNm()+ " && " + mtosiAddress.getNaming().getPtpNm() , MACompAttr.class) == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
          MtosiErrorConstants.MACOMPFR_NOT_FOUND);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }


    if(mtosiAddress.getNaming().getMepNm() != null){
      boolean foundMepId = false;
      String[] mepVals = manet.getValue(MANetAttr.MEP_TABLE).split(",");
      for(String mepVal : mepVals){
        if(mepVal.equals(mtosiAddress.getNaming().getMepNm())){
          foundMepId = true;
        }
      }
      if(!foundMepId){
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
            MtosiErrorConstants.MEP_LIST_MISMATCH);
        throw new ProcessingFailureException(pfet.getReason(), pfet);
      }
    }

   mepDto = facade.findDTOViaMtosiName(ne.getID(), "/cfmmd=" + mtosiAddress.getNaming().getCfmMdNm() + " && /ma=" + mtosiAddress.getNaming().getMaNm() + " && " +
        "" + mtosiAddress.getNaming().getPtpNm() + " && /mep=" + mtosiAddress.getNaming().getMepNm(), MEPAttr.class);
    if(mtosiAddress.getNaming().getMepNm() != null && mepDto == null){
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_CAPACITY_EXCEEDED,
          MtosiErrorConstants.MACOMPFR_EXISTS);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    if (mtosiBody.getMepInfo().getTransmissionParams() != null &&
        mtosiBody.getMepInfo().getTransmissionParams().getValue() != null &&
        mtosiBody.getMepInfo().getTransmissionParams().getValue().getLayeredParameters() != null &&
        mtosiBody.getMepInfo().getTransmissionParams().getValue().getLayeredParameters().get(0) != null){

      parameterListType = mtosiBody.getMepInfo().getTransmissionParams().getValue().getLayeredParameters().get(0);
    } else{
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "Layered Parameter List has not been specified or is invalid.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }

  @Override
  protected void mediate() throws Exception {
    mep = cfmFactory.parseMD(mtosiAddress, mepModifyData,getMtosiCtrl().getMoFacadeManager()).getMEP(mtosiAddress,manet, ptp);
    transact();
  }

  private void transact() throws Exception {
    try {
      facade.openNetTransaction(ne.getID());
//      mep.putOrReplace(MEPAttr.ADMIN_STATE, mepDto.getValue(MEPAttr.ADMIN_STATE));
      mep.putOrReplace(MEPAttr.ENTITY_INDEX, mepDto.getValue(MEPAttr.ENTITY_INDEX));
      mep.putOrReplace(MEPAttr.MTOSI_NAME, mepDto.getValue(MEPAttr.MTOSI_NAME));
      mep = facade.modifyObjectOnDevice(ne.getID(), mep);
      ne.logSROperation(SROperationState.SERVICE_CREATION_SUCCESS, mep.getValue(ManagedObjectAttr.MTOSI_NAME));
    } catch (ObjectInUseException | SNMPCommFailure e) {
      ne.logSROperation(SROperationState.SERVICE_CREATION_FAILURE, mep.getValue(ManagedObjectAttr.MTOSI_NAME));
      throw e;
    } finally {
      facade.closeNetTransaction(ne.getID());
    }
  }

  @Override
  protected void response() throws Exception {
    v1.tmf854ext.adva.ObjectFactory factory = new v1.tmf854ext.adva.ObjectFactory();
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
//    response.setMepName(factory.createCreateMaintenanceEndpointResponseTMepName(mtosiBody.getMepinfo().getMepName()));
//    response.setDiscoveredName(factory.createCreateMaintenanceDomainResponseTDiscoveredName(mtosiAddress.getNaming().getCfmMdNm()));
    NamingAttributesT nm = NamingTranslationFactory.createNamingAttributesMaintenanceEndPoint(mtosiAddress.getNaming().getMeNm(), mtosiAddress.getNaming().getCfmMdNm(),
        mtosiAddress.getNaming().getMaNm() + " && " + mtosiAddress.getNaming().getPtpNm(), mtosiAddress.getNaming().getMepNm());
    response.setMepName(factory.createCreateMaintenanceEndpointResponseTMepName(nm));
    response.setDiscoveredName(factory.createCreateMaintenanceDomainResponseTDiscoveredName(mtosiAddress.getNaming().getMepNm()));
    response.setNamingOS(factory.createCreateMaintenanceDomainResponseTNamingOS(OSFactory.getNmsName()));
    response.setSource(factory.createCreateMaintenanceDomainResponseTSource(source));
    response.setTransmissionParams(factory.createCreateMaintenanceEndpointResponseTTransmissionParams(new FspGE20XCFMMediator().toMtosiMEP(mep)));
  }

  @Override
  public Object getSuccessResponse() {
    if (response == null)
      return null;
    return response;
  }

  private Class getPtpEntityPerNe(String portType) throws ProcessingFailureException {
    if (portType.matches(patternNET)) {
      return PortF3NetAttr.class;
    } else if (portType.matches(patternACC)) {
      return PortF3AccAttr.class;
    }else{
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "Port type cannot be identified for the specified Termination Point.");
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
  }
}
