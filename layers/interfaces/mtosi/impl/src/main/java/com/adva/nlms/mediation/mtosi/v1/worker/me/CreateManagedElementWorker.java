/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.common.config.netypes.NEType;
import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.MDRequestFailedException;
import com.adva.nlms.mediation.common.serviceProvisioning.ManagedElementSPProperties;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementHdlrLocal;
import com.adva.nlms.mediation.config.NoSuchNetworkElementException;
import com.adva.nlms.mediation.config.NoSuchSubnetException;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiMEMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.ManagedElementMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ManagedElementUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPVersionEnum;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import com.adva.nlms.mediation.topology.SubnetHdlrLocal;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.w3c.dom.Node;
import v1.tmf854.HeaderT;
import v1.tmf854.MEVendorExtensionsT;
import v1.tmf854.ManagedElementT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.CreateManagedElementResponseT;
import v1.tmf854ext.adva.CreateManagedElementT;
import v1.tmf854ext.adva.MECreateDataT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.ArrayList;
import java.util.List;

public class CreateManagedElementWorker extends AbstractMtosiWorker {
  Logger LOG = LogManager.getLogger(this.getClass().getName());

  protected CreateManagedElementT mtosiBody;
  protected CreateManagedElementResponseT response = new CreateManagedElementResponseT();
  protected MECreateDataT meCreateData;
  protected NamingAttributesT meName;
  protected MtosiAddress mtosiAddr;
  protected String ipAddress;
  protected NetworkElement ne;

  public CreateManagedElementWorker(CreateManagedElementT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "createManagedElement", "createManagedElement", "createManagedElementResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    meCreateData = mtosiBody.getCreateData();

    if (meCreateData == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ME data has not been specified.");
    }

    if (meCreateData.getName() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ME Name has not been specified.");
    }

    meName = meCreateData.getName();

    if (!NamingTranslationFactory.isManagementDomain(meName)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!meName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MD_NOT_FOUND);
    }

    // check if name, productName, IpValue and SubnetPath are present
    if (meName.getMeNm() == null || meName.getMeNm().equals("")) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ME Name has not been specified.");
    }

    if (meCreateData.getProductName() == null || meCreateData.getProductName().getValue() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The Product Name has not been specified.");
    }

    int ned= NEType.getNETypeFor(meCreateData.getProductName().getValue()).getTypeId();
    if (ned== NeTypeIds.NETWORK_ELEMENT_TYPE_ANY) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          "The Product Name cannot be mapped to a valid network element type.");
    }
    if (meCreateData.getVendorExtensions() == null || meCreateData.getVendorExtensions().getValue() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The Vendor Extensions have not been specified.");
    }
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  //check the the ProductName in the request with the available NEs.
	  validator.validate(meCreateData.getProductName().getValue());
  }
  @Override
  protected void mediate() throws Exception {
    NetworkElementHdlrLocal neHdlr =this.getMtosiCtrl().getConfigCtrl().getHandlers().getNeHdlr();
    final String neName = meName.getMeNm();

    // check if vendor extensions "managementParams" are present
    MEVendorExtensionsT vendorExtensions = meCreateData.getVendorExtensions().getValue();
    Node node = (Node) vendorExtensions.getAny().get(0);
    if (!node.getLocalName().equals("managementParams")) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The vendor extensions has not been specified.");
    }

    // read params from the vendor extensions and validate the mandatory parameters (IpAddress, SubnetPath)
    ManagedElementSPProperties meProps;
    try {
    meProps = MtosiMEMediator.mtosiMEDataTToMEProperties(vendorExtensions);
    }
    catch (NullPointerException npe){
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "A layer or a transmission parameter is missing or is wrong.");
    }

    ipAddress = meProps.get(ManagedElementSPProperties.VS.IPAddress);
    String subnetPath = meProps.get(ManagedElementSPProperties.VS.SubnetPath);
    if (meProps.get(ManagedElementSPProperties.VS.IPAddress) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The IP-Address has not been specified.");
    }
    if (meProps.get(ManagedElementSPProperties.VS.SubnetPath) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The Subnet Path has not been specified.");
    }
    if (meProps.get(ManagedElementSPProperties.VB.UseGlobalSNMPSettings) == null) {
      meProps.set(ManagedElementSPProperties.VB.UseGlobalSNMPSettings, Boolean.TRUE);
    }
    if (!meProps.get(ManagedElementSPProperties.VB.UseGlobalSNMPSettings)) {
      if (meProps.get(ManagedElementSPProperties.VI.SNMPProtocolVersion) == null)
        meProps.set(ManagedElementSPProperties.VI.SNMPProtocolVersion, SNMPVersionEnum.VERSION1.getId());
      if (meProps.get(ManagedElementSPProperties.VS.ReadCommunity) == null)
        meProps.set(ManagedElementSPProperties.VS.ReadCommunity, "");
      if (meProps.get(ManagedElementSPProperties.VS.WriteCommunity) == null)
        meProps.set(ManagedElementSPProperties.VS.WriteCommunity, "");
    }

    NetworkElement ne = neHdlr.getNEByIPAddr(ipAddress);
    if (ne != null)  {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ME already exists.");
    }
    ne = neHdlr.getNEByName(neName);
    if (ne != null && !ne.getIPAddress().equals(ipAddress)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ME Name already exists.");
    }

    // set NE-Type and NE-Name (used for createNe)
    meProps.set(ManagedElementSPProperties.VI.ProductName,
            NEType.getNETypeFor(meCreateData.getProductName().getValue()).getTypeId());

    meProps.set(ManagedElementSPProperties.VS.Name, neName);

    // determine subnets to be created
    List<String> subnetsToCreate = new ArrayList<String>();
    int parentId = ManagedElementUtils.getSubnetsToCreate(subnetPath, subnetsToCreate);

    // perform transaction
    transact(parentId, subnetsToCreate, meProps);
  }

  private void transact(int parentId, List<String> subnets, ManagedElementSPProperties meProps)
          throws ProcessingFailureException, ObjectInUseException, NetTransactionException, MDRequestFailedException, SPValidationException, NoSuchSubnetException {
    SubnetHdlrLocal subnetHdlr = this.getMtosiCtrl().getConfigCtrl().getHandlers().getSubnetHdlr();
    NetworkElementHdlrLocal neHdlr = this.getMtosiCtrl().getConfigCtrl().getHandlers().getNeHdlr();

    NetworkElement locks[] = new NetworkElement[] {};

    Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "CreateManagedElementWorker");
    try
    {
      // *** create subnet-path
      for (String subnet : subnets) {
        parentId = subnetHdlr.createSubnet(parentId, subnet);
      }

      // *** create NE
      subnetHdlr.createNe(meProps, parentId);

      try {
        final NetworkElement ne = neHdlr.getNEImpl(ipAddress);
        logSecurity(ne, SystemAction.AddNetwork, ne.getName());
      } catch (NoSuchNetworkElementException nse) {
        // ignore
      }

      NetTransactionManager.commitNetTransaction(id);
    }
    catch (NetTransactionException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (MDRequestFailedException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (NoSuchSubnetException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } catch (SPValidationException e) {
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
    } finally
    {
      NetTransactionManager.ensureEnd(id);
    }
  }

  @Override
  protected void response() throws Exception {
    NetworkElementHdlrLocal neHdlr = this.getMtosiCtrl().getConfigCtrl().getHandlers().getNeHdlr();
    ne = neHdlr.getNEImpl(ipAddress);

    if (ne != null) {
      ManagedElementT managedElement = ManagedElementMediator.nmsNeToManagedElementT(ne);
      response.setTheManagedElement(managedElement);

    }
  }

  @Override
  public CreateManagedElementResponseT getSuccessResponse()
  {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
//    if (response.getTheManagedElement() != null) {
//       //create new thread, start it and return
//      CreateManagedElementNotification neNotification= new CreateManagedElementNotification(MtosiNotificationHelper.getInstance(),
//              SubscribeUnsubscribeOperations.getInstance(),this.getMtosiCtrl());
//      neNotification.setMtosiCtrl(this.getMtosiCtrl());
//      neNotification.setNe(ne);
//      Thread tr=new Thread(neNotification);
//      tr.start();
//    }
    return response;
  }

}