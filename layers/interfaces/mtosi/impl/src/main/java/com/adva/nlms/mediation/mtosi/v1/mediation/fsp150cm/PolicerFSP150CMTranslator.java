/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fsp150cm;

import com.adva.nlms.common.snmp.MIBFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.F3PolicerSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ShaperSPProperties;
import com.adva.nlms.mediation.config.f3.entity.policer.qospolicer.QOSFlowPolicerImpl;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMSecondaryStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMServiceStateTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMPolicerAlgorithmTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMPolicerColorModeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import v1.tmf854ext.adva.TCProfileT;
import ws.v1.tmf854.ProcessingFailureException;

/**
 * This class is an FSP 150 CM policer MTOSI Translator.
 */
public class PolicerFSP150CMTranslator extends MtosiTranslator {
  private QOSFlowPolicerImpl policer;

  public PolicerFSP150CMTranslator(QOSFlowPolicerImpl policer) {
    this.policer = policer;
  }

  @Override
  public TCProfileT toMtosiTCProfile() throws ProcessingFailureException {
    final v1.tmf854ext.adva.ObjectFactory objFactoryEx = new v1.tmf854ext.adva.ObjectFactory();
    final ObjectFactory objFactory = new ObjectFactory();
    final TCProfileT tcProfileT = objFactoryEx.createTCProfileT();
    final F3PolicerSPProperties props = policer.getPolicerSPProperties();

    // TCProfile Name
    final NamingAttributesT namingAttributes = NamingTranslationFactory.getNamingAttributes(policer);
    tcProfileT.setName(objFactoryEx.createTCProfileTName(namingAttributes));

    // discoveredName
    final String tcpNm = namingAttributes.getTcpNm();
    if (tcpNm == null) {
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.TC_PROFILE_NAME_NOT_VALID);
    }
    tcProfileT.setDiscoveredName(objFactoryEx.createTCProfileTDiscoveredName(tcpNm));

    // namingOS
    tcProfileT.setNamingOS(objFactoryEx.createTCProfileTNamingOS(OSFactory.getNmsName()));

    // source
    final SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    tcProfileT.setSource(objFactoryEx.createTCProfileTSource(source));

    // layers
    final LayeredParametersListT layeredParametersListT = objFactory.createLayeredParametersListT();

    // -------start Layer--------
    LayeredParameterUtils.addLayer(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_POLICER);

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_POLICER,
            LayeredParams.LrPropAdvaEthernetPolicer.ADMINISTRATION_CONTROL_PARAM,
            MtosiUtils.getMtosiString(CMAdministrationControlTranslation.NOT_APPLICABLE, props.get(ShaperSPProperties.VI.AdminState)));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_POLICER,
            LayeredParams.LrPropAdvaEthernetPolicer.SERVICE_STATE_PARAM,
            CMServiceStateTranslation.getMtosiString(props.get(ShaperSPProperties.VI.AdminState), props.get(ShaperSPProperties.VI.OperationalState)));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_POLICER,
            LayeredParams.LrPropAdvaEthernetPolicer.SECONDARY_STATE_PARAM,
            CMSecondaryStateTranslation.getMtosiString(props.get(F3PolicerSPProperties.VI.SecondaryState)));

    if (props.get(ShaperSPProperties.VI.TypeIndex) == MIBFSP150CM.Facility.ShaperTable.TYPE_INDEX_A2N) {
      // ingress parameter
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_POLICER,
              LayeredParams.LrPropAdvaEthernetPolicer.INGRESS_CIR_PARAM,
              Long.toString(props.get(F3PolicerSPProperties.VL.CIR)));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_POLICER,
              LayeredParams.LrPropAdvaEthernetPolicer.INGRESS_EIR_PARAM,
              Long.toString(props.get(F3PolicerSPProperties.VL.EIR)));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_POLICER,
              LayeredParams.LrPropAdvaEthernetPolicer.INGRESS_CBS_PARAM,
              Long.toString(props.get(F3PolicerSPProperties.VL.CBS)));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_POLICER,
              LayeredParams.LrPropAdvaEthernetPolicer.INGRESS_EBS_PARAM,
              Long.toString(props.get(F3PolicerSPProperties.VL.EBS)));
    }
    else {
      // egress parameter
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_POLICER,
              LayeredParams.LrPropAdvaEthernetPolicer.EGRESS_CIR_PARAM,
              Long.toString(props.get(F3PolicerSPProperties.VL.CIR)));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_POLICER,
              LayeredParams.LrPropAdvaEthernetPolicer.EGRESS_EIR_PARAM,
              Long.toString(props.get(F3PolicerSPProperties.VL.EIR)));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_POLICER,
              LayeredParams.LrPropAdvaEthernetPolicer.EGRESS_CBS_PARAM,
              Long.toString(props.get(F3PolicerSPProperties.VL.CBS)));
      LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_POLICER,
              LayeredParams.LrPropAdvaEthernetPolicer.EGRESS_EBS_PARAM,
              Long.toString(props.get(F3PolicerSPProperties.VL.EBS)));
    }

    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_POLICER,
            LayeredParams.LrPropAdvaEthernetPolicer.POLICING_ALGORITHM_PARAM,
            MtosiUtils.getMtosiString(CMPolicerAlgorithmTranslation.NOT_APPLICABLE, props.get(F3PolicerSPProperties.VI.PolicingAlgorithm)));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_POLICER,
            LayeredParams.LrPropAdvaEthernetPolicer.COLOR_MODE_PARAM,
            MtosiUtils.getMtosiString(CMPolicerColorModeTranslation.NOT_APPLICABLE, props.get(F3PolicerSPProperties.VI.ColorMode)));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_POLICER,
            LayeredParams.LrPropAdvaEthernetPolicer.COLOR_MARKING_FLAG_PARAM,
            MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, props.get(F3PolicerSPProperties.VI.ColorMarkingFlag)));
    LayeredParameterUtils.addLayeredParameter(layeredParametersListT, LayeredParams.PROP_ADVA_ETHERNET_POLICER,
            LayeredParams.LrPropAdvaEthernetPolicer.COUPLING_FLAG_PARAM,
            MtosiUtils.getMtosiString(BooleanTypeTranslation.NOT_APPLICABLE, props.get(F3PolicerSPProperties.VI.CouplingFlag)));

    // -------end of Layer-------

    tcProfileT.setTransmissionParams(objFactoryEx.createTCProfileTTransmissionParams(layeredParametersListT));
    return tcProfileT;
  }
}

