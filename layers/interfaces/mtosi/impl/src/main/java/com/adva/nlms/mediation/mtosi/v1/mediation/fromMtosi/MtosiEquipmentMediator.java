/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi;

import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.ModuleCPMRSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.ModuleCardSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.NTUSPPropertiesFSP150CM;
import com.adva.nlms.mediation.common.serviceProvisioning.NetworkElementSPPropertiesFSP150CM;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMAdministrationControlTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMDeviceTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.LLFModeAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.xerces.dom.ElementNSImpl;
import v1.tmf854.EqVendorExtensionsT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.NVSListT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;
import java.util.List;

public class MtosiEquipmentMediator {
  private static Logger logger = LogManager.getLogger(MtosiEquipmentMediator.class.getPackage().getName());

  public static NTUSPPropertiesFSP150CM mtosiVendorExtensionsTToNTUProperties(EqVendorExtensionsT vendorExtensions)
          throws ProcessingFailureException {
    NTUSPPropertiesFSP150CM props = new NTUSPPropertiesFSP150CM();
    List<Object> objects = vendorExtensions.getAny();
    for (Object object : objects) {
      ElementNSImpl el = (ElementNSImpl) object;

      if (el.getNamespaceURI() != null && el.getNamespaceURI().equals(MtosiConstants.VENDOR_NAMESPACE) &&
              el.getLocalName() != null && el.getLocalName().equals(MtosiConstants.VENDOR_ADMINISTRATION_CONTROL) &&
              el.getFirstChild() != null) {
        String adminControl = el.getFirstChild().getTextContent();
        if (adminControl != null) {
          Integer adminControlValue = MtosiUtils.getMIBValue(CMAdministrationControlTranslation.NOT_APPLICABLE, adminControl);
          if (adminControlValue == CMAdministrationControlTranslation.NOT_APPLICABLE.getMIBValue()) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.ADMINISTRATION_CONTROL_ILLEGAL_VALUE);
          }
          props.set(EquipmentSPProperties.VI.AdminState, adminControlValue);
        }
      }
      else if (el.getNamespaceURI() != null && el.getNamespaceURI().equals(MtosiConstants.VENDOR_TMF_NAMESPACE) &&
              el.getLocalName() != null && el.getLocalName().equals(MtosiConstants.VENDOR_TRANSMISSION_PARAMS)) {
        JAXBContext jc;
        Unmarshaller u;

        try {
          jc = JAXBContext.newInstance("v1.tmf854");
          u = jc.createUnmarshaller();
        } catch (JAXBException ex) {
          throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.JAXB_MARSHALLING_PROBLEM, ex);
        }

        try {
          LayeredParametersListT transmissionParams = u.unmarshal(el,LayeredParametersListT.class).getValue();

          String dyingGaspsString = LayeredParameterUtils.getLayeredParameter(transmissionParams,
                  LayeredParams.PROP_ADVA_ETHERNET, LayeredParams.LrPropAdvaEthernet.SNMP_DYING_GASP_ENABLED_PARAM);
          if (dyingGaspsString != null) {
            int realValue = MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE, dyingGaspsString);
            if (realValue == BooleanTypeTranslation.NOT_APPLICABLE.getMIBValue()) {
              throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageIllegal(
                      LayeredParams.LrPropAdvaEthernet.SNMP_DYING_GASP_ENABLED_PARAM));
            }
            props.set(NTUSPPropertiesFSP150CM.VI.SNMPDyingGaspEnabled, realValue);
          }
        }
        catch (JAXBException ex) {
          logger.warn(ex);
        }
      }
    }
    return props;
  }

  public static NetworkElementSPPropertiesFSP150CM mtosiVendorExtensionsTToNEProperties(EqVendorExtensionsT vendorExtensions, String moduleType) throws ProcessingFailureException {
    NetworkElementSPPropertiesFSP150CM props = new NetworkElementSPPropertiesFSP150CM();
    List<Object> objects = vendorExtensions.getAny();
    for (Object object : objects) {
      ElementNSImpl el = (ElementNSImpl) object;

      if (el.getNamespaceURI() != null && el.getNamespaceURI().equals(MtosiConstants.VENDOR_TMF_NAMESPACE) && el.getLocalName() != null
          && el.getLocalName().equals(MtosiConstants.VENDOR_TRANSMISSION_PARAMS)) {
        JAXBContext jc;
        Unmarshaller u;

        try {
          jc = JAXBContext.newInstance("v1.tmf854");
          u = jc.createUnmarshaller();
        } catch (JAXBException ex) {
          throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.JAXB_MARSHALLING_PROBLEM, ex);
        }

        try {
          LayeredParametersListT transmissionParams = u.unmarshal(el, LayeredParametersListT.class).getValue();
          
          NVSListT layer = LayeredParameterUtils.findLayer(transmissionParams, LayeredParams.PROP_ADVA_RemoteCPE);
          // If the layer is null, then we don't create the CPMR
          if (layer == null) {
            return props;
          }
          /**
           * If the layer does exists, then CPEHostname and RemoteType are mandatory parameters
           */
          if (moduleType.equals(MtosiConstants.EQUIPMENT_NTE_GE_SYNC)){
            throw ServiceUtils.createNewPFE( ExceptionUtils.EXCPT_INVALID_INPUT,
                "RemoteType not supported for "+ MtosiConstants.EQUIPMENT_NTE_GE_SYNC );
          }
          // CPEHostName
          String cpeHostName = LayeredParameterUtils.getLayeredParameter(transmissionParams, LayeredParams.PROP_ADVA_RemoteCPE,
              LayeredParams.LrPropAdvaRemoteCPE.CPE_HOSTNAME_PARM);
          if (cpeHostName != null) {
            if (cpeHostName.trim().length() == 0) {
              throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaRemoteCPE.CPE_HOSTNAME_PARM));
            }
            props.set(NetworkElementSPPropertiesFSP150CM.VS.SysName, cpeHostName);
          } else {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.CPE_HOSTNAME_MANDATORY);
          }
          // RemoteType
          String remoteType = LayeredParameterUtils.getLayeredParameter(transmissionParams, LayeredParams.PROP_ADVA_RemoteCPE,
              LayeredParams.LrPropAdvaRemoteCPE.REMOTE_TYPE_PARM);
          if (remoteType != null) {
            Integer remoteTypeNumber = MtosiUtils.getMIBValue(CMDeviceTypeTranslation.NOT_APPLICABLE,remoteType);
            if (remoteTypeNumber.intValue() == CMDeviceTypeTranslation.NOT_APPLICABLE.getMIBValue() ) { 
              throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaRemoteCPE.REMOTE_TYPE_PARM));
            }
            props.set(NetworkElementSPPropertiesFSP150CM.VI.Type, remoteTypeNumber);
          } else {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
                MtosiErrorConstants.REMOTE_TYPE_MANDATORY);
          }
          // CreateLink
          String createLink = LayeredParameterUtils.getLayeredParameter(transmissionParams, LayeredParams.PROP_ADVA_RemoteCPE,
              LayeredParams.LrPropAdvaRemoteCPE.CREATE_LINK_PARM);
          if (createLink != null) {
            Integer createLinkBoolean = MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE,createLink);
            if (createLinkBoolean.intValue() == BooleanTypeTranslation.NOT_APPLICABLE.getMIBValue() ) { 
              throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
                  MtosiErrorConstants.getMessageIllegal(LayeredParams.LrPropAdvaRemoteCPE.CREATE_LINK_PARM));
            }
            if (createLinkBoolean.intValue() == BooleanTypeTranslation.ENABLED.getMIBValue() ) {
            // LinkName
              String linkName = LayeredParameterUtils.getLayeredParameter(transmissionParams, LayeredParams.PROP_ADVA_RemoteCPE,
                  LayeredParams.LrPropAdvaRemoteCPE.LINK_NAME_PARM);
              if (linkName != null) {
                props.set(NetworkElementSPPropertiesFSP150CM.VS.LinkName, linkName);
              } else {
                props.set(NetworkElementSPPropertiesFSP150CM.VS.LinkName, "");
              }
            }
          }
        } catch (JAXBException ex) {
          logger.warn(ex);
        }
      }
    }
    return props;
  }

  public static ModuleCardSPPropertiesFSP150CM mtosiVendorExtensionsTToModuleCardProperties(EqVendorExtensionsT vendorExtensions)
          throws ProcessingFailureException {
    ModuleCardSPPropertiesFSP150CM props = new ModuleCardSPPropertiesFSP150CM();
    List<Object> objects = vendorExtensions.getAny();
    for (Object object : objects) {
      ElementNSImpl el = (ElementNSImpl) object;
      if (el.getNamespaceURI() != null && el.getNamespaceURI().equals(MtosiConstants.VENDOR_NAMESPACE) &&
              el.getLocalName() != null && el.getLocalName().equals(MtosiConstants.VENDOR_ADMINISTRATION_CONTROL) &&
              el.getFirstChild() != null) {
        String adminControl = el.getFirstChild().getTextContent();
        if (adminControl != null) {
          Integer adminControlValue = MtosiUtils.getMIBValue(CMAdministrationControlTranslation.NOT_APPLICABLE, adminControl);
          if (adminControlValue == CMAdministrationControlTranslation.NOT_APPLICABLE.getMIBValue()) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.ADMINISTRATION_CONTROL_ILLEGAL_VALUE);
          }
          props.set(EquipmentSPProperties.VI.AdminState, adminControlValue);
        }
      }
    }
    return props;
  }

  public static ModuleCPMRSPPropertiesFSP150CM mtosiVendorExtensionsTToCPMRProperties(EqVendorExtensionsT vendorExtensions)
          throws ProcessingFailureException {
    ModuleCPMRSPPropertiesFSP150CM props = new ModuleCPMRSPPropertiesFSP150CM();
    List<Object> objects = vendorExtensions.getAny();
    for (Object object : objects) {
      ElementNSImpl el = (ElementNSImpl) object;

      if (el.getNamespaceURI() != null && el.getNamespaceURI().equals(MtosiConstants.VENDOR_NAMESPACE) &&
              el.getLocalName() != null && el.getLocalName().equals(MtosiConstants.VENDOR_ADMINISTRATION_CONTROL) &&
              el.getFirstChild() != null) {
        String adminControl = el.getFirstChild().getTextContent();
        if (adminControl != null) {
          Integer adminControlValue = MtosiUtils.getMIBValue(CMAdministrationControlTranslation.NOT_APPLICABLE, adminControl);
          if (adminControlValue == CMAdministrationControlTranslation.NOT_APPLICABLE.getMIBValue()) {
            throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.ADMINISTRATION_CONTROL_ILLEGAL_VALUE);
          }
          props.set(EquipmentSPProperties.VI.AdminState, adminControlValue);
        }
      }
      else if (el.getNamespaceURI() != null && el.getNamespaceURI().equals(MtosiConstants.VENDOR_TMF_NAMESPACE) &&
              el.getLocalName() != null && el.getLocalName().equals(MtosiConstants.VENDOR_TRANSMISSION_PARAMS)) {
        JAXBContext jc;
        Unmarshaller u;

        try {
          jc = JAXBContext.newInstance("v1.tmf854");
          u = jc.createUnmarshaller();
        } catch (JAXBException ex) {
          throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.JAXB_MARSHALLING_PROBLEM, ex);
        }

        try {
          int netToAccLLF = 2;
          int accToAccLLF = 2;

          LayeredParametersListT transmissionParams = u.unmarshal(el,LayeredParametersListT.class).getValue();

          String netToAccLLFString = LayeredParameterUtils.getLayeredParameter(transmissionParams,
                  LayeredParams.PROP_ADVA_ETHERNET, LayeredParams.LrPropAdvaEthernet.NETWORK_TO_ACCESS_LINK_LOSS_FORWARDING_PARAM);
          if (netToAccLLFString != null) {
            netToAccLLF = MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE, netToAccLLFString);
            if (netToAccLLF == BooleanTypeTranslation.NOT_APPLICABLE.getMIBValue()) {
              throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageIllegal(
                      LayeredParams.LrPropAdvaEthernet.NETWORK_TO_ACCESS_LINK_LOSS_FORWARDING_PARAM));
            }
//            setNetToAccLLF(props, realValue);
          }

          String accToAccLLFString = LayeredParameterUtils.getLayeredParameter(transmissionParams,
                  LayeredParams.PROP_ADVA_ETHERNET, LayeredParams.LrPropAdvaEthernet.ACCESS_TO_ACCESS_LINK_LOSS_FORWARDING_PARAM);
          if (accToAccLLFString != null) {
            accToAccLLF = MtosiUtils.getMIBValue(BooleanTypeTranslation.NOT_APPLICABLE, accToAccLLFString);
            if (accToAccLLF == BooleanTypeTranslation.NOT_APPLICABLE.getMIBValue()) {
              throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.getMessageIllegal(
                      LayeredParams.LrPropAdvaEthernet.ACCESS_TO_ACCESS_LINK_LOSS_FORWARDING_PARAM));
            }
//            setAccToAccLLF(props, realValue);
          }

          props.set(ModuleCPMRSPPropertiesFSP150CM.VI.LlfModeAction, LLFModeAction.getLLFModeActionValue(accToAccLLF,netToAccLLF));

        }
        catch (JAXBException ex) {
          logger.warn(ex);
        }
      }
    }
    return props;
  }

  private static void setAccToAccLLF (ModuleCPMRSPPropertiesFSP150CM props, int accToAccValue) {
    // this translation is based on the following description:
    // ethernetCPMRCardLLFModeAction
    // 1=llfmode-none 2=llfmode-acc2acc 3=llfmode-net2acc 4=llfmode-both
    int newValue = 1;
    if (props.get(ModuleCPMRSPPropertiesFSP150CM.VI.LlfModeAction) != null) {
      newValue = props.get(ModuleCPMRSPPropertiesFSP150CM.VI.LlfModeAction);
    }
    if (accToAccValue == BooleanTypeTranslation.ENABLED.getMIBValue()) {
      switch (newValue) {
        case 1:
          newValue = 2;
          break;
        case 3:
          newValue = 4;
          break;
      }
    }
    else {
      switch (newValue) {
        case 2:
          newValue = 1;
          break;
        case 4:
          newValue = 3;
          break;
      }
    }
    props.set(ModuleCPMRSPPropertiesFSP150CM.VI.LlfModeAction, newValue);
  }

  private static void setNetToAccLLF (ModuleCPMRSPPropertiesFSP150CM props, int netToAccValue) {
    // this translation is based on the following description:
    // ethernetCPMRCardLLFModeAction
    // 1=llfmode-none 2=llfmode-acc2acc 3=llfmode-net2acc 4=llfmode-both
    int newValue = 1;
    if (props.get(ModuleCPMRSPPropertiesFSP150CM.VI.LlfModeAction) != null) {
      newValue = props.get(ModuleCPMRSPPropertiesFSP150CM.VI.LlfModeAction);
    }
    if (netToAccValue == BooleanTypeTranslation.ENABLED.getMIBValue()) {
      switch (newValue) {
        case 1:
          newValue = 3;
          break;
        case 2:
          newValue = 4;
          break;
      }
    } else {
      switch (newValue) {
        case 3:
          newValue = 1;
          break;
        case 4:
          newValue = 2;
          break;
      }
    }
    props.set(ModuleCPMRSPPropertiesFSP150CM.VI.LlfModeAction, newValue);
  }
}
