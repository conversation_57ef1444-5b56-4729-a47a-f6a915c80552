/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import jakarta.xml.ws.Holder;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.GetContinuityTestResponseT;
import ws.v1.tmf854.ProcessingFailureException;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;

public abstract class GetContinuityTestWorker extends AbstractMtosiWorker {
  protected GetContinuityTestResponseT response = new GetContinuityTestResponseT();
  protected NamingAttributesT namingAttributes;
  protected NetworkElement ne;
  protected Port port;

  public GetContinuityTestWorker(Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne){
    super(mtosiHeader, "getContinuityTest", "getContinuityTest", "getContinuityTestResponse");
    this.namingAttributes = namingAttributes;
    this.ne = ne;
  }

  @Override
  public GetContinuityTestResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }

  @Override
  protected void parse() throws Exception {
    if (namingAttributes.getPtpNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ptpNm has not been specified.");
    }
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
  	validator.validate(ne.getDefaultNetworkElementTypeString());
  }

  @Override
  protected void mediate() throws Exception {
    if ((port = ManagedElementFactory.getPort(namingAttributes)) == null){
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              "The specified parent PTP was not found.");
    }
  }
}
