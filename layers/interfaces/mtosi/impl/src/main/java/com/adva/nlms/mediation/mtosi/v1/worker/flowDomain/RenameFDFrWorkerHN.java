/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPPropertiesHN4000;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.hn4000.mtosi.FDFrHN4000;
import com.adva.nlms.mediation.config.mtosi.FDFr;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import v1.tmf854.HeaderT;

import jakarta.xml.ws.Holder;

public class RenameFDFrWorkerHN extends RenameFDFrWorker
{

	FDFrHN4000 fdfrHn = null;
  private String oldFdfrName;

	public RenameFDFrWorkerHN(Holder<HeaderT> mtosiHeader, NetworkElement ne, FDFr fdfr, String newFDFrName)
	{
		super(mtosiHeader, ne, fdfr, newFDFrName);
	}

  @Override
  protected void parse () throws Exception {
    // empty method
  }

  @Override
  protected void mediate() throws Exception
	{
		fdfrHn = (FDFrHN4000) fdfr;

		FDFrSPPropertiesHN4000 fdfrProps = (FDFrSPPropertiesHN4000) fdfrHn.getFDFrSPProperties();
    oldFdfrName = fdfrProps.get(FDFrSPProperties.VS.FDFrName);
		Long evcId = fdfrProps.get(FDFrSPPropertiesHN4000.VL.EvcID);
		final FDFrSPPropertiesHN4000 newProps = new FDFrSPPropertiesHN4000();
		
		newProps.set(FDFrSPProperties.VS.FDFrName, newFDFrName);
		newProps.set(FDFrSPPropertiesHN4000.VL.EvcID, evcId);
		
		transact(newProps);

	}

	private void transact(FDFrSPPropertiesHN4000 props) throws NetTransactionException, SPValidationException, SNMPCommFailure, ObjectInUseException
	{
		NetworkElement locks[] = new NetworkElement[]
		{ ne };
		Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "RenameFDFrWorker");

		try
		{

			fdfrHn.setFdfrProperties(props);

			NetTransactionManager.commitNetTransaction(id);
//      ne.logSROperation(SROperationState.SERVICE_RENAME_SUCCESS, oldFdfrName, props.get(FDFrSPProperties.VS.FDFrName));
		}
		catch (NetTransactionException e)
		{
			ne.logSROperation(SROperationState.SERVICE_RENAME_FAILURE, props.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (SPValidationException e)
		{
			ne.logSROperation(SROperationState.SERVICE_RENAME_FAILURE, props.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (SNMPCommFailure e)
		{
			ne.logSROperation(SROperationState.SERVICE_RENAME_FAILURE, props.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		finally
		{
			NetTransactionManager.ensureEnd(id);
		}
	}
}
