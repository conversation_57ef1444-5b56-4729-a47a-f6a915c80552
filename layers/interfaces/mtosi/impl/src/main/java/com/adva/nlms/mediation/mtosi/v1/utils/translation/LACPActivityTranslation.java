/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum LACPActivityTranslation implements TranslatableEnum {
  PASSIVE        (0, "Passive"),
  ACTIVE         (1, "Active"),
  NOT_APPLICABLE (4, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private LACPActivityTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}