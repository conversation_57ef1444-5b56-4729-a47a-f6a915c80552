/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.mediation;

import com.adva.nlms.common.InstallationState;
import com.adva.nlms.common.config.ResponseStatus;
import com.adva.nlms.mediation.common.serviceProvisioning.EquipmentSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.NetworkElementSPProperties;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.HeaderUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.AdministrationControlTranslation;
import jakarta.xml.bind.JAXBElement;
import v1.tmf854.CommunicationStateT;
import v1.tmf854.EqVendorExtensionsT;
import v1.tmf854.EquipmentHolderT;
import v1.tmf854.EquipmentOrHolderT;
import v1.tmf854.EquipmentT;
import v1.tmf854.MEVendorExtensionsT;
import v1.tmf854.ManagedElementT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ResourceStateT;
import v1.tmf854.ServiceStateEnumT;
import v1.tmf854.ServiceStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;

import javax.xml.namespace.QName;

/**
 * This class is a generic Network Element Mediator - contains abstract methods.
 */
public abstract class NetworkElementMediator {
  private NetworkElement ne;

  public NetworkElementMediator(NetworkElement ne) {
    this.ne = ne;
  }

  public NamingAttributesT createNamingAttributesMe() {
    NamingAttributesT namingAttributes = new NamingAttributesT();
    namingAttributes.setMdNm(OSFactory.getMDNm());
    namingAttributes.setMeNm(ne.getName());
    return namingAttributes;
  }

  public ManagedElementT toManagedElement() {
    ObjectFactory objFactory = new ObjectFactory();
    NetworkElementSPProperties props = ne.getSysInfo();
    ManagedElementT managedElement = objFactory.createManagedElementT();
    // Name
    NamingAttributesT namingAttributes = createNamingAttributesMe();
    JAXBElement<NamingAttributesT> managedElementName = objFactory.createManagedElementTName(namingAttributes);
    managedElement.setName(managedElementName);
    // Discovered Name
    managedElement.setDiscoveredName(props.get(NetworkElementSPProperties.VS.SysName));
    // Naming OS
    JAXBElement<String> namingOS = objFactory.createManagedElementTNamingOS(OSFactory.getNmsName());
    managedElement.setNamingOS(namingOS);
    // User Label
    // Not supported.
    // Source
    // Hardcoded - Discovered from an EMS (not OS and not directly from the ME).
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    JAXBElement<SourceT> managedElementSource = objFactory.createManagedElementTSource(source);
    managedElement.setSource(managedElementSource);
    // Owner
    JAXBElement<String> owner = objFactory.createManagedElementTOwner(props.get(NetworkElementSPProperties.VS.SysContact));
    managedElement.setOwner(owner);
    // Alias Name List
    // Not supported.
    // Resource State
    ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ne.isDiscovered() ? ResourceStateEnumT.INSTALLED : ResourceStateEnumT.PLANNED);
    JAXBElement<ResourceStateT> managedElementResourceState = objFactory.createManagedElementTResourceState(resourceState);
    managedElement.setResourceState(managedElementResourceState);
    // Location
    JAXBElement<String> location = objFactory.createManagedElementTLocation(props.get(NetworkElementSPProperties.VS.SysLocation));
    managedElement.setLocation(location);
    // Manufacturer
    JAXBElement<String> managedElementManufacturer = objFactory.createManagedElementTManufacturer(ne.getMTOSIWorker().getManufacturer());
    managedElement.setManufacturer(managedElementManufacturer);
    // Manufacturer date
    JAXBElement<String> manufacturerDate = objFactory.createEquipmentTManufacturerDate(ne.getMTOSIWorker().getManufactureDate());
    managedElement.setManufacturerDate(manufacturerDate);
    // Product Name
    JAXBElement<String> productName = objFactory.createManagedElementTProductName(ne.getMTOSIWorker().getProductName());
    managedElement.setProductName(productName);
    // Version
    JAXBElement<String> version = objFactory.createManagedElementTVersion(ne.getCurrentNemiSoftwareVersion());
    managedElement.setVersion(version);
    // Communication State
    CommunicationStateT commState;
    ResponseStatus snmpResponseStatus = ne.getSNMPResponseStatus();
    if (snmpResponseStatus == ResponseStatus.RESPONDING) {
      commState = CommunicationStateT.CS_AVAILABLE;
    } else {
      commState = CommunicationStateT.CS_UNAVAILABLE;
    }
    JAXBElement<CommunicationStateT> communicationState = objFactory.createManagedElementTCommunicationState(commState);
    managedElement.setCommunicationState(communicationState);
    // In Sync State
    JAXBElement<Boolean> syncState = objFactory.createManagedElementTInSyncState(props.get(NetworkElementSPProperties.VB.SyncState)
            && commState == CommunicationStateT.CS_AVAILABLE);
    managedElement.setInSyncState(syncState);
    // Vendor Extensions
    managedElement.setVendorExtensions(getVendorExtensions());
    return managedElement;
  }

  abstract protected JAXBElement<MEVendorExtensionsT> getVendorExtensions();

  protected JAXBElement<EqVendorExtensionsT> getVendorExtensions(EquipmentSPProperties props) {
    ObjectFactory objFactory = new ObjectFactory();
    EqVendorExtensionsT extensions = new EqVendorExtensionsT();
    String adminControl = "";
    if (props.get(EquipmentSPProperties.VI.AdminState) < 4) {
      adminControl = MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, props.get(EquipmentSPProperties.VI.AdminState));
    }
    extensions.getAny().add(new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_ADMINISTRATION_CONTROL), String.class, adminControl));
    return objFactory.createEquipmentTVendorExtensions(extensions);
  }

  abstract public String getRelativeNameForProperties(EquipmentSPProperties properties);

  public EquipmentOrHolderT psuToMtosiEquipmentHolder(EquipmentSPProperties properties) {
    NamingAttributesT namingAttributes = NamingTranslationFactory.createNamingAttributesHolder(ne, properties);
    ObjectFactory objFactory = new ObjectFactory();
    EquipmentHolderT equipmentHolder = objFactory.createEquipmentHolderT();
    EquipmentOrHolderT holder = createHolderAndFillBasicParameters(equipmentHolder, properties, MtosiConstants.EQUIPMENT_PSU);

    // manufacturerDate
    equipmentHolder.setManufacturerDate(objFactory.createEquipmentTManufacturerDate(""));

    // expectedOrInstalledEquipment
    NamingAttributesT namingEquipment = NamingTranslationFactory.cloneNamingAttributes(namingAttributes);
    namingEquipment.setEqNm(MtosiConstants.DEFAULT_EQUIPMENT_NAME);
    setExpectedOrInstalledEquipment(equipmentHolder, namingEquipment, properties);

    return holder;
  }

  protected void setExpectedOrInstalledEquipment (EquipmentHolderT equipmentHolder, NamingAttributesT namingEquipment, EquipmentSPProperties properties) {
    ObjectFactory objFactory = new ObjectFactory();
    if (MtosiUtils.isInstalled(properties)) {
      equipmentHolder.setExpectedOrInstalledEquipment(objFactory.createEquipmentHolderTExpectedOrInstalledEquipment(namingEquipment));
    } else {
      equipmentHolder.setExpectedOrInstalledEquipment(objFactory.createEquipmentHolderTExpectedOrInstalledEquipment(new NamingAttributesT()));
    }
  }

  public EquipmentOrHolderT psuToMtosiEquipment(EquipmentSPProperties properties) {
    ObjectFactory objFactory = new ObjectFactory();
    EquipmentT equipment = objFactory.createEquipmentT();
    EquipmentOrHolderT holder = objFactory.createEquipmentOrHolderT();

    fillBasicParameters(equipment, properties, MtosiConstants.EQUIPMENT_PSU);

    // vendorExtensions
    equipment.setVendorExtensions(getEquipmentVendorExtensions(properties));

    holder.setEq(equipment);
    return holder;
  }

  protected ServiceStateT getServiceState (EquipmentSPProperties properties, String objectType) {
    return MtosiUtils.getServiceState(properties.get(EquipmentSPProperties.VI.AdminState), properties.get(EquipmentSPProperties.VI.OperState));
  }

  protected JAXBElement<EqVendorExtensionsT> getEquipmentVendorExtensions(EquipmentSPProperties props) {
    ObjectFactory objFactory = new ObjectFactory();
    EqVendorExtensionsT extensions = new EqVendorExtensionsT();
    String adminControl = "";
    if (props.get(EquipmentSPProperties.VI.AdminState) < 4) {
      adminControl = MtosiUtils.getMtosiString(AdministrationControlTranslation.NOT_APPLICABLE, props.get(EquipmentSPProperties.VI.AdminState));
    }
    JAXBElement<? extends String> je = new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_ADMINISTRATION_CONTROL), String.class, adminControl);
    extensions.getAny().add(je);
    return objFactory.createEquipmentTVendorExtensions(extensions);
  }

  public EquipmentOrHolderT fanToMtosiEquipment(EquipmentSPProperties properties) {
    ObjectFactory objFactory = new ObjectFactory();
    EquipmentT equipment = objFactory.createEquipmentT();
    EquipmentOrHolderT holder = objFactory.createEquipmentOrHolderT();

    fillBasicParameters(equipment, properties, MtosiConstants.EQUIPMENT_FAN);
    
    

    // vendorExtensions
    equipment.setVendorExtensions(getEquipmentVendorExtensions(properties));

    holder.setEq(equipment);
    return holder;
  }

  public EquipmentOrHolderT fanToMtosiEquipmentHolder(EquipmentSPProperties properties) {
    NamingAttributesT namingAttributes = NamingTranslationFactory.createNamingAttributesHolder(ne, properties);
    ObjectFactory objFactory = new ObjectFactory();
    EquipmentHolderT equipmentHolder = objFactory.createEquipmentHolderT();
    EquipmentOrHolderT holder = createHolderAndFillBasicParameters(equipmentHolder, properties, MtosiConstants.EQUIPMENT_FAN);

    // manufacturerDate
    equipmentHolder.setManufacturerDate(objFactory.createEquipmentTManufacturerDate(HeaderUtils.formatDate(properties.get(EquipmentSPProperties.VL.ManufactureDate))));

    // expectedOrInstalledEquipment
    NamingAttributesT namingEquipment = NamingTranslationFactory.cloneNamingAttributes(namingAttributes);
    namingEquipment.setEqNm(MtosiConstants.DEFAULT_EQUIPMENT_NAME);
    equipmentHolder.setExpectedOrInstalledEquipment(objFactory.createEquipmentHolderTExpectedOrInstalledEquipment(namingEquipment));

    return holder;
  }

  private EquipmentOrHolderT createHolderAndFillBasicParameters (EquipmentHolderT equipmentHolder, EquipmentSPProperties properties, String objectType) {
    String nameRelative = NetworkElementMediatorFactory.createNetworkElementMediator(ne).getRelativeNameForProperties(properties);
    NamingAttributesT namingAttributes = NamingTranslationFactory.createNamingAttributesHolder(ne, properties);
    ObjectFactory objFactory = new ObjectFactory();
    EquipmentOrHolderT holder = objFactory.createEquipmentOrHolderT();

    // discoveredName
    equipmentHolder.setDiscoveredName(objFactory.createEquipmentTDiscoveredName(nameRelative));

    // name
    equipmentHolder.setName(objFactory.createEquipmentTName(namingAttributes));

    // alarmReporting
    equipmentHolder.setAlarmReportingIndicator(objFactory.createEquipmentTAlarmReportingIndicator(Boolean.FALSE));

    // manufacturer
    equipmentHolder.setManufacturer(objFactory.createEquipmentTManufacturer(ne.getMTOSIWorker().getManufacturer()));

    // source
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    equipmentHolder.setSource(objFactory.createEquipmentTSource(source));

    // namingOS
    equipmentHolder.setNamingOS(objFactory.createEquipmentTNamingOS(OSFactory.getNmsName()));

    // resourceState
    ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ResourceStateEnumT.INSTALLED);
    equipmentHolder.setResourceState(objFactory.createEquipmentTResourceState(resourceState));

    holder.setEh(equipmentHolder);

    // holderType
    equipmentHolder.setHolderType(objFactory.createEquipmentHolderTHolderType(MtosiConstants.HOLDER_SLOT));

    if (MtosiConstants.EQUIPMENT_PSU.equals(objectType)) {
    	getAcceptablePSUEquipmentTypes(objFactory, equipmentHolder,properties);
    } else {
	    // acceptableEquipmentTypeList
	    EquipmentHolderT.AcceptableEquipmentTypeList list = objFactory.createEquipmentHolderTAcceptableEquipmentTypeList();
	    list.getAcceptableEquipmentType().add(objectType);
	    equipmentHolder.setAcceptableEquipmentTypeList(objFactory.createEquipmentHolderTAcceptableEquipmentTypeList(list));
    }
    // holderState
    equipmentHolder.setHolderState(objFactory.createEquipmentHolderTHolderState(MtosiUtils.getHolderState(properties.get(EquipmentSPProperties.VI.InstallState))));

    return holder;
  }

  protected void getAcceptablePSUEquipmentTypes( ObjectFactory objFactory, EquipmentHolderT equipmentHolder, EquipmentSPProperties properties) {
	    EquipmentHolderT.AcceptableEquipmentTypeList list = objFactory.createEquipmentHolderTAcceptableEquipmentTypeList();
	    list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_PSU_AC);
	    list.getAcceptableEquipmentType().add(MtosiConstants.EQUIPMENT_PSU_DC);
	    equipmentHolder.setAcceptableEquipmentTypeList(objFactory.createEquipmentHolderTAcceptableEquipmentTypeList(list));
}

private void fillBasicParameters (EquipmentT equipment, EquipmentSPProperties properties, String objectType) {
    ObjectFactory objFactory = new ObjectFactory();
    NamingAttributesT namingAttributes = NamingTranslationFactory.createNamingAttributesEquipment(ne, properties);

    // discoveredName
    equipment.setDiscoveredName(objFactory.createEquipmentTDiscoveredName(MtosiConstants.DEFAULT_EQUIPMENT_NAME));

    // name
    equipment.setName(objFactory.createEquipmentTName(namingAttributes));

    // alarmReporting
    equipment.setAlarmReportingIndicator(objFactory.createEquipmentTAlarmReportingIndicator(Boolean.FALSE));

    // partNumber
    equipment.setInstalledPartNumber(objFactory.createEquipmentTInstalledPartNumber(properties.get(EquipmentSPProperties.VS.PartNumber)));

    // serialNumber
    equipment.setInstalledSerialNumber(objFactory.createEquipmentTInstalledSerialNumber(properties.get(EquipmentSPProperties.VS.SerialNumber)));

    // version
    equipment.setInstalledVersion(objFactory.createEquipmentTInstalledVersion(properties.get(EquipmentSPProperties.VS.HardwareVersion)));

    // manufacturer
    equipment.setManufacturer(objFactory.createEquipmentTManufacturer(ne.getMTOSIWorker().getManufacturer()));

    // manufacturerDate
    equipment.setManufacturerDate(objFactory.createEquipmentTManufacturerDate(HeaderUtils.formatDate(properties.get(EquipmentSPProperties.VL.ManufactureDate))));

    // source
    SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_EMS);
    equipment.setSource(objFactory.createEquipmentTSource(source));

    // namingOS
    equipment.setNamingOS(objFactory.createEquipmentTNamingOS(OSFactory.getNmsName()));

    // resourceState
    ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(MtosiUtils.getResourceState(properties.get(EquipmentSPProperties.VI.InstallState)));
    equipment.setResourceState(objFactory.createEquipmentTResourceState(resourceState));

    // serviceState
    equipment.setServiceState(objFactory.createEquipmentTServiceState(getServiceState(properties, objectType)));

    if (MtosiConstants.EQUIPMENT_PSU.equals(objectType)) {
    	getExpectedAndInstalledPSUEquipment(objFactory, equipment,properties);
    } else {
	
	    // expectedEquipmentObject
	    equipment.setExpectedEquipmentObjectType(objFactory.createEquipmentTExpectedEquipmentObjectType(objectType));
	
	    // installedEquipment
	    String installedEquipment = null;
	    switch (properties.get(EquipmentSPProperties.VI.InstallState)) {
	      case InstallationState.PRESENT:
	        installedEquipment = objectType;
	        break;
	      case InstallationState.REMOVED:
	        break;
	      case InstallationState.REINSERTED:
	        installedEquipment = objectType;
	        break;
	    }
	    if (installedEquipment != null) {
	      equipment.setInstalledEquipmentObjectType(objFactory.createEquipmentTInstalledEquipmentObjectType(installedEquipment));
	    }
    }
  }

  private void getExpectedAndInstalledPSUEquipment(ObjectFactory objFactory, EquipmentT equipment, EquipmentSPProperties properties) {
		
	  String objectType = getPsuType(properties);
	    // expectedEquipmentObject
	    equipment.setExpectedEquipmentObjectType(objFactory.createEquipmentTExpectedEquipmentObjectType(objectType));
	
	    // installedEquipment
	    String installedEquipment = "";
	    switch (properties.get(EquipmentSPProperties.VI.InstallState)) {
	      case InstallationState.PRESENT:
	        installedEquipment = objectType;
	        break;
	      case InstallationState.REMOVED:
	        break;
	      case InstallationState.REINSERTED:
	        installedEquipment = objectType;
	        break;
	    }
      equipment.setInstalledEquipmentObjectType(objFactory.createEquipmentTInstalledEquipmentObjectType(installedEquipment));
  }

  /**
   * Extract the appropriate PSU Type from the properties.
   * Since it is NE specific, pass it off to the sub classes to figure out.
   * @param properties
   * @return
   */
  protected abstract String getPsuType(EquipmentSPProperties properties);



public EquipmentOrHolderT sfpToMtosiEquipment(EquipmentSPProperties properties) {
    ObjectFactory objFactory = new ObjectFactory();
    EquipmentT equipment = objFactory.createEquipmentT();
    EquipmentOrHolderT holder = objFactory.createEquipmentOrHolderT();

    fillBasicParameters(equipment, properties, MtosiConstants.EQUIPMENT_SFP);

    // manufacturer
    equipment.setManufacturer(objFactory.createEquipmentTManufacturer(properties.get(EquipmentSPProperties.VS.Manufacturer)));

    // serviceState
    ServiceStateT serviceState = new ServiceStateT();
    serviceState.setValue(ServiceStateEnumT.IN_SERVICE);
    equipment.setServiceState(objFactory.createEquipmentTServiceState(serviceState));

    // vendorExtensions
    equipment.setVendorExtensions(getSFPVendorExtensions());

    holder.setEq(equipment);
    return holder;
  }

  protected JAXBElement<EqVendorExtensionsT> getSFPVendorExtensions() {
    ObjectFactory objFactory = new ObjectFactory();
    EqVendorExtensionsT extensions = new EqVendorExtensionsT();
    String adminControl = AdministrationControlTranslation.ENABLED.getMtosiString();
    JAXBElement<? extends String> je = new JAXBElement<String>(new QName(MtosiConstants.VENDOR_NAMESPACE,
            MtosiConstants.VENDOR_ADMINISTRATION_CONTROL), String.class, adminControl);
    extensions.getAny().add(je);
    return objFactory.createEquipmentTVendorExtensions(extensions);
  }

  public EquipmentOrHolderT sfpToMtosiEquipmentHolder(EquipmentSPProperties properties) {
    NamingAttributesT namingAttributes = NamingTranslationFactory.createNamingAttributesHolder(ne, properties);
    ObjectFactory objFactory = new ObjectFactory();
    EquipmentHolderT equipmentHolder = objFactory.createEquipmentHolderT();
    EquipmentOrHolderT holder = createHolderAndFillBasicParameters(equipmentHolder, properties, MtosiConstants.EQUIPMENT_SFP);

    // manufacturerDate
    equipmentHolder.setManufacturerDate(getSfpManufacturerDate(properties));

    equipmentHolder.setHolderType(objFactory.createEquipmentHolderTHolderType(MtosiConstants.HOLDER_SUBSLOT));

    // expectedOrInstalledEquipment
    NamingAttributesT namingEquipment = NamingTranslationFactory.cloneNamingAttributes(namingAttributes);
    namingEquipment.setEqNm(MtosiConstants.DEFAULT_EQUIPMENT_NAME);
    setExpectedOrInstalledSfpEquipment(equipmentHolder, namingEquipment, properties);

    return holder;
  }

  protected void setExpectedOrInstalledSfpEquipment (EquipmentHolderT equipmentHolder, NamingAttributesT namingEquipment, EquipmentSPProperties properties) {
    setExpectedOrInstalledEquipment(equipmentHolder, namingEquipment, properties);
  }

  protected JAXBElement<String> getSfpManufacturerDate (EquipmentSPProperties properties) {
    ObjectFactory objFactory = new ObjectFactory();
    return objFactory.createEquipmentTManufacturerDate("");
  }

  public EquipmentOrHolderT shelfToMtosiEquipment(EquipmentSPProperties properties) {
    ObjectFactory objFactory = new ObjectFactory();
    EquipmentT equipment = objFactory.createEquipmentT();
    EquipmentOrHolderT holder = objFactory.createEquipmentOrHolderT();

    fillBasicParameters(equipment, properties, getShelfEquipment(properties));

    // serviceState
    equipment.setServiceState(objFactory.createEquipmentTServiceState(getShelfServiceState(properties)));

    // vendorExtensions
    equipment.setVendorExtensions(getVendorExtensions(properties));

    holder.setEq(equipment);
    return holder;
  }

  abstract protected String getShelfEquipment (EquipmentSPProperties properties);

  protected ServiceStateT getShelfServiceState (EquipmentSPProperties properties) {
    ServiceStateT serviceState = new ServiceStateT();
    serviceState.setValue(ServiceStateEnumT.IN_SERVICE);
    return serviceState;
  }
}
