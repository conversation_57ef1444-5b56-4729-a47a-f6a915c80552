/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

/**
 * Created by IntelliJ IDEA. User: mariuszg Date: 2007-06-25 Time: 10:13:25 To change this template use File | Settings
 * | File Templates.
 */
public enum TestStatusTranslation
{
  INITIAL        (0, "Initial"),
  INPROGRESS     (1, "In-Progress"),
  FINISHED       (2, "Finished"),
  NOT_APPLICABLE (3, "n/a");


  //------------------------------------------------------------------------------------------------------------------
  private final int    mibValue;
  private final String mtosiString;
  //------------------------------------------------------------------------------------------------------------------

  /**
   * Constructor.
   * @param mibValue    The MIB defined value
   * @param mtosiString  The string representation used in MTOSI layer.
   */
  private TestStatusTranslation (final int mibValue, final String mtosiString)
  {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  /**
   * Returns the MIB defined value.
   * @return the MIB defined value.
   */
  public int getMIBValue() {
    return mibValue;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @return the string representation used in MTOSI layer.
   */
  public String getMtosiString() {
    return mtosiString;
  }

  /**
   * Returns the string representation used in MTOSI layer.
   * @param mibValue  The MIB defined value
   * @return the string representation used in MTOSI layer.
   */
  public static String getMtosiString(final int mibValue)
  {
    TestStatusTranslation cfmTypeTranslation = NOT_APPLICABLE;  // the return value

    for (TestStatusTranslation tmpCfmTypeTranslation : values())
    {
      if (mibValue == tmpCfmTypeTranslation.getMIBValue())
      {
        cfmTypeTranslation = tmpCfmTypeTranslation;
        break;
      }
    }
    return cfmTypeTranslation.getMtosiString();
  }

}
