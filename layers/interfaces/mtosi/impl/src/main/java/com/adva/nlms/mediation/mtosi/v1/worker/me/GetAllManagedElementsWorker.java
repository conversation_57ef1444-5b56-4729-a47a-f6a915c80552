/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON>rizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.mediation.config.ConfigCtrlImpl;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.toMtosi.ManagedElementMediator;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidationHelper;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import v1.tmf854.GetAllManagedElementsResponseT;
import v1.tmf854.GetAllManagedElementsT;
import v1.tmf854.HeaderT;
import v1.tmf854.ManagedElementListT;
import v1.tmf854.ManagedElementT;
import v1.tmf854.NamingAttributesT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.Set;

public class GetAllManagedElementsWorker extends AbstractMtosiWorker {
  protected GetAllManagedElementsT mtosiBody;
  protected GetAllManagedElementsResponseT response = new GetAllManagedElementsResponseT();
  protected NamingAttributesT mdName;
  protected ManagedElementListT list;

  public GetAllManagedElementsWorker(GetAllManagedElementsT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getAllManagedElements", "getAllManagedElements", "getAllManagedElementsResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    if ((mdName = mtosiBody.getMdName()) == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!NamingTranslationFactory.isManagementDomain(mdName)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!mdName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
              MtosiErrorConstants.MD_NOT_FOUND);
    }
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
  	//No validation required.
  }

  @Override
  protected void mediate() throws Exception {
    final Set<NetworkElement> neSet = ConfigCtrlImpl.get().getHandlers().getNeHdlr().getNetworkElements();

    final Boolean hndlAllNEs = MtosiSupportValidationHelper.canCreateManagedElements();
    list = new ManagedElementListT();
    for (NetworkElement ne : neSet) {
      if (hndlAllNEs || MtosiUtils.isMtosiSupported(ne)) {
        try {
          ManagedElementT nextME = ManagedElementMediator.nmsNeToManagedElementT(ne);
          list.getMe().add(nextME);
        }catch(RuntimeException ex){
          continue;
        }
      }
    }
  }

  @Override
  protected void response() throws Exception {
    response.setMeList(list);
  }

  @Override
  public GetAllManagedElementsResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
