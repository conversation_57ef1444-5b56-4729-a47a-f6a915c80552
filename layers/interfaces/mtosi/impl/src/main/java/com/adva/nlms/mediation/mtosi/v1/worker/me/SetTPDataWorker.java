/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.TPDataT;
import v1.tmf854ext.adva.SetTPDataResponseT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public abstract class SetTPDataWorker extends AbstractMtosiWorker {
  Logger LOG = LogManager.getLogger(this.getClass().getName());

  protected SetTPDataResponseT response = new SetTPDataResponseT();
  protected TPDataT tpInfo;
  protected NamingAttributesT namingAttributes;
  protected NetworkElement ne;

  public SetTPDataWorker (Holder<HeaderT> mtosiHeader, TPDataT tpInfo, NamingAttributesT namingAttributes, NetworkElement ne) {
    super(mtosiHeader, "setTPData", "setTPData", "setTPDataResponse");
    this.tpInfo = tpInfo;
    this.namingAttributes = namingAttributes;
    this.ne = ne;
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
   validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  @Override
  public SetTPDataResponseT getSuccessResponse() {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}