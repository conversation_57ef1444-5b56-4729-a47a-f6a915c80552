/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON>s
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.DeleteFTPResponseT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public class DeleteFTPWorker extends AbstractMtosiWorker
{
	Logger LOG = LogManager.getLogger(this.getClass().getName());

	protected DeleteFTPResponseT response = new DeleteFTPResponseT();
	protected String ftpName;
	protected NamingAttributesT namingAttributes;
	protected NetworkElement ne;

	public DeleteFTPWorker(Holder<HeaderT> mtosiHeader, NamingAttributesT namingAttributes, NetworkElement ne)
	{
		super(mtosiHeader, "deleteFTP", "deleteFTP", "deleteFTPResponse");
		this.namingAttributes = namingAttributes;
		this.ne = ne;
	}

	@Override
  protected void parse() throws Exception
	{
		if ((this.ftpName = namingAttributes.getFtpNm()) == null)
		{
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, "The ftpNm has not been specified.");
		}

	}

	  @Override
	  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
		  validator.validate(ne.getDefaultNetworkElementTypeString());
	  }

	@Override
  protected void mediate() throws Exception
	{
		
		transact();
	}

  @Override
  protected void response () throws Exception {
    // empty method
  }

  private void transact() throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure, ProcessingFailureException
	{
		NetworkElement locks[] = new NetworkElement[]
		{ ne };
		Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "DeleteFTPWorker");

		try
		{
			logSecurity(ne, SystemAction.DeleteNetwork, "ftpNm=" + ftpName);
			
			if (!ne.getMTOSIWorker().deleteFTP(ftpName))
			{
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, "The specified FTP does not exist.");
			}
			
			NetTransactionManager.commitNetTransaction(id);
		}
		catch (NetTransactionException e)
		{
			ne.logSROperation(SROperationState.FTP_DELETION_FAILURE, ftpName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (SPValidationException e)
		{
			ne.logSROperation(SROperationState.FTP_DELETION_FAILURE, ftpName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (SNMPCommFailure e)
		{
			ne.logSROperation(SROperationState.FTP_DELETION_FAILURE, ftpName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (ProcessingFailureException e)
		{
			ne.logSROperation(SROperationState.FTP_DELETION_FAILURE, ftpName);
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		finally
		{
			NetTransactionManager.ensureEnd(id);
		}
	}

	@Override
  public DeleteFTPResponseT getSuccessResponse()
	{
		if (response == null)
			return null;
		response.setTmf854Version(MtosiConstants.VERSION);
		return response;
	}
}