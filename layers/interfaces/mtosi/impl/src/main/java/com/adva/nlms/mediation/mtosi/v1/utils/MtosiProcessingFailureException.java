/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.utils;

public class MtosiProcessingFailureException extends Exception{

  /***
   * It is used to hold the exception type.
   * @see ExceptionUtils exception types such as EXCPT_NOT_IMPLEMENTED, etc
   */
  String exception;

  /**
   * Default Constructor
   */
  public MtosiProcessingFailureException() {
  }

  /**
   * Constructor
   * @param message the reason string for the exception, initializes the parent object string
   * @param cause  a throwable object to nest exceptions
   */
  public MtosiProcessingFailureException(String message, Throwable cause) {
    super(message, cause);
  }

  /**
   *  Constructor
   * @param message the reason string for the exception, initializes the parent object string
   */
  public MtosiProcessingFailureException(String message) {
    super(message);
  }

  public void setException(String ex) {
    this.exception = ex;
  }
}
