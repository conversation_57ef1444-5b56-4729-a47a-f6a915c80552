/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.syncE;

import com.adva.nlms.mediation.common.serviceProvisioning.F3SyncRefSPProperties;
import com.adva.nlms.mediation.config.f3.entity.sync.F3SyncImpl;
import com.adva.nlms.mediation.config.f3.entity.syncref.F3SyncRefImpl;
import com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CMImpl;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.common.NEUtils;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.GetTDFrsWithTPResponseT;
import v1.tmf854ext.adva.GetTDFrsWithTPT;
import v1.tmf854ext.adva.TDFrListT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

/**
 * Worker class for the MTOSI operation getTDFrsWithTP
 */
public class GetTDFrsWithTPWorker extends AbstractMtosiWorker {
  protected GetTDFrsWithTPT mtosiBody;
  protected GetTDFrsWithTPResponseT response = new GetTDFrsWithTPResponseT();
  protected NamingAttributesT tpName;
  protected MtosiAddress mtosiAddr;
  protected TDFrListT tdfrList;

  //Constructor
  public GetTDFrsWithTPWorker(final GetTDFrsWithTPT mtosiBody, final Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "getTDFrsWithTP", "getTDFrsWithTP", "getTDFrsWithTPResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    tpName = mtosiBody.getTpName();
    mtosiAddr = new MtosiAddress(tpName);

    if (tpName == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_FILTER_DEFINITION,
        MtosiErrorConstants.INVALID_FILTER);
    }

    if (tpName.getMdNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
        MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!tpName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        MtosiErrorConstants.MD_NOT_FOUND);
    }

    if (!NamingTranslationFactory.isPort(tpName)) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
          MtosiErrorConstants.MANDATORY_PTP_CTP_FTP);
    }

    MtosiUtils.validateNE(mtosiAddr.getNE());

  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
	  validator.validate(mtosiAddr.getNE().getDefaultNetworkElementTypeString());
  }

  @Override
  protected void mediate() throws Exception {
    // only allowed for FSP150CC-GE201SE && FSP150CM
    if (!NEUtils.isSyncEDevice(mtosiAddr.getNeType())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The specified ME does not support SyncE.");
    }

    NetworkElementFSP150CMImpl ne = (NetworkElementFSP150CMImpl)mtosiAddr.getNE();

    final v1.tmf854ext.adva.ObjectFactory objFactoryEx = new v1.tmf854ext.adva.ObjectFactory();
    tdfrList = objFactoryEx.createTDFrListT();

    // traverse through all F3SyncRefs and find matching CTPs
    F3SYNC_LOOP:
    for (F3SyncImpl f3Sync : getFSP150CMMTOSIWorker(ne).getAllF3Syncs()) {
      for (F3SyncRefImpl f3SyncRef : f3Sync.getAllF3SyncRefs()) {
        if (f3SyncRef.getF3SyncRefProperties().get(F3SyncRefSPProperties.VS.SyncReferenceForMTOSI).equals(tpName.getPtpNm())) {
          tdfrList.getTdfr().add(new MtosiTranslatorFacade(ne, null, null).getMtosiTranslator(f3Sync).toMtosiTDFr());
//          tdfrList.getTdfr().add(f3Sync.getMtosiTranslator().toMtosiTDFr());
          continue F3SYNC_LOOP;
        }
      }
    }
  }

  @Override
  protected void response() throws Exception {
    response.setTDFrList(tdfrList);
  }

  @Override
  public GetTDFrsWithTPResponseT getSuccessResponse() {
    if (response == null)
      return null;

    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}
