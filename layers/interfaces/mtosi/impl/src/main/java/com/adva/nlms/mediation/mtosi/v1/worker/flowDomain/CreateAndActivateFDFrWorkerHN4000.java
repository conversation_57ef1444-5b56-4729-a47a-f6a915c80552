/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.common.MDRequestFailedException;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FDFrSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.FTPSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.FlowSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.LAGSPPropertiesHN4000;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN40002BpmeSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000BondingSPProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.PortHN4000EthernetSProperties;
import com.adva.nlms.mediation.common.serviceProvisioning.UNISPPropertiesHN4000;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.NetTransactionManager;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.Port;
import com.adva.nlms.mediation.config.hn4000.FlowHN4000;
import com.adva.nlms.mediation.config.hn4000.NetworkElementHN4000;
import com.adva.nlms.mediation.config.hn4000.PortHN40002Bpme;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet;
import com.adva.nlms.mediation.config.hn4000.PortHN4000Ethernet2BASE_TL;
import com.adva.nlms.mediation.config.hn4000.mtosi.FDFrHN4000;
import com.adva.nlms.mediation.config.hn4000.mtosi.FTPHN4000;
import com.adva.nlms.mediation.config.sr.service.SROperationState;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.MtosiTranslatorFacade;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiFDFrMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiTPMediator;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.PMEPortPair;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.CreateAndActivateFDFrWorker;
import com.adva.nlms.mediation.ne_comm.snmp.api.SNMPCommFailure;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesListT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.TPDataListT;
import v1.tmf854.TPDataT;
import v1.tmf854ext.adva.CreateAndActivateFDFrT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;
import java.util.Iterator;
import java.util.List;

public class CreateAndActivateFDFrWorkerHN4000 extends CreateAndActivateFDFrWorker
{
	Logger LOG = LogManager.getLogger(this.getClass().getName());

	private NetworkElementHN4000 hnNE = null;
	private FDFrSPPropertiesHN4000 propsFDFr = null;

	private UNISPPropertiesHN4000 eth1UniProps = null;
	private UNISPPropertiesHN4000 eth2UniProps = null;
	private UNISPPropertiesHN4000 lagUniProps = null;
	private UNISPPropertiesHN4000 bondedUniProps = null;

	// MO Objects
	private FTPHN4000 ftp = null;
	//private FlowHN4000 flowBond = null;
	//private FlowHN4000 flowUplink = null;
	private PortHN4000Ethernet eth1 = null;
	private PortHN4000Ethernet eth2 = null;
	private PortHN4000Ethernet2BASE_TL bondedPort = null;

	// tpData objects
	private TPDataT tpDataLag = null;
	private TPDataT tpDataFlowBond = null;
	private TPDataT tpDataFlowUplink = null;
	private TPDataT tpDataEth1 = null;
	private TPDataT tpDataEth2 = null;
	private TPDataT tpDataBonded = null;
	private List<TPDataT> tpDataPMEList = null;

	// properties objects
	private FTPSPProperties propsFTP = null;
	private FlowSPPropertiesHN4000 propsFlowBond = null;
	private FlowSPPropertiesHN4000 propsFlowUplink = null;
	private PortHN4000EthernetSProperties propsEth1 = null;
	private PortHN4000EthernetSProperties propsEth2 = null;
	private PortHN4000BondingSPProperties propsBondedPort = null;

	private List<PMEPortPair> pmePairList = null;

	//private boolean isABonded = true;
	private boolean isLag = true;
	private NamingAttributesT namingFlowBond = null;
	private NamingAttributesT namingFlowUplink = null;
	
	private Integer evcId400 = 1;

	public CreateAndActivateFDFrWorkerHN4000(CreateAndActivateFDFrT mtosiBody, Holder<HeaderT> mtosiHeader, NetworkElement ne, NamingAttributesListT aEnd,
			NamingAttributesListT zEnd)
	{
		super(mtosiBody, mtosiHeader, ne, aEnd, zEnd);
	}

	private void transact() throws ObjectInUseException, NetTransactionException, SPValidationException, SNMPCommFailure, MDRequestFailedException,
			ProcessingFailureException
	{
		NetworkElement locks[] = new NetworkElement[]
		{ ne };
		Integer id = NetTransactionManager.beginUndoNetTransaction(locks, "CreateAndActivateFDFrWorker");
    final String deleteName =  propsFDFr.get(FDFrSPProperties.VS.FDFrName);
		try {
			// Need the FDFr (EVC) first, and also the UNI, in order to create
			// the Flows
			hnNE.getMTOSIWorker().createFDFRr_HN4000(propsFDFr);
      ne.getMTOSIWorker().setFDFrOperationInProgress(deleteName, true);
			FDFrHN4000 newFDFr = (FDFrHN4000) hnNE.getMTOSIWorker().getFDFr(propsFDFr.get(FDFrSPProperties.VS.FDFrName));
			FDFrSPPropertiesHN4000 propsNewFDFr = (FDFrSPPropertiesHN4000) (newFDFr.getFDFrSPProperties());

			Long evcId = propsNewFDFr.get(FDFrSPPropertiesHN4000.VL.EvcID);
			propsFlowBond.set(FlowSPPropertiesHN4000.VL.EvcID, evcId);
			propsFlowUplink.set(FlowSPPropertiesHN4000.VL.EvcID, evcId);
			//propsFlowBond.setDeviceID(propsNewFDFr.getDeviceID());
			//propsFlowUplink.setDeviceID(propsNewFDFr.getDeviceID());

			if (propsBondedPort != null) {
				if (propsBondedPort.size() > 0) {
					bondedPort.setSettings(propsBondedPort);
				}
				if (bondedUniProps != null) {
					if (bondedPort.hasUni()) {
						if (bondedUniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null) {
							// maybe should PFE this?
						} else {
							bondedPort.setUniProperties(bondedUniProps, bondedPort.getUni().getUniSPProperties());
						}
					} else {
						bondedPort.createUni(bondedUniProps);
					}
				}

				bondedPort.createFlow(propsFlowBond);

			} else {
				if (bondedUniProps != null) {
					if (bondedPort.hasUni()) {
						if (bondedUniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null) {
							// maybe should PFE this?
						} else {
							bondedPort.setUniProperties(bondedUniProps, bondedPort.getUni().getUniSPProperties());
						}
					} else {
						bondedPort.createUni(bondedUniProps);
					}
				}
				bondedPort.createFlow(propsFlowBond);
			}

			if (propsFTP != null) {
				ftp.setFTPSPProperties(propsFTP);
				if (lagUniProps != null) {
					if (ftp.getLag().hasUni()) {
						if (lagUniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null) {
							// maybe should PFE this?
						} else {
							ftp.getLag().setUniProperties(lagUniProps, ftp.getLag().getUni().getUniSPProperties());
						}
					} else {
						ftp.getLag().createUni(lagUniProps);
					}
				}
			}

			if (propsEth1 != null) {
				logSecurity(ne, SystemAction.ModifyNetwork, "ptpNm=" + eth1.getMtosiName());
				if (propsEth1.size() > 0) {
					eth1.setSettings(propsEth1);
				}
				if (eth1UniProps != null) {
					if (eth1.hasUni()) {
						if (eth1UniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null) {
							// maybe should PFE this?
						} else {
							eth1.setUniProperties(eth1UniProps, eth1.getUni().getUniSPProperties());
						}
					} else {
						eth1.createUni(eth1UniProps);
					}
				}
			}
			if (propsEth2 != null) {
				logSecurity(ne, SystemAction.ModifyNetwork, "ptpNm=" + eth2.getMtosiName());
				if (propsEth2.size() > 0) {
					eth2.setSettings(propsEth2);
				}
				if (eth2UniProps != null) {
					if (eth2.hasUni()) {
						if (eth2UniProps.get(UNISPPropertiesHN4000.VI.RowStatus) != null) {
							// maybe should PFE this?
						} else {
							eth2.setUniProperties(eth2UniProps, eth2.getUni().getUniSPProperties());
						}
					} else {
						eth2.createUni(eth2UniProps);
					}
				}
			}
			// create the 2nd flow on ETH port or on the LAG
			if (isLag) {
				ftp.getLag().createFlow(propsFlowUplink);
			} else {
				// createFlow on Eth1 or ETh2
				String ptpName = namingFlowUplink.getPtpNm();
				if (ptpName.contains("ETH-1")) {
					eth1 = (PortHN4000Ethernet) ManagedElementFactory.getPort(namingFlowUplink);
					eth1.createFlow(propsFlowUplink);
				} else if (ptpName.contains("ETH-2")) {
					eth2 = (PortHN4000Ethernet) ManagedElementFactory.getPort(namingFlowUplink);
					eth2.createFlow(propsFlowUplink);
				} else {
					throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
				}
			}

			for (Iterator iterator = pmePairList.iterator(); iterator.hasNext();) {
				PMEPortPair nextPair = (PMEPortPair) iterator.next();
				PortHN40002Bpme nextPort = nextPair.getPort();
				PortHN40002BpmeSPProperties nextProps = nextPair.getProps();
				nextPort.setSettings(nextProps);
			}

			NetTransactionManager.commitNetTransaction(id);

		}
		catch (NetTransactionException e) {
			ne.logSROperation(SROperationState.SERVICE_CREATION_FAILURE, propsFDFr.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (ProcessingFailureException e) {
			ne.logSROperation(SROperationState.SERVICE_CREATION_FAILURE, propsFDFr.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (SPValidationException e) {
			ne.logSROperation(SROperationState.SERVICE_CREATION_FAILURE, propsFDFr.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (SNMPCommFailure e) {
			ne.logSROperation(SROperationState.SERVICE_CREATION_FAILURE, propsFDFr.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		catch (MDRequestFailedException e) {
			ne.logSROperation(SROperationState.SERVICE_CREATION_FAILURE, propsFDFr.get(FDFrSPProperties.VS.FDFrName));
      ServiceUtils.rollbackNetTransaction(id, e, LOG);
		}
		finally {
      ne.getMTOSIWorker().setFDFrOperationInProgress(deleteName, false);
			NetTransactionManager.ensureEnd(id);
		}
	}

	private void computeEnds() throws ProcessingFailureException
	{
		NamingAttributesT aName = aEnd.getName().get(0); // one Flow
		NamingAttributesT zName = zEnd.getName().get(0); // other Flow
		if (!NamingTranslationFactory.isFlowHN(aName)) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FDFR_AEEND_INVALID);
		}
		if (!NamingTranslationFactory.isFlowHN(zName)) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FDFR_ZEEND_INVALID);
		}
		if (NamingTranslationFactory.isFlowBonding(aName)) {
			// a is bonding, so z is uplink
			namingFlowBond = aName;
			namingFlowUplink = zName;
			if (NamingTranslationFactory.isFlowLag(zName)) {
				isLag = true;
			} else {
				isLag = false;
			}
		}
		else
		{
			namingFlowUplink = aName;
			namingFlowBond = zName;
			if (NamingTranslationFactory.isFlowLag(aName)) {
				isLag = true;
			} else {
				isLag = false;
			}
		}
		// if its an HN400, then we need to set EVCId based on whether eth1 or eth2 for the uplink flow
		if(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400)
		{
			evcId400 = getEVCId(namingFlowUplink);
			
		}
	}
	
	private int getEVCId(NamingAttributesT naming) throws ProcessingFailureException
	{
		String ptpNm = naming.getPtpNm();
		int ethNo = NamingTranslationFactory.getEthPortNo(ptpNm);
		if(ethNo==-1)
		{
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
		}
		return ethNo;
	}
	

	@Override
  protected void mediate() throws Exception
	{
		// get the zEnd and zEnd, which are both flows, but in order to create
		// them,
		// we first need to ensure the UNIs exist on bonded FTP and on LAG or
		// PTP

		// even before that, we need to look at parents of the two ends, and
		// figure out
		// which one is for bonded port (ftpNm parent contains "/port=ETH-"),
		// and then
		// determine whether other end has parent that is ftpNm or ptpNm.

		// if ftpNm, then there can be a LAG in tpsToModify,
		// and if there is, we can process it first and create UNI if it
		// doesn't exist. If it does exist, then we just process the LAG and
		// modify it.

		// if ptpNm, then we have unprotected and its a flow on a port, in which
		// case there can be a PTP
		// of that name in the tpsToModify. Check for it, and process, which
		// could involve UNI creation.

		// In transact, UNI should be created during processing of port or lag
		// if necessary

		// Once the PTP/LAG and the FTP have been processed to possibly create
		// UNI
		// then we can createFDFR (EVC)

		// then we can mediate the flow ends to get the Flow properties, and
		// create
		// the flows on the bonded port and the lag/ptp objects, associating
		// them with the
		// EVC however that is done.

		// we can also go through the tpsToModify to handle PTPs that form the
		// lag if there is lag
		// and we can get N 2BPME PTPs to process as well for setting state etc.
		// Probably have
		// two lists of PTPs, 2BPME and Physical, with Hashmap of MO objects
		// somehow such that
		// we can process and mediate, lookup each PTP and have a hashmap of PTP
		// by name, then an array
		// of 2 that has the PTP MO object, and the mediated object, and then we
		// can loop each to
		// do the setSettings in the transact.

		hnNE = (NetworkElementHN4000) ne;
		boolean hn400 = hnNE.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400;
		if (tpsToModify != null)
		{
			if (!MtosiTPMediator.checkTPToModifySameNE(ne, tpsToModify))
			{
				ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
						"The specified TPs must be on the same Network Element.");
				throw new ProcessingFailureException(pfet.getReason(), pfet);
			}
			computeEnds();
			tpDataBonded = MtosiTPMediator.getTPDataTForHNBonded(tpsToModify);
			tpDataLag = MtosiTPMediator.getTPDataTForHNFTP(tpsToModify);
			tpDataFlowBond = MtosiTPMediator.getTPDataTForHNFlowBonding(tpsToModify);
			tpDataFlowUplink = MtosiTPMediator.getTPDataTForHNFlowUplink(tpsToModify);
			tpDataEth1 = MtosiTPMediator.getTPDataTForHNPhysicalEth(tpsToModify, 1);
			tpDataEth2 = MtosiTPMediator.getTPDataTForHNPhysicalEth(tpsToModify, 2);
			tpDataPMEList = MtosiTPMediator.getTPDataListForHN2BPME(tpsToModify);
		} else {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_NAME_MISSING);
		}

		if (tpDataLag != null) {
			ftp = (FTPHN4000) ManagedElementFactory.getFtp(tpDataLag.getTpName());
			if (ftp == null) {
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
			}
			propsFTP = MtosiTPMediator.mtosiTPDataTToHN4000FTPProperties(tpDataLag, tpDataLag.getTpName().getFtpNm());
		}

		if (tpDataFlowBond != null) {
			propsFlowBond = MtosiTPMediator.mtosiTPDataTToHNCTPProperties(tpDataFlowBond, null);
			bondedPort = hnNE.getBondedPort(tpDataFlowBond.getTpName().getFtpNm());
			if (bondedPort == null) {
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
			}
		} else {
			// if no bonding Flow Point CTP specified in tpsToModify, get bonded port from a/zEnd name
			bondedPort = hnNE.getBondedPort(namingFlowBond.getFtpNm());
			if (bondedPort == null) {
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
			}
			// if not flow in tpsToModify (and likely won't be), then we need to create propsFlowBond
			propsFlowBond = MtosiTPMediator.mtosiTPDataTToHNCTPProperties(namingFlowBond.getCtpNm());
		}
		if (tpDataFlowUplink != null) {
			propsFlowUplink = MtosiTPMediator.mtosiTPDataTToHNCTPProperties(tpDataFlowUplink, null);
			//If the user specifies the tpDataFlowUplink but does not add the correct layers it was going boom
			// Fix for FNM8573
			if (propsFlowUplink == null) {
				throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.CTP_LAYER_NOT_FOUND);
			}
			if (isLag) {
				ftp = (FTPHN4000) hnNE.getMTOSIWorker().getFTP(tpDataFlowUplink.getTpName().getFtpNm());
				if (ftp == null) {
					throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
				}
				checkFlow(ftp.getLag().getFlowsByName(tpDataFlowUplink.getTpName().getCtpNm()));
			} else {
				checkEthFlow(tpDataFlowUplink.getTpName());
			}
		} else {
			// if no uplink Flow Point CTP specified in tpsToModify, get uplink ftp from a/zEnd name
			// It is mandatory for 4000 if uni is trunk, but we'll let device validate that
			if (isLag) {
				// we need to get the ftp object for later
				ftp = (FTPHN4000) hnNE.getMTOSIWorker().getFTP(namingFlowUplink.getFtpNm());
				if (ftp == null) {
					throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
				}
				checkFlow(ftp.getLag().getFlowsByName(namingFlowUplink.getCtpNm()));
			} else {
				checkEthFlow(namingFlowUplink);
			}
			propsFlowUplink = MtosiTPMediator.mtosiTPDataTToHNCTPProperties(namingFlowUplink.getCtpNm());
			
		}
		if (tpDataEth1 != null) {
			Port port = ManagedElementFactory.getPort(tpDataEth1.getTpName());
			eth1 = (PortHN4000Ethernet) port;
			propsEth1 = MtosiTPMediator.mtosiTPDataTToHNEthernetProperties(tpDataEth1, eth1,hn400);
			NetworkElement ne =  eth1.getNE();
			eth1UniProps = MtosiTPMediator.mtosiTPDataTToHNUniProperties(tpDataEth1, ne.getMTOSIWorker().getNetworkElementTypeForMTOSI(), eth1.hasUni());
			if (eth1UniProps != null && eth1.hasUni()) {
				UNISPPropertiesHN4000 props = eth1.getUni().getUniSPProperties();
				eth1UniProps.set(UNISPPropertiesHN4000.VL.DeviceId, props.get(UNISPPropertiesHN4000.VL.DeviceId));
				eth1UniProps.set(UNISPPropertiesHN4000.VL.PortId, props.get(UNISPPropertiesHN4000.VL.PortId));
			}
		}
		if (tpDataEth2 != null) {
			Port port = ManagedElementFactory.getPort(tpDataEth2.getTpName());
			eth2 = (PortHN4000Ethernet) port;
			propsEth2 = MtosiTPMediator.mtosiTPDataTToHNEthernetProperties(tpDataEth2, eth2,hn400);
			NetworkElement ne = eth2.getNE();
			eth2UniProps = MtosiTPMediator.mtosiTPDataTToHNUniProperties(tpDataEth2, ne.getMTOSIWorker().getNetworkElementTypeForMTOSI(), eth2.hasUni());
			if (eth2UniProps != null && eth2.hasUni()) {
				UNISPPropertiesHN4000 props = eth2.getUni().getUniSPProperties();
				eth2UniProps.set(UNISPPropertiesHN4000.VL.DeviceId, props.get(UNISPPropertiesHN4000.VL.DeviceId));
				eth2UniProps.set(UNISPPropertiesHN4000.VL.PortId, props.get(UNISPPropertiesHN4000.VL.PortId));
			}
		}
		if (tpDataBonded != null) {
			if (bondedPort == null) {
				bondedPort = hnNE.getBondedPort(tpDataBonded.getTpName().getFtpNm());
			}
			NetworkElement ne = bondedPort.getNE();
			propsBondedPort = MtosiTPMediator.mtosiTPDataTToHN4000FTPBondProperties(tpDataBonded, null, hn400, bondedPort.getPortSPProperties(), true);
			bondedUniProps = MtosiTPMediator.mtosiTPDataTToHNUniProperties(tpDataBonded, ne.getMTOSIWorker().getNetworkElementTypeForMTOSI(), bondedPort
					.hasUni());
			if (bondedUniProps != null && bondedPort.hasUni()) {
				UNISPPropertiesHN4000 props = bondedPort.getUni().getUniSPProperties();
				bondedUniProps.set(UNISPPropertiesHN4000.VL.DeviceId, props.get(UNISPPropertiesHN4000.VL.DeviceId));
				bondedUniProps.set(UNISPPropertiesHN4000.VL.PortId, props.get(UNISPPropertiesHN4000.VL.PortId));
			}
		}
		if (tpDataLag != null) {
			if (ftp == null) {
				ftp = (FTPHN4000) hnNE.getMTOSIWorker().getFTP(tpDataLag.getTpName().getFtpNm());
				if (ftp == null) {
					throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
				}
			}
			NetworkElement ne = ftp.getLag().getNE();
			propsFTP = MtosiTPMediator.mtosiTPDataTToHN4000FTPProperties(tpDataLag, null, (LAGSPPropertiesHN4000) ftp
					.getFTPSPProperties());
			lagUniProps = MtosiTPMediator.mtosiTPDataTToHNUniProperties(tpDataLag, ne.getMTOSIWorker().getNetworkElementTypeForMTOSI(), ftp.getLag().hasUni());
			if (lagUniProps != null && ftp.getLag().hasUni()) {
				UNISPPropertiesHN4000 props = ftp.getLag().getUni().getUniSPProperties();
				lagUniProps.set(UNISPPropertiesHN4000.VL.DeviceId, props.get(UNISPPropertiesHN4000.VL.DeviceId));
				lagUniProps.set(UNISPPropertiesHN4000.VL.PortId, props.get(UNISPPropertiesHN4000.VL.PortId));
			}

		}
		if (tpDataPMEList != null) {
			pmePairList = MtosiTPMediator.mtosiTPDataListToPMEPortPair(tpDataPMEList, hn400);
		}

		propsFDFr = MtosiFDFrMediator.mtosiCreateFDFRToFDFRSPProperties4000(createData, null, null);
		if(ne.getMTOSIWorker().getNetworkElementTypeForMTOSI() == NeTypeIds.NETWORK_ELEMENT_TYPE_HN400)
		{
			propsFDFr.set(FDFrSPPropertiesHN4000.VL.EvcID, (long) evcId400);
		}

		transact();
	}

	/**
	 * Check the Eth-1 and Eth-2 Flows, confirming they only have one flow.
	 * @param tpName
	 * @throws ProcessingFailureException 
	 */
	private void checkEthFlow(NamingAttributesT tpName) throws ProcessingFailureException {
		Port port = ManagedElementFactory.getPort(tpName);
		if (tpName.getPtpNm().endsWith("ETH-1")) {
			eth1 = (PortHN4000Ethernet) port;
			checkFlow(eth1.getFlowsByName(tpName.getCtpNm()));
		} else if (tpName.getPtpNm().endsWith("ETH-2")) {
			eth2 = (PortHN4000Ethernet) port;
			checkFlow(eth2.getFlowsByName(tpName.getCtpNm()));
		}
	}

	/**
	 * Check to see if the flow with the specified ctpNm already exists.
	 * @param flowsByName
	 * @throws ProcessingFailureException when the flow already exists.
	 */
	private void checkFlow(List<FlowHN4000> flowsByName) throws ProcessingFailureException {
		if (flowsByName != null && flowsByName.size() > 0) {
			throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.CTP_ALREADY_FOUND);
		}
	}

	@Override
  protected TPDataListT getUpdatedTPs() throws ProcessingFailureException
	{
		ObjectFactory objectFactory = new ObjectFactory();
		TPDataListT tpsToModify = objectFactory.createTPDataListT();

		if (tpDataEth1 != null && eth1 != null) {
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataEth1.getTpName())).toMtosiPTPasTPDataT());
//			tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataEth1.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
		if (tpDataEth2 != null && eth2 != null) {
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(tpDataEth2.getTpName())).toMtosiPTPasTPDataT());
//			tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(tpDataEth2.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
		}
		if (tpDataBonded != null && bondedPort != null) {
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  null).getMtosiTranslator(ManagedElementFactory.getHN4000Ftp(tpDataBonded.getTpName())).toMtosiFTPasTPDataT());
//			tpsToModify.getTpData().add(ManagedElementFactory.getHN4000Ftp(tpDataBonded.getTpName()).getMtosiTranslator().toMtosiFTPasTPDataT());
		}
		if (tpDataLag != null && ftp != null) {
			tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  null).getMtosiTranslator(ManagedElementFactory.getHN4000Ftp(tpDataLag.getTpName())).toMtosiFTPasTPDataT());
//			tpsToModify.getTpData().add(ManagedElementFactory.getHN4000Ftp(tpDataLag.getTpName()).getMtosiTranslator().toMtosiFTPasTPDataT());
		}
		// flowUplink is never set (always "null")
//		if (tpDataFlowUplink != null && flowUplink != null) {
//			FlowHN4000 newFlow = ManagedElementFactory.getHNFlow(tpDataFlowUplink.getTpName());
//			MtosiSupported supported = (MtosiSupported) newFlow;
//			tpsToModify.getTpData().add(supported.getMtosiTranslator().toMtosiCTPasTPDataT());
//		}
		// flowBond is never set (always "null")
//		if (tpDataFlowBond != null && flowBond != null) {
//			FlowHN4000 newFlow = ManagedElementFactory.getHNFlow(tpDataFlowBond.getTpName());
//			MtosiSupported supported = (MtosiSupported) newFlow;
//			tpsToModify.getTpData().add(supported.getMtosiTranslator().toMtosiCTPasTPDataT());
//		}
		if (tpDataPMEList != null) {
			for (Iterator iterator = tpDataPMEList.iterator(); iterator.hasNext();) {
				TPDataT nextTP = (TPDataT) iterator.next();
				tpsToModify.getTpData().add(new MtosiTranslatorFacade(ne, null,  MtosiErrorConstants.MTOSI_NOT_SUPPORTED).getMtosiTranslator(ManagedElementFactory.getMtosiSupportedPort(nextTP.getTpName())).toMtosiPTPasTPDataT());
//				tpsToModify.getTpData().add(ManagedElementFactory.getMtosiSupportedPort(nextTP.getTpName()).getMtosiTranslator().toMtosiPTPasTPDataT());
			}
		}

		return tpsToModify;
	}

	@Override
	protected void response() throws Exception {
		//FNMD-20992 Sometimes lag fdfr points take time to be properly populated in the database
		//Give it 4 seconds....
		if(isLag) {
			try {
				Thread.sleep(4000);
			} catch (InterruptedException ex) {
			}
		}
		super.response();
	}
}