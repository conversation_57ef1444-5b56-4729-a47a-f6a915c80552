/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.worker.flowDomain;

import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.mtosi.FDFr;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.HeaderT;
import v1.tmf854ext.adva.RenameFDFrResponseT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

abstract public class RenameFDFrWorker extends AbstractMtosiWorker
{
	Logger LOG = LogManager.getLogger(this.getClass().getName());
	private RenameFDFrResponseT response = null;
  protected NetworkElement ne;
  protected FDFr fdfr;
  protected String newFDFrName;

  public RenameFDFrWorker(Holder<HeaderT> mtosiHeader,
                            NetworkElement ne, FDFr fdfr, String newFDFrName) {
    super(mtosiHeader, "renameFDFr", "renameFDFr", "renameFDFrResponse");
    this.ne = ne;
    this.fdfr = fdfr;
    this.newFDFrName = newFDFrName;
  }


  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException {
   validator.validate(ne.getDefaultNetworkElementTypeString());
  }
  
  @Override
  public void response() {
    response = new RenameFDFrResponseT();
  }

  @Override
  public RenameFDFrResponseT getSuccessResponse()
	{
		if (response == null)
			return null;
		response.setTmf854Version(MtosiConstants.VERSION);
		return response;
	}
}