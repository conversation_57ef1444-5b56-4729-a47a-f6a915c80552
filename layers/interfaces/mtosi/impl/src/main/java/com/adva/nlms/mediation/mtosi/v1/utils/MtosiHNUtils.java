/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.utils;

import com.adva.nlms.common.snmp.MIBHN4000;

/**
 * Class to share Hatteras Utilities
 */

public class MtosiHNUtils {
	
	/**
	 * Check the PortType for this port. Return True if the port has an
	 * hnEthPortType of ieee100baseT (2) or ieee1000baseT (4)
	 * @param moduleAssignType 
	 * 
	 	MODULE_1000_X2  (3, "HN4000-GbX"),//corrected based on Bob mail from 2008-11-28 15:23
		MODULE_1000_TX2 (4, "HN4000-GbT"),
		MODULE_100_X2   (5, "HN4000-FEX"),

	 * 
	 * @param port
	 * @return
	 */
	public static boolean isHN4000GbT(Integer portType, int moduleAssignType) {
		if (portType == null || portType == 0) {
			return moduleAssignType == 4;  //Very dangerous, but I was not sure if we wanted this class to see NetworkElementDiscovery...
		} else if
				(MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_100BASE_T == portType.intValue()
				|| MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_1000BASE_T == portType.intValue()) {
			return true;
		}
		return false;
	}

	public static boolean isHN4000FEX(Integer portType, int moduleAssignType) {
		if (portType == null || portType == 0) {
			return moduleAssignType == 5;  //Very dangerous, but I was not sure if we wanted this class to see NetworkElementDiscovery...
		} else if (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_100BASE_FX == portType.intValue()) {
			return true;
		}
		return false;
	}

	public static boolean isHN4000GbX(Integer portType, int moduleAssignType) {
		if (portType == null || portType == 0) {
			return moduleAssignType == 3;  //Very dangerous, but I was not sure if we wanted this class to see NetworkElementDiscovery...
		} else if (MIBHN4000.Common.HnInterfaces.EthTable.OID_TYPE_IEEE_1000BASE_FX == portType.intValue()) {
			return true;
		}
		return false;
	}
	
	  /**
	 * Convert the speed from bytes to Kilobytes.
	 * 
	 * @param speed
	 * @return
	 */
	public final static String bytesToKbs(Long speed) {
		if (speed != null && speed > 1000) {
			return String.valueOf(speed / 1000);
		}
		return String.valueOf(speed);
	}
	  /**
	 * Convert the speed from bytes to Megabytes.
	 * 
	 * @param speed
	 * @return
	 */
	public final static String bytesToMbs(Long speed) {
		if (speed != null && speed > 1000000) {
			return String.valueOf(speed / 1000000);
		}
		return String.valueOf(speed);
	}



}
