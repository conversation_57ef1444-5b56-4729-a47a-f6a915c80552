/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils;

import com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams;

public class MtosiErrorConstants
{
	public static final String INVALID_FILTER = "An invalid filter definition was encountered. NamingAttributes type are not specified.";
	public static final String CONTAINS_ILLEGAL_VALUE = " contains an illegal value.";
	public static final String MANDATORY_FOR_NEW_FLOW = " is mandatory for a new Flow Point CTP.";
	public static final String CANNOT_MODIFY_EXISTING_FLOW = " cannot be modified on an existing Flow Point CTP.";
	public static final String MANDATORY = " is a mandatory parameter.";


	public static final String OS_NOT_FOUND   = "The specified Operations System was not found.";
	public static final String MD_NOT_FOUND   = "The specified Management Domain was not found.";
	public static final String EFM_REMOTE_ME_NOT_FOUND   = "The specified Remote Managed Element was not found.";
	public static final String ME_NOT_FOUND   = "The specified Managed Element was not found.";
	public static final String PTP_NOT_FOUND  = "The specified Physical Termination Point was not found.";
	public static final String CTP_NOT_FOUND  = "The specified Connection Termination Point was not found.";
	public static final String CTP_DUPLICATE_FOUND  = "The specified Connection Termination Point name is not unique for the given parent.";
	public static final String CTP_ALREADY_FOUND  = "The specified Connection Termination Point name already exists for the given parent.";
	public static final String FTP_NOT_FOUND  = "The specified Floating Termination Point was not found.";
  public static final String FDFR_NOT_FOUND = "The specified Flow Domain Fragment was not found.";
  public static final String TDFR_NOT_FOUND = "The specified Timing Domain Fragment was not found.";
	public static final String TC_PROFILE_NOT_FOUND = "The specified Traffic Condition Profile was not found.";

	public static final String OS_NAME_MISSING         = "The Operations System name must be specified in the request";
	public static final String MD_NAME_MISSING         = "The Management Domain name must be specified in the request.";
	public static final String ME_NAME_MISSING         = "The Managed Element name must be specified in the request.";
	public static final String CTP_NAME_MISSING        = "The CTP name must be specified in the request.";
	public static final String FTP_NAME_MISSING        = "The FTP name must be specified in the request.";
	public static final String MISSING_FTPNM = "The protection group name has not been specified.";
	public static final String INVALID_COMBINATION_CTP_AND_PTP = "Invalid combination between CTP and PTP.";
	public static final String MISSING_OR_INVALID_ALLOCATED_NUMBER = "The AllocatedNumber has not been specified or is invalid.";
	public static final String MISSING_OR_INVALID_FRAGMENT_SERVER_LAYER = "The FragmentServerLayer parameter specified is invalid or missing.";
	public static final String MISSING_OR_INVALID_PROTECTION_SWITCH_MODE = "The ProtectionSwitchMode parameter specified is invalid or missing.";
	public static final String INVALID_REVERTIVE_ATTRIBUTE = "The Revertive parameter specified is invalid." ;
	public static final String MISSING_TRANSMISSION_PARAMETER = "Required transmission parameter(s) missing";
	public static final String WORKING_PROTECTION_PORT_MATCH = "Working and protection port cannot be the same.";
	public static final String INVALID_FTPNM = "The specified protection group name is invalid.";
	public static final String PROTECTION_GROUP_EXISTS = "Protection Group already exists.";
	public static final String FLOATING_TERMINATION_POINT_DISCOVERY_FAILURE = "The specified Floating Termination Point does not exist.";
	public static final String PTP_INVALID = "The specified Physical Termination Point is invalid.";
	public static final String MISSING_OR_INVALID_TP_NAME = "The termination port name is invalid or missing.";
	public static final String PORT_NOT_FOUND = "Specified port has not been provisioned.";
	public static final String PTP_NAME_MISSING        = "The PTP name must be specified in the request.";
	public static final String TC_PROFILE_NAME_MISSING = "The TC Profile name must be specified in the request.";
	public static final String FDFR_NAME_MISSING       = "The FDFr name must be specified in the request.";
	public static final String FDFR_AEEND_INVALID       = "The FDFr AEnd must be a CTP.";
	public static final String FDFR_ZEEND_INVALID       = "The FDFr ZEnd must be a CTP.";
  public static final String TDFR_NAME_MISSING        = "The TDFr name must be specified in the request.";
  public static final String TDFR_NAME_INVALID        = "The TDFr name is invalid.";
	public static final String EH_NAME_MISSING         = "The Equipment Holder name must be specified in the request.";
  public static final String EQ_NAME_MISSING         = "The Equipment name must be specified in the request.";
  public static final String EH_AND_EQ_NAME_MISSING  = "The Equipment Holder or Equipment name must be specified in the request.";
  public static final String VENDOR_EXTENSIONS_MISSING = "Vendor Extensions are missing.";
  
  public static final String TPS_INVALID = "The tpsToModify must be on the specified FDFr.";
  public static final String OUTER_VID_OPT__PRIO_INVALID = "outerVidOptPrioList can have a maximum of 3 Outer VLAN IDs.";
  public static final String INNER_VID_OPT__PRIO_INVALID = "innerVidOptPrioList can have a maximum of 3 Inner VLAN IDs.";
  
  
  public static final String PORT_NAME_MISSING        = "A PTP or FTP name must be specified in the request.";
	
	public static final String TO_MUCH_PARAMETERS_DEFINED = "To much paramters are defined in the request.";

	public static final String PTP_ILLEGAL = "Specified PTP is illegal for that operation.";

	public static final String PTP_NAME_NOT_VALID        = "The PTP name is invalid.";
  public static final String TP_NAME_NOT_VALID         = "The TP name is invalid.";
  public static final String TC_PROFILE_NAME_NOT_VALID = "The TCProfile name is invalid.";
	public static final String TC_PROFILE_NAME_NOT_VALID_CTP = "The TCProfile name is invalid. CTP name is specified for egress queue.";
	public static final String FTP_NAME_NOT_VALID        = "The FTP name is invalid.";

	public static final String MANDATORY_PTP_CTP_FTP = "A properly formatted PTP, CTP, or FTP must be specified.";
	public final static String ADMINISTRATION_CONTROL_ILLEGAL_VALUE = "AdministrationControl contains an illegal value.";
	public final static String LACP_CONTROL_ILLEGAL_VALUE = "LACPControl" + CONTAINS_ILLEGAL_VALUE;
	public final static String ACTOR_LACP_CONTROL_ILLEGAL_VALUE = "ActorLACPControl" + CONTAINS_ILLEGAL_VALUE;
	public final static String ACTOR_SYSTEM_PRIORITY_ILLEGAL_VALUE = "ActorSystemPriority" + CONTAINS_ILLEGAL_VALUE;
	public final static String ACTOR_PORT_PRIORITY_ILLEGAL_VALUE = "ActorPortPriority" + CONTAINS_ILLEGAL_VALUE;
	public final static String ACTOR_LACP_ACTIVITY_ILLEGAL_VALUE = "ActorLACPActivity" + CONTAINS_ILLEGAL_VALUE;
	public final static String ACTOR_LACP_TIMEOUT_ILLEGAL_VALUE = "ActorLACPTimeout" + CONTAINS_ILLEGAL_VALUE;
	public final static String PARTNER_LACP_ACTIVITY_ILLEGAL_VALUE = "PartnerLACPActivity" + CONTAINS_ILLEGAL_VALUE;
	public final static String PARTNER_LACP_TIMEOUT_ILLEGAL_VALUE = "PartnerLACPTimeout" + CONTAINS_ILLEGAL_VALUE;
	public final static String PARTNER_ADMINISTRATION_PORT_PRIORITY_ILLEGAL_VALUE = "PartnerAdministrationPortPriority" + CONTAINS_ILLEGAL_VALUE;
	public final static String PARTNER_ADMINISTRATION_PORT_NUMBER_ILLEGAL_VALUE = "PartnerAdministrationPortNumber" + CONTAINS_ILLEGAL_VALUE;
	public final static String PARTNER_ADMINISTRATION_SYSTEM_PRIORITY_ILLEGAL_VALUE = "PartnerAdministrationSystemPriority" + CONTAINS_ILLEGAL_VALUE;
	public final static String PARTNER_ADMINISTRATION_SYSTEM_KEY_ILLEGAL_VALUE = "PartnerAdministrationSystemKey" + CONTAINS_ILLEGAL_VALUE;
	public final static String COLLECTOR_MAX_DELAY_ILLEGAL_VALUE = "CollectorMaxDelay" + CONTAINS_ILLEGAL_VALUE;
	public final static String AUTO_NEG_ILLEGAL_VALUE = "AutoNegotiation contains an illegal value";
	public final static String ADMIN_SPEED_RATE_ILLEGAL_VALUE = "AdministrativeSpeedRate contains an illegal value";
	public final static String ADMINISTRATION_CONTROL_MUST_BE_UP_FOR_SPEED_PORT_CHANGE = "AdministrationControl must be up for speed port change";
	public final static String EFMOAM_ENABLE_ILLEGAL_VALUE = "EFMOAMEnable" + CONTAINS_ILLEGAL_VALUE;
	public final static String LINK_LOSS_FWD_ILLEGAL_VALUE = "LinkLossForwarding" + CONTAINS_ILLEGAL_VALUE;
	public final static String MEDIA_TYPE_ILLEGAL_VALUE = "MediaType" + CONTAINS_ILLEGAL_VALUE;
	public final static String ASSIGNED_STATE_ILLEGAL_VALUE = "AssignedState" + CONTAINS_ILLEGAL_VALUE;
	public final static String SHAPING_ENABLED_ILLEGAL_VALUE = "ShapingEnabled" + CONTAINS_ILLEGAL_VALUE;
	public final static String SHAPING_BANDWIDTH_ILLEGAL_VALUE = "ShapingBandwidth" + CONTAINS_ILLEGAL_VALUE;
	public final static String DUPLEX_MODE_ILLEGAL_VALUE = "DuplexMode" + CONTAINS_ILLEGAL_VALUE;
	public final static String ALLOCATED_NUMBER_ILLEGAL_VALUE = "AllocatedNumber" + CONTAINS_ILLEGAL_VALUE;
	public final static String FRAGMENT_SERVER_LAYER_ILLEGAL_VALUE = "FragmentServerLayer" + CONTAINS_ILLEGAL_VALUE;
	public final static String PROTECTION_TYPE_ILLEGAL_VALUE = "ProtectionType" + CONTAINS_ILLEGAL_VALUE;
	public final static String ME_NOT_SUPPORTED = "The requested operation is not supported for this Managed Element type.";
	public final static String PAUSE_FRAMES_ENABLED_ILLEGAL_VALUE = "PauseEnable" + CONTAINS_ILLEGAL_VALUE;
	public final static String MAC_ADDRESS_LEARNING_ILLEGAL_VALUE = "MACAddressLearning" + CONTAINS_ILLEGAL_VALUE;
	public final static String BPDU_FORWARDING_FILTER_ILLEGAL_VALUE = "BPDUForwardingFilter" + CONTAINS_ILLEGAL_VALUE;
	public final static String JAXB_MARSHALLING_PROBLEM = "Failed to successfully unmarshall SOAP request (JAXB Unmarshalling).";
	public final static String FLOW_TYPE_ILLEGAL_VALUE = "FlowType" + CONTAINS_ILLEGAL_VALUE;
	public final static String FLOW_TYPE_CHANGE_ILLEGAL_VALUE = "FlowType" + CANNOT_MODIFY_EXISTING_FLOW;
	public final static String FLOW_VLAN_MEMBERS_ILLEGAL_VALUE = "VLANMembers are not permitted for a FlowType of Default";
	public final static String FLOW_TYPE_MANDATORY_ILLEGAL_VALUE = "FlowType" + MANDATORY_FOR_NEW_FLOW;
	public final static String MULTI_COS_ILLEGAL_VALUE = "MultiCOS" + CONTAINS_ILLEGAL_VALUE;
	public final static String UNTAGGED_FRAMES_ILLEGAL_VALUE = "UntaggedFramesEnabled" + CONTAINS_ILLEGAL_VALUE;
    public final static String TAGGED_FRAMES_ILLEGAL_VALUE = "TaggedFramesEnabled" + CONTAINS_ILLEGAL_VALUE;
	public final static String SPEED_COMBO_ILLEGAL_VALUE = "Specified combination of values for AdministrativeSpeedRate, AutoNegotiation, DuplexMode, and MediaType is not valid.";
	public final static String ADMIN_SPEED_RATE_WAN_0_ILLEGAL = "A specific speed must be configured for the WAN port.  Rate-adaptable auto-negotiation (0) is not supported.";
	public final static String NOT_DISCOVERED = "The Managed Element has not yet been discovered.";
	public final static String MTOSI_NOT_SUPPORTED = "The specified entity is not supported by MTOSI.";
	public final static String STANDBY_SERVER_OPERATION_NOT_SUPPORTED = "Operation not supported on the standby server.";
	//Hatteras Error messages
	public final static String SPAN_PROFILE_ID_NOT_NUMERIC = "SpanProfileID contains an illegal value.";
	public final static String SPAN_ALARM_PROFILE_ID_NOT_NUMERIC = "SpanAlarmProfileID contains an illegal value.";
	public final static String DUPLEX_MODE_NOT_APPLICABLE = "DuplexMode is not applicable when AutoNegotiation is Disabled.";
	
	public final static String TRANSMIT_PAUSE_FRAMES_ENABLED_ILLEGAL_VALUE = LayeredParams.PropHatterasEthernet.TRANSMIT_PAUSE_FRAMES + CONTAINS_ILLEGAL_VALUE;
	public final static String RECEIVE_PAUSE_FRAMES_ENABLED_ILLEGAL_VALUE = LayeredParams.PropHatterasEthernet.RECEIVE_PAUSE_FRAMES + CONTAINS_ILLEGAL_VALUE;
	public final static String PME_VALIDATION_ILLEGAL_VALUE = LayeredParams.PropHatterasBonding.PME_VALIDATION_PARM + CONTAINS_ILLEGAL_VALUE;
	public final static String DOT3_AH_AGGREGATION_ILLEGAL_VALUE = LayeredParams.PropHatterasBonding.DOT3_AH_AGGREGATION_PARM + CONTAINS_ILLEGAL_VALUE;
	public final static String MINIMUM_RATE_ALARM_THRESHOLD_ILLEGAL_VALUE = LayeredParams.PropHatterasBonding.MINIMUM_RATE_ALARM_THRESHOLD_PARM + CONTAINS_ILLEGAL_VALUE;
	public final static String MAX_FRAGMENT_SIZE_ILLEGAL_VALUE = LayeredParams.PropHatterasBonding.MAX_FRAGMENT_SIZE_PARM + CONTAINS_ILLEGAL_VALUE;
	public static final String UNI_NAME_TOO_SHORT = "Name is invalid - must be at least 5 digits.";
	public static final String UNI_NAME_TOO_LONG = "Name is too long, maximum 32 characters.";
	public static final String UNI_DESCRIPTION_TOO_LONG = "Description is too long, maximum 64 characters.";
	public static final String INVALID_EMF_OAM_MODE = "Passive is not a valid EfmOamMode for Bonded Ports.";
	public static final String INVALID_CTAG_MATCH_RX_PRIORITY = "CTagMatchRxPriority is only applicable when CTagControl is Push.";
	public static final String REMOTE_MAC_ADDRESS_INVALID_VALUE = "RemoteMacAddress contains an illegal value.";
	public static final String STAG_VLAN_ID_MANDATORY = "STagVLANId is mandatory when STagControl is not None";
	public static final String STAG_VLAN_PRIORITY_MANDATORY = "STagVLANPriority is mandatory when STagControl is not None";
	public static final String CTAG_VLAN_ID_MANDATORY = "CTagVLANId is mandatory when CTagControl is not None";
	public static final String CTAG_VLAN_PRIORITY_MANDATORY = "CTagVLANPriority is mandatory when CTagControl is not None";
	public static final String SYNCE_ADMIN_CONTROL_WITH_INVALID_SPEED = "SyncEAdministrationControl cannot be Enabled with an AdministrativeSpeedRate of 10";

	public static final String MTOSI_OPERATION_NOT_SUPPORTED                   = "The requested operation is not implemented."; //implies NO entry in the xml file for the specified operation.
	public static final String MTOSI_OPERATION_NOT_SUPPORTED_FOR_DEVICE_TYPE   = "The requested operation is not supported for this Managed Element type.";
	public static final String MTOSI_OPERATION_NOT_SUPPORTED_XML_IS_INVALID    = "The requested operation is not supported. The XML configuration file is not valid.";
	public static final String MTOSI_OPERATION_NOT_SUPPORTED_XML_WAS_NOT_FOUND = "The requested operation is not supported. The XML configuration file is not found.";
	public static final String CTP_LAYER_NOT_FOUND = "The CTP is missing mandatory layer PROP_HATTERAS_Ethernet.";
	
  public static final String CPE_HOSTNAME_MANDATORY = "CPEHostname is a mandatory parameter in the PROP_ADVA_RemoteCPE layer.";
  public static final String REMOTE_TYPE_MANDATORY = "RemoteType is a mandatory parameter in the PROP_ADVA_RemoteCPE layer.";
  public static final String TDFR_REFERENCE_EXISTS = "The specified TDFr Reference already exists.";
  public static final String TDFR_REFERENCE_NOT_EXIST = "The specified TDFr Reference does not exist.";

  public static final String CTP_MANDATORY_LAYER_MISSING =  "Mandatory Layer Missing";
  public static final String CTP_MANDATORY_PARAMS_MISSING = "CPEHostname, RemoteType and IPAddress is a mandatory parameter " +
          "in the PROP_ADVA_RemoteCPE layer.";
  public static final String PTP_PORT_TYPE_WRONG="The port type submitted is invalid. Accepted port types: ACC and NET";
  public static final String PTP_PORT_INDEX_WRONG="The port index submitted is invalid. Accepted port indexs: ACC-[456] or NET-[12]";

 public static final String NE_IS_PEER="The specified network element is peer and cannot be deleted";

  public static final String TRANSMISSION_PARAMS_MISSING="The transmission parameters are missing or are null";

  public static final String TUNNELINDEX_NOT_INT = "TunnelIndex should be an integer value.";
  public static final String VLANID_INDEX_NOT_INT = "VLANID should be an integer value.";
  public static final String CIR_INDEX_NOT_LONG = "CIR should be a long value.";
  public static final String CIR_INDEX_NOT_INT = "CIR should be an integer value.";
  public static final String EIR_INDEX_NOT_LONG = "EIR should be a long value.";
  public static final String EIR_INDEX_NOT_INT = "EIR should be an integer value.";
  public static final String COS_INDEX_NOT_INT = "COS should be an integer value.";
  public static final String BUFFERSIZE_NOT_LONG = "BufferSize should be a long value.";
  public static final String SVLANIDENABLED_NOT_BOOLEAN = "SVLANIDEnabled should be a boolean value.";
  public static final String STAGVLANID_NOT_INT = "STagVLANID should be an integer value.";
  public static final String RIP2PKTSENABLED_NOT_BOOLEAN = "Rip2PktsEnabled should be a boolean value.";
  public static final String ENCAPSULATIONTYPE_NOT_Ethernet= "EncapsulationType: only Ethernet is supported.";
  public static final String SUBNET_MASK_NAME= "Subnet mask name is not valid.";
  public static final String CREATELINK_NOT_BOOLEAN = "CreateLink should be a boolean value.";

  public static final String MDFR_NAME_MISSING = "The Maintenance Domain name must be specified in the request.";
  public static final String FDFR_EXISTS = "Floating domain fragment already exists.";
  public static final String MDFR_EXISTS = "The specified Maintenance Domain already exists.";
  public static final String MDFR_PTP_INVALID = "The specified Maintenance Association name is invalid.";
  public static final String MAFR_NAME_MISSING = "The Maintenance Association name must be specified in the request.";
  public static final String MEPFR_NAME_MISSING = "The specified Maintenance End Point was not found for the specified Maintenance Association Network.";
  public static final String MAFR_EXISTS = "The specified Maintenance Association already exists.";
  public static final String MEPFR_MIPFR_NAME_MISSING = "The Maintenance End Point name must be specified in the request.";
  public static final String MEP_TABLE_MISSING = "The MaMepList list must be specified in the request.";
  public static final String ME_NAME_IS_DIFFERENT = "The Managed Element must be the same for every pmObjectSelect.";
  public static final String MDFR_NOT_FOUND        = "The specified Maintenance Domain was not found.";
  public static final String MANETFR_NOT_FOUND        = "The specified Maintenance Association Network was not found for the specified Maintenance Domain.";
  public static final String MACOMPFR_NOT_FOUND        = "The specified Maintenance Association Component was not found for the specified Maintenance Association Network.";
  public static final String MEP_LIST_MISMATCH        = "The specified MEP id was not found in the Maintenance Association list.";
  public static final String MEP_LIST_NOT_FOUND        = "The specified MEP was not found.";
  public static final String MACOMPFR_EXISTS = "The specified Maintenance End Point already exists.";
  public static final String MDRF_NOT_FOUND = "The specified Maintenance Domain was not found.";
  public static final String MDFR_PTP_NAME_MISSING = "The Physical Termination Point name of the Maintenance Association must be specified in the request.";
  public static final String MANET_HAS_MACOMP        = "The specified Maintenance Association Network already has a Maintenance Association Component.";
  public static final String MEP_TABLE_IVALID = "The MaMepList list is invalid.";
	public final static String INVALID_VALUE_COMBINATION = "Invalid value combination for fields";
	public static final String CPE_HOST_NAME_MISSING = "The host name must be specified in the request.";
	public static final String EFM_REMOTE_TYPE_NOT_VALID   = "The specified Remote Type is not valid.";




	public static String getMessageIllegal(String param) {
		return param + CONTAINS_ILLEGAL_VALUE;
	}

	public static String getMessageCannotBeModifiedExistingFlow(String param) {
		return param + CANNOT_MODIFY_EXISTING_FLOW;
	}

	public static String getMessageMandatoryNewFlow(String param) {
		return param + MANDATORY_FOR_NEW_FLOW;
	}

	public static String getMessageMandatory(String param) {
		return param + MANDATORY;
	}

	public static String getInvalidValueCombinationErrorMessage(String... fieldNames){
		String fields=".";
		for (String fieldName : fieldNames) {
			fields=", "+fieldName+fields;
		}
		return INVALID_VALUE_COMBINATION+fields;
	}
}