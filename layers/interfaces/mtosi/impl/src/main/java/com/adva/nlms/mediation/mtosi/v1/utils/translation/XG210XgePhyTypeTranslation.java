/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */
package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum XG210XgePhyTypeTranslation implements TranslatableEnum {
   /* NOT_APPLICABLE 	(1, "NotApplicable"),
    auto_mdix 		(2, "Auto"),
    crossed   		(3, "Crossover"),
    uncrossed 		(4, "StraightThrough"); */
    NOT_APPLICABLE  (0, "n/a"),
    LAN         (1, "LAN-PHY"),
    WAN         (2, "WAN-PHY");

    private final int    mibValue;
    private final String mtosiString;

    private XG210XgePhyTypeTranslation(final int mibValue, final String mtosiString) {
        this.mibValue   = mibValue;
        this.mtosiString = mtosiString;
    }

    @Override
    public int getMIBValue() {
        return mibValue;
    }

    @Override
    public String getMtosiString() {
        return mtosiString;
    }
}
