/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum CMLinkLossFwdSignalTypeTranslation implements TranslatableEnum {
  NOT_APPLICABLE     (0, "n/a"),
  LLF_EFM_SIGNAL     (1, "EfmRld"),
  LLF_LINK_DOWN      (2, "LinkDown");

  private final int    mibValue;
  private final String mtosiString;

  private CMLinkLossFwdSignalTypeTranslation (final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}