/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

public enum GE20XBitLineTypeTranslation implements TranslatableEnum {
    t1				(1,  "T1"),
    e1				(2,  "E1"),
    type_2048kHz	(3,  "2048KHZ"),
    NOT_APPLICABLE	(-1, "n/a");

  private final int    mibValue;
  private final String mtosiString;

  private GE20XBitLineTypeTranslation(final int mibValue, final String mtosiString) {
    this.mibValue   = mibValue;
    this.mtosiString = mtosiString;
  }

  @Override
  public int getMIBValue() {
    return mibValue;
  }

  @Override
  public String getMtosiString() {
    return mtosiString;
  }
}