/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */

package com.adva.nlms.mediation.mtosi.v1.utils;

import com.adva.nlms.common.topology.SubnetPropertiesDTO;
import com.adva.nlms.common.snmp.NoSuchMDObjectException;
import com.adva.nlms.mediation.config.ConfigCtrlImpl;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.topology.SubnetDBImpl;
import com.adva.nlms.mediation.topology.SubnetHdlrLocal;
import ws.v1.tmf854.ProcessingFailureException;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.ListIterator;

/**
 *
 */
public class ManagedElementUtils {

  public static String getSubnetPath(NetworkElement ne)
  {
    SubnetHdlrLocal subnetHdlr = ConfigCtrlImpl.get().getHandlers().getSubnetHdlr();

    StringBuilder subnetPathBuilder = new StringBuilder();
    int subnetId = ne.getParentID();

    while (subnetId != subnetHdlr.getTopLevelSubnetID()) {
      SubnetDBImpl subnetDbImpl = null;
      try {
        subnetDbImpl = subnetHdlr.getSubnetDBImpl(subnetId, false);
      } catch (NoSuchMDObjectException e) {
        break;
      }

      subnetPathBuilder.insert(0, subnetDbImpl.getName()).insert(0, '/');
      subnetId = subnetDbImpl.getParentID();
    }

    return subnetPathBuilder.toString();
  }

  public static int getSubnetsToCreate(String subnetPaths, List<String> subnetsToCreate)
          throws ProcessingFailureException
  {
    SubnetHdlrLocal subnetHdlr = ConfigCtrlImpl.get().getHandlers().getSubnetHdlr();

    // workaround: replace "\/" by "\t" and after parsing back to "/"
    subnetPaths = subnetPaths.replace("\\/", "\\t");
    List<String> subnetPathList = new ArrayList<String>(Arrays.asList(subnetPaths.split("/")));
    // remove first element if it is null (because subnetPath starts with a "/")
    if (subnetPathList.size() > 0 && subnetPathList.get(0).length() == 0) {
      subnetPathList.remove(0);
    }
    // change back "\t" to "/" and remove leading and trailing white spaces
    for (ListIterator<String> iter = subnetPathList.listIterator(); iter.hasNext();) {
      iter.set(iter.next().replace("\\t", "/").trim());
    }

    // determine Subnet path to be created
    int parentId = subnetHdlr.getTopLevelSubnetID();
    SubnetPropertiesDTO subnetProps = new SubnetPropertiesDTO();
    for (String subnetName : subnetPathList) {
      // throw exception if the subnet-name is empty
      if (subnetName == null || subnetName.isEmpty()) {
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
                "The SubnetPath is wrong (one or more subnet names are empty).");
      }
      if (subnetProps != null) {
        subnetProps = subnetHdlr.getContainedSubnet(parentId, subnetName);
      }
      if (subnetProps != null) {
        parentId = subnetProps.id;
      } else {
        subnetsToCreate.add(subnetName);
      }
    }

    // check the subnetpath
    if (subnetPaths.isEmpty() || subnetPaths.charAt(0) != '/' || subnetPathList.isEmpty())
      throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_INVALID_INPUT,
              "The SubnetPath is missing or wrong.");

    return parentId;
  }
  
}
