/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.factory;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.dto.attr.ProtectionGroupF3Attr;
import com.adva.nlms.mediation.config.mofacade.MOFacadeManager;
import com.adva.nlms.mediation.mtosi.v1.adapter.facade.MtosiProtectionGroupEFMDTO;
import com.adva.nlms.mediation.mtosi.v1.adapter.facade.MtosiProtectionGroupEFMDTOImpl;
import com.adva.nlms.mediation.mtosi.v1.adapter.facade.response.ResponseFactory;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.LayeredParameterUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.BooleanTypeTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.CMProtectionStatusTranslation;
import com.adva.nlms.mediation.mtosi.v1.utils.translation.ProtectionSwitchMode;
import jakarta.xml.bind.JAXBElement;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import v1.tmf854.DirectionalityT;
import v1.tmf854.FloatingTerminationPointT;
import v1.tmf854.LayeredParametersListT;
import v1.tmf854.LayeredParametersT;
import v1.tmf854.NVSListT;
import v1.tmf854.NameAndStringValueT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854.ObjectFactory;
import v1.tmf854.ProcessingFailureExceptionT;
import v1.tmf854.ResourceStateEnumT;
import v1.tmf854.ResourceStateT;
import v1.tmf854.SourceEnumT;
import v1.tmf854.SourceT;
import v1.tmf854.TPDataT;
import v1.tmf854.TerminationPointT;
import v1.tmf854ext.adva.CreateFTPResponseT;
import v1.tmf854ext.adva.FTPCreateDataT;
import ws.v1.tmf854.ProcessingFailureException;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class CreateFloatingTerminationPointFactory<T extends ManagedObjectAttr> extends ResponseFactory {

  private static Logger logger = LogManager.getLogger(CreateFloatingTerminationPointFactory.class);

  private static final String PTP = "PTP";
  private static final String MD = "MD";
  private static final String ME = "ME";
  private static final String ALLOCATED_NUMBER_PARAM_DEFAULT = "2";
  private static final String ALLOCATION_MAXIMUM_PARAM_DEFAULT = "2";
  private static final String INTERFACE_TYPE_PARAM_DEFAULT = "NNI";
  private static final String PORT_TP_ROLE_STATE_PARAM_DEFAULT = "internal";
  private static final String SWITCH_DIRECTION_DEFAULT = "Unidirectional";
  private static final Integer FTP_DOMAIN = 1;
  private int neType = 0;
  private boolean isGE112 = false;


  public MtosiProtectionGroupEFMDTO validateAndParse(MtosiAddress mtosiAddress, FTPCreateDataT ftpCreateData,
                                          MOFacadeManager moFacadeManager) throws ProcessingFailureException {
    MtosiUtils.validateNE(mtosiAddress.getNE());
    neType = mtosiAddress.getNE().getNetworkElementType();

    switch (neType) {
      case NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
        return processRequestForCM(mtosiAddress, ftpCreateData, moFacadeManager);
      default:
        return null;
    }
  }

  public MtosiProtectionGroupEFMDTO processRequestForCM(MtosiAddress mtosiAddress, FTPCreateDataT ftpCreateData,
                                                        MOFacadeManager moFacadeManager) throws ProcessingFailureException {
    // Validate request body
    String ftpNm = ftpCreateData.getName().getFtpNm();
    if (ftpNm == null)
    {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
          ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NAME_MISSING);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    int slot = NamingTranslationFactory.slotNumberFromShelfCombo(ftpNm);
    if(slot==-1)
    {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
          ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NAME_NOT_VALID);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    else if (ManagedElementFactory.needsSlotIncrement(mtosiAddress.getNeType()))
    {
      slot = slot + 1;
    }

    int shelf = NamingTranslationFactory.shelfNumberFromShelfCombo(ftpNm);
    if(shelf==-1)
    {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
          ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NAME_NOT_VALID);
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }


    TPDataT ftpData = new TPDataT();
    JAXBElement<LayeredParametersListT> layersJ = ftpCreateData.getTransmissionParams();
    if (layersJ == null)
    {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
          ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
              .getMessageMandatory(LayeredParams.LrLag.ALLOCATED_NUMBER_PARAM));
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    LayeredParametersListT transmissionParams = layersJ.getValue();
    if (transmissionParams == null)
    {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
          ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
              .getMessageMandatory(LayeredParams.LrLag.ALLOCATED_NUMBER_PARAM));
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    //allocatedNumber
    String allocatedNumber = LayeredParameterUtils.getLayeredParameter(transmissionParams, LayeredParams.LR_LAG,
        LayeredParams.LrLag.ALLOCATED_NUMBER_PARAM);
    if (allocatedNumber != null)
    {
      if (!MtosiUtils.isInteger(allocatedNumber))
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
            ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.ALLOCATED_NUMBER_ILLEGAL_VALUE);
        ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
        throw pfe;
      }
      Integer allocatedNumberValue = Integer.valueOf(allocatedNumber);
      if (allocatedNumberValue != 2)
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
            ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.ALLOCATED_NUMBER_ILLEGAL_VALUE);
        ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
        throw pfe;
      }
    }
    else
    {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
          ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
              .getMessageMandatory(LayeredParams.LrLag.ALLOCATED_NUMBER_PARAM));
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }

    //fragmentServer
    String fragmentServerLayer = LayeredParameterUtils.getLayeredParameter(transmissionParams,
        LayeredParams.LR_LAG, LayeredParams.LrLag.FRAGMENT_SERVER_LAYER_PARAM);
    if (fragmentServerLayer != null)
    {
      if (!fragmentServerLayer.equals(LayeredParams.LR_ETHERNET))
      {
        ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
            ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants.FRAGMENT_SERVER_LAYER_ILLEGAL_VALUE);
        ProcessingFailureException pfe = new ProcessingFailureException(pfet.getReason(), pfet);
        throw pfe;
      }
    }
    else
    {
      ProcessingFailureExceptionT pfet = ExceptionUtils.formatProcessingFailureException(
          ExceptionUtils.EXCPT_INVALID_INPUT, MtosiErrorConstants
              .getMessageMandatory(LayeredParams.LrLag.FRAGMENT_SERVER_LAYER_PARAM));
      throw new ProcessingFailureException(pfet.getReason(), pfet);
    }
    ftpData.setTransmissionParams(ftpCreateData.getTransmissionParams());



    Set<String> layers = new HashSet<>();
    MtosiProtectionGroupEFMDTOImpl floatingTerminationPointDTO = new MtosiProtectionGroupEFMDTOImpl(moFacadeManager);
    floatingTerminationPointDTO.setLayers(layers);
    //Parsing Name
    parseBaseParameters(mtosiAddress, floatingTerminationPointDTO);


    JAXBElement<LayeredParametersListT> layeredParametersTypeList = ftpCreateData.getTransmissionParams();
    List<LayeredParametersT> layeredParametersList = layeredParametersTypeList.getValue().getLayeredParameters();
    for (LayeredParametersT theParameters : layeredParametersList) {
      if (theParameters.getLayer() == null) continue;
      String layer = theParameters.getLayer();
      layers.add(layer);
      NVSListT nameAndValueStringListT = theParameters.getTransmissionParams();
      if (nameAndValueStringListT != null) {
        for (NameAndStringValueT valueType : nameAndValueStringListT.getNvs()) {
          String name = valueType.getName();
          floatingTerminationPointDTO.setValue(layer + "." + name, valueType.getValue());
        }
      }
    }

    return floatingTerminationPointDTO;

  }

  public void parseBaseParameters(MtosiAddress mtosiAddress, MtosiProtectionGroupEFMDTOImpl floatingTerminationPointDTO) {
    debug("parsing base parameters started");
    floatingTerminationPointDTO.setNetworkElement(mtosiAddress.getNE());
    floatingTerminationPointDTO.setPGName(mtosiAddress.getNaming().getFtpNm());
    floatingTerminationPointDTO.setMeName(mtosiAddress.getNaming().getMeNm());
    floatingTerminationPointDTO.setMdName(mtosiAddress.getNaming().getMdNm());
    debug("parsing base parameters completed");
  }

  public void buildResponse(CreateFTPResponseT response, DTO<ProtectionGroupF3Attr> ftpMO,
                            MtosiProtectionGroupEFMDTO floatingTerminationPoint) throws ProcessingFailureException {
    int neType = floatingTerminationPoint.getNetworkElement().getNetworkElementType();
    switch (neType) {
      case NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
        buildResponseFtpEFM(response, ftpMO, floatingTerminationPoint);
        break;
      default:
        logger.error("Unexpected ne type has been found while building mtosi response");
        break;
    }
  }

  protected void buildResponseFtpEFM(CreateFTPResponseT response, DTO<ProtectionGroupF3Attr> ftpMO,
                                     MtosiProtectionGroupEFMDTO floatingTerminationPoint) throws ProcessingFailureException  {
    debug("building response for EFM started");
    FloatingTerminationPointT floatingTerminationPointT = buildFTPResponseForEFM(ftpMO, floatingTerminationPoint);

    final ObjectFactory objFactory = new ObjectFactory();
    floatingTerminationPointT.setDiscoveredName(objFactory.createFloatingTerminationPointTDiscoveredName(floatingTerminationPoint.getPGName()));
    // namingOS
    floatingTerminationPointT.setNamingOS(objFactory.createFloatingTerminationPointTNamingOS(OSFactory.getNmsName()));

    // source
    final SourceT source = new SourceT();
    source.setValue(SourceEnumT.NETWORK_ME);
    floatingTerminationPointT.setSource(objFactory.createFloatingTerminationPointTSource(source));

    // resource state
    final ResourceStateT resourceState = new ResourceStateT();
    resourceState.setValue(ResourceStateEnumT.INSTALLED);
    floatingTerminationPointT.setResourceState(objFactory.createFloatingTerminationPointTResourceState(resourceState));

    // direction
    floatingTerminationPointT.setDirection(objFactory.createFloatingTerminationPointTDirection(DirectionalityT.D_BIDIRECTIONAL));

    // edgePoint
    floatingTerminationPointT.setEdgePoint(objFactory.createFloatingTerminationPointTEdgePoint(Boolean.FALSE));

    prepareResponse(response, floatingTerminationPointT,objFactory);
    debug("building response for EFM completed");
  }

  public void prepareResponse(CreateFTPResponseT response,
                              FloatingTerminationPointT floatingTerminationPointT,
                              ObjectFactory objFactory) {
    TerminationPointT tp = objFactory.createTerminationPointT();
    tp.setFtp(floatingTerminationPointT);
    response.setTheFTP(tp);
  }

  private FloatingTerminationPointT buildFTPResponseForEFM(DTO<ProtectionGroupF3Attr> ftpMO, MtosiProtectionGroupEFMDTO floatingTerminationPoint) {
    debug("Adding layers into the response");
    LayeredParametersListT paramList = createParameterListType(1,LayeredParams.LR_LAG_FRAGMENT,
        LayeredParams.LR_LAG,
        LayeredParams.LR_ETHERNET,
        LayeredParams.PROP_ADVA_PROTECTION_FSP150_1PLUS1,
        LayeredParams.PROP_ADVA_ETHERNET);

    debug("Adding FTP_DOMAIN/LR_LAG layer's attributes");
    addFTP_DOMAIN_LR_LAG_EFM();

    debug("Adding FTP_DOMAIN/LR_ETHERNET layer's attributes");
    addFTP_DOMAIN_LR_ETHERNET_EFM();


    debug("Adding FTP_DOMAIN/PROP_ADVA_PROTECTION_FSP150_1PLUS1 layer's attributes");
    addFTP_DOMAIN_PROP_ADVA_PROTECTION_FSP150_1PLUS1_EFM(ftpMO);


    return createFloatingTerminationPointType(paramList, floatingTerminationPoint.getMdName(),
        floatingTerminationPoint.getMeName(),
        floatingTerminationPoint.getPGName());
  }

  public FloatingTerminationPointT createFloatingTerminationPointType(LayeredParametersListT paramList, String mdName, String meName, String pgName) {
    final ObjectFactory objFactory = new ObjectFactory();
    final FloatingTerminationPointT floatingTerminationPointT = objFactory.createFloatingTerminationPointT();
    NamingAttributesT namingAttribute = NamingTranslationFactory.getNamingAttributes( meName, pgName);
    floatingTerminationPointT.setName(objFactory.createFloatingTerminationPointTName(namingAttribute));

    floatingTerminationPointT.setTransmissionParams(objFactory.createConnectionTerminationPointTTransmissionParams(paramList));

    return floatingTerminationPointT;
  }

  private void addFTP_DOMAIN_PROP_ADVA_PROTECTION_FSP150_1PLUS1_EFM(DTO<ProtectionGroupF3Attr> ftpMO) {
    List<Pair<String, Object>> _PROP_ADVA_ProtectionFSP1501Plus1_pairs = new ArrayList<>();
    _PROP_ADVA_ProtectionFSP1501Plus1_pairs.add(Pair.of(LayeredParams.LrPropAdvaProtectionFSP150CMNTU.PROTECTION_SWITCH_MODE,
        (Object) ProtectionSwitchMode.valueOfId(ftpMO.getValue(ProtectionGroupF3Attr.SWITCH_MODE)).getLabel()));
    BooleanTypeTranslation revertive = BooleanTypeTranslation.valueOfBoolean(ftpMO.getValue(ProtectionGroupF3Attr.REVERTIVE));
    _PROP_ADVA_ProtectionFSP1501Plus1_pairs.add(Pair.of(LayeredParams.LrPropAdvaProtectionFSP150CMNTU.REVERTIVE, (Object) revertive.getLabel()));
    if (revertive.getBooleanValue()) {
      _PROP_ADVA_ProtectionFSP1501Plus1_pairs.add(Pair.of(LayeredParams.LrPropAdvaProtectionFSP150CMNTU.WAIT_TO_RESTORE,
          (Object) ftpMO.getValue(ProtectionGroupF3Attr.WAIT_TO_RESTORE)));
    }
    _PROP_ADVA_ProtectionFSP1501Plus1_pairs.add(Pair.of(LayeredParams.LrPropAdvaProtectionFSP150CMNTU.SWITCH_DIRECTION,
        (Object) SWITCH_DIRECTION_DEFAULT));
    _PROP_ADVA_ProtectionFSP1501Plus1_pairs.add(Pair.of(LayeredParams.LrPropAdvaProtectionFSP150CMNTU.PROTECTION_STATUS,
        (Object) MtosiUtils.getMtosiString(CMProtectionStatusTranslation.NOT_APPLICABLE, ftpMO.getValue(ProtectionGroupF3Attr.GROUP_STATUS))));
    addParams(FTP_DOMAIN, LayeredParams.PROP_ADVA_PROTECTION_FSP150_1PLUS1, _PROP_ADVA_ProtectionFSP1501Plus1_pairs);
  }

  private void addFTP_DOMAIN_LR_ETHERNET_EFM() {
    List<Pair<String, Object>> _LR_Ethernet_pairs = new ArrayList<>();
    _LR_Ethernet_pairs.add(Pair.of(LayeredParams.LrEthernet.CONNECTIONLESS_PORT_PARAM, (Object) BooleanTypeTranslation.ENABLED.getLabel()));
    _LR_Ethernet_pairs.add(Pair.of(LayeredParams.LrEthernet.INTERFACE_TYPE_PARAM, (Object) INTERFACE_TYPE_PARAM_DEFAULT));
    _LR_Ethernet_pairs.add(Pair.of(LayeredParams.LrEthernet.PORT_TP_ROLE_STATE_PARAM, (Object) PORT_TP_ROLE_STATE_PARAM_DEFAULT));
    _LR_Ethernet_pairs.add(Pair.of(LayeredParams.LrEthernet.MAX_NUM_FDFRS_PARAM,(Object)  "0"));
    _LR_Ethernet_pairs.add(Pair.of(LayeredParams.LrEthernet.NUM_CONFIGURED_FDFRS_PARAM, (Object) "0"));
    addParams(FTP_DOMAIN, LayeredParams.LR_ETHERNET, _LR_Ethernet_pairs);
  }

  private void addFTP_DOMAIN_LR_LAG_EFM() {
    List<Pair<String, Object>> _LR_LAG_pairs = new ArrayList<>();
    _LR_LAG_pairs.add(Pair.of(LayeredParams.LrLag.ALLOCATED_NUMBER_PARAM, (Object) ALLOCATED_NUMBER_PARAM_DEFAULT));
    _LR_LAG_pairs.add(Pair.of(LayeredParams.LrLag.ALLOCATION_MAXIMUM_PARAM, (Object) ALLOCATION_MAXIMUM_PARAM_DEFAULT));
    _LR_LAG_pairs.add(Pair.of(LayeredParams.LrLag.FRAGMENT_SERVER_LAYER_PARAM, (Object) LayeredParams.LR_ETHERNET));
    addParams(FTP_DOMAIN, com.adva.nlms.mediation.mtosi.v1.factory.LayeredParams.LR_LAG, _LR_LAG_pairs);
  }

  private void debug(String message) {
    if (logger.isDebugEnabled()) {
      logger.debug(message);
    }
  }

}
