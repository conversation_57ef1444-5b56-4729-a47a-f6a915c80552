/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.common.snmp.MDOperationFailedException;
import com.adva.nlms.mediation.common.transactions.NetTransactionException;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.common.validation.SPValidationException;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementImpl;
import com.adva.nlms.mediation.mtosi.v1.MtosiConstants;
import com.adva.nlms.mediation.mtosi.v1.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v1.mtosisupport.MtosiSupportValidator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import com.adva.nlms.mediation.mtosi.v1.worker.AbstractMtosiWorker;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import com.adva.nlms.mediation.security.api.event.SystemEventLogging;
import com.adva.nlms.mediation.topology.SubnetHdlrLocal;
import v1.tmf854.HeaderT;
import v1.tmf854.NamingAttributesT;
import v1.tmf854ext.adva.DeleteManagedElementResponseT;
import v1.tmf854ext.adva.DeleteManagedElementT;
import ws.v1.tmf854.ProcessingFailureException;

import jakarta.xml.ws.Holder;

public class DeleteManagedElementWorker extends AbstractMtosiWorker {

  protected DeleteManagedElementT mtosiBody;
  protected DeleteManagedElementResponseT response = new DeleteManagedElementResponseT();
  protected NamingAttributesT meName;
  private NetworkElement ne;

  public DeleteManagedElementWorker(DeleteManagedElementT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiHeader, "deleteManagedElement", "deleteManagedElement", "deleteManagedElementResponse");
    this.mtosiBody = mtosiBody;
  }

  @Override
  protected void parse() throws Exception {
    meName = mtosiBody.getMeName();
    if (meName == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ME name has not been specified.");
    }

    if (meName.getMdNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
        MtosiErrorConstants.MD_NAME_MISSING);
    }

    if (!meName.getMdNm().equals(OSFactory.getMDNm())) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        MtosiErrorConstants.MD_NOT_FOUND);
    }

    if (meName.getMeNm() == null) {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              "The ME name has not been specified.");
    }
    ne = this.getMtosiCtrl().getLegacyMtosiMOFacade().getNEByName(meName.getMeNm());
    if (ne == null)
    {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_ENTITY_NOT_FOUND,
        MtosiErrorConstants.ME_NOT_FOUND);
    }
    if (ne.isPeer())
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT,
              MtosiErrorConstants.NE_IS_PEER);
  }

  @Override
  protected void validate(MtosiSupportValidator validator) throws ProcessingFailureException  {
	  validator.validate(ne.getDefaultNetworkElementTypeString());
  }

  @Override
  protected void mediate() throws Exception {

    // perform transaction
    transact(ne);
  }

  private void transact(NetworkElement ne) throws ObjectInUseException, NetTransactionException, MDOperationFailedException, SPValidationException {

    SubnetHdlrLocal subnetHdlr = this.getMtosiCtrl().getConfigCtrl().getHandlers().getSubnetHdlr();
    String neName = ne.getName();
    int neId = ne.getID();

    // *** delete NE
    subnetHdlr.deleteNe((NetworkElementImpl)ne);

    this.getMtosiCtrl().getSecurityCtrl().getSystemEventLogging()
                .addNeEvent(SystemEventLogging.Context.MTOSI, neName, neId, SystemAction.ModifyNetwork, neName, neName);
  }

  @Override
  protected void response() throws Exception {
  }

  @Override
  public DeleteManagedElementResponseT getSuccessResponse()
  {
    if (response == null)
      return null;
    response.setTmf854Version(MtosiConstants.VERSION);
    return response;
  }
}