/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: p<PERSON><PERSON>akis
 */

package com.adva.nlms.mediation.mtosi.v1.utils.translation;

/**
 * Created by IntelliJ IDEA.
 * User: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date: 2008-12-18
 * Time: 23:12:52
 * To change this template use File | Settings | File Templates.
 */
public enum ActiveNetworkTranslation
{
    BOTH           (0, "Both"),
    NET_A          (1, "NET-A"),
    NET_B          (2, "NET-B"),
    NONE           (3, "None"),
    NOT_APPLICABLE (4, "n/a");

    //------------------------------------------------------------------------------------------------------------------
    private final int    mibValue;
    private final String mtosiString;

    //------------------------------------------------------------------------------------------------------------------
    /**
     * Constructor.
     * @param mibValue    The MIB defined value
     * @param mtosiString  The string representation used in MTOSI layer.
     */
    private ActiveNetworkTranslation (final int mibValue, final String mtosiString)
    {
      this.mibValue   = mibValue;
      this.mtosiString = mtosiString;
    }
    /**
     * Returns the MIB defined value.
     * @return the MIB defined value.
     */
    public int getMIBValue() {
      return mibValue;
    }


    /**
     * Returns the string representation used in MTOSI layer.
     * @return the string representation used in MTOSI layer.
     */
    public String getMtosiString() {
      return mtosiString;
    }

    /**
     * Returns the string representation used in MTOSI layer.
     * @param mibValue  The MIB defined value
     * @return the string representation used in MTOSI layer.
     */
    public static String getMtosiString(final int mibValue)
    {
      ActiveNetworkTranslation anEnum = NOT_APPLICABLE;  // the return value

      for (ActiveNetworkTranslation tmpEnum : values())
      {
        if (mibValue == tmpEnum.getMIBValue())
        {
          anEnum = tmpEnum;
          break;
        }
      }
      return anEnum.getMtosiString();
    }

    /**
     * Returns the string representation used in MTOSI layer.
     * @param mtosiString  The MIB defined value
     * @return the string representation used in MTOSI layer.
     */
    public static int getMIBValue (final String mtosiString)
    {
      ActiveNetworkTranslation anEnum = NOT_APPLICABLE;  // the return value

      for (ActiveNetworkTranslation tmpEnum : values())
      {
        if (mtosiString.equals(tmpEnum.getMtosiString()))
        {
          anEnum = tmpEnum;
          break;
        }
      }
      return anEnum.getMIBValue();
    }
}
