/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: pcharizakis
 */
package com.adva.nlms.mediation.mtosi.v1.worker.me;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.dto.DTO;
import com.adva.nlms.mediation.config.dto.attr.EthernetTrafficPortF3Attr;
import com.adva.nlms.mediation.config.dto.attr.FlowF3Attr;
import com.adva.nlms.mediation.config.dto.attr.ManagedObjectAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3AccAttr;
import com.adva.nlms.mediation.config.dto.attr.PortF3NetAttr;
import com.adva.nlms.mediation.config.dto.attr.ProtectionGroupF3Attr;
import com.adva.nlms.mediation.config.mtosi.facade.MtosiMOFacade;
import com.adva.nlms.mediation.mtosi.v1.adapter.facade.MtosiTerminationPointEFMDTOImpl;
import com.adva.nlms.mediation.mtosi.v1.factory.ManagedElementFactory;
import com.adva.nlms.mediation.mtosi.v1.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v1.mediation.fromMtosi.MtosiCTPMediator;
import com.adva.nlms.mediation.mtosi.v1.utils.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiAddress;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v1.utils.MtosiUtils;
import com.adva.nlms.mediation.mtosi.v1.utils.ServiceUtils;
import v1.tmf854.GetContainedCurrentCTPsT;
import v1.tmf854.HeaderT;
import v1.tmf854.ObjectFactory;

import jakarta.xml.ws.Holder;
import java.util.ArrayList;
import java.util.List;

public class GetContainedCurrentCTPsWorkerEFM extends  GetContainedCurrentCTPsWorker{
  private MtosiTerminationPointEFMDTOImpl mtosiTerminationPointEFMDTO;
  private List<DTO<FlowF3Attr>> ctps = new ArrayList<>();
  private MtosiAddress mtosiAddress;
  private MtosiMOFacade facade;
  private DTO<PortF3AccAttr> port;
  private DTO<ProtectionGroupF3Attr> pg;
  private DTO workingPort, protectionPort;

  public GetContainedCurrentCTPsWorkerEFM(GetContainedCurrentCTPsT mtosiBody, Holder<HeaderT> mtosiHeader) {
    super(mtosiBody, mtosiHeader);
    this.mtosiBody = mtosiBody;
    this.mtosiTerminationPointEFMDTO = new MtosiTerminationPointEFMDTOImpl();
  }

  @Override
  public void parse() throws Exception {
    super.parse();
    mtosiAddress = new MtosiAddress(namingAttributes);
    facade = getMtosiCtrl().getMoFacadeManager().getFacadeViaNeId(MtosiMOFacade.class, mtosiAddress.getNE().getID());
  }

  @Override
  protected void mediate() throws Exception {
    if (NamingTranslationFactory.isPort(namingAttributes)) {
      executeContainedCurrentCTPsOnPTP();
    } else if (NamingTranslationFactory.isFtp(namingAttributes)) {
      executeContainedCurrentCTPsOnFTP();
    } else {
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, "TP must be a PTP or FTP. CTPs are not supported.");
    }
  }

  private void executeContainedCurrentCTPsOnPTP() throws Exception {
    if (mtosiAddress.isAccPort(mtosiAddress.getNaming().getPtpNm())){
      DTO<PortF3AccAttr> port = facade.findDTOViaMtosiName(mtosiAddress.getNE().getID(), mtosiAddress.getNaming().getPtpNm(), PortF3AccAttr.class);
      if(port == null){
        throw ServiceUtils.createNewPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.PTP_NOT_FOUND);
      }

      final ObjectFactory objectFactory = new ObjectFactory();
      connectionTerminationPointListT = objectFactory.createConnectionTerminationPointListT();

      List<DTO<FlowF3Attr>> flows = facade.queryDTO(mtosiAddress.getNE().getID(),FlowF3Attr.class);
      for(DTO<FlowF3Attr> flow : flows ){
        if(flow.getValue(FlowF3Attr.ENTITY_INDEX).toStringIndex().contains(port.getValue(PortF3AccAttr.ENTITY_INDEX).toStringIndex()+".") ) {
          MtosiCTPMediator ctpMediator = new MtosiCTPMediator(new MtosiAddress(namingAttributes), facade, flow);
          connectionTerminationPointListT.getCTP().add(ctpMediator.toMtosiCTP());
        }
      }
    }else if(mtosiAddress.isNetPort(mtosiAddress.getNaming().getPtpNm())){
      //Valid port but not supported => return empty response
    }else{
      throw ServiceUtils.createNewPFE(getMtosiHeader(), ExceptionUtils.EXCPT_INVALID_INPUT, "The specified PTP is not supported.");
    }

  }

  private void executeContainedCurrentCTPsOnFTP() throws Exception {
    final ObjectFactory objectFactory = new ObjectFactory();
    connectionTerminationPointListT = objectFactory.createConnectionTerminationPointListT();
    NetworkElement ne = ManagedElementFactory.getAndValidateNE(namingAttributes);

    pg = facade.findDTOViaMtosiName(mtosiAddress.getNE().getID(), mtosiAddress.getNaming().getFtpNm(),ProtectionGroupF3Attr.class);
    if(pg == null){
      throw ExceptionUtils.createNewMPFE(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND, MtosiErrorConstants.FTP_NOT_FOUND);
    }
    workingPort = facade.findDTOViaEntityIndex(mtosiAddress.getNE().getID(), pg.getValue(ProtectionGroupF3Attr.WORK_PORT_ENTITY_INDEX), getEntityClassByNeType(ne.getNetworkElementType()));
    protectionPort = facade.findDTOViaEntityIndex(mtosiAddress.getNE().getID(), pg.getValue(ProtectionGroupF3Attr.PROT_PORT_ENTITY_INDEX),getEntityClassByNeType(ne.getNetworkElementType()));
    List<DTO<EthernetTrafficPortF3Attr>> list = new ArrayList<>();
    list.add(workingPort);
    list.add(protectionPort);
    list = (ArrayList<DTO<EthernetTrafficPortF3Attr>>)facade.refreshDTOs(ne.getID(),list.toArray(new DTO[list.size()]));
    workingPort = MtosiUtils.setRefreshedDTOs(list,workingPort);
    protectionPort = MtosiUtils.setRefreshedDTOs(list,protectionPort);

    MtosiCTPMediator ctpMediatorWorking = new MtosiCTPMediator(mtosiAddress,facade, workingPort);
    connectionTerminationPointListT.getCTP().add(ctpMediatorWorking.toMtosiWorkingProtectCTP((String) workingPort.getValue(ManagedObjectAttr.MTOSI_NAME),"Working", "Active"));
    MtosiCTPMediator ctpMediatorProtection = new MtosiCTPMediator(mtosiAddress,facade, protectionPort);
    connectionTerminationPointListT.getCTP().add(ctpMediatorProtection.toMtosiWorkingProtectCTP((String) protectionPort.getValue(ManagedObjectAttr.MTOSI_NAME), "Protect","Standby"));
    }


  private Class getEntityClassByNeType(int type){
    switch (type) {
      case NeTypeIds.NETWORK_ELEMENT_TYPE_F3_EFM:
        return PortF3NetAttr.class;
      default:
        return null;
    }
  }


}
