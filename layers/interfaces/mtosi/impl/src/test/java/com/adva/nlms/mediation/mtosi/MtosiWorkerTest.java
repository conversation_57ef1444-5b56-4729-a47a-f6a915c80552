/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: polykarposc
 */
package com.adva.nlms.mediation.mtosi;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.common.redundancy.WorkMode;
import com.adva.nlms.common.snmp.NoSuchMDObjectException;
import com.adva.nlms.mediation.config.ConfigCtrlImpl;
import com.adva.nlms.mediation.config.Handlers;
import com.adva.nlms.mediation.config.NetworkElement;
import com.adva.nlms.mediation.config.NetworkElementHdlrLocal;
import com.adva.nlms.mediation.config.mtosi.NetworkElementMTOSIOperations;
import com.adva.nlms.mediation.infrastructure.server_infra.server_modules.api.out.ModuleInitializationException;
import com.adva.nlms.mediation.mtosi.v2.factory.NamingTranslationFactory;
import com.adva.nlms.mediation.mtosi.v2.factory.OSFactory;
import com.adva.nlms.mediation.mtosi.v2.utils.MtosiErrorConstants;
import com.adva.nlms.mediation.mtosi.v2.utils.exceptions.ExceptionUtils;
import com.adva.nlms.mediation.mtosi.v2.utils.exceptions.MtosiProcessingFailureException;
import com.adva.nlms.mediation.mtosi.v2.worker.me.GetTerminationPointWorker;
import com.adva.nlms.mediation.mtosi.v2.worker.me.GetTerminationPointWorkerFacade;
import com.adva.nlms.mediation.server.ServerCtrlImpl;
import com.adva.nlms.mediation.topology.SubnetHdlrLocal;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.tmforum.mtop.fmw.xsd.hdr.v1.Header;
import org.tmforum.mtop.mri.xsd.tpr.v1.RequestProfile2Type;

import jakarta.xml.ws.Holder;
import java.lang.reflect.Field;

import static org.hamcrest.core.Is.is;
import static org.hamcrest.core.Is.isA;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MtosiWorkerTest {

  private GetTerminationPointWorkerFacade fc;

  @Mock
  RequestProfile2Type mtosiBody;

  @Mock
  Header mtosiHeader;

  @Mock
  Handlers handlers;

  @Mock
  NetworkElementHdlrLocal networkElementHdlr;

  @Mock
  SubnetHdlrLocal subnetHdlrLocal;

  @Mock
  NetworkElement networkElement;

  @Mock
  NetworkElementMTOSIOperations neMtosiOperations;


  private static final ConfigCtrlImpl configCtrl = mock(ConfigCtrlImpl.class);
  private static final ServerCtrlImpl serverCtrl = mock(ServerCtrlImpl.class);
  private static final MtosiCtrlImpl mtosiCtrl = new MtosiCtrlImpl();


  @Before
  public void prepare()throws Exception{
    Holder holder = new Holder();
    holder.value = mtosiHeader;
    GetTerminationPointWorker gTPWorker = new GetTerminationPointWorker(mtosiBody, holder);
    fc = new GetTerminationPointWorkerFacade(gTPWorker);
    prepareMtosiCtrlImpl();
    mockHandlers();
  }

   @Test
   public void  testGetTerminationPointParseNullTPRef() throws Exception{
     try{
      fc.parse();
     }catch(Throwable ex) {
       Assert.assertThat(ex, isA((Class)MtosiProcessingFailureException.class));
       Assert.assertThat(ex.getMessage(), is(MtosiErrorConstants.MD_NAME_MISSING));
       Assert.assertThat(((MtosiProcessingFailureException)ex).getException(), is(ExceptionUtils.EXCPT_INVALID_INPUT));
       return;
     }
     Assert.fail();
  }

  @Test
  public void  testGetTerminationPointParseNeNotFound() throws Exception{
    try{
      RequestProfile2Type fullBody = new  RequestProfile2Type();
      fullBody.setTpRef(NamingTranslationFactory.createNamingAttributesPTP("ADVA/Network","",""));
      Holder holder = new Holder();
      holder.value = mtosiHeader;
      fc.setWorker(fullBody, holder);
      fc.parse();
    }catch(Throwable ex) {
      Assert.assertThat(ex, isA((Class)MtosiProcessingFailureException.class));
      Assert.assertThat(ex.getMessage(), is(MtosiErrorConstants.ME_NOT_FOUND));
      Assert.assertThat(((MtosiProcessingFailureException)ex).getException(), is(ExceptionUtils.EXCPT_ENTITY_NOT_FOUND));
      return;
    }
    Assert.fail();
  }

  @Test
  public void  testGetTerminationPointParseIsNotPort() throws Exception{
    try{
      RequestProfile2Type fullBody = new  RequestProfile2Type();
      fullBody.setTpRef(NamingTranslationFactory.createNamingAttributesPTP("ADVA/Network","TestNE",""));
      Holder holder = new Holder();
      holder.value = mtosiHeader;
      fc.setWorker(fullBody, holder);
      when(networkElementHdlr.getNEByName("TestNE")).thenReturn(networkElement);
      when(networkElement.isDiscovered()).thenReturn(true);
      when(networkElement.getNetworkElementType()).thenReturn(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_R7);
      when(networkElement.getMTOSIWorker()).thenReturn(neMtosiOperations);
      when(neMtosiOperations.getNetworkElementTypeForMTOSI()).thenReturn(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_R7);
      fc.parse();
    }catch(Throwable ex) {
      Assert.assertThat(ex, isA((Class)MtosiProcessingFailureException.class));
      Assert.assertThat(ex.getMessage(), is(MtosiErrorConstants.INVALID_PTP_NAME));
      Assert.assertThat(((MtosiProcessingFailureException)ex).getException(), is(ExceptionUtils.EXCPT_INVALID_INPUT));
      return;
    }
    Assert.fail();
  }

  @Test
  public void  testGetTerminationPointParseMissingIsPort() throws Exception{
    try{
      RequestProfile2Type fullBody = new  RequestProfile2Type();
      fullBody.setTpRef(NamingTranslationFactory.createNamingAttributesPTP("ADVA/Network","TestNE","/shelf=1/slot=1/port=12"));
      Holder holder = new Holder();
      holder.value = mtosiHeader;
      fc.setWorker(fullBody, holder);
      when(networkElementHdlr.getNEByName("TestNE")).thenReturn(networkElement);
      when(networkElement.isDiscovered()).thenReturn(true);
      when(networkElement.getNetworkElementType()).thenReturn(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_R7);
      when(networkElement.getMTOSIWorker()).thenReturn(neMtosiOperations);
      when(neMtosiOperations.getNetworkElementTypeForMTOSI()).thenReturn(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP_R7);
      fc.parse();
    }catch(Throwable ex) {
      Assert.fail();
    }

  }

  private static void prepareMtosiCtrlImpl() throws ModuleInitializationException, NoSuchFieldException, IllegalAccessException {
    when(serverCtrl.getWorkMode()).thenReturn(WorkMode.MASTER);
    setMtosiCtrlImplValues(mtosiCtrl, "serverCTRL", serverCtrl);
    setMtosiCtrlImplValues(mtosiCtrl, "configCTRL", configCtrl);
    mtosiCtrl.selfInit();

  }

  private static void setMtosiCtrlImplValues(MtosiCtrlImpl mtosiCtrl, String fieldName, Object value) throws NoSuchFieldException, IllegalAccessException {
    Field privateStringField;
    privateStringField = MtosiCtrlImpl.class.getDeclaredField(fieldName);
    privateStringField.setAccessible(true);
    privateStringField.set(mtosiCtrl, value);
  }

  @SuppressWarnings("deprecation")
  private void mockHandlers() throws NoSuchMDObjectException {
    when(configCtrl.getHandlers()).thenReturn(handlers);
    when(handlers.getNeHdlr()).thenReturn(networkElementHdlr);
    when(handlers.getSubnetHdlr()).thenReturn(subnetHdlrLocal);
    when(handlers.getSubnetHdlr().getTopLevelSubnetID()).thenReturn(10000);
    when(handlers.getSubnetHdlr().getName(10000)).thenReturn("Network");
    when(OSFactory.getMDNm()).thenReturn("Network");

  }




}
