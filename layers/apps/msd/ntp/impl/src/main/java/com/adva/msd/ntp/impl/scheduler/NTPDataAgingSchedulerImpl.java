/*
 *    Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *    Owner: matanb
 */

package com.adva.msd.ntp.impl.scheduler;

import com.adva.msd.ntp.impl.interfaces.NTPDataAgingSchedulerApi;
import com.adva.nlms.common.property.FNMPropertyConstants;
import com.adva.nlms.common.property.FNMPropertyFactory;
import com.adva.nlms.mediation.common.persistence.MDPersistenceManager;
import com.adva.nlms.mediation.common.persistence.MDTransactional;
import jakarta.persistence.EntityManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;
import jakarta.persistence.Query;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Component
public class NTPDataAgingSchedulerImpl implements NTPDataAgingSchedulerApi {

  private static final int MAX_TTL = 5;

  private static final int MIN_TTL = 1;

  private final ScheduledExecutorService scheduler;

  private static final Logger logger = LogManager.getLogger(NTPDataAgingSchedulerImpl.class);

  private static final int TTL = getTTL();

  public NTPDataAgingSchedulerImpl(){
    this.scheduler = Executors.newSingleThreadScheduledExecutor();
  }

  @MDTransactional
  private void handleDataAging() {
    try {
      EntityManager em = MDPersistenceManager.current();
      String strInsertQuery = "INSERT INTO public.cn_ntp_dynamic_client (ne_id, ntp_clock, client_ip, ntp_clock_interface_ip, client_first_request_time, " +
              "client_last_request_time, client_requests_count, ignore) SELECT ne_id, ntp_clock, CONCAT(ne_id, '_', ntp_clock), CONCAT(ne_id, '_', ntp_clock), NOW(), NOW(), SUM(client_requests_count), true " +
              "FROM public.cn_ntp_dynamic_client WHERE client_last_request_time < NOW() - INTERVAL '" + TTL + " days' GROUP BY ne_id,ntp_clock;";
      Query insertQuery = em.createNativeQuery(strInsertQuery);
      insertQuery.executeUpdate();

      String strDeleteFromNtpDynamicClientQuery = "DELETE FROM public.cn_ntp_dynamic_client WHERE client_last_request_time < NOW() - INTERVAL '" + TTL + " days';";
      Query deleteFromNtpDynamicClientQuery = em.createNativeQuery(strDeleteFromNtpDynamicClientQuery);
      deleteFromNtpDynamicClientQuery.executeUpdate();

      String strDeleteFromNtpServerActivityQuery = "DELETE FROM public.cn_ntp_server_activity WHERE time_stamp < NOW() - INTERVAL '" + TTL + " days';";
      Query deleteFromNtpServerActivityQuery = em.createNativeQuery(strDeleteFromNtpServerActivityQuery);
      deleteFromNtpServerActivityQuery.executeUpdate();
    }
    catch (Exception e) {
      logger.error("NTPDataAgingScheduler - Error while handling data aging: {}", e.getMessage());
    }
  }

  @Override
  public void triggerScheduler() {
    scheduler.scheduleAtFixedRate(this::handleDataAging, 0, 1, TimeUnit.DAYS);
  }

  private static int getTTL() {
      int ttl = FNMPropertyFactory.getPropertyAsInt(FNMPropertyConstants.NTP_DATA_TTL_IN_DAYS, FNMPropertyConstants.NTP_DATA_TTL_IN_DAYS_DEFAULT);
      if (ttl < MIN_TTL || ttl > MAX_TTL) {
          logger.warn("NTPDataAgingScheduler - Invalid ntpDataTtlInDays value: {}. Valid range: {}..{}. Using default value: {}",
                  ttl, MIN_TTL, MAX_TTL, FNMPropertyConstants.NTP_DATA_TTL_IN_DAYS_DEFAULT);
          ttl = FNMPropertyConstants.NTP_DATA_TTL_IN_DAYS_DEFAULT;
      }
      return ttl;
  }
}
