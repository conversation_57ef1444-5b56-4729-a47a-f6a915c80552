/*
 *    Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *    Owner: matanb
 */

package com.adva.alarmnbiapi.dto;

import com.adva.fm.api.dto.Disposition;
import com.adva.fm.api.dto.EventResource;
import com.adva.fm.api.dto.Origin;
import com.adva.fm.api.dto.Severity;

import java.util.List;
import java.util.Map;

public class CustomerAlarmDto {
  private String eventMsgType;
  private long eventTime;
  private Map<String, Object> eventLabels;
  private boolean security;
  private String eventName;
  private Disposition disposition;
  private Origin origin;
  private String description;
  private EventResource primaryResource;
  private List<EventResource> secondaryResources;
  private Severity severity;

  public String getEventMsgType() {
    return eventMsgType;
  }

  public void setEventMsgType(String eventMsgType) {
    this.eventMsgType = eventMsgType;
  }

  public long getEventTime() {
    return eventTime;
  }

  public void setEventTime(long eventTime) {
    this.eventTime = eventTime;
  }

  public Map<String, Object> getEventLabels() {
    return eventLabels;
  }

  public void setEventLabels(Map<String, Object> eventLabels) {
    this.eventLabels = eventLabels;
  }

  public boolean isSecurity() {
    return security;
  }

  public void setSecurity(boolean security) {
    this.security = security;
  }

  public Disposition getDisposition() {
    return disposition;
  }

  public void setDisposition(Disposition disposition) {
    this.disposition = disposition;
  }

  public Origin getOrigin() {
    return origin;
  }

  public void setOrigin(Origin origin) {
    this.origin = origin;
  }

  public EventResource getPrimaryResource() {
    return primaryResource;
  }

  public void setPrimaryResource(EventResource primaryResource) {
    this.primaryResource = primaryResource;
  }

  public List<EventResource> getSecondaryResources() {
    return secondaryResources;
  }

  public void setSecondaryResources(List<EventResource> secondaryResources) {
    this.secondaryResources = secondaryResources;
  }

  public Severity getSeverity() {
    return severity;
  }

  public void setSeverity(Severity severity) {
    this.severity = severity;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public String getEventName() {
    return eventName;
  }

  public void setEventName(String eventName) {
    this.eventName = eventName;
  }

  @Override
  public String toString() {
    return "CustomerAlarmDto{" +
            "eventMsgType='" + eventMsgType + '\'' +
            ", eventTime=" + eventTime +
            ", eventLabels=" + eventLabels +
            ", security=" + security +
            ", eventName='" + eventName + '\'' +
            ", disposition=" + disposition +
            ", origin=" + origin +
            ", description='" + description + '\'' +
            ", primaryResource=" + primaryResource +
            ", secondaryResources=" + secondaryResources +
            ", severity=" + severity +
            '}';
  }
}
