plugins {
    id 'java'
}

setupModule(project)

group = 'com.adva.com.adva.alarmnbiapi'
//version = '0.0.1'
sourceCompatibility = 17


dependencies {
    api libs.jackson.annotations
    api modep(mod_fm_api)
    implementation libs.jackson.databind
    implementation modep(mod_fm_api)
    implementation modep(mod_common_definition)
    //implementation group: 'org.springframework.boot', name: 'spring-boot-starter-webflux', version: '3.1.0'
    //implementation group: 'org.springframework.boot', name: 'spring-boot-starter-web', version: '3.1.0'
    //implementation group: 'org.springframework.kafka', name: 'spring-kafka', version: '3.0.6'
    //implementation group: "javax.xml.bind", name: "jaxb-api", version: "2.4.0-b180830.0359"
}
