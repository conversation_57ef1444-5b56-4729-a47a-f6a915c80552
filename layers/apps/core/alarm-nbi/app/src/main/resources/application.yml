#
# Copyright 2023 Adtran Networks SE. All rights reserved.
#
# Owner: GerassimosM

spring:
  output.ansi.enabled: ALWAYS
  # NOTE the following parameters are applicable only for multipart requests
  # json object file size are NOT affected from these parameters
  servlet.multipart.max-file-size: ${WEBSERVER_MAX-FILE-SIZE:20MB}
  servlet.multipart.max-request-size: ${WEBSERVER_MAX_REQUEST_SIZE:21MB}
  kafka.bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9094}

enc:
  server-base: ${ENC_SERVER_BASE:https://localhost:8443}

logging:
  level:
    com.adva.alarmnbiapp: ${LOG_LEVEL:DEBUG}

server:
  port: ${TOMCAT_HTTP_PORT:8109}

management:
  endpoints.web.exposure.include: "info, health, prometheus"
  #  metrics.enable.jvm: false
  metrics.tags:
    instanceHostName: ${HOSTNAME:localhost}
    application: alarm-nbi
    service: alarm-nbi-app

alarm-nbi-app:
  kafka-bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9094}
  kafka-customer-bootstrap-servers: ${KAFKA_CUSTOMER_BOOTSTRAP_SERVERS:localhost:9094}
  kafka-fm-topic: ${KAFKA_FM_TOPIC:v1.cor.fm.eventUpdate}
  kafka-topic-concurrency: ${KAFKA_TOPIC_CONCURRENCY:3}
  use-customer-topic: ${USE_CUSTOMER_TOPIC:false}
  kafka-customer-alarm-topic: ${KAFKA_CUSTOMER_ALARM_TOPIC:v1.cor.alarmnbi.custome.alarms}
  kafka-customer-alarm-topic-replicas: ${KAFKA_CUSTOMER_ALARM_TOPIC_REPLICAS:1}
  kafka-customer-alarm-topic-partitions: ${KAFKA_CUSTOMER_ALARM_TOPIC_PARTITIONS:1}
  kafka-topic-creation-retry-max-attempts: ${KAFKA_TOPIC_CREATION_RETRY_MAX_ATTEMPTS:12}
  kafka-topic-creation-retry-delay-ms: ${KAFKA_TOPIC_CREATION_RETRY_DELAY_MS:5000}
  filter-property-path: ${FILTER_PROPERTY_PATH:src/main/resources/filter.properties}
#  kafka-message-topic: ${KAFKA_MESSAGE_TOPIC:v1.cor.jm.messages}
#  kafka-message-topic-replicas: ${KAFKA_MESSAGE_TOPIC_REPLICAS:1}
#  kafka-message-topic-partitions: ${KAFKA_MESSAGE_TOPIC_PARTITIONS:3}
#  kafka-message-topic-concurrency: ${KAFKA_MESSAGE_TOPIC_CONCURRENCY:3}
#  topic-creation-retry-delay-ms: ${TOPIC_CREATION_RETRY_DELAY_MS:5000}
#  topic-creation-retry-max-attempts: ${TOPIC_CREATION_RETRY_MAX_ATTEMPTS:12}
