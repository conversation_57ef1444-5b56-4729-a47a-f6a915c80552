#!/bin/bash
#
# Copyright 2025 Adtran Networks SE. All rights reserved.
#
# Owner: shiras
#

function get_secrets {
  # Check that curl exist
  if ! command -v curl &> /dev/null
  then
    >&2 echo "The command cannot be executed because curl is not found"
    exit 1
  fi

  if [[ -z "${LOCAL_AUTH_TOKEN}" ]]; then
    if [ ! -f /run/secrets/LOCAL_AUTH_TOKEN ]; then
      if [ ! -f /tmp/tmpt.tmp ]; then
        >&2 echo "The command cannot be executed because the secret token was not found"
        exit 1
      else
        LOCAL_AUTH_TOKEN=`cat /tmp/tmpt.tmp`
      fi
    else
      LOCAL_AUTH_TOKEN=`cat /run/secrets/LOCAL_AUTH_TOKEN`
    fi
  fi
  echo "${LOCAL_AUTH_TOKEN}"
}
