<!--
  ~ Copyright 2025 ADVA Optical Networking SE. All rights reserved.
  ~
  ~ Owner: shiras
  -->

<configuration>

    <property name="LOG_DIR" value="var/log"/>

    <root level="ERROR">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="JOB_MNG_LOG"/>
    </root>

    <logger name="com.adva.alarmnbiapp" level="DEBUG"/>
    <logger name="com.adva.nlms.infrastucture.app.security" level="INFO"/>
    <logger name="com.adva.alarmnbiapp.adapter.in.rest.RequestLoggingConfig$CommonRequestLoggingFilter" level="INFO"
            additivity="false">
        <appender-ref ref="HTTP_REQUESTS_LOG"/>
    </logger>

    <appender name="JOB_MNG_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/job-mng.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${LOG_DIR}/job-mng.%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>5</maxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>10MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread%replace( %mdc{correlationId}){' $', ''}] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="HTTP_REQUESTS_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/http_requests.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>${LOG_DIR}/http_requests.%i.log</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>5</maxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>10MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread%replace( %mdc{correlationId}){' $', ''}] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

</configuration>
