/*
 *    Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *    Owner: matanb
 */
package com.adva.alarmnbiapp.adapter.out.kafka;

import com.adva.alarmnbiapp.config.AlarmNbiConfig;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.NewTopic;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.TopicBuilder;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.retry.annotation.Retryable;
import org.springframework.retry.support.RetrySynchronizationManager;
import org.springframework.retry.support.RetryTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


@ConditionalOnExpression("!${alarm-nbi-app.use-customer-topic:false}")
@Configuration
@EnableRetry
public class KafkaTopicConfig {

    private final AlarmNbiConfig alarmNbiConfig;
    private static final Logger logger = LoggerFactory.getLogger(KafkaTopicConfig.class);

    public KafkaTopicConfig(AlarmNbiConfig alarmNbiConfig) {
        this.alarmNbiConfig = alarmNbiConfig;
    }

    @Bean
    public KafkaAdmin kafkaAdmin() {
        Map<String, Object> configs = new HashMap<>();
        configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, alarmNbiConfig.getKafkaBootstrapServers());
        return new KafkaAdmin(configs);
    }


    /**
     * Define the RetryTemplate bean.
     * The max attempts and the back-off period for the retries will be
     * configured on each @Retryable annotation - default values are used otherwise.
     * @return the retry template
     */
    @Bean
    public RetryTemplate retryTemplate() {
        return new RetryTemplate();
    }

    @Bean
    @Retryable(retryFor = Exception.class,
            maxAttemptsExpression = "${alarm-nbi-app.kafka-topic-creation-retry-max-attempts}",
            backoff = @Backoff(delayExpression = "${alarm-nbi-app.kafka-topic-creation-retry-delay-ms}"))
    public KafkaAdmin createTopics() {
        logger.info("Attempt #{} to create topics", Objects.requireNonNull(RetrySynchronizationManager.getContext()).getRetryCount()+1);
        kafkaAdmin().initialize();
        KafkaAdmin admin = kafkaAdmin();
        admin.createOrModifyTopics(
                customerAlarmsTopic()
        );
        logger.info("Topics successfully created");
        return admin;
    }

    @Bean
    public NewTopic customerAlarmsTopic() {
        return TopicBuilder.name(alarmNbiConfig.getKafkaFmTopic())
                .partitions(alarmNbiConfig.getKafkaCustomerAlarmTopicPartitions())
                .replicas(alarmNbiConfig.getKafkaCustomerAlarmTopicReplicas())
                .build();
    }
}
