/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: zivy
 */

package com.adva.alarmnbiapp.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@EnableConfigurationProperties
@ConfigurationProperties(prefix = "alarm-nbi-app")
public class AlarmNbiConfig {
    private String kafkaBootstrapServers;
    private String kafkaFmTopic;
    private int kafkaTopicConcurrency;
    private String filterPropertyPath;
    private String mappingPath;
    private boolean useCustomerTopic;
    private String kafkaCustomerAlarmTopic;
    private int kafkaTopicCreationRetryMaxAttempts;
    private long kafkaTopicCreationRetryDelayMs;
    private int kafkaCustomerAlarmTopicReplicas;
    private int kafkaCustomerAlarmTopicPartitions;
    private String kafkaCustomerBootstrapServers;

    public String getKafkaBootstrapServers() {
        return kafkaBootstrapServers;
    }

    public void setKafkaBootstrapServers(String kafkaBootstrapServers) {
        this.kafkaBootstrapServers = kafkaBootstrapServers;
    }

    public String getKafkaFmTopic() {
        return kafkaFmTopic;
    }

    public void setKafkaFmTopic(String kafkaFmTopic) {
        this.kafkaFmTopic = kafkaFmTopic;
    }

    public int getKafkaTopicConcurrency() {
        return kafkaTopicConcurrency;
    }

    public void setKafkaTopicConcurrency(int kafkaTopicConcurrency) {
        this.kafkaTopicConcurrency = kafkaTopicConcurrency;
    }

    public String getFilterPropertyPath() {
        return filterPropertyPath;
    }

    public void setFilterPropertyPath(String filterPropertyPath) {
        this.filterPropertyPath = filterPropertyPath;
    }

    public String getMappingPath() {
        return mappingPath;
    }

    public void setMappingPath(String mappingPath) {
        this.mappingPath = mappingPath;
    }

    public boolean isUseCustomerTopic() {
        return useCustomerTopic;
    }

    public void setUseCustomerTopic(boolean useCustomerTopic) {
        this.useCustomerTopic = useCustomerTopic;
    }

    public String getKafkaCustomerAlarmTopic() {
        return kafkaCustomerAlarmTopic;
    }

    public void setKafkaCustomerAlarmTopic(String kafkaCustomerAlarmTopic) {
        this.kafkaCustomerAlarmTopic = kafkaCustomerAlarmTopic;
    }

    public int getKafkaTopicCreationRetryMaxAttempts() {
        return kafkaTopicCreationRetryMaxAttempts;
    }

    public void setKafkaTopicCreationRetryMaxAttempts(int kafkaTopicCreationRetryMaxAttempts) {
        this.kafkaTopicCreationRetryMaxAttempts = kafkaTopicCreationRetryMaxAttempts;
    }

    public long getKafkaTopicCreationRetryDelayMs() {
        return kafkaTopicCreationRetryDelayMs;
    }

    public void setKafkaTopicCreationRetryDelayMs(long kafkaTopicCreationRetryDelayMs) {
        this.kafkaTopicCreationRetryDelayMs = kafkaTopicCreationRetryDelayMs;
    }

    public int getKafkaCustomerAlarmTopicReplicas() {
        return kafkaCustomerAlarmTopicReplicas;
    }

    public void setKafkaCustomerAlarmTopicReplicas(int kafkaCustomerAlarmTopicReplicas) {
        this.kafkaCustomerAlarmTopicReplicas = kafkaCustomerAlarmTopicReplicas;
    }

    public int getKafkaCustomerAlarmTopicPartitions() {
        return kafkaCustomerAlarmTopicPartitions;
    }

    public void setKafkaCustomerAlarmTopicPartitions(int kafkaCustomerAlarmTopicPartitions) {
        this.kafkaCustomerAlarmTopicPartitions = kafkaCustomerAlarmTopicPartitions;
    }

    public String getKafkaCustomerBootstrapServers() {
        return kafkaCustomerBootstrapServers;
    }

    public void setKafkaCustomerBootstrapServers(String kafkaCustomerBootstrapServers) {
        this.kafkaCustomerBootstrapServers = kafkaCustomerBootstrapServers;
    }
}
