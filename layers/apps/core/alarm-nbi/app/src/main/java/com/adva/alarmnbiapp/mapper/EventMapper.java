/*
 *    Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *    Owner: matanb
 */

package com.adva.alarmnbiapp.mapper;

import com.adva.alarmnbiapi.dto.CustomerAlarmDto;
import com.adva.fm.api.dto.AppNetworkMgmtEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface EventMapper {

  @Mapping(source = "eventMsgType", target = "eventMsgType")
  @Mapping(source = "eventTime", target = "eventTime")
  @Mapping(source = "eventLabels", target = "eventLabels")
  @Mapping(source = "security", target = "security")
  @Mapping(source = "eventName", target = "eventName")
  @Mapping(source = "disposition", target = "disposition")
  @Mapping(source = "origin", target = "origin")
  @Mapping(source = "description", target = "description")
  @Mapping(source = "primaryResource", target = "primaryResource")
  @Mapping(source = "secondaryResources", target = "secondaryResources")
  @Mapping(source = "severity", target = "severity")
  CustomerAlarmDto toCustomerAlarmDto(AppNetworkMgmtEvent event);
}
