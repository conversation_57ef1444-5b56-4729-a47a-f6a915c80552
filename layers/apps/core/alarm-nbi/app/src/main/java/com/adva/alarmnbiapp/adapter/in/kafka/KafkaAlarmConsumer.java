/*
 *    Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *    Owner: matanb
 */

package com.adva.alarmnbiapp.adapter.in.kafka;

import com.adva.alarmnbiapi.dto.exception.FailedToConsumeFMEvent;
import com.adva.alarmnbiapp.interfaces.in.MessageHandlerApi;
import com.adva.fm.api.dto.AppNetworkMgmtEvent;
import io.github.springwolf.bindings.kafka.annotations.KafkaAsyncOperationBinding;
import io.github.springwolf.core.asyncapi.annotations.AsyncListener;
import io.github.springwolf.core.asyncapi.annotations.AsyncOperation;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class KafkaAlarmConsumer {

    private static final Logger logger = LoggerFactory.getLogger(KafkaAlarmConsumer.class);

    private final MessageHandlerApi messageHandlerApi;

    public KafkaAlarmConsumer(MessageHandlerApi messageHandlerApi) {
        this.messageHandlerApi = messageHandlerApi;
    }

    @KafkaListener(topics = "${alarm-nbi-app.kafka-fm-topic}",
            groupId = "alarm-nbi-fm-group",
            containerFactory = "kafkaFMListenerContainerFactory")
    @AsyncListener(operation = @AsyncOperation(
            channelName = "${alarm-nbi-app.kafka-fm-topic}",
            description = "Alarm NBI FM event consumer",
            payloadType = AppNetworkMgmtEvent.class
    ))
    @KafkaAsyncOperationBinding(groupId = "alarm-nbi-fm-group")
    public void listen(List<ConsumerRecord<String, AppNetworkMgmtEvent>> consumerRecords) {
        logger.debug("batch size: {} ", consumerRecords.size());
        List<AppNetworkMgmtEvent> appNetworkMgmtEventList = consumerRecords.stream().map(ConsumerRecord::value).toList();
        for( AppNetworkMgmtEvent appNetworkMgmtEvent : appNetworkMgmtEventList) {
            try {
                messageHandlerApi.processEvent(appNetworkMgmtEvent);
            } catch (Exception e) {
                throw new FailedToConsumeFMEvent("Failed to consume FM Event", e);
            }
        }
    }
}
