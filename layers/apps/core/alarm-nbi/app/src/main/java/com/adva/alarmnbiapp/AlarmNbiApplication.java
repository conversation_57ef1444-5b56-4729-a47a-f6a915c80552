/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: gerassimosm
 */

package com.adva.alarmnbiapp;

import com.adva.nlms.infrastucture.security.api.AppBeans;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

import jakarta.annotation.PostConstruct;
import java.util.TimeZone;


@SpringBootApplication(scanBasePackages = {"com.adva.alarmnbiapp", AppBeans.APP_BEANS_SCAN_PACKAGE})
@EnableScheduling
public class AlarmNbiApplication {

  @PostConstruct
  public void init(){
    // Setting Spring Boot SetTimeZone
    TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
  }

  public static void main(String[] args) {
    SpringApplication.run(AlarmNbiApplication.class, args);
  }

}
