/*
 *    Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *    Owner: matanb
 */
package com.adva.alarmnbiapp.adapter.in.kafka;

import com.adva.fm.api.dto.AppNetworkMgmtEvent;
import com.adva.alarmnbiapp.config.AlarmNbiConfig;
import com.adva.nlms.mediation.infrastructure.notification.tracing.api.in.TraceDefinitions;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.DefaultErrorHandler;
import org.springframework.kafka.support.ExponentialBackOffWithMaxRetries;
import org.springframework.kafka.support.serializer.JsonDeserializer;

import java.util.HashMap;
import java.util.Map;


@EnableKafka
@Configuration
public class KafkaConsumerConfig {

    private final AlarmNbiConfig alarmNbiConfig;

    public KafkaConsumerConfig(AlarmNbiConfig alarmNbiConfig) {
        this.alarmNbiConfig = alarmNbiConfig;
    }

    @Bean
    public DefaultErrorHandler errorHandler() {
        ExponentialBackOffWithMaxRetries backOff = new ExponentialBackOffWithMaxRetries(5); // Max 5 retries
        backOff.setInitialInterval(1000L);  // Initial interval of 1 second
        backOff.setMultiplier(2);         // Multiplier of 2
        backOff.setMaxInterval(15000L);     // Maximum interval of 15 seconds
        return new DefaultErrorHandler(backOff);  // Construct an instance with the default recoverer which simply logs the record after the backOff returns STOP for a topic/partition/offset.
    }

    // =================================================
    // kafka Consumer for AppNetworkMgmtEvent messages
    // =================================================

    @Bean
    public ConsumerFactory<String, AppNetworkMgmtEvent> fmConsumerFactory() {
        Map<String, Object> properties = new HashMap<>();
        properties.put( ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, alarmNbiConfig.getKafkaBootstrapServers());
        properties.put( ConsumerConfig.GROUP_ID_CONFIG, "alarm-nbi-fm-group");
        properties.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        properties.put( ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        properties.put( ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, JsonDeserializer.class);
        properties.put(ConsumerConfig.INTERCEPTOR_CLASSES_CONFIG, TraceDefinitions.CONSUMER_INTERCEPTOR);

        return new DefaultKafkaConsumerFactory<>(
                properties,
                new StringDeserializer(),
                new JsonDeserializer<>(AppNetworkMgmtEvent.class)
        );
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, AppNetworkMgmtEvent> kafkaFMListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, AppNetworkMgmtEvent> factory
                = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(fmConsumerFactory());
        factory.setConcurrency(alarmNbiConfig.getKafkaTopicConcurrency());
        factory.setCommonErrorHandler(errorHandler()); // set error handler with ExponentialBackOffWithMaxRetries
        factory.setBatchListener(true);
        return factory;
    }
}
