/*
 *    Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *    Owner: matanb
 */

package com.adva.alarmnbiapp.filter;

import com.adva.fm.api.dto.AppNetworkMgmtEvent;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Component
public class AlarmFilter {
  private final Map<FilterAttribute, Set<String>> filterMap;

  public AlarmFilter() {
    this.filterMap = new EnumMap<>(FilterAttribute.class);
  }

  public void addFilterAttribute(FilterAttribute attribute, String values) {
    if (values != null && !values.isEmpty()) {
      String[] splitValues = values.split(",");
      Set<String> valueSet = new HashSet<>();
      for (String val : splitValues) {
        valueSet.add(val.trim());
      }
      filterMap.put(attribute, valueSet);
    }
  }

  public boolean toFilter(AppNetworkMgmtEvent event){
    Set<String> severitySet = filterMap.get(FilterAttribute.SEVERITY);
    return severitySet != null && !severitySet.contains(event.getSeverity().name()); // Filter out the event if severity does not match
  }
}
