/*
 *    Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *    Owner: matanb
 */

package com.adva.alarmnbiapp.adapter.out.kafka;

import com.adva.alarmnbiapi.dto.CustomerAlarmDto;
import com.adva.alarmnbiapp.config.AlarmNbiConfig;
import com.adva.nlms.mediation.infrastructure.notification.tracing.api.in.TraceDefinitions;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.serializer.JsonSerializer;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class KafkaProducerConfig {
  private final AlarmNbiConfig config;

  public KafkaProducerConfig(AlarmNbiConfig config) {
    this.config = config;
  }

  @Bean
  public ProducerFactory<String, CustomerAlarmDto> customerAlarmProducerFactory() {
    Map<String, Object> configProps = new HashMap<>();
    configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, config.getKafkaCustomerBootstrapServers());
    configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
    configProps.put(ProducerConfig.INTERCEPTOR_CLASSES_CONFIG, TraceDefinitions.PRODUCER_INTERCEPTOR);
    configProps.put(ProducerConfig.LINGER_MS_CONFIG, "20");
    configProps.put(ProducerConfig.BATCH_SIZE_CONFIG, Integer.toString(32*1024)); // 32 KB batch size
    return new DefaultKafkaProducerFactory<>(configProps);
  }

  @Bean
  public KafkaTemplate<String, CustomerAlarmDto> customerAlarmKafkaTemplate() {
      return new KafkaTemplate<>(customerAlarmProducerFactory());
  }
}
