/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: gerassimosm
 */
package com.adva.alarmnbiapp.service;

import com.adva.alarmnbiapp.config.AlarmNbiConfig;
import com.adva.alarmnbiapp.filter.AlarmFilter;
import com.adva.alarmnbiapp.filter.FilterAttribute;
import com.adva.alarmnbiapp.interfaces.in.MessageHandlerApi;
import com.adva.alarmnbiapp.interfaces.out.PublisherApi;
import com.adva.alarmnbiapp.mapper.EventMapper;
import com.adva.fm.api.dto.AppNetworkMgmtEvent;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Properties;

@Service
public class AlarmNbiHandler implements MessageHandlerApi {
  private final Logger logger = LoggerFactory.getLogger(this.getClass().getName());
  private final AlarmNbiConfig config;
  private final AlarmFilter filter;
  private final EventMapper mapper;
  private final PublisherApi producer;

  public AlarmNbiHandler(AlarmNbiConfig config, AlarmFilter filter, EventMapper mapper, PublisherApi producer) {
    this.config = config;
    this.filter = filter;
    this.mapper = mapper;
    this.producer = producer;
  }

  @PostConstruct
  public void loadConfig() {
    try (BufferedReader reader = Files.newBufferedReader(Paths.get(config.getFilterPropertyPath()))) {
      Properties props = new Properties();
      props.load(reader);
      for(FilterAttribute attr : FilterAttribute.values()){
        filter.addFilterAttribute(attr, props.getProperty(attr.getName(), ""));
      }
    }
    catch (IOException e){
        logger.error("Failed to load filter properties from {}", config.getFilterPropertyPath(), e);
    }

  }

  @Override
  public void processEvent(AppNetworkMgmtEvent event) {
    if(!filter.toFilter(event)){
      producer.send(mapper.toCustomerAlarmDto(event));
    }
  }
}
