/*
 *    Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *    Owner: matanb
 */

package com.adva.alarmnbiapp.adapter.out.kafka;

import com.adva.alarmnbiapi.dto.CustomerAlarmDto;
import com.adva.alarmnbiapp.config.AlarmNbiConfig;
import com.adva.alarmnbiapp.interfaces.out.PublisherApi;
import io.github.springwolf.core.asyncapi.annotations.AsyncOperation;
import io.github.springwolf.core.asyncapi.annotations.AsyncPublisher;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Component
public class KafkaCustomerAlarmProducer implements PublisherApi {
  private final KafkaTemplate<String, CustomerAlarmDto> kafkaTemplate;
  private final AlarmNbiConfig config;

  public KafkaCustomerAlarmProducer(KafkaTemplate<String, CustomerAlarmDto> kafkaTemplate,
                                      AlarmNbiConfig config) {
      this.kafkaTemplate = kafkaTemplate;
      this.config = config;
  }

  @AsyncPublisher(operation = @AsyncOperation(
          channelName = "${alarm-nbi-app.kafka-customer-alarm-topic}",
          description = "Customer Alarm producer"
  ))
  @Override
  public void send(CustomerAlarmDto customerAlarmDto) {
      kafkaTemplate.send(config.getKafkaCustomerAlarmTopic(), customerAlarmDto);
  }
}
