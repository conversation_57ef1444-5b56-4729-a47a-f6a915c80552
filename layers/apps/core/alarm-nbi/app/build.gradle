buildscript {
    repositories {
        maven {
            url "https://plugins.gradle.org/m2/"
        }
    }
    dependencies {
        classpath("com.palantir.gradle.docker:gradle-docker:0.37.0")
    }
}

plugins {
    id 'java'
    id 'org.springframework.boot'
}

apply plugin: 'com.palantir.docker'

setupSpringBootAppModule(project)

def NMS_TAG = project.getProperties().get('NMS_TAG') ?: "latest"
def artifactName='alarm-nbi-app'
def isDefaultBranch = System.getenv('IS_DEFAULT_BRANCH') ?: 'true'

group = 'com.adva.alarmnbi'
version = "${NMS_TAG}"
def encAppsVer = Version+"-"+BuildNumber

docker {
    dependsOn build
    name "enc-${bootJar.archiveBaseName.get()}:${NMS_TAG}"
    tag 'tag1', "gdn-artifactory.rd.advaoptical.com:9443/enc/core/alarm-nbi/${artifactName}:${NMS_TAG}"
    tag 'tag2', "gdn-artifactory.rd.advaoptical.com:9443/enc/core/alarm-nbi/${artifactName}:${encAppsVer}"
    files bootJar.archiveFile.get()
    buildArgs(['JAR_FILE': "${bootJar.archiveFileName.get()}", 'NMS_TAG': "${NMS_TAG}"])
}

// Add missing dependency relationship needed for gradle 8 and docker plugin
tasks['dockerPrepare'].dependsOn('bootJar')

dependencies {
    implementation platform(springboot.bom)
    implementation springboot.starter.webmvc.ui
    implementation springboot.starter.web
    implementation springboot.starter.data.rest
    implementation springboot.starter.actuator
    implementation springboot.starter.validation
    implementation springboot.starter.security
    implementation libs.spring.kafka
    implementation libs.springwolf.annotations
    implementation libs.mapstruct
    implementation libs.aspectjweaver
    api modep(mod_alarm_nbi_api)
//    implementation modep(mod_fm_api)
//    implementation modep(mod_common_definition)
//    implementation modep( mod_rest_common_lib )
    implementation modep(mod_app_security_api)
//    implementation modep(mod_app_security_impl)
    implementation modep(mod_notification_tracing_api)
//    implementation modep(mod_rest_tracing)
    annotationProcessor libs.mapstruct.processor

//    runtimeOnly libs.junixsocket.common
//    runtimeOnly libs.junixsocket.common.native
    runtimeOnly modep(mod_notification_tracing_impl)

//    testImplementation springboot.starter.test
//    testImplementation springboot.spring.kafka.test
//    testImplementation springboot.h2

}

// Build and deploy Docker image with SBOM
tasks.register('dockerBuildWithSBOM') {
    description = 'Build and deploy Docker application image with SBOM'
    dependsOn  'bootJar' // Ensure bootJar is created first

    doLast {
        // Define the files to copy into the Docker image
        def filesToCopy = [
                file("${projectDir}/src/main/resources/scripts/funcs"),
                file("${projectDir}/src/main/resources/scripts/healthcheck")
        ]

        // Define the Docker build context directory
        def dockerContextDir = file("${buildDir}/dockerContext")

        // Copy the required files into the Docker build context
        filesToCopy.each { sourceFile ->
            if (sourceFile.exists()) {
                copy {
                    from sourceFile
                    into dockerContextDir
                }
            } else {
                println "File not found or invalid: ${sourceFile}"
            }
        }

        // Copy the bootJar file into the Docker build context
        def bootJarFile = bootJar.archiveFile.get()
        copy {
            from bootJarFile
            into dockerContextDir
        }

        // Copy the Dockerfile into the Docker build context
        def dockerFile = file("${projectDir}/Dockerfile")
        if (dockerFile.exists()) {
            copy {
                from dockerFile
                into dockerContextDir
            }
        } else {
            println "Dockerfile not found at ${dockerFile}"
        }

        new ByteArrayOutputStream().withStream { os ->
            // Determine the Docker arguments based on the branch type
            def dockerArgs
            if (isDefaultBranch == "true") {
                println "##teamcity[setParameter name='env.DOCKER_IMAGE_URL' value='gdn-artifactory.rd.advaoptical.com:9443/enc/core/alarm-nbi/${artifactName}:${encAppsVer}']"
                dockerArgs = [
                        "buildx", "build", "--no-cache",
                        '-t', "gdn-artifactory.rd.advaoptical.com:9443/enc/core/alarm-nbi/${artifactName}:${NMS_TAG}",
                        '-t', "gdn-artifactory.rd.advaoptical.com:9443/enc/core/alarm-nbi/${artifactName}:${encAppsVer}",
                        "-f", "Dockerfile",
                        "--build-arg", "JAR_FILE=${bootJar.archiveFileName.get()}",
                        "--build-arg", "NMS_TAG=${NMS_TAG}",
                        "--sbom=true",
                        "--builder=buildkit-container",
                        "--push",
                        "."
                ]
            } else {
                dockerArgs = [
                        "buildx", "build", "--no-cache",
                        '-t', "gdn-artifactory.rd.advaoptical.com:9443/enc/core/alarm-nbi/${artifactName}:${NMS_TAG}",
                        '-t', "gdn-artifactory.rd.advaoptical.com:9443/enc/core/alarm-nbi/${artifactName}:${encAppsVer}",
                        '-t', "${bootJar.archiveBaseName.get()}:${NMS_TAG}",
                        '-t', "${bootJar.archiveBaseName.get()}:latest",
                        "-f", "Dockerfile",
                        "--build-arg", "JAR_FILE=${bootJar.archiveFileName.get()}",
                        "--build-arg", "NMS_TAG=${NMS_TAG}",
                        "--load",
                        "--builder=buildkit-container",
                        "."
                ]
            }

            // Execute the Docker build command
            def result = exec {
                executable = 'docker'
                args = dockerArgs
                workingDir = dockerContextDir
                standardOutput = os
                errorOutput = os
                ignoreExitValue = true
            }
            def outputAsString = os.toString()


            // Print the output of the Docker build to the console for debugging
            println "Docker build output:\n${outputAsString}"

        }
    }
}