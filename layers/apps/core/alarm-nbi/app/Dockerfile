# syntax=gdn-artifactory.rd.advaoptical.com:9443/bmgmt-tools/dockerfile:1.5.2
ARG BUILDKIT_SBOM_SCAN_CONTEXT=true
ARG BUILDKIT_SBOM_SCAN_STAGE=true

FROM gdn-artifactory.rd.advaoptical.com:9443/enc/java-base/eclipse-temurin:17.0.13_11-jdk-focal-17.1.1-B17038
ARG JAR_FILE
# e.g NMS_TAG = 11.1.1-B4708.tar
ARG NMS_TAG

# Arbitrary labels applied to the image project
LABEL fsp_nm.build="${NMS_TAG}"
LABEL adva.project="core"
LABEL core.module="alarm-nbi-app"
LABEL jar.file="${JAR_FILE}"

# working directory (user home directory /app) is inherited from the base image
COPY --chown=${uid}:${gid} ${JAR_FILE} app.jar
RUN mkdir -p /app/var/log /app/var/ts /app/scripts && \
    chown -R ${uid}:${gid} /app/var/log /app/var/ts /app/scripts

# Copy healthcheck script
COPY --chown=${uid}:${gid} funcs healthcheck ./scripts/

ENV PATH="$PATH:/app/scripts/"

# Healthcheck configuration
HEALTHCHECK --interval=20s --timeout=10s --start-period=60s --retries=3 CMD ["healthcheck"]

#ENTRYPOINT is inherited from base image