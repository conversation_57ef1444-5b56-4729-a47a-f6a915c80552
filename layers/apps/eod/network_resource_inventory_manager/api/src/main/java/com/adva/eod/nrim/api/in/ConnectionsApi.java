/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON> <EMAIL>
 */

package com.adva.eod.nrim.api.in;

import com.adva.eod.nrim.api.dto.ConnectionDto;
import com.adva.eod.nrim.api.dto.ConnectionFilters;
import com.adva.eod.nrim.api.dto.EntityDto;
import com.adva.eod.nrim.api.dto.PotentialConnectionDto;
import com.adva.eod.nrim.api.dto.RouteDto;
import com.adva.eod.nrim.api.dto.SwitchDto;
import com.adva.eod.nrim.api.dto.TopConnectionUpdateDto;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.opticalparameters.api.enums.ProtectionType;
import com.adva.nlms.opticalparameters.api.enums.Role;

import java.util.List;
import java.util.Map;
import java.util.UUID;

public interface ConnectionsApi {

  void updateTopConnection(UUID id, TopConnectionUpdateDto dto, boolean cpEnabled, String userLabel);

  void setTargetAdminState(UUID connectionId, AdminState targetAdminState);

  List<ConnectionDto> getConnections(ConnectionFilters connectionFilters, Integer pageSize, Integer pageOffset, List<String> sort);

  ConnectionDto getConnection(UUID connectionId);

  String getLayerProtocolQualifierOfAConnection(UUID connectionId);

  ConnectionDto getConnectionForEndpoints(List<String> endpoints);

  Map<Role, List<EntityDto>> getEntitiesForConnection(UUID connectionId);

  PotentialConnectionDto getPotentialConnection(String aEndSip, String zEndSip, String layerProtocolQualifier, String payloadQualifier, ProtectionType protectionType);

  List<SwitchDto> getSwitchesForConnection(UUID connectionID); // SwitchDto is a subdomain of ConnectionDto

  List<RouteDto> getRoutesForConnection(UUID connectionId);  // RouteDto is a subdomain of ConnectionDto
}
