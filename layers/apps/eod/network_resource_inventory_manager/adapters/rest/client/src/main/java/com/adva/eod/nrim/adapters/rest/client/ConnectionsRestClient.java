/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON> <EMAIL>
 */

package com.adva.eod.nrim.adapters.rest.client;

import com.adva.eod.nrim.api.dto.*;
import com.adva.eod.nrim.api.in.ConnectionsApi;
import com.adva.nlms.opticalparameters.api.enums.AdminState;

import com.adva.nlms.opticalparameters.api.enums.ProtectionType;
import com.adva.nlms.opticalparameters.api.enums.Role;
import org.apache.commons.lang3.NotImplementedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.RestClientException;
import org.springframework.web.util.UriBuilder;

import java.net.URI;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicReference;

class ConnectionsRestClient implements ConnectionsApi {
  private static final Logger LOGGER = LoggerFactory.getLogger(ConnectionsRestClient.class);
  private final NrimRestClient client;
  private static final String CONNECTIONS = "connections";

  ConnectionsRestClient(NrimRestClient client) {
    this.client = client;
  }

  @Override
  public List<ConnectionDto> getConnections(ConnectionFilters connectionFilters, Integer pageSize, Integer pageOffset, List<String> sort) {
    RestClient.RequestHeadersSpec<?> uri = client.getRestClient().get()
            .uri(uriBuilder -> generateUriForGetConnections(uriBuilder, connectionFilters, pageSize, pageOffset, sort));

    ConnectionDto[] connections = RestClientUtils.executeRequest(ConnectionDto[].class, uri, LOGGER, "Could not get connections");
    return connections != null ? Arrays.stream(connections).toList() : Collections.emptyList();
  }

  private URI generateUriForGetConnections(UriBuilder uriBuilder, ConnectionFilters connectionFilters, Integer pageSize, Integer pageOffset, List<String> sort) {
    uriBuilder.path(client.getNRIMPath() + "/" + CONNECTIONS)
            .queryParam("topConnections", connectionFilters.topConnections());

    RestClientUtils.addParameterToUriBuilder(uriBuilder, "lifecycle", connectionFilters.lifecycle());
    RestClientUtils.addParameterToUriBuilder(uriBuilder, "layerProtocolQualifier", connectionFilters.layerProtocol());
    RestClientUtils.addParameterToUriBuilder(uriBuilder, "payloadQualifier", connectionFilters.payload());
    RestClientUtils.addParametersToUriBuilder(uriBuilder, "endingNEs", connectionFilters.endingNEs());
    RestClientUtils.addParameterToUriBuilder(uriBuilder, "protectionType", connectionFilters.protectionType());
    RestClientUtils.addParameterToUriBuilder(uriBuilder, "freeTextFilter", connectionFilters.freeTextFilter());
    RestClientUtils.addParameterToUriBuilder(uriBuilder, "page", pageOffset);
    RestClientUtils.addParameterToUriBuilder(uriBuilder, "size", pageSize);
    RestClientUtils.addParametersToUriBuilder(uriBuilder, "sort", sort);

    URI uri;
    if (connectionFilters.endingPointsStandard() != null) {
      connectionFilters.endingPointsStandard().forEach(ep -> uriBuilder.queryParam("endingPointsStandard", "{endingPointsStandard}"));
      uri = uriBuilder.build(connectionFilters.endingPointsStandard().toArray());

    } else {
      uri = uriBuilder.build();
    }

    return uri;
  }

  @Override
  public ConnectionDto getConnection(UUID connectionId) {
    RestClient.RequestHeadersSpec<?> uri = client.getRestClient().get()
            .uri(uriBuilder -> uriBuilder
                    .path(client.getNRIMPath() + "/" + CONNECTIONS + "/" + connectionId.toString())
                    .build());

    return RestClientUtils.executeRequest(ConnectionDto.class, uri, LOGGER, "Error while trying to get connection with id: {}", connectionId);
  }

  @Override
  public String getLayerProtocolQualifierOfAConnection(UUID connectionId) {
    RestClient.RequestHeadersSpec<?> uri = client.getRestClient().get()
      .uri(uriBuilder -> uriBuilder
        .path(client.getNRIMPath() + "/" + CONNECTIONS + "/" + connectionId.toString() + "/layer-protocol-qualifier")
        .build());

    return RestClientUtils.executeRequest(String.class, uri, LOGGER, "Error while trying to get connection with id: {}", connectionId);
  }


  @Override
  public void updateTopConnection(UUID id, TopConnectionUpdateDto dto, boolean cpEnabled, String userLabel) {
    throw new IllegalStateException("This method should not be called");
  }

  @Override
  public void setTargetAdminState(UUID connectionId, AdminState targetAdminState) {
    RestClient.RequestHeadersSpec<?> uri = client.getRestClient().post()
            .uri(uriBuilder -> uriBuilder
                    .path(client.getNRIMPath() + "/" + CONNECTIONS + "/" + connectionId.toString()
                            + "/target-admin-state/" + targetAdminState)
                    .build());

    RestClientUtils.executeRequest(Void.class, uri, LOGGER,
            "Error while trying to set target admin state {} for connection: {}", targetAdminState, connectionId);
  }

  @Override
  public ConnectionDto getConnectionForEndpoints(List<String> endpoints) {
    RestClient.RequestHeadersSpec<?> uri = client.getRestClient().get()
            .uri(uriBuilder -> {
              uriBuilder.path(client.getNRIMPath() + "/connections/endpoints");
              endpoints.forEach(cepNrl -> uriBuilder.queryParam("cepNrl", "{cepNrl}"));
              return uriBuilder.build(endpoints.toArray());
            });

    return RestClientUtils.executeRequest(ConnectionDto.class, uri, LOGGER,
            "Error while trying to get connection for endpoints: {}", endpoints);
  }

  @Override
  public Map<Role, List<EntityDto>> getEntitiesForConnection(UUID connectionId) {
    AtomicReference<Map<Role, List<EntityDto>>> body = new AtomicReference<>(Collections.emptyMap());
    ParameterizedTypeReference<Map<Role, List<EntityDto>>> responseType = new ParameterizedTypeReference<>() {
    };

    try {
      client.getRestClient().get().uri(uriBuilder -> uriBuilder
                      .path(client.getNRIMPath() + "/connections/" + connectionId.toString() + "/entities")
                      .build())
              .exchange((request, response) -> {
                body.set(response.bodyTo(responseType));
                LOGGER.warn("Connection Entities for connection id '{}': {}", connectionId, response);
                return body;
              });

    } catch (RestClientException e) {
      LOGGER.warn("Error while trying to get entities for connection id '{}': {}", connectionId, e);
      body.set(Collections.emptyMap());
    }

    return body.get();
  }

  @Override
  public PotentialConnectionDto getPotentialConnection(String aEndSip, String zEndSip, String layerProtocolQualifier, String payloadQualifier, ProtectionType protectionType) {
    RestClient.RequestHeadersSpec<?> uri = client.getRestClient().get()
      .uri(uriBuilder -> uriBuilder
        .path(client.getNRIMPath() + "/potential-connections")
        .queryParam("aEndSip", aEndSip)
        .queryParam("zEndSip", zEndSip)
        .queryParam("layerProtocolQualifier", layerProtocolQualifier)
        .queryParam("payloadQualifier", payloadQualifier)
        .queryParam("protectionType", protectionType.name())
        .build());

    return RestClientUtils.executeRequest(PotentialConnectionDto.class, uri, LOGGER,
           "Error while trying to get potential connection for: '{}', '{}', '{}', '{}', '{}'",
                aEndSip, zEndSip, layerProtocolQualifier, payloadQualifier, protectionType);
  }

  @Override
  public List<SwitchDto> getSwitchesForConnection(UUID connectionId) {
    throw new NotImplementedException("This method is not yet required in REST");
  }

  @Override
  public List<RouteDto> getRoutesForConnection(UUID connectionId) {
    throw new NotImplementedException("This method is not yet required in REST");
  }
}
