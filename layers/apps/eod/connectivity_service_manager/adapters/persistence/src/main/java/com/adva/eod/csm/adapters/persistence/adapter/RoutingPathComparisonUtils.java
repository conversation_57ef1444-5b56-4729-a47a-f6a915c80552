/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: abaso
 */

package com.adva.eod.csm.adapters.persistence.adapter;

import com.adva.eod.csm.adapters.persistence.entity.RoutingPathDBImpl;
import com.adva.eod.csm.connectivityservice.entity.RoutingPath;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class RoutingPathComparisonUtils {

  public boolean isRoutingPathNotEqual(List<RoutingPath> routingPaths, List<RoutingPathDBImpl> routingPathDBS) {
    int routingPathsSize = routingPaths.size();

    if (routingPathsSize != routingPathDBS.size()) {
      return true;
    }

    boolean pathsEqual = true;

    for (int i = 0; i < routingPathsSize; i++) {
      if (!routingPathElementsEqual(routingPaths.get(i), routingPathDBS.get(i))) {
        pathsEqual = false;
        break;
      }
    }

    if (!pathsEqual) {
      pathsEqual = true;
      for (int i = 0; i < routingPathsSize; i++) {
        if (!routingPathElementsEqual(routingPaths.get(i), routingPathDBS.get(routingPathsSize - 1 - i))) {
          pathsEqual = false;
          break;
        }
      }
    }

    return !pathsEqual;
  }

  private boolean routingPathElementsEqual(RoutingPath routingPath, RoutingPathDBImpl routingPathDB) {
    return Objects.equals(routingPath.layerProtocolQualifierName(), routingPathDB.getLayerProtocolQualifierName())
            && Objects.equals(routingPath.role(), routingPathDB.getRole())
            && Objects.equals(routingPath.cepNrls(), routingPathDB.getCepNrls());
  }
}
