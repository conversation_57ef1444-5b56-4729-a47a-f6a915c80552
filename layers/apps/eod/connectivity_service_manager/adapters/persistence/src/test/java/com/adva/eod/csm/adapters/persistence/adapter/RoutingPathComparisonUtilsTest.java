/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: spyrosm
 */

package com.adva.eod.csm.adapters.persistence.adapter;

import com.adva.eod.csm.adapters.persistence.entity.RoutingPathDBImpl;
import com.adva.eod.csm.connectivityservice.entity.RoutingPath;
import com.adva.nlms.opticalparameters.api.enums.Role;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {RoutingPathComparisonUtils.class})
class RoutingPathComparisonUtilsTest {
  @Autowired
  private RoutingPathComparisonUtils routingPathComparisonUtils;

  @Test
  void isRoutingPathNotEqualFalseWhenListsSame() {
    // Case 1 - Same elements
    List<RoutingPath> routingPaths = List.of(
            new RoutingPath(Role.WORKING, "OMS", List.of("aNepNrl", "zNepNrl")),
            new RoutingPath(Role.PROTECTING, "OMS", List.of("aNepNrl2", "zNepNrl2")));

    List<RoutingPathDBImpl> routingPathDBS = List.of(
            new RoutingPathDBImpl(Role.WORKING, "OMS", List.of("aNepNrl", "zNepNrl")),
            new RoutingPathDBImpl(Role.PROTECTING, "OMS", List.of("aNepNrl2", "zNepNrl2")));
    assertFalse(routingPathComparisonUtils.isRoutingPathNotEqual(routingPaths, routingPathDBS));

    // Case 2 - Empty lists
    assertFalse(routingPathComparisonUtils.isRoutingPathNotEqual(List.of(), List.of()));
  }

  @Test
  void testIsRoutingPathNotEqualTrueWhenListSizesDiffer() {
    List<RoutingPath> routingPaths = List.of(
            new RoutingPath(Role.WORKING, "OMS", List.of("aNepNrl", "zNepNrl")),
            new RoutingPath(Role.WORKING, "OMS", List.of("aNepNrlMiddle", "zNepNrlMiddle")),
            new RoutingPath(Role.PROTECTING, "OMS", List.of("aNepNrl2", "zNepNrl2")));

    List<RoutingPathDBImpl> routingPathDBS = List.of(
            new RoutingPathDBImpl(Role.WORKING, "OMS", List.of("aNepNrl", "zNepNrl")),
            new RoutingPathDBImpl(Role.PROTECTING, "OMS", List.of("aNepNrl2", "zNepNrl2")));

    assertTrue(routingPathComparisonUtils.isRoutingPathNotEqual(routingPaths, routingPathDBS));

    assertTrue(routingPathComparisonUtils.isRoutingPathNotEqual(List.of(), routingPathDBS));
    assertTrue(routingPathComparisonUtils.isRoutingPathNotEqual(routingPaths, List.of()));
  }

  @Test
  void testIsRoutingPathNotEqualTrueWhenElementsNotSame() {
    // Case 1 - Same endpoints & roles, different LPQs
    List<RoutingPath> routingPaths = List.of(
            new RoutingPath(Role.WORKING, "OMS", List.of("aNepNrl", "zNepNrl")),
            new RoutingPath(Role.PROTECTING, "OMS", List.of("aNepNrl2", "zNepNrl2")));

    List<RoutingPathDBImpl> routingPathDBS = List.of(
            new RoutingPathDBImpl(Role.WORKING, "OMS", List.of("aNrl", "zNrl")),
            new RoutingPathDBImpl(Role.PROTECTING, "OMS", List.of("aNrl2", "zNrl2")));
    assertTrue(routingPathComparisonUtils.isRoutingPathNotEqual(routingPaths, routingPathDBS));

    // Case 2 - Same endpoints & LPQs, different roles
    routingPathDBS = List.of(
            new RoutingPathDBImpl(Role.PROTECTING, "OMS", List.of("aNepNrl", "zNepNrl")),
            new RoutingPathDBImpl(Role.PROTECTING, "OMS", List.of("aNepNrl2", "zNepNrl2")));
    assertTrue(routingPathComparisonUtils.isRoutingPathNotEqual(routingPaths, routingPathDBS));

    // Case 3 - Same LPQ & roles, different endpoints
    routingPaths = List.of(
            new RoutingPath(Role.WORKING, "OMS", List.of("aNepNrl", "zNepNrl")),
            new RoutingPath(Role.WORKING, "OMS", List.of("aNepNrlMiddle", "zNepNrlMiddle")),
            new RoutingPath(Role.PROTECTING, "OMS", List.of("aNepNrl2", "zNepNrl2")));

    routingPathDBS = List.of(
            new RoutingPathDBImpl(Role.WORKING, "OMS", List.of("aNepNrl", "zNepNrl")),
            new RoutingPathDBImpl(Role.WORKING, "OMS", List.of("aNepNrlMiddle2", "zNepNrlMiddle2")),
            new RoutingPathDBImpl(Role.PROTECTING, "OMS", List.of("aNepNrl2", "zNepNrl2")));
    assertTrue(routingPathComparisonUtils.isRoutingPathNotEqual(routingPaths, routingPathDBS));

    // Case4 - Different element
    routingPaths = List.of(new RoutingPath(null, "OMS", null));
    routingPathDBS = List.of(new RoutingPathDBImpl());
    assertTrue(routingPathComparisonUtils.isRoutingPathNotEqual(routingPaths, routingPathDBS));

  }

  @Test
  void testIsRoutingPathNotEqualTrueWhenElementsSwapped() {
    List<RoutingPath> routingPaths = List.of(
            new RoutingPath(Role.WORKING, "OMS", List.of("aNepNrl", "zNepNrl")),
            new RoutingPath(Role.WORKING, "OMS", List.of("aNepNrlMiddle", "zNepNrlMiddle")),
            new RoutingPath(Role.PROTECTING, "OMS", List.of("aNepNrl2", "zNepNrl2")));

    List<RoutingPathDBImpl> routingPathDBS = List.of(
            new RoutingPathDBImpl(Role.WORKING, "OMS", List.of("aNepNrl", "zNepNrl")),
            new RoutingPathDBImpl(Role.PROTECTING, "OMS", List.of("aNepNrl2", "zNepNrl2")),
            new RoutingPathDBImpl(Role.WORKING, "OMS", List.of("aNepNrlMiddle", "zNepNrlMiddle")));
    assertTrue(routingPathComparisonUtils.isRoutingPathNotEqual(routingPaths, routingPathDBS));
  }
}
