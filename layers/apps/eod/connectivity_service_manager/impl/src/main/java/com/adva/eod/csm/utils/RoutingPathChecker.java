/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: nikolaosl
 */

package com.adva.eod.csm.utils;

import com.adva.eod.csm.connectivityservice.entity.RoutingPath;

import java.util.List;
import java.util.Objects;

public class RoutingPathChecker {

  private RoutingPathChecker() {
  }

  public static boolean isRoutingPathNotEqual(List<RoutingPath> nominalRoutingPaths, List<RoutingPath> currentRoutingPaths) {
    int nominalRoutingPathsSize = nominalRoutingPaths.size();

    if (nominalRoutingPathsSize != currentRoutingPaths.size()) {
      return true;
    }

    boolean pathsEqual = true;

    for (int i = 0; i < nominalRoutingPathsSize; i++) {
      if (!routingPathElementsEqual(nominalRoutingPaths.get(i), currentRoutingPaths.get(i))) {
        pathsEqual = false;
        break;
      }
    }

    if (!pathsEqual) {
      pathsEqual = true;
      for (int i = 0; i < nominalRoutingPathsSize; i++) {
        if (!routingPathElementsEqual(nominalRoutingPaths.get(i), currentRoutingPaths.get(nominalRoutingPathsSize - 1 - i))) {
          pathsEqual = false;
          break;
        }
      }
    }

    return !pathsEqual;
  }

  private static boolean routingPathElementsEqual(RoutingPath nominalRoutingPath, RoutingPath currentRoutingPath) {
    return Objects.equals(nominalRoutingPath.layerProtocolQualifierName(), currentRoutingPath.layerProtocolQualifierName())
            && Objects.equals(nominalRoutingPath.role(), currentRoutingPath.role())
            && Objects.equals(nominalRoutingPath.cepNrls(), currentRoutingPath.cepNrls());
  }
}
