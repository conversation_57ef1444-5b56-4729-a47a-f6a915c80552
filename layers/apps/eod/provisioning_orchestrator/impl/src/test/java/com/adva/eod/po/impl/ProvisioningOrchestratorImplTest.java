/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.eod.po.impl;

import com.adva.eod.nrim.api.dto.ConnectionPointDto;
import com.adva.eod.nrim.api.dto.NodeCompactDto;
import com.adva.eod.nrim.api.dto.RouteDto;
import com.adva.eod.nrim.api.dto.SwitchDto;
import com.adva.eod.nrim.api.in.ConnectionsApi;
import com.adva.eod.po.api.in.SetAdminStateRequest;
import com.adva.eod.po.api.in.SwitchRouteRequest;
import com.adva.eod.po.api.in.TargetRoute;
import com.adva.eod.po.api.in.exception.ConnectionException;
import com.adva.eod.po.api.in.exception.SwitchNotFoundException;
import com.adva.eod.po.impl.lifecycle.LifecycleChangedEvent;
import com.adva.eod.po.impl.lifecycle.LifecycleEvent;
import com.adva.eod.po.impl.lifecycle.LifecycleEventPublisher;
import com.adva.eod.po.impl.lifecycle.LifecycleEventType;
import com.adva.eod.po.impl.lifecycle.LifecycleStartedEvent;
import com.adva.nlms.commondefinition.namerepresentation.NameDto;
import com.adva.nlms.commondefinition.namerepresentation.NameRepresentation;
import com.adva.nlms.mediation.security.api.session.SessionHdlrApi;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.opticalparameters.api.enums.SwitchState;
import com.adva.nlms.resource.mediator.api.in.OperationFailedException;
import com.adva.nlms.resource.mediator.api.in.ResourceRequestMediator;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ProvisioningOrchestratorImplTest {
  private static final UUID OPERATION_ID = UUID.randomUUID();
  private static final UUID CONNECTION_ID = UUID.randomUUID();
  private static final String CONNECTION_NAME = "service_name";
  private static final UUID NE_ID_A = UUID.randomUUID();
  private static final UUID NE_ID_Z = UUID.randomUUID();
  private static final String NE_NAME_A = "node A";
  private static final String NE_NAME_Z = "node Z";
  private static final String DEVICE = "device_1";
  private static final String PROTECTION_GROUP_IDX = "EC_CAT^/mit/me/1/eqh/shelf,1/eqh/slot,4/eq/card/prtgrp/traffic-1";
  private static final int USER_ID = 1;
  @Mock
  ResourceRequestMediator resourceRequestMediator;
  @Mock
  ConnectionsApi connectionsApi;
  @Mock
  ExecutorService executorService;
  @Mock
  LifecycleEventPublisher lifecycleEventPublisher;
  @Mock
  SessionHdlrApi sessionHdlrApi;
  @InjectMocks
  ProvisioningOrchestratorImpl provisioningOrchestrator;

  @Test
  void invokeSwitchRouteOperation_missingConnectionId() {
    final var request = new SwitchRouteRequest(null, TargetRoute.WORKING, CONNECTION_NAME);
    assertThrows(SwitchNotFoundException.class, () -> provisioningOrchestrator.invokeSwitchRouteOperation(request));
  }

  @Test
  void invokeSwitchRouteOperation_missingServiceName() {
    final var request = new SwitchRouteRequest(CONNECTION_ID, TargetRoute.WORKING, null);
    assertThrows(SwitchNotFoundException.class, () -> provisioningOrchestrator.invokeSwitchRouteOperation(request));
  }

  @Test
  void invokeSwitchRouteOperation_missingTargetRoute() {
    final var request = new SwitchRouteRequest(CONNECTION_ID, null, CONNECTION_NAME);
    assertThrows(SwitchNotFoundException.class, () -> provisioningOrchestrator.invokeSwitchRouteOperation(request));
  }

  @Test
  void invokeSwitchRouteOperation_success() throws SwitchNotFoundException, OperationFailedException {
    initExecutorForCallables();
    initExecutorForRunnables();
    String operationName = SwitchProtectionEventFactory.OPERATION_NAME_SWITCH_TO_WORKING;
    final var request = new SwitchRouteRequest(CONNECTION_ID, TargetRoute.WORKING, CONNECTION_NAME);
    final var switchDtos = List.of(
      new SwitchDto(NE_ID_A, NE_NAME_A, PROTECTION_GROUP_IDX, SwitchState.PROTECTING),
      new SwitchDto(NE_ID_Z, NE_NAME_Z, PROTECTION_GROUP_IDX, SwitchState.PROTECTING)
    );
    when(connectionsApi.getSwitchesForConnection(CONNECTION_ID)).thenReturn(switchDtos);
    when(sessionHdlrApi.getClientID()).thenReturn(USER_ID);
    provisioningOrchestrator.invokeSwitchRouteOperation(request, OPERATION_ID);
    verify(resourceRequestMediator).handleProtectionSwitch(NE_ID_A, PROTECTION_GROUP_IDX);
    verify(resourceRequestMediator).handleProtectionSwitch(NE_ID_Z, PROTECTION_GROUP_IDX);
    verify(lifecycleEventPublisher).publishEvent(Events.createLifecycleStartEvent(operationName, switchDtos.size()));
    verify(lifecycleEventPublisher).publishEvent(Events.createLifecycleSuccessfulProgressEvent(operationName, NE_NAME_A));
    verify(lifecycleEventPublisher).publishEvent(Events.createLifecycleSuccessfulProgressEvent(operationName, NE_NAME_Z));
    verify(lifecycleEventPublisher).publishEvent(Events.createLifecycleCompletedEvent(operationName));
  }

  private void initExecutorForCallables() {
    doAnswer(invocation -> {
      Callable<Boolean> callable = invocation.getArgument(0);
      boolean result = callable.call();
      Future<Boolean> future = Mockito.mock();
      // lenient is needed for invokeSwitchRouteOperation_switchingFailedOnOne test -> Unnecessary stubbings detected.
      Mockito.lenient().when(future.get()).thenReturn(result);
      return future;
    }).when(executorService).submit(ArgumentMatchers.<Callable<?>>any());
  }

  private void initExecutorForRunnables() {
    doAnswer(invocation -> {
      Runnable runnable = invocation.getArgument(0);
      runnable.run();
      return null;
    }).when(executorService).submit(any(Runnable.class));
  }

  @Test
  void invokeSwitchRouteOperation_successWithUnlikelyNumberOfSwitchObjects() throws SwitchNotFoundException, OperationFailedException {
    initExecutorForCallables();
    String operationName = SwitchProtectionEventFactory.OPERATION_NAME_SWITCH_TO_WORKING;
    final var request = new SwitchRouteRequest(CONNECTION_ID, TargetRoute.WORKING, CONNECTION_NAME);
    final var switchDtos = List.of(
      new SwitchDto(NE_ID_A, NE_NAME_A, PROTECTION_GROUP_IDX, SwitchState.PROTECTING),
      new SwitchDto(NE_ID_Z, NE_NAME_Z, PROTECTION_GROUP_IDX, SwitchState.PROTECTING),
      new SwitchDto(UUID.randomUUID(), "some device", PROTECTION_GROUP_IDX, SwitchState.WORKING)
    );
    when(connectionsApi.getSwitchesForConnection(CONNECTION_ID)).thenReturn(switchDtos);
    when(sessionHdlrApi.getClientID()).thenReturn(USER_ID);
    provisioningOrchestrator.invokeSwitchRouteOperation(request, OPERATION_ID);
    verify(resourceRequestMediator).handleProtectionSwitch(NE_ID_A, PROTECTION_GROUP_IDX);
    verify(resourceRequestMediator).handleProtectionSwitch(NE_ID_Z, PROTECTION_GROUP_IDX);
    verify(lifecycleEventPublisher).publishEvent(Events.createLifecycleStartEvent(operationName, 2));
  }

  @Test
  void invokeSwitchRouteOperation_successOnlyOneSwitchNeeded() throws SwitchNotFoundException, OperationFailedException {
    initExecutorForCallables();
    final var request = new SwitchRouteRequest(CONNECTION_ID, TargetRoute.PROTECTING, CONNECTION_NAME);
    final var switchDtos = List.of(
      new SwitchDto(NE_ID_A, NE_NAME_A, PROTECTION_GROUP_IDX, SwitchState.WORKING),
      new SwitchDto(NE_ID_Z, NE_NAME_Z, PROTECTION_GROUP_IDX, SwitchState.PROTECTING)
    );
    when(connectionsApi.getSwitchesForConnection(CONNECTION_ID)).thenReturn(switchDtos);
    provisioningOrchestrator.invokeSwitchRouteOperation(request);
    verify(resourceRequestMediator).handleProtectionSwitch(NE_ID_A, PROTECTION_GROUP_IDX);
  }

  @Test
  void invokeSwitchRouteOperation_successNoSwitchNeeded() throws SwitchNotFoundException {
    initExecutorForRunnables();
    String operationName = SwitchProtectionEventFactory.OPERATION_NAME_SWITCH_TO_PROTECTION;
    final var request = new SwitchRouteRequest(CONNECTION_ID, TargetRoute.PROTECTING, CONNECTION_NAME);
    final var switchDtos = List.of(
      new SwitchDto(NE_ID_A, NE_NAME_A, PROTECTION_GROUP_IDX, SwitchState.PROTECTING),
      new SwitchDto(NE_ID_Z, NE_NAME_Z, PROTECTION_GROUP_IDX, SwitchState.PROTECTING)
    );
    when(sessionHdlrApi.getClientID()).thenReturn(USER_ID);
    when(connectionsApi.getSwitchesForConnection(CONNECTION_ID)).thenReturn(switchDtos);
    provisioningOrchestrator.invokeSwitchRouteOperation(request, OPERATION_ID);
    verifyNoInteractions(resourceRequestMediator);
    verify(lifecycleEventPublisher).publishEvent(Events.createLifecycleStartEvent(operationName, 0));
    verify(lifecycleEventPublisher).publishEvent(Events.createNothingToDoEvent("This path is already active, no switch requested."));
    verify(lifecycleEventPublisher).publishEvent(Events.createLifecycleCompletedEvent(operationName));
  }

  @Test
  void invokeSwitchRouteOperation_noSwitchObjectsFound() {
    final var request = new SwitchRouteRequest(CONNECTION_ID, TargetRoute.PROTECTING, CONNECTION_NAME);
    when(connectionsApi.getSwitchesForConnection(CONNECTION_ID)).thenReturn(List.of());
    assertThrows(SwitchNotFoundException.class, () -> provisioningOrchestrator.invokeSwitchRouteOperation(request));
  }

  @Test
  void invokeSwitchRouteOperation_switchingFailedOnOne() throws SwitchNotFoundException, OperationFailedException {
    initExecutorForCallables();
    initExecutorForRunnables();
    String operationName = SwitchProtectionEventFactory.OPERATION_NAME_SWITCH_TO_PROTECTION;

    final var request = new SwitchRouteRequest(CONNECTION_ID, TargetRoute.PROTECTING, CONNECTION_NAME);
    final var switchDtos = List.of(
      new SwitchDto(NE_ID_A, NE_NAME_A, PROTECTION_GROUP_IDX, SwitchState.WORKING),
      new SwitchDto(NE_ID_Z, NE_NAME_Z, PROTECTION_GROUP_IDX, SwitchState.WORKING)
    );
    when(connectionsApi.getSwitchesForConnection(CONNECTION_ID)).thenReturn(switchDtos);
    doThrow(OperationFailedException.class)
      .when(resourceRequestMediator).handleProtectionSwitch(NE_ID_A, PROTECTION_GROUP_IDX);
    when(sessionHdlrApi.getClientID()).thenReturn(USER_ID);
    provisioningOrchestrator.invokeSwitchRouteOperation(request, OPERATION_ID);
    verify(resourceRequestMediator).handleProtectionSwitch(NE_ID_Z, PROTECTION_GROUP_IDX);
    verify(lifecycleEventPublisher).publishEvent(Events.createLifecycleStartEvent(operationName, switchDtos.size()));
    verify(lifecycleEventPublisher).publishEvent(Events.createLifecycleSuccessfulProgressEvent(operationName, NE_NAME_Z));
    verify(lifecycleEventPublisher).publishEvent(Events.createLifecycleFailProgressEvent(operationName, NE_NAME_A));
    verify(lifecycleEventPublisher).publishEvent(Events.createLifecycleFailEvent(operationName));
  }

  @Test
  void invokeSetAdminStateOperation_missingConnectionId() {
    assertThrows(ConnectionException.class, () -> provisioningOrchestrator.invokeSetAdminStateOperation(new SetAdminStateRequest(null, AdminState.UP, "Test")));
  }

  @Test
  void invokeSetAdminStateOperation_missingAdminState() {
    assertThrows(ConnectionException.class, () -> provisioningOrchestrator.invokeSetAdminStateOperation(new SetAdminStateRequest(CONNECTION_ID, AdminState.UP, "Test")));
  }

  @Test
  void invokeSetAdminStateOperationWhenLayerQualifierNotFound() {
    when(connectionsApi.getLayerProtocolQualifierOfAConnection(CONNECTION_ID)).thenReturn(null);
    assertThrows(ConnectionException.class, () -> provisioningOrchestrator.invokeSetAdminStateOperation(new SetAdminStateRequest(CONNECTION_ID, AdminState.UP, "Test")));
  }

  @Test
  void invokeSetAdminStateOperationWhenRoutesDtoWasNull() {
    when(connectionsApi.getLayerProtocolQualifierOfAConnection(CONNECTION_ID)).thenReturn(LayerQualifier.OTSIMC.toString());
    when(connectionsApi.getRoutesForConnection(CONNECTION_ID)).thenReturn(null);
    assertThrows(ConnectionException.class, () -> provisioningOrchestrator.invokeSetAdminStateOperation(new SetAdminStateRequest(CONNECTION_ID, AdminState.UP, "Test")));
  }

  @Test
  void invokeSetAdminStateOperationWhenRoutesDtosIsEmpty() {
    when(connectionsApi.getLayerProtocolQualifierOfAConnection(CONNECTION_ID)).thenReturn(LayerQualifier.OTSIMC.toString());
    when(connectionsApi.getRoutesForConnection(CONNECTION_ID)).thenReturn(List.of());
    assertThrows(ConnectionException.class, () -> provisioningOrchestrator.invokeSetAdminStateOperation(new SetAdminStateRequest(CONNECTION_ID, AdminState.UP, "Test")));
  }

  @Test
  void invokeSetAdminStateOperationWhenConnectionPointDtoIsEmpty() throws ConnectionException {
    List<RouteDto> routeDtos = List.of(new RouteDto(List.of(new ConnectionPointDto(null, null, null))));
    when(connectionsApi.getLayerProtocolQualifierOfAConnection(CONNECTION_ID)).thenReturn(LayerQualifier.OTSIMC.toString());
    when(connectionsApi.getRoutesForConnection(CONNECTION_ID)).thenReturn(routeDtos);
    provisioningOrchestrator.invokeSetAdminStateOperation(new SetAdminStateRequest(CONNECTION_ID, AdminState.MAINTENANCE, "Test"));
    verifyNoInteractions(resourceRequestMediator);
  }

  @Test
  void invokeSetAdminStateOperationWhenResourceRequestMediatorThrowException() throws ConnectionException, OperationFailedException {
    initExecutorForRunnables();
    initExecutorForCallables();
    String operationName = SetAdminStateEventFactory.OPERATION_NAME.formatted(AdminState.MAINTENANCE.getDescription());
    List<RouteDto> routeDtos = List.of(new RouteDto(List.of(new ConnectionPointDto(new NodeCompactDto(NE_ID_A, List.of(new NameDto(NameRepresentation.USER_LABEL, NE_NAME_A))), null, List.of(DEVICE)))));
    when(connectionsApi.getLayerProtocolQualifierOfAConnection(CONNECTION_ID)).thenReturn(LayerQualifier.OTSIMC.toString());
    when(connectionsApi.getRoutesForConnection(CONNECTION_ID)).thenReturn(routeDtos);
    when(sessionHdlrApi.getClientID()).thenReturn(USER_ID);
    doThrow(OperationFailedException.class)
      .when(resourceRequestMediator)
      .handleAdminStateOperation(NE_ID_A, List.of(DEVICE), null, AdminState.MAINTENANCE, LayerQualifier.OTSIMC);
    provisioningOrchestrator.invokeSetAdminStateOperation(OPERATION_ID, new SetAdminStateRequest(CONNECTION_ID, AdminState.MAINTENANCE, CONNECTION_NAME));
    verify(resourceRequestMediator)
      .handleAdminStateOperation(NE_ID_A, List.of(DEVICE), null, AdminState.MAINTENANCE, LayerQualifier.OTSIMC);
    verify(lifecycleEventPublisher).publishEvent(Events.createLifecycleStartEvent(operationName, 1));
    verify(lifecycleEventPublisher).publishEvent(Events.createLifecycleFailProgressEvent(operationName, NE_NAME_A));
    verify(lifecycleEventPublisher).publishEvent(Events.createLifecycleFailEvent(operationName));
  }

  @Test
  void invokeSetAdminState_switchingSuccessful() throws ConnectionException, OperationFailedException {
    initExecutorForRunnables();
    initExecutorForCallables();
    String operationName = SetAdminStateEventFactory.OPERATION_NAME.formatted(AdminState.MAINTENANCE.getDescription());
    List<RouteDto> routeDtos = List.of(
      new RouteDto(List.of(
        new ConnectionPointDto(
          new NodeCompactDto(NE_ID_A, List.of(new NameDto(NameRepresentation.USER_LABEL, NE_NAME_A))),
          null,
          List.of(DEVICE)
        )
      ))
    );
    when(connectionsApi.getLayerProtocolQualifierOfAConnection(CONNECTION_ID)).thenReturn(LayerQualifier.OTSIMC.toString());
    when(connectionsApi.getRoutesForConnection(CONNECTION_ID)).thenReturn(routeDtos);
    when(sessionHdlrApi.getClientID()).thenReturn(USER_ID);
    provisioningOrchestrator.invokeSetAdminStateOperation(OPERATION_ID, new SetAdminStateRequest(CONNECTION_ID, AdminState.MAINTENANCE, CONNECTION_NAME));
    verify(resourceRequestMediator)
      .handleAdminStateOperation(NE_ID_A, List.of(DEVICE), null, AdminState.MAINTENANCE, LayerQualifier.OTSIMC);
    verify(lifecycleEventPublisher).publishEvent(Events.createLifecycleStartEvent(operationName, 1));
    verify(lifecycleEventPublisher).publishEvent(Events.createLifecycleSuccessfulProgressEvent(operationName, NE_NAME_A));
    verify(lifecycleEventPublisher).publishEvent(Events.createLifecycleCompletedEvent(operationName));
  }

  private static class Events {
    private static LifecycleEvent createLifecycleStartEvent(String operationName, int nodesNumber) {
      return new LifecycleStartedEvent(
        OPERATION_ID,
        operationName,
        CONNECTION_ID,
        CONNECTION_NAME,
        nodesNumber,
        "%s for service %s has started.".formatted(operationName, CONNECTION_NAME),
        USER_ID);
    }

    private static LifecycleEvent createLifecycleCompletedEvent(String operationName) {
      return new LifecycleChangedEvent(
        LifecycleEventType.OPERATION_COMPLETED,
        OPERATION_ID,
        "%s for service %s has been completed.".formatted(operationName, CONNECTION_NAME),
        USER_ID);
    }

    private static LifecycleEvent createLifecycleFailEvent(String operationName) {
      return new LifecycleChangedEvent(
        LifecycleEventType.OPERATION_FAILED,
        OPERATION_ID,
        "%s for service %s has failed.".formatted(operationName, CONNECTION_NAME),
        USER_ID);
    }

    private static LifecycleEvent createLifecycleSuccessfulProgressEvent(String operationName, String neName) {
      return new LifecycleChangedEvent(
        LifecycleEventType.OPERATION_STEP_SUCCEEDED,
        OPERATION_ID,
        "%s has been completed on %s.".formatted(operationName, neName),
        USER_ID);
    }

    private static LifecycleEvent createLifecycleFailProgressEvent(String operationName, String neName) {
      return new LifecycleChangedEvent(
        LifecycleEventType.OPERATION_STEP_FAILED,
        OPERATION_ID,
        "%s has failed on %s.".formatted(operationName, neName),
        USER_ID);
    }

    private static LifecycleEvent createNothingToDoEvent(String message) {
      return new LifecycleChangedEvent(
        LifecycleEventType.OPERATION_NOTHING_TO_DO,
        OPERATION_ID,
        message,
        USER_ID);
    }
  }

}
