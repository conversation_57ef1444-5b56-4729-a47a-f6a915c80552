/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.eod.po.impl;

import com.adva.eod.nrim.api.dto.ConnectionPointDto;
import com.adva.eod.nrim.api.dto.RouteDto;
import com.adva.eod.nrim.api.dto.SwitchDto;
import com.adva.eod.nrim.api.in.ConnectionsApi;
import com.adva.eod.po.api.in.ProvisioningOrchestrator;
import com.adva.eod.po.api.in.SetAdminStateRequest;
import com.adva.eod.po.api.in.SwitchRouteRequest;
import com.adva.eod.po.api.in.TargetRoute;
import com.adva.eod.po.api.in.exception.ConnectionException;
import com.adva.eod.po.api.in.exception.SwitchNotFoundException;
import com.adva.eod.po.impl.lifecycle.LifecycleEventPublisher;
import com.adva.nlms.mediation.security.api.session.SessionHdlrApi;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.opticalparameters.api.enums.SwitchState;
import com.adva.nlms.resource.mediator.api.in.OperationFailedException;
import com.adva.nlms.resource.mediator.api.in.ResourceRequestMediator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.function.Consumer;
import java.util.stream.Collectors;

class ProvisioningOrchestratorImpl implements ProvisioningOrchestrator {
  private static final Logger log = LogManager.getLogger(ProvisioningOrchestratorImpl.class);
  private static final String PROTECTION_UNIT_PREFIX = "Protection-Unit";

  private final ResourceRequestMediator resourceRequestMediator;
  private final ConnectionsApi connectionsApi;
  private final ExecutorService executorService;
  private final LifecycleEventPublisher lifecycleEventPublisher;
  private final SessionHdlrApi sessionHdlrApi;

  ProvisioningOrchestratorImpl(ResourceRequestMediator resourceRequestMediator,
                               ConnectionsApi connectionsApi,
                               ExecutorService executorService,
                               LifecycleEventPublisher lifecycleEventPublisher, SessionHdlrApi sessionHdlrApi) {
    this.resourceRequestMediator = resourceRequestMediator;
    this.connectionsApi = connectionsApi;
    this.executorService = executorService;
    this.lifecycleEventPublisher = lifecycleEventPublisher;
    this.sessionHdlrApi = sessionHdlrApi;
  }

  @Override
  public void invokeSwitchRouteOperation(SwitchRouteRequest switchRouteRequest) throws SwitchNotFoundException {
    invokeSwitchRouteOperation(switchRouteRequest, UUID.randomUUID());
  }

  void invokeSwitchRouteOperation(SwitchRouteRequest switchRouteRequest, UUID operationId) throws SwitchNotFoundException {
    log.info("ProtectionSwitch Operation started {}, operationId={}", switchRouteRequest, operationId);
    validateSwitchRouteRequest(switchRouteRequest);
    List<SwitchDto> switchDtos = connectionsApi.getSwitchesForConnection(switchRouteRequest.connectionId());
    validateSwitches(switchDtos, switchRouteRequest.connectionId());

    List<SwitchDto> nodesToSwitchProtection = findNodesToSwitchProtection(switchRouteRequest.targetRoute(), switchDtos);
    final int userId = sessionHdlrApi.getClientID();
    lifecycleEventPublisher.publishEvent(SwitchProtectionEventFactory.createStartEvent(operationId, switchRouteRequest, nodesToSwitchProtection.size(), userId));
    if (nodesToSwitchProtection.isEmpty()) {
      lifecycleEventPublisher.publishEvent(SwitchProtectionEventFactory.creteNothingToDoEvent(operationId, userId));
    }
    var switchOperationsInProgress = nodesToSwitchProtection.stream()
      .map(switchDto -> switchProtection(switchDto, switchRouteRequest.targetRoute(), operationId, userId))
      .toList();
    doWhenFuturesCompleted("Protection switch", switchOperationsInProgress, overallStatusSuccess -> {
      if (overallStatusSuccess == Boolean.TRUE) {
        log.info("Protection switch operation completed successfully.");
        lifecycleEventPublisher.publishEvent(SwitchProtectionEventFactory.createCompleteEvent(switchRouteRequest, operationId, userId));
      } else {
        log.warn("Protection switch operation failed.");
        lifecycleEventPublisher.publishEvent(SwitchProtectionEventFactory.createFailEvent(switchRouteRequest, operationId, userId));
      }
    });
  }

  private void validateSwitchRouteRequest(SwitchRouteRequest request) throws SwitchNotFoundException {
    if (request.connectionId() == null) {
      throw new SwitchNotFoundException("Missing required connectionId parameter");
    }
    if (request.targetRoute() == null) {
      throw new SwitchNotFoundException("Missing required targetRoute parameter");
    }
    if (request.serviceName() == null) {
      throw new SwitchNotFoundException("Missing required serviceName parameter");
    }
  }

  private void validateSwitches(List<SwitchDto> switchDtos, UUID connectionId) throws SwitchNotFoundException {
    if (CollectionUtils.isEmpty(switchDtos)) {
      throw new SwitchNotFoundException("Switch objects not found for connection " + connectionId);
    }
    if (switchDtos.size() != 2) {
      log.warn("Unexpected number of Switch objects found for connection {}", connectionId);
    }
  }

  private List<SwitchDto> findNodesToSwitchProtection(TargetRoute targetRoute, List<SwitchDto> switchDtos) {
    List<SwitchDto> nodesToSwitchProtection = new ArrayList<>();
    for (SwitchDto switchDto : switchDtos) {
      if (mismatchBetweenTargetAndCurrentState(targetRoute, switchDto.switchState())) {
        nodesToSwitchProtection.add(switchDto);
      } else {
        log.info("{} already has target state {}", switchDto, targetRoute);
      }
    }
    return Collections.unmodifiableList(nodesToSwitchProtection);
  }

  private Future<Boolean> switchProtection(SwitchDto switchDto, TargetRoute targetRoute, UUID operationId, int userId) {
    return executorService.submit(() -> {
      try {
        log.info("Invoking protection switch operation on {}", switchDto);
        resourceRequestMediator.handleProtectionSwitch(switchDto.neUuid(), switchDto.entityIndex());
        lifecycleEventPublisher.publishEvent(SwitchProtectionEventFactory.createProgressSucceedEvent(switchDto.neName(), targetRoute, operationId, userId));
        return true;
      } catch (Exception e) {
        log.warn("Protection switch operation failed for {}", switchDto, e);
        lifecycleEventPublisher.publishEvent(SwitchProtectionEventFactory.createProgressFailedEvent(switchDto.neName(), targetRoute, operationId, userId));
        return false;
      }
    });
  }

  private void doWhenFuturesCompleted(String operation, List<Future<Boolean>> futures, Consumer<Boolean> action) {
    executorService.submit(() -> {
      boolean allSucceed = futures.stream()
        .map(f -> {
          try {
            return f.get();
          } catch (ExecutionException executionException) {
            log.error("Waiting for {} operation to complete on individual network elements has failed", operation, executionException);
          } catch (InterruptedException interruptedException) {
            log.error("Waiting for {} operation to complete on individual network elements has been interrupted", operation, interruptedException);
            Thread.currentThread().interrupt();
          }
          return false;
        })
        .allMatch(Boolean.TRUE::equals);
      action.accept(allSucceed);
    });
  }

  private boolean mismatchBetweenTargetAndCurrentState(TargetRoute targetRoute, SwitchState currentState) {
    return switch (targetRoute) {
      case PROTECTING -> currentState != SwitchState.PROTECTING;
      case WORKING -> currentState != SwitchState.WORKING;
    };
  }

  @Override
  public void invokeSetAdminStateOperation(final SetAdminStateRequest setAdminStateRequest) throws ConnectionException {
    invokeSetAdminStateOperation(UUID.randomUUID(), setAdminStateRequest);
  }

  public void invokeSetAdminStateOperation(UUID operationId, final SetAdminStateRequest setAdminStateRequest) throws ConnectionException {
    log.info("Set admin state operation started {}, operationId={}", setAdminStateRequest, operationId);
    validateSetAdminStateRequest(setAdminStateRequest);
    LayerQualifier lpq = getLayerProtocolQualifierOfAConnection(setAdminStateRequest.connectionId());
    List<AdminStateDTO> adminStateSwitches = findAdminStateSwitches(setAdminStateRequest);
    final int userId = sessionHdlrApi.getClientID();
    lifecycleEventPublisher.publishEvent(SetAdminStateEventFactory.createStartEvent(operationId, setAdminStateRequest, adminStateSwitches.size(), userId));
    final List<Future<Boolean>> futures =
      updateConnectionPointsByAdminState(operationId, adminStateSwitches, setAdminStateRequest.adminState(),
        setAdminStateRequest.serviceName(), userId, lpq);
    doWhenFuturesCompleted("Set Admin State", futures, resultsDisjunction -> {
      if (Boolean.TRUE.equals(resultsDisjunction)) {
        lifecycleEventPublisher.publishEvent(SetAdminStateEventFactory.createCompleteEvent(operationId, setAdminStateRequest, userId));
      } else {
        lifecycleEventPublisher.publishEvent(SetAdminStateEventFactory.createFailEvent(operationId, setAdminStateRequest, userId));
      }
    });
  }

  private LayerQualifier getLayerProtocolQualifierOfAConnection(UUID connectionId) throws ConnectionException {
    return Optional.ofNullable(connectionsApi.getLayerProtocolQualifierOfAConnection(connectionId))
      .flatMap(LayerQualifier::fromString)
      .orElseThrow(() -> new ConnectionException("Unable to determine layer protocol qualifier of connection " + connectionId));
  }

  private void validateSetAdminStateRequest(SetAdminStateRequest request) throws ConnectionException {
    if (request.connectionId() == null) {
      throw new ConnectionException("Missing required connectionId parameter");
    }
    if (request.adminState() == null) {
      throw new ConnectionException("Missing required adminState parameter");
    }
  }

  private List<AdminStateDTO> findAdminStateSwitches(SetAdminStateRequest setAdminStateRequest) throws ConnectionException {
    final List<RouteDto> routeDtos = connectionsApi.getRoutesForConnection(setAdminStateRequest.connectionId());
    validateRouteDtos(routeDtos, setAdminStateRequest);
    final List<SwitchDto> switchDtos = connectionsApi.getSwitchesForConnection(setAdminStateRequest.connectionId());
    final List<AdminStateDTO> adminStateDTOS = getAdminStateDTO(routeDtos, switchDtos);
    List<AdminStateDTO> result = new ArrayList<>();
    for (AdminStateDTO adminStateDTO : adminStateDTOS) {
      if (adminStateDTO.nodeId() == null) {
        log.error("NodeId is null in adminStateDTO: {}", adminStateDTO);
      } else if (CollectionUtils.isEmpty(adminStateDTO.deviceEntities())) {
        log.warn("Device entities list is empty for nodeId: {}", adminStateDTO.nodeId());
      } else {
        result.add(adminStateDTO);
      }
    }
    return Collections.unmodifiableList(result);
  }

  private List<Future<Boolean>> updateConnectionPointsByAdminState(
    UUID operationId,
    List<AdminStateDTO> adminStateDTOS,
    AdminState adminState,
    String serviceName,
    int userId,
    LayerQualifier lpq
  ) {
    return adminStateDTOS.stream()
      .map(adminStateDTO -> executorService.submit(
        () -> updateAdminState(operationId, adminStateDTO, adminState, serviceName, userId, lpq)
      ))
      .toList();
  }

  private void validateRouteDtos(List<RouteDto> routeDtos, SetAdminStateRequest setAdminStateRequest) throws ConnectionException {
    if (CollectionUtils.isEmpty(routeDtos)) {
      log.error("RouteDtos not found for connectionId: {}", setAdminStateRequest.connectionId());
      throw new ConnectionException("RouteDtos not found for connectionId: " + setAdminStateRequest.connectionId());
    }
    if (routeDtos.stream().anyMatch(route -> CollectionUtils.isEmpty(route.connectionPoints()))) {
      log.error("ConnectionPoints not found in RouteDTO");
      throw new ConnectionException("ConnectionPoints not found in RouteDTO");
    }
  }

  private List<AdminStateDTO> getAdminStateDTO(final List<RouteDto> routeDtos, final List<SwitchDto> switchDtos) {
    final Map<UUID, List<ConnectionPointDto>> connectionsByNeId = routeDtos
      .stream()
      .map(RouteDto::connectionPoints)
      .flatMap(Collection::stream)
      .filter(connectionPointDto -> connectionPointDto.node() != null)
      .filter(connectionPointDto -> connectionPointDto.node().id() != null)
      .collect(Collectors.groupingBy(connectionPointDto -> connectionPointDto.node().id()));
    return createAdminStateDTOS(connectionsByNeId, switchDtos);
  }

  private boolean updateAdminState(
    UUID operationId,
    AdminStateDTO adminStateDTO,
    AdminState adminState,
    String serviceName,
    int userId,
    LayerQualifier lpq
  ) {
    try {
      log.info("Switch admin state to {} for nodeId {}, service name {}", adminStateDTO, adminStateDTO.nodeId(), serviceName);
      resourceRequestMediator.handleAdminStateOperation(adminStateDTO.nodeId(),
        adminStateDTO.deviceEntities(),
        adminStateDTO.protectionGroupEntityIndex(),
        adminState,
        lpq);
      lifecycleEventPublisher.publishEvent(SetAdminStateEventFactory.createProgressSucceedEvent(operationId, adminStateDTO.nodeName(), adminState, userId));
      return true;
    } catch (OperationFailedException e) {
      log.warn("Failed to switch admin state operation for nodeId {}, with aid's: {}, service name {}",
        adminStateDTO.nodeId(),
        String.join(",", adminStateDTO.deviceEntities()),
        serviceName,
        e);
      lifecycleEventPublisher.publishEvent(SetAdminStateEventFactory.createProgressFailedEvent(operationId, adminStateDTO.nodeName(), adminState, userId));
      return false;
    }
  }

  private List<AdminStateDTO> createAdminStateDTOS(Map<UUID, List<ConnectionPointDto>> connectionsByNeId, final List<SwitchDto> switchDtos) {
    return connectionsByNeId
      .entrySet()
      .stream()
      .map(entry -> {
        UUID neId = entry.getKey();
        Optional<SwitchDto> switchDto = findSwitchDtoByNodeId(neId, switchDtos);
        String protectionGroupEntityIndex = switchDto.map(SwitchDto::entityIndex).orElse(null);
        String neName = switchDto.map(SwitchDto::neName)
          .orElse(NeDescriptor.findConnectionPointNeName(entry.getValue(), neId).orElse(null));
        List<String> neEntitiesWithoutProtectionGroupPrefix = getDeviceEntitiesWithoutProtectionGroupPrefix(entry.getValue());
        return new AdminStateDTO(neId, neName, protectionGroupEntityIndex, neEntitiesWithoutProtectionGroupPrefix);
      })
      .toList();
  }

  private Optional<SwitchDto> findSwitchDtoByNodeId(UUID nodeId, List<SwitchDto> switchDtos) {
    return switchDtos
      .stream()
      .filter(s -> s.neUuid().equals(nodeId))
      .findAny();
  }

  private List<String> getDeviceEntitiesWithoutProtectionGroupPrefix(List<ConnectionPointDto> connectionPointDtos) {
    return connectionPointDtos
      .stream()
      .map(ConnectionPointDto::deviceEntities)
      .flatMap(Collection::stream)
      .filter(deviceEntity -> !deviceEntity.contains(PROTECTION_UNIT_PREFIX))
      .toList();
  }
}
