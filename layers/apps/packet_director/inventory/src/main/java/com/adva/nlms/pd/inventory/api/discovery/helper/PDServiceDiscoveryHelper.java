/*
 *   Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *    Owner: adatta
 */

package com.adva.nlms.pd.inventory.api.discovery.helper;

import com.adva.apps.sm.Definition;
import com.adva.nlms.common.discover.common.dto.FlowDTO;
import com.adva.nlms.common.discover.common.dto.FlowPointDto;
import com.adva.nlms.common.discover.common.dto.PDDiscoveredEntitiesDTO;
import com.adva.nlms.common.discover.common.dto.PortDTO;
import com.adva.nlms.common.messages.ConfigChangeType;
import com.adva.nlms.common.mltopologymodel.enums.MLServiceLifeCycleStateEnum;
import com.adva.nlms.common.mltopologymodel.pce.definition.ServiceIdentificationDto;
import com.adva.nlms.common.mltopologymodel.provisioning.OperationProgressAttributes;
import com.adva.nlms.common.topology.EthEntityType;
import com.adva.nlms.mediation.bean.provider.api.BeanProvider;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.common.persistence.MDTransactional;
import com.adva.nlms.mediation.config.ManagedObjectDAO;
import com.adva.nlms.mediation.config.ManagedObjectDBImpl;
import com.adva.nlms.mediation.config.f3.entity.flow.FlowF3DBImpl;
import com.adva.nlms.mediation.config.f3.entity.serviceflow.eline.ElineFlowF3DBImpl;
import com.adva.nlms.mediation.config.f3.entity.serviceflow.mp.MPFlowF3DBImpl;
import com.adva.nlms.mediation.config.f4.entity.crossconnect.FlowF4DBImpl;
import com.adva.nlms.mediation.event.message.MessageManager;
import com.adva.nlms.mediation.security.api.SecurityCtrl;
import com.adva.nlms.mediation.sm.model.CustomerDAO;
import com.adva.nlms.mediation.sm.model.CustomerDBImpl;
import com.adva.nlms.pd.api.in.dto.enums.PDConnectionPointCategory;
import com.adva.nlms.pd.api.in.dto.enums.PDDiscoveredServiceType;
import com.adva.nlms.pd.api.in.dto.enums.PDTEPropertyKey;
import com.adva.nlms.pd.api.in.service.PDServiceOperationException;
import com.adva.nlms.pd.api.in.service.ProvisioningFacadeAPI;
import com.adva.nlms.pd.api.out.event.EventOperations;
import com.adva.nlms.pd.inventory.api.PDServiceManagementFacade;
import com.adva.nlms.pd.api.in.discovery.dto.PDDiscoveredServiceDTO;
import com.adva.nlms.pd.inventory.api.discovery.exception.PDServiceDiscoveryException;
import com.adva.nlms.pd.inventory.model.dao.PDTopologyElementDAO;
import com.adva.nlms.pd.inventory.model.db.*;
import com.adva.nlms.pd.inventory.model.type.PDTopologyAidMoReference;
import com.adva.nlms.pd.inventory.mofacade.PDMoReferenceHelper;
import com.adva.nlms.pd.inventory.mofacade.modto.statemachine.PDStateMachineBeanContainer;
import com.adva.nlms.pd.inventory.mofacade.notfhelpers.PDServiceCreateNotificationHandler;
import com.adva.nlms.pd.inventory.mofacade.resync.PDAlarmResyncHelper;
import com.adva.nlms.pd.inventory.momediation.dto.service.PDServiceMoDTO;
import com.adva.nlms.pd.inventory.momediation.proc.PDTopologyMoFacade;
import com.adva.nlms.pd.inventory.service.intent.PDEthServiceIntentNotifier;
import com.adva.nlms.pd.inventory.service.intent.PDServiceIntentDAO;
import com.adva.nlms.pd.inventory.service.intent.facade.PDServiceIntentFacade;
import com.adva.nlms.pd.inventory.service.intent.implementation.db.PDEthServiceIntentDBImpl;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.List;
import java.util.LinkedList;
import java.util.HashMap;
import java.util.Set;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.stream.Collectors;

import static com.adva.nlms.mediation.event.message.MessageManager.Ok;
import static com.adva.nlms.mediation.event.message.MessageManager.NO_LOGFILE;

@Component
public class PDServiceDiscoveryHelper {

    private static final Logger LOGGER = LogManager.getLogger(PDServiceDiscoveryHelper.class);

    private final PDMoReferenceHelper moReferenceHelper;
    private final PDServiceDiscoveryEntitiesMapper pdServiceDiscoveryEntitiesMapper;
    private final PDStateMachineBeanContainer pdStateMachineBeanContainer;
    private final MessageManager messageManager;
    private final PDAlarmResyncHelper alarmResyncHelper;
    private final PDTopologyMoFacade pdTopologyMoFacade;
    private final CustomerDAO customerDAO;
    private final PDServiceDiscoveryTopologyResyncFacade pdServiceDiscoveryTopologyResyncFacade;
    private final PDEthServiceIntentNotifier ethServiceIntentNotifier;
    private final SecurityCtrl securityCTRL;
    private final PDServiceIntentDAO serviceIntentDAO;
    private final PDServiceManagementFacade pdServiceManagementFacade;
    private final EventOperations eventOperations;
    private final ProvisioningFacadeAPI provisioningFacade;

    private final PDTopologyElementDAO topologyElementDAO;
    private final ManagedObjectDAO moDAO;

    private static final String UNI_PATTERN = "^E^";

    @Autowired
    public PDServiceDiscoveryHelper(PDMoReferenceHelper moReferenceHelper,
                                    PDServiceDiscoveryEntitiesMapper pdServiceDiscoveryEntitiesMapper,
                                    PDStateMachineBeanContainer pdStateMachineBeanContainer,
                                    MessageManager messageManager,
                                    PDAlarmResyncHelper alarmResyncHelper,
                                    PDTopologyMoFacade pdTopologyMoFacade,
                                    CustomerDAO customerDAO,
                                    PDServiceDiscoveryTopologyResyncFacade pdServiceDiscoveryTopologyResyncFacade,
                                    PDEthServiceIntentNotifier ethServiceIntentNotifier,
                                    SecurityCtrl securityCTRL,
                                    PDServiceIntentDAO serviceIntentDAO,
                                    PDServiceManagementFacade pdServiceManagementFacade,
                                    EventOperations eventOperations,
                                    ProvisioningFacadeAPI provisioningFacade) {
        this.moReferenceHelper = moReferenceHelper;
        this.pdServiceDiscoveryEntitiesMapper = pdServiceDiscoveryEntitiesMapper;
        this.pdStateMachineBeanContainer = pdStateMachineBeanContainer;
        this.messageManager = messageManager;
        this.alarmResyncHelper = alarmResyncHelper;
        this.pdTopologyMoFacade = pdTopologyMoFacade;
        this.pdServiceManagementFacade = pdServiceManagementFacade;
        this.customerDAO = customerDAO;
        this.pdServiceDiscoveryTopologyResyncFacade = pdServiceDiscoveryTopologyResyncFacade;
        this.ethServiceIntentNotifier = ethServiceIntentNotifier;
        this.securityCTRL = securityCTRL;
        this.serviceIntentDAO = serviceIntentDAO;
        this.eventOperations = eventOperations;
        this.provisioningFacade = provisioningFacade;
        this.topologyElementDAO = PDTopologyElementDAO.getInstance();
        this.moDAO = ManagedObjectDAO.getInstance();
    }

    public void findFlowsByServiceTypeAndName(PDDiscoveredEntitiesDTO pdDiscoveredEntitiesDTO) {
        try {
            String serviceType = pdDiscoveredEntitiesDTO.getServiceType();
            String serviceName = pdDiscoveredEntitiesDTO.getServiceName();

            PDDiscoveredServiceType pdDiscoveredServiceType = PDDiscoveredServiceType.fromServiceType(serviceType);

            List<ManagedObjectDBImpl> flowList = getAllFlows(serviceName);
            List<FlowDTO> flowDTOS = new LinkedList<>();

            PDEntityCountHolder pdEntityCountHolder = new PDEntityCountHolder();
            for (ManagedObjectDBImpl flows : flowList) {
                List<PDConnectionDBImpl> pdConnectionDBS
                        = topologyElementDAO.getReferencedPDObjects(flows.getNeID(), flows.getShortDescription(),
                        PDConnectionDBImpl.class);
                mapEntitiesToDto(pdConnectionDBS, flowDTOS, flows.getNeID(), pdDiscoveredServiceType,
                        pdEntityCountHolder, serviceName, flows.getEntityAlias());
            }

            // If FlowDTOs are present, then handle ACCESS FLOW device case
            // Also perform final validation for ELINE service type
            if(!flowDTOS.isEmpty()) {
                handleSingleAccessFlowDeviceCase(pdEntityCountHolder, flowDTOS);
                runValidationForSearch(flowDTOS, pdDiscoveredServiceType, pdEntityCountHolder);
            }

            // If Flow DTOs are still not empty, it means we have valid flows for the service type
            if (!flowDTOS.isEmpty()) {
                sortPdDiscoveryEntities(flowDTOS, pdDiscoveredServiceType);
                pdDiscoveredEntitiesDTO.getFlowDTOS().addAll(flowDTOS);
            }
        } catch (Exception e) {
            LOGGER.error("Error while finding flows for service type: {} and service name: {}. Exception: {}",
                    pdDiscoveredEntitiesDTO.getServiceType(), pdDiscoveredEntitiesDTO.getServiceName(),
                    e.getMessage(), e);
        }
    }

    private void runValidationForSearch(List<FlowDTO> flowDTOS,
                                        PDDiscoveredServiceType pdDiscoveredServiceType,
                                        PDEntityCountHolder pdEntityCountHolder) {
        flowValidation(flowDTOS);
        if (!flowDTOS.isEmpty() && PDDiscoveredServiceType.ELINE.equals(pdDiscoveredServiceType)) {
            finalValidationForEline(flowDTOS, pdEntityCountHolder);
        }
    }

    private void flowValidation(List<FlowDTO> flowDTOS) {
        boolean isInterfaceCountLessThanTwo = false;
        boolean isUNIFPCountMoreThanOne = false;
        for(FlowDTO flow: flowDTOS) {
            if (flow.getPortDTOS().size()<2) {
                isInterfaceCountLessThanTwo = true;
                break;
            }
            for (PortDTO portDTO: flow.getPortDTOS()) {
                if(EthEntityType.UNI.equals(portDTO.getEthEntityType()) && portDTO.getFlowPointDtos().size() != 1){
                    isUNIFPCountMoreThanOne = true;
                    break;
                }
            }
        }

        if (isInterfaceCountLessThanTwo || isUNIFPCountMoreThanOne) flowDTOS.clear();
    }

    private void handleSingleAccessFlowDeviceCase(PDEntityCountHolder pdEntityCountHolder,
                                                  List<FlowDTO> flowDTOS) {
        if(pdEntityCountHolder.totalFlowCount == 1
                && flowDTOS.get(0).isEndPoint()
                && flowDTOS.get(0).getPortDTOS().get(0).getFlowPointDtos().get(0).isAccessFlow()) {
            flowDTOS.get(0).getPortDTOS().stream()
                    .filter(portDTO -> EthEntityType.INNI.equals(portDTO.getEthEntityType()))
                    .forEach(portDTO -> {
                        portDTO.setEthEntityType(EthEntityType.UNI);
                        portDTO.getFlowPointDtos()
                                .forEach(flowPointDto -> flowPointDto.setEthEntityType(EthEntityType.UNI));
                        pdEntityCountHolder.totalUniCount++;
                        pdEntityCountHolder.totalInniCount--;
                    });
        }
    }

    private void mapEntitiesToDto(List<PDConnectionDBImpl> pdConnectionDBS,
                                  List<FlowDTO> flowDTOS,
                                  int neId,
                                  PDDiscoveredServiceType pdDiscoveredServiceType,
                                  PDEntityCountHolder pdEntityCountHolder,
                                  String serviceName,
                                  String flowAlias) {
        for (PDConnectionDBImpl pdConnectionDB : pdConnectionDBS) {
            boolean isAccessFlow = isAccessFlow(pdConnectionDB);

            if(!serviceName.equals(flowAlias) && !isAccessFlow)
                return;

            List<PDTopologyElementDBImpl> evpn = topologyElementDAO.getElementByProperty(PDTEPropertyKey.ASSOCIATED_MP_FLOW,
                    pdConnectionDB.getId());
            if(evpn.isEmpty()) {
                List<PDMtpConnPointEntryDBImpl> connectionPointEntries = pdConnectionDB.getConnectionPointEntries();
                if (checkForMinimumTwoInterfaces(connectionPointEntries))
                    continue;
                Map<Integer, PortDTO> portDTOMap = new HashMap<>();
                FlowDTO flowDTO = new FlowDTO();
                pdServiceDiscoveryEntitiesMapper.mapConnectionToFlowDto(pdConnectionDB, flowDTO, neId);
                pdEntityCountHolder.totalFlowCount++;

                if(isAccessFlow && flowAlias.contains(UNI_PATTERN)) {
                    flowDTO.setEndPoint(true);
                }

                mapFPAndPortDTO(neId, pdEntityCountHolder, connectionPointEntries, flowDTO, portDTOMap, isAccessFlow);
                incrementFlowCount(pdEntityCountHolder, flowDTO);
                if (validateMaxUniForEline(flowDTOS, pdDiscoveredServiceType, pdEntityCountHolder))
                    return;
                flowDTOS.add(flowDTO);
            }
        }
    }

    private boolean checkForMinimumTwoInterfaces(List<PDMtpConnPointEntryDBImpl> connectionPointEntries) {
        int totalPort = connectionPointEntries.stream()
                            .map(cps -> (PDConnectionPointDBImpl) cps.getConnectionPoint().parent())
                            .collect(Collectors.toSet())
                            .size();
        // If there are less than 2 ports, skip this connection
        return totalPort < 2;
    }

    private static void incrementFlowCount(PDEntityCountHolder pdEntityCountHolder, FlowDTO flowDTO) {
        if(flowDTO.isEndPoint()) {
            pdEntityCountHolder.totalEndPointFlowCount++;
        } else {
            pdEntityCountHolder.totalIntermediateFlowCount++;
        }
    }

    private static boolean validateMaxUniForEline(List<FlowDTO> flowDTOS,
                                                  PDDiscoveredServiceType pdDiscoveredServiceType,
                                                  PDEntityCountHolder pdEntityCountHolder) {
        // At any point if we have more than 2 UNI ports in ELINE service, it is invalid service
        if(PDDiscoveredServiceType.ELINE.equals(pdDiscoveredServiceType)
                && (pdEntityCountHolder.totalEndPointFlowCount > 2
                        || pdEntityCountHolder.totalUniCount > 2)) {
            flowDTOS.clear();
            return true;
        }
        return false;
    }

    private void mapFPAndPortDTO(int neId, PDEntityCountHolder pdEntityCountHolder,
                                 List<PDMtpConnPointEntryDBImpl> connectionPointEntries,
                                 FlowDTO flowDTO,
                                 Map<Integer, PortDTO> portDTOMap,
                                 boolean isAccessFlow) {
        for (PDMtpConnPointEntryDBImpl cps : connectionPointEntries) {
            PDConnectionPointDBImpl connectionPoint = cps.getConnectionPoint();
            EthEntityType ethEntityType = EthEntityType.INNI;
            if(isAccessFlow) {
                if(flowDTO.isEndPoint()) {
                    ethEntityType = EthEntityType.UNI;
                }
            } else {
                PDTopologyAidMoReference ref = connectionPoint.getAdminStateMoRef();
                if (ref != null) {
                    @SuppressWarnings("unchecked")
                    ManagedObjectDBImpl moDB = moDAO.get(ref.getEntityType(), ref.getNeID(), ref.getAid());
                    if (moDB != null) {
                        String alias = moDB.getEntityAlias();
                        if (alias != null && alias.contains(UNI_PATTERN)) {
                            ethEntityType = EthEntityType.UNI;
                        }
                    }
                }
            }
            FlowPointDto flowPointDto = new FlowPointDto();
            flowPointDto.setAccessFlow(isAccessFlow);
            pdServiceDiscoveryEntitiesMapper.mapConnectionPointToFlowpointDto(connectionPoint, flowPointDto,
                    ethEntityType, neId);
            PortDTO portDTO = getAssociatedPortAndMapLAGPort(connectionPoint, flowDTO, flowPointDto, portDTOMap,
                    ethEntityType, neId);
            portDTO.getFlowPointDtos().add(flowPointDto);
            if(EthEntityType.UNI.equals(flowPointDto.getEthEntityType())) {
                flowDTO.setEndPoint(true);
                pdEntityCountHolder.totalUniCount++;
            } else {
                pdEntityCountHolder.totalInniCount++;
            }
        }
    }

    private void finalValidationForEline(List<FlowDTO> flowDTOS,
                                         PDEntityCountHolder pdEntityCountHolder) {
        // If 2 UNI are not present in ELINE, it is invalid service
        if(pdEntityCountHolder.totalUniCount != 2) {
            flowDTOS.clear();
            return;
        }

        // If all flows are intermediate, it is invalid service
        if (pdEntityCountHolder.totalFlowCount == pdEntityCountHolder.totalIntermediateFlowCount) {
            flowDTOS.clear();
            return;
        }

        // If we have only one flow, and it is not an endpoint flow, it is invalid service
        if (pdEntityCountHolder.totalFlowCount == 1 && (pdEntityCountHolder.totalEndPointFlowCount != 1
                || pdEntityCountHolder.totalInniCount != 0)) {
            flowDTOS.clear();
            return;
        }

        // If we have more than one flow and only one endpoint flow, it is invalid service
        if (pdEntityCountHolder.totalFlowCount > 1 && pdEntityCountHolder.totalEndPointFlowCount < 2) {
            flowDTOS.clear();
        }
    }

    private PortDTO getAssociatedPortAndMapLAGPort(PDConnectionPointDBImpl cp,
                                                   FlowDTO flowDTO,
                                                   FlowPointDto flowPointDto,
                                                   Map<Integer, PortDTO> portDTOMap,
                                                   EthEntityType ethEntityType,
                                                   int neId) {
        PDConnectionPointDBImpl port = (PDConnectionPointDBImpl) cp.parent();
        PortDTO portDTO = addPortDtoToFlowDto(port, flowPointDto, flowDTO, neId, portDTOMap,
                            ethEntityType, false);
        Set<PDConnectionPointDBImpl> lagMembers = moReferenceHelper.getLagMembers(port);
        if (!lagMembers.isEmpty()) {
            portDTO.setLagPort(true);
            portDTO.setType("LAG");
            lagMembers.forEach(lagMember -> addPortDtoToFlowDto(lagMember, flowPointDto, flowDTO, neId, portDTOMap,
                    ethEntityType, true));
        }
        return portDTO;
    }

    private PortDTO addPortDtoToFlowDto(PDTopologyElementDBImpl port,
                                        FlowPointDto flowPointDto,
                                        FlowDTO flowDTO,
                                        int neId,
                                        Map<Integer, PortDTO> portDTOMap,
                                        EthEntityType ethEntityType,
                                        boolean isLagPortMember) {
        PortDTO portDTO;
        if (!portDTOMap.containsKey(port.getId())) {
            portDTO = new PortDTO();
            pdServiceDiscoveryEntitiesMapper.mapPortToPortDTO(port, neId, portDTO, ethEntityType);
            portDTO.setLagPortMember(isLagPortMember);
            if(flowPointDto.isAccessFlow()) {
                handleAccessAndNetworkPort(port, flowPointDto, portDTO);
            }
            flowDTO.getPortDTOS().add(portDTO);
            portDTOMap.put(port.getId(), portDTO);
        } else {
            portDTO = portDTOMap.get(port.getId());
        }

        return portDTO;
    }

    private void handleAccessAndNetworkPort(PDTopologyElementDBImpl port,
                                            FlowPointDto flowPointDto,
                                            PortDTO portDTO) {
        if (port.getPropertyOrNull(PDTEPropertyKey.CATEGORY) instanceof PDConnectionPointCategory portType) {
            if(EthEntityType.UNI.equals(flowPointDto.getEthEntityType())) {
                if (PDConnectionPointCategory.ACCESS.equals(portType)) {
                    portDTO.setEthEntityType(EthEntityType.UNI);
                } else if (PDConnectionPointCategory.NETWORK.equals(portType)) {
                    portDTO.setEthEntityType(EthEntityType.INNI);
                    flowPointDto.setEthEntityType(EthEntityType.INNI);
                }
            } else {
                portDTO.setEthEntityType(EthEntityType.INNI);
            }
        }
    }

    // ToRevisit: Define own API in Mediation to get all flows by alias
    private List<ManagedObjectDBImpl> getAllFlows(String alias) {

        List<ManagedObjectDBImpl> flows = new ArrayList<>(moDAO.getManagedObjectsByAlias(alias, FlowF4DBImpl.class));

        getFlowsForF3(alias, flows);
        return flows.stream().distinct().toList();
    }

    private void getFlowsForF3(String alias, List<ManagedObjectDBImpl> flows) {
        List<Class<? extends ManagedObjectDBImpl>> flowClasses = List.of(
                FlowF3DBImpl.class,
                ElineFlowF3DBImpl.class,
                MPFlowF3DBImpl.class
        );

        for (Class<? extends ManagedObjectDBImpl> flowClass : flowClasses) {
            flows.addAll(moDAO.getManagedObjectsByAlias(alias, flowClass));
        }
        for (Class<? extends ManagedObjectDBImpl> flowClass : flowClasses) {
            flows.addAll(moDAO.getManagedObjectsByAliasLike(alias + UNI_PATTERN, flowClass));
        }
        for (Class<? extends ManagedObjectDBImpl> flowClass : flowClasses) {
            flows.addAll(moDAO.getManagedObjectsByAliasLike(alias + "^", flowClass));
        }
    }

    protected boolean isAccessFlow(PDConnectionDBImpl crs) {
        return crs.getPropertyOrNull(PDTEPropertyKey.ACCESS_FLOW_MODULE) != null;
    }

    private void sortPdDiscoveryEntities(List<FlowDTO> flowDTOS,
                                         PDDiscoveredServiceType pdDiscoveredServiceType) {
        if(PDDiscoveredServiceType.ELINE.equals(pdDiscoveredServiceType)) {
            sortPdDiscoveryEntitiesForEline(flowDTOS);
        }

        if(PDDiscoveredServiceType.ELAN.equals(pdDiscoveredServiceType)) {
            sortPdDiscoveryEntitiesForElan(flowDTOS);
        }
    }

    private void sortPdDiscoveryEntitiesForElan(List<FlowDTO> flowDTOS) {
        sortFlowBasedOnNameOfNe(flowDTOS);
        PDServiceDiscoveryComparator pdServiceDiscoveryComparator = new PDServiceDiscoveryComparator();
        final Comparator<PortDTO> finalComparator = pdServiceDiscoveryComparator.getPortDTOComparator();
        flowDTOS.forEach(flowDTO -> flowDTO.getPortDTOS().sort(finalComparator));
    }

    private void sortPdDiscoveryEntitiesForEline(List<FlowDTO> flowDTOS) {
        Map<Boolean, List<FlowDTO>> partitioned = flowDTOS.stream()
                .collect(Collectors.partitioningBy(FlowDTO::isEndPoint));
        List<FlowDTO> endpointFlowDTOs = partitioned.get(true);
        List<FlowDTO> nonEndpointFlowDTOs = partitioned.get(false);

        int endpointCount = endpointFlowDTOs.size();

        sortFlowBasedOnNameOfNe(endpointFlowDTOs);
        sortFlowBasedOnNameOfNe(nonEndpointFlowDTOs);

        PDServiceDiscoveryComparator pdServiceDiscoveryComparator = new PDServiceDiscoveryComparator();
        final Comparator<PortDTO> finalComparator = pdServiceDiscoveryComparator.getPortDTOComparator();
        final Comparator<PortDTO> finalReverseComparator = pdServiceDiscoveryComparator.getPortDTOTypeReverseComparator();
        endpointFlowDTOs.get(0).getPortDTOS().sort(finalComparator);
        if(endpointCount > 1)
            endpointFlowDTOs.get(1).getPortDTOS().sort(finalReverseComparator);
        nonEndpointFlowDTOs.forEach(flowDTO -> flowDTO.getPortDTOS().sort(finalComparator));
        flowDTOS.clear();
        flowDTOS.add(endpointFlowDTOs.get(0));
        flowDTOS.addAll(nonEndpointFlowDTOs);
        if(endpointCount > 1)
            flowDTOS.add(endpointFlowDTOs.get(1));
    }

    private void sortFlowBasedOnNameOfNe(List<FlowDTO> flowDTOS) {
        flowDTOS.sort(Comparator.comparing(flow -> flow.getNetworkElementDTO().displayName));
    }

    public void validate(String serviceName) throws PDServiceDiscoveryException {
        PDServiceIntentFacade pdServiceIntentFacade = pdStateMachineBeanContainer.getPdServiceIntentFacade();
        boolean anyMatch = pdServiceIntentFacade.getAllServiceIntents().stream()
                .filter(intentDB -> intentDB.getName() != null)
                .anyMatch(x -> x.getName().equals(serviceName));
        if (anyMatch) {
            messageManager.addMessage(NO_LOGFILE, OperationProgressAttributes.PACKET_DIRECTOR_QUEUE_NAME,
                    this, MessageManager.Error, serviceName + " service Already Exists ");
            throw new PDServiceDiscoveryException("Service Already Exists");
        }
    }

    @MDTransactional
    @MDPersistenceContext
    public void savePdDiscoveredService(ServiceIdentificationDto dto,
                                        String serviceType) throws PDServiceDiscoveryException {
        PDDiscoveredServiceType pdDiscoveredServiceType = PDDiscoveredServiceType
                .fromServiceType(serviceType);

        // Find entities for the service name
        PDDiscoveredEntitiesDTO pdDiscoveredEntitiesDTO = new PDDiscoveredEntitiesDTO(dto.getServiceName(),
                serviceType);
        findFlowsByServiceTypeAndName(pdDiscoveredEntitiesDTO);
        if (pdDiscoveredEntitiesDTO.getFlowDTOS().isEmpty()) {
            throw new PDServiceDiscoveryException("No flows found for service type: " + serviceType
                    + " and service name: " + dto.getServiceName());
        }

        // Create service intent and get the intent ID
        int serviceIntentId = createServiceIntent(dto, pdDiscoveredServiceType);

        // Create service DTO and populate it with the discovered entities
        PDDiscoveredServiceDTO pdDiscoveredServiceDTO
                = createServiceDto(dto, serviceIntentId, pdDiscoveredEntitiesDTO);

        // Create Service Mo DTO to be synced in DB
        PDServiceMoDTO serviceMoDTO = pdTopologyMoFacade.createDataService(pdDiscoveredServiceDTO.getConnectionPointSequence(),
                pdDiscoveredServiceDTO.getConnectionSequence(),
                pdDiscoveredServiceDTO.getServiceEndpointsSequence(),
                serviceIntentId);
        serviceMoDTO.addProperty(PDTEPropertyKey.SETUPMODE, Definition.ServiceCreationMode.DISCOVERED_MODE);
        serviceMoDTO.addProperty(PDTEPropertyKey.ML_NATIVE_SERVICE, true);
        serviceMoDTO.addProperty(PDTEPropertyKey.ML_INTENT_ID, serviceIntentId);


        // Create Service in DB
        PDServiceDBImpl pdServiceDB = pdServiceDiscoveryTopologyResyncFacade.getServiceResyncHelper(serviceMoDTO)
                .resyncService(serviceMoDTO);

        if(pdServiceDB != null) {
            String message = String.format("%s Service Created: %s ", pdDiscoveredServiceType.getDesc(),
                    pdServiceDB.getLabel());
            messageManager.addMessage(NO_LOGFILE, OperationProgressAttributes.PACKET_DIRECTOR_QUEUE_NAME,
                    this, Ok, message);
            alarmResyncHelper.notifyCreated(pdServiceDB, serviceMoDTO);
            resyncIntent(pdServiceDB, pdDiscoveredEntitiesDTO);
        } else {
            String message = String.format("%s Service Creation Failed: %s ", pdDiscoveredServiceType.getDesc(),
                    serviceMoDTO.getLabel());
            messageManager.addMessage(NO_LOGFILE, OperationProgressAttributes.PACKET_DIRECTOR_QUEUE_NAME,
                    this, MessageManager.Error, message);
        }
    }

    protected void resyncIntent(PDServiceDBImpl serviceDB, PDDiscoveredEntitiesDTO pdDiscoveredEntitiesDTO) {
        PDEthServiceIntentDBImpl serviceIntentDB = BeanProvider.get()
                .getBean(PDServiceIntentDAO.class).getIntentById(serviceDB.getServiceIntent());
        updateServiceIntent(serviceIntentDB.getId(), serviceDB, pdDiscoveredEntitiesDTO);
        ethServiceIntentNotifier.notifyTopologyChange(serviceIntentDB, ConfigChangeType.CHANGED);
    }

    protected void updateServiceIntent(int intentId,
                                       PDServiceDBImpl pdServiceDB,
                                       PDDiscoveredEntitiesDTO pdDiscoveredEntitiesDTO) {
        if (pdServiceDB != null) {
            PDServiceIntentFacade pdServiceIntentFacade = pdStateMachineBeanContainer.getPdServiceIntentFacade();
            pdServiceIntentFacade.updateServiceIntentState(intentId,
                    MLServiceLifeCycleStateEnum.INSTALLED,
                    pdDiscoveredEntitiesDTO);
            attachServiceToCustomerAndServiceGroup(pdServiceDB.getId(), pdServiceDB.getServiceName());
        }
    }

    private void attachServiceToCustomerAndServiceGroup(int pdServiceId, String serviceName) {
        PDServiceCreateNotificationHandler serviceCreateNotificationHandler = new PDServiceCreateNotificationHandler(
                topologyElementDAO, alarmResyncHelper, securityCTRL, pdServiceManagementFacade,
                pdServiceId, serviceName, serviceIntentDAO, eventOperations);
        serviceCreateNotificationHandler.handle();
    }

    private int createServiceIntent(ServiceIdentificationDto dto,
                                    PDDiscoveredServiceType pdDiscoveredServiceType) {
        PDServiceIntentFacade pdServiceIntentFacade = pdStateMachineBeanContainer.getPdServiceIntentFacade();
        return pdServiceIntentFacade.createServiceIntentForPdDiscoveredServices(dto, pdDiscoveredServiceType);
    }

    private PDDiscoveredServiceDTO createServiceDto(ServiceIdentificationDto dto,
                                                    int intentId,
                                                    PDDiscoveredEntitiesDTO pdDiscoveredEntitiesDTO) {
        PDDiscoveredServiceDTO serviceDTO = new PDDiscoveredServiceDTO();
        serviceDTO.setServiceIntent(intentId);
        mapIdentificationDTOToServiceDTo(dto, serviceDTO);
        populateServiceEntities(serviceDTO, pdDiscoveredEntitiesDTO);
        return serviceDTO;
    }

    private void mapIdentificationDTOToServiceDTo(ServiceIdentificationDto dto,
                                                  PDDiscoveredServiceDTO serviceDTO) {
        serviceDTO.setCustomerName(dto.getCustomerName());
        serviceDTO.setServiceGroupId(dto.getGroupId());
        serviceDTO.setRemarks(dto.getComments());
        serviceDTO.setCustomerId(getCustomer(dto).getId());
    }

    private void populateServiceEntities(PDDiscoveredServiceDTO serviceDTO,
                                         PDDiscoveredEntitiesDTO pdDiscoveredEntitiesDTO) {
        PDDiscoveredServiceType pdDiscoveredServiceType = PDDiscoveredServiceType
                .fromServiceType(pdDiscoveredEntitiesDTO.getServiceType());
        List<Integer> connectionPointSequence = new LinkedList<>();
        List<Integer> connectionSequence = new LinkedList<>();
        List<Integer> serviceEndPointsSequence = new LinkedList<>();
        if(PDDiscoveredServiceType.ELAN.equals(pdDiscoveredServiceType)) {
            populateServiceEntitiesForElan(connectionPointSequence, connectionSequence, serviceEndPointsSequence,
                    pdDiscoveredEntitiesDTO);
        } else if(PDDiscoveredServiceType.ELINE.equals(pdDiscoveredServiceType)) {
            populateServiceEntitiesForEline(connectionPointSequence, connectionSequence, serviceEndPointsSequence,
                    pdDiscoveredEntitiesDTO);
        }

        serviceDTO.setConnectionPointSequence(connectionPointSequence);
        serviceDTO.setConnectionSequence(connectionSequence);
        serviceDTO.setServiceEndpointsSequence(serviceEndPointsSequence);
    }

    private void populateServiceEntitiesForEline(List<Integer> connectionPointSequence,
                                                 List<Integer> connectionSequence,
                                                 List<Integer> serviceEndPointsSequence,
                                                 PDDiscoveredEntitiesDTO pdDiscoveredEntitiesDTO) {
        int totalFlowDtos = pdDiscoveredEntitiesDTO.getFlowDTOS().size();
        for (int flowIndex = 0; flowIndex < totalFlowDtos; flowIndex++) {
            FlowDTO flowDTO = pdDiscoveredEntitiesDTO.getFlowDTOS().get(flowIndex);
            List<PortDTO> portDTOS = flowDTO.getPortDTOS().stream()
                    .filter(portDTO -> !portDTO.isLagPortMember())
                    .toList();
            int portSize = portDTOS.size();
            List<Integer> beforeFlowPortDtos = new LinkedList<>();
            List<Integer> afterFlowPortDtos = new LinkedList<>();
            List<Integer> beforeFlowFPDtos = new LinkedList<>();
            List<Integer> afterFlowFPDtos = new LinkedList<>();

            int index = 0;
            while (index < portSize) {
                PortDTO portDTO = portDTOS.get(index);
                boolean addPort = canAddPort(flowIndex, index, totalFlowDtos, portSize);
                if (isFirstPortOrLastInni(index, portDTO, flowIndex, totalFlowDtos)) {
                    addPortAndFpForEline(addPort, beforeFlowPortDtos, portDTO, beforeFlowFPDtos);
                } else {
                    addPortAndFpForEline(addPort, afterFlowPortDtos, portDTO, afterFlowFPDtos);
                }
                index++;
            }

            connectionPointSequence.addAll(beforeFlowPortDtos);
            connectionPointSequence.addAll(beforeFlowFPDtos);
            connectionSequence.add(flowDTO.getId());
            connectionPointSequence.addAll(afterFlowFPDtos);
            connectionPointSequence.addAll(afterFlowPortDtos);

            if (flowDTO.isEndPoint()) {
                serviceEndPointsSequence.addAll(beforeFlowFPDtos);
                serviceEndPointsSequence.addAll(afterFlowFPDtos);
            }

        }
    }

    private boolean canAddPort(int flowIndex, int index, int totalFlowDtos, int portSize) {
        return !(flowIndex == 0 && index == 0)
                && !(flowIndex == totalFlowDtos - 1 && index == portSize - 1);
    }

    private boolean isFirstPortOrLastInni(int index, PortDTO portDTO, int flowIndex, int totalFlowDtos) {
        return index == 0
                || (EthEntityType.INNI.equals(portDTO.getEthEntityType()) && flowIndex == totalFlowDtos - 1);
    }

    private void addPortAndFpForEline(boolean addPort,
                                      List<Integer> flowPortDtos,
                                      PortDTO portDTO,
                                      List<Integer> flowFPDtos) {
        if (addPort)
            flowPortDtos.add(portDTO.getId());
        for (FlowPointDto flowPointDto : portDTO.getFlowPointDtos()) {
            flowFPDtos.add(flowPointDto.getId());
        }
    }

    private void populateServiceEntitiesForElan(List<Integer> connectionPointSequence,
                                                List<Integer> connectionSequence,
                                                List<Integer> serviceEndPointsSequence,
                                                PDDiscoveredEntitiesDTO pdDiscoveredEntitiesDTO) {
        for (FlowDTO flowDTO : pdDiscoveredEntitiesDTO.getFlowDTOS()) {
            List<Integer> uniPortDtos = new LinkedList<>();
            List<Integer> inniPortDtos = new LinkedList<>();
            List<Integer> uniFlowPointDtos = new LinkedList<>();
            List<Integer> inniFlowPointDtos = new LinkedList<>();
            // Extracting Port and FlowPointDto information from each FlowDTO
            for (PortDTO portDTO : flowDTO.getPortDTOS()) {
                if(portDTO.isLagPortMember())
                    continue;
                if(EthEntityType.UNI.equals(portDTO.getEthEntityType())) {
                    uniPortDtos.add(portDTO.getId());
                } else {
                    inniPortDtos.add(portDTO.getId());
                }
                for (FlowPointDto flowPointDto : portDTO.getFlowPointDtos()) {
                    if (EthEntityType.UNI.equals(flowPointDto.getEthEntityType())) {
                        uniFlowPointDtos.add(flowPointDto.getId());
                    } else {
                        inniFlowPointDtos.add(flowPointDto.getId());
                    }
                }
            }
            if(flowDTO.isEndPoint()) {
                connectionPointSequence.addAll(uniPortDtos);
                connectionPointSequence.addAll(uniFlowPointDtos);
                connectionSequence.add(flowDTO.getId());
                connectionPointSequence.addAll(inniFlowPointDtos);
                connectionPointSequence.addAll(inniPortDtos);

                serviceEndPointsSequence.addAll(uniFlowPointDtos);
            } else {
                connectionPointSequence.addAll(inniPortDtos);
                connectionPointSequence.addAll(inniFlowPointDtos);
                connectionSequence.add(flowDTO.getId());
            }
        }
    }

    private CustomerDBImpl getCustomer(ServiceIdentificationDto dto) {
        return customerDAO.getCustomerByNameLike(dto.getCustomerName());
    }

    @MDTransactional
    @MDPersistenceContext
    public void removePDDiscoveredService(int serviceIntentId) throws PDServiceOperationException {

        PDEthServiceIntentDBImpl intentDb = serviceIntentDAO.getIntentById(serviceIntentId);
        if(intentDb == null) {
            LOGGER.error("Service Intent Not Found with id = {}", serviceIntentId);
            String message = String.format("Service Intent Not Found with id = %s", serviceIntentId);
            messageManager.addMessage(NO_LOGFILE, OperationProgressAttributes.PACKET_DIRECTOR_QUEUE_NAME,
                    this, MessageManager.Error, message);
            return;
        }
        if (provisioningFacade.isInProgress(intentDb.getName())) {
            throw new PDServiceOperationException(provisioningFacade.getInProgressErrorMessage(intentDb.getName()),
                    new IllegalArgumentException());
        }

        PDServiceDBImpl serviceDB;
        int serviceId;
        try {
            List<PDServiceDBImpl> serviceDBList = topologyElementDAO.getServiceForServiceIntent(serviceIntentId);
            if(serviceDBList != null && !serviceDBList.isEmpty()) {
                serviceDB = serviceDBList.get(0);
                serviceId = serviceDB.getId();
            } else {
                LOGGER.error("Discovered Service Not Found with intent id = {}", serviceIntentId);
                String message = String.format("Discovered Service Not Found with intent id = %s", serviceIntentId);
                messageManager.addMessage(NO_LOGFILE, OperationProgressAttributes.PACKET_DIRECTOR_QUEUE_NAME,
                        this, MessageManager.Error, message);
                return;
            }
        } catch (Exception e) {
            LOGGER.error("Error while fetching Discovered Service with intent id = {}. Error: {}",
                    serviceIntentId, e.getMessage());
            String message = String.format("Error while fetching Discovered Service with intent id = %s", serviceIntentId);
            messageManager.addMessage(NO_LOGFILE, OperationProgressAttributes.PACKET_DIRECTOR_QUEUE_NAME,
                    this, MessageManager.Error, message);
            return;
        }
        if(serviceDB instanceof PDElanServiceDBImpl || serviceDB instanceof PDTrailDBImpl) {
            PDDiscoveredServiceType pdDiscoveredServiceType = serviceDB instanceof PDTrailDBImpl
                    ? PDDiscoveredServiceType.ELINE
                    : PDDiscoveredServiceType.ELAN;
            try {
                boolean isSuccess = pdServiceDiscoveryTopologyResyncFacade.getServiceResyncHelper(pdDiscoveredServiceType)
                                        .destroyService(serviceDB, pdDiscoveredServiceType);
                if(isSuccess) {
                    String message = String.format("%s Service with name %s deleted", pdDiscoveredServiceType.getDesc(),
                            serviceDB.getLabel());
                    messageManager.addMessage(NO_LOGFILE, OperationProgressAttributes.PACKET_DIRECTOR_QUEUE_NAME,
                            this, Ok, message);
                    alarmResyncHelper.notifyDeleted(serviceDB);
                }
            } catch (PDServiceDiscoveryException e) {
                LOGGER.error("Failed to remove {} Service with id = {}. Error: {}",
                        pdDiscoveredServiceType.getDesc(), serviceId, e.getMessage());
                String message = String.format("%s Service with name %s deletion failed", pdDiscoveredServiceType.getDesc(),
                        serviceDB.getLabel());
                messageManager.addMessage(NO_LOGFILE, OperationProgressAttributes.PACKET_DIRECTOR_QUEUE_NAME,
                        this, MessageManager.Error, message);
            }
        } else {
            LOGGER.error("Discovered Service Not Found with id = {}", serviceId);
        }
    }

    private static class PDEntityCountHolder {
        int totalUniCount = 0;
        int totalInniCount = 0;
        int totalEndPointFlowCount = 0;
        int totalIntermediateFlowCount = 0;
        int totalFlowCount = 0;
    }

}
