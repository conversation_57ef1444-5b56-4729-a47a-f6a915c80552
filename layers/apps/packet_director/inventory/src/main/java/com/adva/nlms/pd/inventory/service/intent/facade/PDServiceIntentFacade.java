/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 */
package com.adva.nlms.pd.inventory.service.intent.facade;

import com.adva.ethernet.ring.api.enums.EthernetRingAssociatedObjectType;
import com.adva.ethernet.ring.api.in.EthernetRingDataHelper;
import com.adva.nlms.common.TopologyNodeType;
import com.adva.nlms.common.discover.common.dto.PDDiscoveredEntitiesDTO;
import com.adva.nlms.common.discover.evpn.dto.EvpnEntitiesDTO;
import com.adva.nlms.common.discover.common.dto.FlowDTO;
import com.adva.nlms.common.discover.common.dto.FlowPointDto;
import com.adva.nlms.common.discover.common.dto.PortDTO;
import com.adva.nlms.common.mltopologymodel.enums.MLServiceLifeCycleStateEnum;
import com.adva.nlms.common.mltopologymodel.pce.ServiceViewToServiceDefinitionConverterNew;
import com.adva.nlms.common.mltopologymodel.pce.definition.AffectedRingDto;
import com.adva.nlms.common.mltopologymodel.pce.definition.ServiceDefinitionDto;
import com.adva.nlms.common.mltopologymodel.pce.definition.ServiceIdentificationDto;
import com.adva.nlms.common.mltopologymodel.pce.definition.ServiceNodeDto;
import com.adva.nlms.common.mltopologymodel.pce.definition.ServicePointDto;
import com.adva.nlms.common.mltopologymodel.pce.definition.ServiceViewDto;
import com.adva.nlms.common.mltopologymodel.pce.layerextensions.ProvisioningLayerExtensionDTO;
import com.adva.nlms.common.snmp.MANetNameFormat;
import com.adva.nlms.common.snmp.MDNameFormat;
import com.adva.nlms.common.topology.EthEntityType;
import com.adva.nlms.mediation.bean.provider.api.BeanProvider;
import com.adva.nlms.mediation.common.persistence.MDPersistenceContext;
import com.adva.nlms.mediation.common.persistence.MDPersistenceHelper;
import com.adva.nlms.mediation.common.persistence.MDPersistenceManager;
import com.adva.nlms.mediation.common.persistence.MDTransactional;
import com.adva.nlms.mediation.config.ManagedObjectDAO;
import com.adva.nlms.mediation.config.ManagedObjectDBImpl;
import com.adva.nlms.mediation.config.f3.entity.erp.ErpGroupF3DBImpl;
import com.adva.nlms.mediation.event.message.MessageManager;
import com.adva.nlms.mediation.security.api.event.SystemAction;
import com.adva.nlms.mediation.security.api.event.SystemEventLogging;
import com.adva.nlms.mediation.sm.model.CustomerDAO;
import com.adva.nlms.mediation.topology.ethernet.ring.model.EthernetRingAssociatedObjectDBImpl;
import com.adva.nlms.mediation.topology.ethernet.ring.model.EthernetRingAssociatedObjectDBImplDAO;
import com.adva.nlms.pd.api.in.dto.AffectedRingParamDto;
import com.adva.nlms.pd.api.in.dto.enums.PDDiscoveredServiceType;
import com.adva.nlms.pd.api.in.dto.enums.ServiceIntentParamKeyTypeCommon;
import com.adva.nlms.pd.api.in.dto.enums.ServiceIntentParamKeyTypeEth;
import com.adva.nlms.pd.api.out.networkelement.NetworkElementOperations;
import com.adva.nlms.pd.api.out.networkelement.dto.NeDataDto;
import com.adva.nlms.pd.inventory.api.service.PDServiceToServiceViewDTOMapper;
import com.adva.nlms.pd.inventory.model.dao.PDTopologyElementDAO;
import com.adva.nlms.pd.inventory.model.db.PDConnectionPointDBImpl;
import com.adva.nlms.pd.inventory.model.db.PDServiceDBImpl;
import com.adva.nlms.pd.inventory.mofacade.modto.provisioning.PDServiceDefinitionToServiceIntentConverter;
import com.adva.nlms.pd.inventory.service.intent.PDServiceIntentDAO;
import com.adva.nlms.pd.inventory.service.intent.dto.EthServiceIntentParamDTO;
import com.adva.nlms.pd.inventory.service.intent.implementation.db.PDEthServiceIntentDBImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.adva.nlms.common.mltopologymodel.pce.definition.ServiceCommonSettingsDto.CUSTOM_INDEX_DEFAULT;
import static com.adva.nlms.mediation.event.message.MessageManager.NO_LOGFILE;
import static com.adva.nlms.mediation.event.message.MessageManager.Ok;
import static com.adva.nlms.pd.api.in.dto.enums.ServiceIntentParamKeyTypeEth.ETH_AFFECTED_RINGS;

public class PDServiceIntentFacade {

  private final PDTopologyElementDAO accessHelper;
  private final EthernetRingDataHelper ethernetRingDataHelper;

  public PDServiceIntentFacade(PDTopologyElementDAO accessHelper, EthernetRingDataHelper ethernetRingDataHelper) {
    this.accessHelper = accessHelper;
    this.ethernetRingDataHelper = ethernetRingDataHelper;
  }

  protected final Logger log = LogManager.getLogger(this.getClass());

  public int createServiceIntentAndGetId(ServiceDefinitionDto serviceDefinitionDto) {
    PDEthServiceIntentDBImpl serviceIntentDBObject = createServiceIntentDBObject(serviceDefinitionDto);
    PDServiceTopologyNotifier topologyNotifier = new PDServiceTopologyNotifier();
    topologyNotifier.create(serviceIntentDBObject);
    return serviceIntentDBObject.getId();
  }

  public int createServiceIntentAndGetId(ServiceIdentificationDto dto) {
    PDEthServiceIntentDBImpl serviceIntentDBObject = createServiceIntentDBObject(dto);
    PDServiceTopologyNotifier topologyNotifier = new PDServiceTopologyNotifier();
    topologyNotifier.create(serviceIntentDBObject);
    return serviceIntentDBObject.getId();
  }

  public int createServiceIntentForPdDiscoveredServices(ServiceIdentificationDto dto,
                                                        PDDiscoveredServiceType pdDiscoveredServiceType) {
    PDEthServiceIntentDBImpl serviceIntentDBObject = createServiceIntentDBObjectForPDDiscoveredServices(dto,
            pdDiscoveredServiceType);
    PDServiceTopologyNotifier topologyNotifier = new PDServiceTopologyNotifier();
    topologyNotifier.create(serviceIntentDBObject);
    return serviceIntentDBObject.getId();
  }

  @MDTransactional
  public PDEthServiceIntentDBImpl createServiceIntentDBObject(ServiceDefinitionDto serviceDefinitionDto) {
    final EntityManager em = MDPersistenceManager.current();

    NetworkElementOperations networkElementOperations = BeanProvider.get().getBean(NetworkElementOperations.class);
    CustomerDAO customerDAO = BeanProvider.get().getBean(CustomerDAO.class);
    PDServiceDefinitionToServiceIntentConverter serviceDefinitionToServiceIntentConverter =
            new PDServiceDefinitionToServiceIntentConverter(networkElementOperations, customerDAO);
    PDEthServiceIntentDBImpl serviceIntentDB = serviceDefinitionToServiceIntentConverter.convert(serviceDefinitionDto);
    em.persist(serviceIntentDB);

    return serviceIntentDB;
  }

  @MDTransactional
  public PDEthServiceIntentDBImpl createServiceIntentDBObject(ServiceIdentificationDto serviceIdentificationDto) {
    final EntityManager em = MDPersistenceManager.current();

    NetworkElementOperations networkElementOperations = BeanProvider.get().getBean(NetworkElementOperations.class);
    CustomerDAO customerDAO = BeanProvider.get().getBean(CustomerDAO.class);
    PDServiceDefinitionToServiceIntentConverter serviceDefinitionToServiceIntentConverter =
            new PDServiceDefinitionToServiceIntentConverter(networkElementOperations, customerDAO);
    PDEthServiceIntentDBImpl serviceIntentDB = serviceDefinitionToServiceIntentConverter.convert(serviceIdentificationDto);
    em.persist(serviceIntentDB);

    return serviceIntentDB;
  }

  @MDTransactional
  public PDEthServiceIntentDBImpl createServiceIntentDBObjectForPDDiscoveredServices(ServiceIdentificationDto serviceIdentificationDto,
                                                                                     PDDiscoveredServiceType pdDiscoveredServiceType) {
    final EntityManager em = MDPersistenceManager.current();

    NetworkElementOperations networkElementOperations = BeanProvider.get().getBean(NetworkElementOperations.class);
    CustomerDAO customerDAO = BeanProvider.get().getBean(CustomerDAO.class);
    PDServiceDefinitionToServiceIntentConverter serviceDefinitionToServiceIntentConverter =
            new PDServiceDefinitionToServiceIntentConverter(networkElementOperations, customerDAO);
    PDEthServiceIntentDBImpl serviceIntentDB = serviceDefinitionToServiceIntentConverter
            .convertForPDDiscoveredServices(serviceIdentificationDto, pdDiscoveredServiceType);
    em.persist(serviceIntentDB);

    return serviceIntentDB;
  }

  public void cloneServiceObject(ServiceViewDto serviceViewDto, String serviceName, String cloneName) {
    if (serviceViewDto.getExistingServiceId() != 0) {
      // Clone Provisioned - must clone the PD service since we don't keep the Intent synchronized upon modification
      PDServiceDBImpl pdServiceDB = accessHelper.getServiceByID(serviceViewDto.getExistingServiceId());
      if (pdServiceDB == null) {
        throw new IllegalArgumentException("PD Service " + serviceName + " does not exist");
      }
      PDServiceToServiceViewDTOMapper serviceToServiceViewDTOMapper = new PDServiceToServiceViewDTOMapper();
      serviceViewDto = serviceToServiceViewDTOMapper.map(pdServiceDB);
    }

    PDServiceIntentDAO serviceIntentDAO = BeanProvider.get().getBean(PDServiceIntentDAO.class);
    PDEthServiceIntentDBImpl pdEthServiceIntentDB = serviceIntentDAO.getServiceByName(serviceName);
    if (pdEthServiceIntentDB == null) {
      throw new IllegalArgumentException("Service " + serviceName + " does not exist");
    }

    ServiceDefinitionDto serviceDefinition = new ServiceViewToServiceDefinitionConverterNew().convert(serviceViewDto);
    serviceDefinition.getServiceGeneralViewDto().getServiceIdentificationDto().setServiceName(cloneName);
    serviceDefinition.getServiceGeneralViewDto().getServiceCommonSettingsDto().setAlias(cloneName);
    serviceDefinition.getServiceGeneralViewDto().getServiceCommonSettingsDto().setCustomIndex(CUSTOM_INDEX_DEFAULT);
    serviceDefinition.getServiceGeneralViewDto().getServiceCommonSettingsDto().setCustomIndexEnabled(false);

    if (serviceDefinition.getServiceGeneralViewDto().getCfmGeneralConfigurationDto().getMdNameFormat() == MDNameFormat.CHAR_STRING) {
      serviceDefinition.getServiceGeneralViewDto().getCfmGeneralConfigurationDto().setMdName("MD-" + cloneName);
    }
    if (serviceDefinition.getServiceGeneralViewDto().getCfmGeneralConfigurationDto().getMaNameFormat() == MANetNameFormat.CHAR_STRING) {
      serviceDefinition.getServiceGeneralViewDto().getCfmGeneralConfigurationDto().setMaName("MA-" + cloneName);
    }

    // Clear out all intent ids
    serviceDefinition.getServiceNodes()
            .forEach(serviceNode -> {
              serviceNode.setIntentTpId(0);
              serviceNode.getServicePointDtoList()
                      .forEach(servicePointDto -> servicePointDto.getEndpointConfigurationDto().setIntentTpId(0));
            });

    // During provisioning each ERP interface gets expanded to the underlying east/west interfaces. We need to collapse them back to the ERP interface.
    if (!serviceDefinition.getAffectedRings().isEmpty()) {
      cloneErp(serviceDefinition);
    }

    int intentId = createServiceIntentAndGetId(serviceDefinition);
    pdEthServiceIntentDB = serviceIntentDAO.getServiceByName(serviceName);

    MLServiceLifeCycleStateEnum clonedServiceLifecycleState = getMlServiceLifeCycleStateEnum(pdEthServiceIntentDB);
    updateServiceIntentState(intentId, clonedServiceLifecycleState);

    SystemEventLogging systemEventLogging = BeanProvider.get().getBean(SystemEventLogging.class);
    systemEventLogging.addPdSecurityEvent(intentId, cloneName, serviceViewDto.getServiceGeneralViewDto().getServiceIdentificationDto().getCustomerName(), null, SystemAction.AddService,
            serviceViewDto.getServiceGeneralViewDto().getServiceIdentificationDto().getCustomerName(), TopologyNodeType.PD_SERVICE_INTENT);
    String msg = "Planned service " + cloneName + " with lifecycle state " + clonedServiceLifecycleState.getDisplayValue()
            + " has been created using clone under Customer " + serviceViewDto.getServiceGeneralViewDto().getServiceIdentificationDto().getCustomerName() + ".";
    MessageManager messageManager = BeanProvider.get().getBean(MessageManager.class);
    messageManager.addMessage(NO_LOGFILE, "Mosaic Packet Director", this, Ok, msg);

    PDServiceTopologyNotifier topologyNotifier = new PDServiceTopologyNotifier();
    topologyNotifier.create(pdEthServiceIntentDB);
  }

  private void cloneErp(ServiceDefinitionDto serviceDefinition) {
    NetworkElementOperations networkElementOperations = BeanProvider.get().getBean(NetworkElementOperations.class);
    EthernetRingAssociatedObjectDBImplDAO ethernetRingAssociatedObjectDBImplDAO = BeanProvider.get().getBean(EthernetRingAssociatedObjectDBImplDAO.class);
    ManagedObjectDAO managedObjectDAO = BeanProvider.get().getBean(ManagedObjectDAO.class);

    Set<Integer> ringIds = serviceDefinition.getAffectedRings().stream()
            .map(AffectedRingDto::getRingId)
            .collect(Collectors.toSet());

    for (ServiceNodeDto serviceNode : serviceDefinition.getServiceNodes()) {
      List<ServicePointDto> updatedServicePointDtoList = new ArrayList<>();
      for (ServicePointDto servicePointDto : serviceNode.getServicePointDtoList()) {
        if (servicePointDto.getIsEndpoint()) {
          updatedServicePointDtoList.add(servicePointDto); // not a route interface
          continue;
        }
        NeDataDto neDataDto = networkElementOperations.getNeDataByIpAddress(serviceNode.getIpAddress());
        ManagedObjectDBImpl managedObjectDB = managedObjectDAO.get(neDataDto.getId(), servicePointDto.getName());
        if (managedObjectDB instanceof ErpGroupF3DBImpl) { // note this DBImpl is used for both F3 and F4
          updatedServicePointDtoList.add(servicePointDto); // Already collapsed ERP interface (Planned service)
        } else {
          try {
            EthernetRingAssociatedObjectDBImpl ethernetRingAssociatedObjectDB = ethernetRingAssociatedObjectDBImplDAO.getObjectAssociatedRingsByEntityIndex(neDataDto.getId(), managedObjectDB.getEntityIndex().toString(), ringIds);
            ServicePointDto erpInterface = mapErpToServicePointDto(ethernetRingAssociatedObjectDB, servicePointDto);
            if (updatedServicePointDtoList.stream().noneMatch(point -> point.getName().equals(erpInterface.getName()))) {
              updatedServicePointDtoList.add(erpInterface); // add collapsed ERP interface, no duplicates
            }
          } catch (NoResultException e) {
            updatedServicePointDtoList.add(servicePointDto); // normal port
          }
        }
      }
      serviceNode.setServicePointDtoList(updatedServicePointDtoList);
    }
  }

  private static MLServiceLifeCycleStateEnum getMlServiceLifeCycleStateEnum(PDEthServiceIntentDBImpl pdEthServiceIntentDB) {
    MLServiceLifeCycleStateEnum clonedServiceLifecycleState = MLServiceLifeCycleStateEnum.UNKNOWN;
    if (pdEthServiceIntentDB.getLifecycleState() == MLServiceLifeCycleStateEnum.INSTALLED || pdEthServiceIntentDB.getLifecycleState() == MLServiceLifeCycleStateEnum.PLANNED_FAILED) {
      clonedServiceLifecycleState = MLServiceLifeCycleStateEnum.PLANNED_READY;

    } else if (pdEthServiceIntentDB.getLifecycleState() == MLServiceLifeCycleStateEnum.PLANNED_INCOMPLETE || pdEthServiceIntentDB.getLifecycleState() == MLServiceLifeCycleStateEnum.PLANNED_READY) {
      clonedServiceLifecycleState = pdEthServiceIntentDB.getLifecycleState();
    }
    return clonedServiceLifecycleState;
  }

  private ServicePointDto mapErpToServicePointDto(EthernetRingAssociatedObjectDBImpl erpRingPort, ServicePointDto servicePointDto) {
    if (erpRingPort == null) {
      return null;
    }
    ServicePointDto erpServicePointDto = new ServicePointDto();
    // Copy name from ERP Ring
    if (erpRingPort.getAssociatedObjType() == EthernetRingAssociatedObjectType.LAG) {
      erpServicePointDto.setName(erpRingPort.getParent().getParent().getAssociatedObjAid());
    } else if (erpRingPort.getAssociatedObjType() == EthernetRingAssociatedObjectType.RING_PORT0 || erpRingPort.getAssociatedObjType() == EthernetRingAssociatedObjectType.RING_PORT1) {
      erpServicePointDto.setName(erpRingPort.getParent().getAssociatedObjAid());
    } else {
      throw new IllegalArgumentException("Unsupported Ethernet Ring Port type");
    }
    // Copy rest of attributes
    erpServicePointDto.setCfmEndPointConfigurationDto(servicePointDto.getCfmEndPointConfigurationDto());
    erpServicePointDto.setEndpointConfigurationDto(servicePointDto.getEndpointConfigurationDto());
    erpServicePointDto.setTagManagementDto(servicePointDto.getTagManagementDto());
    erpServicePointDto.setEndPointType(servicePointDto.getEndPointType());
    erpServicePointDto.setInterfaceName(servicePointDto.getInterfaceName());
    erpServicePointDto.setIsEndpoint(servicePointDto.getIsEndpoint());
    erpServicePointDto.setLocalId(servicePointDto.getLocalId());
    erpServicePointDto.setUplink(servicePointDto.isUplink());
    erpServicePointDto.setNodeType(servicePointDto.getNodeType());
    return erpServicePointDto;
  }

  public static List<ProvisioningLayerExtensionDTO> deserialize(List<String> serializedNotification) throws JsonProcessingException {
    ObjectMapper mapper = new ObjectMapper();

    List<ProvisioningLayerExtensionDTO> deserializedNotificationOpt = new ArrayList<>();
    for (String serializedNotif : serializedNotification) {
      deserializedNotificationOpt.add(mapper.readValue(serializedNotif, ProvisioningLayerExtensionDTO.class));
    }
    return deserializedNotificationOpt;
  }

  @MDTransactional
  public void updateServiceIntentState(int intentDbId, MLServiceLifeCycleStateEnum mlServiceLifeCycleStateEnum) {
    final EntityManager em = MDPersistenceManager.current();
    PDEthServiceIntentDBImpl serviceIntentDB = MDPersistenceHelper.getObjectById(
            PDEthServiceIntentDBImpl.class, intentDbId);

    serviceIntentDB.setLifecycleState(mlServiceLifeCycleStateEnum);
    em.persist(serviceIntentDB);

    PDServiceTopologyNotifier topologyNotifier = new PDServiceTopologyNotifier();
    topologyNotifier.update(serviceIntentDB);
  }

  @MDTransactional
  public void updateServiceIntentState(int intentDbId,
                                       MLServiceLifeCycleStateEnum mlServiceLifeCycleStateEnum,
                                       EvpnEntitiesDTO evpnEntitiesDTO) {
    final EntityManager em = MDPersistenceManager.current();
    PDEthServiceIntentDBImpl serviceIntentDB = MDPersistenceHelper.getObjectById(
            PDEthServiceIntentDBImpl.class, intentDbId);
    populateIntentParamsTPs(serviceIntentDB,evpnEntitiesDTO.getFlowDTOS());
    serviceIntentDB.setLifecycleState(mlServiceLifeCycleStateEnum);

    em.persist(serviceIntentDB);
  }

  @MDTransactional
  public void updateServiceIntentState(int intentDbId,
                                       MLServiceLifeCycleStateEnum mlServiceLifeCycleStateEnum,
                                       PDDiscoveredEntitiesDTO entitiesDTO) {
    final EntityManager em = MDPersistenceManager.current();
    PDEthServiceIntentDBImpl serviceIntentDB = MDPersistenceHelper.getObjectById(
            PDEthServiceIntentDBImpl.class, intentDbId);
    populateIntentParamsTPsForPDDiscovered(serviceIntentDB, entitiesDTO.getFlowDTOS());
    serviceIntentDB.setLifecycleState(mlServiceLifeCycleStateEnum);

    em.persist(serviceIntentDB);
  }

  private void populateIntentParamsTPsForPDDiscovered(PDEthServiceIntentDBImpl serviceIntentDB,
                                                      List<FlowDTO> flowDTOS) {
    NetworkElementOperations networkElementOperations = BeanProvider.get().getBean(NetworkElementOperations.class);
    flowDTOS.forEach(flowDTO -> {
      NeDataDto neDataDto = networkElementOperations.getNeDataByIpAddress(flowDTO.getNetworkElementDTO().ipAddress);
      int neIdByIPAddress = neDataDto.getId();
      for (PortDTO portDTO : flowDTO.getPortDTOS()) {
        StringBuilder endpointVlanId = new StringBuilder();
        if(EthEntityType.UNI.equals(portDTO.getEthEntityType())) {
          for (FlowPointDto flowPointDto : portDTO.getFlowPointDtos()) {
            endpointVlanId.append(flowPointDto.getVlansMemberList()).append(" ");
          }
        }
        List<EthServiceIntentParamDTO> serviceIntentParamDTOForTp = new ArrayList<>();
        addIntentParamsForPDDiscovered(flowDTO, portDTO, serviceIntentParamDTOForTp, endpointVlanId);
        serviceIntentDB.addTp(neIdByIPAddress, portDTO.getShortDescription(),
                serviceIntentParamDTOForTp.toArray(new EthServiceIntentParamDTO[0]));
      }
    });
  }

  private void addIntentParamsForPDDiscovered(FlowDTO flowDTO,
                                              PortDTO portDTO,
                                              List<EthServiceIntentParamDTO> serviceIntentParamDTOForTp,
                                              StringBuilder vlanMemberList) {
    serviceIntentParamDTOForTp.add(new EthServiceIntentParamDTO(ServiceIntentParamKeyTypeEth.ETH_NODE_IP_ADDRESS, portDTO.getNetworkElementDTO().ipAddress));
    serviceIntentParamDTOForTp.add(new EthServiceIntentParamDTO(ServiceIntentParamKeyTypeEth.ETH_FLOW_AFFECTED,String.valueOf(flowDTO.getId())));
    serviceIntentParamDTOForTp.add(new EthServiceIntentParamDTO(ServiceIntentParamKeyTypeEth.ETH_PD_PORT_ID,String.valueOf(portDTO.getId())));
    serviceIntentParamDTOForTp.add(new EthServiceIntentParamDTO(ServiceIntentParamKeyTypeEth.ETH_ENDPOINT_VLAN_MEMBERS_LIST, vlanMemberList.toString()));
  }

  private void populateIntentParamsTPs(PDEthServiceIntentDBImpl serviceIntentDB, List<FlowDTO> flowDTOS) {
    NetworkElementOperations networkElementOperations = BeanProvider.get().getBean(NetworkElementOperations.class);
    flowDTOS.forEach(flowDTO -> {
      NeDataDto neDataDto = networkElementOperations.getNeDataByIpAddress(flowDTO.getNetworkElementDTO().ipAddress);
      int neIdByIPAddress = neDataDto.getId();
        for (PortDTO portDTO : flowDTO.getPortDTOS()) {
          StringBuilder fpsIds = new StringBuilder();
            for (FlowPointDto flowPointDto : portDTO.getFlowPointDtos()) {
              fpsIds.append(flowPointDto.getId()).append(" ");
            }
            List<EthServiceIntentParamDTO> serviceIntentParamDTOForTp = new ArrayList<>();
            addIntentParams(flowDTO, portDTO, serviceIntentParamDTOForTp, fpsIds);
          serviceIntentDB.addTp(neIdByIPAddress, portDTO.getShortDescription(),
                    serviceIntentParamDTOForTp.toArray(new EthServiceIntentParamDTO[0]));
        }
    });
  }

  private void addIntentParams(FlowDTO flowDTO, PortDTO portDTO,
                               List<EthServiceIntentParamDTO> serviceIntentParamDTOForTp, StringBuilder fpsIds) {
    serviceIntentParamDTOForTp.add(new EthServiceIntentParamDTO(ServiceIntentParamKeyTypeEth.ETH_NODE_IP_ADDRESS, portDTO.getNetworkElementDTO().ipAddress));
    serviceIntentParamDTOForTp.add(new EthServiceIntentParamDTO(ServiceIntentParamKeyTypeEth.ETH_FLOW_AFFECTED,String.valueOf(flowDTO.getId())));
    serviceIntentParamDTOForTp.add(new EthServiceIntentParamDTO(ServiceIntentParamKeyTypeEth.ETH_PD_PORT_ID,String.valueOf(portDTO.getId())));
    serviceIntentParamDTOForTp.add(new EthServiceIntentParamDTO(ServiceIntentParamKeyTypeEth.ETH_ENDPOINT_VLAN_MEMBERS_LIST, fpsIds.toString()));
  }

  @MDTransactional
  public void addCtpLocationToIntentTp(int intentDbId, List<Integer> cpIds) {
    PDEthServiceIntentDBImpl serviceIntentDB = MDPersistenceHelper.getObjectById(PDEthServiceIntentDBImpl.class, intentDbId);
    addCtpLocationToIntentTpByObject(serviceIntentDB, cpIds);
  }

  @MDTransactional
  public void addCtpLocationToIntentTpByObject(PDEthServiceIntentDBImpl serviceIntentDB, List<Integer> cpIds) {
    final EntityManager em = MDPersistenceManager.current();
    List<PDConnectionPointDBImpl> cpDb = accessHelper.getConnPointList(cpIds);

    new PDCTPLocationToIntentTpParamAdder(accessHelper).add(cpDb, serviceIntentDB);
    em.persist(serviceIntentDB);
  }

  @MDTransactional
  public void setLastErrorMessage(int serviceIntentDBObjectId, String lastErrorMessage, long lastErrorTimestamp) {
    setLastErrorMessageOnNewIntent(serviceIntentDBObjectId, lastErrorMessage, lastErrorTimestamp);
  }

  public void setLastErrorMessageOnNewIntent(int serviceIntentDBObjectId, String lastErrorMessage, long lastErrorTimestamp) {
    PDEthServiceIntentDBImpl serviceIntentDB = MDPersistenceHelper.getObjectById(PDEthServiceIntentDBImpl.class, serviceIntentDBObjectId);
    if (serviceIntentDB == null) {
      log.error("Cannot find service intent object for id=" + serviceIntentDBObjectId + " during setting last error from provisioning operation [" + lastErrorMessage + "].");
    } else {
      serviceIntentDB.addOrUpdateParam(ServiceIntentParamKeyTypeCommon.OPERATION_LAST_ERROR_MESSAGE, lastErrorMessage);
      serviceIntentDB.addOrUpdateParam(ServiceIntentParamKeyTypeCommon.OPERATION_LAST_ERROR_TIMESTAMP, lastErrorTimestamp);
    }
  }

  public boolean validateUniqueServiceName(String serviceName) {
    PDServiceIntentDAO serviceIntentDAO = BeanProvider.get().getBean(PDServiceIntentDAO.class);
    PDEthServiceIntentDBImpl pdEthServiceIntentDB = serviceIntentDAO.getServiceByName(serviceName);
    return pdEthServiceIntentDB == null;
  }

  @MDPersistenceContext
  public List<PDEthServiceIntentDBImpl> getAllServiceIntents() {
    return MDPersistenceHelper.queryForList(PDEthServiceIntentDBImpl.class);
  }

  public PDEthServiceIntentDBImpl findMatchingIntentByID(int mLServiceIntentID) {
    return (PDEthServiceIntentDBImpl) getAllServiceIntents().stream()
            .filter(x -> x instanceof PDEthServiceIntentDBImpl)
            .filter(x -> ((PDEthServiceIntentDBImpl) x).getId() == mLServiceIntentID)
            .findFirst()
            .get();
  }

  public Set<Integer> getNesByService(PDServiceDBImpl serviceDB) {
    PDNEIdsForServiceProvider neIdsForServiceProvider = new PDNEIdsForServiceProvider(accessHelper, ethernetRingDataHelper);
    return neIdsForServiceProvider.getNesByService(serviceDB);
  }

  public int updateServiceIntentAndGetId(int existingIntentId, ServiceDefinitionDto serviceDefinitionDto, List<Integer> cpIds) {
    NetworkElementOperations networkElementOperations = BeanProvider.get().getBean(NetworkElementOperations.class);
    CustomerDAO customerDAO = BeanProvider.get().getBean(CustomerDAO.class);
    PDServiceIntentDAO pdServiceIntentDAO = BeanProvider.get().getBean(PDServiceIntentDAO.class);
    PDEthServiceIntentDBImpl oldPdEthServiceIntentDB = pdServiceIntentDAO.getIntentById(existingIntentId);
    PDServiceDefinitionToServiceIntentConverter serviceDefinitionToServiceIntentConverter =
            new PDServiceDefinitionToServiceIntentConverter(networkElementOperations, customerDAO);
    PDEthServiceIntentDBImpl serviceIntentDB = serviceDefinitionToServiceIntentConverter.convert(serviceDefinitionDto);
    if (cpIds != null && !cpIds.isEmpty()) {
      List<PDConnectionPointDBImpl> cpDb = accessHelper.getConnPointList(cpIds);
      new PDCTPLocationToIntentTpParamAdder(accessHelper).add(cpDb, serviceIntentDB);
    }
    int id = updateIntentTransact(existingIntentId, serviceIntentDB);

    if (serviceIntentDB.getParentId() != oldPdEthServiceIntentDB.getParentId() ||
            serviceIntentDB.getCustomerId() != oldPdEthServiceIntentDB.getCustomerId()) {
      PDServiceTopologyNotifier topologyNotifier = new PDServiceTopologyNotifier();
      topologyNotifier.delete(oldPdEthServiceIntentDB);
      topologyNotifier.create(serviceIntentDB);
    }

    return id;
  }

  @MDTransactional
  private int updateIntentTransact(int existingIntentId, PDEthServiceIntentDBImpl generatedUpdatedServiceIntentDB) {
    PDEthServiceIntentDBImpl serviceIntentDBObject = findMatchingIntentByID(existingIntentId);
    // Do not overwrite TAPI-only parameters UUID and LOCAL-ID upon update
    generatedUpdatedServiceIntentDB.setParamValue(ServiceIntentParamKeyTypeCommon.UUID, serviceIntentDBObject.getParamValue(ServiceIntentParamKeyTypeCommon.UUID));
    if (generatedUpdatedServiceIntentDB.getTpList() != null) {
      generatedUpdatedServiceIntentDB.getTpList().stream()
              .forEach(tp -> {
                serviceIntentDBObject.getTpList().stream()
                        .filter(oldTp -> oldTp.getAid().equals(tp.getAid()) && oldTp.getNeId() == tp.getNeId())
                        .findFirst()
                        .ifPresent(oldTp -> tp.addOrUpdateParam(ServiceIntentParamKeyTypeCommon.LOCAL_ID, oldTp.getParamValue(ServiceIntentParamKeyTypeCommon.LOCAL_ID))
                        );
              });
    }
    serviceIntentDBObject.updateFields(generatedUpdatedServiceIntentDB.generateDTO());
    return serviceIntentDBObject.getId();
  }

  public boolean isServiceIntentConnectedToEthernetRing(int ringId) {
    Optional<AffectedRingParamDto> anyAffectedRing = getAllServiceIntents().stream()
            .filter(PDEthServiceIntentDBImpl.class::isInstance)
            .map(x -> ((PDEthServiceIntentDBImpl) x).getParamValue(ETH_AFFECTED_RINGS))
            .filter(Objects::nonNull)
            .flatMap(Collection::stream)
            .filter(ring -> ring.getRingId() == ringId)
            .findAny();
    return anyAffectedRing.isPresent();
  }
}