/*
 *   Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *    Owner: adatta
 */

package com.adva.nlms.pd.inventory.api.discovery.helper;

import com.adva.nlms.common.config.NetworkElementDTO;
import com.adva.nlms.common.discover.common.dto.FlowDTO;
import com.adva.nlms.common.discover.common.dto.FlowPointDto;
import com.adva.nlms.common.discover.common.dto.PortDTO;
import com.adva.nlms.common.topology.EthEntityType;
import com.adva.nlms.mediation.config.NetworkElementDAO;
import com.adva.nlms.mediation.config.NetworkElementDBImpl;
import com.adva.nlms.pd.api.in.dto.enums.PDNetworkLayer;
import com.adva.nlms.pd.inventory.model.db.PDConnectionDBImpl;
import com.adva.nlms.pd.inventory.model.db.PDConnectionPointDBImpl;
import com.adva.nlms.pd.inventory.model.db.PDTopologyElementDBImpl;
import com.adva.nlms.pd.inventory.model.db.PDVlanLayerExtensionDBImpl;
import com.adva.nlms.pd.inventory.model.interfaces.PDTopologyMOReference;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PDServiceDiscoveryEntitiesMapper {

    public void mapConnectionToFlowDto(PDConnectionDBImpl pdConnectionDB, FlowDTO flowDTO, int neId) {
        flowDTO.setId(pdConnectionDB.getId());
        flowDTO.setFlowName(pdConnectionDB.getLabel());
        flowDTO.setAdminState(pdConnectionDB.getAdminState().getId());
        flowDTO.setOperState(pdConnectionDB.getOperationalStatus().getOperState().getId());
        flowDTO.setShortDescription(pdConnectionDB.getDescription());
        flowDTO.setType("Flow");
        flowDTO.setEthEntityType(EthEntityType.CONNECTION);
        NetworkElementDTO networkElement = getNetworkElement(neId);
        flowDTO.setNetworkElementDTO(networkElement);
        List<PDTopologyMOReference> relatedMOObjects = pdConnectionDB.getRelatedMOObjects();
        if (!relatedMOObjects.isEmpty()) {
            flowDTO.setEntityIndex(relatedMOObjects.get(0).getEntityIndex().toString());
        }
        pdConnectionDB.getRelatedMOObject(PDConnectionPointDBImpl.class)
                .ifPresent(mo -> flowDTO.setEntityIndex(mo.getEntityIndex().toString()));
    }

    // ToRevisit: Use API to fetch NetworkElementDTO instead of fetching from DB
    public NetworkElementDTO getNetworkElement(int neId) {
        NetworkElementDBImpl neDb = NetworkElementDAO.getInstance().getNEDBImplIns(neId);
        return networkElementDTOMapper(neDb);
    }

    public NetworkElementDTO networkElementDTOMapper(NetworkElementDBImpl neDb) {
        NetworkElementDTO networkElementDTO = new NetworkElementDTO();
        networkElementDTO.id = neDb.getId();
        networkElementDTO.ipAddress = neDb.getIPAddress();
        networkElementDTO.type = neDb.getNetworkElementType();
        networkElementDTO.name = neDb.getName();
        networkElementDTO.displayName = neDb.getName() + "( " + neDb.getIPAddress() + ")";
        networkElementDTO.ipAddress = neDb.getIPAddress();
        return networkElementDTO;
    }

    public void mapConnectionPointToFlowpointDto(PDConnectionPointDBImpl connectionPoint,
                                                 FlowPointDto flowPointDto,
                                                 EthEntityType ethEntityType,
                                                 int neId) {
        flowPointDto.setName(connectionPoint.getLabel());
        flowPointDto.setId(connectionPoint.getId());
        flowPointDto.setNeId(connectionPoint.getParentNodeID());
        flowPointDto.setShortDescription(connectionPoint.getDescription());
        flowPointDto.setAdminState(connectionPoint.getAdminState().getId());
        flowPointDto.setOperState(connectionPoint.getOperState().getDbValue());
        flowPointDto.setEntityAlias(connectionPoint.getDescription());
        flowPointDto.setType("Flow Point");
        flowPointDto.setEthEntityType(ethEntityType);
        flowPointDto.setVlansMemberList(getVlanMemberList(connectionPoint));
        NetworkElementDTO networkElement = getNetworkElement(neId);
        flowPointDto.setNetworkElementDTO(networkElement);
        List<PDTopologyMOReference> relatedMOObjects = connectionPoint.getRelatedMOObjects();
        if (!relatedMOObjects.isEmpty()) {
            flowPointDto.setEntityIndex(relatedMOObjects.get(0).getEntityIndex().toString());
        }
    }

    private String getVlanMemberList(PDConnectionPointDBImpl connectionPoint) {
        String vlanMemberList = "";
        if((connectionPoint.getPDLayerExtensionOrNull(PDNetworkLayer.VLAN,
                PDVlanLayerExtensionDBImpl.class)) != null) {
            vlanMemberList = (connectionPoint.getPDLayerExtensionOrNull(PDNetworkLayer.VLAN,
                    PDVlanLayerExtensionDBImpl.class)).getVlanMemberList();

        }
        return vlanMemberList;
    }

    public void mapPortToPortDTO(PDTopologyElementDBImpl port, int neId,
                                 PortDTO portDTO, EthEntityType ethEntityType) {
        portDTO.setId(port.getId());
        portDTO.setShortDescription(port.getLabel());
        NetworkElementDTO networkElement = getNetworkElement(neId);
        portDTO.setNetworkElementDTO(networkElement);
        portDTO.setType("Port");
        portDTO.setEthEntityType(ethEntityType);
        List<PDTopologyMOReference> relatedMOObjects = port.getRelatedMOObjects();
        if (!relatedMOObjects.isEmpty()) {
            if (relatedMOObjects.size() > 1) {
                List<PDTopologyMOReference> portMo = relatedMOObjects.stream().filter(mo -> mo.getEntityIndex().toString().contains("port")).toList();
                if (!portMo.isEmpty()) portDTO.setEntityIndex(portMo.get(0).getEntityIndex().toString());
            } else portDTO.setEntityIndex(relatedMOObjects.get(0).getEntityIndex().toString());
        }
    }

}
