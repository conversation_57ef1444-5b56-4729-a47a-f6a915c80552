<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~ Owner: tomaszw
  -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <bean class="com.adva.nlms.pd.inventory.api.gui.PDEntitiesPageControllerImpl" />
    <bean class="com.adva.nlms.pd.inventory.api.gui.entities.PDEntitiesModelTabBuilder" />
    <bean class="com.adva.nlms.pd.inventory.api.gui.entities.PDEntitiesBuilderImpl" />
    <bean class="com.adva.nlms.pd.inventory.api.sat.PDSatControllerImpl" />
    <bean class="com.adva.nlms.pd.inventory.api.PDGeneralDataControllerImpl" />
    <bean class="com.adva.nlms.pd.inventory.api.service.PDTrailToPDServiceDTOMapper"/>
    <bean class="com.adva.nlms.pd.inventory.api.servicebrowser.PDElineServiceGraphDTOBuilder"/>
    <bean class="com.adva.nlms.pd.inventory.api.servicebrowser.PDElanServiceGraphDTOBuilder"/>
    <bean id="pdElineServiceBrowserGraphModelBuilder" class="com.adva.nlms.pd.inventory.api.servicebrowser.PDElineServiceBrowserGraphModelBuilder"/>
    <bean id="pdElanServiceBrowserGraphModelBuilder" class="com.adva.nlms.pd.inventory.api.servicebrowser.PDElanServiceBrowserGraphModelBuilder"/>
    <bean class="com.adva.nlms.pd.inventory.api.entityswap.helper.PDEntitySwapHelper"/>
    <bean class="com.adva.nlms.pd.inventory.api.entityswap.helper.PDEntitySwapMapper"/>
    <bean class="com.adva.nlms.pd.inventory.api.discovery.helper.PDServiceDiscoveryHelper"/>
    <bean class="com.adva.nlms.pd.inventory.api.discovery.helper.PDServiceDiscoveryEntitiesMapper"/>
    <bean class="com.adva.nlms.pd.inventory.api.discovery.helper.PDServiceDiscoveryTopologyResyncFacade"/>
    <bean id="pdElanServiceDiscoveryResyncHelper" class="com.adva.nlms.pd.inventory.api.discovery.helper.PDElanServiceDiscoveryResyncHelper"/>
    <bean id="pdElineServiceDiscoveryResyncHelper" class="com.adva.nlms.pd.inventory.api.discovery.helper.PDElineServiceDiscoveryResyncHelper"/>
    <bean class="com.adva.nlms.pd.inventory.api.discovery.helper.PDEvpnSeviceToPDServiceDTOMapper"/>
    <bean class="com.adva.nlms.pd.inventory.api.service.PDElanToPDServiceDTOMapper"/>
    <bean class="com.adva.nlms.pd.inventory.api.serviceintent.PDServiceIntentOperationsControllerImpl"/>
    <bean class="com.adva.nlms.pd.inventory.api.gui.PDDTOConverterControllerImpl"/>
    <bean class="com.adva.nlms.pd.inventory.api.gui.PDSearchAndSelectPortInMemoryPagingHdlr" init-method="register"/>

</beans>