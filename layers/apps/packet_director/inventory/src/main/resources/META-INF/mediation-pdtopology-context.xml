<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~ Owner: jak<PERSON><PERSON>l
  -->

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">
    <context:annotation-config/>
    <!-- Module Controller -->

    <!-- SDN -->
    <bean id = "pdNetworkElementMapper" class="com.adva.nlms.pd.inventory.sdn.mappers.PDNetworkElementMapper" />
    <bean id = "pdTrailMapper" class="com.adva.nlms.pd.inventory.sdn.mappers.PDTrailMapper" />
    <bean id = "pdPathMapper" class="com.adva.nlms.pd.inventory.sdn.mappers.PDPathMapper" />
    <bean id = "pdConnectionMapper" class="com.adva.nlms.pd.inventory.sdn.mappers.PDConnectionMapper" />
    <bean id = "pdConnectionPointMapper" class="com.adva.nlms.pd.inventory.sdn.mappers.PDConnectionPointMapper" />
    <bean id = "pdBandwidthProfileMapper" class="com.adva.nlms.pd.inventory.sdn.mappers.PDBandwidthProfileMapper" />
    <bean id = "pdSdnObjectMapperProvider" class="com.adva.nlms.pd.inventory.sdn.mappers.PDSdnObjectMapperProvider" />
    <bean id = "pdTopologyNotificationProcessor" class="com.adva.nlms.pd.inventory.sdn.PDTopologyNotificationProcessor"/>
    <bean id = "pdNetworkElementControllerImpl" class="com.adva.nlms.pd.inventory.api.sdn.PDNetworkElementControllerImpl"/>
    <bean id = "pdConnectionPointControllerImpl" class="com.adva.nlms.pd.inventory.api.sdn.PDConnectionPointControllerImpl"/>
    <bean id = "pdConnectionControllerImpl" class="com.adva.nlms.pd.inventory.api.sdn.PDConnectionControllerImpl"/>
    <bean id = "pdTrailControllerImpl" class="com.adva.nlms.pd.inventory.api.sdn.PDTrailControllerImpl"/>


    <bean id = "pdOperStateCtrlImpl" class="com.adva.nlms.pd.inventory.stateproc.operstate.PDOperStateCtrlImpl" />
    <bean id = "pdConcurrentCtrlImpl" class="com.adva.nlms.pd.inventory.core.concurrent.PDConcurrentCtrlImpl" />
    <bean id = "pdNotificationCtrlImpl" class="com.adva.nlms.pd.inventory.core.notification.PDNotificationCtrlImpl" />

    <bean id = "pdTopologyCtrlImpl" class="com.adva.nlms.pd.inventory.PDTopologyCtrlImpl" init-method="init"/>
    <bean id="pdServiceAdminStateController" class="com.adva.nlms.pd.inventory.stateproc.adminstate.api.PDServiceAdminStateController"/>
    <bean id="pdPostServiceWizardAdminStateWorker" class="com.adva.nlms.pd.inventory.stateproc.adminstate.api.PDPostServiceWizardAdminStateWorker"/>
    <bean id="pdDataAccessFacade" class="com.adva.nlms.pd.inventory.stateproc.adminstate.mofacade.PDDataAccessFacadeDB"/>
    <bean id = "pdLegacyAdminStateController" class="com.adva.nlms.pd.inventory.stateproc.adminstate.api.PDLegacyAdminStateController"/>
    <bean id = "pdBeanProvider" class="com.adva.nlms.pd.inventory.stateproc.adminstate.legacyimpl.PDBeanProvider"/>
    <bean id = "pdOperStateOperationImpl" class="com.adva.nlms.pd.inventory.stateproc.operstate.PDOperStateOperationImpl"/>
    <bean id = "pdPostInitUpgradeActionExecutor" class="com.adva.nlms.pd.inventory.upgrade.PDPostInitUpgradeActionExecutor"/>
    <bean id = "pdTopologyCheckerUtils" class="com.adva.nlms.pd.inventory.servicetopologychecker.PDTopologyCheckerUtils"/>
    <bean id = "pdServiceManagementFacade" class="com.adva.nlms.pd.inventory.api.PDServiceManagementFacade"/>
    <bean id = "pdTopologyDataControllerImpl" class="com.adva.nlms.pd.inventory.api.topology.PDTopologyDataControllerImpl"/>
    <bean id = "pdServiceDataControllerImpl" class="com.adva.nlms.pd.inventory.api.service.PDServiceDataControllerImpl"/>
    <bean id = "pdServiceEncryptionControllerImpl" class="com.adva.nlms.pd.inventory.api.service.PDServiceEncryptionControllerImpl"/>
    <bean id = "pdServiceOperationsControllerImpl" class="com.adva.nlms.pd.inventory.api.service.PDServiceOperationsControllerImpl"/>
    <bean id = "pdServiceBrowserControllerImpl" class="com.adva.nlms.pd.inventory.api.servicebrowser.PDServiceBrowserControllerImpl"/>
    <bean id = "pdServiceEntitiesDataControllerImpl" class="com.adva.nlms.pd.inventory.api.serviceentities.PDServiceEntitiesDataControllerImpl"/>
    <bean id = "pdServiceIntentDataControllerImpl" class="com.adva.nlms.pd.inventory.api.serviceintent.PDServiceIntentDataControllerImpl"/>
    <bean id = "pdServiceProvisioningTestingEndpoint" class="com.adva.nlms.pd.inventory.rest.PDServiceProvisioningTestingEndpoint"/>
    <bean id = "pdDiscoveryControllerImpl" class="com.adva.nlms.pd.inventory.api.discovery.PDDiscoveryControllerImpl"/>
    <bean id = "pdEntitySwapControllerImpl" class="com.adva.nlms.pd.inventory.api.entityswap.PDEntitySwapControllerImpl"/>

    <bean id = "pdSatHelper" class="com.adva.nlms.pd.inventory.sat.PDSatHelper"/>
    <bean id = "pdCfmAlarmHandler" class="com.adva.nlms.pd.inventory.cfm.PDCfmAlarmHandler"/>
    <bean id = "pdCfmToServiceMatcher" class="com.adva.nlms.pd.inventory.cfm.PDCfmToServiceMatcherImpl"/>

    <!--Lifecycle-->
    <bean id = "pfLifecycleStateTransitionFactory" class="com.adva.nlms.pd.inventory.sync.lifecycle.PDLifecycleStateTransitionFactory"/>
    <bean id = "pdLifecycleStateDAO" class="com.adva.nlms.pd.inventory.sync.lifecycle.PDLifecycleStateDAO"/>
    <bean class="com.adva.nlms.pd.inventory.sync.lifecycle.PDDefaultLifecycleStateTransitionHelper" id="pdDefaultLifecycleStateTransitionHelper"/>

    <!-- -->
    <bean id="pdMoReferenceHelper" class="com.adva.nlms.pd.inventory.mofacade.PDMoReferenceHelper"/>
    <bean id="pdMoNotificationHdlr" class="com.adva.nlms.pd.inventory.mofacade.PDMoNotificationHdlr" init-method="init"/>
    <bean id = "pdTaskExecutorServiceImpl" class="com.adva.nlms.pd.inventory.core.concurrent.PDTaskExecutorServiceImpl"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.resync.PDLifecycleTransitionPostCommitHelper" id="PDLifecycleTransitionPostCommitHelper"/>
    <bean id = "pdAlarmResyncHelper" class="com.adva.nlms.pd.inventory.mofacade.resync.PDAlarmResyncHelper" />
    <bean class="com.adva.nlms.pd.inventory.momediation.proc.PDTopologyMoFacade" init-method="init" id = "pdTopologyMoFacade"/>
    <bean id = "pdTopologySpecificNativeDAO" class="com.adva.nlms.pd.inventory.model.dao.PDTopologySpecificNativeDAO" init-method="init"/>
    <bean id="pdTopologyElementDAO" class="com.adva.nlms.pd.inventory.model.dao.PDTopologyElementDAO" init-method="init"/>
    <bean class="com.adva.nlms.pd.inventory.model.dao.PDServiceDAO"/>
    <bean class="com.adva.nlms.pd.inventory.api.discovery.helper.PDEvpnAccessHelper"/>
    <bean class="com.adva.nlms.pd.inventory.api.discovery.helper.PDEvpnEntitesMapper"/>

    <bean id="pdPmProfileCounterDAO" class="com.adva.nlms.pd.inventory.serviceDashboard.pmprofilecounter.PDPmProfileCounterDAO" init-method="init"/>
    <bean id="pdPmProfileDAO" class="com.adva.nlms.pd.inventory.serviceDashboard.pmprofile.PDPmProfileDAO" init-method="init"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.PDTopologyResyncFacade" init-method="init" id="pdTopologyResyncFacade"/>
    <bean id = "pdDefaultMoHelper" class="com.adva.nlms.pd.inventory.momediation.proc.builder.PDDefaultMoHelper"/>
    <bean class="com.adva.nlms.pd.inventory.resources.PDServicePageHdlrAdapterImpl"/>

    <!--Notification Observers-->
    <!--   *** interfaces module within layers, no pd option available    -->
    <bean class="com.adva.nlms.mediation.interfaces.sdn.impl.observers.CustomerServiceGroupObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean class="com.adva.nlms.mediation.interfaces.sdn.impl.observers.CustomerObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
<!--    <bean class="com.adva.nlms.mediation.interfaces.sdn.impl.observers.OchConnectionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>-->
    <bean class="com.adva.nlms.mediation.interfaces.sdn.impl.sm.observers.PortExtensionObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean class="com.adva.nlms.mediation.interfaces.sdn.impl.observers.SegmentAdaptationObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean class="com.adva.nlms.mediation.interfaces.sdn.impl.observers.SubnetUpdateObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean class="com.adva.nlms.mediation.interfaces.sdn.impl.observers.EquipmentObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean class="com.adva.nlms.mediation.interfaces.sdn.impl.observers.NetworkElementObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean class="com.adva.nlms.mediation.interfaces.sdn.impl.observers.PortObserver">
        <qualifier type="com.adva.nlms.inf.api.PredefinedObserver"/>
    </bean>
    <bean class="com.adva.nlms.pd.inventory.core.notification.observer.PDNeObserver" id="pdNeObserver"/>
    <bean class="com.adva.nlms.pd.inventory.core.notification.observer.PDTopoNotfObserver" id="pdTopoNotfObserver"/>
    <bean class="com.adva.nlms.pd.inventory.core.notification.observer.PDTopoModuleObserver" id="pdTopoModuleObserver" />

    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.PDOperStateEventCache" id="pdOperStateEventCache">
        <constructor-arg ref="pdTopologyElementDAO"/>
    </bean>
    <bean class="com.adva.nlms.pd.inventory.core.notification.observer.PDServiceStateCounterObserver" id="pdServiceStateCounterObserver"/>
    <bean class="com.adva.nlms.pd.inventory.core.notification.observer.PDModuleManagementF3MoObserver" id="pdModuleManagementF3MoObserver"/>
    <bean class="com.adva.nlms.pd.inventory.core.notification.observer.PDModuleManagementF4MoObserver" id="pdModuleManagementF4MoObserver"/>
    <bean class="com.adva.nlms.pd.inventory.core.notification.observer.PDTopoIntentObserver" id="pdTopoIntentObeserver"/>
    <bean class="com.adva.nlms.pd.inventory.core.notification.observer.PDAttribMoObserver" id="pdAttribMoObserver"/>
    <bean class="com.adva.nlms.pd.inventory.helpers.PDNeResponseStatusHelper" id="pdNEResponseStatusHelper"/>
    <bean class="com.adva.nlms.pd.inventory.core.notification.observer.PDNeResponseStateListener" id="pdNeResponseStateListener"/>


    <!-- f3 observers section-->
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f3.F3PDObservers"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f3.PDFlowF3MoObserver"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f3.PDPortF3MoObserver"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f3.PDLagPortF3MoObserver"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f3.PDFPObserver"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f3.PDProtectionGroupObserver"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f3.PDElineObserver"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f3.PDMPFlowObserver"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f3.PDPolicerF3MoObserver"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f3.PDShaperF3MoObserver"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f3.PDCFMObserver"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f3.PDCFMMaNetObserver"/>

    <!-- f4 observers -->
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f4.F4PDObservers"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f4.PDF4FPObserver"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f4.PDF4FlowObserver"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f4.PDF4LagObserver"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f4.PDF4MembershipRangeObserver"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f4.PDPortF4MoObserver"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f4.PDF4PolicerProfileObserver"/>
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f4.PDF4ErpUnitObserver" />
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.evpn.PDEvpnObserver" />

    <!-- Helpers for notification observers -->
    <bean id ="pdServiceResyncTaskHelper" class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.PDServiceResyncTaskHelper"/>
    <bean class="com.adva.nlms.pd.inventory.helpers.PDHelperFacade" id="pdHelperFacade"/>
    <bean class="com.adva.nlms.pd.inventory.helpers.PDBandwidthProfileHelper" id = "pdBandwidthProfileHelper"/>
    <bean class="com.adva.nlms.pd.inventory.helpers.PDShaperHelper" />
    <bean class="com.adva.nlms.pd.inventory.helpers.PDPolicerHelper" />
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f3.helpers.PDConnnectionPointResyncTaskProvider" />
    <bean class="com.adva.nlms.pd.inventory.mofacade.notfhelpers.f3.helpers.PDFlowResyncExecutor" />

    <bean class="com.adva.nlms.pd.inventory.mofacade.PDEthObserversInitializer" />

    <!-- NE mo helpers -->
    <bean class="com.adva.nlms.pd.inventory.momediation.proc.builder.PDLineMoHelper" id="pdlineMoHelper"/>

    <!-- Helper Class -->

    <!-- Layer Extension Helpers -->
    <bean class="com.adva.nlms.pd.inventory.momediation.proc.builder.layerExtension.PDDefaultLayerExtensionHelper" id = "pdDefaultLayerExtensionHelper"/>
    <bean class="com.adva.nlms.pd.inventory.momediation.proc.builder.layerExtension.PDLayerExtensionHelper" id = "pdEthLayerExtensionHelper"/>
    <bean class="com.adva.nlms.pd.inventory.momediation.proc.builder.layerExtension.PDVlanLayerExtensionHelper" id =  "pdVlanLayerExtensionHelper"/>
    <bean class="com.adva.nlms.pd.inventory.momediation.proc.builder.layerExtension.PDCFMLayerExtensionHelper" />
    <bean class="com.adva.nlms.pd.inventory.momediation.proc.builder.layerExtension.PDWdmLayersExtensionHelper" id = "pdWdmLayersExtensionHelper"/>
    <bean class="com.adva.nlms.pd.inventory.momediation.proc.builder.layerExtension.PDLagLayerExtensionHelper" id = "pdLagLayerExtensionHelper"/>

    <bean class="com.adva.nlms.mediation.layer.apps.ethsm.message.MessageSenderImpl"/>
    <bean class="com.adva.nlms.mediation.layer.apps.ethsm.sat.SatOperationsImpl"/>

    <bean class="com.adva.nlms.pd.inventory.service.PDServiceNotificationAction" init-method="init"/>

    <bean class="com.adva.nlms.mediation.smapp.data.tab.impl.services.PDL3IPVPNServiceTabBuilder"/>

    <!-- Resync Helpers -->
    <import resource="mofacade/resync/mediation-pdtopology-resync-context.xml"/>

    <!--Support for Resources -->
    <import resource="resources/mediation-pdtopology-resources-context.xml"/>

    <!-- Service Helpers -->
    <import resource="service/mediation-pdtopology-service-context.xml"/>

    <import resource="mofacade/modto/helpers/eth/mediation-pdtopology-eth-context.xml"/>
    <import resource="utilities/mediation-pdtopology-utilities-context.xml"/>

    <!--Support for Processing strategies -->
    <import resource="mofacade/procstrategy/mediation-pdtopology-proc-context.xml"/>
    <!-- Support for processes running dbconsistency automatically in the background -->
    <import resource="dbconsistency/mediation-pdtopology-dbconsistency-context.xml"/>
    <import resource="api/mediation-pdtopology-api-context.xml"/>
</beans>