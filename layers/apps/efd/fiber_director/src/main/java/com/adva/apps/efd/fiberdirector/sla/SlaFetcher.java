/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: karolps
 */

package com.adva.apps.efd.fiberdirector.sla;

import com.adva.apps.efd.fiberdirector.api.AdminState;
import com.adva.nlms.common.event.EventSeverity;
import com.adva.nlms.common.event.EventType;
import com.adva.nlms.common.paging.PagingRestriction;
import com.adva.nlms.mediation.common.paging.FilterCondition;
import com.adva.nlms.mediation.common.paging.PagingCondition;
import com.adva.nlms.mediation.common.paging.SortingConditionConstant;
import com.adva.nlms.mediation.common.rest.exceptions.efd.EfdBaseRuntimeException;
import com.adva.nlms.mediation.common.rest.exceptions.efd.EfdNotFoundRuntimeException;
import com.adva.nlms.mediation.config.NetworkElementDAO;
import com.adva.nlms.mediation.event.EventDBQueryHdlr;
import com.adva.nlms.mediation.event.EventDTO;
import com.adva.nlms.mediation.event.EventPageHdlrImpl;

import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;

import static com.adva.apps.efd.fiberdirector.api.PortInfoHelper.convertPortNameToStartOfMtpName;

public class SlaFetcher {
    private final NetworkElementDAO networkElementDAO;
    private final EventDBQueryHdlr eventDBQueryHdlr;

    private static final String PERCENT_SIGN = "%";

    public SlaFetcher(NetworkElementDAO networkElementDAO, EventDBQueryHdlr eventDBQueryHdlr) {
        this.networkElementDAO = networkElementDAO;
        this.eventDBQueryHdlr = eventDBQueryHdlr;
    }

    protected List<AdminStateElement> fetchAdminStateData(String neIpAddress, String nePortAid, long fromTimestamp) {
        List<FilterCondition> lastEventBeforeFilters = createBaseAdminStateFilters(neIpAddress, nePortAid);
        lastEventBeforeFilters.add(new FilterCondition<>(EventPageHdlrImpl.EventPageColumn.NMS_TIMESTAMP, FilterCondition.DbOperator.LT, fromTimestamp));
        List<EventDTO> events = eventDBQueryHdlr.getEventsFor(
            new PagingCondition(null, PagingRestriction.PageCmd.TOP, 1, null, 0),
            SortingConditionConstant.DEFAULT,
            lastEventBeforeFilters
        );

        List<FilterCondition> filters = createBaseAdminStateFilters(neIpAddress, nePortAid);
        filters.add(new FilterCondition<>(EventPageHdlrImpl.EventPageColumn.NMS_TIMESTAMP, FilterCondition.DbOperator.GE, fromTimestamp));
        events.addAll(eventDBQueryHdlr.getEventsFor(PagingCondition.DEFAULT, SortingConditionConstant.DEFAULT, filters));

        return events.stream()
            .map(eventDTO -> new AdminStateElement(resolveAdminState(eventDTO.getDescription().toString()), Math.max(eventDTO.getNMSTimeStamp(), fromTimestamp)))
            .toList();
    }

    protected List<AlarmStateElement> fetchAlarmsStateData(String neIpAddress, String nePortAid, long fromTimestamp, long currentTimestamp) {
        List<EventDTO> alarms = fetchEvents(neIpAddress, nePortAid, fromTimestamp, false);
        List<EventDTO> mtpAlarms = fetchEvents(neIpAddress, nePortAid, fromTimestamp, true);
        alarms.addAll(mtpAlarms);

        return alarms.stream()
            .filter(eventDTO -> eventDTO.alarmType != 0)
            .map(eventDTO ->
                new AlarmStateElement(
                    resolveEventSeverity(eventDTO.getSeverity().getIdlType()),
                    Math.max(eventDTO.getNMSTimeStamp(), fromTimestamp),
                    eventDTO.getCLearedNMSTimeStamp() == 0 ? currentTimestamp : eventDTO.getCLearedNMSTimeStamp()
                ))
            .sorted(Comparator.comparingInt(s -> s.eventSeverity().getIdlType()))
            .toList();
    }

    private List<EventDTO> fetchEvents(String neIpAddress, String nePortAid, long fromTimestamp, boolean isMtp) {
        FilterCondition entityDescriptionCondition = isMtp ?
            new FilterCondition<>(EventPageHdlrImpl.EventPageColumn.ENTITY_DESCR, FilterCondition.DbOperator.LIKE, convertPortNameToStartOfMtpName(nePortAid) + PERCENT_SIGN) :
            new FilterCondition<>(EventPageHdlrImpl.EventPageColumn.ENTITY_DESCR, FilterCondition.DbOperator.EQ, nePortAid);

        List<FilterCondition> notClearedAlarmsFilters = createBaseAlarmStateFilters(neIpAddress);
        notClearedAlarmsFilters.add(entityDescriptionCondition);
        notClearedAlarmsFilters.add(new FilterCondition<>(EventPageHdlrImpl.EventPageColumn.NMS_CLEARED_TIMESTAMP, FilterCondition.DbOperator.EQ, 0));
        List<EventDTO> alarms = eventDBQueryHdlr.getEventsFor(PagingCondition.DEFAULT, SortingConditionConstant.DEFAULT, notClearedAlarmsFilters);

        List<FilterCondition> clearedAlarmsFilters = createBaseAlarmStateFilters(neIpAddress);
        clearedAlarmsFilters.add(entityDescriptionCondition);
        clearedAlarmsFilters.add(new FilterCondition<>(EventPageHdlrImpl.EventPageColumn.NMS_CLEARED_TIMESTAMP, FilterCondition.DbOperator.GE, fromTimestamp));
        List<EventDTO> clearedAlarms = eventDBQueryHdlr.getEventsFor(PagingCondition.DEFAULT, SortingConditionConstant.DEFAULT, clearedAlarmsFilters);
        alarms.addAll(clearedAlarms);

        return alarms;
    }

    private int getNeId(String neIpAddress) {
        int neId = networkElementDAO.getNEIdByIPAddress(neIpAddress);
        if (neId == 0) {
            throw new EfdNotFoundRuntimeException("Network Element with ipAddress: %s not found.", neIpAddress);
        }
        return neId;
    }

    private AdminState resolveAdminState(String eventDescription) {
        if (eventDescription.contains(AdminState.IN_SERVICE.getEventValue())) {
            return AdminState.IN_SERVICE;
        } else if (eventDescription.contains(AdminState.MANAGEMENT.getEventValue())) {
            return AdminState.MANAGEMENT;
        } else if (eventDescription.contains(AdminState.DISABLED.getEventValue())) {
            return AdminState.DISABLED;
        } else {
            throw new EfdBaseRuntimeException("AdminState parsing failed for: %s", eventDescription);
        }
    }

    private EventSeverity resolveEventSeverity(int eventSeverity) {
        if (eventSeverity == 0) {
            return EventSeverity.CRITICAL;
        } else if (eventSeverity == 1) {
            return EventSeverity.MAJOR;
        } else if (eventSeverity == 2) {
            return EventSeverity.MINOR;
        } else if (eventSeverity == 4) {
            return EventSeverity.INFORMATION;
        } else {
            throw new EfdBaseRuntimeException("EventSeverity parsing failed for: %s", eventSeverity);
        }
    }

    private List<FilterCondition> createBaseAdminStateFilters(String neIpAddress, String nePortAid) {
        List<FilterCondition> filters = new LinkedList<>();
        var neId = getNeId(neIpAddress);
        filters.add(new FilterCondition<>(EventPageHdlrImpl.EventPageColumn.NE_ID, FilterCondition.DbOperator.EQ, neId));
        filters.add(new FilterCondition<>(EventPageHdlrImpl.EventPageColumn.ENTITY_DESCR, FilterCondition.DbOperator.EQ, nePortAid));
        filters.add(new FilterCondition<>(EventPageHdlrImpl.EventPageColumn.SHORT_NAME, FilterCondition.DbOperator.EQ, "NE-CONF"));
        filters.add(new FilterCondition<>(EventPageHdlrImpl.EventPageColumn.DESCRIPTION, FilterCondition.DbOperator.LIKE, "%Admin State%"));

        return filters;
    }

    private List<FilterCondition> createBaseAlarmStateFilters(String neIpAddress) {
        List<FilterCondition> filters = new LinkedList<>();
        var neId = getNeId(neIpAddress);
        filters.add(new FilterCondition<>(EventPageHdlrImpl.EventPageColumn.NE_ID, FilterCondition.DbOperator.EQ, neId));
        List<Object> eventSeverityValues = List.of(EventSeverity._CRITICAL, EventSeverity._MAJOR, EventSeverity._MINOR, EventSeverity._INFORMATION);
        filters.add(new FilterCondition<>(EventPageHdlrImpl.EventPageColumn.SEVERITY, FilterCondition.DbOperator.IN, eventSeverityValues));
        filters.add(new FilterCondition<>(EventPageHdlrImpl.EventPageColumn.TYPE, FilterCondition.DbOperator.NE, EventType._CLEARING));

        return filters;
    }


    record AdminStateElement(
            AdminState adminstate,
            long nmsTimestamp
    ) {
    }

    record AlarmStateElement(
            EventSeverity eventSeverity,
            long nmsTimestamp,
            long nmsClearedTimestamp
    ) {
    }
}


