/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.driver.delegate.resource.mediator;

import com.adva.nlms.driver.bean.registry.DriverRegistry;
import com.adva.nlms.mediation.bean.provider.api.BeanProvider;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.resource.mediator.api.in.OperationFailedException;
import com.adva.nlms.resource.mediator.api.in.ResourceRequestMediator;
import com.adva.topology.manager.api.in.TopologyNodeApi;
import driver.delegate.DelegateNESupport;
import ni.msg.EnvelopeOuterClass;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@Component
public class ResourceRequestMediatorDelegator implements ResourceRequestMediator, DelegateNESupport {

  @Override
  public void removeNetworkElement(int neId) {
    getDDI(getNeID(neId)).removeNetworkElement(neId);
  }

  @Override
  public void handleSegmentRequestInTask(ni.msg.EnvelopeOuterClass.Envelope envelope) {
    getDDI(getNeID(envelope)).handleSegmentRequestInTask(envelope);
  }

  @Override
  public void handleProtectionSwitch(UUID neId, String protectionGroupEntityIndex) throws OperationFailedException {
    getDDI(neId).handleProtectionSwitch(neId, protectionGroupEntityIndex);
  }

  @Override
  public void handleAdminStateOperation(UUID neId, List<String> aids, String protectionGroupIndex,
                                        AdminState adminState, LayerQualifier layerProtocolQualifier) throws OperationFailedException {
    getDDI(neId).handleAdminStateOperation(neId, aids, protectionGroupIndex, adminState, layerProtocolQualifier);
  }

  private ResourceRequestMediator getDDI(UUID neId) {
    var ddiType = getRegistryTypeStringForNE(neId) + "_RESOURCE_REQUEST_MEDIATOR";
    return DriverRegistry.getRegistry().getDDI(ResourceRequestMediator.class, ddiType);
  }

  private static UUID getNeID(EnvelopeOuterClass.Envelope envelope) {
    // Get the network element UUID from the database identifier.
    int neId = Integer.parseInt(envelope.getDestination().getId().getId());
    return getNeID(neId);
  }

  private static UUID getNeID(int neId) {
    TopologyNodeApi topologyNodeApi = BeanProvider.get().getBean(TopologyNodeApi.class);
    try {
      UUID uuid = topologyNodeApi.getNodeUUIDByNeId(neId);
      if (uuid != null) {
        return uuid;
      } else {
        throw new IllegalArgumentException("Node with id " + neId + " not found");
      }
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

}
