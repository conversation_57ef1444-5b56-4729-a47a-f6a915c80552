/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.test.kafka;

import com.adva.nlms.opticalrouter.api.registration.BeaconMessage;
import com.adva.nlms.opticalrouter.api.registration.OpticalRouterDriverBeacon;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;

class BeaconMessageConsumer {
  private final OpticalRouterDriverBeacon beacon;

  BeaconMessageConsumer(OpticalRouterDriverBeacon beacon) {
    this.beacon = beacon;
  }

  @KafkaListener(
    topics = "${app.kafka.beacon-topic.name}",
    groupId = "${app.kafka.group-id}",
    containerFactory = "kafkaListenerBeaconMessageFactory"
  )
  void listen(ConsumerRecord<String, BeaconMessage> beaconMessageConsumerRecord) {
    beacon.process(beaconMessageConsumerRecord.value());
  }
}
