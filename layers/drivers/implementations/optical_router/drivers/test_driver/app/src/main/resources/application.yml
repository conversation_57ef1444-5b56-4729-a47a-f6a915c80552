server:
  port: 9088

spring:
  kafka:
    bootstrap-servers: "${KAFKA_BOOTSTRAP_SERVERS:127.0.0.1:9094}"
  devtools:
    restart:
      enabled: false

management:
  endpoints:
    enabled-by-default: false
  endpoint:
    health:
      enabled: true

app:
  registration:
    # configuration should reflect com.adva.nlms.opticalrouter.api.registration.DriverManifest
    manifest:
      name: Test driver
      abbreviated-name: TEST
      version: 0.0.1
      device-types:
        - name: TEST-36MR
          minimum-version: 24.4R1.8-EVO
          type-id: 1001000
          authentication-methods: [PAP]

  kafka:
    group-id: "optical-router-driver-group"
    registration-topic:
      name: "v1.drv.optical-router.registration"
      partitions: 1
      replicas: 1
    beacon-topic:
      name: "v1.drv.optical-router.beacon"
      partitions: 1
      replicas: 1
    command-responses-topic:
      name: "v1.drv.optical-router.command-responses"
      partitions: 1
      replicas: 1

  driver:
    host: "http://127.0.0.1:9088"
    uri: "enc/v1/drv/test"
