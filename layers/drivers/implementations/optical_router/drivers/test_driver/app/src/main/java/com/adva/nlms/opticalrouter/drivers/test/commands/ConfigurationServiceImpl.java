/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.test.commands;

import com.adva.nlms.opticalrouter.api.resources.OpticalRouterDeviceInfo;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterInventory;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterStatus;

import java.util.UUID;

class ConfigurationServiceImpl implements ConfigurationService {

  private final DeviceRepository deviceRepository;

  public ConfigurationServiceImpl(DeviceRepository deviceRepository) {
    this.deviceRepository = deviceRepository;
  }

  @Override
  public void setColdStartTime(UUID id, long coldStartTime) {
    deviceRepository.setColdStartTime(id, coldStartTime);
  }

  @Override
  public void setDeviceInfo(UUID id, OpticalRouterDeviceInfo opticalRouterDeviceInfo) {
    deviceRepository.setDeviceInfo(id, opticalRouterDeviceInfo);
  }

  @Override
  public void setInventory(UUID id, OpticalRouterInventory opticalRouterInventory) {
    deviceRepository.setInventory(id, opticalRouterInventory);
  }

  @Override
  public void setStatus(UUID id, OpticalRouterStatus opticalRouterStatus) {
    deviceRepository.setStatus(id, opticalRouterStatus);
  }

  @Override
  public void clearAllData(UUID id) {
    deviceRepository.clearAllData(id);
  }
}
