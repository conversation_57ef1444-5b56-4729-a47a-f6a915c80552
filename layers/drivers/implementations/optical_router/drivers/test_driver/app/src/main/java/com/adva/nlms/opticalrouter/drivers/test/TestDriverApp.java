/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.test;

import com.adva.nlms.mediation.infrastructure.concurrent.tracing.MdcTaskDecorator;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@EnableConfigurationProperties(DriverRegistration.RegistrationConfig.class)
@ConfigurationPropertiesScan
@EnableAsync
class TestDriverApp {
  public static void main(String[] args) {
    SpringApplication.run(TestDriverApp.class, args);
  }

  @Bean
  public MdcTaskDecorator mdcTaskDecorator() {
    return new MdcTaskDecorator();
  }
}
