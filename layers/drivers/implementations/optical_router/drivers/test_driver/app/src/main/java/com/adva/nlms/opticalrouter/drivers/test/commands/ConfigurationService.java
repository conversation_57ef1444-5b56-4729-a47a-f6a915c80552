/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.test.commands;

import com.adva.nlms.opticalrouter.api.resources.OpticalRouterDeviceInfo;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterInventory;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterStatus;

import java.util.UUID;

public interface ConfigurationService {

  void setColdStartTime(UUID id, long coldStartTime);

  void setDeviceInfo(UUID id, OpticalRouterDeviceInfo opticalRouterDeviceInfo);

  void setInventory(UUID id, OpticalRouterInventory opticalRouterInventory);

  void setStatus(UUID id, OpticalRouterStatus opticalRouterStatus);

  void clearAllData(UUID id);
}
