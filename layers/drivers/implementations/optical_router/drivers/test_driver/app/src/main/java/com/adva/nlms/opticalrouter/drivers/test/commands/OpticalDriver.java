/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.test.commands;

import com.adva.nlms.opticalrouter.api.commands.CommunicationSettings;
import com.adva.nlms.opticalrouter.api.commands.OpticalRouterDeviceDriver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.UUID;

class OpticalDriver implements OpticalRouterDeviceDriver {

  private static final Logger LOGGER = LoggerFactory.getLogger(OpticalDriver.class);
  private final ConnectionCommandProcessor connectionCommandProcessor;
  private final SynchronizeCommandProcessor synchronizeCommandProcessor;

  OpticalDriver(ConnectionCommandProcessor connectionCommandProcessor,
                SynchronizeCommandProcessor synchronizeCommandProcessor) {
    this.connectionCommandProcessor = connectionCommandProcessor;
    this.synchronizeCommandProcessor = synchronizeCommandProcessor;
  }

  @Override
  public void connect(UUID neId, CommunicationSettings settings) {
    LOGGER.info("connect {} {}", neId, settings);
    connectionCommandProcessor.connect(neId, settings);
  }

  @Override
  public void disconnect(UUID neId) {
    LOGGER.info("disconnect {}", neId);
    connectionCommandProcessor.disconnect(neId);
  }

  @Override
  public boolean isConnected(UUID neId) {
    LOGGER.info("isConnected {}", neId);
    return connectionCommandProcessor.isConnected(neId);
  }

  @Override
  public long getColdStartTime(UUID neId) {
    LOGGER.info("getColdStartTime {}", neId);
    return connectionCommandProcessor.getColdStartTime(neId);
  }

  @Override
  public void synchronizeDeviceInfo(UUID neId) {
    LOGGER.info("synchronizeDeviceInfo {}", neId);
    synchronizeCommandProcessor.synchronizeDeviceInfo(neId);
  }

  @Override
  public void synchronizeInventory(UUID neId) {
    LOGGER.info("synchronizeInventory {}", neId);
    synchronizeCommandProcessor.synchronizeInventory(neId);
  }

  @Override
  public void synchronizeStatus(UUID neId) {
    LOGGER.info("synchronizeStatus {}", neId);
    synchronizeCommandProcessor.synchronizeStatus(neId);
  }

  @Override
  public void testConnection(UUID neId, CommunicationSettings settings) {
    LOGGER.info("testConnection {} settings {}", neId, settings);
  }

}
