/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.test.commands;

import com.adva.nlms.opticalrouter.api.commands.CommunicationSettings;

import java.util.UUID;

import static java.util.Objects.requireNonNull;

class ConnectionCommandProcessor {

  private final DeviceRepository repository;

  ConnectionCommandProcessor(DeviceRepository repository) {
    this.repository = repository;
  }

  void connect(final UUID neId, final CommunicationSettings settings) {
    requireNonNull(neId);
    requireNonNull(settings);
    if (repository.getColdStartTime(neId).isEmpty()) {
      repository.setColdStartTime(neId, System.currentTimeMillis() - 60_000);
    }
    repository.addDevice(neId);
  }

  void disconnect(final UUID neId) {
    requireNonNull(neId);
    repository.removeDevice(neId);
  }

  boolean isConnected(final UUID neId) {
    requireNonNull(neId);
    return repository.isPresent(neId);
  }

  long getColdStartTime(final UUID neId) {
    requireNonNull(neId);
    return repository.getColdStartTime(neId).orElseThrow();
  }

}
