/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.test.commands;

import com.adva.nlms.opticalrouter.api.resources.OpticalRouterDeviceInfo;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterInventory;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterStatus;

import java.util.Optional;
import java.util.UUID;

interface DeviceRepository {

  void addDevice(UUID id);

  void removeDevice(UUID id);

  boolean isPresent(UUID id);

  void setColdStartTime(UUID id, long coldStartTime);

  Optional<Long> getColdStartTime(UUID id);

  void setDeviceInfo(UUID id, OpticalRouterDeviceInfo opticalRouterDeviceInfo);

  Optional<OpticalRouterDeviceInfo> getDeviceInfo(UUID id);

  void setInventory(UUID id, OpticalRouterInventory opticalRouterInventory);

  Optional<OpticalRouterInventory> getInventory(UUID id);

  void setStatus(UUID id, OpticalRouterStatus opticalRouterStatus);

  Optional<OpticalRouterStatus> getStatus(UUID id);

  void clearAllData(UUID id);
}
