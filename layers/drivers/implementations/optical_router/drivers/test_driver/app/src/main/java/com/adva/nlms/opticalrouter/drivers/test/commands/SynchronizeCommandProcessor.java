/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.test.commands;

import com.adva.nlms.opticalrouter.api.commands.CommandResponder;
import com.adva.nlms.opticalrouter.api.commands.CommandResponse;
import com.adva.nlms.opticalrouter.api.commands.Status;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterDeviceInfo;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterInventory;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterStatus;
import org.springframework.scheduling.annotation.Async;

import java.util.UUID;

import static java.util.Objects.requireNonNull;

class SynchronizeCommandProcessor {

  private static final String INVENTORY_PATH = "/examples/opticalRouterInventory.json";
  private static final String DEVICE_INFO_PATH = "/examples/opticalRouterDeviceInfo.json";
  private static final String STATUS_PATH = "/examples/opticalRouterStatus.json";

  private final CommandResponder commandResponder;
  private final DeviceRepository deviceRepository;
  private final JsonDataLoader loader;

  SynchronizeCommandProcessor(CommandResponder commandResponder, DeviceRepository deviceRepository, JsonDataLoader loader) {
    this.commandResponder = commandResponder;
    this.deviceRepository = deviceRepository;
    this.loader = loader;
  }

  @Async
  public void synchronizeDeviceInfo(UUID neId) {
    requireNonNull(neId);

    OpticalRouterDeviceInfo deviceInfo = deviceRepository.getDeviceInfo(neId)
      .orElseGet(() -> {
          var info = loader.load(DEVICE_INFO_PATH, OpticalRouterDeviceInfo.class);
          return info.withName(info.name() + neId);
        }
      );
    CommandResponse response = new CommandResponse(neId, Status.SUCCESS, "Device info with neId=%s has been synchronized".formatted(neId), deviceInfo);

    commandResponder.process(response);
  }

  @Async
  public void synchronizeInventory(UUID neId) {
    requireNonNull(neId);

    OpticalRouterInventory opticalRouterInventory = deviceRepository.getInventory(neId).orElseGet(() -> loader.load(INVENTORY_PATH, OpticalRouterInventory.class));
    CommandResponse response = new CommandResponse(neId, Status.SUCCESS, "Device inventory with neId=%s has been synchronized".formatted(neId), opticalRouterInventory);

    commandResponder.process(response);
  }

  @Async
  public void synchronizeStatus(UUID neId) {
    requireNonNull(neId);

    OpticalRouterStatus opticalRouterStatus = deviceRepository.getStatus(neId).orElseGet(() -> loader.load(STATUS_PATH, OpticalRouterStatus.class));
    CommandResponse response = new CommandResponse(neId, Status.SUCCESS, "Optical Router status with neId=%s has been synchronized".formatted(neId), opticalRouterStatus);

    commandResponder.process(response);
  }

}
