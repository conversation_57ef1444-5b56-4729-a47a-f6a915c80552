/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.test.rest.server;

import com.adva.nlms.opticalrouter.api.resources.OpticalRouterDeviceInfo;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterInventory;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterStatus;
import com.adva.nlms.opticalrouter.drivers.test.commands.ConfigurationService;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@Validated
@RestController
@RequestMapping(path = "${app.driver.uri}")
public class ConfigurationController {

  ConfigurationService configurationService;

  public ConfigurationController(ConfigurationService configurationService) {
    this.configurationService = configurationService;
  }

  @PostMapping("/{neId}/set-cold-start-time/{coldStartTime}")
  public ResponseEntity<Void> setColdStartTime(@PathVariable("neId") UUID neId, @PathVariable("coldStartTime") Integer coldStartTime) {
    configurationService.setColdStartTime(neId, coldStartTime);
    return ResponseEntity.ok().build();
  }

  @PostMapping("/{neId}/set-device-info")
  public ResponseEntity<Void> setDeviceInfo(@PathVariable("neId") UUID neId, @RequestBody OpticalRouterDeviceInfo deviceInfo) {
    configurationService.setDeviceInfo(neId, deviceInfo);
    return ResponseEntity.ok().build();
  }

  @PostMapping("/{neId}/set-optical-router-inventory")
  public ResponseEntity<Void> setOpticalRouterInventory(@PathVariable("neId") UUID neId, @RequestBody OpticalRouterInventory opticalRouterInventory) {
    configurationService.setInventory(neId, opticalRouterInventory);
    return ResponseEntity.ok().build();
  }

  @PostMapping("/{neId}/set-optical-router-status")
  public ResponseEntity<Void> setOpticalRouterStatus(@PathVariable("neId") UUID neId, @RequestBody OpticalRouterStatus opticalRouterStatus) {
    configurationService.setStatus(neId, opticalRouterStatus);
    return ResponseEntity.ok().build();
  }

  @DeleteMapping("/{neId}/clear-all-data")
  public ResponseEntity<Void> clearAllData(@PathVariable("neId") UUID neId) {
    configurationService.clearAllData(neId);
    return ResponseEntity.ok().build();
  }

}
