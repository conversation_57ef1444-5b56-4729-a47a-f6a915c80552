/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.test.commands;

import com.adva.nlms.opticalrouter.api.commands.CommandResponder;
import com.adva.nlms.opticalrouter.api.commands.OpticalRouterDeviceDriver;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
class CommandConfiguration {

  @Bean
  DeviceRepository deviceRepository() {
    return new DeviceRepositoryImpl();
  }

  @Bean
  JsonDataLoader jsonDataLoader(ObjectMapper objectMapper) {
    return new JsonDataLoader(objectMapper);
  }

  @Bean
  ConfigurationService configurationService(DeviceRepository deviceRepository) {
    return new ConfigurationServiceImpl(deviceRepository);
  }

  @Bean
  ConnectionCommandProcessor connectionCommandProcessor(DeviceRepository repository) {
    return new ConnectionCommandProcessor(repository);
  }

  @Bean
  SynchronizeCommandProcessor synchronizeCommandProcessor(CommandResponder commandResponder, DeviceRepository deviceRepository, JsonDataLoader jsonDataLoader) {
    return new SynchronizeCommandProcessor(commandResponder, deviceRepository, jsonDataLoader);
  }

  @Bean
  OpticalRouterDeviceDriver opticalRouterDeviceDriver(ConnectionCommandProcessor connectionCommandProcessor,
                                                      SynchronizeCommandProcessor synchronizeCommandProcessor) {
    return new OpticalDriver(connectionCommandProcessor, synchronizeCommandProcessor);
  }

}
