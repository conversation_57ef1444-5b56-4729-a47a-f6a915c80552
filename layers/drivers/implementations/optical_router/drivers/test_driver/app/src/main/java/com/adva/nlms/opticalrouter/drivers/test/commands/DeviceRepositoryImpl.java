/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.test.commands;

import com.adva.nlms.opticalrouter.api.resources.OpticalRouterDeviceInfo;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterInventory;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterStatus;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

class DeviceRepositoryImpl implements DeviceRepository {
  private final Set<UUID> devices = new HashSet<>();
  private final Map<UUID, Long> coldStartTimeMap = new HashMap<>();
  private final Map<UUID, OpticalRouterDeviceInfo> deviceInfoMap = new HashMap<>();
  private final Map<UUID, OpticalRouterInventory> inventoryMap = new HashMap<>();
  private final Map<UUID, OpticalRouterStatus> statusMap = new HashMap<>();

  @Override
  public void addDevice(UUID id) {
    devices.add(id);
  }

  @Override
  public void removeDevice(UUID id) {
    devices.remove(id);
  }

  @Override
  public boolean isPresent(UUID id) {
    return devices.contains(id);
  }

  @Override
  public void setColdStartTime(UUID id, long coldStartTime) {
    coldStartTimeMap.put(id, coldStartTime);
  }

  @Override
  public Optional<Long> getColdStartTime(UUID id) {
    return Optional.ofNullable(coldStartTimeMap.get(id));
  }

  @Override
  public void setDeviceInfo(UUID id, OpticalRouterDeviceInfo opticalRouterDeviceInfo) {
    deviceInfoMap.put(id, opticalRouterDeviceInfo);
  }

  @Override
  public Optional<OpticalRouterDeviceInfo> getDeviceInfo(UUID id) {
    return Optional.ofNullable(deviceInfoMap.get(id));
  }

  @Override
  public void setInventory(UUID id, OpticalRouterInventory opticalRouterInventory) {
    inventoryMap.put(id, opticalRouterInventory);
  }

  @Override
  public Optional<OpticalRouterInventory> getInventory(UUID id) {
    return Optional.ofNullable(inventoryMap.get(id));
  }

  @Override
  public void setStatus(UUID id, OpticalRouterStatus opticalRouterStatus) {
    statusMap.put(id, opticalRouterStatus);
  }

  @Override
  public Optional<OpticalRouterStatus> getStatus(UUID id) {
    return Optional.ofNullable(statusMap.get(id));
  }

  @Override
  public void clearAllData(UUID id) {
    devices.remove(id);
    coldStartTimeMap.remove(id);
    deviceInfoMap.remove(id);
    inventoryMap.remove(id);
    statusMap.remove(id);
  }

}
