/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.test;

import com.adva.nlms.opticalrouter.api.registration.BeaconMessage;
import com.adva.nlms.opticalrouter.api.registration.OpticalRouterDriverBeacon;
import com.adva.nlms.opticalrouter.api.registration.OpticalRouterDriverRegistration;
import com.adva.nlms.opticalrouter.api.registration.DriverManifest;
import com.adva.nlms.opticalrouter.api.registration.RegistrationMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
class DriverRegistration implements OpticalRouterDriverBeacon {
  private static final Logger LOGGER = LoggerFactory.getLogger(DriverRegistration.class);
  private final OpticalRouterDriverRegistration opticalRouterDriverRegistration;
  private final RegistrationConfig registrationConfig;

  @Value(value = "${app.driver.uri}")
  private String driverUri;

  @Value(value = "${app.driver.host}")
  private String driverHost;

  @ConfigurationProperties(prefix = "app.registration")
  record RegistrationConfig(DriverManifest manifest) {
  }

  public DriverRegistration(RegistrationConfig registrationConfig, OpticalRouterDriverRegistration opticalRouterDriverRegistration) {
    this.opticalRouterDriverRegistration = opticalRouterDriverRegistration;
    this.registrationConfig = registrationConfig;
  }

  @EventListener(ApplicationReadyEvent.class)
  private void sendRegistrationMessage() {
    final var message = new RegistrationMessage(String.format("%s/%s", driverHost, driverUri), registrationConfig.manifest());
    LOGGER.info("Sending registration message {}", message);
    opticalRouterDriverRegistration.register(message);
  }

  @Override
  public void process(BeaconMessage beaconMessage) {
    LOGGER.info("Received {}", beaconMessage);
    sendRegistrationMessage();
  }

}
