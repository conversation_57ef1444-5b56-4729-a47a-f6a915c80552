/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.test.commands;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;

class JsonDataLoader {

  private final ObjectMapper objectMapper;

  public JsonDataLoader(ObjectMapper objectMapper) {
    this.objectMapper = objectMapper;
  }

  public <T> T load(String resourcePath, Class<T> clazz) {
    try (InputStream is = getClass().getResourceAsStream(resourcePath)) {
      if (is == null) {
        throw new FileNotFoundException("Resource not found: " + resourcePath);
      }
      return objectMapper.readValue(is, clazz);
    } catch (IOException e) {
      throw new IllegalStateException("Failed to load JSON from " + resourcePath, e);
    }
  }

}
