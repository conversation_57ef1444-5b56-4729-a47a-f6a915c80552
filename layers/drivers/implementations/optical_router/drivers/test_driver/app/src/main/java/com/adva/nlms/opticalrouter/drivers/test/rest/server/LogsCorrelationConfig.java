/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.test.rest.server;

import com.adva.nlms.mediation.infrastructure.rest.tracing.inbound.JakartaSlf4jCorrelationFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class LogsCorrelationConfig {

  @Bean
  public JakartaSlf4jCorrelationFilter correlationFilter() {
    return new JakartaSlf4jCorrelationFilter();
  }
}
