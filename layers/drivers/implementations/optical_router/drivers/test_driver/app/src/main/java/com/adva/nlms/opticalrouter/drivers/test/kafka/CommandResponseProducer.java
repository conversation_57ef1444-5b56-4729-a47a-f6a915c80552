/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.test.kafka;

import com.adva.nlms.opticalrouter.api.commands.CommandResponse;
import com.adva.nlms.opticalrouter.api.commands.CommandResponder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;

class CommandResponseProducer implements CommandResponder {
  private static final Logger LOGGER = LoggerFactory.getLogger(CommandResponseProducer.class);
  private final KafkaTemplate<String, CommandResponse> kafkaTemplate;
  private final String topicName;

  CommandResponseProducer(String topicName, KafkaTemplate<String, CommandResponse> kafkaTemplate) {
    this.topicName = topicName;
    this.kafkaTemplate = kafkaTemplate;
  }

  @Override
  public void process(CommandResponse commandResponse) {
    try {
      kafkaTemplate.send(topicName, commandResponse)
        .exceptionally(throwable -> {
          LOGGER.error("Failed to send {} to {} topic during commandResponse sending", commandResponse, topicName, throwable);
          return null;
        })
        .get();
      LOGGER.info("Successfully sent {} to {} topic", commandResponse, topicName);
    } catch (InterruptedException e) {
      LOGGER.error("Failed to send {} to {} topic during connection to kafka", commandResponse, topicName, e);
      Thread.currentThread().interrupt();
    } catch (Exception e) {
      LOGGER.error("Failed to send {} to {} topic during connection to kafka", commandResponse, topicName, e);
    }
  }
}
