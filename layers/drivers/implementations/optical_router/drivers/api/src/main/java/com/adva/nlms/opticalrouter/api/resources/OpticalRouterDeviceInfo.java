/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.api.resources;

public record OpticalRouterDeviceInfo(
  String deviceType,
  String name,
  String serialNumber,
  String softwareVersion,
  String semanticSoftwareVersion
) implements OpticalRouterSynchronizationData {
  public OpticalRouterDeviceInfo() {
    this(null, null, null, null, null);
  }

  public OpticalRouterDeviceInfo withName(final String name) {
    return new OpticalRouterDeviceInfo(deviceType, name, serialNumber, softwareVersion, semanticSoftwareVersion);
  }
}
