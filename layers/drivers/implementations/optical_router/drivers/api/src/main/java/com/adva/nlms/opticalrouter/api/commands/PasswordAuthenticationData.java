/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.api.commands;

import com.adva.nlms.opticalrouter.api.registration.AuthenticationMethod;

public record PasswordAuthenticationData(
  String username,
  String password
) implements AuthenticationData {
  @Override
  public AuthenticationMethod type() {
    return AuthenticationMethod.PAP;
  }

  @Override
  public String toString() {
    return "PasswordAuthenticationData{username='" + username + "'}";
  }
}
