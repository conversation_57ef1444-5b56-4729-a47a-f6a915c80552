/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.api.commands;

import com.adva.nlms.opticalrouter.api.registration.AuthenticationMethod;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

@JsonTypeInfo(
  use = JsonTypeInfo.Id.NAME,
  property = "type"
)
@JsonSubTypes({
  @JsonSubTypes.Type(value = PasswordAuthenticationData.class, name = "PAP"),
})
public sealed interface AuthenticationData permits PasswordAuthenticationData {
  AuthenticationMethod type();
}
