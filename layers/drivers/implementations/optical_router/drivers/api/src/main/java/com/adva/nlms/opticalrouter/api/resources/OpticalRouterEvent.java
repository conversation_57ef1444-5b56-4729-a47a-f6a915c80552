/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.api.resources;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.JsonNode;

@JsonTypeInfo(
  use = JsonTypeInfo.Id.NAME,
  property = "type"
)
@JsonSubTypes({
  @JsonSubTypes.Type(value = OpticalRouterEvent.EntityCreated.class, name = "CREATE"),
  @JsonSubTypes.Type(value = OpticalRouterEvent.EntityDeleted.class, name = "DELETE"),
  @JsonSubTypes.Type(value = OpticalRouterEvent.EntityUpdated.class, name = "UPDATE"),
  @JsonSubTypes.Type(value = OpticalRouterEvent.EntityStateChanged.class, name = "STATE"),
  @JsonSubTypes.Type(value = OpticalRouterEvent.EntityAlarmed.class, name = "ALARM"),
})
public sealed interface OpticalRouterEvent permits
  OpticalRouterEvent.EntityCreated,
  OpticalRouterEvent.EntityDeleted,
  OpticalRouterEvent.EntityUpdated,
  OpticalRouterEvent.EntityStateChanged,
  OpticalRouterEvent.EntityAlarmed {

  record EntityCreated(
    JsonNode data // OpticalPort|OpticalPlug
  ) implements OpticalRouterEvent {
  }

  record EntityDeleted(
  ) implements OpticalRouterEvent {
  }

  record EntityUpdated(
    JsonNode data // OpticalPort|OpticalPlug json-patch
  ) implements OpticalRouterEvent {
  }

  record EntityStateChanged(
    AdminState adminState,
    OperationalState operationalState
  ) implements OpticalRouterEvent {
  }

  enum Direction {RX, TX, BI, NA}

  enum Severity {CRITICAL, CLEAR}

  record EntityAlarmed(
    String alarmType, // alarm short name in xml
    Direction direction,
    Severity severity,
    long detectionTimestamp // epoch time in ms
  ) implements OpticalRouterEvent {
  }

}
