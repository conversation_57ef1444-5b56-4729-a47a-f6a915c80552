/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.api.resources;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

@JsonTypeInfo(
  use = JsonTypeInfo.Id.NAME,
  property = "type"
)
@JsonSubTypes({
  @JsonSubTypes.Type(value = OpticalRouterDeviceInfo.class, name = "DEVICE_INFO"),
  @JsonSubTypes.Type(value = OpticalRouterInventory.class, name = "INVENTORY"),
  @JsonSubTypes.Type(value = OpticalRouterStatus.class, name = "STATUS"),
  @JsonSubTypes.Type(value = OpticalRouterConnectionFaultCause.class, name = "CONNECTION_FAULT"),
  @JsonSubTypes.Type(value = OpticalRouterNotification.class, name = "NOTIFICATION"),
})
public sealed interface OpticalRouterSynchronizationData
  permits OpticalRouterConnectionFaultCause, OpticalRouterDeviceInfo, OpticalRouterInventory, OpticalRouterStatus, OpticalRouterNotification{
}
