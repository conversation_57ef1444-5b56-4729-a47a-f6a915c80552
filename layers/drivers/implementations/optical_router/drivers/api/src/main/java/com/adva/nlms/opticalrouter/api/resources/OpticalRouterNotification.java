/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.api.resources;

public record OpticalRouterNotification(
  String entityLabel, // label like the one used in OpticalPort or OpticalPlug
  EntityType entityType,
  long timestamp,  // epoch time in ms
  OpticalRouterEvent event
) implements OpticalRouterSynchronizationData {
  public enum EntityType {
    PORT, PLUG
  }
}
