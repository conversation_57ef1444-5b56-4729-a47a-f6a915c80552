/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.api.commands;

public class OpticalRouterCommandException extends RuntimeException {
  private final CommandStatus commandStatus;

  public OpticalRouterCommandException(CommandStatus status, String message) {
    super(message);
    this.commandStatus = status;
  }

  public OpticalRouterCommandException(CommandStatus status, String message, Throwable cause) {
    super(message, cause);
    this.commandStatus = status;
  }

  public CommandStatus getCommandStatus() {
    return commandStatus;
  }
}
