/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.api.resources;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import java.math.BigDecimal;


@JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION)
@JsonSubTypes({
  @JsonSubTypes.Type(OpticalChannel.FrequencySlot.class),
  @JsonSubTypes.Type(OpticalChannel.Wavelength.class)}
)
public sealed interface OpticalChannel
  permits OpticalChannel.FrequencySlot, OpticalChannel.Wavelength {
  // DWDM
  record FrequencySlot(
    // frequency is in THz as per ITU-T G.694.1
    BigDecimal centerFrequency,
    // slotWidth is in GHz as per ITU-T G.694.1
    BigDecimal slotWidth
  ) implements OpticalChannel {
  }

  //CWDM GRAY
  // nm as per ITU-T G.694.2
  record Wavelength(
    BigDecimal wavelength
  ) implements OpticalChannel {
  }
}
