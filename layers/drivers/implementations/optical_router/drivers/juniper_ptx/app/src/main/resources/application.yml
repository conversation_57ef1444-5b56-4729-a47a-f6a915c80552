server:
  port: "${JUNIPER_PTX_APP_SERVER_PORT:9008}"

spring:
  kafka:
    bootstrap-servers: "${KAFKA_BOOTSTRAP_SERVERS:127.0.0.1:9094}"
  devtools:
    restart:
      enabled: false

management:
  endpoints:
    enabled-by-default: false
  endpoint:
    health:
      enabled: true

app:
  registration:
    # configuration should reflect com.adva.nlms.opticalrouter.api.registration.DriverManifest
    manifest:
      name: Jun<PERSON> PTX
      abbreviated-name: PTX
      version: 0.0.1
      device-types:
        - name: PTX10001-36MR
          minimum-version: 24.4R1.8-EVO
          type-id: 1000001
          authentication-methods: [PAP]
        - name: PTX10003
          minimum-version: 24.4R1.8-EVO
          type-id: 1000002
          authentication-methods: [PAP]
        - name: PTX10002-36QDD
          minimum-version: 24.4R1.8-EVO
          type-id: 1000003
          authentication-methods: [PAP]
        - name: PTX10004
          minimum-version: 24.4R1.8-EVO
          type-id: 1000004
          authentication-methods: [PAP]
        - name: PTX10008
          minimum-version: 24.4R1.8-EVO
          type-id: 1000005
          authentication-methods: [PAP]
        - name: PTX10016
          minimum-version: 24.4R1.8-EVO
          type-id: 1000006
          authentication-methods: [PAP]

  netconf:
    local-username: adva
    session-name: "cfg"
    keep-alive:
      interval: 15
      max-alive-count: 4

  kafka:
    group-id: "optical-router-driver-group"
    registration-topic:
      name: "v1.drv.optical-router.registration"
      partitions: 1
      replicas: 1
    beacon-topic:
      name: "v1.drv.optical-router.beacon"
      partitions: 1
      replicas: 1
    command-responses-topic:
      name: "v1.drv.optical-router.command-responses"
      partitions: 1
      replicas: 1

  driver:
    host: ${JUNIPER_PTX_DRIVER_APP:http://127.0.0.1:9008}
    uri: "enc/v1/drv/juniper-ptx"

enc:
  server-base: "${ENC_SERVER_BASE:https://localhost:8443}"

http-client:
  verify-certs: ${VERIFY_CERTS:false}