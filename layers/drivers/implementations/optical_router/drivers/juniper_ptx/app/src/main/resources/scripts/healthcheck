#!/bin/bash
#
# Copyright 2024 Adtran Networks SE. All rights reserved.
#
# Owner: dimitriosl
#

cd "$(dirname "$0")"
. ./funcs

# Read the token
LOCAL_AUTH_TOKEN="$(get_secrets)"
# Check error
if [ $? -ne 0 ] ; then
   exit 1
fi

# Execute the HTTP request with curl and get the response header and body
res=$(curl -s --fail --show-error --location --request GET 'http://localhost:9008/actuator/health' --header "token: ${LOCAL_AUTH_TOKEN}" --header 'Accept-Encoding: gzip, deflate, br')

# Check error
if [ $? -ne 0 ] ; then
   >&2 echo "$res"
   exit 1
fi
echo ${res}
