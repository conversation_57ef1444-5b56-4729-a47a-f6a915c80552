/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.tailf.jnc.Device;
import com.tailf.jnc.DeviceUser;
import com.tailf.jnc.JNCException;
import com.tailf.jnc.SSHConnection;
import net.schmizz.keepalive.KeepAliveProvider;
import net.schmizz.keepalive.KeepAliveRunner;
import net.schmizz.sshj.Config;
import net.schmizz.sshj.DefaultConfig;
import net.schmizz.sshj.transport.DisconnectListener;

import java.io.IOException;

class JncDevice extends Device {
  private final int keepAliveInterval;
  private final int maxAliveCount;

  JncDevice(String name, DeviceUser user, String mgmtIp, int mgmtPort, int keepAliveInterval, int maxAliveCount) {
    super(name, user, mgmtIp, mgmtPort);
    this.keepAliveInterval = keepAliveInterval;
    this.maxAliveCount = maxAliveCount;
  }

  @SuppressWarnings("java:S2095")
  void connect(String localUser, int timeoutMs, DisconnectListener disconnectListener)
    throws IOException, JNCException {
    Config config = new DefaultConfig();
    config.setKeepAliveProvider(KeepAliveProvider.KEEP_ALIVE);
    var con = new JncSSHConnection();
    con.setHostVerification(null);
    con.connect(mgmt_ip, mgmt_port, timeoutMs, disconnectListener, keepAliveInterval, maxAliveCount);
    this.con = con;
    authenticate(localUser);
  }

  private static class JncSSHConnection extends SSHConnection {
    void connect(String host, int port, int timeoutMs, DisconnectListener disconnectListener, int keepAliveInterval, int maxAliveCount) throws IOException {
      var client = getClient();
      client.setTimeout(timeoutMs); // socket timeout
      client.setConnectTimeout(timeoutMs); // missing in JNC
      client.getTransport().setTimeoutMs(timeoutMs);
      client.getTransport().setDisconnectListener(disconnectListener);
      if (client.getConnection().getKeepAlive() instanceof KeepAliveRunner keepAlive) {
        keepAlive.setKeepAliveInterval(keepAliveInterval);
        keepAlive.setMaxAliveCount(maxAliveCount);
      }
      client.connect(host, port);
    }
  }
}
