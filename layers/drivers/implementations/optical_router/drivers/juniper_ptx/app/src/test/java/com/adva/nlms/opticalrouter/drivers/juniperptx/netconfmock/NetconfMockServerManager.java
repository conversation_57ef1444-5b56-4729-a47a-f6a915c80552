/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock;

import org.apache.sshd.server.SshServer;
import org.thymeleaf.ITemplateEngine;

import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

@SuppressWarnings("unused")
public class NetconfMockServerManager {
  private final ITemplateEngine templateEngine;

  private final Set<NetconfCommand> netconfCommands = new HashSet<>();
  private final AtomicReference<String> currentHost = new AtomicReference<>();
  private final AtomicInteger currentPort = new AtomicInteger(0);
  private HashMap<Object, Object> requestCounter;
  private SshServer sshServer;

  public NetconfMockServerManager(ITemplateEngine templateEngine) {
    this.templateEngine = templateEngine;
  }

  public String getCurrentHost() {
    return currentHost.get();
  }

  public void setCurrentHost(String currentHost) {
    this.currentHost.set(currentHost);
  }

  public int getCurrentPort() {
    return currentPort.get();
  }

  public void setCurrentPort(int currentPort) {
    this.currentPort.set(currentPort);
  }

  public NetconfCommand requestContains(String phrase) {
    var commandHandler = new NetconfCommand(templateEngine, phrase);
    netconfCommands.add(commandHandler);
    return commandHandler;
  }

  public NetconfCommand requestStartsWith(String phrase) {
    var commandHandler = new NetconfCommand(templateEngine, phrase, MatchingStrategy.STARTS_WITH);
    netconfCommands.add(commandHandler);
    return commandHandler;
  }

  public NetconfCommand requestMatches(String pattern) {
    var commandHandler = new NetconfCommand(templateEngine, pattern, MatchingStrategy.REGEX);
    netconfCommands.add(commandHandler);
    return commandHandler;
  }

  Optional<NetconfCommand> handleRequest(String request) {
    return netconfCommands.stream()
      .filter(h -> h.matches(request))
      .findFirst();
  }

  public Set<NetconfCommand> getNetconfCommands() {
    return netconfCommands;
  }

  public void clear() {
    netconfCommands.clear();
  }

  public void setSshServer(SshServer sshd) {
    this.sshServer = sshd;
  }
  public void stopServerImmediately() throws IOException {
    sshServer.stop(true);
  }
}
