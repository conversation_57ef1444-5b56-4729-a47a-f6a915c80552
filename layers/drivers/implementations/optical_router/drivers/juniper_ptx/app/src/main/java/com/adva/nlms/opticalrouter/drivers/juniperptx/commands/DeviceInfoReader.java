/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.api.resources.OpticalRouterDeviceInfo;
import com.tailf.jnc.Element;

class DeviceInfoReader {

  private static final String JUNOS_VERSION_PATH = "software-information/junos-version";
  private static final String PRODUCT_MODEL_PATH = "software-information/product-model";
  private static final String HOST_NAME_PATH = "software-information/host-name";
  private static final String SERIAL_NUMBER_PATH = "chassis-inventory/chassis/serial-number";

  private DeviceInfoReader() {}

  static OpticalRouterDeviceInfo readDeviceInfo(NetconfDataProvider netconfDataProvider) {
    OpticalRouterDeviceInfoBuilder builder = OpticalRouterDeviceInfoBuilder.builder();

    processSoftwareInformationCommand(netconfDataProvider, builder);
    processChassisInventoryCommand(netconfData<PERSON>rov<PERSON>, builder);

    return builder.build();
  }

  private static void processSoftwareInformationCommand(NetconfDataProvider netconfDataProvider, OpticalRouterDeviceInfoBuilder builder) {
    Element softwareInformationResponse = netconfDataProvider.getElement(RpcCommands.GET_SOFTWARE_INFORMATION);

    String deviceType = NetconfReader.getValue(softwareInformationResponse, PRODUCT_MODEL_PATH);
    String name = NetconfReader.getValue(softwareInformationResponse, HOST_NAME_PATH);
    String version = NetconfReader.getValue(softwareInformationResponse, JUNOS_VERSION_PATH);

    builder.setDeviceType(deviceType)
      .setName(name)
      .setSoftwareVersion(version)
      .setSemanticSoftwareVersion(SemanticVersionParser.parseToSemantic(version));
  }

  private static void processChassisInventoryCommand(NetconfDataProvider netconfDataProvider, OpticalRouterDeviceInfoBuilder builder) {
    Element chassisInventoryResponse = netconfDataProvider.getElement(RpcCommands.GET_CHASSIS_INVENTORY);
    String serialNumber = NetconfReader.getValue(chassisInventoryResponse, SERIAL_NUMBER_PATH);
    builder.setSerialNumber(serialNumber);
  }

  @SuppressWarnings("UnusedReturnValue")
  private static class OpticalRouterDeviceInfoBuilder {

    private String deviceType;
    private String name;
    private String serialNumber;
    private String softwareVersion;
    private String semanticSoftwareVersion;

    private OpticalRouterDeviceInfoBuilder() {
    }

    private OpticalRouterDeviceInfoBuilder setDeviceType(String deviceType) {
      this.deviceType = deviceType;
      return this;
    }

    private OpticalRouterDeviceInfoBuilder setName(String name) {
      this.name = name;
      return this;
    }

    private OpticalRouterDeviceInfoBuilder setSerialNumber(String serialNumber) {
      this.serialNumber = serialNumber;
      return this;
    }

    private OpticalRouterDeviceInfoBuilder setSoftwareVersion(String softwareVersion) {
      this.softwareVersion = softwareVersion;
      return this;
    }

    private OpticalRouterDeviceInfoBuilder setSemanticSoftwareVersion(String semanticSoftwareVersion) {
      this.semanticSoftwareVersion = semanticSoftwareVersion;
      return this;
    }

    private OpticalRouterDeviceInfo build() {
      return new OpticalRouterDeviceInfo(deviceType, name, serialNumber, softwareVersion, semanticSoftwareVersion);
    }

    private static OpticalRouterDeviceInfoBuilder builder() {
      return new OpticalRouterDeviceInfoBuilder();
    }

  }

}
