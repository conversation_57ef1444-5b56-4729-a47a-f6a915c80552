/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.api.commands.CommandResponse;
import com.adva.nlms.opticalrouter.api.commands.CommunicationSettings;
import com.adva.nlms.opticalrouter.api.commands.OpticalRouterDeviceDriver;
import com.adva.nlms.opticalrouter.api.commands.PasswordAuthenticationData;
import com.adva.nlms.opticalrouter.api.commands.Status;
import com.adva.nlms.opticalrouter.api.resources.ConnectionFaultCause;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterConnectionFaultCause;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.NetconfMockServerConfiguration;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.NetconfMockServerManager;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.NetconfServerConstants;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.ResponseTemplate;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.junit.NetconfMockingExtension;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.junit.NetconfServer;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(classes = {CommandConfiguration.class, NetconfMockServerConfiguration.class, TestCommandResponderConfiguration.class, TestExecutorServiceConfiguration.class})
@ExtendWith(NetconfMockingExtension.class)
@NetconfServer
public class OpticalRouterStopsRespondingIntTest {
  private static final int TIMEOUT = 5;
  private static final UUID DEVICE_ID = UUID.fromString("d0baf626-c566-47c8-9ba5-390437d0913a");

  @Autowired
  private OpticalRouterDeviceDriver opticalRouterDeviceDriver;
  @Autowired
  private NetconfMockServerManager manager;
  @Autowired
  private NetconfDeviceRepository netconfDeviceRepository;
  @Autowired
  private TestCommandResponderConfiguration.TestCommandResponder responder;

  private CommunicationSettings communicationSettings;

  @BeforeEach
  void setUp() {
    communicationSettings = new CommunicationSettings("TestDevice",
      manager.getCurrentHost(),
      manager.getCurrentPort(),
      TIMEOUT,
      new PasswordAuthenticationData(NetconfServerConstants.DEFAULT_USERNAME, NetconfServerConstants.DEFAULT_PASSWORD));
  }

  @Test
  //because of NetconfMockingExtension limitation to restart server when the previous one has closed/stopped it, this class must contain one and only one test
  void connectAndIdleThenConnectionCrushes() throws IOException {
    manager.requestContains("<hello")
      .respondWithTemplate(ResponseTemplate.HELLO)
      .andTemplateParameter("sessionId", 123);

    opticalRouterDeviceDriver.connect(DEVICE_ID, communicationSettings);
    var beforeServerCrush = netconfDeviceRepository.findDevice(DEVICE_ID);
    assertThat(beforeServerCrush).isPresent();
    manager.stopServerImmediately();

    var actualResponse = responder.getLastResponse();
    assertThat(actualResponse).extracting(CommandResponse::deviceId, CommandResponse::status)
      .containsExactly(DEVICE_ID, Status.FAILURE);
    var responseData = actualResponse.data();
    assertThat(responseData).asInstanceOf(InstanceOfAssertFactories.type(OpticalRouterConnectionFaultCause.class))
      .extracting(OpticalRouterConnectionFaultCause::connectionFaultCause)
      .isEqualTo(ConnectionFaultCause.DEVICE_DISCONNECTED);
  }
}
