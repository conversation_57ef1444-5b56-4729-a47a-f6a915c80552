/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.tailf.jnc.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

class OpticalInterfacesReader {
  private static final Logger log = LoggerFactory.getLogger(OpticalInterfacesReader.class);
  private static final Pattern INTERFACE_ID_PATTERN = Pattern.compile("et-(?<fpc>\\d+)/(?<pic>\\d+)/(?<port>\\d+)");

  private OpticalInterfacesReader() {
    throw new UnsupportedOperationException();
  }

  static Set<InterfaceId> getOpticalInterfaces(NetconfDataProvider dataProvider) {
    Objects.requireNonNull(dataProvider);
    Element opticalInterfaces = dataProvider.getElement(RpcCommands.GET_INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS);

    return NetconfReader.getValues(opticalInterfaces, DevicePortsReader.PHYSICAL_INTERFACE_NAME_PATH)
      .stream()
      .map(OpticalInterfacesReader::parseInterface)
      .filter(Objects::nonNull)
      .collect(Collectors.toSet());
  }

  private static InterfaceId parseInterface(String input) {
    var matcher = INTERFACE_ID_PATTERN.matcher(input);

    if (!matcher.matches()) {
      log.warn("Input interface id {} does not match format: et-<fpc>/<pic>/<port>", input);
      return null;
    }

    return new InterfaceId(input, matcher.group("fpc"), matcher.group("pic"), matcher.group("port"));
  }
}
