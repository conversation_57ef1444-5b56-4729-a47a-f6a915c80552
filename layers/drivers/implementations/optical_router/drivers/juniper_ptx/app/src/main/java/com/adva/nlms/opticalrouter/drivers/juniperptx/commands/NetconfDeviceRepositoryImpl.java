/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.tailf.jnc.Device;

import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

class NetconfDeviceRepositoryImpl implements NetconfDeviceRepository {
  private final Map<UUID, Device> devices = new ConcurrentHashMap<>();

  @Override
  public void addDevice(UUID id, Device device) {
    devices.put(id, device);
  }

  @Override
  public Optional<Device> removeDevice(UUID id) {
    return Optional.ofNullable(devices.remove(id));
  }

  @Override
  public Optional<Device> findDevice(UUID id) {
    return Optional.ofNullable(devices.get(id));
  }
}
