/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.drivers.juniperptx.exception.NetconfException;
import org.assertj.core.api.ThrowableAssert;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class OpticalInterfacesReaderTest {
  private static final String INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS_RESPONSE_XML_FILE_PATH = "plugs/interface-optics-applications-diagnostics.xml";

  private final NetconfDataProvider netconfDataProvider = mock();
  @Test
  void getOpticalInterfacesWhenNullDataProviderPassed() {
    //when
    ThrowableAssert.ThrowingCallable actual = () -> OpticalInterfacesReader.getOpticalInterfaces(null);

    //then
    assertThatThrownBy(actual).isInstanceOf(NullPointerException.class);
  }

  @Test
  void getOpticalInterfacesWhenDataProviderThrowsNetconfException() {
    //given
    when(netconfDataProvider.getElement(RpcCommands.GET_INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS)).thenThrow(NetconfException.class);

    //when
    ThrowableAssert.ThrowingCallable actual = () -> OpticalInterfacesReader.getOpticalInterfaces(netconfDataProvider);

    //then
    assertThatThrownBy(actual).isInstanceOf(NetconfException.class);
  }

  @Test
  void getOpticalInterfacesHappyPath() throws Exception {
    //given
    var interfaceOpticsDiagnostics = JncElementHelper.parseXmlFileToElement(INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS_RESPONSE_XML_FILE_PATH);
    when(netconfDataProvider.getElement(RpcCommands.GET_INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS)).thenReturn(interfaceOpticsDiagnostics);

    //when
    var actual = OpticalInterfacesReader.getOpticalInterfaces(netconfDataProvider);

    //then
    assertThat(actual).extracting(InterfaceId::fullName)
      .containsExactlyInAnyOrder("et-0/0/0", "et-0/0/2");
  }
}