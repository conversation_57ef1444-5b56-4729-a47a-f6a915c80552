/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock;

import java.util.Objects;
import java.util.function.BiFunction;

public enum MatchingStrategy {
  STARTS_WITH(String::startsWith),
  EQUALS(Objects::equals),
  CONTAINS(String::contains),
  REGEX(String::matches);
  private final BiFunction<String, String, Boolean> strategy;

  MatchingStrategy(BiFunction<String,String, Boolean> strategy) {
    this.strategy = strategy;
  }

  public BiFunction<String, String, Boolean> getStrategy() {
    return strategy;
  }

}
