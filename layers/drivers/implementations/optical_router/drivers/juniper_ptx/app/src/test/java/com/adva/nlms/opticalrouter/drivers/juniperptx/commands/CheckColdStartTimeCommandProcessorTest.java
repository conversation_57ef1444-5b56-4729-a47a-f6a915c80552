/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.drivers.juniperptx.exception.NetconfException;
import org.assertj.core.api.ThrowableAssert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CheckColdStartTimeCommandProcessorTest {

  private static final UUID DEVICE_ID = UUID.fromString("d0baf626-c566-47c8-9ba5-390437d0913a");
  private static final String XML_FILE_PATH = "cold-start-time/cold-start-time.xml";
  private static final String XML_FILE_PATH_BAD_RESPONSE = "cold-start-time/cold-start-time-bad-response.xml";

  @Mock
  NetconfDataProviderFactory netconfDataProviderFactory;

  @InjectMocks
  CheckColdStartTimeCommandProcessor checkColdStartTimeCommandProcessor;

  @Test
  void invokeCheckColdStartTime_missingNeId() {
    //when
    ThrowableAssert.ThrowingCallable actual = () -> checkColdStartTimeCommandProcessor.checkColdStartTime(null);

    //then
    assertThatThrownBy(actual).isInstanceOf(NullPointerException.class);
  }


  @Test
  void invokeCheckColdStartTime_throwNetconfExceptionWhenGetElement() {
    //given
    NetconfDataProvider dataProvider = mock();
    when(netconfDataProviderFactory.newDataProvider(DEVICE_ID)).thenReturn(dataProvider);
    when(dataProvider.getElement(RpcCommands.GET_SYSTEM_UPTIME_INFORMATION)).thenThrow(NetconfException.class);

    //when
    ThrowableAssert.ThrowingCallable actual = () -> checkColdStartTimeCommandProcessor.checkColdStartTime(DEVICE_ID);

    //then
    assertThatThrownBy(actual).isInstanceOf(NetconfException.class);
  }

  @Test
  void invokeCheckColdStartTime_throwNullPointerExceptionWhileParsingResponse() {
    //given
    NetconfDataProvider dataProvider = mock();
    when(netconfDataProviderFactory.newDataProvider(DEVICE_ID)).thenReturn(dataProvider);
    when(dataProvider.getElement(RpcCommands.GET_SYSTEM_UPTIME_INFORMATION)).thenReturn(null);

    //when
    ThrowableAssert.ThrowingCallable actual = () -> checkColdStartTimeCommandProcessor.checkColdStartTime(DEVICE_ID);

    //then
    assertThatThrownBy(actual).isInstanceOf(NullPointerException.class);
  }

  @Test
  void invokeCheckColdStartTime_throwNumberFormatExceptionWhileParsingLong() throws Exception {
    //given
    NetconfDataProvider dataProvider = mock();
    var responseFromDevice = JncElementHelper.parseXmlFileToElement(XML_FILE_PATH_BAD_RESPONSE);
    when(netconfDataProviderFactory.newDataProvider(DEVICE_ID)).thenReturn(dataProvider);
    when(dataProvider.getElement(RpcCommands.GET_SYSTEM_UPTIME_INFORMATION)).thenReturn(responseFromDevice);

    //when
    ThrowableAssert.ThrowingCallable actual = () -> checkColdStartTimeCommandProcessor.checkColdStartTime(DEVICE_ID);

    //then
    assertThatThrownBy(actual).isInstanceOf(NetconfException.class)
      .hasMessage("Error while parsing seconds for checking cold start time of node " + DEVICE_ID);
  }

  @Test
  void invokeCheckColdStartTime_happyPath() throws Exception {
    //given
    NetconfDataProvider dataProvider = mock();
    var responseFromDevice = JncElementHelper.parseXmlFileToElement(XML_FILE_PATH);
    when(netconfDataProviderFactory.newDataProvider(DEVICE_ID)).thenReturn(dataProvider);
    when(dataProvider.getElement(RpcCommands.GET_SYSTEM_UPTIME_INFORMATION)).thenReturn(responseFromDevice);

    //when
    var actual = checkColdStartTimeCommandProcessor.checkColdStartTime(DEVICE_ID);

    //then
    assertThat(actual).isEqualTo(1234567890L);
  }

}