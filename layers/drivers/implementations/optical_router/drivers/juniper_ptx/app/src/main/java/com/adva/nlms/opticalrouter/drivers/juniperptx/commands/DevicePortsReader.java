/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.nrl.creator.api.NetworkResourceLocator;
import com.adva.nlms.nrl.creator.api.exception.WrongNRLFormatException;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.opticalrouter.api.resources.AdminState;
import com.adva.nlms.opticalrouter.api.resources.OperationalState;
import com.adva.nlms.opticalrouter.api.resources.OpticalChannel;
import com.adva.nlms.opticalrouter.api.resources.OpticalConfiguration;
import com.adva.nlms.opticalrouter.api.resources.OpticalPort;
import com.adva.nlms.opticalrouter.drivers.juniperptx.exception.NetconfException;
import com.tailf.jnc.Element;
import com.tailf.jnc.NodeSet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

import static java.util.Objects.requireNonNull;

class DevicePortsReader {
  private static final Logger LOGGER = LoggerFactory.getLogger(DevicePortsReader.class);

  private static final String OPTICS_PROPERTIES_PATH = "interface-information/physical-interface/optics-properties";
  static final String PHYSICAL_INTERFACE_NAME_PATH = "interface-information/physical-interface/name";
  private static final String PHYSICAL_INTERFACE_SPEED_PATH = "interface-information/physical-interface/speed";
  private static final String PHYSICAL_INTERFACE_ADMIN_STATUS_PATH = "interface-information/physical-interface/admin-status";
  private static final String PHYSICAL_INTERFACE_OPER_STATUS_PATH = "interface-information/physical-interface/oper-status";
  private static final String PHYSICAL_INTERFACE_TX_POWER_PATH = "nc:data/configuration/interfaces/interface/optics-options/tx-power";
  private static final String PHYSICAL_INTERFACE_FREQUENCY_PATH = "interface-information/physical-interface/optics-properties/frequency";
  private static final String PHYSICAL_INTERFACE_MEDIA_CODE_DESC_PATH = "interface-information/physical-interface/optics-properties/media-code-desc";

  private static final BigDecimal BIG_DECIMAL_100 = BigDecimal.valueOf(100.0);
  private static final BigDecimal BIG_DECIMAL_75 = BigDecimal.valueOf(75.0);
  private static final BigDecimal BIG_DECIMAL_37_5 = BigDecimal.valueOf(37.5);

  private DevicePortsReader() {}

  static List<OpticalPort> readPorts(NetconfDataProvider dataProvider, Set<InterfaceId> opticalInterfaces) {
    requireNonNull(dataProvider);

    if (opticalInterfaces == null || opticalInterfaces.isEmpty()) {
      return List.of();
    }

    return opticalInterfaces.stream()
      .map(interfaceId -> getPortsData(dataProvider, interfaceId.fullName()))
      .filter(Objects::nonNull)
      .toList();
  }

  private static OpticalPort getPortsData(NetconfDataProvider dataProvider, String opticalPortName) {
    requireNonNull(opticalPortName);

    LOGGER.info("Initiating data retrieval for port: {}, neId: {}", opticalPortName, dataProvider);

    Element interfaceInformation = dataProvider.getElement(String.format(RpcCommands.GET_INTERFACE_INFORMATION, opticalPortName));
    NodeSet opticsProperties = NetconfReader.getNodeSet(interfaceInformation, OPTICS_PROPERTIES_PATH);

    if (CollectionUtils.isEmpty(opticsProperties)) {
      LOGGER.info("No optics properties found for port: {}, neId: {}", opticalPortName, dataProvider.getNeId());
      return null;
    }

    OpticalPortBuilder builder = OpticalPortBuilder.builder();

    processBasicInfo(builder, interfaceInformation, dataProvider.getNeId());
    processOpticalConfiguration(builder, interfaceInformation, dataProvider, opticalPortName);

    LOGGER.debug("Gathered all data for port: {}, neId: {}", opticalPortName, dataProvider.getNeId());
    return builder.build();
  }

  private static void processBasicInfo(OpticalPortBuilder builder, Element interfaceInformation, UUID neId) {
    String name = NetconfReader.getValue(interfaceInformation, PHYSICAL_INTERFACE_NAME_PATH);
    builder.setNetworkResourceLocator(createNrl(neId, name));
    builder.setLabel(name);
    builder.setPayload(mapPayload(NetconfReader.getValue(interfaceInformation, PHYSICAL_INTERFACE_SPEED_PATH)));
    builder.setAdminState(EnumMapper.mapAdminState(NetconfReader.getValue(interfaceInformation, PHYSICAL_INTERFACE_ADMIN_STATUS_PATH)));
    builder.setOperationalState(EnumMapper.mapOperationalState(NetconfReader.getValue(interfaceInformation, PHYSICAL_INTERFACE_OPER_STATUS_PATH)));
  }

  private static void processOpticalConfiguration(OpticalPortBuilder builder, Element interfaceInformation, NetconfDataProvider dataProvider, String opticalPortName) {
    Element config = dataProvider.getElement(String.format(RpcCommands.GET_CONFIG_RUNNING_WITH_TX_POWER, opticalPortName));

    String targetOutputPower = NetconfReader.getValue(config, PHYSICAL_INTERFACE_TX_POWER_PATH);
    String centerFrequency = NetconfReader.getValue(interfaceInformation, PHYSICAL_INTERFACE_FREQUENCY_PATH);
    String slotWidth = NetconfReader.getValue(interfaceInformation, PHYSICAL_INTERFACE_MEDIA_CODE_DESC_PATH);

    OpticalChannel opticalChannel = new OpticalChannel.FrequencySlot(new BigDecimal(centerFrequency), mapSlotWidth(slotWidth));
    OpticalConfiguration opticalConfiguration = new OpticalConfiguration(List.of(opticalChannel), targetOutputPower != null ? new BigDecimal(targetOutputPower) : null);

    builder.setOpticalConfiguration(opticalConfiguration);
  }

  private static String createNrl(UUID neId, String port) {
    requireNonNull(neId);
    requireNonNull(port);

    NetworkResourceLocator.Builder nrlBuilder = new NetworkResourceLocator.Builder();
    nrlBuilder.neUUID(neId);
    nrlBuilder.port(StringUtils.replace(port, "/", "-"));

    try {
      return nrlBuilder.build().toString();
    } catch (WrongNRLFormatException e) {
      throw new NetconfException(String.format("Error occurred during NRL creation with neId: %s, port: %s", neId, port));
    }
  }

  private static String mapPayload(String payload) {
    return switch (payload) {
      case "400Gbps" -> LayerQualifier.ET400ZR.toString();
      case "100Gbps" -> LayerQualifier.ET100ZR.toString();
      default -> throw new IllegalStateException("Unexpected payload: " + payload);
    };
  }

  private static BigDecimal mapSlotWidth(String slotWidth) {
    if (isSubsequence("100OFECQPSK", slotWidth)) {
      return BIG_DECIMAL_37_5;
    } else if (isSubsequence("400OFEC16Q", slotWidth)
      || isSubsequence("300OFEC8QAM", slotWidth)
      || isSubsequence("200OFECQPSK", slotWidth)
      || Objects.equals("400ZR, DWDM, amplified", slotWidth)
      || Objects.equals("400ZR, single wavelength", slotWidth)
    ) {
      return BIG_DECIMAL_75;
    } else if (isSubsequence("400OFEC8QAM", slotWidth)) {
      return BIG_DECIMAL_100;
    } else {
      return BIG_DECIMAL_37_5;
    }
  }

  private static boolean isSubsequence(String query, String target) {
    query = query.toLowerCase();
    target = target.toLowerCase();

    int q = 0;
    for (int t = 0; t < target.length() && q < query.length(); t++) {
      if (query.charAt(q) == target.charAt(t)) {
        q++;
      }
    }
    return q == query.length();
  }

  @SuppressWarnings("UnusedReturnValue")
  static class OpticalPortBuilder {

    private String networkResourceLocator;
    private String label;
    private String payload;
    private AdminState adminState;
    private OperationalState operationalState;
    private OpticalConfiguration opticalConfiguration;

    private OpticalPortBuilder() {
    }

    OpticalPortBuilder setNetworkResourceLocator(String networkResourceLocator) {
      this.networkResourceLocator = networkResourceLocator;
      return this;
    }

    OpticalPortBuilder setLabel(String label) {
      this.label = label;
      return this;
    }

    OpticalPortBuilder setPayload(String payload) {
      this.payload = payload;
      return this;
    }

    OpticalPortBuilder setAdminState(AdminState adminState) {
      this.adminState = adminState;
      return this;
    }

    OpticalPortBuilder setOperationalState(OperationalState operationalState) {
      this.operationalState = operationalState;
      return this;
    }

    OpticalPortBuilder setOpticalConfiguration(OpticalConfiguration opticalConfiguration) {
      this.opticalConfiguration = opticalConfiguration;
      return this;
    }

    OpticalPort build() {
      return new OpticalPort(networkResourceLocator, label, payload, adminState, operationalState, opticalConfiguration);
    }

    static OpticalPortBuilder builder() {
      return new OpticalPortBuilder();
    }
  }

}
