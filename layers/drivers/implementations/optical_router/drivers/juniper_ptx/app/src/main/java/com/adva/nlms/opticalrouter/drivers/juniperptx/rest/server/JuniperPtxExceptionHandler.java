/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.rest.server;

import com.adva.nlms.opticalrouter.drivers.juniperptx.exception.NetconfException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.time.ZonedDateTime;
import java.util.LinkedHashMap;
import java.util.Map;

@ControllerAdvice
public class JuniperPtxExceptionHandler {

  @ExceptionHandler(NetconfException.class)
  ResponseEntity<Object> handleException(NetconfException exception) {
    return ResponseEntity
      .status(HttpStatus.INTERNAL_SERVER_ERROR)
      .body(createBody(exception.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR));
  }

  @ExceptionHandler(NullPointerException.class)
  ResponseEntity<Object> handleException(NullPointerException exception) {
    return ResponseEntity
      .status(HttpStatus.BAD_REQUEST)
      .body(createBody(exception.getMessage(), HttpStatus.BAD_REQUEST));
  }

  @ExceptionHandler(IllegalStateException.class)
  ResponseEntity<Object> handleException(IllegalStateException exception) {
    return ResponseEntity
      .status(HttpStatus.BAD_REQUEST)
      .body(createBody(exception.getMessage(), HttpStatus.BAD_REQUEST));
  }

  @ExceptionHandler(UnsupportedOperationException.class)
  ResponseEntity<Object> handleException(UnsupportedOperationException exception) {
    return ResponseEntity
      .status(HttpStatus.BAD_REQUEST)
      .body(createBody(exception.getMessage(), HttpStatus.BAD_REQUEST));
  }

  @ExceptionHandler(RuntimeException.class)
  ResponseEntity<Object> handleException(RuntimeException exception) {
    return ResponseEntity
      .status(HttpStatus.INTERNAL_SERVER_ERROR)
      .body(createBody(exception.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR));
  }

  private Map<String, Object> createBody(String message, HttpStatus httpStatus) {
    Map<String, Object> body = new LinkedHashMap<>();
    body.put("timestamp", ZonedDateTime.now().toString());
    body.put("status", httpStatus.value());
    body.put("error", httpStatus.getReasonPhrase());
    body.put("reason", message);
    return body;
  }
}
