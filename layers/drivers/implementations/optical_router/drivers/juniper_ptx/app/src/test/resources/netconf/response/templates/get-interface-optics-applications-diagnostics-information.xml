<nc:rpc-reply xmlns:nc="urn:ietf:params:xml:ns:netconf:base:1.0" xmlns:junos="http://xml.juniper.net/junos/24.4-202408290538.0-EVO/junos">
    <interface-information xmlns="http://xml.juniper.net/junos/24.4-202408290538.0-EVO/junos-interface" junos:style="normal">
        <physical-interface>
            <name th:text="${portName0}">default port type</name>
            <optics-applications>
                <application-interface-name>et-0/0/0</application-interface-name>
                <current-speed>1x400G</current-speed>
                <optics-application>
                    <apsel>1</apsel>
                    <host-interfaces-code>400GAUI-8 C2M (Annex 120E)(17)</host-interfaces-code>
                    <media-interfaces-code>400ZR, DWDM, amplified(62)</media-interfaces-code>
                    <host-lane>8</host-lane>
                    <media-lane>1</media-lane>
                    <host-assign>1</host-assign>
                    <media-assign>1</media-assign>
                </optics-application>
                <optics-application>
                    <apsel>2</apsel>
                    <host-interfaces-code>400GAUI-8 C2M (Annex 120E)(17)</host-interfaces-code>
                    <media-interfaces-code>400ZR, Single Wavelength,Unamplified(63)</media-interfaces-code>
                    <host-lane>8</host-lane>
                    <media-lane>1</media-lane>
                    <host-assign>1</host-assign>
                    <media-assign>1</media-assign>
                </optics-application>
                <optics-application>
                    <apsel>3</apsel>
                    <host-interfaces-code>100GAUI-2 C2M (Annex 135G)(13)</host-interfaces-code>
                    <media-interfaces-code>400ZR, DWDM, amplified(62)</media-interfaces-code>
                    <host-lane>2</host-lane>
                    <media-lane>1</media-lane>
                    <host-assign>85</host-assign>
                    <media-assign>1</media-assign>
                </optics-application>
            </optics-applications>
        </physical-interface>
        <physical-interface>
            <name th:text="${portName2}">default port type</name>
            <optics-applications>
                <application-interface-name>et-0/0/2</application-interface-name>
                <current-speed>1x400G</current-speed>
                <optics-application>
                    <apsel>1</apsel>
                    <host-interfaces-code>400GAUI-8 C2M (Annex 120E)(17)</host-interfaces-code>
                    <media-interfaces-code>400ZR, DWDM, amplified(62)</media-interfaces-code>
                    <host-lane>8</host-lane>
                    <media-lane>1</media-lane>
                    <host-assign>1</host-assign>
                    <media-assign>1</media-assign>
                </optics-application>
                <optics-application>
                    <apsel>2</apsel>
                    <host-interfaces-code>400GAUI-8 C2M (Annex 120E)(17)</host-interfaces-code>
                    <media-interfaces-code>400ZR, Single Wavelength,Unamplified(63)</media-interfaces-code>
                    <host-lane>8</host-lane>
                    <media-lane>1</media-lane>
                    <host-assign>1</host-assign>
                    <media-assign>1</media-assign>
                </optics-application>
            </optics-applications>
        </physical-interface>
    </interface-information>
</nc:rpc-reply>