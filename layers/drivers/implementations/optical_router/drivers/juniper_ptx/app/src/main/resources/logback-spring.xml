<!--
  ~  Copyright 2025 Adtran Networks SE. All rights reserved.
  ~
  ~  Owner: michalo
  -->

<configuration>
    <property name="LOG_DIR" value="var/log"/>

    <root level="WARN">
        <appender-ref ref="STDOUT"/>
        <springProfile name="dev">
            <appender-ref ref="FILE"/>
        </springProfile>
    </root>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread%replace( %mdc{correlationId}){' $', ''}] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <springProfile name="dev">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>${LOG_DIR}/juniper_ptx_driver.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
                <fileNamePattern>${LOG_DIR}/juniper_ptx_driver.%i.log</fileNamePattern>
                <minIndex>1</minIndex>
                <maxIndex>10</maxIndex>
            </rollingPolicy>
            <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
                <maxFileSize>30MB</maxFileSize>
            </triggeringPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread%replace( %mdc{correlationId}){' $', ''}] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
    </springProfile>

    <logger name="com.adva" level="DEBUG"/>

</configuration>
