/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.kafka;

import com.adva.nlms.mediation.infrastructure.notification.tracing.api.in.TraceDefinitions;
import com.adva.nlms.opticalrouter.api.commands.CommandResponder;
import com.adva.nlms.opticalrouter.api.commands.CommandResponse;
import com.adva.nlms.opticalrouter.api.registration.BeaconMessage;
import com.adva.nlms.opticalrouter.api.registration.OpticalRouterDriverBeacon;
import com.adva.nlms.opticalrouter.api.registration.OpticalRouterDriverRegistration;
import com.adva.nlms.opticalrouter.api.registration.RegistrationMessage;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.kafka.support.serializer.JsonSerializer;

import java.util.HashMap;
import java.util.Map;

@Configuration
class KafkaConfiguration {
  @Value(value = "${app.kafka.group-id}")
  public String groupId;
  @Value(value = "${spring.kafka.bootstrap-servers}")
  private String bootstrapAddress;

  @Value(value = "${app.kafka.beacon-topic.name}")
  private String beaconTopicName;
  @Value(value = "${app.kafka.beacon-topic.partitions}")
  private int beaconTopicPartitions;
  @Value(value = "${app.kafka.beacon-topic.replicas}")
  private short beaconTopicReplicas;

  @Value(value = "${app.kafka.registration-topic.name}")
  private String registrationTopicName;
  @Value(value = "${app.kafka.registration-topic.partitions}")
  private int registrationTopicPartitions;
  @Value(value = "${app.kafka.registration-topic.replicas}")
  private short registrationTopicReplicas;

  @Value(value = "${app.kafka.command-responses-topic.name}")
  private String commandResponsesTopicName;
  @Value(value = "${app.kafka.command-responses-topic.partitions}")
  private int commandResponsesTopicPartitions;
  @Value(value = "${app.kafka.command-responses-topic.replicas}")
  private short commandResponsesTopicReplicas;

  @Bean
  public NewTopic beaconTopic() {
    return new NewTopic(beaconTopicName, beaconTopicPartitions, beaconTopicReplicas);
  }

  @Bean
  public NewTopic registrationTopic() {
    return new NewTopic(registrationTopicName, registrationTopicPartitions, registrationTopicReplicas);
  }

  @Bean
  public NewTopic commandResponsesTopic() {
    return new NewTopic(commandResponsesTopicName, commandResponsesTopicPartitions, commandResponsesTopicReplicas);
  }

  @Bean
  OpticalRouterDriverRegistration opticalRouterDriverRegistration() {
    var factory = new DefaultKafkaProducerFactory<String, RegistrationMessage>(getProducerConfiguration());
    var template = new KafkaTemplate<>(factory);
    return new RegistrationMessageProducer(registrationTopicName, template);
  }

  @Bean
  CommandResponder commandResponseSender() {
    var factory = new DefaultKafkaProducerFactory<String, CommandResponse>(getProducerConfiguration());
    var template = new KafkaTemplate<>(factory);
    return new CommandResponseProducer(commandResponsesTopicName, template);
  }

  private Map<String, Object> getProducerConfiguration() {
    Map<String, Object> properties = new HashMap<>();
    properties.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapAddress);
    properties.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    properties.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
    properties.put(ProducerConfig.INTERCEPTOR_CLASSES_CONFIG, TraceDefinitions.PRODUCER_INTERCEPTOR);
    return properties;
  }

  @Bean
  BeaconMessageConsumer beaconMessageConsumer(OpticalRouterDriverBeacon beacon) {
    return new BeaconMessageConsumer(beacon);
  }

  @Bean
  ConcurrentKafkaListenerContainerFactory<String, BeaconMessage>
  kafkaListenerBeaconMessageFactory() {
    var consumerFactory = new DefaultKafkaConsumerFactory<>(
      getConsumerConfiguration(),
      new StringDeserializer(),
      new JsonDeserializer<>(BeaconMessage.class, true)
    );
    var containerFactory = new ConcurrentKafkaListenerContainerFactory<String, BeaconMessage>();
    containerFactory.setConsumerFactory(consumerFactory);
    return containerFactory;
  }

  public Map<String, Object> getConsumerConfiguration() {
    Map<String, Object> properties = new HashMap<>();
    properties.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapAddress);
    properties.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
    properties.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
    properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
    properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, JsonDeserializer.class);
    properties.put(ConsumerConfig.INTERCEPTOR_CLASSES_CONFIG, TraceDefinitions.CONSUMER_INTERCEPTOR);
    return properties;
  }

}
