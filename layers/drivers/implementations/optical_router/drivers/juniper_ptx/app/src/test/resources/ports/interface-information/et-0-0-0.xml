
<nc:rpc-reply xmlns:nc="urn:ietf:params:xml:ns:netconf:base:1.0" xmlns:junos="http://xml.juniper.net/junos/24.2R1.18-EVO/junos" message-id="2609:10007">
    <interface-information xmlns="http://xml.juniper.net/junos/24.2R1.18-EVO/junos-interface" junos:style="normal">
        <physical-interface>
            <name>et-0/0/0</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <local-index>1248</local-index>
            <snmp-index>521</snmp-index>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <optics-properties junos:style="verbose">
                <wavelength>1528.77</wavelength>
                <frequency>196.100</frequency>
                <optic-loopback>Disabled</optic-loopback>
                <optic-loopback-type>nil</optic-loopback-type>
                <media-code-desc>400ZR, DWDM, amplified</media-code-desc>
                <host-code-desc>400GAUI-8 C2M (Annex 120E)</host-code-desc>
            </optics-properties>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-tx-disabled/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x200000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <physical-interface-cos-information>
                <physical-interface-cos-hw-max-queues>8</physical-interface-cos-hw-max-queues>
                <physical-interface-cos-use-max-queues>8</physical-interface-cos-use-max-queues>
            </physical-interface-cos-information>
            <current-physical-address>b4:f9:5d:3d:4e:4c</current-physical-address>
            <hardware-physical-address>b4:f9:5d:3d:4e:4c</hardware-physical-address>
            <interface-flapped junos:seconds="35">2025-05-12 12:34:52 UTC (00:00:35 ago)</interface-flapped>
            <traffic-statistics junos:style="brief">
                <input-bps>0</input-bps>
                <input-pps>0</input-pps>
                <output-bps>0</output-bps>
                <output-pps>0</output-pps>
            </traffic-statistics>
            <active-alarms>
                <interface-alarms>
                    <ethernet-alarm-link-down/>
                </interface-alarms>
            </active-alarms>
            <active-defects>
                <interface-alarms>
                    <ethernet-alarm-link-down/>
                    <ethernet-alarm-link-local-fault/>
                </interface-alarms>
            </active-defects>
            <ethernet-pcs-statistics junos:style="verbose">
                <bit-error-seconds>0</bit-error-seconds>
                <errored-blocks-seconds>0</errored-blocks-seconds>
            </ethernet-pcs-statistics>
            <ethernet-fec-mode junos:style="verbose">
                <enabled_fec_mode>FEC119</enabled_fec_mode>
                <fec_codeword_size>544</fec_codeword_size>
                <fec_codeword_rate>0.945</fec_codeword_rate>
            </ethernet-fec-mode>
            <ethernet-fec-statistics junos:style="verbose">
                <fec_ccw_count>0</fec_ccw_count>
                <fec_nccw_count>0</fec_nccw_count>
                <fec_ccw_error_rate>0</fec_ccw_error_rate>
                <fec_nccw_error_rate>0</fec_nccw_error_rate>
            </ethernet-fec-statistics>
            <optic-fec-mode junos:style="verbose">
                <fec-mode>CFEC</fec-mode>
            </optic-fec-mode>
            <optic-fec-statistics junos:style="verbose">
                <fec-corrected-errors>0</fec-corrected-errors>
                <fec-uncorrected-words>0</fec-uncorrected-words>
                <fec-corrected-error-rate>0</fec-corrected-error-rate>
                <fec-uncorrected-error-rate>0</fec-uncorrected-error-rate>
                <optic-fec-corrected-error-ratio>5.00e-01</optic-fec-corrected-error-ratio>
                <fec-valid-time>27</fec-valid-time>
            </optic-fec-statistics>
            <ethernet-prbs-mode junos:style="verbose">
                <prbs_mode>Disabled</prbs_mode>
            </ethernet-prbs-mode>
            <interface-transmit-statistics>Disabled</interface-transmit-statistics>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/0/0.16386</name>
                <local-index>1039</local-index>
                <snmp-index>612</snmp-index>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <traffic-statistics junos:style="brief">
                    <input-packets>0</input-packets>
                    <output-packets>0</output-packets>
                </traffic-statistics>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                    <mtu>Unlimited</mtu>
                    <address-family-flags>
                        <ifff-none/>
                    </address-family-flags>
                </address-family>
            </logical-interface>
        </physical-interface>
    </interface-information>
</nc:rpc-reply>
