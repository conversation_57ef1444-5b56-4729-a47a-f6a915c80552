/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.tailf.jnc.Element;
import com.tailf.jnc.XMLParser;
import org.xml.sax.InputSource;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

class JncElementHelper {

  public static Element parseXmlFileToElement(String resourcePath) throws Exception {
    ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
    InputStream inputStream = classLoader.getResourceAsStream(resourcePath);

    if (inputStream == null) {
      throw new IllegalArgumentException("Resource not found: " + resourcePath);
    }

    XMLParser parser = new XMLParser();
    InputSource inputSource = new InputSource(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
    return parser.parse(inputSource);
  }
}

