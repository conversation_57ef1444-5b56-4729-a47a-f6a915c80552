/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock;

import org.apache.sshd.server.channel.ChannelSession;
import org.apache.sshd.server.command.Command;
import org.apache.sshd.server.subsystem.SubsystemFactory;

public class NetconfSubsystemFactory implements SubsystemFactory {

  private final NetconfMockServerManager manager;

  public NetconfSubsystemFactory(NetconfMockServerManager manager) {
    this.manager = manager;
  }

  @Override
  public String getName() {
    return "netconf";
  }

  @Override
  public Command createSubsystem(ChannelSession channelSession) {
    return new NetconfSubsystem(manager);
  }
}