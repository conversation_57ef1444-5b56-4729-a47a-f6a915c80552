/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.nrl.creator.api.NetworkResourceLocator;
import com.adva.nlms.nrl.creator.api.exception.WrongNRLFormatException;
import com.adva.nlms.opticalrouter.api.resources.AdminState;
import com.adva.nlms.opticalrouter.api.resources.OperationalState;
import com.adva.nlms.opticalrouter.api.resources.OpticalPlug;
import com.adva.nlms.opticalrouter.drivers.juniperptx.exception.NetconfException;
import com.tailf.jnc.Element;
import com.tailf.jnc.NodeSet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

class DevicePlugsReader {
  private static final Logger log = LoggerFactory.getLogger(DevicePlugsReader.class);

  private static final String ADMIN_STATE_PATH = "interface-information/physical-interface/admin-status";
  static final String OPERATIONAL_STATE_PATH = "interface-information/physical-interface/oper-status";
  private static final String CHASSIS_FPCS_PATH = "chassis-inventory/chassis/chassis-module";
  private static final String FPC_INFORMATION_PORT_PATH = "fpc-information/fpc/pic-detail/port-information/port";

  private static final String VENDOR_NAME_TAG = "sfp-vendor-name";
  private static final String VENDOR_PART_NUMBER_TAG = "sfp-vendor-pno";
  private static final String VENDOR_FIRMWARE_VERSION_TAG = "sfp-vendor-fw-ver";
  private static final String NAME_TAG = "name";
  private static final String CHASSIS_SUB_MODULE_TAG = "chassis-sub-module";
  private static final String CHASSIS_SUB_SUB_MODULE_TAG = "chassis-sub-sub-module";
  private static final String DESCRIPTION_TAG = "description";
  private static final String SERIAL_NUMBER_TAG = "serial-number";
  private static final String PORT_NUMBER_TAG = "port-number";

  private DevicePlugsReader() {
  }

  static List<OpticalPlug> readPlugs(NetconfDataProvider netconfDataProvider, Set<InterfaceId> opticalInterfaces) {
    Objects.requireNonNull(netconfDataProvider);

    if (opticalInterfaces == null || opticalInterfaces.isEmpty()) {
      return List.of();
    }

    return opticalInterfaces.stream()
      .map(interfaceId -> getPlugData(netconfDataProvider, interfaceId))
      .filter(Objects::nonNull)
      .toList();
  }


  private static OpticalPlug getPlugData(NetconfDataProvider netconfDataProvider, InterfaceId interfaceId) {
    OpticalPlugBuilder builder = OpticalPlugBuilder.builder();

    processGetInterfaceInformationCommand(netconfDataProvider, interfaceId, builder);
    processGetPicDetailCommand(netconfDataProvider, interfaceId, builder);
    processGetChassisInventory(netconfDataProvider, interfaceId, builder);

    return builder.build();
  }

  private static void processGetInterfaceInformationCommand(NetconfDataProvider netconfDataProvider, InterfaceId interfaceId, OpticalPlugBuilder builder) {
    String interfaceName = interfaceId.fullName();
    Element interfaceInformation = netconfDataProvider.getElement(String.format(RpcCommands.GET_INTERFACE_INFORMATION, interfaceName));

    if (interfaceName == null || !interfaceName.contains("-")) {
      throw new IllegalArgumentException("Interface name does not match format: et-<fpc>/<pic>/<port>");
    }
    String label = "plug" + interfaceName.substring(interfaceName.indexOf('-'));
    builder.label(label);

    String adminStateString = NetconfReader.getValue(interfaceInformation, ADMIN_STATE_PATH);
    builder.adminState(EnumMapper.mapAdminState(adminStateString));

    String operationalStateString = NetconfReader.getValue(interfaceInformation, OPERATIONAL_STATE_PATH);
    builder.operationalState(EnumMapper.mapOperationalState(operationalStateString));

    String nrl;
    try {
      nrl = new NetworkResourceLocator.Builder().neUUID(netconfDataProvider.getNeId())
        .port(interfaceId.toNrlPort())
        .build()
        .toString();
    } catch (WrongNRLFormatException e) {
      throw new NetconfException("Cannot build device NRL for neId=%s and interfaceName=%s".formatted(netconfDataProvider.getNeId(), interfaceId), e);
    }
    builder.networkResourceLocator(nrl);
  }

  private static void processGetPicDetailCommand(NetconfDataProvider netconfDataProvider, InterfaceId interfaceId, OpticalPlugBuilder builder) {
    Objects.requireNonNull(interfaceId);

    Element fpcInformation = netconfDataProvider.getElement(String.format(RpcCommands.GET_PIC_DETAIL, interfaceId.fpc(), interfaceId.pic()));

    List<Element> portTags = getPortForInterfaceId(fpcInformation, interfaceId);

    if (portTags.isEmpty()) {
      log.error("Cannot find port tag for port number {}", interfaceId.port());
      return;
    }
    if (portTags.size() > 1) {
      log.warn("Found more than one port tag for port number {}, taking first one and skipping the rest", interfaceId.port());
    }

    Element portTag =  portTags.stream().findFirst().orElseThrow();

    String vendor = NetconfReader.getValue(portTag, VENDOR_NAME_TAG);
    String vendorPartNumber = NetconfReader.getValue(portTag, VENDOR_PART_NUMBER_TAG);
    String firmwareVersion = NetconfReader.getValue(portTag, VENDOR_FIRMWARE_VERSION_TAG);

    builder.vendor(vendor)
      .vendorPartNumber(vendorPartNumber)
      .firmwareVersion(firmwareVersion);
  }

  private static List<Element> getPortForInterfaceId(Element element, InterfaceId interfaceId) {
    if (interfaceId == null) {
      log.error("Cannot find <port> for null interfaceId");
      return List.of();
    }

    return findTagsByPathAndChildContainsContent(element, FPC_INFORMATION_PORT_PATH, PORT_NUMBER_TAG, interfaceId.port());
  }

  private static void processGetChassisInventory(NetconfDataProvider netconfDataProvider, InterfaceId interfaceId, OpticalPlugBuilder builder) {
    Objects.requireNonNull(interfaceId);

    Element chassisInventory = netconfDataProvider.getElement(RpcCommands.GET_CHASSIS_INVENTORY);

    List<Element> chassisSubSubModuleTags = getChassisSubSubModuleForInterfaceId(chassisInventory, interfaceId);

    if (chassisSubSubModuleTags.isEmpty()) {
      log.error("Cannot find chassis module for FPC {}", interfaceId.fpc());
      return;
    }
    if (chassisSubSubModuleTags.size() > 1) {
      log.warn("Found more than one chassis module for FPC {}, taking first one and skipping the rest", interfaceId.fpc());
    }
    Element chassisSubSubModuleTag =  chassisSubSubModuleTags.stream().findFirst().orElseThrow();

    String plugName = NetconfReader.getValue(chassisSubSubModuleTag, DESCRIPTION_TAG);
    String serialNumber = NetconfReader.getValue(chassisSubSubModuleTag, SERIAL_NUMBER_TAG);

    builder.plugType(plugName)
      .serialNumber(serialNumber);
  }

  private static List<Element> getChassisSubSubModuleForInterfaceId(Element rootElement, InterfaceId interfaceId) {
    if (interfaceId == null) {
      log.error("Cannot find <chassis-sub-sub-module> for null interfaceId");
      return List.of();
    }

    List<Element> result = new ArrayList<>();
    List<Element> fpcs = findTagsByPathAndChildContainsContent(rootElement, CHASSIS_FPCS_PATH, NAME_TAG, "FPC " + interfaceId.fpc());
    for (Element module : fpcs) {
      List<Element> pics = findTagsByPathAndChildContainsContent(module, CHASSIS_SUB_MODULE_TAG, NAME_TAG, "PIC " + interfaceId.pic());
      for (Element pic : pics) {
        List<Element> ports = findTagsByPathAndChildContainsContent(pic, CHASSIS_SUB_SUB_MODULE_TAG, NAME_TAG, "Xcvr " + interfaceId.port());
        result.addAll(ports);
      }
    }
    return result;
  }

  private static List<Element> findTagsByPathAndChildContainsContent(Element element, String path,String childTagName, String content) {
    Objects.requireNonNull(element);
    Objects.requireNonNull(path);
    Objects.requireNonNull(childTagName);
    Objects.requireNonNull(content);

    NodeSet targetTags = NetconfReader.getNodeSet(element, path);
    List<Element> result = new ArrayList<>();

    if (targetTags != null && !targetTags.isEmpty()) {
      for (int i = 0; i < targetTags.size(); i++) {

        Element targetTag = targetTags.getElement(i);

        Element childTag = targetTag.getChild(childTagName);
        if (childTag != null && childTag.getValue() != null) {
          String nameValue = childTag.getValue()
            .toString();

          if (Objects.equals(nameValue, content)) {
            result.add(targetTag);
          }
        }
      }
    }
    return result;
  }

  @SuppressWarnings("UnusedReturnValue")
  static class OpticalPlugBuilder {
    private String networkResourceLocator;
    private String label;
    private AdminState adminState;
    private OperationalState operationalState;
    private String plugType;
    private String vendor;
    private String vendorPartNumber;
    private String serialNumber;
    private String firmwareVersion;

    private OpticalPlugBuilder() {
    }

    static OpticalPlugBuilder builder() {
      return new OpticalPlugBuilder();
    }

    OpticalPlugBuilder networkResourceLocator(String networkResourceLocator) {
      this.networkResourceLocator = networkResourceLocator;
      return this;
    }

    OpticalPlugBuilder label(String label) {
      this.label = label;
      return this;
    }

    OpticalPlugBuilder adminState(AdminState adminState) {
      this.adminState = adminState;
      return this;
    }

    OpticalPlugBuilder operationalState(OperationalState operationalState) {
      this.operationalState = operationalState;
      return this;
    }

    OpticalPlugBuilder plugType(String plugType) {
      this.plugType = plugType;
      return this;
    }

    OpticalPlugBuilder vendor(String vendor) {
      this.vendor = vendor;
      return this;
    }

    OpticalPlugBuilder vendorPartNumber(String vendorPartNumber) {
      this.vendorPartNumber = vendorPartNumber;
      return this;
    }

    OpticalPlugBuilder serialNumber(String serialNumber) {
      this.serialNumber = serialNumber;
      return this;
    }

    OpticalPlugBuilder firmwareVersion(String firmwareVersion) {
      this.firmwareVersion = firmwareVersion;
      return this;
    }

    OpticalPlug build() {
      return new OpticalPlug(
        networkResourceLocator,
        label,
        adminState,
        operationalState,
        plugType,
        vendor,
        vendorPartNumber,
        serialNumber,
        firmwareVersion
      );
    }
  }
}
