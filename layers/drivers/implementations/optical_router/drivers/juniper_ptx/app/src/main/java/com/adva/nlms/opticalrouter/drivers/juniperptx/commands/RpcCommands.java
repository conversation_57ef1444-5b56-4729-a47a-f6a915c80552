/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

class RpcCommands {

  private RpcCommands() {
  }

  static final String GET_SYSTEM_UPTIME_INFORMATION = "<rpc><get-system-uptime-information/></rpc>";
  static final String GET_SOFTWARE_INFORMATION = "<rpc><get-software-information/></rpc>";
  static final String GET_CHASSIS_INVENTORY = "<rpc><get-chassis-inventory/></rpc>";
  static final String GET_INTERFACE_INFORMATION = "<rpc><get-interface-information><interface-name>%s</interface-name></get-interface-information></rpc>";
  static final String GET_CONFIG_RUNNING_WITH_TX_POWER = """
    <rpc>
      <get-config>
        <source>
          <running/>
        </source>
        <filter>
          <configuration>
            <interfaces>
              <interface>
                <name>%s</name>
                <optics-options>
                  <tx-power/>
                </optics-options>
              </interface>
            </interfaces>
          </configuration>
        </filter>
      </get-config>
    </rpc>
    """;
  static final String GET_INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS = "<rpc><get-interface-optics-applications-diagnostics/></rpc>";
  static final String GET_PIC_DETAIL = "<rpc><get-pic-detail><fpc-slot>%s</fpc-slot><pic-slot>%s</pic-slot></get-pic-detail></rpc>";

}
