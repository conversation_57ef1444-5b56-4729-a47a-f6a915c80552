/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.api.commands.CommandResponder;
import com.adva.nlms.opticalrouter.api.commands.CommunicationSettings;
import com.adva.nlms.opticalrouter.api.commands.OpticalRouterCommandException;
import com.adva.nlms.opticalrouter.api.commands.OpticalRouterDeviceDriver;
import com.adva.nlms.opticalrouter.api.commands.PasswordAuthenticationData;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;

@SpringBootTest
@Import(CommandConfiguration.class)
@Disabled("Test that connects to real device. This should stay in code until optical device simulator based tests are introduced")
class ConnectionCommandsRestTest {
  private static final String IP = "************";
  private static final int PORT = 830;
  private static final String USERNAME = "admin";
  private static final String PASSWORD = "chgme.1";
  private static final int TIMEOUT = 5;
  private static final UUID DEVICE_ID = UUID.fromString("d0baf626-c566-47c8-9ba5-390437d0913a");

  @Autowired
  private OpticalRouterDeviceDriver opticalRouterDeviceDriver;

  @Test
  @Disabled("Contains infinite loop.")
  void foo() throws OpticalRouterCommandException, InterruptedException {
    connect();
    while (true) {
      checkConnection(true);
      Thread.sleep(1000);
    }
  }

  @Test
  void connectThenCheckColdStartTime() throws OpticalRouterCommandException {
    connect();
    checkConnection(true);
    checkColdStartTime();
    disconnect();
  }

  @Test
  void connectThenCheckConnection() throws OpticalRouterCommandException {
    connect();
    checkConnection(true);
    disconnect();
    checkConnection(false);
  }

  @Test
  void synchronizeDeviceInfo() {
    connect();
    opticalRouterDeviceDriver.synchronizeDeviceInfo(DEVICE_ID);
    disconnect();
  }

  @Test
  void synchronizeInventory() {
    connect();
    opticalRouterDeviceDriver.synchronizeInventory(DEVICE_ID);
    disconnect();
  }

  private void connect() throws OpticalRouterCommandException {
    var communicationSettings = new CommunicationSettings("TestDevice", IP, PORT, TIMEOUT,
      new PasswordAuthenticationData(USERNAME, PASSWORD));
    opticalRouterDeviceDriver.connect(DEVICE_ID, communicationSettings);
  }

  private void checkConnection(Boolean expected) throws OpticalRouterCommandException {
    var response = opticalRouterDeviceDriver.isConnected(DEVICE_ID);
    assertEquals(expected, response);
  }

  private void disconnect() throws OpticalRouterCommandException {
    opticalRouterDeviceDriver.disconnect(DEVICE_ID);
  }

  private void checkColdStartTime() throws OpticalRouterCommandException {
    var response = opticalRouterDeviceDriver.getColdStartTime(DEVICE_ID);
    assertTrue(response > 0);
  }

  @Configuration
  static class TestConfiguration {
    @Bean
    CommandResponder testCommandResponder() {
      return mock();
    }
  }
}