/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.drivers.juniperptx.exception.NetconfException;
import org.assertj.core.api.ThrowableAssert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;

class SemanticVersionParserTest {
  @ParameterizedTest
  @CsvSource({
    "24.4-202408290538.0-EVO,24.4.0",
    " 24.2R1.18-EVO,24.2.1",
    "22.2R3-S4.11  ,22.2.3",
    "20.3R3-S5,20.3.3"
  })
  void parseToSemantic(String input, String output) {
    String actual = SemanticVersionParser.parseToSemantic(input);
    assertThat(actual).isEqualTo(output);
  }

  @Test
  void parseToSemanticNullInput() {
    ThrowableAssert.ThrowingCallable actual = () -> SemanticVersionParser.parseToSemantic(null);
    assertThatThrownBy(actual).isInstanceOf(NetconfException.class)
      .hasMessage("Cannot parse null or blank semantic software version");
  }

}