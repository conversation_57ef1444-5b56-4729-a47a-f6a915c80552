<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern> %d [%thread]-[] %-5level %logger{2} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="net.schmizz.keepalive" level="DEBUG" additivity="false">
        <appender-ref ref="console"/>
    </logger>

    <root level="ERROR">
        <appender-ref ref="console"/>
    </root>

</configuration>