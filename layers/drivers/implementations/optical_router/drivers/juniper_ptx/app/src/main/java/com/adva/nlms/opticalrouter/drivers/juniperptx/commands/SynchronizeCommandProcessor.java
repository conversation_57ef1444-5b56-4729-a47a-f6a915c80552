/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.api.commands.CommandResponder;
import com.adva.nlms.opticalrouter.api.commands.CommandResponse;
import com.adva.nlms.opticalrouter.api.commands.Status;
import com.adva.nlms.opticalrouter.api.resources.OpticalPlug;
import com.adva.nlms.opticalrouter.api.resources.OpticalPort;
import com.adva.nlms.opticalrouter.api.resources.OpticalResourceStatus;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterConnectionFaultCause;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterDeviceInfo;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterInventory;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterStatus;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterSynchronizationData;
import com.adva.nlms.opticalrouter.drivers.juniperptx.exception.ConnectionException;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;

import static java.util.Objects.requireNonNull;

class SynchronizeCommandProcessor {

  private final CommandResponder commandResponder;
  private final NetconfDataProviderFactory netConfDataProviderFactory;
  private final ExecutorService executorService;
  private final Map<UUID, BlockingQueue<Runnable>> elementQueues = new ConcurrentHashMap<>();
  private final Set<UUID> activeElements = ConcurrentHashMap.newKeySet();

  SynchronizeCommandProcessor(CommandResponder commandResponder,
                              NetconfDataProviderFactory netConfDataProviderFactory, ExecutorService executorService) {
    this.commandResponder = commandResponder;
    this.netConfDataProviderFactory = netConfDataProviderFactory;
    this.executorService = executorService;
  }

  void synchronizeDeviceInfo(UUID neId) {
    requireNonNull(neId);
    submitTask(neId, () -> doSynchronizeDeviceInfo(neId));
  }

  private void doSynchronizeDeviceInfo(UUID neId) {
    CommandResponse response;

    try {
      NetconfDataProvider netConfDataProvider = netConfDataProviderFactory.newDataProvider(neId);
      OpticalRouterDeviceInfo deviceInfo = DeviceInfoReader.readDeviceInfo(netConfDataProvider);
      response = new CommandResponse(neId, Status.SUCCESS, "Device info with neId=%s has been synchronized".formatted(neId), deviceInfo);
    } catch (ConnectionException connectionException) {
      commandResponder.process(connectionFailureResponse(neId, connectionException));
      response = failureResponse(neId,
        "Device info synchronization failed on neId=%s due to lost connection".formatted(neId),
        new OpticalRouterDeviceInfo());
    } catch (Exception e) {
      response = failureResponse(neId, "Device info synchronization failed on neId=%s. %s".formatted(neId, e.getMessage()),
        new OpticalRouterDeviceInfo());
    }

    commandResponder.process(response);
  }

  void synchronizeInventory(UUID neId) {
    requireNonNull(neId);
    submitTask(neId, () -> doSynchronizeInventory(neId));
  }

  private void doSynchronizeInventory(UUID neId) {
    CommandResponse response;

    try {
      NetconfDataProvider dataProvider = netConfDataProviderFactory.newDataProvider(neId);
      Set<InterfaceId> opticalInterfaces = OpticalInterfacesReader.getOpticalInterfaces(dataProvider);
      List<OpticalPort> opticalPorts = DevicePortsReader.readPorts(dataProvider, opticalInterfaces);
      List<OpticalPlug> opticalPlugs = DevicePlugsReader.readPlugs(dataProvider, opticalInterfaces);
      response = new CommandResponse(neId,
        Status.SUCCESS,
        "Inventory with neId=%s has been synchronized".formatted(neId),
        new OpticalRouterInventory(opticalPorts, opticalPlugs));
    } catch (ConnectionException connectionException) {
      commandResponder.process(connectionFailureResponse(neId, connectionException));
      response = failureResponse(neId,
        "Inventory synchronization failed on neId=%s due to lost connection".formatted(neId),
        new OpticalRouterInventory());
    } catch (Exception e) {
      response = failureResponse(neId, "Inventory synchronization failed on neId=%s. %s".formatted(neId, e.getMessage()),
        new OpticalRouterInventory());
    }

    commandResponder.process(response);
  }

  void synchronizeStatus(UUID neId) {
    requireNonNull(neId);
    submitTask(neId, () -> doSynchronizeStatus(neId));
  }

  private void doSynchronizeStatus(UUID neId) {
    CommandResponse response;
    try {
      NetconfDataProvider dataProvider = netConfDataProviderFactory.newDataProvider(neId);
      Set<InterfaceId> opticalInterfaces = OpticalInterfacesReader.getOpticalInterfaces(dataProvider);
      List<OpticalResourceStatus> opticalResourceStatusList = DevicePortStatusReader.readDevicePortStatus(dataProvider, opticalInterfaces);
      //Plug statuses are the same as port statuses. There is no need to separately fetch them from the router.
      OpticalRouterStatus opticalRouterStatus = new OpticalRouterStatus(opticalResourceStatusList, opticalResourceStatusList);
      response = new CommandResponse(neId, Status.SUCCESS, "Status with neId=%s has been synchronized".formatted(neId), opticalRouterStatus);
    } catch (ConnectionException connectionException) {
      commandResponder.process(connectionFailureResponse(neId, connectionException));
      response = failureResponse(neId,
        "Status synchronization failed on neId=%s due to lost connection".formatted(neId), new OpticalRouterStatus());
    } catch (Exception e) {
      response = failureResponse(neId, "Status synchronization failed on neId=%s. %s".formatted(neId,
        e.getMessage()), new OpticalRouterStatus());
    }
    commandResponder.process(response);
  }

  private static CommandResponse connectionFailureResponse(UUID neId, ConnectionException connectionException) {
    return new CommandResponse(neId,
      Status.FAILURE,
      connectionException.getMessage(),
      new OpticalRouterConnectionFaultCause(connectionException.getConnectionFaultCause()));
  }

  private static CommandResponse failureResponse(UUID neId, String message, OpticalRouterSynchronizationData data) {
    return new CommandResponse(neId, Status.FAILURE, message, data);
  }

  private void submitTask(UUID neId, Runnable task) {
    if (executorService == null)
      return;
    elementQueues.compute(neId, (integer, runnables) -> {
      if (runnables == null) {
        runnables = new LinkedBlockingQueue<>();
      }
      runnables.add(task);
      return runnables;
    });
    startProcessingIfNeeded(neId);
  }

  private void startProcessingIfNeeded(UUID neId) {
    if (activeElements.add(neId)) {
      executorService.submit(() -> processQueue(neId));
    }
  }

  private void processQueue(UUID neId) {
    try {
      BlockingQueue<Runnable> queue = elementQueues.get(neId);
      while (queue != null && !queue.isEmpty()) {
        Runnable task = queue.poll();
        if (task != null) {
          task.run();
        }
      }
    } finally {
      activeElements.remove(neId);
      elementQueues.computeIfPresent(neId, (id, q) -> q.isEmpty() ? null : q);
      if (elementQueues.containsKey(neId)) {
        startProcessingIfNeeded(neId);
      }
    }
  }
}
