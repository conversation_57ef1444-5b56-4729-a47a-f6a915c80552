/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock;

import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.Context;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;

public class NetconfCommand {

  private final String phrase;
  private final MatchingStrategy matchingStrategy;
  private int counter = 0;

  private ResponseTemplate responseTemplate;
  private String staticTextResponse;
  private boolean sendEOFAsResponse = false;
  private Duration hangingTime;

  private final ITemplateEngine templateEngine;
  private final Context context = new Context();

  NetconfCommand(ITemplateEngine templateEngine, String phrase) {
    this.templateEngine = templateEngine;
    this.phrase = phrase;
    this.matchingStrategy = MatchingStrategy.CONTAINS;
  }

  NetconfCommand(ITemplateEngine templateEngine, String phrase, MatchingStrategy matchingStrategy) {
    this.templateEngine = templateEngine;
    this.phrase = phrase;
    this.matchingStrategy = matchingStrategy;
  }

  public int getCounter() {
    return counter;
  }

  boolean isSendEOFAsResponse() {
    return sendEOFAsResponse;
  }

  public Duration getHangingTime() {
    return hangingTime;
  }

  public String getPhrase() {
    return phrase;
  }

  public MatchingStrategy getMatchingStrategy() {
    return matchingStrategy;
  }

  public NetconfCommand respondWithTemplate(ResponseTemplate responseTemplate) {
    this.responseTemplate = responseTemplate;
    return this;
  }

  @SuppressWarnings("UnusedReturnValue")
  public NetconfCommand respondWithEOF() {
    sendEOFAsResponse = true;
    return this;
  }

  @SuppressWarnings("unused")
  public NetconfCommand respondWith(String staticText) {
    this.staticTextResponse = staticText;
    return this;
  }

  boolean matches(String message) {
    return matchingStrategy.getStrategy()
      .apply(message, phrase);
  }

  String getResponse() {
    if (responseTemplate != null) {
      return templateEngine.process(responseTemplate.getName(), context);
    }
    return staticTextResponse;
  }

  @SuppressWarnings("unused")
  public NetconfCommand andTemplateParameters(Map<String, Object> parameters) {
    context.setVariables(parameters);
    return this;
  }

  public NetconfCommand andTemplateParameter(String parameterName, Object parameterValue) {
    context.setVariable(parameterName, parameterValue);
    return this;
  }

  @SuppressWarnings("UnusedReturnValue")
  public NetconfCommand simulateResponseHangingFor(Duration hangingTime) {
    this.hangingTime = hangingTime;
    return this;
  }

  public void bumpCounter() {
    this.counter++;
  }

  @Override
  public boolean equals(Object o) {
    if (o == null || getClass() != o.getClass()) return false;
    NetconfCommand that = (NetconfCommand) o;
    return Objects.equals(phrase, that.phrase) &&
      matchingStrategy == that.matchingStrategy &&
      responseTemplate == that.responseTemplate &&
      Objects.equals(context.getLocale(), that.context.getLocale()) &&
      Objects.equals(context.getVariableNames(), that.context.getVariableNames()) &&
      Objects.equals(hangingTime, that.hangingTime) &&
      Objects.equals(sendEOFAsResponse, that.sendEOFAsResponse);
  }

  @Override
  public int hashCode() {
    return Objects.hash(phrase, matchingStrategy, responseTemplate);
  }

  @Override
  public String toString() {
    return "NetconfCommand{" +
      "phrase='" + phrase + '\'' +
      ", counter=" + counter +
      ", matchingStrategy=" + matchingStrategy +
      '}';
  }
}
