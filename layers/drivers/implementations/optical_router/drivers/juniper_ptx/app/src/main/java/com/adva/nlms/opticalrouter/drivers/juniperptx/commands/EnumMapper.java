/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.api.resources.AdminState;
import com.adva.nlms.opticalrouter.api.resources.OperationalState;

class EnumMapper {
  private EnumMapper() {
  }

  static AdminState mapAdminState(String adminStatus) {
    return switch (adminStatus) {
      case "up" -> AdminState.UP;
      case "down" -> AdminState.DOWN;
      default -> throw new IllegalStateException("Unexpected adminStatus: " + adminStatus);
    };
  }

  static OperationalState mapOperationalState(String operationalState) {
    return switch (operationalState) {
      case "up" -> OperationalState.NORMAL;
      case "down" -> OperationalState.OUTAGE;
      default -> throw new IllegalStateException("Unexpected operationalState: " + operationalState);
    };
  }
}
