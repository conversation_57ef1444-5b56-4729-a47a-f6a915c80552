/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.api.resources.AdminState;
import com.adva.nlms.opticalrouter.api.resources.OperationalState;
import com.adva.nlms.opticalrouter.api.resources.OpticalChannel;
import com.adva.nlms.opticalrouter.api.resources.OpticalConfiguration;
import com.adva.nlms.opticalrouter.api.resources.OpticalPort;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class DevicePortsReaderTest {

  private static final UUID DEVICE_ID = UUID.fromString("d0baf626-c566-47c8-9ba5-390437d0913a");

  static final String EMPTY_XML_FILE_PATH = "empty-response.xml";
  private static final String INTERFACE_INFORMATION_ET_0_0_0_XML_FILE_PATH = "ports/interface-information/et-0-0-0.xml";
  static final String INTERFACE_INFORMATION_ET_0_0_2_XML_FILE_PATH = "ports/interface-information/et-0-0-2.xml";
  private static final String INTERFACE_INFORMATION_ET_0_0_8_XML_FILE_PATH = "ports/interface-information/et-0-0-8.xml";
  private static final String INTERFACE_INFORMATION_ET_0_0_9_XML_FILE_PATH = "ports/interface-information/et-0-0-9.xml";

  private static final String TX_POWER_ET_0_0_0_XML_FILE_PATH = "ports/tx-power/et-0-0-0.xml";
  private static final String TX_POWER_ET_0_0_2_XML_FILE_PATH = "ports/tx-power/et-0-0-2.xml";
  private static final String TX_POWER_ET_0_0_8_XML_FILE_PATH = "ports/tx-power/et-0-0-8.xml";

  private final NetconfDataProvider netconfDataProvider = mock();

  @Test
  void invokeReadPorts_missingDataProvider() {
    assertThrows(NullPointerException.class, () -> DevicePortsReader.readPorts(null, null));
  }

  @Test
  void invokeReadPorts_nullOpticalInterfaces() throws Exception {
    //given
    var emptyResponse = JncElementHelper.parseXmlFileToElement(EMPTY_XML_FILE_PATH);

    when(netconfDataProvider.getElement(RpcCommands.GET_INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS)).thenReturn(emptyResponse);

    //when
    List<OpticalPort> opticalPorts = DevicePortsReader.readPorts(netconfDataProvider, null);

    //then
    assertTrue(opticalPorts.isEmpty());
  }

  @Test
  void invokeReadPorts_emptyInterfaceInformationResponse() throws Exception {
    //given
    String et008 = "et-0/0/8";
    var opticalInterfaces = Set.of(new InterfaceId(et008,"0","0","8"));

    var emptyResponse = JncElementHelper.parseXmlFileToElement(EMPTY_XML_FILE_PATH);
    when(netconfDataProvider.getElement(String.format(RpcCommands.GET_INTERFACE_INFORMATION, et008))).thenReturn(emptyResponse);
    //when
    var actual = DevicePortsReader.readPorts(netconfDataProvider, opticalInterfaces);

    //then
    assertThat(actual).isEmpty();
  }

  @Test
  void invokeReadPorts_happyPath() throws Exception {
    //given
    String et000 = "et-0/0/0";
    String et002 = "et-0/0/2";
    String et008 = "et-0/0/8";
    String et009 = "et-0/0/9";
    var opticalInterfaces = Set.of(
      new InterfaceId(et000, "0", "0", "0"),
      new InterfaceId(et002, "0", "0", "2"),
      new InterfaceId(et008, "0", "0", "8"),
      new InterfaceId(et009, "0", "0", "9")
    );

    when(netconfDataProvider.getNeId()).thenReturn(DEVICE_ID);

    var et008OpticalConfiguration = new OpticalConfiguration(List.of(new OpticalChannel.FrequencySlot(new BigDecimal("194.900"), new BigDecimal("37.5"))), new BigDecimal(-10));
    var et008OpticalPort = new OpticalPort("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-8", et008, "ET400ZR", AdminState.UP, OperationalState.NORMAL, et008OpticalConfiguration);

    var et002OpticalConfiguration = new OpticalConfiguration(List.of(new OpticalChannel.FrequencySlot(new BigDecimal("196.025"), new BigDecimal("75.0"))), new BigDecimal(-11));
    var et002OpticalPort = new OpticalPort("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-2", et002, "ET400ZR", AdminState.UP, OperationalState.OUTAGE, et002OpticalConfiguration);

    var et000OpticalConfiguration = new OpticalConfiguration(List.of(new OpticalChannel.FrequencySlot(new BigDecimal("196.100"), new BigDecimal("75.0"))), new BigDecimal(-11));
    var et000OpticalPort = new OpticalPort("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-0", et000, "ET400ZR", AdminState.UP, OperationalState.OUTAGE, et000OpticalConfiguration);

    var et_0_0_0_interfaceInformation = JncElementHelper.parseXmlFileToElement(INTERFACE_INFORMATION_ET_0_0_0_XML_FILE_PATH);
    var et_0_0_2_interfaceInformation = JncElementHelper.parseXmlFileToElement(INTERFACE_INFORMATION_ET_0_0_2_XML_FILE_PATH);
    var et_0_0_8_interfaceInformation = JncElementHelper.parseXmlFileToElement(INTERFACE_INFORMATION_ET_0_0_8_XML_FILE_PATH);
    var et_0_0_9_interfaceInformation = JncElementHelper.parseXmlFileToElement(INTERFACE_INFORMATION_ET_0_0_9_XML_FILE_PATH);

    var et_0_0_0_txPower = JncElementHelper.parseXmlFileToElement(TX_POWER_ET_0_0_0_XML_FILE_PATH);
    var et_0_0_2_txPower = JncElementHelper.parseXmlFileToElement(TX_POWER_ET_0_0_2_XML_FILE_PATH);
    var et_0_0_8_txPower = JncElementHelper.parseXmlFileToElement(TX_POWER_ET_0_0_8_XML_FILE_PATH);

    when(netconfDataProvider.getElement(String.format(RpcCommands.GET_INTERFACE_INFORMATION, et000))).thenReturn(et_0_0_0_interfaceInformation);
    when(netconfDataProvider.getElement(String.format(RpcCommands.GET_INTERFACE_INFORMATION, et002))).thenReturn(et_0_0_2_interfaceInformation);
    when(netconfDataProvider.getElement(String.format(RpcCommands.GET_INTERFACE_INFORMATION, et008))).thenReturn(et_0_0_8_interfaceInformation);
    when(netconfDataProvider.getElement(String.format(RpcCommands.GET_INTERFACE_INFORMATION, et009))).thenReturn(et_0_0_9_interfaceInformation);

    when(netconfDataProvider.getElement(String.format(RpcCommands.GET_CONFIG_RUNNING_WITH_TX_POWER, et000))).thenReturn(et_0_0_0_txPower);
    when(netconfDataProvider.getElement(String.format(RpcCommands.GET_CONFIG_RUNNING_WITH_TX_POWER, et002))).thenReturn(et_0_0_2_txPower);
    when(netconfDataProvider.getElement(String.format(RpcCommands.GET_CONFIG_RUNNING_WITH_TX_POWER, et008))).thenReturn(et_0_0_8_txPower);

    //when
    List<OpticalPort> opticalPorts = DevicePortsReader.readPorts(netconfDataProvider, opticalInterfaces);

    //then
    assertThat(opticalPorts)
      .containsExactlyInAnyOrder(et000OpticalPort, et002OpticalPort, et008OpticalPort);
  }

}