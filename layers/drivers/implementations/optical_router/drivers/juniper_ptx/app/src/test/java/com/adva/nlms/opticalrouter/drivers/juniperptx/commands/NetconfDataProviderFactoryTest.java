/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.drivers.juniperptx.exception.NetconfException;
import com.tailf.jnc.Device;
import com.tailf.jnc.NetconfSession;
import org.assertj.core.api.ThrowableAssert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class NetconfDataProviderFactoryTest {
  private static final UUID NE_ID = UUID.randomUUID();

  @Mock
  private NetconfDeviceRepository netconfDeviceRepository;
  @Mock
  private NetconfSessionManager netconfSessionManager;

  @InjectMocks
  private NetconfDataProviderFactory netconfDataProviderFactory;

  @Test
  void newDataProviderWhenNullUuidPassed() {
    // when
    ThrowableAssert.ThrowingCallable actual = () -> netconfDataProviderFactory.newDataProvider(null);

    // then
    assertThatThrownBy(actual)
      .isInstanceOf(NullPointerException.class);
    verifyNoInteractions(netconfDeviceRepository, netconfSessionManager);
  }

  @Test
  void newDataProviderWhenDeviceNotFoundForUuid() {
    //given
    when(netconfDeviceRepository.findDevice(NE_ID)).thenReturn(Optional.empty());

    // when
    ThrowableAssert.ThrowingCallable actual = () -> netconfDataProviderFactory.newDataProvider(NE_ID);

    // then
    assertThatThrownBy(actual)
      .isInstanceOf(IllegalStateException.class)
      .hasMessage("Device not found in repository for neId=" + NE_ID);
    verifyNoInteractions(netconfSessionManager);
  }
  @Test
  void newDataProviderWhenSessionCouldNotBeCreated(){
    //given
    Device deviceMock = mock();
    when(netconfDeviceRepository.findDevice(NE_ID)).thenReturn(Optional.of(deviceMock));

    // when
    ThrowableAssert.ThrowingCallable actual = () -> netconfDataProviderFactory.newDataProvider(NE_ID);

    // then
    assertThatThrownBy(actual)
      .isInstanceOf(NetconfException.class)
      .hasMessage("Cannot open new session for neId=" + NE_ID);
    verify(netconfDeviceRepository,times(1)).findDevice(NE_ID);
    verify(netconfSessionManager, times(1)).openNewSession(deviceMock, NE_ID.toString());
  }

  @Test
  void newDataProviderWhenSessionIsAlreadyActive(){
    //given
    Device deviceMock = mock();
    when(netconfDeviceRepository.findDevice(NE_ID)).thenReturn(Optional.of(deviceMock));
    NetconfSession sessionMock = mock();
    when(deviceMock.getSession(null)).thenReturn(sessionMock);

    // when
    var actual = netconfDataProviderFactory.newDataProvider(NE_ID);

    // then
    assertThat(actual)
      .extracting(NetconfDataProvider::getNeId)
      .isEqualTo(NE_ID);
    verify(netconfDeviceRepository,times(1)).findDevice(NE_ID);
    verifyNoInteractions(netconfSessionManager);
  }

  @Test
  void newDataProviderWhenSessionNeedsToBeOpened(){
    //given
    Device deviceMock = mock();
    when(netconfDeviceRepository.findDevice(NE_ID)).thenReturn(Optional.of(deviceMock));
    NetconfSession sessionMock = mock();
    when(deviceMock.getSession(null)).thenReturn(null, sessionMock);

    // when
    var actual = netconfDataProviderFactory.newDataProvider(NE_ID);

    // then
    assertThat(actual)
      .extracting(NetconfDataProvider::getNeId)
      .isEqualTo(NE_ID);
    verify(netconfDeviceRepository,times(1)).findDevice(NE_ID);
    verify(netconfSessionManager, times(1)).openNewSession(deviceMock, NE_ID.toString());
    verify(deviceMock, times(2)).getSession(null);
  }
}