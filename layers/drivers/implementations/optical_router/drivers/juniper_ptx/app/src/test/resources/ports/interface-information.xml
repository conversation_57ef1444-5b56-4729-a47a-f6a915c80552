
<nc:rpc-reply xmlns:nc="urn:ietf:params:xml:ns:netconf:base:1.0" xmlns:junos="http://xml.juniper.net/junos/24.2R1.18-EVO/junos" message-id="20880:10004">
    <interface-information xmlns="http://xml.juniper.net/junos/24.2R1.18-EVO/junos-interface" junos:style="brief">
        <physical-interface>
            <name>et-0/0/0</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <optics-properties junos:style="verbose">
                <wavelength>1528.77</wavelength>
                <frequency>196.100</frequency>
                <optic-loopback>Disabled</optic-loopback>
                <optic-loopback-type>nil</optic-loopback-type>
                <media-code-desc>400ZR, DWDM, amplified</media-code-desc>
                <host-code-desc>400GAUI-8 C2M (Annex 120E)</host-code-desc>
            </optics-properties>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-serdes-tune-error/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x800000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/0/0.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>pfh-0/0/0</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <if-type>Unspecified</if-type>
            <link-level-type>Unspecified</link-level-type>
            <mtu>Unlimited</mtu>
            <speed>1Gbps</speed>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <clocking>Unspecified</clocking>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-none/>
            </if-config-flags>
            <logical-interface>
                <name>pfh-0/0/0.16383</name>
                <if-config-flags>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>Unspecified</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>inet</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/0/1</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/0/1.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/0/2</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <optics-properties junos:style="verbose">
                <wavelength>1529.36</wavelength>
                <frequency>196.025</frequency>
                <optic-loopback>Disabled</optic-loopback>
                <optic-loopback-type>nil</optic-loopback-type>
                <media-code-desc>400ZR, DWDM, amplified</media-code-desc>
                <host-code-desc>400GAUI-8 C2M (Annex 120E)</host-code-desc>
            </optics-properties>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-rx-alarm/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x80000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/0/2.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>Ethernet-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>ethernet-switching</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/0/3</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/0/3.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/0/4</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet-Bridge</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>100Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/0/4.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>Ethernet-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>ethernet-switching</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/0/5</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet-Bridge</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>100Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/0/5.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>Ethernet-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>ethernet-switching</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/0/6</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet-Bridge</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>100Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/0/6.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>Ethernet-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>ethernet-switching</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/0/7</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>100Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/0/7.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/0/8</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <link-level-type>Ethernet-Bridge</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <optics-properties junos:style="verbose">
                <wavelength>1538.19</wavelength>
                <frequency>194.900</frequency>
                <optic-loopback>Disabled</optic-loopback>
                <optic-loopback-type>nil</optic-loopback-type>
                <media-code-desc>Vendor Specific/Custom</media-code-desc>
                <host-code-desc>400GAUI-8 C2M (Annex 120E)</host-code-desc>
            </optics-properties>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/0/8.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>Ethernet-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>ethernet-switching</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/0/9</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <link-level-type>Ethernet-Bridge</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/0/9.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>Ethernet-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>ethernet-switching</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/0/10</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet-Bridge</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/0/10.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>Ethernet-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>ethernet-switching</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/0/11</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet-Bridge</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/0/11.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>Ethernet-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>ethernet-switching</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/1/0</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet-Bridge</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/1/0.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>Ethernet-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>ethernet-switching</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/1/1</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/1/1.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/1/2</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet-Bridge</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/1/2.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>Ethernet-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>ethernet-switching</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/1/3</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet-Bridge</link-level-type>
            <mtu>9600</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>100Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/1/3.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>Ethernet-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>ethernet-switching</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/1/4</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>9600</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>100Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/1/4.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>Ethernet-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>ethernet-switching</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/1/5</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>9600</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>100Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/1/5.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>Ethernet-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>ethernet-switching</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/1/6</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>9600</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>100Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/1/6.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>Ethernet-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>ethernet-switching</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/1/7</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>9600</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>100Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/1/7.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>Ethernet-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>ethernet-switching</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/1/8</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>100Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/1/8.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/1/9</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/1/9.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/1/10</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/1/10.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/1/11</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/1/11.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/2/0</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>100Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/2/0.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>inet</address-family-name>
                    <max-local-cache>100000</max-local-cache>
                    <new-hold-limit>100000</new-hold-limit>
                    <intf-curr-cnt>0</intf-curr-cnt>
                    <intf-unresolved-cnt>0</intf-unresolved-cnt>
                    <intf-dropcnt>0</intf-dropcnt>
                    <intf-hold-nh-dropcnt>0</intf-hold-nh-dropcnt>
                    <interface-address>
                        <ifa-local junos:emit="emit">*******/24</ifa-local>
                    </interface-address>
                </address-family>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/2/1</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/2/1.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/2/2:0</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>100Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/2/2:0.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/2/3</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/2/3.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/2/4</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Extended-VLAN-Bridge</link-level-type>
            <mtu>9600</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>100Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/2/4.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <link-address junos:format="VLAN-Tag[ 0x8100.666 ] ">[ 0x8100.666 ]</link-address>
                <encapsulation>Extended-VLAN-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>ethernet-switching</address-family-name>
                </address-family>
            </logical-interface>
            <logical-interface>
                <name>et-0/2/4.32767</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <link-address junos:format="VLAN-Tag[ 0x0000.0 ] ">[ 0x0000.0 ]</link-address>
                <encapsulation>Extended-VLAN-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/2/5</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Extended-VLAN-Bridge</link-level-type>
            <mtu>9600</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>100Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/2/5.0</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <link-address junos:format="VLAN-Tag[ 0x8100.666 ] ">[ 0x8100.666 ]</link-address>
                <encapsulation>Extended-VLAN-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>ethernet-switching</address-family-name>
                </address-family>
            </logical-interface>
            <logical-interface>
                <name>et-0/2/5.32767</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <link-address junos:format="VLAN-Tag[ 0x0000.0 ] ">[ 0x0000.0 ]</link-address>
                <encapsulation>Extended-VLAN-Bridge</encapsulation>
                <dont-fragment>DF</dont-fragment>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/2/6</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>100Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/2/6.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/2/7</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>100Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/2/7.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/2/8</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/2/8.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/2/9</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/2/9.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/2/10</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/2/10.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>et-0/2/11</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>down</oper-status>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <sonet-mode>LAN-PHY</sonet-mode>
            <source-filtering>disabled</source-filtering>
            <speed>400Gbps</speed>
            <bpdu-error>None</bpdu-error>
            <ld-pdu-error>none</ld-pdu-error>
            <l2pt-error>None</l2pt-error>
            <eth-switch-error>None</eth-switch-error>
            <loopback>disabled</loopback>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <if-flow-control>enabled</if-flow-control>
            <if-media-type>Fiber</if-media-type>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-xcvr-absent/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-hardware-down/>
                <internal-flags>0x8000</internal-flags>
            </if-config-flags>
            <if-media-flags>
            </if-media-flags>
            <link-degrade-information>
                <lnk-degrd-mon>Disable</lnk-degrd-mon>
            </link-degrade-information>
            <logical-interface>
                <name>et-0/2/11.16386</name>
                <if-config-flags>
                    <iff-snmp-traps/>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>multiservice</address-family-name>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>re0:mgmt-0</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <if-type>Ethernet</if-type>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1500</mtu>
            <speed>1Gbps</speed>
            <if-auto-negotiation>enabled</if-auto-negotiation>
            <clocking>Unspecified</clocking>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
            </if-config-flags>
            <logical-interface>
                <name>re0:mgmt-0.0</name>
                <if-config-flags>
                    <iff-up/>
                </if-config-flags>
                <encapsulation>ENET2</encapsulation>
                <dont-fragment>DF</dont-fragment>
                <address-family>
                    <address-family-name>inet</address-family-name>
                    <max-local-cache>0</max-local-cache>
                    <new-hold-limit>0</new-hold-limit>
                    <intf-curr-cnt>0</intf-curr-cnt>
                    <intf-unresolved-cnt>0</intf-unresolved-cnt>
                    <intf-dropcnt>0</intf-dropcnt>
                    <intf-hold-nh-dropcnt>0</intf-hold-nh-dropcnt>
                    <interface-address>
                        <ifa-local junos:emit="emit">************/24</ifa-local>
                    </interface-address>
                </address-family>
            </logical-interface>
        </physical-interface>
        <physical-interface>
            <name>dsc</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <if-type>Software-Pseudo</if-type>
            <link-level-type>Unspecified</link-level-type>
            <mtu>Unlimited</mtu>
            <speed>Unspecified</speed>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <clocking>Unspecified</clocking>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
                <iff-point-to-point/>
            </if-config-flags>
        </physical-interface>
        <physical-interface>
            <name>esi</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <if-type>Software-Pseudo</if-type>
            <link-level-type>VxLAN-Tunnel-Endpoint</link-level-type>
            <mtu>Unlimited</mtu>
            <speed>Unlimited</speed>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <clocking>Unspecified</clocking>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-snmp-traps/>
            </if-config-flags>
        </physical-interface>
        <physical-interface>
            <name>fti0</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <if-type>FTI</if-type>
            <link-level-type>Flexible-tunnel-Interface</link-level-type>
            <mtu>Unlimited</mtu>
            <speed>Unlimited</speed>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-none/>
            </if-config-flags>
        </physical-interface>
        <physical-interface>
            <name>fti1</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <if-type>FTI</if-type>
            <link-level-type>Flexible-tunnel-Interface</link-level-type>
            <mtu>Unlimited</mtu>
            <speed>Unlimited</speed>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-none/>
            </if-config-flags>
        </physical-interface>
        <physical-interface>
            <name>fti2</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <if-type>FTI</if-type>
            <link-level-type>Flexible-tunnel-Interface</link-level-type>
            <mtu>Unlimited</mtu>
            <speed>Unlimited</speed>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-none/>
            </if-config-flags>
        </physical-interface>
        <physical-interface>
            <name>fti3</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <if-type>FTI</if-type>
            <link-level-type>Flexible-tunnel-Interface</link-level-type>
            <mtu>Unlimited</mtu>
            <speed>Unlimited</speed>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-none/>
            </if-config-flags>
        </physical-interface>
        <physical-interface>
            <name>fti4</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <if-type>FTI</if-type>
            <link-level-type>Flexible-tunnel-Interface</link-level-type>
            <mtu>Unlimited</mtu>
            <speed>Unlimited</speed>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-none/>
            </if-config-flags>
        </physical-interface>
        <physical-interface>
            <name>fti5</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <if-type>FTI</if-type>
            <link-level-type>Flexible-tunnel-Interface</link-level-type>
            <mtu>Unlimited</mtu>
            <speed>Unlimited</speed>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-none/>
            </if-config-flags>
        </physical-interface>
        <physical-interface>
            <name>fti6</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <if-type>FTI</if-type>
            <link-level-type>Flexible-tunnel-Interface</link-level-type>
            <mtu>Unlimited</mtu>
            <speed>Unlimited</speed>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-none/>
            </if-config-flags>
        </physical-interface>
        <physical-interface>
            <name>fti7</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <if-type>FTI</if-type>
            <link-level-type>Flexible-tunnel-Interface</link-level-type>
            <mtu>Unlimited</mtu>
            <speed>Unlimited</speed>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-none/>
            </if-config-flags>
        </physical-interface>
        <physical-interface>
            <name>irb</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <if-type>Ethernet</if-type>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <speed>1Gbps</speed>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <clocking>Unspecified</clocking>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-none/>
            </if-config-flags>
        </physical-interface>
        <physical-interface>
            <name>lo0</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <if-type>Loopback</if-type>
            <link-level-type>Unspecified</link-level-type>
            <mtu>Unlimited</mtu>
            <speed>Unspecified</speed>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <clocking>Unspecified</clocking>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
                <ifdf-loopback/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-none/>
            </if-config-flags>
        </physical-interface>
        <physical-interface>
            <name>lsi</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <if-type>Software-Pseudo</if-type>
            <link-level-type>LSI</link-level-type>
            <mtu>Unlimited</mtu>
            <speed>Unlimited</speed>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <clocking>Unspecified</clocking>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-none/>
            </if-config-flags>
        </physical-interface>
        <physical-interface>
            <name>pip0</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <if-type>Ethernet</if-type>
            <link-level-type>Ethernet</link-level-type>
            <mtu>1514</mtu>
            <speed>Unspecified</speed>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <clocking>Unspecified</clocking>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-none/>
            </if-config-flags>
        </physical-interface>
        <physical-interface>
            <name>vtep</name>
            <admin-status junos:format="Enabled">up</admin-status>
            <oper-status>up</oper-status>
            <if-type>Software-Pseudo</if-type>
            <link-level-type>VxLAN-Tunnel-Endpoint</link-level-type>
            <mtu>Unlimited</mtu>
            <speed>Unlimited</speed>
            <if-auto-negotiation>disabled</if-auto-negotiation>
            <clocking>Unspecified</clocking>
            <if-device-flags>
                <ifdf-present/>
                <ifdf-running/>
            </if-device-flags>
            <ifd-specific-config-flags>
            </ifd-specific-config-flags>
            <if-config-flags>
                <iff-none/>
            </if-config-flags>
        </physical-interface>
    </interface-information>
</nc:rpc-reply>
