/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.drivers.juniperptx.exception.NetconfException;
import com.tailf.jnc.Device;
import com.tailf.jnc.NetconfSession;
import org.springframework.beans.factory.annotation.Value;

import java.util.Optional;
import java.util.UUID;

import static java.util.Objects.requireNonNull;

class NetconfDataProviderFactory {

  private final NetconfDeviceRepository repository;
  private final NetconfSessionManager netconfSessionManager;

  @Value(value = "${app.netconf.session-name}")
  private String netconfSessionName;

  NetconfDataProviderFactory(NetconfSessionManager netconfSessionManager, NetconfDeviceRepository repository) {
    this.netconfSessionManager = netconfSessionManager;
    this.repository = repository;
  }

  NetconfDataProvider newDataProvider(UUID neId) {
    requireNonNull(neId);

    NetconfSession netconfSession = getNetconfSession(neId);
    if (netconfSession == null) {
      throw new NetconfException("Cannot open new session for neId=" + neId);
    }
    return new NetconfDataProvider(neId, netconfSession);
  }

  private NetconfSession getNetconfSession(UUID neId) {
    Device device = getDevice(neId);

    return Optional.of(device)
      .map(d -> d.getSession(netconfSessionName))
      .orElseGet(() -> createAndGetNewSession(device, neId));
  }

  private Device getDevice(UUID neId) {
    return repository.findDevice(neId)
      .orElseThrow(() -> new IllegalStateException("Device not found in repository for neId=" + neId));
  }

  private NetconfSession createAndGetNewSession(Device device, UUID neId) {
    netconfSessionManager.openNewSession(device, neId.toString());
    return device.getSession(netconfSessionName);
  }
}
