/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock;

public class NetconfServerConstants {
  private NetconfServerConstants() {
    throw new IllegalStateException("Constants only class");
  }

  public static final String DEFAULT_IP = "127.0.0.1"; //"************";
  public static final int DEFAULT_PORT = 830;
  public static final String DEFAULT_USERNAME = "admin";
  public static final String DEFAULT_PASSWORD = "chgme.1";
  //default path to host key generated for sshd mock server
  public static final String DEFAULT_HOST_KEY_PATH = "src/test/resources/hostkey.ser";

  public static final String DEFAULT_BASE_TEMPLATES_PATH = "src/test/resources/netconf/response/templates/";
  public static final String DEFAULT_TEMPLATES_SUFFIX = ".xml";
}
