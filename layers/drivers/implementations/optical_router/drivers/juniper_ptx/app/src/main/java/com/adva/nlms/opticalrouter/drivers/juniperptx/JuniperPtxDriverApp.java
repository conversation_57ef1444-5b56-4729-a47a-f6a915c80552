/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx;

import com.adva.nlms.infrastucture.security.api.AppBeans;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(scanBasePackages = {"com.adva.nlms.opticalrouter.drivers.juniperptx", AppBeans.APP_BEANS_SCAN_PACKAGE})
@EnableConfigurationProperties(DriverRegistration.RegistrationConfig.class)
@ConfigurationPropertiesScan
@EnableAsync
class JuniperPtxDriverApp {
  public static void main(String[] args) {
    SpringApplication.run(JuniperPtxDriverApp.class, args);
  }
}
