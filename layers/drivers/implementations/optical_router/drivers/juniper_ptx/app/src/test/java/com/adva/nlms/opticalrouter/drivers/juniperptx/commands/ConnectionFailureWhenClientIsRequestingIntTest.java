/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.api.commands.CommandResponse;
import com.adva.nlms.opticalrouter.api.commands.CommunicationSettings;
import com.adva.nlms.opticalrouter.api.commands.OpticalRouterDeviceDriver;
import com.adva.nlms.opticalrouter.api.commands.PasswordAuthenticationData;
import com.adva.nlms.opticalrouter.api.commands.Status;
import com.adva.nlms.opticalrouter.api.resources.ConnectionFaultCause;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterConnectionFaultCause;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterDeviceInfo;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterInventory;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterStatus;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.NetconfMockServerConfiguration;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.NetconfMockServerManager;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.NetconfServerConstants;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.ResponseTemplate;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.junit.NetconfMockingExtension;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.junit.NetconfServer;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.Duration;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(classes = {CommandConfiguration.class, NetconfMockServerConfiguration.class, TestCommandResponderConfiguration.class, TestExecutorServiceConfiguration.class})
@ExtendWith(NetconfMockingExtension.class)
@NetconfServer
public class ConnectionFailureWhenClientIsRequestingIntTest {
  private static final int TIMEOUT = 5;
  private static final UUID DEVICE_ID = UUID.fromString("d0baf626-c566-47c8-9ba5-390437d0913a");

  @Autowired
  private OpticalRouterDeviceDriver opticalRouterDeviceDriver;
  @Autowired
  private NetconfMockServerManager manager;
  @Autowired
  private TestCommandResponderConfiguration.TestCommandResponder testCommandResponder;

  private CommunicationSettings communicationSettings;

  @BeforeEach
  void setUp() {
    communicationSettings = new CommunicationSettings("TestDevice",
      manager.getCurrentHost(),
      manager.getCurrentPort(),
      TIMEOUT,
      new PasswordAuthenticationData(NetconfServerConstants.DEFAULT_USERNAME, NetconfServerConstants.DEFAULT_PASSWORD));
  }

  @Test
  void synchronizeDeviceInfoWhenDeviceDisconnects() {
    manager.requestContains("<hello")
      .respondWithTemplate(ResponseTemplate.HELLO)
      .andTemplateParameter("sessionId", 123);
    manager.requestContains("<get-software-information")
      .respondWithEOF();

    opticalRouterDeviceDriver.connect(DEVICE_ID, communicationSettings);
    opticalRouterDeviceDriver.synchronizeDeviceInfo(DEVICE_ID);

    var actual = testCommandResponder.getPrevResponse();
    assertThat(actual).extracting(CommandResponse::status).isEqualTo(Status.FAILURE);
    assertThat(actual).extracting(CommandResponse::data)
      .isInstanceOf(OpticalRouterConnectionFaultCause.class)
      .asInstanceOf(InstanceOfAssertFactories.type(OpticalRouterConnectionFaultCause.class))
      .extracting(OpticalRouterConnectionFaultCause::connectionFaultCause).isEqualTo(ConnectionFaultCause.SESSION_CLOSED);
    actual = testCommandResponder.getLastResponse();
    assertThat(actual).extracting(CommandResponse::deviceId, CommandResponse::status)
      .containsExactly(DEVICE_ID, Status.FAILURE);
    assertThat(actual.data()).isInstanceOf(OpticalRouterDeviceInfo.class);
    assertThat(actual.message()).startsWith("Device info synchronization failed on neId=%s due to lost connection".formatted(DEVICE_ID));
  }

  @Test
  void synchronizeInventoryWhenDeviceDisconnects() {
    manager.requestContains("<hello")
      .respondWithTemplate(ResponseTemplate.HELLO)
      .andTemplateParameter("sessionId", 123);
    manager.requestMatches(RpcCommands.GET_INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS)
      .respondWithEOF();

    opticalRouterDeviceDriver.connect(DEVICE_ID, communicationSettings);
    opticalRouterDeviceDriver.synchronizeInventory(DEVICE_ID);

    var actual = testCommandResponder.getPrevResponse();
    assertThat(actual).extracting(CommandResponse::status).isEqualTo(Status.FAILURE);
    assertThat(actual).extracting(CommandResponse::data)
      .isInstanceOf(OpticalRouterConnectionFaultCause.class)
      .asInstanceOf(InstanceOfAssertFactories.type(OpticalRouterConnectionFaultCause.class))
      .extracting(OpticalRouterConnectionFaultCause::connectionFaultCause).isEqualTo(ConnectionFaultCause.SESSION_CLOSED);
    actual = testCommandResponder.getLastResponse();
    assertThat(actual).extracting(CommandResponse::deviceId, CommandResponse::status)
      .containsExactly(DEVICE_ID, Status.FAILURE);
    assertThat(actual.data()).isInstanceOf(OpticalRouterInventory.class);
    assertThat(actual.message()).startsWith("Inventory synchronization failed on neId=%s due to lost connection".formatted(DEVICE_ID));
  }

  @Test
  void synchronizeStatusWhenDeviceDisconnects() {
    manager.requestContains("<hello")
      .respondWithTemplate(ResponseTemplate.HELLO)
      .andTemplateParameter("sessionId", 123);
    manager.requestMatches(RpcCommands.GET_INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS)
      .respondWithEOF();

    opticalRouterDeviceDriver.connect(DEVICE_ID, communicationSettings);
    opticalRouterDeviceDriver.synchronizeStatus(DEVICE_ID);

    var actual = testCommandResponder.getPrevResponse();
    assertThat(actual).extracting(CommandResponse::status).isEqualTo(Status.FAILURE);
    assertThat(actual).extracting(CommandResponse::data)
      .isInstanceOf(OpticalRouterConnectionFaultCause.class)
      .asInstanceOf(InstanceOfAssertFactories.type(OpticalRouterConnectionFaultCause.class))
      .extracting(OpticalRouterConnectionFaultCause::connectionFaultCause).isEqualTo(ConnectionFaultCause.SESSION_CLOSED);
    actual = testCommandResponder.getLastResponse();
    assertThat(actual).extracting(CommandResponse::deviceId, CommandResponse::status)
      .containsExactly(DEVICE_ID, Status.FAILURE);
    assertThat(actual.data()).isInstanceOf(OpticalRouterStatus.class);
    assertThat(actual.message()).startsWith("Status synchronization failed on neId=%s due to lost connection".formatted(DEVICE_ID));
  }

  @Test
  @Disabled
  void synchronizeDeviceInfoWhenDeviceHangsOnResponseAndHitsTimeout() {
    manager.requestContains("<hello")
      .respondWithTemplate(ResponseTemplate.HELLO)
      .andTemplateParameter("sessionId", 123);

    manager.requestContains("<get-software-information")
      .simulateResponseHangingFor(Duration.ofSeconds(TIMEOUT + 2));

    opticalRouterDeviceDriver.connect(DEVICE_ID, communicationSettings);
    opticalRouterDeviceDriver.synchronizeDeviceInfo(DEVICE_ID);

    var actual = testCommandResponder.getLastResponse();
    assertThat(actual).extracting(CommandResponse::status)
      .isEqualTo(Status.FAILURE);

    assertThat(actual).extracting(CommandResponse::data)
      .isInstanceOf(OpticalRouterConnectionFaultCause.class)
      .asInstanceOf(InstanceOfAssertFactories.type(OpticalRouterConnectionFaultCause.class))
      .extracting(OpticalRouterConnectionFaultCause::connectionFaultCause)
      .isEqualTo(ConnectionFaultCause.SESSION_CLOSED);
  }
}
