/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.api.resources.ConnectionFaultCause;
import com.adva.nlms.opticalrouter.drivers.juniperptx.exception.ConnectionException;
import com.adva.nlms.opticalrouter.drivers.juniperptx.exception.NetconfException;
import com.tailf.jnc.Element;
import com.tailf.jnc.JNCException;
import com.tailf.jnc.NetconfSession;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

class NetconfDataProvider {
  private final UUID neId;
  private final NetconfSession netconfSession;
  private final Map<String, Element> requestCache = new HashMap<>();

  NetconfDataProvider(UUID neId, NetconfSession netconfSession) {
    this.neId = neId;
    this.netconfSession = netconfSession;
  }

  UUID getNeId() {
    return neId;
  }

  Element getElement(String command) {
    Objects.requireNonNull(command);
    Objects.requireNonNull(netconfSession);

    if (requestCache.containsKey(command)) {
      return requestCache.get(command);
    }
    try {
      Element response = netconfSession.rpc(command);
      requestCache.put(command, response);
      return response;
    } catch (IOException ioException){
      if (ioException.getMessage().equals("Session closed")) {
        throw new ConnectionException(ConnectionFaultCause.SESSION_CLOSED, ioException.getMessage(), ioException);
      }
      throw new NetconfException("Error while reading command=%s from node=%s: %s".formatted(command, neId, ioException.getMessage()), ioException);
    } catch (JNCException e) {
      throw new NetconfException("Error while reading command=%s from node=%s: %s".formatted(command, neId, e.getMessage()), e);
    }
  }
}
