/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.api.commands.CommunicationSettings;
import com.adva.nlms.opticalrouter.api.commands.PasswordAuthenticationData;
import com.tailf.jnc.Device;
import com.tailf.jnc.SSHConnection;
import com.tailf.jnc.SSHSession;
import net.schmizz.sshj.SSHClient;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ConnectionCommandProcessorTest {

  private static final String IP = "127.0.0.1";
  private static final int PORT = 830;
  private static final String USERNAME = "admin";
  private static final String PASSWORD = "admin";
  private static final int TIMEOUT = 42;
  private static final UUID DEVICE_ID = UUID.fromString("d0baf626-c566-47c8-9ba5-390437d0913a");

  @Mock
  NetconfDeviceRepository netconfDeviceRepository;

  @InjectMocks
  ConnectionCommandProcessor connectionCommandProcessor;

  @Test
  void invokeConnect_missingAuthenticationData() {
    assertThrows(UnsupportedOperationException.class, () -> connectionCommandProcessor.connect(DEVICE_ID,
      new CommunicationSettings("TestDevice", IP, PORT, TIMEOUT, null)));
  }

  @Test
  void invokeConnect_missingNeId() {
    assertThrows(NullPointerException.class, () -> connectionCommandProcessor.connect(null, buildCommunicationSettings()));
  }

  @Test
  void invokeConnect_missingCommunicationSetting() {
    assertThrows(NullPointerException.class, () -> connectionCommandProcessor.connect(DEVICE_ID, null));
  }

  @Test
  void invokeDisconnect_missingNeId() {
    assertThrows(NullPointerException.class, () -> connectionCommandProcessor.disconnect(null));
  }

  @Test
  void invokeIsConnected_missingNeId() {
    assertThrows(NullPointerException.class, () -> connectionCommandProcessor.isConnected(null));
  }

  @Test
  void invokeDisconnect_deviceSSHSessionNotFound() {
    //given
    when(netconfDeviceRepository.removeDevice(DEVICE_ID)).thenReturn(Optional.empty());
    assertDoesNotThrow(() -> connectionCommandProcessor.disconnect(DEVICE_ID));
  }

  @Test
  void invokeIsConnected_deviceSSHSessionNotFound() {
    //given
    var device = mock(Device.class);

    when(netconfDeviceRepository.findDevice(DEVICE_ID)).thenReturn(Optional.of(device));
    when(device.getSSHSession(null)).thenReturn(null);

    //when
    var isConnected = connectionCommandProcessor.isConnected(DEVICE_ID);

    //then
    assertFalse(isConnected);
  }

  @Test
  void invokeIsConnected_throwIOExceptionWhenSessionIsServerSideClosed() throws IOException {
    //given
    var device = mock(Device.class);
    var netconfSession = mock(SSHSession.class);

    when(netconfDeviceRepository.findDevice(DEVICE_ID)).thenReturn(Optional.of(device));
    when(device.getSSHSession(null)).thenReturn(netconfSession);
    when(netconfSession.serverSideClosed()).thenThrow(IOException.class);

    //when
    var isConnected = connectionCommandProcessor.isConnected(DEVICE_ID);

    //then
    assertFalse(isConnected);
  }

  @Test
  void invokeIsConnected_happyPath() throws IOException {
    //given
    Device device = mock();
    SSHSession netconfSession = mock();
    SSHConnection sshConnection = mock();
    SSHClient sshClient = mock();

    when(netconfDeviceRepository.findDevice(DEVICE_ID)).thenReturn(Optional.of(device));
    when(device.getSSHSession(null)).thenReturn(netconfSession);
    when(netconfSession.serverSideClosed()).thenReturn(false);
    when(netconfSession.getSSHConnection()).thenReturn(sshConnection);
    when(sshConnection.getClient()).thenReturn(sshClient);
    when(sshClient.isConnected()).thenReturn(true);

    //when
    var isConnected = connectionCommandProcessor.isConnected(DEVICE_ID);

    //then
    assertTrue(isConnected);
  }

  private static CommunicationSettings buildCommunicationSettings() {
    return new CommunicationSettings("TestDevice", IP, PORT, TIMEOUT, new PasswordAuthenticationData(USERNAME, PASSWORD));
  }
}
