/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.exception;

import com.adva.nlms.opticalrouter.api.resources.ConnectionFaultCause;

public class ConnectionException extends RuntimeException {

  private final ConnectionFaultCause connectionFaultCause;

  public ConnectionException(ConnectionFaultCause connectionFaultCause, String message, Throwable cause) {
    super(message, cause);
    this.connectionFaultCause = connectionFaultCause;
  }

  public ConnectionFaultCause getConnectionFaultCause() {
    return connectionFaultCause;
  }
}
