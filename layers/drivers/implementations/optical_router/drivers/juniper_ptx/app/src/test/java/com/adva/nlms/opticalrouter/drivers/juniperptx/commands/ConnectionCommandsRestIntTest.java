/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.opticalrouter.api.commands.CommandResponse;
import com.adva.nlms.opticalrouter.api.commands.CommunicationSettings;
import com.adva.nlms.opticalrouter.api.commands.OpticalRouterCommandException;
import com.adva.nlms.opticalrouter.api.commands.OpticalRouterDeviceDriver;
import com.adva.nlms.opticalrouter.api.commands.PasswordAuthenticationData;
import com.adva.nlms.opticalrouter.api.commands.Status;
import com.adva.nlms.opticalrouter.api.resources.AdminState;
import com.adva.nlms.opticalrouter.api.resources.OperationalState;
import com.adva.nlms.opticalrouter.api.resources.OpticalChannel;
import com.adva.nlms.opticalrouter.api.resources.OpticalConfiguration;
import com.adva.nlms.opticalrouter.api.resources.OpticalPlug;
import com.adva.nlms.opticalrouter.api.resources.OpticalPort;
import com.adva.nlms.opticalrouter.api.resources.OpticalResourceStatus;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterDeviceInfo;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterInventory;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterStatus;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.NetconfMockServerConfiguration;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.NetconfMockServerManager;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.ResponseTemplate;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.junit.NetconfMockingExtension;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.junit.NetconfServer;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

@SpringBootTest(classes = {CommandConfiguration.class, NetconfMockServerConfiguration.class,
  TestCommandResponderConfiguration.class, TestExecutorServiceConfiguration.class})
@ExtendWith(NetconfMockingExtension.class)
@NetconfServer
class ConnectionCommandsRestIntTest {
  private static final String USERNAME = "admin";
  private static final String PASSWORD = "chgme.1";
  private static final int TIMEOUT = 20;
  private static final UUID DEVICE_ID = UUID.fromString("d0baf626-c566-47c8-9ba5-390437d0913a");

  @Autowired
  private OpticalRouterDeviceDriver opticalRouterDeviceDriver;
  @Autowired
  private TestCommandResponderConfiguration.TestCommandResponder testCommandResponder;
  @Autowired
  private NetconfMockServerManager manager;
  @Autowired
  private NetconfDeviceRepository repository;

  @BeforeEach
  void setup() {
    manager.requestContains("<hello")
      .respondWithTemplate(ResponseTemplate.HELLO)
      .andTemplateParameter("sessionId", 123);
  }

  @Test
  void invokeConnect_alreadyConnected() {
    connect();
    var before = repository.findDevice(DEVICE_ID);
    // Clear manager to prevent multiple requests error
    manager.clear();
    manager.requestContains("<hello")
      .respondWithTemplate(ResponseTemplate.HELLO)
      .andTemplateParameter("sessionId", 123);
    connect();
    var actual = repository.findDevice(DEVICE_ID);
    assertThat(actual).isNotEqualTo(before);
  }

  @Test
  void checkColdStartTime_notConnected() {
    assertThrows(IllegalStateException.class, () -> opticalRouterDeviceDriver.getColdStartTime(DEVICE_ID));
    manager.clear();
  }

  @Test
  void synchronizeDeviceInfo_notConnected() {
    assertThrows(IllegalStateException.class, () -> opticalRouterDeviceDriver.synchronizeDeviceInfo(DEVICE_ID));
    manager.clear();
  }

  @Test
  void synchronizeInventory_notConnected() {
    assertThrows(IllegalStateException.class, () -> opticalRouterDeviceDriver.synchronizeInventory(DEVICE_ID));
    manager.clear();
  }

  @Test
  void synchronizeStatus_notConnected() {
    assertThrows(IllegalStateException.class, () -> opticalRouterDeviceDriver.synchronizeStatus(DEVICE_ID));
    manager.clear();
  }

  @Test
  void connectThenCheckColdStartTime() throws OpticalRouterCommandException {
    manager.requestContains("<get-system-uptime-information")
      .respondWithTemplate(ResponseTemplate.GET_SYSTEM_UPTIME_INFORMATION);

    connect();
    checkConnection(true);
    checkColdStartTime();
    disconnect();
  }

  @Test
  void connectThenCheckConnection() throws OpticalRouterCommandException {
    connect();
    checkConnection(true);
    disconnect();
    checkConnection(false);
  }

  @Test
  void connectThenSynchronizeDeviceInfo_invalidMessage() throws OpticalRouterCommandException {
    manager.requestContains("<get-software-information")
      .respondWithTemplate(ResponseTemplate.GET_SOFTWARE_INFORMATION_INVALID);

    connect();
    checkConnection(true);
    opticalRouterDeviceDriver.synchronizeDeviceInfo(DEVICE_ID);
    var actual = testCommandResponder.getLastResponse();
    assertThat(actual).extracting(CommandResponse::deviceId, CommandResponse::status)
      .containsExactly(DEVICE_ID, Status.FAILURE);
    assertThat(actual.message()).startsWith("Device info synchronization failed on neId=%s".formatted(DEVICE_ID));
    assertThat(actual.data()).isInstanceOf(OpticalRouterDeviceInfo.class);
    disconnect();
  }

  @Test
  void connectThenSynchronizeDeviceInfo() throws OpticalRouterCommandException {
    final String serialNumber = "HA233";
    final String deviceType = "ptx10001";
    final String name = "ptx10001-36mr";
    final String softwareVersion = "24.2R1.18-EVO";
    final String semanticSoftwareVersion = "24.2.1";
    manager.requestContains("<get-software-information")
      .respondWithTemplate(ResponseTemplate.GET_SOFTWARE_INFORMATION)
      .andTemplateParameter("deviceType", deviceType)
      .andTemplateParameter("softwareVersion", softwareVersion)
      .andTemplateParameter("name", name);
    manager.requestContains("<get-chassis-inventory")
      .respondWithTemplate(ResponseTemplate.GET_CHASSIS_INVENTORY)
      .andTemplateParameter("serialNumber", serialNumber);

    connect();
    checkConnection(true);
    synchronizeDeviceInfo(name, deviceType, serialNumber, softwareVersion, semanticSoftwareVersion);
    disconnect();
  }

  @Test
  void connectThenSynchronizeInventory_invalidMessage() throws OpticalRouterCommandException {
    manager.requestMatches(RpcCommands.GET_INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS)
      .respondWithTemplate(ResponseTemplate.GET_INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS);

    connect();
    checkConnection(true);
    opticalRouterDeviceDriver.synchronizeInventory(DEVICE_ID);
    var actual = testCommandResponder.getLastResponse();
    assertThat(actual).extracting(CommandResponse::deviceId, CommandResponse::status)
      .containsExactly(DEVICE_ID, Status.FAILURE);
    assertThat(actual.data()).isInstanceOf(OpticalRouterInventory.class);
    assertThat(actual.message()).startsWith("Inventory synchronization failed on neId=%s".formatted(DEVICE_ID));
    disconnect();
  }

  @Test
  void connectThenSynchronizeInventory() throws OpticalRouterCommandException {
    String et000 = "et-0/0/0";
    String et002 = "et-0/0/2";

    var et0000OpticalPort = buildOpticalPort("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-0",
      et000, LayerQualifier.ET400ZR.toString(), AdminState.UP, OperationalState.OUTAGE, new BigDecimal("196.10"), new BigDecimal("75.0"), new BigDecimal("0.0"));

    var et0002OpticalPort = buildOpticalPort("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-2",
      et002, LayerQualifier.ET100ZR.toString(), AdminState.UP, OperationalState.NORMAL, new BigDecimal("193.50"), new BigDecimal("100.0"), new BigDecimal("-5.0"));


    var et000OpticalPlug = new OpticalPlug("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-0",
      "plug-0/0/0",
      AdminState.UP,
      OperationalState.OUTAGE,
      "QSFP56-DD-400G-ZR-M",
      "Vendor 0",
      "Vendor Part Number 0",
      "Serial number 0",
      "Vendor Firmware 0");

    var et002OpticalPlug = new OpticalPlug("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-2",
      "plug-0/0/2",
      AdminState.UP,
      OperationalState.NORMAL,
      "QSFP56-DD-400G-ZR-M",
      "Vendor 2",
      "Vendor Part Number 2",
      "Serial number 2",
      "Vendor Firmware 2");

    manager.requestMatches(RpcCommands.GET_INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS)
      .respondWithTemplate(ResponseTemplate.GET_INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS)
      .andTemplateParameter("portName0", et000)
      .andTemplateParameter("portName2", et002);

    manager.requestMatches(String.format(RpcCommands.GET_INTERFACE_INFORMATION, et000))
      .respondWithTemplate(ResponseTemplate.GET_INTERFACE_INFORMATION)
      .andTemplateParameter("portName", et000)
      .andTemplateParameter("speed", "400Gbps")
      .andTemplateParameter("adminStatus", "up")
      .andTemplateParameter("operStatus", "down")
      .andTemplateParameter("frequency", "196.10")
      .andTemplateParameter("mediaCodeDesc", "400ZR, DWDM, amplified");

    manager.requestMatches(String.format(RpcCommands.GET_INTERFACE_INFORMATION, et002))
      .respondWithTemplate(ResponseTemplate.GET_INTERFACE_INFORMATION)
      .andTemplateParameter("portName", et002)
      .andTemplateParameter("speed", "100Gbps")
      .andTemplateParameter("adminStatus", "up")
      .andTemplateParameter("operStatus", "up")
      .andTemplateParameter("frequency", "193.50")
      .andTemplateParameter("mediaCodeDesc", "ZR400-OFEC-8QAM-HA");

    manager.requestMatches(String.format(RpcCommands.GET_CONFIG_RUNNING_WITH_TX_POWER, et000))
      .respondWithTemplate(ResponseTemplate.GET_TX_POWER)
      .andTemplateParameter("portName", et000)
      .andTemplateParameter("txPower", "0.0");

    manager.requestMatches(String.format(RpcCommands.GET_CONFIG_RUNNING_WITH_TX_POWER, et002))
      .respondWithTemplate(ResponseTemplate.GET_TX_POWER)
      .andTemplateParameter("portName", et002)
      .andTemplateParameter("txPower", "-5.0");

    manager.requestMatches(String.format(RpcCommands.GET_PIC_DETAIL, "0", "0"))
      .respondWithTemplate(ResponseTemplate.GET_PIC_DETAIL)
      .andTemplateParameter("vendorName0", et000OpticalPlug.vendor())
      .andTemplateParameter("vendorPartNumber0", et000OpticalPlug.vendorPartNumber())
      .andTemplateParameter("vendorFirmwareVersion0", et000OpticalPlug.firmwareVersion())
      .andTemplateParameter("vendorName2", et002OpticalPlug.vendor())
      .andTemplateParameter("vendorPartNumber2", et002OpticalPlug.vendorPartNumber())
      .andTemplateParameter("vendorFirmwareVersion2", et002OpticalPlug.firmwareVersion());

    manager.requestMatches(RpcCommands.GET_CHASSIS_INVENTORY)
      .respondWithTemplate(ResponseTemplate.GET_CHASSIS_INVENTORY)
      .andTemplateParameter("serialNumber", "HA233")
      .andTemplateParameter("serialNumber0", et000OpticalPlug.serialNumber())
      .andTemplateParameter("plugType0", et000OpticalPlug.plugType())
      .andTemplateParameter("serialNumber2", et002OpticalPlug.serialNumber())
      .andTemplateParameter("plugType2", et002OpticalPlug.plugType());

    connect();
    checkConnection(true);
    synchronizeInventory(List.of(et0002OpticalPort, et0000OpticalPort), List.of(et000OpticalPlug, et002OpticalPlug));
    disconnect();
  }

  @Test
  void connectThenSynchronizeStatus_invalidMessage() throws OpticalRouterCommandException {
    manager.requestMatches(RpcCommands.GET_INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS)
      .respondWithTemplate(ResponseTemplate.GET_INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS);

    connect();
    checkConnection(true);
    opticalRouterDeviceDriver.synchronizeStatus(DEVICE_ID);
    var actual = testCommandResponder.getLastResponse();
    assertThat(actual).extracting(CommandResponse::deviceId, CommandResponse::status)
      .containsExactly(DEVICE_ID, Status.FAILURE);
    assertThat(actual.message()).startsWith("Status synchronization failed on neId=%s".formatted(DEVICE_ID));
    assertThat(actual.data()).isInstanceOf(OpticalRouterStatus.class);
    disconnect();
  }

  @Test
  void connectThenSynchronizeStatus() {
    String et000 = "et-0/0/0";
    String et002 = "et-0/0/2";

    List<OpticalResourceStatus> expectedOpticalProtStatuses = List.of(
      new OpticalResourceStatus("/ne=%s///p=%s".formatted(DEVICE_ID, et000.replace("/", "-")), OperationalState.NORMAL),
      new OpticalResourceStatus("/ne=%s///p=%s".formatted(DEVICE_ID, et002.replace("/", "-")), OperationalState.OUTAGE)
    );

    manager.requestMatches(RpcCommands.GET_INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS)
      .respondWithTemplate(ResponseTemplate.GET_INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS)
      .andTemplateParameter("portName0", et000)
      .andTemplateParameter("portName2", et002);

    manager.requestMatches(String.format(RpcCommands.GET_INTERFACE_INFORMATION, et000))
      .respondWithTemplate(ResponseTemplate.GET_INTERFACE_INFORMATION)
      .andTemplateParameter("portName", et000)
      .andTemplateParameter("operStatus", "up");

    manager.requestMatches(String.format(RpcCommands.GET_INTERFACE_INFORMATION, et002))
      .respondWithTemplate(ResponseTemplate.GET_INTERFACE_INFORMATION)
      .andTemplateParameter("portName", et002)
      .andTemplateParameter("operStatus", "down");

    connect();
    checkConnection(true);
    synchronizeStatus(expectedOpticalProtStatuses);
    disconnect();
  }

  private void synchronizeStatus(List<OpticalResourceStatus> expectedOpticalPortStatuses) {
    opticalRouterDeviceDriver.synchronizeStatus(DEVICE_ID);
    var actual = testCommandResponder.getLastResponse();
    assertThat(actual).extracting(CommandResponse::deviceId, CommandResponse::status, CommandResponse::message)
      .containsExactly(DEVICE_ID, Status.SUCCESS, "Status with neId=%s has been synchronized".formatted(DEVICE_ID));
    assertThat(actual.data()).isInstanceOf(OpticalRouterStatus.class);
    var status = (OpticalRouterStatus) (actual.data());

    var opticalPortStatuses = status.ports();
    assertThat(opticalPortStatuses).containsExactlyInAnyOrderElementsOf(expectedOpticalPortStatuses);
  }

  private void connect() throws OpticalRouterCommandException {
    var communicationSettings = new CommunicationSettings("TestDevice",
      manager.getCurrentHost(),
      manager.getCurrentPort(),
      TIMEOUT,
      new PasswordAuthenticationData(USERNAME, PASSWORD));
    opticalRouterDeviceDriver.connect(DEVICE_ID, communicationSettings);
    var actual = repository.findDevice(DEVICE_ID);
    assertThat(actual).isPresent();
  }

  private void checkConnection(Boolean expected) throws OpticalRouterCommandException {
    var response = opticalRouterDeviceDriver.isConnected(DEVICE_ID);
    assertEquals(expected, response);
  }

  private void disconnect() throws OpticalRouterCommandException {
    opticalRouterDeviceDriver.disconnect(DEVICE_ID);

    var actual = repository.findDevice(DEVICE_ID);
    assertThat(actual).isEmpty();
  }

  private void checkColdStartTime() throws OpticalRouterCommandException {
    var actualResponse = opticalRouterDeviceDriver.getColdStartTime(DEVICE_ID);
    assertThat(actualResponse).isEqualTo(1741169166);
  }

  @SuppressWarnings("SameParameterValue")
  private void synchronizeDeviceInfo(String name, String deviceType, String serialNumber, String softwareVersion, String semanticSoftwareVersion) {
    opticalRouterDeviceDriver.synchronizeDeviceInfo(DEVICE_ID);
    var actual = testCommandResponder.getLastResponse();
    assertThat(actual).extracting(CommandResponse::deviceId, CommandResponse::status, CommandResponse::message)
      .containsExactly(DEVICE_ID, Status.SUCCESS, "Device info with neId=%s has been synchronized".formatted(DEVICE_ID));
    assertThat(actual.data()).isNotNull()
      .asInstanceOf(InstanceOfAssertFactories.type(OpticalRouterDeviceInfo.class))
      .extracting(
        OpticalRouterDeviceInfo::name,
        OpticalRouterDeviceInfo::deviceType,
        OpticalRouterDeviceInfo::serialNumber,
        OpticalRouterDeviceInfo::softwareVersion,
        OpticalRouterDeviceInfo::semanticSoftwareVersion
      )
      .containsExactly(name, deviceType, serialNumber, softwareVersion, semanticSoftwareVersion);
  }

  @SuppressWarnings("SameParameterValue")
  private void synchronizeInventory(List<OpticalPort> opticalPorts, List<OpticalPlug> opticalPlugs) throws OpticalRouterCommandException {
    opticalRouterDeviceDriver.synchronizeInventory(DEVICE_ID);
    var actual = testCommandResponder.getLastResponse();
    assertThat(actual).extracting(CommandResponse::deviceId, CommandResponse::status, CommandResponse::message)
      .containsExactly(DEVICE_ID, Status.SUCCESS, "Inventory with neId=%s has been synchronized".formatted(DEVICE_ID));
    assertThat(actual.data()).isInstanceOf(OpticalRouterInventory.class);
    var inventory = (OpticalRouterInventory) (actual.data());

    var ports = inventory.ports();
    assertThat(ports).containsExactlyInAnyOrderElementsOf(opticalPorts);
    var plugs = inventory.plugs();
    assertThat(plugs).containsExactlyInAnyOrderElementsOf(opticalPlugs);
  }

  private OpticalPort buildOpticalPort(String networkResourceLocator,
                                       String label,
                                       String payload,
                                       @SuppressWarnings("SameParameterValue") AdminState adminState,
                                       OperationalState operationalState,
                                       BigDecimal centerFrequency,
                                       BigDecimal slotWidth,
                                       BigDecimal targetOutputPower) {
    DevicePortsReader.OpticalPortBuilder builder = DevicePortsReader.OpticalPortBuilder.builder();
    OpticalConfiguration opticalConfiguration = new OpticalConfiguration(List.of(new OpticalChannel.FrequencySlot(centerFrequency, slotWidth)), targetOutputPower);

    builder.setNetworkResourceLocator(networkResourceLocator);
    builder.setLabel(label);
    builder.setPayload(payload);
    builder.setAdminState(adminState);
    builder.setOperationalState(operationalState);
    builder.setOpticalConfiguration(opticalConfiguration);

    return builder.build();
  }
}