/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.rest.server;

import com.adva.nlms.opticalrouter.api.commands.CommunicationSettings;
import com.adva.nlms.opticalrouter.api.commands.OpticalRouterCommandException;
import com.adva.nlms.opticalrouter.api.commands.OpticalRouterDeviceDriver;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@Validated
@RestController
@RequestMapping(path = "${app.driver.uri}")
public class OpticalRouterDriverController {

  private final OpticalRouterDeviceDriver opticalRouterDeviceDriver;

  public OpticalRouterDriverController(OpticalRouterDeviceDriver opticalRouterDeviceDriver) {
    this.opticalRouterDeviceDriver = opticalRouterDeviceDriver;
  }

  @PostMapping("/{neId}/connect")
  @RolesAllowed({"ModNet"})
  public ResponseEntity<Void> connect(@PathVariable("neId") UUID neId, @RequestBody CommunicationSettings settings) throws OpticalRouterCommandException {
    opticalRouterDeviceDriver.connect(neId, settings);
    return ResponseEntity.ok().build();
  }

  @PostMapping("/{neId}/disconnect")
  @RolesAllowed({"ModNet"})
  public ResponseEntity<Void> disconnect(@PathVariable("neId") UUID neId) throws OpticalRouterCommandException {
    opticalRouterDeviceDriver.disconnect(neId);
    return ResponseEntity.ok().build();
  }

  @GetMapping("/{neId}/is-connected")
  @RolesAllowed({"GetNet"})
  public ResponseEntity<Boolean> isConnected(@PathVariable("neId") UUID neId) throws OpticalRouterCommandException {
    boolean isConnected = opticalRouterDeviceDriver.isConnected(neId);
    return ResponseEntity.ok(isConnected);
  }

  @GetMapping("/{neId}/cold-start-time")
  @RolesAllowed({"GetNet"})
  public ResponseEntity<Long> getColdStartTime(@PathVariable("neId") UUID neId) throws OpticalRouterCommandException {
    long coldStartTime = opticalRouterDeviceDriver.getColdStartTime(neId);
    return ResponseEntity.ok(coldStartTime);
  }

  @PostMapping("/{neId}/synchronize-device-info")
  @RolesAllowed({"ModNet"})
  public ResponseEntity<Void> synchronizeDeviceInfo(@PathVariable("neId") UUID neId) throws OpticalRouterCommandException {
    opticalRouterDeviceDriver.synchronizeDeviceInfo(neId);
    return ResponseEntity.accepted().build();
  }

  @PostMapping("/{neId}/synchronize-inventory")
  @RolesAllowed({"ModNet"})
  public ResponseEntity<Void> synchronizeInventory(@PathVariable("neId") UUID neId) throws OpticalRouterCommandException {
    opticalRouterDeviceDriver.synchronizeInventory(neId);
    return ResponseEntity.accepted().build();
  }

  @PostMapping("/{neId}/synchronize-status")
  @RolesAllowed({"ModNet"})
  public ResponseEntity<Void> synchronizeStatus(@PathVariable("neId") UUID neId) throws OpticalRouterCommandException {
    opticalRouterDeviceDriver.synchronizeStatus(neId);
    return ResponseEntity.accepted().build();
  }

}
