/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.drivers.juniperptx.exception.NetconfException;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

class SemanticVersionParser {
  private static final Pattern BASE_VERSION_PATTERN = Pattern.compile("^(?<year>\\d{2})\\.(?<quarter>\\d)");
  private static final Pattern RELEASE_PATTERN = Pattern.compile("R(?<release>\\d+)");
  private static final Pattern BUILD_PATTERN = Pattern.compile("-\\d{12}\\.\\d+");

  private SemanticVersionParser() {
  }

  static String parseToSemantic(String softwareVersion) {
    if (softwareVersion == null || softwareVersion.isBlank()) {
      throw new NetconfException("Cannot parse null or blank semantic software version");
    }
    softwareVersion = softwareVersion.trim();

    // First, match the base year.quarter pattern
    Matcher baseMatcher = BASE_VERSION_PATTERN.matcher(softwareVersion);
    if (!baseMatcher.find()) {
      throw couldNotParse(softwareVersion);
    }

    try {
      String year = baseMatcher.group("year");
      String quarter = baseMatcher.group("quarter");
      String release;

      // Check for R release pattern
      Matcher releaseMatcher = RELEASE_PATTERN.matcher(softwareVersion);
      if (releaseMatcher.find()) {
        release = releaseMatcher.group("release");
      } else {
        // Check if it has build pattern (alternative to R release)
        Matcher buildMatcher = BUILD_PATTERN.matcher(softwareVersion);
        if (!buildMatcher.find()) {
          throw couldNotParse(softwareVersion);
        }
        release = "0"; // default
      }
      return year + "." + quarter + "." + release;
    } catch (Exception e) {
      throw couldNotParse(softwareVersion);
    }
  }

  private static NetconfException couldNotParse(String softwareVersion) {
    return new NetconfException("Cannot parse semantic software version: " + softwareVersion);
  }
}