/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock;

public enum ResponseTemplate {
  HELLO("hello"),
  GET_SYSTEM_UPTIME_INFORMATION("get-system-uptime-information"),
  GET_SOFTWARE_INFORMATION("get-software-information"),
  GET_SOFTWARE_INFORMATION_INVALID("get-software-information-invalid"),
  GET_CHASSIS_INVENTORY("get-chassis-inventory"),
  GET_INTERFACE_INFORMATION("get-interface-information"),
  GET_TX_POWER("get-tx-power"),
  GET_INTERFACE_OPTICS_APPLICATIONS_DIAGNOSTICS("get-interface-optics-applications-diagnostics-information"),
  GET_PIC_DETAIL("get-pic-detail");

  private final String name;

  ResponseTemplate(String name) {
    this.name = name;
  }

  public String getName() {
    return name;
  }
}
