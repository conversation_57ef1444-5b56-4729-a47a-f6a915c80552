/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.drivers.juniperptx.exception.NetconfException;
import com.tailf.jnc.Element;

import java.util.UUID;

import static java.util.Objects.requireNonNull;

class CheckColdStartTimeCommandProcessor {

  private static final String SYSTEM_BOOT_TIME_PATH = "system-uptime-information/system-booted-time/date-time";

  private final NetconfDataProviderFactory netconfDataProviderFactory;

  CheckColdStartTimeCommandProcessor(NetconfDataProviderFactory netconfDataProviderFactory) {
    this.netconfDataProviderFactory = netconfDataProviderFactory;
  }

  long checkColdStartTime(final UUID neId) {
    requireNonNull(neId);

    NetconfDataProvider netconfDataProvider = netconfDataProviderFactory.newDataProvider(neId);
    Element systemUptimeInformation = netconfDataProvider.getElement(RpcCommands.GET_SYSTEM_UPTIME_INFORMATION);
    String seconds = NetconfReader.getAttrValue(systemUptimeInformation, SYSTEM_BOOT_TIME_PATH, "seconds");
    try {
      return Long.parseLong(seconds);
    } catch (NumberFormatException e) {
      throw new NetconfException("Error while parsing seconds for checking cold start time of node " + neId, e);
    }
  }

}
