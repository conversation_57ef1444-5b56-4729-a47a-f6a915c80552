/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.kafka;

import com.adva.nlms.opticalrouter.api.registration.OpticalRouterDriverRegistration;
import com.adva.nlms.opticalrouter.api.registration.RegistrationMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;

class RegistrationMessageProducer implements OpticalRouterDriverRegistration {
  private static final Logger LOGGER = LoggerFactory.getLogger(RegistrationMessageProducer.class);
  private final KafkaTemplate<String, RegistrationMessage> kafkaTemplate;
  private final String topicName;

  RegistrationMessageProducer(String topicName, KafkaTemplate<String, RegistrationMessage> kafkaTemplate) {
    this.topicName = topicName;
    this.kafkaTemplate = kafkaTemplate;
  }

  @Override
  public void register(RegistrationMessage message) {
    try {
      kafkaTemplate.send(topicName, message)
        .exceptionally(throwable -> {
          LOGGER.error("Failed to send {} to {} topic during message sending", message, topicName, throwable);
          return null;
        })
        .get();
      LOGGER.info("Successfully sent {} to {} topic", message, topicName);
    } catch (InterruptedException e) {
      LOGGER.error("Failed to send {} to {} topic during connection to kafka", message, topicName, e);
      Thread.currentThread().interrupt();
    } catch (Exception e) {
      LOGGER.error("Failed to send {} to {} topic during connection to kafka", message, topicName, e);
    }
  }
}
