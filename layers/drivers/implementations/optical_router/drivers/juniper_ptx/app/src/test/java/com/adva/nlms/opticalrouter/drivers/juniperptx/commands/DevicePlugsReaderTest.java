/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.api.resources.AdminState;
import com.adva.nlms.opticalrouter.api.resources.OperationalState;
import com.adva.nlms.opticalrouter.api.resources.OpticalPlug;
import org.assertj.core.api.ThrowableAssert;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class DevicePlugsReaderTest {

  private static final UUID DEVICE_ID = UUID.fromString("d0baf626-c566-47c8-9ba5-390437d0913a");

  private static final String CHASSIS_INVENTORY_XML_FILE_PATH = "plugs/chassis-inventory/chassis-inventory.xml";
  private static final String INTERFACE_INFORMATION_ET_0_0_0_XML_FILE_PATH = "plugs/interface-information/et-0-0-0.xml";
  private static final String INTERFACE_INFORMATION_ET_0_0_2_XML_FILE_PATH = "plugs/interface-information/et-0-0-2.xml";
  private static final String PIC_DETAIL_ET_0_0_XML_FILE_PATH = "plugs/pic-detail/fpc-information.xml";

  private final NetconfDataProvider netconfDataProvider = mock();

  @Test
  void readPlugsWhenNullSessionPassed() {
    ThrowableAssert.ThrowingCallable actual = () -> DevicePlugsReader.readPlugs(null, null);
    assertThatThrownBy(actual).isInstanceOf(NullPointerException.class);
  }

  @Test
  void readPlugsWhenNullOpticalInterfacesPassed() {
    //when
    List<OpticalPlug> opticalPlugs = DevicePlugsReader.readPlugs(netconfDataProvider, null);

    //then
    assertTrue(opticalPlugs.isEmpty());
  }

  @Test
  void readPlugsWhenEmptyOpticalInterfaces() {
    //when
    List<OpticalPlug> opticalPlugs = DevicePlugsReader.readPlugs(netconfDataProvider, null);

    //then
    assertTrue(opticalPlugs.isEmpty());
  }

  @Test
  void readPlugsWhenEmptyInterfaceInformationResponse() throws Exception {
    //given
    String et003 = "et-0/0/3";
    var opticalInterfaces = Set.of(new InterfaceId(et003, "0", "0", "3"));
    var emptyResponse = JncElementHelper.parseXmlFileToElement(DevicePortsReaderTest.EMPTY_XML_FILE_PATH);
    when(netconfDataProvider.getElement(String.format(RpcCommands.GET_INTERFACE_INFORMATION, et003))).thenReturn(emptyResponse);

    //when
    ThrowableAssert.ThrowingCallable actual = () -> DevicePlugsReader.readPlugs(netconfDataProvider, opticalInterfaces);

    //then
    assertThatThrownBy(actual).isInstanceOf(NullPointerException.class);
  }

  @Test
  void readPlugsHappyPath() throws Exception {
    //given
    String plug000 = "et-0/0/0";
    String plug002 = "et-0/0/2";

    var plug000OpticalPlug = new OpticalPlug("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-0",
      "plug-0/0/0",
      AdminState.UP,
      OperationalState.OUTAGE,
      "QSFP56-DD-400G-ZR-M",
      "Acacia Comm Inc.",
      "DP04QSDD-E30-001",
      "214159987",
      "61.22");

    var plug002OpticalPlug = new OpticalPlug("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-2",
      "plug-0/0/2",
      AdminState.UP,
      OperationalState.NORMAL,
      "QSFP56-DD-400G-ZR-M",
      "Acacia Comm Inc.",
      "DP04QSDD-E30-050",
      "FA52221354846",
      "61.30");

    when(netconfDataProvider.getNeId()).thenReturn(DEVICE_ID);

    var chassisInventoryResponse = JncElementHelper.parseXmlFileToElement(CHASSIS_INVENTORY_XML_FILE_PATH);
    when(netconfDataProvider.getElement(RpcCommands.GET_CHASSIS_INVENTORY)).thenReturn(chassisInventoryResponse);

    var et_0_0_0_interfaceInformation = JncElementHelper.parseXmlFileToElement(INTERFACE_INFORMATION_ET_0_0_0_XML_FILE_PATH);
    var et_0_0_2_interfaceInformation = JncElementHelper.parseXmlFileToElement(INTERFACE_INFORMATION_ET_0_0_2_XML_FILE_PATH);

    when(netconfDataProvider.getElement(String.format(RpcCommands.GET_INTERFACE_INFORMATION, plug000))).thenReturn(et_0_0_0_interfaceInformation);
    when(netconfDataProvider.getElement(String.format(RpcCommands.GET_INTERFACE_INFORMATION, plug002))).thenReturn(et_0_0_2_interfaceInformation);


    var et_0_0_picDetail = JncElementHelper.parseXmlFileToElement(PIC_DETAIL_ET_0_0_XML_FILE_PATH);
    when(netconfDataProvider.getElement(String.format(RpcCommands.GET_PIC_DETAIL, "0", "0"))).thenReturn(et_0_0_picDetail);
    var opticalInterfaces = Set.of(new InterfaceId(plug000, "0", "0", "0"), new InterfaceId(plug002, "0", "0", "2"));

    //when
    var actual = DevicePlugsReader.readPlugs(netconfDataProvider, opticalInterfaces);

    //then
    assertThat(actual).containsExactlyInAnyOrder(plug000OpticalPlug, plug002OpticalPlug);
  }
}