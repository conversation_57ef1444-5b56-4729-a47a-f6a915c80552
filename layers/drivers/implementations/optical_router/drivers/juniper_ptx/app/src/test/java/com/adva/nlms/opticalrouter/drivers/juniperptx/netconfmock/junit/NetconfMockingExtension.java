/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.junit;

import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.NetconfCommand;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.NetconfMockServerManager;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.NetconfServerConstants;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.NetconfSubsystemFactory;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.sshd.common.keyprovider.KeyPairProvider;
import org.apache.sshd.core.CoreModuleProperties;
import org.apache.sshd.server.SshServer;
import org.apache.sshd.server.keyprovider.SimpleGeneratorHostKeyProvider;
import org.junit.jupiter.api.extension.AfterAllCallback;
import org.junit.jupiter.api.extension.AfterEachCallback;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.IOException;
import java.net.BindException;
import java.nio.file.Paths;
import java.util.List;


public class NetconfMockingExtension implements BeforeAllCallback, AfterAllCallback, AfterEachCallback {

  private static final Logger log = LogManager.getLogger(NetconfMockingExtension.class);
  private static final int MAX_RETRIES = 3;

  private SshServer sshd;
  private NetconfMockServerManager manager;

  @Override
  public void beforeAll(ExtensionContext context) throws Exception {

    sshd = SshServer.setUpDefaultServer();
    NetconfServer annotation = getSshdMockServerAnnotation(context);
    sshd.setHost(annotation.ip());
    sshd.setPort(annotation.port());
    sshd.setPasswordAuthenticator(
      (username, password, session) -> username.equals(annotation.username()) && password.equals(annotation.password())
    );

    KeyPairProvider hostKeyProvider = new SimpleGeneratorHostKeyProvider(Paths.get(NetconfServerConstants.DEFAULT_HOST_KEY_PATH));
    sshd.setKeyPairProvider(hostKeyProvider);

    ApplicationContext applicationContext = SpringExtension.getApplicationContext(context);
    manager = applicationContext.getBean(NetconfMockServerManager.class);
    manager.setSshServer(sshd);

    sshd.setSubsystemFactories(List.of(new NetconfSubsystemFactory(manager)));

    for (int i = 0; i < MAX_RETRIES; i++) {
      try {
        sshd.start();
      } catch (BindException e) {
        if (annotation.port() != 0) {
          throw e;
        }
        if (i == MAX_RETRIES - 1) {
          throw new IOException("Max retries reached", e);
        }
        log.warn("Failed to start mock netconf server with address {}:{}, retrying", sshd.getHost(), sshd.getPort());
        sshd.setPort(0);
      }
    }
    log.info("🟢 SSH Server has been started on {}:{} with timeout: {}", sshd.getHost(), sshd.getPort(), CoreModuleProperties.IDLE_TIMEOUT.get(sshd));
    manager.setCurrentHost(sshd.getHost());
    manager.setCurrentPort(sshd.getPort());
  }

  private NetconfServer getSshdMockServerAnnotation(ExtensionContext context) {
    Class<?> testClass = context.getTestClass()
      .orElseThrow(() -> new IllegalStateException("Test class is missing"));

    NetconfServer annotation = testClass.getAnnotation(NetconfServer.class);
    if (annotation == null) {
      throw new IllegalStateException("NetconfServer annotation is missing for test class " + testClass.getName());
    }
    return annotation;
  }

  @Override
  public void afterEach(ExtensionContext context) {
    verifyRequestUsage();
    manager.clear();
  }

  @Override
  public void afterAll(ExtensionContext extensionContext) throws Exception {

    if (sshd != null && !sshd.isClosed()) {
      sshd.stop();
      log.info("🔴 SSH Server has been stopped");
    }
  }

  private void verifyRequestUsage() {
    List<NetconfCommand> multipleRequestedCommands = manager.getNetconfCommands()
      .stream()
      .filter(command -> command.getCounter() > 1)
      .toList();
    if (!multipleRequestedCommands.isEmpty()) {
      throw new IllegalStateException("There are netconf commands which was requested multiple times on the mock server:\n" + String.join(",\n",
        multipleRequestedCommands.toString()));
    }
    List<NetconfCommand> neverRequestedCommands = manager.getNetconfCommands()
      .stream()
      .filter(command -> command.getCounter() == 0)
      .toList();
    if (!neverRequestedCommands.isEmpty()) {
      throw new IllegalStateException("There are netconf commands which was never requested on the mock server:\n" + String.join(",\n",
        neverRequestedCommands.toString()));
    }
  }
}
