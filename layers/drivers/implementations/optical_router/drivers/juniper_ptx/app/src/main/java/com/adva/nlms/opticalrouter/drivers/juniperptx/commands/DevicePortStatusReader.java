/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.nrl.creator.api.NetworkResourceLocator;
import com.adva.nlms.nrl.creator.api.exception.WrongNRLFormatException;
import com.adva.nlms.opticalrouter.api.resources.OperationalState;
import com.adva.nlms.opticalrouter.api.resources.OpticalResourceStatus;
import com.adva.nlms.opticalrouter.drivers.juniperptx.exception.NetconfException;
import com.tailf.jnc.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.adva.nlms.opticalrouter.drivers.juniperptx.commands.DevicePlugsReader.OPERATIONAL_STATE_PATH;

class DevicePortStatusReader {
  private static final Logger LOGGER = LoggerFactory.getLogger(DevicePortStatusReader.class);

  private DevicePortStatusReader() {
  }

  static List<OpticalResourceStatus> readDevicePortStatus(NetconfDataProvider dataProvider, Set<InterfaceId> opticalInterfaces) {
    Objects.requireNonNull(dataProvider);

    if (opticalInterfaces == null || opticalInterfaces.isEmpty()) {
      return List.of();
    }

    return opticalInterfaces.stream()
      .map(interfaceId -> getOpticalPortStatus(dataProvider, interfaceId))
      .toList();
  }

  private static OpticalResourceStatus getOpticalPortStatus(NetconfDataProvider netconfDataProvider, InterfaceId interfaceId) {
    LOGGER.info("Initiating port status retrieval for neId: {}, port: {}", interfaceId.fullName(), netconfDataProvider);
    String nrl;
    try {
      nrl = new NetworkResourceLocator.Builder().neUUID(netconfDataProvider.getNeId())
        .port(interfaceId.toNrlPort())
        .build()
        .toString();
    } catch (WrongNRLFormatException e) {
      throw new NetconfException("Cannot build device NRL for neId=%s and interfaceName=%s".formatted(netconfDataProvider.getNeId(), interfaceId), e);
    }

    Element interfaceInformation = netconfDataProvider.getElement(RpcCommands.GET_INTERFACE_INFORMATION.formatted(interfaceId.fullName()));
    String operationalStateString = NetconfReader.getValue(interfaceInformation, OPERATIONAL_STATE_PATH);
    OperationalState operationalState = EnumMapper.mapOperationalState(operationalStateString);

    LOGGER.info("Finished port status retrieval for neId: {}, port: {}", interfaceId.fullName(), netconfDataProvider);
    return new OpticalResourceStatus(nrl, operationalState);
  }

}
