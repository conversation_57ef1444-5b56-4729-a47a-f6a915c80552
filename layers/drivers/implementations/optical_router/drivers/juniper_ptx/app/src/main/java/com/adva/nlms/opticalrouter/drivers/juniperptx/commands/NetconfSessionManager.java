/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.drivers.juniperptx.exception.NetconfException;
import com.tailf.jnc.Device;
import com.tailf.jnc.IOSubscriber;
import com.tailf.jnc.JNCException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import java.io.IOException;

class NetconfSessionManager {
  private static final Logger log = LoggerFactory.getLogger(NetconfSessionManager.class);

  @Value(value = "${app.netconf.session-name}")
  private String netconfSessionName;

  void openNewSession(Device device, String nodeIdString) {
    final var debugMessageSubscriber = new DebugMessagesSubscriber(nodeIdString);
    try {
      device.newSession(debugMessageSubscriber, netconfSessionName);
      log.debug("Netconf session with name={} has opened", netconfSessionName);
    } catch (IOException | JNCException e) {
      throw new NetconfException("Failed to connect to device " + nodeIdString, e);
    }
  }

  private static class DebugMessagesSubscriber extends IOSubscriber {
    private final String deviceId;

    private DebugMessagesSubscriber(String deviceId) {
      this.deviceId = deviceId;
    }

    @Override
    public void input(String s) {
      log.debug("Device {} received data {}", deviceId, s);
    }

    @Override
    public void output(String s) {
      log.debug("Device {} sending data {}", deviceId, s);
    }
  }

}
