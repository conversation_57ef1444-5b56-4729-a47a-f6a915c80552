/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Configuration
class ExecutorServiceConfiguration {
  @Bean("synchronizeCommandsExecutor")
  ExecutorService synchronizeCommandsExecutor() {
    return Executors.newFixedThreadPool(4);
  }
}
