/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.drivers.juniperptx.exception.NetconfException;
import com.tailf.jnc.Element;
import com.tailf.jnc.JNCException;
import com.tailf.jnc.NodeSet;

import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.Objects.requireNonNull;

class NetconfReader {

  private static final String ELEMENT_READING_ERROR_MSG = "Error while reading path %s from element %s";

  private NetconfReader() {
  }

  static NodeSet getNodeSet(final Element element, String path) {
    Objects.requireNonNull(element);
    Objects.requireNonNull(path);

    try {
      return element.get(path);
    } catch (JNCException e) {
      throw new NetconfException(ELEMENT_READING_ERROR_MSG.formatted(path, element), e);
    }
  }

  static String getValue(Element element) {
    return getValue(element, "");
  }

  static String getValue(Element element, String path) {
    requireNonNull(element);
    requireNonNull(path);

    try {
      return Optional.ofNullable(element.getFirst(path))
        .map(Element::getValue)
        .map(String::valueOf)
        .orElse(null);
    } catch (JNCException e) {
      throw new NetconfException(ELEMENT_READING_ERROR_MSG.formatted(path, element), e);
    }
  }

  static Set<String> getValues(Element element, String path) {
    requireNonNull(element);
    requireNonNull(path);

    try {
      return element.get(path).stream()
        .map(NetconfReader::getValue)
        .collect(Collectors.toSet());
    } catch (JNCException e) {
      throw new NetconfException(ELEMENT_READING_ERROR_MSG.formatted(path, element), e);
    }
  }

  static String getAttrValue(Element element, String path, String attrName) {
    Objects.requireNonNull(element);
    try {
      return element.getFirst(path).getAttrValue(attrName);
    } catch (JNCException e) {
      throw new NetconfException("Error while reading attrValue %s by path %s from element %s".formatted(attrName, path, element), e);
    }
  }

}
