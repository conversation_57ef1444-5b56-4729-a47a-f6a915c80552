/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.api.resources.OperationalState;
import com.adva.nlms.opticalrouter.api.resources.OpticalResourceStatus;
import org.assertj.core.api.ThrowableAssert;
import org.junit.jupiter.api.Test;

import java.util.Set;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class DevicePortStatusReaderTest {

  private static final UUID NE_ID = UUID.fromString("191ccf41-83c1-4fec-be21-250174a537c4");
  private final NetconfDataProvider netconfDataProvider = mock();

  @Test
  void readDevicePortStatusWhenNullDataProviderPassed() {
    //when
    ThrowableAssert.ThrowingCallable actual = () -> DevicePortStatusReader.readDevicePortStatus(null, null);

    //then
    assertThatThrownBy(actual).isInstanceOf(NullPointerException.class);
  }

  @Test
  void readDevicePortStatusWhenNullOpticalInterfacesPassed() {
    //when
    var actual = DevicePortStatusReader.readDevicePortStatus(netconfDataProvider, null);

    //then
    assertThat(actual).isEmpty();
  }

  @Test
  void readDevicePortStatusWhenEmptyOpticalInterfacesPassed() {
    //when
    var actual = DevicePortStatusReader.readDevicePortStatus(netconfDataProvider, Set.of());

    //then
    assertThat(actual).isEmpty();
  }

  @Test
  void readDevicePortStatusWhenNullResponseForGivenInterface() {
    //given
    InterfaceId et001 = new InterfaceId("et-0/0/1", "0", "0", "1");
    var opticalInterfaces = Set.of(et001);
    when(netconfDataProvider.getElement(RpcCommands.GET_INTERFACE_INFORMATION.formatted(et001.fullName()))).thenReturn(null);
    when(netconfDataProvider.getNeId()).thenReturn(NE_ID);
    //when
    ThrowableAssert.ThrowingCallable actual = () -> DevicePortStatusReader.readDevicePortStatus(netconfDataProvider, opticalInterfaces);

    //then
    assertThatThrownBy(actual).isInstanceOf(NullPointerException.class);
  }

  @Test
  void readDevicePortStatusWhenInterfaceHasUnrecognizedOperationalState() throws Exception {
    //given
    InterfaceId et002 = new InterfaceId("et-0/0/2", "0", "0", "2");
    var opticalInterfaces = Set.of(et002);
    var et_0_0_2_interfaceInformation = JncElementHelper.parseXmlFileToElement(DevicePortsReaderTest.INTERFACE_INFORMATION_ET_0_0_2_XML_FILE_PATH);
    et_0_0_2_interfaceInformation.getFirst("interface-information/physical-interface/oper-status")
      .setValue("unrecognized");
    when(netconfDataProvider.getElement(RpcCommands.GET_INTERFACE_INFORMATION.formatted(et002.fullName()))).thenReturn(et_0_0_2_interfaceInformation);
    when(netconfDataProvider.getNeId()).thenReturn(NE_ID);

    //when
    ThrowableAssert.ThrowingCallable actual = () -> DevicePortStatusReader.readDevicePortStatus(netconfDataProvider, opticalInterfaces);

    //then
    assertThatThrownBy(actual).isInstanceOf(IllegalStateException.class);
    System.out.println(NE_ID);
  }

  @Test
  void readDevicePortStatusHappyPath() throws Exception {
    //given
    InterfaceId et002 = new InterfaceId("et-0/0/2", "0", "0", "2");
    var opticalInterfaces = Set.of(et002);
    var et_0_0_2_interfaceInformation = JncElementHelper.parseXmlFileToElement(DevicePortsReaderTest.INTERFACE_INFORMATION_ET_0_0_2_XML_FILE_PATH);
    when(netconfDataProvider.getElement(RpcCommands.GET_INTERFACE_INFORMATION.formatted(et002.fullName()))).thenReturn(et_0_0_2_interfaceInformation);
    when(netconfDataProvider.getNeId()).thenReturn(NE_ID);

    //when
    var actual = DevicePortStatusReader.readDevicePortStatus(netconfDataProvider, opticalInterfaces);

    //then
    assertThat(actual).hasSize(1)
      .first()
      .extracting(OpticalResourceStatus::networkResourceLocator, OpticalResourceStatus::operationalState)
      .containsExactly("/ne=%s///p=%s".formatted(NE_ID, et002.toNrlPort()), OperationalState.OUTAGE);
  }
}