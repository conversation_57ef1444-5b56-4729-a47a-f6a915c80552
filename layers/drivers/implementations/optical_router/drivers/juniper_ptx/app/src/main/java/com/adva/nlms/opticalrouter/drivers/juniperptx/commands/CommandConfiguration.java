/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.api.commands.CommandResponder;
import com.adva.nlms.opticalrouter.api.commands.OpticalRouterDeviceDriver;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ExecutorService;

@Configuration
class CommandConfiguration {

  @Bean
  NetconfDeviceRepository netconfDeviceRepository() {
    return new NetconfDeviceRepositoryImpl();
  }

  @Bean
  ConnectionCommandProcessor connectionCommandProcessor(
    NetconfDeviceRepository repository,
    NetconfSessionManager sessionManager,
    CommandResponder commandResponder
  ) {
    return new ConnectionCommandProcessor(repository, sessionManager, commandResponder);
  }

  @Bean
  CheckColdStartTimeCommandProcessor checkColdStartTimeCommandProcessor(NetconfDataProviderFactory netconfDataProviderFactory) {
    return new CheckColdStartTimeCommandProcessor(netconfDataProviderFactory);
  }

  @Bean
  SynchronizeCommandProcessor synchronizeCommandProcessor(CommandResponder commandResponder,
                                                          NetconfDataProviderFactory netConfDataProviderFactory,
                                                          @Qualifier("synchronizeCommandsExecutor") ExecutorService synchronizeCommandsExecutor) {
    return new SynchronizeCommandProcessor(commandResponder, netConfDataProviderFactory, synchronizeCommandsExecutor);
  }

  @Bean
  OpticalRouterDeviceDriver opticalRouterDeviceDriver(ConnectionCommandProcessor connectionCommandProcessor,
                                                      CheckColdStartTimeCommandProcessor checkColdStartTimeCommandProcessor,
                                                      SynchronizeCommandProcessor synchronizeCommandProcessor) {
    return new JuniperPtxDriver(connectionCommandProcessor, checkColdStartTimeCommandProcessor, synchronizeCommandProcessor);
  }

  @Bean
  NetconfSessionManager netconfSessionManager() {
    return new NetconfSessionManager();
  }

  @Bean
  NetconfDataProviderFactory netconfDataProviderFactory(NetconfDeviceRepository repository) {
    return new NetconfDataProviderFactory(netconfSessionManager(), repository);
  }

}
