/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.adva.nlms.opticalrouter.api.commands.CommandResponder;
import com.adva.nlms.opticalrouter.api.commands.CommandResponse;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class TestCommandResponderConfiguration {
  @Bean
  TestCommandResponder testCommandResponder() {
    return new TestCommandResponder();
  }

  static class TestCommandResponder implements CommandResponder {
    private CommandResponse prevResponse;
    private CommandResponse lastResponse;

    @Override
    public void process(CommandResponse response) {
      this.prevResponse = this.lastResponse;
      this.lastResponse = response;
    }

    CommandResponse getLastResponse() {
      return lastResponse;
    }

    public CommandResponse getPrevResponse() {
      return prevResponse;
    }
  }
}
