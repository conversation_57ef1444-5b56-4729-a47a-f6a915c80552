/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.sshd.server.Environment;
import org.apache.sshd.server.ExitCallback;
import org.apache.sshd.server.channel.ChannelSession;
import org.apache.sshd.server.command.Command;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

public class NetconfSubsystem implements Command, Runnable {
  private static final Logger log = LogManager.getLogger(NetconfSubsystem.class);

  public static final String NETCONF_MESSAGE_ENDING = "]]>]]>";

  private final NetconfMockServerManager manager;

  private InputStream in;
  private OutputStream out;
  private OutputStream err;
  private ExitCallback callback;
  private Thread thread;
  private volatile boolean running = true;
  private boolean keepResponding = true;

  public NetconfSubsystem(NetconfMockServerManager manager) {
    this.manager = manager;
  }

  @Override
  public void setInputStream(InputStream in) {
    this.in = in;
  }

  @Override
  public void setOutputStream(OutputStream out) {
    this.out = out;
  }

  @Override
  public void setErrorStream(OutputStream err) {
    this.err = err;
  }

  @Override
  public void setExitCallback(ExitCallback callback) {
    this.callback = callback;
  }

  @Override
  public void start(ChannelSession channelSession, Environment environment) {
    thread = new Thread(this, "NetconfSubsystem");
    thread.start();
  }

  @Override
  public void destroy(ChannelSession channelSession) {
    running = false;
    thread.interrupt();
  }

  @Override
  public void run() {
    log.info("---------------------------------------------------- NetconfSubsystem started ----------------------------------------------------");
    try (
      BufferedReader reader = new BufferedReader(new InputStreamReader(in, StandardCharsets.UTF_8));
      PrintWriter writer = new PrintWriter(new OutputStreamWriter(out, StandardCharsets.UTF_8), true)
    ) {
      StringBuilder messageBuffer = new StringBuilder();
      int requestTerminationTokenIndex = 0;
      boolean requestTerminated = false;
      while (running && keepResponding) {
        int characterCode = reader.read();
        if (characterCode == -1) {
          log.info("Received EOF from client");
          break;
        }
        char character = (char) characterCode;
        if (character == NETCONF_MESSAGE_ENDING.charAt(requestTerminationTokenIndex)) {
          requestTerminationTokenIndex++;
          if (requestTerminationTokenIndex == NETCONF_MESSAGE_ENDING.length()) {
            requestTerminated = true;
            requestTerminationTokenIndex = 0;
          }
        }
        messageBuffer.append((char) characterCode);

        if (requestTerminated) {
          String request = messageBuffer.toString();
          messageBuffer.setLength(0);
          requestTerminated = false;

          request = request.substring(0, request.length() - NETCONF_MESSAGE_ENDING.length());
          keepResponding = handleSingleRequest(request, writer);
        }
      }
      log.info("---------------------------------------------------- NetconfSubsystem has finished ----------------------------------------------------");
    } catch (IOException | InterruptedException e) {
      e.printStackTrace(new PrintWriter(err, true));
      callback.onExit(-1, "Unexpected error: " + e.getMessage());
      return;
    }
    callback.onExit(0);
  }

  private boolean handleSingleRequest(String request, PrintWriter writer) throws InterruptedException {
    log.debug("Received request:\n{}", request);
    var command = manager.handleRequest(request);
    if (command.isEmpty()) {
      log.warn("Sending no response for request:\n{}", request);
      return true;
    }

    NetconfCommand netconfCommand = command.get();
    netconfCommand.bumpCounter();
    if (netconfCommand.isSendEOFAsResponse()) {
      log.info("Responding with EOF. Leaving the netconf loop");
      return false;
    }
    if (netconfCommand.getHangingTime() != null) {
      log.info("Starting device hanging simulation for {}", netconfCommand.getHangingTime());
      TimeUnit.MILLISECONDS.sleep(netconfCommand.getHangingTime().toMillis());
      log.info("Device hanging simulation for {} has ended", netconfCommand.getHangingTime());
    }
    String responseString = Optional.of(netconfCommand)
      .map(NetconfCommand::getResponse)
      .orElseThrow(() -> new IllegalStateException("No response configured for request " + netconfCommand.getMatchingStrategy() + " and phrase=" + netconfCommand.getPhrase()));
    log.debug("Sending response:\n{}", responseString);
    writer.print(responseString);
    writer.println(NetconfSubsystem.NETCONF_MESSAGE_ENDING);
    writer.flush();
    return true;
  }
}
