/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.commands;

import com.google.common.util.concurrent.MoreExecutors;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;

import java.util.concurrent.ExecutorService;

@TestConfiguration
class TestExecutorServiceConfiguration {
  @Bean("synchronizeCommandsExecutor")
  ExecutorService synchronizeCommandsExecutor() {
    return MoreExecutors.newDirectExecutorService();
  }
}
