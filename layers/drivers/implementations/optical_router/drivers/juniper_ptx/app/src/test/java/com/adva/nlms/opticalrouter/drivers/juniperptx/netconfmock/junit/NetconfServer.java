/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.junit;

import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.NetconfMockServerManager;
import com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock.NetconfServerConstants;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Configures Netconf server parameters.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
@Documented
public @interface NetconfServer {
  String ip() default NetconfServerConstants.DEFAULT_IP;

  /**
   * @return port number to use for Netconf server. If 0, a random port will be used and be available via {@link NetconfMockServerManager#getCurrentPort()}.
   */
  int port() default 0;
  String username() default NetconfServerConstants.DEFAULT_USERNAME;
  String password() default NetconfServerConstants.DEFAULT_PASSWORD;
}
