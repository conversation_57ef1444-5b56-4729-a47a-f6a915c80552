/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON>
 */

package com.adva.nlms.opticalrouter.drivers.juniperptx.netconfmock;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.templatemode.TemplateMode;
import org.thymeleaf.templateresolver.FileTemplateResolver;
import org.thymeleaf.templateresolver.ITemplateResolver;

@Configuration
public class NetconfMockServerConfiguration {
  @Bean
  NetconfMockServerManager netConfMockServerManager(ITemplateEngine templateEngine) {
    return new NetconfMockServerManager(templateEngine);
  }

  @Bean
  public TemplateEngine templateEngine(ITemplateResolver templateResolver) {
    var templateEngine = new TemplateEngine();
    templateEngine.setTemplateResolver(templateResolver);
    return templateEngine;
  }

  @Bean
  public ITemplateResolver thymeleafTemplateResolver() {
    var resolver = new FileTemplateResolver();
    resolver.setTemplateMode(TemplateMode.XML);
    resolver.setCharacterEncoding("UTF-8");
    resolver.setPrefix(NetconfServerConstants.DEFAULT_BASE_TEMPLATES_PATH);
    resolver.setSuffix(NetconfServerConstants.DEFAULT_TEMPLATES_SUFFIX);
    return resolver;
  }
}
