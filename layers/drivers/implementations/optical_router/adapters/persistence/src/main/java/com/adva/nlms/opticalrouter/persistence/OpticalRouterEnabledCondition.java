/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: asowa
 */

package com.adva.nlms.opticalrouter.persistence;

import com.adva.nlms.common.property.FNMPropertyConstants;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

public class OpticalRouterEnabledCondition implements Condition {

  @Override
  public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
    return Boolean.TRUE.equals(FNMPropertyConstants.OPTICAL_ROUTER_ENABLED.getValue());
  }
}
