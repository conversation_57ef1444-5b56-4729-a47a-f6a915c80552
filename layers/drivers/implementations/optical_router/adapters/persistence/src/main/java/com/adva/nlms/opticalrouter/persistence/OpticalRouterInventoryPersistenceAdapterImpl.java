/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: asowa
 */

package com.adva.nlms.opticalrouter.persistence;

import com.adva.nlms.common.InstallationState;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.mediation.common.persistence.MDPersistenceHelper;
import com.adva.nlms.mediation.common.persistence.MDTransactional;
import com.adva.nlms.mediation.common.persistence.model.PersistentObjectHelper;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.opticalrouter.api.resources.OpticalChannel;
import com.adva.nlms.opticalrouter.api.resources.OpticalConfiguration;
import com.adva.nlms.opticalrouter.api.resources.OpticalPlug;
import com.adva.nlms.opticalrouter.api.resources.OpticalPort;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterInventory;
import com.adva.topology.manager.api.in.TopologyNodeApi;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

final class OpticalRouterInventoryPersistenceAdapterImpl implements OpticalRouterInventoryPersistenceAdapter {
  private static final Logger LOGGER = LogManager.getLogger(OpticalRouterInventoryPersistenceAdapterImpl.class);

  private final OpticalRouterPlugDao plugDao;
  private final OpticalRouterPortDao portDao;
  private final TopologyNodeApi topologyNodeApi;

  OpticalRouterInventoryPersistenceAdapterImpl(
    final OpticalRouterPlugDao plugDao,
    final OpticalRouterPortDao portDao,
    final TopologyNodeApi topologyNodeApi) {
    this.plugDao = plugDao;
    this.portDao = portDao;
    this.topologyNodeApi = topologyNodeApi;
  }

  @Override
  public void removeData(UUID deviceId) {
    final var node = topologyNodeApi.getNodeByUUID(deviceId);
    if (node == null) {
      LOGGER.warn("When removing data for router {} cannot find node this id", deviceId);
      return;
    }
    final int neId = node.neId();
    PersistentObjectHelper.destroyAll(portDao.findAllNeEntities(neId));
    PersistentObjectHelper.destroyAll(plugDao.findAllNeEntities(neId));
  }

  @MDTransactional
  @Override
  public void processInventory(final UUID deviceId, final OpticalRouterInventory opticalRouterInventory) {
    final var node = topologyNodeApi.getNodeByUUID(deviceId);
    if (node == null) {
      LOGGER.warn("Cannot find node for device {} when processing inventory", deviceId);
      return;
    }
    final int neId = node.neId();
    final var plugs = processOpticalRouterPlug(neId, opticalRouterInventory.plugs());
    processOpticalRouterPort(neId, opticalRouterInventory.ports(), plugs);
  }

  private void processOpticalRouterPort(final int neId, final List<OpticalPort> ports, List<OpticalRouterPlugDBImpl> plugs) {
    InventoryEntityUpdater.syncEntities(
      neId,
      ports,
      portDao,
      port -> new EntityIndex(EntityIndex.OR_CATEGORY_INDEX.concat(port.label())),
      (port, portDB) -> updateOpticalRouterPortDBImpl(port, portDB, findPlug(plugs, port.networkResourceLocator())),
      port -> {
        final OpticalRouterPortDBImpl portDB = new OpticalRouterPortDBImpl(neId);
        portDB.setId(MDPersistenceHelper.getUniqueIntID());
        portDB.setEntityIndex(new EntityIndex(EntityIndex.OR_CATEGORY_INDEX.concat(port.label())));
        updateOpticalRouterPortDBImpl(port, portDB, findPlug(plugs, port.networkResourceLocator()));
        return portDB;
      }
    );
  }

  private OpticalRouterPlugDBImpl findPlug(List<OpticalRouterPlugDBImpl> plugs, String nrl) {
    return plugs.stream().filter(plug -> plug.getNetworkResourceLocator().equals(nrl)).findFirst().orElse(null);
  }

  private List<OpticalRouterPlugDBImpl> processOpticalRouterPlug(final int neId, final List<OpticalPlug> plugs) {
    return InventoryEntityUpdater.syncEntities(
      neId,
      plugs,
      plugDao,
      plug -> new EntityIndex(EntityIndex.OR_CATEGORY_INDEX.concat(plug.label())),
      this::updateOpticalRouterPlugDBImpl,
      plug -> {
        final OpticalRouterPlugDBImpl plugDB = new OpticalRouterPlugDBImpl(neId);
        plugDB.setId(MDPersistenceHelper.getUniqueIntID());
        plugDB.setEntityIndex(new EntityIndex(EntityIndex.OR_CATEGORY_INDEX.concat(plug.label())));
        updateOpticalRouterPlugDBImpl(plug, plugDB);
        return plugDB;
      }
    );
  }

  private void updateOpticalRouterPlugDBImpl(OpticalPlug plug, OpticalRouterPlugDBImpl plugDB) {
    plugDB.setAidString(plug.label());
    plugDB.setShortDescription(plug.label());
    plugDB.setTypeString(plug.plugType());
    plugDB.setFirmwareRev(plug.firmwareVersion());
    plugDB.setHardwareRev(plug.firmwareVersion());
    plugDB.setSerialNum(plug.serialNumber());
    plugDB.setOriginalSerialNumber(plug.serialNumber());
    plugDB.setVendid(plug.vendor());
    plugDB.setManufacturer(plug.vendor());
    plugDB.setVendorPartNumber(plug.vendorPartNumber());
    plugDB.setNetworkResourceLocator(plug.networkResourceLocator());
    plugDB.setUnifiedAdminState(plug.adminState());
    plugDB.setOperState(StateMapper.mapToOperState(plug.operationalState()));
    plugDB.setAdminState(StateMapper.mapToAdminState(plug.adminState()));
    plugDB.setUnifiedOperationalState(plug.operationalState());
    plugDB.setOperState(StateMapper.mapToOperState(plug.operationalState()));
    plugDB.setInstallState(InstallationState.PRESENT);
    plugDB.setInstallDate(System.currentTimeMillis());
    plugDB.setContainedIn(EntityIndex.ZERO);
  }

  private void updateOpticalRouterPortDBImpl(final OpticalPort port, final OpticalRouterPortDBImpl portDB,
                                             final OpticalRouterPlugDBImpl parent) {
    portDB.setAidString(port.label());
    portDB.setShortDescription(port.label());
    portDB.setNetworkResourceLocator(port.networkResourceLocator());
    portDB.setLayerQualifiers(List.of(LayerQualifier.OTSIMC.toString(), port.payload()));
    portDB.setUnifiedAdminState(port.adminState());
    portDB.setAdminState(StateMapper.mapToAdminState(port.adminState()));
    portDB.setUnifiedOperationalState(port.operationalState());
    portDB.setOperState(StateMapper.mapToOperState(port.operationalState()));
    portDB.setWavelengths(getWavelengths(port));
    portDB.setFrequencySlots(getFrequencySlots(port));
    portDB.setTargetOutputPower(getTargetOutputPower(port));
    if (parent != null) {
      portDB.setContainedIn(parent.getEntityIndex());
      portDB.setParent(parent);
    } else {
      portDB.setContainedIn(EntityIndex.ZERO);
      portDB.clearParent();
    }
  }

  private List<FrequencySlot> getFrequencySlots(final OpticalPort port) {
    return port
      .opticalConfiguration()
      .channels()
      .stream()
      .filter(OpticalChannel.FrequencySlot.class::isInstance)
      .map(OpticalChannel.FrequencySlot.class::cast)
      .map(channel -> new FrequencySlot(channel.centerFrequency(), channel.slotWidth()))
      .toList();
  }

  private List<Wavelength> getWavelengths(final OpticalPort port) {
    return port.opticalConfiguration().channels().stream()
      .filter(OpticalChannel.Wavelength.class::isInstance)
      .map(OpticalChannel.Wavelength.class::cast)
      .map(channel -> new Wavelength(channel.wavelength()))
      .toList();
  }

  private BigDecimal getTargetOutputPower(OpticalPort opticalPort) {
    return Optional.ofNullable(opticalPort.opticalConfiguration())
      .map(OpticalConfiguration::targetOutputPower)
      .orElse(null);
  }
}
