/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.persistence;

import com.adva.nlms.mediation.common.persistence.MDTransactional;
import com.adva.nlms.opticalrouter.api.resources.OpticalResourceStatus;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterStatus;

class OpticalRouterStatusPersistenceAdapterImpl implements OpticalRouterStatusPersistenceAdapter {

  private final OpticalRouterPortDao portDao;
  private final OpticalRouterPlugDao plugDao;

  OpticalRouterStatusPersistenceAdapterImpl(OpticalRouterPortDao portDao, OpticalRouterPlugDao plugDao) {
    this.portDao = portDao;
    this.plugDao = plugDao;
  }

  @MDTransactional
  @Override
  public void processOpticalRouterStatus(final OpticalRouterStatus opticalRouterStatus) {
    for (OpticalResourceStatus opticalResourceStatus : opticalRouterStatus.ports()) {
      portDao.updateOpticalRouterPortOperationalStateByNrl(opticalResourceStatus.networkResourceLocator(), opticalResourceStatus.operationalState());
    }
    
    for (OpticalResourceStatus opticalResourceStatus : opticalRouterStatus.plugs()) {
      plugDao.updateOpticalRouterPlugOperationalStateByNrl(opticalResourceStatus.networkResourceLocator(), opticalResourceStatus.operationalState());
    }
  }
}
