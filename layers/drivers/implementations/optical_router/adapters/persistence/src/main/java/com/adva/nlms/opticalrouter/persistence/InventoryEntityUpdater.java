/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: asowa
 */
package com.adva.nlms.opticalrouter.persistence;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.mediation.config.EntityDBImpl;
import com.adva.nlms.mediation.config.dao.GenericMODao;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

final class InventoryEntityUpdater {

  private InventoryEntityUpdater() {
    throw new UnsupportedOperationException();
  }

  static <API_TYPE, DB_TYPE extends EntityDBImpl> List<DB_TYPE> syncEntities(
    int neId,
    List<API_TYPE> currentEntities,
    GenericMODao<DB_TYPE> genericMODao,
    Function<API_TYPE, EntityIndex> indexExtractor,
    BiConsumer<API_TYPE, DB_TYPE> updater,
    Function<API_TYPE, DB_TYPE> creator) {

    deleteEntities(neId, currentEntities, genericMODao, indexExtractor);
    return updateEntities(neId, currentEntities, genericMODao, indexExtractor, updater, creator);
  }

  static <API_TYPE, DB_TYPE extends EntityDBImpl> void deleteEntities(
    int neId,
    List<API_TYPE> currentEntities,
    GenericMODao<DB_TYPE> genericMODao,
    Function<API_TYPE, EntityIndex> indexExtractor) {
    Set<EntityIndex> currentIndices = currentEntities.stream()
      .map(indexExtractor)
      .collect(Collectors.toSet());

    Map<EntityIndex, DB_TYPE> existingEntities = genericMODao.findAllNeEntities(neId).stream()
      .collect(Collectors.toMap(DB_TYPE::getEntityIndex, e -> e));

    existingEntities.keySet().stream()
      .filter(index -> !currentIndices.contains(index))
      .map(existingEntities::get)
      .forEach(genericMODao::delete);
  }

  static <API_TYPE, DB_TYPE extends EntityDBImpl> List<DB_TYPE> updateEntities(
    int neId,
    List<API_TYPE> currentEntities,
    GenericMODao<DB_TYPE> genericMODao,
    Function<API_TYPE, EntityIndex> indexExtractor,
    BiConsumer<API_TYPE, DB_TYPE> updater,
    Function<API_TYPE, DB_TYPE> creator
  ) {
    List<DB_TYPE> updatedEntities = new ArrayList<>();
    for (API_TYPE apiEntity : currentEntities) {
      EntityIndex index = indexExtractor.apply(apiEntity);
      var dbEntity = genericMODao.findNeEntityByIndex(neId, index).orElse(null);
      if (dbEntity != null) {
        updater.accept(apiEntity, dbEntity);
      } else {
        dbEntity = creator.apply(apiEntity);
        genericMODao.create(dbEntity);
      }
      updatedEntities.add(dbEntity);
    }
    return updatedEntities;
  }
}