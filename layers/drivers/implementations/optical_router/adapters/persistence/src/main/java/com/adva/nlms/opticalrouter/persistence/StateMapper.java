/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: asowa
 */
package com.adva.nlms.opticalrouter.persistence;

import com.adva.nlms.common.snmp.MIBFSP_R7Enums;
import com.adva.nlms.opticalrouter.api.resources.AdminState;
import com.adva.nlms.opticalrouter.api.resources.OperationalState;

final class StateMapper {

  private StateMapper() {
    throw new UnsupportedOperationException();
  }

  //DOWN -> DSBLD, UP -> IS, NA -> UNKNOWN, AUTOMATIC_IN_SERVICE -> AINS, MAINTENANCE -> MT
  static int mapToAdminState(AdminState inputState) {
    return switch (inputState) {
      case NA -> MIBFSP_R7Enums.EntityState.ADMIN_STATE_UNDEFINED;
      case UP -> MIBFSP_R7Enums.EntityState.ADMIN_STATE_IS;
      case AUTOMATIC_IN_SERVICE -> MIBFSP_R7Enums.EntityState.ADMIN_STATE_AINS;
      case MAINTENANCE -> MIBFSP_R7Enums.EntityState.ADMIN_STATE_MT;
      case DOWN -> MIBFSP_R7Enums.EntityState.ADMIN_STATE_DSBLD;
    };
  }

  //NORMAL -> OPER_STATE_NR, OUTAGE -> OPER_STATE_OUT
  static int mapToOperState(OperationalState inputState) {
    return switch (inputState) {
      case OUTAGE -> MIBFSP_R7Enums.EntityState.OPER_STATE_OUT;
      case NORMAL -> MIBFSP_R7Enums.EntityState.OPER_STATE_NR;
    };
  }
}

