/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: asowa
 */
package com.adva.nlms.opticalrouter.persistence;

import com.adva.nlms.opticalrouter.api.resources.OpticalRouterInventory;

import java.util.UUID;

public interface OpticalRouterInventoryPersistenceAdapter {
  void processInventory(UUID deviceId, OpticalRouterInventory opticalRouterInventory);

  void removeData(UUID opticalRouterId);
}
