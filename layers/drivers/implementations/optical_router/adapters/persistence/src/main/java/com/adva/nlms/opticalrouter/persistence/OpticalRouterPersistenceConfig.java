/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: asowa
 */
package com.adva.nlms.opticalrouter.persistence;

import com.adva.topology.manager.api.in.TopologyNodeApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;

@Configuration
@Conditional(OpticalRouterEnabledCondition.class)
class OpticalRouterPersistenceConfig {

  @Bean
  OpticalRouterInventoryPersistenceAdapter opticalRouterInventoryPersistenceAdapter(final OpticalRouterPlugDao plugDao,
                                                                                    final OpticalRouterPortDao portDao,
                                                                                    final TopologyNodeApi topologyNodeApi) {
    return new OpticalRouterInventoryPersistenceAdapterImpl(plugDao, portDao, topologyNodeApi);
  }

  @Bean
  OpticalRouterStatusPersistenceAdapter opticalRouterStatusPersistenceAdapter(final OpticalRouterPortDao portDao,
                                                                              final OpticalRouterPlugDao plugDao) {
    return new OpticalRouterStatusPersistenceAdapterImpl(portDao, plugDao);
  }

  @Bean
  OpticalRouterPlugDao opticalRouterPlugDao() {
    return new OpticalRouterPlugDaoImpl();
  }

  @Bean
  OpticalRouterPortDao opticalRouterPortDao() {
    return new OpticalRouterPortDaoImpl();
  }

}