/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: asowa
 */
package com.adva.nlms.opticalrouter.persistence;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.opticalrouter.api.resources.AdminState;
import com.adva.nlms.opticalrouter.api.resources.OperationalState;
import com.adva.nlms.opticalrouter.api.resources.OpticalChannel;
import com.adva.nlms.opticalrouter.api.resources.OpticalConfiguration;
import com.adva.nlms.opticalrouter.api.resources.OpticalPlug;
import com.adva.nlms.opticalrouter.api.resources.OpticalPort;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterInventory;
import com.adva.topology.manager.api.dto.NodeDto;
import com.adva.topology.manager.api.in.TopologyNodeApi;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


class OpticalRouterInventoryProcessorTest {

  private static final String OR = "OR_CAT^";
  private OpticalRouterPortDao portDao;
  private OpticalRouterPlugDao plugDao;
  private TopologyNodeApi topologyNodeApi;
  private OpticalRouterInventoryPersistenceAdapter processor;
  int neId = 42;
  private final UUID deviceId = UUID.randomUUID();
  private final NodeDto nodeDtoMock = mock(NodeDto.class);

  @BeforeEach
  void setUp() {
    portDao = mock(OpticalRouterPortDao.class);
    plugDao = mock(OpticalRouterPlugDao.class);
    topologyNodeApi = mock(TopologyNodeApi.class);
    processor = new OpticalRouterInventoryPersistenceAdapterImpl(plugDao, portDao, topologyNodeApi);
  }

  @Test
  void testProcessInventory_callsAddingPortProcessing() {
    //given
    when(topologyNodeApi.getNodeByUUID(deviceId)).thenReturn(nodeDtoMock);
    when(nodeDtoMock.neId()).thenReturn(neId);
    String et000 = "et-0/0/0";
    String et002 = "et-0/0/2";
    String et008 = "et-0/0/8";

    var et0008opticalConfiguration = new OpticalConfiguration(List.of(new OpticalChannel.FrequencySlot(new BigDecimal("194.900"), new BigDecimal("37.5")), new OpticalChannel.Wavelength(new BigDecimal("1510"))), new BigDecimal(-10));
    var et0008OpticalPort = new OpticalPort("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-8", et008, "ET400ZR", AdminState.UP, OperationalState.NORMAL, et0008opticalConfiguration);

    var et0002pticalConfiguration = new OpticalConfiguration(List.of(new OpticalChannel.FrequencySlot(new BigDecimal("196.025"), new BigDecimal("75.0")), new OpticalChannel.Wavelength(new BigDecimal("1310"))), new BigDecimal(-11));
    var et0002OpticalPort = new OpticalPort("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-2", et002, "ET400ZR", AdminState.NA, OperationalState.NORMAL, et0002pticalConfiguration);

    var et0000pticalConfiguration = new OpticalConfiguration(List.of(new OpticalChannel.FrequencySlot(new BigDecimal("196.100"), new BigDecimal("75.0"))), new BigDecimal(-11));
    var et0000OpticalPort = new OpticalPort("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-0", et000, "ET400ZR", AdminState.DOWN, OperationalState.OUTAGE, et0000pticalConfiguration);

    OpticalRouterInventory inventory = new OpticalRouterInventory(List.of(et0008OpticalPort, et0002OpticalPort, et0000OpticalPort), List.of());
    ArgumentCaptor<OpticalRouterPortDBImpl> portCaptor = ArgumentCaptor.forClass(OpticalRouterPortDBImpl.class);
    //when
    processor.processInventory(deviceId, inventory);
    //then
    verify(topologyNodeApi).getNodeByUUID(deviceId);
    verify(nodeDtoMock).neId();
    verify(portDao, times(3)).create(portCaptor.capture());
    OpticalRouterPortDBImpl portDB = portCaptor.getAllValues().get(0);
    assertPort(et0008OpticalPort, portDB, null, neId, 2, 1, new BigDecimal("194.900"), new BigDecimal("37.5"), new BigDecimal("1510"));
    portDB = portCaptor.getAllValues().get(1);
    assertPort(et0002OpticalPort, portDB, null, neId, 0, 1, new BigDecimal("196.025"), new BigDecimal("75.0"), new BigDecimal("1310"));
    portDB = portCaptor.getAllValues().get(2);
    assertPort(et0000OpticalPort, portDB, null, neId, 6, 3, new BigDecimal("196.100"), new BigDecimal("75.0"), null);
  }

  @Test
  void testProcessInventory_callsUpdatePortProcessing() {
    //given
    when(topologyNodeApi.getNodeByUUID(deviceId)).thenReturn(nodeDtoMock);
    when(nodeDtoMock.neId()).thenReturn(neId);
    OpticalRouterPortDBImpl opticalRouterPortDBImplMock = mock(OpticalRouterPortDBImpl.class);
    when(portDao.findAllNeEntities(neId)).thenReturn(List.of(opticalRouterPortDBImplMock));
    var entityIndex = new EntityIndex("OR_CAT^et-0/0/2");
    when(portDao.findNeEntityByIndex(neId, entityIndex)).thenReturn(Optional.of(opticalRouterPortDBImplMock));
    when(opticalRouterPortDBImplMock.getEntityIndex()).thenReturn(entityIndex);
    var et0002pticalConfiguration = new OpticalConfiguration(List.of(new OpticalChannel.FrequencySlot(new BigDecimal("196.025"), new BigDecimal("75.0")), new OpticalChannel.Wavelength(new BigDecimal("1310"))), new BigDecimal(-11));
    var et0002OpticalPort = new OpticalPort("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-2", "et-0/0/2", "ET400ZR", AdminState.UP, OperationalState.OUTAGE, et0002pticalConfiguration);
    OpticalRouterInventory inventory = new OpticalRouterInventory(List.of(et0002OpticalPort), List.of());
    //when
    processor.processInventory(deviceId, inventory);
    //then
    verify(portDao, never()).create(any());
    verify(portDao, never()).delete(any(OpticalRouterPortDBImpl.class));
  }

  @Test
  void testProcessInventory_callsDeletePortProcessing() {
    //given
    when(topologyNodeApi.getNodeByUUID(deviceId)).thenReturn(nodeDtoMock);
    when(nodeDtoMock.neId()).thenReturn(neId);
    OpticalRouterPortDBImpl opticalRouterPortDBImplMock = mock(OpticalRouterPortDBImpl.class);
    when(portDao.findAllNeEntities(neId)).thenReturn(List.of(opticalRouterPortDBImplMock));
    when(opticalRouterPortDBImplMock.getEntityIndex()).thenReturn(new EntityIndex("OR_CAT^et-0-0-2"));
    OpticalRouterInventory inventory = new OpticalRouterInventory(List.of(), List.of());
    ArgumentCaptor<OpticalRouterPortDBImpl> portCaptor = ArgumentCaptor.forClass(OpticalRouterPortDBImpl.class);
    //when
    processor.processInventory(deviceId, inventory);
    //then
    verify(topologyNodeApi).getNodeByUUID(deviceId);
    verify(nodeDtoMock).neId();
    verify(portDao, times(1)).delete(portCaptor.capture());
    OpticalRouterPortDBImpl portDB = portCaptor.getAllValues().get(0);
    assertEquals(opticalRouterPortDBImplMock, portDB);
  }


  private void assertPort(
    OpticalPort opticalPort,
    OpticalRouterPortDBImpl portDB,
    OpticalRouterPlugDBImpl plugDB,
    int neId,
    int adminState,
    int operState,
    BigDecimal centerFrequency,
    BigDecimal slotWidth,
    BigDecimal wavelengths
  ) {
    assertNotNull(portDB,
      "Failed to set port: " + opticalPort.label());
    assertEquals(neId, portDB.getNeID(),
      "Failed to set neId for port: " + opticalPort.label());
    assertEquals(new EntityIndex(OR.concat(opticalPort.label())), portDB.getEntityIndex(),
      "Failed to set entityIndex for port: " + opticalPort.label());
    assertEquals(opticalPort.networkResourceLocator(), portDB.getNetworkResourceLocator(),
      "Failed to set nrl for port: " + opticalPort.label());
    assertEquals(opticalPort.label(), portDB.getAidString(),
      "Failed to set aid for port: " + opticalPort.label());
    assertEquals(opticalPort.label(), portDB.getShortDescription(),
      "Failed to set short description for port: " + opticalPort.label());
    assertEquals(2, portDB.getLayerQualifiers().size(),
      "Failed to set layer qualifiers for port: " + opticalPort.label());
    assertEquals(LayerQualifier.OTSIMC.toString(), portDB.getLayerQualifiers().get(0),
      "Failed to set OTSiMC layer for port: " + opticalPort.label());
    assertEquals(opticalPort.payload(), portDB.getLayerQualifiers().get(1),
      "Failed to set payload for port: " + opticalPort.label());
    assertEquals(adminState, portDB.getAdminState(),
      "Failed to set adminState for port: " + opticalPort.label());
    assertEquals(opticalPort.adminState(), portDB.getUnifiedAdminState(),
      "Failed to set unifiedAdminState for port: " + opticalPort.label());
    assertEquals(operState, portDB.getOperState(),
      "Failed to set operState for port: " + opticalPort.label());
    if (plugDB != null) {
      assertEquals(plugDB, portDB.getParent());
      assertEquals(plugDB.getEntityIndex(), portDB.getContainedIn());
    } else {
      assertNull(portDB.getParent());
      assertEquals(EntityIndex.ZERO, portDB.getContainedIn(),
        "Failed to set containedIn for port: " + opticalPort.label());
    }
    assertEquals(opticalPort.opticalConfiguration().targetOutputPower(), portDB.getTargetOutputPower(),
      "Failed to set targetOutputPower for port: " + opticalPort.label());
    assertEquals(1, portDB.getFrequencySlots().size(),
      "Failed to set FrequencySlots for port: " + opticalPort.label());
    var frequencySlot = portDB.getFrequencySlots().get(0);
    assertEquals(centerFrequency, frequencySlot.getCenterFrequency(),
      "Failed to set centerFrequency for port: " + opticalPort.label());
    assertEquals(slotWidth, frequencySlot.getSlotWidth(),
      "Failed to set slotWidth for port: " + opticalPort.label());
    if (wavelengths != null) {
      assertEquals(1, portDB.getWavelengths().size(),
        "Failed to set wavelengths for port: " + opticalPort.label());
      assertEquals(wavelengths, portDB.getWavelengths().get(0).getValue(),
        "Failed to set wavelengths for port: " + opticalPort.label());
    }
  }

  @Test
  void testProcessInventory_callsAddingPlugProcessing() {
    //given
    when(topologyNodeApi.getNodeByUUID(deviceId)).thenReturn(nodeDtoMock);
    when(nodeDtoMock.neId()).thenReturn(neId);
    String pl000 = "et-0/0/0";
    String pl002 = "et-0/0/2";
    String pl008 = "et-0/0/8";
    var opticalPlug000 = new OpticalPlug("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-0", pl000, AdminState.UP, OperationalState.NORMAL, "QSFP56-DD-400G-ZR-M", "Acacia Comm Inc.", "NON-JNPR", "FA52221354846", "61.30");
    var opticalPlug002 = new OpticalPlug("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-2", pl002, AdminState.DOWN, OperationalState.OUTAGE, "QSFP56-DD-400G-FR4", "Acacia Comm Inc.", "NON-JNPR", "DL6C320006", "2.0");
    var opticalPlug008 = new OpticalPlug("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-8", pl008, AdminState.NA, OperationalState.OUTAGE, "QSFP56-DD-400G-FR4", "Acacia Comm Inc.", "NNON-JNPR", "213654026", "61.10");
    OpticalRouterInventory inventory = new OpticalRouterInventory(List.of(), List.of(opticalPlug000, opticalPlug002, opticalPlug008));
    ArgumentCaptor<OpticalRouterPlugDBImpl> portCaptor = ArgumentCaptor.forClass(OpticalRouterPlugDBImpl.class);
    //when
    processor.processInventory(deviceId, inventory);
    //then
    verify(plugDao, times(3)).create(portCaptor.capture());
    OpticalRouterPlugDBImpl plugDB = portCaptor.getAllValues().get(0);
    assertPlug(opticalPlug000, plugDB, neId, 2, 1);
    plugDB = portCaptor.getAllValues().get(1);
    assertPlug(opticalPlug002, plugDB, neId, 6, 3);
    plugDB = portCaptor.getAllValues().get(2);
    assertPlug(opticalPlug008, plugDB, neId, 0, 3);
  }

  @Test
  void testProcessInventory_callsUpdatePlugProcessing() {
    //given
    when(topologyNodeApi.getNodeByUUID(deviceId)).thenReturn(nodeDtoMock);
    when(nodeDtoMock.neId()).thenReturn(neId);
    OpticalRouterPlugDBImpl opticalRouterPlugDBImplMock = mock(OpticalRouterPlugDBImpl.class);
    when(plugDao.findAllNeEntities(neId)).thenReturn(List.of(opticalRouterPlugDBImplMock));
    var entityIndex = new EntityIndex("OR_CAT^et-0/0/2");
    var opticalPlug000 = new OpticalPlug("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-0", "et-0/0/2", AdminState.UP, OperationalState.OUTAGE, "QSFP56-DD-400G-ZR-M", "Acacia Comm Inc.", "NON-JNPR", "FA52221354846", "61.30");
    when(opticalRouterPlugDBImplMock.getEntityIndex()).thenReturn(entityIndex);
    when(plugDao.findNeEntityByIndex(neId, entityIndex)).thenReturn(Optional.of(opticalRouterPlugDBImplMock));
    OpticalRouterInventory inventory = new OpticalRouterInventory(List.of(), List.of(opticalPlug000));
    //when
    processor.processInventory(deviceId, inventory);
    //then
    verify(topologyNodeApi).getNodeByUUID(deviceId);
    verify(nodeDtoMock).neId();
    verify(plugDao, never()).create(any());
    verify(plugDao, never()).delete(any(OpticalRouterPlugDBImpl.class));
  }

  @Test
  void testProcessInventory_callsDeletePlugProcessing() {
    //given
    when(topologyNodeApi.getNodeByUUID(deviceId)).thenReturn(nodeDtoMock);
    when(nodeDtoMock.neId()).thenReturn(neId);
    OpticalRouterPlugDBImpl opticalRouterPlugDBImplMock = mock(OpticalRouterPlugDBImpl.class);
    when(plugDao.findAllNeEntities(neId)).thenReturn(List.of(opticalRouterPlugDBImplMock));
    when(opticalRouterPlugDBImplMock.getEntityIndex()).thenReturn(new EntityIndex("OR_CAT^et-0-0-2"));
    OpticalRouterInventory inventory = new OpticalRouterInventory(List.of(), List.of());
    ArgumentCaptor<OpticalRouterPlugDBImpl> portCaptor = ArgumentCaptor.forClass(OpticalRouterPlugDBImpl.class);
    //when
    processor.processInventory(deviceId, inventory);
    //then
    verify(topologyNodeApi).getNodeByUUID(deviceId);
    verify(nodeDtoMock).neId();
    verify(plugDao, times(1)).delete(portCaptor.capture());
    OpticalRouterPlugDBImpl portDB = portCaptor.getAllValues().get(0);
    assertEquals(opticalRouterPlugDBImplMock, portDB);
  }


  private void assertPlug(
    OpticalPlug opticalPlug,
    OpticalRouterPlugDBImpl plugDB,
    int neId,
    int adminState,
    int operState
  ) {
    assertNotNull(plugDB,
      "Failed to set plug: " + opticalPlug.label());
    assertEquals(neId, plugDB.getNeID(),
      "Failed to set neId for plug: " + opticalPlug.label());
    assertEquals(opticalPlug.networkResourceLocator(), plugDB.getNetworkResourceLocator(),
      "Failed to set nrl for plug: " + opticalPlug.label());
    assertEquals(opticalPlug.label(), plugDB.getAidString(),
      "Failed to set aid for plug: " + opticalPlug.label());
    assertEquals(opticalPlug.label(), plugDB.getShortDescription(),
      "Failed to set short description for plug: " + opticalPlug.label());
    assertEquals(new EntityIndex(OR.concat(opticalPlug.label())), plugDB.getEntityIndex(),
      "Failed to set entityIndex for plug: " + opticalPlug.label());
    assertEquals(adminState, plugDB.getAdminState(),
      "Failed to set adminState for plug: " + opticalPlug.label());
    assertEquals(opticalPlug.adminState(), plugDB.getUnifiedAdminState(),
      "Failed to set unifiedAdminState for plug: " + opticalPlug.label());
    assertEquals(operState, plugDB.getOperState(),
      "Failed to set unifiedAdminState for plug: " + opticalPlug.label());
    assertEquals(opticalPlug.plugType(), plugDB.getTypeString(),
      "Failed to set operState for plug: " + opticalPlug.label());
    assertEquals(opticalPlug.vendor(), plugDB.getVendid(),
      "Failed to set operState for plug: " + opticalPlug.label());
    assertEquals(opticalPlug.vendorPartNumber(), plugDB.getVendorPartNumber(),
      "Failed to set operState for plug: " + opticalPlug.label());
    assertEquals(opticalPlug.serialNumber(), plugDB.getSerialNum(),
      "Failed to set operState for plug: " + opticalPlug.label());
    assertEquals(opticalPlug.firmwareVersion(), plugDB.getFirmwareRev(),
      "Failed to set operState for plug: " + opticalPlug.label());
    assertEquals(EntityIndex.ZERO, plugDB.getContainedIn(),
      "Failed to set containedIn for plug: " + opticalPlug.label());
  }

  @Test
  void testProcessInventory_correctRelationBetweenPortAndPlug() {
    //given
    when(topologyNodeApi.getNodeByUUID(deviceId)).thenReturn(nodeDtoMock);
    when(nodeDtoMock.neId()).thenReturn(neId);
    var opticalPlug000 = new OpticalPlug("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-0", "et-0/0/0", AdminState.UP, OperationalState.NORMAL, "QSFP56-DD-400G-ZR-M", "Acacia Comm Inc.", "NON-JNPR", "FA52221354846", "61.30");
    var et0000pticalConfiguration = new OpticalConfiguration(List.of(new OpticalChannel.FrequencySlot(new BigDecimal("196.100"), new BigDecimal("75.0"))), new BigDecimal(-11));
    var et0000OpticalPort = new OpticalPort("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-0", "et-0/0/0", "ET400ZR", AdminState.DOWN, OperationalState.OUTAGE, et0000pticalConfiguration);

    OpticalRouterInventory inventory = new OpticalRouterInventory(List.of(et0000OpticalPort), List.of(opticalPlug000));
    ArgumentCaptor<OpticalRouterPlugDBImpl> plugCaptor = ArgumentCaptor.forClass(OpticalRouterPlugDBImpl.class);
    ArgumentCaptor<OpticalRouterPortDBImpl> portCaptor = ArgumentCaptor.forClass(OpticalRouterPortDBImpl.class);
    //when
    processor.processInventory(deviceId, inventory);
    //then
    verify(topologyNodeApi).getNodeByUUID(deviceId);
    verify(nodeDtoMock).neId();

    verify(plugDao, times(1)).create(plugCaptor.capture());
    OpticalRouterPlugDBImpl plugDB = plugCaptor.getAllValues().get(0);
    assertPlug(opticalPlug000, plugDB, neId, 2, 1);
    plugDB = plugCaptor.getAllValues().get(0);

    verify(portDao, times(1)).create(portCaptor.capture());
    OpticalRouterPortDBImpl portDB = portCaptor.getAllValues().get(0);
    assertPort(et0000OpticalPort, portDB, plugDB, neId, 6, 3, new BigDecimal("196.100"), new BigDecimal("75.0"), null);
  }

}