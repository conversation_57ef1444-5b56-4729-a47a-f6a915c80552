/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.persistence;

import com.adva.nlms.opticalrouter.api.resources.OperationalState;
import com.adva.nlms.opticalrouter.api.resources.OpticalResourceStatus;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

class OpticalRouterStatusPersistenceAdapterImplTest {

  private OpticalRouterPortDao portDao;
  private OpticalRouterPlugDao plugDao;
  private OpticalRouterStatusPersistenceAdapter adapter;

  @BeforeEach
  void setUp() {
    portDao = mock(OpticalRouterPortDao.class);
    plugDao = mock(OpticalRouterPlugDao.class);
    adapter = new OpticalRouterStatusPersistenceAdapterImpl(portDao, plugDao);
  }

  @Test
  void testProcessOpticalRouterStatus_happyPath() {
    //given
    OpticalResourceStatus opticalPort000 = new OpticalResourceStatus("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-0", OperationalState.OUTAGE);
    OpticalResourceStatus opticalPort002 = new OpticalResourceStatus("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-2", OperationalState.NORMAL);
    OpticalResourceStatus opticalPort008 = new OpticalResourceStatus("/ne=d0baf626-c566-47c8-9ba5-390437d0913a///p=et-0-0-8", OperationalState.OUTAGE);

    var opticalRouterPortStatuses = List.of(opticalPort000, opticalPort002, opticalPort008);

    OpticalRouterStatus opticalRouterStatus = new OpticalRouterStatus(opticalRouterPortStatuses, opticalRouterPortStatuses);

    //when
    adapter.processOpticalRouterStatus(opticalRouterStatus);

    //then
    for (OpticalResourceStatus portStatus : opticalRouterStatus.ports()) {
      verify(portDao).updateOpticalRouterPortOperationalStateByNrl(portStatus.networkResourceLocator(), portStatus.operationalState());
    }

    for (OpticalResourceStatus plugStatus : opticalRouterStatus.plugs()) {
      verify(plugDao).updateOpticalRouterPlugOperationalStateByNrl(plugStatus.networkResourceLocator(), plugStatus.operationalState());
    }
  }

}