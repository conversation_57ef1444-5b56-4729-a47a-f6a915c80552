
/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: asowa
 */
plugins {
    id 'com.adva.gradle.plugin.aspectj-weaver'
}

setupMediationModule(project)

aspectjWeave {
    sourceSets = [ "main" ]

    if (briefOutput) {
        lintLevel = 'ignore'
    }
}

dependencies {
    api modep( mod_inf_api )
    api modep(mod_mo_model_core_api)
    api modep(mod_optical_router_drivers_api)

    implementation modep(mod_property)
    implementation modep(mod_nmscommon)
    implementation modep(mod_persistence_common)
    implementation modep(mod_topology_manager_api)
    implementation modep(mod_optical_router_drivers_api)
    implementation modep(mod_mo_model_opt_optical_router)
    implementation modep(mod_optical_parameters_common)

    implementation libs.jakarta.persistence
    implementation libs.aspectjrt
    implementation libs.spring.context
    implementation libs.log4j.api

    aspectjpath modep(mod_mediation)
    aspectjpath modep(mod_persistence_api)
    aspectjpath modep(mod_optical_router_drivers_api)

    testImplementation libs.bundles.junit
    testImplementation libs.mockito.core
}

test {
    useJUnitPlatform()
}