/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: bka<PERSON>k
 */

plugins {
    id 'com.adva.gradle.plugin.aspectj-weaver'
}

aspectjWeave {
    if (briefOutput) {
        lintLevel = 'ignore'
    }
}

setupMediationModule(project)

dependencies {
    implementation modep(mod_rest_common_lib)
    implementation modep(mod_rest_infra)
    implementation modep(mod_security_api)
    implementation modep(mod_global_security_api)
    implementation modep(mod_property)
    implementation modep(mod_optical_router_drivers_api)
    implementation modep(mod_optical_router_api)

    implementation modep(mod_security_api)
    implementation modep(mod_rest_api)
    implementation modep(mod_nmscommon)

    implementation libs.log4j.api
    implementation libs.spring.context
    implementation libs.spring.web
    implementation libs.jakarta.ws.rs.api
    implementation libs.commons.lang3
    implementation libs.jersey.client

    runtimeOnly modep(mod_security_impl)


    aspectjpath libs.aspectjrt
    aspectjpath modep(mod_security_api)
}
