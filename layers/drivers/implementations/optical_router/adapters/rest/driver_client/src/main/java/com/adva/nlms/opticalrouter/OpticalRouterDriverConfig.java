/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter;

import com.adva.common.model.clusteraccess.ClusterAccessParamsAPI;
import com.adva.nlms.common.property.FNMPropertyConstants;
import com.adva.nlms.mediation.security.api.http.EncHttpCientProvider;
import com.adva.nlms.mediation.security.api.session.SessionHdlrApi;
import com.adva.nlms.opticalrouter.api.commands.OpticalRouterDeviceDriver;
import com.adva.nlms.opticalrouter.api.registry.in.OpticalRouterDriverRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;

@Configuration
@Conditional(OpticalRouterEnabledCondition.class)
public class OpticalRouterDriverConfig {

  @Bean
  OpticalRouterNeDriverRepository opticalRouterNeDriverRepository() {
    return new InMemoryOpticalRouterNeDriverRepository();
  }

  @Bean
  OpticalRouterDriverRestClient opticalRouterDriverRestClient(EncHttpCientProvider encHttpCientProvider, SessionHdlrApi sessionHdlrApi, ClusterAccessParamsAPI clusterAccessParams) {
    int connectTimeout = FNMPropertyConstants.OPTICAL_ROUTER_REST_CLIENT_CONNECT_TIMEOUT.getValue();
    int connectionRequestTimeout = FNMPropertyConstants.OPTICAL_ROUTER_REST_CLIENT_REQUEST_TIMEOUT.getValue();
    return new OpticalRouterDriverRestClient(encHttpCientProvider, sessionHdlrApi, clusterAccessParams, connectTimeout, connectionRequestTimeout);
  }

  @Bean
  OpticalRouterDeviceDriver opticalRouterDeviceDriver(OpticalRouterNeDriverRepository opticalRouterNeDriverRepository,
                                                      OpticalRouterDriverRegistry opticalRouterDriverRegistry,
                                                      OpticalRouterDriverRestClient opticalRouterDriverRestClient) {
    return new OpticalRouterDriverClient(opticalRouterNeDriverRepository, opticalRouterDriverRegistry, opticalRouterDriverRestClient);
  }

}
