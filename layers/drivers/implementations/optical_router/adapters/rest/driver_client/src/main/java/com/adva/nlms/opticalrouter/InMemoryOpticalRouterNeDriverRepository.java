/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class InMemoryOpticalRouterNeDriverRepository implements OpticalRouterNeDriverRepository {

  private final Map<UUID, String> map = new ConcurrentHashMap<>();

  @Override
  public void save(UUID neId, String uri) {
    map.put(neId, uri);
  }

  @Override
  public void delete(UUID neId) {
    map.remove(neId);
  }

  @Override
  public String findUriByNeId(UUID neId) {
    return map.get(neId);
  }
}