/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter;

import com.adva.common.model.clusteraccess.ClusterAccessParamsAPI;
import com.adva.common.model.clusteraccess.ClusterAccessRestParamsDTO;
import com.adva.common.model.clusteraccess.EncServiceName;
import com.adva.nlms.mediation.security.api.http.EncHttpCientProvider;
import com.adva.nlms.mediation.security.api.http.EncHttpClient;
import com.adva.nlms.mediation.security.api.session.ISession;
import com.adva.nlms.mediation.security.api.session.SessionHdlrApi;
import jakarta.ws.rs.client.Client;
import jakarta.ws.rs.client.ClientBuilder;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.MultivaluedHashMap;
import jakarta.ws.rs.core.MultivaluedMap;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.glassfish.jersey.client.ClientProperties;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

class OpticalRouterDriverRestClient {

  private static final Logger LOGGER = LogManager.getLogger(OpticalRouterDriverRestClient.class);
  private static final String SESSION_ID = "sessionID";
  private static final String USER_UUID = "userUUID";

  private final int connectTimeout;
  private final int connectionRequestTimeout;
  private final EncHttpCientProvider encHttpCientProvider;
  private final SessionHdlrApi sessionHdlrApi;
  private final ClusterAccessParamsAPI clusterAccessParamsAPI;
  private final Client restClient;

  OpticalRouterDriverRestClient(EncHttpCientProvider encHttpCientProvider, SessionHdlrApi sessionHdlrApi, ClusterAccessParamsAPI clusterAccessParamsAPI, int connectTimeout, int connectionRequestTimeout) {
    this.connectTimeout = connectTimeout;
    this.connectionRequestTimeout = connectionRequestTimeout;
    this.encHttpCientProvider = encHttpCientProvider;
    this.sessionHdlrApi = sessionHdlrApi;
    this.clusterAccessParamsAPI = clusterAccessParamsAPI;
    this.restClient = createRestClient();
  }

  Client getRestClient() {
    return restClient;
  }

  private Client createRestClient() {
    Client result = null;
    try {
      result = encHttpCientProvider.newFor(
        EncHttpClient.RPROXY,
        ClientBuilder.newBuilder());
      result.property(ClientProperties.CONNECT_TIMEOUT, Duration.ofSeconds(connectTimeout).toMillis());
      result.property(ClientProperties.READ_TIMEOUT, Duration.ofSeconds(connectionRequestTimeout).toMillis());
    } catch (Exception e) {
      LOGGER.error("Failed to create Rest Client {}, {}", e.getMessage(), e.getCause());
    }
    return result;
  }

  MultivaluedMap<String, Object> createHeaders() {
    ClusterAccessRestParamsDTO clusterAccessParams = this.clusterAccessParamsAPI.getAccessParams(EncServiceName.EOD_DRIVER_JUNIPER_PTX);
    MultivaluedMap<String, Object> headers = new MultivaluedHashMap<>();

    Map<String, String> sessionAndUserInfo = getSessionAndUserInfo();

    if (clusterAccessParams.encServiceHeaderKey() != null && !clusterAccessParams.encServiceHeaderKey().isEmpty()) {
      headers.putSingle(clusterAccessParams.encServiceHeaderKey(), clusterAccessParams.encServiceHeaderValue());
    }
    headers.putSingle(SESSION_ID, sessionAndUserInfo.get(SESSION_ID));
    headers.putSingle("Content-Type", MediaType.APPLICATION_JSON_TYPE);
    return headers;
  }

  private Map<String, String> getSessionAndUserInfo() {
    String session = "";
    UUID userUuid = null;

    Map<String, String> userInfo = new HashMap<>();

    ISession iSession = sessionHdlrApi.getSession(sessionHdlrApi.getSessionID());
    if (iSession != null && iSession.getSessionInfo() != null) {
      session = iSession.getSessionInfo().getSessionId();
      userUuid = iSession.getSessionInfo().getUserUuid();
    }

    userInfo.put(SESSION_ID, session);
    userInfo.put(USER_UUID, String.valueOf(userUuid));

    return userInfo;
  }
}
