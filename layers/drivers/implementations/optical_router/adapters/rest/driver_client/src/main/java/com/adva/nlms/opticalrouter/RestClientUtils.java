/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter;

import com.adva.nlms.opticalrouter.api.polling.in.CommandStatus;
import com.adva.nlms.opticalrouter.api.polling.in.OpticalRouterCommandException;
import jakarta.ws.rs.ProcessingException;
import jakarta.ws.rs.client.Invocation;
import jakarta.ws.rs.core.Response;

import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.function.Function;

class RestClientUtils {

  private static final String TIMEOUT_ERROR = "Timeout occurred during rest call to driver uri: %s, error message: %s";
  private static final String DRIVER_ERROR = "Driver error occurred during rest call to driver uri: %s, error message: %s";

  private RestClientUtils() {
    throw new IllegalStateException("Utility class");
  }

  public static void executeRequest(RestRequest restRequest) {
    executeRequest(restRequest, response -> {
      if (!isSuccessful(response)) {
        throw new OpticalRouterCommandException(
          CommandStatus.DRIVER_ERROR,
          DRIVER_ERROR.formatted(restRequest.uri(), responseStatusMessage(response))
        );
      }
      return null;
    });
  }

  public static <T> T executeRequest(Class<T> clazz, RestRequest restRequest) {
    return executeRequest(restRequest, response -> {
      if (isSuccessful(response)) {
        return response.readEntity(clazz);
      } else {
        throw new OpticalRouterCommandException(
          CommandStatus.DRIVER_ERROR,
          DRIVER_ERROR.formatted(restRequest.uri(), responseStatusMessage(response))
        );
      }
    });
  }

  private static <T> T executeRequest(RestRequest restRequest, Function<Response, T> responseHandler) {
    try (Response response = restRequest.invocation.invoke()) {
      return responseHandler.apply(response);
    } catch (ProcessingException e) {
      Throwable cause = e.getCause();
      if (cause instanceof SocketTimeoutException || cause instanceof ConnectException) {
        throw new OpticalRouterCommandException(
          CommandStatus.TIMEOUT,
          String.format(TIMEOUT_ERROR, restRequest.uri(), e.getMessage())
        );
      } else {
        throw new OpticalRouterCommandException(
          CommandStatus.DRIVER_ERROR,
          String.format(DRIVER_ERROR, restRequest.uri(), e.getMessage())
        );
      }
    } catch (OpticalRouterCommandException e) {
      throw e;
    } catch (Exception e) {
      throw new OpticalRouterCommandException(
        CommandStatus.DRIVER_ERROR,
        String.format(DRIVER_ERROR, restRequest.uri(), e.getMessage())
      );
    }
  }

  private static boolean isSuccessful(Response response) {
    return Response.Status.Family.familyOf(response.getStatus()) == Response.Status.Family.SUCCESSFUL;
  }

  private static String responseStatusMessage(Response response) {
    Response.StatusType status = response.getStatusInfo();
    return "Family: %s, status: %d, reason: %s".formatted(status.getFamily().name(), status.getStatusCode(), status.getReasonPhrase());
  }

  record RestRequest(Invocation invocation, String uri) {

  }
}
