/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.kafka.commands;

import com.adva.nlms.opticalrouter.api.commands.CommandResponder;
import com.adva.nlms.opticalrouter.api.commands.CommandResponse;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.kafka.annotation.KafkaListener;

import static com.adva.nlms.opticalrouter.kafka.OpticalRouterKafkaConfiguration.OPTICAL_ROUTER_DRIVER_GROUP;

class OpticalRouterCommandResponsesConsumer {
  public static final String OPTICAL_ROUTER_COMMAND_RESPONSES_TOPIC = "v1.drv.optical-router.command-responses";
  private static final Logger log = LogManager.getLogger(OpticalRouterCommandResponsesConsumer.class);

  private final CommandResponder commandResponder;

  OpticalRouterCommandResponsesConsumer(CommandResponder commandResponder) {
    this.commandResponder = commandResponder;
  }

  @KafkaListener(
    topics = OPTICAL_ROUTER_COMMAND_RESPONSES_TOPIC,
    groupId = OPTICAL_ROUTER_DRIVER_GROUP,
    containerFactory = "kafkaListenerOpticalRouterCommandResponsesFactory"
  )
  void listen(ConsumerRecord<String, CommandResponse> consumerRecord) {
    try {
      commandResponder.process(consumerRecord.value());
    } catch (Exception e) {
      log.error("Error while processing command response from kafka consumer", e);
    }
  }
}
