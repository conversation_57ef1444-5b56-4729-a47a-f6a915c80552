/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.kafka;

import com.adva.nlms.kafka.KafkaUtils;
import org.apache.kafka.clients.consumer.ConsumerConfig;

import java.util.Map;

public class OpticalRouterKafkaConfiguration {
  public static final String OPTICAL_ROUTER_DRIVER_GROUP = "optical-router-driver-group";

  private OpticalRouterKafkaConfiguration() {
  }

  public static Map<String, Object> getConsumerConfiguration() {
    Map<String, Object> properties = KafkaUtils.modeBasedConsumerProperties();
    properties.put(ConsumerConfig.GROUP_ID_CONFIG, OPTICAL_ROUTER_DRIVER_GROUP);
    return properties;
  }

  public static Map<String, Object> getProducerConfiguration() {
    return KafkaUtils.modeBasedProducerProperties();
  }

}
