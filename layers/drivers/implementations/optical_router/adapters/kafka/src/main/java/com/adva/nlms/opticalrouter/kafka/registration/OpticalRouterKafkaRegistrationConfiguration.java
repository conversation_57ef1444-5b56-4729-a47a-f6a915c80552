/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.kafka.registration;

import com.adva.nlms.opticalrouter.api.registration.BeaconMessage;
import com.adva.nlms.opticalrouter.api.registration.OpticalRouterDriverRegistration;
import com.adva.nlms.opticalrouter.api.registration.RegistrationMessage;
import com.adva.nlms.opticalrouter.kafka.OpticalRouterEnabledCondition;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.TopicBuilder;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.serializer.JsonDeserializer;

import static com.adva.nlms.opticalrouter.kafka.OpticalRouterKafkaConfiguration.getConsumerConfiguration;
import static com.adva.nlms.opticalrouter.kafka.OpticalRouterKafkaConfiguration.getProducerConfiguration;

@Configuration
@Conditional(OpticalRouterEnabledCondition.class)
class OpticalRouterKafkaRegistrationConfiguration {

  @Bean
  OpticalRouterRegistrationMessageConsumer opticalRouterRegistrationMessageConsumer(OpticalRouterDriverRegistration registration) {
    return new OpticalRouterRegistrationMessageConsumer(registration);
  }

  @Bean
  OpticalRouterBeaconMessageProducer opticalRouterCommandsProducer() {
    var factory = new DefaultKafkaProducerFactory<String, BeaconMessage>(getProducerConfiguration());
    var template = new KafkaTemplate<>(factory);
    return new OpticalRouterBeaconMessageProducer(template);
  }

  @Bean
  NewTopic opticalRouterDriverRegistrationTopic() {
    return TopicBuilder.name(OpticalRouterRegistrationMessageConsumer.OPTICAL_ROUTER_DRIVER_REGISTRATION_TOPIC)
      .partitions(1)
      .replicas(1)
      .build();
  }

  @Bean
  NewTopic opticalRouterDriverCommandsTopic() {
    return TopicBuilder.name(OpticalRouterBeaconMessageProducer.BEACON_TOPIC_NAME)
      .partitions(1)
      .replicas(1)
      .build();
  }

  @Bean
  ConcurrentKafkaListenerContainerFactory<String, RegistrationMessage>
  kafkaListenerOpticalRouterRegistrationMessageFactory() {
    var consumerFactory = new DefaultKafkaConsumerFactory<>(
      getConsumerConfiguration(),
      new StringDeserializer(),
      new JsonDeserializer<>(RegistrationMessage.class, false)
    );
    var containerFactory = new ConcurrentKafkaListenerContainerFactory<String, RegistrationMessage>();
    containerFactory.setConsumerFactory(consumerFactory);
    return containerFactory;
  }

}