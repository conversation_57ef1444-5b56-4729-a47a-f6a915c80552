/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.kafka.registration;

import com.adva.nlms.opticalrouter.api.registration.BeaconMessage;
import com.adva.nlms.opticalrouter.api.registration.OpticalRouterDriverBeacon;
import org.springframework.kafka.core.KafkaTemplate;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

class OpticalRouterBeaconMessageProducer implements OpticalRouterDriverBeacon {
  private static final Logger LOGGER = LogManager.getLogger(OpticalRouterBeaconMessageProducer.class);
  static final String BEACON_TOPIC_NAME = "v1.drv.optical-router.beacon";
  private final KafkaTemplate<String, BeaconMessage> kafkaTemplate;

  OpticalRouterBeaconMessageProducer(KafkaTemplate<String, BeaconMessage> kafkaTemplate) {
    this.kafkaTemplate = kafkaTemplate;
  }

  @Override
  public void process(BeaconMessage message) {
    try {
      kafkaTemplate.send(BEACON_TOPIC_NAME, message)
        .exceptionally(throwable -> {
          LOGGER.error("Failed to send {} to {} topic during message sending", message, BEACON_TOPIC_NAME, throwable);
          return null;
        })
        .get();
      LOGGER.info("Successfully sent {} to {} topic", message, BEACON_TOPIC_NAME);
    } catch (InterruptedException e) {
      LOGGER.error("Failed to send {} to {} topic during connection to kafka", message, BEACON_TOPIC_NAME, e);
      Thread.currentThread().interrupt();
    } catch (Exception e) {
      LOGGER.error("Failed to send {} to {} topic during connection to kafka", message, BEACON_TOPIC_NAME, e);
    }
  }
}
