/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.kafka.registration;

import com.adva.nlms.opticalrouter.api.registration.OpticalRouterDriverRegistration;
import com.adva.nlms.opticalrouter.api.registration.RegistrationMessage;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;

import static com.adva.nlms.opticalrouter.kafka.OpticalRouterKafkaConfiguration.OPTICAL_ROUTER_DRIVER_GROUP;

class OpticalRouterRegistrationMessageConsumer {
  public static final String OPTICAL_ROUTER_DRIVER_REGISTRATION_TOPIC = "v1.drv.optical-router.registration";
  private final OpticalRouterDriverRegistration registration;

  OpticalRouterRegistrationMessageConsumer(OpticalRouterDriverRegistration registration) {
    this.registration = registration;
  }

  @KafkaListener(
    topics = OPTICAL_ROUTER_DRIVER_REGISTRATION_TOPIC,
    groupId = OPTICAL_ROUTER_DRIVER_GROUP,
    containerFactory = "kafkaListenerOpticalRouterRegistrationMessageFactory"
  )
  void listen(ConsumerRecord<String, RegistrationMessage> consumerRecord) {
    registration.register(consumerRecord.value());
  }
}
