/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.kafka.commands;

import com.adva.nlms.opticalrouter.api.commands.CommandResponder;
import com.adva.nlms.opticalrouter.api.commands.CommandResponse;
import com.adva.nlms.opticalrouter.kafka.OpticalRouterEnabledCondition;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.TopicBuilder;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.support.serializer.JsonDeserializer;

import static com.adva.nlms.opticalrouter.kafka.OpticalRouterKafkaConfiguration.getConsumerConfiguration;

@Configuration
@Conditional(OpticalRouterEnabledCondition.class)
class OpticalRouterKafkaCommandsConfiguration {

  @Bean
  OpticalRouterCommandResponsesConsumer opticalRouterCommandResponsesConsumer(CommandResponder commandResponder) {
    return new OpticalRouterCommandResponsesConsumer(commandResponder);
  }

  @Bean
  NewTopic opticalRouterDriverCommandResponsesTopic() {
    return TopicBuilder.name(OpticalRouterCommandResponsesConsumer.OPTICAL_ROUTER_COMMAND_RESPONSES_TOPIC)
      .partitions(1)
      .replicas(1)
      .build();
  }

  @Bean
  ConcurrentKafkaListenerContainerFactory<String, CommandResponse>
  kafkaListenerOpticalRouterCommandResponsesFactory() {
    var consumerFactory = new DefaultKafkaConsumerFactory<>(
      getConsumerConfiguration(),
      new StringDeserializer(),
      new JsonDeserializer<>(CommandResponse.class, false)
    );
    var containerFactory = new ConcurrentKafkaListenerContainerFactory<String, CommandResponse>();
    containerFactory.setConsumerFactory(consumerFactory);
    return containerFactory;
  }

}