/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: <PERSON><PERSON><PERSON>
 */

setupMediationModule(project)

dependencies {
    implementation modep(mod_optical_router_api)
    implementation modep(mod_optical_router_drivers_api)
    implementation modep(mod_optical_router_persistence)
    implementation modep(mod_property)
    implementation modep(mod_support_server_state_api)

    implementation libs.commons.lang3
    implementation libs.log4j.api
    implementation libs.spring.context
    implementation libs.spring.kafka
    implementation libs.jackson.databind

    testImplementation modep(mod_nrl_creator)
    testImplementation libs.bundles.junit.jupiter
    testImplementation libs.mockito.jupiter

}

test {
    useJUnitPlatform()
}
