/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.impl.registry;

import com.adva.nlms.opticalrouter.api.registry.in.DeviceType;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

record OpticalRouterDriverEntry(
  UUID id,
  String uri,
  String name,
  String version,
  List<DeviceType> deviceTypes,
  Instant firstRegistrationTime,
  Instant lastRegistrationTime
) {
}
