/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: aleksandrao
 */
package com.adva.nlms.opticalrouter.impl.notification;

import com.adva.nlms.opticalrouter.api.notifications.DeviceDisconnectedEvent;
import com.adva.nlms.opticalrouter.api.notifications.RemoteOperationCompletitionEvent;
import com.adva.nlms.opticalrouter.api.notifications.RemoteOperationType;
import com.adva.nlms.opticalrouter.api.polling.in.CommandStatus;
import com.adva.nlms.opticalrouter.api.polling.in.OpticalRouterCommandException;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterDeviceInfo;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterDriverManagerNotifications;
import com.adva.nlms.opticalrouter.api.polling.out.SynchronizationCommandResult;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.UUID;
@Component
public class OpticalRouterDriverManagerNotificationsImpl implements OpticalRouterDriverManagerNotifications {


  private final ApplicationEventPublisher applicationEventPublisher;

  public OpticalRouterDriverManagerNotificationsImpl(ApplicationEventPublisher applicationEventPublisher) {
    this.applicationEventPublisher = applicationEventPublisher;
  }

  @Override
  public void deviceDisconnected(UUID neId) {
    applicationEventPublisher.publishEvent(new DeviceDisconnectedEvent(neId));
  }

  @Override
  public void deviceInfoReceived(OpticalRouterDeviceInfo deviceInfo) {
    publishDeviceInfo(deviceInfo);
  }

  @Override
  public void synchronizationOfDeviceInfoCompleted(SynchronizationCommandResult result) {
    // Handle device info synchronization completion
    // Left empty as per current implementation requirements
  }

  @Override
  public void synchronizationOfInventoryCompleted(SynchronizationCommandResult result) {
    publishRemoteOperationCompletedEvent(RemoteOperationType.INVENTORY_SYNCHRONIZATION, result);
  }

  @Override
  public void synchronizationOfStatusCompleted(SynchronizationCommandResult result) {
    publishRemoteOperationCompletedEvent(RemoteOperationType.STATUS_SYNCHRONIZATION, result);
  }

  /**
   * Publishes an InventorySynchronizationEvent based on the synchronization result.
   *
   * @param result The synchronization command result
   */
  private void publishRemoteOperationCompletedEvent(RemoteOperationType remoteOperationType, SynchronizationCommandResult result) {
    Exception syncException = null;

    if (result.status() != CommandStatus.SUCCESS) {
      String errorMessage = String.format("Remote %s failed with status: %s, error: %s",
              remoteOperationType,
              result.status(),
              result.message());
      syncException = new OpticalRouterCommandException(result.status(), errorMessage);
    }

    applicationEventPublisher.publishEvent(new RemoteOperationCompletitionEvent(remoteOperationType, result.neId(),
            syncException));
  }

  private void publishDeviceInfo(OpticalRouterDeviceInfo deviceInfo){
    applicationEventPublisher.publishEvent(deviceInfo);
  }

}
