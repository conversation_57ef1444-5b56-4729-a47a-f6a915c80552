/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.impl.registry;

import com.adva.nlms.opticalrouter.api.registry.in.DeviceType;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

class InMemoryOpticalRouterDriverRegistryRepository implements OpticalRouterDriverRegistryRepository {
  private final Map<UUID, OpticalRouterDriverEntry> map = new ConcurrentHashMap<>();

  @Override
  public List<OpticalRouterDriverEntry> findAll() {
    return new ArrayList<>(map.values());
  }

  @Override
  public void save(OpticalRouterDriverEntry driver) {
    map.put(driver.id(), driver);
  }

  @Override
  public void delete(UUID id) {
    map.remove(id);
  }

  @Override
  public Optional<OpticalRouterDriverEntry> findDriverContainingAnyOfDeviceTypesName(List<String> deviceTypes) {
    return map.values().stream()
      .filter(v -> v.deviceTypes().stream().map(DeviceType::name).anyMatch(deviceTypes::contains))
      .findFirst();
  }

  @Override
  public Optional<OpticalRouterDriverEntry> findDriverByName(String name) {
    return map.values().stream()
      .filter(v -> Objects.equals(v.name(), name))
      .findFirst();
  }

  @Override
  public Optional<OpticalRouterDriverEntry> findDriverByUri(String uri) {
    return map.values().stream()
      .filter(v -> Objects.equals(v.uri(), uri))
      .findFirst();
  }
}
