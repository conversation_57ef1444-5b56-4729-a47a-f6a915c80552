/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.impl.registry;

import com.adva.nlms.mediation.server.state.api.ServerState;
import com.adva.nlms.mediation.server.state.api.ServerStateAPI;
import com.adva.nlms.opticalrouter.api.registration.BeaconMessage;
import com.adva.nlms.opticalrouter.api.registration.OpticalRouterDriverBeacon;
import com.adva.nlms.opticalrouter.api.registration.OpticalRouterDriverRegistration;
import com.adva.nlms.opticalrouter.api.registry.in.OpticalRouterDriverRegistry;
import com.adva.nlms.opticalrouter.impl.OpticalRouterEnabledCondition;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;

@Configuration
@Conditional(OpticalRouterEnabledCondition.class)
class OpticalRouterDriverRegistryConfiguration {
  @Bean
  OpticalRouterDriverRegistration opticalRouterDriverRegistration(
    OpticalRouterDriverRegistryRepository repository,
    OpticalRouterDriverBeacon beacon,
    ServerStateAPI serverStateAPI
  ) {
    serverStateAPI.executeAfter(ServerState.RUNNING,
      () -> new Thread(() -> beacon.process(new BeaconMessage())).start());
    return new OpticalRouterDriverRegistrationImpl(repository);
  }

  @Bean
  OpticalRouterDriverRegistry opticalRouterDriverRegistry(OpticalRouterDriverRegistryRepository repository) {
    return new OpticalRouterDriverRegistryImpl(repository);
  }

  @Bean
  OpticalRouterDriverRegistryRepository opticalRouterDriverRegistryRepository() {
    return new InMemoryOpticalRouterDriverRegistryRepository();
  }

}
