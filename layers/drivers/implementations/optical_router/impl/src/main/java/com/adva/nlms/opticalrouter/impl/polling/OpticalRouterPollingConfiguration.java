/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.impl.polling;

import com.adva.nlms.opticalrouter.api.commands.OpticalRouterDeviceDriver;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterDriverManagerNotifications;
import com.adva.nlms.opticalrouter.impl.OpticalRouterEnabledCondition;
import com.adva.nlms.opticalrouter.impl.notification.OpticalRouterDriverManagerNotificationsImpl;
import com.adva.nlms.opticalrouter.persistence.OpticalRouterInventoryPersistenceAdapter;
import com.adva.nlms.opticalrouter.persistence.OpticalRouterStatusPersistenceAdapter;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;

@Configuration
@Conditional(OpticalRouterEnabledCondition.class)
class OpticalRouterPollingConfiguration {
  @Bean
  OpticalRouterDriverManagerImpl opticalRouterDriversManager(
    OpticalRouterDeviceDriver opticalRouterDeviceDriver,
    OpticalRouterInventoryPersistenceAdapter persistenceAdapter) {
    return new OpticalRouterDriverManagerImpl(opticalRouterDeviceDriver, persistenceAdapter);
  }
  @Bean
  OpticalRouterDriverManagerNotificationsImpl opticalRouterDriverManagerNotifications(ApplicationEventPublisher applicationEventPublisher){
    return new OpticalRouterDriverManagerNotificationsImpl(applicationEventPublisher);
  }

  @Bean
  OpticalRouterSynchronizeProcessor opticalRouterSynchronizeProcessor(OpticalRouterDriverManagerNotifications opticalRouterDriverManagerNotifications,
                                                                      OpticalRouterInventoryPersistenceAdapter opticalRouterInventoryPersistenceAdapter,
                                                                      OpticalRouterStatusPersistenceAdapter opticalRouterStatusPersistenceAdapter) {
    return new OpticalRouterSynchronizeProcessor(
      opticalRouterDriverManagerNotifications,
      opticalRouterInventoryPersistenceAdapter,
      opticalRouterStatusPersistenceAdapter
    );
  }
}
