/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.impl.registry;

import com.adva.nlms.opticalrouter.api.registration.AuthenticationMethod;
import com.adva.nlms.opticalrouter.api.registration.DeviceType;
import com.adva.nlms.opticalrouter.api.registration.OpticalRouterDriverRegistration;
import com.adva.nlms.opticalrouter.api.registration.RegistrationMessage;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

class OpticalRouterDriverRegistrationImpl implements OpticalRouterDriverRegistration {
  private static final Logger LOGGER = LogManager.getLogger(OpticalRouterDriverRegistrationImpl.class);
  private final OpticalRouterDriverRegistryRepository repository;

  OpticalRouterDriverRegistrationImpl(OpticalRouterDriverRegistryRepository repository) {
    this.repository = repository;
  }

  @Override
  public void register(RegistrationMessage message) {
    LOGGER.info("Processing registration for {}", message);
    var matchingDriver = repository.findDriverByUri(message.uri());
    if (matchingDriver.isPresent()) {
      LOGGER.info("Updating OpticalRouterDriver {}", matchingDriver.get());
      var updatedEntry = updateDriver(matchingDriver.get(), message);
      repository.save(updatedEntry);
      return;
    }

    var deviceTypesNames = message.manifest().deviceTypes().stream().map(DeviceType::name).toList();
    var conflictingDriver = repository.findDriverContainingAnyOfDeviceTypesName(deviceTypesNames);
    if (conflictingDriver.isPresent()) {
      LOGGER.error("OpticalRouterDriver {} is already registered with overlapping device types", conflictingDriver.get());
      return;
    }

    var newEntry = buildNewDriverEntry(message);
    repository.save(newEntry);
    LOGGER.info("New driver registered {}", newEntry);
  }

  private static OpticalRouterDriverEntry updateDriver(OpticalRouterDriverEntry driver, RegistrationMessage message) {
    return new OpticalRouterDriverEntry(
      driver.id(),
      message.uri(),
      driver.name(),
      message.manifest().version(),
      mapDeviceTypes(message.manifest().deviceTypes()),
      driver.firstRegistrationTime(),
      Instant.now()
    );
  }

  private static OpticalRouterDriverEntry buildNewDriverEntry(RegistrationMessage message) {
    var id = UUID.randomUUID();
    var now = Instant.now();
    return new OpticalRouterDriverEntry(
      id,
      message.uri(),
      message.manifest().name(),
      message.manifest().version(),
      mapDeviceTypes(message.manifest().deviceTypes()),
      now,
      now);
  }

  private static List<com.adva.nlms.opticalrouter.api.registry.in.DeviceType> mapDeviceTypes(List<DeviceType> deviceTypes) {
    return deviceTypes.stream()
      .map(d -> new com.adva.nlms.opticalrouter.api.registry.in.DeviceType(
        d.name(),
        d.minimumVersion(),
        d.typeId(),
        d.authenticationMethods().stream().map(OpticalRouterDriverRegistrationImpl::mapAuthenticationMethod).toList()
      ))
      .toList();
  }

  private static com.adva.nlms.opticalrouter.api.registry.in.AuthenticationMethod mapAuthenticationMethod(AuthenticationMethod authenticationMethod) {
    return switch (authenticationMethod) {
      case PAP -> com.adva.nlms.opticalrouter.api.registry.in.AuthenticationMethod.PAP;
    };
  }
}
