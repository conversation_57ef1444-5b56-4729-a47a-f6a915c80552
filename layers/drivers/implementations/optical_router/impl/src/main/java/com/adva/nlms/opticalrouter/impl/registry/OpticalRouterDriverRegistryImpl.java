/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.impl.registry;

import com.adva.nlms.opticalrouter.api.registry.in.OpticalRouterDriver;
import com.adva.nlms.opticalrouter.api.registry.in.OpticalRouterDriverRegistry;
import com.adva.nlms.opticalrouter.api.registry.in.OpticalRouterDrivers;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

class OpticalRouterDriverRegistryImpl implements OpticalRouterDriverRegistry {
  private static final Logger LOGGER = LogManager.getLogger(OpticalRouterDriverRegistryImpl.class);
  private final OpticalRouterDriverRegistryRepository repository;

  OpticalRouterDriverRegistryImpl(OpticalRouterDriverRegistryRepository repository) {
    this.repository = repository;
  }

  @Override
  public OpticalRouterDrivers getDrivers() {
    LOGGER.info("Getting drivers");
    var drivers = repository.findAll();
    var dtos = drivers.stream()
      .map(OpticalRouterDriverRegistryImpl::map)
      .toList();
    return new OpticalRouterDrivers(dtos);
  }

  @Override
  public OpticalRouterDriver getDriver(String deviceType) {
    LOGGER.info("Getting driver for device type: {}", deviceType);
    var driver = repository.findDriverContainingAnyOfDeviceTypesName(List.of(deviceType));
    return driver.map(OpticalRouterDriverRegistryImpl::map).orElse(null);
  }

  private static OpticalRouterDriver map(OpticalRouterDriverEntry e) {
    return new OpticalRouterDriver(
      e.id(),
      e.uri(),
      e.name(),
      e.version(),
      e.deviceTypes(),
      e.firstRegistrationTime().toString(),
      e.lastRegistrationTime().toString()
    );
  }

}
