/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.impl.registry;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

interface OpticalRouterDriverRegistryRepository {
  List<OpticalRouterDriverEntry> findAll();
  void save(OpticalRouterDriverEntry driver);
  void delete(UUID id);
  Optional<OpticalRouterDriverEntry> findDriverContainingAnyOfDeviceTypesName(List<String> deviceTypes);
  Optional<OpticalRouterDriverEntry> findDriverByName(String name);
  Optional<OpticalRouterDriverEntry> findDriverByUri(String uri);
}
