/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.opticalrouter.impl.polling;

import com.adva.nlms.opticalrouter.api.commands.CommandResponse;
import com.adva.nlms.opticalrouter.api.commands.Status;
import com.adva.nlms.opticalrouter.api.polling.in.CommandStatus;
import com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterDriverManagerNotifications;
import com.adva.nlms.opticalrouter.api.polling.out.SynchronizationCommandResult;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterDeviceInfo;
import com.adva.nlms.opticalrouter.api.resources.OpticalRouterSynchronizationData;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class OpticalRouterSynchronizeProcessorTest {

  private static final UUID DEVICE_ID = UUID.fromString("d0baf626-c566-47c8-9ba5-390437d0913a");

  @Mock
  OpticalRouterDriverManagerNotifications opticalRouterDriverManagerNotifications;

  @InjectMocks
  OpticalRouterSynchronizeProcessor opticalRouterSynchronizeProcessor;

  @Test
  void invokeProcessOpticalRouterDeviceInfo_statusFailure() {
    //given
    var opticalRouterDeviceInfo = buildResourcesOpticalRouterDeviceInfo();
    var commandResponse = buildCommandResponse(Status.FAILURE, opticalRouterDeviceInfo);

    var synchronizationCommandResult = buildSynchronizationCommandResult(CommandStatus.DRIVER_ERROR);

    //when
    opticalRouterSynchronizeProcessor.process(commandResponse);

    //then
    verify(opticalRouterDriverManagerNotifications, times(0)).deviceInfoReceived(any());
    verify(opticalRouterDriverManagerNotifications, times(1)).synchronizationOfDeviceInfoCompleted(synchronizationCommandResult);
  }

  @Test
  void invokeProcessOpticalRouterDeviceInfo_statusTimeout() {
    //given
    var opticalRouterDeviceInfo = buildResourcesOpticalRouterDeviceInfo();
    var commandResponse = buildCommandResponse(Status.TIMEOUT, opticalRouterDeviceInfo);

    var synchronizationCommandResult = buildSynchronizationCommandResult(CommandStatus.TIMEOUT);

    //when
    opticalRouterSynchronizeProcessor.process(commandResponse);

    //then
    verify(opticalRouterDriverManagerNotifications, times(0)).deviceInfoReceived(any());
    verify(opticalRouterDriverManagerNotifications, times(1)).synchronizationOfDeviceInfoCompleted(synchronizationCommandResult);
  }

  @Test
  void invokeProcessOpticalRouterDeviceInfo_statusUnsupportedDevice() {
    //given
    var opticalRouterDeviceInfo = buildResourcesOpticalRouterDeviceInfo();
    var commandResponse = buildCommandResponse(Status.UNSUPPORTED_DEVICE, opticalRouterDeviceInfo);

    var synchronizationCommandResult = buildSynchronizationCommandResult(CommandStatus.UNSUPPORTED_DEVICE);

    //when
    opticalRouterSynchronizeProcessor.process(commandResponse);

    //then
    verify(opticalRouterDriverManagerNotifications, times(0)).deviceInfoReceived(any());
    verify(opticalRouterDriverManagerNotifications, times(1)).synchronizationOfDeviceInfoCompleted(synchronizationCommandResult);
  }

  @Test
  void invokeProcessOpticalRouterDeviceInfo_nullSynchronizationData() {
    //given
    var commandResponse = buildCommandResponse(Status.SUCCESS, null);

    //when
    opticalRouterSynchronizeProcessor.process(commandResponse);

    //then
    verify(opticalRouterDriverManagerNotifications, times(0)).deviceInfoReceived(any());
    verify(opticalRouterDriverManagerNotifications, times(0)).synchronizationOfDeviceInfoCompleted(any());
  }

  @Test
  void invokeProcessOpticalRouterDeviceInfo_happyPath() {
    //given
    var opticalRouterDeviceInfo = buildResourcesOpticalRouterDeviceInfo();
    var commandResponse = buildCommandResponse(Status.SUCCESS, opticalRouterDeviceInfo);

    var pollingOpticalRouterDeviceInfo = buildPollingOpticalRouterDeviceInfo();
    var synchronizationCommandResult = buildSynchronizationCommandResult(CommandStatus.SUCCESS);

    //when
    opticalRouterSynchronizeProcessor.process(commandResponse);

    //then
    verify(opticalRouterDriverManagerNotifications, times(1)).deviceInfoReceived(pollingOpticalRouterDeviceInfo);
    verify(opticalRouterDriverManagerNotifications, times(1)).synchronizationOfDeviceInfoCompleted(synchronizationCommandResult);
  }

  private SynchronizationCommandResult buildSynchronizationCommandResult(CommandStatus commandStatus) {
    return new SynchronizationCommandResult(DEVICE_ID, commandStatus, "");
  }

  private com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterDeviceInfo buildPollingOpticalRouterDeviceInfo() {
    return new com.adva.nlms.opticalrouter.api.polling.out.OpticalRouterDeviceInfo(DEVICE_ID, "", "", "", "", "");
  }

  private OpticalRouterDeviceInfo buildResourcesOpticalRouterDeviceInfo() {
    return new OpticalRouterDeviceInfo("", "", "", "", "");
  }

  private CommandResponse buildCommandResponse(Status status, OpticalRouterSynchronizationData opticalRouterSynchronizationData) {
    return new CommandResponse(DEVICE_ID, status, "", opticalRouterSynchronizationData);
  }

}