/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.api.resources;

import com.adva.nlms.nrl.creator.api.NetworkResourceLocator;
import com.adva.nlms.nrl.creator.api.exception.WrongNRLFormatException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;

class SerializationTest {
  @Test
  void testSerializePort() throws JsonProcessingException, WrongNRLFormatException {
    final var neId = UUID.randomUUID();
    final var nrl = new NetworkResourceLocator.Builder()
      .neUUID(neId)
      .port("0-0-0-1")
      .build()
      .toString();
    final var channel = new OpticalChannel.FrequencySlot(BigDecimal.valueOf(193.100), BigDecimal.valueOf(62.5));
    final var opticalConfiguration = new OpticalConfiguration(List.of(channel), BigDecimal.valueOf(1.2));
    final var port = new OpticalPort(nrl, "0/0/0/1", "ET400ZR", AdminState.UP, OperationalState.NORMAL, opticalConfiguration);
    final var mapper = new ObjectMapper();
    final var portStr = mapper.writeValueAsString(port);
    final var portObj = mapper.readValue(portStr, OpticalPort.class);
    assertEquals(port, portObj);
  }
}
