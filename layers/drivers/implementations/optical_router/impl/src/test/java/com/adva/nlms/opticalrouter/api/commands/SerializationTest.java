/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.api.commands;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

class SerializationTest {
  @Test
  void testSerializeRequest() throws JsonProcessingException {
    var request = new CommunicationSettings("juniper", "*******", 830, 30000,
      new PasswordAuthenticationData("admin", "chgme.1"));
    ObjectMapper mapper = new ObjectMapper();
    var requestStr = mapper.writeValueAsString(request);
    var requestObj = mapper.readValue(requestStr, CommunicationSettings.class);
    assertEquals(request, requestObj);
  }
}
