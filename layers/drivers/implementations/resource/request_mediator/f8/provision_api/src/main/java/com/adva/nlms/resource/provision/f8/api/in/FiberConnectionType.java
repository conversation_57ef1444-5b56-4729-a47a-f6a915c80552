/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: asowa
 */
package com.adva.nlms.resource.provision.f8.api.in;

public enum FiberConnectionType {
  BIDIRECTIONAL("bi"),
  UNIDIRECTIONAL_TRANSMIT("unitx"),
  UNIDIRECTIONAL_RECEIVE("unirx")
  ;

  final String keyword;

  FiberConnectionType(String keyword) {
    this.keyword = keyword;
  }

  public String getKeyword() {
    return keyword;
  }
}
