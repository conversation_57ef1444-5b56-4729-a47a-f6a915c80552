/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.provision.f8.api.in;

import com.adva.nlms.mediation.config.f8.croma.provision.api.SlcEqualizationDirection;
import com.adva.nlms.mediation.config.f8.croma.provision.api.SlcExpressConfiguration;
import com.adva.nlms.mediation.ec.model.aos.svct.SncTypeIdentity;
import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.enums.AdminState;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Provision resources in the device CIM model
 */
public interface Provision {

  /**
   * Creates CTP object in the device CIM model
   *
   * @param neID       Network Element ID
   * @param newCtpURI  URI of new CTP, include parameters in the path, like layer,
   *                   eg. /mit/me/1/eqh/shelf,2/eqh/slot,3/eq/card/ptp/cl,12/ctp/och
   * @param parameters attributes to be set on new CTP
   * @return ID of missing entity, if one was found, when not full URI was given (ending with '-')
   */
  Optional<Integer> provisionCtp(NetworkElementID neID, String newCtpURI, Set<MoCimParameter> parameters) throws ProvisionException;

  /**
   *
   * @param neID Network Element ID
   * @param ctpURI URI of existing CTP
   * @param parameters parameters to be modified
   * @throws ProvisionException
   */
  void modifyCtp(NetworkElementID neID, String ctpURI, Set<MoCimParameter> parameters) throws ProvisionException;

  /**
   * Deletes CTP from the device CIM model
   *
   * @param neID   Network Element ID
   * @param ctpURI URI of CTP to be deleted
   * @throws ProvisionException when CTP deletion fails on given network element
   */
  void deleteCtp(NetworkElementID neID, String ctpURI) throws ProvisionException, ObjectDoesNotExistException;

  /**
   * Update admin state
   *
   * @param neID       Network Element ID
   * @param uri        URI of entity to be modified
   * @param adminState desired admin state
   */
  void updateAdminState(NetworkElementID neID, String uri, AdminState adminState) throws ProvisionException;

  /**
   * Update admin state
   *
   * @param neID       Network Element ID
   * @param aid        Aid of entity to be modified
   * @param adminState desired admin state
   */
  void updateAdminStateByAid(NetworkElementID neID, String aid, AdminState adminState) throws ProvisionException;

  /**
   * Provisions PTP on NE and scans the newly created entity in case events are not received.
   * @param neId id of network element
   * @param fullPtpUri uri to PTP, i.e. /mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/cl,9-4
   * @param parameters additional parameter list
   * @return uri of newly created PTP, i.e. /mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/cl,9-4
   * @throws ProvisionException when provisioning fails
   */
  String provisionPtp(NetworkElementID neId, String fullPtpUri, Set<MoCimParameter> parameters) throws ProvisionException;

  void deletePtp(NetworkElementID neId, String uri) throws ProvisionException, ObjectDoesNotExistException;

  /**
   * Updates PTP in the device CIM model
   *
   * @param neID       Network Element ID
   * @param ptpURI     URI of modifying PTP
   * @param parameters attributes to be modified
   */
  void updatePtp(NetworkElementID neID, String ptpURI, Set<MoCimParameter> parameters)
    throws ProvisionException;

  /**
   * Creates SNC(cross connect) object in the device CIM model
   *
   * @param neID          Network Element ID
   * @param newSncURI     Base URI for new SNC eg. "/mit/me/1/eqh/shelf,3/eqh/slot,4/eq/card/sn/odu4/snc"
   * @param aEndpointURIs aEndpoint uris
   * @param zEndpointURIs zEndpoint uris
   * @return Created SNC identifier
   */
  Integer provisionSnc(NetworkElementID neID, String newSncURI, Set<String> aEndpointURIs, Set<String> zEndpointURIs, SncTypeIdentity sncType) throws ProvisionException;

  /**
   * Modifies SNC (cross connect) object in the device CIM model
   * @param neID          Network Element ID
   * @param sncURI     Base URI for new SNC eg. "/mit/me/1/eqh/shelf,3/eqh/slot,4/eq/card/sn/odu4/snc"
   * @param aEndpointURIs aEndpoint uris
   * @param zEndpointURIs zEndpoint uris
   * @param sncType SNC type
   * @throws ProvisionException when SNC modification fails
   */
  void modifySNC(NetworkElementID neID, String sncURI, LinkedHashSet<String> aEndpointURIs, LinkedHashSet<String> zEndpointURIs, String sncType) throws ProvisionException;

  /**
   * Deletes SNC from the device CIM model
   *
   * @param neID   Network Element ID
   * @param sncURI URI of SNC to be deleted
   * @throws ProvisionException when SNC deletion fails
   */
  void deleteSnc(NetworkElementID neID, String sncURI) throws ProvisionException, ObjectDoesNotExistException;


  /**
   * Method returns first free CTP identifier from the device
   *
   * @param neID   Network Element ID
   * @param ctpUri URI prefix to find identifier, e.g. "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/nw,1/ctp/otuc2/ctp/oduc2/ctp/odu1-"
   * @return Available CTP identifier
   * @throws ProvisionException
   */
  Optional<Integer> getAvailableCtpEntity(NetworkElementID neID, String ctpUri) throws ProvisionException;

  void removeAlienCtpOtsi(int neId, int shelfNumber, int ptpNumber) throws ProvisionException;

  void removeAlienCtpOtsia(int neId, int shelfNumber, int ptpNumber) throws ProvisionException;

  String provisionAlienCtpOtsia(int neId, int shelfNumber, int ptpNumber, List<ParametersAlienConfigurationDto> parametersAlienConfigurationDtoList) throws ProvisionException;

  String provisionAlienPtp(int neId, int shelfNumber) throws ProvisionException;

  void deleteFiberConnection(int neId, String fromPtpAid, String toPtpAid);

  void provisionFiberConnectionToAlien(int neId, String fromPtpUri, String toPtpUri, FiberConnectionType type);

  String createOtnProtectionGroup(int neId, String crossConnectUri, String workingCtpUri, String protectCtpUri, ResilienceDto resilienceDto) throws ProvisionException;

  String createOppmProtectionGroup(int neId, String crossConnectUri, String workingCtpUri, String protectCtpUri, ResilienceDto resilienceDto) throws ProvisionException;

  void deletePrtGrp(int neId, String protectionGroupUri) throws ProvisionException, ObjectDoesNotExistException;

  void setEpte(int neId, String protectionGroupUri, int epteId, String epteUri) throws ProvisionException;

  boolean setEpteOnFirstAvailableEpteOnProtectionGroup(int neId, String protectionGroupUri, String epteUri);

  void clearEpte(int neId, String protectionGroupUri, String epteUri) throws ProvisionException;

  void removeAlienPtp(int neId, int shelfNumber, int alienPtpNumber);

  Integer createSlc(int neId, SlcExpressConfiguration configuration) throws ProvisionException;

  void deleteSlc(int neId, int slcIdentifier) throws ProvisionException;

  void updateSLCAdminState(int neId, int slcId, SlcEqualizationDirection direction, AdminState adminState) throws ProvisionException;

  void modifySlcPathNodeNumber(int neId, int slcId, Integer aEndPathNodeNumber, Integer zEndPathNodeNumber) throws ProvisionException;

  String createCccpGroup(int neId, CccpCreateDto cccpCreateDto);

  void deleteCccpGroup(int neId, String cccpName) throws ProvisionException, ObjectDoesNotExistException;
}
