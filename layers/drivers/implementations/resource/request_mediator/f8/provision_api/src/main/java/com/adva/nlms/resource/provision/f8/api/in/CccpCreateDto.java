/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: aleksandrao
 */

package com.adva.nlms.resource.provision.f8.api.in;

import java.io.Serializable;
import java.util.Objects;

public class CccpCreateDto implements Serializable {
    private Integer neId;
    private String usrlbl;
    private String wrkent;
    private String prtent;
    private Boolean pcomften;
    private Integer hdoff;
    private Integer wtr;
    private Boolean rtr;
    private Boolean freeze;
    private String dir;

    public CccpCreateDto() {}

    public CccpCreateDto(Integer neId, String usrlbl, String wrkent, String prtent, Boolean pcomften, Integer hdoff, Integer wtr, Boolean rtr, Boolean freeze, String dir) {
        this.neId = neId;
        this.usrlbl = usrlbl;
        this.wrkent = wrkent;
        this.prtent = prtent;
        this.pcomften = pcomften;
        this.hdoff = hdoff;
        this.wtr = wtr;
        this.rtr = rtr;
        this.freeze = freeze;
        this.dir = dir;
    }

    public Integer getNeId() {
        return neId;
    }

    public void setNeId(Integer neId) {
        this.neId = neId;
    }

    public String getUsrlbl() {
        return usrlbl;
    }

    public void setUsrlbl(String usrlbl) {
        this.usrlbl = usrlbl;
    }

    public String getWrkent() {
        return wrkent;
    }

    public void setWrkent(String wrkent) {
        this.wrkent = wrkent;
    }

    public String getPrtent() {
        return prtent;
    }

    public void setPrtent(String prtent) {
        this.prtent = prtent;
    }

    public Boolean getPcomften() {
        return pcomften;
    }

    public void setPcomften(Boolean pcomften) {
        this.pcomften = pcomften;
    }

    public Integer getHdoff() {
        return hdoff;
    }

    public void setHdoff(Integer hdoff) {
        this.hdoff = hdoff;
    }

    public Integer getWtr() {
        return wtr;
    }

    public void setWtr(Integer wtr) {
        this.wtr = wtr;
    }

    public Boolean getRtr() {
        return rtr;
    }

    public void setRtr(Boolean rtr) {
        this.rtr = rtr;
    }

    public Boolean getFreeze() {
        return freeze;
    }

    public void setFreeze(Boolean freeze) {
        this.freeze = freeze;
    }

    public String getDir() {
        return dir;
    }

    public void setDir(String dir) {
        this.dir = dir;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof CccpCreateDto)) return false;
        CccpCreateDto that = (CccpCreateDto) o;
        return Objects.equals(neId, that.neId) &&
          Objects.equals(pcomften, that.pcomften) &&
          Objects.equals(hdoff, that.hdoff) &&
          Objects.equals(wtr, that.wtr) &&
          Objects.equals(rtr, that.rtr) &&
          Objects.equals(freeze, that.freeze) &&
          Objects.equals(usrlbl, that.usrlbl) &&
          Objects.equals(wrkent, that.wrkent) &&
          Objects.equals(prtent, that.prtent) &&
          Objects.equals(dir, that.dir);
    }

    @Override
    public int hashCode() {
        return Objects.hash(neId, usrlbl, wrkent, prtent, pcomften, hdoff, wtr, rtr, freeze, dir);
    }
}
