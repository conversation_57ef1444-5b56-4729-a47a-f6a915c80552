/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: zbigniewj
 */

package com.adva.nlms.resource.provision.f8.api.in;

import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.OpticalParameters;

import java.util.List;

public interface CimProvisionRequestParametersCreator {
  List<ProvisionRequestParameters> prepareProvisionRequestParameters(NetworkElementID networkElementID,
                                                                     String moduleType,
                                                                     String plugType,
                                                                     String portAid,
                                                                     String portUri,
                                                                     List<OpticalParameters.SelectedParameter> opticalParameters);

  List<ProvisionRequestParameters> prepareProvisionRequestParametersForOdu(NetworkElementID networkElementID,
                                                                           String portUri, MoCapabilityInquiryParams params,
                                                                           List<OpticalParameters.SelectedParameter> opticalParameters);

  List<ProvisionRequestParameters> prepareOduProvisionRequestParameters(NetworkElementID networkElementID,
                                                                        String cardUri,
                                                                        MoCapabilityInquiryParams moCapabilityInquiryParams,
                                                                        List<OpticalParameters.SelectedParameter> opticalParameters);
}
