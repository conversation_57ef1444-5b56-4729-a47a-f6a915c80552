/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.provision.f8.api.in;

public record MoCapabilityInquiryParams(String moduleType, String plugType, String portAid, String... layerQualifiers) {

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    private Builder() {}

    private String moduleType;
    private String plugType;
    private String portAid;
    private String[] layerQualifiers;

    public Builder moduleType(String moduleType) {
      this.moduleType = moduleType;
      return this;
    }

    public Builder plugType(String plugType) {
      this.plugType = plugType;
      return this;
    }

    public Builder portAid(String portAid) {
      this.portAid = portAid;
      return this;
    }

    public Builder layerQualifiers(String... layerQualifiers) {
      this.layerQualifiers = layerQualifiers;
      return this;
    }

    public MoCapabilityInquiryParams build() {
      return new MoCapabilityInquiryParams(moduleType, plugType, portAid, layerQualifiers);
    }
  }
}
