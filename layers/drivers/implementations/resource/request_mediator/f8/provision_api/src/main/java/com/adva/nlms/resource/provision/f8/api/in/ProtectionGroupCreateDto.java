/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: lukaszwl
 */

package com.adva.nlms.resource.provision.f8.api.in;

import java.util.Objects;

public class ProtectionGroupCreateDto {
    private Integer neId;
    private String ent;
    private String usrlbl;
    private Boolean dir;
    private Integer wtr;
    private Boolean rtr;
    private Boolean freeze;
    private Integer hdoff;
    private String wrkent;
    private String prtent;
    private String prtsnc;

    public ProtectionGroupCreateDto() {}

    public ProtectionGroupCreateDto(Integer neId, String ent, String usrlbl, Boolean dir, Integer wtr, Boolean rtr, Boolean freeze, Integer hdoff, String wrkent, String prtent, String prtsnc) {
        this.neId = neId;
        this.ent = ent;
        this.usrlbl = usrlbl;
        this.dir = dir;
        this.wtr = wtr;
        this.rtr = rtr;
        this.freeze = freeze;
        this.hdoff = hdoff;
        this.wrkent = wrkent;
        this.prtent = prtent;
        this.prtsnc = prtsnc;
    }

    public Integer getNeId() {
        return neId;
    }

    public void setNeId(Integer neId) {
        this.neId = neId;
    }

    public String getEnt() {
        return ent;
    }

    public void setEnt(String ent) {
        this.ent = ent;
    }

    public String getUsrlbl() {
        return usrlbl;
    }

    public void setUsrlbl(String usrlbl) {
        this.usrlbl = usrlbl;
    }

    public Boolean getDir() {
        return dir;
    }

    public void setDir(Boolean dir) {
        this.dir = dir;
    }

    public Integer getWtr() {
        return wtr;
    }

    public void setWtr(Integer wtr) {
        this.wtr = wtr;
    }

    public Boolean getRtr() {
        return rtr;
    }

    public void setRtr(Boolean rtr) {
        this.rtr = rtr;
    }

    public Boolean getFreeze() {
        return freeze;
    }

    public void setFreeze(Boolean freeze) {
        this.freeze = freeze;
    }

    public Integer getHdoff() {
        return hdoff;
    }

    public void setHdoff(Integer hdoff) {
        this.hdoff = hdoff;
    }

    public String getWrkent() {
        return wrkent;
    }

    public void setWrkent(String wrkent) {
        this.wrkent = wrkent;
    }

    public String getPrtent() {
        return prtent;
    }

    public void setPrtent(String prtent) {
        this.prtent = prtent;
    }

    public String getPrtsnc() {
        return prtsnc;
    }

    public void setPrtsnc(String prtsnc) {
        this.prtsnc = prtsnc;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ProtectionGroupCreateDto that = (ProtectionGroupCreateDto) o;
        return Objects.equals(neId, that.neId) && Objects.equals(ent, that.ent) && Objects.equals(usrlbl, that.usrlbl) && Objects.equals(dir, that.dir) && Objects.equals(wtr, that.wtr) && Objects.equals(rtr, that.rtr) && Objects.equals(freeze, that.freeze) && Objects.equals(hdoff, that.hdoff) && Objects.equals(wrkent, that.wrkent) && Objects.equals(prtent, that.prtent) && Objects.equals(prtsnc, that.prtsnc);
    }

    @Override
    public int hashCode() {
        return Objects.hash(neId, ent, usrlbl, dir, wtr, rtr, freeze, hdoff, wrkent, prtent, prtsnc);
    }

    @Override
    public String toString() {
        return "ProtectionGroupCreateDto{" +
                "neId=" + neId +
                ", ent='" + ent + '\'' +
                ", usrlbl='" + usrlbl + '\'' +
                ", dir=" + dir +
                ", wtr=" + wtr +
                ", rtr=" + rtr +
                ", freeze=" + freeze +
                ", hdoff=" + hdoff +
                ", wrkent='" + wrkent + '\'' +
                ", prtent='" + prtent + '\'' +
                ", prtsnc='" + prtsnc + '\'' +
                '}';
    }
}