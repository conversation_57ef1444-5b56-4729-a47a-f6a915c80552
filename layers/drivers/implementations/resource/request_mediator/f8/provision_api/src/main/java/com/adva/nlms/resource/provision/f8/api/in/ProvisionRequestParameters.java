/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: zbigniewj
 */

package com.adva.nlms.resource.provision.f8.api.in;

import com.adva.nlms.mediation.topology.NetworkElementID;

import java.util.List;

public record ProvisionRequestParameters(NetworkElementID neID, String cimClass, String cimPath,
                                         List<MoCimParameter> cimParameters) {
}
