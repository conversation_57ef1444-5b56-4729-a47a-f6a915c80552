/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm.adapters.persistence;

import com.adva.nlms.resource.mediator.f8.wdm.api.in.RrmSegmentRepository;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RrmPersistenceConfiguration {

  @Bean
  public RrmSegmentRepository rrmSegmentRepository() {
    return new RrmSegmentRepositoryImpl();
  }
}
