/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.moprovider.api.MoEpteInquiryObject;
import com.adva.infrastructure.capability.moprovider.api.MoPortGroupInquiryObject;
import com.adva.infrastructure.capability.provider.api.CapabilityInquiryObject;
import com.adva.infrastructure.capability.provider.api.CapabilityInquiryObjectBuilder;
import com.adva.infrastructure.capability.provider.api.CapabilityProvider;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.PortIdExtractor;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.opticalparameters.api.Values;
import com.adva.nlms.opticalparameters.api.enums.ParameterName;

import java.util.List;
import java.util.Optional;


class RrmOtnCardCapabilitiesImpl implements RrmOtnCardCapabilities {
  private final NEDataProvider neDataProvider;
  private final CapabilityProvider capabilityProvider;
  private final MoCapabilityProvider moCapabilityProvider;
  private static final String CAPLQ = "_PORT";

  RrmOtnCardCapabilitiesImpl(NEDataProvider neDataProvider, CapabilityProvider capabilityProvider, MoCapabilityProvider moCapabilityProvider) {
    this.neDataProvider = neDataProvider;
    this.capabilityProvider = capabilityProvider;
    this.moCapabilityProvider = moCapabilityProvider;
  }

  @Override
  public boolean isPortClient(int neId, CardRef cardRef, PlugRef plugRef, Aid ptpAid) {
    if (plugRef == null) {
      // TeraFlexes' network ports case
      return false;
    }

    var capio = buildCapabilityInquiryObject(neId, cardRef, plugRef, PortIdExtractor.getPortId(ptpAid.aid()));
    return capabilityProvider.getParameter(ParameterName.SEP_ENNI, capio, CAPLQ) != null ||
           capabilityProvider.getParameter(ParameterName.SEP_UNI, capio, CAPLQ) != null; // modules such as T-MP-2D3DT don't have SEP_ENNI
  }

  @Override
  public List<Values.PortPoolDescriptor.PortSetDescriptor> getPortSetDescriptors(
    int neId,
    CardRef cardRef,
    PlugRef plugRef) {
    var capio = buildCapabilityInquiryObject(neId, cardRef, plugRef, PortIdExtractor.getPortId(plugRef.aid().aid()));
    var result = capabilityProvider.getParameter(ParameterName.POOL_RESTRICTION, capio, CAPLQ);
    if (result != null && result.values() instanceof Values.PortPoolDescriptor portPoolDescriptor) {
      return portPoolDescriptor.values();
    }
    return List.of();
  }

  @Override
  public Optional<String> getGroupPort(String moduleType, String portIdentifier) {
    return moCapabilityProvider.getMoPortGroup(new MoPortGroupInquiryObject(moduleType, portIdentifier));
  }


  @Override
  public Optional<String> getProtectionRestrictionPort(int neId, CardRef cardRef, PlugRef plugRef, Aid ptpAid, String layer) {
    var capio = buildCapabilityInquiryObject(neId, cardRef, plugRef, PortIdExtractor.getPortId(ptpAid.aid()));
    var result = capabilityProvider.getParameter(ParameterName.PROTECTION_RESTRICTION, capio, layer);
    if (result != null && result.values() instanceof Values.PortPoolDescriptor portPoolDescriptor) {
      return Optional.of(portPoolDescriptor.values().get(0).ports().get(0));
    }
    return Optional.empty();
  }

  private CapabilityInquiryObject buildCapabilityInquiryObject(int neId,CardRef cardRef, PlugRef plugRef, String plugOrPortId) {
    var neData = neDataProvider.getNeData(neId);
    return CapabilityInquiryObjectBuilder.newBuilder()
        .elementSwVersion(neData.getSwVersion())
        .elementType(neData.getNetworkElementTypeString())
        .moduleType(cardRef.type())
        .moduleMode(cardRef.mode())
        .plugType(plugRef.type())
        .portIdentifier(plugOrPortId)
        .build();
  }

  @Override
  public MoEpteInquiryObject buildMoEpteInquiryObject(int neId, CardRef cardRef) {
    var neData = neDataProvider.getNeData(neId);
    return new MoEpteInquiryObject(neData.getSwVersion(), cardRef.type(), cardRef.mode());
  }

}
