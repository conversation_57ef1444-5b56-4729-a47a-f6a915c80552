/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.mediator.f8.events.SncProtectionGroupCreatedEvent;
import com.adva.nlms.resource.provision.f8.api.in.ObjectDoesNotExistException;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionException;
import com.adva.nlms.resource.provision.f8.api.in.ResilienceDto;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;

class SncProtectionGroupRequest implements ProtectionGroupRequest {

  private static final Logger log = LogManager.getLogger(SncProtectionGroupRequest.class);
  private final int neId;
  private final String prtGrpUri;
  private final Provision provisionApi;
  private final String sncUri;
  private final String workingCtpUri;
  private final String protectCtpUri;
  private final ResilienceDto resilienceDto;
  private final String ptpToDisableUri;
  private final boolean exists;

  SncProtectionGroupRequest(int neId, String sncUri, String workingCtpUri, String protectCtpUri, ResilienceDto resilienceDto, String ptpToDisableUri, Provision provisionApi) {
    // used in create case
    this.neId = neId;
    this.prtGrpUri = "";
    this.provisionApi = provisionApi;
    this.sncUri = sncUri;
    this.workingCtpUri = workingCtpUri;
    this.protectCtpUri = protectCtpUri;
    this.resilienceDto = resilienceDto;
    this.exists = false;
    this.ptpToDisableUri = ptpToDisableUri;
  }

  SncProtectionGroupRequest(int neId, String prtGrpUri, Provision provisionApi, boolean exists) {
    // used in "adopt" case, and for delete (create request for delete from event)
    this.neId = neId;
    this.prtGrpUri = prtGrpUri;
    this.provisionApi = provisionApi;
    this.sncUri = "";
    this.workingCtpUri = "";
    this.protectCtpUri = "";
    this.resilienceDto = null;
    this.exists = exists;
    this.ptpToDisableUri = "";
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    SncProtectionGroupRequest that = (SncProtectionGroupRequest) o;
    return neId == that.neId && Objects.equals(prtGrpUri, that.prtGrpUri) && Objects.equals(sncUri, that.sncUri)
      && Objects.equals(workingCtpUri, that.workingCtpUri) && Objects.equals(protectCtpUri, that.protectCtpUri)
      && Objects.equals(resilienceDto, that.resilienceDto) && Objects.equals(ptpToDisableUri, that.ptpToDisableUri)
      && exists == that.exists;
  }

  @Override
  public int hashCode() {
    return Objects.hash(neId, prtGrpUri, sncUri, workingCtpUri, protectCtpUri, resilienceDto, exists, ptpToDisableUri);
  }

  @Override
  public ProvisionLocalEvent provision() {
    if (exists) {
      log.info("Protection group uri={} already exists.", prtGrpUri);
      return new SncProtectionGroupCreatedEvent(prtGrpUri);
    } else {
      if (!ptpToDisableUri.isEmpty()) {
        provisionApi.updateAdminState(NetworkElementID.create(neId), ptpToDisableUri, AdminState.DOWN);
      }
      try {
        String protectionGroupUri = provisionApi.createOtnProtectionGroup(neId, sncUri, workingCtpUri, protectCtpUri, resilienceDto);
        log.info("Protection group uri={} created.", protectionGroupUri);
        return new SncProtectionGroupCreatedEvent(protectionGroupUri);
      } catch (ProvisionException e) {
        log.error("Protection group creation failed: {}", e.getLocalizedMessage());
        if (!ptpToDisableUri.isEmpty()) {
          provisionApi.updateAdminState(NetworkElementID.create(neId), ptpToDisableUri, AdminState.UP);
        }
        throw e;
      }
    }
  }

  @Override
  public void delete() {
    log.info("Deleting Protection Group {}", prtGrpUri);
    try {
      provisionApi.deletePrtGrp(neId, prtGrpUri);
    } catch (ObjectDoesNotExistException e) {
      log.warn("Could not delete Protection Group on NE: {} with uri {} - entity does not exist", neId, prtGrpUri);
    }
  }

  @Override
  public ProvisionLocalEvent adopt() {
    return null;
  }
}
