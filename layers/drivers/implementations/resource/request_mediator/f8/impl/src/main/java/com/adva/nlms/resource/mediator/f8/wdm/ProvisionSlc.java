/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.common.util.StringUtils;
import com.adva.nlms.mediation.config.f8.croma.api.CromaMOService;
import com.adva.nlms.mediation.config.f8.croma.endpoint.api.CromaEcServicePathDTO;
import com.adva.nlms.mediation.config.f8.croma.provision.api.SlcEndpointConfiguration;
import com.adva.nlms.mediation.config.f8.croma.provision.api.SlcExpressConfiguration;
import com.adva.nlms.mediation.config.f8.croma.slc.api.Slc;
import com.adva.nlms.mediation.ec.support.EcEntityIndex;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.resource.crm.model.wdm.F8DeviceModelConstants;
import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;
import com.adva.nlms.resource.mediator.f8.SegmentRequestFailedException;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.CrmNodePosition;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Objects;

class ProvisionSlc {
  private static final Logger log = LogManager.getLogger(ProvisionSlc.class);

  private final Provision provision;
  private final CromaMOService cromaMOService;
  private final PtpResources ptpResources;

  ProvisionSlc(Provision provision, CromaMOService cromaMOService, PtpResources ptpResources) {
    this.provision = provision;
    this.cromaMOService = cromaMOService;
    this.ptpResources = ptpResources;
  }

  int provision(ProvisionContext context, ProvisionedPort provisionedPort, List<Integer> deletedSlcs) {
    String protectionPathUri = getProtectionPathUri(context, provisionedPort);
    var endpointsConfig = SlcEndpointsConfiguration.create(context, provisionedPort, protectionPathUri);
    var frequencySlotConfig = SlcFrequencySlotConfiguration.create(context.request(), provisionedPort);
    validSlc(context, provisionedPort, deletedSlcs);
    var slcConfiguration = new SlcExpressConfiguration(
      frequencySlotConfig.bandwidths, frequencySlotConfig.centerFrequencies, endpointsConfig.aEnd, endpointsConfig.zEnd);
    log.info("[CREATE] Creating new SLC neId={} configuration={}", context.neId(), slcConfiguration);
    return provision.createSlc(context.neId(), slcConfiguration);
  }

  private Integer getSlcId(String slcUri) {
    try {
      return Integer.valueOf(slcUri.substring(slcUri.lastIndexOf('/') + 1));
    } catch (NumberFormatException e) {
      return null;
    }
  }

  private boolean isServicePathUsedByMatchingEndpoints(CromaEcServicePathDTO servicePath, ProvisionContext context) {
    return ptpResources.findPtp(context.neId(), new Uri(servicePath.getPortId()))
      .filter(ptp -> {
        var ptpAid = ptp.aid().aid();
        return ptpAid.equals(context.aidDestTp()) || ptpAid.equals(context.aidSrcTp());
      })
      .isPresent();
  }

  private void validSlc(ProvisionContext context, ProvisionedPort provisionedPort, List<Integer> deletedSlcs) {
    if (provisionedPort == null) {
      return;
    }
    var sep = cromaMOService.getServiceEndpoint(context.neId(), EcEntityIndex.getEcEntityIndex(context.sepUri()));
    if (sep == null) {
      return;
    }
    sep.getServicePathEcDTOS().stream()
      .filter(servicePath -> StringUtils.isNotEmpty(servicePath.getSlc()))
      .filter(servicePath -> !deletedSlcs.contains(getSlcId(servicePath.getSlc())))
      .filter(servicePath -> isServicePathUsedByMatchingEndpoints(servicePath, context))
      .findFirst()
      .ifPresent(collidingServicePath -> {
        throw new SegmentRequestFailedException("Found SLC %s already occupying requested port %s".formatted(
          getSlcId(collidingServicePath.getSlc()), provisionedPort.ctpUri()));
      });
  }

  int adopt(ProvisionContext context, boolean reversed) {
    int aEndDegree = switch (context.nodePosition()) {
      case INGRESS -> context.dstDegreeNumber();
      case EGRESS -> context.srcDegreeNumber();
      case TRANSIT -> reversed ? context.dstDegreeNumber() : context.srcDegreeNumber();
      case TRANSIT_REVERSED -> reversed ? context.srcDegreeNumber() : context.dstDegreeNumber();
    };
    int zEndDegree = switch (context.nodePosition()) {
      case INGRESS -> context.srcDegreeNumber();
      case EGRESS -> context.dstDegreeNumber();
      case TRANSIT -> reversed ? context.srcDegreeNumber() : context.dstDegreeNumber();
      case TRANSIT_REVERSED -> reversed ? context.dstDegreeNumber() : context.srcDegreeNumber();
    };
    var aEndActiveEndPoint = aEndDegree != 0 ? generateMitUriOfDegree(aEndDegree) : null;
    var zEndActiveEndPoint = zEndDegree != 0 ? generateMitUriOfDegree(zEndDegree) : null;
    var slcs = cromaMOService.getSlcForNe(context.neId());
    return slcs.stream()
      .filter(slc -> matchFrequencySlot(slc, context.request()))
      .filter(slc ->
        (aEndActiveEndPoint == null || aEndActiveEndPoint.equals(slc.getaEndpoint().getActiveEndpoint().getResourceInstance())) &&
          (zEndActiveEndPoint == null || zEndActiveEndPoint.equals(slc.getzEndpoint().getActiveEndpoint().getResourceInstance()))
      )
      .findAny()
      .map(Slc::getIdentifier)
      .orElse(0);
  }

  public void adoptPathNodeNumbers(int neId, int slcId, SlcPathNodeNumber slcPathNodeNumber) {
    Slc slc = cromaMOService.getSlcForNeAndSlcId(neId, slcId)
      .orElseThrow(() -> new SegmentRequestFailedException("Cannot fetch Slc from the SlcId. neId=%d slcId=%d".formatted(neId, slcId)));
    Integer aEndToModify = slc.getaEndpoint().getPathNodeNumber() != slcPathNodeNumber.directionAZ ? slcPathNodeNumber.directionAZ : null;
    Integer zEndToModify = slc.getzEndpoint().getPathNodeNumber() != slcPathNodeNumber.directionZA ? slcPathNodeNumber.directionZA : null;
    if(aEndToModify == null && zEndToModify == null) {
      log.debug("Modify Slc path node number for both Slc endpoints is not required (no changes).");
    } else {
      log.info("Modify Slc path node number: aEnd - {}, zEnd - {}.",
        aEndToModify != null ? aEndToModify : "no change",
        zEndToModify != null ? zEndToModify : "no change");
      provision.modifySlcPathNodeNumber(neId, slcId, aEndToModify, zEndToModify);
    }
  }

  private record SlcEndpointsConfiguration(SlcEndpointConfiguration aEnd, SlcEndpointConfiguration zEnd) {
    static SlcEndpointsConfiguration create(ProvisionContext context, ProvisionedPort provisionedPort, String protectionPathUri) {
      var aEndDegree = context.srcDegreeNumber() != 0 ? context.srcDegreeNumber() : context.dstDegreeNumber();
      var aEndUri = generateMitUriOfDegree(aEndDegree);
      var zEndUri = findZEndUriOfSlc(context, provisionedPort, aEndDegree);
      var slcPathNodeNumber = SlcPathNodeNumber.create(context.request(), context.nodePosition());
      var setPointDelta = convertSetPointDecibelBasedOnDeviceModel(context.request()
        .getSetPointDelta());
      var aEndConfig = new SlcEndpointConfiguration(slcPathNodeNumber.directionAZ, aEndUri, setPointDelta);
      var zEndConfig = new SlcEndpointConfiguration(slcPathNodeNumber.directionZA, zEndUri, setPointDelta, protectionPathUri);
      return new SlcEndpointsConfiguration(aEndConfig, zEndConfig);
    }

    private static int convertSetPointDecibelBasedOnDeviceModel(Double setPointDeviationDecibel) {
      return (int) (setPointDeviationDecibel * F8DeviceModelConstants.SET_POINT_DEVIATION_MULTIPLIER);
    }

    private static String findZEndUriOfSlc(ProvisionContext context, ProvisionedPort provisionedPort, int aEndDegree) {
      String zEndUri;
      if (context.ttpAid() != null) {
        // AddDrop scenario
        if (provisionedPort == null)
          throw new SegmentRequestFailedException(StringUtils.format(
            "Slc creation failed - Ctp appears to be note created for TTP={}", context.ttpAid()));
        zEndUri = provisionedPort.ctpUri();
      } else {
        // Express scenario
        var zEndDegree = aEndDegree == context.srcDegreeNumber() ? context.dstDegreeNumber() : context.srcDegreeNumber();
        zEndUri = generateMitUriOfDegree(zEndDegree);
      }
      return zEndUri;
    }
  }

  private record SlcFrequencySlotConfiguration(List<Integer> bandwidths, List<Integer> centerFrequencies) {
    static SlcFrequencySlotConfiguration create(CrmSegmentRequestDto segmentRequestDto, ProvisionedPort provisionedPort) {
      var bandwidth = segmentRequestDto.getSlotWidthSrcValueMHz();
      var centerFrequency = segmentRequestDto.getChannelSrcValue();
      if (provisionedPort != null && provisionedPort.getLineCardCtpUri() != null && provisionedPort.bandwidthExplicitlySet()) {
        // workaround for T-MP-M8DCT card which has 2 carriers, even though it is configured as single carrier on CTP
        var subChannelBandwidth = bandwidth / 2;
        var bandwidths = List.of(subChannelBandwidth, subChannelBandwidth);
        var centerFrequencies = List.of(
          centerFrequency - subChannelBandwidth / 2,
          centerFrequency + subChannelBandwidth / 2);
        return new SlcFrequencySlotConfiguration(bandwidths, centerFrequencies);
      } else {
        return new SlcFrequencySlotConfiguration(List.of(bandwidth), List.of(centerFrequency));
      }
    }
  }

  record SlcPathNodeNumber(int directionAZ, int directionZA) {
    static SlcPathNodeNumber create(CrmSegmentRequestDto segmentRequest, CrmNodePosition crmNodePosition) {
      if (crmNodePosition == CrmNodePosition.INGRESS) {
        return new SlcPathNodeNumber(segmentRequest.getReverseSequenceNumber(), segmentRequest.getForwardSequenceNumber());
      } else {
        return new SlcPathNodeNumber(segmentRequest.getForwardSequenceNumber(), segmentRequest.getReverseSequenceNumber());
      }
    }
  }

  private static String generateMitUriOfDegree(int degreeNumber) {
    return "/mit/me/1/croma/degree/" + degreeNumber;
    // Note that FQDN of the degree cannot be used because of a problem in SLC during WdmSegment Adoption.
    // The persistent class of SLC doesnot use FQDN of activeEndpoints and hence we have to always rely on MITURI
  }

  private boolean matchFrequencySlot(Slc slc, CrmSegmentRequestDto request) {
    var frequencies = slc.getFrequencies();
    var bandwidths = slc.getBandwidths();
    if (frequencies.size() == 1) {
      var centerFrequency = frequencies.get(0);
      var bandwidth = bandwidths.get(0);
      return centerFrequency == request.getChannelSrcValue() && bandwidth >= request.getSlotWidthSrcValueMHz();
    } else if (frequencies.size() == 2) {
      // workaround for T-MP-M8DCT card which has 2 carriers, even though it is configured as single carrier on CTP
      var centerFrequency = (frequencies.get(0) + frequencies.get(1)) / 2;
      var bandwidth = bandwidths.get(0) + slc.getBandwidths().get(1);
      return centerFrequency == request.getChannelSrcValue() && bandwidth >= request.getSlotWidthSrcValueMHz();
    } else {
      // Invalid slc configuration?
      return false;
    }
  }

  private String getProtectionPathUri(ProvisionContext context, ProvisionedPort provisionedPort) {
    if (Objects.nonNull(provisionedPort) && !Objects.equals(context.ttpAid(), context.aidSrcTp()) && !Objects.equals(context.ttpAid(), context.aidDestTp())) {
      // Condition to check if it's OPPM card
      return context.srcDegreeNumber() != 0 ? getPtpUri(context.neId(), context.aidDestTp()) : getPtpUri(context.neId(), context.aidSrcTp());
    }

    return null;
  }

  private String getPtpUri(int neId, String aid) {
    return ptpResources.findPtp(neId, new Aid(aid))
      .map(PtpRef::uri)
      .map(Uri::uri)
      .orElse(null);
  }

}
