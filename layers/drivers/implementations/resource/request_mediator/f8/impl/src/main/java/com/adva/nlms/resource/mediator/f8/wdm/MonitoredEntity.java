/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.mediator.f8.wdm;

public record MonitoredEntity(String segmentRequestId, String entityUri, TerminationPointPair terminationPointPair, EquipmentType equipmentType) {

  public record TerminationPointPair(String tx, String rx) {
    public static TerminationPointPair of(String tx, String rx) {
      return new TerminationPointPair(tx, rx);
    }
  }

  public enum EquipmentType {
    ROADM, LINE_CARD, ALIEN
  }

  public MonitoredEntityKey toKey(int neId) {
    return new MonitoredEntityKey(neId, entityUri);
  }

}
