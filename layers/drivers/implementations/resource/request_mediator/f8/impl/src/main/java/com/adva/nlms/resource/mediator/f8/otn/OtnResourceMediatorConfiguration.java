/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: zbigniewj
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.provider.api.CapabilityProvider;
import com.adva.nlms.inf.api.PredefinedObserver;
import com.adva.nlms.mediation.mo.inventory.resources.CardResources;
import com.adva.nlms.mediation.mo.inventory.resources.CrossConnectResources;
import com.adva.nlms.mediation.mo.inventory.resources.CtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.PlugResources;
import com.adva.nlms.mediation.mo.inventory.resources.ProtectionGroupResources;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.resource.mediator.f8.ProtectionGroupFinder;
import com.adva.nlms.resource.mediator.f8.adapters.persistence.InMemoryOtnSegmentRepository;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegmentRepository;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OtnResourceMediatorConfiguration {
  @Bean
  RrmOtnCardCapabilities rrmOtnCardCapabilities(NEDataProvider neDataProvider, CapabilityProvider capabilityProvider, MoCapabilityProvider moCapabilityProvider) {
    return new RrmOtnCardCapabilitiesImpl(neDataProvider, capabilityProvider, moCapabilityProvider);
  }

  @Bean
  RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade(CtpResources ctpResources, PtpResources ptpResources,
                                                  PlugResources plugResources, CardResources cardResources,
                                                  CrossConnectResources crossConnectResources, ProtectionGroupResources protectionGroupResources) {
    return new RrmOtnDbResourcesFacadeImpl(ctpResources, ptpResources, plugResources, cardResources,
                                           crossConnectResources, protectionGroupResources);
  }

  @Bean
  OtnResourceRequestMediator otnResourceRequestMediator(MoCapabilityProvider moCapabilityProvider,
                                                        Provision provisionApi,
                                                        OtnSegmentRepository otnSegmentRepository,
                                                        RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade,
                                                        RrmOtnCardCapabilities rrmOtnCardCapabilities,
                                                        ProtectionGroupFinder protectionGroupFinder) {
    return new OtnResourceRequestMediatorImpl(
      new OtnCimProvisionRequestParametersCreatorImpl(moCapabilityProvider),
      provisionApi,
      new RequestExecutor(otnSegmentRepository, provisionApi),
      otnSegmentRepository,
      rrmOtnDbResourcesFacade,
      rrmOtnCardCapabilities,
      protectionGroupFinder,
      moCapabilityProvider
    );
  }

  @Bean
  @PredefinedObserver
  RrmOtnNeObserver rrmOtnNeObserver(OtnResourceRequestMediator otnResourceRequestMediator) {
    return new RrmOtnNeObserver(otnResourceRequestMediator);
  }

  @Bean
  OtnSegmentRepository inMemoryOtnSegmentRepository() {
    return new InMemoryOtnSegmentRepository();
  }
}