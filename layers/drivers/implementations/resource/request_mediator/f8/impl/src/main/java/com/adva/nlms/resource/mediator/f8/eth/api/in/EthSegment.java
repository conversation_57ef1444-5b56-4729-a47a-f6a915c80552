/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.eth.api.in;

import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;

import java.util.Collections;
import java.util.List;

public record EthSegment(SegmentID segmentID, List<ProvisionLocalEvent> provisionEvents) {
  @Override
  public List<ProvisionLocalEvent> provisionEvents() {
    return Collections.unmodifiableList(provisionEvents);
  }
}
