/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.infrastructure.capability.moprovider.api.MoAttributePathFormatter;
import com.adva.infrastructure.capability.moprovider.api.MoAttributePathParameter;
import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.moprovider.api.MoClassCapabilities;
import com.adva.infrastructure.capability.moprovider.api.MoClassCapability;
import com.adva.infrastructure.capability.moprovider.api.MoClassCapabilityInquiryObject;
import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.OpticalParameters;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.opticalparameters.cim.api.CimParameter;
import com.adva.nlms.opticalparameters.cim.api.OpticalParametersCim;
import com.adva.nlms.resource.provision.f8.api.in.MoCapabilityInquiryParams;
import com.adva.nlms.resource.provision.f8.api.in.MoCimParameter;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionRequestParameters;

import java.util.ArrayList;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Class prepares provision parameters from protobuf model to CIM model
 */
class OtnCimProvisionRequestParametersCreatorImpl {

  private final MoCapabilityProvider moCapabilityProvider;

  OtnCimProvisionRequestParametersCreatorImpl(MoCapabilityProvider moCapabilityProvider) {
    this.moCapabilityProvider = moCapabilityProvider;
  }

  List<ProvisionRequestParameters> prepareProvisionRequestParameters(NetworkElementID networkElementID,
                                                                     String cardUri,
                                                                     MoCapabilityInquiryParams moCapabilityInquiryParams,
                                                                     List<OpticalParameters.SelectedParameter> opticalParameters) {
    List<CimParameter> cimParameters = OpticalParametersCim.fromCommon(opticalParameters);
    List<MoParams> allMoParams = convertRequestParameters(moCapabilityInquiryParams, cimParameters);

    String ptpPathPart = resolvePtpUriPathPart(moCapabilityInquiryParams, allMoParams);
    return allMoParams.stream()
      .map(classMoParams -> {
        String path = prepareCimPathFromPortUri(classMoParams.moClass(), cardUri, ptpPathPart, classMoParams.path());
        return new ProvisionRequestParameters(networkElementID, classMoParams.moClass(), path, classMoParams.params());
      })
      .toList();
  }

  boolean isEntityAutocreated(String type, String mode, LayerQualifier layerQualifier) {
    return moCapabilityProvider.isEntityAutocreated(type, mode, layerQualifier);
  }

  private String resolvePtpUriPathPart(MoCapabilityInquiryParams moCapabilityInquiryParams, List<MoParams> allMoParams) {
    String portNumber = PortParser.convertToPortNumber(moCapabilityInquiryParams.portAid());
    return allMoParams.stream()
      .filter(params -> "ptp".equals(params.moClass()))
      .map(MoParams::path)
      .findAny()
      .map(path -> MoAttributePathFormatter.formatPath(path, Map.of(MoAttributePathParameter.PORT_ID, portNumber), false))
      .orElse(null);
  }

  private String prepareCimPathFromPortUri(String objectType, String cardUri, String ptpUri, String capPath) {
    final List<String> uriParts = new ArrayList<>();
    uriParts.add(cardUri);
    switch (objectType) {
      case "ctp" -> uriParts.add(ptpUri);
      case "ptp" -> { // nothing to add for this case
      }
      default ->
        throw new IllegalArgumentException("Cannot prepare valid cim URI for unknown %s object type.".formatted(objectType));
    }
    uriParts.add(capPath);
    return uriParts.stream()
      .filter(Objects::nonNull)
      .collect(Collectors.joining());
  }

  private List<MoParams> convertRequestParameters(MoCapabilityInquiryParams moCapabilityInquiryParams, List<CimParameter> cimParameters) {
    Map<MoAttributePathParameter, String> pathParameters = new EnumMap<>(MoAttributePathParameter.class);
    String portNumber = PortParser.convertToPortNumber(moCapabilityInquiryParams.portAid());
    pathParameters.put(MoAttributePathParameter.PORT_ID, portNumber);
    pathParameters.put(MoAttributePathParameter.ID, "{id}");
    pathParameters.put(MoAttributePathParameter.IF_NUM, "1"); // constant for now
    String portIdentifier = PortParser.convertToPortIdentifier(moCapabilityInquiryParams.portAid());

    var mocCapIO = new MoClassCapabilityInquiryObject(
      moCapabilityInquiryParams.moduleType(), moCapabilityInquiryParams.plugType(), portIdentifier);
    MoClassCapabilities capabilities = moCapabilityProvider.getMoClassCapabilities(
      mocCapIO, moCapabilityInquiryParams.layerQualifiers());

    return capabilities.capabilities()
      .stream()
      .map(moClassCapability -> getMoParams(cimParameters, moClassCapability, pathParameters))
      .toList();
  }

  private MoParams getMoParams(List<CimParameter> cimParameters, MoClassCapability moClassCapability, Map<MoAttributePathParameter, String> pathParameters) {
    var parameters = cimParameters.stream()
      .map(cimParameter -> getMoCimParameter(moClassCapability, pathParameters, cimParameter))
      .flatMap(Optional::stream)
      .toList();
    String moClassCapPath = MoAttributePathFormatter.formatPath(moClassCapability.path(), pathParameters, false);
    return new MoParams(moClassCapability.moClass(), moClassCapPath, parameters);
  }

  private Optional<MoCimParameter> getMoCimParameter(MoClassCapability moClassCapability,
                                                     Map<MoAttributePathParameter, String> pathParameters,
                                                     CimParameter cimParameter) {
    var pathCap = moClassCapability.attributes()
      .stream()
      .filter(moAttributePathCapability -> moAttributePathCapability.attribute().equals(cimParameter.name()))
      .findAny();
    return pathCap.map(moAttributePathCapability -> {
      String path = MoAttributePathFormatter.formatPath(moAttributePathCapability.path(), pathParameters, true);
      return new MoCimParameter(
        path,
        cimParameter.name(),
        cimParameter.value());
    });
  }

  private record MoParams(String moClass, String path, List<MoCimParameter> params) {
  }

  private static class PortParser {

    private static final Pattern PORT_NUMBER_PATTERN = Pattern.compile("\\d+");

    static String convertToPortNumber(String portAid) {

      var portId = portAid.substring(portAid.lastIndexOf("/") + 1);
      Matcher matcher = PORT_NUMBER_PATTERN.matcher(portId);
      if (matcher.find()) {
        return portId.substring(matcher.start());
      }
      return "1";// if no port number specified, return 1
    }

    static String convertToPortIdentifier(String portAid) {
      return portAid.substring(portAid.lastIndexOf("/") + 1).toUpperCase();
    }
  }
}
