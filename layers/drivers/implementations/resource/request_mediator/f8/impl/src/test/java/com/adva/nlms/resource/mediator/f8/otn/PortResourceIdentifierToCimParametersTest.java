/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: zbigniewj
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.opticalparameters.cim.api.CimValue;
import com.adva.nlms.resource.mediator.f8.internalpath.Complete;
import com.adva.nlms.resource.mediator.f8.internalpath.Container;
import com.adva.nlms.resource.mediator.f8.internalpath.Indistinct;
import com.adva.nlms.resource.mediator.f8.internalpath.LabelDTO;
import com.adva.nlms.resource.mediator.f8.internalpath.MultiLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabelType;
import com.adva.nlms.resource.mediator.f8.internalpath.PortResourceIdentifier;
import com.adva.nlms.resource.mediator.f8.internalpath.StackedLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.Whole;
import com.adva.nlms.resource.mediator.f8.otn.PortResourceIdentifierToCimParameters;
import com.adva.nlms.resource.provision.f8.api.in.MoCimParameter;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class PortResourceIdentifierToCimParametersTest {

  private static PortResourceIdentifier generateOtnLabelDTO(OtnLabelType in) {
    return new LabelDTO(new MultiLabel(List.of(new OtnLabel(in))));
  }

  private static PortResourceIdentifier generateOtnStackedLabel(OtnLabelType in) {
    return new StackedLabel(new LabelDTO(new MultiLabel(List.of(new OtnLabel(in)))),
      new LabelDTO(new MultiLabel(List.of(new OtnLabel(new Container(4))))));
  }

  private final LayerQualifier type = LayerQualifier.ODU2;

  @Test
  void generateFromWholeLabelTest() {
    var label = generateOtnLabelDTO(new Whole());
    var result = PortResourceIdentifierToCimParameters.generate(label, type);
    Assertions.assertTrue(result.isEmpty());
  }

  @Test
  void generateFromCompleteLabelTest() {
    var label = generateOtnLabelDTO(new Complete(1, List.of(1, 2, 3, 4)));
    var tpParam = new MoCimParameter("odu2/odtu", "tp", new CimValue.Int(1));
    var tsParam = new MoCimParameter("odu2/odtu", "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L)));
    var result = PortResourceIdentifierToCimParameters.generate(label, type);

    Assertions.assertEquals(2, result.size());
    Assertions.assertTrue(result.contains(tpParam));
    Assertions.assertTrue(result.contains(tsParam));
  }

  @Test
  void generateFromContainerLabelTest() {
    var label = generateOtnLabelDTO(new Container(6));
    var result = PortResourceIdentifierToCimParameters.generate(label, type);
    Assertions.assertTrue(result.isEmpty());
  }

  @Test
  void generateFromIndistinctLabelTest() {
    var label = generateOtnLabelDTO(new Indistinct(6, 20));
    var result = PortResourceIdentifierToCimParameters.generate(label, type);
    Assertions.assertTrue(result.isEmpty());
  }

  @Test
  void generateFromStackedLabelWholeLabelTest() {
    var label = generateOtnStackedLabel(new Whole());
    var result = PortResourceIdentifierToCimParameters.generate(label, type);
    Assertions.assertTrue(result.isEmpty());
  }

  @Test
  void generateFromStackedLabelCompleteLabelTest() {
    var label = generateOtnStackedLabel(new Complete(1, List.of(1, 2, 30, 4)));
    var tpParam = new MoCimParameter("odu2/odtu", "tp", new CimValue.Int(1));
    var tsParam = new MoCimParameter("odu2/odtu", "ts", new CimValue.IntList(List.of(1L, 2L, 30L, 4L)));
    var result = PortResourceIdentifierToCimParameters.generate(label, type);

    Assertions.assertEquals(2, result.size());
    Assertions.assertTrue(result.contains(tpParam));
    Assertions.assertTrue(result.contains(tsParam));
  }

  @Test
  void generateFromStackedLabelContainerLabelTest() {
    var label = generateOtnStackedLabel(new Container(6));
    var result = PortResourceIdentifierToCimParameters.generate(label, type);
    Assertions.assertTrue(result.isEmpty());
  }

  @Test
  void generateFromStackedLabelIndistinctLabelTest() {
    var label = generateOtnStackedLabel(new Indistinct(6, 20));
    var result = PortResourceIdentifierToCimParameters.generate(label, type);
    Assertions.assertTrue(result.isEmpty());
  }
}
