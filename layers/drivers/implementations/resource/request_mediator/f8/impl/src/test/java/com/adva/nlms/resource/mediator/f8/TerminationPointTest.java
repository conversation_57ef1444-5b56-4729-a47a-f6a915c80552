/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */
 
package com.adva.nlms.resource.mediator.f8;

import com.adva.nlms.resource.mediator.f8.internalpath.TerminationPoint;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class TerminationPointTest {

    @Test
    void testGetModuleAid_ValidAid() {
        TerminationPoint tp = new TerminationPoint("Port-1/1/c6", null, true);
        String moduleAid = tp.getModuleAid();
        assertEquals("Module-1/1", moduleAid);
    }

    @Test
    void testGetModuleAid_ValidLongAid() {
        TerminationPoint tp = new TerminationPoint("Port-2/12/c6/p4-2", null, true);
        String moduleAid = tp.getModuleAid();
        assertEquals("Module-2/12", moduleAid);
    }

    @Test
    void testGetModuleAid_InvalidAid() {
        TerminationPoint tp = new TerminationPoint("Port-1", null, true);
        Exception exception = assertThrows(IllegalArgumentException.class, tp::getModuleAid);
        assertEquals("Invalid termination point aid: Port-1", exception.getMessage());
    }

}