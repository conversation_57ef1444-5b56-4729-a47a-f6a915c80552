/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.resource.mediator.f8.ProtectionSettings;
import com.adva.nlms.resource.mediator.f8.events.CtpAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.CtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegment;
import com.adva.nlms.resource.provision.f8.api.in.CccpCreateDto;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionException;

import java.util.Optional;

class PrepareCccProtectionGroupRequest {
  private final int neId;
  private final Provision provisionApi;
  private final OtnSegment workingSegment;
  private final OtnSegment protectionSegment;
  private final ProtectionSettings protectionSettings;
  private final RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade;

  PrepareCccProtectionGroupRequest(int neId, OtnSegment workingSegment, OtnSegment protectionSegment, ProtectionSettings protectionSettings, Provision provisionApi, RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade) {
    this.neId = neId;
    this.workingSegment = workingSegment;
    this.protectionSegment = protectionSegment;
    this.protectionSettings = protectionSettings;
    this.provisionApi = provisionApi;
    this.rrmOtnDbResourcesFacade = rrmOtnDbResourcesFacade;
  }

  CccProtectionGroupRequest prepareRequest() throws ProvisionException {
    var workingUri = findClientUri(workingSegment);
    var protectUri = findClientUri(protectionSegment);
    Optional<String> cccpName = findExistingCccProtectionGroup(workingUri, protectUri);
    if (cccpName.isPresent()) {
      return new CccProtectionGroupRequest(neId, cccpName.get(), provisionApi);
    }
    CccpCreateDto cccpCreateDto = new CccpCreateDto(neId, "", workingUri.uri(), protectUri.uri(), null, protectionSettings.holdOffTimer(), protectionSettings.waitToRestore(), protectionSettings.revertive(), null, protectionSettings.direction());
    return new CccProtectionGroupRequest(neId, cccpCreateDto, provisionApi);
  }

  private Optional<String> findExistingCccProtectionGroup(Uri workingUri, Uri protectUri) {
    return rrmOtnDbResourcesFacade.findCccpGroupName(neId, workingUri)
      .or(() -> rrmOtnDbResourcesFacade.findCccpGroupName(neId, protectUri));
  }

  private Uri findClientUri(OtnSegment segment) {
    var events = segment.provisionEvents();
    var clientPtp = rrmOtnDbResourcesFacade.findPtp(neId, new Aid(segment.protectionData().clientProtectionPort().aid()));
    if (clientPtp.isEmpty()) {
      throw new ProvisionException("Could not create protection group, could not found client PTP for Protection Group Request");
    }
    var clientUri = clientPtp.get().uri().uri();
    return events.stream()
      .filter(event -> event.uri().contains(clientUri) && event instanceof CtpCreatedEvent || event instanceof CtpAdoptedEvent)
      .map(event -> new Uri(event.uri()))
      .findFirst()
      .orElseThrow(() -> new ProvisionException("Could not create protection group, could not found client CTP for Protection Group Request"));
  }
}
