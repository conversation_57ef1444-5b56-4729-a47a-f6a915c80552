/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8;

import com.adva.infrastructure.capability.moprovider.api.MoAttributePathCapability;
import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.moprovider.api.MoClassCapabilities;
import com.adva.infrastructure.capability.moprovider.api.MoClassCapability;
import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.OpticalParameters;
import com.adva.nlms.opticalparameters.api.enums.ParameterName;
import com.adva.nlms.resource.provision.f8.api.in.MoCapabilityInquiryParams;
import com.adva.nlms.resource.provision.f8.api.in.MoCimParameter;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionRequestParameters;
import ni.proto.map.MapOuterClass;
import org.assertj.core.api.Assertions;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;


@ExtendWith(MockitoExtension.class)
class CimProvisionRequestParametersCreatorImplTest {

  private static final String MODULE_TYPE = "T-MP-2D8DCT";
  private static final String PORT_AID = "Port-1/2/n";
  private static final String PORT_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1";
  private static final String LAYER_QUALIFIER = "OTUC4PA";

  private static final NetworkElementID NETWORK_ELEMENT_ID = NetworkElementID.create(1);

  @Mock
  private MoCapabilityProvider moCapabilityProvider;

  private CimProvisionRequestParametersCreatorImpl sut;

  @BeforeEach
  void beforeEach() {
    sut = new CimProvisionRequestParametersCreatorImpl(moCapabilityProvider);
  }

  @Test
  void testPositiveCase() {
    MapOuterClass.KeyValuePair channelKeyValuePair = MapOuterClass.KeyValuePair.newBuilder()
                                                                               .setK(ParameterName.CHANNEL.toString())
                                                                               .setV("196.125 THz")
                                                                               .build();
    MapOuterClass.KeyValuePair glqKeyValuePair = MapOuterClass.KeyValuePair.newBuilder()
                                                                           .setK(ParameterName.GLQ.toString())
                                                                           .setV(LAYER_QUALIFIER)
                                                                           .build();
    MapOuterClass.KeyValuePair bpsKeyValuePair = MapOuterClass.KeyValuePair.newBuilder()
                                                                           .setK(ParameterName.BPS.toString())
                                                                           .setV("3.532")
                                                                           .build();
    MapOuterClass.KeyValuePair modulationKeyValuePair = MapOuterClass.KeyValuePair.newBuilder()
                                                                                  .setK(ParameterName.MODULATION.toString())
                                                                                  .setV("QPSK")
                                                                                  .build();

    List<OpticalParameters.SelectedParameter> selectedParameters =
            convertProtoParametersToOptical(List.of(channelKeyValuePair, glqKeyValuePair, bpsKeyValuePair, modulationKeyValuePair));
    MoClassCapabilities capabilities = new MoClassCapabilities(
            List.of(create_OF_2D16DCT_N1_OTUC4_Ctp_MoAttributePathCapability(),
                    create_OF_2D16DCT_N1_OTUC4_Ptp_MoAttributePathCapability()));
    Mockito.when(moCapabilityProvider.getMoClassCapabilities(Mockito.any(), Mockito.anyString()))
           .thenReturn(capabilities);

    List<ProvisionRequestParameters> provisionRequests = sut.prepareProvisionRequestParameters(NETWORK_ELEMENT_ID,
                                                                                               MODULE_TYPE,
                                                                                               "",
                                                                                               PORT_AID,
                                                                                               PORT_URI,
                                                                                               selectedParameters);

    Assertions.assertThat(provisionRequests)
              .isNotNull()
              .filteredOn(p -> "ctp".equals(p.cimClass()))
              .first()
              .hasFieldOrPropertyWithValue("cimClass", "ctp")
              .hasFieldOrPropertyWithValue("neID", NETWORK_ELEMENT_ID)
              .hasFieldOrPropertyWithValue("cimPath", PORT_URI + "/ctp/ot400")
              .extracting(ProvisionRequestParameters::cimParameters)
              .asList()
              .hasSize(2)
              .hasOnlyElementsOfType(MoCimParameter.class)
              .filteredOn(cimParameter -> "modulation".equals(((MoCimParameter) cimParameter).name()))
              .first(InstanceOfAssertFactories.type(MoCimParameter.class))
              .isNotNull()
              .extracting(MoCimParameter::motrait)
              .isEqualTo("otsia/#otsi=id=1/ochcfg");
  }

  @Test
  public void prepareProvisionRequestParametersForOduTest() {
    final String moduleType = "OF-2D16DCT";
    MapOuterClass.KeyValuePair channelKeyValuePair = MapOuterClass.KeyValuePair.newBuilder()
                                                                               .setK(ParameterName.TERM.toString())
                                                                               .setV("CTP")
                                                                               .build();
    MapOuterClass.KeyValuePair bpsKeyValuePair = MapOuterClass.KeyValuePair.newBuilder()
                                                                           .setK(ParameterName.BPS.toString())
                                                                           .setV("3.532")
                                                                           .build();
    MapOuterClass.KeyValuePair modulationKeyValuePair = MapOuterClass.KeyValuePair.newBuilder()
                                                                                  .setK(ParameterName.MODULATION.toString())
                                                                                  .setV("QPSK")
                                                                                  .build();

    List<OpticalParameters.SelectedParameter> selectedParameters = convertProtoParametersToOptical(List.of(channelKeyValuePair,
                                                                                                           bpsKeyValuePair,
                                                                                                           modulationKeyValuePair));
    MoClassCapabilities capabilities = new MoClassCapabilities(
            List.of(create_OF_2D16DCT_N1_OTUC4_Ptp_MoAttributePathCapability(),
                    create_OF_2D16DCT_N1_OTUC4_ODUC4_Ctp_MoAttributePathCapability()));
    Mockito.when(moCapabilityProvider.getMoClassCapabilities(Mockito.any(), Mockito.any(String[].class))).thenReturn(capabilities);

    MoCapabilityInquiryParams moCapabilityInquiryParams = MoCapabilityInquiryParams.builder()
                                                                                   .moduleType(moduleType)
                                                                                   .plugType("QSFP28-112G-ZR+-SM-LC")
                                                                                   .portAid(PORT_AID)
                                                                                   .layerQualifiers("otu4", "odu4")
                                                                                   .build();

    List<ProvisionRequestParameters> actual = sut.prepareProvisionRequestParametersForOdu(NETWORK_ELEMENT_ID,
                                                                                          PORT_URI,
                                                                                          moCapabilityInquiryParams,
                                                                                          selectedParameters);
    Assertions.assertThat(actual)
              .as("ProvisionRequestParameters list")
              .isNotNull()
              .filteredOn(p -> "ctp".equals(p.cimClass()))
              .first()
              .hasFieldOrPropertyWithValue("neID", NETWORK_ELEMENT_ID)
              .hasFieldOrPropertyWithValue("cimClass", "ctp")
              .hasFieldOrPropertyWithValue("cimPath", PORT_URI + "/ctp/otuc4/ctp/oduc4")
              .extracting(ProvisionRequestParameters::cimParameters)
              .asList()
              .hasSize(2)
              .hasOnlyElementsOfType(MoCimParameter.class)
              .filteredOn(cimParameter -> "termination-mode".equals(((MoCimParameter) cimParameter).name()))
              .first(InstanceOfAssertFactories.type(MoCimParameter.class))
              .isNotNull()
              .extracting(MoCimParameter::motrait)
              .isEqualTo("");
  }


  @Test
  public void prepareOduProvisionRequestParametersTest() {
    final String moduleType = "OF-2D16DCT";
    final String cardUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card";
    MapOuterClass.KeyValuePair channelKeyValuePair = MapOuterClass.KeyValuePair.newBuilder()
                                                                               .setK(ParameterName.CHANNEL.toString())
                                                                               .setV("196.125 THz")
                                                                               .build();
    MapOuterClass.KeyValuePair bpsKeyValuePair = MapOuterClass.KeyValuePair.newBuilder()
                                                                           .setK(ParameterName.BPS.toString())
                                                                           .setV("3.532")
                                                                           .build();
    MapOuterClass.KeyValuePair modulationKeyValuePair = MapOuterClass.KeyValuePair.newBuilder()
                                                                                  .setK(ParameterName.MODULATION.toString())
                                                                                  .setV("QPSK")
                                                                                  .build();

    List<OpticalParameters.SelectedParameter> selectedParameters = convertProtoParametersToOptical(List.of(channelKeyValuePair,
                                                                                                           bpsKeyValuePair,
                                                                                                           modulationKeyValuePair));
    MoClassCapabilities capabilities = new MoClassCapabilities(
            List.of(create_OF_2D16DCT_N1_OTUC4_Ctp_MoAttributePathCapability(),
                    create_OF_2D16DCT_N1_OTUC4_ODUC4_Ctp_MoAttributePathCapability(),
                    create_OF_2D16DCT_N1_Ptp_MoAttributePathCapability()));

    Mockito.when(moCapabilityProvider.getMoClassCapabilities(Mockito.any(), Mockito.any(String[].class))).thenReturn(capabilities);

    MoCapabilityInquiryParams moCapabilityInquiryParams = MoCapabilityInquiryParams.builder()
                                                                                   .moduleType(moduleType)
                                                                                   .plugType("")
                                                                                   .portAid("Port-1/2/n")
                                                                                   .layerQualifiers("otu4")
                                                                                   .build();

    List<ProvisionRequestParameters> actual = sut.prepareOduProvisionRequestParameters(NETWORK_ELEMENT_ID,
                                                                                       cardUri,
                                                                                       moCapabilityInquiryParams,
                                                                                       selectedParameters);
    Assertions.assertThat(actual)
              .as("ProvisionRequestParameters list")
              .isNotNull()
              .filteredOn(p -> "ctp".equals(p.cimClass()))
              .first()
              .hasFieldOrPropertyWithValue("neID", NETWORK_ELEMENT_ID)
              .hasFieldOrPropertyWithValue("cimClass", "ctp")
      .hasFieldOrPropertyWithValue("cimPath", cardUri + "/ptp/nw,1/ctp/ot400")
              .extracting(ProvisionRequestParameters::cimParameters)
              .asList()
              .hasSize(2)
              .hasOnlyElementsOfType(MoCimParameter.class)
              .filteredOn(cimParameter -> "modulation".equals(((MoCimParameter) cimParameter).name()))
              .first(InstanceOfAssertFactories.type(MoCimParameter.class))
              .isNotNull()
              .extracting(MoCimParameter::motrait)
              .isEqualTo("otsia/#otsi=id=1/ochcfg");
  }

  @Test
  public void prepareOduProvisionRequestParametersForFirstClientPort() {
    final String moduleType = "OF-2D16DCT";
    final String cardUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card";
    MapOuterClass.KeyValuePair channelKeyValuePair = MapOuterClass.KeyValuePair.newBuilder()
                                                                               .setK(ParameterName.CHANNEL.toString())
                                                                               .setV("196.125 THz")
                                                                               .build();
    MapOuterClass.KeyValuePair setPointKeyValuePair = MapOuterClass.KeyValuePair.newBuilder()
                                                                                  .setK(ParameterName.FTS_CONTROL.toString())
                                                                                  .setV("AIS")
                                                                                  .build();

    List<OpticalParameters.SelectedParameter> selectedParameters = convertProtoParametersToOptical(List.of(channelKeyValuePair,
                                                                                                           setPointKeyValuePair));
    MoClassCapabilities capabilities = new MoClassCapabilities(
            List.of(create_OF_2D16DCT_C1_Ptp_MoAttributePathCapability()));
    Mockito.when(moCapabilityProvider.getMoClassCapabilities(Mockito.any(), Mockito.any(String[].class)))
           .thenReturn(capabilities);

    MoCapabilityInquiryParams moCapabilityInquiryParams = MoCapabilityInquiryParams.builder()
                                                                                   .moduleType(moduleType)
                                                                                   .plugType("")
                                                                                   .portAid("Port-1/1/c")
                                                                                   .layerQualifiers("OTSiMC")
                                                                                   .build();

    List<ProvisionRequestParameters> actual = sut.prepareOduProvisionRequestParameters(NETWORK_ELEMENT_ID,
                                                                                       cardUri,
                                                                                       moCapabilityInquiryParams,
                                                                                       selectedParameters);
    Assertions.assertThat(actual)
              .as("ProvisionRequestParameters list")
              .isNotNull()
              .filteredOn(p -> "ptp".equals(p.cimClass()))
              .first()
              .hasFieldOrPropertyWithValue("neID", NETWORK_ELEMENT_ID)
              .hasFieldOrPropertyWithValue("cimClass", "ptp")
              .hasFieldOrPropertyWithValue("cimPath", cardUri + "/ptp/cl,1")
              .extracting(ProvisionRequestParameters::cimParameters)
              .asList()
              .hasSize(2)
              .hasOnlyElementsOfType(MoCimParameter.class)
              .filteredOn(cimParameter -> "fts-control".equals(((MoCimParameter) cimParameter).name()))
              .first(InstanceOfAssertFactories.type(MoCimParameter.class))
              .isNotNull()
              .extracting(MoCimParameter::motrait)
              .isEqualTo("dsbld");
  }

  private MoClassCapability create_OF_2D16DCT_N1_OTUC4_Ctp_MoAttributePathCapability() {
    List<MoAttributePathCapability> attributes = List.of(
            new MoAttributePathCapability("cdc-range", "otsia/#otsi={id}/cdcrange"),
            new MoAttributePathCapability("bits-per-symbol", "otsia/#otsi={id}/ochcfg"),
            new MoAttributePathCapability("filter-roll-off", "otsia/#otsi={id}/ochcfg"),
            new MoAttributePathCapability("filter-shape", "otsia/#otsi={id}/ochcfg"),
            new MoAttributePathCapability("modulation", "otsia/#otsi={id}/ochcfg"),
            new MoAttributePathCapability("state-of-polarization-tracking", "otsia/#otsi={id}/ochcfg"),
            new MoAttributePathCapability("fec-type", "otuc4pa")
    );
    return new MoClassCapability("ctp", "/ctp/ot400", attributes);
  }

  private MoClassCapability create_OF_2D16DCT_N1_OTUC4_Ptp_MoAttributePathCapability() {
    List<MoAttributePathCapability> attributes = List.of(
            new MoAttributePathCapability("tuned-frequency", "opt"),
            new MoAttributePathCapability("non-assoc-oh-support", "opt"),
            new MoAttributePathCapability("opt-setpoint", "opt")
    );
    return new MoClassCapability("ptp", "ptp/nw,{portId}", attributes);
  }

  private MoClassCapability create_OF_2D16DCT_N1_OTUC4_ODUC4_Ctp_MoAttributePathCapability() {
    List<MoAttributePathCapability> attributes = List.of(
            new MoAttributePathCapability("modulation", "otsia/#otsi={id}/ochcfg"),
            new MoAttributePathCapability("fec-type", "otuc4"),
            new MoAttributePathCapability("termination-mode", "")
    );
    return new MoClassCapability("ctp", "/ctp/otuc4/ctp/oduc4", attributes);
  }

  private MoClassCapability create_OF_2D16DCT_N1_Ptp_MoAttributePathCapability() {
    List<MoAttributePathCapability> attributes = List.of(
            new MoAttributePathCapability("tuned-frequency", "opt"),
            new MoAttributePathCapability("opt-setpoint", "otp")
    );
    return new MoClassCapability("ptp", "/ptp/nw,{portId}", attributes);
  }

  private MoClassCapability create_OF_2D16DCT_C1_Ptp_MoAttributePathCapability(){
    List<MoAttributePathCapability> attributes = List.of(
            new MoAttributePathCapability("tuned-frequency", "opt"),
            new MoAttributePathCapability("fts-control", "dsbld")
    );
    return new MoClassCapability("ptp", "/ptp/cl,{portId}", attributes);
  }
  List<OpticalParameters.SelectedParameter> convertProtoParametersToOptical(List<MapOuterClass.KeyValuePair> cpcParameters) {
    return cpcParameters.stream()
                        .map(cpcParam -> new OpticalParameters.KeyValuePair(cpcParam.getK(), cpcParam.getV()))
                        .map(OpticalParameters::fromKeyValuePair)
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .toList();
  }
}
