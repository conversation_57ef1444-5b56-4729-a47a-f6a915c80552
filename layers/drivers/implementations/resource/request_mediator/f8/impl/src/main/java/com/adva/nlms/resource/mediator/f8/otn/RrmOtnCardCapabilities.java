/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.infrastructure.capability.moprovider.api.MoEpteInquiryObject;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.opticalparameters.api.Values;

import java.util.List;
import java.util.Optional;

interface RrmOtnCardCapabilities {
  boolean isPortClient(int neId, CardRef cardRef, PlugRef plugRef, Aid ptpAid);

  List<Values.PortPoolDescriptor.PortSetDescriptor> getPortSetDescriptors(int neId, CardRef cardRef, PlugRef plugRef);

  Optional<String> getGroupPort(String moduleType, String portIdentifier);

  Optional<String> getProtectionRestrictionPort(int neId, CardRef cardRef, PlugRef plugRef, Aid ptpAid, String layer);

  MoEpteInquiryObject buildMoEpteInquiryObject(int neId, CardRef cardRef);
}
