/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */
package com.adva.nlms.resource.mediator.f8.eth;

import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.provision.f8.api.in.MoCimParameter;
import com.adva.nlms.resource.provision.f8.api.in.Provision;

import java.util.Objects;
import java.util.Set;

import static com.adva.nlms.resource.mediator.f8.events.ProvisionEventsCreator.ptpAdopted;

class PtpParamsRequest implements Request {
  private final int neId;
  private final String uri;
  private final boolean exists;
  private final Set<MoCimParameter> params;

  private final Provision provisionApi;


  PtpParamsRequest(int neId, String uri, boolean exists, Set<MoCimParameter> params, Provision provisionApi) {
    this.neId = neId;
    this.uri = uri;
    this.exists = exists;
    this.params = params;
    this.provisionApi = provisionApi;
  }

  @Override
  public ProvisionLocalEvent provision() {
    updatePtpParams();
    return ptpAdopted(uri);
  }

  @Override
  public void delete() {
    // FNMD-126301 - When deleting we need to set PTP's admin state to Auto In Service
    provisionApi.updateAdminState(NetworkElementID.create(neId), uri, AdminState.AUTOMATIC_IN_SERVICE);
  }

  @Override
  public ProvisionLocalEvent adopt() {
    return null;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    PtpParamsRequest that = (PtpParamsRequest) o;
    return neId == that.neId && Objects.equals(uri, that.uri) && exists == that.exists &&
      Objects.equals(params, that.params) && Objects.equals(provisionApi, that.provisionApi);
  }

  @Override
  public int hashCode() {
    return Objects.hash(neId, uri, exists, params, provisionApi);
  }

  @Override
  public String toString() {
    return "PtpParamsRequest{" +
            "neId=" + neId +
            ", uri='" + uri + '\'' +
            ", exists=" + exists +
            ", params=" + params +
            ", provisionApi=" + provisionApi +
            '}';
  }

  private void updatePtpParams() {
    if (params.isEmpty()) {
      return;
    }
    var provNeId = NetworkElementID.create(neId);
    provisionApi.updateAdminState(provNeId, uri, AdminState.DOWN);
    provisionApi.updatePtp(provNeId, uri, params);
    provisionApi.updateAdminState(provNeId, uri, AdminState.UP);
  }
}
