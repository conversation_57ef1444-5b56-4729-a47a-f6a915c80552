/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.eth;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.moprovider.api.MoEpteInquiryObject;
import com.adva.infrastructure.capability.provider.api.CapabilityInquiryObjectBuilder;
import com.adva.infrastructure.capability.provider.api.CapabilityProvider;
import com.adva.nlms.mediation.mo.inventory.resources.PortIdExtractor;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.opticalparameters.api.Values;
import com.adva.nlms.opticalparameters.api.enums.ParameterName;

import java.util.List;

class RrmEthCardCapabilitiesImpl implements RrmEthCardCapabilities {
  private final CapabilityProvider capabilityProvider;
  private final MoCapabilityProvider moCapabilityProvider;
  private final NEDataProvider neDataProvider;

  public RrmEthCardCapabilitiesImpl(CapabilityProvider capabilityProvider, MoCapabilityProvider moCapabilityProvider,
                                    NEDataProvider neDataProvider) {
    this.capabilityProvider = capabilityProvider;
    this.moCapabilityProvider = moCapabilityProvider;
    this.neDataProvider = neDataProvider;
  }

  @Override
  public boolean isEthCardSupported(String cardType) {
    return moCapabilityProvider.isCardSupported(cardType);
  }

  @Override
  public List<Values.PortPoolDescriptor.PortSetDescriptor> getPortSetDescriptors(int neId, String cardType, String plugType, String portId) {
    var neData = neDataProvider.getNeData(neId);
    var capio = CapabilityInquiryObjectBuilder.newBuilder()
        .elementType(neData.getNetworkElementTypeString())
        .moduleType(cardType)
        .plugType(plugType)
        .portIdentifier(PortIdExtractor.getPortId(portId))
        .elementSwVersion(neData.getSwVersion())
        .build();
    var result = capabilityProvider.getParameter(ParameterName.POOL_RESTRICTION, capio, "_PORT");
    if (result != null && result.values() instanceof Values.PortPoolDescriptor portPoolDescriptor) {
      return portPoolDescriptor.values();
    }
    return List.of();
  }

  @Override
  public boolean isEpteSupportedOnCard(int neId, String cardType, String cardMode) {
    var neData = neDataProvider.getNeData(neId);
    var io = new MoEpteInquiryObject(neData.getSwVersion(), cardType, cardMode);
    return moCapabilityProvider.isEpteSupported(io);
  }
}
