/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.mediation.config.f8.croma.api.CromaMOService;
import com.adva.nlms.mediation.config.f8.croma.provision.api.SlcEqualizationDirection;
import com.adva.nlms.mediation.config.f8.croma.slc.api.Slc;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.CtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.resource.crm.model.CrmCtrlTaskScheduler;
import com.adva.nlms.resource.crm.model.wdm.CrmWdmModelDAO;
import com.adva.nlms.resource.crm.model.wdm.LtpInfo;
import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;
import com.adva.nlms.resource.mediator.f8.SegmentRequestFailedException;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.CrmNodePosition;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.RrmSegmentRepository;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.WdmSegment;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.assertj.core.api.Assertions;
import org.assertj.core.api.ThrowableAssert;
import org.assertj.core.groups.Tuple;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class WdmSegmentSetAdminStateRequestTest {

  private static final String OTSIA_3_3_N = "/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/nw,1/ctp/oms/ctp/spslg-1/ctp/otsia";
  private static final String OTSIA_3_3_C1 = "/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/cl,1/ctp/oms/ctp/spslg-1/ctp/otsia";
  private static final String OTSIA_2_2_N = "/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/nw,1/ctp/oms/ctp/spslg-1/ctp/otsia";
  private static final String OTSIA_2_2_C1 = "/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/cl,1/ctp/oms/ctp/spslg-1/ctp/otsia";
  private static final String DEGREE_1 = "/mit/me/1/croma/degree/1";
  private static final String DEGREE_2 = "/mit/me/1/croma/degree/2";
  private static final String PORT_2_2_N_AID = "Port-2/2/n";
  private static final String PORT_3_3_N_AID = "Port-3/3/n";
  private static final String TTP_AID = "Port-1/1/n1";
  private static final String OMS_3_3_C1 = "/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/cl,1/ctp/oms";

  @Mock
  private RrmSegmentRepository rrmSegmentRepository;
  @Mock
  private CromaMOService cromaMOService;
  @Mock
  private MonitoredEntityManager monitoredEntityManager;
  @Mock
  private CrmWdmModelDAO crmWdmModelDAO;
  @Mock
  private CtpResources ctpResources;
  @Mock
  private PtpResources ptpResources;
  @Mock
  private CrmCtrlTaskScheduler crmCtrlTaskScheduler;
  @Mock
  private Provision  provision;

  @Captor
  ArgumentCaptor<MonitoredEntityKey> keyCaptor;
  @Captor
  ArgumentCaptor<MonitoredEntity> entityCaptor;

  @InjectMocks
  private SegmentSetAdminStateRequest sut;

  private CrmSegmentRequestDto request;

  @BeforeEach
  public void beforeEach(){
    request = new CrmSegmentRequestDto();
  }

  @Test
  void contextIsNull() {
    request.adminState = CrmSegmentRequestDto.AdminState.ENABLED;
    request.id = "4";
    request.neId = 99;
    when(rrmSegmentRepository.getSegment(request.id, request.neId))
      .thenReturn(WdmSegment.newSegment().build());
    var actual = Assertions.assertThatThrownBy(() -> sut.execute(request, null));
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("Context is null");
  }

  @Test
  void contextIsOfInvalidType() {
    request.adminState = CrmSegmentRequestDto.AdminState.ENABLED;
    request.id = "4";
    request.neId = 99;
    when(rrmSegmentRepository.getSegment(request.id, request.neId))
      .thenReturn(WdmSegment.newSegment().build());
    var context = new ProvisionContext(null, null, null, 0, 0, null, null, false, null);
    var actual = Assertions.assertThatThrownBy(() -> sut.execute(request, context));
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("Invalid Context type ProvisionContext");
  }

  @DisplayName("""
    GIVEN: setAdminStateRequest with null admin state
    WHEN: execute() invoked with predefined request
    THEN: SegmentRequestFailedException is thrown
    """)
  @Test
  void executeNullAdminState() {
    //WHEN
    ThrowableAssert.ThrowingCallable actual = () -> sut.execute(request, null);

    //THEN
    Assertions.assertThatThrownBy(actual)
      .isInstanceOf(SegmentRequestFailedException.class);
  }

  @DisplayName("""
    GIVEN: setAdminStateRequest with invalid admin state
    WHEN: execute() invoked with predefined request
    THEN: SegmentRequestFailedException is thrown
    """)
  @Test
  void executeInvalidAdminState() {
    //GIVEN
    request.adminState = CrmSegmentRequestDto.AdminState.UNRECOGNIZED;
    //WHEN
    ThrowableAssert.ThrowingCallable actual = () -> sut.execute(request, null);

    //THEN
    Assertions.assertThatThrownBy(actual)
      .isInstanceOf(SegmentRequestFailedException.class);
  }

  @DisplayName("""
    GIVEN: setAdminStateRequest with DISABLED admin state
    WHEN: execute() invoked with predefined request
    THEN: monitored entities removed for neId and segment request Id
    """)
  @Test
  void executeDisabledAdminState() {
    //GIVEN
    request.adminState = CrmSegmentRequestDto.AdminState.DISABLED;
    request.id = "4";
    request.neId = 99;
    //WHEN
    var actual = sut.execute(request, null);
    //THEN
    Assertions.assertThat(actual)
      .isNotNull()
      .extracting(SegmentRequestOperationRet::operationCompleted)
      .isEqualTo(true);

    verify(monitoredEntityManager, times(1))
      .removeEntities(request.neId, request.id);
  }

  @DisplayName("""
    GIVEN: setAdminStateRequest with DISABLED admin state and BLOCKING_SIGNAL flag set
    WHEN: execute() invoked with predefined request
    THEN: SLC admin state DOWN on both directions
    """)
  @Test
  void executeDisabledAdminStateWithBlockingSignal() {
    //GIVEN
    request.adminState = CrmSegmentRequestDto.AdminState.DISABLED;
    request.id = "6";
    request.neId = 11;
    request.setBlockingSignal(true);
    int slcId = 3;

    final WdmSegment segment = WdmSegment.newSegment()
      .withNeId(request.neId )
      .withSegmentRequestId(request.id)
      .withSlcId(slcId)
      .build();

    when(rrmSegmentRepository.getSegment(request.id, request.neId))
      .thenReturn(segment);
    //WHEN
    var actual = sut.execute(request, null);
    //THEN
    Assertions.assertThat(actual)
      .isNotNull()
      .extracting(SegmentRequestOperationRet::operationCompleted)
      .isEqualTo(true);

    verify(provision, times(1)).updateSLCAdminState(request.neId, slcId, SlcEqualizationDirection.PATH_AZ, AdminState.DOWN);
    verify(provision, times(1)).updateSLCAdminState(request.neId, slcId, SlcEqualizationDirection.PATH_ZA, AdminState.DOWN);
  }

  @DisplayName("""
    GIVEN: setAdminStateRequest with ENABLED admin state and segment id
    WHEN: execute() invoked with predefined request
    THEN: SegmentRequestFailedException is thrown
    """)
  @Test
  void buildContextENABLEDAdminStateWhenSegmentNotFound() {
    //GIVEN
    request.adminState = CrmSegmentRequestDto.AdminState.ENABLED;
    request.id = "4";
    request.neId = 99;
    //WHEN
    ThrowableAssert.ThrowingCallable actual = () -> sut.buildContext(request);

    //THEN
    Assertions.assertThatThrownBy(actual)
      .isInstanceOf(SegmentRequestFailedException.class);

    verifyNoInteractions(monitoredEntityManager);
    verify(rrmSegmentRepository, times(1))
      .getSegment(request.id, request.neId);
  }

  @DisplayName("""
    GIVEN: setAdminStateRequest with ENABLED admin state and segment id
    WHEN: execute() invoked with predefined request
    THEN: SegmentRequestFailedException is thrown
    """)
  @Test
  void executeENABLEDAdminStateWhenSegmentNotFound() {
    //GIVEN
    request.adminState = CrmSegmentRequestDto.AdminState.ENABLED;
    request.id = "4";
    request.neId = 99;
    //WHEN
    ThrowableAssert.ThrowingCallable actual = () -> sut.execute(request, null);

    //THEN
    Assertions.assertThatThrownBy(actual)
      .isInstanceOf(SegmentRequestFailedException.class);

    verifyNoInteractions(monitoredEntityManager);
    verify(rrmSegmentRepository, times(1))
      .getSegment(request.id, request.neId);
  }

  @DisplayName("""
    GIVEN: setAdminStateRequest with ENABLED admin state, segmentId, neId and SLC with line card
    WHEN: execute() invoked with predefined request
    THEN: result: success, single monitored entity collected of type LINE_CARD with ptp uri
    """)
  @Test
  void executeENABLEDAdminStateSegmentWithLineCard() {
    //GIVEN
    final int neId = 99;
    request.adminState = CrmSegmentRequestDto.AdminState.ENABLED;
    request.id = "4";
    request.neId = neId;

    final Uri transponderCtpUri = new Uri("/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/nw,1/ctp/ot200");
    final Uri transponderPtpUri = new Uri("/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/nw,1");
    final PtpRef transponderPtp = new PtpRef(neId, new Aid(PORT_3_3_N_AID), transponderPtpUri, null);
    final WdmSegment segment = WdmSegment.newSegment()
      .withNeId(neId)
      .withSegmentRequestId(request.id)
      .withAidSrcTp(PORT_2_2_N_AID)
      .withAidDstTp(PORT_3_3_N_AID)
      .withTransponderCtpUri(transponderCtpUri.uri())
      .build();

    when(rrmSegmentRepository.getSegment(request.id, neId))
      .thenReturn(segment);
    when(ctpResources.findPort(neId, transponderCtpUri))
      .thenReturn(Optional.of(transponderPtp));

    //WHEN
    var context = sut.buildContext(request);
    var actual = sut.execute(request, context);
    //THEN
    Assertions.assertThat(actual)
      .isNotNull()
      .extracting(SegmentRequestOperationRet::operationCompleted)
      .isEqualTo(true);

    verify(monitoredEntityManager, times(2))
      .addEntity(keyCaptor.capture(), entityCaptor.capture());
    verifyNoMoreInteractions(monitoredEntityManager);

    verify(ctpResources, times(1))
      .findPort(neId, transponderCtpUri);

    verify(crmCtrlTaskScheduler, times(1))
      .submitTask(any());

    Assertions.assertThat(keyCaptor.getValue())
      .extracting(MonitoredEntityKey::neId, MonitoredEntityKey::entityUri)
      .containsExactly(neId, transponderPtpUri.uri());
    Assertions.assertThat(entityCaptor.getAllValues())
      .extracting(MonitoredEntity::segmentRequestId, MonitoredEntity::entityUri, MonitoredEntity::equipmentType, MonitoredEntity::terminationPointPair)
      .containsExactly(Tuple.tuple(request.id,
          transponderCtpUri.uri(),
          MonitoredEntity.EquipmentType.LINE_CARD,
          MonitoredEntity.TerminationPointPair.of(null, segment.getAidSrcTp())),
        Tuple.tuple(request.id,
          transponderPtpUri.uri(),
          MonitoredEntity.EquipmentType.LINE_CARD,
          MonitoredEntity.TerminationPointPair.of(null, segment.getAidSrcTp()))
      );
  }

  @DisplayName("""
    GIVEN: setAdminStateRequest with ENABLED admin state, segmentId, neId and SLC on line card and ROADM
    WHEN: execute() invoked with predefined request
    THEN: result: success, 3 monitored entity collected, one of type LINE_CARD and 2 of type ROADM
    """)
  @Test
  void executeENABLEDAdminStateSegmentWithLineCardAndROADM() {
    //GIVEN
    final int neId = 99;
    final int slcId = 4;
    final String segmentSrcTPAid = PORT_2_2_N_AID;
    final String segmentDstTPAid = PORT_3_3_N_AID;
    request.adminState = CrmSegmentRequestDto.AdminState.ENABLED;
    request.id = "4";
    request.neId = neId;

    final Uri transponderCtpUri = new Uri("/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/nw,1/ctp/ot200");
    final Uri transponderPtpUri = new Uri("/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/nw,1");
    final PtpRef transponderPtp = new PtpRef(neId, new Aid(segmentDstTPAid), transponderPtpUri, null);
    final WdmSegment segment = WdmSegment.newSegment()
      .withNeId(neId)
      .withSegmentRequestId(request.id)
      .withSlcId(slcId)
      .withAidSrcTp(segmentSrcTPAid)
      .withAidDstTp(segmentDstTPAid)
      .withTransponderCtpUri(transponderCtpUri.uri())
      .build();

    final Slc slc = SlcTestBuilder.newBuilder()
      .withIdentifier(slcId)
      .withNeId(neId)
      .aEndpoint()
      .withAdminState(1)
      .withOperation(1)
      .activeEndpoint()
      .withResourceInstance(DEGREE_1)
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/cl,1/ctp/oms")
      .addInventoryElement(OTSIA_2_2_C1)
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/nw,1/ctp/oms")
      .addInventoryElement(OTSIA_2_2_N)
      .buildActiveEndpoint()
      .buildEndpoint()
      .zEndpoint()
      .withAdminState(1)
      .withOperation(2)
      .activeEndpoint()
      .withResourceInstance(transponderCtpUri.uri())
      .addInventoryElement(transponderCtpUri.uri())
      .buildActiveEndpoint()
      .buildEndpoint()
      .build();
    when(cromaMOService.getSlcForNeAndSlcId(neId, slcId))
      .thenReturn(Optional.of(slc));
    when(crmWdmModelDAO.getLtpFromAid(segment.getNeId(), segment.getAidSrcTp()))
      .thenReturn(Optional.of(new LtpInfo(segmentSrcTPAid, null, null, 1, false, null, null)));
    when(rrmSegmentRepository.getSegment(request.id, neId))
      .thenReturn(segment);
    when(ctpResources.findPort(neId, transponderCtpUri))
      .thenReturn(Optional.of(transponderPtp));

    //WHEN
    var context = sut.buildContext(request);
    var actual = sut.execute(request, context);
    //THEN
    Assertions.assertThat(actual)
      .isNotNull()
      .extracting(SegmentRequestOperationRet::operationCompleted)
      .isEqualTo(true);

    verify(monitoredEntityManager, times(4))
      .addEntity(keyCaptor.capture(), entityCaptor.capture());
    verifyNoMoreInteractions(monitoredEntityManager);

    verify(ctpResources, times(1))
      .findPort(neId, transponderCtpUri);

    verify(crmCtrlTaskScheduler, times(1))
      .submitTask(any());

    Assertions.assertThat(keyCaptor.getAllValues())
      .extracting(MonitoredEntityKey::neId, MonitoredEntityKey::entityUri)
      .containsExactly(Tuple.tuple(neId, OTSIA_2_2_C1), Tuple.tuple(neId, OTSIA_2_2_N), Tuple.tuple(neId, transponderCtpUri.uri()), Tuple.tuple(neId, transponderPtpUri.uri()));
    Assertions.assertThat(entityCaptor.getAllValues())
      .extracting(MonitoredEntity::segmentRequestId, MonitoredEntity::entityUri, MonitoredEntity::equipmentType, MonitoredEntity::terminationPointPair)
      .containsExactly(Tuple.tuple(request.id,
                                   OTSIA_2_2_C1,
                                   MonitoredEntity.EquipmentType.ROADM,
                                   MonitoredEntity.TerminationPointPair.of(segmentSrcTPAid, null)),
                       Tuple.tuple(request.id,
                                   OTSIA_2_2_N,
                                   MonitoredEntity.EquipmentType.ROADM,
                                   MonitoredEntity.TerminationPointPair.of(null, segmentSrcTPAid)),
                       Tuple.tuple(request.id,
                                   transponderCtpUri.uri(),
                                   MonitoredEntity.EquipmentType.LINE_CARD,
                                   MonitoredEntity.TerminationPointPair.of(null, segmentSrcTPAid)),
                       Tuple.tuple(request.id,
                                   transponderPtpUri.uri(),
                                   MonitoredEntity.EquipmentType.LINE_CARD,
                                   MonitoredEntity.TerminationPointPair.of(null, segmentSrcTPAid)
                       ));
  }

  @DisplayName("""
    GIVEN: setAdminStateRequest with ENABLED admin state, neId, segmentId with existing slc on two ROADMS when slc's aEnd degree has segment's srcTpAid
    WHEN: execute() invoked with predefined request
    THEN: 4 monitored entities collected
    """)
  @Test
  void executeENABLEDAdminStateSlcOnTwoRoadms() {
    //GIVEN
    final int slcId = 5;
    final int neId = 9;
    final String segmentSrcTPAid = PORT_2_2_N_AID;
    final String segmentDstTPAid = PORT_3_3_N_AID;
    request.adminState = CrmSegmentRequestDto.AdminState.ENABLED;
    request.id = "4";
    request.neId = neId;

    final WdmSegment segment = WdmSegment.newSegment()
      .withNeId(neId)
      .withSegmentRequestId(request.id)
      .withSlcId(slcId)
      .withAidSrcTp(segmentSrcTPAid)
      .withAidDstTp(segmentDstTPAid)
      .build();
    when(rrmSegmentRepository.getSegment(request.id, neId))
      .thenReturn(segment);

    final Slc slc = SlcTestBuilder.newBuilder()
      .withIdentifier(slcId)
      .withNeId(neId)
      .aEndpoint()
      .withAdminState(1)
      .withOperation(1)
      .activeEndpoint()
      .withResourceInstance(DEGREE_1)
      .addInventoryElement(OMS_3_3_C1)
      .addInventoryElement("/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/cl,1/ctp/oms/ctp/spslg-1")
      .addInventoryElement(OTSIA_3_3_C1)
      .addInventoryElement("/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/nw,1/ctp/oms")
      .addInventoryElement("/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/nw,1/ctp/oms/ctp/spslg-1")
      .addInventoryElement(OTSIA_3_3_N)
      .addInventoryElement("3/3/media/1")
      .buildActiveEndpoint()
      .buildEndpoint()
      .zEndpoint()
      .withAdminState(1)
      .withOperation(2)
      .activeEndpoint()
      .withResourceInstance(DEGREE_2)
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/cl,1/ctp/oms")
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/cl,1/ctp/oms/ctp/spslg-1")
      .addInventoryElement(OTSIA_2_2_C1)
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/nw,1/ctp/oms")
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/nw,1/ctp/oms/ctp/spslg-1")
      .addInventoryElement(OTSIA_2_2_N)
      .buildActiveEndpoint()
      .buildEndpoint()
      .build();
    when(cromaMOService.getSlcForNeAndSlcId(neId, slcId))
      .thenReturn(Optional.of(slc));
    when(crmWdmModelDAO.getLtpFromAid(segment.getNeId(), segment.getAidSrcTp()))
      .thenReturn(Optional.of(new LtpInfo(segmentSrcTPAid, null, null, 1, false, null, null)));


    //WHEN
    var context = sut.buildContext(request);
    var actual = sut.execute(request, context);
    //THEN
    Assertions.assertThat(actual)
      .isNotNull()
      .extracting(SegmentRequestOperationRet::operationCompleted)
      .isEqualTo(true);

    verify(monitoredEntityManager, times(4))
      .addEntity(keyCaptor.capture(), entityCaptor.capture());
    verifyNoMoreInteractions(monitoredEntityManager);

    List<MonitoredEntity> expectedEntities = List.of(MonitoredEntityBuilder.newBuilder()
                                                       .segmentRequestId(request.id)
                                                       .entityUri(OTSIA_3_3_N)
                                                       .txAid(segmentDstTPAid)
                                                       .rxAid(segmentSrcTPAid)
                                                       .equipmentType(MonitoredEntity.EquipmentType.ROADM)
                                                       .build(),
                                                     MonitoredEntityBuilder.newBuilder()
                                                       .segmentRequestId(request.id)
                                                       .entityUri(OTSIA_3_3_C1)
                                                       .txAid(segmentSrcTPAid)
                                                       .rxAid(segmentDstTPAid)
                                                       .equipmentType(MonitoredEntity.EquipmentType.ROADM)
                                                       .build(),
                                                     MonitoredEntityBuilder.newBuilder()
                                                       .segmentRequestId(request.id)
                                                       .entityUri(OTSIA_2_2_N)
                                                       .txAid(segmentSrcTPAid)
                                                       .rxAid(segmentDstTPAid)
                                                       .equipmentType(MonitoredEntity.EquipmentType.ROADM)
                                                       .build(),
                                                     MonitoredEntityBuilder.newBuilder()
                                                       .segmentRequestId(request.id)
                                                       .entityUri(OTSIA_2_2_C1)
                                                       .txAid(segmentDstTPAid)
                                                       .rxAid(segmentSrcTPAid)
                                                       .equipmentType(MonitoredEntity.EquipmentType.ROADM)
                                                       .build());
    Assertions.assertThat(keyCaptor.getAllValues())
      .containsExactlyInAnyOrderElementsOf(expectedEntities.stream()
                                             .map(e -> e.toKey(neId))
                                             .toList());

    Assertions.assertThat(entityCaptor.getAllValues())
      .containsExactlyInAnyOrderElementsOf(expectedEntities);

    verify(crmCtrlTaskScheduler, times(1))
      .submitTask(any());
  }

  @DisplayName("""
    GIVEN: setAdminStateRequest with ENABLED admin state, neId, segmentId with existing slc on two ROADMS when slc's aEnd degree has segment's srcTpAid and no Degree on zEnd
    WHEN: execute() invoked with predefined request
    THEN: 5 monitored entities collected
    """)
  @Test
  void executeENABLEDAdminStateSlcOnTwoRoadmsWithoutZEndDegree() {
    //GIVEN
    final int slcId = 5;
    final int neId = 9;
    final String segmentSrcTPAid = PORT_2_2_N_AID;
    final String segmentDstTPAid = PORT_3_3_N_AID;
    request.adminState = CrmSegmentRequestDto.AdminState.ENABLED;
    request.id = "4";
    request.neId = neId;

    final WdmSegment segment = WdmSegment.newSegment()
      .withNeId(neId)
      .withSegmentRequestId(request.id)
      .withSlcId(slcId)
      .withAidSrcTp(segmentSrcTPAid)
      .withAidDstTp(segmentDstTPAid)
      .withAlienWaveCtpUri("/mit/me/1/eqh/shelf,20/eqh/slot,alien/eq/card/ptp/alien,1/ctp/otsia")
      .build();
    when(rrmSegmentRepository.getSegment(request.id, neId))
      .thenReturn(segment);

    final Slc slc = SlcTestBuilder.newBuilder()
      .withIdentifier(slcId)
      .withNeId(neId)
      .aEndpoint()
      .withAdminState(1)
      .withOperation(1)
      .activeEndpoint()
      .withResourceInstance(DEGREE_1)
      .addInventoryElement(OMS_3_3_C1)
      .addInventoryElement("/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/cl,1/ctp/oms/ctp/spslg-1")
      .addInventoryElement(OTSIA_3_3_C1)
      .addInventoryElement("/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/nw,1/ctp/oms")
      .addInventoryElement("/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/nw,1/ctp/oms/ctp/spslg-1")
      .addInventoryElement(OTSIA_3_3_N)
      .addInventoryElement("3/3/media/1")
      .buildActiveEndpoint()
      .buildEndpoint()
      .zEndpoint()
      .withAdminState(1)
      .withOperation(2)
      .activeEndpoint()
      .withResourceInstance("/mit/me/1/eqh/shelf,2/eqh/slot,alien/eq/card/ptp/alien,1/ctp/otsia")
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/cl,1/ctp/oms")
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/cl,1/ctp/oms/ctp/spslg-1")
      .addInventoryElement(OTSIA_2_2_C1)
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/nw,1/ctp/oms")
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/nw,1/ctp/oms/ctp/spslg-1")
      .addInventoryElement(OTSIA_2_2_N)
      .buildActiveEndpoint()
      .buildEndpoint()
      .build();
    when(cromaMOService.getSlcForNeAndSlcId(neId, slcId))
      .thenReturn(Optional.of(slc));
    when(crmWdmModelDAO.getLtpFromAid(segment.getNeId(), segment.getAidSrcTp()))
      .thenReturn(Optional.of(new LtpInfo(segmentSrcTPAid, null, null, 1, false, null, null)));
    when(ptpResources.findAllCtps(segment.getNeId(), new Aid(segment.getTtpAid())))
      .thenReturn(List.of(new CtpRef(neId, new Aid("OMS-3/3/c1/oms"), new Uri(OMS_3_3_C1), null)));

    //WHEN
    var context = sut.buildContext(request);
    var actual = sut.execute(request, context);
    //THEN
    Assertions.assertThat(actual)
      .isNotNull()
      .extracting(SegmentRequestOperationRet::operationCompleted)
      .isEqualTo(true);

    verify(monitoredEntityManager, times(5))
      .addEntity(keyCaptor.capture(), entityCaptor.capture());
    verifyNoMoreInteractions(monitoredEntityManager);

    List<MonitoredEntity> expectedEntities = List.of(
      MonitoredEntityBuilder.newBuilder()
        .segmentRequestId(request.id)
        .entityUri(OTSIA_3_3_C1)
        .txAid(segmentSrcTPAid)
        .equipmentType(MonitoredEntity.EquipmentType.ROADM)
        .build(),
      MonitoredEntityBuilder.newBuilder()
        .segmentRequestId(request.id)
        .entityUri(OTSIA_3_3_N)
        .rxAid(segmentSrcTPAid)
        .equipmentType(MonitoredEntity.EquipmentType.ROADM)
        .build(),
      MonitoredEntityBuilder.newBuilder()
        .segmentRequestId(request.id)
        .entityUri(OTSIA_2_2_C1)
        .txAid(segmentSrcTPAid)
        .equipmentType(MonitoredEntity.EquipmentType.ROADM)
        .build(),
      MonitoredEntityBuilder.newBuilder()
        .segmentRequestId(request.id)
        .entityUri(OTSIA_2_2_N)
        .rxAid(segmentSrcTPAid)
        .equipmentType(MonitoredEntity.EquipmentType.ROADM)
        .build(),
      MonitoredEntityBuilder.newBuilder()
        .segmentRequestId(request.id)
        .entityUri(OMS_3_3_C1)
        .rxAid(segmentDstTPAid)
        .equipmentType(MonitoredEntity.EquipmentType.ALIEN)
        .build()
      );
    Assertions.assertThat(keyCaptor.getAllValues())
      .containsExactlyInAnyOrderElementsOf(expectedEntities.stream()
        .map(e -> e.toKey(neId))
        .toList());

    Assertions.assertThat(entityCaptor.getAllValues())
      .containsExactlyInAnyOrderElementsOf(expectedEntities);

    verify(crmCtrlTaskScheduler, times(1))
      .submitTask(any());
  }


  @DisplayName("""
    GIVEN: setAdminStateRequest with ENABLED admin state, neId, segmentId with existing slc but no endpoints
    WHEN: execute() invoked with predefined request
    THEN: IllegalArgumentException is thrown
    """)
  @Test
  void executeENABLEDAdminStateSlcWithNoAEndpoints() {
    //GIVEN
    final int neId = 99;
    final int slcId = 5;
    request.adminState = CrmSegmentRequestDto.AdminState.ENABLED;
    request.id = "4";
    request.neId = neId;

    final WdmSegment segment = WdmSegment.newSegment()
      .withNeId(neId)
      .withSegmentRequestId(request.id)
      .withSlcId(slcId)
      .withAidSrcTp(PORT_2_2_N_AID)
      .withAidDstTp(PORT_3_3_N_AID)
      .build();
    when(rrmSegmentRepository.getSegment(request.id, neId))
      .thenReturn(segment);

    final Slc slc = SlcTestBuilder.newBuilder()
      .withIdentifier(slcId)
      .withNeId(neId)
      .build();
    when(cromaMOService.getSlcForNeAndSlcId(neId, slcId))
      .thenReturn(Optional.of(slc));

    //WHEN
    var context = sut.buildContext(request);
    ThrowableAssert.ThrowingCallable actual = () -> sut.execute(request, context);

    //THEN
    Assertions.assertThatThrownBy(actual)
      .isInstanceOf(SegmentRequestFailedException.class);
  }

  @DisplayName("""
    GIVEN: setAdminStateRequest with ENABLED admin state, neId, segmentId with existing slc on ROADMS when slc's AEnd degree has segment's dstTPAid
    WHEN: execute() invoked with predefined request
    THEN: 4 monitored entities collected
    """)
  @Test
  void executeENABLEDAdminStateSlcOnTwoRoadmsWhenAEndIsDstTP() {
    //GIVEN
    final int slcId = 5;
    final int neId = 9;
    final String segmentSrcTPAid = PORT_2_2_N_AID;
    final String segmentDstTPAid = PORT_3_3_N_AID;
    request.adminState = CrmSegmentRequestDto.AdminState.ENABLED;
    request.id = "4";
    request.neId = neId;

    final WdmSegment segment = WdmSegment.newSegment()
      .withNeId(neId)
      .withSegmentRequestId(request.id)
      .withSlcId(slcId)
      .withAidSrcTp(segmentSrcTPAid)
      .withAidDstTp(segmentDstTPAid)
      .build();
    when(rrmSegmentRepository.getSegment(request.id, neId))
      .thenReturn(segment);

    final Slc slc = SlcTestBuilder.newBuilder()
      .withIdentifier(slcId)
      .withNeId(neId)
      .aEndpoint()
      .withAdminState(1)
      .withOperation(1)
      .activeEndpoint()
      .withResourceInstance(DEGREE_1)
      .addInventoryElement(OMS_3_3_C1)
      .addInventoryElement("/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/cl,1/ctp/oms/ctp/spslg-1")
      .addInventoryElement(OTSIA_3_3_C1)
      .addInventoryElement("/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/nw,1/ctp/oms")
      .addInventoryElement("/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/nw,1/ctp/oms/ctp/spslg-1")
      .addInventoryElement(OTSIA_3_3_N)
      .addInventoryElement("3/3/media/1")
      .buildActiveEndpoint()
      .buildEndpoint()
      .zEndpoint()
      .withAdminState(1)
      .withOperation(1)
      .activeEndpoint()
      .withResourceInstance(DEGREE_2)
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/cl,1/ctp/oms")
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/cl,1/ctp/oms/ctp/spslg-1")
      .addInventoryElement(OTSIA_2_2_C1)
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/nw,1/ctp/oms")
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/nw,1/ctp/oms/ctp/spslg-1")
      .addInventoryElement(OTSIA_2_2_N)
      .buildActiveEndpoint()
      .buildEndpoint()
      .build();
    when(cromaMOService.getSlcForNeAndSlcId(neId, slcId))
      .thenReturn(Optional.of(slc));
    when(crmWdmModelDAO.getLtpFromAid(segment.getNeId(), segment.getAidSrcTp()))
      .thenReturn(Optional.of(new LtpInfo(segmentSrcTPAid, null, null, 2, false, null, null)));

    //WHEN
    var context = sut.buildContext(request);
    var actual = sut.execute(request, context);
    //THEN

    Assertions.assertThat(actual)
      .isNotNull()
      .extracting(SegmentRequestOperationRet::operationCompleted)
      .isEqualTo(true);

    verify(monitoredEntityManager, times(4))
      .addEntity(keyCaptor.capture(), entityCaptor.capture());
    verifyNoMoreInteractions(monitoredEntityManager);

    List<MonitoredEntity> expectedEntities = List.of(MonitoredEntityBuilder.newBuilder()
                                                       .segmentRequestId(request.id)
                                                       .entityUri(OTSIA_3_3_N)
                                                       .txAid(segmentSrcTPAid)
                                                       .rxAid(segmentDstTPAid)
                                                       .equipmentType(MonitoredEntity.EquipmentType.ROADM)
                                                       .build(),
                                                     MonitoredEntityBuilder.newBuilder()
                                                       .segmentRequestId(request.id)
                                                       .entityUri(OTSIA_3_3_C1)
                                                       .txAid(segmentDstTPAid)
                                                       .rxAid(segmentSrcTPAid)
                                                       .equipmentType(MonitoredEntity.EquipmentType.ROADM)
                                                       .build(),
                                                     MonitoredEntityBuilder.newBuilder()
                                                       .segmentRequestId(request.id)
                                                       .entityUri(OTSIA_2_2_N)
                                                       .txAid(segmentDstTPAid)
                                                       .rxAid(segmentSrcTPAid)
                                                       .equipmentType(MonitoredEntity.EquipmentType.ROADM)
                                                       .build(),
                                                     MonitoredEntityBuilder.newBuilder()
                                                       .segmentRequestId(request.id)
                                                       .entityUri(OTSIA_2_2_C1)
                                                       .txAid(segmentSrcTPAid)
                                                       .rxAid(segmentDstTPAid)
                                                       .equipmentType(MonitoredEntity.EquipmentType.ROADM)
                                                       .build());

    Assertions.assertThat(keyCaptor.getAllValues())
      .containsExactlyInAnyOrderElementsOf(expectedEntities.stream()
                                             .map(e -> e.toKey(neId))
                                             .toList());

    Assertions.assertThat(entityCaptor.getAllValues())
      .containsExactlyInAnyOrderElementsOf(expectedEntities);

    verify(crmCtrlTaskScheduler, times(1))
      .submitTask(any());
  }

  @DisplayName("""
    GIVEN: setAdminStateRequest with ENABLED admin state, segmentId, neId and segment with alien wave connected to Splitter
    WHEN: execute() invoked with predefined request
    THEN: result: success, 3 monitored entity collected one of type ALIEN and 2 of type ROADM
    """)
  @Test
  void executeENABLEDAdminStateSegmentWithAlienWaveOnSplitter() {
    //GIVEN
    final int slcId = 5;
    final int neId = 9;
    final String segmentSrcTPAid = PORT_2_2_N_AID;
    final String segmentDstTPAid = "Port-3/1/c1";
    request.adminState = CrmSegmentRequestDto.AdminState.ENABLED;
    request.id = "4";
    request.neId = neId;

    final String alienWaveCtpUriString = "/mit/me/1/eqh/shelf,3/eqh/slot,alien/eq/card/ptp/alien,1/ctp/otsia";
    final String ptpUriString = "/mit/me/1/eqh/shelf,3/eqh/slot,1/eq/card/ptp/cl,1";
    final Uri ptpUri = new Uri(ptpUriString);
    final PtpRef ptp = new PtpRef(request.neId, new Aid(segmentDstTPAid), ptpUri, null);
    final WdmSegment segment = WdmSegment.newSegment()
      .withNeId(neId)
      .withSegmentRequestId(request.id)
      .withSlcId(slcId)
      .withAidSrcTp(segmentSrcTPAid)
      .withAidDstTp(segmentDstTPAid)
      .withAlienWaveCtpUri(alienWaveCtpUriString)
      .withTtpAid(TTP_AID)
      .build();

    when(rrmSegmentRepository.getSegment(request.id, request.neId))
      .thenReturn(segment);

    final Slc slc = SlcTestBuilder.newBuilder()
      .withIdentifier(slcId)
      .withNeId(neId)
      .aEndpoint()
      .withAdminState(1)
      .withOperation(1)
      .activeEndpoint()
      .withResourceInstance(DEGREE_1)
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/cl,1/ctp/oms")
      .addInventoryElement(OTSIA_2_2_C1)
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/nw,1/ctp/oms")
      .addInventoryElement(OTSIA_2_2_N)
      .buildActiveEndpoint()
      .buildEndpoint()
      .zEndpoint()
      .withAdminState(1)
      .withOperation(2)
      .activeEndpoint()
      .withResourceInstance("/mit/me/1/eqh/shelf,3/eqh/slot,alien/eq/card/ptp/alien,1/ctp/otsia")
      .addInventoryElement("/mit/me/1/eqh/shelf,3/eqh/slot,alien/eq/card/ptp/alien,1/ctp/otsia")
      .buildActiveEndpoint()
      .buildEndpoint()
      .build();
    when(cromaMOService.getSlcForNeAndSlcId(neId, slcId))
      .thenReturn(Optional.of(slc));
    when(crmWdmModelDAO.getLtpFromAid(segment.getNeId(), segment.getAidSrcTp()))
      .thenReturn(Optional.of(new LtpInfo(segmentSrcTPAid, null, null, 1, false, null, null)));
    when(ptpResources.findAllCtps(request.neId, new Aid(TTP_AID)))
      .thenReturn(List.of());
    when(ptpResources.findPtp(request.neId, new Aid(TTP_AID)))
      .thenReturn(Optional.of(ptp));

    //WHEN
    var context = sut.buildContext(request);
    var actual = sut.execute(request, context);

    //THEN
    Assertions.assertThat(actual)
      .isNotNull()
      .extracting(SegmentRequestOperationRet::operationCompleted)
      .isEqualTo(true);

    verify(monitoredEntityManager, times(3))
      .addEntity(keyCaptor.capture(), entityCaptor.capture());
    verifyNoMoreInteractions(monitoredEntityManager);

    verify(ptpResources, times(1))
      .findPtp(request.neId, new Aid(TTP_AID));
    verify(ptpResources)
      .findAllCtps(request.neId, new Aid(TTP_AID));
    verifyNoMoreInteractions(ptpResources);

    verify(crmCtrlTaskScheduler, times(1))
      .submitTask(any());

    Assertions.assertThat(keyCaptor.getAllValues())
      .extracting(MonitoredEntityKey::neId, MonitoredEntityKey::entityUri)
      .containsExactly(Tuple.tuple(neId, OTSIA_2_2_C1), Tuple.tuple(neId, OTSIA_2_2_N), Tuple.tuple(neId, ptpUriString));
    Assertions.assertThat(entityCaptor.getAllValues())
      .extracting(MonitoredEntity::segmentRequestId, MonitoredEntity::entityUri, MonitoredEntity::equipmentType, MonitoredEntity::terminationPointPair)
      .containsExactly(Tuple.tuple(request.id,
                                   OTSIA_2_2_C1,
                                   MonitoredEntity.EquipmentType.ROADM,
                                   MonitoredEntity.TerminationPointPair.of(segmentSrcTPAid, null)),
                       Tuple.tuple(request.id,
                                   OTSIA_2_2_N,
                                   MonitoredEntity.EquipmentType.ROADM,
                                   MonitoredEntity.TerminationPointPair.of(null, segmentSrcTPAid)),
                       Tuple.tuple(request.id,
                                   ptpUriString,
                                   MonitoredEntity.EquipmentType.ALIEN,
                                   MonitoredEntity.TerminationPointPair.of(null,
                                                                           ptp.aid()
                                                                             .aid())));
  }

  @DisplayName("""
    GIVEN: setAdminStateRequest with ENABLED admin state, segmentId, neId and segment with alien wave connected to ROADM
    WHEN: execute() invoked with predefined request
    THEN: result: success, single monitored entity collected of type ALIEN with oms uri
    """)
  @Test
  void executeENABLEDAdminStateSegmentWithAlienWaveOnROADM() {
    //GIVEN
    final int slcId = 5;
    final int neId = 9;
    final String segmentSrcTPAid = PORT_2_2_N_AID;
    final String segmentDstTPAid = "Port-2/2/c1";
    final String ctpDstTPAid ="OMS-2/2/c1/oms" ;
    request.adminState = CrmSegmentRequestDto.AdminState.ENABLED;
    request.id = "4";
    request.neId = neId;

    final String alienWaveCtpUriString = "/mit/me/1/eqh/shelf,2/eqh/slot,alien/eq/card/ptp/alien,1/ctp/otsia";
    final String ctpUriString = "/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/cl,1/ctp/oms";
    final Uri ctpUri = new Uri(ctpUriString);
    final CtpRef ctp = new CtpRef(request.neId, new Aid(ctpDstTPAid), ctpUri, null);
    final WdmSegment segment = WdmSegment.newSegment()
      .withNeId(neId)
      .withSegmentRequestId(request.id)
      .withSlcId(slcId)
      .withAidSrcTp(segmentSrcTPAid)
      .withAidDstTp(segmentDstTPAid)
      .withAlienWaveCtpUri(alienWaveCtpUriString)
      .withTtpAid(TTP_AID)
      .withNodePosition(CrmNodePosition.EGRESS)
      .build();

    when(rrmSegmentRepository.getSegment(request.id, request.neId))
      .thenReturn(segment);

    final Slc slc = SlcTestBuilder.newBuilder()
      .withIdentifier(slcId)
      .withNeId(neId)
      .aEndpoint()
      .withAdminState(1)
      .withOperation(1)
      .activeEndpoint()
      .withResourceInstance(DEGREE_1)
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/cl,1/ctp/oms")
      .addInventoryElement(OTSIA_2_2_C1)
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/nw,1/ctp/oms")
      .addInventoryElement(OTSIA_2_2_N)
      .buildActiveEndpoint()
      .buildEndpoint()
      .zEndpoint()
      .withAdminState(1)
      .withOperation(2)
      .activeEndpoint()
      .withResourceInstance("/mit/me/1/eqh/shelf,2/eqh/slot,alien/eq/card/ptp/alien,1/ctp/otsia")
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,alien/eq/card/ptp/alien,1/ctp/otsia")
      .buildActiveEndpoint()
      .buildEndpoint()
      .build();
    when(cromaMOService.getSlcForNeAndSlcId(neId, slcId))
      .thenReturn(Optional.of(slc));
    when(crmWdmModelDAO.getLtpFromAid(segment.getNeId(), segment.getAidSrcTp()))
      .thenReturn(Optional.of(new LtpInfo(segmentSrcTPAid, null, null, 1, false, null, null)));
    when(ptpResources.findAllCtps(request.neId, new Aid(TTP_AID)))
      .thenReturn(List.of(ctp));

    //WHEN
    var context = sut.buildContext(request);
    var actual = sut.execute(request, context);

    //THEN
    Assertions.assertThat(actual)
      .isNotNull()
      .extracting(SegmentRequestOperationRet::operationCompleted)
      .isEqualTo(true);

    verify(monitoredEntityManager, times(3))
      .addEntity(keyCaptor.capture(), entityCaptor.capture());
    verifyNoMoreInteractions(monitoredEntityManager);

    verify(ptpResources, times(1))
      .findAllCtps(request.neId, new Aid(TTP_AID));
    verifyNoMoreInteractions(ptpResources);

    verify(crmCtrlTaskScheduler, times(1))
      .submitTask(any());

    Assertions.assertThat(keyCaptor.getAllValues())
      .extracting(MonitoredEntityKey::neId, MonitoredEntityKey::entityUri)
      .containsExactly(Tuple.tuple(neId, OTSIA_2_2_C1), Tuple.tuple(neId, OTSIA_2_2_N), Tuple.tuple(neId, ctpUriString));
    Assertions.assertThat(entityCaptor.getAllValues())
      .extracting(MonitoredEntity::segmentRequestId, MonitoredEntity::entityUri, MonitoredEntity::equipmentType, MonitoredEntity::terminationPointPair)
      .containsExactly(Tuple.tuple(request.id,
                                   OTSIA_2_2_C1,
                                   MonitoredEntity.EquipmentType.ROADM,
                                   MonitoredEntity.TerminationPointPair.of(segmentSrcTPAid, null)),
                       Tuple.tuple(request.id,
                                   OTSIA_2_2_N,
                                   MonitoredEntity.EquipmentType.ROADM,
                                   MonitoredEntity.TerminationPointPair.of(null, segmentSrcTPAid)),
                       Tuple.tuple(request.id,
                                   ctpUriString,
                                   MonitoredEntity.EquipmentType.ALIEN,
                                   MonitoredEntity.TerminationPointPair.of(null, segmentDstTPAid))
      );
  }

  @DisplayName("""
    GIVEN: setAdminStateRequest with ENABLED admin state, segmentId, neId and segment with alien wave connected to ROADM and aEnd refers to dstTPAid
    WHEN: execute() invoked with predefined request
    THEN: result: success, single monitored entity collected of type ALIEN
    """)
  @Test
  void executeENABLEDAdminStateSegmentWithAlienWaveOnROADMaEndIsDstTpAid() {
    //GIVEN
    final int slcId = 5;
    final int neId = 9;
    final String segmentSrcTPAid ="Port-2/2/c1" ;
    final String segmentDstTPAid = PORT_2_2_N_AID;
    final String ctpSrcTPAid ="OMS-2/2/c1/oms" ;
    request.adminState = CrmSegmentRequestDto.AdminState.ENABLED;
    request.id = "4";
    request.neId = neId;

    final String alienWaveCtpUriString = "/mit/me/1/eqh/shelf,2/eqh/slot,alien/eq/card/ptp/alien,1/ctp/otsia";
    final String ctpUriString = "/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/cl,1/ctp/oms";
    final Uri ctpUri = new Uri(ctpUriString);
    final CtpRef ctp = new CtpRef(request.neId, new Aid(ctpSrcTPAid), ctpUri, null);
    final WdmSegment segment = WdmSegment.newSegment()
      .withNeId(neId)
      .withSegmentRequestId(request.id)
      .withSlcId(slcId)
      .withAidSrcTp(segmentSrcTPAid)
      .withAidDstTp(segmentDstTPAid)
      .withAlienWaveCtpUri(alienWaveCtpUriString)
      .withTtpAid(TTP_AID)
      .withNodePosition(CrmNodePosition.INGRESS)
      .build();

    when(rrmSegmentRepository.getSegment(request.id, request.neId))
      .thenReturn(segment);

    final Slc slc = SlcTestBuilder.newBuilder()
      .withIdentifier(slcId)
      .withNeId(neId)
      .aEndpoint()
      .withAdminState(1)
      .withOperation(1)
      .activeEndpoint()
      .withResourceInstance(DEGREE_1)
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/cl,1/ctp/oms")
      .addInventoryElement(OTSIA_2_2_C1)
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,2/eq/card/ptp/nw,1/ctp/oms")
      .addInventoryElement(OTSIA_2_2_N)
      .buildActiveEndpoint()
      .buildEndpoint()
      .zEndpoint()
      .withAdminState(1)
      .withOperation(2)
      .activeEndpoint()
      .withResourceInstance("/mit/me/1/eqh/shelf,2/eqh/slot,alien/eq/card/ptp/alien,1/ctp/otsia")
      .addInventoryElement("/mit/me/1/eqh/shelf,2/eqh/slot,alien/eq/card/ptp/alien,1/ctp/otsia")
      .buildActiveEndpoint()
      .buildEndpoint()
      .build();
    when(cromaMOService.getSlcForNeAndSlcId(neId, slcId))
      .thenReturn(Optional.of(slc));
    when(crmWdmModelDAO.getLtpFromAid(segment.getNeId(), segment.getAidSrcTp()))
      .thenReturn(Optional.empty());
    when(ptpResources.findAllCtps(request.neId, new Aid(TTP_AID)))
      .thenReturn(List.of(ctp));

    //WHEN
    var context = sut.buildContext(request);
    var actual = sut.execute(request, context);

    //THEN
    Assertions.assertThat(actual)
      .isNotNull()
      .extracting(SegmentRequestOperationRet::operationCompleted)
      .isEqualTo(true);

    verify(monitoredEntityManager, times(3))
      .addEntity(keyCaptor.capture(), entityCaptor.capture());
    verifyNoMoreInteractions(monitoredEntityManager);

    verify(ptpResources, times(1))
      .findAllCtps(request.neId, new Aid(TTP_AID));
    verifyNoMoreInteractions(ptpResources);

    verify(crmCtrlTaskScheduler, times(1))
      .submitTask(any());

    Assertions.assertThat(keyCaptor.getAllValues())
      .extracting(MonitoredEntityKey::neId, MonitoredEntityKey::entityUri)
      .containsExactly(Tuple.tuple(neId, OTSIA_2_2_C1), Tuple.tuple(neId, OTSIA_2_2_N), Tuple.tuple(neId, ctpUriString));
    Assertions.assertThat(entityCaptor.getAllValues())
      .extracting(MonitoredEntity::segmentRequestId, MonitoredEntity::entityUri, MonitoredEntity::equipmentType, MonitoredEntity::terminationPointPair)
      .containsExactly(Tuple.tuple(request.id,
                                   OTSIA_2_2_C1,
                                   MonitoredEntity.EquipmentType.ROADM,
                                   MonitoredEntity.TerminationPointPair.of(segmentDstTPAid, null)),
                       Tuple.tuple(request.id,
                                   OTSIA_2_2_N,
                                   MonitoredEntity.EquipmentType.ROADM,
                                   MonitoredEntity.TerminationPointPair.of(null, segmentDstTPAid)),
                       Tuple.tuple(request.id,
                                   ctpUriString,
                                   MonitoredEntity.EquipmentType.ALIEN,
                                   MonitoredEntity.TerminationPointPair.of(null,segmentSrcTPAid))
      );
  }
}