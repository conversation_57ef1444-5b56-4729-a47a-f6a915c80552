/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.OpticalParameters;
import com.adva.nlms.opticalparameters.api.Value;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.opticalparameters.api.enums.ParameterName;
import com.adva.nlms.opticalparameters.api.enums.Termination;
import com.adva.nlms.opticalparameters.cim.api.CimValue;
import com.adva.nlms.opticalparameters.cim.api.OpticalParametersCim;
import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;
import com.adva.nlms.resource.mediator.f8.InterfaceType;
import com.adva.nlms.resource.mediator.f8.ProtectionGroupFinder;
import com.adva.nlms.resource.mediator.f8.events.SncLocalEvent;
import com.adva.nlms.resource.mediator.f8.internalpath.Connection;
import com.adva.nlms.resource.mediator.f8.internalpath.CrossConnect;
import com.adva.nlms.resource.mediator.f8.internalpath.ForkPlacement;
import com.adva.nlms.resource.mediator.f8.internalpath.InterModuleConnection;
import com.adva.nlms.resource.mediator.f8.internalpath.PortResourceIdentifier;
import com.adva.nlms.resource.mediator.f8.internalpath.SignalType;
import com.adva.nlms.resource.mediator.f8.internalpath.StackedLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.SubConnection;
import com.adva.nlms.resource.mediator.f8.internalpath.TerminationPoint;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegmentRepository;
import com.adva.nlms.resource.provision.f8.api.in.MoCapabilityInquiryParams;
import com.adva.nlms.resource.provision.f8.api.in.MoCimParameter;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionRequestParameters;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.cglib.core.internal.Function;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

class PrepareProvisionRequest {

  private final Logger log = LogManager.getLogger(getClass());

  private final OtnCimProvisionRequestParametersCreatorImpl provisionRequestParametersCreator;
  private final Provision provisionApi;
  private final RrmOtnCardCapabilities rrmOtnCardCapabilities;
  private final RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade;
  private final OtnSegmentRepository otnSegmentRepository;
  private final ProtectionGroupFinder protectionGroupFinder;
  private final MoCapabilityProvider moCapabilityProvider;
  private final List<Request> provisionRequests;

  // stores CTPs provisioned as part of intermodule connections to avoid redundant processing
  private final Map<Aid, Uri> interModuleTerminationPointToGeneratedCtpUri = new HashMap<>();

  public PrepareProvisionRequest(OtnCimProvisionRequestParametersCreatorImpl cimProvisionRequestParametersCreator,
                                 Provision provisionApi, RrmOtnCardCapabilities rrmOtnCardCapabilities,
                                 RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade,
                                 OtnSegmentRepository otnSegmentRepository,
                                 ProtectionGroupFinder protectionGroupFinder, MoCapabilityProvider moCapabilityProvider) {
    this.provisionRequestParametersCreator = cimProvisionRequestParametersCreator;
    this.provisionApi = provisionApi;
    this.rrmOtnCardCapabilities = rrmOtnCardCapabilities;
    this.rrmOtnDbResourcesFacade = rrmOtnDbResourcesFacade;
    this.otnSegmentRepository = otnSegmentRepository;
    this.protectionGroupFinder = protectionGroupFinder;
    this.moCapabilityProvider = moCapabilityProvider;

    provisionRequests = new ArrayList<>();
  }

  private enum NodePosition {
    INGRESS, TRANSIT, EGRESS
  }

  private record ProtectedSncData(
    String sncUri,
    Collection<String> aEndList,
    Collection<String> zEndList,
    String clientCtp,
    String networkCtp,
    boolean exists,
    boolean isEntityAutocreated
  ) {
  }

  private record CtpProvisionContext(
    TerminationPoint terminationPoint,
    CardRef cardRef,
    SignalType signalType,
    List<OpticalParameters.SelectedParameter> opticalParams,
    boolean isEntityAutocreated,
    boolean isInterModuleConnectionEntity
    // FNMD-124342 & FNMD-124797 temporary field for intermodule connections to not delete CTPs in optical layer
  ) {
  }

  public List<Request> prepareRequest(int neId, CrmSegmentRequestDto request) {
    var nodePosition = determineNodePosition(request.getSrcInterfaceType(), request.getDstInterfaceType());

    prepareInterModuleConnections(neId, request.getInternalPath().interModuleConnections());
    prepareConnections(neId, request.getInternalPath().connections(), request.portParams, nodePosition);

    if (CrmSegmentRequestDto.RequestType.CREATE.equals(request.getRequestType())) {
      prepareEpteRequests(neId);
    }

    return provisionRequests;
  }

  private NodePosition determineNodePosition(InterfaceType srcInterfaceType, InterfaceType dstInterfaceType) {
    if (isEnniOrUni(srcInterfaceType)) {
      return NodePosition.INGRESS;
    } else if (isEnniOrUni(dstInterfaceType)) {
      return NodePosition.EGRESS;
    }
    return NodePosition.TRANSIT;
  }

  private boolean isEnniOrUni(InterfaceType interfaceType) {
    return interfaceType == InterfaceType.ENNI || interfaceType == InterfaceType.UNI;
  }

  private void addToProvisionRequestsList(Request request) {
    if (!provisionRequests.contains(request)) {
      provisionRequests.add(request);
    } else {
      log.info("{} already exists on provision requests list", request.toString());
    }
  }

  private void prepareInterModuleConnections(int neId, List<InterModuleConnection> interModuleConnection) {
    if (interModuleConnection == null || interModuleConnection.isEmpty()) {
      return;
    }
    interModuleConnection.forEach(connection -> prepareIntermoduleConnection(neId, connection));
  }

  private void prepareIntermoduleConnection(int neId, InterModuleConnection interModuleConnection) {
    var opticalParams = prepareCommonOpticalParams(interModuleConnection.commonParams());

    var srcCardRef = getCardRefFromTerminationPoint(neId, interModuleConnection.source());
    var dstCardRef = getCardRefFromTerminationPoint(neId, interModuleConnection.destination());

    var srcIsEntityAutocreated = provisionRequestParametersCreator.isEntityAutocreated(
      srcCardRef.type(), srcCardRef.mode(), interModuleConnection.signalType().oduType().signalType());
    var dstIsEntityAutocreated = provisionRequestParametersCreator.isEntityAutocreated(
      dstCardRef.type(), dstCardRef.mode(), interModuleConnection.signalType().oduType().signalType());

    var srcCtpContext = new CtpProvisionContext(
      interModuleConnection.source(), srcCardRef, interModuleConnection.signalType(), opticalParams, srcIsEntityAutocreated, true);
    var dstCtpContext = new CtpProvisionContext(
      interModuleConnection.destination(), dstCardRef, interModuleConnection.signalType(), opticalParams, dstIsEntityAutocreated, true);

    prepareInterModuleConnectionTerminationPoint(neId, srcCtpContext);
    prepareInterModuleConnectionTerminationPoint(neId, dstCtpContext);
  }

  private void prepareInterModuleConnectionTerminationPoint(int neId, CtpProvisionContext context) {
    var ctpUri = prepareAllLayersForTerminationPoint(neId, context);
    interModuleTerminationPointToGeneratedCtpUri.put(new Aid(context.terminationPoint.aid()), new Uri(ctpUri));
  }

  private void prepareConnections(int neId, List<Connection> connections,
                                  List<OpticalParameters.SelectedParameter> opticalParams,
                                  NodePosition nodePosition) {
    if (connections == null || connections.isEmpty()) {
      return;
    }
    connections.forEach(connection -> prepareConnection(neId, connection, opticalParams, nodePosition));
  }

  private void prepareConnection(int neId, Connection connection,
                                 List<OpticalParameters.SelectedParameter> opticalParams,
                                 NodePosition nodePosition) {
    connection.subConnections()
      .forEach(subconnection -> prepareSubConnection(
        neId, connection.signalType(), subconnection, opticalParams, nodePosition));
  }

  private void prepareSubConnection(int neId, SignalType signalType, SubConnection subConnection,
                                    List<OpticalParameters.SelectedParameter> opticalParams,
                                    NodePosition nodePosition) {
    if (subConnection instanceof CrossConnect crossConnect) {
      prepareCrossConnect(neId, signalType, crossConnect, opticalParams, nodePosition);
    } else if (subConnection instanceof TerminationPoint terminationPoint) {
      // All cases which has separate TerminationPoint in InternalPath are the ones that require only ODU layer provisioning
      var cardRef = getCardRefFromTerminationPoint(neId, terminationPoint);
      var isEntityAutocreated = provisionRequestParametersCreator.isEntityAutocreated(
        cardRef.type(), cardRef.mode(), signalType.oduType().signalType());

      var ctpContext = new CtpProvisionContext(terminationPoint, cardRef, signalType, opticalParams, isEntityAutocreated, false);

      prepareOduLayerForTerminationPoint(neId, ctpContext);
    }
  }

  private void prepareCrossConnect(int neId, SignalType signalType, CrossConnect crossConnect,
                                   List<OpticalParameters.SelectedParameter> opticalParams,
                                   NodePosition nodePosition) {
    CardRef cardRef = getCardRefFromTerminationPoint(neId, crossConnect.source());

    var isEntityAutocreated = provisionRequestParametersCreator.isEntityAutocreated(cardRef.type(), cardRef.mode(), signalType.oduType().signalType());
    final var sncUriPrefix = generateSncUri(neId, signalType, cardRef);

    if (isForkedCrossConnect(crossConnect)) {
      var source = getSourceOfForkedCrossConnect(crossConnect);
      var destination = getDestinationOfForkedCrossConnect(crossConnect);

      // Port with no port params passed in segment request could need GLQ parameter, other params are not needed
      // As we are changing direction for egress node (based on fork), only ingress node could have port params and all layers provisioned
      var srcPortParams = nodePosition != NodePosition.TRANSIT ? opticalParams : filterGlqParam(opticalParams);
      var dstPortParams = filterGlqParam(opticalParams);

      var srcCtpContext = new CtpProvisionContext(source, cardRef, signalType, srcPortParams, isEntityAutocreated, false);
      var dstCtpContext = new CtpProvisionContext(destination, cardRef, signalType, dstPortParams, isEntityAutocreated, false);

      var srcCtp = nodePosition != NodePosition.TRANSIT ?
        prepareAllLayersForTerminationPoint(neId, srcCtpContext) :
        prepareOduLayerForTerminationPoint(neId, srcCtpContext);

      var dstCtp = prepareOduLayerForTerminationPoint(neId, dstCtpContext);

      if (handleProtectedCrossConnect(neId, sncUriPrefix, srcCtp, dstCtp, isEntityAutocreated)) {
        return;
      }
    }

    prepareUnprotectedCrossConnect(neId, signalType, crossConnect, opticalParams, sncUriPrefix, cardRef, isEntityAutocreated, nodePosition);
  }

  private boolean handleProtectedCrossConnect(int neId, String sncUriPrefix, String sourceCtp, String destinationCtp,
                                              boolean isEntityAutocreated) {
    var protectedCross = rrmOtnDbResourcesFacade.getExistingProtectedSncUri(neId, sncUriPrefix, sourceCtp, destinationCtp);
    if (protectedCross.isPresent()) {
      var snc = rrmOtnDbResourcesFacade.findCrossConnect(neId, protectedCross.get());
      snc.ifPresentOrElse(
        s -> {
          var protectedSncData = new ProtectedSncData(
            s.uri().uri(), s.aEndList(), s.zEndList(), sourceCtp, destinationCtp, true, isEntityAutocreated);
          prepareProtectedCrossConnect(neId, protectedSncData);
        },
        () -> {
          throw new OtnProvisioningException(String.format("could not find SNC %s", protectedCross.get().uri()));
        }
      );
      return true;
    }

    var sncEvent = getSncEventDataFromFirstLeg(neId, sourceCtp, sncUriPrefix);
    if (sncEvent.isPresent()) {
      var protectedSncData = new ProtectedSncData(
        sncEvent.get().uri(), sncEvent.get().aEndpoints(), sncEvent.get().zEndpoints(), sourceCtp, destinationCtp,
        false, isEntityAutocreated);

      prepareProtectedCrossConnect(neId, protectedSncData);
      return true;
    }
    return false;
  }

  private void prepareProtectedCrossConnect(int neId, ProtectedSncData protectedSncData) {
    var aEndpoints = new LinkedHashSet<>(protectedSncData.aEndList);
    var zEndpoints = new LinkedHashSet<>(protectedSncData.zEndList);
    if (aEndpoints.contains(protectedSncData.clientCtp)) {
      zEndpoints.add(protectedSncData.networkCtp);
    } else {
      aEndpoints.add(protectedSncData.networkCtp);
    }
    if (protectedSncData.exists) {
      provisionRequests.add(new SncRequest(
        neId, protectedSncData.sncUri, true, protectedSncData.isEntityAutocreated, aEndpoints, zEndpoints, provisionApi));
    } else {
      provisionRequests.add(new SncModifyRequest(
        neId, protectedSncData.sncUri, aEndpoints, zEndpoints, protectedSncData.isEntityAutocreated, provisionApi));
    }
  }

  private void prepareUnprotectedCrossConnect(int neId, SignalType signalType, CrossConnect crossConnect,
                                              List<OpticalParameters.SelectedParameter> opticalParams,
                                              String sncUriPrefix, CardRef cardRef, boolean isEntityAutocreated,
                                              NodePosition nodePosition) {
    TerminationPoint source = crossConnect.source();
    TerminationPoint destination = crossConnect.destination();
    CardRef srcCardRef = cardRef;
    CardRef dstCardRef = cardRef;

    if (isDualCardMode(cardRef)) {
      // In dual card mode, we have to get both cardRefs
      dstCardRef = getCardRefFromTerminationPoint(neId, destination);
    }

    // For some cards, client port ODU CTP must be provisioned before network port
    // Because there is no such problem in the opposite direction, we'll try to provision C port first in all cases
    if (nodePosition == NodePosition.EGRESS || arePortsInOppositeDirectionOnTransit(neId, crossConnect.source(), cardRef, nodePosition)) {
      source = crossConnect.destination();
      destination = crossConnect.source();
      srcCardRef = dstCardRef;
      dstCardRef = cardRef;
    }

    // Port with no port params passed in segment request could need GLQ parameter, other params are not needed
    // As we are changing direction for egress node, only ingress node could have port params and all layers provisioned
    var srcPortParams = nodePosition != NodePosition.TRANSIT ? opticalParams : filterGlqParam(opticalParams);
    var dstPortParams = filterGlqParam(opticalParams);

    var srcCtpContext = new CtpProvisionContext(source, srcCardRef, signalType, srcPortParams, isEntityAutocreated, false);
    var dstCtpContext = new CtpProvisionContext(destination, dstCardRef, signalType, dstPortParams, isEntityAutocreated, false);

    var srcCtp = nodePosition != NodePosition.TRANSIT ?
      prepareAllLayersForTerminationPoint(neId, srcCtpContext) :
      prepareOduLayerForTerminationPoint(neId, srcCtpContext);

    var dstCtp = prepareOduLayerForTerminationPoint(neId, dstCtpContext);

    var aEndpoints = Set.of(srcCtp);
    var zEndpoints = Set.of(dstCtp);

    Optional<String> adoptUri;
    if (isEntityAutocreated) {
      // For AccessFlex case, autocreated SNC may not yet contain z-endpoint. Therefore, we'll check only by a-endpoint
      adoptUri = rrmOtnDbResourcesFacade.findSncUriByUriPrefixAndSingleSideEndpoints(neId, sncUriPrefix, aEndpoints).map(Uri::uri);
    } else {
      adoptUri = rrmOtnDbResourcesFacade.findSncUriByUriPrefixAndEndpoints(neId, sncUriPrefix, aEndpoints, zEndpoints).map(Uri::uri);
    }
    addToProvisionRequestsList(
      new SncRequest(neId, adoptUri.orElse(sncUriPrefix), adoptUri.isPresent(), isEntityAutocreated, aEndpoints, zEndpoints, provisionApi));
  }

  private String prepareAllLayersForTerminationPoint(int neId, CtpProvisionContext context) {
    log.debug("Prepare all layers for Termination Point {}", context.terminationPoint.aid());
    return prepareLayersForTerminationPoint(neId, context, true);
  }

  private String prepareOduLayerForTerminationPoint(int neId, CtpProvisionContext context) {
    log.debug("Prepare ODU layer for Termination Point {}", context.terminationPoint.aid());
    return prepareLayersForTerminationPoint(neId, context, false);
  }

  private String prepareLayersForTerminationPoint(int neId, CtpProvisionContext context, boolean prepareAllLayers) {
    var terminationPoint = context.terminationPoint;

    if (interModuleTerminationPointToGeneratedCtpUri.containsKey(new Aid(terminationPoint.aid()))) {
      return interModuleTerminationPointToGeneratedCtpUri.get(new Aid(terminationPoint.aid())).uri();
    }

    var ptpAid = new Aid(terminationPoint.aid());
    var plugAid = new Aid(convertToPlugId(terminationPoint.aid(), context.cardRef.type()));
    var plugRef = rrmOtnDbResourcesFacade.findPlugFromPtp(neId, ptpAid)
      .orElseGet(() -> rrmOtnDbResourcesFacade.findPlug(neId, plugAid).orElse(null));

    var parentCtpUri = prepareAllLayers ? preparePtpWithCtp(neId, context, plugRef)
      : getParentCtpUri(neId, terminationPoint);
    var parentCtpLayer = getParentLayerQualifier(neId, context, parentCtpUri, prepareAllLayers);

    return generateOduCtpUri(neId, context, parentCtpLayer, parentCtpUri, plugRef);
  }

  private String preparePtpWithCtp(int neId,
                                   CtpProvisionContext context,
                                   PlugRef plugRef) {
    String plugType = plugRef != null ? plugRef.type() : ""; // non-pluggable interface case
    var layerQualifier = context.signalType.oduType().signalType();
    List<OpticalParameters.SelectedParameter> inParams = new ArrayList<>(context.opticalParams);

    String portLayer = inParams.stream()
      .filter(p -> p.name().equals(ParameterName.GLQ))
      .findFirst()
      .map(p -> ((Value.Enum) p.value()).value())
      .orElseGet(() -> {
        var layer = generateGlqParameter(context.terminationPoint, layerQualifier);
        inParams.add(layer);
        return ((Value.Enum) layer.value()).value();
      });

    MoCapabilityInquiryParams inMoParams = MoCapabilityInquiryParams.builder()
      .moduleType(context.cardRef.type())
      .plugType(plugType)
      .portAid(context.terminationPoint.aid())
      .layerQualifiers(portLayer)
      .build();

    var params = provisionRequestParametersCreator.prepareProvisionRequestParameters(
      NetworkElementID.create(neId),
      context.cardRef.uri().uri(),
      inMoParams,
      inParams
    );

    var ptpRef = rrmOtnDbResourcesFacade.findPtp(neId, new Aid(context.terminationPoint.aid()));
    var isPtpCreated = ptpRef.isPresent();

    var ptp = params.stream()
      .filter(item -> item.cimClass().equals("ptp"))
      .findFirst();

    // FNMD-113290: For some cases (old 400 cards modelling) params on PTP has to be set after CTP creation.
    // Therefore, 3 request are created in specific order:
    // - PtpRequest (for PTP creation - if needed)
    // - CtpRequest (for higher order CTP creation)
    // - PtpModifyRequest (for updating parameters on PTP)
    if (!isPtpCreated) {
      ptp.map(item -> new PtpRequest(neId, item.cimPath(), false, provisionApi))
        .ifPresent(this::addToProvisionRequestsList);
    }

    var ctp = params.stream()
      .filter(item -> item.cimClass().equals("ctp"))
      .findFirst().orElseThrow(() -> new OtnProvisioningException(String.format("Cannot get CTP for %s",
        context.terminationPoint.aid())));

    var isCtpCreated = rrmOtnDbResourcesFacade.findCtp(neId, new Uri(ctp.cimPath())).isPresent();

    // FNMD-124342 & FNMD-124797 temporary workaround for intermodule connections to not delete CTPs in optical layer when they are autocreated
    var isEntityAutocreated = context.isEntityAutocreated ||
                              (context.isInterModuleConnectionEntity && isCtpCreated);

    var ctpRequest = new CtpRequest(neId, ctp.cimPath(), isCtpCreated, isEntityAutocreated, new HashSet<>(ctp.cimParameters()), provisionApi);
    addToProvisionRequestsList(ctpRequest);

    // FNMD-126301 - since we need to set admin state on PTP after segment deletion, we'll process PtpParamsRequest
    // even if there are no params passed.
    ptp.map(item ->
        new PtpParamsRequest(neId, item.cimPath(), isPtpCreated, new HashSet<>(item.cimParameters()), provisionApi))
      .ifPresent(this::addToProvisionRequestsList);

    return ctp.cimPath();
  }

  private String getParentCtpUri(int neId, TerminationPoint terminationPoint) {
    final var children = rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(neId, new Aid(terminationPoint.aid()));
    if (children.size() != 1) {
      log.error("{} has provisioned {} ctps. Only one is supported!", terminationPoint.aid(), children.size());
      throw new OtnProvisioningException(String.format("%s has provisioned %d ctps. Only one is supported!",
        terminationPoint.aid(), children.size()));
    }

    return children.get(0).uri().uri();
  }

  private LayerQualifier getParentLayerQualifier(int neId,
                                                 CtpProvisionContext context,
                                                 String parentCtpUri,
                                                 boolean prepareAllLayers) {
    var parentCtp = rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(neId, new Uri(parentCtpUri));
    if (parentCtp.isEmpty() && !prepareAllLayers) {
      throw new OtnProvisioningException(String.format("Ctp %s is missing in database!", parentCtpUri));
    }

    if (parentCtp.isPresent()) {
      return OpticalParametersCim.getLayerQualifierFromLayerList(parentCtp.get().layerRate(), parentCtp.get().aid().aid())
        .orElse(LayerQualifier.NONE);
    }

    var glqParam = context.opticalParams.stream()
      .filter(p -> p.name().equals(ParameterName.GLQ))
      .findFirst();
    if (glqParam.isPresent() && glqParam.get().value() instanceof Value.Enum) {
      return LayerQualifier.fromString(((Value.Enum) glqParam.get().value()).value())
        .orElse(LayerQualifier.NONE);
    }

    // For OTU signals there is no GLQ in request so Layer Qualifier has to be deduced from signalType
    return LayerQualifier.fromString("OT" + context.signalType.oduType().signalType().toString().substring(2))
      .orElse(LayerQualifier.NONE);
  }

  private String generateOduCtpUri(int neId,
                                   CtpProvisionContext context,
                                   LayerQualifier parentCtpLayer,
                                   String parentCtpUri,
                                   PlugRef plugRef) {
    var terminationPoint = context.terminationPoint;
    var signalType = context.signalType;

    if (parentCtpLayer == null || parentCtpLayer == LayerQualifier.NONE) {
      throw new OtnProvisioningException(
        String.format("Missing parent Layer Qualifier for provisioned ODU entity on port %s", terminationPoint.aid()));
    }

    Set<MoCimParameter> parentParams = new HashSet<>();
    if (terminationPoint.resourceIdentifier() instanceof StackedLabel label) {
      // LayerQualifier is not important as we only generate parent params to find proper uri
      parentParams.addAll(PortResourceIdentifierToCimParameters.generateFromParentInStackedLabel(label, LayerQualifier.NONE));
    }

    var params = PortResourceIdentifierToCimParameters.generate(terminationPoint.resourceIdentifier(),
      signalType.oduType().signalType());
    var childCtpLayer = signalType.oduType().signalType();
    var layers = getLayers(neId, parentCtpUri, parentParams, parentCtpLayer.toString(), childCtpLayer.toString());

    var ctpParams = generateOduCtpParams(neId, context, plugRef, layers);

    var ctpUri = resolveCtpUriIds(neId, ctpParams.cimPath(), terminationPoint.resourceIdentifier(), parentParams, params);
    params.addAll(new HashSet<>(ctpParams.cimParameters()));
    boolean isAdopt = false;

    if (ctpUri.endsWith("-")) {
      ctpUri = findFreeCtpUriForCompleteLabel(neId, terminationPoint.aid(), ctpUri, params);
    } else {
      isAdopt = rrmOtnDbResourcesFacade.findCtp(neId, new Uri(ctpUri)).isPresent();
    }

    addToProvisionRequestsList(new CtpRequest(neId, ctpUri, isAdopt, context.isEntityAutocreated, params, provisionApi));
    return ctpUri;
  }

  private String findFreeCtpUriForCompleteLabel(int neId, String terminationPoint, String ctpUriPrefix, Set<MoCimParameter> params) {
    var tp = params.stream()
      .filter(s -> s.name().equals("tp"))
      .findFirst()
      .map(s -> (int) ((CimValue.Int) s.value()).value())
      .orElse(0);

    // First we need to check if we can provision CTP with id matching tributary port
    var id = provisionApi.getAvailableCtpEntity(NetworkElementID.create(neId), ctpUriPrefix + tp);
    if (id.isPresent()) {
      return ctpUriPrefix + tp;
    }

    // If we cannot provision CTP with id matching tributary port, we will try to find first free CTP
    id = provisionApi.getAvailableCtpEntity(NetworkElementID.create(neId), ctpUriPrefix);
    if (id.isEmpty()) {
      throw new OtnProvisioningException(String.format("No more available CTPs under %s", terminationPoint));
    }
    return ctpUriPrefix + id.get();
  }

  private List<String> getLayers(int neId, String parentUri, Set<MoCimParameter> parentParams,
                                 String parentLayer, String childLayer) {
    ArrayList<String> layers = new ArrayList<>();
    layers.add(parentLayer);

    if (!parentParams.isEmpty()) {
      // When parent label in Stacked label is Complete label we deal with OTUCn dual stage multiplexing case.
      // Therefore, we have to get 2 CTPs hierarchically as there is another intermediate layer for ODUCn
      // which doesn't interest us
      var firstLevelIntermediateCtps = rrmOtnDbResourcesFacade.findAllCtpsForParentUri(neId, new Uri(parentUri));
      if (firstLevelIntermediateCtps.size() == 1) {
        var uriPrefix = firstLevelIntermediateCtps.get(0).uri().uri();
        findCtpUriByUriPrefixAndParams(neId, uriPrefix, parentParams)
          .flatMap(uri -> rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(neId, uri))
          .flatMap(ctp -> OpticalParametersCim.getLayerQualifierFromLayerList(ctp.layerRate(), ctp.uri().uri()))
          .ifPresent(intermediateLayer -> layers.add(intermediateLayer.toString()));
      }
    }

    layers.add(childLayer);
    return layers;
  }

  private Optional<Uri> findCtpUriByUriPrefixAndParams(final int neId,
                                                       String tmpUri,
                                                       final Set<MoCimParameter> params) {
    if (params.isEmpty()) {
      return Optional.empty();
    }

    var tp = params.stream()
      .filter(s -> s.name().equals("tp"))
      .findFirst()
      .map(s -> (int) ((CimValue.Int) s.value()).value())
      .orElse(0);

    var ts = params.stream()
      .filter(s -> s.name().equals("ts"))
      .findFirst()
      .map(s -> ((CimValue.IntList) s.value()).value())
      .orElse(List.of());

    return rrmOtnDbResourcesFacade.findCtpUriByUriPrefixAndParams(neId, tmpUri, tp, ts);
  }

  private ProvisionRequestParameters generateOduCtpParams(
    int neId,
    CtpProvisionContext context,
    PlugRef plugRef,
    List<String> layers) {
    var terminationPoint = context.terminationPoint;
    var ctpParameters = new ArrayList<OpticalParameters.SelectedParameter>();
    var termination = new OpticalParameters.SelectedParameter(ParameterName.TERM,
      new Value.Enum(terminationPoint.terminateSignal() ? Termination.TTP.toString() : Termination.CTP.toString()));
    ctpParameters.add(termination);
    // ODUflex payload (ofcbrcl) is mapped from GLQ

    context.opticalParams.stream()
      .filter(parameter -> parameter.name().equals(ParameterName.GLQ))
      .findAny()
      .ifPresent(ctpParameters::add);

    var plugType = plugRef != null ? plugRef.type() : "";

    MoCapabilityInquiryParams moCapabilityInquiryParams = MoCapabilityInquiryParams.builder()
      .moduleType(context.cardRef.type())
      .plugType(plugType)
      .portAid(terminationPoint.aid())
      .layerQualifiers(layers.toArray(new String[0]))
      .build();

    var provisionRequestParameters = provisionRequestParametersCreator.prepareProvisionRequestParameters(
      NetworkElementID.create(neId),
      context.cardRef.uri().uri(),
      moCapabilityInquiryParams,
      ctpParameters);

    return provisionRequestParameters.stream()
      .filter(item -> item.cimClass().equals("ctp") && item.cimPath().contains("odu"))
      .findFirst().orElseThrow(() -> new OtnProvisioningException(String.format("Cannot get ODU CTP for %s",
        terminationPoint.aid())));
  }

  private String resolveCtpUriIds(int neId, String ctpUri, PortResourceIdentifier portResourceIdentifier,
                                  Set<MoCimParameter> parentParams, Set<MoCimParameter> childParams) {
    if (!ctpUri.contains("{id}")) {
      return ctpUri;
    }

    String[] uriParts = ctpUri.split("\\{id}");
    if (uriParts.length < 2 && portResourceIdentifier instanceof StackedLabel) {
      throw new OtnProvisioningException(
        String.format("Wrong ODU CTP uri path %s for request with StackedLabel", ctpUri));
    }

    if (uriParts.length == 2) {
      String higherOrderCtpPart = resolveCtpPart(neId, uriParts[0], portResourceIdentifier, parentParams)
        .orElseThrow(() -> new OtnProvisioningException(
          String.format("Cannot get CTP for ODU CTP uri part %s from path %s", uriParts[0], ctpUri)));
      String childCtpUri = higherOrderCtpPart + uriParts[1];
      return resolveCtpPart(neId, childCtpUri, portResourceIdentifier, childParams).orElse(childCtpUri);
    }

    return resolveCtpPart(neId, uriParts[0], portResourceIdentifier, childParams).orElse(uriParts[0]);
  }

  private Optional<String> resolveCtpPart(int neId, String uriPart, PortResourceIdentifier portResourceIdentifier,
                                          Set<MoCimParameter> params) {
    if (params.isEmpty()) {
      // Container as parent label
      return PortResourceIdentifierToCimString.convert(portResourceIdentifier)
        .filter(containerId -> !containerId.isEmpty())
        .map(containerId -> uriPart + containerId);
    } else {
      // Complete as parent label
      return findCtpUriByUriPrefixAndParams(neId, uriPart, params).map(Uri::uri);
    }
  }

  private CardRef getCardRefFromTerminationPoint(int neId, TerminationPoint terminationPoint) {
    var cardRef = rrmOtnDbResourcesFacade.findCardFromModule(neId, new Aid(terminationPoint.getModuleAid()));
    if (cardRef == null) {
      log.error("Cannot get card reference for termination point {}", terminationPoint.aid());
      throw new OtnProvisioningException(
        String.format("Cannot get card reference for termination point %s", terminationPoint.aid()));
    }
    return cardRef;
  }

  private String generateSncUri(int neId, SignalType signalType, CardRef cardRef) {
    Function<String, String> generateUriWithPrefix = uriPrefix -> uriPrefix + "/sn/" + LayerQualifierToCimString.convert(signalType.oduType().signalType()) + "/snc";
    if (isDualCardMode(cardRef)) {
      var cardClusterRef = rrmOtnDbResourcesFacade.findAssociatedCardCluster(neId, cardRef.aid())
        .orElseThrow(() -> {
          log.error("cannot get card cluster reference for the card {}", cardRef.aid().aid());
          return new OtnProvisioningException(String.format("cannot get card cluster reference for the card %s", cardRef.aid().aid()));
        });
      return generateUriWithPrefix.apply(cardClusterRef.uri().uri());
    }
    return generateUriWithPrefix.apply(cardRef.uri().uri());
  }

  private boolean isDualCardMode(CardRef cardRef) {
    return "dualcc".equals(cardRef.mode());
  }

  private Optional<SncLocalEvent> getSncEventDataFromFirstLeg(int neId, String endpointUri, String sncUriPrefix) {
    var associatedSegment = otnSegmentRepository.findAssociatedSegmentByEndpointAndSncUriPrefix(neId, endpointUri, sncUriPrefix);
    return associatedSegment.flatMap(segment ->
      segment.provisionEvents().stream()
        .filter(SncLocalEvent.class::isInstance)
        .findFirst()
        .map(SncLocalEvent.class::cast));
  }

  private String convertToPlugId(final String port, final String moduleType) {
    String[] parts = port.split("[-/]");

    if (!port.contains("/")) {
      throw new OtnProvisioningException(String.format("Invalid port %s format, it does not contains '/' character", port));
    }
    var portGroup = rrmOtnCardCapabilities.getGroupPort(moduleType, port.substring(port.lastIndexOf('/') + 1).toUpperCase());

    if (portGroup.isPresent()) {
      if (parts.length < 3) {
        throw new OtnProvisioningException(String.format("Invalid port %s format, could not determine shelf/slot/portGroup", port));
      }
      return "Plug-" + parts[1] + "/" + parts[2] + "/" + portGroup.get().toLowerCase();
    }

    if (!port.contains("-")) {
      throw new OtnProvisioningException(String.format("Invalid port %s format, it does not contains '-' character", port));
    }

    int firstDash = port.indexOf('-');
    int lastDash = port.lastIndexOf('-');

    if (firstDash == lastDash) {
      return "Plug" + port.substring(firstDash);
    } else {
      return "Plug" + port.substring(firstDash, lastDash);
    }
  }

  private boolean arePortsInOppositeDirectionOnTransit(int neId, TerminationPoint terminationPoint, CardRef cardRef,
                                                       NodePosition nodePosition) {
    // For cases when there is cross on transit node from network acting port to client acting port and card requires
    // cross provisioning from client acting to network acting
    return nodePosition == NodePosition.TRANSIT && !isPortClient(neId, terminationPoint, cardRef);
  }

  private boolean isPortClient(int neId, TerminationPoint terminationPoint, CardRef cardRef) {
    var ptpAid = new Aid(terminationPoint.aid());
    var plugAid = new Aid(convertToPlugId(terminationPoint.aid(), cardRef.type()));
    PlugRef plugRef = rrmOtnDbResourcesFacade.findPlugFromPtp(neId, ptpAid)
      .orElseGet(() -> rrmOtnDbResourcesFacade.findPlug(neId, plugAid)
        .orElse(null));

    return rrmOtnCardCapabilities.isPortClient(neId, cardRef, plugRef, ptpAid);
  }

  private void prepareEpteRequests(int neId) {
    var epteRequests = provisionRequests.stream()
      .filter(CtpRequest.class::isInstance)
      .map(CtpRequest.class::cast)
      .map(CtpRequest::getUri)
      .filter(ctpUri -> isEpteSupportedOnCard(neId, ctpUri))
      .map(ctpUri -> {
        var protectionGroup = protectionGroupFinder.findByRelatedSlcZEndpointCtp(neId, ctpUri);
        return protectionGroup
          .map(protectionGroupRef -> new EpteRequest(neId, ctpUri, protectionGroupRef.uri().uri(), provisionApi))
          .orElse(null);
      })
      .filter(Objects::nonNull)
      .toList();
    epteRequests.forEach(this::addToProvisionRequestsList);
  }

  private boolean isEpteSupportedOnCard(int neId, String ctpUri) {
    Optional<CardRef> card = rrmOtnDbResourcesFacade.findCardFromCtp(neId, new Uri(ctpUri));
    if (card.isPresent()) {
      var epteInquiryObject = rrmOtnCardCapabilities.buildMoEpteInquiryObject(neId, card.get());
      return moCapabilityProvider.isEpteSupported(epteInquiryObject);
    }
    return false;
  }

  private static OpticalParameters.SelectedParameter generateGlqParameter(TerminationPoint terminationPoint,
                                                                          LayerQualifier layer) throws OtnProvisioningException {
    var param = LayerQualifier.fromString("OT" + layer.toString().substring(2)).orElseThrow(() ->
      new OtnProvisioningException(String.format("Could not deduce expected payload for client port %s provisioning. GLQ parameter is missing and layerQualifier %s does not match any OTUk payload.",
        terminationPoint.aid(), layer)));
    return new OpticalParameters.SelectedParameter(ParameterName.GLQ,
      new Value.Enum(param.toString()));
  }

  private static boolean isForkedCrossConnect(CrossConnect crossConnect) {
    return crossConnect.fork() == ForkPlacement.SOURCE || crossConnect.fork() == ForkPlacement.DESTINATION;
  }

  private static TerminationPoint getSourceOfForkedCrossConnect(CrossConnect crossConnect) {
    return crossConnect.fork() == ForkPlacement.SOURCE ? crossConnect.source() : crossConnect.destination();
  }

  private static TerminationPoint getDestinationOfForkedCrossConnect(CrossConnect crossConnect) {
    return crossConnect.fork() == ForkPlacement.SOURCE ? crossConnect.destination() : crossConnect.source();
  }

  private static List<OpticalParameters.SelectedParameter> prepareCommonOpticalParams(Map<String, String> commonParams) {
    if (commonParams == null) {
      return List.of();
    }
    return commonParams.entrySet().stream().map(PrepareProvisionRequest::convertToOpticalParams).toList();
  }

  private static OpticalParameters.SelectedParameter convertToOpticalParams(Map.Entry<String, String> commonParameters) {
    return OpticalParameters.fromKeyValuePair(
      new OpticalParameters.KeyValuePair(
        commonParameters.getKey(),
        commonParameters.getValue())
    ).orElseThrow(() -> new OtnProvisioningException("Failed to parse optical params"));
  }

  private static List<OpticalParameters.SelectedParameter> filterGlqParam(List<OpticalParameters.SelectedParameter> opticalParams) {
    return opticalParams.stream()
      .filter(p -> p.name().equals(ParameterName.GLQ))
      .toList();
  }
}
