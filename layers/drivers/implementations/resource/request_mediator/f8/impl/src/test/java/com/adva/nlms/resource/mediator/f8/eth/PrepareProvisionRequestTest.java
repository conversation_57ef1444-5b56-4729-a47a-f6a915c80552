/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.eth;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capabilityprovider.mo.MoCapabilityProviderConfiguration;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.Ctp;
import com.adva.nlms.mediation.mo.inventory.resources.CtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.ProtectionGroupRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.OpticalParameters;
import com.adva.nlms.opticalparameters.api.Value;
import com.adva.nlms.opticalparameters.api.enums.ParameterName;
import com.adva.nlms.opticalparameters.cim.api.CimValue;
import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;
import com.adva.nlms.resource.mediator.f8.InterfaceType;
import com.adva.nlms.resource.mediator.f8.ProtectionGroupFinder;
import com.adva.nlms.resource.mediator.f8.eth.Container;
import com.adva.nlms.resource.mediator.f8.eth.CtpRequest;
import com.adva.nlms.resource.mediator.f8.eth.EpteRequest;
import com.adva.nlms.resource.mediator.f8.eth.EthCimProvisionRequestParametersCreatorImpl;
import com.adva.nlms.resource.mediator.f8.eth.EthProvisioningException;
import com.adva.nlms.resource.mediator.f8.eth.PrepareProvisionRequest;
import com.adva.nlms.resource.mediator.f8.eth.PtpParamsRequest;
import com.adva.nlms.resource.mediator.f8.eth.PtpRequest;
import com.adva.nlms.resource.mediator.f8.eth.Request;
import com.adva.nlms.resource.mediator.f8.eth.RrmEthCardCapabilities;
import com.adva.nlms.resource.mediator.f8.eth.RrmEthDbResourcesFacade;
import com.adva.nlms.resource.mediator.f8.eth.SncRequest;
import com.adva.nlms.resource.mediator.f8.eth.Whole;
import com.adva.nlms.resource.provision.f8.api.in.MoCimParameter;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;


@ExtendWith(MockitoExtension.class)
class PrepareProvisionRequestTest {
  private static final int NE_ID = 1;
  private static final String SEGMENT_ID = "someSegment";

  private static final CardRef CARD_REF = new CardRef(NE_ID, new Aid("Module-1/1"), new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card"), "MF-M6MDT", 2, "ethxp");

  private static final String PROTECTION_GROUP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,9/eq/card/prtgrp/traffic-1";

  private static final String P2_PLUG_AID = "Plug-1/1/p2";
  private static final String P3_PLUG_AID = "Plug-1/1/p3";
  private static final String P4_PLUG_AID = "Plug-1/1/p4";
  private static final String P7_PLUG_AID = "Plug-1/1/p7";

  private static final PlugRef P2_MUXPONDER_PLUG_REF = PlugRef.builder().setNeId(NE_ID).setAid(P2_PLUG_AID).setType("QSFP56-DD-425G-OZR+-SM-LC").build();
  private static final PlugRef P2_TRANSPONDER_PLUG_REF = PlugRef.builder().setNeId(NE_ID).setAid(P2_PLUG_AID).setType("QSFP56-DD-425G-ZR-SM-LC").build();
  private static final PlugRef P3_PLUG_REF = PlugRef.builder().setNeId(NE_ID).setAid(P3_PLUG_AID).setType("QSFP56-DD-425G-DR4-SM-MPO").build();
  private static final PlugRef P4_PLUG_REF = PlugRef.builder().setNeId(NE_ID).setAid(P4_PLUG_AID).setType("QSFP28-112G-LR4-SM-LC").build();
  private static final PlugRef P7_PLUG_REF = PlugRef.builder().setNeId(NE_ID).setAid(P7_PLUG_AID).setType("QSFP28-112G-ZR+-SM-LC").build();

  private static final String P3_PTP_AID = "Port-1/1/p3";
  private static final String P3_1_PTP_AID = "Port-1/1/p3-1";
  private static final String P4_PTP_AID = "Port-1/1/p4";

  private static final String P3_PTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,3";
  private static final String P3_1_PTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,3-1";
  private static final String P4_PTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,4";

  private static final String TRANSPONDER_P2_CTP_AID = "ET400ZR-1/1/p2/et400zr";
  private static final String MUXPONDER_P2_CTP_AID = "ET400OZRP-1/1/p2/et400ozrp";
  private static final String TRANSPONDER_P7_CTP_AID = "ET100ZR-1/1/p7/et100zr";

  private static final String TRANSPONDER_P2_CTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,2/ctp/et400zr";
  private static final String TRANSPONDER_P2_LOWER_CTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,2/ctp/et400zr/ctp/etc400g";
  private static final String MUXPONDER_P2_CTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,2/ctp/et400ozrp";
  private static final String MUXPONDER_P2_LOWER_CTP_URI_PREFIX = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,2/ctp/et400ozrp/ctp/etc100g-";
  private static final String MUXPONDER_P2_LOWER_CTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,2/ctp/et400ozrp/ctp/etc100g-1";
  private static final String MUXPONDER_P2_LOWER_CTP_URI_ID_2 = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,2/ctp/et400ozrp/ctp/etc100g-2";
  private static final String P3_CTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,3/ctp/et400";
  private static final String P3_1_CTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,3-1/ctp/et100";
  private static final String P4_CTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,4/ctp/et100";
  private static final String TRANSPONDER_P7_CTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,7/ctp/et100zr";
  private static final String TRANSPONDER_P7_LOWER_CTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,7/ctp/et100zr/ctp/etc100g";

  private static final Ctp TRANSPONDER_P2_CTP_REF = new Ctp.Builder()
    .setNeId(NE_ID)
    .setAid(new Aid(TRANSPONDER_P2_CTP_AID))
    .setUri(new Uri(TRANSPONDER_P2_CTP_URI))
    .setLayerRate(List.of("flexo4e"))
    .build();

  private static final Ctp MUXPONDER_P2_CTP_REF = new Ctp.Builder()
    .setNeId(NE_ID)
    .setAid(new Aid(MUXPONDER_P2_CTP_AID))
    .setUri(new Uri(MUXPONDER_P2_CTP_URI))
    .setLayerRate(List.of("flexo4e"))
    .build();

  private static final Ctp TRANSPONDER_P7_CTP_REF = new Ctp.Builder()
    .setNeId(NE_ID)
    .setAid(new Aid(TRANSPONDER_P7_CTP_AID))
    .setUri(new Uri(TRANSPONDER_P7_CTP_URI))
    .setLayerRate(List.of("eth100zr"))
    .build();

  private static final String SNC_100G_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/sn/etc6/snc";
  private static final String SNC_400G_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/sn/etc400/snc";

  private static final PtpRef P3_1_PTP_REF = new PtpRef(NE_ID, new Aid(P3_1_PTP_AID), new Uri(P3_1_PTP_URI), null);
  private static final PtpRef P4_PTP_REF = new PtpRef(NE_ID, new Aid(P4_PTP_AID), new Uri(P4_PTP_URI), null);

  private PrepareProvisionRequest prepareProvisionRequest;

  @Mock
  private RrmEthDbResourcesFacade rrmEthDbResourcesFacade;

  @Mock
  private RrmEthCardCapabilities capabilities;

  @Mock
  private Provision provisionApi;

  @Mock
  private ProtectionGroupFinder protectionGroupFinder;

  @BeforeEach
  void setUp() {
    MoCapabilityProvider provider = new MoCapabilityProviderConfiguration().moCapabilityProvider();
    EthCimProvisionRequestParametersCreatorImpl ethCimProvisionRequestParametersCreator = new EthCimProvisionRequestParametersCreatorImpl(provider);
    prepareProvisionRequest = new PrepareProvisionRequest(rrmEthDbResourcesFacade, ethCimProvisionRequestParametersCreator,
      provisionApi, capabilities, protectionGroupFinder);
  }

  @Test
  void testEgressTransponderExclusiveGroupsPlugPtpNotProvisioned() {
    // Given
    CrmSegmentRequestDto crmSegmentRequestDto = new CrmSegmentRequestDto();
    crmSegmentRequestDto.neId = NE_ID;
    crmSegmentRequestDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    crmSegmentRequestDto.id = SEGMENT_ID;
    crmSegmentRequestDto.dstTp = TRANSPONDER_P7_CTP_AID;
    crmSegmentRequestDto.srcTp = P3_1_PTP_AID;
    crmSegmentRequestDto.setSrcInterfaceType(InterfaceType.ENNI);
    crmSegmentRequestDto.setEthLabel(new Whole());
    crmSegmentRequestDto.portParams = List.of(new OpticalParameters.SelectedParameter(
      ParameterName.GLQ, new Value.Enum("ETH-100G")
    ));
    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Aid(TRANSPONDER_P7_CTP_AID)))
      .thenReturn(Optional.of(CARD_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlug(NE_ID, new Aid(P3_PLUG_AID)))
      .thenReturn(P3_PLUG_REF);
    Mockito.when(rrmEthDbResourcesFacade.findCtp(Mockito.eq(NE_ID), Mockito.any(Uri.class))).thenReturn(Optional.empty());
    Mockito.when(rrmEthDbResourcesFacade.findPtp(NE_ID, new Aid(P3_1_PTP_AID))).thenReturn(Optional.empty());
    Mockito.when(rrmEthDbResourcesFacade.findCtpExtended(NE_ID, new Aid(TRANSPONDER_P7_CTP_AID)))
      .thenReturn(Optional.of(TRANSPONDER_P7_CTP_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlugFromCtp(NE_ID, new Aid(TRANSPONDER_P7_CTP_AID)))
      .thenReturn(Optional.of(P7_PLUG_REF));
    // When
    var result = prepareProvisionRequest.prepareRequest(NE_ID, crmSegmentRequestDto);
    // Then
    List<Request> expected = new ArrayList<>(List.of(
      new PtpRequest(NE_ID, P3_1_PTP_URI, false, provisionApi),
      new CtpRequest(NE_ID, P3_1_CTP_URI, false, provisionApi),
      new PtpParamsRequest(NE_ID, P3_1_PTP_URI, false, Set.of(), provisionApi),
      new CtpRequest(NE_ID, TRANSPONDER_P7_LOWER_CTP_URI, false, Set.of(), provisionApi),
      new SncRequest(NE_ID, SNC_100G_URI, false, P3_1_CTP_URI, TRANSPONDER_P7_LOWER_CTP_URI, provisionApi)
    ));
    Assertions.assertEquals(expected, result);
  }

  @Test
  void testIngressTransponder() {
    // Given
    CrmSegmentRequestDto crmSegmentRequestDto = new CrmSegmentRequestDto();
    crmSegmentRequestDto.neId = NE_ID;
    crmSegmentRequestDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    crmSegmentRequestDto.id = SEGMENT_ID;
    crmSegmentRequestDto.srcTp = TRANSPONDER_P7_CTP_AID;
    crmSegmentRequestDto.dstTp = P4_PTP_AID;
    crmSegmentRequestDto.setEthLabel(new Whole());
    crmSegmentRequestDto.portParams = List.of(new OpticalParameters.SelectedParameter(
      ParameterName.GLQ, new Value.Enum("ETH-100G")
    ));
    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Aid(TRANSPONDER_P7_CTP_AID)))
      .thenReturn(Optional.of(CARD_REF));
    PlugRef plugRef = PlugRef.builder()
      .setNeId(NE_ID)
      .setAid(P4_PLUG_AID)
      .setType("QSFP28-103G-CWDM4E-SM-LC")
      .build();
    Mockito.when(rrmEthDbResourcesFacade.findPlug(NE_ID, new Aid(P4_PLUG_AID)))
      .thenReturn(plugRef);
    Mockito.when(rrmEthDbResourcesFacade.findCtp(Mockito.eq(NE_ID), Mockito.any(Uri.class))).thenReturn(Optional.empty());
    Mockito.when(rrmEthDbResourcesFacade.findPtp(NE_ID, new Aid(P4_PTP_AID))).thenReturn(Optional.of(P3_1_PTP_REF));
    Mockito.when(rrmEthDbResourcesFacade.findCtpExtended(NE_ID, new Aid(TRANSPONDER_P7_CTP_AID)))
      .thenReturn(Optional.of(TRANSPONDER_P7_CTP_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlugFromCtp(NE_ID, new Aid(TRANSPONDER_P7_CTP_AID)))
      .thenReturn(Optional.of(P7_PLUG_REF));
    // When
    var result = prepareProvisionRequest.prepareRequest(NE_ID, crmSegmentRequestDto);
    // Then
    List<Request> expected = new ArrayList<>(List.of(
      new CtpRequest(NE_ID, P4_CTP_URI, false, new HashSet<>(), provisionApi),
      new PtpParamsRequest(NE_ID, P4_PTP_URI, true, new HashSet<>(), provisionApi),
      new CtpRequest(NE_ID, TRANSPONDER_P7_LOWER_CTP_URI, false, Set.of(), provisionApi),
      new SncRequest(NE_ID, SNC_100G_URI, false, P4_CTP_URI, TRANSPONDER_P7_LOWER_CTP_URI, provisionApi)
    ));
    Assertions.assertEquals(expected, result);
  }

  @Test
  void testIngressTransponderWithEPTE() {
    // Given
    CrmSegmentRequestDto crmSegmentRequestDto = new CrmSegmentRequestDto();
    crmSegmentRequestDto.neId = NE_ID;
    crmSegmentRequestDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    crmSegmentRequestDto.id = SEGMENT_ID;
    crmSegmentRequestDto.srcTp = TRANSPONDER_P7_CTP_AID;
    crmSegmentRequestDto.dstTp = P4_PTP_AID;
    crmSegmentRequestDto.setEthLabel(new Whole());
    crmSegmentRequestDto.portParams = List.of(new OpticalParameters.SelectedParameter(
      ParameterName.GLQ, new Value.Enum("ETH-100G")
    ));
    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Aid(TRANSPONDER_P7_CTP_AID)))
      .thenReturn(Optional.of(CARD_REF));
    PlugRef plugRef = PlugRef.builder()
      .setNeId(NE_ID)
      .setAid(P4_PLUG_AID)
      .setType("QSFP28-103G-CWDM4E-SM-LC")
      .build();
    Mockito.when(rrmEthDbResourcesFacade.findPlug(NE_ID, new Aid(P4_PLUG_AID)))
      .thenReturn(plugRef);
    Mockito.when(rrmEthDbResourcesFacade.findCtp(Mockito.eq(NE_ID), Mockito.any(Uri.class))).thenReturn(Optional.empty());
    Mockito.when(rrmEthDbResourcesFacade.findPtp(NE_ID, new Aid(P4_PTP_AID))).thenReturn(Optional.of(P3_1_PTP_REF));
    Mockito.when(rrmEthDbResourcesFacade.findCtpExtended(NE_ID, new Aid(TRANSPONDER_P7_CTP_AID)))
      .thenReturn(Optional.of(TRANSPONDER_P7_CTP_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlugFromCtp(NE_ID, new Aid(TRANSPONDER_P7_CTP_AID)))
      .thenReturn(Optional.of(P7_PLUG_REF));
    // EPTE setup
    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Uri(P4_CTP_URI)))
      .thenReturn(Optional.of(CARD_REF));
    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Uri(TRANSPONDER_P7_LOWER_CTP_URI)))
      .thenReturn(Optional.of(CARD_REF));
    Mockito.when(capabilities.isEpteSupportedOnCard(NE_ID, CARD_REF.type(), CARD_REF.mode()))
      .thenReturn(true);
    ProtectionGroupRef protectionGroup = Mockito.mock();
    Mockito.when(protectionGroupFinder.findByRelatedSlcZEndpointCtp(NE_ID, P4_CTP_URI))
      .thenReturn(Optional.empty());
    Mockito.when(protectionGroupFinder.findByRelatedSlcZEndpointCtp(NE_ID, TRANSPONDER_P7_LOWER_CTP_URI))
      .thenReturn(Optional.of(protectionGroup));
    Mockito.when(protectionGroup.uri()).thenReturn(new Uri(PROTECTION_GROUP_URI));

    // When
    var result = prepareProvisionRequest.prepareRequest(NE_ID, crmSegmentRequestDto);
    // Then
    List<Request> expected = new ArrayList<>(List.of(
      new CtpRequest(NE_ID, P4_CTP_URI, false, new HashSet<>(), provisionApi),
      new PtpParamsRequest(NE_ID, P4_PTP_URI, true, new HashSet<>(), provisionApi),
      new CtpRequest(NE_ID, TRANSPONDER_P7_LOWER_CTP_URI, false, Set.of(), provisionApi),
      new SncRequest(NE_ID, SNC_100G_URI, false, P4_CTP_URI, TRANSPONDER_P7_LOWER_CTP_URI, provisionApi),
      new EpteRequest(NE_ID, TRANSPONDER_P7_LOWER_CTP_URI, PROTECTION_GROUP_URI, provisionApi)
    ));
    Assertions.assertEquals(expected, result);
  }

  @Test
  void testIngressTransponder400G() {
    // Given
    CrmSegmentRequestDto crmSegmentRequestDto = new CrmSegmentRequestDto();
    crmSegmentRequestDto.neId = NE_ID;
    crmSegmentRequestDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    crmSegmentRequestDto.id = SEGMENT_ID;
    crmSegmentRequestDto.srcTp = TRANSPONDER_P2_CTP_AID;
    crmSegmentRequestDto.dstTp = P3_PTP_AID;
    crmSegmentRequestDto.setEthLabel(new Whole());
    crmSegmentRequestDto.portParams = List.of(new OpticalParameters.SelectedParameter(ParameterName.GLQ, new Value.Enum("ETH-400G")));
    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Aid(TRANSPONDER_P2_CTP_AID))).thenReturn(Optional.of(CARD_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlug(NE_ID, new Aid(P3_PLUG_AID))).thenReturn(P3_PLUG_REF);
    Mockito.when(rrmEthDbResourcesFacade.findCtp(Mockito.eq(NE_ID), Mockito.any(Uri.class))).thenReturn(Optional.empty());
    Mockito.when(rrmEthDbResourcesFacade.findPtp(NE_ID, new Aid(P3_PTP_AID))).thenReturn(Optional.of(P3_1_PTP_REF));
    Mockito.when(rrmEthDbResourcesFacade.findCtpExtended(NE_ID, new Aid(TRANSPONDER_P2_CTP_AID)))
      .thenReturn(Optional.of(TRANSPONDER_P2_CTP_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlugFromCtp(NE_ID, new Aid(TRANSPONDER_P2_CTP_AID)))
      .thenReturn(Optional.of(P2_TRANSPONDER_PLUG_REF));
    // When
    var result = prepareProvisionRequest.prepareRequest(NE_ID, crmSegmentRequestDto);
    // Then
    List<Request> expected = new ArrayList<>(List.of(
      new CtpRequest(NE_ID, P3_CTP_URI, false, new HashSet<>(), provisionApi),
      new PtpParamsRequest(NE_ID, P3_PTP_URI, true, new HashSet<>(), provisionApi),
      new CtpRequest(NE_ID, TRANSPONDER_P2_LOWER_CTP_URI, false, Set.of(), provisionApi),
      new SncRequest(NE_ID, SNC_400G_URI, false, P3_CTP_URI, TRANSPONDER_P2_LOWER_CTP_URI, provisionApi)
    ));
    Assertions.assertEquals(expected, result);
  }

  @Test
  void testIngressMuxponderNormalCPlugTributaryPort2() {
    // Given
    CrmSegmentRequestDto crmSegmentRequestDto = new CrmSegmentRequestDto();
    crmSegmentRequestDto.neId = NE_ID;
    crmSegmentRequestDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    crmSegmentRequestDto.id = SEGMENT_ID;
    crmSegmentRequestDto.srcTp = MUXPONDER_P2_CTP_AID;
    crmSegmentRequestDto.dstTp = P4_PTP_AID;
    crmSegmentRequestDto.setEthLabel(new Container(List.of(2), List.of(2)));
    crmSegmentRequestDto.portParams = List.of(new OpticalParameters.SelectedParameter(
      ParameterName.GLQ, new Value.Enum("ETH-100G")
    ));

    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Aid(MUXPONDER_P2_CTP_AID))).thenReturn(Optional.of(CARD_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlug(NE_ID, new Aid(P4_PLUG_AID))).thenReturn(P4_PLUG_REF);

    Mockito.when(rrmEthDbResourcesFacade.findPtp(NE_ID, new Aid(P4_PTP_AID))).thenReturn(Optional.of(P4_PTP_REF));

    Mockito.when(rrmEthDbResourcesFacade.findCtp(Mockito.eq(NE_ID), Mockito.eq(new Uri(P4_CTP_URI))))
      .thenReturn(Optional.empty());

    Mockito.when(rrmEthDbResourcesFacade.findCtpExtended(NE_ID, new Aid(MUXPONDER_P2_CTP_AID)))
      .thenReturn(Optional.of(MUXPONDER_P2_CTP_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlugFromCtp(NE_ID, new Aid(MUXPONDER_P2_CTP_AID)))
      .thenReturn(Optional.of(P2_MUXPONDER_PLUG_REF));

    Mockito.when(rrmEthDbResourcesFacade.findCtpUriByUriPrefixAndParams(NE_ID, MUXPONDER_P2_LOWER_CTP_URI_PREFIX, 2, List.of(2L)))
      .thenReturn(Optional.empty());

    Mockito.when(provisionApi.getAvailableCtpEntity(NetworkElementID.create(NE_ID), MUXPONDER_P2_LOWER_CTP_URI_ID_2))
      .thenReturn(Optional.of(2));

    // When
    var result = prepareProvisionRequest.prepareRequest(NE_ID, crmSegmentRequestDto);
    // Then
    Set<MoCimParameter> p2CtpParams = new HashSet<>();
    p2CtpParams.add(new MoCimParameter("flexontu", "tp", new CimValue.Int(2)));
    p2CtpParams.add(new MoCimParameter("flexontu", "ts", new CimValue.IntList(List.of(2L))));
    List<Request> expected = new ArrayList<>(List.of(
      new CtpRequest(NE_ID, P4_CTP_URI, false, new HashSet<>(), provisionApi),
      new PtpParamsRequest(NE_ID, P4_PTP_URI, true, Set.of(), provisionApi),
      new CtpRequest(NE_ID, MUXPONDER_P2_LOWER_CTP_URI_ID_2, false, p2CtpParams, provisionApi),
      new SncRequest(NE_ID, SNC_100G_URI, false, P4_CTP_URI, MUXPONDER_P2_LOWER_CTP_URI_ID_2, provisionApi)
    ));
    Assertions.assertEquals(expected, result);
  }

  @Test
  void testIngressMuxponderNormalCPlugTributaryPort2CtpOccupied() {
    // Given
    CrmSegmentRequestDto crmSegmentRequestDto = new CrmSegmentRequestDto();
    crmSegmentRequestDto.neId = NE_ID;
    crmSegmentRequestDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    crmSegmentRequestDto.id = SEGMENT_ID;
    crmSegmentRequestDto.srcTp = MUXPONDER_P2_CTP_AID;
    crmSegmentRequestDto.dstTp = P4_PTP_AID;
    crmSegmentRequestDto.setEthLabel(new Container(List.of(2), List.of(2)));
    crmSegmentRequestDto.portParams = List.of(new OpticalParameters.SelectedParameter(
      ParameterName.GLQ, new Value.Enum("ETH-100G")
    ));

    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Aid(MUXPONDER_P2_CTP_AID))).thenReturn(Optional.of(CARD_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlug(NE_ID, new Aid(P4_PLUG_AID))).thenReturn(P4_PLUG_REF);

    Mockito.when(rrmEthDbResourcesFacade.findPtp(NE_ID, new Aid(P4_PTP_AID))).thenReturn(Optional.of(P3_1_PTP_REF));
    Mockito.when(rrmEthDbResourcesFacade.findCtp(Mockito.eq(NE_ID), Mockito.eq(new Uri(P4_CTP_URI))))
      .thenReturn(Optional.empty());

    Mockito.when(rrmEthDbResourcesFacade.findCtpExtended(NE_ID, new Aid(MUXPONDER_P2_CTP_AID)))
      .thenReturn(Optional.of(MUXPONDER_P2_CTP_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlugFromCtp(NE_ID, new Aid(MUXPONDER_P2_CTP_AID)))
      .thenReturn(Optional.of(P2_MUXPONDER_PLUG_REF));

    Mockito.when(rrmEthDbResourcesFacade.findCtpUriByUriPrefixAndParams(NE_ID, MUXPONDER_P2_LOWER_CTP_URI_PREFIX, 2, List.of(2L)))
      .thenReturn(Optional.empty());

    Mockito.when(provisionApi.getAvailableCtpEntity(NetworkElementID.create(NE_ID), MUXPONDER_P2_LOWER_CTP_URI_ID_2))
      .thenReturn(Optional.empty());

    Mockito.when(provisionApi.getAvailableCtpEntity(NetworkElementID.create(NE_ID), MUXPONDER_P2_LOWER_CTP_URI_PREFIX))
      .thenReturn(Optional.of(1));

    // When
    var result = prepareProvisionRequest.prepareRequest(NE_ID, crmSegmentRequestDto);
    // Then
    Set<MoCimParameter> p2CtpParams = new HashSet<>();
    p2CtpParams.add(new MoCimParameter("flexontu", "tp", new CimValue.Int(2)));
    p2CtpParams.add(new MoCimParameter("flexontu", "ts", new CimValue.IntList(List.of(2L))));
    List<Request> expected = new ArrayList<>(List.of(
      new CtpRequest(NE_ID, P4_CTP_URI, false, new HashSet<>(), provisionApi),
      new PtpParamsRequest(NE_ID, P4_PTP_URI, true, Set.of(), provisionApi),
      new CtpRequest(NE_ID, MUXPONDER_P2_LOWER_CTP_URI, false, p2CtpParams, provisionApi),
      new SncRequest(NE_ID, SNC_100G_URI, false, P4_CTP_URI, MUXPONDER_P2_LOWER_CTP_URI, provisionApi)
    ));
    Assertions.assertEquals(expected, result);
  }

  @Test
  void testIngressMuxponderNormalCPlugNoFreeCtps() {
    // Given
    CrmSegmentRequestDto crmSegmentRequestDto = new CrmSegmentRequestDto();
    crmSegmentRequestDto.neId = NE_ID;
    crmSegmentRequestDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    crmSegmentRequestDto.id = SEGMENT_ID;
    crmSegmentRequestDto.srcTp = MUXPONDER_P2_CTP_AID;
    crmSegmentRequestDto.dstTp = P4_PTP_AID;
    crmSegmentRequestDto.setEthLabel(new Container(List.of(1), List.of(1)));
    crmSegmentRequestDto.portParams = List.of(new OpticalParameters.SelectedParameter(
      ParameterName.GLQ, new Value.Enum("ETH-100G")
    ));

    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Aid(MUXPONDER_P2_CTP_AID))).thenReturn(Optional.of(CARD_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlug(NE_ID, new Aid(P4_PLUG_AID))).thenReturn(P4_PLUG_REF);

    Mockito.when(rrmEthDbResourcesFacade.findPtp(NE_ID, new Aid(P4_PTP_AID))).thenReturn(Optional.of(P3_1_PTP_REF));
    Mockito.when(rrmEthDbResourcesFacade.findCtp(Mockito.eq(NE_ID), Mockito.eq(new Uri(P4_CTP_URI))))
      .thenReturn(Optional.empty());

    Mockito.when(rrmEthDbResourcesFacade.findCtpExtended(NE_ID, new Aid(MUXPONDER_P2_CTP_AID)))
      .thenReturn(Optional.of(MUXPONDER_P2_CTP_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlugFromCtp(NE_ID, new Aid(MUXPONDER_P2_CTP_AID)))
      .thenReturn(Optional.of(P2_MUXPONDER_PLUG_REF));

    Mockito.when(rrmEthDbResourcesFacade.findCtpUriByUriPrefixAndParams(NE_ID, MUXPONDER_P2_LOWER_CTP_URI_PREFIX, 1, List.of(1L)))
      .thenReturn(Optional.empty());

    // When - Then
    Assertions.assertThrows(EthProvisioningException.class,
      () -> prepareProvisionRequest.prepareRequest(NE_ID, crmSegmentRequestDto));
  }

  @Test
  void testIngressMuxponderExclusiveGroupsPlugPtpProvisioned() {
    // Given
    CrmSegmentRequestDto crmSegmentRequestDto = new CrmSegmentRequestDto();
    crmSegmentRequestDto.neId = NE_ID;
    crmSegmentRequestDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    crmSegmentRequestDto.id = SEGMENT_ID;
    crmSegmentRequestDto.srcTp = MUXPONDER_P2_CTP_AID;
    crmSegmentRequestDto.dstTp = P3_1_PTP_AID;
    crmSegmentRequestDto.setEthLabel(new Container(List.of(1), List.of(1)));
    crmSegmentRequestDto.portParams = List.of(new OpticalParameters.SelectedParameter(
      ParameterName.GLQ, new Value.Enum("ETH-100G")
    ));

    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Aid(MUXPONDER_P2_CTP_AID))).thenReturn(Optional.of(CARD_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlug(NE_ID, new Aid(P3_PLUG_AID))).thenReturn(P3_PLUG_REF);

    Mockito.when(rrmEthDbResourcesFacade.findPtp(NE_ID, new Aid(P3_1_PTP_AID))).thenReturn(Optional.of(P3_1_PTP_REF));
    Mockito.when(rrmEthDbResourcesFacade.findCtp(Mockito.eq(NE_ID), Mockito.eq(new Uri(P3_1_CTP_URI))))
      .thenReturn(Optional.empty());

    Mockito.when(rrmEthDbResourcesFacade.findCtpExtended(NE_ID, new Aid(MUXPONDER_P2_CTP_AID)))
      .thenReturn(Optional.of(MUXPONDER_P2_CTP_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlugFromCtp(NE_ID, new Aid(MUXPONDER_P2_CTP_AID)))
      .thenReturn(Optional.of(P2_MUXPONDER_PLUG_REF));

    Mockito.when(rrmEthDbResourcesFacade.findCtpUriByUriPrefixAndParams(NE_ID, MUXPONDER_P2_LOWER_CTP_URI_PREFIX, 1, List.of(1L)))
      .thenReturn(Optional.empty());

    Mockito.when(provisionApi.getAvailableCtpEntity(NetworkElementID.create(NE_ID), MUXPONDER_P2_LOWER_CTP_URI))
      .thenReturn(Optional.of(1));

    // When
    var result = prepareProvisionRequest.prepareRequest(NE_ID, crmSegmentRequestDto);
    // Then
    Set<MoCimParameter> p2CtpParams = new HashSet<>();
    p2CtpParams.add(new MoCimParameter("flexontu", "tp", new CimValue.Int(1)));
    p2CtpParams.add(new MoCimParameter("flexontu", "ts", new CimValue.IntList(List.of(1L))));
    List<Request> expected = new ArrayList<>(List.of(
      new CtpRequest(NE_ID, P3_1_CTP_URI, false, new HashSet<>(), provisionApi),
      new PtpParamsRequest(NE_ID, P3_1_PTP_URI, true, Set.of(), provisionApi),
      new CtpRequest(NE_ID, MUXPONDER_P2_LOWER_CTP_URI, false, p2CtpParams, provisionApi),
      new SncRequest(NE_ID, SNC_100G_URI, false, P3_1_CTP_URI, MUXPONDER_P2_LOWER_CTP_URI, provisionApi)
    ));
    Assertions.assertEquals(expected, result);
  }

  @Test
  void testIngressMuxponderExclusiveGroupsPlugPtpUnprovisioned() {
    // Given
    CrmSegmentRequestDto crmSegmentRequestDto = new CrmSegmentRequestDto();
    crmSegmentRequestDto.neId = NE_ID;
    crmSegmentRequestDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    crmSegmentRequestDto.id = SEGMENT_ID;
    crmSegmentRequestDto.srcTp = MUXPONDER_P2_CTP_AID;
    crmSegmentRequestDto.dstTp = P3_1_PTP_AID;
    crmSegmentRequestDto.setEthLabel(new Container(List.of(1), List.of(1)));
    crmSegmentRequestDto.portParams = List.of(new OpticalParameters.SelectedParameter(
      ParameterName.GLQ, new Value.Enum("ETH-100G")
    ));

    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Aid(MUXPONDER_P2_CTP_AID))).thenReturn(Optional.of(CARD_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlug(NE_ID, new Aid(P3_PLUG_AID))).thenReturn(P3_PLUG_REF);

    Mockito.when(rrmEthDbResourcesFacade.findPtp(NE_ID, new Aid(P3_1_PTP_AID))).thenReturn(Optional.empty());

    Mockito.when(rrmEthDbResourcesFacade.findCtp(Mockito.eq(NE_ID), Mockito.eq(new Uri(P3_1_CTP_URI))))
      .thenReturn(Optional.empty());

    Mockito.when(rrmEthDbResourcesFacade.findCtpExtended(NE_ID, new Aid(MUXPONDER_P2_CTP_AID)))
      .thenReturn(Optional.of(MUXPONDER_P2_CTP_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlugFromCtp(NE_ID, new Aid(MUXPONDER_P2_CTP_AID)))
      .thenReturn(Optional.of(P2_MUXPONDER_PLUG_REF));

    Mockito.when(rrmEthDbResourcesFacade.findCtpUriByUriPrefixAndParams(NE_ID, MUXPONDER_P2_LOWER_CTP_URI_PREFIX, 1, List.of(1L)))
      .thenReturn(Optional.empty());

    Mockito.when(provisionApi.getAvailableCtpEntity(NetworkElementID.create(NE_ID), MUXPONDER_P2_LOWER_CTP_URI))
      .thenReturn(Optional.of(1));

    // When
    var result = prepareProvisionRequest.prepareRequest(NE_ID, crmSegmentRequestDto);
    // Then
    Set<MoCimParameter> p2CtpParams = new HashSet<>();
    p2CtpParams.add(new MoCimParameter("flexontu", "tp", new CimValue.Int(1)));
    p2CtpParams.add(new MoCimParameter("flexontu", "ts", new CimValue.IntList(List.of(1L))));
    List<Request> expected = new ArrayList<>(List.of(
      new PtpRequest(NE_ID, P3_1_PTP_URI, false, provisionApi),
      new CtpRequest(NE_ID, P3_1_CTP_URI, false, provisionApi),
      new PtpParamsRequest(NE_ID, P3_1_PTP_URI, false, Set.of(), provisionApi),
      new CtpRequest(NE_ID, MUXPONDER_P2_LOWER_CTP_URI, false, p2CtpParams, provisionApi),
      new SncRequest(NE_ID, SNC_100G_URI, false, P3_1_CTP_URI, MUXPONDER_P2_LOWER_CTP_URI, provisionApi)
    ));
    Assertions.assertEquals(expected, result);
  }

  @Test
  void testAdopt() {
    // Given
    CrmSegmentRequestDto crmSegmentRequestDto = new CrmSegmentRequestDto();
    crmSegmentRequestDto.neId = NE_ID;
    crmSegmentRequestDto.requestType = CrmSegmentRequestDto.RequestType.ADOPT;
    crmSegmentRequestDto.id = SEGMENT_ID;
    crmSegmentRequestDto.srcTp = MUXPONDER_P2_CTP_AID;
    crmSegmentRequestDto.dstTp = P3_1_PTP_AID;
    crmSegmentRequestDto.setEthLabel(new Container(List.of(1), List.of(1)));
    crmSegmentRequestDto.portParams = List.of(new OpticalParameters.SelectedParameter(
      ParameterName.GLQ, new Value.Enum("ETH-100G")
    ));

    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Aid(MUXPONDER_P2_CTP_AID))).thenReturn(Optional.of(CARD_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlug(NE_ID, new Aid(P3_PLUG_AID))).thenReturn(P3_PLUG_REF);

    Mockito.when(rrmEthDbResourcesFacade.findPtp(NE_ID, new Aid(P3_1_PTP_AID))).thenReturn(Optional.of(P3_1_PTP_REF));
    Mockito.when(rrmEthDbResourcesFacade.findCtpExtended(NE_ID, new Aid(MUXPONDER_P2_CTP_AID)))
      .thenReturn(Optional.of(MUXPONDER_P2_CTP_REF));
    Mockito.when(rrmEthDbResourcesFacade.findPlugFromCtp(NE_ID, new Aid(MUXPONDER_P2_CTP_AID)))
      .thenReturn(Optional.of(P2_MUXPONDER_PLUG_REF));

    Mockito.when(rrmEthDbResourcesFacade.findCtpUriByUriPrefixAndParams(NE_ID, MUXPONDER_P2_LOWER_CTP_URI_PREFIX, 1, List.of(1L)))
      .thenReturn(Optional.of(new Uri(MUXPONDER_P2_LOWER_CTP_URI)));

    Mockito.when(rrmEthDbResourcesFacade.findCtp(NE_ID, new Uri(P3_1_CTP_URI))).thenReturn(Optional.of(new CtpRef(NE_ID, null, null, null)));
    Mockito.when(rrmEthDbResourcesFacade.findCtp(NE_ID, new Uri(MUXPONDER_P2_LOWER_CTP_URI))).thenReturn(Optional.of(new CtpRef(NE_ID, null, null, null)));

    Mockito.when(rrmEthDbResourcesFacade.findSncUriByUriPrefixAndEndpoints(NE_ID, SNC_100G_URI,
      Set.of(P3_1_CTP_URI), Set.of(MUXPONDER_P2_LOWER_CTP_URI))).thenReturn(Optional.of(new Uri(SNC_100G_URI + "/1")));
    // When
    var result = prepareProvisionRequest.prepareRequest(NE_ID, crmSegmentRequestDto);
    // Then
    Set<MoCimParameter> p2CtpParams = new HashSet<>();
    p2CtpParams.add(new MoCimParameter("flexontu", "tp", new CimValue.Int(1)));
    p2CtpParams.add(new MoCimParameter("flexontu", "ts", new CimValue.IntList(List.of(1L))));
    List<Request> expected = new ArrayList<>(List.of(
      new CtpRequest(NE_ID, P3_1_CTP_URI, true, new HashSet<>(), provisionApi),
      new PtpParamsRequest(NE_ID, P3_1_PTP_URI, true, Set.of(), provisionApi),
      new CtpRequest(NE_ID, MUXPONDER_P2_LOWER_CTP_URI, true, p2CtpParams, provisionApi),
      new SncRequest(NE_ID, SNC_100G_URI + "/1", true, P3_1_CTP_URI, MUXPONDER_P2_LOWER_CTP_URI, provisionApi)
    ));
    Assertions.assertEquals(expected, result);
  }

  @Test
  void testProvisionWhenNetworkCtpOrCardNotInDb() {
    // Given
    CrmSegmentRequestDto crmSegmentRequestDto = new CrmSegmentRequestDto();
    crmSegmentRequestDto.neId = NE_ID;
    crmSegmentRequestDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    crmSegmentRequestDto.id = SEGMENT_ID;
    crmSegmentRequestDto.srcTp = MUXPONDER_P2_CTP_AID;
    crmSegmentRequestDto.dstTp = P3_1_PTP_AID;
    crmSegmentRequestDto.setEthLabel(new Container(List.of(1), List.of(1)));
    crmSegmentRequestDto.portParams = List.of(new OpticalParameters.SelectedParameter(
      ParameterName.GLQ, new Value.Enum("ETH-100G")
    ));

    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Aid(MUXPONDER_P2_CTP_AID))).thenReturn(Optional.empty());

    // When & Then
    Assertions.assertThrows(EthProvisioningException.class, () ->
      prepareProvisionRequest.prepareRequest(NE_ID, crmSegmentRequestDto)
    );
  }
}
