/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capabilityprovider.mo.MoCapabilityProviderConfiguration;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.config.f8.croma.SlcActiveEndpoint;
import com.adva.nlms.mediation.config.f8.croma.api.CromaMOService;
import com.adva.nlms.mediation.config.f8.croma.slc.api.Slc;
import com.adva.nlms.mediation.config.f8.croma.slc.api.SlcEndpoint;
import com.adva.nlms.mediation.ec.model.F8OperState;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.CardResources;
import com.adva.nlms.mediation.mo.inventory.resources.CrossConnectRef;
import com.adva.nlms.mediation.mo.inventory.resources.CrossConnectResources;
import com.adva.nlms.mediation.mo.inventory.resources.CtpBuilder;
import com.adva.nlms.mediation.mo.inventory.resources.CtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.LineProtectionGroupRef;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpBuilder;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.topology.NEData;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.opticalparameters.api.OpticalParameters.SelectedParameter;
import com.adva.nlms.opticalparameters.api.Value;
import com.adva.nlms.opticalparameters.api.enums.ParameterName;
import com.adva.nlms.resource.crm.model.wdm.CrmWdmModelConfiguration;
import com.adva.nlms.resource.crm.model.wdm.CrmWdmModelDAO;
import com.adva.nlms.resource.crm.model.wdm.LtpInfo;
import com.adva.nlms.resource.crm.model.wdm.SpectrumInfo;
import com.adva.nlms.resource.crm.model.wdm.TtpInfo;
import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;
import com.adva.nlms.resource.mediator.f8.RRMConfiguration;
import com.adva.nlms.resource.mediator.f8.SegmentRequestFailedException;
import com.adva.nlms.resource.mediator.f8.wdm.adapters.persistence.RrmPersistenceConfiguration;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.CrmNodePosition;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.RrmSegmentRepository;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.WdmSegment;
import com.adva.nlms.resource.provision.f8.api.in.CimProvisionRequestParametersCreator;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

class WdmSegmentAdoptRequestTest {
  private static final MoCapabilityProvider moCapabilityProvider = new MoCapabilityProviderConfiguration().moCapabilityProvider();
  private static final CimProvisionRequestParametersCreator parametersCreator = new RRMConfiguration().cimProvisionRequestParametersCreator((moCapabilityProvider));
  private static final String SW_VERSION = "6.5.1";
  private final CrmWdmModelDAO modelDao = new CrmWdmModelConfiguration().crmWdmModelDAO();
  private final RrmSegmentRepository repository = new RrmPersistenceConfiguration().rrmSegmentRepository();
  private final PtpResources ptpResources = mock();
  private final CromaMOService cromaMOService = mock();
  private final SegmentSetAdminStateRequest segmentSetAdminStateRequest = mock();
  private final CtpResources ctpResources = mock();
  private final Provision provision = mock();
  private final CardResources cardResources = mock();
  private final CrossConnectResources crossConnectResources = mock();
  private final FiberTracer fiberTracer = mock();
  private final NEDataProvider neDataProvider = mock();
  private final SegmentAdoptRequest segmentAdoptRequest = new WdmSegmentRequestConfig().wdmSegmentAdoptRequest(modelDao,
    repository,
    parametersCreator,
    provision,
    ctpResources,
    cardResources,
    crossConnectResources,
    ptpResources,
    cromaMOService,
    moCapabilityProvider,
    fiberTracer,
    segmentSetAdminStateRequest,
    neDataProvider);

  private final static String REQUEST_ID = "segment id";
  private final static int NE_ID = 23;
  private final static SpectrumInfo OPAQUE_SPECTRUM = new SpectrumInfo();
  private final static SpectrumInfo NON_OPAQUE_SPECTRUM = new SpectrumInfo(SpectrumInfo.Type.TYPE_FREQUENCY,
    192000000, 196000000, 3125, 6250, new SpectrumInfo.SlotWidthSupport(37500, 4918750, 12500), false);

  @Test
  void transitIlaHappyPath() throws ObjectInUseException {
    var srcAid = "Port-1/2/n";
    var dstAid = "Port-3/4/n";
    // Given
    modelDao.addLtp(NE_ID, new LtpInfo(srcAid, OPAQUE_SPECTRUM, 1, false));
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, OPAQUE_SPECTRUM, 2, false));

    // When
    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.forwardSequenceNumber = 3;
    request.reverseSequenceNumber = 2;
    setNetworkElementVersion();
    var context = segmentAdoptRequest.buildContext(request);
    var result = segmentAdoptRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
    assertEquals(0, segment.getSlcId());
    assertEquals(CrmNodePosition.TRANSIT, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(dstAid, segment.getAidDstTp());
    assertNull(segment.getTransponderCtpUri());
  }

  @Test
  void conflictingSegment() throws ObjectInUseException {
    final var srcAid = "Port-1/2/n";
    final var dstAid = "Port-3/4/n";
    final var channel = 196000000;
    final var bandwidth = 37500;
    final var slcId = 1;
    // Given
    modelDao.addLtp(NE_ID, new LtpInfo(srcAid, NON_OPAQUE_SPECTRUM, 1, false));
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, NON_OPAQUE_SPECTRUM, 2, false));
    var conflictingSegment = WdmSegment.newSegment()
      .withSegmentRequestId("segment id 2")
      .withNeId(NE_ID)
      .withNodePosition(CrmNodePosition.TRANSIT)
      .withAidSrcTp(srcAid)
      .withAidDstTp(dstAid)
      .withChannelLabel(channel)
      .withSlcId(slcId)
      .build();
    repository.store(conflictingSegment);

    // When
    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.channelSrcLabel = channel;
    request.slotWidthSrcLabel = bandwidth;
    request.forwardSequenceNumber = 3;
    request.reverseSequenceNumber = 2;
    request.adminState = CrmSegmentRequestDto.AdminState.UNKNOWN;
    var slc = new Slc(
      0, NE_ID, slcId, "",
      new SlcEndpoint(1, 0, 0, new SlcActiveEndpoint("/mit/me/1/croma/degree/1", Collections.singletonList("/mit/me/1/croma/degree/1")), null),
      new SlcEndpoint(2, 0, 0, new SlcActiveEndpoint("/mit/me/1/croma/degree/2", Collections.singletonList("/mit/me/1/croma/degree/2")), null),
      List.of(bandwidth), List.of(channel),
      null, null, null
    );
    when(cromaMOService.getSlcForNe(NE_ID)).thenReturn(List.of(slc));
    when(cromaMOService.getSlcForNeAndSlcId(NE_ID, slcId)).thenReturn(Optional.of(slc));

    setNetworkElementVersion();
    var context = segmentAdoptRequest.buildContext(request);
    var result = segmentAdoptRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
    assertEquals(CrmNodePosition.TRANSIT, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(dstAid, segment.getAidDstTp());
    assertNull(segment.getTransponderCtpUri());
    assertEquals(0, conflictingSegment.getSlcId());
  }

  @Test
  void adoptSlcTransitReversed() throws ObjectInUseException {
    // Given
    final var srcAid = "Port-1/2/n";
    final var dstAid = "Port-3/4/n";
    final var channel = 196000000;
    final var bandwidth = 37500;
    final var slcId = 1;
    modelDao.addLtp(NE_ID, new LtpInfo(srcAid, NON_OPAQUE_SPECTRUM, 1, false));
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, NON_OPAQUE_SPECTRUM, 2, false));

    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.channelSrcLabel = channel;
    request.slotWidthSrcLabel = bandwidth;
    request.forwardSequenceNumber = 3;
    request.reverseSequenceNumber = 2;
    request.adminState = CrmSegmentRequestDto.AdminState.UNKNOWN;
    var slc = new Slc(
      0, NE_ID, slcId, "",
      new SlcEndpoint(1, 0, 0, new SlcActiveEndpoint("/mit/me/1/croma/degree/2", Collections.singletonList("/mit/me/1/croma/degree/1")), null),
      new SlcEndpoint(2, 0, 0, new SlcActiveEndpoint("/mit/me/1/croma/degree/1", Collections.singletonList("/mit/me/1/croma/degree/2")), null),
      List.of(bandwidth), List.of(channel),
      null, null, null
    );
    when(cromaMOService.getSlcForNe(NE_ID)).thenReturn(List.of(slc));
    when(cromaMOService.getSlcForNeAndSlcId(NE_ID, slcId)).thenReturn(Optional.of(slc));

    setNetworkElementVersion();

    // When
    var context = segmentAdoptRequest.buildContext(request);
    var result = segmentAdoptRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
    assertEquals(1, segment.getSlcId());
    assertEquals(CrmNodePosition.TRANSIT_REVERSED, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(dstAid, segment.getAidDstTp());
    assertNull(segment.getTransponderCtpUri());
  }

  @Test
  void adoptSlcEgrees() throws ObjectInUseException {
    final var ttpName = "Port-1/2/n1";
    final var dstAid = "Port-1/2/n1";
    final var srcAid = "Port-3/4/n";
    final var ctpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1/ctp/otuc2";
    final var cardUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card";
    final var cardAid = "Card-1/2";
    final var ptpUri = new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1");
    final var channel = 196000000;
    final var bandwidth = 37500;
    final var slcId = 1;
    // Given
    TtpInfo ttp = WdmSegmentUtil.sampleTtpWithTunableTps(ttpName, dstAid);
    modelDao.addTtp(NE_ID, ttp);
    modelDao.addLtp(NE_ID, new LtpInfo(srcAid, NON_OPAQUE_SPECTRUM, 1, false));

    // When
    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.channelSrcLabel = channel;
    request.slotWidthSrcLabel = bandwidth;
    request.forwardSequenceNumber = 3;
    request.reverseSequenceNumber = 2;
    request.adminState = CrmSegmentRequestDto.AdminState.UNKNOWN;
    request.portParams = new ArrayList<>();
    request.portParams.add(new SelectedParameter(ParameterName.GLQ, new Value.Enum("OTUC2")));
    request.portParams.add(new SelectedParameter(ParameterName.CHANNEL, new Value.Numeric(new BigDecimal(channel), "MHz")));
    when(ptpResources.findCard(NE_ID, new Aid(ttpName)))
      .thenReturn(Optional.of(new CardRef(NE_ID, new Aid(cardAid), new Uri(cardUri), "OF-2D16DCT", F8OperState.ANR.getValue())));
    when(ptpResources.findPlug(NE_ID, new Aid(ttpName)))
      .thenReturn(Optional.of(PlugRef.builder()
        .setNeId(NE_ID)
        .setAid("")
        .setUri("")
        .setType("CFP2-448G-#BDCTC-SM-LC")
        .build())
      );
    var ctp = CtpBuilder.newBuilder().withUri(new Uri(ctpUri)).build();
    when(ctpResources.findCtpExtended(NE_ID, new Uri(ctpUri))).thenReturn(Optional.of(ctp));
    var ptp = PtpBuilder.newBuilder()
      .withUri(ptpUri)
      .withTunedFrequency(channel)
      .build();
    when(ptpResources.findPtpExtended(NE_ID, new Aid(ttpName))).thenReturn(Optional.of(ptp));
    var slc = new Slc(
      0, NE_ID, slcId, "",
      new SlcEndpoint(2, 0, 0, new SlcActiveEndpoint("/mit/me/1/croma/degree/1", Collections.singletonList("/mit/me/1/croma/degree/1")), null),
      new SlcEndpoint(1, 0, 0, new SlcActiveEndpoint(ctpUri, Collections.singletonList(ctpUri)), null),
      List.of(bandwidth), List.of(channel),
      null, null, null
    );
    when(cromaMOService.getSlcForNe(NE_ID)).thenReturn(List.of(slc));
    when(cromaMOService.getSlcForNeAndSlcId(NE_ID, slcId)).thenReturn(Optional.of(slc));
    setNetworkElementVersion();
    var context = segmentAdoptRequest.buildContext(request);
    var result = segmentAdoptRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
    assertEquals(1, segment.getSlcId());
    assertEquals(CrmNodePosition.EGRESS, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(dstAid, segment.getAidDstTp());
    assertEquals(ctpUri, segment.getTransponderCtpUri());
    verify(provision).modifySlcPathNodeNumber(NE_ID, segment.getSlcId(), 3, 2);
  }

  @Test
  void conflictingSegmentAddDrop() throws ObjectInUseException {
    final var ttpName = "Port-1/2/n1";
    final var srcAid = "Port-1/2/n1";
    final var dstAid = "Port-3/4/n";
    final var ctpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1/ctp/otuc2";
    final var cardUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card";
    final var cardAid = "Card-1/2";
    final var ptpUri = new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1");
    final var channel = 196000000;
    final var bandwidth = 37500;
    final var slcId = 1;
    // Given
    TtpInfo ttp = WdmSegmentUtil.sampleTtpWithTunableTps(ttpName, srcAid);
    modelDao.addTtp(NE_ID, ttp);
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, NON_OPAQUE_SPECTRUM, 1, false));
    var conflictingSegment = WdmSegment.newSegment()
      .withSegmentRequestId("segment id 2")
      .withNeId(NE_ID)
      .withNodePosition(CrmNodePosition.TRANSIT)
      .withAidSrcTp(srcAid)
      .withAidDstTp(dstAid)
      .withChannelLabel(channel)
      .withSlcId(slcId)
      .build();
    repository.store(conflictingSegment);

    // When
    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.channelSrcLabel = channel;
    request.slotWidthSrcLabel = bandwidth;
    request.forwardSequenceNumber = 1;
    request.reverseSequenceNumber = 2;
    request.adminState = CrmSegmentRequestDto.AdminState.UNKNOWN;
    request.portParams = new ArrayList<>();
    request.portParams.add(new SelectedParameter(ParameterName.GLQ, new Value.Enum("OTUC2")));
    request.portParams.add(new SelectedParameter(ParameterName.CHANNEL, new Value.Numeric(new BigDecimal(channel), "MHz")));
    when(ptpResources.findCard(NE_ID, new Aid(ttpName)))
      .thenReturn(Optional.of(new CardRef(NE_ID, new Aid(cardAid), new Uri(cardUri), "OF-2D16DCT", F8OperState.ANR.getValue())));
    when(ptpResources.findPlug(NE_ID, new Aid(ttpName)))
      .thenReturn(Optional.of(PlugRef.builder()
        .setNeId(NE_ID)
        .setAid("")
        .setUri("")
        .setType("CFP2-448G-#BDCTC-SM-LC")
        .build())
      );
    var ctp = CtpBuilder.newBuilder().withUri(new Uri(ctpUri)).build();
    when(ctpResources.findCtpExtended(NE_ID, new Uri(ctpUri))).thenReturn(Optional.of(ctp));
    var ptp = PtpBuilder.newBuilder()
      .withUri(ptpUri)
      .withTunedFrequency(channel)
      .build();
    when(ptpResources.findPtpExtended(NE_ID, new Aid(ttpName))).thenReturn(Optional.of(ptp));
    var slc = new Slc(
      0, NE_ID, slcId, "",
      new SlcEndpoint(2, 0, 0, new SlcActiveEndpoint("/mit/me/1/croma/degree/1", Collections.singletonList("/mit/me/1/croma/degree/1")), null),
      new SlcEndpoint(1, 0, 0, new SlcActiveEndpoint(ctpUri, Collections.singletonList(ctpUri)), null),
      List.of(bandwidth), List.of(channel),
      null, null, null
    );
    when(cromaMOService.getSlcForNe(NE_ID)).thenReturn(List.of(slc));
    when(cromaMOService.getSlcForNeAndSlcId(NE_ID, slcId)).thenReturn(Optional.of(slc));
    setNetworkElementVersion();
    var context = segmentAdoptRequest.buildContext(request);
    var result = segmentAdoptRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
    assertEquals(1, segment.getSlcId());
    assertEquals(CrmNodePosition.INGRESS, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(dstAid, segment.getAidDstTp());
    assertEquals(ctpUri, segment.getTransponderCtpUri());
    assertEquals(0, conflictingSegment.getSlcId());
    assertNull(conflictingSegment.getTransponderCtpUri());
  }

  @Test
  void transitIlaHappyPathWithAdminStateEnabled() throws ObjectInUseException {
    var srcAid = "Port-1/2/n";
    var dstAid = "Port-3/4/n";
    // Given
    modelDao.addLtp(NE_ID, new LtpInfo(srcAid, OPAQUE_SPECTRUM, 1, false));
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, OPAQUE_SPECTRUM, 2, false));

    // When
    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.forwardSequenceNumber = 3;
    request.reverseSequenceNumber = 2;
    request.adminState = CrmSegmentRequestDto.AdminState.ENABLED;
    setNetworkElementVersion();
    var context = segmentAdoptRequest.buildContext(request);
    var result = segmentAdoptRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    var adminStateContext = new SegmentSetAdminStateContext(1);
    assertNotNull(segment);
    assertEquals(0, segment.getSlcId());
    assertEquals(CrmNodePosition.TRANSIT, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(dstAid, segment.getAidDstTp());
    assertNull(segment.getTransponderCtpUri());
    verify(segmentSetAdminStateRequest).handleEnableAdminstate(request, segment, adminStateContext);
  }

  @Test
  void ingressRoadmWithPluggableLineCardHappyPath() throws ObjectInUseException {
    var ttpName = "Port-1/2/n1";
    var srcAid = "Port-1/2/n1";
    var dstAid = "Port-3/4/n";
    var cardAid = "Card-1/2";
    var cardUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card";
    var ptpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1";
    var ctpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1/ctp/otuc2";
    var slcId = 1;
    var channel = 196000000;
    var bandwidth = 37500;

    // Given
    TtpInfo ttp = WdmSegmentUtil.sampleTtpWithTunableTps(ttpName, srcAid);
    modelDao.addTtp(NE_ID, ttp);
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, NON_OPAQUE_SPECTRUM, 2, false));

    // When
    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.forwardSequenceNumber = 1;
    request.reverseSequenceNumber = 2;
    request.setPointDelta = 1.0;
    request.channelSrcLabel = channel;
    request.slotWidthSrcLabel = bandwidth;
    request.portParams = new ArrayList<>();
    request.portParams.add(new SelectedParameter(ParameterName.GLQ, new Value.Enum("OTUC2")));
    request.portParams.add(new SelectedParameter(ParameterName.CHANNEL, new Value.Numeric(new BigDecimal(channel), "MHz")));
    Aid ttpAid = new Aid(ttpName);
    when(ptpResources.findCard(NE_ID, ttpAid))
      .thenReturn(Optional.of(new CardRef(NE_ID, new Aid(cardAid), new Uri(cardUri), "OF-2D16DCT", F8OperState.ANR.getValue())));
    when(ptpResources.findPlug(NE_ID, ttpAid))
      .thenReturn(Optional.of(PlugRef.builder()
        .setNeId(NE_ID)
        .setAid("")
        .setUri("")
        .setType("CFP2-448G-#BDCTC-SM-LC")
        .build())
      );
    var ctp = CtpBuilder.newBuilder().withUri(new Uri(ctpUri)).build();
    when(ctpResources.findCtpExtended(NE_ID, new Uri(ctpUri))).thenReturn(Optional.of(ctp));
    var ptp = PtpBuilder.newBuilder()
      .withTunedFrequency(channel)
      .withUri(new Uri(ptpUri))
      .build();
    when(ptpResources.findPtpExtended(NE_ID, ttpAid)).thenReturn(Optional.of(ptp));
    var slc = new Slc(
      0, NE_ID, slcId, "",
      new SlcEndpoint(2, 0, 0, new SlcActiveEndpoint("/mit/me/1/croma/degree/2", Collections.singletonList("/mit/me/1/croma/degree/2")), null),
      new SlcEndpoint(1, 0, 0, new SlcActiveEndpoint(ctpUri, Collections.singletonList(ctpUri)), null),
      List.of(bandwidth), List.of(channel),
      null, null, null
    );
    when(cromaMOService.getSlcForNe(NE_ID)).thenReturn(List.of(slc));
    when(cromaMOService.getSlcForNeAndSlcId(NE_ID, slcId)).thenReturn(Optional.of(slc));

    setNetworkElementVersion();
    var context = segmentAdoptRequest.buildContext(request);
    var result = segmentAdoptRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
    assertEquals(slcId, segment.getSlcId());
    assertEquals(CrmNodePosition.INGRESS, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(dstAid, segment.getAidDstTp());
    assertEquals(ttpName, segment.getTtpAid());
    assertEquals(ctpUri, segment.getTransponderCtpUri());
  }

  @Test
  void contextIsNull() {
    var request = new CrmSegmentRequestDto();
    var actual = Assertions.assertThatThrownBy(() -> segmentAdoptRequest.execute(request, null));
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("Context is null");
  }

  @Test
  void contextIsOfInvalidType() {
    var request = new CrmSegmentRequestDto();
    var context = new SegmentSetAdminStateContext(1);
    var actual = Assertions.assertThatThrownBy(() -> segmentAdoptRequest.execute(request, context));
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("Invalid Context type SegmentSetAdminStateContext");
  }

  @Test
  void ingressRoadmWithPluggableLineCardHappyPathWithOppm() throws ObjectInUseException {
    var ttpName = "Port-1/2/n1";
    var srcAid = "Port-1/9/n-e";
    var dstAid = "Port-3/4/n";
    var cardAid = "Card-1/2";
    var cardUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card";
    var ptpUri = new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1");
    var ctpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1/ctp/otuc2";
    var slcId = 1;
    var channel = 196000000;
    var bandwidth = 37500;

    // Given
    TtpInfo ttp = WdmSegmentUtil.sampleTtpWithTunableTps(ttpName, srcAid);
    modelDao.addTtp(NE_ID, ttp);
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, NON_OPAQUE_SPECTRUM, 2, false));

    // When
    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.forwardSequenceNumber = 1;
    request.reverseSequenceNumber = 2;
    request.setPointDelta = 1.0;
    request.channelSrcLabel = channel;
    request.slotWidthSrcLabel = bandwidth;
    request.portParams = new ArrayList<>();
    request.portParams.add(new SelectedParameter(ParameterName.GLQ, new Value.Enum("OTUC2")));
    request.portParams.add(new SelectedParameter(ParameterName.CHANNEL, new Value.Numeric(new BigDecimal(channel), "MHz")));
    when(ptpResources.findCard(NE_ID, new Aid(ttpName)))
      .thenReturn(Optional.of(new CardRef(NE_ID, new Aid(cardAid), new Uri(cardUri), "OF-2D16DCT", F8OperState.ANR.getValue())));
    when(ptpResources.findPlug(NE_ID, new Aid(ttpName)))
      .thenReturn(Optional.of(PlugRef.builder()
        .setNeId(NE_ID)
        .setAid("")
        .setUri("")
        .setType("CFP2-448G-#BDCTC-SM-LC")
        .build())
      );
    var ctp = CtpBuilder.newBuilder().withUri(new Uri(ctpUri)).build();
    when(ctpResources.findCtpExtended(NE_ID, new Uri(ctpUri))).thenReturn(Optional.of(ctp));

    var ptp = PtpBuilder.newBuilder()
      .withUri(ptpUri)
      .withTunedFrequency(channel)
      .build();
    when(ptpResources.findPtpExtended(NE_ID, new Aid(ttpName))).thenReturn(Optional.of(ptp));
    var slc = new Slc(
      0, NE_ID, slcId, "",
      new SlcEndpoint(2, 0, 0, new SlcActiveEndpoint("/mit/me/1/croma/degree/2", Collections.singletonList("/mit/me/1/croma/degree/2")), null),
      new SlcEndpoint(1, 0, 0, new SlcActiveEndpoint(ctpUri, Collections.singletonList(ctpUri)), null),
      List.of(bandwidth), List.of(channel),
      null, null, null
    );
    when(cromaMOService.getSlcForNe(NE_ID)).thenReturn(List.of(slc));
    when(cromaMOService.getSlcForNeAndSlcId(NE_ID, slcId)).thenReturn(Optional.of(slc));

    final String protectingPtpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,9/eq/card/ptp/nw,1-e";
    final String protectingCtpUri = protectingPtpUri + "/ctp/traffic";
    final String workingPtpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,9/eq/card/ptp/nw,1-e";
    final String workingCtpUri = workingPtpUri + "/ctp/traffic";
    PtpRef workingPtpRef = new PtpRef(NE_ID, new Aid(srcAid), new Uri(workingPtpUri), null);
    when(ptpResources.findPtp(NE_ID, new Aid(srcAid))).thenReturn(Optional.of(workingPtpRef));

    CardRef oppmCard = new CardRef(NE_ID, new Aid("cardAid"), new Uri(workingCtpUri), null, 0);
    when(ctpResources.findCard(NE_ID, new Uri(workingCtpUri))).thenReturn(Optional.of(oppmCard));

    String crossConnectUri = "crossConnectUri";
    CrossConnectRef crossConnect = new CrossConnectRef(NE_ID, null, new Uri(crossConnectUri), List.of(), List.of(workingCtpUri), null, null);
    when(cardResources.listCrossConnections(NE_ID, oppmCard.aid())).thenReturn(List.of(crossConnect));

    var protectionGroupUri = "protectionGroupUri";
    var protectionGroup = new LineProtectionGroupRef(NE_ID, null, new Uri(protectionGroupUri),
      null, new Uri(workingCtpUri), null, new Uri(protectingCtpUri), null,
      new Uri(crossConnectUri), null, null, null, null);
    when(crossConnectResources.getProtectionGroup(NE_ID, new Uri(crossConnectUri)))
      .thenReturn(Optional.of(protectionGroup));
    verifyNoInteractions(provision);

    setNetworkElementVersion();
    var context = segmentAdoptRequest.buildContext(request);
    var result = segmentAdoptRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
    assertEquals(slcId, segment.getSlcId());
    assertEquals(CrmNodePosition.INGRESS, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(dstAid, segment.getAidDstTp());
    assertEquals(ttpName, segment.getTtpAid());
    assertEquals(ctpUri, segment.getTransponderCtpUri());
    assertEquals(protectionGroupUri, segment.getProtectionGroupUri());
  }

  @Test
  void ingressRoadmWithPluggableLineCardWithOppmProtectionGroupNotFound() throws ObjectInUseException {
    var ttpName = "Port-1/2/n1";
    var srcAid = "Port-1/9/n-e";
    var dstAid = "Port-3/4/n";
    var cardAid = "Card-1/2";
    var cardUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card";
    var ptpUri = new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1");
    var ctpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1/ctp/otuc2";
    var slcId = 1;
    var channel = 196000000;
    var bandwidth = 37500;

    // Given
    TtpInfo ttp = WdmSegmentUtil.sampleTtpWithTunableTps(ttpName, srcAid);
    modelDao.addTtp(NE_ID, ttp);
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, NON_OPAQUE_SPECTRUM, 2, false));

    // When
    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.forwardSequenceNumber = 1;
    request.reverseSequenceNumber = 2;
    request.setPointDelta = 1.0;
    request.channelSrcLabel = channel;
    request.slotWidthSrcLabel = bandwidth;
    request.portParams = new ArrayList<>();
    request.portParams.add(new SelectedParameter(ParameterName.GLQ, new Value.Enum("OTUC2")));
    request.portParams.add(new SelectedParameter(ParameterName.CHANNEL, new Value.Numeric(new BigDecimal(channel), "MHz")));
    when(ptpResources.findCard(NE_ID, new Aid(ttpName)))
      .thenReturn(Optional.of(new CardRef(NE_ID, new Aid(cardAid), new Uri(cardUri), "OF-2D16DCT", F8OperState.ANR.getValue())));
    when(ptpResources.findPlug(NE_ID, new Aid(ttpName)))
      .thenReturn(Optional.of(PlugRef.builder()
        .setNeId(NE_ID)
        .setAid("")
        .setUri("")
        .setType("CFP2-448G-#BDCTC-SM-LC")
        .build())
      );
    var ctp = CtpBuilder.newBuilder().withUri(new Uri(ctpUri)).build();
    when(ctpResources.findCtpExtended(NE_ID, new Uri(ctpUri))).thenReturn(Optional.of(ctp));
    var ptp = PtpBuilder.newBuilder()
      .withUri(ptpUri)
      .withTunedFrequency(channel)
      .build();
    when(ptpResources.findPtpExtended(NE_ID, new Aid(ttpName))).thenReturn(Optional.of(ptp));
    var slc = new Slc(
      0, NE_ID, slcId, "",
      new SlcEndpoint(2, 0, 0, new SlcActiveEndpoint("/mit/me/1/croma/degree/2", Collections.singletonList("/mit/me/1/croma/degree/2")), null),
      new SlcEndpoint(1, 0, 0, new SlcActiveEndpoint(ctpUri, Collections.singletonList(ctpUri)), null),
      List.of(bandwidth), List.of(channel),
      null, null, null
    );
    when(cromaMOService.getSlcForNe(NE_ID)).thenReturn(List.of(slc));
    when(cromaMOService.getSlcForNeAndSlcId(NE_ID, slcId)).thenReturn(Optional.of(slc));

    final String workingPtpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,9/eq/card/ptp/nw,1-e";
    final String workingCtpUri = workingPtpUri + "/ctp/traffic";
    PtpRef workingPtpRef = new PtpRef(NE_ID, new Aid(srcAid), new Uri(workingPtpUri), null);
    when(ptpResources.findPtp(NE_ID, new Aid(srcAid))).thenReturn(Optional.of(workingPtpRef));

    CardRef oppmCard = new CardRef(NE_ID, new Aid("cardAid"), new Uri(workingCtpUri), null, 0);
    when(ctpResources.findCard(NE_ID, new Uri(workingCtpUri))).thenReturn(Optional.of(oppmCard));

    String crossConnectUri = "crossConnectUri";
    CrossConnectRef crossConnect = new CrossConnectRef(NE_ID, null, new Uri(crossConnectUri), List.of(), List.of(workingCtpUri), null, null);
    when(cardResources.listCrossConnections(NE_ID, oppmCard.aid())).thenReturn(List.of(crossConnect));

    when(crossConnectResources.getProtectionGroup(NE_ID, new Uri(crossConnectUri)))
      .thenReturn(Optional.empty());
    verifyNoInteractions(provision);

    setNetworkElementVersion();

    var context = segmentAdoptRequest.buildContext(request);
    var result = segmentAdoptRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
    assertEquals(slcId, segment.getSlcId());
    assertEquals(CrmNodePosition.INGRESS, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(dstAid, segment.getAidDstTp());
    assertEquals(ttpName, segment.getTtpAid());
    assertEquals(ctpUri, segment.getTransponderCtpUri());
    assertNull(segment.getProtectionGroupUri());
  }

  private void setNetworkElementVersion() {
    when(neDataProvider.getNeData(NE_ID)).thenReturn(new NEData(NE_ID, 0, null, null, null, SW_VERSION));
  }
}
