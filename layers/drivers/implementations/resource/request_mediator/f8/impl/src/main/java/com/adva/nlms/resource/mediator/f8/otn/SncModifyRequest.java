/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */
package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.provision.f8.api.in.Provision;

import java.util.LinkedHashSet;
import java.util.Objects;
import java.util.Set;

import static com.adva.nlms.resource.mediator.f8.events.ProvisionEventsCreator.sncAutocreated;
import static com.adva.nlms.resource.mediator.f8.events.ProvisionEventsCreator.sncModify;


class SncModifyRequest implements Request {
  private final int neId;

  private final String uri;

  private final boolean entityAutocreated;

  private final Set<String> aEndpointURIs;

  private final Set<String> zEndpointURIs;

  private final Provision provisionApi;

  SncModifyRequest(int neId, String uri, LinkedHashSet<String> aEndpointURIs, LinkedHashSet<String> zEndpointURIs, boolean entityAutocreated, Provision provisionApi) {
    this.neId = neId;
    this.uri = uri;
    this.aEndpointURIs = aEndpointURIs;
    this.zEndpointURIs = zEndpointURIs;
    this.entityAutocreated = entityAutocreated;
    this.provisionApi = provisionApi;
  }

  @Override
  public ProvisionLocalEvent provision() {
    if (entityAutocreated) {
      return sncAutocreated(uri, aEndpointURIs, zEndpointURIs);
    }
    provisionApi.modifySNC(NetworkElementID.create(neId), uri, new LinkedHashSet<>(aEndpointURIs), new LinkedHashSet<>(zEndpointURIs), "adz");
    return sncModify(uri, aEndpointURIs, zEndpointURIs);
  }

  @Override
  public void delete() {
    // when deleting we always create SncCreateRequest
  }

  @Override
  public ProvisionLocalEvent adopt() {
    return null;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    SncModifyRequest that = (SncModifyRequest) o;
    return neId == that.neId && Objects.equals(uri, that.uri) &&
      Objects.equals(aEndpointURIs, that.aEndpointURIs) && Objects.equals(zEndpointURIs, that.zEndpointURIs) &&
      Objects.equals(entityAutocreated, that.entityAutocreated);
  }

  @Override
  public int hashCode() {
    return Objects.hash(neId, uri, aEndpointURIs, zEndpointURIs, provisionApi, entityAutocreated);
  }
}
