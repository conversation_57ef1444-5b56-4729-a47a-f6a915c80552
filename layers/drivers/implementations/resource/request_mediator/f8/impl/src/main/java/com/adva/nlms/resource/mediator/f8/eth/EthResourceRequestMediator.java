/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.eth;

import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;

public interface EthResourceRequestMediator {
  void handleRequest(CrmSegmentRequestDto request);

  boolean isSegmentRequestSupported(CrmSegmentRequestDto request);

  void clearSegmentCache(int neId);
}
