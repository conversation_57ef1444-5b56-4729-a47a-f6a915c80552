/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.resource.mediator.f8.lock.NetworkTransactionWrapper;

class DummyNetworkTransactionWrapper implements NetworkTransactionWrapper {
  @Override
  public void executeInNetworkTransaction(int neId, Runnable operation, String logPrefix) throws ObjectInUseException {
    operation.run();
  }
}
