/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.resource.mediator.f8.events.SncAutocreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.otn.SncRequest;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Set;

public class SncRequestTest {

  static private final int NETWORK_ELEMENT_ID = 3;
  static private final String SNC_URI = "SNC_URI";
  private final Provision provisionApi = Mockito.mock(Provision.class);

  @Test
  void provisionAutocreatedSnc() {
    var request = new SncRequest(NETWORK_ELEMENT_ID, SNC_URI, false, true, Set.of(), Set.of(), provisionApi);

    var result = request.provision();

    Mockito.verifyNoInteractions(provisionApi);
    Assertions.assertEquals(new SncAutocreatedEvent(SNC_URI, Set.of(), Set.of()), result);
  }

  @Test
  void provisionAutocreatedSncWithExistsFlag() {
    var request = new SncRequest(NETWORK_ELEMENT_ID, SNC_URI, true, true, Set.of(), Set.of(), provisionApi);

    var result = request.provision();

    Mockito.verifyNoInteractions(provisionApi);
    Assertions.assertEquals(new SncAutocreatedEvent(SNC_URI, Set.of(), Set.of()), result);
  }

  @Test
  void provisionAdoptSnc() {
    var request = new SncRequest(NETWORK_ELEMENT_ID, SNC_URI, true, false, Set.of(), Set.of(), provisionApi);

    var result = request.provision();

    Mockito.verifyNoInteractions(provisionApi);
    Assertions.assertEquals(new SncAdoptedEvent(SNC_URI, Set.of(), Set.of()), result);
  }

  @Test
  void adoptAutocreatedSnc() {
    var request = new SncRequest(NETWORK_ELEMENT_ID, SNC_URI, false, true, Set.of(), Set.of(), provisionApi);

    var result = request.adopt();

    Mockito.verifyNoInteractions(provisionApi);
    Assertions.assertEquals(new SncAutocreatedEvent(SNC_URI, Set.of(), Set.of()), result);
  }
}
