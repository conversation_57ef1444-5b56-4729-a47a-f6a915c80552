/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.provider.api.CapabilityProvider;
import com.adva.infrastructure.capabilityprovider.capabilities.CapabilityConfiguration;
import com.adva.infrastructure.capabilityprovider.core.CapabilityProviderConfiguration;
import com.adva.infrastructure.capabilityprovider.mo.MoCapabilityProviderConfiguration;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.Ctp;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.topology.NEData;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.OpticalParameters;
import com.adva.nlms.opticalparameters.api.Value;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.opticalparameters.api.enums.ParameterName;
import com.adva.nlms.opticalparameters.cim.api.CimValue;
import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;
import com.adva.nlms.resource.mediator.f8.InterfaceType;
import com.adva.nlms.resource.mediator.f8.ProtectionGroupFinder;
import com.adva.nlms.resource.mediator.f8.internalpath.Complete;
import com.adva.nlms.resource.mediator.f8.internalpath.Connection;
import com.adva.nlms.resource.mediator.f8.internalpath.CrossConnect;
import com.adva.nlms.resource.mediator.f8.internalpath.ForkPlacement;
import com.adva.nlms.resource.mediator.f8.internalpath.InternalPath;
import com.adva.nlms.resource.mediator.f8.internalpath.LabelDTO;
import com.adva.nlms.resource.mediator.f8.internalpath.MultiLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.OduType;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabelType;
import com.adva.nlms.resource.mediator.f8.internalpath.SignalType;
import com.adva.nlms.resource.mediator.f8.internalpath.TerminationPoint;
import com.adva.nlms.resource.mediator.f8.internalpath.Whole;
import com.adva.nlms.resource.mediator.f8.otn.CtpRequest;
import com.adva.nlms.resource.mediator.f8.otn.LayerQualifierToCimString;
import com.adva.nlms.resource.mediator.f8.otn.OtnCimProvisionRequestParametersCreatorImpl;
import com.adva.nlms.resource.mediator.f8.otn.PrepareProvisionRequest;
import com.adva.nlms.resource.mediator.f8.otn.PtpParamsRequest;
import com.adva.nlms.resource.mediator.f8.otn.PtpRequest;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnCardCapabilities;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnCardCapabilitiesImpl;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnDbResourcesFacade;
import com.adva.nlms.resource.mediator.f8.otn.SncRequest;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegmentRepository;
import com.adva.nlms.resource.provision.f8.api.in.MoCimParameter;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class PrepareProvisionRequestOpenFab400Tests {

    private final int NE_ID = 42;
    private final String MODULE_TYPE = "MA-B5LT";
    private static final LayerQualifier LAYER = LayerQualifier.ODU2;

    private final RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade = mock(RrmOtnDbResourcesFacade.class);
    private final NEDataProvider neDataProvider = mock(NEDataProvider.class);
    private static final CapabilityProvider capabilityProvider = new CapabilityProviderConfiguration()
      .capabilityProvider(new CapabilityConfiguration().capabilityRepository(), null, null);
    private static final MoCapabilityProvider moCapabilityProvider = new MoCapabilityProviderConfiguration().moCapabilityProvider();
    private final RrmOtnCardCapabilities rrmOtnCardCapabilities = new RrmOtnCardCapabilitiesImpl(neDataProvider, capabilityProvider, moCapabilityProvider);
    private final MoCapabilityProvider provider = new MoCapabilityProviderConfiguration().moCapabilityProvider();
    private final OtnCimProvisionRequestParametersCreatorImpl cimProvisionRequestParametersCreator = new OtnCimProvisionRequestParametersCreatorImpl(provider);
    private final Provision provisionApi = mock(Provision.class);
    private final OtnSegmentRepository otnSegmentRepository = mock(OtnSegmentRepository.class);
    private final ProtectionGroupFinder protectionGroupFinder = Mockito.mock(ProtectionGroupFinder.class);

    private final PrepareProvisionRequest sut = new PrepareProvisionRequest(
      cimProvisionRequestParametersCreator, provisionApi, rrmOtnCardCapabilities, rrmOtnDbResourcesFacade, otnSegmentRepository, protectionGroupFinder, moCapabilityProvider);
    private static TerminationPoint generateTerminatePoint(String aid, OtnLabelType type, boolean terminate) {
        return new TerminationPoint(aid, new LabelDTO(new MultiLabel(List.of(new OtnLabel(type)))), terminate);
    }

    private CtpRequest generateCtpRequest(String uri, boolean isAdopt, Set<MoCimParameter> params) {
        return new CtpRequest(NE_ID, uri, isAdopt, false, params, provisionApi);
    }

    private PtpRequest generatePtpRequest(String uri, boolean isAdopt) {
        return new PtpRequest(NE_ID, uri, isAdopt, provisionApi);
    }

    private PtpParamsRequest generatePtpParamsRequest(String uri, boolean isAdopt) {
        return new PtpParamsRequest(NE_ID, uri, isAdopt, Set.of(), provisionApi);
    }

    @Test
    void prepareRequestC2() {
        // given
        // c port
        var cPort = "Port-2/1/c2";
        var cPlug = "Plug-2/1/c1-c4";
        var cPlugUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/eqh/plgh,3/eq/plg";
        var cPtpUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2";
        var cCtpUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2/ctp/otu2";
        var cCtpOduUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2/ctp/otu2/ctp/odu2";
        var cPlugRef = PlugRef.builder()
          .setNeId(NE_ID)
          .setAid(cPlug)
          .setUri(cPlugUri)
          .setType("QSFP28-112G-AOC-0100")
          .build();
        // n port
        var nPort = "Port-2/1/n";
        var nPlug = "Plug-2/1/n";
        var nPortUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/eqh/plgh,1/eq/plg";
        var nParentCtpUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/nw,1/ctp/ot100";
        var nCtpUriPrefix = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/nw,1/ctp/ot100/ctp/odu4/ctp/odu2-";
        var nCtpUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/nw,1/ctp/ot100/ctp/odu4/ctp/odu2-1";
        var nPlugRef = PlugRef.builder()
          .setNeId(NE_ID)
          .setAid(nPlug)
          .setUri(nPortUri)
          .setType("CFP2-224G-#DCTC-SM-LC")
          .build();
        var nCtp = new Ctp(NE_ID, new Aid("OT100-2/1/n/ot100"), new Uri(nParentCtpUri), List.of("och", "otu4"), null, null, null, null, "tss", new Aid("Port-2/1/n"), "qpsk", "normal", "feca15i3", null);
        // card
        var sncUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/sn/odu2/snc";
        var moduleAid = "Module-2/1";
        var cardUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card";
        var cardRef = new CardRef(NE_ID, new Aid(moduleAid), new Uri(cardUri), MODULE_TYPE, 2, "mux");

        var request = new CrmSegmentRequestDto();
        request.requestType = CrmSegmentRequestDto.RequestType.CREATE;
        request.portParams = List.of(new OpticalParameters.SelectedParameter(ParameterName.GLQ, new Value.Enum("OTU2")));
        request.srcTp = cPort;
        request.dstTp = nPort;
        request.setSrcInterfaceType(InterfaceType.ENNI);

        var motrait = LayerQualifierToCimString.convert(LAYER) + "/odtu";
        var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(1));
        var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L)));
        var termParam = new MoCimParameter("", "termination-mode", new CimValue.Enum("nss"));

        var srcTp = generateTerminatePoint(cPort, new Whole(), false);
        var dstTp = generateTerminatePoint(nPort, new Complete(1, List.of(1, 2, 3, 4, 5, 6, 7, 8)), false);

        request.internalPath = new InternalPath(
          null,
          List.of(new Connection(
            new SignalType(new OduType(LAYER, 0)),
            List.of(new CrossConnect(srcTp, dstTp, ForkPlacement.NONE)))
          ),
          null
        );

        // setup
        when(rrmOtnDbResourcesFacade.findCardFromModule(NE_ID, new Aid(moduleAid))).thenReturn(cardRef);
        when(rrmOtnDbResourcesFacade.findPlugFromPtp(NE_ID, new Aid(cPort))).thenReturn(Optional.of(cPlugRef));
        when(rrmOtnDbResourcesFacade.findPlugFromPtp(NE_ID, new Aid(nPort))).thenReturn(Optional.of(nPlugRef));
        when(neDataProvider.getNeData(NE_ID)).thenReturn(
          new NEData(NE_ID, 8888,"FSP 3000C", "FSP3000C", "********", "6.5.1")
        );
        when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(NE_ID, new Aid(nPort))).thenReturn(List.of(nCtp));
        when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(NE_ID, new Uri(nParentCtpUri))).thenReturn(Optional.of(nCtp));
        when(provisionApi.getAvailableCtpEntity(eq(NetworkElementID.create(NE_ID)),
          eq(nCtpUriPrefix + 1))).thenReturn(Optional.of(1));

        // when
        var result = sut.prepareRequest(NE_ID, request);

        // then
        assertEquals(6, result.size());
        assertInstanceOf(PtpRequest.class, result.get(0));
        assertEquals(generatePtpRequest(cPtpUri, false), result.get(0));
        assertInstanceOf(CtpRequest.class, result.get(1));
        assertEquals(generateCtpRequest(cCtpUri, false, Set.of()), result.get(1));
        assertInstanceOf(PtpParamsRequest.class, result.get(2));
        assertEquals(generatePtpParamsRequest(cPtpUri, false), result.get(2));
        assertInstanceOf(CtpRequest.class, result.get(3));
        assertEquals(generateCtpRequest(cCtpOduUri, false, Set.of(termParam)), result.get(3));
        assertInstanceOf(CtpRequest.class, result.get(4));
        assertEquals(generateCtpRequest(nCtpUri, false, Set.of(tpParam, tsParam)), result.get(4));
        assertInstanceOf(SncRequest.class, result.get(5));
        assertEquals(new SncRequest(NE_ID, sncUri, false, Set.of(cCtpOduUri), Set.of(nCtpUri), provisionApi), result.get(5));
    }

    @Test
    void prepareRequestC6() {
        // given
        // c port
        var cPort = "Port-2/1/c6";
        var cPlug = "Plug-2/1/c5-c8";
        var cPlugUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/eqh/plgh,3/eq/plg";
        var cPtpUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,6";
        var cCtpUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,6/ctp/otu2";
        var cCtpOduUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,6/ctp/otu2/ctp/odu2";
        var cPlugRef = PlugRef.builder()
          .setNeId(NE_ID)
          .setAid(cPlug)
          .setUri(cPlugUri)
          .setType("QSFP28-112G-AOC-0100")
          .build();
        // n port
        var nPort = "Port-2/1/n";
        var nPlug = "Plug-2/1/n";
        var nPortUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/eqh/plgh,1/eq/plg";
        var nParentCtpUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/nw,1/ctp/ot100";
        var nCtpUriPrefix = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/nw,1/ctp/ot100/ctp/odu4/ctp/odu2-";
        var nCtpUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/nw,1/ctp/ot100/ctp/odu4/ctp/odu2-1";
        var nPlugRef = PlugRef.builder()
          .setNeId(NE_ID)
          .setAid(nPlug)
          .setUri(nPortUri)
          .setType("CFP2-224G-#DCTC-SM-LC")
          .build();
        var nCtp = new Ctp(NE_ID, new Aid("OT100-2/1/n/ot100"), new Uri(nParentCtpUri), List.of("och", "otu4"), null, null, null, null, "tss", new Aid("Port-2/1/n"), "qpsk", "normal", "feca15i3", null);
        // card
        var sncUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/sn/odu2/snc";
        var moduleAid = "Module-2/1";
        var cardUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card";
        var cardRef = new CardRef(NE_ID, new Aid(moduleAid), new Uri(cardUri), MODULE_TYPE, 2, "mux");

        var request = new CrmSegmentRequestDto();
        request.requestType = CrmSegmentRequestDto.RequestType.CREATE;
        request.portParams = List.of(new OpticalParameters.SelectedParameter(ParameterName.GLQ, new Value.Enum("OTU2")));
        request.srcTp = cPort;
        request.dstTp = nPort;
        request.setSrcInterfaceType(InterfaceType.ENNI);

        var motrait = LayerQualifierToCimString.convert(LAYER) + "/odtu";
        var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(1));
        var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L)));
        var termParam = new MoCimParameter("", "termination-mode", new CimValue.Enum("nss"));

        var srcTp = generateTerminatePoint(cPort, new Whole(), false);
        var dstTp = generateTerminatePoint(nPort, new Complete(1, List.of(1, 2, 3, 4, 5, 6, 7, 8)), false);

        request.internalPath = new InternalPath(
          null,
          List.of(new Connection(
            new SignalType(new OduType(LAYER, 0)),
            List.of(new CrossConnect(srcTp, dstTp, ForkPlacement.NONE)))
          ),
          null
        );

        // setup
        when(rrmOtnDbResourcesFacade.findCardFromModule(NE_ID, new Aid(moduleAid))).thenReturn(cardRef);
        when(rrmOtnDbResourcesFacade.findPlugFromPtp(NE_ID, new Aid(cPort))).thenReturn(Optional.of(cPlugRef));
        when(rrmOtnDbResourcesFacade.findPlugFromPtp(NE_ID, new Aid(nPort))).thenReturn(Optional.of(nPlugRef));
        when(neDataProvider.getNeData(NE_ID)).thenReturn(
          new NEData(NE_ID, 8888,"FSP 3000C", "FSP3000C", "********", "6.5.1")
        );
        when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(NE_ID, new Aid(nPort))).thenReturn(List.of(nCtp));
        when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(NE_ID, new Uri(nParentCtpUri))).thenReturn(Optional.of(nCtp));
        when(provisionApi.getAvailableCtpEntity(eq(NetworkElementID.create(NE_ID)),
          eq(nCtpUriPrefix + 1))).thenReturn(Optional.of(1));

        // when
        var result = sut.prepareRequest(NE_ID, request);

        // then
        assertEquals(6, result.size());
        assertInstanceOf(PtpRequest.class, result.get(0));
        assertEquals(generatePtpRequest(cPtpUri, false), result.get(0));
        assertInstanceOf(CtpRequest.class, result.get(1));
        assertEquals(generateCtpRequest(cCtpUri, false, Set.of()), result.get(1));
        assertInstanceOf(PtpParamsRequest.class, result.get(2));
        assertEquals(generatePtpParamsRequest(cPtpUri, false), result.get(2));
        assertInstanceOf(CtpRequest.class, result.get(3));
        assertEquals(generateCtpRequest(cCtpOduUri, false, Set.of(termParam)), result.get(3));
        assertInstanceOf(CtpRequest.class, result.get(4));
        assertEquals(generateCtpRequest(nCtpUri, false, Set.of(tpParam, tsParam)), result.get(4));
        assertInstanceOf(SncRequest.class, result.get(5));
        assertEquals(new SncRequest(NE_ID, sncUri, false, Set.of(cCtpOduUri), Set.of(nCtpUri), provisionApi), result.get(5));
    }
}
