/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: zbigniewj
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.resource.mediator.f8.internalpath.LabelDTO;
import com.adva.nlms.resource.mediator.f8.internalpath.Complete;
import com.adva.nlms.resource.mediator.f8.internalpath.Container;
import com.adva.nlms.resource.mediator.f8.internalpath.MultiLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.Indistinct;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabelType;
import com.adva.nlms.resource.mediator.f8.internalpath.PortResourceIdentifier;
import com.adva.nlms.resource.mediator.f8.internalpath.Whole;


import com.adva.nlms.resource.mediator.f8.otn.PortResourceIdentifierToCimString;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class PortResourceIdentifierToCimStringTest {

  private PortResourceIdentifier generateOtnLabelDTO(OtnLabelType in) {
    return new LabelDTO(new MultiLabel(List.of(new OtnLabel(in))));
  }

  @Test
  void convertWholeLabelTest() {
    var label = generateOtnLabelDTO(new Whole());
    var result = PortResourceIdentifierToCimString.convert(label);
    Assertions.assertTrue(result.isEmpty());
  }

  @Test
  void convertCompleteLabelTest() {
    var label = generateOtnLabelDTO(new Complete(1, List.of(1, 2, 3, 4)));
    var result = PortResourceIdentifierToCimString.convert(label);
    Assertions.assertTrue(result.isPresent());
    Assertions.assertTrue(result.get().isEmpty());
  }

  @Test
  void convertContainerLabelTest() {
    var label = generateOtnLabelDTO(new Container(6));
    var result = PortResourceIdentifierToCimString.convert(label);
    Assertions.assertTrue(result.isPresent());
    Assertions.assertEquals("6", result.get());
  }

  @Test
  void convertIndistinctLabelTest() {
    var label = generateOtnLabelDTO(new Indistinct(6, 20));
    var result = PortResourceIdentifierToCimString.convert(label);
    Assertions.assertTrue(result.isEmpty());
  }
}
