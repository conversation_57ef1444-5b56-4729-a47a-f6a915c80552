/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.resource.mediator.f8;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.mediation.config.f8.entity.protection.EcProtectionFreezeException;
import com.adva.nlms.mediation.config.f8.entity.protection.EcProtectionSwitchException;
import com.adva.nlms.mediation.config.f8.entity.protection.ProtectionGroupF8Service;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.CtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.resource.mediator.api.in.OperationFailedException;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionException;
import com.adva.nlms.txprovisioning.api.ProvisionTransactional;
import com.adva.topology.manager.api.in.TopologyNodeApi;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.UUID;

public class ProvisionAdapter {
  private static final Logger log = LogManager.getLogger(ProvisionAdapter.class);
  private final ProtectionGroupF8Service protectionGroupF8Service;
  private final TopologyNodeApi topologyNodeApi;
  private final Provision provision;
  private final MoCapabilityProvider moCapabilityProvider;
  private final CtpResources ctpResources;
  private final PtpResources ptpResources;

  ProvisionAdapter(ProtectionGroupF8Service protectionGroupF8Service, TopologyNodeApi topologyNodeApi, Provision provision, MoCapabilityProvider moCapabilityProvider, CtpResources ctpResources, PtpResources ptpResources) {
    this.protectionGroupF8Service = protectionGroupF8Service;
    this.topologyNodeApi = topologyNodeApi;
    this.provision = provision;
    this.moCapabilityProvider = moCapabilityProvider;
    this.ctpResources = ctpResources;
    this.ptpResources = ptpResources;
  }

  void handleAdminStateOperation(
    final UUID neId,
    List<String> aids,
    final String protectionGroupIndex,
    final AdminState adminState,
    final LayerQualifier layerProtocolQualifier
  ) throws OperationFailedException {
    try {
      final AdminStateOperationDTO adminStateOperationDTO = createAdminStateOperationDTO(neId, protectionGroupIndex);
      aids = filterEntities(adminStateOperationDTO.getNetworkElementID(), aids, adminState, layerProtocolQualifier);
      if (!aids.isEmpty()) {
        handleAdminStateOperationInTransaction(adminStateOperationDTO, aids, adminState);
      }
    } catch (EcProtectionFreezeException e) {
      final String message = String.format("Failed to switch admin state for neId %s with %s and protectionGroupIndex %s to adminState: %s", neId, String.join(",", aids), protectionGroupIndex, adminState);
      throw new OperationFailedException(message, e);
    }
  }

  void handleProtectionSwitch(UUID neId, String protectionGroupEntityIndex) throws OperationFailedException {
    var networkElementID = getNetworkElementID(neId);
    try {
      protectionGroupF8Service.switchProtection(networkElementID, new EntityIndex(protectionGroupEntityIndex));
    } catch (EcProtectionSwitchException e) {
      throw new OperationFailedException("Failed to switch protection for " + protectionGroupEntityIndex, e);
    }
  }

  private NetworkElementID getNetworkElementID(UUID neId) throws OperationFailedException {
    try {
      var node = topologyNodeApi.getNodeByUUID(neId);
      return NetworkElementID.create(node.neId());
    } catch (Exception e) {
      throw new OperationFailedException("Not found NE " + neId, e);
    }
  }

  @ProvisionTransactional
  private void handleAdminStateOperationInTransaction(final AdminStateOperationDTO adminStateOperationDTO, final List<String> aids, final AdminState adminState) throws EcProtectionFreezeException {
    if (AdminState.MAINTENANCE.equals(adminState)) {
      protectionGroupF8Service.freezeProtectionGroup(adminStateOperationDTO.getNetworkElementID(), adminStateOperationDTO.getProtectionGroupEntityIndex());
    }
    updateAdminState(adminStateOperationDTO.getNetworkElementID(), aids, adminState);
    if (AdminState.UP.equals(adminState)) {
      protectionGroupF8Service.unFreezeProtectionGroup(adminStateOperationDTO.getNetworkElementID(), adminStateOperationDTO.getProtectionGroupEntityIndex());
    }
  }

  private AdminStateOperationDTO createAdminStateOperationDTO(UUID neId, String protectionGroupIndex) throws OperationFailedException {
    final var networkElementID = getNetworkElementID(neId);
    final var protectionGroupEntityIndex = EntityIndex.newIndex(protectionGroupIndex);
    return new AdminStateOperationDTO(networkElementID, protectionGroupEntityIndex);
  }

  private void updateAdminState(
    final NetworkElementID networkElementID,
    final List<String> aids,
    final AdminState adminState
  ) {
    for (var aid : aids) {
      try {
        provision.updateAdminStateByAid(networkElementID, aid, adminState);
      } catch (ProvisionException e) {
        log.info("Failed to switch admin state to: {} with aid: {} for networkElementID: {}", adminState, aid, networkElementID, e);
      }
    }
  }

  private List<String> filterEntities(
    final NetworkElementID networkElementID,
    final List<String> aids,
    final AdminState adminState,
    final LayerQualifier layerProtocolQualifier
  ) {
    return aids.stream()
      .filter(aid -> !isAlien(aid)) // filter out alien entities, since they do not have admin state
      .filter(aid -> applyLayerSpecificFiltering(networkElementID, aid, adminState, layerProtocolQualifier))
      .toList();
  }

  private boolean applyLayerSpecificFiltering(
    final NetworkElementID networkElementID,
    final String aidString,
    final AdminState adminState,
    final LayerQualifier layerProtocolQualifier
  ) {
    // see https://polarion.adtran.com/polarion/#/project/FNM_Spec/workitem?id=FNMS-65932
    if (adminState == AdminState.DOWN) {
      if (layerProtocolQualifier == LayerQualifier.OTS) {
        // Never set entities in OTS layer to down
        return false;
      } else if (layerProtocolQualifier == LayerQualifier.OMS) {
        // For OMS layer, only channel card entities can be set to down
        final int neId = networkElementID.getValue();
        final Aid aid = new Aid(aidString);
        return ptpResources.findCard(neId, aid)
          .or(() -> ctpResources.findCard(neId, aid))  // this entity can be either ptp or ctp
          .map(CardRef::type)
          .map(moCapabilityProvider::isCardSupported)
          .orElse(false);
      }
    }
    return true;
  }

  private static boolean isAlien(String aid) {
    return aid.contains("alien");
  }

}
