/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: asowa
 *
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capabilityprovider.mo.MoCapabilityProviderConfiguration;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.CtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;
import com.adva.nlms.resource.mediator.f8.SegmentRequestFailedException;
import com.adva.nlms.resource.mediator.f8.wdm.adapters.persistence.RrmPersistenceConfiguration;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.CrmNodePosition;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.RrmSegmentRepository;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.WdmSegment;
import com.adva.nlms.resource.provision.f8.api.in.ObjectDoesNotExistException;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

class WdmSegmentDeleteRequestTest {
  private static final int NE_ID = 23;
  private static final int SLC_ID = 2;
  private static final String TTP_NAME = "TTP_NAME";
  private static final String SRC_TP_AID = "SRC_TP_AID";
  private static final String NOT_BLANK = "NOT_BLANK";
  private static final String PROTECTION_GROUP_URI = "protection-group-uri";
  private static final String SEGMENT_ID = "segment-id";

  private static final MoCapabilityProvider MO_CAPABILITY_PROVIDER = new MoCapabilityProviderConfiguration().moCapabilityProvider();
  private final RrmSegmentRepository repository = new RrmPersistenceConfiguration().rrmSegmentRepository();
  private final Provision provision = mock();
  private final FiberTracer fiberTracer = mock();
  private final CtpResources ctpResources = mock();
  private final SegmentDeleteRequest sut = new WdmSegmentRequestConfig().wdmSegmentDeleteRequest(
    provision,
    repository,
    fiberTracer,
    new DummyNetworkTransactionWrapper(),
    mock(),
    ctpResources,
    MO_CAPABILITY_PROVIDER
  );

  private CrmSegmentRequestDto request;
  private FiberTracer.AlienPtpDTO alienPtpDTO;

  @BeforeEach
  void init() {
    this.request = new CrmSegmentRequestDto();
    this.request.id = SEGMENT_ID;
    this.request.neId = NE_ID;
    this.alienPtpDTO = new FiberTracer.AlienPtpDTO(0, 1, NOT_BLANK, false);
  }

  static private WdmSegment generateSegment(CrmNodePosition position,String segmentId, String aidSrcTp, String aidDstTp, String transponderUri) {
    return WdmSegment.newSegment()
      .withSegmentRequestId(segmentId)
      .withNeId(NE_ID)
      .withSlcId(SLC_ID)
      .withNodePosition(position)
      .withAidSrcTp(aidSrcTp)
      .withAidDstTp(aidDstTp)
      .withChannelLabel(196000000)
      .withTransponderCtpUri(transponderUri)
      .withTtpAid(WdmSegmentDeleteRequestTest.TTP_NAME)
      .withProtectionGroupUri(PROTECTION_GROUP_URI)
      .build();
  }

  @Test
  void test_isSegmentOperationSupported() {
    //when
    boolean actual = sut.isSegmentOperationSupported(CrmSegmentRequestDto.RequestType.DELETE);

    //then
    assertThat(actual).as("Method isSegmentOperationSupported invoked with CrmSegmentRequestDto.RequestType.DELETE argument")
      .isTrue();
  }

  @Test
  void test_executeWhenSegmentNotExistThrowSegmentRequestFailedException() throws ObjectInUseException {

    //when
    var actual = sut.execute(request, null);

    //then
    assertThat(actual).isNotNull()
      .extracting(SegmentRequestOperationRet::operationCompleted, SegmentRequestOperationRet::deletedSegments)
      .containsExactly(true, Collections.emptyList());
  }


  @Test
  void test_executeWhenAlienPtpDTONotFound() throws ObjectInUseException, ObjectDoesNotExistException {
    //given
    WdmSegment segment = generateSegment(CrmNodePosition.INGRESS, SEGMENT_ID, SRC_TP_AID, "123", null);
    repository.store(segment);
    //when
    when(fiberTracer.findOtherEndOfAlienFiber(request.neId, segment.getAidSrcTp())).thenReturn(Optional.empty());
    var result = sut.execute(request, null);
    //then
    assertTrue(result.operationCompleted(), "Should return on success: true");
    verify(provision).deletePrtGrp(NE_ID, PROTECTION_GROUP_URI);
    verify(provision).deleteSlc(request.neId, segment.getSlcId());
    var actual = repository.getSegment(segment.getSegReqId(), segment.getNeId());
    assertThat(actual).isNull();
  }

  @Test
  void test_executeWhenDeleteSlcThrowsRuntimeException() throws ObjectDoesNotExistException {
    //given
    WdmSegment segment = generateSegment(CrmNodePosition.INGRESS, SEGMENT_ID, SRC_TP_AID, "123", null);
    repository.store(segment);
    //when
    when(fiberTracer.findOtherEndOfAlienFiber(request.neId, segment.getAidSrcTp())).thenReturn(Optional.empty());
    doThrow(new RuntimeException("failed")).when(provision).deleteSlc(request.neId, segment.getSlcId());
    SegmentRequestFailedException exception = assertThrows(SegmentRequestFailedException.class, () -> sut.execute(request, null));
    //then
    assertTrue(exception.getMessage().contains("Cannot delete SLC"), "Exception message should contains: Cannot delete SLC");
    verify(provision).deletePrtGrp(NE_ID, PROTECTION_GROUP_URI);
    verify(provision).deleteSlc(request.neId, segment.getSlcId());
    var actual = repository.getSegment(segment.getSegReqId(), segment.getNeId());
    assertThat(actual).isNull();
  }

  @Test
  void test_executeWhenDeleteFiberConnectionInBothDirectionFails() throws ObjectDoesNotExistException {
    //given
    WdmSegment segment = generateSegment(CrmNodePosition.INGRESS, SEGMENT_ID, SRC_TP_AID, "123", null);
    repository.store(segment);
    when(fiberTracer.findOtherEndOfAlienFiber(request.neId, segment.getTtpAid())).thenReturn(Optional.of(alienPtpDTO));
    doThrow(new RuntimeException("failed")).when(provision).deleteFiberConnection(request.neId, segment.getTtpAid(), alienPtpDTO.aidString());
    doThrow(new RuntimeException("failed")).when(provision).deleteFiberConnection(request.neId, alienPtpDTO.aidString(), segment.getTtpAid());

    //when
    var actual = assertThatThrownBy(() -> sut.execute(request, null));

    //then
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageContaining("Cannot delete FiberConnection");
    verify(provision).deletePrtGrp(NE_ID, PROTECTION_GROUP_URI);
    verify(provision).deleteSlc(request.neId, segment.getSlcId());
    var actualSegment = repository.getSegment(segment.getSegReqId(), segment.getNeId());
    assertThat(actualSegment).isNull();
  }

  @Test
  void test_executeWhenCannotDeleteAlienPtp() throws ObjectDoesNotExistException {
    //given
    WdmSegment segment = generateSegment(CrmNodePosition.INGRESS, SEGMENT_ID, SRC_TP_AID, null, null);
    repository.store(segment);
    when(fiberTracer.findOtherEndOfAlienFiber(request.neId, segment.getTtpAid())).thenReturn(Optional.of(alienPtpDTO));
    doThrow(new RuntimeException("failed")).when(provision).removeAlienPtp(request.neId, alienPtpDTO.shelfNo(), alienPtpDTO.alienPtpNumber());

    //when
    var actualAssertion = assertThatThrownBy(() -> sut.execute(request, null));

    //then
    actualAssertion.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("Cannot delete AlienPtp instance id=%d on shelf=%d. %s".formatted(alienPtpDTO.alienPtpNumber(),alienPtpDTO.shelfNo(),"failed"));
    verify(provision).deletePrtGrp(NE_ID, PROTECTION_GROUP_URI);
    verify(provision).deleteSlc(request.neId, segment.getSlcId());
    var actual = repository.getSegment(segment.getSegReqId(), segment.getNeId());
    assertThat(actual).isNull();
  }

  @Test
  void test_executeWhenDeleteCtpThrowRuntimeException() throws ObjectDoesNotExistException {
    //given
    WdmSegment segment = generateSegment(CrmNodePosition.INGRESS, SEGMENT_ID, "", null, NOT_BLANK);
    repository.store(segment);
    NetworkElementID networkElementID = NetworkElementID.create(request.neId);
    doThrow(new RuntimeException("failed")).when(provision).deleteCtp(networkElementID, segment.getTransponderCtpUri());

    //when
    var exception = assertThatThrownBy(() -> sut.execute(request, null));

    //then
    exception.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageContaining("Cannot delete CTP");
    verify(provision).deletePrtGrp(NE_ID, PROTECTION_GROUP_URI);
    verify(provision).deleteSlc(request.neId, segment.getSlcId());
    verify(provision).deleteCtp(networkElementID, segment.getTransponderCtpUri());
    var actual = repository.getSegment(segment.getSegReqId(), segment.getNeId());
    assertThat(actual).isNull();
  }

  @Test
  void test_executeWhenDeleteCtpThrowObjectDoesNotExistException() throws ObjectInUseException, ObjectDoesNotExistException {
    //given
    WdmSegment segment = generateSegment(CrmNodePosition.TRANSIT, SEGMENT_ID, "", null, NOT_BLANK);
    repository.store(segment);
    NetworkElementID networkElementID = NetworkElementID.create(request.neId);
    doThrow(new ObjectDoesNotExistException("The requested operation could not be completed because the entity does not exist."))
      .when(provision).deleteCtp(networkElementID, segment.getTransponderCtpUri());
    //when
    var result = sut.execute(request, null);
    //then
    assertTrue(result.operationCompleted(), "Execute should return on success: true");
    verify(provision).deletePrtGrp(NE_ID, PROTECTION_GROUP_URI);
    verify(provision).deleteSlc(request.neId, segment.getSlcId());
    verify(provision).deleteCtp(networkElementID, segment.getTransponderCtpUri());
    var actual = repository.getSegment(segment.getSegReqId(), segment.getNeId());
    assertThat(actual).isNull();
  }

  @Test
  void test_executeWhenCtpIsAutoCreated() throws ObjectInUseException, ObjectDoesNotExistException {
    //given
    WdmSegment segment = generateSegment(CrmNodePosition.INGRESS, SEGMENT_ID, "", null, NOT_BLANK);
    repository.store(segment);
    when(ctpResources.findCard(NE_ID, new Uri(NOT_BLANK)))
      .thenReturn(Optional.of(new CardRef(NE_ID, null, null, "AF-4X4XGT", 0, "transponder")));

    //when
    var result = sut.execute(request, null);

    //then
    assertTrue(result.operationCompleted(), "Execute should return on success: true");
    verify(provision).deletePrtGrp(NE_ID, PROTECTION_GROUP_URI);
    verify(provision).deleteSlc(request.neId, segment.getSlcId());
    verifyNoMoreInteractions(provision);
    var actual = repository.getSegment(segment.getSegReqId(), segment.getNeId());
    assertThat(actual).isNull();
  }

  @Test
  void test_executeWhenThereIsAnotherSegmentBasedOnSameCTP() throws ObjectInUseException, ObjectDoesNotExistException {
    //given
    WdmSegment segment1 = generateSegment(CrmNodePosition.INGRESS, SEGMENT_ID, "", null, NOT_BLANK);
    WdmSegment segment2 = generateSegment(CrmNodePosition.INGRESS, "other-segment-under-protection-group", "", null, NOT_BLANK);
    repository.store(segment1);
    repository.store(segment2);

    //when
    sut.execute(request, null);

    //then
    verify(provision).deletePrtGrp(NE_ID, PROTECTION_GROUP_URI);
    verify(provision).deleteSlc(NE_ID, segment1.getSlcId());
    verifyNoMoreInteractions(provision);
    var segmentsInCaseWithCtp = repository.findSegmentsByCtpTransponderUri(NE_ID, NOT_BLANK);
    assertThat(segmentsInCaseWithCtp).hasSize(1);
  }

  @Test
  void test_executeWhenNodePositionIsIngress() throws ObjectInUseException, ObjectDoesNotExistException {
    //given
    WdmSegment segment = generateSegment(CrmNodePosition.INGRESS, SEGMENT_ID, SRC_TP_AID, null, null);
    repository.store(segment);
    when(fiberTracer.findOtherEndOfAlienFiber(request.neId, segment.getTtpAid())).thenReturn(Optional.of(alienPtpDTO));
    //when
    var result = sut.execute(request, null);
    //then
    assertTrue(result.operationCompleted(), "Execute should return on success: true");
    verify(provision).deletePrtGrp(NE_ID, PROTECTION_GROUP_URI);
    verify(provision).deleteSlc(request.neId, segment.getSlcId());
    verify(fiberTracer).findOtherEndOfAlienFiber(request.neId, segment.getTtpAid());
    verify(provision).removeAlienPtp(request.neId, alienPtpDTO.shelfNo(), alienPtpDTO.alienPtpNumber());
    var actual = repository.getSegment(segment.getSegReqId(), segment.getNeId());
    assertThat(actual).isNull();
  }

  @Test
  void test_executeWhenNodePositionIsEgress() throws ObjectInUseException, ObjectDoesNotExistException {
    //given
    WdmSegment segment = generateSegment(CrmNodePosition.EGRESS, SEGMENT_ID, SRC_TP_AID, null, NOT_BLANK);
    repository.store(segment);
    PtpRef transponderPtp = new PtpRef(NE_ID, new Aid("ptpAid"), new Uri("ptpUri"), null);
    when(ctpResources.findPort(NE_ID, new Uri(NOT_BLANK))).thenReturn(Optional.of(transponderPtp));
    //when
    var result = sut.execute(request, null);
    //then
    assertTrue(result.operationCompleted(), "Execute should return on success: true");
    verify(provision).deletePrtGrp(NE_ID, PROTECTION_GROUP_URI);
    verify(provision).deleteSlc(request.neId, segment.getSlcId());
    verify(provision).updateAdminState(NetworkElementID.create(NE_ID), transponderPtp.uri().uri(), AdminState.AUTOMATIC_IN_SERVICE);
    var actual = repository.getSegment(segment.getSegReqId(), segment.getNeId());
    assertThat(actual).isNull();
  }

  @Test
  void test_executeWhenNodePositionTransit() throws ObjectInUseException {
    //given
    WdmSegment segment = generateSegment(CrmNodePosition.TRANSIT, SEGMENT_ID, "", null, NOT_BLANK);
    repository.store(segment);
    //when
    var result = sut.execute(request, null);
    //then
    assertTrue(result.operationCompleted(), "Execute should return on success: true");
    var actual = repository.getSegment(segment.getSegReqId(), segment.getNeId());
    assertThat(actual).isNull();
  }
}
