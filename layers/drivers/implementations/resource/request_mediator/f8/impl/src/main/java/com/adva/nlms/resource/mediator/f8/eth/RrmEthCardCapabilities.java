/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.eth;


import com.adva.nlms.opticalparameters.api.Values;

import java.util.List;

interface RrmEthCardCapabilities {
  boolean isEthCardSupported(String cardType);

  List<Values.PortPoolDescriptor.PortSetDescriptor> getPortSetDescriptors(int neId, String cardType, String plugType, String portId);

  boolean isEpteSupportedOnCard(int neId, String cardType, String cardMode);
}
