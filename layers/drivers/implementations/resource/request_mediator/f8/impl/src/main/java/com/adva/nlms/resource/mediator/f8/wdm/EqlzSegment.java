/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */
package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.common.config.EquipmentState;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.config.f8.croma.api.CromaMOService;
import com.adva.nlms.mediation.config.f8.croma.provision.api.SlcEqualizationDirection;
import com.adva.nlms.mediation.config.f8.croma.provision.api.SlcEqualizationException;
import com.adva.nlms.mediation.config.f8.croma.slc.api.Slc;
import com.adva.nlms.mediation.config.f8.croma.slc.api.SlcEndpoint;
import com.adva.nlms.mediation.ec.model.F8OperState;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.CtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.Ptp;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.resource.mediator.f8.NiCallbackSender;
import com.adva.nlms.resource.mediator.f8.PowerEqOperation;
import com.adva.nlms.resource.mediator.f8.SegmentRequestFailedException;
import com.adva.nlms.resource.mediator.f8.lock.NetworkTransactionWrapper;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.CrmNodePosition;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.WdmSegment;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionException;
import ni.proto.connection_segment.ConnectionSegmentOuterClass;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.EnumSet;
import java.util.List;
import java.util.Optional;

import static com.adva.nlms.resource.mediator.f8.PowerEqOperation.ABORT;
import static com.adva.nlms.resource.mediator.f8.PowerEqOperation.EQUALIZE_FORWARD;
import static com.adva.nlms.resource.mediator.f8.PowerEqOperation.EQUALIZE_REVERSE;
import static org.apache.commons.lang3.StringUtils.isEmpty;

class EqlzSegment {

  static final String LOG_PREFIX = "[POWEREQUALIZE]:";
  private static final String POWER_EQUALIZE_FAILED = "Failed to handle power equalize.";
  private static final String FORWARD = "FORWARD";
  private static final String REVERSE = "REVERSE";
  private static final String SLC_AZ = "SLC.AZ";
  private static final String SLC_ZA = "SLC.ZA";
  private static final List<CrmNodePosition> REVERSED_SLC_DIRECTION = List.of(
    CrmNodePosition.INGRESS, CrmNodePosition.TRANSIT_REVERSED);
  private static final Logger log = LogManager.getLogger(EqlzSegment.class);
  private static final EnumSet<EquipmentState> WORKING_LASER_ADMIN_STATES = EnumSet.of(
    EquipmentState.IS, EquipmentState.AINS, EquipmentState.MT, EquipmentState.MGT);
  private static final AdminState ENABLE_LASER_ADMIN_STATE = AdminState.UP;
  private final NiCallbackSender niCallbackSender;
  private final NetworkTransactionWrapper networkTransactionWrapper;
  private final CtpResources ctpResources;
  private final PtpResources ptpResources;
  private final Provision provision;
  private final CromaMOService cromaMOService;
  private final LaserPowerWatcher laserPowerWatcher;

  EqlzSegment(NiCallbackSender niCallbackSender,
              NetworkTransactionWrapper networkTransactionWrapper,
              CtpResources ctpResources,
              PtpResources ptpResources,
              Provision provision,
              CromaMOService cromaMOService,
              LaserPowerWatcher laserPowerWatcher) {
    this.niCallbackSender = niCallbackSender;
    this.networkTransactionWrapper = networkTransactionWrapper;
    this.ctpResources = ctpResources;
    this.ptpResources = ptpResources;
    this.provision = provision;
    this.cromaMOService = cromaMOService;
    this.laserPowerWatcher = laserPowerWatcher;
  }

  public static ConnectionSegmentOuterClass.PowerEqualizationOperation mapEqOperation(PowerEqOperation eqOperation) {
    return switch (eqOperation) {
      case UNKNOWN -> ConnectionSegmentOuterClass.PowerEqualizationOperation.UNKNOWN;
      case EQUALIZE_FORWARD -> ConnectionSegmentOuterClass.PowerEqualizationOperation.EQUALIZE_FORWARD;
      case EQUALIZE_REVERSE -> ConnectionSegmentOuterClass.PowerEqualizationOperation.EQUALIZE_REVERSE;
      case ABORT -> ConnectionSegmentOuterClass.PowerEqualizationOperation.ABORT;
      default -> ConnectionSegmentOuterClass.PowerEqualizationOperation.UNRECOGNIZED;
    };
  }

  public void equalizeSegment(PowerEqOperation operation, WdmSegment segment) throws SegmentRequestFailedException {
    if (operation == ABORT) {
      segment.clearEqualizationData();
    } else {
      segment.setCorrelationIdFromContext();
      try {
        if (isTxEdgeNode(operation, segment) && isEquippedTransponder(segment)) {
          networkTransactionWrapper.executeInNetworkTransaction(segment.getNeId(),
            () -> beginTransponderEqualization(operation, segment), LOG_PREFIX);
        } else {
          beginSlcEqualization(operation, segment);
        }
      } catch (Exception e) {
        segment.clearEqualizationData();
        throw new SegmentRequestFailedException(POWER_EQUALIZE_FAILED + e.getMessage(), e);
      }
    }
  }

  public void beginSlcEqualizationIfRelevant(WdmSegment segment) {
    var direction = segment.getWaitingForLaserOnDelayClear();
    if (direction != null) {
      segment.setWaitingForLaserOnDelayClear(null);
      beginSlcEqualizationWithNiCallbackOnFailure(direction, segment);
    } else {
      if (log.isInfoEnabled()) {
        log.info("{} Not waiting for Laser On Delay clear {}", LOG_PREFIX, LaserPowerWatcherContext.getLogPostfix(segment));
      }
    }
  }

  private boolean isTxEdgeNode(PowerEqOperation direction, WdmSegment segment) {
    if (isEqualizeForward(direction)) {
      return segment.getNodePosition() == CrmNodePosition.INGRESS;
    } else {
      return segment.getNodePosition() == CrmNodePosition.EGRESS;
    }
  }

  private boolean isEquippedTransponder(WdmSegment segment) {
    if (!isTransponder(segment)) {
      return false;
    }
    if (isUnequipped(segment)) {
      if (log.isInfoEnabled()) {
        log.info("{} Transponder is unequipped, skipping turning laser on {}", LOG_PREFIX, LaserPowerWatcherContext.getLogPostfix(segment));
      }
      return false;
    }
    return true;
  }

  private void beginSlcEqualizationWithNiCallbackOnFailure(PowerEqOperation direction, WdmSegment segment) {
    try {
      beginSlcEqualization(direction, segment);
    } catch (Exception e) {
      var niDirection = mapEqOperation(direction);
      niCallbackSender.sendEqualizationFailure(e.getMessage(), niDirection, segment);
    }
  }

  private void ensureAdminStateOnCtp(WdmSegment segment) {
    var neId = segment.getNeId();

    ctpResources.findCtp(neId, new Uri(segment.getTransponderCtpUri()))
      .ifPresent(
        ctp -> {
          if (!WORKING_LASER_ADMIN_STATES.contains(ctp.state().adminState())) {
            // If there is no CTP, then assume it exists and is in UP state, because it was just created by segment create,
            // but was not yet synchronized in db
            var ctpAid = ctp.aid().aid();
            if (log.isInfoEnabled()) {
              log.info("{} {} admin state is {} changing to {} {}",
                       LOG_PREFIX,
                       ctpAid,
                       ctp.state().adminState(),
                       ENABLE_LASER_ADMIN_STATE,
                       LaserPowerWatcherContext.getLogPostfix(segment));
            }
            provision.updateAdminState(NetworkElementID.create(neId), ctpAid, AdminState.UP);
          }
        }
      );
  }

  private void ensureAdminStateOnPtp(Ptp ptp, WdmSegment segment) {
    if (!WORKING_LASER_ADMIN_STATES.contains(ptp.state().adminState())) {
      var ptpAid = ptp.aid().aid();
      if (log.isInfoEnabled()) {
        log.info("{} {} admin state is {} changing to {} {}",
                 LOG_PREFIX,
                 ptpAid,
                 ptpAid,
                 ENABLE_LASER_ADMIN_STATE,
                 LaserPowerWatcherContext.getLogPostfix(segment));
      }
      provision.updateAdminState(NetworkElementID.create(segment.getNeId()), ptpAid, ENABLE_LASER_ADMIN_STATE);
    }
  }

  private Ptp getPtp(WdmSegment segment) {
    var neId = segment.getNeId();
    var ptpAid = segment.getTtpAid();
    return ptpResources.findPtpExtended(neId, new Aid(ptpAid))
      .orElseThrow(() -> new SegmentRequestFailedException("%s - PTP %s object not found %s".formatted(LOG_PREFIX,
        ptpAid,
        LaserPowerWatcherContext.getLogPostfix(segment))));
  }

  private boolean isTransponder(WdmSegment segment) {
    return !isEmpty(segment.getTransponderCtpUri());
  }

  private boolean isUnequipped(WdmSegment segment) {
    var neId = segment.getNeId();
    var ptpAid = segment.getTtpAid();
    return isUnequippedPtp(neId, new Aid(ptpAid));
  }

  public boolean isUnequippedPtp(int neId, Aid ptpAid) {
    Optional<CardRef> cardRef = ptpResources.findCard(neId, ptpAid);

    if (cardRef.isPresent() && F8OperState.OUT.getValue() == cardRef.get().operState()) {
      return true;
    }

    return cardRef
      .flatMap(card -> ptpResources.findPlug(neId, ptpAid))
      .filter(plug -> F8OperState.OUT.getValue() == plug.operState())
      .isPresent();
  }

  private void beginTransponderEqualization(PowerEqOperation direction, WdmSegment segment) {
    var ptp = getPtp(segment);
    try {
      ensureAdminStateOnPtp(ptp, segment);
      ensureAdminStateOnCtp(segment);
    } catch (Exception e) {
      throw new SegmentRequestFailedException(e);
    }
    LaserPowerWatcherContext context = new LaserPowerWatcherContext(segment, ptp, direction);
    laserPowerWatcher.whenLaserIsOnOrLODRaised(context, this::beginSlcEqualizationIfRelevant);
  }

  private void beginSlcEqualization(PowerEqOperation direction, WdmSegment segment) throws ObjectInUseException {
    if (segment.getSlcId() == 0) {
      skipSlcEqualization(direction, segment);
    } else {
      networkTransactionWrapper.executeInNetworkTransaction(segment.getNeId(),
        () -> equalizeSlc(direction, segment), LOG_PREFIX);
    }
  }

  private void skipSlcEqualization(PowerEqOperation direction, WdmSegment segment) {
    if (log.isInfoEnabled()) {
      log.info("{} equalizeSlc: No SLC to perform equalization {}", LOG_PREFIX, LaserPowerWatcherContext.getLogPostfix(segment));
    }
    sendEqSuccessUpdateNotificationToNI(direction, segment);
  }

  private void equalizeSlc(PowerEqOperation direction, WdmSegment segment) {
    try {
      Slc slc = findSlcBySlcIdentifier(segment);
      if (REVERSED_SLC_DIRECTION.contains(segment.getNodePosition())) {
        if (isEqualizeForward(direction)) {
          logEqualizationStarted(FORWARD, SLC_ZA, segment);
          handleZAEqualization(slc, segment);
        } else if (isEqualizeReverse(direction)) {
          logEqualizationStarted(REVERSE, SLC_AZ, segment);
          handleAZEqualization(slc, segment);
        }
      } else {
        if (isEqualizeForward(direction)) {
          logEqualizationStarted(FORWARD, SLC_AZ, segment);
          handleAZEqualization(slc, segment);
        } else if (isEqualizeReverse(direction)) {
          logEqualizationStarted(REVERSE, SLC_ZA, segment);
          handleZAEqualization(slc, segment);
        }
      }
    } catch (NullPointerException e) {
      log.error("{} failed: Exception while handling Power Equalize, neId={}, slcId={}, error={} ",
        LOG_PREFIX, segment.getNeId(), segment.getSlcId(), e.getMessage(), e);
      throwEqualizationError(e);
    } catch (ProvisionException e) {
      log.error("{} failed: Exception after modifySlcAdminState invoked, neId={}, slcId={}, error={} ",
        LOG_PREFIX, segment.getNeId(), segment.getSlcId(), e.getMessage(), e);
      throwEqualizationError(e);
    } catch (SlcEqualizationException e) {
      log.error("{} failed: Exception after Equalization has been invoked, neId={}, slcId={}, error={} ",
        LOG_PREFIX, segment.getNeId(), segment.getSlcId(), e.getMessage(), e);
      throwEqualizationError(e);
    }
  }

  private void handleAZEqualization(Slc slc, WdmSegment segment) throws SlcEqualizationException, ProvisionException {
    Long aEndEqTimeOld = getEqualizationTime(slc.getaEndpoint());
    segment.setEqExpected(aEndEqTimeOld, true);
    boolean isAEndAdminStateIS = EquipmentState.valueOf(slc.getaEndpoint().getAdminState()) == EquipmentState.IS;
    if (isAEndAdminStateIS) {
      cromaMOService.invokeSLCEqualization(segment.getNeId(), segment.getSlcId(), SlcEqualizationDirection.PATH_AZ);
    } else {
      provision.updateSLCAdminState(segment.getNeId(), segment.getSlcId(), SlcEqualizationDirection.PATH_AZ, AdminState.UP);
    }
  }

  private void handleZAEqualization(Slc slc, WdmSegment segment) throws SlcEqualizationException, ProvisionException {
    Long zEndEqTimeOld = getEqualizationTime(slc.getzEndpoint());
    segment.setEqExpected(zEndEqTimeOld, false);
    boolean isZEndAdminStateIS = EquipmentState.valueOf(slc.getzEndpoint().getAdminState()) == EquipmentState.IS;
    if (isZEndAdminStateIS) {
      cromaMOService.invokeSLCEqualization(segment.getNeId(), segment.getSlcId(), SlcEqualizationDirection.PATH_ZA);
    } else {
      provision.updateSLCAdminState(segment.getNeId(), segment.getSlcId(), SlcEqualizationDirection.PATH_ZA, AdminState.UP);
    }
  }

  private Long getEqualizationTime(SlcEndpoint slcEndpoint) {
    if (slcEndpoint == null || slcEndpoint.getSlcEqState() == null || slcEndpoint.getSlcEqState().getEqTime() == null)
      return (long) -1;
    return slcEndpoint.getSlcEqState().getEqTime();
  }

  private void logEqualizationStarted(String niDirection, String slcDirection, WdmSegment segment) {
    log.info("{} Equalization is invoked in {} direction({}), NodePosition={}, for neId={}, slcId={}, segReqId={}",
      LOG_PREFIX, niDirection, slcDirection, segment.getNodePosition(), segment.getNeId(), segment.getSlcId(), segment.getSegReqId());
  }

  private void sendEqSuccessUpdateNotificationToNI(PowerEqOperation direction, WdmSegment segment) {
    var equalizationDirection = mapEqOperation(direction);
    niCallbackSender.sendEqualizationSuccess(equalizationDirection, segment);
  }

  // The method attempts to retrieve the SLC from the database. If it’s not found, it is fetched directly from the NE.
  private Slc findSlcBySlcIdentifier(WdmSegment dto) throws SegmentRequestFailedException {
    return cromaMOService.getSlcForNeAndSlcId(dto.getNeId(), dto.getSlcId())
      .orElseGet(() -> cromaMOService.getSlcDirecly(dto.getNeId(), dto.getSlcId())
        .orElseThrow(() -> new SegmentRequestFailedException("%s Cannot fetch Slc from the SlcId. neId=%d slcId=%d segReqId=%S"
            .formatted(POWER_EQUALIZE_FAILED, dto.getNeId(), dto.getSlcId(), dto.getSegReqId())
          )
        )
      );
  }

  private void throwEqualizationError(Exception e) throws SegmentRequestFailedException {
    throw new SegmentRequestFailedException(POWER_EQUALIZE_FAILED + e.getMessage(), e);
  }

  private boolean isEqualizeForward(PowerEqOperation direction) {
    return direction == EQUALIZE_FORWARD;
  }

  private boolean isEqualizeReverse(PowerEqOperation direction) {
    return direction == EQUALIZE_REVERSE;
  }

}