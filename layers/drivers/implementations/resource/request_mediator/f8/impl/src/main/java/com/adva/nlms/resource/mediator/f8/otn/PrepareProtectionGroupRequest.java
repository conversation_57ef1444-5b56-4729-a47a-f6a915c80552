/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.resource.mediator.f8.AssignRoles;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegmentRepository;
import com.adva.nlms.resource.mediator.f8.otn.api.in.SegmentID;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionException;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

class PrepareProtectionGroupRequest {
  private final Logger log = LogManager.getLogger(PrepareProtectionGroupRequest.class);
  private final int neId;
  private final AssignRoles assignRoles;
  private final Provision provisionApi;
  private final OtnSegmentRepository otnSegmentRepository;
  private final RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade;
  private final RrmOtnCardCapabilities rrmOtnCardCapabilities;

  public PrepareProtectionGroupRequest(int neId, AssignRoles assignRoles, Provision provisionApi, OtnSegmentRepository otnSegmentRepository,
                                       RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade, RrmOtnCardCapabilities rrmOtnCardCapabilities) {
    this.neId = neId;
    this.assignRoles = assignRoles;
    this.provisionApi = provisionApi;
    this.otnSegmentRepository = otnSegmentRepository;
    this.rrmOtnDbResourcesFacade = rrmOtnDbResourcesFacade;
    this.rrmOtnCardCapabilities = rrmOtnCardCapabilities;
  }

  public ProtectionGroupRequest prepareRequest() throws ProvisionException {
    var workingSegment = otnSegmentRepository.findOne(new SegmentID(neId, assignRoles.workingLegId()));
    var protectionSegment = otnSegmentRepository.findOne(new SegmentID(neId, assignRoles.protectionLegId()));
    if (workingSegment.protectionData().isCccProtection() && protectionSegment.protectionData().isCccProtection()) {
      log.info("Processing CCC PROTECTION GROUP request for neId={}", neId);
      return new PrepareCccProtectionGroupRequest(neId, workingSegment, protectionSegment, assignRoles.protectionSettings(), provisionApi, rrmOtnDbResourcesFacade).prepareRequest();
    } else if (workingSegment.protectionData().isLineProtection() && protectionSegment.protectionData().isLineProtection()) {
      log.info("Processing SNC PROTECTION GROUP request for neId={}", neId);
      return new PrepareSncProtectionGroupRequest(neId, workingSegment, protectionSegment, assignRoles.protectionSettings(), provisionApi, rrmOtnDbResourcesFacade, rrmOtnCardCapabilities).prepareRequest();
    }
    throw new ProvisionException(String.format("Unsupported protection group type for neId=%d, assignRoles=%s", neId, assignRoles));
  }
}
