/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */
package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.resource.mediator.f8.events.CccProtectionGroupCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.CtpAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.CtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.EpteModifyEvent;
import com.adva.nlms.resource.mediator.f8.events.PtpAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncProtectionGroupCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.mediator.f8.events.PtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncModifyEvent;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegment;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegmentRepository;
import com.adva.nlms.resource.mediator.f8.otn.api.in.ProtectionData;
import com.adva.nlms.resource.mediator.f8.otn.api.in.SegmentID;
import com.adva.nlms.resource.provision.f8.api.in.ObjectManagedBySystemException;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;

class RequestExecutor {

  private static final Logger log = LogManager.getLogger(RequestExecutor.class);

  private final OtnSegmentRepository otnSegmentRepository;

  private final Provision provisionApi;

  RequestExecutor(OtnSegmentRepository otnSegmentRepository, Provision provisionApi) {
    this.otnSegmentRepository = otnSegmentRepository;
    this.provisionApi = provisionApi;
  }

  void createOrAdopt(List<Request> requests, int neId, String segmentId, ProtectionData protectionData) {
    List<ProvisionLocalEvent> provisionEvents = new ArrayList<>();
    try {
      for (var req : requests) {
        log.info("[OTN] Prepared request: {}", req);
        ProvisionLocalEvent created = req.provision();
        if (created != null) {
          provisionEvents.add(created);
        }
      }
      if (protectionData != null) {
        otnSegmentRepository.save(new OtnSegment(new SegmentID(neId, segmentId), provisionEvents, protectionData));
      } else {
        otnSegmentRepository.save(new OtnSegment(new SegmentID(neId, segmentId), provisionEvents));
      }
    } catch (ProvisionException exc) {
      log.error("Exception raised during create or adopt operation, invoking rollback", exc);
      delete(neId, provisionEvents, false);
      throw exc;
    }
  }

  void adopt(List<Request> requests, int neId, String segmentId, ProtectionData protectionData) {
    log.info("adopt segment {}", segmentId);
    List<ProvisionLocalEvent> adoptEvents = requests.stream()
      .map(Request::adopt)
      .filter(Objects::nonNull)
      .toList();
    if (protectionData != null) {
      otnSegmentRepository.save(new OtnSegment(new SegmentID(neId, segmentId), adoptEvents, protectionData));
    } else {
      otnSegmentRepository.save(new OtnSegment(new SegmentID(neId, segmentId), adoptEvents));
    }
  }

  private void delete(int neId, List<ProvisionLocalEvent> provisionEvents, boolean deleteAdopt) {
    for (int i = provisionEvents.size() - 1; i >= 0; i--) {
      ProvisionLocalEvent event = provisionEvents.get(i);
      deleteRequestFromEvent(neId, event, deleteAdopt);
    }
  }

  void delete(int neId, String segmentId, List<ProvisionLocalEvent> provisionEvents) {
    SegmentID segmentID = new SegmentID(neId, segmentId);
    try {
      delete(neId, provisionEvents, true);
    } catch (ProvisionException e) {
      log.error("Could not delete request from event in segment {}", segmentId);
      throw e;
    } finally {
      otnSegmentRepository.delete(segmentID);
    }
  }

  private void deleteRequestFromEvent(int neId, ProvisionLocalEvent event, boolean deleteAdopt) throws ProvisionException {
    Request request = getRequestFromEventType(neId, event, deleteAdopt);
    if (request != null) {
      try {
        request.delete();
      } catch (ObjectManagedBySystemException e) {
        log.info("Object {} is managed by the device system and cannot be deleted. " +
          "It will be removed with corresponding parent entity.", event.uri());
      } catch (ProvisionException e) {
        log.warn("Provision Exception could not delete request for event {}", event.uri());
      } catch (Exception e) {
        log.warn("Could not delete request for event {}", event.uri());
      }
    }
  }

  private Request getRequestFromEventType(int neId, ProvisionLocalEvent event, boolean deleteAdopt) {
    if (event instanceof SncProtectionGroupCreatedEvent) {
      return new SncProtectionGroupRequest(neId, event.uri(), provisionApi, false);
    }
    if (event instanceof CccProtectionGroupCreatedEvent) {
      return new CccProtectionGroupRequest(neId, event.uri(), provisionApi);
    }
    if (event instanceof CtpCreatedEvent || (deleteAdopt && event instanceof CtpAdoptedEvent)) {
      return new CtpRequest(neId, event.uri(), false, false, provisionApi);
    }
    if (event instanceof SncCreatedEvent || event instanceof SncModifyEvent || (deleteAdopt && event instanceof SncAdoptedEvent)) {
      return new SncRequest(neId, event.uri(), false, null, null, provisionApi);
    }
    if (event instanceof PtpCreatedEvent) {
      return new PtpRequest(neId, event.uri(), false, provisionApi);
    }
    if (event instanceof PtpAdoptedEvent) {
      return new PtpParamsRequest(neId, event.uri(), true, new HashSet<>(), provisionApi);
    }
    if (event instanceof EpteModifyEvent epteModifyEvent) {
      return new EpteRequest(neId, epteModifyEvent.epteUri(), event.uri(), provisionApi);
    }
    return null;
  }

  void abandon(int neId, String segmentId) {
    log.info("Abandon segment {}", segmentId);
    otnSegmentRepository.delete(new SegmentID(neId, segmentId));
  }

  void createProtectionGroup(ProtectionGroupRequest protectionGroupRequest) {
    log.info("Create protection group {}", protectionGroupRequest);
    protectionGroupRequest.provision();
  }
}
