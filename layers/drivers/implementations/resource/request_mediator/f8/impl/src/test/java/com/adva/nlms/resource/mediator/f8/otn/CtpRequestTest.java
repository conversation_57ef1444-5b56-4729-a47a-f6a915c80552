/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: zbigniewj
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.cim.api.CimValue;
import com.adva.nlms.resource.mediator.f8.events.CtpAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.CtpAutocreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.CtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.otn.CtpRequest;
import com.adva.nlms.resource.provision.f8.api.in.MoCimParameter;
import com.adva.nlms.resource.provision.f8.api.in.ObjectDoesNotExistException;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public class CtpRequestTest {

  static private final int NETWORK_ELEMENT_ID = 3;
  static private final String CTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/nw,1/ctp/otuc2/ctp/oduc2/ctp/odu4-1";
  static private final int tp = 6;
  static private final List<Long> ts = List.of(5L, 6L, 1L, 30L);
  static private final CimValue cimTp = new CimValue.Int(tp);
  static private final CimValue cimTs = new CimValue.IntList(ts);

  static private final Set<MoCimParameter> params =
    Set.of(
      new MoCimParameter("trait", "tp", cimTp),
      new MoCimParameter("trait", "ts", cimTs));
  private final Provision provisionApi = Mockito.mock(Provision.class);

  @Test
  void deleteCtp() throws ObjectDoesNotExistException {
    var request = new CtpRequest(NETWORK_ELEMENT_ID, CTP_URI, false, false, provisionApi);

    request.delete();

    Mockito.verify(provisionApi, Mockito.times(1))
      .deleteCtp(Mockito.any(), Mockito.any());
  }

  @Test
  void provisionCreateCtp() {
    Mockito
      .when(provisionApi.provisionCtp(
        Mockito.eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
        Mockito.eq(CTP_URI),
        Mockito.any()))
      .thenReturn(Optional.empty());

    var request = new CtpRequest(NETWORK_ELEMENT_ID, CTP_URI, false, false, provisionApi);

    var result = request.provision();
    Assertions.assertEquals(new CtpCreatedEvent(CTP_URI), result);
  }

  @Test
  void provisionAutocreatedCtp() {
    var request = new CtpRequest(NETWORK_ELEMENT_ID, CTP_URI, false, true, provisionApi);

    var result = request.provision();

    Mockito.verifyNoInteractions(provisionApi);
    Assertions.assertEquals(new CtpAutocreatedEvent(CTP_URI), result);
  }

  @Test
  void provisionAutocreatedCtpWithParams() {
    var request = new CtpRequest(NETWORK_ELEMENT_ID, CTP_URI, false, true, params, provisionApi);

    var result = request.provision();

    Mockito.verify(provisionApi).modifyCtp(Mockito.eq(NetworkElementID.create(NETWORK_ELEMENT_ID)), Mockito.eq(CTP_URI), Mockito.eq(params));
    Mockito.verify(provisionApi, Mockito.never()).provisionCtp(Mockito.any(), Mockito.any(), Mockito.any());
    Assertions.assertEquals(new CtpAutocreatedEvent(CTP_URI), result);
  }

  @Test
  void provisionAutocreatedCtpWithExistsFlag() {
    var request = new CtpRequest(NETWORK_ELEMENT_ID, CTP_URI, true, true, provisionApi);

    var result = request.provision();

    Mockito.verifyNoInteractions(provisionApi);
    Assertions.assertEquals(new CtpAutocreatedEvent(CTP_URI), result);
  }

  @Test
  void provisionAdoptCtp() {
    var request = new CtpRequest(NETWORK_ELEMENT_ID, CTP_URI, true, false, provisionApi);

    var result = request.provision();

    Mockito.verifyNoInteractions(provisionApi);
    Assertions.assertEquals(new CtpAdoptedEvent(CTP_URI), result);
  }

  @Test
  void provisionAdoptCtpWithParams() {
    var request = new CtpRequest(NETWORK_ELEMENT_ID, CTP_URI, true, false, params, provisionApi);

    var result = request.provision();

    Mockito.verify(provisionApi).modifyCtp(Mockito.eq(NetworkElementID.create(NETWORK_ELEMENT_ID)), Mockito.eq(CTP_URI), Mockito.eq(params));
    Mockito.verify(provisionApi, Mockito.never()).provisionCtp(Mockito.any(), Mockito.any(), Mockito.any());
    Assertions.assertEquals(new CtpAdoptedEvent(CTP_URI), result);
  }

  @Test
  void adoptAutocreatedCtp() {
    var request = new CtpRequest(NETWORK_ELEMENT_ID, CTP_URI, false, true, provisionApi);

    var result = request.adopt();

    Mockito.verifyNoInteractions(provisionApi);
    Assertions.assertEquals(new CtpAutocreatedEvent(CTP_URI), result);
  }
}
