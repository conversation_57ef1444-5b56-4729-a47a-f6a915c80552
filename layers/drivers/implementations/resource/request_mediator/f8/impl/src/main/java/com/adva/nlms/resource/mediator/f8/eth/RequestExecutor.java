/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.eth;

import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.resource.mediator.f8.events.CtpAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.CtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.mediator.f8.eth.api.in.EthSegment;
import com.adva.nlms.resource.mediator.f8.eth.api.in.EthSegmentRepository;
import com.adva.nlms.resource.mediator.f8.eth.api.in.SegmentID;
import com.adva.nlms.resource.mediator.f8.events.PtpAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.PtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncCreatedEvent;
import com.adva.nlms.resource.mediator.f8.lock.NetworkTransactionWrapper;
import com.adva.nlms.resource.provision.f8.api.in.ObjectManagedBySystemException;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionException;
import com.adva.nlms.txprovisioning.api.ProvisionTransactional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;

class RequestExecutor {
  private static final Logger log = LogManager.getLogger(RequestExecutor.class);
  private final EthSegmentRepository ethSegmentRepository;
  private final Provision provisionApi;
  private final NetworkTransactionWrapper networkTransactionWrapper;

  RequestExecutor(EthSegmentRepository ethSegmentRepository, Provision provisionApi,
                  NetworkTransactionWrapper networkTransactionWrapper) {
    this.ethSegmentRepository = ethSegmentRepository;
    this.provisionApi = provisionApi;
    this.networkTransactionWrapper = networkTransactionWrapper;
  }

  void createOrAdopt(List<Request> requests, int neId, String segmentId) {
    try {
      networkTransactionWrapper.executeInNetworkTransaction(neId,
          () -> createOrAdoptTransaction(requests, new ProvisionContext(neId, segmentId)),
          "Segment Creation");
    } catch (ObjectInUseException e) {
      log.error("[ETH] Create adopt of segment {} failed", segmentId);
    }
  }

  @ProvisionTransactional
  private void createOrAdoptTransaction(List<Request> requests, ProvisionContext provisionContext) {
    List<ProvisionLocalEvent> provisionEvents = new ArrayList<>();
    try {
      requests.forEach(request -> {
        log.info("[ETH] Provisioning: {}", request);
        ProvisionLocalEvent created = request.provision();
        if (created != null) {
          provisionEvents.add(created);
        }
      });
      ethSegmentRepository.save(new EthSegment(new SegmentID(provisionContext.neId(), provisionContext.segmentId()),
          provisionEvents));
      log.debug("[ETH] NE: {}, Segment:{} created", provisionContext.neId(), provisionContext.segmentId());
    } catch (ProvisionException e) {
      log.error("Exception raised during create or adopt operation for segment {}", provisionContext.segmentId());
      throw e;
    }
  }

  void adopt(List<Request> requests, int neId, String segmentId) {
    log.info("[ETH] Adopt segment {}", segmentId);
    List<ProvisionLocalEvent> adoptEvents = requests.stream()
        .map(Request::adopt)
        .filter(Objects::nonNull)
        .toList();
    ethSegmentRepository.save(new EthSegment(new SegmentID(neId, segmentId),
        adoptEvents));
  }

  void delete(List<ProvisionLocalEvent> provisionEvents, int neId, String segmentId) {
    try {
      networkTransactionWrapper.executeInNetworkTransaction(neId,
          () -> deleteTransaction(provisionEvents, new ProvisionContext(neId, segmentId)),
          "Segment Deletion");
      log.debug("[ETH] NE: {}, Segment:{} deleted", neId, segmentId);
    } catch (ObjectInUseException e) {
      log.error("[ETH] Deletion of segment {} failed", segmentId);
    }
  }

  @ProvisionTransactional
  void deleteTransaction(List<ProvisionLocalEvent> provisionEvents, ProvisionContext provisionContext) {
    SegmentID segmentID = new SegmentID(provisionContext.neId(), provisionContext.segmentId());
    try {
      for (int i = provisionEvents.size() - 1; i >= 0; i--) {
        deleteRequestFromEvent(provisionContext.neId(), provisionEvents.get(i), true);
      }
    } catch (ProvisionException e) {
      log.error("[ETH] Could not delete request from event in segment {}", provisionContext.segmentId());
      throw e;
    } finally {
      ethSegmentRepository.delete(segmentID);
    }
  }

  private void deleteRequestFromEvent(int neId, ProvisionLocalEvent event, boolean deleteAdopt) throws ProvisionException {
    Request request = getRequestFromEventType(neId, event, deleteAdopt);
    if (request != null) {
      try {
        log.info("[ETH] Deleting: {}", request);
        request.delete();
      } catch (ObjectManagedBySystemException e) {
        log.info("[ETH] Object {} is managed by the device system and cannot be deleted. " +
            "It will be removed with corresponding parent entity.", event.uri());
      } catch (ProvisionException e) {
        log.warn("[ETH] Provision Exception could not deleteAdopt request for event {}", event.uri());
      } catch (Exception e) {
        log.warn("[ETH] Could not deleteAdopt request for event {}", event.uri());
      }
    }
  }

  void abandon(int neId, String segmentId) {
    log.info("Abandon segment {}", segmentId);
    ethSegmentRepository.delete(new SegmentID(neId, segmentId));
  }

  private Request getRequestFromEventType(int neId, ProvisionLocalEvent event, boolean deleteAdopt) {
    if (event instanceof CtpCreatedEvent || (deleteAdopt && event instanceof CtpAdoptedEvent)) {
      return new CtpRequest(neId, event.uri(), false, provisionApi);
    } else if (event instanceof PtpCreatedEvent) {
      return new PtpRequest(neId, event.uri(), false, provisionApi);
    } else if (event instanceof SncCreatedEvent || (deleteAdopt && event instanceof SncAdoptedEvent)) {
      return new SncRequest(neId, event.uri(), false, null, null, provisionApi);
    } else if (event instanceof PtpAdoptedEvent) {
      return new PtpParamsRequest(neId, event.uri(), true, new HashSet<>(), provisionApi);
    }
    return null;
  }
}
