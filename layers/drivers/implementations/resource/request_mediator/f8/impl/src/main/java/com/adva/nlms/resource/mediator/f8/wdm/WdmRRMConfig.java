/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.mediation.config.f8.croma.api.CromaMOService;
import com.adva.nlms.mediation.mo.inventory.f8.PMScanner;
import com.adva.nlms.mediation.mo.inventory.resources.CtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.FiberResources;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.resource.crm.model.CrmCtrlTaskScheduler;
import com.adva.nlms.resource.crm.model.wdm.CrmWdmModelDAO;
import com.adva.nlms.resource.mediator.f8.NiCallbackSender;
import com.adva.nlms.resource.mediator.f8.lock.NetworkTransactionWrapper;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.RrmSegmentRepository;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
class WdmRRMConfig {
  @Bean
  WdmResourceRequestMediator wdmResourceRequestMediatorImpl(SegmentOperationDispatcher segmentOperationDispatcher,
                                                            MonitoredEntityManager monitoredEntityManager,
                                                            CrmCtrlTaskScheduler crmCtrlTaskScheduler,
                                                            RrmSegmentRepository rrmSegmentRepository) {
    return new WdmResourceRequestMediatorImpl(segmentOperationDispatcher, monitoredEntityManager, crmCtrlTaskScheduler,
      rrmSegmentRepository);
  }

  @Bean
  LaserOnDelayConditionRepository laserOnDelayConditionRepository() {
    return new LaserOnDelayConditionRepository();
  }

  @Bean
  EqlzSegment eqlzSegment(NiCallbackSender niCallbackSender,
                          NetworkTransactionWrapper networkTransactionWrapper,
                          CtpResources ctpResources,
                          PtpResources ptpResources,
                          Provision provision,
                          CromaMOService cromaMOService,
                          LaserPowerWatcher laserPowerWatcher) {
    return new EqlzSegment(niCallbackSender, networkTransactionWrapper, ctpResources, ptpResources, provision, cromaMOService, laserPowerWatcher);
  }

  @Bean
  FiberTracer fiberTracer(FiberResources fiberResources, PtpResources ptpResources) {
    return new FiberTracer(fiberResources, ptpResources);
  }

  @Bean
  EqualizeStatusUpdateUseCase equalizeStatusUpdateUseCase(RrmSegmentRepository rrmSegmentRepository,
                                                          NiCallbackSender niCallbackSender) {
    return new EqualizeStatusUpdateUseCase(rrmSegmentRepository, niCallbackSender);
  }

  @Bean
  EqlzLaserOnDelayUseCase eqlzLaserOnDelayUseCase(RrmSegmentRepository rrmSegmentRepository,
                                                  EqlzSegment crmSegmentEqualizer,
                                                  LaserOnDelayConditionRepository lodRepository) {
    return new EqlzLaserOnDelayUseCase(rrmSegmentRepository, crmSegmentEqualizer, lodRepository);
  }

  @Bean
  AGATESStatusUpdateUseCase agatesStatusUpdateUseCase(RrmSegmentRepository rrmSegmentRepository,
                                                      NiCallbackSender niCallbackSender) {
    return new AGATESStatusUpdateUseCase(rrmSegmentRepository, niCallbackSender);
  }

  @Bean
  EqualizeUseCasesImpl equalizeUseCases(EqualizeStatusUpdateUseCase equalizeStatusUpdateUseCase,
                                        EqlzLaserOnDelayUseCase eqlzLaserOnDelayUseCase,
                                        AGATESStatusUpdateUseCase agatesStatusUpdateUseCase) {
    return new EqualizeUseCasesImpl(equalizeStatusUpdateUseCase, eqlzLaserOnDelayUseCase, agatesStatusUpdateUseCase);
  }

  @Bean
  LaserOnDelayCleanupPeriodicScheduler rrmTasksExecutor(CrmCtrlTaskScheduler taskScheduler,
                                                        LaserOnDelayConditionRepository laserOnDelayConditionRepository) {
    return new LaserOnDelayCleanupPeriodicScheduler(taskScheduler, laserOnDelayConditionRepository);
  }

  @Bean
  MonitoredEntityManager monitoredEntityManager(CtpResources ctpResources,
                                                PtpResources ptpResources,
                                                TpOutageCpcNotifier tpOutageCpcNotifier,
                                                CrmCtrlTaskScheduler crmCtrlTaskScheduler) {
    return new MonitoredEntityManager(ctpResources, ptpResources, tpOutageCpcNotifier, crmCtrlTaskScheduler);
  }

  @Bean
  TpOutageCpcNotifier tpOutageCpcNotifier(NiCallbackSender niCallbackSender,
                                          CrmWdmModelDAO crmWdmModelDAO,
                                          RrmSegmentRepository rrmSegmentRepository) {
    return new TpOutageCpcNotifier(new OutageMonitor(), niCallbackSender, crmWdmModelDAO, rrmSegmentRepository);
  }

  @Bean
  LaserPowerWatcher laserPowerWatcher(CrmCtrlTaskScheduler crmCtrlTaskScheduler,
                                      PMScanner pmScanner,
                                      LaserOnDelayConditionRepository laserOnDelayConditionRepository) {
    return new LaserPowerWatcher(crmCtrlTaskScheduler, pmScanner, laserOnDelayConditionRepository);
  }
}
