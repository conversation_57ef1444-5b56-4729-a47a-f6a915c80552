/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.eth;

import com.adva.nlms.common.config.EquipmentState;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.CtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.OperationalState;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.State;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.opticalparameters.api.Values;
import com.adva.nlms.resource.mediator.f8.eth.PrepareDeprovisionEvents;
import com.adva.nlms.resource.mediator.f8.eth.RrmEthCardCapabilities;
import com.adva.nlms.resource.mediator.f8.eth.RrmEthDbResourcesFacade;
import com.adva.nlms.resource.mediator.f8.eth.api.in.EthSegment;
import com.adva.nlms.resource.mediator.f8.eth.api.in.EthSegmentRepository;
import com.adva.nlms.resource.mediator.f8.eth.api.in.SegmentID;
import com.adva.nlms.resource.mediator.f8.events.CtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.mediator.f8.events.PtpAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.PtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncCreatedEvent;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.IntStream;

@ExtendWith(MockitoExtension.class)
class PrepareDeprovisionEventsTest {
  private static final int NE_ID = 1;
  private static final SegmentID SEGMENT_ID = new SegmentID(NE_ID, "someSegment");
  private static final String MODULE_AID = "Module-1/1";
  private static final String MODULE_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card";
  private static final String PLUG_AID = "Plug-1/1/p3";
  private static final String PLUG_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/eqh/plgh,3/eq/plg";
  private static final String PLUG_TYPE = "QSFP56-DD-425G-DR4-SM-MPO";
  private static final String PORT_AID_PREFIX = "Port-2/1/p3";
  private static final String PTP_P3_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,3";
  private static final String PTP_P3_1_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,3-1";
  private static final String PTP_P3_2_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,3-2";
  private static final String CTP_P3_ET400_URI = PTP_P3_URI + "/ctp/et400";
  private static final String CTP_P3_1_ET100_URI = PTP_P3_1_URI + "/ctp/et100";
  private static final String CTP_P3_2_ET100_URI = PTP_P3_2_URI + "/ctp/et100";
  private static final String CTP_P2_ET400ozrp_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,2/ctp/et400zr";
  private static final String CTP_P2_ETC400g_URI = CTP_P2_ET400ozrp_URI + "/ctp/etc400g";
  private static final String CTP_P2_ETC100g_1_URI = CTP_P2_ET400ozrp_URI + "/ctp/etc100g-1";
  private static final String SNC_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/sn/etc400g/snc/1";
  private static final State ENTITY_STATE = new State(EquipmentState.IS, OperationalState.OUTAGE, List.of());

  @InjectMocks
  private PrepareDeprovisionEvents prepareDeprovisionEvents;

  @Mock
  private EthSegmentRepository ethSegmentRepository;

  @Mock
  private RrmEthDbResourcesFacade rrmEthDbResourcesFacade;

  @Mock
  private RrmEthCardCapabilities rrmEthCardCapabilities;

  void init(EthSegment segment, Aid plugAid, String ctpUri, String ptpUri, String portAid) {
    Mockito.when(ethSegmentRepository.findOne(SEGMENT_ID))
      .thenReturn(Optional.of(segment));
    Mockito.when(rrmEthDbResourcesFacade.findPlugFromCtp(NE_ID, new Uri(ctpUri)))
      .thenReturn(Optional.of(PlugRef.builder()
        .setNeId(NE_ID)
        .setAid(plugAid)
        .setUri(PLUG_URI)
        .setType(PLUG_TYPE)
        .build())
      );
    Mockito.when(rrmEthDbResourcesFacade.findPortFromCtp(NE_ID, new Uri(ctpUri)))
      .thenReturn(Optional.of(new PtpRef(NE_ID, new Aid(portAid), new Uri(PTP_P3_URI), ENTITY_STATE)));
    Mockito.when(rrmEthDbResourcesFacade.findCardFromPlug(NE_ID, plugAid)).thenReturn(Optional.of(new CardRef(NE_ID,
      new Aid(MODULE_AID), new Uri(MODULE_URI), "MF-M6MDT", 2)));
    Mockito.when(rrmEthCardCapabilities.getPortSetDescriptors(Mockito.anyInt(), Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
      .thenReturn(List.of(
        new Values.PortPoolDescriptor.PortSetDescriptor(List.of("P3")),
        new Values.PortPoolDescriptor.PortSetDescriptor(List.of("P3-1", "P3-2", "P3-3", "P3-4"))
      ));
    Mockito.when(rrmEthDbResourcesFacade.findPtp(NE_ID, new Aid(portAid)))
      .thenReturn(Optional.of(new PtpRef(NE_ID, new Aid(portAid), new Uri(ptpUri), ENTITY_STATE)));
  }

  @Test
  void testCreatedExclusiveGroupWithP3port() {
    // Given
    List<ProvisionLocalEvent> provisionLocalEvents = List.of(
        new PtpCreatedEvent(PTP_P3_URI),
        new CtpCreatedEvent(CTP_P3_ET400_URI),
        new PtpAdoptedEvent(PTP_P3_URI),
        new CtpCreatedEvent(CTP_P2_ETC400g_URI),
        new SncCreatedEvent(SNC_URI, Set.of(CTP_P3_ET400_URI), Set.of(CTP_P2_ETC400g_URI))
    );
    EthSegment segment = new EthSegment(SEGMENT_ID, provisionLocalEvents);
    var plugAid = new Aid(PLUG_AID);
    var portAid = PORT_AID_PREFIX;
    init(segment, plugAid, CTP_P3_ET400_URI, PTP_P3_URI, portAid);
    Mockito.when(rrmEthDbResourcesFacade.findAllCtpsFromPtp(NE_ID, new Aid(portAid)))
        .thenReturn(List.of(new CtpRef(NE_ID, new Aid(portAid), new Uri(CTP_P3_ET400_URI), null)));
    // When
    var deprovisionEvents = prepareDeprovisionEvents.prepareEvents(NE_ID, SEGMENT_ID.segmentId());
    // Then
    List<ProvisionLocalEvent> referenceDeprovisionEvents = List.copyOf(provisionLocalEvents);
    Assertions.assertEquals(referenceDeprovisionEvents, deprovisionEvents);
  }

  @Test
  void testCreatedExclusiveGroupWithP3_1port() {
    // Given
    List<ProvisionLocalEvent> provisionLocalEvents = List.of(
        new PtpCreatedEvent(PTP_P3_1_URI),
        new CtpCreatedEvent(CTP_P3_1_ET100_URI),
        new PtpAdoptedEvent(PTP_P3_1_URI),
        new CtpCreatedEvent(CTP_P2_ETC100g_1_URI),
        new SncCreatedEvent(SNC_URI, Set.of(CTP_P3_1_ET100_URI), Set.of(CTP_P2_ETC100g_1_URI))
    );
    EthSegment segment = new EthSegment(SEGMENT_ID, provisionLocalEvents);
    var plugAid = new Aid(PLUG_AID);
    var portAid = PORT_AID_PREFIX + "-1";
    init(segment, plugAid, CTP_P3_1_ET100_URI, PTP_P3_1_URI, portAid);
    Mockito.when(rrmEthDbResourcesFacade.findPtp(NE_ID, new Aid(PORT_AID_PREFIX)))
        .thenReturn(Optional.empty());
    IntStream.range(2, 5).forEach(i ->
      Mockito.when(rrmEthDbResourcesFacade.findPtp(NE_ID, new Aid(PORT_AID_PREFIX + "-" + i)))
          .thenReturn(Optional.of(new PtpRef(NE_ID,
              new Aid(PORT_AID_PREFIX + "-" + i),
              new Uri(PTP_P3_URI + "-" + i),
              ENTITY_STATE)))
    );
    Mockito.when(rrmEthDbResourcesFacade.findAllCtpsFromPtp(NE_ID, new Aid(portAid)))
        .thenReturn(List.of(new CtpRef(NE_ID, new Aid(portAid), new Uri(CTP_P3_1_ET100_URI), null)));
    // When
    var deprovisionEvents = prepareDeprovisionEvents.prepareEvents(NE_ID, SEGMENT_ID.segmentId());
    // Then
    List<ProvisionLocalEvent> referenceDeprovisionEvents = List.of(
        new PtpCreatedEvent(PTP_P3_URI + "-" + 1),
        new PtpCreatedEvent(PTP_P3_URI + "-" + 2),
        new PtpCreatedEvent(PTP_P3_URI + "-" + 3),
        new PtpCreatedEvent(PTP_P3_URI + "-" + 4),
        new CtpCreatedEvent(CTP_P3_1_ET100_URI),
        new PtpAdoptedEvent(PTP_P3_1_URI),
        new CtpCreatedEvent(CTP_P2_ETC100g_1_URI),
        new SncCreatedEvent(SNC_URI, Set.of(CTP_P3_1_ET100_URI), Set.of(CTP_P2_ETC100g_1_URI))
    );
    Assertions.assertEquals(referenceDeprovisionEvents, deprovisionEvents);
  }

  @Test
  void testCreatedExclusiveGroupWithP3_1portOtherPortOccupied() {
    // Given
    List<ProvisionLocalEvent> provisionLocalEvents = List.of(
        new PtpCreatedEvent(PTP_P3_1_URI),
        new CtpCreatedEvent(CTP_P3_1_ET100_URI),
        new PtpAdoptedEvent(PTP_P3_1_URI),
        new CtpCreatedEvent(CTP_P2_ETC100g_1_URI),
        new SncCreatedEvent(SNC_URI, Set.of(CTP_P3_1_ET100_URI), Set.of(CTP_P2_ETC100g_1_URI))
    );
    EthSegment segment = new EthSegment(SEGMENT_ID, provisionLocalEvents);
    var plugAid = new Aid(PLUG_AID);
    var portAid = PORT_AID_PREFIX + "-1";
    init(segment, plugAid, CTP_P3_1_ET100_URI, PTP_P3_1_URI, portAid);
    Mockito.when(rrmEthDbResourcesFacade.findPtp(NE_ID, new Aid(PORT_AID_PREFIX)))
        .thenReturn(Optional.empty());
    IntStream.range(2, 5).forEach(i ->
      Mockito.when(rrmEthDbResourcesFacade.findPtp(NE_ID, new Aid(PORT_AID_PREFIX + "-" + i)))
          .thenReturn(Optional.of(new PtpRef(NE_ID,
              new Aid(PORT_AID_PREFIX + "-" + i),
              new Uri(PTP_P3_URI + "-" + i),
              ENTITY_STATE)))
    );
    Mockito.when(rrmEthDbResourcesFacade.findAllCtpsFromPtp(NE_ID, new Aid(portAid)))
        .thenReturn(List.of(new CtpRef(NE_ID, new Aid(portAid), new Uri(CTP_P3_1_ET100_URI), null),
            new CtpRef(NE_ID, new Aid(PORT_AID_PREFIX + "-2"), new Uri(CTP_P3_2_ET100_URI), null)));
    // When
    var deprovisionEvents = prepareDeprovisionEvents.prepareEvents(NE_ID, SEGMENT_ID.segmentId());
    // Then
    List<ProvisionLocalEvent> referenceDeprovisionEvents = List.of(
        new CtpCreatedEvent(CTP_P3_1_ET100_URI),
        new PtpAdoptedEvent(PTP_P3_1_URI),
        new CtpCreatedEvent(CTP_P2_ETC100g_1_URI),
        new SncCreatedEvent(SNC_URI, Set.of(CTP_P3_1_ET100_URI), Set.of(CTP_P2_ETC100g_1_URI))
    );
    Assertions.assertEquals(referenceDeprovisionEvents, deprovisionEvents);
  }
}
