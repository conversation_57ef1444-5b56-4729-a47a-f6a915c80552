/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON> E<PERSON>
 */

package com.adva.nlms.resource.mediator.f8.eth;

import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.OpticalParameters;
import com.adva.nlms.opticalparameters.api.Value;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.opticalparameters.api.enums.ParameterName;
import com.adva.nlms.opticalparameters.cim.api.CimValue;
import com.adva.nlms.opticalparameters.cim.api.OpticalParametersCim;
import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;
import com.adva.nlms.resource.mediator.f8.ProtectionGroupFinder;
import com.adva.nlms.resource.provision.f8.api.in.MoCapabilityInquiryParams;
import com.adva.nlms.resource.provision.f8.api.in.MoCimParameter;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionRequestParameters;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;


class PrepareProvisionRequest {

  private final Logger log = LogManager.getLogger(PrepareProvisionRequest.class);

  private final List<Request> provisionRequests;
  private final RrmEthDbResourcesFacade rrmEthDbResourcesFacade;
  private final EthCimProvisionRequestParametersCreatorImpl ethCimProvisionRequestParametersCreator;
  private final Provision provisionApi;
  private final RrmEthCardCapabilities cardCapabilities;
  private final ProtectionGroupFinder protectionGroupFinder;

  public PrepareProvisionRequest(RrmEthDbResourcesFacade rrmEthDbResourcesFacade,
                                 EthCimProvisionRequestParametersCreatorImpl ethCimProvisionRequestParametersCreator,
                                 Provision provisionApi, RrmEthCardCapabilities cardCapabilities, ProtectionGroupFinder protectionGroupFinder) {
    this.provisionRequests = new ArrayList<>();
    this.rrmEthDbResourcesFacade = rrmEthDbResourcesFacade;
    this.ethCimProvisionRequestParametersCreator = ethCimProvisionRequestParametersCreator;
    this.provisionApi = provisionApi;
    this.cardCapabilities = cardCapabilities;
    this.protectionGroupFinder = protectionGroupFinder;
  }

  public List<Request> prepareRequest(int neId, CrmSegmentRequestDto request) {
    var source = request.srcTp;
    var destination = request.dstTp;
    if (isTtpEndpoint(request.dstTp)) {
      source = request.dstTp;
      destination = request.srcTp;
    }
    List<OpticalParameters.SelectedParameter> portParams = request.portParams;
    prepareCrossConnect(neId, source, destination, request.getEthLabel(), portParams);

    if (CrmSegmentRequestDto.RequestType.CREATE.equals(request.getRequestType())) {
      prepareEpteRequests(neId);
    }

    return provisionRequests;
  }

  private void prepareCrossConnect(int neId, String srcAid, String dstAid, EthLabel ethLabel,
                                   List<OpticalParameters.SelectedParameter> portParams) {
    LayerQualifier signalType = getSignalTypeFromPortParams(portParams);
    var cardRef = rrmEthDbResourcesFacade.findCardFromCtp(neId, new Aid(dstAid))
      .orElseThrow(() -> new EthProvisioningException(String.format("Module not found for CTP %s", dstAid)));

    var srcUri = prepareTtpSide(neId, srcAid, signalType, portParams, cardRef);
    var dstUri = prepareLtpSide(neId, dstAid, signalType, ethLabel, cardRef);
    var sncUri = generateSncUri(signalType, cardRef);
    var adoptUri = rrmEthDbResourcesFacade.findSncUriByUriPrefixAndEndpoints(neId, sncUri, Set.of(srcUri), Set.of(dstUri));
    addToProvisionRequestsList(new SncRequest(
      neId,
      adoptUri.map(Uri::uri).orElse(sncUri),
      adoptUri.isPresent(),
      srcUri,
      dstUri,
      provisionApi)
    );
  }

  private void addToProvisionRequestsList(Request request) {
    if (!provisionRequests.contains(request)) {
      provisionRequests.add(request);
    } else {
      log.info("{} already exists on provision requests list", request);
    }
  }

  private String prepareLtpSide(int neId, String terminationPoint, LayerQualifier signalType,
                                EthLabel ethLabel, CardRef cardRef) {
    List<String> layers = new ArrayList<>();

    var plugRef = rrmEthDbResourcesFacade.findPlugFromCtp(neId, new Aid(terminationPoint))
      .orElseThrow(() -> new EthProvisioningException(String.format("Plug not found for CTP %s", terminationPoint)));

    var parentCtpRef = rrmEthDbResourcesFacade.findCtpExtended(neId, new Aid(terminationPoint))
      .orElseThrow(() -> new EthProvisioningException(String.format("CTP %s not found in DB", terminationPoint)));
    var parentLayer = OpticalParametersCim.getLayerQualifierFromLayerList(parentCtpRef.layerRate(), parentCtpRef.aid().aid());
    parentLayer.ifPresent(layer -> layers.add(layer.toString()));

    layers.add(signalType.toString());

    var ctpParams = generateLtpCtpParams(neId, terminationPoint, cardRef, plugRef, layers);
    var ctpUri = ctpParams.cimPath();

    var params = new HashSet<>(ctpParams.cimParameters());

    if (ethLabel instanceof Container container) {
      // CTP in ETH layer is "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,2/ctp/et400ozrp/etc100g-{ID}"
      // Where ID is either found (adopt scenario), ID matching tributary port, or first free selected
      var tp = container.id().get(0);
      List<Long> ts = container.slots().stream()
        .map(Integer::longValue)
        .toList();

      var ctpUriPrefix = ctpUri.replace("{id}", "");
      var foundUri = rrmEthDbResourcesFacade.findCtpUriByUriPrefixAndParams(neId, ctpUriPrefix, tp, ts);
      ctpUri = foundUri.map(Uri::uri)
        .orElseGet(() -> {
          // First we need to check if we can provision CTP with id matching tributary port
          var id = provisionApi.getAvailableCtpEntity(NetworkElementID.create(neId), ctpUriPrefix + tp);
          if (id.isPresent()) {
          return ctpUriPrefix + tp;
          }
          // If we cannot provision CTP with id matching tributary port, we will try to find first free CTP
          id = provisionApi.getAvailableCtpEntity(NetworkElementID.create(neId), ctpUriPrefix);
          if (id.isEmpty()) {
            throw new EthProvisioningException(String.format("No more available CTPs under %s", terminationPoint));
          }
          return ctpUriPrefix + id.get();
      });

      params.add(new MoCimParameter("flexontu", "tp", new CimValue.Int(tp)));
      params.add(new MoCimParameter("flexontu", "ts", new CimValue.IntList(ts)));
    }

    var ctpExists = rrmEthDbResourcesFacade.findCtp(neId, new Uri(ctpUri)).isPresent();

    addToProvisionRequestsList(new CtpRequest(neId, ctpUri, ctpExists, params, provisionApi));
    return ctpUri;
  }

  private String prepareTtpSide(int neId, String portAid, LayerQualifier signalType,
                                final List<OpticalParameters.SelectedParameter> opticalParams, CardRef cardRef) {
    Aid plugAid = new Aid(convertToPlugId(portAid));
    PlugRef plugRef = rrmEthDbResourcesFacade.findPlug(neId, plugAid);
    MoCapabilityInquiryParams inMoParams = MoCapabilityInquiryParams.builder()
      .moduleType(cardRef.type())
      .plugType(plugRef.type())
      .portAid(portAid)
      .layerQualifiers(signalType.toString())
      .build();
    var params = ethCimProvisionRequestParametersCreator.prepareProvisionRequestParameters(
      NetworkElementID.create(neId),
      cardRef.uri().uri(),
      inMoParams,
      opticalParams
    );

    var ptp = params.stream()
      .filter(item -> item.cimClass().equals("ptp"))
      .findFirst();

    var isPtpCreated = rrmEthDbResourcesFacade.findPtp(neId, new Aid(portAid)).isPresent();

    if (!isPtpCreated) {
      ptp.map(item -> new PtpRequest(neId, item.cimPath(), false, provisionApi))
        .ifPresent(this::addToProvisionRequestsList);
    }

    var ctp = params.stream()
      .filter(item -> item.cimClass().equals("ctp"))
      .findFirst().orElseThrow(() -> new EthProvisioningException(String.format("Cannot get CTP for %s",
        portAid)));

    var isCtpCreated = rrmEthDbResourcesFacade.findCtp(neId, new Uri(ctp.cimPath())).isPresent();

    addToProvisionRequestsList(new CtpRequest(neId,
      ctp.cimPath(),
      isCtpCreated,
      new HashSet<>(ctp.cimParameters()),
      provisionApi));

    ptp.map(item ->
        new PtpParamsRequest(neId, item.cimPath(), isPtpCreated, new HashSet<>(item.cimParameters()), provisionApi))
      .ifPresent(this::addToProvisionRequestsList);

    return ctp.cimPath();
  }

  private ProvisionRequestParameters generateLtpCtpParams(int neId, String terminationPoint, CardRef cardRef,
    PlugRef plugRef, List<String> layers) {
    MoCapabilityInquiryParams moCapabilityInquiryParams = MoCapabilityInquiryParams.builder()
      .moduleType(cardRef.type())
      .plugType(plugRef.type())
      .portAid(terminationPoint.substring(0, terminationPoint.lastIndexOf("/")))
      .layerQualifiers(layers.toArray(new String[0]))
      .build();

    var provisionRequestParameters = ethCimProvisionRequestParametersCreator.prepareProvisionRequestParameters(
      NetworkElementID.create(neId),
      cardRef.uri().uri(),
      moCapabilityInquiryParams,
      new ArrayList<>());

    return provisionRequestParameters.stream()
      .filter(item ->
        item.cimClass().equals("ctp"))
      .findFirst()
      .orElseThrow(() ->
        new EthProvisioningException(String.format("Cannot get CTP for %s", terminationPoint))
      );
  }

  private static LayerQualifier getSignalTypeFromPortParams(List<OpticalParameters.SelectedParameter> portParams) {
    return portParams.stream()
      .filter(param -> param.name().equals(ParameterName.GLQ))
      .findFirst()
      .map(p -> ((Value.Enum) p.value()).value())
      .map(LayerQualifier::fromString)
      .orElse(Optional.of(LayerQualifier.NONE))
      .orElse(LayerQualifier.NONE);
  }

  private static String convertToPlugId(final String port) {
    var first = port.indexOf('-');
    var last = port.lastIndexOf("-");
    if (first == last) {
      return "Plug" + port.substring(port.indexOf('-'));
    } else {
      return "Plug" + port.substring(port.indexOf('-'), last);
    }
  }

  private static String generateSncUri(LayerQualifier signalType, CardRef cardRef) {
    return cardRef.uri().uri() +
      "/sn/" +
      convertLayerQualifierToCimString(signalType) +
      "/snc";
  }

  private static String convertLayerQualifierToCimString(LayerQualifier layerQualifier) {
    return OpticalParametersCim.getCimLayerFromLayerQualifier(layerQualifier).orElseThrow(() ->
      new EthProvisioningException("LayerQualifier %s is not supported".formatted(layerQualifier)));
  }

  private void prepareEpteRequests(int neId) {
    var epteRequests = provisionRequests.stream()
      .filter(CtpRequest.class::isInstance)
      .map(CtpRequest.class::cast)
      .map(CtpRequest::getUri)
      .filter(ctpUri -> isEpteSupportedOnCard(neId, ctpUri))
      .map(ctpUri -> {
        var protectionGroup = protectionGroupFinder.findByRelatedSlcZEndpointCtp(neId, ctpUri);
        return protectionGroup
          .map(protectionGroupRef -> new EpteRequest(neId, ctpUri, protectionGroupRef.uri().uri(), provisionApi))
          .orElse(null);
      })
      .filter(Objects::nonNull)
      .toList();
    epteRequests.forEach(this::addToProvisionRequestsList);
  }

  private boolean isEpteSupportedOnCard(int neId, String ctpUri) {
    return rrmEthDbResourcesFacade.findCardFromCtp(neId, new Uri(ctpUri))
      .filter(card -> cardCapabilities.isEpteSupportedOnCard(neId, card.type(), card.mode()))
      .isPresent();
  }

  private static boolean isTtpEndpoint(String aid) {
    return aid.startsWith("Port");
  }

}
