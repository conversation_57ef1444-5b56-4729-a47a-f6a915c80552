/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.provider.api.CapabilityProvider;
import com.adva.infrastructure.capabilityprovider.capabilities.CapabilityConfiguration;
import com.adva.infrastructure.capabilityprovider.core.CapabilityProviderConfiguration;
import com.adva.infrastructure.capabilityprovider.mo.MoCapabilityProviderConfiguration;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.resource.mediator.f8.AssignRoles;
import com.adva.nlms.resource.mediator.f8.ProtectionSettings;
import com.adva.nlms.resource.mediator.f8.events.CtpAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.CtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.mediator.f8.events.SncCreatedEvent;
import com.adva.nlms.resource.mediator.f8.internalpath.TerminationPoint;
import com.adva.nlms.resource.mediator.f8.otn.CccProtectionGroupRequest;
import com.adva.nlms.resource.mediator.f8.otn.PrepareProtectionGroupRequest;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnCardCapabilities;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnCardCapabilitiesImpl;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnDbResourcesFacade;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegment;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegmentRepository;
import com.adva.nlms.resource.mediator.f8.otn.api.in.ProtectionData;
import com.adva.nlms.resource.mediator.f8.otn.api.in.SegmentID;
import com.adva.nlms.resource.provision.f8.api.in.CccpCreateDto;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.mockito.Mockito.mock;

class PrepareCccProtectionGroupOf1200Test {
  private final int neId = 42;
  private final Provision provisionApi = Mockito.mock(Provision.class);
  private final OtnSegmentRepository otnSegmentRepository = Mockito.mock(OtnSegmentRepository.class);
  private final RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade = Mockito.mock(RrmOtnDbResourcesFacade.class);
  private final String workingLegId = "workingLegId";
  private final String protectionLegId = "protectionLegId";
  private final ProtectionSettings protectionSettings = new ProtectionSettings(true, "bidir", 5, 0);
  private final AssignRoles assignRoles = new AssignRoles(workingLegId, protectionLegId, protectionSettings);
  private final NEDataProvider neDataProvider = mock(NEDataProvider.class);
  private static final CapabilityProvider capabilityProvider = new CapabilityProviderConfiguration()
    .capabilityProvider(new CapabilityConfiguration().capabilityRepository(), null, null);
  private static final MoCapabilityProvider moCapabilityProvider = new MoCapabilityProviderConfiguration().moCapabilityProvider();
  private final RrmOtnCardCapabilities rrmOtnCardCapabilities = new RrmOtnCardCapabilitiesImpl(neDataProvider, capabilityProvider, moCapabilityProvider);

  // working segment
  private final String clientOTUUri1 = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-3/ctp/otu2";
  private final String clientPtpUri1 = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-3";
  private final String clientODUUri1 = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-3/ctp/otu2/ctp/odu2";
  private final String networkUri1 = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/nw,1/ctp/otuc3/ctp/oduc3/ctp/odu2-1";
  private final String sncUri1 = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/sn/odu2/snc/1";
  private final SegmentID workingSegmentId = new SegmentID(neId, workingLegId);
  private final String clientAid1 = "Port-2/1";
  private final TerminationPoint clientProtection1 = new TerminationPoint(clientAid1, null, false);

  // protection segment
  private final String clientOTUUri2 = "/mit/me/1/eqh/shelf,2/eqh/slot,3/eq/card/ptp/cl,2-3/ctp/otu2";
  private final String clientPtpUri2 = "/mit/me/1/eqh/shelf,2/eqh/slot,3/eq/card/ptp/cl,2-3";
  private final String clientODUUri2 = "/mit/me/1/eqh/shelf,2/eqh/slot,3/eq/card/ptp/cl,2-3/ctp/otu2/ctp/odu2";
  private final String networkUri2 = "/mit/me/1/eqh/shelf,2/eqh/slot,3/eq/card/ptp/nw,1/ctp/otuc3/ctp/oduc3/ctp/odu2-1";
  private final String sncUri2 = "/mit/me/1/eqh/shelf,2/eqh/slot,3/eq/card/sn/odu2/snc/1";
  private final SegmentID protectionSegmentId = new SegmentID(neId, protectionLegId);
  private final String clientAid2 = "Port-2/3";
  private final TerminationPoint clientProtection2 = new TerminationPoint(clientAid2, null, false);

  private final PrepareProtectionGroupRequest sut = new PrepareProtectionGroupRequest(neId, assignRoles, provisionApi, otnSegmentRepository, rrmOtnDbResourcesFacade, rrmOtnCardCapabilities);

  @Test
  void testCreate() {
    List<ProvisionLocalEvent> workingEvents = List.of(
      new CtpCreatedEvent(clientOTUUri1),
      new CtpCreatedEvent(clientODUUri1),
      new CtpCreatedEvent(networkUri1),
      new SncCreatedEvent(sncUri1, Set.of(clientODUUri1), Set.of(networkUri1))
    );
    CccpCreateDto cccpCreateDto = new CccpCreateDto(neId, "", clientOTUUri1, clientOTUUri2, null, 0, 5, true, null, "bidir");

    OtnSegment working = new OtnSegment(workingSegmentId, workingEvents, new ProtectionData(clientProtection1));
    List<ProvisionLocalEvent> protectionEvents = List.of(
      new CtpCreatedEvent(clientOTUUri2),
      new CtpCreatedEvent(clientODUUri2),
      new CtpCreatedEvent(networkUri2),
      new SncCreatedEvent(sncUri2, Set.of(clientODUUri2), Set.of(networkUri2))
    );
    OtnSegment protection = new OtnSegment(protectionSegmentId, protectionEvents, new ProtectionData(clientProtection2));

    Mockito.when(otnSegmentRepository.findOne(workingSegmentId)).thenReturn(working);
    Mockito.when(otnSegmentRepository.findOne(protectionSegmentId)).thenReturn(protection);
    Mockito.when(rrmOtnDbResourcesFacade.findPtp(neId, new Aid(clientAid1))).thenReturn(Optional.of(new PtpRef(neId, new Aid(clientAid1), new Uri(clientPtpUri1), null)));
    Mockito.when(rrmOtnDbResourcesFacade.findPtp(neId, new Aid(clientAid2))).thenReturn(Optional.of(new PtpRef(neId, new Aid(clientAid2), new Uri(clientPtpUri2), null)));

    CccProtectionGroupRequest cccProtectionGroupRequest = new CccProtectionGroupRequest(neId, cccpCreateDto, provisionApi);
    Assertions.assertEquals(cccProtectionGroupRequest, sut.prepareRequest());
  }

  @Test
  void testAdopt() {
    List<ProvisionLocalEvent> workingEvents = List.of(
      new CtpCreatedEvent(clientOTUUri1),
      new CtpCreatedEvent(clientODUUri1),
      new CtpCreatedEvent(networkUri1),
      new SncCreatedEvent(sncUri1, Set.of(clientODUUri1), Set.of(networkUri1))
    );
    OtnSegment working = new OtnSegment(workingSegmentId, workingEvents, new ProtectionData(clientProtection1));
    List<ProvisionLocalEvent> protectionEvents = List.of(
      new CtpAdoptedEvent(clientOTUUri2),
      new CtpAdoptedEvent(clientODUUri2),
      new CtpCreatedEvent(networkUri2),
      new SncCreatedEvent(sncUri2, Set.of(clientODUUri2), Set.of(networkUri2))
    );
    OtnSegment protection = new OtnSegment(protectionSegmentId, protectionEvents, new ProtectionData(clientProtection2));

    Mockito.when(otnSegmentRepository.findOne(workingSegmentId)).thenReturn(working);
    Mockito.when(otnSegmentRepository.findOne(protectionSegmentId)).thenReturn(protection);
    Mockito.when(rrmOtnDbResourcesFacade.findPtp(neId, new Aid(clientAid1))).thenReturn(Optional.of(new PtpRef(neId, new Aid(clientAid1), new Uri(clientPtpUri1), null)));
    Mockito.when(rrmOtnDbResourcesFacade.findPtp(neId, new Aid(clientAid2))).thenReturn(Optional.of(new PtpRef(neId, new Aid(clientAid2), new Uri(clientPtpUri2), null)));
    Mockito.when(rrmOtnDbResourcesFacade.findCccpGroupName(neId, new Uri(clientOTUUri2))).thenReturn(Optional.of("cccp-1"));

    CccProtectionGroupRequest cccProtectionGroupRequest = new CccProtectionGroupRequest(neId, "cccp-1", provisionApi);
    Assertions.assertEquals(cccProtectionGroupRequest, sut.prepareRequest());
  }

}
