/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.common.config.EquipmentState;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.FiberRef;
import com.adva.nlms.mediation.mo.inventory.resources.FiberResources;
import com.adva.nlms.mediation.mo.inventory.resources.OperationalState;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.State;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.resource.mediator.f8.wdm.FiberTracer;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class FiberTracerTest {
  private final static int NE_ID = 23;
  private final static Aid aEndAid = new Aid("Port-3/3/c1c6/c5");
  private final static Uri aEndUri = new Uri("/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/cl,1-6/ptp/cl,5");
  private final static Aid zEndAid = new Aid("External-3/alien/4");
  private final static Uri zEndUri = new Uri("/mit/me/1/eqh/shelf,3/eqh/slot,alien/eq/card/ptp/alien,4");
  private final static String linkName = "3/3/c1c6/c5:3/alien/4";
  private final FiberResources fiberResources = mock(FiberResources.class);
  private final PtpResources ptpResources = mock(PtpResources.class);
  private final FiberTracer fiberTracer = new FiberTracer(fiberResources, ptpResources);
  private final State IS = new State(EquipmentState.IS, OperationalState.OUTAGE, List.of());

  @Test
  void getOtherEndOfAlienFiber_NoFiberFound() {
    when(ptpResources.findPtp(NE_ID, aEndAid))
      .thenReturn(Optional.of(new PtpRef(NE_ID, aEndAid, aEndUri, IS)));
    when(ptpResources.findPtp(NE_ID, zEndUri))
      .thenReturn(Optional.of(new PtpRef(NE_ID, zEndAid, zEndUri, IS)));
    var result = fiberTracer.findOtherEndOfAlienFiber(NE_ID, aEndAid.aid());
    assertTrue(result.isEmpty());
  }

  @Test
  void getOtherEndOfAlienFiber_NoAlienFiberFound() {
    when(ptpResources.findPtp(NE_ID, aEndAid))
      .thenReturn(Optional.of(new PtpRef(NE_ID, aEndAid, aEndUri, IS)));
    when(ptpResources.findPtp(NE_ID, zEndUri))
      .thenReturn(Optional.of(new PtpRef(NE_ID, zEndAid, zEndUri, IS)));
    when(fiberResources.findFibers(NE_ID))
      .thenReturn(List.of(new FiberRef(NE_ID, "not-alien", "bi", aEndUri, zEndUri)));
    var result = fiberTracer.findOtherEndOfAlienFiber(NE_ID, aEndAid.aid());
    assertTrue(result.isEmpty());
  }

  @Test
  void getOtherEndOfAlienFiber_InvalidAid() {
    when(ptpResources.findPtp(NE_ID, aEndAid))
      .thenReturn(Optional.of(new PtpRef(NE_ID, aEndAid, aEndUri, IS)));
    when(ptpResources.findPtp(NE_ID, zEndUri))
      .thenReturn(Optional.of(new PtpRef(NE_ID, new Aid("INVALID"), zEndUri, IS)));
    when(fiberResources.findFibers(NE_ID))
      .thenReturn(List.of(new FiberRef(NE_ID, linkName, "bi", aEndUri, zEndUri)));
    var result = fiberTracer.findOtherEndOfAlienFiber(NE_ID, aEndAid.aid());
    assertTrue(result.isPresent());
    assertTrue(result.get().isInvalid());
  }

  @Test
  void getOtherEndOfAlienFiber_InvalidAidShelf() {
    when(ptpResources.findPtp(NE_ID, aEndAid))
      .thenReturn(Optional.of(new PtpRef(NE_ID, aEndAid, aEndUri, IS)));
    when(ptpResources.findPtp(NE_ID, zEndUri))
      .thenReturn(Optional.of(new PtpRef(NE_ID, new Aid("External-Ext3/alien/4"), zEndUri, IS)));
    when(fiberResources.findFibers(NE_ID))
      .thenReturn(List.of(new FiberRef(NE_ID, linkName, "bi", aEndUri, zEndUri)));
    var result = fiberTracer.findOtherEndOfAlienFiber(NE_ID, aEndAid.aid());
    assertTrue(result.isPresent());
    assertTrue(result.get().isInvalid());
  }

  @Test
  void getOtherEndOfAlienFiber_HappyPath() {
    when(ptpResources.findPtp(NE_ID, aEndAid))
      .thenReturn(Optional.of(new PtpRef(NE_ID, aEndAid, aEndUri, IS)));
    when(ptpResources.findPtp(NE_ID, zEndUri))
      .thenReturn(Optional.of(new PtpRef(NE_ID, zEndAid, zEndUri, IS)));
    when(fiberResources.findFibers(NE_ID))
      .thenReturn(List.of(new FiberRef(NE_ID, linkName, "bi", aEndUri, zEndUri)));
    var result = fiberTracer.findOtherEndOfAlienFiber(NE_ID, aEndAid.aid());
    assertTrue(result.isPresent());
    assertEquals(3, result.get().shelfNo());
    assertEquals(4, result.get().alienPtpNumber());
    assertEquals(zEndAid.aid(), result.get().aidString());
    assertFalse(result.get().isFiberAEnd());
  }

  @Test
  void getOtherEndOfAlienFiber_HappyPathReverseFiber() {
    when(ptpResources.findPtp(NE_ID, aEndAid))
      .thenReturn(Optional.of(new PtpRef(NE_ID, aEndAid, aEndUri, IS)));
    when(ptpResources.findPtp(NE_ID, zEndUri))
      .thenReturn(Optional.of(new PtpRef(NE_ID, zEndAid, zEndUri, IS)));
    when(fiberResources.findFibers(NE_ID))
      .thenReturn(List.of(new FiberRef(NE_ID, linkName, "bi", zEndUri, aEndUri)));
    var result = fiberTracer.findOtherEndOfAlienFiber(NE_ID, aEndAid.aid());
    assertTrue(result.isPresent());
    assertEquals(3, result.get().shelfNo());
    assertEquals(4, result.get().alienPtpNumber());
    assertEquals(zEndAid.aid(), result.get().aidString());
    assertTrue(result.get().isFiberAEnd());
  }
}