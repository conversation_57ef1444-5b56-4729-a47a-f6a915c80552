/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.driver.bean.resource.mediator.f8;

import com.adva.nlms.driver.api.in.registry.AbstractDriverFeatures;
import com.adva.nlms.driver.api.in.registry.DriverFeaturesDDI;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.resource.mediator.api.in.ResourceRequestMediator;
import ni.msg.EnvelopeOuterClass;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@Component
class UnoResourceRequestMediatorFeatures extends AbstractDriverFeatures implements DriverFeaturesDDI {
  protected static final Integer VERSION = 1;
  protected static final String DRIVER_ID = "UNMANAGED_RESOURCE_REQUEST_MEDIATOR_DRIVER";
  protected static final String QUALIFIER = "UNMANAGED_RESOURCE_REQUEST_MEDIATOR"; // qualifier is used in delegator

  @Override
  public Integer getVersion() {
    return VERSION;
  }

  @Override
  public String getID() {
    return DRIVER_ID;
  }

  @Override
  protected void initialize() {

    addFeature(
      ResourceRequestMediator.class,
      QUALIFIER,
      () -> new ResourceRequestMediator() {
        @Override
        public void removeNetworkElement(int neId) {
          // no implementation required for Unmanaged nodes
        }

        @Override
        public void handleSegmentRequestInTask(EnvelopeOuterClass.Envelope envelope) {
          // no implementation required for Unmanaged nodes
        }

        @Override
        public void handleProtectionSwitch(UUID neId, String protectionGroupEntityIndex) {
          // no implementation required for Unmanaged nodes
        }

        @Override
        public void handleAdminStateOperation(UUID neId, List<String> aids, String protectionGroupIndex, AdminState adminState, LayerQualifier layerProtocolQualifier) {
          // no implementation required for Unmanaged nodes
        }
      }
    );
  }
}
