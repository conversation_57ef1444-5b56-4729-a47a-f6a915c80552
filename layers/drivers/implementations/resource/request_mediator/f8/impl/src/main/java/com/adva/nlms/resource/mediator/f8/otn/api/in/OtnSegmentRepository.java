/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.otn.api.in;

import java.util.Optional;

public interface OtnSegmentRepository {

  void save(OtnSegment segment);

  OtnSegment findOne(SegmentID segmentID);

  Optional<OtnSegment> findAssociatedSegmentByEndpointAndSncUriPrefix(int neId, String endpointUri, String sncUriPrefix);

  void delete(SegmentID segmentID);

  boolean exists(SegmentID segmentID);

  void deleteSegments(int neId);
}
