/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm.api.in;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.mediation.config.f8.croma.slc.api.SlcEqSt;

public interface EqualizeUseCases {
  void processSlcEqualizationDone(int neId, int slcId, SlcEqSt slcEqSt, boolean isSlcAEnd);

  void processSlcEqualizationUnavailable(int neId, int slcId, SlcEqSt slcEqSt, boolean isSlcAEnd);

  void processLaserOnDelayAlarm(int neId, EntityIndex entityIndex, boolean cleared);

  void processAgatesStatusUpdate(AgatesParameters agatesParameters);
}
