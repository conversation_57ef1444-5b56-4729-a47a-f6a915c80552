/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: zbigniewj
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.resource.mediator.f8.otn.LayerQualifierToCimString;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class LayerQualifierToCimStringTest {

  @Test
  void convert() {
    Assertions.assertEquals("oduf", LayerQualifierToCimString.convert(LayerQualifier.ODUFLEX));
    Assertions.assertEquals("odu0", LayerQualifierToCimString.convert(LayerQualifier.ODU0));
    Assertions.assertEquals("odu1", LayerQualifierToCimString.convert(LayerQualifier.ODU1));
    Assertions.assertEquals("odu2", LayerQualifierToCimString.convert(LayerQualifier.ODU2));
    Assertions.assertEquals("odu3", LayerQualifierToCimString.convert(LayerQualifier.ODU3));
    Assertions.assertEquals("odu4", LayerQualifierToCimString.convert(LayerQualifier.ODU4));
    Assertions.assertEquals("odu2e", LayerQualifierToCimString.convert(LayerQualifier.ODU2E));
    Assertions.assertEquals("", LayerQualifierToCimString.convert(LayerQualifier.ODUC2PA));
    Assertions.assertEquals("", LayerQualifierToCimString.convert(LayerQualifier.ODUC3PA));
    Assertions.assertEquals("", LayerQualifierToCimString.convert(LayerQualifier.ODUC4PA));
    Assertions.assertEquals("", LayerQualifierToCimString.convert(LayerQualifier.ODUC5PA));
    Assertions.assertEquals("", LayerQualifierToCimString.convert(LayerQualifier.ODUC6PA));
    Assertions.assertEquals("", LayerQualifierToCimString.convert(LayerQualifier.ODUC7PA));
    Assertions.assertEquals("", LayerQualifierToCimString.convert(LayerQualifier.ODUC8PA));
    Assertions.assertEquals("", LayerQualifierToCimString.convert(LayerQualifier.ODUC9PA));
    Assertions.assertEquals("", LayerQualifierToCimString.convert(LayerQualifier.ODUC10PA));
    Assertions.assertEquals("", LayerQualifierToCimString.convert(LayerQualifier.ODUC11PA));
    Assertions.assertEquals("", LayerQualifierToCimString.convert(LayerQualifier.ODUC12PA));
    Assertions.assertEquals("", LayerQualifierToCimString.convert(LayerQualifier.ODUC2));
    Assertions.assertEquals("", LayerQualifierToCimString.convert(LayerQualifier.ODUC3));
    Assertions.assertEquals("", LayerQualifierToCimString.convert(LayerQualifier.ODUC4));
  }

}
