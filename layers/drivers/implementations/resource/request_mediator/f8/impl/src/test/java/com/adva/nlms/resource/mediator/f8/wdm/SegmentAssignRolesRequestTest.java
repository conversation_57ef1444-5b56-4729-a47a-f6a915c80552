/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.moprovider.api.MoEpteInquiryObject;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.ec.support.EcEntityIndex;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.CardResources;
import com.adva.nlms.mediation.mo.inventory.resources.CrossConnectRef;
import com.adva.nlms.mediation.mo.inventory.resources.CrossConnectResources;
import com.adva.nlms.mediation.mo.inventory.resources.CtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.CtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.LineProtectionGroupRef;
import com.adva.nlms.mediation.mo.inventory.resources.ProtectionGroupRef;
import com.adva.nlms.mediation.mo.inventory.resources.ProtectionGroupResources;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.topology.NEData;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.resource.mediator.f8.AssignRoles;
import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;
import com.adva.nlms.resource.mediator.f8.ProtectionSettings;
import com.adva.nlms.resource.mediator.f8.SegmentRequestFailedException;
import com.adva.nlms.resource.mediator.f8.wdm.SegmentAssignRolesRequest;
import com.adva.nlms.resource.mediator.f8.wdm.SegmentRequestOperationRet;
import com.adva.nlms.resource.mediator.f8.wdm.WdmSegmentRequestConfig;
import com.adva.nlms.resource.mediator.f8.wdm.adapters.persistence.RrmPersistenceConfiguration;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.CrmNodePosition;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.RrmSegmentRepository;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.WdmSegment;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.nlms.resource.provision.f8.api.in.ResilienceDto;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

class SegmentAssignRolesRequestTest {
  private static final int NE_ID = 53;
  private static final String WORKING_SEGMENT_ID = "43";
  private static final String PROTECTION_SEGMENT_ID = "44";
  private static final ProtectionSettings PROTECTION_SETTINGS = new ProtectionSettings(true, "bidir", 5, 0);
  private static final String WORKING_TTP_AID = "Port-1/3";
  private static final String PROTECTING_TTP_AID = "Port-1/7";
  private static final String WORKING_PTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/nw,3-e";
  private static final String PROTECTING_PTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/nw,1-e";
  private static final String TRANSPONDER_CTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,7/eq/card/ptp/nw,1/ctp/ot200";
  private static final String CHILD_1_CTP_OF_TRANSPONDER_CTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,7/eq/card/ptp/nw,1/ctp/ot200/ctp/odu4-1";
  private static final String CHILD_2_CTP_OF_TRANSPONDER_CTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,7/eq/card/ptp/nw,1/ctp/ot200/ctp/odu4-2";

  private final RrmSegmentRepository segmentRepository = new RrmPersistenceConfiguration().rrmSegmentRepository();
  private final CtpResources ctpResources = mock();
  private final PtpResources ptpResources = mock();
  private final CardResources cardResources = mock();
  private final Provision provision = mock();
  private final CrossConnectResources crossConnectResources = mock();
  private final ProtectionGroupResources protectionGroupResources = mock();
  private final MoCapabilityProvider moCapabilityProvider = mock();
  private final NEDataProvider neDataProvider = mock();

  private final SegmentAssignRolesRequest sut = new WdmSegmentRequestConfig().wdmSegmentAssignRolesRequest(
    segmentRepository,
    ctpResources,
    cardResources,
    ptpResources,
    provision,
    crossConnectResources,
    moCapabilityProvider,
    neDataProvider
  );

  @Test
  void isSegmentOperationSupported() {
    //when
    boolean actual = sut.isSegmentOperationSupported(CrmSegmentRequestDto.RequestType.ASSIGN_ROLES);

    //then
    assertThat(actual).isTrue();
  }

  @Test
  void segmentRequestIsNull() {
    //when
    var actual = assertThatThrownBy(() -> sut.execute(null, null));
    //then
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("request cannot be null");
  }

  @Test
  void segmentRequestHasInvalidNeId() {
    //given
    CrmSegmentRequestDto request = new CrmSegmentRequestDto();
    //when
    var actual = assertThatThrownBy(() -> sut.execute(request, null));
    //then
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("Invalid neId. Must be greater than 0");
  }

  @Test
  void segmentRequestHasNullAssignRolesObject() {
    //given
    CrmSegmentRequestDto request = new CrmSegmentRequestDto();
    request.neId = NE_ID;
    //when
    var actual = assertThatThrownBy(() -> sut.execute(request, null));
    //then
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("request.assignRoles cannot be null");
  }

  @Test
  void segmentRequestsAssignRolesHasNullWorkingLeg() {
    //given
    CrmSegmentRequestDto request = new CrmSegmentRequestDto();
    request.neId = NE_ID;
    request.assignRoles = new AssignRoles(null, null, null);
    //when
    var actual = assertThatThrownBy(() -> sut.execute(request, null));
    //then
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("request.assignRoles.workingLegId cannot be blank");
  }


  @Test
  void segmentRequestsAssignRolesHasNullProtectionLeg() {
    //given
    CrmSegmentRequestDto request = new CrmSegmentRequestDto();
    request.neId = NE_ID;
    request.assignRoles = new AssignRoles(WORKING_SEGMENT_ID, null, null);
    //when
    var actual = assertThatThrownBy(() -> sut.execute(request, null));
    //then
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("request.assignRoles.protectionLegId cannot be blank");
  }

  @Test
  void validRequestButWorkingSegmentNotFound() {
    //given
    CrmSegmentRequestDto request = validRequest();

    //when
    var actual = assertThatThrownBy(() -> sut.execute(request, null));
    //then
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("Cannot find segment for neId=%d, segmentId=%s".formatted(NE_ID, WORKING_SEGMENT_ID));
  }

  @Test
  void validRequestButProtectingSegmentNotFound() {
    //given
    CrmSegmentRequestDto request = validRequest();

    WdmSegment workingSegment = WdmSegment.newSegment()
      .withSegmentRequestId(WORKING_SEGMENT_ID)
      .withNeId(NE_ID)
      .build();
    segmentRepository.store(workingSegment);
    //when
    var actual = assertThatThrownBy(() -> sut.execute(request, null));
    //then
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("Cannot find segment for neId=%d, segmentId=%s".formatted(NE_ID, PROTECTION_SEGMENT_ID));
  }

  @Test
  void validRequestAndBothSegmentsExistWithCtpUrisButCrossConnectUriNotFound() {
    //given
    final String workingCtpUri = WORKING_PTP_URI + "/ctp/traffic";
    CrmSegmentRequestDto request = validRequest();

    prepareSegment(WORKING_SEGMENT_ID, WORKING_TTP_AID, CrmNodePosition.INGRESS);
    prepareSegment(PROTECTION_SEGMENT_ID, PROTECTING_TTP_AID, CrmNodePosition.INGRESS);

    PtpRef workingPtpRef = new PtpRef(NE_ID, new Aid(WORKING_TTP_AID), new Uri(WORKING_PTP_URI), null);
    when(ptpResources.findPtp(NE_ID, new Aid(WORKING_TTP_AID))).thenReturn(Optional.of(workingPtpRef));

    PtpRef protectingPtpRef = new PtpRef(NE_ID, new Aid(PROTECTING_TTP_AID), new Uri(PROTECTING_PTP_URI), null);
    when(ptpResources.findPtp(NE_ID, new Aid(PROTECTING_TTP_AID))).thenReturn(Optional.of(protectingPtpRef));

    CardRef oppmCard = new CardRef(NE_ID, null, new Uri(workingCtpUri), null, 0);
    when(ctpResources.findCard(NE_ID, new Uri(workingCtpUri))).thenReturn(Optional.of(oppmCard));

    //when
    var actual = assertThatThrownBy(() -> sut.execute(request, null));
    //then
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("Cannot find cross connect whose z-end uris contains ctpUri=%s".formatted(workingCtpUri));
  }

  @Test
  void executeHappyPath() throws ObjectInUseException {
    //given
    final String workingCtpUri = WORKING_PTP_URI + "/ctp/traffic";
    final String protectionCtpUri = PROTECTING_PTP_URI + "/ctp/traffic";
    CrmSegmentRequestDto request = validRequest();

    prepareSegment(WORKING_SEGMENT_ID, WORKING_TTP_AID, CrmNodePosition.INGRESS);
    prepareSegment(PROTECTION_SEGMENT_ID, PROTECTING_TTP_AID, CrmNodePosition.INGRESS);

    PtpRef workingPtpRef = new PtpRef(NE_ID, new Aid(WORKING_TTP_AID), new Uri(WORKING_PTP_URI), null);
    when(ptpResources.findPtp(NE_ID, new Aid(WORKING_TTP_AID))).thenReturn(Optional.of(workingPtpRef));

    PtpRef protectingPtpRef = new PtpRef(NE_ID, new Aid(PROTECTING_TTP_AID), new Uri(PROTECTING_PTP_URI), null);
    when(ptpResources.findPtp(NE_ID, new Aid(PROTECTING_TTP_AID))).thenReturn(Optional.of(protectingPtpRef));

    CardRef oppmCard = new CardRef(NE_ID, new Aid("cardAid"), new Uri(workingCtpUri), null, 0);
    when(ctpResources.findCard(NE_ID, new Uri(workingCtpUri))).thenReturn(Optional.of(oppmCard));

    String crossConnectUri = "crossConnectUri";
    CrossConnectRef crossConnect = new CrossConnectRef(NE_ID, null, new Uri(crossConnectUri), List.of(), List.of(workingCtpUri), null, null);
    when(cardResources.listCrossConnections(NE_ID, oppmCard.aid())).thenReturn(List.of(crossConnect));

    var protectionGroupUri = "protectionGroupUri";
    var resilienceDto = new ResilienceDto(true, 5, true, 0);
    when(provision.createOppmProtectionGroup(NE_ID, crossConnectUri, workingCtpUri, protectionCtpUri, resilienceDto)).thenReturn(protectionGroupUri);

    //when
    var actual = sut.execute(request, null);
    //then
    assertThat(actual).isNotNull()
      .extracting(SegmentRequestOperationRet::operationCompleted)
      .isEqualTo(true);
    var workingSegment = segmentRepository.getSegment(WORKING_SEGMENT_ID, NE_ID);
    assertThat(workingSegment)
      .isNotNull()
      .extracting(WdmSegment::getProtectionGroupUri)
      .isEqualTo(protectionGroupUri);
    var protectionSegment = segmentRepository.getSegment(PROTECTION_SEGMENT_ID, NE_ID);
    assertThat(protectionSegment)
      .isNotNull()
      .extracting(WdmSegment::getProtectionGroupUri)
      .isEqualTo(protectionGroupUri);

    verify(provision, times(0)).setEpte(eq(NE_ID), any(), anyInt(), any());
  }

  @Test
  void verifySetEpteOnHappyPath() throws ObjectInUseException {
    //given
    final String workingCtpUri = WORKING_PTP_URI + "/ctp/traffic";
    final String protectionCtpUri = PROTECTING_PTP_URI + "/ctp/traffic";
    CrmSegmentRequestDto request = new CrmSegmentRequestDto();
    request.neId = NE_ID;
    request.assignRoles = new AssignRoles(WORKING_SEGMENT_ID, PROTECTION_SEGMENT_ID, new ProtectionSettings(false, "bidir", 5, 0));

    prepareSegment(WORKING_SEGMENT_ID, WORKING_TTP_AID, CrmNodePosition.INGRESS);
    prepareSegment(PROTECTION_SEGMENT_ID, PROTECTING_TTP_AID, CrmNodePosition.INGRESS);

    PtpRef workingPtpRef = new PtpRef(NE_ID, new Aid(WORKING_TTP_AID), new Uri(WORKING_PTP_URI), null);
    when(ptpResources.findPtp(NE_ID, new Aid(WORKING_TTP_AID))).thenReturn(Optional.of(workingPtpRef));

    PtpRef protectingPtpRef = new PtpRef(NE_ID, new Aid(PROTECTING_TTP_AID), new Uri(PROTECTING_PTP_URI), null);
    when(ptpResources.findPtp(NE_ID, new Aid(PROTECTING_TTP_AID))).thenReturn(Optional.of(protectingPtpRef));

    CardRef oppmCard = new CardRef(NE_ID, new Aid("cardAid"), new Uri(workingCtpUri), null, 0);
    when(ctpResources.findCard(NE_ID, new Uri(workingCtpUri))).thenReturn(Optional.of(oppmCard));

    String crossConnectUri = "crossConnectUri";
    CrossConnectRef crossConnect = new CrossConnectRef(NE_ID, null, new Uri(crossConnectUri), List.of(), List.of(workingCtpUri), null, null);
    when(cardResources.listCrossConnections(NE_ID, oppmCard.aid())).thenReturn(List.of(crossConnect));

    var protectionGroupUri = "protectionGroupUri";
    var resilienceDto = new ResilienceDto(true, 5, false, 0);
    when(provision.createOppmProtectionGroup(NE_ID, crossConnectUri, workingCtpUri, protectionCtpUri, resilienceDto)).thenReturn(protectionGroupUri);

    CardRef lineCard = new CardRef(NE_ID, new Aid("cardAid"), new Uri(workingCtpUri), "MA-B2C3LT-A", 0);
    when(ctpResources.findCard(NE_ID, new Uri(TRANSPONDER_CTP_URI))).thenReturn(Optional.of(lineCard));

    when(ctpResources.findChildCtpsByNeIdAndParentEntity(NE_ID, EcEntityIndex.getEcEntityIndex(TRANSPONDER_CTP_URI)))
      .thenReturn(List.of(
        new CtpRef(NE_ID, null, new Uri(CHILD_1_CTP_OF_TRANSPONDER_CTP_URI), null),
        new CtpRef(NE_ID, null, new Uri(CHILD_2_CTP_OF_TRANSPONDER_CTP_URI), null))
      );

    ProtectionGroupRef protectionGroup = new LineProtectionGroupRef(NE_ID, null, new Uri(protectionGroupUri),
      null, new Uri(workingCtpUri), null, new Uri(protectionCtpUri), null,
      new Uri(crossConnectUri), new Uri("epte1"), new Uri(CHILD_1_CTP_OF_TRANSPONDER_CTP_URI), new Uri(""), new Uri(""));

    when(protectionGroupResources.findProtectionGroup(NE_ID, new Uri(protectionGroupUri))).thenReturn(Optional.of(protectionGroup));

    NEData neData = new NEData(NE_ID, 1, "MA-B2C3LT-A", "", "", "5.1.1");
    when(neDataProvider.getNeData(NE_ID)).thenReturn(neData);
    MoEpteInquiryObject epteInquiryObject = new MoEpteInquiryObject(neData.getSwVersion(),lineCard.type(), lineCard.mode() );
    when(moCapabilityProvider.isEpteSupported(epteInquiryObject)).thenReturn(true);
    //when
    var actual = sut.execute(request, null);
    //then
    assertThat(actual).isNotNull()
      .extracting(SegmentRequestOperationRet::operationCompleted)
      .isEqualTo(true);
    var workingSegment = segmentRepository.getSegment(WORKING_SEGMENT_ID, NE_ID);
    assertThat(workingSegment)
      .isNotNull()
      .extracting(WdmSegment::getProtectionGroupUri)
      .isEqualTo(protectionGroupUri);
    var protectionSegment = segmentRepository.getSegment(PROTECTION_SEGMENT_ID, NE_ID);
    assertThat(protectionSegment)
      .isNotNull()
      .extracting(WdmSegment::getProtectionGroupUri)
      .isEqualTo(protectionGroupUri);

    verify(provision, times(1)).setEpte(NE_ID, protectionGroupUri, 1, CHILD_1_CTP_OF_TRANSPONDER_CTP_URI);
    verify(provision, times(1)).setEpte(NE_ID, protectionGroupUri, 2, CHILD_2_CTP_OF_TRANSPONDER_CTP_URI);
  }

  private void prepareSegment(String segmentId, String aid, CrmNodePosition crmNodePosition) {
    WdmSegment.Builder builder = WdmSegment.newSegment()
      .withSegmentRequestId(segmentId)
      .withNeId(NE_ID)
      .withNodePosition(crmNodePosition)
      .withTransponderCtpUri(TRANSPONDER_CTP_URI);
    if (crmNodePosition == CrmNodePosition.INGRESS) {
      builder.withAidSrcTp(aid);
    } else {
      builder.withAidDstTp(aid);
    }
    segmentRepository.store(builder.build());
  }

  private CrmSegmentRequestDto validRequest() {
    CrmSegmentRequestDto request = new CrmSegmentRequestDto();
    request.neId = NE_ID;
    request.assignRoles = new AssignRoles(WORKING_SEGMENT_ID, PROTECTION_SEGMENT_ID, PROTECTION_SETTINGS);
    return request;
  }

  @Test
  void adoptExistingProtectionGroup() throws ObjectInUseException {
    //given
    final String workingCtpUri = WORKING_PTP_URI + "/ctp/traffic";
    final String protectionCtpUri = PROTECTING_PTP_URI + "/ctp/traffic";
    CrmSegmentRequestDto request = validRequest();

    prepareSegment(WORKING_SEGMENT_ID, WORKING_TTP_AID, CrmNodePosition.EGRESS);
    prepareSegment(PROTECTION_SEGMENT_ID, PROTECTING_TTP_AID, CrmNodePosition.EGRESS);

    PtpRef workingPtpRef = new PtpRef(NE_ID, new Aid(WORKING_TTP_AID), new Uri(WORKING_PTP_URI), null);
    when(ptpResources.findPtp(NE_ID, new Aid(WORKING_TTP_AID))).thenReturn(Optional.of(workingPtpRef));

    PtpRef protectingPtpRef = new PtpRef(NE_ID, new Aid(PROTECTING_TTP_AID), new Uri(PROTECTING_PTP_URI), null);
    when(ptpResources.findPtp(NE_ID, new Aid(PROTECTING_TTP_AID))).thenReturn(Optional.of(protectingPtpRef));

    CardRef oppmCard = new CardRef(NE_ID, new Aid("cardAid"), new Uri(workingCtpUri), null, 0);
    when(ctpResources.findCard(NE_ID, new Uri(workingCtpUri))).thenReturn(Optional.of(oppmCard));

    String crossConnectUri = "crossConnectUri";
    CrossConnectRef crossConnect = new CrossConnectRef(NE_ID, null, new Uri(crossConnectUri), List.of(), List.of(workingCtpUri), null, null);
    when(cardResources.listCrossConnections(NE_ID, oppmCard.aid())).thenReturn(List.of(crossConnect));

    var protectionGroupUri = "protectionGroupUri";
    var protectionGroup = new LineProtectionGroupRef(NE_ID, null, new Uri(protectionGroupUri),
      null, new Uri(workingCtpUri), null, new Uri(protectionCtpUri), null,
      null, null, null, null, null);
    when(crossConnectResources.getProtectionGroup(NE_ID, new Uri(crossConnectUri)))
      .thenReturn(Optional.of(protectionGroup));
    verifyNoInteractions(provision);

    //when
    var actual = sut.execute(request, null);
    //then
    assertThat(actual).isNotNull()
      .extracting(SegmentRequestOperationRet::operationCompleted)
      .isEqualTo(true);
    var workingSegment = segmentRepository.getSegment(WORKING_SEGMENT_ID, NE_ID);
    assertThat(workingSegment)
      .isNotNull()
      .extracting(WdmSegment::getProtectionGroupUri)
      .isEqualTo(protectionGroupUri);
    var protectionSegment = segmentRepository.getSegment(PROTECTION_SEGMENT_ID, NE_ID);
    assertThat(protectionSegment)
      .isNotNull()
      .extracting(WdmSegment::getProtectionGroupUri)
      .isEqualTo(protectionGroupUri);
  }

}