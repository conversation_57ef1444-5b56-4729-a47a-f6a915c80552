/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */
package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.provision.f8.api.in.ObjectDoesNotExistException;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.Objects;

import static com.adva.nlms.resource.mediator.f8.events.ProvisionEventsCreator.ptpAdopted;
import static com.adva.nlms.resource.mediator.f8.events.ProvisionEventsCreator.ptpCreated;

class PtpRequest implements Request {
  // FNMD-113290: For some cases (old 400 cards modelling) params on PTP has to be set after CTP creation.
  // Therefore, this request is used only for PTP creation. For updating params on PTP, PtpModifyRequest was introduced.
  private static final Logger log = LogManager.getLogger(PtpRequest.class);

  private final int neId;
  private final String uri;
  private final boolean exists;
  private final Provision provisionApi;


  PtpRequest(int neId, String uri, boolean exists, Provision provisionApi) {
    this.neId = neId;
    this.uri = uri;
    this.exists = exists;
    this.provisionApi = provisionApi;
  }

  @Override
  public ProvisionLocalEvent provision() {
    if (exists) {
      return ptpAdopted(uri);
    }
    provisionApi.provisionPtp(NetworkElementID.create(neId), uri, Collections.emptySet());
    return ptpCreated(uri);
  }

  @Override
  public void delete() {
    log.info("Deleting PTP {}", uri);
    try {
      provisionApi.deletePtp(NetworkElementID.create(neId), uri);
    } catch (ObjectDoesNotExistException e) {
      log.warn("Could not delete PTP on NE: {} with uri {} - entity does not exist", neId, uri);
    }
  }

  @Override
  public ProvisionLocalEvent adopt() {
    if (exists) {
      return ptpAdopted(uri);
    }
    throw new NoAdoptResourceException("Missing PTP %s".formatted(uri));
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    PtpRequest that = (PtpRequest) o;
    return neId == that.neId && Objects.equals(uri, that.uri) && exists == that.exists &&
      Objects.equals(provisionApi, that.provisionApi);
  }

  @Override
  public int hashCode() {
    return Objects.hash(neId, uri, exists, provisionApi);
  }

  @Override
  public String toString() {
    return "PtpRequest{" +
            "neId=" + neId +
            ", uri='" + uri + '\'' +
            ", exists='" + exists +
            ", provisionApi=" + provisionApi +
            '}';
  }
}
