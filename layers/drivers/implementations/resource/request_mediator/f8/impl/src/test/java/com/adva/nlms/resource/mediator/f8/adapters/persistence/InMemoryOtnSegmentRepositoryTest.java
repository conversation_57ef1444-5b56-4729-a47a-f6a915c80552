/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.adapters.persistence;

import com.adva.nlms.resource.mediator.f8.events.CtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncCreatedEvent;
import com.adva.nlms.resource.mediator.f8.adapters.persistence.InMemoryOtnSegmentRepository;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegment;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegmentRepository;
import com.adva.nlms.resource.mediator.f8.otn.api.in.SegmentID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

class InMemoryOtnSegmentRepositoryTest {

  private OtnSegmentRepository sut;

  @BeforeEach
  void initTest() {
    sut = new InMemoryOtnSegmentRepository();
  }

  @Test
  void testSave() {
    SegmentID segmentID = new SegmentID(1, UUID.randomUUID().toString());
    sut.save(new OtnSegment(segmentID, List.of(new CtpCreatedEvent("ctp_uri"))));
    assertTrue(sut.exists(segmentID));
  }

  @Test
  void testDelete() {
    SegmentID segmentID = new SegmentID(1, UUID.randomUUID().toString());
    sut.save(new OtnSegment(segmentID, List.of(new CtpCreatedEvent("ctp_uri"))));
    sut.delete(segmentID);
    assertFalse(sut.exists(segmentID));
  }

  @Test
  void testFind() {
    SegmentID segmentID = new SegmentID(1, UUID.randomUUID().toString());
    sut.save(new OtnSegment(segmentID, List.of(new CtpCreatedEvent("ctp_uri"))));
    assertNotNull(sut.findOne(segmentID));
  }

  @Test
  void testFindAssociatedSegment() {
    final String ctpClientUri = "ctp_client_uri";
    final String ctpNetworkUri = "ctp_network_uri";
    final String sncUri = "snc_uri";
    final String sncUriPrefix = "snc_";
    SegmentID segmentID = new SegmentID(1, UUID.randomUUID().toString());
    sut.save(new OtnSegment(segmentID, List.of(
      new CtpCreatedEvent(ctpClientUri),
      new CtpCreatedEvent(ctpNetworkUri),
      new SncCreatedEvent(sncUri, Set.of(ctpClientUri), Set.of(ctpNetworkUri))
    )));
    assertTrue(sut.findAssociatedSegmentByEndpointAndSncUriPrefix(1, ctpClientUri, sncUriPrefix).isPresent());
  }
}