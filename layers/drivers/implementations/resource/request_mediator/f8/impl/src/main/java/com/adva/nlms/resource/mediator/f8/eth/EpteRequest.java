/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: asowa
 */
package com.adva.nlms.resource.mediator.f8.eth;

import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;

import static com.adva.nlms.resource.mediator.f8.events.ProvisionEventsCreator.epteModify;

class EpteRequest implements Request {

  private static final Logger log = LogManager.getLogger(EpteRequest.class);

  private final int neId;
  private final String epteUri;
  private final String protectionGroupUri;
  private final Provision provisionApi;

  EpteRequest(int neId, String epteUri, String protectionGroupUri, Provision provisionApi) {
    this.neId = neId;
    this.epteUri = epteUri;
    this.protectionGroupUri = protectionGroupUri;
    this.provisionApi = provisionApi;
  }

  @Override
  public ProvisionLocalEvent provision() {
    log.info("Provision epte {} on protection group {}", epteUri, protectionGroupUri);
    if (provisionApi.setEpteOnFirstAvailableEpteOnProtectionGroup(neId, protectionGroupUri, epteUri)) {
      return epteModify(protectionGroupUri, epteUri);
    }
    return null;
  }

  @Override
  public void delete() {
    log.info("Clearing epte {} on protection group {}", epteUri, protectionGroupUri);
    provisionApi.clearEpte(neId, protectionGroupUri, epteUri);
  }

  @Override
  public ProvisionLocalEvent adopt() {
    log.info("Adopt, leaving epte {} as it is on protection group {}", epteUri, protectionGroupUri);
    return null;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    EpteRequest that = (EpteRequest) o;
    return neId == that.neId && Objects.equals(epteUri, that.epteUri) && Objects.equals(protectionGroupUri, that.protectionGroupUri) && Objects.equals(provisionApi, that.provisionApi);
  }

  @Override
  public int hashCode() {
    return Objects.hash(neId, epteUri, protectionGroupUri, provisionApi);
  }

  @Override
  public String toString() {
    return "EpteRequest{" +
      "neId=" + neId +
      ", epteUri='" + epteUri + '\'' +
      ", protectiongroupUri='" + protectionGroupUri + '\'' +
      ", provisionApi=" + provisionApi +
      '}';
  }
}
