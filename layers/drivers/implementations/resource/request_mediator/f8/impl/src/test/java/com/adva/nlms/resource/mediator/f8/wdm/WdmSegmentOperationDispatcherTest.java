/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;
import com.adva.nlms.resource.mediator.f8.wdm.SegmentAbandonRequest;
import com.adva.nlms.resource.mediator.f8.wdm.SegmentAdoptRequest;
import com.adva.nlms.resource.mediator.f8.wdm.SegmentCreateRequest;
import com.adva.nlms.resource.mediator.f8.wdm.SegmentDeleteRequest;
import com.adva.nlms.resource.mediator.f8.wdm.SegmentOperationDispatcher;
import com.adva.nlms.resource.mediator.f8.wdm.SegmentPowerEqualizeRequest;
import com.adva.nlms.resource.mediator.f8.wdm.SegmentSetAdminStateRequest;
import org.junit.jupiter.api.Test;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

class WdmSegmentOperationDispatcherTest {

  private final SegmentOperationDispatcher sut = new SegmentOperationDispatcher(
    Set.of(new SegmentCreateRequest(null, null, null, null, null, null, null, null, null, null),
      new SegmentAbandonRequest(null, null),
      new SegmentAdoptRequest(null, null, null, null, null, null, null, null),
      new SegmentDeleteRequest(null, null, null, null, null, null, null, null),
      new SegmentPowerEqualizeRequest(null, null),
      new SegmentSetAdminStateRequest(null,null, null, null, null, null, null, null)
    ));

  @Test
  void testValidImplementationTaken() {
    assertEquals(SegmentCreateRequest.class, sut.getSegmentRequestOperation(CrmSegmentRequestDto.RequestType.CREATE).getClass());
    assertEquals(SegmentAdoptRequest.class, sut.getSegmentRequestOperation(CrmSegmentRequestDto.RequestType.ADOPT).getClass());
    assertEquals(SegmentAbandonRequest.class, sut.getSegmentRequestOperation(CrmSegmentRequestDto.RequestType.ABANDON).getClass());
    assertEquals(SegmentDeleteRequest.class, sut.getSegmentRequestOperation(CrmSegmentRequestDto.RequestType.DELETE).getClass());
    assertEquals(SegmentPowerEqualizeRequest.class, sut.getSegmentRequestOperation(CrmSegmentRequestDto.RequestType.POWEREQUALIZE).getClass());
    assertEquals(SegmentSetAdminStateRequest.class, sut.getSegmentRequestOperation(CrmSegmentRequestDto.RequestType.SETADMINSTATE).getClass());
  }

  @Test
  void testUnsupportedOperation() {
    assertThrows(UnsupportedOperationException.class, () -> sut.getSegmentRequestOperation(CrmSegmentRequestDto.RequestType.UNKNOWN));
  }

}
