/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.mediation.config.f8.croma.SlcActiveEndpoint;
import com.adva.nlms.mediation.config.f8.croma.slc.api.Slc;
import com.adva.nlms.mediation.config.f8.croma.slc.api.SlcEndpoint;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class SlcTestBuilder {
  private int neId;
  private int identifier;
  private SlcEndpointBuilder aEndpointBuilder;
  private SlcEndpointBuilder zEndpointBuilder;

  public static SlcTestBuilder newBuilder() {
    return new SlcTestBuilder();
  }
  private SlcTestBuilder() {}

  public SlcTestBuilder withNeId(final int neId) {
    this.neId = neId;
    return this;
  }
  public SlcTestBuilder withIdentifier(final int identifier) {
    this.identifier = identifier;
    return this;
  }
  public SlcEndpointBuilder aEndpoint() {
    aEndpointBuilder = new SlcEndpointBuilder();
    return aEndpointBuilder;
  }

  public SlcEndpointBuilder zEndpoint() {
    zEndpointBuilder = new SlcEndpointBuilder();
    return zEndpointBuilder;
  }

  public Slc build() {
    SlcEndpoint aEndpoint = Optional.ofNullable(aEndpointBuilder).map(SlcEndpointBuilder::construct).orElse(null);
    SlcEndpoint zEndpoint = Optional.ofNullable(zEndpointBuilder).map(SlcEndpointBuilder::construct).orElse(null);
    return new Slc(1, neId, identifier, null, aEndpoint, zEndpoint, List.of(), List.of(), null, null, null);
  }

  public class SlcEndpointBuilder {
    private int adminState;
    private int operation;
    private int pathNodeNumber = 1;
    private SlcActiveEndpointBuilder activeEndpointBuilder;

    private SlcEndpointBuilder() {}

    SlcActiveEndpointBuilder activeEndpoint() {
      this.activeEndpointBuilder = new SlcActiveEndpointBuilder();
      return activeEndpointBuilder;
    }

    SlcEndpointBuilder withAdminState(final int adminState) {
      this.adminState = adminState;
      return this;
    }

    SlcEndpointBuilder withOperation(final int operation) {
      this.operation = operation;
      return this;
    }

    SlcEndpointBuilder withPathNodeNumber(final int pathNodeNumber) {
      this.pathNodeNumber = pathNodeNumber;
      return this;
    }

    public SlcTestBuilder buildEndpoint(){
      return SlcTestBuilder.this;
    }

    SlcEndpoint construct(){
      return new SlcEndpoint(pathNodeNumber, adminState, operation, activeEndpointBuilder.construct(), null);
    }

    public class SlcActiveEndpointBuilder {
      private String resourceInstance;
      private List<String> slcInventory = new ArrayList<>();

      private SlcActiveEndpointBuilder() {
      }

      SlcActiveEndpointBuilder withResourceInstance(final String resourceInstance) {
        this.resourceInstance = resourceInstance;
        return this;
      }

      SlcActiveEndpointBuilder withSlcInventory(final List<String> slcInventory) {
        this.slcInventory.clear();
        this.slcInventory.addAll(slcInventory);
        return this;
      }

      SlcActiveEndpointBuilder addInventoryElement(final String slcInventoryElement) {
        slcInventory.add(slcInventoryElement);
        return this;
      }

      public SlcEndpointBuilder buildActiveEndpoint(){
        return SlcEndpointBuilder.this;
      }
      SlcActiveEndpoint construct(){
        return new SlcActiveEndpoint(resourceInstance, slcInventory);
      }
    }
  }
}
