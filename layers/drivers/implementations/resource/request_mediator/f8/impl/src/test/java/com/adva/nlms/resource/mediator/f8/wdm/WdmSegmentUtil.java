/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.resource.crm.model.wdm.ConstrainingEntry;
import com.adva.nlms.resource.crm.model.wdm.Selector;
import com.adva.nlms.resource.crm.model.wdm.TtpInfo;
import com.adva.nlms.resource.crm.model.wdm.Tunable;

import java.util.Optional;

class WdmSegmentUtil {
  private WdmSegmentUtil() {
    throw new UnsupportedOperationException("Utility class");
  }

  static TtpInfo sampleTtpWithTunableTps(String ttpName, String portIdAid) {
    Selector selector = new Selector();

    Optional.ofNullable(portIdAid)
      .map(Tunable::new)
      .map(resource -> {
        ConstrainingEntry entry = new ConstrainingEntry();
        entry.setConstrainedResource(resource);
        return entry;
      })
      .ifPresent(selector.getConstrainingEntryList()::add);

    TtpInfo ttp = new TtpInfo(ttpName);
    ttp.getEdgeBindingConstraints()
      .addSelector(selector);
    return ttp;
  }
}
