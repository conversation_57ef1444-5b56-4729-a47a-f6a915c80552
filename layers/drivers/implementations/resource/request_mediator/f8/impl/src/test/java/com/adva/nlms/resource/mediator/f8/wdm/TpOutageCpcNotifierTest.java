/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.common.config.EquipmentState;
import com.adva.nlms.mediation.mo.inventory.resources.OperationalState;
import com.adva.nlms.mediation.mo.inventory.resources.SecondaryState;
import com.adva.nlms.mediation.mo.inventory.resources.State;
import com.adva.nlms.resource.advertisement.api.out.CrmMessageSender;
import com.adva.nlms.resource.crm.model.CrmModelConfiguration;
import com.adva.nlms.resource.crm.model.CrmNiTranslator;
import com.adva.nlms.resource.crm.model.OSCStatus;
import com.adva.nlms.resource.crm.model.wdm.CrmWdmModelConfiguration;
import com.adva.nlms.resource.crm.model.wdm.CrmWdmModelDAO;
import com.adva.nlms.resource.crm.model.wdm.LtpInfoBuilder;
import com.adva.nlms.resource.crm.model.wdm.OSCSignal;
import com.adva.nlms.resource.mediator.f8.NiCallbackSender;
import com.adva.nlms.resource.mediator.f8.RRMConfiguration;
import com.adva.nlms.resource.mediator.f8.wdm.MonitoredEntity;
import com.adva.nlms.resource.mediator.f8.wdm.MonitoredEntity.TerminationPointPair;
import com.adva.nlms.resource.mediator.f8.wdm.MonitoredEntityKey;
import com.adva.nlms.resource.mediator.f8.wdm.OutageMonitor;
import com.adva.nlms.resource.mediator.f8.wdm.TpOutageCpcNotifier;
import com.adva.nlms.resource.mediator.f8.wdm.adapters.persistence.RrmPersistenceConfiguration;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.EnvelopeIncomingAddresses;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.RrmSegmentRepository;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.WdmSegment;
import com.google.protobuf.InvalidProtocolBufferException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatcher;
import org.mockito.Mockito;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static ni.msg.EnvelopeOuterClass.Address;
import static ni.msg.EnvelopeOuterClass.Envelope;
import static ni.msg.EnvelopeOuterClass.Identifier;
import static ni.proto.segment_callback.SegmentCallbackOuterClass.AlarmNotify;
import static ni.proto.segment_callback.SegmentCallbackOuterClass.SegmentCallback;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

class TpOutageCpcNotifierTest {

  private static final int NE_ID = 76437;
  private static final String OTSIA_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,3/eq/card/ptp/nw,1/ctp/oms/ctp/spslg-17/ctp/otsia";
  private static final String LINE_CARD_URI = "/mit/me/1/eqh/shelf,3/eqh/slot,3/eq/card/ptp/nw,1";
  private static final String SEGMENT_REQUEST_ID = UUID.randomUUID().toString();
  private static final TerminationPointPair TERMINATION_POINT_PAIR = new TerminationPointPair("TX_TP", "RX_TP");
  private static final TerminationPointPair LINE_TERMINATION_POINT_PAIR = new TerminationPointPair(null, "RX_TP");
  private static final Address SRC_ADDRESS = Address.newBuilder().setId(Identifier.newBuilder().setId("SRC")).build();
  private static final Address DST_ADDRESS = Address.newBuilder().setId(Identifier.newBuilder().setId("DST")).build();
  private static final MonitoredEntity ROADM_ENTITY = new MonitoredEntity(SEGMENT_REQUEST_ID, OTSIA_URI, TERMINATION_POINT_PAIR, MonitoredEntity.EquipmentType.ROADM);
  private static final MonitoredEntityKey ROADM_ENTITY_KEY = ROADM_ENTITY.toKey(NE_ID);
  private static final MonitoredEntity LINE_CARD_ENTITY = new MonitoredEntity(SEGMENT_REQUEST_ID, LINE_CARD_URI, LINE_TERMINATION_POINT_PAIR, MonitoredEntity.EquipmentType.LINE_CARD);
  private static final MonitoredEntityKey LINE_CARD_ENTITY_KEY = LINE_CARD_ENTITY.toKey(NE_ID);
  private static final ArgumentMatcher<Envelope> RX_SET = terminationPointMatcher(AlarmNotify.EventType.ALARM_SET, TERMINATION_POINT_PAIR.rx());
  private static final ArgumentMatcher<Envelope> TX_SET = terminationPointMatcher(AlarmNotify.EventType.ALARM_SET, TERMINATION_POINT_PAIR.tx());
  private static final ArgumentMatcher<Envelope> RX_CLEAR = terminationPointMatcher(AlarmNotify.EventType.ALARM_CLEAR, TERMINATION_POINT_PAIR.rx());
  private static final ArgumentMatcher<Envelope> TX_CLEAR = terminationPointMatcher(AlarmNotify.EventType.ALARM_CLEAR, TERMINATION_POINT_PAIR.tx());
  private static final ArgumentMatcher<Envelope> RX_SET_ONLY = createEnvelopeMatcher(AlarmNotify.EventType.ALARM_SET, TERMINATION_POINT_PAIR.rx(), AlarmNotify.OscStatus.OSC_UNKNOWN, false);
  private static final ArgumentMatcher<Envelope> TX_SET_ONLY = createEnvelopeMatcher(AlarmNotify.EventType.ALARM_SET, TERMINATION_POINT_PAIR.tx(), AlarmNotify.OscStatus.OSC_UNKNOWN, false);
  private static final ArgumentMatcher<Envelope> RX_CLEAR_ONLY = createEnvelopeMatcher(AlarmNotify.EventType.ALARM_CLEAR, TERMINATION_POINT_PAIR.rx(), AlarmNotify.OscStatus.OSC_UNKNOWN, false);
  private static final ArgumentMatcher<Envelope> TX_CLEAR_ONLY = createEnvelopeMatcher(AlarmNotify.EventType.ALARM_CLEAR, TERMINATION_POINT_PAIR.tx(), AlarmNotify.OscStatus.OSC_UNKNOWN, false);

  private static State buildOperOutage(OperationalState operationalState) {
    return new State(EquipmentState.IS, operationalState, List.of(SecondaryState.FAF));
  }

  private static State buildOperNormal() {
    return new State(EquipmentState.IS, OperationalState.NORMAL, List.of());
  }

  private final RrmSegmentRepository segmentRepository;
  private final CrmWdmModelDAO crmWdmModelDAO;
  private final CrmMessageSender crmMessageSender;
  private final TpOutageCpcNotifier sut;

  TpOutageCpcNotifierTest() {
    OutageMonitor outageMonitor = new OutageMonitor();
    segmentRepository = new RrmPersistenceConfiguration().rrmSegmentRepository();
    CrmWdmModelConfiguration wdmModelConfiguration = new CrmWdmModelConfiguration();
    crmWdmModelDAO = wdmModelConfiguration.crmWdmModelDAO();
    CrmModelConfiguration crmModelConfiguration = new CrmModelConfiguration();
    CrmNiTranslator crmNiTranslator = crmModelConfiguration.crmNiTranslator();
    crmMessageSender = Mockito.spy(new CrmMessageSender() {
      @Override
      public void send(Envelope envelope) {
      }

      @Override
      public void send(Envelope envelope, int neId) {
      }

      @Override
      public boolean isConnectionOpenForNE(int neId) {
        return true;
      }

      @Override
      public void closeConnection(int neId) {

      }
    });
    NiCallbackSender niCallbackSender = new RRMConfiguration().niCallbackSender(crmMessageSender, crmNiTranslator);

    sut = new TpOutageCpcNotifier(outageMonitor, niCallbackSender, crmWdmModelDAO, segmentRepository);
  }

  @BeforeEach
  void initTestData() {
    WdmSegment segment = WdmSegment.newSegment()
      .withNeId(NE_ID)
      .withSegmentRequestId(SEGMENT_REQUEST_ID)
      .build();
    segment.setEnvelopeIncomingAddresses(EnvelopeIncomingAddresses.of(SRC_ADDRESS, DST_ADDRESS));
    segmentRepository.store(segment);
  }

  @DisplayName("""
    WHEN: entity transitions to oper=outage
    THEN: raise alarm on rx
    """)
  @Test
  void testRoadmAlarmRaisedRx() {
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperOutage(OperationalState.OUTAGE), true, true);
    verify(crmMessageSender).send(argThat(RX_SET_ONLY));
  }


  @DisplayName("""
    WHEN: entity's admin state=Maintenance and it transitions to oper=outage
    THEN: alarm not raised
    """)
  @Test
  void testRoadmMaintenanceAminStateAlarmNotRaisedRx() {
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, new State(EquipmentState.MT, OperationalState.OUTAGE, List.of(SecondaryState.FAF)), true, true);
    verifyNoInteractions(crmMessageSender);
  }

  @DisplayName("""
    WHEN: entity transitions to oper=outage-tx
    THEN: raise alarm on tx
    """)
  @Test
  void testRoadmAlarmRaisedTx() {
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperOutage(OperationalState.OUTAGE_TX), true, true);
    verify(crmMessageSender).send(argThat(TX_SET_ONLY));
  }

  @DisplayName("""
    WHEN: entity transitions to oper=outage-bidirectional
    THEN: process outage as if both outage-rx and outage-tx were raised
    """)
  @Test
  void testRoadmAlarmRaisedBi() {
    var state = buildOperOutage(OperationalState.OUTAGE_BIDIRECTIONAL);
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, state, true, true);
    verify(crmMessageSender, times(1)).send(argThat(RX_SET));
    verify(crmMessageSender, times(1)).send(argThat(TX_SET));
    verify(crmMessageSender, times(0)).send(argThat(RX_CLEAR));
    verify(crmMessageSender, times(0)).send(argThat(TX_CLEAR));
  }

  @DisplayName("""
    GIVEN: entity in oper=outage
    WHEN: entity transitions to oper=outage-bidirectional
    THEN: process outage as if outage-tx was raised
    """)
  @Test
  void testRoadmAlarmRaisedBiAfterOutageRx() {
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperOutage(OperationalState.OUTAGE), true, true);
    Mockito.clearInvocations(crmMessageSender);
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperOutage(OperationalState.OUTAGE_BIDIRECTIONAL), true, true);
    verify(crmMessageSender).send(argThat(TX_SET_ONLY));
  }

  @DisplayName("""
    GIVEN: entity in oper=outage-tx
    WHEN: entity transitions to oper=outage-bidirectional
    THEN: process outage as if outage was raised
    """)
  @Test
  void testRoadmAlarmRaisedBiAfterOutageTx() {
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperOutage(OperationalState.OUTAGE_TX), true, true);
    Mockito.clearInvocations(crmMessageSender);
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperOutage(OperationalState.OUTAGE_BIDIRECTIONAL), true, true);
    verify(crmMessageSender).send(argThat(RX_SET_ONLY));
  }

  @DisplayName("""
    GIVEN: entity in oper=outage-bidirectional
    WHEN: entity transitions to oper=normal
    THEN: process outage as if both outage-rx and outage-tx were cleared
    """)
  @Test
  void testRoadmAlarmClearedBi() {
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperOutage(OperationalState.OUTAGE_BIDIRECTIONAL), true, true);
    Mockito.clearInvocations(crmMessageSender);
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperNormal(), true, true);
    verify(crmMessageSender, times(0)).send(argThat(RX_SET));
    verify(crmMessageSender, times(0)).send(argThat(TX_SET));
    verify(crmMessageSender, times(1)).send(argThat(RX_CLEAR));
    verify(crmMessageSender, times(1)).send(argThat(TX_CLEAR));
  }

  @DisplayName("""
    GIVEN: entity in oper=outage-bidirectional
    WHEN: entity transitions to oper=outage
    THEN: process outage as if outage-tx was cleared
    """)
  @Test
  void testRoadmAlarmClearedTxAfterOutageBi() {
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperOutage(OperationalState.OUTAGE_BIDIRECTIONAL), true, true);
    Mockito.clearInvocations(crmMessageSender);
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperOutage(OperationalState.OUTAGE), true, true);
    verify(crmMessageSender).send(argThat(TX_CLEAR_ONLY));
  }

  @DisplayName("""
    GIVEN: entity in oper=outage-bidirectional
    WHEN: entity transitions to oper=outage-tx
    THEN: process outage as if outage was cleared
    """)
  @Test
  void testRoadmAlarmClearedRxAfterOutageBi() {
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperOutage(OperationalState.OUTAGE_BIDIRECTIONAL), true, true);
    Mockito.clearInvocations(crmMessageSender);
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperOutage(OperationalState.OUTAGE_TX), true, true);
    verify(crmMessageSender).send(argThat(RX_CLEAR_ONLY));
  }

  @DisplayName("""
    GIVEN: entity in oper=outage
    WHEN: entity transitions to oper=normal
    THEN: alarm is cleared in rx direction
    """)
  @Test
  void testAlarmRxCleared() {
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperOutage(OperationalState.OUTAGE), true, true);
    Mockito.clearInvocations(crmMessageSender);
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperNormal(), true, true);
    verify(crmMessageSender).send(argThat(RX_CLEAR_ONLY));
  }

  @DisplayName("""
    GIVEN: entity in oper=outage-tx
    WHEN: entity transitions to oper=normal
    THEN: alarm is cleared in tx direction
    """)
  @Test
  void testAlarmTxCleared() {
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperOutage(OperationalState.OUTAGE_TX), true, true);
    Mockito.clearInvocations(crmMessageSender);
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperNormal(), true, true);
    verify(crmMessageSender).send(argThat(TX_CLEAR_ONLY));
  }

  @DisplayName("""
    GIVEN: entity in oper=outage-tx and admin state=Disabling
    WHEN: entity transitions to oper=normal
    THEN: alarm is cleared in tx direction
    """)
  @Test
  void testAlarmTxClearedEvenWhenAdminStateIsOtherThanISOrAIS() {
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperOutage(OperationalState.OUTAGE_TX), true, true);
    Mockito.clearInvocations(crmMessageSender);
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, new State(EquipmentState.DSBLD, OperationalState.NORMAL, List.of()), true, true);
    verify(crmMessageSender).send(argThat(TX_CLEAR_ONLY));
  }


  @DisplayName("""
    GIVEN: entity in oper=outage-tx
    WHEN: entity transitions to oper=normal
    THEN: alarm is cleared in tx direction when last entityUri is cleared
    """)
  @Test
  void testAlarmTxClearedLastOneTurnOffTheLightPrinciple() {
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperOutage(OperationalState.OUTAGE), true, true);
    sut.notifyOutage(LINE_CARD_ENTITY_KEY, LINE_CARD_ENTITY, buildOperOutage(OperationalState.OUTAGE), true, true);
    Mockito.clearInvocations(crmMessageSender);
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperNormal(), true, true);
    verifyNoInteractions(crmMessageSender);
    sut.notifyOutage(LINE_CARD_ENTITY_KEY, LINE_CARD_ENTITY, buildOperNormal(), true, true);
    verify(crmMessageSender).send(argThat(RX_CLEAR));
  }

  @DisplayName("""
    WHEN: entity transitions to oper=outage-bidirectional
    THEN: alarm for rx direction is raised
    """)
  @Test
  void testLineCardAlarmRaisedBi() {
    sut.notifyOutage(LINE_CARD_ENTITY_KEY, LINE_CARD_ENTITY, buildOperOutage(OperationalState.OUTAGE_BIDIRECTIONAL), true, true);
    verify(crmMessageSender).send(argThat(RX_SET_ONLY));
  }

  @DisplayName("""
    WHEN: entity transitions to oper=outage
    THEN: alarm for rx direction is raised
    """)
  @Test
  void testLineCardAlarmRaisedRx() {
    sut.notifyOutage(LINE_CARD_ENTITY_KEY, LINE_CARD_ENTITY, buildOperOutage(OperationalState.OUTAGE), true, true);
    verify(crmMessageSender).send(argThat(RX_SET_ONLY));
  }

  @DisplayName("""
    WHEN: entity transitions to oper=outage-tx
    THEN: alarm is not raised
    """)
  @Test
  void testLineCardAlarmNotRaisedTx() {
    sut.notifyOutage(LINE_CARD_ENTITY_KEY, LINE_CARD_ENTITY, buildOperOutage(OperationalState.OUTAGE_TX), true, true);
    verifyNoInteractions(crmMessageSender);
  }

  @DisplayName("""
    WHEN: entity transitions to oper=outage and secondary state is not facility failure
    THEN: alarm is not raised
    """)
  @Test
  void testLineCardAlarmNotRaisedWhenNoFAF() {
    State state = new State(EquipmentState.IS, OperationalState.OUTAGE, List.of(SecondaryState.SGEO));
    sut.notifyOutage(LINE_CARD_ENTITY_KEY, LINE_CARD_ENTITY, state, true, true);
    verifyNoInteractions(crmMessageSender);
  }

  @DisplayName("""
    GIVEN: Line Card entity in oper=outage
    WHEN: entity transitions to oper=normal
    THEN: alarm is cleared in rx direction
    """)
  @Test
  void testLineCardAlarmRxCleared() {
    sut.notifyOutage(LINE_CARD_ENTITY_KEY, LINE_CARD_ENTITY, buildOperOutage(OperationalState.OUTAGE), true, true);
    Mockito.clearInvocations(crmMessageSender);
    sut.notifyOutage(LINE_CARD_ENTITY_KEY, LINE_CARD_ENTITY, buildOperNormal(), true, true);
    verify(crmMessageSender).send(argThat(RX_CLEAR_ONLY));
  }

  @DisplayName("""
    GIVEN: Line Card entity in oper=outage-tx
    WHEN: entity transitions to oper=normal
    THEN: no alarm is cleared
    """)
  @Test
  void testLineCardAlarmTxCleared() {
    sut.notifyOutage(LINE_CARD_ENTITY_KEY, LINE_CARD_ENTITY, buildOperOutage(OperationalState.OUTAGE_TX), true, true);
    Mockito.clearInvocations(crmMessageSender);
    sut.notifyOutage(LINE_CARD_ENTITY_KEY, LINE_CARD_ENTITY, buildOperNormal(), true, true);
    verifyNoInteractions(crmMessageSender);
  }

  @Test
  void testAlienAlarmRaisedRx() {
    MonitoredEntity monitoredEntity = new MonitoredEntity(SEGMENT_REQUEST_ID, OTSIA_URI, LINE_TERMINATION_POINT_PAIR, MonitoredEntity.EquipmentType.ALIEN);
    sut.notifyOutage(monitoredEntity.toKey(NE_ID), monitoredEntity, buildOperOutage(OperationalState.OUTAGE), true, true);
    ArgumentMatcher<Envelope> matcher = createEnvelopeMatcher(AlarmNotify.EventType.ALARM_SET, LINE_TERMINATION_POINT_PAIR.rx(), AlarmNotify.OscStatus.OSC_UNKNOWN, true);
    Mockito.verify(crmMessageSender).send(Mockito.argThat(matcher));
  }

  @Test
  void testAlarmWithValidOscSignal() {
    crmWdmModelDAO.addLtp(NE_ID, LtpInfoBuilder.newBuilder()
      .withLtpName(TERMINATION_POINT_PAIR.rx())
      .withOscSignal(new OSCSignal("ptpUri", OSCStatus.DOWN))
      .build());
    sut.notifyOutage(ROADM_ENTITY_KEY, ROADM_ENTITY, buildOperOutage(OperationalState.OUTAGE), true, true);
    ArgumentMatcher<Envelope> matcher = createEnvelopeMatcher(AlarmNotify.EventType.ALARM_SET, TERMINATION_POINT_PAIR.rx(), AlarmNotify.OscStatus.OSC_DOWN, false);
    verify(crmMessageSender, times(1)).send(argThat(matcher));
  }

  private static ArgumentMatcher<Envelope> terminationPointMatcher(AlarmNotify.EventType eventType, String terminationPoint) {
    return envelope -> {
      try {
        assertThat(envelope)
          .isNotNull()
          .extracting(Envelope::getSource, Envelope::getDestination)
          .containsExactly(DST_ADDRESS, SRC_ADDRESS);

        var segmentCallback = SegmentCallback.parseFrom(envelope.getPayload());
        assertThat(segmentCallback)
          .isNotNull()
          .extracting(SegmentCallback::getAlarmNotify)
          .isNotNull();
        var alarmNotify = segmentCallback.getAlarmNotify();
        return eventType == alarmNotify.getEventType() &&
          Objects.equals(terminationPoint, alarmNotify.getTerminationPoint());
      } catch (InvalidProtocolBufferException e) {
        fail(e.getLocalizedMessage(), e);
      }
      return true;
    };
  }

  private static ArgumentMatcher<Envelope> createEnvelopeMatcher(AlarmNotify.EventType eventType, String terminationPoint, AlarmNotify.OscStatus oscStatus, boolean isNoClientSignal) {
    return envelope -> {
      try {
        assertThat(envelope)
          .isNotNull()
          .extracting(Envelope::getSource, Envelope::getDestination)
          .containsExactly(DST_ADDRESS, SRC_ADDRESS);

        SegmentCallback segmentCallback = SegmentCallback.parseFrom(envelope.getPayload());
        //noinspection unchecked
        assertThat(segmentCallback)
          .isNotNull()
          .extracting(SegmentCallback::getAlarmNotify)
          .isNotNull()
          .extracting(
            AlarmNotify::getEventType,
            AlarmNotify::getOscStatus,
            AlarmNotify::getNoClientSignal,
            AlarmNotify::getSegmentId,
            AlarmNotify::getTerminationPoint
          )
          .containsExactly(
            eventType,
            oscStatus,
            isNoClientSignal,
            SEGMENT_REQUEST_ID,
            terminationPoint
          );
      } catch (InvalidProtocolBufferException e) {
        fail(e.getLocalizedMessage(), e);
      }
      return true;
    };
  }

}
