/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: zbigniewj
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.opticalparameters.cim.api.CimValue;
import com.adva.nlms.resource.mediator.f8.internalpath.Complete;
import com.adva.nlms.resource.mediator.f8.internalpath.LabelDTO;
import com.adva.nlms.resource.mediator.f8.internalpath.LabelType;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabelType;
import com.adva.nlms.resource.mediator.f8.internalpath.PortResourceIdentifier;
import com.adva.nlms.resource.mediator.f8.internalpath.StackedLabel;
import com.adva.nlms.resource.provision.f8.api.in.MoCimParameter;

import java.util.HashSet;
import java.util.Set;

class PortResourceIdentifierToCimParameters {
  private PortResourceIdentifierToCimParameters() {
  }

  public static Set<MoCimParameter> generate(PortResourceIdentifier in, LayerQualifier type) {
    if (in instanceof LabelDTO label) {
      return generateFromLabelDTO(label, type);
    } else if (in instanceof StackedLabel label) {
      return generateFromStackedLabel(label, type);
    }
    return new HashSet<>();
  }

  public static Set<MoCimParameter> generateFromParentInStackedLabel(PortResourceIdentifier in, LayerQualifier type) {
    if (in instanceof StackedLabel label) {
      return generateFromLabelDTO(label.parentLabel(), type);
    }
    return new HashSet<>();
  }

  private static Set<MoCimParameter> generateFromLabelDTO(LabelDTO in, LayerQualifier type) {
    if (!in.multiLabel().label().isEmpty()) {
      return generateFromLabelType(in.multiLabel().label().iterator().next(), type);
    }
    return new HashSet<>();
  }

  private static Set<MoCimParameter> generateFromLabelType(LabelType in, LayerQualifier type) {
    if (in instanceof OtnLabel label) {
      return generateFromOtnLabel(label.type(), type);
    }
    return new HashSet<>();
  }

  private static Set<MoCimParameter> generateFromOtnLabel(OtnLabelType in, LayerQualifier type) {
    Set<MoCimParameter> result = new HashSet<>();
    if (in instanceof Complete label) {
      String motrait = LayerQualifierToCimString.convert(type) + "/odtu";
      result.add(new MoCimParameter(motrait, "tp", new CimValue.Int(label.tp())));
      result.add(new MoCimParameter(motrait, "ts",
        new CimValue.IntList(label.ts().stream().map(Integer::longValue).toList())));
    }
    return result;
  }

  private static Set<MoCimParameter> generateFromStackedLabel(StackedLabel in, LayerQualifier type) {
    return generateFromLabelDTO(in.label(), type);
  }
}
