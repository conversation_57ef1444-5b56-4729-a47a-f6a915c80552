/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.eth;

import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.CtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.PortIdExtractor;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.opticalparameters.api.Values;
import com.adva.nlms.resource.mediator.f8.eth.api.in.EthSegmentRepository;
import com.adva.nlms.resource.mediator.f8.eth.api.in.SegmentID;
import com.adva.nlms.resource.mediator.f8.events.CtpAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.CtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.mediator.f8.events.PtpCreatedEvent;

import java.util.ArrayList;
import java.util.List;

class PrepareDeprovisionEvents {
  private final EthSegmentRepository ethSegmentRepository;
  private final RrmEthDbResourcesFacade rrmEthDbResourcesFacade;
  private final RrmEthCardCapabilities rrmEthCardCapabilities;

  PrepareDeprovisionEvents(EthSegmentRepository ethSegmentRepository, RrmEthDbResourcesFacade rrmEthDbResourcesFacade,
                           RrmEthCardCapabilities rrmEthCardCapabilities) {
    this.ethSegmentRepository = ethSegmentRepository;
    this.rrmEthDbResourcesFacade = rrmEthDbResourcesFacade;
    this.rrmEthCardCapabilities = rrmEthCardCapabilities;
  }

  public List<ProvisionLocalEvent> prepareEvents(int neId, String segmentId) {
    SegmentID segmentID = new SegmentID(neId, segmentId);
    var ethSegment = ethSegmentRepository.findOne(segmentID);
    return ethSegment.map(segment -> segment.provisionEvents().stream()
        .flatMap(event -> analyseEvent(event, neId).stream())
        .distinct()
        .toList()).orElseGet(List::of);
  }

  private List<ProvisionLocalEvent> analyseEvent(ProvisionLocalEvent event, int neId) {
    List<ProvisionLocalEvent> events = new ArrayList<>();
    if (event instanceof CtpCreatedEvent || event instanceof CtpAdoptedEvent) {
      events.addAll(buildPtpEventsFromCtp(event, neId));
    }
    // ignore ptp events, as we create the ones we want to delete based on ctp event
    // PtpAdoptedEvent has to be processed in order to set admin state to Auto In Service
    if (!(event instanceof PtpCreatedEvent)) {
      events.add(event);
    }
    return events;
  }

  private List<ProvisionLocalEvent> buildPtpEventsFromCtp(ProvisionLocalEvent event, int neId) {
    var uri = event.uri();
    var plugRef = rrmEthDbResourcesFacade.findPlugFromCtp(neId, new Uri(uri));
    var ptpRef = rrmEthDbResourcesFacade.findPortFromCtp(neId, new Uri(uri));
    if (plugRef.isEmpty() || ptpRef.isEmpty()) {
      return List.of();
    }
    CardRef cardRef = rrmEthDbResourcesFacade.findCardFromPlug(neId, plugRef.get().aid())
        .orElseThrow(() -> new EthProvisioningException(String.format("Cannot find module for plug with aid %s", plugRef.get().aid().aid())));
    List<Values.PortPoolDescriptor.PortSetDescriptor> portSetDescriptors = rrmEthCardCapabilities.getPortSetDescriptors(
        neId, cardRef.type(), plugRef.get().type(), mapPtpAid(ptpRef.get().aid().aid())
    );
    return portSetDescriptors.stream()
        .flatMap(descriptor -> preparePtpEvents(descriptor, ptpRef.get(), uri, neId).stream())
        .toList();
  }

  private String mapPtpAid(String ptpAid) {
    // Cut out optional lane number e.g. "Port-1/3/p3-1" -> "Port-1/3/p3"
    int firstHyphenIndex = ptpAid.indexOf('-');
    int secondHyphenIndex = ptpAid.indexOf('-', firstHyphenIndex + 1);
    if (secondHyphenIndex != -1) {
      return ptpAid.substring(0, secondHyphenIndex);
    } else {
      return ptpAid;
    }
  }

  private List<ProvisionLocalEvent> preparePtpEvents(Values.PortPoolDescriptor.PortSetDescriptor portSetDescriptor,
                                                     PtpRef ptpRef, String ctp, int neId) {
    List<CtpRef> ctps = new ArrayList<>();
    List<ProvisionLocalEvent> events = new ArrayList<>();
    var baseAid = ptpRef.aid().aid().substring(0, ptpRef.aid().aid().length() - PortIdExtractor.getPortId(ptpRef.aid().aid()).length());
    for (var port : portSetDescriptor.ports()) {
      var portPtp = rrmEthDbResourcesFacade.findPtp(neId, new Aid(baseAid + port.toLowerCase()));
      if (portPtp.isPresent()) {
        ctps.addAll(rrmEthDbResourcesFacade.findAllCtpsFromPtp(neId, portPtp.get().aid()));
        events.add(new PtpCreatedEvent(portPtp.get().uri().uri()));
      }
    }
    if (ctps.size() == 1 && ctps.get(0).uri().uri().equals(ctp)) {
      return events;
    }
    return List.of();
  }
}
