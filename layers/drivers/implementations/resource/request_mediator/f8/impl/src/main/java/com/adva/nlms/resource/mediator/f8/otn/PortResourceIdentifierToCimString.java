/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: zbigniewj
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.resource.mediator.f8.internalpath.Complete;
import com.adva.nlms.resource.mediator.f8.internalpath.Container;
import com.adva.nlms.resource.mediator.f8.internalpath.LabelDTO;
import com.adva.nlms.resource.mediator.f8.internalpath.LabelType;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabelType;
import com.adva.nlms.resource.mediator.f8.internalpath.PortResourceIdentifier;
import com.adva.nlms.resource.mediator.f8.internalpath.StackedLabel;

import java.util.Optional;

class PortResourceIdentifierToCimString {
  private PortResourceIdentifierToCimString() {
  }

  public static Optional<String> convert(PortResourceIdentifier in) {
    if (in instanceof LabelDTO label) {
      return convertLabelDTO(label);
    } else if (in instanceof StackedLabel label) {
      return convertStackedLabel(label);
    }
    return Optional.empty();
  }

  private static Optional<String> convertLabelDTO(LabelDTO in) {
    if (!in.multiLabel().label().isEmpty()) {
      return convertLabelType(in.multiLabel().label().iterator().next());
    }
    return Optional.empty();
  }

  private static Optional<String> convertLabelType(LabelType in) {
    if (in instanceof OtnLabel label) {
      return convertOtnLabel(label.type());
    }
    return Optional.empty();
  }

  private static Optional<String> convertOtnLabel(OtnLabelType in) {
    if (in instanceof Container label) {
      return Optional.of(String.valueOf(label.id()));
    } else if (in instanceof Complete) {
      return (Optional.of(""));
    }
    return Optional.empty();
  }

  private static Optional<String> convertStackedLabel(StackedLabel in) {
    return convertLabelDTO(in.parentLabel());
  }
}
