/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.resource.mediator.f8;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capabilityprovider.mo.MoCapabilityProviderConfiguration;
import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.commondefinition.namerepresentation.NameDto;
import com.adva.nlms.commondefinition.namerepresentation.NameRepresentation;
import com.adva.nlms.mediation.config.f8.entity.protection.EcProtectionFreezeException;
import com.adva.nlms.mediation.config.f8.entity.protection.EcProtectionSwitchException;
import com.adva.nlms.mediation.config.f8.entity.protection.ProtectionGroupF8Service;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.CtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.resource.mediator.api.in.OperationFailedException;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.topology.manager.api.dto.NodeDto;
import com.adva.topology.manager.api.in.TopologyNodeApi;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class ProvisionAdapterTest {
  private final ProtectionGroupF8Service protectionGroupF8Service = mock();
  private final TopologyNodeApi topologyNodeApi = mock();
  private final Provision provision = mock();
  private final MoCapabilityProvider moCapabilityProvider =
    new MoCapabilityProviderConfiguration().moCapabilityProvider();
  private final CtpResources ctpResources = mock();
  private final PtpResources ptpResources = mock();
  private final ProvisionAdapter sut = new ProvisionAdapter(protectionGroupF8Service, topologyNodeApi, provision,
    moCapabilityProvider, ctpResources, ptpResources);

  private static final UUID NE_UUID = UUID.randomUUID();
  private static final int NE_ID = 42;
  private static final NodeDto node = new NodeDto(NE_UUID,
    List.of(new NameDto(NameRepresentation.USER_LABEL, "MyNode")), NE_ID, null, 0, null,
    null, null, null, null, null, null, null, null);
  private static final String PROTECTION_GROUP_IDX = "EC_CAT^/mit/me/1/eqh/shelf,1/eqh/slot,4/eq/card/prtgrp/traffic-1";
  private static final NetworkElementID NE_ID_WRAPPER = NetworkElementID.create(NE_ID);
  private static final EntityIndex PROTECTION_GROUP_ENTITY_INDEX = new EntityIndex(PROTECTION_GROUP_IDX);

  @BeforeEach
  void setUp() {
    when(topologyNodeApi.getNodeByUUID(NE_UUID)).thenReturn(node);
  }

  @Test
  void handleProtectionSwitch() throws OperationFailedException, EcProtectionSwitchException {
    sut.handleProtectionSwitch(NE_UUID, PROTECTION_GROUP_IDX);
    verify(protectionGroupF8Service, times(1))
      .switchProtection(NE_ID_WRAPPER, PROTECTION_GROUP_ENTITY_INDEX);
  }

  @Test
  void handleProtectionSwitch_nodeNotFound() {
    when(topologyNodeApi.getNodeByUUID(NE_UUID)).thenThrow(RuntimeException.class);
    assertThrows(OperationFailedException.class,
      () -> sut.handleProtectionSwitch(NE_UUID, PROTECTION_GROUP_IDX));
  }

  @Test
  void handleProtectionSwitch_switchFailed() throws EcProtectionSwitchException {
    doThrow(EcProtectionSwitchException.class)
      .when(protectionGroupF8Service).switchProtection(NE_ID_WRAPPER, PROTECTION_GROUP_ENTITY_INDEX);
    assertThrows(OperationFailedException.class,
      () -> sut.handleProtectionSwitch(NE_UUID, PROTECTION_GROUP_IDX));
  }

  @Test
  void handleSetAdminState_nodeNotFound() {
    when(topologyNodeApi.getNodeByUUID(NE_UUID)).thenThrow(RuntimeException.class);
    assertThrows(OperationFailedException.class,
      () -> sut.handleAdminStateOperation(NE_UUID, List.of("DEVICE"), null, AdminState.DOWN, LayerQualifier.OTSIMC));
  }

  @Test
  void handleSwitchAdminState() throws EcProtectionFreezeException, OperationFailedException {
    sut.handleAdminStateOperation(NE_UUID, List.of("DEVICE"), PROTECTION_GROUP_IDX, AdminState.MAINTENANCE, LayerQualifier.OTSIMC);
    verify(protectionGroupF8Service, times(1))
      .freezeProtectionGroup(any(), any());
    verify(provision).updateAdminStateByAid(NE_ID_WRAPPER, "DEVICE", AdminState.MAINTENANCE);
  }

  @Test
  void handleSwitchAdminStateOtsToMaintenance() throws EcProtectionFreezeException, OperationFailedException {
    sut.handleAdminStateOperation(NE_UUID, List.of("DEVICE"), PROTECTION_GROUP_IDX, AdminState.MAINTENANCE,
      LayerQualifier.OTS);
    verify(protectionGroupF8Service, times(1))
      .freezeProtectionGroup(any(), any());
    verify(provision).updateAdminStateByAid(NE_ID_WRAPPER, "DEVICE", AdminState.MAINTENANCE);
  }

  @Test
  void handleSwitchAdminStateOmsToMaintenance() throws EcProtectionFreezeException, OperationFailedException {
    sut.handleAdminStateOperation(NE_UUID, List.of("DEVICE"), PROTECTION_GROUP_IDX, AdminState.MAINTENANCE,
      LayerQualifier.OMS);
    verify(protectionGroupF8Service, times(1))
      .freezeProtectionGroup(any(), any());
    verify(provision).updateAdminStateByAid(NE_ID_WRAPPER, "DEVICE", AdminState.MAINTENANCE);
  }

  @Test
  void handleSwitchAdminStateOtsToDown() throws OperationFailedException {
    sut.handleAdminStateOperation(NE_UUID, List.of("DEVICE"), PROTECTION_GROUP_IDX, AdminState.DOWN,
      LayerQualifier.OTS);
    verifyNoInteractions(protectionGroupF8Service);
    verifyNoInteractions(provision);
  }

  @Test
  void handleSwitchAdminStateOmsToDown() throws OperationFailedException {
    sut.handleAdminStateOperation(NE_UUID, List.of("DEVICE"), PROTECTION_GROUP_IDX, AdminState.DOWN,
      LayerQualifier.OMS);
    verifyNoInteractions(protectionGroupF8Service);
    verifyNoInteractions(provision);
  }

  @Test
  void handleSwitchAdminStateForAlien() throws OperationFailedException {
    sut.handleAdminStateOperation(NE_UUID, List.of("alien", "otsia"), null, AdminState.DOWN,
      LayerQualifier.OTSIMC);
    verifyNoInteractions(protectionGroupF8Service);
    verify(provision).updateAdminStateByAid(NE_ID_WRAPPER, "otsia", AdminState.DOWN);
    verifyNoMoreInteractions(provision);
  }

  @Test
  void handleSwitchAdminStateOmsToDownWithTransponder() throws OperationFailedException {
    final var transponderCard = Optional.of(new CardRef(NE_ID, null, null, "T-MP-M8DCT", 0));
    final var roadmCard = Optional.of(new CardRef(NE_ID, null, null, "RD-12RS", 0));
    when(ptpResources.findCard(NE_ID, new Aid("transponder-ptp"))).thenReturn(transponderCard);
    when(ptpResources.findCard(NE_ID, new Aid("roadm-ptp"))).thenReturn(roadmCard);
    when(ctpResources.findCard(NE_ID, new Aid("transponder-ctp"))).thenReturn(transponderCard);
    when(ctpResources.findCard(NE_ID, new Aid("roadm-ctp"))).thenReturn(roadmCard);
    sut.handleAdminStateOperation(
      NE_UUID,
      List.of("transponder-ctp", "roadm-ctp", "transponder-ptp", "roadm-ptp"),
      null,
      AdminState.DOWN,
      LayerQualifier.OMS);
    verifyNoInteractions(protectionGroupF8Service);
    verify(provision).updateAdminStateByAid(NE_ID_WRAPPER, "transponder-ptp", AdminState.DOWN);
    verify(provision).updateAdminStateByAid(NE_ID_WRAPPER, "transponder-ctp", AdminState.DOWN);
    verifyNoMoreInteractions(provision);
  }

  @Test
  void handleSwitchAdminStateWhenFreezeProtectionGroup() throws EcProtectionFreezeException, OperationFailedException {
    sut.handleAdminStateOperation(NE_UUID, List.of("DEVICE"), PROTECTION_GROUP_IDX, AdminState.MAINTENANCE, LayerQualifier.OTSIMC);
    verify(protectionGroupF8Service, times(1))
      .freezeProtectionGroup(any(), any());
  }

  @Test
  void handleSwitchAdminStateWhenFreezeProtectionGroupFailed() throws EcProtectionFreezeException {
    doThrow(EcProtectionFreezeException.class)
      .when(protectionGroupF8Service).freezeProtectionGroup(NE_ID_WRAPPER, PROTECTION_GROUP_ENTITY_INDEX);
    assertThrows(OperationFailedException.class,
      () -> sut.handleAdminStateOperation(NE_UUID, List.of("DEVICE"), PROTECTION_GROUP_IDX, AdminState.MAINTENANCE, LayerQualifier.OTSIMC));
    verify(protectionGroupF8Service, times(1))
      .freezeProtectionGroup(NE_ID_WRAPPER, PROTECTION_GROUP_ENTITY_INDEX);
  }

  @Test
  void handleSwitchAdminStateWhenUnFreezeProtectionGroupFailed() throws EcProtectionFreezeException {
    doThrow(EcProtectionFreezeException.class)
      .when(protectionGroupF8Service).unFreezeProtectionGroup(NE_ID_WRAPPER, PROTECTION_GROUP_ENTITY_INDEX);
    assertThrows(OperationFailedException.class,
      () -> sut.handleAdminStateOperation(NE_UUID, List.of("DEVICE"), PROTECTION_GROUP_IDX, AdminState.UP, LayerQualifier.OTSIMC));
    verify(protectionGroupF8Service, times(1))
      .unFreezeProtectionGroup(NE_ID_WRAPPER, PROTECTION_GROUP_ENTITY_INDEX);
  }
}
