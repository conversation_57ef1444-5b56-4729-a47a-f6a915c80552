/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */
 
package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.provider.api.CapabilityProvider;
import com.adva.infrastructure.capabilityprovider.capabilities.CapabilityConfiguration;
import com.adva.infrastructure.capabilityprovider.core.CapabilityProviderConfiguration;
import com.adva.infrastructure.capabilityprovider.mo.MoCapabilityProviderConfiguration;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.topology.NEData;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnCardCapabilities;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnCardCapabilitiesImpl;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;


public class RRMOtnCardCapabilitiesTest {
  private static final int NE_ID = 1;
  private static final Aid CARD_AID = new Aid("Card-1-1");
  private static final Uri CARD_URI = new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card");

  private final NEDataProvider neDataProvider = Mockito.mock(NEDataProvider.class);
  private static final CapabilityProvider capabilityProvider = new CapabilityProviderConfiguration()
    .capabilityProvider(new CapabilityConfiguration().capabilityRepository(), null, null);
  private static final MoCapabilityProvider moCapabilityProvider = new MoCapabilityProviderConfiguration().moCapabilityProvider();

  private final RrmOtnCardCapabilities sut = new RrmOtnCardCapabilitiesImpl(neDataProvider, capabilityProvider, moCapabilityProvider);

  @BeforeEach
  void setUp() {
    Mockito.when(neDataProvider.getNeData(Mockito.anyInt()
      )).thenReturn(new NEData(NE_ID, 0,
      "FSP 3000C", null, null, "6.5.1")
    );
  }

  @Test
  void testcheckClientPortOnMFLex800() {
    // Given
    var ptpAid = new Aid("Port-1/1/p3");
    var plugAid = new Aid("Plug-1/1/p3");
    var plugUri = new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,3");
    var plugType = "QSFP28-112G-ER4F-SM-LC";
    var moduleType = "MF-M6MDT";
    var plugRef = PlugRef.builder()
      .setNeId(NE_ID)
      .setAid(plugAid)
      .setUri(plugUri)
      .setType(plugType)
      .build();
    var cardRef = new CardRef(NE_ID, CARD_AID, CARD_URI, moduleType, 0, "otnxp");
    // When
    var result = sut.isPortClient(NE_ID, cardRef, plugRef, ptpAid);
    // Then
    Assertions.assertTrue(result);
  }

  @Test
  void testcheckNetworkPortOnMFLex800() {
    // Given
    var ptpAid = new Aid("Port-1/1/p2");
    var plugAid = new Aid("Plug-1/1/p2");
    var plugUri = new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,2");
    var plugType = "QSFP56-DD-448G-OZRF-SM-LC";
    var moduleType = "MF-M6MDT";
    var plugRef = PlugRef.builder()
      .setNeId(NE_ID)
      .setAid(plugAid)
      .setUri(plugUri)
      .setType(plugType)
      .build();
    var cardRef = new CardRef(NE_ID, CARD_AID, CARD_URI, moduleType, 0, "otnxp");
    // When
    var result = sut.isPortClient(NE_ID, cardRef, plugRef, ptpAid);
    // Then
    Assertions.assertFalse(result);
  }

  @Test
  void testcheckNetworkPortOnOF1200() {
    // Given
    var ptpAid = new Aid("Port-1/1/n1");
    var plugAid = new Aid("Plug-1/1/n1");
    var plugUri = new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/nw,1");
    var plugType = "CFP2-448G-#DCTCG-SM-LC";
    var moduleType = "OF-2D16DCT";
    var plugRef = PlugRef.builder()
      .setNeId(NE_ID)
      .setAid(plugAid)
      .setUri(plugUri)
      .setType(plugType)
      .build();
    var cardRef = new CardRef(NE_ID, CARD_AID, CARD_URI, moduleType, 0);
    // When
    var result = sut.isPortClient(NE_ID, cardRef, plugRef, ptpAid);
    // Then
    Assertions.assertFalse(result);
  }

  @Test
  void testcheckClientkPortOnOF1200() {
    // Given
    var ptpAid = new Aid("Port-1/1/c10");
    var plugAid = new Aid("Plug-1/1/c10");
    var plugUri = new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,10");
    var plugType = "SFP+-11GU-C1270L-SM-LC";
    var moduleType = "OF-2D16DCT";
    var plugRef = PlugRef.builder()
      .setNeId(NE_ID)
      .setAid(plugAid)
      .setUri(plugUri)
      .setType(plugType)
      .build();
    var cardRef = new CardRef(NE_ID, CARD_AID, CARD_URI, moduleType, 0);
    // When
    var result = sut.isPortClient(NE_ID, cardRef, plugRef, ptpAid);
    // Then
    Assertions.assertTrue(result);
  }

  @Test
  void testGetPortSetDescriptors_withExclusiveGroups() {
    // Given
    var ptpAid = new Aid("Port-1/1/c2");
    var plugAid = new Aid("Plug-1/1/c2");
    var plugUri = new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,2");
    var plugType = "QSFP28-103G-PSM4-SM-MPO";
    var moduleType = "OF-2D16DCT";
    var plugRef = PlugRef.builder()
      .setNeId(NE_ID)
      .setAid(plugAid)
      .setUri(plugUri)
      .setType(plugType)
      .build();
    var cardRef = new CardRef(NE_ID, CARD_AID, CARD_URI, moduleType, 0);
    // When
    var result = sut.getPortSetDescriptors(NE_ID, cardRef, plugRef);
    // Then
    Assertions.assertEquals(2, result.size());
    Assertions.assertEquals("PortSetDescriptor[ports=[C2]]", result.get(0).toString());
    Assertions.assertEquals("PortSetDescriptor[ports=[C2-1, C2-2, C2-3, C2-4]]", result.get(1).toString());
  }

  @Test
  void testGetPortSetDescriptors_withNonExclusiveGroups() {
    // Given
    var ptpAid = new Aid("Port-1/1/c1");
    var plugAid = new Aid("Plug-1/1/c1");
    var plugUri = new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,1");
    var plugType = "SFP28-25G-SR-MM-LC-TWD";
    var moduleType = "OF-2D16DCT";
    var plugRef = PlugRef.builder()
      .setNeId(NE_ID)
      .setAid(plugAid)
      .setUri(plugUri)
      .setType(plugType)
      .build();
    var cardRef = new CardRef(NE_ID, CARD_AID, CARD_URI, moduleType, 0);
    // When
    var result = sut.getPortSetDescriptors(NE_ID, cardRef, plugRef);
    // Then
    Assertions.assertTrue(result.isEmpty());
  }

}
