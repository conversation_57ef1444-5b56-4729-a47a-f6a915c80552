/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm.adapters.messaging;

import com.adva.nlms.inf.api.PredefinedObserver;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.resource.advertisement.api.out.CrmToCpcConnection;
import com.adva.nlms.resource.crm.model.CrmCtrlTaskScheduler;
import com.adva.nlms.resource.mediator.f8.wdm.WdmResourceRequestMediator;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.EqualizeUseCases;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

@Configuration
@EnableAsync
class WdmRRMMessagingConfig {

  @Bean
  EqualizeEventsObserver equalizeEventsObserver(CrmCtrlTaskScheduler crmCtrlTaskScheduler, EqualizeUseCases equalizeUseCases, NEDataProvider neDataProvider) {
    return new EqualizeEventsObserver(crmCtrlTaskScheduler, equalizeUseCases, neDataProvider);
  }

  @Bean
  @PredefinedObserver
  TPOperationalStateObserver tpOperationalStateObserver(CrmToCpcConnection crmToCpcConnection, WdmResourceRequestMediator wdmResourceRequestMediator) {
    return new TPOperationalStateObserver(crmToCpcConnection, wdmResourceRequestMediator);
  }

  @Bean
  @PredefinedObserver
  RrmWdmNeObserver rrmWdmNeObserver(WdmResourceRequestMediator wdmResourceRequestMediator) {
    return new RrmWdmNeObserver(wdmResourceRequestMediator);
  }
}
