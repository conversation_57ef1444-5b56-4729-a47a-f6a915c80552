/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.resource.mediator.f8.events.CccProtectionGroupCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.provision.f8.api.in.CccpCreateDto;
import com.adva.nlms.resource.provision.f8.api.in.ObjectDoesNotExistException;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;

class CccProtectionGroupRequest implements ProtectionGroupRequest {
  private static final Logger log = LogManager.getLogger(CccProtectionGroupRequest.class);
  private final int neId;
  private final CccpCreateDto cccpCreateDto;
  private final String cccProtectionGroupName;
  private final Provision provisionApi;


  public CccProtectionGroupRequest(int neId, CccpCreateDto cccpCreateDto, Provision provisionApi) {
    this.neId = neId;
    this.provisionApi = provisionApi;
    this.cccpCreateDto = cccpCreateDto;
    this.cccProtectionGroupName = "";
  }

  public CccProtectionGroupRequest(int neId, String cccpName, Provision provisionApi) {
    this.neId = neId;
    this.provisionApi = provisionApi;
    this.cccpCreateDto = null;
    this.cccProtectionGroupName = cccpName;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (!(o instanceof CccProtectionGroupRequest)) return false;
    CccProtectionGroupRequest that = (CccProtectionGroupRequest) o;
    return neId == that.neId &&
      Objects.equals(cccProtectionGroupName, that.cccProtectionGroupName) &&
      Objects.equals(cccpCreateDto, that.cccpCreateDto);
  }

  @Override
  public int hashCode() {
    return Objects.hash(neId, cccpCreateDto, cccProtectionGroupName);
  }

  @Override
  public ProvisionLocalEvent provision() {
    if (exists()) {
      log.info("CCC-Protection group named={} already exists.", cccProtectionGroupName);
      return new CccProtectionGroupCreatedEvent(cccProtectionGroupName);
    }
    String cccpName = provisionApi.createCccpGroup(neId, cccpCreateDto);
    log.info("CCC-Protection group named={} created.", cccpName);
    return new CccProtectionGroupCreatedEvent(cccpName);
  }

  @Override
  public void delete() {
    log.info("Deleting CCC-Protection Group {}", cccProtectionGroupName);
    try {
      provisionApi.deleteCccpGroup(neId, cccProtectionGroupName);
    } catch (ObjectDoesNotExistException e) {
      log.warn("Could not delete CCC-Protection Group on NE: {} with name {} - entity does not exist", neId, cccProtectionGroupName);
    }
  }

  @Override
  public ProvisionLocalEvent adopt() {
    return null;
  }

  private boolean exists() { return !cccProtectionGroupName.isEmpty(); }

}
