/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.mediation.ec.model.aos.protection.ProtectionSwitchDirectionType;
import com.adva.nlms.mediation.mo.inventory.resources.PortIdExtractor;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.resource.mediator.f8.ProtectionSettings;
import com.adva.nlms.resource.mediator.f8.events.CtpLocalEvent;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.mediator.f8.events.SncLocalEvent;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegment;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionException;
import com.adva.nlms.resource.provision.f8.api.in.ResilienceDto;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Optional;
import java.util.stream.Stream;

class PrepareSncProtectionGroupRequest {
  private final Logger log = LogManager.getLogger(getClass());

  record RequestData(String sncUri, String workingCtpUri, String protectionCtpUri) {
  }

  private final int neId;
  private final OtnSegment workingSegment;
  private final OtnSegment protectionSegment;
  private final Provision provisionApi;
  private final ProtectionSettings protectionSettings;
  private final RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade;
  private final RrmOtnCardCapabilities rrmOtnCardCapabilities;


  PrepareSncProtectionGroupRequest(int neId, OtnSegment workingSegment, OtnSegment protectionSegment, ProtectionSettings protectionSettings, Provision provisionApi, RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade, RrmOtnCardCapabilities rrmOtnCardCapabilities) {
    this.neId = neId;
    this.workingSegment = workingSegment;
    this.protectionSegment = protectionSegment;
    this.protectionSettings = protectionSettings;
    this.provisionApi = provisionApi;
    this.rrmOtnDbResourcesFacade = rrmOtnDbResourcesFacade;
    this.rrmOtnCardCapabilities = rrmOtnCardCapabilities;
  }

  SncProtectionGroupRequest prepareRequest() throws ProvisionException {
    var clientCtpUri = findClientUri(workingSegment, protectionSegment).orElseThrow(
      () -> new ProvisionException("Could not create protection group, could not found client CTP for Protection Group Request")
    );

    var requestData = getRequestData(clientCtpUri.uri(), workingSegment, protectionSegment).orElseThrow(
      () -> new ProvisionException("Could not create protection group, no parameters defined in Protection Group Request.")
    );

    var existingProtectionGroup = rrmOtnDbResourcesFacade.findProtectionGroupBySnc(neId, new Uri(requestData.sncUri));
    if (existingProtectionGroup.isPresent()) {
      return new SncProtectionGroupRequest(neId, existingProtectionGroup.get().uri().uri(), provisionApi, true);
    }

    ResilienceDto resilienceDto = new ResilienceDto(
      !ProtectionSwitchDirectionType.UNIDIRECTIONAL.getNickName().equals(protectionSettings.direction()), // if not set send "true"/"bidir" as default
      protectionSettings.waitToRestore(),
      protectionSettings.revertive(),
      protectionSettings.holdOffTimer()
    );

    return new SncProtectionGroupRequest(neId,
      requestData.sncUri,
      requestData.workingCtpUri,
      requestData.protectionCtpUri,
      resilienceDto,
      findPortToDisable(clientCtpUri),
      provisionApi);
  }

  private String findPortToDisable(Uri clientCtpUri) {
    var ctp = rrmOtnDbResourcesFacade.findCtp(neId, clientCtpUri);
    var cardRef = rrmOtnDbResourcesFacade.findCardFromCtp(neId, clientCtpUri);
    var plugRef = rrmOtnDbResourcesFacade.findPlugFromCtp(neId, clientCtpUri);
    if (ctp.isPresent() && cardRef.isPresent() && plugRef.isPresent()) {
      String layer = clientCtpUri.uri().substring(clientCtpUri.uri().lastIndexOf('/') + 1).toUpperCase();
      var portIdentifier = rrmOtnCardCapabilities.getProtectionRestrictionPort(neId, cardRef.get(), plugRef.get(), plugRef.get().aid(), layer); // return e.g. "P5"
      var ptp = rrmOtnDbResourcesFacade.findPortFromCtp(neId, ctp.get().uri());
      if (portIdentifier.isPresent() && ptp.isPresent()) {
        return replacePortInUri(ptp.get().uri().uri(), PortIdExtractor.getMainPortId(plugRef.get().aid().aid()).toLowerCase(), portIdentifier.get().toLowerCase());
      }
      log.debug("Could not find PTP or portIdentifier for CTP: {}, Card: {}, Plug: {}; if this is not AccessFlex this should be fine", clientCtpUri.uri(), cardRef.get().aid(), plugRef.get().aid());
    }
    log.debug("Could not find CTP, Card or Plug for client CTP: {}, if this is not AccessFlex this should be fine", clientCtpUri.uri());
    return "";
  }

  private static String replacePortInUri(String ptp, String portToReplace, String newPort) {
    var replace = "ptrf," + portToReplace.replace("p", "");
    var target = "ptrf," + newPort.replace("p", "");
    return ptp.replace(replace, target);
  }

  private static Optional<RequestData> getRequestData(String clientCtpUri, OtnSegment working, OtnSegment protection) {
    var workingEvent = working.provisionEvents().stream()
      .filter(SncLocalEvent.class::isInstance)
      .map(SncLocalEvent.class::cast)
      .filter(event -> event.aEndpoints().contains(clientCtpUri) || event.zEndpoints().contains(clientCtpUri))
      .findAny();
    var protectionEvent = protection.provisionEvents().stream()
      .filter(SncLocalEvent.class::isInstance)
      .map(SncLocalEvent.class::cast)
      .filter(event -> event.aEndpoints().contains(clientCtpUri) || event.zEndpoints().contains(clientCtpUri))
      .findAny();
    if (workingEvent.isPresent() && protectionEvent.isPresent()) {
      var workingCtpUri = matchSncAndSegmentCtp(protectionEvent.get(), working);
      var protectionCtpUri = matchSncAndSegmentCtp(protectionEvent.get(), protection);
      if (workingCtpUri.isPresent() && protectionCtpUri.isPresent()) {
        return Optional.of(new RequestData(protectionEvent.get().uri(), workingCtpUri.get(), protectionCtpUri.get()));
      }
    }
    return Optional.empty();
  }

  private static Optional<String> matchSncAndSegmentCtp(SncLocalEvent event, OtnSegment segment) {
    Stream<String> endpoints = event.zEndpoints().size() > 1 ? event.zEndpoints().stream() : event.aEndpoints().stream();
    return endpoints
      .filter(networkCtp -> segment.provisionEvents().stream()
        .filter(CtpLocalEvent.class::isInstance)
        .anyMatch(provisionEvent -> provisionEvent.uri().equals(networkCtp)))
      .findFirst();
  }

  private static Optional<Uri> findClientUri(OtnSegment working, OtnSegment protection) {
    return working.provisionEvents().stream()
      .filter(CtpLocalEvent.class::isInstance)
      .filter(workingEvent -> protection.provisionEvents().stream()
        .anyMatch(protectionEvent -> (protectionEvent instanceof CtpLocalEvent)
          // cross is created between ctps on ODU layer, discard ctps on otu layer
          && workingEvent.uri().contains("odu")
          && workingEvent.uri().equals(protectionEvent.uri())))
      .map(ProvisionLocalEvent::uri)
      .findFirst()
      .map(Uri::new);
  }
}
