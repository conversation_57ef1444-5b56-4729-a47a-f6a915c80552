/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.mediator.f8.wdm.adapters.messaging;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.common.config.EquipmentState;
import com.adva.nlms.inf.mo.api.notification.MOCreationNotification;
import com.adva.nlms.inf.mo.api.notification.MONotification;
import com.adva.nlms.inf.mo.api.notification.MOUpdateNotification;
import com.adva.nlms.inf.mo.impl.notification.MoNotificationBuilder;
import com.adva.nlms.mediation.config.ec.entity.facility.ctp.CtpF8Attributes;
import com.adva.nlms.mediation.ec.support.EcEntityIndex;
import com.adva.nlms.mediation.mo.inventory.resources.OperationalState;
import com.adva.nlms.resource.advertisement.api.out.CrmToCpcConnection;
import com.adva.nlms.resource.mediator.f8.wdm.WdmResourceRequestMediator;
import com.adva.nlms.resource.mediator.f8.wdm.adapters.messaging.TPOperationalStateObserver;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TPOperationalStateObserverTest {

  private static final int NE_ID = 83;
  private static final String ENTITY_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1";
  private static final EntityIndex ENTITY_INDEX = EcEntityIndex.getEcEntityIndex(ENTITY_URI);

  @Mock
  private CrmToCpcConnection crmToCpcConnection;
  @Mock
  private WdmResourceRequestMediator wdmResourceRequestMediator;

  @InjectMocks
  private TPOperationalStateObserver sut;

  private void connectionIsClosed() {
    setConnectionForNe(false);
  }

  private void connectionIsOpen() {
    setConnectionForNe(true);
  }

  private void setConnectionForNe(boolean isOpen) {
    when(crmToCpcConnection.isConnectionOpenForNE(NE_ID)).thenReturn(isOpen);
  }

  @DisplayName("""
    GIVEN: connection for NE is open and MOCreationNotification
    WHEN: handle() method invoked
    THEN: notification processing is skipped
    """)
  @Test
  void handleConnectionIsOpenAndCreationNotificationArrived() {
    //given
    connectionIsOpen();
    MONotification notification = MoNotificationBuilder.builderOfType(MOCreationNotification.class)
      .neId(NE_ID)
      .build();
    //when
    sut.handle(notification);
    //then
    verifyNoInteractions(wdmResourceRequestMediator);
  }

  @DisplayName("""
    GIVEN: connection for NE is closed and MOUpdateNotification arrived""
    WHEN: handle() method invoked
    THEN: notification processing is skipped
    """)
  @Test
  void handleUpdateNotificationWhenConnectionToNeIsClosed() {
    //given
    connectionIsClosed();
    MONotification notification = MoNotificationBuilder.builderOfType(MOCreationNotification.class)
      .neId(NE_ID)
      .build();
    //when
    sut.handle(notification);
    //then
    verifyNoInteractions(wdmResourceRequestMediator);
  }

  @DisplayName("""
    GIVEN: connection for NE is open and MOUpdateNotification arrived
    WHEN: handle() method invoked
    THEN: notification processing is skipped
    """)
  @Test
  void handleConnectionIsOpenAndUpdateNotificationArrived() {
    //given
    connectionIsOpen();
    MONotification notification = MoNotificationBuilder.builderOfType(MOUpdateNotification.class)
      .neId(NE_ID)
      .build();
    //when
    sut.handle(notification);
    //then
    verifyNoInteractions(wdmResourceRequestMediator);
  }

  @DisplayName("""
    GIVEN: connection for NE is open and MOUpdateNotification with oper state change only arrived
    WHEN: handle() method invoked
    THEN: notification processing is skipped
    """)
  @Test
  void handleConnectionIsOpenAndUpdateNotificationWithOperStateChangeArrived() {
    //given
    connectionIsOpen();
    MONotification notification = MoNotificationBuilder.builderOfType(MOUpdateNotification.class)
      .neId(NE_ID)
      .entityIndex(ENTITY_INDEX)
      .addOldValue(CtpF8Attributes.ORIGINAL_OPERATIONAL_STATE, OperationalState.NORMAL.getDeviceVal())
      .addNewValue(CtpF8Attributes.ORIGINAL_OPERATIONAL_STATE, OperationalState.OUTAGE.getDeviceVal())
      .build();
    //when
    sut.handle(notification);
    //then
    verify(wdmResourceRequestMediator, times(1)).handleOperationalStateChange(anyInt(), anyString());
  }

  @DisplayName("""
    GIVEN: connection for NE is open and MOUpdateNotification with admin state change only arrived
    WHEN: handle() method invoked
    THEN: notification processing is skipped
    """)
  @Test
  void handleConnectionIsOpenAndUpdateNotificationWithAdminStateChangeArrived() {
    //given
    connectionIsOpen();
    MONotification notification = MoNotificationBuilder.builderOfType(MOUpdateNotification.class)
      .neId(NE_ID)
      .entityIndex(ENTITY_INDEX)
      .addOldValue(CtpF8Attributes.ADMIN_STATE, EquipmentState.IS.value())
      .addNewValue(CtpF8Attributes.ADMIN_STATE, EquipmentState.MT.value())
      .build();
    //when
    sut.handle(notification);
    //then
    verify(wdmResourceRequestMediator, times(1)).handleOperationalStateChange(anyInt(), anyString());
  }
}