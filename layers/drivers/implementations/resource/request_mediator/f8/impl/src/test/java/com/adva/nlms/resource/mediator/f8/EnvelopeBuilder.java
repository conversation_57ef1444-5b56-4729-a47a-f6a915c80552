/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8;

import ni.msg.EnvelopeOuterClass;
import ni.proto.segment_request.SegmentRequestOuterClass;

class EnvelopeBuilder {

  private String srcNeId;

  private String dstNeId;

  private SegmentRequestOuterClass.SegmentRequest segmentRequest;

  private EnvelopeBuilder() {
  }

  public static EnvelopeBuilder newEnvelopeBuilder() {
    return new EnvelopeBuilder();
  }

  public EnvelopeBuilder withSrcNeId(String srcNeId) {
    this.srcNeId = srcNeId;
    return this;
  }

  public EnvelopeBuilder withDstNeId(String dstNeId) {
    this.dstNeId = dstNeId;
    return this;
  }

  public EnvelopeBuilder withSegmentRequest(SegmentRequestOuterClass.SegmentRequest segmentRequest) {
    this.segmentRequest = segmentRequest;
    return this;
  }

  public EnvelopeOuterClass.Envelope build() {
    return EnvelopeOuterClass.Envelope.newBuilder()
      .setSource(EnvelopeOuterClass.Address.newBuilder()
        .setType(EnvelopeOuterClass.Application.CPM)
        .setId(EnvelopeOuterClass.Identifier.newBuilder()
          .setId(srcNeId)
        )
      )
      .setDestination(EnvelopeOuterClass.Address.newBuilder()
        .setType(EnvelopeOuterClass.Application.CPM_AGENT)
        .setId(EnvelopeOuterClass.Identifier.newBuilder()
          .setId(dstNeId)
        )
      )
      .setType(EnvelopeOuterClass.MessageType.SEGMENT_REQUEST)
      .setPayload(segmentRequest.toByteString())
      .build();

  }


}
