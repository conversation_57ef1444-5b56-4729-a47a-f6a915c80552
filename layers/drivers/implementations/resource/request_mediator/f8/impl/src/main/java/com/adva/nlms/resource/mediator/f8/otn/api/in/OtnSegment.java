/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.otn.api.in;

import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;

import java.util.Collections;
import java.util.List;

public record OtnSegment(SegmentID segmentID, List<ProvisionLocalEvent> provisionEvents,
                         ProtectionData protectionData) {

  public OtnSegment(SegmentID segmentID, List<ProvisionLocalEvent> provisionEvents) {
    this(segmentID, provisionEvents, null);
  }

  @Override
  public List<ProvisionLocalEvent> provisionEvents() {
    return Collections.unmodifiableList(provisionEvents);
  }
}
