/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.eth;


import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.CrossConnectResources;
import com.adva.nlms.mediation.mo.inventory.resources.Ctp;
import com.adva.nlms.mediation.mo.inventory.resources.CtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.CtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.PlugResources;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;

import java.util.List;
import java.util.Optional;
import java.util.Set;

class RrmEthDbResourcesFacadeImpl implements RrmEthDbResourcesFacade {
  private final CtpResources ctpResources;
  private final PlugResources plugResources;
  private final PtpResources ptpResources;
  private final CrossConnectResources crossConnectResources;

  public RrmEthDbResourcesFacadeImpl(CtpResources ctpResources, PlugResources plugResources, PtpResources ptpResources,
                                     CrossConnectResources crossConnectResources) {
    this.ctpResources = ctpResources;
    this.plugResources = plugResources;
    this.ptpResources = ptpResources;
    this.crossConnectResources = crossConnectResources;
  }

  @Override
  public Optional<CardRef> findCardFromCtp(int neId, Aid ctpAid) {
    return ctpResources.findCard(neId, mapCtpAid(ctpAid));
  }

  @Override
  public Optional<CardRef> findCardFromCtp(int neId, Uri uri) {
    return ctpResources.findCard(neId, uri);
  }

  @Override
  public Optional<CardRef> findCardFromPlug(int neId, Aid plugAid) {
    return plugResources.findCard(neId, plugAid);
  }

  @Override
  public PlugRef findPlug(int neId, Aid plugAid) {
    return plugResources.getPlug(neId, plugAid);
  }

  @Override
  public Optional<PlugRef> findPlugFromCtp(int neId, Aid ctpAid) {
    return ctpResources.findPlug(neId, ctpAid);
  }

  @Override
  public Optional<PlugRef> findPlugFromCtp(int neId, Uri ctpUri) {
    return ctpResources.findPlug(neId, ctpUri);
  }

  @Override
  public Optional<PtpRef> findPortFromCtp(int neId, Uri ctpUri) {
    return ctpResources.findPort(neId, ctpUri);
  }

  @Override
  public Optional<PtpRef> findPtp(int neId, Aid ptpAid) {
    return ptpResources.findPtp(neId, ptpAid);
  }

  @Override
  public Optional<PtpRef> findPtp(int neId, Uri uri) {
    return ptpResources.findPtp(neId, uri);
  }

  @Override
  public Optional<CtpRef> findCtp(int neId, Aid ctpAid) {
    return ctpResources.findCtp(neId, mapCtpAid(ctpAid));
  }

  @Override
  public Optional<Ctp> findCtpExtended(int neId, Aid ctpAid) { return ctpResources.findCtpExtended(neId, mapCtpAid(ctpAid)); }

  @Override
  public Optional<CtpRef> findCtp(int neId, Uri uri) {
    return ctpResources.findCtp(neId, uri);
  }

  @Override
  public Optional<Uri> findCtpUriByUriPrefixAndParams(int neId, String uriPrefix, int tp, List<Long> ts) {
    return ctpResources.getCtpUriByUriPrefixAndParams(neId, uriPrefix, tp, ts);
  }

  @Override
  public List<CtpRef> findAllCtpsFromPtp(int neId, Aid ptpAid) {
    return ptpResources.findAllCtps(neId, ptpAid);
  }

  @Override
  public Optional<Uri> findSncUriByUriPrefixAndEndpoints(int neId, String uriPrefix, Set<String> aEndpointURIs, Set<String> zEndpointURIs) {
    return crossConnectResources.getSncUriByUriPrefixAndEndpoints(neId, uriPrefix, aEndpointURIs, zEndpointURIs);
  }

  private Aid mapCtpAid(Aid aid) {
    String aidString = aid.aid();
    return new Aid(aidString.substring(0, aidString.indexOf("-")).toUpperCase()
        + aidString.substring(aidString.indexOf("-")));
  }
}
