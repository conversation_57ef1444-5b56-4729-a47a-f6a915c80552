/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm.api.in;

import com.adva.nlms.resource.mediator.f8.PowerEqOperation;

import java.util.Objects;

public class WdmSegment {
  private final int neId;
  private final String segReqId;
  private final CrmNodePosition nodePosition;
  private final String aidSrcTp;
  private final String aidDstTp;
  private final int channelLabel;
  private final boolean isBandwidthExplicitlySet;
  private final int forwardSequenceNumber;
  private final int reverseSequenceNumber;
  private final String ttpAid;
  private final SegmentPowerEqualization segmentPowerEqualization;

  private int slcId;
  private String alienWaveCtpUri;
  private String protectionGroupUri;
  private String transponderCtpUri;

  private WdmSegment(Builder builder) {
    this.neId = builder.neId;
    this.slcId = builder.slcId;
    this.segReqId = builder.segmentRequestId;
    this.nodePosition = builder.nodePosition;
    this.aidSrcTp = builder.aidSrcTp;
    this.aidDstTp = builder.aidDstTp;
    this.channelLabel = builder.channelLabel;
    this.transponderCtpUri = builder.transponderCtpUri;
    this.alienWaveCtpUri = builder.alienWaveCtpUri;
    this.isBandwidthExplicitlySet = builder.isBandwidthExplicitlySet;
    this.forwardSequenceNumber = builder.forwardSequenceNumber;
    this.reverseSequenceNumber = builder.reverseSequenceNumber;
    this.ttpAid = builder.ttpAid;
    this.protectionGroupUri = builder.protectionGroupUri;
    this.segmentPowerEqualization = builder.segmentPowerEqualization;
    if (builder.aEndEqExpected) {
      this.setEqExpected(0, true);
    }
  }

  public static WdmSegment createWdmSegment(String segmentRequestId, WdmSegment other) {
    return newSegment()
      .withNeId(other.neId)
      .withSegmentRequestId(segmentRequestId)
      .withSlcId(other.slcId)
      .withNodePosition(other.nodePosition)
      .withAidSrcTp(other.aidSrcTp)
      .withAidDstTp(other.aidDstTp)
      .withChannelLabel(other.channelLabel)
      .withTransponderCtpUri(other.transponderCtpUri)
      .withAlienWaveCtpUri(other.alienWaveCtpUri)
      .withIsBandwidthExplicitlySet(other.isBandwidthExplicitlySet)
      .withForwardSequenceNumber(other.forwardSequenceNumber)
      .withReverseSequenceNumber(other.reverseSequenceNumber)
      .withTtpAid(other.ttpAid)
      .withSegmentPowerEqualization(other.segmentPowerEqualization).build();
  }


  public boolean hasSlcId(int slcId) {
    return this.slcId == slcId;
  }

  public boolean hasProtectionGroupUri(String uri) {
    return Objects.equals(protectionGroupUri, uri);
  }

  public boolean isAgatesInterfaceAEnd(String agatesInterface) {
    return segmentPowerEqualization.isAgatesInterfaceAEnd(agatesInterface);
  }

  public boolean isAgatesInterface(String agatesInterface) {
    return segmentPowerEqualization.isAgatesInterface(agatesInterface);
  }

  public boolean isEqExpected(boolean isAEnd) {
    return segmentPowerEqualization.isEqExpected(isAEnd);
  }

  public boolean isLatestNotification(long eqTime, boolean isAEnd) {
    return segmentPowerEqualization.isLatestNotification(eqTime, isAEnd);
  }

  public void resetSlcId() {
    this.slcId = 0;
  }

  public void clearDataPlaneReferences() {
    this.slcId = 0;
    this.transponderCtpUri = null;
    this.alienWaveCtpUri = null;
  }

  public void clearAgatesInterface(boolean isAEnd) {
    segmentPowerEqualization.setAgatesInterface("", isAEnd);
  }

  public void clearEqualizationData() {
    segmentPowerEqualization.clearEqualizationData();
  }

  public void setWaitingForLaserOnDelayClear(PowerEqOperation waitingForLaserOnDelayClear) {
    segmentPowerEqualization.setWaitingForLaserOnDelayClear(waitingForLaserOnDelayClear);
  }

  public PowerEqOperation getWaitingForLaserOnDelayClear() {
    return segmentPowerEqualization.getWaitingForLaserOnDelayClear();
  }

  public EnvelopeIncomingAddresses getEnvelopeIncomingAddresses() {
    return segmentPowerEqualization.getEnvelopeIncomingAddresses();
  }

  public void setAgatesInterface(String agatesInterface, boolean isAEnd) {
    segmentPowerEqualization.setAgatesInterface(agatesInterface, isAEnd);
  }

  public void setEqExpected(long eqInvocationTime, boolean isAEnd) {
    segmentPowerEqualization.setEqExpected(eqInvocationTime, isAEnd);
  }

  public void unsetEqExpected(boolean isAEnd) {
    segmentPowerEqualization.unsetEqExpected(isAEnd);
  }

  public void setCorrelationIdFromContext() {
    segmentPowerEqualization.setCorrelationIdFromContext();
  }

  public void putCorrelationIdInContext() {
    segmentPowerEqualization.putCorrelationIdInContext();
  }

  public void setProtectionGroupUri(String protectionGroupUri) {
    this.protectionGroupUri = protectionGroupUri;
  }

  public void setEnvelopeIncomingAddresses(EnvelopeIncomingAddresses envelopeIncomingAddresses) {
    segmentPowerEqualization.setEnvelopeIncomingAddresses(envelopeIncomingAddresses);
  }

  public static Builder newSegment() {
    return new Builder();
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    WdmSegment that = (WdmSegment) o;
    return neId == that.neId && Objects.equals(segReqId, that.segReqId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(neId, segReqId);
  }

  @Override
  public String toString() {
    return "WdmSegment{" +
      "neId=" + neId +
      ", slcId=" + slcId +
      ", segReqId='" + segReqId + '\'' +
      ", nodePosition=" + nodePosition +
      ", aidSrcTp='" + aidSrcTp + '\'' +
      ", aidDstTp='" + aidDstTp + '\'' +
      ", transponderCtpUri='" + transponderCtpUri + '\'' +
      ", alienWaveCtpUri='" + alienWaveCtpUri + '\'' +
      ", channelLabel=" + channelLabel +
      ", isBandwidthExplicitlySet=" + isBandwidthExplicitlySet +
      ", forwardSequenceNumber=" + forwardSequenceNumber +
      ", reverseSequenceNumber=" + reverseSequenceNumber +
      ", ttpAid='" + ttpAid + '\'' +
      ", protectionGroupUri='" + protectionGroupUri + '\'' +
      ", segmentPowerEqualization=" + segmentPowerEqualization +
      '}';
  }

  public int getNeId() {
    return neId;
  }

  public String getSegReqId() {
    return segReqId;
  }

  public CrmNodePosition getNodePosition() {
    return nodePosition;
  }

  public boolean isBandwidthExplicitlySet() {
    return isBandwidthExplicitlySet;
  }

  public String getTtpAid() {
    return ttpAid;
  }

  public int getSlcId() {
    return slcId;
  }

  public String getAlienWaveCtpUri() {
    return alienWaveCtpUri;
  }

  public String getProtectionGroupUri() {
    return protectionGroupUri;
  }

  public String getTransponderCtpUri() {
    return transponderCtpUri;
  }

  public String getAidSrcTp() {
    return aidSrcTp;
  }

  public int getChannelLabel() {
    return channelLabel;
  }

  public int getForwardSequenceNumber() {
    return forwardSequenceNumber;
  }

  public int getReverseSequenceNumber() {
    return reverseSequenceNumber;
  }

  public String getAidDstTp() {
    return aidDstTp;
  }

  public static class Builder {

    private int neId;
    private String segmentRequestId;
    private int slcId;
    private CrmNodePosition nodePosition;
    private String aidSrcTp;
    private String aidDstTp;
    private int channelLabel;
    private String transponderCtpUri;
    private String alienWaveCtpUri;
    private boolean isBandwidthExplicitlySet;
    private int forwardSequenceNumber;
    private int reverseSequenceNumber;
    private String ttpAid;
    private String protectionGroupUri;
    boolean aEndEqExpected;
    private SegmentPowerEqualization segmentPowerEqualization = new SegmentPowerEqualization();

    private Builder() {
    }

    public Builder withNeId(int neId) {
      this.neId = neId;
      return this;
    }

    public Builder withSegmentRequestId(String segReqId) {
      this.segmentRequestId = segReqId;
      return this;
    }

    public Builder withSlcId(int slcId) {
      this.slcId = slcId;
      return this;
    }

    public Builder withNodePosition(CrmNodePosition nodePosition) {
      this.nodePosition = nodePosition;
      return this;
    }

    public Builder withAidSrcTp(String aidSrcTp) {
      this.aidSrcTp = aidSrcTp;
      return this;
    }

    public Builder withAidDstTp(String aidDstTp) {
      this.aidDstTp = aidDstTp;
      return this;
    }

    public Builder withChannelLabel(int channelLabel) {
      this.channelLabel = channelLabel;
      return this;
    }

    public Builder withTransponderCtpUri(String uri) {
      this.transponderCtpUri = uri;
      return this;
    }

    public Builder withAlienWaveCtpUri(String uri) {
      this.alienWaveCtpUri = uri;
      return this;
    }

    public Builder withIsBandwidthExplicitlySet(boolean flag) {
      this.isBandwidthExplicitlySet = flag;
      return this;
    }

    public Builder withForwardSequenceNumber(int num) {
      this.forwardSequenceNumber = num;
      return this;
    }

    public Builder withReverseSequenceNumber(int num) {
      this.reverseSequenceNumber = num;
      return this;
    }

    public Builder withTtpAid(String ttpAid) {
      this.ttpAid = ttpAid;
      return this;
    }

    public Builder withProtectionGroupUri(String uri) {
      this.protectionGroupUri = uri;
      return this;
    }

    public Builder withSegmentPowerEqualization(SegmentPowerEqualization eq) {
      this.segmentPowerEqualization = eq;
      return this;
    }

    public Builder withAendEqExpected(boolean aEndEqExpected) {
      this.aEndEqExpected = aEndEqExpected;
      return this;
    }

    public WdmSegment build() {
      return new WdmSegment(this);
    }
  }
}