/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.provider.api.CapabilityProvider;
import com.adva.infrastructure.capabilityprovider.capabilities.CapabilityConfiguration;
import com.adva.infrastructure.capabilityprovider.core.CapabilityProviderConfiguration;
import com.adva.infrastructure.capabilityprovider.mo.MoCapabilityProviderConfiguration;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.LineProtectionGroupRef;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.resource.mediator.f8.AssignRoles;
import com.adva.nlms.resource.mediator.f8.ProtectionSettings;
import com.adva.nlms.resource.mediator.f8.events.CtpAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.CtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.mediator.f8.events.SncAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncModifyEvent;
import com.adva.nlms.resource.mediator.f8.internalpath.ForkPlacement;
import com.adva.nlms.resource.mediator.f8.otn.PrepareProtectionGroupRequest;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnCardCapabilities;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnCardCapabilitiesImpl;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnDbResourcesFacade;
import com.adva.nlms.resource.mediator.f8.otn.SncProtectionGroupRequest;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegment;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegmentRepository;
import com.adva.nlms.resource.mediator.f8.otn.api.in.ProtectionData;
import com.adva.nlms.resource.mediator.f8.otn.api.in.SegmentID;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.nlms.resource.provision.f8.api.in.ResilienceDto;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.mockito.Mockito.mock;

public class PrepareSncProtectionGroupOf1200Test {

  private final int neId = 42;
  private final String workingLeg = "working";
  private final String protectionLeg = "protection";
  private final ProtectionSettings protectionSettings = new ProtectionSettings(null, null, null, null);
  private final AssignRoles assignRoles = new AssignRoles(workingLeg, protectionLeg, protectionSettings);
  private final Provision provisionApi = Mockito.mock(Provision.class);
  private final OtnSegmentRepository otnSegmentRepository = Mockito.mock(OtnSegmentRepository.class);
  private final RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade = Mockito.mock(RrmOtnDbResourcesFacade.class);
  private final NEDataProvider neDataProvider = mock(NEDataProvider.class);
  private static final CapabilityProvider capabilityProvider = new CapabilityProviderConfiguration()
    .capabilityProvider(new CapabilityConfiguration().capabilityRepository(), null, null);
  private static final MoCapabilityProvider moCapabilityProvider = new MoCapabilityProviderConfiguration().moCapabilityProvider();
  private final RrmOtnCardCapabilities rrmOtnCardCapabilities = new RrmOtnCardCapabilitiesImpl(neDataProvider, capabilityProvider, moCapabilityProvider);
  private final String clientOTUUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-3/ctp/otu2";
  private final String clientODUUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-3/ctp/otu2/ctp/odu2";
  private final String network1Uri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/nw,1/ctp/otuc3/ctp/oduc3/ctp/odu2-1";
  private final String network2Uri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/nw,2/ctp/otuc2/ctp/oduc2/ctp/odu2-1";
  private final String sncUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/sn/odu2/snc/1";
  private final Uri sncUriUri = new Uri(sncUri);
  private final SegmentID workingSegmentId = new SegmentID(neId, workingLeg);
  private final SegmentID protectionSegmentId = new SegmentID(neId, protectionLeg);
  private final String protectionGroupAid = "Protection-2/1/odu2-1";

  private final PrepareProtectionGroupRequest sut = new PrepareProtectionGroupRequest(
    neId, assignRoles, provisionApi, otnSegmentRepository, rrmOtnDbResourcesFacade, rrmOtnCardCapabilities);

  @Test
  void testCreate() {
    ResilienceDto resilienceDto = new ResilienceDto(true, null, null, null);
    List<ProvisionLocalEvent> workingEvents = List.of(
      new CtpCreatedEvent(clientOTUUri),
      new CtpCreatedEvent(clientODUUri),
      new CtpCreatedEvent(network1Uri),
      new SncCreatedEvent(sncUri, Set.of(clientODUUri), Set.of(network1Uri))
    );
    OtnSegment working = new OtnSegment(new SegmentID(neId, workingLeg), workingEvents, new ProtectionData(ForkPlacement.DESTINATION));
    List<ProvisionLocalEvent> protectionEvents = List.of(
      new CtpAdoptedEvent(clientOTUUri),
      new CtpAdoptedEvent(clientODUUri),
      new CtpCreatedEvent(network2Uri),
      new SncModifyEvent(sncUri, Set.of(clientODUUri), Set.of(network1Uri, network2Uri))
    );
    OtnSegment protection = new OtnSegment(new SegmentID(neId, protectionLeg), protectionEvents, new ProtectionData(ForkPlacement.DESTINATION));
    Mockito.when(
      otnSegmentRepository.findOne(workingSegmentId)
    ).thenReturn(
      working
    );
    Mockito.when(
      otnSegmentRepository.findOne(protectionSegmentId)
    ).thenReturn(
      protection
    );
    Mockito.when(
      rrmOtnDbResourcesFacade.findProtectionGroupBySnc(neId, sncUriUri)
    ).thenReturn(
    Optional.empty()
    );
    SncProtectionGroupRequest sncProtectionGroupRequest = new SncProtectionGroupRequest(neId, sncUri, network1Uri, network2Uri, resilienceDto, "", provisionApi);
    Assertions.assertEquals(sncProtectionGroupRequest, sut.prepareRequest());
  }

  @Test
  void testAdoptProtectionGroup() {
    List<ProvisionLocalEvent> workingEvents = List.of(
      new CtpAdoptedEvent(clientOTUUri),
      new CtpAdoptedEvent(clientODUUri),
      new CtpAdoptedEvent(network1Uri),
      new SncAdoptedEvent(sncUri, Set.of(clientODUUri), Set.of(network1Uri))
    );
    OtnSegment working = new OtnSegment(new SegmentID(neId, workingLeg), workingEvents, new ProtectionData(ForkPlacement.DESTINATION));
    List<ProvisionLocalEvent> protectionEvents = List.of(
      new CtpAdoptedEvent(clientOTUUri),
      new CtpAdoptedEvent(clientODUUri),
      new CtpAdoptedEvent(network2Uri),
      new SncAdoptedEvent(sncUri, Set.of(clientODUUri), Set.of(network1Uri, network2Uri))
    );
    OtnSegment protection = new OtnSegment(new SegmentID(neId, protectionLeg), protectionEvents, new ProtectionData(ForkPlacement.DESTINATION));
    Mockito.when(
      otnSegmentRepository.findOne(workingSegmentId)
    ).thenReturn(
      working
    );
    Mockito.when(
      otnSegmentRepository.findOne(protectionSegmentId)
    ).thenReturn(
      protection
    );
    Mockito.when(
      rrmOtnDbResourcesFacade.findProtectionGroupBySnc(neId, sncUriUri)
    ).thenReturn(
      Optional.of(new LineProtectionGroupRef(neId,
        new Aid(protectionGroupAid), new Uri(protectionGroupAid),
        new Aid(network1Uri), new Uri(network1Uri),
        new Aid(network2Uri), new Uri(network2Uri),
        new Aid(sncUri), sncUriUri,
        null, null, null, null))
    );
    SncProtectionGroupRequest sncProtectionGroupRequest = new SncProtectionGroupRequest(neId, protectionGroupAid, provisionApi, true);
    Assertions.assertEquals(sncProtectionGroupRequest, sut.prepareRequest());
  }

}
