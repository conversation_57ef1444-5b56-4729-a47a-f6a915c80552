/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.mediation.mo.inventory.f8.events.AGATESDoneResultType;
import com.adva.nlms.resource.mediator.f8.NiCallbackSender;
import com.adva.nlms.resource.mediator.f8.wdm.EqlzSpanNotificationHandler;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.WdmSegment;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.Mockito;

import static org.mockito.Mockito.verify;

public class EqlzSpanNotificationHandlerTest {

    private static final String TEST_OMS_CTP = "testOmsCtp";

    @Test
    public void testHandleSpanEqResultIsNull() {
        WdmSegment segmentMock = Mockito.mock();
        NiCallbackSender niCallbackSender = Mockito.mock();
        var eqlzSpanNotificationHandler = new EqlzSpanNotificationHandler(niCallbackSender, segmentMock, true, null, TEST_OMS_CTP);

        eqlzSpanNotificationHandler.handle();

        verify(segmentMock).setAgatesInterface(TEST_OMS_CTP, true);
    }

    @ParameterizedTest
    @EnumSource(value = AGATESDoneResultType.class, names = {"Success", "SuccessAbnormal"})
    public void testHandleSpanEqResultIsSuccess(AGATESDoneResultType agatesDoneResult) {
        WdmSegment segmentMock = Mockito.mock();
        NiCallbackSender niCallbackSender = Mockito.mock();
        var eqlzSpanNotificationHandler = new EqlzSpanNotificationHandler(niCallbackSender, segmentMock, true, agatesDoneResult, TEST_OMS_CTP);

        eqlzSpanNotificationHandler.handle();

        verify(segmentMock).clearAgatesInterface(true);
        verify(niCallbackSender, Mockito.times(1)).sendEqualizationSuccess(Mockito.any(),Mockito.any());
    }

    @ParameterizedTest
    @EnumSource(value = AGATESDoneResultType.class, names = {"Unavailable","Fail","FailTimeout","UnavailableAdmin"})
    public void testHandleSpanEqResultIsNotSuccess(AGATESDoneResultType agatesDoneResult) {
        WdmSegment segmentMock = Mockito.mock();
        NiCallbackSender niCallbackSender = Mockito.mock();
        var eqlzSpanNotificationHandler = new EqlzSpanNotificationHandler(niCallbackSender, segmentMock, true, agatesDoneResult, TEST_OMS_CTP);

        eqlzSpanNotificationHandler.handle();

        verify(segmentMock).clearAgatesInterface(true);
        verify(niCallbackSender, Mockito.times(1)).sendEqualizationFailure(Mockito.anyString(),Mockito.any(),Mockito.any());
    }

}