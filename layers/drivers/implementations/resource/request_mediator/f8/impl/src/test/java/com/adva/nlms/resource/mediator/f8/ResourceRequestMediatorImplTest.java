/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8;

import com.adva.nlms.resource.crm.model.CrmCtrlTaskScheduler;
import com.adva.nlms.resource.mediator.f8.eth.EthResourceRequestMediator;
import com.adva.nlms.resource.mediator.f8.otn.OtnResourceRequestMediator;
import com.adva.nlms.resource.mediator.f8.wdm.SegmentRequestOperationRet;
import com.adva.nlms.resource.mediator.f8.wdm.WdmResourceRequestMediator;
import ni.msg.EnvelopeOuterClass;
import ni.proto.segment_request.SegmentRequestOuterClass;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ResourceRequestMediatorImplTest {
  CrmCtrlTaskScheduler crmCtrlTaskScheduler = new CrmCtrlTaskScheduler() {
    @Override
    public void initThreadPools() {
    }

    @Override
    public void submitProvisioningTask(int neId, Runnable task) {
      task.run();
    }

    @Override
    public Future<?> submitTask(Runnable task) {
      task.run();
      return null;
    }

    @Override
    public Future<?> scheduleTask(Runnable task, long delay, TimeUnit unit) {
      return null;
    }

    @Override
    public void schedulePeriodicTask(Runnable task, long period, TimeUnit unit) {
    }
  };

  private final WdmResourceRequestMediator wdmRRM = mock();
  private final OtnResourceRequestMediator otnRRM = mock();
  private final EthResourceRequestMediator ethRRM = mock();
  private final NiCallbackSender niCallbackSender = mock();
  private final ProvisionAdapter provisionAdapter = mock();
  private final ResourceRequestMediatorImpl mediator = new ResourceRequestMediatorImpl(
    wdmRRM, otnRRM, ethRRM, niCallbackSender, crmCtrlTaskScheduler, provisionAdapter);

  @Test
  public void testHandleSegmentRequestInTask() {
    EnvelopeOuterClass.Envelope envelope = mock(EnvelopeOuterClass.Envelope.class);
    mediator.handleSegmentRequestInTask(envelope);
    verifyNoInteractions(wdmRRM);
  }

  @Test
  public void testHandleAssignRolesRequestSuccess() {
    var workingLegId = "workingLegId";
    var protectionLegId = "protectionLegId";
    var assignRoles = SegmentRequestOuterClass.AssignRoles.newBuilder();
    assignRoles.setWorkingLegId(workingLegId);
    assignRoles.setProtectionLegId(protectionLegId);

    var segmentRequest = SegmentRequestOuterClass.SegmentRequest.newBuilder()
      .setAssignRoles(assignRoles)
      .build();

    EnvelopeOuterClass.Envelope envelope = EnvelopeBuilder.newEnvelopeBuilder()
      .withSrcNeId("NI_host_1/CPM")
      .withDstNeId("230823")
      .withSegmentRequest(segmentRequest)
      .build();

    CrmSegmentRequestDto expectedSegmentDto = new CrmSegmentRequestDto();
    expectedSegmentDto.neId = 230823;
    expectedSegmentDto.requestType = CrmSegmentRequestDto.RequestType.ASSIGN_ROLES;
    expectedSegmentDto.assignRoles = new AssignRoles(workingLegId, protectionLegId, null);

    when(wdmRRM.handleSegmentRequest(expectedSegmentDto, null)).thenReturn(new SegmentRequestOperationRet(true, null));

    mediator.handleSegmentRequest(envelope);

    verify(wdmRRM, times(1)).handleSegmentRequest(expectedSegmentDto, null);

    verify(niCallbackSender, times(1)).sendAssignRolesSuccess(
      expectedSegmentDto.assignRoles.workingLegId(), expectedSegmentDto.assignRoles.protectionLegId(),
      envelope.getSource(), envelope.getDestination());
  }

  @Test
  public void testHandleAssignRolesRequestFailure() {
    var workingLegId = "workingLegId";
    var protectionLegId = "protectionLegId";
    var assignRoles = SegmentRequestOuterClass.AssignRoles.newBuilder();
    assignRoles.setWorkingLegId(workingLegId);
    assignRoles.setProtectionLegId(protectionLegId);

    var segmentRequest = SegmentRequestOuterClass.SegmentRequest.newBuilder()
      .setAssignRoles(assignRoles)
      .build();

    EnvelopeOuterClass.Envelope envelope = EnvelopeBuilder.newEnvelopeBuilder()
      .withSrcNeId("NI_host_1/CPM")
      .withDstNeId("230823")
      .withSegmentRequest(segmentRequest)
      .build();

    CrmSegmentRequestDto expectedSegmentDto = new CrmSegmentRequestDto();
    expectedSegmentDto.neId = 230823;
    expectedSegmentDto.requestType = CrmSegmentRequestDto.RequestType.ASSIGN_ROLES;
    expectedSegmentDto.assignRoles = new AssignRoles(workingLegId, protectionLegId, null);

    RuntimeException expectedException = new RuntimeException();
    when(wdmRRM.handleSegmentRequest(expectedSegmentDto, null)).thenThrow(expectedException);

    mediator.handleSegmentRequest(envelope);

    verify(wdmRRM, times(1)).handleSegmentRequest(expectedSegmentDto, null);

    verify(niCallbackSender, times(1)).sendAssignRolesFailure(
      expectedException.getMessage(), expectedSegmentDto.assignRoles.workingLegId(),
      expectedSegmentDto.assignRoles.protectionLegId(), envelope.getSource(), envelope.getDestination());
  }

  @Test
  public void testHandleSegmentRequestSuccess() {

    SegmentRequestOuterClass.SegmentRequest segmentRequest = SegmentRequestBuilder
      .newBuilder()
      .withSrcTerminationPoint("Port-10/1/n1")
      .withDstTerminationPoint("Port-10/1/n1")
      .build();

    EnvelopeOuterClass.Envelope envelope = EnvelopeBuilder.newEnvelopeBuilder()
      .withSrcNeId("NI_host_1/CPM")
      .withDstNeId("230823")
      .withSegmentRequest(segmentRequest)
      .build();

    CrmSegmentRequestDto expectedSegmentDto = new CrmSegmentRequestDto();
    expectedSegmentDto.id = segmentRequest.getOperation().getId();
    expectedSegmentDto.neId = 230823;
    expectedSegmentDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    List<String> deletedSegments = List.of();

    when(wdmRRM.handleSegmentRequest(expectedSegmentDto, null)).thenReturn(new SegmentRequestOperationRet(true, null));

    mediator.handleSegmentRequest(envelope);

    verify(wdmRRM, times(1)).buildSegmentRequestContext(expectedSegmentDto);
    verify(wdmRRM, times(1)).handleSegmentRequest(expectedSegmentDto, null);

    verify(niCallbackSender, times(1)).sendSuccess(
      expectedSegmentDto.id, expectedSegmentDto.requestType,
      envelope.getSource(), envelope.getDestination(), deletedSegments);
  }

  @Test
  public void testHandleSegmentBuildContextFailure() {

    SegmentRequestOuterClass.SegmentRequest segmentRequest = SegmentRequestBuilder
      .newBuilder()
      .withSrcTerminationPoint("Port-10/1/n1")
      .withDstTerminationPoint("Port-10/1/n1")
      .build();

    EnvelopeOuterClass.Envelope envelope = EnvelopeBuilder.newEnvelopeBuilder()
      .withSrcNeId("NI_host_1/CPM")
      .withDstNeId("230823")
      .withSegmentRequest(segmentRequest)
      .build();

    CrmSegmentRequestDto expectedSegmentDto = new CrmSegmentRequestDto();
    expectedSegmentDto.id = segmentRequest.getOperation().getId();
    expectedSegmentDto.neId = 230823;
    expectedSegmentDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;

    RuntimeException expectedException = new RuntimeException();
    when(wdmRRM.buildSegmentRequestContext(expectedSegmentDto)).thenThrow(expectedException);

    mediator.handleSegmentRequest(envelope);

    verify(wdmRRM, times(1)).buildSegmentRequestContext(expectedSegmentDto);
    verify(niCallbackSender, times(1)).sendFailure(
      expectedSegmentDto.id, expectedSegmentDto.requestType, expectedException,
      envelope.getSource(), envelope.getDestination(), List.of());
  }

  @Test
  public void testHandleSegmentRequestFailure() {

    SegmentRequestOuterClass.SegmentRequest segmentRequest = SegmentRequestBuilder
      .newBuilder()
      .withSrcTerminationPoint("Port-10/1/n1")
      .withDstTerminationPoint("Port-10/1/n1")
      .build();

    EnvelopeOuterClass.Envelope envelope = EnvelopeBuilder.newEnvelopeBuilder()
      .withSrcNeId("NI_host_1/CPM")
      .withDstNeId("230823")
      .withSegmentRequest(segmentRequest)
      .build();

    CrmSegmentRequestDto expectedSegmentDto = new CrmSegmentRequestDto();
    expectedSegmentDto.id = segmentRequest.getOperation().getId();
    expectedSegmentDto.neId = 230823;
    expectedSegmentDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;

    RuntimeException expectedException = new RuntimeException();
    when(wdmRRM.handleSegmentRequest(expectedSegmentDto, null)).thenThrow(expectedException);

    mediator.handleSegmentRequest(envelope);

    verify(wdmRRM, times(1)).buildSegmentRequestContext(expectedSegmentDto);
    verify(wdmRRM, times(1)).handleSegmentRequest(expectedSegmentDto, null);
    verify(niCallbackSender, times(1)).sendFailure(
      expectedSegmentDto.id, expectedSegmentDto.requestType, expectedException,
      envelope.getSource(), envelope.getDestination(), List.of());
  }

  @Test
  public void testHandleSegmentRequestFailureWithMutableSegments() {

    SegmentRequestOuterClass.SegmentRequest segmentRequest = SegmentRequestBuilder
      .newBuilder()
      .withSrcTerminationPoint("Port-10/1/n1")
      .withDstTerminationPoint("Port-10/1/n1")
      .build();

    EnvelopeOuterClass.Envelope envelope = EnvelopeBuilder.newEnvelopeBuilder()
      .withSrcNeId("NI_host_1/CPM")
      .withDstNeId("230823")
      .withSegmentRequest(segmentRequest)
      .build();

    CrmSegmentRequestDto expectedSegmentDto = new CrmSegmentRequestDto();
    expectedSegmentDto.id = segmentRequest.getOperation().getId();
    expectedSegmentDto.neId = 230823;
    expectedSegmentDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    SegmentRequestFailedException expectedException = new SegmentRequestFailedException("Failure");
    when(wdmRRM.handleSegmentRequest(expectedSegmentDto, null)).thenThrow(expectedException);

    mediator.handleSegmentRequest(envelope);

    verify(wdmRRM, times(1)).buildSegmentRequestContext(expectedSegmentDto);
    verify(wdmRRM, times(1)).handleSegmentRequest(expectedSegmentDto, null);
    verify(niCallbackSender, times(1)).sendFailure(
      expectedSegmentDto.id, expectedSegmentDto.requestType, expectedException,
      envelope.getSource(), envelope.getDestination(), List.of());
  }
}
