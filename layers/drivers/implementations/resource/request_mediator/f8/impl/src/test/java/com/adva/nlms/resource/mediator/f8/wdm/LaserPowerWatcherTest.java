/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.common.config.EquipmentState;
import com.adva.nlms.mediation.common.AuthorizationException;
import com.adva.nlms.mediation.common.UntrustedCertificateException;
import com.adva.nlms.mediation.mo.inventory.f8.PMScanner;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.OperationalState;
import com.adva.nlms.mediation.mo.inventory.resources.Ptp;
import com.adva.nlms.mediation.mo.inventory.resources.PtpBuilder;
import com.adva.nlms.mediation.mo.inventory.resources.State;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.ne.comm.rest.RestConfigurationException;
import com.adva.nlms.resource.crm.model.CrmCtrlTaskScheduler;
import com.adva.nlms.resource.mediator.f8.PowerEqOperation;
import com.adva.nlms.resource.mediator.f8.wdm.LaserOnDelayConditionRepository;
import com.adva.nlms.resource.mediator.f8.wdm.LaserPowerWatcher;
import com.adva.nlms.resource.mediator.f8.wdm.LaserPowerWatcherContext;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.WdmSegment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LaserPowerWatcherTest {

  private static final int NE_ID = 93392;
  private static final String PTP_AID = "ptp-aid";
  private static final String PTP_URI = "ptp-uri";
  private static final PowerEqOperation POWER_EQUALIZATION_OPERATION = PowerEqOperation.EQUALIZE_FORWARD;

  @Mock
  private CrmCtrlTaskScheduler mockScheduler;
  @Mock
  private PMScanner pmScanner;
  @Mock
  private LaserOnDelayConditionRepository lodRepository;
  @Mock
  private Consumer<WdmSegment> action;

  @InjectMocks
  private LaserPowerWatcher sut;

  private final ArgumentCaptor<Long> longCaptor = ArgumentCaptor.forClass(Long.class);
  private final ArgumentCaptor<TimeUnit> timeUnitCaptor = ArgumentCaptor.forClass(TimeUnit.class);

  private LaserPowerWatcherContext context;

  @BeforeEach
  public void setUp() {
    WdmSegment segment = WdmSegment.newSegment()
      .withNeId(NE_ID)
      .build();
    Ptp ptp = PtpBuilder.newBuilder()
      .withNeId(NE_ID)
        .withAid(new Aid(PTP_AID))
      .withUri(new Uri(PTP_URI))
      .withState(new State(EquipmentState.UNKNOWN, OperationalState.NORMAL, List.of()))
      .withOpticalSetpoint(0)
      .build();
    context = new LaserPowerWatcherContext(segment, ptp, POWER_EQUALIZATION_OPERATION);
  }

  @Test
  void whenLaserIsOnOrLODRaised_NoLODRaisedNullLaserPowerReturned() throws AuthorizationException, UntrustedCertificateException, RestConfigurationException {
    //GIVEN
    when(lodRepository.isRaised(NE_ID, PTP_URI)).thenReturn(false);
    when(pmScanner.scanLaserPower(NE_ID, PTP_URI)).thenReturn(null);
    //WHEN
    sut.whenLaserIsOnOrLODRaised(context, action);
    //THEN
    assertThat(context)
      .extracting(LaserPowerWatcherContext::getSegment)
      .extracting(WdmSegment::getWaitingForLaserOnDelayClear)
      .isEqualTo(POWER_EQUALIZATION_OPERATION);

    assertThat(context).extracting(LaserPowerWatcherContext::shouldScanLaserPower)
      .isEqualTo(true);
    assertThat(context).extracting(LaserPowerWatcherContext::getLaserPower)
      .isEqualTo(LaserPowerWatcher.POWER_LEVEL_NULL);

    verify(mockScheduler, times(1)).scheduleTask(any(), longCaptor.capture(), timeUnitCaptor.capture());
    assertThat(longCaptor.getValue()).isEqualTo(context.getLaserPowerProbingDelay().toMillis());
    assertThat(timeUnitCaptor.getValue()).isEqualTo(TimeUnit.MILLISECONDS);
    verifyNoInteractions(action);
  }

  @Test
  void whenLaserIsOnOrLODRaised_NoLODRaisedLaserPowerAboveDefaultThresholdReturned() throws AuthorizationException, UntrustedCertificateException, RestConfigurationException {
    //GIVEN
    final float expectedLaserPower = 5.2F;
    when(lodRepository.isRaised(NE_ID, PTP_URI)).thenReturn(false);
    when(pmScanner.scanLaserPower(NE_ID, PTP_URI)).thenReturn(expectedLaserPower);
    //WHEN
    sut.whenLaserIsOnOrLODRaised(context, action);
    //THEN
    assertThat(context)
      .extracting(LaserPowerWatcherContext::getSegment)
      .extracting(WdmSegment::getWaitingForLaserOnDelayClear)
      .isEqualTo(POWER_EQUALIZATION_OPERATION);

    assertThat(context).extracting(LaserPowerWatcherContext::shouldScanLaserPower)
      .isEqualTo(true);

    assertThat(context).extracting(LaserPowerWatcherContext::getLaserPower)
      .isEqualTo(expectedLaserPower);

    verify(mockScheduler, times(1)).scheduleTask(any(), longCaptor.capture(), timeUnitCaptor.capture());

    assertThat(longCaptor.getValue()).isEqualTo(context.getLaserPowerProbingDelay().toMillis());
    assertThat(timeUnitCaptor.getValue()).isEqualTo(TimeUnit.MILLISECONDS);

    verifyNoInteractions(action);
  }

  @Test
  void whenLaserIsOnOrLODRaised_NoLODRaisedNullOpticalSetPointReturned() throws AuthorizationException, UntrustedCertificateException, RestConfigurationException {
    //GIVEN
    WdmSegment segment = WdmSegment.newSegment()
      .withNeId(NE_ID)
      .build();
    Ptp ptp = PtpBuilder.newBuilder()
      .withNeId(NE_ID)
      .withAid(new Aid(PTP_AID))
      .withUri(new Uri(PTP_URI))
      .withState(new State(EquipmentState.UNKNOWN, OperationalState.NORMAL, List.of()))
      .build();
    context = new LaserPowerWatcherContext(segment, ptp, POWER_EQUALIZATION_OPERATION);

    final float expectedLaserPower = 0.1F;
    when(lodRepository.isRaised(NE_ID, PTP_URI)).thenReturn(false);
    when(pmScanner.scanLaserPower(NE_ID, PTP_URI)).thenReturn(expectedLaserPower);
    //WHEN
    sut.whenLaserIsOnOrLODRaised(context, action);
    //THEN
    assertThat(context)
      .extracting(LaserPowerWatcherContext::getSegment)
      .extracting(WdmSegment::getWaitingForLaserOnDelayClear)
      .isEqualTo(POWER_EQUALIZATION_OPERATION);

    assertThat(context).extracting(LaserPowerWatcherContext::shouldScanLaserPower)
      .isEqualTo(true);

    assertThat(context).extracting(LaserPowerWatcherContext::getLaserPower)
      .isEqualTo(expectedLaserPower);

    verify(mockScheduler, times(1)).scheduleTask(any(), longCaptor.capture(), timeUnitCaptor.capture());
  }

  @Test
  void whenLaserIsOnOrLODRaised_NoLODRaisedLaserPowerBelowDefaultThresholdReturned() throws AuthorizationException, UntrustedCertificateException, RestConfigurationException {
    //GIVEN
    final float expectedLaserPower = 0.1F;
    when(lodRepository.isRaised(NE_ID, PTP_URI)).thenReturn(false);
    when(pmScanner.scanLaserPower(NE_ID, PTP_URI)).thenReturn(expectedLaserPower);
    //WHEN
    sut.whenLaserIsOnOrLODRaised(context, action);
    //THEN
    assertThat(context)
      .extracting(LaserPowerWatcherContext::getSegment)
      .extracting(WdmSegment::getWaitingForLaserOnDelayClear)
      .isEqualTo(POWER_EQUALIZATION_OPERATION);

    assertThat(context).extracting(LaserPowerWatcherContext::shouldScanLaserPower)
      .isEqualTo(true);

    assertThat(context).extracting(LaserPowerWatcherContext::getLaserPower)
      .isEqualTo(expectedLaserPower);

    verify(mockScheduler, times(1)).scheduleTask(any(), longCaptor.capture(), timeUnitCaptor.capture());
  }

  @Test
  void whenLaserIsOnOrLODRaised_LODAlreadyRaised() {
    //GIVEN
    when(lodRepository.isRaised(NE_ID, PTP_URI)).thenReturn(true);
    //WHEN
    sut.whenLaserIsOnOrLODRaised(context, action);
    //THEN
    assertThat(context)
      .extracting(LaserPowerWatcherContext::getSegment)
      .extracting(WdmSegment::getWaitingForLaserOnDelayClear)
      .isEqualTo(POWER_EQUALIZATION_OPERATION);

    assertThat(context).extracting(LaserPowerWatcherContext::shouldScanLaserPower)
      .isEqualTo(true);

    verifyNoInteractions(action, pmScanner, mockScheduler);
  }


  @Test
  void whenLaserIsOnOrLODRaised_restConfigurationExceptionThrownWhilePMScan() throws AuthorizationException, UntrustedCertificateException, RestConfigurationException {
    //GIVEN
    when(lodRepository.isRaised(NE_ID, PTP_URI)).thenReturn(false);
    when(pmScanner.scanLaserPower(NE_ID, PTP_URI)).thenThrow(RestConfigurationException.class);
    //WHEN
    sut.whenLaserIsOnOrLODRaised(context, action);
    //THEN
    verify(action, times(0)).accept(context.getSegment());
  }

  @Test
  void whenLaserIsOnOrLODRaised_untrustedCertificateExceptionThrownWhilePMScan() throws AuthorizationException, UntrustedCertificateException, RestConfigurationException {
    //GIVEN
    when(lodRepository.isRaised(NE_ID, PTP_URI)).thenReturn(false);
    when(pmScanner.scanLaserPower(NE_ID, PTP_URI)).thenThrow(UntrustedCertificateException.class);
    //WHEN
    sut.whenLaserIsOnOrLODRaised(context, action);
    //THEN
    verify(action, times(0)).accept(context.getSegment());
  }

  @Test
  void whenLaserIsOnOrLODRaised_authorizationExceptionThrownWhilePMScan() throws AuthorizationException, UntrustedCertificateException, RestConfigurationException {
    //GIVEN
    when(lodRepository.isRaised(NE_ID, PTP_URI)).thenReturn(false);
    when(pmScanner.scanLaserPower(NE_ID, PTP_URI)).thenThrow(AuthorizationException.class);
    //WHEN
    sut.whenLaserIsOnOrLODRaised(context, action);
    //THEN
    verify(action, times(0)).accept(context.getSegment());
  }
}