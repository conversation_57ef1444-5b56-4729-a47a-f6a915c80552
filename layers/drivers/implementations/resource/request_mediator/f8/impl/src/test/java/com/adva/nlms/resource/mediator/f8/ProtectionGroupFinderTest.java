/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */

package com.adva.nlms.resource.mediator.f8;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.mediation.config.f8.croma.api.CromaMOService;
import com.adva.nlms.mediation.config.f8.croma.endpoint.api.CromaEcServiceEndpointDTO;
import com.adva.nlms.mediation.config.f8.croma.endpoint.api.CromaEcServicePathDTO;
import com.adva.nlms.mediation.config.f8.croma.slc.api.Slc;
import com.adva.nlms.mediation.ec.support.EcEntityIndex;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.LineProtectionGroupRef;
import com.adva.nlms.mediation.mo.inventory.resources.ProtectionGroupRef;
import com.adva.nlms.mediation.mo.inventory.resources.ProtectionGroupResources;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class ProtectionGroupFinderTest {

  private final CromaMOService cromaMOService = mock();
  private final ProtectionGroupResources protectionGroupResources = mock();
  private final ProtectionGroupFinder sut = new ProtectionGroupFinder(cromaMOService, protectionGroupResources);

  private static final int NE_ID = 1;
  private static final String CTP_TRAFFIC = "/ctp/traffic";
  private static final String CTP_URI = "/mit/me/1/eqh/shelf,2/eqh/slot,10/eq/card/ptp/nw,2/ctp/ot200/ctp/odu4-1";
  private static final String PARENT_CTP_URI = "/mit/me/1/eqh/shelf,2/eqh/slot,10/eq/card/ptp/nw,2/ctp/ot200";
  private static final String CROMA_AND_ENCODED_PARENT_CTP_URI = "/mit/me/1/croma/svcep/%2Fmit%2Fme%2F1%2Feqh%2Fshelf%2C2%2Feqh%2Fslot%2C10%2Feq%2Fcard%2Fptp%2Fnw%2C2";
  private static final String workingPortId = "/mit/me/1/eqh/shelf,2/eqh/slot,9/eq/card/ptp/nw,1-w";
  private static final String protectingPortId = "/mit/me/1/eqh/shelf,2/eqh/slot,9/eq/card/ptp/nw,1-e";
  private static final EntityIndex workingEntity = EcEntityIndex.getEcEntityIndex(workingPortId + CTP_TRAFFIC);
  private static final EntityIndex protectingEntity = EcEntityIndex.getEcEntityIndex(protectingPortId + CTP_TRAFFIC);

  @Test
  void findProtectionGroupByRelatedSlcZEndpointCtp() {
    //given
    var slcs = List.of(prepareSlc(1), prepareSlc(2));
    var sep = prepareSep();
    var protectionGroup = prepareProtectionGroup();

    when(cromaMOService.findSlcByNeIdAndZEndpointResourceInstance(NE_ID, PARENT_CTP_URI)).thenReturn(slcs);
    when(cromaMOService.getServiceEndpoint(NE_ID, EcEntityIndex.getEcEntityIndex(CROMA_AND_ENCODED_PARENT_CTP_URI))).thenReturn(sep);
    when(protectionGroupResources.findProtectionGroupByWorkingAndProtectingEntity(NE_ID, List.of(workingEntity, protectingEntity))).thenReturn(List.of(protectionGroup));

    //when
    var foundProtectionGroup = sut.findByRelatedSlcZEndpointCtp(NE_ID, CTP_URI);

    //then
    assertTrue(foundProtectionGroup.isPresent());
    assertNotNull(foundProtectionGroup.get());
  }

  @Test
  void shouldNotFoundProtectionGroupSlcsLessThanTwo() {
    //given
    var slcs = List.of(prepareSlc(1));

    when(cromaMOService.findSlcByNeIdAndZEndpointResourceInstance(NE_ID, PARENT_CTP_URI)).thenReturn(slcs);

    //when
    var foundProtectionGroup = sut.findByRelatedSlcZEndpointCtp(NE_ID, CTP_URI);

    //then
    assertTrue(foundProtectionGroup.isEmpty());
  }

  @Test
  void shouldNotFoundProtectionGroupSepNotFound() {
    //given
    var slcs = List.of(prepareSlc(1), prepareSlc(2));

    when(cromaMOService.findSlcByNeIdAndZEndpointResourceInstance(NE_ID, PARENT_CTP_URI)).thenReturn(slcs);
    when(cromaMOService.getServiceEndpoint(NE_ID, EcEntityIndex.getEcEntityIndex(CROMA_AND_ENCODED_PARENT_CTP_URI))).thenReturn(null);

    //when
    var foundProtectionGroup = sut.findByRelatedSlcZEndpointCtp(NE_ID, CTP_URI);

    //then
    assertTrue(foundProtectionGroup.isEmpty());
  }

  @Test
  void shouldNotFoundProtectionGroupServicePathsSizeNotEqualToTwo() {
    //given
    var slcs = List.of(prepareSlc(1), prepareSlc(2));
    var sep = new CromaEcServiceEndpointDTO();
    sep.setServicePathEcDTOS(List.of(prepareWorkingServicePath(1)));

    when(cromaMOService.findSlcByNeIdAndZEndpointResourceInstance(NE_ID, PARENT_CTP_URI)).thenReturn(slcs);
    when(cromaMOService.getServiceEndpoint(NE_ID, EcEntityIndex.getEcEntityIndex(CROMA_AND_ENCODED_PARENT_CTP_URI))).thenReturn(sep);

    //when
    var foundProtectionGroup = sut.findByRelatedSlcZEndpointCtp(NE_ID, CTP_URI);

    //then
    assertTrue(foundProtectionGroup.isEmpty());
  }

  private Slc prepareSlc(int slcId) {
    return new Slc(-1, NE_ID, slcId, null, null, null, List.of(), List.of(), EcEntityIndex.getEcEntityIndex(String.format("/mit/me/1/croma/slc/%d", slcId)), null, null);
  }

  private CromaEcServiceEndpointDTO prepareSep() {
    CromaEcServiceEndpointDTO cromaEcServiceEndpointDTO = new CromaEcServiceEndpointDTO();
    cromaEcServiceEndpointDTO.setServicePathEcDTOS(List.of(prepareWorkingServicePath(1), prepareProtectingServicePath(2)));
    return cromaEcServiceEndpointDTO;
  }

  private CromaEcServicePathDTO prepareWorkingServicePath(int slcId) {
    CromaEcServicePathDTO cromaEcServicePathDTO = new CromaEcServicePathDTO();
    cromaEcServicePathDTO.setSlc(String.format("/mit/me/1/croma/slc/%d", slcId));
    cromaEcServicePathDTO.setPortId(workingPortId);
    return cromaEcServicePathDTO;
  }

  private CromaEcServicePathDTO prepareProtectingServicePath(int slcId) {
    CromaEcServicePathDTO cromaEcServicePathDTO = new CromaEcServicePathDTO();
    cromaEcServicePathDTO.setSlc(String.format("/mit/me/1/croma/slc/%d", slcId));
    cromaEcServicePathDTO.setPortId(protectingPortId);
    return cromaEcServicePathDTO;
  }

  private ProtectionGroupRef prepareProtectionGroup() {
    return new LineProtectionGroupRef(NE_ID,
      new Aid("Protection-2/9/traffic-1"),
      new Uri("/mit/me/1/eqh/shelf,2/eqh/slot,9/eq/card/prtgrp/traffic-1"),
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      null);
  }
}
