/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.provider.api.CapabilityProvider;
import com.adva.infrastructure.capabilityprovider.capabilities.CapabilityConfiguration;
import com.adva.infrastructure.capabilityprovider.core.CapabilityProviderConfiguration;
import com.adva.infrastructure.capabilityprovider.mo.MoCapabilityProviderConfiguration;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.resource.mediator.f8.adapters.persistence.InMemoryOtnSegmentRepository;
import com.adva.nlms.resource.mediator.f8.events.CtpAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.CtpAutocreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.CtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.PtpAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.PtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncAutocreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncCreatedEvent;
import com.adva.nlms.resource.mediator.f8.otn.CtpRequest;
import com.adva.nlms.resource.mediator.f8.otn.PrepareDeprovisionEvents;
import com.adva.nlms.resource.mediator.f8.otn.PtpParamsRequest;
import com.adva.nlms.resource.mediator.f8.otn.PtpRequest;
import com.adva.nlms.resource.mediator.f8.otn.Request;
import com.adva.nlms.resource.mediator.f8.otn.RequestExecutor;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnCardCapabilities;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnCardCapabilitiesImpl;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnDbResourcesFacade;
import com.adva.nlms.resource.mediator.f8.otn.SncRequest;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegment;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegmentRepository;
import com.adva.nlms.resource.mediator.f8.otn.api.in.SegmentID;
import com.adva.nlms.resource.provision.f8.api.in.ObjectDoesNotExistException;
import com.adva.nlms.resource.provision.f8.api.in.ObjectManagedBySystemException;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionException;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionObjectExistsException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class RequestExecutorTest {

  private static final int NE_ID = 777;
  private static final int SNC_NUMBER = 1;
  private static final String SAMPLE_CTP_URI_1 = "CTP_1_URI";
  private static final String SAMPLE_CTP_URI_2 = "CTP_2_URI";

  private static final String SAMPLE_PTP_URI = "PTP_URI";
  private static final String SEGMENT_ID = "SEGMENT_ID";
  private static final String SAMPLE_SNC_URI = "SNC_URI";
  private static final String CREATED_SNC_URI = "SNC_URI/1";
  private RequestExecutor sut;
  private OtnSegmentRepository segmentRepository;
  private Provision provisionApi;
  private final RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade = Mockito.mock(RrmOtnDbResourcesFacade.class);
  private final NEDataProvider neDataProvider = Mockito.mock(NEDataProvider.class);
  private static final CapabilityProvider capabilityProvider = new CapabilityProviderConfiguration()
    .capabilityProvider(new CapabilityConfiguration().capabilityRepository(), null, null);
  private static final MoCapabilityProvider moCapabilityProvider = new MoCapabilityProviderConfiguration().moCapabilityProvider();
  private final RrmOtnCardCapabilities rrmOtnCardCapabilities = new RrmOtnCardCapabilitiesImpl(neDataProvider, capabilityProvider, moCapabilityProvider);

  @BeforeEach
  void init() {
    segmentRepository = new InMemoryOtnSegmentRepository();
    provisionApi = Mockito.mock(Provision.class);
    sut = new RequestExecutor(segmentRepository, provisionApi);
  }

  @Test
  void test_adopt_positive_case() {
    List<Request> requests = prepareRequestsList(true);

    sut.adopt(requests, NE_ID, SEGMENT_ID, null);

    OtnSegment createdSegment = segmentRepository.findOne(new SegmentID(NE_ID, SEGMENT_ID));
    assertNotNull(createdSegment);
    var events = createdSegment.provisionEvents();
    assertEquals(4, events.size());
    assertEquals(PtpAdoptedEvent.class, events.get(0).getClass());
    assertEquals(SAMPLE_PTP_URI, events.get(0).uri());
    assertEquals(CtpAdoptedEvent.class, events.get(1).getClass());
    assertEquals(SAMPLE_CTP_URI_1, events.get(1).uri());
    assertEquals(CtpAdoptedEvent.class, events.get(2).getClass());
    assertEquals(SAMPLE_CTP_URI_2, events.get(2).uri());
    assertEquals(SncAdoptedEvent.class, events.get(3).getClass());
    assertEquals(CREATED_SNC_URI, events.get(3).uri());
  }

  @Test
  void test_create_positive_case() {
    List<Request> requests = prepareRequestsList(false);

    Mockito.when(provisionApi.provisionSnc(Mockito.any(NetworkElementID.class), Mockito.anyString(), Mockito.anySet(), Mockito.anySet(), Mockito.isNull())).thenReturn(SNC_NUMBER);

    sut.createOrAdopt(requests, NE_ID, SEGMENT_ID, null);

    OtnSegment createdSegment = segmentRepository.findOne(new SegmentID(NE_ID, SEGMENT_ID));
    assertNotNull(createdSegment);
    var events = createdSegment.provisionEvents();
    assertEquals(5, events.size());
    assertEquals(PtpCreatedEvent.class, events.get(0).getClass());
    assertEquals(SAMPLE_PTP_URI, events.get(0).uri());
    assertEquals(CtpCreatedEvent.class, events.get(1).getClass());
    assertEquals(SAMPLE_CTP_URI_1, events.get(1).uri());
    assertEquals(PtpAdoptedEvent.class, events.get(2).getClass());
    assertEquals(SAMPLE_PTP_URI, events.get(2).uri());
    assertEquals(CtpCreatedEvent.class, events.get(3).getClass());
    assertEquals(SAMPLE_CTP_URI_2, events.get(3).uri());
    assertEquals(SncCreatedEvent.class, events.get(4).getClass());
    assertEquals(CREATED_SNC_URI, events.get(4).uri());
  }

  @Test
  void test_create_with_autocreation() {
    List<Request> requests = prepareRequestsList(false);

    Mockito.when(provisionApi.provisionSnc(Mockito.any(NetworkElementID.class), Mockito.anyString(), Mockito.anySet(), Mockito.anySet(), Mockito.isNull())).thenReturn(SNC_NUMBER);

    Mockito.doThrow(ProvisionObjectExistsException.class)
      .when(provisionApi)
      .provisionCtp(NetworkElementID.create(NE_ID), SAMPLE_CTP_URI_1, Set.of());

    sut.createOrAdopt(requests, NE_ID, SEGMENT_ID, null);

    OtnSegment createdSegment = segmentRepository.findOne(new SegmentID(NE_ID, SEGMENT_ID));
    assertNotNull(createdSegment);
    var events = createdSegment.provisionEvents();
    assertEquals(5, events.size());
    assertEquals(PtpCreatedEvent.class, events.get(0).getClass());
    assertEquals(SAMPLE_PTP_URI, events.get(0).uri());
    assertEquals(CtpAdoptedEvent.class, events.get(1).getClass());
    assertEquals(SAMPLE_CTP_URI_1, events.get(1).uri());
    assertEquals(PtpAdoptedEvent.class, events.get(2).getClass());
    assertEquals(SAMPLE_PTP_URI, events.get(2).uri());
    assertEquals(CtpCreatedEvent.class, events.get(3).getClass());
    assertEquals(SAMPLE_CTP_URI_2, events.get(3).uri());
    assertEquals(SncCreatedEvent.class, events.get(4).getClass());
    assertEquals(CREATED_SNC_URI, events.get(4).uri());
  }

  @Test
  void test_create_when_entity_autocreated() {
    CtpRequest ctpRequest1 = new CtpRequest(NE_ID, SAMPLE_CTP_URI_1, false, true, provisionApi);
    CtpRequest ctpRequest2 = new CtpRequest(NE_ID, SAMPLE_CTP_URI_2, true, true, provisionApi);
    SncRequest sncRequest = new SncRequest(NE_ID, CREATED_SNC_URI, true, true, Set.of(), Set.of(), provisionApi);

    List<Request> requests = List.of(
      ctpRequest1,
      ctpRequest2,
      sncRequest);

    sut.createOrAdopt(requests, NE_ID, SEGMENT_ID, null);

    OtnSegment createdSegment = segmentRepository.findOne(new SegmentID(NE_ID, SEGMENT_ID));
    assertNotNull(createdSegment);
    var events = createdSegment.provisionEvents();
    assertEquals(3, events.size());
    assertEquals(CtpAutocreatedEvent.class, events.get(0).getClass());
    assertEquals(SAMPLE_CTP_URI_1, events.get(0).uri());
    assertEquals(CtpAutocreatedEvent.class, events.get(1).getClass());
    assertEquals(SAMPLE_CTP_URI_2, events.get(1).uri());
    assertEquals(SncAutocreatedEvent.class, events.get(2).getClass());
    assertEquals(CREATED_SNC_URI, events.get(2).uri());
  }

  @Test
  void test_create_and_adopt_positive_case() {
    PtpRequest ptpRequest = new PtpRequest(NE_ID, SAMPLE_PTP_URI, false, provisionApi);
    CtpRequest ctpRequest1 = new CtpRequest(NE_ID, SAMPLE_CTP_URI_1, false, false, provisionApi);
    PtpParamsRequest ptpParamsRequest = new PtpParamsRequest(NE_ID, SAMPLE_PTP_URI, false, Set.of(), provisionApi);
    CtpRequest ctpRequest2 = new CtpRequest(NE_ID, SAMPLE_CTP_URI_2, true, false, provisionApi);
    SncRequest sncRequest = new SncRequest(NE_ID, CREATED_SNC_URI, true, Set.of(), Set.of(), provisionApi);

    List<Request> requests = List.of(
      ptpRequest,
      ctpRequest1,
      ptpParamsRequest,
      ctpRequest2,
      sncRequest);

    sut.createOrAdopt(requests, NE_ID, SEGMENT_ID, null);

    OtnSegment createdSegment = segmentRepository.findOne(new SegmentID(NE_ID, SEGMENT_ID));
    assertNotNull(createdSegment);
    var events = createdSegment.provisionEvents();
    assertEquals(5, events.size());
    assertEquals(PtpCreatedEvent.class, events.get(0).getClass());
    assertEquals(SAMPLE_PTP_URI, events.get(0).uri());
    assertEquals(CtpCreatedEvent.class, events.get(1).getClass());
    assertEquals(SAMPLE_CTP_URI_1, events.get(1).uri());
    assertEquals(PtpAdoptedEvent.class, events.get(2).getClass());
    assertEquals(SAMPLE_PTP_URI, events.get(2).uri());
    assertEquals(CtpAdoptedEvent.class, events.get(3).getClass());
    assertEquals(SAMPLE_CTP_URI_2, events.get(3).uri());
    assertEquals(SncAdoptedEvent.class, events.get(4).getClass());
    assertEquals(CREATED_SNC_URI, events.get(4).uri());
  }

  @Test
  void test_snc_create_provision_failure() {
    List<Request> requests = prepareRequestsList(false);

    Mockito.doThrow(new ProvisionException("snc prov error"))
      .when(provisionApi).provisionSnc(NetworkElementID.create(NE_ID), SAMPLE_SNC_URI, Set.of(), Set.of(), null);

    assertThrows(
      ProvisionException.class,
      () -> sut.createOrAdopt(requests, NE_ID, SEGMENT_ID, null)
    );

    OtnSegment createdSegment = segmentRepository.findOne(new SegmentID(NE_ID, SEGMENT_ID));
    assertNull(createdSegment);
  }

  @Test
  void test_ctp_create_provision_failure() {
    List<Request> requests = prepareRequestsList(false);

    Mockito.doThrow(new ProvisionException("ctp prov error"))
      .when(provisionApi).provisionCtp(NetworkElementID.create(NE_ID), SAMPLE_CTP_URI_1, Set.of());

    assertThrows(
      ProvisionException.class,
      () -> sut.createOrAdopt(requests, NE_ID, SEGMENT_ID, null)
    );

    OtnSegment createdSegment = segmentRepository.findOne(new SegmentID(NE_ID, SEGMENT_ID));
    assertNull(createdSegment);
  }

  @Test
  void test_delete() {
    List<Request> requests = prepareRequestsList(false);

    sut.createOrAdopt(requests, NE_ID, SEGMENT_ID, null);
    assertTrue(segmentRepository.exists(new SegmentID(NE_ID, SEGMENT_ID)));
    var deprovisionEvents = new PrepareDeprovisionEvents(
      segmentRepository, rrmOtnDbResourcesFacade, rrmOtnCardCapabilities)
      .prepareEvents(NE_ID, SEGMENT_ID);
    sut.delete(NE_ID, SEGMENT_ID, deprovisionEvents);
    assertFalse(segmentRepository.exists(new SegmentID(NE_ID, SEGMENT_ID)));
  }

  @Test
  void test_delete_when_one_of_object_is_managed_by_device() throws ObjectDoesNotExistException {
    List<Request> requests = prepareRequestsList(false);

    sut.createOrAdopt(requests, NE_ID, SEGMENT_ID, null);
    assertTrue(segmentRepository.exists(new SegmentID(NE_ID, SEGMENT_ID)));
    var deprovisionEvents = new PrepareDeprovisionEvents(
      segmentRepository, rrmOtnDbResourcesFacade, rrmOtnCardCapabilities)
      .prepareEvents(NE_ID, SEGMENT_ID);
    Mockito.doThrow(ObjectManagedBySystemException.class)
      .when(provisionApi)
      .deleteCtp(NetworkElementID.create(NE_ID), SAMPLE_CTP_URI_1);
    sut.delete(NE_ID, SEGMENT_ID, deprovisionEvents);
    assertFalse(segmentRepository.exists(new SegmentID(NE_ID, SEGMENT_ID)));
  }

  @Test
  void test_delete_when_one_of_request_fails() throws ObjectDoesNotExistException {
    List<Request> requests = prepareRequestsList(false);

    sut.createOrAdopt(requests, NE_ID, SEGMENT_ID, null);
    assertTrue(segmentRepository.exists(new SegmentID(NE_ID, SEGMENT_ID)));
    var deprovisionEvents = new PrepareDeprovisionEvents(
      segmentRepository, rrmOtnDbResourcesFacade, rrmOtnCardCapabilities)
      .prepareEvents(NE_ID, SEGMENT_ID);
    Mockito.doThrow(ProvisionException.class)
      .when(provisionApi)
      .deleteCtp(NetworkElementID.create(NE_ID), SAMPLE_CTP_URI_1);
    sut.delete(NE_ID, SEGMENT_ID, deprovisionEvents);
    assertFalse(segmentRepository.exists(new SegmentID(NE_ID, SEGMENT_ID)));
  }



  @Test
  void test_abandon() {
    List<Request> requests = prepareRequestsList(false);

    sut.createOrAdopt(requests, NE_ID, SEGMENT_ID, null);
    assertTrue(segmentRepository.exists(new SegmentID(NE_ID, SEGMENT_ID)));
    sut.abandon(NE_ID, SEGMENT_ID);
    assertFalse(segmentRepository.exists(new SegmentID(NE_ID, SEGMENT_ID)));
  }

  private List<Request> prepareRequestsList(boolean isAdopt) {
    PtpRequest ptpRequest = new PtpRequest(NE_ID, SAMPLE_PTP_URI, isAdopt, provisionApi);
    CtpRequest ctpRequest1 = new CtpRequest(NE_ID, SAMPLE_CTP_URI_1, isAdopt, false, provisionApi);
    PtpParamsRequest ptpParamsRequest = new PtpParamsRequest(NE_ID, SAMPLE_PTP_URI, isAdopt, Set.of(), provisionApi);
    CtpRequest ctpRequest2 = new CtpRequest(NE_ID, SAMPLE_CTP_URI_2, isAdopt, false, provisionApi);
    SncRequest sncRequest = new SncRequest(NE_ID, isAdopt? CREATED_SNC_URI : SAMPLE_SNC_URI, isAdopt, Set.of(), Set.of(), provisionApi);

    return List.of(
      ptpRequest,
      ctpRequest1,
      ptpParamsRequest,
      ctpRequest2,
      sncRequest);
  }

}
