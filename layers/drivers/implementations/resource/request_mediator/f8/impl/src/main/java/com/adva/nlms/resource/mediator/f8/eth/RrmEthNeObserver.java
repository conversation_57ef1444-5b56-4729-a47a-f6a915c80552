/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.resource.mediator.f8.eth;

import com.adva.nlms.resource.crm.model.NeObserver;

class RrmEthNeObserver extends NeObserver {
  private final EthResourceRequestMediator ethResourceRequestMediator;

  RrmEthNeObserver(EthResourceRequestMediator ethResourceRequestMediator) {
    this.ethResourceRequestMediator = ethResourceRequestMediator;
  }

  @Override
  protected void handleNeRemoval(int neId) {
    ethResourceRequestMediator.clearSegmentCache(neId);
  }
}
