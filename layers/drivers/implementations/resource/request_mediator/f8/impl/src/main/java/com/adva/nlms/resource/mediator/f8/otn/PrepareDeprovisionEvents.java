/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.CtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.PortIdExtractor;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.opticalparameters.api.Values;
import com.adva.nlms.resource.mediator.f8.events.CccProtectionGroupCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.CtpAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.CtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.mediator.f8.events.PtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncLocalEvent;
import com.adva.nlms.resource.mediator.f8.events.SncProtectionGroupCreatedEvent;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegment;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegmentRepository;
import com.adva.nlms.resource.mediator.f8.otn.api.in.SegmentID;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

class PrepareDeprovisionEvents {
  private static final Logger log = LogManager.getLogger(PrepareDeprovisionEvents.class);

  private final OtnSegmentRepository otnSegmentRepository;
  private final RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade;
  private final RrmOtnCardCapabilities rrmOtnCardCapabilities;

  PrepareDeprovisionEvents(OtnSegmentRepository otnSegmentRepository,
                           RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade,
                           RrmOtnCardCapabilities rrmOtnCardCapabilities) {
    this.otnSegmentRepository = otnSegmentRepository;
    this.rrmOtnDbResourcesFacade = rrmOtnDbResourcesFacade;
    this.rrmOtnCardCapabilities = rrmOtnCardCapabilities;
  }

  private List<ProvisionLocalEvent> preparePtpEvents(Values.PortPoolDescriptor.PortSetDescriptor portSetDescriptor,
                                                     PtpRef ptpRef,
                                                     String ctp, int neId) {
    List<CtpRef> ctps = new ArrayList<>();
    List<ProvisionLocalEvent> events = new ArrayList<>();
    var baseAid = ptpRef.aid().aid().substring(0, ptpRef.aid().aid().length() - PortIdExtractor.getPortId(ptpRef.aid().aid()).length());
    for (var port : portSetDescriptor.ports()) {
      var portPtp = rrmOtnDbResourcesFacade.findPtp(neId, new Aid(baseAid + port.toLowerCase()));
      if (portPtp.isPresent()) {
        ctps.addAll(rrmOtnDbResourcesFacade.findAllCtpsFromPtp(neId, portPtp.get().aid()));
        events.add(new PtpCreatedEvent(portPtp.get().uri().uri()));
      }
    }
    // if there is only one ctp, the one that is going to be deleted, left, delete all ptps within capability as well
    if (ctps.size() == 1 && ctps.get(0).uri().uri().equals(ctp)) {
      return events;
    }
    return List.of();
  }

  private List<ProvisionLocalEvent> buildPtpEventsFromCtp(ProvisionLocalEvent event, int neId) {
    var uri = event.uri();
    var plugRef = rrmOtnDbResourcesFacade.findPlugFromCtp(neId, new Uri(uri));
    var ptpRef = rrmOtnDbResourcesFacade.findPortFromCtp(neId, new Uri(uri));
    if (plugRef.isEmpty() || ptpRef.isEmpty()) {
      return List.of();
    }
    CardRef cardRef = rrmOtnDbResourcesFacade.findCardFromPlug(neId, plugRef.get().aid())
      .orElseThrow(() -> new OtnProvisioningException(String.format("Cannot find module for plug with aid %s", plugRef.get().aid().aid())));

    var portSetDescriptors = rrmOtnCardCapabilities.getPortSetDescriptors(neId, cardRef, plugRef.get());
    return portSetDescriptors.stream()
      .flatMap(descriptor -> preparePtpEvents(descriptor, ptpRef.get(), uri, neId).stream())
      .toList();
  }

  private List<ProvisionLocalEvent> analyseEvent(ProvisionLocalEvent event, int neId) {
    List<ProvisionLocalEvent> events = new ArrayList<>();
    if (event instanceof CtpCreatedEvent || event instanceof CtpAdoptedEvent) {
      events.addAll(buildPtpEventsFromCtp(event, neId));
    }
    // ignore ptp events, as we create the ones we want to delete based on ctp event
    // PtpAdoptedEvent has to be processed in order to set admin state to Auto In Service
    if (!(event instanceof PtpCreatedEvent)) {
      events.add(event);
    }
    return events;
  }

  public List<ProvisionLocalEvent> prepareEvents(int neId, String segmentId) {
    SegmentID segmentID = new SegmentID(neId, segmentId);
    OtnSegment otnSegment = otnSegmentRepository.findOne(segmentID);

    List<ProvisionLocalEvent> events = new ArrayList<>(otnSegment.provisionEvents().stream()
      .flatMap(event -> analyseEvent(event, neId).stream())
      .distinct()
      .toList());

    if (otnSegment.protectionData() != null) {
      log.debug("Found Protection Data on segment {} - trying to delete", otnSegment.segmentID());
      if (otnSegment.protectionData().isCccProtection()) {
        var cccProtectionEvent = createCccProtectionEvent(neId, otnSegment);
        cccProtectionEvent.ifPresent(events::add);
      } else if(otnSegment.protectionData().isLineProtection()) {
        var lineProtectionEvent = createLineProtectionEvent(neId, otnSegment);
        lineProtectionEvent.ifPresent(events::add);
      }
    }

    return events;
  }

  private Optional<ProvisionLocalEvent> createCccProtectionEvent(int neId, OtnSegment otnSegment) {
    var port = otnSegment.protectionData().clientProtectionPort();
    var ctps = rrmOtnDbResourcesFacade.findAllCtpsFromPtp(neId, new Aid(port.aid()));
    if (ctps.isEmpty()) {
      log.warn("Cannot find ctps for ccc protection group port");
      return Optional.empty();
    }

    var protectionGroup = rrmOtnDbResourcesFacade.findCccProtectionGroupByWorkingOrProtectingCtpUri(neId, ctps.get(0).uri());
    if (protectionGroup.isPresent()) {
      var protectionGroupAid = protectionGroup.get().aid().aid();
      var protectionGroupName = protectionGroupAid.substring(protectionGroupAid.indexOf("-") + 1);
      return Optional.of(new CccProtectionGroupCreatedEvent(protectionGroupName));
    } else {
      log.warn("Cannot find ccc protection group for ctp uri");
      return Optional.empty();
    }
  }

  private Optional<ProvisionLocalEvent> createLineProtectionEvent(int neId, OtnSegment otnSegment) {
    Optional<ProvisionLocalEvent> sncEvent = otnSegment.provisionEvents().stream()
      .filter(SncLocalEvent.class::isInstance)
      .findFirst();

    if (sncEvent.isEmpty()) {
      log.warn("Cannot find snc event for line protection group");
      return Optional.empty();
    }

    var lineProtectionGroup = rrmOtnDbResourcesFacade.findProtectionGroupBySnc(neId, new Uri(sncEvent.get().uri()));
    if (lineProtectionGroup.isPresent()) {
      return Optional.of(new SncProtectionGroupCreatedEvent(lineProtectionGroup.get().uri().uri()));
    } else {
      log.warn("Cannot find line protection group for snc event");
      return Optional.empty();
    }
  }
}
