/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.mediator.f8;

import com.adva.nlms.opticalparameters.api.OpticalParameters;
import com.adva.nlms.opticalparameters.api.Value;
import com.adva.nlms.opticalparameters.api.enums.Direction;
import com.adva.nlms.opticalparameters.api.enums.ParameterName;
import com.adva.nlms.opticalparameters.api.enums.Revertive;
import com.adva.nlms.resource.mediator.f8.eth.Container;
import com.adva.nlms.resource.mediator.f8.eth.Whole;
import com.adva.nlms.resource.mediator.f8.wdm.CrmRequestTranslateException;
import ni.msg.EnvelopeOuterClass;
import ni.proto.connection_segment.ConnectionSegmentOuterClass;
import ni.proto.external.common.InterfaceTypeOuterClass;
import ni.proto.external.common.signal_description.FlexgridParams;
import ni.proto.external.common.signal_description.SignalDescription;
import ni.proto.external.common.signal_description.SignalEthernet;
import ni.proto.external.common.signal_description.SignalOtn;
import ni.proto.inet.Label;
import ni.proto.inet.MultiLabel;
import ni.proto.map.MapOuterClass;
import ni.proto.segment_callback.SegmentCallbackOuterClass;
import ni.proto.segment_request.SegmentRequestOuterClass;
import org.assertj.core.api.ThrowableAssert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

class WdmSegmentRequestTranslatorTest {

  @Test
  void emptyEnvelope() {
    //GIVEN
    var emptyEnvelope = EnvelopeOuterClass.Envelope.newBuilder()
      .build();

    //WHEN
    var actual = ThrowableAssert.catchThrowable(() -> SegmentRequestTranslator.translate(emptyEnvelope));

    //THEN
    assertThat(actual).isInstanceOf(NumberFormatException.class);
  }


  @Test
  void assignRolesEnvelope() throws CrmRequestTranslateException {
    //GIVEN
    final CrmSegmentRequestDto expected = new CrmSegmentRequestDto();
    expected.neId = 32355;
    expected.srcTp = "theSrcTp";
    expected.dstTp = "theDstTp";
    ProtectionSettings protectionSettings = new ProtectionSettings(false, "unidir", 5, 0);
    expected.assignRoles = new AssignRoles("workingLegId", "protectionLegId", protectionSettings);

    var envelope = EnvelopeOuterClass.Envelope.newBuilder();

    var srcAddress = EnvelopeOuterClass.Address.newBuilder();
    var srcIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    srcIdentifier.setId("35123");
    srcAddress.setId(srcIdentifier);

    envelope.setSource(srcAddress);
    expected.setSrcAddress(srcAddress.build());

    var dstAddress = EnvelopeOuterClass.Address.newBuilder();
    var dstIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    dstIdentifier.setId(String.valueOf(expected.neId));
    dstAddress.setId(dstIdentifier);

    envelope.setDestination(dstAddress);
    expected.setDstAddress(dstAddress.build());

    var segmentRequest = SegmentRequestOuterClass.SegmentRequest.newBuilder();
    var assignRoles = SegmentRequestOuterClass.AssignRoles.newBuilder();
    assignRoles.setWorkingLegId(expected.assignRoles.workingLegId());
    assignRoles.setProtectionLegId(expected.assignRoles.protectionLegId());
    assignRoles.setProtectionSettings(ConnectionSegmentOuterClass.ProtectionSettings.newBuilder().
      setParameters(MapOuterClass.Map.newBuilder()
        .addKvp(MapOuterClass.KeyValuePair.newBuilder().setK(ParameterName.HOLD_OFF_TIMER.toString()).setV("0 ms").build())
        .addKvp(MapOuterClass.KeyValuePair.newBuilder().setK(ParameterName.REVERTIVE.toString()).setV(Revertive.NO.getValue()).build())
        .addKvp(MapOuterClass.KeyValuePair.newBuilder().setK(ParameterName.DIRECTION.toString()).setV(Direction.UNIDIR.getValue()).build())
        .addKvp(MapOuterClass.KeyValuePair.newBuilder().setK(ParameterName.WAIT_TO_RESTORE.toString()).setV("5 min").build())
        .build())
      .build());

    segmentRequest.setAssignRoles(assignRoles);
    var segmentRequestObject = segmentRequest.build();
    envelope.setPayload(segmentRequestObject.toByteString());
    //WHEN
    var actual = SegmentRequestTranslator.translate(envelope.build());
    //THEN
    assertThat(actual).
      hasFieldOrPropertyWithValue("neId", expected.neId)
      .hasFieldOrPropertyWithValue("srcAddress", expected.getSrcAddress())
      .hasFieldOrPropertyWithValue("dstAddress", expected.getDstAddress())
      .extracting(r -> r.assignRoles)
      .hasFieldOrPropertyWithValue("workingLegId", expected.assignRoles.workingLegId())
      .hasFieldOrPropertyWithValue("protectionLegId", expected.assignRoles.protectionLegId())
      .hasFieldOrPropertyWithValue("protectionSettings", expected.assignRoles.protectionSettings());
  }

  @Test
  void unsupportedOperationEnvelope() {
    //GIVEN
    var envelope = EnvelopeOuterClass.Envelope.newBuilder();
    var destination = EnvelopeOuterClass.Address.newBuilder();
    var identifier = EnvelopeOuterClass.Identifier.newBuilder();
    identifier.setId("35123");
    destination.setId(identifier.build());
    envelope.setDestination(destination);
    var segmentRequestBuilder = SegmentRequestOuterClass.SegmentRequest.newBuilder();
    var segmentRequestObject = segmentRequestBuilder.build();
    envelope.setPayload(segmentRequestObject.toByteString());

    //WHEN
    var actual = ThrowableAssert.catchThrowable(() -> SegmentRequestTranslator.translate(envelope.build()));

    //THEN
    assertThat(actual).isInstanceOf(CrmRequestTranslateException.class)
      .extracting(Throwable::getMessage)
      .isEqualTo("Unsupported request type");
  }

  @Test
  void createOperationWithEthIngressContainerSignalEnvelope() throws CrmRequestTranslateException {
    //GIVEN
    final CrmSegmentRequestDto expected = new CrmSegmentRequestDto();
    expected.neId = 1;
    expected.id = "someId";
    expected.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    expected.srcTp = "ptpAid";
    expected.dstTp = "ltpName";

    expected.setEthLabel(new Container(List.of(1, 2), List.of(1, 2)));
    expected.portParams = List.of(
      new OpticalParameters.SelectedParameter(ParameterName.GLQ, new Value.Enum("ETH-100G"))
    );
    expected.setSrcInterfaceType(InterfaceType.ENNI);
    expected.setDstInterfaceType(InterfaceType.UNSPECIFIED);

    var envelope = EnvelopeOuterClass.Envelope.newBuilder();

    var srcAddress = EnvelopeOuterClass.Address.newBuilder();
    var srcIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    srcIdentifier.setId("35123");
    srcAddress.setId(srcIdentifier);

    envelope.setSource(srcAddress);
    expected.setSrcAddress(srcAddress.build());

    var dstAddress = EnvelopeOuterClass.Address.newBuilder();
    var dstIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    dstIdentifier.setId(String.valueOf(expected.neId));
    dstAddress.setId(dstIdentifier);

    envelope.setDestination(dstAddress);
    expected.setDstAddress(dstAddress.build());

    var segmentRequest = SegmentRequestOuterClass.SegmentRequest.newBuilder();
    var operation = SegmentRequestOuterClass.Operation.newBuilder();
    operation.setId(expected.id);
    var create = SegmentRequestOuterClass.Create.newBuilder();
    create.setIsAdopt(expected.requestType == CrmSegmentRequestDto.RequestType.ADOPT);
    var config = ConnectionSegmentOuterClass.Config.newBuilder();
    var lane = ConnectionSegmentOuterClass.Lane.newBuilder();
    var srcEndpoint = ConnectionSegmentOuterClass.Endpoint.newBuilder();
    srcEndpoint.setTerminationPoint(expected.srcTp);
    srcEndpoint.getPortParamsBuilder().getParamsBuilder()
      .addKvp(MapOuterClass.KeyValuePair.newBuilder()
        .setK("key")
        .setV("value")
        .build());
    srcEndpoint.setInterfaceType(InterfaceTypeOuterClass.InterfaceType.ENNI);

    var dstEndpoint = ConnectionSegmentOuterClass.Endpoint.newBuilder();
    dstEndpoint.setTerminationPoint(expected.dstTp);
    dstEndpoint.setInterfaceType(InterfaceTypeOuterClass.InterfaceType.UNSPECIFIED);
    var label = Label.newBuilder();
    var multiLabel = MultiLabel.newBuilder();
    var labelType = SegmentRequestBuilder.newBuilder().createEthLabel(expected.getEthLabel());
    multiLabel.addLabel(labelType);
    label.setMultiLabel(multiLabel);
    dstEndpoint.setLabel(label);
    lane.setSource(srcEndpoint);
    lane.setDestination(dstEndpoint);
    config.setLane(lane);
    config.setSignal(SignalDescription.newBuilder()
      .setEth(SignalEthernet.newBuilder()
        .setSignalType(SignalEthernet.SignalType.ETH_SIGNAL_TYPE_100GBE)
        .build()
      ).build()
    );
    create.setConfig(config);
    operation.setCreate(create);
    segmentRequest.setOperation(operation);
    var segmentRequestObject = segmentRequest.build();
    envelope.setPayload(segmentRequestObject.toByteString());

    //WHEN
    var actual = SegmentRequestTranslator.translate(envelope.build());

    //THEN
    assertThat(actual).satisfies(o -> basicOperationPropertiesMatches(o, expected))
      .hasFieldOrPropertyWithValue("srcTp", expected.srcTp)
      .hasFieldOrPropertyWithValue("dstTp", expected.dstTp)
      .hasFieldOrPropertyWithValue("ethLabel", expected.getEthLabel())
      .hasFieldOrPropertyWithValue("srcInterfaceType", expected.getSrcInterfaceType())
      .hasFieldOrPropertyWithValue("dstInterfaceType", expected.getDstInterfaceType())
      .extracting(r -> r.portParams)
      .asList()
      .containsExactlyInAnyOrderElementsOf(expected.portParams);
  }

  @Test
  void createOperationWithEthEgressWholeSignalEnvelope() throws CrmRequestTranslateException {
    //GIVEN
    final CrmSegmentRequestDto expected = new CrmSegmentRequestDto();
    expected.neId = 1;
    expected.id = "someId";
    expected.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    expected.srcTp = "ltpName";
    expected.dstTp = "ptpAid";

    expected.setEthLabel(new Whole());
    expected.portParams = List.of(
      new OpticalParameters.SelectedParameter(ParameterName.GLQ, new Value.Enum("ETH-400G"))
    );
    expected.setSrcInterfaceType(InterfaceType.UNSPECIFIED);
    expected.setDstInterfaceType(InterfaceType.ENNI);

    var envelope = EnvelopeOuterClass.Envelope.newBuilder();

    var srcAddress = EnvelopeOuterClass.Address.newBuilder();
    var srcIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    srcIdentifier.setId("35123");
    srcAddress.setId(srcIdentifier);

    envelope.setSource(srcAddress);
    expected.setSrcAddress(srcAddress.build());

    var dstAddress = EnvelopeOuterClass.Address.newBuilder();
    var dstIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    dstIdentifier.setId(String.valueOf(expected.neId));
    dstAddress.setId(dstIdentifier);

    envelope.setDestination(dstAddress);
    expected.setDstAddress(dstAddress.build());

    var segmentRequest = SegmentRequestOuterClass.SegmentRequest.newBuilder();
    var operation = SegmentRequestOuterClass.Operation.newBuilder();
    operation.setId(expected.id);
    var create = SegmentRequestOuterClass.Create.newBuilder();
    create.setIsAdopt(expected.requestType == CrmSegmentRequestDto.RequestType.ADOPT);
    var config = ConnectionSegmentOuterClass.Config.newBuilder();
    var lane = ConnectionSegmentOuterClass.Lane.newBuilder();
    var srcEndpoint = ConnectionSegmentOuterClass.Endpoint.newBuilder();
    srcEndpoint.setTerminationPoint(expected.srcTp);
    var label = Label.newBuilder();
    var multiLabel = MultiLabel.newBuilder();
    var labelType = SegmentRequestBuilder.newBuilder().createEthLabel(expected.getEthLabel());
    multiLabel.addLabel(labelType);
    label.setMultiLabel(multiLabel);
    srcEndpoint.setLabel(label);
    srcEndpoint.setInterfaceType(InterfaceTypeOuterClass.InterfaceType.UNSPECIFIED);

    var dstEndpoint = ConnectionSegmentOuterClass.Endpoint.newBuilder();
    dstEndpoint.setTerminationPoint(expected.dstTp);
    dstEndpoint.getPortParamsBuilder().getParamsBuilder()
      .addKvp(MapOuterClass.KeyValuePair.newBuilder()
        .setK("key")
        .setV("value")
        .build());
    dstEndpoint.setInterfaceType(InterfaceTypeOuterClass.InterfaceType.ENNI);

    lane.setSource(srcEndpoint);
    lane.setDestination(dstEndpoint);
    config.setLane(lane);
    config.setSignal(SignalDescription.newBuilder()
      .setEth(SignalEthernet.newBuilder()
        .setSignalType(SignalEthernet.SignalType.ETH_SIGNAL_TYPE_400GBE)
        .build()
      ).build()
    );
    create.setConfig(config);
    operation.setCreate(create);
    segmentRequest.setOperation(operation);
    var segmentRequestObject = segmentRequest.build();
    envelope.setPayload(segmentRequestObject.toByteString());

    //WHEN
    var actual = SegmentRequestTranslator.translate(envelope.build());

    //THEN
    assertThat(actual).satisfies(o -> basicOperationPropertiesMatches(o, expected))
      .hasFieldOrPropertyWithValue("srcTp", expected.srcTp)
      .hasFieldOrPropertyWithValue("dstTp", expected.dstTp)
      .hasFieldOrPropertyWithValue("ethLabel", expected.getEthLabel())
      .hasFieldOrPropertyWithValue("srcInterfaceType", expected.getSrcInterfaceType())
      .hasFieldOrPropertyWithValue("dstInterfaceType", expected.getDstInterfaceType())
      .extracting(r -> r.portParams)
      .asList()
      .containsExactlyInAnyOrderElementsOf(expected.portParams);
  }

  @Test
  void createOperationWithWdmSignalEnvelope() throws CrmRequestTranslateException {
    //GIVEN
    final CrmSegmentRequestDto expected = new CrmSegmentRequestDto();
    expected.neId = 32355;
    expected.id = "theOperationId";
    expected.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    expected.srcTp = "theSrcTp";
    expected.dstTp = "theDstTp";
    expected.setMutableSegments(List.of("35", "63"));

    expected.channelSrcLabel = 191_243_000; // GHz
    expected.slotWidthSrcLabel = 50_000; // MHz
    expected.forwardSequenceNumber = 203;
    expected.reverseSequenceNumber = 201;
    expected.setPointDelta = 0D;
    expected.adminState = CrmSegmentRequestDto.AdminState.UNKNOWN;

    expected.portParams = List.of(
      new OpticalParameters.SelectedParameter(ParameterName.CHANNEL_BANDWIDTH, new Value.Numeric(new BigDecimal(expected.slotWidthSrcLabel), "MHz")),
      new OpticalParameters.SelectedParameter(ParameterName.CHANNEL, new Value.Numeric(new BigDecimal(String.valueOf(191243.0)), "GHz"))
    );

    var envelope = EnvelopeOuterClass.Envelope.newBuilder();

    var srcAddress = EnvelopeOuterClass.Address.newBuilder();
    var srcIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    srcIdentifier.setId("35123");
    srcAddress.setId(srcIdentifier);

    envelope.setSource(srcAddress);
    expected.setSrcAddress(srcAddress.build());

    var dstAddress = EnvelopeOuterClass.Address.newBuilder();
    var dstIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    dstIdentifier.setId(String.valueOf(expected.neId));
    dstAddress.setId(dstIdentifier);

    envelope.setDestination(dstAddress);
    expected.setDstAddress(dstAddress.build());

    var segmentRequest = SegmentRequestOuterClass.SegmentRequest.newBuilder();
    var operation = SegmentRequestOuterClass.Operation.newBuilder();
    operation.setId(expected.id);
    var create = SegmentRequestOuterClass.Create.newBuilder();
    create.addAllMutableSegments(expected.getMutableSegments());
    create.setIsAdopt(expected.requestType == CrmSegmentRequestDto.RequestType.ADOPT);
    var config = ConnectionSegmentOuterClass.Config.newBuilder();
    var lane = ConnectionSegmentOuterClass.Lane.newBuilder();
    var srcEndpoint = ConnectionSegmentOuterClass.Endpoint.newBuilder();
    srcEndpoint.setTerminationPoint(expected.srcTp);
    var label = Label.newBuilder();
    var multiLabel = MultiLabel.newBuilder();
    var labelType = SegmentRequestBuilder.newBuilder()
      .createWdmLabel(191243D, expected.srcTp, FlexgridParams.SlotWidth.SLOT_WIDTH_50G);
    multiLabel.addLabel(labelType);
    label.setMultiLabel(multiLabel);
    srcEndpoint.setLabel(label);

    var dstEndpoint = ConnectionSegmentOuterClass.Endpoint.newBuilder();
    dstEndpoint.setTerminationPoint(expected.dstTp);
    lane.setSource(srcEndpoint);
    lane.setDestination(dstEndpoint);
    config.setLane(lane);
    config.setForwardSequenceNumber(expected.forwardSequenceNumber);
    config.setReverseSequenceNumber(expected.reverseSequenceNumber);
    create.setConfig(config);
    operation.setCreate(create);
    segmentRequest.setOperation(operation);
    var segmentRequestObject = segmentRequest.build();
    envelope.setPayload(segmentRequestObject.toByteString());
    //WHEN
    var actual = SegmentRequestTranslator.translate(envelope.build());
    //THEN
    assertThat(actual).satisfies(o -> basicOperationPropertiesMatches(o, expected))
      .hasFieldOrPropertyWithValue("mutableSegments", expected.getMutableSegments())
      .hasFieldOrPropertyWithValue("srcTp", expected.srcTp)
      .hasFieldOrPropertyWithValue("dstTp", expected.dstTp)
      .hasFieldOrPropertyWithValue("channelSrcLabel", expected.channelSrcLabel)
      .hasFieldOrPropertyWithValue("slotWidthSrcLabel", expected.slotWidthSrcLabel)
      .hasFieldOrPropertyWithValue("forwardSequenceNumber", expected.forwardSequenceNumber)
      .hasFieldOrPropertyWithValue("reverseSequenceNumber", expected.reverseSequenceNumber)
      .hasFieldOrPropertyWithValue("setPointDelta", expected.setPointDelta)
      .hasFieldOrPropertyWithValue("adminState", expected.adminState)
      .extracting(r -> r.portParams)
      .asList()
      .containsExactlyInAnyOrderElementsOf(expected.portParams);
  }

  private void basicOperationPropertiesMatches(CrmSegmentRequestDto actual, CrmSegmentRequestDto expected) {
    assertThat(actual)
      .hasFieldOrPropertyWithValue("neId", expected.neId)
      .hasFieldOrPropertyWithValue("id", expected.id)
      .hasFieldOrPropertyWithValue("srcAddress", expected.getSrcAddress())
      .hasFieldOrPropertyWithValue("dstAddress", expected.getDstAddress())
      .hasFieldOrPropertyWithValue("requestType", expected.requestType);
  }


  @Test
  void createOperationWithGrayLabelEnvelope() throws CrmRequestTranslateException {
    //GIVEN
    final CrmSegmentRequestDto expected = new CrmSegmentRequestDto();
    expected.neId = 32355;
    expected.id = "theOperationId";
    expected.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    expected.srcTp = "theSrcTp";
    expected.dstTp = "theDstTp";
    expected.setMutableSegments(List.of("35", "63"));

    expected.channelSrcLabel = 191_243_000; // nanometers
    expected.slotWidthSrcLabel = 0; // MHz
    expected.forwardSequenceNumber = 203;
    expected.reverseSequenceNumber = 201;
    expected.setPointDelta = 0D;
    expected.adminState = CrmSegmentRequestDto.AdminState.UNKNOWN;

    expected.portParams = List.of(
      new OpticalParameters.SelectedParameter(ParameterName.GLQ, new Value.Enum("OTU2E")),
      new OpticalParameters.SelectedParameter(ParameterName.FEC, new Value.Enum("GFEC"))
    );

    var envelope = EnvelopeOuterClass.Envelope.newBuilder();

    var srcAddress = EnvelopeOuterClass.Address.newBuilder();
    var srcIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    srcIdentifier.setId("35123");
    srcAddress.setId(srcIdentifier);

    envelope.setSource(srcAddress);
    expected.setSrcAddress(srcAddress.build());

    var dstAddress = EnvelopeOuterClass.Address.newBuilder();
    var dstIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    dstIdentifier.setId(String.valueOf(expected.neId));
    dstAddress.setId(dstIdentifier);

    envelope.setDestination(dstAddress);
    expected.setDstAddress(dstAddress.build());

    var segmentRequest = SegmentRequestOuterClass.SegmentRequest.newBuilder();
    var operation = SegmentRequestOuterClass.Operation.newBuilder();
    operation.setId(expected.id);
    var create = SegmentRequestOuterClass.Create.newBuilder();
    create.addAllMutableSegments(expected.getMutableSegments());
    create.setIsAdopt(expected.requestType == CrmSegmentRequestDto.RequestType.ADOPT);
    var config = ConnectionSegmentOuterClass.Config.newBuilder();
    var lane = ConnectionSegmentOuterClass.Lane.newBuilder();
    var srcEndpoint = ConnectionSegmentOuterClass.Endpoint.newBuilder();
    srcEndpoint.setTerminationPoint(expected.srcTp);
    expected.portParams.stream()
      .map(p -> MapOuterClass.KeyValuePair.newBuilder()
        .setK(p.name().toString())
        .setV(((Value.Enum)p.value()).value())
        .build())
      .forEach(srcEndpoint.getPortParamsBuilder().getParamsBuilder()::addKvp);

    var label = Label.newBuilder();
    var multiLabel = MultiLabel.newBuilder();
    var labelType = SegmentRequestBuilder.newBuilder()
      .createGrayLabel(191_243_000D, expected.srcTp);
    multiLabel.addLabel(labelType);
    label.setMultiLabel(multiLabel);
    srcEndpoint.setLabel(label);

    var dstEndpoint = ConnectionSegmentOuterClass.Endpoint.newBuilder();
    dstEndpoint.setTerminationPoint(expected.dstTp);
    lane.setSource(srcEndpoint);
    lane.setDestination(dstEndpoint);
    config.setLane(lane);
    config.setForwardSequenceNumber(expected.forwardSequenceNumber);
    config.setReverseSequenceNumber(expected.reverseSequenceNumber);
    create.setConfig(config);
    operation.setCreate(create);
    segmentRequest.setOperation(operation);
    var segmentRequestObject = segmentRequest.build();
    envelope.setPayload(segmentRequestObject.toByteString());
    //WHEN
    var actual = SegmentRequestTranslator.translate(envelope.build());
    //THEN
    assertThat(actual).satisfies(o -> basicOperationPropertiesMatches(o, expected))
      .hasFieldOrPropertyWithValue("mutableSegments", expected.getMutableSegments())
      .hasFieldOrPropertyWithValue("srcTp", expected.srcTp)
      .hasFieldOrPropertyWithValue("dstTp", expected.dstTp)
      .hasFieldOrPropertyWithValue("channelSrcLabel", expected.channelSrcLabel)
      .hasFieldOrPropertyWithValue("slotWidthSrcLabel", expected.slotWidthSrcLabel)
      .hasFieldOrPropertyWithValue("forwardSequenceNumber", expected.forwardSequenceNumber)
      .hasFieldOrPropertyWithValue("reverseSequenceNumber", expected.reverseSequenceNumber)
      .hasFieldOrPropertyWithValue("setPointDelta", expected.setPointDelta)
      .hasFieldOrPropertyWithValue("adminState", expected.adminState)
      .extracting(r -> r.portParams)
      .asList()
      .containsExactlyInAnyOrderElementsOf(expected.portParams);
  }

  @Test
  void adoptOperationWithOtnSignalEnvelope() throws CrmRequestTranslateException {
    //GIVEN
    final CrmSegmentRequestDto expected = new CrmSegmentRequestDto();
    expected.neId = 32355;
    expected.id = "theOperationId";
    expected.requestType = CrmSegmentRequestDto.RequestType.ADOPT;
    expected.srcTp = "theSrcTp";
    expected.dstTp = "theDstTp";
    expected.setMutableSegments(List.of("8735"));

    expected.adminState = CrmSegmentRequestDto.AdminState.UNKNOWN;

    final String payloadQualifier = "ODUC2PA";
    expected.portParams = List.of(new OpticalParameters.SelectedParameter(ParameterName.GLQ, new Value.Enum(payloadQualifier)));

    var envelope = EnvelopeOuterClass.Envelope.newBuilder();

    var srcAddress = EnvelopeOuterClass.Address.newBuilder();
    var srcIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    srcIdentifier.setId("35123");
    srcAddress.setId(srcIdentifier);

    envelope.setSource(srcAddress);
    expected.setSrcAddress(srcAddress.build());

    var dstAddress = EnvelopeOuterClass.Address.newBuilder();
    var dstIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    dstIdentifier.setId(String.valueOf(expected.neId));
    dstAddress.setId(dstIdentifier);

    envelope.setDestination(dstAddress);
    expected.setDstAddress(dstAddress.build());

    var segmentRequest = SegmentRequestOuterClass.SegmentRequest.newBuilder();
    var operation = SegmentRequestOuterClass.Operation.newBuilder();
    operation.setId(expected.id);
    var create = SegmentRequestOuterClass.Create.newBuilder();
    create.addAllMutableSegments(expected.getMutableSegments());
    create.setIsAdopt(expected.requestType == CrmSegmentRequestDto.RequestType.ADOPT);
    var config = ConnectionSegmentOuterClass.Config.newBuilder();
    var lane = ConnectionSegmentOuterClass.Lane.newBuilder();
    var srcEndpoint = ConnectionSegmentOuterClass.Endpoint.newBuilder();
    srcEndpoint.setTerminationPoint(expected.srcTp);

    var dstEndpoint = ConnectionSegmentOuterClass.Endpoint.newBuilder();
    dstEndpoint.setTerminationPoint(expected.dstTp);
    lane.setSource(srcEndpoint);
    lane.setDestination(dstEndpoint);
    config.setLane(lane);
    config.setSignal(generateOtnSignalWithPayloadQualifier());
    create.setConfig(config);
    operation.setCreate(create);
    segmentRequest.setOperation(operation);
    var segmentRequestObject = segmentRequest.build();
    envelope.setPayload(segmentRequestObject.toByteString());
    //WHEN
    var actual = SegmentRequestTranslator.translate(envelope.build());
    //THEN
    assertThat(actual).satisfies(o -> basicOperationPropertiesMatches(o, expected))
      .hasFieldOrPropertyWithValue("mutableSegments", expected.getMutableSegments())
      .hasFieldOrPropertyWithValue("srcTp", expected.srcTp)
      .hasFieldOrPropertyWithValue("dstTp", expected.dstTp)
      .hasFieldOrPropertyWithValue("adminState", expected.adminState)
      .extracting(r -> r.portParams)
      .asList()
      .containsExactlyInAnyOrderElementsOf(expected.portParams);
  }

  private static SignalDescription.Builder generateOtnSignalWithPayloadQualifier() {
    var signalDescription = SignalDescription.newBuilder();
    var otn = SignalOtn.newBuilder();
    otn.setPayloadQualifier("ODUC2PA");
    signalDescription.setOtn(otn);
    return signalDescription;
  }

  @Test
  void deleteOperationEnvelope() throws CrmRequestTranslateException {
    //GIVEN
    final CrmSegmentRequestDto expected = new CrmSegmentRequestDto();
    expected.neId = 32355;
    expected.id = "theOperationId";
    expected.requestType = CrmSegmentRequestDto.RequestType.DELETE;
    expected.srcTp = "theSrcTp";
    expected.dstTp = "theDstTp";

    var envelope = EnvelopeOuterClass.Envelope.newBuilder();

    var srcAddress = EnvelopeOuterClass.Address.newBuilder();
    var srcIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    srcIdentifier.setId("35123");
    srcAddress.setId(srcIdentifier);

    envelope.setSource(srcAddress);
    expected.setSrcAddress(srcAddress.build());

    var dstAddress = EnvelopeOuterClass.Address.newBuilder();
    var dstIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    dstIdentifier.setId(String.valueOf(expected.neId));
    dstAddress.setId(dstIdentifier);

    envelope.setDestination(dstAddress);
    expected.setDstAddress(dstAddress.build());

    var segmentRequest = SegmentRequestOuterClass.SegmentRequest.newBuilder();
    var operation = SegmentRequestOuterClass.Operation.newBuilder();
    operation.setId(expected.id);
    var delete = SegmentRequestOuterClass.Delete.newBuilder();
    delete.setIsAbandon(expected.requestType == CrmSegmentRequestDto.RequestType.ABANDON);

    operation.setDelete(delete);
    segmentRequest.setOperation(operation);
    var segmentRequestObject = segmentRequest.build();
    envelope.setPayload(segmentRequestObject.toByteString());
    //WHEN
    var actual = SegmentRequestTranslator.translate(envelope.build());
    //THEN
    assertThat(actual).satisfies(o -> basicOperationPropertiesMatches(o, expected));
  }

  @Test
  void abandonOperationEnvelope() throws CrmRequestTranslateException {
    //GIVEN
    final CrmSegmentRequestDto expected = new CrmSegmentRequestDto();
    expected.neId = 32355;
    expected.id = "theOperationId";
    expected.requestType = CrmSegmentRequestDto.RequestType.ABANDON;
    expected.srcTp = "theSrcTp";
    expected.dstTp = "theDstTp";

    var envelope = EnvelopeOuterClass.Envelope.newBuilder();

    var srcAddress = EnvelopeOuterClass.Address.newBuilder();
    var srcIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    srcIdentifier.setId("35123");
    srcAddress.setId(srcIdentifier);

    envelope.setSource(srcAddress);
    expected.setSrcAddress(srcAddress.build());

    var dstAddress = EnvelopeOuterClass.Address.newBuilder();
    var dstIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    dstIdentifier.setId(String.valueOf(expected.neId));
    dstAddress.setId(dstIdentifier);

    envelope.setDestination(dstAddress);
    expected.setDstAddress(dstAddress.build());

    var segmentRequest = SegmentRequestOuterClass.SegmentRequest.newBuilder();
    var operation = SegmentRequestOuterClass.Operation.newBuilder();
    operation.setId(expected.id);
    var delete = SegmentRequestOuterClass.Delete.newBuilder();
    delete.setIsAbandon(expected.requestType == CrmSegmentRequestDto.RequestType.ABANDON);

    operation.setDelete(delete);
    segmentRequest.setOperation(operation);
    var segmentRequestObject = segmentRequest.build();
    envelope.setPayload(segmentRequestObject.toByteString());
    //WHEN
    var actual = SegmentRequestTranslator.translate(envelope.build());
    //THEN
    assertThat(actual).satisfies(o -> basicOperationPropertiesMatches(o, expected));
  }

  @ParameterizedTest(name = "[{index}] {0} is properly mapped to {1}")
  @MethodSource("powerEqualizeOperationEnvelopeInput")
  void powerEqualizeOperationEnvelope(int niBundleEqualizationDirection, PowerEqOperation expectedEqDirection) throws CrmRequestTranslateException {
    //GIVEN
    final CrmSegmentRequestDto expected = new CrmSegmentRequestDto();
    expected.neId = 32355;
    expected.id = "theOperationId";
    expected.requestType = CrmSegmentRequestDto.RequestType.POWEREQUALIZE;
    expected.srcTp = "theSrcTp";
    expected.dstTp = "theDstTp";

    expected.equalizeDirection = expectedEqDirection;

    var envelope = EnvelopeOuterClass.Envelope.newBuilder();

    var srcAddress = EnvelopeOuterClass.Address.newBuilder();
    var srcIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    srcIdentifier.setId("35123");
    srcAddress.setId(srcIdentifier);

    envelope.setSource(srcAddress);
    expected.setSrcAddress(srcAddress.build());

    var dstAddress = EnvelopeOuterClass.Address.newBuilder();
    var dstIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    dstIdentifier.setId(String.valueOf(expected.neId));
    dstAddress.setId(dstIdentifier);

    envelope.setDestination(dstAddress);
    expected.setDstAddress(dstAddress.build());

    var segmentRequest = SegmentRequestOuterClass.SegmentRequest.newBuilder();
    var operation = SegmentRequestOuterClass.Operation.newBuilder();
    operation.setId(expected.id);
    var powerEqualize = SegmentRequestOuterClass.PowerEqualize.newBuilder();
    powerEqualize.setOperationValue(niBundleEqualizationDirection);

    operation.setPowerEqualize(powerEqualize);
    segmentRequest.setOperation(operation);
    var segmentRequestObject = segmentRequest.build();
    envelope.setPayload(segmentRequestObject.toByteString());
    //WHEN
    var actual = SegmentRequestTranslator.translate(envelope.build());
    //THEN
    assertThat(actual).satisfies(o -> basicOperationPropertiesMatches(o, expected))
      .extracting(CrmSegmentRequestDto::getEqualizeDirection)
      .isEqualTo(expected.equalizeDirection);
  }

  static Stream<Arguments> powerEqualizeOperationEnvelopeInput() {
    return Stream.of(
      Arguments.of(ConnectionSegmentOuterClass.PowerEqualizationOperation.UNKNOWN.getNumber(), PowerEqOperation.UNKNOWN),
      Arguments.of(ConnectionSegmentOuterClass.PowerEqualizationOperation.EQUALIZE_FORWARD.getNumber(), PowerEqOperation.EQUALIZE_FORWARD),
      Arguments.of(ConnectionSegmentOuterClass.PowerEqualizationOperation.EQUALIZE_REVERSE.getNumber(), PowerEqOperation.EQUALIZE_REVERSE),
      Arguments.of(ConnectionSegmentOuterClass.PowerEqualizationOperation.ABORT.getNumber(), PowerEqOperation.ABORT),
      Arguments.of(-1, PowerEqOperation.UNRECOGNIZED),
      Arguments.of(949382, PowerEqOperation.UNRECOGNIZED)
    );
  }

  @ParameterizedTest(name = "[{index}] {0} is properly mapped to {1}")
  @MethodSource("setAdminStateOperationEnvelopeInput")
  void setAdminStateOperationEnvelope(ConnectionSegmentOuterClass.Config.AdminState niBundleAdminState,
                                      CrmSegmentRequestDto.AdminState expectedAdminState) throws CrmRequestTranslateException {
    //GIVEN
    final CrmSegmentRequestDto expected = new CrmSegmentRequestDto();
    expected.neId = 32355;
    expected.id = "theOperationId";
    expected.requestType = CrmSegmentRequestDto.RequestType.SETADMINSTATE;
    expected.srcTp = "theSrcTp";
    expected.dstTp = "theDstTp";

    expected.adminState = expectedAdminState;

    var envelope = EnvelopeOuterClass.Envelope.newBuilder();

    var srcAddress = EnvelopeOuterClass.Address.newBuilder();
    var srcIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    srcIdentifier.setId("35123");
    srcAddress.setId(srcIdentifier);

    envelope.setSource(srcAddress);
    expected.setSrcAddress(srcAddress.build());

    var dstAddress = EnvelopeOuterClass.Address.newBuilder();
    var dstIdentifier = EnvelopeOuterClass.Identifier.newBuilder();
    dstIdentifier.setId(String.valueOf(expected.neId));
    dstAddress.setId(dstIdentifier);

    envelope.setDestination(dstAddress);
    expected.setDstAddress(dstAddress.build());

    var segmentRequest = SegmentRequestOuterClass.SegmentRequest.newBuilder();
    var operation = SegmentRequestOuterClass.Operation.newBuilder();
    operation.setId(expected.id);
    var setAdminState = SegmentRequestOuterClass.SetAdminState.newBuilder();
    setAdminState.setState(niBundleAdminState);

    operation.setSetAdminState(setAdminState);
    segmentRequest.setOperation(operation);
    var segmentRequestObject = segmentRequest.build();
    envelope.setPayload(segmentRequestObject.toByteString());
    //WHEN
    var actual = SegmentRequestTranslator.translate(envelope.build());
    //THEN
    assertThat(actual).satisfies(o -> basicOperationPropertiesMatches(o, expected))
      .extracting(a -> a.adminState)
      .isEqualTo(expected.adminState);
  }

  static Stream<Arguments> setAdminStateOperationEnvelopeInput() {
    return Stream.of(
      Arguments.of(ConnectionSegmentOuterClass.Config.AdminState.UNKNOWN, CrmSegmentRequestDto.AdminState.UNKNOWN),
      Arguments.of(ConnectionSegmentOuterClass.Config.AdminState.DISABLED, CrmSegmentRequestDto.AdminState.DISABLED),
      Arguments.of(ConnectionSegmentOuterClass.Config.AdminState.ENABLED, CrmSegmentRequestDto.AdminState.ENABLED)
    );
  }

  @ParameterizedTest(name = "[{index}] {0} is properly mapped to {1}")
  @MethodSource("inputOperationCaseSource")
  void getOperationCase(CrmSegmentRequestDto.RequestType input, SegmentCallbackOuterClass.Response.OperationCase expect) {
    var actual = SegmentRequestTranslator.getOperationCase(input);
    assertThat(actual).isEqualTo(expect);
  }

  static Stream<Arguments> inputOperationCaseSource() {
    return Stream.of(
      Arguments.of(CrmSegmentRequestDto.RequestType.ADOPT, SegmentCallbackOuterClass.Response.OperationCase.CREATE),
      Arguments.of(CrmSegmentRequestDto.RequestType.CREATE, SegmentCallbackOuterClass.Response.OperationCase.CREATE),
      Arguments.of(CrmSegmentRequestDto.RequestType.ABANDON, SegmentCallbackOuterClass.Response.OperationCase.DELETE),
      Arguments.of(CrmSegmentRequestDto.RequestType.DELETE, SegmentCallbackOuterClass.Response.OperationCase.DELETE),
      Arguments.of(CrmSegmentRequestDto.RequestType.POWEREQUALIZE, SegmentCallbackOuterClass.Response.OperationCase.POWEREQUALIZE),
      Arguments.of(CrmSegmentRequestDto.RequestType.SETADMINSTATE, SegmentCallbackOuterClass.Response.OperationCase.SETADMINSTATE),
      Arguments.of(null, SegmentCallbackOuterClass.Response.OperationCase.OPERATION_NOT_SET),
      Arguments.of(CrmSegmentRequestDto.RequestType.UNKNOWN, SegmentCallbackOuterClass.Response.OperationCase.OPERATION_NOT_SET)
    );
  }
}