/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8;

import com.adva.nlms.resource.mediator.f8.internalpath.Complete;
import com.adva.nlms.resource.mediator.f8.internalpath.Container;
import com.adva.nlms.resource.mediator.f8.internalpath.Indistinct;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.Whole;
import ni.proto.inet.Label;
import ni.proto.inet.LabelType;
import ni.proto.inet.MultiLabel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class LabelMapperTest {

  private static Label generateOtnLabel(LabelType.OtnLabel in) {
    return Label.newBuilder()
      .setMultiLabel(MultiLabel.newBuilder()
        .addLabel(LabelType.newBuilder()
          .setOtnLabel(in)
          .build())
        .build())
      .build();
  }

  @Test
  void mapWholeLabel() {
    var label = LabelType.OtnLabel.Whole.newBuilder().build();
    Label in = generateOtnLabel(LabelType.OtnLabel.newBuilder().setWhole(label).build());
    var result = LabelMapper.map(in);
    var ref = new OtnLabel(new Whole());

    Assertions.assertEquals(1, result.multiLabel().label().size());
    Assertions.assertEquals(ref, result.multiLabel().label().get(0));
  }

  @Test
  void mapContainerLabel() {
    var label = LabelType.OtnLabel.Container.newBuilder().setId(3).build();
    Label in = generateOtnLabel(LabelType.OtnLabel.newBuilder().setContainer(label).build());
    var result = LabelMapper.map(in);
    var ref = new OtnLabel(new Container(3));

    Assertions.assertEquals(1, result.multiLabel().label().size());
    Assertions.assertEquals(ref, result.multiLabel().label().get(0));
  }

  @Test
  void mapCompleteLabel() {
    var label = LabelType.OtnLabel.Complete.newBuilder()
      .setTP(2)
      .addTS(4)
      .addTS(12)
      .build();
    Label in = generateOtnLabel(LabelType.OtnLabel.newBuilder().setComplete(label).build());
    var result = LabelMapper.map(in);
    var ref = new OtnLabel(new Complete(2, List.of(4, 12)));

    Assertions.assertEquals(1, result.multiLabel().label().size());
    Assertions.assertEquals(ref, result.multiLabel().label().get(0));
  }

  @Test
  void mapIndistinctLabel() {
    var label = LabelType.OtnLabel.Indistinct.newBuilder()
      .setTP(2)
      .setNumberOfTS(6)
      .build();
    Label in = generateOtnLabel(LabelType.OtnLabel.newBuilder().setIndistinct(label).build());
    var result = LabelMapper.map(in);
    var ref = new OtnLabel(new Indistinct(2, 6));

    Assertions.assertEquals(1, result.multiLabel().label().size());
    Assertions.assertEquals(ref, result.multiLabel().label().get(0));
  }
}
