/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import java.util.HashSet;
import java.util.Set;

class OutageState {

  private final Set<String> outageEntities;
  private boolean alarmRaised;

  OutageState() {
    outageEntities = new HashSet<>();
  }

  /**
   * Adds outage entity with the state if owning Termination Point alarm raised to Cpc
   *
   * @param entityUri uri of monitored entity
   */
  boolean planRaise(String entityUri) {
    outageEntities.add(entityUri);
    return alarmRaised;
  }

  void acceptRaise() {
    this.alarmRaised = true;
  }

  /**
   * Removes outage entity with given entityAid
   *
   * @param entityUri uri of monitored entity
   * @return true if all outage entities cleared(removed) for the owning Termination Point
   * false otherwise
   */
  boolean clear(String entityUri) {
    outageEntities.removeIf(oe -> oe.equals(entityUri));
    if (outageEntities.isEmpty()) {
      alarmRaised = false;
    }
    return !alarmRaised;
  }
}
