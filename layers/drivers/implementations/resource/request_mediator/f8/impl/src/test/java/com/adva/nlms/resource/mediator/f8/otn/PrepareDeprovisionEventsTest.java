/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.provider.api.CapabilityProvider;
import com.adva.infrastructure.capabilityprovider.capabilities.CapabilityConfiguration;
import com.adva.infrastructure.capabilityprovider.core.CapabilityProviderConfiguration;
import com.adva.infrastructure.capabilityprovider.mo.MoCapabilityProviderConfiguration;
import com.adva.nlms.common.config.EquipmentState;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.CtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.OperationalState;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.State;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.topology.NEData;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.resource.mediator.f8.events.CtpAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.CtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.mediator.f8.events.PtpAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.PtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncCreatedEvent;
import com.adva.nlms.resource.mediator.f8.otn.PrepareDeprovisionEvents;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnCardCapabilities;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnCardCapabilitiesImpl;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnDbResourcesFacade;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegment;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegmentRepository;
import com.adva.nlms.resource.mediator.f8.otn.api.in.SegmentID;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.List;
import java.util.Optional;
import java.util.Set;

class PrepareDeprovisionEventsTest {
  private final int neId = 42;
  private final String segmentId = "patisserie";
  private final OtnSegmentRepository otnSegmentRepository = Mockito.mock(OtnSegmentRepository.class);
  private final RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade = Mockito.mock(RrmOtnDbResourcesFacade.class);
  private final NEDataProvider neDataProvider = Mockito.mock(NEDataProvider.class);
  private static final CapabilityProvider capabilityProvider = new CapabilityProviderConfiguration()
    .capabilityProvider(new CapabilityConfiguration().capabilityRepository(), null, null);
  private static final MoCapabilityProvider moCapabilityProvider = new MoCapabilityProviderConfiguration().moCapabilityProvider();
  private final RrmOtnCardCapabilities rrmOtnCardCapabilities = new RrmOtnCardCapabilitiesImpl(neDataProvider, capabilityProvider, moCapabilityProvider);
  private final SegmentID segmentID = new SegmentID(neId, segmentId);

  private final String ptpC2Uri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2";
  private final String ctpC2OtuUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2/ctp/otu4";
  private final String ctpC2OduUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2/ctp/otu4/ctp/odu4";
  private final String ctpC2NUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/nw,1/ctp/ot200/ctp/odu4-1";
  private final String sncC2Uri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/sn/odu4/snc/1";

  private final String ptpC2_1Uri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-1";
  private final String ctpC2_1OtuUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-1/ctp/otu2";
  private final String ctpC2_1OduUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-1/ctp/otu2/ctp/odu2";
  private final String ctpC2_1NUri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/nw,2/ctp/otuc2/ctp/oduc2/ctp/odu2-1";
  private final String sncC2_1Uri = "/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/sn/odu2/snc/1";

  private final State entityState = new State(EquipmentState.IS, OperationalState.OUTAGE, List.of());

  private final PrepareDeprovisionEvents sut = new PrepareDeprovisionEvents(
    otnSegmentRepository, rrmOtnDbResourcesFacade, rrmOtnCardCapabilities);

  void init(OtnSegment segment, Aid plugAid, String ctpUri, String ptpUri, String portAid, String plugType) {
    Mockito.when(neDataProvider.getNeData(Mockito.anyInt()
    )).thenReturn(new NEData(neId, 0,
      "FSP 3000C", null, null, "6.5.1")
    );
    var cardRef = new CardRef(neId, new Aid("Module-2/1"),
      new Uri("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card"),
      "OF-2D16DCT", 3);
    var plugRef = PlugRef.builder()
      .setNeId(neId)
      .setAid(plugAid)
      .setUri("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/eqh/plgh,4/eq/plg")
      .setType(plugType)
      .build();

    Mockito.when(
      otnSegmentRepository.findOne(segmentID)
    ).thenReturn(segment);

    Mockito.when(
      rrmOtnDbResourcesFacade.findPlugFromCtp(neId, new Uri(ctpUri))
    ).thenReturn(
      Optional.of(plugRef)
    );

    Mockito.when(
      rrmOtnDbResourcesFacade.findPortFromCtp(neId, new Uri(ctpUri))
    ).thenReturn(
      Optional.of(new PtpRef(neId,
        new Aid("Port-2/1/c2"),
        new Uri("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2"),
        entityState
      ))
    );

    Mockito.when(
      rrmOtnDbResourcesFacade.findCardFromPlug(neId, plugAid)
    ).thenReturn(
      Optional.of(cardRef)
    );

    Mockito.when(
      rrmOtnDbResourcesFacade.findPtp(neId, new Aid(portAid))
    ).thenReturn(
      Optional.of(new PtpRef(neId, new Aid(portAid), new Uri(ptpUri), entityState))
    );

  }

  @Test
  void testCreatedExclusiveGroupWithC2port() {
    List<ProvisionLocalEvent> provisionLocalEvents = List.of(
      new PtpCreatedEvent(ptpC2Uri),
      new CtpCreatedEvent(ctpC2OtuUri),
      new PtpAdoptedEvent(ptpC2Uri),
      new CtpCreatedEvent(ctpC2OduUri),
      new CtpCreatedEvent(ctpC2NUri),
      new SncCreatedEvent(sncC2Uri, Set.of(ctpC2OduUri), Set.of(ctpC2NUri))
    );
    OtnSegment segment = new OtnSegment(segmentID, provisionLocalEvents);
    var plugAid = new Aid("Plug-2/1/c2");
    var portAid = "Port-2/1/c2";

    init(segment, plugAid, ctpC2OtuUri, ptpC2Uri, portAid, "QSFP28-112G-AOC-0100");

    Mockito.when(
      rrmOtnDbResourcesFacade.findAllCtpsFromPtp(neId, new Aid(portAid))
    ).thenReturn(
      List.of(new CtpRef(neId, new Aid(portAid), new Uri(ctpC2OtuUri), null))
    );

    List<ProvisionLocalEvent> referenceDeprovisionEvents = List.of(
      new PtpCreatedEvent(ptpC2Uri),
      new CtpCreatedEvent(ctpC2OtuUri),
      new PtpAdoptedEvent(ptpC2Uri),
      new CtpCreatedEvent(ctpC2OduUri),
      new CtpCreatedEvent(ctpC2NUri),
      new SncCreatedEvent(sncC2Uri, Set.of(ctpC2OduUri), Set.of(ctpC2NUri))
    );

    var deprovisionEvents = sut.prepareEvents(neId, segmentId);
    Assertions.assertEquals(deprovisionEvents, referenceDeprovisionEvents);
  }

  @Test
  void testCreatedExclusiveGroupWithC2_1port() {
    List<ProvisionLocalEvent> provisionLocalEvents = List.of(
      new PtpCreatedEvent(ptpC2_1Uri),
      new CtpCreatedEvent(ctpC2_1OtuUri),
      new PtpAdoptedEvent(ptpC2_1Uri),
      new CtpCreatedEvent(ctpC2_1OduUri),
      new CtpCreatedEvent(ctpC2_1NUri),
      new SncCreatedEvent(sncC2_1Uri, Set.of(ctpC2_1OduUri), Set.of(ctpC2_1NUri))
    );
    OtnSegment segment = new OtnSegment(segmentID, provisionLocalEvents);
    var plugAid = new Aid("Plug-2/1/c2");
    var portAid = "Port-2/1/c2-1";

    init(segment, plugAid, ctpC2_1OtuUri, ptpC2_1Uri, portAid, "QSFP28-103G-PSM4-SM-MPO");

    Mockito.when(
      rrmOtnDbResourcesFacade.findPtp(neId, new Aid("Port-2/1/c2-2"))
    ).thenReturn(
      Optional.of(new PtpRef(neId, new Aid("Port-2/1/c2-2"), new Uri("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-2"), entityState))
    );

    Mockito.when(
      rrmOtnDbResourcesFacade.findPtp(neId, new Aid("Port-2/1/c2-3"))
    ).thenReturn(
      Optional.of(new PtpRef(neId, new Aid("Port-2/1/c2-3"), new Uri("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-3"), entityState))
    );

    Mockito.when(
      rrmOtnDbResourcesFacade.findPtp(neId, new Aid("Port-2/1/c2-4"))
    ).thenReturn(
      Optional.of(new PtpRef(neId, new Aid("Port-2/1/c2-4"), new Uri("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-4"), entityState))
    );

    Mockito.when(
      rrmOtnDbResourcesFacade.findAllCtpsFromPtp(neId, new Aid(portAid))
    ).thenReturn(
      List.of(new CtpRef(neId, new Aid(portAid), new Uri(ctpC2_1OtuUri), null))
    );

    List<ProvisionLocalEvent> referenceDeprovisionEvents = List.of(
      new PtpCreatedEvent("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-1"),
      new PtpCreatedEvent("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-2"),
      new PtpCreatedEvent("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-3"),
      new PtpCreatedEvent("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-4"),
      new CtpCreatedEvent(ctpC2_1OtuUri),
      new PtpAdoptedEvent(ptpC2_1Uri),
      new CtpCreatedEvent(ctpC2_1OduUri),
      new CtpCreatedEvent(ctpC2_1NUri),
      new SncCreatedEvent(sncC2_1Uri, Set.of(ctpC2_1OduUri), Set.of(ctpC2_1NUri))
    );

    var deprovisionEvents = sut.prepareEvents(neId, segmentId);
    Assertions.assertEquals(deprovisionEvents, referenceDeprovisionEvents);
  }


  @Test
  void testAdoptedExclusiveGroupWithC2_1port() {
    List<ProvisionLocalEvent> provisionLocalEvents = List.of(
      new CtpAdoptedEvent(ctpC2_1OtuUri),
      new CtpAdoptedEvent(ctpC2_1OduUri),
      new CtpAdoptedEvent(ctpC2_1NUri),
      new SncAdoptedEvent(sncC2_1Uri, Set.of(ctpC2_1OduUri), Set.of(ctpC2_1NUri))
    );
    OtnSegment segment = new OtnSegment(segmentID, provisionLocalEvents);
    var plugAid = new Aid("Plug-2/1/c2");
    var portAid = "Port-2/1/c2-1";

    init(segment, plugAid, ctpC2_1OtuUri, ptpC2_1Uri, portAid, "QSFP28-103G-PSM4-SM-MPO");

    Mockito.when(
      rrmOtnDbResourcesFacade.findPtp(neId, new Aid("Port-2/1/c2-2"))
    ).thenReturn(
      Optional.of(new PtpRef(neId, new Aid("Port-2/1/c2-2"), new Uri("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-2"), entityState))
    );

    Mockito.when(
      rrmOtnDbResourcesFacade.findPtp(neId, new Aid("Port-2/1/c2-3"))
    ).thenReturn(
      Optional.of(new PtpRef(neId, new Aid("Port-2/1/c2-3"), new Uri("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-3"), entityState))
    );

    Mockito.when(
      rrmOtnDbResourcesFacade.findPtp(neId, new Aid("Port-2/1/c2-4"))
    ).thenReturn(
      Optional.of(new PtpRef(neId, new Aid("Port-2/1/c2-4"), new Uri("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-4"), entityState))
    );

    Mockito.when(
      rrmOtnDbResourcesFacade.findAllCtpsFromPtp(neId, new Aid(portAid))
    ).thenReturn(
      List.of(new CtpRef(neId, new Aid(portAid), new Uri(ctpC2_1OtuUri), null))
    );

    List<ProvisionLocalEvent> referenceDeprovisionEvents = List.of(
      new PtpCreatedEvent("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-1"),
      new PtpCreatedEvent("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-2"),
      new PtpCreatedEvent("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-3"),
      new PtpCreatedEvent("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-4"),
      new CtpAdoptedEvent(ctpC2_1OtuUri),
      new CtpAdoptedEvent(ctpC2_1OduUri),
      new CtpAdoptedEvent(ctpC2_1NUri),
      new SncAdoptedEvent(sncC2_1Uri, Set.of(ctpC2_1OduUri), Set.of(ctpC2_1NUri))
    );

    var deprovisionEvents = sut.prepareEvents(neId, segmentId);
    Assertions.assertEquals(deprovisionEvents, referenceDeprovisionEvents);
  }

  @Test
  void testCreatedExclusiveGroupWithC2_1portOtherPortOccupied() {
    List<ProvisionLocalEvent> provisionLocalEvents = List.of(
      new PtpCreatedEvent(ptpC2_1Uri),
      new CtpCreatedEvent(ctpC2_1OtuUri),
      new PtpAdoptedEvent(ptpC2_1Uri),
      new CtpCreatedEvent(ctpC2_1OduUri),
      new CtpCreatedEvent(ctpC2_1NUri),
      new SncCreatedEvent(sncC2_1Uri, Set.of(ctpC2_1OduUri), Set.of(ctpC2_1NUri))
    );
    OtnSegment segment = new OtnSegment(segmentID, provisionLocalEvents);
    var plugAid = new Aid("Plug-2/1/c2");
    var portAid = "Port-2/1/c2-1";

    init(segment, plugAid, ctpC2_1OtuUri, ptpC2_1Uri, portAid, "QSFP28-112G-AOC-0100");

    Mockito.when(
      rrmOtnDbResourcesFacade.findPtp(neId, new Aid("Port-2/1/c2-2"))
    ).thenReturn(
      Optional.of(new PtpRef(neId, new Aid("Port-2/1/c2-2"), new Uri("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-2"), entityState))
    );

    Mockito.when(
      rrmOtnDbResourcesFacade.findPtp(neId, new Aid("Port-2/1/c2-3"))
    ).thenReturn(
      Optional.of(new PtpRef(neId, new Aid("Port-2/1/c2-3"), new Uri("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-3"), entityState))
    );

    Mockito.when(
      rrmOtnDbResourcesFacade.findPtp(neId, new Aid("Port-2/1/c2-4"))
    ).thenReturn(
      Optional.of(new PtpRef(neId, new Aid("Port-2/1/c2-4"), new Uri("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-4"), entityState))
    );

    Mockito.when(
      rrmOtnDbResourcesFacade.findAllCtpsFromPtp(neId, new Aid(portAid))
    ).thenReturn(List.of(new CtpRef(neId, new Aid(portAid), new Uri("/mit/me/1/eqh/shelf,2/eqh/slot,1/eq/card/ptp/cl,2-2"), entityState)));

    List<ProvisionLocalEvent> referenceDeprovisionEvents = List.of(
      new CtpCreatedEvent(ctpC2_1OtuUri),
      new PtpAdoptedEvent(ptpC2_1Uri),
      new CtpCreatedEvent(ctpC2_1OduUri),
      new CtpCreatedEvent(ctpC2_1NUri),
      new SncCreatedEvent(sncC2_1Uri, Set.of(ctpC2_1OduUri), Set.of(ctpC2_1NUri))
    );

    var deprovisionEvents = sut.prepareEvents(neId, segmentId);
    Assertions.assertEquals(deprovisionEvents, referenceDeprovisionEvents);
  }

}
