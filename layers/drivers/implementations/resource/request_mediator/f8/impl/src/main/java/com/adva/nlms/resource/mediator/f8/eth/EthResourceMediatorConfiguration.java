/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.eth;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.provider.api.CapabilityProvider;
import com.adva.nlms.inf.api.PredefinedObserver;
import com.adva.nlms.mediation.mo.inventory.resources.CrossConnectResources;
import com.adva.nlms.mediation.mo.inventory.resources.CtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.PlugResources;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.resource.mediator.f8.ProtectionGroupFinder;
import com.adva.nlms.resource.mediator.f8.adapters.persistence.InMemoryEthSegmentRepository;
import com.adva.nlms.resource.mediator.f8.eth.api.in.EthSegmentRepository;
import com.adva.nlms.resource.mediator.f8.lock.NetworkTransactionWrapper;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
class EthResourceMediatorConfiguration {
  @Bean
  RrmEthDbResourcesFacade ethRrmResourcesFacade(CtpResources ctpResources, PlugResources plugResources,
                                                PtpResources ptpResources, CrossConnectResources crossConnectResources) {
    return new RrmEthDbResourcesFacadeImpl(ctpResources, plugResources, ptpResources, crossConnectResources);
  }

  @Bean
  RrmEthCardCapabilities ethRrmCardCapabilities(CapabilityProvider capabilityProvider, MoCapabilityProvider moCapabilityProvider,
                                                NEDataProvider neDataProvider) {
    return new RrmEthCardCapabilitiesImpl(capabilityProvider, moCapabilityProvider, neDataProvider);
  }

  @Bean
  EthResourceRequestMediator ethResourceRequestMediator(RrmEthDbResourcesFacade rrmEthDbResourcesFacade,
                                                        Provision provisionApi,
                                                        MoCapabilityProvider moCapabilityProvider,
                                                        EthSegmentRepository ethSegmentRepository,
                                                        RrmEthCardCapabilities rrmEthCardCapabilities,
                                                        NetworkTransactionWrapper networkTransactionWrapper,
                                                        ProtectionGroupFinder protectionGroupFinder
  ) {
    return new EthResourceRequestMediatorImpl(
      rrmEthDbResourcesFacade,
      new EthCimProvisionRequestParametersCreatorImpl(moCapabilityProvider),
      provisionApi,
      createProvisionRequestExecutor(provisionApi, ethSegmentRepository, networkTransactionWrapper),
      ethSegmentRepository,
      rrmEthCardCapabilities,
      protectionGroupFinder);
  }

  @Bean
  EthSegmentRepository inMemoryEthSegmentRepository() {
    return new InMemoryEthSegmentRepository();
  }

  @Bean
  @PredefinedObserver
  RrmEthNeObserver rrmEthNeObserver(EthResourceRequestMediator ethResourceRequestMediator) {
    return new RrmEthNeObserver(ethResourceRequestMediator);
  }

  private RequestExecutor createProvisionRequestExecutor(Provision provisionApi, EthSegmentRepository ethSegmentRepository,
                                                         NetworkTransactionWrapper networkTransactionWrapper) {
    return new RequestExecutor(ethSegmentRepository, provisionApi, networkTransactionWrapper);
  }
}
