/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.resource.crm.model.NeObserver;

class RrmOtnNeObserver extends NeObserver {
  private final OtnResourceRequestMediator otnResourceRequestMediator;

  RrmOtnNeObserver(OtnResourceRequestMediator otnResourceRequestMediator) {
    this.otnResourceRequestMediator = otnResourceRequestMediator;
  }

  @Override
  protected void handleNeRemoval(int neId) {
    otnResourceRequestMediator.clearSegmentCache(neId);
  }
}
