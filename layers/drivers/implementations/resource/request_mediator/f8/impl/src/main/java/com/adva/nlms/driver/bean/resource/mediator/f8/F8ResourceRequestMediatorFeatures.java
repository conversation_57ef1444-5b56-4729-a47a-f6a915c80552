/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.driver.bean.resource.mediator.f8;

import com.adva.nlms.driver.api.in.registry.AbstractDriverFeatures;
import com.adva.nlms.driver.api.in.registry.DriverFeaturesDDI;
import com.adva.nlms.mediation.bean.provider.api.BeanProvider;
import com.adva.nlms.mediation.bean.provider.api.BeanProviderAPI;
import com.adva.nlms.resource.crm.model.CrmCtrlTaskScheduler;
import com.adva.nlms.resource.mediator.f8.NiCallbackSender;
import com.adva.nlms.resource.mediator.f8.ProvisionAdapter;
import com.adva.nlms.resource.mediator.f8.RRMConfiguration;
import com.adva.nlms.resource.mediator.api.in.ResourceRequestMediator;
import com.adva.nlms.resource.mediator.f8.eth.EthResourceRequestMediator;
import com.adva.nlms.resource.mediator.f8.otn.OtnResourceRequestMediator;
import com.adva.nlms.resource.mediator.f8.wdm.WdmResourceRequestMediator;
import org.springframework.stereotype.Component;

@Component
class F8ResourceRequestMediatorFeatures extends AbstractDriverFeatures implements DriverFeaturesDDI {
  protected static final Integer VERSION = 1;
  protected static final String DRIVER_ID = "F8_RESOURCE_REQUEST_MEDIATOR_DRIVER";
  protected static final String QUALIFIER = "F8_RESOURCE_REQUEST_MEDIATOR"; // qualifier is used in delegator

  @Override
  public Integer getVersion() {
    return VERSION;
  }

  @Override
  public String getID() {
    return DRIVER_ID;
  }

  @Override
  protected void initialize() {
    // ResourceRequestMediator can't be declared as Spring Bean,
    // because delegate Bean, implementing the same interface already exist in the SpringContext
    addFeature(
      ResourceRequestMediator.class,
      QUALIFIER,
      () -> {
        BeanProviderAPI beanProvider = BeanProvider.get();
        WdmResourceRequestMediator wdmResourceRequestMediator = beanProvider.getBean(WdmResourceRequestMediator.class);
        OtnResourceRequestMediator otnResourceRequestMediator = beanProvider.getBean(OtnResourceRequestMediator.class);
        EthResourceRequestMediator ethResourceRequestMediator = beanProvider.getBean(EthResourceRequestMediator.class);
        NiCallbackSender niCallbackSender = beanProvider.getBean(NiCallbackSender.class);
        CrmCtrlTaskScheduler crmCtrlTaskScheduler = beanProvider.getBean(CrmCtrlTaskScheduler.class);
        ProvisionAdapter provision = beanProvider.getBean(ProvisionAdapter.class);
        return new RRMConfiguration().resourceRequestMediator(wdmResourceRequestMediator,
          otnResourceRequestMediator,
          ethResourceRequestMediator,
          niCallbackSender,
          crmCtrlTaskScheduler,
          provision);
      }
    );
  }
}
