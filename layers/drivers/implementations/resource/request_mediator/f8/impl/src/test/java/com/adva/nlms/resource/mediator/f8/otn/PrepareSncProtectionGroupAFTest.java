/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.provider.api.CapabilityProvider;
import com.adva.infrastructure.capabilityprovider.capabilities.CapabilityConfiguration;
import com.adva.infrastructure.capabilityprovider.core.CapabilityProviderConfiguration;
import com.adva.infrastructure.capabilityprovider.mo.MoCapabilityProviderConfiguration;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.CtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.LineProtectionGroupRef;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.topology.NEData;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.resource.mediator.f8.AssignRoles;
import com.adva.nlms.resource.mediator.f8.ProtectionSettings;
import com.adva.nlms.resource.mediator.f8.events.CtpAutocreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.mediator.f8.events.PtpAdoptedEvent;
import com.adva.nlms.resource.mediator.f8.events.PtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncAutocreatedEvent;
import com.adva.nlms.resource.mediator.f8.internalpath.ForkPlacement;
import com.adva.nlms.resource.mediator.f8.otn.PrepareProtectionGroupRequest;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnCardCapabilities;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnCardCapabilitiesImpl;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnDbResourcesFacade;
import com.adva.nlms.resource.mediator.f8.otn.SncProtectionGroupRequest;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegment;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegmentRepository;
import com.adva.nlms.resource.mediator.f8.otn.api.in.ProtectionData;
import com.adva.nlms.resource.mediator.f8.otn.api.in.SegmentID;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.nlms.resource.provision.f8.api.in.ResilienceDto;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.mockito.Mockito.mock;

class PrepareSncProtectionGroupAFTest {

  private final int neId = 42;
  private final String workingLeg = "working";
  private final String protectionLeg = "protection";
  private final ProtectionSettings protectionSettings = new ProtectionSettings(null, "bidir", null, null);
  private final AssignRoles assignRoles = new AssignRoles(workingLeg, protectionLeg, protectionSettings);
  private final Provision provisionApi = Mockito.mock(Provision.class);
  private final OtnSegmentRepository otnSegmentRepository = Mockito.mock(OtnSegmentRepository.class);
  private final RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade = Mockito.mock(RrmOtnDbResourcesFacade.class);
  private final NEDataProvider neDataProvider = mock(NEDataProvider.class);
  private static final CapabilityProvider capabilityProvider = new CapabilityProviderConfiguration()
    .capabilityProvider(new CapabilityConfiguration().capabilityRepository(), null, null);
  private static final MoCapabilityProvider moCapabilityProvider = new MoCapabilityProviderConfiguration().moCapabilityProvider();
  private final RrmOtnCardCapabilities rrmOtnCardCapabilities = new RrmOtnCardCapabilitiesImpl(neDataProvider, capabilityProvider, moCapabilityProvider);
  private final String clientOTUUri = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,6/ctp/et10";
  private final String clientODUUri = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,6/ctp/et10/ctp/odu2e";
  private final String network1Uri = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,1/ctp/otu2e/ctp/odu2e";
  private final String network2Uri = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,2/ctp/otu2e/ctp/odu2e";
  private final String sncUri2 = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/sn/odu2e/snc/2";
  private final Uri sncUri2Uri = new Uri(sncUri2);
  private final SegmentID workingSegmentId = new SegmentID(neId, workingLeg);
  private final SegmentID protectionSegmentId = new SegmentID(neId, protectionLeg);
  private final String protectionGroupAid = "Protection-2/1/odu2-1";

  private final PrepareProtectionGroupRequest sut = new PrepareProtectionGroupRequest(
    neId, assignRoles, provisionApi, otnSegmentRepository, rrmOtnDbResourcesFacade, rrmOtnCardCapabilities);

  @Test
  void testCreate_p1portFirstProvisioned() {
    ResilienceDto resilienceDto = new ResilienceDto(true, null, null, null);
    List<ProvisionLocalEvent> workingEvents = List.of(
      new CtpAutocreatedEvent(clientOTUUri),
      new CtpAutocreatedEvent(clientODUUri),
      new CtpAutocreatedEvent(network1Uri),
      new PtpAdoptedEvent("this should be ignored"),
      new PtpCreatedEvent("this should be filtered out"),
      new SncAutocreatedEvent(sncUri2, Set.of(clientODUUri), Set.of(network1Uri))
    );
    OtnSegment working = new OtnSegment(new SegmentID(neId, workingLeg), workingEvents, new ProtectionData(ForkPlacement.DESTINATION));

    List<ProvisionLocalEvent> protectionEvents = List.of(
      new CtpAutocreatedEvent(clientOTUUri),
      new PtpAdoptedEvent("this should be ignored"),
      new CtpAutocreatedEvent(clientODUUri),
      new CtpAutocreatedEvent(network2Uri),
      new PtpCreatedEvent("this should be filtered out"),
      new SncAutocreatedEvent(sncUri2, Set.of(clientODUUri), Set.of(network1Uri, network2Uri))
    );
    OtnSegment protection = new OtnSegment(new SegmentID(neId, protectionLeg), protectionEvents, new ProtectionData(ForkPlacement.DESTINATION));

    Mockito.when(otnSegmentRepository.findOne(workingSegmentId)
    ).thenReturn(
      working
    );
    Mockito.when(otnSegmentRepository.findOne(protectionSegmentId)
    ).thenReturn(
      protection
    );
    Mockito.when(rrmOtnDbResourcesFacade.findProtectionGroupBySnc(neId, sncUri2Uri)
    ).thenReturn(
      Optional.empty()
    );
    Mockito.when(rrmOtnDbResourcesFacade.findCtp(neId, new Uri(clientODUUri))
    ).thenReturn(
      Optional.of(new CtpRef(neId, new Aid("ODU2E-1/1/p6/et10/odu2e"), new Uri(clientODUUri), null))
    );
    Mockito.when(rrmOtnDbResourcesFacade.findCardFromCtp(neId, new Uri(clientODUUri)))
      .thenReturn(
        Optional.of(new CardRef(neId, new Aid("Module-1/1"), new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card"), "AF-4X4XGT", 1, "transp"))
      );
    Mockito.when(rrmOtnDbResourcesFacade.findPlugFromCtp(neId, new Uri(clientODUUri)))
      .thenReturn(
        Optional.of(PlugRef.builder()
          .setNeId(neId)
          .setAid("Plug-1/1/p6")
          .setUri("/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/eqh/plgh,8/eq/plg")
          .setType("SFP+-11GU-850I-MM-LC-TIN")
          .build())
      );
    Mockito.when(neDataProvider.getNeData(neId)).thenReturn(
      new NEData(neId, 1, "AF-4X4XGT", null, "", "7.1.1"));
    Mockito.when(rrmOtnDbResourcesFacade.findPortFromCtp(neId, new Uri(clientODUUri)))
      .thenReturn(
        Optional.of(new PtpRef(neId, new Aid("Port-1/1/p6"), new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,6"), null))
      );
    SncProtectionGroupRequest sncProtectionGroupRequest = new SncProtectionGroupRequest(neId, sncUri2, network1Uri, network2Uri, resilienceDto, "", provisionApi);
    Assertions.assertEquals(sncProtectionGroupRequest, sut.prepareRequest());
  }

  @Test
  void testCreate_p2portFirstProvisioned() {
    ResilienceDto resilienceDto = new ResilienceDto(true, null, null, null);
    List<ProvisionLocalEvent> workingEvents = List.of(
      new CtpAutocreatedEvent(clientOTUUri),
      new PtpAdoptedEvent("this should be ignored"),
      new CtpAutocreatedEvent(clientODUUri),
      new CtpAutocreatedEvent(network2Uri),
      new PtpCreatedEvent("this should be filtered out"),
      new SncAutocreatedEvent(sncUri2, Set.of(clientODUUri), Set.of(network2Uri))
    );
    OtnSegment working = new OtnSegment(new SegmentID(neId, workingLeg), workingEvents, new ProtectionData(ForkPlacement.DESTINATION));

    List<ProvisionLocalEvent> protectionEvents = List.of(
      new CtpAutocreatedEvent(clientOTUUri),
      new CtpAutocreatedEvent(clientODUUri),
      new CtpAutocreatedEvent(network1Uri),
      new PtpAdoptedEvent("this should be ignored"),
      new PtpCreatedEvent("this should be filtered out"),
      new SncAutocreatedEvent(sncUri2, Set.of(clientODUUri), Set.of(network1Uri, network2Uri))
    );
    OtnSegment protection = new OtnSegment(new SegmentID(neId, protectionLeg), protectionEvents, new ProtectionData(ForkPlacement.DESTINATION));

    Mockito.when(otnSegmentRepository.findOne(workingSegmentId)
    ).thenReturn(
      working
    );
    Mockito.when(otnSegmentRepository.findOne(protectionSegmentId)
    ).thenReturn(
      protection
    );
    Mockito.when(rrmOtnDbResourcesFacade.findCtp(neId, new Uri(clientODUUri))
    ).thenReturn(
      Optional.of(new CtpRef(neId, new Aid("ODU2E-1/1/p6/et10/odu2e"), new Uri(clientODUUri), null))
    );
    Mockito.when(rrmOtnDbResourcesFacade.findCardFromCtp(neId, new Uri(clientODUUri)))
      .thenReturn(
        Optional.of(new CardRef(neId, new Aid("Module-1/1"), new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card"), "AF-4X4XGT", 1, "transp"))
      );
    Mockito.when(rrmOtnDbResourcesFacade.findPlugFromCtp(neId, new Uri(clientODUUri)))
      .thenReturn(
        Optional.of(PlugRef.builder()
          .setNeId(neId)
          .setAid("Plug-1/1/p6")
          .setUri("/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/eqh/plgh,8/eq/plg")
          .setType("SFP+-11GU-850I-MM-LC-TIN")
          .build())
      );
    Mockito.when(neDataProvider.getNeData(neId)).thenReturn(
      new NEData(neId, 1, "AF-4X4XGT", null, "", "7.1.1"));
    Mockito.when(rrmOtnDbResourcesFacade.findPortFromCtp(neId, new Uri(clientODUUri)))
      .thenReturn(
        Optional.of(new PtpRef(neId, new Aid("Port-1/1/p6"), new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,6"), null))
      );

    SncProtectionGroupRequest sncProtectionGroupRequest = new SncProtectionGroupRequest(neId, sncUri2, network2Uri, network1Uri, resilienceDto, "", provisionApi);
    Assertions.assertEquals(sncProtectionGroupRequest, sut.prepareRequest());
  }

  @Test
  void testAdoptProtectionGroup() {
    List<ProvisionLocalEvent> workingEvents = List.of(
      new CtpAutocreatedEvent(clientOTUUri),
      new CtpAutocreatedEvent(clientODUUri),
      new CtpAutocreatedEvent(network1Uri),
      new SncAutocreatedEvent(sncUri2, Set.of(clientODUUri), Set.of(network1Uri))
    );
    OtnSegment working = new OtnSegment(new SegmentID(neId, workingLeg), workingEvents, new ProtectionData(ForkPlacement.DESTINATION));

    List<ProvisionLocalEvent> protectionEvents = List.of(
      new CtpAutocreatedEvent(clientOTUUri),
      new CtpAutocreatedEvent(clientODUUri),
      new CtpAutocreatedEvent(network2Uri),
      new SncAutocreatedEvent(sncUri2, Set.of(clientODUUri), Set.of(network1Uri, network2Uri))
    );
    OtnSegment protection = new OtnSegment(new SegmentID(neId, protectionLeg), protectionEvents, new ProtectionData(ForkPlacement.DESTINATION));

    Mockito.when(
      otnSegmentRepository.findOne(workingSegmentId)
    ).thenReturn(
      working
    );
    Mockito.when(
      otnSegmentRepository.findOne(protectionSegmentId)
    ).thenReturn(
      protection
    );
    Mockito.when(
      rrmOtnDbResourcesFacade.findProtectionGroupBySnc(neId, sncUri2Uri)
    ).thenReturn(
      Optional.of(new LineProtectionGroupRef(neId,
        new Aid(protectionGroupAid), new Uri(protectionGroupAid),
        new Aid(network1Uri), new Uri(network1Uri),
        new Aid(network2Uri), new Uri(network2Uri),
        new Aid(sncUri2), sncUri2Uri,
        null, null, null, null))
    );

    SncProtectionGroupRequest sncProtectionGroupRequest = new SncProtectionGroupRequest(neId, protectionGroupAid, provisionApi, true);
    Assertions.assertEquals(sncProtectionGroupRequest, sut.prepareRequest());
  }

}
