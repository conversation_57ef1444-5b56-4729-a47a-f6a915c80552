/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.common.config.EquipmentState;
import com.adva.nlms.mediation.common.AuthorizationException;
import com.adva.nlms.mediation.common.UntrustedCertificateException;
import com.adva.nlms.mediation.mo.inventory.f8.PMScanner;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.OperationalState;
import com.adva.nlms.mediation.mo.inventory.resources.Ptp;
import com.adva.nlms.mediation.mo.inventory.resources.PtpBuilder;
import com.adva.nlms.mediation.mo.inventory.resources.State;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.ne.comm.rest.RestConfigurationException;
import com.adva.nlms.resource.crm.model.CrmCtrlTaskScheduler;
import com.adva.nlms.resource.mediator.f8.PowerEqOperation;
import com.adva.nlms.resource.mediator.f8.wdm.LaserOnDelayConditionRepository;
import com.adva.nlms.resource.mediator.f8.wdm.LaserPowerWatcher;
import com.adva.nlms.resource.mediator.f8.wdm.LaserPowerWatcherContext;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.WdmSegment;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LaserPowerWatcherMaxLODClearAttemptHitTest {

  private static final int NE_ID = 93392;
  private static final String PTP_AID = "ptp-aid";
  private static final String PTP_URI = "ptp-uri";
  private static final PowerEqOperation POWER_EQUALIZATION_OPERATION = PowerEqOperation.EQUALIZE_FORWARD;

  @Mock
  private PMScanner pmScanner;
  @Mock
  private LaserOnDelayConditionRepository lodRepository;
  @Mock
  private Consumer<WdmSegment> action;

  private LaserPowerWatcher sut;

  private final CrmCtrlTaskScheduler testScheduler = new CrmCtrlTaskScheduler() {

    @Override
    public void initThreadPools() {
    }

    @Override
    public void submitProvisioningTask(int neId, Runnable task) {
      task.run();
    }

    @Override
    public Future<?> submitTask(Runnable task) {
      return null;
    }

    @Override
    public Future<?> scheduleTask(Runnable task, long delay, TimeUnit unit) {
      task.run();
      return null;
    }

    @Override
    public void schedulePeriodicTask(Runnable task, long period, TimeUnit unit) {
    }
  };
  private LaserPowerWatcherContext context;

  @BeforeEach
  public void setUp() {
    sut = new LaserPowerWatcher(testScheduler, pmScanner, lodRepository);
    WdmSegment segment = WdmSegment.newSegment()
      .withNeId(NE_ID)
      .build();
    Ptp ptp = PtpBuilder.newBuilder()
      .withNeId(NE_ID)
      .withAid(new Aid(PTP_AID))
      .withUri(new Uri(PTP_URI))
      .withState(new State(EquipmentState.UNKNOWN, OperationalState.NORMAL, List.of()))
      .withOpticalSetpoint(0)
      .build();
    context = Mockito.spy(new LaserPowerWatcherContext(segment, ptp, POWER_EQUALIZATION_OPERATION));

  }


  @Test
  void whenLaserIsOnOrLODRaised_LODClearAttemptsHit() throws AuthorizationException, UntrustedCertificateException, RestConfigurationException {
    //GIVEN
    when(lodRepository.isRaised(NE_ID, PTP_URI)).thenReturn(false);
    when(pmScanner.scanLaserPower(NE_ID, PTP_URI)).thenReturn(
      2.0F, 4.1F, 4.4F, 5.0F, 1.0F, 8.0F, 5.0F, 1.0F, 2.0F, 3.0F, 4.0F, 7.0F, 9.0F, 4.0F, 7.0F, 9.0F, 1.0F, 4.0F, 9.0F, 9.0F,
      2.0F, 4.1F, 4.4F, 5.0F, 1.0F, 8.0F, 5.0F, 1.0F, 2.0F, 3.0F, 4.0F, 7.0F, 9.0F, 4.0F, 7.0F, 9.0F, 1.0F, 4.0F, 9.0F, 9.0F,
      2.0F, 4.1F, 4.4F, 5.0F, 1.0F, 8.0F, 5.0F, 1.0F, 2.0F, 3.0F, 4.0F, 7.0F, 9.0F, 4.0F, 7.0F, 9.0F, 1.0F, 4.0F, 9.0F, 9.0F,
      2.0F, 4.1F, 4.4F, 5.0F, 1.0F, 8.0F, 5.0F, 1.0F, 2.0F, 3.0F, 4.0F, 7.0F, 9.0F, 4.0F, 7.0F, 9.0F, 1.0F, 4.0F, 9.0F, 9.0F
    );
    //WHEN
    sut.whenLaserIsOnOrLODRaised(context, action);

    //THEN
    assertThat(context)
      .extracting(LaserPowerWatcherContext::getSegment)
      .extracting(WdmSegment::getWaitingForLaserOnDelayClear)
      .isEqualTo(POWER_EQUALIZATION_OPERATION);

    assertThat(context).extracting(LaserPowerWatcherContext::shouldScanLaserPower)
      .isEqualTo(false); // attempts hit LaserPowerWatcherContext.MAX_LASER_POWER_PROBE_ATTEMPTS
    assertThat(context).extracting(LaserPowerWatcherContext::getLaserPower)
      .isEqualTo(9.0F); //last scanned value

    verify(action, times(1)).accept(context.getSegment());
  }

  @Test
  void whenLaserIsOnAndStableButSetPointIsNotReached_continueEqualizationAfterSeveralAttempts() throws AuthorizationException, UntrustedCertificateException, RestConfigurationException {
    //GIVEN
    final float actualLaserPower = -7.0F;
    when(lodRepository.isRaised(NE_ID, PTP_URI)).thenReturn(false);
    when(pmScanner.scanLaserPower(NE_ID, PTP_URI)).thenReturn(actualLaserPower);
    //WHEN
    sut.whenLaserIsOnOrLODRaised(context, action);

    //THEN
    assertThat(context)
      .extracting(LaserPowerWatcherContext::getSegment)
      .extracting(WdmSegment::getWaitingForLaserOnDelayClear)
      .isEqualTo(POWER_EQUALIZATION_OPERATION);

    assertThat(context).extracting(LaserPowerWatcherContext::isMaxLodClearCheckAttemptHit)
      .isEqualTo(false);
    assertThat(context).extracting(LaserPowerWatcherContext::shouldScanLaserPower)
      .isEqualTo(true);
    assertThat(context).extracting(LaserPowerWatcherContext::getLaserPower)
      .isEqualTo(actualLaserPower);
    assertThat(context).extracting(LaserPowerWatcherContext::isLaserPowerStable)
      .isEqualTo(true);

    verify(action, times(1)).accept(context.getSegment());
  }

  @Test
  void whenLaserIsOnAndPowerLevelToLow_LODClearAttemptsHit() throws AuthorizationException, UntrustedCertificateException, RestConfigurationException {
    //GIVEN
    when(lodRepository.isRaised(NE_ID, PTP_URI)).thenReturn(false);
    when(pmScanner.scanLaserPower(NE_ID, PTP_URI)).thenReturn(
      -40.0F
    );
    //WHEN
    sut.whenLaserIsOnOrLODRaised(context, action);

    //THEN
    assertThat(context)
      .extracting(LaserPowerWatcherContext::getSegment)
      .extracting(WdmSegment::getWaitingForLaserOnDelayClear)
      .isEqualTo(POWER_EQUALIZATION_OPERATION);

    assertThat(context).extracting(LaserPowerWatcherContext::shouldScanLaserPower)
      .isEqualTo(false); // attempts hit LaserPowerWatcherContext.MAX_LASER_POWER_PROBE_ATTEMPTS
    assertThat(context).extracting(LaserPowerWatcherContext::getLaserPower)
      .isEqualTo(-40.0F); //last scanned value

    verify(action, times(1)).accept(context.getSegment());
  }

  @Test
  void whenLaserIsOnAndPowerLevelCorrectAndNearSetpoint_LODClearAttemptsHit() throws AuthorizationException, UntrustedCertificateException, RestConfigurationException {
    //GIVEN
    when(lodRepository.isRaised(NE_ID, PTP_URI)).thenReturn(false);
    when(pmScanner.scanLaserPower(NE_ID, PTP_URI)).thenReturn(
      0.2F
    );
    //WHEN
    sut.whenLaserIsOnOrLODRaised(context, action);

    //THEN
    assertThat(context)
      .extracting(LaserPowerWatcherContext::getSegment)
      .extracting(WdmSegment::getWaitingForLaserOnDelayClear)
      .isEqualTo(POWER_EQUALIZATION_OPERATION);

    assertThat(context).extracting(LaserPowerWatcherContext::shouldScanLaserPower)
      .isEqualTo(true);
    verify(context, times(3)).shouldScanLaserPower();
    assertThat(context).extracting(LaserPowerWatcherContext::getLaserPower)
      .isEqualTo(0.2F);

    verify(action, times(1)).accept(context.getSegment());
  }

  @Test
  void whenLaserIsOnAndPowerLevelCorrectAndAwayFromSetpoint_LODClearAttemptsHit() throws AuthorizationException, UntrustedCertificateException, RestConfigurationException {
    //GIVEN
    when(lodRepository.isRaised(NE_ID, PTP_URI)).thenReturn(false);
    when(pmScanner.scanLaserPower(NE_ID, PTP_URI)).thenReturn(
      5.0F
    );
    //WHEN
    sut.whenLaserIsOnOrLODRaised(context, action);

    //THEN
    assertThat(context)
      .extracting(LaserPowerWatcherContext::getSegment)
      .extracting(WdmSegment::getWaitingForLaserOnDelayClear)
      .isEqualTo(POWER_EQUALIZATION_OPERATION);

    assertThat(context).extracting(LaserPowerWatcherContext::shouldScanLaserPower)
      .isEqualTo(true);
    assertThat(context).extracting(LaserPowerWatcherContext::isLaserPowerStable)
      .isEqualTo(true);
    assertThat(context).extracting(LaserPowerWatcherContext::getLaserPower)
      .isEqualTo(5.0F);

    verify(action, times(1)).accept(context.getSegment());
  }

  @Test
  void whenLaserIsOnAndPowerLevelNull_LODClearAttemptsHit() throws AuthorizationException, UntrustedCertificateException, RestConfigurationException {
    //GIVEN
    when(lodRepository.isRaised(NE_ID, PTP_URI)).thenReturn(false);
    when(pmScanner.scanLaserPower(NE_ID, PTP_URI)).thenReturn(
      null
    );
    //WHEN
    sut.whenLaserIsOnOrLODRaised(context, action);

    //THEN
    assertThat(context)
      .extracting(LaserPowerWatcherContext::getSegment)
      .extracting(WdmSegment::getWaitingForLaserOnDelayClear)
      .isEqualTo(POWER_EQUALIZATION_OPERATION);

    assertThat(context).extracting(LaserPowerWatcherContext::isLaserPowerStable)
      .isEqualTo(false);
    assertThat(context).extracting(LaserPowerWatcherContext::isMaxLodClearCheckAttemptHit)
      .isEqualTo(true);


    verify(action, times(1)).accept(context.getSegment());
  }


}
