/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardClusterRef;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.CrossConnectRef;
import com.adva.nlms.mediation.mo.inventory.resources.Ctp;
import com.adva.nlms.mediation.mo.inventory.resources.CtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.ProtectionGroupRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;

import java.util.List;
import java.util.Optional;
import java.util.Set;

interface RrmOtnDbResourcesFacade {
  CardRef findCardFromModule(int neId, Aid aid);

  Optional<Ctp> findCtpWithExtendedDesc(int neId, Uri uri);

  Optional<CardRef> findCardFromCtp(int neId, Uri uri);

  List<Ctp> listCtpsOnCard(int neId, Aid aid);

  Optional<CardRef> findCardFromPlug(int neId, Aid aid);

  Optional<CardRef> findCardFromPtp(int neId, Aid aid);

  Optional<PlugRef> findPlug(int neId, Aid aid);

  Optional<CardClusterRef> findAssociatedCardCluster(int neId, Aid aid);

  Optional<CrossConnectRef> findCrossConnect(int neId, Uri uri);

  Optional<ProtectionGroupRef> findProtectionGroupBySnc(int neId, Uri uri);

  Optional<PtpRef> findPtp(int neId, Aid aid);

  Optional<PtpRef> findPtp(int neId, Uri uri);

  Optional<CtpRef> findCtp(int neId, Uri uri);

  List<CtpRef> findAllCtpsFromPtp(int neId, Aid aid);

  List<CtpRef> findAllCtpsForParentUri(int neId, Uri uri);

  List<Ctp> findAllCtpsWithExtendedDescFromPtp(int neId, Aid aid);

  Optional<PlugRef> findPlugFromPtp(int neId, Aid aid);

  Optional<PlugRef> findPlugFromCtp(int neId, Uri uri);

  Optional<PtpRef> findPortFromCtp(int neId, Uri uri);

  Optional<Uri> findSncUriByUriPrefixAndEndpoints(int neId, String uriPrefix, Set<String> aEndpointURIs,
                                                     Set<String> zEndpointURIs);

  Optional<Uri> findSncUriByUriPrefixAndSingleSideEndpoints(int neId, String uriPrefix, Set<String> aEndpointURIs);

  Optional<Uri> findCtpUriByUriPrefixAndParams(int neId, String uriPrefix, int tp, List<Long> ts);

  Optional<Uri> getExistingProtectedSncUri(int neId, String uriPrefix, String aEndpointUri, String zEndpointUri);

  Optional <String> findCccpGroupName(int neId, Uri uri);

  Optional<ProtectionGroupRef> findCccProtectionGroupByWorkingOrProtectingCtpUri(int neId, Uri ctpUri);
}
