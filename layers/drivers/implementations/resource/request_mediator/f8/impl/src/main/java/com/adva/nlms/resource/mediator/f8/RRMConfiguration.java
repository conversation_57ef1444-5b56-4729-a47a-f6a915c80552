/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.nlms.mediation.config.f8.croma.api.CromaMOService;
import com.adva.nlms.mediation.config.f8.entity.protection.ProtectionGroupF8Service;
import com.adva.nlms.mediation.mo.inventory.resources.CtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.ProtectionGroupResources;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.resource.advertisement.api.out.CrmMessageSender;
import com.adva.nlms.resource.crm.model.CrmCtrlTaskScheduler;
import com.adva.nlms.resource.crm.model.CrmNiTranslator;
import com.adva.nlms.resource.mediator.api.in.ResourceRequestMediator;
import com.adva.nlms.resource.mediator.f8.eth.EthResourceRequestMediator;
import com.adva.nlms.resource.mediator.f8.otn.OtnResourceRequestMediator;
import com.adva.nlms.resource.mediator.f8.wdm.WdmResourceRequestMediator;
import com.adva.nlms.resource.provision.f8.api.in.CimProvisionRequestParametersCreator;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.topology.manager.api.in.TopologyNodeApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RRMConfiguration {

  @Bean
  public NiCallbackSender niCallbackSender(CrmMessageSender webSocket, CrmNiTranslator crmNiTranslator) {
    return new NiCallbackSender(webSocket, crmNiTranslator);
  }

  @Bean
  public ProvisionAdapter provisionAdapter(
    ProtectionGroupF8Service protectionGroupF8Service,
    TopologyNodeApi topologyNodeApi,
    Provision provision,
    MoCapabilityProvider moCapabilityProvider,
    CtpResources ctpResources,
    PtpResources ptpResources
  ) {
    return new ProvisionAdapter(protectionGroupF8Service, topologyNodeApi, provision, moCapabilityProvider,
      ctpResources, ptpResources);
  }

  @Bean
  public ProtectionGroupFinder protectionGroupFinder(CromaMOService cromaMOService, ProtectionGroupResources protectionGroupResources) {
    return new ProtectionGroupFinder(cromaMOService, protectionGroupResources);
  }

  //this is used by DDI and can't be declared as @Bean
  public ResourceRequestMediator resourceRequestMediator(
    WdmResourceRequestMediator wdmResourceRequestMediator,
    OtnResourceRequestMediator otnResourceRequestMediator,
    EthResourceRequestMediator ethResourceRequestMediator,
    NiCallbackSender niCallbackSender,
    CrmCtrlTaskScheduler crmCtrlTaskScheduler,
    ProvisionAdapter provisionAdapter) {
    return new ResourceRequestMediatorImpl(
      wdmResourceRequestMediator,
      otnResourceRequestMediator,
      ethResourceRequestMediator,
      niCallbackSender,
      crmCtrlTaskScheduler,
      provisionAdapter);
  }

  @Bean
  public CimProvisionRequestParametersCreator cimProvisionRequestParametersCreator(MoCapabilityProvider moCapabilityProvider) {
    return new CimProvisionRequestParametersCreatorImpl(moCapabilityProvider);
  }
}
