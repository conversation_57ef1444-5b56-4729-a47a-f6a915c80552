/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm.api.in;

import java.util.List;

public interface RrmSegmentRepository {
  void store(WdmSegment segment);

  void remove(WdmSegment segment);

  void removeSegments(int neId);

  WdmSegment getSegment(String segmentRequestId, int neId);

  WdmSegment getSegmentBySlcID(int neId, int slcId);

  List<WdmSegment> getSegmentByAgatesInterface(int neId, String agatesInterface);

  List<WdmSegment> findSegmentsByPtpTransponderUri(int neId, String ptpTransponderUri);

  List<WdmSegment> findSegmentsByCtpTransponderUri(int neId, String ctpTransponderUri);

  List<WdmSegment> findSegmentsByProtectionGroupUri(int neId, String protectionGroupUri);

  List<WdmSegment> findSegmentsByEndpoints(int neId, String srcTp, String dstTp, int channel);
}
