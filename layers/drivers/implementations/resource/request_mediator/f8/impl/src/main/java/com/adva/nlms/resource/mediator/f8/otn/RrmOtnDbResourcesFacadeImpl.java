/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardClusterRef;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.CardResources;
import com.adva.nlms.mediation.mo.inventory.resources.CrossConnectRef;
import com.adva.nlms.mediation.mo.inventory.resources.CrossConnectResources;
import com.adva.nlms.mediation.mo.inventory.resources.Ctp;
import com.adva.nlms.mediation.mo.inventory.resources.CtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.CtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.PlugResources;
import com.adva.nlms.mediation.mo.inventory.resources.ProtectionGroupRef;
import com.adva.nlms.mediation.mo.inventory.resources.ProtectionGroupResources;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;

import java.util.List;
import java.util.Optional;
import java.util.Set;

class RrmOtnDbResourcesFacadeImpl implements RrmOtnDbResourcesFacade {
  private final CtpResources ctpResources;
  private final PtpResources ptpResources;
  private final PlugResources plugResources;
  private final CardResources cardResources;
  private final CrossConnectResources crossConnectResources;
  private final ProtectionGroupResources protectionGroupResources;

  public RrmOtnDbResourcesFacadeImpl(CtpResources ctpResources, PtpResources ptpResources,
                                     PlugResources plugResources, CardResources cardResources,
                                     CrossConnectResources crossConnectResources, ProtectionGroupResources protectionGroupResources) {
    this.ctpResources = ctpResources;
    this.ptpResources = ptpResources;
    this.plugResources = plugResources;
    this.cardResources = cardResources;
    this.crossConnectResources = crossConnectResources;
    this.protectionGroupResources = protectionGroupResources;
  }

  @Override
  public CardRef findCardFromModule(int neId, Aid aid) {
    return cardResources.getCard(neId, aid);
  }

  @Override
  public Optional<Ctp> findCtpWithExtendedDesc(int neId, Uri uri) {
    return ctpResources.findCtpExtended(neId, uri);
  }

  @Override
  public Optional<CardRef> findCardFromCtp(int neId, Uri uri) {
    return ctpResources.findCard(neId, uri);
  }

  @Override
  public List<Ctp> listCtpsOnCard(int neId, Aid aid) {
    return cardResources.listCtps(neId, aid);
  }

  @Override
  public Optional<CardRef> findCardFromPlug(int neId, Aid aid) {
    return plugResources.findCard(neId, aid);
  }

  @Override
  public Optional<CardRef> findCardFromPtp(int neId, Aid aid) {
    return ptpResources.findCard(neId, aid);
  }

  @Override
  public Optional<PlugRef> findPlug(int neId, Aid aid) {
    return Optional.ofNullable(plugResources.getPlug(neId, aid));
  }

  @Override
  public Optional<CardClusterRef> findAssociatedCardCluster(int neId, Aid aid) {
    return cardResources.getAssociatedCardCluster(neId, aid);
  }

  @Override
  public Optional<CrossConnectRef> findCrossConnect(int neId, Uri uri) {
    return crossConnectResources.findCrossConnect(neId, uri);
  }

  @Override
  public Optional<ProtectionGroupRef> findProtectionGroupBySnc(int neId, Uri uri) {
    return crossConnectResources.getProtectionGroup(neId, uri);
  }

  @Override
  public Optional<String> findCccpGroupName(int neId, Uri uri) {
    return protectionGroupResources.findCccpGroupName(neId, uri);
  }

  @Override
  public Optional<ProtectionGroupRef> findCccProtectionGroupByWorkingOrProtectingCtpUri(int neId, Uri ctpUri) {
    return protectionGroupResources.findCccProtectionGroupByWorkingOrProtectingCtpUri(neId, ctpUri);
  }

  @Override
  public Optional<PtpRef> findPtp(int neId, Aid aid) {
    return  ptpResources.findPtp(neId, aid);
  }

  @Override
  public Optional<PtpRef> findPtp(int neId, Uri uri) {
    return ptpResources.findPtp(neId, uri);
  }

  @Override
  public Optional<CtpRef> findCtp(int neId, Uri uri) {
    return ctpResources.findCtp(neId, uri);
  }

  @Override
  public List<CtpRef> findAllCtpsFromPtp(int neId, Aid aid) {
    return ptpResources.findAllCtps(neId, aid);
  }

  @Override
  public List<CtpRef> findAllCtpsForParentUri(int neId, Uri uri) {
    return ptpResources.findAllCtps(neId, uri);
  }

  @Override
  public List<Ctp> findAllCtpsWithExtendedDescFromPtp(int neId, Aid aid) {
    return ptpResources.findAllCtpsWithExtendedDesc(neId, aid);
  }

  @Override
  public Optional<PlugRef> findPlugFromPtp(int neId, Aid aid) {
    return ptpResources.findPlug(neId, aid);
  }

  @Override
  public Optional<PlugRef> findPlugFromCtp(int neId, Uri uri) {
    return ctpResources.findPlug(neId, uri);
  }

  @Override
  public Optional<PtpRef> findPortFromCtp(int neId, Uri uri) {
    return ctpResources.findPort(neId, uri);
  }

  @Override
  public Optional<Uri> getExistingProtectedSncUri(int neId, String uriPrefix, String aEndpointUri, String zEndpointUri) {
    return crossConnectResources.getExistingProtectedSncUri(neId, uriPrefix, aEndpointUri, zEndpointUri);
  }

  @Override
  public Optional<Uri> findSncUriByUriPrefixAndEndpoints(int neId, String uriPrefix, Set<String> aEndpointURIs,
                                                            Set<String> zEndpointURIs) {
    return crossConnectResources.getSncUriByUriPrefixAndEndpoints(neId, uriPrefix, aEndpointURIs, zEndpointURIs);
  }

  @Override
  public Optional<Uri> findSncUriByUriPrefixAndSingleSideEndpoints(int neId, String uriPrefix, Set<String> aEndpointURIs) {
    return crossConnectResources.getSncUriByUriPrefixAndSingleSideEndpoints(neId, uriPrefix, aEndpointURIs);
  }

  @Override
  public Optional<Uri> findCtpUriByUriPrefixAndParams(int neId, String uriPrefix, int tp, List<Long> ts) {
    return ctpResources.getCtpUriByUriPrefixAndParams(neId, uriPrefix, tp, ts);
  }

}
