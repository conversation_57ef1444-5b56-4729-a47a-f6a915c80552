/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.Map;

class OutageMonitor {

  private static final Logger log = LogManager.getLogger(OutageMonitor.class);
  private static final Logger outageTracer = LogManager.getLogger("ALARM_TRACER");
  private final Map<SegmentTerminationPoint, OutageState> terminationPointOutages = new HashMap<>();

  /**
   * Adds alarm notify for given entity in the context of TerminationPoint
   * If it's a first alarm notify to be raised, then RAISED state is not set until alarmRaised method is invoked
   *
   * @return true if any alarm notify already RAISED
   * false otherwise
   */
  boolean planAlarm(SegmentTerminationPoint segmentTerminationPoint, String entityUri) {
    outageTracer.trace("planAlarm()-> entityUri={}, segmentTerminationPoint={}", entityUri, segmentTerminationPoint);
    OutageState outageState = terminationPointOutages.get(segmentTerminationPoint);
    boolean alarmAlreadyRaised;
    if (outageState == null) {
      OutageState newOutageState = new OutageState();
      alarmAlreadyRaised = newOutageState.planRaise(entityUri);
      terminationPointOutages.put(segmentTerminationPoint, newOutageState);
      outageTracer.trace("outage state NOT found, alarmAlreadyRaised={}", alarmAlreadyRaised);
    } else {
      alarmAlreadyRaised = outageState.planRaise(entityUri);
      outageTracer.trace("outage state found, alarmAlreadyRaised={}", alarmAlreadyRaised);
    }
    log.info("Outage entity added: {}, URI:{}, alarmRaised:{}", segmentTerminationPoint, entityUri, alarmAlreadyRaised);
    return alarmAlreadyRaised;
  }

  /**
   * Sets alarm notify as RAISED for given TerminationPoint
   */
  void alarmRaised(SegmentTerminationPoint segmentTerminationPoint) {
    OutageState outageState = terminationPointOutages.get(segmentTerminationPoint);
    if (outageState != null) {
      outageState.acceptRaise();
    }
  }

  /**
   * Clears alarm notify for given entity in the context of TerminationPoint
   *
   * @return {@code true} if every alarm for outage state entity have been cleared,
   * {@code false} otherwise
   */
  boolean clearAlarm(SegmentTerminationPoint segmentTerminationPoint, String entityUri) {
    outageTracer.trace("clearAlarm()-> segmentTerminationPoint={}, entityUri={}", segmentTerminationPoint, entityUri);
    OutageState outageState = terminationPointOutages.get(segmentTerminationPoint);
    if (outageState != null) {
      boolean alarmCleared = outageState.clear(entityUri);
      if (alarmCleared) {
        terminationPointOutages.remove(segmentTerminationPoint);
        outageTracer.trace("outage state removed");
      }
      log.debug("Outage entity removed: {}, URI:{}, alarm cleared: {}", segmentTerminationPoint, entityUri, alarmCleared);
      outageTracer.trace("clearAlarm()<- return={}", alarmCleared);
      return alarmCleared;
    }
    outageTracer.trace("clearAlarm()<- return={}", false);
    return false;
  }

  void removeOutagesForSegment(int neId, String segmentId) {
    terminationPointOutages.keySet()
      .removeIf(key -> key.neId() == neId && key.segmentId().equals(segmentId));
  }

  void removeOutagesForNe(int neId) {
    terminationPointOutages.keySet()
      .removeIf(key -> key.neId() == neId);
  }
}
