/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.mediation.config.f8.croma.slc.api.SlcEqSt;
import com.adva.nlms.mediation.ec.model.aos.croma.SlcEqualizeStatusType;
import com.adva.nlms.resource.mediator.f8.NiCallbackSender;
import com.adva.nlms.resource.mediator.f8.wdm.EqlzSlcNotificationHandler;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.CrmNodePosition;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.WdmSegment;
import ni.proto.connection_segment.ConnectionSegmentOuterClass;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

class EqlzSlcNotificationHandlerTest {

  @ParameterizedTest
  @EnumSource(value = CrmNodePosition.class, names = {"INGRESS", "TRANSIT_REVERSED"})
  void testHandleEqualizationDoneNotificationWithSuccess(CrmNodePosition crmNodePosition) {
    // given
    WdmSegment segment = WdmSegment.newSegment()
      .withNodePosition(crmNodePosition)
      .withAendEqExpected(true)
      .build();
    SlcEqSt slcEqSt = new SlcEqSt(SlcEqualizeStatusType.SUCCESS, null, System.currentTimeMillis());
    NiCallbackSender niCallbackSender = mock(NiCallbackSender.class);
    EqlzSlcNotificationHandler eqlzSlcNotificationHandler = new EqlzSlcNotificationHandler(niCallbackSender, segment, true, slcEqSt);
    // when
    eqlzSlcNotificationHandler.handleEqualizationDone();
    // then
    verify(niCallbackSender, times(1))
      .sendEqualizationSuccess(ConnectionSegmentOuterClass.PowerEqualizationOperation.EQUALIZE_REVERSE, segment);
  }

  @Test
  void testHandleEqualizationDoneNotificationWithFailure() {
    // Given
    WdmSegment segment = WdmSegment.newSegment()
      .withAendEqExpected(true)
      .build();
    SlcEqSt slcEqSt = new SlcEqSt(SlcEqualizeStatusType.INGRESS_SWITCH_TIMEOUT, null, System.currentTimeMillis());
    NiCallbackSender niCallbackSender = mock(NiCallbackSender.class);
    // When
    EqlzSlcNotificationHandler eqlzSlcNotificationHandler = new EqlzSlcNotificationHandler(niCallbackSender, segment, true, slcEqSt);
    eqlzSlcNotificationHandler.handleEqualizationDone();
    // Then
    verify(niCallbackSender, times(1)).sendEqualizationFailure(any(), any(), any());
  }

  @Test
  void testHandleEqualizationDoneNotificationWithInvalidState() {
    // Given
    WdmSegment segment = mock(WdmSegment.class);
    SlcEqSt slcEqSt = new SlcEqSt(SlcEqualizeStatusType.SUCCESS, null, null);
    NiCallbackSender niCallbackSender = mock(NiCallbackSender.class);
    // When
    EqlzSlcNotificationHandler eqlzSlcNotificationHandler = new EqlzSlcNotificationHandler(niCallbackSender, segment, true, slcEqSt);
    eqlzSlcNotificationHandler.handleEqualizationDone();
    // Then
    verify(niCallbackSender, never()).sendEqualizationSuccess(any(), any());
    verify(niCallbackSender, never()).sendEqualizationFailure(any(), any(), any());
  }
}