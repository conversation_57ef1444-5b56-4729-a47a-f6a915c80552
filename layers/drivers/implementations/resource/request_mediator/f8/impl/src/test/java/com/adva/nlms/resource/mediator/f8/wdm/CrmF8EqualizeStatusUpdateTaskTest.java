/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.mediation.ec.model.aos.croma.SlcEqualizeStatusType;
import com.adva.nlms.resource.mediator.f8.wdm.EqlzSlcNotificationHandler;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Set;


class CrmF8EqualizeStatusUpdateTaskTest {

  @Test
  @DisplayName("check CRM dependency on SlcEqualizeStatusType Enum of MO")
  void crmShouldContainAllSlcFailureStates() {
    Set<SlcEqualizeStatusType> equalizeStatusTypes = new java.util.HashSet<>(Set.of(SlcEqualizeStatusType.values()));
    equalizeStatusTypes.remove(SlcEqualizeStatusType.SUCCESS);
    equalizeStatusTypes.remove(SlcEqualizeStatusType.IN_SERVICE_IN_PROGRESS);
    equalizeStatusTypes.remove(SlcEqualizeStatusType.EQUALIZE_IN_PROGRESS);

    int enumsExpectedInCrm = EqlzSlcNotificationHandler.EQUALIZATION_FAILURE_STATES.size();
    int enumsInMO = equalizeStatusTypes.size();
    Assertions.assertEquals(enumsExpectedInCrm, enumsInMO, "The MO changes to SlcEqualizeStatusType enum appears to be not updated in CRM. Please inform SMB team");
  }
}