/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.eth;

import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;
import com.adva.nlms.resource.mediator.f8.ProtectionGroupFinder;
import com.adva.nlms.resource.mediator.f8.eth.api.in.EthSegmentRepository;
import com.adva.nlms.resource.mediator.f8.eth.api.in.SegmentID;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Optional;

class EthResourceRequestMediatorImpl implements EthResourceRequestMediator {
  private final Logger log = LogManager.getLogger(EthResourceRequestMediatorImpl.class);

  private final RrmEthDbResourcesFacade rrmEthDbResourcesFacade;
  private final EthCimProvisionRequestParametersCreatorImpl ethCimProvisionRequestParametersCreator;
  private final Provision provisionApi;
  private final RequestExecutor requestExecutor;
  private final EthSegmentRepository ethSegmentRepository;
  private final RrmEthCardCapabilities rrmEthCardCapabilities;
  private final ProtectionGroupFinder protectionGroupFinder;

  public EthResourceRequestMediatorImpl(RrmEthDbResourcesFacade rrmEthDbResourcesFacade,
                                        EthCimProvisionRequestParametersCreatorImpl ethCimProvisionRequestParametersCreator,
                                        Provision provisionApi, RequestExecutor requestExecutor, EthSegmentRepository ethSegmentRepository,
                                        RrmEthCardCapabilities rrmEthCardCapabilities, ProtectionGroupFinder protectionGroupFinder) {
    this.rrmEthDbResourcesFacade = rrmEthDbResourcesFacade;
    this.ethCimProvisionRequestParametersCreator = ethCimProvisionRequestParametersCreator;
    this.provisionApi = provisionApi;
    this.requestExecutor = requestExecutor;
    this.ethSegmentRepository = ethSegmentRepository;
    this.rrmEthCardCapabilities = rrmEthCardCapabilities;
    this.protectionGroupFinder = protectionGroupFinder;
  }

  @Override
  public void handleRequest(CrmSegmentRequestDto request) {
    int neId = request.neId;
    log.info("[ETH] Handling of {} segment request has begun: neId={}, segReqId={}",
      request.getRequestType(), neId, request.getId());
    switch (request.getRequestType()) {
      case CREATE -> {
        var requests = prepareProvisionRequests(neId, request);
        requestExecutor.createOrAdopt(requests, neId, request.getId());
      }
      case DELETE -> {
        var deprovisionEvents = new PrepareDeprovisionEvents(
          ethSegmentRepository, rrmEthDbResourcesFacade, rrmEthCardCapabilities)
          .prepareEvents(neId, request.getId());
        requestExecutor.delete(deprovisionEvents, neId, request.id);
      }
      case ADOPT -> {
        var requests = prepareProvisionRequests(neId, request);
        requestExecutor.adopt(requests, neId, request.getId());
      }
      case ABANDON -> requestExecutor.abandon(neId, request.getId());
      case SETADMINSTATE -> handleAdminStateRequest();
      default -> throw new EthProvisioningException(String.format("[ETH] Unsupported request type: %s",
        request.getRequestType().toString()));
    }
  }

  @Override
  public boolean isSegmentRequestSupported(CrmSegmentRequestDto request) {
    log.debug("[ETH] Checking if segment request is supported, NE: {}; request id : {}", request.neId, request.id);
    if(request.hasAssignRoles()){
      return false;
    }
    if (ethSegmentRepository.exists(new SegmentID(request.neId, request.id))) {
      return true;
    }
    if (request.srcTp == null || request.dstTp == null) { // filter out delete/abandon requests from other layers
      log.debug("[ETH] Source or destination TP not found for segment request, NE: {}; request id : {}", request.neId, request.id);
      return false;
    }
    Optional<CardRef> card = getCardInfoFromRequest(request);
    if (card.isEmpty()) {
      log.debug("[ETH] Card not found for segment request, NE: {}; request id : {}", request.neId, request.id);
      return false;
    }
    if (!isEthMode(card.get().mode())) {
      log.debug("[ETH] Card is not in ETH mode, NE: {}; request id : {}, mode: {}", request.neId, request.id, card.get().mode());
      return false;
    }
    log.debug("[ETH] Checking if card is supported card: {}, NE: {}; request id : {}", card.get().type(), request.neId, request.id);
    return rrmEthCardCapabilities.isEthCardSupported(card.get().type());
  }

  @Override
  public void clearSegmentCache(int neId) {
    ethSegmentRepository.deleteSegments(neId);
    log.info("Removed EthSegments on ne {}", neId);
  }

  private List<Request> prepareProvisionRequests(int neId, CrmSegmentRequestDto request) {
    var prepareRequest = new PrepareProvisionRequest(rrmEthDbResourcesFacade, ethCimProvisionRequestParametersCreator,
      provisionApi, rrmEthCardCapabilities, protectionGroupFinder);
    return prepareRequest.prepareRequest(neId, request);
  }

  private Optional<CardRef> getCardInfoFromRequest(CrmSegmentRequestDto request) {
    return rrmEthDbResourcesFacade.findCardFromCtp(request.neId, new Aid(request.srcTp))
      .or(() -> rrmEthDbResourcesFacade.findCardFromCtp(request.neId, new Aid(request.dstTp))); // LTP on zEnd
  }

  private boolean isEthMode(String mode) {
    if (mode == null) {
      return false;
    }
    return switch (mode) {
      case "ethxp", "tripletp", "quadtp", "cfp2xp" -> true;
      default -> false;
    };
  }

  private void handleAdminStateRequest() {
    // SetAdminState request is not supported in ETH layer
    // To conform to ENC-NI API, the request to handle the AdminState is a No-Operation
  }
}
