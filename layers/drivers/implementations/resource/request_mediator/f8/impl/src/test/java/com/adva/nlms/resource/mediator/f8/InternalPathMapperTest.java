/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8;

import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.resource.mediator.f8.internalpath.ClientProtection;
import com.adva.nlms.resource.mediator.f8.internalpath.Connection;
import com.adva.nlms.resource.mediator.f8.internalpath.CrossConnect;
import com.adva.nlms.resource.mediator.f8.internalpath.ForkPlacement;
import com.adva.nlms.resource.mediator.f8.internalpath.InterModuleConnection;
import com.adva.nlms.resource.mediator.f8.internalpath.LabelDTO;
import com.adva.nlms.resource.mediator.f8.internalpath.OduType;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.SignalType;
import com.adva.nlms.resource.mediator.f8.internalpath.TerminationPoint;
import com.adva.nlms.resource.mediator.f8.internalpath.Whole;
import ni.proto.external.common.signal_description.SignalOtn;
import ni.proto.inet.Label;
import ni.proto.inet.LabelType;
import ni.proto.inet.MultiLabel;
import ni.proto.inet.PortId;
import ni.proto.internal_path.InternalPathOuterClass;
import ni.proto.map.MapOuterClass;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

public class InternalPathMapperTest {

  private static final String SOURCE_AID = "Port-1/1/c6";
  private static final String DESTINATION_AID = "Port-1/1/n1";

  private static Label generateOtnLabel(LabelType.OtnLabel in) {
    return Label.newBuilder()
      .setMultiLabel(MultiLabel.newBuilder()
        .addLabel(LabelType.newBuilder()
          .setOtnLabel(in)
          .build())
        .build())
      .build();
  }

  private static InternalPathOuterClass.PortResourceIdentifier generatePortRsrc() {
    var whole = LabelType.OtnLabel.Whole.newBuilder().build();
    return InternalPathOuterClass.PortResourceIdentifier.newBuilder()
      .setLabel(generateOtnLabel(LabelType.OtnLabel.newBuilder().setWhole(whole).build()))
      .build();
  }

  private static InternalPathOuterClass.TerminationPoint generateProtoTerminationPoint(String name) {
    return InternalPathOuterClass.TerminationPoint.newBuilder()
      .setPortId(PortId.newBuilder().setId(name).build())
      .setTerminateSignal(true)
      .setResourceIdentifier(generatePortRsrc())
      .build();
  }

  private static InternalPathOuterClass.CrossConnect generateProtoCrossConnect() {
    return InternalPathOuterClass.CrossConnect.newBuilder()
      .setSource(generateProtoTerminationPoint(SOURCE_AID))
      .setDestination(generateProtoTerminationPoint(DESTINATION_AID))
      .setFork(InternalPathOuterClass.CrossConnect.ForkPlacement.SOURCE)
      .build();
  }

  private static InternalPathOuterClass.SignalType generateProtoSignalType() {
    var odu = InternalPathOuterClass.OduType.newBuilder()
      .setSignalType(SignalOtn.SignalType.OTN_SIGNAL_TYPE_ODU4)
      .build();
    return InternalPathOuterClass.SignalType.newBuilder().setOdu(odu).build();
  }

  private static MapOuterClass.Map generateProtoParams() {
    var kv = MapOuterClass.KeyValuePair.newBuilder().setK("FEC").setV("GFEC").build();
    return MapOuterClass.Map.newBuilder().addKvp(kv).build();
  }

  private static TerminationPoint generateTerminatePoint(String aid) {
    return new TerminationPoint(aid,
      new LabelDTO(new com.adva.nlms.resource.mediator.f8.internalpath.MultiLabel(List.of(new OtnLabel(new Whole())))),
      true);
  }

  private static SignalType generateSignalType() {
    return new SignalType(new OduType(LayerQualifier.ODU4, 0));
  }

  @Test
  void interModuleConnectionTest() {

    var conn = InternalPathOuterClass.InterModuleConnection.newBuilder()
      .setDestination(generateProtoTerminationPoint(DESTINATION_AID))
      .setSource(generateProtoTerminationPoint(SOURCE_AID))
      .setSignalType(generateProtoSignalType())
      .setCommonParams(generateProtoParams())
      .build();
    var in = InternalPathOuterClass.InternalPath.newBuilder().addInterModuleConnections(conn).build();

    var ref =
      List.of(new InterModuleConnection(
        generateSignalType(),
        generateTerminatePoint(SOURCE_AID),
        generateTerminatePoint(DESTINATION_AID),
        Map.of("FEC", "GFEC")));

    var result = InternalPathMapper.map(in);

    Assertions.assertEquals(0, result.connections().size());
    Assertions.assertEquals(ref, result.interModuleConnections());
  }

  @Test
  void connectionTest() {
    var conn = InternalPathOuterClass.Connection.newBuilder()
      .setSignalType(generateProtoSignalType())
      .addSubConnections(InternalPathOuterClass.SubConnection.newBuilder()
        .setCrossConnect(generateProtoCrossConnect())
        .build())
      .addSubConnections(InternalPathOuterClass.SubConnection.newBuilder()
        .setTerminationPoint(generateProtoTerminationPoint(SOURCE_AID))
        .build())
      .build();
    var in = InternalPathOuterClass.InternalPath.newBuilder().addConnections(conn).build();

    var ref =
      List.of(new Connection(
        generateSignalType(),
        List.of(
          new CrossConnect(generateTerminatePoint(SOURCE_AID), generateTerminatePoint(DESTINATION_AID), ForkPlacement.SOURCE),
          generateTerminatePoint(SOURCE_AID))
      ));

    var result = InternalPathMapper.map(in);

    Assertions.assertEquals(0, result.interModuleConnections().size());
    Assertions.assertEquals(ref, result.connections());
  }

  @Test
  void clientProtectionTest() {
    var clientProtection = InternalPathOuterClass.ClientProtection.newBuilder()
      .setPort(generateProtoTerminationPoint(SOURCE_AID))
      .build();
    var in = InternalPathOuterClass.InternalPath.newBuilder().setClientProtection(clientProtection).build();
    var ref = new ClientProtection(generateTerminatePoint(SOURCE_AID));

    var result = InternalPathMapper.map(in);

    Assertions.assertEquals(0, result.connections().size());
    Assertions.assertEquals(0, result.interModuleConnections().size());
    Assertions.assertEquals(ref, result.clientProtection());
  }

  static Stream<Arguments> inputConvertProtoSignalOtnSignalTypeToLayerQualifierTest() {
    return Stream.of(
      Arguments.of(SignalOtn.SignalType.OTN_SIGNAL_TYPE_ODU0, LayerQualifier.ODU0),
      Arguments.of(SignalOtn.SignalType.OTN_SIGNAL_TYPE_ODU1, LayerQualifier.ODU1),
      Arguments.of(SignalOtn.SignalType.OTN_SIGNAL_TYPE_ODU2, LayerQualifier.ODU2),
      Arguments.of(SignalOtn.SignalType.OTN_SIGNAL_TYPE_ODU3, LayerQualifier.ODU3),
      Arguments.of(SignalOtn.SignalType.OTN_SIGNAL_TYPE_ODU4, LayerQualifier.ODU4),
      Arguments.of(SignalOtn.SignalType.OTN_SIGNAL_TYPE_2E, LayerQualifier.ODU2E),
      Arguments.of(SignalOtn.SignalType.OTN_SIGNAL_TYPE_ODUFLEX, LayerQualifier.ODUFLEX),
      Arguments.of(SignalOtn.SignalType.OTN_SIGNAL_TYPE_3E1, LayerQualifier.OPTICAL),
      Arguments.of(SignalOtn.SignalType.OTN_SIGNAL_TYPE_3E2, LayerQualifier.OPTICAL),
      Arguments.of(SignalOtn.SignalType.OTN_SIGNAL_TYPE_1E, LayerQualifier.OPTICAL),
      Arguments.of(SignalOtn.SignalType.OTN_SIGNAL_TYPE_UNSPECIFIED, LayerQualifier.OPTICAL),
      Arguments.of(SignalOtn.SignalType.ODUC2PA, LayerQualifier.ODUC2PA),
      Arguments.of(SignalOtn.SignalType.ODUC3PA, LayerQualifier.ODUC3PA),
      Arguments.of(SignalOtn.SignalType.ODUC4PA, LayerQualifier.ODUC4PA),
      Arguments.of(SignalOtn.SignalType.ODUC5PA, LayerQualifier.ODUC5PA),
      Arguments.of(SignalOtn.SignalType.ODUC6PA, LayerQualifier.ODUC6PA),
      Arguments.of(SignalOtn.SignalType.ODUC7PA, LayerQualifier.ODUC7PA),
      Arguments.of(SignalOtn.SignalType.ODUC8PA, LayerQualifier.ODUC8PA),
      Arguments.of(SignalOtn.SignalType.ODUC9PA, LayerQualifier.ODUC9PA),
      Arguments.of(SignalOtn.SignalType.ODUC10PA, LayerQualifier.ODUC10PA),
      Arguments.of(SignalOtn.SignalType.ODUC11PA, LayerQualifier.ODUC11PA),
      Arguments.of(SignalOtn.SignalType.ODUC12PA, LayerQualifier.ODUC12PA),
      Arguments.of(SignalOtn.SignalType.ODUC2, LayerQualifier.ODUC2),
      Arguments.of(SignalOtn.SignalType.ODUC3, LayerQualifier.ODUC3),
      Arguments.of(SignalOtn.SignalType.ODUC4, LayerQualifier.ODUC4),
      Arguments.of(SignalOtn.SignalType.UNRECOGNIZED, LayerQualifier.OPTICAL)
    );
  }

  @ParameterizedTest
  @MethodSource("inputConvertProtoSignalOtnSignalTypeToLayerQualifierTest")
  void convertProtoSignalOtnSignalTypeToLayerQualifierTest(SignalOtn.SignalType in, LayerQualifier ref) {
    try {
      Assertions.assertEquals(ref, InternalPathMapper.convertProtoSignalOtnSignalTypeToLayerQualifier(in));
    } catch (Exception e) {
      Assertions.assertEquals(LayerQualifier.OPTICAL, ref);
    }
  }

  static Stream<Arguments> inputConvertForkPlacementTest() {
    return Stream.of(
      Arguments.of(InternalPathOuterClass.CrossConnect.ForkPlacement.NONE, ForkPlacement.NONE),
      Arguments.of(InternalPathOuterClass.CrossConnect.ForkPlacement.SOURCE, ForkPlacement.SOURCE),
      Arguments.of(InternalPathOuterClass.CrossConnect.ForkPlacement.DESTINATION, ForkPlacement.DESTINATION),
      Arguments.of(InternalPathOuterClass.CrossConnect.ForkPlacement.UNRECOGNIZED, ForkPlacement.NONE)
    );
  }

  @ParameterizedTest
  @MethodSource("inputConvertForkPlacementTest")
  void convertForkPlacementTest(InternalPathOuterClass.CrossConnect.ForkPlacement in, ForkPlacement ref) {
    try {
      Assertions.assertEquals(ref, InternalPathMapper.convertForkPlacement(in));
    } catch (Exception e) {
      Assertions.fail();
    }
  }
}
