/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8;

import com.adva.nlms.resource.mediator.f8.eth.Container;
import com.adva.nlms.resource.mediator.f8.eth.EthLabel;
import com.adva.nlms.resource.mediator.f8.eth.Whole;
import ni.proto.connection_segment.ConnectionSegmentOuterClass;
import ni.proto.external.common.signal_description.EqualizationParams;
import ni.proto.external.common.signal_description.FacilityType;
import ni.proto.external.common.signal_description.FlexgridParams;
import ni.proto.external.common.signal_description.OptionalDouble;
import ni.proto.external.common.signal_description.PvParams;
import ni.proto.external.common.signal_description.SignalDescription;
import ni.proto.external.common.signal_description.SignalWdm;
import ni.proto.inet.Label;
import ni.proto.inet.LabelType;
import ni.proto.inet.MultiLabel;
import ni.proto.map.MapOuterClass;
import ni.proto.port_params.PortParamsOuterClass;
import ni.proto.segment_request.SegmentRequestOuterClass;

import java.util.ArrayList;
import java.util.List;

class SegmentRequestBuilder {

  static SegmentRequestBuilder INSTANCE;
  private String srcTerminationPoint;
  private String dstTerminationPoint;
  private final List<String> mutableSegment = new ArrayList<>();

  private SegmentRequestBuilder() {
  }

  public static SegmentRequestBuilder newBuilder() {
     return new SegmentRequestBuilder();
  }

  public SegmentRequestBuilder withSrcTerminationPoint(String srcTerminationPoint) {
    this.srcTerminationPoint = srcTerminationPoint;
    return this;
  }

  public SegmentRequestBuilder withDstTerminationPoint(String dstTerminationPoint) {
    this.dstTerminationPoint = dstTerminationPoint;
    return this;
  }

  public SegmentRequestBuilder addMutableSegment(String SegmentId) {
    this.mutableSegment.add(SegmentId);
    return this;
  }

  SegmentRequestOuterClass.SegmentRequest build() {
    return SegmentRequestOuterClass.SegmentRequest.newBuilder()
      .setOperation(SegmentRequestOuterClass.Operation.newBuilder()
        .setId("692d6862-57be-4b4b-b9de-1e18fba1650b:0:1")
        .setCreate(SegmentRequestOuterClass.Create.newBuilder()
          .setConfig(ConnectionSegmentOuterClass.Config.newBuilder()
            .setLane(ConnectionSegmentOuterClass.Lane.newBuilder()
              .setSource(ConnectionSegmentOuterClass.Endpoint.newBuilder()
                .setTerminationPoint(srcTerminationPoint)
                .setLabel(Label.newBuilder()
                  .setMultiLabel(MultiLabel.newBuilder()
                    .addLabel(createWdmLabel(191243.75f, srcTerminationPoint, FlexgridParams.SlotWidth.SLOT_WIDTH_50G))
                  )
                )
                .setPortParams(createPortParams())
              )
              .setDestination(ConnectionSegmentOuterClass.Endpoint.newBuilder()
                .setTerminationPoint(dstTerminationPoint)
                .setLabel(Label.newBuilder()
                  .setMultiLabel(MultiLabel.newBuilder()
                    .addLabel(createWdmLabel(191243.75f, dstTerminationPoint, FlexgridParams.SlotWidth.SLOT_WIDTH_50G))
                  )
                )
                .setPortParams(createPortParams())
              )
            )
            .setAdminState(ConnectionSegmentOuterClass.Config.AdminState.DISABLED)
            .setSignal(createSignalDescription()
            )
            .setForwardSequenceNumber(1)
            .setReverseSequenceNumber(2)
            .setRole(ConnectionSegmentOuterClass.Config.Role.UNPROTECTED_WORKING)
          ).addAllMutableSegments(mutableSegment)
        )
      ).build();
  }

  private static SignalDescription createSignalDescription() {
    return SignalDescription.newBuilder()
      .setWdm(SignalWdm.newBuilder()
        .setPvParams(PvParams.newBuilder()
          .setForwardErrorCorrection(PvParams.FecType.FEC_TYPE_EFEC_13)
          .setModulation(PvParams.ModulationType.MODULATION_TYPE_16PQAM)
          .setFraction(OptionalDouble.newBuilder()
            .setOverrideDefault(true)
            .setValue(3.0f)
            .build()
          )
          // Add other parameters as needed
          .build()
        )
        .setFlexgridParams(FlexgridParams.newBuilder()
          .setSlotWidth(FlexgridParams.SlotWidth.SLOT_WIDTH_50G)
          .setFrequencySlotRetention(FlexgridParams.FrequencySlotRetention.RETENTION_CENTER_FREQ_AND_SLOT_WIDTH)
          .build()
        )
        .setEqualizationParams(EqualizationParams.newBuilder()
          .setSetPointDelta(OptionalDouble.newBuilder()
            .setOverrideDefault(true)
            .build()
          )
          .build()
        )
        .build()
      )
      .setFacilityType(FacilityType.FACILITY_TYPE_OTUC2PA)
      .build();
  }

  LabelType createWdmLabel(double channel, String port, FlexgridParams.SlotWidth slotwidth) {
    return LabelType.newBuilder()
      .setWdmLabel(LabelType.DwdmLabel.newBuilder()
        .setChannel(channel)
        .setPort(port)
        .setSlotWidth(slotwidth)
        .build()
      ).build();
  }

  LabelType createGrayLabel(double channel, String port) {
    return LabelType.newBuilder()
      .setGrayLabel(
        LabelType.GrayLabel.newBuilder()
          .setChannel(channel)
          .setPort(port)
          .build()
      ).build();
  }

  LabelType createEthLabel(EthLabel ethLabel) {
    if (ethLabel instanceof Container container) {
      return LabelType.newBuilder()
        .setEthLabel(LabelType.EthLabel.newBuilder()
          .setContainer(LabelType.EthLabel.Container.newBuilder()
            .addAllId(container.id())
            .addAllSlots(container.slots())
            .build()
          ).build()
        ).build();
    } else if (ethLabel instanceof Whole) {
      return LabelType.newBuilder()
        .setEthLabel(LabelType.EthLabel.newBuilder()
          .setWhole(LabelType.EthLabel.Whole.getDefaultInstance())
          .build()
        ).build();
    }
    throw new IllegalArgumentException("Unknown EthLabel type in this test");
  }
  private PortParamsOuterClass.PortParams createPortParams() {
    return PortParamsOuterClass.PortParams.newBuilder()
      .setParams(MapOuterClass.Map.newBuilder()
        .addKvp(MapOuterClass.KeyValuePair.newBuilder()
          .setK("Setpoint")
          .setV("3 dBm")
          .build()
        )
        .addKvp(MapOuterClass.KeyValuePair.newBuilder()
          .setK("GLQ")
          .setV("OTUC2PA")
          .build()
        )
        // Add other kvp entries as needed
        .build()
      )
      .build();
  }
}
