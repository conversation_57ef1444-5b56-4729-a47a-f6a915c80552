/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm.adapters.messaging;

import com.adva.nlms.common.config.EntityIndex;
import com.adva.nlms.mediation.mo.inventory.f8.events.AGATESDoneResultType;
import com.adva.nlms.mediation.mo.inventory.f8.events.AGATESNotification;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.AgatesDone;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.AgatesParameters;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.AgatesPending;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.AgatesTrigger;

import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

class AgatesParametersMapper {

  private static final String EQUALIZATION_PREFIX = "[POWEREQUALIZE]:";

  private AgatesParametersMapper(){}
  static AgatesParameters verifyAndCollectParameters(AGATESNotification agatesNotification) {
    if (agatesNotification.isPending()) {
      return verifyAndCollectPendingParameters(agatesNotification);
    } else {
      return verifyAndCollectDoneParameters(agatesNotification);
    }
  }

  private static AgatesParameters verifyAndCollectDoneParameters(AGATESNotification agatesNotification) {
    AGATESDoneResultType resultType = agatesNotification.getResultType();

    String sourceInfo = agatesNotification.getEntityName();
    Objects.requireNonNull(sourceInfo, "%s Failed to handle SpanEqualization AGATES-Done event. The value 'source' is null".formatted(EQUALIZATION_PREFIX));

    String omsInterface = mapOmsInterface(sourceInfo);
    AgatesDone agatesDone = new AgatesDone(agatesNotification.getNeID(), omsInterface, resultType);
    return new AgatesParameters(AgatesTrigger.DONE, null, agatesDone);
  }

  private static String mapOmsInterface(String sourceInfo) {
    String[] toStringParts = sourceInfo.split(" ");
    if (toStringParts.length != 6) {
      throw new IllegalArgumentException("%s Failed to handle SpanEqualization AGATES-Done event. The value of 'source' is in incorrect format".formatted(EQUALIZATION_PREFIX));
    }
    String omsInterface = toStringParts[3];
    if (omsInterface == null || !omsInterface.contains("oms")) {
      throw new IllegalArgumentException("%s Failed to handle SpanEqualization AGATES-Done event. The value of 'source' does not contain 'oms'".formatted(EQUALIZATION_PREFIX));
    }
    return omsInterface;
  }

  private static AgatesParameters verifyAndCollectPendingParameters(AGATESNotification agatesNotification) {
    String entityName = agatesNotification.getEntityName();
    Objects.requireNonNull(entityName, "%s Failed to handle SpanEqualization PendingAGATES event. The value 'to' is null".formatted(EQUALIZATION_PREFIX));
    String[] otsiaParts = mapOtsiaParts(entityName);
    String networkPortOmsCtp = IntStream.of(0, 1, 2, 3).mapToObj(i -> otsiaParts[i]).collect(Collectors.joining("/"));
    int slcId = getSlcIdFromEntityIndex(agatesNotification.getSlcIndex());
    if (slcId == -1) {
      throw new IllegalArgumentException("%s Failed handle SpanEqualization PendingAGATES event. Cannot retrieve slcId for neId=%d, slcIndex=%s".formatted(EQUALIZATION_PREFIX, agatesNotification.getNeID(), agatesNotification.getSlcIndex()));
    }
    AgatesPending agatesPending = new AgatesPending(agatesNotification.getNeID(), slcId, networkPortOmsCtp, agatesNotification.isAEnd());
    return new AgatesParameters(AgatesTrigger.PENDING, agatesPending, null);
  }

  private static String[] mapOtsiaParts(String entityName) {
    String[] toStringParts = entityName.split(" ");
    if (toStringParts.length != 4) {
      throw new IllegalArgumentException("%s Failed to handle SpanEqualization PendingAGATES event. The value of 'to' is in incorrect format".formatted(EQUALIZATION_PREFIX));
    }
    String otsiaInterface = toStringParts[3];
    if (!otsiaInterface.contains("oms")) {
      throw new IllegalArgumentException("%s Failed to handle SpanEqualization PendingAGATES event. The value of 'to' does not contain 'oms'".formatted(EQUALIZATION_PREFIX));
    }
    return otsiaInterface.split("/");
  }

  static int getSlcIdFromEntityIndex(EntityIndex entityIndex) {
    String[] entityIndexArr = entityIndex
      .toString()
      .split("/");

    if (!isValidSlcEntityIndex(entityIndexArr)) {
      // unexpected format of the EntityIndexString
      return -1;
    }
    try {
      return Integer.parseInt(entityIndexArr[6]);
    } catch (NumberFormatException e) {
      return -1;
    }
  }

  static boolean isValidSlcEntityIndex(String[] entityIndexArr) {
    return entityIndexArr.length == 7 && "slc".equals(entityIndexArr[5]);
  }
}
