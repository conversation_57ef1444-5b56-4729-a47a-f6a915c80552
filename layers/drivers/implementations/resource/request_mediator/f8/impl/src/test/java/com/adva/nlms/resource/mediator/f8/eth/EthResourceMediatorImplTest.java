/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.eth;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.provider.api.CapabilityProvider;
import com.adva.infrastructure.capabilityprovider.capabilities.CapabilityConfiguration;
import com.adva.infrastructure.capabilityprovider.core.CapabilityProviderConfiguration;
import com.adva.infrastructure.capabilityprovider.mo.MoCapabilityProviderConfiguration;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;
import com.adva.nlms.resource.mediator.f8.ProtectionGroupFinder;
import com.adva.nlms.resource.mediator.f8.eth.EthCimProvisionRequestParametersCreatorImpl;
import com.adva.nlms.resource.mediator.f8.eth.EthResourceRequestMediatorImpl;
import com.adva.nlms.resource.mediator.f8.eth.RequestExecutor;
import com.adva.nlms.resource.mediator.f8.eth.RrmEthCardCapabilities;
import com.adva.nlms.resource.mediator.f8.eth.RrmEthCardCapabilitiesImpl;
import com.adva.nlms.resource.mediator.f8.eth.RrmEthDbResourcesFacade;
import com.adva.nlms.resource.mediator.f8.eth.api.in.EthSegmentRepository;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

@ExtendWith(MockitoExtension.class)
class EthResourceMediatorImplTest {
  private static final int NE_ID = 1;
  private static final String CARD_AID = "Module-1/1";

  private EthResourceRequestMediatorImpl ethResourceRequestMediator;

  @Mock
  private RrmEthDbResourcesFacade rrmEthDbResourcesFacade;

  @Mock
  private EthCimProvisionRequestParametersCreatorImpl ethCimProvisionRequestParametersCreator;

  @Mock
  private Provision provisionApi;

  @Mock
  private RequestExecutor requestExecutor;

  @Mock
  private EthSegmentRepository ethSegmentRepository;

  private static final CapabilityProvider CAPABILITY_PROVIDER = new CapabilityProviderConfiguration()
      .capabilityProvider(new CapabilityConfiguration().capabilityRepository(), null, null);

  private static final MoCapabilityProvider MO_CAPABILITY_PROVIDER = new MoCapabilityProviderConfiguration().moCapabilityProvider();

  @Mock
  private NEDataProvider neDataProvider;

  @Mock
  private ProtectionGroupFinder protectionGroupFinder;

  @BeforeEach
  void setUp() {
    RrmEthCardCapabilities rrmEthCardCapabilities = new RrmEthCardCapabilitiesImpl(CAPABILITY_PROVIDER, MO_CAPABILITY_PROVIDER, neDataProvider);
    ethResourceRequestMediator = new EthResourceRequestMediatorImpl(rrmEthDbResourcesFacade,
      ethCimProvisionRequestParametersCreator, provisionApi, requestExecutor, ethSegmentRepository, rrmEthCardCapabilities, protectionGroupFinder);
  }

  @Test
  void testIsSegmentRequestSupportedWhenEthCard() {
    // Given
    CrmSegmentRequestDto crmSegmentRequestDto = new CrmSegmentRequestDto();
    crmSegmentRequestDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    crmSegmentRequestDto.srcTp = "Port-1/1/p4";
    crmSegmentRequestDto.dstTp = "ET400zr-1/1/p2/et400zr";
    crmSegmentRequestDto.neId = NE_ID;
    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Aid(crmSegmentRequestDto.srcTp)))
        .thenReturn(Optional.empty());
    CardRef cardRef = new CardRef(NE_ID, new Aid(CARD_AID), null, "MF-M6MDT", 2, "ethxp");
    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Aid(crmSegmentRequestDto.dstTp)))
        .thenReturn(Optional.of(cardRef));
    // When
    boolean result = ethResourceRequestMediator.isSegmentRequestSupported(crmSegmentRequestDto);
    // Then
    Assertions.assertTrue(result);
  }

  @Test
  void testIsSegmentRequestSupportedWhenNotEthCard() {
    // Given
    CrmSegmentRequestDto crmSegmentRequestDto = new CrmSegmentRequestDto();
    crmSegmentRequestDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    crmSegmentRequestDto.srcTp = "ODU4-1/1/n1/otu4/odu4";
    crmSegmentRequestDto.dstTp = "Port-1/1/p3";
    crmSegmentRequestDto.neId = NE_ID;
    CardRef cardRef = new CardRef(NE_ID, new Aid(CARD_AID), null, "OF-2D16DCT", 2, "singlecc");
    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Aid(crmSegmentRequestDto.srcTp)))
        .thenReturn(Optional.of(cardRef));
    // When
    boolean result = ethResourceRequestMediator.isSegmentRequestSupported(crmSegmentRequestDto);
    // Then
    Assertions.assertFalse(result);
  }

  @Test
  void testIsSegmentRequestSupportedWhenUnsupportedCard() {
    // Given
    CrmSegmentRequestDto crmSegmentRequestDto = new CrmSegmentRequestDto();
    crmSegmentRequestDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    crmSegmentRequestDto.srcTp = "ETC400g-1/1/p2/et400zr/etc400g";
    crmSegmentRequestDto.dstTp = "Port-1/1/p3";
    crmSegmentRequestDto.neId = NE_ID;
    CardRef cardRef = new CardRef(NE_ID, new Aid(CARD_AID), null, "some_unsupported_card", 2, "quadtp");
    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Aid(crmSegmentRequestDto.srcTp)))
      .thenReturn(Optional.of(cardRef));
    // When
    boolean result = ethResourceRequestMediator.isSegmentRequestSupported(crmSegmentRequestDto);
    // Then
    Assertions.assertFalse(result);
  }

  @Test
  void testIsSegmentRequestSupportedWhenNoCardFound() {
    // Given
    CrmSegmentRequestDto crmSegmentRequestDto = new CrmSegmentRequestDto();
    crmSegmentRequestDto.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    crmSegmentRequestDto.srcTp = "ETC400g-1/1/p2/et400zr/etc400g";
    crmSegmentRequestDto.dstTp = "Port-1/1/p3";
    crmSegmentRequestDto.neId = NE_ID;
    Mockito.when(rrmEthDbResourcesFacade.findCardFromCtp(NE_ID, new Aid(crmSegmentRequestDto.srcTp)))
        .thenReturn(Optional.empty());
    // When
    boolean result = ethResourceRequestMediator.isSegmentRequestSupported(crmSegmentRequestDto);
    // Then
    Assertions.assertFalse(result);
  }

  @Test
  void testIsSegmentRequestSupportedWhenDeleteRequestFromDifferentLayer() {
    // Given
    CrmSegmentRequestDto crmSegmentRequestDto = new CrmSegmentRequestDto();
    crmSegmentRequestDto.requestType = CrmSegmentRequestDto.RequestType.DELETE;
    crmSegmentRequestDto.neId = NE_ID;
    // When
    boolean result = ethResourceRequestMediator.isSegmentRequestSupported(crmSegmentRequestDto);
    // Then
    Assertions.assertFalse(result);
  }
}
