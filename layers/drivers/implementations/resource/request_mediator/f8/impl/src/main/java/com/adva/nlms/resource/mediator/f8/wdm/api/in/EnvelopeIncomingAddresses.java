/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.mediator.f8.wdm.api.in;

import ni.msg.EnvelopeOuterClass;

public record EnvelopeIncomingAddresses(EnvelopeOuterClass.Address incomingSrcAddress, EnvelopeOuterClass.Address incomingDstAddress) {
  public static EnvelopeIncomingAddresses of(EnvelopeOuterClass.Address incomingSrcAddress, EnvelopeOuterClass.Address incomingDstAddress) {
    return new EnvelopeIncomingAddresses(incomingSrcAddress, incomingDstAddress);
  }
}
