/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */
package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.provision.f8.api.in.ObjectDoesNotExistException;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;
import java.util.Set;

import static com.adva.nlms.resource.mediator.f8.events.ProvisionEventsCreator.sncAdopted;
import static com.adva.nlms.resource.mediator.f8.events.ProvisionEventsCreator.sncAutocreated;
import static com.adva.nlms.resource.mediator.f8.events.ProvisionEventsCreator.sncCreated;

class SncRequest implements Request {

  private static final Logger log = LogManager.getLogger(SncRequest.class);

  private final int neId;

  private final String uri;

  private final boolean exists; // for typical adopt cases - also in createOrAdopt scenario
  private final boolean entityAutocreated; // for entities that are autocreated and can't be deleted

  private final Set<String> aEndpointURIs;

  private final Set<String> zEndpointURIs;

  private final Provision provisionApi;

  SncRequest(int neId, String uri, boolean exists, boolean entityAutocreated, Set<String> aEndpointURIs,
             Set<String> zEndpointURIs, Provision provisionApi) {
    this.neId = neId;
    this.uri = uri;
    this.exists = exists;
    this.entityAutocreated = entityAutocreated;
    this.aEndpointURIs = aEndpointURIs;
    this.zEndpointURIs = zEndpointURIs;
    this.provisionApi = provisionApi;
  }

  SncRequest(int neId, String uri, boolean exists, Set<String> aEndpointURIs,
             Set<String> zEndpointURIs, Provision provisionApi) {
    this(neId, uri, exists, false, aEndpointURIs, zEndpointURIs, provisionApi);
  }

  @Override
  public ProvisionLocalEvent provision() {
    if (entityAutocreated) {
      return sncAutocreated(uri, aEndpointURIs, zEndpointURIs);
    }
    if (exists) {
      return sncAdopted(uri, aEndpointURIs, zEndpointURIs);
    } else {
      int sncNumber = provisionApi.provisionSnc(NetworkElementID.create(neId), uri, aEndpointURIs, zEndpointURIs, null);
      return sncCreated(uri + "/" + sncNumber, aEndpointURIs, zEndpointURIs);
    }
  }

  @Override
  public void delete() {
    log.info("Deleting SNC {}", uri);
    try {
      provisionApi.deleteSnc(NetworkElementID.create(neId), uri);
    } catch (ObjectDoesNotExistException e) {
      log.warn("Could not delete SNC on NE: {} with uri {} - entity does not exist", neId, uri);
    }
  }

  @Override
  public ProvisionLocalEvent adopt() {
    if (entityAutocreated) {
      return sncAutocreated(uri, aEndpointURIs, zEndpointURIs);
    }
    if (exists) {
      return sncAdopted(uri, aEndpointURIs, zEndpointURIs);
    } else {
      throw new NoAdoptResourceException("Missing SNC %s".formatted(uri));
    }
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    SncRequest that = (SncRequest) o;
    return neId == that.neId && Objects.equals(uri, that.uri) && Objects.equals(exists, that.exists)
      && Objects.equals(entityAutocreated, that.entityAutocreated) && Objects.equals(aEndpointURIs, that.aEndpointURIs)
      && Objects.equals(zEndpointURIs, that.zEndpointURIs);
  }

  @Override
  public int hashCode() {
    return Objects.hash(neId, uri, exists, entityAutocreated, aEndpointURIs, zEndpointURIs);
  }

  @Override
  public String toString() {
    return "SncRequest{" +
            "neId=" + neId +
            ", uri='" + uri + '\'' +
            ", exists=" + exists +
            ", entityAutocreated=" + entityAutocreated +
            ", aEndpointURIs=" + aEndpointURIs +
            ", zEndpointURIs=" + zEndpointURIs +
            ", provisionApi=" + provisionApi +
            '}';
  }
}
