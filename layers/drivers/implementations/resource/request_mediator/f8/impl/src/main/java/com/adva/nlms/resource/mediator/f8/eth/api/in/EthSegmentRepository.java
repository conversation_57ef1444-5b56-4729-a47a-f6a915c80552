/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: Leszek E<PERSON>
 */

package com.adva.nlms.resource.mediator.f8.eth.api.in;

import java.util.Optional;

public interface EthSegmentRepository {
  void save(EthSegment segment);

  Optional<EthSegment> findOne(SegmentID segmentID);

  void delete(SegmentID segmentID);

  boolean exists(SegmentID segmentID);

  void deleteSegments(int neId);
}
