/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.common.config.EquipmentState;
import com.adva.nlms.mediation.ec.model.F8OperState;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.CtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.CtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.OperationalState;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.State;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.resource.crm.model.CrmCtrlTaskScheduler;
import com.adva.nlms.resource.mediator.f8.wdm.MonitoredEntity;
import com.adva.nlms.resource.mediator.f8.wdm.MonitoredEntityKey;
import com.adva.nlms.resource.mediator.f8.wdm.MonitoredEntityManager;
import com.adva.nlms.resource.mediator.f8.wdm.TpOutageCpcNotifier;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

class MonitoredEntityManagerTest {

  private static final int NE_ID = 76437;
  private static final String OTSIA_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,3/eq/card/ptp/nw,1/ctp/oms/ctp/spslg-17/ctp/otsia";
  public static final MonitoredEntityKey KEY = new MonitoredEntityKey(NE_ID, OTSIA_URI);
  private static final String SEGMENT_REQUEST_ID = UUID.randomUUID().toString();
  private static final MonitoredEntity.TerminationPointPair TERMINATION_POINT_PAIR = new MonitoredEntity.TerminationPointPair("TX_TP", "RX_TP");
  public static final MonitoredEntity MONITORED_ENTITY = new MonitoredEntity(SEGMENT_REQUEST_ID, OTSIA_URI, TERMINATION_POINT_PAIR, MonitoredEntity.EquipmentType.ROADM);

  private final CtpResources ctpResources;
  private final TpOutageCpcNotifier tpOutageCpcNotifier;
  private final CrmCtrlTaskScheduler crmCtrlTaskScheduler;
  private final MonitoredEntityManager sut;

  MonitoredEntityManagerTest() {
    ctpResources = mock(CtpResources.class);
    PtpResources ptpResources = mock(PtpResources.class);
    tpOutageCpcNotifier = mock(TpOutageCpcNotifier.class);
    crmCtrlTaskScheduler = mock(CrmCtrlTaskScheduler.class);
    sut = new MonitoredEntityManager(ctpResources, ptpResources, tpOutageCpcNotifier, crmCtrlTaskScheduler);
  }

  @Test
  void test_initialProcessMonitoredEntity() {
    sut.addEntity(KEY, MONITORED_ENTITY);
    when(ctpResources.findCard(NE_ID, new Uri(OTSIA_URI))).thenReturn(Optional.of(equippedCard()));
    when(ctpResources.findCtp(NE_ID, new Uri(OTSIA_URI))).thenReturn(Optional.of(otsiaCtp()));
    when(tpOutageCpcNotifier.notifyOutage(KEY, MONITORED_ENTITY, otsiaCtp().state(), false, false)).thenReturn(TpOutageCpcNotifier.AlarmResult.DEFERRED);
    sut.initialProcessMonitoredEntity(KEY);
    verify(tpOutageCpcNotifier, times(1)).notifyOutage(KEY, MONITORED_ENTITY, otsiaCtp().state(), false, false);
    verify(crmCtrlTaskScheduler, times(1)).scheduleTask(any(), anyLong(), any());
  }

  @Test
  void test_processMonitoredEntity() {
    CtpRef ctp = otsiaCtp();
    sut.addEntity(KEY, MONITORED_ENTITY);
    when(ctpResources.findCard(NE_ID, new Uri(OTSIA_URI))).thenReturn(Optional.of(equippedCard()));
    when(ctpResources.findCtp(NE_ID, new Uri(OTSIA_URI))).thenReturn(Optional.of(ctp));
    State state = ctp.state();
    when(tpOutageCpcNotifier.notifyOutage(KEY, MONITORED_ENTITY, state, true, false)).thenReturn(TpOutageCpcNotifier.AlarmResult.DEFERRED);
    sut.processMonitoredEntity(KEY);
    verify(tpOutageCpcNotifier, times(1)).notifyOutage(KEY, MONITORED_ENTITY, state, true, false);
    verify(crmCtrlTaskScheduler, times(1)).scheduleTask(any(), anyLong(), any());
  }

  @Test
  void test_processMonitoredEntityDeferred(){
    sut.addEntity(KEY, MONITORED_ENTITY);
    when(ctpResources.findCard(NE_ID, new Uri(OTSIA_URI))).thenReturn(Optional.of(equippedCard()));
    sut.processMonitoredEntityDeferred(KEY);
    verify(crmCtrlTaskScheduler, times(1)).scheduleTask(any(), anyLong(), any());
  }

  @Test
  void test_removeEntities() {
    sut.addEntity(KEY, MONITORED_ENTITY);
    sut.removeEntities(NE_ID, SEGMENT_REQUEST_ID);
    sut.processMonitoredEntity(KEY);
    verifyNoInteractions(ctpResources);
    verify(tpOutageCpcNotifier).removeOutagesForSegment(NE_ID, SEGMENT_REQUEST_ID);
    verifyNoInteractions(crmCtrlTaskScheduler);
  }


  private CardRef equippedCard() {
    return new CardRef(NE_ID, new Aid("cardAid"), new Uri("cardUri"), "cardType", F8OperState.NR.getValue());
  }

  private CtpRef otsiaCtp() {
    State state = new State(EquipmentState.UNKNOWN, OperationalState.OUTAGE, List.of());
    return new CtpRef(NE_ID, new Aid("OTSIA_AID"), new Uri(OTSIA_URI), state);
  }
}
