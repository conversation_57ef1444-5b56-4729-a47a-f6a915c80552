/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */
package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.provision.f8.api.in.MoCimParameter;
import com.adva.nlms.resource.provision.f8.api.in.ObjectDoesNotExistException;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionObjectExistsException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.Objects;
import java.util.Set;

import static com.adva.nlms.resource.mediator.f8.events.ProvisionEventsCreator.autoCreatedCtpAdopted;
import static com.adva.nlms.resource.mediator.f8.events.ProvisionEventsCreator.ctpAdopted;
import static com.adva.nlms.resource.mediator.f8.events.ProvisionEventsCreator.ctpAutocreated;
import static com.adva.nlms.resource.mediator.f8.events.ProvisionEventsCreator.ctpCreated;

class CtpRequest implements Request {

  private static final Logger log = LogManager.getLogger(CtpRequest.class);

  private final int neId;
  private final String uri;
  private final Set<MoCimParameter> params;
  private final boolean exists; // for typical adopt cases - also in createOrAdopt scenario
  private final boolean entityAutocreated; // for entities that are autocreated and can't be deleted
  private final Provision provisionApi;

  CtpRequest(int neId, String uri, boolean exists, boolean entityAutocreated, Set<MoCimParameter> params, Provision provisionApi) {
    this.neId = neId;
    this.uri = uri;
    this.exists = exists;
    this.entityAutocreated = entityAutocreated;
    this.params = params;
    this.provisionApi = provisionApi;
  }

  CtpRequest(int neId, String uri, boolean exists, boolean entityAutocreated, Provision provisionApi) {
    this(neId, uri, exists, entityAutocreated, Collections.emptySet(), provisionApi);
  }

  @Override
  public ProvisionLocalEvent provision() {
    if (entityAutocreated) {
      updateCtpParams();
      return ctpAutocreated(uri);
    }
    if (exists) {
      updateCtpParams();
      return ctpAdopted(uri);
    }
    try {
      provisionApi.provisionCtp(NetworkElementID.create(neId), uri, params);
      return ctpCreated(uri);
    } catch (ProvisionObjectExistsException ex) {
      // some CTPs are created automatically when parent is created, eg. et100 -> et100/odu4
      return autoCreatedCtpAdopted(uri);
    }
  }

  @Override
  public void delete() {
    log.info("Deleting CTP {}", uri);
    try {
      provisionApi.deleteCtp(NetworkElementID.create(neId), uri);
    } catch (ObjectDoesNotExistException e) {
      log.warn("Could not delete CTP on NE: {} with uri {} - entity does not exist", neId, uri);
    }
  }

  @Override
  public ProvisionLocalEvent adopt() {
    if (entityAutocreated) {
      return ctpAutocreated(uri);
    }
    if (exists) {
      return ctpAdopted(uri);
    }
    throw new NoAdoptResourceException("Missing CTP %s".formatted(uri));
  }

  String getUri() {
    return uri;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    CtpRequest that = (CtpRequest) o;
    return neId == that.neId && Objects.equals(uri, that.uri) && Objects.equals(exists, that.exists)
           && Objects.equals(entityAutocreated, that.entityAutocreated)
           && Objects.equals(params, that.params);
  }

  @Override
  public int hashCode() {
    return Objects.hash(neId, uri, exists, entityAutocreated, params);
  }

  @Override
  public String toString() {
    return "CtpRequest{" +
      "neId=" + neId +
      ", uri='" + uri + '\'' +
      ", params=" + params +
      ", exists=" + exists +
      ", entityAutocreated=" + entityAutocreated +
      '}';
  }

  private void updateCtpParams() {
    if (params.isEmpty()) {
      return;
    }
    var provNeId = NetworkElementID.create(neId);
    provisionApi.updateAdminState(provNeId, uri, AdminState.DOWN);
    provisionApi.modifyCtp(provNeId, uri, params);
    provisionApi.updateAdminState(provNeId, uri, AdminState.UP);
  }
}
