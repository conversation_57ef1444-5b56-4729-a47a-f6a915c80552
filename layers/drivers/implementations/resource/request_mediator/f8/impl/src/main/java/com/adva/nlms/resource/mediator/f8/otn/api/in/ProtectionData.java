/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn.api.in;


import com.adva.nlms.resource.mediator.f8.internalpath.ForkPlacement;
import com.adva.nlms.resource.mediator.f8.internalpath.TerminationPoint;

// For CCCP case we're storing termination point from clientProtection field in segment request
// Since Line Protection segment request doesn't contain clientProtection field, we're storing forkPlacement instead
public record ProtectionData(TerminationPoint clientProtectionPort, ForkPlacement forkPlacement) {
  public ProtectionData(TerminationPoint port) {
    this(port, null);
  }

  public ProtectionData(ForkPlacement forkPlacement) {
    this(null, forkPlacement);
  }

  public boolean isCccProtection() {
    return clientProtectionPort != null;
  }

  public boolean isLineProtection() {
    return forkPlacement != null;
  }
}
