/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.mediation.mo.inventory.resources.Ctp;
import com.adva.nlms.mediation.mo.inventory.resources.CtpBuilder;
import com.adva.nlms.mediation.mo.inventory.resources.Ptp;
import com.adva.nlms.mediation.mo.inventory.resources.PtpBuilder;
import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.cim.api.CimValue;
import com.adva.nlms.resource.mediator.f8.wdm.ParametersChecker;
import com.adva.nlms.resource.provision.f8.api.in.MoCimParameter;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionRequestParameters;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class ParametersCheckerTest {

  private static ProvisionRequestParameters requestParameters;
  private final Ctp ctpWithMatch = CtpBuilder.newBuilder().withFrequency(196000000L).withModulation("p16qam").withTerminationMode("tss").build();
  private final Ctp ctpModulationNotMatch = CtpBuilder.newBuilder().withFrequency(196000000L).withModulation("16qam").withTerminationMode("tss").build();
  private final Ctp ctpFrequencyNotMatch = CtpBuilder.newBuilder().withFrequency(200000000L).withModulation("16qam").withTerminationMode("tss").build();

  @BeforeAll
  static void init() {
    NetworkElementID neId = new NetworkElementID(123);
    MoCimParameter frequencyPar = new MoCimParameter("motrait", "tuned-frequency", new CimValue.Int(196000000L));
    MoCimParameter bandwidthPar = new MoCimParameter("motrait", "optical-bandwidth", new CimValue.Int(100000L));
    MoCimParameter modulationPar = new MoCimParameter("motrait", "modulation", new CimValue.Enum("p16qam"));
    MoCimParameter fecPar = new MoCimParameter("motrait", "fec-type", new CimValue.Enum("feca15i1"));
    List<MoCimParameter> cimParameters = List.of(frequencyPar, bandwidthPar, modulationPar, fecPar);
    requestParameters = new ProvisionRequestParameters(neId, "ctp", "/mit/me/1/eqh/shelf,12/eqh/slot,1/eq/card/ptp/nw,1/ctp/ot200", cimParameters);
  }

  @Test
  void ctpParametersMatch() {
    assertFalse(ParametersChecker.ctpParametersMismatch(ctpWithMatch, requestParameters).mismatch());
    assertTrue(ParametersChecker.ctpParametersMismatch(ctpFrequencyNotMatch, requestParameters).mismatch());
    assertTrue(ParametersChecker.ctpParametersMismatch(ctpModulationNotMatch, requestParameters).mismatch());
  }

  @Test
  void ptpParametersMatch() {

    Ptp ptpMatch = PtpBuilder.newBuilder()
      .withTunedFrequency(196000000)
      .withOpticalSetpoint(1000)
      .build();

    Ptp ptpFrequencyNotMatch = PtpBuilder.newBuilder()
      .withTunedFrequency(200000000)
      .withOpticalSetpoint(1000)
      .withOpticalBandwidth(100000)
      .build();

    assertFalse(ParametersChecker.ptpParametersMismatch(ptpMatch, requestParameters));
    assertTrue(ParametersChecker.ptpParametersMismatch(ptpFrequencyNotMatch, requestParameters));
  }
}
