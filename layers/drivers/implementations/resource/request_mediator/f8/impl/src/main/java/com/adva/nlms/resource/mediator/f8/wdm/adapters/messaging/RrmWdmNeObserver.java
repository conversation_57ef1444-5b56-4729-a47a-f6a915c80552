/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.resource.mediator.f8.wdm.adapters.messaging;

import com.adva.nlms.resource.crm.model.NeObserver;
import com.adva.nlms.resource.mediator.f8.wdm.WdmResourceRequestMediator;

class RrmWdmNeObserver extends NeObserver {
  private final WdmResourceRequestMediator wdmRRM;

  RrmWdmNeObserver(WdmResourceRequestMediator wdmRRM) {
    this.wdmRRM = wdmRRM;
  }

  @Override
  protected void handleNeRemoval(int neId) {
    wdmRRM.clearSegmentCache(neId);
  }
}
