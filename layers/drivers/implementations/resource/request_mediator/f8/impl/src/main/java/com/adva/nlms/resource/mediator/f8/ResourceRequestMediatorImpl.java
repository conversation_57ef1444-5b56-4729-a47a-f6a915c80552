/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8;

import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.resource.crm.model.CrmCtrlTaskScheduler;
import com.adva.nlms.resource.mediator.api.in.OperationFailedException;
import com.adva.nlms.resource.mediator.api.in.ResourceRequestMediator;
import com.adva.nlms.resource.mediator.f8.eth.EthResourceRequestMediator;
import com.adva.nlms.resource.mediator.f8.otn.OtnResourceRequestMediator;
import com.adva.nlms.resource.mediator.f8.wdm.WdmResourceRequestMediator;
import ni.msg.EnvelopeOuterClass;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

class ResourceRequestMediatorImpl implements ResourceRequestMediator {
  private final Logger log = LogManager.getLogger(ResourceRequestMediatorImpl.class);
  private final WdmResourceRequestMediator wdmRRM;
  private final OtnResourceRequestMediator otnRRM;
  private final EthResourceRequestMediator ethRRM;
  private final NiCallbackSender niCallbackSender;
  private final CrmCtrlTaskScheduler crmCtrlTaskScheduler;
  private final ProvisionAdapter provisionAdapter;

  ResourceRequestMediatorImpl(WdmResourceRequestMediator wdmRRM,
                              OtnResourceRequestMediator otnRRM,
                              EthResourceRequestMediator ethRRM,
                              NiCallbackSender niCallbackSender,
                              CrmCtrlTaskScheduler crmCtrlTaskScheduler,
                              ProvisionAdapter provisionAdapter
  ) {
    this.wdmRRM = wdmRRM;
    this.otnRRM = otnRRM;
    this.ethRRM = ethRRM;
    this.niCallbackSender = niCallbackSender;
    this.crmCtrlTaskScheduler = crmCtrlTaskScheduler;
    this.provisionAdapter = provisionAdapter;
  }

  @Override
  public void removeNetworkElement(int neId) {
    try {
      crmCtrlTaskScheduler.submitProvisioningTask(neId, () -> {
        log.info("Clearing Segment cache for ne {}", neId);
        wdmRRM.clearSegmentCache(neId);
        otnRRM.clearSegmentCache(neId);
        ethRRM.clearSegmentCache(neId);
      });
    } catch (Exception e) {
      log.error("NE removal failed", e);
    }
  }

  @Override
  public void handleSegmentRequestInTask(EnvelopeOuterClass.Envelope envelope) {
    try {
      crmCtrlTaskScheduler.submitTask(() -> handleSegmentRequest(envelope));
    } catch (Exception e) {
      log.error("Handle segment request failed.", e);
    }
  }

  @Override
  public void handleProtectionSwitch(UUID neId, String protectionGroupEntityIndex) throws OperationFailedException {
    provisionAdapter.handleProtectionSwitch(neId, protectionGroupEntityIndex);
  }

  @Override
  public void handleAdminStateOperation(final UUID neId, final List<String> aids, final String protectionGroupIndex,
                                        final AdminState adminState, final LayerQualifier layerProtocolQualifier
  ) throws OperationFailedException {
    provisionAdapter.handleAdminStateOperation(neId, aids, protectionGroupIndex, adminState, layerProtocolQualifier);
  }

  void handleSegmentRequest(EnvelopeOuterClass.Envelope envelope) {
    CrmSegmentRequestDto dto;
    try {
      dto = SegmentRequestTranslator.translate(envelope);
    } catch (Exception e) {
      log.error("Failed to translate segment request, unable to send callback to NI!", e);
      return;
    }
    try {
      var context = buildSegmentRequestContext(dto);
      crmCtrlTaskScheduler.submitProvisioningTask(dto.neId, () -> handleSegmentProvisioningRequest(dto, context));
    } catch (Exception e) {
      log.error("Handle segment request failed for neId={}", dto.neId, e);
      niCallbackSender.sendFailure(dto.getId(), dto.getRequestType(), e,
        dto.getSrcAddress(), dto.getDstAddress(), List.of());
    }
  }

  void handleSegmentProvisioningRequest(CrmSegmentRequestDto dto, SegmentRequestContext context) {
    boolean sendSuccess;
    List<String> deletedSegments = new ArrayList<>();
    try {
      sendSuccess = handleRequest(dto, context, deletedSegments);
    } catch (Exception e) {
      // something went wrong during configuration on the device
      // --> request could not be finished successfully and we have to inform
      // NI controller about this using callback message with status FAILED
      if (dto.hasAssignRoles()) {
        log.error("Handle AssignRoles request failed for neId={}", dto.neId, e);
        niCallbackSender.sendAssignRolesFailure(e.getMessage(), dto.assignRoles.workingLegId(),
          dto.assignRoles.protectionLegId(), dto.getSrcAddress(), dto.getDstAddress());
      } else {
        log.error("Handle segment provision request failed for neId={}", dto.neId, e);
        niCallbackSender.sendFailure(dto.getId(), dto.getRequestType(), e,
          dto.getSrcAddress(), dto.getDstAddress(), List.of());
      }
      sendSuccess = false;
    }
    if (sendSuccess) {
      if (dto.hasAssignRoles()) {
        niCallbackSender.sendAssignRolesSuccess(dto.assignRoles.workingLegId(), dto.assignRoles.protectionLegId(),
          dto.getSrcAddress(), dto.getDstAddress());
      } else {
        niCallbackSender.sendSuccess(dto.getId(), dto.getRequestType(),
          dto.getSrcAddress(), dto.getDstAddress(), deletedSegments);
      }
      // request was completed successfully, and we have to send success message to NI controller
    }
  }

  private boolean handleRequest(CrmSegmentRequestDto request, SegmentRequestContext context, List<String> deletedSegments) {
    if (otnRRM.isSegmentRequestSupported(request)) {
      otnRRM.handleRequest(request);
      return true;
    } else if (ethRRM.isSegmentRequestSupported(request)) {
      ethRRM.handleRequest(request);
      return true;
    } else {
      var ret = wdmRRM.handleSegmentRequest(request, context);
      if (ret.deletedSegments() != null) {
        deletedSegments.addAll(ret.deletedSegments());
      }
      return ret.operationCompleted();
    }
  }

  private SegmentRequestContext buildSegmentRequestContext(CrmSegmentRequestDto request) {
    if (otnRRM.isSegmentRequestSupported(request)) {
      return null;
    } else if (ethRRM.isSegmentRequestSupported(request)) {
      return null;
    } else {
      return wdmRRM.buildSegmentRequestContext(request);
    }
  }
}
