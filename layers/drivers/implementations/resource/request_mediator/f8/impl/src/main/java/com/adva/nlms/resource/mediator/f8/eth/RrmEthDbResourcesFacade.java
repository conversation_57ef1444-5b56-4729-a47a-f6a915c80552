/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.eth;


import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.Ctp;
import com.adva.nlms.mediation.mo.inventory.resources.CtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;

import java.util.List;
import java.util.Optional;
import java.util.Set;

interface RrmEthDbResourcesFacade {
  Optional<CardRef> findCardFromCtp(int neId, Aid ctpAid);

  Optional<CardRef> findCardFromCtp(int neId, Uri uri);

  Optional<CardRef> findCardFromPlug(int neId, Aid plugAid);

  PlugRef findPlug(int neId, Aid plugAid);

  Optional<PlugRef> findPlugFromCtp(int neId, Aid ctpAid);

  Optional<PlugRef> findPlugFromCtp(int neId, Uri ctpUri);

  Optional<PtpRef> findPortFromCtp(int neId, Uri ctpUri);

  Optional<PtpRef> findPtp(int neId, Aid ptpAid);

  Optional<PtpRef> findPtp(int neId, Uri uri);

  Optional<CtpRef> findCtp(int neId, Aid ctpAid);

  Optional<Ctp> findCtpExtended(int neId, Aid ctpAid);

  Optional<CtpRef> findCtp(int neId, Uri uri);

  Optional<Uri> findCtpUriByUriPrefixAndParams(int neId, String uriPrefix, int tp, List<Long> ts);

  List<CtpRef> findAllCtpsFromPtp(int neId, Aid ptpAid);

  Optional<Uri> findSncUriByUriPrefixAndEndpoints(int neId, String uriPrefix, Set<String> aEndpointURIs, Set<String> zEndpointURIs);
}
