/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.resource.mediator.f8.wdm.OutageMonitor;
import com.adva.nlms.resource.mediator.f8.wdm.SegmentTerminationPoint;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.UUID;

class OutageMonitorTest {

  private static final int NE_ID = 4342;
  private static final String SEGMENT_ID = UUID.randomUUID().toString();
  private static final String TP = "Port-1/1/n";
  private static final String N_OTSIA_AID = "OTSIA-3/3/n/oms/spslg-1";
  private static final String C_OTSIA_AID = "OTSIA-3/3/c1/oms/spslg-1";

  private final OutageMonitor outageMonitor = new OutageMonitor();

  @Test
  void testPlanAlarm() {
    SegmentTerminationPoint segmentTerminationPoint = new SegmentTerminationPoint(NE_ID, SEGMENT_ID, TP);
    Assertions.assertFalse(outageMonitor.planAlarm(segmentTerminationPoint, N_OTSIA_AID));
    Assertions.assertFalse(outageMonitor.planAlarm(segmentTerminationPoint, C_OTSIA_AID));
  }

  @Test
  void testAlarmRaised() {
    SegmentTerminationPoint segmentTerminationPoint = new SegmentTerminationPoint(NE_ID, SEGMENT_ID, TP);
    outageMonitor.planAlarm(segmentTerminationPoint, N_OTSIA_AID);
    outageMonitor.alarmRaised(segmentTerminationPoint);
    Assertions.assertTrue(outageMonitor.planAlarm(segmentTerminationPoint, C_OTSIA_AID));
  }

  @Test
  void testRemoveOutagesForSegment() {
    SegmentTerminationPoint segmentTerminationPoint = new SegmentTerminationPoint(NE_ID, SEGMENT_ID, TP);
    outageMonitor.planAlarm(segmentTerminationPoint, N_OTSIA_AID);
    outageMonitor.alarmRaised(segmentTerminationPoint);
    outageMonitor.removeOutagesForSegment(NE_ID, SEGMENT_ID);
    Assertions.assertFalse(outageMonitor.clearAlarm(segmentTerminationPoint, N_OTSIA_AID));
  }

  @Test
  void testClearAlarm() {
    SegmentTerminationPoint segmentTerminationPoint = new SegmentTerminationPoint(NE_ID, SEGMENT_ID, TP);
    outageMonitor.planAlarm(segmentTerminationPoint, N_OTSIA_AID);
    outageMonitor.planAlarm(segmentTerminationPoint, C_OTSIA_AID);
    outageMonitor.alarmRaised(segmentTerminationPoint);
    Assertions.assertFalse(outageMonitor.clearAlarm(segmentTerminationPoint, N_OTSIA_AID));
    Assertions.assertTrue(outageMonitor.clearAlarm(segmentTerminationPoint, C_OTSIA_AID));
  }
}