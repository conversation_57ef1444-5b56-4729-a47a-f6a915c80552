/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.eth;

import com.adva.nlms.txprovisioning.api.NetworkElementIdHolder;

class ProvisionContext implements NetworkElementIdHolder {
  private final int neId;
  private final String segmentId;

  public ProvisionContext(int neId, String segmentId) {
    this.neId = neId;
    this.segmentId = segmentId;
  }

  @Override
  public int neId() {
    return neId;
  }

  public String segmentId() {
    return segmentId;
  }
}
