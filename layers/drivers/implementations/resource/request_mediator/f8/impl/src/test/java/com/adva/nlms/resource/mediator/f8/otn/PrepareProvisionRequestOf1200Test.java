/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: zbigniewj
 */

package com.adva.nlms.resource.mediator.f8.otn;


import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.provider.api.CapabilityProvider;
import com.adva.infrastructure.capabilityprovider.capabilities.CapabilityConfiguration;
import com.adva.infrastructure.capabilityprovider.core.CapabilityProviderConfiguration;
import com.adva.infrastructure.capabilityprovider.mo.MoCapabilityProviderConfiguration;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardClusterRef;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.CrossConnectRef;
import com.adva.nlms.mediation.mo.inventory.resources.Ctp;
import com.adva.nlms.mediation.mo.inventory.resources.CtpBuilder;
import com.adva.nlms.mediation.mo.inventory.resources.CtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.LineProtectionGroupRef;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.topology.NEData;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.OpticalParameters;
import com.adva.nlms.opticalparameters.api.Value;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.opticalparameters.api.enums.ParameterName;
import com.adva.nlms.opticalparameters.cim.api.CimValue;
import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;
import com.adva.nlms.resource.mediator.f8.InterfaceType;
import com.adva.nlms.resource.mediator.f8.ProtectionGroupFinder;
import com.adva.nlms.resource.mediator.f8.events.CtpCreatedEvent;
import com.adva.nlms.resource.mediator.f8.events.SncCreatedEvent;
import com.adva.nlms.resource.mediator.f8.internalpath.Complete;
import com.adva.nlms.resource.mediator.f8.internalpath.Connection;
import com.adva.nlms.resource.mediator.f8.internalpath.Container;
import com.adva.nlms.resource.mediator.f8.internalpath.CrossConnect;
import com.adva.nlms.resource.mediator.f8.internalpath.ForkPlacement;
import com.adva.nlms.resource.mediator.f8.internalpath.InternalPath;
import com.adva.nlms.resource.mediator.f8.internalpath.LabelDTO;
import com.adva.nlms.resource.mediator.f8.internalpath.MultiLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.OduType;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabelType;
import com.adva.nlms.resource.mediator.f8.internalpath.SignalType;
import com.adva.nlms.resource.mediator.f8.internalpath.StackedLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.TerminationPoint;
import com.adva.nlms.resource.mediator.f8.internalpath.Whole;
import com.adva.nlms.resource.mediator.f8.otn.CtpRequest;
import com.adva.nlms.resource.mediator.f8.otn.EpteRequest;
import com.adva.nlms.resource.mediator.f8.otn.LayerQualifierToCimString;
import com.adva.nlms.resource.mediator.f8.otn.OtnCimProvisionRequestParametersCreatorImpl;
import com.adva.nlms.resource.mediator.f8.otn.OtnProvisioningException;
import com.adva.nlms.resource.mediator.f8.otn.PrepareProvisionRequest;
import com.adva.nlms.resource.mediator.f8.otn.PtpParamsRequest;
import com.adva.nlms.resource.mediator.f8.otn.PtpRequest;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnCardCapabilities;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnCardCapabilitiesImpl;
import com.adva.nlms.resource.mediator.f8.otn.RrmOtnDbResourcesFacade;
import com.adva.nlms.resource.mediator.f8.otn.SncModifyRequest;
import com.adva.nlms.resource.mediator.f8.otn.SncRequest;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegment;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegmentRepository;
import com.adva.nlms.resource.mediator.f8.otn.api.in.SegmentID;
import com.adva.nlms.resource.provision.f8.api.in.MoCimParameter;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class PrepareProvisionRequestOf1200Test {

  private static final LayerQualifier layer = LayerQualifier.ODU2;
  private static final int NETWORK_ELEMENT_ID = 1;
  private static final int PORT_N_CTP_ID = 2;
  private static final String PORT_N_AID = "Port-1/1/n1";
  private static final String PORT_N_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/nw,1";
  private static final String PORT_N_OPTICAL_AID = "OTUC2-1/1/n1/otuc2";
  private static final String PORT_N_OPTICAL_URI = PORT_N_URI + "/ctp/otuc2";
  private static final String PORT_N_CTP_URI = PORT_N_OPTICAL_URI + "/ctp/oduc2/ctp/" + LayerQualifierToCimString.convert(layer) + "-";
  private static final String PORT_N_CTP_URI_FULL = PORT_N_CTP_URI + PORT_N_CTP_ID;

  private static final String CARD2_PORT_N_AID = "Port-1/2/n1";
  private static final String CARD2_PORT_N_OPTICAL_AID = "OTUC2-1/2/n1/otuc2";
  private static final String CARD2_PORT_N_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1";
  private static final String CARD2_PORT_N_OPTICAL_URI = CARD2_PORT_N_URI + "/ctp/otuc2";
  private static final String CARD2_PORT_N_CTP_URI = CARD2_PORT_N_OPTICAL_URI + "/ctp/oduc2/ctp/" + LayerQualifierToCimString.convert(layer) + "-";
  private static final String CARD2_PORT_N_CTP_URI_FULL = CARD2_PORT_N_CTP_URI + PORT_N_CTP_ID;

  private static final String PORT_C_AID = "Port-1/1/c3";
  private static final String PORT_C_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,3";
  private static final String PORT_C_OPTICAL_URI_OTU2 = PORT_C_URI + "/ctp/otu2";
  private static final String PORT_C_CTP_URI = PORT_C_OPTICAL_URI_OTU2 + "/ctp/" + LayerQualifierToCimString.convert(layer);

  private static final String PORT_C_OPTICAL_URI_ET25 = PORT_C_URI + "/ctp/et25";

  private static final String PLUG_C_AID = "Plug-1/1/c3";
  private static final String PLUG_C_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/plgh,3";

  private static final String PLUG_N_AID = "Plug-1/1/n1";
  private static final String PLUG_N_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/plgh,nw,1";

  private static final String PORT_C2_2_AID = "Port-1/1/c2-2";
  private static final String PORT_C2_2_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,2-2";
  private static final String PLUG_C2_2_AID = "Plug-1/1/c2";
  private static final String PLUG_C2_2_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/plgh,2";

  private static final String PORT_C10_AID = "Port-1/1/c10";
  private static final String PORT_C10_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,10";
  private static final String PLUG_C10_AID = "Plug-1/1/c10";
  private static final String PLUG_C10_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/plgh,10";

  private static final String ET25_CTP_AID = "ET25-CtpAidSuffix";
  private static final String OTU2_CTP_AID = "OTU2-CtpAidSuffix";
  private static final String ODU2_CTP_AID = "ODU2-CtpAidSuffix";
  private static final String OTU2E_CTP_AID = "OTU2E-CtpAidSuffix";
  private static final String ODU2E_CTP_AID = "ODU2E-CtpAidSuffix";
  private static final String OTU4_CTP_AID = "OTU4-CtpAidSuffix";
  private static final String ODU4_CTP_AID = "ODU4-CtpAidSuffix";
  private static final String OT200_CTP_AID = "OT200-CtpAidSuffix";


  private static final String CARD_AID = "Module-1/1";
  private static final String CARD2_AID = "Module-1/2";
  private static final String CARD_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card";
  private static final String CARD2_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card";
  private static final String SNC_URI = generateSncUri(Optional.empty());
  private static final String MODULE_TYPE = "OF-2D16DCT";
  private static final String PLUG_C_25GB_TYPE = "SFP28-25GU-LR-SM-LC-TIN";
  private static final String PLUG_C_TYPE = "SFP+-11GU-C1270L-SM-LC";
  private static final String PLUG_N_TYPE = "CFP2-224G-#DCTC-SM-LC";
  private static final List<OpticalParameters.SelectedParameter> inParamsEt25 = List.of(generateGlqOpticalParam("ETH-25G"));

  private static final List<String> OTU2_LAYERS = List.of("och", "otu2");
  private static final List<String> ODU2_LAYERS = List.of("odu2");
  private static final List<String> OTU2E_LAYERS = List.of("och", "otu2e");
  private static final List<String> ODU2E_LAYERS = List.of("odu2e");
  private static final List<String> _25GBE_LAYERS = List.of("ety25g", "etc25g", "mac");
  private static final List<String> OTU4_LAYERS = List.of("ops-multilink", "otu4");
  private static final List<String> ODU4_LAYERS = List.of("odu4");
  private static final List<String> OTUC2PA_LAYERS = List.of("och", "otuc2pa", "oduc2pa");
  private static final List<String> OTUC2_LAYERS = List.of("och", "flexo-2", "otuc2");

  private static final String CARD_CLUSTER_URI = "/mit/me/1/cclst/ofdual,cluster1";
  private static final String CARD_CLUSTER_SNC_URI = "/mit/me/1/cclst/ofdual,cluster1/sn/" + LayerQualifierToCimString.convert(layer) + "/snc";

  private static final MoCimParameter refTermParams = new MoCimParameter("", "termination-mode", new CimValue.Enum("nss"));
  private static final MoCimParameter refTermParamsTmsn = new MoCimParameter("", "termination-mode", new CimValue.Enum("tmsn"));

  private final RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade = mock(RrmOtnDbResourcesFacade.class);
  private final NEDataProvider neDataProvider = mock(NEDataProvider.class);
  private static final CapabilityProvider capabilityProvider = new CapabilityProviderConfiguration()
    .capabilityProvider(new CapabilityConfiguration().capabilityRepository(), null, null);
  private static final MoCapabilityProvider moCapabilityProvider = new MoCapabilityProviderConfiguration().moCapabilityProvider();
  private final RrmOtnCardCapabilities rrmOtnCardCapabilities = new RrmOtnCardCapabilitiesImpl(neDataProvider, capabilityProvider, moCapabilityProvider);
  private final MoCapabilityProvider provider = new MoCapabilityProviderConfiguration().moCapabilityProvider();
  private final OtnCimProvisionRequestParametersCreatorImpl cimProvisionRequestParametersCreator = new OtnCimProvisionRequestParametersCreatorImpl(provider);
  private final Provision provisionApi = mock(Provision.class);
  private final OtnSegmentRepository otnSegmentRepository = mock(OtnSegmentRepository.class);
  private final ProtectionGroupFinder protectionGroupFinder = Mockito.mock(ProtectionGroupFinder.class);

  private final PrepareProvisionRequest sut = new PrepareProvisionRequest(
    cimProvisionRequestParametersCreator, provisionApi, rrmOtnCardCapabilities, rrmOtnDbResourcesFacade, otnSegmentRepository, protectionGroupFinder, moCapabilityProvider);

  private static TerminationPoint generateTerminatePoint(String aid, OtnLabelType type, boolean terminate) {
    return new TerminationPoint(aid, new LabelDTO(new MultiLabel(List.of(new OtnLabel(type)))), terminate);
  }

  private static TerminationPoint generateTerminatePointStackedLabel(String aid, StackedLabel label, boolean terminate) {
    return new TerminationPoint(aid, label, terminate);
  }

  private static Optional<CardRef> generateCardRef() {
    return Optional.of(new CardRef(NETWORK_ELEMENT_ID, new Aid(CARD_AID), new Uri(CARD_URI), MODULE_TYPE, 0));
  }

  private static PlugRef generatePlugRef_C() {
    return PlugRef.builder()
      .setNeId(NETWORK_ELEMENT_ID)
      .setAid(PLUG_C_AID)
      .setUri(PLUG_C_URI)
      .setType(PLUG_C_TYPE)
      .build();
  }

  private static PlugRef generatePlugRef_C(String aid, String uri, String type) {
    return PlugRef.builder()
      .setNeId(NETWORK_ELEMENT_ID)
      .setAid(aid)
      .setUri(uri)
      .setType(type)
      .build();
  }

  private static PlugRef generatePlugRef_C_ETH25GB() {
    return PlugRef.builder()
      .setNeId(NETWORK_ELEMENT_ID)
      .setAid(PLUG_C_AID)
      .setUri(PLUG_C_URI)
      .setType(PLUG_C_25GB_TYPE)
      .build();
  }

  private static PlugRef generatePlugRef_N_244() {
    return PlugRef.builder()
      .setNeId(NETWORK_ELEMENT_ID)
      .setAid(PLUG_N_AID)
      .setUri(PLUG_N_URI)
      .setType(PLUG_N_TYPE)
      .build();
  }

  private static String generateSncUri(Optional<LayerQualifier> signalType) {
    return "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/sn/" + LayerQualifierToCimString.convert(signalType.orElse(layer)) + "/snc";
  }

  private static OpticalParameters.SelectedParameter generateGlqOpticalParam(String value) {
    return new OpticalParameters.SelectedParameter(ParameterName.GLQ, new Value.Enum(value));
  }

  private static CrmSegmentRequestDto generateCrossConnectRequest(List<OpticalParameters.SelectedParameter> params,
                                                                  String src,
                                                                  OtnLabelType srcLabel,
                                                                  boolean srcTerminate,
                                                                  String dst,
                                                                  OtnLabelType dstLabel,
                                                                  boolean dstTerminate,
                                                                  Optional<LayerQualifier> signalType,
                                                                  InterfaceType srcInterfaceType,
                                                                  InterfaceType dstInterfaceType) {
    var request = new CrmSegmentRequestDto();
    request.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    request.portParams = params;
    request.srcTp = src;
    request.dstTp = dst;
    request.setSrcInterfaceType(srcInterfaceType);
    request.setDstInterfaceType(dstInterfaceType);
    var srcTtp = generateTerminatePoint(request.srcTp, srcLabel, srcTerminate);
    var dstTtp = generateTerminatePoint(request.dstTp, dstLabel, dstTerminate);


    request.internalPath = new InternalPath(null,
      List.of(new Connection(
        new SignalType(new OduType(signalType.orElse(layer), 0)), List.of(
        new CrossConnect(srcTtp, dstTtp, ForkPlacement.NONE)))),
      null);
    return request;
  }

  private static CrmSegmentRequestDto generateCrossConnectRequestStackedLabel(List<OpticalParameters.SelectedParameter> params,
                                                                              String src,
                                                                              OtnLabelType srcLabel,
                                                                              boolean srcTerminate,
                                                                              String dst,
                                                                              StackedLabel dstLabel,
                                                                              boolean dstTerminate,
                                                                              Optional<LayerQualifier> signalType,
                                                                              InterfaceType srcInterfaceType) {
    var request = new CrmSegmentRequestDto();
    request.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    request.portParams = params;
    request.srcTp = src;
    request.dstTp = dst;
    request.setSrcInterfaceType(srcInterfaceType);
    var srcTtp = generateTerminatePoint(request.srcTp, srcLabel, srcTerminate);
    var dstTtp = generateTerminatePointStackedLabel(request.dstTp, dstLabel, dstTerminate);


    request.internalPath = new InternalPath(null,
      List.of(new Connection(
        new SignalType(new OduType(signalType.orElse(layer), 0)), List.of(
        new CrossConnect(srcTtp, dstTtp, ForkPlacement.NONE)))),
      null);
    return request;
  }

  private static CrmSegmentRequestDto generateCrossConnectRequestWithForkPlacement(List<OpticalParameters.SelectedParameter> params,
                                                                                   String src,
                                                                                   OtnLabelType srcLabel,
                                                                                   boolean srcTerminate,
                                                                                   String dst,
                                                                                   OtnLabelType dstLabel,
                                                                                   boolean dstTerminate,
                                                                                   Optional<LayerQualifier> signalType,
                                                                                   InterfaceType srcInterfaceType,
                                                                                   InterfaceType dstInterfaceType,
                                                                                   ForkPlacement forkPlacement) {
    var request = new CrmSegmentRequestDto();
    request.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    request.portParams = params;
    request.srcTp = src;
    request.dstTp = dst;
    request.setSrcInterfaceType(srcInterfaceType);
    request.setDstInterfaceType(dstInterfaceType);
    var srcTtp = generateTerminatePoint(request.srcTp, srcLabel, srcTerminate);
    var dstTtp = generateTerminatePoint(request.dstTp, dstLabel, dstTerminate);

    request.internalPath = new InternalPath(null,
      List.of(new Connection(
        new SignalType(new OduType(signalType.orElse(layer), 0)), List.of(
        new CrossConnect(srcTtp, dstTtp, forkPlacement)))),
      null);
    return request;
  }

  private static CrmSegmentRequestDto generateTerminationPointRequest(List<OpticalParameters.SelectedParameter> params,
                                                                      String src,
                                                                      OtnLabelType srcLabel,
                                                                      boolean srcTerminate,
                                                                      LayerQualifier signalType) {
    var request = new CrmSegmentRequestDto();
    request.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    request.portParams = params;
    request.srcTp = src;
    request.dstTp = src;
    var terminationPoint = generateTerminatePoint(request.srcTp, srcLabel, srcTerminate);
    request.internalPath = new InternalPath(null, List.of(new Connection(new SignalType(new OduType(signalType, 0)), List.of(terminationPoint))), null);
    return request;
  }


  private CtpRequest generateCtpRequest(String uri, boolean isAdopt, Set<MoCimParameter> params) {
    return new CtpRequest(NETWORK_ELEMENT_ID, uri, isAdopt, false, params, provisionApi);
  }

  private PtpRequest generatePtpRequest(String uri, boolean isAdopt) {
    return new PtpRequest(NETWORK_ELEMENT_ID, uri, isAdopt, provisionApi);
  }

  private PtpParamsRequest generatePtpParamsRequest(String uri, boolean isAdopt, Set<MoCimParameter> params) {
    return new PtpParamsRequest(NETWORK_ELEMENT_ID, uri, isAdopt, params, provisionApi);
  }

  void init(PlugRef plugRef) {
    when(rrmOtnDbResourcesFacade.findCardFromModule(NETWORK_ELEMENT_ID, new Aid(CARD_AID)))
      .thenReturn(generateCardRef().get());

    when(neDataProvider.getNeData(anyInt()
    )).thenReturn(new NEData(NETWORK_ELEMENT_ID, 0,
      "FSP 3000C", null, null, "6.5.1")
    );

    when(rrmOtnDbResourcesFacade.findCardFromPlug(
      eq(NETWORK_ELEMENT_ID),
      eq(plugRef.aid())
    )).thenReturn(
      generateCardRef()
    );

    when(rrmOtnDbResourcesFacade.findPlug(
      eq(NETWORK_ELEMENT_ID),
      eq(plugRef.aid())
    )).thenReturn(
      Optional.of(plugRef)
    );

    when(rrmOtnDbResourcesFacade.findCardFromPlug(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PLUG_N_AID))
    )).thenReturn(
      generateCardRef()
    );

    when(rrmOtnDbResourcesFacade.findCardFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C_AID))
    )).thenReturn(
      generateCardRef()
    );

    when(rrmOtnDbResourcesFacade.findCardFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      generateCardRef()
    );

    when(rrmOtnDbResourcesFacade.findPlugFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C_AID))
    )).thenReturn(
      Optional.of(plugRef)
    );
  }

  @Test
  void prepareRequestCtoNTermN() {
    init(generatePlugRef_C());

    when(rrmOtnDbResourcesFacade.findPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C_AID))
    )).thenReturn(
      Optional.of(new PtpRef(NETWORK_ELEMENT_ID, null, null, null))
    );

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      List.of(createCtp(NETWORK_ELEMENT_ID, new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OPTICAL_URI), OTUC2PA_LAYERS))
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OPTICAL_URI))
    )).thenReturn(
      Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OPTICAL_URI), OTUC2_LAYERS))
    );

    when(provisionApi.getAvailableCtpEntity(
      eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
      eq(PORT_N_CTP_URI + PORT_N_CTP_ID)
    )).thenReturn(
      Optional.of(PORT_N_CTP_ID)
    );

    var C_PORT_LABEL = new Whole();
    var N_PORT_LABEL = new Complete(2, List.of(1, 2, 3, 4, 5, 6, 7, 8));
    var motrait = LayerQualifierToCimString.convert(layer) + "/odtu";
    var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(2));
    var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L)));
    var termParam = new MoCimParameter("", "termination-mode", new CimValue.Enum("nss"));
    var request = generateCrossConnectRequest(List.of(), PORT_C_AID, C_PORT_LABEL, false,
      PORT_N_AID, N_PORT_LABEL, true, Optional.empty(), InterfaceType.ENNI, InterfaceType.UNSPECIFIED);

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(5, result.size());
      assertInstanceOf(CtpRequest.class, result.get(0));
      assertEquals(generateCtpRequest(PORT_C_OPTICAL_URI_OTU2, false, Set.of()), result.get(0));
      assertInstanceOf(PtpParamsRequest.class, result.get(1));
      assertEquals(generatePtpParamsRequest(PORT_C_URI, true, Set.of()), result.get(1));
      assertInstanceOf(CtpRequest.class, result.get(2));
      assertEquals(generateCtpRequest(PORT_C_CTP_URI, false, Set.of(termParam)), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI_FULL, false,
          Set.of(tpParam, tsParam,refTermParamsTmsn)),
        result.get(3));
      assertInstanceOf(SncRequest.class, result.get(4));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, SNC_URI, false,
        Set.of(PORT_C_CTP_URI),
        Set.of(PORT_N_CTP_URI_FULL),
        provisionApi), result.get(4));
    } catch (Exception e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void prepareRequestCtoNoduf_25g() {
    init(generatePlugRef_C_ETH25GB());

    when(rrmOtnDbResourcesFacade.findPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C_AID))
    )).thenReturn(
      Optional.of(new PtpRef(NETWORK_ELEMENT_ID, null, null, null))
    );

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      List.of(createCtp(NETWORK_ELEMENT_ID, new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OPTICAL_URI), OTUC2_LAYERS))
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C_OPTICAL_URI_ET25))
    )).thenReturn(
      Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(ET25_CTP_AID), new Uri(PORT_C_OPTICAL_URI_ET25), _25GBE_LAYERS))
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OPTICAL_URI))
    )).thenReturn(
      Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OPTICAL_URI), OTUC2_LAYERS))
    );

    when(provisionApi.getAvailableCtpEntity(
      eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
      eq(PORT_N_OPTICAL_URI + "/ctp/oduc2/ctp/oduf-" + PORT_N_CTP_ID)
    )).thenReturn(
      Optional.of(PORT_N_CTP_ID)
    );

    var signalType = Optional.of(LayerQualifier.ODUFLEX);
    var C_PORT_LABEL = new Whole();
    var N_PORT_LABEL = new Complete(2, List.of(1, 2, 3, 4, 5));
    var motrait = LayerQualifierToCimString.convert(LayerQualifier.ODUFLEX) + "/odtu";
    var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(2));
    var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L, 5L)));
    var ofcbrcl = new MoCimParameter("oduf", "ofcbrcl", new CimValue.Enum("eth25g"));
    var request = generateCrossConnectRequest(inParamsEt25, PORT_C_AID, C_PORT_LABEL, false,
      PORT_N_AID, N_PORT_LABEL, true, signalType, InterfaceType.UNI, InterfaceType.UNSPECIFIED);
    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(5, result.size());
      assertInstanceOf(CtpRequest.class, result.get(0));
      assertEquals(generateCtpRequest(PORT_C_OPTICAL_URI_ET25, false, Set.of()), result.get(0));
      assertInstanceOf(PtpParamsRequest.class, result.get(1));
      assertEquals(generatePtpParamsRequest(PORT_C_URI, true, Set.of()), result.get(1));
      assertInstanceOf(CtpRequest.class, result.get(2));
      assertEquals(generateCtpRequest(PORT_C_OPTICAL_URI_ET25 + "/ctp/oduf", false, Set.of()), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_N_OPTICAL_URI + "/ctp/oduc2/ctp/oduf-" + PORT_N_CTP_ID, false,
          Set.of(tpParam, tsParam, ofcbrcl)),
        result.get(3));
      assertInstanceOf(SncRequest.class, result.get(4));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, generateSncUri(signalType), false,
        Set.of(PORT_C_OPTICAL_URI_ET25 + "/ctp/oduf"),
        Set.of(PORT_N_OPTICAL_URI + "/ctp/oduc2/ctp/oduf-" + PORT_N_CTP_ID),
        provisionApi), result.get(4));
    } catch (Exception e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void prepareRequestCtoNoduf_400g() {
    init(generatePlugRef_C_ETH25GB());

    when(rrmOtnDbResourcesFacade.findPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C_AID))
    )).thenReturn(
      Optional.of(new PtpRef(NETWORK_ELEMENT_ID, null, null, null))
    );

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      List.of(createCtp(NETWORK_ELEMENT_ID, new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OPTICAL_URI), OTUC2_LAYERS))
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C_OPTICAL_URI_ET25))
    )).thenReturn(
      Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(ET25_CTP_AID), new Uri(PORT_C_OPTICAL_URI_ET25), _25GBE_LAYERS))
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OPTICAL_URI))
    )).thenReturn(
      Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OPTICAL_URI), OTUC2_LAYERS))
    );

    when(provisionApi.getAvailableCtpEntity(
      eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
      eq(PORT_N_OPTICAL_URI + "/ctp/oduc2/ctp/oduf-" + 5)
    )).thenReturn(
      Optional.of(5)
    );

    var signalType = Optional.of(LayerQualifier.ODUFLEX);
    var C_PORT_LABEL = new Whole();
    var N_PORT_LABEL = new Complete(5, List.of(1, 2, 3, 4, 5, 6, 7, 8));
    var motrait = LayerQualifierToCimString.convert(LayerQualifier.ODUFLEX) + "/odtu";
    var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(5));
    var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L)));
    var ofcbrcl = new MoCimParameter("oduf", "ofcbrcl", new CimValue.Enum("eth25g"));
    var request = generateCrossConnectRequest(inParamsEt25, PORT_C_AID, C_PORT_LABEL, false,
      PORT_N_AID, N_PORT_LABEL, true, signalType, InterfaceType.UNI, InterfaceType.UNSPECIFIED);
    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(5, result.size());
      assertInstanceOf(CtpRequest.class, result.get(0));
      assertEquals(generateCtpRequest(PORT_C_OPTICAL_URI_ET25, false, Set.of()), result.get(0));
      assertInstanceOf(PtpParamsRequest.class, result.get(1));
      assertEquals(generatePtpParamsRequest(PORT_C_URI, true, Set.of()), result.get(1));
      assertInstanceOf(CtpRequest.class, result.get(2));
      assertEquals(generateCtpRequest(PORT_C_OPTICAL_URI_ET25 + "/ctp/oduf", false, Set.of()), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_N_OPTICAL_URI + "/ctp/oduc2/ctp/oduf-" + 5, false,
          Set.of(tpParam, tsParam, ofcbrcl)),
        result.get(3));
      assertInstanceOf(SncRequest.class, result.get(4));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, generateSncUri(signalType), false,
        Set.of(PORT_C_OPTICAL_URI_ET25 + "/ctp/oduf"),
        Set.of(PORT_N_OPTICAL_URI + "/ctp/oduc2/ctp/oduf-" + 5),
        provisionApi), result.get(4));
    } catch (Exception e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void prepareAdoptRequestCtoN() {
    init(generatePlugRef_C());

    when(rrmOtnDbResourcesFacade.findCtp(
      eq(NETWORK_ELEMENT_ID),
      any()
    )).thenReturn(
      Optional.of(new CtpRef(NETWORK_ELEMENT_ID, null, null, null))
    );

    when(rrmOtnDbResourcesFacade.findPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C_AID))
    )).thenReturn(
      Optional.of(new PtpRef(NETWORK_ELEMENT_ID, null, null, null))
    );

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      List.of(createCtp(NETWORK_ELEMENT_ID, new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OPTICAL_URI), OTUC2_LAYERS))
    );

    when(rrmOtnDbResourcesFacade.findCtpUriByUriPrefixAndParams(
      eq(NETWORK_ELEMENT_ID),
      eq(PORT_N_CTP_URI),
      eq(2),
      eq(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L))
    )).thenReturn(Optional.of(new Uri(PORT_N_CTP_URI + PORT_N_CTP_ID)));

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C_OPTICAL_URI_OTU2))
    )).thenReturn(
      Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(OTU2_CTP_AID), new Uri(PORT_C_OPTICAL_URI_OTU2), OTU2_LAYERS))
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OPTICAL_URI))
    )).thenReturn(
      Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OPTICAL_URI), OTUC2_LAYERS))
    );

    when(rrmOtnDbResourcesFacade.findSncUriByUriPrefixAndEndpoints(eq(NETWORK_ELEMENT_ID), eq(SNC_URI),
      eq(Set.of(PORT_C_CTP_URI)), eq(Set.of(PORT_N_CTP_URI + PORT_N_CTP_ID))))
      .thenReturn(Optional.of(new Uri(SNC_URI + "/1")));

    var C_PORT_LABEL = new Whole();
    var N_PORT_LABEL = new Complete(2, List.of(1, 2, 3, 4, 5, 6, 7, 8));
    var motrait = LayerQualifierToCimString.convert(layer) + "/odtu";
    var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(2));
    var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L)));
    var termParam = new MoCimParameter("", "termination-mode", new CimValue.Enum("nss"));
    var request = generateCrossConnectRequest(List.of(), PORT_C_AID, C_PORT_LABEL,
      false,
      PORT_N_AID,
      N_PORT_LABEL,
      false,
      Optional.empty(),
      InterfaceType.ENNI, InterfaceType.UNSPECIFIED);

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(5, result.size());
      assertInstanceOf(CtpRequest.class, result.get(0));
      assertEquals(generateCtpRequest(PORT_C_OPTICAL_URI_OTU2, true, Set.of()), result.get(0));
      assertInstanceOf(PtpParamsRequest.class, result.get(1));
      assertEquals(generatePtpParamsRequest(PORT_C_URI, true, Set.of()), result.get(1));
      assertInstanceOf(CtpRequest.class, result.get(2));
      assertEquals(generateCtpRequest(PORT_C_CTP_URI, true, Set.of(termParam)), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI + PORT_N_CTP_ID, true, Set.of(tpParam, tsParam, termParam)), result.get(3));
      assertInstanceOf(SncRequest.class, result.get(4));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, SNC_URI + "/1", true,
        Set.of(PORT_C_CTP_URI),
        Set.of(PORT_N_CTP_URI + PORT_N_CTP_ID),
        provisionApi), result.get(4));
    } catch (Exception e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void prepareRequestNtoCTermC_noDualMode() {
    init(generatePlugRef_C());

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      List.of(createCtp(NETWORK_ELEMENT_ID, new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OPTICAL_URI), OTUC2_LAYERS))
    );

    when(rrmOtnDbResourcesFacade.findPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C_AID))
    )).thenReturn(
      Optional.of(new PtpRef(NETWORK_ELEMENT_ID, null, null, null))
    );

    when(rrmOtnDbResourcesFacade.findCardFromPlug(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid("Plug-1/1/n1"))
    )).thenReturn(
      Optional.of(new CardRef(NETWORK_ELEMENT_ID, new Aid(CARD_AID), new Uri(CARD_URI), MODULE_TYPE, 1))
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OPTICAL_URI))
    )).thenReturn(
      Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OPTICAL_URI), OTUC2_LAYERS))
    );

    when(provisionApi.getAvailableCtpEntity(
      eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
      eq(PORT_N_CTP_URI + PORT_N_CTP_ID)
    )).thenReturn(
      Optional.of(PORT_N_CTP_ID)
    );

    var C_PORT_LABEL = new Whole();
    var N_PORT_LABEL = new Complete(2, List.of(1, 2, 3, 4, 5, 6, 7, 8));
    var motrait = LayerQualifierToCimString.convert(layer) + "/odtu";
    var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(2));
    var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L)));
    var termParam = new MoCimParameter("", "termination-mode", new CimValue.Enum("nss"));
    var request = generateCrossConnectRequest(List.of(), PORT_N_AID, N_PORT_LABEL,
      false,
      PORT_C_AID,
      C_PORT_LABEL,
      false,
      Optional.empty(),
      InterfaceType.UNSPECIFIED, InterfaceType.ENNI);

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(5, result.size());
      assertInstanceOf(CtpRequest.class, result.get(0));
      assertEquals(generateCtpRequest(PORT_C_OPTICAL_URI_OTU2, false, Set.of()), result.get(0));
      assertInstanceOf(PtpParamsRequest.class, result.get(1));
      assertEquals(generatePtpParamsRequest(PORT_C_URI, true, Set.of()), result.get(1));
      assertInstanceOf(CtpRequest.class, result.get(2));
      assertEquals(generateCtpRequest(PORT_C_CTP_URI, false, Set.of(termParam)), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI_FULL, false,
        Set.of(tpParam, tsParam, refTermParams)), result.get(3));
      assertInstanceOf(SncRequest.class, result.get(4));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, SNC_URI, false,
        Set.of(PORT_C_CTP_URI),
        Set.of(PORT_N_CTP_URI_FULL),
        provisionApi), result.get(4));
    } catch (Exception e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void prepareRequestNtoCTermC_dualMode() {
    when(rrmOtnDbResourcesFacade.findCardFromModule(NETWORK_ELEMENT_ID, new Aid(CARD_AID)))
      .thenReturn(new CardRef(NETWORK_ELEMENT_ID, new Aid(CARD_AID), new Uri(CARD_URI), MODULE_TYPE, 0, "dualcc", List.of(CARD2_URI)));

    when(rrmOtnDbResourcesFacade.findCardFromModule(NETWORK_ELEMENT_ID, new Aid(CARD2_AID)))
      .thenReturn(new CardRef(NETWORK_ELEMENT_ID, new Aid(CARD2_AID), new Uri(CARD2_URI), MODULE_TYPE, 0, "dualcc", List.of(CARD_URI)));

    when(rrmOtnDbResourcesFacade.findAssociatedCardCluster(NETWORK_ELEMENT_ID, new Aid(CARD2_AID)))
      .thenReturn(createCardClusterRef());

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(CARD2_PORT_N_AID))
    )).thenReturn(
      List.of(createCtp(NETWORK_ELEMENT_ID, new Aid(CARD2_PORT_N_OPTICAL_AID), new Uri(CARD2_PORT_N_OPTICAL_URI), OTUC2_LAYERS))
    );

    when(rrmOtnDbResourcesFacade.findPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C_AID))
    )).thenReturn(
      Optional.of(new PtpRef(NETWORK_ELEMENT_ID, null, null, null))
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(CARD2_PORT_N_OPTICAL_URI))
    )).thenReturn(
      Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(CARD2_PORT_N_OPTICAL_AID), new Uri(CARD2_PORT_N_OPTICAL_URI), OTUC2_LAYERS))
    );

    when(provisionApi.getAvailableCtpEntity(
      eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
      eq(CARD2_PORT_N_CTP_URI + PORT_N_CTP_ID)
    )).thenReturn(
      Optional.of(PORT_N_CTP_ID)
    );

    var C_PORT_LABEL = new Whole();
    var N_PORT_LABEL = new Complete(2, List.of(1, 2, 3, 4, 5, 6, 7, 8));
    var motrait = LayerQualifierToCimString.convert(layer) + "/odtu";
    var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(2));
    var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L)));
    var termParam = new MoCimParameter("", "termination-mode", new CimValue.Enum("nss"));
    var request = generateCrossConnectRequest(List.of(), CARD2_PORT_N_AID, N_PORT_LABEL,
      false,
      PORT_C_AID,
      C_PORT_LABEL,
      false,
      Optional.empty(),
      InterfaceType.UNSPECIFIED, InterfaceType.ENNI);

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(5, result.size());
      assertInstanceOf(CtpRequest.class, result.get(0));
      assertEquals(generateCtpRequest(PORT_C_OPTICAL_URI_OTU2, false, Set.of()), result.get(0));
      assertInstanceOf(PtpParamsRequest.class, result.get(1));
      assertEquals(generatePtpParamsRequest(PORT_C_URI, true, Set.of()), result.get(1));
      assertInstanceOf(CtpRequest.class, result.get(2));
      assertEquals(generateCtpRequest(PORT_C_CTP_URI, false, Set.of(termParam)), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(CARD2_PORT_N_CTP_URI_FULL, false,
        Set.of(tpParam, tsParam,refTermParams)), result.get(3));
      assertInstanceOf(SncRequest.class, result.get(4));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, CARD_CLUSTER_SNC_URI, false,
        Set.of(PORT_C_CTP_URI),
        Set.of(CARD2_PORT_N_CTP_URI_FULL),
        provisionApi), result.get(4));
    } catch (Exception e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void prepareRequestCtoN_exception_missingGlqOnClient() {
    init(generatePlugRef_C());

    var signalType = Optional.of(LayerQualifier.ODUFLEX);
    var C_PORT_LABEL = new Whole();
    var N_PORT_LABEL = new Complete(5, List.of(1, 2, 3, 4, 5, 6, 7, 8));
    var request = generateCrossConnectRequest(List.of(), PORT_C_AID, C_PORT_LABEL, false,
      PORT_N_AID, N_PORT_LABEL, true, signalType, InterfaceType.ENNI, InterfaceType.UNSPECIFIED);

    try {
      sut.prepareRequest(NETWORK_ELEMENT_ID, request);
    } catch (OtnProvisioningException e) {
      assertEquals("Could not deduce expected payload for client port " + PORT_C_AID +
        " provisioning. GLQ parameter is missing and layerQualifier " + signalType.get() +
        " does not match any OTUk payload.", e.getMessage());
    }
  }

  @Test
  void prepareRequestCtoN_extendedClientPtp_c2_2() {
    final var signalType = Optional.of(LayerQualifier.ODU2E);
    final String PLUG_C2_TYPE = "QSFP28-112G-AOC-0500";
    final String PORT_C2_2_OPTICAL_URI = PORT_C2_2_URI + "/ctp/otu2e";
    final String PORT_C2_2_CTP_URI = PORT_C2_2_OPTICAL_URI + "/ctp/" + LayerQualifierToCimString.convert(signalType.get());
    final String PORT_N_CTP_URI = PORT_N_OPTICAL_URI + "/ctp/oduc2/ctp/" + LayerQualifierToCimString.convert(signalType.get()) + "-";
    final String PORT_N_CTP_URI_FULL = PORT_N_CTP_URI + PORT_N_CTP_ID;
    final PlugRef plugRef = PlugRef.builder()
      .setNeId(NETWORK_ELEMENT_ID)
      .setAid(PLUG_C2_2_AID)
      .setUri(PLUG_C2_2_URI)
      .setType(PLUG_C2_TYPE)
      .build();
    final var opticalParams = List.of(
      new OpticalParameters.SelectedParameter(ParameterName.FEC, new Value.Enum("GFEC")),
      new OpticalParameters.SelectedParameter(ParameterName.FTS_CONTROL, new Value.Enum("Laser off immediate"))
    );

    init(generatePlugRef_C());

    when(rrmOtnDbResourcesFacade.findCardFromPlug(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PLUG_C2_2_AID))
    )).thenReturn(
      generateCardRef()
    );

    when(rrmOtnDbResourcesFacade.findPlug(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PLUG_C2_2_AID))
    )).thenReturn(
      Optional.of(plugRef)
    );

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      createListOfNetworkPortCtpOtuc2()
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C2_2_OPTICAL_URI))
    )).thenReturn(
      createClientPortCtpOtu2E(PORT_C2_2_OPTICAL_URI)
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OPTICAL_URI))
    )).thenReturn(
      createNetworkPortCtpOtuc2()
    );

    when(provisionApi.getAvailableCtpEntity(
      eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
      eq(PORT_N_CTP_URI + PORT_N_CTP_ID)
    )).thenReturn(
      Optional.of(PORT_N_CTP_ID)
    );

    var C_PORT_LABEL = new Whole();
    var N_PORT_LABEL = new Complete(2, List.of(1, 2, 3, 4, 5, 6, 7, 8));
    var motrait = LayerQualifierToCimString.convert(signalType.get()) + "/odtu";
    var ftsCtrlParam = new MoCimParameter("opt", "fts-control", new CimValue.Enum("imdt"));
    var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(2));
    var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L)));
    var fecParam = new MoCimParameter("otu2e", "fec-type", new CimValue.Enum("gfec"));

    var request = generateCrossConnectRequest(opticalParams, PORT_C2_2_AID, C_PORT_LABEL, false,
      PORT_N_AID, N_PORT_LABEL, true, signalType, InterfaceType.ENNI, InterfaceType.UNSPECIFIED);

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(6, result.size());
      assertInstanceOf(PtpRequest.class, result.get(0));
      assertEquals(generatePtpRequest(PORT_C2_2_URI, false), result.get(0));
      assertInstanceOf(CtpRequest.class, result.get(1));
      assertEquals(generateCtpRequest(PORT_C2_2_OPTICAL_URI, false, Set.of(fecParam)), result.get(1));
      assertInstanceOf(PtpParamsRequest.class, result.get(2));
      assertEquals(generatePtpParamsRequest(PORT_C2_2_URI, false, Set.of(ftsCtrlParam)), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      var termParam = new MoCimParameter("", "termination-mode", new CimValue.Enum("nss"));
      assertEquals(generateCtpRequest(PORT_C2_2_CTP_URI, false, Set.of(termParam)), result.get(3));
      assertInstanceOf(CtpRequest.class, result.get(4));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI_FULL, false,
        Set.of(tpParam, tsParam)), result.get(4));
      assertInstanceOf(SncRequest.class, result.get(5));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, generateSncUri(signalType), false,
        Set.of(PORT_C2_2_CTP_URI),
        Set.of(PORT_N_CTP_URI_FULL),
        provisionApi), result.get(5));
    } catch (OtnProvisioningException e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void prepareRequestCtoN_forkPlacementCrossNotExist() {
    init(generatePlugRef_C_ETH25GB());

    when(rrmOtnDbResourcesFacade.findPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C_AID))
    )).thenReturn(
      Optional.of(new PtpRef(NETWORK_ELEMENT_ID, null, null, null))
    );

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      createListOfNetworkPortCtpOtuc2()
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C_OPTICAL_URI_ET25))
    )).thenReturn(
      createClientPortCtp25GBE()
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OPTICAL_URI))
    )).thenReturn(
      createNetworkPortCtpOtuc2()
    );

    when(provisionApi.getAvailableCtpEntity(
      eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
      eq(PORT_N_OPTICAL_URI + "/ctp/oduc2/ctp/oduf-" + 5)
    )).thenReturn(
      Optional.of(5)
    );

    var signalType = Optional.of(LayerQualifier.ODUFLEX);
    var C_PORT_LABEL = new Whole();
    var N_PORT_LABEL = new Complete(5, List.of(1, 2, 3, 4, 5, 6, 7, 8));
    var motrait = LayerQualifierToCimString.convert(LayerQualifier.ODUFLEX) + "/odtu";
    var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(5));
    var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L)));
    var ofcbrcl = new MoCimParameter("oduf", "ofcbrcl", new CimValue.Enum("eth25g"));
    var request = generateCrossConnectRequestWithForkPlacement(inParamsEt25, PORT_C_AID, C_PORT_LABEL, false,
      PORT_N_AID, N_PORT_LABEL, true, signalType, InterfaceType.UNI, InterfaceType.UNSPECIFIED, ForkPlacement.SOURCE);
    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(5, result.size());
      assertInstanceOf(CtpRequest.class, result.get(0));
      assertEquals(generateCtpRequest(PORT_C_OPTICAL_URI_ET25, false, Set.of()), result.get(0));
      assertInstanceOf(PtpParamsRequest.class, result.get(1));
      assertEquals(generatePtpParamsRequest(PORT_C_URI, true, Set.of()), result.get(1));
      assertInstanceOf(CtpRequest.class, result.get(2));
      assertEquals(generateCtpRequest(PORT_C_OPTICAL_URI_ET25 + "/ctp/oduf", false, Set.of()), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_N_OPTICAL_URI + "/ctp/oduc2/ctp/oduf-" + 5, false,
          Set.of(tpParam, tsParam, ofcbrcl)),
        result.get(3));
      assertInstanceOf(SncRequest.class, result.get(4));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, generateSncUri(signalType), false,
        Set.of(PORT_C_OPTICAL_URI_ET25 + "/ctp/oduf"),
        Set.of(PORT_N_OPTICAL_URI + "/ctp/oduc2/ctp/oduf-" + 5),
        provisionApi), result.get(4));
    } catch (Exception e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void prepareRequestCtoN_forkPlacementModifyCross() {
    String clientPtpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,3";
    String clientOTUUri = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,3/ctp/et25";
    String PORT_C_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,3/ctp/et25/ctp/oduf";
    String PORT_N_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/nw,1/ctp/otuc2/ctp/oduc2/ctp/oduf-5";
    String PORT_N2_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/nw,2/ctp/otuc2/ctp/oduc2/ctp/oduf-5";
    String SNC_URI_PREFIX = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/sn/oduf/snc";
    String SNC_URI = SNC_URI_PREFIX + "/1";

    init(generatePlugRef_C());

    when(otnSegmentRepository.findAssociatedSegmentByEndpointAndSncUriPrefix(
      eq(NETWORK_ELEMENT_ID),
      eq(PORT_C_URI),
      eq(SNC_URI_PREFIX)
    )).thenReturn(
      Optional.of(new OtnSegment(
        new SegmentID(NETWORK_ELEMENT_ID, "SegmentId"),
        List.of(
          new CtpCreatedEvent(PORT_C_URI),
          new CtpCreatedEvent(PORT_N2_URI),
          new SncCreatedEvent(SNC_URI, Set.of(PORT_C_URI), Set.of(PORT_N2_URI))
        )
      ))
    );

    when(rrmOtnDbResourcesFacade.findCtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C_URI))
    )).thenReturn(
      Optional.of(new CtpRef(NETWORK_ELEMENT_ID, null, null, null))
    );

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      createListOfNetworkPortCtpOtuc2()
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(clientOTUUri))
    )).thenReturn(
      createClientPortCtp25GBE(clientOTUUri)
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OPTICAL_URI))
    )).thenReturn(
      createNetworkPortCtpOtuc2()
    );

    when(provisionApi.getAvailableCtpEntity(
      eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
      eq(PORT_N_OPTICAL_URI + "/ctp/oduc2/ctp/oduf-" + 5)
    )).thenReturn(
      Optional.of(5)
    );

    when(rrmOtnDbResourcesFacade.findPlugFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C_AID))
    )).thenReturn(Optional.of(
        PlugRef.builder()
          .setNeId(NETWORK_ELEMENT_ID)
          .setAid(PLUG_C_AID)
          .setUri(PLUG_C_URI)
          .setType("SFP28-25GU-LR-SM-LC-TIN")
          .build()
      )
    );

    var signalType = Optional.of(LayerQualifier.ODUFLEX);
    var C_PORT_LABEL = new Whole();
    var N_PORT_LABEL = new Complete(5, List.of(1, 2, 3, 4, 5, 6, 7, 8));
    var motrait = LayerQualifierToCimString.convert(LayerQualifier.ODUFLEX) + "/odtu";
    var ftsCtrlParam = new MoCimParameter("opt", "fts-control", new CimValue.Enum("imdt"));
    var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(5));
    var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L)));
    var ofcbrcl = new MoCimParameter("oduf", "ofcbrcl", new CimValue.Enum("eth25g"));
    var inParams = List.of(
      new OpticalParameters.SelectedParameter(ParameterName.GLQ, new Value.Enum("ETH-25G")),
      new OpticalParameters.SelectedParameter(ParameterName.FTS_CONTROL, new Value.Enum("Laser off immediate"))
    );
    var request = generateCrossConnectRequestWithForkPlacement(inParams, PORT_C_AID, C_PORT_LABEL, false,
      PORT_N_AID, N_PORT_LABEL, true, signalType, InterfaceType.UNI, InterfaceType.UNSPECIFIED, ForkPlacement.SOURCE);
    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(6, result.size());
      assertInstanceOf(PtpRequest.class, result.get(0));
      assertEquals(generatePtpRequest(clientPtpUri, false), result.get(0));

      assertInstanceOf(CtpRequest.class, result.get(1));
      assertEquals(generateCtpRequest(clientOTUUri, false, Set.of()), result.get(1));

      assertInstanceOf(PtpParamsRequest.class, result.get(2));
      assertEquals(generatePtpParamsRequest(clientPtpUri, false, Set.of(ftsCtrlParam)), result.get(2));

      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_C_URI, true, Set.of()), result.get(3));

      assertInstanceOf(CtpRequest.class, result.get(4));
      assertEquals(generateCtpRequest(PORT_N_URI, false,
          Set.of(tpParam, tsParam, ofcbrcl)),
        result.get(4));

      assertInstanceOf(SncModifyRequest.class, result.get(5));
      LinkedHashSet<String> aEndpointURIs = new LinkedHashSet<>();
      LinkedHashSet<String> zEndpointURIs = new LinkedHashSet<>();
      aEndpointURIs.add(PORT_C_URI);
      zEndpointURIs.add(PORT_N_URI);
      zEndpointURIs.add(PORT_N2_URI);
      assertEquals(new SncModifyRequest(NETWORK_ELEMENT_ID, SNC_URI,
        aEndpointURIs,
        zEndpointURIs,
        false,
        provisionApi), result.get(5));
    } catch (Exception e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void prepareRequestCtoN_forkPlacementAdoptProtected() {
    String clientPtpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,3";
    String clientOTUUri = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,3/ctp/et25";
    String PORT_C_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,3/ctp/et25/ctp/oduf";
    String PORT_N_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/nw,1/ctp/otuc2/ctp/oduc2/ctp/oduf-5";
    String PORT_N2_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/nw,2/ctp/otuc2/ctp/oduc2/ctp/oduf-5";
    String SNC_URI_PREFIX = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/sn/oduf/snc";
    String SNC_URI = SNC_URI_PREFIX + "/1";

    init(generatePlugRef_C());

    when(rrmOtnDbResourcesFacade.getExistingProtectedSncUri(
      eq(NETWORK_ELEMENT_ID),
      eq(SNC_URI_PREFIX),
      eq(PORT_C_URI),
      eq(PORT_N_URI)
    )).thenReturn(
      Optional.of(new Uri(SNC_URI))
    );

    when(rrmOtnDbResourcesFacade.findCrossConnect(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(SNC_URI))
    )).thenReturn(
      Optional.of(new CrossConnectRef(1, new Aid(SNC_URI), new Uri(SNC_URI),
        List.of(PORT_C_URI), List.of(PORT_N_URI, PORT_N2_URI), null, null))
    );

    when(rrmOtnDbResourcesFacade.findCtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C_URI))
    )).thenReturn(
      Optional.of(new CtpRef(NETWORK_ELEMENT_ID, null, null, null))
    );

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      createListOfNetworkPortCtpOtuc2()
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(clientOTUUri))
    )).thenReturn(
      createClientPortCtp25GBE(clientOTUUri)
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OPTICAL_URI))
    )).thenReturn(
      createNetworkPortCtpOtuc2()
    );

    when(provisionApi.getAvailableCtpEntity(
      eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
      eq(PORT_N_OPTICAL_URI + "/ctp/oduc2/ctp/oduf-" + 5)
    )).thenReturn(
      Optional.of(5)
    );

    when(rrmOtnDbResourcesFacade.findPlugFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C_AID))
    )).thenReturn(Optional.of(
        PlugRef.builder()
          .setNeId(NETWORK_ELEMENT_ID)
          .setAid(PLUG_C_AID)
          .setUri(PLUG_C_URI)
          .setType("SFP28-25GU-LR-SM-LC-TIN")
          .build()
      )
    );

    var signalType = Optional.of(LayerQualifier.ODUFLEX);
    var C_PORT_LABEL = new Whole();
    var N_PORT_LABEL = new Complete(5, List.of(1, 2, 3, 4, 5, 6, 7, 8));
    var motrait = LayerQualifierToCimString.convert(LayerQualifier.ODUFLEX) + "/odtu";
    var ftsCtrlParam = new MoCimParameter("opt", "fts-control", new CimValue.Enum("imdt"));
    var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(5));
    var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L)));
    var ofcbrcl = new MoCimParameter("oduf", "ofcbrcl", new CimValue.Enum("eth25g"));
    var inParams = List.of(
      new OpticalParameters.SelectedParameter(ParameterName.GLQ, new Value.Enum("ETH-25G")),
      new OpticalParameters.SelectedParameter(ParameterName.FTS_CONTROL, new Value.Enum("Laser off immediate"))
    );
    var request = generateCrossConnectRequestWithForkPlacement(inParams, PORT_C_AID, C_PORT_LABEL, false,
      PORT_N_AID, N_PORT_LABEL, true, signalType, InterfaceType.UNI, InterfaceType.UNSPECIFIED, ForkPlacement.SOURCE);
    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(6, result.size());
      assertInstanceOf(PtpRequest.class, result.get(0));
      assertEquals(generatePtpRequest(clientPtpUri, false), result.get(0));

      assertInstanceOf(CtpRequest.class, result.get(1));
      assertEquals(generateCtpRequest(clientOTUUri, false, Set.of()), result.get(1));

      assertInstanceOf(PtpParamsRequest.class, result.get(2));
      assertEquals(generatePtpParamsRequest(clientPtpUri, false, Set.of(ftsCtrlParam)), result.get(2));

      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_C_URI, true, Set.of()), result.get(3));

      assertInstanceOf(CtpRequest.class, result.get(4));
      assertEquals(generateCtpRequest(PORT_N_URI, false,
          Set.of(tpParam, tsParam, ofcbrcl)),
        result.get(4));

      assertInstanceOf(SncRequest.class, result.get(5));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, SNC_URI, true,
        Set.of(PORT_C_URI),
        Set.of(PORT_N_URI, PORT_N2_URI),
        provisionApi), result.get(5));
    } catch (Exception e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void prepareRequestNtoC_forkPlacementAdoptProtected() {
    String clientPtpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,3";
    String clientOTUUri = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,3/ctp/et25";
    String PORT_C_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,3/ctp/et25/ctp/oduf";
    String PORT_N_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/nw,1/ctp/otuc2/ctp/oduc2/ctp/oduf-5";
    String PORT_N2_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/nw,2/ctp/otuc2/ctp/oduc2/ctp/oduf-5";
    String SNC_URI_PREFIX = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/sn/oduf/snc";
    String SNC_URI = SNC_URI_PREFIX + "/1";

    init(generatePlugRef_C());

    when(rrmOtnDbResourcesFacade.getExistingProtectedSncUri(
      eq(NETWORK_ELEMENT_ID),
      eq(SNC_URI_PREFIX),
      eq(PORT_C_URI),
      eq(PORT_N_URI)
    )).thenReturn(
      Optional.of(new Uri(SNC_URI))
    );

    when(rrmOtnDbResourcesFacade.findCrossConnect(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(SNC_URI))
    )).thenReturn(
      Optional.of(new CrossConnectRef(1, new Aid(SNC_URI), new Uri(SNC_URI),
        List.of(PORT_C_URI), List.of(PORT_N_URI, PORT_N2_URI), null, null))
    );

    when(rrmOtnDbResourcesFacade.findCtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C_URI))
    )).thenReturn(
      Optional.of(new CtpRef(NETWORK_ELEMENT_ID, null, null, null))
    );

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      createListOfNetworkPortCtpOtuc2()
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(clientOTUUri))
    )).thenReturn(
      createClientPortCtp25GBE(clientOTUUri)
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OPTICAL_URI))
    )).thenReturn(
      createNetworkPortCtpOtuc2()
    );

    when(provisionApi.getAvailableCtpEntity(
      eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
      eq(PORT_N_OPTICAL_URI + "/ctp/oduc2/ctp/oduf-" + 5)
    )).thenReturn(
      Optional.of(5)
    );

    when(rrmOtnDbResourcesFacade.findPlugFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C_AID))
    )).thenReturn(Optional.of(
        PlugRef.builder()
          .setNeId(NETWORK_ELEMENT_ID)
          .setAid(PLUG_C_AID)
          .setUri(PLUG_C_URI)
          .setType("SFP28-25GU-LR-SM-LC-TIN")
          .build()
      )
    );

    var signalType = Optional.of(LayerQualifier.ODUFLEX);
    var C_PORT_LABEL = new Whole();
    var N_PORT_LABEL = new Complete(5, List.of(1, 2, 3, 4, 5, 6, 7, 8));
    var motrait = LayerQualifierToCimString.convert(LayerQualifier.ODUFLEX) + "/odtu";
    var ftsCtrlParam = new MoCimParameter("opt", "fts-control", new CimValue.Enum("imdt"));
    var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(5));
    var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L)));
    var ofcbrcl = new MoCimParameter("oduf", "ofcbrcl", new CimValue.Enum("eth25g"));
    var inParams = List.of(
      new OpticalParameters.SelectedParameter(ParameterName.GLQ, new Value.Enum("ETH-25G")),
      new OpticalParameters.SelectedParameter(ParameterName.FTS_CONTROL, new Value.Enum("Laser off immediate"))
    );
    var request = generateCrossConnectRequestWithForkPlacement(inParams, PORT_N_AID, N_PORT_LABEL, true,
      PORT_C_AID, C_PORT_LABEL, false, signalType, InterfaceType.UNSPECIFIED, InterfaceType.UNI, ForkPlacement.DESTINATION);
    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(6, result.size());
      assertInstanceOf(PtpRequest.class, result.get(0));
      assertEquals(generatePtpRequest(clientPtpUri, false), result.get(0));

      assertInstanceOf(CtpRequest.class, result.get(1));
      assertEquals(generateCtpRequest(clientOTUUri, false, Set.of()), result.get(1));

      assertInstanceOf(PtpParamsRequest.class, result.get(2));
      assertEquals(generatePtpParamsRequest(clientPtpUri, false, Set.of(ftsCtrlParam)), result.get(2));

      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_C_URI, true, Set.of()), result.get(3));

      assertInstanceOf(CtpRequest.class, result.get(4));
      assertEquals(generateCtpRequest(PORT_N_URI, false,
          Set.of(tpParam, tsParam, ofcbrcl)),
        result.get(4));

      assertInstanceOf(SncRequest.class, result.get(5));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, SNC_URI, true,
        Set.of(PORT_C_URI),
        Set.of(PORT_N_URI, PORT_N2_URI),
        provisionApi), result.get(5));
    } catch (Exception e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void prepareRequestCtoN_odufOverOT100() {
    final var signalType = Optional.of(LayerQualifier.ODUFLEX);
    final String PLUG_C2_TYPE = "QSFP28-112G-AOC-0500";
    final String PORT_C2_2_OPTICAL_URI = PORT_C2_2_URI + "/ctp/et25";
    final String PORT_C2_2_CTP_URI = PORT_C2_2_OPTICAL_URI + "/ctp/" + LayerQualifierToCimString.convert(signalType.get());
    final String PORT_N_OT100_OPTICAL_URI = PORT_N_URI + "/ctp/ot100";
    final String PORT_N_CTP_URI = PORT_N_OT100_OPTICAL_URI + "/ctp/odu4/ctp/" + LayerQualifierToCimString.convert(signalType.get()) + "-";
    final String PORT_N_CTP_URI_FULL = PORT_N_CTP_URI + PORT_N_CTP_ID;
    final PlugRef plugRef = PlugRef.builder()
      .setNeId(NETWORK_ELEMENT_ID)
      .setAid(PLUG_C2_2_AID)
      .setUri(PLUG_C2_2_URI)
      .setType(PLUG_C2_TYPE)
      .build();
    final var opticalParams = List.of(
      new OpticalParameters.SelectedParameter(ParameterName.GLQ, new Value.Enum("ETH-25G"))
    );

    init(generatePlugRef_C());

    when(rrmOtnDbResourcesFacade.findPlug(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PLUG_N_AID))
    )).thenReturn(
      Optional.of(generatePlugRef_N_244())
    );

    when(rrmOtnDbResourcesFacade.findPlugFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      Optional.of(generatePlugRef_N_244())
    );

    when(rrmOtnDbResourcesFacade.findCardFromPlug(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PLUG_C2_2_AID))
    )).thenReturn(
      generateCardRef()
    );

    when(rrmOtnDbResourcesFacade.findPlug(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PLUG_C2_2_AID))
    )).thenReturn(
      Optional.of(plugRef)
    );

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      createListOfNetworkPortCtpOtu4(PORT_N_OT100_OPTICAL_URI)
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C2_2_OPTICAL_URI))
    )).thenReturn(
      createClientPortCtp25GBE(PORT_C2_2_OPTICAL_URI)
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OT100_OPTICAL_URI))
    )).thenReturn(
      createNetworkPortCtpOtu4(PORT_N_OT100_OPTICAL_URI)
    );

    when(provisionApi.getAvailableCtpEntity(
      eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
      eq(PORT_N_CTP_URI + PORT_N_CTP_ID)
    )).thenReturn(
      Optional.of(PORT_N_CTP_ID)
    );

    var C_PORT_LABEL = new Whole();
    var N_PORT_LABEL = new Complete(2, List.of(1, 2, 3, 4, 5, 6, 7, 8));
    var motrait = LayerQualifierToCimString.convert(signalType.get()) + "/odtu";
    var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(2));
    var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L)));
    var ofcbrcl = new MoCimParameter("oduf", "ofcbrcl", new CimValue.Enum("eth25g"));

    var request = generateCrossConnectRequest(opticalParams, PORT_C2_2_AID, C_PORT_LABEL, false,
      PORT_N_AID, N_PORT_LABEL, true, signalType, InterfaceType.UNI, InterfaceType.UNSPECIFIED);

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(6, result.size());
      assertInstanceOf(PtpRequest.class, result.get(0));
      assertEquals(generatePtpRequest(PORT_C2_2_URI, false), result.get(0));
      assertInstanceOf(CtpRequest.class, result.get(1));
      assertEquals(generateCtpRequest(PORT_C2_2_OPTICAL_URI, false, Set.of()), result.get(1));
      assertInstanceOf(PtpParamsRequest.class, result.get(2));
      assertEquals(generatePtpParamsRequest(PORT_C2_2_URI, false, Set.of()), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_C2_2_CTP_URI, false, Set.of()), result.get(3));
      assertInstanceOf(CtpRequest.class, result.get(4));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI_FULL, false,
        Set.of(tpParam, tsParam, ofcbrcl)), result.get(4));
      assertInstanceOf(SncRequest.class, result.get(5));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, generateSncUri(signalType), false,
        Set.of(PORT_C2_2_CTP_URI),
        Set.of(PORT_N_CTP_URI_FULL),
        provisionApi), result.get(5));
    } catch (OtnProvisioningException e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void prepareRequestCtoN_odufOverOT200() {
    final var parentSignalType = Optional.of(LayerQualifier.ODU4);
    final var signalType = Optional.of(LayerQualifier.ODUFLEX);
    final String PORT_C2_2_OPTICAL_URI = PORT_C2_2_URI + "/ctp/et25";
    final String PORT_C2_2_CTP_URI = PORT_C2_2_OPTICAL_URI + "/ctp/" + LayerQualifierToCimString.convert(signalType.get());
    final String PORT_N_OT200_OPTICAL_URI = PORT_N_URI + "/ctp/ot200";
    final String PORT_N_CTP_URI = PORT_N_OT200_OPTICAL_URI + "/ctp/odu4-1/ctp/" + LayerQualifierToCimString.convert(signalType.get()) + "-";
    final String PORT_N_CTP_URI_FULL = PORT_N_CTP_URI + PORT_N_CTP_ID;
    final String PORT_N_PARENT_CTP_URI = PORT_N_OT200_OPTICAL_URI + "/ctp/" + LayerQualifierToCimString.convert(parentSignalType.get()) + "-1";
    final var opticalParams = List.of(
      new OpticalParameters.SelectedParameter(ParameterName.GLQ, new Value.Enum("ETH-25G")),
      new OpticalParameters.SelectedParameter(ParameterName.FTS_CONTROL, new Value.Enum("Laser off immediate"))
    );

    init(generatePlugRef_C());

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      createListOfNetworkPortCtpOtu4(PORT_N_OT200_OPTICAL_URI)
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C2_2_OPTICAL_URI))
    )).thenReturn(
      createClientPortCtp25GBE(PORT_C2_2_OPTICAL_URI)
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OT200_OPTICAL_URI))
    )).thenReturn(
      createNetworkPortCtpOtuc2PA(PORT_N_OT200_OPTICAL_URI)
    );

    when(rrmOtnDbResourcesFacade.findCardFromCtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OT200_OPTICAL_URI))
    )).thenReturn(
      Optional.of(new CardRef(NETWORK_ELEMENT_ID, new Aid(CARD_AID), null, null, 0))
    );

    when(rrmOtnDbResourcesFacade.listCtpsOnCard(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(CARD_AID))
    )).thenReturn(
      List.of(
        createCtp(NETWORK_ELEMENT_ID, new Aid(OTU2E_CTP_AID), new Uri(PORT_C2_2_OPTICAL_URI), OTU2E_LAYERS),
        createCtp(NETWORK_ELEMENT_ID, new Aid(ODU2E_CTP_AID), new Uri(PORT_C2_2_CTP_URI), ODU2E_LAYERS),
        createCtp(NETWORK_ELEMENT_ID, new Aid(OT200_CTP_AID), new Uri(PORT_N_OT200_OPTICAL_URI), OTUC2PA_LAYERS),
        createCtp(NETWORK_ELEMENT_ID, new Aid(ODU4_CTP_AID), new Uri(PORT_N_PARENT_CTP_URI), ODU4_LAYERS)
      )
    );

    when(provisionApi.getAvailableCtpEntity(
      eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
      eq(PORT_N_CTP_URI + PORT_N_CTP_ID)
    )).thenReturn(
      Optional.of(PORT_N_CTP_ID)
    );

    var C_PORT_LABEL = new Whole();
    var N_PORT_PARENT_LABEL = new Container(1);
    var N_PORT_CHILD_LABEL = new Complete(2, List.of(1, 2, 3, 4, 5, 6, 7, 8));
    var N_PORT_LABEL = new StackedLabel(
      new LabelDTO(new MultiLabel(List.of(new OtnLabel(N_PORT_CHILD_LABEL)))),
      new LabelDTO(new MultiLabel(List.of(new OtnLabel(N_PORT_PARENT_LABEL))))
    );
    var motrait = LayerQualifierToCimString.convert(signalType.get()) + "/odtu";
    var ftsCtrlParam = new MoCimParameter("opt", "fts-control", new CimValue.Enum("imdt"));
    var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(2));
    var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L)));
    var ofcbrcl = new MoCimParameter("oduf", "ofcbrcl", new CimValue.Enum("eth25g"));

    var request = generateCrossConnectRequestStackedLabel(opticalParams, PORT_C2_2_AID, C_PORT_LABEL, false,
      PORT_N_AID, N_PORT_LABEL, true, signalType, InterfaceType.UNI);

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(6, result.size());
      assertInstanceOf(PtpRequest.class, result.get(0));
      assertEquals(generatePtpRequest(PORT_C2_2_URI, false), result.get(0));
      assertInstanceOf(CtpRequest.class, result.get(1));
      assertEquals(generateCtpRequest(PORT_C2_2_OPTICAL_URI, false, Set.of()), result.get(1));
      assertInstanceOf(PtpParamsRequest.class, result.get(2));
      assertEquals(generatePtpParamsRequest(PORT_C2_2_URI, false, Set.of(ftsCtrlParam)), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_C2_2_CTP_URI, false, Set.of()), result.get(3));
      assertInstanceOf(CtpRequest.class, result.get(4));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI_FULL, false,
        Set.of(tpParam, tsParam, ofcbrcl)), result.get(4));
      assertInstanceOf(SncRequest.class, result.get(5));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, generateSncUri(signalType), false,
        Set.of(PORT_C2_2_CTP_URI),
        Set.of(PORT_N_CTP_URI_FULL),
        provisionApi), result.get(5));
    } catch (OtnProvisioningException e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void prepareRequestCtoN_FNMD_102010() {
    final var signalType = Optional.of(LayerQualifier.ODU2E);
    final String PORT_C2_2_OPTICAL_URI = PORT_C2_2_URI + "/ctp/otu2e";
    final String PORT_C2_2_CTP_URI = PORT_C2_2_OPTICAL_URI + "/ctp/" + LayerQualifierToCimString.convert(signalType.get());
    final String PORT_N_OT100_OPTICAL_URI = PORT_N_URI + "/ctp/ot100";
    final String PORT_N_CTP_URI = PORT_N_OT100_OPTICAL_URI + "/ctp/odu4/ctp/" + LayerQualifierToCimString.convert(signalType.get()) + "-";
    final String PORT_N_CTP_URI_FULL = PORT_N_CTP_URI + PORT_N_CTP_ID;
    final var opticalParams = List.of(new OpticalParameters.SelectedParameter(ParameterName.FEC, new Value.Enum("GFEC")));

    init(generatePlugRef_C());

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      createListOfNetworkPortCtpOtu4(PORT_N_OT100_OPTICAL_URI)
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C2_2_OPTICAL_URI))
    )).thenReturn(
      createClientPortCtpOtu2E(PORT_C2_2_OPTICAL_URI)
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OT100_OPTICAL_URI))
    )).thenReturn(
      createNetworkPortCtpOtu4(PORT_N_OT100_OPTICAL_URI)
    );

    when(provisionApi.getAvailableCtpEntity(
      eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
      eq(PORT_N_CTP_URI + PORT_N_CTP_ID)
    )).thenReturn(
      Optional.of(PORT_N_CTP_ID)
    );

    var C_PORT_LABEL = new Whole();
    var N_PORT_LABEL = new Complete(2, List.of(1, 2, 3, 4, 5, 6, 7, 8));
    var motrait = LayerQualifierToCimString.convert(signalType.get()) + "/odtu";
    var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(2));
    var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L)));
    var fecParam = new MoCimParameter("otu2e", "fec-type", new CimValue.Enum("gfec"));

    var request = generateCrossConnectRequest(opticalParams, PORT_C2_2_AID, C_PORT_LABEL, false,
      PORT_N_AID, N_PORT_LABEL, true, signalType, InterfaceType.ENNI, InterfaceType.UNSPECIFIED);

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(6, result.size());
      assertInstanceOf(PtpRequest.class, result.get(0));
      assertEquals(generatePtpRequest(PORT_C2_2_URI, false), result.get(0));
      assertInstanceOf(CtpRequest.class, result.get(1));
      assertEquals(generateCtpRequest(PORT_C2_2_OPTICAL_URI, false, Set.of(fecParam)), result.get(1));
      assertInstanceOf(PtpParamsRequest.class, result.get(2));
      assertEquals(generatePtpParamsRequest(PORT_C2_2_URI, false, Set.of()), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_C2_2_CTP_URI, false, Set.of(new MoCimParameter("", "termination-mode", new CimValue.Enum("nss")))), result.get(3));
      assertInstanceOf(CtpRequest.class, result.get(4));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI_FULL, false,
        Set.of(tpParam, tsParam)), result.get(4));
      assertInstanceOf(SncRequest.class, result.get(5));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, generateSncUri(signalType), false,
        Set.of(PORT_C2_2_CTP_URI),
        Set.of(PORT_N_CTP_URI_FULL),
        provisionApi), result.get(5));
    } catch (OtnProvisioningException e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void prepareRequestCtoN_FNMD_102010_ODU4_WholeLabel() {
    final var signalType = Optional.of(LayerQualifier.ODU4);
    final String PLUG_C10_TYPE = "QSFP28-112G-AOC-0500";
    final String PORT_C10_OPTICAL_URI = PORT_C10_URI + "/ctp/otu4";
    final String PORT_C10_CTP_URI = PORT_C10_OPTICAL_URI + "/ctp/" + LayerQualifierToCimString.convert(signalType.get());
    final String PORT_N_OT100_OPTICAL_URI = PORT_N_URI + "/ctp/ot100";
    final String PORT_N_CTP_URI = PORT_N_OT100_OPTICAL_URI + "/ctp/" + LayerQualifierToCimString.convert(signalType.get());

    init(generatePlugRef_C(PLUG_C10_AID, PLUG_C10_URI, PLUG_C10_TYPE));

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      createListOfNetworkPortCtpOtu4(PORT_N_OT100_OPTICAL_URI)
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C10_OPTICAL_URI))
    )).thenReturn(
      createNetworkPortCtpOtu4(PORT_C10_OPTICAL_URI)
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OT100_OPTICAL_URI))
    )).thenReturn(
      createNetworkPortCtpOtu4(PORT_N_OT100_OPTICAL_URI)
    );

    var C_PORT_LABEL = new Whole();
    var N_PORT_LABEL = new Whole();

    var termParam = new MoCimParameter("", "termination-mode", new CimValue.Enum("nss"));

    var request = generateCrossConnectRequest(List.of(), PORT_C10_AID, C_PORT_LABEL, false, PORT_N_AID, N_PORT_LABEL, false, signalType, InterfaceType.ENNI, InterfaceType.UNSPECIFIED);

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(6, result.size());
      assertInstanceOf(PtpRequest.class, result.get(0));
      assertEquals(generatePtpRequest(PORT_C10_URI, false), result.get(0));
      assertInstanceOf(CtpRequest.class, result.get(1));
      assertEquals(generateCtpRequest(PORT_C10_OPTICAL_URI, false, Set.of()), result.get(1));
      assertInstanceOf(PtpParamsRequest.class, result.get(2));
      assertEquals(generatePtpParamsRequest(PORT_C10_URI, false, Set.of()), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_C10_CTP_URI, false, Set.of(termParam)), result.get(3));
      assertInstanceOf(CtpRequest.class, result.get(4));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI, false, Set.of(termParam)), result.get(4));
      assertInstanceOf(SncRequest.class, result.get(5));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, generateSncUri(signalType), false,
        Set.of(PORT_C10_CTP_URI),
        Set.of(PORT_N_CTP_URI),
        provisionApi), result.get(5));
    } catch (OtnProvisioningException e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void prepareRequestCtoN_StackedLabel_ParentContainerLabel_ChildCompleteLabel() {
    final var parentSignalType = Optional.of(LayerQualifier.ODU4);
    final var signalType = Optional.of(LayerQualifier.ODU2E);
    final String PLUG_C2_TYPE = "QSFP28-112G-AOC-0500";
    final String PORT_C2_2_OPTICAL_URI = PORT_C2_2_URI + "/ctp/otu2e";
    final String PORT_C2_2_CTP_URI = PORT_C2_2_OPTICAL_URI + "/ctp/" + LayerQualifierToCimString.convert(signalType.get());
    final String PORT_N_OT200_OPTICAL_URI = PORT_N_URI + "/ctp/ot200";
    final String PORT_N_PARENT_CTP_URI = PORT_N_OT200_OPTICAL_URI + "/ctp/" + LayerQualifierToCimString.convert(parentSignalType.get()) + "-1";
    final String PORT_N_CTP_URI = PORT_N_PARENT_CTP_URI + "/ctp/" + LayerQualifierToCimString.convert(signalType.get()) + "-";
    final String PORT_N_CTP_URI_FULL = PORT_N_CTP_URI + PORT_N_CTP_ID;
    final String PORT_N_CTP_URI_SECOND_RESOURCE = PORT_N_CTP_URI + "2";
    final var opticalParams = List.of(
      new OpticalParameters.SelectedParameter(ParameterName.FEC, new Value.Enum("GFEC")),
      new OpticalParameters.SelectedParameter(ParameterName.FTS_CONTROL, new Value.Enum("Laser off immediate"))
    );

    init(generatePlugRef_C(PLUG_C2_2_AID, PLUG_C2_2_URI, PLUG_C2_TYPE));

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      createListOfNetworkPortCtpOtu4(PORT_N_OT200_OPTICAL_URI)
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C2_2_OPTICAL_URI))
    )).thenReturn(
      createClientPortCtpOtu2E(PORT_C2_2_OPTICAL_URI)
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OT200_OPTICAL_URI))
    )).thenReturn(
      createNetworkPortCtpOtuc2PA(PORT_N_OT200_OPTICAL_URI)
    );

    when(rrmOtnDbResourcesFacade.findCardFromCtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OT200_OPTICAL_URI))
    )).thenReturn(
      Optional.of(new CardRef(NETWORK_ELEMENT_ID, new Aid(CARD_AID), null, null, 0))
    );

    when(rrmOtnDbResourcesFacade.listCtpsOnCard(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(CARD_AID))
    )).thenReturn(
      List.of(
        createCtp(NETWORK_ELEMENT_ID, new Aid(OTU2E_CTP_AID), new Uri(PORT_C2_2_OPTICAL_URI), OTU2E_LAYERS),
        createCtp(NETWORK_ELEMENT_ID, new Aid(ODU2E_CTP_AID), new Uri(PORT_C2_2_CTP_URI), ODU2E_LAYERS),
        createCtp(NETWORK_ELEMENT_ID, new Aid(OT200_CTP_AID), new Uri(PORT_N_OT200_OPTICAL_URI), OTUC2PA_LAYERS),
        createCtp(NETWORK_ELEMENT_ID, new Aid(ODU2_CTP_AID), new Uri(PORT_N_CTP_URI_SECOND_RESOURCE), ODU2_LAYERS),
        createCtp(NETWORK_ELEMENT_ID, new Aid(ODU4_CTP_AID), new Uri(PORT_N_PARENT_CTP_URI), ODU4_LAYERS)
      )
    );

    when(provisionApi.getAvailableCtpEntity(
      eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
      eq(PORT_N_CTP_URI + PORT_N_CTP_ID)
    )).thenReturn(
      Optional.of(PORT_N_CTP_ID)
    );

    var C_PORT_LABEL = new Whole();
    var N_PORT_PARENT_LABEL = new Container(1);
    var N_PORT_CHILD_LABEL = new Complete(2, List.of(1, 2, 3, 4, 5, 6, 7, 8));
    var N_PORT_LABEL = new StackedLabel(
      new LabelDTO(new MultiLabel(List.of(new OtnLabel(N_PORT_CHILD_LABEL)))),
      new LabelDTO(new MultiLabel(List.of(new OtnLabel(N_PORT_PARENT_LABEL))))
    );
    var motrait = LayerQualifierToCimString.convert(signalType.get()) + "/odtu";
    var ftsCtrlParam = new MoCimParameter("opt", "fts-control", new CimValue.Enum("imdt"));
    var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(2));
    var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L)));
    var fecParam = new MoCimParameter("otu2e", "fec-type", new CimValue.Enum("gfec"));
    var termParam = new MoCimParameter("", "termination-mode", new CimValue.Enum("nss"));

    var request = generateCrossConnectRequestStackedLabel(opticalParams, PORT_C2_2_AID, C_PORT_LABEL, false,
      PORT_N_AID, N_PORT_LABEL, true, signalType, InterfaceType.ENNI);

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(6, result.size());
      assertInstanceOf(PtpRequest.class, result.get(0));
      assertEquals(generatePtpRequest(PORT_C2_2_URI, false), result.get(0));
      assertInstanceOf(CtpRequest.class, result.get(1));
      assertEquals(generateCtpRequest(PORT_C2_2_OPTICAL_URI, false, Set.of(fecParam)), result.get(1));
      assertInstanceOf(PtpParamsRequest.class, result.get(2));
      assertEquals(generatePtpParamsRequest(PORT_C2_2_URI, false, Set.of(ftsCtrlParam)), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_C2_2_CTP_URI, false, Set.of(termParam)), result.get(3));
      assertInstanceOf(CtpRequest.class, result.get(4));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI_FULL, false,
        Set.of(tpParam, tsParam)), result.get(4));
      assertInstanceOf(SncRequest.class, result.get(5));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, generateSncUri(signalType), false,
        Set.of(PORT_C2_2_CTP_URI),
        Set.of(PORT_N_CTP_URI_FULL),
        provisionApi), result.get(5));
    } catch (OtnProvisioningException e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void testOneTerminationPointOnlyRequestClientSideMultiplexing() {
    final var signalType = Optional.of(LayerQualifier.ODU2);
    final String PLUG_C1_TYPE = "QSFP28-112G-AOC-0500";
    final String PLUG_C1_AID = "Plug-1/1/c1";
    final String PLUG_C1_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/plgh,1";

    final String PORT_C1_AID = "Port-1/1/c1-1";
    final String PORT_C1_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,1-1";
    final String PORT_C1_OPTICAL_URI = PORT_C1_URI + "/ctp/otu2";
    final String PORT_C1_CTP_URI = PORT_C1_OPTICAL_URI + "/ctp/" + LayerQualifierToCimString.convert(signalType.get());

    init(generatePlugRef_C(PLUG_C1_AID, PLUG_C1_URI, PLUG_C1_TYPE));

    when(rrmOtnDbResourcesFacade.findPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C1_AID))
    )).thenReturn(
      Optional.of(new PtpRef(NETWORK_ELEMENT_ID, new Aid(PORT_C1_AID), new Uri(PORT_C1_URI), null)
    ));

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C1_AID))
    )).thenReturn(
      createListOfClientPortCtpOtu2(PORT_C1_OPTICAL_URI)
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C1_OPTICAL_URI))
    )).thenReturn(
      createClientPortCtpOtu2(PORT_C1_OPTICAL_URI)
    );

    var C_PORT_LABEL = new Whole();

    var request = generateTerminationPointRequest(List.of(), PORT_C1_AID, C_PORT_LABEL, true, layer);

    var termParam = new MoCimParameter("", "termination-mode", new CimValue.Enum("tmsn"));

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(1, result.size());
      assertInstanceOf(CtpRequest.class, result.get(0));
      assertEquals(generateCtpRequest(PORT_C1_CTP_URI, false, Set.of(termParam)), result.get(0));
    } catch (OtnProvisioningException e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void testCrossConnectWithMultiplexingOnBothEnds() {
    final var signalType = Optional.of(LayerQualifier.ODU0);
    final var parentSignalType = Optional.of(LayerQualifier.ODU2);

    final String PLUG_C1_TYPE = "QSFP28-112G-AOC-0500";
    final String PLUG_C1_AID = "Plug-1/1/c1";
    final String PLUG_C1_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/plgh,1";

    final String PORT_C1_AID = "Port-1/1/c1-1";
    final String PORT_C1_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,1-1";
    final String PORT_C1_OPTICAL_URI = PORT_C1_URI + "/ctp/otu2";
    final String PORT_C1_PARENT_CTP_URI = PORT_C1_OPTICAL_URI + "/ctp/" + LayerQualifierToCimString.convert(parentSignalType.get());
    final String PORT_C1_CTP_URI = PORT_C1_PARENT_CTP_URI + "/ctp/" + LayerQualifierToCimString.convert(signalType.get()) + "-";
    final String PORT_C1_CTP_URI_FULL = PORT_C1_CTP_URI + 1;

    final String PORT_N_AID = "Port-1/1/n2";
    final String PORT_N_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/nw,2";
    final String PORT_N_OTUC2_OPTICAL_URI = PORT_N_URI + "/ctp/otuc2";
    final String PORT_N_INTERMEDIATE_LAYER_URI = PORT_N_OTUC2_OPTICAL_URI + "/ctp/oduc2";
    final String PORT_N_PARENT_CTP_URI_PREFIX = PORT_N_INTERMEDIATE_LAYER_URI + "/ctp/" + LayerQualifierToCimString.convert(parentSignalType.get()) + "-";
    final String PORT_N_PARENT_CTP_URI = PORT_N_PARENT_CTP_URI_PREFIX + "1";
    final String PORT_N_PARENT_CTP_AID = "ODU2-1/1/n2/otuc2/oduc2/odu2-1";
    final String PORT_N_CTP_URI = PORT_N_PARENT_CTP_URI + "/ctp/" + LayerQualifierToCimString.convert(signalType.get()) + "-";
    final String PORT_N_CTP_URI_FULL = PORT_N_CTP_URI + 1;

    init(generatePlugRef_C(PLUG_C1_AID, PLUG_C1_URI, PLUG_C1_TYPE));

    when(rrmOtnDbResourcesFacade.findPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C1_AID))
    )).thenReturn(
      Optional.of(new PtpRef(NETWORK_ELEMENT_ID, new Aid(PORT_C1_AID), new Uri(PORT_C1_URI), null)
      ));

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C1_AID))
    )).thenReturn(
      List.of(createCtp(NETWORK_ELEMENT_ID, new Aid(OTU2_CTP_AID), new Uri(PORT_C1_OPTICAL_URI), OTU2_LAYERS)
    ));

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C1_OPTICAL_URI))
    )).thenReturn(
      Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(OTU2_CTP_AID), new Uri(PORT_C1_OPTICAL_URI), OTU2_LAYERS))
    );

    when(rrmOtnDbResourcesFacade.findCtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C1_CTP_URI + 1))
    )).thenReturn(
      Optional.empty()
    );

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      List.of(createCtp(NETWORK_ELEMENT_ID, new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OTUC2_OPTICAL_URI), OTUC2_LAYERS))
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OTUC2_OPTICAL_URI))
    )).thenReturn(
      Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OTUC2_OPTICAL_URI), OTUC2_LAYERS))
    );

    when(provisionApi.getAvailableCtpEntity(
      eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
      eq(PORT_C1_CTP_URI + 1)
    )).thenReturn(
      Optional.of(1)
    );

    when(rrmOtnDbResourcesFacade.findAllCtpsForParentUri(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OTUC2_OPTICAL_URI))
    )).thenReturn(
      List.of(createCtpRef(PORT_N_INTERMEDIATE_LAYER_URI))
    );

    when(rrmOtnDbResourcesFacade.findCtpUriByUriPrefixAndParams(
      eq(NETWORK_ELEMENT_ID),
      eq(PORT_N_INTERMEDIATE_LAYER_URI),
      eq(1),
      eq(List.of(1L, 2L))
    )).thenReturn(Optional.of(new Uri(PORT_N_PARENT_CTP_URI)));

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_PARENT_CTP_URI))
    )).thenReturn(
      Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(PORT_N_PARENT_CTP_AID), new Uri(PORT_N_PARENT_CTP_URI), ODU2_LAYERS))
    );

    when(rrmOtnDbResourcesFacade.findCtpUriByUriPrefixAndParams(
      eq(NETWORK_ELEMENT_ID),
      eq(PORT_N_PARENT_CTP_URI_PREFIX),
      eq(1),
      eq(List.of(1L, 2L))
    )).thenReturn(Optional.of(new Uri(PORT_N_PARENT_CTP_URI)));

    when(provisionApi.getAvailableCtpEntity(
      eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
      eq(PORT_N_CTP_URI_FULL)
    )).thenReturn(
      Optional.of(1)
    );

    var C_PORT_LABEL = new Complete(1, List.of(1));
    var N_PORT_PARENT_LABEL = new Complete(1, List.of(1, 2));
    var N_PORT_CHILD_LABEL = new Complete(1, List.of(1));
    var N_PORT_LABEL = new StackedLabel(
      new LabelDTO(new MultiLabel(List.of(new OtnLabel(N_PORT_CHILD_LABEL)))),
      new LabelDTO(new MultiLabel(List.of(new OtnLabel(N_PORT_PARENT_LABEL))))
    );

    // srcInterfaceType is set to UNSPECIFIED in this case as multiplexing on client side is supported only for transit node
    var request = generateCrossConnectRequestStackedLabel(List.of(), PORT_C1_AID, C_PORT_LABEL, false, PORT_N_AID, N_PORT_LABEL, false, signalType, InterfaceType.UNSPECIFIED);

    var motrait = LayerQualifierToCimString.convert(signalType.get()) + "/odtu";
    var tpParam = new MoCimParameter(motrait, "tp", new CimValue.Int(1));
    var tsParam = new MoCimParameter(motrait, "ts", new CimValue.IntList(List.of(1L)));

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(3, result.size());
      assertInstanceOf(CtpRequest.class, result.get(0));
      assertEquals(generateCtpRequest(PORT_C1_CTP_URI_FULL, false, Set.of(
        tpParam, tsParam)), result.get(0));
      assertInstanceOf(CtpRequest.class, result.get(1));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI_FULL, false, Set.of(
        tpParam, tsParam)), result.get(1));
      assertInstanceOf(SncRequest.class, result.get(2));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, generateSncUri(signalType), false,
        Set.of(PORT_C1_CTP_URI_FULL),
        Set.of(PORT_N_CTP_URI_FULL),
        provisionApi), result.get(2));
    } catch (OtnProvisioningException e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void prepareRequestCtoN_odu4OverOT200WithEpteRequests() {
    final var signalType = Optional.of(LayerQualifier.ODU4);
    final String CLIENT_PTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,1";
    final String PORT_C1_OPTICAL_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,1/ctp/otu4";
    final String PORT_C1_CTP_URI = PORT_C1_OPTICAL_URI + "/ctp/" + LayerQualifierToCimString.convert(signalType.get());
    final String PORT_N_OT200_OPTICAL_URI = PORT_N_URI + "/ctp/ot200";
    final String PORT_N_CTP_URI = PORT_N_OT200_OPTICAL_URI + "/ctp/odu4-1";
    final String PROTECTION_GROUP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,3/eq/card/prtgrp/traffic-1";
    final var opticalParams = List.of(
      new OpticalParameters.SelectedParameter(ParameterName.FTS_CONTROL, new Value.Enum("AIS")),
      new OpticalParameters.SelectedParameter(ParameterName.FEC, new Value.Enum("None"))
    );

    init(generatePlugRef_C("Plug-1/1/c1", "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/plgh,1", "QSFP28-112G-LR4-SM-LC"));

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      createListOfNetworkPortCtpOtu4(PORT_N_OT200_OPTICAL_URI)
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C1_OPTICAL_URI))
    )).thenReturn(
      createClientPortCtpOtu4(PORT_C1_OPTICAL_URI)
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OT200_OPTICAL_URI))
    )).thenReturn(
      createNetworkPortCtpOtuc2PA(PORT_N_OT200_OPTICAL_URI)
    );

    when(rrmOtnDbResourcesFacade.findCardFromCtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OT200_OPTICAL_URI))
    )).thenReturn(
      Optional.of(new CardRef(NETWORK_ELEMENT_ID, new Aid(CARD_AID), null, null, 0))
    );

    when(rrmOtnDbResourcesFacade.listCtpsOnCard(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(CARD_AID))
    )).thenReturn(
      List.of(
        createCtp(NETWORK_ELEMENT_ID, new Aid(OTU2E_CTP_AID), new Uri(PORT_C1_OPTICAL_URI), OTU2E_LAYERS),
        createCtp(NETWORK_ELEMENT_ID, new Aid(ODU2E_CTP_AID), new Uri(PORT_C1_CTP_URI), ODU2E_LAYERS),
        createCtp(NETWORK_ELEMENT_ID, new Aid(OT200_CTP_AID), new Uri(PORT_N_OT200_OPTICAL_URI), OTUC2PA_LAYERS)
      )
    );

    when(rrmOtnDbResourcesFacade.findCtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_CTP_URI))
    )).thenReturn(
      Optional.empty()
    );

    when(rrmOtnDbResourcesFacade.findCardFromCtp(
      eq(NETWORK_ELEMENT_ID),
      (Uri) any()
    )).thenReturn(
      Optional.of(new CardRef(NETWORK_ELEMENT_ID, null, null, "OF-2D16DCT", 1))
    );

    when(protectionGroupFinder.findByRelatedSlcZEndpointCtp(
      NETWORK_ELEMENT_ID,
      PORT_N_CTP_URI
    )).thenReturn(
      Optional.of(new LineProtectionGroupRef(NETWORK_ELEMENT_ID,
        new Aid("Protection-1/3/traffic-1"),
        new Uri(PROTECTION_GROUP_URI),
        new Aid("TRAFFIC-1/3/n1-e/traffic"),
        new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,3/eq/card/ptp/nw,1-e/ctp/traffic"),
        new Aid("TRAFFIC-1/3/n1-w/traffic"),
        new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,3/eq/card/ptp/nw,1-w/ctp/traffic"),
        new Aid("Connection-1/3/traffic/1"),
        new Uri("/mit/me/1/eqh/shelf,1/eqh/slot,3/eq/card/sn/traffic/snc/1"),
        null,
        null,
        null,
        null
      ))
    );

    var C_PORT_LABEL = new Whole();
    var N_PORT_LABEL = new Container(1);
    var otu4_fec_type_no_fec = new MoCimParameter("otu4", "fec-type", new CimValue.Enum("nofec"));
    var optm_fts_control_dsbld = new MoCimParameter("optm", "fts-control", new CimValue.Enum("dsbld"));
    var termination_mode_nss = new MoCimParameter("", "termination-mode", new CimValue.Enum("nss"));

    var request = generateCrossConnectRequest(opticalParams, "Port-1/1/c1", C_PORT_LABEL, false,
      "Port-1/1/n1", N_PORT_LABEL, false, signalType, InterfaceType.ENNI, InterfaceType.UNSPECIFIED);

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(7, result.size());
      assertInstanceOf(PtpRequest.class, result.get(0));
      assertEquals(generatePtpRequest(CLIENT_PTP_URI, false), result.get(0));
      assertInstanceOf(CtpRequest.class, result.get(1));
      assertEquals(generateCtpRequest(PORT_C1_OPTICAL_URI, false, Set.of(otu4_fec_type_no_fec)), result.get(1));
      assertInstanceOf(PtpParamsRequest.class, result.get(2));
      assertEquals(generatePtpParamsRequest(CLIENT_PTP_URI, false, Set.of(optm_fts_control_dsbld)), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_C1_CTP_URI, false, Set.of(termination_mode_nss)), result.get(3));
      assertInstanceOf(CtpRequest.class, result.get(4));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI, false, Set.of(termination_mode_nss)), result.get(4));
      assertInstanceOf(SncRequest.class, result.get(5));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/sn/odu4/snc", false,
        Set.of(PORT_C1_CTP_URI),
        Set.of(PORT_N_CTP_URI),
        provisionApi), result.get(5));
      assertInstanceOf(EpteRequest.class, result.get(6));
      assertEquals(new EpteRequest(NETWORK_ELEMENT_ID, PORT_N_CTP_URI, PROTECTION_GROUP_URI, provisionApi), result.get(6));
    } catch (OtnProvisioningException e) {
      fail("No Exception expected: " + e);
    }
  }

  private static CtpRef createCtpRef(String uri) {
    return new CtpRef(NETWORK_ELEMENT_ID, null, new Uri(uri), null);
  }

  private static Optional<Ctp> createNetworkPortCtpOtuc2() {
    return Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OPTICAL_URI), OTUC2_LAYERS));
  }

  private static Optional<Ctp> createClientPortCtpOtu2E(String clientPortUri) {
    return Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(OTU2E_CTP_AID), new Uri(clientPortUri), OTU2E_LAYERS));
  }

  private static List<Ctp> createListOfClientPortCtpOtu2(String clientPortUri) {
    return List.of(createCtp(NETWORK_ELEMENT_ID, new Aid(OTU2_CTP_AID), new Uri(clientPortUri), OTU2_LAYERS));
  }

  private static Optional<Ctp> createClientPortCtpOtu2(String clientPortUri) {
    return Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(OTU2_CTP_AID), new Uri(clientPortUri), OTU2_LAYERS));
  }

  private static Optional<Ctp> createClientPortCtpOtu4(String clientPortUri) {
    return Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(OTU4_CTP_AID), new Uri(clientPortUri), OTU4_LAYERS));
  }

  private static List<Ctp> createListOfNetworkPortCtpOtuc2() {
    return List.of(createCtp(NETWORK_ELEMENT_ID, new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OPTICAL_URI), OTUC2_LAYERS));
  }

  private static Optional<Ctp> createClientPortCtp25GBE() {
    return Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(ET25_CTP_AID), new Uri(PORT_C_OPTICAL_URI_ET25), _25GBE_LAYERS));
  }

  private static List<Ctp> createListOfNetworkPortCtpOtu4(String networkPortUri) {
    return List.of(createCtp(NETWORK_ELEMENT_ID, new Aid(OTU4_CTP_AID), new Uri(networkPortUri), OTU4_LAYERS));
  }

  private static Optional<Ctp> createClientPortCtp25GBE(String clientOTUUri) {
    return Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(ET25_CTP_AID), new Uri(clientOTUUri), _25GBE_LAYERS));
  }

  private static Optional<Ctp> createNetworkPortCtpOtu4(String networkPortUri) {
    return Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(OTU4_CTP_AID), new Uri(networkPortUri), OTU4_LAYERS));
  }

  private static Optional<Ctp> createNetworkPortCtpOtuc2PA(String networkPortUri) {
    return Optional.of(createCtp(NETWORK_ELEMENT_ID, new Aid(OT200_CTP_AID), new Uri(networkPortUri), OTUC2PA_LAYERS));
  }

  private static Ctp createCtp(int neId, Aid ctpAid, Uri ctpUri, List<String> layerRate) {
    return CtpBuilder.newBuilder()
      .withNeId(neId)
      .withAid(ctpAid)
      .withUri(ctpUri)
      .withLayerRate(layerRate)
      .withTributaryPort(0)
      .build();
  }

  private static Optional<CardClusterRef> createCardClusterRef() {
    return Optional.of(new CardClusterRef(NETWORK_ELEMENT_ID, new Uri(CARD_CLUSTER_URI), "Name", "Type", List.of(CARD_URI, CARD2_URI)));
  }
}