/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capabilityprovider.mo.MoCapabilityProviderConfiguration;
import com.adva.nlms.common.config.EquipmentState;
import com.adva.nlms.mediation.common.transactions.ObjectInUseException;
import com.adva.nlms.mediation.config.f8.croma.api.CromaMOService;
import com.adva.nlms.mediation.config.f8.croma.endpoint.api.CromaEcServiceEndpointDTO;
import com.adva.nlms.mediation.config.f8.croma.endpoint.api.CromaEcServicePathDTO;
import com.adva.nlms.mediation.config.f8.croma.provision.api.SlcEndpointConfiguration;
import com.adva.nlms.mediation.config.f8.croma.provision.api.SlcExpressConfiguration;
import com.adva.nlms.mediation.ec.model.F8OperState;
import com.adva.nlms.mediation.ec.support.EcEntityIndex;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.CtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.OperationalState;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.State;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.topology.NEData;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.OpticalParameters.SelectedParameter;
import com.adva.nlms.opticalparameters.api.Value;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.opticalparameters.api.enums.ParameterName;
import com.adva.nlms.opticalparameters.cim.api.CimValue;
import com.adva.nlms.resource.crm.model.wdm.CrmWdmModelConfiguration;
import com.adva.nlms.resource.crm.model.wdm.CrmWdmModelDAO;
import com.adva.nlms.resource.crm.model.wdm.LtpInfo;
import com.adva.nlms.resource.crm.model.wdm.SpectrumInfo;
import com.adva.nlms.resource.crm.model.wdm.TtpInfo;
import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;
import com.adva.nlms.resource.mediator.f8.RRMConfiguration;
import com.adva.nlms.resource.mediator.f8.SegmentRequestFailedException;
import com.adva.nlms.resource.mediator.f8.wdm.adapters.persistence.RrmPersistenceConfiguration;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.CrmNodePosition;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.RrmSegmentRepository;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.WdmSegment;
import com.adva.nlms.resource.provision.f8.api.in.CimProvisionRequestParametersCreator;
import com.adva.nlms.resource.provision.f8.api.in.MoCimParameter;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import com.adva.nlms.resource.provision.f8.api.in.ProvisionException;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class WdmSegmentCreateRequestTest {
  private static CimProvisionRequestParametersCreator parametersCreator;
  private static MoCapabilityProvider moCapabilityProvider;
  private static final String SW_VERSION = "6.5.1";
  private SegmentCreateRequest segmentCreateRequest;
  private CrmWdmModelDAO modelDao;
  private RrmSegmentRepository repository;
  private Provision provision;
  private CtpResources ctpResources;
  private PtpResources ptpResources;
  private CromaMOService cromaMOService;
  private NEDataProvider neDataProvider;
  private final State IS = new State(EquipmentState.IS, OperationalState.OUTAGE, List.of());

  @BeforeAll
  static void initCapabilityProvider() {
    moCapabilityProvider = new MoCapabilityProviderConfiguration().moCapabilityProvider();
    parametersCreator = new RRMConfiguration().cimProvisionRequestParametersCreator((moCapabilityProvider));
  }

  @BeforeEach
  void init() {
    var config = new WdmSegmentRequestConfig();
    modelDao = new CrmWdmModelConfiguration().crmWdmModelDAO();
    repository = new RrmPersistenceConfiguration().rrmSegmentRepository();
    var networkTransactionWrapper = new DummyNetworkTransactionWrapper();
    provision = mock();
    ctpResources = mock();
    ptpResources = mock();
    FiberTracer fiberTracer = mock();
    cromaMOService = mock();
    var housekeeper = config.wdmProvisionHousekeeper(provision, repository);
    neDataProvider = mock();
    segmentCreateRequest = config.wdmSegmentCreateRequest(
      modelDao,
      repository,
      networkTransactionWrapper,
      parametersCreator,
      provision,
      ptpResources,
      ctpResources,
      cromaMOService,
      fiberTracer,
      moCapabilityProvider,
      housekeeper,
      neDataProvider);
  }

  private static final String REQUEST_ID = "segment id";
  private static final int NE_ID = 23;
  private static final NetworkElementID NE_ID_WRAP = NetworkElementID.create(NE_ID);
  private final static SpectrumInfo OPAQUE_SPECTRUM = new SpectrumInfo();
  private final static SpectrumInfo NON_OPAQUE_SPECTRUM = new SpectrumInfo(SpectrumInfo.Type.TYPE_FREQUENCY,
    192000000, 196000000, 3125, 6250, new SpectrumInfo.SlotWidthSupport(37500, 4918750, 12500), false);
  private static final String PROTECTION_GROUP_URI = "protection-group-uri";

  @Test
  void transitIlaHappyPath() throws ObjectInUseException {
    var srcAid = "Port-1/2/n";
    var dstAid = "Port-3/4/n";
    // Given
    modelDao.addLtp(NE_ID, new LtpInfo(srcAid, OPAQUE_SPECTRUM, 1, false));
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, OPAQUE_SPECTRUM, 2, false));

    // When
    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.forwardSequenceNumber = 3;
    request.reverseSequenceNumber = 2;
    setNetworkElementVersion();
    var context = segmentCreateRequest.buildContext(request);
    var result = segmentCreateRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    assertTrue(result.deletedSegments().isEmpty());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
    assertEquals(0, segment.getSlcId());
    assertEquals(CrmNodePosition.TRANSIT, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(dstAid, segment.getAidDstTp());
    assertNull(segment.getTransponderCtpUri());
  }

  @Test
  void ingressRoadmWithPluggableLineCardHappyPath() throws ObjectInUseException {
    var ttpAid = "Port-1/1/n1";
    var srcAid = "Port-1/2/n1";
    var dstAid = "Port-3/4/n";
    var cardAid = "Card-1/2";
    var cardUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card";
    var ptpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1";
    var ctpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1/ctp/otuc2";
    var slcId = 1;
    var channel = 196000000;
    var bandwidth = 37500;

    // Given
    TtpInfo ttpInfo = WdmSegmentUtil.sampleTtpWithTunableTps(ttpAid, srcAid);
    modelDao.addTtp(NE_ID, ttpInfo);
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, NON_OPAQUE_SPECTRUM, 2, false));

    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.forwardSequenceNumber = 1;
    request.reverseSequenceNumber = 2;
    request.setPointDelta = 1.0;
    request.channelSrcLabel = channel;
    request.slotWidthSrcLabel = bandwidth;
    request.portParams = new ArrayList<>();
    request.portParams.add(new SelectedParameter(ParameterName.GLQ, new Value.Enum("OTUC2")));
    request.portParams.add(new SelectedParameter(ParameterName.CHANNEL, new Value.Numeric(new BigDecimal(channel), "MHz")));
    when(ptpResources.findPtp(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(new PtpRef(NE_ID, new Aid(ttpAid), new Uri(ptpUri), IS)));
    when(ptpResources.findCard(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(new CardRef(NE_ID, new Aid(cardAid), new Uri(cardUri), "OF-2D16DCT", F8OperState.ANR.getValue())));
    when(ptpResources.findPlug(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(PlugRef.builder()
        .setNeId(NE_ID)
        .setAid("")
        .setUri("")
        .setType("CFP2-448G-#DCTCG-SM-LC")
        .build())
      );
    when(ctpResources.findCtpExtended(NE_ID, new Uri(ctpUri)))
      .thenReturn(Optional.empty());
    when(provision.createSlc(NE_ID, new SlcExpressConfiguration(
      List.of(bandwidth),
      List.of(channel),
      new SlcEndpointConfiguration(2, "/mit/me/1/croma/degree/2", 10),
      new SlcEndpointConfiguration(1, ctpUri, 10)
    ))).thenReturn(slcId);

    setNetworkElementVersion();

    // When
    var context = segmentCreateRequest.buildContext(request);
    var result = segmentCreateRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    assertTrue(result.deletedSegments().isEmpty());
    verify(provision).updateAdminState(NE_ID_WRAP, ptpUri, AdminState.DOWN);
    verify(provision).updatePtp(NE_ID_WRAP, ptpUri, Set.of(
      new MoCimParameter("opt", "tuned-frequency", new CimValue.Int(channel))));
    verify(provision).updateAdminState(NE_ID_WRAP, ptpUri, AdminState.UP);
    verify(provision).provisionCtp(NE_ID_WRAP, ctpUri, Set.of());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
    assertEquals(slcId, segment.getSlcId());
    assertEquals(CrmNodePosition.INGRESS, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(dstAid, segment.getAidDstTp());
    assertEquals(ttpAid, segment.getTtpAid());
    assertEquals(ctpUri, segment.getTransponderCtpUri());
  }

  @Test
  void transitWithLeftoverSegment() throws ObjectInUseException {
    var srcAid = "Port-1/2/n";
    var dstAid = "Port-3/4/n";
    var leftoverSegmentId = REQUEST_ID + " 2";
    var mutableSrcAid = "Port-1/3/n";
    var mutableDstAid = "Port-3/2/n";
    var slcId = 42;

    // Given
    modelDao.addLtp(NE_ID, new LtpInfo(srcAid, NON_OPAQUE_SPECTRUM, 1, false));
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, NON_OPAQUE_SPECTRUM, 2, false));
    var leftoverSegment = WdmSegment.newSegment()
      .withNeId(NE_ID)
      .withSegmentRequestId(leftoverSegmentId)
      .withNodePosition(CrmNodePosition.TRANSIT)
      .withAidSrcTp(mutableSrcAid)
      .withAidDstTp(mutableDstAid)
      .withChannelLabel(195000000)
      .withSlcId(slcId)
      .build();
    repository.store(leftoverSegment);

    var channel = 196000000;
    var bandwidth = 37500;
    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.channelSrcLabel = channel;
    request.slotWidthSrcLabel = bandwidth;
    request.forwardSequenceNumber = 3;
    request.reverseSequenceNumber = 2;
    request.setPointDelta = 1.0;

    when(provision.createSlc(NE_ID, new SlcExpressConfiguration(
      List.of(bandwidth),
      List.of(channel),
      new SlcEndpointConfiguration(3, "/mit/me/1/croma/degree/1", 10),
      new SlcEndpointConfiguration(2, "/mit/me/1/croma/degree/2", 10)
    ))).thenReturn(slcId);

    setNetworkElementVersion();

    // When
    var context = segmentCreateRequest.buildContext(request);
    var result = segmentCreateRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
    assertEquals(slcId, segment.getSlcId());
    assertEquals(CrmNodePosition.TRANSIT, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(dstAid, segment.getAidDstTp());
    assertNull(segment.getTransponderCtpUri());
    var leftoverSegmentDto = repository.getSegment(leftoverSegmentId, NE_ID);
    assertNotNull(leftoverSegmentDto);
    assertEquals(0, leftoverSegmentDto.getSlcId());
  }

  @Test
  void transitWithMutableSegment() throws ObjectInUseException {
    var srcAid = "Port-1/2/n";
    var dstAid = "Port-3/4/n";
    var mutableId = REQUEST_ID + " 2";
    var mutableSrcAid = "Port-1/3/n";
    var mutableDstAid = "Port-3/2/n";

    // Given
    modelDao.addLtp(NE_ID, new LtpInfo(srcAid, OPAQUE_SPECTRUM, 1, false));
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, OPAQUE_SPECTRUM, 2, false));
    var mutableSegment = WdmSegment.newSegment()
      .withNeId(NE_ID)
      .withSegmentRequestId(mutableId)
      .withNodePosition(CrmNodePosition.TRANSIT)
      .withAidSrcTp(mutableSrcAid)
      .withAidDstTp(mutableDstAid)
      .withChannelLabel(196000000)
      .build();
    repository.store(mutableSegment);

    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.channelSrcLabel = 196000000;
    request.forwardSequenceNumber = 3;
    request.reverseSequenceNumber = 2;
    request.setMutableSegments(List.of(mutableId));

    setNetworkElementVersion();

    // When
    var context = segmentCreateRequest.buildContext(request);
    var result = segmentCreateRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    assertEquals(List.of(mutableId), result.deletedSegments());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
    assertEquals(0, segment.getSlcId());
    assertEquals(CrmNodePosition.TRANSIT, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(dstAid, segment.getAidDstTp());
    assertNull(segment.getTransponderCtpUri());
  }

  @Test
  void transitModificationWithNoChange() throws ObjectInUseException {
    var srcAid = "Port-1/2/n";
    var dstAid = "Port-3/4/n";
    var mutableId = REQUEST_ID + " 2";

    // Given
    modelDao.addLtp(NE_ID, new LtpInfo(srcAid, OPAQUE_SPECTRUM, 1, false));
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, OPAQUE_SPECTRUM, 2, false));
    var mutableSegment = WdmSegment.newSegment()
      .withNeId(NE_ID)
      .withSegmentRequestId(mutableId)
      .withNodePosition(CrmNodePosition.TRANSIT)
      .withAidSrcTp(srcAid)
      .withAidDstTp(dstAid)
      .withChannelLabel(196000000)
      .withForwardSequenceNumber(3)
      .withReverseSequenceNumber(2)
      .build();
    repository.store(mutableSegment);

    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.channelSrcLabel = 196000000;
    request.forwardSequenceNumber = 3;
    request.reverseSequenceNumber = 2;
    request.setMutableSegments(List.of(mutableId));

    setNetworkElementVersion();

    // When
    var context = segmentCreateRequest.buildContext(request);
    var result = segmentCreateRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    assertTrue(result.deletedSegments().isEmpty());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
    assertEquals(0, segment.getSlcId());
    assertEquals(CrmNodePosition.TRANSIT, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(dstAid, segment.getAidDstTp());
    assertNull(segment.getTransponderCtpUri());
    assertNull(repository.getSegment(mutableId, NE_ID)); // segment removed
  }

  @Test
  void transitModificationWithNoChangeAnd2MutableSegments() throws ObjectInUseException {
    var srcAid = "Port-1/2/n";
    var dstAid = "Port-3/4/n";
    var mutableId1 = REQUEST_ID + " 1";
    var mutableId2 = REQUEST_ID + " 2";

    // Given
    modelDao.addLtp(NE_ID, new LtpInfo(srcAid, NON_OPAQUE_SPECTRUM, 1, false));
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, NON_OPAQUE_SPECTRUM, 2, false));
    var mutableSegment1 = WdmSegment.newSegment()
      .withNeId(NE_ID)
      .withSegmentRequestId(mutableId1)
      .withNodePosition(CrmNodePosition.TRANSIT)
      .withAidSrcTp(srcAid)
      .withAidDstTp(dstAid)
      .withChannelLabel(196000000)
      .withForwardSequenceNumber(3)
      .withReverseSequenceNumber(2)
      .build();
    repository.store(mutableSegment1);

    // Second mutable segment is identical, but controls DP
    var mutableSegment2 = WdmSegment.newSegment()
      .withNeId(NE_ID)
      .withSegmentRequestId(mutableId2)
      .withNodePosition(CrmNodePosition.TRANSIT)
      .withAidSrcTp(srcAid)
      .withAidDstTp(dstAid)
      .withChannelLabel(196000000)
      .withForwardSequenceNumber(3)
      .withReverseSequenceNumber(2)
      .withSlcId(5)
      .build();
    repository.store(mutableSegment2);

    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.channelSrcLabel = 196000000;
    request.forwardSequenceNumber = 3;
    request.reverseSequenceNumber = 2;
    request.setMutableSegments(List.of(mutableId1, mutableId2));

    setNetworkElementVersion();

    // When
    var context = segmentCreateRequest.buildContext(request);
    var result = segmentCreateRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    assertTrue(result.deletedSegments().isEmpty());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
    assertEquals(5, segment.getSlcId());
    assertEquals(CrmNodePosition.TRANSIT, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(dstAid, segment.getAidDstTp());
    assertNull(segment.getTransponderCtpUri());
    assertNull(repository.getSegment(mutableId1, NE_ID)); // segment removed
    assertNull(repository.getSegment(mutableId2, NE_ID)); // segment removed
  }

  @Test
  void ingressRoadmWithMutableSegmentOnOtherLtp() throws ObjectInUseException {
    var ttpAid = "Port-1/1/n1";
    var srcAid = "Port-1/2/n1";
    var dstAid = "Port-3/4/n";
    var cardAid = "Card-1/2";
    var cardUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card";
    var ptpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1";
    var ctpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1/ctp/otuc2";
    var slcId = 1;
    var channel = 196000000;
    var bandwidth = 37500;
    var mutableId = REQUEST_ID + " 2";
    var mutableDstAid = "Port-3/5/n";

    // Given
    TtpInfo ttpInfo = WdmSegmentUtil.sampleTtpWithTunableTps(ttpAid, dstAid);
    modelDao.addTtp(NE_ID, ttpInfo);
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, NON_OPAQUE_SPECTRUM, 2, false));
    var mutableSegment = WdmSegment.newSegment()
      .withNeId(NE_ID)
      .withSegmentRequestId(mutableId)
      .withNodePosition(CrmNodePosition.INGRESS)
      .withSlcId(slcId)
      .withAidSrcTp(srcAid)
      .withAidDstTp(mutableDstAid)
      .withChannelLabel(channel)
      .withTransponderCtpUri(ctpUri)
      .withTtpAid(ttpAid)
      .withProtectionGroupUri(PROTECTION_GROUP_URI)
      .build();
    repository.store(mutableSegment);
    var slcConfiguration = new SlcExpressConfiguration(
      List.of(bandwidth),
      List.of(channel),
      new SlcEndpointConfiguration(2, "/mit/me/1/croma/degree/2", 10),
      new SlcEndpointConfiguration(1, ctpUri, 10)
    );

    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.forwardSequenceNumber = 1;
    request.reverseSequenceNumber = 2;
    request.setPointDelta = 1.0;
    request.channelSrcLabel = channel;
    request.slotWidthSrcLabel = bandwidth;
    request.portParams = new ArrayList<>();
    request.portParams.add(new SelectedParameter(ParameterName.GLQ, new Value.Enum("OTUC2")));
    request.portParams.add(new SelectedParameter(ParameterName.CHANNEL, new Value.Numeric(new BigDecimal(channel), "MHz")));
    request.setMutableSegments(List.of(mutableId));
    when(ptpResources.findPtp(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(new PtpRef(NE_ID, new Aid(ttpAid), new Uri(ptpUri), IS)));
    when(ptpResources.findCard(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(new CardRef(NE_ID, new Aid(cardAid), new Uri(cardUri), "OF-2D16DCT", F8OperState.ANR.getValue())));
    when(ptpResources.findPlug(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(PlugRef.builder()
        .setNeId(NE_ID)
        .setAid("")
        .setUri("")
        .setType("CFP2/448G/#DCTCG/SM/LC")
        .build())
      );
    when(ctpResources.findCtpExtended(NE_ID, new Uri(ctpUri)))
      .thenReturn(Optional.empty());
    when(provision.createSlc(NE_ID, slcConfiguration)).thenReturn(slcId);

    setNetworkElementVersion();

    // When
    var context = segmentCreateRequest.buildContext(request);
    var result = segmentCreateRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    assertEquals(result.deletedSegments(), List.of(mutableId));
    verify(provision, times(1)).deleteSlc(NE_ID, slcId);
    verify(provision, times(1)).createSlc(NE_ID, slcConfiguration);
    verify(provision, times(0)).updateAdminState(NE_ID_WRAP, ptpUri, AdminState.DOWN);
    verify(provision, times(0)).updatePtp(NE_ID_WRAP, ptpUri, Set.of(
      new MoCimParameter("opt", "tuned-frequency", new CimValue.Int(channel))));
    verify(provision, times(0)).updateAdminState(NE_ID_WRAP, ptpUri, AdminState.UP);
    verify(provision, times(0)).provisionCtp(NE_ID_WRAP, ctpUri, Set.of());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
    assertEquals(slcId, segment.getSlcId());
    assertEquals(CrmNodePosition.INGRESS, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(ttpAid, segment.getTtpAid());
    assertEquals(dstAid, segment.getAidDstTp());
    assertEquals(ctpUri, segment.getTransponderCtpUri());
    assertNull(repository.getSegment(mutableId, NE_ID)); // segment removed
  }

  @Test
  void ingressRoadmWithMutableSegmentOnOtherLambda() throws ObjectInUseException {
    var ttpAid = "Port-1/1/n1";
    var srcAid = "Port-1/2/n1";
    var dstAid = "Port-3/4/n";
    var cardAid = "Card-1/2";
    var cardUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card";
    var ptpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1";
    var ctpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1/ctp/otuc2";
    var slcId = 1;
    var channel = 196000000;
    var bandwidth = 37500;
    var mutableId = REQUEST_ID + " 2";
    var mutableChannel = 195900000;


    // Given
    TtpInfo ttpInfo = WdmSegmentUtil.sampleTtpWithTunableTps(ttpAid, srcAid);
    modelDao.addTtp(NE_ID, ttpInfo);
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, NON_OPAQUE_SPECTRUM, 2, false));
    var mutableSegment = WdmSegment.newSegment()
      .withNeId(NE_ID)
      .withSegmentRequestId(mutableId)
      .withNodePosition(CrmNodePosition.INGRESS)
      .withSlcId(slcId)
      .withAidSrcTp(srcAid)
      .withAidDstTp(dstAid)
      .withChannelLabel(mutableChannel)
      .withTransponderCtpUri(ctpUri)
      .withTtpAid(ttpAid)
      .withProtectionGroupUri(PROTECTION_GROUP_URI)
      .build();
    repository.store(mutableSegment);
    var slcConfiguration = new SlcExpressConfiguration(
      List.of(bandwidth),
      List.of(channel),
      new SlcEndpointConfiguration(2, "/mit/me/1/croma/degree/2", 10),
      new SlcEndpointConfiguration(1, ctpUri, 10)
    );

    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.forwardSequenceNumber = 1;
    request.reverseSequenceNumber = 2;
    request.setPointDelta = 1.0;
    request.channelSrcLabel = channel;
    request.slotWidthSrcLabel = bandwidth;
    request.portParams = new ArrayList<>();
    request.portParams.add(new SelectedParameter(ParameterName.GLQ, new Value.Enum("OTUC2")));
    request.portParams.add(new SelectedParameter(ParameterName.CHANNEL, new Value.Numeric(new BigDecimal(channel), "MHz")));
    request.setMutableSegments(List.of(mutableId));
    when(ptpResources.findPtp(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(new PtpRef(NE_ID, new Aid(ttpAid), new Uri(ptpUri), IS)));
    when(ptpResources.findCard(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(new CardRef(NE_ID, new Aid(cardAid), new Uri(cardUri), "OF-2D16DCT", F8OperState.ANR.getValue())));
    when(ptpResources.findPlug(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(PlugRef.builder()
        .setNeId(NE_ID)
        .setAid("")
        .setUri("")
        .setType("CFP2-448G-#BDCTC-SM-LC")
        .build())
      );
    when(ctpResources.findCtpExtended(NE_ID, new Uri(ctpUri)))
      .thenReturn(Optional.empty());
    when(provision.createSlc(NE_ID, slcConfiguration)).thenReturn(slcId);

    setNetworkElementVersion();

    // When
    var context = segmentCreateRequest.buildContext(request);
    var result = segmentCreateRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    assertEquals(result.deletedSegments(), List.of(mutableId));
    verify(provision, times(1)).deleteSlc(NE_ID, slcId);
    verify(provision, times(1)).createSlc(NE_ID, slcConfiguration);
    verify(provision).updateAdminState(NE_ID_WRAP, ptpUri, AdminState.DOWN);
    verify(provision).updatePtp(NE_ID_WRAP, ptpUri, Set.of(
      new MoCimParameter("opt", "tuned-frequency", new CimValue.Int(channel))));
    verify(provision).updateAdminState(NE_ID_WRAP, ptpUri, AdminState.UP);
    verify(provision, times(0)).provisionCtp(NE_ID_WRAP, ctpUri, Set.of());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
    assertEquals(slcId, segment.getSlcId());
    assertEquals(CrmNodePosition.INGRESS, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(dstAid, segment.getAidDstTp());
    assertEquals(ttpAid, segment.getTtpAid());
    assertEquals(ctpUri, segment.getTransponderCtpUri());
    assertEquals(channel, segment.getChannelLabel());
    assertNull(repository.getSegment(mutableId, NE_ID)); // segment removed
  }

  @Test
  void contextIsNull() {
    var request = new CrmSegmentRequestDto();
    var actual = Assertions.assertThatThrownBy(() -> segmentCreateRequest.execute(request, null));
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("Context is null");
  }

  @Test
  void contextIsOfInvalidType() {
    var request = new CrmSegmentRequestDto();
    var context = new SegmentSetAdminStateContext(1);
    var actual = Assertions.assertThatThrownBy(() -> segmentCreateRequest.execute(request, context));
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("Invalid Context type SegmentSetAdminStateContext");
  }

  @Test
  void ingressRoadmWithMutableSegmentLineCardException() {
    var ttpAid = "Port-1/1/n1";
    var srcAid = "Port-1/2/n1";
    var dstAid = "Port-3/4/n";
    var cardAid = "Card-1/2";
    var cardUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card";
    var ptpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1";
    var ctpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1/ctp/otuc2";
    var slcId = 1;
    var channel = 196000000;
    var bandwidth = 37500;
    var mutableId = REQUEST_ID + " 2";
    var mutableChannel = 195900000;

    // Given
    TtpInfo ttpInfo = WdmSegmentUtil.sampleTtpWithTunableTps(ttpAid, srcAid);
    modelDao.addTtp(NE_ID, ttpInfo);
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, NON_OPAQUE_SPECTRUM, 2, false));
    var mutableSegment = WdmSegment.newSegment()
      .withNeId(NE_ID)
      .withSegmentRequestId(mutableId)
      .withNodePosition(CrmNodePosition.INGRESS)
      .withSlcId(slcId)
      .withAidSrcTp(srcAid)
      .withAidDstTp(dstAid)
      .withChannelLabel(mutableChannel)
      .withTransponderCtpUri(ctpUri)
      .withTtpAid(ttpAid)
      .withProtectionGroupUri(PROTECTION_GROUP_URI)
      .build();
    repository.store(mutableSegment);

    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.forwardSequenceNumber = 1;
    request.reverseSequenceNumber = 2;
    request.setPointDelta = 1.0;
    request.channelSrcLabel = channel;
    request.slotWidthSrcLabel = bandwidth;
    request.portParams = new ArrayList<>();
    request.portParams.add(new SelectedParameter(ParameterName.GLQ, new Value.Enum("OTUC2")));
    request.portParams.add(new SelectedParameter(ParameterName.CHANNEL, new Value.Numeric(new BigDecimal(channel), "MHz")));
    request.setMutableSegments(List.of(mutableId));
    when(ptpResources.findPtp(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(new PtpRef(NE_ID, new Aid(ttpAid), new Uri(ptpUri), IS)));
    when(ptpResources.findCard(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(new CardRef(NE_ID, new Aid(cardAid), new Uri(cardUri), "OF-2D16DCT", F8OperState.ANR.getValue())));
    when(ptpResources.findPlug(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(PlugRef.builder()
        .setNeId(NE_ID)
        .setAid("")
        .setUri("")
        .setType("CFP2-448G-#DCTCG-SM-LC")
        .build())
      );
    when(ctpResources.findCtpExtended(NE_ID, new Uri(ctpUri)))
      .thenReturn(Optional.empty());
    doThrow(new ProvisionException("Cannot set parameter"))
      .when(provision)
      .updatePtp(NE_ID_WRAP, ptpUri, Set.of(new MoCimParameter("opt", "tuned-frequency", new CimValue.Int(channel))));

    setNetworkElementVersion();

    // When
    var context = segmentCreateRequest.buildContext(request);
    var actual = Assertions.assertThatThrownBy(() -> segmentCreateRequest.execute(request, context));

    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("Port Configuration failed - Cannot set parameter");

    verify(provision, times(1)).deleteSlc(NE_ID, slcId);

    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNull(segment);
    var mutable = repository.getSegment(mutableId, NE_ID);
    assertThat(mutable).isNotNull()
      .extracting(WdmSegment::getSlcId,
                  WdmSegment::getNodePosition,
                  WdmSegment::getAidSrcTp,
                  WdmSegment::getAidDstTp,
                  WdmSegment::getTtpAid,
                  WdmSegment::getTransponderCtpUri,
                  WdmSegment::getChannelLabel,
                  WdmSegment::getForwardSequenceNumber,
                  WdmSegment::getReverseSequenceNumber,
                  WdmSegment::isBandwidthExplicitlySet)
      .containsExactly(slcId, CrmNodePosition.INGRESS, srcAid, dstAid, ttpAid, ctpUri, mutableChannel, 0, 0, false);
  }

  @Test
  void ingressRoadmWithMutableSegmentSlcException() {
    var ttpAid = "Port-1/1/n1";
    var srcAid = "Port-1/2/n1";
    var dstAid = "Port-3/4/n";
    var cardAid = "Card-1/2";
    var cardUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card";
    var ptpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1";
    var ctpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1/ctp/otuc2";
    var slcId = 1;
    var channel = 196000000;
    var bandwidth = 37500;
    var mutableId = REQUEST_ID + " 2";
    var mutableChannel = 195900000;

    // Given
    TtpInfo ttpInfo = WdmSegmentUtil.sampleTtpWithTunableTps(ttpAid, srcAid);
    modelDao.addTtp(NE_ID, ttpInfo);
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, NON_OPAQUE_SPECTRUM, 2, false));
    var mutableSegment = WdmSegment.newSegment()
      .withNeId(NE_ID)
      .withSegmentRequestId(mutableId)
      .withNodePosition(CrmNodePosition.INGRESS)
      .withSlcId(slcId)
      .withAidSrcTp(srcAid)
      .withAidDstTp(dstAid)
      .withChannelLabel(mutableChannel)
      .withTransponderCtpUri(ctpUri)
      .withTtpAid(ttpAid)
      .withProtectionGroupUri(PROTECTION_GROUP_URI)
      .build();
    repository.store(mutableSegment);
    var slcConfiguration = new SlcExpressConfiguration(
      List.of(bandwidth),
      List.of(channel),
      new SlcEndpointConfiguration(2, "/mit/me/1/croma/degree/2", 10),
      new SlcEndpointConfiguration(1, ctpUri, 10)
    );

    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.forwardSequenceNumber = 1;
    request.reverseSequenceNumber = 2;
    request.setPointDelta = 1.0;
    request.channelSrcLabel = channel;
    request.slotWidthSrcLabel = bandwidth;
    request.portParams = new ArrayList<>();
    request.portParams.add(new SelectedParameter(ParameterName.GLQ, new Value.Enum("OTUC2")));
    request.portParams.add(new SelectedParameter(ParameterName.CHANNEL, new Value.Numeric(new BigDecimal(channel), "MHz")));
    request.setMutableSegments(List.of(mutableId));
    when(ptpResources.findPtp(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(new PtpRef(NE_ID, new Aid(ttpAid), new Uri(ptpUri), IS)));
    when(ptpResources.findCard(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(new CardRef(NE_ID, new Aid(cardAid), new Uri(cardUri), "OF-2D16DCT", F8OperState.ANR.getValue())));
    when(ptpResources.findPlug(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(PlugRef.builder()
        .setNeId(NE_ID)
        .setAid("")
        .setUri("")
        .setType("CFP2-448G-#DCTCG-SM-LC")
        .build())
      );
    when(ctpResources.findCtpExtended(NE_ID, new Uri(ctpUri)))
      .thenReturn(Optional.empty());
    when(provision.createSlc(NE_ID, slcConfiguration))
      .thenThrow(new ProvisionException("Method Not Allowed on uri: /mit/me/1/croma/slc. Operation denied, service path is not available."));

    setNetworkElementVersion();

    // When
    var context = segmentCreateRequest.buildContext(request);
    var actual = Assertions.assertThatThrownBy(() -> segmentCreateRequest.execute(request, context));
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("SLC configuration failed - Method Not Allowed on uri: /mit/me/1/croma/slc. Operation denied, service path is not available.");

    verify(provision, times(1)).deleteSlc(NE_ID, slcId);
    verify(provision, times(1)).createSlc(NE_ID, slcConfiguration);

    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNull(segment);
    var mutable = repository.getSegment(mutableId, NE_ID);
    assertNotNull(mutable);
    assertEquals(slcId, mutable.getSlcId());
    assertEquals(CrmNodePosition.INGRESS, mutable.getNodePosition());
    assertEquals(srcAid, mutable.getAidSrcTp());
    assertEquals(dstAid, mutable.getAidDstTp());
    assertEquals(ttpAid, mutable.getTtpAid());
    assertEquals(ctpUri, mutable.getTransponderCtpUri());
    assertEquals(mutableChannel, mutable.getChannelLabel());
    assertEquals(0, mutable.getForwardSequenceNumber());
    assertEquals(0, mutable.getReverseSequenceNumber());
    assertFalse(mutable.isBandwidthExplicitlySet());
  }

  @Test
  void ingressRoadmWithPluggableLineCardSlcAlreadyCreated() {
    var ttpAid = "Port-1/2/n1";
    var srcAid = "Port-1/2/n1";
    var dstAid = "Port-3/4/n";
    var cardAid = "Card-1/2";
    var cardUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card";
    var ptpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1";
    var ctpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1/ctp/otuc2";
    var sepUri = "/mit/me/1/croma/svcep//mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1";
    var slcId = 3;
    var channel = 196000000;
    var bandwidth = 37500;

    // Given
    TtpInfo ttpInfo = WdmSegmentUtil.sampleTtpWithTunableTps(ttpAid, srcAid);
    ttpInfo.setSepUri(sepUri);
    modelDao.addTtp(NE_ID, ttpInfo);
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, NON_OPAQUE_SPECTRUM, 2, false));

    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.forwardSequenceNumber = 1;
    request.reverseSequenceNumber = 2;
    request.setPointDelta = 1.0;
    request.channelSrcLabel = channel;
    request.slotWidthSrcLabel = bandwidth;
    request.portParams = new ArrayList<>();
    request.portParams.add(new SelectedParameter(ParameterName.GLQ, new Value.Enum("OTUC2")));
    request.portParams.add(new SelectedParameter(ParameterName.CHANNEL, new Value.Numeric(new BigDecimal(channel), "MHz")));
    var ptpRef = Optional.of(new PtpRef(NE_ID, new Aid(ttpAid), new Uri(ptpUri), IS));
    when(ptpResources.findPtp(NE_ID, new Aid(ttpAid)))
      .thenReturn(ptpRef);
    when(ptpResources.findCard(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(new CardRef(NE_ID, new Aid(cardAid), new Uri(cardUri), "OF-2D16DCT", F8OperState.ANR.getValue())));
    when(ptpResources.findPlug(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(PlugRef.builder()
        .setNeId(NE_ID)
        .setAid("")
        .setUri("")
        .setType("CFP2-448G-#DCTCG-SM-LC")
        .build())
      );
    when(ctpResources.findCtpExtended(NE_ID, new Uri(ctpUri)))
      .thenReturn(Optional.empty());

    var servicePath = new CromaEcServicePathDTO();
    servicePath.setSlc("/mit/me/1/croma/slc/" + slcId);
    servicePath.setPortId(ptpUri);
    var servicePaths = List.of(servicePath);
    var sep = new CromaEcServiceEndpointDTO();
    sep.setNeID(NE_ID);
    sep.setServicePathEcDTOS(servicePaths);
    when(cromaMOService.getServiceEndpoint(NE_ID, EcEntityIndex.getEcEntityIndex(sepUri)))
      .thenReturn(sep);
    when(ptpResources.findPtp(NE_ID, new Uri(ptpUri)))
      .thenReturn(ptpRef);

    setNetworkElementVersion();

    // When
    var context = segmentCreateRequest.buildContext(request);
    var actual = Assertions.assertThatThrownBy(() -> segmentCreateRequest.execute(request, context));
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("Found SLC 3 already occupying requested port " + ctpUri);
  }

  @Test
  void ingressRoadmWithPluggableLineCardAndOppmSlcAlreadyCreated() {
    var ttpAid = "Port-1/2/n1";
    var srcAid = "Port-1/3/n-e";
    var dstAid = "Port-3/4/n";
    var cardAid = "Card-1/2";
    var cardUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card";
    var ptpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1";
    var ptpNeUri = "/mit/me/1/eqh/shelf,1/eqh/slot,3/eq/card/ptp/nw,e";
    var ptpNwUri = "/mit/me/1/eqh/shelf,1/eqh/slot,3/eq/card/ptp/nw,w";
    var ctpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1/ctp/otuc2";
    var sepUri = "/mit/me/1/croma/svcep//mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1";
    var slcId = 3;
    var channel = 196000000;
    var bandwidth = 37500;

    // Given
    TtpInfo ttpInfo = WdmSegmentUtil.sampleTtpWithTunableTps(ttpAid, srcAid);
    ttpInfo.setSepUri(sepUri);
    modelDao.addTtp(NE_ID, ttpInfo);
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, NON_OPAQUE_SPECTRUM, 2, false));

    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.forwardSequenceNumber = 1;
    request.reverseSequenceNumber = 2;
    request.setPointDelta = 1.0;
    request.channelSrcLabel = channel;
    request.slotWidthSrcLabel = bandwidth;
    request.portParams = new ArrayList<>();
    request.portParams.add(new SelectedParameter(ParameterName.GLQ, new Value.Enum("OTUC2")));
    request.portParams.add(new SelectedParameter(ParameterName.CHANNEL, new Value.Numeric(new BigDecimal(channel), "MHz")));
    when(ptpResources.findPtp(NE_ID, new Aid(ttpAid))).thenReturn(Optional.of(new PtpRef(NE_ID, new Aid(ttpAid), new Uri(ptpUri), IS)));
    when(ptpResources.findCard(NE_ID, new Aid(ttpAid))).thenReturn(Optional.of(new CardRef(NE_ID, new Aid(cardAid), new Uri(cardUri), "OF-2D16DCT", F8OperState.ANR.getValue())));
    when(ptpResources.findPlug(NE_ID, new Aid(ttpAid))).thenReturn(Optional.of(PlugRef.builder()
      .setNeId(NE_ID)
      .setAid("")
      .setUri("")
      .setType("CFP2-448G-#DCTCG-SM-LC")
      .build()
    ));
    when(ctpResources.findCtpExtended(NE_ID, new Uri(ctpUri))).thenReturn(Optional.empty());

    var servicePathNe = new CromaEcServicePathDTO();
    servicePathNe.setSlc("/mit/me/1/croma/slc/" + slcId);
    servicePathNe.setPortId(ptpNeUri);
    var servicePathNw = new CromaEcServicePathDTO();
    servicePathNw.setPortId(ptpNwUri);
    var servicePaths = List.of(servicePathNe, servicePathNw);
    var sep = new CromaEcServiceEndpointDTO();
    sep.setNeID(NE_ID);
    sep.setServicePathEcDTOS(servicePaths);
    when(cromaMOService.getServiceEndpoint(NE_ID, EcEntityIndex.getEcEntityIndex(sepUri))).thenReturn(sep);
    var ptpNeRef = new PtpRef(NE_ID, new Aid(srcAid), new Uri(ptpNeUri), IS);
    when(ptpResources.findPtp(NE_ID, new Uri(ptpNeUri))).thenReturn(Optional.of(ptpNeRef));
    var ptpNwRef = new PtpRef(NE_ID, new Aid("Port-1/3/n-w"), new Uri(ptpNwUri), IS);
    when(ptpResources.findPtp(NE_ID, new Uri(ptpNwUri))).thenReturn(Optional.of(ptpNwRef));

    setNetworkElementVersion();

    // When
    var context = segmentCreateRequest.buildContext(request);
    var actual = Assertions.assertThatThrownBy(() -> segmentCreateRequest.execute(request, context));

    // Then
    actual.isInstanceOf(SegmentRequestFailedException.class)
      .hasMessageEndingWith("Found SLC 3 already occupying requested port " + ctpUri);
  }

  @Test
  void ingressRoadmWithPluggableLineCardAndOppmSlcAlreadyCreatedOnOtherPort() throws ObjectInUseException {
    var ttpAid = "Port-1/2/n1";
    var srcAid = "Port-1/3/n-w";
    var dstAid = "Port-3/4/n";
    var cardAid = "Card-1/2";
    var cardUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card";
    var ptpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1";
    var ptpNeUri = "/mit/me/1/eqh/shelf,1/eqh/slot,3/eq/card/ptp/nw,e";
    var ptpNwUri = "/mit/me/1/eqh/shelf,1/eqh/slot,3/eq/card/ptp/nw,w";
    var ctpUri = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1/ctp/otuc2";
    var sepUri = "/mit/me/1/croma/svcep//mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1";
    var slcId = 3;
    var channel = 196000000;
    var bandwidth = 37500;

    // Given
    TtpInfo ttpInfo = WdmSegmentUtil.sampleTtpWithTunableTps(ttpAid, srcAid);
    ttpInfo.setSepUri(sepUri);
    modelDao.addTtp(NE_ID, ttpInfo);
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, NON_OPAQUE_SPECTRUM, 2, false));

    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.forwardSequenceNumber = 1;
    request.reverseSequenceNumber = 2;
    request.setPointDelta = 1.0;
    request.channelSrcLabel = channel;
    request.slotWidthSrcLabel = bandwidth;
    request.portParams = new ArrayList<>();
    request.portParams.add(new SelectedParameter(ParameterName.GLQ, new Value.Enum("OTUC2")));
    request.portParams.add(new SelectedParameter(ParameterName.CHANNEL, new Value.Numeric(new BigDecimal(channel), "MHz")));
    when(ptpResources.findPtp(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(new PtpRef(NE_ID, new Aid(ttpAid), new Uri(ptpUri), IS)));
    when(ptpResources.findCard(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(new CardRef(NE_ID, new Aid(cardAid), new Uri(cardUri), "OF-2D16DCT", F8OperState.ANR.getValue())));
    when(ptpResources.findPlug(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(PlugRef.builder()
        .setNeId(NE_ID)
        .setAid("")
        .setUri("")
        .setType("CFP2-448G-#DCTCG-SM-LC")
        .build())
      );
    when(ctpResources.findCtpExtended(NE_ID, new Uri(ctpUri)))
      .thenReturn(Optional.empty());

    var servicePathNe = new CromaEcServicePathDTO();
    servicePathNe.setSlc("/mit/me/1/croma/slc/" + slcId);
    servicePathNe.setPortId(ptpNeUri);
    var servicePathNw = new CromaEcServicePathDTO();
    servicePathNw.setPortId(ptpNwUri);
    var servicePaths = List.of(servicePathNe, servicePathNw);
    var sep = new CromaEcServiceEndpointDTO();
    sep.setNeID(NE_ID);
    sep.setServicePathEcDTOS(servicePaths);
    when(cromaMOService.getServiceEndpoint(NE_ID, EcEntityIndex.getEcEntityIndex(sepUri)))
      .thenReturn(sep);
    var ptpNeRef = new PtpRef(NE_ID, new Aid("Port-1/3/n-e"), new Uri(ptpNeUri), IS);
    when(ptpResources.findPtp(NE_ID, new Uri(ptpNeUri)))
      .thenReturn(Optional.of(ptpNeRef));
    var ptpNwRef = new PtpRef(NE_ID, new Aid(srcAid), new Uri(ptpNwUri), IS);
    when(ptpResources.findPtp(NE_ID, new Uri(ptpNwUri)))
      .thenReturn(Optional.of(ptpNwRef));

    setNetworkElementVersion();

    // When
    var context = segmentCreateRequest.buildContext(request);
    var result = segmentCreateRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    assertTrue(result.deletedSegments().isEmpty());
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
  }

  @Test
  void ingressRoadmWithPluggableLineCardAndOppmHappyPath() throws ObjectInUseException {
    // Given
    var ttpAid = "Port-2/10/n2";
    var srcAid = "Port-2/9/n-e";
    var dstAid = "Port-2/5/n";
    var cardAid = "Module-2/10";
    var cardUri = "/mit/me/1/eqh/shelf,2/eqh/slot,10/eq/card";
    var ptpUri = "/mit/me/1/eqh/shelf,2/eqh/slot,10/eq/card/ptp/nw,2";
    var ctpUri = "/mit/me/1/eqh/shelf,2/eqh/slot,10/eq/card/ptp/nw,2/ctp/otuc2";
    var slcId = 1;
    var channel = 194000000;
    var bandwidth = 50000;
    var protectionPathUri = "/mit/me/1/eqh/shelf,2/eqh/slot,9/eq/card/ptp/nw,1-e";

    TtpInfo ttpInfo = WdmSegmentUtil.sampleTtpWithTunableTps(ttpAid, srcAid);
    modelDao.addTtp(NE_ID, ttpInfo);
    modelDao.addLtp(NE_ID, new LtpInfo(dstAid, NON_OPAQUE_SPECTRUM, 2, false));

    setNetworkElementVersion();

    var request = new CrmSegmentRequestDto();
    request.id = REQUEST_ID;
    request.neId = NE_ID;
    request.srcTp = srcAid;
    request.dstTp = dstAid;
    request.forwardSequenceNumber = 1;
    request.reverseSequenceNumber = 1;
    request.setPointDelta = 0.0;
    request.channelSrcLabel = channel;
    request.slotWidthSrcLabel = bandwidth;
    request.portParams = new ArrayList<>();
    request.portParams.add(new SelectedParameter(ParameterName.GLQ, new Value.Enum("OTUC2")));
    request.portParams.add(new SelectedParameter(ParameterName.SETPOINT, new Value.Numeric(BigDecimal.valueOf(-5.9), "dBm")));
    request.portParams.add(new SelectedParameter(ParameterName.MODULATION, new Value.Enum("8QAM")));
    request.portParams.add(new SelectedParameter(ParameterName.FEC, new Value.Enum("OFEC")));
    request.portParams.add(new SelectedParameter(ParameterName.BPS, new Value.Numeric(BigDecimal.valueOf(3))));
    request.portParams.add(new SelectedParameter(ParameterName.CHANNEL_BANDWIDTH, new Value.Numeric(BigDecimal.valueOf(50000), "MHz")));
    request.portParams.add(new SelectedParameter(ParameterName.CHANNEL, new Value.Numeric(new BigDecimal(channel), "MHz")));
    when(ptpResources.findPtp(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(new PtpRef(NE_ID, new Aid(ttpAid), new Uri(ptpUri), IS)));
    when(ptpResources.findCard(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(new CardRef(NE_ID, new Aid(cardAid), new Uri(cardUri), "OF-2D16DCT", F8OperState.ANR.getValue())));
    when(ptpResources.findPlug(NE_ID, new Aid(ttpAid)))
      .thenReturn(Optional.of(PlugRef.builder()
        .setNeId(NE_ID)
        .setAid("")
        .setUri("")
        .setType("CFP2-448G-#DCTCG-SM-LC")
        .build())
      );
    when(ctpResources.findCtpExtended(NE_ID, new Uri(ctpUri)))
      .thenReturn(Optional.empty());
    when(provision.createSlc(NE_ID, new SlcExpressConfiguration(
      List.of(bandwidth),
      List.of(channel),
      new SlcEndpointConfiguration(1, "/mit/me/1/croma/degree/2", 0, null),
      new SlcEndpointConfiguration(1, ctpUri, 0, protectionPathUri)
    ))).thenReturn(slcId);
    when(ptpResources.findPtp(NE_ID, new Aid(srcAid)))
      .thenReturn(Optional.of(new PtpRef(NE_ID, null, new Uri(protectionPathUri), IS)));

    // When
    var context = segmentCreateRequest.buildContext(request);
    var result = segmentCreateRequest.execute(request, context);

    // Then
    assertTrue(result.operationCompleted());
    assertTrue(result.deletedSegments().isEmpty());
    verify(provision).updateAdminState(NE_ID_WRAP, ptpUri, AdminState.DOWN);
    verify(provision).updatePtp(NE_ID_WRAP, ptpUri, Set.of(
      new MoCimParameter("opt", "tuned-frequency", new CimValue.Int(channel)),
      new MoCimParameter("opt", "opt-setpoint", new CimValue.Int(-59)))
    );
    verify(provision).updateAdminState(NE_ID_WRAP, ptpUri, AdminState.UP);
    verify(provision).provisionCtp(NE_ID_WRAP, ctpUri, Set.of(
      new MoCimParameter("#flexo2=ifnum=1", "fec-type", new CimValue.Enum("ofec")),
      new MoCimParameter("otsia/#otsi=id=1/ochcfg", "modulation", new CimValue.Enum("8qam"))
    ));
    var segment = repository.getSegment(REQUEST_ID, NE_ID);
    assertNotNull(segment);
    assertEquals(slcId, segment.getSlcId());
    assertEquals(CrmNodePosition.INGRESS, segment.getNodePosition());
    assertEquals(srcAid, segment.getAidSrcTp());
    assertEquals(dstAid, segment.getAidDstTp());
    assertEquals(ttpAid, segment.getTtpAid());
    assertEquals(ctpUri, segment.getTransponderCtpUri());
  }

  private void setNetworkElementVersion() {
    when(neDataProvider.getNeData(NE_ID)).thenReturn(new NEData(NE_ID, 0, null, null, null, SW_VERSION));
  }
}
