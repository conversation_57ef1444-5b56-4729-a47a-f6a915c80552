/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: bkaminsk
 */
package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;

import static com.adva.nlms.resource.mediator.f8.events.ProvisionEventsCreator.epteModify;

class EpteRequest implements Request {

  private static final Logger log = LogManager.getLogger(EpteRequest.class);

  private final int neId;
  private final String epteUri;
  private final String protectiongroupUri;
  private final Provision provisionApi;

  public EpteRequest(int neId, String epteUri, String protectiongroupUri, Provision provisionApi) {
    this.neId = neId;
    this.epteUri = epteUri;
    this.protectiongroupUri = protectiongroupUri;
    this.provisionApi = provisionApi;
  }

  @Override
  public ProvisionLocalEvent provision() {
    log.info("Provision epte {} on protection group {}", epteUri, protectiongroupUri);
    if (provisionApi.setEpteOnFirstAvailableEpteOnProtectionGroup(neId, protectiongroupUri, epteUri)) {
      return epteModify(protectiongroupUri, epteUri);
    }
    return null;
  }

  @Override
  public void delete() {
    log.info("Clearing epte {} on protection group {}", epteUri, protectiongroupUri);
    provisionApi.clearEpte(neId, protectiongroupUri, epteUri);
  }

  @Override
  public ProvisionLocalEvent adopt() {
    log.info("Adopt, leaving epte {} as it is on protection group {}", epteUri, protectiongroupUri);
    return null;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    EpteRequest that = (EpteRequest) o;
    return neId == that.neId && Objects.equals(epteUri, that.epteUri) && Objects.equals(protectiongroupUri, that.protectiongroupUri) && Objects.equals(provisionApi, that.provisionApi);
  }

  @Override
  public int hashCode() {
    return Objects.hash(neId, epteUri, protectiongroupUri, provisionApi);
  }

  @Override
  public String toString() {
    return "EpteRequest{" +
      "neId=" + neId +
      ", epteUri='" + epteUri + '\'' +
      ", protectiongroupUri='" + protectiongroupUri + '\'' +
      ", provisionApi=" + provisionApi +
      '}';
  }
}
