/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */
package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;
import com.adva.nlms.resource.mediator.f8.ProtectionGroupFinder;
import com.adva.nlms.resource.mediator.f8.internalpath.CrossConnect;
import com.adva.nlms.resource.mediator.f8.internalpath.ForkPlacement;
import com.adva.nlms.resource.mediator.f8.otn.api.in.ProtectionData;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegmentRepository;
import com.adva.nlms.resource.mediator.f8.otn.api.in.SegmentID;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

class OtnResourceRequestMediatorImpl implements OtnResourceRequestMediator {
  private final Logger log = LogManager.getLogger(OtnResourceRequestMediatorImpl.class);

  private final OtnCimProvisionRequestParametersCreatorImpl cimProvisionRequestParametersCreator;
  private final Provision provisionApi;
  private final RequestExecutor requestExecutor;
  private final OtnSegmentRepository otnSegmentRepository;
  private final RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade;
  private final RrmOtnCardCapabilities rrmOtnCardCapabilities;
  private final ProtectionGroupFinder protectionGroupFinder;
  private final MoCapabilityProvider moCapabilityProvider;

  public OtnResourceRequestMediatorImpl(OtnCimProvisionRequestParametersCreatorImpl cimProvisionRequestParametersCreator,
                                        Provision provisionApi,
                                        RequestExecutor requestExecutor,
                                        OtnSegmentRepository otnSegmentRepository,
                                        RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade,
                                        RrmOtnCardCapabilities rrmOtnCardCapabilities,
                                        ProtectionGroupFinder protectionGroupFinder,
                                        MoCapabilityProvider moCapabilityProvider) {
    this.cimProvisionRequestParametersCreator = cimProvisionRequestParametersCreator;
    this.provisionApi = provisionApi;
    this.requestExecutor = requestExecutor;
    this.otnSegmentRepository = otnSegmentRepository;
    this.rrmOtnDbResourcesFacade = rrmOtnDbResourcesFacade;
    this.rrmOtnCardCapabilities = rrmOtnCardCapabilities;
    this.protectionGroupFinder = protectionGroupFinder;
    this.moCapabilityProvider = moCapabilityProvider;
  }

  @Override
  public void handleRequest(CrmSegmentRequestDto request) {
    int neId = request.neId;
    if (request.hasAssignRoles()) {
      log.info("handling of PROTECTION GROUP request: neId={}, assign roles={}", neId, request.getAssignRoles());
      var protectionGroupRequest = new PrepareProtectionGroupRequest(neId, request.getAssignRoles(), provisionApi, otnSegmentRepository, rrmOtnDbResourcesFacade, rrmOtnCardCapabilities).prepareRequest();
      requestExecutor.createProtectionGroup(protectionGroupRequest);
      log.info("PROTECTION GROUP request done");
    } else {
      log.info("handling of {} Segment request for OTN has begun: neId={}, segReqId={}", request.getRequestType(), neId, request.getId());
      switch (request.getRequestType()) {
        case CREATE -> {
          var requests = prepareProvisionRequests(neId, request);
          var protectionData = findProtectionDataInRequest(request);
          if (protectionData.isPresent()) {
            requestExecutor.createOrAdopt(requests, neId, request.getId(), protectionData.get());
          } else {
            requestExecutor.createOrAdopt(requests, neId, request.getId(), null);
          }
        }
        case DELETE -> {
          var deprovisionEvents = new PrepareDeprovisionEvents(
            otnSegmentRepository, rrmOtnDbResourcesFacade, rrmOtnCardCapabilities)
            .prepareEvents(neId, request.getId());
          requestExecutor.delete(neId, request.getId(), deprovisionEvents);
        }
        case ADOPT -> {
          var requests = prepareProvisionRequests(neId, request);
          var protectionData = findProtectionDataInRequest(request);
          if (protectionData.isPresent()) {
            requestExecutor.adopt(requests, neId, request.getId(), protectionData.get());
          } else {
            requestExecutor.adopt(requests, neId, request.getId(), null);
          }
        }
        case ABANDON -> requestExecutor.abandon(neId, request.getId());
        case SETADMINSTATE -> handleAdminStateRequest();
        default ->
          throw new OtnProvisioningException(String.format("Unsupported Request Type: %s", request.getRequestType().toString()));
      }
    }
  }

  @Override
  public boolean isSegmentRequestSupported(CrmSegmentRequestDto request) {
    if (request.hasAssignRoles()) {
      return otnSegmentRepository.exists(new SegmentID(request.neId, request.assignRoles.workingLegId()));
    } else if (request.internalPath != null) {
      return true;
    } else {
      return otnSegmentRepository.exists(new SegmentID(request.neId, request.getId()));
    }
  }

  @Override
  public void clearSegmentCache(int neId) {
    otnSegmentRepository.deleteSegments(neId);
    log.info("Removed OtnSegments on ne {}", neId);
  }

  private List<Request> prepareProvisionRequests(int neId, CrmSegmentRequestDto request) {
    var prepareRequest = new PrepareProvisionRequest(
      cimProvisionRequestParametersCreator,
      provisionApi,
      rrmOtnCardCapabilities,
      rrmOtnDbResourcesFacade,
      otnSegmentRepository,
      protectionGroupFinder,
      moCapabilityProvider
    );
    return prepareRequest.prepareRequest(neId, request);
  }

  private void handleAdminStateRequest() {
    // Enabling/Disabling of AlarmsForwarding feature is not supported in F8 currently
    // This method implementation needs to be changed if F8 supports MAINTENANCE admin state on SLCs.
    // To conform to ENC-NI API, the request to handle the AdminState is a No-Operation
  }

  private static Optional<ProtectionData> findProtectionDataInRequest(CrmSegmentRequestDto request) {
    if (request.getInternalPath().clientProtection() != null) {
      return Optional.of(new ProtectionData(request.getInternalPath().clientProtection().port()));
    }
    var forkPlacement = findForkPlacementInRequest(request);
    if (forkPlacement.isPresent() && !forkPlacement.get().equals(ForkPlacement.NONE)) {
      return Optional.of(new ProtectionData(forkPlacement.get()));
    }
    return Optional.empty();
  }

  private static Optional<ForkPlacement> findForkPlacementInRequest(CrmSegmentRequestDto request) {
    return request.getInternalPath().connections().stream()
      .flatMap(connection -> connection.subConnections().stream()
        .map(subConnection -> {
          if (subConnection instanceof CrossConnect crossConnect) {
            return crossConnect.fork();
          }
          return null;
        }))
      .filter(Objects::nonNull)
      .findFirst();
  }
}
