/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm.adapters.messaging;

import com.adva.nlms.mediation.ec.support.EcEntityIndex;
import com.adva.nlms.mediation.mo.inventory.f8.events.AGATESDoneResultType;
import com.adva.nlms.mediation.mo.inventory.f8.events.AGATESNotification;
import com.adva.nlms.resource.mediator.f8.wdm.adapters.messaging.AgatesParametersMapper;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.AgatesDone;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.AgatesParameters;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.AgatesPending;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.AgatesTrigger;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class AgatesParametersMapperTest {

    private static final int NE_ID = 7;
    private static final String OMS = "1/6/n/oms";
    private static final int SLC_ID = 16;

    @Test
    void testVerifyAndCollectParameters_Pending() {
        // Given
        AGATESNotification agatesNotification = new AGATESNotification(NE_ID);
        agatesNotification.withEntityName("node 1 interface %s/spslg-1/otsia".formatted(OMS));
        agatesNotification.withSlcIndex(EcEntityIndex.getEcEntityIndex("/mit/me/1/croma/slc/%d".formatted(SLC_ID)));
        agatesNotification.belongsToAEnd(true);

        // When
        AgatesParameters agatesParameters = AgatesParametersMapper.verifyAndCollectParameters(agatesNotification);

        // Then
        assertNotNull(agatesParameters);
        assertEquals(AgatesTrigger.PENDING, agatesParameters.trigger());
        AgatesPending pending = agatesParameters.agatesPending();
        assertNotNull(pending);
        assertEquals(NE_ID, pending.neId());
        assertEquals(OMS, pending.omsCtp());
        assertEquals(SLC_ID, pending.slcId());
        assertTrue(pending.isSlcAEnd());
    }

    @Test
    void testVerifyAndCollectParameters_Done() {
        // Given
        AGATESNotification agatesNotification = new AGATESNotification(NE_ID, AGATESDoneResultType.Success);
        agatesNotification.withEntityName("node 1 interface %s oms degree-span-equalization".formatted(OMS));

        // When
        AgatesParameters agatesParameters = AgatesParametersMapper.verifyAndCollectParameters(agatesNotification);

        // Then
        assertNotNull(agatesParameters);
        assertEquals(AgatesTrigger.DONE, agatesParameters.trigger());
        AgatesDone agatesDone = agatesParameters.agatesDone();
        assertNotNull(agatesDone);
        assertEquals(NE_ID, agatesDone.neId());
        assertEquals(OMS, agatesDone.omsCtp());
    }

    @Test
    void testVerifyAndCollectParameters_InvalidEntityName() {
        // Given
        AGATESNotification agatesNotification = new AGATESNotification(NE_ID);
        agatesNotification.withEntityName("invalid entity name");

        // When - Then
        assertThrows(IllegalArgumentException.class, () -> AgatesParametersMapper.verifyAndCollectParameters(agatesNotification));
    }

    @Test
    void testVerifyAndCollectParameters_NullEntityName() {
        // Given
        AGATESNotification agatesNotification = new AGATESNotification(NE_ID);

        // When - Then
        assertThrows(NullPointerException.class, () -> AgatesParametersMapper.verifyAndCollectParameters(agatesNotification));
    }
}