/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.mediation.config.f8.croma.api.CromaMOService;
import com.adva.nlms.mediation.config.f8.croma.slc.api.Slc;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Optional;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

public class ProvisionSlcTest {

  private static final int SLC_ID = 1;
  private static final int NE_ID = 123;

  private final Provision provision = Mockito.mock(Provision.class);
  private final CromaMOService cromaMOService = Mockito.mock(CromaMOService.class);
  private final PtpResources ptpResources = Mockito.mock(PtpResources.class);

  private final ProvisionSlc sut = new ProvisionSlc(provision, cromaMOService, ptpResources);

  @Test
  public void test_adoptPathNodeNumbers_noChange() {
    //given
    int aEndPathNodeNumber = 1;
    int zEndPathNodeNumber = 2;
    Slc slc = createSlc(aEndPathNodeNumber, zEndPathNodeNumber);
    Mockito.when(cromaMOService.getSlcForNeAndSlcId(NE_ID, SLC_ID))
      .thenReturn(Optional.of(slc));
    try {
      // when
      ProvisionSlc.SlcPathNodeNumber slcPathNodeNumber = new ProvisionSlc.SlcPathNodeNumber(aEndPathNodeNumber, zEndPathNodeNumber);
      sut.adoptPathNodeNumbers(NE_ID, SLC_ID, slcPathNodeNumber);
      // then
      verifyNoInteractions(provision);
    } catch (Exception e) {
      Assertions.fail(e.getMessage());
    }
  }

  @Test
  public void test_adoptPathNodeNumbers_aEndSlcChange() {
    //given
    int aEndPathNodeNumberCurrent = 1;
    int zEndPathNodeNumberCurrent = 2;
    Slc slc = createSlc(aEndPathNodeNumberCurrent, zEndPathNodeNumberCurrent);
    Mockito.when(cromaMOService.getSlcForNeAndSlcId(NE_ID, SLC_ID))
      .thenReturn(Optional.of(slc));
    try {
      // when
      int aEndPathNodeNumberNew = 5;
      ProvisionSlc.SlcPathNodeNumber slcPathNodeNumber = new ProvisionSlc.SlcPathNodeNumber(aEndPathNodeNumberNew, zEndPathNodeNumberCurrent);
      sut.adoptPathNodeNumbers(NE_ID, SLC_ID, slcPathNodeNumber);
      // then
      verify(provision).modifySlcPathNodeNumber(NE_ID, SLC_ID, aEndPathNodeNumberNew, null);
    } catch (Exception e) {
      Assertions.fail(e.getMessage());
    }
  }

  @Test
  public void test_adoptPathNodeNumbers_zEndSlcChange() {
    //given
    int aEndPathNodeNumberCurrent = 1;
    int zEndPathNodeNumberCurrent = 2;
    Slc slc = createSlc(aEndPathNodeNumberCurrent, zEndPathNodeNumberCurrent);
    Mockito.when(cromaMOService.getSlcForNeAndSlcId(NE_ID, SLC_ID))
      .thenReturn(Optional.of(slc));
    try {
      // when
      int zEndPathNodeNumberNew = 5;
      ProvisionSlc.SlcPathNodeNumber slcPathNodeNumber = new ProvisionSlc.SlcPathNodeNumber(aEndPathNodeNumberCurrent, zEndPathNodeNumberNew);
      sut.adoptPathNodeNumbers(NE_ID, SLC_ID, slcPathNodeNumber);
      // then
      verify(provision).modifySlcPathNodeNumber(NE_ID, SLC_ID, null, zEndPathNodeNumberNew);
    } catch (Exception e) {
      Assertions.fail(e.getMessage());
    }
  }


  private Slc createSlc(int aEndPathNodeNumber, int zEndPathNodeNumber) {
    return SlcTestBuilder.newBuilder()
      .withNeId(NE_ID)
      .withIdentifier(SLC_ID)
      .aEndpoint()
      .withPathNodeNumber(aEndPathNodeNumber)
      .activeEndpoint()
      .buildActiveEndpoint()
      .buildEndpoint()
      .zEndpoint()
      .withPathNodeNumber(zEndPathNodeNumber)
      .activeEndpoint()
      .buildActiveEndpoint()
      .buildEndpoint()
      .build();
  }

//  @org.junit.Test
//  public void test_modifySlcPathNodeNumber_aEndSlcChange() {
//    //given
//    // existing SLC data found in mock SLC repository
//    int neId = 858;
//    int slcId = 1;
//    int zEndPathNodeNumberCurrent = 2;
//    int aEndPathNodeNumberNew = 5;
//    try {
//      // when
//      sut.modifySlcPathNodeNumber(neId, slcId, aEndPathNodeNumberNew, zEndPathNodeNumberCurrent);
//      // then
//      verify(slcProvisionRestClient).modifySlcPathNodeNumber(neId, slcId, aEndPathNodeNumberNew, null);
//    } catch (Exception e) {
//      Assert.fail(e.getMessage());
//    }
//  }
//
//  @org.junit.Test
//  public void test_modifySlcPathNodeNumber_zEndSlcChange() {
//    //given
//    // existing SLC data found in mock SLC repository
//    int neId = 858;
//    int slcId = 1;
//    int aEndPathNodeNumberCurrent = 1;
//    int zEndPathNodeNumberNew = 5;
//    try {
//      // when
//      sut.modifySlcPathNodeNumber(neId, slcId, aEndPathNodeNumberCurrent, zEndPathNodeNumberNew);
//      // then
//      verify(slcProvisionRestClient).modifySlcPathNodeNumber(neId, slcId, null, zEndPathNodeNumberNew);
//    } catch (Exception e) {
//      Assert.fail(e.getMessage());
//    }
//  }
}