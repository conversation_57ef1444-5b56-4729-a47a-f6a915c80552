/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: zbigniewj
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;

class LayerQualifierToCimString {
  private LayerQualifierToCimString() {
  }

  static String convert(LayerQualifier in) {
    switch (in) {
      case ODU0, ODU1, ODU2, ODU3, ODU4, ODU2E -> {
        return in.toString().toLowerCase();
      }
      case ODUFLEX -> {
        return "oduf";
      }
      default -> {
        return "";
      }
    }
  }
}
