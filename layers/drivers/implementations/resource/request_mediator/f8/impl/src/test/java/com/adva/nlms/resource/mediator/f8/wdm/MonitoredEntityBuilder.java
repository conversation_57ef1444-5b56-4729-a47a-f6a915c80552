/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.mediator.f8.wdm;

public class MonitoredEntityBuilder {
  private String segmentRequestId;
  String entityUri;
  String txAid;
  String rxAid;
  MonitoredEntity.EquipmentType equipmentType;

  private MonitoredEntityBuilder() {
  }
  public static MonitoredEntityBuilder newBuilder() {
    return new MonitoredEntityBuilder();
  }

  MonitoredEntityBuilder segmentRequestId(String segmentRequestId) {
    this.segmentRequestId = segmentRequestId;
    return this;
  }
  MonitoredEntityBuilder entityUri(String entityAid) {
    this.entityUri = entityAid;
    return this;
  }
  MonitoredEntityBuilder txAid(String txAid) {
    this.txAid = txAid;
    return this;
  }
  MonitoredEntityBuilder rxAid(String rxAid) {
    this.rxAid = rxAid;
    return this;
  }
  MonitoredEntityBuilder equipmentType(MonitoredEntity.EquipmentType equipmentType) {
    this.equipmentType = equipmentType;
    return this;
  }
  public MonitoredEntity build() {
    return new MonitoredEntity(segmentRequestId, entityUri, MonitoredEntity.TerminationPointPair.of(txAid, rxAid), equipmentType);
  }
}
