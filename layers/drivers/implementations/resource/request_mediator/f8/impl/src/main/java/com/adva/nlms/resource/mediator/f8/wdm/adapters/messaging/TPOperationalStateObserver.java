/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm.adapters.messaging;

import com.adva.nlms.inf.api.DestinationArea;
import com.adva.nlms.inf.api.ObjectInterfaceSelector;
import com.adva.nlms.inf.api.notification.InternalNotificationObserverException;
import com.adva.nlms.inf.api.notification.NotificationManager;
import com.adva.nlms.inf.api.notification.observer.NotificationObserver;
import com.adva.nlms.inf.api.notification.selector.NotificationHeaderSelectorInjector;
import com.adva.nlms.inf.mo.api.notification.MONotification;
import com.adva.nlms.inf.mo.api.notification.MOUpdateNotification;
import com.adva.nlms.inf.mo.api.notification.selector.CtpF8EcSelector;
import com.adva.nlms.inf.mo.api.notification.selector.PhysicalTerminationPointEcSelector;
import com.adva.nlms.mediation.config.EntityAttributes;
import com.adva.nlms.mediation.config.ec.entity.facility.ctp.CtpF8Attributes;
import com.adva.nlms.mediation.ec.support.EcEntityIndex;
import com.adva.nlms.resource.advertisement.api.out.CrmToCpcConnection;
import com.adva.nlms.resource.mediator.f8.wdm.WdmResourceRequestMediator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Objects;

class TPOperationalStateObserver implements NotificationObserver<MONotification>, NotificationHeaderSelectorInjector {

  private static final Logger log = LogManager.getLogger(TPOperationalStateObserver.class);

  private final CrmToCpcConnection crmToCpcConnection;
  private final WdmResourceRequestMediator wdmResourceRequestMediator;

  TPOperationalStateObserver(CrmToCpcConnection crmToCpcConnection, WdmResourceRequestMediator wdmResourceRequestMediator) {
    this.crmToCpcConnection = crmToCpcConnection;
    this.wdmResourceRequestMediator = wdmResourceRequestMediator;
  }

  @Override
  public void handle(MONotification notification) {
    if (canHandle(notification)) {
      log.debug("[RRM] Start handle TP operational state notification {}", notification);
      wdmResourceRequestMediator.handleOperationalStateChange(notification.getNeId(), EcEntityIndex.getEcUri(notification.getEntityIndex()));
    }
  }

  private boolean canHandle(MONotification notification) {
    if (!crmToCpcConnection.isConnectionOpenForNE(notification.getNeId())) {
      return false;
    }
    if (notification instanceof MOUpdateNotification updateNotification) {
      return operationalStateChanged(updateNotification) || adminStateChanged(updateNotification);
    } else {
      return false;
    }
  }

  private boolean operationalStateChanged(MOUpdateNotification updateNotification) {
    return !Objects.equals(updateNotification.getOldValue(CtpF8Attributes.ORIGINAL_OPERATIONAL_STATE),
                           updateNotification.getValue(CtpF8Attributes.ORIGINAL_OPERATIONAL_STATE));
  }

  private boolean adminStateChanged(MOUpdateNotification updateNotification) {
    return !Objects.equals(updateNotification.getOldValue(EntityAttributes.ADMIN_STATE),
                           updateNotification.getValue(EntityAttributes.ADMIN_STATE));
  }

  @Override
  public String getObserverID() {
    return this.getClass().getName();
  }

  @Override
  public DestinationArea getDestination() {
    return DestinationArea.SM;
  }

  @Override
  public void injectSelectors(NotificationManager notificationManager) throws InternalNotificationObserverException {
    notificationManager.addSelector(this, new ObjectInterfaceSelector(CtpF8EcSelector.class));
    notificationManager.addSelector(this, new ObjectInterfaceSelector(PhysicalTerminationPointEcSelector.class));
  }

}
