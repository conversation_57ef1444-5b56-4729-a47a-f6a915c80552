/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.eth;

import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.resource.mediator.f8.events.ProvisionLocalEvent;
import com.adva.nlms.resource.provision.f8.api.in.ObjectDoesNotExistException;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Collections;
import java.util.Objects;
import java.util.Set;

import static com.adva.nlms.resource.mediator.f8.events.ProvisionEventsCreator.sncAdopted;
import static com.adva.nlms.resource.mediator.f8.events.ProvisionEventsCreator.sncCreated;

class SncRequest implements Request {
  private static final Logger log = LogManager.getLogger(SncRequest.class);
  private final int neId;
  private final String uri;
  private final boolean exists;
  // support of protection is not planned however we use set to facilitate communication with services
  private final Set<String> aEndpointUri;
  private final Set<String> zEndpointUri;
  private final Provision provisionApi;

  SncRequest(int neId, String uri, boolean exists, String aEndpointUri, String zEndpointUri, Provision provisionApi) {
    this.neId = neId;
    this.uri = uri;
    this.exists = exists;
    this.aEndpointUri = Collections.singleton(aEndpointUri);
    this.zEndpointUri = Collections.singleton(zEndpointUri);
    this.provisionApi = provisionApi;
  }

  @Override
  public ProvisionLocalEvent provision() {
    log.info("[ETH] Provisioning  SNC {}", uri);
    if (exists) {
      return sncAdopted(uri, aEndpointUri, zEndpointUri);
    } else {
      int sncNumber = provisionApi.provisionSnc(NetworkElementID.create(neId), uri, aEndpointUri, zEndpointUri, null);
      return sncCreated(uri + "/" + sncNumber, aEndpointUri, zEndpointUri);
    }
  }

  @Override
  public void delete() {
    log.info("[ETH] Deleting SNC {}", uri);
    try {
      provisionApi.deleteSnc(NetworkElementID.create(neId), uri);
    } catch (ObjectDoesNotExistException e) {
      log.warn("Could not delete SNC on NE: {} with uri {} - entity does not exist", neId, uri);
    }
  }

  @Override
  public ProvisionLocalEvent adopt() {
    if (exists) {
      return sncAdopted(uri, aEndpointUri, zEndpointUri);
    }
    throw new AdoptFailedException("Missing SNC %s".formatted(uri));
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    SncRequest that = (SncRequest) o;
    return neId == that.neId && Objects.equals(uri, that.uri) && Objects.equals(exists, that.exists)
        && Objects.equals(aEndpointUri, that.aEndpointUri) && Objects.equals(zEndpointUri, that.zEndpointUri);
  }

  @Override
  public int hashCode() {
    return Objects.hash(neId, uri, exists, aEndpointUri, zEndpointUri);
  }

  @Override
  public String toString() {
    return "SncRequest{" +
        "neId=" + neId +
        ", uri='" + uri + '\'' +
        ", exists=" + exists +
        ", aEndpointUri=" + aEndpointUri +
        ", zEndpointUri=" + zEndpointUri +
        '}';
  }
}
