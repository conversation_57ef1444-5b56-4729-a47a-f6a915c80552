/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.mediator.f8.wdm.api.in;

import com.adva.nlms.mediation.infrastructure.notification.tracing.api.in.TraceDefinitions;
import com.adva.nlms.resource.mediator.f8.PowerEqOperation;
import org.slf4j.MDC;

public class SegmentPowerEqualization {

  private EnvelopeIncomingAddresses envelopeIncomingAddresses;

  private long aEndEqInvocationTime = -1;  // Epoch time at which SLC equalization in A-->Z direction was invoked from CRM
  private long zEndEqInvocationTime = -1;  // Epoch time at which SLC equalization in Z-->A direction was invoked from CRM
  private boolean isAendEqExpected = false;
  private boolean isZendEqExpected = false;
  private volatile String correlationId;

  // Span Equalization
  private String agatesInterfaceAEnd = "";
  private String agatesInterfaceZEnd = "";

  // Transponder Equalization
  private PowerEqOperation waitingForLaserOnDelayClear = null;

  public SegmentPowerEqualization() {
  }

  public synchronized EnvelopeIncomingAddresses getEnvelopeIncomingAddresses() {
    return envelopeIncomingAddresses;
  }

  public synchronized void setEnvelopeIncomingAddresses(EnvelopeIncomingAddresses envelopeIncomingAddresses) {
    this.envelopeIncomingAddresses = envelopeIncomingAddresses;
  }

  public synchronized boolean isLatestNotification(long eqTime, boolean isAEnd) {
    if (isAEnd) {
      return eqTime > aEndEqInvocationTime;
    } else {
      return eqTime > zEndEqInvocationTime;
    }
  }

  public synchronized void setEqExpected(long eqTime, boolean isAEnd) {
    if (isAEnd) {
      isAendEqExpected = true;
      aEndEqInvocationTime = eqTime;
    } else {
      isZendEqExpected = true;
      zEndEqInvocationTime = eqTime;
    }
  }

  public synchronized void unsetEqExpected(boolean isAEnd) {
    if (isAEnd) {
      isAendEqExpected = false;
    } else {
      isZendEqExpected = false;
    }
  }

  public synchronized boolean isEqExpected(boolean isAEnd) {
    return isAEnd ? isAendEqExpected : isZendEqExpected;
  }

  public void setCorrelationIdFromContext() {
    correlationId = MDC.get(TraceDefinitions.CORRELATION_ID_LOG_VAR_NAME);
  }

  public void putCorrelationIdInContext() {
    MDC.put(TraceDefinitions.CORRELATION_ID_LOG_VAR_NAME, correlationId);
  }

  public synchronized boolean isAgatesInterfaceAEnd(String agatesInterface) {
    return agatesInterfaceAEnd.equalsIgnoreCase(agatesInterface);
  }

  public synchronized boolean isAgatesInterface(String agatesInterface) {
    return agatesInterfaceAEnd.equalsIgnoreCase(agatesInterface) || agatesInterfaceZEnd.equalsIgnoreCase(agatesInterface);
  }

  public synchronized void setAgatesInterface(String agatesInterface, boolean isAEnd) {
    if (isAEnd) {
      agatesInterfaceAEnd = agatesInterface;
    } else {
      agatesInterfaceZEnd = agatesInterface;
    }
  }

  public synchronized PowerEqOperation getWaitingForLaserOnDelayClear() {
    return waitingForLaserOnDelayClear;
  }

  public synchronized void setWaitingForLaserOnDelayClear(PowerEqOperation waitingForLaserOnDelayClear) {
    this.waitingForLaserOnDelayClear = waitingForLaserOnDelayClear;
  }

  public synchronized void clearEqualizationData() {
    envelopeIncomingAddresses = null;
    aEndEqInvocationTime = -1;
    zEndEqInvocationTime = -1;
    isAendEqExpected = false;
    isZendEqExpected = false;
    correlationId = null;
    agatesInterfaceAEnd = "";
    agatesInterfaceZEnd = "";
    waitingForLaserOnDelayClear = null;
  }

  @Override
  public synchronized String toString() {
    return "SegmentPowerEqualization{" +
      "envelopeIncomingAddresses set=" + (envelopeIncomingAddresses != null) +
      ", aEndEqInvocationTime=" + aEndEqInvocationTime +
      ", zEndEqInvocationTime=" + zEndEqInvocationTime +
      ", isAendEqExpected=" + isAendEqExpected +
      ", isZendEqExpected=" + isZendEqExpected +
      ", agatesInterfaceAEnd='" + agatesInterfaceAEnd + '\'' +
      ", agatesInterfaceZEnd='" + agatesInterfaceZEnd + '\'' +
      ", waitingForLaserOnDelayClear=" + waitingForLaserOnDelayClear +
      '}';
  }
}
