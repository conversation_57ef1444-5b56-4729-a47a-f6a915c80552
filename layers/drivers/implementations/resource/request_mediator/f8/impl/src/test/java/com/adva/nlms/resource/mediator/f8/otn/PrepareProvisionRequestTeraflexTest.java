/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: zbigniewj
 */

package com.adva.nlms.resource.mediator.f8.otn;


import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.provider.api.CapabilityProvider;
import com.adva.infrastructure.capabilityprovider.capabilities.CapabilityConfiguration;
import com.adva.infrastructure.capabilityprovider.core.CapabilityProviderConfiguration;
import com.adva.infrastructure.capabilityprovider.mo.MoCapabilityProviderConfiguration;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.Ctp;
import com.adva.nlms.mediation.mo.inventory.resources.CtpBuilder;
import com.adva.nlms.mediation.mo.inventory.resources.CtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.topology.NEData;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.opticalparameters.api.OpticalParameters;
import com.adva.nlms.opticalparameters.api.Value;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.opticalparameters.api.enums.ParameterName;
import com.adva.nlms.opticalparameters.cim.api.CimValue;
import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;
import com.adva.nlms.resource.mediator.f8.InterfaceType;
import com.adva.nlms.resource.mediator.f8.ProtectionGroupFinder;
import com.adva.nlms.resource.mediator.f8.internalpath.Connection;
import com.adva.nlms.resource.mediator.f8.internalpath.Container;
import com.adva.nlms.resource.mediator.f8.internalpath.CrossConnect;
import com.adva.nlms.resource.mediator.f8.internalpath.ForkPlacement;
import com.adva.nlms.resource.mediator.f8.internalpath.InternalPath;
import com.adva.nlms.resource.mediator.f8.internalpath.LabelDTO;
import com.adva.nlms.resource.mediator.f8.internalpath.MultiLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.OduType;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabelType;
import com.adva.nlms.resource.mediator.f8.internalpath.SignalType;
import com.adva.nlms.resource.mediator.f8.internalpath.TerminationPoint;
import com.adva.nlms.resource.mediator.f8.internalpath.Whole;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegmentRepository;
import com.adva.nlms.resource.provision.f8.api.in.MoCimParameter;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class PrepareProvisionRequestTeraflexTest {

  private static final int NETWORK_ELEMENT_ID = 1;
  private static final int CONTAINER_ID = 3;
  private static final String PORT_N_AID = "Port-1/1/n1";
  private static final String PORT_N_OPTICAL_AID = "OT400-1/1/n1/ot400";
  private static final String PORT_N_OPTICAL_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/nw,1/ctp/ot400";
  private static final String PORT_N_CTP_URI = PORT_N_OPTICAL_URI + "/ctp/odu4-" + CONTAINER_ID;

  private static final String PLUG_N_AID = "Plug-1/1/n1";
  private static final String PLUG_N_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/plgh,nw,1";
  private static final String PLUG_N_TYPE = "N-teraflex";

  private static final String PORT_C_AID = "Port-1/1/c3";
  private static final String PORT_C_OPTICAL_AID = "OTU4-1/1/c3/otu4";
  private static final String PORT_C_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/cl,3";
  private static final String PORT_C_OPTICAL_URI = PORT_C_URI + "/ctp/otu4";
  private static final String PORT_C_CTP_URI = PORT_C_OPTICAL_URI + "/ctp/odu4";

  private static final String PLUG_C_AID = "Plug-1/1/c3";
  private static final String PLUG_C_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/plgh,3";

  private static final String CARD_AID = "Card-1/1";
  private static final String MODULE_AID = "Module-1/1";
  private static final String CARD_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card";
  private static final String SNC_URI = CARD_URI + "/sn/odu4/snc";
  private static final String MODULE_TYPE = "T-MP-2D8DCT";
  private static final String PLUG_C_TYPE = "QSFP28-112G-SR4-MM-MPO";
  private static final List<OpticalParameters.SelectedParameter> inParams = List.of(generateGlqOpticalParam());
  private static final List<String> OTU4_LAYERS = List.of("ops-multilink", "otu4");
  private static final List<String> OTUC4PA_LAYERS = List.of("och", "otuc4pa", "oduc4pa");

  private final RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade = mock(RrmOtnDbResourcesFacade.class);
  private final NEDataProvider neDataProvider = mock(NEDataProvider.class);
  private static final CapabilityProvider capabilityProvider = new CapabilityProviderConfiguration()
    .capabilityProvider(new CapabilityConfiguration().capabilityRepository(), null, null);
  private static final MoCapabilityProvider moCapabilityProvider = new MoCapabilityProviderConfiguration().moCapabilityProvider();
  private final RrmOtnCardCapabilities rrmOtnCardCapabilities = new RrmOtnCardCapabilitiesImpl(neDataProvider, capabilityProvider, moCapabilityProvider);
  private final MoCapabilityProvider provider = new MoCapabilityProviderConfiguration().moCapabilityProvider();
  private final OtnCimProvisionRequestParametersCreatorImpl cimProvisionRequestParametersCreator = new OtnCimProvisionRequestParametersCreatorImpl(provider);
  private final Provision provisionApi = mock(Provision.class);
  private final OtnSegmentRepository otnSegmentRepository = mock(OtnSegmentRepository.class);
  private final ProtectionGroupFinder protectionGroupFinder = Mockito.mock(ProtectionGroupFinder.class);

  private final PrepareProvisionRequest sut = new PrepareProvisionRequest(
    cimProvisionRequestParametersCreator, provisionApi, rrmOtnCardCapabilities, rrmOtnDbResourcesFacade, otnSegmentRepository, protectionGroupFinder, moCapabilityProvider);

  private static OpticalParameters.SelectedParameter generateGlqOpticalParam() {
    return new OpticalParameters.SelectedParameter(ParameterName.GLQ, new Value.Enum("OTU4"));
  }

  private static TerminationPoint generateTerminatePoint(String aid, OtnLabelType type) {
    return new TerminationPoint(aid, new LabelDTO(new MultiLabel(List.of(new OtnLabel(type)))), true);
  }

  private static Optional<CardRef> generateCardRef() {
    return Optional.of(new CardRef(NETWORK_ELEMENT_ID, new Aid(CARD_AID), new Uri(CARD_URI), MODULE_TYPE, 0));
  }

  private static PlugRef generatePlugRef_C() {
    return PlugRef.builder()
      .setNeId(NETWORK_ELEMENT_ID)
      .setAid(PLUG_C_AID)
      .setUri(PLUG_C_URI)
      .setType(PLUG_C_TYPE)
      .build();
  }

  private static PlugRef generatePlugRef_N() {
    return PlugRef.builder()
      .setNeId(NETWORK_ELEMENT_ID)
      .setAid(PLUG_N_AID)
      .setUri(PLUG_N_URI)
      .setType(PLUG_N_TYPE)
      .build();
  }

  private static CrmSegmentRequestDto generateCrossConnectRequest(List<OpticalParameters.SelectedParameter> params,
                                                                  String src,
                                                                  String dst,
                                                                  InterfaceType srcInterfaceType,
                                                                  InterfaceType dstInterfaceType) {
    var request = new CrmSegmentRequestDto();
    request.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    request.portParams = params;
    request.srcTp = src;
    request.dstTp = dst;
    request.setSrcInterfaceType(srcInterfaceType);
    request.setDstInterfaceType(dstInterfaceType);
    var srcTtp = generateTerminatePoint(request.srcTp,
      request.srcTp.equals(PORT_N_AID) ? new Container(CONTAINER_ID) : new Whole());
    var dstTtp = generateTerminatePoint(request.dstTp,
      request.dstTp.equals(PORT_N_AID) ? new Container(CONTAINER_ID) : new Whole());


    request.internalPath = new InternalPath(null,
      List.of(new Connection(
        new SignalType(new OduType(LayerQualifier.ODU4, 0)), List.of(
        new CrossConnect(srcTtp, dstTtp, ForkPlacement.NONE)))),
      null);
    return request;
  }

  private static List<MoCimParameter> generateCimParams() {
    return List.of(new MoCimParameter("otu4", "fec-type", new CimValue.Enum("gfec")));
  }

  private CtpRequest generateCtpRequest(String uri, boolean isAdopt, Set<MoCimParameter> params) {
    return new CtpRequest(NETWORK_ELEMENT_ID, uri, isAdopt, false, params, provisionApi);
  }

  void init() {
    when(rrmOtnDbResourcesFacade.findCardFromModule(NETWORK_ELEMENT_ID, new Aid(MODULE_AID)
    )).thenReturn(generateCardRef().get());

    when(neDataProvider.getNeData(anyInt()
    )).thenReturn(new NEData(NETWORK_ELEMENT_ID, 0,
      "FSP 3000C", null, null, "6.5.1")
    );

    when(rrmOtnDbResourcesFacade.findCardFromPlug(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PLUG_C_AID))
    )).thenReturn(
      generateCardRef()
    );

    when(rrmOtnDbResourcesFacade.findPlug(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PLUG_C_AID))
    )).thenReturn(
      Optional.of(generatePlugRef_C())
    );

    when(rrmOtnDbResourcesFacade.findCardFromPlug(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PLUG_N_AID))
    )).thenReturn(
      null
    );

    when(rrmOtnDbResourcesFacade.findPlug(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PLUG_N_AID))
    )).thenReturn(
      Optional.empty()
    );

    when(rrmOtnDbResourcesFacade.findPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C_AID)))
    ).thenReturn(
      Optional.of(new PtpRef(NETWORK_ELEMENT_ID, null, null, null))
    );

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      List.of(createCtp(new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OPTICAL_URI), OTUC4PA_LAYERS))
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C_OPTICAL_URI))
    )).thenReturn(
      Optional.of(createCtp(new Aid(PORT_C_OPTICAL_AID), new Uri(PORT_C_OPTICAL_URI), OTU4_LAYERS))
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OPTICAL_URI))
    )).thenReturn(
      Optional.of(createCtp(new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OPTICAL_URI), OTUC4PA_LAYERS))
    );

    when(rrmOtnDbResourcesFacade.findCtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_CTP_URI))
    )).thenReturn(Optional.of(new CtpRef(NETWORK_ELEMENT_ID, null, null, null)));

    when(rrmOtnDbResourcesFacade.findCardFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C_AID))
    )).thenReturn(
      generateCardRef()
    );

    when(rrmOtnDbResourcesFacade.findCardFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      generateCardRef()
    );

    when(rrmOtnDbResourcesFacade.findPlugFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C_AID))
    )).thenReturn(
      Optional.of(generatePlugRef_C())
    );

    when(rrmOtnDbResourcesFacade.findPlugFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      Optional.of(generatePlugRef_N())
    );
  }

  private static Ctp createCtp(Aid ctpAid, Uri ctpUri, List<String> layerRate) {
    return CtpBuilder.newBuilder()
      .withNeId(PrepareProvisionRequestTeraflexTest.NETWORK_ELEMENT_ID)
      .withAid(ctpAid)
      .withUri(ctpUri)
      .withLayerRate(layerRate)
      .withTributaryPort(0)
      .build();
  }

  @Test
  void ingressPrepareRequestNoOpticalParams() {
    init();

    when(rrmOtnDbResourcesFacade.findPlug(eq(NETWORK_ELEMENT_ID), eq(new Aid(PLUG_C_AID)))).thenReturn(Optional.of(generatePlugRef_C()));
    when(rrmOtnDbResourcesFacade.findCardFromPlug(eq(NETWORK_ELEMENT_ID), eq(new Aid(PLUG_C_AID)))).thenReturn(generateCardRef());

    var request = generateCrossConnectRequest(inParams, PORT_C_AID, PORT_N_AID, InterfaceType.ENNI, InterfaceType.UNSPECIFIED);

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(5, result.size());
      assertInstanceOf(CtpRequest.class, result.get(0));
      assertEquals(generateCtpRequest(PORT_C_OPTICAL_URI, false, Set.of()), result.get(0));
      assertInstanceOf(PtpParamsRequest.class, result.get(1));
      assertEquals(new PtpParamsRequest(NETWORK_ELEMENT_ID, PORT_C_URI, true, Set.of(), provisionApi), result.get(1));
      assertInstanceOf(CtpRequest.class, result.get(2));
      assertEquals(generateCtpRequest(PORT_C_CTP_URI, false, Set.of()), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI, true, Set.of()), result.get(3));
      assertInstanceOf(SncRequest.class, result.get(4));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, SNC_URI, false,
        Set.of(PORT_C_CTP_URI),
        Set.of(PORT_N_CTP_URI),
        provisionApi), result.get(4));
    } catch (Exception e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void ingressPrepareRequest() {
    init();

    when(rrmOtnDbResourcesFacade.findPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C_AID))
    )).thenReturn(Optional.of(new PtpRef(NETWORK_ELEMENT_ID, null, null, null)));

    when(rrmOtnDbResourcesFacade.findPlug(eq(NETWORK_ELEMENT_ID), eq(new Aid(PLUG_C_AID)))).thenReturn(Optional.of(generatePlugRef_C()));
    when(rrmOtnDbResourcesFacade.findCardFromPlug(eq(NETWORK_ELEMENT_ID), eq(new Aid(PLUG_C_AID)))).thenReturn(generateCardRef());

    var moParams = generateCimParams();
    List<OpticalParameters.SelectedParameter> params = new ArrayList<>();
    params.add(new OpticalParameters.SelectedParameter(ParameterName.FEC, new Value.Enum("GFEC")));
    params.addAll(inParams);
    var request = generateCrossConnectRequest(params, PORT_C_AID, PORT_N_AID, InterfaceType.ENNI, InterfaceType.UNSPECIFIED);

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(5, result.size());
      assertInstanceOf(CtpRequest.class, result.get(0));
      assertEquals(generateCtpRequest(PORT_C_OPTICAL_URI, false, new HashSet<>(moParams)), result.get(0));
      assertInstanceOf(PtpParamsRequest.class, result.get(1));
      assertEquals(new PtpParamsRequest(NETWORK_ELEMENT_ID, PORT_C_URI, true, Set.of(), provisionApi), result.get(1));
      assertInstanceOf(CtpRequest.class, result.get(2));
      assertEquals(generateCtpRequest(PORT_C_CTP_URI, false, Set.of()), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI, true, Set.of()), result.get(3));
      assertInstanceOf(SncRequest.class, result.get(4));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, SNC_URI, false,
        Set.of(PORT_C_CTP_URI),
        Set.of(PORT_N_CTP_URI),
        provisionApi), result.get(4));
    } catch (Exception e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void ingressPrepareRequestNoClientCtpFound() {
    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_C_OPTICAL_URI))
    )).thenReturn(
      Optional.of(
        createCtp(new Aid(PORT_C_OPTICAL_AID), new Uri(PORT_C_OPTICAL_URI), OTU4_LAYERS)
      )
    );

    when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OPTICAL_URI))
    )).thenReturn(
      Optional.of(
        createCtp(new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OPTICAL_URI), OTUC4PA_LAYERS)
      )
    );

    when(rrmOtnDbResourcesFacade.findPlug(eq(NETWORK_ELEMENT_ID), eq(new Aid(PLUG_C_AID)))).thenReturn(Optional.of(generatePlugRef_C()));
    when(rrmOtnDbResourcesFacade.findCardFromPlug(eq(NETWORK_ELEMENT_ID), eq(new Aid(PLUG_C_AID)))).thenReturn(generateCardRef());

    List<OpticalParameters.SelectedParameter> params = new ArrayList<>();
    params.add(new OpticalParameters.SelectedParameter(ParameterName.FEC, new Value.Enum("GFEC")));
    params.addAll(inParams);
    var request = generateCrossConnectRequest(params, PORT_C_AID, PORT_N_AID, InterfaceType.ENNI, InterfaceType.UNSPECIFIED);
    assertThrows(OtnProvisioningException.class, () -> sut.prepareRequest(NETWORK_ELEMENT_ID, request));
  }

  @Test
  void egressPrepareRequestNoOpticalParams() {
    init();

    when(rrmOtnDbResourcesFacade.findCardFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      Optional.of(new CardRef(NETWORK_ELEMENT_ID, new Aid(CARD_AID), new Uri(CARD_URI), MODULE_TYPE, 1))
    );

    when(rrmOtnDbResourcesFacade.findPlug(eq(NETWORK_ELEMENT_ID), eq(new Aid(PLUG_C_AID)))).thenReturn(Optional.of(generatePlugRef_C()));
    when(rrmOtnDbResourcesFacade.findCardFromPlug(eq(NETWORK_ELEMENT_ID), eq(new Aid(PLUG_C_AID)))).thenReturn(generateCardRef());

    var request = generateCrossConnectRequest(inParams, PORT_N_AID, PORT_C_AID, InterfaceType.UNSPECIFIED, InterfaceType.ENNI);

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(5, result.size());
      assertInstanceOf(CtpRequest.class, result.get(0));
      assertEquals(generateCtpRequest(PORT_C_OPTICAL_URI, false, Set.of()), result.get(0));
      assertInstanceOf(PtpParamsRequest.class, result.get(1));
      assertEquals(new PtpParamsRequest(NETWORK_ELEMENT_ID, PORT_C_URI, true, Set.of(), provisionApi), result.get(1));
      assertInstanceOf(CtpRequest.class, result.get(2));
      assertEquals(generateCtpRequest(PORT_C_CTP_URI, false, Set.of()), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI, true, Set.of()), result.get(3));
      assertInstanceOf(SncRequest.class, result.get(4));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, SNC_URI, false,
        Set.of(PORT_C_CTP_URI),
        Set.of(PORT_N_CTP_URI),
        provisionApi), result.get(4));

    } catch (Exception e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void egressPrepareRequest() {
    init();

    when(rrmOtnDbResourcesFacade.findCardFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_N_AID))
    )).thenReturn(
      Optional.of(new CardRef(NETWORK_ELEMENT_ID, new Aid(CARD_AID), new Uri(CARD_URI), MODULE_TYPE, 1))
    );

    when(rrmOtnDbResourcesFacade.findPlug(eq(NETWORK_ELEMENT_ID), eq(new Aid(PLUG_C_AID)))).thenReturn(Optional.of(generatePlugRef_C()));
    when(rrmOtnDbResourcesFacade.findCardFromPlug(eq(NETWORK_ELEMENT_ID), eq(new Aid(PLUG_C_AID)))).thenReturn(generateCardRef());

    var moParams = generateCimParams();
    List<OpticalParameters.SelectedParameter> params = new ArrayList<>();
    params.add(new OpticalParameters.SelectedParameter(ParameterName.FEC, new Value.Enum("GFEC")));
    params.addAll(inParams);

    var request = generateCrossConnectRequest(params, PORT_N_AID, PORT_C_AID, InterfaceType.UNSPECIFIED, InterfaceType.ENNI);

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(5, result.size());
      assertInstanceOf(CtpRequest.class, result.get(0));
      assertEquals(generateCtpRequest(PORT_C_OPTICAL_URI, false, new HashSet<>(moParams)), result.get(0));
      assertInstanceOf(PtpParamsRequest.class, result.get(1));
      assertEquals(new PtpParamsRequest(NETWORK_ELEMENT_ID, PORT_C_URI, true, Set.of(), provisionApi), result.get(1));
      assertInstanceOf(CtpRequest.class, result.get(2));
      assertEquals(generateCtpRequest(PORT_C_CTP_URI, false, Set.of()), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI, true, Set.of()), result.get(3));
      assertInstanceOf(SncRequest.class, result.get(4));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, SNC_URI, false,
        Set.of(PORT_C_CTP_URI),
        Set.of(PORT_N_CTP_URI),
        provisionApi), result.get(4));
    } catch (Exception e) {
      fail("No Exception expected: " + e);
    }
  }

  @Test
  void transitPrepareRequest() {
    init();

    when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Aid(PORT_C_AID))
    )).thenReturn(
      List.of(
        createCtp(new Aid(PORT_C_OPTICAL_AID), new Uri(PORT_C_OPTICAL_URI), OTU4_LAYERS)
      )
    );

    List<OpticalParameters.SelectedParameter> params = new ArrayList<>();
    params.add(new OpticalParameters.SelectedParameter(ParameterName.FEC, new Value.Enum("GFEC")));
    params.addAll(inParams);

    var request = generateCrossConnectRequest(params, PORT_N_AID, PORT_C_AID, InterfaceType.UNSPECIFIED, InterfaceType.UNSPECIFIED);

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(3, result.size());
      assertInstanceOf(CtpRequest.class, result.get(0));
      assertEquals(generateCtpRequest(PORT_C_CTP_URI, false, Set.of()), result.get(0));
      assertInstanceOf(CtpRequest.class, result.get(1));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI, true, Set.of()), result.get(1));
      assertInstanceOf(SncRequest.class, result.get(2));
      assertEquals(new SncRequest(NETWORK_ELEMENT_ID, SNC_URI, false,
        Set.of(PORT_C_CTP_URI),
        Set.of(PORT_N_CTP_URI),
        provisionApi), result.get(2));
    } catch (Exception e) {
      fail("No Exception expected: " + e);
    }
  }
}
