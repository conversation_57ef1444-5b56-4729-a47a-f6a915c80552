/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.mediator.f8.wdm.adapters.persistence;

import com.adva.nlms.resource.mediator.f8.wdm.api.in.RrmSegmentRepository;
import com.adva.nlms.resource.mediator.f8.wdm.api.in.WdmSegment;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;


class RrmSegmentRepositoryImpl implements RrmSegmentRepository {
  private static final Logger log = LogManager.getLogger(RrmSegmentRepositoryImpl.class);

  private final Map<String, WdmSegment> segReqIdToSegmentMapper = new ConcurrentHashMap<>();
  private final Map<Integer, List<WdmSegment>> neIdToSegmentsMapper = new ConcurrentHashMap<>();

  @Override
  public void store(WdmSegment segment) {
    var oldSegment = segReqIdToSegmentMapper.put(createMapKey(segment.getSegReqId(), segment.getNeId()), segment);
    if (oldSegment != null) {
      neIdToSegmentsMapper.get(segment.getNeId()).remove(oldSegment);
    }
    neIdToSegmentsMapper.computeIfAbsent(segment.getNeId(), neId -> new ArrayList<>()).add(segment);
    log.info("added WdmSegment {}", segment);
  }

  @Override
  public void remove(WdmSegment segment) {
    neIdToSegmentsMapper.computeIfPresent(segment.getNeId(), (neId, segments) -> {
      segments.remove(segment);
      if (segments.isEmpty()) {
        return null;
      } else {
        return segments;
      }
    });
    WdmSegment dto = segReqIdToSegmentMapper.remove(createMapKey(segment.getSegReqId(), segment.getNeId()));
    log.info("removed WdmSegment {}", dto);
  }

  @Override
  public void removeSegments(int neId) {
    segReqIdToSegmentMapper.entrySet()
      .removeIf(entry -> entry.getValue().getNeId() == neId);
    neIdToSegmentsMapper.remove(neId);
    log.info("Removed WdmSegments on ne {}", neId);
  }

  @Override
  public WdmSegment getSegment(String segmentRequestId, int neId) {
    return segReqIdToSegmentMapper.get(createMapKey(segmentRequestId, neId));
  }

  private static String createMapKey(String segReqId, int neId) {
    return segReqId + " <---> " + neId;
  }

  @Override
  public WdmSegment getSegmentBySlcID(int neId, int slcId) {
    List<WdmSegment> segmentList = neIdToSegmentsMapper.getOrDefault(neId, List.of()).stream()
      .filter(segment -> segment.hasSlcId(slcId))
      .toList();
    if (segmentList.isEmpty()) {
      log.info("No dto found for neId={}, slcId={} combination", neId, slcId);
      return null;
    } else if (segmentList.size() > 1) {
      log.warn("More than 1 dto found for neId={}, slcId={} combination", neId, slcId);
      return null;
    } else {
      return segmentList.get(0);
    }
  }

  @Override
  public List<WdmSegment> getSegmentByAgatesInterface(int neId, String agatesInterface) {
    return neIdToSegmentsMapper.getOrDefault(neId, List.of()).stream()
      .filter(segment -> segment.isAgatesInterface(agatesInterface))
      .toList();
  }

  @Override
  public List<WdmSegment> findSegmentsByPtpTransponderUri(int neId, String ptpTransponderUri) {
    return neIdToSegmentsMapper.getOrDefault(neId, List.of()).stream()
      .filter(segment -> segment.getTransponderCtpUri() != null && segment.getTransponderCtpUri().contains(ptpTransponderUri))
      .toList();
  }

  @Override
  public List<WdmSegment> findSegmentsByCtpTransponderUri(int neId, String ctpTransponderUri) {
    return neIdToSegmentsMapper.getOrDefault(neId, List.of()).stream()
      .filter(segment -> Objects.equals(ctpTransponderUri, segment.getTransponderCtpUri()))
      .toList();
  }

  @Override
  public List<WdmSegment> findSegmentsByProtectionGroupUri(int neId, String protectionGroupUri) {
    return neIdToSegmentsMapper.getOrDefault(neId, List.of()).stream()
      .filter(segment -> segment.hasProtectionGroupUri(protectionGroupUri))
      .toList();
  }

  @Override
  public List<WdmSegment> findSegmentsByEndpoints(int neId, String srcTp, String dstTp, int channel) {
    return neIdToSegmentsMapper.getOrDefault(neId, List.of()).stream()
      .filter(segment -> segment.getChannelLabel() == channel && srcTp.equals(segment.getAidSrcTp()) && dstTp.equals(segment.getAidDstTp()))
      .toList();
  }
}
