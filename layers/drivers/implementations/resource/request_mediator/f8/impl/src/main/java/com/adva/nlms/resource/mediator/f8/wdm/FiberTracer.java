/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.resource.mediator.f8.wdm;

import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.FiberRef;
import com.adva.nlms.mediation.mo.inventory.resources.FiberResources;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpResources;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Optional;

class FiberTracer {
  private static final Logger log = LogManager.getLogger(FiberTracer.class);
  private final FiberResources fiberResources;
  private final PtpResources ptpResources;

  FiberTracer(FiberResources fiberResources, PtpResources ptpResources) {
    this.fiberResources = fiberResources;
    this.ptpResources = ptpResources;
  }

  record AlienPtpDTO(int shelfNo, int alienPtpNumber, String aidString, boolean isFiberAEnd) {
    public boolean isInvalid() {
      return shelfNo == 0 || alienPtpNumber == 0;
    }
  }

  Optional<AlienPtpDTO> findOtherEndOfAlienFiber(int neId, String ptpAid) {
    final var portUri = ptpResources.findPtp(neId, new Aid(ptpAid))
                                    .map(PtpRef::uri)
                                    .orElse(null);
    final var fibers = fiberResources.findFibers(neId);
    return fibers.stream()
                 .filter(fiber -> fiber.linkName().contains("/alien/"))
                 .map(fiber -> mapToAlienPtpDtoBasedOnFiberEnd(fiber, portUri, neId))
                 .flatMap(Optional::stream)
                 .findFirst();
  }

  private Optional<AlienPtpDTO> mapToAlienPtpDtoBasedOnFiberEnd(FiberRef fiber, Uri portUri, int neId){
    if (fiber.aEnd().equals(portUri)) {
      return mapToAlienPtpDto(neId, fiber.zEnd(), false);
    } else if (fiber.zEnd().equals(portUri)) {
      return mapToAlienPtpDto(neId, fiber.aEnd(), true);
    }
    return Optional.empty();
  }

  private Optional<AlienPtpDTO> mapToAlienPtpDto(int neId, Uri alienPtpUri, boolean isFiberAEnd) {
    return ptpResources.findPtp(neId, alienPtpUri)
      .map(PtpRef::aid)
      .map(Aid::aid)
      .map(aid -> mapToAlienPtpDto(aid, isFiberAEnd));
  }

  private static AlienPtpDTO mapToAlienPtpDto(String alienPtpAid, boolean isFiberAEnd) {
    // Example aid External-3/alien/4
    int ptpNumber = 0;
    int shelfNumber = 0;
    String ssp = alienPtpAid.substring(alienPtpAid.indexOf("-") + 1);
    String[] arr = ssp.split("/");
    if (arr.length == 3) {
      try {
        shelfNumber = Integer.parseInt(arr[0]);
        ptpNumber = Integer.parseInt(arr[2]);
      } catch (NumberFormatException e) {
        log.warn("[WDM] FiberTracer: failed to parse alien ptp aid {} invalid shelf/number", alienPtpAid);
      }
    } else {
      log.warn("[WDM] FiberTracer: failed to parse alien ptp aid {} invalid number of /", alienPtpAid);
    }
    return new AlienPtpDTO(shelfNumber, ptpNumber, alienPtpAid, isFiberAEnd);
  }
}
