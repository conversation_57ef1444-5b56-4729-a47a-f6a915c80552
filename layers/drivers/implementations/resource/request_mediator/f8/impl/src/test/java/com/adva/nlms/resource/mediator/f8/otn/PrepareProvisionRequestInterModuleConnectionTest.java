/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.mediator.f8.otn;

import com.adva.infrastructure.capability.moprovider.api.MoCapabilityProvider;
import com.adva.infrastructure.capability.provider.api.CapabilityProvider;
import com.adva.infrastructure.capabilityprovider.capabilities.CapabilityConfiguration;
import com.adva.infrastructure.capabilityprovider.core.CapabilityProviderConfiguration;
import com.adva.infrastructure.capabilityprovider.mo.MoCapabilityProviderConfiguration;
import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.mediation.mo.inventory.resources.CardRef;
import com.adva.nlms.mediation.mo.inventory.resources.Ctp;
import com.adva.nlms.mediation.mo.inventory.resources.CtpBuilder;
import com.adva.nlms.mediation.mo.inventory.resources.CtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.PlugRef;
import com.adva.nlms.mediation.mo.inventory.resources.PtpRef;
import com.adva.nlms.mediation.mo.inventory.resources.Uri;
import com.adva.nlms.mediation.topology.NEData;
import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.mediation.topology.NetworkElementID;
import com.adva.nlms.opticalparameters.api.OpticalParameters;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import com.adva.nlms.opticalparameters.cim.api.CimValue;
import com.adva.nlms.resource.mediator.f8.CrmSegmentRequestDto;
import com.adva.nlms.resource.mediator.f8.ProtectionGroupFinder;
import com.adva.nlms.resource.mediator.f8.internalpath.Complete;
import com.adva.nlms.resource.mediator.f8.internalpath.Connection;
import com.adva.nlms.resource.mediator.f8.internalpath.CrossConnect;
import com.adva.nlms.resource.mediator.f8.internalpath.ForkPlacement;
import com.adva.nlms.resource.mediator.f8.internalpath.InterModuleConnection;
import com.adva.nlms.resource.mediator.f8.internalpath.InternalPath;
import com.adva.nlms.resource.mediator.f8.internalpath.LabelDTO;
import com.adva.nlms.resource.mediator.f8.internalpath.MultiLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.OduType;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabel;
import com.adva.nlms.resource.mediator.f8.internalpath.OtnLabelType;
import com.adva.nlms.resource.mediator.f8.internalpath.SignalType;
import com.adva.nlms.resource.mediator.f8.internalpath.TerminationPoint;
import com.adva.nlms.resource.mediator.f8.internalpath.Whole;
import com.adva.nlms.resource.mediator.f8.otn.api.in.OtnSegmentRepository;
import com.adva.nlms.resource.provision.f8.api.in.MoCimParameter;
import com.adva.nlms.resource.provision.f8.api.in.Provision;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PrepareProvisionRequestInterModuleConnectionTest {

  private static final int NETWORK_ELEMENT_ID = 1;

  private static final LayerQualifier layer = LayerQualifier.ODU4;
  private final Map<String, String> INTER_MODULE_COMMON_PARAMS = Map.of(
  );

  private static final String MODULE_1_1_TYPE = "MF-M6MDT";
  private static final String MODULE_1_1_AID = "Module-1/1";
  private static final String MODULE_1_1_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card";
  private static final String PORT_C_AID = "Port-1/1/p6";
  private static final String PORT_C_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,6";
  private static final String PLUG_C_TYPE = "QSFP28-112G-LR4-SM-LC";
  private static final String PLUG_C_AID = "Plug-1/1/p6";
  private static final String PLUG_C_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/plgh,6";
  private static final String PORT_C_OPTICAL_URI = PORT_C_URI + "/ctp/otu4";
  private static final String PORT_C_CTP_URI = PORT_C_OPTICAL_URI + "/ctp/" + LayerQualifierToCimString.convert(layer);

  private static final String MODULE_1_2_TYPE = "MA-2C5LT";
  private static final String MODULE_1_2_AID = "Module-1/2";
  private static final String MODULE_1_2_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card";
  private static final String PORT_N_AID = "Port-1/2/n1";
  private static final String PORT_N_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/ptp/nw,1";
  private static final String PLUG_N_TYPE = "QSFP28-112G-LR4-SM-LC";
  private static final String PLUG_N_AID = "Plug-1/2/n1";
  private static final String PLUG_N_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,2/eq/card/plgh,nw,1";
  private static final String PORT_N_OPTICAL_AID = "OTU4-1/2/n1/otu4";
  private static final String PORT_N_OPTICAL_URI = PORT_N_URI + "/ctp/otu4";
  private static final String PORT_N_CTP_URI = PORT_N_OPTICAL_URI + "/ctp/" + LayerQualifierToCimString.convert(layer);

  private static final String PORT_N_CRS_AID = "Port-1/1/p2";
  private static final String PORT_N_CRS_CTP_AID = "OTUC4-1/1/p2/otuc4";
  private static final String PORT_N_CRS_CTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,2/ctp/otuc4";
  private static final String PORT_N_CRS_INTERMEDIATE_CTP_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/ptp/ptrf,2/ctp/otuc4/ctp/oduc4/ctp/odu4-";
  private static final String SNC_URI = "/mit/me/1/eqh/shelf,1/eqh/slot,1/eq/card/sn/" + LayerQualifierToCimString.convert(layer) + "/snc";

  private static final List<String> OTUC4_LAYERS = List.of("och", "flexo-4", "otuc4");

  private final RrmOtnDbResourcesFacade rrmOtnDbResourcesFacade = Mockito.mock(RrmOtnDbResourcesFacade.class);
  private final NEDataProvider neDataProvider = Mockito.mock(NEDataProvider.class);
  private static final CapabilityProvider capabilityProvider = new CapabilityProviderConfiguration()
    .capabilityProvider(new CapabilityConfiguration().capabilityRepository(), null, null);
  private static final MoCapabilityProvider moCapabilityProvider = new MoCapabilityProviderConfiguration().moCapabilityProvider();
  private final RrmOtnCardCapabilities rrmOtnCardCapabilities = new RrmOtnCardCapabilitiesImpl(neDataProvider, capabilityProvider, moCapabilityProvider);
  private final MoCapabilityProvider provider = new MoCapabilityProviderConfiguration().moCapabilityProvider();
  private final OtnCimProvisionRequestParametersCreatorImpl cimProvisionRequestParametersCreator = new OtnCimProvisionRequestParametersCreatorImpl(provider);
  private final Provision provisionApi = Mockito.mock(Provision.class);
  private final OtnSegmentRepository otnSegmentRepository = Mockito.mock(OtnSegmentRepository.class);
  private final ProtectionGroupFinder protectionGroupFinder = Mockito.mock(ProtectionGroupFinder.class);

  private final PrepareProvisionRequest sut = new PrepareProvisionRequest(
    cimProvisionRequestParametersCreator, provisionApi, rrmOtnCardCapabilities, rrmOtnDbResourcesFacade, otnSegmentRepository, protectionGroupFinder, moCapabilityProvider);

  private static TerminationPoint generateTerminatePoint(String aid, OtnLabelType type, boolean terminate) {
    return new TerminationPoint(aid, new LabelDTO(new MultiLabel(List.of(new OtnLabel(type)))), terminate);
  }

  private static CardRef generateCardRef_1_1() {
    return new CardRef(NETWORK_ELEMENT_ID, new Aid(MODULE_1_1_AID), new Uri(MODULE_1_1_URI), MODULE_1_1_TYPE, 0);
  }

  private static CardRef generateCardRef_1_2() {
    return new CardRef(NETWORK_ELEMENT_ID, new Aid(MODULE_1_2_AID), new Uri(MODULE_1_2_URI), MODULE_1_2_TYPE, 0);
  }

  private static PlugRef generatePlugRef_C() {
    return PlugRef.builder()
      .setNeId(NETWORK_ELEMENT_ID)
      .setAid(PLUG_C_AID)
      .setUri(PLUG_C_URI)
      .setType(PLUG_C_TYPE)
      .build();
  }

  private static PlugRef generatePlugRef_N() {
    return PlugRef.builder()
      .setNeId(NETWORK_ELEMENT_ID)
      .setAid(PLUG_N_AID)
      .setUri(PLUG_N_URI)
      .setType(PLUG_N_TYPE)
      .build();
  }

  private static List<Connection> generateCrossConnects(String src,
                                                                  OtnLabelType srcLabel,
                                                                  boolean srcTerminate,
                                                                  String dst,
                                                                  OtnLabelType dstLabel,
                                                                  boolean dstTerminate) {
    var srcTtp = generateTerminatePoint(src, srcLabel, srcTerminate);
    var dstTtp = generateTerminatePoint(dst, dstLabel, dstTerminate);
    var connection = new Connection(
      new SignalType(new OduType(layer, 0)), List.of(
      new CrossConnect(srcTtp, dstTtp, ForkPlacement.NONE)));

    return List.of(connection);
  }

  private static List<InterModuleConnection> generateInterModuleConnections(String src,
                                                                            OtnLabelType srcLabel,
                                                                            boolean srcTerminate,
                                                                            String dst,
                                                                            OtnLabelType dstLabel,
                                                                            boolean dstTerminate,
                                                                            Map<String, String> interModulecommonParams) {
    var signalType = new SignalType(new OduType(layer, 0));
    var srcTtp = generateTerminatePoint(src, srcLabel, srcTerminate);
    var dstTtp = generateTerminatePoint(dst, dstLabel, dstTerminate);
    var interModuleConnection = new InterModuleConnection(signalType, srcTtp, dstTtp, interModulecommonParams);

    return List.of(interModuleConnection);
  }

  private static CrmSegmentRequestDto generateInterModuleRequestOnly(List<OpticalParameters.SelectedParameter> params,
                                                      String src,
                                                      OtnLabelType srcLabel,
                                                      boolean srcTerminate,
                                                      String dst,
                                                      OtnLabelType dstLabel,
                                                      boolean dstTerminate,
                                                      Map<String, String> interModulecommonParams) {
    var request = new CrmSegmentRequestDto();
    request.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    request.portParams = params;
    request.srcTp = src;
    request.dstTp = dst;
    var interModuleConnection = generateInterModuleConnections(src,
      srcLabel,
      srcTerminate,
      dst,
      dstLabel,
      dstTerminate,
      interModulecommonParams);
    request.internalPath = new InternalPath(interModuleConnection, null, null);
    return request;
  }

  private static CrmSegmentRequestDto generateInterModuleAndCrossConnectsRequest(
    List<OpticalParameters.SelectedParameter> params,
    /* src for cross connect and inter module */
                                                      String srcCrsAndInter,
                                                      OtnLabelType srcLabelCrsAndInter,
                                                      boolean srcTerminateCrsAndInter,
    /* dst for cross connect only */
                                                      String dstCrs,
                                                      OtnLabelType dstLabelCrs,
                                                      boolean dstTerminateCrs,
    /* dst for inter module only */
                                                      String dstInter,
                                                      OtnLabelType dstLabelInter,
                                                      boolean dstTerminateInter,
                                                      Map<String, String> interModulecommonParams) {
    var request = new CrmSegmentRequestDto();
    request.requestType = CrmSegmentRequestDto.RequestType.CREATE;
    request.portParams = params;
    request.srcTp = dstInter;
    request.dstTp = dstCrs;
    var interModuleConnection = generateInterModuleConnections(srcCrsAndInter,
      srcLabelCrsAndInter,
      srcTerminateCrsAndInter,
      dstInter,
      dstLabelInter,
      dstTerminateInter,
      interModulecommonParams);
    var crossConnect = generateCrossConnects(srcCrsAndInter,
      srcLabelCrsAndInter,
      srcTerminateCrsAndInter,
      dstCrs,
      dstLabelCrs,
      dstTerminateCrs);
    request.internalPath = new InternalPath(interModuleConnection, crossConnect, null);
    return request;
  }

  private CtpRequest generateCtpRequest(String uri, boolean isAdopt, boolean entityAutocreated, Set<MoCimParameter> params) {
    return new CtpRequest(NETWORK_ELEMENT_ID, uri, isAdopt, entityAutocreated, params, provisionApi);
  }

  private SncRequest generateSncRequest(String uri, boolean isAdopt, String aEndpoint, String zEndpoint) {
    return new SncRequest(NETWORK_ELEMENT_ID, uri, isAdopt, Set.of(aEndpoint), Set.of(zEndpoint), provisionApi);
  }

  @BeforeEach
  void init() {
    Mockito.when(rrmOtnDbResourcesFacade.findCardFromModule(NETWORK_ELEMENT_ID, new Aid(MODULE_1_1_AID)))
      .thenReturn(generateCardRef_1_1());

    Mockito.when(rrmOtnDbResourcesFacade.findCardFromModule(NETWORK_ELEMENT_ID, new Aid(MODULE_1_2_AID)))
        .thenReturn(generateCardRef_1_2());

    Mockito.when(neDataProvider.getNeData(Mockito.anyInt()
    )).thenReturn(new NEData(NETWORK_ELEMENT_ID, 0,
      "FSP 3000C", null, null, "6.5.1")
    );

    Mockito.when(rrmOtnDbResourcesFacade.findCardFromPlug(
      Mockito.eq(NETWORK_ELEMENT_ID),
      Mockito.eq(new Aid(PLUG_C_AID))
    )).thenReturn(
      Optional.of(generateCardRef_1_1())
    );

    Mockito.when(rrmOtnDbResourcesFacade.findPlug(
      Mockito.eq(NETWORK_ELEMENT_ID),
      Mockito.eq(new Aid(PLUG_C_AID))
    )).thenReturn(
      Optional.of(generatePlugRef_C())
    );

    Mockito.when(rrmOtnDbResourcesFacade.findCardFromPlug(
      Mockito.eq(NETWORK_ELEMENT_ID),
      Mockito.eq(new Aid(PLUG_N_AID))
    )).thenReturn(
      Optional.of(generateCardRef_1_2())
    );

    Mockito.when(rrmOtnDbResourcesFacade.findPlug(
      Mockito.eq(NETWORK_ELEMENT_ID),
      Mockito.eq(new Aid(PLUG_N_AID))
    )).thenReturn(
      Optional.of(generatePlugRef_N())
    );

    Mockito.when(rrmOtnDbResourcesFacade.findCardFromPtp(
      Mockito.eq(NETWORK_ELEMENT_ID),
      Mockito.eq(new Aid(PORT_C_AID))
    )).thenReturn(
      Optional.of(generateCardRef_1_1())
    );

    Mockito.when(rrmOtnDbResourcesFacade.findCardFromPtp(
      Mockito.eq(NETWORK_ELEMENT_ID),
      Mockito.eq(new Aid(PORT_N_AID))
    )).thenReturn(
      Optional.of(generateCardRef_1_2())
    );

    Mockito.when(rrmOtnDbResourcesFacade.findPlugFromPtp(
      Mockito.eq(NETWORK_ELEMENT_ID),
      Mockito.eq(new Aid(PORT_C_AID))
    )).thenReturn(
      Optional.of(generatePlugRef_C())
    );

    Mockito.when(rrmOtnDbResourcesFacade.findPlugFromPtp(
      Mockito.eq(NETWORK_ELEMENT_ID),
      Mockito.eq(new Aid(PORT_N_AID))
    )).thenReturn(
      Optional.of(generatePlugRef_N())
    );

    Mockito.when(rrmOtnDbResourcesFacade.findPtp(
      Mockito.eq(NETWORK_ELEMENT_ID),
      Mockito.eq(new Aid(PORT_C_AID))
    )).thenReturn(
      Optional.of(new PtpRef(NETWORK_ELEMENT_ID, new Aid(PORT_C_AID), new Uri(PORT_C_URI), null)
    ));

    Mockito.when(rrmOtnDbResourcesFacade.findPtp(
      Mockito.eq(NETWORK_ELEMENT_ID),
      Mockito.eq(new Aid(PORT_N_AID))
    )).thenReturn(
      Optional.of(new PtpRef(NETWORK_ELEMENT_ID, new Aid(PORT_N_AID), new Uri(PORT_N_URI), null)
    ));

    when(rrmOtnDbResourcesFacade.findCtp(
      eq(NETWORK_ELEMENT_ID),
      eq(new Uri(PORT_N_OPTICAL_URI))
    )).thenReturn(
      Optional.of(new CtpRef(NETWORK_ELEMENT_ID, new Aid(PORT_N_OPTICAL_AID), new Uri(PORT_N_OPTICAL_URI), null))
    );
  }

  @Test
  void testPrepareIntermoduleRequestNtoC() {
    var srcLabel = new Whole();
    var dstLabel = new Whole();
    var request = generateInterModuleRequestOnly(
      List.of(), PORT_N_AID,
      srcLabel,
      false,
      PORT_C_AID,
      dstLabel,
      false,
      INTER_MODULE_COMMON_PARAMS
    );

    MoCimParameter refTermParams = new MoCimParameter("", "termination-mode", new CimValue.Enum("nss"));

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(6, result.size());
      assertInstanceOf(CtpRequest.class, result.get(0));
      assertEquals(generateCtpRequest(PORT_N_OPTICAL_URI, true, true, Set.of()), result.get(0));
      assertInstanceOf(PtpParamsRequest.class, result.get(1));
      assertEquals(new PtpParamsRequest(NETWORK_ELEMENT_ID, PORT_N_URI, true, Set.of(), provisionApi), result.get(1));
      assertInstanceOf(CtpRequest.class, result.get(2));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI, false, false, Set.of(refTermParams)), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_C_OPTICAL_URI, false, false, Set.of()), result.get(3));
      assertInstanceOf(PtpParamsRequest.class, result.get(4));
      assertEquals(new PtpParamsRequest(NETWORK_ELEMENT_ID, PORT_C_URI, true, Set.of(), provisionApi), result.get(4));
      assertInstanceOf(CtpRequest.class, result.get(5));
      assertEquals(generateCtpRequest(PORT_C_CTP_URI, false, false, Set.of()), result.get(5));
    } catch (Exception e) {
      fail("Exception thrown: " + e.getMessage());
    }
  }

  @Test
  void testPrepareIntermoduleRequestCtoN() {
    var srcLabel = new Whole();
    var dstLabel = new Whole();
    var request = generateInterModuleRequestOnly(
      List.of(), PORT_C_AID,
      srcLabel,
      false,
      PORT_N_AID,
      dstLabel,
      false,
      INTER_MODULE_COMMON_PARAMS
    );

    MoCimParameter refTermParams = new MoCimParameter("", "termination-mode", new CimValue.Enum("nss"));

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(6, result.size());
      assertInstanceOf(CtpRequest.class, result.get(0));
      assertEquals(generateCtpRequest(PORT_C_OPTICAL_URI, false, false, Set.of()), result.get(0));
      assertInstanceOf(PtpParamsRequest.class, result.get(1));
      assertEquals(new PtpParamsRequest(NETWORK_ELEMENT_ID, PORT_C_URI, true, Set.of(), provisionApi), result.get(1));
      assertInstanceOf(CtpRequest.class, result.get(2));
      assertEquals(generateCtpRequest(PORT_C_CTP_URI, false, false, Set.of()), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_N_OPTICAL_URI, true, true, Set.of()), result.get(3));
      assertInstanceOf(PtpParamsRequest.class, result.get(4));
      assertEquals(new PtpParamsRequest(NETWORK_ELEMENT_ID, PORT_N_URI, true, Set.of(), provisionApi), result.get(4));
      assertInstanceOf(CtpRequest.class, result.get(5));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI, false, false, Set.of(refTermParams)), result.get(5));
    } catch (Exception e) {
      fail("Exception thrown: " + e.getMessage());
    }
  }

  @Test
  void testPrepareRequestInterModuleAndCrossConnect() {
    Mockito.when(rrmOtnDbResourcesFacade.findAllCtpsWithExtendedDescFromPtp(
      Mockito.eq(NETWORK_ELEMENT_ID),
      Mockito.eq(new Aid(PORT_N_CRS_AID))
    )).thenReturn(
      List.of(createCtp(PORT_N_CRS_CTP_AID, PORT_N_CRS_CTP_URI, OTUC4_LAYERS))
    );

    Mockito.when(rrmOtnDbResourcesFacade.findCtpWithExtendedDesc(
      Mockito.eq(NETWORK_ELEMENT_ID),
      Mockito.eq(new Uri(PORT_N_CRS_CTP_URI))
    )).thenReturn(
      Optional.of(createCtp(PORT_N_CRS_CTP_AID, PORT_N_CRS_CTP_URI, OTUC4_LAYERS))
    );

    when(provisionApi.getAvailableCtpEntity(
      eq(NetworkElementID.create(NETWORK_ELEMENT_ID)),
      eq(PORT_N_CRS_INTERMEDIATE_CTP_URI + 4)
    )).thenReturn(
      Optional.of(4)
    );

    var srcCrsInterLabel = new Whole();
    List<Integer> srcCrsTs = IntStream.range(61, 81).boxed().toList();
    var dstCrsLabel = new Complete(4, srcCrsTs);
    var dstInterLabel = new Whole();

    var request = generateInterModuleAndCrossConnectsRequest(
      List.of(),
      PORT_C_AID,
      srcCrsInterLabel,
      false,
      PORT_N_CRS_AID,
      dstCrsLabel,
      false,
      PORT_N_AID,
      dstInterLabel,
      true,
      INTER_MODULE_COMMON_PARAMS
    );

    var portNTermParams = new MoCimParameter("", "termination-mode", new CimValue.Enum("tmsn"));
    var portNCrsTermParams = Set.of(
      new MoCimParameter(layer.name().toLowerCase() + "/odtu", "ts",
        new CimValue.IntList(srcCrsTs.stream().map(Long::valueOf).toList())),
      new MoCimParameter(layer.name().toLowerCase() + "/odtu", "tp", new CimValue.Int(4))
    );

    try {
      var result = sut.prepareRequest(NETWORK_ELEMENT_ID, request);
      assertEquals(8, result.size());
      assertInstanceOf(CtpRequest.class, result.get(0));
      assertEquals(generateCtpRequest(PORT_C_OPTICAL_URI, false, false, Set.of()), result.get(0));
      assertInstanceOf(PtpParamsRequest.class, result.get(1));
      assertEquals(new PtpParamsRequest(NETWORK_ELEMENT_ID, PORT_C_URI, true, Set.of(), provisionApi), result.get(1));
      assertInstanceOf(CtpRequest.class, result.get(2));
      assertEquals(generateCtpRequest(PORT_C_CTP_URI, false, false, Set.of()), result.get(2));
      assertInstanceOf(CtpRequest.class, result.get(3));
      assertEquals(generateCtpRequest(PORT_N_OPTICAL_URI, true, true, Set.of()), result.get(3));
      assertInstanceOf(PtpParamsRequest.class, result.get(4));
      assertEquals(new PtpParamsRequest(NETWORK_ELEMENT_ID, PORT_N_URI, true, Set.of(), provisionApi), result.get(4));
      assertInstanceOf(CtpRequest.class, result.get(5));
      assertEquals(generateCtpRequest(PORT_N_CTP_URI, false, false, Set.of(portNTermParams)), result.get(5));
      assertInstanceOf(CtpRequest.class, result.get(6));
      assertEquals(generateCtpRequest(PORT_N_CRS_INTERMEDIATE_CTP_URI + 4, false, false, portNCrsTermParams), result.get(6));
      assertInstanceOf(SncRequest.class, result.get(7));
      assertEquals(generateSncRequest(SNC_URI, false, PORT_C_CTP_URI, PORT_N_CRS_INTERMEDIATE_CTP_URI + 4), result.get(7));
    } catch (Exception e) {
      fail("Exception thrown: " + e.getMessage());
    }
  }

  private static Ctp createCtp(String ctpAid, String ctpUri, List<String> layerRate) {
    return CtpBuilder.newBuilder()
      .withNeId(PrepareProvisionRequestInterModuleConnectionTest.NETWORK_ELEMENT_ID)
      .withAid(new Aid(ctpAid))
      .withUri(new Uri(ctpUri))
      .withLayerRate(layerRate)
      .withTributaryPort(0)
      .build();
  }
}
