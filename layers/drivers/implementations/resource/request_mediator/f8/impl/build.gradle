/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: <PERSON><PERSON><PERSON>
 */
plugins {
    id 'com.adva.gradle.plugin.aspectj-weaver'
}

setupMediationModule(project)

dependencies {
    implementation modep(mod_resource_mediator_api)
    implementation modep(mod_resource_provision_f8_api)
    implementation modep(mod_resource_crm_model_f8)

    implementation modep(mod_cap_prov_api)
    implementation modep(mod_optical_parameters_common)

    implementation modep(mod_mo_inventory_f8_api)
    implementation modep(mod_mo_opt_croma_api)

    implementation modep(mod_nmscommon)
    implementation modep(mod_apps_sm_api)

    implementation modep(mod_server_infra_server_modules_api)

    implementation modep(mod_server_common_utils_api)
    implementation modep(mod_resource_advertiser_api)  // CrmMessageSender
    implementation modep(mod_driver_registry_api)
    implementation modep(mod_property)

    api modep(mod_transactional_provisioning)
    api modep(mod_mo_inventory_f8_api)
    api modep(mod_topology_manager_api)

    aspectjpath modep(mod_transactional_provisioning)

    implementation libs.log4j.api
    implementation libs.slf4j.api
    implementation modep(mod_notification_tracing_api)
    implementation libs.spring.context
    implementation libs.enc.ypdb.commons
    implementation libs.nms.ni.model
    implementation libs.commons.lang3
    implementation libs.jakarta.annotation.api

    testImplementation modep(mod_cap_prov_impl)
    testImplementation modep(mod_inf_mo_impl)
    testImplementation modep(mod_inf_mo_impl).sourceSets.test.output

    testImplementation libs.bundles.junit.jupiter
    testImplementation libs.mockito.jupiter
    testImplementation libs.assertJ

    testImplementation modep(mod_mo_provisioning_api)

}

test {
    useJUnitPlatform()
}
