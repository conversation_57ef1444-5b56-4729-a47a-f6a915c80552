/*
 *   Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *   Owner: <PERSON><PERSON><PERSON>
 */
package com.adva.nlms.resource.advertisement.adapters.rest.server.wdm.f7;

import com.adva.nlms.resource.advertisement.f7.impl.F7ResourcesRepository;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Profile("RA-test")
@Configuration
public class WdmF7ResourceAdvertiserControllerConfiguration {
  @Bean
  public WdmF7ResourceAdvertiserController wdmF7ResourceAdvertiserController(F7ResourcesRepository f7ResourcesRepository) {
    return new WdmF7ResourceAdvertiserController(f7ResourcesRepository);
  }
}
