/*
 *   Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *   Owner: <PERSON><PERSON><PERSON>
 */
package com.adva.nlms.resource.advertisement.adapters.rest.server.wdm.f7;

import com.adva.nlms.mediation.common.rest.MDRestComponent;
import com.adva.nlms.resource.advertisement.common.exceptions.NoDataException;
import com.adva.nlms.resource.advertisement.f7.impl.F7ResourcesRepository;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

@Path("f7/resource-advertiser")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@MDRestComponent(basicAuthentication = true)
public class WdmF7ResourceAdvertiserController {
  private final F7ResourcesRepository f7ResourcesRepository;

  WdmF7ResourceAdvertiserController(F7ResourcesRepository f7ResourcesRepository) {
    this.f7ResourcesRepository = f7ResourcesRepository;
  }

  /**
   * Request URI: {@code https://localhost:8443/advabase/f7/resource-advertiser/ltp/{neId}/{ltpName}}
   */
  @GET
  @Path("ltp/{neId}/{ltpName}")
  @Produces(MediaType.APPLICATION_JSON)
  public String getLtp(@PathParam("neId") int neId, @PathParam("ltpName") String ltpName) throws NoDataException, InvalidProtocolBufferException {
    var ltp = f7ResourcesRepository.getLtp(neId, ltpName);
    if (ltp == null) {
      throw new NoDataException();
    }
    return JsonFormat.printer().print(ltp.toBuilder());
  }

  /**
   * Request URI: {@code https://localhost:8443/advabase/f7/resource-advertiser/ttp/{neId}/{ttpName}}
   */
  @GET
  @Path("ttp/{neId}/{ttpName}")
  @Produces(MediaType.APPLICATION_JSON)
  public String getTtp(@PathParam("neId") int neId, @PathParam("ttpName") String ttpName) throws NoDataException, InvalidProtocolBufferException {
    var ttps = f7ResourcesRepository.getTtp(neId);
    if (ttps == null) {
      throw new NoDataException();
    }
    var ttp = ttps.getTtpsList().stream()
        .filter(t -> t.getInterfaceId().getName().equals(ttpName))
        .findFirst()
        .orElseThrow(NoDataException::new);
    return JsonFormat.printer().print(ttp.toBuilder());
  }

  /**
   * Request URI: {@code https://localhost:8443/advabase/f7/resource-advertiser/resource-group/{neId}/{resourceId}}
   */
  @GET
  @Path("resource-group/{neId}/{resourceId}")
  @Produces(MediaType.APPLICATION_JSON)
  public String getResourceGroup(@PathParam("neId") int neId, @PathParam("resourceId") int resourceId) throws NoDataException, InvalidProtocolBufferException {
    var resourceGroups = f7ResourcesRepository.getResourceGroup(neId);
    if (resourceGroups == null) {
      throw new NoDataException();
    }
    var resourceGroup = resourceGroups.getResourceGroupList().stream()
        .filter(rg -> rg.getId() == resourceId)
        .findFirst()
        .orElseThrow(NoDataException::new);
    return JsonFormat.printer().print(resourceGroup.toBuilder());
  }
}
