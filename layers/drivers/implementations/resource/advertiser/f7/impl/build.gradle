/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: <PERSON><PERSON><PERSON>
 */

setupMediationModule(project)

dependencies {
    implementation modep(mod_resource_advertiser_api)
    implementation modep(mod_topology_manager_api)
    implementation modep(mod_mo_model_ec_api)
    implementation modep(mod_driver_registry_api)
    implementation modep(mod_adva_concurrent)

    implementation libs.log4j.api
    implementation libs.spring.context
    implementation libs.jakarta.validation.api
    implementation libs.commons.lang3

    testImplementation libs.bundles.junit.jupiter
    testImplementation libs.mockito.jupiter
    testImplementation libs.assertJ
    testImplementation libs.spring.test
    testImplementation libs.log4j.core
}

test {
    useJUnitPlatform()
}
