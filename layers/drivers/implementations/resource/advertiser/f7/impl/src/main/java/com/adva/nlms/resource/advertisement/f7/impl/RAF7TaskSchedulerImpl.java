/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.advertisement.f7.impl;

import com.adva.nlms.mediation.infrastructure.concurrent.AdvaExecutors;
import com.adva.nlms.mediation.infrastructure.concurrent.NamedThreadFactory;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;


public class RAF7TaskSchedulerImpl implements RAF7TaskScheduler {
  private final ExecutorService raTaskService;
  private final Map<Integer, BlockingQueue<Runnable>> elementQueues = new ConcurrentHashMap<>();
  private final Set<Integer> activeElements = ConcurrentHashMap.newKeySet();

  public RAF7TaskSchedulerImpl() {
    raTaskService = AdvaExecutors.newFixedThreadPool(5, new NamedThreadFactory("RACrmF7Tasks"), true);
  }

  @Override
  public void submitTask(int neId, Runnable task) {
    if (raTaskService == null)
      return;
    elementQueues.compute(neId, (integer, runnables) -> {
      if (runnables == null) {
        runnables = new LinkedBlockingQueue<>();
      }
      runnables.add(task);
      return runnables;
    });
    startProcessingIfNeeded(neId);
  }

  private void startProcessingIfNeeded(int neId) {
    if (activeElements.add(neId)) {
      raTaskService.submit(() -> processQueue(neId));
    }
  }

  private void processQueue(int neId) {
    try {
      BlockingQueue<Runnable> queue = elementQueues.get(neId);
      while (queue != null && !queue.isEmpty()) {
        Runnable task = queue.poll();
        if (task != null) {
          task.run();
        }
      }
    } finally {
      activeElements.remove(neId);
      elementQueues.computeIfPresent(neId, (id, q) -> q.isEmpty() ? null : q);
      if (elementQueues.containsKey(neId)) {
        startProcessingIfNeeded(neId);
      }
    }
  }
}
