/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.advertisement.f7.impl;

import ni.proto.mla.LinkTerminationPointOuterClass;
import ni.proto.mla.NodeOuterClass;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

class F7ResourcesInMemoryRepository implements F7ResourcesRepository {
  private record LtpKey(int neId, String ltpName) {
  }

  private final ConcurrentHashMap<Integer, NodeOuterClass.TunnelTerminationPoints> ttp = new ConcurrentHashMap<>();
  private final ConcurrentHashMap<Integer, NodeOuterClass.ResourceGroups> resourceGroup = new ConcurrentHashMap<>();
  private final ConcurrentHashMap<LtpKey, LinkTerminationPointOuterClass.LinkTerminationPoint> ltp = new ConcurrentHashMap<>();

  @Override
  public void addTtp(int neId, NodeOuterClass.TunnelTerminationPoints ttps) {
    if (ttps != null) {
      ttp.put(neId, ttps);
    }
  }

  @Override
  public NodeOuterClass.TunnelTerminationPoints getTtp(int neId) {
    return ttp.get(neId);
  }

  @Override
  public void addResourceGroup(int neId, NodeOuterClass.ResourceGroups envelope) {
    if (envelope != null) {
      resourceGroup.put(neId, envelope);
    }
  }

  @Override
  public NodeOuterClass.ResourceGroups getResourceGroup(int neId) {
    return resourceGroup.get(neId);
  }

  @Override
  public void addOrUpdateLtp(int neId, String ltpName, LinkTerminationPointOuterClass.LinkTerminationPoint envelope) {
    if (envelope != null) {
      ltp.put(new LtpKey(neId, ltpName), envelope);
    }
  }

  @Override
  public LinkTerminationPointOuterClass.LinkTerminationPoint getLtp(int neId, String ltpName) {
    return ltp.get(new LtpKey(neId, ltpName));
  }

  @Override
  public List<LinkTerminationPointOuterClass.LinkTerminationPoint> getAllLtp() {
    return ltp.values().stream().toList();
  }

  @Override
  public void removeLtp(int neId, String key) {
    ltp.remove(new LtpKey(neId, key));
  }

  @Override
  public void clear(int neId) {
    ttp.remove(neId);
    resourceGroup.remove(neId);
    ltp.keySet().removeIf(key -> key.neId == neId);
  }

}
