/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */
 
package com.adva.nlms.resource.advertisement.f7.impl;

import ni.proto.mla.LinkTerminationPointOuterClass;
import ni.proto.mla.NodeOuterClass;

import java.util.List;

public class F7NoopRepository implements F7ResourcesRepository {


  @Override
  public void addTtp(int neId, NodeOuterClass.TunnelTerminationPoints envelope) {
    // method intentionally left empty
  }

  @Override
  public NodeOuterClass.TunnelTerminationPoints getTtp(int neId) {
    return null;
  }

  @Override
  public void addResourceGroup(int neId, NodeOuterClass.ResourceGroups envelope) {
    // method intentionally left empty
  }

  @Override
  public NodeOuterClass.ResourceGroups getResourceGroup(int neId) {
    return null;
  }

  @Override
  public void addOrUpdateLtp(int neId, String ltpName, LinkTerminationPointOuterClass.LinkTerminationPoint envelope) {
    // method intentionally left empty
  }

  @Override
  public LinkTerminationPointOuterClass.LinkTerminationPoint getLtp(int neId, String ltpName) {
    return null;
  }

  @Override
  public List<LinkTerminationPointOuterClass.LinkTerminationPoint> getAllLtp() {
    return List.of();
  }

  @Override
  public void removeLtp(int neId, String ltpName) {
    // method intentionally left empty
  }

  @Override
  public void clear(int neId) {
    // method intentionally left empty
  }
}
