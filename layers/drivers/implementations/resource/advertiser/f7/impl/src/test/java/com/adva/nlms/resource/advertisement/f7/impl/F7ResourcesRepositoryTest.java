/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.advertisement.f7.impl;

import ni.proto.mla.LinkTerminationPointOuterClass;
import ni.proto.mla.NodeOuterClass;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;


class F7ResourcesRepositoryTest {

    private F7ResourcesInMemoryRepository repository;

    @BeforeEach
    void setUp() {
        repository = new F7ResourcesInMemoryRepository();
    }

    @Test
    void testAddAndGetTtp() {
        var ttp = mock(NodeOuterClass.TunnelTerminationPoints.class);
        repository.addTtp(1, ttp);
        assertEquals(ttp, repository.getTtp(1));
    }

    @Test
    void testAddAndGetResourceGroup() {
        var rg = mock(NodeOuterClass.ResourceGroups.class);
        repository.addResourceGroup(2, rg);
        assertEquals(rg, repository.getResourceGroup(2));
    }

    @Test
    void testAddOrUpdateAndGetLtp() {
        var ltp = mock(LinkTerminationPointOuterClass.LinkTerminationPoint.class);
        repository.addOrUpdateLtp(3, "ltpA", ltp);
        assertEquals(ltp, repository.getLtp(3, "ltpA"));
    }

    @Test
    void testUpdateLtp() {
        var ltp1 = mock(LinkTerminationPointOuterClass.LinkTerminationPoint.class);
        var ltp2 = mock(LinkTerminationPointOuterClass.LinkTerminationPoint.class);
        repository.addOrUpdateLtp(4, "ltpB", ltp1);
        repository.addOrUpdateLtp(4, "ltpB", ltp2);
        assertEquals(ltp2, repository.getLtp(4, "ltpB"));
    }

    @Test
    void testRemoveLtp() {
        var ltp = mock(LinkTerminationPointOuterClass.LinkTerminationPoint.class);
        repository.addOrUpdateLtp(5, "ltpC", ltp);
        repository.removeLtp(5, "ltpC");
        assertNull(repository.getLtp(5, "ltpC"));
    }

    @Test
    void testGetAllLtp() {
        var ltp1 = mock(LinkTerminationPointOuterClass.LinkTerminationPoint.class);
        var ltp2 = mock(LinkTerminationPointOuterClass.LinkTerminationPoint.class);
        repository.addOrUpdateLtp(6, "ltpD", ltp1);
        repository.addOrUpdateLtp(7, "ltpE", ltp2);
        List<LinkTerminationPointOuterClass.LinkTerminationPoint> all = repository.getAllLtp();
        assertTrue(all.contains(ltp1));
        assertTrue(all.contains(ltp2));
    }

    @Test
    void testClear() {
        var ttp = mock(NodeOuterClass.TunnelTerminationPoints.class);
        var rg = mock(NodeOuterClass.ResourceGroups.class);
        var ltp = mock(LinkTerminationPointOuterClass.LinkTerminationPoint.class);
        repository.addTtp(8, ttp);
        repository.addResourceGroup(8, rg);
        repository.addOrUpdateLtp(8, "ltpF", ltp);
        repository.clear(8);
        assertNull(repository.getTtp(8));
        assertNull(repository.getResourceGroup(8));
        assertNull(repository.getLtp(8, "ltpF"));
    }
}
