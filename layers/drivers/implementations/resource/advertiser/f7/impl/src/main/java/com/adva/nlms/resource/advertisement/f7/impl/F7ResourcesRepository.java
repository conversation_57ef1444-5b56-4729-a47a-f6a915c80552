/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */
 
package com.adva.nlms.resource.advertisement.f7.impl;

import ni.proto.mla.LinkTerminationPointOuterClass;
import ni.proto.mla.NodeOuterClass;

import java.util.List;

public interface F7ResourcesRepository {

  void addTtp(int neId, NodeOuterClass.TunnelTerminationPoints envelope);
  NodeOuterClass.TunnelTerminationPoints getTtp(int neId);

  void addResourceGroup(int neId, NodeOuterClass.ResourceGroups envelope);
  NodeOuterClass.ResourceGroups getResourceGroup(int neId);

  void addOrUpdateLtp(int neId, String ltpName, LinkTerminationPointOuterClass.LinkTerminationPoint envelope);
  LinkTerminationPointOuterClass.LinkTerminationPoint getLtp(int neId, String ltpName);
  List<LinkTerminationPointOuterClass.LinkTerminationPoint> getAllLtp();
  void removeLtp(int neId, String ltpName);

  void clear(int neId);
}
