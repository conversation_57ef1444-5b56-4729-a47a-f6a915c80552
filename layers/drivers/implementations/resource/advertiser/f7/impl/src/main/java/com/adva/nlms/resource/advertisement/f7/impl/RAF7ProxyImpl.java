/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */
 
package com.adva.nlms.resource.advertisement.f7.impl;

import com.adva.nlms.driver.resource.advertisement.api.in.ResourceAdvertiserProxy;
import com.adva.nlms.resource.advertisement.api.out.CrmMessageSender;
import com.google.protobuf.InvalidProtocolBufferException;
import ni.msg.EnvelopeOuterClass;
import ni.proto.ml.MlSync;
import ni.proto.mla.messages.MlaMessages;

import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

class RAF7ProxyImpl implements ResourceAdvertiserProxy {
  private final Logger log = LogManager.getLogger(RAF7ProxyImpl.class);

  private final F7ResourcesRepository f7ResourcesRepository;
  private final CrmMessageSender crmMessageSender;
  private final RAF7TaskScheduler taskScheduler;

  public RAF7ProxyImpl(F7ResourcesRepository f7ResourcesRepository, CrmMessageSender crmMessageSender, RAF7TaskScheduler taskScheduler) {
    this.f7ResourcesRepository = f7ResourcesRepository;
    this.crmMessageSender = crmMessageSender;
    this.taskScheduler = taskScheduler;
  }

  @Override
  public void handleEnvelopeMessage(EnvelopeOuterClass.Envelope envelope, int neId) {
    log.info("Received Resource Advertisement from F7 NE {}", neId);
    try {
      taskScheduler.submitTask(neId, () -> {
        var adaptedEnvelope = adaptEnvelopeToEvo(envelope, neId);
        log.info("Sending Resource Advertisement from F7 NE {} to NI Controller", neId);
        crmMessageSender.send(adaptedEnvelope, neId);
      });
    } catch (Exception e) {
      log.error("Failed to process envelope for NE {}, exception {}", neId, e);
    }
  }

  private EnvelopeOuterClass.Envelope adaptEnvelopeToEvo(EnvelopeOuterClass.Envelope envelope, int neId) {
    var mlaMesssageBuilder = MlaMessages.Message.newBuilder();
    try {
      var payload = envelope.getPayload();
      var mlaMessage = MlaMessages.Message.parseFrom(payload);
      if (mlaMessage.hasUpdate()) {
        List<MlSync.Data> dataList = mlaMessage.getUpdate().getDataList().stream()
          .map(data -> parseMlaData(data, neId))
          .toList();
        mlaMesssageBuilder.setUpdate(
          mlaMessage.getUpdate().toBuilder()
            .addAllData(dataList)
            .build()
        );
      } else if (mlaMessage.hasSyncAck()) {
        List<MlSync.Data> dataList = mlaMessage.getSyncAck().getDataList().stream()
          .map(data -> parseMlaData(data, neId))
          .toList();
        mlaMesssageBuilder.setSyncAck(
          mlaMessage.getSyncAck().toBuilder()
            .addAllData(dataList)
            .build()
        );
      } else {
        log.warn("Received unsupported MLA message type from F7 NE {}", neId);
      }

    } catch (InvalidProtocolBufferException e) {
      log.error("Error parsing dispatcher message {}", envelope, e);
    } catch (NumberFormatException e) {
      log.error("Error neId from message {}", envelope, e);
    }
    return envelope.toBuilder()
      .setPayload(mlaMesssageBuilder.build().toByteString())
      .build();
  }

  private MlSync.Data parseMlaData(MlSync.Data data, int neId) {
    var payload = data.getPayload();
    if (payload.hasLtp()) {
      var ltp = payload.getLtp();
      f7ResourcesRepository.addOrUpdateLtp(neId, ltp.getInterfaceId().getName(), ltp);
      var raLtp = LtpTranslator.adaptWdmLtp(ltp);
      f7ResourcesRepository.addOrUpdateLtp(neId, ltp.getInterfaceId().getName(), raLtp);
      data = data.toBuilder()
        .setPayload(data.getPayload().toBuilder().setLtp(raLtp))
        .build();
    } else if (payload.hasTtps()) {
      f7ResourcesRepository.addTtp(neId, payload.getTtps());
    } else if (payload.hasResourceGroups()) {
      f7ResourcesRepository.addResourceGroup(neId, payload.getResourceGroups());
    }
    return data;
  }
}
