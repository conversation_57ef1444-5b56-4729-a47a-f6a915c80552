/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.advertisement.f7.impl;

import com.adva.nlms.driver.resource.advertisement.api.in.DispatcherProxy;
import com.adva.nlms.driver.resource.advertisement.api.in.ResourceAdvertiserProxy;
import com.adva.nlms.resource.advertisement.api.out.CrmMessageSender;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Configuration
class RAConfiguration {
  @Bean(name = "f7ResourcesRepositoryInMemory")
  @Profile("RA-test")
  public F7ResourcesRepository f7ResourcesRepositoryInMemory() {
    return new F7ResourcesInMemoryRepository();
  }

  @Bean(name = "f7ResourcesRepositoryNoop")
  @Profile("!RA-test")
  public F7ResourcesRepository f7ResourcesRepositoryNoop() {
    return new F7NoopRepository();
  }

  @Bean
  public RAF7TaskScheduler raf7TaskScheduler() {
    return new RAF7TaskSchedulerImpl();
  }

  @Bean
  public ResourceAdvertiserProxy resourceAdvertiserProxy(F7ResourcesRepository f7ResourcesRepository, CrmMessageSender crmMessageSender, RAF7TaskScheduler taskScheduler) {
    return new RAF7ProxyImpl(f7ResourcesRepository, crmMessageSender, taskScheduler);
  }

  @Bean
  public DispatcherProxy dispatcherProxy(CrmMessageSender crmMessageSender, RAF7TaskScheduler taskScheduler) {
    return new DispatcherProxyImpl(crmMessageSender, taskScheduler);
  }
}
