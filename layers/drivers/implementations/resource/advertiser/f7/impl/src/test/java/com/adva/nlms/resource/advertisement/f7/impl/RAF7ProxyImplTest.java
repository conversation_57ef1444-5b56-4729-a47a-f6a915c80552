/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: zbigniewj
 */

package com.adva.nlms.resource.advertisement.f7.impl;

import com.adva.nlms.resource.advertisement.api.out.CrmMessageSender;
import ni.msg.EnvelopeOuterClass;
import ni.proto.ml.MlSync;
import ni.proto.mla.messages.MlaMessages;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

class RAF7ProxyImplTest {
  private CrmMessageSender crmMessageSender;
  private RAF7TaskScheduler taskScheduler;
  private RAF7ProxyImpl raf7Proxy;

  @BeforeEach
  void setUp() {
    F7ResourcesRepository f7ResourcesRepository = mock(F7ResourcesRepository.class);
    crmMessageSender = mock(CrmMessageSender.class);
    taskScheduler = mock(RAF7TaskScheduler.class);
    raf7Proxy = new RAF7ProxyImpl(f7ResourcesRepository, crmMessageSender, taskScheduler);
  }

  @Test
  @DisplayName("""
    GIVEN: A valid Envelope message with SyncAck payload
    WHEN:  handleEnvelopeMessage is called
    THEN:  It submits a task to the task scheduler and sends the message using CrmMessageSender
    """)
  void testHandleEnvelopeMessage_SubmitsTaskAndSendsMessage() {
    // Given
    MlaMessages.Message payload = MlaMessages.Message.newBuilder()
      .setSyncAck(MlaMessages.SyncAck.newBuilder()
        .addData(MlSync.Data.newBuilder().build())
        .build())
      .build();
    EnvelopeOuterClass.Envelope envelope = EnvelopeOuterClass.Envelope.newBuilder()
      .setPayload(payload.toByteString())
      .build();
    int neId = 123;

    // When
    doAnswer(invocation -> {
      int argumentNeId = invocation.getArgument(0);
      assertEquals(neId, argumentNeId);
      Runnable task = invocation.getArgument(1);
      task.run();
      return null;
    }).when(taskScheduler).submitTask(eq(neId), any(Runnable.class));

    raf7Proxy.handleEnvelopeMessage(envelope, neId);

    // Then
    verify(taskScheduler).submitTask(eq(neId), any(Runnable.class));
    verify(crmMessageSender).send(any(EnvelopeOuterClass.Envelope.class), eq(neId));
  }
}
