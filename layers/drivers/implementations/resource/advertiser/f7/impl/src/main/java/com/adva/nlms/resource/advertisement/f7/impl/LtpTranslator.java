/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.advertisement.f7.impl;

import ni.proto.external.common.signal_description.LspSwitchingType;
import ni.proto.mla.LinkTerminationPointOuterClass;

public class LtpTranslator {

  private LtpTranslator() {
    throw new UnsupportedOperationException("Util class. Creating an instance of this class is not possible.");
  }

  public static LinkTerminationPointOuterClass.LinkTerminationPoint adaptWdmLtp(LinkTerminationPointOuterClass.LinkTerminationPoint ltp) {
    if (isWdm(ltp)) {
      var interfaceIdName = ltp.getInterfaceId().getName();
      return ltp.toBuilder()
        .setInterfaceId(ltp.getInterfaceId().toBuilder().setName(interfaceIdName.replace("OL-", "PTP-")))
        .setAssociatedTerminationPoint(interfaceIdName)
        .build();
    }
    return ltp;
  }

  private static boolean isWdm(LinkTerminationPointOuterClass.LinkTerminationPoint ltp) {
    return ltp.getLayer().equals(LspSwitchingType.LSP_SWITCHING_TYPE_LSC);
  }
}
