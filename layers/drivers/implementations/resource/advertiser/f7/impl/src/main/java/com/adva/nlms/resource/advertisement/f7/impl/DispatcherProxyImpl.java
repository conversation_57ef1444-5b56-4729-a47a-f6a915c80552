/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.advertisement.f7.impl;

import com.adva.nlms.driver.resource.advertisement.api.in.DispatcherProxy;
import com.adva.nlms.resource.advertisement.api.out.CrmMessageSender;
import ni.msg.EnvelopeOuterClass;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class DispatcherProxyImpl implements DispatcherProxy {
  private final Logger log = LogManager.getLogger(DispatcherProxyImpl.class);
  private final CrmMessageSender crmMessageSender;
  private final RAF7TaskScheduler taskScheduler;

  public DispatcherProxyImpl(CrmMessageSender crmMessageSender, RAF7TaskScheduler taskScheduler) {
    this.crmMessageSender = crmMessageSender;
    this.taskScheduler = taskScheduler;
  }

  @Override
  public void handleEnvelopeMessage(EnvelopeOuterClass.Envelope envelope, int neId) {
    try {
      taskScheduler.submitTask(neId, () -> crmMessageSender.send(envelope, neId));
    } catch (Exception e) {
      log.error("Failed to process {} message for NE {}, exception {}",
        envelope.getType().getValueDescriptor(), neId, e);
    }
  }
}
