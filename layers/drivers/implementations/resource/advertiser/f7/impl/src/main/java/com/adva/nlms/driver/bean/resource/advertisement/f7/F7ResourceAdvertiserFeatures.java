/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.driver.bean.resource.advertisement.f7;

import com.adva.nlms.driver.api.in.registry.AbstractDriverFeatures;
import com.adva.nlms.driver.api.in.registry.DriverFeaturesDDI;
import com.adva.nlms.driver.resource.advertisement.api.in.ResourceAdvertiser;
import com.adva.nlms.mediation.bean.provider.api.BeanProvider;
import com.adva.nlms.resource.advertisement.f7.impl.F7ResourcesRepository;
import com.adva.topology.manager.api.in.TopologyNodeApi;
import org.springframework.stereotype.Component;

@Component
class F7ResourceAdvertiserFeatures extends AbstractDriverFeatures implements DriverFeaturesDDI {
  protected static final Integer VERSION = 1;
  protected static final String DRIVER_ID = "F7_RESOURCE_ADVERTISER_DRIVER";
  protected static final String QUALIFIER = "F7_RESOURCE_ADVERTISER"; // qualifier is used in delegator

  @Override
  public Integer getVersion() {
    return VERSION;
  }

  @Override
  public String getID() {
    return DRIVER_ID;
  }

  @Override
  protected void initialize() {
    // F7ResourceAdvertiser can't be declared as Spring Bean,
    // because delegate Bean, implementing the same interface already exist in the SpringContext
    addFeature(
      ResourceAdvertiser.class,
      QUALIFIER,
      () -> {
        TopologyNodeApi topoNodeApi = BeanProvider.get().getBean(TopologyNodeApi.class);
        F7ResourcesRepository f7ResourcesRepository = BeanProvider.get().getBean(F7ResourcesRepository.class);
        return new F7ResourceAdvertiser(topoNodeApi, f7ResourcesRepository);
      }
    );
  }
}
