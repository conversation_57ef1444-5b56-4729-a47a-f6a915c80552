/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.advertisement.f7.impl;

import ni.proto.external.common.signal_description.LspSwitchingType;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;


class LtpTranslatorTest {

  @Test
  @DisplayName("""
    GIVEN: Proto LTP message with Ethernet layer
    WHEN:  adaptWdmLtp is called
    THEN:  Proto LTP message with interfaceId set to PTP-15 and associatedTerminationPoint set to OL-15
    """)
  void testAdaptWdmLtp_WdmLtp() {
    var interfaceId = ni.proto.inet.InterfaceId.newBuilder()
      .setName("OL-15")
      .build();
    var ltp = ni.proto.mla.LinkTerminationPointOuterClass.LinkTerminationPoint.newBuilder()
      .setInterfaceId(interfaceId)
      .setLayer(LspSwitchingType.LSP_SWITCHING_TYPE_LSC)
      .build();
    var adaptedLtp = LtpTranslator.adaptWdmLtp(ltp);
    assertEquals("OL-15", adaptedLtp.getAssociatedTerminationPoint());
    assertEquals("PTP-15", adaptedLtp.getInterfaceId().getName());
  }

  @Test
  @DisplayName("""
    GIVEN: Proto LTP message with Ethernet layer
    WHEN:  adaptWdmLtp invoked
    THEN:  Proto LTP message with no changes
    """)
  void testAdaptWdmLtp_EthLtp() {
    // Given
    var interfaceId = ni.proto.inet.InterfaceId.newBuilder()
      .setName("OL-15")
      .build();
    var ltp = ni.proto.mla.LinkTerminationPointOuterClass.LinkTerminationPoint.newBuilder()
      .setInterfaceId(interfaceId)
      .setLayer(LspSwitchingType.LSP_SWITCHING_TYPE_ETHERNET)
      .build();

    // When
    var adaptedLtp = LtpTranslator.adaptWdmLtp(ltp);

    // Then
    assertTrue(adaptedLtp.getAssociatedTerminationPoint().isEmpty());
    assertEquals("OL-15", adaptedLtp.getInterfaceId().getName());
  }
}
