/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.driver.bean.resource.advertisement.f7;

import java.util.UUID;

import com.adva.nlms.driver.resource.advertisement.api.in.ResourceAdvertiser;
import com.adva.nlms.resource.advertisement.f7.impl.F7ResourcesRepository;
import com.adva.topology.manager.api.dto.NodeDto;
import com.adva.topology.manager.api.in.TopologyNodeApi;


class F7ResourceAdvertiser implements ResourceAdvertiser {
  private final TopologyNodeApi topologyNodeApi;
  private final F7ResourcesRepository f7ResourcesRepository;

  F7ResourceAdvertiser(TopologyNodeApi topologyNodeApi, F7ResourcesRepository f7ResourcesRepository) {
    this.topologyNodeApi = topologyNodeApi;
    this.f7ResourcesRepository = f7ResourcesRepository;
  }

  @Override
  public void resyncNE(UUID uuid) {
    // Method intentionally left empty.
    // This implementation is not required for F7ResourceAdvertiser.
  }

  @Override
  public void populateNE(UUID uuid) {
    // Method intentionally left empty.
    // This implementation is not required for F7ResourceAdvertiser.
  }

  @Override
  public void removeNE(UUID uuid) {
    f7ResourcesRepository.clear(lookupNeID(uuid));
  }

  /**
   * @deprecated This method is deprecated and kept only for interface compatibility.
   *             It is intentionally left empty and will be removed in a future release.
   */
  @Deprecated(forRemoval = true)
  @Override
  public void resyncNE(int neId) {
    // Method intentionally left empty to satisfy interface contract.
    // This implementation is not required for F7ResourceAdvertiser.
  }

  /**
   * @deprecated This method is deprecated and kept only for interface compatibility.
   *             It is intentionally left empty and will be removed in a future release.
   */
  @Deprecated(forRemoval = true)
  @Override
  public void populateNE(int neId) {
    // Method intentionally left empty to satisfy interface contract.
    // This implementation is not required for F7ResourceAdvertiser.
  }

  /**
   * @deprecated This method is deprecated and kept only for interface compatibility.
   *             It is intentionally left empty and will be removed in a future release.
   */
  @Deprecated(forRemoval = true)
  @Override
  public void removeNE(int neId) {
    // Method intentionally left empty to satisfy interface contract.
    // This implementation is not required for F7ResourceAdvertiser.
  }

  /**
   * Convert the UUID from the request to the Network Element database id.
   * This could be converted into a cache in the future as these ID's do not change.
   */
  private int lookupNeID(UUID uuid) {
    NodeDto node = topologyNodeApi.getNodeByUUID(uuid);
    if (node == null) {
      throw new IllegalArgumentException("UUID " + uuid.toString() + " is not a valid network element UUID.");
    } else {
      return node.neId();
    }
  }
}
