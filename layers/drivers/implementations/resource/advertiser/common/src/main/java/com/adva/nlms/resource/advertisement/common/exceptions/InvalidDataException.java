/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.advertisement.common.exceptions;


import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Response;

public class InvalidDataException extends WebApplicationException {

    public InvalidDataException() {
        super("Got invalid data for selected NE", Response.Status.SERVICE_UNAVAILABLE);
    }
}
