/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: krz<PERSON><PERSON>tofz
 */

package com.adva.nlms.resource.advertisement.adapters.rest.server.otn;

import com.adva.nlms.mediation.common.rest.MDRestComponent;
import com.adva.nlms.resource.advertisement.common.exceptions.InvalidDataException;
import com.adva.nlms.resource.advertisement.common.exceptions.NoDataException;
import com.adva.nlms.resource.advertisement.fiber.AdvertiseFiberResources;
import com.adva.nlms.resource.advertisement.inventory.AdvertiseInventoryResources;
import com.adva.nlms.resource.advertisement.occupied_resources.AdvertiseOccupiedResources;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import ni.proto.mla.clc.CardResources;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import java.util.List;

@Path("resource-advertiser")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@MDRestComponent(basicAuthentication = true)
public class ResourceAdvertiserController {
  private final Logger logger = LogManager.getLogger(ResourceAdvertiserController.class);
  private final AdvertiseFiberResources advertiseFiberResources;
  private final AdvertiseInventoryResources advertiseInventoryResources;
  private final AdvertiseOccupiedResources advertiseOccupiedResources;

  ResourceAdvertiserController(AdvertiseFiberResources advertiseFiberResources, AdvertiseInventoryResources advertiseInventoryResources,
                               AdvertiseOccupiedResources advertiseOccupiedResources) {
    this.advertiseFiberResources = advertiseFiberResources;
    this.advertiseInventoryResources = advertiseInventoryResources;
    this.advertiseOccupiedResources = advertiseOccupiedResources;
  }

  /**
   * Request URI: "https://localhost:8443/advabase/resource-advertiser/fiber-map/{neId}"
   */
  @GET
  @Path("fiber-map/{neId}")
  @Produces(MediaType.APPLICATION_JSON)
  public String getFiberMap(@PathParam("neId") int neId) throws InvalidDataException {
    var fiberMap = advertiseFiberResources.collectFiberMap(neId);
    try {
      return JsonFormat.printer().print(fiberMap);
    } catch (Exception e) {
      logger.error("Got invalid FiberMap data for NE {}", neId);
      throw new InvalidDataException();
    }
  }

  /**
   * Request URI: "https://localhost:8443/advabase/resource-advertiser/inventory/{neId}"
   */
  @GET
  @Path("inventory/{neId}")
  @Produces(MediaType.APPLICATION_JSON)
  public String getInventory(@PathParam("neId") int neId) throws NoDataException, InvalidDataException {
    var inventory = advertiseInventoryResources.getInventory(neId).orElseThrow(NoDataException::new);
    try {
      return JsonFormat.printer().print(inventory);
    } catch (Exception e) {
      logger.error("Got invalid Inventory data for NE {}", neId);
      throw new InvalidDataException();
    }
  }

  /**
   * Request URI: "https://localhost:8443/advabase/resource-advertiser/occupied-resources/{neId}/{cardAid}"
   */
  @GET
  @Path("occupied-resources/{neId}/{cardAid}")
  @Produces(MediaType.APPLICATION_JSON)
  public String getOccupiedResources(@PathParam("neId") int neId, @PathParam("cardAid") String cardAid) throws NoDataException, InvalidDataException {
    var occupiedResources = advertiseOccupiedResources.getOccupiedResources(neId)
      .stream()
      .filter(cr -> cardAid.equals(cr.getCardId().getName()) && !cr.getPortsList().stream().allMatch(p -> p.getResourcesList().isEmpty()))
      .findFirst().orElseThrow(NoDataException::new);
    try {
      return JsonFormat.printer().print(occupiedResources);
    } catch (Exception e) {
      logger.error("Got invalid Occupied Resources data for NE {} with selected module {}", neId, cardAid);
      throw new InvalidDataException();
    }
  }

  /**
   * Request URI: "https://localhost:8443/advabase/resource-advertiser/clc/{neId}"
   */
  @GET
  @Path("clc/{neId}")
  @Produces(MediaType.APPLICATION_JSON)
  public String getWholeClcData(@PathParam("neId") int neId) throws NoDataException, InvalidDataException {
    var inventory = advertiseInventoryResources.getInventory(neId).orElseThrow(NoDataException::new);
    var occupiedResources = advertiseOccupiedResources.getOccupiedResources(neId)
      .stream()
      .filter(cr -> !cr.getPortsList().stream().allMatch(p -> p.getResourcesList().isEmpty()))
      .toList();
    var fiberMap = advertiseFiberResources.collectFiberMap(neId);
    try {
      String inventoryJson = JsonFormat.printer().print(inventory);
      String occupiedResourcesJson = mapOccupiedResourcesToJsonArray(occupiedResources);
      String fiberMapJson = JsonFormat.printer().print(fiberMap);

      return String.format(
        "{\"Inventory\": %s, \"OccupiedResources\": %s, \"FiberMap\": %s}",
        inventoryJson, occupiedResourcesJson, fiberMapJson
      );
    } catch (Exception e) {
      logger.error("Got invalid CLC data for NE {}", neId);
      throw new InvalidDataException();
    }
  }

  private String mapOccupiedResourcesToJsonArray(List<CardResources> occupiedResources) throws InvalidProtocolBufferException {
    StringBuilder jsonArray = new StringBuilder("[");
    boolean isFirst = true;

    for (CardResources resource : occupiedResources) {
      if (!isFirst) {
        jsonArray.append(",");
      }
      String resourceJson = JsonFormat.printer().print(resource);
      jsonArray.append(resourceJson);
      isFirst = false;
    }

    jsonArray.append("]");
    return jsonArray.toString();
  }
}
