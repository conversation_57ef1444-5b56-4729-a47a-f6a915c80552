/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *   Owner: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.advertisement.adapters.rest.server.eth;

import com.adva.nlms.mediation.common.rest.MDRestComponent;
import com.adva.nlms.resource.advertisement.common.exceptions.InvalidDataException;
import com.adva.nlms.resource.advertisement.common.exceptions.NoDataException;
import com.adva.nlms.resource.crm.model.eth.CrmEthModelDAO;
import com.adva.nlms.resource.crm.model.eth.CrmEthNiTranslator;
import com.google.protobuf.util.JsonFormat;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@Path("resource-advertiser")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@MDRestComponent(basicAuthentication = true)
public class EthResourceAdvertiserController {
  private final Logger logger = LogManager.getLogger(EthResourceAdvertiserController.class);
  private final CrmEthModelDAO crmEthModelDAO;
  private final CrmEthNiTranslator crmEthNiTranslator;

  EthResourceAdvertiserController(CrmEthModelDAO crmEthModelDAO, CrmEthNiTranslator crmEthNiTranslator) {
    this.crmEthModelDAO = crmEthModelDAO;
    this.crmEthNiTranslator = crmEthNiTranslator;
  }

  /**
   * Request URI: "https://localhost:8443/advabase/resource-advertiser/ltp-eth/{neId}/{ltpName}"
   */
  @GET
  @Path("ltp-eth/{neId}/{ltpName}")
  @Produces(MediaType.APPLICATION_JSON)
  public String getLtp(@PathParam("neId") int neId, @PathParam("ltpName") String ltpName) throws NoDataException, InvalidDataException {
    var ltp = crmEthModelDAO.getLtp(neId, ltpName);
    if (ltp == null) {
      throw new NoDataException();
    }
    try {
      var ltpProto = crmEthNiTranslator.translateLtpData(ltp, neId);
      return JsonFormat.printer().print(ltpProto);
    } catch (Exception e) {
      logger.error("Got invalid LTP data for NE {}", neId);
      throw new InvalidDataException();
    }
  }

  /**
   * Request URI: "https://localhost:8443/advabase/resource-advertiser/ttp-eth/{neId}/{ttpName}"
   */
  @GET
  @Path("ttp-eth/{neId}/{ttpName}")
  @Produces(MediaType.APPLICATION_JSON)
  public String getTtp(@PathParam("neId") int neId, @PathParam("ttpName") String ttpName) throws NoDataException, InvalidDataException {
    var ttp = crmEthModelDAO.getTtp(neId, ttpName);
    if (ttp == null) {
      throw new NoDataException();
    }
    try {
      var ttpProto = crmEthNiTranslator.translateTtpData(ttp, neId);
      return JsonFormat.printer().print(ttpProto);
    } catch (Exception e) {
      logger.error("Got invalid TTP data for NE {}", neId);
      throw new InvalidDataException();
    }
  }
}
