/*
 *   Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *   Owner: <PERSON><PERSON><PERSON>
 */
package com.adva.nlms.resource.advertisement.adapters.rest.server.wdm.f8;

import com.adva.nlms.mediation.common.rest.MDRestComponent;
import com.adva.nlms.resource.advertisement.common.exceptions.InvalidDataException;
import com.adva.nlms.resource.advertisement.common.exceptions.NoDataException;
import com.adva.nlms.resource.crm.model.wdm.CrmWdmModelDAO;
import com.adva.nlms.resource.crm.model.wdm.CrmWdmNiTranslator;
import com.google.protobuf.util.JsonFormat;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@Path("resource-advertiser")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@MDRestComponent(basicAuthentication = true)
public class WdmResourceAdvertiserController {
  private final Logger logger = LogManager.getLogger(WdmResourceAdvertiserController.class);
  private final CrmWdmModelDAO crmWdmModelDAO;
  private final CrmWdmNiTranslator crmWdmNiTranslator;

  WdmResourceAdvertiserController(CrmWdmModelDAO crmWdmModelDAO, CrmWdmNiTranslator crmWdmNiTranslator) {
    this.crmWdmModelDAO = crmWdmModelDAO;
    this.crmWdmNiTranslator = crmWdmNiTranslator;
  }

  /**
   * Request URI: {@code https://localhost:8443/advabase/resource-advertiser/ltp/{neId}/{ltpName}}
   */
  @GET
  @Path("ltp/{neId}/{ltpName}")
  @Produces(MediaType.APPLICATION_JSON)
  public String getLtp(@PathParam("neId") int neId, @PathParam("ltpName") String ltpName) throws NoDataException, InvalidDataException {
    var ltp = crmWdmModelDAO.getLtp(neId, ltpName)
      .orElseThrow(NoDataException::new);
    try {
      var ltpProto = crmWdmNiTranslator.translateLTPData(ltp, neId);
      return JsonFormat.printer().print(ltpProto);
    } catch (Exception e) {
      logger.error("Got invalid LTP data for NE {}", neId);
      throw new InvalidDataException();
    }
  }

  /**
   * Request URI: {@code https://localhost:8443/advabase/resource-advertiser/ttp/{neId}/{ttpName}}
   */
  @GET
  @Path("ttp/{neId}/{ttpName}")
  @Produces(MediaType.APPLICATION_JSON)
  public String getTtp(@PathParam("neId") int neId, @PathParam("ttpName") String ttpName) throws NoDataException, InvalidDataException {
    var ttp = crmWdmModelDAO.getTtpFromAid(neId, ttpName)
      .orElseThrow(NoDataException::new);
    try {
      var ttpProto = crmWdmNiTranslator.translateTtpData(ttp);
      return JsonFormat.printer().print(ttpProto);
    } catch (Exception e) {
      logger.error("Got invalid TTP data for NE {}", neId);
      throw new InvalidDataException();
    }
  }

  /**
   * Request URI: {@code https://localhost:8443/advabase/resource-advertiser/resource-group/{neId}/{resourceId}}
   */
  @GET
  @Path("resource-group/{neId}/{resourceId}")
  @Produces(MediaType.APPLICATION_JSON)
  public String getResourceGroup(@PathParam("neId") int neId, @PathParam("resourceId") int resourceId) throws NoDataException, InvalidDataException {
    var resourceGroup = crmWdmModelDAO.getResourceGroupInfo(neId, resourceId);
    if (resourceGroup == null) {
      throw new NoDataException();
    }
    try {
      var resourceGroupProto = crmWdmNiTranslator.translateResourceGroup(resourceGroup);
      return JsonFormat.printer().print(resourceGroupProto);
    } catch (Exception e) {
      logger.error("Got invalid resource group data for NE {}", neId);
      throw new InvalidDataException();
    }
  }
}
