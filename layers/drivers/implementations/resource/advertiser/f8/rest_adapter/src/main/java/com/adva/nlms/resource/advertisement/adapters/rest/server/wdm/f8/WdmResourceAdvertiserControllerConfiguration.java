/*
 *   Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *   Owner: <PERSON><PERSON><PERSON>
 */
package com.adva.nlms.resource.advertisement.adapters.rest.server.wdm.f8;

import com.adva.nlms.resource.crm.model.wdm.CrmWdmModelDAO;
import com.adva.nlms.resource.crm.model.wdm.CrmWdmNiTranslator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Profile("RA-test")
@Configuration
public class WdmResourceAdvertiserControllerConfiguration {
  @Bean
  public WdmResourceAdvertiserController wdmResourceAdvertiserController(CrmWdmModelDAO crmWdmModelDAO,
                                                                         CrmWdmNiTranslator crmWdmNiTranslator) {
    return new WdmResourceAdvertiserController(crmWdmModelDAO, crmWdmNiTranslator);
  }
}
