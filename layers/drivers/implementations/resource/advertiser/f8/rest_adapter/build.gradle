/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: krz<PERSON><PERSON><PERSON><PERSON>z
 */

setupMediationModule(project)

dependencies {

    implementation modep(mod_resource_advertiser_f8_impl)
    implementation modep(mod_rest_infra)
    implementation modep(mod_resource_crm_model_f8)
    implementation modep(mod_resource_advertiser_common)


    implementation libs.spring.context
    implementation libs.jakarta.ws.rs.api
    implementation libs.protobuf.java.util
    implementation libs.nms.ni.model

    implementation libs.log4j.api

    testImplementation libs.bundles.junit.jupiter
    testImplementation libs.mockito.jupiter

}

test {
    useJUnitPlatform()
}
