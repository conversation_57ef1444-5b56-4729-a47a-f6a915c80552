/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

setupMediationModule(project)

dependencies {
    implementation modep (mod_topology_ne_api) //CrmNiTranslatorImpl
    implementation modep(mod_adva_concurrent) // CrmCtrlTaskSchedulerImpl
    implementation modep(mod_nmscommon) // NeObserver - NeTypeIds
    implementation modep(mod_mo_inventory_f8_api)
    implementation modep(mod_optical_parameters_common)
    implementation libs.slf4j.api
    implementation libs.spring.context
    implementation libs.nms.ni.model
    implementation libs.enc.ypdb.commons
    implementation modep( mod_notification_tracing_api )

    testImplementation modep(mod_cap_prov_impl)
    testImplementation libs.bundles.junit.jupiter
    testImplementation libs.mockito.jupiter
    testImplementation libs.assertJ

}

test {
    useJUnitPlatform()
}
