/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;

public class ConstrainingEntry {
  private String interfaceId;
  private ConstrainedResource constrainedResource;
  private Boolean unequipped;

  public String getInterfaceId() {
    return interfaceId;
  }

  public void setInterfaceId(String interfaceId) {
    this.interfaceId = interfaceId;
  }

  public ConstrainedResource getConstrainedResource() {
    return constrainedResource;
  }

  public void setConstrainedResource(ConstrainedResource constrainedResource) {
    this.constrainedResource = constrainedResource;
  }

}
