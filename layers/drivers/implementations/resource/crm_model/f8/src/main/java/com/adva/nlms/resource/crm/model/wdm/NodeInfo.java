/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;


import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class NodeInfo {

  private List<LtpInfo> ltpInfoList;
  private List<TtpInfo> ttpInfos;
  private List<ResourceGroupInfo> resourceGroupInfos;

  public NodeInfo() {
    this.ltpInfoList = new ArrayList<>();
    this.ttpInfos = new ArrayList<>();
    this.resourceGroupInfos = new ArrayList<>();
  }

  public NodeInfo(LtpInfo ltpInfo) {
    this.ltpInfoList = new ArrayList<>();
    this.ttpInfos = new ArrayList<>();
    this.resourceGroupInfos = new ArrayList<>();
    ltpInfoList.add(ltpInfo);
  }

  public NodeInfo(List<ResourceGroupInfo> resourceGroupInfoList) {
    this.ltpInfoList = new ArrayList<>();
    this.ttpInfos = new ArrayList<>();
    this.resourceGroupInfos = new ArrayList<>();
    this.resourceGroupInfos.addAll(resourceGroupInfoList);

  }

  public List<LtpInfo> getLtpInfoList() {		return ltpInfoList;	}

  public void addLtpInfo(LtpInfo ltpInfo){
    ltpInfoList.add(ltpInfo);
  }

  public void addTtpInfo(TtpInfo ttpInfo){    	this.ttpInfos.add(ttpInfo); }

  public List<TtpInfo> getAllTtpInfos(){    	return this.ttpInfos;		}

  public List<ResourceGroupInfo> getAllResourceGroupInfos() {		return resourceGroupInfos;	}

  public void addResourceGroup(ResourceGroupInfo resourceGroupInfo){
    this.resourceGroupInfos.add(resourceGroupInfo);
  }

  public String toString(int nodeId) {
    String ltpList = ltpInfoList.stream().map(LtpInfo::toString).sorted().collect(Collectors.joining(","));
    String ttpList = ttpInfos.stream().map(TtpInfo::toString).sorted().collect(Collectors.joining(","));
    String ressourceList = resourceGroupInfos.stream().map(ResourceGroupInfo::toString).sorted().collect(Collectors.joining(","));
    return "{\"nodeId\":" + nodeId +
            ",\"ltpInfos\":[" + ltpList +
            "],\"ttpInfos\":[" + ttpList +
            "],\"resourceGroupInfos\":[" + ressourceList +
            "]}";
  }
}
