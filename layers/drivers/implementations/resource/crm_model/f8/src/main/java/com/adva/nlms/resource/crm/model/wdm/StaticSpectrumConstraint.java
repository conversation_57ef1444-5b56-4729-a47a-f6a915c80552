/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;

import java.util.List;

public class StaticSpectrumConstraint extends SpectrumConstraint {
  StaticSpectrumConstraint(OneOfType type, String interfaceId) {
    super(type, interfaceId);
  }

  public static StaticSpectrumConstraint generateAllowAllStaticSpectrumConstraint(String otherEndLtpName) {
    var constraintType = new SpectrumConstraint.OneOfType(new SpectrumConstraint.All(), null);
    return new StaticSpectrumConstraint(constraintType, otherEndLtpName);
  }

  public static StaticSpectrumConstraint generateFrequencySlotsStaticSpectrumConstraint(String otherEndLtpName, List<FrequencySlot> frequencySlots) {
    var frequencySlotPackage = new SpectrumConstraint.FrequencySlots(frequencySlots);
    var constraintType = new SpectrumConstraint.OneOfType(frequencySlotPackage);
    return new StaticSpectrumConstraint(constraintType, otherEndLtpName);
  }
}
