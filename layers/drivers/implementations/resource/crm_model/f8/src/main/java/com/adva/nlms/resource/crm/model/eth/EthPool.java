/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.eth;

import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;

import java.util.Objects;

public class EthPool {

    private final LayerQualifier layerQualifier;
    private ResourceDescriptor idleEthResources;
    private ResourceDescriptor busyEthResources;

    public EthPool(LayerQualifier layerQualifier, ResourceDescriptor idleEthResources,
                   ResourceDescriptor busyEthResources) {
        this.layerQualifier = layerQualifier;
        this.idleEthResources = idleEthResources;
        this.busyEthResources = busyEthResources;
    }

    public LayerQualifier getLayerQualifier() { return layerQualifier; }

    public ResourceDescriptor getIdleEthResources() { return idleEthResources; }

    public void setIdleEthResources(ResourceDescriptor idleEthResources) {
        this.idleEthResources = idleEthResources;
    }

    public ResourceDescriptor getBusyEthResources() { return busyEthResources; }

    public void setBusyEthResources(ResourceDescriptor busyEthResources) {
        this.busyEthResources = busyEthResources;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EthPool that = (EthPool) o;
        return Objects.equals(layerQualifier, that.layerQualifier) &&
                Objects.equals(idleEthResources, that.idleEthResources) &&
                Objects.equals(busyEthResources, that.busyEthResources);
    }

    @Override
    public int hashCode() {
        return Objects.hash(layerQualifier, idleEthResources, busyEthResources);
    }

}
