/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.eth;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

public class ContainerSet implements ResourceDescriptor {

    private final List<Container> containers;

    public ContainerSet(List<Container> containers) {
        this.containers = containers;
    }

    public ContainerSet() {
        this.containers = new ArrayList<>();
    }

    public List<Container> getContainers() {
        return containers;
    }

    public Optional<Container> findContainerWithTributaryIds(List<Integer> tributaryIds) {
        return containers.stream()
                .filter(container -> container.getTributaryIds().equals(tributaryIds))
                .findFirst();
    }

    public boolean isEmpty() {
        return containers.isEmpty();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ContainerSet that = (ContainerSet) o;
        return Objects.equals(containers, that.containers);
    }

    @Override
    public int hashCode() {
        return Objects.hash(containers);
    }

}
