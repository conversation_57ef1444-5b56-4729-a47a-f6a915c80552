/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *   Owner: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model;

import ni.msg.EnvelopeOuterClass;
import ni.proto.connection_segment.ConnectionSegmentOuterClass;
import ni.proto.ml.MlSync;
import ni.proto.mla.messages.MlaMessages;

import java.util.List;
import java.util.Optional;

public interface CrmNiTranslator {
  EnvelopeOuterClass.Envelope createSegmentCallbackResponse(String segmentRequestId,
                                                            SegmentCallbackData segmentCallbackData,
                                                            Optional<ConnectionSegmentOuterClass.PowerEqualizationOperation> optEqlzDirection,
                                                            EnvelopeOuterClass.Address incomingSrcAddress,
                                                            EnvelopeOuterClass.Address incomingDstAddress,
                                                            List<String> deletedSegments);

  EnvelopeOuterClass.Envelope createSegmentCallbackAssignRoles(AssignRolesCallback assignRolesCallback);

  EnvelopeOuterClass.Envelope createOutageSegmentCallback(OutageSegmentCallback outageSegmentCallback, AlarmType alarmAction);

  Optional<EnvelopeOuterClass.Envelope> buildEnvelope(int neId, List<MlSync.Data> datas, MlaMessages.Message.TypeCase messageType);

  MlSync.Data mergeDataPayload(List<MlSync.Data> datas);
}
