/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.eth;

import java.util.List;
import java.util.Objects;

// For now Selector isn't used in ETH model due to strict mapping of tributaryIds to tributarySlots
// It will be changed in the future
public class Selector implements ResourceDescriptor {

    private List<Integer> idList;
    private final int numberOfIdsToAllocate;

    public Selector(List<Integer> idList, int numberOfIdsToAllocate) {
        this.idList = idList;
        this.numberOfIdsToAllocate = numberOfIdsToAllocate;
    }

    public List<Integer> getIdList() {
        return idList;
    }

    public void setIdList(List<Integer> idList) {
        this.idList = idList;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Selector that = (Selector) o;
        return Objects.equals(idList, that.idList) &&
                numberOfIdsToAllocate == that.numberOfIdsToAllocate;
    }

    @Override
    public int hashCode() {
        return Objects.hash(idList, numberOfIdsToAllocate);
    }

}
