/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.eth;

import com.adva.nlms.mediation.mo.inventory.resources.Aid;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;

import java.util.List;

public record LtpSncRelatedResources(Aid aid, LayerQualifier layerQualifier, List<Integer> tributaryIds,
                                     List<Integer> tributarySlots) {
}
