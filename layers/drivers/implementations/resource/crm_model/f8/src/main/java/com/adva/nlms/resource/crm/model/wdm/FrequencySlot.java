/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;

import java.util.ArrayList;
import java.util.List;

public record FrequencySlot(int centerFreq, int slotwidth, ChannelType channelType) {
  public static final int DEFAULT_GRAY_BANDWIDTH = 20;
  public static final FrequencySlot EMPTY_SLOT = new FrequencySlot(0, 0, null);

  public static FrequencySlot dwdmFrequencySlot(int centerFreq, int slotwidth) {
    return new FrequencySlot(centerFreq, slotwidth, ChannelType.DWDM);
  }

  public static List<FrequencySlot> fromList(List<Integer> frequencies, List<Integer> bandwidths) {
    int numFrequencies = frequencies.size();
    int numBandwidths = bandwidths.size();
    if (numFrequencies != numBandwidths) {
      throw new IllegalArgumentException("Unequal number of Freq and bandwidth entries contained in SLC");
    }
    List<FrequencySlot> frequencySlots = new ArrayList<>();
    for (int i = 0; i < numFrequencies; i++) {
      frequencySlots.add(FrequencySlot.dwdmFrequencySlot(frequencies.get(i), bandwidths.get(i)));
    }
    return frequencySlots;
  }

  public boolean isEmpty() {
    return centerFreq == 0 || slotwidth == 0;
  }

}
