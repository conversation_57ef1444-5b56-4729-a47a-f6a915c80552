/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.eth;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class EdgeBindingConstraints {

  private List<ConstrainingEntry> constrainingEntryList;
  private String selectedInterfaceId;

  public EdgeBindingConstraints(List<ConstrainingEntry> constrainingEntryList, String selectedInterfaceId) {
    this.constrainingEntryList = constrainingEntryList;
    this.selectedInterfaceId = selectedInterfaceId;
  }

  public EdgeBindingConstraints() {
    this.constrainingEntryList = new ArrayList<>();
  }

  public List<ConstrainingEntry> getConstrainingEntryList() {
    return constrainingEntryList;
  }

  public void setConstrainingEntryList(List<ConstrainingEntry> constrainingEntryList) {
    this.constrainingEntryList = constrainingEntryList;
  }

  public String getSelectedInterfaceId() {
    return selectedInterfaceId;
  }

  public void setSelectedInterfaceId(String selectedInterfaceId) {
    this.selectedInterfaceId = selectedInterfaceId;
  }

  public void clearSelectedInterfaceId() {
    this.selectedInterfaceId = null;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    EdgeBindingConstraints that = (EdgeBindingConstraints) o;
    return Objects.equals(constrainingEntryList, that.constrainingEntryList) &&
            Objects.equals(selectedInterfaceId, that.selectedInterfaceId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(constrainingEntryList, selectedInterfaceId);
  }

}
