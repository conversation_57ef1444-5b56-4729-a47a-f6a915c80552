/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;


import java.util.ArrayList;
import java.util.List;

public class TransitConstraintsInfo {
  private final List<StaticSpectrumConstraint> staticSpectrumConstraints = new ArrayList<>();
  private final List<DynamicSpectrumConstraint> dynamicSpectrumConstraints = new ArrayList<>();

  public void addSpectrumConstraint(SpectrumConstraint spectrumConstraint){
    if(spectrumConstraint instanceof StaticSpectrumConstraint staticSpectrumConstraint) {
      staticSpectrumConstraints.add(staticSpectrumConstraint);
    } else if(spectrumConstraint instanceof DynamicSpectrumConstraint dynamicSpectrumConstraint) {
      dynamicSpectrumConstraints.add(dynamicSpectrumConstraint);
    }
  }

  public List<StaticSpectrumConstraint> getStaticSpectrumConstraints() {
    return staticSpectrumConstraints;
  }

  public List<DynamicSpectrumConstraint> getDynamicSpectrumConstraints() {
    return dynamicSpectrumConstraints;
  }

  public boolean isEmpty() {
    return staticSpectrumConstraints.isEmpty() && dynamicSpectrumConstraints.isEmpty();
  }

  boolean containsConstraint(SpectrumConstraint spectrumConstraint) {
    if (spectrumConstraint instanceof StaticSpectrumConstraint) {
      return staticSpectrumConstraints.stream().anyMatch(constraint -> constraint.equals(spectrumConstraint));
    } else if (spectrumConstraint instanceof DynamicSpectrumConstraint) {
      return dynamicSpectrumConstraints.stream().anyMatch(constraint -> constraint.equals(spectrumConstraint));
    } else {
      return false;
    }
  }
}
