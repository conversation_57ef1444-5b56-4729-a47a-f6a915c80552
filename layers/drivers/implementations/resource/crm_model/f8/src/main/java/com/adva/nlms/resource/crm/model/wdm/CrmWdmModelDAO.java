/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;


import java.util.List;
import java.util.Optional;

/***
 * This class provides the interface to the CRM internal model
 */
public interface CrmWdmModelDAO {
  /***
   * This method is used to fetch the LTP from the internal model based on the AidString
   * @param neId NE Id in the database
   * @param aidString Name of the LTP (i.e. the interface name of the CROMA Degree)
   * @return LTPInfo object from the internal model
   */
  Optional<LtpInfo> getLtpFromAid(int neId, String aidString);


  /***
   * This method is used to fetch the TTP from the internal model based on the AidString
   * @param neId NE Id in the database
   * @param aidString Name of the TTP (i.e. the interface name Alien/N-port of Channel Card)
   * @return Optional containing TTPInfo object from the internal model
   */
  Optional<TtpInfo> getTtpFromAid(int neId, String aidString);

  /***
   * Fetch the TTP based on the ActiveEndPoint
   * @param neId NE Id in the database
   * @param activeEndPoint activeEndPoint of the SLC
   * @return Optional containing TtpInfo
   */
  Optional<TtpInfo> getTtpFromSlcActiveEndpoint(int neId, String activeEndPoint);


  /***
   * Fetch the LTP based on the degree number
   * @param neId NE Id in the database
   * @param degreeNumber degree number of the LTP
   * @return LtpInfo object
   */
  Optional<LtpInfo> getLtpByDegreeNumber(int neId, int degreeNumber);

  /***
   * Deletes the LTP fom the internal model
   * @param neId  NE Id in the database
   * @param degreeNumber degree number present in the LTP
   */
  void removeLtpByDegreeNumber(int neId, int degreeNumber);

  void replaceLtp(int neId, LtpInfo ltpInfo);

  /***
   * Deletes the TTP fom the internal model
   * @param neId NE Id in the database
   * @param aidString TTP name
   * @return {@code true} if any ttp removed, {@code false} otherwise
   */
  boolean removeTtpByPortId(int neId, String aidString);


  /***
   * is the LTP(identified by the degree number) present in the internal model
   * @param neId NE Id in the database
   * @param degreeNumber integer identifier of degree
   * @return {@code true} if LTP is present {@code false} otherwise
   */
  boolean isLtpPresent(int neId, int degreeNumber);


  /***
   * Gets the number of the LTP objects in the internal model
   * @param neId NE Id in the database
   */
  int getLtpsSize(int neId);

  /***
   * Gets the number of the TTP objects in the internal model
   * @param neId NE Id in the database
   */
  int getTtpsSize(int neId);

  /***
   * Gets the number of the TTP objects in the internal model
   * @param neId NE Id in the database
   */
  int getRGroupsSize(int neId);

  /***
   * Gets the list of all LTPs
   * @param neId  NE Id in the database
   */
  List<LtpInfo> getAllLtps(int neId);

  Optional<LtpInfo> getLtp(int neId, String ltpName);

  /***
   * Gets the list of all TTPs
   * @param neId  NE Id in the database
   */
  List<TtpInfo> getAllTtps(int neId);

  /***
   * Adds the spectrum constraint to the LTP based on the degree number
   * @param neId NE Id in the database
   * @param degreeNumber integer identifier of degree
   * @param spectrumConstraint {@link SpectrumConstraint}
   */
  void addSpectrumConstraint(int neId, int degreeNumber, SpectrumConstraint spectrumConstraint);

  boolean isConstraintInLtp(int neId, int degreeNumber, SpectrumConstraint spectrumConstraint);

  boolean isConstraintInTtp(int neId, String aidString, String ltpDegreeName);

  /***
   * Adds the LTP to the internal model
   * @param neId NE Id in the database
   * @param ltpInfo Ltp Object
   */
  void addLtp(int neId, LtpInfo ltpInfo);

  void addTtp(int neId, TtpInfo ttpInfo);

  void addResourceGroup(int neId, ResourceGroupInfo resourceGroupInfo);

  void setTtpActiveEndPoint(int neId, String ttpAidString, String activeEndpoint);

  void removeTtpActiveEndPoint(int neId, String activeEndPoint);

  void removeTtpPortsFromResourceGroup(int neId, int groupId, List<String> portIds);

  Optional<SpectrumInfo> getSpectrumInfo(int neId, int degreeNumber);

  void setSelectedInterfaceIdInTtp(int neId, String ttpAidString, String interfaceId);

  void clearSelectedInterfaceIdInTtp(int neId, String activeEndPoint);

  void setCurrentFrequencySlotInTtp(int neId, String ttpAidString, String degreeName, FrequencySlot flexibleChannel);

  void clearCurrentFrequencySlotInTtp(int neId, String activeEndPoint, String degreeName);

  void addFrequencySlotToLtp(int neId, int degreeNumber, FrequencySlot frequencySlot);

  void deleteFrequencySlotInLtp(int neId, int degreeNumber, int frequency, int slotwidth);

  List<ResourceGroupInfo> getAllResourceGroupInfos(int neId);

  ResourceGroupInfo getResourceGroupInfo(int neId, int id);

  ResourceGroupInfo removeResourceGroupInfo(int neId, int resourceId);

  /***
   * Removes a Static Spectrum Constraint in the LTP whose InterfaceId name matches with the given interfaceId
   * @param neId NE Id in the database
   * @param degreeNumber Degree Number for the LTP
   * @param constraintInterfaceId the interfaceId name which needs to be removed in the Spectrum Constraint of the LTP
   */
  void removeStaticSpectrumConstraintInLtp(int neId, int degreeNumber, String constraintInterfaceId);

  void removeDynamicSpectrumConstraintInLtp(int neId, int degreeNumber, String constraintInterfaceId);

  List<TtpInfo> getAllTtpsFromResourceGroupId(int neID, int resourceGroupId);

  void addNode(int neId, NodeInfo node);

  NodeInfo getNode(int neId);

  boolean isNeInInternalModel(int neId);

  void removeNodeInfo(int neId);

  Optional<TtpInfo> removeTtpBySepUri(int neId, String sepUri);

  Optional<LtpInfo> findLtpByOscInterface(int neId, String ptpUri);

  Optional<TtpInfo> getTtpByPortId(int neId, String portId);

  List<LtpInfo> removeLtpByNameAndDegreeNumber(int neId, String ltpName, int degreeNumber);

  Optional<LtpInfo> getLtpByProtectionGroupAid(int neId, String aidString);
}