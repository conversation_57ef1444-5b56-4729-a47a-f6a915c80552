/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.eth;

import java.util.Objects;

public class Whole implements ResourceDescriptor {

    private String crossAid; // internal field - used when SncDeleteNotification comes
    private String crossedTtpName; // internal field - used for freeing TTP resources when crossed LTP is deleted
    private LtpSncRelatedResources crossedLtpResources; // internal field - used for freeing LTP resources when crossed TTP is deleted

    public Whole(String crossAid, String crossedTtpName) {
        this.crossAid = crossAid;
        this.crossedTtpName = crossedTtpName;
    }

    public Whole(String crossAid, LtpSncRelatedResources crossedLtpResources) {
        this.crossAid = crossAid;
        this.crossedLtpResources = crossedLtpResources;
    }

    public Whole(String crossedTtpName) {
        this.crossedTtpName = crossedTtpName;
    }

    public Whole() {}

    public String getCrossAid() { return crossAid; }

    public String getCrossedTtpName() { return crossedTtpName; }

    public LtpSncRelatedResources getCrossedLtpResources() { return crossedLtpResources; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
      return o != null && getClass() == o.getClass();
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(crossAid);
    }

}
