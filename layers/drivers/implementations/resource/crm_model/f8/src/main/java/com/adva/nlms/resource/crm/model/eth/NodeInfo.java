/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.eth;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class NodeInfo {

  private final List<LtpInfo> ltpInfoList;
  private final List<TtpInfo> ttpInfoList;

  public NodeInfo() {
    this.ltpInfoList = new ArrayList<>();
    this.ttpInfoList = new ArrayList<>();
  }

  public List<LtpInfo> getLtpInfoList() { return ltpInfoList; }

  public void addLtpInfo(LtpInfo ltpInfo){
    ltpInfoList.add(ltpInfo);
  }

  public List<TtpInfo> getTtpInfoList(){ return this.ttpInfoList; }

  public void addTtpInfo(TtpInfo ttpInfo){ this.ttpInfoList.add(ttpInfo); }

  public String toString(int nodeId) {
    String ltpList = ltpInfoList.stream().map(LtpInfo::toString).sorted().collect(Collectors.joining(","));
    String ttpList = ttpInfoList.stream().map(TtpInfo::toString).sorted().collect(Collectors.joining(","));
    return "{\"nodeId\":" + nodeId +
            ",\"ltpInfos\":[" + ltpList +
            "],\"ttpInfos\":[" + ttpList +
            "]}";
  }

}
