/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *   Owner: JakubM
 */

package com.adva.nlms.resource.crm.model.wdm;

import com.adva.nlms.mediation.topology.NEDataProvider;
import ni.proto.inet.IpAddr;
import ni.proto.ml.MlSync;
import ni.proto.mla.LinkTerminationPointOuterClass.LinkTerminationPoint;
import ni.proto.mla.NodeOuterClass;
import ni.proto.mla.ResourceGroupOuterClass;
import ni.proto.mla.TunnelTerminationPointOuterClass.TunnelTerminationPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/*
 * This class translates java object into ProtoBuf format.
 * The goal of this class is to de-couple the whole translation logic from the CrmModel in WDM layer (i.e. CrmF8ModelTask)
 * The goal of the design is that this class should implement an interface defined in business logic of the CrmModel
 */
class CrmWdmNiTranslatorImpl implements CrmWdmNiTranslator {

  private static final String DEFAULT_IP_ADDRESS = "0.0.0.0";
  private static final Logger log = LoggerFactory.getLogger(CrmWdmNiTranslatorImpl.class);
  private final NEDataProvider neDataProvider;
  private final CrmWdmModelDAO crmWdmModelDAO;

  public CrmWdmNiTranslatorImpl(NEDataProvider neDataProvider, CrmWdmModelDAO crmWdmModelDAO){
    this.neDataProvider = neDataProvider;
    this.crmWdmModelDAO = crmWdmModelDAO;
  }

  @Override
  public LinkTerminationPoint translateLTPData(LtpInfo ltpObject, int neId) {
    return LtpNiTranslator.translateLTPData(ltpObject, neId);
  }

  @Override
  public MlSync.Data translateLtpIntoData(int neId, LtpInfo ltpInfo, MlSync.Data.Command command) {
    LinkTerminationPoint ltp = translateLTPData(ltpInfo, neId);
    return convertProtoLTPIntoData(ltp, command, neId);
  }

  @Override
  public TunnelTerminationPoint translateTtpData(TtpInfo ttpInfo) {
    return TtpAndResourceGroupNiTranslator.translateTtpData(ttpInfo);
  }

  @Override
  public ResourceGroupOuterClass.ResourceGroup translateResourceGroup(ResourceGroupInfo resourceGroupInfo) {
    return TtpAndResourceGroupNiTranslator.translateResourceGroup(resourceGroupInfo);
  }

  private MlSync.Data convertProtoLTPIntoData(LinkTerminationPoint ltp, MlSync.Data.Command command, int neId) {
    if(ltp == null) {
      log.error("convertProtoLTPIntoData: Null input LTP value for neId={}", neId);
      return null;
    }

    MlSync.Data.Payload payloadData = MlSync.Data.Payload.newBuilder()
            .setLtp(ltp)
            .build();

    MlSync.Header header = MlSync.Header.newBuilder()
            .setId(ltp.getInterfaceId().getName())
            .setEpoch(String.valueOf(System.currentTimeMillis()))
            .setVersion(1)
            .build();

    IpAddr routerAddress = getRouterIpAddress(neId);

    MlSync.Data.Builder dataBuilder = MlSync.Data.newBuilder()
            .setCommand(command)
            .setHeader(header)
            .setRouter(routerAddress);
    if (command == MlSync.Data.Command.CMD_ADD || command == MlSync.Data.Command.CMD_UPDATE)
      dataBuilder.setPayload(payloadData);

    return dataBuilder.build();
  }

  @Override
  public MlSync.Data getAllTtpsData(int neId) {
    List<TunnelTerminationPoint> ttpsList = new ArrayList<>();
    if(crmWdmModelDAO.getTtpsSize(neId) > 0){
      ttpsList= crmWdmModelDAO.getAllTtps(neId).stream()
              .map(this::translateTtpData)
              .collect(Collectors.toList());
    }

    NodeOuterClass.TunnelTerminationPoints tunnelTerminationPoints = NodeOuterClass.TunnelTerminationPoints.newBuilder()
            .addAllTtps(ttpsList)
            .build();

    MlSync.Data.Payload payloadData = MlSync.Data.Payload.newBuilder()
            .setTtps(tunnelTerminationPoints)
            .build();

    MlSync.Header header = MlSync.Header.newBuilder()
            .setId("TTPs")
            .setEpoch(String.valueOf(System.currentTimeMillis()))
            .setVersion(1)
            .build();

    IpAddr routerAddress = getRouterIpAddress(neId);

    return MlSync.Data.newBuilder()
            .setCommand(MlSync.Data.Command.CMD_UPDATE)
            .setPayload(payloadData)
            .setHeader(header)
            .setRouter(routerAddress)
            .build();
  }

  @Override
  public MlSync.Data getAllRGroupsData(int neId) {
    List<ResourceGroupInfo> resourceGroupInfos ;
    List<ResourceGroupOuterClass.ResourceGroup> resourceGroupList = new ArrayList<>();
    if(crmWdmModelDAO.getRGroupsSize(neId) > 0){
      resourceGroupInfos = crmWdmModelDAO.getAllResourceGroupInfos(neId);

      //Convert resourceGroupInfo to protobuf based
      resourceGroupList = resourceGroupInfos.stream()
              .map(this::translateResourceGroup)
              .collect(Collectors.toList());
    }

    NodeOuterClass.ResourceGroups resourceGroups = NodeOuterClass.ResourceGroups.newBuilder()
            .addAllResourceGroup(resourceGroupList)
            .build();

    MlSync.Data.Payload rGroupsPayloadData = MlSync.Data.Payload.newBuilder()
            .setResourceGroups(resourceGroups)
            .build();

    MlSync.Header rGroupsheader = MlSync.Header.newBuilder()
            .setId("RGroups")
            .setEpoch(String.valueOf(System.currentTimeMillis()))
            .setVersion(1)
            .build();

    IpAddr routerAddress = getRouterIpAddress(neId);

    return MlSync.Data.newBuilder()
            .setCommand(MlSync.Data.Command.CMD_UPDATE)
            .setPayload(rGroupsPayloadData)
            .setHeader(rGroupsheader)
            .setRouter(routerAddress)
            .build();
  }

  private IpAddr getRouterIpAddress(int neId) {
    return IpAddr.newBuilder()
            .setS(neDataProvider.getNeData(neId).getIpAddress() != null ? neDataProvider.getNeData(neId).getIpAddress() : DEFAULT_IP_ADDRESS)
            .build();
  }
}
