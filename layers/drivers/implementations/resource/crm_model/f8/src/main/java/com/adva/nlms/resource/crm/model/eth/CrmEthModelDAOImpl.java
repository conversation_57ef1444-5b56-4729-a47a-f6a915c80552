/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.eth;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

class CrmEthModelDAOImpl implements CrmEthModelDAO {

  private final Map<Integer, NodeInfo> nodeInfoMap = new HashMap<>();

  @Override
  public void removeLtp(int neId, String ltpName) {
    if (getLtpsSize(neId) > 0) {
      nodeInfoMap.get(neId).getLtpInfoList()
        .removeIf(ltpInfo -> Objects.equals(ltpInfo.getName(), ltpName));
    }
  }

  @Override
  public void removeTtp(int neId, String aidString) {
    if (getTtpsSize(neId) > 0) {
      nodeInfoMap.get(neId).getTtpInfoList()
        .removeIf(ttpInfo -> ttpInfo.getName().equals(aidString));
    }
  }

  @Override
  public int getLtpsSize(int neId) {
    int size = 0;
    if (isNeInInternalModel(neId)) {
      size = getAllLtps(neId).size();
    }
    return size;
  }

  @Override
  public int getTtpsSize(int neId) {
    int size = 0;
    if (isNeInInternalModel(neId)) {
      size = getAllTtps(neId).size();
    }
    return size;
  }

  @Override
  public List<LtpInfo> getAllLtps(int neId) {
    return isNeInInternalModel(neId)
      ? nodeInfoMap.get(neId).getLtpInfoList()
      : Collections.emptyList();
  }

  @Override
  public LtpInfo getLtp(int neId, String ltpName) {
    Optional<LtpInfo> optionalLtpInfo = Optional.empty();
    if (getLtpsSize(neId) > 0) {
      optionalLtpInfo = getAllLtps(neId).stream()
              .filter(ltpInfo -> ltpInfo.getName().equals(ltpName))
              .findAny();
    }
    return optionalLtpInfo.orElse(null);
  }

  @Override
  public TtpInfo getTtp(int neId, String ttpName) {
    Optional<TtpInfo> optionalTtpInfo = Optional.empty();
    if (getTtpsSize(neId) > 0) {
      optionalTtpInfo = getAllTtps(neId).stream()
              .filter(ttpInfo -> ttpInfo.getName().equals(ttpName))
              .findAny();
    }
    return optionalTtpInfo.orElse(null);
  }

  @Override
  public List<TtpInfo> getAllTtps(int neId) {
    return isNeInInternalModel(neId)
      ? nodeInfoMap.get(neId).getTtpInfoList()
      : Collections.emptyList();
  }

  @Override
  public void addLtp(int neId, LtpInfo ltpInfo) {
    if (isNeInInternalModel(neId)) {
      nodeInfoMap.get(neId).addLtpInfo(ltpInfo);
    } else {
      NodeInfo nodeInfo = new NodeInfo();
      nodeInfo.addLtpInfo(ltpInfo);
      nodeInfoMap.put(neId, nodeInfo);
    }
  }

  @Override
  public void addTtp(int neId, TtpInfo ttpInfo) {
    if (isNeInInternalModel(neId)) {
      nodeInfoMap.get(neId).addTtpInfo(ttpInfo);
    } else {
      NodeInfo nodeInfo = new NodeInfo();
      nodeInfo.addTtpInfo(ttpInfo);
      nodeInfoMap.put(neId, nodeInfo);
    }
  }

  public boolean isNeInInternalModel(int neId) {
    return nodeInfoMap.containsKey(neId);
  }

  @Override
  public void removeNodeInfo(int neId) {
    nodeInfoMap.remove(neId);
  }

  @Override
  public String toString() {
    return "{\"nodeInfoMap\":[" +
      nodeInfoMap.entrySet().stream().map(this::convert).collect(Collectors.joining(",")) +
      "]}";
  }

  private String convert(Map.Entry<Integer, NodeInfo> entry) {
    return entry.getValue().toString(entry.getKey());
  }

}
