/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;

import java.util.Optional;

public sealed interface ConstrainedResource permits Tunable, NonTunable {
  String getName();
  ChannelType getChannelType();
  Optional<FrequencySlot> getCurrentFrequencySlot();
  void setFrequencySlot(FrequencySlot frequencySlot);
  void clearFrequencySlotAndBandwidth();
}
