/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *   Owner: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.eth;

import ni.proto.ml.MlSync;
import ni.proto.mla.LinkTerminationPointOuterClass;
import ni.proto.mla.TunnelTerminationPointOuterClass;

public interface CrmEthNiTranslator {

  LinkTerminationPointOuterClass.LinkTerminationPoint translateLtpData(LtpInfo ltpObject, int neId);

  MlSync.Data translateLtpIntoData(int neId, LtpInfo ltpInfo, MlSync.Data.Command command);

  TunnelTerminationPointOuterClass.TunnelTerminationPoint translateTtpData(TtpInfo ttpInfo, int neId);

  MlSync.Data getAllTtpsData(int neId);

}
