/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.eth;

import java.util.List;
import java.util.Objects;

public class Container implements ResourceDescriptor {

    private final List<Integer> tributaryIds;
    private final List<Integer> tributarySlots;
    private String crossAid; // internal field - stored in TtpInfo only - used when SncDeleteNotification comes
    private String crossedTtpName; // internal field - used for freeing TTP resources when crossed LTP is deleted
    private LtpSncRelatedResources crossedLtpResources; // internal field - used for freeing LTP resources when crossed TTP is deleted

    public Container(List<Integer> tributaryIds, List<Integer> tributarySlots, String crossAid, String crossedTtpName) {
        this.tributaryIds = tributaryIds;
        this.tributarySlots = tributarySlots;
        this.crossAid = crossAid;
        this.crossedTtpName = crossedTtpName;
    }

    public Container(List<Integer> tributaryIds, List<Integer> tributarySlots, String crossAid, Ltp<PERSON>ncRelatedResources crossedLtpResources) {
        this.tributaryIds = tributaryIds;
        this.tributarySlots = tributarySlots;
        this.crossAid = crossAid;
        this.crossedLtpResources = crossedLtpResources;
    }

    public Container(List<Integer> tributaryIds, List<Integer> tributarySlots, String crossedTtpName) {
        this.tributaryIds = tributaryIds;
        this.tributarySlots = tributarySlots;
        this.crossedTtpName = crossedTtpName;
    }

    public Container(List<Integer> tributaryIds, List<Integer> tributarySlots) {
        this.tributaryIds = tributaryIds;
        this.tributarySlots = tributarySlots;
    }

    public List<Integer> getTributaryIds() {
        return tributaryIds;
    }

    public List<Integer> getTributarySlots() {
        return tributarySlots;
    }

    public String getCrossAid() { return crossAid; }

    public String getCrossedTtpName() { return crossedTtpName; }

    public LtpSncRelatedResources getCrossedLtpResources() { return crossedLtpResources; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Container that = (Container) o;
        return Objects.equals(tributaryIds, that.tributaryIds) &&
          Objects.equals(tributarySlots, that.tributarySlots);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tributaryIds, tributarySlots);
    }

}
