/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalmi
 */

package com.adva.nlms.resource.crm.model.wdm;

public final class LtpInfoBuilder {
  private String ltpName;
  private String omsTerminationId;
  private SpectrumInfo spectrumInfo;
  private int degreeNumber;
  private boolean isFixedDegree;
  private TransitConstraintsInfo constraints;
  private OSCSignal oscSignal;
  private String protectionGroupAid;

  private LtpInfoBuilder() {
  }

  public static LtpInfoBuilder newBuilder() {
    return new LtpInfoBuilder();
  }

  public static LtpInfoBuilder newBuilder(LtpInfo ltpInfo) {

    return new LtpInfoBuilder()
      .withLtpName(ltpInfo.getName())
      .withOmsTerminationId(ltpInfo.getOmsTerminationId())
      .withSpectrumInfo(ltpInfo.getSpectrumInfo())
      .withDegreeNumber(ltpInfo.getDegreeNumber())
      .withIsFixedDegree(ltpInfo.isFixedDegree())
      .withConstraints(ltpInfo.getConstraints())
      .withOscSignal(ltpInfo.getOscSignal())
      .withProtectionGroupAid(ltpInfo.getProtectionGroupAid());
  }

  public LtpInfoBuilder withLtpName(String ltpName) {
    this.ltpName = ltpName;
    return this;
  }

  public LtpInfoBuilder withOmsTerminationId(String omsTerminationId) {
    this.omsTerminationId = omsTerminationId;
    return this;
  }

  public LtpInfoBuilder withSpectrumInfo(SpectrumInfo spectrumInfo) {
    this.spectrumInfo = spectrumInfo;
    return this;
  }

  public LtpInfoBuilder withDegreeNumber(int degreeNumber) {
    this.degreeNumber = degreeNumber;
    return this;
  }

  public LtpInfoBuilder withIsFixedDegree(boolean isFixedDegree) {
    this.isFixedDegree = isFixedDegree;
    return this;
  }

  public LtpInfoBuilder withConstraints(TransitConstraintsInfo constraints) {
    this.constraints = constraints;
    return this;
  }

  public LtpInfoBuilder withOscSignal(OSCSignal oscSignal) {
    this.oscSignal = oscSignal;
    return this;
  }

  public LtpInfoBuilder withProtectionGroupAid(String protectionGroupAid) {
    this.protectionGroupAid = protectionGroupAid;
    return this;
  }

  public LtpInfo build() {
    return new LtpInfo(ltpName, omsTerminationId, spectrumInfo, degreeNumber, isFixedDegree, constraints, oscSignal, protectionGroupAid);
  }
}
