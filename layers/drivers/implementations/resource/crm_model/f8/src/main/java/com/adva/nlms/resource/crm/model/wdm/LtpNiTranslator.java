/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;

import com.adva.nlms.resource.crm.model.OSCStatusTranslator;
import ni.proto.external.common.signal_description.FlexgridParams;
import ni.proto.external.common.signal_description.LspSwitchingType;
import ni.proto.inet.InterfaceId;
import ni.proto.mla.ChannelOuterClass;
import ni.proto.mla.FrequencySlotOuterClass;
import ni.proto.mla.LinkTerminationPointOuterClass;
import ni.proto.mla.SpectrumConstraintOuterClass;
import ni.proto.mla.SpectrumOuterClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

class LtpNiTranslator {
  private static final Logger log = LoggerFactory.getLogger(LtpNiTranslator.class);

  private LtpNiTranslator() {
    throw new UnsupportedOperationException("Util class. Creating an instance of this class is forbidden.");
  }

  static LinkTerminationPointOuterClass.LinkTerminationPoint translateLTPData(LtpInfo ltpObject, int neId){
    if(ltpObject == null) {
      log.error("translateLTPData: null input arguments in neId={}", neId);
      return null;
    }
    Objects.requireNonNull(ltpObject.getName(),"LTP name must not be null");
    Objects.requireNonNull(ltpObject.getSpectrumInfo(),"Spectrum info must not be null");
    Objects.requireNonNull(ltpObject.getConstraints(),"Constraints must not be null");

    LinkTerminationPointOuterClass.LinkTerminationPoint.TransitConstraints transitConstraints = generateNiTransitConstraints(ltpObject);
    LspSwitchingType layer = LspSwitchingType.LSP_SWITCHING_TYPE_LSC;
    SpectrumOuterClass.Spectrum spectrum = generateNiLtpSpectrum(ltpObject.getSpectrumInfo());

    InterfaceId name = InterfaceId.newBuilder().setName(ltpObject.getName()).build();

    LinkTerminationPointOuterClass.LinkTerminationPoint.Status status = LinkTerminationPointOuterClass.LinkTerminationPoint.Status.newBuilder()
      .setOscStatus(OSCStatusTranslator.toNiOSCStatus(ltpObject.getOscSignal().oscStatus()))
      .setDataPlaneStatus(LinkTerminationPointOuterClass.DataPlaneStatus.DATAPLANE_STATUS_UNKNOWN)
      .build();

    LinkTerminationPointOuterClass.LinkTerminationPoint.Resources resources = LinkTerminationPointOuterClass.LinkTerminationPoint.Resources.newBuilder()
      .addSpectrum(spectrum)
      .build();

    var builder = LinkTerminationPointOuterClass.LinkTerminationPoint.newBuilder()
      .setLayer(layer)
      .setInterfaceId(name)
      .setResources(resources)
      .setTransitConstraints(transitConstraints)
      .setStatus(status);
    if (ltpObject.getOmsTerminationId() != null) {
      builder.setOmsTerminationId(ltpObject.getOmsTerminationId());
    }
    return builder.build();
  }

  private static LinkTerminationPointOuterClass.LinkTerminationPoint.TransitConstraints generateNiTransitConstraints(LtpInfo ltpObject) {
    TransitConstraintsInfo transitConstraints = ltpObject.getConstraints();
    if (transitConstraints == null || transitConstraints.isEmpty()) {
      return generateAllowNoneTransitConstraintDueToFNMD74931(ltpObject);
    }

    var internalStaticSpectrumConstraints = transitConstraints.getStaticSpectrumConstraints();
    var internalDynamicSpectrumConstraints = transitConstraints.getDynamicSpectrumConstraints();

    List<SpectrumConstraintOuterClass.SpectrumConstraint> staticSpectrumConstraints = new ArrayList<>();
    List<SpectrumConstraintOuterClass.SpectrumConstraint> dynamicSpectrumConstraints = new ArrayList<>();

    internalStaticSpectrumConstraints.forEach(staticSpectrumConstraint -> staticSpectrumConstraints.add(generateNiSpectrumConstraint(staticSpectrumConstraint)));
    internalDynamicSpectrumConstraints.forEach(dynamicSpectrumConstraint -> dynamicSpectrumConstraints.add(generateNiSpectrumConstraint(dynamicSpectrumConstraint)));

    return LinkTerminationPointOuterClass.LinkTerminationPoint.TransitConstraints.newBuilder()
      .addAllStaticSpectrumConstraint(staticSpectrumConstraints)
      .addAllDynamicSpectrumConstraint(dynamicSpectrumConstraints)
      .build();
  }

  /***
   * This method fixes the defect FNMD-74931. Refer to polarion for more details.
   * The fix is provided here so as have very little impact on the existing code in the CrmModel.
   * This fix is to overcome an issue in NI(PCE) w.r.t F7.
   * @param ltpObject  LTPInfo
   * @return a protobuf transit constraint where the constraint is of type ALLOWNONE to itself
   */
  private static LinkTerminationPointOuterClass.LinkTerminationPoint.TransitConstraints generateAllowNoneTransitConstraintDueToFNMD74931(LtpInfo ltpObject) {
    InterfaceId ltpInterfaceId = InterfaceId.newBuilder().setName(ltpObject.getName()).build();
    SpectrumConstraintOuterClass.SpectrumConstraint staticSpectrumConstraintToItself =  SpectrumConstraintOuterClass.SpectrumConstraint.newBuilder()
      .setInterfaceId(ltpInterfaceId)
      .setConstraint(SpectrumConstraintOuterClass.SpectrumConstraint.Constraint.newBuilder().setAllowNone(SpectrumConstraintOuterClass.SpectrumConstraint.AllowNone.newBuilder().build()).build())
      .build();
    return LinkTerminationPointOuterClass.LinkTerminationPoint.TransitConstraints.newBuilder()
      .addStaticSpectrumConstraint(staticSpectrumConstraintToItself)
      .build();
  }

  private static SpectrumConstraintOuterClass.SpectrumConstraint generateNiSpectrumConstraint(SpectrumConstraint spectrumConstraint) {
    InterfaceId interfaceId = InterfaceId.newBuilder().setName(spectrumConstraint.getInterfaceId()).build();
    SpectrumConstraintOuterClass.SpectrumConstraint niSpectrumConstraint = SpectrumConstraintOuterClass.SpectrumConstraint.newBuilder()
      .setInterfaceId(interfaceId).build();

    if (spectrumConstraint.getType().hasAll()) {
      niSpectrumConstraint = niSpectrumConstraint.toBuilder()
        .setConstraint(SpectrumConstraintOuterClass.SpectrumConstraint.Constraint.newBuilder().setAllowAll(SpectrumConstraintOuterClass.SpectrumConstraint.AllowAll.newBuilder().build()).build())
        .build();
    }
    if (spectrumConstraint.getType().hasNone()) {
      niSpectrumConstraint = niSpectrumConstraint.toBuilder()
        .setConstraint(SpectrumConstraintOuterClass.SpectrumConstraint.Constraint.newBuilder().setAllowNone(SpectrumConstraintOuterClass.SpectrumConstraint.AllowNone.newBuilder().build()).build())
        .build();
    }
    if (spectrumConstraint.getType().hasFrequencySlots()) {
      niSpectrumConstraint = niSpectrumConstraint.toBuilder()
        .setConstraint(SpectrumConstraintOuterClass.SpectrumConstraint.Constraint.newBuilder().setFrequencySlots(generateNiFrequencySlots(spectrumConstraint.getType().getFrequencySlots())))
        .build();
    }

    return niSpectrumConstraint;
  }

  private static SpectrumConstraintOuterClass.SpectrumConstraint.FrequencySlots generateNiFrequencySlots(SpectrumConstraint.FrequencySlots frequencySlots){
    List<FrequencySlotOuterClass.FrequencySlot> frequencySlotList = new ArrayList<>();
    frequencySlots.getFrequencySlotList().forEach( frequencySlot -> frequencySlotList.add(generateNIFrequencySlot(frequencySlot)));
    return SpectrumConstraintOuterClass.SpectrumConstraint.FrequencySlots.newBuilder().addAllFrequencySlot(frequencySlotList).build();
  }

  private static  FrequencySlotOuterClass.FrequencySlot generateNIFrequencySlot(FrequencySlot internalFrequencySlot) {
    if (internalFrequencySlot.channelType() != ChannelType.DWDM) {
      log.error("CrmWdmNiTranslator: FrequencySlot with ChannelType {} is not supported", internalFrequencySlot.channelType());
    }
    double slotWidthInMHz = internalFrequencySlot.slotwidth();
    double cfInGHz = internalFrequencySlot.centerFreq()/1000.0;

    ChannelOuterClass.Channel.Dwdm dwdm = ChannelOuterClass.Channel.Dwdm.newBuilder().setChannel(cfInGHz).build();
    ChannelOuterClass.Channel channelCenterFrequency = ChannelOuterClass.Channel.newBuilder().setDwdm(dwdm).build();
    FlexgridParams.SlotWidth flexGridParamsSlotWidth = convertSlotWidthFromMHz((int)slotWidthInMHz);
    if(flexGridParamsSlotWidth == null){
      log.error("CrmWdmNiTranslator: slotwidth is NULL for sloWithInMHz = {}", slotWidthInMHz);
      return FrequencySlotOuterClass.FrequencySlot.newBuilder().build();
    }
    FrequencySlotOuterClass.FrequencySlot.SlotWidth freqSlotSlotWidth = FrequencySlotOuterClass.FrequencySlot.SlotWidth.newBuilder().setSlotWidth(flexGridParamsSlotWidth).build();

    return FrequencySlotOuterClass.FrequencySlot.newBuilder().setCenterFrequency(channelCenterFrequency).setSlotWidth(freqSlotSlotWidth).build();
  }

  private static  SpectrumOuterClass.Spectrum.SlotWidthSupport generateNiSlotWidthSupport(SpectrumInfo spectrumInfo) {
    SpectrumOuterClass.Spectrum.SlotWidthSupport.Builder slotWidthSupportBuilder = SpectrumOuterClass.Spectrum.SlotWidthSupport.newBuilder();
    if(spectrumInfo.isOpaque()) {
      return slotWidthSupportBuilder.setOpaque(SpectrumOuterClass.Spectrum.SlotWidthSupport.Opaque.newBuilder().build()).build();
    } else {
      return slotWidthSupportBuilder.setRange(mapSlotWidthSupport(spectrumInfo.getSlotWidthSupport())).build();
    }
  }

  private static  SpectrumOuterClass.Spectrum.Data generateNiSpectrumData(SpectrumInfo spectrumInfo) {
    SpectrumOuterClass.Spectrum.Data.Builder builder = SpectrumOuterClass.Spectrum.Data.newBuilder();
    List<FrequencySlot> occupiedFrequencySlots = spectrumInfo.getUsedFrequencySlots();
    List<FrequencySlotOuterClass.FrequencySlot> frequencySlotList = generateNiSymmetricFrequencySlots(occupiedFrequencySlots);
    SpectrumOuterClass.Spectrum.Data.SymmetricOccupiedFrequencySlot symmetricOccupiedFrequencySlot = SpectrumOuterClass.Spectrum.Data.SymmetricOccupiedFrequencySlot.newBuilder().addAllFrequencySlot(frequencySlotList).build();
    return builder.setSymmetricOccupiedFrequencySlot(symmetricOccupiedFrequencySlot).build();
  }

  private static List<FrequencySlotOuterClass.FrequencySlot> generateNiSymmetricFrequencySlots(List<FrequencySlot> occupiedFrequencySlots) {
    return occupiedFrequencySlots.stream().map(LtpNiTranslator::convertFrequencySlot).toList();
  }

  private static FrequencySlotOuterClass.FrequencySlot convertFrequencySlot(FrequencySlot internalFrequencySlot) {
    double cfInGHz = internalFrequencySlot.centerFreq()/1000.0;
    ChannelOuterClass.Channel.Dwdm dwdm = ChannelOuterClass.Channel.Dwdm.newBuilder().setChannel(cfInGHz).build();
    ChannelOuterClass.Channel channel = ChannelOuterClass.Channel.newBuilder().setDwdm(dwdm).build();

    FlexgridParams.SlotWidth slotWidth = convertSlotWidthFromMHz(internalFrequencySlot.slotwidth());
    FrequencySlotOuterClass.FrequencySlot.SlotWidth freqSlotSlotWidth = FrequencySlotOuterClass.FrequencySlot.SlotWidth.newBuilder().setSlotWidth(slotWidth).build();

    return FrequencySlotOuterClass.FrequencySlot.newBuilder()
      .setCenterFrequency(channel)
      .setSlotWidth(freqSlotSlotWidth)
      .build();
  }

  private static SpectrumOuterClass.Spectrum.Type mapSpectrumType(SpectrumInfo.Type spectrumInfoType) {
      return switch (spectrumInfoType) {
        case TYPE_FREQUENCY -> SpectrumOuterClass.Spectrum.Type.TYPE_FREQUENCY;
        case TYPE_WAVELENGTH -> SpectrumOuterClass.Spectrum.Type.TYPE_WAVELENGTH;
        case TYPE_GRAY -> SpectrumOuterClass.Spectrum.Type.TYPE_GRAY;
        case TYPE_UNDEFINED -> SpectrumOuterClass.Spectrum.Type.TYPE_UNDEFINED;
      };
  }

  private static SpectrumOuterClass.Spectrum generateNiLtpSpectrum(SpectrumInfo spectrumInfo) {
    var builder = SpectrumOuterClass.Spectrum.newBuilder();
    if (spectrumInfo.getType() == SpectrumInfo.Type.TYPE_UNDEFINED) {
      return builder.build();
    }
    SpectrumOuterClass.Spectrum.Data spectrumData = generateNiSpectrumData(spectrumInfo);

    int minSlotWidth = spectrumInfo.getSlotWidthSupport().min();

    SpectrumOuterClass.Spectrum.Capability capability = spectrumInfo.isOpaque() ?
      SpectrumOuterClass.Spectrum.Capability.CAP_TRANSIT_ADD_DROP_OPAQUE : SpectrumOuterClass.Spectrum.Capability.CAP_TRANSIT_ADD_DROP;

    SpectrumOuterClass.Spectrum.SlotWidthSupport slotWidthSupport = generateNiSlotWidthSupport(spectrumInfo);
    var spectrumType = mapSpectrumType(spectrumInfo.getType());

    return builder
      .setFirst(spectrumInfo.getFirst())
      .setFirstCF((int) (spectrumInfo.getFirst() + 0.5 * minSlotWidth))
      .setLast(spectrumInfo.getLast())
      .setStepCF(spectrumInfo.getStepCf())
      .setSpacing(spectrumInfo.getSpacing())
      .setLastCF((int) (spectrumInfo.getLast() - 0.5 * minSlotWidth))
      .setSlotWidthSupport(slotWidthSupport)
      .setType(spectrumType)
      .setCapability(capability)
      .setData(spectrumData)
      .build();
  }

  private static SpectrumOuterClass.Spectrum.SlotWidthSupport.Range mapSlotWidthSupport(SpectrumInfo.SlotWidthSupport slotWidthSupport) {
    return SpectrumOuterClass.Spectrum.SlotWidthSupport.Range.newBuilder()
      .setMin(slotWidthSupport.min())
      .setMax(slotWidthSupport.max())
      .setStep(slotWidthSupport.step())
      .build();
  }

  /*
   This method contains the all the SlotWidths(based on RD-32RS) that are expected to be supported in Release 13.1
   It is based on the ENUM defined in ni.proto.external.common.signal_description.FlexgridParams.SlotWidth
   */
  private static final Map<Integer, FlexgridParams.SlotWidth> supportedSlotWidth = Map.<Integer, FlexgridParams.SlotWidth>ofEntries(
    Map.entry(37500, FlexgridParams.SlotWidth.SLOT_WIDTH_37p5G),
    Map.entry(43750, FlexgridParams.SlotWidth.SLOT_WIDTH_43p75G),
    Map.entry(50000, FlexgridParams.SlotWidth.SLOT_WIDTH_50G),
    Map.entry(56250, FlexgridParams.SlotWidth.SLOT_WIDTH_56p25G),
    Map.entry(62500, FlexgridParams.SlotWidth.SLOT_WIDTH_62p5G),
    Map.entry(68750, FlexgridParams.SlotWidth.SLOT_WIDTH_68p75G),
    Map.entry(75000, FlexgridParams.SlotWidth.SLOT_WIDTH_75G),
    Map.entry(81250, FlexgridParams.SlotWidth.SLOT_WIDTH_81p25G),
    Map.entry(87500, FlexgridParams.SlotWidth.SLOT_WIDTH_87p5G),
    Map.entry(93750, FlexgridParams.SlotWidth.SLOT_WIDTH_93p75G),
    Map.entry(100000, FlexgridParams.SlotWidth.SLOT_WIDTH_100G),
    Map.entry(106250, FlexgridParams.SlotWidth.SLOT_WIDTH_106p25G),
    Map.entry(112500, FlexgridParams.SlotWidth.SLOT_WIDTH_112p5G),
    Map.entry(118750, FlexgridParams.SlotWidth.SLOT_WIDTH_118p75G),
    Map.entry(125000, FlexgridParams.SlotWidth.SLOT_WIDTH_125G),
    Map.entry(131250, FlexgridParams.SlotWidth.SLOT_WIDTH_131p25G),
    Map.entry(137500, FlexgridParams.SlotWidth.SLOT_WIDTH_137p5G),
    Map.entry(143750, FlexgridParams.SlotWidth.SLOT_WIDTH_143p75G),
    Map.entry(150000, FlexgridParams.SlotWidth.SLOT_WIDTH_150G),
    Map.entry(156250, FlexgridParams.SlotWidth.SLOT_WIDTH_156p25G),
    Map.entry(162500, FlexgridParams.SlotWidth.SLOT_WIDTH_162p5G),
    Map.entry(168750, FlexgridParams.SlotWidth.SLOT_WIDTH_168p75G),
    Map.entry(175000, FlexgridParams.SlotWidth.SLOT_WIDTH_175G),
    Map.entry(181250, FlexgridParams.SlotWidth.SLOT_WIDTH_181p25G),
    Map.entry(187500, FlexgridParams.SlotWidth.SLOT_WIDTH_187p5G),
    Map.entry(193750, FlexgridParams.SlotWidth.SLOT_WIDTH_193p75G),
    Map.entry(200000, FlexgridParams.SlotWidth.SLOT_WIDTH_200G),
    Map.entry(206250, FlexgridParams.SlotWidth.SLOT_WIDTH_206p25G),
    Map.entry(212500, FlexgridParams.SlotWidth.SLOT_WIDTH_212p5G),
    Map.entry(218750, FlexgridParams.SlotWidth.SLOT_WIDTH_218p75G),
    Map.entry(225000, FlexgridParams.SlotWidth.SLOT_WIDTH_225G),
    Map.entry(231250, FlexgridParams.SlotWidth.SLOT_WIDTH_231p25G),
    Map.entry(237500, FlexgridParams.SlotWidth.SLOT_WIDTH_237p5G),
    Map.entry(243750, FlexgridParams.SlotWidth.SLOT_WIDTH_243p75G),
    Map.entry(250000, FlexgridParams.SlotWidth.SLOT_WIDTH_250G),
    Map.entry(256250, FlexgridParams.SlotWidth.SLOT_WIDTH_256p25G),
    Map.entry(262500, FlexgridParams.SlotWidth.SLOT_WIDTH_262p5G),
    Map.entry(268750, FlexgridParams.SlotWidth.SLOT_WIDTH_268p75G),
    Map.entry(275000, FlexgridParams.SlotWidth.SLOT_WIDTH_275G),
    Map.entry(281250, FlexgridParams.SlotWidth.SLOT_WIDTH_281p25G),
    Map.entry(287500, FlexgridParams.SlotWidth.SLOT_WIDTH_287p5G),
    Map.entry(293750, FlexgridParams.SlotWidth.SLOT_WIDTH_293p75G),
    Map.entry(300000, FlexgridParams.SlotWidth.SLOT_WIDTH_300G),
    Map.entry(306250, FlexgridParams.SlotWidth.SLOT_WIDTH_306p25G),
    Map.entry(312500, FlexgridParams.SlotWidth.SLOT_WIDTH_312p5G),
    Map.entry(318750, FlexgridParams.SlotWidth.SLOT_WIDTH_318p75G),
    Map.entry(325000, FlexgridParams.SlotWidth.SLOT_WIDTH_325G),
    Map.entry(331250, FlexgridParams.SlotWidth.SLOT_WIDTH_331p25G),
    Map.entry(337500, FlexgridParams.SlotWidth.SLOT_WIDTH_337p5G),
    Map.entry(343750, FlexgridParams.SlotWidth.SLOT_WIDTH_343p75G),
    Map.entry(350000, FlexgridParams.SlotWidth.SLOT_WIDTH_350G),
    Map.entry(356250, FlexgridParams.SlotWidth.SLOT_WIDTH_356p25G),
    Map.entry(362500, FlexgridParams.SlotWidth.SLOT_WIDTH_362p5G),
    Map.entry(368750, FlexgridParams.SlotWidth.SLOT_WIDTH_368p75G),
    Map.entry(375000, FlexgridParams.SlotWidth.SLOT_WIDTH_375G),
    Map.entry(381250, FlexgridParams.SlotWidth.SLOT_WIDTH_381p25G),
    Map.entry(387500, FlexgridParams.SlotWidth.SLOT_WIDTH_387p5G),
    Map.entry(393750, FlexgridParams.SlotWidth.SLOT_WIDTH_393p75G),
    Map.entry(400000, FlexgridParams.SlotWidth.SLOT_WIDTH_400G)
  );

  private static FlexgridParams.SlotWidth convertSlotWidthFromMHz(int slotWidthMHz) {
    return supportedSlotWidth.get(slotWidthMHz);
  }
}
