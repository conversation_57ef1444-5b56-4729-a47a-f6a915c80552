/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;

import com.adva.nlms.mediation.topology.NEDataProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CrmWdmModelConfiguration {
  @Bean
  public CrmWdmModelDAO crmWdmModelDAO() {
    return new CrmWdmModelDAOImpl();
  }

  @Bean
  public CrmWdmNiTranslator crmWdmNiTranslator(NEDataProvider neDataProvider, CrmWdmModelDAO crmWdmModelDAO) {
    return new CrmWdmNiTranslatorImpl(neDataProvider, crmWdmModelDAO);
  }
}
