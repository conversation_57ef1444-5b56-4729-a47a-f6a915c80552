/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;


import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;


public class SpectrumConstraint {
  private final OneOfType type;
  private final String interfaceId;

  protected SpectrumConstraint(OneOfType type, String interfaceId) {
    this.type = type;
    this.interfaceId = interfaceId;
  }

  public OneOfType getType() {
    return type;
  }

  public String getInterfaceId() {
    return interfaceId;
  }

  public static class All {
  }

  public static class None {
  }

  public record FrequencySlots(Set<FrequencySlot> frequencySlotList) {
    public FrequencySlots(List<FrequencySlot> frequencySlotList) {
      this(new HashSet<>(frequencySlotList));
    }

    public Set<FrequencySlot> getFrequencySlotList() {
      return frequencySlotList;
    }

    public void addFrequencySlot(FrequencySlot frequencySlot) {
      this.frequencySlotList.add(frequencySlot);
    }

    public boolean containsFrequencySlot(FrequencySlot frequencySlot) {
      return frequencySlotList.contains(frequencySlot);
    }
  }

  public static class OneOfType {
    All all;
    None none;
    FrequencySlots frequencySlots;

    public OneOfType(All all, None none) {
      this.all = all;
      this.none = none;
    }

    public OneOfType(FrequencySlots frequencySlots) {
      this.frequencySlots = frequencySlots;
    }

    public All getAll() {
      return all;
    }

    public void setAll(All all) {
      this.all = all;
    }

    public boolean hasAll() {
      return all != null;
    }

    public boolean hasNone() {
      return none != null;
    }

    public boolean hasFrequencySlots() {
      return frequencySlots != null;
    }

    public FrequencySlots getFrequencySlots() {
      return frequencySlots;
    }

    @Override
    public boolean equals(Object o) {
      if (this == o) return true;
      if (o == null || getClass() != o.getClass()) return false;
      OneOfType other = (OneOfType) o;
      if (this.hasAll() && other.hasAll())
        return true;
      else if (this.hasNone() && other.hasNone())
        return true;
      else
        return this.hasFrequencySlots() && other.hasFrequencySlots();
    }

    @Override
    public int hashCode() {
      return Objects.hash(all, none, frequencySlots);
    }
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    SpectrumConstraint other = (SpectrumConstraint) o;

    if (!this.interfaceId.equals(other.interfaceId))
      return false;

    if (!this.getType().equals(other.getType()))
      return false;

    if (this.getType().hasFrequencySlots()) {
      return this.getType().getFrequencySlots().equals(other.getType().getFrequencySlots());
    }

    return true;
  }

  @Override
  public int hashCode() {
    return Objects.hash(type, interfaceId);
  }
}
