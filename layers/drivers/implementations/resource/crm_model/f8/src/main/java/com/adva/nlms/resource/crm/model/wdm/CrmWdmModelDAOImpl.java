/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ObjectUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

class CrmWdmModelDAOImpl implements CrmWdmModelDAO {

  private static final Logger log = LoggerFactory.getLogger(CrmWdmModelDAOImpl.class);
  private final Map<Integer, NodeInfo> nodeInfoMap = new HashMap<>();

  @Override
  public Optional<LtpInfo> getLtpFromAid(int neId, String aidString) {
    if (!isNeInInternalModel(neId) || ObjectUtils.isEmpty(aidString)) {
      return Optional.empty();
    }
    return getAllLtps(neId).stream()
      .filter(ltpInfo -> ltpInfo.getName().equals(aidString))
      .findAny();
  }
  @Override
  public Optional<TtpInfo> getTtpByPortId(int neId, String portId) {
    if (!isNeInInternalModel(neId) || ObjectUtils.isEmpty(portId)) {
      return Optional.empty();
    }
    List<TtpInfo> result = getAllTtps(neId).stream()
      .filter(ttpInfo -> ttpInfo.hasPortId(portId))
      .toList();
    if (result.size() > 1) {
      log.error("CrmWdmModel lookup unambiguity! Multiple ({}) ttps found with neId={} and portId={}.", result.size(), neId, portId);
    }
    return result.isEmpty() ? Optional.empty() : Optional.of(result.get(0));
  }

  @Override
  public List<LtpInfo> removeLtpByNameAndDegreeNumber(int neId, String ltpName, int degreeNumber) {
    if (!isNeInInternalModel(neId) || ObjectUtils.isEmpty(ltpName)) {
      return List.of();
    }
    List<LtpInfo> removedLtps = getAllLtps(neId).stream()
      .filter(ltp -> Objects.equals(ltp.getName(), ltpName) && degreeNumber == ltp.getDegreeNumber())
      .toList();

    log.info("Removing LTP(s) for neId={} LTP(s)={}", neId, removedLtps);
    getAllLtps(neId).removeAll(removedLtps);
    return removedLtps;
  }

  @Override
  public Optional<LtpInfo> getLtpByProtectionGroupAid(int neId, String aidString) {
    if (!isNeInInternalModel(neId) || ObjectUtils.isEmpty(aidString)) {
      return Optional.empty();
    }
    return getAllLtps(neId).stream()
      .filter(ltpInfo -> aidString.equals(ltpInfo.getProtectionGroupAid()))
      .findAny();
  }

  @Override
  public Optional<TtpInfo> getTtpFromAid(int neId, String aidString) {
    if (!isNeInInternalModel(neId) || ObjectUtils.isEmpty(aidString)) {
      return Optional.empty();
    }
    return getAllTtps(neId).stream()
      .filter(ttpInfo -> Objects.equals(ttpInfo.getName(), aidString))
      .findFirst();
  }

  @Override
  public Optional<TtpInfo> getTtpFromSlcActiveEndpoint(int neId, String activeEndPoint) {
    if (!isNeInInternalModel(neId) || ObjectUtils.isEmpty(activeEndPoint)) {
      return Optional.empty();
    }
    return getAllTtps(neId).stream()
      .filter(ttpInfo -> activeEndPoint.equals(ttpInfo.getActiveEndPoint()))
      .findFirst();
  }

  @Override
  public Optional<LtpInfo> getLtpByDegreeNumber(int neId, int degreeNumber) {
    return getAllLtps(neId).stream()
      .filter(ltpInfo -> ltpInfo.getDegreeNumber() == degreeNumber)
      .findFirst();
  }

  @Override
  public void removeLtpByDegreeNumber(int neId, int degreeNumber) {
    getAllLtps(neId).removeIf(ltpInfo -> ltpInfo.getDegreeNumber() == degreeNumber);
  }

  @Override
  public void replaceLtp(int neId, LtpInfo ltpInfo) {
    removeLtpByDegreeNumber(neId, ltpInfo.getDegreeNumber());
    addLtp(neId, ltpInfo);
  }

  @Override
  public boolean removeTtpByPortId(int neId, String aidString) {
    return getAllTtps(neId).removeIf(ttpInfo -> Objects.equals(ttpInfo.getName(), aidString));
  }

  @Override
  public boolean isLtpPresent(int neId, int degreeNumber) {
    return getLtpByDegreeNumber(neId, degreeNumber).isPresent();
  }

  @Override
  public int getLtpsSize(int neId) {
    int size = 0;
    if (isNeInInternalModel(neId)) {
      size = getAllLtps(neId).size();
    }
    return size;
  }

  @Override
  public int getTtpsSize(int neId) {
    int size = 0;
    if (isNeInInternalModel(neId)) {
      size = getAllTtps(neId).size();
    }
    return size;
  }

  @Override
  public int getRGroupsSize(int neId) {
    int size = 0;
    if (isNeInInternalModel(neId)) {
      size = getAllResourceGroupInfos(neId).size();
    }
    return size;
  }

  @Override
  public List<LtpInfo> getAllLtps(int neId) {
    return isNeInInternalModel(neId)
      ? nodeInfoMap.get(neId).getLtpInfoList()
      : Collections.emptyList();
  }
  @Override
  public Optional<LtpInfo> getLtp(int neId, String ltpName) {
    Optional<LtpInfo> optionalLtpInfo = Optional.empty();
    if (getLtpsSize(neId) > 0) {
      optionalLtpInfo = getAllLtps(neId).stream()
        .filter(ltpInfo -> Objects.equals(ltpInfo.getName(), ltpName))
        .findAny();
    }
    return optionalLtpInfo;
  }

  @Override
  public ResourceGroupInfo getResourceGroupInfo(int neId, int id) {
    Optional<ResourceGroupInfo> optionalResourceGroupInfo = Optional.empty();
    if (getRGroupsSize(neId) > 0) {
      optionalResourceGroupInfo = getAllResourceGroupInfos(neId).stream()
              .filter(resourceGroupInfo -> resourceGroupInfo.getId() == id)
              .findAny();
    }
    return optionalResourceGroupInfo.orElse(null);
  }

  @Override
  public List<TtpInfo> getAllTtps(int neId) {
    return isNeInInternalModel(neId)
      ? nodeInfoMap.get(neId).getAllTtpInfos()
      : Collections.emptyList();
  }

  @Override
  public void addSpectrumConstraint(int neId, int degreeNumber, SpectrumConstraint spectrumConstraint) {
    // check if the LTP exists for the degreeNumber
    // if yes, then add the constraint
    getLtpByDegreeNumber(neId, degreeNumber).ifPresentOrElse(
      ltp -> ltp.getConstraints()
        .addSpectrumConstraint(spectrumConstraint),
      () -> log.warn("addSpectrumConstraint: Could not find LTP - neId={}, degreeNumber={}", neId, degreeNumber));
  }

  @Override
  public boolean isConstraintInLtp(int neId, int degreeNumber, SpectrumConstraint spectrumConstraint) {
    return getLtpByDegreeNumber(neId, degreeNumber)
      .map(LtpInfo::getConstraints)
      .map(c -> c.containsConstraint(spectrumConstraint))
      .orElse(false);
  }

  @Override
  public boolean isConstraintInTtp(int neId, String aidString, String ltpDegreeName) {
    if (ltpDegreeName == null) {
      return false;
    }
    return getTtpFromAid(neId, aidString).filter(ttp -> ttp.hasConstrainingEntryWithInterfaceId(ltpDegreeName))
      .isPresent();
  }

  @Override
  public void addLtp(int neId, LtpInfo ltpInfo) {
    nodeInfoMap.computeIfAbsent(neId, id -> new NodeInfo())
      .addLtpInfo(ltpInfo);
  }

  @Override
  public void addTtp(int neId, TtpInfo ttpInfo) {
    nodeInfoMap.computeIfAbsent(neId, id -> new NodeInfo())
      .addTtpInfo(ttpInfo);
  }

  @Override
  public void addResourceGroup(int neId, ResourceGroupInfo resourceGroupInfo) {
    nodeInfoMap.computeIfAbsent(neId, id -> new NodeInfo()).addResourceGroup(resourceGroupInfo);
  }

  @Override
  public void setTtpActiveEndPoint(int neId, String ttpAidString, String activeEndpoint) {
    getTtpFromAid(neId, ttpAidString).ifPresentOrElse(
      ttp -> ttp.setActiveEndPoint(activeEndpoint),
      () -> log.warn("setTtpActiveEndPoint: Could not find LTP - neId={}, ttpAidString={}", neId, ttpAidString)
    );
  }

  @Override
  public void removeTtpActiveEndPoint(int neId, String activeEndPoint) {
    getTtpFromSlcActiveEndpoint(neId, activeEndPoint).ifPresentOrElse(
      TtpInfo::removeActiveEndPoint,
      () -> log.warn("removeTtpActiveEndPoint: Could not get TTP with activeEndpoint={} in neId={}", activeEndPoint, neId)
    );
  }

  @Override
  public Optional<SpectrumInfo> getSpectrumInfo(int neId, int degreeNumber) {
    return getLtpByDegreeNumber(neId, degreeNumber).map(LtpInfo::getSpectrumInfo);
  }

  @Override
  public void setCurrentFrequencySlotInTtp(int neId, String ttpAidString, String degreeName, FrequencySlot frequencySlot) {
    getTtpFromAid(neId, ttpAidString)
      .filter(TtpInfo::hasConstrainingEntries)
      .ifPresentOrElse(ttp ->
                         ttp.findConstrainingEntriesByInterfaceId(degreeName)
                           .forEach(constrainingEntry -> constrainingEntry.getConstrainedResource().setFrequencySlot(frequencySlot))
        , () -> log.warn("setCurrentFrequencySlotInTtp: Could not find TTP - neId={}, ttpAid={}", neId, ttpAidString)
      );
  }

  @Override
  public void clearCurrentFrequencySlotInTtp(int neId, String activeEndpoint, String degreeName) {
    getTtpFromSlcActiveEndpoint(neId, activeEndpoint)
      .filter(TtpInfo::hasConstrainingEntries)
      .ifPresentOrElse(
        ttp -> ttp.findConstrainingEntriesByInterfaceId(degreeName)
          .forEach(constrainingEntry -> constrainingEntry.getConstrainedResource()
            .clearFrequencySlotAndBandwidth()),
        () -> log.warn("clearCurrentFrequencySlotInTtp: Could not find TTP - neId={}, activeEndpoint={}", neId, activeEndpoint)
      );
  }

  @Override
  public void setSelectedInterfaceIdInTtp(int neId, String ttpAidString, String interfaceId) {
    getTtpFromAid(neId, ttpAidString)
      .map(TtpInfo::getEdgeBindingConstraints)
      .map(EdgeBindingConstraints::getSelectors)
      .stream()
      .flatMap(Collection::stream)
      .forEach(selector -> selector.setSelectedInterfaceId(interfaceId));
  }

  @Override
  public void clearSelectedInterfaceIdInTtp(int neId, String activeEndPoint) {
    getTtpFromSlcActiveEndpoint(neId, activeEndPoint)
      .ifPresentOrElse(
        ttpInfo -> ttpInfo.getEdgeBindingConstraints().getSelectors().forEach(Selector::clearSelectedInterfaceId),
        () -> log.warn("clearSelectedInterfaceIdInTtp: Could not get TTP with activeEndpoint={} in neId={}", activeEndPoint, neId)
      );
  }

  @Override
  public void addFrequencySlotToLtp(int neId, int degreeNumber, FrequencySlot frequencySlot) {
    if (isLtpPresent(neId, degreeNumber)) {
      if (!isFreqSlotPresentInLtp(neId, degreeNumber, frequencySlot))
        getLtpByDegreeNumber(neId, degreeNumber).map(LtpInfo::getSpectrumInfo)
          .ifPresent(s -> s.addFrequencySlot(frequencySlot));
    } else {
      log.warn("addFrequencySlotToLtp: LTP not present neId={} degreeNumber={}", neId, degreeNumber);
    }
  }

  private boolean isFreqSlotPresentInLtp(int neId, int degreeNumber, FrequencySlot freqSlot) {
    var freqSlots = getLtpByDegreeNumber(neId, degreeNumber).map(LtpInfo::getSpectrumInfo)
      .map(SpectrumInfo::getUsedFrequencySlots)
      .orElseGet(Collections::emptyList);
    return freqSlots.stream()
      .anyMatch(frequencySlot -> frequencySlot.centerFreq() == freqSlot.centerFreq() && frequencySlot.slotwidth() == freqSlot.slotwidth());
  }

  @Override
  public void deleteFrequencySlotInLtp(int neId, int degreeNumber, int frequency, int slotwidth) {
    getLtpByDegreeNumber(neId, degreeNumber).ifPresentOrElse(
      ltpInfo -> {
        List<FrequencySlot> frequencySlots = ltpInfo.getSpectrumInfo()
          .getUsedFrequencySlots()
          .stream()
          .filter(frequencySlot -> frequencySlot.centerFreq() == frequency && frequencySlot.slotwidth() == slotwidth)
          .toList();
        if (frequencySlots.isEmpty()) {
          log.error("No frequencySlot to delete: LTP with neId={}, name={} does not contain frequencySlot for frequency={}, slotwidth={}",
                    neId,
                    ltpInfo.getName(),
                    frequency,
                    slotwidth);
        } else if (frequencySlots.size() == 1) {
          ltpInfo.getSpectrumInfo()
            .removeFrequencySlot(frequency, slotwidth);
        } else {
          log.error("Found more than 1 frequencySlot to delete: LTP with neId={}, name={} for frequency={}, slotwidth={}",
                    neId,
                    ltpInfo.getName(),
                    frequency,
                    slotwidth);
        }
      },
      () -> log.warn("deleteFrequencySlotInLtp: LTP not present neId={} degreeNumber={}", neId, degreeNumber));
  }


  @Override
  public List<ResourceGroupInfo> getAllResourceGroupInfos(int neId) {
    if (isNeInInternalModel(neId)) {
      return nodeInfoMap.get(neId).getAllResourceGroupInfos();
    }
    return Collections.emptyList();
  }

  @Override
  public ResourceGroupInfo removeResourceGroupInfo(int neId, int resourceId) {
    List<ResourceGroupInfo> allInfosOfThisNe = getAllResourceGroupInfos(neId);
    Optional<ResourceGroupInfo> myInfo = allInfosOfThisNe.stream()
      .filter(resourceGroupInfo -> resourceGroupInfo.getId() == resourceId)
      .findAny();
    myInfo.ifPresent(allInfosOfThisNe::remove);
    return myInfo.orElse(null);
  }

  @Override
  public void removeStaticSpectrumConstraintInLtp(int neId, int degreeNumber, String constraintInterfaceId) {
    getLtpByDegreeNumber(neId, degreeNumber).ifPresentOrElse(
      ltp -> ltp.getConstraints()
        .getStaticSpectrumConstraints()
        .removeIf(spectrumConstraint -> spectrumConstraint.getInterfaceId().equals(constraintInterfaceId)),
      () -> log.warn("removeStaticSpectrumConstraintInLtp: LTP not present neId={} degreeNumber={}", neId, degreeNumber));
  }

  @Override
  public void removeDynamicSpectrumConstraintInLtp(int neId, int degreeNumber, String constraintInterfaceId) {
    getLtpByDegreeNumber(neId, degreeNumber).ifPresentOrElse(
      ltp -> ltp.getConstraints()
        .getDynamicSpectrumConstraints()
        .removeIf(spectrumConstraint -> spectrumConstraint.getInterfaceId().equals(constraintInterfaceId)),
      () -> log.warn("removeDynamicSpectrumConstraintInLtp: LTP not present neId={} degreeNumber={}", neId, degreeNumber));
  }

  public List<TtpInfo> getAllTtpsFromResourceGroupId(int neId, int resourceGroupId) {
    return getAllTtps(neId).stream()
      .filter(TtpInfo::hasConstrainingEntries)
      .filter(ttpInfo -> ttpInfo.getEdgeBindingConstraints()
        .getSelectors()
        .stream()
        .map(Selector::getResourceGroupId)
        .anyMatch(id -> id == resourceGroupId))
      .collect(Collectors.toList());
  }

  @Override
  public void removeTtpPortsFromResourceGroup(int neId, int groupId, List<String> portIds) {
    var resourceGroups = getAllResourceGroupInfos(neId);
    resourceGroups.stream()
      .filter(group -> group.id == groupId)
      .forEach(group -> group.removePortsByIds(portIds));
    resourceGroups.removeIf(group -> group.resourceList.isEmpty());
  }

  @Override
  public void addNode(int neId, NodeInfo node) {
    nodeInfoMap.put(neId, node);
  }

  @Override
  public NodeInfo getNode(int neId) {
    return nodeInfoMap.get(neId);
  }


  public boolean isNeInInternalModel(int neId) {
    return nodeInfoMap.containsKey(neId);
  }

  @Override
  public void removeNodeInfo(int neId) {
    nodeInfoMap.remove(neId);
  }

  @Override
  public Optional<TtpInfo> removeTtpBySepUri(int neId, String sepUri) {
    Objects.requireNonNull(sepUri, "SEP URI must not be null");
    var deletedTtp = getAllTtps(neId).stream()
      .filter(ttp -> sepUri.equals(ttp.getSepUri()))
      .findAny();
    getAllTtps(neId).removeIf(ttp -> sepUri.equals(ttp.getSepUri()));
    return deletedTtp;
  }

  @Override
  public Optional<LtpInfo> findLtpByOscInterface(int neId, String ptpUri) {
    return getAllLtps(neId)
      .stream()
      .filter(ltp -> ptpUri.equals(ltp.getOscSignal().ptpUri()))
      .findFirst();
  }

  @Override
  public String toString() {
    return "{\"nodeInfoMap\":[" +
      nodeInfoMap.entrySet().stream().map(this::convert).collect(Collectors.joining(",")) +
      "]}";
  }


  private String convert(Map.Entry<Integer, NodeInfo> entry) {
    return entry.getValue().toString(entry.getKey());
  }

}


