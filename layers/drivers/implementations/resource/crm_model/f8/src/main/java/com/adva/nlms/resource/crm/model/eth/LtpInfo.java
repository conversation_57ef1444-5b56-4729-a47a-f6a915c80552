/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.eth;

import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class LtpInfo {

  private final String ltpName;
  private List<EthPool> ethPools;
  private LayerQualifier primaryLayerQualifier; // internal field - used for storing primary signal of multiplexed LTPs

  public LtpInfo(String ltpName, List<EthPool> ethPools, LayerQualifier primaryLayerQualifier) {
    this.ltpName = ltpName;
    this.ethPools = ethPools;
    this.primaryLayerQualifier = primaryLayerQualifier;
  }

  public LtpInfo(String ltpName, LayerQualifier primaryLayerQualifier) {
    this.ltpName = ltpName;
    this.ethPools = new ArrayList<>();
    this.primaryLayerQualifier = primaryLayerQualifier;
  }

  public LtpInfo(String ltpName, List<EthPool> ethPools) {
    this.ltpName = ltpName;
    this.ethPools = ethPools;
  }

  public LtpInfo(String ltpName) {
    this.ltpName = ltpName;
    this.ethPools = new ArrayList<>();
  }

  public String getName() {	return ltpName;	}

  public List<EthPool> getEthPools() { return ethPools; }

  public void setEthPools(List<EthPool> ethPools) { this.ethPools = ethPools; }

  public LayerQualifier getPrimaryLayerQualifier() { return primaryLayerQualifier; }

  @Override
  public boolean equals(Object obj) {
    if(this == obj)
      return true;
    if(!(obj instanceof LtpInfo other)){
      return false;
    }
    return Objects.equals(other.getName(), this.getName())
        && Objects.equals(other.ethPools, this.ethPools);
  }

  @Override
  public int hashCode() {
    return Objects.hash(ltpName, ethPools);
  }

  @Override
  public String toString() {
    return "\"" + ltpName + "\"";
  }

}
