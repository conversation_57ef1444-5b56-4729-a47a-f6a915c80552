/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;


public class LtpInfo {

  private String ltpName;
  private final String omsTerminationId;
  private final SpectrumInfo spectrumInfo;
  private final int degreeNumber;
  private final boolean isFixedDegree;
  private final TransitConstraintsInfo constraints;
  private final OSCSignal oscSignal;
  private final String protectionGroupAid;

  public LtpInfo(String ltpName,
                 String omsTerminationId,
                 SpectrumInfo spectrumInfo,
                 int degreeNumber,
                 boolean isFixedDegree,
                 TransitConstraintsInfo constraints,
                 OSCSignal oscSignal,
                 String protectionGroupAid) {
    this.ltpName = ltpName;
    this.omsTerminationId = omsTerminationId;
    this.spectrumInfo = spectrumInfo;
    this.degreeNumber = degreeNumber;
    this.isFixedDegree = isFixedDegree;
    this.constraints = constraints;
    this.oscSignal = oscSignal;
    this.protectionGroupAid = protectionGroupAid;
  }

  public LtpInfo(String name, SpectrumInfo spectrumInfo, int degreeNumber, boolean isFixedDegree, TransitConstraintsInfo constraints, String protectionGroupAid) {
    this(name, null, spectrumInfo, degreeNumber, isFixedDegree, constraints, OSCSignal.UNKNOWN_SIGNAL, protectionGroupAid);
  }

  public LtpInfo(String name, SpectrumInfo spectrumInfo, int degreeNumber, boolean isFixedDegree) {
    this(name, spectrumInfo, degreeNumber, isFixedDegree, new TransitConstraintsInfo(), null);
  }

  public LtpInfo(String name, String omsTerminationId, SpectrumInfo spectrumInfo, int degreeNumber, boolean isFixedDegree, OSCSignal oscSignal,String protectionGroupAid) {
    this(name, omsTerminationId, spectrumInfo, degreeNumber, isFixedDegree, new TransitConstraintsInfo(), oscSignal, protectionGroupAid);
  }

  public String getName() {		return ltpName;	}

  public void setName(String ltpName) {
    this.ltpName = ltpName;
  }

  public String getOmsTerminationId() {
    return omsTerminationId;
  }

  public SpectrumInfo getSpectrumInfo() {		return spectrumInfo;	}

  public int getDegreeNumber() {		return degreeNumber;	}

  public boolean isFixedDegree() {
    return isFixedDegree;
  }

  public OSCSignal getOscSignal() {
    return oscSignal;
  }

  public TransitConstraintsInfo getConstraints() {
    return constraints;
  }

  public String getProtectionGroupAid() {
    return protectionGroupAid;
  }


  public LtpInfo(LtpInfo other) {
    this.ltpName = other.ltpName;
    this.omsTerminationId = other.omsTerminationId;
    this.spectrumInfo = other.spectrumInfo;
    this.constraints = other.constraints;
    this.isFixedDegree = other.isFixedDegree;
    this.degreeNumber = other.degreeNumber;
    this.oscSignal = other.oscSignal;
    this.protectionGroupAid = other.protectionGroupAid;
  }

  @Override
  public boolean equals(Object obj) {
    if(this == obj)
      return true;
    if(!(obj instanceof LtpInfo other)){
      return false;
    }
    return other.getDegreeNumber() == this.getDegreeNumber();
  }

  @Override
  public int hashCode() {
    return super.hashCode();
  }

  @Override
  public String toString() {
    return "\"" + ltpName + "\"";
  }
}