/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;


import java.util.ArrayList;
import java.util.List;

public class SpectrumInfo {
  private final Type type;
  private final int first;
  private final int last;
  private final int spacing;              // Defines size of each chunk on the grid
  private final int stepCf;
  private final SlotWidthSupport slotWidthSupport;
  private final boolean isOpaque;
  private List<FrequencySlot> usedFrequencySlots;

  public record SlotWidthSupport(int min, int max, int step) {
    // step for each next slotwidth after minimum
    SlotWidthSupport() {
      this(0, Integer.MAX_VALUE, Integer.MAX_VALUE);
    }
  }

  public SpectrumInfo(Type type, int first, int last, int spacing, int stepCf, SlotWidthSupport slotWidthSupport, boolean isOpaque) {
    this.type = type;
    this.first = first;
    this.last = last;
    this.spacing = spacing;
    this.stepCf = stepCf;
    this.slotWidthSupport = slotWidthSupport;
    this.isOpaque = isOpaque;
    this.usedFrequencySlots = new ArrayList<>();
  }

  public SpectrumInfo(Type type) {
    // Everything is allowed for this range, i.e. [0, max(int)]. Other values are ignored
    this(type, 0, Integer.MAX_VALUE, 0, 0, new SlotWidthSupport(), true);
  }

  public SpectrumInfo() {
    this(Type.TYPE_FREQUENCY);
  }

  public int getFirst() {
    return first;
  }

  public int getLast() {
    return last;
  }

  public int getSpacing() {
    return spacing;
  }

  public int getStepCf() {
    return stepCf;
  }

  public SlotWidthSupport getSlotWidthSupport() {
    return slotWidthSupport;
  }

  public boolean isOpaque() {
    return isOpaque;
  }

  public List<FrequencySlot> getUsedFrequencySlots() {
    return usedFrequencySlots;
  }

  public Type getType() {
    return type;
  }

  public enum Type {
    TYPE_UNDEFINED,
    TYPE_FREQUENCY,
    TYPE_WAVELENGTH,
    TYPE_GRAY;

    public static Type fromChannelType(ChannelType channelType) {
      if (channelType == null) {
        return TYPE_UNDEFINED;
      }
      return switch (channelType) {
        case DWDM -> TYPE_FREQUENCY;
        case CWDM -> TYPE_WAVELENGTH;
        case GRAY -> TYPE_GRAY;
      };
    }
  }

  public void addFrequencySlot(FrequencySlot frequencySlot) {
    this.usedFrequencySlots.add(frequencySlot);
  }

  public void setUsedFrequencySlots(List<FrequencySlot> frequencySlots) {
    usedFrequencySlots = frequencySlots;
  }

  public void removeFrequencySlot(int freq, int slotwidth) {
    usedFrequencySlots.removeIf(frequencySlot -> frequencySlot.centerFreq() == freq && frequencySlot.slotwidth() == slotwidth);
  }
}
