/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.eth;

import java.util.List;

/***
 * This class provides the interface to the CRM ETH internal model
 */
public interface CrmEthModelDAO {

  /***
   * Deletes the LTP from the internal model
   * @param neId	NE Id in the database
   * @param ltpName LTP name
   */
  void removeLtp(int neId, String ltpName);

  /***
   * Deletes the TTP fom the internal model
   * @param neId NE Id in the database
   * @param aidString TTP name
   */
  void removeTtp(int neId, String aidString);

  /***
   * Gets the number of the LTP objects in the internal model
   * @param neId NE Id in the database
   */
  int getLtpsSize(int neId);

  /***
   * Gets the number of the TTP objects in the internal model
   * @param neId NE Id in the database
   */
  int getTtpsSize(int neId);

  /***
   * Gets the list of all LTPs
   * @param neId  NE Id in the database
   */
  List<LtpInfo> getAllLtps(int neId);

  /***
   * Gets the LTP by its name
   * @param neId  NE Id in the database
   * @param ltpName  LTP name
   */
  LtpInfo getLtp(int neId, String ltpName);

  /***
   * Gets the list of all TTPs
   * @param neId  NE Id in the database
   */
  List<TtpInfo> getAllTtps(int neId);

  /***
   * Gets the TTP by its name
   * @param neId  NE Id in the database
   * @param ttpName  TTP name
   */
  TtpInfo getTtp(int neId, String ttpName);

  /***
   * Adds the LTP to the internal model
   * @param neId NE Id in the database
   * @param ltpInfo Ltp Object
   */
  void addLtp(int neId, LtpInfo ltpInfo);

  /***
   * Adds the TTP to the internal model
   * @param neId NE Id in the database
   * @param ttpInfo TTP Object
   */
  void addTtp(int neId, TtpInfo ttpInfo);

  /***
   *  Checks if NodeInfo object is present in internal model Map
   * @param neId NE Id in the database
   */
  boolean isNeInInternalModel(int neId);

  /***
   *  Removes the NodeInfo object from internal model Map
   * @param neId NE Id in the database
   */
  void removeNodeInfo(int neId);

}
