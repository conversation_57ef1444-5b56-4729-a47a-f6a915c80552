/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;

import java.util.Optional;

public final class Tunable implements ConstrainedResource {
  public static final ChannelType CHANNEL_TYPE = ChannelType.DWDM; // currently only support DWDM on Tunable
  private final String name;                // name of the 'port'
  private int base;                         // defines tunable frequency range in NI model [MHz]
  private int interval;                     // defines tunable frequency step interval in NI model [MHz]
  private int count;                        // defines number of tunable frequencies in NI model

  private double currentChannel;            // value of the dwdm channel currently in use
  private int bandwidth;                    // current bandwidth as seen from a transponder's port (forms the SlotWidthParams in NI model)
  private int slotwidth;                    // slot width occupied by this transponder's port on LTP (forms the SlotWidthParams in NI model)
  private int constrainingRangeFirst;
  private int constrainingRangeLast;

  public Tunable(String name) {
    this.name = name;
  }

  public double getBase() {
    return base;
  }

  public void setBase(int base) {
    this.base = base;
  }

  public double getInterval() {
    return interval;
  }

  public void setInterval(int interval) {
    this.interval = interval;
  }

  public int getCount() {
    return count;
  }

  public void setCount(int count) {
    this.count = count;
  }

  public String getName() {
    return name;
  }

  @Override
  public ChannelType getChannelType() {
    return CHANNEL_TYPE;
  }

  public double getCurrentChannel() {
    return currentChannel;
  }

  public void setCurrentChannel(double currentChannel) {
    this.currentChannel = currentChannel;
  }

  public void clearCurrentChannel() {
    this.currentChannel = 0;
  }

  public int getBandwidth() {
    return bandwidth;
  }

  public void setBandwidth(int bandwidth) {
    this.bandwidth = bandwidth;
  }

  public void clearBandwidth() {
    this.bandwidth = 0;
  }

  public int getSlotwidth() {
    return slotwidth;
  }

  public void setSlotwidth(int slotwidth) {
    this.slotwidth = slotwidth;
  }

  public void clearSlotwidth() {
    this.slotwidth = 0;
  }

  public int getConstrainingRangeFirst() {
    return constrainingRangeFirst;
  }
  public int getConstrainingRangeLast() {
    return constrainingRangeLast;
  }

  public void setConstrainingRangeFirst(int value) {
    this.constrainingRangeFirst = value;
  }
  public void setConstrainingRangeLast(int value) {
    this.constrainingRangeLast = value;
  }

  @Override
  public Optional<FrequencySlot> getCurrentFrequencySlot() {
    var centerFreq = (int) getCurrentChannel();
    if (centerFreq == 0 && slotwidth == 0) {
      return Optional.empty();
    } else {
      return Optional.of(new FrequencySlot(centerFreq, slotwidth, CHANNEL_TYPE));
    }
  }

  @Override
  public void setFrequencySlot(FrequencySlot frequencySlot) {
    setBandwidth(frequencySlot.slotwidth());
    setSlotwidth(frequencySlot.slotwidth());
    setCurrentChannel(frequencySlot.centerFreq());
  }

  @Override
  public void clearFrequencySlotAndBandwidth() {
    clearCurrentChannel();
    clearBandwidth();
    clearSlotwidth();
  }
}
