/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *   Owner: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.eth;

import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import ni.proto.external.common.signal_description.LspSwitchingType;
import ni.proto.external.common.signal_description.SignalEthernet;
import ni.proto.inet.InterfaceId;
import ni.proto.inet.IpAddr;
import ni.proto.ml.MlSync;
import ni.proto.mla.LinkTerminationPointOuterClass.LinkTerminationPoint;
import ni.proto.mla.NodeOuterClass;
import ni.proto.mla.TunnelTerminationPointOuterClass;
import ni.proto.mla.TunnelTerminationPointOuterClass.TunnelTerminationPoint;
import ni.proto.mla.eth.Eth;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/*
 * This class translates java object into ProtoBuf format.
 * The goal of this class is to de-couple the whole translation logic from the CrmModel in Eth layer (i.e. CrmF8ModelTask)
 * The goal of the design is that this class should implement an interface defined in business logic of the CrmModel
 */
class CrmEthNiTranslatorImpl implements CrmEthNiTranslator {

  private static final String DEFAULT_IP_ADDRESS = "0.0.0.0";
  private final Logger log = LoggerFactory.getLogger(CrmEthNiTranslatorImpl.class);
  private final NEDataProvider neDataProvider;
  private final CrmEthModelDAO crmEthModelDAO;

  public CrmEthNiTranslatorImpl(NEDataProvider neDataProvider, CrmEthModelDAO crmEthModelDAO) {
    this.neDataProvider = neDataProvider;
    this.crmEthModelDAO = crmEthModelDAO;
  }

  @Override
  public LinkTerminationPoint translateLtpData(LtpInfo ltpInfo, int neId) {
    if (ltpInfo == null) {
      log.error("translateLTPData: null input arguments in neId={}", neId);
      return null;
    }
    Objects.requireNonNull(ltpInfo.getName(), "LTP name must not be null");
    if (ltpInfo.getEthPools().isEmpty()) {
      throw new IllegalArgumentException("LTP Eth pools must not be empty");
    }

    InterfaceId name = InterfaceId.newBuilder().setName(ltpInfo.getName()).build();

    LinkTerminationPoint.Resources resources = LinkTerminationPoint.Resources.newBuilder()
      .setEthResources(Eth.EthResources.newBuilder()
        .addAllEthPools(generateEthPools(ltpInfo.getEthPools()))
        .build())
      .build();

    return LinkTerminationPoint.newBuilder()
      .setInterfaceId(name)
      .setLayer(LspSwitchingType.LSP_SWITCHING_TYPE_ETHERNET)
      .setResources(resources)
      .build();
  }

  private List<Eth.EthResources.EthPool> generateEthPools(List<EthPool> ethPools) {
    return ethPools.stream()
      .map(this::generateEthPool)
      .toList();
  }

  private Eth.EthResources.EthPool generateEthPool(EthPool ethPool) {
    return Eth.EthResources.EthPool.newBuilder()
      .setSignalType(mapSignalType(ethPool.getLayerQualifier()))
      .setIdle(generateResourceDescriptor(ethPool.getIdleEthResources()))
      .setBusy(generateResourceDescriptor(ethPool.getBusyEthResources()))
      .build();
  }

  private SignalEthernet.SignalType mapSignalType(LayerQualifier layerQualifier) {
    return switch (layerQualifier) {
      case ETH_100G -> SignalEthernet.SignalType.ETH_SIGNAL_TYPE_100GBE;
      case ETH_400G -> SignalEthernet.SignalType.ETH_SIGNAL_TYPE_400GBE;
      case ETH_800G -> SignalEthernet.SignalType.ETH_SIGNAL_TYPE_800GBE;
      default -> SignalEthernet.SignalType.ETH_SIGNAL_TYPE_UNSPECIFIED;
    };
  }

  private Eth.ResourceDescriptor generateResourceDescriptor(ResourceDescriptor resourceDescriptor) {
    Eth.ResourceDescriptor niResourceDescriptor = Eth.ResourceDescriptor.newBuilder().build();

    if (resourceDescriptor instanceof ContainerSet containerSet) {
      niResourceDescriptor = niResourceDescriptor.toBuilder()
        .setContainerSet(generateContainerSet(containerSet))
        .build();
    } else if (resourceDescriptor instanceof Whole) {
      niResourceDescriptor = niResourceDescriptor.toBuilder()
        .setWhole(Eth.ResourceDescriptor.Whole.newBuilder().build())
        .build();
    }

    return niResourceDescriptor;
  }

  private Eth.ResourceDescriptor.ContainerSet generateContainerSet(ContainerSet containerSet) {
    return Eth.ResourceDescriptor.ContainerSet.newBuilder()
      .addAllContainer(generateContainers(containerSet.getContainers()))
      .build();
  }

  private List<Eth.ResourceDescriptor.Container> generateContainers(List<Container> containers) {
    return containers.stream()
      .map(container -> Eth.ResourceDescriptor.Container.newBuilder()
        .addAllId(container.getTributaryIds())
        .addAllSlots(container.getTributarySlots())
        .build())
      .toList();
  }

  @Override
  public MlSync.Data translateLtpIntoData(int neId, LtpInfo ltpInfo, MlSync.Data.Command command) {
    LinkTerminationPoint ltp = translateLtpData(ltpInfo, neId);
    return convertProtoLtpIntoData(ltp, command, neId);
  }

  private MlSync.Data convertProtoLtpIntoData(LinkTerminationPoint ltp, MlSync.Data.Command command, int neId) {
    if (ltp == null) {
      log.error("convertProtoLTPIntoData: Null input LTP value for neId={}", neId);
      return null;
    }

    MlSync.Data.Payload payloadData = MlSync.Data.Payload.newBuilder()
      .setLtp(ltp)
      .build();

    MlSync.Header header = MlSync.Header.newBuilder()
      .setId(ltp.getInterfaceId().getName())
      .setEpoch(String.valueOf(System.currentTimeMillis()))
      .setVersion(1)
      .build();

    IpAddr routerAddress = getRouterIpAddress(neId);

    MlSync.Data.Builder dataBuilder = MlSync.Data.newBuilder()
      .setCommand(command)
      .setHeader(header)
      .setRouter(routerAddress);
    if (command == MlSync.Data.Command.CMD_ADD || command == MlSync.Data.Command.CMD_UPDATE)
      dataBuilder.setPayload(payloadData);

    return dataBuilder.build();
  }

  @Override
  public TunnelTerminationPoint translateTtpData(TtpInfo ttpInfo, int neId) {
    if (ttpInfo == null) {
      log.error("translateTTPData: null input arguments in neId={}", neId);
      return null;
    }
    Objects.requireNonNull(ttpInfo.getName(), "TTP name must not be null");

    InterfaceId interfaceId = InterfaceId.newBuilder().setName(ttpInfo.getName()).build();

    TunnelTerminationPointOuterClass.EdgeBindingConstraints edgeBindingConstraints = TunnelTerminationPointOuterClass.EdgeBindingConstraints.newBuilder().build();

    if (ttpInfo.hasConstrainingEntries()) {
      edgeBindingConstraints = edgeBindingConstraints.toBuilder()
        .addSelector(generateSelector(ttpInfo.getEdgeBindingConstraints()))
        .setType(TunnelTerminationPointOuterClass.EdgeBindingConstraints.Type.TYPE_TERMINATION)
        .build();
    }

    return TunnelTerminationPoint.newBuilder()
      .setInterfaceId(interfaceId)
      .setEdgeBindingConstraints(edgeBindingConstraints)
      .setLayer(LspSwitchingType.LSP_SWITCHING_TYPE_ETHERNET)
      .build();
  }

  private TunnelTerminationPointOuterClass.Selector generateSelector(EdgeBindingConstraints edgeBindingConstraints) {
    TunnelTerminationPointOuterClass.Selector selector = TunnelTerminationPointOuterClass.Selector.newBuilder()
      .addAllConstrainingEntry(generateConstrainingEntries(edgeBindingConstraints.getConstrainingEntryList()))
      .build();

    if (edgeBindingConstraints.getSelectedInterfaceId() != null) {
      selector = selector.toBuilder()
        .setSelectedInterfaceId(InterfaceId.newBuilder().setName(edgeBindingConstraints.getSelectedInterfaceId()).build())
        .build();
    }

    return selector;
  }

  private List<TunnelTerminationPointOuterClass.ConstrainingEntry> generateConstrainingEntries(List<ConstrainingEntry> constrainingEntries) {
    return constrainingEntries.stream()
      .map(this::generateConstrainingEntry)
      .toList();
  }

  private TunnelTerminationPointOuterClass.ConstrainingEntry generateConstrainingEntry(ConstrainingEntry constrainingEntry) {
    TunnelTerminationPointOuterClass.ConstrainingEntry conEntry = TunnelTerminationPointOuterClass.ConstrainingEntry.newBuilder()
      .setInterfaceId(InterfaceId.newBuilder().setName(constrainingEntry.getInterfaceId()).build())
      .build();

    if (constrainingEntry.getConstrainedResources() != null) {
      conEntry = conEntry.toBuilder()
        .setEth(generateEthResource(constrainingEntry.getConstrainedResources()))
        .build();
    }

    return conEntry;
  }

  private TunnelTerminationPointOuterClass.ConstrainingEntry.Eth generateEthResource(ResourceDescriptor resourceDescriptor) {
    TunnelTerminationPointOuterClass.ConstrainingEntry.Eth niEth = TunnelTerminationPointOuterClass.ConstrainingEntry.Eth.newBuilder().build();

    if (resourceDescriptor instanceof Container container) {
      niEth = niEth.toBuilder()
        .setContainer(Eth.ResourceDescriptor.Container.newBuilder()
          .addAllId(container.getTributaryIds())
          .addAllSlots(container.getTributarySlots())
          .build())
        .build();
    } else if (resourceDescriptor instanceof Whole) {
      niEth = niEth.toBuilder()
        .setWhole(Eth.ResourceDescriptor.Whole.newBuilder().build())
        .build();
    }

    return niEth;
  }

  @Override
  public MlSync.Data getAllTtpsData(int neId) {
    List<TunnelTerminationPoint> ttpsList = new ArrayList<>();
    if (crmEthModelDAO.getTtpsSize(neId) > 0) {
      ttpsList = crmEthModelDAO.getAllTtps(neId).stream()
        .map(ttpInfo -> translateTtpData(ttpInfo, neId))
        .toList();
    }

    NodeOuterClass.TunnelTerminationPoints tunnelTerminationPoints = NodeOuterClass.TunnelTerminationPoints.newBuilder()
      .addAllTtps(ttpsList)
      .build();

    MlSync.Data.Payload payloadData = MlSync.Data.Payload.newBuilder()
      .setTtps(tunnelTerminationPoints)
      .build();

    MlSync.Header header = MlSync.Header.newBuilder()
      .setId("TTPs")
      .setEpoch(String.valueOf(System.currentTimeMillis()))
      .setVersion(1)
      .build();

    IpAddr routerAddress = getRouterIpAddress(neId);

    return MlSync.Data.newBuilder()
      .setCommand(MlSync.Data.Command.CMD_UPDATE)
      .setPayload(payloadData)
      .setHeader(header)
      .setRouter(routerAddress)
      .build();
  }

  private IpAddr getRouterIpAddress(int neId) {
    return IpAddr.newBuilder()
      .setS(neDataProvider.getNeData(neId).getIpAddress() != null ? neDataProvider.getNeData(neId).getIpAddress() : DEFAULT_IP_ADDRESS)
      .build();
  }

}
