/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.crm.model;

import ni.proto.mla.LinkTerminationPointOuterClass;
import ni.proto.segment_callback.SegmentCallbackOuterClass;

public class OSCStatusTranslator {
  private OSCStatusTranslator() {
  }

  public static LinkTerminationPointOuterClass.OscStatus toNiOSCStatus(OSCStatus status) {
    return switch (status) {
      case UP -> LinkTerminationPointOuterClass.OscStatus.OSC_STATUS_UP;
      case DOWN -> LinkTerminationPointOuterClass.OscStatus.OSC_STATUS_DOWN;
      case UNKNOWN -> LinkTerminationPointOuterClass.OscStatus.OSC_STATUS_UNKNOWN;
    };
  }

  static SegmentCallbackOuterClass.AlarmNotify.OscStatus toNiAlarmNotifyOscStatus(OSCStatus status) {
    return switch (status) {
      case UP -> SegmentCallbackOuterClass.AlarmNotify.OscStatus.OSC_UP;
      case DOWN -> SegmentCallbackOuterClass.AlarmNotify.OscStatus.OSC_DOWN;
      case UNKNOWN -> SegmentCallbackOuterClass.AlarmNotify.OscStatus.OSC_UNKNOWN;
    };
  }
}
