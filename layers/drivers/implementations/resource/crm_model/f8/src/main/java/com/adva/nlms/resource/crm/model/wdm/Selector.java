/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;

import java.util.ArrayList;
import java.util.List;

public class Selector {
  private final List<ConstrainingEntry> constrainingEntryList = new ArrayList<>();
  private String selectedInterfaceId;
  private int resourceGroupId;

  public List<ConstrainingEntry> getConstrainingEntryList() {
    return constrainingEntryList;
  }

  public void addConstrainingEntry(ConstrainingEntry constrainingEntry) {
    this.constrainingEntryList.add(constrainingEntry);
  }

  public String getSelectedInterfaceId() {
    return selectedInterfaceId;
  }

  public void setSelectedInterfaceId(String selectedInterfaceId) {
    this.selectedInterfaceId = selectedInterfaceId;
  }

  public void clearSelectedInterfaceId() {
    this.selectedInterfaceId = null;
  }

  public int getResourceGroupId() {
    return resourceGroupId;
  }

  public void setResourceGroupId(int resourceGroupId) {
    this.resourceGroupId = resourceGroupId;
  }

}
