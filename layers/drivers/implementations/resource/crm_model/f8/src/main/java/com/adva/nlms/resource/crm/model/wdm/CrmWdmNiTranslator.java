/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *   Owner: JakubM
 */

package com.adva.nlms.resource.crm.model.wdm;

import ni.proto.ml.MlSync;
import ni.proto.mla.LinkTerminationPointOuterClass;
import ni.proto.mla.ResourceGroupOuterClass;
import ni.proto.mla.TunnelTerminationPointOuterClass;

public interface CrmWdmNiTranslator {
  LinkTerminationPointOuterClass.LinkTerminationPoint translateLTPData(LtpInfo ltpObject, int neId);

  MlSync.Data translateLtpIntoData(int neId, LtpInfo ltpInfo, MlSync.Data.Command command);

  TunnelTerminationPointOuterClass.TunnelTerminationPoint translateTtpData(TtpInfo ttpInfo);

  ResourceGroupOuterClass.ResourceGroup translateResourceGroup(ResourceGroupInfo resourceGroupInfo);

  MlSync.Data getAllTtpsData(int neId);

  MlSync.Data getAllRGroupsData(int neId);
}
