/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.eth;

import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

public class TtpInfo {

  private final String name;
  private final EdgeBindingConstraints edgeBindingConstraints;
  private Set<LayerQualifier> supportedSignals; // internal only - used for associating TTPs with LTPs
  private Set<LayerQualifier> restrictedSignals; // internal only - used for storing signals that are restricted at a given time due to usage restrictions
  private boolean exclusiveGroupsMember; // internal only

  public TtpInfo(String name, EdgeBindingConstraints edgeBindingConstraints) {
    this.name = name;
    this.edgeBindingConstraints = edgeBindingConstraints;
  }

  public TtpInfo(String name, EdgeBindingConstraints edgeBindingConstraints, Set<LayerQualifier> supportedSignals) {
    this.name = name;
    this.edgeBindingConstraints = edgeBindingConstraints;
    this.supportedSignals = supportedSignals;
    this.restrictedSignals = new HashSet<>();
  }

  public TtpInfo(String name, EdgeBindingConstraints edgeBindingConstraints, Set<LayerQualifier> supportedSignals, boolean exclusiveGroupsMember) {
    this.name = name;
    this.edgeBindingConstraints = edgeBindingConstraints;
    this.supportedSignals = supportedSignals;
    this.restrictedSignals = new HashSet<>();
    this.exclusiveGroupsMember = exclusiveGroupsMember;
  }

  public String getName(){
    return this.name;
  }

  public EdgeBindingConstraints getEdgeBindingConstraints() { return edgeBindingConstraints; }

  public Set<LayerQualifier> getSupportedSignals() { return supportedSignals; }

  public void setSupportedSignals(Set<LayerQualifier> supportedSignals) { this.supportedSignals = supportedSignals; }

  public Set<LayerQualifier> getRestrictedSignals() { return restrictedSignals; }

  public boolean isExclusiveGroupsMember() { return exclusiveGroupsMember; }

  public void setExclusiveGroupsMember(boolean exclusiveGroupsMember) { this.exclusiveGroupsMember = exclusiveGroupsMember; }

  public boolean hasConstrainingEntries() {
    return getEdgeBindingConstraints() != null &&
      getEdgeBindingConstraints().getConstrainingEntryList() != null  &&
      !getEdgeBindingConstraints().getConstrainingEntryList().isEmpty();
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    TtpInfo ttpInfo = (TtpInfo) o;
    return Objects.equals(name, ttpInfo.name) &&
        Objects.equals(edgeBindingConstraints, ttpInfo.edgeBindingConstraints);
  }

  @Override
  public int hashCode() {
    return Objects.hashCode(name);
  }

  @Override
  public String toString() {
    return "\"" + name + "\"";
  }

}
