/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.resource.crm.model;

import com.adva.nlms.common.config.netypes.NeTypeIds;
import com.adva.nlms.inf.api.DestinationArea;
import com.adva.nlms.inf.api.notification.InternalNotificationObserverException;
import com.adva.nlms.inf.api.notification.NotificationAttribute;
import com.adva.nlms.inf.api.notification.NotificationManager;
import com.adva.nlms.inf.api.notification.observer.NotificationObserver;
import com.adva.nlms.inf.api.notification.selector.NotificationHeaderSelectorInjector;
import com.adva.nlms.mediation.messaging.inf.NEDeletionNotification;
import com.adva.nlms.mediation.messaging.inf.NENotification;
import com.adva.nlms.mediation.messaging.inf.NENotificationNETypeSelector;
import com.adva.nlms.mediation.messaging.inf.NEUpdateNotification;

public abstract class NeObserver implements NotificationObserver<NENotification>, NotificationHeaderSelectorInjector {
  // copied from com.adva.nlms.mediation.config.NetworkElementObjectAttributes to avoid pulling in mediation dependency
  private static final NotificationAttribute<Boolean> MANAGED_BY_NI = new NotificationAttribute<>("managedByNiController");

  @Override
  public void handle(NENotification notification) {
    if (niDisabledOnNe(notification) || niManagedNeDeleted(notification)) {
      handleNeRemoval(notification.getNotifiedObjectId());
    }
  }

  private boolean niManagedNeDeleted(NENotification notification) {
    return NEDeletionNotification.class.isAssignableFrom(notification.getClass()) &&
      Boolean.TRUE.equals(notification.getValue(MANAGED_BY_NI));
  }

  private boolean niDisabledOnNe(NENotification notification) {
    return NEUpdateNotification.class.isAssignableFrom(notification.getClass()) &&
      notification.containsKey(MANAGED_BY_NI) &&
      Boolean.FALSE.equals(notification.getValue(MANAGED_BY_NI));
  }

  protected abstract void handleNeRemoval(int neId);

  @Override
  public String getObserverID() {
    return getClass().getSimpleName();
  }

  @Override
  public DestinationArea getDestination() {
    return DestinationArea.SM;
  }

  @Override
  public void injectSelectors(NotificationManager moNotificationManager) throws InternalNotificationObserverException {
    moNotificationManager.addSelector(this, new NENotificationNETypeSelector(NeTypeIds.NETWORK_ELEMENT_TYPE_FSP3000C));
  }
}
