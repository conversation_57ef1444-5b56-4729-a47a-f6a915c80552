/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.eth;

import java.util.Objects;

public class ConstrainingEntry {

  private final String interfaceId;
  private ResourceDescriptor constrainedResources;

  public ConstrainingEntry(String interfaceId, ResourceDescriptor constrainedResources) {
    this.interfaceId = interfaceId;
    this.constrainedResources = constrainedResources;
  }

  public String getInterfaceId() {
    return interfaceId;
  }

  public ResourceDescriptor getConstrainedResources() {
    return constrainedResources;
  }

  public void setConstrainedResources(ResourceDescriptor constrainedResources) {
    this.constrainedResources = constrainedResources;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    ConstrainingEntry that = (ConstrainingEntry) o;
    return Objects.equals(interfaceId, that.interfaceId) &&
            Objects.equals(constrainedResources, that.constrainedResources);
  }

  @Override
  public int hashCode() {
    return Objects.hash(interfaceId, constrainedResources);
  }

}
