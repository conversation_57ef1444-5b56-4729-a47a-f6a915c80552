/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model;

import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

public interface CrmCtrlTaskScheduler {

  void initThreadPools();
  void submitProvisioningTask(int neId, Runnable task);

  Future<?> submitTask(Runnable task);

  Future<?> scheduleTask(Runnable task, long delay, TimeUnit unit);

  void schedulePeriodicTask(Runnable task, long period, TimeUnit unit);
}
