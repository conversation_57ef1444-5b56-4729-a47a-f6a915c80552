/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;


import org.slf4j.Logger;
import org.springframework.util.ObjectUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

public class TtpInfo {
  private String activeEndPoint = "";  // used only in legacy workflows
  private String name;								 // used to generate the InterfaceId
  private final EdgeBindingConstraints edgeBindingConstraints = new EdgeBindingConstraints();
  private boolean isTunable = false;
  private String sepUri;

  public TtpInfo() {}
  public TtpInfo(String name) {
    this.name = name;
  }

  public String getActiveEndPoint() {		return activeEndPoint;	}

  public void setActiveEndPoint(String activeEndPoint) {		this.activeEndPoint = activeEndPoint;	}

  public void removeActiveEndPoint(){
    this.activeEndPoint = "";
  }

  public String getName(){
    return this.name;
  }

  public void setName(String name) {		this.name = name;	}

  public EdgeBindingConstraints getEdgeBindingConstraints() {		return edgeBindingConstraints;	}

  public boolean isTunable() {
    return isTunable;
  }

  public void setTunable(boolean tunable) {
    this.isTunable = tunable;
  }

  public String getSepUri() {return sepUri;}

  public void setSepUri(String sepUri) { this.sepUri = sepUri;}

  public boolean hasConstrainingEntries() {
    return constrainingEntriesStream().findAny()
      .isPresent();
  }

  public boolean hasConstrainingEntryWithInterfaceId(String interfaceId) {
    if (ObjectUtils.isEmpty(interfaceId)) {
      return false;
    }
    return constrainingEntriesStream()
      .map(ConstrainingEntry::getInterfaceId)
      .anyMatch(interfaceId::equals);
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    TtpInfo ttpInfo = (TtpInfo) o;
    return Objects.equals(name, ttpInfo.name);
  }

  @Override
  public int hashCode() {
    return Objects.hashCode(name);
  }

  public List<ConstrainingEntry> findConstrainingEntriesByInterfaceId(String interfaceId) {
    if (ObjectUtils.isEmpty(interfaceId)) {
      return Collections.emptyList();
    }
    return constrainingEntriesStream()
      .filter(entry -> interfaceId.equals(entry.getInterfaceId()))
      .toList();
  }

  public Stream<ConstrainingEntry> constrainingEntriesStream() {
    return Optional.of(edgeBindingConstraints)
      .map(EdgeBindingConstraints::getSelectors)
      .stream()
      .flatMap(Collection::stream)
      .map(Selector::getConstrainingEntryList)
      .flatMap(Collection::stream);
  }

  @Override
  public String toString() {
    return "\"" + name + "\"";
  }

  public boolean hasPortId(String portId) {
    if (ObjectUtils.isEmpty(portId)) {
      return false;
    }
    return constrainingEntriesStream()
      .map(ConstrainingEntry::getConstrainedResource)
      .map(ConstrainedResource::getName)
      .anyMatch(portId::equals);
  }

  public boolean updateConstrainingEntryInterfaceId(String oldInterfaceId, String newInterfaceId, Logger log) {
    boolean anyUpdated = false;
    for (ConstrainingEntry entry : findConstrainingEntriesByInterfaceId(oldInterfaceId)) {
      entry.setInterfaceId(newInterfaceId);
      log.debug("Updated TTP {} constraining entry interfaceId from {} to {}", getName(), oldInterfaceId, newInterfaceId);
      anyUpdated = true;
    }
    return anyUpdated;
  }

  public void removeConstrainingEntryByInterfaceId(String interfaceId) {
    if (ObjectUtils.isEmpty(interfaceId)) {
      return;
    }
    edgeBindingConstraints.getSelectors()
      .stream()
      .map(Selector::getConstrainingEntryList)
      .forEach(list -> list.removeIf(e -> interfaceId.equals(e.getInterfaceId())));
  }

  public Selector getSingleSelectorForLegacyFlow() {
    if(edgeBindingConstraints.getSelectors().size()>1){
      throw new IllegalStateException("Too many TTP selectors (%s) for legacy flow".formatted(edgeBindingConstraints.getSelectors().size()));
    }
    return edgeBindingConstraints.getSelectors().stream().findFirst().orElse(null);
  }
}
