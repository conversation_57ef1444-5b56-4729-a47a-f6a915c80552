/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;

import java.util.List;

public class DynamicSpectrumConstraint extends SpectrumConstraint {
  public DynamicSpectrumConstraint(OneOfType type, String interfaceId) {
    super(type, interfaceId);
  }

  public static DynamicSpectrumConstraint generateAllowAllDynamicSpectrumConstraint(String otherEndLtpName) {
    var constraintType = new SpectrumConstraint.OneOfType(new SpectrumConstraint.All(), null);
    return new DynamicSpectrumConstraint(constraintType, otherEndLtpName);
  }

  public static DynamicSpectrumConstraint generateFrequencySlotsDynamicSpectrumConstraint(String otherEndLtpName, List<FrequencySlot> frequencySlots) {
    var frequencySlotPackage = new SpectrumConstraint.FrequencySlots(frequencySlots);
    var constraintType = new SpectrumConstraint.OneOfType(frequencySlotPackage);
    return new DynamicSpectrumConstraint(constraintType, otherEndLtpName);
  }
}
