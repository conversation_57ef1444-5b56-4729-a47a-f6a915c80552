/*
 *   Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *   Owner: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model;

import com.adva.nlms.common.util.StringUtils;
import com.adva.nlms.mediation.infrastructure.notification.tracing.api.in.TraceDefinitions;
import com.google.protobuf.AbstractMessage;
import ni.msg.EnvelopeOuterClass;
import ni.msg.EnvelopeOuterClass.Address;
import ni.msg.EnvelopeOuterClass.Envelope;
import ni.msg.EnvelopeOuterClass.MessageType;
import ni.proto.connection_segment.ConnectionSegmentOuterClass.PowerEqualizationOperation;
import ni.proto.ml.MlSync;
import ni.proto.mla.messages.MlaMessages;
import ni.proto.segment_callback.SegmentCallbackOuterClass;
import ni.proto.segment_callback.SegmentCallbackOuterClass.AssignRoles;
import ni.proto.segment_callback.SegmentCallbackOuterClass.Create;
import ni.proto.segment_callback.SegmentCallbackOuterClass.Delete;
import ni.proto.segment_callback.SegmentCallbackOuterClass.Error;
import ni.proto.segment_callback.SegmentCallbackOuterClass.PowerEqualize;
import ni.proto.segment_callback.SegmentCallbackOuterClass.RequestStatus;
import ni.proto.segment_callback.SegmentCallbackOuterClass.Response;
import ni.proto.segment_callback.SegmentCallbackOuterClass.SegmentCallback;
import ni.proto.segment_callback.SegmentCallbackOuterClass.SetAdminState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

/*
 * This is the base class for WDM and ETH translators in CRM model
 * This class implements common methods for both layers
 */
class CrmNiTranslatorImpl implements CrmNiTranslator {

  private final Logger log = LoggerFactory.getLogger(CrmNiTranslatorImpl.class);

  @Override
  public Envelope createSegmentCallbackResponse(String segmentRequestId,
                                                SegmentCallbackData segmentCallbackData,
                                                Optional<PowerEqualizationOperation> optEqlzDirection,
                                                Address incomingSrcAddress,
                                                Address incomingDstAddress,
                                                List<String> deletedSegments) {
    Error error = Error.newBuilder().setMessage(segmentCallbackData.message()).build();
    RequestStatus status = RequestStatus.newBuilder().setStatus(segmentCallbackData.statusType()).setError(error).build();

    Response.Builder responseBuilder = Response.newBuilder();
    responseBuilder.setId(segmentRequestId).setStatus(status);

    switch (segmentCallbackData.operation()) {
      case CREATE:
        var builder = Create.newBuilder();
        if (deletedSegments != null) {
          builder.addAllDeletedSegments(deletedSegments);
        }
        Create create = builder.build();
        responseBuilder.setCreate(create);
        break;

      case DELETE:
        Delete delete = Delete.newBuilder().build();
        responseBuilder.setDelete(delete);
        break;

      case POWEREQUALIZE:
        PowerEqualize.Builder powerEqBuilder = PowerEqualize.newBuilder();
        optEqlzDirection.ifPresent(powerEqBuilder::setOperation);
        PowerEqualize powerEqualize = powerEqBuilder.build();
        responseBuilder.setPowerEqualize(powerEqualize);
        break;

      case SETADMINSTATE:
        SetAdminState setAdminState = SetAdminState.newBuilder().build();
        responseBuilder.setSetAdminState(setAdminState);
        break;

      default:
        log.error("CrmNiTranslator: createSegmentCallback failed. Unable to handle operation {}", segmentCallbackData.operation());
        break;
    }

    Response response = responseBuilder.build();

    SegmentCallback segmentCallback = SegmentCallback.newBuilder().setResponse(response).build();

    // set the dst and src addresses of the outgoing envelope, using the addresses from the incoming envelope
    return buildSegmentCallbackEnvelope(incomingSrcAddress, incomingDstAddress, segmentCallback);
  }

  @Override
  public Envelope createSegmentCallbackAssignRoles(AssignRolesCallback assignRolesCallback) {
    AssignRoles.Builder assignRoles = AssignRoles.newBuilder();
    assignRoles.setWorkingLegId(assignRolesCallback.workingLeg());
    assignRoles.setProtectionLegId(assignRolesCallback.protectionLeg());
    Error error = Error.newBuilder().setMessage(assignRolesCallback.message()).build();
    RequestStatus requestStatus = RequestStatus.newBuilder()
      .setStatus(assignRolesCallback.statusType())
      .setError(error)
      .build();
    assignRoles.setStatus(requestStatus);
    SegmentCallback callback = SegmentCallback.newBuilder().setAssignRoles(assignRoles.build()).build();
    return buildSegmentCallbackEnvelope(assignRolesCallback.incomingSrcAddress(), assignRolesCallback.incomingDstAddress(), callback);
  }

  @Override
  public Envelope createOutageSegmentCallback(OutageSegmentCallback outageSegmentCallback, AlarmType alarmAction) {
    SegmentCallbackOuterClass.AlarmNotify alarmNotify = translateOperationalStatusChange(outageSegmentCallback, alarmAction);
    SegmentCallback segmentCallback = SegmentCallback.newBuilder().setAlarmNotify(alarmNotify).build();
    return buildSegmentCallbackEnvelope(outageSegmentCallback.incomingSrcAddress(), outageSegmentCallback.incomingDstAddress(), segmentCallback);
  }

  private SegmentCallbackOuterClass.AlarmNotify translateOperationalStatusChange(OutageSegmentCallback outageSegmentCallback, AlarmType alarmType) {
    return SegmentCallbackOuterClass.AlarmNotify.newBuilder()
      .setSegmentId(outageSegmentCallback.segmentId())
      .setTerminationPoint(outageSegmentCallback.terminationPoint())
      .setOscStatus(OSCStatusTranslator.toNiAlarmNotifyOscStatus(outageSegmentCallback.oscStatus()))
      .setEventType(translateEventType(alarmType))
      .setNoClientSignal(outageSegmentCallback.noClientSignal())
      .build();
  }

  private SegmentCallbackOuterClass.AlarmNotify.EventType translateEventType(AlarmType alarmType) {
    return switch (alarmType) {
      case RAISE -> SegmentCallbackOuterClass.AlarmNotify.EventType.ALARM_SET;
      case CLEAR -> SegmentCallbackOuterClass.AlarmNotify.EventType.ALARM_CLEAR;
      default -> throw new IllegalArgumentException("Unknown alarm type: " + alarmType);
    };
  }

  @Override
  public Optional<Envelope> buildEnvelope(int neId, List<MlSync.Data> datas, MlaMessages.Message.TypeCase messageType) {
    datas = datas.stream().filter(Objects::nonNull).toList();
    MlaMessages.Message topLevelMessage;
    switch (messageType) {
      case UPDATE -> {
        MlaMessages.Update updateMessage = MlaMessages.Update.newBuilder()
          .addAllData(datas).build();
        topLevelMessage = MlaMessages.Message.newBuilder().setUpdate(updateMessage).build();
      }

      case SYNCACK -> {
        MlaMessages.SyncAck syncAckMessageData = MlaMessages.SyncAck.newBuilder()
          .addAllData(datas)
          .setFinal(true)
          .build();
        topLevelMessage = MlaMessages.Message.newBuilder().setSyncAck(syncAckMessageData).build();
      }
      default -> {
        return Optional.empty();
      }
    }
    EnvelopeOuterClass.Application dstType = EnvelopeOuterClass.Application.MLC;
    EnvelopeOuterClass.Identifier identifier = EnvelopeOuterClass.Identifier.newBuilder().setId("mlc").build();
    Address dst = Address.newBuilder()
      .setType(dstType).setId(identifier).build();

    EnvelopeOuterClass.Application srcType = EnvelopeOuterClass.Application.MLA_AGENT;
    EnvelopeOuterClass.Identifier srcId = EnvelopeOuterClass.Identifier.newBuilder()
      .setId(String.valueOf(neId))
      .build();
    Address src = Address.newBuilder()
      .setType(srcType).setId(srcId).build();

    var envelope = Envelope.newBuilder()
      .setDestination(dst)
      .setSource(src)
      .setType(MessageType.MLC_UPDATE)
      .setPayload(topLevelMessage.toByteString())
      .setXCorrelationId(getCorrelationId())
      .build();
    return Optional.of(envelope);
  }

  @Override
  public MlSync.Data mergeDataPayload(List<MlSync.Data> datas) {
    log.debug("Collecting TTPs from all layers to one message");
    if (datas.isEmpty()) {
      log.error("Empty TTPs list");
      return null;
    }

    MlSync.Data.Builder finalMessageBuilder = datas.get(0).toBuilder();
    for (int i = 1; i < datas.size(); i++) {
      finalMessageBuilder.mergePayload(datas.get(i).getPayload());
    }

    return finalMessageBuilder.build();
  }

  private Envelope buildSegmentCallbackEnvelope(Address incomingSrcAddress, Address incomingDstAddress, AbstractMessage message) {
    return Envelope.newBuilder()
      .setDestination(incomingSrcAddress)
      .setSource(incomingDstAddress)
      .setType(MessageType.SEGMENT_CALLBACK)
      .setPayload(message.toByteString())
      .setXCorrelationId(getCorrelationId())
      .build();
  }

  private static String getCorrelationId() {
    var correlationId = MDC.get(TraceDefinitions.CORRELATION_ID_LOG_VAR_NAME);
    return StringUtils.isNotEmpty(correlationId) ? correlationId : UUID.randomUUID().toString();
  }
}
