/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.eth;

import com.adva.nlms.mediation.topology.NEDataProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CrmEthModelConfiguration {

    @Bean
    public CrmEthModelDAO crmEthModelDAO() {
        return new CrmEthModelDAOImpl();
    }

    @Bean
    public CrmEthNiTranslator crmEthNiTranslator(NEDataProvider neDataProvider, CrmEthModelDAO crmEthModelDAO) {
        return new CrmEthNiTranslatorImpl(neDataProvider, crmEthModelDAO);
    }

}
