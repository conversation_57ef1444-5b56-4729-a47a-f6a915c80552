/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;

import java.util.Optional;

public final class NonTunable implements ConstrainedResource {
  private final String name;                      // name of the 'port'
  private ChannelType channelType;
  private double channel;
  private int bandwidth;              // current bandwidth as seen from a transponder's port (used in SlotWidthParams of NI model)
  private int slotwidth;             // slot width occupied by this transponder's port on LTP (used in SlotWidthParams of NI model)

  private int slotWidthConstraints;   // the maximum bandwidth that this transponder's port can support (used in SlotWidthConstraints of NI model)

  public NonTunable(String name) {
    this.name = name;
  }

  public String getName() {
    return name;
  }

  @Override
  public ChannelType getChannelType() {
    return channelType;
  }

  public void setChannelType(ChannelType channelType) {
    this.channelType = channelType;
  }

  public double getChannel() {
    return channel;
  }

  public void setChannel(double channel) {
    this.channel = channel;
  }

  public int getBandwidth() {
    return bandwidth;
  }

  public void setBandwidth(int bandwidth) {
    this.bandwidth = bandwidth;
  }

  public void clearBandwidth() {
    this.bandwidth = 0;
  }

  public int getSlotwidth() {
    return slotwidth;
  }

  public void setSlotwidth(int slotWidth) {
    this.slotwidth = slotWidth;
  }

  public void clearSlotwidth() {
    this.slotwidth = 0;
  }

  public int getSlotWidthConstraints() {
    return slotWidthConstraints;
  }

  public void setSlotWidthConstraints(int slotWidthConstraints) {
    this.slotWidthConstraints = slotWidthConstraints;
  }

  @Override
  public Optional<FrequencySlot> getCurrentFrequencySlot() {
    var centerFreq = (int) getChannel();
    if (centerFreq == 0 && slotwidth == 0) {
      return Optional.empty();
    } else {
      return Optional.of(new FrequencySlot(centerFreq, slotwidth, channelType));
    }
  }

  @Override
  public void setFrequencySlot(FrequencySlot frequencySlot) {
    setBandwidth(frequencySlot.slotwidth());
    setSlotwidth(frequencySlot.slotwidth());
    setChannel(frequencySlot.centerFreq());
    setChannelType(frequencySlot.channelType());
  }

  @Override
  public void clearFrequencySlotAndBandwidth() {
    // As FrequencySlot is fixed for NonTunable, it won't be cleared
    clearBandwidth();
    clearSlotwidth();
  }

}
