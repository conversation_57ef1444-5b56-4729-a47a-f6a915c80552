/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */
package com.adva.nlms.resource.crm.model;

import com.adva.nlms.mediation.infrastructure.concurrent.AdvaExecutors;
import com.adva.nlms.mediation.infrastructure.concurrent.NamedThreadFactory;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

class CrmCtrlTaskSchedulerImpl implements CrmCtrlTaskScheduler {
  private ScheduledExecutorService crmTaskService;
  private ExecutorService crmProvisioningService;
  private final Map<Integer, BlockingQueue<Runnable>> elementQueues = new ConcurrentHashMap<>();
  private final Set<Integer> activeElements = ConcurrentHashMap.newKeySet();

  @Override
  public synchronized void initThreadPools() {
    if (crmTaskService == null) {
      crmTaskService = AdvaExecutors.newSingleThreadScheduledExecutor(new NamedThreadFactory("CrmTasks"), true);
    }
    if (crmProvisioningService == null) {
      crmProvisioningService = AdvaExecutors.newFixedThreadPool(5, new NamedThreadFactory("CrmProvTasks"), true);
    }
  }

  @Override
  public void submitProvisioningTask(int neId, Runnable task) {
    if (crmProvisioningService == null)
      return;
    elementQueues.compute(neId, (integer, runnables) -> {
      if (runnables == null) {
        runnables = new LinkedBlockingQueue<>();
      }
      runnables.add(task);
      return runnables;
    });
    startProcessingIfNeeded(neId);
  }

  private void startProcessingIfNeeded(int neId) {
    if (activeElements.add(neId)) {
      crmProvisioningService.submit(() -> processQueue(neId));
    }
  }

  private void processQueue(int neId) {
    try {
      BlockingQueue<Runnable> queue = elementQueues.get(neId);
      while (queue != null && !queue.isEmpty()) {
        Runnable task = queue.poll();
        if (task != null) {
          task.run();
        }
      }
    } finally {
      activeElements.remove(neId);
      elementQueues.computeIfPresent(neId, (id, q) -> q.isEmpty() ? null : q);
      if (elementQueues.containsKey(neId)) {
        startProcessingIfNeeded(neId);
      }
    }
  }

  @Override
  public Future<?> submitTask(Runnable task) {
    if (crmTaskService == null) return null;
    return crmTaskService.submit(task);
  }

  @Override
  public Future<?> scheduleTask(Runnable task, long delay, TimeUnit unit) {
    if (crmTaskService == null) return null;
    return crmTaskService.schedule(task, delay, unit);
  }

  @Override
  public void schedulePeriodicTask(Runnable task, long period, TimeUnit unit) {
    if (crmTaskService != null)
      crmTaskService.scheduleAtFixedRate(task, period, period, unit);
  }
}
