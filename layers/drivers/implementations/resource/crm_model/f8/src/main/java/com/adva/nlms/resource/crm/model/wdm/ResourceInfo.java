/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;

import java.util.Objects;

public class ResourceInfo {
  private final boolean isTunable;
  private final Port port;

  public ResourceInfo(Port port, boolean isTunable) {
    this.isTunable = isTunable;
    this.port = port;
  }

  public Port getTunable() {
    return isTunable ? port : null;
  }

  public Port getNonTunable() {
    return isTunable ? null : port;
  }

  public boolean isTunable() {
    return isTunable;
  }

  public String getPortId() {
    return port.portId();
  }

  @Override
  public String toString() {
    String t = isTunable ? "(T)" : "(N)";
    return "\"" + port.portId() + t + "\"";
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    ResourceInfo that = (ResourceInfo) o;
    return Objects.equals(this.getPortId(), that.getPortId());
  }

  @Override
  public int hashCode() {
    return Objects.hash(isTunable, port);
  }
}