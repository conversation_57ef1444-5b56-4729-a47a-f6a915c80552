/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.crm.model.wdm;

import ni.proto.inet.InterfaceId;
import ni.proto.mla.ChannelOuterClass;
import ni.proto.mla.FrequencyOuterClass;
import ni.proto.mla.FrequencyRangeOuterClass;
import ni.proto.mla.PortIdOuterClass;
import ni.proto.mla.ResourceGroupOuterClass;
import ni.proto.mla.SlotWidth;
import ni.proto.mla.SlotWidthConstraintsOuterClass;
import ni.proto.mla.TunnelTerminationPointOuterClass;

import java.util.ArrayList;
import java.util.List;

final class TtpAndResourceGroupNiTranslator {
  private TtpAndResourceGroupNiTranslator() {
    throw new UnsupportedOperationException("Util class. Creating an instance of this class is forbidden.");
  }

  static ResourceGroupOuterClass.ResourceGroup translateResourceGroup(ResourceGroupInfo resourceGroupInfo) {
    List<ResourceGroupOuterClass.ResourceGroup.Resource> resources = new ArrayList<>();
    for (ResourceInfo resourceInfo : resourceGroupInfo.getResourceList()) {
      //Translate each ResourceInfo into proto Resource
      var portBuilder = ResourceGroupOuterClass.ResourceGroup.Port.newBuilder();
      var port = resourceInfo.isTunable() ? resourceInfo.getTunable() : resourceInfo.getNonTunable();

      var portId = PortIdOuterClass.PortId.newBuilder().setName(port.portId()).build();
      portBuilder.setPortId(portId);

      if (port.slotWidth() != 0 && port.channelType() != null) {
        var slotWidthFrequency = TtpAndResourceGroupNiTranslator.frequency(port.slotWidth(), port.channelType());
        portBuilder.setSlotWidth(slotWidthFrequency);
      }

      if (port.channel() != 0 && port.channelType() != null) {
        portBuilder.setChannel(TtpAndResourceGroupNiTranslator.channel(port.channel(), port.channelType()));
      }

      ResourceGroupOuterClass.ResourceGroup.Resource resource = resourceInfo.isTunable()
        ? ResourceGroupOuterClass.ResourceGroup.Resource.newBuilder()
        .setTunable(portBuilder.build())
        .build()
        : ResourceGroupOuterClass.ResourceGroup.Resource.newBuilder()
        .setNonTunable(portBuilder.build())
        .build();
      resources.add(resource);
    }

    return ResourceGroupOuterClass.ResourceGroup.newBuilder()
      .setId(resourceGroupInfo.getId())
      .addAllResource(resources)
      .build();
  }

  static TunnelTerminationPointOuterClass.TunnelTerminationPoint translateTtpData(TtpInfo ttpInfo) {
    var ttpProto = TunnelTerminationPointOuterClass.TunnelTerminationPoint.newBuilder()
      .setInterfaceId(TtpAndResourceGroupNiTranslator.interfaceId(ttpInfo.getName()));

    var edgeBindingConstraintsProto = TunnelTerminationPointOuterClass.EdgeBindingConstraints.newBuilder()
      .setType(TunnelTerminationPointOuterClass.EdgeBindingConstraints.Type.TYPE_TERMINATION);

    for (Selector selector : ttpInfo.getEdgeBindingConstraints().getSelectors()) {
      var selectorProto = TunnelTerminationPointOuterClass.Selector.newBuilder()
        .setResourceGroupId(selector.getResourceGroupId());
      TtpAndResourceGroupNiTranslator.addSelectedInterfaceId(selectorProto, selector.getSelectedInterfaceId());

      for (ConstrainingEntry constrainingEntry : selector.getConstrainingEntryList()) {
        var constrainingEntryProto = TunnelTerminationPointOuterClass.ConstrainingEntry.newBuilder()
          .setInterfaceId(TtpAndResourceGroupNiTranslator.interfaceId(constrainingEntry.getInterfaceId())); /* InterfaceId is the name of the LTP */

        if (constrainingEntry.getConstrainedResource() instanceof Tunable tunable) {
          constrainingEntryProto.setTunable(TtpAndResourceGroupNiTranslator.tunableProto(tunable));

        } else if (constrainingEntry.getConstrainedResource() instanceof NonTunable nonTunableLocal) {
          constrainingEntryProto.setNonTunable(TtpAndResourceGroupNiTranslator.nonTunableProto(nonTunableLocal));
        }

        selectorProto.addConstrainingEntry(constrainingEntryProto);
      }

      edgeBindingConstraintsProto.addSelector(selectorProto);
    }
    // case for OPPM single channel protection
    if (ttpInfo.getEdgeBindingConstraints().getSelectors().size() > 1) {
      edgeBindingConstraintsProto.addFlags(TunnelTerminationPointOuterClass.EdgeBindingConstraints.Flags.FLAG_SPLITTER);
    }
    ttpProto.setEdgeBindingConstraints(edgeBindingConstraintsProto);
    return ttpProto.build();
  }

  private static InterfaceId.Builder interfaceId(String interfaceIdString) {
    return InterfaceId.newBuilder()
      .setName(interfaceIdString);
  }

  private static void addRange(TunnelTerminationPointOuterClass.ConstrainingEntry.Tunable.Builder tunableBuilder, double base, double interval, int count) {
    var range = TunnelTerminationPointOuterClass.ConstrainingEntry.Tunable.Range.newBuilder()
      .setBase(base)
      .setInterval(interval)
      .setCount(count);
    tunableBuilder.setRange(range);
  }

  private static FrequencyRangeOuterClass.FrequencyRange.Builder frequencyRange(int constrainingRangeFirst, int constrainingRangeLast) {
    return FrequencyRangeOuterClass.FrequencyRange.newBuilder()
      .setFirst(FrequencyOuterClass.Frequency.newBuilder()
        .setMhz(FrequencyOuterClass.Frequency.MHz.newBuilder()
          .setValue(constrainingRangeFirst)
          .build())
        .build())
      .setLast(FrequencyOuterClass.Frequency.newBuilder()
        .setMhz(FrequencyOuterClass.Frequency.MHz.newBuilder()
          .setValue(constrainingRangeLast)
          .build())
        .build());
  }

  private static PortIdOuterClass.PortId.Builder portId(String portId) {
    return PortIdOuterClass.PortId.newBuilder()
      .setName(portId);
  }

  private static void addSlotWidthConstraints(TunnelTerminationPointOuterClass.ConstrainingEntry.Tunable.Builder tunableBuilder,
                                              int constrainingRangeFirst,
                                              int constrainingRangeLast) {
    if (constrainingRangeFirst != 0 && constrainingRangeLast != 0) {
      var slotWidthConstraints = SlotWidthConstraintsOuterClass.SlotWidthConstraints.newBuilder()
        .setConstrainingRange(frequencyRange(constrainingRangeFirst, constrainingRangeLast));
      tunableBuilder.setSlotWidthConstraints(slotWidthConstraints);
    }
  }

  private static FrequencyOuterClass.Frequency.Builder frequency(int frequency, ChannelType channelType) {
    var builder = FrequencyOuterClass.Frequency.newBuilder();
    switch (channelType) {
      case DWDM -> builder.setMhz(FrequencyOuterClass.Frequency.MHz.newBuilder()
        .setValue(frequency)
      );
      case CWDM, GRAY -> builder.setNm(FrequencyOuterClass.Frequency.Nm.newBuilder()
        .setValue(frequency)
      );
    }
    return builder;
  }

  private static void addSlotWidthParams(TunnelTerminationPointOuterClass.ConstrainingEntry.Tunable.Builder tunableBuilder, int bandwidth, int slotwidth) {
    if (bandwidth != 0 && slotwidth != 0) {
      tunableBuilder.setSlotWidthParams(slotWidthParams(bandwidth, slotwidth, Tunable.CHANNEL_TYPE));
    } else {
      // Temporary until SIT corrects the tests to expect no slot with params instead of empty object
      tunableBuilder.setSlotWidthParams(SlotWidth.SlotWidthParams.newBuilder());
    }
  }

  private static void nonTunableAddSlotWidthParams(TunnelTerminationPointOuterClass.ConstrainingEntry.NonTunable.Builder nonTunableBuilder, int bandwidth, int slotwidth, ChannelType channelType) {
    if (bandwidth != 0 && slotwidth != 0 && channelType != null) {
      nonTunableBuilder.setSlotWidthParams(slotWidthParams(bandwidth, slotwidth, channelType));
    } else {
      // Temporary until SIT corrects the tests to expect no slot with params instead of empty object
      if (channelType == ChannelType.DWDM) {
        nonTunableBuilder.setSlotWidthParams(SlotWidth.SlotWidthParams.newBuilder()
          .setBandwidth(FrequencyOuterClass.Frequency.newBuilder()
            .setMhz(FrequencyOuterClass.Frequency.MHz.newBuilder())));
      }
    }
  }

  private static SlotWidth.SlotWidthParams.Builder slotWidthParams(int bandwidth, int slotwidth, ChannelType channelType) {
    return SlotWidth.SlotWidthParams.newBuilder()
      .setBandwidth(frequency(bandwidth, channelType))
      .setSlotWidth(frequency(slotwidth, channelType));
  }

  private static void tunableAddChannel(TunnelTerminationPointOuterClass.ConstrainingEntry.Tunable.Builder tunableBuilder, double currentChannel) {
    if (currentChannel != 0) {
      tunableBuilder.setCurrentChannel(channel(currentChannel, Tunable.CHANNEL_TYPE));
    }
  }

  private static void nonTunableAddChannel(TunnelTerminationPointOuterClass.ConstrainingEntry.NonTunable.Builder nonTunableBuilder, double channel, ChannelType channelType) {
    if (channel != 0 && channelType != null) {
      nonTunableBuilder.setChannel(channel(channel, channelType));
    }
  }

  private static ChannelOuterClass.Channel.Builder channel(double channel, ChannelType channelType) {
    return switch (channelType) {
      case CWDM -> cwdmChannel(channel);
      case DWDM -> dwdmChannel(channel);
      case GRAY -> grayChannel(channel);
    };
  }

  private static ChannelOuterClass.Channel.Builder dwdmChannel(double channel) {
    double channelInGhz = channel / 1000.0;
    ChannelOuterClass.Channel.Dwdm.Builder dwdm = ChannelOuterClass.Channel.Dwdm.newBuilder()
      .setChannel(channelInGhz);
    return ChannelOuterClass.Channel.newBuilder()
      .setDwdm(dwdm);
  }

  private static ChannelOuterClass.Channel.Builder cwdmChannel(double channel) {
    var builder = ChannelOuterClass.Channel.Cwdm.newBuilder()
      .setChannel(channel);
    return ChannelOuterClass.Channel.newBuilder()
      .setCwdm(builder);
  }

  private static ChannelOuterClass.Channel.Builder grayChannel(double channel) {
    var builder = ChannelOuterClass.Channel.Gray.newBuilder()
      .setChannel(channel);
    return ChannelOuterClass.Channel.newBuilder()
      .setGray(builder);
  }

  private static TunnelTerminationPointOuterClass.ConstrainingEntry.Tunable.Builder tunableProto(Tunable tunable) {
    var tunableProto = TunnelTerminationPointOuterClass.ConstrainingEntry.Tunable.newBuilder()
      .setPortId(portId(tunable.getName()));
    addRange(tunableProto, tunable.getBase() / 1000, tunable.getInterval() / 1000, tunable.getCount());
    addSlotWidthParams(tunableProto, tunable.getBandwidth(), tunable.getSlotwidth());
    tunableAddChannel(tunableProto, tunable.getCurrentChannel());
    addSlotWidthConstraints(tunableProto, tunable.getConstrainingRangeFirst(), tunable.getConstrainingRangeLast());
    return tunableProto;
  }

  private static void nonTunableAddSlotWidthConstraints(TunnelTerminationPointOuterClass.ConstrainingEntry.NonTunable.Builder nonTunableBuilder, int slotWidthConstraints, ChannelType channelType) {
    if (slotWidthConstraints > 0 && channelType != null) {
      var slotWidthConstraintsProto = SlotWidthConstraintsOuterClass.SlotWidthConstraints.newBuilder().setMaxSlotWidth(frequency(slotWidthConstraints, channelType));     // only for filters
      nonTunableBuilder.setSlotWidthConstraints(slotWidthConstraintsProto);
    }
  }

  private static TunnelTerminationPointOuterClass.ConstrainingEntry.NonTunable.Builder nonTunableProto(NonTunable nonTunable) {
    var nonTunableProto = TunnelTerminationPointOuterClass.ConstrainingEntry.NonTunable.newBuilder()
      .setPortId(portId(nonTunable.getName()));
    nonTunableAddChannel(nonTunableProto, nonTunable.getChannel(), nonTunable.getChannelType());
    nonTunableAddSlotWidthParams(nonTunableProto, nonTunable.getBandwidth(), nonTunable.getSlotwidth(), nonTunable.getChannelType());
    nonTunableAddSlotWidthConstraints(nonTunableProto, nonTunable.getSlotWidthConstraints(), nonTunable.getChannelType());
    return nonTunableProto;
  }

  private static void addSelectedInterfaceId(TunnelTerminationPointOuterClass.Selector.Builder selector, String selectedInterfaceString) {
    if (selectedInterfaceString != null) {
      selector.setSelectedInterfaceId(interfaceId(selectedInterfaceString));
    }
  }
}
