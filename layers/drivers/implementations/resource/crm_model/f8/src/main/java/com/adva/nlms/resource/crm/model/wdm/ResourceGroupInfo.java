/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class ResourceGroupInfo {
  int id;
  List<ResourceInfo> resourceList;

  public ResourceGroupInfo(int id, List<ResourceInfo> resourceList) {
    this.id = id;
    this.resourceList = new ArrayList<>(resourceList);
  }

  public int getId() {
    return id;
  }

  public List<ResourceInfo> getResourceList() {
    return resourceList;
  }

  public void addResourceList(List<ResourceInfo> resourceInfos) {
    resourceList.removeAll(resourceInfos);
    resourceList.addAll(resourceInfos);
  }

  void removePortsByIds(List<String> portIds) {
    resourceList.removeIf(resourceInfo -> portIds.contains(resourceInfo.getPortId()));
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    ResourceGroupInfo that = (ResourceGroupInfo) o;
    return id == that.id;
  }

  @Override
  public int hashCode() {
    return Objects.hash(id);
  }

  @Override
  public String toString() {
    return "{\"id\":" + id + ",\"resourceList\":[" +
      resourceList.stream().map(ResourceInfo::toString).sorted().collect(Collectors.joining(",")) +
      "]}";
  }
}