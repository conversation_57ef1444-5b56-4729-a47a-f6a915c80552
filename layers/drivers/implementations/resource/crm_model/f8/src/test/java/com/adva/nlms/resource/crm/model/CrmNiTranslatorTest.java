/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model;

import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.resource.crm.model.eth.ConstrainingEntry;
import com.adva.nlms.resource.crm.model.eth.CrmEthModelConfiguration;
import com.adva.nlms.resource.crm.model.eth.CrmEthModelDAO;
import com.adva.nlms.resource.crm.model.eth.CrmEthNiTranslator;
import com.adva.nlms.resource.crm.model.eth.EdgeBindingConstraints;
import com.adva.nlms.resource.crm.model.eth.TtpInfo;
import com.adva.nlms.resource.crm.model.wdm.ChannelType;
import com.adva.nlms.resource.crm.model.wdm.CrmWdmModelConfiguration;
import com.adva.nlms.resource.crm.model.wdm.CrmWdmModelDAO;
import com.adva.nlms.resource.crm.model.wdm.CrmWdmNiTranslator;
import com.adva.nlms.resource.crm.model.wdm.TtpInfoBuilder;
import ni.proto.inet.IpAddr;
import ni.proto.ml.MlSync;
import ni.proto.mla.NodeOuterClass;
import ni.proto.mla.TunnelTerminationPointOuterClass;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class CrmNiTranslatorTest {

  static private final int NETWORK_ELEMENT_ID = 3;
  static private final String ROUTER_IP = "*******";

  private final NEDataProvider neDataProvider = Mockito.mock(NEDataProvider.class);
  private final CrmWdmModelDAO crmWdmModelDAO = Mockito.mock(CrmWdmModelDAO.class);
  private final CrmEthModelDAO crmEthModelDAO = Mockito.mock(CrmEthModelDAO.class);
  private final CrmWdmNiTranslator crmWdmNiTranslator = new CrmWdmModelConfiguration().crmWdmNiTranslator(neDataProvider, crmWdmModelDAO);
  private final CrmEthNiTranslator crmEthNiTranslator = new CrmEthModelConfiguration().crmEthNiTranslator(neDataProvider, crmEthModelDAO);

  CrmNiTranslatorImpl sut = new CrmNiTranslatorImpl();

  @Test
  @DisplayName("""
    GIVEN: Proto messages with TTPs for WDM and ETH layers
    WHEN: Collect all TTPs messages into one
    THEN: Single proto TTP message containing TTPs from all layers
    """)
  public void testMergeDataPayload() {
    // given
    TunnelTerminationPointOuterClass.TunnelTerminationPoint wdmTtp = prepareTtpForWdmLayer();
    TunnelTerminationPointOuterClass.TunnelTerminationPoint ethTtp = prepareTtpForEthLayer();
    MlSync.Data wdmTtpMessage = prepareTtpsMessageForLayer(List.of(wdmTtp));
    MlSync.Data ethTtpMessage = prepareTtpsMessageForLayer(List.of(ethTtp));
    List<MlSync.Data> ttpsData = List.of(wdmTtpMessage, ethTtpMessage);

    // when
    var ttpProto = sut.mergeDataPayload(ttpsData);

    // then
    assertEquals("TTPs", ttpProto.getHeader().getId());
    assertEquals(MlSync.Data.Command.CMD_UPDATE, ttpProto.getCommand());
    assertEquals(ROUTER_IP, ttpProto.getRouter().getS());
    assertEquals(2, ttpProto.getPayload().getTtps().getTtpsCount());
    assertTrue(ttpProto.getPayload().getTtps().getTtpsList().contains(wdmTtp));
    assertTrue(ttpProto.getPayload().getTtps().getTtpsList().contains(ethTtp));
  }

  TunnelTerminationPointOuterClass.TunnelTerminationPoint prepareTtpForWdmLayer() {
    com.adva.nlms.resource.crm.model.wdm.TtpInfo ttpInfo = TtpInfoBuilder.builder()
      .name("Port-5/1/n2")
      .tunable(false)
      .newSelector(s -> s.resourceGroupId(3)
        .selectedInterfaceId("Port-3/19/n")
        .newConstrainingEntry(e -> e.interfaceId("PTP-3/19/n")
          .nonTunableResource(t -> t.name("Port-5/1/n2")
            .bandwidth(37500)
            .channelType(ChannelType.DWDM)
            .channel(196000.0)
            .slotwidth(37500)
            .slotWidthConstraints(75000))))
      .build();
    return crmWdmNiTranslator.translateTtpData(ttpInfo);
  }

  private TunnelTerminationPointOuterClass.TunnelTerminationPoint prepareTtpForEthLayer() {
    String ttpName = "1/1/p3";
    String ltpName = "ET400OZRP-1/1/p2/et400ozrp";
    com.adva.nlms.resource.crm.model.eth.ConstrainingEntry constrainingEntry = new ConstrainingEntry(ltpName, null);
    com.adva.nlms.resource.crm.model.eth.EdgeBindingConstraints edgeBindingConstraints = new EdgeBindingConstraints();
    edgeBindingConstraints.setConstrainingEntryList(List.of(constrainingEntry));
    com.adva.nlms.resource.crm.model.eth.TtpInfo ttpInfo = new TtpInfo(ttpName, edgeBindingConstraints);

    return crmEthNiTranslator.translateTtpData(ttpInfo, NETWORK_ELEMENT_ID);
  }

  private MlSync.Data prepareTtpsMessageForLayer(List<TunnelTerminationPointOuterClass.TunnelTerminationPoint> ttpsList) {
    NodeOuterClass.TunnelTerminationPoints tunnelTerminationPoints = NodeOuterClass.TunnelTerminationPoints.newBuilder()
      .addAllTtps(ttpsList)
      .build();

    MlSync.Data.Payload payloadData = MlSync.Data.Payload.newBuilder()
      .setTtps(tunnelTerminationPoints)
      .build();

    MlSync.Header header = MlSync.Header.newBuilder()
      .setId("TTPs")
      .setEpoch(String.valueOf(System.currentTimeMillis()))
      .setVersion(1)
      .build();

    IpAddr routerAddress = IpAddr.newBuilder().setS(ROUTER_IP).build();

    return MlSync.Data.newBuilder()
      .setCommand(MlSync.Data.Command.CMD_UPDATE)
      .setPayload(payloadData)
      .setHeader(header)
      .setRouter(routerAddress)
      .build();
  }
}
