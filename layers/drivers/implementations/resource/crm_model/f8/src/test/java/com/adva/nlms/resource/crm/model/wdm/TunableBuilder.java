/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.crm.model.wdm;

@SuppressWarnings("UnusedReturnValue")
public final class TunableBuilder {

  String name;

  int base;
  int interval;
  int count;

  double currentChannel;

  int bandwidth;
  int slotwidth;
  int constrainingRangeFirst;
  int constrainingRangeLast;

  TunableBuilder() {
  }

  public TunableBuilder name(String name) {
    this.name = name;
    return this;
  }

  public TunableBuilder base(int base) {
    this.base = base;
    return this;
  }

  public TunableBuilder interval(int interval) {
    this.interval = interval;
    return this;
  }

  public TunableBuilder count(int count) {
    this.count = count;
    return this;
  }

  public TunableBuilder slotwidth(int slotwidth) {
    this.slotwidth = slotwidth;
    return this;
  }

  public TunableBuilder bandwidth(int bandwidth) {
    this.bandwidth = bandwidth;
    return this;
  }

  public TunableBuilder currentChannel(double currentChannel) {
    this.currentChannel = currentChannel;
    return this;
  }

  public TunableBuilder constrainingRangeFirst(int constrainingRangeFirst) {
    this.constrainingRangeFirst = constrainingRangeFirst;
    return this;
  }

  public TunableBuilder constrainingRangeLast(int constrainingRangeLast) {
    this.constrainingRangeLast = constrainingRangeLast;
    return this;
  }

  Tunable build() {
    Tunable tunableObject = new Tunable(name);
    tunableObject.setBase(base);
    tunableObject.setInterval(interval);
    tunableObject.setCount(count);
    tunableObject.setSlotwidth(slotwidth);
    tunableObject.setBandwidth(bandwidth);
    tunableObject.setCurrentChannel(currentChannel);
    tunableObject.setConstrainingRangeFirst(constrainingRangeFirst);
    tunableObject.setConstrainingRangeLast(constrainingRangeLast);
    return tunableObject;
  }
}
