/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.crm.model.wdm;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

public final class SelectorBuilder {

  private final List<Consumer<ConstrainingEntryBuilder>> builders = new ArrayList<>();
  private String selectedInterfaceId;
  private int resourceGroupId;


  public SelectorBuilder selectedInterfaceId(String selectedInterfaceId) {
    this.selectedInterfaceId = selectedInterfaceId;
    return this;
  }
  public SelectorBuilder resourceGroupId(int resourceGroupId) {
    this.resourceGroupId = resourceGroupId;
    return this;
  }

  public SelectorBuilder newConstrainingEntry(Consumer<ConstrainingEntryBuilder> builder) {
    builders.add(builder);
    return this;
  }

  Selector build() {
    Selector selector = new Selector();
    selector.setSelectedInterfaceId(selectedInterfaceId);
    selector.setResourceGroupId(resourceGroupId);
    for (Consumer<ConstrainingEntryBuilder> builder : builders) {
      ConstrainingEntryBuilder constrainingEntryBuilder = new ConstrainingEntryBuilder();
      builder.accept(constrainingEntryBuilder);
      selector.getConstrainingEntryList()
        .add(constrainingEntryBuilder.build());
    }
    return selector;
  }
}
