/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.crm.model.wdm;

@SuppressWarnings({"unused", "UnusedReturnValue"})
public final class NonTunableBuilder {

  private String name;
  private ChannelType channelType;
  private double channel;
  private int slotwidth;
  private int bandwidth;
  private int slotWidthConstraints;

  public NonTunableBuilder name(String name) {
    this.name = name;
    return this;
  }

  public NonTunableBuilder channel(double channel) {
    this.channel = channel;
    return this;
  }

  public NonTunableBuilder slotwidth(int slotwidth) {
    this.slotwidth = slotwidth;
    return this;
  }

  public NonTunableBuilder bandwidth(int bandwidth) {
    this.bandwidth = bandwidth;
    return this;
  }

  public NonTunableBuilder channelType(ChannelType channelType) {
    this.channelType = channelType;
    return this;
  }

  public NonTunableBuilder slotWidthConstraints(int slotWidthConstraints) {
    this.slotWidthConstraints = slotWidthConstraints;
    return this;
  }

  NonTunable build() {
    NonTunable nonTunableObject = new NonTunable(name);
    nonTunableObject.setSlotwidth(slotwidth);
    nonTunableObject.setBandwidth(bandwidth);
    nonTunableObject.setChannelType(channelType);
    nonTunableObject.setChannel(channel);
    nonTunableObject.setSlotWidthConstraints(slotWidthConstraints);
    return nonTunableObject;
  }
}
