/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.wdm;

import com.adva.nlms.mediation.topology.NEDataProvider;
import ni.proto.external.common.signal_description.LspSwitchingType;
import ni.proto.inet.InterfaceId;
import ni.proto.mla.ChannelOuterClass;
import ni.proto.mla.FrequencyOuterClass;
import ni.proto.mla.FrequencyRangeOuterClass;
import ni.proto.mla.LinkTerminationPointOuterClass;
import ni.proto.mla.PortIdOuterClass;
import ni.proto.mla.SlotWidth;
import ni.proto.mla.SlotWidthConstraintsOuterClass;
import ni.proto.mla.SpectrumOuterClass;
import ni.proto.mla.TunnelTerminationPointOuterClass;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;

public class CrmWdmNiTranslatorTest {

  private static final String TTP_NAME = "Port-5/1/n2";
  private static final String PORT_3_19_N = "Port-3/19/n";
  private static final int RESOURCE_GROUP_ID1 = 3;
  private static final int RESOURCE_GROUP_ID2 = 8;

  private final NEDataProvider neDataProvider = mock();
  private final CrmWdmModelDAO crmWdmModelDAO = mock();

  CrmWdmNiTranslatorImpl sut = new CrmWdmNiTranslatorImpl(neDataProvider, crmWdmModelDAO);

  @Test
  void testTranslateTtp_tunable() {
    // given
    TtpInfo ttpInfo = TtpInfoBuilder.builder()
      .name(TTP_NAME)
      .tunable(true)
      .newSelector(s -> s.selectedInterfaceId(PORT_3_19_N)
        .resourceGroupId(RESOURCE_GROUP_ID1)
        .newConstrainingEntry(c -> c.interfaceId("Port-5/2/n1")
          .tunableResource(t -> t.name("Port-5/1/n1")
            .base(191275000)
            .interval(6250)
            .count(776)
            .constrainingRangeFirst(192000000)
            .constrainingRangeLast(196000000))))
      .build();
    // when
    var tunnelTerminationPointProto = sut.translateTtpData(ttpInfo);

    // then
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::getInterfaceId)
      .extracting(InterfaceId::getName)
      .as("Tunnel termination point name")
      .isEqualTo(TTP_NAME);
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::hasEdgeBindingConstraints)
      .as("Tunnel termination point has edge binding constraints")
      .isEqualTo(true);
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::getEdgeBindingConstraints)
      .extracting(TunnelTerminationPointOuterClass.EdgeBindingConstraints::getSelectorCount)
      .as("Tunnel termination point selectors count")
      .isEqualTo(ttpInfo.getEdgeBindingConstraints().getSelectors().size());
    var selectorProto = tunnelTerminationPointProto.getEdgeBindingConstraints()
      .getSelector(0);

    assertThat(selectorProto).extracting(TunnelTerminationPointOuterClass.Selector::getSelectedInterfaceId)
      .extracting(InterfaceId::getName)
      .as("Tunnel termination point selected interfaceId")
      .isEqualTo(PORT_3_19_N);
    assertThat(selectorProto).extracting(TunnelTerminationPointOuterClass.Selector::getResourceGroupId)
      .as("Tunnel termination point resource group id")
      .isEqualTo(RESOURCE_GROUP_ID1);
    assertThat(selectorProto).extracting(TunnelTerminationPointOuterClass.Selector::getConstrainingEntryCount)
      .as("Tunnel termination point selector's constraining entry count")
      .isEqualTo(ttpInfo.getEdgeBindingConstraints().getSelectors().get(0).getConstrainingEntryList().size());

    var constrainingEntryProto = selectorProto.getConstrainingEntryList()
      .get(0);
    checkTunableCEntry(constrainingEntryProto, "Port-5/2/n1", "Port-5/1/n1", 191275.0, 6.25, 776, 192000000, 196000000, null, null, null);
  }


  @Test
  void testTranslateTtp_with_single_selector_with_multiple_tunables() {
    // given
    TtpInfo ttpInfo = TtpInfoBuilder.builder()
      .name(TTP_NAME)
      .tunable(true)
      .newSelector(s -> s.selectedInterfaceId(PORT_3_19_N)
        .resourceGroupId(RESOURCE_GROUP_ID1)
        .newConstrainingEntry(c -> c.interfaceId("PTP-3/19/n")
          .tunableResource(t -> t.name("Port-5/1/n1")
            .base(191275000)
            .interval(6250)
            .count(776)
            .constrainingRangeFirst(192000000)
            .constrainingRangeLast(196000000)))
        .newConstrainingEntry(e -> e.interfaceId("PTP-3/20/n")
          .tunableResource(t -> t.name("Port-5/2/n1")
            .currentChannel(37500.0)
            .bandwidth(190000)
            .slotwidth(50)))
        .newConstrainingEntry(e -> e.interfaceId("PTP-3/21/n")
          .tunableResource(t -> t.name("Port-5/3/n1")
            .currentChannel(37500.0)
            .bandwidth(190000)))
      )
      .build();
    // when
    var tunnelTerminationPointProto = sut.translateTtpData(ttpInfo);

    // then
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::getInterfaceId)
      .extracting(InterfaceId::getName)
      .as("Tunnel termination point name")
      .isEqualTo(TTP_NAME);
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::hasEdgeBindingConstraints)
      .as("Tunnel termination point has edge binding constraints")
      .isEqualTo(true);
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::getEdgeBindingConstraints)
      .extracting(TunnelTerminationPointOuterClass.EdgeBindingConstraints::getSelectorCount)
      .as("Tunnel termination point selectors count")
      .isEqualTo(ttpInfo.getEdgeBindingConstraints().getSelectors().size());
    var selectorProto = tunnelTerminationPointProto.getEdgeBindingConstraints()
      .getSelector(0);

    assertThat(selectorProto).extracting(TunnelTerminationPointOuterClass.Selector::getSelectedInterfaceId)
      .extracting(InterfaceId::getName)
      .as("Tunnel termination point selected interfaceId")
      .isEqualTo(PORT_3_19_N);
    assertThat(selectorProto).extracting(TunnelTerminationPointOuterClass.Selector::getResourceGroupId)
      .as("Tunnel termination point resource group id")
      .isEqualTo(RESOURCE_GROUP_ID1);
    assertThat(selectorProto).extracting(TunnelTerminationPointOuterClass.Selector::getConstrainingEntryCount)
      .as("Tunnel termination point selector's constraining entry count")
      .isEqualTo(ttpInfo.getEdgeBindingConstraints().getSelectors().get(0).getConstrainingEntryList().size());

    var cEntryIterator = selectorProto.getConstrainingEntryList().iterator();
    checkTunableCEntry(cEntryIterator.next(), "PTP-3/19/n", "Port-5/1/n1", 191275.0, 6.25, 776, 192000000, 196000000, null, null, null);
    checkTunableCEntry(cEntryIterator.next(), "PTP-3/20/n", "Port-5/2/n1", null, null, null, null, null, 37.500, 190000, 50);
    checkTunableCEntry(cEntryIterator.next(), "PTP-3/21/n", "Port-5/3/n1", null, null, null, null, null, 37.500, 0, 0);
  }

  @Test
  void testTranslateTtp_with_multiple_selectors__multiple_tunables() {
    // given
    TtpInfo ttpInfo = TtpInfoBuilder.builder()
      .name(TTP_NAME)
      .tunable(true)
      .newSelector(s -> s.selectedInterfaceId("Port-3/20/n")
        .resourceGroupId(RESOURCE_GROUP_ID1)
        .newConstrainingEntry(c -> c.interfaceId("PTP-3/19/n")
          .tunableResource(t -> t.name("Port-5/1/n1")
            .base(191275000)
            .interval(6250)
            .count(776)
            .constrainingRangeFirst(192000000)
            .constrainingRangeLast(196000000)))
        .newConstrainingEntry(e -> e.interfaceId("PTP-3/20/n")
          .tunableResource(t -> t.name("Port-5/2/n1")
            .currentChannel(37500.0)
            .bandwidth(190000)
            .slotwidth(50)))
      ).newSelector(s -> s.selectedInterfaceId("Port-1/2/n")
        .resourceGroupId(RESOURCE_GROUP_ID2)
        .newConstrainingEntry(c -> c.interfaceId("PTP-1/1/n")
          .tunableResource(t -> t.name("Port-2/6/n1")
            .base(195300000)
            .interval(12250)
            .count(829)
            .constrainingRangeFirst(198000000)
            .constrainingRangeLast(199500000)))
        .newConstrainingEntry(e -> e.interfaceId("PTP-1/2/n")
          .tunableResource(t -> t.name("Port-2/7/n1")
            .currentChannel(37500.0)
            .bandwidth(190000)
            .slotwidth(50)))
      )
      .build();
    // when
    var tunnelTerminationPointProto = sut.translateTtpData(ttpInfo);

    // then
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::getInterfaceId)
      .extracting(InterfaceId::getName)
      .as("Tunnel termination point name")
      .isEqualTo(TTP_NAME);
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::hasEdgeBindingConstraints)
      .as("Tunnel termination point has edge binding constraints")
      .isEqualTo(true);
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::getEdgeBindingConstraints)
      .extracting(TunnelTerminationPointOuterClass.EdgeBindingConstraints::getSelectorCount)
      .as("Tunnel termination point selectors count")
      .isEqualTo(ttpInfo.getEdgeBindingConstraints().getSelectors().size());
    var selectorProtoIterator = tunnelTerminationPointProto.getEdgeBindingConstraints().getSelectorList().iterator();

    // ------------------------------------------------- selector #1 -------------------------------------------------
    var firstSelector = selectorProtoIterator.next();
    assertThat(firstSelector).extracting(TunnelTerminationPointOuterClass.Selector::getSelectedInterfaceId)
      .extracting(InterfaceId::getName)
      .as("Tunnel termination point selected interfaceId")
      .isEqualTo("Port-3/20/n");
    assertThat(firstSelector).extracting(TunnelTerminationPointOuterClass.Selector::getResourceGroupId)
      .as("Tunnel termination point resource group id")
      .isEqualTo(RESOURCE_GROUP_ID1);
    assertThat(firstSelector).extracting(TunnelTerminationPointOuterClass.Selector::getConstrainingEntryCount)
      .as("Tunnel termination point selector's constraining entry count")
      .isEqualTo(ttpInfo.getEdgeBindingConstraints().getSelectors().get(0).getConstrainingEntryList().size());

    var firstSelectorCEntryIterator = firstSelector.getConstrainingEntryList().iterator();
    checkTunableCEntry(firstSelectorCEntryIterator.next(), "PTP-3/19/n", "Port-5/1/n1", 191275.0, 6.25, 776, 192000000, 196000000, null, null, null);
    checkTunableCEntry(firstSelectorCEntryIterator.next(), "PTP-3/20/n", "Port-5/2/n1", null, null, null, null, null, 37.500, 190000, 50);

    // ------------------------------------------------- selector #2 -------------------------------------------------
    var secondSelector = selectorProtoIterator.next();
    assertThat(secondSelector).extracting(TunnelTerminationPointOuterClass.Selector::getSelectedInterfaceId)
      .extracting(InterfaceId::getName)
      .as("Tunnel termination point selected interfaceId")
      .isEqualTo("Port-1/2/n");
    assertThat(secondSelector).extracting(TunnelTerminationPointOuterClass.Selector::getResourceGroupId)
      .as("Tunnel termination point resource group id")
      .isEqualTo(RESOURCE_GROUP_ID2);
    assertThat(secondSelector).extracting(TunnelTerminationPointOuterClass.Selector::getConstrainingEntryCount)
      .as("Tunnel termination point selector's constraining entry count")
      .isEqualTo(ttpInfo.getEdgeBindingConstraints().getSelectors().get(1).getConstrainingEntryList().size());

    var secondSelectorCEntryIterator = secondSelector.getConstrainingEntryList().iterator();

    checkTunableCEntry(secondSelectorCEntryIterator.next(), "PTP-1/1/n", "Port-2/6/n1", 195300.0, 12.25, 829, 198000000, 199500000, null, null, null);
    checkTunableCEntry(secondSelectorCEntryIterator.next(), "PTP-1/2/n", "Port-2/7/n1", null, null, null, null, null, 37.500, 190000, 50);
  }

  @Test
  void testTranslateTtp_nonTunable() {
    // given
    TtpInfo ttpInfo = TtpInfoBuilder.builder()
      .name(TTP_NAME)
      .tunable(false)
      .newSelector(s -> s.selectedInterfaceId(PORT_3_19_N)
        .resourceGroupId(RESOURCE_GROUP_ID1)
        .newConstrainingEntry(e -> e.interfaceId("PTP-3/19/n")
          .nonTunableResource(n -> n.name("Port-5/1/n2")
            .bandwidth(37500)
            .channelType(ChannelType.DWDM)
            .channel(196000.0)
            .slotwidth(37500)
            .slotWidthConstraints(75000))))
      .build();

    // when
    var tunnelTerminationPointProto = sut.translateTtpData(ttpInfo);

    // then
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::getInterfaceId)
      .extracting(InterfaceId::getName)
      .isEqualTo(TTP_NAME);
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::hasEdgeBindingConstraints)
      .isEqualTo(true);
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::getEdgeBindingConstraints)
      .extracting(TunnelTerminationPointOuterClass.EdgeBindingConstraints::getSelectorList)
      .asList()
      .hasSize(1);
    var selectorProto = tunnelTerminationPointProto.getEdgeBindingConstraints().getSelector(0);
    assertThat(selectorProto).extracting(s -> s.getSelectedInterfaceId().getName(),
        TunnelTerminationPointOuterClass.Selector::getResourceGroupId,
        TunnelTerminationPointOuterClass.Selector::getConstrainingEntryCount)
      .containsExactly(PORT_3_19_N, RESOURCE_GROUP_ID1, 1);
    assertThat(selectorProto).extracting(TunnelTerminationPointOuterClass.Selector::getConstrainingEntryCount)
      .isEqualTo(1);
    var constrainingEntryProto = selectorProto.getConstrainingEntryList().get(0);
    checkNonTunableCEntry(constrainingEntryProto, "PTP-3/19/n", "Port-5/1/n2", 37500, 37500, 196.0, 75000);
  }


  @Test
  void testTranslateTtp_with_single_selector_with_multiple_nonTunables() {
    // given
    TtpInfo ttpInfo = TtpInfoBuilder.builder()
      .name(TTP_NAME)
      .tunable(false)
      .newSelector(s -> s.selectedInterfaceId(PORT_3_19_N)
        .resourceGroupId(RESOURCE_GROUP_ID1)
        .newConstrainingEntry(e -> e.interfaceId("PTP-3/19/n")
          .nonTunableResource(n -> n.name("Port-5/1/n1")
            .bandwidth(37500)
            .slotwidth(37500)
            .channelType(ChannelType.DWDM)
            .channel(196000.0)
            .slotWidthConstraints(75000)))
        .newConstrainingEntry(e -> e.interfaceId("PTP-3/20/n")
          .nonTunableResource(n -> n.name("Port-5/1/n2")
            .bandwidth(40500)
            .slotwidth(39500)
            .channelType(ChannelType.DWDM)
            .channel(197000.0)
            .slotWidthConstraints(60000))))
      .build();

    // when
    var tunnelTerminationPointProto = sut.translateTtpData(ttpInfo);

    // then
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::getInterfaceId)
      .extracting(InterfaceId::getName)
      .isEqualTo(TTP_NAME);
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::hasEdgeBindingConstraints)
      .isEqualTo(true);
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::getEdgeBindingConstraints)
      .extracting(TunnelTerminationPointOuterClass.EdgeBindingConstraints::getSelectorList)
      .asList()
      .hasSize(1);
    var selectorProto = tunnelTerminationPointProto.getEdgeBindingConstraints().getSelector(0);
    assertThat(selectorProto).extracting(s -> s.getSelectedInterfaceId().getName(),
        TunnelTerminationPointOuterClass.Selector::getResourceGroupId,
        TunnelTerminationPointOuterClass.Selector::getConstrainingEntryCount)
      .containsExactly(PORT_3_19_N, RESOURCE_GROUP_ID1, 2);
    var cEntryIterator = selectorProto.getConstrainingEntryList().iterator();
    var firstCEntry = cEntryIterator.next();
    var secondCEntry = cEntryIterator.next();
    checkNonTunableCEntry(firstCEntry, "PTP-3/19/n", "Port-5/1/n1", 37500, 37500, 196.0, 75000);
    checkNonTunableCEntry(secondCEntry, "PTP-3/20/n", "Port-5/1/n2", 40500, 39500, 197.0, 60000);
  }

  @Test
  void testTranslateTtp_with_multiple_selectors_and_multiple_nonTunables() {
    // given
    TtpInfo ttpInfo = TtpInfoBuilder.builder()
      .name(TTP_NAME)
      .tunable(false)
      .newSelector(s -> s.selectedInterfaceId(PORT_3_19_N)
        .resourceGroupId(RESOURCE_GROUP_ID1)
        .newConstrainingEntry(e -> e.interfaceId("PTP-3/19/n")
          .nonTunableResource(n -> n.name("Port-5/1/n1")
            .bandwidth(37500)
            .slotwidth(37500)
            .channelType(ChannelType.DWDM)
            .channel(196000.0)
            .slotWidthConstraints(75000)))
        .newConstrainingEntry(e -> e.interfaceId("PTP-3/20/n")
          .nonTunableResource(n -> n.name("Port-5/1/n2")
            .bandwidth(40500)
            .slotwidth(39500)
            .channelType(ChannelType.DWDM)
            .channel(197000.0)
            .slotWidthConstraints(60000))))
      .newSelector(s -> s.selectedInterfaceId("Port-4/2/n")
        .resourceGroupId(RESOURCE_GROUP_ID2)
        .newConstrainingEntry(e -> e.interfaceId("PTP-4/1/n")
          .nonTunableResource(n -> n.name("Port-4/2/n1")
            .bandwidth(37000)
            .slotwidth(500)
            .channelType(ChannelType.DWDM)
            .channel(198000.0)
            .slotWidthConstraints(1000)))
      )
      .build();

    // when
    var tunnelTerminationPointProto = sut.translateTtpData(ttpInfo);

    // then
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::getInterfaceId)
      .extracting(InterfaceId::getName)
      .as("Tunnel Termination Point name")
      .isEqualTo(TTP_NAME);
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::hasEdgeBindingConstraints)
      .isEqualTo(true);
    assertThat(tunnelTerminationPointProto).extracting(TunnelTerminationPointOuterClass.TunnelTerminationPoint::getEdgeBindingConstraints)
      .extracting(TunnelTerminationPointOuterClass.EdgeBindingConstraints::getSelectorCount)
      .as("selectors count")
      .isEqualTo(ttpInfo.getEdgeBindingConstraints()
        .getSelectors()
        .size());
    var selectorsIterator = tunnelTerminationPointProto.getEdgeBindingConstraints().getSelectorList().iterator();

    // --------------------------------------------------------------- selector #1 ---------------------------------------------------------------
    var firstSelector = selectorsIterator.next();
    assertThat(firstSelector).extracting(s -> s.getSelectedInterfaceId().getName(),
        TunnelTerminationPointOuterClass.Selector::getResourceGroupId,
        TunnelTerminationPointOuterClass.Selector::getConstrainingEntryCount)
      .containsExactly(PORT_3_19_N, RESOURCE_GROUP_ID1, ttpInfo.getEdgeBindingConstraints().getSelectors().get(0).getConstrainingEntryList().size());
    var firstSelectorCEntryIterator = firstSelector.getConstrainingEntryList().iterator();
    var firstSelectorsFirstCEntry = firstSelectorCEntryIterator.next();
    var firstSelectorsSecondCEntry = firstSelectorCEntryIterator.next();
    checkNonTunableCEntry(firstSelectorsFirstCEntry, "PTP-3/19/n", "Port-5/1/n1", 37500, 37500, 196.0, 75000);
    checkNonTunableCEntry(firstSelectorsSecondCEntry, "PTP-3/20/n", "Port-5/1/n2", 40500, 39500, 197.0, 60000);

    // --------------------------------------------------------------- selector #2 ---------------------------------------------------------------
    var secondSelector = selectorsIterator.next();
    assertThat(secondSelector).extracting(s -> s.getSelectedInterfaceId().getName(),
        TunnelTerminationPointOuterClass.Selector::getResourceGroupId,
        TunnelTerminationPointOuterClass.Selector::getConstrainingEntryCount)
      .containsExactly("Port-4/2/n", RESOURCE_GROUP_ID2, ttpInfo.getEdgeBindingConstraints().getSelectors().get(1).getConstrainingEntryList().size());
    var secondSelectorCEntryIterator = secondSelector.getConstrainingEntryList().iterator();
    var secondSelectorsFirstCEntry = secondSelectorCEntryIterator.next();
    checkNonTunableCEntry(secondSelectorsFirstCEntry, "PTP-4/1/n", "Port-4/2/n1", 37000, 500, 198.0, 1000);
  }

  @Test
  @DisplayName("""
    GIVEN: null LtpInfo
    WHEN: translateLTPData invoked
    THEN: null returned""")
  public void translateLTPDataWhenNullLtpInfo() {
    var actual = sut.translateLTPData(null, -1);
    assertThat(actual).as("LTP NI bundle representation")
      .isNull();
  }

  @Test
  @DisplayName("""
    GIVEN: LtpInfo with OSC status OSC_STATUS_UNKNOWN
    WHEN: translateLTPData invoked
    THEN: result bundle contains NI OSC_STATUS_UNKNOWN""")
  public void translateLTPDataWithOscStatus() {
    final String ltpName = "ltp-name";
    final String omsTerminationId = "oms-term";
    final SpectrumInfo.SlotWidthSupport slotWidthSupport = new SpectrumInfo.SlotWidthSupport(1, 2, 1);
    final SpectrumInfo spectrumInfo = new SpectrumInfo(SpectrumInfo.Type.TYPE_FREQUENCY, 1, 10, 1, 1, slotWidthSupport, true);
    LtpInfo ltpInfo = new LtpInfo(ltpName, omsTerminationId, spectrumInfo, 5, true, OSCSignal.UNKNOWN_SIGNAL, null);
    var actual = sut.translateLTPData(ltpInfo, 1);
    assertThat(actual).as("LTP NI bundle representation")
      .extracting(LinkTerminationPointOuterClass.LinkTerminationPoint::getLayer)
      .isEqualTo(LspSwitchingType.LSP_SWITCHING_TYPE_LSC);
    assertThat(actual).extracting(LinkTerminationPointOuterClass.LinkTerminationPoint::getInterfaceId)
      .extracting(InterfaceId::getName)
      .isEqualTo(ltpName);
    assertThat(actual).extracting(LinkTerminationPointOuterClass.LinkTerminationPoint::getOmsTerminationId)
      .isEqualTo(omsTerminationId);
    assertThat(actual).extracting(LinkTerminationPointOuterClass.LinkTerminationPoint::getStatus)
      .hasFieldOrPropertyWithValue("dataPlaneStatus", LinkTerminationPointOuterClass.DataPlaneStatus.DATAPLANE_STATUS_UNKNOWN)
      .extracting(LinkTerminationPointOuterClass.LinkTerminationPoint.Status::getOscStatus)
      .isEqualTo(LinkTerminationPointOuterClass.OscStatus.OSC_STATUS_UNKNOWN);
  }

  @Test
  @DisplayName("""
    WHEN: for LtpInfo with frequency Spectrum translateLTPData invoked
    THEN: result bundle proper spectrum translation
    """)
  public void translateLTPData() {
    final String ltpName = "ltp-name";
    final SpectrumInfo.SlotWidthSupport slotWidthSupport = new SpectrumInfo.SlotWidthSupport(2, 20, 3);
    final SpectrumInfo spectrumInfo = new SpectrumInfo(SpectrumInfo.Type.TYPE_FREQUENCY, 100, 200, 4, 5, slotWidthSupport, false);
    LtpInfo ltpInfo = new LtpInfo(ltpName, spectrumInfo, 5, true);
    var actual = sut.translateLTPData(ltpInfo, 1);
    assertThat(actual).as("LTP NI bundle representation")
      .extracting(LinkTerminationPointOuterClass.LinkTerminationPoint::getLayer)
      .isEqualTo(LspSwitchingType.LSP_SWITCHING_TYPE_LSC);
    assertThat(actual).extracting(LinkTerminationPointOuterClass.LinkTerminationPoint::getInterfaceId)
      .extracting(InterfaceId::getName)
      .isEqualTo(ltpName);
    assertThat(actual).extracting(LinkTerminationPointOuterClass.LinkTerminationPoint::getResources)
      .extracting(LinkTerminationPointOuterClass.LinkTerminationPoint.Resources::getSpectrumList)
      .asList()
      .isNotEmpty()
      .first(InstanceOfAssertFactories.type(SpectrumOuterClass.Spectrum.class))
      .satisfies(this::validateSpectrum);
  }

  private void validateSpectrum(SpectrumOuterClass.Spectrum spectrum) {
    assertThat(spectrum.getFirst()).isEqualTo(100);
    assertThat(spectrum.getFirstCF()).isEqualTo(101);
    assertThat(spectrum.getLast()).isEqualTo(200);
    assertThat(spectrum.getLastCF()).isEqualTo(199);
    assertThat(spectrum.getStepCF()).isEqualTo(5);
    assertThat(spectrum.getSpacing()).isEqualTo(4);
    assertThat(spectrum.hasSlotWidthSupport()).isTrue();
    assertThat(spectrum.getSlotWidthSupport().hasRange()).isTrue();
    final var range = spectrum.getSlotWidthSupport().getRange();
    assertThat(range.getMin()).isEqualTo(2);
    assertThat(range.getMax()).isEqualTo(20);
    assertThat(range.getStep()).isEqualTo(3);
  }

  private void checkTunableCEntry(TunnelTerminationPointOuterClass.ConstrainingEntry firstCEntry,
                                  String interfaceId,
                                  String tunablePortId,
                                  Double base,
                                  Double interval,
                                  Integer count,
                                  Integer constrainingRangeFirst,
                                  Integer constrainingRangeLast,
                                  Double currentChannel,
                                  Integer bandwidth,
                                  Integer slotwidth) {
    assertThat(firstCEntry).extracting(TunnelTerminationPointOuterClass.ConstrainingEntry::hasTunable)
      .isEqualTo(true);
    assertThat(firstCEntry).extracting(TunnelTerminationPointOuterClass.ConstrainingEntry::getInterfaceId)
      .extracting(InterfaceId::getName)
      .isEqualTo(interfaceId);
    var firstTunableProto = firstCEntry.getTunable();
    assertThat(firstTunableProto).isNotNull()
      .extracting(TunnelTerminationPointOuterClass.ConstrainingEntry.Tunable::getPortId)
      .extracting(PortIdOuterClass.PortId::getName)
      .isEqualTo(tunablePortId);
    var rangeProto = firstTunableProto.getRange();
    if (base != null) {
      assertThat(rangeProto).extracting(TunnelTerminationPointOuterClass.ConstrainingEntry.Tunable.Range::getBase)
        .isEqualTo(base);
    }
    if (interval != null) {
      assertThat(rangeProto).extracting(TunnelTerminationPointOuterClass.ConstrainingEntry.Tunable.Range::getInterval)
        .isEqualTo(interval);
    }
    if (count != null) {
      assertThat(rangeProto).extracting(TunnelTerminationPointOuterClass.ConstrainingEntry.Tunable.Range::getCount)
        .isEqualTo(count);
    }
    if (constrainingRangeFirst != null) {
      assertThat(firstTunableProto).extracting(TunnelTerminationPointOuterClass.ConstrainingEntry.Tunable::getSlotWidthConstraints)
        .extracting(SlotWidthConstraintsOuterClass.SlotWidthConstraints::getConstrainingRange)
        .extracting(FrequencyRangeOuterClass.FrequencyRange::getFirst)
        .extracting(FrequencyOuterClass.Frequency::getMhz)
        .extracting(FrequencyOuterClass.Frequency.MHz::getValue)
        .isEqualTo(constrainingRangeFirst);
    }
    if (constrainingRangeLast != null) {
      assertThat(firstTunableProto).extracting(TunnelTerminationPointOuterClass.ConstrainingEntry.Tunable::getSlotWidthConstraints)
        .extracting(SlotWidthConstraintsOuterClass.SlotWidthConstraints::getConstrainingRange)
        .extracting(FrequencyRangeOuterClass.FrequencyRange::getLast)
        .extracting(FrequencyOuterClass.Frequency::getMhz)
        .extracting(FrequencyOuterClass.Frequency.MHz::getValue)
        .isEqualTo(constrainingRangeLast);
    }
    if (currentChannel != null) {
      assertThat(firstTunableProto).extracting(TunnelTerminationPointOuterClass.ConstrainingEntry.Tunable::getCurrentChannel)
        .extracting(ChannelOuterClass.Channel::getDwdm)
        .extracting(ChannelOuterClass.Channel.Dwdm::getChannel)
        .isEqualTo(currentChannel);
    }
    if (bandwidth != null) {
      assertThat(firstTunableProto).extracting(TunnelTerminationPointOuterClass.ConstrainingEntry.Tunable::getSlotWidthParams)
        .extracting(SlotWidth.SlotWidthParams::getBandwidth)
        .extracting(FrequencyOuterClass.Frequency::getMhz)
        .extracting(FrequencyOuterClass.Frequency.MHz::getValue)
        .isEqualTo(bandwidth);
    }
    if (slotwidth != null) {
      assertThat(firstTunableProto).extracting(TunnelTerminationPointOuterClass.ConstrainingEntry.Tunable::getSlotWidthParams)
        .extracting(SlotWidth.SlotWidthParams::getSlotWidth)
        .extracting(FrequencyOuterClass.Frequency::getMhz)
        .extracting(FrequencyOuterClass.Frequency.MHz::getValue)
        .isEqualTo(slotwidth);
    }
  }

  private void checkNonTunableCEntry(TunnelTerminationPointOuterClass.ConstrainingEntry constrainingEntryProto,
                                     String entryInterfaceId,
                                     String portId,
                                     Integer bandwidth,
                                     Integer slotwidth,
                                     Double channel,
                                     Integer slotwidthConstraints) {
    assertThat(constrainingEntryProto).extracting(TunnelTerminationPointOuterClass.ConstrainingEntry::getInterfaceId)
      .extracting(InterfaceId::getName)
      .isEqualTo(entryInterfaceId);
    assertThat(constrainingEntryProto).extracting(TunnelTerminationPointOuterClass.ConstrainingEntry::hasNonTunable)
      .isEqualTo(true);
    var nonTunableProto = constrainingEntryProto.getNonTunable();
    assertThat(nonTunableProto).extracting(TunnelTerminationPointOuterClass.ConstrainingEntry.NonTunable::getPortId)
      .extracting(PortIdOuterClass.PortId::getName)
      .isEqualTo(portId);
    if (bandwidth != null) {
      assertThat(nonTunableProto).extracting(TunnelTerminationPointOuterClass.ConstrainingEntry.NonTunable::getSlotWidthParams)
        .extracting(SlotWidth.SlotWidthParams::getBandwidth)
        .extracting(FrequencyOuterClass.Frequency::getMhz)
        .extracting(FrequencyOuterClass.Frequency.MHz::getValue)
        .isEqualTo(bandwidth);
    }
    if (slotwidth != null) {
      assertThat(nonTunableProto).extracting(TunnelTerminationPointOuterClass.ConstrainingEntry.NonTunable::getSlotWidthParams)
        .extracting(SlotWidth.SlotWidthParams::getSlotWidth)
        .extracting(FrequencyOuterClass.Frequency::getMhz)
        .extracting(FrequencyOuterClass.Frequency.MHz::getValue)
        .isEqualTo(slotwidth);
    }
    if (channel != null) {
      assertThat(nonTunableProto).extracting(TunnelTerminationPointOuterClass.ConstrainingEntry.NonTunable::getChannel)
        .extracting(ChannelOuterClass.Channel::getDwdm)
        .extracting(ChannelOuterClass.Channel.Dwdm::getChannel)
        .isEqualTo(channel);
    }
    if (slotwidthConstraints != null) {
      assertThat(nonTunableProto).extracting(TunnelTerminationPointOuterClass.ConstrainingEntry.NonTunable::getSlotWidthConstraints)
        .extracting(SlotWidthConstraintsOuterClass.SlotWidthConstraints::getMaxSlotWidth)
        .extracting(FrequencyOuterClass.Frequency::getMhz)
        .extracting(FrequencyOuterClass.Frequency.MHz::getValue)
        .isEqualTo(slotwidthConstraints);
    }
  }
}
