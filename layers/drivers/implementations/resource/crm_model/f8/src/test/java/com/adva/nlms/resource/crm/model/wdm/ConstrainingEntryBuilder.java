/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: michalo
 */

package com.adva.nlms.resource.crm.model.wdm;

import java.util.function.Consumer;

@SuppressWarnings("UnusedReturnValue")
public final class ConstrainingEntryBuilder {

  private String interfaceId;
  private Consumer<TunableBuilder> tunableBuilder;
  private Consumer<NonTunableBuilder> nonTunableBuilderFunction;


  public ConstrainingEntryBuilder interfaceId(String interfaceId) {
    this.interfaceId = interfaceId;
    return this;
  }

  public ConstrainingEntryBuilder tunableResource(Consumer<TunableBuilder> builderFunction) {
    this.tunableBuilder = builderFunction;
    return this;
  }

  public ConstrainingEntryBuilder nonTunableResource(Consumer<NonTunableBuilder> builderFunction) {
    this.nonTunableBuilderFunction = builderFunction;
    return this;
  }

  ConstrainingEntry build() {
    ConstrainingEntry constrainingEntry = new ConstrainingEntry();
    constrainingEntry.setInterfaceId(interfaceId);
    if (tunableBuilder != null) {
      TunableBuilder builder = new TunableBuilder();
      tunableBuilder.accept(builder);
      constrainingEntry.setConstrainedResource(builder.build());
    } else if (nonTunableBuilderFunction != null) {
      NonTunableBuilder builder = new NonTunableBuilder();
      nonTunableBuilderFunction.accept(builder);
      constrainingEntry.setConstrainedResource(builder.build());
    }
    return constrainingEntry;
  }
}
