/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 */

package com.adva.nlms.resource.crm.model.eth;

import com.adva.nlms.mediation.topology.NEDataProvider;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import ni.proto.external.common.signal_description.LspSwitchingType;
import ni.proto.external.common.signal_description.SignalEthernet;
import ni.proto.mla.TunnelTerminationPointOuterClass;
import ni.proto.mla.eth.Eth;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.List;
import java.util.stream.IntStream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

class CrmEthNiTranslatorTest {

  private static final int NETWORK_ELEMENT_ID = 3;

  private final NEDataProvider neDataProvider = Mockito.mock(NEDataProvider.class);
  private final CrmEthModelDAO crmEthModelDAO = Mockito.mock(CrmEthModelDAO.class);

  CrmEthNiTranslatorImpl sut = new CrmEthNiTranslatorImpl(neDataProvider, crmEthModelDAO);

  @Test
  @DisplayName("""
    GIVEN: LtpInfo with idle resources with label Whole and empty busy resources
    WHEN: translateLTPData invoked
    THEN: Proto LTP message
    """)
  void testTranslateLtpDataWholeIdleResourcesEmptyBusyResources() {
    // given
    String ltpName = "ET400OZRP-1/1/p2/et400ozrp";
    EthPool ethPool = new EthPool(LayerQualifier.ETH_400G, new Whole(), null);
    LtpInfo ltpInfo = new LtpInfo(ltpName, List.of(ethPool));

    // when
    var ltpProto = sut.translateLtpData(ltpInfo, NETWORK_ELEMENT_ID);

    // then
    assertEquals(ltpName, ltpProto.getInterfaceId().getName());
    assertEquals(LspSwitchingType.LSP_SWITCHING_TYPE_ETHERNET, ltpProto.getLayer());
    assertEquals(1, ltpProto.getResources().getEthResources().getEthPoolsCount());
    Eth.EthResources.EthPool ethPoolProto = ltpProto.getResources().getEthResources().getEthPools(0);
    assertEquals(SignalEthernet.SignalType.ETH_SIGNAL_TYPE_400GBE, ethPoolProto.getSignalType());
    assertTrue(ethPoolProto.getIdle().hasWhole());
    assertEquals(Eth.ResourceDescriptor.TypeCase.TYPE_NOT_SET, ethPoolProto.getBusy().getTypeCase());
  }

  @Test
  @DisplayName("""
    GIVEN: LtpInfo with empty idle resources and busy resources with label Whole
    WHEN: translateLTPData invoked
    THEN: Proto LTP message
    """)
  void testTranslateLtpDataEmptyIdleResourcesWholeBusyResources() {
    // given
    String ltpName = "ET400OZRP-1/1/p2/et400ozrp";
    EthPool ethPool = new EthPool(LayerQualifier.ETH_400G, null, new Whole());
    LtpInfo ltpInfo = new LtpInfo(ltpName, List.of(ethPool));

    // when
    var ltpProto = sut.translateLtpData(ltpInfo, NETWORK_ELEMENT_ID);

    // then
    assertEquals(ltpName, ltpProto.getInterfaceId().getName());
    assertEquals(LspSwitchingType.LSP_SWITCHING_TYPE_ETHERNET, ltpProto.getLayer());
    assertEquals(1, ltpProto.getResources().getEthResources().getEthPoolsCount());
    Eth.EthResources.EthPool ethPoolProto = ltpProto.getResources().getEthResources().getEthPools(0);
    assertEquals(SignalEthernet.SignalType.ETH_SIGNAL_TYPE_400GBE, ethPoolProto.getSignalType());
    assertEquals(Eth.ResourceDescriptor.TypeCase.TYPE_NOT_SET, ethPoolProto.getIdle().getTypeCase());
    assertTrue(ethPoolProto.getBusy().hasWhole());
  }

  @Test
  @DisplayName("""
    GIVEN: LtpInfo with idle resources with ContainerSet label and container in busy resources
    WHEN: translateLTPData invoked
    THEN: Proto LTP message
    """)
  void testTranslateLtpDataContainerSetIdleResourcesContainerInBusyResources() {
    // given
    String ltpName = "ET400OZRP-1/1/p2/et400ozrp";
    ContainerSet idleResources = new ContainerSet();
    IntStream.range(2, 5).forEach(i -> idleResources.getContainers().add(new Container(List.of(i), List.of(i))));
    ContainerSet busyResources = new ContainerSet(List.of(new Container(List.of(1), List.of(1))));
    EthPool ethPool = new EthPool(LayerQualifier.ETH_400G, idleResources, busyResources);
    LtpInfo ltpInfo = new LtpInfo(ltpName, List.of(ethPool));

    // when
    var ltpProto = sut.translateLtpData(ltpInfo, NETWORK_ELEMENT_ID);

    // then
    assertEquals(ltpName, ltpProto.getInterfaceId().getName());
    assertEquals(LspSwitchingType.LSP_SWITCHING_TYPE_ETHERNET, ltpProto.getLayer());
    assertEquals(1, ltpProto.getResources().getEthResources().getEthPoolsCount());
    Eth.EthResources.EthPool ethPoolProto = ltpProto.getResources().getEthResources().getEthPools(0);
    assertEquals(SignalEthernet.SignalType.ETH_SIGNAL_TYPE_400GBE, ethPoolProto.getSignalType());
    assertTrue(ethPoolProto.getIdle().hasContainerSet());
    IntStream.range(0, 3).forEach(i -> {
      assertEquals(idleResources.getContainers().get(i).getTributaryIds(), ethPoolProto.getIdle().getContainerSet().getContainer(i).getIdList());
      assertEquals(idleResources.getContainers().get(i).getTributarySlots(), ethPoolProto.getIdle().getContainerSet().getContainer(i).getSlotsList());
    });
    assertTrue(ethPoolProto.getBusy().hasContainerSet());
    assertEquals(1, ethPoolProto.getBusy().getContainerSet().getContainerCount());
    assertEquals(busyResources.getContainers().get(0).getTributaryIds(), ethPoolProto.getBusy().getContainerSet().getContainer(0).getIdList());
  }

  @Test
  @DisplayName("""
    GIVEN: LtpInfo with idle resources with ContainerSet label and container with multiple ids in busy resources
    WHEN: translateLTPData invoked
    THEN: Proto LTP message
    """)
  void testTranslateLtpDataContainerSetIdleResourcesContainerWithMultipleIdsInBusyResources() {
    // given
    String ltpName = "ET400OZRP-1/1/p2/et400ozrp";
    ContainerSet idleResources = new ContainerSet();
    IntStream.range(3, 5).forEach(i -> idleResources.getContainers().add(new Container(List.of(i), List.of(i))));
    ContainerSet busyResources = new ContainerSet(List.of(new Container(List.of(1, 2), List.of(1, 2))));
    EthPool ethPool = new EthPool(LayerQualifier.ETH_400G, idleResources, busyResources);
    LtpInfo ltpInfo = new LtpInfo(ltpName, List.of(ethPool));

    // when
    var ltpProto = sut.translateLtpData(ltpInfo, NETWORK_ELEMENT_ID);

    // then
    assertEquals(ltpName, ltpProto.getInterfaceId().getName());
    assertEquals(LspSwitchingType.LSP_SWITCHING_TYPE_ETHERNET, ltpProto.getLayer());
    assertEquals(1, ltpProto.getResources().getEthResources().getEthPoolsCount());
    Eth.EthResources.EthPool ethPoolProto = ltpProto.getResources().getEthResources().getEthPools(0);
    assertEquals(SignalEthernet.SignalType.ETH_SIGNAL_TYPE_400GBE, ethPoolProto.getSignalType());
    assertTrue(ethPoolProto.getIdle().hasContainerSet());
    IntStream.range(0, 2).forEach(i -> {
      assertEquals(idleResources.getContainers().get(i).getTributaryIds(), ethPoolProto.getIdle().getContainerSet().getContainer(i).getIdList());
      assertEquals(idleResources.getContainers().get(i).getTributarySlots(), ethPoolProto.getIdle().getContainerSet().getContainer(i).getSlotsList());
    });
    assertTrue(ethPoolProto.getBusy().hasContainerSet());
    assertEquals(1, ethPoolProto.getBusy().getContainerSet().getContainerCount());
    assertEquals(busyResources.getContainers().get(0).getTributaryIds(), ethPoolProto.getBusy().getContainerSet().getContainer(0).getIdList());
  }

  @Test
  @DisplayName("""
    GIVEN: LtpInfo with idle resources with ContainerSet label and multiple containers in busy resources
    WHEN: translateLTPData invoked
    THEN: Proto LTP message
    """)
  void testTranslateLtpDataContainerSetIdleResourcesMultipleContainersInBusyResources() {
    // given
    String ltpName = "ET400OZRP-1/1/p2/et400ozrp";
    ContainerSet idleResources = new ContainerSet();
    IntStream.range(2, 4).forEach(i -> idleResources.getContainers().add(new Container(List.of(i), List.of(i))));
    Container container1 = new Container(List.of(1), List.of(1));
    Container container2 = new Container(List.of(4), List.of(4));
    ContainerSet busyResources = new ContainerSet(List.of(container1, container2));
    EthPool ethPool = new EthPool(LayerQualifier.ETH_400G, idleResources, busyResources);
    LtpInfo ltpInfo = new LtpInfo(ltpName, List.of(ethPool));

    // when
    var ltpProto = sut.translateLtpData(ltpInfo, NETWORK_ELEMENT_ID);

    // then
    assertEquals(ltpName, ltpProto.getInterfaceId().getName());
    assertEquals(LspSwitchingType.LSP_SWITCHING_TYPE_ETHERNET, ltpProto.getLayer());
    assertEquals(1, ltpProto.getResources().getEthResources().getEthPoolsCount());
    Eth.EthResources.EthPool ethPoolProto = ltpProto.getResources().getEthResources().getEthPools(0);
    assertEquals(SignalEthernet.SignalType.ETH_SIGNAL_TYPE_400GBE, ethPoolProto.getSignalType());
    assertTrue(ethPoolProto.getIdle().hasContainerSet());
    IntStream.range(0, 2).forEach(i -> {
      assertEquals(idleResources.getContainers().get(i).getTributaryIds(), ethPoolProto.getIdle().getContainerSet().getContainer(i).getIdList());
      assertEquals(idleResources.getContainers().get(i).getTributarySlots(), ethPoolProto.getIdle().getContainerSet().getContainer(i).getSlotsList());
    });
    assertTrue(ethPoolProto.getBusy().hasContainerSet());
    assertEquals(2, ethPoolProto.getBusy().getContainerSet().getContainerCount());
    Eth.ResourceDescriptor.Container protoContainer1 = ethPoolProto.getBusy().getContainerSet().getContainer(0);
    Eth.ResourceDescriptor.Container protoContainer2 = ethPoolProto.getBusy().getContainerSet().getContainer(1);
    assertTrue(container1.getTributaryIds().equals(protoContainer1.getIdList()) || container2.getTributaryIds().equals(protoContainer1.getIdList()));
    assertTrue(container1.getTributaryIds().equals(protoContainer2.getIdList()) || container2.getTributaryIds().equals(protoContainer2.getIdList()));
  }

  @Test
  @DisplayName("""
    GIVEN: null LtpInfo
    WHEN: translateLTPData invoked
    THEN: null returned
    """)
  void translateLTPDataWhenNullLtpInfo() {
    // when
    var actual = sut.translateLtpData(null, -1);

    // then
    assertThat(actual).as("LTP NI bundle representation")
      .isNull();
  }

  @Test
  @DisplayName("""
    GIVEN: LtpInfo with no name
    WHEN: translateLTPData invoked
    THEN: NullPointerException thrown
    """)
  void translateLTPDataWhenLtpInfoHasNoName() {
    // given
    EthPool ethPool = new EthPool(LayerQualifier.ETH_400G, new Whole(), null);
    LtpInfo ltpInfo = new LtpInfo(null, List.of(ethPool));

    // when - then
    assertThrows(NullPointerException.class, () -> sut.translateLtpData(ltpInfo, NETWORK_ELEMENT_ID));
  }

  @Test
  @DisplayName("""
    GIVEN: LtpInfo with empty EthPools list
    WHEN: translateLTPData invoked
    THEN: IllegalArgumentException thrown
    """)
  void translateLTPDataWhenLtpInfoHasEmptyEthPools() {
    // given
    String ltpName = "ET400OZRP-1/1/p2/et400ozrp";
    LtpInfo ltpInfo = new LtpInfo(ltpName);

    // when - then
    assertThrows(IllegalArgumentException.class, () -> sut.translateLtpData(ltpInfo, NETWORK_ELEMENT_ID));
  }

  @Test
  @DisplayName("""
    GIVEN: TtpInfo without selected LTP
    WHEN: translateTTPData invoked
    THEN: Proto TTP message
    """)
  void testTranslateTTPDataWithoutSelectedLtp() {
    // given
    String ttpName = "1/1/p3";
    String ltpName = "ET400OZRP-1/1/p2/et400ozrp";
    ConstrainingEntry constrainingEntry = new ConstrainingEntry(ltpName, null);
    EdgeBindingConstraints edgeBindingConstraints = new EdgeBindingConstraints();
    edgeBindingConstraints.setConstrainingEntryList(List.of(constrainingEntry));
    TtpInfo ttpInfo = new TtpInfo(ttpName, edgeBindingConstraints);

    // when
    var ttpProto = sut.translateTtpData(ttpInfo, NETWORK_ELEMENT_ID);

    // then
    assertEquals(ttpName, ttpProto.getInterfaceId().getName());
    assertEquals(1, ttpProto.getEdgeBindingConstraints().getSelector(0).getConstrainingEntryCount());
    TunnelTerminationPointOuterClass.ConstrainingEntry constrainingEntryProto = ttpProto.getEdgeBindingConstraints().getSelector(0).getConstrainingEntry(0);
    assertEquals(ltpName, constrainingEntryProto.getInterfaceId().getName());
    assertFalse(constrainingEntryProto.hasEth());
    assertEquals(TunnelTerminationPointOuterClass.EdgeBindingConstraints.Type.TYPE_TERMINATION, ttpProto.getEdgeBindingConstraints().getType());
  }

  @Test
  @DisplayName("""
    GIVEN: TtpInfo without selected LTP - with multiple constraints
    WHEN: translateTTPData invoked
    THEN: Proto TTP message
    """)
  void testTranslateTTPDataWithoutSelectedLtpMultipleConstraints() {
    // given
    String ttpName = "1/1/p3";
    String ltpName1 = "ET400OZRP-1/1/p2/et400ozrp";
    String ltpName2 = "ET400OZRP-1/1/p7/et400ozrp";
    ConstrainingEntry constrainingEntry1 = new ConstrainingEntry(ltpName1, null);
    ConstrainingEntry constrainingEntry2 = new ConstrainingEntry(ltpName2, null);
    EdgeBindingConstraints edgeBindingConstraints = new EdgeBindingConstraints();
    edgeBindingConstraints.setConstrainingEntryList(List.of(constrainingEntry1, constrainingEntry2));
    TtpInfo ttpInfo = new TtpInfo(ttpName, edgeBindingConstraints);

    // when
    var ttpProto = sut.translateTtpData(ttpInfo, NETWORK_ELEMENT_ID);

    // then
    assertEquals(ttpName, ttpProto.getInterfaceId().getName());
    assertEquals(2, ttpProto.getEdgeBindingConstraints().getSelector(0).getConstrainingEntryCount());
    var constrainingEntryProto1 = ttpProto.getEdgeBindingConstraints().getSelector(0).getConstrainingEntryList().stream()
      .filter(constrainingEntry -> ltpName1.equals(constrainingEntry.getInterfaceId().getName())).findFirst().orElse(null);
    assertNotNull(constrainingEntryProto1);
    assertEquals(ltpName1, constrainingEntryProto1.getInterfaceId().getName());
    assertFalse(constrainingEntryProto1.hasEth());
    var constrainingEntryProto2 = ttpProto.getEdgeBindingConstraints().getSelector(0).getConstrainingEntryList().stream()
      .filter(constrainingEntry -> ltpName2.equals(constrainingEntry.getInterfaceId().getName())).findFirst().orElse(null);
    assertNotNull(constrainingEntryProto2);
    assertEquals(ltpName2, constrainingEntryProto2.getInterfaceId().getName());
    assertFalse(constrainingEntryProto2.hasEth());
    assertEquals(TunnelTerminationPointOuterClass.EdgeBindingConstraints.Type.TYPE_TERMINATION, ttpProto.getEdgeBindingConstraints().getType());
  }

  @Test
  @DisplayName("""
    GIVEN: TtpInfo with selected LTP with Container label
    WHEN: translateTTPData invoked
    THEN: Proto TTP message
    """)
  void testTranslateTTPDataWithSelectedLtpWithContainer() {
    // given
    String ttpName = "1/1/p3";
    String ltpName = "ET400OZRP-1/1/p2/et400ozrp";
    Container container = new Container(List.of(1), List.of(1));
    ConstrainingEntry constrainingEntry = new ConstrainingEntry(ltpName, container);
    EdgeBindingConstraints edgeBindingConstraints = new EdgeBindingConstraints();
    edgeBindingConstraints.setSelectedInterfaceId(ltpName);
    edgeBindingConstraints.setConstrainingEntryList(List.of(constrainingEntry));
    TtpInfo ttpInfo = new TtpInfo(ttpName, edgeBindingConstraints);

    // when
    var ttpProto = sut.translateTtpData(ttpInfo, NETWORK_ELEMENT_ID);

    // then
    assertEquals(ttpName, ttpProto.getInterfaceId().getName());
    assertEquals(ltpName, ttpProto.getEdgeBindingConstraints().getSelector(0).getSelectedInterfaceId().getName());
    assertEquals(1, ttpProto.getEdgeBindingConstraints().getSelector(0).getConstrainingEntryCount());
    TunnelTerminationPointOuterClass.ConstrainingEntry constrainingEntryProto = ttpProto.getEdgeBindingConstraints().getSelector(0).getConstrainingEntry(0);
    assertEquals(ltpName, constrainingEntryProto.getInterfaceId().getName());
    assertTrue(constrainingEntryProto.getEth().hasContainer());
    assertEquals(container.getTributaryIds(), constrainingEntryProto.getEth().getContainer().getIdList());
    assertEquals(container.getTributarySlots(), constrainingEntryProto.getEth().getContainer().getSlotsList());
    assertEquals(TunnelTerminationPointOuterClass.EdgeBindingConstraints.Type.TYPE_TERMINATION, ttpProto.getEdgeBindingConstraints().getType());
  }

  @Test
  @DisplayName("""
    GIVEN: TtpInfo with selected LTP with Whole label
    WHEN: translateTTPData invoked
    THEN: Proto TTP message
    """)
  void testTranslateTTPDataWithSelectedLtpWithWhole() {
    // given
    String ttpName = "1/1/p3";
    String ltpName = "ET400OZRP-1/1/p2/et400ozrp";
    ConstrainingEntry constrainingEntry = new ConstrainingEntry(ltpName, new Whole());
    EdgeBindingConstraints edgeBindingConstraints = new EdgeBindingConstraints();
    edgeBindingConstraints.setSelectedInterfaceId(ltpName);
    edgeBindingConstraints.setConstrainingEntryList(List.of(constrainingEntry));
    TtpInfo ttpInfo = new TtpInfo(ttpName, edgeBindingConstraints);

    // when
    var ttpProto = sut.translateTtpData(ttpInfo, NETWORK_ELEMENT_ID);

    // then
    assertEquals(ttpName, ttpProto.getInterfaceId().getName());
    assertEquals(ltpName, ttpProto.getEdgeBindingConstraints().getSelector(0).getSelectedInterfaceId().getName());
    assertEquals(1, ttpProto.getEdgeBindingConstraints().getSelector(0).getConstrainingEntryCount());
    TunnelTerminationPointOuterClass.ConstrainingEntry constrainingEntryProto = ttpProto.getEdgeBindingConstraints().getSelector(0).getConstrainingEntry(0);
    assertEquals(ltpName, constrainingEntryProto.getInterfaceId().getName());
    assertTrue(constrainingEntryProto.getEth().hasWhole());
    assertEquals(TunnelTerminationPointOuterClass.EdgeBindingConstraints.Type.TYPE_TERMINATION, ttpProto.getEdgeBindingConstraints().getType());
  }

  @Test
  @DisplayName("""
    GIVEN: TtpInfo without edgeBindingConstraints
    WHEN: translateTTPData invoked
    THEN: Proto TTP message
    """)
  void testTranslateTTPDataWithoutEdgeBindingConstraints() {
    // given
    String ttpName = "1/1/p3";
    TtpInfo ttpInfo = new TtpInfo(ttpName, null);

    // when
    var ttpProto = sut.translateTtpData(ttpInfo, NETWORK_ELEMENT_ID);

    // then
    assertEquals(ttpName, ttpProto.getInterfaceId().getName());
    assertEquals(0, ttpProto.getEdgeBindingConstraints().getSelectorCount());
  }

  @Test
  @DisplayName("""
    GIVEN: null TtpInfo
    WHEN: translateTTPData invoked
    THEN: null returned
    """)
  void translateTTPDataWhenNullTtpInfo() {
    // when
    var actual = sut.translateTtpData(null, -1);

    // then
    assertThat(actual).as("TTP NI bundle representation")
      .isNull();
  }

  @Test
  @DisplayName("""
    GIVEN: TtpInfo with no name
    WHEN: translateTTPData invoked
    THEN: NullPointerException thrown
    """)
  void translateTTPDataWhenTtpInfoHasNoName() {
    // given
    String ltpName = "ET400OZRP-1/1/p2/et400ozrp";
    ConstrainingEntry constrainingEntry = new ConstrainingEntry(ltpName, new Whole());
    EdgeBindingConstraints edgeBindingConstraints = new EdgeBindingConstraints();
    edgeBindingConstraints.setSelectedInterfaceId(ltpName);
    edgeBindingConstraints.setConstrainingEntryList(List.of(constrainingEntry));
    TtpInfo ttpInfo = new TtpInfo(null, edgeBindingConstraints);

    // when - then
    assertThrows(NullPointerException.class, () -> sut.translateTtpData(ttpInfo, NETWORK_ELEMENT_ID));
  }
}
