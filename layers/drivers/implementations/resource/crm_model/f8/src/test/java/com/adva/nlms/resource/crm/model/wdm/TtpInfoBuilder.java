/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: tstec
 */

package com.adva.nlms.resource.crm.model.wdm;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

public final class TtpInfoBuilder {

  private String name;
  private String sepUri;
  private boolean tunable;
  private final List<Consumer<SelectorBuilder>> builderLambdas = new ArrayList<>();

  public TtpInfoBuilder sepUri(String sepUri) {
    this.sepUri = sepUri;
    return this;
  }

  public TtpInfoBuilder name(String name) {
    this.name = name;
    return this;
  }

  public TtpInfoBuilder tunable(boolean tunable) {
    this.tunable = tunable;
    return this;
  }

  public TtpInfoBuilder newSelector(Consumer<SelectorBuilder> builderLambda) {
    builderLambdas.add(builderLambda);
    return this;
  }

  public TtpInfo build() {
    TtpInfo result = new TtpInfo();
    result.setName(name);
    result.setSepUri(sepUri);
    result.setTunable(tunable);
    for (Consumer<SelectorBuilder> builderLambda : builderLambdas) {
      SelectorBuilder selectorBuilder = new SelectorBuilder();
      builderLambda.accept(selectorBuilder);
      result.getEdgeBindingConstraints()
        .addSelector(selectorBuilder.build());
    }
    return result;
  }

  public static TtpInfoBuilder builder() {
    return new TtpInfoBuilder();
  }
}
