/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: <PERSON><PERSON><PERSON>
 */
package com.adva.nlms.resource.mediator.api.in;

import com.adva.nlms.driver.api.in.DDI;
import com.adva.nlms.opticalparameters.api.enums.AdminState;
import com.adva.nlms.opticalparameters.api.enums.LayerQualifier;
import ni.msg.EnvelopeOuterClass;

import java.util.List;
import java.util.UUID;

public interface ResourceRequestMediator extends DDI {
  void removeNetworkElement(int neId);

  void handleSegmentRequestInTask(EnvelopeOuterClass.Envelope envelope);

  void handleProtectionSwitch(UUID neId, String protectionGroupEntityIndex) throws OperationFailedException;

  void handleAdminStateOperation(UUID neId, List<String> aids, String protectionGroupIndex, AdminState adminState,
                                 LayerQualifier layerProtocolQualifier) throws OperationFailedException;
}
