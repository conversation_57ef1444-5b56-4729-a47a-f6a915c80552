/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.api.polling.out;

import java.util.UUID;

public interface OpticalRouterDriverManagerNotifications {
  /**
   * Received device info from device driver.
   *
   * @param deviceInfo basic information about the device
   */
  void deviceInfoReceived(OpticalRouterDeviceInfo deviceInfo);

  /**
   * Devi<PERSON> has been disconnected from the device driver.
   *
   * @param neId device identifier
   */
  void deviceDisconnected(UUID neId);

  /**
   * Callback invoked as a result of synchronizeDeviceInfo
   *
   * @param result command result
   */
  void synchronizationOfDeviceInfoCompleted(SynchronizationCommandResult result);

  /**
   * Callback invoked as a result of synchronizeInventory
   *
   * @param result command result
   */
  void synchronizationOfInventoryCompleted(SynchronizationCommandResult result);

  /**
   * Callback invoked as a result of synchronizeStatus
   *
   * @param result command result
   */
  void synchronizationOfStatusCompleted(SynchronizationCommandResult result);

}
