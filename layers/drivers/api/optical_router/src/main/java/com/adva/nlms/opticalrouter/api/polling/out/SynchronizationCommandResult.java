/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.api.polling.out;

import com.adva.nlms.opticalrouter.api.polling.in.CommandStatus;

import java.util.UUID;

/**
 * @param neId    device identifier
 * @param status  command status
 * @param message error message, if status is SUCCESS, then empty string
 */
public record SynchronizationCommandResult(
  UUID neId,
  CommandStatus status,
  String message
) {
}
