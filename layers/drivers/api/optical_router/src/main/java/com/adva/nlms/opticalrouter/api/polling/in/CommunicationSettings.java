/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: michalo
 */

package com.adva.nlms.opticalrouter.api.polling.in;

public record CommunicationSettings(
  String deviceType,
  String ipAddress,
  int port,
  String username,
  String password,
  int timeoutSeconds
) {
  @Override
  public String toString() {
    return "CommunicationSettings{" +
      "deviceType='" + deviceType + '\'' +
      ", ipAddress='" + ipAddress + '\'' +
      ", port=" + port +
      ", username='" + username + '\'' +
      ", timeoutSeconds=" + timeoutSeconds +
      '}';
  }
}
