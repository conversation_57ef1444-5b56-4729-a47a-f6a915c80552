[{"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/.*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+OTUC2PA, NEP+SIP@TTCTP@OTSiMC+OTU4"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#DCTCG/SM/LC, CFP2/448G/#DCTC/SM/LC, CFP2/448G/#BDCTC/SM/LC, CFP2/448G/#DCTCV/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+OTUC2, NEP+SIP@TTCTP@OTSiMC+OTUC3, NEP+SIP@TTCTP@OTSiMC+OTUC4"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.5.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#DCTCA.*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+OTUC2, NEP+SIP@TTCTP@OTSiMC+OTUC3, NEP+SIP@TTCTP@OTSiMC+OTUC4"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTCB/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.250:196.100@0.00625 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP, SYNC"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#DCTC/SM/LC, CFP2/448G/#DCTCG/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.2375:196.125@0.00625 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP, SYNC"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#BDCTC/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.275:196.125@0.00625 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "7.5.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#DCTCA.*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.2375:196.100@0.00625 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP, SYNC"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#DCTCV/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.300:196.100@0.00625 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP, SYNC"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.2375:196.100@0.00625 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP, SYNC"}, {"parameter": "Channel-RX", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#BDCTC/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.275:196.100@0.00625 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP"}, {"parameter": "Channel Bandwidth", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTCB/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "37.5@GHz", "default": "37.5 GHz", "tag": "OCAP, SYNC"}, {"parameter": "Channel Bandwidth", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "37.5, 50.0, 75.0@GHz", "default": "50.0 GHz", "tag": "OCAP, SYNC"}, {"parameter": "Setpoint", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTCB/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "-6.5:-0.5@0.1dBm", "default": "-0.5 dBm", "tag": "ECAP"}, {"parameter": "Setpoint", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#DCTCV/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "-5.0:4.0@0.1dBm", "default": "-2.0 dBm", "tag": "ECAP"}, {"parameter": "Setpoint", "elementtype": "FSP 3000C", "version": "7.5.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#DCTCA.*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "-8.0:0.0@0.1dBm", "default": "0.0dBm", "tag": "ECAP"}, {"parameter": "Setpoint", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#BDCTC/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "-13.0:0.0@0.1dBm", "default": "0.0dBm", "tag": "HIDE"}, {"parameter": "Setpoint", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "-8.0:1.0@0.1dBm", "default": "0.0dBm", "tag": "ECAP"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/.*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTU4", "values": "QPSK", "default": "QPSK", "tag": "ECAP, SYNC"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/.*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC2PA", "values": "16QAM", "default": "16QAM", "tag": "ECAP, SYNC"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#DCTCA.*, CFP2/448G/#DCTCG/SM/LC, CFP2/448G/#DCTC/SM/LC, CFP2/448G/#BDCTC/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC2", "values": "QPSK, 8QAM, 16QAM", "default": "QPSK", "tag": "ECAP, SYNC"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#DCTCA.*, CFP2/448G/#DCTCG/SM/LC, CFP2/448G/#DCTC/SM/LC, CFP2/448G/#BDCTC/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC3", "values": "8QAM", "default": "8QAM", "tag": "ECAP, SYNC"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#DCTCA.*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC4", "values": "16QAM, PS16QAM", "default": "16QAM", "tag": "ECAP, SYNC"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#DCTCG/SM/LC, CFP2/448G/#DCTC/SM/LC, CFP2/448G/#BDCTC/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC4", "values": "16QAM", "default": "16QAM", "tag": "ECAP, SYNC"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#DCTCV/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC2", "values": "QPSK, PS16QAM, 16QAM", "default": "QPSK", "tag": "ECAP, SYNC"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#DCTCV/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC3", "values": "8QAM, PS16QAM", "default": "8QAM", "tag": "ECAP, SYNC"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#DCTCV/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC4", "values": "16QAM, PS16QAM", "default": "16QAM", "tag": "ECAP, SYNC"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTCB/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC2PA|OTU4", "values": "FEC-SDB-20", "default": "FEC-SDB-20", "tag": "ECAP, SYNC"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC2PA|OTU4", "values": "FEC-A-15i3, FEC-A-15i1", "default": "FEC-A-15i3", "tag": "ECAP, SYNC"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#DCTCA.*, CFP2/448G/#DCTCG/SM/LC, CFP2/448G/#DCTC/SM/LC, CFP2/448G/#BDCTC/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC2|OTUC3|OTUC4", "values": "OFEC", "default": "OFEC", "tag": "ECAP, SYNC"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/448G/#DCTCV/SM/LC", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC2|OTUC3|OTUC4", "values": "FEC-SDB-20", "default": "FEC-SDB-20", "tag": "ECAP, SYNC"}, {"parameter": "Polarization Tracking", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC2|OTUC3|OTUC4", "values": "Normal, Fast", "default": "Normal", "tag": "ECAP, SYNC"}, {"parameter": "CD Post Compensation", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTU4", "values": "CD Range 1, CD Range 2", "default": "CD Range 2", "tag": "ECAP, SYNC"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTUC2PA", "values": "NEP+SIP@TTCTP@ODUC2PA"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTU4", "values": "NEP+SIP@TTCTP@ODU4, NEP@CTP@ODU4"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTUC2", "values": "NEP+SIP@TTCTP@ODUC2"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTUC3", "values": "NEP+SIP@TTCTP@ODUC3"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTUC4", "values": "NEP+SIP@TTCTP@ODUC4"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUC2PA", "values": "NEP+SIP@TTCTP@ODU4[1:2], NEP@CTP@ODU4[1:2]"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU4", "values": "NEP@CTP@ODU0[1:80], NEP@CTP@ODU1[1:80], NEP@CTP@ODU2[1:80], NEP@CTP@ODU2E[1:80], NEP@CTP@ODUF20[1:80]"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUC2", "values": "NEP@CTP@ODU0[1:20], NE<PERSON>@CTP@ODU1[1:20], NEP@CTP@ODU2[1:20], NEP@CTP@ODU2E[1:20], NEP@CTP@ODU4[1:20], NEP@CTP@ODUF20[1:20], NEP+SIP@TTCTP@ODU2[1:20]"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUC3", "values": "NEP@CTP@ODU0[1:30], NEP@CTP@ODU1[1:30], NEP@CTP@ODU2[1:30], NEP@CTP@ODU2E[1:30], NEP@CTP@ODU4[1:30], NEP@CTP@ODUF20[1:30], NEP+SIP@TTCTP@ODU2[1:30]"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUC4", "values": "NEP@CTP@ODU0[1:40], NE<PERSON>@CTP@ODU1[1:40], NEP@CTP@ODU2[1:40], NEP@CTP@ODU2E[1:40], NEP@CTP@ODU4[1:40], NEP@CTP@ODUF20[1:40], NEP@CTP@ODUF320, NEP+SIP@TTCTP@ODU2[1:40]"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N[1:2]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU2", "values": "NEP@CTP@ODU0[1:8], NEP@CTP@ODU1[1:8]"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP10/11G/4LR/SM/MPO,QSFP10/43G/SR4/MM/MPO,QSFP28/112G/AOC/.*,QSFP10-NONAPPROVED", "portid": "C[12789]-[1-4]|C1[012]-[1-4]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+OTU2, NEP+SIP@TTCTP@OTSiMC+OTU2E"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "SFP+/11G/.*,SFP+/11GU/.*,SFP+CDR/11GU/.*,SFP+-ACCEPTED,SFP+-NONAPPROVED", "portid": "C[3-6]|C13|C14", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+OTU2, NEP+SIP@TTCTP@OTSiMC+OTU2E"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": "dualcc", "plugtype": "SFP+/11G/.*,SFP+/11GU/.*,SFP+CDR/11GU/.*,SFP+-ACCEPTED,SFP+-NONAPPROVED", "portid": "C15|C16", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+OTU2, NEP+SIP@TTCTP@OTSiMC+OTU2E"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/SR4/.*,QSFP28/112G/LR4/.*,QSFP28/112G/ER4.*,QSFP28/112G/PSM4/.*,QSFP28/112G/AOC/.*,QSFP28-NONAPPROVED", "portid": "C1|C2|C7|C8|C9|C10|C11|C12", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+OTU4"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C2|C7|C8|C9|C10|C11|C12", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+OTU4, NEP+SIP@TTCTP@OTSiMC+ET100ZR"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP10/11G/4LR/SM/MPO,QSFP10/43G/SR4/MM/MPO,QSFP10-NONAPPROVED", "portid": "C[12789]-[1-4]|C1[012]-[1-4]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL+OTU2, SIP@CTP@ODU2, NEP@TTCTP@OPTICAL+OTU2E, SIP@CTP@ODU2E"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "SFP+/11G/.*,SFP+/11GU/.*,SFP+CDR/11GU/.*,SFP+-ACCEPTED,SFP+-NONAPPROVED", "portid": "C[3-6]|C13|C14", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL+OTU2, SIP@CTP@ODU2, NEP@TTCTP@OPTICAL+OTU2E, SIP@CTP@ODU2E"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": "dualcc", "plugtype": "SFP+/11G/.*,SFP+/11GU/.*,SFP+CDR/11GU/.*,SFP+-ACCEPTED,SFP+-NONAPPROVED", "portid": "C15|C16", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL+OTU2, SIP@CTP@ODU2, NEP@TTCTP@OPTICAL+OTU2E, SIP@CTP@ODU2E"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/SR4/.*,QSFP28/112G/LR4/.*,QSFP28/112G/ER4.*,QSFP28/112G/PSM4/.*,QSFP28/112G/AOC/.*,QSFP28-NONAPPROVED", "portid": "C1|C2|C7|C8|C9|C10|C11|C12", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL+OTU4, SIP@CTP@ODU4"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C2|C7|C8|C9|C10|C11|C12", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL+OTU4, SIP@CTP@ODU4"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/AOC/.*", "portid": "C[12789]-[1-4]|C1[012]-[1-4]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL+OTU2, SIP@CTP@ODU2, NEP@TTCTP@OPTICAL+OTU2E, SIP@CTP@ODU2E"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C2|C7|C8|C9|C10|C11|C12", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL+ET100ZR"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP10/11G/4LR/SM/MPO, QSFP10/43G/SR4/MM/MPO, QSFP10-NONAPPROVED", "portid": "C[12789]-[1-4]|C1[012]-[1-4]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-10G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "SFP+/11G/.*,SFP+/11GU/.*,SFP+CDR/11GU/.*,SFP+-ACCEPTED,SFP+-NONAPPROVED", "portid": "C[3-6]|C13|C14", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-10G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": "dualcc", "plugtype": "SFP+/11G/.*,SFP+/11GU/.*,SFP+CDR/11GU/.*,SFP+-ACCEPTED,SFP+-NONAPPROVED", "portid": "C15|C16", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-10G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/SR4/MM/MPO,QSFP28/112G/PSM4/SM/MPO,QSFP28/103G/PSM4/SM/MPO,QSFP28/103G/SR4/MM/MPO,QSFP28-NONAPPROVED", "portid": "C[12789]-[1-4]|C1[012]-[1-4]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-25G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "SFP28/25G.*, SFP28-NONAPPROVED", "portid": "C[3-6]|C13|C14", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-25G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": "dualcc", "plugtype": "SFP28/25G.*, SFP28-NONAPPROVED", "portid": "C15|C16", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-25G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/AOC/.*", "portid": "C[12789]-[1-4]|C1[012]-[1-4]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-10G, NEP+SIP@CTP@ETH-25G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/.*,QSFP28/106G/.*,QSFP28/103G/.*,QSFP28-NONAPPROVED", "portid": "C1|C2|C7|C8|C9|C10|C11|C12", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C2|C7|C8|C9|C10|C11|C12", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL+ET100ZR", "values": "NEP+SIP@CTP@ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": "singlecc", "plugtype": "QSFP56-DD/425G/.*,QSFP56-DD-NONAPPROVED", "portid": "C1|C2", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-400G"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "SFP+/11GU/DCT.*", "portid": "C[3-6]|C1[3-6]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.250:196.000@0.05000 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP, SYNC"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "SFP+/11GU/DCT.*", "portid": "C[3-6]|C1[3-6]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "191.250:196.000@0.05000 THz", "multiplier": 1000000, "default": "196.000 THz", "tag": "ECAP"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C2|C7|C8|C9|C10|C11|C12", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.250:196.100@0.00625 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP, SYNC"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C2|C7|C8|C9|C10|C11|C12", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "191.250:196.100@0.00625 THz", "multiplier": 1000000, "default": "196.000 THz", "tag": "ECAP"}, {"parameter": "Channel Bandwidth", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "SFP+/11GU/DCT.*, SFP+/11GU/#.*", "portid": "C[3-6]|C1[3-6]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC|OPTICAL", "values": "50.0@GHz", "default": "50.0 GHz", "tag": "OCAP, SYNC"}, {"parameter": "Channel Bandwidth", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C2|C7|C8|C9|C10|C11|C12", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "37.5@GHz", "default": "37.5 GHz", "tag": "OCAP"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C2|C7|C8|C9|C10|C11|C12", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ET100ZR|OTU4", "values": "QPSK", "default": "QPSK", "tag": "ECAP, SYNC"}, {"parameter": "Error Forwarding Mode", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC|OPTICAL", "values": "AIS, <PERSON><PERSON> off immediate, <PERSON><PERSON> off delayed", "default": "Laser off immediate", "tag": "ECAP"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTU2|OTU2E|OTU4", "values": "None, GFEC", "default": "GFEC", "tag": "ECAP, SYNC"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "SFP28/25G/SR/MM/LC/TWD,SFP28/25GU/LR/SM/LC/TIN,SFP28-NONAPPROVED,QSFP28/103G/LR4/SM/LC,QSFP28/103G/SR4/MM/MPO,QSFP28/103G/PSM4/SM/MPO,QSFP28/103G/CWDM4/SM/LC,QSFP28/103G/CWDM4E/SM/LC,QSFP28/112G/.*,QSFP28-NONAPPROVED", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-25G|ETH-100G", "values": "None, RSFEC-2", "default": "RSFEC-2", "tag": "ECAP"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/103G/SR-BD/MM/LC", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-100G", "values": "None", "default": "None", "tag": "ECAP"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/106G/.*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-100G", "values": "RSFEC-544", "default": "RSFEC-544", "tag": "ECAP"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C1|C2", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-400G", "values": "RSFEC-544", "default": "RSFEC-544", "tag": "ECAP"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C2|C7|C8|C9|C10|C11|C12", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ET100ZR|OTU4", "values": "SCFEC", "default": "SCFEC", "tag": "ECAP, SYNC"}, {"parameter": "Maintenance Signal", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-10G|ETH-25G|ETH-100G|ETH-400G", "values": "Idle, Local Fault", "default": "Local Fault", "tag": "ECAP"}, {"parameter": "Maintenance Signal", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-100G", "values": "Idle, Local Fault", "default": "Local Fault", "tag": "HIDE"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTU2", "values": "NEP+SIP@TTCTP@ODU2, NEP@CTP@ODU2"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTU2E", "values": "NEP@CTP@ODU2E"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTU4", "values": "NEP+SIP@TTCTP@ODU4, NEP@CTP@ODU4"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL+OTU2", "values": "NEP@CTP@ODU2"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL+OTU2E", "values": "NEP@CTP@ODU2E"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL+OTU4", "values": "NEP@CTP@ODU4"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C1|C2|C7|C8|C9|C10|C11|C12", "lpqterm": "TTCTP", "payloadtype": ".*", "physicalpq": "OTSiMC+OTU4", "lpq": "ODU4", "values": "NEP@CTP@ODU0[1:80], NEP@CTP@ODU1[1:80], NEP@CTP@ODU2[1:80], NEP@CTP@ODU2E[1:80], NEP@CTP@ODUF20[1:80]"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C[12789]-[1-4]|C1[012]-[1-4]", "lpqterm": "TTCTP", "payloadtype": ".*", "physicalpq": "OTSiMC+OTU2", "lpq": "ODU2", "values": "NEP@CTP@ODU0[1:8], NEP@CTP@ODU1[1:8]"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C[3-6]|C1[3-6]", "lpqterm": "TTCTP", "payloadtype": ".*", "physicalpq": "OTSiMC+OTU2", "lpq": "ODU2", "values": "NEP@CTP@ODU0[1:8], NEP@CTP@ODU1[1:8]"}, {"parameter": "POOL-RESTRICTION", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/103G/PSM4/SM/MPO, QSFP28/103G/SR4/MM/MPO, QSFP28/112G/AOC/.*, QSFP28/112G/PSM4/SM/MPO, QSFP28/112G/SR4/MM/MPO, QSFP28-NONAPPROVED", "portid": "C1", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "[C1], [C1-1, C1-2, C1-3, C1-4]", "tag": "GRES"}, {"parameter": "POOL-RESTRICTION", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/103G/PSM4/SM/MPO, QSFP28/103G/SR4/MM/MPO, QSFP28/112G/AOC/.*, QSFP28/112G/PSM4/SM/MPO, QSFP28/112G/SR4/MM/MPO, QSFP28-NONAPPROVED", "portid": "C2", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "[C2], [C2-1, C2-2, C2-3, C2-4]", "tag": "GRES"}, {"parameter": "POOL-RESTRICTION", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/103G/PSM4/SM/MPO, QSFP28/103G/SR4/MM/MPO, QSFP28/112G/AOC/.*, QSFP28/112G/PSM4/SM/MPO, QSFP28/112G/SR4/MM/MPO, QSFP28-NONAPPROVED", "portid": "C7", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "[C7], [C7-1, C7-2, C7-3, C7-4]", "tag": "GRES"}, {"parameter": "POOL-RESTRICTION", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/103G/PSM4/SM/MPO, QSFP28/103G/SR4/MM/MPO, QSFP28/112G/AOC/.*, QSFP28/112G/PSM4/SM/MPO, QSFP28/112G/SR4/MM/MPO, QSFP28-NONAPPROVED", "portid": "C8", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "[C8], [C8-1, C8-2, C8-3, C8-4]", "tag": "GRES"}, {"parameter": "POOL-RESTRICTION", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/103G/PSM4/SM/MPO, QSFP28/103G/SR4/MM/MPO, QSFP28/112G/AOC/.*, QSFP28/112G/PSM4/SM/MPO, QSFP28/112G/SR4/MM/MPO, QSFP28-NONAPPROVED", "portid": "C9", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "[C9], [C9-1, C9-2, C9-3, C9-4]", "tag": "GRES"}, {"parameter": "POOL-RESTRICTION", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/103G/PSM4/SM/MPO, QSFP28/103G/SR4/MM/MPO, QSFP28/112G/AOC/.*, QSFP28/112G/PSM4/SM/MPO, QSFP28/112G/SR4/MM/MPO, QSFP28-NONAPPROVED", "portid": "C10", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "[C10], [C10-1, C10-2, C10-3, C10-4]", "tag": "GRES"}, {"parameter": "POOL-RESTRICTION", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/103G/PSM4/SM/MPO, QSFP28/103G/SR4/MM/MPO, QSFP28/112G/AOC/.*, QSFP28/112G/PSM4/SM/MPO, QSFP28/112G/SR4/MM/MPO, QSFP28-NONAPPROVED", "portid": "C11", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "[C11], [C11-1, C11-2, C11-3, C11-4]", "tag": "GRES"}, {"parameter": "POOL-RESTRICTION", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/103G/PSM4/SM/MPO, QSFP28/103G/SR4/MM/MPO, QSFP28/112G/AOC/.*, QSFP28/112G/PSM4/SM/MPO, QSFP28/112G/SR4/MM/MPO, QSFP28-NONAPPROVED", "portid": "C12", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "[C12], [C12-1, C12-2, C12-3, C12-4]", "tag": "GRES"}, {"parameter": "PROTECTION-ENDPOINT", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU2|ODU2E|ODU4", "values": "SNCP, CCCP"}, {"parameter": "PROTECTION-ENDPOINT", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU2E+ETH-10G|ODUF20+ETH-25G|ODU4+ETH-100G", "values": "SNCP, CCCP"}, {"parameter": "PROTECTION-ENDPOINT", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C1|C2", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUF320+ETH-400G", "values": "CCCP"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP10/11G/4LR/SM/MPO,QSFP10/43G/SR4/MM/MPO,QSFP10-NONAPPROVED", "portid": "C[12789]-[1-4]|C1[012]-[1-4]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU2E, SIP@CTP@ODU2E+ETH-10G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "SFP+/11G/.*,SFP+/11GU/.*,SFP+CDR/11GU/.*,SFP+-ACCEPTED,SFP+-NONAPPROVED", "portid": "C[3-6]|C13|C14", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU2E, SIP@CTP@ODU2E+ETH-10G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": "dualcc", "plugtype": "SFP+/11G/.*,SFP+/11GU/.*,SFP+CDR/11GU/.*,SFP+-ACCEPTED,SFP+-NONAPPROVED", "portid": "C15|C16", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU2E, SIP@CTP@ODU2E+ETH-10G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/SR4/MM/MPO,QSFP28/112G/PSM4/SM/MPO,QSFP28/103G/PSM4/SM/MPO,QSFP28/103G/SR4/MM/MPO,QSFP28-NONAPPROVED", "portid": "C[12789]-[1-4]|C1[012]-[1-4]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODUF20, SIP@CTP@ODUF20+ETH-25G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "SFP28/25G.*, SFP28-NONAPPROVED", "portid": "C[3-6]|C13|C14", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODUF20, SIP@CTP@ODUF20+ETH-25G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": "dualcc", "plugtype": "SFP28/25G.*, SFP28-NONAPPROVED", "portid": "C15|C16", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODUF20, SIP@CTP@ODUF20+ETH-25G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/AOC/.*", "portid": "C[12789]-[1-4]|C1[012]-[1-4]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU2E, SIP@CTP@ODU2E+ETH-10G, NEP@TTP@ODUF20, SIP@CTP@ODUF20+ETH-25G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/.*,QSFP28/106G/.*,QSFP28/103G/.*,QSFP28-NONAPPROVED", "portid": "C1|C2|C7|C8|C9|C10|C11|C12", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU4, SIP@CTP@ODU4+ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": "singlecc", "plugtype": "QSFP56-DD/425G/.*,QSFP56-DD-NONAPPROVED", "portid": "C1|C2", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODUF320, SIP@CTP@ODUF320+ETH-400G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU2E", "values": "NEP@CTP@ETH-10G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUF20", "values": "NEP@CTP@ETH-25G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU4", "values": "NEP@CTP@ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "OF-2D16DCT-SP", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUF320", "values": "NEP@CTP@ETH-400G"}]