[{"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+OTUC2PA, NEP+SIP@TTCTP@OTSiMC+OTU4"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTCB/SM/LC", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.250:196.100@0.00625 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP, SYNC"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTC/SM/LC, CFP2/224G/#DCTCG/SM/LC", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.2375:196.100@0.00625 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP, SYNC"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTC2/SM/LC, CFP2/224G/#DCTC2G/SM/LC", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.2375:196.125@0.00625 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP, SYNC"}, {"parameter": "Channel Bandwidth", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTC/SM/LC, CFP2/224G/#DCTCG/SM/LC", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "37.5, 50.0@GHz", "default": "50.0 GHz", "tag": "OCAP, SYNC"}, {"parameter": "Channel Bandwidth", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTC2/SM/LC, CFP2/224G/#DCTC2G/SM/LC", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "37.5, 50.0, 75.0@GHz", "default": "50.0 GHz", "tag": "OCAP, SYNC"}, {"parameter": "Channel Bandwidth", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTCB/SM/LC", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "37.5@GHz", "default": "37.5 GHz", "tag": "OCAP, SYNC"}, {"parameter": "Setpoint", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTCB/SM/LC", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "-6.5:-0.5@0.1dBm", "default": "-0.5 dBm", "tag": "ECAP"}, {"parameter": "Setpoint", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTC/SM/LC, CFP2/224G/#DCTCG/SM/LC", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "-5.0:1.0@0.1dBm", "default": "0.0 dBm", "tag": "ECAP"}, {"parameter": "Setpoint", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTC2/SM/LC, CFP2/224G/#DCTC2G/SM/LC", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "-8.0:3.0@0.1dBm", "default": "0.0 dBm", "tag": "ECAP"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTU4", "values": "QPSK", "default": "QPSK", "tag": "ECAP, SYNC"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTCB/SM/LC", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC2PA", "values": "16QAM", "default": "16QAM", "tag": "ECAP, SYNC"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTC/SM/LC, CFP2/224G/#DCTCG/SM/LC", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC2PA", "values": "8QAM, 16QAM", "default": "8QAM", "tag": "ECAP, SYNC"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTC2/SM/LC, CFP2/224G/#DCTC2G/SM/LC", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC2PA", "values": "QPSK, 8QAM, 16QAM", "default": "8QAM", "tag": "ECAP, SYNC"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTCB/SM/LC", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC2PA|OTU4", "values": "FEC-SDB-20", "default": "FEC-SDB-20", "tag": "ECAP, SYNC"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTC/SM/LC, CFP2/224G/#DCTCG/SM/LC", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC2PA|OTU4", "values": "FEC-A-15i3, FEC-A-15i1", "default": "FEC-A-15i3", "tag": "ECAP, SYNC"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTC2/SM/LC, CFP2/224G/#DCTC2G/SM/LC", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC2PA|OTU4", "values": "OFEC", "default": "OFEC", "tag": "ECAP, SYNC"}, {"parameter": "Polarization Tracking", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC2PA|OTU4", "values": "Normal, Fast", "default": "Normal", "tag": "ECAP, SYNC"}, {"parameter": "CD Post Compensation", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "CFP2/224G/#DCTC/SM/LC, CFP2/224G/#DCTCG/SM/LC", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTU4", "values": "CD Range 1, CD Range 2", "default": "CD Range 2", "tag": "ECAP, SYNC"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTUC2PA", "values": "NEP+SIP@TTCTP@ODUC2PA"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTU4", "values": "NEP+SIP@TTCTP@ODU4, NEP@CTP@ODU4"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUC2PA", "values": "NEP+SIP@TTCTP@ODU4[1:2], NEP@CTP@ODU4[1:2]"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": ".*", "portid": "N", "lpqterm": "TTP", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU4", "values": "NEP@CTP@ODU2[1:80], NEP@CTP@ODU2E[1:80], NEP@CTP@ODU3[1:80], NEP@CTP@ODUF07[1:80], NEP@CTP@ODUF11[1:80]"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "otncc", "plugtype": ".*", "portid": "N", "lpqterm": "TTP", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU4", "values": "NEP@CTP@ODU0[1:80], NEP@CTP@ODU1[1:80], NEP@CTP@ODU2[1:80], NEP@CTP@ODU2E[1:80], NEP@CTP@ODU3[1:80]"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP10/11G/.*,QSFP10/43G/SR4/MM/MPO,QSFP10-NONAPPROVED", "portid": "C[1-9]|C10|C1[1-9]|C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+OTU2, NEP+SIP@TTCTP@OTSiMC+OTU2E"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP10/11G/.*,QSFP10/43G/SR4/MM/MPO,QSFP10-NONAPPROVED", "portid": "C[1-9]|C10|C1[1-9]|C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL+OTU2, SIP@CTP@ODU2, NEP@TTCTP@OPTICAL+OTU2E, SIP@CTP@ODU2E"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/SR4/.*,QSFP28/112G/LR4/.*,QSFP28/112G/ER4.*,QSFP28/112G/PSM4/.*,QSFP28-NONAPPROVED", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+OTU4"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/SR4/.*,QSFP28/112G/LR4/.*,QSFP28/112G/ER4.*,QSFP28/112G/PSM4/.*,QSFP28-NONAPPROVED", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL+OTU4, SIP@CTP@ODU4"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+OTU2, NEP+SIP@TTCTP@OTSiMC+OTU2E, NEP+SIP@TTCTP@OTSiMC+OTU4"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL+OTU2, SIP@CTP@ODU2, NEP@TTCTP@OPTICAL+OTU2E, SIP@CTP@ODU2E, NEP@TTCTP@OPTICAL+OTU4, SIP@CTP@ODU4"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C[2-4]|C[6-9]|C10|C1[1-9]|C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+OTU2, NEP+SIP@TTCTP@OTSiMC+OTU2E"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C[2-4]|C[6-9]|C10|C1[1-9]|C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL+OTU2, SIP@CTP@ODU2, NEP@TTCTP@OPTICAL+OTU2E, SIP@CTP@ODU2E"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C[1-9]|C10|C1[1-9]|C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP10/11G/.*,QSFP10-NONAPPROVED", "portid": "C[1-9]|C10|C1[1-9]|C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-10G, NEP+SIP@CTP@STM-64, NEP+SIP@CTP@FC-8G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP10/43G/LR4/SM/LC", "portid": "C1|C5|C9|C13|C17", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-40G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP10/43G/SR4/MM/MPO", "portid": "C1|C5|C9|C13|C17", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-10G, NEP+SIP@CTP@STM-64, NEP+SIP@CTP@FC-8G, NEP+SIP@CTP@ETH-40G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP10/43G/SR4/MM/MPO", "portid": "C[234678]|C10|C1[1245689]|C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-10G, NEP+SIP@CTP@STM-64, NEP+SIP@CTP@FC-8G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP14/16GFC/4LR/SM/MPO,QSFP14-NONAPPROVED", "portid": "C[1-9]|C10|C1[1-9]|C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-10G, NEP+SIP@CTP@FC-8G, NEP+SIP@CTP@FC-16G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP14/56G/SR4/MM/MPO", "portid": "C1|C5|C9|C13|C17", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-10G, NEP+SIP@CTP@FC-8G, NEP+SIP@CTP@FC-16G, NEP+SIP@CTP@ETH-40G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP14/56G/SR4/MM/MPO", "portid": "C[234678]|C10|C1[1245689]|C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-10G, NEP+SIP@CTP@FC-8G, NEP+SIP@CTP@FC-16G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/103G/PSM4/SM/MPO,QSFP28/103G/SR4/MM/MPO", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/PSM4/SM/MPO, QSFP28/112G/SR4/MM/MPO", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@FC-16G, NEP+SIP@CTP@ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/PSM4/SM/MPO, QSFP28/112G/SR4/MM/MPO", "portid": "C[234678]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@FC-16G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/SR4/MM/MPO", "portid": "C9|C10|C1[123456789]|C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@FC-16G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/LR4/.*,QSFP28/112G/ER4.*,QSFP28/106G/.*,QSFP28/103G/LR4/.*,QSFP28/103G/CWDM.*,QSFP28/103G/SR-BD/.*,QSFP28/10X10G/850I/MM/MPO,QSFP28-NONAPPROVED", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@FC-8G, NEP+SIP@CTP@FC-16G, NEP+SIP@CTP@ETH-10G, NEP+SIP@CTP@STM-64, NEP+SIP@CTP@ETH-40G, NEP+SIP@CTP@ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C[234678]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@FC-8G, NEP+SIP@CTP@FC-16G, NEP+SIP@CTP@ETH-10G, NEP+SIP@CTP@STM-64"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C9|C13|C17", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@FC-8G, NEP+SIP@CTP@FC-16G, NEP+SIP@CTP@ETH-10G, NEP+SIP@CTP@STM-64, NEP+SIP@CTP@ETH-40G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C1[01245689]|C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@FC-8G, NEP+SIP@CTP@FC-16G, NEP+SIP@CTP@ETH-10G, NEP+SIP@CTP@STM-64"}, {"parameter": "Error Forwarding Mode", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC|OPTICAL", "values": "AIS, <PERSON><PERSON> off immediate, <PERSON><PERSON> off delayed", "default": "Laser off immediate", "tag": "ECAP"}, {"parameter": "Error Forwarding Mode", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/DAC/.*, QSFP28/112G/ZR+/.*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC|OPTICAL", "values": "AIS, <PERSON><PERSON> off immediate, <PERSON><PERSON> off delayed", "default": "Laser off immediate", "tag": "HIDE"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTU2|OTU2E|OTU4", "values": "None, GFEC", "default": "GFEC", "tag": "ECAP, SYNC"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/103G/SR-BD/MM/LC,QSFP28/10X10G/850I/MM/MPO, QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-100G", "values": "None", "default": "None", "tag": "ECAP"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/106G/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-100G", "values": "RSFEC-544", "default": "RSFEC-544", "tag": "ECAP"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-100G", "values": "None, RSFEC-2", "default": "RSFEC-2", "tag": "ECAP"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-100G", "values": "None", "default": "None", "tag": "HIDE"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTU2", "values": "NEP@CTP@ODU2"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "otncc", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTU2", "values": "NEP+SIP@TTCTP@ODU2, NEP@CTP@ODU2"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTU2E", "values": "NEP@CTP@ODU2E"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTU4", "values": "NEP@CTP@ODU4"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "otncc", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTU4", "values": "NEP+SIP@TTCTP@ODU4, NEP@CTP@ODU4"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL+OTU2", "values": "NEP@CTP@ODU2"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL+OTU2E", "values": "NEP@CTP@ODU2E"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL+OTU4", "values": "NEP@CTP@ODU4"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "otncc", "plugtype": ".*", "portid": "C1|C5", "lpqterm": "TTCTP", "payloadtype": ".*", "physicalpq": "OTSiMC+OTU4", "lpq": "ODU4", "values": "NEP@CTP@ODU0[1:80], NEP@CTP@ODU1[1:80], NEP@CTP@ODU2[1:80]"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "otncc", "plugtype": ".*", "portid": "C[1-9]|C10|C1[1-9]|C20", "lpqterm": "TTCTP", "payloadtype": ".*", "physicalpq": "OTSiMC+OTU2", "lpq": "ODU2", "values": "NEP@CTP@ODU0[1:8], NEP@CTP@ODU1[1:8]"}, {"parameter": "POOL-RESTRICTION", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/PSM4/SM/MPO, QSFP28/112G/SR4/MM/MPO, QSFP10/43G/SR4/MM/MPO, QSFP14/56G/SR4/MM/MPO, QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C1-C4", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "[C1, C2, C3, C4]", "tag": "GRES"}, {"parameter": "POOL-RESTRICTION", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/PSM4/SM/MPO, QSFP28/112G/SR4/MM/MPO, QSFP10/43G/SR4/MM/MPO, QSFP14/56G/SR4/MM/MPO, QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C5-C8", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "[C5, C6, C7, C8]", "tag": "GRES"}, {"parameter": "POOL-RESTRICTION", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/PSM4/SM/MPO, QSFP28/112G/SR4/MM/MPO, QSFP10/43G/SR4/MM/MPO, QSFP14/56G/SR4/MM/MPO, QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C9-C12", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "[C9, C10, C11, C12]", "tag": "GRES"}, {"parameter": "POOL-RESTRICTION", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/PSM4/SM/MPO, QSFP28/112G/SR4/MM/MPO, QSFP10/43G/SR4/MM/MPO, QSFP14/56G/SR4/MM/MPO, QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C13-C16", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "[C13, C14, C15, C16]", "tag": "GRES"}, {"parameter": "POOL-RESTRICTION", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/PSM4/SM/MPO, QSFP28/112G/SR4/MM/MPO, QSFP10/43G/SR4/MM/MPO, QSFP14/56G/SR4/MM/MPO, QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C17-C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "[C17, C18, C19, C20]", "tag": "GRES"}, {"parameter": "USAGE-RESTRICTION", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/PSM4/SM/MPO, QSFP28/112G/SR4/MM/MPO", "portid": "C1", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-100G|OTU4", "values": "[C2, C3, C4]", "tag": "GRES"}, {"parameter": "USAGE-RESTRICTION", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/PSM4/SM/MPO, QSFP28/112G/SR4/MM/MPO", "portid": "C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-100G|OTU4", "values": "[C6, C7, C8]", "tag": "GRES"}, {"parameter": "USAGE-RESTRICTION", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP10/43G/SR4/MM/MPO, QSFP14/56G/SR4/MM/MPO", "portid": "C1", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-40G", "values": "[C2, C3, C4]", "tag": "GRES"}, {"parameter": "USAGE-RESTRICTION", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP10/43G/SR4/MM/MPO, QSFP14/56G/SR4/MM/MPO", "portid": "C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-40G", "values": "[C6, C7, C8]", "tag": "GRES"}, {"parameter": "USAGE-RESTRICTION", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP10/43G/SR4/MM/MPO, QSFP14/56G/SR4/MM/MPO", "portid": "C9", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-40G", "values": "[C10, C11, C12]", "tag": "GRES"}, {"parameter": "USAGE-RESTRICTION", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP10/43G/SR4/MM/MPO, QSFP14/56G/SR4/MM/MPO", "portid": "C13", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-40G", "values": "[C14, C15, C16]", "tag": "GRES"}, {"parameter": "USAGE-RESTRICTION", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP10/43G/SR4/MM/MPO, QSFP14/56G/SR4/MM/MPO", "portid": "C17", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-40G", "values": "[C18, C19, C20]", "tag": "GRES"}, {"parameter": "USAGE-RESTRICTION", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C1", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-40G|ETH-100G|OTU4", "values": "[C2, C3, C4]", "tag": "GRES"}, {"parameter": "USAGE-RESTRICTION", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-40G|ETH-100G|OTU4", "values": "[C6, C7, C8]", "tag": "GRES"}, {"parameter": "USAGE-RESTRICTION", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C9", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-40G", "values": "[C10, C11, C12]", "tag": "GRES"}, {"parameter": "USAGE-RESTRICTION", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C13", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-40G", "values": "[C14, C15, C16]", "tag": "GRES"}, {"parameter": "USAGE-RESTRICTION", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C17", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-40G", "values": "[C18, C19, C20]", "tag": "GRES"}, {"parameter": "PROTECTION-ENDPOINT", "elementtype": "FSP 3000C", "version": "5.5.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU2|ODU2E|ODU4", "values": "CCCP"}, {"parameter": "PROTECTION-ENDPOINT", "elementtype": "FSP 3000C", "version": "5.5.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU2+STM-64|ODU2E+ETH-10G|ODUF07+FC-8G|ODUF11+FC-16G|ODU3+ETH-40G|ODU4+ETH-100G", "values": "CCCP"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+ET100ZR"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL+ET100ZR"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.250:196.100@0.00625 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP, SYNC"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "191.250:196.100@0.00625 THz", "multiplier": 1000000, "default": "196.000 THz", "tag": "ECAP"}, {"parameter": "Channel Bandwidth", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "37.5@GHz", "default": "37.5 GHz", "tag": "OCAP"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ET100ZR", "values": "QPSK", "default": "QPSK", "tag": "ECAP, SYNC"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ET100ZR", "values": "SCFEC", "default": "SCFEC", "tag": "ECAP, SYNC"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+ET100ZR", "values": "NEP@CTP@ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL+ET100ZR", "values": "NEP+SIP@CTP@ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP10/11G/.*,QSFP10-NONAPPROVED", "portid": "C[1-9]|C10|C1[1-9]|C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU2E, SIP@CTP@ODU2E+ETH-10G, NEP@TTP@ODU2, SIP@CTP@ODU2+STM-64, NEP@TTP@ODU2, NEP@TTP@ODUF07, SIP@CTP@ODUF07+FC-8G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP10/43G/LR4/SM/LC", "portid": "C1|C5|C9|C13|C17", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU3, SIP@CTP@ODU3+ETH-40G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP10/43G/SR4/MM/MPO", "portid": "C1|C5|C9|C13|C17", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU2E, SIP@CTP@ODU2E+ETH-10G, NEP@TTP@ODU2, SIP@CTP@ODU2+STM-64, NEP@TTP@ODU2, NEP@TTP@ODUF07, SIP@CTP@ODUF07+FC-8G, NE<PERSON>@TTP@ODU3, SIP@CTP@ODU3+ETH-40G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP10/43G/SR4/MM/MPO", "portid": "C[234678]|C10|C1[1245689]|C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU2E, SIP@CTP@ODU2E+ETH-10G, NEP@TTP@ODU2, SIP@CTP@ODU2+STM-64, NEP@TTP@ODU2, NEP@TTP@ODUF07, SIP@CTP@ODUF07+FC-8G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP14/16GFC/4LR/SM/MPO,QSFP14-NONAPPROVED", "portid": "C[1-9]|C10|C1[1-9]|C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU2E, SIP@CTP@ODU2E+ETH-10G, NEP@TTP@ODUF07, SIP@CTP@ODUF07+FC-8G, NEP@TTP@ODUF11, SIP@CTP@ODUF11+FC-16G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP14/56G/SR4/MM/MPO", "portid": "C1|C5|C9|C13|C17", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU2E, SIP@CTP@ODU2E+ETH-10G, NEP@TTP@ODUF07, SIP@CTP@ODUF07+FC-8G, NEP@TTP@ODUF11, SIP@CTP@ODUF11+FC-16G, NEP@TTP@ODU3, SIP@CTP@ODU3+ETH-40G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP14/56G/SR4/MM/MPO", "portid": "C[234678]|C10|C1[1245689]|C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU2E, SIP@CTP@ODU2E+ETH-10G, NEP@TTP@ODUF07, SIP@CTP@ODUF07+FC-8G, NEP@TTP@ODUF11, SIP@CTP@ODUF11+FC-16G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/103G/PSM4/SM/MPO,QSFP28/103G/SR4/MM/MPO", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU4, SIP@CTP@ODU4+ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/PSM4/SM/MPO,QSFP28/112G/SR4/MM/MPO", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODUF11, SIP@CTP@ODUF11+FC-16G, NEP@TTP@ODU4, SIP@CTP@ODU4+ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU4, SIP@CTP@ODU4+ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/PSM4/SM/MPO,QSFP28/112G/SR4/MM/MPO", "portid": "C[234678]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODUF11, SIP@CTP@ODUF11+FC-16G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/SR4/MM/MPO", "portid": "C9|C10|C1[123456789]|C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODUF11, SIP@CTP@ODUF11+FC-16G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/LR4/.*,QSFP28/112G/ER4.*,QSFP28/106G/.*,QSFP28/103G/LR4/.*,QSFP28/103G/CWDM.*,QSFP28/103G/SR-BD/.*,QSFP28/10X10G/850I/MM/MPO,QSFP28-NONAPPROVED", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU4, SIP@CTP@ODU4+ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODUF07, <PERSON>IP@CTP@ODUF07+FC-8G, NE<PERSON>@TTP@ODUF11, SIP@CTP@ODUF11+FC-16G, NEP@TTP@ODU2E, SIP@CTP@ODU2E+ETH-10G, <PERSON><PERSON>@TTP@ODU2, <PERSON>IP@CTP@ODU2+STM-64, <PERSON><PERSON>@TTP@ODU2, NE<PERSON>@TTP@ODU3, SIP@CTP@ODU3+ETH-40G, NEP@TTP@ODU4, SIP@CTP@ODU4+ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C[234678]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODUF07, SIP@CTP@ODUF07+FC-8G, NEP@TTP@ODUF11, SIP@CTP@ODUF11+FC-16G, NEP@TTP@ODU2E, SIP@CTP@ODU2E+ETH-10G, <PERSON><PERSON>@TTP@ODU2, SIP@CTP@ODU2+STM-64, NEP@TTP@ODU2"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C9|C13|C17", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODUF07, SIP@CTP@ODUF07+FC-8G, NEP@TTP@ODUF11, SIP@CTP@ODUF11+FC-16G, NEP@TTP@ODU3, SIP@CTP@ODU3+ETH-40G, <PERSON><PERSON>@TTP@ODU2E, SIP@CTP@ODU2E+ETH-10G, NE<PERSON>@TTP@ODU2, SIP@CTP@ODU2+STM-64, NEP@TTP@ODU2"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": "mux", "plugtype": "QSFP28/112G/AOC/.*, QSFP28/112G/DAC/.*", "portid": "C1[01245689]|C20", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODUF07, SIP@CTP@ODUF07+FC-8G, NEP@TTP@ODUF11, SIP@CTP@ODUF11+FC-16G, NEP@TTP@ODU2E, SIP@CTP@ODU2E+ETH-10G, <PERSON><PERSON>@TTP@ODU2, SIP@CTP@ODU2+STM-64, NEP@TTP@ODU2"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU2", "values": "NEP@CTP@STM-64"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU2E", "values": "NEP@CTP@ETH-10G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUF07", "values": "NEP@CTP@FC-8G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUF11", "values": "NEP@CTP@FC-16G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU3", "values": "NEP@CTP@ETH-40G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "MA-B2C3LT-A", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU4", "values": "NEP@CTP@ETH-100G"}]