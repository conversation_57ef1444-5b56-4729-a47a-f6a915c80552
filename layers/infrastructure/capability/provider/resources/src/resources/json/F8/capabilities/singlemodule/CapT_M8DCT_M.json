[{"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+OTUC4PA, NEP+SIP@TTCTP@OTSiMC+OTUC5PA, NEP+SIP@TTCTP@OTSiMC+OTUC6PA, NEP+SIP@TTCTP@OTSiMC+OTUC7PA, NEP+SIP@TTCTP@OTSiMC+OTUC8PA"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.300:196.100@0.00625 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP, SYNC"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "6.5.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.275:196.100@0.00625 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP, SYNC"}, {"parameter": "Channel Bandwidth", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "100.0, 112.5, 125.0, 150.0@GHz", "multiplier": 1000, "default": "150.0 GHz", "tag": "OCAP, SYNC"}, {"parameter": "Setpoint", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "-10.0:3.5@0.1dBm", "multiplier": 10, "default": "3.0dBm", "tag": "ECAP"}, {"parameter": "Setpoint", "elementtype": "FSP 3000C", "version": "6.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "-10.0:4.8@0.1dBm", "multiplier": 10, "default": "3.0dBm", "tag": "ECAP"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC4PA", "values": "QPSK, QPSK-P16QAM, P16QAM, P16QAM-16QAM, 16QAM", "default": "P16QAM", "tag": "ECAP, SYNC"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC5PA", "values": "QPSK-P16QAM, P16QAM, P16QAM-16QAM, 16QAM, 16QAM-32QAM", "default": "P16QAM", "tag": "ECAP, SYNC"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC6PA", "values": "QPSK-P16QAM, P16QAM, P16QAM-16QAM, 16QAM, 16QAM-32QAM", "default": "P16QAM-16QAM", "tag": "ECAP, SYNC"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC7PA", "values": "P16QAM-16QAM, 16QAM, 16QAM-32QAM, 32QAM", "default": "16QAM", "tag": "ECAP, SYNC"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC8PA", "values": "P16QAM-16QAM, 16QAM, 16QAM-32QAM, 32QAM", "default": "16QAM-32QAM", "tag": "ECAP, SYNC"}, {"parameter": "Bits Per Symbol", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC4PA", "values": "2.0:4.0@0.001", "default": "3.0", "tag": "ECAP, SYNC"}, {"parameter": "Bits Per Symbol", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC5PA", "values": "2.196:5.0@0.001", "default": "3.0", "tag": "ECAP, SYNC"}, {"parameter": "Bits Per Symbol", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC6PA", "values": "3.0:4.993@0.001", "default": "3.281", "tag": "ECAP, SYNC"}, {"parameter": "Bits Per Symbol", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC7PA", "values": "3.086:5.0@0.001", "default": "4.0", "tag": "ECAP, SYNC"}, {"parameter": "Bits Per Symbol", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC8PA", "values": "3.508:5.0@0.001", "default": "4.547", "tag": "ECAP, SYNC"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC[4-8]PA", "values": "FEC-A-27i7, FEC-A-15i7", "default": "FEC-A-27i7", "tag": "ECAP, SYNC"}, {"parameter": "<PERSON><PERSON>", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC[4-8]PA", "values": "0.004:1.0@0.001", "default": "0.1", "tag": "ECAP, SYNC"}, {"parameter": "<PERSON><PERSON>", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC[4-8]PA", "values": "RRCOSNE, RCOSNE", "default": "RRCOSNE", "tag": "ECAP, SYNC"}, {"parameter": "Polarization Tracking", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC[4-8]PA", "values": "Normal, Fast", "default": "Normal", "tag": "ECAP, SYNC"}, {"parameter": "CD Post Compensation", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTUC[4-8]PA", "values": "CD Range 1, CD Range 2, CD Range 3, CD Range 4", "default": "CD Range 2", "tag": "ECAP, SYNC"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTUC4PA", "values": "NEP+SIP@TTCTP@ODUC4PA"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTUC5PA", "values": "NEP+SIP@TTCTP@ODUC5PA"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTUC6PA", "values": "NEP+SIP@TTCTP@ODUC6PA"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTUC7PA", "values": "NEP+SIP@TTCTP@ODUC7PA"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTUC8PA", "values": "NEP+SIP@TTCTP@ODUC8PA"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUC4PA", "values": "NEP@CTP@ODU4[1:4], NEP@CTP@ODUF320"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUC5PA", "values": "NEP@CTP@ODU4[1:5], NEP@CTP@ODUF320"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUC6PA", "values": "NEP@CTP@ODU4[1:6], NEP@CTP@ODUF320"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUC7PA", "values": "NEP@CTP@ODU4[1:7], NEP@CTP@ODUF320"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "Integrated", "portid": "N", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUC8PA", "values": "NEP@CTP@ODU4[1:8], NEP@CTP@ODUF320[1:2]"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28-NONAPPROVED, QSFP28/112G/LR4/.*, QSFP28/112G/SR4/.*, QSFP28/112G/ER4F/.*, QSFP28/112G/PSM4/.*, QSFP28/112G/AOC/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+OTU4"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+ET100ZR"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/425G/ZR/SM/LC", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+ET400ZR"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/425G/OZR+/SM/LC, QSFP56-DD/448G/OZRE/SM/LC", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP+SIP@TTCTP@OTSiMC+ET400OZRP"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28-NONAPPROVED, QSFP28/112G/LR4/.*, QSFP28/112G/SR4/.*, QSFP28/112G/ER4F/.*, QSFP28/112G/PSM4/.*, QSFP28/112G/AOC/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL+OTU4, SIP@CTP@ODU4"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL+ET100ZR"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/425G/ZR/SM/LC", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL+ET400ZR"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/425G/OZR+/SM/LC, QSFP56-DD/448G/OZRE/SM/LC", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL+ET400OZRP"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_PORT", "values": "NEP@TTCTP@OPTICAL"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.250:196.100@0.00625 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP, SYNC"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "191.250:196.100@0.00625 THz", "multiplier": 1000000, "default": "196.000 THz", "tag": "ECAP"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/425G/ZR/SM/LC", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.375:196.100@0.075 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP, SYNC"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/425G/ZR/SM/LC", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "191.375:196.100@0.075 THz", "multiplier": 1000000, "default": "196.000 THz", "tag": "ECAP"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/425G/OZR+/SM/LC, QSFP56-DD/448G/OZRE/SM/LC", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "191.275:196.125@0.00625 THz", "multiplier": 1000000, "sysselect": "Y", "tag": "OCAP, SYNC"}, {"parameter": "Channel", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/425G/OZR+/SM/LC, QSFP56-DD/448G/OZRE/SM/LC", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "191.275:196.125@0.00625 THz", "multiplier": 1000000, "default": "196.000 THz", "tag": "ECAP"}, {"parameter": "Channel Bandwidth", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "37.5@GHz", "default": "37.5 GHz", "tag": "OCAP"}, {"parameter": "Channel Bandwidth", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/425G/ZR/SM/LC, QSFP56-DD/425G/OZR+/SM/LC, QSFP56-DD/448G/OZRE/SM/LC", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "75.0@GHz", "default": "75.0 GHz", "tag": "OCAP"}, {"parameter": "Setpoint", "elementtype": "FSP 3000C", "version": "7.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/448G/OZRE/SM/LC", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC", "values": "-9.0:1.0@0.1dBm", "default": "0.0dBm", "tag": "ECAP"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ET100ZR", "values": "QPSK", "default": "QPSK", "tag": "ECAP, SYNC"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/425G/ZR/SM/LC", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ET400ZR", "values": "16QAM", "default": "16QAM", "tag": "ECAP, SYNC"}, {"parameter": "Modulation", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/425G/OZR+/SM/LC, QSFP56-DD/448G/OZRE/SM/LC", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ET400OZRP", "values": "16QAM", "default": "16QAM", "tag": "ECAP, SYNC"}, {"parameter": "Error Forwarding Mode", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD-NONAPPROVED, QSFP56-DD/425G/.*,QSFP56-DD/448G/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "AIS, <PERSON><PERSON> off immediate, <PERSON><PERSON> off delayed", "default": "Laser off immediate", "tag": "ECAP"}, {"parameter": "Error Forwarding Mode", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28-NONAPPROVED, QSFP28/103G/.*, QSFP28/106G/.*, QSFP28/10X10G/.*, QSFP28/112G/LR4/.*, QSFP28/112G/SR4/.*, QSFP28/112G/ER4F/.*, QSFP28/112G/PSM4/.*, QSFP28/112G/AOC/.*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC|OPTICAL", "values": "AIS, <PERSON><PERSON> off immediate, <PERSON><PERSON> off delayed", "default": "Laser off immediate", "tag": "ECAP"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+OTU4", "values": "NEP@CTP@ODU4"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+ET100ZR", "values": "NEP@CTP@ETH-100G"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/425G/ZR/SM/LC", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+ET400ZR", "values": "NEP@CTP@ETH-400G"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/425G/OZR+/SM/LC, QSFP56-DD/448G/OZRE/SM/LC", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTSiMC+ET400OZRP", "values": "NEP@CTP@ETH-400G"}, {"parameter": "SEP-ENNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL+OTU4", "values": "NEP@CTP@ODU4"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/10X10G/.*", "portid": "C[1-8]-[1-9]|C[1-8]-10", "lpqterm": "TTP", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-10G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28-NONAPPROVED, QSFP28/103G/.*, QSFP28/106G/.*, QSFP28/10X10G/850I/MM/MPO, QSFP28/112G/LR4/.*, QSFP28/112G/SR4/.*, QSFP28/112G/ER4F/.*, QSFP28/112G/PSM4/.*, QSFP28/112G/AOC/.*", "portid": "C[1-8]", "lpqterm": "TTP", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD-NONAPPROVED, QSFP56-DD/425G/.*", "portid": "C1|C5", "lpqterm": "TTP", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL", "values": "NEP+SIP@CTP@ETH-400G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL+ET100ZR", "values": "NEP+SIP@CTP@ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/425G/ZR/SM/LC", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL+ET400ZR", "values": "NEP+SIP@CTP@ETH-400G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/425G/OZR+/SM/LC, QSFP56-DD/448G/OZRE/SM/LC", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OPTICAL+ET400OZRP", "values": "NEP+SIP@CTP@ETH-400G"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-400G", "values": "RSFEC-544", "default": "RSFEC-544", "tag": "ECAP"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/10X10G/.*, QSFP28/103G/SR-BD/MM/LC", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-100G", "values": "None", "default": "None", "tag": "ECAP"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/106G/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-100G", "values": "RSFEC-544", "default": "RSFEC-544", "tag": "ECAP"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-100G", "values": "None, RSFEC-2", "default": "RSFEC-2", "tag": "ECAP"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTU4", "values": "None, GFEC", "default": "GFEC", "tag": "ECAP, SYNC"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/AOC/.*,QSFP56-DD/425G/AOC/.*", "portid": "C.*", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "OTU4|ETH-100G|ETH-400G", "values": "None", "default": "None", "tag": "ECAP, SYNC"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ET100ZR", "values": "SCFEC", "default": "SCFEC", "tag": "ECAP, SYNC"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/425G/ZR/SM/LC", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ET400ZR", "values": "CFEC", "default": "CFEC", "tag": "ECAP, SYNC"}, {"parameter": "Forward Error Correction", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ET400OZRP", "values": "OFEC", "default": "OFEC", "tag": "ECAP, SYNC"}, {"parameter": "Maintenance Signal", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-100G|ETH-400G", "values": "Local Fault, Idle", "default": "Local Fault", "tag": "ECAP"}, {"parameter": "Maintenance Signal", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD/425G/ZR/SM/LC, QSFP56-DD/425G/OZR+/SM/LC, QSFP56-DD/448G/OZRE/SM/LC, QSFP28/112G/ZR+/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ETH-100G|ETH-400G", "values": "Local Fault, Idle", "default": "Local Fault", "tag": "HIDE"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/10X10G/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU4, SIP@CTP@ODU4+ETH-10G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28-NONAPPROVED, QSFP28/112G/.*, QSFP28/106G/.*, QSFP28/103G/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU4, SIP@CTP@ODU4+ETH-100G"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODU4, SIP@CTP@ODU4+ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP56-DD-NONAPPROVED, QSFP56-DD/425G/.*,QSFP56-DD/448G/.*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "_FLOAT", "values": "NEP@TTP@ODUF320, SIP@CTP@ODUF320+ETH-400G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C1|C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUF320", "values": "NEP@CTP@ETH-400G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28-NONAPPROVED, QSFP28/112G/.*, QSFP28/106G/.*, QSFP28/103G/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU4", "values": "NEP@CTP@ETH-100G"}, {"parameter": "SEP-NNI", "elementtype": "FSP 3000C", "version": "6.5.2", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/112G/ZR+/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU4", "values": "NEP@CTP@ETH-100G"}, {"parameter": "SEP-UNI", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": "QSFP28/10X10G/.*", "portid": "C[1-8]", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU4", "values": "NEP@CTP@ETH-10G[1:10]"}, {"parameter": "Fix Cross Connect", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C1", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU4", "values": "N/ODU4[1]"}, {"parameter": "Fix Cross Connect", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C2", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU4", "values": "N/ODU4[2]"}, {"parameter": "Fix Cross Connect", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C3", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU4", "values": "N/ODU4[3]"}, {"parameter": "Fix Cross Connect", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C4", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU4", "values": "N/ODU4[4]"}, {"parameter": "Fix Cross Connect", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU4", "values": "N/ODU4[5]"}, {"parameter": "Fix Cross Connect", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C6", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU4", "values": "N/ODU4[6]"}, {"parameter": "Fix Cross Connect", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C7", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU4", "values": "N/ODU4[7]"}, {"parameter": "Fix Cross Connect", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C8", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODU4", "values": "N/ODU4[8]"}, {"parameter": "Fix Cross Connect", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C1", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUF320", "values": "N/ODUF320[1]"}, {"parameter": "Fix Cross Connect", "elementtype": "FSP 3000C", "version": "5.1.1", "moduletype": "T-MP-M8DCT", "modulecap": ".*", "modulemode": ".*", "plugtype": ".*", "portid": "C5", "lpqterm": ".*", "payloadtype": ".*", "physicalpq": ".*", "lpq": "ODUF320", "values": "N/ODUF320[2]"}]