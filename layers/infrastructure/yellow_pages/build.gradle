/*
 *  Copyright 2024 Adtran Networks SE. All rights reserved.
 */
import com.adva.gradle.jarexec.JarExec
import org.gradle.work.NormalizeLineEndings
import org.slf4j.LoggerFactory

import javax.inject.Inject

plugins {
    id 'com.adva.gradle.plugin.aspectj-weaver'
}

aspectjWeave {
    if (briefOutput) {
        lintLevel = 'ignore'
    }
}

def genDir = file( "$buildDir/generatedSources" ).getCanonicalPath()

sourceSets {

    main {
        java {
            srcDir genDir
            exclude 'com/adva/nlms/common/**/build.xml'
        }

        resources {
            // TXT, JSON, and XSLT files have not been included in jar traditionally, should they be?
            exclude 'com/adva/nlms/common/**/*.json'
            exclude 'com/adva/nlms/common/**/*.xslt'
            exclude 'com/adva/nlms/common/**/*.txt'
        }
    }
}

configurations {
    saxon
}

dependencies {
    implementation libs.aspectjrt
    implementation libs.enc.ypdb.commons
    implementation libs.log4j.api
    implementation libs.jgoodies.common
    implementation libs.jgoodies.binding
    implementation libs.jakarta.annotation.api
    implementation libs.enc.ypdb.commons
    implementation modep( mod_ypdb_bridge )

    implementation modep(mod_apps_sm_api)
    implementation modep(mod_enc_utils)
    implementation modep(mod_rest_api)

    testImplementation libs.bundles.junit
    testImplementation libs.spring.test
    testImplementation libs.spring.beans
    testImplementation libs.spring.aop
    testImplementation libs.log4j.core
    testImplementation modep(mod_mediation)
    testRuntimeOnly modep( mod_ypdb_bridge )

    saxon libs.saxonsa

    // Use yellow page aspect for database access
    aspectjpath modep(mod_ypdb_bridge)
}


ext.saxonWrapper = new JarExec(project)
        .setMain("net.sf.saxon.Transform")
        .setClasspath(configurations.saxon)

// Eventually the source paths may be relative to this directory
def commonPath = "com/adva/nlms/common"
def commonRes = file("src/main/resources/$commonPath").getCanonicalPath()
def commonGen = "$genDir/$commonPath"


// Generate sources for F7 AlarmMap and EventMap
task generateF7EventAlarmXml (type: SaxonTask) {
    group('enc.build')
    description('Generate F7 event and alarm xml resources for ENC')

    dependsOn(saxonWrapper.getJarTask())
    setBrief(briefOutput)
    setJarWrapper(saxonWrapper.getPathToWrapper())
    setInputFiles([new File("$commonRes/yp/dic/parameter_definition_new.xml"), new File("$commonRes/yp/dic/parameter_definition_new.xml")])
    setOutputFiles([new File("$commonGen/f7/alarms/AlarmMap_F7.xml"), new File("$commonGen/f7/events/NetworkEventMap_F7.xml")])
    setStyleFiles([new File("$commonRes/AlarmMap_F7.xsl"), new File("$commonRes/NetworkEventMap_F7.xsl")])
}

createSaxonTasks(tasks, commonRes, commonGen, compileJava)

def createSaxonTasks(tasks, commonRes, commonGen, parentTask) {
    def saxonProps = [
            [
                    // taskName: "generateDictionary",
                    input: "$commonRes/yp/dic/parameter_definition_new.xml",
                    output: "$commonGen/mib/Dictionary.java",
                    style: "$commonRes/yp/Entity2java.xsl",
            ],
            [
                    // taskName: "generateParamEnums",
                    input: "$commonRes/yp/dic/parameter_definition_new.xml",
                    output: "$commonGen/yp/ParameterEnums.java",
                    style: "$commonRes/yp/parameterEnum2java.xsl"
            ],
            [
                    // taskName: "generateParameters",
                    input: "$commonRes/yp/dic/parameter_definition_new.xml",
                    output: "$commonGen/yp/Parameters.java",
                    style: "$commonRes/yp/Parameter2java.xsl"
            ],
            [
                    // taskName: "generateChannelProvisionEnum",
                    input: "$commonRes/yp/dic/parameter_definition_new.xml",
                    output: "$commonGen/yp/ChannelProvisionEnum.java",
                    style: "$commonRes/yp/channelProvision.xsl",
            ],
    ]

    List inputFiles = new ArrayList()
    List outputFiles = new ArrayList()
    List styleFiles = new ArrayList()

    saxonProps.each {p ->
        inputFiles.add(new File(p.get('input')))
        outputFiles.add(new File(p.get('output')))
        styleFiles.add(new File(p.get('style')))
    }

    // Create a task that will run all saxon tasks in parallel
    Task saxonTask = tasks.create(name: "generateSaxon", type: SaxonTask) {
        setBrief(briefOutput)
        setJarWrapper(saxonWrapper.getPathToWrapper())
        setInputFiles(inputFiles)
        setInputFiles(inputFiles)
        setOutputFiles(outputFiles)
        setStyleFiles(styleFiles)
    }

    saxonTask.dependsOn(saxonWrapper.getJarTask())

    parentTask.dependsOn( 'generateSaxon' )

}

interface SaxonWorkParameters extends WorkParameters {
    Property<Boolean> getBriefOutput();
    RegularFileProperty getJarWrapper();
    RegularFileProperty getInputFile();
    RegularFileProperty getStyleFile();
    RegularFileProperty getOutputFile();
}

abstract class SaxonJob implements WorkAction<SaxonWorkParameters> {
    private final org.slf4j.Logger logger = LoggerFactory.getLogger(this.getClass())

    @Override
    void execute() {
        // Execute castor source generator, start by getting parameters
        boolean briefOutput = getParameters().getBriefOutput().get()
        File jarWrapper = getParameters().getJarWrapper().getAsFile().get()
        File inputFile = getParameters().getInputFile().getAsFile().get()
        File outputFile = getParameters().getOutputFile().getAsFile().get()
        File styleFile = getParameters().getStyleFile().getAsFile().get()

        String jarWrapperPath = jarWrapper.getCanonicalPath()
        String inputFilePath = inputFile.getCanonicalPath()
        String outputFilePath = outputFile.getCanonicalPath()
        String styleFilePath = styleFile.getCanonicalPath()

        String[] cmd = [ "java", "-jar", jarWrapperPath,
                         "-o", outputFilePath, inputFilePath, styleFilePath ]

        ProcessBuilder pb = new ProcessBuilder( cmd )
        pb.redirectErrorStream(true)
        Process process = pb.start()

        int result = process.waitFor()
        String output = process.text

        if (result == 0) {
            if (!briefOutput) {
                logger.quiet("Saxon generation complete for " + outputFile.name +
                        ": " + output)
            }
        } else {
            logger.error( "Saxon generation failed for " + outputFile.name +
                    ": " + output )
        }
    }
}
@CacheableTask
abstract class SaxonTask extends DefaultTask {
    boolean brief = false
    @NormalizeLineEndings
    @InputFile
    @PathSensitive(PathSensitivity.ABSOLUTE)
    private File jarWrapper
    private List<File> inputFiles = new ArrayList<>()
    private List<File> outputFiles = new ArrayList<>()
    private List<File> styleFiles = new ArrayList<>()

    File getJarWrapper() {
        return jarWrapper
    }

    void setJarWrapper(File jarWrapper) {
        this.jarWrapper = jarWrapper
    }

    @Input
    boolean getBrief() {
        return brief
    }

    boolean setBrief(boolean brief) {
        this.brief = brief
    }

    @NormalizeLineEndings
    @InputFiles
    @PathSensitive(PathSensitivity.RELATIVE)
    List<File> getInputFiles() {
        return inputFiles
    }

    void setInputFiles(List<File> inputFiles) {
        this.inputFiles = inputFiles
    }

    @OutputFiles
    List<File> getOutputFiles() {
        return outputFiles
    }

    void setOutputFiles(List<File> outputFiles) {
        this.outputFiles = outputFiles
    }

    @NormalizeLineEndings
    @InputFiles
    @PathSensitive(PathSensitivity.RELATIVE)
    List<File> getStyleFiles() {
        return styleFiles
    }

    void setStyleFiles(List<File> styleFiles) {
        this.styleFiles = styleFiles
    }

    @TaskAction
    void execute() {
        // update all outputs
        WorkQueue workQueue = getWorkerExecutor().noIsolation()
        for (int i=0; i<inputFiles.size(); i++) {
            runSaxon(workQueue, i)
        }
    }

    @Inject
    abstract WorkerExecutor getWorkerExecutor();

    void runSaxon(WorkQueue workQueue, int index) {
        File wrapper = jarWrapper
        File iFile = inputFiles.get(index)
        File oFile = outputFiles.get(index)
        File sFile = styleFiles.get(index)

        workQueue.submit(SaxonJob.class, params -> {
            params.getBriefOutput().set(brief)
            params.getJarWrapper().set(wrapper)
            params.getInputFile().set(iFile)
            params.getOutputFile().set(oFile)
            params.getStyleFile().set(sFile)
        })
    }
}

// Working directory for yellow pages testing.
// All test runtime resources will be placed here.
// This directory isolates the test from resources being
// changed by tests in other modules that may run concurrently.
File testRoot = new File(buildDir, "test/standAlone")

task standAloneYpDbSetup(type: Copy) {
    dependsOn(':installYpDb')
    // Quiet gradle warnings asking for these dependencies.
    // Not really needed but the logic in gradle can't tell this is the case.
    if (buildPlatform) {
        dependsOn(':setupDir_db')
        dependsOn(':setupRootFiles')
    }

    // Needed for YellowPageDBTest
    from fileTree("$serverRoot/db/yp")

    into "$testRoot/db/yp"
}
task standAloneTestSetup() {
    dependsOn(standAloneYpDbSetup)

    doLast {
        // ensure var/log directory is created
        new File(testRoot, "var/log").mkdirs();
    }
}

test {
    useJUnitPlatform()
    dependsOn(standAloneTestSetup)
    dependsOn(processTestResources)

    // Tests assume the current directory is the project root, so set it here
    workingDir = testRoot

    testClassesDirs = sourceSets.test.output.classesDirs
    classpath = files("$buildDir/$buildChain.main.outputDir", "$buildDir/$buildChain.test.outputDir",
            sourceSets.main.output.resourcesDir, sourceSets.test.output.resourcesDir) +
            configurations.testRuntimeClasspath
}
