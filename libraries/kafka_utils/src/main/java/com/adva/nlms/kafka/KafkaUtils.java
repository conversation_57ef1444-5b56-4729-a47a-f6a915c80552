/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 * Owner: dimitriosl
 */

package com.adva.nlms.kafka;

import com.adva.nlms.common.property.FNMPropertyConstants;
import com.adva.nlms.common.property.FNMPropertyFactory;
import com.adva.nlms.common.rest.clusteraccess.ClusterAccessType;
import com.adva.nlms.mediation.common.persistence.AdvaKafkaPasswordHelper;
import com.adva.nlms.mediation.infrastructure.notification.tracing.api.in.TraceDefinitions;
import com.adva.nlms.kafka.ssl.TrustAllSslEngineFactory;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.config.SslConfigs;
import org.apache.kafka.common.security.plain.PlainLoginModule;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.apache.logging.log4j.util.Strings;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.kafka.support.serializer.JsonSerializer;

import java.util.HashMap;
import java.util.Map;

public class KafkaUtils {
  private KafkaUtils() {
  }

  public static Map<String, Object> modeBasedConsumerProperties() {

    String clusterHost = FNMPropertyFactory.getProperty(FNMPropertyConstants.ENC_CLUSTER_HOST, "");

    ClusterAccessType clusterAccessType = Strings.isBlank(clusterHost) // by default if not filled then mode is NONE
            ? ClusterAccessType.None
            : ClusterAccessType.getCurrent();

    String kafkaAddress = clusterAccessType == ClusterAccessType.None
            ? FNMPropertyFactory.getProperty(FNMPropertyConstants.EOD_KAFKA_ADDRESS, FNMPropertyConstants.EOD_KAFKA_ADDRESS_DEFAULT)
            : clusterHost + ":" + FNMPropertyFactory.getPropertyAsInt(
            FNMPropertyConstants.ENC_CLUSTER_KAFKA_PORT, FNMPropertyConstants.ENC_CLUSTER_KAFKA_PORT_DEFAULT);

    Map<String, Object> properties = new HashMap<>();
    properties.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaAddress);
    properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
    properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, JsonDeserializer.class);
    properties.put(ConsumerConfig.INTERCEPTOR_CLASSES_CONFIG, TraceDefinitions.CONSUMER_INTERCEPTOR);
    properties.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");

    properties.putAll(commonModeBasedProperties(clusterAccessType));

    return properties;
  }

  public static Map<String, Object> modeBasedProducerProperties() {

    String clusterHost = FNMPropertyFactory.getProperty(FNMPropertyConstants.ENC_CLUSTER_HOST, "");
    ClusterAccessType clusterAccessType = Strings.isBlank(clusterHost) // by default if not filled then mode is NONE
            ? ClusterAccessType.None
            : ClusterAccessType.getCurrent();

    String kafkaAddress = clusterAccessType == ClusterAccessType.None
            ? FNMPropertyFactory.getProperty(FNMPropertyConstants.EOD_KAFKA_ADDRESS, FNMPropertyConstants.EOD_KAFKA_ADDRESS_DEFAULT)
            : clusterHost + ":" + FNMPropertyFactory.getPropertyAsInt(
            FNMPropertyConstants.ENC_CLUSTER_KAFKA_PORT, FNMPropertyConstants.ENC_CLUSTER_KAFKA_PORT_DEFAULT);

    Map<String, Object> properties = new HashMap<>();
    properties.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaAddress);
    properties.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    properties.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
    properties.put(ProducerConfig.INTERCEPTOR_CLASSES_CONFIG, TraceDefinitions.PRODUCER_INTERCEPTOR);
    var kafkaBlockTimeout = FNMPropertyFactory.getPropertyAsInt(FNMPropertyConstants.KAFKA_MAX_BLOCK_MS_CONFIG, FNMPropertyConstants.KAFKA_MAX_BLOCK_MS_CONFIG_DEFAULT);
    properties.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, kafkaBlockTimeout);

    properties.putAll(commonModeBasedProperties(clusterAccessType));

    return properties;
  }

  private static Map<String, Object> commonModeBasedProperties(ClusterAccessType clusterAccessType) {
    Map<String, Object> properties = new HashMap<>();

    if (clusterAccessType != ClusterAccessType.None) {
      properties.put(SslConfigs.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG, "");

      var acceptCaCerts = FNMPropertyFactory.getPropertyAsBoolean(FNMPropertyConstants.KAFKA_ACCEPT_CA_CERTS, FNMPropertyConstants.KAFKA_ACCEPT_CA_CERTS_DEFAULT);

      if (!acceptCaCerts) {
        properties.put(KafkaConstants.CONFIG_SSL_FACTORY_CLASS, TrustAllSslEngineFactory.class.getName());
      } else {
        String permanentTrustStorePath = System.getProperty("javax.net.ssl.trustStore");
        String permanentTrustStorePassword = System.getProperty("javax.net.ssl.trustStorePassword");
        properties.put(SslConfigs.SSL_TRUSTSTORE_LOCATION_CONFIG, permanentTrustStorePath);
        properties.put(SslConfigs.SSL_TRUSTSTORE_PASSWORD_CONFIG, permanentTrustStorePassword);
        properties.put(SslConfigs.SSL_TRUSTSTORE_TYPE_CONFIG, "JKS");
      }
      properties.putAll(authProperties());
    }
    return properties;
  }

  private static Map<String, Object> authProperties() {
    Map<String, Object> properties = new HashMap<>();
    properties.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
    properties.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
    properties.put(SaslConfigs.SASL_JAAS_CONFIG, buildSaslJaasConfig());

    return properties;
  }

  private static String buildSaslJaasConfig() {
    AdvaKafkaPasswordHelper helper = new AdvaKafkaPasswordHelper();
    String password = helper.getKafkaPassword();
    String username = helper.getKafkaUsername();
    return String.format("%s required username=\"%s\" password=\"%s\";", PlainLoginModule.class.getName(), username, password);
  }
}
