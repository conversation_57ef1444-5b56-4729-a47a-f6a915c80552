/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 *
 * Owner: astec
 */

package com.adva.nlms.kafka.ssl;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.SSLParameters;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.util.Map;
import java.util.Set;

import org.apache.kafka.common.security.auth.SslEngineFactory;
public class TrustAllSslEngineFactory implements SslEngineFactory {

    private SSLContext sslContext;

    @Override
    public void configure(Map<String, ?> configs) {
        try {
            sslContext = SSLContext.getInstance("TLS");

            TrustManager[] trustAllCerts = new TrustManager[]{
                    new X509TrustManager() {
                        @Override
                        public java.security.cert.X509Certificate[] getAcceptedIssuers() { return new java.security.cert.X509Certificate[0]; }

                        @Override
                        @SuppressWarnings("java:S4830")
                        public void checkClientTrusted(java.security.cert.X509Certificate[] certs, String authType) {
                            //NOSONAR - no need to check anything here as we're skipping client validation
                        }

                        @Override
                        @SuppressWarnings("java:S4830")
                        public void checkServerTrusted(java.security.cert.X509Certificate[] certs, String authType) {
                            //NOSONAR - no need to check anything here as we're using this on client side
                        }
                    }
            };

            sslContext.init(null, trustAllCerts, new SecureRandom());
        } catch (GeneralSecurityException e) {
            throw new IllegalStateException("Failed to create SSLContext", e);
        }
    }

    @Override
    public SSLEngine createClientSslEngine(String peerHost, int peerPort, String endpointIdentification) {
        SSLEngine sslEngine = sslContext.createSSLEngine(peerHost, peerPort);
        sslEngine.setUseClientMode(true);

        SSLParameters sslParameters = sslEngine.getSSLParameters();
        sslParameters.setEndpointIdentificationAlgorithm(null);
        sslEngine.setSSLParameters(sslParameters);

        return sslEngine;
    }

    @Override
    public SSLEngine createServerSslEngine(String peerHost, int peerPort) {
        // Not used for client-side usage but required by interface
        throw new UnsupportedOperationException("Server SSL engine not supported in client mode.");
    }

    @Override
    public boolean shouldBeRebuilt(Map<String, Object> map) {
        return false;
    }

    @Override
    public Set<String> reconfigurableConfigs() {
        return Set.of();
    }

    @Override
    public KeyStore keystore() {
        return null;
    }

    @Override
    public KeyStore truststore() {
        return null;
    }

    @Override
    public void close() throws IOException {
        //NOSONAR - no need to clear any resources
    }
}
