/*
 *  Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 *  Owner: bwisniew
 */
package com.adva.nlms.kafka.admin;

import com.adva.nlms.common.property.FNMPropertyConstants;
import com.adva.nlms.common.property.FNMPropertyFactory;
import com.adva.nlms.kafka.KafkaConstants;
import com.adva.nlms.kafka.ssl.TrustAllSslEngineFactory;
import com.adva.nlms.mediation.common.persistence.AdvaKafkaPasswordHelper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.config.SslConfigs;
import org.apache.kafka.common.security.plain.PlainLoginModule;

import java.util.Collections;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

/**
 * Utility class for managing Kafka topics and admin operations.
 * Refactored from KafkaAdminForMNCLanuncher to provide better separation of concerns.
 */
@SuppressWarnings("all")
public class KafkaTopicManager {

    private static Logger logger = LogManager.getLogger(KafkaTopicManager.class);

    private static final String APPROVAL_TOPIC_NAME = "v1.cor.sm.approval";
    private static final int APPROVAL_TOPIC_PARTITIONS = 1;
    private static final short APPROVAL_TOPIC_REPLICATION_FACTOR = 1;
    private static final int TOPIC_CREATION_TIMEOUT_SECONDS = 10;
    private static final int DEFAULT_RETRY_DELAY_SECONDS = 30;

    private KafkaTopicManager() {
        // Utility class - prevent instantiation
    }

    /**
     * Creates Kafka topics with retry logic if EOD evolution is enabled.
     * This method handles the creation of the approval topic required for MNC operations.
     */
    public static void createRequiredTopicsWithRetry() {
        if (!isEodEvolutionEnabled()) {
            return;
        }

        Properties adminProperties = createAdminClientProperties();
        int maxRetries = getMaxRetries();
        int retryDelaySeconds = DEFAULT_RETRY_DELAY_SECONDS;

        logger.info("Trying to create Kafka topic...");
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            logger.info("Attempt {} of {} to create kafka topics.", attempt, maxRetries);

            if (attemptTopicCreation(adminProperties, attempt, maxRetries)) {
                return; // Success or topic already exists
            }

            if (attempt < maxRetries) {
                waitBeforeRetry(retryDelaySeconds, attempt);
            }
        }
    }

    /**
     * Creates admin client properties based on cluster configuration.
     */
    private static Properties createAdminClientProperties() {
        Properties props = new Properties();

        if (isSwarmCluster()) {
            configureSwarmClusterProperties(props);
        } else {
            configureLocalKafkaProperties(props);
        }

        return props;
    }

    /**
     * Configures properties for Swarm cluster mode with SSL and authentication.
     */
    private static void configureSwarmClusterProperties(Properties props) {
        String kafkaAddress = buildSwarmKafkaAddress();
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaAddress);

        configureAuthentication(props);
        configureSslSettings(props);
    }

    /**
     * Configures authentication properties for Kafka admin client.
     */
    private static void configureAuthentication(Properties props) {
        AdvaKafkaPasswordHelper helper = new AdvaKafkaPasswordHelper();
        String password = helper.getKafkaPassword();
        String username = helper.getKafkaUsername();

        props.put(AdminClientConfig.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
        props.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
        props.put(SaslConfigs.SASL_JAAS_CONFIG, buildSaslJaasConfig(username, password));
    }

    /**
     * Configures SSL settings based on certificate acceptance policy.
     */
    private static void configureSslSettings(Properties props) {
        props.put(SslConfigs.SSL_ENDPOINT_IDENTIFICATION_ALGORITHM_CONFIG, "");

        boolean acceptCaCerts = FNMPropertyFactory.getPropertyAsBoolean(
                FNMPropertyConstants.KAFKA_ACCEPT_CA_CERTS,
                FNMPropertyConstants.KAFKA_ACCEPT_CA_CERTS_DEFAULT
        );

        if (!acceptCaCerts) {
            props.put(KafkaConstants.CONFIG_SSL_FACTORY_CLASS, TrustAllSslEngineFactory.class.getName());
        } else {
            configureTrustStore(props);
        }
    }

    /**
     * Configures trust store settings for SSL.
     */
    private static void configureTrustStore(Properties props) {
        String trustStorePath = System.getProperty("javax.net.ssl.trustStore");
        String trustStorePassword = System.getProperty("javax.net.ssl.trustStorePassword");

        props.put(SslConfigs.SSL_TRUSTSTORE_LOCATION_CONFIG, trustStorePath);
        props.put(SslConfigs.SSL_TRUSTSTORE_PASSWORD_CONFIG, trustStorePassword);
        props.put(SslConfigs.SSL_TRUSTSTORE_TYPE_CONFIG, "JKS");
    }

    /**
     * Configures properties for local Kafka instance.
     */
    private static void configureLocalKafkaProperties(Properties props) {
        String localKafkaAddress = FNMPropertyFactory.getProperty(
                FNMPropertyConstants.LOCAL_KAFKA_ADDRESS,
                "localhost:9094"
        );
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, localKafkaAddress);
    }

    /**
     * Attempts to create the required Kafka topic.
     *
     * @param adminProperties Properties for admin client
     * @param attempt         Current attempt number
     * @param maxRetries      Maximum number of retries
     * @return true if successful or topic already exists, false if should retry
     */
    private static boolean attemptTopicCreation(Properties adminProperties, int attempt, int maxRetries) {
        try (AdminClient adminClient = AdminClient.create(adminProperties)) {
            NewTopic topic = new NewTopic(APPROVAL_TOPIC_NAME, APPROVAL_TOPIC_PARTITIONS, APPROVAL_TOPIC_REPLICATION_FACTOR);
            adminClient.createTopics(Collections.singletonList(topic))
                    .all()
                    .get(TOPIC_CREATION_TIMEOUT_SECONDS, TimeUnit.SECONDS);

            logger.info("Kafka topic created successfully.");
            return true;

        } catch (Exception e) {
            return handleTopicCreationException(e, attempt, maxRetries);
        }
    }

    /**
     * Handles exceptions during topic creation.
     */
    private static boolean handleTopicCreationException(Exception e, int attempt, int maxRetries) {
        String message = e.getMessage();

        if (message != null && message.contains("TopicExistsException")) {
            logger.info("Kafka topic already exists.");
            return true; // Topic already exists - success
        }

        logger.info("Attempt {} of {} to create kafka topics failed.", attempt, maxRetries);

        if (attempt == maxRetries) {
            logger.info("All retry attempts failed. Kafka broker is unreachable.");
        }

        return false; // Should retry
    }

    /**
     * Waits before retrying topic creation.
     */
    private static void waitBeforeRetry(int retryDelaySeconds, int attempt) {
        try {
            TimeUnit.SECONDS.sleep(retryDelaySeconds);
        } catch (InterruptedException ie) {
            Thread.currentThread().interrupt();
            logger.info("Retry interrupted.", ie);
        }
    }

    // Helper methods for configuration checks and property retrieval

    private static boolean isEodEvolutionEnabled() {
        return FNMPropertyFactory.getPropertyAsBoolean(FNMPropertyConstants.EOD_EVOLUTION_ENABLED, false);
    }

    private static boolean isSwarmCluster() {
        return "Swarm".equals(FNMPropertyFactory.getProperty(FNMPropertyConstants.ENC_CLUSTER_TYPE, ""));
    }

    private static String buildSwarmKafkaAddress() {
        String kafkaHost = FNMPropertyFactory.getProperty(FNMPropertyConstants.EOD_KAFKA_ADDRESS, "localhost");
        int kafkaPort = FNMPropertyFactory.getPropertyAsInt(
                FNMPropertyConstants.ENC_CLUSTER_KAFKA_PORT,
                FNMPropertyConstants.ENC_CLUSTER_KAFKA_PORT_DEFAULT
        );
        return kafkaHost + ":" + kafkaPort;
    }

    private static String buildSaslJaasConfig(String username, String password) {
        return String.format(
                "%s required username=\"%s\" password=\"%s\";",
                PlainLoginModule.class.getName(),
                username,
                password
        );
    }

    private static int getMaxRetries() {
        return FNMPropertyFactory.getPropertyAsInt(FNMPropertyConstants.MAX_RETRIES_FOR_KAFKA_CONNECTION, 10);
    }
}
