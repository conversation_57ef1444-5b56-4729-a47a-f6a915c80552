/*
 * Copyright 2025 Adtran Networks SE. All rights reserved.
 *
 * Owner: astec
 */

plugins {
    id 'java-library'
}

setupModule(project)

dependencies {
    // Internal dependencies
    implementation modep(mod_property)
    implementation modep(mod_rest_infra)
    implementation modep(mod_persistence_common)
    implementation modep(mod_notification_tracing_api)

    // External dependencies - Kafka
    implementation libs.spring.kafka

    // External dependencies - Logging
    implementation libs.log4j.api

    // Test dependencies
    testImplementation libs.bundles.junit
    testImplementation libs.mockito.core
}

test {
    useJUnitPlatform()
}
