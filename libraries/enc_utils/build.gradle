/*
 * Copyright 2024 Adtran Networks SE. All rights reserved.
 */

plugins {
    id 'com.adva.gradle.plugin.aspectj-weaver'
}

dependencies {
    implementation libs.log4j.api
    implementation libs.log4j.core
    implementation libs.aspectjrt
    implementation libs.metrics.core
    implementation libs.guava
    // It was decided along with the SDAs that this library can use the mod_property module,
    // in order to support SecureRandom initialization tweaks (using /dev/urandom instead /dev/random)
    implementation modep( mod_property )
    testImplementation libs.bundles.junit.jupiter
}

aspectjWeave {
    if (briefOutput) {
        lintLevel = 'ignore'
    }
}
