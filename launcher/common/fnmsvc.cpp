//#include <windows.h>
#include <afx.h>
#include <jni.h>
#include "nt_svc.h"
#include "fnmsvc.h"
#include <process.h>
//#include <winreg.h>
#include <direct.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

#define KEY_ADVA_NMS  "SOFTWARE\\ADVA Optical Networking\\FSP Network Manager"
#define KEY_INSTDIR   "InstallDir"

char java_with_path[MAX_PATH];
ServerType serverType = ADVASERVERTYPE;

void OpenLogFile(const bool isShutdown);
int do_fnmsvc(ServerType serverType);

extern "C"
{
  jboolean GetApplicationHome(char *buf, jint bufsize);
  void RunJavaApplication(void *pargs);
  void CallMediationShutdownMethod();
  extern FILE *logfile;
}


HANDLE  hServerStopEvent = NULL;

VOID ServiceStart (DWORD dwArgc, LPTSTR *lpszArgv)
{
  HANDLE hEvents[2] = {NULL, NULL};

  ///////////////////////////////////////////////////
  //
  // Service initialization
  //

  // report the status to the service control manager.
  //
  if (!ReportStatusToSCMgr(
        SERVICE_START_PENDING, // service state
        NO_ERROR,              // exit code
        3000)) {               // wait hint

    if (hServerStopEvent)
        CloseHandle(hServerStopEvent);

    if (hEvents[1]) // overlapped i/o event
        CloseHandle(hEvents[1]);

    return;
  }

  // create the event object. The control handler function signals
  // this event when it receives the "stop" control code.
  //
  hServerStopEvent = CreateEvent(
      NULL,    // no security attributes
      TRUE,    // manual reset event
      FALSE,   // not-signalled
      NULL);   // no name

  if ( hServerStopEvent == NULL) {

    if (hServerStopEvent)
        CloseHandle(hServerStopEvent);

    if (hEvents[1]) // overlapped i/o event
        CloseHandle(hEvents[1]);

    return;

  }

  hEvents[0] = hServerStopEvent;

  // report the status to the service control manager.
  //
  if (!ReportStatusToSCMgr(
      SERVICE_START_PENDING, // service state
      NO_ERROR,              // exit code
      3000)) {               // wait hint

    if (hServerStopEvent)
        CloseHandle(hServerStopEvent);

    if (hEvents[1]) // overlapped i/o event
        CloseHandle(hEvents[1]);

    return;
  }

  // create the event object object use in overlapped i/o
  //
  hEvents[1] = CreateEvent(
      NULL,    // no security attributes
      TRUE,    // manual reset event
      FALSE,   // not-signalled
      NULL);   // no name

  if ( hEvents[1] == NULL) {

    if (hServerStopEvent)
        CloseHandle(hServerStopEvent);

    if (hEvents[1]) // overlapped i/o event
        CloseHandle(hEvents[1]);

    return;
}

  // report the status to the service control manager.
  //
  if (!ReportStatusToSCMgr(
    SERVICE_RUNNING,       // service state
    NO_ERROR,              // exit code
    0)) {                  // wait hint

    if (hServerStopEvent)
        CloseHandle(hServerStopEvent);

    if (hEvents[1]) // overlapped i/o event
        CloseHandle(hEvents[1]);

    return;
  }

  OpenLogFile(false);

  int error = do_fnmsvc(serverType);

  WaitForSingleObject(hServerStopEvent, INFINITE);

  if (serverType != MediationServer)
  {
    Sleep(2000);  // Give Mediation Server some time to notify clients
  }

  // report the status to the service control manager.
  //
  if (!ReportStatusToSCMgr(
      SERVICE_STOPPED      , // service state
      error,                 // exit code
      3000)) {               // wait hint

    if (hServerStopEvent)
        CloseHandle(hServerStopEvent);

    if (hEvents[1]) // overlapped i/o event
        CloseHandle(hEvents[1]);

    return;
  }

}

VOID ServiceStop()
{

  OpenLogFile(true);

  CallMediationShutdownMethod();

  if ( hServerStopEvent )
    SetEvent(hServerStopEvent);

}

void OpenLogFile(const bool isShutdown) {

  LPTSTR tmpPath = new char[MAX_PATH+1];
  GetTempPath(MAX_PATH, tmpPath);
  CString logFilePath = CString(tmpPath);

  switch (serverType) {
  case NameServer:
    logFilePath += "NameServer";
    break;
  case EventServer:
    logFilePath += "EventServer";
    break;
  case SNMPServer:
    logFilePath += "SNMPServer";
    break;
  case MediationServer:
    logFilePath += "MediationServer";
    break;
  case AlarmServer:
    logFilePath += "AlarmServer";
    break;
  case KeyServer:
    logFilePath += "KeyServer";
    break;
  }

  if (isShutdown)
    logFilePath += "Stop";

  logFilePath += ".log";

  logfile = fopen((LPCTSTR)logFilePath, "w+");
}


int do_fnmsvc(ServerType serverType) {

  static CString cpath = CString("");
  static CString cpath_snmp = CString("");
  CString instDir;
  CString odb_lock_dir;
  CFileFind odb_lock_dir_ff;

/*
  HKEY reg_key;
  DWORD key_type, key_value_len;
  BYTE key_value[MAX_KEY_LEN];

  unsigned long error;

  // Resolve installation path
  if (error = RegOpenKeyEx(HKEY_LOCAL_MACHINE,
			 KEY_ADVA_NMS,
			 0,
			 KEY_QUERY_VALUE,
			 &reg_key) != 0)
  {
    exit(1);
  }

  key_value_len = MAX_KEY_LEN;
  if ((error = RegQueryValueEx(reg_key,
  			 KEY_INSTDIR,
				 NULL,
				 &key_type,
				 key_value,
				 &key_value_len
         )) == ERROR_SUCCESS) {

      instDir = key_value;

  }
  else
  {
    exit(1);
  }

  RegCloseKey(reg_key);
*/    
  char buf[MAX_PATH - 1];
  GetApplicationHome(buf, MAX_PATH);


//  if (_chdir(instDir) != 0)
  if (_chdir(buf) != 0)
  {
    exit(1);
  }

  // Check for database lock
  if (serverType == MediationServer)
  {
    odb_lock_dir = "var\\odb\\meddev.odx";
  
    // If lock directory found, remove it
    if (odb_lock_dir_ff.FindFile((LPCTSTR)odb_lock_dir))
    {
      CString odblock_errmsg;
      CString odb_lock_file;
    
//      ((CFnmsvcDlg *)param)->m_log.AddString("WARNING: Database was not shutdown properly.");

      odb_lock_dir_ff.Close();

      // Remove all files in the lock directory
      CFileFind odb_lock_file_ff;
      odb_lock_file = odb_lock_dir + "\\*.*";
      if (odb_lock_file_ff.FindFile(odb_lock_file))
      {
        // Process each file in the lock directory
        while (odb_lock_file_ff.FindNextFile())
        {
	        odb_lock_file = odb_lock_file_ff.GetFilePath();

          // Skip current and parent directory
          if ((odb_lock_file_ff.GetFileName() == ".") ||
              (odb_lock_file_ff.GetFileName() == ".."))
            {
	            continue;
            }

          // Remove files
          if (DeleteFile((LPCTSTR)odb_lock_file) == FALSE)
          {
         	  CString errmsg;

	          errmsg = "Could not remove lock file " +
		                 odb_lock_file +
		                 " (" +
		                 strerror(GetLastError()) +
		                 ").";
		
	          exit(1);
          }

        }
        odb_lock_file_ff.Close();
	
        // Remove lock directory
        if (RemoveDirectory((LPCTSTR)odb_lock_dir) == FALSE)
        {
          CString errmsg;
	    
	        errmsg = "Could not remove lock directory " +
		               odb_lock_dir +
		               " (" +
		               strerror(GetLastError()) +
		               ").";
	    
	        exit(1);

        }

      }	// end of lock directory deletion
    }
  } // end of ODB lock check

  // Compile Java command components
  GetModuleFileName(NULL, java_with_path, MAX_PATH - 1);

  // Build classpath
  CFileFind findFile;
  
  BOOL isNextFile = findFile.FindFile("lib\\*.jar");
  while(isNextFile) {
    isNextFile = findFile.FindNextFile();
    cpath += ("lib\\" + findFile.GetFileName() + ";");
  }
  findFile.Close();

  static CString bootclasspathOption = CString("-Xbootclasspath/p:lib\\xml-apis.jar;lib\\log4j.jar;lib\\jdo-1.0.2.jar;lib\\OB.jar;");
  bootclasspathOption += cpath;
  
  fflush(logfile);
  
  // Start FSP Network Manager Servers
  int retval = 0;

  switch (serverType)
  {
  // CORBA Name Server (ORBacus)
  case NameServer:
    {
      static const char *nsArgv[] = {(const char*)java_with_path,
                                     "-Xms10M",
                                     "-Xmx500M",
                                     "-Xrs",
                                     "-classpath",
                                     (LPCTSTR)cpath,
                                     "com.ooc.CosNaming.Server",
                                     "-OAport",
                                     "33027",
                                     NULL
                                    };

      _beginthread(RunJavaApplication, 0, (void *)nsArgv);
    }
    break;

  // CORBA Event Server (ORBacus)
  case EventServer:
    {
      static const char *esArgv[] = {(const char*)java_with_path,
                                     "-Xms10M",
                                     "-Xmx500M",
                                     "-Xrs",
                                     "-Djava.endorsed.dirs=lib\\endorsed",
                                     "-classpath",
                                     (const char*)(LPCTSTR)cpath,
                                     "com.ooc.CosEvent.Server",
                                     "-ORBconfig",
                                     "event.config",
                                     "-OAport",
                                     "33028",
                                     NULL
                                    };
  
      _beginthread(RunJavaApplication, 0, (void *)esArgv);
    }
    break;

  // SNMP Provider Server (ADVA Oslo)
  case SNMPServer:
    {
      static const char *ssArgv[] = {(const char*)java_with_path,
                                     "-Xms10M",
                                     "-Xmx500M",
                                     "-Xrs",
                                     "-Djtrace.logdir=var\\log",
                                     "-classpath",
                                     (const char*)(LPCTSTR)cpath,
  	                                 "com.adva.snmpprovider.snmpserver.server.Server",
  	                                 "-proxy_port",
                                     "2544",
                                     "-trap_port",
                                     "2544",
                                     NULL
                                    };
  
      _beginthread(RunJavaApplication, 0, (void *)ssArgv);
    }
    break;

  // Mediation Device
  case MediationServer:
    {
      static const char *msArgv[] = {(const char*)java_with_path,
                                     "-Xms50M",
                                     "-Xmx1000M",
                                     "-Xrs",
                                     "-Djava.endorsed.dirs=lib\\endorsed",
                                     "-Djtrace.logdir=var\\log",
                                     "-classpath",
                                     (const char*)(LPCTSTR)cpath,
                                     "com.adva.nlms.mediation.Launcher",
                                     NULL
                                    };
  
      _beginthread(RunJavaApplication, 0, (void *)msArgv);
    }
    break;

  // Alarm Handler
  case AlarmServer:
    {
      static const char *asArgv[] = {(const char*)java_with_path,
                                     "-Xms10M",
                                     "-Xmx500M",
                                     "-Xrs",
                                     "-Djtrace.logdir=var\\log",
                                     "-classpath",
                                     (const char*)(LPCTSTR)cpath,
  	                                 "com.adva.nlms.eventcorrel.Launcher",
                                     NULL
                                    };
  
      _beginthread(RunJavaApplication, 0, (void *)asArgv);
    }
    break;
  
  // Key Generation Server
  case KeyServer:
    {
      static const char *ksArgv[] = {(const char*)java_with_path,
                                     "-Xrs",
                                     "-Dcatalina.base=.",
                                     "-Dcatalina.home=." ,
                                     "-classpath",
                                     "server\\lib\\bootstrap.jar;common\\lib\\servlet.jar;common\\lib\\tools.jar",
  	                                 "org.apache.catalina.startup.Bootstrap",
                                     "start",
                                     NULL
                                    };
  
      _beginthread(RunJavaApplication, 0, (void *)ksArgv);
    }
    break;
  }

  fclose(logfile);

  return(retval);

}

/////////////////////////////////////////////////////////////////////////////
// do_exit - This function terminates the server

/*void do_exit() {
  
  exit(0);
}*/

