// fnmsvc.h : Haupt-Header-Datei f�r die Anwendung FNMSVC
//

#if !defined(AFX_FNMSVC_H__1E40F380_FBE0_11D3_B8A5_0050DA45ACBC__INCLUDED_)
#define AFX_FNMSVC_H__1E40F380_FBE0_11D3_B8A5_0050DA45ACBC__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#define MAX_KEY_LEN   128

#define KEY_CLASSES   "SOFTWARE\\Classes\\"
#define KEY_HTML      "SOFTWARE\\Classes\\.html"
#define KEY_OPEN      "\\shell\\open\\command"

typedef enum
{
  NameServer,
  EventServer,
  SNMPServer,
  MediationServer,
  AlarmServer,
  KeyServer
} ServerType;

void do_exit();


#endif // !defined(AFX_FNMSVC_H__1E40F380_FBE0_11D3_B8A5_0050<PERSON><PERSON><PERSON>BC__INCLUDED_)
