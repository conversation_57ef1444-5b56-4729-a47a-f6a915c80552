// FemCommandLineInfo.h: Schnittstelle f�r die Klasse FemCommandLineInfo.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_FEMCOMMANDLINEINFO_H__B81FFDA4_62AE_4DA2_BB62_72AF1A60E436__INCLUDED_)
#define AFX_FEMCOMMANDLINEINFO_H__B81FFDA4_62AE_4DA2_BB62_72AF1A60E436__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include <afxwin.h>

class FemCommandLineInfo : public CCommandLineInfo  
{
public:
	FemCommandLineInfo();
	virtual ~FemCommandLineInfo();

  void ParseParam(LPCTSTR lpszParam, BO<PERSON> bFlag, BOOL bLast);

  char **getArgv();

private:
  char *argv[30];
  int argc;

};

#endif // !defined(AFX_FEMCOMMANDLINEINFO_H__B81FFDA4_62AE_4DA2_BB62_72AF1A60E436__INCLUDED_)
