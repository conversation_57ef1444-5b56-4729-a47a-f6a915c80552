// fem.cpp : Legt das Klassenverhalten f�r die Anwendung fest.
//

#include "stdafx.h"
#include "fem.h"
#include "femDlg.h"
#include "FemCommandLineInfo.h"
#include <process.h>
#include <winreg.h>
#include <direct.h>
//#include <OV/OVsnmpConf.h>


#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern "C"
{
  int java_main(int argc, char **argv);
  extern FILE *logfile;
}

unsigned long do_fem_thread;
int ps_ns;
char **argv;


/////////////////////////////////////////////////////////////////////////////
// getViewerFor - Get the path to the viewer for specified file extension
//                from the registry

CString getViewerFor(CString extension) {

  HKEY reg_key;
  DWORD key_type, key_value_len;
  BYTE key_value[MAX_KEY_LEN];
  LONG error;
  unsigned int i,j;
  key_value_len = MAX_KEY_LEN;
  static char viewer[MAX_KEY_LEN];

  CString keyForExtension = CString(KEY_CLASSES);
  keyForExtension += extension;

  strcpy(viewer, "none");

  error = RegOpenKeyEx(HKEY_LOCAL_MACHINE,
		       keyForExtension,
		       0,
		       KEY_QUERY_VALUE,
		       &reg_key);

  if ((error = RegQueryValueEx(reg_key,
		      NULL,
		      NULL,
		      &key_type,
		      key_value,
		      &key_value_len
		      )) == ERROR_SUCCESS) {

    CString key_open;

    key_open = KEY_CLASSES;
    key_open += (char *)key_value;
    key_open += KEY_OPEN;

    error = RegOpenKeyEx(HKEY_LOCAL_MACHINE,
			 (LPCTSTR)key_open,
			 0,
			 KEY_QUERY_VALUE,
			 &reg_key);

    key_value_len = MAX_KEY_LEN;
    if ((error = RegQueryValueEx(reg_key,
  		   NULL,
				 NULL,
				 &key_type,
				 key_value,
				 &key_value_len
				 )) == ERROR_SUCCESS) {

      BOOL is_escaped = FALSE;
      BOOL viewerFound = FALSE;

      // Use only program part (no argument placeholder)
      for ((i = 0, j = 0); key_value[i] != '\0'; i++) {
		    if (key_value[i] == '"') {
		      if (is_escaped) {
  			    viewer[j++] = key_value[i];
			      viewer[j] = '\0';
			      break;
          } else {
  			    viewer[j++] = key_value[i];
			      is_escaped = TRUE;
			      continue;
          }
        }

		    if ((key_value[i] == ' ') && viewerFound && !is_escaped) {
		      viewer[j] = '\0';
		      break;
        }

		    viewer[j++] = key_value[i];
		    viewerFound = TRUE;
      }
    }
    
  }
  CString retval = CString(viewer);
  return retval;
}


/////////////////////////////////////////////////////////////////////////////
// do_fem - This function performs the complete front end application
//		 management including pre launch checks, start of all
//		 processes and shutdown.

void do_fem(void *param) {

  CString java_with_path;
  CString java_options;
  CString logpath = CString("-Djtrace.logdir=");
  CString propertiespath = CString("-Dproperties.dir=");
  CString cpath = CString("");
  CString instDir;

  LPTSTR tmpPath = new char[MAX_PATH+1];
  GetTempPath(MAX_PATH, tmpPath);
  CString logFilePath = CString(tmpPath);
  logFilePath += "fem.log";
  logfile = fopen((LPCTSTR)logFilePath, "w+");

  // Resolve installation path
/*  if ((error = RegQueryValueEx(reg_key,
		      NULL,
		      NULL,
		      &key_type,
		      key_value,
		      &key_value_len
		      )) == ERROR_SUCCESS) {

    CString key_install_dir;

    key_install_dir = KEY_ADVA_NMS;
    key_install_dir += "\\";
    key_install_dir += KEY_INSTDIR;

    error = RegOpenKeyEx(HKEY_LOCAL_MACHINE,
			 (LPCTSTR)key_install_dir,
			 0,
			 KEY_QUERY_VALUE,
			 &reg_key);

    key_value_len = MAX_KEY_LEN;
    if ((error = RegQueryValueEx(reg_key,
  			 NULL,
				 NULL,
				 &key_type,
				 key_value,
				 &key_value_len
         )) == ERROR_SUCCESS) {

      instDir = key_value;


    }

    RegCloseKey(reg_key);

  }
*/

  // Set current directory
  char buf[MAX_PATH - 1];

  GetModuleFileName(0, buf, MAX_PATH - 1);
  *strrchr(buf, '\\') = '\0'; /* remove .exe file name */
//  strcat(buf, "\\fsp2000");

  if (_chdir(buf) != 0)
  {
    exit(1);
  }
  fprintf(logfile, "Current working directory:\n  %s\n\n", buf);

  // Compile Java command components
  java_with_path = "fem";
  java_options = "-client -Xms10M -Xmx500M -Djtrace.logdir=log";
  fprintf(logfile, "Java options: %s\n\n", java_options);

  // Build classpath
  cpath = "";
  CFileFind findFile;
  BOOL isNextFile = findFile.FindFile("lib\\*.jar");
  while(isNextFile) {
    isNextFile = findFile.FindNextFile();
    cpath += ("lib\\" + findFile.GetFileName() + ";");
  }
  findFile.Close();

  fprintf(logfile, "Java class path:\n  %s\n\n", cpath);


  // Start FSP Network Manager Front End synchronously
//  ((CFnmDlg *)param)->m_log.AddString("FSP Network Manager...");
//  ((CFnmDlg *)param)->ShowWindow(SW_HIDE);


  char* homedir = getenv("USERPROFILE");

  if (homedir != NULL)
  {
    CString femdir = CString(homedir);
    femdir += "\\FSP Element Manager";

    CreateDirectory((LPCTSTR)femdir, NULL);
    propertiespath += femdir;
    fprintf(logfile, "Properties path:\n  %s\n\n", femdir);

    femdir += "\\log";
    CreateDirectory((LPCTSTR)femdir, NULL);
    logpath += femdir;
    fprintf(logfile, "Logging path:\n  %s\n\n", femdir);



  }
  else
  {
    fprintf(logfile, "User home directory not found\n\n");
    propertiespath += ".";
    logpath += "\\log";
  }

  CString htmlViewer = getViewerFor(".html");
  fprintf(logfile, "HTML Viewer path:\n  %s\n\n", htmlViewer);

  CString pdfViewer = getViewerFor(".pdf");
  fprintf(logfile, "PDF Viewer path:\n  %s\n\n", pdfViewer);
  fflush(logfile);

  int argc;
  argv[0] = (char*)(LPCTSTR)java_with_path;
  argv[1] = "-Xms10M";
  argv[2] = "-Xmx500M";
  argv[3] = "-classpath";
  argv[4] = (char*)(LPCTSTR)cpath;
  argv[5] = "com.adva.em.gui.core.common.Main";

  if (argv[7] != NULL)
    argv[6] = "-agent_addr";

  for (argc = -1; argv[++argc]; ) { }  // count args so far (including fem args)

#ifdef HPOV
  OVsnmpConfDest *snmpConf = NULL;

  if (OVsnmpConfOpen(SNMP_CONF_OPEN_RDONLY)) {
    if (snmpConf = OVsnmpConfResolveDest(argv[7], SNMP_CONF_RESOLVE)) {
      argv[argc++] = "-rc";
      argv[argc++] = snmpConf->confEntry->community;
    }
    if ((snmpConf = OVsnmpConfResolveDest(argv[7], SNMP_CONF_RESOLVE)) &&
        !snmpConf->confEntry->setCommunity) {
      argv[argc++] = "-wc";
      argv[argc++] = snmpConf->confEntry->setCommunity;
    }
    OVsnmpConfClose();
  }

#endif

  java_main(argc, (char**)argv);

  fclose(logfile);    
  exit(0);
}

/////////////////////////////////////////////////////////////////////////////
// CFnmApp

BEGIN_MESSAGE_MAP(CFnmApp, CWinApp)
	//{{AFX_MSG_MAP(CFnmApp)
	//}}AFX_MSG
	ON_COMMAND(ID_HELP, CWinApp::OnHelp)
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CFnmApp Konstruktion

CFnmApp::CFnmApp()
{
}

/////////////////////////////////////////////////////////////////////////////
// Das einzige CFnmApp-Objekt

CFnmApp theApp;

/////////////////////////////////////////////////////////////////////////////
// CFnmApp Initialisierung

BOOL CFnmApp::InitInstance()
{
/*	if (!AfxSocketInit())
	{
		AfxMessageBox(IDP_SOCKETS_INIT_FAILED);
		return FALSE;
	}
*/
	// Standardinitialisierung

#ifdef _AFXDLL
	Enable3dControls();			// Diese Funktion bei Verwendung von MFC in gemeinsam genutzten DLLs aufrufen
#else
	Enable3dControlsStatic();	// Diese Funktion bei statischen MFC-Anbindungen aufrufen
#endif

  // Parse Command Line
  FemCommandLineInfo fcli;
  ParseCommandLine(fcli);
  argv = fcli.getArgv();

	CFnmDlg dlg;
	m_pMainWnd = &dlg;

	// Create a non-modal dialog
	int nResponse = dlg.Create(IDD_FNM_DIALOG, NULL);
	if (nResponse)
	{

	  // If dialog is created start the work thread and run the main loop
//    do_fem(NULL);
	  do_fem_thread = _beginthread(do_fem, 0, &dlg);
	  Run();
	  exit(0);

	}
	else
	{
	}

	// Da das Dialogfeld geschlossen wurde, FALSE zur�ckliefern, so dass wir die
	//  Anwendung verlassen, anstatt das Nachrichtensystem der Anwendung zu starten.
	return FALSE;
}
