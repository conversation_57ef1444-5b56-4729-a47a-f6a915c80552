// femfeDlg.h : Header-Datei
//

#if !defined(AFX_CELLCASTDLG_H__1E40F382_FBE0_11D3_B8A5_0050DA45ACBC__INCLUDED_)
#define AFX_CELLCASTDLG_H__1E40F382_FBE0_11D3_B8A5_0050DA45ACBC__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

/////////////////////////////////////////////////////////////////////////////
// CFnmDlg Dialogfeld

class CFnmDlg : public CDialog
{
// Konstruktion
public:
	CFnmDlg(CWnd* pParent = NULL);	// Standard-Konstruktor

// Dialogfelddaten
	//{{AFX_DATA(CFnmDlg)
	enum { IDD = IDD_FNM_DIALOG };
	CListBox	m_log;
	//}}AFX_DATA

	// Vom Klassenassistenten generierte �berladungen virtueller Funktionen
	//{{AFX_VIRTUAL(CFnmDlg)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);	// DDX/DDV-Unterst�tzung
	//}}AFX_VIRTUAL

// Implementierung
protected:
	HICON m_hIcon;

	// Generierte Message-Map-Funktionen
	//{{AFX_MSG(CFnmDlg)
	virtual BOOL OnInitDialog();
	afx_msg void OnPaint();
	afx_msg HCURSOR OnQueryDragIcon();
	afx_msg void OnClose();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ f�gt unmittelbar vor der vorhergehenden Zeile zus�tzliche Deklarationen ein.

#endif // !defined(AFX_CELLCASTDLG_H__1E40F382_FBE0_11D3_B8A5_0050DA45ACBC__INCLUDED_)
