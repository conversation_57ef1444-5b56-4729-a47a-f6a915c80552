// FemCommandLineInfo.cpp: Implementierung der Klasse FemCommandLineInfo.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "FemCommandLineInfo.h"

//////////////////////////////////////////////////////////////////////
// Konstruktion/Destruktion
//////////////////////////////////////////////////////////////////////

FemCommandLineInfo::FemCommandLineInfo()
{
  argc = 7;
}

FemCommandLineInfo::~FemCommandLineInfo()
{

}

void FemCommandLineInfo::ParseParam(LPCTSTR lpszParam, BOOL bFlag, BOOL bLast) {

  if (bFlag) {
    CString *option = new CString("-");
    CString *flag = new CString(lpszParam);
    *option += *flag;
    argv[argc++] = (char*)(LPCTSTR )*option;
  } else {
    argv[argc++] = (char*)lpszParam;
  }

  if (bLast)
    argv[argc++] = NULL;

}

char **FemCommandLineInfo::getArgv(){

  return argv;
}
