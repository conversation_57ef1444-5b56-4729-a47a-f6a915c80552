//Microsoft Developer Studio generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "afxres.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// Deutsch (Deutschland) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_DEU)
#ifdef _WIN32
LANGUAGE LANG_GERMAN, SUBLANG_GERMAN
#pragma code_page(1252)
#endif //_WIN32

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE DISCARDABLE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE DISCARDABLE 
BEGIN
    "#include ""afxres.h""\r\n"
    "\0"
END

3 TEXTINCLUDE DISCARDABLE 
BEGIN
    "#define _AFX_NO_SPLITTER_RESOURCES\r\n"
    "#define _AFX_NO_OLE_RESOURCES\r\n"
    "#define _AFX_NO_TRACKER_RESOURCES\r\n"
    "#define _AFX_NO_PROPERTY_RESOURCES\r\n"
    "\r\n"
    "#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)\r\n"
    "#ifdef _WIN32\r\n"
    "LANGUAGE 9, 1\r\n"
    "#pragma code_page(1252)\r\n"
    "#endif //_WIN32\r\n"
    "#include ""res\\fem.rc2""  // Nicht mit Microsoft Visual C++ bearbeitete Ressourcen\r\n"
    "#include ""afxres.rc""         // Standardkomponenten\r\n"
    "#endif\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED

#endif    // Deutsch (Deutschland) resources
/////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
// Englisch (USA) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US
#pragma code_page(1252)
#endif //_WIN32

/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDD_FNM_DIALOG DIALOGEX 0, 0, 196, 146
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION
EXSTYLE WS_EX_APPWINDOW
CAPTION "FSP Network Manager"
FONT 8, "MS Sans Serif", 0, 0, 0x1
BEGIN
    LISTBOX         IDC_LOG,7,91,182,48,NOT LBS_NOTIFY | 
                    LBS_NOINTEGRALHEIGHT | WS_VSCROLL | WS_HSCROLL | 
                    WS_TABSTOP
    ICON            IDR_MAINFRAME,IDC_STATIC,88,7,20,20
    CTEXT           "FSP Network Manager\nVersion 1.0",IDC_STATIC,25,44,146,
                    30,SS_SUNKEN,WS_EX_DLGMODALFRAME
END


#ifndef _MAC
/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 1,0,0,1
 PRODUCTVERSION 1,0,0,1
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "Comments", "\0"
            VALUE "CompanyName", "ADVA Optical Networking\0"
            VALUE "FileDescription", "FSP 2000 Element Manager\0"
            VALUE "FileVersion", "1, 0, 0, 1\0"
            VALUE "InternalName", "FSP Element Manager\0"
            VALUE "LegalCopyright", "Copyright (C) 2002-2004 ADVA Optical Networking.\0"
            VALUE "LegalTrademarks", "\0"
            VALUE "OriginalFilename", "fem.exe\0"
            VALUE "PrivateBuild", "\0"
            VALUE "ProductName", "fem Application\0"
            VALUE "ProductVersion", "6.0\0"
            VALUE "SpecialBuild", "\0"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END

#endif    // !_MAC


/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
IDR_MAINFRAME           ICON    DISCARDABLE     "fem.ico"

/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE DISCARDABLE 
BEGIN
    IDP_SOCKETS_INIT_FAILED "Windows sockets initialization failed."
    IDS_ERR_NOENV_CELLWARE  "Environment variable CELLWARE not found. Please check your CELL-CAST installation."
    IDS_ODBLOCK             "The CELL-CAST database is currently locked. Do you want to remove this lock?\n\nRemove this lock only if you are sure that no other instance of CELL-CAST is running."
    IDS_NO_SYSROOT          "Could not find system directory (%SystemRoot%). Please check your Windows installation."
    IDS_READERR             "Error while reading from CMMP port file. Please check your CELL-CAST installation."
    IDS_NO_CMMPPORTFILE     "Could not open CMMP port file. Please check your CELL-CAST installation."
END

#endif    // Englisch (USA) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//
#define _AFX_NO_SPLITTER_RESOURCES
#define _AFX_NO_OLE_RESOURCES
#define _AFX_NO_TRACKER_RESOURCES
#define _AFX_NO_PROPERTY_RESOURCES

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE 9, 1
#pragma code_page(1252)
#endif //_WIN32
#include "res\fem.rc2"  // Nicht mit Microsoft Visual C++ bearbeitete Ressourcen
#include "afxres.rc"         // Standardkomponenten
#endif

/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

