/*****************************************************************************
*                                                __
*                  (c) Cellware Broadband      _|__|
*                      Berlin                 |_|__
*                      Germany                  |__|
*
*          $Source$ 
*            $Date$ 
*        $Revision$ 
*          $Author$ 
*            $Name$ 
*
*     $ProjectName: Q:/project/nmssw/cd/src/project.pj $ 
* $ProjectRevision: 1.24 $ 
*
*****************************************************************************/
// This file is based on the Windows NT service example (Microsoft)

#ifndef _NT_SERVICE_H
#define _NT_SERVICE_H

#ifdef __cplusplus
extern "C" {
#endif

//////////////////////////////////////////////////////////////////////////////
// name of the executable
#define SZAPPNAME            "SNMPServer"
// internal name of the service
#define SZSERVICENAME        "advass"
// displayed name of the service
#define SZSERVICEDISPLAYNAME "ADVA: SNMP Server"
// list of service dependencies - "dep1\0dep2\0\0"
#define SZDEPENDENCIES       ""
// which server shall be started
#define ADVASERVERTYPE       SNMPServer
//////////////////////////////////////////////////////////////////////////////




//////////////////////////////////////////////////////////////////////////////
//// todo: ServiceStart()must be defined by in your code.
////       The service should use ReportStatusToSCMgr to indicate
////       progress.  This routine must also be used by StartService()
////       to report to the SCM when the service is running.
////
////       If a ServiceStop procedure is going to take longer than
////       3 seconds to execute, it should spawn a thread to
////       execute the stop code, and return.  Otherwise, the
////       ServiceControlManager will believe that the service has
////       stopped responding
////
VOID ServiceStart(DWORD dwArgc, LPTSTR *lpszArgv);
VOID ServiceStop();
//////////////////////////////////////////////////////////////////////////////



//////////////////////////////////////////////////////////////////////////////
//// The following are procedures which
//// may be useful to call within the above procedures,
//// but require no implementation by the user.
//// They are implemented in service.c

//
//  FUNCTION: ReportStatusToSCMgr()
//
//  PURPOSE: Sets the current status of the service and
//           reports it to the Service Control Manager
//
//  PARAMETERS:
//    dwCurrentState - the state of the service
//    dwWin32ExitCode - error code to report
//    dwWaitHint - worst case estimate to next checkpoint
//
//  RETURN VALUE:
//    TRUE  - success 
//    FALSE - failure
//
BOOL ReportStatusToSCMgr(DWORD dwCurrentState, DWORD dwWin32ExitCode, DWORD dwWaitHint);


//
//  FUNCTION: AddToMessageLog(LPTSTR lpszMsg)
//
//  PURPOSE: Allows any thread to log an error message
//
//  PARAMETERS:
//    lpszMsg - text for message
//
//  RETURN VALUE:
//    none
//
void AddToMessageLog(LPTSTR lpszMsg);

//////////////////////////////////////////////////////////////////////////////


#ifdef __cplusplus
}
#endif

#endif
/*****************************************************************************
*
*  $Log$
*  Revision 1.1  2001/07/18 15:37:02  bollow
*  Initial revision
* 
*  Revision 1.1  2000/01/21 10:25:00Z  bollow 
*  Common (UNIX/WIN) event list for polling 
*  WIN: select loop only; no CAsyncSocket 
*
*****************************************************************************/
