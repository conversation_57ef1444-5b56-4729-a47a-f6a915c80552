// fnp.cpp : Legt das Klassenverhalten f�r die Anwendung fest.
//

#include "stdafx.h"
#include "fnp.h"
#include "fnpDlg.h"
#include <process.h>
#include <winreg.h>
#include <direct.h>

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

extern "C"
{
  int java_main(int argc, char **argv);
  extern FILE *logfile;
}

unsigned long do_fnp_thread;
int ps_ns;

/////////////////////////////////////////////////////////////////////////////
// getViewerFor - Get the path to the viewer for specified file extension
//                from the registry

CString getViewerFor(CString extension) {

  HKEY reg_key;
  DWORD key_type, key_value_len;
  BYTE key_value[MAX_KEY_LEN];
  LONG error;
  unsigned int i,j;
  key_value_len = MAX_KEY_LEN;
  static char viewer[MAX_KEY_LEN];

  CString keyForExtension = CString(KEY_CLASSES);
  keyForExtension += extension;

  strcpy(viewer, "none");

  error = RegOpenKeyEx(HKEY_LOCAL_MACHINE,
		       keyForExtension,
		       0,
		       KEY_QUERY_VALUE,
		       &reg_key);

  if ((error = RegQueryValueEx(reg_key,
		      NULL,
		      NULL,
		      &key_type,
		      key_value,
		      &key_value_len
		      )) == ERROR_SUCCESS) {

    CString key_open;

    key_open = KEY_CLASSES;
    key_open += (char *)key_value;
    key_open += KEY_OPEN;

    error = RegOpenKeyEx(HKEY_LOCAL_MACHINE,
			 (LPCTSTR)key_open,
			 0,
			 KEY_QUERY_VALUE,
			 &reg_key);

    key_value_len = MAX_KEY_LEN;
    if ((error = RegQueryValueEx(reg_key,
  		   NULL,
				 NULL,
				 &key_type,
				 key_value,
				 &key_value_len
				 )) == ERROR_SUCCESS) {

      BOOL is_escaped = FALSE;
      BOOL viewerFound = FALSE;

      // Use only program part (no argument placeholder)
      for ((i = 0, j = 0); key_value[i] != '\0'; i++) {
		    if (key_value[i] == '"') {
		      if (is_escaped) {
  			    viewer[j++] = key_value[i];
			      viewer[j] = '\0';
			      break;
          } else {
  			    viewer[j++] = key_value[i];
			      is_escaped = TRUE;
			      continue;
          }
        }

		    if ((key_value[i] == ' ') && viewerFound && !is_escaped) {
		      viewer[j] = '\0';
		      break;
        }

		    viewer[j++] = key_value[i];
		    viewerFound = TRUE;
      }
    }
    
  }
  CString retval = CString(viewer);
  return retval;
}

/////////////////////////////////////////////////////////////////////////////
// do_fnp - This function performs the complete front end application
//		 management including pre launch checks, start of all
//		 processes and shutdown.

void do_fnp(void *param) {

  CString java_with_path;
  CString java_options;
  CString logpath = CString("-Djtrace.logdir=");			// User log folder
  CString propertiespath = CString("-Dproperties.dir=");	// User data folder (properties and data)
  CString cpath = CString("");
  CString instDir;


  LPTSTR tmpPath = new char[MAX_PATH+1];
  GetTempPath(MAX_PATH, tmpPath);
  CString logFilePath = CString(tmpPath);
  logFilePath += "fnp.log";
  logfile = fopen((LPCTSTR)logFilePath, "w+");


/*
  char html_browser[MAX_KEY_LEN];

  HKEY reg_key;
  DWORD key_type, key_value_len;
  BYTE key_value[MAX_KEY_LEN];
  LONG error;

  unsigned int i,j;

  // Look for HTML browser
  strcpy(html_browser, "none");
  key_value_len = MAX_KEY_LEN;

  error = RegOpenKeyEx(HKEY_LOCAL_MACHINE,
		       KEY_HTML,
		       0,
		       KEY_QUERY_VALUE,
		       &reg_key);

  if ((error = RegQueryValueEx(reg_key,
		      NULL,
		      NULL,
		      &key_type,
		      key_value,
		      &key_value_len
		      )) == ERROR_SUCCESS) {

    CString key_open_html;

    key_open_html = KEY_CLASSES;
    key_open_html += (char *)key_value;
    key_open_html += KEY_OPEN;

    error = RegOpenKeyEx(HKEY_LOCAL_MACHINE,
			 (LPCTSTR)key_open_html,
			 0,
			 KEY_QUERY_VALUE,
			 &reg_key);

    key_value_len = MAX_KEY_LEN;
    if ((error = RegQueryValueEx(reg_key,
  				 NULL,
				 NULL,
				 &key_type,
				 key_value,
				 &key_value_len
				 )) == ERROR_SUCCESS) {

      BOOL is_escaped = FALSE;
      BOOL is_html_browser = FALSE;

      for ((i = 0, j = 0); key_value[i] != '\0'; i++) {
		if (key_value[i] == '"') {
		  if (is_escaped) {
  			html_browser[j++] = key_value[i];
			html_browser[j] = '\0';
			break;
		  } else {
  			html_browser[j++] = key_value[i];
			is_escaped = TRUE;
			continue;
		  }
		}

		if ((key_value[i] == ' ') && is_html_browser && !is_escaped) {
		  html_browser[j] = '\0';
		  break;
		}

		html_browser[j++] = key_value[i];
		is_html_browser = TRUE;

      }
    }
    
  }

*/
  // Resolve installation path
/*  if ((error = RegQueryValueEx(reg_key,
		      NULL,
		      NULL,
		      &key_type,
		      key_value,
		      &key_value_len
		      )) == ERROR_SUCCESS) {

    CString key_install_dir;

    key_install_dir = KEY_ADVA_NMS;
    key_install_dir += "\\";
    key_install_dir += KEY_INSTDIR;

    error = RegOpenKeyEx(HKEY_LOCAL_MACHINE,
			 (LPCTSTR)key_install_dir,
			 0,
			 KEY_QUERY_VALUE,
			 &reg_key);

    key_value_len = MAX_KEY_LEN;
    if ((error = RegQueryValueEx(reg_key,
  			 NULL,
				 NULL,
				 &key_type,
				 key_value,
				 &key_value_len
         )) == ERROR_SUCCESS) {

      instDir = key_value;


    }

    RegCloseKey(reg_key);

  }
*/
  // Compile Java command components
  java_with_path = "fnp";
  java_options = "-hotspot -Xms10M -Xmx500M -Djtrace.logdir=log";
  fprintf(logfile, "Java options: %s\n\n", java_options);
  cpath = cpath
        + "lib\\planner.jar" + ";"        // FNP front end
        + "lib\\jdom.jar" + ";"           // Parser support JAVA Collection API (abstract version - latest version)
        + "lib\\jaxp.jar" + ";"           // SAX Parser
        + "lib\\parser.jar" + ";"         // XML parser
        + "lib\\soap.jar" + ";"           // services -> Connection between Server and Clients
        + "lib\\jaxp.jar" + ";"           // XML parser
		+ "lib\\xerces.jar" + ";"         // second impl. Parser: XML / XSL and HTML stylesheet
        + "lib\\xlrd.jar" + ";"           // Excel parser
        + "lib\\xt.jar" + ";"             // XML to HTML converter
        + "lib\\poi.jar" + ";"            // POI for service: import and export order
        + "lib\\service-data.jar" + ";"    // contains the data structure of order for import and export service
        + "lib\\commons-logging.jar" + ";" // logging stuff for apache components
        + "lib\\castor.jar" + ";"          // class to xml
        + "lib\\commons-collections.jar" + ";" // extensions for Collection
        ;
  fprintf(logfile, "Java class path:\n  %s\n\n", cpath);


  // Start FSP Network Planner Front End synchronously
//  ((CFnpDlg *)param)->m_log.AddString("FSP Network Planner...");
//  ((CFnpDlg *)param)->ShowWindow(SW_HIDE);

  char buf[MAX_PATH - 1];

  GetModuleFileName(0, buf, MAX_PATH - 1);
  *strrchr(buf, '\\') = '\0'; /* remove .exe file name */

  if (_chdir(buf) != 0)
  {
    exit(1);
  }
  fprintf(logfile, "Current working directory:\n  %s\n\n", buf);

  char* homedir = getenv("USERPROFILE");

  if (homedir != NULL)
  {
    CString fnpdir = CString(homedir);
    fnpdir += "\\FSP Network Planner";

    CreateDirectory((LPCTSTR)fnpdir, NULL);
    propertiespath += fnpdir;
    fprintf(logfile, "Properties path:\n  %s\n\n", propertiespath);

	CString datadir = fnpdir + "\\data";
    CreateDirectory((LPCTSTR)datadir, NULL);
    fprintf(logfile, "Data path:\n  %s\n\n", datadir);

    fnpdir += "\\log";
    CreateDirectory((LPCTSTR)fnpdir, NULL);
    logpath += fnpdir;
    fprintf(logfile, "Logging path:\n  %s\n\n", logpath);

    CString logConfigFile = CString(buf);
    logConfigFile += "\\log\\default.cfg";

    if (CopyFile((LPCTSTR)logConfigFile, (LPCTSTR)(fnpdir + "\\default.cfg"), TRUE) == 0)
    {
      int error = GetLastError();
    }
    fprintf(logfile, "default.cfg copied to Logging path\n\n");
  }
  else
  {
    fprintf(logfile, "User home directory not found\n\n");
    propertiespath += ".";
    logpath += "\\log";
  }


//  CString htmlViewer = getViewerFor(".html");
//  fprintf(logfile, "HTML Viewer path:\n  %s\n\n", htmlViewer);

  CString xlsViewer = getViewerFor(".xls");
  fprintf(logfile, "XLS Viewer path:\n  %s\n\n", xlsViewer);

  CString pdfViewer = getViewerFor(".pdf");
  fprintf(logfile, "PDF Viewer path:\n  %s\n\n", pdfViewer);
  fflush(logfile);


  int argc;
  static const char *argv[] = {(const char*)(LPCTSTR)java_with_path,
                               "-client",
                               "-Xms10M",
                               "-Xmx500M",
                               (const char*)(LPCTSTR)logpath,
                               (const char*)(LPCTSTR)propertiespath,
                               "-classpath",
                               (const char*)(LPCTSTR)cpath,
                               "com.adva.planner.Launcher",
                               "-conf.gui.xlsViewerPath",
                               (const char*)(LPCTSTR)xlsViewer,
                               "-conf.gui.browserPath",
                               (const char*)(LPCTSTR)pdfViewer,
                               NULL
                              };

  for (argc = -1; argv[++argc]; ) {}

  java_main(argc, (char**)argv);
    
  fclose(logfile);    
  exit(0);
}

/////////////////////////////////////////////////////////////////////////////
// CFnpApp

BEGIN_MESSAGE_MAP(CFnpApp, CWinApp)
	//{{AFX_MSG_MAP(CFnpApp)
	//}}AFX_MSG
	ON_COMMAND(ID_HELP, CWinApp::OnHelp)
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CFnpApp Konstruktion

CFnpApp::CFnpApp()
{
}

/////////////////////////////////////////////////////////////////////////////
// Das einzige CFnpApp-Objekt

CFnpApp theApp;

/////////////////////////////////////////////////////////////////////////////
// CFnpApp Initialisierung

BOOL CFnpApp::InitInstance()
{
/*	if (!AfxSocketInit())
	{
		AfxMessageBox(IDP_SOCKETS_INIT_FAILED);
		return FALSE;
	}
*/
	// Standardinitialisierung

#ifdef _AFXDLL
	Enable3dControls();			// Diese Funktion bei Verwendung von MFC in gemeinsam genutzten DLLs aufrufen
#else
	Enable3dControlsStatic();	// Diese Funktion bei statischen MFC-Anbindungen aufrufen
#endif

	CFnpDlg dlg;
	m_pMainWnd = &dlg;

	// Create a non-modal dialog
	int nResponse = dlg.Create(IDD_FNP_DIALOG, NULL);
	if (nResponse)
	{

	  // If dialog is created start the work thread and run the main loop
//    do_fnp(NULL);
	  do_fnp_thread = _beginthread(do_fnp, 0, &dlg);
	  Run();
	  exit(0);

	}
	else
	{
	}

	// Da das Dialogfeld geschlossen wurde, FALSE zur�ckliefern, so dass wir die
	//  Anwendung verlassen, anstatt das Nachrichtensystem der Anwendung zu starten.
	return FALSE;
}
