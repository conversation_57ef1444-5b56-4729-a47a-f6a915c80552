// fnpfe.h : Haupt-Header-Datei f�r die Anwendung CELLCAST
//

#if !defined(AFX_CELLCAST_H__1E40F380_FBE0_11D3_B8A5_0050DA45ACBC__INCLUDED_)
#define AFX_CELLCAST_H__1E40F380_FBE0_11D3_B8A5_0050DA45ACBC__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#ifndef __AFXWIN_H__
	#error include 'stdafx.h' before including this file for PCH
#endif

#include "resource.h"		// Hauptsymbole

#define MAX_KEY_LEN   128

#define KEY_CLASSES   "SOFTWARE\\Classes\\"
#define KEY_HTML      "SOFTWARE\\Classes\\.html"
#define KEY_OPEN      "\\shell\\open\\command"

#define KEY_ADVA_NMS  "SOFTWARE\\ADVA Optical Networking\\FSP Management Suite"
#define KEY_INSTDIR   "InstallDir"

/////////////////////////////////////////////////////////////////////////////
// CFnpDlg:
// Siehe fnpfe.cpp f�r die Implementierung dieser Klasse
//

class CFnpApp : public CWinApp
{
public:
	CFnpApp();

// �berladungen
	// Vom Klassenassistenten generierte �berladungen virtueller Funktionen
	//{{AFX_VIRTUAL(CFnpDlg)
	public:
	virtual BOOL InitInstance();
	//}}AFX_VIRTUAL

// Implementierung

	//{{AFX_MSG(CFnpDlg)
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};


/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ f�gt unmittelbar vor der vorhergehenden Zeile zus�tzliche Deklarationen ein.

#endif // !defined(AFX_CELLCAST_H__1E40F380_FBE0_11D3_B8A5_0050DA45ACBC__INCLUDED_)
