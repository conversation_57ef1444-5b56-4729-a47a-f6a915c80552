<?xml version="1.0" encoding="UTF-8"?>
<Configuration name="ProxyConfig" status="info" strict="true" monitorInterval="10">
    <Properties>
        <Property name="logdir">var/log</Property>
    </Properties>

    <Loggers>
        <Logger name="org.eclipse.jetty" level="info" additivity="false">
            <AppenderRef ref="jettyproxylog"/>
        </Logger>
        <Logger name="com.adva.nlms.mediation.server.proxy" level="debug" additivity="false">
            <AppenderRef ref="jettyproxylog"/>
        </Logger>
    </Loggers>

    <Appenders>
        <Appender name="jettyproxylog" type="RollingFile" fileName="${logdir}/jettyproxy.log" filePattern="${logdir}/jettyproxy.log.%i" append="true" >
            <Layout type="PatternLayout" pattern="%d [%.15t] %-5p - %m%n" />
            <DefaultRolloverStrategy max="12" />
            <SizeBasedTriggeringPolicy size="50mb" />
        </Appender>
    </Appenders>
</Configuration>