/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: tedd
 */

def ENC_REPO_NAME = "Enc"
def DEV_REPO_NAME = "Dev"
def LIBS_REPO_NAME = "Libs"

// Define repositories to use for all projects
def repos = {
    if (PluginDeveloper) {
        mavenLocal()
    }

    maven {
        name = ENC_REPO_NAME
        credentials {
            username artifactoryReadUser
            password artifactoryReadPassword
        }
        url "https://$artifactoryServer/artifactory/NMS"
        metadataSources {
            mavenPom()
            artifact()
        }
    }

    maven {
        name = DEV_REPO_NAME
        credentials {
            username artifactoryReadUser
            password artifactoryReadPassword
        }
        url "https://$artifactoryServer/artifactory/libs-snapshot"
        metadataSources {
            mavenPom()
            artifact()
        }
    }

    maven {
        name = LIBS_REPO_NAME
        credentials {
            username artifactoryReadUser
            password artifactoryReadPassword
        }
        url "https://$artifactoryServer/artifactory/libs-release"
        metadataSources {
            mavenPom()
            artifact()
        }
    }
}

// Only apply repositories if not already applied
if (project.getRepositories().size() == 0 || project.getRepositories().findByName(ENC_REPO_NAME) == null) {
    logger.info("Applying repositories.gradle from {} (current configured repository size = {})", project.getName(), project.getRepositories().size())
    allprojects { Project p ->
        // Due to timing issue with gradle apply repositories to project and buildscript
        // so it is available to both
        project.buildscript.repositories(repos)
        repositories(repos)
    }
    logger.info("Final repository list:")
    project.getRepositories().forEach {
        logger.info("  {}", it.getName())
    }
}
