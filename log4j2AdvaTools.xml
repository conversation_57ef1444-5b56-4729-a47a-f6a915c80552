<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~  Copyright 2023 Adtran Networks SE. All rights reserved.
  ~
  ~  Owner: szczesny
  Adva Tools Network Manager log4j-Logging Configuration File
  -->
<Configuration name="AdvaTools" status="info">
    <Properties>
        <Property name="logdir">var/log</Property>
    </Properties>
    <Loggers>
        <Root level="debug" additivity="false">
            <AppenderRef ref="advatoolslog"/>
        </Root>
        <Logger name="com.adva" level="debug" additivity="false">
            <AppenderRef ref="advatoolslog"/>
        </Logger>
    </Loggers>
    <Appenders>
        <RollingFile name="advatoolslog" fileName="${logdir}/advatools.log" filePattern="${logdir}/advatools.log.%i">
            <PatternLayout>
                <Pattern>%d [%.15t] %-5p - %m%n</Pattern>
            </PatternLayout>
            <Policies>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="12"/>
        </RollingFile>
    </Appenders>
</Configuration>