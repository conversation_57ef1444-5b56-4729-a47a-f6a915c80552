launcher.nl.java.option.additional= -client -Xms10M -XX:+DisableAttachMechanism -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -Dorg.apache.activemq.SERIALIZABLE_PACKAGES="java.lang,java.util,org.apache.activemq,org.fusesource.hawtbuf,com.thoughtworks.xstream.mapper,ni,java.time,org.apache.commons.lang3.tuple,com.google.common.collect,java.beans,com.adva"
launcher.class.path=lib/frontend.jar;
launcher.main.class=com.adva.common.workbench.DefaultContextManager
launcher.command.line.args=-application frontend
launcher.command.line.productname= FSP Network Manager
launcher.command.line.executable= FSP NM ClientUpdater.exe

#the path must follow  one the following patterns:
#c:\\Apps\\ADVA\\nmsclients    OR
#c:/Apps/ADVA/nmsclients
#very important to use either "/"  or "\\" as file/directory separator.
launcher.program.directory=
launcher.java64.memory=-Xmx2048M
launcher.java32.memory=-Xmx1024M
#the path must follow  one the following patterns:
#c:\\Apps\\ADVA\\downloads    OR
#c:/Apsp/ADVA/downloads
#very important to use either "/"  or "\\" as file/directory separator.

#if port is not to be used, it should be set to 0
launcher.download.directory=
launcher.webserver.port_0=8443
launcher.webserver.port_1=8080
launcher.webserver.port_2=80
launcher.webserver.port_3=9000
