<?xml version="1.0" encoding="UTF-8"?>

<Configuration name="NMClientUpdaterConfig" status="info">
    <!--<Configuration status="error">-->
    <Properties>
        <!--# log directories-->
        <Property name="logdir">${sys:user.home}/ClientUpdater/log</Property>
    </Properties>
    <Loggers>
        <Root level="info">
            <AppenderRef ref="culog"/>
        </Root>
        <Logger name="com.adva.nlms.common" level="info"  additivity="false" >
            <AppenderRef ref="culog" />
        </Logger>
    </Loggers>
    <Appenders>
        <RollingFile name="culog" fileName="${logdir}/ClientUpdater.error"
                     filePattern="${logdir}/ClientUpdater.error.%i" append="true">
            <PatternLayout pattern="%d [%.15t] %-5p - %m%n"/>
            <DefaultRolloverStrategy max="2"/>
            <SizeBasedTriggeringPolicy size="10mb"/>
        </RollingFile>
    </Appenders>
</Configuration>