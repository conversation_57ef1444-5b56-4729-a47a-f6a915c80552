launcher.nl.java.option.additional= -client -Xms10M -Xmx1024M -XX:+HeapDumpOnOutOfMemoryError -Dprism.allowhidpi=false -Dfile.encoding=UTF-8
launcher.class.path=lib/frontend.jar;
launcher.main.class=com.adva.common.workbench.DefaultContextManager
launcher.command.line.args=-application frontend
launcher.command.line.productname= FSP Network Manager
launcher.command.line.executable= FSP NM ClientUpdater.exe

#the path must follow  one the following patterns:
#c:\\Apps\\ADVA\\nmsclients    OR
#c:/Apps/ADVA/nmsclients
#very important to use either "/"  or "\\" as file/directory separator.
launcher.program.directory=

#the path must follow  one the following patterns:
#c:\\Apps\\ADVA\\downloads    OR
#c:/Apsp/ADVA/downloads
#very important to use either "/"  or "\\" as file/directory separator.
launcher.download.directory=

#if port is not to be used, it should be set to 0
launcher.webserver.port_0=8443
launcher.webserver.port_1=8080
launcher.webserver.port_2=80
launcher.webserver.port_3=9000
