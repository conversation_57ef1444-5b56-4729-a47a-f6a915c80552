# Uncomment to enable all log files
#-*

+snmpproxy*
+performance*
+polling*
+com.adva.nlms.mediation.report*

# Example:
# date+  time+  tickcount+  threadID+  threadPriority+  threadName+
# 01/02/06 22:04:50 983905490421 7a78d3  main  5   | trace...


#**************JTraceWriter********************
# Date enable/disable (on/off)
writer.date=on

# Time enable/disable (on/off)
writer.time=on

# Time starts from 0 enable/disable (on/off)
writer.resetTime=off

# Tick count enable/disable (on/off/full)
writer.tickcount=off

# Thread ID enable/disable (+/-)
writer.threadID=off

# Thread name enable/disable (+/-)
writer.threadName=on

# Thread priority enable/disable (+/-)
writer.threadPriority=off

# Maximum level (on default 10) static parametrs
writer.maxLevel=30

#*****************************************************


#**************JTraceConnectorFactory*****************
# Put the files in the subdirectory
connector.subDir=off

# Log extension (on default .log) static parametrs
connector.extension=.log

# Count open connections (on default 100)
connector.openFiles=90

#connector.bufferSize=8192
#connector.bufferLine=8
#connector.flushTime=2000
#*****************************************************


#**************JTraceRule*****************************
# Error JTrace log
#errorLog=lib\frontend.error.log

# Polling period (on default 2000 ms)
pollingPeriod=2000

#*****************************************************


