/*
 * Copyright 2023 Adtran Networks SE. All rights reserved.
 */
import org.apache.tools.ant.taskdefs.condition.Os

// Common build system parameters

try{
    // Apply the custom.gradle from the user's home directory
    applyUserCustomGradle(rootProject)

    // Apply the custom.gradle for the root project
    applyCustomGradle(rootProject)
} catch (Throwable t) {
    logger.debug("unable to read custom.gradle from project, trying settings")

    // Maybe settings, try settings instead

    // Apply the custom.gradle from the user's home directory
    applyUserCustomGradle(settings)

    // Apply the custom.gradle for the root project
    applyCustomGradle(settings)
}

ext {
    // If configured to 'false' will disable publishing build scans (on by default)
    publishBuildScan = getBooleanPropWithDefault("publishBuildScan", true)

   // True if the build is a production build
    ProductionBuild = getBooleanPropWithDefault("ProductionBuild", false)

    // Set to true to add version intformation to build generated artifacts
    versionArtifacts = getBooleanPropWithDefault("versionArtifacts", false)

    // True if the build is targeted to filling the gradle build cache only
    BuildCacheOnly = getBooleanPropWithDefault("BuildCacheOnly", false)

    // If false then manipulate jar names to remove version information in production install
    RetainJarVersionInfo = getBooleanPropWithDefault("RetainJarVersionInfo", true)

    // Use user specified location of FSP workspace if specified
    // User specified path should be absolute to be most effective
    fspRoot = getFilePropWithDefault('fspRoot', rootProject.projectDir)

    fspRootArchives = "$fspRoot/build/archives"
    fspRootPackage = "$fspRoot/build/package"
    fspRootLibs = "$fspRoot/lib"
    fspRootTestLibs = "$fspRoot/test/lib"
    fspRootClasses = "$fspRoot/build/classes"
    fspRootTestClasses = "$fspRoot/test/classes"
    fspRootSources = "$fspRoot/build/java"

    // Additional build scan tags to add to the scan data
    tags = getStringPropWithDefault("tags", "")

    // Look for the best candidate to use for postgres
    def findPostgresDir = {
        // Fallback, if postgres is installed locally 'psql' and other tools will be in /usr/bin
        String postgresDir = "/usr/bin"
        File f = file("/opt/adva/fsp_nm/postgres")

        if (f.exists()) {
            // Found production install use the postgres from production install
            postgresDir = f.getCanonicalPath()+"/bin"
        }

        return postgresDir
    }

    //alternate postgres location
    postgresdir = getStringPropWithDefault("PostgresDir", findPostgresDir())
    // Use a symlink for the entire postgres directory instead of individual files
    usePostgresDirLink = getBooleanPropWithDefault("usePostgresDirLink", true)

    // Compile related settings
    encoding = getStringPropWithDefault("encoding", "UTF-8")
    compileWarnings = getStringPropWithDefault("compileWarnings", "false")
    compileDebug = getStringPropWithDefault("compileDebug", "true")
    // Use the version of the JDK as the default for building PV but allow for override
    String defaultSourceLevel = JavaVersion.current().toString()
    String defaultTargetLevel = JavaVersion.current().toString()
    sourceLevel = JavaVersion.toVersion(getStringPropWithDefault('sourceLevel', defaultSourceLevel))
    targetLevel = JavaVersion.toVersion(getStringPropWithDefault('targetLevel', defaultTargetLevel))

    // JAVA17_MIGRATION
    // Work around to eliminate false positives in sonarqube analysis.
    // Using java 11 bytecode causes sonarqube to believe private methods are not used when they really are in some cases.
    //if (sourceLevel.isJava11()) {
    //    sourceLevel = JavaVersion.VERSION_1_10
    //}
    //if (targetLevel.isJava11()) {
    //    targetLevel = JavaVersion.VERSION_1_10
    //}

    javafx11 = getBooleanPropWithDefault("javafx11", false)
    //standalone test debug related settings - none - no module is debuged
    testDebugModule = getStringPropWithDefault("testDebugModule", "none")

    // Flag to indicate that brief output should be used in build output
    // For Team City use brief output by default
    briefOutput = getBooleanPropWithDefault("briefOutput", System.getenv('TEAMCITY_VERSION') != null ? true : false)

    // flags for building optional items
    codeCoverage = getBooleanPropWithDefault('codeCoverage', false)
    // Used to define the Title of the code coverage report
    codeCoverageTitle = getStringPropWithDefault('codeCoverageTitle', 'ENC Test Code Coverage Report')
    // The name of the jacoco exec file to use for JVM under analysis.
    // At the time of the definition of this variable this applies only to starting the mediation server.
    codeCoverageOutputFile = getStringPropWithDefault('codeCoverageOutputFile', 'enc.exec')

    // Inclusion/Exclusion comma separated lists of project paths to include or exclude from code coverage analysis
    codeCoverageProjectInclusions = getStringPropWithDefault('codeCoverageProjectInclusions', '')
    // Exclude defaults to projects that do not contribute to production artifacts.
    codeCoverageProjectExclusions = getStringPropWithDefault('codeCoverageProjectExclusions', [
            // ENC development tools
            ':modules:tools:CycleDiff',
            ':modules:tools:propup',
            ':modules:tools:SonarToTCReporter',
            ':modules:tools:SourceRepair',
            ':modules:tools:StaticClasspathAnalyzer',
            ':modules:provision:Src:Config',
            ':modules:provision:Src:ModelConstantGenerator',
            // Projects containing test code only
            ':modules:mdtest',
            ':modules:test_utils',
            ':modules:tfw_sit',
            ':modules:provision:Src:Tests:AAAMock',
            ':modules:provision:Src:Tests:AlarmMock',
            ':modules:provision:Src:Tests:ConfigMock',
            ':modules:provision:Src:Tests:HealthMock',
            ':modules:provision:Src:Tests:HibernateRamMock',
            ':modules:provision:Src:Tests:IntScaleTest',
            ':modules:provision:Src:Tests:IntStressTest',
            ':modules:provision:Src:Tests:L18nMock',
            ':modules:provision:Src:Tests:LicenseMock',
            ':modules:provision:Src:Tests:PreferencesMock',
            ':modules:provision:Src:Tests:RestTestUtils',
            ':modules:provision:Src:Tests:SnMock',
            ':modules:provision:Src:Tests:TaskDispatcherMock',
            ':modules:provision:Src:Tests:TaskMock',
            ':modules:provision:Src:Tests:UIClientTests',
            ':modules:provision:Src:Tests:UITest',
    ].join(','))

    // By default don't build GNSS collector
    buildGNSS = getBooleanPropWithDefault('buildGNSS', false)
    //By default don't attach this module - it is for testing purpose only
    attachMo2CsmTest = getBooleanPropWithDefault('attachMo2CsmTest', false)

    // Apply only the specified modules for debug in mediation server
    mediationDebug = getStringPropWithDefault('mediationDebug', 'mediation.jar,nmscommon.jar')

    aspectjWeave = getBooleanPropWithDefault('aspectjWeave', true)

    prioritizeTasks = getBooleanPropWithDefault('prioritizeTasks', false)

    buildPlatform = getBooleanPropWithDefault('buildPlatform', false)

    // sub-parameters that are set based on platform setting
    if (buildPlatform) {
        platformDirName = "platforms"
        serverParentDirName = "server"
        serverDirName = "$serverParentDirName/fsp_nm"
        clientDirName = "client"
        serverPath = "$platformDirName/$serverDirName"
        clientPath = "$platformDirName/$clientDirName"
        platformDir = new File(fspRoot, platformDirName)
        serverRoot = new File(platformDir, serverDirName)
        serverLibs = new File(serverRoot, "lib")
        clientRoot = new File(platformDir, clientDirName)
    } else {
        // Defaults - built to FSP root workspace directory
        platformDirName = ""
        serverParentDirName = ""
        serverDirName = ""
        clientDirName = ""
        serverPath = ""
        clientPath = ""
        platformDir = fspRoot
        serverRoot = fspRoot
        serverLibs = new File(fspRoot, "lib")
        clientRoot = fspRoot
    }

    // Set default based on if running with PV
    String DefaultMemory = "4G"
    MediationRuntimeMemory = getStringPropWithDefault("MediationRuntimeMemory", DefaultMemory)

    // Username to use for generated copyright headers. Defaults to username from idea.
    // In some cases IDEA may use the wrong user name and this will need to be replaced
    copyrightUserName = getStringPropWithDefault("copyrightUserName", "\$username.toLowerCase()")

    // The build-gradle.xml file will pass these variables on as -D options from the team city
    // production build job, but if any variable is not set this will case the ant build to
    // fail with a 'cyclic' variable declaration since the value of the variables will be
    // set as '${VAR_NAME}'. If this occurs the variable is not really set so just clear it.
    [
            "fnm_svn_revision", "cac_svn_revision", "build.vcs.number.build_fnm_trunk",
            "build.vcs.number.nms_fnm_trunk_dist_FnmTrunk", "build.number",
            "file.encoding", "increase.build.number",
    ].each { var ->
        String value = System.getProperty(var)
        if (value != null && value.equals('${' + var + '}')) {
            System.clearProperty(var)
        }
    }

    // Team City will set the fnm_svn_revision cac_snv_revision values when building the workspace
    // Pass them on as the default,
    FnmRevision = getStringPropWithDefault("FnmRevision", System.getProperty("fnm_svn_revision", "Unknown"))
    CacRevision = getStringPropWithDefault("CacRevision", System.getProperty("cac_svn_revision", "Unknown"))


    // If true then build gradle plugins from local sources
    // If false then use pre-built plugins from artifactory
    PluginDeveloper = getBooleanPropWithDefault("PluginDeveloper", false)

    // If true then aspectj and eclipse link static (compile time) weaving will be used,
    // if false then dynamic (load time or run time) weaving will be used.
    weaveEnc = getBooleanPropWithDefault("weaveEnc", true)

    // Project level directories used by IDEA
    ideaProjectOutputPath = 'out/production'
    ideaProjectResourcePath = "$ideaProjectOutputPath/resources"
    ideaProjectClassesPath = "$ideaProjectOutputPath/classes"

    // generates a reduced Idea project.
    // supported values:
    // - webngsim: for development of webng in simulation mode
    smallIdea = getStringPropWithDefault("smallIdea", System.getProperty("smallIdea", ""))

    // Location where FSP Mediation server web apps live
    fspWebAppWarPath = "$fspRoot/ws/webapps"
    // Location where webapps are installed waiting to be deployed
    fspDeployPath = "$fspRoot/ws/deploy"
    // Location of Jetty Home for FSP embedded Jetty server
    fspWebAppDeployPath = "$fspRoot/var/web"
    // Location where jar wrapper launchers are found
    launchWrappersDir = "$fspRoot/build/launchers"

    JDK = getStringPropWithDefault("JDK", System.getenv("JAVA_HOME"))

    // These options are not supported by FSP build so hard code them here until support is added.
    // buildOptional = getBooleanPropWithDefault("buildOptional", true)
    // IDE = getStringPropWithDefault("IDE", "idea")
    // REMOVED but could be reintroduced in the future for improved build performance
//    buildOptional = true
    IDE = 'idea'

    // The JDK to use for the project created from the gradle idea plugin
    IdeaJDK = getStringPropWithDefault("IdeaJDK", "DevJdk")

    // The number of mbytes to use for IDEA compile process when using gradle idea plugin to generate project
    IdeaCompileMemory = getIntegerPropWithDefault("IdeaCompileMemory", 4096)

    // Specify if project VCS should be set to Subversion.
    // This may need to be turned off if regenerating the project file and the project has changes
    ConfigProjectVcs = getBooleanPropWithDefault("ConfigureVcs", true)

   //defaultPort
    defaultPort = getStringPropWithDefault('defaultPort', '8080')


    // Artifactory parameters
    artifactoryServer = getStringPropWithDefault('artifactoryServer', "gdn-artifactory.rd.advaoptical.com")
    artifactoryReadUser = getStringPropWithDefault('artifactoryReadUser', "nms-read")
    artifactoryReadPassword = getStringPropWithDefault('artifactoryReadPassword', "AP3R5AB6tkeK2ccxfnHdULcRdRBdHqrpmvcMtu")

    // Properties used when publishing NMS artifacts
    PublicationNmsGroup = getStringPropWithDefault('PublicationNmsGroup', "com.adva")
    artifactoryPublicationUser = getStringPropWithDefault('artifactoryPublicationUser', "nms")
    artifactoryPublicationPassword = getStringPropWithDefault('artifactoryPublicationPassword', "NetworkManager")

    // Restored these variables until enc-dw-w can be converted to use project-module plugin (migrated to build-flags.gradle)
    publicationHost = getStringPropWithDefault('publicationHost', 'gdn-s-sitnms1.advaoptical.com')
    publicationUser = getStringPropWithDefault('publicationUser', 'publisher')
    publicationPrivateCertificate = getStringPropWithDefault('publicationPrivateCertificate', "Unknown")

    // Filter for which projects to include for sonarqube analysis (comma separated list of gradle project paths)
    // If set only those projects will be processed, if not set (i.e. empty) then use default set of projects to process.
    sonarIncludeProjects = getStringPropWithDefault('sonarIncludeProjects', '')

    // Filter for which projects to exclude from sonarqube analysis (comma separated list of gradle project paths)
    // If set then the specified projects will be excluded from processing, if not set (i.e. empty) then use default set of projects to process.
    // At this time there are many projects that are not compatible with sonarqube and are used in default exclusions
    sonarExcludeProjects = getStringPropWithDefault('sonarExcludeProjects', "")

    // Closure designed to strip off path from branch, so refs/heads/master becomes 'master'
    sonarBranchFilter = { String branch ->
        int index = branch.lastIndexOf("/")
        if (index < 0) {
            return branch;
        } else {
            return branch.substring(index+1)
        }
    }

    sonarTargetBranch = sonarBranchFilter(getStringPropWithDefault('sonarTargetBranch', 'master'))
    //Defines parameter to run scan on community edition
    sonarEnterprise = getStringPropWithDefault('sonarEnterprise', 'true')

    // Fill the sonarExclusions property to bypass javascript analysis: **/*.js,**/*.jsx,**/*.cjs,**/*.mjs,**/*.vue,**/*.ts,**/*.tsx,**/*.cts,**/*.mts,**/*.html,**/*.htm,**/*.yaml
    sonarExclusions = getStringPropWithDefault('sonarExclusions', '')

    // Defines paremeters required to trigger a 'pull request' analysis in sonarqube.
    // Pull request analysis only runs analysis on the branch changes.
    sonarDevBranch = sonarBranchFilter(getStringPropWithDefault('sonarDevBranch', ''))
    sonarParentBranch = sonarBranchFilter(getStringPropWithDefault('sonarParentBranch', ''))
    sonarIncrementalID = getStringPropWithDefault('sonarIncrementalID', '')
    sonarBranchChanges = []

    if (rootProject instanceof Project) {
        if (!sonarDevBranch.isEmpty() && !sonarParentBranch.isEmpty()) {
            // Prepare for sonar incremental analysis.

            // Set the sonarIncrementalID if not configured
            if (sonarIncrementalID.isEmpty()) {
                sonarIncrementalID = "git rev-parse HEAD".execute().text
            }

            // Collect files changed for use later
            // NOTE: user remote origin for diffs for two reasons.
            // 1. The checkout directory is sparse so the parent branch will not be local
            // 2. The local branch may be merged with master adding addition changes which make sonar analysis larger
            sonarBranchChanges = "git diff --name-only remotes/origin/${sonarParentBranch}...remotes/origin/${sonarDevBranch}"
                    .execute()
                    .getText()
                    .split("\\s+")
                    .toList()
        }
    }

    if (rootProject instanceof Project) {
        // Parameters.gradle is also included in settings.gradle but these values are not applicable there
        // and this code won't work there, so only apply when working with a Project and not a ProjectDescriptor
        sonarGradleProjects = new HashSet<>()

        if (sonarIncludeProjects.isEmpty()) {
            // Use all projects
            rootProject.allprojects.each { p ->
                sonarGradleProjects.add(p.path)
            }
        } else {
            for (String path : sonarIncludeProjects.split(',')) {
                String fullPath = path.trim()
                String ipath = ""
                // For a project to execute need to include the full path to the project, so add parent projects too
                for (String part : fullPath.split(':')) {
                    if (!ipath.endsWith(':')) {
                        ipath += ':'
                    }
                    ipath += part
                    sonarGradleProjects.add(ipath)
                }
            }
        }

        if (!sonarExcludeProjects.isEmpty()) {
            for (String path : sonarExcludeProjects.split(',')) {
                // Note this will exclude the provided project and all sub-projects
                sonarGradleProjects.remove(path.trim())
            }
        }
    }

    // ENC specific plugin settings
    pluginGroup = 'com.adva.gradle.plugin'
    pluginVersion = '4.1.6'

    // Database parameters used for DB configuration
    dbName = getStringPropWithDefault('dbName', "fnm")
    dbServer = getStringPropWithDefault('dbServer', "localhost")

    dbAdminID = getStringPropWithDefault('dbAdminID', "root")
    dbAdminPW = getStringPropWithDefault('dbAdminPW', "ChgMeNOW")
    dbAdmin = [
            db: dbName,
            user: dbAdminID,
            password: dbAdminPW,
    ]

    dbUserID = getStringPropWithDefault('dbUserID', "adva")
    dbUserPW = getStringPropWithDefault('dbUserPW', "NeverChange")
    dbUser = [
            db: dbName,
            user: dbUserID,
            password: dbUserPW,
    ]

    // Location of installed postgres application executables
    pgCmd = "$fspRoot/postgres/bin/psql"
    pgDumpCmd = "$fspRoot/postgres/bin/pg_dump"
    pgDropCmd = "$fspRoot/postgres/bin/dropdb"
    pgCreateCmd = "$fspRoot/postgres/bin/createdb"

    // Dabase Manipulation Methods available build system wide

    dbCommand = { Map<String, String> auth, String command ->

        String db = auth.db
        String user = auth.user
        String pw = auth.password

        String[] cmd = [ pgCmd, "-U", user, "-c", command, "--set", "ON_ERROR_STOP=on", db ]
        ProcessBuilder pb = new ProcessBuilder( cmd )
        pb.redirectErrorStream(true)
        pb.environment().put("PGPASSWORD", pw)
        Process p = pb.start()

        int result = p.waitFor()
        String output = p.getText()

        processDbResult(result, cmd, output)

        return output
    }

    dbScript = { Map<String, String> auth, script ->

        String scriptPath

        if (script instanceof File) {
            scriptPath = script.getCanonicalPath()
        } else {
            scriptPath = script
        }

        String db = auth.db
        String user = auth.user
        String pw = auth.password

        String[] cmd = [ pgCmd, "-U", user, "-f", scriptPath, "--set", "ON_ERROR_STOP=on", db ]
        ProcessBuilder pb = new ProcessBuilder( cmd )
        pb.redirectErrorStream(true)
        pb.environment().put("PGPASSWORD", pw)
        Process p = pb.start()

        int result = p.waitFor()
        String output = p.getText()

        processDbResult(result, cmd, output)

        return output
    }

    dropDB = { Map<String, String> auth, String db ->
        String user = auth.user
        String pw = auth.password

        String[] cmd = [ pgDropCmd, "-U", user, db ]
        ProcessBuilder pb = new ProcessBuilder( cmd )
        pb.redirectErrorStream(true)
        pb.environment().put("PGPASSWORD", pw)
        Process p = pb.start()

        int result = p.waitFor()
        String output = p.getText()

        processDbResult(result, cmd, output)

        return output
    }

    createDB = { Map<String, String> auth, String db ->
        String user = auth.user
        String pw = auth.password

        String[] cmd = [ pgCreateCmd, "-U", user, db ]
        ProcessBuilder pb = new ProcessBuilder( cmd )
        pb.redirectErrorStream(true)
        pb.environment().put("PGPASSWORD", pw)
        Process p = pb.start()

        int result = p.waitFor()
        String output = p.getText()

        processDbResult(result, cmd, output)

        return output
    }

    dumpDB = { Map<String, String> auth, String db, String outfile ->
        String user = auth.user
        String pw = auth.password

        String[] cmd = [ pgDumpCmd, "-U", user, "-f", outfile, db ]
        ProcessBuilder pb = new ProcessBuilder( cmd )
        pb.redirectErrorStream(true)
        pb.directory(fspRoot)
        pb.environment().put("PGPASSWORD", pw)
        Process p = pb.start()

        int result = p.waitFor()
        String output = p.getText()

        processDbResult(result, cmd, output)

        return output
    }

    processDbResult = { int result, String[] cmd, String output ->
        // Process psql result codes
        if (result == 0) {
            return
        } else if (result == 1) {
            throw new RuntimeException("Database fatal error for command $cmd\n$output\n");
        } else if (result == 2) {
            throw new RuntimeException("Database connection error for command $cmd\n$output\n");
        } else if (result == 3) {
            throw new RuntimeException("Database command error for command $cmd\n$output\n");
        } else {
            throw new RuntimeException("Database error for command $cmd\n$output\n");
        }
    }

    // convert a configuration to a classpath suitable for use in a jar manifest file
    config2ManifestClasspath = { Configuration c ->
        return c.collect {
            it.toURI().toString()
        }.join(' ')
    }

    // convert configuration to a simple list of class files without path or version info.
    config2FlatManifestClasspath = { FileCollection c ->
        return c.collect {
            stripVersion(it.getName())
        }.join(' ')
    }

    // Adva libs use the module name as the version name so the jar artifacts
    // end up with names like 'module-module.jar'. The map here provides the
    // key to renaming these files.
    customRenameMap = [
            "classycle-classycle.jar"                    : "classycle.jar",
            "emplugin-emplugin.jar"                      : "emplugin.jar",
            "chart-chart.jar"                            : "chart.jar",
            "jasperreports-fonts-jasperreports-fonts.jar": "jasperreports-fonts.jar",
            "jxl-jxl.jar"                                : "jxl.jar",
            "classmexer-classmexer.jar"                  : "classmexer.jar",
            "mockito-mockito.jar"                        : "mockito.jar",
            "spring-test-spring-test.jar"                : "spring-test.jar",
            "xmlunit-xmlunit.jar"                        : "xmlunit.jar",
            "org.sadun.util-org.sadun.util.jar"          : "org.sadun.util.jar",

            // This jar has a name that looks like a version, so just trim off the correct amount
            // moved to dependencies.gradle so we can use ver_log4j variable instead of literal
            // "log4j-1.2-api-2.10.0.jar"                   : "log4j-1.2-api.jar",
    ]

    stripVersion = { String name ->
        if (RetainJarVersionInfo) {
            return name
        } else {
            return stripVersionWithMap(name, customRenameMap)
        }
    }

    stripVersionWithMap = { String name, Map<String, String> renameMap ->
        // Special case for this adva lib which is called out by a specific name in build.xml
        // This may be removed when ant files are removed.
        String value = renameMap.get(name)
        if (value != null) {
            return value
        }

        int extPos = name.lastIndexOf(".")
        if (extPos < 0) {
            // Nothing to do (does not look like a jar), maybe this is a directory
            return name;
        }

        String ext = name.substring(extPos, name.length())
        String pre = name.substring(0, extPos)

        String[] parts = pre.split("-")

        if (parts.length > 1) {
            pre = null

            for (String part : parts) {
                // Add parts back on until we see something that looks like a version
                // Unfortunately version numbering is hard to pin down, so look for anything that starts with a number
                if (part.matches("^\\d.*")) {
                    break;
                }

                if (pre == null) {
                    pre = part
                } else {
                    pre += "-" + part
                }
            }
        }

        return pre + ext
    }

    optionalBuildItems = new ArrayList<Closure>()

    // method to accept closure containing optional build items
    /* REMOVED optional functionality. This paradigm was introduced initially for PV but removed
     * when PV integrated into ENC since ENC did not use it and it caused build issued.
     *
    optional = { Closure c ->
        optionalBuildItems.add(c)
    }
     */

    eclipseBuildItems = new ArrayList<Closure>()

    // method to accept closure containing eclipse specific build items
    eclipseOnly = { Closure c ->
        eclipseBuildItems.add(c)
    }

    ideaBuildItems = new ArrayList<Closure>()

    // method to accept closure containing idea specific build items
    ideaOnly = { Closure c ->
        ideaBuildItems.add(c)
    }

    // publication artifactory repository configuration
    publicationRepository = { RepositoryHandler repositoryHandler ->
        repositoryHandler.maven {
            name = 'nmsartifactory'

            url = "https://$artifactoryServer/artifactory/NMS"

            credentials {
                username artifactoryPublicationUser
                password artifactoryPublicationPassword
            }
        }
    }
}
