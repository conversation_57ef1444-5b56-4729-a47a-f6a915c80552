::
:: Copyright 2023 Adtran Networks SE. All rights reserved.
::
:: Owner: hoehme
::
:: $Id: SetVMOptions.bat 68162 2013-04-06 16:54:31Z hoehme $
::

echo off
setlocal enabledelayedexpansion
set CURRENTDIR=%~dp0
if exist "%CURRENTDIR%jre64\bin\java.exe" (
  set PRUN_HOME="%CURRENTDIR%prunsrv64.exe"
) else (
  echo.
  echo jre64 folder not found.
  echo.
  echo Changing VM options for 32-bit systems is not supported.
  GOTO:EOF
)

SET vmoptions=""
For /F "usebackq" %%A IN ("%CURRENTDIR%fspnm.vmoptions") DO (
  IF !vmoptions! == "" (
    SET vmoptions=--JvmOptions=%%A;
  ) ELSE (
    SET vmoptions=!vmoptions!%%A;
  )
)

%PRUN_HOME% //US//advams "!vmoptions!"
endlocal
