/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  owner: tedd
 *
 *  ____  _____ ____  ____  _____ ____    _  _____ _____ ____
 * |  _ \| ____|  _ \|  _ \| ____/ ___|  / \|_   _| ____|  _ \
 * | | | |  _| | |_) | |_) |  _|| |     / _ \ | | |  _| | | | |
 * | |_| | |___|  __/|  _ <| |__| |___ / ___ \| | | |___| |_| |
 * |____/|_____|_|   |_| \_\_____\____/_/   \_\_| |_____|____/
 *
 *  PLEASE ADD NEW DEPENDENCY DEFINITIONS TO "libs.versions.toml"
 *  See the link below for additional information on the gradle version catalog:
 *  https://docs.gradle.org/current/userguide/platforms.html
 */

import org.gradle.internal.os.OperatingSystem

ext {

    def platform
    if (OperatingSystem.current().isLinux()) {
        platform = "linux"
    } else if (OperatingSystem.current().isMacOsX()) {
        platform = "mac"
    } else {
        platform = "win"
        platform = "win"
    }

// Dependency definitions

    dep_enc_cmsw = [group: "com.adva.enc.cmsw", name: "cmsw", version: "4.01.11", ext: "zip", transitive: true]
    dep_enc_docs = [group: "com.adva.enc.docs", name: "enc-docs", version: "17.1.1.250725", ext: "zip", transitive: true]
    dep_enc_ypdb = [group: "com.adva.ypdb", name: "yp", version: "17.2.1-20250801.123219-7", ext: "zip", transitive: true]
    dep_enc_docs_other = [group: "com.adva.enc.docs", name: "enc-docs-other", version: "14.1.1", ext: "zip", transitive: true]

    dep_enc_eth_help = [group: "com.adva.enc.docs", name: "enc-eth-help", version: "17.1.1.250704", ext: "zip", transitive: true]
    dep_enc_fiber_help = [group: "com.adva.enc.docs", name: "enc-fiber-help", version: "17.1.1.250320", ext: "zip", transitive: true]
    dep_enc_sync_help = [group: "com.adva.enc.docs", name: "enc-sync-help", version: "17.1.1.250611", ext: "zip", transitive: true]
    dep_enc_wdm_help = [group: "com.adva.enc.docs", name: "enc-wdm-help", version: "17.1.1.250711", ext: "zip", transitive: true]
    dep_enc_um_help = [group: "com.adva.enc.docs", name: "enc-um-help", version: "17.1.1.250725", ext: "zip", transitive: true]
    ver_aspectj = "*******"
    // Used in aspectj gradle plugin for ENC
    dep_aspectjtools = [group: "org.aspectj", name: "aspectjtools", version: ver_aspectj, transitive: true]
    dep_commons_betwixt = [group: 'commons-betwixt', name: 'commons-betwixt', version: "0.8", transitive: false]
    dep_copssh = [group: 'net.itefix.copssh', name: 'copssh', version: '5.6.2', classifier: 'installer', ext: 'exe']
    dep_fast_class_scanner = [group: 'io.github.lukehutch', name: 'fast-classpath-scanner', version: '2.18.1', transitive: true]
    ver_findbugs = '3.0.0'
    dep_findbugs_annots = [ group: 'com.google.code.findbugs', name: 'annotations', version: ver_findbugs, transitive: false ]
    dep_findbugs_jsr305 = [ group: 'com.google.code.findbugs', name: 'jsr305', version: ver_findbugs, transitive: false ]
    ver_flexnet = '2022.02'
    dep_flexnet_winFlxCore = [group: 'com.flexnet', name: 'FlxCore64', version: ver_flexnet, ext: 'dll', transitive: true]
    dep_flexnet_linFlxCore = [group: 'com.flexnet', name: 'libFlxCore64', version: ver_flexnet, ext: 'so.' + ver_flexnet, transitive: true]

    ver_javafx = '17.0.7'

    // Used in build dependencies, assumes that target platform is platform building on
    String javafxPlatformClassifier = OperatingSystem.current().isLinux() ? 'linux' : 'win'

    dep_javafx_base = [
            [ group: 'org.openjfx', name: 'javafx-base', version: ver_javafx ],
            [ group: 'org.openjfx', name: 'javafx-base', version: ver_javafx, classifier: javafxPlatformClassifier ],
    ]
    dep_javafx_controls = [
            [ group: 'org.openjfx', name: 'javafx-controls', version: ver_javafx ],
            [ group: 'org.openjfx', name: 'javafx-controls', version: ver_javafx, classifier: javafxPlatformClassifier ],
    ]
    dep_javafx_fxml = [
            [ group: 'org.openjfx', name: 'javafx-fxml', version: ver_javafx ],
            [ group: 'org.openjfx', name: 'javafx-fxml', version: ver_javafx, classifier: javafxPlatformClassifier ],
    ]
    dep_javafx_swing = [
            [ group: 'org.openjfx', name: 'javafx-swing', version: ver_javafx ],
            [ group: 'org.openjfx', name: 'javafx-swing', version: ver_javafx, classifier: javafxPlatformClassifier ],
    ]
    dep_javafx_graphics = [
            [ group: 'org.openjfx', name: 'javafx-graphics', version: ver_javafx ],
            [ group: 'org.openjfx', name: 'javafx-graphics', version: ver_javafx, classifier: javafxPlatformClassifier ],
    ]
    dep_javafx_media = [
            [ group: 'org.openjfx', name: 'javafx-media', version: ver_javafx ],
            [ group: 'org.openjfx', name: 'javafx-media', version: ver_javafx, classifier: javafxPlatformClassifier ],
    ]
    dep_javafx_web = [
            [ group: 'org.openjfx', name: 'javafx-web', version: ver_javafx ],
            [ group: 'org.openjfx', name: 'javafx-web', version: ver_javafx, classifier: javafxPlatformClassifier ],
    ]


    // Definitions used by frontend build to create an appropriate jar manifest for launching
    dep_javafx_win = [
            [ group: 'org.openjfx', name: 'javafx-base', version: ver_javafx, classifier: 'win' ],
            [ group: 'org.openjfx', name: 'javafx-controls', version: ver_javafx, classifier: 'win' ],
            [ group: 'org.openjfx', name: 'javafx-fxml', version: ver_javafx, classifier: 'win' ],
            [ group: 'org.openjfx', name: 'javafx-swing', version: ver_javafx, classifier: 'win' ],
            [ group: 'org.openjfx', name: 'javafx-graphics', version: ver_javafx, classifier: 'win' ],
            [ group: 'org.openjfx', name: 'javafx-media', version: ver_javafx, classifier: 'win' ],
            [ group: 'org.openjfx', name: 'javafx-web', version: ver_javafx, classifier: 'win' ],
    ]

    dep_javafx_linux = [
            [ group: 'org.openjfx', name: 'javafx-base', version: ver_javafx, classifier: 'linux' ],
            [ group: 'org.openjfx', name: 'javafx-controls', version: ver_javafx, classifier: 'linux' ],
            [ group: 'org.openjfx', name: 'javafx-fxml', version: ver_javafx, classifier: 'linux' ],
            [ group: 'org.openjfx', name: 'javafx-swing', version: ver_javafx, classifier: 'linux' ],
            [ group: 'org.openjfx', name: 'javafx-graphics', version: ver_javafx, classifier: 'linux' ],
            [ group: 'org.openjfx', name: 'javafx-media', version: ver_javafx, classifier: 'linux' ],
            [ group: 'org.openjfx', name: 'javafx-web', version: ver_javafx, classifier: 'linux' ],
    ]

    dep_javafx_core = [
            [ group: 'org.openjfx', name: 'javafx-base', version: ver_javafx ],
            [ group: 'org.openjfx', name: 'javafx-controls', version: ver_javafx ],
            [ group: 'org.openjfx', name: 'javafx-fxml', version: ver_javafx ],
            [ group: 'org.openjfx', name: 'javafx-swing', version: ver_javafx ],
            [ group: 'org.openjfx', name: 'javafx-graphics', version: ver_javafx ],
            [ group: 'org.openjfx', name: 'javafx-media', version: ver_javafx ],
            [ group: 'org.openjfx', name: 'javafx-web', version: ver_javafx ],
    ]
    // Used for production build to include all platform jar files in install
    dep_javafx_all = dep_javafx_linux + dep_javafx_win + dep_javafx_base

    // Need for ant based unit tests so that ivy-classapath*.properties files are built as expected
    // otherwise javafx libraries are handled by the javafx plugin.
    // This can probably be removed when tests move to gradle instead of ant.
    dep_javafx = dep_javafx_base

    // Add in platform specific items
    if (OperatingSystem.current().isLinux()) {
        dep_javafx += dep_javafx_linux
    } else {
        dep_javafx += dep_javafx_win
    }//    ver_jaxb = "2.4.0-b180830.0359"
    dep_jsr303 = [
            'jakarta.validation:jakarta.validation-api:3.1.0',
            'org.hibernate.validator:hibernate-validator:8.0.1.Final',
            'org.glassfish.expressly:expressly:5.0.0'
    ]

    dep_svp = [group: 'com.adva.enc.svp', name: 'svp', version: '1.0', ext: 'zip']

    dep_win32_libraries = [group: 'com.adva.enc.development.win32', name: 'libraries', version: '1.0', ext: 'zip']
    dep_win32_postgres = [group: 'com.adva.enc.development.win32', name: 'postgres-bin', version: '10.4.1', ext: 'zip']
    dep_win64_postgres = [group: 'com.adva.enc.development.win64', name: 'postgres-bin', version: '17.4', ext: 'zip']
    dep_win_filezilla = [group: 'com.adva.enc.development.win32', name: 'filezilla-server', version: '********', ext: 'exe']

// Dependency group definitions

    dgroup_client_compile = [
            libs.activemq.client,
            libs.activemq.spring,
            libs.jgoodies.binding,
            libs.castor,
            libs.chart,
            libs.commons.collections4,
            libs.commons.lang3,
            libs.commons.text,
            libs.controlsfx,
            // Include desired javafx libraries for ant based test classpath
            // Can probably remove when no longer using ant tests
            dep_javafx,
            libs.eclipselink,
            libs.emplugin,
            libs.externalsortinginjava,
            libs.forms,
            libs.jakarta.jms.api,
            libs.guava,
            libs.h2,
            libs.jackson.databind,
            libs.jasper,
            libs.jasperreports.pdf,
            libs.jasperreports.charts,
            libs.jasperreports.ant,
            libs.jakarta.annotation.api,
            libs.jakarta.ws.rs.api,
            libs.jakarta.xml.bind.api,
            libs.jcommon,
            libs.jersey.client,
            libs.jettison,
            libs.jul.to.slf4j,
            libs.jxbrowser,
            libs.jxbrowser.javafx,
            libs.jxbrowser.linux64,
            libs.jxbrowser.win64,
            libs.jxbrowser.win32,
            libs.jxl,
            libs.jxpath,
            libs.l2fprod.common.all,
            libs.log4j12.api,
            libs.log4j.api,
            libs.org.netbeans.api.visual,
            libs.parser,
            libs.slf4j.api,
            libs.spring.beans,
            libs.spring.context,
            libs.spring.jms,
            libs.validation,
            libs.xercesImpl,
            libs.xml.apis,
            libs.yfiles.for.javafx,
            libs.jfoenix.javafx
    ]

    dgroup_client_runtime = [
            libs.activemq.broker,
            libs.activemq.jms.pool,
            libs.activemq.openwire.legacy,
            libs.activemq.pool,
            libs.activemq.protobuf,
            libs.commons.collections4,
            libs.commons.digester,
            libs.geronimo.j2ee.management.spec,
            libs.hawtbuf,
            libs.jasperreports.fonts,
            libs.jersey.media.jaxb,
            libs.jersey.hk2,
            libs.log4j.slf4j.impl,
            libs.micrometer.observation,
            libs.openpdf,
            libs.spring.aop,
            libs.spring.expression,
            libs.spring.jdbc,
            libs.spring.orm,
            libs.woodstox.core,
            libs.yfiles.license,
    ]

    dgroup_common_compile = [
            libs.jgoodies.binding,
            libs.castor,
            libs.commons.collections4,
            libs.commons.collections4,
            libs.commons.lang3,
            libs.commons.logging,
            libs.commons.net,
            libs.controlsfx,
            // Include desired javafx libraries for ant based test classpath
            // Can probably remove when no longer using ant tests
            dep_javafx,
            libs.eclipselink,
            libs.forms,
            libs.guava,
            libs.jackson.annotations,
            libs.jackson.core,
            libs.jackson.databind,
            libs.jackson.json.provider,
            libs.jakarta.activation,
            libs.jakarta.annotation.api,
            libs.jakarta.persistence,
            libs.jakarta.ws.rs.api,
            libs.jakarta.xml.bind.api,
            libs.jaxb.impl,
            libs.jersey.common,
            libs.jersey.guava,
            libs.jersey.media.json.jackson,
            libs.jxpath,
            libs.l2fprod.common.all,
            libs.log4j.api,
            libs.jakarta.mail,
            libs.metrics.core,
            libs.org.netbeans.api.visual,
            libs.parser,
            libs.protobuf.java,
            libs.protobuf.java.util,
            libs.jakarta.servlet.api,
            libs.slf4j.api,
            libs.snmp4j,
            libs.spring.beans,
            libs.spring.context,
            libs.spring.core,
            libs.spring.tx,
            libs.spring.web,
            libs.validation,
            libs.jakarta.validation.api,
            libs.xercesImpl,
            libs.xml.apis,
            libs.yfiles.for.javafx,
            libs.commons.compress,
            libs.jfoenix.javafx,
    ]

    dgroup_common_runtime = [
            libs.activemq.openwire.legacy,
            libs.hawtbuf,
            libs.log4j.slf4j.impl,
            libs.spring.expression,
    ]

    dgroup_coverage = [
            libs.jacocoagent.enc,
    ]

    dgroup_jms_broker = [
            libs.activeio.core,
            libs.activemq.broker,
            libs.activemq.client,
            libs.activemq.kahadb.store,
            libs.activemq.openwire.legacy,
            libs.activemq.protobuf,
            libs.activemq.spring,
            libs.commons.net,
            libs.geronimo.j2ee.management.spec,
            libs.jakarta.jms.api,
            libs.hawtbuf,
            libs.jolokia.jvm.agent,
            libs.log4j12.api,
            libs.log4j.api,
            libs.log4j.iostreams,
            libs.log4j.slf4j.impl,
            libs.postgresql,
            libs.slf4j.api,
            libs.spring.aop,
            libs.spring.beans,
            libs.spring.context,
            libs.spring.core,
            libs.spring.expression,
            libs.spring.jms,
            libs.spring.tx,
    ]

    dgroup_long = [
            libs.classycle,
            libs.javaparser,
            libs.javassist,
            libs.jersey.test.framework.core,
            libs.jersey.test.framework.provider.inmemory,
            libs.jersey.test.framework.provider.grizzly2,
            libs.jersey.hk2,
            libs.org.sadun.util,
            libs.spring.test,
            libs.xmlunit,
    ]

    dgroup_nms_admin = [
            libs.log4j12.api,
            libs.log4j.api,
            libs.postgresql,
            libs.jersey.client,
            libs.jersey.common,
            libs.jersey.hk2,
            libs.jettison,
            libs.jetty.proxy,
            libs.jakarta.ws.rs.api,
            libs.protobuf.java,
            libs.protobuf.java.util,
            libs.nms.ni.model,
            libs.slf4j.api,
            libs.commons.collections4,
            libs.commons.lang3,
            libs.commons.compress,
            libs.log4j.slf4j.impl,
            libs.jakarta.xml.bind.api,
    ]

    dgroup_server_compile = [
            libs.commons.compress,
            libs.hikaricp,
            libs.activemq.broker,
            libs.activemq.client,
            libs.adva.mtosi,
            libs.jgoodies.binding,
            libs.castor,
            libs.commons.beanutils,
            libs.commons.text,
            libs.commons.codec,
            libs.commons.collections4,
            libs.commons.lang3,
            libs.commons.logging,
            libs.commons.net,
            libs.eclipselink,
            libs.flexnet.eccpresso,
            libs.flexnet.flxBinary,
            libs.flexnet.flxClient,
            libs.flexnet.flxClientNative,
            libs.jakarta.jms.api,
            libs.guava,
            libs.h2,
            libs.jackson.annotations,
            libs.jackson.core,
            libs.jackson.json.provider,
            libs.jackson.databind,
            libs.jakarta.activation,
            libs.jakarta.annotation.api,
            libs.jakarta.persistence,
            libs.jakarta.ws.rs.api,
            libs.jakarta.xml.bind.api,
            libs.jaxb.impl,
            libs.jaxb.xjc,
            libs.jaxws.api,
            libs.jakarta.jws.api,
            libs.jdom2,
            libs.jersey.client,
            libs.jersey.common,
            libs.jersey.guava,
            libs.jersey.media.json.jackson,
            libs.jersey.server,
            libs.jettison,
            libs.jetty.proxy,
            libs.jradius.core,
            libs.jradius.dictionary,
            libs.jsch,
            libs.jul.to.slf4j,
            libs.jxl,
            libs.log4j12.api,
            libs.log4j.api,
            libs.log4j.iostreams,
            libs.log4j.jcl,
            libs.metrics.core,
            libs.metrics.jmx,
            libs.nms.ni.model,
            libs.parser,
            libs.postgresql,
            libs.protobuf.java,
            libs.protobuf.java.util,
            libs.radclient,
            libs.jakarta.servlet.api,
            libs.slf4j.api,
            libs.snmp4j,
            libs.spring.beans,
            libs.spring.context,
            libs.spring.core,
            libs.spring.jms,
            libs.spring.tx,
            libs.spring.web,
            libs.tacclient,
            libs.jakarta.validation.api,
            libs.velocity,
            libs.xalan,
            libs.xalan.serializer,
            libs.xercesImpl,
            libs.xml.apis,
            libs.jakarta.xml.bind.api,
    ]

    dgroup_server_runtime = [
            libs.activemq.jms.pool,
            libs.activemq.kahadb.store,
            libs.activemq.openwire.legacy,
            libs.activemq.pool,
            libs.activemq.protobuf,
            libs.activemq.spring,
            libs.commons.collections4,
            libs.commons.digester,
            libs.geronimo.j2ee.management.spec,
            libs.hawtbuf,
            libs.jersey.container.servlet,
            libs.jersey.container.servlet.core,
            libs.jersey.hk2,
            libs.jersey.media.jaxb,
            libs.jradius.core,
            libs.jradius.dictionary,
            libs.log4j.slf4j.impl,
            libs.openpdf,
            libs.spring.aop,
            libs.spring.expression,
            libs.spring.jdbc,
            libs.spring.orm,
            libs.yfiles.license,
    ]


    dgroup_tests_compile = [
            libs.activemq.client,
            libs.adva.mtosi,
            libs.awaitility,
            libs.jgoodies.binding,
            libs.castor,
            libs.cglib.nodep,
            libs.classmexer,
            libs.classycle,
            libs.opencsv,
            libs.openpdf,
            libs.commons.beanutils,
            libs.commons.text,
            libs.commons.collections4,
            libs.commons.lang3,
            libs.commons.logging,
            libs.cxf.core,
            libs.diffutils,
            libs.jakarta.jms.api,
            libs.guava,
            libs.hamcrest.core,
            libs.hamcrest.library,
            libs.jackson.annotations,
            libs.jackson.core,
            libs.jackson.core,
            libs.jackson.databind,
            libs.jasper,
            libs.jasperreports.pdf,
            libs.jasperreports.charts,
            libs.jasperreports.ant,
            libs.jakarta.activation,
            libs.jakarta.annotation.api,
            libs.jakarta.persistence,
            libs.jakarta.ws.rs.api,
            libs.jakarta.xml.bind.api,
            libs.jaxb.xjc,
            libs.jdom2,
            libs.jersey.client,
            libs.jersey.common,
//            libs.jersey.hk2,
            libs.jersey.guava,
            libs.jersey.media.json.jackson,
            libs.jersey.server,
            libs.jettison,
            libs.jsch,
            libs.bundles.junit,
            libs.assertJ,
            libs.log4j.api,
            libs.log4j.iostreams,
            libs.log4j.jcl,
            libs.metrics.core,
            libs.nms.ni.model,
            libs.objenesis,
            libs.org.netbeans.api.visual,
            libs.parser,
            libs.postgresql,
            libs.slf4j.api,
            libs.snmp4j,
            libs.spring.aop,
            libs.spring.beans,
            libs.spring.context,
            libs.spring.core,
            libs.spring.web,
            libs.validation,
            libs.velocity,
            libs.xalan,
            libs.xalan.serializer,
            libs.xercesImpl,
            libs.xml.apis,
            libs.yfiles.for.javafx,
    ]

    dgroup_nms_ml_client_implementation = [
            libs.guava,
            mod_sdn_model.project,
            libs.log4j.api,
            libs.slf4j.api,
            libs.jetty.websocket.client,
            libs.commons.lang3
    ]


    dgroup_customer_databases = [
            [group: "com.adva.nlms.databases", name: "SIT_6.4.2 HEAnet dbfnm updated to 8.2.1_2015_04_22.sql", version: "15.3", ext: 'zip', transitive: true],
            [group: "com.adva.nlms.databases", name: "SIT_FNMDB0110_BT_updated to 8.2.1_2015_04_22.sql", version: "15.3", ext: 'zip', transitive: true],
            [group: "com.adva.nlms.databases", name: "dtag", version: "15.3", ext: 'zip', transitive: true],
            [group: "com.adva.nlms.databases", name: "BT_openreach", version: "15.3", ext: 'zip', transitive: true],
//            [group: "com.adva.nlms.databases", name: "pt_lux", version: "14.3", ext: 'zip', transitive: true],
            [group: "com.adva.nlms.databases", name: "teleItal", version: "16.1", ext: 'zip', transitive: true],
            [group: "com.adva.nlms.databases", name: "tata", version: "16.1", ext: 'zip', transitive: true],
            [group: "com.adva.nlms.databases", name: "telstra", version: "16.1.1", ext: 'zip', transitive: true],
    ]

// Global Exclusions
    exclusions = [
            [group: "javax.inject", module: "javax.inject"],
            [group: "javax.xml.stream", module: "stax-api"],
            [group: "org.glassfish", module: "javax.json"],
            [group: "org.eclipse.persistence", module: "commonj.sdo"],
            [group: "com.sun.jdmk", module: "jmxtools"],
            [group: "log4j", module: "log4j"],
            [group: "org.slf4j", module: "slf4j-log4j12"],
            [group: "org.apache.geronimo.specs", module: "geronimo-javamail_1.4_spec"],
            [group: "org.apache.geronimo.javamail", module: "geronimo-javamail_1.4_mail"],
            [group: "commons-configuration", module: "commons-configuration"],
            [group: "org.apache.geronimo.specs", module: "geronimo-jta_1.0.1B_spec"],
            [group: "com.google.errorprone", module: "error_prone_annotations"],
            [group: "org.codehaus.mojo", module: "animal-sniffer-annotations"],
            [group: "com.google.j2objc", module: "j2objc-annotations"],
            // Removed, @Nonnull annotation is widely used and removing this could impact static analsys
            // [group: "com.google.code.findbugs", module: "jsr305"],
            [group: "net.sf.ehcache", module: "ehcache-core"],
            // Exclude spring-jcl which provides an alternate logging implementation
            // than what is provided by commons-logging.  This is bad for Java 9+ migration
            // and the apache commons-logging has the ability to be extended allowing
            // the eclipse link debug plugin logic to work as expected.
            [group: "org.springframework", module: "spring-jcl"],
            [group: "c3p0", module: "c3p0"],
            // We use log4j so don't include logback in our system, it will confuse slf4j
            [group: "ch.qos.logback", module: "logback-classic"],
            [group: "org.apache.xmlgraphics"],
    ]

// Global Overrides
        overrides = [
                [group: "commons-beanutils", module: "commons-beanutils", version: libs.versions.ver.commons.beanutils.get()],
                [group: "org.apache.commons", module: "commons-lang3", version: libs.versions.ver.commons.lang3.get()],
                [group: "commons-logging", module: "commons-logging", version: libs.versions.ver.commons.logging.get()],
                [group: "com.fasterxml.jackson.module", module: "jackson-module-jaxb-annotations", version: libs.versions.ver.jackson.get()],
                [group: "com.fasterxml.jackson.jaxrs", module: "jackson-jaxrs-json-provider", version: libs.versions.ver.jackson.get()],
                [group: "com.fasterxml.jackson.core", module: "jackson-annotations", version: libs.versions.ver.jackson.get()],
                [group: "com.fasterxml.jackson.core", module: "jackson-databind", version: libs.versions.ver.jackson.get()],
                [group: "com.fasterxml.jackson.core", module: "jackson-core", version: libs.versions.ver.jackson.get()],
                [group: "com.google.guava", module: "guava", version: libs.versions.ver.guava.get()],
                [group: "org.aspectj", module: "aspectjrt", version: libs.versions.ver.aspectj.get()],
                [group: "org.aspectj", module: "aspectjweaver", version: libs.versions.ver.aspectj.get()],
                [group: 'com.ibm.icu', module: 'icu4j', version: '60.2'],
                [group: "org.slf4j", module: "slf4j-api", version: libs.versions.ver.slf4j.asProvider().get()],
                [group: "com.google.code.gson", module: 'gson', version: libs.versions.ver.gson.get()],
                [group: "org.apache.commons", module: 'commons-compress', version: libs.versions.ver.commons.compress.get()],
                // Eliminate duplicate activation-api seen in mediation BOM generation from transitive dependencies
                // Activation-api is not used by mediation directly
                [group: 'jakarta.activation', module: 'jakarta.activation-api', version: libs.versions.ver.jakarta.activation.api.get()],
        ]

// Dependency group inheritance
    dgroup_server_runtime.addAll(dgroup_server_compile)
    dgroup_server_runtime.addAll(dgroup_common_compile)

    dgroup_client_runtime.addAll(dgroup_client_compile)
    dgroup_client_runtime.addAll(dgroup_common_compile)
    dgroup_client_runtime.addAll(dgroup_common_runtime)

    dgroup_common_runtime.addAll(dgroup_common_compile)

    dgroup_tests_compile.addAll(dgroup_long)
    dgroup_tests_compile.addAll(dgroup_coverage)

    // This jar has a name that looks like a version, so just trim off the correct amount
    // This definition is placed here instead of parameters.gradle so we have access to ver_log4j variable
    customRenameMap.put("log4j-1.2-api-" + libs.versions.ver.log4j + ".jar", "log4j-1.2-api.jar")

    // The javafx jars need the target platform qualifier to handle them here
    customRenameMap.put("javafx-base-" + ver_javafx + "-linux.jar", "javafx-base-linux.jar")
    customRenameMap.put("javafx-controls-" + ver_javafx + "-linux.jar", "javafx-controls-linux.jar")
    customRenameMap.put("javafx-fxml-" + ver_javafx + "-linux.jar", "javafx-fxml-linux.jar")
    customRenameMap.put("javafx-graphics-" + ver_javafx + "-linux.jar", "javafx-graphics-linux.jar")
    customRenameMap.put("javafx-media-" + ver_javafx + "-linux.jar", "javafx-media-linux.jar")
    customRenameMap.put("javafx-swing-" + ver_javafx + "-linux.jar", "javafx-swing-linux.jar")
    customRenameMap.put("javafx-web-" + ver_javafx + "-linux.jar", "javafx-web-linux.jar")

    customRenameMap.put("javafx-base-" + ver_javafx + "-win.jar", "javafx-base-win.jar")
    customRenameMap.put("javafx-controls-" + ver_javafx + "-win.jar", "javafx-controls-win.jar")
    customRenameMap.put("javafx-fxml-" + ver_javafx + "-win.jar", "javafx-fxml-win.jar")
    customRenameMap.put("javafx-graphics-" + ver_javafx + "-win.jar", "javafx-graphics-win.jar")
    customRenameMap.put("javafx-media-" + ver_javafx + "-win.jar", "javafx-media-win.jar")
    customRenameMap.put("javafx-swing-" + ver_javafx + "-win.jar", "javafx-swing-win.jar")
    customRenameMap.put("javafx-web-" + ver_javafx + "-win.jar", "javafx-web-win.jar")
}
