<?xml version="1.0" encoding="UTF-8"?>

<!--Network Manager log4j-Logging Configuration File-->
<Configuration name="NMSConfig" monitorInterval="2" status="info" packages="com.adva.nlms.mediation.common.persistence.monitor,com.adva.nlms.mediation.logging,com.adva.nlms.mediation.monitoring.logging" strict="true">

	<Properties>
		<!--# log directories-->
		<Property name="logdir">var/log</Property>
		<Property name="mondir">var/monitoring</Property>
		<Property name="migdir">var/migration</Property>
		<Property name="usermigdir">${migdir}</Property>
		<Property name="usermondir">${mondir}</Property>
		<Property name="userlogdir">${logdir}</Property>
		<!--<Property name="nes">**************,**************</Property>-->
		<!--
        #Below log level values for EclipseLink are standard log4j values (OFF, FATAL, ERROR, WARN, INFO, DEBUG, TRACE, ALL)
        #They are mapped to EclipseLink log level values by custom logger
        #OFF -> OFF
        #FATAL, ERROR -> SEVERE
        #WARN -> WARNING
        #INFO -> INFO, CONFIG
        #DEBUG -> FINE
        #TRACE -> FINER, FINEST, ALL
        #ALL -> ALL
        #
        #Due to strange implementation of adding custom loggers to EclipseLink, all three loggers should be set to the same value (for the best results)
        #For that reason please change only eclipselink_logger variable which is used in these loggers.
        #It can be change during runtime (unless it was set to OFF or null initially - in that case changing level won't work)
        -->
		<Property name="eclipselink_logger_appenderRef">mediationlog</Property>
		<Property name="eclipselink_logger_level">ERROR</Property>
	</Properties>

    <CustomLevels>
        <CustomLevel name="ON" intLevel="400" />  <!-- custom level for CSV Logger on==info (400) -->
    </CustomLevels>

	<!--LOGGERS-->
	<Loggers>
		<Root level="ERROR">
			<AppenderRef ref="console"/>
		</Root>

		<Logger name="org.eclipse.jetty" level="warn"  additivity="false" >
			<AppenderRef ref="jettylog" />
		</Logger>

        <!--# Network Manager CSV event logger -->
        <!--  set to 'on' to enable or 'off' to disable -->
        <Logger name="CSVEventLogger" level="off"  additivity="false" >
            <AppenderRef ref="csveventlog" />
        </Logger>

	<!--
    # Network Manager trap reception
    # Set to 'info' to enable or 'off' to disable (in case of a trap flood)
    -->
		<!-- set to "debug" to enable logging of connection lease / release information from Apache5 HTTP client -->
		<Logger name="org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager" level="off" additivity="false">
			<AppenderRef ref="Apache5Client" />
		</Logger>
		<!--
		set to "error" to enable wrapping requests in simple debugging information and to "info" to make it more verbose
		WARNING! slight performance / memory impact!
		-->
		<Logger name="Apache5ClientDebugging" level="off" additivity="false">
			<AppenderRef ref="Apache5Client" />
		</Logger>
		<Logger name="TrapHandler" level="info" >
		</Logger>
		<!--# Network Manager server logging-->
		<Logger name="pm" level="info"  additivity="false" >
			<AppenderRef ref="pmlog" />
		</Logger>
		<Logger name="watchdog" level="info"  additivity="false" >
			<AppenderRef ref="duplicates-watchdog" />
		</Logger>
		<Logger name="timeoutWatchdog" level="info"  additivity="false" >
			<AppenderRef ref="timeouts-watchdog" />
		</Logger>
		<Logger name="neCountersWatchdog" level="info"  additivity="false" >
			<AppenderRef ref="ne-counters-watchdog" />
		</Logger>
		<Logger name="neNoConnectionPmPolling" level="error"  additivity="false" >
			<AppenderRef ref="ne-counters-watchdog" />
		</Logger>
		<Logger name="ms" level="info" >
			<AppenderRef ref="mslog" />
		</Logger>
		<Logger name="dbupgrade" level="info"  additivity="false" >
           <AppenderRef ref="dbupgradelog" />
        </Logger>
		<Logger name="dcnfeature" level="error" additivity="false" >
			<AppenderRef ref="dcnlog" />
		</Logger>
		<Logger name="ne_discovery" level="error" >
			<AppenderRef ref="ne_discovery" />
		</Logger>

		<Logger name="PeerHandlingLogger" level="error" >
			<AppenderRef ref="peernelog" />
		</Logger>
        <Logger name="com.adva.nlms.mediation.common.persistence.wrapper.RosettaStoneAdvaEntityManager" level="debug" additivity="false" >
            <AppenderRef ref="rosettastonelog" />
        </Logger>
		<Logger name="com.adva.nlms.mediation.StartupLogger" level="info"  additivity="false" >
			<AppenderRef ref="mediationlog" />
			<AppenderRef ref="mediationerr" />
		</Logger>
		<!-- Kafka utilities logging - visible during startup -->
		<Logger name="com.adva.nlms.kafka" level="info"  additivity="false" >
			<AppenderRef ref="mediationlog" />
			<AppenderRef ref="mediationerr" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.topology.HasLinkLightCheck" level="debug"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.topology.SubnetworkPlannerExportService" level="info" additivity="false" >
			<AppenderRef ref="PlannerExportLog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.planner_export.pm.PlannerPMServiceImpl" level="warn" additivity="false" >
			<AppenderRef ref="PlannerExportLog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.planner_export" level="info" additivity="false" >
			<AppenderRef ref="PlannerExportLog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
			<AppenderRef ref="mediationerr" />
		</Logger>
		<Logger name="com.adva.nlms.approvalproxy" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
			<AppenderRef ref="mediationerr" />
		</Logger>
		<Logger name="entityCreationLog" level="error"  additivity="false" >
			<AppenderRef ref="entityCreationLog" />
		</Logger>
		<Logger name="handleOrPassFurtherLog" level="error"  additivity="false" >
			<AppenderRef ref="molog" />
		</Logger>
		<Logger name="pdModuleLog" level="info"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.inf" level="error"  additivity="false" >
			<AppenderRef ref="inflog" />
			<AppenderRef ref="mediationlog" />  <!-- temp added to monitor whitelist exceptions, remove it -->
		</Logger>
		<Logger name="com.adva.enc" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="sout" level="error"  additivity="false" >
			<AppenderRef ref="sysout" />
			<AppenderRef ref="mediationlog" />
			<AppenderRef ref="mediationerr" />
		</Logger>
		<Logger name="serr" level="error"  additivity="false" >
			<AppenderRef ref="syserr" />
			<AppenderRef ref="mediationlog" />
			<AppenderRef ref="mediationerr" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.infrastructure.server_modules" level="info"  additivity="false" >
			<AppenderRef ref="modulelog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.mtosi" level="info" >
			<AppenderRef ref="mtosilog" />
		</Logger>
		<Logger name="org.apache.cxf" level="info"  additivity="false" >
			<AppenderRef ref="celtixlog" />
			<AppenderRef ref="mtosilog" />
		</Logger>
		<Logger name="org.apache.wss4j" level="info"  additivity="false" >
			<AppenderRef ref="celtixlog" />
			<AppenderRef ref="mtosilog" />
		</Logger>
		<Logger name="org.apache.cxf.phase" level="error" >
			<AppenderRef ref="mtosilog" />
		</Logger>
		<Logger name="com.adva.nlms.common" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
			<AppenderRef ref="mediationerr" />
		</Logger>
		<Logger name="com.adva.nlms.common.notification.agent.ScriptLauncherAgent" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.common.performance.structure" level="debug"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.hn4000.necomm.SNMPCtrlImplHN4000" level="debug" additivity="false">
			<AppenderRef ref="mediationlog" />
        </Logger>
		<Logger name="com.adva.nlms.mediation.config.traverse.utils.DatabaseDumpUtil" level="info" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.common.sm.ethcrypto.SecureState" level="warn"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<!--<Logger name="com.adva.nlms.mediation.config.ovn.neComm" level="debug" additivity="false">-->
			<!--<AppenderRef ref="mediationlog" />-->
		<!--</Logger>-->
		<!--# SR framework-->
		<Logger name="com.adva.nlms.mediation.config.sr.SRRepositoryManager" level="info"  additivity="false" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.security.cert" level="info"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.security.api.permission" level="warn" additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.infrastucture.security" level="error" additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.security.auth.EncMicroServicesTokenHandlerImpl" level="info" additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.sr.MOEventsSRObserver">
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.sr.NEEventsSRObserver">
		</Logger>
		<Logger name="com.adva.nlms.mediation.messaging.batch" level="warn"/>
		<!--# INF framework-->
		<Logger name="com.adva.nlms.mediation.messaging.inf.impl.InternalNotificationJMSConsumer" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.monotification.impl.MONotificationManagerImpl" level="error" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="MONotificationManagerImplIncomingNotifications" additivity="false" level="error">
			<AppenderRef ref="inflog" />
		</Logger>
		<Logger name="MONotificationManagerImplOutgoingNotifications" additivity="false" level="error">
			<AppenderRef ref="inflog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f3.entity.sj.clockprobe.ClockProbeEventHandler" level="info"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<!--# NI Module-->
		<Logger name="com.adva.nlms.ni" additivity="false" level="error">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<!--# NI Proxy -->
		<Logger name="com.adva.nlms.mediation.ni_proxy" additivity="false" level="debug">
			<AppenderRef ref="niproxylog" />
		</Logger>
		<!--# Common Module-->
		<Logger name="com.adva.nlms.mediation.common.util.RestStat" level="info">
		</Logger>
		<Logger name="com.adva.nlms.mediation.common">
		</Logger>
		<Logger name="com.adva.nlms.mediation.common.persistence" level="error" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.common.persistence.MDPersistenceContextAspect" level="debug" additivity="false">
			<AppenderRef ref="mdpersistenceaspectlog" />
		</Logger>
		<!--#default time for long transaction is defined in fnm.properties com.adva.nlms.mediation.common.persistence.LONG_RUNNING_TRANSACTION_TIMEOUT-->
		<Logger name="com.adva.nlms.mediation.common.persistence.longTransaction" level="info"  additivity="false" >
			<AppenderRef ref="longtransactionlog" />
		</Logger>
		<!--##default time for long open context is defined in fnm.properties com.adva.nlms.mediation.common.persistence.LONG_RUNNING_CONTEXT_TIMEOUT-->
		<Logger name="com.adva.nlms.mediation.common.persistence.longContext" level="warn"  additivity="false" >
			<AppenderRef ref="mediationlog" />
			<AppenderRef ref="longcontextlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.infrastructure.concurrent" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.common.concurrent" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.infrastructure.concurrent.ThreadPoolManager" level="warn"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.ne_comm.cmd.FlowControllerImpl" level="warn"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
<!--		<Logger name="AsyncSnmpDbThreadPoolOverflowLog" level="info"  additivity="false" >
			<AppenderRef ref="AsyncSnmpDbThreadPoolOverflowLogAppender" />
		</Logger>-->
		<!-- set to debug to see all net transaction related messages -->
		<Logger name="com.adva.nlms.mediation.common.transactions" level="off" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.common.rest" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<!--#set log level to info to start recording web socket notifications-->
		<Logger name="RestNotifyLog" level="info"  additivity="false" >
			<AppenderRef ref="restnotifylog" />
		</Logger>
		<!--## Core API-->
		<Logger name="com.adva.nlms.mediation.cor.fm.api" level="info"/>
		<!--## Config Module-->
		<Logger name="com.adva.nlms.mediation.config">
		</Logger>

		<Logger name="com.adva.nlms.mediation.config.SynchronizationProcess" level="warn" additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>

		<Logger name="com.adva.nlms.mediation.config.polling.PBFPollingDecorator" level="warn" additivity="false" >
			<AppenderRef ref="molog" />
		</Logger>
<!--		<Logger name="com.adva.nlms.mediation.config.polling.managedobject.MODescriptionsSynchronizer" level="debug">-->
<!--			<AppenderRef ref="mediationlog" />-->
<!--		</Logger>-->
		<Logger name="com.adva.nlms.mediation.config.neconfig.impl.SecureProtocolsStatelessWorkerImpl" level="warn"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.configWarn" level="warn" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.dbconsistency" level="warn" additivity="false" >
			<AppenderRef ref="consistencylog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.mltopologymodel.dbconsistency.checkers" level="warn" additivity="false" >
			<AppenderRef ref="consistencylog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.SNMPCommLogger" level="warn" >
			<AppenderRef ref="snmplog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.ConfigCtrlImpl.PM">
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.Subnet">
		</Logger>
		<Logger name="com.adva.nlms.mediation.topology.SubnetHdlrImpl" level="error" additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.NetworkElement">
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.NetworkElementImpl" level="error" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.HasLinkLightCheckTest">
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.Connection">
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.EntityRelationsSynchronizer" level="info" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.fsp150cm.NetworkElementFSP150CMImpl">
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.ec.EcAidStringUtil" level="error" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.topology.TopologyManagerImpl">
		</Logger>
		<Logger name="com.adva.nlms.mediation.topology.CustomerGroupViewCalculator" level="debug"  additivity="false">
            <AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.topology.SearchAndSelectPagingHdlr" level="warn"  additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.topology.lineproperties.vlan.VLanHdlrF3" level="warn" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.topology.lineproperties.vlan.VlanHdlrF4" level="warn" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.smapp.nbi.websocket.MLTopologyWebSocketServer" level="info" additivity="false" >
			<AppenderRef ref="sdnwebsocket" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.interfaces.sdn" level="info" additivity="false" >
			<AppenderRef ref="sdnlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.interfaces.sdn.impl.sm" level="error" additivity="false" >
			<AppenderRef ref="sdnlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.interfaces.sdn.impl.observers" level="info" additivity="false" >
			<AppenderRef ref="sdnlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.interfaces.sdn.impl.sm.provisioning" level="error" additivity="false" >
			<AppenderRef ref="smlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.smapp.nbi" level="error" additivity="false" >
			<AppenderRef ref="mllog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.importExport.TopologyFacade" level="info" >
			<AppenderRef ref="topimport" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.fsp_r7.handlers.PostProcess121FSP_R7EventHandler">
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.fsp_r7.moHdlrs.CPTunnelWDMHdlr" level="info"  additivity="false" >
			<AppenderRef ref="smlog" />
		</Logger>
		<!-- ================== -->
		<Logger name="com.adva.nlms.mediation.config.f8.F8AidStringUpdaterService" level="info">
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.custom">
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.customWarn" level="warn" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f3.entity.AbstractEventHandler" level="warn" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f3.entity.module.AbstractModuleF3EventHandler" level="error" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f3.entity.ManagedObjectScan" level="warn" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f3.entity.module.ModuleScan" level="warn" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f3.entity.module.linecard.ocnstmPseudoWireOcnStmCardEventHandler" level="error" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.fsp_r7.consistencyCheck" level="info" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.fsp_r7.cp.lockedlinks" level="info" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.fsp_r7.cp.rest.polling.CpRestSwitchCommunicationCommand" level="error" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.fsp_r7.AdvaSpecificSerialNumbersService" level="info" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.arc" level="error" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f3.sm.impl.SMMOESAComponentF3DAO" level="warn" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f3.sm.impl.CreateESAProbeCommand" level="warn" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.neconfig.SSOManager" level="error">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.lldp.neComm.EgmEnableLldpCommand" level="info" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.nettransaction.manager.OperationExecutorPolling" level="info" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<!-- # GIS -->
		<Logger name="com.adva.nlms.mediation.gistransfer.geoserver.GeoServerHdlrImpl" level="info" />
		<Logger name="com.adva.apps.efd.fiberdirector" level="error" additivity="true">
			<AppenderRef ref="mobileApplog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.gistransfer.fiberplant.GisAlmSynchronization" level="warn" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<!--# Server Module-->
		<Logger name="com.adva.nlms.mediation.server">
		</Logger>
		<Logger name="rcalls" level="warn">
			<AppenderRef ref="rcallslog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.server.disk.monitoring" level="info">
		</Logger>
		<Logger name="com.adva.nlms.mediation.server.MDMessageSender">
		</Logger>
		<Logger name="com.adva.nlms.mediation.server.upgrade" level="info"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.server.MDMessageBuffer" level="error" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.infrastructure.licensing.impl.flexera.FlexeraServerMonitor.FlexeraServerMonitorTimerTask" level="info">
		</Logger>
		<Logger name="com.adva.nlms.mediation.security.session.SessionHdlr" level="warn" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.security.auth.RemoteAuthCommand">
		</Logger>
		<Logger name="com.adva.nlms.mediation.security.ldap.LdapAuthCommand" level="info">
		</Logger>
		<!--# EvtProc Module-->
		<Logger name="com.adva.nlms.mediation.evtProc">
		</Logger>
		<Logger name="com.adva.nlms.mediation.evtProc.EventQueue">
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.ec.polling.DiscoverEcCromaEntitySpecificPolling" level="warn">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.mo.inventory.incoherence.IncoherentObjectsRepository" level="error">
			<AppenderRef ref="molog"/>
		</Logger>
		<!--# Event Module-->
		<Logger name="com.adva.nlms.mediation.event">
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.test" level="info" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.EventDBChangeHdlrImpl" level="warn" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.enrichment" />
		<Logger name="com.adva.nlms.mediation.event.notification">
			<AppenderRef ref="notificationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.notification.NotificationCommProblems" level="warn" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.itf">
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.itf.sbi" level="warn" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.eventCtrl">
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.processing">
		</Logger>
		<Logger name="com.adva.nlms.mediation.ne_comm.netconf" level="debug">
		</Logger>
		<Logger name="com.adva.nlms.mediation.ne_comm.rest" level="info">
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.processing.buffer" level="warn" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.polling">
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.operations">
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.correlation" level="info" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.correlation.CorrelationHdlrImpl" level="debug" additivity="false" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.dbModify" level="warn" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.dbQuery">
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.archiving" level="info">
		</Logger>
		<Logger name="com.adva.nlms.mediation.publishing">
			<AppenderRef ref="publishinglog" />
		</Logger>
		<!--# Service Manager Module-->
		<Logger name="com.adva.nlms.mediation.sm" level="info"  additivity="false" >
			<AppenderRef ref="smlog" />
		</Logger>
		<Logger name="LOGGER_FOR_PARAMETER_GROUP" level="info"  additivity="false" >
			<AppenderRef ref="sm_parameter_group_log" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.track.discovery.utils.TraversalProvider" level="info"  additivity="false" >
			<AppenderRef ref="sm_traversal_log" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.topology.ServiceTopologyProvider" level="warn"  additivity="false" >
			<AppenderRef ref="sm_service_topo_provider_log" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.operationalStatus" level="info"  additivity="false" >
			<AppenderRef ref="operationalStatus" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.adminstate.ConnectionAdminStateHdlrImpl" level="info"  additivity="false" >
			<AppenderRef ref="connectionAdminState" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.validation">
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.validation.interfaces.Traversal" level="info" additivity="false">
			<AppenderRef ref="pathtraversal" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.validation.interfaces.MultipathTraversal" level="debug" additivity="false">
			<AppenderRef ref="pathtraversal" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.nbi.fmNbiLog" level="error" >
			<AppenderRef ref="fmnbilog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.serviceImportValidation" level="info" >
           <AppenderRef ref="serviceimportlog" />
        </Logger>
        <Logger name="com.adva.nlms.mediation.importExport.sm.serviceImportValidation" level="info" >
           <AppenderRef ref="serviceimportlog" />
        </Logger>
		<Logger name="com.adva.nlms.mediation.importExport.sm.ImportExportServiceTreeHdlr" level="info"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.prov.ni.rest.NITunnelRestHandler" level="info"  additivity="false" >
			<AppenderRef ref="smNiRequestsLog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.protectionswitch.ni.NiRequest" level="info"  additivity="false" >
			<AppenderRef ref="smNiRequestsLog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.prov.ni.BundleCache" level="info"  additivity="false" >
			<AppenderRef ref="smNiRequestsLog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.prov.ni.ServiceNotificationHandlerImpl" level="info"  additivity="false" >
			<AppenderRef ref="smNiRequestsLog" />
		</Logger>
		<Logger name="com.adva.nlms.ni.links.NILinksRestClientImpl" level="debug"  additivity="false" >
			<AppenderRef ref="linksNiRequestsLog" />
		</Logger>
		<!--# Conversion-->
		<Logger name="com.adva.nlms.mediation.sm.conversion.ConversionHandler" level="error" >
			<AppenderRef ref="conversionlog" />
		</Logger>
		<!--## Other Modules-->
		<Logger name="com.adva.nlms.mediation.ne_comm.monitoring.snmp.SNMPStatisticsFacade" level="info" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.validation.fsp_r7.matrices.MatrixOsfm" level="info" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.validation.fsp_r7.matrices.Matrix_xCSMU" level="info" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.validation.fsp_r7.matrices.Matrix1CSMU_D_">
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.validation.fsp_r7.matrices.Matrix_xCSMU_C" level="info" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.validation.fsp_r7.matrices.Matrix_xGSM_D" level="info" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.validation.fsp_r7.matrices.Matrix_4GSM_D" level="info" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.validation.fsp_r7.matrices.Matrix1CSMU_EW_C_" level="info" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.sm.validation.fsp_r7.matrices.MatrixOF_2D16DCT" level="info" >
			<AppenderRef ref="smlog" />
		</Logger>
		<Logger name="com.adva.sm.tag" level="info" additivity="false">
			<AppenderRef ref="sm_tag_log" />
		</Logger>
		<Logger name="com.adva.sm.migration" level="info" >
			<AppenderRef ref="smlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.ne_comm" level="error" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.ne_comm.snmp" level="error" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.ne_comm.snmpscan.optimizer" level="warn" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.ne_comm.snmpscan.scanplan.iterator" level="warn" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.ne_comm.snmpQueryLog" level="error" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.ne_comm.snmpscan.scanplan.helper.CompositeIndexJoinQueryExecutor" level="error" >
			<AppenderRef ref="mediationlog" />
			<AppenderRef ref="mediationerr" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.ne_comm.kap.trapsink" level="error" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.ne_comm.kap.trapsink.TrapsinkRegisterCommand.messages" level="warn" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.ne_comm.ec.kap.missedtraps.MissedTrapsEC" level="warn" >
			<AppenderRef ref="mediationlog" />
		</Logger>

		<Logger name="com.adva.nlms.mediation.ni.migration.ConfigureNetIntelligenceCommand.messages" level="info" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.report" level="warn" additivity="false">
			<AppenderRef ref="reportlog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.guidata" level="error" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.security.SecurityRestResource" level="info" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
<!--		<Logger name="com.adva.nlms.mediation.security">-->
<!--		</Logger>-->
		<Logger name="com.adva.nlms.mediation.security.SecurityCtrlImpl" level="error">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.nbi" level="error">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance" level="error" additivity="false">
			<AppenderRef ref="pmlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.f8.F8PMCollector" level="warn">
			<AppenderRef ref="f8pmlog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.f8.F8PMFloatDataTypeSet" level="info">
			<AppenderRef ref="f8pmlog"/>
		</Logger>
		<Logger name="com.adva.nlms.common.performance.structure.PerformanceDataCachePerformances" level="info">
			<AppenderRef ref="f8pmlog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.f8.BinsToCheckUpdater" level="info" additivity="false">
			<AppenderRef ref="f8pmlog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.f8.F8BinUpdater" level="info" additivity="false">
			<AppenderRef ref="f8pmlog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.f8.SaveF8BinRunnable" level="info" additivity="false">
			<AppenderRef ref="f8pmlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.worker.PerformanceWorkerF8Impl" level="info" additivity="false">
			<AppenderRef ref="f8pmlog" />
		</Logger>
        <Logger name="com.adva.nlms.mediation.performance.PerformancePollingShortCommand" level="warn" additivity="false">
            <AppenderRef ref="pmlog" />
        </Logger>
		<Logger name="com.adva.nlms.mediation.performance.PerformanceCtrlImpl.csvTime">
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.hprl">
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.manager.PerformanceManagerFactory" level="warn" >
			<AppenderRef ref="pmlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.layer.apps.ethsm.ipvpnservice.PDL3ServiceManagerFacade" level="warn" >
			<AppenderRef ref="pmlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.layer.apps.ethsm.ipvpnservice.job.IpvpnPmJobManager" level="warn" >
			<AppenderRef ref="pmlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.layer.apps.ethsm.ipvpnservice.job.IpvpnPmEntitiesJob" level="warn" >
			<AppenderRef ref="pmlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.layer.apps.ethsm.pdservice.job.PDServicePmEntitiesJobFetchManager" level="error" additivity="false">
			<AppenderRef ref="pmlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.layer.apps.ethsm.pdservice.job.PDServicePmEntitiesFetchJob" level="error" additivity="false">
			<AppenderRef ref="pmlog" />
		</Logger>

        <Logger name="com.adva.nlms.mediation.performance.database.RecordSaver" level="error">
			<AppenderRef ref="pmlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.database.RecordSaver.recordExists" level="error" additivity="false">
			<AppenderRef ref="pmlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.aspect" level="error" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.statistics.PMStatisticsCollector.snmpStats" level="error" >
			<AppenderRef ref="pmlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.diagnostics" level="error" >
			<AppenderRef ref="pmdiag" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.template.OptimizedConnectionProvider" level="error" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.template.TemplateAssignmentManager" level="warn" additivity="false">
			<AppenderRef ref="pmlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.template.AssignPmTemplateIpvpnService" level="info" additivity="false">
			<AppenderRef ref="pmlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.template.TemplateManagerImpl" level="error" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.template.ServicePMFinder" level="warn" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.template.ServiceChangeNotifier" level="warn" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.template.TemplateAssignmentManager" level="error"  additivity="false" >
			<AppenderRef ref="pmlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.csv" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.performance.csv.dtag" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.polling" additivity="true" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.polling.PollingTimerTask" level="warn" additivity="true">
		</Logger>
		<Logger name="com.adva.nlms.mediation.polling.LatchPollingSynchronizer" level="warn" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.polling.VolatilePollingSynchronizer" level="warn" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.polling.conf.AutowiredPollingsUtil" level="warn" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.polling.pollingWarn" level="warn" >
		</Logger>
		<Logger name="logger.com.adva.nlms.mediation.polling.queue.GlobalPollingStarterQueueImpl" level="error" additivity="false">
			<AppenderRef ref="polling" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.polling.queue.PerformancePollingStarterQueueImpl.delayedPm" level="warn" additivity="false">
			<AppenderRef ref="pmdiag" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.polling.core.PollingCore.locks" level="off" additivity="false">
		</Logger>
		<Logger name="com.adva.nlms.mediation.polling.core.PollingExecutor" level="warn" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.polling.core.AsynchronousExecutor" level="info" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.tomcat">
		</Logger>
		<Logger name="com.adva.nlms.mediation.redundancy"/>
		<Logger name="com.adva.nlms.mediation.redundancy.ssh" level="error">
			<AppenderRef ref="mediationlog" />
		</Logger>
        <Logger name="com.adva.nlms.mediation.redundancy.switchover" level="info">
        </Logger>
		<Logger name="com.adva.nlms.mediation.monitoring">
		</Logger>
		<Logger name="com.adva.nlms.mediation.monitoring.healthreport" level="debug" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.monitoring.healthreport.HealthReport.output" level="debug" additivity="false">
			<AppenderRef ref="healthReportOutput" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.monitoring.extTrigger.ExternalTrigger" level="info" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.monitoring.diagnostic" level="info" additivity="false" >
			<AppenderRef ref="diagnosticlog" />
		</Logger>
		<Logger name="ShortTermThreadDumpLogger" level="info" additivity="false" >
			<AppenderRef ref="shortTermThreadDump" />
		</Logger>
		<Logger name="ShortTermLogger" level="info" additivity="false" >
			<AppenderRef ref="shortTerm" />
		</Logger>
		<Logger name="LongTermThreadDumpLogger" level="info" additivity="false" >
			<AppenderRef ref="longTermThreadDump" />
		</Logger>
		<Logger name="LongTermLogger" level="info" additivity="false" >
			<AppenderRef ref="longTerm" />
		</Logger>
		<Logger name="OnDemandThreadDumpLogger" level="info" additivity="false" >
			<AppenderRef ref="onDemandThreadDump" />
		</Logger>
		<Logger name="OnDemandLogger" level="info" additivity="false" >
			<AppenderRef ref="onDemand" />
		</Logger>
		<Logger name="ThreadDumpLogger" level="info" additivity="false" >
			<AppenderRef ref="threadDump" />
		</Logger>
		<Logger name="RapidTermLogger" level="info" additivity="false" >
			<AppenderRef ref="rapidTerm" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.synchronization.discovery.SyncDiscovery" level="debug" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.synchronization.util.SyncEventUtil">
		</Logger>
		<Logger name="com.adva.nlms.mediation.synchronization.discovery.action.ptptopology.dupidentity" level="debug" additivity="false">
			<AppenderRef ref="synclog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.synchronization.model.ntp" level="warn" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.topology.ExtLayersHdlr" level="info"  additivity="false" >
			<AppenderRef ref="extlayerslog" />
		</Logger>
		<!--## PCA-->
		<Logger name="com.adva.nlms.mediation.pca.PCALogger" level="info" >
		</Logger>
		<!--## Housekeeping-->
		<Logger name="com.adva.nlms.mediation.housekeeping" level="error" additivity="false">
			<AppenderRef ref="mediationlog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.common.housekeeping.transferclient" level="error" additivity="false">
			<AppenderRef ref="mediationlog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.housekeeping.nebackup" level="error" additivity="false">
			<AppenderRef ref="mediationlog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.housekeeping.nebackup.necomm" level="error" additivity="false">
			<AppenderRef ref="mediationlog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.housekeeping.swupgrade" level="info" additivity="false" >
			<AppenderRef ref="swupgradelog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.cua" level="info"  additivity="false" >
			<AppenderRef ref="cualog" />
			<AppenderRef ref="mediationlog" level="error"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.housekeeping.serverproperties" level="info"  additivity="false" >
			<AppenderRef ref="swupgradelog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.server.interceptor" level="info"  additivity="false" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.neResources" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.util.FNMTestCase" level="info"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.neprofile.masterprofile" level="info" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.synchronization.SyncHandler" level="info"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.msd.ntp" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.neprofile.masterprofile.f8.impl.download.MasterProfileF8ServiceImpl" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f8.entity.handlers.update.masterprofile.MasterProfile_F8EndDownloadEventHandler" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f8.entity.handlers.update.masterprofile.MasterProfile_F8EventHandler" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f8.entity.facility.CtpF8DBImplUpdater" level="error"  additivity="false" >
			<AppenderRef ref="molog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.neprofile.masterprofile.f8.impl.upload.MasterProfileF8UploadServiceImpl" level="error" additivity="false">
			<AppenderRef ref="mediationlog"/>
		</Logger>

	<!--<Logger name="com.adva.nlms.mediation.topology.NetworkTopologyProvider" level="debug"  additivity="false" >
        <AppenderRef ref="mediationlog" />
    </Logger>-->
		<Logger name="org.snmp4j" level="error"  additivity="false" >
           <AppenderRef ref="snmplog" />
        </Logger>
		<!--# YP DB API Module-->
		<Logger name="com.adva.yp.api" level="error">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.yp.api.data_accessor.database.impl" level="warn" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.common.model.DataTransferObjectHelper" level="error" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<!--# Logging for multilayer topology-->
		<Logger name="com.adva.nlms.mediation.mltopologymodel" level="info"  additivity="false" >
			<AppenderRef ref="mllog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.mltopologymodel.api.MLServiceManagementFacade" level="info"  additivity="false" >
			<AppenderRef ref="mllog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f8.entity.protection.ProtectionSwitchF8ServiceImpl" level="info"  additivity="false" >
			<AppenderRef ref="mllog" />
		</Logger>

		<!--# Logging for multilayer topology diagnostic -->
		<Logger name="com.adva.nlms.mediation.mltopologymodel.diagnostic" level="info"  additivity="false" >
			<AppenderRef ref="mldiaglog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.mltopologymodel.service.diagnostics" level="info"  additivity="false" >
			<AppenderRef ref="mldiaglog" />
		</Logger>
		<Logger name="com.adva.nlms.driver" level="info"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<!--# Logging for NRIM -->
		<Logger name="com.adva.nlms.mediation.mltopologymodel.api" level="info"  additivity="false" >
			<AppenderRef ref="nrimlog" />
		</Logger>
        <!--# Logging for CRM -->
        <Logger name="com.adva.nlms.mediation.smapp.crm" level="info" additivity="false" >
            <AppenderRef ref="crmlog" />
        </Logger>
		<Logger name="com.adva.nlms.resource.crm.model" level="info" additivity="false" >
			<AppenderRef ref="crmlog" />
		</Logger>
        <Logger name="com.adva.nlms.mediation.smapp.crm.comm" level="info" additivity="false" >
            <AppenderRef ref="crmlog" />
        </Logger>
		<Logger name="com.adva.nlms.driver.bean.resource.advertisement" level="info" additivity="false">
			<AppenderRef ref="crmlog" />
		</Logger>
		<Logger name="com.adva.nlms.resource.advertisement" level="info" additivity="false">
			<AppenderRef ref="crmlog" />
		</Logger>
		<Logger name="com.adva.nlms.resource.mediator" level="info" additivity="false" >
			<AppenderRef ref="crmlog" />
		</Logger>
		<Logger name="com.adva.nlms.resource.advertisement.wdm.impl.TtpManager" level="debug" additivity="false" >
			<AppenderRef ref="crmlog" />
		</Logger>
		<!--# Logging for CRM F7 WebSocket -->
		<Logger name="com.adva.nlms.mediation.ne_comm.f7.cp.websocket" level="info" additivity="false" >
			<AppenderRef ref="crmF7WebsocketLog" />
		</Logger>

		<!--# Logging for service adminstate handling-->
		<Logger name="com.adva.nlms.mediation.mltopologymodel.stateproc.adminstate" level="info"  additivity="false" >
			<AppenderRef ref="serviceadminstate" />
		</Logger>
		<!--# Logging for multilayer topology-->
		<Logger name="com.adva.nlms.mediation.smapp.nbi.mtosi" level="info" additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.mltopologymodel.log.sync" level="debug"  additivity="false" >
			<AppenderRef ref="mllog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.mltopologymodel.log.lifecycle" level="info"  additivity="false" >
			<AppenderRef ref="mllog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.mltopologymodel.log.operstate" level="debug"  additivity="false" >
			<AppenderRef ref="mllog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.mltopologymodel.servicetopologychecker" level="info"  additivity="false" >
			<AppenderRef ref="mldiaglog" />
		</Logger>
		<Logger name="org.springframework" level="warn">
			<AppenderRef ref="mediationlog"/>
		</Logger>
		<Logger name="org.eclipse.persistence" level="${eclipselink_logger_level}" >
			<AppenderRef ref="${eclipselink_logger_appenderRef}" />
		</Logger>
		<Logger name="org.eclipse.persistence.default" level="${eclipselink_logger_level}"  additivity="false" >
			<AppenderRef ref="${eclipselink_logger_appenderRef}" />
		</Logger>
		<Logger name="org.eclipse.persistence.session" level="${eclipselink_logger_level}"  additivity="false" >
			<AppenderRef ref="${eclipselink_logger_appenderRef}" />
		</Logger>
		<!--#BoneCP-->
		<Logger name="com.jolbox.bonecp" level="ERROR"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<!--#HikariCP-->
		<Logger name="com.zaxxer.hikari" level="WARN"  additivity="false" >
			<AppenderRef ref="hikarilog" />
		</Logger>
		<!-- we need info to log false-positive connection leak information from warn logger. -->
		<Logger name="com.zaxxer.hikari.pool.ProxyLeakTask" level="INFO"  additivity="false" >
			<AppenderRef ref="hikarilog" />
		</Logger>
		<!--#Profile method duration-->
		<Logger name="com.adva.nlms.common.benchmark.Benchmark" level="info"  additivity="false" >
			<AppenderRef ref="benchmarklog" />
		</Logger>
		<!--# SNMP logging-->
		<Logger name="com.adva.nlms.mediation.ne_comm.snmpscan" level="info"  additivity="false" >
			<AppenderRef ref="snmpqllog" />
		</Logger>
		<!--#Logging concepts discovery properties-->
		<Logger name="com.adva.nlms.mediation.config.DiscoveryLog" level="info"  additivity="false" >
         </Logger>
		<Logger name="com.adva.nlms.mediation.common.persistence.Deadlocks" level="info"  additivity="false" >
			<AppenderRef ref="deadlocklog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.common.persistence.Monitor" level="info"  additivity="false" >
			<AppenderRef ref="persistencemonitorlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.common.persistence.executor.SQLExecutorPostgreSQL" level="error" >
		</Logger>
		<Logger name="com.adva.nlms.mediation.common.persistence.model.AbstractPersistentObject" level="warn"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="restRequestLog" level="warn" additivity="false">
			<AppenderRef ref="restrequestlog"/>
		</Logger>
		<!--<Logger name="com.adva.nlms.mediation.polling.PollingManagerImpl.NE.32165" level="info" >
           <AppenderRef ref="polling" />
        </Logger>
        <Logger name="com.adva.nlms.mediation.polling.PollingManagerImpl.SUBNET.23907" level="info" >
           <AppenderRef ref="polling" />
        </Logger>-->
		<Logger name="com.adva.nlms.mediation.ethNEConfig"  level="warn" additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<!--# Link Loss Collection logs-->
		<Logger name="com.adva.nlms.mediation.performance.spanloss" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<!--#long running dbupgrade failure investigation-->
		<Logger name="longRunningFailureLog" level="info" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.infrastructure.concurrent.AdvaThreadPoolQueueMonitor" level="info"  additivity="false" >
			<AppenderRef ref="poolQueue" />
		</Logger>

		<!--http request log-->
		<Logger name="org.eclipse.jetty.server.RequestLog" level="info" additivity="false">
			<AppenderRef ref="httpRequestLog" />
		</Logger>
		<Logger name="com.adva.RequestLogLongRunning" level="info" additivity="false">
			<AppenderRef ref="httpRequestLogLongRunning" />
		</Logger>
		<!--NI request log-->
<!--		<Logger name="com.adva.nlms.common.rest.ProtobufMessageBodyReader" level="debug" additivity="false">-->
<!--			<AppenderRef ref="nirequestlog" />-->
<!--		</Logger>-->
<!--		<Logger name="com.adva.nlms.common.rest.ProtobufMessageBodyWriter" level="debug" additivity="false">-->
<!--			<AppenderRef ref="nirequestlog" />-->
<!--		</Logger>-->
<!--		<Logger name="com.adva.nlms.common.rest.RequestLoggingFilter" level="debug" additivity="false">-->
<!--			<AppenderRef ref="nirequestlog" />-->
<!--		</Logger>-->
		<Logger name="com.adva.nlms.common.rest" level="error" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.polling.metric.PollingDelayMetric" level="info" additivity="false">
			<AppenderRef ref="polling" />
		</Logger>
		<Logger name="com.adva.snmpStatistics" level="info" additivity="false">
			<AppenderRef ref="snmpStatistics" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.ne_comm.cmd.SNMPCommand" level="error" additivity="false">
			<AppenderRef ref="commanderlog" >
				<!-- Below filter allows to show logs only for given NE. -->
				<!--<MapFilter onMatch="ACCEPT">-->
					<!--<KeyValuePair key="ip" value="*************"/>-->
				<!--</MapFilter>-->
			</AppenderRef>
			<AppenderRef ref="mediationlog" level="error"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.ext.cpc" level="info" additivity="false">
			<AppenderRef ref="nimanagerlog"/>
			<AppenderRef ref="mediationlog" level="error"/>
		</Logger>
        <Logger name="com.adva.nlms.mediation.sm.validation.TraversalStatistics" level="info" additivity="false">
			<AppenderRef ref="traversalStatistics"/>
		</Logger>

		<Logger name="com.adva.nlms.mediation.config.osa3230b" level="debug" additivity="false">
			<AppenderRef ref="osa3230blog"/>
			<AppenderRef ref="mediationlog" level="error"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.userlabels" level="info" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<!-- Remember to uncomment appropriate appender! -->
		<!-- info - default logging, debug - verbose logging -->
		<Logger name="com.adva.nlms.mediation.polling.SimplePollingLogger" level="info" additivity="false">
			<!--<AppenderRef ref="SimplePollingLog"/>-->
			<AppenderRef ref="AsyncSimplePollingLog"/>
		</Logger>
		<!--<Logger name="com.adva.nlms.mediation.polling.SimplePollingLogger.10.12.105.30" level="info" additivity="false">
			<AppenderRef ref="SimplePollingLog"/>
		</Logger>-->
		<!--<Logger name="pollingTrace" level="trace" additivity="false">
			<AppenderRef ref="pollingTrace" />
		</Logger>-->
		<!--<Logger name="com.adva.nlms.mediation.polling.monitoring.KAPMonitor.KAPExecution" level="trace" additivity="false">
			<AppenderRef ref="KAPExecutionLog"/>
		</Logger>-->

		<Logger name="com.adva.di.server" level="info"  additivity="false" >
				<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.pv" level="info"  additivity="false" >
				<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.ne_comm.snmp.api.SNMP4JConfiguration" level="warn"  additivity="false" >
			<AppenderRef ref="snmpsession" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.ne_comm.snmp.ExtendedUdpTransportMapping" level="error"  additivity="false" >
			<AppenderRef ref="snmpsession" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.mltopologymodel.mofacade.modto.helpers.eth.prov.MLOperationCallback" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.mltopologymodel.mofacade.modto.statemachine" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.db.consolidation.tool.export" level="debug" additivity="false">
			<AppenderRef ref="routingImpExpLog" />
		</Logger>
		<Logger name="com.adva.nlms.db.consolidation.tool.dbimport" level="debug" additivity="false">
			<AppenderRef ref="routingImpExpLog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.server.jetty" level="warn"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.infrastructure.licensing.impl.flexera.licensing" level="info"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.infrastructure.licensing.impl.flexera.licensing.LicenseEnforcementApiAdapter" level="off" additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.common.rest.authorization" level="info"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.common.rest.core.ServerSideRestWebResourceWithCertificates" level="info"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.security.ca.CAHandlerImpl" level="info"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.monitoring.metrics.annotations.CodahaleTimerAspectSlowProcessed" level="info"  additivity="false" >
			<AppenderRef ref="slowprocessinglog" />
		</Logger>
		<Logger name="com.adva.nlms.inf.impl.InternalNotificationConsumerSlowProcessed" level="info"  additivity="false" >
			<AppenderRef ref="slowprocessinglog" />
		</Logger>
		<Logger name="com.adva.enc.profile.snmp" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.common.persistence.MDPersistentObjectChangeListener.BeforeUpdate" level="info"  additivity="false" >
			<AppenderRef ref="persistentObjectsLog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.common.persistence.MDPersistentObjectChangeListener.AfterUpdate" level="debug"  additivity="false" >
			<AppenderRef ref="persistentObjectsLog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.common.persistence.MDPersistentObjectChangeListener.AfterLoad" level="info"  additivity="false" >
			<AppenderRef ref="persistentObjectsLog" />
		</Logger>
		<Logger name="ypInitializationLog" level="off" additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.ni.migration.generation" level="info" additivity="false" >
			<AppenderRef ref="cpcmigration" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.ni.NINodeOperator" level="info" additivity="false">
			<AppenderRef ref="cpclog" />
		</Logger>
		<Logger name="com.adva.nlms.ni.links.NILinkOperator" level="info" additivity="false">
			<AppenderRef ref="cpclog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.ni.NINodeMultiLinkManager" level="info" additivity="false" >
			<AppenderRef ref="cpclog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.ni.adapter.LineDBImplToNILinkAdapter" level="info" additivity="false" >
			<AppenderRef ref="cpclog" />
		</Logger>
        <Logger name="com.adva.nlms.ni.mediator.client.NiTopologyResyncClient" level="info" additivity="false" >
            <AppenderRef ref="cpcmigration" />
        </Logger>

        <!--<Logger name="ypApiCallsLog" level="info" additivity="false">
			<AppenderRef ref="YpApiCallsAppender"/>
		</Logger>-->
		<Logger name="com.adva.nlms.mediation.reload.modules.SendUpdateForIntraNe" level="off" additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.housekeeping.swupgrade.db.SWUpgradeDAO" level="warn" additivity="false">
			<AppenderRef ref="mediationlog"/>
		</Logger>
		<Logger name="ecCommLog" level="debug" additivity="false">
			<AppenderRef ref="EcCommAppender"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.topology.GraphNodeLocation.GraphNodeLocationDao" level="debug" additivity="false">
			<AppenderRef ref="topologylayoutlog"/>
		</Logger>
		<!-- mo logs -->
		<Logger name="com.adva.nlms.mediation.config.f8.entity.handlers" level="info" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.ec.entity.handlers.CreateEventHandler" level="info" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.ec.entity.handlers.DeleteEventHandler" level="info" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.ec.entity.handlers.update" level="info" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.ec.entity.handlers.EC_EventHandler" level="error" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f4.entity.evpn.EvpnF4UpdateEventHandler" level="info" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.mo.ec.scan" level="info" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.mo.inventory" level="info" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f4.entity.l3flowpoint" level="info" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f4.entity.vrf" level="info" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f8.croma" level="info" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f8.croma.CromaSlcDBImpl" level="info" additivity="false">
			<AppenderRef ref="mediationlog"/>
		</Logger>

		<Logger name="com.adva.nlms.mediation.f8.NetworkElementF8Impl" level="error" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>

		<Logger name="com.adva.nlms.mediation.config.fsp_r7.polling" level="info" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.ec.generator" level="info" additivity="false">
			<AppenderRef ref="EcModelGeneratorAppender"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.fsp_r7.entity.fibermap" level="warn" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.fsp_r7.entity.crossconnect" level="info" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.fsp_r7.cards.Card" level="info" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.config.fsp1XX.capabilities" level="warn" additivity="false">
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.alm.NetworkElementALMImpl" level="debug" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.event.statusPolling" level="info" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.ec.neComm.provision" level="info" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<!-- fallback user logs -->
		<Logger name="com.adva.nlms.mediation.config.fallback" level="info" additivity="false">
			<AppenderRef ref="fallbackuserlog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.fsp_r7.fallback" level="info" additivity="false">
			<AppenderRef ref="fallbackuserlog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.ne_comm.fallback.AddUserCommand" level="info" additivity="false">
			<AppenderRef ref="fallbackuserlog"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.ne_comm.fallback.FallbackPasswordCommandF7" level="info" additivity="false">
			<AppenderRef ref="fallbackuserlog"/>
		</Logger>
		<Logger name="com.adva.device_inventory.license_manager.impl.DeviceLicenseManagerImpl" level="info" additivity="false">
			<AppenderRef ref="mediationlog"/>
		</Logger>
                <Logger name="com.adva.packet_layer3" level="info"  additivity="false" >
                        <AppenderRef ref="pdl3log" />
                </Logger>
		<Logger name="com.adva.nlms.pd.api.in" level="info"  additivity="false" >
			<AppenderRef ref="pdlog" />
		</Logger>
		<Logger name="com.adva.nlms.pd.api.out" level="info"  additivity="false" >
			<AppenderRef ref="pdlog" />
		</Logger>
		<Logger name="com.adva.nlms.pd.inventory" level="info"  additivity="false" >
			<AppenderRef ref="pdlog" />
		</Logger>
		<Logger name="com.adva.nlms.pd.inventory.dbconsistency.checkers" level="warn" additivity="false" >
			<AppenderRef ref="consistencylog" />
		</Logger>
		<Logger name="com.adva.nlms.pd.inventory.diagnostic" level="info"  additivity="false" >
			<AppenderRef ref="pddiaglog" />
		</Logger>
		<Logger name="com.adva.nlms.pd.inventory.mofacade.modto.provisioning.PDCrossConnectOperationStateMachine" level="warn"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.pd.inventory.mofacade.modto.statemachine" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.pd.inventory.mofacade.modto.statemachine.process.PDGeneralSMFailedProcess" level="warn"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.pd.inventory.momediation.proc.builder.eth.prov.PDOperationCallback" level="error"  additivity="false" >
			<AppenderRef ref="mediationlog" />
		</Logger>
		<Logger name="com.adva.nlms.pd.inventory.servicetopologychecker" level="info"  additivity="false" >
			<AppenderRef ref="pddiaglog" />
		</Logger>
		<Logger name="com.adva.nlms.pd.inventory.stateproc.adminstate" level="debug"  additivity="false" >
			<AppenderRef ref="serviceadminstate" />
		</Logger>
		<Logger name="neDiscoveryF8SwVersionLog" level="info" additivity="false">
			<AppenderRef ref="molog"/>
		</Logger>
		<Logger name="LineDiscoveryLogger" level="info" additivity="false">
			<AppenderRef ref="LineDiscoveryLog"/>
		</Logger>
		<Logger name="syncEcDataModelPollingLogger" level="debug" additivity="false">
			<AppenderRef ref="syncEcDataModelPollingLog"/>
		</Logger>
		<Logger name="com.adva.nlms.opticalparameters" level="info"  additivity="false" >
			<AppenderRef ref="opticalParameters" />
		</Logger>
		<Logger name="com.adva.nlms.opticalrouter" level="info"  additivity="false" >
			<AppenderRef ref="opticalrouter" />
		</Logger>
		<Logger name="com.adva.eod.po" level="info"  additivity="false" >
			<AppenderRef ref="provorch" />
		</Logger>
		<Logger name="com.adva.infrastructure.capabilityprovider" level="info"  additivity="false" >
			<AppenderRef ref="capability" />
		</Logger>
		<Logger name="com.adva.eod.capabilitybroker" level="info"  additivity="false" >
			<AppenderRef ref="capability" />
		</Logger>
		<Logger name="com.adva.nlms.txprovisioning" level="info"  additivity="false" >
			<AppenderRef ref="crmlog" />
		</Logger>
		<Logger name="ALARM_TRACER" level="off" additivity="false">
			<AppenderRef ref="OUTAGE_ALARMS"/>
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.ec.polling.DiscoverCrossConnectEcPolling" level="debug"  additivity="true" >
			<AppenderRef ref="molog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.ec.polling.SynchronizeConnectivityMap" level="info" additivity="false" >
			<AppenderRef ref="molog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.mo.inventory.event.handler.EquipmentHolderF8CreateEventHandler" level="debug"  additivity="true" >
			<AppenderRef ref="molog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.AbstractEntityUpdater" level="info"  additivity="false" >
			<AppenderRef ref="molog" />
		</Logger>
		<Logger name="com.adva.nlms.mediation.config.f8.entity.crossconnect.CrossConnectF8DBImplUpdater" level="debug"  additivity="false" >
			<AppenderRef ref="molog" />
		</Logger>
		<!-- -->
	</Loggers>

	<!--APPENDERS-->
	<Appenders>
        <!--# CSV Event logger -->
        <Appender name="csveventlog" type="RollingFile" fileName="${logdir}/eventlog.csv" filePattern="${logdir}/eventlog.csv.%i" append="true"  >
            <Layout type="PatternLayout" pattern="%m" />
            <DefaultRolloverStrategy max="10" />
            <SizeBasedTriggeringPolicy size="1mb" />
        </Appender>
		<!--# console logging-->
		<Appender name="console" type="Console"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
		</Appender>
		<!--# Network Manager server logging-->
		<Appender name="mediationlog" type="RollingFile" fileName="${logdir}/mediation.log" filePattern="${logdir}/mediation.log.%i.gz" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="120" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<Appender name="jettylog" type="RollingFile" fileName="${logdir}/jetty.log" filePattern="${logdir}/jetty.log.%i.gz" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="6" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<Appender name="reportlog" type="RollingFile" fileName="${logdir}/report.log" filePattern="${logdir}/report.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="10" />
			<SizeBasedTriggeringPolicy size="10mb" />
		</Appender>
		<Appender name="persistentObjectsLog" type="RollingFile" fileName="${logdir}/persist.log" filePattern="${logdir}/persist.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="6" />
			<SizeBasedTriggeringPolicy size="10mb" />
		</Appender>
		<Appender name="mobileApplog" type="RollingFile" fileName="${logdir}/mobileApp.log" filePattern="${logdir}/mobileApp.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<Appender name="topologylayoutlog" type="RollingFile" fileName="${logdir}/topologylayout.log" filePattern="${logdir}/topologylayout.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
        <Appender name="rosettastonelog" type="RollingFile" fileName="${logdir}/rosettastone.log" filePattern="${logdir}/rosettastone.log.%i" append="true"  >
            <Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
            <DefaultRolloverStrategy max="12" />
            <SizeBasedTriggeringPolicy size="50mb" />
        </Appender>
        <Appender name="mdpersistenceaspectlog" type="RollingFile" fileName="${logdir}/mdpersistenceaspect.log" filePattern="${logdir}/mdpersistenceaspect.log.%i" append="true">
            <Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
            <DefaultRolloverStrategy max="12" />
            <SizeBasedTriggeringPolicy size="50mb" />
        </Appender>
        <Appender name="f8pmlog" type="RollingFile" fileName="${logdir}/f8pm.log" filePattern="${logdir}/f8pm.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<Appender name="consistencylog" type="RollingFile" fileName="${logdir}/consistency.log" filePattern="${logdir}/consistency.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<Appender name="inflog" type="RollingFile" fileName="${logdir}/inf.log" filePattern="${logdir}/inf.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<!--# HikariCP logging-->
		<Appender name="hikarilog" type="RollingFile" fileName="${logdir}/hikari.log" filePattern="${logdir}/hikari.log.%i" append="true">
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<!--# SW Upgrade logger-->
		<Appender name="swupgradelog" type="RollingFile" fileName="${logdir}/swupgrade.log" filePattern="${logdir}/swupgrade.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<!--# keep one copy, because a new log file empty!-->
			<DefaultRolloverStrategy max="2" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<Appender name="cualog" type="RollingFile" fileName="${logdir}/cua.log" filePattern="${logdir}/cua.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="2" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<!--## Network Manager server error logging-->
		<Appender name="mediationerr" type="RollingFile" fileName="${logdir}/mediation.err" filePattern="${logdir}/mediation.err.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
			<Filter type="ThresholdFilter" level="ERROR" />
		</Appender>
		<Appender name="sysout" type="RollingFile" fileName="${logdir}/systemout.log" filePattern="${logdir}/systemout.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t] %-5p =%c= - %C %M %L %m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<Appender name="syserr" type="RollingFile" fileName="${logdir}/systemerr.log" filePattern="${logdir}/systemerr.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t] %-5p =%c= - %C %M %L %m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<!--## Network Manager server logging-->
		<Appender name="dbupgradelog" type="RollingFile" fileName="${logdir}/dbupgrade.log" filePattern="${logdir}/dbupgrade.log.%i" append="true"  >
           <Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
           <DefaultRolloverStrategy max="12" />
           <SizeBasedTriggeringPolicy size="50mb" />
        </Appender>
		<!--## PM logging-->
		<Appender name="pmlog" type="RollingFile" fileName="${logdir}/pm.log" filePattern="${logdir}/pm.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t] %-5p %markerSimpleName - %m%n " />
			<DefaultRolloverStrategy max="1" />
			<SizeBasedTriggeringPolicy size="10mb" />
<!--			<Filters >-->
<!--				<MarkerFilter marker="ALL" onMatch="ACCEPT" onMismatch="NEUTRAL" />-->
<!--				<MarkerFilter marker="10.12.104.93" onMatch="ACCEPT" onMismatch="DENY" />-->
<!--			</Filters>-->
		</Appender>
		<!--# PM diagnostics-->
		<Appender name="pmdiag" type="RollingFile" fileName="${logdir}/pm_diag.log" filePattern="${logdir}/pm_diag.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="10" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<!--# Mediation start logging-->
		<Appender name="mslog" type="RollingFile" fileName="${logdir}/mediation-start.log" filePattern="${logdir}/mediation-start.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<DefaultRolloverStrategy max="1" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<!--## PM logging (duplicated timestamps while DB saving)-->
		<Appender name="duplicates-watchdog" type="RollingFile" fileName="${logdir}/duplicates-watchdog.log" filePattern="${logdir}/duplicates-watchdog.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="1" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<!--# PM logging (timeouts during PM collection)-->
		<Appender name="timeouts-watchdog" type="RollingFile" fileName="${logdir}/timeouts-watchdog.log" filePattern="${logdir}/timeouts-watchdog.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="1" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<Appender name="ne-counters-watchdog" type="RollingFile" fileName="${logdir}/ne-counters-watchdog.log" filePattern="${logdir}/ne-counters-watchdog.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="10" />
			<SizeBasedTriggeringPolicy size="80mb" />
		</Appender>
		<!--## topology import-->
		<Appender name="topimport" type="RollingFile" fileName="${logdir}/topimport.log" filePattern="${logdir}/topimport.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="3" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<Appender name="notificationlog" type="RollingFile" fileName="${logdir}/notification.log" filePattern="${logdir}/notification.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<!--# keep one copy, because a new log file empty!-->
			<DefaultRolloverStrategy max="1" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<Appender name="publishinglog" type="RollingFile" fileName="${logdir}/publishing.log" filePattern="${logdir}/publishing.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<!--# keep one copy, because a new log file empty!-->
			<DefaultRolloverStrategy max="1" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<!--## Network Manager server startup and shutdown logging-->
		<Appender name="modulelog" type="File" fileName="${logdir}/module.log" append="false"  >
			<Layout type="PatternLayout" pattern="%d [%.15t] %c{1} %-5p - %m%notEmpty{ correlationId=%X{correlationId}}%n" />
		</Appender>
		<Appender name="snmplog" type="RollingFile" fileName="${logdir}/snmp.log" filePattern="${logdir}/snmp.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t] %-5p %c{2} - %m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<DefaultRolloverStrategy max="10" />
			<SizeBasedTriggeringPolicy size="40mb" />
		</Appender>
		<Appender name="snmpsession" type="RollingFile" fileName="${logdir}/snmpsession.log" filePattern="${logdir}/snmpsession.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="10" />
			<SizeBasedTriggeringPolicy size="40mb" />
		</Appender>
		<!--# REST Notification logging... catch all TRPs -->
		<Appender name="restnotifylog" type="RollingFile" fileName="${logdir}/restnotify.log" filePattern="${logdir}/restnotify.log.%i.gz" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t] %-5p %c{2} - %m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<DefaultRolloverStrategy max="20" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<!--# SNMPQL logging-->
		<Appender name="snmpqllog" type="RollingFile" fileName="${logdir}/snmpql.log" filePattern="${logdir}/snmpql.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t] %-5p %c{2} - %m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<DefaultRolloverStrategy max="1" />
			<SizeBasedTriggeringPolicy size="40mb" />
		</Appender>
		<!--# SNMP logging-->
		<Appender name="commanderlog" type="RollingFile" fileName="${logdir}/commander.log" filePattern="${logdir}/commander.log.%i" append="true" >
			<!-- you can change %m to %map{message} to have all logs without ip tag. -->
           <Layout type="PatternLayout" pattern="%d [%.15t] %-5p %c{2} - %m%notEmpty{ correlationId=%X{correlationId}}%n" />
           <DefaultRolloverStrategy max="10" />
           <SizeBasedTriggeringPolicy size="40mb" />
        </Appender>
		<!--# Network Manager mtosi logging-->
		<Appender name="mtosilog" type="RollingFile" fileName="${logdir}/mtosi.log" filePattern="${logdir}/mtosi.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="1" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<!--# Network Manager cxf logging-->
		<Appender name="celtixlog" type="RollingFile" fileName="${logdir}/celtix.log" filePattern="${logdir}/celtix.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="10" />
			<SizeBasedTriggeringPolicy size="512kb" />
		</Appender>
		<!--## ML Topology logging-->
		<Appender name="mllog" type="RollingFile" fileName="${logdir}/mltopo.log" filePattern="${logdir}/mltopo.log.%i.gz" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="100" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<!--## ML Topology logging-->
		<Appender name="mldiaglog" type="RollingFile" fileName="${logdir}/mltopo_diag.log" filePattern="${logdir}/mltopo_diag.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="2" />
			<SizeBasedTriggeringPolicy size="1mb" />
		</Appender>
		<!--## NRIM  logging-->
		<Appender name="nrimlog" type="RollingFile" fileName="${logdir}/nrim.log" filePattern="${logdir}/nrim.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="10" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<!--## Service Manager logging-->
		<Appender name="smlog" type="RollingFile" fileName="${logdir}/sm.log" filePattern="${logdir}/sm.log.%i.gz" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="80" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<Appender name="extlayerslog" type="RollingFile" fileName="${logdir}/extlayers.log" filePattern="${logdir}/extlayers.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="8" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<Appender name="sm_parameter_group_log" type="RollingFile" fileName="${logdir}/sm_parameter_group.log" filePattern="${logdir}/sm_parameter_group.log.%i.gz" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="80" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<Appender name="sm_traversal_log" type="RollingFile" fileName="${logdir}/sm_traversal.log" filePattern="${logdir}/sm_traversal.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="8" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<Appender name="sm_service_topo_provider_log" type="RollingFile" fileName="${logdir}/sm_service_topo_provider.log" filePattern="${logdir}/sm_service_topo_provider.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t] %-5p - %m%n" />
			<DefaultRolloverStrategy max="8" />
			<SizeBasedTriggeringPolicy size="10mb" />
		</Appender>
		<Appender name="sm_tag_log" type="RollingFile" fileName="${logdir}/sm_tag.log" filePattern="${logdir}/sm_tag.log.%i.gz" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="8" />
			<SizeBasedTriggeringPolicy size="2mb" />
		</Appender>
		<Appender name="operationalStatus" type="RollingFile" fileName="${logdir}/operational_status.log" filePattern="${logdir}/operational_status.log.%i.gz" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="80" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<Appender name="connectionAdminState" type="RollingFile" fileName="${logdir}/connection_admin_state.log" filePattern="${logdir}/connection_admin_state.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="8" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<!--## SM protobuf calls-->
		<Appender name="smNiRequestsLog" type="RollingFile" fileName="${logdir}/sm_ni_requests.log" filePattern="${logdir}/sm_ni_requests.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="8" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<!--## NI Links protobuf calls-->
		<Appender name="linksNiRequestsLog" type="RollingFile" fileName="${logdir}/links_ni_requests.log" filePattern="${logdir}/links_ni_requests.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="8" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<!--## SDN Rest API logging-->
		<Appender name="sdnwebsocket" type="RollingFile" fileName="${logdir}/sdnwebsocket.log" filePattern="${logdir}/sdnwebsocket.log.%i.gz" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="80" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<Appender name="sdnlog" type="RollingFile" fileName="${logdir}/sdn.log" filePattern="${logdir}/sdn.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="8" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
        <!--## CRM logging-->
        <Appender name="crmlog" type="RollingFile" fileName="${logdir}/crm.log" filePattern="${logdir}/crm.log.%i.gz" append="true"  >
            <Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
            <DefaultRolloverStrategy max="20" />
            <SizeBasedTriggeringPolicy size="50mb" />
        </Appender>
		<!--## CRM - F7 WebSocket logging-->
		<Appender name="crmF7WebsocketLog" type="RollingFile" fileName="${logdir}/crm_f7_websocket.log" filePattern="${logdir}/crm_f7_websocket.log.%i.gz" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="20" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<!--## DTAG FM NBI: log protocol messages-->
		<Appender name="fmnbilog" type="RollingFile" fileName="${logdir}/fm_nbi.log" filePattern="${logdir}/fm_nbi.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%t] %-5p - %m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<DefaultRolloverStrategy max="1" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<!--## New Service Admin State Handling -->
		<Appender name="serviceadminstate" type="RollingFile" fileName="${logdir}/serviceadminstate.log" filePattern="${logdir}/serviceadminstate.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="2" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<!--# DTAG Service Import Validation: log status of TNMS service-->
		<Appender name="serviceimportlog" type="RollingFile" fileName="${logdir}/service_import.log" filePattern="${logdir}/service_import.log.%i" append="true"  >
           <Layout type="PatternLayout" pattern="%d %-5p - %m%notEmpty{ correlationId=%X{correlationId}}%n" />
           <DefaultRolloverStrategy max="3" />
           <SizeBasedTriggeringPolicy size="8mb" />
        </Appender>
		<!--# Conversion logging-->
		<Appender name="conversionlog" type="RollingFile" fileName="${logdir}/conversion.log" filePattern="${logdir}/conversion.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d{dd MMM HH:mm:ss} %m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<DefaultRolloverStrategy max="3" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<!--#long transaction logging-->
		<Appender name="longtransactionlog" type="RollingFile" fileName="${logdir}/longtransaction.log" filePattern="${logdir}/longtransaction.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="3" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<!--##long open context logging-->
		<Appender name="longcontextlog" type="RollingFile" fileName="${logdir}/longcontext.log" filePattern="${logdir}/longcontext.log.%1" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="3" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<!--#dcn feature logging-->
		<Appender name="dcnlog" type="RollingFile" fileName="${logdir}/dcnlog.log" filePattern="${logdir}/dcnlog.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<!--#ne_discovery logging-->
		<Appender name="ne_discovery" type="RollingFile" fileName="${logdir}/ne_discovery.log" filePattern="${logdir}/ne_discovery.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<!--#for logs connected to peer handling.-->
		<Appender name="peernelog" type="RollingFile" fileName="${logdir}/peerne.log" filePattern="${logdir}/peerne.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<!--# Deadlocks logging-->
		<Appender name="deadlocklog" type="RollingFile" fileName="${logdir}/deadlock.log" filePattern="${logdir}/deadlock.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="2" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<!--## Persistence monitor logging-->
		<Appender name="persistencemonitorlog" type="RollingFile" fileName="${logdir}/persistence_monitor.csv" filePattern="${logdir}/persistence_monitor.csv.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%m%notEmpty{ correlationId=%X{correlationId}}%n" header="TIME;TYPE;DATA1;DATA2;DATA3;DATA4;DATA5;DATA6;DATA7;DATA8;DATA9 ${permon:LINE_SEPARATOR}" />
			<DefaultRolloverStrategy max="10" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<!--# Benchmark logging-->
		<Appender name="benchmarklog" type="RollingFile" fileName="${logdir}/benchmark.log" filePattern="${logdir}/benchmark.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="5" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<Appender name="polling" type="RollingFile" fileName="${logdir}/pollings.log" filePattern="${logdir}/pollings.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d{dd MMM HH:mm:ss} %m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<DefaultRolloverStrategy max="3" />
			<SizeBasedTriggeringPolicy size="80mb" />
		</Appender>
		<Appender name="poolQueue" type="RollingFile" fileName="${logdir}/threadPoolQueue.log" filePattern="${logdir}/threadPoolQueue.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="3" />
			<SizeBasedTriggeringPolicy size="80mb" />
		</Appender>
		<!--#ne_discovery logging-->
		<Appender name="molog" type="RollingFile" fileName="${logdir}/mo.log" filePattern="${logdir}/mo.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="8" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<Appender name="restrequestlog" type="RollingFile" fileName="${logdir}/restrequest.log" filePattern="${logdir}/restrequest.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="4" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>

		<Appender name="neprofilelog" type="RollingFile" fileName="${logdir}/neprofilelog.log" filePattern="${logdir}/neprofilelog.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="8" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>

		<Appender name="httpRequestLog" type="RollingFile" fileName="${logdir}/request.log" filePattern="${logdir}/%d{yyyy_MM_dd}.request.log" append="true">
			<Layout type="PatternLayout" pattern="%m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<Policies>
				<TimeBasedTriggeringPolicy />
			</Policies>
			<DefaultRolloverStrategy max="14">
				<Delete basePath="${logdir}" maxDepth="1">
					<IfFileName glob="????_??_??.request.log" />
					<ifLastModified age="14d" />
				</Delete>
			</DefaultRolloverStrategy>
		</Appender>

		<Appender name="httpRequestLogLongRunning" type="RollingFile" fileName="${logdir}/request.long_running.log" filePattern="${logdir}/request.long_running.log.bak" append="true">
			<Layout type="PatternLayout" pattern="%-5p %m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<DefaultRolloverStrategy max="1" />
			<SizeBasedTriggeringPolicy size="10mb" />
		</Appender>

		<Appender name="diagnosticlog" type="RollingFile" fileName="${logdir}/diagnostic.csv" filePattern="${logdir}/diagnostic.csv.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d{yyyy-MM-dd HH:mm:ss},%-5p,%m%notEmpty{ correlationId=%X{correlationId}}%n" header="sep=,%n"/>
			<DefaultRolloverStrategy max="5" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>

		<Appender name="longTerm" type="RollingFile" fileName="${mondir}/longTerm.csv" filePattern="${mondir}/longTerm.csv.%i" append="true">
			<Layout type="MonitoringLogLayout" pattern="%d{dd MMM yyyy HH:mm:ss} %m%notEmpty{ correlationId=%X{correlationId}}%n" header="LongTerm"/>
			<DefaultRolloverStrategy max="5" />
			<Policies>
				<SizeBasedTriggeringPolicy size="2500kb" />
			</Policies>
		</Appender>

		<Appender name="shortTerm" type="RollingFile" fileName="${mondir}/shortTerm.csv" filePattern="${mondir}/shortTerm.csv.%i" append="true">
			<Layout type="MonitoringLogLayout" pattern="%d{dd MMM yyyy HH:mm:ss} %m%notEmpty{ correlationId=%X{correlationId}}%n" header="ShortTerm"/>
			<DefaultRolloverStrategy max="5" />
			<Policies>
				<SizeBasedTriggeringPolicy size="2500kb" />
			</Policies>
		</Appender>

		<Appender name="onDemand" type="RollingFile" fileName="${mondir}/onDemand.csv" filePattern="${mondir}/onDemand.csv.%i" append="true">
			<Layout type="MonitoringLogLayout" pattern="%d{dd MMM yyyy HH:mm:ss} %m%notEmpty{ correlationId=%X{correlationId}}%n" header="OnDemand"/>
			<DefaultRolloverStrategy max="5" />
			<Policies>
				<SizeBasedTriggeringPolicy size="2500kb" />
			</Policies>
		</Appender>

		<Appender name="rapidTerm" type="RollingFile" fileName="${mondir}/rapidTerm.csv" filePattern="${mondir}/rapidTerm.csv.%i" append="true">
			<Layout type="MonitoringLogLayout" pattern="%d{dd MMM yyyy HH:mm:ss} %m%notEmpty{ correlationId=%X{correlationId}}%n" header="RapidTerm"/>
			<DefaultRolloverStrategy max="5" />
			<Policies>
				<SizeBasedTriggeringPolicy size="2500kb" />
			</Policies>
		</Appender>

		<Appender name="shortTermThreadDump" type="RollingFile" fileName="${mondir}/shortTermThreadDump.log" filePattern="${mondir}/shortTermThreadDump.log.%i" append="true">
			<Layout type="PatternLayout" pattern="%d{dd MMM yyyy HH:mm:ss} %m%notEmpty{ correlationId=%X{correlationId}}%n"/>
			<DefaultRolloverStrategy max="5" />
			<SizeBasedTriggeringPolicy size="0b" />
		</Appender>

		<Appender name="longTermThreadDump" type="RollingFile" fileName="${mondir}/longTermThreadDump.log" filePattern="${mondir}/longTermThreadDump.log.%i" append="true">
			<Layout type="PatternLayout" pattern="%d{dd MMM yyyy HH:mm:ss} %m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<DefaultRolloverStrategy max="5" />
			<SizeBasedTriggeringPolicy size="0b" />
		</Appender>

		<Appender name="onDemandThreadDump" type="RollingFile" fileName="${mondir}/onDemandThreadDump.log" filePattern="${mondir}/onDemandThreadDump.log.%i" append="true">
			<Layout type="PatternLayout" pattern="%m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<DefaultRolloverStrategy max="5"/>
			<SizeBasedTriggeringPolicy size="0b"/>
		</Appender>

		<Appender name="threadDump" type="RollingFile" fileName="${mondir}/threadDump.log" filePattern="${mondir}/threadDump.log.%i" append="false">
			<Layout type="PatternLayout" pattern="%m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<DefaultRolloverStrategy max="5"/>
			<SizeBasedTriggeringPolicy size="0kb" />
		</Appender>

		<Appender name="snmpStatistics" type="RollingFile" fileName="${logdir}/snmpStatistics.log" filePattern="${logdir}/snmpStatistics.log.%i.gz" append="false">
			<Layout type="PatternLayout" pattern="%d{dd-MM-yyyy, HH:mm:ss}, %m%notEmpty{ correlationId=%X{correlationId}}%n"/>
			<DefaultRolloverStrategy max="50" />
			<SizeBasedTriggeringPolicy size="80mb" />
		</Appender>

		<Appender name="traversalStatistics" type="RollingFile" fileName="${logdir}/traversal-statistics.log" filePattern="${logdir}/traversal-statistics.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d{dd-MM-yyyy, HH:mm:ss}, %m%notEmpty{ correlationId=%X{correlationId}}%n"/>
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>

		<!--#synclog feature logging-->
		<Appender name="synclog" type="RollingFile" fileName="${logdir}/synclog.log" filePattern="${logdir}/synclog.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>

		!--#synclog feature logging-->
		<Appender name="osa3230blog" type="RollingFile" fileName="${logdir}/osa3230b.log" filePattern="${logdir}/osa3230b.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>

		<!--Cpc migration logging-->
		<Appender name="cpcmigration" type="RollingFile" fileName="${logdir}/cpc-migration.log" filePattern="${logdir}/cpc-migration.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="6" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>

		<!--NI manager logging-->
		<Appender name="nimanagerlog" type="RollingFile" fileName="${logdir}/ni-manager.log" filePattern="${logdir}/ni-manager.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>

		<!--NI Proxy appender-->
		<Appender name="niproxylog" type="RollingFile" fileName="${logdir}/niproxy.log" filePattern="${logdir}/niproxy.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="1" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>

		<Appender name="nirequestlog" type="RollingFile" fileName="${logdir}/ni-request.log" filePattern="${logdir}/ni-request.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="2" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>

		<Appender name="cpclog" type="RollingFile" fileName="${logdir}/cpc-operations.log" filePattern="${logdir}/cpc-operations.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="6" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>

		<!--<Appender name="pollingTrace" type="RollingFile" fileName="${logdir}/pollingTrace.log" filePattern="${logdir}/pollingTrace.log.%i" >
			<Layout type="PatternLayout"
					pattern="%d{dd MMM HH:mm:ss} DOM[%X{DOMAIN}] T[%X{TYPE}] PR[%X{PRIORITY}] TU[%X{THREAD_USAGE}] UN[%X{TASK_UNREGISTRATION}] PAR[%X{POLLING_PARAMETERS}] ID[%X{PMID}] %m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<DefaultRolloverStrategy max="10"/>
			<SizeBasedTriggeringPolicy size="100mb" />
			<Filters>
				<MarkerFilter marker="POLLINGMARKER0" onMatch="ACCEPT" onMismatch="NEUTRAL"/>	&lt;!&ndash;UNKNOWN&ndash;&gt;
				<MarkerFilter marker="POLLINGMARKER1" onMatch="ACCEPT" onMismatch="NEUTRAL"/>	&lt;!&ndash;INVENTORY&ndash;&gt;
				<MarkerFilter marker="POLLINGMARKER2" onMatch="ACCEPT" onMismatch="NEUTRAL"/>	&lt;!&ndash;CONFIGURATION&ndash;&gt;
				<MarkerFilter marker="POLLINGMARKER3" onMatch="ACCEPT" onMismatch="NEUTRAL"/>	&lt;!&ndash;STATUS&ndash;&gt;
				<MarkerFilter marker="POLLINGMARKER6" onMatch="ACCEPT" onMismatch="NEUTRAL"/>	&lt;!&ndash;KAP&ndash;&gt;
				<MarkerFilter marker="POLLINGMARKER12" onMatch="ACCEPT" onMismatch="NEUTRAL"/>	&lt;!&ndash;INITSNMP&ndash;&gt;
				<MarkerFilter marker="POLLINGMARKER10" onMatch="ACCEPT" onMismatch="NEUTRAL"/>	&lt;!&ndash;COLDSTART&ndash;&gt;
			</Filters>
		</Appender>-->
		<Appender name="SimplePollingLog" type="RollingFile" fileName="${logdir}/SimplePolling.log" filePattern="${logdir}/SimplePolling.log.%i.gz" append="true">
			<Layout type="PatternLayout" pattern="%d [%.40t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="60" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<Async name="AsyncSimplePollingLog">
			<AppenderRef ref="SimplePollingLog"/>
			<LinkedTransferQueue/>
		</Async>
		<Appender name="LineDiscoveryLog" type="RollingFile" fileName="${logdir}/LineDiscovery.log" filePattern="${logdir}/LineDiscovery.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.40t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="3" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<!--<Appender name="KAPExecutionLog" type="RollingFile" fileName="${logdir}/KAPExecution.csv" filePattern="${logdir}/KAPExecution_%i.csv" append="true"  >
			<Layout type="PatternLayout" pattern="%m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<DefaultRolloverStrategy max="6" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>-->
		<!--Export & Import logging-->
		<Routing name="routingImpExpLog">
			<Routes pattern="${ctx:logFileName}">
				<Route>
					<RollingFile name="Rolling-${ctx:logFileName}"
								 fileName="${migdir}/${ctx:logFileName}.log"
								 filePattern="${migdir}/${ctx:logFileName}.log">
						<PatternLayout pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n"/>
						<SizeBasedTriggeringPolicy size="50mb" />
					</RollingFile>
				</Route>
			</Routes>
		</Routing>
		<Appender name="YpApiCallsAppender" type="RollingFile" fileName="${logdir}/YpApiCalls.log" filePattern="${logdir}/YpApiCalls.log.%i" append="true">
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<Appender name="slowprocessinglog" type="RollingFile" fileName="${logdir}/slow-processing.log" filePattern="${logdir}/slow-processing.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<!--# keep one copy, because a new log file empty!-->
			<DefaultRolloverStrategy max="2" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<Appender name="EcCommAppender" type="RollingFile" fileName="${logdir}/EcComm.log" filePattern="${logdir}/EcComm.log.%i" append="true" createOnDemand="true">
			<Layout type="PatternLayout" pattern="%d %m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<DefaultRolloverStrategy max="6" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<Appender name="AsyncSnmpDbThreadPoolOverflowLogAppender" type="RollingFile" fileName="${logdir}/AsyncSnmpDbThreadPoolOverflow.log" filePattern="${logdir}/AsyncSnmpDbThreadPoolOverflow.log.%i" append="true" createOnDemand="true">
			<Layout type="PatternLayout" pattern="%d %m%notEmpty{ correlationId=%X{correlationId}}%n" />
			<DefaultRolloverStrategy max="10" />
			<SizeBasedTriggeringPolicy size="100mb" />
		</Appender>
		<Appender name="EcModelGeneratorAppender" type="RollingFile" fileName="${logdir}/EcModelGenerator.log" filePattern="${logdir}/EcModelGenerator.log.%i" append="true" createOnDemand="true">
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="3" />
			<SizeBasedTriggeringPolicy size="100mb" />
		</Appender>
		<Appender name="healthReportOutput" type="RollingFile" fileName="${logdir}/healthReportOutput.log" filePattern="${logdir}/healthReportOutput.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="2" />
			<SizeBasedTriggeringPolicy size="10mb" />
		</Appender>
		<Appender name="pathtraversal" type="RollingFile" fileName="${logdir}/path_traversal.log" filePattern="${logdir}/path_traversal.log.%i" append="true">
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="6" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<Appender name="fallbackuserlog" type="RollingFile" fileName="${logdir}/fallbackuser.log" filePattern="${logdir}/fallbackuser.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="2" />
			<SizeBasedTriggeringPolicy size="10mb" />
		</Appender>
		<Appender name="rcallslog" type="RollingFile" fileName="${logdir}/rcalls.log" filePattern="${logdir}/rcalls.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="2" />
			<SizeBasedTriggeringPolicy size="10mb" />
		</Appender>
                <Appender name="pdl3log" type="RollingFile" fileName="${logdir}/pdl3.log" filePattern="${logdir}/pdl3.log.%i" append="true"  >
                        <Layout type="PatternLayout" pattern="%d [%.15t] %-5p - %m%n" />
                        <DefaultRolloverStrategy max="10" />
                        <SizeBasedTriggeringPolicy size="50mb" />
                </Appender>
		<!--## PD Inventory logging-->
		<Appender name="pdlog" type="RollingFile" fileName="${logdir}/pdtopo.log" filePattern="${logdir}/pdtopo.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="10" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<!--## PD Inventory logging-->
		<Appender name="pddiaglog" type="RollingFile" fileName="${logdir}/pdtopo_diag.log" filePattern="${logdir}/pdtopo_diag.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="2" />
			<SizeBasedTriggeringPolicy size="1mb" />
		</Appender>
		<Appender name="syncEcDataModelPollingLog" type="RollingFile" fileName="${logdir}/syncEcDataModelPollingLog.log" filePattern="${logdir}/syncEcDataModelPollingLog.log.%i" append="true" >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="3" />
			<SizeBasedTriggeringPolicy size="100mb" />
		</Appender>
		<Appender name="entityCreationLog" type="RollingFile" fileName="${logdir}/entityCreationLog.log" filePattern="${logdir}/entityCreationLog.log.%i" append="true" >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="3" />
			<SizeBasedTriggeringPolicy size="100mb" />
		</Appender>
		<Appender name="opticalParameters" type="RollingFile" fileName="${logdir}/optical_parameters.log" filePattern="${logdir}/opticalParameters.log.%i" append="true" >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="3" />
			<SizeBasedTriggeringPolicy size="100mb" />
		</Appender>
		<Appender name="capability" type="RollingFile" fileName="${logdir}/capability.log" filePattern="${logdir}/capability.log.%i" append="true" >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="3" />
			<SizeBasedTriggeringPolicy size="100mb" />
		</Appender>
		<Appender name="provorch" type="RollingFile" fileName="${logdir}/provisioning_orchestrator.log" filePattern="${logdir}/provisioning_orchestrator.log.%i" append="true" >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="3" />
			<SizeBasedTriggeringPolicy size="10mb" />
		</Appender>
		<Appender name="opticalrouter" type="RollingFile" fileName="${logdir}/optical_router.log" filePattern="${logdir}/optical_router.log.%i" append="true" >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="3" />
			<SizeBasedTriggeringPolicy size="10mb" />
		</Appender>
		<Appender name="PlannerExportLog" type="RollingFile" fileName="${logdir}/PlannerExport.log" filePattern="${logdir}/PlannerExport.log.%i" append="true">
			<Layout type="PatternLayout" pattern="%d [%.40t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="3" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
		<Appender name="OUTAGE_ALARMS" type="RollingFile" fileName="${logdir}/outage-alarms.log" filePattern="${logdir}/outage-alarms.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="8" />
			<SizeBasedTriggeringPolicy size="8mb" />
		</Appender>
		<Appender name="Apache5Client" type="RollingFile" fileName="${logdir}/Apache5Client.log" filePattern="${logdir}/Apache5Client.log.%i.gz" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t%notEmpty{ %X{correlationId}}] %-5p - %m%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
	</Appenders>
</Configuration>
	<!-- END OF FILE -->
