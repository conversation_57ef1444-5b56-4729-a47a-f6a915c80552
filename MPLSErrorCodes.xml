<?xml version="1.0"?>
<!--
  -  Copyright 2023 Adtran Networks SE. All rights reserved.
  -
  -  Owner: sku<PERSON><PERSON><PERSON>
  -->

<!-- This file maps MPLS/RSVP error codes and conditions to plain text -->

<MPLSErrors>

  <RSVP_ERROR_CONF 	CODE="0" 	STRING="Confirmation"/>

  <RSVP_ERROR_AC	CODE="1" 	STRING="Admission Control Failure">
    <RSVP_ERROR_AC_DELAY	CODE="1"	STRING="Delay Bound Cannot be Met"/>
    <RSVP_ERROR_AC_BW		CODE="2"	STRING="Requested Bandwidth Unavailable"/>
    <RSVP_ERROR_AC_MTU		CODE="3"	STRING="MTU in FlowSpec greater than Interface MTU"/>
    <RSVP_ERROR_AC_MTU		CODE="3"	STRING="MTU in FlowSpec greater than Interface MTU"/>
    <RSVP_ERROR_AC_MVZ_INVALID  CODE="35328"    STRING="Invalid evalue 0x8a00 (35328)"/>
    <RSVP_ERROR_AC_MVZ_UNKNOWN  CODE="35329"    STRING="General resource/lambda allocation failure (3)"/>
    <RSVP_ERROR_AC_MVZ_RA_FAILED_SHARE        CODE="35330"    STRING="Failed to share resource"/>
    <RSVP_ERROR_AC_MVZ_RA_FAILED_CHANGE_OWNER CODE="35331"    STRING="Failed to change owner"/>
    <RSVP_ERROR_AC_MVZ_RA_NO_RM_BHANDLE       CODE="35332"    STRING="Failed to create binding group for cross-connect"/>
    <RSVP_ERROR_AC_MVZ_RA_NO_RM_RHANDLE       CODE="35333"    STRING="Failed to get resource"/>
    <RSVP_ERROR_AC_MVZ_RA_RM_ALLOC_REJECTED   CODE="35334"    STRING="Resource allocation request rejected"/>
    <RSVP_ERROR_AC_MVZ_RA_RM_MODIFY_REJECTED  CODE="35335"    STRING="Resource modify request rejected"/>
    <RSVP_ERROR_AC_MVZ_RA_RM_FREE_REJECTED    CODE="35336"    STRING="Resource release request rejected"/>
    <RSVP_ERROR_AC_MVZ_RA_RM_BIND_REJECTED    CODE="35337"    STRING="Cross-connect request rejected"/>
    <RSVP_ERROR_AC_MVZ_RA_RM_UNBIND_REJECTED  CODE="35338"    STRING="Cross-connect release request rejected"/>
    <RSVP_ERROR_AC_MVZ_RA_RM_EALARM_REJECTED  CODE="35339"    STRING="Enable alarms request rejected"/>
    <RSVP_ERROR_AC_MVZ_RA_RM_DALARM_REJECTED  CODE="35340"    STRING="Disable alarms request rejected"/>
    <RSVP_ERROR_AC_MVZ_RA_RM_ALLOC_FAILED     CODE="35341"    STRING="Resource allocation failure"/>
    <RSVP_ERROR_AC_MVZ_RA_RM_BIND_FAILED      CODE="35342"    STRING="Cross-connect failure"/>
    <RSVP_ERROR_AC_MVZ_RA_RM_BIND_PRIORITY_FAILED CODE="35343"    STRING="Cross-connect failure (owing to priority)"/>
    <RSVP_ERROR_AC_MVZ_RA_RM_UNBIND_FAILED    CODE="35344"    STRING="Cross-connect release failure"/>
    <RSVP_ERROR_AC_MVZ_RA_RM_MODIFY_FAILED    CODE="35345"    STRING="Resource modify failure"/>
    <RSVP_ERROR_AC_MVZ_RA_RM_INTER_REJECTED   CODE="35346"    STRING="Failed to interrupt resource request"/>
    <RSVP_ERROR_AC_MVZ_RA_MASTER_RSRC_FAILED  CODE="35347"    STRING="Failure on primary"/>
    <RSVP_ERROR_AC_MVZ_RA_NO_WORKING_OCTP     CODE="35348"    STRING="Allocation requested when no working OCTP present"/>
    <RSVP_ERROR_AC_MVZ_RA_GENERIC	      CODE="35349"    STRING="General resource/lambda allocation failure (1)"/>
    <RSVP_ERROR_AC_MVZ_RA_INVALID_INTF	      CODE="35350"    STRING="Invalid or absent interface or circuit pack"/>
    <RSVP_ERROR_AC_MVZ_RA_RESERVED_1	      CODE="35351"    STRING="Evalue 0x8a17 (35351)"/>
    <RSVP_ERROR_AC_MVZ_RA_RESERVED_2	      CODE="35352"    STRING="Evalue 0x8a18 (35352)"/>
    <RSVP_ERROR_AC_MVZ_RA_RESERVED_3	      CODE="35353"    STRING="Evalue 0x8a19 (35353)"/>
    <RSVP_ERROR_AC_MVZ_RA_RESERVED_4	      CODE="35354"    STRING="Evalue 0x8a1a (35354)"/>
    <RSVP_ERROR_AC_MVZ_RA_RESERVED_5	      CODE="35355"    STRING="Evalue 0x8a1b (35355)"/>
    <RSVP_ERROR_AC_MVZ_RM_FAILED              CODE="35356"    STRING="General resource manager failure"/>
    <RSVP_ERROR_AC_MVZ_RM_INTERFACE_FAILURE   CODE="35357"    STRING="General interface fault"/>
    <RSVP_ERROR_AC_MVZ_RM_NET_FAILURE         CODE="35358"    STRING="General network fault"/>
    <RSVP_ERROR_AC_MVZ_RM_NODE_FAILURE        CODE="35359"    STRING="General node fault"/>
    <RSVP_ERROR_AC_MVZ_RM_INTERFACEID         CODE="35360"    STRING="SIM card fault or not present or wavelength mismatch"/>
    <RSVP_ERROR_AC_MVZ_RM_RING_NOT_COMPLETE   CODE="35361"    STRING="Ring/OSC fault"/>
    <RSVP_ERROR_AC_MVZ_RM_NO_OLD_RES          CODE="35362"    STRING="OLD fault or unavailable at requested priority"/>
    <RSVP_ERROR_AC_MVZ_RM_NO_TXVR_RES         CODE="35363"    STRING="Transceiver not found"/>
    <RSVP_ERROR_AC_MVZ_RM_NO_OEO_RES          CODE="35364"    STRING="Cross-connects allocation failure"/>
    <RSVP_ERROR_AC_MVZ_RM_CANT_PREEMPT        CODE="35365"    STRING="Unable to preempt resource (priority too low)"/>
    <RSVP_ERROR_AC_MVZ_RM_RES_NOT_ALLOCATED   CODE="35366"    STRING="Invalid or failed cross-connect"/>
    <RSVP_ERROR_AC_MVZ_RM_INVALID_GRP         CODE="35367"    STRING="Invalid group"/>
    <RSVP_ERROR_AC_MVZ_RM_INVALID_CP          CODE="35368"    STRING="Invalid circuit pack"/>
    <RSVP_ERROR_AC_MVZ_RM_BAD_EXC_PORT_MAPPING CODE="35369"    STRING="Cross-connect mapping nor present or invalid"/>
    <RSVP_ERROR_AC_MVZ_RM_XCONN_FAILED        CODE="35370"    STRING="Failed to establish cross-connect"/>
    <RSVP_ERROR_AC_MVZ_RM_LAMBDA_DROP         CODE="35371"    STRING="Pass-through lambda is dropped at transit node"/>
    <RSVP_ERROR_AC_MVZ_RM_INVALID_RH          CODE="35372"    STRING="Invalid resource handle"/>
    <RSVP_ERROR_AC_MVZ_RM_BAD_DATARATE        CODE="35373"    STRING="Invalid datarate or cannot program datarate"/>
    <RSVP_ERROR_AC_MVZ_RM_SET_AGGPORT         CODE="35374"    STRING="SIM port fault"/>
    <RSVP_ERROR_AC_MVZ_RM_PROTECTION_FAIL     CODE="35375"    STRING="Unable to set protection"/>
    <RSVP_ERROR_AC_MVZ_RM_GENERIC             CODE="35376"    STRING="General resource/lambda allocation failure (2)"/>
    <RSVP_ERROR_AC_MVZ_RM_LINK_INACTIVE       CODE="35377"    STRING="Data link is inactive"/>
    <RSVP_ERROR_AC_MVZ_RM_RESERVED_1          CODE="35378"    STRING="Evalue 0x8a32 (35378)"/>
    <RSVP_ERROR_AC_MVZ_RM_RESERVED_2          CODE="35379"    STRING="Evalue 0x8a33 (35379)"/>
    <RSVP_ERROR_AC_MVZ_RM_RESERVED_3          CODE="35380"    STRING="Evalue 0x8a34 (35380)"/>
    <RSVP_ERROR_AC_MVZ_RM_RESERVED_4          CODE="35381"    STRING="Evalue 0x8a35 (35381)"/>
    <RSVP_ERROR_AC_MVZ_RM_RESERVED_5          CODE="35382"    STRING="Evalue 0x8a36 (35382)"/>
    <RSVP_ERROR_AC_MVZ_LSM_IN_INTERFACE       CODE="35383"    STRING="Upstream interface not present or fault"/>
    <RSVP_ERROR_AC_MVZ_LSM_UP_IN_LABEL        CODE="35384"    STRING="No usable lambda on upstream interface"/>
    <RSVP_ERROR_AC_MVZ_LSM_DOWN_IN_LABEL      CODE="35385"    STRING="No usable input lambda on downstream interface"/>
    <RSVP_ERROR_AC_MVZ_LSM_DOWN_OUT_LABEL     CODE="35386"    STRING="No usable output lambda on downstream interface"/>
    <RSVP_ERROR_AC_MVZ_LSM_LABEL_SET          CODE="35387"    STRING="Label set present at ingress"/>
    <RSVP_ERROR_AC_MVZ_LSM_TGT_CARD           CODE="35388"    STRING="Target slot not present or fault"/>
    <RSVP_ERROR_AC_MVZ_LSM_BAD_UP_IN_LABEL    CODE="35389"    STRING="Unable to allocate selected upstream input lambda"/>
    <RSVP_ERROR_AC_MVZ_LSM_BAD_UP_OUT_LABEL   CODE="35390"    STRING="Unable to allocate selected upstream output lambda"/>
    <RSVP_ERROR_AC_MVZ_LSM_BAD_DOWN_IN_LABEL  CODE="35391"    STRING="Unable to allocate selected downstream input lambda"/>
    <RSVP_ERROR_AC_MVZ_LSM_BAD_DOWN_OUT_LABEL CODE="35392"    STRING="Unable to allocate selected downstream output lambda"/>
    <RSVP_ERROR_AC_MVZ_LSM_OUT_INTERFACE      CODE="35393"    STRING="Downstream interface not present or fault"/>
    <RSVP_ERROR_AC_MVZ_LSM_SRC_CARD           CODE="35394"    STRING="Source slot not present or faulted"/>
    <RSVP_ERROR_AC_MVZ_LSM_RESERVED_1         CODE="35395"    STRING="Evalue 0x8a43 (35395)"/>
    <RSVP_ERROR_AC_MVZ_LSM_RESERVED_2         CODE="35396"    STRING="Evalue 0x8a44 (35396)"/>
    <RSVP_ERROR_AC_MVZ_LSM_RESERVED_3         CODE="35397"    STRING="Evalue 0x8a45 (35397)"/>
    <RSVP_ERROR_AC_MVZ_LSM_RESERVED_4         CODE="35398"    STRING="Evalue 0x8a46 (35398)"/>
    <RSVP_ERROR_AC_MVZ_LSM_RESERVED_5         CODE="35399"    STRING="Evalue 0x8a47 (35399)"/>
    <RSVP_ERROR_AC_MVZ_OM_GENERIC             CODE="35400"    STRING="General cross-connect failure"/>
    <RSVP_ERROR_AC_MVZ_OM_NO_PROTECTION       CODE="35401"    STRING="No protection provisioned"/>
    <RSVP_ERROR_AC_MVZ_OM_PROTECTION_FAILED   CODE="35402"    STRING="Required protection cannot be provisioned"/>
    <RSVP_ERROR_AC_MVZ_OM_BIND_PROT_PSN       CODE="35403"    STRING="Failed to bind protect OCTP on ingress of protect"/>
    <RSVP_ERROR_AC_MVZ_OM_BIND_PROT_PMN       CODE="35404"    STRING="Failed to bind protect OCTP on egress of protect"/>
    <RSVP_ERROR_AC_MVZ_OM_REBIND_WORK_PSN     CODE="35405"    STRING="Failed to rebind working OCTP on ingress of protect"/>
    <RSVP_ERROR_AC_MVZ_OM_REBIND_WORK_PMN     CODE="35406"    STRING="Failed to rebind working OCTP on egress of protect"/>
    <RSVP_ERROR_AC_MVZ_OM_UNBIND_PROT_PSN     CODE="35407"    STRING="Failed to release cross-connect for protection path (ingress)"/>
    <RSVP_ERROR_AC_MVZ_OM_UNBIND_PROT_PMN     CODE="35408"    STRING="Failed to release cross-connect for protection path (egress)"/>
    <RSVP_ERROR_AC_MVZ_OM_UNBIND_WORK_PSN     CODE="35409"    STRING="Failed to release cross-connect for working path (ingress)"/>
    <RSVP_ERROR_AC_MVZ_OM_UNBIND_WORK_PMN     CODE="35410"    STRING="Failed to release cross-connect for working path (egress)"/>
    <RSVP_ERROR_AC_MVZ_OM_RSRC_INACTIVE       CODE="35411"    STRING="Data link inactive"/>
    <RSVP_ERROR_AC_MVZ_OM_RESERVED_2          CODE="35412"    STRING="Evalue 0x8a54 (35412)"/>
    <RSVP_ERROR_AC_MVZ_OM_RESERVED_3          CODE="35413"    STRING="Evalue 0x8a55 (35413)"/>
    <RSVP_ERROR_AC_MVZ_OM_RESERVED_4          CODE="35414"    STRING="Evalue 0x8a56 (35414)"/>
    <RSVP_ERROR_AC_MVZ_OM_RESERVED_5          CODE="35415"    STRING="Evalue 0x8a57 (35415)"/>
    <RSVP_ERROR_AC_MVZ_OM_RESERVED_6          CODE="35416"    STRING="Evalue 0x8a58 (35416)"/>

  </RSVP_ERROR_AC>

  <RSVP_ERROR_POLICY 		CODE="2" 	STRING="Policy Control Failure"/>
  <RSVP_ERROR_NO_PATH 		CODE="3" 	STRING="No Path State for RESV"/>
  <RSVP_ERROR_NO_SNDR 		CODE="4" 	STRING="No Sender Information for RESV"/>
  <RSVP_ERROR_STYLE 		CODE="5" 	STRING="Conflicting Reservation Style"/>
  <RSVP_ERROR_BAD_STYLE 	CODE="6" 	STRING="Unknown Reservation Style"/>
  <RSVP_ERROR_DST_P 		CODE="7" 	STRING="Conflicting Destination Ports"/>
  <RSVP_ERROR_SNDR_P 		CODE="8" 	STRING="Conflicting Sender Ports"/>
  <RSVP_ERROR_PREEMPTED 	CODE="12" 	STRING="Service Preempted"/>
  <RSVP_ERROR_CLASS 		CODE="13" 	STRING="Unknown Object Class"/>
  <RSVP_ERROR_CTYPE 		CODE="14" 	STRING="Unknown Object C-Type"/>
  <RSVP_ERROR_API 		CODE="20" 	STRING="Reserved for API"/>

  <RSVP_ERROR_TC 		CODE="21" 	STRING="Traffic Control Error">
    <RSVP_ERROR_TC_CONFLICT 		CODE="1" 	STRING="Service Conflict"/>
    <RSVP_ERROR_TC_SERVICE 		CODE="2" 	STRING="Service Unsupported"/>
    <RSVP_ERROR_TC_FLOWSPEC 		CODE="3" 	STRING="Bad Flowspec Value"/>
    <RSVP_ERROR_TC_TSPEC 		CODE="4" 	STRING="Bad Tspec Value"/>
    <RSVP_ERROR_TC_ADSPEC 		CODE="5" 	STRING="Bad Adspec value"/>
  </RSVP_ERROR_TC>

  <RSVP_ERROR_TC_SYSTEM 	CODE="22" 	STRING="Traffic Control System Error"/>

  <RSVP_ERROR_SYSTEM 		CODE="23" 	STRING="RSVP System Error">
    <RSVP_ERROR_SYSTEM_OTHER 			CODE="0" 	STRING="RSVP System Error OTHER"/>
    <RSVP_ERROR_SYSTEM_SB_ALLOC 		CODE="1" 	STRING="Unable to Alloc State Block"/>
    <RSVP_ERROR_SYSTEM_MALLOC 			CODE="2" 	STRING="General Memory Alloc Failure"/>
    <RSVP_ERROR_SYSTEM_POLICY 			CODE="3" 	STRING="General policy failure"/>
    <RSVP_ERROR_SYSTEM_ROUTING 			CODE="4" 	STRING="General routing failure"/>
    <RSVP_ERROR_SYSTEM_AC_TC 			CODE="5" 	STRING="General admission control failure"/>
    <RSVP_ERROR_SYSTEM_IPC 			CODE="6" 	STRING="General internal failure"/>
    <RSVP_ERROR_SYSTEM_STATE 			CODE="7" 	STRING="General state machine failure"/>
    <RSVP_ERROR_SYSTEM_MULTIACCESS 		CODE="8" 	STRING="Detected Unsupported xBMA Net"/>
    <RSVP_ERROR_SYSTEM_RESOURCE 		CODE="9" 	STRING="General resource failure"/>
    <RSVP_ERROR_SYSTEM_RACE 			CODE="10" 	STRING="Setup request received while processing teardown request"/>
    <RSVP_ERROR_SYSTEM_PROV_NONE 		CODE="11" 	STRING="No error"/>
    <RSVP_ERROR_SYSTEM_PROV_ALLOC 		CODE="12" 	STRING="Memory allocation failure"/>
    <RSVP_ERROR_SYSTEM_PROV_VERSION 		CODE="13" 	STRING="Version mismatch"/>
    <RSVP_ERROR_SYSTEM_PROV_TYPE 		CODE="14" 	STRING="Unknown message"/>
    <RSVP_ERROR_SYSTEM_PROV_MSG_LENGTH 		CODE="15" 	STRING="Received message has invalid length"/>
    <RSVP_ERROR_SYSTEM_PROV_ERSO_LENGTH 	CODE="16" 	STRING="Received ERO subobject has invalid length"/>
    <RSVP_ERROR_SYSTEM_PROV_OBJ_LENGTH 		CODE="17" 	STRING="Received object has invalid length"/>
    <RSVP_ERROR_SYSTEM_PROV_PARSE 		CODE="18" 	STRING="Internal parsing error"/>
    <RSVP_ERROR_SYSTEM_PROV_ERSO_PARSE 		CODE="19" 	STRING="Unknown ERO subobject"/>
    <RSVP_ERROR_SYSTEM_PROV_MISSING 		CODE="20" 	STRING="Required object not present"/>
    <RSVP_ERROR_SYSTEM_PROV_ERSO_MISSING 	CODE="21" 	STRING="ERO endpoint missing"/>
    <RSVP_ERROR_SYSTEM_PROV_CANCEL 		CODE="22" 	STRING="Transaction canceled"/>
    <RSVP_ERROR_SYSTEM_PROV_BUSY 		CODE="23" 	STRING="Busy"/>
    <RSVP_ERROR_SYSTEM_PROV_SIGNALING 		CODE="24" 	STRING="General failure"/>
    <RSVP_ERROR_SYSTEM_PROV_PERMISSION 		CODE="25" 	STRING="No permission to act on OCT"/>
    <RSVP_ERROR_SYSTEM_PROV_DELETED 		CODE="26" 	STRING="OCT already been deleted"/>
    <RSVP_ERROR_SYSTEM_RA_MALLOC  		CODE="27" 	STRING="RA memory alloc failure"/>
    <RSVP_ERROR_SYSTEM_RA_STATE 		CODE="28" 	STRING="RA inconsistent state"/>
    <RSVP_ERROR_SYSTEM_RM_STATE 		CODE="29" 	STRING="RM inconsistent state"/>
    <RSVP_ERROR_SYSTEM_OM_STATE 		CODE="30" 	STRING="OM inconsistent state"/>
    <RSVP_ERROR_SYSTEM_NOT_USED_1 		CODE="31" 	STRING="NOT USED"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_0 		CODE="32" 	STRING="SC_OK"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_1 		CODE="33" 	STRING="SC_OK_REMOVED"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_2 		CODE="34" 	STRING="SC_BUSY"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_3 		CODE="35" 	STRING="SC_RES_UNAVAIL"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_4 		CODE="36" 	STRING="SC_STATE_NOT_APPL"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_5 		CODE="37" 	STRING="SC_FAILED"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_6 		CODE="38" 	STRING="SC_BADPARM"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_7 		CODE="39" 	STRING="SC_TOOMANY"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_8 		CODE="40" 	STRING="SC_NOMEMORY"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_9 		CODE="41" 	STRING="SC_NO_MATCH"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_10 		CODE="42" 	STRING="SC_FOUND"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_11 		CODE="43" 	STRING="Not managed by Control Plane"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_12 		CODE="44" 	STRING="SC_EOF"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_13 		CODE="45" 	STRING="SC_NO_ROUTING"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_14 		CODE="46" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_15 		CODE="47" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_16 		CODE="48" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_17 		CODE="49" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_18 		CODE="50" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_19		CODE="51" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_20 		CODE="52" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_21 		CODE="53" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_22 		CODE="54" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_23 		CODE="55" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_24 		CODE="56" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_25 		CODE="57" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_26 		CODE="58" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_27 		CODE="59" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_28 		CODE="60" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_29 		CODE="61" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_30 		CODE="62" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_DB_ERROR_31 		CODE="63" 	STRING="Reserved for Future DB Error"/>
    <RSVP_ERROR_SYSTEM_NOT_USED_3  		CODE="64"	STRING="evalue 64"/>
    <RSVP_ERROR_SYSTEM_PCE_UNKNOWN_ADDRESS 	CODE="65"	STRING="No route to destination IP interface shown"/>
    <RSVP_ERROR_SYSTEM_PCE_INVALID_ARGUMENTS 	CODE="66"	STRING="Invalid parameters"/>
    <RSVP_ERROR_SYSTEM_PCE_REGISTRATION_ERROR 	CODE="67"	STRING="Topology database error"/>
    <RSVP_ERROR_SYSTEM_PCE_INTERFACE_ERROR 	CODE="68"	STRING="IP interface error"/>
    <RSVP_ERROR_SYSTEM_PCE_TOPOLOGY_ERROR 	CODE="69"	STRING="General topology error"/>
    <RSVP_ERROR_SYSTEM_PCE_CHANNEL_ERROR 	CODE="70"	STRING="Inconsistent Lambda advertisements"/>
    <RSVP_ERROR_SYSTEM_PCE_GENERAL_ERROR 	CODE="71"	STRING="Path Computation general error"/>
    <RSVP_ERROR_SYSTEM_PCE_FATAL_ERROR 		CODE="72"	STRING="Fatal error"/>
    <RSVP_ERROR_SYSTEM_PCE_PATH_FAILED 		CODE="73"	STRING="Existing path no longer available"/>
    <RSVP_ERROR_SYSTEM_PCE_NO_MEMORY 		CODE="74"	STRING="Memory allocation failure"/>
    <RSVP_ERROR_SYSTEM_PCE_NO_THREAD 		CODE="75"	STRING="System resources failure"/>
    <RSVP_ERROR_SYSTEM_PCE_NO_TIMER 		CODE="76"	STRING="System timers unavailable"/>
    <RSVP_ERROR_SYSTEM_PCE_OTHER 		CODE="77" 	STRING="General failure"/>
    <RSVP_ERROR_SYSTEM_PCE_RESERVED_1 		CODE="78"	STRING=" "/>
    <RSVP_ERROR_SYSTEM_PCE_RESERVED_2 		CODE="79"	STRING=" "/>
    <RSVP_ERROR_SYSTEM_PCE_RESERVED_3 		CODE="80"	STRING=" "/>
    <RSVP_ERROR_SYSTEM_PCE_RESERVED_4 		CODE="81"	STRING=" "/>
    <RSVP_ERROR_SYSTEM_PCE_RESERVED_5 		CODE="82"	STRING=" "/>
    <RSVP_ERROR_SYSTEM_PROV_MOD_ILLEGAL 	CODE="83"	STRING="Provisioning alloc fail"/>
    <RSVP_ERROR_SYSTEM_ASSORTED_ERROR_RSV_1 	CODE="84"	STRING=" "/>
    <RSVP_ERROR_SYSTEM_ASSORTED_ERROR_RSV_2 	CODE="85"	STRING=" "/>
    <RSVP_ERROR_SYSTEM_ASSORTED_ERROR_RSV_3 	CODE="86"	STRING=" "/>
    <RSVP_ERROR_SYSTEM_ASSORTED_ERROR_RSV_4 	CODE="87"	STRING=" "/>
    <RSVP_ERROR_SYSTEM_ASSORTED_ERROR_RSV_5 	CODE="88"	STRING=" "/>
    <RSVP_ERROR_SYSTEM_ASSORTED_ERROR_RSV_6 	CODE="89"	STRING=" "/>
    <RSVP_ERROR_SYSTEM_ASSORTED_ERROR_RSV_7 	CODE="90"	STRING=" "/>
  </RSVP_ERROR_SYSTEM>

  <RSVP_ERROR_ROUTING 		CODE="24" 	STRING="Routing Problem">
    <RSVP_ERROR_ROUTING_NONE		CODE="0"	STRING="No Error"/>
    <RSVP_ERROR_ROUTING_ERO		CODE="1"	STRING="Bad EXPLICIT_ROUTE Object"/>
    <RSVP_ERROR_ROUTING_STRICT		CODE="2"	STRING="Bad Strict Node"/>
    <RSVP_ERROR_ROUTING_LOOSE		CODE="3"	STRING="Bad Loose Node"/>
    <RSVP_ERROR_ROUTING_INITIAL		CODE="4"	STRING="Bad Initial Subobject"/>
    <RSVP_ERROR_ROUTING_NO_ROUTE	CODE="5"	STRING="No Route Towards Destination"/>
    <RSVP_ERROR_ROUTING_RRO_SYNTAX	CODE="6"	STRING="RRO Syntaz Error Detected"/>
    <RSVP_ERROR_ROUTING_LOOP		CODE="7"	STRING="RRO Indicated Routing Loops"/>
    <RSVP_ERROR_ROUTING_NO_HOP		CODE="8"	STRING="MPLS and non-RSVP-capable Router"/>
    <RSVP_ERROR_ROUTING_LABEL		CODE="9"	STRING="MPLS Label Allocation Failure"/>
    <RSVP_ERROR_ROUTING_L3PID		CODE="10"	STRING="Unsupported L3PID"/>
    <RSVP_ERROR_ROUTING_LABEL_SET	CODE="11"	STRING="MPLS Label Set Failure"/>
    <RSVP_ERROR_ROUTING_SWITCH_TP 	CODE="12"	STRING="Bad Switching Type"/>
    <RSVP_ERROR_ROUTING_UNACCEPT_LBL 	CODE="13"	STRING="Unacceptable label"/>
    <RSVP_ERROR_ROUTING_ENCODING 	CODE="14"	STRING="Unsupported encoding"/>
    <RSVP_ERROR_ROUTING_LINK_PROT 	CODE="15"	STRING="Unsupported link protection"/>
  </RSVP_ERROR_ROUTING>

  <RSVP_ERROR_NOTIFY 		CODE="25" 	STRING="Notify">
    <RSVP_ERROR_NOTIFY_RRO_MTU		CODE="1"	STRING="RRO too Large for MTU"/>
    <RSVP_ERROR_NOTIFY_RRO		CODE="2"	STRING="RRO Notification"/>
    <RSVP_ERROR_NOTIFY_UNDEF 		CODE="3"	STRING="Undefined error"/>
    <RSVP_ERROR_NOTIFY_CTRL_ACT 	CODE="4"	STRING="Control channel active"/>
    <RSVP_ERROR_NOTIFY_CTRL_DEGRADE 	CODE="5"	STRING="Control channel degraded"/>
  </RSVP_ERROR_NOTIFY>

  <RSVP_e_ERR_RSVP_MAX 		CODE="128"	STRING=" "/>
  <RSVP_e_ERR_OK		CODE="129"	STRING="ERR_OK"/>
  <RSVP_e_ERR_NO_MEMORY		CODE="130"	STRING="NO_MEMORY"/>
  <RSVP_e_ERR_PC_FAILED		CODE="131"	STRING="Path computation failed"/>
  <RSVP_e_ERR_PC_UNAVAILABLE	CODE="132"	STRING="Path computation unavailable"/>
  <RSVP_e_ERR_RM_PROBLEM	CODE="133"	STRING="Resource allocation error or alarm"/>
  <RSVP_e_ERR_RM_RESTORATION	CODE="134"	STRING="Error restoring resources"/>
  <RSVP_e_ERR_OE_RECOMMIT	CODE="135"	STRING="Temporary error: OCT will be recommitted"/>
  <RSVP_e_ERR_BAD_DEV_ID	CODE="136"	STRING="Bad source device specified"/>
  <RSVP_e_ERR_CP_DISABLED	CODE="137"	STRING="Control plane disabled for debugging"/>
  <RSVP_e_ERR_NO_PROTECTION	CODE="138"	STRING="No protection was provisioned or is applicable for this OCTP"/>
  <RSVP_e_ERR_SETUP_TIMEOUT	CODE="139"	STRING="OCTP setup time out"/>
  <RSVP_e_ERR_PROTECTION_UNAVAIL CODE="140"	STRING="Required protection can not be installed"/>
  <RSVP_e_ERR_OCTPDB_MAX_ERROR	CODE="141"	STRING=" "/>

</MPLSErrors>
