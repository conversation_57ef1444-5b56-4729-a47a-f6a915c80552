# Overview
This directory contains all the necessary helm chart and tools to deploy the MNC application on a Kubernetes cluster.


## worker nodes labels
-  Add `mnc-node` labels to 3 worker nodes that will be used for mnc
```shell
kubectl label nodes <nodename-1> mnc-node=node1
kubectl label nodes <nodename-1> mnc-node=node2
kubectl label nodes <nodename-1> mnc-node=node3
```

## create directories for local storage
- Only in case local storage is used then directories need to be created on the mnc worker nodes
- For example:
the following directories need to be created on the worker nodes 
```shell
# gnss
mkdir -p /mnt/adtran/mnc-synca-gnss-ts-data
mkdir -p /mnt/adtran/mnc-synca-gnss-kafka-data
# tpa
mkdir -p /mnt/adtran/mnc-synca-tpa-ts-data
mkdir -p /mnt/adtran/mnc-synca-tpa-kafka-data
```  
