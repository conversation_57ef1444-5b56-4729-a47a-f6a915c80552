#!/bin/bash
#
# Copyright 2024 Adtran Networks SE. All rights reserved.
#
# Owner: zyan<PERSON><PERSON><PERSON>
#

##   script to create a new service account and return the output(key and password) to the caller
##   in case successful, a new service account shall be added to the minio user. the output of the script shall contain the added
##   service account details in the form of:
##      Access Key: [access key]
##      Secret Key: [secret key]
##      Expiration: [expiration option]
##
##   for example:
##      Access Key: ********************
##      Secret Key: IPIUmicLe1RmSI3SICSJ3t2Lt7McYlMinTEeTL43
##      Expiration: no-expiry

# set -e # automatic exit on error
typeset -r dirOfThisScript=$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )
source "${dirOfThisScript}"/base/util_common.sh


if [[ "$1" == "-h" || "$1" == "--help" || $# -gt 0 ]]; then
    echo "Usage:"
    echo "$0"
    echo "Optional environment variables can be set:"
    echo "export MINIO_NAMESPACE=<minio namespace> - default is 'mnc-common'"
    echo "export MINIO_RELEASE_NAME=<minio release name> - default is 'minio'"
    echo "export MINIO_ADMIN_USERNAME=<minio console admin username> - default is 'admin'"
    echo "export MINIO_ADMIN_PASSWORD=<minio console admin password> - if not set shall be interactively requested by the script"
    exit 0
fi

if [ -z "${MINIO_ADMIN_PASSWORD}" ]; then
  # get the database password only in case env variable is missing
  echo "Please enter minio admin password and press Enter"
  read -r -s minioAdminPass
  if [ -z "${minioAdminPass}" ]; then
    printError "Password cannot be empty"
    printError "Failed to create minio service account for mnc-minio-user"
    exit ${FAILURE_CODE}
  fi
  export MINIO_ADMIN_PASSWORD="${minioAdminPass}"
fi

if [ -z "${MINIO_ADMIN_USERNAME}" ]; then
  export MINIO_ADMIN_USERNAME="admin"
fi

if [ -z "${MINIO_NAMESPACE}" ]; then
  export MINIO_NAMESPACE="mnc-common"
fi

if [ -z "${MINIO_RELEASE_NAME}" ]; then
  export MINIO_RELEASE_NAME="minio"
fi

echo "Using namespace=${MINIO_NAMESPACE}"
echo "Using release name=${MINIO_RELEASE_NAME}"
echo "Using minio admin username=${MINIO_ADMIN_USERNAME}"


# export the environment variables to be used by the exec_in_pod() function
export NAMESPACE=$MINIO_NAMESPACE
export POD_NAME="$MINIO_RELEASE_NAME-0"

#Connect to minio server from within the cluster(from inside the pod), by adding an alias
#And giving it the minio service endpoint and the admin user and password
#Aftre that we can just use the alias as the minio server we want to execute commands on
echo "Connecting to minio server..."
export COMMAND="mc alias set minio https://${MINIO_RELEASE_NAME}.${MINIO_NAMESPACE}.svc.cluster.local:9000 ${MINIO_ADMIN_USERNAME} ${MINIO_ADMIN_PASSWORD} --insecure"

# Call the helper script to execute the command and return output
output=$(exec_in_pod)
if [[ ! "{$output}" =~ "success" ]]; then
  printError "Failed to connect to minio server: ${output}"
  exit ${FAILURE_CODE}
fi

#Add a new service account to a user (access key and password shall be auto generated and returned as output)
echo "Adding new service account to mnc-minio-user..."
export COMMAND="mc admin user svcacct add minio/ mnc-minio-user --insecure"

# Call the helper script to execute the command and return output
output=$(exec_in_pod)
if [[ ! "{$output}" =~ "Access Key:" ]]; then
  printError "Failed to add service account for user: 'mnc-minio-user' in minio server: ${output}"
  exit ${FAILURE_CODE}
else
  echo "A new service account was added to mnc-minio-user:"
  echo "${output}"
fi