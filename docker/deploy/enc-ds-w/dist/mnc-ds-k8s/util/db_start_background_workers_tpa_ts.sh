#!/bin/bash
#
# Copyright 2024 Adtran Networks SE. All rights reserved.
#
# Owner: wpta<PERSON>ski
#

## script to start background timescaledb workers after db_backup_pg_synca_tpa_ts.sh completes

## this script is connecting to the database as the superuser to be able to stop db background workers. Since the superuser is "postgres"
## which matches the system user in timescaledb container, password authentication is not required ("peer" auth method for local users specified in pg_hba.conf)

set -e # automatic exit on error
typeset -r dirOfThisScript=$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )
source "${dirOfThisScript}"/base/util_common.sh

#cd ${dirOfThisScript}

if [[ "$1" == "-h" || "$1" == "--help" || $# -gt 0 ]]; then
    echo "Usage:"
    echo "$0"
    echo "Optional environment variables can be set:"
    echo "export DB_NAMESPACE=<namespace of the deployed database> - default is 'mnc-synca-tpa'"
#    echo "export DB_RELEASE_NAME=<release name of the deployed database> - default is 'timescaledb'"
#    echo "export DB_PASSWORD=<password to access the deployed database> - if not set shall be interactively requested by the script"
    exit 0
fi

if [ -z "${DB_NAMESPACE}" ]; then
  export DB_NAMESPACE="mnc-synca-tpa"
fi

#if [ -z "${DB_RELEASE_NAME}" ]; then
#  export DB_RELEASE_NAME="timescaledb"
#fi

DB_CONTAINER_NAME="timescaledb"
DB_USERNAME="postgres"
DB_NAME="fnm_sync_pm"

# DB_CREDENTIALS_SECRET_NAME="${DB_RELEASE_NAME}-credentials"

# if [ -z "${DB_PASSWORD}" ]; then
  # dbPass=$(kubectl get secret ${DB_CREDENTIALS_SECRET_NAME} -n ${DB_NAMESPACE} -o jsonpath="{.data.PATRONI_SUPERUSER_PASSWORD}" 2> /dev/null | base64 --decode)
  # if [[ -n "$(echo $dbPass | grep -i 'not found')" || -z "$dbPass" ]]; then
    ## get the database password only in case env variable is missing and failed to get it from the secret
    # echo "Please enter tpa timescale database superuser password"
    # read -r -s dbPass
    # if [ -z "${dbPass}" ]; then
      # printError "Password cannot be empty"
      # printError "Failed to start TPA timescale database background workers"
      # exit ${FAILURE_CODE}
    # fi
  # fi
  # export DB_PASSWORD="${dbPass}"
# fi

echo "Using namespace=${DB_NAMESPACE}"
# echo "Using release name=${DB_RELEASE_NAME}"
echo "Using container name=${DB_CONTAINER_NAME}"
echo "Using db name=${DB_NAME}"
echo "Using db username=${DB_USERNAME}"

# needed for find_db_master_pod() and exec-in_pod()
export NAMESPACE=$DB_NAMESPACE

# Find the master pod dynamically
printInfoMessage "Searching for the database master pod in the namespace ${DB_NAMESPACE} ..."
set +e
output=$(find_db_master_pod)
set -e
if [ -n "$(echo $output | grep 'Error')" ]; then
  printError "${output}"
  exit $FAILURE_CODE
fi
MASTER_POD=${output}
printInfoMessage "${MASTER_POD} is the master database pod in the ${DB_NAMESPACE} namespace"

# needed for exec-in_pod()
export POD_NAME=$MASTER_POD
export CONTAINER_NAME=$DB_CONTAINER_NAME
export COMMAND="PGPASSWORD=$DB_PASSWORD psql -At -d $DB_NAME -U $DB_USERNAME -c 'SELECT _timescaledb_functions.start_background_workers()'"

printInfoMessage "starting background workers"

set +e
output=$(exec_in_pod)
set -e
# fails when exec_in_pod() returns error message or sql statement output is not "t"
if [[ -n "$(echo $output | grep "Error")" || ! "$output" =~ ^[[:space:]]*t[[:space:]]*$ ]]; then
  printError "Failed to start TPA timescale database background workers."
  echo "DB response: $output"
  exit $FAILURE_CODE
else
  echo "DB response: $output"
  printInfoMessage "TPA timescale database background workers started successfully."
fi
