#!/bin/bash
#
# Copyright 2024 Adtran Networks SE. All rights reserved.
#
# Owner: zyan<PERSON><PERSON><PERSON>
#

## script to restore the master(db might be running in HA mode) synca gnss timescale db instance from a logical pg_dump file
## backup file is expected to be available on the local machine before running the script

set -e # automatic exit on error
typeset -r dirOfThisScript=$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )
source "${dirOfThisScript}"/base/util_common.sh

#cd ${dirOfThisScript}
if [[ "$1" == "-h" || "$1" == "--help" || $# -lt 1 || $# -gt 1 ]]; then
    echo "Usage:"
    echo "$0 [<full path of timescale db backup file>]"
    echo "Optional environment variables can be set:"
    echo "export DB_NAMESPACE=<namespace of the deployed database> - default is 'mnc-synca-gnss'"
    echo "export DB_RELEASE_NAME=<release name of the deployed database> - default is 'timescale'"
    exit 0
fi

export DB_BACKUP_FILE_NAME=$1

if [ -z "${DB_NAMESPACE}" ]; then
  export DB_NAMESPACE="mnc-synca-gnss"
fi

if [ -z "${DB_RELEASE_NAME}" ]; then
  export DB_RELEASE_NAME="timescale"
fi

export DB_CONTAINER_NAME="timescaledb"
export DB_USERNAME="fnm_sync_pm"
export DB_NAME="fnm_sync_pm"

"${dirOfThisScript}"/base/db_restore_pg.sh