# This file and its contents are licensed under the Apache License 2.0.
# Please see the included NOTICE for copyright information and LICENSE for a copy of the license.
{{/*
The sole purpose of this job is to trigger <PERSON><PERSON><PERSON> to rewrite the
Dynamic Configuration[1] in the DCS.

As the configuration may change between every invocation of <PERSON><PERSON>, we
need to run this trigger after every update.

To prevent <PERSON><PERSON> failing to update a deployment, we add helm release revision
number to the created job name; <PERSON><PERSON> cannot create a job with the same name,
however, (completed) jobs will not immediately be removed by Kubernetes.

1: https://patroni.readthedocs.io/en/latest/dynamic_configuration.html
*/}}
apiVersion: batch/v1
kind: Job
metadata:
  name: "{{ template "timescaledb.fullname" . }}-patroni-{{ .Release.Revision }}"
  namespace: {{ .Release.Namespace }}
  labels:
    {{ include "timescaledb-helm.labels" . | nindent 4 }}
    app.kubernetes.io/component: patroni
  annotations:
    "helm.sh/hook": post-upgrade
    "helm.sh/hook-delete-policy": hook-succeeded
spec:
  activeDeadlineSeconds: 120
  template:
    metadata:
      labels:
        {{ include "timescaledb-helm.labels" . | nindent 8 }}
    spec:
      restartPolicy: OnFailure
      containers:
      - name: {{ template "timescaledb.fullname" . }}-patch-patroni-config
        image: "{{ .Values.curlImage.repository }}:{{ .Values.curlImage.tag }}"
        imagePullPolicy: {{ .Values.curlImage.pullPolicy }}
        command: ["/bin/sh"]
        # Patching the Patroni configuration is good, however it should not block an upgrade from going through
        # Therefore we ensure we always exit with an exitcode 0, so that Helm is satisfied with this upgrade job
        args:
        - '-c'
        - |
          /usr/bin/curl --connect-timeout 30 --include --request PATCH --data \
          {{ .Values.patroni.bootstrap.dcs | toJson | quote }} \
          "http://{{ template "clusterName" . }}-config:8008/config"
          exit 0
