# This file and its contents are licensed under the Apache License 2.0.
# Please see the included NOTICE for copyright information and LICENSE for a copy of the license.
{{- if eq .Values.secrets.pgbackrestSecretName "" }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "secrets_pgbackrest" $ }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{ include "timescaledb-helm.labels" . | nindent 4 }}
    app.kubernetes.io/component: pgbackrest
  annotations:
    "helm.sh/hook": pre-install
    "helm.sh/hook-weight": "0"
type: Opaque
{{- if and (.Release.IsUpgrade) (ne (len .Values.secrets.pgbackrest) 0) }}
data: {{ (lookup "v1" "Secret" .Release.Namespace (include "secrets_pgbackrest" .)).data }}
{{- else }}
stringData:
{{ toYaml .Values.secrets.pgbackrest | indent 2 }}
{{- end }}
...
{{- end }}
