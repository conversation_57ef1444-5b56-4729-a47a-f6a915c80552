# This file and its contents are licensed under the Apache License 2.0.
# Please see the included NOTICE for copyright information and LICENSE for a copy of the license.

{{- if .Values.pgBouncer.enabled }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "timescaledb.fullname" . }}-pgbouncer
  namespace: {{ .Release.Namespace }}
  labels:
    {{ include "timescaledb-helm.labels" . | nindent 4 }}
    app.kubernetes.io/component: pgbouncer
data:
  pg_hba.conf: |
  {{- range $hba := (.Values.pgBouncer.pg_hba | default (list "local all all peer")) }}
    {{ $hba }}
  {{- end }}
  pgbouncer-sidecar.ini: |
    [databases]
    * =
    [pgbouncer]
    pidfile = /var/run/postgresql/pgbouncer.pid
    listen_addr = *
    listen_port = 6432
    unix_socket_dir = /var/run/postgresql
    unix_socket_mode = 0755
    # We want to protect the superusers as much as we can, we therefore disallow superusers to connect
    # on many levels, even at this point.
    # We add the application_name in there to ensure the connections for using pass through authentication
    # are easily identified
    auth_query = SELECT rolname, rolpassword FROM pg_catalog.set_config('application_name', 'pgbouncer authentication', false) CROSS JOIN pg_catalog.pg_authid WHERE rolname = $1 AND NOT rolsuper AND NOT rolreplication AND NOT rolbypassrls
    auth_type = hba
    auth_hba_file = /etc/pgbouncer/pg_hba.conf
    auth_user = postgres
    {{- if not .Values.pgBouncer.config.client_tls_sslmode }}
    client_tls_sslmode=require
    {{- end }}
    client_tls_key_file = /etc/certificate/tls.key
    client_tls_cert_file = /etc/certificate/tls.crt
    # Loading these last, to allow for overriding of any of the above defaults. Since the
    # last instance of an option gets used. Exercise caution overriding any of the above.
    {{- $config := .Values.pgBouncer.config | default dict }}
    {{- range $key := keys $config | sortAlpha }}
    {{ $key }} = {{ index $config $key }}
    {{- end }}
...
{{ end }}
