# This file and its contents are licensed under the Apache License 2.0.
# Please see the included NOTICE for copyright information and LICENSE for a copy of the license.

{{- if .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "timescaledb.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{ include "timescaledb-helm.labels" . | nindent 4 }}
    app.kubernetes.io/component: rbac
subjects:
  - kind: ServiceAccount
    name: {{ template "timescaledb.serviceAccountName" . }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ template "timescaledb.fullname" . }}
{{- end }}
