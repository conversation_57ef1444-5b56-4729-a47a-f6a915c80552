# This file and its contents are licensed under the Apache License 2.0.
# Please see the included NOTICE for copyright information and LICENSE for a copy of the license.
{{- if eq .Values.secrets.certificateSecretName "" }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "secrets_certificate" $ }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{ include "timescaledb-helm.labels" . | nindent 4 }}
    app.kubernetes.io/component: certificates
  annotations:
    "helm.sh/hook": pre-install
    "helm.sh/hook-weight": "0"
type: kubernetes.io/tls
{{- if .Release.IsUpgrade }}
data: {{ (lookup "v1" "Secret" .Release.Namespace (include "secrets_certificate" .)).data }}
{{ else }}
{{ $ca := genCA (include "clusterName" .) 1826 -}}
stringData:
  tls.crt: {{ $ca.Cert | quote }}
  tls.key: {{ $ca.Key  | quote }}
{{- end }}
...
{{- end }}
