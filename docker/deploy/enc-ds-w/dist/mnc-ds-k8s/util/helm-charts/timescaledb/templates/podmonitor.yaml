{{- if .Values.podMonitor.enabled }}
---
apiVersion: monitoring.coreos.com/v1
kind: PodMonitor
metadata:
  name: {{ template "timescaledb.fullname" . }}
  {{- if .Values.podMonitor.namespace }}
  namespace: {{ .Values.podMonitor.namespace }}
  {{- end }}
  labels:
    {{ include "timescaledb-helm.labels" . | nindent 4 }}
    {{- if .Values.podMonitor.labels -}}
    {{ toYaml .Values.podMonitor.labels | nindent 4 }}
    {{- end }}
spec:
  podMetricsEndpoints:
  - interval: {{ .Values.podMonitor.interval }}
    {{- if .Values.podMonitor.scrapeTimeout }}
    scrapeTimeout: {{ .Values.podMonitor.scrapeTimeout }}
    {{- end }}
    honorLabels: true
    port: pg-exporter
    path: {{ .Values.podMonitor.path }}
    {{- if .Values.podMonitor.metricRelabelings }}
    metricRelabelings: {{ toYaml .Values.podMonitor.metricRelabelings | nindent 6 }}
    {{- end }}
  jobLabel: "{{ .Release.Name }}"
  selector:
    matchLabels:
      app: {{ template "timescaledb.fullname" . }}
      release: "{{ .Release.Name }}"
  {{- if .Values.podMonitor.podTargetLabels }}
  podTargetLabels: {{ toYaml .Values.podMonitor.podTargetLabels | nindent 4 }}
  {{- end }}
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
{{- end }}
