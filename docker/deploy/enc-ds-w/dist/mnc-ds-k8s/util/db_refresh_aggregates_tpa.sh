#!/bin/bash
#
# Copyright 2025 Adtran Networks SE. All rights reserved.
#
# Owner: wlod<PERSON><PERSON>rz<PERSON>


## base script to refresh the all TPA aggregates.
## It uses the refresh window as equal to the maximum allowed retention period that can be used by TPA - 365 days

set -e # automatic exit on error
typeset -r dirOfThisScript=$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )
source "${dirOfThisScript}"/base/util_common.sh

if [[ "$1" == "-h" || "$1" == "--help" || $# -gt 0 ]]; then
    echo "Usage:"
    echo "$0"
    echo "Optional environment variables can be set:"
    echo "export DB_NAMESPACE=<namespace of the deployed database> - default is 'mnc-synca-tpa'"
    echo "export DB_PASSWORD=<password to access the deployed database> - if not set shall be interactively requested by the script"
    exit $SUCCESS_CODE
fi

if [ -z "${DB_PASSWORD}" ]; then
  # get the database password only in case env variable is missing
  echo "Please enter tpa timescale database password"
  read -r -s dbPass
  if [ -z "${dbPass}" ]; then
    printError "Password cannot be empty"
    printError "Failed to refresh TPA timescale aggregates"
    exit ${FAILURE_CODE}
  fi
  export DB_PASSWORD="${dbPass}"
fi

if [ -z "${DB_NAMESPACE}" ]; then
  export DB_NAMESPACE="mnc-synca-tpa"
fi

export DB_CONTAINER_NAME="timescaledb"
export DB_USER="fnm_sync_pm"
export AGGREGATES="tpadataaccess.tpa_rtie_five_min tpadataaccess.tpa_dtie_five_min"
export REFRESH_WINDOW="365"

"${dirOfThisScript}"/base/db_refresh_aggregates.sh