#!/bin/bash
#
# Copyright 2023 Adtran Networks SE. All rights reserved.

## Script to acquire the token used for secure communication with MNC and store it in Docker secret

if [[ "$1" == "-h" || "$1" == "--help" || $# -lt 1 || $# -gt 1 ]]; then
    echo "Usage:"
    echo "$0 [<MNC server IP address>]"
    echo "<MNC server IP address> - IP address of MNC server from which the token should be acquired."
    echo "export MNC_SECURE_PORT=<MNC rest secure port no>   # ==> to use custom port (default is 8443)"
    echo "export MNC_ADMIN_PASSWORD=<MNC Admin password>   # ==> to skip interactive password query"
    exit 0
fi

typeset -r dirOfThisScript=$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )
source "${dirOfThisScript}"/base/util_common.sh
set -e # automatic exit on error

cd ${dirOfThisScript}
typeset -r encIp=$1
typeset -r tokenFile="mnc_token.txt"
typeset -r encPort=${MNC_SECURE_PORT:-8443}
declare -i httpResponseCode=1
declare -i curlExitCode=1


# get the password
if [ -n "$MNC_ADMIN_PASSWORD" ]; then
  printInfoMessage "using MNC_ADMIN_PASSWORD environment variable, skipping password query"
  encAdminPass=${MNC_ADMIN_PASSWORD}
else
  echo "Please enter MNC Admin password and press Enter"
  read -r -s encAdminPass
  if [ -z "${encAdminPass}" ]; then
    printError "Password cannot be empty"
    printError "Generation of the token FAILED"
    exit ${FAILURE_CODE}
  fi
fi

# get the token from MNC
printInfoMessage "Get token from MNC running on ${encIp}"
urlRequestToken="https://${encIp}:${encPort}/advabase/ca/refreshToken/synca"
set +e # disable automatic exit on error
# to determine token acquisition status curl exit code and http status are used
# it is not enough to verify if the response has content, because the content can be present even if token was not acquired successfully (e.g. ERROR 403 when wrong password is used)
# token acquisition is considered to be failure in case curl exit code !=0 or http status !=200
httpResponseCode=$(curl -w %{http_code} -k --silent --location --request POST "${urlRequestToken}" -u admin:"${encAdminPass}" -o "${tokenFile}")
curlExitCode=$?
set -e # enable automatic exit on error
if [[ ${httpResponseCode} -ne 200 || ${curlExitCode} -ne 0 ]]; then
  printError "ERROR when trying to get the token from MNC:"
  echo "exit code: ${curlExitCode}"
  echo "http status code: ${httpResponseCode}"
  printError "Generation of the token FAILED"
  rm -f "${dirOfThisScript}/${tokenFile}"
  exit ${FAILURE_CODE}
fi
printInfoMessage "Generation of the token finished with SUCCESS"

