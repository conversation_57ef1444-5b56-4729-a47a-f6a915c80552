#!/bin/bash
#
# Copyright 2024 Adtran Networks SE. All rights reserved.
#
# Owner: zyankel<PERSON>itz
#

## script to clear the bucket contents (recursive) of the minio bucket holding the backups for synca tpa timescale db

set -e # automatic exit on error
typeset -r dirOfThisScript=$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )
source "${dirOfThisScript}"/base/util_common.sh

if [[ "$1" == "-h" || "$1" == "--help" || $# -gt 0 ]]; then
    echo "Usage:"
    echo "$0"
    echo "Optional environment variables can be set:"
    echo "export MINIO_NAMESPACE=<minio namespace> - default is 'mnc-common'"
    echo "export MINIO_RELEASE_NAME=<minio release name> - default is 'minio'"
    echo "export MINIO_ADMIN_USERNAME=<minio console admin username> - default is 'admin'"
    echo "export MINIO_ADMIN_PASSWORD=<minio console admin password> - if not set shall be interactively requested by the script"
    echo "export SKIP_CONFIRM=y - to skip confirmation check before performing this operation"
    exit 0
fi

if [ -z "${MINIO_ADMIN_PASSWORD}" ]; then
  # get the database password only in case env variable is missing
  echo "Please Enter minio admin password and press"
  read -r -s minioAdminPass
  if [ -z "${minioAdminPass}" ]; then
    printError "Password cannot be empty"
    printError "Failed to clear minio bucket for synca tpa timescale db"
    exit ${FAILURE_CODE}
  fi
  export MINIO_ADMIN_PASSWORD="${minioAdminPass}"
fi

if [ -z "${MINIO_NAMESPACE}" ]; then
  export MINIO_NAMESPACE="mnc-common"
fi

if [ -z "${MINIO_RELEASE_NAME}" ]; then
  export MINIO_RELEASE_NAME="minio"
fi

if [ -z "${MINIO_ADMIN_USERNAME}" ]; then
  export MINIO_ADMIN_USERNAME="admin"
fi

export MINIO_BUCKET_NAME="mnc-synca-tpa-ts-backup"

if [ -z "${SKIP_CONFIRM}" ]; then
  read -p "Are you sure you want to clear the '${MINIO_BUCKET_NAME}' bucket contents (Y/y)?" -n 1 -r
  if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Operation cancelled."
    exit 0
  fi
fi

"${dirOfThisScript}"/base/bucket_clear_minio.sh
