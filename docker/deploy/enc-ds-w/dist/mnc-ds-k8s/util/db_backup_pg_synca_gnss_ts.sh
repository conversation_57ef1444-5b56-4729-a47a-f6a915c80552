#!/bin/bash
#
# Copyright 2024 Adtran Networks SE. All rights reserved.
#
# Owner: zyan<PERSON><PERSON><PERSON>
#

## script to create a logical postgreSQL db backup (pg_dump) from a master synca gnss timescale db instance (db might be running in HA mode)
## should create the db backup file at the location of the script
## backup file name format: <db release name>_<db namespace>_backup_<Date Time of the file creation in format:yyyyMMdd_HHmmss>
## for example: gnss_timescale_db_backup_20240316_180003

set -e # automatic exit on error
typeset -r dirOfThisScript=$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )
source "${dirOfThisScript}"/base/util_common.sh

#cd ${dirOfThisScript}

if [[ "$1" == "-h" || "$1" == "--help" || $# -gt 0 ]]; then
    echo "Usage:"
    echo "$0"
    echo "Optional environment variables can be set:"
    echo "export DB_NAMESPACE=<namespace of the deployed database> - default is 'mnc-synca-gnss'"
    echo "export DB_RELEASE_NAME=<release name of the deployed database> - default is 'timescale'"
    echo "export DB_PASSWORD=<password to access the deployed database> - if not set shall be interactively requested by the script"
    exit 0
fi


if [ -z "${DB_PASSWORD}" ]; then
  # get the database password only in case env variable is missing
  echo "Please Enter gnss timescale database password and press"
  read -r -s dbPass
  if [ -z "${dbPass}" ]; then
    printError "Password cannot be empty"
    printError "Failed to backup GNSS timescale database"
    exit ${FAILURE_CODE}
  fi
  export DB_PASSWORD="${dbPass}"
fi

if [ -z "${DB_NAMESPACE}" ]; then
  export DB_NAMESPACE="mnc-synca-gnss"
fi

if [ -z "${DB_RELEASE_NAME}" ]; then
  export DB_RELEASE_NAME="timescale"
fi

export DB_CONTAINER_NAME="timescaledb"
export DB_USERNAME="fnm_sync_pm"
export DB_NAME="fnm_sync_pm"

"${dirOfThisScript}"/base/db_backup_pg.sh