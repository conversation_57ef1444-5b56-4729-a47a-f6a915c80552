{{- if .Values.localPersistentVolumes.enabled }}
{{- $nodeCount := int .Values.minio.statefulset.replicaCount }}
{{- $drivesCountPerNode := int .Values.minio.statefulset.drivesPerNode }}
{{- $persistence := dict "size" .Values.minio.persistence.size "storageClass" .Values.minio.persistence.storageClass }}
{{- $localVolumePathPrefix := .Values.localPersistentVolumes.path }}
{{- $chartNameAndVersion := printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- $releaseName := .Release.Name }}
{{- $releaseService := .Release.Service }}
{{- range $nodeIndex := until $nodeCount }}
{{- $currentNodeIndex := (add $nodeIndex 1) }}
{{- range $driveIndex := until $drivesCountPerNode }}
{{- $currentDriveIndex := (add $driveIndex 1) }}
apiVersion: v1
kind: PersistentVolume
metadata:
  name: mnc-common-minio-data-node{{ $currentNodeIndex }}-d{{ $currentDriveIndex }}
  labels:
    helm.sh/chart: {{ $chartNameAndVersion }}
    app.kubernetes.io/instance: {{ $releaseName }}
    app.kubernetes.io/managed-by: {{ $releaseService }}
  annotations:
    # helm.sh/hook: pre-install,post-delete
    # helm.sh/hook-weight: 0
    # resource will not be deleted by helm
    helm.sh/resource-policy: keep
spec:
  capacity:
    storage: {{ $persistence.size }}
  volumeMode: Filesystem
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: {{ $persistence.storageClass | default "mnc-common-minio-local-storage"}}
  local:
    path: {{ $localVolumePathPrefix | default "/mnt/adtran/mnc-common-minio-data"}}-d{{ $currentDriveIndex }}
  nodeAffinity:
    required:
      nodeSelectorTerms:
        - matchExpressions:
          - key: mnc-node
            operator: In
            values:
              - node{{ $currentNodeIndex }}
---
{{- end }}
{{- end }}
{{- end }}