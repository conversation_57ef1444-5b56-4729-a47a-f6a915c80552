localPersistentVolumes:
  ## enabled [mandatory]
  ## Set to false when the automatic pv provisioning is available
  ## Set to true for the creation local pv when automatic provisioning is not available
  ## if enabled, it will automatically create (.Values.minio.statefulset.replicaCount * .Values.minio.statefulset.drivesPerNode) persistent volumes,
  ## on each node it shall create .Values.minio.statefulset.drivesPerNode persistent volumes
  ##   e.g. for a 3 node cluster(replicaCount: 3) with 2 drives per node(drivesPerNode: 2) it will generate 6 PVs, 2 on each nod
  ## PV name format:
    ## mnc-common-minio-data-node<replica number starting from 1>-d<drive number starting from 1>
    ## e.g. mnc-common-minio-data-node1-d1
  ## PV local path name format:
    ## <path>-d<drive number starting from 1>
    ## e.g. /mnt/adtran/mnc-common-minio-data-d1
  enabled: true
  ## --------------------------------------
  ## path: [optional] (pathPrefix)
  ## Default path prefix is /mnt/adtran/mnc-common-minio-data
  ## Important: the actual local path that needs to be created on the host is: <path>-d<drive number starting from 1>
  ## e.g. to use 2 drives per node, using the default path(/mnt/adtran/mnc-common-minio-data):
    ## set drivesPerNode: 2
    ## create the following two paths on each worker node:
      ##  /mnt/adtran/mnc-common-minio-data-d1
      ##  /mnt/adtran/mnc-common-minio-data-d2
  ##
  ## Set a custom specific directory prefix path for the local persistent volumes
  # path: "/mnt/adtran/mnc-common-minio-data"
  ## --------------------------------------
minio:
  image:
    ## Set the registry value to empty string so that only the repository is used to construct the image name
    registry: ""
    repository: docker.io/bitnami/minio
    tag: 2024.2.26-debian-12-r0
    digest: ""
  clientImage:
    ## Set the registry value to empty string so that only the repository is used to construct the image name
    registry: ""
    repository: docker.io/bitnami/minio-client
    tag: 2024.2.24-debian-12-r0
    digest: ""
  persistence:
    enabled: true
    ## Must be set to "mnc-common-minio-local-storage" if using local storage
    ## When is set to "mnc-common-minio-local-storage" the localPersistentVolumes.enabled must be set to true
    storageClass: "mnc-common-minio-local-storage"
    size: 500Gi
  auth:
    rootUser: admin
    rootPassword: "ChgMeNOW"
  tls:
    enabled: true
    autoGenerated: true
#  service:
#    type: NodePort
#    nodePorts:
#      api: ""
#      console: "30811"
  ## server mode (`standalone` or `distributed`). ref: https://docs.minio.io/docs/distributed-minio-quickstart-guide
  mode: distributed
  statefulset:
    ## Number of pods per zone (only for distributed mode).
    replicaCount: 3
    ##  Number of zones (only for distributed mode)
    zones: 1
    ## Number of drives attached to every node (only for distributed mode)
    ## Total number of drives (drivesPerNode*replicaCount*zones) should be even and `>= 4`
    drivesPerNode: 2

  provisioning:
    ## Enable provisioning Job
    enabled: true

    ## users provisioning. Can be used in addition to provisioning.usersExistingSecrets.
    ## https://docs.min.io/docs/minio-admin-complete-guide.html#user
    ## e.g.
    users:
      - username: mnc-minio-user
        password: ChgMeNOW
        disabled: false
        policies:
          - readwrite
          - consoleAdmin
          - diagnostics
        # When set to true, it will replace all policies with the specified.
        # When false, the policies will be added to the existing.
        setPolicies: false


    ## buckets provisioning per supported aplication:
    ## buckets, versioning, lifecycle, quota and tags provisioning
    ## https://docs.min.io/docs/minio-client-complete-guide.html#mb
    buckets:
      - name: mnc-synca-gnss-ts-backup
        region: region-1
        # Allowed values: "Versioned" | "Suspended" | "Unchanged"
        #  ref: https://docs.minio.io/docs/distributed-minio-quickstart-guide
        versioning: Suspended
        # Versioning is automatically enabled if withLock is true
        # ref: https://docs.min.io/docs/minio-bucket-versioning-guide.html
        withLock: false
        # quota:
        #   # set or clear(+ omit size)
        #   type: set
        #   size: 300GiB
        tags:
          # bucket and its db backup belongs to gnss application
          app: gnss
      - name: mnc-synca-tpa-ts-backup
        region: region-1
        # Allowed values: "Versioned" | "Suspended" | "Unchanged"
        #  ref: https://docs.minio.io/docs/distributed-minio-quickstart-guide
        versioning: Suspended
        # Versioning is automatically enabled if withLock is true
        # ref: https://docs.min.io/docs/minio-bucket-versioning-guide.html
        withLock: false
#        quota:
#          # set or clear(+ omit size)
#          type: set
#          size: 300GiB
        tags:
          # bucket and its db backup belongs to gnss application
          app: tpa

    ## config provisioning
    ## https://docs.min.io/docs/minio-server-configuration-guide.html
    ## setup the server region
    config:
      - name: region
        options:
          name: region-1


  ## in order to use the control node to deploy minio pod, use toleration specified below
  # tolerations:
  # - key: "node-role.kubernetes.io/control-plane"
  #   operator: "Exists"
  #   effect: "NoSchedule"

