### Deploy minio in distributed mode
1. minio topology in distributed mode: 3 minio servers(one on each worker node), 2 drives per node for a total of 6 derives
   in this topology minio can afford to loos one node and continue to function regularly
2. with automatic provisioning the administrator will need to make sure that 2 drives exist per node prior to deployment
3. If there is no automatic pv provisioning, then we need to create the pvs manually by executing the following steps:  
   3.1. create the `mnc-common-minio-local-storage` storage class as follows:
     ```yaml
     apiVersion: storage.k8s.io/v1
     kind: StorageClass
     metadata:
       name: mnc-common-minio-local-storage
     provisioner: kubernetes.io/no-provisioner
     volumeBindingMode: WaitForFirstConsumer
     ```
   3.2. Add labels to the nodes on which we want to deploy minio   
    ```shell
    kubectl label nodes <nodename-1> mnc-node=node1
    kubectl label nodes <nodename-1> mnc-node=node2
    kubectl label nodes <nodename-1> mnc-node=node3
    ```
   3.3. Create `/mnt/adtran/mnc-common-minio-data-d1` and `/mnt/adtran/mnc-common-minio-data-d2` directories on each worker node responsible for the minio. 
        Note then even when the pvs are deleted from the cluster the data will be preserved on the nodes.
4. set persistentVolumes size in values.yaml to be at least 3 times bigger than the persistentVolumes size used for timescaledb
5. Install minio helm chart from the minio directory
    ```sh
    helm upgrade --install -n minio --create-namespace minio .
    ```
 