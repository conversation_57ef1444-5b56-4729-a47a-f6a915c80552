---
# Source: minio-bitnami/charts/minio/templates/networkpolicy.yaml
kind: NetworkPolicy
apiVersion: networking.k8s.io/v1
metadata:
  name: release-name-minio
  namespace: "default"
  labels:
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: minio
    app.kubernetes.io/version: 2024.2.26
    helm.sh/chart: minio-13.6.4
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/instance: release-name
      app.kubernetes.io/name: minio
  policyTypes:
    - Ingress
    - Egress
  egress:
    - {}
  ingress:
    # Allow inbound connections
    - ports:
        - port: 9001
        - port: 9000
---
# Source: minio-bitnami/charts/minio/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: release-name-minio
  namespace: "default"
  labels:
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: minio
    app.kubernetes.io/version: 2024.2.26
    helm.sh/chart: minio-13.6.4
automountServiceAccountToken: false
secrets:
  - name: release-name-minio
---
# Source: minio-bitnami/charts/minio/templates/secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: release-name-minio
  namespace: "default"
  labels:
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: minio
    app.kubernetes.io/version: 2024.2.26
    helm.sh/chart: minio-13.6.4
type: Opaque
data:
  root-user: "YWRtaW4="
  root-password: "Q2hnTWVOT1c="
---
# Source: minio-bitnami/charts/minio/templates/tls-secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: release-name-minio-crt
  labels:
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: minio
    app.kubernetes.io/version: 2024.2.26
    helm.sh/chart: minio-13.6.4
type: kubernetes.io/tls
data:
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUVKVENDQXcyZ0F3SUJBZ0lRTVB1SGRKQ0lCN2ttSTAwNlBCb1RiakFOQmdrcWhraUc5dzBCQVFzRkFEQVQKTVJFd0R3WURWUVFERXdodGFXNXBieTFqWVRBZUZ3MHlOREF6TVRBeE56TTBOREphRncweU5UQXpNVEF4TnpNMApOREphTUIweEd6QVpCZ05WQkFNVEVuSmxiR1ZoYzJVdGJtRnRaUzF0YVc1cGJ6Q0NBU0l3RFFZSktvWklodmNOCkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFNS0h0dW1teWE3dGk3cmFmOEJlaHNna1Z3RHd6VDE3MDJ0RXFZcWMKYTc2K1RKYTNNNTlMd3dQVjNFTHNlcjJQVEZNVEpkSU1FMHFmUlNQRnRoQVhIRXpGbWpEcmwxM0g3eHQ3WlBmOQpZUzBzeWZUTjFLR3drK3BGQ2VjV0RYQ1pYNnlTUC8wRDZTdEwxZ2U3a1FWTmNNMHZJNDdFK3M3SXovb2xTOUJTCnZZb1JRbGdrZWJESlVkRG1KY1Yzdm9TemV4WUVmUDRtVDlHTzlrcGJNV3Q1NWZaL0hsbVN1QlRLYm8xczB4VGoKVU56YWZiRjlvWWVsWnBBN3l4czdoai9WSEl1ME5GWko5R3Jic0x6QXZrU3VkeHFkaEhrd25VQzVCZW5JQ21xcwpKVE9uaXl5SDQrc2dWenhtSy9GMnFUWE5BTUlGcnlaSHJBYlJydWhic0N1dnFya0NBd0VBQWFPQ0FXa3dnZ0ZsCk1BNEdBMVVkRHdFQi93UUVBd0lGb0RBZEJnTlZIU1VFRmpBVUJnZ3JCZ0VGQlFjREFRWUlLd1lCQlFVSEF3SXcKREFZRFZSMFRBUUgvQkFJd0FEQWZCZ05WSFNNRUdEQVdnQlRFR1ZVZ1BseWw0VW9TSGRNTkJLUURpSW8xUnpDQwpBUU1HQTFVZEVRU0IrekNCK0lJdUtpNXlaV3hsWVhObExXNWhiV1V0YldsdWFXOHVaR1ZtWVhWc2RDNXpkbU11ClkyeDFjM1JsY2k1c2IyTmhiSUlzY21Wc1pXRnpaUzF1WVcxbExXMXBibWx2TG1SbFptRjFiSFF1YzNaakxtTnMKZFhOMFpYSXViRzlqWVd5Q055b3VjbVZzWldGelpTMXVZVzFsTFcxcGJtbHZMV2hsWVdSc1pYTnpMbVJsWm1GMQpiSFF1YzNaakxtTnNkWE4wWlhJdWJHOWpZV3lDTlhKbGJHVmhjMlV0Ym1GdFpTMXRhVzVwYnkxb1pXRmtiR1Z6CmN5NWtaV1poZFd4MExuTjJZeTVqYkhWemRHVnlMbXh2WTJGc2dna3hNamN1TUM0d0xqR0NDV3h2WTJGc2FHOXoKZElJU2NtVnNaV0Z6WlMxdVlXMWxMVzFwYm1sdk1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQlpQRm5CcjNpawoxL3ZiclJJV2pzU2VjdUQxK1R1RnYzNlZxaXhsOFZzQ0h5dTk4T3RHQ3ljYXdBMDBVdXhOd2R5U2V4RzQ3N0xUCjBCUVNOZmljUHNPSW14MFNHdjFjQmZyTlJJWjFSYkxCdVhrUUR1RGJWeEF5LzdBVi9VUFdSUHBGb1F5czdPY3IKUjRRM3VzZHhxMWI2ZytCOUNwbU4xemVZM0E2dGtrLytiZXVjMkpUb2VPMnlpQy9HSlMyTmdhOWFXNHovTEd4MQpZUXNUMXFkOG9MUHBFcElxYmZ4Wi9RanczZ3NjWXBsZWczWW5vUnN2ZlRQRkxoYzNzUUVxSkljbmRWd2FrdmFYCjVUbktXNXB0djg3Nzl3R2s5TDNXZFNzRjlaM0FZOTFzRmVuMDZxUnZRdHlSYUsxck9Jb1pLeWJwdVJrOStQdmMKZXZQUGxrYkNrRytICi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
  tls.key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  ca.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURFVENDQWZtZ0F3SUJBZ0lRT0xhQ3U0eWFqU1dPcXZadEowL1hYVEFOQmdrcWhraUc5dzBCQVFzRkFEQVQKTVJFd0R3WURWUVFERXdodGFXNXBieTFqWVRBZUZ3MHlOREF6TVRBeE56TTBOREphRncweU5UQXpNVEF4TnpNMApOREphTUJNeEVUQVBCZ05WQkFNVENHMXBibWx2TFdOaE1JSUJJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBUThBCk1JSUJDZ0tDQVFFQTBjSWFtSHlQUTdDcVFCbnhXKzkrWlF0U0hlVFRpMXR1N2JleGxHTzBFd29rSDJvdXBLQzgKaldyekYzMGdvS1Ryb1ZTRlpEUThKR3prYXpkRzhuOWRsL1llOEUxaml5NFE5L21JcVI4cWY3ajg0WXVmOVkrUQp5ZjcyWkE0a2FLN0RvYS9KcUthaHlmQWljUlh5QytJWWRkSGpVa3J4NGd3dlIzZnNqMTRBZzR1QWFMbkM2dmVaCm5sb3VOaktidmY4ai94VHplMy85VC9RM2NleWJTZWNDVTVLR1ZQWStsamsxKy9BbmFvbFVjRnYxRFhScmUxbEsKTi9WWHpzV2NHZjgzZi9HeDBLbHpaZkROSHBXWHFaRkVic3hDOTFPWnZwVUpwbGNBSXpFbnQyeHBTTCtJWkdYYgpRVzMraXdScmliVXFpZUIzdnJqekZuMHlOS0YvV0RZV0hRSURBUUFCbzJFd1h6QU9CZ05WSFE4QkFmOEVCQU1DCkFxUXdIUVlEVlIwbEJCWXdGQVlJS3dZQkJRVUhBd0VHQ0NzR0FRVUZCd01DTUE4R0ExVWRFd0VCL3dRRk1BTUIKQWY4d0hRWURWUjBPQkJZRUZNUVpWU0ErWEtYaFNoSWQwdzBFcEFPSWlqVkhNQTBHQ1NxR1NJYjNEUUVCQ3dVQQpBNElCQVFBVzVQbm80WGhSZHVUQlpkWnMxZ1g4aWhDQ0FlbUNtTW9ScXpJNDd6cHFwTTJvcVB5WG81YnQ0WkZaCnhXOTN2N0thZmc4WjR6eVlacW9IMFFybVQ5MlhqaVR4QWlsT3JySExXdndRUlhkTjlXOTVsaFJXM2VrU0xWUkcKbHhENzdSL09UMFBvN005OS9CTzF2Nkp4U2VuNTV5K1h1R2dSR08yOUd2dVlMK1F3YVF3YmNTaVNVenZPTm96NQpYRnpUakszNk9BTzZyTG5vRDVkN0FzQVowQitqdG9PdDdCbjM1ZVlBNnNETHF2ZkE2TTVhYysrc0hoMDIwRTRkCmlpS2V2RUFadmhacW1TUzBKOE9mQVV1eVYwb2RjUWdrTDAxbmxMU2U5K0o0WFdHbGJKMkx5YzYyKzFjUHdsR0wKbmlOSlRXUkNuMzB6R2dmYnNhRTZSdXRmaE00dgotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==
---
# Source: minio-bitnami/charts/minio/templates/provisioning-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: release-name-minio-provisioning
  namespace: "default"
  labels:
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: minio
    app.kubernetes.io/version: 2024.2.26
    helm.sh/chart: minio-13.6.4
    app.kubernetes.io/component: minio-provisioning
data:
---
# Source: minio-bitnami/templates/sc.yaml
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: enc-infra-minio-local-storage
  labels:
    helm.sh/chart: minio-bitnami-1.0.0
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/managed-by: Helm
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer
---
# Source: minio-bitnami/templates/pv.yaml
apiVersion: v1
kind: PersistentVolume
metadata:
  name: enc-infra-minio-data-node1-d1
spec:
  capacity:
    storage: 300Gi
  volumeMode: Filesystem
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Delete
  storageClassName: enc-infra-minio-local-storage
  local:
    path: /mnt/adtran/enc-infra-minio-data-d1
  nodeAffinity:
    required:
      nodeSelectorTerms:
        - matchExpressions:
          - key: enc-node
            operator: In
            values:
              - node1
---
# Source: minio-bitnami/templates/pv.yaml
apiVersion: v1
kind: PersistentVolume
metadata:
  name: enc-infra-minio-data-node1-d2
spec:
  capacity:
    storage: 300Gi
  volumeMode: Filesystem
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Delete
  storageClassName: enc-infra-minio-local-storage
  local:
    path: /mnt/adtran/enc-infra-minio-data-d2
  nodeAffinity:
    required:
      nodeSelectorTerms:
        - matchExpressions:
          - key: enc-node
            operator: In
            values:
              - node1
---
# Source: minio-bitnami/templates/pv.yaml
apiVersion: v1
kind: PersistentVolume
metadata:
  name: enc-infra-minio-data-node2-d1
spec:
  capacity:
    storage: 300Gi
  volumeMode: Filesystem
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Delete
  storageClassName: enc-infra-minio-local-storage
  local:
    path: /mnt/adtran/enc-infra-minio-data-d1
  nodeAffinity:
    required:
      nodeSelectorTerms:
        - matchExpressions:
          - key: enc-node
            operator: In
            values:
              - node2
---
# Source: minio-bitnami/templates/pv.yaml
apiVersion: v1
kind: PersistentVolume
metadata:
  name: enc-infra-minio-data-node2-d2
spec:
  capacity:
    storage: 300Gi
  volumeMode: Filesystem
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Delete
  storageClassName: enc-infra-minio-local-storage
  local:
    path: /mnt/adtran/enc-infra-minio-data-d2
  nodeAffinity:
    required:
      nodeSelectorTerms:
        - matchExpressions:
          - key: enc-node
            operator: In
            values:
              - node2
---
# Source: minio-bitnami/templates/pv.yaml
apiVersion: v1
kind: PersistentVolume
metadata:
  name: enc-infra-minio-data-node3-d1
spec:
  capacity:
    storage: 300Gi
  volumeMode: Filesystem
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Delete
  storageClassName: enc-infra-minio-local-storage
  local:
    path: /mnt/adtran/enc-infra-minio-data-d1
  nodeAffinity:
    required:
      nodeSelectorTerms:
        - matchExpressions:
          - key: enc-node
            operator: In
            values:
              - node3
---
# Source: minio-bitnami/templates/pv.yaml
apiVersion: v1
kind: PersistentVolume
metadata:
  name: enc-infra-minio-data-node3-d2
spec:
  capacity:
    storage: 300Gi
  volumeMode: Filesystem
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Delete
  storageClassName: enc-infra-minio-local-storage
  local:
    path: /mnt/adtran/enc-infra-minio-data-d2
  nodeAffinity:
    required:
      nodeSelectorTerms:
        - matchExpressions:
          - key: enc-node
            operator: In
            values:
              - node3
---
# Source: minio-bitnami/charts/minio/templates/distributed/headless-svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: release-name-minio-headless
  namespace: "default"
  labels:
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: minio
    app.kubernetes.io/version: 2024.2.26
    helm.sh/chart: minio-13.6.4
spec:
  type: ClusterIP
  clusterIP: None
  ports:
    - name: minio-api
      port: 9000
      targetPort: minio-api
    - name: minio-console
      port: 9001
      targetPort: minio-console
  publishNotReadyAddresses: true
  selector:
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/name: minio
---
# Source: minio-bitnami/charts/minio/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: release-name-minio
  namespace: "default"
  labels:
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: minio
    app.kubernetes.io/version: 2024.2.26
    helm.sh/chart: minio-13.6.4
spec:
  type: NodePort
  externalTrafficPolicy: "Cluster"
  ports:
    - name: minio-api
      port: 9000
      targetPort: minio-api
    - name: minio-console
      port: 9001
      targetPort: minio-console
      nodePort: 30811
  selector:
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/name: minio
---
# Source: minio-bitnami/charts/minio/templates/distributed/statefulset.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: release-name-minio
  namespace: "default"
  labels:
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: minio
    app.kubernetes.io/version: 2024.2.26
    helm.sh/chart: minio-13.6.4
spec:
  selector:
    matchLabels:
      app.kubernetes.io/instance: release-name
      app.kubernetes.io/name: minio
  serviceName: release-name-minio-headless
  replicas: 3
  podManagementPolicy: Parallel
  updateStrategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/instance: release-name
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: minio
        app.kubernetes.io/version: 2024.2.26
        helm.sh/chart: minio-13.6.4
      annotations:
        checksum/credentials-secret: 97661d93bdb7f3d9b23bdb9b56505eabf04f05bd281a4b18dd5ba8963d4d395a
    spec:
      
      serviceAccountName: release-name-minio
      affinity:
        podAffinity:
          
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchLabels:
                    app.kubernetes.io/instance: release-name
                    app.kubernetes.io/name: minio
                topologyKey: kubernetes.io/hostname
              weight: 1
        nodeAffinity:
          
      automountServiceAccountToken: false
      securityContext:
        fsGroup: 1001
        fsGroupChangePolicy: OnRootMismatch
        supplementalGroups: []
        sysctls: []
      containers:
        - name: minio
          image: docker.io/bitnami/minio:2024.2.26-debian-12-r0
          imagePullPolicy: "IfNotPresent"
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
            runAsUser: 1001
            seccompProfile:
              type: RuntimeDefault
          env:
            - name: BITNAMI_DEBUG
              value: "false"
            - name: MINIO_DISTRIBUTED_MODE_ENABLED
              value: "yes"
            - name: MINIO_DISTRIBUTED_NODES
              value: "release-name-minio-{0...2}.release-name-minio-headless.default.svc.cluster.local:9000/bitnami/minio/data-{0...1}"
            - name: MINIO_SCHEME
              value: "https"
            - name: MINIO_FORCE_NEW_KEYS
              value: "no"
            - name: MINIO_ROOT_USER
              valueFrom:
                secretKeyRef:
                  name: release-name-minio
                  key: root-user
            - name: MINIO_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: release-name-minio
                  key: root-password
            - name: MINIO_SKIP_CLIENT
              value: "yes"
            - name: MINIO_BROWSER
              value: "on"
            - name: MINIO_PROMETHEUS_AUTH_TYPE
              value: "public"
            - name: MINIO_DATA_DIR
              value: "/bitnami/minio/data"
          envFrom:
          ports:
            - name: minio-api
              containerPort: 9000
              protocol: TCP
            - name: minio-console
              containerPort: 9001
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /minio/health/live
              port: minio-api
              scheme: "HTTPS"
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 5
          readinessProbe:
            tcpSocket:
              port: minio-api
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 1
            successThreshold: 1
            failureThreshold: 5
          volumeMounts:
            - name: minio-certs
              mountPath: /certs
            - name: data-0
              mountPath: /bitnami/minio/data-0
            - name: data-1
              mountPath: /bitnami/minio/data-1
      volumes:
        - name: minio-certs
          secret:
            secretName: release-name-minio-crt
            items:
            - key: tls.crt
              path: public.crt
            - key: tls.key
              path: private.key
            - key: ca.crt
              path: CAs/public.crt
  volumeClaimTemplates:
    - metadata:
        name: data-0
        labels:
          app.kubernetes.io/instance: release-name
          app.kubernetes.io/name: minio
      spec:
        accessModes:
          - "ReadWriteOnce"
        resources:
          requests:
            storage: "300Gi"
        storageClassName: enc-infra-minio-local-storage
    - metadata:
        name: data-1
        labels:
          app.kubernetes.io/instance: release-name
          app.kubernetes.io/name: minio
      spec:
        accessModes:
          - "ReadWriteOnce"
        resources:
          requests:
            storage: "300Gi"
        storageClassName: enc-infra-minio-local-storage
---
# Source: minio-bitnami/charts/minio/templates/provisioning-job.yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: release-name-minio-provisioning
  namespace: "default"
  labels:
    app.kubernetes.io/instance: release-name
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: minio
    app.kubernetes.io/version: 2024.2.26
    helm.sh/chart: minio-13.6.4
    app.kubernetes.io/component: minio-provisioning
  annotations:
    helm.sh/hook: post-install,post-upgrade
    helm.sh/hook-delete-policy: before-hook-creation
spec: 
  parallelism: 1
  template:
    metadata:
      labels:
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/version: 2024.2.26
        helm.sh/chart: minio-13.6.4
        app.kubernetes.io/component: minio-provisioning
    spec:
      
      restartPolicy: OnFailure
      terminationGracePeriodSeconds: 0
      securityContext:
        fsGroup: 1001
        fsGroupChangePolicy: Always
        supplementalGroups: []
        sysctls: []
      serviceAccountName: release-name-minio
      initContainers:
        - name: wait-for-available-minio
          image: docker.io/bitnami/minio:2024.2.26-debian-12-r0
          imagePullPolicy: "IfNotPresent"
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
            runAsUser: 1001
            seccompProfile:
              type: RuntimeDefault
          command:
            - /bin/bash
            - -c
            - |-
              set -e;
              echo "Waiting for Minio";
              wait-for-port \
                --host=release-name-minio \
                --state=inuse \
                --timeout=120 \
                9000;
              echo "Minio is available";
      containers:
        - name: minio
          image: docker.io/bitnami/minio:2024.2.26-debian-12-r0
          imagePullPolicy: "IfNotPresent"
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            privileged: false
            readOnlyRootFilesystem: false
            runAsNonRoot: true
            runAsUser: 1001
            seccompProfile:
              type: RuntimeDefault
          command:
            - /bin/bash
            - -c
            - >-
              set -e;
              echo "Start Minio provisioning";

              function attachPolicy() {
                local tmp=$(mc admin $1 info provisioning $2 | sed -n -e 's/^Policy.*: \(.*\)$/\1/p');
                IFS=',' read -r -a CURRENT_POLICIES <<< "$tmp";
                if [[ ! "${CURRENT_POLICIES[*]}" =~ "$3" ]]; then
                  mc admin policy attach provisioning $3 --$1=$2;
                fi;
              };

              function detachDanglingPolicies() {
                local tmp=$(mc admin $1 info provisioning $2 | sed -n -e 's/^Policy.*: \(.*\)$/\1/p');
                IFS=',' read -r -a CURRENT_POLICIES <<< "$tmp";
                IFS=',' read -r -a DESIRED_POLICIES <<< "$3";
                for current in "${CURRENT_POLICIES[@]}"; do
                  if [[ ! "${DESIRED_POLICIES[*]}" =~ "${current}" ]]; then
                    mc admin policy detach provisioning $current --$1=$2;
                  fi;
                done;
              }

              function addUsersFromFile() {
                local username=$(grep -oP '^username=\K.+' $1);
                local password=$(grep -oP '^password=\K.+' $1);
                local disabled=$(grep -oP '^disabled=\K.+' $1);
                local policies_list=$(grep -oP '^policies=\K.+' $1);
                local set_policies=$(grep -oP '^setPolicies=\K.+' $1);

                mc admin user add provisioning "${username}" "${password}";

                IFS=',' read -r -a POLICIES <<< "${policies_list}";
                for policy in "${POLICIES[@]}"; do
                  attachPolicy user "${username}" "${policy}";
                done;
                if [ "${set_policies}" == "true" ]; then
                  detachDanglingPolicies user "${username}" "${policies_list}";
                fi;

                local user_status="enable";
                if [[ "${disabled}" != "" && "${disabled,,}" == "true" ]]; then
                  user_status="disable";
                fi;

                mc admin user "${user_status}" provisioning "${username}";
              };
              mc alias set provisioning $MINIO_SCHEME://release-name-minio:9000 $MINIO_ROOT_USER $MINIO_ROOT_PASSWORD;
              mc admin config set provisioning region name=region-1;

              mc admin service restart provisioning;
              mc admin user add provisioning enc-minio-user ChgMeNOW;
              attachPolicy user enc-minio-user readwrite;
              attachPolicy user enc-minio-user consoleAdmin;
              attachPolicy user enc-minio-user diagnostics;
              mc admin user enable provisioning enc-minio-user;
              mc mb provisioning/mnc-synca-gnss-ts-backup --ignore-existing --region=region-1 ;
              mc version suspend provisioning/mnc-synca-gnss-ts-backup;
              mc tag set provisioning/mnc-synca-gnss-ts-backup "app=gnss";

              echo "End Minio provisioning";
          env:
            - name: MINIO_SCHEME
              value: "https"
            - name: MINIO_ROOT_USER
              valueFrom:
                secretKeyRef:
                  name: release-name-minio
                  key: root-user
            - name: MINIO_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: release-name-minio
                  key: root-password
          envFrom:
          volumeMounts:
            - name: minio-provisioning
              mountPath: /etc/ilm
            - name: minio-certs
              mountPath: /certs
            - name: minio-client-certs
              mountPath: /.mc/certs
      nodeSelector:
        enc-node: node1
      volumes:
        - name: minio-provisioning
          configMap:
            name: release-name-minio-provisioning
        - name: minio-certs
          secret:
            secretName: release-name-minio-crt
            items:
            - key: tls.crt
              path: public.crt
            - key: tls.key
              path: private.key
            - key: ca.crt
              path: CAs/public.crt
        - name: minio-client-certs
          secret:
            secretName: release-name-minio-crt
            items:
              - key: ca.crt
                path: CAs/public.crt
