apiVersion: v1
kind: ConfigMap
metadata:
  name: cm-envoy-config
data:
  envoy-config.yaml: |-
    admin:
      address:
        socket_address:
          address: 0.0.0.0
          port_value: 9901
    static_resources:
      listeners:
        - name: listener_0
          address:
            socket_address:
              address: 0.0.0.0
              port_value: 10000
          filter_chains:
            - filters:
                - name: envoy.filters.network.http_connection_manager
                  typed_config:
                    "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                    stat_prefix: mnc-int-proxy
                {{- if .Values.enableAccessLog }}
                    access_log:
                    - name: envoy.access_loggers.stdout
                      typed_config:
                        "@type": type.googleapis.com/envoy.extensions.access_loggers.stream.v3.StdoutAccessLog
                        log_format:
                          text_format_source:
                            inline_string: "[%START_TIME%] %REQ(:METHOD)% %REQ(enc-service?-)% %REQ(X-ENVOY-ORIGINAL-PATH?:PATH)% %PROTOCOL% %RESPONSE_CODE% %RESPONSE_FLAGS% %BYTES_RECEIVED% %BYTES_SENT% %DURATION% %RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)% %REQ(X-FORWARDED-FOR)% %REQ(USER-AGENT)% %REQ(X-REQUEST-ID)% %REQ(:AUTHORITY)% %UPSTREAM_HOST%\n"
                {{- end }}
                    http_filters:
                    {{- if .Values.enableTapFilter }}
                      ## tap filter dynamic config
                      - name: envoy.filters.http.tap
                        typed_config:
                          "@type": type.googleapis.com/envoy.extensions.filters.http.tap.v3.Tap
                          common_config:
                            admin_config:
                              config_id: enc_tap_id
                    {{- end }}
                      - name: envoy.filters.http.router
                        typed_config:
                          "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                    route_config:
                      virtual_hosts:
                        - name: all_domains
                          domains: ["*"]
                          routes:
                          ## path based routing
                          {{- range .Values.pathBaseRoutes }}
                            - match:
                                prefix: "{{ .pathPrefix }}"
                              route:
                                cluster: {{ .name }}
                          {{- end }}
                          ## header based routing
                          {{- range .Values.headerBaseRoutes }}
                            - match:
                                prefix: "/"
                                headers:
                                  - name: "enc-service"
                                    exact_match: "{{ .name }}"
                              route:
                                cluster: {{ .name }}
                          {{- end }}
      clusters:
      {{- range .Values.pathBaseRoutes }}
        - name: {{ .name }}
          connect_timeout: 5s
          type: STRICT_DNS
          lb_policy: ROUND_ROBIN
          load_assignment:
            cluster_name: {{ .name }}_svc
            endpoints:
              - lb_endpoints:
                  - endpoint:
                      address:
                        socket_address:
                          address: {{ .serviceName }}
                          port_value: {{ .servicePort }}
      {{- end }}
      {{- range .Values.headerBaseRoutes }}
        - name: {{ .name }}
          connect_timeout: 5s
          type: STRICT_DNS
          lb_policy: ROUND_ROBIN
          load_assignment:
            cluster_name: {{ .name }}_svc
            endpoints:
              - lb_endpoints:
                  - endpoint:
                      address:
                        socket_address:
                          address: {{ .serviceName }}
                          port_value: {{ .servicePort }}
      {{- end }}
