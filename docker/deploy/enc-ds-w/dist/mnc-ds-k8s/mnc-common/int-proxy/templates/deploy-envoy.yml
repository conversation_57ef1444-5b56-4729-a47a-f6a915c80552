apiVersion: apps/v1
kind: Deployment
metadata:
  name: mnc-int-proxy
  labels:
    helm.sh/chart: "{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" -}}"
    app.kubernetes.io/instance: "{{ .Release.Name }}"
    app.kubernetes.io/managed-by: "{{ .Release.Service }}"
    enc-stack: mnc-common
    app: mnc-int-proxy
    version: v1.29.3
spec:
  replicas: {{ .Values.replicas | default 1 }}
  selector:
    matchLabels:
      app: mnc-int-proxy
      version: v1.29.3
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: mnc-int-proxy
        version: v1.29.3
    spec:
      affinity:
        {{- if .Values.podAntiAffinity }}
        podAntiAffinity: {{- toYaml .Values.podAntiAffinity | nindent 10 }}
        {{- else }}
        podAntiAffinity:
        # The following podAntiAffinity is set to avoid pods of the same Deployment to be scheduled on the same node
          requiredDuringSchedulingIgnoredDuringExecution:
            # All the following topology rules are combined with a logical AND
            # This rule ensures that pods of the same Deployment are not scheduled on the same node
            - topologyKey: kubernetes.io/hostname
              labelSelector:
                matchLabels:
                  app: mnc-int-proxy
                  version: v1.29.3
        {{- end }}
      containers:
      - image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
        name: envoy
        args:
          - "--config-path" 
          - "/cfg/envoy-config.yaml"
        ports:
          - name: http-web
            containerPort: 10000
          - name: http-mng
            containerPort: 9901
        resources:
          limits:
            memory: 1Gi
          requests:
            cpu: 100m
            memory: 128Mi
        livenessProbe:
          httpGet:
            path: /ready
            port: 9901
          # initialDelaySeconds: 10
          periodSeconds: 20
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /ready
            port: 9901
          # initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
        volumeMounts:
          - name: envoy-config
            mountPath: /cfg
      volumes:
        - name: envoy-config
          configMap:
            name: cm-envoy-config

