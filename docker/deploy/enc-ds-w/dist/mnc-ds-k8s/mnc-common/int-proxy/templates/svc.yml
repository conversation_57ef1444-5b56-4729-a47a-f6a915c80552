apiVersion: v1
kind: Service
metadata:
  name: mnc-int-proxy
  labels:
    helm.sh/chart: "{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" -}}"
    app.kubernetes.io/instance: "{{ .Release.Name }}"
    app.kubernetes.io/managed-by: "{{ .Release.Service }}"
spec:
  type: {{ $.Values.service.type | default "ClusterIP" }}
  selector:
    app: mnc-int-proxy
  ports:
  - name: http-web
    port: 10000
    protocol: TCP
    {{- if eq $.Values.service.type "NodePort" }}
    nodePort: {{ .Values.service.nodePort }}
    {{- end }}
  # - name: http-mng
  #   port: 9901
  #   protocol: TCP
  #   nodePort: 30101
