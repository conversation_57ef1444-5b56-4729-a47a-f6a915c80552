---
# Source: mnc-int-proxy/templates/cm-envoy-config.yml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cm-envoy-config
data:
  envoy-config.yaml: |-
    admin:
      address:
        socket_address:
          address: 0.0.0.0
          port_value: 9901
    static_resources:
      listeners:
        - name: listener_0
          address:
            socket_address:
              address: 0.0.0.0
              port_value: 10000
          filter_chains:
            - filters:
                - name: envoy.filters.network.http_connection_manager
                  typed_config:
                    "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                    stat_prefix: mnc-int-proxy
                    http_filters:
                      - name: envoy.filters.http.router
                        typed_config:
                          "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                    route_config:
                      virtual_hosts:
                        - name: all_domains
                          domains: ["*"]
                          routes:
                          ## path based routing
                          ## header based routing
                            - match:
                                prefix: "/"
                                headers:
                                  - name: "enc-service"
                                    exact_match: "mnc-synca-gnss-collector"
                              route:
                                cluster: mnc-synca-gnss-collector
                            - match:
                                prefix: "/"
                                headers:
                                  - name: "enc-service"
                                    exact_match: "mnc-synca-gnss-data-access"
                              route:
                                cluster: mnc-synca-gnss-data-access
                            - match:
                                prefix: "/"
                                headers:
                                  - name: "enc-service"
                                    exact_match: "mnc-synca-gnss-rca-analysis"
                              route:
                                cluster: mnc-synca-gnss-rca-analysis
      clusters:
        - name: mnc-synca-gnss-collector
          connect_timeout: 5s
          type: STRICT_DNS
          lb_policy: ROUND_ROBIN
          load_assignment:
            cluster_name: mnc-synca-gnss-collector_svc
            endpoints:
              - lb_endpoints:
                  - endpoint:
                      address:
                        socket_address:
                          address: synca-gnss-collector.mnc-synca-gnss.svc.cluster.local
                          port_value: 8090
        - name: mnc-synca-gnss-data-access
          connect_timeout: 5s
          type: STRICT_DNS
          lb_policy: ROUND_ROBIN
          load_assignment:
            cluster_name: mnc-synca-gnss-data-access_svc
            endpoints:
              - lb_endpoints:
                  - endpoint:
                      address:
                        socket_address:
                          address: synca-gnss-data-access.mnc-synca-gnss.svc.cluster.local
                          port_value: 8092
        - name: mnc-synca-gnss-rca-analysis
          connect_timeout: 5s
          type: STRICT_DNS
          lb_policy: ROUND_ROBIN
          load_assignment:
            cluster_name: mnc-synca-gnss-rca-analysis_svc
            endpoints:
              - lb_endpoints:
                  - endpoint:
                      address:
                        socket_address:
                          address: synca-gnss-rca-analysis.mnc-synca-gnss.svc.cluster.local
                          port_value: 8110
---
# Source: mnc-int-proxy/templates/svc.yml
apiVersion: v1
kind: Service
metadata:
  name: mnc-int-proxy
  labels:
    helm.sh/chart: "mnc-int-proxy-1.0.0"
    app.kubernetes.io/instance: "release-name"
    app.kubernetes.io/managed-by: "Helm"
spec:
  type: ClusterIP
  selector:
    app: mnc-int-proxy
  ports:
  - name: http-web
    port: 10000
    protocol: TCP
  # - name: http-mng
  #   port: 9901
  #   protocol: TCP
  #   nodePort: 30101
---
# Source: mnc-int-proxy/templates/deploy-envoy.yml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mnc-int-proxy
  labels:
    helm.sh/chart: "mnc-int-proxy-1.0.0"
    app.kubernetes.io/instance: "release-name"
    app.kubernetes.io/managed-by: "Helm"
    enc-stack: mnc-common
    app: mnc-int-proxy
    version: v1.29.3
spec:
  replicas: 1
  selector:
    matchLabels:
      app: mnc-int-proxy
      version: v1.29.3
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: mnc-int-proxy
        version: v1.29.3
    spec:
      affinity:
        podAntiAffinity:
        # The following podAntiAffinity is set to avoid pods of the same Deployment to be scheduled on the same node
          requiredDuringSchedulingIgnoredDuringExecution:
            # All the following topology rules are combined with a logical AND
            # This rule ensures that pods of the same Deployment are not scheduled on the same node
            - topologyKey: kubernetes.io/hostname
              labelSelector:
                matchLabels:
                  app: mnc-int-proxy
                  version: v1.29.3
      containers:
      - image: "registry-server:5000/enc/envoyproxy/envoy:v1.29.3"
        name: envoy
        args:
          - "--config-path" 
          - "/cfg/envoy-config.yaml"
        ports:
          - name: http-web
            containerPort: 10000
          - name: http-mng
            containerPort: 9901
        resources:
          limits:
            memory: 1Gi
          requests:
            cpu: 100m
            memory: 128Mi
        livenessProbe:
          httpGet:
            path: /ready
            port: 9901
          # initialDelaySeconds: 10
          periodSeconds: 20
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /ready
            port: 9901
          # initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 3
        volumeMounts:
          - name: envoy-config
            mountPath: /cfg
      volumes:
        - name: envoy-config
          configMap:
            name: cm-envoy-config
