#!/bin/bash

NODE_IP="<set_your_node_ip>"
curl -v -H "enc-service: enc-synca-gnss-collector" "http://${NODE_IP}:30100/"
curl -v -H "enc-service: enc-synca-gnss-data-access" "http://${NODE_IP}:30100/"
curl -v -H "enc-service: enc-synca-gnss-rca-analysis" "http://${NODE_IP}:30100/"

curl -X POST -d @tap-config-any.json http://localhost:9901/tap
curl -X POST --data-binary @tap-config-match-headers.yaml http://localhost:9901/tap
