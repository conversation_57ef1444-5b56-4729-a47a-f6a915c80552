## Ref:
## https://www.envoyproxy.io/docs/envoy/latest/start/quick-start/admin#admin
admin:
  address:
    socket_address:
      address: 0.0.0.0
      port_value: 9901
##
## https://www.envoyproxy.io/docs/envoy/latest/start/quick-start/configuration-static#static-resources
static_resources:
  listeners:
    - name: listener_0
      address:
        socket_address:
          address: 0.0.0.0
          port_value: 10000
      filter_chains:
        - filters:
            - name: envoy.filters.network.http_connection_manager
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
                stat_prefix: mnc-int-proxy
                access_log:
                # https://www.envoyproxy.io/docs/envoy/latest/api-v3/extensions/access_loggers/stream/v3/stream.proto#extensions-access-loggers-stream-v3-stdoutaccesslog
                - name: envoy.access_loggers.stdout
                  typed_config:
                    "@type": type.googleapis.com/envoy.extensions.access_loggers.stream.v3.StdoutAccessLog
                    # https://www.envoyproxy.io/docs/envoy/latest/api-v3/config/core/v3/substitution_format_string.proto#envoy-v3-api-msg-config-core-v3-substitutionformatstring
                    log_format:
                      # https://www.envoyproxy.io/docs/envoy/latest/api-v3/config/core/v3/substitution_format_string.proto
                      text_format_source:
                        inline_string: "[%START_TIME%] %REQ(:METHOD)% %REQ(enc-service?-)% %REQ(X-ENVOY-ORIGINAL-PATH?:PATH)% %PROTOCOL% %RESPONSE_CODE% %RESPONSE_FLAGS% %BYTES_RECEIVED% %BYTES_SENT% %DURATION% %RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)% %REQ(X-FORWARDED-FOR)% %REQ(USER-AGENT)% %REQ(X-REQUEST-ID)% %REQ(:AUTHORITY)% %UPSTREAM_HOST%\n"
                http_filters:
                  - name: envoy.filters.http.router
                    typed_config:
                      "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
                route_config:
                  ## ref:
                  ## https://www.envoyproxy.io/docs/envoy/latest/api-v3/config/route/v3/route_components.proto#config-route-v3-virtualhost
                  virtual_hosts:
                    - name: all_domains
                      domains: ["*"]
                      routes:
                      ## path based routing
                        - match:
                            prefix: "/red"
                          route:
                            cluster: red
                        - match:
                            prefix: "/yellow"
                          route:
                            cluster: yellow
                      ## header based routing
                        - match:
                            prefix: "/"
                            headers:
                              - name: "enc-service"
                                exact_match: "blue"
                          route:
                            cluster: blue
                        - match:
                            prefix: "/"
                            headers:
                              - name: "enc-service"
                                exact_match: "green"
                          route:
                            cluster: green
  clusters:
    - name: blue
      connect_timeout: 5s
      type: STRICT_DNS
      lb_policy: ROUND_ROBIN
      load_assignment:
        cluster_name: blue_svc
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: color-app-blue.capp.svc.cluster.local
                      port_value: 3000
    - name: green
      connect_timeout: 5s
      type: STRICT_DNS
      lb_policy: ROUND_ROBIN
      load_assignment:
        cluster_name: green_svc
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: color-app-green.capp.svc.cluster.local
                      port_value: 3000
    - name: red
      connect_timeout: 5s
      type: STRICT_DNS
      lb_policy: ROUND_ROBIN
      load_assignment:
        cluster_name: red_svc
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: color-app-red.capp.svc.cluster.local
                      port_value: 3000
    - name: yellow
      connect_timeout: 5s
      type: STRICT_DNS
      lb_policy: ROUND_ROBIN
      load_assignment:
        cluster_name: yellow_svc
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: color-app-yellow.capp.svc.cluster.local
                      port_value: 3000