image:
  # repository: "envoyproxy/envoy"
  repository: "registry-server:5000/enc/envoyproxy/envoy"
  tag: "v1.29.3"
service:
  type: "" # if not set => ClusterIP 
#   nodePort: 30100 # if service.type is NodePort then this value is required
replicas: 1
## In case we want to allow multiple replicas on single node cluster, specific podAntiAffinity has to be defined, where labelSelector is missing
# podAntiAffinity:
#   requiredDuringSchedulingIgnoredDuringExecution:
#     - topologyKey: kubernetes.io/hostname

enableAccessLog: false
enableTapFilter: false

headerBaseRoutes:
# the name must match the value of the header "enc-service" in the request
  - name: "enc-synca-gnss-collector"
    serviceName: "synca-gnss-collector.mnc-synca-gnss.svc.cluster.local"
    servicePort: 8090
  - name: "enc-synca-gnss-data-access"
    serviceName: "synca-gnss-data-access.mnc-synca-gnss.svc.cluster.local"
    servicePort: 8092
  - name: "enc-synca-gnss-rca-analysis"
    serviceName: "synca-gnss-rca-analysis.mnc-synca-gnss.svc.cluster.local"
    servicePort: 8110
  - name: "enc-synca-tpa-collector"
    serviceName: "synca-tpa-collector.mnc-synca-tpa.svc.cluster.local"
    servicePort: 8098
  - name: "enc-synca-tpa-data-access"
    serviceName: "synca-tpa-data-access.mnc-synca-tpa.svc.cluster.local"
    servicePort: 8096
  - name: "enc-synca-tpa-online-qm"
    serviceName: "synca-tpa-online-qm.mnc-synca-tpa.svc.cluster.local"
    servicePort: 8102

pathBaseRoutes:
#  - name: "example"
#    pathPrefix: "/example"
#    serviceName: "example.ns.svc.cluster.local"
#    servicePort: 3000

