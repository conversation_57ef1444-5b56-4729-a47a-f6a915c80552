{{- if .Values.collector.enabled }}
{{- $_ := set .Values "encApp" .Values.collector -}}
---
{{ include "enc.common.deploymentBundle" . }}
---
# NOTE: Manual POD restart is required after update 
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-env-cm" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm  
data:
  DB_HOST: "{{ .Values.dbHost }}"
  DB_PORT: "5432"
  DB_NAME: fnm_sync_pm
  DB_USER: fnm_sync_pm
  JAVA_OPTS: "-Xms256m -Xmx1500m -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError"
  GNSSC_SCHEDULER_CRON_EXP_SEC: '0'
  GNSSC_POOL_SIZE: '50'
  GNSSC_KAFKA_TOPIC: "gnss-topic-{{ .Values.encVersion }}"
  GNSSC_KAFKA_TOPIC_REPLICAS: '3'
  GNSSC_KAFKA_TOPIC_PARTITIONS: '9'
  GNSSC_KAFKA_BOOTSTRAP_SERVERS: "{{ .Values.kafkaBootStrapServers }}"
  GNSSC_SNMP_GET_BULK_DEF_MAX_REP: '10'
  GNSSC_EXTERNAL_SERVICE_HTTP_CONNECT_TIMEOUT_IN_MILLIS: '10000'
  GNSSC_EXTERNAL_SERVICE_HTTP_READ_TIMEOUT_IN_MILLIS: '10000'
  GNSS_LOG_LEVEL: INFO
  GNSS_LIVE_LOG_LEVEL: INFO
  GNSS_LIVE_POOL_SIZE: '10'
  GNSS_LIVE_SCHEDULER_RATE_SEC: '10'
  GNSS_LIVE_REGISTRATION_TTL_SEC: '10'
  GNSS_LIVE_KAFKA_TOPIC: "gnss-live-topic-{{ .Values.encVersion }}"
  GNSS_LIVE_KAFKA_TOPIC_REPLICAS: '3'
  GNSS_LIVE_KAFKA_TOPIC_PARTITIONS: '9'
  GNSS_LIVE_SNMP_TIMEOUT_IN_MSEC: '9500' # snmp setting used for the live collector , this is global for live data and overwrites the one stored in DB
  GNSS_LIVE_SNMP_RETRY_COUNT: '0' # snmp setting used for the live collector , this is global for live data and overwrites the one stored in DB
  GNSS_NE_CLI_SCRIPT_TOPIC: "gnss-ne-cli-script-topic-{{ .Values.encVersion }}"
  GNSS_NE_CLI_SCRIPT_TOPIC_REPLICAS: '3'
  GNSS_NE_CLI_SCRIPT_TOPIC_PARTITIONS: '9'
  #GNSS_CLI_WORKER_SERVICE: "custom-worker:8100"
  GNSS_CLI_WORKER_SERVICE_HTTP_CONNECT_TIMEOUT_IN_MILLIS: '10000'
  GNSS_CLI_WORKER_SERVICE_HTTP_READ_TIMEOUT_IN_MILLIS: '10000'

---
# NOTE: Manual POD restart is required after update 
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-env-secret" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm  
data:
  TEST_04: "VkFMXzA0" # echo -n "VAL_04" | base64
  DB_PASSWORD: "{{ .Values.dbPassword | b64enc }}"

{{- end }}