{{- if .Values.machineLearning.enabled }}
  {{- $_ := set .Values "encApp" .Values.machineLearning -}}
---
{{ include "enc.common.deploymentBundle" . }}
---
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-env-cm" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  JAVA_OPTS: "-Xms256m -Xmx1500m -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError"
  GNSSC_KAFKA_TOPIC: "gnss-topic-{{ .Values.encVersion }}"
  GNSSC_KAFKA_BOOTSTRAP_SERVERS: "{{ .Values.kafkaBootStrapServers }}"
  GNSS_LOG_LEVEL: INFO
  GNSS_ML_PREDICTOR_POOL_SIZE: '20'
  GNSS_ML_PREDICTOR_QUEUE_SIZE: '4000'
  GNSS_ML_DATA_ACCESS_HTTP_CONNECT_TIMEOUT_IN_MILLIS: '8000'
  GNSS_ML_DATA_ACCESS_HTTP_READ_TIMEOUT_IN_MILLIS: '8000'
  ENC_HOST: "{{ .Values.encHost }}"
  ENC_HOST_2: "{{ .Values.encHost2 }}"
  ENC_PORT: '8443'
  GNSS_ML_NOTIFY_PREDICTIONS: 'true'
  GNSS_ML_OVERRIDE_PREDICTION_AGG_PERIOD_SPOOF_JAM: ""
  GNSS_ML_OVERRIDE_PREDICTION_AGG_PERIOD_OBSTRUCTION: ""

---
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-env-secret" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  ENC_MTLS_TOKEN: "{{ .Values.encMtlsToken | b64enc }}"

  {{- end }}