{{- if .Values.rcaAnalysis.enabled }}
  {{- $_ := set .Values "encApp" .Values.rcaAnalysis -}}
---
{{ include "enc.common.deploymentBundle" . }}
---
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-env-cm" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  ENC_HOST: "{{ .Values.encHost }}"
  ENC_HOST_2: "{{ .Values.encHost2 }}"
  ENC_PORT: '8443'
  DB_HOST: "{{ .Values.dbHost }}"
  DB_PORT: "5432"
  DB_NAME: fnm_sync_pm
  DB_USER: fnm_sync_pm
  JAVA_OPTS: "-Xms256m -Xmx1500m -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError"
  RCA_LOG_LEVEL: INFO
  KAFKA_RCA_BOOTSTRAP_SERVERS: "{{ .Values.kafkaBootStrapServers }}"
  KAFKA_RCA_ANALYSIS_REQUEST_TOPIC: "rca-analysis-request-topic-{{ .Values.encVersion }}"
  KAFKA_RCA_ANALYSIS_REQUEST_TOPIC_REPLICAS: '3'
  KAFKA_RCA_ANALYSIS_REQUEST_TOPIC_PARTITIONS: '9'
  KAFKA_RCA_CORRELATION_UPDATE_TOPIC: "rca-correlation-update-topic-{{ .Values.encVersion }}"
  KAFKA_RCA_CORRELATION_UPDATE_TOPIC_REPLICAS: '3'
  KAFKA_RCA_CORRELATION_UPDATE_TOPIC_PARTITIONS: '9'
  RCA_ANALYSIS_CORRELATION_MAX_TIME_GAP_IN_SEC: '305'
  KAFKA_RCA_ANALYSIS_CONSUMER_CONCURRENCY: '3'

---
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-env-secret" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  TEST_04: "VkFMXzA0" # echo -n "VAL_04" | base64
  DB_PASSWORD: "{{ .Values.dbPassword | b64enc }}"
  ENC_MTLS_TOKEN: "{{ .Values.encMtlsToken | b64enc }}"

  {{- end }}