{{- if .Values.cliWorker.enabled }}
  {{- $_ := set .Values "encApp" .Values.cliWorker -}}
---
{{ include "enc.common.deploymentBundle" . }}
---
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-env-cm" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  JAVA_OPTS: "-Xms256m -Xmx500m -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005"
  NE_CLI_SCRIPT_DIR: "/home/<USER>/cli_script"  
  GNSS_LOG_LEVEL: INFO
  GNSSC_KAFKA_BOOTSTRAP_SERVERS: "{{ .Values.kafkaBootStrapServers }}"
  GNSS_NE_CLI_SCRIPT_TOPIC: "gnss-ne-cli-script-topic-{{ .Values.encVersion }}"
  GNSS_PROCCESS_TIMEOUT_IN_SEC: "10"

---
# in case of cli-worker this secret is currently a dummy one. It is however required for every GNSS service to have one as it is used by the deployment object
# NOTE: Manual POD restart is required after update 
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-env-secret" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm  
data:
  TEST_04: "VkFMXzA0" # echo -n "VAL_04" | base64

  {{- end }}