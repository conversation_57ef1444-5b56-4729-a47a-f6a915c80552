---
# Source: synca-gnss/templates/collector.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: synca-gnss-collector-sa
---
# Source: synca-gnss/templates/data-access.yml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: synca-gnss-data-access-sa
---
# Source: synca-gnss/templates/machine-learning.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: synca-gnss-machine-learning-sa
---
# Source: synca-gnss/templates/rca-analysis.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: synca-gnss-rca-analysis-sa
---
# Source: synca-gnss/templates/collector.yaml
# NOTE: Manual POD restart is required after update 
apiVersion: v1
kind: Secret
metadata:
  name: synca-gnss-collector-env-secret
  labels:
    app.kubernetes.io/managed-by: Helm  
data:
  TEST_04: "VkFMXzA0" # echo -n "VAL_04" | base64
  DB_PASSWORD: "Zm5tX3N5bmNfcG0="
---
# Source: synca-gnss/templates/data-access.yml
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: Secret
metadata:
  name: synca-gnss-data-access-env-secret
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  TEST_04: "VkFMXzA0" # echo -n "VAL_04" | base64
  DB_PASSWORD: "Zm5tX3N5bmNfcG0="
---
# Source: synca-gnss/templates/machine-learning.yaml
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: Secret
metadata:
  name: synca-gnss-machine-learning-env-secret
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  ENC_MTLS_TOKEN: ""
---
# Source: synca-gnss/templates/rca-analysis.yaml
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: Secret
metadata:
  name: synca-gnss-rca-analysis-env-secret
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  TEST_04: "VkFMXzA0" # echo -n "VAL_04" | base64
  DB_PASSWORD: "Zm5tX3N5bmNfcG0="
---
# Source: synca-gnss/templates/collector.yaml
# NOTE: Manual POD restart is required after update 
apiVersion: v1
kind: ConfigMap
metadata:
  name: synca-gnss-collector-env-cm
  labels:
    app.kubernetes.io/managed-by: Helm  
data:
  DB_HOST: "timescaledb"
  DB_PORT: "5432"
  DB_NAME: fnm_sync_pm
  DB_USER: fnm_sync_pm
  JAVA_OPTS: "-Xms256m -Xmx1500m -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError"
  GNSSC_SCHEDULER_CRON_EXP_SEC: '0'
  GNSSC_POOL_SIZE: '50'
  GNSSC_KAFKA_TOPIC: "gnss-topic-latest"
  GNSSC_KAFKA_TOPIC_REPLICAS: '3'
  GNSSC_KAFKA_TOPIC_PARTITIONS: '9'
  GNSSC_KAFKA_BOOTSTRAP_SERVERS: "kafka:9092"
  GNSSC_SNMP_GET_BULK_DEF_MAX_REP: '10'
  GNSSC_EXTERNAL_SERVICE_HTTP_CONNECT_TIMEOUT_IN_MILLIS: '10000'
  GNSSC_EXTERNAL_SERVICE_HTTP_READ_TIMEOUT_IN_MILLIS: '10000'
  GNSS_LOG_LEVEL: INFO
  GNSS_LIVE_LOG_LEVEL: INFO
  GNSS_LIVE_POOL_SIZE: '10'
  GNSS_LIVE_SCHEDULER_RATE_SEC: '10'
  GNSS_LIVE_REGISTRATION_TTL_SEC: '10'
  GNSS_LIVE_KAFKA_TOPIC: "gnss-live-topic-latest"
  GNSS_LIVE_KAFKA_TOPIC_REPLICAS: '3'
  GNSS_LIVE_KAFKA_TOPIC_PARTITIONS: '9'
  GNSS_LIVE_SNMP_TIMEOUT_IN_MSEC: '9500' # snmp setting used for the live collector , this is global for live data and overwrites the one stored in DB
  GNSS_LIVE_SNMP_RETRY_COUNT: '0' # snmp setting used for the live collector , this is global for live data and overwrites the one stored in DB
  GNSS_NE_CLI_SCRIPT_TOPIC: "gnss-ne-cli-script-topic-latest"
  GNSS_NE_CLI_SCRIPT_TOPIC_REPLICAS: '3'
  GNSS_NE_CLI_SCRIPT_TOPIC_PARTITIONS: '9'
  #GNSS_CLI_WORKER_SERVICE: "custom-worker:8100"
  GNSS_CLI_WORKER_SERVICE_HTTP_CONNECT_TIMEOUT_IN_MILLIS: '10000'
  GNSS_CLI_WORKER_SERVICE_HTTP_READ_TIMEOUT_IN_MILLIS: '10000'
---
# Source: synca-gnss/templates/data-access.yml
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: ConfigMap
metadata:
  name: synca-gnss-data-access-env-cm
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  DB_HOST: "timescaledb"
  DB_PORT: "5432"
  DB_NAME: fnm_sync_pm
  DB_USER: fnm_sync_pm
  JAVA_OPTS: "-Xms256m -Xmx1500m -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError"
  GNSSC_KAFKA_TOPIC: "gnss-topic-latest"
  GNSSC_KAFKA_BOOTSTRAP_SERVERS: "kafka:9092"
  GNSS_DATA_ACCESS_RETENTION_DAYS: '365' # truncate historical data older then 365 days. valid range(7...730)
  GNSS_DATA_ACCESS_AGGREGATION_RETENTION_DAYS: '365' # truncate historical aggregated data older then 365 days. valid range(60...730)
  GNSS_DATA_ACCESS_RETENTION_LIVE_MINUTES: '4' # truncate live data older then 4 minutes. value can't be less then 4 minutes as we have 2 min chunks (max 60)
  GNSS_LOG_LEVEL: INFO
  GNSS_LIVE_LOG_LEVEL: INFO  # set the log level for functionality related to live data
  GNSS_LIVE_KAFKA_TOPIC: "gnss-live-topic-latest"
  DB_BACKUP_ESTIMATED_TIME_IN_HOURS: '10'
  CLEAR_DB_BACKUP_CONTROL_SCHEDULER_CRON_EXP_HOUR: '0' # clear db should be triggered just before automatic db backup hour for the gnss db (configured in the db-backup section)
---
# Source: synca-gnss/templates/machine-learning.yaml
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: ConfigMap
metadata:
  name: synca-gnss-machine-learning-env-cm
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  JAVA_OPTS: "-Xms256m -Xmx1500m -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError"
  GNSSC_KAFKA_TOPIC: "gnss-topic-latest"
  GNSSC_KAFKA_BOOTSTRAP_SERVERS: "kafka:9092"
  GNSS_LOG_LEVEL: INFO
  GNSS_ML_PREDICTOR_POOL_SIZE: '20'
  GNSS_ML_PREDICTOR_QUEUE_SIZE: '4000'
  GNSS_ML_DATA_ACCESS_HTTP_CONNECT_TIMEOUT_IN_MILLIS: '8000'
  GNSS_ML_DATA_ACCESS_HTTP_READ_TIMEOUT_IN_MILLIS: '8000'
  ENC_HOST: "*******"
  ENC_HOST_2: "*******"
  ENC_PORT: '8443'
  GNSS_ML_NOTIFY_PREDICTIONS: 'true'
---
# Source: synca-gnss/templates/rca-analysis.yaml
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: ConfigMap
metadata:
  name: synca-gnss-rca-analysis-env-cm
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  ENC_HOST: "*******"
  ENC_HOST_2: "*******"
  ENC_PORT: '8443'
  DB_HOST: "timescaledb"
  DB_PORT: "5432"
  DB_NAME: fnm_sync_pm
  DB_USER: fnm_sync_pm
  JAVA_OPTS: "-Xms256m -Xmx1500m -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError"
  RCA_LOG_LEVEL: INFO
  KAFKA_RCA_BOOTSTRAP_SERVERS: "kafka:9092"
  KAFKA_RCA_ANALYSIS_REQUEST_TOPIC: "rca-analysis-request-topic-latest"
  KAFKA_RCA_ANALYSIS_REQUEST_TOPIC_REPLICAS: '3'
  KAFKA_RCA_ANALYSIS_REQUEST_TOPIC_PARTITIONS: '9'
  KAFKA_RCA_CORRELATION_UPDATE_TOPIC: "rca-correlation-update-topic-latest"
  KAFKA_RCA_CORRELATION_UPDATE_TOPIC_REPLICAS: '3'
  KAFKA_RCA_CORRELATION_UPDATE_TOPIC_PARTITIONS: '9'
  RCA_ANALYSIS_CORRELATION_MAX_TIME_GAP_IN_SEC: '305'
  KAFKA_RCA_ANALYSIS_CONSUMER_CONCURRENCY: '3'
---
# Source: synca-gnss/templates/collector.yaml
apiVersion: v1
kind: Service
metadata:
  name: synca-gnss-collector
  labels:
    helm.sh/chart: synca-gnss-1.0.0
    app.kubernetes.io/instance: synca-gnss
    app.kubernetes.io/managed-by: Helm
    app: synca-gnss-collector
    version: latest
spec:
  selector:
    app: synca-gnss-collector
    version: latest
  ports:
  - name: http-web
    port: 8090
    protocol: TCP
---
# Source: synca-gnss/templates/data-access.yml
apiVersion: v1
kind: Service
metadata:
  name: synca-gnss-data-access
  labels:
    helm.sh/chart: synca-gnss-1.0.0
    app.kubernetes.io/instance: synca-gnss
    app.kubernetes.io/managed-by: Helm
    app: synca-gnss-data-access
    version: latest
spec:
  selector:
    app: synca-gnss-data-access
    version: latest
  ports:
  - name: http-web
    port: 8092
    protocol: TCP
---
# Source: synca-gnss/templates/machine-learning.yaml
apiVersion: v1
kind: Service
metadata:
  name: synca-gnss-machine-learning
  labels:
    helm.sh/chart: synca-gnss-1.0.0
    app.kubernetes.io/instance: synca-gnss
    app.kubernetes.io/managed-by: Helm
    app: synca-gnss-machine-learning
    version: latest
spec:
  selector:
    app: synca-gnss-machine-learning
    version: latest
  ports:
  - name: http-web
    port: 8094
    protocol: TCP
---
# Source: synca-gnss/templates/rca-analysis.yaml
apiVersion: v1
kind: Service
metadata:
  name: synca-gnss-rca-analysis
  labels:
    helm.sh/chart: synca-gnss-1.0.0
    app.kubernetes.io/instance: synca-gnss
    app.kubernetes.io/managed-by: Helm
    app: synca-gnss-rca-analysis
    version: latest
spec:
  selector:
    app: synca-gnss-rca-analysis
    version: latest
  ports:
  - name: http-web
    port: 8110
    protocol: TCP
---
# Source: synca-gnss/templates/collector.yaml
apiVersion: apps/v1
kind: Deployment
metadata:  
  name: synca-gnss-collector
  labels:
    helm.sh/chart: synca-gnss-1.0.0
    app.kubernetes.io/instance: synca-gnss
    app.kubernetes.io/managed-by: Helm
    app: synca-gnss-collector
    version: latest
    enc-stack: gnss
spec:
  replicas: 1
  selector:
    matchLabels:
      app: synca-gnss-collector
      app.kubernetes.io/instance: synca-gnss
      enc-stack: gnss
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: synca-gnss-collector
        app.kubernetes.io/instance: synca-gnss
        enc-stack: gnss
        version: latest
    spec:
      affinity:
        podAntiAffinity:
        # The following podAntiAffinity is set to avoid pods of the same Deployment to be scheduled on the same node
          requiredDuringSchedulingIgnoredDuringExecution:
            # All the following topology rules are combined with a logical AND
            # This rule ensures that pods of the same Deployment are not scheduled on the same node
            - topologyKey: kubernetes.io/hostname
              labelSelector:
                matchLabels:
                  app: synca-gnss-collector
                  version: latest
      securityContext: 
        runAsUser: 1100
        runAsGroup: 1100
        fsGroup: 1100
      serviceAccountName: synca-gnss-collector-sa
      containers:
      - image: "registry-server:5000/enc/synca/gnss-collector:latest"
        imagePullPolicy: IfNotPresent
        name: collector
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "2000Mi"
            cpu: "4000m"
        env:
          - name: VAR_01
            value: "VAL_01"
          - name: ENV_VAR_TEST
            value: "ENV_VAL_TEST"
          - name: GNSSC_POOL_SIZE
            value: "50"
          - name: GNSSC_SCHEDULER_CRON_EXP_MIN
            value: "*/5"
          - name: GNSS_CLI_WORKER_SERVICE
            value: "synca-gnss-cli-worker:8100"
          - name: GNSS_LIVE_LOG_LEVEL
            value: "INFO"
          - name: GNSS_LOG_LEVEL
            value: "INFO"
        envFrom:
          - configMapRef:
              name: synca-gnss-collector-env-cm
          - secretRef:
              name: synca-gnss-collector-env-secret
        ports:
          - name: http-web
            containerPort: 8090
        livenessProbe:
          httpGet:
            path: /
            port: 8090
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /
            port: 8090
          initialDelaySeconds: 30
          periodSeconds: 15
          timeoutSeconds: 3
---
# Source: synca-gnss/templates/data-access.yml
apiVersion: apps/v1
kind: Deployment
metadata:  
  name: synca-gnss-data-access
  labels:
    helm.sh/chart: synca-gnss-1.0.0
    app.kubernetes.io/instance: synca-gnss
    app.kubernetes.io/managed-by: Helm
    app: synca-gnss-data-access
    version: latest
    enc-stack: gnss
spec:
  replicas: 1
  selector:
    matchLabels:
      app: synca-gnss-data-access
      app.kubernetes.io/instance: synca-gnss
      enc-stack: gnss
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: synca-gnss-data-access
        app.kubernetes.io/instance: synca-gnss
        enc-stack: gnss
        version: latest
    spec:
      affinity:
        podAntiAffinity:
        # The following podAntiAffinity is set to avoid pods of the same Deployment to be scheduled on the same node
          requiredDuringSchedulingIgnoredDuringExecution:
            # All the following topology rules are combined with a logical AND
            # This rule ensures that pods of the same Deployment are not scheduled on the same node
            - topologyKey: kubernetes.io/hostname
              labelSelector:
                matchLabels:
                  app: synca-gnss-data-access
                  version: latest
      securityContext: 
        runAsUser: 1100
        runAsGroup: 1100
        fsGroup: 1100
      serviceAccountName: synca-gnss-data-access-sa
      containers:
      - image: "registry-server:5000/enc/synca/gnss-data-access:latest"
        imagePullPolicy: IfNotPresent
        name: data-access
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "2000Mi"
            cpu: "4000m"
        env:
          - name: VAR_01
            value: "VAL_01"
          - name: ENV_VAR_TEST
            value: "ENV_VAL_TEST"
          - name: GNSS_DATA_ACCESS_AGGREGATION_RETENTION_DAYS
            value: "365"
          - name: GNSS_DATA_ACCESS_RETENTION_DAYS
            value: "365"
          - name: GNSS_DATA_ACCESS_RETENTION_LIVE_MINUTES
            value: "4"
          - name: GNSS_LIVE_LOG_LEVEL
            value: "INFO"
          - name: GNSS_LOG_LEVEL
            value: "INFO"
        envFrom:
          - configMapRef:
              name: synca-gnss-data-access-env-cm
          - secretRef:
              name: synca-gnss-data-access-env-secret
        ports:
          - name: http-web
            containerPort: 8092
        livenessProbe:
          httpGet:
            path: /
            port: 8092
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /
            port: 8092
          initialDelaySeconds: 30
          periodSeconds: 15
          timeoutSeconds: 3
---
# Source: synca-gnss/templates/machine-learning.yaml
apiVersion: apps/v1
kind: Deployment
metadata:  
  name: synca-gnss-machine-learning
  labels:
    helm.sh/chart: synca-gnss-1.0.0
    app.kubernetes.io/instance: synca-gnss
    app.kubernetes.io/managed-by: Helm
    app: synca-gnss-machine-learning
    version: latest
    enc-stack: gnss
spec:
  replicas: 1
  selector:
    matchLabels:
      app: synca-gnss-machine-learning
      app.kubernetes.io/instance: synca-gnss
      enc-stack: gnss
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: synca-gnss-machine-learning
        app.kubernetes.io/instance: synca-gnss
        enc-stack: gnss
        version: latest
    spec:
      affinity:
        podAntiAffinity:
        # The following podAntiAffinity is set to avoid pods of the same Deployment to be scheduled on the same node
          requiredDuringSchedulingIgnoredDuringExecution:
            # All the following topology rules are combined with a logical AND
            # This rule ensures that pods of the same Deployment are not scheduled on the same node
            - topologyKey: kubernetes.io/hostname
              labelSelector:
                matchLabels:
                  app: synca-gnss-machine-learning
                  version: latest
      securityContext: 
        runAsUser: 1100
        runAsGroup: 1100
        fsGroup: 1100
      serviceAccountName: synca-gnss-machine-learning-sa
      containers:
      - image: "registry-server:5000/enc/synca/gnss-machine-learning:latest"
        imagePullPolicy: IfNotPresent
        name: machine-learning
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "2000Mi"
            cpu: "4000m"
        env:
          - name: VAR_01
            value: "VAL_01"
          - name: ENV_VAR_TEST
            value: "ENV_VAL_TEST"
          - name: GNSS_LOG_LEVEL
            value: "INFO"
          - name: GNSS_ML_DATA_ACCESS_SERVICE
            value: "synca-gnss-data-access:8092"
          - name: GNSS_ML_PREDICTOR_POOL_SIZE
            value: "20"
          - name: GNSS_ML_PREDICTOR_QUEUE_SIZE
            value: "4000"
        envFrom:
          - configMapRef:
              name: synca-gnss-machine-learning-env-cm
          - secretRef:
              name: synca-gnss-machine-learning-env-secret
        ports:
          - name: http-web
            containerPort: 8094
        livenessProbe:
          httpGet:
            path: /
            port: 8094
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /
            port: 8094
          initialDelaySeconds: 30
          periodSeconds: 15
          timeoutSeconds: 3
---
# Source: synca-gnss/templates/rca-analysis.yaml
apiVersion: apps/v1
kind: Deployment
metadata:  
  name: synca-gnss-rca-analysis
  labels:
    helm.sh/chart: synca-gnss-1.0.0
    app.kubernetes.io/instance: synca-gnss
    app.kubernetes.io/managed-by: Helm
    app: synca-gnss-rca-analysis
    version: latest
    enc-stack: gnss
spec:
  replicas: 1
  selector:
    matchLabels:
      app: synca-gnss-rca-analysis
      app.kubernetes.io/instance: synca-gnss
      enc-stack: gnss
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: synca-gnss-rca-analysis
        app.kubernetes.io/instance: synca-gnss
        enc-stack: gnss
        version: latest
    spec:
      affinity:
        podAntiAffinity:
        # The following podAntiAffinity is set to avoid pods of the same Deployment to be scheduled on the same node
          requiredDuringSchedulingIgnoredDuringExecution:
            # All the following topology rules are combined with a logical AND
            # This rule ensures that pods of the same Deployment are not scheduled on the same node
            - topologyKey: kubernetes.io/hostname
              labelSelector:
                matchLabels:
                  app: synca-gnss-rca-analysis
                  version: latest
      securityContext: 
        runAsUser: 1100
        runAsGroup: 1100
        fsGroup: 1100
      serviceAccountName: synca-gnss-rca-analysis-sa
      containers:
      - image: "registry-server:5000/enc/synca/gnss-rca-analysis:latest"
        imagePullPolicy: IfNotPresent
        name: rca-analysis
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "2000Mi"
            cpu: "4000m"
        env:
          - name: VAR_01
            value: "VAL_01"
          - name: ENV_VAR_TEST
            value: "ENV_VAL_TEST"
          - name: RCA_ANALYSIS_CORRELATION_MAX_TIME_GAP_IN_SEC
            value: "305"
          - name: RCA_LOG_LEVEL
            value: "INFO"
        envFrom:
          - configMapRef:
              name: synca-gnss-rca-analysis-env-cm
          - secretRef:
              name: synca-gnss-rca-analysis-env-secret
        ports:
          - name: http-web
            containerPort: 8110
        livenessProbe:
          httpGet:
            path: /
            port: 8110
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /
            port: 8110
          initialDelaySeconds: 30
          periodSeconds: 15
          timeoutSeconds: 3
---
# Source: synca-gnss/charts/common/templates/exampleApp.yaml
## Use this file to develop and test this common enc chart
