{{- if .Values.dataAccess.enabled }}
  {{- $_ := set .Values "encApp" .Values.dataAccess -}}
---
{{ include "enc.common.deploymentBundle" . }}
---
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-env-cm" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  DB_HOST: "{{ .Values.dbHost }}"
  DB_PORT: "5432"
  DB_NAME: fnm_sync_pm
  DB_USER: fnm_sync_pm
  JAVA_OPTS: "-Xms256m -Xmx1500m -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError"
  GNSSC_KAFKA_TOPIC: "gnss-topic-{{ .Values.encVersion }}"
  GNSSC_KAFKA_BOOTSTRAP_SERVERS: "{{ .Values.kafkaBootStrapServers }}"
  GNSS_DATA_ACCESS_RETENTION_DAYS: '365' # truncate historical data older then 365 days. valid range(7...730)
  GNSS_DATA_ACCESS_AGGREGATION_RETENTION_DAYS: '365' # truncate historical aggregated data older then 365 days. valid range(60...730)
  GNSS_DATA_ACCESS_RETENTION_LIVE_MINUTES: '4' # truncate live data older then 4 minutes. value can't be less then 4 minutes as we have 2 min chunks (max 60)
  GNSS_LOG_LEVEL: INFO
  GNSS_LIVE_LOG_LEVEL: INFO  # set the log level for functionality related to live data
  GNSS_LIVE_KAFKA_TOPIC: "gnss-live-topic-{{ .Values.encVersion }}"
  GNSS_STRONG_JAMMING_MITIGATION_ENABLED: 'true'
  GNSS_CNO_CLOSE_TO_ZERO_LIMITER: '0'
  GNSS_CNO_CLOSE_TO_ZERO_REPLACEMENT: '1'
  GNSS_AGC_CLOSE_TO_ZERO_LIMITER: '0'
  GNSS_AGC_CLOSE_TO_ZERO_REPLACEMENT: '1'

---
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-env-secret" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  TEST_04: "VkFMXzA0" # echo -n "VAL_04" | base64
  DB_PASSWORD: "{{ .Values.dbPassword | b64enc }}"

  {{- end }}