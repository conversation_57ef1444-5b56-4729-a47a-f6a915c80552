# The encVersion must be set to a specific version number e.g  16.1.1-B12345 during deployment
# Attention: Do not use "latest" for the encVersion 
encVersion: latest # Must be set during deployment
encHost: "*******" # Must be set during deployment
encHost2: "*******" # Must be set during deployment
encMtlsToken: ""  # Must be set during deployment
dbHost: "timescaledb"
dbPassword: "fnm_sync_pm"
kafkaBootStrapServers: "kafka:9092"

## Values for collector
collector:
  enabled: true
  replicas: 1
  name: collector
  stackLabel: gnss
  port: 8090
  image:
    repository: registry-server:5000/enc/synca/gnss-collector
  # service:
  #   type: NodePort
  #   nodePort: 31090
  env: 
    ENV_VAR_TEST: "ENV_VAL_TEST"
    GNSS_CLI_WORKER_SERVICE: "synca-gnss-cli-worker:8100"
    GNSSC_SCHEDULER_CRON_EXP_MIN: '*/5'
    GNSSC_POOL_SIZE: 50
    GNSS_LOG_LEVEL: INFO
    GNSS_LIVE_LOG_LEVEL: INFO

## Values for dataAccess
dataAccess:
  enabled: true
  replicas: 1
  name: data-access
  stackLabel: gnss
  port: 8092
  image:
    repository: registry-server:5000/enc/synca/gnss-data-access
  env:
    ENV_VAR_TEST: "ENV_VAL_TEST"
    GNSS_LOG_LEVEL: INFO
    GNSS_LIVE_LOG_LEVEL: INFO  # set the log level for functionality related to live data
    GNSS_DATA_ACCESS_RETENTION_DAYS: '365' # truncate historical data older then 365 days. valid range(7...730)
    GNSS_DATA_ACCESS_AGGREGATION_RETENTION_DAYS: '365' # truncate historical aggregated data older then 365 days. valid range(60...730)
    GNSS_DATA_ACCESS_RETENTION_LIVE_MINUTES: '4' # truncate live data older then 4 minutes. value can't be less then 4 minutes as we have 2 min chunks (max 60)


## Values for machineLearning
machineLearning:
  enabled: true
  replicas: 1
  name: machine-learning
  stackLabel: gnss
  port: 8094
  image:
    repository: registry-server:5000/enc/synca/gnss-machine-learning
  env:
    ENV_VAR_TEST: "ENV_VAL_TEST"
    GNSS_ML_DATA_ACCESS_SERVICE: "synca-gnss-data-access:8092"
    GNSS_LOG_LEVEL: INFO
    GNSS_ML_PREDICTOR_POOL_SIZE: '20'
    GNSS_ML_PREDICTOR_QUEUE_SIZE: '4000'


## Values for guardMl
guardMl:
  enabled: false
  replicas: 1
  name: guard-ml
  stackLabel: gnss
  port: 8112
  image:
    repository: registry-server:5000/enc/synca/gnss-guard-ml
  env:
    DATA_ACCESS_SERVICE: "synca-gnss-data-access:8092"
    LOG_LEVEL: INFO
    LONG_TERM_DATA_HISTORICAL_PERIOD_DAYS: '7'


## Values for rcaAnalysis
rcaAnalysis:
  enabled: true
  replicas: 1
  name: rca-analysis
  stackLabel: gnss
  port: 8110
  image:
    repository: registry-server:5000/enc/synca/gnss-rca-analysis
  env:
    ENV_VAR_TEST: "ENV_VAL_TEST"
    RCA_LOG_LEVEL: INFO
    RCA_ANALYSIS_CORRELATION_MAX_TIME_GAP_IN_SEC: 305

## Values for cli-worker
cliWorker:
  enabled: false
  name: cli-worker
  stackLabel: gnss
  port: 8100
  image:
    repository: registry-server:5000/enc/synca/gnss-cli-worker
  env:
    ENV_VAR_TEST: "ENV_VAL_TEST"
    GNSS_COLLECTOR_SERVICE: "synca-gnss-collector:8090"
