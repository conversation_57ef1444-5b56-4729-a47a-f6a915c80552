{{- if .Values.guardMl.enabled }}
  {{- $_ := set .Values "encApp" .Values.guardMl -}}
---
{{ include "enc.common.deploymentBundle" . }}
---
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-env-cm" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  JAVA_OPTS: "-Xms256m -Xmx1500m -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError"
  GNSSC_KAFKA_TOPIC: "gnss-topic-{{ .Values.encVersion }}"
  KAFKA_BOOTSTRAP_SERVERS: "{{ .Values.kafkaBootStrapServers }}"
  LOG_LEVEL: INFO
  KAFKA_CONSUMER_CONCURRENCY: '3'
  DATA_ACCESS_HTTP_CONNECT_TIMEOUT_IN_MILLIS: '8000'
  DATA_ACCESS_HTTP_READ_TIMEOUT_IN_MILLIS: '8000'
  ENC_HOST: "{{ .Values.encHost }}"
  ENC_HOST_2: "{{ .Values.encHost2 }}"
  ENC_PORT: '8443'
  LONG_TERM_DATA_HISTORICAL_PERIOD_DAYS: '7'

---
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-env-secret" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  ENC_MTLS_TOKEN: "{{ .Values.encMtlsToken | b64enc }}"

  {{- end }}