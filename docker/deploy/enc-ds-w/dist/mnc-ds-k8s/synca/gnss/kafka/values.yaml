localPersistentVolumes:
  ## enabled [mandatory]
  ## Set to false when the automatic pv provisioning is available
  ## Set to true for the creation local pv when automatic provisioning is not available
  ## if enabled, it will automatically create .Values.kafka.controller.replicaCount persistent volumes, one per node
  ##   e.g. for a 3 node cluster(replicaCount: 3) it will generate 3 PVs:
  ##   (mnc-synca-gnss-kafka-data-node1, mnc-synca-gnss-kafka-data-node2, mnc-synca-gnss-kafka-data-node3)
  enabled: true
  ## --------------------------------------
  ## path: [optional]
  ## Default path is /mnt/adtran/mnc-synca-gnss-kafka-data
  ## Set a custom specific directory path for the local persistent volumes
  # path: "/mnt/adtran/mnc-synca-gnss-kafka-data"
  ## ---------------------------------------

kafka:
  image:
    ## Set the registry value to empty string so that only the repository is used to construct the image name
    registry: ""
    repository: docker.io/bitnami/kafka
    tag: 3.6.0-debian-11-r0

  controller:
    replicaCount: 3
    extraConfig: "auto.create.topics.enable=false"

    persistence:
      enabled: true
      storageClass: mnc-synca-gnss-kafka-local-storage
      size: 20Gi
      accessModes: ["ReadWriteOnce"]

  volumePermissions:
    enabled: true
    image:
      ## Set the registry value to empty string so that only the repository is used to construct the image name
      registry: ""
      repository: docker.io/bitnami/os-shell
      tag: 11-debian-11-r90

  listeners:
    client:
      protocol: PLAINTEXT

# kafka-ui:
#   service:
#     type: NodePort
#     nodePort: 30900
#   envs:
#     config: 
#       KAFKA_CLUSTERS_0_NAME: "kafka-mnc-synca-gnss"
#       KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: "kafka:9092" 

#       # KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: "kafka.svc.cluster.local:9092"
#       AUTH_TYPE: "DISABLED"
#       MANAGEMENT_HEALTH_LDAP_ENABLED: "FALSE" 