---
# Source: enc-kafka/charts/kafka-ui/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: synca-tpa-kafka-ui
  namespace: default
  labels:
    helm.sh/chart: kafka-ui-0.7.5
    app.kubernetes.io/name: kafka-ui
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/version: "v0.7.1"
    app.kubernetes.io/managed-by: Helm
---
# Source: enc-kafka/charts/kafka/templates/rbac/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: synca-tpa-kafka
  namespace: "default"
  labels:
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: kafka
    app.kubernetes.io/version: 3.6.0
    helm.sh/chart: kafka-26.2.0
    app.kubernetes.io/component: kafka
automountServiceAccountToken: true
---
# Source: enc-kafka/charts/kafka/templates/secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: synca-tpa-kafka-user-passwords
  namespace: "default"
  labels:
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: kafka
    app.kubernetes.io/version: 3.6.0
    helm.sh/chart: kafka-26.2.0
type: Opaque
data:
  inter-broker-password: "ZGFGbnlYOWJSQw=="
  controller-password: "ZEVaMTZwTGVFTA=="
---
# Source: enc-kafka/charts/kafka/templates/secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: synca-tpa-kafka-kraft-cluster-id
  namespace: "default"
  labels:
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: kafka
    app.kubernetes.io/version: 3.6.0
    helm.sh/chart: kafka-26.2.0
type: Opaque
data:
  kraft-cluster-id: "TlR3WDd1Z3QyRlBrcjZrMDhRMExXRg=="
---
# Source: enc-kafka/charts/kafka-ui/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: synca-tpa-kafka-ui
  namespace: default
  labels:
    helm.sh/chart: kafka-ui-0.7.5
    app.kubernetes.io/name: kafka-ui
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/version: "v0.7.1"
    app.kubernetes.io/managed-by: Helm
data:
  AUTH_TYPE: DISABLED
  KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:9092
  KAFKA_CLUSTERS_0_NAME: kafka-mnc-synca-tpa
  MANAGEMENT_HEALTH_LDAP_ENABLED: "FALSE"
---
# Source: enc-kafka/charts/kafka/templates/controller-eligible/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: synca-tpa-kafka-controller-configuration
  namespace: "default"
  labels:
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: kafka
    app.kubernetes.io/version: 3.6.0
    helm.sh/chart: kafka-26.2.0
    app.kubernetes.io/component: controller-eligible
    app.kubernetes.io/part-of: kafka
data:
  server.properties: |-
    # Listeners configuration
    listeners=CLIENT://:9092,INTERNAL://:9094,CONTROLLER://:9093
    advertised.listeners=CLIENT://advertised-address-placeholder:9092,INTERNAL://advertised-address-placeholder:9094
    listener.security.protocol.map=CLIENT:PLAINTEXT,INTERNAL:SASL_PLAINTEXT,CONTROLLER:SASL_PLAINTEXT
    # KRaft process roles
    process.roles=controller,broker
    #node.id=
    controller.listener.names=CONTROLLER
    controller.quorum.voters=<EMAIL>:9093,<EMAIL>:9093,<EMAIL>:9093
    # Kraft Controller listener SASL settings
    sasl.mechanism.controller.protocol=PLAIN
    listener.name.controller.sasl.enabled.mechanisms=PLAIN
    listener.name.controller.plain.sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="controller_user" password="controller-password-placeholder" user_controller_user="controller-password-placeholder";
    log.dir=/bitnami/kafka/data
    sasl.enabled.mechanisms=PLAIN,SCRAM-SHA-256,SCRAM-SHA-512
    # Interbroker configuration
    inter.broker.listener.name=INTERNAL
    sasl.mechanism.inter.broker.protocol=PLAIN
    # Listeners SASL JAAS configuration
    listener.name.internal.plain.sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="inter_broker_user" password="interbroker-password-placeholder" user_inter_broker_user="interbroker-password-placeholder" user_user1="password-placeholder-0";
    listener.name.internal.scram-sha-256.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username="inter_broker_user" password="interbroker-password-placeholder";
    listener.name.internal.scram-sha-512.sasl.jaas.config=org.apache.kafka.common.security.scram.ScramLoginModule required username="inter_broker_user" password="interbroker-password-placeholder";
    # End of SASL JAAS configuration
    
    auto.create.topics.enable=false
---
# Source: enc-kafka/charts/kafka/templates/scripts-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: synca-tpa-kafka-scripts
  namespace: "default"
  labels:
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: kafka
    app.kubernetes.io/version: 3.6.0
    helm.sh/chart: kafka-26.2.0
data:
  kafka-init.sh: |-
    #!/bin/bash

    set -o errexit
    set -o nounset
    set -o pipefail

    error(){
      local message="${1:?missing message}"
      echo "ERROR: ${message}"
      exit 1
    }

    retry_while() {
        local -r cmd="${1:?cmd is missing}"
        local -r retries="${2:-12}"
        local -r sleep_time="${3:-5}"
        local return_value=1

        read -r -a command <<< "$cmd"
        for ((i = 1 ; i <= retries ; i+=1 )); do
            "${command[@]}" && return_value=0 && break
            sleep "$sleep_time"
        done
        return $return_value
    }

    replace_in_file() {
        local filename="${1:?filename is required}"
        local match_regex="${2:?match regex is required}"
        local substitute_regex="${3:?substitute regex is required}"
        local posix_regex=${4:-true}

        local result

        # We should avoid using 'sed in-place' substitutions
        # 1) They are not compatible with files mounted from ConfigMap(s)
        # 2) We found incompatibility issues with Debian10 and "in-place" substitutions
        local -r del=$'\001' # Use a non-printable character as a 'sed' delimiter to avoid issues
        if [[ $posix_regex = true ]]; then
            result="$(sed -E "s${del}${match_regex}${del}${substitute_regex}${del}g" "$filename")"
        else
            result="$(sed "s${del}${match_regex}${del}${substitute_regex}${del}g" "$filename")"
        fi
        echo "$result" > "$filename"
    }

    kafka_conf_set() {
        local file="${1:?missing file}"
        local key="${2:?missing key}"
        local value="${3:?missing value}"

        # Check if the value was set before
        if grep -q "^[#\\s]*$key\s*=.*" "$file"; then
            # Update the existing key
            replace_in_file "$file" "^[#\\s]*${key}\s*=.*" "${key}=${value}" false
        else
            # Add a new key
            printf '\n%s=%s' "$key" "$value" >>"$file"
        fi
    }

    replace_placeholder() {
      local placeholder="${1:?missing placeholder value}"
      local password="${2:?missing password value}"
      sed -i "s/$placeholder/$password/g" "$KAFKA_CONFIG_FILE"
    }

    append_file_to_kafka_conf() {
        local file="${1:?missing source file}"
        local conf="${2:?missing kafka conf file}"

        cat "$1" >> "$2"
    }

    configure_external_access() {
      # Configure external hostname
      if [[ -f "/shared/external-host.txt" ]]; then
        host=$(cat "/shared/external-host.txt")
      elif [[ -n "${EXTERNAL_ACCESS_HOST:-}" ]]; then
        host="$EXTERNAL_ACCESS_HOST"
      elif [[ -n "${EXTERNAL_ACCESS_HOSTS_LIST:-}" ]]; then
        read -r -a hosts <<<"$(tr ',' ' ' <<<"${EXTERNAL_ACCESS_HOSTS_LIST}")"
        host="${hosts[$POD_ID]}"
      elif [[ "$EXTERNAL_ACCESS_HOST_USE_PUBLIC_IP" =~ ^(yes|true)$ ]]; then
        host=$(curl -s https://ipinfo.io/ip)
      else
        error "External access hostname not provided"
      fi

      # Configure external port
      if [[ -f "/shared/external-port.txt" ]]; then
        port=$(cat "/shared/external-port.txt")
      elif [[ -n "${EXTERNAL_ACCESS_PORT:-}" ]]; then
        if [[ "${EXTERNAL_ACCESS_PORT_AUTOINCREMENT:-}" =~ ^(yes|true)$ ]]; then
          port="$((EXTERNAL_ACCESS_PORT + POD_ID))"
        else
          port="$EXTERNAL_ACCESS_PORT"
        fi
      elif [[ -n "${EXTERNAL_ACCESS_PORTS_LIST:-}" ]]; then
        read -r -a ports <<<"$(tr ',' ' ' <<<"${EXTERNAL_ACCESS_PORTS_LIST}")"
        port="${ports[$POD_ID]}"
      else
        error "External access port not provided"
      fi
      # Configure Kafka advertised listeners
      sed -i -E "s|^(advertised\.listeners=\S+)$|\1,EXTERNAL://${host}:${port}|" "$KAFKA_CONFIG_FILE"
    }
    configure_kafka_sasl() {

      # Replace placeholders with passwords
      replace_placeholder "interbroker-password-placeholder" "$KAFKA_INTER_BROKER_PASSWORD"
      replace_placeholder "controller-password-placeholder" "$KAFKA_CONTROLLER_PASSWORD"
    }

    export KAFKA_CONFIG_FILE=/config/server.properties
    cp /configmaps/server.properties $KAFKA_CONFIG_FILE

    # Get pod ID and role, last and second last fields in the pod name respectively
    POD_ID=$(echo "$MY_POD_NAME" | rev | cut -d'-' -f 1 | rev)
    POD_ROLE=$(echo "$MY_POD_NAME" | rev | cut -d'-' -f 2 | rev)

    # Configure node.id and/or broker.id
    if [[ -f "/bitnami/kafka/data/meta.properties" ]]; then
        if grep -q "broker.id" /bitnami/kafka/data/meta.properties; then
          ID="$(grep "broker.id" /bitnami/kafka/data/meta.properties | awk -F '=' '{print $2}')"
          kafka_conf_set "$KAFKA_CONFIG_FILE" "node.id" "$ID"
        else
          ID="$(grep "node.id" /bitnami/kafka/data/meta.properties | awk -F '=' '{print $2}')"
          kafka_conf_set "$KAFKA_CONFIG_FILE" "node.id" "$ID"
        fi
    else
        ID=$((POD_ID + KAFKA_MIN_ID))
        kafka_conf_set "$KAFKA_CONFIG_FILE" "node.id" "$ID"
    fi
    replace_placeholder "advertised-address-placeholder" "${MY_POD_NAME}.synca-tpa-kafka-${POD_ROLE}-headless.default.svc.cluster.local"
    if [[ "${EXTERNAL_ACCESS_ENABLED:-false}" =~ ^(yes|true)$ ]]; then
      configure_external_access
    fi
    configure_kafka_sasl
    if [ -f /secret-config/server-secret.properties ]; then
      append_file_to_kafka_conf /secret-config/server-secret.properties $KAFKA_CONFIG_FILE
    fi
---
# Source: enc-kafka/templates/sc.yaml
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: mnc-synca-tpa-kafka-local-storage
  labels:
    helm.sh/chart: enc-kafka-1.0.0
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/managed-by: Helm
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer
---
# Source: enc-kafka/templates/pv.yaml
apiVersion: v1
kind: PersistentVolume
metadata:
  name: mnc-synca-tpa-kafka-data-node1
  labels:
    helm.sh/chart: enc-kafka-1.0.0
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/managed-by: Helm
  annotations:
    # helm.sh/hook: pre-install,post-delete
    # helm.sh/hook-weight: 0
    # resource will not be deleted by helm 
    helm.sh/resource-policy: keep
spec:
  capacity:
    storage: 20Gi
  volumeMode: Filesystem
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: mnc-synca-tpa-kafka-local-storage
  local:
    path: /mnt/adtran/mnc-synca-tpa-kafka-data
  nodeAffinity:
    required:
      nodeSelectorTerms:
        - matchExpressions:
          - key: mnc-node
            operator: In
            values:
              - node1
---
# Source: enc-kafka/templates/pv.yaml
apiVersion: v1
kind: PersistentVolume
metadata:
  name: mnc-synca-tpa-kafka-data-node2
  labels:
    helm.sh/chart: enc-kafka-1.0.0
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/managed-by: Helm
  annotations:
    # helm.sh/hook: pre-install,post-delete
    # helm.sh/hook-weight: 0
    # resource will not be deleted by helm 
    helm.sh/resource-policy: keep
spec:
  capacity:
    storage: 20Gi
  volumeMode: Filesystem
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: mnc-synca-tpa-kafka-local-storage
  local:
    path: /mnt/adtran/mnc-synca-tpa-kafka-data
  nodeAffinity:
    required:
      nodeSelectorTerms:
        - matchExpressions:
          - key: mnc-node
            operator: In
            values:
              - node2
---
# Source: enc-kafka/templates/pv.yaml
apiVersion: v1
kind: PersistentVolume
metadata:
  name: mnc-synca-tpa-kafka-data-node3
  labels:
    helm.sh/chart: enc-kafka-1.0.0
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/managed-by: Helm
  annotations:
    # helm.sh/hook: pre-install,post-delete
    # helm.sh/hook-weight: 0
    # resource will not be deleted by helm 
    helm.sh/resource-policy: keep
spec:
  capacity:
    storage: 20Gi
  volumeMode: Filesystem
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: mnc-synca-tpa-kafka-local-storage
  local:
    path: /mnt/adtran/mnc-synca-tpa-kafka-data
  nodeAffinity:
    required:
      nodeSelectorTerms:
        - matchExpressions:
          - key: mnc-node
            operator: In
            values:
              - node3
---
# Source: enc-kafka/charts/kafka-ui/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: synca-tpa-kafka-ui
  namespace: default
  labels:
    helm.sh/chart: kafka-ui-0.7.5
    app.kubernetes.io/name: kafka-ui
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/version: "v0.7.1"
    app.kubernetes.io/managed-by: Helm
spec:
  type: NodePort
  ports:
    - port: 80
      targetPort: http
      protocol: TCP
      name: http
      nodePort: 30900
  selector:
    app.kubernetes.io/name: kafka-ui
    app.kubernetes.io/instance: synca-tpa
---
# Source: enc-kafka/charts/kafka/templates/controller-eligible/svc-headless.yaml
apiVersion: v1
kind: Service
metadata:
  name: synca-tpa-kafka-controller-headless
  namespace: "default"
  labels:
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: kafka
    app.kubernetes.io/version: 3.6.0
    helm.sh/chart: kafka-26.2.0
    app.kubernetes.io/component: controller-eligible
    app.kubernetes.io/part-of: kafka
spec:
  type: ClusterIP
  clusterIP: None
  publishNotReadyAddresses: true
  ports:
    - name: tcp-interbroker
      port: 9094
      protocol: TCP
      targetPort: interbroker
    - name: tcp-client
      port: 9092
      protocol: TCP
      targetPort: client
    - name: tcp-controller
      protocol: TCP
      port: 9093
      targetPort: controller
  selector:
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/name: kafka
    app.kubernetes.io/component: controller-eligible
    app.kubernetes.io/part-of: kafka
---
# Source: enc-kafka/charts/kafka/templates/svc.yaml
apiVersion: v1
kind: Service
metadata:
  name: synca-tpa-kafka
  namespace: "default"
  labels:
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: kafka
    app.kubernetes.io/version: 3.6.0
    helm.sh/chart: kafka-26.2.0
    app.kubernetes.io/component: kafka
spec:
  type: ClusterIP
  sessionAffinity: None
  ports:
    - name: tcp-client
      port: 9092
      protocol: TCP
      targetPort: client
      nodePort: null
  selector:
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/name: kafka
    app.kubernetes.io/part-of: kafka
---
# Source: enc-kafka/charts/kafka-ui/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: synca-tpa-kafka-ui
  namespace: default
  labels:
    helm.sh/chart: kafka-ui-0.7.5
    app.kubernetes.io/name: kafka-ui
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/version: "v0.7.1"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: kafka-ui
      app.kubernetes.io/instance: synca-tpa
  template:
    metadata:
      annotations:
        checksum/config: 772e72e9002b0d80e4f6ef9f6fb861575f955dcb136c326737c1346db2dea3d4
        checksum/configFromValues: 01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b
        checksum/secret: 01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b
      labels:
        app.kubernetes.io/name: kafka-ui
        app.kubernetes.io/instance: synca-tpa
    spec:
      serviceAccountName: synca-tpa-kafka-ui
      securityContext:
        {}
      containers:
        - name: kafka-ui
          securityContext:
            {}
          image: docker.io/provectuslabs/kafka-ui:v0.7.1
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: synca-tpa-kafka-ui    
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: http
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: http
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
          resources:
            {}
---
# Source: enc-kafka/charts/kafka/templates/controller-eligible/statefulset.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: synca-tpa-kafka-controller
  namespace: "default"
  labels:
    app.kubernetes.io/instance: synca-tpa
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: kafka
    app.kubernetes.io/version: 3.6.0
    helm.sh/chart: kafka-26.2.0
    app.kubernetes.io/component: controller-eligible
    app.kubernetes.io/part-of: kafka
spec:
  podManagementPolicy: Parallel
  replicas: 3
  selector:
    matchLabels:
      app.kubernetes.io/instance: synca-tpa
      app.kubernetes.io/name: kafka
      app.kubernetes.io/component: controller-eligible
      app.kubernetes.io/part-of: kafka
  serviceName: synca-tpa-kafka-controller-headless
  updateStrategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/instance: synca-tpa
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: kafka
        app.kubernetes.io/version: 3.6.0
        helm.sh/chart: kafka-26.2.0
        app.kubernetes.io/component: controller-eligible
        app.kubernetes.io/part-of: kafka
      annotations:
        checksum/configuration: 9ce0213f8fa5f2970651d93275bf2208b939a2dc8ec136c7767cbcf51131fba2
        checksum/passwords-secret: 9e7c1f958dea28b7a6c35bd136acbc1090227ff8f4e6a4120c516d67f5ed8986
    spec:
      
      hostNetwork: false
      hostIPC: false
      affinity:
        podAffinity:
          
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchLabels:
                    app.kubernetes.io/instance: synca-tpa
                    app.kubernetes.io/name: kafka
                    app.kubernetes.io/component: controller-eligible
                topologyKey: kubernetes.io/hostname
              weight: 1
        nodeAffinity:
          
      securityContext:
        fsGroup: 1001
        seccompProfile:
          type: RuntimeDefault
      serviceAccountName: synca-tpa-kafka
      enableServiceLinks: true
      initContainers:
        - name: volume-permissions
          image: docker.io/bitnami/os-shell:11-debian-11-r90
          imagePullPolicy: "IfNotPresent"
          command:
            - /bin/bash
          args:
            - -ec
            - |
              mkdir -p "/bitnami/kafka" "/opt/bitnami/kafka/logs"
              chown -R 1001:1001 "/bitnami/kafka" "/opt/bitnami/kafka/logs"
              find "/bitnami/kafka" -mindepth 1 -maxdepth 1 -not -name ".snapshot" -not -name "lost+found" | xargs -r chown -R 1001:1001
              find "/opt/bitnami/kafka/logs" -mindepth 1 -maxdepth 1 -not -name ".snapshot" -not -name "lost+found" | xargs -r chown -R 1001:1001
          securityContext:
            runAsUser: 0
          resources:
            limits: {}
            requests: {}
          volumeMounts:
            - name: data
              mountPath: /bitnami/kafka
            - name: logs
              mountPath: /opt/bitnami/kafka/logs
        - name: kafka-init
          image: docker.io/bitnami/kafka:3.6.0-debian-11-r0
          imagePullPolicy: IfNotPresent
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 1001
          command:
            - /bin/bash
          args:
            - -ec
            - |
              /scripts/kafka-init.sh
          env:
            - name: BITNAMI_DEBUG
              value: "false"
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                    fieldPath: metadata.name
            - name: KAFKA_VOLUME_DIR
              value: "/bitnami/kafka"
            - name: KAFKA_MIN_ID
              value: "0"
            - name: KAFKA_INTER_BROKER_USER
              value: "inter_broker_user"
            - name: KAFKA_INTER_BROKER_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: synca-tpa-kafka-user-passwords
                  key: inter-broker-password
            - name: KAFKA_CONTROLLER_USER
              value: "controller_user"
            - name: KAFKA_CONTROLLER_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: synca-tpa-kafka-user-passwords
                  key: controller-password
          volumeMounts:
            - name: data
              mountPath: /bitnami/kafka
            - name: kafka-config
              mountPath: /config
            - name: kafka-configmaps
              mountPath: /configmaps
            - name: kafka-secret-config
              mountPath: /secret-config
            - name: scripts
              mountPath: /scripts
            - name: tmp
              mountPath: /tmp
      containers:
        - name: kafka
          image: docker.io/bitnami/kafka:3.6.0-debian-11-r0
          imagePullPolicy: "IfNotPresent"
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 1001
          env:
            - name: BITNAMI_DEBUG
              value: "false"
            - name: KAFKA_HEAP_OPTS
              value: "-Xmx1024m -Xms1024m"
            - name: KAFKA_KRAFT_CLUSTER_ID
              valueFrom:
                secretKeyRef:
                  name: synca-tpa-kafka-kraft-cluster-id
                  key: kraft-cluster-id
            - name: KAFKA_KRAFT_BOOTSTRAP_SCRAM_USERS
              value: "true"
            - name: KAFKA_INTER_BROKER_USER
              value: "inter_broker_user"
            - name: KAFKA_INTER_BROKER_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: synca-tpa-kafka-user-passwords
                  key: inter-broker-password
            - name: KAFKA_CONTROLLER_USER
              value: "controller_user"
            - name: KAFKA_CONTROLLER_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: synca-tpa-kafka-user-passwords
                  key: controller-password
          ports:
            - name: controller
              containerPort: 9093
            - name: client
              containerPort: 9092
            - name: interbroker
              containerPort: 9094
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
            tcpSocket:
              port: "controller"
          readinessProbe:
            failureThreshold: 6
            initialDelaySeconds: 5
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
            tcpSocket:
              port: "controller"
          resources:
            limits: {}
            requests: {}
          volumeMounts:
            - name: data
              mountPath: /bitnami/kafka
            - name: logs
              mountPath: /opt/bitnami/kafka/logs
            - name: kafka-config
              mountPath: /opt/bitnami/kafka/config/server.properties
              subPath: server.properties
            - name: tmp
              mountPath: /tmp
      volumes:
        - name: kafka-configmaps
          configMap:
            name: synca-tpa-kafka-controller-configuration
        - name: kafka-secret-config
          emptyDir: {}
        - name: kafka-config
          emptyDir: {}
        - name: tmp
          emptyDir: {}
        - name: scripts
          configMap:
            name: synca-tpa-kafka-scripts
            defaultMode: 0755
        - name: logs
          emptyDir: {}
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes:
          - "ReadWriteOnce"
        resources:
          requests:
            storage: "20Gi"
        storageClassName: mnc-synca-tpa-kafka-local-storage
