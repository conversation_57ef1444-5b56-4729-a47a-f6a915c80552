{{- if .Values.localPersistentVolumes.enabled }}
{{- $nodeCount := int .Values.kafka.controller.replicaCount }}
{{- $persistence := dict "size" .Values.kafka.controller.persistence.size "storageClass" .Values.kafka.controller.persistence.storageClass }}
{{- $localVolumePath := .Values.localPersistentVolumes.path}}
{{- $chartNameAndVersion := printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- $releaseName := .Release.Name }}
{{- $releaseService := .Release.Service }}
{{- range $index := until $nodeCount }}
apiVersion: v1
kind: PersistentVolume
metadata:
  name: mnc-synca-tpa-kafka-data-node{{ add $index 1 }}
  labels:
    helm.sh/chart: {{ $chartNameAndVersion }}
    app.kubernetes.io/instance: {{ $releaseName }}
    app.kubernetes.io/managed-by: {{ $releaseService }}
  annotations:
    # helm.sh/hook: pre-install,post-delete
    # helm.sh/hook-weight: 0
    # resource will not be deleted by helm 
    helm.sh/resource-policy: keep
spec:
  capacity:
    storage: {{ $persistence.size }}
  volumeMode: Filesystem
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: {{ $persistence.storageClass | default "mnc-synca-tpa-kafka-local-storage"}}
  local:
    path: {{ $localVolumePath | default "/mnt/adtran/mnc-synca-tpa-kafka-data"}}
  nodeAffinity:
    required:
      nodeSelectorTerms:
        - matchExpressions:
          - key: mnc-node
            operator: In
            values:
              - node{{ add $index 1 }}
---
{{- end }}
{{- end }}
