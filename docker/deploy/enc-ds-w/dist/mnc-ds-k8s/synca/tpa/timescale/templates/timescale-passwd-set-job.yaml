apiVersion: batch/v1
kind: Job
metadata:
  name: timescaledb-passwd-set-job-{{ .Release.Revision }}
spec:
  backoffLimit: 15
  # ttlSecondsAfterFinished: 600
  template:
    spec:
      containers:
        - name: timescaledb-passwd-set
          image: "{{ .Values.timescaledb.image.repository }}:{{ .Values.timescaledb.image.tag }}"
          command: ["/usr/lib/postgresql/14/bin/psql"]
          args: ["-v", "ON_ERROR_STOP=1", "-f", "/etc/synca/user_passwd_set_sql_statement.sql"]
          env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: timescaledb-credentials
                  key: PATRONI_SUPERUSER_PASSWORD
            - name: PGDATABASE
              value: fnm_sync_pm
            - name: PGUSER
              value: postgres
            - name: PGPORT
              value: {{ .Values.timescaledb.service.primary.port | quote }}
            - name: PGHOST
              value: timescaledb
          volumeMounts:
            - name: sql-secret-volume
              mountPath: /etc/synca
              readOnly: true
      restartPolicy: OnFailure
      volumes:
        - name: sql-secret-volume
          secret:
            secretName: timescaledb-passwd-set-sql
            items:
              - key: sql_statement
                path: user_passwd_set_sql_statement.sql