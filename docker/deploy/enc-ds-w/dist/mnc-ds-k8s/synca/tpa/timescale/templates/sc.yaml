{{- if .Values.localPersistentVolumes.enabled }}
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: mnc-synca-tpa-ts-local-storage
  labels:
    helm.sh/chart: {{ printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer
{{- end }}