passwordInitScriptBase64Decoded: "IyEvYmluL2Jhc2gKcHNxbCAtZCAiJDEiIC0tZmlsZT0tIC0tc2V0IE9OX0VSUk9SX1NUT1A9MSA8PCBfX1NRTF9fClNFVCBsb2dfc3RhdGVtZW50IFRPIG5vbmU7ICAgICAgLS0gcHJldmVudCB0aGVzZSBwYXNzd29yZHMgZnJvbSBiZWluZyBsb2dnZWQKQUxURVIgVVNFUiBmbm1fc3luY19wbSAgV0lUSCBQQVNTV09SRCAnZm5tX3N5bmNfcG0nOwpBTFRFUiBST0xFIGZubV9zeW5jX3BtIFdJVEggTE9HSU47CkFMVEVSIFVTRVIgdGVzdDIgV0lUSCBQQVNTV09SRCAnaGFyZGVyMmd1ZXNzJzsKX19TUUxfXw=="
localPersistentVolumes:
  ## enabled [mandatory]
  ## Set to false when the automatic pv provisioning is available
  ## Set to true for the creation local pv when automatic provisioning is not available
  ## if enabled, it will automatically create .Values.timescaledb.replicaCount persistent volumes, one per node
  ##   e.g. for a 3 node cluster(replicaCount: 3) it will generate 3 PVs:
  ##   (mnc-synca-tpa-ts-data-node1, mnc-synca-tpa-ts-data-node2, mnc-synca-tpa-ts-data-node3)
  enabled: true
  ## --------------------------------------
  ## path: [optional]
  ## Default path is /mnt/adtran/mnc-synca-tpa-ts-data
  ## Set a custom specific directory path for the local persistent volumes
  # path: "/mnt/adtran/mnc-synca-tpa-ts-data"
  ## --------------------------------------

timescaledb:
  image:
    repository: timescale/timescaledb-ha
    tag: pg14.11-ts2.14.2
  replicaCount: 3
  postInit:
    - configMap:
        name: timescale-post-init
        optional: true
    - secret:
        name: timescale-post-init-pw
 
  persistentVolumes:
    data:
      enabled: true
      size: 500Gi
      ##  storageClass:
      ## If undefined (the default) or set to null, no storageClassName spec is set, 
      ## choosing the default provisioner  (gp3 on AWS, standard on GKE etc..)
      ##
      ## storageClass: "mnc-synca-tpa-ts-local-storage"
      ## Must be set to "mnc-synca-tpa-ts-local-storage" if using local storage
      ## When is set to "mnc-synca-tpa-ts-local-storage" the localPersistentVolumes.enabled must be set to true
      storageClass: "mnc-synca-tpa-ts-local-storage"

    wal:
    ## WAL will be a subdirectory of the data volume, which means enabling a separate
    ## volume for the WAL files should just work for new pods.
      enabled: false
      # size: 1Gi
      # storageClass: "local-storage"

  ## Uncomment the following to expose the service to the outside world via a node port
#  service:
#    primary:
#      type: NodePort
#      nodePort: 30258

  secrets:
    ## access key and secret key of S3 user authorized to write to relevant bucket (mandatory for backup)
    pgbackrest:
      PGBACKREST_REPO1_S3_KEY: "Put_your_S3_user_access_key_here"
      PGBACKREST_REPO1_S3_KEY_SECRET: "Put_your_S3_user_secret_key_here"

  patroni:
    log:
      level: WARNING
    bootstrap:
      dcs:
        maximum_lag_on_failover: 33554432
        postgresql:
          parameters:
            ## For the documentation of pgbackrest archive-push command refer to: https://pgbackrest.org/command.html#command-archive-push
            ## --archive-push-queue-max was added for two reasons:
            ## - prevent db master from failure due to disk filled up with not archived WAL files in case archive repo is lagging
            ## - recover from file conflict issue blocking WAL archiving and backup in some failure scenarios
            ## set this value carefully as limiting the queue size too much can cause gaps in WAL archives
            archive_command: "/etc/timescaledb/scripts/pgbackrest_archive.sh %p --archive-push-queue-max=2GiB"
            archive_timeout: 1800s
            max_connections: 300
            max_wal_size: '1GB'
            ## use max_slot_wal_keep_size to prevent db master from failure due to disk filled up with WAL files
            ## in case db replica is broken
            max_slot_wal_keep_size: '2GB'
    postgresql:
      ## Use basebackup replica creation method as the default one as it turned out more reliable especially in
      ## bootstrap from backup scenarios when point in time recovery and timeline selection was used
      create_replica_methods:
        - basebackup
        - pgbackrest

  ## Periodical backup of the database
  backup:
    enabled: true
    pgBackRest:
      compress-type: lz4
      process-max: 4
      start-fast: "y"
      repo1-cipher-type: "none"
      ## service name should be: <object storage(minio) release name>.<object storage (minio) namespace>.svc.cluster.local
      repo1-s3-endpoint: "minio.mnc-common.svc.cluster.local"
      ## should be set even in case region is not defined for the S3 bucket
      repo1-s3-region: "region-1"
      repo1-s3-bucket: "mnc-synca-tpa-ts-backup"
      repo1-path: "/"
      repo1-storage-port: "9000"
      repo1-s3-uri-style: "path"
      ## tls storage verification should be disabled in case the certificate is self-signed
      repo1-storage-verify-tls: 'n'      
      repo1-type: "s3"
      ## supported values: time, count (default)
      repo1-retention-full-type: "count"
      ## how many days of backup / how many backups should be retained
      ## incremental backup retention is dependent on full backup retention
      repo1-retention-full: 7
    ## backup schedule
    jobs:
      - name: full-daily
        type: full
        schedule: "0 0 * * *"
      # - name: incremental-hourly
      #   type: incr
      #   schedule: "0 * * * *"

  ## use to recover from last backup and get all missing wal files from the backup repository (refer to documentation for the whole procedure)
  bootstrapFromBackup:
    enabled: false
    # Setting the s3 path is mandatory to avoid overwriting an already existing backup,
    # and to be sure the restore is explicitly the one requested.
    repo1-path: "/"
    # Here you can (optionally) provide a restore point in time (date time string) value for the bootstrap from master operation
    # by default this configuration is empty, which means the bootstrap of master will be done based on the latest available backup
    # if configured, it should contain a valid date time string in this format: yyyy-MM-dd HH:mm:ss
    # also make sure that the archive repository contains wal files up to this time from the nearest full backup(there is automatic retention of wal files in the backup repository)
    # by default (restore-point-in-time-timeline is empty) this action shall use the latest timeline(of the master) to recover
    # note that a PITR recovery shall create a new timeline branched out of the timeline used for recovery
    # for example:
    # restore-point-in-time: "2024-03-16 18:00:03"
    restore-point-in-time: ""
    # here you can provide information about which timeline to use( for the restore point in time operation )
    # it is only relevant if restore-point-in-time is set with a specific time to recover from (not empty)
    # supported values:
    # "latest" (this is the default) - use the latest master timeline for the recovery
    # "current" - use the timeline of the previous backup (relative to the restore-point-in-time used)
    # "<positive integer>" - use a specific exiting timeline number inside a string
    # for example:
    # restore-point-in-time-timeline: "5"
    restore-point-in-time-timeline: "latest"


  # There are job and cronjob resources that run during updates or backups that use curl image
  # Users in that want to define their own curl image can set it below
  # Example: https://github.com/timescale/helm-charts/blob/main/charts/timescaledb-single/templates/job-update-patroni.yaml
  curlImage:
    repository: curlimages/curl
    tag: "8.6.0"
