{{- if .Values.onlineQm.enabled }}
  {{- $_ := set .Values "encApp" .Values.onlineQm -}}
---
{{ include "enc.common.deploymentBundle" . }}
---
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-env-cm" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  # General
  DB_HOST: "{{ .Values.dbHost }}"
  DB_PORT: "5432"
  DB_NAME: fnm_sync_pm
  DB_USER: fnm_sync_pm
  ENC_HOST: "{{ .Values.encHost }}"
  ENC_HOST_2: "{{ .Values.encHost2 }}"
  ENC_PORT: '8443'
  JAVA_OPTS: "-Xms256m -Xmx1500m -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError"
  TPA_LOG_LEVEL: {{ .Values.logLevel }}
  ONLINE_QM_KAFKA_BOOTSTRAP_SERVERS: "{{ .Values.kafkaBootStrapServers }}"
  # Collection
  # TODO? - construct collector service name dynamically (also for gnss cli worker and internal proxy)
#  TEST: {{ printf "%#v" . }}
  ONLINE_QM_COLLECTOR_HOST: "synca-tpa-collector"
  ONLINE_QM_COLLECTOR_CONNECT_TIMEOUT_MS: '3000'
  ONLINE_QM_COLLECTOR_READ_TIMEOUT_MS: '3000'
  ONLINE_QM_KAFKA_COLLECTOR_REGISTRATION_CMD_TOPIC: "tpa-online-qm-collector-registration-cmd-topic-{{ .Values.encVersion }}"
  ONLINE_QM_KAFKA_COLLECTOR_REGISTRATION_CMD_TOPIC_REPLICAS: '3'
  ONLINE_QM_KAFKA_COLLECTOR_REGISTRATION_CMD_TOPIC_PARTITIONS: '9'
  ONLINE_QM_KAFKA_COLLECTOR_REGISTRATION_REPLY_TOPIC: "tpa-online-qm-collector-registration-reply-topic-{{ .Values.encVersion }}"
  # Profiles, job and calculation
  ONLINE_QM_KAFKA_CALCULATION_TOPIC: "tpa-online-qm-calculation-topic-{{ .Values.encVersion }}"
  ONLINE_QM_KAFKA_CALCULATION_TOPIC_CONSUMER_CONCURRENCY: '10'
  ONLINE_QM_KAFKA_PROFILE_ADMIN_TOPIC: "tpa-online-qm-profile-admin-topic-{{ .Values.encVersion }}"
  ONLINE_QM_KAFKA_PROFILE_ADMIN_TOPIC_REPLICAS: '3'
  ONLINE_QM_KAFKA_PROFILE_ADMIN_TOPIC_PARTITIONS: '9'
  ONLINE_QM_KAFKA_JOB_STATE_TOPIC: "tpa-online-qm-job-state-topic-{{ .Values.encVersion }}"
  ONLINE_QM_KAFKA_JOB_STATE_TOPIC_REPLICAS: '3'
  ONLINE_QM_KAFKA_JOB_STATE_TOPIC_PARTITIONS: '9'
  # Alarms and ENC connectivity
  ONLINE_QM_KAFKA_ALARM_TOPIC: "tpa-online-qm-alarm-topic-{{ .Values.encVersion }}"
  ONLINE_QM_KAFKA_ALARM_TOPIC_REPLICAS: '3'
  ONLINE_QM_KAFKA_ALARM_TOPIC_PARTITIONS: '9'
  ONLINE_QM_ENC_CONNECT_TIMEOUT_MS: '5000'
  ONLINE_QM_ENC_READ_TIMEOUT_MS: '5000'
  ONLINE_QM_HIKARI_MAX_LIFETIME: '600000'
  ONLINE_QM_HIKARI_MAXIMUM_POOL_SIZE: '20'
  ONLINE_QM_HIKARI_MINIMUM_IDLE: '20'
  ONLINE_QM_HIKARI_CONNECTION_TIMEOUT: '30000'
  ONLINE_QM_HIKARI_IDLE_TIMEOUT: '600000'
  ONLINE_QM_HIKARI_VALIDATION_TIMEOUT: '5000'

---
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-env-secret" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  DB_PASSWORD: "{{ .Values.dbPassword | b64enc }}"
  ENC_MTLS_TOKEN: "{{ .Values.encMtlsToken | b64enc }}"

  {{- end }}