# The encVersion must be set to a specific version number e.g  16.2.1-B12345 during deployment
# Attention: Do not use "latest" for the encVersion
encVersion: latest # Must be set during deployment
encHost: "*******" # Must be set during deployment
encHost2: "*******" # Must be set during deployment
encMtlsToken: ""  # Must be set during deployment
dbHost: "timescaledb"
dbPassword: "fnm_sync_pm"
kafkaBootStrapServers: "kafka:9092"
logLevel: "INFO"

## Values for collector
collector:
  enabled: true
  replicas: 1
  name: collector
  stackLabel: tpa
  port: 8098
  image:
    repository: registry-server:5000/enc/synca/tpa-collector
  # service:
  #   type: NodePort
  #   nodePort: 31098

  # NOTE: environment variables set here have precedence over the ones taken from application's ConfigMap and in some cases (like log level)
  # they can shadow values taken from mnc-ds-ctl.yml. This happens when such variable is constructed in the ConfigMap
  # from field specified in mnc-ds-ctl.yml other than tpa.apps.values.config.collector.env.variable_name
  env:
    TPA_LOG_LEVEL: INFO
    TPA_SCHEDULER_CRON_EXP_MIN: '*/5'
    TPA_PROCESS_ONLY_NEW_FILES: 'true'
    TPA_MAX_DEBUG_MODE_PROBES: '10'

## Values for dataAccess
dataAccess:
  enabled: true
  replicas: 1
  name: data-access
  stackLabel: tpa
  port: 8096
  image:
    repository: registry-server:5000/enc/synca/tpa-data-access
  # service:
  #   type: NodePort
  #   nodePort: 31096
  
  # NOTE: environment variables set here have precedence over the ones taken from application's ConfigMap and in some cases (like log level)
  # they can shadow values taken from mnc-ds-ctl.yml. This happens when such variable is constructed in the ConfigMap
  # from field specified in mnc-ds-ctl.yml other than tpa.apps.values.config.dataAccess.env.variable_name
  env:
    TPA_LOG_LEVEL: INFO
    TPADA_RETENTION_DAYS: '90'
    TPADA_DM_RETENTION_DAYS: '14'

## Values for onlineQm
onlineQm:
  enabled: true
  replicas: 1
  name: online-qm
  stackLabel: tpa
  port: 8102
  image:
    repository: registry-server:5000/enc/synca/tpa-online-qm
  # NOTE: environment variables set here have precedence over the ones taken from application's ConfigMap and in some cases (like log level)
  # they can shadow values taken from mnc-ds-ctl.yml. This happens when such variable is constructed in the ConfigMap
  # from field specified in mnc-ds-ctl.yml other than tpa.apps.values.config.onlineQm.env.variable_name
  env:
    TPA_LOG_LEVEL: INFO