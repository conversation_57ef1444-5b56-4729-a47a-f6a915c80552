{{- if .Values.collector.enabled }}
{{- $_ := set .Values "encApp" .Values.collector -}}
---
{{ include "enc.common.deploymentBundle" . }}
---
# NOTE: Manual POD restart is required after update 
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-env-cm" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm  
data:
  # General
  DB_HOST: "{{ .Values.dbHost }}"
  DB_PORT: "5432"
  DB_NAME: fnm_sync_pm
  DB_USER: fnm_sync_pm
  JAVA_OPTS: "-Xms256m -Xmx1500m -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError"
  KAFKA_BOOTSTRAP_SERVERS: "{{ .Values.kafkaBootStrapServers }}"
  TPA_LOG_LEVEL: {{ .Values.logLevel }}
  # Scheduling
  TPA_SCHEDULER_CRON_EXP_MIN: '*/5'
  TPA_SCHEDULER_CRON_EXP_SEC: '0'
  # File operations
  FILE_WORKER_POOL_SIZE: '3'
  TPA_PROCESS_ONLY_NEW_FILES: 'true'
  TPA_PROCESS_ONLY_NEW_FILES_BASED_ON_PREVIOUS_LIST_OPERATION: 'true'
  TPA_DELAY_AFTER_FILE_LIST_OPERATION_SEC: '-1' # add delay after the list operation only for testing purposes (-1=disable).
  # TPA_MAX_NUMBER_OF_DOWNLOAD_RETRIES. (Documentation)
  # Note that with a regular delay factor retryCount*5min (max delay 1 hour) => last retry is performed ~ after 2 days
  # Once we reach the max retry counter number then no more download retries are performed
  TPA_MAX_NUMBER_OF_DOWNLOAD_RETRIES: '50'
  # TPA_DELAY_FACTOR_BETWEEN_DOWNLOAD_RETRIES_SEC. (Documentation)
  # It is used to calculate delay of the next retry = retryCount * delay-factor-between-download-retries
  # The default value is set to 5 min (300sec) so
  # First retry is after 5 min , second retry is 2*5=10 after 10min and so on
  # Note also that max delay is hardcoded to be 6h regardless of the calculation
  TPA_DELAY_FACTOR_BETWEEN_DOWNLOAD_RETRIES_SEC: '300'
  KAFKA_TPA_FILE_LIST_TOPIC: "tpa-file-list-topic-{{ .Values.encVersion }}"
  KAFKA_TPA_FILE_LIST_TOPIC_REPLICAS: '3'
  KAFKA_TPA_FILE_LIST_TOPIC_PARTITIONS: '9'
  KAFKA_FILE_RESCHEDULE_DOWNLOAD_WITH_DELAY_TOPIC: "tpa-file-reschedule-download-with-delay-topic-{{ .Values.encVersion }}"
  KAFKA_FILE_RESCHEDULE_DOWNLOAD_WITH_DELAY_TOPIC_REPLICAS: '3'
  KAFKA_FILE_RESCHEDULE_DOWNLOAD_WITH_DELAY_TOPIC_PARTITIONS: '9'
  # TIE measurements
  KAFKA_TPA_TIE_MEASUREMENTS_TOPIC: "tpa-tie-measurements-topic-{{ .Values.encVersion }}"
  KAFKA_TPA_TIE_MEASUREMENTS_TOPIC_REPLICAS: '3'
  KAFKA_TPA_TIE_MEASUREMENTS_TOPIC_PARTITIONS: '9'
  TIE_MEASUREMENT_CHUNK_SIZE: '4000'
  TPA_MAX_DEBUG_MODE_PROBES: '10'
  # Online QM
  KAFKA_TPA_ONLINE_QM_COLLECTOR_REGISTRATION_CMD_TOPIC: "tpa-online-qm-collector-registration-cmd-topic-{{ .Values.encVersion }}"
  KAFKA_TPA_ONLINE_QM_COLLECTOR_REGISTRATION_REPLY_TOPIC: "tpa-online-qm-collector-registration-reply-topic-{{ .Values.encVersion }}"
  KAFKA_TPA_ONLINE_QM_COLLECTOR_REGISTRATION_REPLY_TOPIC_REPLICAS: '3'
  KAFKA_TPA_ONLINE_QM_COLLECTOR_REGISTRATION_REPLY_TOPIC_PARTITIONS: '9'
  KAFKA_TPA_ONLINE_QM_CALCULATION_TOPIC: "tpa-online-qm-calculation-topic-{{ .Values.encVersion }}"
  KAFKA_TPA_ONLINE_QM_CALCULATION_TOPIC_REPLICAS: '3'
  KAFKA_TPA_ONLINE_QM_CALCULATION_TOPIC_PARTITIONS: '30'

---
# NOTE: Manual POD restart is required after update 
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-env-secret" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm  
data:
  DB_PASSWORD: "{{ .Values.dbPassword | b64enc }}"

{{- end }}