{{- if .Values.dataAccess.enabled }}
  {{- $_ := set .Values "encApp" .Values.dataAccess -}}
---
{{ include "enc.common.deploymentBundle" . }}
---
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-env-cm" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  # General
  DB_HOST: "{{ .Values.dbHost }}"
  DB_PORT: "5432"
  DB_NAME: fnm_sync_pm
  DB_USER: fnm_sync_pm
  JAVA_OPTS: "-Xms256m -Xmx5500m -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError"
  TPA_LOG_LEVEL: {{ .Values.logLevel }}
  TPADA_KAFKA_BOOTSTRAP_SERVERS: "{{ .Values.kafkaBootStrapServers }}"
  TPADA_DB_CON_MAX_POOL_SIZE: '30'
  # Measurement store
  TPADA_KAFKA_TOPIC: "tpa-tie-measurements-topic-{{ .Values.encVersion }}"
  TIE_MEASUREMENT_TOPIC_CONSUMER_CONCURRENCY: '3'
  TIE_DATA_STORE_THREAD_POOL_SIZE: '3' #number of workers storing into database in parallel. should be no more then TPADA_DB_CON_MAX_POOL_SIZE
  TIE_DATA_STORE_THREAD_POOL_QUEUE_SIZE: '5000'  # the size queue to store messages comming from kafka to store in database by the workers
  TIE_DATA_STORE_BATCH_SIZE: '1000'  # TIE batch size to insert into database in one shot
  # QM calculation
  KAFKA_TPA_QM_CALCULATION_TOPIC : "tpa-qm-calculation-topic-{{ .Values.encVersion }}"
  KAFKA_TPA_QM_CALCULATION_TOPIC_REPLICAS: '3'
  KAFKA_TPA_QM_CALCULATION_TOPIC_PARTITIONS: '9'
  QM_CALCULATION_WORKER_POOL_SIZE: '2' # max number of QM calculations (threads) to run in parallel
  QM_CALCULATION_MAX_SCHEDULED_JOB: '25' # max number of scheduled QM calculation jobs. -1 means infinite
  TEST_TIE_RESULT_THREAD_POOL_SIZE: '2' # max number of active threads processing the test tie results
  TEST_TIE_RESULT_THREAD_POOL_QUEUE_SIZE: '10' # Queue size for the testTieResult Thread Pool
  # Export Long Term Results
  EXPORT_TO_FILE_TIE_MAX_CHUNK_SIZE: '150000' # valid range: (4000..500000) while exporting file, this is the theoretical max number of TIEs to send into streaming reply in one shot(get from db, generate body chunk, and send to output stream)
  EXPORT_TO_FILE_MAX_RANGE_IN_DAYS: '14' # maximum range in days allowed in the export file request (range is determined by the to-from)
  # Data retention
  TPADA_RETENTION_DAYS: '90'  # number of days (old) before automatic deletion of regular TIEs and test status rows from database. truncate historical data older then 90 days. valid range(1...360)
  TPADA_DM_RETENTION_DAYS: '14' # number of days (old) before automatic deletion of debug TIEs rows from database. truncate historical debug mode data older then 14 days. valid range(1...30)

---
# NOTE: Manual POD restart is required after update
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-env-secret" (include "enc.common.names.fullname" .) }}
  labels:
    app.kubernetes.io/managed-by: Helm
data:
  DB_PASSWORD: "{{ .Values.dbPassword | b64enc }}"

  {{- end }}