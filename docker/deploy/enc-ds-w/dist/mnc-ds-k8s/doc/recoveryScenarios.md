# Documentation for recovering from different failure conditions 

## General

## Prerequisites
1. Sync Assurance application is deployed on a three node k8s cluster.
2. MNC Mediation server is installed on separate machine/vm, and is configured to access the Sync Assurance application on the K8s cluster.
3. MNC and Sync Assurance application were running, and at some point some error was recognized, resulting in Sync Assurance
   application in a degraded state, or failing to run completely.
4. The mnc-ds-k8s tool is installed on the linux tooling machine at this location: /opt/adtran/mnc-ds-k8s.
5. Tooling machine has connectivity and access to the k8s cluster where the Sync Assurance application is deployed.

## Recover from database failures
**General note on restoring database from physical backup:**

Delivered solution provides a feature of automatic physical backup of the database to S3 object storage repository.
With this feature enabled, physical copy of the database is created according to user defined schedule.
Integral part of this solution is the continuous archiving of WAL files. At all times, database maintains a write ahead log (WAL) in
a form of series of WAL files (segments). The log records every change made to the database's data files. Since db backup
is carried out on a running database, data can be modified while the backup is in progress, as the collection of GNSS/PTP information is not interrupted.
When database is restored from backup, backed-up WAL files are replayed to bring the system to a current state. 
In order to successfully restore the database from backup, at least a
minimum set of archived WAL segments is required, representing the changes made to database at the time backup was in progress.
These files are mandatory in order for the database to be able to reach consistent state after the restoration. Whenever
considering restoring database backup, it is recommended to check for existence of these WAL archives to prevent the operation from failure. 
In order to determine which files are required, if any of the database pods is running, execute following command:
- GNSS:
    ```bash
    kubectl exec -n mnc-synca-gnss timescaledb-0 -c timescaledb -- pgbackrest info
    ```
- TPA:
    ```bash
    kubectl exec -n mnc-synca-tpa timescaledb-0 -c timescaledb -- pgbackrest info
    ```
where `timescaledb-0` is the name of the running pod. The output should be similar to:
```bash
stanza: poddb
    status: ok
    cipher: none

    db (current)
        wal archive min/max (14): 000000170000000900000002/000000550000000F000000C8

        full backup: 20240513-000005F
            timestamp start/stop: 2024-05-13 00:00:05+00 / 2024-05-13 00:00:39+00
            wal start/stop: 000000170000000900000002 / 000000170000000900000002
            database size: 2.3GB, database backup size: 2.3GB
            repo1: backup set size: 398.1MB, backup size: 398.1MB

        full backup: 20240514-000004F
            timestamp start/stop: 2024-05-14 00:00:04+00 / 2024-05-14 00:00:44+00
            wal start/stop: 0000003F0000000C00000032 / 0000003F0000000C00000035
            database size: 3.1GB, database backup size: 3.1GB
            repo1: backup set size: 560.2MB, backup size: 560.2MB

        full backup: 20240515-000006F
            timestamp start/stop: 2024-05-15 00:00:06+00 / 2024-05-15 00:00:57+00
            wal start/stop: 0000004F0000000F000000D7 / 0000004F0000000F000000DA
            database size: 4.4GB, database backup size: 4.4GB
            repo1: backup set size: 789.4MB, backup size: 789.4MB
```
Look for `wal start/stop` entry in the command output.
For `20240515-000006F` backup, mandatory WAL segments are `0000004F0000000F000000D7` to `0000004F0000000F000000DA`.

In case no database pod is running, inspection of the backup manifest file is required inside the minio object store.
Refer to the section *inspecting files inside minio object store*.
`backup.manifest` file located in directory of verified backup in the S3 backup repository, for example: 
`mnc-synca-gnss-ts-backup/backup/poddb/20240515-000006F`, where `mnc-synca-gnss-ts-backup` is the bucket name and 
`20240515-000006F` refers to the physical backup in question.

Inside this file look for `[backup]` section. It should look
similar to the example below:
```bash
[backup]
backup-archive-start="0000004F0000000F000000D7"
backup-archive-stop="0000004F0000000F000000DA"
```
In the example above WAL archives `0000004F0000000F000000D7` to `0000004F0000000F000000DA` are mandatory for the `20240515-000006F`
backup file to be applicable for the database restore operation. If any of these files is missing, this particular backup 
should not be considered valid. An attempt to use it is going to fail. To check whether the WAL files are present, go to   
`mnc-synca-gnss-ts-backup/archive/poddb/14-1` directory in the repository and in `0000004F0000000F` subdirectory look for 
files/subdirectories starting with `0000004F0000000F000000D7-`. Files should look similar to the example below:
```bash
0000004F0000000F000000D7-aacc94f5f9468f0a7a9a7822a1a657ab48ea18d9.lz4
0000004F0000000F000000D7.0001AC68.backup
0000004F0000000F000000D8-79012eb6c8f13ead90bfb259b668bedf54d6099f.lz4
0000004F0000000F000000D9-9b53a2dcbbf52a2d1ac8460fba1101aa9e477257.lz4
0000004F0000000F000000DA-695814c6d8e19992ae16a80c0fc50ac370e16032.lz4
```
Ignore the files with the `backup` extension, as they hold metadata related to the physical backup, not the archived WAL segment.

Do not proceed with this recovery option in case any of these files is missing.

**Understanding which physical backup is going to be used in the context of Point in time recovery**

In case Point in time recovery is required, it is important to know which physical backup it is going to use. 
In order to determine this, if any of the database pods is running, execute following command:
- GNSS:
    ```bash
    kubectl exec -n mnc-synca-gnss timescaledb-0 -c timescaledb -- pgbackrest info
    ```
- TPA:
    ```bash
    kubectl exec -n mnc-synca-tpa timescaledb-0 -c timescaledb -- pgbackrest info
    ```
where `timescaledb-0` is the name of the running pod. The output should be similar to:
```bash
stanza: poddb
    status: ok
    cipher: none

    db (current)
        wal archive min/max (14): 000000170000000900000002/000000550000000F000000C8

        full backup: 20240513-000005F
            timestamp start/stop: 2024-05-13 00:00:05+00 / 2024-05-13 00:00:39+00
            wal start/stop: 000000170000000900000002 / 000000170000000900000002
            database size: 2.3GB, database backup size: 2.3GB
            repo1: backup set size: 398.1MB, backup size: 398.1MB

        full backup: 20240514-000004F
            timestamp start/stop: 2024-05-14 00:00:04+00 / 2024-05-14 00:00:44+00
            wal start/stop: 0000003F0000000C00000032 / 0000003F0000000C00000035
            database size: 3.1GB, database backup size: 3.1GB
            repo1: backup set size: 560.2MB, backup size: 560.2MB

        full backup: 20240515-000006F
            timestamp start/stop: 2024-05-15 00:00:06+00 / 2024-05-15 00:40:57+00
            wal start/stop: 0000004F0000000F000000D7 / 0000004F0000000F000000DA
            database size: 4.4GB, database backup size: 4.4GB
            repo1: backup set size: 789.4MB, backup size: 789.4MB
```
Look for the `timestamp start/stop` entries in the output of the command. The backup that is going to be used is the last one
which stop time is **before (not equal)** the requested point in time for recovery.

In case there is no database pod alive, list the contents of `<bucket-name>/backup/poddb` of the S3 repository, where `mnc-synca-gnss-ts-backup`
or `mnc-synca-tpa-ts-backup` is the default bucket name for GNSS and PTP Assurance application respectively.
Refer to the section *inspecting files inside minio object store*.
Inside this directory several subdirectories can be located, each of them dedicated for one physical backup, for example:
```bash
20240513-000005F
20240514-000004F
20240515-000006F
```
Each directory name refers to the **start** date-time of the backup in UTC time zone. If, for example we want to restore
data to 2024-05-15 00:30:00 UTC, we should start from inspecting first backup prior to this time, which in this example is
in `20240515-000006F` directory. From that directory, open `backup.manifest` file and find `backup-archive-start` in the 
`[backup]` section:
```bash
[backup]
backup-archive-start="0000004F0000000F000000D7"
```
Go to `<bucket-name>/archive/poddb/14-1` directory in the repository and in `0000004F0000000F` subdirectory look for
file of `backup` type, starting with `0000004F0000000F000000D7`:
```bash
0000004F0000000F000000D7.0001AC68.backup
```
This file should look similar to the example below:
```bash
START WAL LOCATION: F/D701AC68 (file 0000004F0000000F000000D7)
STOP WAL LOCATION: F/DABE9AA8 (file 0000004F0000000F000000DA)
CHECKPOINT LOCATION: F/D7330CE8
BACKUP METHOD: streamed
BACKUP FROM: primary
START TIME: 2024-05-15 00:00:07 UTC
LABEL: pgBackRest backup started at 2024-05-15 00:00:06.653258+00
START TIMELINE: 79
STOP TIME: 2024-05-15 00:40:57 UTC
STOP TIMELINE: 79
```
Look for the `STOP TIME` entry in this file. In case we want to restore the database to the point in time **later (not equal)** than
the `STOP TIME`, then this physical backup is the one that is going to be used to carry out the restore operation. If not,
the previous one should be inspected. In this example we should continue with checking the previous backup, because
desired point in time 2024-05-15 00:30:00 UTC is before 2024-05-15 00:40:57 UTC which is the time `20240515-000006F` 
backup was completed.

**Stopping Sync Assurance application in case database failed to start after restore**

In case database restore is unsuccessful, usually next logical step is to find out the cause and retry the procedure 
after resolving the issue. Before uninstalling current deployment of affected Sync Assurance application, *stop* command should be used 
(refer to *lifeCycleOperations.md*).
- in case GNSS Assurance application is affected
   ```bash
   ./mnc-ds-ctl stop gnss -w
   ```
- in case PTP Assurance application is affected
   ```bash
   ./mnc-ds-ctl stop tpa -w
   ```
However, since *timescaledb-passwd-set-job* is waiting for the database to become available and is using a long timeout,
it is often convenient to delete this job manually. After executing the stop command, open second terminal to the 
Linux tooling machine and execute the command there:
- to get GNSS *timescaledb-passwd-set-job*
   ```bash
   kubectl -n mnc-synca-gnss get jobs.batch
   ```
- to get TPA *timescaledb-passwd-set-job*
   ```bash
   kubectl -n mnc-synca-tpa get jobs.batch
   ```
It should return an output similar to:
   ```bash
NAME                           COMPLETIONS   DURATION   AGE
(...)
timescaledb-passwd-set-job-1   0/1           5m48s      5m48s
(...)
   ```
Note down full job name and delete it using the command:
- GNSS
   ```bash
   kubectl delete -n mnc-synca-gnss jobs.batch timescaledb-passwd-set-job-1
   ```
- TPA
   ```bash
   kubectl delete -n mnc-synca-tpa jobs.batch timescaledb-passwd-set-job-1
   ```
Now the *timescaledb-passwd-set-job* will no longer delay *stop* command completion.

**Inspecting files inside minio object store**

Objects inside Minio S3 object store can be inspected with the help of minio console or minio cli client. In order to use the
browser, add the "yaml" section to the mnc-ds-ct.yml file:
```yaml
(...)
    minio:
      values:
        config:
          minio.auth.rootPassword: ChgMeNOW
          minio.mode: distributed
          minio.provisioning.enabled: "true"
          minio.statefulset.drivesPerNode: "2"
          minio.statefulset.zones: "1"
        yaml:
          minio:
            service:
              type: NodePort
              nodePorts:
                console: "30811"
      deployments:
        - name: minioBitnami
          enabled: true
          image:
            repository: enc/common_3rd-party/bitnami-minio
            tag: 2024.2.26-debian-12-r0
          replicas: 3
      storage:
        useLocalPV: true
```
Save the file and redeploy mnc-common:
```bash
./mnc-ds-ctl deploy mnc-common
```
Enter following address in a web browser to open the minio console:
```
https://<IP address of any of the cluster nodes>:30811
```
Log in with the user "admin" and password defined in the mnc-ds-ct.yml file.
Use the Object Browser to browse the stored objects. Objects like backup manifests can be downloaded and inspected locally.

Note: some browsers can block the download as suspicious. At the time of writing this document, the most convenient browser in this regard was Chrome.

In order to use the minio cli client to browse and inspect the objects, follow the steps below:

Open shell on the kubernetes cluster tooling machine. Exec into the minio container:
```bash
kubectl exec -it -n mnc-common minio-0 -- bash
```
add an alias for the used minio configuration:
```bash
mc alias set minio https://localhost:9000 admin <minio root password> --insecure
```
Use the mc cli client to view the stored bucket names:
```bash
mc ls minio --insecure
```
Use the mc cli client to list files:
```bash
mc ls minio/<bucket-name>/backup/poddb --insecure
mc ls minio/<bucket-name>/archive/poddb/14-1 --insecure
```
Use the client to inspect file contents:
```bash
mc cat minio/<bucket-name>/backup/poddb/<backup-name>/backup.manifest --insecure
```

### Database replica failure
1. Login to the linux tooling machine
2. Identify the name of the pod with the broken db replica
3. Find out which pod is used for the database master for relevant Sync Assurance application:
   ```bash
   # gnss
   kubectl get pod -n mnc-synca-gnss -L role
   # tpa
   kubectl get pod -n mnc-synca-tpa -L role
   
    NAME            READY   STATUS      RESTARTS   AGE     ROLE
    ...
    timescaledb-0   2/2     Running     0          6m33s   replica
    timescaledb-1   2/2     Running     0          8m18s   master
    timescaledb-2   2/2     Running     0          6m56s   replica
    ...
   ```
   Look at the "role" label to determine the db master pod.
4. In case db master is using timescaledb-1 pod and replica using timescaledb-0 pod is broken or
   db master is using timescaledb-2 and both replicas are broken, use the *Timescaledb Master database pod fails* procedure
5. Change dir to the mnc-ds-k8s directory:
    ```bash
    cd /opt/adtran/mnc-ds-k8s
    ```
6. Open the mnc-ds-ctl.yml file in your favorite editor:
    ```bash
    vim mnc-ds-ctl.yml
    ``` 
7. Decrease the number of Timescale database replica to shut down the pod with broken db replica.
   When scaling down the stateful set, Kubernetes shuts down pods with the highest ordinals in order to meet
   the selected replica count. For instance in case timescaledb-2 pod contains the broken database replica,
   setting the replica count to 2 is going to shut down timescaledb-2 pod:
    ```yaml
      timescale:
        deployments:
          - name: "timescaleDbHa"
            enabled: true
            ...
            replicas: 2        
    ```
8. Deploy relevant Sync Assurance application:
    ```bash
   # gnss
    ./mnc-ds-ctl deploy gnss
   # tpa
    ./mnc-ds-ctl deploy tpa
    ```
9. Confirm that the pod with failed db replica of affected Sync Assurance application has been stopped:
    ```bash
   # gnss 
   kubectl get pod -n mnc-synca-gnss -L role
   # tpa
   kubectl get pod -n mnc-synca-tpa -L role
   
    NAME            READY   STATUS      RESTARTS   AGE   ROLE
    ...
    timescaledb-0   2/2     Running     0          30m   replica
    timescaledb-1   2/2     Running     0          32m   master
    ...
    ```
10. Fix the specific node problem (replace hardware, fix disc, etc..) if applicable
11. Clear the affected Sync Assurance application's Timescaledb database volumes contents (data and wal directory of the affected database replica).
    Volumes are handled by the cluster administrator or the team which is responsible to handle volumes per deployed application.
12. Restore the original Timescaledb replica number in mnc-ds-ctl.yml file:
    ```yaml
      timescale:
        deployments:
          - name: "timescaleDbHa"
            enabled: true
            ...
            replicas: 3  
    ```
13. Deploy relevant Sync Assurance application:
    ```bash
    # gnss
    ./mnc-ds-ctl deploy gnss
    # tpa
    ./mnc-ds-ctl deploy tpa
    ```
14. Verify that all timescaledb replicas of relevant Sync Assurance application are up and running:
    ```bash
    # gnss
    kubectl -n mnc-synca-gnss get pod -L role
    # tpa
    kubectl -n mnc-synca-tpa get pod -L role
    ```
    You should expect an output similar to the following:
   ```bash
    NAME            READY   STATUS      RESTARTS   AGE     ROLE
    ...
    timescaledb-0   2/2     Running     0          6m33s   replica
    timescaledb-1   2/2     Running     0          8m18s   master
    timescaledb-2   2/2     Running     0          6m56s   replica
    ...
   ```
15. Verify if db replica is now working as expected

    Note: replica restoration can take several minutes depending on the db size. Logs are available after restoration is completed.

### Timescaledb Master database pod failure - default recovery option
Default recovery option uses the latest database backup together with archived WAL segments to restore the database to the
most recent point in time available in the archive (on the latest database timeline). Before starting the procedure verify
that the archive repository contains valid backup and at least the required archived WAL segments related to this backup
(refer to *General note on restoring database from physical backup*).
Do not proceed with this recovery option in case any of the required files is missing.

In order to apply the recovery, follow the steps:

1. Login to the linux tooling machine
2. Change dir to the mnc-ds-k8s directory:
    ```bash
    cd /opt/adtran/mnc-ds-k8s
    ```
3. Stop relevant Sync Assurance application (gracefully):
   ```bash
   # gnss
   ./mnc-ds-ctl stop gnss -w
   # tpa
   ./mnc-ds-ctl stop tpa -w
   ```
4. Uninstall relevant Sync Assurance application:
   ```bash
   # gnss
   ./mnc-ds-ctl uninstall gnss --delete-pvc -w
   # tpa
   ./mnc-ds-ctl uninstall tpa --delete-pvc -w
   ```
5. Fix the specific node problem (replace hardware, fix disc, etc.) if relevant
6. Clear the relevant Sync Assurance application's Timescaledb database volumes contents (data and wal directories of all database replicas).
   Volumes are handled by the cluster administrator or the team which is responsible to handle volumes per deployed application.

7. Clear the relevant Sync Assurance application's Kafka database volumes contents (config and data directories of all kafka replicas).
   Volumes are handled by the cluster administrator or the team which is responsible to handle volumes per deployed application.

8. Open the mnc-ds-ctl.yml file in your favorite editor:
    ```bash
    vim mnc-ds-ctl.yml
    ``` 
9. Configure relevant Sync Assurance application to run only single Timescale database replica with backup disabled and bootstrap from backup enabled.
   Disable all [gnss|tpa].apps.deployments, for example in case of GNSS Assurance application:
    ```yaml
    gnss:
      ...
      apps:
        ...
        deployments:
          - name: collector
            enabled: false
          ...
          - name: dataAccess
              enabled: false
          ...
          - name: machineLearning
              enabled: false
          ...
          - name: rcaAnalysis
              enabled: false
          ...
          - name: cliWorker
              enabled: false
      timescale:
        values:
          config:
            timescaledb.backup.enabled: "false"
            ...
            timescaledb.bootstrapFromBackup.enabled: "true"
            ...
        ...
        deployments:
          - name: "timescaleDbHa"
            enabled: true
            ...
            replicas: 1        
      kafka:
        ...
        deployments:
          - name: "kafkaBitnami"
            enabled: true
            ...                  
    ```
10. Deploy relevant Sync Assurance application:
    ```bash
    # gnss
    ./mnc-ds-ctl deploy gnss
    # tpa
    ./mnc-ds-ctl deploy tpa
    ``` 
11. Wait until relevant Sync Assurance application's database recovery is complete. To verify this, you can check the status of *timescaledb-passwd-set-job-x* job
    ```bash
    # gnss
    kubectl get -n mnc-synca-gnss jobs.batch
    # tpa
    kubectl get -n mnc-synca-tpa jobs.batch
    
    NAME                           COMPLETIONS   DURATION   AGE
    timescaledb-passwd-set-job-1   1/1           8s         13m
    ```
   Verify the *COMPLETIONS* field - once the process is completed, it is going to indicate "1/1".
   
   Note: In case some of the WAL files are missing in the archive, data is going to be recovered only to the point in time
   where such gap starts. The recovery will fail completely if mandatory WAL files created during the backup was in progress 
   are missing in the archive (refer to *General note on restoring database from physical backup*). In such case you can try
   using Point in Time Recovery to recover database to a point in time before archiving failure occurred.

12. Open the mnc-ds-ctl.yml file in your favorite editor:
    ```bash
    vim mnc-ds-ctl.yml
    ``` 
    
13. Restore original configuration of relevant Sync Assurance application, for example in case of GNSS Assurance application:
    ```yaml
    gnss:
      ...
      apps:
        ...
        deployments:
          - name: collector
            enabled: true
          ...
          - name: dataAccess
              enabled: true
          ...
          - name: machineLearning
              enabled: true
          ...
          - name: rcaAnalysis
              enabled: true
          ...
          - name: cliWorker
              enabled: <false | true>
      timescale:
        values:
          config:
            timescaledb.backup.enabled: "true"
            ...
            timescaledb.bootstrapFromBackup.enabled: "false"
            ...
        ...
        deployments:
          - name: "timescaleDbHa"
            enabled: true
            ...
            replicas: 3        
      kafka:
        ...
        deployments:
          - name: "kafkaBitnami"
            enabled: true
            ...                  
    ```
14. Deploy relevant Sync Assurance application:
    ```bash
    # gnss
    ./mnc-ds-ctl deploy gnss
    # tpa
    ./mnc-ds-ctl deploy tpa
    ``` 
15. Verify that Sync Assurance application is up and running:
    ```bash
    # in case GNSS Assurance application was restored:
    kubectl -n mnc-synca-gnss get pod -L role
    # in case PTP Assurance application was restored:
    kubectl -n mnc-synca-tpa get pod -L role
    ```
    For example, in case of GNSS Assurance application, you should expect an output similar to the following:
    ```bash
    NAME                                           READY   STATUS             RESTARTS   AGE   ROLE
    kafka-controller-0                             1/1     Running            0          2m
    kafka-controller-1                             1/1     Running            0          2m
    kafka-controller-2                             1/1     Running            0          2m    
    synca-gnss-cli-worker-7c98c9d59c-5dk4r         1/1     Running            0          2m
    synca-gnss-collector-7d89475d48-52hcw          1/1     Running            0          2m
    synca-gnss-data-access-566b9f598b-wfpv4        1/1     Running            0          2m
    synca-gnss-machine-learning-8c57c9d59c-5dk44s  1/1     Running            0          2m
    synca-gnss-rca-analysis-7g57c9d59c-wfk44s      1/1     Running            0          2m
    timescaledb-0                                  1/1     Running            0          2m   replica
    timescaledb-1                                  1/1     Running            0          2m   master
    timescaledb-2                                  1/1     Running            0          2m   replica
    ```
16. Verify that no errors are found on the restored Sync Assurance application's timescaledb, kafka, and synca-gnss / synca-tpa pods.
17. Reconnect to the Sync Assurance application from MNC Sync Director client and verify that the correct data exist based on the used database backup file.


### Timescaledb Master database failure - Point in Time recovery
In case default recovery option is failing due to a problem with archived data, Point in Time recovery can be used. 
With this recovery method, it is possible to restore the database to a state in which it was at a certain point in time in the past.
It can be used when there is a data corruption or data loss at some point in time that we know about. In such case we may want to restore the
database only to the point before this problem happened.

If the desired point in time is not exactly known, it is possible
to perform a series of point in time recoveries to find out to which point it is best to restore the data. However, if
such procedure is to be applied, it is strongly recommended to start with the most recent point in time we want to check
and go back in time with each consecutive attempt. Each time Point in Time recovery is used, it creates new database timeline
which is then considered the "latest" and used in the consecutive restore attempts by default. It doesn't contain the data
past the selected point in time anymore. Consecutive Point in Time recovery attempts going forward in time will only succeed when the
timeline containing the missing data is going to be specified explicitly (refer to *Restoring database using selected timeline ID*). 

Before starting, it is recommended to confirm that the archive repository contains backup file prior to specified date 
(refer to *Understanding which physical backup is going to be used in the context of Point in time recovery*) as well as
WAL files between the backup time (check wal start in the output of pgbackrest info command or `backup-archive-start` 
entry in the `backup.manifest`) and restoration point in time.

In order to apply the recovery, follow
the steps:

1. Login to the linux tooling machine
2. Change dir to the mnc-ds-k8s directory:
    ```bash
    cd /opt/adtran/mnc-ds-k8s
    ```
3. Stop relevant Sync Assurance application (gracefully):
   ```bash
   # gnss
   ./mnc-ds-ctl stop gnss -w
   # tpa
   ./mnc-ds-ctl stop tpa -w
   ```
4. Uninstall relevant Sync Assurance application:
   ```bash
   # gnss
   ./mnc-ds-ctl uninstall gnss --delete-pvc -w
   # tpa
   ./mnc-ds-ctl uninstall tpa --delete-pvc -w
   ```
5. Fix the specific node problem (replace hardware, fix disc, etc.) if relevant
6. Clear the relevant Sync Assurance application's Timescaledb database volumes contents (data and wal directories of all database replicas).
   Volumes are handled by the cluster administrator or the team which is responsible to handle volumes per deployed application.

7. Clear the relevant Sync Assurance application's Kafka database volumes contents (config and data directories of all kafka replicas).
   Volumes are handled by the cluster administrator or the team which is responsible to handle volumes per deployed application.

8. Open the mnc-ds-ctl.yml file in your favorite editor:
    ```bash
    vim mnc-ds-ctl.yml
    ``` 
9. Configure relevant Sync Assurance application to run only single Timescale database replica with backup disabled and bootstrap from backup enabled.
   Specify desired point in time and disable all [gnss|tpa].apps.deployments, for example in case of GNSS Assurance application:
    ```yaml
    gnss:
      ...
      apps:
        ...
        deployments:
          - name: collector
            enabled: false
          ...
          - name: dataAccess
              enabled: false
          ...
          - name: machineLearning
              enabled: false
          ...
          - name: rcaAnalysis
              enabled: false
          ...
          - name: cliWorker
              enabled: false
      timescale:
        values:
          config:
            timescaledb.backup.enabled: "false"
            ...
            timescaledb.bootstrapFromBackup.enabled: "true"
            timescaledb.bootstrapFromBackup.restore-point-in-time: "2024-03-16 18:00:03"
            timescaledb.bootstrapFromBackup.restore-point-in-time-timeline: latest
            ...
        ...
        deployments:
          - name: "timescaleDbHa"
            enabled: true
            ...
            replicas: 1        
      kafka:
        ...
        deployments:
          - name: "kafkaBitnami"
            enabled: true
            ...                  
    ```
   
   Note: *restore-point-in-time* should contain a valid date time string in this format: yyyy-MM-dd HH:mm:ss in UTC time zone.

10. Deploy relevant Sync Assurance application:
    ```bash
    # gnss
    ./mnc-ds-ctl deploy gnss
    # tpa
    ./mnc-ds-ctl deploy tpa
    ``` 
11. Wait until relevant Sync Assurance application's database recovery is complete. To verify this, you can check the status of *timescaledb-passwd-set-job-x* job
    ```bash
    # gnss
    kubectl get -n mnc-synca-gnss jobs.batch
    # tpa
    kubectl get -n mnc-synca-tpa jobs.batch
    
    NAME                           COMPLETIONS   DURATION   AGE
    timescaledb-passwd-set-job-1   1/1           8s         13m
    ```
    Verify the *COMPLETIONS* field, once the database is ready it is going to be "1/1"
        
    Note: 
    In case backup file prior to specified time or some of the WAL files between the `backup-archive-start` 
    entry in the `backup.manifest` file and the point in time specified are missing in the archive, recovery will fail.
    There is no workaround for the first case - it is not possible recover to point in time before the first backup stored 
    in the repository. In the second case, try choosing earlier point in time for the recovery.

12. Open the mnc-ds-ctl.yml file in your favorite editor:
    ```bash
    vim mnc-ds-ctl.yml
    ``` 

13. Restore original configuration of relevant Sync Assurance application, for example in case of GNSS Assurance application:
    ```yaml
    gnss:
      ...
      apps:
        ...
        deployments:
          - name: collector
            enabled: true
          ...
          - name: dataAccess
              enabled: true
          ...
          - name: machineLearning
              enabled: true
          ...
          - name: rcaAnalysis
              enabled: true
          ...
          - name: cliWorker
              enabled: <false | true>
      timescale:
        values:
          config:
            timescaledb.backup.enabled: "true"
            ...
            timescaledb.bootstrapFromBackup.enabled: "false"
            timescaledb.bootstrapFromBackup.restore-point-in-time: ""
            timescaledb.bootstrapFromBackup.restore-point-in-time-timeline: latest
            ...
        ...
        deployments:
          - name: "timescaleDbHa"
            enabled: true
            ...
            replicas: 3        
      kafka:
        ...
        deployments:
          - name: "kafkaBitnami"
            enabled: true
            ...                  
    ```
14. Deploy relevant Sync Assurance application:
    ```bash
    # gnss
    ./mnc-ds-ctl deploy gnss
    # tpa
    ./mnc-ds-ctl deploy tpa
    ``` 
15. Verify that Sync Assurance application is up and running:
    ```bash
    # in case GNSS Assurance application was restored:
    kubectl -n mnc-synca-gnss get pod -L role
    # in case PTP Assurance application was restored:
    kubectl -n mnc-synca-tpa get pod -L role
    ```
    For example, in case of GNSS Assurance application, you should expect an output similar to the following:
    ```bash
    NAME                                           READY   STATUS             RESTARTS   AGE   ROLE
    kafka-controller-0                             1/1     Running            0          2m
    kafka-controller-1                             1/1     Running            0          2m
    kafka-controller-2                             1/1     Running            0          2m    
    synca-gnss-cli-worker-7c98c9d59c-5dk4r         1/1     Running            0          2m
    synca-gnss-collector-7d89475d48-52hcw          1/1     Running            0          2m
    synca-gnss-data-access-566b9f598b-wfpv4        1/1     Running            0          2m
    synca-gnss-machine-learning-8c57c9d59c-5dk44s  1/1     Running            0          2m
    synca-gnss-rca-analysis-7g57c9d59c-wfk44s      1/1     Running            0          2m
    timescaledb-0                                  1/1     Running            0          2m   replica
    timescaledb-1                                  1/1     Running            0          2m   master
    timescaledb-2                                  1/1     Running            0          2m   replica
    ```
16. Verify that no errors are found on the restored Sync Assurance application's timescaledb, kafka, and synca-gnss / synca-tpa pods.
17. Reconnect to the Sync Assurance application from MNC Sync Director client and verify that the correct data exist based on the used database backup file.

#### Restoring database using selected timeline ID (expert mode)

*Caution: Please use this procedure only if you have strong understanding of how timescaledb is using timelines and you know which one to use.*

Sync Assurance applications' databases allow using explicitly selected database timeline to carry out the restoration. Refer to https://www.postgresql.org/docs/14/continuous-archiving.html#BACKUP-TIMELINES
to understand the concept of database timelines. Timeline history files can be found in the S3 object storage used for
backup and archiving of the database, inside `<bucket-name>/archive/poddb/14-1`, where `mnc-synca-gnss-ts-backup`
 or `mnc-synca-tpa-ts-backup` is the default bucket name for GNSS and PTP Assurance application respectively. 
These files are named `<8 digit hex Timeleine ID>.history`, like `00000054.history`. They are created 
when the timeline is created and contain complete history about which timeline it branched off from and when.
Refer to the section *inspecting files inside minio object store*.

In order to apply the recovery, follow
the steps:

1. Login to the linux tooling machine
2. Change dir to the mnc-ds-k8s directory:
    ```bash
    cd /opt/adtran/mnc-ds-k8s
    ```
3. Stop relevant Sync Assurance application (gracefully):
   ```bash
   # gnss
   ./mnc-ds-ctl stop gnss -w
   # tpa
   ./mnc-ds-ctl stop tpa -w
   ```
4. Uninstall relevant Sync Assurance application:
   ```bash
   # gnss
   ./mnc-ds-ctl uninstall gnss --delete-pvc -w
   # tpa
   ./mnc-ds-ctl uninstall tpa --delete-pvc -w
   ```
5. Fix the specific node problem (replace hardware, fix disc, etc.) if relevant
6. Clear the relevant Sync Assurance application's Timescaledb database volumes contents (data and wal directories of all database replicas).
   Volumes are handled by the cluster administrator or the team which is responsible to handle volumes per deployed application.

7. Clear the relevant Sync Assurance application's Kafka database volumes contents (config and data directories of all kafka replicas).
   Volumes are handled by the cluster administrator or the team which is responsible to handle volumes per deployed application.

8. Open the mnc-ds-ctl.yml file in your favorite editor:
    ```bash
    vim mnc-ds-ctl.yml
    ``` 
9. Configure relevant Sync Assurance application to run only single Timescale database replica with backup disabled and bootstrap from backup enabled.
   Specify desired point in time, database timeline ID in decimal notation and disable all [gnss|tpa].apps.deployments, 
   for example in case of GNSS Assurance application:
    ```yaml
    gnss:
      ...
      apps:
        ...
        deployments:
          - name: collector
            enabled: false
          ...
          - name: dataAccess
              enabled: false
          ...
          - name: machineLearning
              enabled: false
          ...
          - name: rcaAnalysis
              enabled: false
          ...
          - name: cliWorker
              enabled: false
      timescale:
        values:
          config:
            timescaledb.backup.enabled: "false"
            ...
            timescaledb.bootstrapFromBackup.enabled: "true"
            timescaledb.bootstrapFromBackup.restore-point-in-time: "2024-03-16 17:30:00"
            timescaledb.bootstrapFromBackup.restore-point-in-time-timeline: "80"
            ...
        ...
        deployments:
          - name: "timescaleDbHa"
            enabled: true
            ...
            replicas: 1        
      kafka:
        ...
        deployments:
          - name: "kafkaBitnami"
            enabled: true
            ...                  
    ```

   Note: *restore-point-in-time* should contain a valid date time string in this format: yyyy-MM-dd HH:mm:ss in UTC time zone.
   *restore-point-in-time-timeline* should contain the Timeline ID (in decimal notation) which we want to use in the recovery 
   and be enclosed in double brackets.

10. Deploy relevant Sync Assurance application:
    ```bash
    # gnss
    ./mnc-ds-ctl deploy gnss
    # tpa
    ./mnc-ds-ctl deploy tpa
    ``` 
11. Wait until relevant Sync Assurance application's database recovery is complete. To verify this, you can check the status of *timescaledb-passwd-set-job-x* job
    ```bash
    # gnss
    kubectl get -n mnc-synca-gnss jobs.batch
    # tpa
    kubectl get -n mnc-synca-tpa jobs.batch
    
    NAME                           COMPLETIONS   DURATION   AGE
    timescaledb-passwd-set-job-1   1/1           8s         13m
    ```
    Verify the *COMPLETIONS* field, once the database is ready it is going to be "1/1"

    Note:
    In case backup file prior to specified time or some of the WAL files between the backup start time and the point in time specified are missing, 
    recovery will fail. The recovery may also fail in case there is gap in the data related to the timeline path that 
    has to be traversed between the backup used and the selected timeline. You may try using *Restoring database using minimum set of WAL archives*
    as the last resort.

12. Open the mnc-ds-ctl.yml file in your favorite editor:
    ```bash
    vim mnc-ds-ctl.yml
    ``` 

13. Restore original configuration of relevant Sync Assurance application, for example in case of GNSS Assurance application:
    ```yaml
    gnss:
      ...
      apps:
        ...
        deployments:
          - name: collector
            enabled: true
          ...
          - name: dataAccess
              enabled: true
          ...
          - name: machineLearning
              enabled: true
          ...
          - name: rcaAnalysis
              enabled: true
          ...
          - name: cliWorker
              enabled: <false | true>
      timescale:
        values:
          config:
            timescaledb.backup.enabled: "true"
            ...
            timescaledb.bootstrapFromBackup.enabled: "false"
            timescaledb.bootstrapFromBackup.restore-point-in-time: ""
            timescaledb.bootstrapFromBackup.restore-point-in-time-timeline: latest
            ...
        ...
        deployments:
          - name: "timescaleDbHa"
            enabled: true
            ...
            replicas: 3        
      kafka:
        ...
        deployments:
          - name: "kafkaBitnami"
            enabled: true
            ...                  
    ```
14. Deploy relevant Sync Assurance application:
    ```bash
    # gnss
    ./mnc-ds-ctl deploy gnss
    # tpa
    ./mnc-ds-ctl deploy tpa
    ``` 
15. Verify that Sync Assurance application is up and running:
    ```bash
    # in case GNSS Assurance application was restored:
    kubectl -n mnc-synca-gnss get pod -L role
    # in case PTP Assurance application was restored:
    kubectl -n mnc-synca-tpa get pod -L role
    ```
    For example, in case of GNSS Assurance application, you should expect an output similar to the following:
    ```bash
    NAME                                           READY   STATUS             RESTARTS   AGE   ROLE
    kafka-controller-0                             1/1     Running            0          2m
    kafka-controller-1                             1/1     Running            0          2m
    kafka-controller-2                             1/1     Running            0          2m    
    synca-gnss-cli-worker-7c98c9d59c-5dk4r         1/1     Running            0          2m
    synca-gnss-collector-7d89475d48-52hcw          1/1     Running            0          2m
    synca-gnss-data-access-566b9f598b-wfpv4        1/1     Running            0          2m
    synca-gnss-machine-learning-8c57c9d59c-5dk44s  1/1     Running            0          2m
    synca-gnss-rca-analysis-7g57c9d59c-wfk44s      1/1     Running            0          2m
    timescaledb-0                                  1/1     Running            0          2m   replica
    timescaledb-1                                  1/1     Running            0          2m   master
    timescaledb-2                                  1/1     Running            0          2m   replica
    ```
16. Verify that no errors are found on the restored Sync Assurance application's timescaledb, kafka, and synca-gnss / synca-tpa pods.
17. Reconnect to the Sync Assurance application from MNC Sync Director client and verify that the correct data exist based on the used database backup file.

#### Restoring database using minimum set of WAL archives
This procedure can be applied in case the regular recovery is failing and we want the avoid a trial and error 
approach described in *Timescaledb Master database failure - Point in Time recovery*, but also when this method 
or *Restoring database using selected timeline ID* is failing.
The principle here is that the recovery will only use the physical backup and the minimum set of WAL files that were created
when backup was in progress. It is also going to use the same timeline that was current when the base backup was taken.
It is going to restore the data only to the point in time when the backup was completed.

In order to apply the recovery, follow the steps:

1. Login to the linux tooling machine
2. Change dir to the mnc-ds-k8s directory:
    ```bash
    cd /opt/adtran/mnc-ds-k8s
    ```
3. Stop relevant Sync Assurance application (gracefully):
   ```bash
   # gnss
   ./mnc-ds-ctl stop gnss -w
   # tpa
   ./mnc-ds-ctl stop tpa -w
   ```
4. Uninstall relevant Sync Assurance application:
   ```bash
   # gnss
   ./mnc-ds-ctl uninstall gnss --delete-pvc -w
   # tpa
   ./mnc-ds-ctl uninstall tpa --delete-pvc -w
   ```
5. Fix the specific node problem (replace hardware, fix disc, etc.) if relevant
6. Clear the relevant Sync Assurance application's Timescaledb database volumes contents (data and wal directories of all database replicas).
   Volumes are handled by the cluster administrator or the team which is responsible to handle volumes per deployed application.

7. Clear the relevant Sync Assurance application's Kafka database volumes contents (config and data directories of all kafka replicas).
   Volumes are handled by the cluster administrator or the team which is responsible to handle volumes per deployed application.

8. Open the mnc-ds-ctl.yml file in your favorite editor:
    ```bash
    vim mnc-ds-ctl.yml
    ``` 
9. Configure relevant Sync Assurance application to run only single Timescale database replica with backup disabled and bootstrap from backup enabled.
   Disable all [gnss|tpa].apps.deployments.
   Find out the stop time of the selected backup (refer to *General note on restoring database from physical backup:* -
   it is either `timestamp stop` from the command output or `STOP TIME` from the `backup` file). For instance, it can be
   equal to `2024-05-14 00:00:44+00`.
   Add 1 second to this value and put it in the `mnc-ds-ctl.yml` file. In this example it would be `2024-05-14 00:00:45`. 
   Define `restore-point-in-time-timeline` as "current", for example in case of GNSS Assurance application:
    ```yaml
    gnss:
      ...
      apps:
        ...
        deployments:
          - name: collector
            enabled: false
          ...
          - name: dataAccess
              enabled: false
          ...
          - name: machineLearning
              enabled: false
          ...
          - name: rcaAnalysis
              enabled: false
          ...
          - name: cliWorker
              enabled: false
      timescale:
        values:
          config:
            timescaledb.backup.enabled: "false"
            ...
            timescaledb.bootstrapFromBackup.enabled: "true"
            timescaledb.bootstrapFromBackup.restore-point-in-time: "2024-05-14 00:00:45"
            timescaledb.bootstrapFromBackup.restore-point-in-time-timeline: "current"
            ...
        ...
        deployments:
          - name: "timescaleDbHa"
            enabled: true
            ...
            replicas: 1        
      kafka:
        ...
        deployments:
          - name: "kafkaBitnami"
            enabled: true
            ...                  
    ```

10. Deploy relevant Sync Assurance application:
    ```bash
    # gnss
    ./mnc-ds-ctl deploy gnss
    # tpa
    ./mnc-ds-ctl deploy tpa
    ``` 
11. Wait until relevant Sync Assurance application's database recovery is complete. To verify this, you can check the status of *timescaledb-passwd-set-job-x* job
    ```bash
    # gnss
    kubectl get -n mnc-synca-gnss jobs.batch
    # tpa
    kubectl get -n mnc-synca-tpa jobs.batch
    
    NAME                           COMPLETIONS   DURATION   AGE
    timescaledb-passwd-set-job-1   1/1           8s         13m
    ```
    Verify the *COMPLETIONS* field, once the database is ready it is going to be "1/1"

    Note:
    The recovery will fail if mandatory WAL files created during the backup was in progress
    are missing in the archive (refer to *General note on restoring database from physical backup*).
    In such case you can try this procedure with different base backup or Point in Time Recovery that is going to use it
    (refer to *Understanding which physical backup is going to be used in the context of Point in time recovery*).

12. Open the mnc-ds-ctl.yml file in your favorite editor:
    ```bash
    vim mnc-ds-ctl.yml
    ``` 

13. Restore original configuration of relevant Sync Assurance application, for example in case of GNSS Assurance application:
    ```yaml
    gnss:
      ...
      apps:
        ...
        deployments:
          - name: collector
            enabled: true
          ...
          - name: dataAccess
              enabled: true
          ...
          - name: machineLearning
              enabled: true
          ...
          - name: rcaAnalysis
              enabled: true
          ...
          - name: cliWorker
              enabled: <false | true>
      timescale:
        values:
          config:
            timescaledb.backup.enabled: "true"
            ...
            timescaledb.bootstrapFromBackup.enabled: "false"
            timescaledb.bootstrapFromBackup.restore-point-in-time: ""
            timescaledb.bootstrapFromBackup.restore-point-in-time-timeline: latest
            ...
        ...
        deployments:
          - name: "timescaleDbHa"
            enabled: true
            ...
            replicas: 3        
      kafka:
        ...
        deployments:
          - name: "kafkaBitnami"
            enabled: true
            ...                  
    ```
14. Deploy relevant Sync Assurance application:
    ```bash
    # gnss
    ./mnc-ds-ctl deploy gnss
    # tpa
    ./mnc-ds-ctl deploy tpa
    ``` 
15. Verify that Sync Assurance application is up and running:
    ```bash
    # in case GNSS Assurance application was restored:
    kubectl -n mnc-synca-gnss get pod -L role
    # in case PTP Assurance application was restored:
    kubectl -n mnc-synca-tpa get pod -L role
    ```
    For example, in case of GNSS Assurance application, you should expect an output similar to the following:
    ```bash
    NAME                                           READY   STATUS             RESTARTS   AGE   ROLE
    kafka-controller-0                             1/1     Running            0          2m
    kafka-controller-1                             1/1     Running            0          2m
    kafka-controller-2                             1/1     Running            0          2m    
    synca-gnss-cli-worker-7c98c9d59c-5dk4r         1/1     Running            0          2m
    synca-gnss-collector-7d89475d48-52hcw          1/1     Running            0          2m
    synca-gnss-data-access-566b9f598b-wfpv4        1/1     Running            0          2m
    synca-gnss-machine-learning-8c57c9d59c-5dk44s  1/1     Running            0          2m
    synca-gnss-rca-analysis-7g57c9d59c-wfk44s      1/1     Running            0          2m
    timescaledb-0                                  1/1     Running            0          2m   replica
    timescaledb-1                                  1/1     Running            0          2m   master
    timescaledb-2                                  1/1     Running            0          2m   replica
    ```
16. Verify that no errors are found on the restored Sync Assurance application's timescaledb, kafka, and synca-gnss / synca-tpa pods.
17. Reconnect to the Sync Assurance application from MNC Sync Director client and verify that the correct data exist based on the used database backup file.


## Recover Sync Assurance application from a timescaledb database backup file (pg-dump)
This procedure can be used to recover the Sync Assurance application from a Timescale database backup file (result of pg_dump for same version of PostgreSQL).
It will actually apply (recover) the Timescale database backup on a clean Sync Assurance application deployment into the cluster.
This procedure can also be used on a clean Sync Assurance application deploy.
1. Login to the linux tooling machine.
2. Change dir to the mnc-ds-k8s directory:
   ```bash
   cd /opt/adtran/mnc-ds-k8s
   ```
3. Stop relevant Sync Assurance application (gracefully):
   ```bash
   # gnss
   ./mnc-ds-ctl stop gnss -w
   # tpa
   ./mnc-ds-ctl stop tpa -w
   ```
4. Uninstall relevant Sync Assurance application:
   ```bash
   # gnss
   ./mnc-ds-ctl uninstall gnss --delete-pvc -w
   # tpa
   ./mnc-ds-ctl uninstall tpa --delete-pvc -w
   ```
5. Clear the relevant Sync Assurance application's Timescaledb database volumes contents (data and wal directories of all database replicas).
   Volumes are handled by the cluster administrator or the team which is responsible to handle volumes per deployed application.
6. Delete relevant Sync Assurance application's Timescaledb database Local Persistent Volume - skip if not using Local Persistent Volumes:
   ```bash
   # gnss
   kubectl delete pv mnc-synca-gnss-ts-data-node1
   kubectl delete pv mnc-synca-gnss-ts-data-node2
   kubectl delete pv mnc-synca-gnss-ts-data-node3
   
   # tpa
   kubectl delete pv mnc-synca-tpa-ts-data-node1
   kubectl delete pv mnc-synca-tpa-ts-data-node2
   kubectl delete pv mnc-synca-tpa-ts-data-node3
   ```
7. Clear the relevant Sync Assurance application's Kafka database volumes contents (config and data directories of all kafka replicas).
   Volumes are handled by the cluster administrator or the team which is responsible to handle volumes per deployed application.
8. Delete relevant Sync Assurance application's Kafka database Local Persistent Volume - skip if not using Local Persistent Volumes:
   ```bash
   # gnss
   kubectl delete pv mnc-synca-gnss-kafka-data-node1
   kubectl delete pv mnc-synca-gnss-kafka-data-node2
   kubectl delete pv mnc-synca-gnss-kafka-data-node3
   
   # tpa
   kubectl delete pv mnc-synca-tpa-kafka-data-node1
   kubectl delete pv mnc-synca-tpa-kafka-data-node2
   kubectl delete pv mnc-synca-tpa-kafka-data-node3
   ```
9. Clear the contents of the Object Storage bucket for relevant Sync Assurance application's Timescaledb database - skip if using MinIO Object Storage service.
   This is where the automatic Sync Assurance timescaledb database backups are stored. 
   Before performing this step make sure this is what you really want to do, as this operation shall delete all your previous backups.  
   You will need to clear the relevant backup on the S3 compatible service that is used, via the service admin tools.  
10. Clear the contents of the MinIO Object Storage bucket for relevant Sync Assurance application's Timescaledb database - skip if using external Object Storage service.
    This is where the automatic Sync Assurance timescaledb database backups are stored.
    Before performing this step make sure this is what you really want to do, as this operation shall delete all your previous backups:
    ```bash
    cd /opt/adtran/mnc-ds-k8s/util
    # run the following script and follow the instructions if you want to clear gnss minio bucket 
    ./bucket_clear_minio_synca_gnss_ts.sh
    
    # run the following script and follow the instructions if you want to clear tpa minio bucket 
    ./bucket_clear_minio_synca_tpa_ts.sh
    ```
11. Change dir to the mnc-ds-k8s directory:
    ```bash
    cd /opt/adtran/mnc-ds-k8s
    ```
12. Open the mnc-ds-ctl.yml file in your favorite editor:
    ```bash
    vim mnc-ds-ctl.yml
    ``` 
13. Configure Sync Assurance application being restored to run only single Timescale database replica with backup disabled, for example in case of GNSS:
    ```yaml
    gnss:
      ...
      apps:
        ...
        deployments:
          - name: collector
            enabled: false
          ...
          - name: dataAccess
              enabled: false
          ...
          - name: machineLearning
              enabled: false
          ...
          - name: rcaAnalysis
              enabled: false
          ...
          - name: cliWorker
              enabled: false
      timescale:
        values:
          config:
            timescaledb.backup.enabled: "false"
            ...
        ...
        deployments:
          - name: "timescaleDbHa"
            enabled: true
            ...
            replicas: 1        
            ...                  
    ```
14. Save the mnc-ds-ctl.yml file after editing in previous step, and exit editor.
15. Deploy the relevant Sync Assurance application:
    ```bash
    # gnss
    ./mnc-ds-ctl deploy gnss
    
    # tpa
    ./mnc-ds-ctl deploy tpa
    ``` 
16. Make sure relevant timescaledb database pod is up and running as expected:
    ```bash
    # gnss
    kubectl -n mnc-synca-gnss get pod -L role
    
    # tpa
    kubectl -n mnc-synca-tpa get pod -L role
    ```
    You should expect an output similar to the following:
    ```bash
    NAME                             READY   STATUS    RESTARTS   AGE   ROLE   
    timescaledb-0                    1/1     Running   0          38s   master
    ```
17. Change dir to the mnc-ds-k8s util directory:
    ```bash
    cd /opt/adtran/mnc-ds-k8s/util
    ```
18. Execute the timescaledb database restore script, and follow the instructions to provide database password and the full path to the local database backup file:
    ```bash
    # if you want to restore GNSS database
    ./db_restore_pg_synca_gnss_ts.sh <full path to the GNSS Assurance Timescaledb backup file>
    
    # if you want to restore TPA database
    ./db_restore_pg_synca_tpa_ts.sh <full path to the PTP Assurance Timescaledb backup file>
    ```
    Wait for the successful completion of the script.
19. Confirm that intense database activity is no longer taking place in the log:
    ```bash
    # gnss
    kubectl logs --namespace mnc-synca-gnss --follow timescaledb-0

    # tpa
    kubectl logs --namespace mnc-synca-tpa --follow timescaledb-0
    ```
20. Make sure timescaledb database pod is up and running as expected:
    ```bash
    # gnss
    kubectl -n mnc-synca-gnss get pod -L role
    
    # tpa
    kubectl -n mnc-synca-tpa get pod -L role
    ```
    You should expect an output similar to the following:
    ```bash
    NAME                             READY   STATUS    RESTARTS   AGE     ROLE    
    timescaledb-0                    1/1     Running   0          2m12s   master
    ```
21. Change dir to the mnc-ds-k8s directory:
    ```bash
    cd /opt/adtran/mnc-ds-k8s
    ```
22. Stop relevant Sync Assurance application (gracefully):
   ```bash
   # gnss
   ./mnc-ds-ctl stop gnss -w

   # tpa
   ./mnc-ds-ctl stop tpa -w
   ```
23. Open the mnc-ds-ctl.yml file in your favorite editor:
    ```bash
    vim mnc-ds-ctl.yml
    ``` 
24. Configure all deployments of the restored Sync Assurance application to run with three replicas, and Timescale database backup enabled,
    using the relevant Object Storage access and secret keys (if not already set), for example in case of GNSS Assurance:
    ```yaml
    gnss:
      ...
      apps:
        ...
        deployments:
          - name: collector
            enabled: true
            replicas: 3  
          ...
          - name: dataAccess
            enabled: true
            replicas: 3  
          ...
          - name: machineLearning
            enabled: true
            replicas: 3  
          ...
          - name: rcaAnalysis
            enabled: true
            replicas: 3  
          ...
          - name: cliWorker
            enabled: true
            replicas: 3  
      timescale:
        values:
          config:
            timescaledb.backup.enabled: "true"            
            ...
            timescaledb.secrets.pgbackrest.PGBACKREST_REPO1_S3_KEY: "<Put_your_S3_user_access_key_here>"
            timescaledb.secrets.pgbackrest.PGBACKREST_REPO1_S3_KEY_SECRET: "<Put_your_S3_user_secret_key_here>"
        ...
        deployments:
          - name: "timescaleDbHa"
            enabled: true
            ...
            replicas: 3   
            ...
    ```
25. Save the mnc-ds-ctl.yml file after editing in previous step, and exit editor.
26. Deploy the relevant Sync Assurance application:
    ```bash
    # gnss
    ./mnc-ds-ctl deploy gnss
    
    # tpa
    ./mnc-ds-ctl deploy tpa
    ```    
27. Verify that Sync Assurance application is up and running:
    ```bash
    # in case GNSS Assurance application was restored:
    kubectl -n mnc-synca-gnss get pod -L role
    # in case PTP Assurance application was restored:
    kubectl -n mnc-synca-tpa get pod -L role
    ```
    For example, in case of GNSS Assurance application, you should expect an output similar to the following:
    ```bash
    NAME                                           READY   STATUS             RESTARTS   AGE   ROLE
    kafka-controller-0                             1/1     Running            0          2m
    kafka-controller-1                             1/1     Running            0          2m
    kafka-controller-2                             1/1     Running            0          2m    
    synca-gnss-cli-worker-7c98c9d59c-kfps7         1/1     Running            0          2m
    synca-gnss-cli-worker-7c98c9d59c-wde9r         1/1     Running            0          2m
    synca-gnss-cli-worker-7c98c9d59c-juk16         1/1     Running            0          2m
    synca-gnss-collector-5588dfd97d-2v7ft          1/1     Running            0          2m
    synca-gnss-collector-5588dfd97d-6cd8j          1/1     Running            0          2m
    synca-gnss-collector-5588dfd97d-52hcw          1/1     Running            0          2m
    synca-gnss-data-access-b54b97c77-wfpv4         1/1     Running            0          2m
    synca-gnss-data-access-b54b97c77-j9fx5         1/1     Running            0          2m
    synca-gnss-data-access-b54b97c77-kdgdp         1/1     Running            0          2m
    synca-gnss-machine-learning-77fffcbdb5-5dk44s  1/1     Running            0          2m
    synca-gnss-machine-learning-77fffcbdb5-bp9sn   1/1     Running            0          2m
    synca-gnss-machine-learning-77fffcbdb5-nv87c   1/1     Running            0          2m
    synca-gnss-rca-analysis-765d6c8cf6-wfk44s      1/1     Running            0          2m
    synca-gnss-rca-analysis-765d6c8cf6-2m8fp       1/1     Running            0          2m
    synca-gnss-rca-analysis-765d6c8cf6-hnc2v       1/1     Running            0          2m
    timescaledb-0                                  1/1     Running            0          2m   replica
    timescaledb-1                                  1/1     Running            0          2m   master
    timescaledb-2                                  1/1     Running            0          2m   replica
    ```
28. Verify that no errors are found on the restored Sync Assurance application's timescaledb, kafka, and synca-gnss / synca-tpa pods.
29. Reconnect to the Sync Assurance application from MNC Sync Director client and verify that the correct data exist based on the used database backup file.
