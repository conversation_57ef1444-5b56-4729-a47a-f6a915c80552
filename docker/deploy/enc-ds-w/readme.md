# enc-ds-ctl

## project initial setup
 - init project `go mod init adva/enc-ds-ctl`
 - install the `cobra-cli` tool
 - add `deploy`, `status`, `stop` commands
 - add `gnss`, `csm`, `jobmanager` sub-commands
```shell
mkdir -p src/enc-ds-ctl
go mod init adva/enc-ds-ctl
go install github.com/spf13/cobra-cli@latest
cobra-cli init --author "gerassimosm" --viper
cobra-cli add deploy
cobra-cli add status
cobra-cli add stop
cobra-cli add init
cobra-cli add gnss -p 'deployCmd'
cobra-cli add csm -p 'deployCmd'
cobra-cli add jobmanager -p 'deployCmd'
cobra-cli add enc -p 'initCmd'
cobra-cli add csm -p 'initCmd'
cobra-cli add jobmanager -p 'initCmd'
```

## get update packages
```
go get -u github.com/spf13/viper
go get -u github.com/AlecAivazis/survey/v2
go get -u github.com/lmittmann/tint
go get github.com/briandowns/spinner
```

## dev environment
- The first action to perform for code development is to generate the `enc-ds-ctl.yml` file.  
  The generated `enc-ds-ctl.yml` file will located under the `src/enc-ds-ctl/config/resources/` directory  
  To generate the `enc-ds-ctl.yml` file  
  ```shell
  cd enc-ds-w/
  export ENC_MEDIATION_VERSION=16.1.1-B15231
  make build_config
  ```
- Note that: 
  During development the app is looking for the `enc-ds-ctl.yml` file in the `src/enc-ds-ctl/config/resources/` path
  In production case the app is looking for the `enc-ds-ctl.yml` file in the `.` path
  The location of the `enc-ds-ctl.yml` file is controlled from the `ENC_DS_CTL_CONFIG` env variable

- During the code development we can run the app as follow:
  ```shell
  cd enc-ds-w/src/enc-ds-ctl/
  go run main.go
  go run main.go deploy
  go run main.go deploy --help
  go run main.go deploy csm
  ```


## commands overview
```shell
enc-ds-ctl init-config -> user will be prompt to input all configuration parameters 
enc-ds-ctl init-config enc --enc-host=******* -> non interactive input  
  
enc-ds-ctl deploy -> deploy all enable applications
enc-ds-ctl deploy gnss --enc-host=******* -> deploy gnss app - override default value for enc.host config parameter 

enc-ds-ctl status
enc-ds-ctl status gnss 

enc-ds-ctl stop -> stop all installed applications - user will be prompt to confirm  
enc-ds-ctl stop gnss -> stop gnss app - user will NOT be prompt
  
enc-ds-ctl uninstall -> remove all installed applications - user will be prompt to confirm 
```

## application components
- enc-mediation (mediation-sever, activemq, postgres, ni?, flexera?)
- enc-core (job-manager, alert-manager )
- infra (kafka, reverse-proxy, timescaleDB)
- eod (csmApp)
- gnss (not supported yet)  
- tpa (not supported yet)
- snt (not supported yet)

## cmd - init-config
- The `init-config` cmd will prompt the user for input. 
  User input from the `init-config` will have the following high level results:
  1. Update the `enc-ds-ctl.yml` file with user defined configuration  
  2. Create `secrets` and `configmap` for all "enc-ds" applications that are enabled
  3. Create `enc-token` secret. User need to provide the enc admin user and password to generate the token
  4. At the end user is prompt to run the deploy command
 
- Configurations:
  - `<app>.enabled`        -> stored in `enc-ds-ctl.yml` file
  - `enc.host`             -> stored in `enc-ds-ctl.yml` file
  - `enc.adminUser`        
  - `enc.adminPassword`    -> stored in `enc-mediation-token`  Docker secret
  - `enc.dbUser`           -> stored in `enc-ds-ctl.yml` file ???
  - `enc.dbPassword`       -> stored in `enc-mediation-db-pw` Docker secret
  - `gnss.dbPassword`      -> stored in `enc-gnss-db-pw` Docker secret
  - `infra.dbPassword`     -> stored in `enc-infra-db-pw` Docker secret
  - `registry`             -> stored in `enc-ds-ctl.yml` file (all images) 
  
- During the `init-config` cmd the user will be prompt to select the applications to enabled for deployment
  A multi-checkbox prompt is used to select the applications to enable  
  The global `deploy` cmd will deploy all applications for which the related `<app>.enabled` is `true`
- The `registry` is the docker registry that will be used to pull the images
  The default value is `adva.artifactory` we should consider using `docker.io`
  Note that we are assuming that namespaces (repo names) for all images will be preserved.
  For example if the original image is 
  `adva.artifactory:1234/enc/synca/gnss-collector`
  After the user set a custom `registry` the final image repo will be 
  `<custom-registry>:5678/enc/synca/gnss-collector` 
- The `enc` parameters are special since are related to the monolithic enc mediation server application
- The `enc` parameters can be used from all applications that are enabled 
  The `enc` section in the config file is NOT an application component section that can be enabled/disabled
 
- `<app>.dbPassword` - user will be prompt to set the sensitive data such as passwords for all enabled applications 
  User will NOT be prompted to set sensitive data for which the related secret or configmap already exists
  View the section on how to update secrets and configmaps
- Secrets update
  How to update secrets such as the `enc-db-password` after an initial deployment already took place?
  Note that
  - There is not any docker command available to update secrets
  - Secrets cannot be deleted id there are still containers using them
  - To actually change a secret we need to delete the old one and create a new one
  - All secrets are created during the `init-config` command
  So to update a secret we need to use `unistall` command that will delete all secrets and artifacts
  We can also use `unistall <app>` that will delete only the secrets and artifacts related to the specified `app`
  Ideally we should never need to update a secret. 
  Once the secrets are created all future deployments should use the same secrets.
  Secrets should be also preserved during migration  

### init-config - NOT interactively
- The original thinking was to use init-config subcommands for not interactive execution.
  But SIT has ask us to quickly implement not interactive way for automated tests.
  So we decided to implement an simple approach without subcommands and use only flags
  Examples:
  `enc-ds-ctl init-config --enc-host=******* --infra-kafka-advertise-host=******* --infra-db-pw=12345`
  `enc-ds-ctl init-config --enc-host=******* --infra-kafka-advertise-host=*******`
  Note that the user can also not specify all the flags
  So if a user does not specify a flag (such a password) then the default value will be used
- Notes for the original implementation
  The `init-config` cmd can be executed also NOT interactively by providing the subcommand (`enc`, `eod`, `gnss`) and related flags
  Examples 
  `enc-ds-ctl init-config enc --enc-host="***********"` -> will set the `enc.host` config parameter 
  `enc-ds-ctl init-config enc --enc-db-password="ChgMeNOW"` -> will create the `enc-db-password` secret
  `enc-ds-ctl init-config enc --enc-admin-user="admin" --enc-admin-password="ChgMeNOW"` -> will create the `enc-token` secret
  `enc-ds-ctl init-config gnss --gnss-cli-worker-enabled=true` -> will set the `gnss.cliWorker.enabled` config parameter
   If secret already exists the cmd will fail 

## cmd - uninstall
- The `uninstall` cmd will delete all artifacts and secrets for all installed applications
- The `uninstall <app>` cmd will delete all artifacts and secrets for the specified application
- The following flags can be used to control the behavior of the `uninstall` cmd
  - `--delete-volumes=true` - Default is `false`
  - `--delete-secrets=true` - Default is `false`
  - `--delete-configs=true` - Default is `false`
  - `--delete-images=true`  - Default is `true` 

## cmd - deploy
- The `deploy` cmd will deploy all applications for which the related `<app>.enabled` is `true`
- The `deploy <app>` cmd will deploy the specified application
- In general config parameters can be set via the related command flags `enc.Host` -> `--enc-host`
  These config parameters will override the default values defined in the `enc-ds-ctl.yml` file
  But the config file is not actually updated. The config file is only updated via the `init-config` cmd
  The `deploy` cmd expect to find all secrets, confimaps and yaml files needed for the deployment
- Examples 
  - `deploy gnss --enc-host="***********" --gnss-cli-worker-enabled=true` -> will deploy the gnss application 
     and set the `gnss.cliWorker.enabled=true` config parameter
- Flags are only attached to the `deploy <app>` cmd. 
  The `deploy` cmd will not accept any flags. Will only use the config parameters defined in the `enc-ds-ctl.yml` file 

## cmd - status
- The `status` cmd will display the status of all installed applications
  Note that the `enabled` flag does not play any role here.
  We could consider to display the status for all applications (installed or not)
  For not installed applications the status will be `not installed` 
- The `status <app>` cmd will display the status of the specified application

## cmd - stop
- The `stop` cmd will stop all installed applications. 
- The `stop <app>` cmd will stop the specified application
- The `stop` cmd will use the `docker stack rm <app>` command to 
  stop and delete the containers and networks of the specified app 

## How to set a config parameter
 - There are multiple ways to set the value of a config parameter
 - The default values of all config parameters are defined in the `enc-ds-ctl.yml` file
   This file should contain sensible default values for all config parameters 
 - All configuration parameters can be set via the related env variable
   Examples:
   - `eod.DbHost` -> `EOD_DBHOST`
   - `eod.DbUser` -> `EOD_DBUSER`
 - Most configuration parameters can be set via the related command flags
   - `eod.DbHost` -> `--eod-db-host=*******`
   - `eod.DbUser` -> `--eod-db-user=csm`
- Note that sensitive info like passwords are stored in secrets only during the `init-config` cmd 
 
## version.yml
- The `version.yml` is generated at build time
- The `version-dev.yml` is used as template for `version.yml`
- The `versiom` command will try to read the `version.yml` file first and if does not exist
  it will try to read the `version-dev.yml` file

## enc-ds-ctl.yml - build
- The `enc-ds-ctl.yml` is generated at build time, and it is copied under the `enc-ds-w/dist/enc-ds` folder
  During the build process Image Tags for all application are set
- The `enc-ds-ctl-dev.yml` is used as template for `enc-ds-ctl.yml`

## make targets
- `build_enc-ds-ctl_version` 
  Generate version.yml file
  We need to set the ENC_MEDIATION_VERSION from the command line e.g.
  `make build_enc-ds-ctl_version ENC_MEDIATION_VERSION=15.3.1-B12345`
  or via environment variable `export ENC_MEDIATION_VERSION=15.3.1-B12345` 
  otherwise the build will fail 
- `build_enc-ds-ctl_config` -> generate enc-ds-ctl.yml file
- `build_dev` -> build the application for dev


## Findings - Observation - Decisions
- check if we should have one common `enc-ds.yml` file or should have multiple files
  maybe is better to use multiple files can later be used as `values.yml` for helm charts
  consider also have one viper object and merging all yaml files to one?
  [ref](https://stackoverflow.com/questions/47185318/multiple-config-files-with-go-viper)
- Answer:
  cannot multiple yaml files (config files) with a single viper instance
  when using single instance only the last config file is used
- The used of `.env` file is only supported with docker compose
  It is NOT supported with `docker stack deploy`
  [ref](https://docs.docker.com/compose/environment-variables/set-environment-variables/#compose-file)
- Note about which style to use for yaml keys
  ref: (https://stackoverflow.com/questions/22771226/what-is-the-canonical-yaml-naming-style)
  use: CamelCase -> also used by kubernetes and istio
  avoid using `-`
  otherwise we do have to replace `-` with `_`
- The use of Docker go SDK `github.com/docker/docker/client` added 5MB to the final binary
  And a big number of dependencies
  For the moment we can execute docker commands via `exec.Command`
- cli prompt
  Investigate which go packages can be used to have user interaction functionality "prompt"
  [Interactive CLI prompts in Go](https://dev.to/tidalcloud/interactive-cli-prompts-in-go-3bj9) 
- interactive checkboxes
- Application - Service
  The deployment component are logically grouped in `applications` and `services` 
  An application has one to one mapping with a folder under the `enc-dr` root folder
  Example the `gnss-ds` application is related to the `gnss` folder
  An application has one to one mapping with a docker stack
  We may consider in the future to have multiple docker stacks for one application
  Each application has one or more services (containers) 
- Log
  Log message are written to stdout with pretty human-readable format 
  Log level can be set via the env variable `LOG_LEVEL` valid values are `DEBUG`, `INFO`, `WARN` and `ERROR`
- Write yaml config (enc-ds-ctl.yml)
  Comments and other formatting are not preserved when we write the yaml config file 
  [ref](https://stackoverflow.com/questions/60891174/i-want-to-load-a-yaml-file-possibly-edit-the-data-and-then-dump-it-again-how)
  There are many ways to write (to process) a yaml programmatically.
  I tested using the `viper.WriteConfig` way but in this case non only the comments are lost
  but also the order of the keys is changed and the camel case keys are converted to lower case
  e.f encMediation -> encmediation
  There is a way to preserve comments by using the `yaml.Node` object
  more info
  [ref1](https://forum.golangbridge.org/t/modify-yaml-files-using-golang/31838/3)
  [ref2](https://github.com/go-yaml/yaml/issues/709)
  This way it very complex to process the yaml file since the data are not strongly structured
  For this initial phase I decided to use the struct approach.
  We might consider in the future to use the `yaml.Node` approach 
- Version (build time)
  To include the version number in the final enc-ds-ctl binary we use `build_enc-ds-ctl_version` make target
  To include the tags of Docker images of our application in the final enc-ds-ctl.yaml we use `build_enc-ds-ctl_config` make target
- Config File (enc-ds-ctl.yml)
  The location of the config file is as follows:
   - Default location is at the same folder as the `enc-ds-ctl` binary
   - Set via env variable `ENC_DS_CTL_CONFIG_FILE`
   - Set via command line flag `--config`

## Test endpoints via curl
```shell
curl -k --header "Content-Type: application/json" --header "Host: kafdrop.adva.swarm" \
  --request GET  --resolve adva.swarm:8093:127.0.0.1 "https://adva.swarm:8093"
```

## Questions
- `init-config`
  - Init ENC token. How should we handle the initialization of the ENC token?
    considering that the enc-ds-ctl (in the future) will also deploy enc-mediation
    Should we wait until the enc-mediation is up and running and then init the ENC token? 
    During the `init` process we update the `enc-ds-ctl.yml` file  
    We could store the ENC admin user and password to later use them to init the ENC token
  - Where to store `enc.dbUser`???           
    Can be stored in  secret/configmap/file or use only default value ???


## TODO
- Images
  Should we handle deployment when there is no access to a registry?
  Implement import images functionality from tar.gz file
  Implement export images functionality to tar.gz file from artifactory server
  Should the Build enc-ds-ctl process export the images and include them in the final tar.gz file?
  Probably NOT , if one day we will include the entire SyncAssurance the final tar.gz file will be huge
- Migration functionality
  How to implement migration functionality?
  The general idea is to support incremental migrations.
  For this we need to store the version number for each application.
  At deployment time we need to compare the version numbers "OLD" and "NEW"
  The comparison will determine:
  - if the migration is needed or not (e.g for clean install there is no need for migration)
  - if the migration is supported or not.  
    The comparison is based on the version stored on a configmap or a reference image tag
- Build enc-ds && enc-ds-ctl 
  Use the `build` target to build the enc-ds-ctl binary and create the enc-ds.tar.gz file 
  Use a Dockerfile to build go code into a binary file `enc-ds-ctl`
  Copy `enc-ds-ctl` into ens-ds dir
  Create a `enc-ds.tar.gz` file of ens-ds dir
  Copy enc-ds.tar.gz to sit nms
- [nice to have] implement cli tool to add new application
- kafka mtls
- eod deploy use secrets for db password
- eod add health check in java code and docker-stack.yml
- eod remove exposed port 8989 from docker-stack.yml
- remove hardcode 15.3.1-B14002
- use image from artifactory for 3rd party services 
- wait for infra services to be up and running or add instruction to deploy first 
  work around add delay in enc-ds-ctl  
- docker will not automatically pull gdn-artifactory.rd.advaoptical.com:9443/enc/eod/csm_app:15.3.1-B14002, 
  investigate why 
  work around pull image from enc-ds-ctl 
- uninstall - delete images
- init-config - ask to retype password and check if they match 

