import com.fasterxml.jackson.dataformat.yaml.YAMLFactory
import com.fasterxml.jackson.databind.ObjectMapper
import com.adva.gradle.imagepublisher.ImagePublisherTask
import org.gradle.api.GradleException
import org.gradle.internal.os.OperatingSystem
import java.nio.file.Files
import java.nio.file.Paths

/*
------------------------------------------------------------
Project ':enc-ds-w'
TaskTree for preparing Sync Assurance K8s Registry
------------------------------------------------------------
\--- :enc-ds-w:zipDockerRegistry - Create package with Docker Registry
    \--- :enc-ds-w:exportDockerRegistryContainer - Export Docker Registry Container to tar file
        \--- :enc-ds-w:copyReadmeRegistry - Copies the Readme.md file to the Docker registry tarball directory.
            \--- :enc-ds-w:copyImportRegistryScript - Copies the import-docker-images.sh script to the Docker registry tarball directory.
                \--- :enc-ds-w:Pull3rdpartGNSSImages - Collects all 3rd party GNSS docker images needed for k8s
                    \--- :enc-ds-w:PullGNSSImages - Collects all GNSS docker images needed for k8s
                        \--- :enc-ds-w:dockerLogin - Login into local Docker Registry
                            \--- :enc-ds-w:waitForDockerRegistry - Waits for 20 seconds after starting the local Docker registry.
                                \--- :enc-ds-w:runLocalDockerRegistry - Run Local Docker Registry on localhost:5100
                                    \--- :enc-ds-w:generateRegistryPasswordFile - Generate password file for Local Docker Registry
*/

buildscript {

    // Build script configuration for custom plugins
    ext.fspRoot = file("../../..")
    apply from: "$fspRoot/utilities.gradle"
    apply from: "$fspRoot/parameters.gradle"
    apply from: "$fspRoot/repositories.gradle"

    dependencies {
        classpath('org.hidetake:gradle-ssh-plugin:2.10.1')
    }
}
plugins {
    id 'com.adva.gradle.plugin.image-publisher'
    id "com.dorongold.task-tree" version "4.0.0"
}

ext {
    encRootPath = '../../..'
    tag = "$Version-$BuildNumber"
    //tag = "$Version-B15316"
    publicationTargetName = 'publication-target'
    exportPackagesOnly = false
    exportDockerRegistry = false
    registryUrlPort = 'localhost:5100'
    registryUsername = 'admin'
    registryPassword = 'ChgMeNOW'
    docker_config = '/root/.docker/registry'
}

// Closure that can be used with image publisher that defines the default file server for publishing build installers
ext.publicationTarget = {
    host = publicationHost
    user = publicationUser
    certificate = publicationPrivateCertificate
    trust = true
}

tasks.register('GnssPackagesCreation') {
    group('enc.build')
    description('Collects all GNSS docker images needed for k8s')
    
    // Define inputs and outputs
    inputs.file("src/docker-images/synca-docker-image-enc.yml")
    outputs.dir("$buildDir/gnss_images")

    doLast {
        def yamlFile = file("src/docker-images/synca-docker-image-enc.yml")
        def mapper = new ObjectMapper(new YAMLFactory())
        def imageDir = file("$buildDir/gnss_images")
        def imageFileList = new File("$buildDir/gnss_images/images_list")
        if (!imageDir.exists()) {
            imageDir.mkdirs()
        } else {
            if (imageDir.isDirectory()) {
                logger.quiet("Cleaning up ${imageDir}/ ...")
                imageDir.listFiles().each { file ->
                    if (file.isFile()) {
                        file.delete()
                    }
                }
            } else {
                logger.quiet("Removing directory ${imageDir} ...")
                imageDir.delete()
            }
        }
        // Parse the YAML file
        def data = mapper.readValue(yamlFile, Map)

        // Access the parsed data
        def images = data.spec.images
        images.each { image ->
            def imageName = image.privateRepo.split("/").last()
            def imageRepositoryUrl = image.privateRepo.split("/").first()
            // Extract the tag
            def imageWithTag = image.privateRepo.replace(":latest", ":${tag}")
            def imageNameWithTag = imageName.replace(":latest", ":${tag}")
            logger.quiet("Pulling ${imageWithTag}...")
            exec {
                commandLine "docker", "pull", "${imageWithTag}"
            }        
            logger.quiet("Saving ${imageWithTag} to ${imageDir}/${imageNameWithTag}.tar...")
            exec {
                commandLine "docker", "save", "${imageWithTag}", "-o", "${imageDir}/${imageNameWithTag}.tar"
            }
            logger.quiet("Removing ${imageWithTag}...")
            exec {
                commandLine "docker", "rmi", "${imageWithTag}"
            }
            imageFileList.append("${imageNameWithTag}\n")
        }
    }
}

tasks.register('Gnss3partPackagesCreation') {
    group('enc.build')
    description('Collects all 3rd party GNSS docker images needed for k8s')

    inputs.file("src/docker-images/synca-docker-image-3rd-party.yml")
    outputs.dir("$buildDir/gnss_images")
    
    dependsOn('GnssPackagesCreation')
    doLast {
        def yamlFile = file("src/docker-images/synca-docker-image-3rd-party.yml")
        def mapper = new ObjectMapper(new YAMLFactory())
        // Parse the YAML file
        def data = mapper.readValue(yamlFile, Map)
        def imageName = ""
        def digest = ""
        def imageDir = file("$buildDir/gnss_images")
        def imageFileList = new File("$buildDir/gnss_images/images_list")

        // Access the parsed data
        def images = data.spec.images
        images.each { image ->
            def imageNameWithTag = image.privateRepo.split("/").last()
            logger.quiet("imageNameWithTag ${imageNameWithTag}...")
            logger.quiet("Pulling ${imageNameWithTag}...")
            exec {
                commandLine "docker", "pull", "${image.privateRepo}"
                logger.quiet("image.privateRepo ${image.privateRepo}...")
            }
            logger.quiet("Saving ${image.privateRepo} to ${imageDir}/${imageNameWithTag}.tar...")
            exec {
                commandLine "docker", "save", "${image.privateRepo}", "-o", "${imageDir}/${imageNameWithTag}.tar"
            }
            logger.quiet("Removing ${image.privateRepo}...")
            exec {
                commandLine "docker", "rmi", "${image.privateRepo}"
            }
            imageFileList.append("${imageNameWithTag}\n")
        }
    }
}

task copyImportScript(type: Copy) {
    dependsOn('Gnss3partPackagesCreation')
    from "$buildDir/../dist/registry/common-scripts/import-docker-images.sh"
    into "$buildDir/gnss_images"
}

task zipGnssImages(type: Tar) {
    group('enc.build')
    description('Create package with all required images for GNSS in k8s')
        
    dependsOn('copyImportScript')
    archiveFileName = "SyncAssuranceK8sImages_v${tag}.tar.gz"
    destinationDirectory = file("$buildDir/archives")
    compression = Compression.GZIP

    from("$buildDir/gnss_images") {
        include '**/*'
    }
    into('')
}

imagePublisher {
    addTarget publicationTargetName, publicationTarget
}

task publishToSitHostGnss (type: ImagePublisherTask) {
    group('enc.build')
    description('Publish package with all required images for GNSS in k8s on sitnms1')
    
    project.ext.exportPackagesOnly = true

    dependsOn('zipGnssImages')
    target = publicationTargetName
    destination = '/opt/ftp/enc/builds/production'
    updateSymlink = true
    segmentByDay = true
    cleanup = false
    extractZip = false

    setFiles  {

        return file("$buildDir/archives/SyncAssuranceK8sImages_v${tag}.tar.gz")
    }
    doLast {
        print("##teamcity[buildStatus status='SUCCESS' text='Success: published SyncAssurancek8sImages_v${tag}.tar.gz']\n")
    }
    
}


tasks.register('Gnss3partImagesDownloader') {
    group('enc.build')
    description('Download images from external docker registry and save in adtran docker registry')

    doLast {
        def yamlFile = file("src/docker-images/synca-docker-image-3rd-party.yml")
        def mapper = new ObjectMapper(new YAMLFactory())
        // Parse the YAML file
        def data = mapper.readValue(yamlFile, Map)
        def imageName = ""
        def digest = ""

        // Access the parsed data
        def images = data.spec.images
        images.each { image ->
            // Extract the image name
            def imageNameMatch = image.publicRepo =~ /docker\.io\/([^:]+)/
            if (imageNameMatch.find()) {
                imageName = imageNameMatch.group(1)  
                logger.quiet("Image name: $imageName")
            } else {
                print("##teamcity[buildStatus status='ERROR' text='Error: parse image name: $image.publicRepo']\n")
                throw new GradleException("Task failed: Error: parse image name: $image.publicRepo")
            }
            // Extract the digest
            def parts = image.publicRepo.split('@')
            if (parts.size() > 1) {
                digest = parts[1]
                logger.quiet("Image digest: $digest")
            } else {
                print("##teamcity[buildStatus status='ERROR' text='Error: parse image digest: $image.publicRepo']\n")
                throw new GradleException("Task failed: Error: parse image digest: $image.publicRepo")
            }

            logger.quiet("Checking if ${image.publicRepo} exists in our artifactory...")
            def result = exec {
                commandLine "docker", "manifest", "inspect", "${image.privateRepo}"
                
                standardOutput = new ByteArrayOutputStream()
                errorOutput = new ByteArrayOutputStream()
                ignoreExitValue = true
            }
            
            if (result.exitValue == 0) {
                logger.quiet("Image exists: ${image.privateRepo}, skipping download")
            } else {
                logger.quiet("Image does not exist: ${image.privateRepo} pulling from external registry...")
                logger.quiet("Pulling ${image.publicRepo}...")
                exec {
                    commandLine "docker", "pull", "${image.publicRepo}"
                }
                logger.quiet("Tagging ${imageName}@${digest} to ${image.privateRepo}...")
                exec {
                    commandLine "docker", "tag", "${imageName}@${digest}", "${image.privateRepo}"
                }
                logger.quiet("Removing ${imageName}@${digest}...")
                exec {
                    commandLine "docker", "rmi", "${imageName}@${digest}"
                }
                logger.quiet("Pushing ${image.privateRepo}...")
                exec {
                    commandLine "docker", "push", "${image.privateRepo}"
                }
                logger.quiet("Removing ${image.privateRepo}...")
                exec {
                    commandLine "docker", "rmi", "${image.privateRepo}"
                }
            }
        }
    }
}

task encdsDownloadAndExtractRelease {
    group('enc.build')
    description('Download enc-ds-ctl tool with a current ENC Build Number')

    doLast {
        def versionPropertiesFile = file("${encRootPath}/modules/nmscommon/src/main/resources/com/adva/nlms/common/version.properties")
        def versionProperties = new Properties()
        versionProperties.load(new FileInputStream(versionPropertiesFile))

        def majorVersion = versionProperties.getProperty('MAJOR_VERSION')
        def minorVersion = versionProperties.getProperty('MINOR_VERSION')
        def patchVersion = versionProperties.getProperty('PATCH_VERSION')
        def buildNumber = versionProperties.getProperty('BUILD_NUMBER')

        def fileName = "enc-ds-${majorVersion}.${minorVersion}.${patchVersion}-B${buildNumber}.tar.gz"
        def url = "http://gdn-s-sitnms1/enc/builds/production/current/${fileName}"
        
        def currentDir = file('.').absolutePath
        def downloadFile = "${currentDir}/${fileName}"
        
        // Download the file
        ant.get(src: url, dest: downloadFile)
        
        // Extract the downloaded file using tar
        exec {
            commandLine 'tar', 'xzf', downloadFile, '-C', currentDir, '--strip-components=1'
        }
        
        // Delete the downloaded tar.gz file
        file(downloadFile).delete()

    }
}

task encdsGetIpAddress(dependsOn: 'encdsDownloadAndExtractRelease') {
    group('enc.build')
    description('Get an IP address of host machine')

    doLast {
        String command
        if (OperatingSystem.current().isLinux()) {
            command = "hostname -I | cut -f1 -d' '"
        } else if (OperatingSystem.current().isWindows()) {
            command = "powershell.exe -Command \"(Get-NetIPAddress -InterfaceAlias (Get-NetRoute -DestinationPrefix 0.0.0.0/0).InterfaceAlias | Where-Object { \$_.AddressFamily -eq 'IPv4' } | Select-Object -ExpandProperty IPAddress | Select-Object -First 1).ToString()\""
        } else {
            throw new GradleException("Unsupported operating system")
        }

        def output = new ByteArrayOutputStream()
        def error = new ByteArrayOutputStream()

        exec {
            if (OperatingSystem.current().isWindows()) {
                commandLine 'cmd', '/c', command
            } else {
                commandLine 'bash', '-c', command
            }
            standardOutput = output
            errorOutput = error
        }

        if (!error.toString().isEmpty()) {
            throw new GradleException("Command execution failed: ${error.toString().trim()}")
        }

        def ipAddress = output.toString().trim()
        println "IP Address: $ipAddress"

        // Setting TeamCity environment variable 
        print("##teamcity[setParameter name='env.TC_AGENT_IP' value='$ipAddress']")

        project.ext.set('ipAddress', ipAddress)
    }
}

task encdsInitConfigSwarm(dependsOn: 'encdsGetIpAddress') {
    group('enc.build')
    description('Init configuration for a enc-ds-ctl tool - YAML file')

    doLast {
        def ipAddress = project.findProperty('ipAddress')
        if (ipAddress == null || ipAddress.isEmpty()) {
            throw new GradleException("IP Address is not available. Please run 'getIpAddress' task first.")
        }

        String command = "./enc-ds-ctl init-config --enc-host=${ipAddress} --infra-kafka-advertise-host=${ipAddress}"
        def result = exec {
            commandLine 'bash', '-c', command
        }
        println "initConfig executed successfully."
    }
}

task encdsDeployInfraSwarm(dependsOn: 'encdsInitConfigSwarm') {
    group('enc.build')
    description('Deploy Infrastructure Stack on Swarm')

    doLast {
        String command = "./enc-ds-ctl deploy infra"
        def result = exec {
            commandLine 'bash', '-c', command
        }
        println "Infra deployed successfully."
    }
}

task encdsAddKafkaAddress(dependsOn: 'encdsDeployInfraSwarm') {
    group('enc.build')
    description('Add kafka_address and eod.evolution.enabled to fnm.properties')

    doLast {
        // Read the properties file
        def propertiesFile = file("${encRootPath}/fnm.properties")
        propertiesFile.append("\ncom.adva.nlms.eod.evolution.kafka_address=${ipAddress}:9094\n")
        propertiesFile.append("com.adva.nlms.eod.evolution.enabled=true\n")
    }
}

task encdsInstallENC(type: Exec, dependsOn: 'encdsAddKafkaAddress') {
    group('enc.build')
    description('Install ENC')

    String command = "${encRootPath}/gradlew clean"
    commandLine "${encRootPath}/gradlew", '-b', "${encRootPath}/build.gradle", 'clean', 'spotless', 'all', 'createDatabase', 'waitForMediation', 'modules:tfw_sit:classpathJar' // Execute './gradlew clean' from the root directory of the 'enc' project
}

task encdsDeployRproxySwarm(dependsOn: 'encdsInstallENC') {
    group('enc.build')
    description('Deploy RProxy on Swarm')

    doLast {
        String command = "./enc-ds-ctl deploy rproxy"
        def result = exec {
            commandLine 'bash', '-c', command
        }
        println "rproxy deployed successfully."
    }
}

task encdsDeployEncCoreSwarm(dependsOn: 'encdsDeployRproxySwarm') {
    group('enc.build')
    description('Deploy ENC Core on Swarm')

    doLast {
        String command = "./enc-ds-ctl deploy enc-core"
        def result = exec {
            commandLine 'bash', '-c', command
        }
        println "enc-core deployed successfully."
    }
}

task encdsDeployEODSwarm(dependsOn: 'encdsDeployEncCoreSwarm') {
    group('enc.build')
    description('Deploy EOD on Swarm')

    doLast {
        String command = "./enc-ds-ctl deploy eod"
        def result = exec {
            commandLine 'bash', '-c', command
        }
        println "EOD deployed successfully."
    }
}

task encdsStopStackSwarm {
    group('enc.build')
    description('Stop whole Swarm Stack')

    doLast {
        String command = "./enc-ds-ctl stop"
        def result = exec {
            commandLine 'bash', '-c', command
        }
        sleep(20000)
    }
    sleep(20000)
}

task encdsUninstallStackSwarm(dependsOn: 'encdsStopStackSwarm') {
    group('enc.build')
    description('Uninstall Stack Swarm with Volumes and Network Layer')
    
    doLast {
        String command = "./enc-ds-ctl uninstall --delete-volumes"
        def result = exec {
            ignoreExitValue true
            commandLine 'bash', '-c', command
        }
    }
}

task encdsUninstallENC(type: Exec, dependsOn: 'encdsUninstallStackSwarm') {
    group('enc.build')
    description('Uninstall ENC')
    
    commandLine "${encRootPath}/gradlew", '-b', "${encRootPath}/build.gradle", 'stopServers', 'cleanFiles'
}

task encdsStatusStackSwarm {
    group('enc.build')
    description('Check Status Swarm Services')

    doLast {
        def timeoutMillis = 5 * 60 * 1000 // 5 minutes timeout
        def startTime = System.currentTimeMillis()
        def command = "./enc-ds-ctl status"
        def processOutput = ""

        def result_main = exec {
            commandLine 'bash', '-c', command
        }
        
        while (true) {
            if (System.currentTimeMillis() - startTime > timeoutMillis) {
                throw new GradleException("Timeout reached while waiting for services to stabilize.")
            }

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream()
            def result = exec {
                commandLine 'bash', '-c', command
                standardOutput = outputStream // redirect output to ByteArrayOutputStream
            }

            // Get the output from the ByteArrayOutputStream
            def output = outputStream.toString().trim()

            // Check if there are any instances with "0/1" replicas
            if (!output.contains("0/1")) {
                println("All services have stabilized.")
                break
            }

            println("Waiting for services to stabilize...")
            Thread.sleep(5000) // Wait for 5 seconds before checking again
        }

        // String command = "./enc-ds-ctl status"
        // def result = exec {
        //     commandLine 'bash', '-c', command
        // }

        // if (result.exitValue != 0) {
        //     throw new GradleException("Command execution failed: ${result.errorOutput.toString().trim()}")
        // }
    }
}

task encdsStartSwarm {
    group('enc.build')
    description('Start Installation')

    dependsOn(encdsDeployEODSwarm)
}

task encdsUninstall {
    group('enc.build')
    description('Uninstall Swarm Stack and ENC')

    dependsOn(encdsUninstallENC)
}

// docker registry part
task generateRegistryPasswordFile {
    group('enc.build')
    description('Generate password file for Local Docker Registry')

    doLast {
        def username = 'admin'
        def password = '$2y$05$D0TQatCXZ.A8R6es4C.AkOOgVxmEfEu7arHqifG4EWHV.an5X25.q'
        def passwordFilePath = "${projectDir}/registry.password"
        def passwordContent = "${username}:${password}".getBytes()
        Files.write(Paths.get(passwordFilePath), passwordContent)
    }
}

task runLocalDockerRegistry(type: Exec) {
    group('enc.build')
    description('Run Local Docker Registry on localhost:5100')  

    dependsOn(generateRegistryPasswordFile)
    commandLine 'docker', 'run', '-e', 'REGISTRY_STORAGE_FILESYSTEM_ROOTDIRECTORY=/home', '-e', 'REGISTRY_STORAGE_DELETE_ENABLED=true', '-d', '-p', '5100:5000', '--restart', 'always', '--name', 'registry_adtran', '-v', "${projectDir}/registry.password:/auth/registry.password", '-v', "${projectDir}/dist/registry/configs/config.yml:/etc/docker/registry/config.yml", '-e', 'REGISTRY_AUTH=htpasswd', '-e', 'REGISTRY_AUTH_HTPASSWD_REALM=Registry Realm', '-e', 'REGISTRY_AUTH_HTPASSWD_PATH=/auth/registry.password', 'gdn-artifactory.rd.advaoptical.com:9443/registry:2.8.3'
}

task dockerLogin(type: Exec) {
    group('enc.build')
    description('Login into Local Docker Registry on localhost:5100')

    environment 'DOCKER_CONFIG', docker_config
    
    group('enc.build')
    description('Login into local Docker Registry')  

    //dependsOn('runLocalDockerRegistry')
    dependsOn('waitForDockerRegistry')
    // Define the command to execute
    sleep(20000)
    commandLine 'docker', 'login', registryUrlPort, '-u', registryUsername, '-p', registryPassword
}



task exportDockerRegistryContainer() {
    group('enc.build')
    description('Export Docker Registry Container to tar file')

    outputs.dir("$buildDir/docker_registry_tar")
    
    dependsOn('copyReadmeRegistry')

    doLast {
        def outputDir = file("$buildDir/docker_registry_tar")
        def stopCommand = ['docker', 'stop', 'registry_adtran']
        def exportCommand = ['docker', 'export', '-o', "${outputDir}/SyncAssuranceK8sImages.tar", 'registry_adtran']

        println "Executing command: ${stopCommand}"  
        exec {
            commandLine stopCommand
        }  
        println "Executing command: ${exportCommand}"
        exec {
            commandLine exportCommand
        }
    }
}

task zipDockerRegistry(type: Tar) {
    group('enc.build')
    description('Create package with Docker Registry')
    
    dependsOn('exportDockerRegistryContainer')
    archiveFileName = "SyncAssuranceK8sImages_v${tag}.tar.gz"
    destinationDirectory = file("$buildDir/archives")
    compression = Compression.GZIP

    from("$buildDir/docker_registry_tar") {
        include '**/*'
        into 'SyncAssuranceK8sImages'
    }
    into('')
}

task stopAndRemoveDockerRegistry() {
    group('enc.build')
    description('Stop and remove Local Docker Registry')

    dependsOn('cleanDockerRegistry')
    doLast {
        // Execute docker stop command
        exec {
            commandLine 'docker', 'stop', 'registry_adtran'
        }
        // Execute docker rm command
        exec {
            commandLine 'docker', 'rm', 'registry_adtran'
        }
        exec {
            environment 'DOCKER_CONFIG', docker_config
            commandLine 'docker', 'logout', registryUrlPort
        }
        exec {
            commandLine 'rm', '-rf', docker_config
        }
        exec {
            commandLine 'docker', 'rmi', 'gdn-artifactory.rd.advaoptical.com:9443/registry:2.8.3'
        }
        exec {
            commandLine 'rm', '-fr', '/var/lib/docker/overlay2/*/diff/home/<USER>/registry/v2/'
        }
    }
}

task publishToSitHostGnssRegistry(type: ImagePublisherTask) {
    group('enc.build')
    description('Publish package with Registry for GNSS in k8s on sitnms1')

    project.ext.exportDockerRegistry = true

    dependsOn('stopAndRemoveDockerRegistry')
    target = publicationTargetName
    destination = '/opt/ftp/enc/builds/production'
    updateSymlink = true
    segmentByDay = true
    cleanup = false
    extractZip = false

    setFiles  {
        return file("$buildDir/archives/SyncAssuranceK8sImages_v${tag}.tar.gz")
    }
    doLast {
        print("##teamcity[buildStatus status='SUCCESS' text='Success: published SyncAssuranceK8sImages_v${tag}.tar.gz']\n")
    }
}

tasks.register('PullGNSSImages') {
    group('enc.build')
    description('Collects all GNSS docker images needed for k8s')

    // Define inputs and outputs
    inputs.file("src/docker-images/synca-docker-image-enc.yml")
    outputs.dir("$buildDir/docker_registry_tar")

    if (project.exportDockerRegistry) {
        dependsOn 'dockerLogin'
    }

    doLast {
        def yamlFile = file("src/docker-images/synca-docker-image-enc.yml")
        def mapper = new ObjectMapper(new YAMLFactory())
        def imageDir = file("$buildDir/docker_registry_tar")
        def imageFileList = new File("$buildDir/docker_registry_tar/images_list")
        if (!imageDir.exists()) {
            imageDir.mkdirs()
        } else {
            if (imageDir.isDirectory()) {
                logger.quiet("Cleaning up ${imageDir}/ ...")
                imageDir.listFiles().each { file ->
                    if (file.isFile()) {
                        file.delete()
                    }
                }
            } else {
                logger.quiet("Removing directory ${imageDir} ...")
                imageDir.delete()
            }
        }
        // Parse the YAML file
        def data = mapper.readValue(yamlFile, Map)

        // Access the parsed data
        def images = data.spec.images
        images.each { image ->
            def imageName = image.privateRepo.split("/").last()
            def imageRepositoryUrl = image.privateRepo.split("/").first()
            // Extract the tag
            def imageWithTag = image.privateRepo.replace(":latest", ":${tag}")
            def imageWithTagLocalReg = imageWithTag.replace("${imageRepositoryUrl}", "${registryUrlPort}")
            def imageNameWithTag = imageName.replace(":latest", ":${tag}")
            logger.quiet("Pulling ${imageWithTag}...")
            exec {
                commandLine "docker", "pull", "${imageWithTag}"
            }

            exec {
                commandLine "docker", "tag", "${imageWithTag}", "${imageWithTagLocalReg}"
                logger.quiet("Tagging ${imageWithTagLocalReg}...")
            }
            exec {
                environment 'DOCKER_CONFIG', docker_config
                commandLine "docker", "push", "${imageWithTagLocalReg}"
                logger.quiet("Pushing ${imageWithTagLocalReg}...")
            }
            exec {
                commandLine "docker", "rmi", "${imageWithTagLocalReg}"
                logger.quiet("Deleting ${imageWithTagLocalReg}...")
            }
            exec {
                commandLine "docker", "rmi", "${imageWithTag}"
            } 
            imageFileList.append("${imageWithTagLocalReg}\n")
        }
    }
}

tasks.register('Pull3rdpartGNSSImages') {
    group('enc.build')
    description('Collects all 3rd party GNSS docker images needed for k8s')

    inputs.file("src/docker-images/synca-docker-image-3rd-party.yml")
    outputs.dir("$buildDir/docker_registry_tar")
    
    dependsOn('PullGNSSImages')
    doLast {
        def yamlFile = file("src/docker-images/synca-docker-image-3rd-party.yml")
        def mapper = new ObjectMapper(new YAMLFactory())
        // Parse the YAML file
        def data = mapper.readValue(yamlFile, Map)
        def imageName = ""
        def digest = ""
        def imageDir = file("$buildDir/docker_registry_tar")
        def imageFileList = new File("$buildDir/docker_registry_tar/images_list")

        // Access the parsed data
        def images = data.spec.images
        images.each { image ->
            def imageNameWithTag = image.privateRepo.split("/").last()
            def imageRepositoryUrl = image.privateRepo.split("/").first()
            def imageWithTagLocalReg = image.privateRepo.replace("${imageRepositoryUrl}", "${registryUrlPort}")

            logger.quiet("Pulling ${imageNameWithTag}...")
            exec {
                commandLine "docker", "pull", "${image.privateRepo}"
            }

            exec {
                commandLine "docker", "tag", "${image.privateRepo}", "${imageWithTagLocalReg}"
                logger.quiet("Tagging ${imageWithTagLocalReg}...")
            }
            exec {
                environment 'DOCKER_CONFIG', docker_config
                commandLine "docker", "push", "${imageWithTagLocalReg}"
                logger.quiet("Pushing ${imageWithTagLocalReg}...")
            }
            exec {
                commandLine "docker", "rmi", "${imageWithTagLocalReg}"
                logger.quiet("Deleting ${imageWithTagLocalReg}...")
            }
            exec {
                commandLine "docker", "rmi", "${image.privateRepo}"
                logger.quiet("Deleting ${image.privateRepo}...")
            }
            imageFileList.append("${imageWithTagLocalReg}\n")
        }
    }
}

task copyImportRegistryScript(type: Copy) {
    group('enc.build')
    description('Copies the import-docker-images.sh script to the Docker registry tarball directory.')

    dependsOn('Pull3rdpartGNSSImages')
    from "$buildDir/../dist/registry/common-scripts/import-docker-images.sh"
    into "$buildDir/docker_registry_tar"
}

task copyReadmeRegistry(type: Copy) {
    group('enc.build')
    description('Copies the Readme.md file to the Docker registry tarball directory.')

    dependsOn('copyImportRegistryScript')
    from "$buildDir/../dist/registry/common-scripts/Readme.md"
    into "$buildDir/docker_registry_tar"
}

task waitForDockerRegistry(dependsOn: 'runLocalDockerRegistry') {
    group('enc.build')
    description('Waits for 20 seconds after starting the local Docker registry.')

    doLast {
        println "Waiting for 20 seconds..."
        sleep 20000 // 20 seconds in milliseconds
        println "Done waiting!"
    }
}

task cleanDockerRegistry() {
    group('enc.build')
    description('Executes the clean-docker-registry.sh script to clean the Docker registry.')

    dependsOn('zipDockerRegistry')
    // Make the script executable (optional)
    doLast {
         // Execute docker stop command
        exec {
            commandLine 'docker', 'start', 'registry_adtran'
        }
        sleep(20000)
        exec {
            def scriptPath = "${projectDir}/dist/registry/common-scripts/clean-docker-registry.sh"
            file(scriptPath).setExecutable(true)
            ignoreExitValue = true

            // Set the command to execute the script
            commandLine 'bash', scriptPath
        }
    }
}

task cleanDockerRegistryOnHost(type: Exec) {
    group('enc.build')
    description('Removes all directories under /var/lib/docker/overlay2/*/diff/home/<USER>/registry/v2/')
    commandLine 'bash', '-c', 'rm -fr /var/lib/docker/overlay2/*/diff/home/<USER>/registry/v2/'
}

task restartDockerService(type: Exec) {
    group('enc.build')
    description('Restarts the Docker service')
    dependsOn('cleanDockerRegistryOnHost')

    commandLine 'systemctl', 'restart', 'docker'
}