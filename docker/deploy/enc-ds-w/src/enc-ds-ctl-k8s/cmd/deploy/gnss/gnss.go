package gnss

import (
	deployUtil "adva/enc-ds-ctl/cmd/deploy/util"
	"adva/enc-ds-ctl/config"
	"fmt"
	"log/slog"
	"os"

	"github.com/spf13/cobra"
)

type DeploymentName string

const (
	appName                        = config.GnssAppName
	cmdUse                         = appName
	cmdShort                       = "Deploy " + appName + " services"
	cmdLong                        = "Deploy " + appName + " services"
	guardMl         DeploymentName = "guardMl"
	machineLearning DeploymentName = "machineLearning"
)

func Cmd() *cobra.Command {
	// gnssCmd represents the gnss command
	var gnssCmd = &cobra.Command{
		Use:   cmdUse,
		Short: cmdShort,
		Long:  cmdLong,
		Run: func(cmd *cobra.Command, args []string) {
			run(cmd, args)
		},
	}
	return gnssCmd
}

func run(cmd *cobra.Command, args []string) {

	ctlConfig, waitFlag := deployUtil.CommonPreDeployOperationsExitOnError(cmd, args, appName)

	syncApp := &ctlConfig.Gnss

	//validate syncApp exit on error
	validateSyncAppExitOnError(syncApp)

	// deploy Kafka
	deployUtil.DeploySyncAppKafkaExitOnError(ctlConfig, syncApp, waitFlag)

	// deploy timescaleDB
	deployUtil.DeploySyncAppTimescaleDBExitOnError(ctlConfig, syncApp, waitFlag)

	// deploy java apps
	deployUtil.DeploySyncAppJavaAppsExitOnError(ctlConfig, syncApp, GnssJavaAppsHelmChart{}, waitFlag)
}

type GnssJavaAppsHelmChart struct {
}

func (_ GnssJavaAppsHelmChart) UpdateHelmSetMap(helmSetMap map[string]string, c *config.EncDsCtlConfig, syncApp *config.SyncApp) {
	helmSetMap["encVersion"] = c.Global.EncMediation.Version
	helmSetMap["encHost"] = c.Global.EncMediation.Host
	helmSetMap["encHost2"] = c.Global.EncMediation.Host2
	helmSetMap["encMtlsToken"] = c.Global.EncMediation.EncMtlsToken
	helmSetMap["dbPassword"] = syncApp.DbPassword
}

func validateSyncAppExitOnError(syncApp *config.SyncApp) {
	//Validate that only one of machineLearning or guardMl is enabled

	isMachineLearningEnabled := false
	isGuardMlEnabled := false
	d := syncApp.Apps.Deployments
	//loop through deployments and validate
	for _, deployment := range d {
		if deployment.Enabled {
			if DeploymentName(deployment.Name) == machineLearning {
				isMachineLearningEnabled = true
			} else if DeploymentName(deployment.Name) == guardMl {
				isGuardMlEnabled = true
			}
		}
	}

	if isMachineLearningEnabled && isGuardMlEnabled {
		// log error and exit
		errMsg := fmt.Sprintf("Only one of %s or %s can be enabled at a time. Please disable one of them in the config file.", machineLearning, guardMl)
		slog.Error(errMsg)

		os.Exit(1)
	}

}
