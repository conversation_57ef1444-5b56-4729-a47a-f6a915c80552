include enc-ds-ctl.properties

# ENC_MEDIATION_VERSION=15.3.1-B12345
EOD_CSMAPP_TAG="${ENC_MEDIATION_VERSION}"
ENCCORE_JOBMANAGER_TAG="${ENC_MEDIATION_VERSION}"
# We set the above env variable via export or via cmd - example:
# make build_enc-ds-ctl_version ENC_MEDIATION_VERSION=15.3.1-B12345

# enc-ds-ctl.properties variables
# enc_ds_ctl_version
build: build_version build_config build_binary
	@echo "Building all distribution files..."
	echo "test"
	@echo "Done!"

build_tar: build
	@echo "Building tar file"
	cd dist && rm -rfv enc-ds-*.tar.gz && tar -cvzf "enc-ds-${ENC_MEDIATION_VERSION}.tar.gz" enc-ds
	@echo "Done!"


build_version:
	@echo "Building enc-ds-ctl version..."
	cd src/enc-ds-ctl && go run cmd/version/main/build_version.go \
    -encDsCtlVersion="${enc_ds_ctl_version}" -encMediationVersion="${ENC_MEDIATION_VERSION}" \
	echo "generated version.yml" && cat cmd/version/resources/version.yml
	@echo "Done!"

build_config:
	@echo "Building enc-ds-ctl config..."
	cd src/enc-ds-ctl && go run config/main/build_config.go \
    -encMediationVersion="${ENC_MEDIATION_VERSION}" \
    -eodCsmAppTag="${EOD_CSMAPP_TAG}" -encCoreJobManagerTag="${ENCCORE_JOBMANAGER_TAG}" \
	&& cp -v config/resources/enc-ds-ctl.yml ../../dist/enc-ds/ && \
	echo "generated enc-ds-ctl.yml" && cat config/resources/enc-ds-ctl.yml
	@echo "Done!"

build_binary:
	@echo "Building enc-ds-ctl for dev"
	go build -C src/enc-ds-ctl -o ../../ && \
    mv -v enc-ds-ctl dist/enc-ds/ && \
    cd dist/enc-ds/ && ./enc-ds-ctl completion bash > util/completion.sh
	@echo "Done!"

# ==============================================================================
# enc-ds-ctl-k8s
# ==============================================================================
build_k8s: build_version_k8s build_config_k8s build_binary_k8s build_helm_charts
	@echo "Building all distribution files..."
	echo "test"
	@echo "Done!"

build_tar_k8s: build_k8s
	@echo "Building k8s tar file"
	cd dist && rm -rfv mnc-ds-k8s-*.tar.gz && tar -cvzf "mnc-ds-k8s-${ENC_MEDIATION_VERSION}.tar.gz" mnc-ds-k8s
	@echo "Done!"

build_values_schema:
	@echo "Building values.schema.json"
	cd src/enc-ds-ctl-k8s && go mod download
	# cd src/enc-ds-ctl-k8s && go run helm/schema/main/ctl_schema_converter.go -convert="autoGenSchemaJsonToYaml"
	cd src/enc-ds-ctl-k8s && go run helm/schema/main/ctl_schema_converter.go -convert="schemaJsonToYaml"	
	@echo "Done!"

build_helm_charts:
	@echo "Building Helm Charts"
	cd src/enc-ds-ctl-k8s && go mod download
	cd src/enc-ds-ctl-k8s && go run helm/charts/main/build.go -encMediationVersion="${ENC_MEDIATION_VERSION}" 
	@echo "Done!"

build_version_k8s:
	@echo "Building enc-ds-ctl-k8s version..."
	cd src/enc-ds-ctl-k8s && go run cmd/version/main/build_version.go \
    -encDsCtlVersion="${enc_ds_ctl_k8s_version}" -encMediationVersion="${ENC_MEDIATION_VERSION}" \
	echo "generated version.yml" && cat cmd/version/resources/version.yml
	@echo "Done!"

build_config_k8s: build_values_schema
	@echo "Building enc-ds-ctl config..."
	cd dist/mnc-ds-k8s/util/helm-charts/ctl-lint  && \
	helm lint -f ../../../../../src/enc-ds-ctl-k8s/config/resources/mnc-ds-ctl-dev.yml
	cd src/enc-ds-ctl-k8s && \
	go run config/main/build_config.go -encMediationVersion="${ENC_MEDIATION_VERSION}" &&\
	cp -v config/resources/mnc-ds-ctl.yml ../../dist/mnc-ds-k8s/ && \
	cp -v config/resources/mnc-ds-ctl-dev.yml ../../dist/mnc-ds-k8s/doc/mnc-ds-ctl.reference.yml && \
	echo "generated mnc-ds-ctl.yml"
	@echo "Done!"

build_binary_k8s:
	@echo "Building enc-ds-ctl for k8s"
	go build -C src/enc-ds-ctl-k8s -o ../../mnc-ds-ctl && \
    mv -v mnc-ds-ctl dist/mnc-ds-k8s/ && \
    cd dist/mnc-ds-k8s/ && ./mnc-ds-ctl completion bash > util/completion.sh
	@echo "Done!"	

# ==============================================================================
# docker-images-sbom
# ==============================================================================
docker_build_sbom: 
	@echo "Building sbom docker images"
	cd src/docker-images-sbom && go mod download
	cd src/docker-images-sbom && go run . -encMediationVersion="${ENC_MEDIATION_VERSION}" -buildType="${SYNC_BUILD_TYPE}"
	@echo "Done!"