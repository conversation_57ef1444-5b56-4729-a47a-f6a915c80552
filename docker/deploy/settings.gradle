pluginManagement {
    ext.fspRoot = file("../..")
    apply from: "$fspRoot/utilities.gradle"
    apply from: "$fspRoot/parameters.gradle"

    repositories {
        if (PluginDeveloper) {
            mavenLocal()
        }
        maven {
            credentials {
                username artifactoryReadUser
                password artifactoryReadPassword
            }
            url "https://$artifactoryServer/artifactory/NMS"
        }
        maven {
            credentials {
                username artifactoryReadUser
                password artifactoryReadPassword
            }
            url "https://$artifactoryServer/artifactory/gradle-plugins-remote-cache"
            metadataSources {
                mavenPom()
                artifact()
            }
        }

        // Fallback to gradle plugin repository if ADVA Artifacty fails
        gradlePluginPortal()
    }

    plugins {
        id 'com.adva.gradle.plugin.project-module-build-plugin'    version pluginVersion
        id 'com.adva.gradle.plugin.project-module-settings-plugin' version pluginVersion
        id 'com.adva.gradle.plugin.image-publisher'     version pluginVersion
    }
}

buildscript {
    // Build script configuration for custom plugins
    ext.fspRoot = file("../..")
    apply from: "$fspRoot/utilities.gradle"
    apply from: "$fspRoot/parameters.gradle"

    // Pull custom ADVA plugins from Artifactory server
    repositories {
        if (PluginDeveloper) {
            mavenLocal()
        }
        maven {
            credentials {
                username artifactoryReadUser
                password artifactoryReadPassword
            }
            url "https://$artifactoryServer/artifactory/NMS"
        }
        maven {
            credentials {
                username artifactoryReadUser
                password artifactoryReadPassword
            }
            url "https://$artifactoryServer/artifactory/libs-release"
        }
    }

    dependencies {
        classpath 'com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.13.0'
    }

}

plugins {
    id "com.gradle.develocity" version "3.17.5"
    id 'com.adva.gradle.plugin.project-module-settings-plugin'
}

// configure the project-module
projectModule {
    excludeDirs  = [ 'src', 'build', 'buildSrc' ] as Set
    projectPaths = [ 'enc-ds-w', 'helm', 'mod-tools' ] as Set
}