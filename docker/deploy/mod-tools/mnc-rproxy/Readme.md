# MOD MNC RPROXY Package in Docker Swarm

This package contains Bash scripts and a Docker stack configuration to manage the deployment and removal of the MOD MNC RPROXY package in a Docker Swarm environment. The provided scripts help you automate the setup and teardown of the necessary Docker services, networks, volumes, and secrets.

## Package Structure

- `install-mnc-rproxy.sh`: <PERSON><PERSON><PERSON> to install the MOD MNC RPROXY package.
- `uninstall-mnc-rproxy.sh`: <PERSON>ript to uninstall the MOD MNC RPROXY package.
- `mnc-rproxy-ctl.sh`: Script to manage the MOD MNC RPROXY package (status, start, stop, restart).
- `docker-stack.yml`: Docker stack configuration file for the MOD MNC RPROXY services.
- `docker_images`: Directory containing Docker images in tar files needed to run the mnc-rproxy app.
- `utils/`: Directory containing helper scripts and default configurations.

## Prerequisites

Ensure you have Docker and Docker Swarm initialized on your system. If not, you can initialize Docker Swarm with the following command:

```bash
docker swarm init
```

## Usage

### Installation

To install the MOD MNC RPROXY package, use the `install-mnc-rproxy.sh` script. This script performs the following actions:

1. Imports Docker images.
2. Creates necessary Docker volumes.
3. Creates a Docker network.
4. Creates a Docker secret for the database password.
5. Deploys the Docker stack.

Run the installation script with the following command:

```bash
./install-mnc-rproxy.sh install
```

You can check the status of the deployed stack using:

```bash
./mnc-rproxy-ctl.sh status
```

### Uninstallation

To uninstall the MOD MNC RPROXY package, use the `uninstall-mnc-rproxy.sh` script. This script performs the following actions:

1. Stops and removes the Docker stack.
2. Removes Docker volumes.
3. Removes the Docker network.
4. Deletes the Docker secret for the database password.
5. Removes related Docker images.

Run the uninstallation script with the following command:

```bash
./uninstall-mnc-rproxy.sh
```

### Application Management

You can manage the MOD MNC RPROXY package using the `mnc-rproxy-ctl.sh` script with the following commands:

- `status`: Check the status of the Docker mnc-rproxy stack.
- `start`: Start the mnc-rproxy stack.
- `stop`: Stop the mnc-rproxy stack.
- `restart`: Restart the mnc-rproxy stack.

Example usage:

```bash
./mnc-rproxy-ctl.sh status
./mnc-rproxy-ctl.sh start
./mnc-rproxy-ctl.sh stop
./mnc-rproxy-ctl.sh restart
```

## Configuration Details

### `docker-stack.yml`

The `docker-stack.yml` file defines the services, networks, volumes, and secrets required for the MOD MNC RPROXY package. Key sections include:

- **Services**:
  - `traefik`: Main application service.

- **Networks**:
  - `net-traefik`: External network used by the services.

### `install-mnc-rproxy.sh`

This script includes functions to handle the creation of Docker secrets, volumes, networks, and the deployment of the Docker stack. It also includes functions to check the status of the deployed stack.

### `uninstall-mnc-rproxy.sh`

This script includes functions to stop and remove the Docker stack, delete Docker secrets, volumes, and networks, and remove related Docker images.

### `mnc-rproxy-ctl.sh`
This script includes functions to manage the Docker mnc-rproxy stack, allowing you to check its status, start, stop, and restart the stack.

### `utils/`
The `utils/` directory contains helper scripts and default configurations used by the main installation and uninstallation scripts.

## Notes

- Ensure that the `docker_images` directory contains the necessary Docker images in tar files before running the installation script.
- Update the environment variables and Docker configurations as needed to match your specific setup.

By following the instructions in this README, you can easily manage the deployment and removal of the MOD MNC RPROXY package in your Docker Swarm environment.
