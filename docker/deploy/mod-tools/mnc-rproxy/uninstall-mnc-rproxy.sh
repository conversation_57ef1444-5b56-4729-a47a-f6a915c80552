#!/bin/bash

# Get the directory of this script
typeset -r dirOfThisScript=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

# Source utility scripts
source "${dirOfThisScript}"/utils/docker_helper.sh
source "${dirOfThisScript}"/utils/defaults.sh

# Define installation directory
INSTALL_DIR="/opt/adva/stacks/mnc-rproxy"

# Function to uninstall the stack and all related resources
uninstall_stack() {
  actionMsg="Start: uninstall procedure of ${STACK_NAME}"
  echo $actionMsg

  # Check all relevant resources
  local resource_blocked=0

  if is_resource_in_use_elsewhere network "$STACK_NAME"_"$NETWORK_NAME" "$STACK_NAME"; then
    echo "Network $NETWORK_NAME is still used by another stack. Skipping uninstall."
    resource_blocked=1
  fi

  if [[ $resource_blocked -eq 1 ]]; then
    echo "Uninstall aborted: One or more resources are still in use by other stacks."
    return 1
  fi

  bash "${dirOfThisScript}"/mnc-rproxy-ctl.sh --id $STACK_NAME stop
  bash "${dirOfThisScript}"/utils/stack_delete_all.sh "$STACK_NAME"

  deleteStackVolumes "$STACK_NAME"
  removeDanglingVolumes

  if [[ $(docker images | grep adtran/mod/mnc-rproxy/ || true) ]]; then
    for image in traefik; do
      image=$(docker images --format '{{.Repository}}:{{.Tag}}' | grep adtran/mod/mnc-rproxy/${image})
      docker image rm ${image}
    done
  fi

  echo "Cleaning up directory $INSTALL_DIR"
  rm -rf ${INSTALL_DIR}

  echo "Deleting certificates from host path: $CERT_PATH_ON_HOST"
  rm -f ${CERT_PATH_ON_HOST} && echo "Certificate at $CERT_PATH_ON_HOST deleted successfully." || echo "Failed to delete certificate at $CERT_PATH_ON_HOST."

  echo "Updating the CA trust store..."
  # Update the CA trust store
  update-ca-trust && echo "CA trust store updated successfully." || echo "Failed to update CA trust store."

  actionMsg="End: uninstall procedure"
  echo "${actionMsg}"
}

uninstall_stack
