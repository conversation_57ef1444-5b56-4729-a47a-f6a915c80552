version: "3.7"

x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "${RPROXY_LOG_SIZE:-100m}"
    max-file: "${RPROXY_LOG_MAX_FILES:-3}"

x-deploy: &default-deploy
  replicas: 1
  update_config:
    parallelism: 1
    delay: 10s
  restart_policy:
    condition: any
    max_attempts: 10
    window: 30m

services:
  traefik:
    image: "${MNC_RPROXY_TRAEFIK_IMAGE}:${MNC_RPROXY_TRAEFIK_TAG}"
    command:
      - "--providers.swarm.endpoint=unix:///var/run/docker.sock"
      - "--providers.docker=true"
      - "--providers.swarm.exposedbydefault=false"
      - "--providers.docker.exposedbydefault=false"
      - "--providers.file.directory=/etc/traefik/config"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--entrypoints.websecure.http.tls=true"
      - "--entrypoints.kafkaentry.address=:9200"
      - "--log.filePath=/var/log/traefik.log"
      - "--log.level=INFO"
      - "--ping=true"
    networks:
      - net-traefik
    deploy:
      placement:
        constraints:
          - node.role == manager
      resources:
        limits:
          memory: 512M  # Slightly above tested limit for added safety
          cpus: '1.0'
        reservations:
          memory: 100M # Ensures a minimum of 100MB is reserved
          cpus: "0.2" # Ensures a minimum of 0.2 CPUs is reserved
    ports:
      - "8093:443"
      - "9200:9200"
    volumes:
      - /opt/adva/stacks/mnc-rproxy/config:/etc/traefik/config
      - /opt/adva/stacks/mnc-rproxy/ssl:/etc/traefik/certs
      - /var/run/docker.sock:/var/run/docker.sock
      - traefik-logs:/var/log
    healthcheck:
      test: ["CMD-SHELL", "traefik healthcheck --ping"]
      interval: 60s
      timeout: 20s
      retries: 3
    environment:
      TZ: UTC
    logging: *default-logging
    read_only: true
    cap_drop:
      - ALL
    cap_add:
      - NET_BIND_SERVICE
    tmpfs:
      - /run
      - /tmp
    ulimits:
      nofile:
        soft: 1024
        hard: 4096

networks:
  net-traefik:

volumes:
  traefik-logs:
