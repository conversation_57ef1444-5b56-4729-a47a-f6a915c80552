#!/bin/bash
set -e

DYNAMIC_CONFIG="/opt/adva/stacks/mnc-rproxy/config/traefik.yml"
PLACEHOLDER="\${MNC_HOST}"

validate_ip() {
  local ip=$1
  local result=1

  if [[ $ip =~ ^([0-9]{1,3})\.([0-9]{1,3})\.([0-9]{1,3})\.([0-9]{1,3})$ ]]; then
    local o1=${BASH_REMATCH[1]}
    local o2=${BASH_REMATCH[2]}
    local o3=${BASH_REMATCH[3]}
    local o4=${BASH_REMATCH[4]}

    if (( o1 > 255 || o2 > 255 || o3 > 255 || o4 > 255 )); then
      return 1
    fi

    if (( o1 == 127 )); then return 1; fi
    if (( o1 == 0 )); then return 1; fi
    if (( o1 == 169 && o2 == 254 )); then return 1; fi
    if (( o1 >= 224 )); then return 1; fi

    result=0
  fi

  return $result
}

if [[ ! -f "$DYNAMIC_CONFIG" ]]; then
  echo "Error: Dynamic configuration file $DYNAMIC_CONFIG does not exist."
  exit 1
fi

if ! grep -q "$PLACEHOLDER" "$DYNAMIC_CONFIG"; then
  # If the placeholder is not found, it's already set, exit silently.
  exit 0
fi

# If MNC_HOST is already set and valid, skip prompting
if [ -n "$MNC_HOST" ] && validate_ip "$MNC_HOST"; then
  echo "MNC_HOST is set to '$MNC_HOST'"
else
  echo "MNC_HOST is not set or invalid."

  while true; do
    read -p "Please provide the MNC IP address: " MNC_HOST_VAR
    if validate_ip "$MNC_HOST_VAR"; then
      export MNC_HOST="$MNC_HOST_VAR"
      echo "MNC_HOST is set to $MNC_HOST"
      break
    else
      echo "Error: Invalid IP address. Please enter a valid IP address."
    fi
  done
fi

sed -i "s/$PLACEHOLDER/$MNC_HOST/g" "$DYNAMIC_CONFIG"
echo "Replaced $PLACEHOLDER with $MNC_HOST in $DYNAMIC_CONFIG."
