#!/bin/bash

typeset -r proxyUtilDir=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
source "${proxyUtilDir}"/../../defaults.sh

CERT_PATH_ON_HOST="/etc/pki/ca-trust/source/anchors/server.crt"
DOCKER_STACK_BUILD='docker-stack.yml'
IMAGES_DIR="${scriptDir}/../docker_images"
INSTALL_DIR="/opt/adva/stacks/mnc-rproxy"
LOG_DIR="/var/tmp/healthcheck_stacks"
NETWORK_NAME="net-traefik"
NLMS=$NMS_HOME/fsp_nm
NMS_HOME=/opt/adva
REPO='adtran/mod/mnc-rproxy'
RPROXY_TAG=v3.3.7
SOURCE_LOG="/var/lib/docker/volumes/mnc-rproxy_traefik-logs/_data/"
STACK_FILE="docker-stack.yml"
STACK_NAME="mnc-rproxy"
TARGET_LOG="/opt/adva/fsp_nm/var/log/mnc-rproxy"
SERVICE_LIST_FILE="/opt/adva/stacks/service_list"
TRAEFIK_APP_CONTAINER_NAME="traefik"
TRAEFIK_APP_LOG_PATH="/var/log/traefik.log"
