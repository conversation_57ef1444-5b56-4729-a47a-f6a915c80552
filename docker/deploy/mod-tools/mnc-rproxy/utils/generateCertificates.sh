#!/bin/bash

## Basic script to generate SSL certificates, Docker secrets and config for rproxy

set -e # automatic exit on error
typeset -r dirOfThisScript=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
source "${dirOfThisScript}"/defaults.sh
source "${dirOfThisScript}"/docker_helper.sh
cd "${dirOfThisScript}"

RPROXY_TAG=${RPROXY_TAG}
echo $RPROXY_TAG

# Check if MNC_HOST environment variable is set
if [ -z "$MNC_HOST" ]; then
  echo "Warning: MNC_HOST environment variable is not set."
  read_mnc_ip
fi

# Define variables
# IMPORTANT! CERT_DIR and CERT_KEY, CERT_CSR, CERT_CRT must match those used in traefik.yml and traefik's docker-stack.yml

CERT_KEY="$INSTALL_DIR/ssl/server.key"
CERT_CSR="$INSTALL_DIR/ssl/server.csr"
CERT_CRT="$INSTALL_DIR/ssl/server.crt"

# Function to replace existing certificates with user-provided ones
replace_certificates() {
  local user_cert_crt=$1
  local user_cert_key=$2

  if [ "$EUID" -ne 0 ]
    then echo "Root privileges are needed to perform this action."
    exit 1
  fi

  echo ""
  echo "This script will replace certificates."

  if [[ ! -f "$user_cert_crt" || ! -f "$user_cert_key" ]]; then
    echo "Error: Provided certificate files do not exist."
    exit 1
  fi

  echo "Checking provided certificates."
  if ! openssl x509 -noout -in "$user_cert_crt" >/dev/null 2>&1; then
    echo "Error: Provided certificate file is not a valid X.509 certificate. Exiting."
    exit 1
  else
    echo "Provided crt file is a valid X.509 certificate."
  fi

  if ! openssl rsa -noout -in "$user_cert_key" >/dev/null 2>&1; then
    echo "Error: Provided key file is not a valid RSA private key. Exiting."
    exit 1
  else
    echo "Provided key file is a valid RSA private key."
  fi

  echo "Replacing existing certificates with user-provided ones."
  cp -pf "$user_cert_crt" "$CERT_CRT"
  cp -pf "$user_cert_key" "$CERT_KEY"

  echo "Updating the CA trust store."

  # Copy the new certificate to the system CA trust store
  cp -pf "$CERT_CRT" /etc/pki/ca-trust/source/anchors/

  # Update the CA trust store
  update-ca-trust

  echo "Certificate replacement completed. Please restart MNC and MOD."
  exit 0
}

# Check if script is run with replace-certificate flag
if [[ "$1" == "--replace-certificate" ]]; then
  if [[ -z "$2" || -z "$3" ]]; then
    echo "Usage: $0 --replace-certificate /path/to/new/server.crt /path/to/new/server.key"
    exit 1
  fi
  replace_certificates "$2" "$3"
fi

countAdtranProxySecrets=$(docker secret ls | grep "$STACK_NAME_adtran_proxy_ssl_key_${RPROXY_TAG}" | wc -l)
if [ "${countAdtranProxySecrets}" -gt 0 ]; then
  action_msg="Docker secrets for mnc-rproxy ssl keys already exist."
  echo "${action_msg}"
  action_msg="Exiting..."
  echo "${action_msg}"
fi

# Deleting old ssl dir if exist
if [ -d "${INSTALL_DIR}/ssl" ]; then
  # Take action if $DIR exists. #
  action_msg="Deleting old ssl dir..."
  echo "${action_msg}"
  rm -rfv "${INSTALL_DIR}/ssl"
fi

# Create SSL certificates for CA
action_msg="Create SSL certificates for CA."
echo "Start: ${action_msg}"
mkdir -p "${INSTALL_DIR}"/ssl

# Generate a private key
openssl genrsa -out "$CERT_KEY" 2048

# Create a certificate signing request (CSR) with CN as MNC_HOST
openssl req -new -key "$CERT_KEY" -out "$CERT_CSR" -subj "/CN=$MNC_HOST"

# Generate a self-signed certificate valid for 365 days
openssl x509 -req -days 365 -in "$CERT_CSR" -signkey "$CERT_KEY" -out "$CERT_CRT"

# Copy the certificate to the system CA trust store
cp "$CERT_CRT" /etc/pki/ca-trust/source/anchors/

# Update the CA trust store
update-ca-trust

####################################################
# Gernerate Docker secrets and config for traefik
####################################################
# # create docker secrets to store ssl certificates
# action_msg="Create docker secrets to store ssl certificates"
# echo "Start: ${action_msg}"
# # the following ca.crt file is needed if we want to enable the mTLS
# # docker secret create adtran_proxy_ssl_ca_${RPROXY_TAG} ${dirOfThisScript}/ssl/ca.crt
# docker secret create "${STACK_NAME}_adtran_proxy_ssl_key_${RPROXY_TAG}" $CERT_KEY
# docker secret create "${STACK_NAME}_adtran_proxy_ssl_crt_${RPROXY_TAG}" $CERT_CRT
# docker secret ls | grep "${STACK_NAME}_adtran_proxy_ssl"
# echo "End: ${action_msg}"

# # create docker config to store traefik dynamic configuration file

# countAdtranConfig=$(docker config ls | grep "$STACK_NAME_adtran_proxy_dynamic_config_${RPROXY_TAG}" | wc -l)
# if [ "${countAdtranConfig}" -gt 0 ]; then
#   action_msg="Docker config for mnc-rproxy already exists."
#   echo "${action_msg}"
#   action_msg="Exiting..."
#   echo "${action_msg}"
# else
#   action_msg="Create docker config to store traefik dynamic configuration file"
#   echo "Start: ${action_msg}"
#   docker config create ${STACK_NAME}_adtran_proxy_dynamic_config_${RPROXY_TAG} ${INSTALL_DIR}/traefik.yml
#   docker config ls | grep ${STACK_NAME}_adtran_proxy_dynamic_config
#   echo "End: ${action_msg}"
# fi