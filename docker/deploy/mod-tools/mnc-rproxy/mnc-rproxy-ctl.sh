#!/bin/bash

set -e # automatic exit on error

typeset -r dirOfThisScript=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
source "${dirOfThisScript}"/utils/docker_helper.sh
source "${dirOfThisScript}"/utils/defaults.sh
cd "${dirOfThisScript}" || exit 1

assertDocker

ID="mnc-rproxy"
HELP_MSG=$(
  cat <<-EOF
Usage: ./mnc-rproxy-ctl.sh [--id <deploy-id>] <command>

Options:
  --id <deploy-id>          Specify the stack ID (default: mnc-rproxy)

Commands:
  status                    Check the status of all services in the stack and whether certificate verification is enabled or not.
  start                     Start the stack.
  stop                      Stop the stack.
  restart                   Restart the stack (stop and start).
  logs                      Gather and display logs for the stack.
  --help, -h                Display this help message.

Examples:
  ./mnc-rproxy-ctl.sh --id my-deployment start
  ./mnc-rproxy-ctl.sh restart
  ./mnc-rproxy-ctl.sh logs
EOF
)

# Initialize ACTION and ID variables
ACTION=""
while [[ $# -gt 0 ]]; do
  case "$1" in
  --id)
    if [[ -n "$2" && "$2" != "--"* ]]; then
      ID="$2"
      shift 2
    else
      echo "Invalid argument for --id"
      echo "$HELP_MSG"
      exit 1
    fi
    ;;
  --help | -h)
    echo "$HELP_MSG"
    exit 0
    ;;
  status | start | stop | restart | logs)
    ACTION="$1"
    shift
    ;;
  *)
    echo "Invalid argument: $1"
    echo "$HELP_MSG"
    exit 1
    ;;
  esac
done

if [[ -z "$ACTION" ]]; then
  echo "Invalid arguments."
  echo "$HELP_MSG"
  exit 1
fi

case ${ACTION} in
status)
  check_stack_status "$ID"
  ;;
start)
  deployDockerStack "$ID"
  ;;
stop)
  deleteDockerStack "$ID"
  ;;
restart)
  deleteDockerStack "$ID"
  deployDockerStack "$ID"
  ;;
logs)
  healthcheck_dir="${LOG_DIR}/healthcheck_$ID"
  mkdir -p "${healthcheck_dir}"
  getContainerFile "$ID" "$TRAEFIK_APP_CONTAINER_NAME" "$SOURCE_LOG"
  gatherLogs "$ID"
  ;;
*)
  echo "Unknown action"
  echo "$HELP_MSG"
  ;;
esac
