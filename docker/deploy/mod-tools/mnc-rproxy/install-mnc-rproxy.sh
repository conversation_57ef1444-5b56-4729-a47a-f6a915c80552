#!/bin/bash

typeset -r dirOfThisScript=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
source "${dirOfThisScript}"/utils/docker_helper.sh
source "${dirOfThisScript}"/utils/defaults.sh

cd "${dirOfThisScript}" || exit 1

INSTALL_DIR="/opt/adva/stacks/mnc-rproxy"

HELP_MSG=$(
  cat <<-EOF
Usage: ./install-mnc-rproxy.sh <command>

Commands:
  install                   Install mnc-rproxy.
  --help, -h                Display this help message.

Examples:
  ./install-mnc-rproxy.sh install
EOF
)

install() {
  # Check if the stack is already installed
  if docker stack ls | grep -qw "$STACK_NAME"; then
    echo "Stack '$STACK_NAME' is already installed. Skipping installation."
    return 0
  fi

  echo "Installing mnc-rproxy"
  echo ""
  mkdir -p ${INSTALL_DIR}
  cp -r utils config ${INSTALL_DIR}
  cp *.sh *.yml Readme.md ${INSTALL_DIR}

  assertDocker

  # stop stack before installing newer version
  # if stack does not exist or is not running, this call does nothing
  bash "${dirOfThisScript}/mnc-rproxy-ctl.sh" --id $STACK_NAME stop
  source ./utils/import-docker-images.sh
  bash ./utils/generateCertificates.sh
  #source ./utils/prepareTraefikDynamicConfig.sh
  bash "${dirOfThisScript}/mnc-rproxy-ctl.sh" --id $STACK_NAME start

  echo "mnc-rproxy installation finished"
  echo ""
}

if [[ $# -eq 0 ]]; then
  echo "No arguments provided."
  echo "$HELP_MSG"
  exit 1
fi

case "$1" in
install)
  install
  ;;
--help | -h)
  echo "$HELP_MSG"
  exit 0
  ;;
*)
  echo "Invalid command: $1"
  echo "$HELP_MSG"
  exit 1
  ;;
esac
