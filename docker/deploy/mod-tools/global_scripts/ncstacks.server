#!/bin/sh
. /etc/setenv.sh
mode=$1

case "$mode" in
  'start')
    # Start services
        $NMS_HOME/stacks/mnc-rproxy/mnc-rproxy-ctl.sh start
        $NMS_HOME/stacks/core/core-ctl.sh start
        $NMS_HOME/stacks/csm/csm-ctl.sh start
        $NMS_HOME/fsp_nm_ni/ni-ctl.sh start
    ;;

  'stop')
    # Stop services
        $NMS_HOME/fsp_nm_ni/ni-ctl.sh stop
        $NMS_HOME/stacks/csm/csm-ctl.sh stop
        $NMS_HOME/stacks/core/core-ctl.sh stop
        $NMS_HOME/stacks/mnc-rproxy/mnc-rproxy-ctl.sh stop
    ;;
    *)
      echo "Usage: $0  {start|stop}  [  MOD options ]"
      exit 1
    ;;
esac
exit 0
