#!/bin/bash

typeset -r dirOfThisScript=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

TIMESTAMP_FORMAT="+%Y-%m-%d_%H-%M-%S"

# Directories where temporary files will be stored
TAR_DIR='/var/tmp'
LOG_DIR="${TAR_DIR}/healthcheck_stacks"
# Output tarball file name with current timestamp
TAR_FILE="${LOG_DIR}_$(date "$TIMESTAMP_FORMAT").tgz"
# Log file name
LOG_FILE='sdp.log'
DOCKER_STS_LOGFILE='docker-status.log'
DOCKER_STS_LOGFILE_PATH="${LOG_DIR}/${DOCKER_STS_LOGFILE}"
DOCKER_DAEMON_LOGFILE='docker-daemon.log'
DOCKER_DAEMON_LOGFILE_PATH="${LOG_DIR}/${DOCKER_DAEMON_LOGFILE}"

# Paths to the control scripts
CSM_SCRIPT=${dirOfThisScript}/csm/csm-ctl.sh
CORE_SCRIPT=${dirOfThisScript}/core/core-ctl.sh
RPROXY_SCRIPT=${dirOfThisScript}/mnc-rproxy/mnc-rproxy-ctl.sh
SERVICE_MIGRATION_SCRIPT=${dirOfThisScript}/service-migration/service-migration-ctl.sh

# Healthcheck file names
CSM_HEALTHCHECK='healthcheck_csm.tgz'
CORE_HEALTHCHECK='healthcheck_mnc-core.tgz'
RPROXY_HEALTHCHECK='healthcheck_mnc-rproxy.tgz'
SERVICE_MIGRATION_HEALTHCHECK='healthcheck_service-migration.tgz'

# List of stack names
STACK_NAMES=("csm" "mnc-core" "mnc-rproxy" "service-migration")

# List of services to check
# This file should contain the names of the services you want to check
# in the format: <service_name>
SERVICE_LIST="/opt/adva/stacks/service_list"
CONTAINER_DIR="/var/lib/docker/containers"
DEST_DIR="./logs_output"

printInfoMessage() {
    echo [$(date "$TIMESTAMP_FORMAT"]) $@
}

required_commands=("docker" "tar" "date")
for cmd in "${required_commands[@]}"; do
    if ! command -v "$cmd" &>/dev/null; then
        echo "Error: Required command '$cmd' is not installed." >&2
        exit 1
    fi
done

# Log and display information messages with timestamps
logInfo() {
    local message=$1
    echo "[$(date "$TIMESTAMP_FORMAT")] INFO: $message" | tee -a "${LOG_DIR}/${LOG_FILE}"
}

# Log and display error messages with timestamps
logError() {
    local message=$1
    echo "[$(date "$TIMESTAMP_FORMAT")] ERROR: $message" | tee -a "${LOG_DIR}/${LOG_FILE}" >&2
}

# Function to generate a list of services in the stack
get_and_copy_logs() {
    while IFS= read -r service_name; do
        echo "==> Processing service: $service_name"

        for container_path in "$CONTAINER_DIR"/*; do
            config_file="$container_path/config.v2.json"
            container_id=$(basename "$container_path")
            log_file="$container_path/${container_id}-json.log"

            if [[ -f "$config_file" ]]; then
                container_service_name=$(jq -r '.Config.Labels["com.docker.swarm.service.name"]' "$config_file" 2>/dev/null)

                if [[ "$container_service_name" == "$service_name" ]]; then
                    service_dir="${DEST_DIR}/${service_name}"
                    mkdir -p "$service_dir"

                    # Use the mtime of the config file as a timestamp
                    timestamp=$(date -d @"$(stat -c %Y "$config_file")" +"%Y%m%d_%H%M")

                    echo "  -> Found container for $service_name at $timestamp"

                    cp "$config_file" "$service_dir/${timestamp}_config.v2.json"

                    if [[ -f "$log_file" ]]; then
                        cp "$log_file" "$service_dir/${timestamp}_${service_name}.log"
                    else
                        echo "  Warning: Log file not found for container $container_id"
                    fi
                fi
            fi
        done
        echo ""
    done <"$SERVICE_LIST"
}

# Run a components script and log its output, if the script exists
runComponentScript() {
    local script_path=$1
    local component_name=$2

    if [ ! -f "$script_path" ]; then
        logError "Control script for ${component_name} does not exist: ${script_path}"
        return 1
    else
        logInfo "Running ${component_name} script: ${script_path}"
        "$script_path" logs | tee -a "${LOG_DIR}/${LOG_FILE}"
        return $?
    fi
}

# Cleanup function to remove temporary files
cleanup() {
    logInfo "Cleanup started"
    rm -rf "${LOG_DIR}"
    printInfoMessage "INFO: Cleanup finished"
}

# generate docker status logfile
dockerStatusLog() {
    declare -A commands_map

    logInfo "Generating ${DOCKER_STS_LOGFILE_PATH}"

    commands_map=(["docker ps"]="docker ps"
        ["docker services list"]="docker service ls"
        ["docker stacks"]="docker stack ls"
        ["docker volumes"]="docker volume ls"
        ["docker networks"]="docker network ls"
        ["docker secrets"]="docker secret ls"
        ["docker image"]="docker image ls"
        ["docker system df"]="docker system df"
        ["docker statistics"]="docker stats --no-stream"
        ["docker system info"]="docker system info"
        ["docker node"]="docker node ps")

    # Add a separate loop for running `docker stack ps` for each stack
    for stack_name in "${STACK_NAMES[@]}"; do
        output="============docker stack ps $stack_name====================\n"
        output+=$(docker stack ps "$stack_name" --no-trunc)
        output+="\n\n\n"

        echo -e "$output" >>"$DOCKER_STS_LOGFILE_PATH"
        [[ "$TC_LOG" == "true" ]] && echo -e "$output"

        logInfo "Executed docker stack ps for $stack_name"
    done

    for i in "${!commands_map[@]}"; do
        output="============$i====================\n"
        output+=$(${commands_map[$i]})
        output+="\n\n\n"

        echo -e "$output" >>"$DOCKER_STS_LOGFILE_PATH"
        [[ "$TC_LOG" == "true" ]] && echo -e "$output"
    done

    logInfo "${DOCKER_STS_LOGFILE_PATH} created"
}

# generate docker daemon logfile
dockerDaemonLog() {
    logInfo "Generating ${DOCKER_DAEMON_LOGFILE_PATH}"
    journalctl -u docker.service | grep error >>$DOCKER_DAEMON_LOGFILE_PATH
    logInfo "${DOCKER_DAEMON_LOGFILE_PATH} created"
}

# Main process starts here
# Check for main log directory existency
if [ -d "${LOG_DIR}" ]; then
    printInfoMessage "Removing main log directory"
    rm -rf "${LOG_DIR}"
fi

# Create main log directory
mkdir -p "${LOG_DIR}"
logInfo "Creating main log directory"

logInfo "Script started"

# Run health check for CSM
runComponentScript "$CSM_SCRIPT" "CSM"
# Run health check for CORE
runComponentScript "$CORE_SCRIPT" "CORE"
# Run health check for RPROXY
runComponentScript "$RPROXY_SCRIPT" "RPROXY"
# Run health check for SERVICE_MIGRATION
runComponentScript "$SERVICE_MIGRATION_SCRIPT" "SERVICE_MIGRATION"

# Run docker-status.log creation
dockerStatusLog

# Run docker-daemon.log creation
dockerDaemonLog

# Remove old helath check mod logs
rm -rf "${TAR_DIR}"/healthcheck_stacks_*.tgz

# Dynamically build the list of files to include in the tarball
files_to_archive=()

# Include all healthcheck tgz files (with timestamps)
for file in "${LOG_DIR}"/healthcheck_*.tgz; do
    [ -f "$file" ] && files_to_archive+=("$(basename "$file")")
done

# Include main log files if they exist
[ -f "${LOG_DIR}/${LOG_FILE}" ] && files_to_archive+=("${LOG_FILE}")
[ -f "${DOCKER_STS_LOGFILE_PATH}" ] && files_to_archive+=("${DOCKER_STS_LOGFILE}")
[ -f "${DOCKER_DAEMON_LOGFILE_PATH}" ] && files_to_archive+=("${DOCKER_DAEMON_LOGFILE}")

# Run tar only if there are files to archive
if [ ${#files_to_archive[@]} -gt 0 ]; then
    logInfo "Combining health checks and logs into ${TAR_FILE}"
    tar -C "${LOG_DIR}" -czhf "${TAR_FILE}" "${files_to_archive[@]}"

    if [ $? -eq 0 ]; then
        printInfoMessage "INFO: Script finished successfully ${TAR_FILE}"
    else
        printInfoMessage "Failed to create tarball: ${TAR_FILE}"
        exit 1
    fi
else
    logError "No files to archive, skipping tar creation."
fi

cleanup

# Cleanup is handled by the trap
printInfoMessage "INFO: Script finished successfully"
