#!/bin/sh
STACK_FILE=/opt/adva/stacks/core/docker-stack.yml

# ensure mnc-core stacl is not running
start_time=$(date +%s)
while true; do
  if docker service ls | grep -q mnc-core; then
    echo "Waiting until mnc-core stack will be stopped"
    sleep 5
  else
    echo "mnc-core stack is not running"
    sleep 5
    break
  fi
  # ensure waiting will be no longer than 2 minutes
  current_time=$(date +%s)
  elapsed_time=$((current_time - start_time))
  if [ $elapsed_time -ge 120 ]; then
    echo "Timeout 2 minutes for waiting till mnc-core stack will be stopped reached"
    exit 1
  fi
done

# remove all volumes from mnc-core stack
volumes=( mnc-core_kafka-data mnc-core_kafka-config mnc-core_job-manager-tmp mnc-core_job-manager-var mnc-core_mnc-web-logs mnc-core_mnc-web-set-db mnc-core_notification-manager-app mnc-core_notification-manager-tmp )
for volume in "${volumes[@]}"; do
  docker volume rm "$volume"
done

# update kafka variables
patterns=(
  's|KAFKA_CFG_ADVERTISED_LISTENERS: CLIENT://kafka:9092,EXTERNAL://${INFRA_KAFKA_ADVERTISED_HOST}:9200|KAFKA_CFG_ADVERTISED_LISTENERS: CLIENT://kafka:9092,EXTERNAL://${INFRA_KAFKA_ADVERTISED_HOST}:9200,EXTERNAL_TEST://${INFRA_KAFKA_ADVERTISED_HOST}:9999|'
  's|KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: CLIENT:PLAINTEXT,EXTERNAL:SASL_PLAINTEXT,CONTROLLER:PLAINTEXT|KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: CLIENT:PLAINTEXT,EXTERNAL:SASL_PLAINTEXT,EXTERNAL_TEST:PLAINTEXT,CONTROLLER:PLAINTEXT|'
  's|KAFKA_CFG_LISTENERS: CLIENT://:9092,EXTERNAL://:9094,CONTROLLER://:9093|KAFKA_CFG_LISTENERS: CLIENT://:9092,EXTERNAL://:9094,EXTERNAL_TEST://:9091,CONTROLLER://:9093|'
)

for pattern in "${patterns[@]}"; do
  sed -i "$pattern" "$STACK_FILE"
done


# add ports section to kafka service
cd /opt/adva/stacks/core || exit 1
awk '/^[[:space:]]*kafka:[[:space:]]*$/ {print; found=1; next} found && !added && /^[[:space:]]*$/ {print "    ports:\n      - \"9999:9091\""; added=1; next} {print}' docker-stack.yml > temp.yml && mv temp.yml docker-stack.yml