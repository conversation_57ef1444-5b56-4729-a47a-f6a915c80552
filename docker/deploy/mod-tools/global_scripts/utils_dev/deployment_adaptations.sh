#!/bin/bash

# ============================================
# MNC & MOD Deployment Adaptation Script
# ============================================
#
# USAGE:
# 1. Install MNC and MOD using the Linux package distribution.
# 2. Ensure all required services are running by adjusting `fnm.properties`
#    and configuring `csm` accordingly.
# 3. Place content of utils_dev directory in one location on your host
# 4. Since docker swarm is not able to pull kafka-ui image from artifactory,
#    you need to pull the image manually using 'docker pull' command
# 5. Run this script only once:
#    ./deployment_adaptations.sh
# 6. Access Kafka UI using the IP address of your MNC instance on port 9998:
#    http://<MNC_IP>:9998

# Set variables
MGR_DIR=/opt/adva/fsp_nm
typeset -r dirOfThisScript=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

# Stop NMS and MOD
"$MGR_DIR"/bin/nmsadmin.sh B || { echo "Failed to stop NMS/MOD"; exit 1; }

# Execute script to update kafka variables
if [ -f "${dirOfThisScript}/kafka_adpt.sh" ]; then
  source "${dirOfThisScript}/kafka_adpt.sh"
else
  echo "Error: kafka_adpt.sh not found."
  exit 1
fi

# Start NMS and MOD
"$MGR_DIR"/bin/nmsadmin.sh S || { echo "Failed to start NMS/MOD"; exit 1; }

# Deploy kafka UI
docker stack deploy -c "${dirOfThisScript}"/docker-stack.yml mnc-core
if [[ $? -ne 0 ]]; then
  echo "Error occurred during deploying kafka UI service."
  exit 1
else
  echo "Kafka UI service deployed successfully."
fi