#!/bin/bash

typeset -r dirOfThisScript=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
source "${dirOfThisScript}"/docker_helper.sh
cd "${dirOfThisScript}" || exit 1

ID="stack_name"
HELP_MSG="Usage: ./deploy.sh [--id <deploy-id>]"

COMPOSE_FILES="../docker-stack.yml"

# Parse arguments
while [[ "$#" -gt 0 ]]; do
  case $1 in
  --id)
    if [ -n "$2" ]; then
      ID="$2"
      shift 2
    else
      echo "Error: --id requires a stack name"
      echo "$HELP_MSG"
      exit 1
    fi
    ;;
  *)
    echo "Unknown option: $1"
    echo "$HELP_MSG"
    exit 1
    ;;
  esac
done

actionMsg="Deploy all services: docker stack deploy ${COMPOSE_FILES} ${ID}"
echo "Start: ${actionMsg}"

get_host_tz
replace_utc_with_local_tz

if [ -"$(docker stack ls | grep -c "$ID")" -eq 0 ]; then
  docker stack deploy -c "${COMPOSE_FILES}" "${ID}"
  deploy_status=$?
  if [ $deploy_status -ne 0 ]; then
    echo "Failed to deploy stack '${ID}'"
    exit 1
  fi
else
  echo "Stack '${ID}' already exists"
  exit 1
fi

echo "End: ${actionMsg}"
