#!/bin/bash

typeset -r scriptDir=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

REPO=$REPO
DOCKER_STACK_BUILD='docker-stack.yml'
IMAGES_DIR="${scriptDir}/../docker_images"

function load_image() {
  echo "Load ${1} as a docker image"
  docker image load -q -i "$1"
  if [ $? -ne 0 ]; then
    echo "Failed to load image ${1}"
    exit 1
  fi
}

function update_tag() {
  local image=$1
  local version=$2
  docker image tag $REPO/$image:$version $REPO/$image:latest
  if [ $? -ne 0 ]; then
    echo "Failed to tag image ${REPO}/${image}:${version}"
    exit 1
  fi
}

if [ -d "$IMAGES_DIR" ]; then
  shopt -s nullglob
  tar_files=("$IMAGES_DIR"/*.tar)
  shopt -u nullglob

  if [ ${#tar_files[@]} -eq 0 ]; then
    echo "No .tar files found in directory '$IMAGES_DIR'. No docker images to import, interrupting installation."
    exit 1
  fi

  for file in "${tar_files[@]}"; do
    load_image "$file"
  done
else
  if [ ! -f "$DOCKER_STACK_BUILD" ]; then
    echo "Could not find directory '$IMAGES_DIR' and '$DOCKER_STACK_BUILD' file is missing. No docker images to import, interrupting installation."
    exit 1
  else
    echo "Using $DOCKER_STACK_BUILD to get docker images."
  fi
fi
