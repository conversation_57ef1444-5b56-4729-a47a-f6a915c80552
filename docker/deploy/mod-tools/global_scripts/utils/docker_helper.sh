#!/bin/bash

typeset -r currentdir=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
source "${currentdir}"/../../defaults.sh

CONTAINER_DIR="/var/lib/docker/containers"

printInfoMessage() {
  echo [$(date +"%Y-%m-%d_%H-%M-%S"]) $@
}

checkIfStackContainersExistWithRetries() {
  local stackName=$1
  local -i retries=$2
  local -i exit_code=0
  for i in $(eval "echo {1..${retries}}"); do
    if [ "$(checkIfStackContainersExist "${stackName}")" == "false" ]; then
      printInfoMessage "Containers for stack ${stackName} are successfully removed."
      exit_code=0
      break
    else
      printInfoMessage "Containers for stack ${stackName} still exist, wait and retry (${i}):"
      exit_code=1
      sleep 3
    fi
  done
  return ${exit_code}
}

checkIfStackContainersExist() {
  local stackName=$1
  exist="false"
  numberOfContainers=$(docker ps -aqf "label=com.docker.stack.namespace=${stackName}" | wc -l)
  if [ "$numberOfContainers" -gt 0 ]; then
    exist="true"
  fi
  echo ${exist}
}

forceDeleteContainerOfStacksWithRetries() {
  local stackName=$1
  local -i retries=$2
  printInfoMessage "Force delete containers for stack: ${stackName}"
  for i in $(eval "echo {1..${retries}}"); do
    if [ "$(getNumberOfContainersForStack "$stackName")" -gt 0 ]; then
      printInfoMessage "Trying to remove containers for stack ${stackName} (${i})"
      forceDeleteContainerOfStack "$stackName"
    else
      printInfoMessage "Containers for stack ${stackName} are successfully removed"
      break
    fi
  done

  if [ "$(getNumberOfContainersForStack "$stackName")" -gt 0 ]; then
    printInfoMessage "Failed to delete containers for ${stackName}"
  fi
}

getNumberOfContainersForStack() {
  local stackName="$1"
  docker ps -aqf "name=^/${stackName}_" | wc -l
}

forceDeleteContainerOfStack() {
  local stackName=$1
  docker ps -aqf "label=com.docker.stack.namespace=${stackName}" | xargs docker container rm -f >/dev/null
}

function assertDocker() {
  set +e
  which docker &>/dev/null
  if [ $? -ne 0 ]; then
    printInfoMessage "Command 'docker' not found, make sure docker engine is installed."
    exit 1
  fi

  docker version &>/dev/null
  if [ $? -ne 0 ]; then
    echo "Unable to use 'docker' command, check if:"
    echo " - docker service is running"
    echo " - user has permissions to use docker or belongs to 'docker' group"
    exit 1
  fi

  docker stack ls &>/dev/null
  if [ $? -ne 0 ]; then
    printInfoMessage "Make sure host is participating in a docker swarm."
    exit 1
  fi
  set -e
}

deleteDockerStack() {
  local stackName=$1
  actionMsg="Deleting stack ${stackName}"
  printInfoMessage "Start: ${actionMsg}"

  stopRelatedContainers "${stackName}"
  if [ $? -ne 0 ]; then
    printInfoMessage "Failed to stop related containers for ${stackName}"
    return 1
  fi

  docker stack rm "${stackName}"
  if [ $? -ne 0 ]; then
    printInfoMessage "Failed to remove stack ${stackName}"
    return 1
  fi

  waitForStackNetworkRemoval "$stackName"
  if [ $? -ne 0 ]; then
    printInfoMessage "Failed to remove network for stack ${stackName}"
    return 1
  fi

  printInfoMessage "End: ${actionMsg}"
  return 0
}

stopRelatedContainers() {
  local stackName=$1

  local cliContainers=$(docker ps -q -f "name=${stackName}")
  for id in ${cliContainers}; do
    printInfoMessage "Stopping ${id}"
    docker stop "${id}"
    if [ $? -ne 0 ]; then
      return 1
    fi
    docker rm -f "${id}"
    if [ $? -ne 0 ]; then
      return 1
    fi
  done
  return 0
}

waitForStackNetworkRemoval() {
  local stackName=$1
  while [ "$(docker network ls --filter label=com.docker.stack.namespace="$stackName" | grep -c swarm || true)" -ne 0 ]; do
    sleep 1
  done
  return 0
}

# Function to check the status of the deployed stack
check_stack_status() {
  local stackName=$1

  echo
  actionMsg="Check stack status for ${stackName}"
  printInfoMessage "Start: ${actionMsg}"

  services=$(docker stack services "$stackName" --format "{{.Name}}: {{.Replicas}}")

  if [ -n "$services" ]; then
    printInfoMessage "Services found in stack: ${stackName}"
    echo "$services"

    # Check if all services are running
    all_running=true
    while IFS= read -r service; do
      replicas=$(echo "$service" | cut -d: -f2 | xargs)
      desired=$(echo "$replicas" | cut -d/ -f2)
      current=$(echo "$replicas" | cut -d/ -f1)

      if [ "$desired" != "$current" ]; then
        all_running=false
        printInfoMessage "One or more services are not running as expected for stack: ${stackName}."
        break
      fi
    done <<<"$services"

    if $all_running; then
      printInfoMessage "All services are running as expected for stack: ${stackName}."
      return 0
    else
      printInfoMessage "One or more services are not running as expected for stack: ${stackName}."
      return 2
    fi
  else
    printInfoMessage "No services found for stack: ${stackName} or stack does not exist."
    return 1
  fi
  printInfoMessage "End: ${actionMsg}" && echo
}

deployDockerStack() {
  local stackName=$1
  bash utils/deploy.sh --id "$stackName"
}

deleteStackVolumes() {
  local stackName="$1"
  printInfoMessage "Deleting volumes for stack ${stackName}"

  # Get the list of volumes
  local volumes=$(docker volume ls --filter "label=com.docker.stack.namespace=${stackName}" -q)

  # Check if volumes are found
  if [ -z "$volumes" ]; then
    printInfoMessage "No volumes found for stack ${stackName}, skipping deletion."
    return
  fi

  # Delete the volumes
  printInfoMessage "$volumes" | xargs docker volume rm >/dev/null
  printInfoMessage "Volumes deleted for stack ${stackName}."
}

# Function to prompt for MNC IP address
read_kafka_adertised_ip() {
  if [ -z "$INFRA_KAFKA_ADVERTISED_HOST" ]; then
    if [ -z "$MNC_HOST" ]; then
      if [ -f "$STACKS_DIR/mnchost" ]; then
        ip=$(<$STACKS_DIR/mnchost)
        export MNC_HOST=$ip
        export INFRA_KAFKA_ADVERTISED_HOST=$ip
      else
        printInfoMessage "MNC_HOST and INFRA_KAFKA_ADVERTISED_HOST is not set or is empty"
        read -p "Please provide the Kafka Advertised IP address (same as MNC): " INFRA_KAFKA_ADVERTISED_HOST_VAR
        export INFRA_KAFKA_ADVERTISED_HOST=$INFRA_KAFKA_ADVERTISED_HOST_VAR
        export MNC_HOST=$INFRA_KAFKA_ADVERTISED_HOST_VAR
      fi
    else
      printInfoMessage "MNC_HOST is set to '$MNC_HOST'. Using it for INFRA_KAFKA_ADVERTISED_HOST."
      export INFRA_KAFKA_ADVERTISED_HOST=$MNC_HOST
    fi
  else
    printInfoMessage "INFRA_KAFKA_ADVERTISED_HOST is already set to '$INFRA_KAFKA_ADVERTISED_HOST'"
  fi
}

# Function to prompt for MNC IP address
read_mnc_ip() {
  if [ -z "$MNC_HOST" ]; then
     if [ -f "$STACKS_DIR/mnchost" ]; then
        ip=$(<$STACKS_DIR/mnchost)
        export MNC_HOST=$ip
     else
      printInfoMessage "MNC_HOST is not set or is empty"
      read -p "Please provide the MNC IP address: " MNC_HOST_VAR
      export MNC_HOST=$MNC_HOST_VAR
     fi
  else
    printInfoMessage "MNC_HOST is set to '$MNC_HOST'"
  fi
}

# Function to read from mnchost file
read_mnchost_file() {
  local mnchost_file="${INSTALL_DIR}"/../mnchost
  if [ -f "$mnchost_file" ] && [ -s "$mnchost_file" ]; then
    local temp_ipaddr=$(cat "$mnchost_file")
      if check_ip_address "$temp_ipaddr"; then
        export MNC_HOST="$temp_ipaddr"
        printInfoMessage "MNC_HOST is set to '$MNC_HOST'."
      else
        printInfoMessage "Invalid IP address found in '$mnchost_file'."
        read_mnc_ip
      fi
  else
    read_mnc_ip
  fi
}

check_ip_address() {
  local ip=$1
  local result=1

  if [[ $ip =~ ^([0-9]{1,3})\.([0-9]{1,3})\.([0-9]{1,3})\.([0-9]{1,3})$ ]]; then
    local o1=${BASH_REMATCH[1]}
    local o2=${BASH_REMATCH[2]}
    local o3=${BASH_REMATCH[3]}
    local o4=${BASH_REMATCH[4]}

    if (( o1 > 255 || o2 > 255 || o3 > 255 || o4 > 255 )); then
      return 1
    fi

    if (( o1 == 127 )); then return 1; fi
    if (( o1 == 0 )); then return 1; fi
    if (( o1 == 169 && o2 == 254 )); then return 1; fi
    if (( o1 >= 224 )); then return 1; fi

    result=0
  fi

  return $result
}

removeDanglingVolumes() {
  action_msg="removing dangling Volumes"
  printInfoMessage "Start ${action_msg}"
  docker volume ls -f "dangling=true" # display dangling volumes that will be removed
  nrOfDanglingVolumes=$(docker volume ls -f "dangling=true" -q | wc -l)
  if [ ${nrOfDanglingVolumes} -gt 0 ]; then
    docker volume rm $(docker volume ls -f "dangling=true" -q)
  else
    printInfoMessage "No Dangling Volumes to remove"
  fi

  printInfoMessage "Finished ${action_msg}"
}

execute() {
  $@ 2>&1
}

getContainerFiles() {
  local stackName=$1
  local appName=$2
  local sourceLogPath=$3

  local healthcheck_dir="${LOG_DIR}/healthcheck_${stackName}"
  local logFiles="${stackName}_${appName}_logs"
  local actionMsg="Get log files from volume for ${stackName}_${appName}"

  printInfoMessage "Start: ${actionMsg}"

  if [ ! -d "${sourceLogPath}" ]; then
    printInfoMessage "Log volume path ${sourceLogPath} does not exist. Skipping..."
    printInfoMessage "End: ${actionMsg}"
    return
  fi

  mkdir -p "${healthcheck_dir}"
  cd "${healthcheck_dir}" || return

  if [ -d "${logFiles}" ]; then
    printInfoMessage "Directory ${logFiles} exists. Removing it..."
    rm -rf "${logFiles}"
  fi

  mkdir "${logFiles}"
  cp -r "${sourceLogPath}/." "${logFiles}/"
  if [ $? -ne 0 ]; then
    printInfoMessage "Failed to copy log files from ${sourceLogPath} to ${logFiles}."
    printInfoMessage "End: ${actionMsg}"
    return 1
  fi

  printInfoMessage "End: ${actionMsg}"
}

getServicesLog() {
  local stackName=$1

  actionMsg="Get service logs"
  printInfoMessage "Start: ${actionMsg}"

  printInfoMessage "Fetching services for stack: ${stackName}"
  stackServices=$(docker stack services "${stackName}" --format "{{.Name}}")
  # Debug: Log the services being processed
  printInfoMessage "Services for stack ${stackName}: ${stackServices}"

  if [[ -z "${stackServices}" ]]; then
    printInfoMessage "No services found for stack: ${stackName}"
    printInfoMessage "End: ${actionMsg}"
    return
  fi

  for stackService in ${stackServices}; do
    printInfoMessage "Processing service: ${stackService}" >>"${stackService}".log

    # Check service replicas
    serviceReplicas=$(docker service ls --filter "name=${stackService}" --format "{{.Replicas}}")
    currentReplicas=$(echo "${serviceReplicas}" | cut -d'/' -f1)
    desiredReplicas=$(echo "${serviceReplicas}" | cut -d'/' -f2)

    for container_path in "$CONTAINER_DIR"/*; do
      config_file="$container_path/config.v2.json"
      container_id=$(basename "$container_path")
      log_file="$container_path/${container_id}-json.log"

      if [[ -f "$config_file" ]]; then
        if command -v jq >/dev/null 2>&1; then
          container_service_name=$(jq -r '.Config.Labels["com.docker.swarm.service.name"]' "$config_file" 2>/dev/null)
          if [[ $? -ne 0 || -z "$container_service_name" ]]; then
            printInfoMessage "Warning: Failed to extract service name using jq for container $container_id"
            continue
          fi
        else
          container_service_name=$(grep -o '"com.docker.swarm.service.name"\s*:\s*"[^"]*"' "$config_file" | sed 's/.*: *"\([^"]*\)".*/\1/')
          if [[ -z "$container_service_name" ]]; then
            printInfoMessage "Warning: Could not extract service name from $config_file for container $container_id"
            continue
          fi
        fi

        if [[ "$container_service_name" == "$stackService" ]]; then
          service_dir="${stackService}_service_logs"
          mkdir -p "$service_dir"

          # Use the mtime of the config file as a timestamp
          timestamp=$(date -d @"$(stat -c %Y "$config_file")" +"%Y%m%d_%H%M")

          printInfoMessage "  -> Found container for $stackService at $timestamp"

          cp "$config_file" "$service_dir/${timestamp}_config.v2.json"

          if [[ -f "$log_file" ]]; then
            cp "$log_file" "$service_dir/${timestamp}_${stackService}.log"
          else
            printInfoMessage "  Warning: Log file not found for container $container_id"
          fi
        fi
      fi
    done
  done

  printInfoMessage "End: ${actionMsg}"
}

gatherLogs() {
  currentDate=`date +"%Y-%m-%d__%H-%M-%S"`
  local healthcheck_dir=${LOG_DIR}/healthcheck_${1}
  local healthcheck_file="${healthcheck_dir}"_"${currentDate}".tgz

  # create healthcheck directory and set it as working directory
  actionMsg="Create healthcheck directory and set it as working directory"
  printInfoMessage "Start: ${actionMsg}"
  cd "${healthcheck_dir}"
  printInfoMessage "End: ${actionMsg}"

  for stack in "$1"; do
    execute getServicesLog "${stack}"
  done

  # create the tar file and delete the healthcheck directory
  cd ..
  execute tar -C "${healthcheck_dir}" -czhf "${healthcheck_file}" .
  execute rm -rf "${healthcheck_dir}"

  printInfoMessage "Healthcheck created: $healthcheck_file"
  printInfoMessage "END $(basename "$0")"
}

collectMigrationData() {
  currentDate=`date +"%Y-%m-%d__%H-%M-%S"`
  local migrationreport_dir=service-migration-status-report_"${currentDate}"
  local migrationreport_full_dir=${LOG_DIR}/${migrationreport_dir}
  local migrationreport_file=service-migration-status-report_"${currentDate}".tar.gz

  # collect migration data
  actionMsg="Collect Service Migration data"
  printInfoMessage "Start: ${actionMsg}"

  # create the tar file
  execute mkdir -p ${migrationreport_full_dir}
  execute mkdir -p ${migrationreport_full_dir}/log
  execute mkdir -p ${migrationreport_full_dir}/reports
  execute cp -r ${SOURCE_LOG}/_data/* ${migrationreport_full_dir}/log
  execute cp -r ${SOURCE_REPORTS}/_data/* ${migrationreport_full_dir}/reports
  execute tar -C "${LOG_DIR}" -czhf "${LOG_DIR}/${migrationreport_file}" ${migrationreport_dir}

  printInfoMessage "service migration report created: ${LOG_DIR}/${migrationreport_file}"
  printInfoMessage "End: ${actionMsg}"
  printInfoMessage "END $(basename "$0")"
}

getContainerFile() {
  execute getContainerFiles "$1" "$2" "$3"
}

deleteDockerSecrets() {
  local stackName="$1"
  local secrets=$(docker secret ls --format "{{.ID}} {{.Name}}" | grep "^.* ${stackName}")

  if [ -z "$secrets" ]; then
    printInfoMessage "No secrets found with prefix '${stackName}'."
    return 0
  fi

  printInfoMessage "Deleting secrets with prefix '${stackName}':"
  printInfoMessage "$secrets"

  while IFS= read -r line; do
    local secret_id=$(echo "$line" | awk '{print $1}')
    docker secret rm "$secret_id"
    printInfoMessage "Deleted secret: $secret_id"
  done <<<"$secrets"
}

deleteDockerConfigs() {
  local stackName="$1"
  docker config ls --filter name="${stackName}-*" --format '{{.Name}}' | xargs -I {} docker config rm {}
}

get_host_tz() {
  HOST_TZ=$(ls -al /etc/localtime | awk -F'zoneinfo/' '{print $2}')
  printInfoMessage "Host timezone: $HOST_TZ"
}

replace_utc_with_local_tz() {
  # Check if HOST_TZ is not empty
  if [ -z "$HOST_TZ" ]; then
    printInfoMessage "HOST_TZ is empty. Exiting without replacing."
    return 1
  fi

  # Search for all files matching *-stack.yml in the directory $AD/MOD_CSM
  find "../" -type f -name "*-stack.yml" | while read -r file; do
    # Check if the file contains the string "TZ: UTC"
    if grep -q "TZ: UTC" "$file"; then
      # Perform the replacement with sed
      sed -i "s|TZ: UTC|TZ: $HOST_TZ|g" "$file"
      printInfoMessage "Replaced 'UTC' with '$HOST_TZ' in $file"
    fi
  done
}

handle_certificates() {
  local certs_dir=$1

  mkdir -p "$certs_dir" && chmod 755 "$certs_dir"

  if [ "$(ls -A "$certs_dir")" ]; then
    printInfoMessage "There are certificates in $certs_dir - please make sure they are valid"
  else
    printInfoMessage "Generating a self-signed certificate in $certs_dir"
    openssl ecparam -name secp384r1 -genkey -noout -out "$certs_dir/server.pem"
    openssl req -new -key "$certs_dir/server.pem" -out "$certs_dir/server.csr" -sha256 -subj "/CN=adtran.com/OU=Mosaic Network Controller/O=Adtran Networks SE/L=/ST=/C=US"
    openssl x509 -req -in "$certs_dir/server.csr" -signkey "$certs_dir/server.pem" -out "$certs_dir/server.crt" -days 3650 -sha256
    openssl ec -in "$certs_dir/server.pem" -out "$certs_dir/server.key"
    chmod 644 "$certs_dir/server.key"
  fi
}

update_stack_env_var() {
  local full_service_name=$1 # Example: mnc-core_job-manager
  local env_var=$2           # VERIFY_CERTS
  local new_value=$3         # true or false
  local stack_file="docker-stack.yml"

  # Extract the actual service name by removing the stack prefix
  local service_name=${full_service_name#*_} # Removes eg. "mnc-core_", leaving "job-manager"

  # Update the environment variable in the corresponding service block using awk
  awk -v srv="$service_name" -v val="$new_value" '
    BEGIN { in_service=0 }
    /^  [a-zA-Z0-9_-]+:/ { in_service=($0 ~ "^  " srv ":"); }
    in_service && /VERIFY_CERTS:/ { sub(/VERIFY_CERTS: .*/, "VERIFY_CERTS: \"" val "\""); }
    { print }
  ' "$stack_file" >temp.yml && mv temp.yml "$stack_file"
  printInfoMessage -e "\e[1;32mUpdated $stack_file for service $service_name to ${env_var}=${new_value}.\e[0m"
}

set_verify_certs_false() {
  export VERIFY_CERTS=false
  printInfoMessage "Environment variable VERIFY_CERTS set to false."
}

check_verify_certs() {
  local stackName=$1

  actionMsg="Check VERIFY_CERTS environment variable in services"
  echo
  printInfoMessage "Start: ${actionMsg}"

  stackServices=$(docker stack services "${stackName}" --format "{{.Name}}")

  if [[ -z "${stackServices}" ]]; then
    printInfoMessage "No services found for stack: ${stackName}"
    printInfoMessage "End: ${actionMsg}"
    return
  fi

  for stackService in ${stackServices}; do
    # Get environment variables for the service's tasks
    serviceTasks=$(docker service ps --filter "desired-state=running" "${stackService}" --format "{{.ID}}")

    if [[ -z "${serviceTasks}" ]]; then
      printInfoMessage "No tasks found for service: ${stackService}"
      continue
    fi

    for taskId in ${serviceTasks}; do
      # Get the container ID for the task
      containerId=$(docker inspect --format '{{.Status.ContainerStatus.ContainerID}}' "${taskId}" 2>/dev/null)

      if [[ -z "${containerId}" ]]; then
        printInfoMessage "No container associated with task: ${taskId} for service: ${stackService}"
        continue
      fi

      # Check VERIFY_CERTS environment variable in the container
      verifyCertsValue=$(docker inspect --format '{{range .Config.Env}}{{if eq . "VERIFY_CERTS=false"}}false{{end}}{{if eq . "VERIFY_CERTS=true"}}true{{end}}{{end}}' "${containerId}" 2>/dev/null)

      if [[ "${verifyCertsValue}" == "false" ]]; then
        printInfoMessage "Service: ${stackService} - Certificate Verification is Disabled."
      elif [[ "${verifyCertsValue}" == "true" ]]; then
        printInfoMessage "Service: ${stackService} - Certificate Verification is Enabled."
      else
        printInfoMessage "Service: ${stackService} - VERIFY_CERTS environment variable not set."
      fi
    done
  done

  printInfoMessage "End: ${actionMsg}"
}

# Function to get the current VERIFY_CERTS state for a specific service
get_service_state() {
  local service_name=$1
  docker service inspect "$service_name" --format '{{ range .Spec.TaskTemplate.ContainerSpec.Env }}{{ . }}{{ "\n" }}{{ end }}' |
    grep "VERIFY_CERTS=" | cut -d '=' -f2
}

# List all services in the stack with VERIFY_CERTS configured
list_services_with_cert_verification() {
  local stack_id=$1
  docker service ls --filter "label=com.docker.stack.namespace=${stack_id}" --format '{{ .Name }}' | while read -r service_name; do
    if docker service inspect "$service_name" --format '{{ range .Spec.TaskTemplate.ContainerSpec.Env }}{{ println . }}{{ end }}' | grep -q '^VERIFY_CERTS='; then
      echo "$service_name"
    fi
  done
}

# Update the VERIFY_CERTS environment variable for a service
update_service_env() {
  local service_name=$1
  local new_value=$2

  printInfoMessage "Updating VERIFY_CERTS for service: $service_name to $new_value..."
  docker service update --env-add "VERIFY_CERTS=${new_value}" "$service_name"

  if [ $? -eq 0 ]; then
    printInfoMessage -e "\e[1;32mSuccessfully updated and redeployed $service_name.\e[0m"
  else
    printInfoMessage -e "\e[1;31mFailed to update $service_name. Please check logs.\e[0m"
  fi
}

# Set Certificate Verification for all relevant services in the stack
set_certs_verify() {
  local stack_id=$1
  local services
  services=$(list_services_with_cert_verification "$stack_id")

  check_verify_certs "$stack_id"

  if [ -z "$services" ]; then
    echo -e "\e[1;31mNo services found with VERIFY_CERTS configured in stack $stack_id.\e[0m"
    exit 0
  fi

  echo -e "\nWhat would you like to do?"
  echo "1. Enable Certificate Verification for the whole $stack_id stack (true)"
  echo "2. Disable Certificate Verification for the whole $stack_id stack (false)"
  echo "3. Set Certificate Verification for each service one by one in the $stack_id stack"
  echo "4. Cancel action"
  echo
  read -p "Enter your choice (1-4): " user_choice
  echo ""

  case $user_choice in
  1)
    echo "Enabling Certificate Verification for all services in stack $stack_id..."
    for service in $services; do
      current_state=$(get_service_state "$service")
      if [ "$current_state" == "true" ]; then
        echo -e "\e[1;33mService $service already has Certificate Verification enabled. Skipping...\e[0m"
        update_stack_env_var "$service" "VERIFY_CERTS" "$current_state"s >/dev/null 2>&1
      else
        update_service_env "$service" "true"
        update_stack_env_var "$service" "VERIFY_CERTS" "true"
      fi
    done
    ;;
  2)
    echo "Disabling Certificate Verification for all services in stack $stack_id..."
    for service in $services; do
      current_state=$(get_service_state "$service")
      if [ "$current_state" == "false" ]; then
        echo -e "\e[1;33mService $service already has Certificate Verification disabled. Skipping...\e[0m"
        update_stack_env_var "$service" "VERIFY_CERTS" "$current_state" >/dev/null 2>&1
      else
        update_service_env "$service" "false"
        update_stack_env_var "$service" "VERIFY_CERTS" "false"
      fi
    done
    ;;
  3)
    echo "Setting Certificate Verification for each service one by one..."
    for service in $services; do
      local current_state
      current_state=$(get_service_state "$service")

      echo -e "\nService: \e[1;34m${service}\e[0m"
      echo -e "Current Certificate Verification state: \e[1;34m${current_state:-false}\e[0m"
      echo "Would you like to update the state for this service?"
      echo "1. Enable Certificate Verification (true)"
      echo "2. Disable Certificate Verification (false)"
      echo "3. Skip this service"
      read -p "Enter your choice (1-3): " service_choice
      echo ""

      case $service_choice in
      1)
        if [ "$current_state" == "true" ]; then
          echo -e "\e[1;33mService $service already has Certificate Verification enabled. Skipping...\e[0m"
          update_stack_env_var "$service" "VERIFY_CERTS" "$current_state" >/dev/null 2>&1
        else
          update_service_env "$service" "true"
          update_stack_env_var "$service" "VERIFY_CERTS" "true"
        fi
        ;;
      2)
        if [ "$current_state" == "false" ]; then
          echo -e "\e[1;33mService $service already has Certificate Verification disabled. Skipping...\e[0m"
          update_stack_env_var "$service" "VERIFY_CERTS" "$current_state" >/dev/null 2>&1
        else
          update_service_env "$service" "false"
          update_stack_env_var "$service" "VERIFY_CERTS" "false"
        fi
        ;;
      3)
        echo "Skipping $service."
        # Ensure the current state is written back to `docker-stack.yml`
        update_stack_env_var "$service" "VERIFY_CERTS" "$current_state" >/dev/null 2>&1
        ;;
      *)
        echo "Invalid choice. Skipping $service."
        update_stack_env_var "$service" "VERIFY_CERTS" "$current_state" >/dev/null 2>&1
        ;;
      esac
    done
    ;;
  4)
    echo "Action canceled."
    exit 0
    ;;
  *)
    echo "Invalid choice. Exiting."
    exit 1
    ;;
  esac

  echo -e "\n\e[1;32mCertificate Verification update process completed.\e[0m"
}

# Function to create a Docker secret
create_db_secret() {
  local secret_name=$1
  local secret_username=$2

  # Check if the secret already exists
  if docker secret ls | grep -q "$secret_name"; then
    printInfoMessage "Secret $secret_name already exists. Skipping creation."
  else
    # Prompt the user for the encrypted database password
    echo -n "Enter the database password: "
    read -s db_password
    echo

    # Create a temporary file to store the password
    local temp_file=$(mktemp)
    local secret_file=$temp_file

    # Write the password to the temporary file
    echo "$db_password" >"$temp_file"

    if [ -f "$secret_file" ]; then
      docker secret create "$secret_name" "$secret_file"
      if docker secret ls | grep -q "$secret_name"; then
        printInfoMessage "Secret $secret_name created successfully."
      else
        printInfoMessage "Failed to create secret $secret_name."
      fi
    else
      printInfoMessage "Secret file $secret_file does not exist."
    fi

    # Remove the temporary file
    rm -f "$temp_file"
  fi
}

execute_migration_script() {
  local stack_name="$1"
  local service_container_name="$2"
  local service_name="${stack_name}_${service_container_name}" # Construct service name

  # Get the container ID of the running service
  local container_id
  container_id=$(docker ps --filter "name=${service_name}" --format "{{.ID}}" | head -n 1)

  if [[ -z "$container_id" ]]; then
    printInfoMessage "Error: No running container found for service ${service_name}. Ensure the stack is started."
    return 1
  fi

  printInfoMessage "Executing migration script inside container ${container_id}..."

  # Run the script inside the container
  docker exec -it "$container_id" /app/scripts/run

  if [[ $? -eq 0 ]]; then
    printInfoMessage "Migration script executed successfully."
  else
    printInfoMessage "Migration script execution failed."
    return 1
  fi
}

execute_status_script() {
  local stack_name="$1"
  local service_container_name="$2"
  local service_name="${stack_name}_${service_container_name}" # Construct service name

  # Get the container ID of the running service
  local container_id
  container_id=$(docker ps --filter "name=${service_name}" --format "{{.ID}}" | head -n 1)

  if [[ -z "$container_id" ]]; then
    printInfoMessage "Error: No running container found for service ${service_name}. Ensure the stack is started."
    return 1
  fi

  printInfoMessage "Executing status script for ${container_id}..."

  # Run the status script inside the container and capture output
  local status_output
  status_output=$(docker exec -it "$container_id" /app/scripts/status 2>&1)
  local exit_code=$?

  if [[ $exit_code -eq 0 ]]; then
    printInfoMessage "Status script executed successfully."
    printInfoMessage "$status_output"
  else
    printInfoMessage "Status script execution failed. Output: $status_output"
    return 1
  fi
}

clean_kafka_data() {
  action_msg="Deleting kafka data volume"
  printInfoMessage "Start: ${action_msg}"
  sleep 5
  docker volume rm mnc-core_kafka-data
  printInfoMessage "End: ${action_msg}"
}

clean_kafka_config() {
  action_msg="Deleting kafka config volume"
  printInfoMessage "Start: ${action_msg}"
  sleep 5
  docker volume rm mnc-core_kafka-config
  printInfoMessage "End: ${action_msg}"
}

collect_kafka_details(){
  local stack_name="$1"
  local hc_dir="$2"
  local logs_dir="$hc_dir"/"$stack_name"_kafka_details
  local kafka_details_logfile="kafka_details.log"
  local kafka_details_log="$logs_dir"/"$kafka_details_logfile"
  local cmd_timeout=30
  local  kafka_container_id
  kafka_container_id=$(docker ps -q -f name="$stack_name"_kafka)

  if [[ -z "$kafka_container_id" ]]; then
        kafka_container_id="NO KAFKA CONTAINER FOUND!!!"
        printInfoMessage "$kafka_container_id"
  fi

  mkdir -p "$logs_dir"

  printInfoMessage "Start: Collecting kafka details"

  declare -A commands_kafka
  commands_kafka=( ["topics list"]="docker exec $kafka_container_id kafka-topics.sh --list --bootstrap-server localhost:9092" )

  for i in "${!commands_kafka[@]}"; do
        problem="Gathering output of ${commands_kafka[$i]} failed or timeouted in $cmd_timeout seconds"

        output="============$i====================\n"
        # shellcheck disable=SC2086
        output+=$(timeout $cmd_timeout ${commands_kafka[$i]} || echo "$problem")
        output+="\n\n\n"

        echo -e "$output" >> "$kafka_details_log"
        [[ "$TC_LOG" == "true" ]] && echo -e "$output"
  done

  printInfoMessage "End: Collecting kafka details"
}

create_docker_config() {
  local config_name="$1"
  local file_path="${2:-config/kafka/log4j.properties}"

  if [[ ! -f "$file_path" ]]; then
    printInfoMessage "Error: File '$file_path' not found."
    return 2
  fi

  if docker config inspect "$config_name" &>/dev/null; then
    printInfoMessage "Docker config '$config_name' already exists. Skipping creation."
    return 0
  fi

  printInfoMessage "Creating Docker config '$config_name' from file '$file_path'..."
  if docker config create "$config_name" "$file_path" >/dev/null; then
    printInfoMessage "Docker config '$config_name' created successfully."
    return 0
  else
    printInfoMessage "Failed to create Docker config '$config_name'."
    return 1
  fi
}

# Helper: Check if a resource is used by any stack except the current one
is_resource_in_use_elsewhere() {
  local resource_type="$1" # network|volume|config|secret
  local resource_name="$2"
  local current_stack="$3"

  printInfoMessage "Checking if resource '$resource_name' of type '$resource_type' is in use by stacks other than '$current_stack'"

  # Get all stack names except the current one
  mapfile -t other_stacks < <(docker stack ls --format '{{.Name}}' | grep -v "^${current_stack}$")
  printInfoMessage "Other stacks: ${other_stacks[*]}"

  case "$resource_type" in
    network)
      printInfoMessage "  -> Inspecting network '$resource_name' for attached containers"

      attached_containers=""
      # Try jq first
      if command -v jq &> /dev/null; then
        container_json=$(docker network inspect "$resource_name" --format '{{json .Containers}}' 2>/dev/null)
        if [[ "$container_json" == "null" || -z "$container_json" ]]; then
          printInfoMessage "  -> No containers attached to network '$resource_name'"
          return 1
        fi
        attached_containers=$(docker network inspect "$resource_name" --format '{{json .Containers}}' | jq -r 'to_entries[] | .value.Name')
      else
        # Fallback using grep/sed
        attached_containers=$(docker network inspect "$resource_name" | \
          sed -n '/"Containers": {/,/},/p' | \
          grep '"Name":' | \
          sed -E 's/.*"Name": "([^"]+)".*/\1/')
        if [[ -z "$attached_containers" ]]; then
          printInfoMessage "  -> No containers attached to network '$resource_name'"
          return 1
        fi
      fi

      local found_external_use=0
      for container in $attached_containers; do
        stack_prefix="${container%%_*}"
        if [[ "$stack_prefix" != "$current_stack" ]]; then
          printInfoMessage "  -> Found container '$container' from stack '$stack_prefix' using network '$resource_name'"
          found_external_use=1
          #return 0
        fi
      done
      if [[ $found_external_use -eq 1 ]]; then
        return 0
      else
        printInfoMessage "  -> Network '$resource_name' is only used by stack '$current_stack'"
        return 1
      fi
      ;;
    volume|config|secret)
      for stack in "${other_stacks[@]}"; do
        printInfoMessage "Inspecting stack: $stack"
        mapfile -t services < <(docker stack services "$stack" --format '{{.Name}}')
        printInfoMessage "Services in $stack: ${services[*]}"

        for service in "${services[@]}"; do
          printInfoMessage "Inspecting service: $service"
          case "$resource_type" in
            volume)
              docker service inspect "$service" --format '{{json .Spec.TaskTemplate.ContainerSpec.Mounts}}' | grep -q "$resource_name" && return 0
              ;;
            config)
              docker service inspect "$service" --format '{{json .Spec.TaskTemplate.ContainerSpec.Configs}}' | grep -q "$resource_name" && return 0
              ;;
            secret)
              docker service inspect "$service" --format '{{json .Spec.TaskTemplate.ContainerSpec.Secrets}}' | grep -q "$resource_name" && return 0
              ;;
          esac
        done
      done
      ;;
    *)
      printInfoMessage "Unsupported resource type: $resource_type"
      return 1
      ;;
  esac
  return 1
}
