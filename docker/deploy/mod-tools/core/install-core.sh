#!/bin/bash

typeset -r dirOfThisScript=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
source "${dirOfThisScript}"/utils/docker_helper.sh
source "${dirOfThisScript}"/utils/defaults.sh

cd "${dirOfThisScript}" || exit 1

INSTALL_DIR="/opt/adva/stacks/core"

HELP_MSG=$(
  cat <<-EOF
Usage: ./install-core.sh <command>

Commands:
  install                   Install and deploy MOD Core.
  --help, -h                Display this help message.

Examples:
  ./install-core.sh install
EOF
)

# Function to create Docker swarm network
create_core_network() {
  if docker network ls | grep -q "$NETWORK_NAME"; then
    echo "Network $NETWORK_NAME already exists."
  else
    docker network create --driver overlay "$NETWORK_NAME"
    if docker network ls | grep -q "$NETWORK_NAME"; then
      echo "Network $NETWORK_NAME created successfully."
    else
      echo "Failed to create network $NETWORK_NAME."
    fi
  fi
}

# Function to create Docker volumes
create_kafka_volumes() {
  for volume in "${KAFKA_VOLUMES[@]}"; do
    if docker volume ls | grep -q "$volume"; then
      echo "Volume $volume already exists."
    else
      docker volume create "$volume"
      if docker volume ls | grep -q "$volume"; then
        echo "Volume $volume created successfully."
      else
        echo "Failed to create volume $volume."
      fi
    fi
  done
}

# Function to create Docker volumes
create_zookeeper_volumes() {
  for volume in "${ZOOKEEPER_VOLUMES[@]}"; do
    if docker volume ls | grep -q "$volume"; then
      echo "Volume $volume already exists."
    else
      docker volume create "$volume"
      if docker volume ls | grep -q "$volume"; then
        echo "Volume $volume created successfully."
      else
        echo "Failed to create volume $volume."
      fi
    fi
  done
}

install() {
  # Check if the stack is already installed
  if docker stack ls | grep -qw "$STACK_NAME"; then
    echo "Stack '$STACK_NAME' is already installed. Skipping installation."
    return 0
  fi

  echo "Installing MOD Core"
  echo ""
  read_mnc_ip
  create_db_secret $SECRET_NAME $SECRET_USERNAME
  read_kafka_adertised_ip
  set_verify_certs_false

  mkdir -p ${INSTALL_DIR}
  cp -r utils ${INSTALL_DIR}
  cp -r config ${INSTALL_DIR}

  cp *.sh *.yml Readme.md ${INSTALL_DIR}

  assertDocker

  # stop stack before installing newer version
  # if stack does not exist or is not running, this call does nothing
  bash "${dirOfThisScript}/core-ctl.sh" --id $STACK_NAME stop

  source ./utils/import-docker-images.sh
  create_kafka_volumes
  create_zookeeper_volumes
  create_core_network

  create_docker_config "${KAFKA_LOG4J_CONFIG_NAME}" "${INSTALL_DIR}/config/kafka/log4j.properties"
  create_docker_config "${ZOOKEEPER_LOG4J_CONFIG_NAME}" "${INSTALL_DIR}/config/zookeeper/logback.xml"

  bash "${dirOfThisScript}/core-ctl.sh" --id $STACK_NAME start

  echo "MOD Core installation finished"
  echo ""
}

if [[ $# -eq 0 ]]; then
  echo "No arguments provided."
  echo "$HELP_MSG"
  exit 1
fi

case "$1" in
install)
  install
  ;;
--help | -h)
  echo "$HELP_MSG"
  exit 0
  ;;
*)
  echo "Invalid command: $1"
  echo "$HELP_MSG"
  exit 1
  ;;
esac
