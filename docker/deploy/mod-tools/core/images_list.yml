apiVersion: enc.adva.com/v1
kind: DockerImageEnc
metadata:
  name: coreImages
spec:
  images:
    - name: kafka
      privateRepo: "gdn-artifactory.rd.advaoptical.com:9443/enc/common_3rd-party/bitnami-kafka:3.8.1-debian-12-r1"
      thirdParty: true
    - name: zookeeper
      privateRepo: "gdn-artifactory.rd.advaoptical.com:9443/enc/common_3rd-party/zookeeper:3.8.4"
      thirdParty: true
    - name: job-manager-app
      privateRepo: "gdn-artifactory.rd.advaoptical.com:9443/enc/core/job-manager/job-manager-app:latest"  
      thirdParty: false
    - name: mnc-web
      privateRepo: "gdn-artifactory.rd.advaoptical.com:9443/enc/mnc/mnc-web:latest" 
      thirdParty: false
    - name: reg-manager
      privateRepo: "gdn-artifactory.rd.advaoptical.com:9443/enc/core/notification/notification-manager:latest" 
      thirdParty: false  