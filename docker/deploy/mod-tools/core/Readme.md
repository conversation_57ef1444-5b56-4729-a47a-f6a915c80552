# MOD CORE Package Installation and Uninstallation in Docker Swarm

This package contains Bash scripts and a Docker stack configuration to manage the deployment and removal of the MOD CORE package in a Docker Swarm environment. The provided scripts help you automate the setup and teardown of the necessary Docker services, networks, volumes, and secrets.

## Package Structure

- `install-core.sh`: <PERSON><PERSON><PERSON> to install the MOD CORE package.
- `uninstall-core.sh`: <PERSON><PERSON><PERSON> to uninstall the MOD CORE package.
- `core-ctl.sh`: <PERSON><PERSON><PERSON> to manage the MOD CSM package (status, start, stop, restart).
- `docker-stack.yml`: Docker stack configuration file for the MOD CORE services.
- `docker_images`: Directory containing Docker images in tar files needed to run the CORE app.
- `utils/`: Directory containing helper scripts and default configurations.

## Prerequisites

Ensure you have Docker and Docker Swarm initialized on your system. If not, you can initialize Docker Swarm with the following command:

```bash
docker swarm init
```

## Usage

### Installation

To install the MOD CORE package, use the `install-core.sh` script. This script performs the following actions:

1. Imports Docker images.
2. Creates necessary Docker volumes.
3. Creates a Docker network.
4. Creates a Docker secret for the database password.
5. Deploys the Docker stack.

Run the installation script with the following command:

```bash
./install-core.sh install
```

You can check the status of the deployed stack using:

```bash
./core-ctl.sh status
```

### Uninstallation

To uninstall the MOD CORE package, use the `uninstall-core.sh` script. This script performs the following actions:

1. Stops and removes the Docker stack.
2. Removes Docker volumes.
3. Removes the Docker network.
4. Deletes the Docker secret for the database password.
5. Removes related Docker images.

Run the uninstallation script with the following command:

```bash
./uninstall-core.sh
```

### Application Management

You can manage the MOD Core package using the `core-ctl.sh` script with the following commands:

- `status`: Check the status of the Docker Core stack.
- `start`: Start the Core stack.
- `stop`: Stop the Core stack.
- `restart`: Restart the Core stack.

Example usage:

```bash
./core-ctl.sh status
./core-ctl.sh start
./core-ctl.sh stop
./core-ctl.sh restart
```

## Configuration Details

### `docker-stack.yml`

The `docker-stack.yml` file defines the services, networks, volumes, and secrets required for the MOD CORE package. Key sections include:

- **Services**:
  - `kafka`: kafka Main application service.
  - `zookeeper`: Zookeeper service for coordination.

- **Networks**:
  - `net-kafka`: Network used by the services.

- **Volumes**:
  - `kafka-data`: Volume for Kafka data.
  - `zoo-data`: Volume for Zookeeper data.
  - `zoo-datalog`: Volume for Zookeeper datalog.


  ### `install-core.sh`

This script includes functions to handle the creation of Docker secrets, volumes, networks, and the deployment of the Docker stack. It also includes functions to check the status of the deployed stack.

### `uninstall-core.sh`

This script includes functions to stop and remove the Docker stack, delete Docker secrets, volumes, and networks, and remove related Docker images.

### `core-ctl.sh`
This script includes functions to manage the Docker Core stack, allowing you to check its status, start, stop, and restart the stack.

### `utils/`

The `utils/` directory contains helper scripts and default configurations used by the main installation and uninstallation scripts.

## Notes

- Ensure that the `docker_images` directory contains the necessary Docker images in tar files before running the installation script.
- Update the environment variables and Docker configurations as needed to match your specific setup.

By following the instructions in this README, you can easily manage the deployment and removal of the MOD CORE package in your Docker Swarm environment.
