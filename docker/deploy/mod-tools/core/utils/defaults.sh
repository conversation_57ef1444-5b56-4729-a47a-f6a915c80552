#!/bin/bash

typeset -r coreUtilDir=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
source "${coreUtilDir}"/../../defaults.sh

DOCKER_STACK_BUILD='docker-stack.yml'
IMAGES_DIR="${scriptDir}/../docker_images"
KAFKA_VOLUMES=("kafka-data")
LOG_DIR="/var/tmp/healthcheck_stacks"
NETWORK_NAME="net-kafka"
REPO='adtran/mod/core'
SECRET_NAME="mnc-postgresql-db-pw"
STACK_FILE="docker-stack.yml"
STACK_NAME="mnc-core"
SERVICE_LIST_FILE="/opt/adva/stacks/service_list"
KAFKA_LOG4J_CONFIG_NAME="mod-kafka-log4j"
KAFKA_APP_CONTAINER_NAME="kafka"
KAFKA_APP_LOG_PATH="/var/lib/docker/volumes/mnc-core_kafka-logs/_data/"
ZOOKEEPER_LOG4J_CONFIG_NAME="mod-zookeeper-log4j"
ZOOKEEPER_VOLUMES=("zoo-data" "zoo-datalog")
ZOOKEEPER_APP_CONTAINER_NAME="zookeeper"
ZOOKEEPER_APP_LOG_PATH="/var/lib/docker/volumes/mnc-core_zoo-logs/_data/"
