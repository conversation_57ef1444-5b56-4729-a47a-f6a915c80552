#!/bin/bash

typeset -r dirOfThisScript=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
source "${dirOfThisScript}"/utils/docker_helper.sh
source "${dirOfThisScript}"/utils/defaults.sh

INSTALL_DIR="/opt/adva/stacks/core"

# Function to uninstall the stack and all related resources
uninstall_stack() {
  actionMsg="Start: uninstall procedure of ${STACK_NAME}"
  echo "$actionMsg"

  # Check all relevant resources
  local resource_blocked=0

  if is_resource_in_use_elsewhere network "$STACK_NAME"_"$NETWORK_NAME" "$STACK_NAME"; then
    echo "Network $NETWORK_NAME is still used by another stack. Skipping uninstall."
    resource_blocked=1
  fi

  if [[ $resource_blocked -eq 1 ]]; then
    echo "Uninstall aborted: One or more resources are still in use by other stacks."
    return 1
  fi

  bash "${dirOfThisScript}"/core-ctl.sh --id $STACK_NAME stop
  bash "${dirOfThisScript}"/utils/stack_delete_all.sh "$STACK_NAME"

  deleteStackVolumes "$STACK_NAME"
  removeDanglingVolumes

  if [[ $(docker images | grep adtran/mod/core/ || true) ]]; then
    for image in bitnami-kafka zookeeper job-manager-app mnc-web notification-manager; do
      image=$(docker images --format '{{.Repository}}:{{.Tag}}' | grep adtran/mod/core/${image})
      docker image rm ${image}
    done
  else
    # remove images pulled from artifactory
    for image in bitnami-kafka zookeeper job-manager-app mnc-web notification-manager; do
      docker images --format "{{.ID}} {{.Repository}}:{{.Tag}}" | grep "/${image}:" |
        awk '{ print $1 }' | uniq | xargs docker rmi -f ${image} || true
    done
  fi

  docker network rm "$NETWORK_NAME"
  docker config rm "$KAFKA_LOG4J_CONFIG_NAME"
  docker config rm "$ZOOKEEPER_LOG4J_CONFIG_NAME"

  echo "Cleaning up directory $INSTALL_DIR"
  rm -rf ${INSTALL_DIR}

  actionMsg="End: uninstall procedure"
  echo "${actionMsg}"
}

uninstall_stack
