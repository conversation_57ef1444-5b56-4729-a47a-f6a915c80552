version: "3.7"

x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "${ENC_CORE_LOG_SIZE:-100m}"
    max-file: "${ENC_CORE_LOG_MAX_FILES:-3}"

x-deploy: &default-deploy
  replicas: 1
  update_config:
    parallelism: 1
    delay: 10s
  restart_policy:
    condition: any
    max_attempts: 5
    window: 30m

x-enc-core-healthcheck: &enc-core-healthcheck
  interval: 60s
  timeout: 20s
  retries: 3
  start_period: 30s

services:
  job-manager:
    image: "${JOB_MANAGER_APP_IMAGE}:${JOB_MANAGER_APP_TAG}"
    networks:
      - net-kafka
    deploy:
      <<: *default-deploy
      resources:
        limits:
          memory: 4096M
          cpus: '6.0'
        reservations:
          cpus: '0.1'
    environment:
      CLEAN_JOB_SCHEDULER_CRON_EXP_DAY: "*/1"
      DB_URL_PARAMS: default-docker
      ENC_SERVER_BASE: "https://${MNC_HOST}:8443"
      JAVA_OPTS: "-Xms256m -Xmx1500m -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005"
      JOB_TTL_DAYS: 1
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      LOG_LEVEL: INFO
      MESSAGE_TTL_DAYS: 1
      VERIFY_CERTS: "false"
    healthcheck:
      test: ["CMD-SHELL", "healthcheck"]
      interval: 20s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging: *default-logging
    secrets:
      - db-password
      - db-username
    volumes:
      - /var/run/postgresql/.s.PGSQL.5432:/var/run/postgresql/.s.PGSQL.5432
      - job-manager-tmp:/tmp
      - job-manager-var:/app/var
    read_only: true
    cap_drop:
      - ALL
    ulimits:
      nofile:
        soft: 1024
        hard: 4096

  mnc-web:
    image: "${MNC_WEB_IMAGE}:${MNC_WEB_TAG}"
    networks:
      - net-kafka
      - mnc-rproxy_net-traefik
    volumes:
      - mnc-web-logs:/usr/mnc-web/server/logs
      - mnc-web-set-db:/usr/mnc-web/server/db/database/user_preferences
    environment:
      BASE_URL: "/mnc"
      ENC_IS_SECURE: "true"
      ENC_SERVER_PORT: 8443
      ENC_SERVER: ${MNC_HOST}
      ENC_TIMEOUT: 60000
      IS_SECURE: "false"
      JOBS_IS_SECURE: "false"
      JOBS_SERVER_PORT: 8108
      JOBS_SERVER: "job-manager"
      KAFKA_IP: "kafka"
      KAFKA_PORT: "9092"
      LOG_FOLDER: logs
      SECURE_PORT: 12443
      SERVER_CRT_FILE_PATH: "sslcert/server.crt"
      SERVER_KEY_FILE_PATH: "sslcert/server.key"
      USER_PREFERENCES: "db/database/user_preferences"
      VERIFY_CERTS: "false"
    logging: *default-logging
    read_only: true
    deploy:
      update_config:
        parallelism: 1
        delay: 15s
      restart_policy:
        condition: on-failure
      resources:
        limits:
          memory: 1024M
          cpus: '2.0'
        reservations:
          cpus: '0.2'
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.mncwebapp_mnc-web.rule=PathPrefix(`/mnc`)"
        - "traefik.http.routers.mncwebapp_mnc-web.entrypoints=websecure"
        - "traefik.http.services.mncwebapp_mnc-web.loadbalancer.server.port=12443"
        - "traefik.swarm.network=mnc-rproxy_net-traefik"
        - "traefik.http.routers.mncwebapp_mnc-web.tls=true"
        - "traefik.http.middlewares.mncweb-security.headers.customresponseheaders.Content-Security-Policy=default-src 'self'; script-src 'self' 'nonce-901adtran35806'; style-src 'self' 'nonce-901adtran35806'; img-src 'self' data:"
        - "traefik.http.middlewares.mncweb-security.headers.customresponseheaders.X-Frame-Options=DENY"
        - "traefik.http.middlewares.mncweb-security.headers.customresponseheaders.Strict-Transport-Security=max-age=31536000; includeSubDomains; preload"
        - "traefik.http.routers.mncwebapp_mnc-web.middlewares=mncweb-security@swarm"
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - DAC_OVERRIDE
      - KILL
      - SETFCAP
      - SETGID
      - SETUID
    ulimits:
      nofile:
        soft: 1024
        hard: 4096
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "wget --no-verbose --no-check-certificate --tries=1 --spider http://localhost:12443/mnc/ || exit 1",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
  kafka:
    image: "${INFRA_KAFKA_IMAGE}:${INFRA_KAFKA_TAG}"
    networks:
      - net-kafka
      - mnc-rproxy_net-traefik
    deploy:
      <<: *default-deploy
      restart_policy:
        condition: any
        max_attempts: 10
        window: 30m
      resources:
        limits:
          memory: 3072M
          cpus: '4.0'
        reservations:
          cpus: "0.5"
      labels:
        - "traefik.enable=true"
        - "traefik.tcp.routers.mnc-core_kafka.rule=HostSNI(`*`)"
        - "traefik.tcp.routers.mnc-core_kafka.entrypoints=kafkaentry"
        - "traefik.tcp.services.mnc-core_kafka.loadbalancer.server.port=9094"
        - "traefik.swarm.network=mnc-rproxy_net-traefik"
        - "traefik.tcp.routers.mnc-core_kafka.tls=true"
        - "traefik.tcp.routers.mnc-core_kafka.tls.passthrough=false"
    environment:
      ALLOW_PLAINTEXT_LISTENER: "yes"
      KAFKA_BROKER_ID: 101
      KAFKA_CFG_ADVERTISED_LISTENERS: CLIENT://kafka:9092,EXTERNAL://${INFRA_KAFKA_ADVERTISED_HOST}:9200
      KAFKA_CFG_AUTO_CREATE_TOPICS_ENABLE: "false"
      KAFKA_CFG_CONTROLLER_LISTENER_NAMES: CONTROLLER
      KAFKA_CFG_CONTROLLER_QUORUM_VOTERS: 101@kafka:9093
      KAFKA_CFG_INTER_BROKER_LISTENER_NAME: CLIENT
      KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: CLIENT:PLAINTEXT,EXTERNAL:SASL_PLAINTEXT,CONTROLLER:PLAINTEXT
      KAFKA_CFG_LISTENERS: CLIENT://:9092,EXTERNAL://:9094,CONTROLLER://:9093
      KAFKA_CFG_LOG_DIRS: /bitnami/kafka/log
      KAFKA_CFG_LOG_RETENTION_HOURS: 168
      KAFKA_CFG_NODE_ID: 101
      KAFKA_CFG_PROCESS_ROLES: broker,controller
      KAFKA_CFG_SASL_ENABLED_MECHANISMS: PLAIN
      KAFKA_CLIENT_PASSWORDS_FILE: /run/secrets/kafka-password
      KAFKA_CLIENT_USERS_FILE: /run/secrets/kafka-username
      KAFKA_ENABLE_KRAFT: "yes"
      KAFKA_KRAFT_CLUSTER_ID: 3Nm_8FkdTIe3Th6iqMyLHQ
      TZ: UTC
      VERIFY_CERTS: "false"
    secrets:
      - kafka-password
      - kafka-username
    configs:
      - source: mod-kafka-log4j
        target: /opt/bitnami/kafka/config/log4j.properties
    volumes:
      - kafka-config:/opt/bitnami/kafka/config
      - kafka-data:/bitnami
      - kafka-logs:/opt/bitnami/kafka/logs
      - kafka-tmp:/tmp
    logging: *default-logging
    healthcheck:
      interval: 30s
      timeout: 10s
      retries: 3
      test:
        [
          "CMD-SHELL",
          "bash",
          "-c",
          "kafka-topics.sh",
          "--list",
          "--bootstrap-server",
          "localhost:9092",
        ]
    read_only: true
    cap_drop:
      - ALL
    ulimits:
      nofile:
        soft: 2048
        hard: 4096

  notification-manager:
    image: "${REG_MANAGER_APP_IMAGE}:${REG_MANAGER_APP_TAG}"
    networks:
      - net-kafka
    deploy:
      <<: *default-deploy
      resources:
        limits:
          memory: 2048M
          cpus: '2.5'
        reservations:
          cpus: "0.1"
    environment:
      ENC_SERVER_BASE: "https://${MNC_HOST}:8443"
      JAVA_OPTS: "-Xms256m -Xmx1500m -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:5005"
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      LOG_LEVEL: INFO
      VERIFY_CERTS: "false"
    logging: *default-logging
    healthcheck:
      test: ["CMD-SHELL", "healthcheck"]
      interval: 20s
      timeout: 10s
      retries: 3
      start_period: 60s
    volumes:
      - notification-manager-tmp:/tmp
      - notification-manager-var:/app/var
    read_only: true
    cap_drop:
      - ALL
    ulimits:
      nofile:
        soft: 1024
        hard: 4096


  zookeeper:
    image: "${EOD_CORE_ZOOKEEPER_IMAGE}:${EOD_CORE_ZOOKEEPER_TAG}"
    user: "zookeeper"
    environment:
      TZ: UTC
      VERIFY_CERTS: "false"
      ZOO_LOG4J_PROP: "INFO,ROLLINGFILE"
    healthcheck:
      test: ["CMD-SHELL", "zkCli.sh ls /"]
      interval: 20s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - net-kafka
    volumes:
      - zoo-conf:/conf
      - zoo-data:/data
      - zoo-datalog:/datalog
      - zoo-logs:/logs
    deploy:
      <<: *default-deploy
      resources:
        limits:
          memory: 1536M
          cpus: '2.0'
        reservations:
          cpus: "0.25"
    logging: *default-logging
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - FOWNER
    ulimits:
      nofile:
        soft: 1024
        hard: 4096
    read_only: true
    configs:
      - source: mod-zookeeper-log4j
        target: /conf/logback.xml

networks:
  net-kafka:
  mnc-rproxy_net-traefik:
    external: true

volumes:
  job-manager-tmp:
  job-manager-var:
  kafka-config:
  kafka-data:
  kafka-logs:
  kafka-tmp:
  mnc-web-logs:
  mnc-web-set-db:
  notification-manager-tmp:
  notification-manager-var:
  zoo-conf:
  zoo-data:
  zoo-datalog:
  zoo-logs:


secrets:
  db-password:
    external: true
    name: mnc-postgresql-db-pw
  db-username:
    external: true
    name: mnc-postgresql-db-user
  kafka-password:
    external: true
    name: mnc-core-kafka-pw
  kafka-username:
    external: true
    name: mnc-core-kafka-user

configs:
  mod-kafka-log4j:
    external: true
  mod-zookeeper-log4j:
    external: true
