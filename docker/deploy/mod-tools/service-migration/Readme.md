# MOD Service Migration Package in Docker Swarm

This package contains Bash scripts and a Docker stack configuration to manage the deployment and removal of the MOD Service Migration package in a Docker Swarm environment. The provided scripts help you automate the setup and teardown of the necessary Docker services, networks, volumes, and secrets.

## Package Structure

- `install-service-migration.sh`: <PERSON><PERSON><PERSON> to install the MOD Service Migration package.
- `uninstall-service-migration.sh`: <PERSON><PERSON><PERSON> to uninstall the MOD Service Migration package.
- `service-migration-ctl.sh`: <PERSON><PERSON><PERSON> to manage the MOD Service Migration package (start | run | status | stop | logs).
- `docker-stack.yml`: Docker stack configuration file for the MOD Service Migration services.
- `docker_images`: Directory containing Docker images in tar files needed to run the service-migration-tool app.
- `utils/`: Directory containing helper scripts and default configurations.

## Prerequisites

Ensure you have Docker and Docker Swarm initialized on your system. If not, you can initialize Docker Swarm with the following command:

```bash
docker swarm init
```

## Usage

### Installation

To install the MOD Service Migration package, use the `install-service-migration.sh` script. This script performs the following actions:

1. Imports Docker images.
2. Creates necessary Docker volumes.
3. Creates a Docker network.
4. Creates a Docker secret for the database password.
5. Deploys the Docker stack.

Run the installation script with the following command:

```bash
./install-service-migration.sh install
```

You can check the status of the migration process:

```bash
./service-migration-ctl.sh status
```

### Uninstallation

To uninstall the MOD Service Migration package, use the `uninstall-service-migration.sh` script. This script performs the following actions:

1. Stops and removes the Docker stack.
2. Removes Docker volumes.
3. Removes the Docker network.
4. Deletes the Docker secret for the database password.
5. Removes related Docker images.

Run the uninstallation script with the following command:

```bash
./uninstall-service-migration.sh
```

### Service Migration Control

##### Overview
The `service-migration-ctl.sh` script is used to control the migration service. It provides commands to deploy, start, check the status, and stop the migration process.

##### Usage
```bash
service-migration-ctl.sh <option>
```

##### Options

###### `start`
Deploys the service migration stack using the included `service-migration-stack.yml` file. The migration process does not start automatically.

- **Initial service status:** `migration-pending`

###### `run`
Starts the migration process.

- **Possible service statuses:**
  - `migration-running`
  - `migration-completed-failed`
  - `migration-completed-success`

###### `status`
Outputs the current status of the service migration. Possible values include:

- `service-migration-tool-not-started` - The tool has not been started yet.
- `migration-pending` - The migration stack is deployed, but the process has not started.
- `migration-running <percentage complete>` - The migration is currently in progress. Optionally, the completion percentage may be displayed.
- `migration-completed-failed` - The migration process has finished but was unsuccessful.
- `migration-completed-success` - The migration process has finished successfully.

###### `stop`
Stops the migration process.


## Configuration Details

### `docker-stack.yml`

The `docker-stack.yml` file defines the services, networks, volumes, and secrets required for the MOD Service Migration package. Key sections include:

- **Services**:
  - `service-migration-tool`: Main application service.

- **Networks**:
  - `mnc-core_net-kafka` and `mnc-rproxy_net-traefik`: External network used by the services.

### `install-service-migration.sh`

This script includes functions to handle the creation of Docker secrets, volumes, networks, and the deployment of the Docker stack. It also includes functions to check the status of the deployed stack.

### `uninstall-service-migration.sh`

This script includes functions to stop and remove the Docker stack, delete Docker secrets, volumes, and networks, and remove related Docker images.

### `service-migration-ctl.sh`
This script includes functions to manage the Docker service-migration stack, allowing you to check its status, start, stop, and run migration process.

### `utils/`
The `utils/` directory contains helper scripts and default configurations used by the main installation and uninstallation scripts.

## Notes

- Ensure that the `docker_images` directory contains the necessary Docker images in tar files before running the installation script.
- Update the environment variables and Docker configurations as needed to match your specific setup.

By following the instructions in this README, you can easily manage the deployment and removal of the MOD Service Migration package in your Docker Swarm environment.
