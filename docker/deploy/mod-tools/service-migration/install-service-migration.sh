#!/bin/bash

typeset -r dirOfThisScript=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
source "${dirOfThisScript}"/utils/docker_helper.sh
source "${dirOfThisScript}"/utils/defaults.sh

cd "${dirOfThisScript}" || exit 1

INSTALL_DIR="/opt/adva/stacks/service-migration"

HELP_MSG=$(cat <<-EOF
Usage: ./install-service-migration.sh <command>

Commands:
  install                   Install and deploy Service Migration.
  --help, -h                Display this help message.

Examples:
  ./install-service-migration.sh install
EOF
)

# Function to create Docker volumes
create_service_migration_volumes() {
  for volume in "${SERVICE_MIGRATION_VOLUMES[@]}"; do
    if docker volume ls | grep -q "$volume"; then
      echo "Volume $volume already exists."
    else
      docker volume create "$volume"
      if docker volume ls | grep -q "$volume"; then
        echo "Volume $volume created successfully."
      else
        echo "Failed to create volume $volume."
      fi
    fi
  done
}

install() {
  # Check if the stack is already installed
  if docker stack ls | grep -qw "$STACK_NAME"; then
    echo "Stack '$STACK_NAME' is already installed. Skipping installation."
    return 0
  fi

  echo "Installing MOD Service Migration"
  echo ""
  read_mnc_ip
  mkdir -p ${INSTALL_DIR}

  if [ -d "${INSTALL_DIR}" ]; then
    cp -r utils ${INSTALL_DIR}
    cp *.sh *.yml Readme.md ${INSTALL_DIR}
  else
    echo "Failed to create ${INSTALL_DIR}, skipping copy."
  fi

  assertDocker

  source ./utils/import-docker-images.sh

  mkdir -p src

  echo "MOD Service Migration installation finished"
  echo ""
}

if [[ $# -eq 0 ]]; then
  echo "No arguments provided."
  echo "$HELP_MSG"
  exit 1
fi

case "$1" in
  install)
    install
    ;;
  --help|-h)
    echo "$HELP_MSG"
    exit 0
    ;;
  *)
    echo "Invalid command: $1"
    echo "$HELP_MSG"
    exit 1
    ;;
esac

