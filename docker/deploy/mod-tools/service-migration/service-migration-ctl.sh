#!/bin/bash

set -e # automatic exit on error

typeset -r dirOfThisScript=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
source "${dirOfThisScript}"/utils/docker_helper.sh
source "${dirOfThisScript}"/utils/defaults.sh
cd "${dirOfThisScript}" || exit 1

assertDocker

ID="service-migration"
HELP_MSG=$(
  cat <<-EOF
Usage: ./service-migration-ctl.sh [--id <deploy-id>] <command>

Options:
  --id <deploy-id>          Specify the stack ID (default: service-migration)

Commands:
  status                    Check the status of all service and migration process.
  start                     Start the stack.
  stop                      Stop the stack.
  run                       Run migration process.
  logs                      Gather and display logs for the stack.
  collect_migration_data    Collect logs and reports data for service migration process
  --help, -h                Display this help message.

Examples:
  ./service-migration-ctl.sh --id my-deployment start
  ./service-migration-ctl.sh run
  ./service-migration-ctl.sh logs
EOF
)

# Initialize ACTION and ID variables
ACTION=""

if [[ $# -eq 0 ]]; then
  echo "No arguments provided."
  echo "$HELP_MSG"
  exit 1
fi

while [[ $# -gt 0 ]]; do
  case "$1" in
  --id)
    if [[ -n "$2" && "$2" != "--"* ]]; then
      ID="$2"
      shift 2
    else
      echo "Invalid argument for --id"
      echo "$HELP_MSG"
      exit 1
    fi
    ;;
  --help | -h)
    echo "$HELP_MSG"
    exit 0
    ;;
  status | start | stop | run | logs | collect_migration_data)
    ACTION="$1"
    shift
    ;;
  *)
    echo "Invalid argument: $1"
    echo "$HELP_MSG"
    exit 1
    ;;
  esac
done

if [[ -z "$ACTION" ]]; then
  echo "Invalid arguments."
  echo "$HELP_MSG"
  exit 1
fi

case ${ACTION} in
status)
  execute_status_script "$ID" "$SERVICE_MIGRATION_CONTAINER_NAME" # Pass parameters
  ;;
start)
  # Check if the stack is already installed
  if docker stack ls | grep -qw "$STACK_NAME"; then
    echo "Stack '$STACK_NAME' is already running. Skipping running."
    return 0
  fi

  mkdir -p ${INSTALL_DIR}
  if [ ! -d "${INSTALL_DIR}" ]; then
    cp -r utils ${INSTALL_DIR}
    cp *.sh *.yml Readme.md ${INSTALL_DIR}
  else
    echo "utils already exists in ${INSTALL_DIR}, skipping copy."
  fi

  read_mnchost_file
  assertDocker

  # stop stack before installing newer version
  # if stack does not exist or is not running, this call does nothing
  deleteDockerStack "$ID"

  source ./utils/import-docker-images.sh

  deployDockerStack "$ID"

  echo "MOD Service Migration installation finished"
  echo ""
  ;;
stop)
  actionMsg="Start: stop procedure of ${STACK_NAME}"
  echo $actionMsg
  deleteDockerStack "$ID"
  bash "${dirOfThisScript}"/utils/stack_delete_all.sh "$STACK_NAME"

  actionMsg="End: stop procedure"
  echo "${actionMsg}"
  ;;
run)
  execute_migration_script "$STACK_NAME" "$SERVICE_MIGRATION_CONTAINER_NAME" # Pass parameters
  ;;
logs)
  healthcheck_dir="${LOG_DIR}/healthcheck_$ID"
  mkdir -p "${healthcheck_dir}"
  getContainerFile "$ID" "$SERVICE_MIGRATION_CONTAINER_NAME" "$SOURCE_LOG/_data"
  gatherLogs "$ID"
  ;;
collect_migration_data)
  if [ $(id -u) -ne 0 ]; then
      echo "This option must be run as root or with sudo privileges to collect migration data."
      exit 1
  fi
  mkdir -p "${LOG_DIR}"
  collectMigrationData "$ID"
  ;;
*)
  echo "Unknown action"
  echo "$HELP_MSG"
  ;;
esac
