version: "3.7"

x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "${SERVICE_MIGRATION_LOG_SIZE:-100m}"
    max-file: "${SERVICE_MIGRATION_LOG_MAX_FILES:-3}"

x-deploy: &default-deploy
  replicas: 1
  update_config:
    parallelism: 1
    delay: 10s
  restart_policy:
    condition: any
    max_attempts: 10
    window: 30m

services:
  service-migration-tool:
    image: "${SERVICE_MIGRATION_IMAGE}:${SERVICE_MIGRATION_IMAGE_TAG}"
    #command: ["/app/scripts/run"]
    networks:
      - mnc-core_net-kafka
      - mnc-rproxy_net-traefik
    deploy:
      <<: *default-deploy
      restart_policy:
        condition: none # Override the default restart_policy here
      # resources:
      #   limits:
      #     memory: 1700M
      #     cpus: "2.5"
      #   reservations:
      #     cpus: "0.25"
    ports:
      - "8992:8992"
    volumes:
      - service-migration-logs:/app/var/log
      - service-migration-reports:/app/var/reports
      - service-migration-tmp:/tmp
      - service-migration-ts:/app/var/ts
      - service-migration-db:/app/var/db
    environment:
      TZ: UTC
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      CSM_SERVER_BASE: "http://csm-app:8989"
      ENC_SERVER_BASE: "https://${MNC_HOST}:8443"
      VERIFY_CERTS: "false"
    logging: *default-logging
    healthcheck:
      test: ["CMD-SHELL", "healthcheck"]
      interval: 20s
      timeout: 10s
      retries: 3
      start_period: 60s
    ulimits:
      nofile:
        soft: 1024
        hard: 4096
    read_only: true
    cap_drop:
      - ALL

networks:
  mnc-core_net-kafka:
    external: true
  mnc-rproxy_net-traefik:
    external: true

volumes:
  service-migration-logs:
  service-migration-db:
  service-migration-reports:
  service-migration-tmp:
  service-migration-ts:
