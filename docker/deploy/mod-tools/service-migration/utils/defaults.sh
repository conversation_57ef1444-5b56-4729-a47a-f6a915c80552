#!/bin/bash

typeset -r smUtilDir=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
source "${smUtilDir}"/../../defaults.sh

DOCKER_STACK_BUILD='docker-stack.yml'
IMAGES_DIR="${scriptDir}/../docker_images"
INSTALL_DIR="/opt/adva/stacks/service-migration"
LOG_DIR="/var/tmp/healthcheck_stacks"
REPO='adtran/mod/service-migration'
SERVICE_MIGRATION_CONTAINER_NAME="service-migration-tool"
SERVICE_MIGRATION_LOG_PATH="/app/var/log"
SERVICE_MIGRATION_VOLUMES=("service-migration-logs" "service-migration-reports")
SOURCE_LOG="/var/lib/docker/volumes/service-migration_service-migration-logs"
SOURCE_REPORTS="/var/lib/docker/volumes/service-migration_service-migration-reports"
STACK_FILE="docker-stack.yml"
STACK_NAME="service-migration"
SERVICE_LIST_FILE="/opt/adva/stacks/service_list"