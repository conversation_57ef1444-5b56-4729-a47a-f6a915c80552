#!/bin/bash

# Get the directory of this script
typeset -r dirOfThisScript=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

# Source utility scripts
source "${dirOfThisScript}"/utils/docker_helper.sh
source "${dirOfThisScript}"/utils/defaults.sh

# Define installation directory
INSTALL_DIR="/opt/adva/stacks/service-migration"

# Function to uninstall the stack and all related resources
uninstall_stack() {
  actionMsg="Start: uninstall procedure of ${STACK_NAME}"
  echo "$actionMsg"

  # Check all relevant resources
  local resource_blocked=0

  # # Example: Check network
  # if is_resource_in_use_elsewhere network "$NETWORK_NAME" "$STACK_NAME"; then
  #   echo "Network $NETWORK_NAME is still used by another stack. Skipping uninstall."
  #   resource_blocked=1
  # fi

  # Example: Check volumes
  # for volume in "${SERVICE_MIGRATION_VOLUMES[@]}"; do
  #   if is_resource_in_use_elsewhere volume "$volume" "$STACK_NAME"; then
  #     echo "Volume $volume is still used by another stack. Skipping uninstall."
  #     resource_blocked=1
  #   fi
  # done

  # Example: Check configs (add your config names if any)
  # for config in "${SERVICE_MIGRATION_CONFIGS[@]}"; do
  #   if is_resource_in_use_elsewhere config "$config" "$STACK_NAME"; then
  #     echo "Config $config is still used by another stack. Skipping uninstall."
  #     resource_blocked=1
  #   fi
  # done

  # Example: Check secrets (add your secret names if any)
  # for secret in "${SERVICE_MIGRATION_SECRETS[@]}"; do
  #   if is_resource_in_use_elsewhere secret "$secret" "$STACK_NAME"; then
  #     echo "Secret $secret is still used by another stack. Skipping uninstall."
  #     resource_blocked=1
  #   fi
  # done

  if [[ $resource_blocked -eq 1 ]]; then
    echo "Uninstall aborted: One or more resources are still in use by other stacks."
    return 1
  fi

  # Proceed with uninstall if all resources are free
  bash "${dirOfThisScript}"/service-migration-ctl.sh --id $STACK_NAME stop
  bash "${dirOfThisScript}"/utils/stack_delete_all.sh "$STACK_NAME"
  deleteStackVolumes "$STACK_NAME"
  removeDanglingVolumes

  if [[ $(docker images | grep adtran/mod/service-migration/ || true) ]]; then
    for image in service-migration; do
      image=$(docker images --format '{{.Repository}}:{{.Tag}}' | grep adtran/mod/service-migration/${image})
      docker image rm ${image}
    done
  fi

  echo "Cleaning up directory $INSTALL_DIR"
  rm -rf ${INSTALL_DIR}

  actionMsg="End: uninstall procedure"
  echo "${actionMsg}"
}

uninstall_stack
