import com.fasterxml.jackson.dataformat.yaml.YAMLFactory
import com.fasterxml.jackson.databind.ObjectMapper
import com.adva.gradle.imagepublisher.ImagePublisherTask
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.nio.file.Files
import java.nio.file.Paths
import java.net.HttpURLConnection
import groovy.json.JsonOutput
import groovy.json.JsonSlurper

buildscript {
    // Build script configuration for custom plugins
    ext.fspRoot = file("../../..")
    apply from: "$fspRoot/utilities.gradle"
    apply from: "$fspRoot/parameters.gradle"
    apply from: "$fspRoot/repositories.gradle"

    dependencies {
        classpath('org.hidetake:gradle-ssh-plugin:2.10.1')
    }
}

plugins {
    id 'java-library'
    id 'com.adva.gradle.plugin.image-publisher'
    id "com.dorongold.task-tree" version "4.0.0"
    id 'maven-publish'
    id "enc-logging" version "1.1.0"
}

// ------------------------------------------------------------
// Package 'CSM/Core'
// ------------------------------------------------------------

// :mod-tools:buildCSMPackage - Start Building CSM and Core Package
// \--- :mod-tools:publishToSitHostMODCSCoreMPackage - Publish package with MOD CSM/CORE/MNC-RPROXY on sitnms1
//      \--- :mod-tools:tarCSMCoreContent - Create package with CSM and Core and MNC-Rproxy
//           \--- :mod-tools:copyModSdpScript - Copy sdp.sh to build directory
//                \--- :mod-tools:copyRProxyContent - Copies Core scripts and utilities to the build directory.
//                     \--- :mod-tools:updateRProxyDockerStack - Updates the RProxy Docker stack file with the properties and version.
//                          \--- :mod-tools:pullRProxyImage - Pulls a RProxy specified Docker image, saves it as a tar file in the build directory, and then removes the image from the local Docker registry.
//                               \--- :mod-tools:copyCoreContent - Copies Core scripts and utilities to the build directory.
//                                    \--- :mod-tools:updateCoreDockerStack - Updates the Core Docker stack file with the properties and version.
//                                         \--- :mod-tools:pullMNCCoreImage - Pulls a Core specified Docker image, saves it as a tar file in the build directory, and then removes the image from the local Docker cache.
//                                              \--- :mod-tools:copyCSMContent - Copies CSM scripts and utilities to the build directory.
//                                                   \--- :mod-tools:updateCSMDockerStack - Updates the CSM Docker stack file with the properties and version.
//                                                        \--- :mod-tools:pullCSMImage - Pulls a CSM specified Docker image, saves it as a tar file in the build directory, and then removes the image from the local Docker registry.
//                                                             \--- :mod-tools:extractVersionTags


group = 'com.adtran.enc'
def buildBranch = System.getenv('teamcity_build_branch') ?: 'defaultBranch'
def branchName = buildBranch.replace("refs/heads/", "")
// Get current timestamp
DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss")
String timestamp = LocalDateTime.now().format(formatter)
version = Version + '-SNAPSHOT'

// Determine the build type
String buildType = getStringPropWithDefault('PRODUCTION_BUILD_TYPE', 'Official')
boolean onDemandBuild = buildType != null && buildType.equals('OnDemand')

// Results Aggregator
def RA_URL = 'http://ra.adtran.com'

ext {
    tag = "$Version-$BuildNumber"

    // for local testing purposes only //
    //tag = "16.2.1-B16357"
    //buildNumberTest = "16357"
    // end of local testing purposes  //

    publicationTargetName = 'publication-target'
    exportPackagesOnly = false

    if (onDemandBuild) {
        // Adjust the image name to make it clear that is is an 'On Demand' image and not an official image
        String builder = System.getenv('TRIGGEREDBY')
        if (builder == null) builder = 'unknown'
        archiveFileNameLocal = "MOD_CSM_v${tag}_${builder}_${timestamp}.tar.gz"
    }  else {
        archiveFileNameLocal = "MOD_CSM_v${tag}.tar.gz"
    }

    // Artifactory
    artifactoryServer = "gdn-artifactory.rd.advaoptical.com"
    String artifactoryServerPort = "${artifactoryServer}:9443"
    artiUser = System.getenv('ARTIFACTORY_USER')
    artiToken = System.getenv('ARTIFACTORY_TOKEN')

    // Artifactory directory
    artifactoryDirectory = "$Version-$branchName"
}

// Closure that can be used with image publisher that defines the default file server for publishing build installers
ext.publicationTarget = {
    host = publicationHost
    user = publicationUser
    certificate = publicationPrivateCertificate
    trust = true
}

def dockerCSMPropertiesFile = file('csm/docker.properties')
def dockerCSMStackFile = file('csm/docker-stack.yml')
def outputdockerCSMStackFile = file('build/csm/docker-stack.yml')

def dockerCorePropertiesFile = file('core/docker.properties')
def dockerCoreStackFile = file('core/docker-stack.yml')
def outputdockerCoreStackFile = file('build/core/docker-stack.yml')

def dockerRProxyPropertiesFile = file('mnc-rproxy/docker.properties')
def dockerRProxyStackFile = file('mnc-rproxy/docker-stack.yml')
def outputdockerRProxyStackFile = file('build/mnc-rproxy/docker-stack.yml')

def dockerServiceMigrationPropertiesFile = file('service-migration/docker.properties')
def dockerServiceMigrationStackFile = file('service-migration/docker-stack.yml')
def outputdockerServiceMigrationStackFile = file('build/service-migration/docker-stack.yml')

def loadProperties(File file) {
    def props = new Properties()
    file.withInputStream { props.load(it) }
    return props
}

def replaceCSMPlaceholders(String content, Properties props, String version) {
    content = content.replaceAll(/\$\{EOD_CSM_APP_IMAGE\}/, props.EOD_CSM_APP_IMAGE)
    content = content.replaceAll(/\$\{EOD_CSM_APP_TAG\}/, tag)
    content = content.replaceAll(/\$\{EOD_EOD_WEB_IMAGE\}/, props.EOD_EOD_WEB_IMAGE)
    content = content.replaceAll(/\$\{EOD_EOD_WEB_TAG\}/, eodWebTag)
    content = content.replaceAll(/\$\{EOD_CSM_SYNC_SERVICE_IMAGE\}/, props.EOD_CSM_SYNC_SERVICE_IMAGE)
    content = content.replaceAll(/\$\{EOD_CSM_SYNC_SERVICE_TAG\}/, tag)
    return content
}

def replaceCorePlaceholders(String content, Properties props, String version) {
    content = content.replaceAll(/\$\{INFRA_KAFKA_IMAGE\}/, props.INFRA_KAFKA_IMAGE)
    content = content.replaceAll(/\$\{INFRA_KAFKA_TAG\}/, props.INFRA_KAFKA_TAG)
    content = content.replaceAll(/\$\{EOD_CORE_ZOOKEEPER_IMAGE\}/, props.EOD_CORE_ZOOKEEPER_IMAGE)
    content = content.replaceAll(/\$\{EOD_CORE_ZOOKEEPER_TAG\}/, props.EOD_CORE_ZOOKEEPER_TAG)
    content = content.replaceAll(/\$\{JOB_MANAGER_APP_IMAGE\}/, props.JOB_MANAGER_APP_IMAGE)
    content = content.replaceAll(/\$\{JOB_MANAGER_APP_TAG\}/, tag)
    content = content.replaceAll(/\$\{MNC_WEB_IMAGE\}/, props.MNC_WEB_IMAGE)
    content = content.replaceAll(/\$\{MNC_WEB_TAG\}/, mncWebTag)
    content = content.replaceAll(/\$\{REG_MANAGER_APP_IMAGE\}/, props.REG_MANAGER_APP_IMAGE)
    content = content.replaceAll(/\$\{REG_MANAGER_APP_TAG\}/, tag)
    return content
}

def replaceRProxyPlaceholders(String content, Properties props, String version) {
    content = content.replaceAll(/\$\{MNC_RPROXY_TRAEFIK_IMAGE\}/, props.MNC_RPROXY_TRAEFIK_IMAGE)
    content = content.replaceAll(/\$\{MNC_RPROXY_TRAEFIK_TAG\}/, props.MNC_RPROXY_TRAEFIK_TAG)
    return content
}

def replaceServiceMigrationPlaceholders(String content, Properties props, String version) {
    content = content.replaceAll(/\$\{SERVICE_MIGRATION_IMAGE\}/, props.SERVICE_MIGRATION_IMAGE)
    content = content.replaceAll(/\$\{SERVICE_MIGRATION_IMAGE_TAG\}/, tag)
    return content
}

// Function that handles the HTTP POST request and takes URL and JSON payload as parameters
def sendPostRequest(String url, Map jsonPayload) {
    def connection = (HttpURLConnection) new URL(url).openConnection()

    connection.setRequestMethod("POST")
    connection.setRequestProperty("Content-Type", "application/json")
    connection.setRequestProperty("Accept", "application/json")
    connection.setDoOutput(true)

    def json = JsonOutput.toJson(jsonPayload)

    connection.outputStream.withWriter("UTF-8") { writer ->
        writer.write(json)
    }

    def responseCode = connection.getResponseCode()

    if (responseCode == HttpURLConnection.HTTP_OK || responseCode == HttpURLConnection.HTTP_CREATED) {
        def responseText = connection.inputStream.text
        println "POST request sent successfully, resource created"
        println "Response: ${cleanResponse(responseText)}"
    } else {
        def errorResponse = connection.errorStream?.text ?: "No response text available"
        println "POST request failed with response code: $responseCode\nResponse: ${cleanResponse(errorResponse)}"
    }

    connection.disconnect()
}

// Function to clean up the response by removing brackets if it’s a list with a single element
def cleanResponse(response) {
    def jsonSlurper = new JsonSlurper()
    def parsedResponse = jsonSlurper.parseText(response)

    if (parsedResponse instanceof List && parsedResponse.size() == 1) {
        return parsedResponse[0]
    }
    return response
}

encLogging {
    monitoredTasks = ['pullCSMImage', 'pullMNCCoreImage', 'pullRProxyImage']
}

task extractVersionTags {
    description('Fetches version tags from the external API for mod-web and mnc-web. Falls back to parsing version.json files if the API data is unavailable.')

    doLast {
        // Define the base URL and construct the full URL with query parameters
        def urlBase = getStringPropWithDefault('RA_API_GET_VERSION_MAPPING_URL', RA_URL + "/api/get-version-mapping/")
        def url = "${urlBase}?app_name=mnc&build_number=${BuildNumber}&release=${Version}&format=json"

        // Print release and build number
        println "Release: ${Version}"
        println "Build Number: ${BuildNumber}"

        try {
            // Attempt to fetch data from the API
            def connection = new URL(url).openConnection() as HttpURLConnection
            connection.setRequestMethod("GET")
            connection.setRequestProperty("Accept", "application/json")

            if (connection.responseCode == 200) {
                def response = connection.inputStream.text
                def jsonResponse = new JsonSlurper().parseText(response)

                // Extract `mod-web` and `mnc-web` information from `child_versions`
                def modWebData = jsonResponse[0]?.child_versions?.find { it.app_name == "mod-web" }
                def mncWebData = jsonResponse[0]?.child_versions?.find { it.app_name == "mnc-web" }

                if (modWebData && mncWebData) {
                    // Set tags from the API response
                    project.ext.eodWebTag = "${modWebData.release}-B${modWebData.build_number}"
                    project.ext.mncWebTag = "${mncWebData.release}-B${mncWebData.build_number}"

                    println "##teamcity[setParameter name='env.csm_mod_web_release' value='${modWebData.release}']"
                    println "##teamcity[setParameter name='env.csm_mod_web_build_number' value='${modWebData.build_number}']"
                    println "##teamcity[setParameter name='env.csm_mnc_web_release' value='${mncWebData.release}']"
                    println "##teamcity[setParameter name='env.csm_mnc_web_build_number' value='${mncWebData.build_number}']"
                    return // Exit the task here if API data was successful
                } else {
                    throw new RuntimeException("Version data for mod-web or mnc-web is missing in the response.")
                }
            } else if (connection.responseCode == 400) {
                def errorResponse = connection.errorStream.text
                def errorMessage = new JsonSlurper().parseText(errorResponse)?.detail ?: "Unknown error"
                println "API returned 400: Bad Request. Detail: ${errorMessage}"
            } else if (connection.responseCode == 404) {
                def errorResponse = connection.errorStream.text
                def errorMessage = new JsonSlurper().parseText(errorResponse)?.detail ?: "Unknown error"
                println "API returned 404: Not Found. Detail: ${errorMessage}. Falling back to parsing version.json files."
            } else {
                throw new RuntimeException("Failed to fetch data. HTTP response code: ${connection.responseCode}")
            }

        } catch (Exception e) {
            println "Error fetching version data: ${e.message}. Falling back to parsing version.json files."
        }

        // Fallback logic to parse version.json files if API data is not available
        def modWebVcsDir = getStringPropWithDefault('MOD_WEB_VCS_DIR', 'mod-web')
        def mncWebVcsDir = getStringPropWithDefault('MNC_WEB_VCS_DIR', 'mnc-web')

        // Read version.json for EOD Web
        def versionJsonFileEODWeb = file("../../../../$modWebVcsDir/server/version.json")
        def versionDataEODWeb = new ObjectMapper().readValue(versionJsonFileEODWeb, Map)
        project.ext.majorVersionEODWeb = versionDataEODWeb.MAJOR_VERSION
        project.ext.minorVersionEODWeb = versionDataEODWeb.MINOR_VERSION
        project.ext.patchVersionEODWeb = versionDataEODWeb.PATCH_VERSION
        project.ext.buildNumberEODWeb = versionDataEODWeb.BUILD_NUMBER
        project.ext.eodWebTag = "${project.majorVersionEODWeb}.${project.minorVersionEODWeb}.${project.patchVersionEODWeb}-B${project.buildNumberEODWeb}"

        println "##teamcity[setParameter name='env.csm_mod_web_release' value='${project.majorVersionEODWeb}.${project.minorVersionEODWeb}.${project.patchVersionEODWeb}']"
        println "##teamcity[setParameter name='env.csm_mod_web_build_number' value='${project.buildNumberEODWeb}']"

        // Read version.json for MNC Web
        def versionJsonFileMNCWeb = file("../../../../$mncWebVcsDir/server/version.json")
        def versionDataMNCWeb = new ObjectMapper().readValue(versionJsonFileMNCWeb, Map)
        project.ext.majorVersionMNCWeb = versionDataMNCWeb.MAJOR_VERSION
        project.ext.minorVersionMNCWeb = versionDataMNCWeb.MINOR_VERSION
        project.ext.patchVersionMNCWeb = versionDataMNCWeb.PATCH_VERSION
        project.ext.buildNumberMNCWeb = versionDataMNCWeb.BUILD_NUMBER
        project.ext.mncWebTag = "${project.majorVersionMNCWeb}.${project.minorVersionMNCWeb}.${project.patchVersionMNCWeb}-B${project.buildNumberMNCWeb}"

        println "##teamcity[setParameter name='env.csm_mnc_web_release' value='${project.majorVersionMNCWeb}.${project.minorVersionMNCWeb}.${project.patchVersionMNCWeb}']"
        println "##teamcity[setParameter name='env.csm_mnc_web_build_number' value='${project.buildNumberMNCWeb}']"
    }
}


task pullCSMImage() {
    group('enc.build')
    description('Pulls a CSM specified Docker image, saves it as a tar file in the build directory, and then removes the image from the local Docker registry.')

    dependsOn('extractVersionTags')
    // Define inputs and outputs
    inputs.file("csm/images_list.yml")
    outputs.dir("$buildDir/csm/docker_images")

    // Always run the task
    outputs.upToDateWhen { false }

    doLast {
        def yamlFile = file("csm/images_list.yml")

        def mapper = new ObjectMapper(new YAMLFactory())
        def imageDir = file("$buildDir/csm/docker_images")
        def imageFileList = new File("$buildDir/csm/docker_images/images_list")
        if (!imageDir.exists()) {
            imageDir.mkdirs()
        } else {
            if (imageDir.isDirectory()) {
                logger.quiet("Cleaning up ${imageDir}/ ...")
                imageDir.listFiles().each { file ->
                    if (file.isFile()) {
                        file.delete()
                    }
                }
            } else {
                logger.quiet("Removing directory ${imageDir} ...")
                imageDir.delete()
            }
        }

        // Parse the YAML file
        def data = mapper.readValue(yamlFile, Map)

        // Access the parsed data
        def images = data.spec.images
        images.each { image ->
            def imageName = image.privateRepo.split("/").last()
            def imageRepositoryUrl = image.privateRepo.split("/").first()

            // Use a local variable to hold the appropriate tag for each image
            def imageTag = tag
            if (image.name == "eod-web") {
                imageTag = eodWebTag
            }

            // Extract the tag
            def imageWithTag = image.privateRepo.replace(":latest", ":${imageTag}")
            def imageNameWithTag = imageName.replace(":latest", ":${imageTag}")
            def imageAdtranTag = "adtran/mod/csm/" + imageNameWithTag

            logger.quiet("Pulling ${imageWithTag}...")
            exec {
                commandLine "docker", "pull", "${imageWithTag}"
            }
            exec {
                commandLine "docker", "tag", "${imageWithTag}", "${imageAdtranTag}"
                logger.quiet("Tagging ${imageAdtranTag}...")
            }
            logger.quiet("Saving ${imageAdtranTag} to ${imageDir}/${imageNameWithTag}.tar...")
            exec {
                commandLine "docker", "save", "${imageAdtranTag}", "-o", "${imageDir}/${imageNameWithTag}.tar"
            }
            logger.quiet("Removing ${imageWithTag}...")
            exec {
                commandLine "docker", "rmi", "${imageWithTag}"
            }
            exec {
                commandLine "docker", "rmi", "${imageAdtranTag}"
                logger.quiet("Removing ${imageAdtranTag}...")
            }
            // Download the SBOM file for 3rd party images and save it in the build directory
            if (image.thirdParty) {
                def sbomOutputFile = new File(buildDir, "${image.name}.json")
                try {
                    exec {
                        commandLine "docker", "buildx", "imagetools", "inspect", imageWithTag,
                                    "--format", "{{ json .SBOM.SPDX }}"
                        standardOutput = new FileOutputStream(sbomOutputFile)
                        logger.quiet("Downloading SBOM for ${image.name} and saving it to ${sbomOutputFile}")
                    }
                } catch (Exception e) {
                    logger.error("Failed to download SBOM for ${image.name}.", e)
                    throw new GradleException("Error executing Docker command for ${image.name}: ${e.message}")
                }
            }
            imageFileList.append("${imageAdtranTag}\n")
        }
    }
}

task updateCSMDockerStack {
    group('enc.build')
    description('Updates the CSM Docker stack file with the properties and version.')

    dependsOn(pullCSMImage)
    doLast {
        if (!dockerCSMPropertiesFile.exists() || !dockerCSMStackFile.exists()) {
            throw new GradleException("Required files are missing")
        }

        // Load properties
        def props = loadProperties(dockerCSMPropertiesFile)
        def version = tag

        // Read the docker-stack.yml content
        def content = dockerCSMStackFile.text

        // Replace placeholders
        content = replaceCSMPlaceholders(content, props, version)

        // Ensure the output directory exists
        outputdockerCSMStackFile.parentFile.mkdirs()

        // Write the updated content to the output file
        outputdockerCSMStackFile.text = content

        println "Updated docker-stack.yml and copied to ${outputdockerCSMStackFile.path}"
    }
}

task copyCSMContent(type: Copy) {
    group('enc.build')
    description('Copies CSM scripts and utilities to the build directory.')

    dependsOn(updateCSMDockerStack)
    // Define the destination directory
    def destDir = file("$buildDir/csm")

    // Set the destination directory for the task
    into("$buildDir/csm")

    // Copy install.sh to the destination directory
    from("csm/install-csm.sh")
    from("csm/uninstall-csm.sh")
    from("csm/csm-ctl.sh")
    from("csm/configure_mnc_for_csm.sh")

    // Copy Readme.md to the desctination directory
    from("csm/Readme.md")

    from("global_scripts/utils") {
        into("utils")
    }

    // Copy the utils directory to the destination directory
    from("csm/utils/defaults.sh") {
        into("utils")
    }

    // Copy docker-stack.yml to the destination directory
    // from("csm/docker-stack.yml")

    // Copy db_pass to the destination directory
    // from("csm/db_pass")


}

task pullMNCCoreImage() {
    group('enc.build')
    description('Pulls a Core specified Docker image, saves it as a tar file in the build directory, and then removes the image from the local Docker cache.')

    dependsOn(copyCSMContent)
    // Define inputs and outputs
    inputs.file("core/images_list.yml")
    outputs.dir("$buildDir/core/docker_images")

    // Always run the task
    outputs.upToDateWhen { false }

    doLast {
        def yamlFile = file("core/images_list.yml")
        def mapper = new ObjectMapper(new YAMLFactory())
        def imageDir = file("$buildDir/core/docker_images")
        def imageFileList = new File("$buildDir/core/docker_images/images_list")
        if (!imageDir.exists()) {
            imageDir.mkdirs()
        } else {
            if (imageDir.isDirectory()) {
                logger.quiet("Cleaning up ${imageDir}/ ...")
                imageDir.listFiles().each { file ->
                    if (file.isFile()) {
                        file.delete()
                    }
                }
            } else {
                logger.quiet("Removing directory ${imageDir} ...")
                imageDir.delete()
            }
        }
        // Parse the YAML file
        def data = mapper.readValue(yamlFile, Map)

        // Access the parsed data
        def images = data.spec.images
        images.each { image ->
            def imageName = image.privateRepo.split("/").last()
            def imageRepositoryUrl = image.privateRepo.split("/").first()

            // Use a local variable to hold the appropriate tag for each image
            def imageTag = tag
            if (image.name == "mnc-web") {
                imageTag = mncWebTag
            }

            // Extract the tag
            def imageWithTag = image.privateRepo.replace(":latest", ":${imageTag}")
            def imageNameWithTag = imageName.replace(":latest", ":${imageTag}")
            def imageAdtranTag = "adtran/mod/core/" + imageNameWithTag

            logger.quiet("Pulling ${imageWithTag}...")
            exec {
                commandLine "docker", "pull", "${imageWithTag}"
            }
            exec {
                commandLine "docker", "tag", "${imageWithTag}", "${imageAdtranTag}"
                logger.quiet("Tagging ${imageAdtranTag}...")
            }
            logger.quiet("Saving ${imageAdtranTag} to ${imageDir}/${imageNameWithTag}.tar...")
            exec {
                commandLine "docker", "save", "${imageAdtranTag}", "-o", "${imageDir}/${imageNameWithTag}.tar"
            }
            logger.quiet("Removing ${imageWithTag}...")
            exec {
                commandLine "docker", "rmi", "${imageWithTag}"
            }
            exec {
                commandLine "docker", "rmi", "${imageAdtranTag}"
                logger.quiet("Removing ${imageAdtranTag}...")
            }
            // Download the SBOM file for 3rd party images and save it in the build directory
            if (image.thirdParty) {
                def sbomOutputFile = new File(buildDir, "${image.name}.json")
                try {
                    exec {
                        commandLine "docker", "buildx", "imagetools", "inspect", imageWithTag,
                                    "--format", "{{ json .SBOM.SPDX }}"
                        standardOutput = new FileOutputStream(sbomOutputFile)
                        logger.quiet("Downloading SBOM for ${image.name} and saving it to ${sbomOutputFile}")
                    }
                } catch (Exception e) {
                    logger.error("Failed to download SBOM for ${image.name}.", e)
                    throw new GradleException("Error executing Docker command for ${image.name}: ${e.message}")
                }
            }
            imageFileList.append("${imageAdtranTag}\n")
        }
    }
}

task updateCoreDockerStack {
    group('enc.build')
    description('Updates the Core Docker stack file with the properties and version.')

    dependsOn(pullMNCCoreImage)
    doLast {
        if (!dockerCorePropertiesFile.exists() || !dockerCoreStackFile.exists()) {
            throw new GradleException("Required files are missing")
        }

        // Load properties
        def props = loadProperties(dockerCorePropertiesFile)
        def version = tag

        // Read the docker-stack.yml content
        def content = dockerCoreStackFile.text

        // Replace placeholders
        content = replaceCorePlaceholders(content, props, version)

        // Ensure the output directory exists
        outputdockerCoreStackFile.parentFile.mkdirs()

        // Write the updated content to the output file
        outputdockerCoreStackFile.text = content

        println "Updated docker-stack.yml and copied to ${outputdockerCoreStackFile.path}"
    }
}

task copyCoreContent(type: Copy) {
    group('enc.build')
    description('Copies Core scripts and utilities to the build directory.')

    dependsOn(updateCoreDockerStack)
    // Define the destination directory
    def destDir = file("$buildDir/core")

    // Set the destination directory for the task
    into("$buildDir/core")

    // Copy install.sh to the destination directory
    from("core/install-core.sh")
    from("core/uninstall-core.sh")
    from("core/core-ctl.sh")

    // Copy Readme.md to the desctination directory
    from("core/Readme.md")

    from("global_scripts/utils") {
        into("utils")
    }

    // Copy the utils directory to the destination directory
    from("core/utils/defaults.sh") {
        into("utils")
    }

    from("core/config/kafka/log4j.properties") {
        into("config/kafka")
    }

    from("core/config/zookeeper/logback.xml") {
        into("config/zookeeper")
    }

    // Copy docker-stack.yml to the destination directory
    // from("csm/docker-stack.yml")

    // Copy db_pass to the destination directory
    //from("core/db_pass")
}

task pullRProxyImage() {
    group('enc.build')
    description('Pulls a RProxy specified Docker image, saves it as a tar file in the build directory, and then removes the image from the local Docker registry.')

    dependsOn(copyCoreContent)
    // Define inputs and outputs
    inputs.file("mnc-rproxy/images_list.yml")
    outputs.dir("$buildDir/mnc-rproxy/docker_images")

    // Always run the task
    outputs.upToDateWhen { false }

    doLast {
        def yamlFile = file("mnc-rproxy/images_list.yml")

        def mapper = new ObjectMapper(new YAMLFactory())
        def imageDir = file("$buildDir/mnc-rproxy/docker_images")
        def imageFileList = new File("$buildDir/mnc-rproxy/docker_images/images_list")
        if (!imageDir.exists()) {
            imageDir.mkdirs()
        } else {
            if (imageDir.isDirectory()) {
                logger.quiet("Cleaning up ${imageDir}/ ...")
                imageDir.listFiles().each { file ->
                    if (file.isFile()) {
                        file.delete()
                    }
                }
            } else {
                logger.quiet("Removing directory ${imageDir} ...")
                imageDir.delete()
            }
        }

        // Parse the YAML file
        def data = mapper.readValue(yamlFile, Map)

        // Access the parsed data
        def images = data.spec.images
        images.each { image ->
            def imageName = image.privateRepo.split("/").last()
            def imageRepositoryUrl = image.privateRepo.split("/").first()

            // Use a local variable to hold the appropriate tag for each image
            def imageTag = tag

            // Extract the tag
            def imageWithTag = image.privateRepo.replace(":latest", ":${imageTag}")
            def imageNameWithTag = imageName.replace(":latest", ":${imageTag}")
            def imageAdtranTag = "adtran/mod/mnc-rproxy/" + imageNameWithTag

            logger.quiet("Pulling ${imageWithTag}...")
            exec {
                commandLine "docker", "pull", "${imageWithTag}"
            }
            exec {
                commandLine "docker", "tag", "${imageWithTag}", "${imageAdtranTag}"
                logger.quiet("Tagging ${imageAdtranTag}...")
            }
            logger.quiet("Saving ${imageAdtranTag} to ${imageDir}/${imageNameWithTag}.tar...")
            exec {
                commandLine "docker", "save", "${imageAdtranTag}", "-o", "${imageDir}/${imageNameWithTag}.tar"
            }
            logger.quiet("Removing ${imageWithTag}...")
            exec {
                commandLine "docker", "rmi", "${imageWithTag}"
            }
            exec {
                commandLine "docker", "rmi", "${imageAdtranTag}"
                logger.quiet("Removing ${imageAdtranTag}...")
            }
            // Download the SBOM file for 3rd party images and save it in the build directory
            if (image.thirdParty) {
                def sbomOutputFile = new File(buildDir, "${image.name}.json")
                try {
                    exec {
                        commandLine "docker", "buildx", "imagetools", "inspect", imageWithTag,
                                    "--format", "{{ json .SBOM.SPDX }}"
                        standardOutput = new FileOutputStream(sbomOutputFile)
                        logger.quiet("Downloading SBOM for ${image.name} and saving it to ${sbomOutputFile}")
                    }
                } catch (Exception e) {
                    logger.error("Failed to download SBOM for ${image.name}.", e)
                    throw new GradleException("Error executing Docker command for ${image.name}: ${e.message}")
                }
            }
            imageFileList.append("${imageAdtranTag}\n")
        }
    }
}

task updateRProxyDockerStack {
    group('enc.build')
    description('Updates the RProxy Docker stack file with the properties and version.')

    dependsOn(pullRProxyImage)
    doLast {
        if (!dockerRProxyPropertiesFile.exists() || !dockerRProxyStackFile.exists()) {
            throw new GradleException("Required files are missing")
        }

        // Load properties
        def props = loadProperties(dockerRProxyPropertiesFile)
        def version = tag

        // Read the docker-stack.yml content
        def content = dockerRProxyStackFile.text

        // Replace placeholders
        content = replaceRProxyPlaceholders(content, props, version)

        // Ensure the output directory exists
        outputdockerRProxyStackFile.parentFile.mkdirs()

        // Write the updated content to the output file
        outputdockerRProxyStackFile.text = content

        println "Updated docker-stack.yml and copied to ${outputdockerRProxyStackFile.path}"
    }
}

task copyRProxyContent(type: Copy) {
    group('enc.build')
    description('Copies Core scripts and utilities to the build directory.')

    dependsOn(updateRProxyDockerStack)
    // Define the destination directory
    def destDir = file("$buildDir/mnc-rproxy")

    // Set the destination directory for the task
    into("$buildDir/mnc-rproxy")

    // Copy install.sh to the destination directory
    from("mnc-rproxy/install-mnc-rproxy.sh")
    from("mnc-rproxy/uninstall-mnc-rproxy.sh")
    from("mnc-rproxy/mnc-rproxy-ctl.sh")

    // Copy Readme.md to the desctination directory
    from("mnc-rproxy/Readme.md")

    from("global_scripts/utils") {
        into("utils")
    }

    // Copy the utils directory to the destination directory
    from("mnc-rproxy/utils/defaults.sh") {
        into("utils")
    }

    from("mnc-rproxy/utils/testCertificates.sh") {
        into("utils")
    }

    from("mnc-rproxy/utils/generateCertificates.sh") {
        into("utils")
    }

    from("mnc-rproxy/utils/prepareTraefikDynamicConfig.sh") {
        into("utils")
    }

    //from("mnc-rproxy/traefik.yml")

    from("mnc-rproxy/config") {
        into("config")
    }
}

task pullServiceMigrationImage() {
    group('enc.build')
    description('Pulls a Service Migration specified Docker image, saves it as a tar file in the build directory, and then removes the image from the local Docker registry.')

    dependsOn(copyRProxyContent)
    // Define inputs and outputs
    inputs.file("service-migration/images_list.yml")
    outputs.dir("$buildDir/service-migration/docker_images")

    // Always run the task
    outputs.upToDateWhen { false }

    doLast {
        def yamlFile = file("service-migration/images_list.yml")

        def mapper = new ObjectMapper(new YAMLFactory())
        def imageDir = file("$buildDir/service-migration/docker_images")
        def imageFileList = new File("$buildDir/service-migration/docker_images/images_list")
        if (!imageDir.exists()) {
            imageDir.mkdirs()
        } else {
            if (imageDir.isDirectory()) {
                logger.quiet("Cleaning up ${imageDir}/ ...")
                imageDir.listFiles().each { file ->
                    if (file.isFile()) {
                        file.delete()
                    }
                }
            } else {
                logger.quiet("Removing directory ${imageDir} ...")
                imageDir.delete()
            }
        }

        // Parse the YAML file
        def data = mapper.readValue(yamlFile, Map)

        // Access the parsed data
        def images = data.spec.images
        images.each { image ->
            def imageName = image.privateRepo.split("/").last()
            def imageRepositoryUrl = image.privateRepo.split("/").first()

            // Use a local variable to hold the appropriate tag for each image
            def imageTag = tag

            // Extract the tag
            def imageWithTag = image.privateRepo.replace(":latest", ":${imageTag}")
            def imageNameWithTag = imageName.replace(":latest", ":${imageTag}")
            def imageAdtranTag = "adtran/mod/service-migration/" + imageNameWithTag

            logger.quiet("Pulling ${imageWithTag}...")
            exec {
                commandLine "docker", "pull", "${imageWithTag}"
            }
            exec {
                commandLine "docker", "tag", "${imageWithTag}", "${imageAdtranTag}"
                logger.quiet("Tagging ${imageAdtranTag}...")
            }
            logger.quiet("Saving ${imageAdtranTag} to ${imageDir}/${imageNameWithTag}.tar...")
            exec {
                commandLine "docker", "save", "${imageAdtranTag}", "-o", "${imageDir}/${imageNameWithTag}.tar"
            }
            logger.quiet("Removing ${imageWithTag}...")
            exec {
                commandLine "docker", "rmi", "${imageWithTag}"
            }
            exec {
                commandLine "docker", "rmi", "${imageAdtranTag}"
                logger.quiet("Removing ${imageAdtranTag}...")
            }
            // Download the SBOM file for 3rd party images and save it in the build directory
            if (image.thirdParty) {
                def sbomOutputFile = new File(buildDir, "${image.name}.json")
                try {
                    exec {
                        commandLine "docker", "buildx", "imagetools", "inspect", imageWithTag,
                                    "--format", "{{ json .SBOM.SPDX }}"
                        standardOutput = new FileOutputStream(sbomOutputFile)
                        logger.quiet("Downloading SBOM for ${image.name} and saving it to ${sbomOutputFile}")
                    }
                } catch (Exception e) {
                    logger.error("Failed to download SBOM for ${image.name}.", e)
                    throw new GradleException("Error executing Docker command for ${image.name}: ${e.message}")
                }
            }
            imageFileList.append("${imageAdtranTag}\n")
        }
    }
}

task updateServiceMigrationDockerStack {
    group('enc.build')
    description('Updates the Service Migration Docker stack file with the properties and version.')

    dependsOn(pullServiceMigrationImage)
    doLast {
        if (!dockerServiceMigrationPropertiesFile.exists() || !dockerServiceMigrationStackFile.exists()) {
            throw new GradleException("Required files are missing")
        }

        // Load properties
        def props = loadProperties(dockerServiceMigrationPropertiesFile)
        def version = tag

        // Read the docker-stack.yml content
        def content = dockerServiceMigrationStackFile.text

        // Replace placeholders
        content = replaceServiceMigrationPlaceholders(content, props, version)

        // Ensure the output directory exists
        outputdockerServiceMigrationStackFile.parentFile.mkdirs()

        // Write the updated content to the output file
        outputdockerServiceMigrationStackFile.text = content

        println "Updated docker-stack.yml and copied to ${outputdockerServiceMigrationStackFile.path}"
    }
}

task copyServiceMigrationContent(type: Copy) {
    group('enc.build')
    description('Copies ServiceMigration scripts and utilities to the build directory.')

    dependsOn(updateServiceMigrationDockerStack)
    // Define the destination directory
    def destDir = file("$buildDir/service-migration")

    // Set the destination directory for the task
    into("$buildDir/service-migration")

    // Copy install.sh to the destination directory
    from("service-migration/install-service-migration.sh")
    from("service-migration/uninstall-service-migration.sh")
    from("service-migration/service-migration-ctl.sh")

    // Copy Readme.md to the desctination directory
    from("service-migration/Readme.md")

    from("global_scripts/utils") {
        into("utils")
    }

    // Copy the utils directory to the destination directory
    from("service-migration/utils/defaults.sh") {
        into("utils")
    }

    from("service-migration/config") {
        into("config")
    }
}

task copyModSdpScript(type: Copy) {
    group = 'enc.build'
    description = 'Copy sdp.sh, ncstacks.server, ncstacks.service, defaults.sh to build directory'

    dependsOn(copyServiceMigrationContent)

    def destDir = file("$buildDir")
    // Set the destination directory for the task
    into("$buildDir")

    from("global_scripts/sdp.sh")
    from("global_scripts/ncstacks.server")
    from("global_scripts/ncstacks.service")
    from("global_scripts/defaults.sh")
}

task tarCSMCoreContent(type: Tar) {
    group('enc.build')
    description('Create package with CSM and Core and MNC-Rproxy')

    dependsOn(copyModSdpScript)

    // Define the archive name and destination directory
    archiveFileName = archiveFileNameLocal
    destinationDirectory = file("$buildDir/archives")

    // Use GZIP compression
    compression = Compression.GZIP

    // Include $buildDir/csm directory into MOD_CSM/csm
    from("$buildDir/csm") {
        // Include all files and directories
        include '**/*'
        into 'MOD_CSM/csm'
    }

    // Include $buildDir/core directory into MOD_CSM/core
    from("$buildDir/core") {
        include '**/*'
        into 'MOD_CSM/core'
    }

    // Include $buildDir/mnc-rproxy directory into MOD_CSM/mnc-rproxy
    from("$buildDir/mnc-rproxy") {
        include '**/*'
        into 'MOD_CSM/mnc-rproxy'
    }

     // Include $buildDir/service-migration directory into MOD_CSM/service-migration
    from("$buildDir/service-migration") {
        include '**/*'
        into 'MOD_CSM/service-migration'
    }

    // Include $buildDir/sdp.sh script into MOD_CSM/
    from("$buildDir/") {
        include 'sdp.sh', 'ncstacks.service', 'ncstacks.server', 'defaults.sh'
        into 'MOD_CSM'
    }

    into('')
}



imagePublisher {
    addTarget publicationTargetName, publicationTarget
}

task publishToSitHostMODCSCoreMPackage(type: ImagePublisherTask) {
    group('enc.build')
    description('Publish package with MOD CSM/CORE/MNC-RPROXY/service-migration on sitnms1')

    dependsOn(tarCSMCoreContent)

    target = publicationTargetName
    destination = '/opt/ftp/enc/builds/production'
    updateSymlink = true
    segmentByDay = true
    cleanup = false
    extractZip = false

    setFiles  {
        return file("$buildDir/archives/${archiveFileNameLocal}")
    }
    doLast {
        print("##teamcity[buildStatus status='SUCCESS' text='Success: published ${archiveFileNameLocal}']\n")
    }
}

task publishOnDemandToSitHostMODCSCoreMPackage(type: ImagePublisherTask) {
    group('enc.build')
    description('Publish on demand package with MOD CSM/CORE/MNC-RPROXY/service-migration on sitnms1')

    dependsOn(tarCSMCoreContent)

    target = publicationTargetName
    destination = '/opt/ftp/enc/builds/ad-hoc'
    updateSymlink = false
    segmentByDay = true
    cleanup = false
    extractZip = false

    setFiles  {
        return file("$buildDir/archives/${archiveFileNameLocal}")
    }
    doLast {
        print("##teamcity[buildStatus status='SUCCESS' text='Success: published ${archiveFileNameLocal}']\n")
        if (onDemandBuild) {
            // Notify the user of the image published via Team City API
            String targetDir =  LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))

            // Create simple html file to add to build artifacts on Team City
            File resultHtml = file("$projectDir/result.html")

            resultHtml.withWriter('UTF8') {BufferedWriter writer ->
                writer.write("<HTML><BODY><P>" +
                        "On demand image <a href=\"https://gdn-s-sitnms1/enc/builds/ad-hoc/$targetDir/${archiveFileNameLocal}\">" +
                        "${archiveFileNameLocal}</a> generated</P><P>Full URL is https://gdn-s-sitnms1/enc/builds/ad-hoc/$targetDir/${archiveFileNameLocal}</P>"+
                        "<P>All on demand images can be found at <a href=\"https://gdn-s-sitnms1/enc/builds/ad-hoc/\">" +
                        "https://gdn-s-sitnms1/enc/builds/ad-hoc</a></P>" +
                        "<BODY></HTML>")
            }
        }
    }
}

// task deployCSMPackageToArti {
//     dependsOn(publishToSitHostMODCSMPackage)

//     doLast {
//         def archiveDir = file("$buildDir/archives")
//         def archiveFile = file("$archiveDir/MOD_CSM_v${tag}.tar.gz")

//         if (!archiveDir.exists()) {
//             throw new GradleException("Directory ${archiveDir} does not exist.")
//         }

//         println "Contents of $archiveDir:"
//         archiveDir.eachFile { file ->
//             println file.name
//         }

//         if (!archiveFile.exists()) {
//             throw new GradleException("File ${archiveFile} does not exist.")
//         }

//         exec {
//             workingDir archiveDir
//             commandLine "curl", "-u", "${artiUser}:${artiToken}", "-X", "PUT", "https://${artifactoryServer}/artifactory/NMS/com/adtran/enc/mod_csm/${Version}/MOD_CSM_v${tag}.tar.gz", "-T", "MOD_CSM_v${tag}.tar.gz"
//         }
//     }

// }

publishing {
    publications {
        mod_csmModel(MavenPublication) {
            artifactId = 'mod_csm'
            groupId = 'com.adtran.enc'
            version = Version + '-SNAPSHOT'

            artifact("$buildDir/archives/MOD_CSM_v${tag}.tar.gz") {
                classifier = null
                extension = 'tar.gz'
            }
        }
    }
    repositories {
        maven {
            url "https://${artifactoryServer}/artifactory/NMS"
            credentials {
                username = "${artiUser}"
                password = "${artiToken}"
            }
        }
    }
}

task sendPostRequestTask {
    description('Sends a POST request to the Results Aggregator with the version mapping information.')
    doLast {
        def targetUrl = getStringPropWithDefault('RA_API_VERSION_MAPPING_URL', RA_URL + "/api/version-mapping/")
        def mod_web_release = getStringPropWithDefault('csm_mod_web_release', null)
        def mod_web_build_number = getStringPropWithDefault('csm_mod_web_build_number', null)
        def mnc_web_release = getStringPropWithDefault('csm_mnc_web_release', null)
        def mnc_web_build_number = getStringPropWithDefault('csm_mnc_web_build_number', null)

        def jsonPayload = [
            app_name    : "mnc",
            release     : "${Version}",
            build_number: "${RawBuildNumber}",
            apps        : [
                [
                    app_name    : "mod-web",
                    release     : "${mod_web_release}",
                    build_number: "${mod_web_build_number}"
                ],
                [
                    app_name    : "mnc-web",
                    release     : "${mnc_web_release}",
                    build_number: "${mnc_web_build_number}"
                ],
                [
                    app_name    : "service-synchronizer",
                    release     : "${Version}",
                    build_number: "${RawBuildNumber}"
                ],
                [
                    app_name    : "job-manager",
                    release     : "${Version}",
                    build_number: "${RawBuildNumber}"
                ],
                [
                    app_name    : "csm-app",
                    release     : "${Version}",
                    build_number: "${RawBuildNumber}"
                ],
                [
                    app_name    : "service-synchronizer-tool",
                    release     : "${Version}",
                    build_number: "${RawBuildNumber}"
                ]

            ]
        ]

        sendPostRequest(targetUrl, jsonPayload)
    }
}

// tasks['publishMod_csmModelPublicationToMavenRepository'].doLast {
//     exec {
//         commandLine "curl", "-u", "${artiUser}:${artiToken}", "-X", "PUT", "https://${artifactoryServer}/artifactory/NMS/com/adtran/enc/mod_csm/${Version}/MOD_CSM_v${tag}.tar.gz", "-T", "MOD_CSM_v${tag}.tar.gz"
//     }
// }


task buildCSMPackage {
    group('enc.build')
    description('Start Building CSM and Core Package')

    dependsOn(publishToSitHostMODCSCoreMPackage)
}

task buildMNCCorePackage {
    group('enc.build')
    description('Start Building MNC-RPROXY')
}