#!/bin/bash

typeset -r dirOfThisScript=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
source "${dirOfThisScript}"/utils/docker_helper.sh
source "${dirOfThisScript}"/utils/defaults.sh

cd "${dirOfThisScript}" || exit 1

INSTALL_DIR="/opt/adva/stacks/csm"
HELP_MSG=$(
  cat <<-EOF
Usage: ./install-csm.sh <command>

Commands:
  install                   Install and deploy CSM.
  --help, -h                Display this help message.

Examples:
  ./install-csm.sh install
EOF
)

# Function to create Docker swarm network
create_csm_network() {
  if docker network ls | grep -q "$NETWORK_NAME"; then
    echo "Network $NETWORK_NAME already exists."
  else
    docker network create --driver overlay "$NETWORK_NAME"
    if docker network ls | grep -q "$NETWORK_NAME"; then
      echo "Network $NETWORK_NAME created successfully."
    else
      echo "Failed to create network $NETWORK_NAME."
    fi
  fi
}

install() {
  # Check if the stack is already installed
  if docker stack ls | grep -qw "$STACK_NAME"; then
    echo "Stack '$STACK_NAME' is already installed. Skipping installation."
    return 0
  fi

  echo "Installing MOD CSM"
  echo ""
  read_mnc_ip
  mkdir -p ${INSTALL_DIR}
  cp -r utils ${INSTALL_DIR}
  cp *.sh *.yml Readme.md ${INSTALL_DIR}

  assertDocker

  # stop stack before installing newer version
  # if stack does not exist or is not running, this call does nothing
  bash "${dirOfThisScript}/csm-ctl.sh" --id $STACK_NAME stop

  source ./utils/import-docker-images.sh
  create_csm_network
  create_db_secret $SECRET_NAME $SECRET_USERNAME
  mkdir -p src

  handle_certificates "$CERTSDIR"

  bash "${dirOfThisScript}/csm-ctl.sh" --id $STACK_NAME start

  echo "MOD CSM installation finished"
  echo ""
}

if [[ $# -eq 0 ]]; then
  echo "No arguments provided."
  echo "$HELP_MSG"
  exit 1
fi

case "$1" in
install)
  install
  ;;
--help | -h)
  echo "$HELP_MSG"
  exit 0
  ;;
*)
  echo "Invalid command: $1"
  echo "$HELP_MSG"
  exit 1
  ;;
esac
