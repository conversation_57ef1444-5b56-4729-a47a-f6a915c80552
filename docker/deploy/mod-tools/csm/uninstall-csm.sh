#!/bin/bash

# Get the directory of this script
typeset -r dirOfThisScript=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)

# Source utility scripts
source "${dirOfThisScript}"/utils/docker_helper.sh
source "${dirOfThisScript}"/utils/defaults.sh

# Define installation directory
INSTALL_DIR="/opt/adva/stacks/csm"

# Function to uninstall the stack and all related resources
uninstall_stack() {
  actionMsg="Start: uninstall procedure of ${STACK_NAME}"
  echo $actionMsg

  bash "${dirOfThisScript}"/csm-ctl.sh --id $STACK_NAME stop
  bash "${dirOfThisScript}"/utils/stack_delete_all.sh "$STACK_NAME"

  deleteStackVolumes "$STACK_NAME"
  removeDanglingVolumes

  if [[ $(docker images | grep adtran/mod/csm/ || true) ]]; then
    for image in csm_app service-sync-app eod-web; do
      image=$(docker images --format '{{.Repository}}:{{.Tag}}' | grep adtran/mod/csm/${image})
      docker image rm ${image}
    done
  else
    # remove images pulled from artifactory
    for image in csm_app service-sync-app eod-web; do
      docker images --format "{{.ID}} {{.Repository}}:{{.Tag}}" | grep "/${image}:" |
        awk '{ print $1 }' | uniq | xargs docker rmi -f ${image} || true
    done
  fi

  docker network rm "$NETWORK_NAME"
  docker config rm "$ZOOKEEPER_LOG4J_CONFIG_NAME"

  echo "Cleaning up directory $INSTALL_DIR"
  rm -rf ${INSTALL_DIR}

  actionMsg="End: uninstall procedure"
  echo "${actionMsg}"
}

uninstall_stack
