version: "3.7"

x-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "${EOD_LOG_SIZE:-100m}"
    max-file: "${EOD_LOG_MAX_FILES:-3}"

x-deploy: &default-deploy
  replicas: 1
  update_config:
    parallelism: 1
    delay: 10s
  restart_policy:
    condition: any
    max_attempts: 5
    window: 30m

x-eod-healthcheck: &eod-healthcheck
  interval: 60s
  timeout: 20s
  retries: 3
  start_period: 30s

services:
  csm-app:
    image: "${EOD_CSM_APP_IMAGE}:${EOD_CSM_APP_TAG}"
    networks:
      - mnc-core_net-kafka
      - mnc-rproxy_net-traefik
    deploy:
      <<: *default-deploy
      resources:
        limits:
          memory: 3072M
          cpus: '6.0'
        reservations:
          cpus: "0.5"
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.csm_csm-app.rule=Header(`enc-service`, `enc-eod-csm`)"
        - "traefik.http.routers.csm_csm-app.entrypoints=websecure"
        - "traefik.http.services.csm_csm-app.loadbalancer.server.port=8989"
        - "traefik.swarm.network=mnc-rproxy_net-traefik"
        - "traefik.http.routers.csm_csm-app.tls=true"
    environment:
      DB_URL_PARAMS: default-docker
      ENC_SERVER_BASE: "https://${MNC_HOST}:8443"
      JAVA_OPTS: "-Xms256m -Xmx1500m -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError"
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      TZ: UTC
      VERIFY_CERTS: "false"
      ZOOKEEPER_SERVER: zookeeper:2181
    healthcheck:
      test: ["CMD-SHELL", "healthcheck"]
      interval: 20s
      timeout: 10s
      retries: 3
      start_period: 120s
    volumes:
      - /var/run/postgresql/.s.PGSQL.5432:/var/run/postgresql/.s.PGSQL.5432
      - csm-app-logs:/app/var/log
      - csm-app-tmp:/tmp
      - csm-app-ts:/app/var/ts
    logging: *default-logging
    secrets:
      - db-password
      - db-username
    ulimits:
      nofile:
        soft: 1024
        hard: 4096
    read_only: true
    cap_drop:
      - ALL

  eod-web:
    image: "${EOD_EOD_WEB_IMAGE}:${EOD_EOD_WEB_TAG}"
    networks:
      - mnc-rproxy_net-traefik
      - mnc-core_net-kafka
    volumes:
      - eod-web-logs:/usr/eod-web/server/logs
      - user-db:/usr/eod-web/server/db/database/user_preferences
    environment:
      CSM_SERVER: csm-app
      ENC_SERVER: ${MNC_HOST}
      IS_SECURE: "false"
      JOBS_IS_SECURE: "false"
      JOBS_SERVER_PORT: 8108
      JOBS_SERVER: "job-manager"
      KAFKA_IP: "kafka"
      KAFKA_PORT: "9092"
      TZ: UTC
      VERIFY_CERTS: "false"
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"
    read_only: true
    deploy:
      update_config:
        parallelism: 1
        delay: 15s
      restart_policy:
        condition: on-failure
      resources:
        limits:
          memory: 1024M
          cpus: '2.0'
        reservations:
          cpus: '0.05'  # Plenty for 2–3% mean
      labels:
        - "traefik.enable=true"
        - "traefik.swarm.network=mnc-rproxy_net-traefik"
        - "traefik.http.services.csm_eod-web.loadbalancer.server.port=11443"
        - 'traefik.http.routers.csm_eod-web-auth.rule=(PathPrefix(`/mod`) || PathPrefix(`/mnc`)) && QueryRegexp(`otk`,`.+`)'
        - "traefik.http.routers.csm_eod-web-auth.entrypoints=websecure"
        - "traefik.http.routers.csm_eod-web-auth.tls=true"
        - "traefik.http.routers.csm_eod-web-auth.priority=20"
        - "traefik.http.routers.csm_eod-web-auth.service=csm_eod-web"

        - "traefik.http.middlewares.otk-auth.forwardauth.address=https://MNC_HOST_PLACEHOLDER:8443/advabase/security/validateOtk"
        - "traefik.http.middlewares.otk-auth.forwardauth.trustforwardheader=true"
        - "traefik.http.middlewares.otk-auth.forwardauth.tls.insecureskipverify=true"
        - "traefik.http.middlewares.otk-auth.forwardauth.authresponseheaders=X-Session-Id, sessionID"
        - "traefik.http.routers.csm_eod-web-auth.middlewares=otk-auth"

        - 'traefik.http.routers.csm_eod-web-redirect.rule=(PathPrefix(`/mod`) || PathPrefix(`/mnc`)) && !QueryRegexp(`otk`,`.+`)'
        - "traefik.http.routers.csm_eod-web-redirect.entrypoints=websecure"
        - "traefik.http.routers.csm_eod-web-redirect.tls=true"
        - "traefik.http.routers.csm_eod-web-redirect.priority=15"
        - "traefik.http.routers.csm_eod-web-redirect.service=csm_eod-web"
    cap_drop:
      - ALL
    cap_add:
      - CHOWN
      - DAC_OVERRIDE
      - KILL
      - SETFCAP
      - SETGID
      - SETUID
    ulimits:
      nofile:
        soft: 1024
        hard: 4096
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "wget --no-verbose --no-check-certificate --tries=1 --spider http://localhost:11443/mod/ || exit 1",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s


  service-synchronizer:
    image: "${EOD_CSM_SYNC_SERVICE_IMAGE}:${EOD_CSM_SYNC_SERVICE_TAG}"
    networks:
      - mnc-core_net-kafka
      - mnc-rproxy_net-traefik
    volumes:
      - service-synchronizer-logs:/app/var/log
      - service-synchronizer-reports:/app/var/reports
      - service-synchronizer-tmp:/tmp
      - service-synchronizer-ts:/app/var/ts
    deploy:
      <<: *default-deploy
      resources:
        limits:
          memory: 1536M
          cpus: '3.0'
        reservations:
          cpus: "0.2"
    logging: *default-logging
    healthcheck:
      test: ["CMD-SHELL", "healthcheck"]
      interval: 20s
      timeout: 10s
      retries: 3
      start_period: 120s
    environment:
      CSM_SERVER_BASE: "http://csm-app:8989"
      ENC_SERVER_BASE: "https://${MNC_HOST}:8443"
      JAVA_OPTS: "-Xms256m -Xmx1500m -XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError"
      KAFKA_BOOTSTRAP_SERVERS: kafka:9092
      TZ: UTC
      VERIFY_CERTS: "false"
      ZOOKEEPER_SERVER: zookeeper:2181
    ulimits:
      nofile:
        soft: 1024
        hard: 4096
    read_only: true
    cap_drop:
      - ALL

networks:
  mnc-core_net-kafka:
    external: true
  mnc-rproxy_net-traefik:
    external: true

secrets:
  db-password:
    external: true
    name: mnc-postgresql-db-pw
  db-username:
    external: true
    name: mnc-postgresql-db-user

volumes:
  csm-app-logs:
  csm-app-tmp:
  csm-app-ts:
  eod-web-logs:
  service-synchronizer-logs:
  service-synchronizer-reports:
  service-synchronizer-tmp:
  service-synchronizer-ts:
  user-db:

