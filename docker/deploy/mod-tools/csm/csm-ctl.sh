#!/bin/bash

set -e # automatic exit on error

typeset -r dirOfThisScript=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
source "${dirOfThisScript}"/utils/docker_helper.sh
source "${dirOfThisScript}"/utils/defaults.sh
cd "${dirOfThisScript}" || exit 1

assertDocker

ID="csm"
HELP_MSG=$(
  cat <<-EOF
Usage: ./csm-ctl.sh [--id <deploy-id>] <command>

Options:
  --id <deploy-id>          Specify the stack ID (default: csm)

Commands:
  status                    Check the status of all services in the stack and whether certificate verification is enabled or not.
  start                     Start the stack.
  stop                      Stop the stack.
  restart                   Restart the stack (stop and start).
  logs                      Gather and display logs for the stack.
  set_certs_verify          Enable or Disable Certificate Verification - set the VERIFY_CERTS environment variable (true/false) based on user input.
  --help, -h                Display this help message.

Examples:
  ./csm.sh --id my-deployment start
  ./csm.sh restart
  ./csm.sh logs
EOF
)

# Initialize ACTION and ID variables
ACTION=""

if [[ $# -eq 0 ]]; then
  echo "No arguments provided."
  echo "$HELP_MSG"
  exit 1
fi

while [[ $# -gt 0 ]]; do
  case "$1" in
  --id)
    if [[ -n "$2" && "$2" != "--"* ]]; then
      ID="$2"
      shift 2
    else
      echo "Invalid argument for --id"
      echo "$HELP_MSG"
      exit 1
    fi
    ;;
  --help | -h)
    echo "$HELP_MSG"
    exit 0
    ;;
  status | start | stop | restart | logs | set_certs_verify)
    ACTION="$1"
    shift
    ;;
  *)
    echo "Invalid argument: $1"
    echo "$HELP_MSG"
    exit 1
    ;;
  esac
done

if [[ -z "$ACTION" ]]; then
  echo "Invalid arguments."
  echo "$HELP_MSG"
  exit 1
fi

case ${ACTION} in
status)
  check_stack_status "$ID"
  check_verify_certs "$ID"
  ;;
start)
  read_mnc_ip
  deployDockerStack "$ID"
  ;;
stop)
  deleteDockerStack "$ID"
  ;;
restart)
  deleteDockerStack "$ID"
  read_mnc_ip
  deployDockerStack "$ID"
  ;;
logs)
  healthcheck_dir="${LOG_DIR}/healthcheck_$ID"
  mkdir -p "${healthcheck_dir}"
  getContainerFile "$ID" "$CSM_APP_CONTAINER_NAME" "$SOURCE_LOG"
  getContainerFile "$ID" "$MOD_WEB_CONTAINER_NAME" "$SOURCE_LOG_WEB_UI"
  getContainerFile "$ID" "$SERVICE_SYNCHRONIZER_CONTAINER_NAME" "$SERVICE_SYNCHRONIZER_LOG_PATH"
  gatherLogs "$ID"
  ;;
set_certs_verify)
  set_certs_verify "$ID"
  ;;
*)
  echo "Unknown action"
  echo "$HELP_MSG"
  ;;
esac
