#!/bin/bash

typeset -r csmUtilDir=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
source "${csmUtilDir}"/../../defaults.sh

CERTSDIR='/opt/adva/certs'
CSM_APP_CONTAINER_NAME="csm-app"
CSM_APP_LOG_PATH="/app/var/log"
CSM_VOLUMES=("csm-app-logs")
DOCKER_STACK_BUILD='docker-stack.yml'
EOD_WEB_VOLUMES=("eod-web-logs" "user-db")
IMAGES_DIR="${scriptDir}/../docker_images"
LOG_DIR="/var/tmp/healthcheck_stacks"
MNC_HOST_VAR="**************"
MOD_WEB_CONTAINER_NAME="eod-web"
MOD_WEB_LOG_PATH="/usr/eod-web/server/logs"
NETWORK_NAME="csm-network"
REPO='adtran/mod/csm'
SECRET_NAME="mnc-postgresql-db-pw"
SECRET_USERNAME="mnc-postgresql-db-user"
SERVICE_SYNCHRONIZER_CONTAINER_NAME="service-synchronizer"
SERVICE_SYNCHRONIZER_LOG_PATH="/var/lib/docker/volumes/csm_service-synchronizer-logs/_data/"
SERVICE_SYNCHRONIZER_VOLUMES=("service-synchronizer-logs" "service-synchronizer-reports")
SOURCE_LOG_WEB_UI="/var/lib/docker/volumes/csm_eod-web-logs/_data/"
SOURCE_LOG="/var/lib/docker/volumes/csm_csm-app-logs/_data/"
STACK_FILE="docker-stack.yml"
STACK_NAME="csm"
TARGET_LOG_WEB_UI="/opt/adva/fsp_nm/var/log/csm/eod-web"
TARGET_LOG="/opt/adva/fsp_nm/var/log/csm/csm-app"
SERVICE_LIST_FILE="/opt/adva/stacks/service_list"

