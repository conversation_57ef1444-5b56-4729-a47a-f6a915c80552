# MOD CSM Package in Docker Swarm

This package contains Bash scripts and a Docker stack configuration to manage the deployment and removal of the MOD CSM package in a Docker Swarm environment. The provided scripts help you automate the setup and teardown of the necessary Docker services, networks, volumes, and secrets.

## Package Structure

- `install-csm.sh`: <PERSON><PERSON><PERSON> to install the MOD CSM package.
- `uninstall-csm.sh`: <PERSON><PERSON><PERSON> to uninstall the MOD CSM package.
- `csm-ctl.sh`: <PERSON><PERSON>t to manage the MOD CSM package (status, start, stop, restart).
- `docker-stack.yml`: Docker stack configuration file for the MOD CSM services.
- `docker_images`: Directory containing Docker images in tar files needed to run the CSM app.
- `utils/`: Directory containing helper scripts and default configurations.

## Prerequisites

Ensure you have Docker and Docker Swarm initialized on your system. If not, you can initialize Docker Swarm with the following command:

```bash
docker swarm init
```

Additionally, ensure the following properties are added to `fnm.properties` on the MNC side:

```properties
com.adva.nlms.eod.evolution.enabled=true
com.adva.nlms.eod.evolution.kafka_address={MNC_HOST_IP}:9094

# To run CSM using a port instead of Traefik
com.adva.nlms.mediation.rest.cluster.access.host=localhost
com.adva.nlms.mediation.rest.cluster.type=None
com.adva.nlms.mediation.eodevo.csm.port=8989
```

## Usage

### Installation

To install the MOD CSM package, use the `install-csm.sh` script. This script performs the following actions:

1. Imports Docker images.
2. Creates necessary Docker volumes.
3. Creates a Docker network.
4. Creates a Docker secret for the database password.
5. Deploys the Docker stack.

Run the installation script with the following command:

```bash
./install-csm.sh install
```

You can check the status of the deployed stack using:

```bash
./csm-ctl.sh status
```

### Uninstallation

To uninstall the MOD CSM package, use the `uninstall-csm.sh` script. This script performs the following actions:

1. Stops and removes the Docker stack.
2. Removes Docker volumes.
3. Removes the Docker network.
4. Deletes the Docker secret for the database password.
5. Removes related Docker images.

Run the uninstallation script with the following command:

```bash
./uninstall-csm.sh
```

### Application Management

You can manage the MOD CSM package using the `csm-ctl.sh` script with the following commands:

- `status`: Check the status of the Docker CSM stack.
- `start`: Start the CSM stack.
- `stop`: Stop the CSM stack.
- `restart`: Restart the CSM stack.

Example usage:

```bash
./csm-ctl.sh status
./csm-ctl.sh start
./csm-ctl.sh stop
./csm-ctl.sh restart
```

## Configuration Details

### `docker-stack.yml`

The `docker-stack.yml` file defines the services, networks, volumes, and secrets required for the MOD CSM package. Key sections include:

- **Services**:
  - `csm-app`: Main application service.

- **Networks**:
  - `mnc-core_net-kafka`: External network used by the services.

- **Secrets**:
  - `db-password`: External secret for the database password.

- **Volumes**:

### `install-csm.sh`

This script includes functions to handle the creation of Docker secrets, volumes, networks, and the deployment of the Docker stack. It also includes functions to check the status of the deployed stack.

### `uninstall-csm.sh`

This script includes functions to stop and remove the Docker stack, delete Docker secrets, volumes, and networks, and remove related Docker images.

### `csm-ctl.sh`
This script includes functions to manage the Docker CSM stack, allowing you to check its status, start, stop, and restart the stack.

### `utils/`
The `utils/` directory contains helper scripts and default configurations used by the main installation and uninstallation scripts.

## Notes

- Ensure that the `docker_images` directory contains the necessary Docker images in tar files before running the installation script.
- Update the environment variables and Docker configurations as needed to match your specific setup.

By following the instructions in this README, you can easily manage the deployment and removal of the MOD CSM package in your Docker Swarm environment.
