#!/bin/bash
#
# Copyright 2023 Adtran Networks SE. All rights reserved.
#
# Owner: gerassimosm
#

##   Basic script to build docker images for FSP NM

typeset -r dirOfThisScript=$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )
source ${dirOfThisScript}/resources/docker_helper.sh

if [ $# -lt 2 ]
then
  echo "Usage: $0 "
  echo "<base file name of fsp_nm e.g. Mosaic_Network_Controller_for_Linux_v10.3.1-B3901.tar >  "
  echo "<tag of docker image>" 
  echo "[true/false to enable/disable publishing the image. default = true => will publish the image]"
  echo "[username of the artifactory/repository server user]"
  echo "[password of the artifactory/repository server user]"
  exit 1
fi

NMS_BUILD=$1
NMS_TAG=$2
PUBLISH_ENABLE=$3
NMS_BUILD_USER=$4
NMS_BUILD_PASSWORD=$5


IMAGE_NAME_AND_TAG="fsp_nm_centos7-vnc:dev-${NMS_TAG}"
ARTIFACTORY_SERVER="gdn-artifactory.rd.advaoptical.com:9443"

cd ${dirOfThisScript}

if [ -f ${NMS_BUILD} ]; then 
	printInfoMessage "start creating docker images for ${NMS_BUILD}"
else	
  printError "ERROR: File ${NMS_BUILD} not found in ${dirOfThisScript}"
  exit $FAILURE_CODE
fi

#display size of top level directories under docker
displaySizeOfDirsUnderDocker

# Delete all FSP_NM images
deleteAllFSPNMimages

#remove dangling  images and volumes before building the new images
removeDanglingImages
removeDanglingVolumes

action_msg="building centos7-xfce-vnc_fsp_nm:${NMS_TAG}"
printInfoMessage "Start ${action_msg}"
docker build --build-arg NMS_BUILD=${NMS_BUILD}  -t ${IMAGE_NAME_AND_TAG} -f Dockerfile-fsp_nm-centos7-vnc .
exit_status=$?
if [ ${exit_status} -eq ${SUCCESS_CODE} ]; then 
  printInfoMessage "Finished ${action_msg}"
else
  printError "ERROR occurred while ${action_msg}"
  exit $FAILURE_CODE
fi

#remove dangling  images and volumes after building the new images
removeDanglingImages
removeDanglingVolumes
if [ ! -z ${PUBLISH_ENABLE} ] && [ ${PUBLISH_ENABLE} == "false" ]; then
  printInfoMessage "The image will not be published"
  printInfoMessage "Finished creating docker images for ${NMS_BUILD}"
  exit $SUCCESS_CODE
fi

action_msg="docker login"
printInfoMessage "Start ${action_msg}"
echo ${NMS_BUILD_PASSWORD} | docker login --username ${NMS_BUILD_USER} --password-stdin ${ARTIFACTORY_SERVER}
exit_status=$?
if [ ${exit_status} -eq ${SUCCESS_CODE} ]; then 
  printInfoMessage "Finished ${action_msg}"
else
  printError "ERROR occurred while ${action_msg}"
  exit $FAILURE_CODE
fi

action_msg="docker tagging"
printInfoMessage "Start ${action_msg}"
docker tag ${IMAGE_NAME_AND_TAG} ${ARTIFACTORY_SERVER}/${IMAGE_NAME_AND_TAG}
exit_status=$?
if [ ${exit_status} -eq ${SUCCESS_CODE} ]; then 
  printInfoMessage "Finished ${action_msg}"
else
  printError "ERROR occurred while ${action_msg}"
  exit $FAILURE_CODE
fi

action_msg="docker pushing image"
printInfoMessage "Start ${action_msg}"
docker push ${ARTIFACTORY_SERVER}/${IMAGE_NAME_AND_TAG}
if [ ${exit_status} -eq ${SUCCESS_CODE} ]; then 
  printInfoMessage "Finished ${action_msg}"
else
  printError "ERROR occurred while ${action_msg}"
  exit $FAILURE_CODE
fi