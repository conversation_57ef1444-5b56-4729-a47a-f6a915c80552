#
# Copyright ${YEAR} ADVA Optical Networking SE. All rights reserved.
#
# Owner: gerassimosm
#
# $Id: $
#

## Docker file to build image based on on centos-xfce-vnc for FSP NM (server with VNC Desktop)

# Use an CentOS 7 with Desktop VNC environment as parent (basic) image
FROM consol/centos-xfce-vnc
MAINTAINER Gerassimos Mitropoulos (<EMAIL>)

#Fix/work around for bug : "docker stop/start broken #40"
# https://github.com/ConSol/docker-headless-vnc-container/issues/40
RUN cp /dockerstartup/vnc_startup.sh /dockerstartup/vnc_startup.sh.bck.original
RUN sed -i 's/mkdir "\$HOME\/\.vnc/mkdir -p "\$HOME\/.vnc/' /dockerstartup/vnc_startup.sh

# To define and initialize the NMS_BUILD variable passed at build-time to the builder with the docker build command using the --build-arg <varname>=<value> flag.
# The NMS_BUILD must be initialized to the name of the FSP_NM tar file to include in the image
# e.g NMS_BUILD= FSP_Network_Manager_for_Linux_v10.3.1-B3901.tar
# At build time the FSP_NM tar file must be available in the same directory as this Docker file
ARG NMS_BUILD

# Arbitrary labels applied to the image project
LABEL fsp_nm.build="${NMS_BUILD}"
LABEL fsp_nm.server_type="server with VNC GUI"
LABEL adva.project="FSP_NM"

# Define and initialize the environment variable NMS_HOME
ENV NMS_HOME=/opt/adva/

# become root user
# To execute the following steps as root user
# It necessary because the base image is running as non-root user
USER 0

# Set the working directory to /opt/adva
# This will create the directory if does not exist
WORKDIR ${NMS_HOME}

# This will actual extract the fsp_nm.tar from the docker host under the /opt/adva directory of the docker image
# Note the the tar file it self if not copied 
ADD ${NMS_BUILD} ${NMS_HOME}

# copy helper scripts
# copy all files under resources to the /opt/adva directory 
COPY resources/*  ${NMS_HOME}

# Install fsp server (not interactive mode)
# set to "root" password of postgres root user.
RUN echo -e "y\nroot\nroot\nn" | sh ${NMS_HOME}/install > fsp_nm_install.log 2> fsp_nm_install_error.log

# Create symbolic link of fnm client startup script on the the VNC Desktop
RUN  ln -s /opt/adva/fsp_nm/fnm /headless/Desktop

# port to expose for receiving SNMP  traps
EXPOSE  161-162/udp
EXPOSE  161-162

# port to expose for web server Jetty for client-server communication and for MTOSI web services
EXPOSE  80 8080 8443 9000

# port to expose for  JMS
EXPOSE  33028

# To access/persist the container's files under ${NMS_HOME} form the host machine
# e.g. files will be available in the host system under the following direcory:
# "/var/lib/docker/volumes/<id>/_data/"
# VOLUME ["${NMS_HOME}"]

# Set the default command
# The default is to start the postgres and fsp_nm server
# Note fsp_nm server and postgres will not start as service since systemd is not available in the base image
CMD [ "sh", "-c", "${NMS_HOME}/docker_start_fsp_nm.sh"  ]

