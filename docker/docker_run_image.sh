#!/bin/bash
#
# Copyright 2023 Adtran Networks SE. All rights reserved.
#
# Owner: gerassimosm
#

## Basic script to run docker images for FSP NM

typeset -r dirOfThisScript=$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )
source ${dirOfThisScript}/resources/docker_helper.sh

if [ $# -lt 2 ]
then
  echo "Usage: $0 <docker image name to run>  <docker name of the container> [trapsink_ipv4]"
  exit 1
fi

DOCKER_IMAGE=$1
DOCKER_CONTAINER_NAME=$2
trapsink_ipv4=$3

docker run -it -p 161-162:161-162/udp -p 161-162:161-162 \
-p 8080:8080 -p 8443:8443 -p 9000:9000 -p 33028:33028  \
-p 5901:5901 -p 6901:6901 \
-e VNC_PW=ChgMeNOW \
-e TRAPSINK_IPV4_ENV=${trapsink_ipv4} \
--name ${DOCKER_CONTAINER_NAME} \
${DOCKER_IMAGE}