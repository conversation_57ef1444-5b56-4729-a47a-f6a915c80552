#!/bin/bash
#
# Copyright 2023 Adtran Networks SE. All rights reserved.
#
# Owner: gerassimosm
#

## Basic script to start postgres and FSP NM server 
## we need to start these processes because they are not running as services (systemd is not available)
 
typeset -r dirOfThisScript=$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )
source ${dirOfThisScript}/docker_helper.sh

printInfoMessage "starting postgresql ..."
cd /opt/adva/fsp_nm/postgres/
su postgres -c "bin/pg_ctl -w -D data -l logfile start"
printInfoMessage "postgresql started"

# setup trapsink
. ${dirOfThisScript}/docker_setup_trapsink.sh

printInfoMessage "executing fnm.server start..."
sh /opt/adva/fsp_nm/bin/fnm.server start &
tail -n 0 -F /opt/adva/fsp_nm/var/log/mediation.log
