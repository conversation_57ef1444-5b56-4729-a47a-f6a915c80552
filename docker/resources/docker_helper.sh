#!/bin/bash
#
# Copyright 2023 Adtran Networks SE. All rights reserved.
#
# Owner: gerassimosm
#

##   common/helper script used other docker shell scripts

typeset -i SUCCESS_CODE=0
typeset -i FAILURE_CODE=1

printError()
{
  # print the error message in red color  with time-stamp 
  >&2 echo -e "\e[01;31m`date +%Y-%m-%d_%H:%M:%S` $*\e[0m"
}

printInfoMessage()
{
  echo "`date +%Y-%m-%d_%H:%M:%S`: $@"
}

valid_ip()
{
  local  ip=$1
  local  stat=$FAILURE_CODE

  if [[ $ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
    OIFS=$IFS
    IFS='.'
    ip=($ip)
    IFS=$OIFS
    [[ ${ip[0]} -le 255 && ${ip[1]} -le 255 \
    && ${ip[2]} -le 255 && ${ip[3]} -le 255 ]]
    stat=$?
  fi
  return $stat
}

removeDanglingImages()
{
  action_msg="removing dangling images"
  printInfoMessage "Start ${action_msg}"
  docker images -f "dangling=true" # display dangling images that will be removed
  nrOfDanglingImages=$(docker images -f "dangling=true" -q | wc -l)
  if [ ${nrOfDanglingImages} -gt 0 ]; then
    docker rmi --force $(docker images -f "dangling=true" -q)
  else
    printInfoMessage "No Dangling Images to remove"
  fi
  printInfoMessage "Finished ${action_msg}"
}

removeDanglingVolumes()
{
  action_msg="removing dangling Volumes"
  printInfoMessage "Start ${action_msg}"
  docker volume ls -f "dangling=true" # display dangling volumes that will be removed
  nrOfDanglingVolumes=$(docker volume ls -f "dangling=true" -q | wc -l)
  if [ ${nrOfDanglingVolumes} -gt 0 ]; then
    docker volume rm $(docker volume ls -f "dangling=true" -q)
  else
    printInfoMessage "No Dangling Volumes to remove"
  fi

  printInfoMessage "Finished ${action_msg}"
}

displaySizeOfDirsUnderDocker()
{
  #display size of top level directories under docker
  echo "";
  du -h --max-depth=1 /var/lib/docker/
  echo "";
}

deleteAllFSPNMimages()
{
  action_msg="deleting FSP NM Docker images"
  printInfoMessage "Start ${action_msg}"
  nrOfFSPNMImages=$(docker images --filter label=fsp_nm.build -q | wc -l)
  if [ ${nrOfFSPNMImages} -gt 0 ]; then
    docker images --filter label=fsp_nm.build # Display fsp_nm images to delete
    fspNMImagesToDelete=$(docker images --filter label=fsp_nm.build -q )
    docker image rm --force ${fspNMImagesToDelete}
  else
    printInfoMessage "No FSP NM Images to delete"
  fi
  printInfoMessage "Finished ${action_msg}"
}