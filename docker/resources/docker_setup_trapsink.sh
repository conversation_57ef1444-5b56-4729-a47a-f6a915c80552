#!/bin/bash
#
# Copyright 2023 Adtran Networks SE. All rights reserved.
#
# Owner: gerassimosm
#

## Script to set the trapsink IP address 



typeset -r dirOfThisScript=$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )
source ${dirOfThisScript}/docker_helper.sh
source ${dirOfThisScript}/docker.properties

pathOf_fnm_properties="/opt/adva/fsp_nm/fnm.properties"

# Set the value of trapsink_ipv4 variable to the value of docker_host_ip defined in docker.properties
# If there is docker_host_ip defined then try to initalize from the environment variable TRAPSINK_IPV4_ENV
# NOTE
# The docker_host_ip defined in docker.properties will override the TRAPSINK_IPV4_ENV
if [ ! -z ${docker_host_ip} ]; then
  trapsink_ipv4=${docker_host_ip}
else
  if [ ! -z ${TRAPSINK_IPV4_ENV} ]; then
    trapsink_ipv4=${TRAPSINK_IPV4_ENV}
  fi
fi

# validate trapsink ip v4
if [ ! -z ${trapsink_ipv4} ]; then
  action_msg="updating fnm.properties to set trapsink and trapsink.IpValidationEnabled properties"
  printInfoMessage "Start ${action_msg}"
  valid_ip ${trapsink_ipv4}
  exit_status=$?
  if [ ${exit_status} -ne ${SUCCESS_CODE} ]; then
    printError "The ${trapsink_ipv4} is NOT valid IP address"
    exit $FAILURE_CODE
  fi

  sed -i "s/[#]*com\.adva\.fnm\.option\.trapsink[ ]*=.*/com.adva.fnm.option.trapsink=${trapsink_ipv4}/" ${pathOf_fnm_properties}
  
  # Only the fist time we start the container we will set 
  # com.adva.fnm.option.trapsink.IpValidationEnabled property = true
  countIpValidationProperty=$(grep -c 'com.adva.fnm.option.trapsink.IpValidationEnabled' ${pathOf_fnm_properties})
  if [ ${countIpValidationProperty} -eq 0 ]; then 
    # hear in case the com.adva.fnm.option.trapsink.IpValidationEnabled property
    # is not present 
    # add new line aftert rapsink ip V4 with the following key=value
    # com.adva.fnm.option.trapsink.IpValidationEnabled=true
    sed -i "/[#]*com\.adva\.fnm\.option\.trapsink[ ]*=.*/a com.adva.fnm.option.trapsink.IpValidationEnabled=false" ${pathOf_fnm_properties}
  else
    # Always set IpValidationEnabled=false
    sed -i "s/[#]*com\.adva\.fnm\.option\.trapsink\.IpValidationEnabled[ ]*=.*/com.adva.fnm.option.trapsink.IpValidationEnabled=false/" ${pathOf_fnm_properties}
  fi 
  printInfoMessage "Finished ${action_msg}"

else
  printInfoMessage "trapsink_ipv4 is null. No setup action will be performed"
fi

