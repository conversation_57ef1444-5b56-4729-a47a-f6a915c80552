/*
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 */

pluginManagement {
    apply from: 'utilities.gradle'
    apply from: 'parameters.gradle'

    repositories {
        if (PluginDeveloper) {
            mavenLocal()
        }

        maven {
            credentials {
                username artifactoryReadUser
                password artifactoryReadPassword
            }
            url "https://$artifactoryServer/artifactory/NMS"
        }
        maven {
            credentials {
                username artifactoryReadUser
                password artifactoryReadPassword
            }
            url "https://$artifactoryServer/artifactory/gradle-plugins-remote-cache"
            metadataSources {
                mavenPom()
                artifact()
            }
        }

        // Fallback to gradle plugin repository if ADVA Artifacty fails
        gradlePluginPortal()
    }

    plugins {
        // Make locally developed plugins stored on artifactory available to the build
        id 'com.adva.gradle.plugin.rest-api-plugin'                version pluginVersion
        id 'com.adva.gradle.plugin.aspectj-weaver'                 version pluginVersion
        id 'com.adva.gradle.plugin.build-chain'                    version pluginVersion
        id 'com.adva.gradle.plugin.eclipse-link-weaver'            version pluginVersion
        id 'com.adva.gradle.plugin.document-generation'            version pluginVersion
        id 'com.adva.gradle.plugin.project-module-build-plugin'    version pluginVersion
        id 'com.adva.gradle.plugin.project-module-settings-plugin' version pluginVersion
        id 'com.adva.gradle.plugin.jar-exec'                       version pluginVersion
        id 'com.adva.gradle.plugin.image-publisher'                version pluginVersion
        id 'com.adva.gradle.plugin.source-analyzer'                version pluginVersion

        id 'org.springframework.boot' version '3.4.7'
        id 'com.autonomousapps.dependency-analysis' version '1.32.0'
    }
}

buildscript {
    // Build script configuration for custom plugins
    apply from: 'utilities.gradle'
    apply from: 'parameters.gradle'

    // Pull custom ADVA plugins from Artifactory server
    repositories {
        if (PluginDeveloper) {
            mavenLocal()
        }

        maven {
            credentials {
                username artifactoryReadUser
                password artifactoryReadPassword
            }
            url "https://$artifactoryServer/artifactory/NMS"
        }
        maven {
            credentials {
                username artifactoryReadUser
                password artifactoryReadPassword
            }
            url "https://$artifactoryServer/artifactory/libs-release"
        }
    }
}

plugins {
    id "com.gradle.develocity" version "3.17.5"
    id 'com.adva.gradle.plugin.project-module-settings-plugin'
}

setupBuildCache()

// configure the project-module
projectModule {
    excludeDirs = [
            // Standard exclusions
            'src', 'build', 'buildSrc'
    ] as Set
    layerKeywords =     [ 'necomm', 'element', 'network', 'app', 'nbi', 'support' ] as Set
    qualifierKeywords = [ 'api', 'impl', 'db', 'config' ] as Set
    scanPaths =         [ 'layers', 'modules', 'libraries' ] as Set
}

// if there is a 'smallIdea' property, use it for the project name.
// Otherwise if there is a 'ProjectName' property, use that one.
// Otherwise stick to 'FSP-NM'
def projName = getStringPropWithDefault('ProjectName', 'enc')
def smidea = getStringPropWithDefault("smallIdea", "");
rootProject.name = (smidea == "")? projName : projName+"-"+smidea

// Make 'dist' a part of the ENC project
include 'dist'
include 'dist:lnx'
include 'dist:win'
enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")
dependencyResolutionManagement {
    versionCatalogs {
        libs {
            from(files("libs.versions.toml"))
        }
        springboot {
            from(files("springboot.versions.toml"))
        }
    }
}
