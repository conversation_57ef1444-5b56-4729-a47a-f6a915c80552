-Xmx4000M
-XX:HeapDumpPath=NMHeapDumps
-XX:+HeapDumpOnOutOfMemoryError
-Xrs
-Djakarta.xml.bind.JAXBContextFactory=org.glassfish.jaxb.runtime.v2.ContextFactory
-Dcom.sun.xml.bind.v2.runtime.JAXBContextImpl.fastBoot=true
-Djtrace.logdir=var/log
-Djava.awt.headless=true
-Dfile.encoding=UTF-8
-Djava.util.logging.config.file=./logging.properties
-Dorg.apache.activemq.SERIALIZABLE_PACKAGES="java.lang,java.util,org.apache.activemq,org.fusesource.hawtbuf,com.thoughtworks.xstream.mapper,ni,java.time,org.apache.commons.lang3.tuple,com.google.common.collect,java.beans,com.adva"
-Djava.library.path=lib
-Djdk.tls.ephemeralDHKeySize=2048
--add-opens=java.base/java.net=ALL-UNNAMED
--add-opens=java.base/sun.net.www.protocol.https=ALL-UNNAMED
