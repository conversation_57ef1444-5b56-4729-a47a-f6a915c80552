<?xml version="1.0" encoding="UTF-8"?>
<!--
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: ssingam
 *
 *  $Id: performanceTemplate.xml 83938 2014-10-17 13:12:34Z askarzycki $
-->
<performances>
<performance type="OTU Performance">
    <timeGroup type="15 min">
        <PerformanData type="Errored Seconds (ES)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Severely Errored Seconds (SES)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Background Block Errors (BBE)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Unavailable Seconds (UAS)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Bit Interleaved Parity (BIP)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Errored Seconds Far End (ES)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Severely Errored Seconds Far End (SES)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Background Block Errors Far End (BBE)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Unavailable Seconds Far End (UAS)">
            <selected>true</selected>
        </PerformanData>

    </timeGroup>
    <timeGroup type="24 h">
        <PerformanData type="Errored Seconds (ES)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Severely Errored Seconds (SES)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Background Block Errors (BBE)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Unavailable Seconds (UAS)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Errored Seconds Far End (ES)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Severely Errored Seconds Far End (SES)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Background Block Errors Far End (BBE)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Unavailable Seconds Far End (UAS)">
            <selected>true</selected>
        </PerformanData>
    </timeGroup>
</performance>
<performance type="ODU Performance">
    <timeGroup type="15 min">
        <PerformanData type="Errored Seconds (ES)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Severely Errored Seconds (SES)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Background Block Errors (BBE)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Unavailable Seconds (UAS)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Bit Interleaved Parity (BIP)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Errored Seconds Far End (ES)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Severely Errored Seconds Far End (SES)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Background Block Errors Far End (BBE)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Unavailable Seconds Far End (UAS)">
            <selected>true</selected>
        </PerformanData>
    </timeGroup>
    <timeGroup type="24 h">
        <PerformanData type="Errored Seconds (ES)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Severely Errored Seconds (SES)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Background Block Errors (BBE)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Unavailable Seconds (UAS)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Errored Seconds Far End (ES)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Severely Errored Seconds Far End (SES)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Background Block Errors Far End (BBE)">
            <selected>true</selected>
        </PerformanData>
        <PerformanData type="Unavailable Seconds Far End (UAS)">
            <selected>true</selected>
        </PerformanData>
    </timeGroup>
</performance>
</performances>