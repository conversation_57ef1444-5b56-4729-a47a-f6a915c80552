<?xml version="1.0"?>
<!--
  -  Copyright 2023 Adtran Networks SE. All rights reserved.
  -
  -  Owner: ssagiv
  -->

<!-- This file provides pre-defined hosts and applications for Syncjack Tool -->

<root>

  <Applications>
    <APP_1 APP_ID="1" NAME="ping" PORT="8888" HTTP_METHOD="GET" URI="/rest/SyncMngrLwp/Ping" REST="false" PARAMS=""/>
    <APP_2 APP_ID="2" NAME="pm_file_download" PORT="8007" HTTP_METHOD="GET" URI="/download/SyncMngrLwp/ExportTIE" REST="true" PARAMS="Ne=XXX&amp;Probe=XXX&amp;Start=XXX&amp;From=XXX&amp;To=XXX&amp;Format=XXX&amp;GZip=XXX"/>
    <APP_3 APP_ID="3" NAME="file server get config" PORT="8888" HTTP_METHOD="GET" URI="/rest/SyncMngrLwp/Config" REST="true" PARAMS="ConfName=FileServer&amp;FileServerHost=XXX"/>
    <APP_4 APP_ID="4" NAME="file server delete config" PORT="8888" HTTP_METHOD="DELETE" URI="/rest/SyncMngrLwp/Config" REST="true" PARAMS="ConfName=FileServer&amp;FileServerHost=XXX"/>

    <APP_5 APP_ID="5" NAME="file server set config" PORT="8888" HTTP_METHOD="PUT" URI="/rest/SyncMngrLwp/Config" REST="true" PARAMS="ConfName=FileServer&amp;FileServerHost=XXX&amp;TransferMethod=XXX&amp;User=XXX&amp;Password=XXX&amp;Port=XXX&amp;Passive=XXX"/>
    <APP_6 APP_ID="6" NAME="file collection get config" PORT="8888" HTTP_METHOD="GET" URI="/rest/SyncMngrLwp/Config" REST="true" PARAMS="ConfName=FileCollection&amp;FileServerHost=XXX"/>
    <APP_7 APP_ID="7" NAME="file collection delete config" PORT="8888" HTTP_METHOD="DELETE" URI="/rest/SyncMngrLwp/Config" REST="true" PARAMS="ConfName=FileCollection&amp;FileServerHost=XXX"/>
    <APP_8 APP_ID="8" NAME="file collection set config" PORT="8888" HTTP_METHOD="PUT" URI="/rest/SyncMngrLwp/Config" REST="true" PARAMS="ConfName=FileCollection&amp;FileServerHost=XXX&amp;DAHosts=XXX&amp;WaitTimeInMinutes=XXX"/>
  </Applications>

  <Hosts>
    <HOST_1 HOST_ID="1" HOSTNAME="localhost"/>
    <HOST_2 HOST_ID="2" HOSTNAME="**************"/>
  </Hosts>

</root>