**/build-gradle/
**/.gradle/
**/.idea/
**/.idea_old/
**/build/
**/*.jar
**/*.war
**/*.exe
**/*.log
**/*.dll
**/*.swp
**/*.hprof
**/*.iml
**/*.ipr
**/*.iws
**/cleandump*
**/gradle-wrapper.properties


/var/
/reports/com/adva/nlms/frontend/report/generator/scheme/locale/
/ws/webapps/reportdb/*
/failureprediction/cfp/
/monitoringConfig/
/svp/
/db/yp/
/cmsw/
/docs/
/platforms/
/postgres/bin
/postgres/lib
/doc/api/

/custom.gradle
/dir.properties
/gradle.properties
/fnm.custom.properties
/initdb.sql

/lib/libFlxCore64.so.2022.02
/lib/report/ivy*.properties

modules/provision/Src/WebApps/Pv14/package.json
modules/provision/Src/WebApps/Pv14/webpack.config.js
modules/provision/Src/WebApps/Pv14/webpack.generated.js

/certs/*auth.ks
/ca
/activemq/data
/priv
/ssocerts/*.jks

/scripts/createDDL_ddlGeneration.jdbc

modules/gnss/docker/deploy/synca_k8s/**/charts
modules/gnss/docker/deploy/synca_k8s/**/Chart.lock
/modules/gnss/common-grpc/src/generated/
/modules/gnss/machine-learning/src/main/resources/ml/models/

layers/apps/eod/gradle/
layers/apps/eod/gradlew
layers/apps/eod/gradlew.bat

/layers/apps/eod/docker/.env
docker/deploy/enc-ds-w/dist/enc-ds/util/completion.sh
docker/deploy/enc-ds-w/dist/enc-ds/rproxy/ssl/
docker/deploy/enc-ds-w/dist/enc-ds/enc-ds-ctl
docker/deploy/enc-ds-w/dist/enc-ds/enc-ds-ctl.yml
docker/deploy/enc-ds-w/src/enc-ds-ctl/config/resources/enc-ds-ctl.yml
docker/deploy/enc-ds-w/src/enc-ds-ctl/cmd/version/resources/version.yml
docker/deploy/enc-ds-w/dist/mnc-ds-k8s/synca/**/**/Chart.lock
docker/deploy/enc-ds-w/dist/mnc-ds-k8s/synca/**/**/charts/
docker/deploy/enc-ds-w/dist/mnc-ds-k8s/util/enc_token.txt
docker/deploy/enc-ds-w/dist/mnc-ds-k8s/util/helm-charts/ctl-lint/values.schema.json
docker/deploy/enc-ds-w/enc-ds-w.code-workspace
docker/deploy/enc-ds-w/dist/mnc-ds-k8s/mnc-ds-ctl
docker/deploy/enc-ds-w/dist/mnc-ds-k8s/util/completion.sh
docker/deploy/enc-ds-w/dist/mnc-ds-k8s/mnc-ds-ctl.yml
docker/deploy/enc-ds-w/dist/mnc-ds-k8s/doc/mnc-ds-ctl.reference.yml
docker/deploy/enc-ds-w/dist/mnc-ds-k8s/reports/*
docker/deploy/enc-ds-w/src/enc-ds-ctl-k8s/.vscode/
docker/deploy/enc-ds-w/src/enc-ds-ctl-k8s/config/resources/mnc-ds-ctl.yml
docker/deploy/enc-ds-w/src/enc-ds-ctl-k8s/cmd/version/resources/version.yml
docker/deploy/enc-ds-w/registry.password

.gitreview
