###################################################################
# Spring Boot Dependency Artifact Definitions
###################################################################
#
# Spring Boot uses <PERSON><PERSON> (Bill of Materials)
# When defining dependencies omit the 'version' information so that this information is taken from the BoM
# instead of direct defiition.  This allows the versioning to follow the Spring Boot BoM.
#
# Dependencies in this file can be used as indication that they are part of the Spring Boot Framework.
# If a dependency is not included in this file you may need to validate if it is part of the BoM or not.
# One way to validate if the dependency is included in the Spring Boot BoM is to add the dependency here,
# update the corresponding build.gradle file to reference this definition, then run a build and see if
# the dependency can be resolved.
#

[versions]
ver_spring_boot = "3.4.7"
ver_start_webmvc_ui = "2.3.0"
ver_flyway_db = "11.9.1"

[libraries]

# Spring Boot BOM (Bill of Materials) dependency that defines all the artifact versions to use for a specific spring-boot release
bom = { group = "org.springframework.boot", name = "spring-boot-dependencies", version.ref = "ver_spring_boot" }

# Depencency definitions requiring Spring Boot BOM version definitions
annotation_api = { group = 'jakarta.annotation', name = 'jakarta.annotation-api' }
jakarta_validation_api = { group = "jakarta.validation", name = "jakarta.validation-api" }
flyway = { group = "org.flywaydb", name = "flyway-core" }
flyway_db_pg = { group = "org.flywaydb", name = "flyway-database-postgresql", version.ref = 'ver_flyway_db' }
httpclient5 = { group = 'org.apache.httpcomponents.client5', name = 'httpclient5' }
h2 = { group = 'com.h2database', name = 'h2' }
jackson_annotations = { group = "com.fasterxml.jackson.core", name = "jackson-annotations" }
jackson_databind = { group = "com.fasterxml.jackson.core", name = "jackson-databind" }
junit_jupiter_api = { group = "org.junit.jupiter", name = "junit-jupiter-api"  }
junit_jupiter_engine = { group = "org.junit.jupiter", name = "junit-jupiter-engine" }
log4j_api = { group = "org.apache.logging.log4j", name = "log4j-api" }
logback_classic = { group = "ch.qos.logback", name = "logback-classic" }
logback_core = { group = "ch.qos.logback", name = "logback-core" }
micrometer_registry_prometheus = { group = 'io.micrometer', name = 'micrometer-registry-prometheus' }
postgresql = { group = "org.postgresql", name = "postgresql" }
servlet_api = { group = "jakarta.servlet", name = "jakarta.servlet-api" }
spring_aspects = { group = 'org.springframework', name = 'spring-aspects' }
spring_beans = { group = "org.springframework", name = "spring-beans" }
spring_core = { group = "org.springframework", name = "spring-core" }
spring_context = { group = "org.springframework", name = "spring-context" }
spring_kafka = { group = "org.springframework.kafka", name = "spring-kafka" }
spring_kafka_test = { group = "org.springframework.kafka", name = "spring-kafka-test" }
spring_retry = { group = 'org.springframework.retry', name = 'spring-retry' }
spring_tx = { group = "org.springframework", name = "spring-tx" }
spring_web = { group = "org.springframework", name = "spring-web" }
spring_webmvc = { group = "org.springframework", name = "spring-webmvc" }
spring_security_acl = { group = "org.springframework.security", name = "spring-security-acl" }
spring_security_config = { group = "org.springframework.security", name = "spring-security-config" }
spring_security_core = { group = "org.springframework.security", name = "spring-security-core" }
spring_security_web = { group = "org.springframework.security", name = "spring-security-web" }
spring_context_support = { group = "org.springframework", name = "spring-context-support" }
tomcat_embeded_core = { group = 'org.apache.tomcat.embed', name='tomcat-embed-core' }
thymeleaf = { group = 'org.thymeleaf', name = 'thymeleaf' }

# Psuedo dependencies defined by spring boot generally useful based on their transitive dependcies
starter = { group = 'org.springframework.boot', name = 'spring-boot-starter' }
starter_configuration_processor = { group = 'org.springframework.boot', name = 'spring-boot-configuration-processor' }
starter_data_jpa = { group = 'org.springframework.boot', name = 'spring-boot-starter-data-jpa' }
starter_data_rest = { group = 'org.springframework.boot', name = 'spring-boot-starter-data-rest' }
starter_logging  = { group = 'org.springframework.boot', name = 'spring-boot-starter-logging' }
starter_jetty = { group = 'org.springframework.boot', name = 'spring-boot-starter-jetty' }
starter_security  = { group = 'org.springframework.boot', name = 'spring-boot-starter-security' }
starter_web = { group = 'org.springframework.boot', name = 'spring-boot-starter-web' }
starter_websocket = { group = 'org.springframework.boot', name = 'spring-boot-starter-websocket' }
starter_test = { group = 'org.springframework.boot', name = 'spring-boot-starter-test' }
starter_validation = { group = 'org.springframework.boot', name = 'spring-boot-starter-validation' }
starter_actuator = { group = 'org.springframework.boot', name = 'spring-boot-starter-actuator' }
integration_zookeeper = { group = 'org.springframework.integration', name = 'spring-integration-zookeeper' }
integration_jdbc = { group = 'org.springframework.integration', name = 'spring-integration-jdbc' }
# Not part of spring BoM so specify version directly, although this library is strongly tied to spring boot.
starter_webmvc_ui = { group = 'org.springdoc', name = 'springdoc-openapi-starter-webmvc-ui', version.ref = 'ver_start_webmvc_ui' }


[bundles]
