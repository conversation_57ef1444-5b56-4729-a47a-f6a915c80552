<template>
<header>
<neType>FSP 150CC-XG116Pro, FSP 150CC-XG120Pro</neType>
<applyMode>Delta</applyMode>
<version>1.4</version>
<summary>Service template - EVPL (SingleCOS). Suitable for Linear and ERP protected service on XG116Pro and XG120Pro devices.</summary>
<category>Service Provisioning</category>
<comment>#</comment>
</header>
<cli-command>
# DO NOT EDIT THIS LINE. FILE_TYPE=CONFIGURATION_FILE
</cli-command>

<command>
<block display="Service Circuit Configuration" name="ServiceBlock"/>
<param display="Circuit Name " name="circuitName" default="Customer Service" block="ServiceBlock" scope="global">
<type>String</type>
</param>
<param display="Circuit ID (MP Flow No.- auto)" name="mpFlowID" block="ServiceBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.mpFlow"/>
</default>
</param>
<param display="Service End Point" name="fnm.serviceEnd" default="FP-1-1-1-1-1" block="ServiceBlock" scope="global">
<type>Composite</type>
<value>FP-1-1-1-__%fp1EthPort__-__%fp1id__</value>
</param>
</command>

<!-- Eth Port Configuration section for FP1 -->
<command>
<block display="Ingress Configuration - FlowPoint No.1 [FP1]" name="PortConfBlock1"/>
<param display="Eth Port" name="fp1EthPort" default="ETH PORT-1" block="PortConfBlock1" paramOrder="1" scope="global">
<type>Enum</type>
<token display="ETH PORT-1" literal="1"/>
<token display="ETH PORT-2" literal="2"/>
<token display="ETH PORT-3" literal="3"/>
<token display="ETH PORT-4" literal="4"/>
<token display="ETH PORT-5" literal="5"/>
<token display="ETH PORT-6" literal="6"/>
<token display="ETH PORT-7" literal="7"/>
<token display="ETH PORT-8" literal="8"/>
</param>
<param display="Eth Port Admin State" name="fp1Adminstate" default="IS" block="PortConfBlock1" paramOrder="1" scope="global">
<type>Enum</type>
<token display="Disabled" literal="disabled"/>
<token display="IS" literal="in-service"/>
<token display="Maintenance" literal="maintenance"/>
<token display="Management" literal="management"/>
<token display="Unassigned" literal="unassigned"/>
</param>
<param display="Eth Port Speed" name="fp1Speed" default="Auto 1000 Full" block="PortConfBlock1" paramOrder="1" scope="global">
<type>Enum</type>
<token display="100 Full" literal="100-full"/>
<token display="1000 Full" literal="1000-full"/>
<token display="Auto 1000 Full" literal="auto-1000-full"/>
<token display="Fixed 10G Full" literal="fixed-10g-full"/>
<token display="Auto Speed Detect" literal="auto-speed-detect"/>
</param>
<!-- <param display="Port Alias" name="port1Alias" default="Customer Name (Acc side)" block="PortConfBlock1" scope="global">
<type>String</type>
</param> -->
<param display="FP1 No. (auto)" name="fp1id" block="PortConfBlock1" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.fp">
		<arg>1</arg>
		<arg>%fp1EthPort</arg>
	</function>
</default>
</param>
<param display="FP1 Alias " name="fp1Alias" default="Customer Name (Tag:12)" block="PortConfBlock1" scope="global">
<type>String</type>
</param>

<!-- FP1 BWP Configuration Block -->
<block display="BW Configuration" name="FP1CosBlock" blockParent="PortConfBlock1"/>
<param display="CIR (bps)" name="fp1cir" default="64000" copyFrom="fp1cir"  block="FP1CosBlock" scope="global">
<type>Integer</type>
</param>
<param display="EIR (bps)" name="fp1eir" default="64000" copyFrom="fp1eir" block="FP1CosBlock" scope="global">
<type>Integer</type>
</param>
<param display="Buffer Size (KB)" name="fp1bufsize" default="256" copyFrom="fp1bufsize" block="FP1CosBlock" scope="global">
<type>Integer</type>
</param>

<!-- FP1 Configuration block -->
<block display="VLAN Configuration" name="FP1ConfBlock" blockParent="PortConfBlock1"/>
<param display="C-Tag Control" name="fp1CtagControl" default="Push" block="FP1ConfBlock" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Push" literal="push __%fp1Ctag__"/>
<token display="Push-vid" literal="push-vid __%fp1Ctag__"/>
<token display="Swap-vid" literal="swap-vid __%fp1Ctag__"/>
</param>
<param display="C-Tag - Priority" name="fp1Ctag" default="11-0" block="FP1ConfBlock" optional="true" scope="global">
<type>String</type>
</param>
<param display="S-Tag Control" name="fp1StagControl" default="None" block="FP1ConfBlock" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Push" literal="push __%fp1Stag__"/>
<token display="Push-vid" literal="push-vid __%fp1Stag__"/>
<token display="Swap-vid" literal="swap-vid __%fp1Stag__"/>
</param>
<param display="S-Tag - Priority" name="fp1Stag" default="" copyFrom="fp1Stag" block="FP1ConfBlock" optional="true" scope="global">
<type>String</type>
</param>
<param display="VLAN Member" name="fp1VlanMember" default="12-*" copyFrom="fp1VlanMember" block="FP1ConfBlock" scope="global">
<type>String</type>
</param>

</command>

<!-- Eth Port Configuration section for FP2 -->
<command>


<block display="Egress Configuration - FlowPoint No.2 [FP2]" name="PortConfBlock2"/>
<param display="Eth Port" name="fp2EthPort" default="LAG-1-1" block="PortConfBlock2" paramOrder="1" scope="global">
<type>Enum</type>
<token display="LAG-1-1" literal="lag-1-1"/>
<token display="LAG-1-2" literal="lag-1-2"/>
</param>
<param display="Eth Port Admin State" name="fp2Adminstate" default="IS" block="PortConfBlock2" paramOrder="1" scope="global">
<type>Enum</type>
<token display="Disabled" literal="disabled"/>
<token display="IS" literal="in-service"/>
<token display="Maintenance" literal="maintenance"/>
<token display="Management" literal="management"/>
<token display="Unassigned" literal="unassigned"/>
</param>
<param display="Eth Port Speed" name="fp2Speed" default="Auto 1000 Full" block="PortConfBlock2" paramOrder="1" scope="global">
<type>Enum</type>
<token display="100 Full" literal="100-full"/>
<token display="1000 Full" literal="1000-full"/>
<token display="Auto 1000 Full" literal="auto-1000-full"/>
<token display="Fixed 10G Full" literal="fixed-10g-full"/>
<token display="Auto Speed Detect" literal="auto-speed-detect"/>
</param>
<!-- <param display="Port Alias " name="port2Alias" default="Network Side Name" block="PortConfBlock2" scope="global">
<type>String</type>
</param> -->
<param display="FP2 No (auto)" name="fp2id" block="PortConfBlock2" scope="global">
<type>Integer</type>
</param>
<param display="FP2 Alias " name="fp2Alias" default="Network Access (Tag:1200)" block="PortConfBlock2" scope="global">
<type>String</type>
</param>

<!-- FP2 BWP Configuration Block -->
<block display="BW Configuration" name="FP2CosBlock" blockParent="PortConfBlock2"/>
<param display="CIR (bps)" name="fp2cir" default="64000" copyFrom="fp2cir"  block="FP2CosBlock" scope="global">
<type>Integer</type>
</param>
<param display="EIR (bps)" name="fp2eir" default="64000" copyFrom="fp2eir" block="FP2CosBlock" scope="global">
<type>Integer</type>
</param>
<param display="Buffer Size (KB)" name="fp2bufsize" default="256" copyFrom="fp2bufsize" block="FP2CosBlock" scope="global">
<type>Integer</type>
</param>

<!-- FP2 Configuration Block -->
<block display="VLAN Configuration" name="FP2ConfBlock" blockParent="PortConfBlock2"/>
<param display="C-Tag Control" name="fp2CtagControl" default="Push" block="FP2ConfBlock" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Push" literal="push __%fp2Ctag__"/>
<token display="Push-vid" literal="push-vid __%fp2Ctag__"/>
<token display="Swap-vid" literal="swap-vid __%fp2Ctag__"/>
</param>
<param display="C-Tag - Priority" name="fp2Ctag" default="1200-0" copyFrom="fp2Ctag" block="FP2ConfBlock" optional="true" scope="global">
<type>String</type>
</param>
<param display="S-Tag Control" name="fp2StagControl" default="None" block="FP2ConfBlock" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Push" literal="push __%fp2Stag__"/>
<token display="Push-vid" literal="push-vid __%fp2Stag__"/>
<token display="Swap-vid" literal="swap-vid __%fp2Stag__"/>
</param>
<param display="S-Tag - Priority" name="fp2Stag" default="" copyFrom="fp2Stag" block="FP2ConfBlock" optional="true" scope="global">
<type>String</type>
</param>
<param display="VLAN Member" name="fp2VlanMember" default="12-*" copyFrom="fp2VlanMember" block="FP2ConfBlock" scope="global">
<type>String</type>
</param>
</command>




<fragment block="PortConfBlock1" resolveGlobalParams="true">
#
#CLI:FP-1-1-1-__%fp1EthPort__-__%fp1id__
#
home
network-element ne-1
configure nte ntexg116pro-1-1-1
configure eth-port eth_port-1-1-1-__%fp1EthPort__
add flowpoint __%fp1id__ "__%fp1Alias__" disabled disabled disabled disabled "__%fp1VlanMember__" disabled __%fp1CtagControl__ __%fp1StagControl__ none none
</fragment>

<fragment block="FP1CosBlock" resolveGlobalParams="true">
#
#CLI:SHAPER-1-1-1-__%fp1EthPort__-__%fp1id__-0  Edit
#
home
network-element ne-1
configure nte ntexg116pro-1-1-1 
configure eth-port eth_port-1-1-1-__%fp1EthPort__
configure flowpoint fp-1-1-1-__%fp1EthPort__-__%fp1id__
configure shaper fp_shaper-1-1-1-__%fp1EthPort__-__%fp1id__-0
buffersize %fp1bufsize
cir %fp1cir
eir %fp1eir

#
#CLI:POLICER-1-1-1-__%fp1EthPort__-__%fp1id__-0  Edit
#
home
network-element ne-1
configure nte ntexg116pro-1-1-1 
configure eth-port eth_port-1-1-1-__%fp1EthPort__
configure flowpoint fp-1-1-1-__%fp1EthPort__-__%fp1id__
configure policer fp_policer-1-1-1-__%fp1EthPort__-__%fp1id__-0
cbs 32
ebs 1
cir %fp1cir
eir %fp1eir


#
#CLI:ETH PORT-1-1-1-__%fp1EthPort__  Edit
#
home
network-element ne-1
configure nte ntexg116pro-1-1-1 
configure eth-port eth_port-1-1-1-__%fp1EthPort__
admin-state %fp1Adminstate
</fragment>

<fragment block="PortConfBlock2" resolveGlobalParams="true">
#
#CLI:FP-1-1-1-__%fp2EthPort__-__%fp2id__
#
home
network-element ne-1
configure lag __%fp2EthPort__
add flowpoint __%fp2id__ "__%fp2Alias__" disabled disabled disabled disabled "__%fp2VlanMember__" disabled __%fp2CtagControl__ __%fp2StagControl__ none none
</fragment>

<fragment block="FP2CosBlock" resolveGlobalParams="true">
#
#CLI:SHAPER-1-1-1-__%fp2EthPort__-__%fp2id__-0  Edit
#
home
network-element ne-1
configure lag __%fp2EthPort__ 
configure flowpoint lag_fp-1-1-1
configure shaper lag_fp_shaper-1-1-1-0
buffersize %fp2bufsize
cir %fp2cir
eir %fp2eir

#
#CLI:POLICER-1-1-1-__%fp2EthPort__-__%fp2id__-0  Edit
#
home
network-element ne-1
configure lag __%fp2EthPort__  
configure flowpoint lag_fp-1-1-1
configure policer lag_fp_policer-1-1-1-0
cbs 32
ebs 1
cir %fp2cir
eir %fp2eir
</fragment>


<fragment block="ServiceBlock" resolveGlobalParams="true">
#
#CLI:FLOW-1-1  Create
#
home
network-element ne-1
  add mp-flow %mpFlowID "__%circuitName__" none
  configure mp-flow mp_flow-1-__%mpFlowID__
	add fp fp-1-1-1-__%fp1EthPort__-__%fp1id__ 
	add fp lag_fp-1-1-1
	admin-state in-service
</fragment>


</template>