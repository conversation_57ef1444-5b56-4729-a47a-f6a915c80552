<template>
<header>
<neType>FSP 150EG-X </neType>
<applyMode>Delta</applyMode>
<version>1.4</version>
<summary>Service Multiplexing template (MultiCOS). Suitable for Linear service.</summary>
<category>Service Provisioning</category>
<comment>#</comment>
</header>
<cli-command># DO NOT EDIT THIS LINE. FILE_TYPE=CONFIGURATION_FILE</cli-command>

<command>
<block display="Traffic Port Configuration for FP1" name="block2"/>
<param display="Module" name="module1" default="GE-10S-H" block="block2" scope="global">
<type>Enum</type>
<token display="GE-10S" literal="ge-10s ge_10s"/>
<token display="GE-10S-H" literal="ge-10s-h ge_10s_h"/>
<token display="XG-1X" literal="xg-1x xg_1x"/>
<token display="XG-1X-H" literal="xg-1x-h xg_1x_h"/>
</param>
<param display="Slot No" name="slot1" default="1" block="block2" scope="global">
<type>Integer</type>
</param>
<param display="Port No." name="portid1" default="1" block="block2" scope="global">
<type>Integer</type>
</param>
<param display="Port Alias " name="port1Alias" default="HG1-1104-003" block="block2" scope="global">
<type>String</type>
</param>
<param display="FlowPoint1 No." name="fp1id" block="block2" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.fp">
		<arg>%slot1</arg>
		<arg>%portid1</arg>
	</function>
</default>
</param>
<param display="FlowPoint1 Alias " name="fp1Alias" default="G30-1105-0224" block="block2" scope="global">
<type>String</type>
</param>
<param display="FlowPoint1 Policer Profile" name="fp1Profile" default="Default_Policer_Profile" block="block2" scope="global">
<type>Enum</type>
<token function="fnm.db.policerProfile"/>
</param>
<param display="FlowPoint1 Queue Profile" name="fp1QueProfile" default="Queue_profile_14" block="block2" scope="global">
<type>Enum</type>
<token function="fnm.db.queueProfile"/>
</param>
</command>

<command>
<block display="Traffic Port Configuration for FP2" name="block3"/>
<param display="Module" name="module2" default="GE-10S-H" block="block3" paramOrder="1" scope="global">
<type>Enum</type>
<token display="GE-10S" literal="ge-10s ge_10s"/>
<token display="GE-10S-H" literal="ge-10s-h ge_10s_h"/>
<token display="XG-1X" literal="xg-1x xg_1x"/>
<token display="XG-1X-H" literal="xg-1x-h xg_1x_h"/>
</param>
<param display="Slot No" name="slot2" default="2" block="block3" scope="global">
<type>Integer</type>
</param>
<param display="Port No." name="portid2" default="1" block="block3" scope="global">
<type>Integer</type>
</param>
<param display="Port Alias " name="port2Alias" default="HG1-1104-003" block="block3" scope="global">
<type>String</type>
</param>
<param display="FlowPoint2 No." name="fp2id" block="block3" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.fp">
		<arg>%slot2</arg>
		<arg>%portid2</arg>
	</function>
</default>
</param>
<param display="FlowPoint2 Alias " name="fp2Alias" default="G30-1105-0224" block="block3" scope="global">
<type>String</type>
</param>
<param display="FlowPoint2 Policer Profile" name="fp2Profile" default="Default_Policer_Profile" block="block3" scope="global">
<type>Enum</type>
<token function="fnm.db.policerProfile"/>
</param>
<param display="FlowPoint2 Queue Profile" name="fp2QueProfile" default="Queue_profile_14" block="block3" scope="global">
<type>Enum</type>
<token function="fnm.db.queueProfile"/>
</param>
</command>

<command>
<block display="Service Parameters for FP1" name="block4" />
<param display="CTag Control" name="ctagcontrol" default="Push" block="block4" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Push" literal="push"/>
<token display="Push-vid" literal="push-vid"/>
<token display="Swap-vid" literal="swap-vid"/>
<token display="Pop" literal="pop"/>
</param>
<param display="C-Tag - Priority" name="ctag" default="1-0" block="block4" optional="true" scope="global">
<type>String</type>
</param>
<param display="STag Control" name="stagcontrol" default="Push" block="block4" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Push" literal="push"/>
<token display="Push-vid" literal="push-vid"/>
<token display="Swap-vid" literal="swap-vid"/>
<token display="Pop" literal="pop"/>
</param>
<param display="S-Tag - Priority" name="stag" default="1-0" block="block4" optional="true" scope="global">
<type>String</type>
</param>
<param display="Vlan Memeber" name="vlanmember" default="1-*" block="block4" scope="global">
<type>String</type>
</param>
</command>

<command>
<block display="Service Configuration" name="block5"/>
<param display="ELine No. (FLow-1-x)" name="elineID" block="block5" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.elineFlow"/>
</default>
</param>
<param display="Circuit Name " name="circuitName" default="G30-1105-0224" block="block5" scope="global">
<type>String</type>
</param>
<param display="Service End Point" name="fnm.serviceEnd" default="FP-1-1-1-1-1" block="block5" scope="global">
<type>Composite</type>
<value>FP-1-1-__%slot1__-__%portid1__-__%fp1id__</value>
</param>
</command>

<fragment block="block2" resolveGlobalParams="true">
#
#CLI:FP-1-1-__%slot1__-__%portid1__-__%fp1id__  Create
#
home
network-element ne-1
  configure __%module1__-1-1-__%slot1__
    configure port eth_port-1-1-__%slot1__-__%portid1__
	alias "__%port1Alias__"
      add flowpoint %fp1id "__%fp1Alias__" disabled 0 disabled disabled disabled "" disabled %ctagcontrol %ctag %stagcontrol %stag none none

#
#CLI:FP-1-1-__%slot2__-__%portid2__-__%fp2id__  Create
#
home
network-element ne-1
  configure __%module2__-1-1-__%slot2__
    configure port eth_port-1-1-__%slot2__-__%portid2__		
	alias "__%port2Alias__"
	add flowpoint %fp2id "__%fp2Alias__" disabled 0 disabled disabled disabled "%vlanmember" disabled none none none none

#
#CLI:POLICER-1-1-__%slot1__-__%portid1__-__%fp1id__-0  Create
#
home
network-element ne-1
  configure __%module1__-1-1-__%slot1__
    configure port eth_port-1-1-__%slot1__-__%portid1__
      configure flowpoint fp-1-1-__%slot1__-__%portid1__-__%fp1id__
        add policer 0 pol-profile select policer_profile-__%fp1Profile__

#
#CLI:POLICER-1-1-__%slot2__-__%portid2__-__%fp2id__-0  Create
#
home
network-element ne-1
  configure __%module2__-1-1-__%slot2__
    configure port eth_port-1-1-__%slot2__-__%portid2__
      configure flowpoint fp-1-1-__%slot2__-__%portid2__-__%fp2id__
        add policer 0 pol-profile select policer_profile-__%fp2Profile__

#
#CLI:SHAPER-1-1-__%slot1__-__%portid1__-__%fp1id__-0  Create
#
home
network-element ne-1
  configure __%module1__-1-1-__%slot1__
    configure port eth_port-1-1-__%slot1__-__%portid1__
      configure flowpoint fp-1-1-__%slot1__-__%portid1__-__%fp1id__
        add shaper 0 queue_profile-__%fp1QueProfile__
        configure shaper shaper-1-1-__%slot1__-__%portid1__-__%fp1id__-0
          additional-bw 0

#
#CLI:SHAPER-1-1-__%slot2__-__%portid2__-__%fp2id__-0  Create
#
home
network-element ne-1
  configure __%module2__-1-1-__%slot2__
    configure port eth_port-1-1-__%slot2__-__%portid2__
      configure flowpoint fp-1-1-__%slot2__-__%portid2__-__%fp2id__
        add shaper 0 queue_profile-__%fp2QueProfile__
        configure shaper shaper-1-1-__%slot2__-__%portid2__-__%fp2id__-0
          additional-bw 0

#
#CLI:FLOW-1-1  Create
#
home
network-element ne-1
  add flow %elineID "__%circuitName__" fp-1-1-__%slot1__-__%portid1__-__%fp1id__ fp-1-1-__%slot2__-__%portid2__-__%fp2id__
  configure flow flow-1-__%elineID__    
    admin-state in-service
</fragment>
</template>

