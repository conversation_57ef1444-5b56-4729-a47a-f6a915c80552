<template>
<header>
<neType>FSP 150EG-X </neType>
<applyMode>Delta</applyMode>
<version>1.4</version>
<summary>PWE Service Creation EGX</summary>
<category>Service Provisioning</category>
<comment>#</comment>
</header>
<cli-command># DO NOT EDIT THIS LINE. FILE_TYPE=CONFIGURATION_FILE</cli-command>

<command>
<block display="EBP Configuration for FP1" name="block2" blockOrder="2"/>
<param display="Module" name="module1" default="STM1-4-PW" block="block2" scope="global">
<type>Enum</type>
<token display="STM1-4-PW" literal="stm1-4-pw stm1_4_pw"/>
</param>
<param display="Slot No" name="slot1" default="22" block="block2" scope="global">
<type>Integer</type>
</param>
<!--param display="Port No." name="portid1" default="1" block="block2" scope="global">
<type>Integer</type>
</param-->
<param display="EBP Alias " name="port1Alias" default="Base Station 22" block="block2" scope="global">
<type>String</type>
</param>
<param display="FlowPoint1 No." name="fp1id" block="block2" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.fp">
		<arg>%slot1</arg>
		<arg>1</arg>
	</function>
</default>
</param>
<param display="FlowPoint1 Alias " name="fp1Alias" default="T1-424/22" block="block2" scope="global">
<type>String</type>
</param>
<param display="VlanMembers" name="vlanm" default="30-*"  block="block2" scope="global">
<type>String</type>
</param>
<param display="Untagged" name="tagging" default="Disabled" block="block2" scope="global">
<type>Enum</type>
<token display="Enabled" literal="enabled"/>
<token display="Disabled" literal="disabled"/>
</param>

<block display="COS #1 Configuration" name="cos0fp1ConfigurationBlock" blockParent="block2" />
<param display="ID" name="cos0fp1id" default="COS-0" block="cos0fp1ConfigurationBlock" scope="global">
<type>Enum</type>
<token display="COS-0" literal="0"/>
<token display="COS-1" literal="1"/>
<token display="COS-2" literal="2"/>
<token display="COS-3" literal="3"/>
<token display="COS-4" literal="4"/>
<token display="COS-5" literal="5"/>
<token display="COS-6" literal="6"/>
<token display="COS-7" literal="7"/>
</param>
<param display="COS #1 Policer Profile" name="cos0fp1PolicerProfile" block="cos0fp1ConfigurationBlock" scope="global">
<type>Enum</type>
<token function="fnm.db.policerProfile"/>
</param>
<param display="COS #1 Queue Profile" name="cos0p1QueueProfile" default="BS_192" block="cos0fp1ConfigurationBlock" scope="global">
<type>Enum</type>
<token function="fnm.db.queueProfile"/>
</param>
</command>

<command>
<block display="Traffic Port Configuration for FP2" name="block3" blockOrder="3" />
<param display="Module" name="module2" default="GE-10S" block="block3" paramOrder="1" scope="global">
<type>Enum</type>
<token display="GE-10S" literal="ge-10s ge_10s"/>
<token display="GE-10S-H" literal="ge-10s-h ge_10s_h"/>
<token display="XG-1X" literal="xg-1x xg_1x"/>
<token display="XG-1X-H" literal="xg-1x-h xg_1x_h"/>
</param>
<param display="Slot No" name="slot2" default="17" block="block3" scope="global">
<type>Integer</type>
</param>
<param display="Port No." name="portid2" default="9" block="block3" scope="global">
<type>Integer</type>
</param>
<param display="Port Alias" name="port2Alias" default="Ethernet Cloud - MEN " block="block3" scope="global">
<type>String</type>
</param>
<param display="FlowPoint2 No." name="fp2id" block="block3" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.fp">
		<arg>%slot2</arg>
		<arg>1</arg>
	</function>
</default>
</param>
<param display="FlowPoint2 Alias " name="fp2Alias" default="T1-424/22" block="block3" scope="global">
<type>String</type>
</param>
<param display="VLAN Member FP2" name="vlanmemberFP2" default="0" copyFrom="vlanm"  block="block3" scope="global">
<type>String</type>
</param>
<param display="FlowPoint2 Egress Outer Prio Map" name="fp2OuterTagPrioCtrl" default="Enabled" block="block3" scope="global">
<type>Enum</type>
<token display="Enabled" literal="enabled"/>
<token display="Disabled" literal="disabled"/>
</param>
<block display="COS #1 Configuration" name="cos0fp2ConfigurationBlock" blockParent="block3" />
<param display="ID" name="cos0fp2id" default="COS-0" block="cos0fp2ConfigurationBlock" copyFrom="cos0fp1id" scope="global">
<type>Enum</type>
<token display="COS-0" literal="0"/>
<token display="COS-1" literal="1"/>
<token display="COS-2" literal="2"/>
<token display="COS-3" literal="3"/>
<token display="COS-4" literal="4"/>
<token display="COS-5" literal="5"/>
<token display="COS-6" literal="6"/>
<token display="COS-7" literal="7"/>
</param>
<param display="COS #1 Policer Profile" name="cos0fp2PolicerProfile" copyFrom="cos0fp1PolicerProfile" block="cos0fp2ConfigurationBlock" scope="global">
<type>Enum</type>
<token function="fnm.db.policerProfile"/>
</param>
<param display="COS #1 Queue Profile" name="cos0fp2QueueProfile" copyFrom="cos0p1QueueProfile" block="cos0fp2ConfigurationBlock" scope="global">
<type>Enum</type>
<token function="fnm.db.queueProfile"/>
</param>


</command>

<command>
<block display="Service Parameters for FP2" name="block4" blockOrder="4" blockParent="block3" />
<param display="CTag Control" name="ctagcontrol" default="None" block="block4" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Push" literal="push __%ctag__"/>
<token display="Push VID" literal="push-vid __%ctag__"/>
<token display="Swap VID" literal="swap-vid __%ctag__"/>
</param>
<param display="CTag-Priority" name="ctag" default="302-0" block="block4" optional="true" scope="global">
<type>String</type>
</param>
</command>

<command>
<block display="Service Configuration" name="block5"  blockOrder="1" />
<param display="ELine No. (FLOW-1-X)" name="elineID" block="block5" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.elineFlow"/>
</default>
</param>
<param display="Circuit Name" name="circuitName" default="TMobile Srv 34" block="block5" scope="global">
<type>String</type>
</param>
<param display="Service End Point" name="fnm.trailServiceEnd" default="FP-1-1-1-1-1" block="block5" scope="global">
<type>Composite</type>
<value>FP-1-1-__%slot1__-1-__%fp1id__</value>
</param>
</command>

<fragment block="block2" resolveGlobalParams="true">
#
#CLI:FP-1-1-__%slot1__-1-__%fp1id__  Create
#
home
network-element ne-1
  configure __%module1__-1-1-__%slot1__
    configure ebp-port eth_bp_port-1-1-__%slot1__-1
	alias "__%port1Alias__"
	admin-state in-service
    add flowpoint %fp1id %fp1Alias %cos0fp1id %vlanm %tagging
	#for EGX 5.6.1 add flowpoint %fp1id "__%fp1Alias__" enabled 0 "" disabled %ctagcontrol none
	configure flowpoint fp-1-1-__%slot1__-1-__%fp1id__
	
#
#CLI:FP-1-1-__%slot2__-__%portid2__-__%fp2id__  Create
#
home
network-element ne-1
  configure __%module2__-1-1-__%slot2__
    configure port eth_port-1-1-__%slot2__-__%portid2__
	prio-map-mode 802.1p
	alias "__%port2Alias__"
	admin-state in-service
	add flowpoint %fp2id __%fp2Alias__ disabled %cos0fp2id disabled disabled disabled "%vlanmemberFP2" disabled %ctagcontrol none none none
	#for EGX 5.6.1 add flowpoint %fp2id "__%fp2Alias__" enabled 0 "%vlanmemberFP2" disabled none none
	configure flowpoint fp-1-1-__%slot2__-__%portid2__-__%fp2id__
	egress-outer-prio-map %fp2OuterTagPrioCtrl
#
#CLI:POLICER-1-1-__%slot1__-1-__%fp1id__-__%cos0fp1id__  Create
#
home
network-element ne-1
  configure __%module1__-1-1-__%slot1__
    configure ebp-port eth_bp_port-1-1-__%slot1__-1
      configure flowpoint fp-1-1-__%slot1__-1-__%fp1id__
        add policer 0 pol-profile select policer_profile-__%cos0fp1PolicerProfile__

#
#CLI:POLICER-1-1-__%slot2__-__%portid2__-__%fp2id__-__%cos0fp2id__  Create
#
home
network-element ne-1
  configure __%module2__-1-1-__%slot2__
    configure port eth_port-1-1-__%slot2__-__%portid2__
      configure flowpoint fp-1-1-__%slot2__-__%portid2__-__%fp2id__
        add policer 0 pol-profile select policer_profile-__%cos0fp2PolicerProfile__

#
#CLI:SHAPER-1-1-__%slot1__-1-__%fp1id__-__%cos0fp1id__  Create
#
home
network-element ne-1
  configure __%module1__-1-1-__%slot1__
    configure ebp-port eth_bp_port-1-1-__%slot1__-1
      configure flowpoint fp-1-1-__%slot1__-1-__%fp1id__
        add shaper 0 queue_profile-__%cos0fp2QueueProfile__
        configure shaper shaper-1-1-__%slot1__-1-__%fp1id__-0
          additional-bw 0

#
#CLI:SHAPER-1-1-__%slot2__-__%portid2__-__%fp2id__-__%cos0fp2id__  Create
#
home
network-element ne-1
  configure __%module2__-1-1-__%slot2__
    configure port eth_port-1-1-__%slot2__-__%portid2__
      configure flowpoint fp-1-1-__%slot2__-__%portid2__-__%fp2id__
        add shaper 0 queue_profile-__%cos0fp2QueueProfile__
        configure shaper shaper-1-1-__%slot2__-__%portid2__-__%fp2id__-0
          additional-bw 0

</fragment>	
	  

<fragment block="block5" resolveGlobalParams="true">		 
#
#CLI:FLOW-1-1  Create
#
home
network-element ne-1
  add flow %elineID "__%circuitName__" fp-1-1-__%slot1__-1-__%fp1id__ fp-1-1-__%slot2__-__%portid2__-__%fp2id__
  configure flow flow-1-__%elineID__    
    admin-state in-service
</fragment>



<command>
<block display="SATOP Creation" name="blockentity" blockOrder="6" blockOptionality="true"/>
<param display="Admin State " name="adminstate3" default="IS" block="blockentity" scope="global">
<type>Enum</type>
<token display="IS" literal="in-service"/>
<token display="Management" literal="management"/>
</param>
<param display="OC/STM Port No." name="ocStmPortIndex" default="1" block="blockentity" scope="global">
<type>Integer</type>
</param>
<param display="STS/VC4 Index" name="stsVcIndex" default="1" block="blockentity" scope="global">
<type>Integer</type>
</param>
<param display="VT15/VC12 Index" name="vt15vc12index" default="1" block="blockentity" scope="global">
<type>Integer</type>	
</param>
<param display="T1/E1 Index" name="t1e1Index" default="1" block="blockentity" scope="global">
<type>Integer</type>
</param>
<param display="Alias" name="allias3" default="CES 34/1" block="blockentity" scope="global">
<type>String</type>
</param>
<param display="SATOP Index" name="satopindex3" block="blockentity" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.satop">
		<arg>%slot1</arg>
	</function>
</default>
</param>
<param display="Payload size" name="payloadsize3" default="16" block="blockentity" scope="global">
<type>String</type>
</param>
<param display="Buffer size" name="buffersize3" default="4000" block="blockentity" scope="global">
<type>String</type>
</param>
<param display="Vlan Tag" name="vlantag3" default="30-0" copyFrom="vlanm" block="blockentity" scope="global" regexp="(\d+-[0])">
<type>String</type>
</param>
<param display="TXPW Label" name="txpw3" default="16" block="blockentity" scope="global">
<type>String</type>
</param>
<param display="EXPW Label" name="rxpw3" default="16" block="blockentity" scope="global">
<type>String</type>
</param>
<param display="FNM_SERVICE_END" name="fnm.serviceEnd" default="SATOP-1-1-22-1" block="blockentity" paramOrder="1" scope="global" >
<type>Composite</type>
<value>SATOP-1-1-__%slot1__-__%satopindex3__</value>
</param>
</command>


<command>
<block display="Europe Standard" name="europeBlock" blockOrder="7" blockOptionality="true" blockParent="blockentity" />
<param display="STM1-4" name="stmoceurope" default="STM1" block="europeBlock" scope="global">
<type>Enum</type>
<token display="STM1" literal="stm1"/>
<token display="STM4" literal="stm4"/>
</param>
<param display="Transport Mode" name="transportmodeurope" default="E1" block="europeBlock" scope="global">
<type>Enum</type>
<token display="E1" literal="e1 fac_e1"/>
</param>
</command>


<command>
<block display="AMERICA Standard" name="americaBlock" blockOrder="8" blockOptionality="true" blockParent="blockentity" expanded="false" selected="false" />
<param display="OC3-12" name="stmocamerica" default="OC12" block="americaBlock" scope="global">
<type>Enum</type>
<token display="OC12" literal="oc12"/>
<token display="OC3" literal="oc3"/>
</param>
<param display="Transport Mode" name="transportmodeamerica" default="T1" block="americaBlock" scope="global">
<type>Enum</type>
<token display="Octet Aligned T1" literal="octet-aligned-t1 fac_t1"/>
<token display="T1" literal="t1 fac_t1"/>
</param>
</command>

<command>
<block display="Discovery Type Dynamic" name="dynamicBlock" blockOrder="9" blockOptionality="true" blockParent="blockentity" expanded="true" selected="true" />
<param display="Discovery Type" name="discoverytypedynamic" default="Dynamic" block="dynamicBlock" scope="global">
<type>Enum</type>
<token display="Dynamic" literal="dynamic"/>
</param>
<param display="Remote IP Address" name="remoteipdynamic" default="*******" block="dynamicBlock" scope="global">
<type>String</type>
</param>
</command>

<command>
<block display="Discovery Type Static" name="staticBlock" blockOrder="10" blockOptionality="true" blockParent="blockentity" expanded="false" selected="false" />
<param display="Discovery Type" name="discoverytypestatic" default="Static" block="staticBlock" scope="global">
<type>Enum</type>
<token display="Static" literal="static"/>
</param>
<param display="Remote MAC Address" name="remotemac" default="30:00:00:00:00:00" optional="false" block="staticBlock" scope="global">
<type>String</type>
</param>
<param display="Remote IP Address" name="remoteipstatic" default="*******" block="staticBlock" scope="global">
<type>String</type>
</param>
</command>

<fragment block="blockentity" resolveGlobalParams="true">



#CLI:STM1-1-1-__%slot1__-__%ocStmPortIndex__  Edit
#

home
network-element ne-1
configure __%module1__-1-1-__%slot1__
configure port __%stmoceurope____%stmocamerica__-1-1-__%slot1__-__%ocStmPortIndex__
admin-state in-service
#
#CLI:SATOP-1-1-22-1  Create
#
home
network-element ne-1
configure __%module1__-1-1-__%slot1__
add satop satop-1-1-__%slot1__-__%satopindex3__ __%transportmodeurope____%transportmodeamerica__-1-1-__%slot1__-__%ocStmPortIndex__-__%stsVcIndex__-__%vt15vc12index__-__%t1e1Index__ __%payloadsize3__ __%buffersize3__ disabled enabled __%discoverytypedynamic__ __%discoverytypestatic__ __%remotemac__ __%remoteipdynamic__ __%remoteipstatic__ vlan-one-mpls-label __%vlantag3__ __%txpw3__ __%rxpw3__
configure satop satop-1-1-__%slot1__-__%satopindex3__
admin-state __%adminstate3__
alias "__%allias3__"
</fragment>

</template>

