<template>
<header>
<neType>FSP 150CC-XG210, FSP 150CC-XG210C</neType>
<applyMode>Delta</applyMode>
<version>1.4</version>
<summary>E-LAN Service Creation</summary>
<category>Service Provisioning</category>
<comment>#</comment>
</header>
<cli-command>
# DO NOT EDIT THIS LINE. FILE_TYPE=CONFIGURATION_FILE
</cli-command>
<command>
<block display="Access Port Configuration" name="AccPortConfBlock" />
<param display="LC" name="accLcIndex" default="Select card number" block="AccPortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="NTE-1" literal="1" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="LC-2" literal="2" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="LC-3" literal="3" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
</param>
<param display="Card Type" name="accCardType" default="Select card type" block="AccPortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="NTEXG210C" literal="nte ntexg210c" neType="FSP 150CC-XG210C"/>
<token display="NTEXG210" literal="nte ntexg210" neType="FSP 150CC-XG210"/>
<token display="XG-1X-CC" literal="xg_1x_cc xg_1x_cc" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="XG-1S-CC" literal="xg_1s_cc xg_1s_cc" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="GE-8E-CC" literal="ge_8e_cc ge_8e_cc" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="GE-8S-CC" literal="ge_8s_cc ge_8s_cc" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
</param>
<param display="Port" name="accPort" default="Select port" block="AccPortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="NETWORK PORT-1" literal="1"/>
<token display="NETWORK PORT-2" literal="2" />
<token display="ACCESS PORT-1" literal="1"/>
<token display="ACCESS PORT-2" literal="2" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="ACCESS PORT-3" literal="3" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="ACCESS PORT-4" literal="4" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="ACCESS PORT-5" literal="5" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="ACCESS PORT-6" literal="6" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="ACCESS PORT-7" literal="7" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="ACCESS PORT-8" literal="8" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
</param>
<!--param display="Speed" name="accSpeed" default="auto-1000-full" block="AccPortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="100-full" literal="100-full"/>
<token display="1000-full" literal="1000-full"/>
<token display="auto-1000-full" literal="auto-1000-full"/>
<token display="auto-speed-detect" literal="auto-speed-detect"/>
</param>
<param display="Admin State" name="accAdminstate" default="IS" block="AccPortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="Disabled" literal="disabled"/>
<token display="IS" literal="in-service"/>
<token display="Maintenance" literal="maintenance"/>
<token display="Management" literal="management"/>
<token display="Unassigned" literal="unassigned"/>
</param-->

</command>

<command>
<block display="Network Port Configuration" name="NetPortConfBlock" />
<param display="LC" name="netLcIndex" default="Select card number" block="NetPortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="NTE-1" literal="1" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="LC-2" literal="2" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="LC-3" literal="3" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
</param>
<param display="Card Type" name="netCardType" default="Select card type" block="NetPortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="NTEXG210C" literal="nte ntexg210c" neType="FSP 150CC-XG210C"/>
<token display="NTEXG210" literal="nte ntexg210" neType="FSP 150CC-XG210"/>
<token display="XG-1X-CC" literal="xg_1x_cc xg_1x_cc" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="XG-1S-CC" literal="xg_1s_cc xg_1s_cc" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="GE-8E-CC" literal="ge_8e_cc ge_8e_cc" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="GE-8S-CC" literal="ge_8s_cc ge_8s_cc" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
</param>
<param display="Port" name="netPort" default="Select port" block="NetPortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="NETWORK PORT-1" literal="1"/>
<token display="NETWORK PORT-2" literal="2" />
<token display="ACCESS PORT-1" literal="1"/>
<token display="ACCESS PORT-2" literal="2" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="ACCESS PORT-3" literal="3" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="ACCESS PORT-4" literal="4" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="ACCESS PORT-5" literal="5" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="ACCESS PORT-6" literal="6" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="ACCESS PORT-7" literal="7" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
<token display="ACCESS PORT-8" literal="8" neType="FSP 150CC-XG210, FSP 150CC-XG210C"/>
</param>
<!--param display="Speed" name="netSpeed" default="auto-1000-full" block="NetPortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="100-full" literal="100-full"/>
<token display="1000-full" literal="1000-full"/>
<token display="auto-1000-full" literal="auto-1000-full"/>
<token display="auto-speed-detect" literal="auto-speed-detect"/>
</param>
<param display="Admin State" name="netAdminstate" default="IS" block="NetPortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="Disabled" literal="disabled"/>
<token display="IS" literal="in-service"/>
<token display="Maintenance" literal="maintenance"/>
<token display="Management" literal="management"/>
<token display="Unassigned" literal="unassigned"/>
</param-->
<block display="MP Flow" name="MPFlowConfBlock" />
<param display="MP Flow" name="mpFlowIndex" block="MPFlowConfBlock" paramOrder="1" scope="global">
<type>Integer</type>
<default>
<function name="nextIndex" object="fnm.db.mpFlow"/>
</default>
</param>
</command>

<command>
<block display="Service Configuration FP1" name="ServiceConfBlock" />
<param display="FP 1" name="fp1Index" block="ServiceConfBlock" paramOrder="1" scope="global">
<type>Integer</type>
<default>
<function name="nextIndex" object="fnm.db.fp">
<arg>%accLcIndex</arg>
<arg>%accPort</arg>
</function>
</default>
</param>
<block display="Details:" name="FP1DetailsBlock" blockParent="ServiceConfBlock"/>
<param display="Alias" name="fp1Alias" block="FP1DetailsBlock" paramOrder="1" scope="global">
<type>String</type>
</param>
<param display="MultiCOS" name="fp1MCos" default="Disabled" block="FP1DetailsBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="Disabled" literal="disabled"/>
<token display="Enabled" literal="enabled"/>
</param>
<param display="C-TAG Control" name="fp1CTagControl" default="Push" block="FP1DetailsBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Push" literal="push"/>
<token display="PushVID" literal="push_vid"/>
</param>
<param display="C-TAG - Prio" name="fp1CTag" default="1-0" block="FP1DetailsBlock" paramOrder="1" scope="global">
<type>String</type>
</param>
<param display="S-TAG Control" name="fp1STagControl" default="Push" block="FP1DetailsBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Push" literal="push"/>
<token display="PushVID" literal="push_vid"/>
</param>
<param display="S-TAG - Prio" name="fp1STag" default="1-0" block="FP1DetailsBlock" paramOrder="1" scope="global">
<type>String</type>
</param>
<param display="VLANs" name="fp1VLANs" default="12-2,13-*" block="FP1DetailsBlock" paramOrder="1" scope="global">
<type>String</type>
</param>

<block display="Service Configuration FP2" name="FP2OptionBlock" blockOptionality="true" selected="false" expanded="false"/>
<param display="FP 2" name="fp2Index" block="FP2OptionBlock" paramOrder="1" scope="global">
<type>Integer</type>
<default>
<function name="nextIndex" object="fnm.db.fp">
<arg>%accLcIndex</arg>
<arg>%accPort</arg>
</function>
</default>
</param>

<block display="Service Configuration FP3" name="FP3OptionBlock" blockOptionality="true" selected="false" expanded="false"/>
<param display="FP 3" name="fp3Index" block="FP3OptionBlock" paramOrder="1" scope="global">
<type>Integer</type>
<default>
<function name="nextIndex" object="fnm.db.fp">
<arg>%accLcIndex</arg>
<arg>%accPort</arg>
</function>
</default>
</param>

<block display="Service Configuration FP4" name="FP4OptionBlock" blockOptionality="true" selected="false" expanded="false"/>
<param display="FP 4" name="fp4Index" block="FP4OptionBlock" paramOrder="1" scope="global">
<type>Integer</type>
<default>
<function name="nextIndex" object="fnm.db.fp">
<arg>%accLcIndex</arg>
<arg>%accPort</arg>
</function>
</default>
</param>
</command>

<fragment block="AccPortConfBlock" resolveGlobalParams="true">
accLcIndex %accLcIndex
accPort %accPort
netLcIndex %netLcIndex
netPort %netPort
</fragment>
</template>