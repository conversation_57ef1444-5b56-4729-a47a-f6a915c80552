<template>
<header>
<neType>OSA 5420, OSA 5411 </neType>
<applyMode>Delta</applyMode>
<version>1.2</version>
<summary>Raw Data Collection FTP Server Configuration Template.</summary>
<category>Bulk Configuration</category>
</header>
<cli-command># DO NOT EDIT THIS LINE. FILE_TYPE=CONFIGURATION_FILE</cli-command>

<cli-command>

home
configure system
</cli-command>
<command>
<block display="Settings" name="block1" />
<param display="FTP" name="ftp" block="block1" >
<type>Enum</type>
<token display="disabled" literal="disabled"/>
<token display="enabled" literal="enabled"/>
</param>
<substitution>ftp %ftp </substitution>

</command>


<command>
<param display="SFTP" name="sftp" block="block1" >
<type>Enum</type>
<token display="disabled" literal="disabled"/>
<token display="enabled" literal="enabled"/>
</param>
<substitution>sftp %sftp </substitution>

</command>


<command>
<param display="TELNET" name="telnet" block="block1" >
<type>Enum</type>
<token display="disabled" literal="disabled"/>
<token display="enabled" literal="enabled"/>
</param>
<substitution>telnet %telnet </substitution>

</command>

<command>
<param display="HTTP" name="http" block="block1" >
<type>Enum</type>
<token display="disabled" literal="disabled"/>
<token display="enabled" literal="enabled"/>
</param>
<substitution>http %http </substitution>

</command>

<command>
<param display="HTTPS" name="https" block="block1" >
<type>Enum</type>
<token display="disabled" literal="disabled"/>
<token display="enabled" literal="enabled"/>
</param>
<substitution>https %https </substitution>

</command>
</template>


