<template>
<header>
<neType> FSP 150EG-X</neType>
<applyMode>Delta</applyMode>
<version>1.4</version>
<summary>G.8032 Ring Creation: ERP with MP FLOW (Default, SingleCOS, Hierarchical COS Enabled)</summary>
<category>Service Provisioning</category>
<comment>#</comment>
</header>
<cli-command>
# DO NOT EDIT THIS LINE. FILE_TYPE=CONFIGURATION_FILE
</cli-command>
<command>
<block display="G.8032 ERP Configuration" name="RingBlock" />
<param display="ERP Instance ID" name="erpIndex" block="RingBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.erp"/>
</default>
</param>
<param display="R-APS Tag Vlan (e.g. 4094)" name="rapsTagID" default="4094" copyFrom="rapsTagID" conveyanceType="locked" block="RingBlock" scope="global">
<type>String</type>
</param>
<param display="R-APS Tag COS Level" name="rapsCosID" default="7" copyFrom="rapsCosID" conveyanceType="locked" block="RingBlock" scope="global" regexp="[0-7]">
<type>Integer</type>
</param>
<param display="Ring ID" name="ringID" default="1" copyFrom="ringID" conveyanceType="locked" block="RingBlock" scope="global">
<type>Integer</type>
</param>
<param display="R-APS MD Level" name="rapsMDLevel" default="0" copyFrom="rapsMDLevel" block="RingBlock" scope="global">
<type>Enum</type>
<token display="0" literal="0"/>
<token display="1" literal="1"/>
<token display="2" literal="2"/>
<token display="3" literal="3"/>
<token display="4" literal="4"/>
<token display="5" literal="5"/>
<token display="6" literal="6"/>
<token display="7" literal="7"/>
</param>
<param display="Alias" name="alias" default="MyRing" copyFrom="alias" block="RingBlock" scope="global">
<type>String</type>
</param>
<param display="G.8032 Ring ETrail" name="fnm.erp.trailServiceEnd" default="ERP-1-1" block="RingBlock" scope="global">
<type>Composite</type>
<value>ERP-1-__%erpIndex__</value>
</param>
</command>

<command>
<block display="Port Configuration for RPL and FP" name="PortBlock"/>
<param display="Card Type for Ring Port 0" name="cardType0" default="GE-10S-H" block="PortBlock" scope="global">
<type>Enum</type>
<token display="GE-10S" literal="ge-10s ge_10s"/>
<token display="GE-10S-H" literal="ge-10s-h ge_10s_h"/>
<token display="XG-1X" literal="xg-1x xg_1x"/>
<token display="XG-1X-H" literal="xg-1x-h xg_1x_h"/>
</param>
<param display="Ring Port 0 Slot Index" name="slotRing0Index" default="1" block="PortBlock" scope="global">
<type>Integer</type>
</param>
<param display="Ring Port 0" name="ringPort0Index" default="1" block="PortBlock" scope="global">
<type>Integer</type>
</param>
<param display="Ring Port 0 Role" name="ringPort0Role" default="None" block="PortBlock" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Neighbor" literal="neighbor"/>
<token display="Owner" literal="owner"/>
</param>
<param display="Card Type for Ring Port 1" name="cardType1" default="GE-10S-H" block="PortBlock" scope="global">
<type>Enum</type>
<token display="GE-10S" literal="ge-10s ge_10s"/>
<token display="GE-10S-H" literal="ge-10s-h ge_10s_h"/>
<token display="XG-1X" literal="xg-1x xg_1x"/>
<token display="XG-1X-H" literal="xg-1x-h xg_1x_h"/>
</param>
<param display="Ring Port 1 Slot Index" name="slotRing1Index" default="1" block="PortBlock" scope="global">
<type>Integer</type>
</param>
<param display="Ring Port 1" name="ringPort1Index" default="2" block="PortBlock" scope="global">
<type>Integer</type>
</param>
<param display="Ring Port 1 Role" name="ringPort1Role" default="None" block="PortBlock" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Neighbor" literal="neighbor"/>
<token display="Owner" literal="owner"/>
</param>
</command>



<fragment block="PortBlock" resolveGlobalParams="true">
<!--#Puting port 0 in Service Mulitplexing state.
home
ne ne-1
configure __%cardType0__-1-1-__%slotRing0Index__
configure port eth_port-1-1-__%slotRing0Index__-__%ringPort0Index__
admin-state unassigned
service-type service-multiplexing
#Puting port 1 in Service Mulitplexing state.
home
ne ne-1  
configure __%cardType1__-1-1-__%slotRing1Index__
configure port eth_port-1-1-__%slotRing1Index__-__%ringPort1Index__	
admin-state unassigned
service-type service-multiplexing
-->
#Set up RING
home
ne ne-1
add erp %erpIndex %alias __%rapsTagID__-__%rapsCosID__ %ringID %rapsMDLevel ring-port-0 eth_port-1-1-__%slotRing0Index__-__%ringPort0Index__ ring-port-1 eth_port-1-1-__%slotRing1Index__-__%ringPort1Index__
configure erp erp-1-__%erpIndex__
ring-port0-role %ringPort0Role
ring-port1-role %ringPort1Role
</fragment>

<command>
<block display="MP Flow Configuration" name="MPFlowBlock"/>
<param display="R-APS MP Flow ID" name="rapsMPFlowIndex" block="MPFlowBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.mpFlow"/>
</default>
</param>
<param display="R-APS FP-1" name="rapsFPIndex0" block="MPFlowBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.fp">
		<arg>%slotRing0Index</arg>
		<arg>%ringPort0Index</arg>
	</function>
</default>
</param>
<param display="R-APS FP-2" name="rapsFPIndex1" block="MPFlowBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.fp">
		<arg>%slotRing1Index</arg>
		<arg>%ringPort1Index</arg>
	</function>
</default>
</param>
<param display="R-APS Policer Profile" name="rapsPolicerProfile" copyFrom="rapsPolicerProfile" block="MPFlowBlock" scope="global">
<type>Enum</type>
<token function="fnm.db.policerProfile"/>
</param>
<param display="R-APS Queue Profile" name="rapsQueueProfile" copyFrom="rapsQueueProfile" block="MPFlowBlock" scope="global">
<type>Enum</type>
<token function="fnm.db.queueProfile"/>
</param>
<param display="Traffic MP Flow ID" name="mpFlowIndex" block="MPFlowBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.mpFlow"/>
</default>
</param>
<param display="Traffic FP-1" name="fpIndex0" block="MPFlowBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.fp">
		<arg>%slotRing0Index</arg>
		<arg>%ringPort0Index</arg>
	</function>
</default>
</param>
<param display="Traffic FP-2" name="fpIndex1" block="MPFlowBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.fp">
		<arg>%slotRing1Index</arg>
		<arg>%ringPort1Index</arg>
	</function>
</default>
</param>
<param display="Traffic Policer Profile" name="trafficPolicerProfile" copyFrom="trafficPolicerProfile" block="MPFlowBlock" scope="global">
<type>Enum</type>
<token function="fnm.db.policerProfile"/>
</param>
<param display="Traffic Queue Profile" name="trafficQueueProfile" copyFrom="trafficQueueProfile" block="MPFlowBlock" scope="global">
<type>Enum</type>
<token function="fnm.db.queueProfile"/>
</param>
<param display="Alias" name="mpFlowAlias" default="myMPFlow" block="MPFlowBlock" scope="global">
<type>String</type>
</param>
<param display="Untagged Frames" name="mpFlowUntaggedFrames" default="Disabled" copyFrom="mpFlowUntaggedFrames" block="MPFlowBlock" scope="global">
<type>Enum</type>
<token display="Disabled" literal="disabled"/>
<token display="Enabled" literal="enabled"/>
</param>
<param display="COS" name="mpFlowCOS" default="7" copyFrom="mpFlowCOS" block="MPFlowBlock" scope="global">
<type>Enum</type>
<token display="0" literal="0"/>
<token display="1" literal="1"/>
<token display="2" literal="2"/>
<token display="3" literal="3"/>
<token display="4" literal="4"/>
<token display="5" literal="5"/>
<token display="6" literal="6"/>
<token display="7" literal="7"/>
</param>
<param display="HCOS Guaranteed Flow Bandwidth (bps)" name="guaranteedBW" default="100000000" copyFrom="guaranteedBW" block="MPFlowBlock" scope="global">
<type>Integer</type>
</param>
<param display="HCOS Maximum Flow Bandwidth (bps)" name="maxBW" default="150000000" copyFrom="maxBW" block="MPFlowBlock" scope="global">
<type>Integer</type>
</param>
</command>

<fragment block="MPFlowBlock" resolveGlobalParams="true">
#CLI:FP-1-1-__%slotRing0Index__-__%ringPort0Index__-__%rapsFPIndex0__  Create RAPS FP
#RAPS Flow for ERP-1-__%erpIndex__
home
network-element ne-1
  configure __%cardType0__-1-1-__%slotRing0Index__
    configure port eth_port-1-1-__%slotRing0Index__-__%ringPort0Index__
	add flowpoint %rapsFPIndex0 "RAPS FP-__%rapsFPIndex0__ for ERP-1-__%erpIndex__" disabled %rapsCosID disabled disabled disabled "__%rapsTagID__-*" disabled none none none none
	
#
#CLI:POLICER-1-1-__%slotRing0Index__-__%ringPort0Index__-__%rapsFPIndex0__-0  Create RAPS Policer
#
home
network-element ne-1
  configure __%cardType0__-1-1-__%slotRing0Index__
    configure port eth_port-1-1-__%slotRing0Index__-__%ringPort0Index__
      configure flowpoint fp-1-1-__%slotRing0Index__-__%ringPort0Index__-__%rapsFPIndex0__
        add policer 0 pol-profile select policer_profile-__%rapsPolicerProfile__ disabled
#
#CLI:SHAPER-1-1-__%slotRing0Index__-__%ringPort0Index__-__%rapsFPIndex0__-0  Create RAPS Shaper
#
home
network-element ne-1
  configure __%cardType0__-1-1-__%slotRing0Index__
    configure port eth_port-1-1-__%slotRing0Index__-__%ringPort0Index__
      configure flowpoint fp-1-1-__%slotRing0Index__-__%ringPort0Index__-__%rapsFPIndex0__
        add shaper 0 queue_profile-__%rapsQueueProfile__		
        configure shaper shaper-1-1-__%slotRing0Index__-__%ringPort0Index__-__%rapsFPIndex0__-0
          cir 195000
          eir 0
          ingress-average-frame-size 128		
		
#CLI:FP-1-1-__%slotRing1Index__-__%ringPort1Index__-__%rapsFPIndex1__  Create RAPS FP
#RAPS Flow for ERP-1-__%erpIndex__
home
network-element ne-1
  configure __%cardType1__-1-1-__%slotRing1Index__
    configure port eth_port-1-1-__%slotRing1Index__-__%ringPort1Index__
	add flowpoint %rapsFPIndex1 "RAPS FP-__%rapsFPIndex1__ for ERP-1-__%erpIndex__" disabled %rapsCosID disabled disabled disabled "__%rapsTagID__-*" disabled none none none none
	
#
#CLI:POLICER-1-1-__%slotRing1Index__-__%ringPort1Index__-__%rapsFPIndex1__-0  Create RAPS Policer
#
home
network-element ne-1
  configure __%cardType1__-1-1-__%slotRing1Index__
    configure port eth_port-1-1-__%slotRing1Index__-__%ringPort1Index__
      configure flowpoint fp-1-1-__%slotRing1Index__-__%ringPort1Index__-__%rapsFPIndex1__
        add policer 0 pol-profile select policer_profile-__%rapsPolicerProfile__ disabled

#
#CLI:SHAPER-1-1-__%slotRing1Index__-__%ringPort1Index__-__%rapsFPIndex1__-0  Create RAPS Shaper
#
home
network-element ne-1
  configure __%cardType1__-1-1-__%slotRing1Index__
    configure port eth_port-1-1-__%slotRing1Index__-__%ringPort1Index__
      configure flowpoint fp-1-1-__%slotRing1Index__-__%ringPort1Index__-__%rapsFPIndex1__
        add shaper 0 queue_profile-__%rapsQueueProfile__		
        configure shaper shaper-1-1-__%slotRing1Index__-__%ringPort1Index__-__%rapsFPIndex1__-0
          cir 195000
          eir 0
          ingress-average-frame-size 128	
#
#CLI:MP FLOW-1-__%rapsMPFlowIndex__  Create RAPS MP Flow
#
home
network-element ne-1
	add mp-flow %rapsMPFlowIndex "RAPS Flow for ERP-1-__%erpIndex__" fp-1-1-__%slotRing0Index__-__%ringPort0Index__-__%rapsFPIndex0__,fp-1-1-__%slotRing1Index__-__%ringPort1Index__-__%rapsFPIndex1__ enabled fwd_table_size_profile-1
		configure mp-flow mp_flow-1-__%rapsMPFlowIndex__
			admin-state in-service
	
#CLI:FP-1-1-__%slotRing0Index__-__%ringPort0Index__-__%fpIndex0__  Create Default FP
home
network-element ne-1
  configure __%cardType0__-1-1-__%slotRing0Index__
    configure port eth_port-1-1-__%slotRing0Index__-__%ringPort0Index__
      add flowpoint %fpIndex0 "" disabled %mpFlowCOS disabled disabled enabled "" disabled none none none erp-1-__%erpIndex__
      configure flowpoint fp-1-1-__%slotRing0Index__-__%ringPort0Index__-__%fpIndex0__
        hierarchical-cos enabled
        maximum-bw %maxBW
        guaranteed-bw %guaranteedBW     
	
#
#CLI:POLICER-1-1-__%slotRing0Index__-__%ringPort0Index__-__%fpIndex0__-0  Create Policer
#
home
network-element ne-1
  configure __%cardType0__-1-1-__%slotRing0Index__
    configure port eth_port-1-1-__%slotRing0Index__-__%ringPort0Index__
      configure flowpoint fp-1-1-__%slotRing0Index__-__%ringPort0Index__-__%fpIndex0__
        add policer 0 pol-profile select policer_profile-__%trafficPolicerProfile__ enabled

#
#CLI:SHAPER-1-1-__%slotRing0Index__-__%ringPort0Index__-__%fpIndex0__-0  Create Shaper
#
home
network-element ne-1
  configure __%cardType0__-1-1-__%slotRing0Index__
    configure port eth_port-1-1-__%slotRing0Index__-__%ringPort0Index__
      configure flowpoint fp-1-1-__%slotRing0Index__-__%ringPort0Index__-__%fpIndex0__
        add shaper 0 queue_profile-__%trafficQueueProfile__		
        configure shaper shaper-1-1-__%slotRing0Index__-__%ringPort0Index__-__%fpIndex0__-0
          cir 195000
          eir 0
          ingress-average-frame-size 128		
		  
#			
#CLI:FP-1-1-__%slotRing1Index__-__%ringPort1Index__-__%fpIndex1__  Create Default FP
home
network-element ne-1
  configure __%cardType1__-1-1-__%slotRing1Index__
    configure port eth_port-1-1-__%slotRing1Index__-__%ringPort1Index__
      add flowpoint %fpIndex1 "" disabled %mpFlowCOS disabled disabled enabled "" disabled none none none erp-1-__%erpIndex__
      configure flowpoint fp-1-1-__%slotRing1Index__-__%ringPort1Index__-__%fpIndex1__
        hierarchical-cos enabled
        maximum-bw %maxBW
        guaranteed-bw %guaranteedBW
		
#
#CLI:POLICER-1-1-__%slotRing1Index__-__%ringPort1Index__-__%fpIndex1__-0  Create Policer
#
home
network-element ne-1
  configure __%cardType1__-1-1-__%slotRing1Index__
    configure port eth_port-1-1-__%slotRing1Index__-__%ringPort1Index__
      configure flowpoint fp-1-1-__%slotRing1Index__-__%ringPort1Index__-__%fpIndex1__
        add policer 0 pol-profile select policer_profile-__%trafficPolicerProfile__ enabled		
#
#CLI:SHAPER-1-1-__%slotRing1Index__-__%ringPort1Index__-__%fpIndex1__-0  Create Shaper
#
home
network-element ne-1
  configure __%cardType1__-1-1-__%slotRing1Index__
    configure port eth_port-1-1-__%slotRing1Index__-__%ringPort1Index__
      configure flowpoint fp-1-1-__%slotRing1Index__-__%ringPort1Index__-__%fpIndex1__
        add shaper 0 queue_profile-__%trafficQueueProfile__		
        configure shaper shaper-1-1-__%slotRing1Index__-__%ringPort1Index__-__%fpIndex1__-0
          cir 195000
          eir 0
          ingress-average-frame-size 128	
   
#CLI:MP FLOW-1-1  Create   
home
network-element ne-1
  add mp-flow %mpFlowIndex %mpFlowAlias fp-1-1-__%slotRing0Index__-__%ringPort0Index__-__%fpIndex0__,fp-1-1-__%slotRing1Index__-__%ringPort1Index__-__%fpIndex1__ enabled fwd_table_size_profile-1
  configure mp-flow mp_flow-1-__%mpFlowIndex__
    admin-state in-service
</fragment>

<command>
<block display="CFM Configuration" name="cfmBlock" blockOptionality="true" selected="false"/>
<param display="MD ID" name="mdID" block="cfmBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.md"/>
</default>
</param>
<param display="MD Name" name="mdName" default="MD-1" block="cfmBlock" scope="global">
<type>String</type>
</param>
<param display="Ring Port 0 MEP ID" name="ringPort0MEPID" default="1" block="cfmBlock" scope="global">
<type>Enum</type>
<token display="1" literal="1"/>
<token display="2" literal="2"/>
</param>
<param display="Ring Port 0 OAM FP ID" name="ringPort0OAMID" default="2" block="cfmBlock" scope="global">
<type>Integer</type>
</param>
<param display="Ring Port 1 MEP ID" name="ringPort1MEPID" default="1" block="cfmBlock" scope="global">
<type>Enum</type>
<token display="1" literal="1"/>
<token display="2" literal="2"/>
</param>
<param display="Ring Port 1 OAM FP ID" name="ringPort1OAMID" default="2" block="cfmBlock" scope="global">
<type>Integer</type>
</param>
</command>

<fragment block="cfmBlock" resolveGlobalParams="true">
#CFM Section
#
#CLI:CFM MD-__%mdID__  Create
#
home
configure cfm
add md %mdID string "%mdName" %rapsMDLevel none
#
#CLI:CFM MANET-__%mdID__-1  Create
#CLI:CFM MANET-__%mdID__-2  Create
#
home
configure cfm
configure md md-__%mdID__
add manet 1 string "MA-__%mdID__-1" 300hz 1,2 
add manet 2 string "MA-__%mdID__-2" 300hz 1,2
#
#CLI:CFM MACOMP-__%mdID__-1-1  Create
#
home
configure cfm
configure md md-__%mdID__
configure manet manet-__%mdID__-1
add macomp 1 eth_port-1-1-__%slotRing0Index__-__%ringPort0Index__ %rapsTagID defer
add mep %ringPort0MEPID down eth_port-1-1-__%slotRing0Index__-__%ringPort0Index__ in-service all-defined enabled 0
#
#CLI:CFM MACOMP-__%mdID__-2-1  Create
#
home
configure cfm
configure md md-__%mdID__
configure manet manet-__%mdID__-2
add macomp 1 eth_port-1-1-__%slotRing1Index__-__%ringPort1Index__ %rapsTagID defer
add mep %ringPort1MEPID down eth_port-1-1-__%slotRing1Index__-__%ringPort1Index__ in-service all-defined enabled 0

#
#CLI:ADD CFM to RING ERP-1-__%erpIndex__
#
home
ne ne-1
configure erp erp-1-__%erpIndex__
chg-ring-ports ring-port-0 eth_port-1-1-__%slotRing0Index__-__%ringPort0Index__ mep-__%mdID__-1-__%ringPort0MEPID__ ring-port-1 eth_port-1-1-__%slotRing1Index__-__%ringPort1Index__ mep-__%mdID__-2-__%ringPort1MEPID__

#
#CLI:ADD OAM FP-1-1-__%slotRing0Index__-__%ringPort0Index__-__%ringPort0OAMID__
#
home
network-element ne-1
configure __%cardType0__-1-1-__%slotRing0Index__
configure port eth_port-1-1-__%slotRing0Index__-__%ringPort0Index__
add oam-flowpoint %ringPort0OAMID "Down MEP-__%mdID__-1-__%ringPort0MEPID__" disabled %rapsTagID

#
#CLI:ADD OAM FP-1-1-__%slotRing1Index__-__%ringPort1Index__-__%ringPort1OAMID__
#
home
network-element ne-1
configure __%cardType1__-1-1-__%slotRing1Index__
configure port eth_port-1-1-__%slotRing1Index__-__%ringPort1Index__
add oam-flowpoint %ringPort1OAMID "Down MEP-__%mdID__-1-__%ringPort1MEPID__" disabled %rapsTagID
</fragment>
</template>