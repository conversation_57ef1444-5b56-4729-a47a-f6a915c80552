<template>
<header>
<neType>FSP 150CC-GE206V</neType>

<applyMode>Delta</applyMode>
<version>1.2</version>
<summary>A end service template.</summary>

<category>Service Provisioning</category>
</header>

<cli-command>
# DO NOT EDIT THIS LINE. FILE_TYPE=CONFIGURATION_FILE
home
network-element ne-1
</cli-command>

<command>
<block display="PWE3 Selection" name="block1" blockOrder="1"/>
<param display="entity" name="pwe316e1t1" default="pwe3_16e1t1" block="block1" paramOrder="1">
<type>Enum</type>
<token display="lag" literal="lag"/>
<token display="nte" literal="nte"/>
<token display="psu" literal="psu"/>
<token display="pwe3_16e1t1" literal="pwe3_16e1t1"/>
<token display="sync" literal="sync"/>
</param>
<param display="entity ID" name="pwe316e1t1112" default="pwe3_16e1t1-1-1-2" block="block1" paramOrder="2" >
<type>String</type>
</param>
<substitution suppressAllIfParamsAreNull="true">configure %pwe316e1t1 %pwe316e1t1112</substitution>
</command>

<command>
<param display="admin-state" name="adminstate" default="in-service" block="block1" paramOrder="3">
<type>Enum</type>
<token display="in-service" literal="in-service"/>
<token display="management" literal="management"/>
</param>
<substitution>admin-state %adminstate</substitution>
</command>

<cli-command>
#CLI:SATOP-1-1-2-1  Create
#
home
network-element ne-1
</cli-command>

<command>
<block display="CREATE SATOP" name="block2" blockOrder="2"/>
<param display="entity" name="pwe316e1t1" default="pwe3_16e1t1" block="block2" paramOrder="1">
<type>Enum</type>
<token display="lag" literal="lag"/>
<token display="nte" literal="nte"/>
<token display="psu" literal="psu"/>
<token display="pwe3_16e1t1" literal="pwe3_16e1t1"/>
<token display="sync" literal="sync"/>
</param>
<param display="entity ID" name="pwe316e1t1112" default="pwe3_16e1t1-1-1-2" block="block2" paramOrder="2" >
<type>String</type>
</param>
<substitution suppressAllIfParamsAreNull="true">configure %pwe316e1t1 %pwe316e1t1112</substitution>
</command>

<command>
<param display="satop ID" name="addsatopID" default="1" block="block2" paramOrder="3" scope="global">
<type>Integer</type>
</param>
<param display="e1 ID" name="e1" default="e1-1-1-2-1" block="block2" paramOrder="4">
<type>Enum</type>
<token display="e1-1-1-2-1" literal="e1-1-1-2-1"/>
<token display="e1-1-1-2-2" literal="e1-1-1-2-2"/>
<token display="e1-1-1-2-3" literal="e1-1-1-2-3"/>
<token display="e1-1-1-2-4" literal="e1-1-1-2-4"/>
<token display="e1-1-1-2-5" literal="e1-1-1-2-5"/>
<token display="e1-1-1-2-6" literal="e1-1-1-2-6"/>
<token display="e1-1-1-2-7" literal="e1-1-1-2-7"/>
<token display="e1-1-1-2-8" literal="e1-1-1-2-8"/>
<token display="e1-1-1-2-9" literal="e1-1-1-2-9"/>
<token display="e1-1-1-2-10" literal="e1-1-1-2-10"/>
<token display="e1-1-1-2-11" literal="e1-1-1-2-11"/>
<token display="e1-1-1-2-12" literal="e1-1-1-2-12"/>
<token display="e1-1-1-2-13" literal="e1-1-1-2-13"/>
<token display="e1-1-1-2-14" literal="e1-1-1-2-14"/>
<token display="e1-1-1-2-15" literal="e1-1-1-2-15"/>
<token display="e1-1-1-2-16" literal="e1-1-1-2-16"/>
</param>

<param display="e1/t1" name="e" default="e1" block="block2" paramOrder="5" >
<type>String</type>
</param>

<param display="payload" name="payload" default="16" block="block2" paramOrder="6" >
<type>String</type>
</param>

<param display="jitter buffer [us]" name="buffersize" default="1000" block="block2" paramOrder="7" >
<type>String</type>
</param>

<param display="RTP-Protocol" name="rtp" default="disabled" block="block2" paramOrder="8" >
<type>Enum</type>
<token display="disabled" literal="disabled"/>
<token display="enabled" literal="enabled"/>
</param>

<param display="control word" name="control" default="enabled" block="block2" paramOrder="9" >
<type>Enum</type>
<token display="disabled" literal="disabled"/>
<token display="enabled" literal="enabled"/>
</param>

<param display="discovery type" name="discovery" default="dynamic" block="block2" paramOrder="10" >
<type>Enum</type>
<token display="dynamic" literal="dynamic"/>
<token display="static" literal="static"/>
</param>

<param display="remote tunnel ip" name="ip" default="**************" block="block2" paramOrder="11" >
<type>String</type>
</param>

<param display="encapsulation type" name="encapsulation" default="vlan-one-mpls-label" block="block2" paramOrder="12">
<type>Enum</type>
<token display="vlan-one-mpls-label" literal="vlan-one-mpls-label"/>
<token display="no-vlan-two-mpls-label" literal="no-vlan-two-mpls-label"/>
<token display="vlan-two-mpls-label" literal="vlan-two-mpls-label"/>
</param>

<param display="vlan-tag" name="vlan" default="16-0" block="block2" paramOrder="13" >
<type>String</type>
</param>

<param display="TX-PW Label" name="tx" default="16" block="block2" paramOrder="14" >
<type>String</type>
</param>

<param display="RX-PW Label" name="rx" default="16" block="block2" paramOrder="15" >
<type>String</type>
</param>

<substitution suppressAllIfParamsAreNull="true">add satop satop-1-1-2-__%addsatopID__ %e1 %e %payload %buffersize %rtp %control %discovery %ip %encapsulation %vlan %tx %rx</substitution>
</command>

<cli-command>back</cli-command>
<command>



<param display="entity" name="pwe316e1t1" default="pwe3_16e1t1" block="block2" paramOrder="16">
<type>Enum</type>
<token display="lag" literal="lag"/>
<token display="nte" literal="nte"/>
<token display="psu" literal="psu"/>
<token display="pwe3_16e1t1" literal="pwe3_16e1t1"/>
<token display="sync" literal="sync"/>
</param>

<param display="entity ID" name="pwe316e1t1112" default="pwe3_16e1t1-1-1-2" block="block2" paramOrder="17" >
<type>String</type>
</param>

<substitution suppressAllIfParamsAreNull="true">configure %pwe316e1t1 %pwe316e1t1112</substitution>

</command>


<command>

<param display="satop ID" name="satop" default="satop-1-1-2-1" block="block2" paramOrder="18" >
<type>String</type>
</param>
<substitution>configure satop %satop</substitution>

</command>


<command>

<param display="admin-state" name="adminstate" default="in-service" block="block2" paramOrder="19">
<type>Enum</type>
<token display="in-service" literal="in-service"/>
<token display="management" literal="management"/>
</param>

<substitution>admin-state %adminstate</substitution>

</command>

<cli-command>
#CLI:EBP-1-1-2-1  Edit

home
network-element ne-1
</cli-command>
<command>


<block display="EBP" name="block7" blockOrder="7"/>

<param display="entity" name="pwe316e1t1" default="pwe3_16e1t1" block="block7" paramOrder="1">
<type>Enum</type>
<token display="lag" literal="lag"/>
<token display="nte" literal="nte"/>
<token display="psu" literal="psu"/>
<token display="pwe3_16e1t1" literal="pwe3_16e1t1"/>
<token display="sync" literal="sync"/>
</param>

<param display="entity ID" name="pwe316e1t1112" default="pwe3_16e1t1-1-1-2" block="block7" paramOrder="2" >
<type>String</type>
</param>

<substitution suppressAllIfParamsAreNull="true">configure %pwe316e1t1 %pwe316e1t1112</substitution>

</command>

<command>

<param display="port type" name="selection" default="ebp-port" block="block7" paramOrder="3">
<type>Enum</type>
<token display="e1-port" literal="e1-port"/>
<token display="ebp-port" literal="ebp-port"/>
<token display="esa" literal="esa"/>
<token display="satop" literal="satop"/>
<token display="schedule" literal="schedule"/>
<token display="sync" literal="sync"/>
</param>

<param display="backplane port ID" name="backplaneport" default="ebp-1-1-2-1" block="block7" paramOrder="4" >
<type>String</type>
</param>

<substitution suppressAllIfParamsAreNull="true">configure %selection %backplaneport</substitution>

</command>

<command>

<param display="admin-state" name="adminstate" default="in-service" block="block7" paramOrder="5">
<type>Enum</type>
<token display="in-service" literal="in-service"/>
<token display="management" literal="management"/>
<token display="unassigned" literal="unassigned"/>
<token display="maintenance" literal="maintenance"/>

</param>

<substitution>admin-state %adminstate</substitution>

</command>

<cli-command>
#CLI:FLOW-1-1-2-1-1  Create
home
network-element ne-1
</cli-command>

<command>


<block display="EBP-FLOW" name="block8" blockOrder="8"/>

<param display="entity" name="pwe316e1t1" default="pwe3_16e1t1" block="block8" paramOrder="1">
<type>Enum</type>
<token display="lag" literal="lag"/>
<token display="nte" literal="nte"/>
<token display="psu" literal="psu"/>
<token display="pwe3_16e1t1" literal="pwe3_16e1t1"/>
<token display="sync" literal="sync"/>
</param>

<param display="entity id" name="pwe316e1t1112" default="pwe3_16e1t1-1-1-2" block="block8" paramOrder="2" >
<type>String</type>
</param>

<substitution suppressAllIfParamsAreNull="true">configure %pwe316e1t1 %pwe316e1t1112</substitution>

</command>

<command>

<param display="port type" name="selection" default="ebp-port" block="block8" paramOrder="3">
<type>Enum</type>
<token display="e1-port" literal="e1-port"/>
<token display="ebp-port" literal="ebp-port"/>
<token display="esa" literal="esa"/>
<token display="satop" literal="satop"/>
<token display="schedule" literal="schedule"/>
<token display="sync" literal="sync"/>
</param>

<param display="backplane port ID" name="backplaneport" default="ebp-1-1-2-1" block="block8" paramOrder="4" >
<type>String</type>
</param>

<substitution suppressAllIfParamsAreNull="true">configure %selection %backplaneport</substitution>

</command>



<command>

<param display="flow ID" name="ebpflow" default="1" block="block8" paramOrder="5" scope="global">
<type>Integer</type>
</param>

<param display="circuit name" name="circuit" default="2" block="block8" paramOrder="6" >
<type>String</type>
</param>

<param display="untagged frames" name="frames" default="disabled" block="block8" paramOrder="7" >
<type>Enum</type>
<token display="disabled" literal="disabled"/>
<token display="enabled" literal="enabled"/>
</param>


<param display="cos level" name="cos" default="7" block="block8" paramOrder="8" >
<type>String</type>
</param>

<param display="evc-vlan-list" name="evcvlan" default="16-*" block="block8" paramOrder="13" >
<type>String</type>
</param>

<param display="auto bandwidth" name="bandwidth" default="enabled" block="block8" paramOrder="14" >
<type>Enum</type>
<token display="disabled" literal="disabled"/>
<token display="enabled" literal="enabled"/>
</param>

<substitution suppressAllIfParamsAreNull="true">add flow flow-1-1-2-1-__%ebpflow__ %circuit %frames %cos none none %evcvlan %bandwidth</substitution>

</command>


<command>
<param display="FNM_TRAIL_SERVICE_END" name="fnm.trailServiceEnd" default="FLOW-1-1-2-1-1" block="block8" paramOrder="16" scope="global" >
<type>Composite</type>
<value>FLOW-1-1-2-1-__%ebpflow__</value>
</param>
<param display="FNM_SERVICE_END" name="fnm.serviceEnd" default="SATOP-1-1-2-1" block="block8" paramOrder="17" scope="global" >
<type>Composite</type>
<value>SATOP-1-1-2-__%addsatopID__</value>
</param>
</command>
<cli-command>home</cli-command>
</template>