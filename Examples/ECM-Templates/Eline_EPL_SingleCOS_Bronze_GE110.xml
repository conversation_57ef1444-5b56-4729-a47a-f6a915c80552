<template>
<header>
<neType>FSP 150CC-GE114, FSP 150CC-GE112, FSP 150CC-GE114S, FSP 150CC-GE114SH, FSP 150CC-GE114H, FSP 150CC-GE114PH, FSP 150-GE112Pro, FSP 150-GE112Pro-M, FSP 150-GE112Pro-H, FSP 150-GE114Pro, FSP 150-GE114Pro-C, FSP 150-GE114Pro-SH, FSP 150-GE114Pro-CSH, FSP 150-GE114Pro-HE, FSP 150-GE101Pro </neType>
<applyMode>Delta</applyMode>
<version>1.4</version>
<summary>Service template - EPL (SingleCOS) for GE110/GE110Pro versions</summary>
<category>Service Provisioning</category>
<comment>#</comment>
</header>
<cli-command># DO NOT EDIT THIS LINE. FILE_TYPE=CONFIGURATION_FILE</cli-command>

<command>
<block display="Port Configuration" name="block2" blockOrder="2"/>
<param display="ACC Port" name="accport" default="ACCESS PORT-1-1-1-3" block="block2" scope="global">
<type>Enum</type>
<token display="ACCESS PORT-1-1-1-3" literal="3"/>
<token display="ACCESS PORT-1-1-1-4" literal="4"/>
<token display="ACCESS PORT-1-1-1-5" literal="5" neType="FSP 150CC-GE114, FSP 150CC-GE114S, FSP 150CC-GE114SH, FSP 150CC-GE114H, FSP 150CC-GE114PH, FSP 150-GE114Pro, FSP 150-GE114Pro-C, FSP 150-GE114Pro-SH, FSP 150-GE114Pro-CSH, FSP 150-GE114Pro-HE"/>
<token display="ACCESS PORT-1-1-1-6" literal="6" neType="FSP 150CC-GE114, FSP 150CC-GE114S, FSP 150CC-GE114SH, FSP 150CC-GE114H, FSP 150CC-GE114PH, FSP 150-GE114Pro, FSP 150-GE114Pro-C, FSP 150-GE114Pro-SH, FSP 150-GE114Pro-CSH, FSP 150-GE114Pro-HE"/>
</param>
<param display="Admin State" name="adminstate" default="IS" copyFrom="adminstate" block="block2" scope="global" >
<type>Enum</type>
<token display="Disabled" literal="disabled"/>
<token display="IS" literal="in-service"/>
<token display="Maintenance" literal="maintenance"/>
<token display="Management" literal="management"/>
<token display="Unassigned" literal="unassigned"/>
</param>
<param display="Speed" name="speed" default="auto-1000-full" block="block2" scope="global">
<type>Enum</type>
<token display="100-full" literal="100-full"/>
<token display="1000-full" literal="1000-full"/>
<token display="auto-1000-full" literal="auto-1000-full"/>
<token display="auto-speed-detect" literal="auto-speed-detect"/>
</param>
</command>
<fragment block="block2" resolveGlobalParams="true">

#
#CLI:ACCESS PORT-1-1-1-__%accport__  Edit
#

home
network-element ne-1
<neType types="FSP 150CC-GE114">configure nte nte114-1-1-1</neType>
<neType types="FSP 150CC-GE112">configure nte nte112-1-1-1</neType>
<neType types="FSP 150CC-GE114S">configure nte nte114s-1-1-1</neType>
<neType types="FSP 150CC-GE114SH">configure nte nte114sh-1-1-1</neType>
<neType types="FSP 150CC-GE114H">configure nte nte114h-1-1-1</neType>
<neType types="FSP 150CC-GE114PH">configure nte nte114ph-1-1-1</neType>
<neType types="FSP 150-GE114Pro">configure nte nte114pro-1-1-1</neType>
<neType types="FSP 150-GE114Pro-C">configure nte nte114pro_c-1-1-1</neType>
<neType types="FSP 150-GE114Pro-SH">configure nte nte114pro_sh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-CSH">configure nte nte114pro_csh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-HE">configure nte nte114pro_he-1-1-1</neType>
<neType types="FSP 150-GE101Pro">configure nte nte101pro-1-1-1</neType>
configure access-port access-1-1-1-__%accport__
admin-state unassigned
speed %speed
</fragment>

<command>
<block display="Flow Configuration" name="FlowConfigurationBlock" blockOrder="4"/>
<param display="COS" name="cosid" default="COS-7" copyFrom="cosid" block="FlowConfigurationBlock" scope="global">
<type>Enum</type>
<token display="COS-0" literal="0"/>
<token display="COS-1" literal="1"/>
<token display="COS-2" literal="2"/>
<token display="COS-3" literal="3"/>
<token display="COS-4" literal="4"/>
<token display="COS-5" literal="5"/>
<token display="COS-6" literal="6"/>
<token display="COS-7" literal="7"/>
</param>
<param display="Service End Point" name="fnm.serviceEnd" default="FLOW-1-1-1-3-1" block="FlowConfigurationBlock" scope="global">
<type>Composite</type>
<value>FLOW-1-1-1-__%accport__-1</value>
</param>
</command>

<command>
<block display="Tag Configuration" name="TagConfigurationBlock" blockParent="FlowConfigurationBlock"/>
<param display="CTag Control" name="ctagcontrol" default="Push" block="TagConfigurationBlock" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Push" literal="push __%ctag__"/>
</param>
<param display="C-Tag - Priority" name="ctag" default="1-0" copyFrom="ctag" block="TagConfigurationBlock" optional="true" scope="global">
<type>String</type>
</param>
<param display="STag Control" name="stagcontrol" default="Push" block="TagConfigurationBlock" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Push" literal="push __%stag__"/>
</param>
<param display="S-Tag - Priority" name="stag" default="1-0" copyFrom="stag" block="TagConfigurationBlock" optional="true" scope="global">
<type>String</type>
</param>
</command>

<command>
<block display="Bandwith Configuration" name="bwpBlock" blockParent="FlowConfigurationBlock"/>
<param display="CIR (bps)" name="cir" default="64000" copyFrom="cir" block="bwpBlock" scope="global">
<type>Integer</type>
</param>
<param display="EIR (bps)" name="eir" default="64000" copyFrom="eir" block="bwpBlock" scope="global">
<type>Integer</type>
</param>
<param display="CBS (KB)" name="cbs" default="2" copyFrom="cbs" block="bwpBlock" scope="global">
<type>Integer</type>
</param>
<param display="EBS (KB)" name="ebs" default="2" copyFrom="ebs" block="bwpBlock" scope="global">
<type>Integer</type>
</param>
<param display="Buffer Size A2N (KB)" name="bufsizeA2N" copyFrom="bufsizeA2N" default="256" block="bwpBlock" scope="global">
<type>Integer</type>
</param>
<param display="Buffer Size N2A (KB)" name="bufsizeN2A" copyFrom="bufsizeN2A" default="800" block="bwpBlock" scope="global">
<type>Integer</type>
</param>
</command>

<fragment block="FlowConfigurationBlock" resolveGlobalParams="true">
#
#CLI:FLOW-1-1-1-__%accport__-1  Modify
#
home
network-element ne-1
<neType types="FSP 150CC-GE114">configure nte nte114-1-1-1</neType>
<neType types="FSP 150CC-GE112">configure nte nte112-1-1-1</neType>
<neType types="FSP 150CC-GE114S">configure nte nte114s-1-1-1</neType>
<neType types="FSP 150CC-GE114SH">configure nte nte114sh-1-1-1</neType>
<neType types="FSP 150CC-GE114H">configure nte nte114h-1-1-1</neType>
<neType types="FSP 150CC-GE114PH">configure nte nte114ph-1-1-1</neType>
<neType types="FSP 150-GE114Pro">configure nte nte114pro-1-1-1</neType>
<neType types="FSP 150-GE114Pro-C">configure nte nte114pro_c-1-1-1</neType>
<neType types="FSP 150-GE114Pro-SH">configure nte nte114pro_sh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-CSH">configure nte nte114pro_csh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-HE">configure nte nte114pro_he-1-1-1</neType>
<neType types="FSP 150-GE101Pro">configure nte nte101pro-1-1-1</neType>
configure access-port access-1-1-1-__%accport__
service-type epl
  
# SINGLE COS Invocation EPL 
configure flow flow-1-1-1-__%accport__-1
default-cos %cosid
<neType types="FSP 150CC-GE112, FSP 150CC-GE114">a2n-shaping-type flow-based</neType>
</fragment>




<fragment block="TagConfigurationBlock" resolveGlobalParams="true">
ctag %ctagcontrol
stag %stagcontrol
</fragment>

<fragment block="bwpBlock" resolveGlobalParams="true">
#
#CLI:A2N SHAPER-1-1-1-__%accport__-__1-0  Edit
#

home
network-element ne-1
<neType types="FSP 150CC-GE114">configure nte nte114-1-1-1</neType>
<neType types="FSP 150CC-GE112">configure nte nte112-1-1-1</neType>
<neType types="FSP 150CC-GE114S">configure nte nte114s-1-1-1</neType>
<neType types="FSP 150CC-GE114SH">configure nte nte114sh-1-1-1</neType>
<neType types="FSP 150CC-GE114H">configure nte nte114h-1-1-1</neType>
<neType types="FSP 150CC-GE114PH">configure nte nte114ph-1-1-1</neType>
<neType types="FSP 150-GE114Pro">configure nte nte114pro-1-1-1</neType>
<neType types="FSP 150-GE114Pro-C">configure nte nte114pro_c-1-1-1</neType>
<neType types="FSP 150-GE114Pro-SH">configure nte nte114pro_sh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-CSH">configure nte nte114pro_csh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-HE">configure nte nte114pro_he-1-1-1</neType>
<neType types="FSP 150-GE101Pro">configure nte nte101pro-1-1-1</neType>
<neType types="FSP 150CC-GE112, FSP 150CC-GE114">
configure access-port access-1-1-1-__%accport__
configure flow flow-1-1-1-__%accport__-1
configure a2n-shaper a2n_shaper-1-1-1-__%accport__-1-0
buffersize %bufsizeA2N
</neType>
<neType types="FSP 150CC-GE114S, FSP 150CC-GE114SH">
configure network-port network-1-1-1-1
configure a2n-shaper port_a2n_shaper-1-1-1-1-__%cosid__
buffersize %bufsizeA2N
</neType>



#CLI:A2N POLICER-1-1-1-__%accport__-1-0  Edit
home
network-element ne-1
<neType types="FSP 150CC-GE114">configure nte nte114-1-1-1</neType>
<neType types="FSP 150CC-GE112">configure nte nte112-1-1-1</neType>
<neType types="FSP 150CC-GE114S">configure nte nte114s-1-1-1</neType>
<neType types="FSP 150CC-GE114SH">configure nte nte114sh-1-1-1</neType>
<neType types="FSP 150CC-GE114H">configure nte nte114h-1-1-1</neType>
<neType types="FSP 150CC-GE114PH">configure nte nte114ph-1-1-1</neType>
<neType types="FSP 150-GE114Pro">configure nte nte114pro-1-1-1</neType>
<neType types="FSP 150-GE114Pro-C">configure nte nte114pro_c-1-1-1</neType>
<neType types="FSP 150-GE114Pro-SH">configure nte nte114pro_sh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-CSH">configure nte nte114pro_csh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-HE">configure nte nte114pro_he-1-1-1</neType>
<neType types="FSP 150-GE101Pro">configure nte nte101pro-1-1-1</neType>
configure access-port access-1-1-1-__%accport__
configure flow flow-1-1-1-__%accport__-1
configure a2n-policer a2n_policer-1-1-1-__%accport__-1-0  
cir %cir
cbs %cbs
eir %eir
ebs %ebs

#
#CLI:PORT N2A SHAPER-1-1-1-__%accport__-__%cosid__  Edit
#
home
network-element ne-1
<neType types="FSP 150CC-GE114">configure nte nte114-1-1-1</neType>
<neType types="FSP 150CC-GE112">configure nte nte112-1-1-1</neType>
<neType types="FSP 150CC-GE114S">configure nte nte114s-1-1-1</neType>
<neType types="FSP 150CC-GE114SH">configure nte nte114sh-1-1-1</neType>
<neType types="FSP 150CC-GE114H">configure nte nte114h-1-1-1</neType>
<neType types="FSP 150CC-GE114PH">configure nte nte114ph-1-1-1</neType>
<neType types="FSP 150-GE114Pro">configure nte nte114pro-1-1-1</neType>
<neType types="FSP 150-GE114Pro-C">configure nte nte114pro_c-1-1-1</neType>
<neType types="FSP 150-GE114Pro-SH">configure nte nte114pro_sh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-CSH">configure nte nte114pro_csh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-HE">configure nte nte114pro_he-1-1-1</neType>
<neType types="FSP 150-GE101Pro">configure nte nte101pro-1-1-1</neType>
configure access-port access-1-1-1-__%accport__
configure n2a-shaper port_n2a_shaper-1-1-1-__%accport__-__%cosid__
buffersize %bufsizeN2A

#
#CLI:ACCESS PORT-1-1-1-__%accport__  Edit
#
home
network-element ne-1
<neType types="FSP 150CC-GE114">configure nte nte114-1-1-1</neType>
<neType types="FSP 150CC-GE112">configure nte nte112-1-1-1</neType>
<neType types="FSP 150CC-GE114S">configure nte nte114s-1-1-1</neType>
<neType types="FSP 150CC-GE114SH">configure nte nte114sh-1-1-1</neType>
<neType types="FSP 150CC-GE114H">configure nte nte114h-1-1-1</neType>
<neType types="FSP 150CC-GE114PH">configure nte nte114ph-1-1-1</neType>
<neType types="FSP 150-GE114Pro">configure nte nte114pro-1-1-1</neType>
<neType types="FSP 150-GE114Pro-C">configure nte nte114pro_c-1-1-1</neType>
<neType types="FSP 150-GE114Pro-SH">configure nte nte114pro_sh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-CSH">configure nte nte114pro_csh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-HE">configure nte nte114pro_he-1-1-1</neType>
<neType types="FSP 150-GE101Pro">configure nte nte101pro-1-1-1</neType>
configure access-port access-1-1-1-__%accport__
admin-state %adminstate
#end
</fragment>

<command>
<block display="CFM Configuration" name="cfmBlock" blockOrder="5" blockOptionality="true" selected="false"/>
<param display="MD ID" name="mdID" block="cfmBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.md"/>
</default>
</param>
<param display="MD Name" name="mdName" default="MD-1" block="cfmBlock" scope="global">
<type>String</type>
</param>
<param display="Level" name="level" default="0" copyFrom="level" block="cfmBlock" scope="global">
<type>Enum</type>
<token display="0" literal="0"/>
<token display="1" literal="1"/>
<token display="2" literal="2"/>
<token display="3" literal="3"/>
<token display="4" literal="4"/>
<token display="5" literal="5"/>
<token display="6" literal="6"/>
<token display="7" literal="7"/>
</param>
<param display="MA ID" name="maID" block="cfmBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.ma">
		<arg>%mdID</arg>
	</function>
</default>
</param>
<param display="MA Name" name="maName" default="MA-1-1" block="cfmBlock" scope="global">
<type>String</type>
</param>
<param display="MEP ID List" name="mepIDList" default="1,3" block="cfmBlock" scope="global">
<type>String</type>
</param>
<param display="MEP ID" name="mepID" default="1" block="cfmBlock" scope="global">
<type>Integer</type>
</param>
<param display="Priamry VID" name="primaryVID" default="1" copyFrom="primaryVID" block="cfmBlock" scope="global">
<type>Integer</type>
</param>
</command>


<fragment block="cfmBlock" resolveGlobalParams="true">
#CFM Section
#
#CLI:CFM MD-__%mdID__  Create
#
home
configure cfm
add md %mdID string "%mdName" %level none
#
#CLI:CFM MANET-__%mdID__-__%maID__  Create
#
home
configure cfm
configure md md-__%mdID__
add manet %maID string "%maName" 1sec %mepIDList
#
#CLI:CFM MACOMP-__%mdID__-__%maID__-1  Create
#
home
configure cfm
configure md md-__%mdID__
configure manet manet-__%mdID__-__%maID__
add macomp 1 access-1-1-1-__%accport__ %primaryVID defer
add mep %mepID up access-1-1-1-__%accport__ in-service mac-remote-xcon-error disabled 0
#
#CLI:CFM N2A VID SHAPER-1-1-1-__%accport__-1  Edit
#
home
configure cfm
configure cfm-n2a-vid-shaper access-1-1-1-__%accport__ cfm_n2a_vid_shaper-1-1-1-__%accport__-1
admin-state in-service
</fragment>

<command>
<block display="ESA Y.1731 LM/DM Configuration" name="esaBlock" blockOrder="6" blockOptionality="true" selected="false"/>
<param display="ESA No." name="esaid" block="esaBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.esa">
		<arg>1</arg>
	</function>
</default>
</param>
<param display="Probe ID" name="probeID" default="ProbeY1731" block="esaBlock" scope="global">
<type>String</type>
<validate>
	<function name="uniqueName" object="fnm.db.esa">
		<arg>%probeID</arg>
	</function>
</validate>
</param>
<param display="Dst MEP ID" name="destMepID" default="3" block="esaBlock" scope="global">
<type>Integer</type>
</param>
<param display="Packet Interval" name="packetInterval" default="10sec" copyFrom="packetInterval"  block="esaBlock" scope="global">
<type>Enum</type>
<token display="1sec" literal="1sec"/>
<token display="10sec" literal="10sec"/>
<token display="1min" literal="1min"/>
</param>
<param display="Packet Size" name="packetSize" default="64" copyFrom="packetSize" block="esaBlock" scope="global">
<type>Integer</type>
</param>
<param display="Probe time interval for stats bin" name="timeInterval" default="15min" copyFrom="timeInterval" block="esaBlock" scope="global">
<type>Enum</type>
<token display="1min" literal="1min"/>
<token display="5min" literal="5min"/>
<token display="10min" literal="10min"/>
<token display="15min" literal="15min"/>
<token display="60min" literal="60min"/>
</param>
<param display="Probe number of stats history bins" name="historyBins" default="32" copyFrom="historyBins" block="esaBlock" scope="global">
<type>Integer</type>
</param>
<param display="Probe time interval for distribution stats bin" name="timeIntervalStatsBins" default="15min" copyFrom="timeIntervalStatsBins" block="esaBlock" scope="global">
<type>Enum</type>
<token display="1min" literal="1min"/>
<token display="5min" literal="5min"/>
<token display="10min" literal="10min"/>
<token display="15min" literal="15min"/>
<token display="60min" literal="60min"/>
</param>
<param display="Probe number of distribution stats history bins" name="distributionHistoryBins" default="32" copyFrom="distributionHistoryBins" block="esaBlock" scope="global">
<type>Integer</type>
</param>
</command>

<fragment block="esaBlock" resolveGlobalParams="true">
#ESA Section
#
#CLI:ESA Y.1731 LM/DM Create
#
home
network-element ne-1
<neType types="FSP 150CC-GE114">configure nte nte114-1-1-1</neType>
<neType types="FSP 150CC-GE112">configure nte nte112-1-1-1</neType>
<neType types="FSP 150CC-GE114S">configure nte nte114s-1-1-1</neType>
<neType types="FSP 150CC-GE114SH">configure nte nte114sh-1-1-1</neType>
<neType types="FSP 150CC-GE114H">configure nte nte114h-1-1-1</neType>
<neType types="FSP 150CC-GE114PH">configure nte nte114ph-1-1-1</neType>
<neType types="FSP 150-GE114Pro">configure nte nte114pro-1-1-1</neType>
<neType types="FSP 150-GE114Pro-C">configure nte nte114pro_c-1-1-1</neType>
<neType types="FSP 150-GE114Pro-SH">configure nte nte114pro_sh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-CSH">configure nte nte114pro_csh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-HE">configure nte nte114pro_he-1-1-1</neType>
<neType types="FSP 150-GE101Pro">configure nte nte101pro-1-1-1</neType>
configure esa
add probe esa_probe-1-1-1-__%esaid__ %probeID y1731-lm-dm access-1-1-1-__%accport__ mep-__%mdID__-__%maID__-__%mepID__ mep %destMepID disabled cos-__%cosid__ %packetInterval %packetSize %timeInterval %historyBins %timeIntervalStatsBins %distributionHistoryBins
</fragment>
</template>