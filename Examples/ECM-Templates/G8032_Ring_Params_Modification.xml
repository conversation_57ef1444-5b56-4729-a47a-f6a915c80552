<template>
<header>
<neType> FSP 150CC-XG210, FSP 150CC-GE206V, FSP 150EG-X, FSP3000-SH1PCS </neType>
<applyMode>Delta</applyMode>
<version>1.2</version>
<summary>G.8032 Ring Modification (Revertive, Guard Time, WTR Time, HO Time, Subring Virtual Channel, Ring Ports Roles)</summary>
<category>Service Provisioning</category>
<comment>#</comment>
</header>
<cli-command>
# DO NOT EDIT THIS LINE. FILE_TYPE=CONFIGURATION_FILE
</cli-command>

<command>
<block display="G.8032 ERP Configuration" name="RingConfBlock" />
<param display="ERP Instance ID" name="erpIndex" default="1" block="RingConfBlock" scope="global">
<type>Integer</type>
</param>
<param display="Revertive" name="revertive" copyFrom="revertive" conveyanceType="locked" block="RingConfBlock" optional="true" scope="global">
<type>Enum</type>
<token display="Enabled" literal="enabled"/>
<token display="Disabled" literal="disabled"/>
</param>
<param display="Guard Time (10-2000 ms)" name="guardTime"  copyFrom="guardTime" conveyanceType="locked" block="RingConfBlock" optional="true" scope="global">
<type>Integer</type>
</param>
<param display="Wait-to-Restore Time (1-12 minutes)" name="waitToRestoreTime"  copyFrom="waitToRestoreTime" conveyanceType="locked" block="RingConfBlock" optional="true" scope="global">
<type>Integer</type>
</param>
<param display="Hold-off Time (0-10000 ms)" name="holdOffTime" copyFrom="holdOffTime" conveyanceType="locked" block="RingConfBlock" optional="true" scope="global">
<type>Integer</type>
</param>
<param display="Sub-Ring without Virtual Channel" name="virtualChannel"  copyFrom="virtualChannel" conveyanceType="locked" block="RingConfBlock" optional="true" scope="global">
<type>Enum</type>
<token display="Enabled" literal="enabled"/>
<token display="Disabled" literal="disabled"/>
</param>
<param display="Ring Port 0 Role" name="ringPort0RoleGE" default="None" block="RingConfBlock" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Neighbor" literal="neighbor"/>
<token display="Owner" literal="owner"/>
</param>
<param display="Ring Port 1 Role" name="ringPort1RoleGE" default="None" block="RingConfBlock" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Neighbor" literal="neighbor"/>
<token display="Owner" literal="owner"/>
</param>
</command>
<command><substitution>home</substitution></command>
<command><substitution>ne ne-1</substitution></command>
<command><substitution>configure erp erp-1-__%erpIndex__</substitution></command>
<command><substitution suppressAllIfParamsAreNull="true">revertive %revertive</substitution></command>
<command><substitution suppressAllIfParamsAreNull="true">guard-time %guardTime</substitution></command>
<command><substitution suppressAllIfParamsAreNull="true">wtr-time %waitToRestoreTime</substitution></command>
<command><substitution suppressAllIfParamsAreNull="true">ho-time %holdOffTime</substitution></command>
<command><substitution suppressAllIfParamsAreNull="true">subring-without-vc %virtualChannel</substitution></command>
<command><substitution>ring-port0-role %ringPort0RoleGE</substitution></command>
<command><substitution>ring-port1-role %ringPort1RoleGE</substitution></command>
</template>