<template>
<header>
<neType> FSP 150CC-XG210, FSP 150CC-XG210C, FSP 150CC-GE206V, FSP 150CC-GE206,FSP 150CC-GE201, FSP 150CC-GE201SE, FSP 150CC-GE206F, FSP3000-SH1PCS</neType>
<applyMode>Delta</applyMode>
<version>1.4</version>
<summary>Service template - EVPL (COS Bronze). Suitable for Linear and ERP protected segments of the service on GE20x and XG210 device.</summary>
<category>Service Provisioning</category>
<comment>#</comment>
</header>
<cli-command>
# DO NOT EDIT THIS LINE. FILE_TYPE=CONFIGURATION_FILE
</cli-command>
<command>
<block display="Port Configuration" name="PortConfBlock" />
<param display="LC" name="lcIndex" default="Select card number" block="PortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="NTE-1" literal="1" neType="FSP 150CC-GE206V, FSP 150CC-GE206,FSP 150CC-GE201, FSP 150CC-GE201SE, FSP 150CC-GE206F"/>
<token display="LC-2" literal="2" neType="FSP 150CC-XG210, FSP 150CC-XG210C, FSP 150CC-GE206V, FSP3000-SH1PCS"/>
<token display="LC-3" literal="3" neType="FSP 150CC-XG210, FSP 150CC-XG210C, FSP 150CC-GE206V, FSP3000-SH1PCS"/>
</param>
<param display="Card Type" name="cardType" default="Select card type" block="PortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="NTE201" literal="nte nte201" neType="FSP 150CC-GE201"/>
<token display="NTE201SE" literal="nte nte201se" neType="FSP 150CC-GE201SE"/>
<token display="NTE206" literal="nte nte206" neType="FSP 150CC-GE206"/>
<token display="NTE206F" literal="nte nte206f" neType="FSP 150CC-GE206F"/>
<token display="NTE206V" literal="nte nte206v" neType="FSP 150CC-GE206V"/>
<token display="XG-1X-CC" literal="xg_1x_cc xg_1x_cc" neType="FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS"/>
<token display="XG-1S-CC" literal="xg_1s_cc xg_1s_cc" neType="FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS"/>
<token display="GE-8E-CC" literal="ge_8e_cc ge_8e_cc" neType="FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS"/>
<token display="GE-8S-CC" literal="ge_8s_cc ge_8s_cc" neType="FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS"/>
<token display="GE-8SC-CC" literal="ge_8sc_cc ge_8sc_cc" neType="FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS"/>
<token display="GE-4E-CC" literal="ge_4e_cc ge_4e_cc" neType="FSP 150CC-GE206V"/>
<token display="GE-4S-CC" literal="ge_4s_cc ge_4s_cc" neType="FSP 150CC-GE206V"/>
</param>
<param display="ACC Port" name="accport" default="Select port" block="PortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="ACCESS PORT-1" literal="1"/>
<token display="ACCESS PORT-2" literal="2" neType="FSP 150CC-GE206, FSP 150CC-GE206V, FSP 150CC-GE206F, FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS"/>
<token display="ACCESS PORT-3" literal="3" neType="FSP 150CC-GE206, FSP 150CC-GE206V, FSP 150CC-GE206F, FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS"/>
<token display="ACCESS PORT-4" literal="4" neType="FSP 150CC-GE206, FSP 150CC-GE206V, FSP 150CC-GE206F, FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS"/>
<token display="ACCESS PORT-5" literal="5" neType="FSP 150CC-GE206, FSP 150CC-GE206V, FSP 150CC-GE206F, FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS"/>
<token display="ACCESS PORT-6" literal="6" neType="FSP 150CC-GE206, FSP 150CC-GE206V, FSP 150CC-GE206F, FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS"/>
<token display="ACCESS PORT-7" literal="7" neType="FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS"/>
<token display="ACCESS PORT-8" literal="8" neType="FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS"/>
</param>
<param display="Speed" name="speed" default="auto-1000-full" block="PortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="100-full" literal="100-full"/>
<token display="1000-full" literal="1000-full"/>
<token display="auto-1000-full" literal="auto-1000-full"/>
<token display="auto-speed-detect" literal="auto-speed-detect"/>
</param>
<param display="Admin State" name="adminstate" default="IS" block="PortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="Disabled" literal="disabled"/>
<token display="IS" literal="in-service"/>
<token display="Maintenance" literal="maintenance"/>
<token display="Management" literal="management"/>
<token display="Unassigned" literal="unassigned"/>
</param>
</command>

<fragment block="PortConfBlock" resolveGlobalParams="true">
#
#CLI:ACCESS PORT-1-1-__%lcIndex__-__%accport__  Edit
#
home
network-element ne-1
configure __%cardType__-1-1-__%lcIndex__
configure access-port access-1-1-__%lcIndex__-__%accport__
#admin-state unassigned
speed %speed
</fragment>

<command>
<block display="Flow Configuration" name="FlowConfBlock" />
<param display="Flow No. (auto)" name="flowid" block="FlowConfBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.flow">
		<arg>%lcIndex</arg>
		<arg>%accport</arg>
	</function>
</default>
</param>
<param display="Network Interface" name="netInterface" copyFrom="netInterface" default="NETWORK PORT-1-1-1-1" block="FlowConfBlock" scope="global">
<type>Enum</type>
<token display="NETWORK PORT-1-1-1-1" literal="network-1-1-1-1"/>
<token display="NETWORK PORT-1-1-1-2" literal="network-1-1-1-2"/>
<token display="ERP-1-1" literal="erp-1-1" neType="FSP 150CC-GE206V,FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS"/>
<token display="ERP-1-2" literal="erp-1-2" neType="FSP 150CC-GE206V,FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS"/>
<token display="LAG-1-1" literal="lag-1-1"/>
<token display="LAG-1-2" literal="lag-1-2" neType="FSP 150CC-GE206, FSP 150CC-GE206V, FSP 150CC-GE206F, FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS"/>
<token display="LAG-1-3" literal="lag-1-3" neType="FSP 150CC-GE206, FSP 150CC-GE206V, FSP 150CC-GE206F, FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS"/>
<token display="LAG-1-4" literal="lag-1-4" neType="FSP 150CC-GE206, FSP 150CC-GE206V, FSP 150CC-GE206F, FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS"/>
</param>
<param display="CTag Control" name="ctagcontrol" default="Push" block="FlowConfBlock" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Push" literal="push __%ctag__"/>
<token display="Push-vid" literal="push-vid __%ctag__"/>
<token display="Swap-vid" literal="swap-vid __%ctag__"/>
<token display="Pop" literal="pop __%ctag__"/>
</param>
<param display="C-Tag - Priority" name="ctag" default="1-0" copyFrom="ctag" block="FlowConfBlock" optional="true" scope="global">
<type>String</type>
</param>
<param display="STag Control" name="stagcontrol" default="Push" block="FlowConfBlock" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Push" literal="push __%stag__"/>
</param>
<param display="S-Tag - Priority" name="stag" default="1-0" copyFrom="stag" block="FlowConfBlock" optional="true" scope="global">
<type>String</type>
</param>
<param display="Vlan Memeber" name="vlanmember" default="1-*" copyFrom="vlanmember" block="FlowConfBlock" scope="global">
<type>String</type>
</param>

<neType types="FSP 150CC-GE206V, FSP 150CC-XG210, FSP 150CC-XG210C, FSP3000-SH1PCS">
<param display="Service End Point" name="fnm.serviceEnd" default="FLOW-1-1-1-1-1" block="FlowConfBlock" scope="global">
<type>Composite</type>
<value>FLOW-1-1-__%lcIndex__-__%accport__-__%flowid__</value>
</param>
</neType>

<neType types="FSP 150CC-GE201, FSP 150CC-GE201SE, FSP 150CC-GE206, FSP 150CC-GE206F">
<param display="Service End Point" name="fnm.serviceEnd" default="FLOW-1-1-1-1-1" block="FlowConfBlock" scope="global">
<type>Composite</type>
<value>Flow 1-__%lcIndex__-__%accport__-__%flowid__</value>
</param>
</neType>
</command>

<command>
<block display="BW Configuration" name="CosBlock" blockParent="FlowConfBlock"/>
<param display="CIR (bps)" name="cir" default="64000" copyFrom="cir"  block="CosBlock" scope="global">
<type>Integer</type>
</param>
<param display="EIR (bps)" name="eir" default="64000" copyFrom="eir" block="CosBlock" scope="global">
<type>Integer</type>
</param>
<param display="Buffer Size (KB)" name="bufsize" default="256" copyFrom="bufsize" block="CosBlock" scope="global">
<type>Integer</type>
</param>
</command>

<fragment block="FlowConfBlock" resolveGlobalParams="true">
#
#CLI:FLOW-1-1-__%lcIndex__-__%accport__-__%flowid__
#
home
network-element ne-1
configure __%cardType__-1-1-__%lcIndex__ 
configure access-port access-1-1-__%lcIndex__-__%accport__
service-type evpl
add flow flow-1-1-__%lcIndex__-__%accport__-__%flowid__ "" regular-evc disabled disabled disabled disabled disabled __%ctagcontrol__ __%stagcontrol__ "__%vlanmember__" 64000 64000 access-interface access-1-1-__%lcIndex__-__%accport__ network-interface __%netInterface__
</fragment>

<fragment block="CosBlock" resolveGlobalParams="true">
#
#CLI:A2N SHAPER-1-1-__%lcIndex__-__%accport__-__%flowid__-0  Edit
#
home
network-element ne-1
configure __%cardType__-1-1-__%lcIndex__ 
configure access-port access-1-1-__%lcIndex__-__%accport__
configure flow flow-1-1-__%lcIndex__-__%accport__-__%flowid__
configure a2n-shaper a2n_shaper-1-1-__%lcIndex__-__%accport__-__%flowid__-0
buffersize %bufsize

#
#CLI:PORT N2A SHAPER-1-1-__%lcIndex__-__%accport__-0  Edit
#
home
network-element ne-1
configure __%cardType__-1-1-__%lcIndex__ 
configure access-port access-1-1-__%lcIndex__-__%accport__
configure n2a-shaper port_n2a_shaper-1-1-__%lcIndex__-__%accport__-0
buffersize 800


#
#CLI:A2N POLICER-1-1-__%lcIndex__-__%accport__-__%flowid__-0  Edit
#
home
network-element ne-1
configure __%cardType__-1-1-__%lcIndex__ 
configure access-port access-1-1-__%lcIndex__-__%accport__
configure flow flow-1-1-__%lcIndex__-__%accport__-__%flowid__
configure a2n-policer a2n_policer-1-1-__%lcIndex__-__%accport__-__%flowid__-0
cir %cir
eir %eir
cbs 32
ebs 1



#
#CLI:ACCESS PORT-1-1-__%lcIndex__-__%accport__  Edit
#
home
network-element ne-1
configure __%cardType__-1-1-__%lcIndex__ 
configure access-port access-1-1-__%lcIndex__-__%accport__
admin-state %adminstate
</fragment>

<command>
<block display="CFM Configuration" name="cfmBlock" blockOptionality="true" selected="false" expanded="false"/>
<param display="MD ID (auto)" name="mdID" block="cfmBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.md"/>
</default>
</param>
<param display="MD Name" name="mdName" default="MD-1" copyFrom="mdName" block="cfmBlock" scope="global">
<type>String</type>
</param>
<param display="Level" name="level" default="0" copyFrom="level" block="cfmBlock" scope="global">
<type>Enum</type>
<token display="0" literal="0"/>
<token display="1" literal="1"/>
<token display="2" literal="2"/>
<token display="3" literal="3"/>
<token display="4" literal="4"/>
<token display="5" literal="5"/>
<token display="6" literal="6"/>
<token display="7" literal="7"/>
</param>
<param display="MA ID (auto)" name="maID" block="cfmBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.ma">
		<arg>%mdID</arg>
	</function>
</default>
</param>
<param display="MA Name" name="maName" default="MA-1-1" copyFrom="maName" block="cfmBlock" scope="global">
<type>String</type>
</param>
<param display="MEP ID List" name="mepIDList" default="1,3" copyFrom="mepIDList" block="cfmBlock" scope="global">
<type>String</type>
</param>
<param display="MEP ID" name="mepID" default="1" block="cfmBlock" scope="global">
<type>Integer</type>
</param>
<param display="Priamry VID" name="primaryVID" default="1" copyFrom="primaryVID" block="cfmBlock" scope="global">
<type>Integer</type>
</param>
</command>


<fragment block="cfmBlock" resolveGlobalParams="true">
#CFM Section
#
#CLI:CFM MD-__%mdID__  Create
#
home
configure cfm
add md %mdID string "%mdName" %level none
#
#CLI:CFM MANET-__%mdID__-__%maID__  Create
#
home
configure cfm
configure md md-__%mdID__
add manet %maID string "%maName" 1sec %mepIDList
#
#CLI:CFM MACOMP-__%mdID__-__%maID__-1  Create
#
home
configure cfm
configure md md-__%mdID__
configure manet manet-__%mdID__-__%maID__
add macomp 1 access-1-1-__%lcIndex__-__%accport__ %primaryVID defer
add mep %mepID up access-1-1-__%lcIndex__-__%accport__ in-service mac-remote-xcon-error enabled 0
#
#CLI:CFM N2A VID SHAPER-1-1-__%lcIndex__-__%accport__-1  Edit
#
home
configure cfm
configure cfm-n2a-vid-shaper access-1-1-__%lcIndex__-__%accport__ cfm_n2a_vid_shaper-1-1-__%lcIndex__-__%accport__-1
admin-state %adminstate
</fragment>

<command>
<block display="ESA Y.1731 LM/DM Configuration" name="esaBlock" blockOptionality="true" selected="false" expanded="false"/>
<param display="ESA No. (auto)" name="esaid" block="esaBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.esa">
		<arg>%lcIndex</arg>
	</function>
</default>
</param>
<param display="Probe ID" name="probeID" default="ProbeY1731" block="esaBlock" scope="global">
<type>String</type>
<validate>
	<function name="uniqueName" object="fnm.db.esa">
		<arg>%probeID</arg>
	</function>
</validate>
</param>
<param display="Dst MEP ID" name="destMepID" default="3" block="esaBlock" scope="global">
<type>Integer</type>
</param>
<param display="Packet Interval" name="packetInterval" default="10sec" copyFrom="packetInterval"  block="esaBlock" scope="global">
<type>Enum</type>
<token display="1sec" literal="1sec"/>
<token display="10sec" literal="10sec"/>
<token display="1min" literal="1min"/>
</param>
<param display="Packet Size" name="packetSize" default="64" copyFrom="packetSize" block="esaBlock" scope="global">
<type>Integer</type>
</param>
<param display="Probe time interval for stats bin" name="timeInterval" default="15min" copyFrom="timeInterval" block="esaBlock" scope="global">
<type>Enum</type>
<token display="1min" literal="1min"/>
<token display="5min" literal="5min"/>
<token display="10min" literal="10min"/>
<token display="15min" literal="15min"/>
<token display="60min" literal="60min"/>
</param>
<param display="Probe number of stats history bins" name="historyBins" default="32" copyFrom="historyBins" block="esaBlock" scope="global">
<type>Integer</type>
</param>
<param display="Probe time interval for distribution stats bin" name="timeIntervalStatsBins" default="15min" copyFrom="timeIntervalStatsBins" block="esaBlock" scope="global">
<type>Enum</type>
<token display="1min" literal="1min"/>
<token display="5min" literal="5min"/>
<token display="10min" literal="10min"/>
<token display="15min" literal="15min"/>
<token display="60min" literal="60min"/>
</param>
<param display="Probe number of distribution stats history bins" name="distributionHistoryBins" default="32" copyFrom="distributionHistoryBins" block="esaBlock" scope="global">
<type>Integer</type>
</param>
</command>

<fragment block="esaBlock" resolveGlobalParams="true">
#ESA Section
#
#CLI:ESA Y.1731 LM/DM Create
#
home
network-element ne-1
configure __%cardType__-1-1-__%lcIndex__
configure esa
add probe esa_probe-1-1-__%lcIndex__-__%esaid__ %probeID y1731-lm-dm access-1-1-__%lcIndex__-__%accport__ mep-__%mdID__-__%maID__-__%mepID__ mep %destMepID disabled cos-0 %packetInterval %packetSize %timeInterval %historyBins %timeIntervalStatsBins %distributionHistoryBins
</fragment>
</template>