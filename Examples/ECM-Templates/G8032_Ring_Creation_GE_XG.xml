<template>
<header>
<neType>FSP 150CC-XG210, FSP 150CC-XG210C, FSP 150CC-GE206V, FSP3000-SH1PCS, FSP 150-XG116Pro</neType>
<applyMode>Delta</applyMode>
<version>1.4</version>
<summary>G.8032 Ring Creation: ERP, Link CFM, PT Flow (Default, SingleCOS, Auto Bandwidth Enabled)</summary>
<category>Service Provisioning</category>
<comment>#</comment>
</header>
<cli-command>
# DO NOT EDIT THIS LINE. FILE_TYPE=CONFIGURATION_FILE
</cli-command>

<command>
<block display="G.8032 ERP Configuration" name="RingConfBlock" />
<param display="ERP Instance" name="erpIndex" block="RingConfBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.erp"/>
</default>
</param>
<param display="R-APS Tag Vlan (e.g. 4094)" name="rapsTagID" default="4094" copyFrom="rapsTagID" conveyanceType="locked" block="RingConfBlock" scope="global">
<type>Integer</type>
</param>
<param display="R-APS Tag COS Level" name="rapsCosID" default="7" copyFrom="rapsCosID" block="RingConfBlock" scope="global">
<type>Enum</type>
<token display="0" literal="0"/>
<token display="1" literal="1"/>
<token display="2" literal="2"/>
<token display="3" literal="3"/>
<token display="4" literal="4"/>
<token display="5" literal="5"/>
<token display="6" literal="6"/>
<token display="7" literal="7"/>
</param>
<param display="Ring ID" name="ringID" default="1" copyFrom="ringID" conveyanceType="locked" block="RingConfBlock" scope="global">
<type>Integer</type>
</param>
<param display="R-APS MD Level" name="rapsMDLevel" default="0" copyFrom="rapsMDLevel"  block="RingConfBlock" scope="global">
<type>Enum</type>
<token display="0" literal="0"/>
<token display="1" literal="1"/>
<token display="2" literal="2"/>
<token display="3" literal="3"/>
<token display="4" literal="4"/>
<token display="5" literal="5"/>
<token display="6" literal="6"/>
<token display="7" literal="7"/>
</param>
<param display="Alias" name="alias" default="MyRing" copyFrom="alias" block="RingConfBlock" scope="global">
<type>String</type>
</param>
</command>

<command>
<block display="RPL Configuration" name="PortConfBlock"/>
<param display="Ring Port 0" name="ringPort0GE" default="NETWORK PORT-1-1-1-1" block="PortConfBlock" scope="global">
<type>Enum</type>
<token display="NETWORK PORT-1-1-1-1" literal="network-1-1-1-1" neType="FSP 150CC-XG210, FSP 150CC-XG210C, FSP 150CC-GE206V, FSP3000-SH1PCS"/>
<token display="NETWORK PORT-1-1-1-2" literal="network-1-1-1-2" neType="FSP 150CC-XG210, FSP 150CC-XG210C, FSP 150CC-GE206V, FSP3000-SH1PCS"/>
<token display="ETH PORT-1-1-1-1" literal="eth_port-1-1-1-1" neType="FSP 150-XG116Pro"/>
<token display="ETH PORT-1-1-1-2" literal="eth_port-1-1-1-2" neType="FSP 150-XG116Pro"/>
<token display="ETH PORT-1-1-1-3" literal="eth_port-1-1-1-3" neType="FSP 150-XG116Pro"/>
<token display="ETH PORT-1-1-1-4" literal="eth_port-1-1-1-4" neType="FSP 150-XG116Pro"/>
<token display="ETH PORT-1-1-1-5" literal="eth_port-1-1-1-5" neType="FSP 150-XG116Pro"/>
<token display="ETH PORT-1-1-1-6" literal="eth_port-1-1-1-6" neType="FSP 150-XG116Pro"/>
<token display="ETH PORT-1-1-1-7" literal="eth_port-1-1-1-7" neType="FSP 150-XG116Pro"/>
<token display="ETH PORT-1-1-1-8" literal="eth_port-1-1-1-8" neType="FSP 150-XG116Pro"/>
</param>

<param display="Ring Port 0 Role" name="ringPort0RoleGE" default="None" block="PortConfBlock" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Neighbor" literal="neighbor"/>
<token display="Owner" literal="owner"/>
</param>


<param display="Ring Port 1" name="ringPort1GE" block="PortConfBlock" scope="global">
<type>Enum</type>
<token display="NETWORK PORT-1-1-1-1" literal="network-1-1-1-1" neType="FSP 150CC-XG210, FSP 150CC-XG210C, FSP 150CC-GE206V, FSP3000-SH1PCS"/>
<token display="NETWORK PORT-1-1-1-2" literal="network-1-1-1-2" neType="FSP 150CC-XG210, FSP 150CC-XG210C, FSP 150CC-GE206V, FSP3000-SH1PCS"/>
<token display="ETH PORT-1-1-1-1" literal="eth_port-1-1-1-1" neType="FSP 150-XG116Pro"/>
<token display="ETH PORT-1-1-1-2" literal="eth_port-1-1-1-2" neType="FSP 150-XG116Pro"/>
<token display="ETH PORT-1-1-1-3" literal="eth_port-1-1-1-3" neType="FSP 150-XG116Pro"/>
<token display="ETH PORT-1-1-1-4" literal="eth_port-1-1-1-4" neType="FSP 150-XG116Pro"/>
<token display="ETH PORT-1-1-1-5" literal="eth_port-1-1-1-5" neType="FSP 150-XG116Pro"/>
<token display="ETH PORT-1-1-1-6" literal="eth_port-1-1-1-6" neType="FSP 150-XG116Pro"/>
<token display="ETH PORT-1-1-1-7" literal="eth_port-1-1-1-7" neType="FSP 150-XG116Pro"/>
<token display="ETH PORT-1-1-1-8" literal="eth_port-1-1-1-8" neType="FSP 150-XG116Pro"/>
</param>

<param display="Ring Port 1 Role" name="ringPort1RoleGE" default="None" block="PortConfBlock" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Neighbor" literal="neighbor"/>
<token display="Owner" literal="owner"/>
</param>
<param display="G.8032 Ring ETrail" name="fnm.erp.trailServiceEnd" default="ERP-1-1" block="PortConfBlock" scope="global">
<type>Composite</type>
<value>ERP-1-__%erpIndex__</value>
</param>
</command>

<fragment block="PortConfBlock" resolveGlobalParams="true">
home
ne ne-1
add erp erp-1-__%erpIndex__ __%rapsTagID__-__%rapsCosID__ %ringID %rapsMDLevel ring-port-0 %ringPort0GE ring-port-1 %ringPort1GE
configure erp erp-1-__%erpIndex__
user-label %alias
ring-port0-role %ringPort0RoleGE
ring-port1-role %ringPort1RoleGE
</fragment>

<command>
<block display="PT Flow Configuration" name="PTFlowBlock"/>
<neType types="FSP 150CC-XG210, FSP 150CC-XG210C, FSP 150CC-GE206V, FSP3000-SH1PCS">
<param display="PT Flow ID" name="ptFlowIndex" block="PTFlowBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.flow">
		<arg>255</arg>
		<arg>0</arg>
	</function>
</default>
</param>
<param display="Alias" name="ptFlowAlias" default="myPTFlow" block="PTFlowBlock" scope="global">
<type>String</type>
</param>
<param display="Untagged Frames" name="ptFlowUntaggedFrames" default="Disabled" copyFrom="ptFlowUntaggedFrames"  block="PTFlowBlock" scope="global">
<type>Enum</type>
<token display="Disabled" literal="disabled"/>
<token display="Enabled" literal="enabled"/>
</param>
<param display="COS" name="ptFlowCOS" default="0" copyFrom="ptFlowCOS" block="PTFlowBlock" scope="global">
<type>Enum</type>
<token display="0" literal="0"/>
<token display="1" literal="1"/>
<token display="2" literal="2"/>
<token display="3" literal="3"/>
<token display="4" literal="4"/>
<token display="5" literal="5"/>
<token display="6" literal="6"/>
<token display="7" literal="7"/>
</param>
<param display="Auto CIR Bandwidth Percent (%)" name="ptFlowAutoBW" default="80" copyFrom="ptFlowAutoBW" block="PTFlowBlock" scope="global">
<type>Integer</type>
</param>
</neType>
</command>

<fragment block="PTFlowBlock" resolveGlobalParams="true">
<neType types="FSP 150CC-XG210, FSP 150CC-XG210C, FSP 150CC-GE206V, FSP3000-SH1PCS">
home
ne ne-1
add passthru-flow pt_flow-1-__%ptFlowIndex__ %ptFlowAlias default-evc %ptFlowUntaggedFrames disabled %ptFlowCOS 200000 200000
configure passthru-flow pt_flow-1-__%ptFlowIndex__
auto-bw-config enabled
auto-cir-bw-percent %ptFlowAutoBW
loop-avoidance erp-1-__%erpIndex__
</neType>
</fragment>

<command>
<block display="CFM Configuration" name="cfmBlock" blockOptionality="true" selected="false"/>
<param display="MD ID" name="mdID" block="cfmBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.md"/>
</default>
</param>
<param display="MD Name" name="mdName" default="MD-1" block="cfmBlock" scope="global">
<type>String</type>
</param>
<param display="Ring Port 0 MEP ID" name="ringPort0MEPID" default="1" block="cfmBlock" scope="global">
<type>Enum</type>
<token display="1" literal="1"/>
<token display="2" literal="2"/>
</param>
<param display="Ring Port 1 MEP ID" name="ringPort1MEPID" default="1" block="cfmBlock" scope="global">
<type>Enum</type>
<token display="1" literal="1"/>
<token display="2" literal="2"/>
</param>
</command>

<fragment block="cfmBlock" resolveGlobalParams="true">
#CFM Section
#
#CLI:CFM MD-__%mdID__  Create
#
home
configure cfm
add md %mdID string "%mdName" %rapsMDLevel none
#
#CLI:CFM MANET-__%mdID__-1  Create
#CLI:CFM MANET-__%mdID__-2  Create
#
home
configure cfm
configure md md-__%mdID__
add manet 1 string "MA-__%mdID__-1" 3.3ms 1,2 
add manet 2 string "MA-__%mdID__-2" 3.3ms 1,2
#
#CLI:CFM MACOMP-__%mdID__-1-1  Create
#
home
configure cfm
configure md md-__%mdID__
configure manet manet-__%mdID__-1
add macomp 1 %ringPort0GE %rapsTagID defer
add mep %ringPort0MEPID down %ringPort0GE in-service all-defined enabled 0

#
#CLI:CFM MACOMP-__%mdID__-2-1  Create
#
home
configure cfm
configure md md-__%mdID__
configure manet manet-__%mdID__-2
add macomp 1 %ringPort1GE %rapsTagID defer
add mep %ringPort1MEPID down %ringPort1GE in-service all-defined enabled 0


#
#CLI:ADD CFM to RING ERP-1-__%erpIndex__
#
home
ne ne-1
configure erp erp-1-__%erpIndex__
chg-ring-ports ring-port-0 %ringPort0GE mep-__%mdID__-1-__%ringPort0MEPID__ ring-port-1 %ringPort1GE mep-__%mdID__-2-__%ringPort1MEPID__
</fragment>
</template>
