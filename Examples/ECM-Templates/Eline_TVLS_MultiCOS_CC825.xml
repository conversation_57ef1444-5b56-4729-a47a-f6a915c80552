<template>
<header>
<neType>FSP 150CCf-825 </neType>
<applyMode>Delta</applyMode>
<version>1.2</version>
<summary>Service template - EVPL (MultiCOS). Suitable for Linear service.</summary>
<category>Service Provisioning</category>
<comment>remark #</comment>
</header>
<cli-command>remark # DO NOT EDIT THIS LINE. FILE_TYPE=RUNNING_CONFIG </cli-command>

<command>
<block display="Port Configuration" name="portConfBlock" blockOrder="2"/>
<param display="LAN Port" name="lanPort" default="E100[LAN-1]" block="portConfBlock" scope="global">
<type>Enum</type>
<token display="E100[LAN-1]" literal="1"/>
<token display="E100[LAN-2]" literal="2"/>
<token display="E100[LAN-3]" literal="3"/>
<token display="E100[LAN-4]" literal="4"/>
<!--token display="E1000[LAN-5]" literal="e1000-lan-5"/-->
</param>
<param display="Circuit Name" name="portCircuitName" default="G30-1105-0224" copyFrom="circuitName" block="portConfBlock" optional="true" scope="global">
<type>String</type>
</param>
<param display="Admin State" name="adminstate" default="Enabled" copyFrom="adminstate" block="portConfBlock" scope="global" >
<type>Enum</type>
<token display="Enabled" literal="enable"/>
<token display="Disabled" literal="disable"/>
<token display="Testing" literal="testing"/>
</param>
<param display="Speed" name="speed" default="Auto" block="portConfBlock" scope="global">
<type>Enum</type>
<token display="Auto" literal="auto"/>
<token display="Auto 100 Mb Full Duplex" literal="auto100full"/>
<token display="Auto 100 Mb Half Duplex" literal="auto100half"/>
<token display="100 Mb Full Duplex" literal="100full"/>
<token display="100 Mb Half Duplex" literal="100half"/>
<token display="Auto 10 Mb Full Duplex" literal="auto10full"/>
<token display="Auto 10 Mb Half Duplex" literal="auto10half"/>
<token display="10 Mb Full Duplex" literal="10full"/>
<token display="10 Mb Half Duplex" literal="10half"/>
</param>
<!--param display="Priority Map Mode" name="prioMapMode" default="802.1P" block="cTagConfigurationBlock" scope="global">
<type>Enum</type>
<token display="802.1P" literal="8021p"/>
<token display="TOS" literal="tos"/>
<token display="DSCP" literal="dscp"/>
</param-->
</command>

<fragment block="portConfBlock" resolveGlobalParams="true">
configure interface e100-lan-__%lanPort__
assignstate disable
speed %speed
circuit-name "__%portCircuitName__"
</fragment>
<command>
<block display="Flow Configuration" name="FlowConfigurationBlock" blockOrder="4"/>
<param display="Flow ID" name="flowID" default="1" block="FlowConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<param display="Circuit Name" name="circuitName" default="G30-1105-0224" copyFrom="circuitName" block="FlowConfigurationBlock" optional="true" scope="global">
<type>String</type>
</param>
<block display="CTag Configuration" name="cTagConfigurationBlock" blockParent="FlowConfigurationBlock" blockOptionality="true" selected="true" expanded="true"/>
<param display="CTag Control" name="ctagcontrol" default="Push" block="cTagConfigurationBlock" scope="global">
<type>Enum</type>
<token display="Push" literal="push"/>
<!--token display="None" literal="none"/-->
</param>
<param display="C-Tag - Priority" name="ctag" default="400-0" copyFrom="ctag" block="cTagConfigurationBlock" optional="true" scope="global">
<type>String</type>
</param>
<block display="COS #1 Configuration" name="cos0ConfigurationBlock" blockParent="FlowConfigurationBlock" selected="true" expanded="true"/>
<param display="COS #1" name="cos0id" default="COS-0" copyFrom="cos0id" block="cos0ConfigurationBlock" scope="global">
<type>Enum</type>
<token display="COS-0" literal="0"/>
<!--token display="COS-1" literal="1"/>
<token display="COS-2" literal="2"/>
<token display="COS-3" literal="3"/>
<token display="COS-4" literal="4"/>
<token display="COS-5" literal="5"/>
<token display="COS-6" literal="6"/>
<token display="COS-7" literal="7"/-->
</param>
<param display="CIR (bps)" name="cos0cir" default="0" copyFrom="cos0cir" block="cos0ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<param display="EIR (bps)" name="cos0eir" default="31436800" copyFrom="cos0eir" block="cos0ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<param display="CBS (KB)" name="cos0cbs" default="168" copyFrom="cos0cbs" block="cos0ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<param display="EBS (KB)" name="cos0ebs" default="168" copyFrom="cos0ebs" block="cos0ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<param display="BS A2N (KB)" name="cos0shaperA2N" default="172" copyFrom="cos0shaperA2N" block="cos0ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<param display="BS N2A (KB)" name="cos0shaperN2A" default="800" copyFrom="cos0shaperN2A" block="cos0ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
</command>
<command>
<block display="COS #2 Configuration" name="cos1ConfigurationBlock" blockParent="FlowConfigurationBlock" blockOptionality="true" selected="true" expanded="true"/>
<param display="COS #2" name="cos1id" default="COS-6" copyFrom="cos1id" block="cos1ConfigurationBlock" scope="global">
<type>Enum</type>
<token display="COS-0" literal="0"/>
<token display="COS-1" literal="1"/>
<token display="COS-2" literal="2"/>
<token display="COS-3" literal="3"/>
<token display="COS-4" literal="4"/>
<token display="COS-5" literal="5"/>
<token display="COS-6" literal="6"/>
<token display="COS-7" literal="7"/>
</param>
<param display="CIR (bps)" name="cos1cir" default="3148800" copyFrom="cos1cir" block="cos1ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<param display="EIR (bps)" name="cos1eir" default="28288000" copyFrom="cos1eir" block="cos1ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<param display="CBS (KB)" name="cos1cbs" default="168" copyFrom="cos1cbs" block="cos1ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<param display="EBS (KB)" name="cos1ebs" default="168" copyFrom="cos1ebs" block="cos1ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<param display="BS A2N (KB)" name="cos1shaperA2N" default="172" copyFrom="cos1shaperA2N" block="cos1ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<param display="BS N2A (KB)" name="cos1shaperN2A" default="800" copyFrom="cos1shaperN2A" block="cos1ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<block display="COS #3 Configuration" name="cos2ConfigurationBlock" blockParent="FlowConfigurationBlock" blockOptionality="true" selected="true" expanded="true"/>
<param display="COS #3" name="cos2id" default="COS-7" copyFrom="cos2id" block="cos2ConfigurationBlock" scope="global">
<type>Enum</type>
<token display="COS-0" literal="0"/>
<token display="COS-1" literal="1"/>
<token display="COS-2" literal="2"/>
<token display="COS-3" literal="3"/>
<token display="COS-4" literal="4"/>
<token display="COS-5" literal="5"/>
<token display="COS-6" literal="6"/>
<token display="COS-7" literal="7"/>
</param>
<param display="CIR (bps)" name="cos2cir" default="6297600" copyFrom="cos2cir" block="cos2ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<param display="EIR (bps)" name="cos2eir" default="0" copyFrom="cos2eir" block="cos2ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<param display="CBS (KB)" name="cos2cbs" default="10" copyFrom="cos2cbs" block="cos2ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<param display="EBS (KB)" name="cos2ebs" default="0" copyFrom="cos2ebs" block="cos2ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<param display="BS A2N (KB)" name="cos2shaperA2N" default="12" copyFrom="cos2shaperA2N" block="cos2ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<param display="BS N2A (KB)" name="cos2shaperN2A" default="800" copyFrom="cos2shaperN2A" block="cos2ConfigurationBlock" scope="global">
<type>Integer</type>
</param>
<param display="Vlan Member" name="vlanMember" default="12-*" block="FlowConfigurationBlock" scope="global">
<type>String</type>
</param>
<param display="Service End Point" name="fnm.serviceEnd" default="Flow 1-1" block="FlowConfigurationBlock" scope="global">
<type>Composite</type>
<value>Flow __%lanPort__-__%flowID__</value>
</param>
</command>


<fragment block="FlowConfigurationBlock" resolveGlobalParams="true">
home
configure interface e100-lan-__%lanPort__
portmode Connection-oriented
evc-mode multi-flow
cos-mode 8021p
ed-cosmap 0 5 none swap 1 0
ed-cosmap 1 5 none swap 1 1
ed-cosmap 2 5 none swap 1 2
ed-cosmap 3 6 none swap 2 3
ed-cosmap 4 5 none swap 1 4
ed-cosmap 5 7 none swap 5 5
ed-cosmap 6 6 none swap 2 6
ed-cosmap 7 6 none swap 2 7
assignstate enable
add flow fid-__%lanPort__-__%flowID__ "__%circuitName__" coregular wan-if e1000-wan-1 disable multi none %cos0cir %cos0eir %cos0cbs %cos0ebs %cos0shaperA2N ctag %ctagcontrol %ctag stag none %vlanMember
  

</fragment>

<fragment block="cos1ConfigurationBlock" resolveGlobalParams="true">
home
configure interface e100-lan-__%lanPort__
  flow fid-__%lanPort__-__%flowID__
	add queue que-__%lanPort__-__%flowID__-__%cos1id__ basic %cos1shaperA2N
	add policer ingress pol-__%lanPort__-__%flowID__-__%cos1id__ %cos1cir %cos1eir %cos1cbs %cos1ebs que-__%lanPort__-__%flowID__-__%cos1id__
	add policer egress pol-__%lanPort__-__%flowID__-__%cos1id__ %cos1cir %cos1eir %cos1cbs %cos1ebs
	back
	queue que-__%lanPort__-__%cos1id__ %cos1shaperN2A
</fragment>

<fragment block="cos2ConfigurationBlock" resolveGlobalParams="true">
home
configure interface e100-lan-__%lanPort__
  flow fid-__%lanPort__-__%flowID__
	add queue que-__%lanPort__-__%flowID__-__%cos2id__ basic %cos2shaperA2N
	add policer ingress pol-__%lanPort__-__%flowID__-__%cos2id__ %cos2cir %cos2eir %cos2cbs %cos2ebs que-__%lanPort__-__%flowID__-__%cos2id__
	add policer egress pol-__%lanPort__-__%flowID__-__%cos2id__ %cos2cir %cos2eir %cos2cbs %cos2ebs
	back
	queue que-__%lanPort__-__%cos2id__ %cos2shaperN2A
</fragment>

<fragment block="portConfBlock" resolveGlobalParams="true">
home
configure interface e100-lan-__%lanPort__
</fragment>

<!--cli-command>
home
configure interface %lanPort
assignstate disable
portmode Connection-oriented
add flow fid-1-2 www coregular wan-if e1000-wan-1 disable multi none 1024000 0 32 0 128 ctag push 123-0 stag none 123-*
flow fid-1-2
add queue que-1-2-1 basic 128
policer ingress pol-1-2-0 1024000 0 32 0
add policer ingress pol-1-2-1 1024000 0 32 0 que-1-2-1
policer egress pol-1-2-0 1024000 0 32 0
add policer egress pol-1-2-1 1024000 0 32 0
exit
assignstate enable
</cli-command-->
</template>