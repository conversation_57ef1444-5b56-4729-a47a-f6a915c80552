<template>
<header>
<neType>FSP 150CC-GE114, FSP 150-GE101<PERSON><PERSON>, FSP 150CC-GE112, FSP 150CC-GE114S, FSP 150CC-GE114SH, FSP 150CC-GE114H, FSP 150CC-GE114PH, FSP 150-GE112Pro, FSP 150-GE112Pro-M, FSP 150-GE112Pro-H, FSP 150-GE114Pro, FSP 150-GE114Pro-C, FSP 150-GE114Pro-SH, FSP 150-GE114Pro-CSH, FSP 150-GE114Pro-HE </neType>
<applyMode>Delta</applyMode>
<version>1.4</version>
<summary>Service template - EVPL (SingleCOS).</summary>
<category>Service Provisioning</category>
<comment>#</comment>
</header>
<cli-command>
# DO NOT EDIT THIS LINE. FILE_TYPE=CONFIGURATION_FILE
</cli-command>

<command>
<block display="Port Configuration" name="PortConfBlock" />
<param display="ACC Port" name="accport" default="Select port" block="PortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="ACCESS PORT-1-1-1-3" literal="3"/>
<token display="ACCESS PORT-1-1-1-4" literal="4"/>
<token display="ACCESS PORT-1-1-1-5" literal="5"/>
<token display="ACCESS PORT-1-1-1-6" literal="6"/>
</param>
<param display="Speed" name="speed" default="auto-1000-full" block="PortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="100-full" literal="100-full"/>
<token display="1000-full" literal="1000-full"/>
<token display="auto-1000-full" literal="auto-1000-full"/>
<token display="auto-speed-detect" literal="auto-speed-detect"/>
</param>
<param display="Admin State" name="adminstate" default="IS" block="PortConfBlock" paramOrder="1" scope="global">
<type>Enum</type>
<token display="Disabled" literal="disabled"/>
<token display="IS" literal="in-service"/>
<token display="Maintenance" literal="maintenance"/>
<token display="Management" literal="management"/>
<token display="Unassigned" literal="unassigned"/>
</param>
</command>

<fragment block="PortConfBlock" resolveGlobalParams="true">
#
#CLI:ACCESS PORT-1-1-1-__%accport__  Edit
#
home
network-element ne-1
<neType types="FSP 150CC-GE114">configure nte nte114-1-1-1</neType>
<neType types="FSP 150CC-GE112">configure nte nte112-1-1-1</neType>
<neType types="FSP 150CC-GE114S">configure nte nte114s-1-1-1</neType>
<neType types="FSP 150CC-GE114SH">configure nte nte114sh-1-1-1</neType>
<neType types="FSP 150CC-GE114H">configure nte nte114h-1-1-1</neType>
<neType types="FSP 150CC-GE114PH">configure nte nte114ph-1-1-1</neType>
<neType types="FSP 150-GE114Pro">configure nte nte114pro-1-1-1</neType>
<neType types="FSP 150-GE114Pro-C">configure nte nte114pro_c-1-1-1</neType>
<neType types="FSP 150-GE114Pro-SH">configure nte nte114pro_sh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-CSH">configure nte nte114pro_csh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-HE">configure nte nte114pro_he-1-1-1</neType>
<neType types="FSP 150-GE101Pro">configure nte nte101pro-1-1-1</neType>
configure access-port access-1-1-1-__%accport__
admin-state %adminstate
speed %speed
</fragment>

<command>
<block display="Flow Configuration" name="FlowConfBlock" />
<param display="Flow No." name="flowid" block="FlowConfBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.flow">
		<arg>1</arg>
		<arg>%accport</arg>
	</function>
</default>
</param>
<param display="COS" name="cosid" default="COS-7" copyFrom="cosid" block="FlowConfBlock" scope="global">
<type>Enum</type>
<token display="COS-0" literal="0"/>
<token display="COS-1" literal="1"/>
<token display="COS-2" literal="2"/>
<token display="COS-3" literal="3"/>
<token display="COS-4" literal="4"/>
<token display="COS-5" literal="5"/>
<token display="COS-6" literal="6"/>
<token display="COS-7" literal="7"/>
</param>
<param display="Network Interface" name="netInterface" copyFrom="netInterface" default="NETWORK PORT-1-1-1-1" block="FlowConfBlock" scope="global">
<type>Enum</type>
<token display="NETWORK PORT-1-1-1-1" literal="network-1-1-1-1"/>
<token display="NETWORK PORT-1-1-1-2" literal="network-1-1-1-2"/>
<token display="ERP-1-1" literal="erp-1-1"/>
<token display="ERP-1-2" literal="erp-1-2"/>
</param>
<param display="CTag Control" name="ctagcontrol" default="Push" block="FlowConfBlock" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Push" literal="push __%ctag__"/>
<token display="Push-vid" literal="push-vid __%ctag__"/>
<token display="Swap-vid" literal="swap-vid __%ctag__"/>
<token display="Pop" literal="pop __%ctag__"/>
</param>
<param display="C-Tag - Priority" name="ctag" default="1-0" copyFrom="ctag" block="FlowConfBlock" optional="true" scope="global">
<type>String</type>
</param>
<param display="STag Control" name="stagcontrol" default="Push" block="FlowConfBlock" scope="global">
<type>Enum</type>
<token display="None" literal="none"/>
<token display="Push" literal="push __%stag__"/>
</param>
<param display="S-Tag - Priority" name="stag" default="1-0" copyFrom="stag" block="FlowConfBlock" optional="true" scope="global">
<type>String</type>
</param>
<param display="Vlan Memeber" name="vlanmember" default="1-*" copyFrom="vlanmember" block="FlowConfBlock" scope="global">
<type>String</type>
</param>

<neType types="FSP 150CC-GE114, FSP 150-GE101Pro, FSP 150CC-GE112, FSP 150CC-GE114S, FSP 150CC-GE114SH, FSP 150CC-GE114H, FSP 150CC-GE114PH, FSP 150-GE112Pro, FSP 150-GE112Pro-M, FSP 150-GE112Pro-H, FSP 150-GE114Pro, FSP 150-GE114Pro-C, FSP 150-GE114Pro-SH, FSP 150-GE114Pro-CSH, FSP 150-GE114Pro-HE">
<param display="Service End Point" name="fnm.serviceEnd" default="FLOW-1-1-1-2-1" block="FlowConfBlock" scope="global">
<type>Composite</type>
<value>FLOW-1-1-1-__%accport__-__%flowid__</value>
</param>
</neType>

</command>

<command>
<block display="Single COS" name="CosBlock" blockParent="FlowConfBlock"/>
<param display="CIR (bps)" name="cir" default="64000" copyFrom="cir"  block="CosBlock" scope="global">
<type>Integer</type>
</param>
<param display="EIR (bps)" name="eir" default="64000" copyFrom="eir" block="CosBlock" scope="global">
<type>Integer</type>
</param>
<param display="Buffer Size (KB)" name="bufsize" default="256" copyFrom="bufsize" block="CosBlock" scope="global">
<type>Integer</type>
</param>
</command>

<fragment block="FlowConfBlock" resolveGlobalParams="true">
#
#CLI:FLOW-1-1-1-__%accport__-__%flowid__
#
home
network-element ne-1
<neType types="FSP 150CC-GE114">configure nte nte114-1-1-1</neType>
<neType types="FSP 150CC-GE112">configure nte nte112-1-1-1</neType>
<neType types="FSP 150CC-GE114S">configure nte nte114s-1-1-1</neType>
<neType types="FSP 150CC-GE114SH">configure nte nte114sh-1-1-1</neType>
<neType types="FSP 150CC-GE114H">configure nte nte114h-1-1-1</neType>
<neType types="FSP 150CC-GE114PH">configure nte nte114ph-1-1-1</neType>
<neType types="FSP 150-GE114Pro">configure nte nte114pro-1-1-1</neType>
<neType types="FSP 150-GE114Pro-C">configure nte nte114pro_c-1-1-1</neType>
<neType types="FSP 150-GE114Pro-SH">configure nte nte114pro_sh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-CSH">configure nte nte114pro_csh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-HE">configure nte nte114pro_he-1-1-1</neType>
<neType types="FSP 150-GE101Pro">configure nte nte101pro-1-1-1</neType>
configure access-port access-1-1-1-__%accport__
service-type evpl
add flow flow-1-1-1-__%accport__-__%flowid__ "" regular-evc disabled disabled disabled disabled %cosid disabled __%ctagcontrol__ disabled __%stagcontrol__ "__%vlanmember__" 64000 64000 access-interface access-1-1-1-__%accport__ network-interface __%netInterface__
</fragment>

<fragment block="CosBlock" resolveGlobalParams="true">
#
#CLI:A2N SHAPER-1-1-1-__%accport__-__%flowid__-0  Edit
#
home
network-element ne-1
<neType types="FSP 150CC-GE114">configure nte nte114-1-1-1</neType>
<neType types="FSP 150CC-GE112">configure nte nte112-1-1-1</neType>
<neType types="FSP 150CC-GE114S">configure nte nte114s-1-1-1</neType>
<neType types="FSP 150CC-GE114SH">configure nte nte114sh-1-1-1</neType>
<neType types="FSP 150CC-GE114H">configure nte nte114h-1-1-1</neType>
<neType types="FSP 150CC-GE114PH">configure nte nte114ph-1-1-1</neType>
<neType types="FSP 150-GE114Pro">configure nte nte114pro-1-1-1</neType>
<neType types="FSP 150-GE114Pro-C">configure nte nte114pro_c-1-1-1</neType>
<neType types="FSP 150-GE114Pro-SH">configure nte nte114pro_sh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-CSH">configure nte nte114pro_csh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-HE">configure nte nte114pro_he-1-1-1</neType>
<neType types="FSP 150-GE101Pro">configure nte nte101pro-1-1-1</neType>
configure access-port access-1-1-1-__%accport__
configure flow flow-1-1-1-__%accport__-__%flowid__
configure a2n-shaper a2n_shaper-1-1-1-__%accport__-__%flowid__-0
buffersize %bufsize

#
#CLI:A2N POLICER-1-1-1-__%accport__-__%flowid__-0  Edit
#
home
network-element ne-1
<neType types="FSP 150CC-GE114">configure nte nte114-1-1-1</neType>
<neType types="FSP 150CC-GE112">configure nte nte112-1-1-1</neType>
<neType types="FSP 150CC-GE114S">configure nte nte114s-1-1-1</neType>
<neType types="FSP 150CC-GE114SH">configure nte nte114sh-1-1-1</neType>
<neType types="FSP 150CC-GE114H">configure nte nte114h-1-1-1</neType>
<neType types="FSP 150CC-GE114PH">configure nte nte114ph-1-1-1</neType>
<neType types="FSP 150-GE114Pro">configure nte nte114pro-1-1-1</neType>
<neType types="FSP 150-GE114Pro-C">configure nte nte114pro_c-1-1-1</neType>
<neType types="FSP 150-GE114Pro-SH">configure nte nte114pro_sh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-CSH">configure nte nte114pro_csh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-HE">configure nte nte114pro_he-1-1-1</neType>
<neType types="FSP 150-GE101Pro">configure nte nte101pro-1-1-1</neType>
configure access-port access-1-1-1-__%accport__
configure flow flow-1-1-1-__%accport__-__%flowid__
configure a2n-policer a2n_policer-1-1-1-__%accport__-__%flowid__-0
cir %cir
eir %eir
cbs 32
ebs 1

#
#CLI:PORT N2A SHAPER-1-1-1-__%accport__-__%cosid__  Edit
#
home
network-element ne-1
<neType types="FSP 150CC-GE114">configure nte nte114-1-1-1</neType>
<neType types="FSP 150CC-GE112">configure nte nte112-1-1-1</neType>
<neType types="FSP 150CC-GE114S">configure nte nte114s-1-1-1</neType>
<neType types="FSP 150CC-GE114SH">configure nte nte114sh-1-1-1</neType>
<neType types="FSP 150CC-GE114H">configure nte nte114h-1-1-1</neType>
<neType types="FSP 150CC-GE114PH">configure nte nte114ph-1-1-1</neType>
<neType types="FSP 150-GE114Pro">configure nte nte114pro-1-1-1</neType>
<neType types="FSP 150-GE114Pro-C">configure nte nte114pro_c-1-1-1</neType>
<neType types="FSP 150-GE114Pro-SH">configure nte nte114pro_sh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-CSH">configure nte nte114pro_csh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-HE">configure nte nte114pro_he-1-1-1</neType>
<neType types="FSP 150-GE101Pro">configure nte nte101pro-1-1-1</neType>
configure access-port access-1-1-1-__%accport__
configure n2a-shaper port_n2a_shaper-1-1-1-__%accport__-__%cosid__
buffersize 800

#
#CLI:ACCESS PORT-1-1-1-__%accport__  Edit
#
home
network-element ne-1
<neType types="FSP 150CC-GE114">configure nte nte114-1-1-1</neType>
<neType types="FSP 150CC-GE112">configure nte nte112-1-1-1</neType>
<neType types="FSP 150CC-GE114S">configure nte nte114s-1-1-1</neType>
<neType types="FSP 150CC-GE114SH">configure nte nte114sh-1-1-1</neType>
<neType types="FSP 150CC-GE114H">configure nte nte114h-1-1-1</neType>
<neType types="FSP 150CC-GE114PH">configure nte nte114ph-1-1-1</neType>
<neType types="FSP 150-GE114Pro">configure nte nte114pro-1-1-1</neType>
<neType types="FSP 150-GE114Pro-C">configure nte nte114pro_c-1-1-1</neType>
<neType types="FSP 150-GE114Pro-SH">configure nte nte114pro_sh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-CSH">configure nte nte114pro_csh-1-1-1</neType>
<neType types="FSP 150-GE114Pro-HE">configure nte nte114pro_he-1-1-1</neType>
<neType types="FSP 150-GE101Pro">configure nte nte101pro-1-1-1</neType>
configure access-port access-1-1-1-__%accport__
admin-state in-service
</fragment>

<command>
<block display="CFM Configuration" name="cfmBlock" blockOptionality="true" selected="false"/>
<param display="MD ID" name="mdID" block="cfmBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.md"/>
</default>
</param>
<param display="MD Name" name="mdName" default="MD-1" block="cfmBlock" scope="global">
<type>String</type>
</param>
<param display="Level" name="level" default="0" copyFrom="level" block="cfmBlock" scope="global">
<type>Enum</type>
<token display="0" literal="0"/>
<token display="1" literal="1"/>
<token display="2" literal="2"/>
<token display="3" literal="3"/>
<token display="4" literal="4"/>
<token display="5" literal="5"/>
<token display="6" literal="6"/>
<token display="7" literal="7"/>
</param>
<param display="MA ID" name="maID" block="cfmBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.ma">
		<arg>%mdID</arg>
	</function>
</default>
</param>
<param display="MA Name" name="maName" default="MA-1-1" block="cfmBlock" scope="global">
<type>String</type>
</param>
<param display="MEP ID List" name="mepIDList" default="1,3" block="cfmBlock" scope="global">
<type>String</type>
</param>
<param display="MEP ID" name="mepID" default="1" block="cfmBlock" scope="global">
<type>Integer</type>
</param>
<param display="Priamry VID" name="primaryVID" default="1" copyFrom="primaryVID" block="cfmBlock" scope="global">
<type>Integer</type>
</param>
</command>


<fragment block="cfmBlock" resolveGlobalParams="true">
#CFM Section
#
#CLI:CFM MD-__%mdID__  Create
#
home
configure cfm
add md %mdID string "%mdName" %level none
#
#CLI:CFM MANET-__%mdID__-__%maID__  Create
#
home
configure cfm
configure md md-__%mdID__
add manet %maID string "%maName" 1sec %mepIDList
#
#CLI:CFM MACOMP-__%mdID__-__%maID__-1  Create
#
home
configure cfm
configure md md-__%mdID__
configure manet manet-__%mdID__-__%maID__
add macomp 1 access-1-1-1-__%accport__ %primaryVID defer
add mep %mepID up access-1-1-1-__%accport__ in-service mac-remote-xcon-error disabled 0
#
#CLI:CFM N2A VID SHAPER-1-1-1-__%accport__-1  Edit
#
home
configure cfm
configure cfm-n2a-vid-shaper access-1-1-1-__%accport__ cfm_n2a_vid_shaper-1-1-1-__%accport__-1
admin-state in-service
</fragment>

<command>
<block display="ESA Y.1731 LM/DM Configuration" name="esaBlock" blockOptionality="true" selected="false"/>
<param display="ESA No." name="esaid" block="esaBlock" scope="global">
<type>Integer</type>
<default>
	<function name="nextIndex" object="fnm.db.esa">
		<arg>%lcIndex</arg>
	</function>
</default>
</param>
<param display="Probe ID" name="probeID" default="ProbeY1731" block="esaBlock" scope="global">
<type>String</type>
<validate>
	<function name="uniqueName" object="fnm.db.esa">
		<arg>%probeID</arg>
	</function>
</validate>
</param>
<param display="Dst MEP ID" name="destMepID" default="3" block="esaBlock" scope="global">
<type>Integer</type>
</param>
<param display="Packet Interval" name="packetInterval" default="10sec" copyFrom="packetInterval"  block="esaBlock" scope="global">
<type>Enum</type>
<token display="1sec" literal="1sec"/>
<token display="10sec" literal="10sec"/>
<token display="1min" literal="1min"/>
</param>
<param display="Packet Size" name="packetSize" default="64" copyFrom="packetSize" block="esaBlock" scope="global">
<type>Integer</type>
</param>
<param display="Probe time interval for stats bin" name="timeInterval" default="15min" copyFrom="timeInterval" block="esaBlock" scope="global">
<type>Enum</type>
<token display="1min" literal="1min"/>
<token display="5min" literal="5min"/>
<token display="10min" literal="10min"/>
<token display="15min" literal="15min"/>
<token display="60min" literal="60min"/>
</param>
<param display="Probe number of stats history bins" name="historyBins" default="32" copyFrom="historyBins" block="esaBlock" scope="global">
<type>Integer</type>
</param>
<param display="Probe time interval for distribution stats bin" name="timeIntervalStatsBins" default="15min" copyFrom="timeIntervalStatsBins" block="esaBlock" scope="global">
<type>Enum</type>
<token display="1min" literal="1min"/>
<token display="5min" literal="5min"/>
<token display="10min" literal="10min"/>
<token display="15min" literal="15min"/>
<token display="60min" literal="60min"/>
</param>
<param display="Probe number of distribution stats history bins" name="distributionHistoryBins" default="32" copyFrom="distributionHistoryBins" block="esaBlock" scope="global">
<type>Integer</type>
</param>
</command>

<fragment block="esaBlock" resolveGlobalParams="true">
#ESA Section
#
#CLI:ESA Y.1731 LM/DM Create
#
home
network-element ne-1
configure esa
add probe esa_probe-1-1-1-__%esaid__ %probeID y1731-lm-dm access-1-1-1-__%accport__ mep-__%mdID__-__%maID__-__%mepID__ mep %destMepID disabled cos-__%cosid__ %packetInterval %packetSize %timeInterval %historyBins %timeIntervalStatsBins %distributionHistoryBins
</fragment>
</template>