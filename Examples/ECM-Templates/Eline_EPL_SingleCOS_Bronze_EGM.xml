<template>
<header>
<neType>FSP 150EG-M4, FSP 150EG-M8</neType>
<applyMode>Delta</applyMode>
<version>1.4</version>
<summary>Service template - EPL (SingleCOS) for 150EG-M</summary>
<category>Service Provisioning</category>
<comment>#</comment>
</header>
<cli-command># DO NOT EDIT THIS LINE. FILE_TYPE=CONFIGURATION_FILE</cli-command>

<command>

<block display="Port Configuration" name="portConfigBlock" blockOrder="2"/>
<param display="Slot 1" name="slotIndex1" default="EoF-24/2 on Slot 3" block="portConfigBlock" scope="global">
<type>Enum</type>
<token display="EoF-24/2 on Slot 3" literal="3" neType="FSP 150EG-M4, FSP 150EG-M8"/>
<token display="EoF-24/2 on Slot 4" literal="4" neType="FSP 150EG-M8"/>
<token display="EoF-24/2 on Slot 5" literal="5" neType="FSP 150EG-M8"/>
<token display="EoF-24/2 on Slot 6" literal="6" neType="FSP 150EG-M8"/>
<token display="EoTDM-12/4 on Slot 4" literal="4" neType="FSP 150EG-M4"/>
<token display="EoTDM-12/4 on Slot 9" literal="9" neType="FSP 150EG-M8"/>
<token display="EoTDM-12/4 on Slot 10" literal="10" neType="FSP 150EG-M8"/>
<token display="EoC-96 on Slot 5" literal="5" neType="FSP 150EG-M4"/>
<token display="EoC-96 on Slot 11" literal="11" neType="FSP 150EG-M8"/>
<token display="EoC-96 on Slot 12" literal="12" neType="FSP 150EG-M8"/>
</param>
<param display="Slot 2" name="slotIndex2" default="EoF-24/2 on Slot 3" block="portConfigBlock" scope="global">
<type>Enum</type>
<token display="EoF-24/2 on Slot 3" literal="3" neType="FSP 150EG-M4, FSP 150EG-M8"/>
<token display="EoF-24/2 on Slot 4" literal="4" neType="FSP 150EG-M8"/>
<token display="EoF-24/2 on Slot 5" literal="5" neType="FSP 150EG-M8"/>
<token display="EoF-24/2 on Slot 6" literal="6" neType="FSP 150EG-M8"/>
<token display="EoTDM-12/4 on Slot 4" literal="4" neType="FSP 150EG-M4"/>
<token display="EoTDM-12/4 on Slot 9" literal="9" neType="FSP 150EG-M8"/>
<token display="EoTDM-12/4 on Slot 10" literal="10" neType="FSP 150EG-M8"/>
<token display="EoC-96 on Slot 5" literal="5" neType="FSP 150EG-M4"/>
<token display="EoC-96 on Slot 11" literal="11" neType="FSP 150EG-M8"/>
<token display="EoC-96 on Slot 12" literal="12" neType="FSP 150EG-M8"/>
</param>

<param display="Port 1" name="portIndex1" default="1" block="portConfigBlock" scope="global" regexp="^([1-9]|1[0-9]|2[0-4])$">
<type>Integer</type>
</param>
<param display="Type of Port 1" name="portType1" default="Gigabit" block="portConfigBlock" scope="global">
<type>Enum</type>
<token display="Aggregation" literal="aggregation" />
<token display="Bits" literal="bits" />
<token display="Cds3" literal="cds3"/>
<token display="Choc12" literal="choc12" />
<token display="Choc3" literal="choc3" />
<token display="Ds3" literal="ds3"/>
<token display="Efm" literal="efm" />
<token display="Gigabit" literal="gigabit" />
<token display="Oc12" literal="oc12"/>
<token display="Oc3" literal="oc3" />
<token display="PPP" literal="ppp" />
<token display="Tbpme" literal="tbpme"/>
<token display="Tbtl" literal="tbtl" />
<token display="Tengigabit" literal="tengigabit" />
<token display="Virtual" literal="virtual"/>
<token display="X86" literal="x86" />
</param>
<param display="Port 2" name="portIndex2" default="1" block="portConfigBlock" scope="global" regexp="^([1-9]|1[0-9]|2[0-4])$">
<type>Integer</type>
</param>
<param display="Type of Port 2" name="portType2" default="Gigabit" block="portConfigBlock" scope="global">
<type>Enum</type>
<token display="Aggregation" literal="aggregation" />
<token display="Bits" literal="bits" />
<token display="Cds3" literal="cds3"/>
<token display="Choc12" literal="choc12" />
<token display="Choc3" literal="choc3" />
<token display="Ds3" literal="ds3"/>
<token display="Efm" literal="efm" />
<token display="Gigabit" literal="gigabit" />
<token display="Oc12" literal="oc12"/>
<token display="Oc3" literal="oc3" />
<token display="PPP" literal="ppp" />
<token display="Tbpme" literal="tbpme"/>
<token display="Tbtl" literal="tbtl" />
<token display="Tengigabit" literal="tengigabit" />
<token display="Virtual" literal="virtual"/>
<token display="X86" literal="x86" />
</param>
<param display="Admin State" name="portAdminState" default="up" copyFrom="adminstate" block="portConfigBlock" scope="global" >
<type>Enum</type>
<token display="up" literal="up"/>
<token display="down" literal="down"/>
</param>
</command>
<fragment block="portConfigBlock" resolveGlobalParams="true">
#
#CLI:Port-__%slotIndex1__-__%portIndex1__  Edit
#CLI:Port-__%slotIndex2__-__%portIndex2__  Edit
#
configuration
interface %portType1 __%slotIndex1__.__%portIndex1__
admin %portAdminState
back
configuration
interface %portType2 __%slotIndex2__.__%portIndex2__
admin %portAdminState
back
</fragment>

<command>
<block display="Service Configuration" name="ServiceConfigurationBlock" blockOrder="4"/>
<param display="Name" name="serviceName" copyFrom="serviceName" block="ServiceConfigurationBlock" scope="global">
<type>String</type>
<validate>
	<function name="uniqueName" object="fnm.db.flow">
		<arg>%serviceName</arg>
	</function>
</validate>
</param>

<param display="Service End Point" name="fnm.serviceEnd" default="FLOW-1-1-1-3-1" block="ServiceConfigurationBlock" scope="global">
<type>Composite</type>
<value>Service-__%serviceName__</value>
</param>
</command>

<command>
<block display="Tag Configuration" name="TagConfigurationBlock" blockParent="ServiceConfigurationBlock"/>

</command>

<command>
<block display="Rate Configuration" name="RateBlock" blockParent="ServiceConfigurationBlock"/>
<param display="SP-1 Priority Rate" name="rateProfileSP1" copyFrom="rateProfileSP1" block="RateBlock" scope="global">
<type>Enum</type>
<token function="fnm.db.priorityRateProfile"/>
</param>
<param display="SP-1 Queue Profile" name="queueProfileSP1" copyFrom="queueProfileSP1" block="RateBlock" scope="global">
<type>Enum</type>
<token function="fnm.db.queueProfile"/>
</param>
<param display="SP-2 Priority Rate" name="rateProfileSP2" copyFrom="rateProfileSP2" block="RateBlock" scope="global">
<type>Enum</type>
<token function="fnm.db.priorityRateProfile"/>
</param>
<param display="SP-2 Queue Profile" name="queueProfileSP2" copyFrom="queueProfileSP2" block="RateBlock" scope="global">
<type>Enum</type>
<token function="fnm.db.queueProfile"/>
</param>

</command>

<fragment block="ServiceConfigurationBlock" resolveGlobalParams="true">
#
#CLI:Service %serviceName Create
#
service %serviceName
#
service-port sp-1 __%slotIndex1__.__%portIndex1__
police-by-priority %rateProfileSP1
qos-profile %queueProfileSP1
back
#
service-port sp-2 __%slotIndex2__.__%portIndex2__
police-by-priority %rateProfileSP1
qos-profile %queueProfileSP2
back
#
configuration
</fragment>

<fragment block="TagConfigurationBlock" resolveGlobalParams="true">
#???
</fragment>
</template>