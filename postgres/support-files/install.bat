@echo off
REM color 3
title ADVA PostgreSQL installer

set LANG=C

set source_dir=%cd%\pgsql
set pg_bin_dir=%source_dir%\bin
set pg_data_dir=%source_dir%\data

set service_name=postgres
set display_name="ADVA: PostgreSQL Server"
set locale=en_US
set encoding=UTF8
set superuser=root
set dbname=fnm
set pass4root=ChgMeNOW


:start
cls
echo ADVA PostgreSQL installer
echo ----------------------------
echo 1. Install
echo 2. Uninstall
echo 9. Exit

:invalid_choice
set /p choice="enter your choice 1,2,9: "
if %choice%==1 goto install
if %choice%==2 goto uninstall
if %choice%==9 exit
echo invalid choice: %choice%
goto invalid_choice

:install
REM ======== INSTALL ==========
REM echo Copying in progress... 
REM xcopy %source_dir% %dest_dir% /-Y /E /H /Q /I /O /X /K
echo ----------------------------
echo Initilizing database cluster
echo ----------------------------
"%pg_bin_dir%"\pg_ctl init -s -D "%pg_data_dir%" -o "-U %superuser% -E UTF8 --locale=C" 

echo ----------------------------
echo Copying config files
echo ----------------------------
xcopy "%source_dir%"\support-files\postgresql.conf "%pg_data_dir%"\postgresql.conf /R /S /Y

echo ----------------------------
echo Registering service
echo ----------------------------
"%pg_bin_dir%"\pg_ctl.exe register -N %service_name% -D "%pg_data_dir%" -w
sc config postgres DisplayName= %display_name%

echo ----------------------------
echo Starting the server
echo ----------------------------
net start %service_name%

echo ----------------------------
echo Creating '%dbname%' database
echo ---------------------------- 
"%pg_bin_dir%"\createdb -U %superuser% %dbname%

echo ----------------------------
echo Creating users
echo ----------------------------
"%pg_bin_dir%"\psql -U %superuser% -c "CREATE USER adva WITH PASSWORD 'NeverChange';" %dbname%
"%pg_bin_dir%"\psql -U %superuser% -c "alter user adva with replication;" %dbname%
"%pg_bin_dir%"\psql -U %superuser% -c "alter user root with password 'ChgMeNOW';" %dbname%
"%pg_bin_dir%"\psql -U %superuser% -c "CREATE USER postgres;" %dbname%

echo ----------------------------
echo Stopping the server
echo ----------------------------
%SystemRoot%\system32\net stop %service_name%

echo ----------------------------
echo Creating authentication files...
echo ----------------------------
xcopy "%source_dir%"\support-files\pg_hba.conf-win "%pg_data_dir%"\pg_hba.conf /R /S /Y

echo ----------------------------
echo Starting the server
echo ----------------------------
%SystemRoot%\system32\net start %service_name%

goto end

:uninstall
REM ======== UNINSTALL ==========
echo ----------------------------
echo Unregistering service
echo ----------------------------
%SystemRoot%\system32\net stop %service_name%
"%pg_bin_dir%"\pg_ctl.exe unregister -N %service_name%

rmdir "%pg_data_dir%" /S /Q

:end

PAUSE
REM color 7
@ECHO on