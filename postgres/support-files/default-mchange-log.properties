# logger
#log4j.logger.com.mchange.v2.log.MLog = error, console
#log4j.logger.com.mchange.v2.sql = error, console
#log4j.logger.com.mchange.v2.c3p0 = error, console
#log4j.logger.com.mchange.v2.c3p0.management = error, console
#log4j.logger.com.mchange.v2.c3p0.impl = error, console
log4j.logger.com.adva.nlms.mediation.common.persistence.AdvaDBPasswordHelper = error, console

#BoneCP
log4j.logger.com.jolbox.bonecp=error, console

# console logging
log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.ConversionPattern=%d [%.15t] %-5p -%m%n




