[Unit]
Description=FNM Server
Requires=postgres.service
After=postgres.service
[Service]
Environment=LAUNCHED_BY_SYSTEMD=yes
Type=forking
RemainAfterExit=no
ExecStart=/usr/lib/systemd/system/fnmserver start
ExecStop=/usr/lib/systemd/system/fnmserver stop
Restart=always
RestartSec=30
TimeoutStartSec=360
PIDFile=/opt/adva/fsp_nm/fnmserver.pid
LimitNOFILE=90000
RestartPreventExitStatus=9
[Install]
WantedBy=multi-user.target
