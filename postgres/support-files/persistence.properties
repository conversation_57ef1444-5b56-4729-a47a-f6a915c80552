#
# Copyright 2023 Adtran Networks SE. All rights reserved.
#
# Owner: mkaszuba
#

#-----------------------------------------------------------------------------------------------------------------------#
#------------------------------------------------ DEADLOCK HANDLING ----------------------------------------------------#
#-----------------------------------------------------------------------------------------------------------------------#
# The minimal timeout after a deadlock. [ms]
MIN_DEADLOCK_TIMEOUT_MILLIS=100

# The maximal timeout after a deadlock. [ms]
MAX_DEADLOCK_TIMEOUT_MILLIS=300000

# The maximal number of transaction restart after OptimisticLockException
MAX_TRANSACTION_RESTART_NUMBER=9

# Timeout after transaction restart caused by OptimisticLockException
TIMEOUT_AFTER_OPTIMISTIC_LOCK_MILLIS=25


#-----------------------------------------------------------------------------------------------------------------------#
#----------------------------------------- POSTGRESQL CONNECTION PROPERTIES --------------------------------------------#
#-----------------------------------------------------------------------------------------------------------------------#

#PostgreSQL JDBC Driver fully qualified class name
POSTGRESQL_DRIVER_CLASS=org.postgresql.Driver

#PostgreSQL JDBC host
POSTGRESQL_JDBC_HOST=localhost

#PostgreSQL JDBC port number
POSTGRESQL_JDBC_PORT=5432

#Fully qualified class name of class that will be used for controlling custom operations and SQL generation for the specified database
#Must extends org.eclipse.persistence.platform.database.DatabasePlatform
POSTGRESQL_TARGET_DATABASE=com.adva.nlms.mediation.common.persistence.factory.AdvaPostgreSQLPlatform


#-----------------------------------------------------------------------------------------------------------------------#
#------------------------------------------- GENERAL CONNECTION PROPERTIES ---------------------------------------------#
#-----------------------------------------------------------------------------------------------------------------------#

#DB User name
DB_USER=adva

#Defines the query that will be executed for all connection tests
PREFERRED_TEST_QUERY=SELECT 1

#The size of statements cache
STATEMENTS_SIZE=10000

#Seconds a Connection can remain pooled but unused before being discarded
MAX_IDLE_TIME=600

#How often (in seconds) connection pool should test all idle, pooled but unchecked-out connections
IDLE_CONNECTION_TEST_PERIOD=660

#How many times connection pool will try to acquire a new Connection from the database before giving up
ACQUIRE_RETRY_ATTEMPTS=5

#The number of milliseconds a client calling getConnection() will wait for a Connection to be checked-in or acquired when the pool is exhausted
CHECKOUT_TIMEOUT=90000

#-----------------------------------------------------------------------------------------------------------------------#
#------------------------------------------ HIKARI CP CONNECTION PROPERTIES --------------------------------------------#
#-----------------------------------------------------------------------------------------------------------------------#
#The minimum number of idle connections that HikariCP tries to maintain in the pool, including both idle and in-use connections
MINIMUM_IDLE=8

#The maximum size that the pool is allowed to reach, including both idle and in-use connections
MAXIMUM_POOL_SIZE=145

#The maximum amount of time (in milliseconds) that a connection is allowed to sit idle in the pool
IDLE_TIMEOUT=540000

#The maximum lifetime of a connection in the pool. When a connection reaches this timeout, even if recently used, it will
#be retired from the pool
MAX_LIFETIME=630000

#Whether or not JMX Management Beans ("MBeans") are registered or not
REGISTER_MBEANS=true

#Property controls the maximum amount of time that a connection will be tested for aliveness. This value must be less than the CHECKOUT_TIMEOUT. The lowest accepted validation timeout is 1000ms (1 second).
VALIDATION_TIMEOUT=30000

#Property controls the maximum amount of time that a connection is treated as not-leaked.
LEAK_DETECTION_THRESHOLD=615000

#-----------------------------------------------------------------------------------------------------------------------#
#-------------------------------------------- BONECP CONNECTION PROPERTIES ---------------------------------------------#
#-----------------------------------------------------------------------------------------------------------------------#

#Number of partitions to use
PARTITION_COUNT=4

#Minimum number of connections that will be contained in every partition (total minimum number of connections: PARTITION_COUNT * MIN_CONNECTIONS_PER_PARTITION)
MIN_CONNECTIONS_PER_PARTITION=2

#Maximum number of connections that will be contained in every partition (total maximum number of connections: PARTITION_COUNT * MAX_CONNECTIONS_PER_PARTITION)
MAX_CONNECTIONS_PER_PARTITION=36

#When the available connections are about to run out, BoneCP will dynamically create new ones in batches.
#This property controls how many new connections to create in one go
ACQUIRE_INCREMENT=5

#If set to true, keep track of some more statistics for exposure via JMX. Will slow down the pool operation.
STATISTICS_ENABLED=false
#-----------------------------------------------------------------------------------------------------------------------#
#---------------------------------------------- PERSISTENT OBJECT CACHE ------------------------------------------------#
#-----------------------------------------------------------------------------------------------------------------------#

#Max entries for single object in object history
MAX_ENTRIES_FOR_SINGLE_OBJECT=5

#Max entries for all objects in objects history
MAX_ENTRIES_FOR_ALL_OBJECTS=1000

 #-----------------------------------------------------------------------------------------------------------------------#
 #-------------------------------------------- ECLIPSELINK PROPERTIES ---------------------------------------------------#
 #-----------------------------------------------------------------------------------------------------------------------#

# initial eclipselink cache size
CACHE_SIZE=20000

#minimum number of connections in connection pool
CONNECTION_POOL_MIN=145

#maximum number of connections in connection pool
CONNECTION_POOL_MAX=145