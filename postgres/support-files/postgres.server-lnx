#!/bin/sh
. /etc/setenv.sh
basedir=$NMS_HOME/fsp_nm/postgres
mode=$1

LD_LIBRARY_PATH=$LD_LIBRARY_PATH:${NMS_HOME}/fsp_nm/postgres/lib
export LD_LIBRARY_PATH

# check user
. $NMS_HOME/fsp_nm/bin/fnm_user.sh

if [ -z "$2" ]
then
  # if fnmuser is root then switch to postgres
  if [ "$fnmuser" = "root" ]
  then
    su postgres -c "$basedir/support-files/postgres.server $1 continue"
    exit
  else
    # if current logged in user is not a fnmuser (probably is root) then switch to fnmuser
    if [ "$LOGNAME" != "$fnmuser" ]
    then
      su $fnmuser -c "$basedir/support-files/postgres.server $1 continue"
      exit
    fi
  fi
fi

case "$mode" in
  'start')
    # Start daemon
    # Safeguard (relative paths, core dumps..)
    cd $basedir
    bin/pg_ctl -w -D data -l logfile start
    ;;

  'stop')
    # Stop daemon
    cd $basedir
    bin/pg_ctl -w -D data -m fast stop
    ;;

  'restart')
    # Restart daemon.
    cd $basedir
    bin/pg_ctl -w -D data -l logfile -m fast restart
    ;;

    *)
      # usage
      echo "Usage: $0  {start|stop|restart}  [ PostgreSQL server options ]"
      exit 1
    ;;
esac
exit 0