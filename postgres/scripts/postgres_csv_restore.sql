---
--- Copies data from the CSV file to the specific table
--- <PERSON><PERSON><PERSON> given table before copying
---
CREATE OR REPLACE FUNCTION restore_csv_dump_table(dir_path text, tbl_name text, columns_str text, file_separator text) RETURNS integer AS $$
DECLARE
	col RECORD;
BEGIN
-- select restore_csv_dump_table('c:\Dev\fnm\nms\var\csv_dumps', 'csv_table');

    -- fetch all columns
	--SELECT array_to_string(array(SELECT (column_name::text) FROM information_schema.columns WHERE table_name = tbl_name order by column_name),', ')
	--		INTO columns_str;

    -- deletes content of the table
	EXECUTE 'DELETE FROM ' || tbl_name;

	-- copy data from CSV to the table
	-- PERFORM copy_from( tbl_name, columns_str, dir_path || '/' || tbl_name ) version  for unix;
	PERFORM copy_from( tbl_name, columns_str, dir_path || file_separator || tbl_name );

    -- update fields decoded as HEX in CSV file
	FOR col IN SELECT * FROM information_schema.columns WHERE table_name = tbl_name and data_type = 'bytea' LOOP
		EXECUTE format('UPDATE %I SET %I = decode(encode(%I,''escape''),''hex'')', tbl_name, col.column_name, col.column_name);
	END LOOP;
	RETURN 1;
END;
$$ LANGUAGE plpgsql;

ALTER FUNCTION restore_csv_dump_table(text, text, text, text)
  OWNER TO adva;

CREATE OR REPLACE FUNCTION restore_csv_dump(dir_path text, file_separator text) RETURNS integer AS $$
DECLARE
	tab RECORD;
BEGIN
	for tab IN SELECT * FROM table_desc ORDER BY name LOOP
		PERFORM restore_csv_dump_table(dir_path, tab.name, tab.columns, file_separator);
	END LOOP;
	RETURN 1;
END;
$$ LANGUAGE plpgsql;

ALTER FUNCTION restore_csv_dump(text, text)
  OWNER TO adva;
  
