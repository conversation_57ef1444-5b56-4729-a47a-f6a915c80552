---
--- Copies data from CSV file file to the specific table
---
create or replace function copy_from(table_name text, columns text, file_path text)
returns void
security definer as
$$
declare
begin
	EXECUTE 'COPY '|| table_name ||' ('|| columns ||') FROM ''' || file_path || ''' csv NULL ''\N'' ESCAPE ''\''';
end;
$$ language plpgsql;

revoke all on function copy_from( text, text, text ) from public;
grant execute on function copy_from( text, text, text ) to adva;
  
