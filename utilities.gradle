/*
 *  Common utility groovy methods for use in the build system to abstract
 *  out ADVA build concepts.
 *
 *  Copyright 2023 Adtran Networks SE. All rights reserved.
 *
 *  Owner: tedd
 */
import org.gradle.api.plugins.PluginAware
import org.gradle.api.Project
import org.gradle.api.initialization.Settings

import java.util.regex.Pattern

ext {

    // Common methods for finding and applying contents of custom.gradle
    applyCustomGradle = { PluginAware pa ->
        if (pa instanceof Project) {
            applyCustomGradleFromProject(pa)
        } else if (pa instanceof Settings) {
            applyCustomGradleFromSettings(pa)
        } else {
            logger.error("Value passed to applyCustomGradle must be either gradle Settings or Project\n")
            logger.error("Type of applyCustomGradle parameter = " + pa.getClass().getName() + "\n")
        }
    }

    // find the custom.gradle in the passed project (directory) and
    // apply the content to ext variables
    applyCustomGradleFromProject = { Project p ->
        File customGradle = p.file('custom.gradle')
        if (customGradle.exists()) {
            p.apply(from: customGradle.getCanonicalPath())
        } else if (customGradle.getParentFile().getName().equals("buildSrc")) {
            // if this is the buildSrc project then try to use the custom.gradle from the parent
            File parent = customGradle.getParentFile().getParentFile();
            customGradle = new File(parent, 'custom.gradle')
            if (customGradle.exists()) {
                p.apply(from: customGradle.getCanonicalPath())
            } else {
                logger.debug("No custom.gradle found")
            }
        } else {
            logger.debug("No custom.gradle found")
        }
    }

    // Find the custom.gradle file for the gradle settings and
    // apply the content to ext variables
    applyCustomGradleFromSettings = { Settings s ->
        File customGradle = new File(s.getRootDir(), 'custom.gradle')
        if (customGradle.exists()) {
            s.apply(from: customGradle.getCanonicalPath())
        } else if (customGradle.getParentFile().getName().equals("buildSrc")) {
            // if this is the buildSrc project then try to use the custom.gradle from the parent
            File parent = customGradle.getParentFile().getParentFile();
            customGradle = new File(parent, 'custom.gradle')
            if (customGradle.exists()) {
                s.apply(from: customGradle.getCanonicalPath())
            } else {
                logger.debug("No custom.gradle found")
            }
        } else {
            logger.debug("No custom.gradle found")
        }
    }

    // Apply the users global customizations found in the user's home directory
    applyUserCustomGradle = { PluginAware pa ->
        String userHome = System.getProperty("user.home")
        if (new File("$userHome/.custom.gradle").exists()) {
            pa.apply(from: "$userHome/.custom.gradle")
        }
    }

    // Common definitions of methods for use to resolve build properties (flags) that can be
    // be redefined at the build command line or in custom.gradle files

    getStringPropWithDefault = { String name, String defaultValue ->
        if (System.getProperty(name) != null) {
            // value specified on command line
            return System.getProperty(name)
        } else if (System.getenv(name)) {
            // value specified in OS environment
            return System.getenv(name)
        } else if (ext.has(name)) {
            return ext.get(name)
        } else {
            // The root project is the FSP root.
            return defaultValue
        }
    }

    getBooleanPropWithDefault = { String name, Boolean defaultValue ->
        String value = getStringPropWithDefault(name, (String)null)

        if (value == null) {
            return defaultValue
        } else {
            try {
                if (value.equalsIgnoreCase('true')) {
                    return true
                } else {
                    return false
                }
            } catch(Throwable t) {
                // If we can't read this then assume false
                return false
            }
        }
    }

    getIntegerPropWithDefault = { String name, Integer defaultValue ->
        String value = getStringPropWithDefault(name, (String)null)

        if (value == null) {
            return defaultValue
        } else {
            try {
                return Integer.parseInt(value)
            } catch(Throwable t) {
                return defaultValue
            }
        }
    }

    getFilePropWithDefault = { String name, File defaultValue ->
        String value = getStringPropWithDefault(name, (String)null)
        if (value == null) {
            return defaultValue
        } else {
            return new File(value)
        }
    }

    // Global method that will apply dependency limitations to all defined configurations for a project
    applyConfigurationLimits = { Project p ->
        p.afterEvaluate {
            p.configurations.each { Configuration config ->
                // Some builds don't set exclusions or overrides so ignore them if this fails
                try {
                    // Apply global exclusions
                    exclusions.each { e ->
                        config.exclude group: e.group, module: e.module
                    }

                    // Apply global overrides
                    config.resolutionStrategy {
                        // apply the overrides from dependencies.gradle
                        overrides.each { o ->
                            force o.group + ":" + o.module + ":" + o.version
                        }
                    }
                } catch (Throwable t) {
                    logger.debug("Skipping application of excludes and overrides since one of them does not exist", t)
                }
            }
        }
    }

    def getDateAndTime = {
        new Date().format('yyyy-MM-dd-HH:mm:ss')
    }

    def getUserName = {
        username = System.properties['user.name']
        username != null ? username : 'unknown'
    }

    // Setup a module with the broiler-plate gradle tasks.
    //   _build_ = top level entry point for build and connected to jar construction
    setupModule = { Project p ->
        // Module level task to perform all 'build' operations
        p.tasks.create(name: "_build_") {
            dependsOn('jar')
        }

        // Make _build_ the default task
        p.defaultTasks("_build_")

        p.tasks.jar {
            // archiveName = p.name + ".jar"
            manifest {
                attributes("Copyright": "Adtran Networks SE",
                        "Implementation-Title": p.name,
                        "Implementation-Version": p.version,
                        "NMS-Build-Number": BuildNumber,
                        // Removing since this forces a rebuild of jar every time
                        // For now prefer reproducable binary artifacts
                        // "Build-Time": getDateAndTime(),
                        "Build-By": getUserName()
                )
            }
        }

        // Workaround for resources marked read only
        // In many projects some resources are read-only which causes re-build issues
        // when trying to copy over those read only resources.
        p.tasks.processResources {
            fileMode = 0644
        }

        // Only apply dependency limits to legacy builds.
        // Newer layers dependencies are negatively impacted by this logic.
        if (!p.path.contains(":layers:")) {
            applyConfigurationLimits(p)
        }

        // Ensure all copy tasks run after spotless
        Task spotlessTask = p.rootProject.tasks.findByName(':spotless')
        if (spotlessTask != null) {
            p.tasks.withType(Copy).each { Task t ->
                t.mustRunAfter(spotlessTask)
            }
        }
    }

    setupSpringBootAppModule = { Project p ->
        setupModule(p)

        // Ensure spring application boot jar is built
        p.tasks['_build_'].dependsOn('bootJar')


        // Create launch task to start the application
        p.tasks.register('launch', JavaExec) {
            classpath = files(p.tasks['bootJar'].archiveFile.get().asFile)
        }
    }
    
    setupMediationModule = { Project p ->
        setupModule(p)

        def pm = p.gradle.projectModules.getProjectsByAbsPath(p.path)

        if (pm == null || pm.size() != 1) {
            throw new RuntimeException("setupMediationModule expected to find ProjectModule entry for the project " +
                    p.path);
        } else {
            // For jar name use key elements separated by '-' like LAYER-MODULE-QUALIFIER
            String basename = pm[0].getKeyElements().join("-")
            p.tasks.jar.archiveBaseName = basename
            p.archivesBaseName = basename
        }
    }

    jarPatchFilter = { Project p, Collection<File> files ->
        List<File> filterFiles = []

        files.each { File f ->
            if (f.getCanonicalPath().contains("/build/libs/")) {
                File patchedFile = p.file(f.getCanonicalPath().replace('/build/libs/', '/build/patched/'))
                if (patchedFile.exists()) {
                    f = patchedFile
                } else {
                    p.logger.warn("WARNING: File {} patched with bom.json from original jar {} NOT FOUND", patchedFile, f)
                }
            }
            filterFiles.add(f)
        }

        return filterFiles
    }

    setupArtifactPackagingTasks = { Project p ->
         String moduleName = upperFirst(p.name)
         String advaArtifactsTask = "prepareAdva${moduleName}Artifacts"
         String obfuscatedArtifactsTask = "prepareObfuscated${moduleName}Artifacts"
         // List of jars that must be obfuscated for legal reasons
         List<String> obfuscatedJars = ["frontend.jar", "yfiles-for-javafx-3.2.jar"]

         p.tasks.create(name: advaArtifactsTask) {
             // Ensure yguard has executed
             dependsOn(":prepareProduction")

             doFirst {
                 copy {
                     from {
                         List<File> classpathFiles = jarPatchFilter(p, p.configurations.runtimeClasspath.files)

                         return p.files(classpathFiles)
                     }
                     into("$fspRootPackage/$p.name/lib")

                     exclude { FileTreeElement details ->
                         // Exclude special files that must be obfuscated to protect proprietary code
                         if (obfuscatedJars.contains(details.file.name)
                                 // Don't include directory based dependencies (only include jars)
                                 || details.file.isDirectory()) {
                             return true;
                         }
                         return false;
                     }

                     filePermissions { 0644 }
                     duplicatesStrategy = 'exclude'
                 }
             }
         }

         p.tasks.create(name: obfuscatedArtifactsTask /* , type: Copy */) {
             // Ensure yguard has executed
             dependsOn(':modules:client:frontend:yguard')

             // With upgrade to gradle 7.5.1 this task will not run if the 'frontend.jar' does not
             // exist in the build/yguard directory at the time the build starts. To work around this
             // issue a forced copy is used below which will not check for existence of the frontend.jar
             // file when the build starts before deciding to run this task.
             doFirst{
                 copy {
                     // Copy obfuscated files from yguard output directory
                     from {
                         Set<File> rawFiles = fileTree("$fspRoot/build/yguard") {
                             include("*.jar")
                         }.files
                         Collection<File> patchedFiles = jarPatchFilter(p, rawFiles)
                         return p.files(patchedFiles)
                     }

                     into("$fspRootPackage/$p.name/lib")

                     exclude { FileTreeElement details ->
                         for (File f : p.configurations.runtimeClasspath.asCollection()) {
                             // Copy the obfuscated file if it is on the runtime classpath for the application
                             if (details.file.name.equals(f.name)) {
                                 return false;
                             }
                         }
                         return true;
                     }

                     rename { String filename ->
                         return stripVersion(filename)
                     }

                     filePermissions { 0644 }
                 }
             }
         }

         p.tasks.create(name: "prepareArtifacts${moduleName}") {
             dependsOn(advaArtifactsTask)
             dependsOn(obfuscatedArtifactsTask)
         }
     }

    // Convert the first character of the passed string to upper case
    upperFirst = { String s ->
        return s.substring(0,1).toUpperCase() + s.substring(1)
    }

    // Methods for modules that are typically build from artifact published to artifactory instead of being built locally
    // These should only be used for modules that have no other dependencies to other ADVA built modules or will
    // get dependency resolution errors when trying to resolve transitive dependencies

    setupPublishedModule = { Project p ->
        boolean buildLocal = getBooleanPropWithDefault("build"+upperFirst(p.name), false)
        if (buildLocal) {
            setupModule(p)
        } else {
            // Setup a module that is primarily published to artifactory instead of built.
            // Will setup publication tasks and include publication plugins.
            // A shortcut task will be created for publishing the module named 'publish<PROJECT_NAME>'.
            // Group for publication is common 'com.adva'
            // Version for publication is taken from ext variable 'ver_<PROJECT_NAME>'.
            p.apply(plugin: 'maven-publish')

            p.group = "com.adva"
            p.version = p.ext.get("ver_"+p.name)

            p.configurations {
                _published_module_artifact_ {
                    transitive = false
                }
            }

            p.dependencies {
                _published_module_artifact_ p.group+":"+p.name+":"+p.version
            }

            p.publishing {
                publications {
                    java(MavenPublication) {
                        from p.components.java
                    }
                }
                repositories {
                    maven {
                        name = 'nmsartifactory'

                        url = "https://$artifactoryServer/artifactory/NMS"

                        credentials {
                            username = "nms"
                            password = "NetworkManager"
                        }
                    }
                }
            }

            // define shortcut for publishing
            p.tasks.create(name: "publish"+upperFirst(p.name)) {
                dependsOn('publishJavaPublicationToNmsartifactoryRepository')
            }
        }
    }

    // Setup a war module with the broiler-plate gradle tasks.
    //   _build_ = top level entry point for build and connected to war construction
    setupWarModule = { Project p ->
        // Module level task to perform all 'build' operations
        p.tasks.create(name: "_build_") {
            dependsOn("war")
        }

        // Make _build_ the default task
        p.defaultTasks("_build_")

        // Workaround for resources marked read only
        // In many projects some resources are read-only which causes re-build issues
        // when trying to copy over those read only resources.
        p.tasks.processResources {
            fileMode = 0644
        }
    }

    /**
     * Converts the passed jar file to the equivilent java compile output directory
     * if the passed jar is generated from the gradle project, or just returns
     * the same jar if not created by a gradle project.
     * This method is used to provide IDE debug hotswap support as IDEA can not
     * hot swap a code change for a classfile that is in a jar on the runtime classpath.
     */
    jarToIDEDebugPath = { File jarFile, Collection debugPath ->

        File rdir = rootProject.getProjectDir();

        if (jarFile.getCanonicalPath().startsWith(rdir.getCanonicalPath()) &&
                jarFile.name.endsWith(".jar")) {
            // This is a jar produced by a local gradle project,
            // determine the compiler output directory
            File libsDir = jarFile.getParentFile();

            // Project structure is 'build/libs/JARFILE' or 'out/libs/JARFILE'
            if (libsDir.name.equals("libs")) {
                File outputDir = libsDir.getParentFile();
                File gradleFile = new File(outputDir.getParentFile(), "build.gradle")

                if (outputDir.name.equals("build") && gradleFile.exists()) {
                    File classesDir = new File(outputDir, "classes/java/main");
                    debugPath.add(classesDir)
                    File resourcesDir = new File(outputDir, "resources/main");
                    debugPath.add(resourcesDir)
                    
                    // Include the generated persistence.xml if it exists
                    resourcesDir = new File(outputDir, "persist-full");
                    if (resourcesDir.exists()) {
                        debugPath.add(resourcesDir)
                    }
                    return;
                }
            }
        }

        debugPath.add(jarFile)
    }

    /**
     * Take the passed required classpath files and create a debug file collection from them.
     * Any jars coming from local projects will be switched out for the compile output directory
     * and the resources directory.
     * For gradle 7 a dependency from the passed task will be made to the compileJava and
     * processResources tasks to ensure proper task execution sequence and silence gradle
     * errors.
     */
    toDebugFileCollection = { FileCollection files, Set<String> debugJars ->
        Collection<File> debug = new ArrayList<>(files.size())
        files.each { File f ->
            if (isWindows && debugJars != null) {
                // Only process specified jars, other's add as-is
                if (debugJars.contains(f.name)) {
                    jarToIDEDebugPath(f, debug)
                } else {
                    debug.add(f)
                }
            } else {
                jarToIDEDebugPath(f, debug)
            }
        }

        return project.files(debug)
    }

    // Closure used to force specific tasks to have higher priority than others
    prioritizeTask = { Project project, Task task ->
        rootProject.allprojects { subproject ->
            subproject.tasks.configureEach {
                if (!(project.equals(subproject) && it.name.equals(task.name))) {
                    if (!it.name.equals('dependencies') && !it.name.equals('clean') && !it.name.equals('spotless')) {
                        it.shouldRunAfter(task);
                    } else if (it.name.equals('clean') || it.name.equals('spotless')) {
                        // Ensure clean or spotless always runs first
                        task.mustRunAfter(it);
                    }
                }
            }
        }
    }

    /**
     * A closure to get the changes to files in the build.
     * Used for identifying which files are changed in a TC job and take
     * specific actions based on what has been changed.
     * Allows for fine-tuning team city builds to adjust based what has changed.
     *
     * Structure of data returned:
     * Map indexed by file changed (full path) into a map of the change information.
     * Change information is a map of two values:
     *   TYPE => CHANGED, ADDED, REMOVED, NOT_CHANGED, DIRECTORY_CHANGED, DIRECTORY_ADDED, DIRECTORY_REMOVED
     *   VERSION => SVN version number for the changed file (or the string "<personal>" if not yet committed)
     */
    getTCChanges = {
        Map<String, Map<String, String>> map = new HashMap()
        // String changesFilePath = System.properties['system.teamcity.build.changedFiles.file']
        if (rootProject.ext.has('teamcity.build.changedFiles.file')) {
            String changesFilePath = rootProject.ext['teamcity.build.changedFiles.file']
            File changesFile = new File(changesFilePath)
            if (changesFile.exists()) {
                int pathIndexOfENCWorkspace = 0;
                if (rootProject.ext.has('teamcity.build.checkoutDir')) {
                    String checkoutPath = rootProject.ext['teamcity.build.checkoutDir']
                    // Determine if there is a pre-path to the ENC workspace
                    pathIndexOfENCWorkspace = rootProject.getProjectDir().getCanonicalPath().length() -
                            checkoutPath.size();
                    if (pathIndexOfENCWorkspace < 0) pathIndexOfENCWorkspace = 0
                } else {
                    logger.warn("teamcity.build.checkoutDir NOT DEFINED, using raw paths without adjustment")
                }

                changesFile.readLines().each { String line ->
                    String[] parts = line.split(":")
                    if (parts.size() >= 3) {
                        try {
                            Map<String, String> props = [
                                    TYPE   : parts[1],
                                    VERSION: parts[2],
                            ]
                            String path = parts[0].substring(pathIndexOfENCWorkspace)
                            map.put(path, props)
                        } catch (Exception e) {
                            logger.quiet("Failed to parse team city changes line '{}', error: {}",
                                    line, e.getMessage());
                        }
                    }
                }
            } else {
                logger.warn("Unable to find team city changes file $changesFile")
            }
        }

        return map
    }

    /**
     * Checks either a list of strings or a regex pattern against the files changed in team city
     * job. If any matches are found returns true, otherwise return false.  NOTE this means for the
     * list case only one match from the list against TC changes being tested is considered success.
     */
    checkTCChanges = { checker ->
        boolean result = false
        if (checker instanceof Collection) {
            Map<String, Map<String, String>> changes = getTCChanges()
            for (String item : checker) {
                if (item instanceof String) {
                    if (changes.keySet().contains(item)) {
                        result = true
                        return result
                    }
                } else {
                    throw new RuntimeException("Unsupported list item type for TC change checker: ", item.getClass())
                }
            }
            return false
        } else if (checker instanceof Pattern) {
            Map<String, Map<String, String>> changes = getTCChanges()
            for (String file : changes.keySet()) {
                // Return true on first match
                if (file =~ checker) {
                    result = true
                    return result
                }
            }
        } else {
            throw new RuntimeException("Unsupported type for TC change checker: "+checker.getClass())
        }
        return result
    }

    // Create a TEST runtime classpath that uses the woven output instead of the compile output
    // of a gradle module.
    // By default the unit tests will use compile output but then we weave these classes and the
    // woven classes are required for any tests that rely on aspectj or eclipse link functionality.
    // This approach allows for 'dynamic deubgging' of tests, i.e. making changes to classes while
    // the test is executing and 'loading' the changes into the active test JVM.
    // It is also possible to just use the 'configurations.testRuntimeClasspath' directly in the test,
    // but this approach requires a rebuild of the project and re-run of the tests to see changes
    // in debugging scenarios.
    getUnitTestWovenClasspath = { SourceSet ss, Configuration c ->
        List<File> classpath = new LinkedList<>()
        // Add in the project's test output directories to the runtime classpath
        classpath.add(ss.java.classesDirectory.get())
        classpath.add(ss.output.resourcesDir)
        File root = rootProject.projectDir
        String rootPath = root.getCanonicalPath()

        c.each { entry ->
            if (entry instanceof File) {
                File file = (File) entry
                String path = file.getCanonicalPath()
                File element = file
                // check to see if this a dependency from this project in the form 'projectPath/build/libs/project.jar'
                boolean processed = false
                if (path.startsWith(rootPath)) {
                    String parentPath = file.getParent()
                    if (file.getParent().endsWith('build/libs')) {
                        if (file.getName().endsWith("-test-fixtures.jar")) {
                            // This is a test fixture jar so use the test fixtures compile output
                            element = new File(file.getParentFile().getParent(), "classes/java/testFixtures")
                            classpath.add(element)
                            element = new File(file.getParentFile().getParent(), "resources/testFixtures")
                            classpath.add(element)
                            processed = true
                        } else {
                            // Use the project build/classes/java/main dir instead of main jar
                            element = new File(file.getParentFile().getParent(), "classes/java/main")
                            classpath.add(element)
                            element = new File(file.getParentFile().getParent(), "resources/main")
                            classpath.add(element)
                            processed = true
                        }
                    }
                }

                if (!processed) {
                    classpath.add(element)
                }
            }
        }

        return files(classpath)
    }

    setupBuildCache = {
        String cacheServer = getStringPropWithDefault("cacheServer", null)
        develocity {
            server = 'https://gradle.advaoptical.com'
            // The JDK does not include the ADTRAN certificate so untrusted servers must be allowed for now
            allowUntrustedServer.set(true)
        }

        buildCache {
            remote(develocity.buildCache) {
                // Push artifacts to build cache on Team City only
                push = System.getenv("TEAMCITY_VERSION") != null
                enabled = true

                if (cacheServer != null) {
                    server = "https://$cacheServer"
                }
            }

            local {
                enabled = true
            }
        }
    }

    isJavaProject = { Project p ->
        return p.file('src/main/java').exists() || p.file('src/test/java').exists()
    }
}
