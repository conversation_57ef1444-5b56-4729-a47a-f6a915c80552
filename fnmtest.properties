#
# Owner: aborkowski
#

##################################################################
#Start of copy subtopology tool configuration parameters

#com.adva.nlms.idl.mediation.init.ServerName=***********
com.adva.nlms.idl.mediation.init.ServerName=localhost
com.adva.nlms.idl.mediation.init.ServerPort=33029
com.adva.nlms.idl.mediation.init.JettyPort=8080
com.adva.nlms.idl.mediation.init.userLogin=admin
com.adva.nlms.idl.mediation.init.password=ChgMeNOW
com.adva.nlms.idl.mediation.init.authType=LOCAL
com.adva.nlms.idl.mediation.init.connectToOrb=false

# REST secure connection options
com.adva.nlms.idl.mediation.init.secureMode=true
com.adva.nlms.idl.mediation.init.securePort=8443

#absolute path to agent view directory
#setting for pySim ver. 3.0.1
com.adva.nlms.tool.agentViewDirectory=C:\\Program Files\\ADVA\\pySim
#relative path to aconfig file in agent view directory
#setting for pySim ver. 3.0.1
com.adva.nlms.tool.configDirectoryName=config
#absolute path to pySim directory
com.adva.nlms.tool.pySimDirectory=C:\\Program Files\\ADVA\\pySim
#setting for pySim ver. 3.0.1
com.adva.nlms.tool.mibdirectory=vars\\mibs
#setting of time before pySim will be started
com.adva.nlms.tool.pySimStartTime=5

 #End of copy subtopology tool configuration parametes
#################################################################

## L E V E L

# 2nd PACKAGE
com.adva.nlms.idl.mediation.NE_IPAddress=************
com.adva.nlms.idl.mediation.Subnet_Name=Subnet2Level2
com.adva.nlms.idl.mediation.Line_Name=Line1
com.adva.nlms.idl.mediation.SSLRemoteIPAddresss=*************

# 1sT PACKAGE
com.adva.nlms.idl.mediation.config.NE_IPAddress=************
com.adva.nlms.idl.mediation.config.Subnet_Name=Subnet2Level2

# CLASS
com.adva.nlms.idl.mediation.config.LineTest.Subnet_Name=FSP150CM
com.adva.nlms.idl.mediation.config.ProtLineTest.Subnet_Name=FSP150CM
com.adva.nlms.idl.mediation.config.LineExtTest.Subnet_Name=FSP150CM
com.adva.nlms.idl.mediation.config.TopologyRecreationTest.Subnet_Name=FSP150CM
com.adva.nlms.idl.mediation.config.SubnetExtTest.Subnet_Name Subnet4Level2
com.adva.nlms.idl.mediation.config.FSP150CCTest.NE_IPAddress=**************

com.adva.nlms.idl.mediation.event.EventTest.Subnet_Name=FSP150CM
com.adva.nlms.idl.mediation.event.EventExtTest.Subnet_Name=FSP150CM

com.adva.nlms.FTPServerIP=*************
com.adva.nlms.FTPServerLogin=jftp
com.adva.nlms.FTPServerPass=ChgMeNOW
com.adva.nlms.FTPServerBackupDir=nms_backup


com.adva.nlms.SFTPServerIP=*************
com.adva.nlms.SFTPServerLogin=sftp
com.adva.nlms.SFTPServerPass=ChgMeNOW
com.adva.nlms.SFTPServerBackupDir=nms_backup
com.adva.nlms.mediation.housekeeping.nebackup.defaultUploadTimeout=70

com.adva.nlms.mediation.security.Subnet_Name=Network
com.adva.nlms.mediation.security.Customer_Group_Name=Services

# HA
com.adva.nlms.SSLRemoteIPAddress= ***************
com.adva.nlms.SSLUser= advaremote
com.adva.nlms.SSLPassword= advaremote

com.adva.nlms.mediation.event.syncAlarmsListenerPort=4000
###########################################################################
# JMS Tests
###########################################################################

#jms connection properties
jms.transportProtocol=nio+ssl
jms.url=0.0.0.0
jms.brokerName=nmsServer
jms.port=33028
jms.additional.args=&socket.verifyHostName=false

#broker configuration
activemq.data=../var/queue
activemq.persistent=false
activemq.useJMX=true
activemq.advisorySupport=false
activemq.jmx.port=33092

#pool properties
pool.maxConnections = 8
pool.maximumActive=500
pool.idleTimeout=60000

###########################################################################
# PCA Tests
###########################################################################
com.adva.nlms.idl.mediation.common.pca.PCAManagerImplTest.NE_IPAddress_150CM=**************
com.adva.nlms.idl.mediation.common.pca.PCAManagerImplTest.NE_IPAddress_150CP=************
com.adva.nlms.idl.mediation.common.pca.PCAManagerImplTest.NE_IPAddress_F7=**************
com.adva.nlms.idl.mediation.common.pca.PCAManagerImplTest.NE_IPAddress_HN4000=*************

#Licences
# uncomment for overwriting the ADVA internal licenses
#com.adva.nlms.idl.mediation.config.TopologyBuilderLocal.windowsLicenceName=myLicenseName
#com.adva.nlms.idl.mediation.config.TopologyBuilderLocal.windowsLicenceKey=abcdefg
#com.adva.nlms.idl.mediation.config.TopologyBuilderLocal.unixLicenceName=myLicenseName
#com.adva.nlms.idl.mediation.config.TopologyBuilderLocal.unixLicenceKey=abcdefg
#com.adva.nlms.idl.mediation.config.TopologyBuilderLocal.linuxLicenceName=myLicenseName
#com.adva.nlms.idl.mediation.config.TopologyBuilderLocal.linuxLicenceKey=abcdefg

#Scripts runner
com.adva.nlms.mediation.housekeeping.DevInitScriptsManager.RunnerServer_IPAddress=gdn-s-sitnms
com.adva.nlms.mediation.housekeeping.DevInitScriptsManager.RunnerServer_User=ftptest
com.adva.nlms.mediation.housekeeping.DevInitScriptsManager.RunnerServer_Password=ChgMeNOW
com.adva.nlms.mediation.housekeeping.DevInitScriptsManager.RunnerServer_Path=/home/<USER>/scripts
com.adva.nlms.mediation.housekeeping.DevInitScriptsManager.RunnerServer_LogPath=/home/<USER>/scripts_logs_trunk                                                                                                                                                                                                                                                                                                                                                                                                                                                   
#NI integration tests server
com.adva.nlms.mediation.sm.prov.ni.controllerURI=http://***********:8080/ni/public
com.adva.fnm.security.admin_password_change_next_logon_required=false
com.adva.fnm.option.NeTlsCertificateHandling=acceptAny

com.adva.nlms.mediation.report.keptfilesnumber.manual=5