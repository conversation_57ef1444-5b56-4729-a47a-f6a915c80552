@echo OFF
setlocal EnableDelayedExpansion

set CURRENT_DIR=%~dp0
set FLEXNET_DIR=%CURRENT_DIR%flexnetls
set CATALINA_BASE=%CURRENT_DIR%apache-tomcat
set CATALINA_HOME=%CURRENT_DIR%apache-tomcat
set FIREWALL[0].PORT=7070
set FIREWALL[0].NAME="Adva Embedded License Server http"
set FIREWALL[1].PORT=7071
set FIREWALL[1].NAME="Adva Embedded License Server https"
set FIREWALL[2].PORT=8081
set FIREWALL[2].NAME="Adva Embedded License Manager"
set ADVA_JRE64="%CURRENT_DIR%jre"

if exist "%ADVA_JRE64:"=%\bin\server\jvm.dll" (
set CATALINA_JVM="%ADVA_JRE64:"=%\bin\server\jvm.dll"
) else (
set CATALINA_JVM="%ADVA_JRE64:"=%\bin\client\jvm.dll"
)

goto gotEnv


:gotEnv
set menu_opt=0

if ""%1"" == ""install"" goto doCerts
if ""%1"" == ""start"" goto doStart
if ""%1"" == ""stop"" goto doStop
if ""%1"" == ""status"" goto getStatus
if ""%1"" == ""uninstall"" goto doUninstall
if ""%1"" == ""update"" goto doCertsCheck

if not [%1] EQU [] goto end

echo Adva Embedded License Server and Manager automatic installation:
echo.
echo   1. start             Start ELS and Manager services.
echo   2. stop              Stop ELS and Manager services.
echo   3. status            Check status of ELS service.
echo   4. install           Install ELS and Manager services.
echo   5. update            Update ELS and Manager services.
echo   6. uninstall         Uninstall ELS and Manager services.
echo   7. http off          Close ELS HTTP port, leaving only HTTPS.
echo   8. http on           Reopen closed ELS HTTP port.
echo   9. exit              Terminate this script session.
echo.

set /p menu_opt="Please type desired option number [1-9]: "
echo.
if %menu_opt%==1 goto doStart
if %menu_opt%==2 goto doStop
if %menu_opt%==3 goto getStatus
if %menu_opt%==4 goto doNetworking
if %menu_opt%==5 goto doCertsCheck
if %menu_opt%==6 goto delNetworking
if %menu_opt%==7 goto delHttpConfig
if %menu_opt%==8 goto doHttpConfig
if %menu_opt%==9 goto end

goto gotEnv


:doNetworking
echo.
set /p fw_opt="Would you like to open firewall ports automatically? [y/n] "
echo.
if %fw_opt%==y (
	echo Validating firewall rules...
	for /l %%f in (0,1,2) do (
		netsh advfirewall firewall show rule name=!FIREWALL[%%f].NAME! >nul
		if ERRORLEVEL 1 (
			netsh advfirewall firewall add rule name=!FIREWALL[%%f].NAME! dir=in action=allow protocol=TCP localport=!FIREWALL[%%f].PORT!
			echo !FIREWALL[%%f].NAME! firewall rule created to open !FIREWALL[%%f].PORT! port.
			)
		)
	echo done.
	echo.
)
goto doCerts

:doCertsCheck
cd /D "%ADVA_JRE64:"=%\bin\"
call keytool ^
    -list ^
    -alias els-server-key ^
	-noprompt ^
	-storepass changeit ^
	-keystore "%ADVA_JRE64:"=%\lib\security\cacerts" >nul 2>nul ^
	& IF ERRORLEVEL 1 ( goto doCertsImport ) else ( goto doUpdate )

:doCertsImport
if exist "%FLEXNET_DIR:"=%\server\els.der" (
    cd /D "%ADVA_JRE64:"=%\bin\"
    echo Importing existing self-signed certificate...
    call keytool ^
    	-importcert ^
    	-alias els-server-key ^
    	-file "%FLEXNET_DIR:"=%\server\els.der" ^
    	-trustcacerts ^
    	-noprompt ^
    	--storepass changeit ^
    	-keystore "%ADVA_JRE64:"=%\lib\security\cacerts"
    goto doUpdate
) else ( goto doCerts )

:doCerts
echo Generating self-signed certificates...

set els_host_ip=
set els_host_dns=
set system_alternative_names=dns:localhost,ip:127.0.0.1

rem Create list of local IPv4 Addresses
for /f "tokens=2 delims=:" %%i in ('ipconfig ^| findstr "IP.*[0-9][0-9]*\.[0-9][0-9]*\.[0-9][0-9]*\.[0-9][0-9]*"') do (
	set els_host_ip=!els_host_ip!%%i
)

rem Create list of domain names assigned to local IP Addresses
for %%i in (!els_host_ip!) do (
	for /f "tokens=2" %%j in ('ping -a -n 1 -w 0 %%i ^| findstr "["') do (
		set els_host_dns=!els_host_dns! %%j
	)
)

rem Insert domain names list to SAN without duplicates
for %%i in (!els_host_dns!) do (
	echo !system_alternative_names! | FINDSTR /C:"%%i" >nul & IF ERRORLEVEL 1 (
	set system_alternative_names=!system_alternative_names!,dns:%%i
	)
)

rem Insert ip list to SAN without duplicates
for %%i in (!els_host_ip!) do (
	echo !system_alternative_names! | FINDSTR /C:"%%i" >nul & IF ERRORLEVEL 1 (
	set system_alternative_names=!system_alternative_names!,ip:%%i
	)
)

cd /D "%ADVA_JRE64:"=%\bin\"
call keytool ^
	-genkey ^
	-alias els-server-key ^
	-dname "CN=%COMPUTERNAME%,OU=Embedded License Server,O=ADVA Optical Networking SE,L=,ST=,C=DE" ^
	-keyalg RSA ^
	-storetype pkcs12 ^
	-validity 3650 ^
	--storepass NeverChange ^
	--keypass NeverChange ^
	-keystore "%FLEXNET_DIR:"=%\server\els.ks" ^
	-ext SAN=%system_alternative_names% >nul
call keytool ^
	-exportcert ^
	-alias els-server-key ^
	-file "%FLEXNET_DIR:"=%\server\els.der" ^
	--storepass NeverChange ^
	-keystore "%FLEXNET_DIR:"=%\server\els.ks" >nul 2>nul
call keytool ^
	-importcert ^
	-alias els-server-key ^
	-file "%FLEXNET_DIR:"=%\server\els.der" ^
	-trustcacerts ^
	-noprompt ^
	--storepass changeit ^
	-keystore "%ADVA_JRE64:"=%\lib\security\cacerts"
echo done.
echo.

set is_update=0
if ""%1"" == ""update"" set is_update=1
if %menu_opt%==5 set is_update=1
if %is_update%==1 goto doUpdate
if %is_update%==0 goto doInstall


:doInstall
echo Disabling ELS HTTP port config...
echo HTTP can be enabled by using option 8. http on
echo.
cd /D "%FLEXNET_DIR:"=%\server\"
rename flexnetls.settings flexnetls.settings.tmp
for /f "tokens=*" %%a in ('type flexnetls.settings.tmp') do (
	set settings_line=%%a
    echo "!settings_line!" | findstr "PORT=" >nul
	if not errorlevel 1 ( set settings_line=PORT=0 )
	echo !settings_line! >> flexnetls.settings
)
del flexnetls.settings.tmp

echo Installing Adva Embedded License Server service...
%SystemRoot%\System32\icacls "!CURRENT_DIR:~0,-1!" /grant "NETWORK SERVICE:RX" /T /C /Q >nul 2>nul
cd /D "%FLEXNET_DIR:"=%\server\"
call flexnetls.bat -install
call flexnetls.bat -status
echo done.
echo.

echo Installing Adva Embedded License Manager service...
cd /D "%CATALINA_HOME:"=%\bin\"
call tomcat8 //IS//Tomcat8 --DisplayName="ADVA: Embedded License Manager" ^
	 --Install="%CATALINA_HOME%\bin\tomcat8.exe" ^
	 --Jvm=%CATALINA_JVM% ^
	 --Description="ADVA: Embedded License Manager" ^
	 --Startup=auto ^
	 --StartMode=jvm ^
	 --StopMode=jvm ^
	 --StartClass=org.apache.catalina.startup.Bootstrap --StartParams=start ^
	 --StopClass=org.apache.catalina.startup.Bootstrap --StopParams=stop ^
	 --Classpath="%CATALINA_HOME%\bin\tomcat-juli.jar;%CATALINA_HOME%\bin\bootstrap.jar"
echo done.
echo.

goto doStart


:doUpdate
echo Updating Adva Embedded License Server service...
cd /D "%FLEXNET_DIR:"=%\server\"
call flexnetls.bat -update
call flexnetls.bat -status
echo done.
echo.

echo Updating Adva Embedded License Manager service...
cd /D "%CATALINA_HOME:"=%\bin\"
%SystemRoot%\system32\net stop Tomcat8
call tomcat8 //DS//Tomcat8
call tomcat8 //IS//Tomcat8 --DisplayName="ADVA: Embedded License Manager" ^
	 --Install="%CATALINA_HOME%\bin\tomcat8.exe" ^
	 --Jvm=%CATALINA_JVM% ^
	 --Description="ADVA: Embedded License Manager" ^
	 --Startup=auto ^
	 --StartMode=jvm ^
	 --StopMode=jvm ^
	 --StartClass=org.apache.catalina.startup.Bootstrap --StartParams=start ^
	 --StopClass=org.apache.catalina.startup.Bootstrap --StopParams=stop ^
	 --Classpath="%CATALINA_HOME%\bin\tomcat-juli.jar;%CATALINA_HOME%\bin\bootstrap.jar"
echo done.
echo.

goto doStart


:doStart
echo Starting Adva Embedded License Server service...
cd /D "%FLEXNET_DIR:"=%\server\"
call flexnetls.bat -start
echo done.
echo.

echo Starting Adva Embedded License Manager service...
%SystemRoot%\system32\net start Tomcat8
echo done.
echo.

goto getStatus


:doStop
echo Stopping Adva Embedded License Manager service...
%SystemRoot%\system32\net stop Tomcat8
echo done.
echo.

echo Stopping Adva Embedded License Server service...
cd /D "%FLEXNET_DIR:"=%\server\"
call flexnetls.bat -stop
echo done.
echo.

goto getStatus


:delNetworking
echo.
set /p fw_opt="Would you like to close firewall ports automatically? [y/n] "
echo.
if %fw_opt%==y (
	echo Cleaning firewall rules...
	for /l %%f in (0,1,2) do (
		netsh advfirewall firewall show rule name=!FIREWALL[%%f].NAME! >nul
		if not ERRORLEVEL 1 (
			netsh advfirewall firewall delete rule name=!FIREWALL[%%f].NAME!
			echo !FIREWALL[%%f].NAME! firewall rule removed to close !FIREWALL[%%f].PORT! port.
			)
		)
	echo done.
	echo.
)
goto doUninstall


:doHttpConfig
echo Enabling ELS HTTP port config...
cd /D "%FLEXNET_DIR:"=%\server\"
rename flexnetls.settings flexnetls.settings.tmp
for /f "tokens=*" %%a in ('type flexnetls.settings.tmp') do (
	set settings_line=%%a
    echo "!settings_line!" | findstr "PORT=" >nul
	if not errorlevel 1 ( set settings_line=PORT=!FIREWALL[0].PORT! )
	echo !settings_line! >> flexnetls.settings
)
del flexnetls.settings.tmp
echo done.
goto doUpdate


:delHttpConfig
echo Disabling ELS HTTP port config...
cd /D "%FLEXNET_DIR:"=%\server\"
rename flexnetls.settings flexnetls.settings.tmp
for /f "tokens=*" %%a in ('type flexnetls.settings.tmp') do (
	set settings_line=%%a
    echo "!settings_line!" | findstr "PORT=" >nul
	if not errorlevel 1 ( set settings_line=PORT=0 )
	echo !settings_line! >> flexnetls.settings
)
del flexnetls.settings.tmp
echo done.
goto doUpdate


:doUninstall
echo Stopping Adva Embedded License Server service...
cd /D "%FLEXNET_DIR:"=%\server\"
call flexnetls.bat -stop
echo done.
echo.

echo Removing Adva Embedded License Server service...
cd /D "%FLEXNET_DIR:"=%\server\"
call flexnetls.bat -uninstall
%SystemRoot%\system32\sc delete FNLS-advaoptc >NUL 2>NUL
RD /S /Q %SystemRoot%\ServiceProfiles\NetworkService\flexnetls\advaoptc >NUL 2>NUL
echo done.
echo.

echo Stopping Adva Embedded License Manager service...
%SystemRoot%\system32\net stop tomcat8
echo done.
echo.

echo Deleting Adva Embedded License Manager service...
cd /D "%CATALINA_HOME:"=%\bin\"
call tomcat8 //DS//Tomcat8
echo done.
echo.

goto delCerts


:delCerts
echo Removing self-signed certificates...
cd /D "%ADVA_JRE64:"=%\bin\"
call keytool ^
	-delete ^
	-alias els-server-key ^
	--storepass changeit ^
	-keystore "%ADVA_JRE64:"=%\lib\security\cacerts" >NUL 2>NUL
DEL /F /Q "%FLEXNET_DIR:"=%\server\els.ks" >NUL 2>NUL
DEL /F /Q "%FLEXNET_DIR:"=%\server\els.der" >NUL 2>NUL
echo done.
echo.

goto getStatus


:getStatus
echo Checking Adva Embedded License Server service status...
cd /D "%FLEXNET_DIR:"=%\server\"
call flexnetls.bat -status
echo done.
echo.

if [%1] EQU [] ( goto gotEnv )
goto end


:end
cd /D %CURRENT_DIR%
echo All tasks completed.
echo.
echo.
if [%1] EQU [] cmd /k pause