#!/bin/bash
CURRENT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
jre_path="$( cd "${CURRENT_DIR}/jre" && pwd )"
manager_home="$( cd "${CURRENT_DIR}/apache-tomcat" && pwd )"
manager_service_name="flexnetls-manager.service"
manager_systemd_file="/usr/lib/systemd/system/${manager_service_name}"
manager_init_file="/etc/init.d/${manager_service_name%.*}"
manager_service_pid="${manager_home}/tomcat.pid"
manager_https_port=8081
els_http_port=7070
els_https_port=7071
els_home="${CURRENT_DIR}/flexnetls/server"
els_install_dir="$( mkdir -p ${CURRENT_DIR}/local && cd ${CURRENT_DIR}/local && pwd )"
els_service_name="flexnetls-advaoptc.service"
els_init_file="/etc/init.d/${els_service_name%.*}"
els_init_config="/etc/default/flexnetls-advaoptc"
els_init_config_java="/etc/default/flexnetls"
els_systemd_file="/etc/systemd/system/${els_service_name}"
els_server_files_list=("/etc/default/flexnetls-advaoptc" \
                       "/etc/default/flexnetls" \
                       "/etc/init.d/flexnetls-advaoptc" \
                       "/etc/systemd/system/${els_service_name}" \
                       "/etc/systemd/system/${els_service_name}.d" \
                       "/var/opt/flexnetls" \
                       ${els_install_dir} \
                       ${manager_service_pid} \
                       ${manager_init_file} \
                       ${manager_systemd_file})
els_http_config_list=( "${els_home}/flexnetls" \
                       "${els_home}/configure" \
                       "/etc/default/${els_service_name%.*}" )

if [[ `ps -p 1 -o cmd=` = *"systemd"* ]] ; then
	systemd_on=true
	firewall_zone=`firewall-cmd --get-default-zone 2>/dev/null`
else
	systemd_on=false
fi

doCertsCheck(){
    cd ${jre_path}/bin
	./keytool -list \
		-alias els-server-key \
		-noprompt \
		--storepass changeit \
		-keystore "${jre_path}/lib/security/cacerts" &>/dev/null
	echo $?
}

doCertsImport(){
    cd ${jre_path}/bin
    ./keytool -importcert \
		-alias els-server-key \
		-file "${els_install_dir}/els.der" \
		-trustcacerts \
		-noprompt \
		--storepass changeit \
		-keystore "${jre_path}/lib/security/cacerts"
}

doCertsExport(){
    cd ${jre_path}/bin
    ./keytool -exportcert \
            -alias els-server-key \
            -file "${els_install_dir}/els.der" \
            --storepass NeverChange \
            -keystore "${els_install_dir}/els.ks" &>/dev/null
}

doCertsELSSettings(){
	cat <<EOF >${els_install_dir}/els.settings
keystore.path=${els_install_dir}/els.ks
keystore.password=NeverChange
https.port=${els_https_port}
EOF
}

doCertsUpdate(){
    local cert_not_in_java_keystore=$(doCertsCheck)
    if [[ ${cert_not_in_java_keystore} ]] && [[ -e "${els_install_dir}/els.der" ]] ; then
        doCertsImport
    elif [[ ${cert_not_in_java_keystore} ]] && [[ -e "${els_install_dir}/els.ks" ]] ; then
        doCertsExport
        doCertsImport
    elif [[ ${cert_not_in_java_keystore} ]] && [[ ! -e "${els_install_dir}/els.ks" ]] ; then
        doCerts
    fi
    if [[ ! -e ${els_install_dir}/els.settings ]] ; then
        doCertsELSSettings
    fi
}

doCerts(){
	echo Generating self-signed certificates...

	system_alternative_names=""
	for els_host_ip in `hostname -I` ; do
		els_host_dns=""
		els_host_dns=`getent hosts ${els_host_ip} | awk '{ print $2 }'`
		if [[ -n ${els_host_dns} ]] && [[ "${els_host_dns}" != `hostname` ]] ; then
			system_alternative_names=(${system_alternative_names}",dns:"${els_host_dns})
		fi
		system_alternative_names=(${system_alternative_names}",ip:"${els_host_ip})
	done
	els_host_san=("dns:localhost,ip:127.0.0.1"${system_alternative_names})

	mkdir -p ${els_install_dir}
	cd ${jre_path}/bin
	./keytool -genkey \
		-alias els-server-key \
		-dname "CN=`hostname`,OU=Embedded License Server,O=ADVA Optical Networking SE,L=,ST=,C=DE" \
		-keyalg RSA \
		-storetype pkcs12 \
		-validity 3650 \
		--storepass NeverChange \
		--keypass NeverChange \
		-keystore "${els_install_dir}/els.ks" \
		-ext SAN=${els_host_san}

	doCertsExport

	doCertsImport

	doCertsELSSettings

	echo -e "done.\n"
}

doInstall_server_systemd(){
	cd ${els_home}
	# set given http port before installation
    sed -i "0,/^PORT_SPEC=.*/s//PORT_SPEC=\"port: ${els_http_port}\"/" ${els_home}/install-functions.sh

    ./install-systemd.sh --program-dir ${els_install_dir} --jre_home ${jre_path} --overwrite

    systemctl is-active --quiet ${els_service_name} \
    && systemctl stop ${els_service_name} &>/dev/null

	# create local tmp folder that can be used instead of default /tmp by flexnetls user
	mkdir -p ${els_install_dir}/tmp
    chown -R flexnetls:flexnetls ${els_install_dir}/tmp
    # set property to satisfy noexec in /tmp folder
    sed -i "0,\|^.*jni-helper-directory.*|s||jni-helper-directory: ${els_install_dir}/tmp|" ${els_install_dir}/local-configuration.yaml

    # set http configuration property
    sed -i "0,/^port:.*/s//port: ${els_http_port}/" ${els_install_dir}/local-configuration.yaml
    # set https configuration properties
    sed -i "0,/^.*enabled:.*/s//  enabled: true/" ${els_install_dir}/local-configuration.yaml
    sed -i "0,/^  port:.*/s//  port: ${els_https_port}/" ${els_install_dir}/local-configuration.yaml
    sed -i "0,\|^.*keystore-path:.*|s||  keystore-path: ${els_install_dir}/els.ks|" ${els_install_dir}/local-configuration.yaml
    sed -i "0,\|^.*keystore-password:.*|s||  keystore-password: NeverChange|" ${els_install_dir}/local-configuration.yaml

    # set proper java in server service file
    sed -i "0,\|^ExecStart=.*|s||ExecStart=${jre_path}/bin/java \$JVMOPTS \$DEFINES -jar flexnetls.jar \$OPTIONS --service|" ${els_systemd_file}
    sed -i "0,\|^ExecStop=.*|s||ExecStop=${jre_path}/bin/java \$DEFINES -jar flexnetls.jar --service-shutdown --logging-threshold=DEBUG|" ${els_systemd_file}
    systemctl daemon-reload
}

doInstall_server_sysV(){
    cd ${els_home}
    # set given http port before installation
    sed -i "0,\|^HTTPS_SERVER_FILE=.*|s||HTTPS_SERVER_FILE=${els_install_dir}/els.settings|" ${els_home}/flexnetls
	sed -i "0,/^PORT=.*/s//PORT=${els_http_port}/" ${els_home}/flexnetls
	sed -i "0,/^PORT=.*/s//PORT=${els_http_port}/" ${els_home}/configure
	# remove old service config files before configuring new ones
	[[ -e "${els_init_config}" ]] && rm -f "${els_init_config}"
	[[ -e "${els_init_config_java}" ]] && rm -f "${els_init_config_java}"
	./configure --program-dir ${els_install_dir} --jre_home ${jre_path} --java_home ${jre_path} &>/dev/null
	sed -i "0,/^PORT=.*/s//PORT=${els_http_port}/" /etc/default/${els_service_name%.*}

	./flexnetls install

	# create local tmp folder that can be used instead of default /tmp by flexnetls user
	mkdir -p ${els_install_dir}/tmp
    chown -R flexnetls:flexnetls ${els_install_dir}/tmp
	# copy default config and set property to satisfy noexec in /tmp folder
	cp ${els_home}/../local-configuration.yaml ${els_install_dir}
	sed -i "0,\|^.*jni-helper-directory.*|s||jni-helper-directory: ${els_install_dir}/tmp|" ${els_install_dir}/local-configuration.yaml

	# edit server service to run from local-configuration.yaml location to fix /tmp config loading problem
	sed -i "\|std_arguments \"\$OP\"|a cd ${els_install_dir}" ${els_init_file}
}

doInstall(){
	echo Installing Adva Embedded License Server service...
	chmod -R a+r ${jre_path}

	if $systemd_on ; then
	    doInstall_server_systemd
	else
	    doInstall_server_sysV
	fi

	chown -R flexnetls ${els_install_dir}
	if [[ ! -e ${els_install_dir}/els.settings ]] ; then
        doCertsELSSettings
    fi
	chmod go-r ${els_install_dir}/els.settings
	cd ${CURRENT_DIR}
	echo -e "done.\n"

	echo Installing Adva Embedded License Manager service...
	if $systemd_on ; then
		cat <<EOF > ${manager_systemd_file}
[Unit]
Description=ADVA: Embedded License Manager
After=syslog.target network.target ${els_service_name}
[Service]
Type=forking
Environment=CATALINA_PID=${manager_service_pid}
Environment=CATALINA_HOME=${manager_home}
Environment=CATALINA_BASE=${manager_home}
Environment=JRE_HOME=${jre_path}
Environment=JAVA_OPTS="-Djava.security.egd=file:/dev/./urandom"
ExecStart=${manager_home}/bin/startup.sh
ExecStop=${manager_home}/bin/shutdown.sh
Restart=on-failure
[Install]
WantedBy=multi-user.target
EOF
		systemctl daemon-reload
		systemctl enable ${manager_service_name}
		systemctl enable ${els_service_name}
		systemctl daemon-reload
	else
		cat <<EOF > ${manager_init_file}
#!/bin/bash
##############################################################
# chkconfig: 345 98 98
### BEGIN INIT INFO
# Provides: ${manager_service_name%.*}
# Required-Start: \$local_fs \$network \$remote_fs \$syslog
# Required-Stop: \$local_fs \$network \$remote_fs \$syslog
# Default-Start:  3 4 5
# Default-Stop: 0 1 6
# Short-Description: ADVA: Embedded License Manager
# Description: Tomcat8 service launcher script
### END INIT INFO

export CATALINA_PID=${manager_service_pid}
export CATALINA_HOME=${manager_home}
export CATALINA_BASE=${manager_home}
export JAVA_HOME=${jre_path}
export JAVA_OPTS="-Djava.security.egd=file:/dev/./urandom"

# source function library
. /etc/rc.d/init.d/functions

fm_status() {
	status -p \${CATALINA_PID} ${manager_service_name%.*}
}

case "\$1" in
	start)
		${manager_home}/bin/startup.sh &>/dev/null
		fm_status
		exit \$?
		;;
	stop)
		${manager_home}/bin/shutdown.sh &>/dev/null
		fm_status
		exit \$?
		;;
	restart)
		${manager_home}/bin/shutdown.sh &>/dev/null
		${manager_home}/bin/startup.sh &>/dev/null
		fm_status
		exit \$?
		;;
	status)
		fm_status
		exit \$?
		;;
	*)
        echo "Usage: \$0 {start|stop|restart|status}"
        ;;
esac

exit 0
EOF
		chmod 0755 ${manager_init_file}
		/sbin/chkconfig --add ${manager_service_name%.*}
	fi
	echo -e "done.\n"
}

doStart(){
	local msg_srv="Starting Adva Embedded License Server service..."
	local msg_mgr="Starting Adva Embedded License Manager service..."
	if $systemd_on ; then
		echo ${msg_srv}
		systemctl is-active --quiet ${els_service_name} \
		|| systemctl start ${els_service_name}
		echo -e "done.\n"

		echo ${msg_mgr}
		systemctl is-active --quiet ${manager_service_name} \
		|| systemctl start ${manager_service_name}
		echo -e "done.\n"
	else
		echo ${msg_srv}
		/sbin/service ${els_service_name%.*} start &>/dev/null \
		&& echo ${els_service_name%.*} started \
		|| echo ${els_service_name%.*} failed to start
		echo -e "done.\n"

		echo ${msg_mgr}
		/sbin/service ${manager_service_name%.*} start &>/dev/null \
		&& echo ${manager_service_name%.*} started \
		|| echo ${manager_service_name%.*} failed to start
		echo -e "done.\n"
	fi
}

doStop(){
	local msg_srv="Stopping Adva Embedded License Server service..."
	local msg_mgr="Stopping Adva Embedded License Manager service..."
	if $systemd_on ; then
		echo ${msg_mgr}
		systemctl is-active --quiet ${manager_service_name} \
		&& systemctl stop ${manager_service_name}
		echo -e "done.\n"

		echo ${msg_srv}
		systemctl is-active --quiet ${els_service_name} \
		&& systemctl stop ${els_service_name}
		echo -e "done.\n"
	else
		echo ${msg_mgr}
		/sbin/service ${manager_service_name%.*} stop &>/dev/null \
		&& echo ${manager_service_name%.*} failed to stop \
		|| echo ${manager_service_name%.*} stopped
		echo -e "done.\n"

		echo ${msg_srv}
		/sbin/service ${els_service_name%.*} stop &>/dev/null
		# suppress misleading service output and self check
		# if user flexnetls still holds any java processes
		if ! $(pgrep -u flexnetls &>/dev/null) ; then
		    echo ${els_service_name%.*} stopped
        else
            for flexnetls_process in $(pgrep -u flexnetls) ; do
                ps ${flexnetls_process} | grep java | grep flexnetls.jar \
                && echo ${els_service_name%.*} failed to stop \
                || echo ${els_service_name%.*} stopped
            done
        fi
		echo -e "done.\n"
	fi
}

infoStatus(){
	local msg_srv="Checking Adva Embedded License Server service status..."
	local msg_mgr="Checking Adva Embedded License Manager service status..."
	if $systemd_on ; then
		echo  ${msg_srv}
		if [[ ! -e ${els_systemd_file} ]] ; then
		    echo Service ${els_service_name%.*} does not exist
		else
            systemctl is-active --quiet ${els_service_name} \
            && echo ${els_service_name} is running \
            || echo ${els_service_name} is not running
        fi
		echo -e "done.\n"

		echo  ${msg_mgr}
		if [[ ! -e ${manager_systemd_file} ]] ; then
		    echo Service ${manager_service_name%.*} does not exist
		else
            systemctl is-active --quiet ${manager_service_name} \
            && echo ${manager_service_name} is running \
            || echo ${manager_service_name} is not running
        fi
		echo -e "done.\n"
	else
		echo  ${msg_srv}
		if [[ ! -e ${els_init_file} ]] ; then
		    echo Service ${els_service_name%.*} does not exist
		else
            /sbin/service ${els_service_name%.*} status &>/dev/null \
            && echo ${els_service_name%.*} is running \
            || echo ${els_service_name%.*} is not running
        fi
		echo -e "done.\n"

		echo  ${msg_mgr}
		if [[ ! -e ${manager_init_file} ]] ; then
		    echo Service ${manager_service_name%.*} does not exist
		else
            /sbin/service ${manager_service_name%.*} status &>/dev/null \
            && echo ${manager_service_name%.*} is running \
            || echo ${manager_service_name%.*} is not running
        fi
		echo -e "done.\n"
	fi
}

doUninstall(){
	local msg_srv="Deleting Adva Embedded License Server service..."
	local msg_mgr="Deleting Adva Embedded License Manager service..."
	echo ${msg_mgr}
	if $systemd_on ; then
		systemctl disable ${manager_service_name} &>/dev/null
		[ -e ${manager_service_pid} ] \
		&& pkill -F ${manager_service_pid} 2>/dev/null
		systemctl daemon-reload
	else
		/sbin/chkconfig --del ${manager_service_name%.*}
		[ -e ${manager_service_pid} ] \
		&& pkill -F ${manager_service_pid} 2>/dev/null
	fi
	echo -e "done.\n"

	echo ${msg_srv}
	cd ${els_home}
	./flexnetls uninstall
	cd ${CURRENT_DIR}
	( $systemd_on ) \
	&& systemctl disable ${els_service_name} 2>/dev/null
	echo -e "done.\n"
}

doCleanup(){
	echo Cleanup local configuration and database files...
	for conf_file in ${els_server_files_list[@]}
	do
		[ -e ${conf_file} ] && rm -rf ${conf_file}
	done
	[ -n `awk -F':' '{ print $1}' /etc/passwd | grep flexnetls` ] \
	&& userdel -r flexnetls 2>/dev/null
	echo -e "done.\n"
}

delCerts(){
	echo Removing self-signed certificates...
	cd ${jre_path}/bin
	./keytool -delete \
		-alias els-server-key \
		--storepass changeit \
		-keystore "${jre_path}/lib/security/cacerts" &>/dev/null

	cd ${els_install_dir} 2>/dev/null && rm -rf els.der els.ks 2>/dev/null
	echo -e "done.\n"
}

infoFirewall(){
	local firewall_msg_manual="Please make sure to ${1} firewall ports manually."

	if $systemd_on ; then
		systemctl is-active --quiet firewalld &>/dev/null || return
	else
		( /sbin/service iptables status &>/dev/null ) || return
	fi

	if [[ "$menu_opt" == "0" ]] ; then
		echo $firewall_msg_manual
		return
	elif [[ "$menu_opt" == 4 ]] ; then
		doFirewall
	elif [[ "$menu_opt" == 6 ]] ; then
		delFirewall
	fi
}

doFirewall(){
	echo "Would you like to open firewall ports automatically on all interfaces? [y/n] "
	read firewall_opt
	if [[ "${firewall_opt}" != "y" ]] ; then
		menu_opt=0
		infoFirewall "open"
		return
	fi

	if [[ "${els_http_port}" == "0" ]] ; then
		local firewall_ports=( ${manager_https_port} ${els_https_port} )
	else
		local firewall_ports=( ${manager_https_port} ${els_https_port} ${els_http_port} )
	fi

	for fport in ${firewall_ports[@]} ; do
		if $systemd_on ; then
			systemctl is-active --quiet firewalld &>/dev/null || return
			echo Open firewall ports...
			firewall-cmd --zone=$firewall_zone --permanent --add-port=${fport}/tcp &>/dev/null
			echo Port ${fport} in $firewall_zone zone opened
			firewall-cmd --reload
		else
			( /sbin/service iptables status &>/dev/null ) || return
			echo Open firewall ports...
			grep -e "-A INPUT -p tcp -m state --state NEW -m tcp --dport ${fport} -j ACCEPT" /etc/sysconfig/iptables &>/dev/null \
			|| sed -i "0,/.*REJECT.*/s/.*REJECT.*/-A INPUT -p tcp -m state --state NEW -m tcp --dport ${fport} -j ACCEPT\n&/" /etc/sysconfig/iptables
			echo Port ${fport} opened
			/sbin/service iptables restart
		fi
	done
	echo -e "done.\n"
}

delFirewall(){
	echo "Would you like to close firewall ports automatically on all interfaces? [y/n] "
	read firewall_opt
	if [[ "${firewall_opt}" != "y" ]] ; then
		menu_opt=0
		infoFirewall "close"
		return
	fi

	if [[ "${els_http_port}" == "0" ]] ; then
		local firewall_ports=( ${manager_https_port} ${els_https_port} )
	else
		local firewall_ports=( ${manager_https_port} ${els_https_port} ${els_http_port} )
	fi

	for fport in ${firewall_ports[@]} ; do
		if $systemd_on ; then
			systemctl is-active --quiet firewalld &>/dev/null || return
			echo Close firewall ports...
			firewall-cmd --zone=$firewall_zone --permanent --remove-port=${fport}/tcp &>/dev/null
			echo Port ${fport} in $firewall_zone zone closed
			firewall-cmd --reload
		else
			( /sbin/service iptables status &>/dev/null ) || return
			echo Close firewall ports...
			sed -i "/-A INPUT -p tcp -m state --state NEW -m tcp --dport ${fport} -j ACCEPT/d" /etc/sysconfig/iptables
			/sbin/service iptables restart
			echo Port ${fport} closed
		fi
	done
	echo -e "done.\n"
}

doHttpPortSwitch(){
	local msg_open="Enabling ELS HTTP port config..."
	local msg_close="Disabling ELS HTTP port config...\nHTTP can be enabled by using option 8. http on"
	local els_http_port=${1}

	[[ "${1}" == "0" ]] \
	&& echo -e ${msg_close} \
	|| echo ${msg_open}

    if $systemd_on ; then
        sed -i "0,/^port:.*/s//port: ${els_http_port}/" "${els_install_dir}/local-configuration.yaml"
	else
		for els_http_config in ${els_http_config_list[@]} ; do
	        sed -i "0,/^PORT=.*/s//PORT=${els_http_port}/" ${els_http_config}
	    done
	fi

	echo -e "done.\n"
}

getHelp(){
	echo -e "\nAdva Embedded License Server and Manager automatic installation:"
	echo ""
	echo "	1. start			Start ELS and Manager services."
	echo "	2. stop				Stop ELS and Manager services."
	echo "	3. status			Check status of ELS and Manager service."
	echo "	4. install			Install ELS and Manager services."
	echo "	5. update			Reinstall, leaving database and certs untouched."
	echo "	6. uninstall			Uninstall ELS and Manager services."
	echo "	7. http off			Close ELS HTTP port, leaving only HTTPS."
	echo "	8. http on			Reopen closed ELS HTTP port."
	echo "	9. exit				Terminate this script session."
	echo ""
}

while true; do
	if [ -z "$1" ] ; then
		getHelp
		echo "Please type desired option number [1-9]: "
		read menu_opt
	else
		menu_opt=0
	fi

	if [[ $1 == "start" ]] || [[ "$menu_opt" == "1" ]] ; then
		doStart
	elif [[ $1 == "stop" ]] || [[ "$menu_opt" == "2" ]] ; then
		doStop
		infoStatus
	elif [[ $1 == "status" ]] || [[ "$menu_opt" == "3" ]] ; then
		infoStatus
	elif [[ $1 == "install" ]] || [[ "$menu_opt" == "4" ]] ; then
		doCerts
		doInstall
		doHttpPortSwitch 0
		doStart
		infoFirewall "open"
		infoStatus
	elif [[ $1 == "update" ]] || [[ "$menu_opt" == "5" ]] ; then
		doStop
		doUninstall
		doInstall
		doCertsUpdate
		doStart
		infoStatus
	elif [[ $1 == "uninstall" ]] || [[ "$menu_opt" == "6" ]] ; then
		doStop
		doUninstall
		delCerts
		doCleanup
		infoFirewall "close"
		infoStatus
	elif [[ "$menu_opt" == "7" ]] ; then
		doStop
		doHttpPortSwitch 0
		doStart
		infoFirewall "close"
	elif [[ "$menu_opt" == "8" ]] ; then
		doStop
		doHttpPortSwitch ${els_http_port}
		doStart
		infoFirewall "open"
	elif [[ "$menu_opt" == "9" ]] ; then
		exit 0
	fi
	if [[ -n "$1" ]] ; then
		break
	else
		echo
		read -n1 -r -p "Press any key to continue..." key
	fi

done