<?xml version="1.0" encoding="UTF-8"?>

<Configuration name="NMClientConfig" monitorInterval="10" status="info">
<!--<Configuration status="error">-->
    <Properties>
        <!--# log directories, fsp.home is now set before xml is initialized. -->
        <!--# fsp.home contains path of user home directory location of ENC and is depending on product name which can modified by the customer in launch.properties (see FNMD-32887) -->
        <Property name="logdir">${sys:fsp.home}log</Property>
    </Properties>
    <Loggers>
        <Root level="warn">
            <AppenderRef ref="frontendlog"/>
            <AppenderRef ref="frontenderrorlog"/>
        </Root>
<!-- Warning: used to debug by a developer, not for SIT/SVT/production as it will choke the client.
        <Logger name="com.adva.nlms.frontend.topology.TopologyManager" level="info" additivity="false">
            <AppenderRef ref="frontendlog"/>
        </Logger>
-->
    <Logger name="com.adva.nlms.frontend.comm.service.rest.core.RestClientRequestFilter" level="error" additivity="false">
       <AppenderRef ref="UIBlockingErrorslog"/>

    </Logger>

        <Logger name="com.adva.nlms.frontend.security.config.group.GroupTableView" level="debug" additivity="false">
            <AppenderRef ref="frontendlog"/>
        </Logger>
        <Logger name="com.adva.nlms.frontend.security.config.roles.RolesTableView" level="debug" additivity="false">
            <AppenderRef ref="frontendlog"/>
        </Logger>
        <Logger name="com.adva.nlms.frontend.security.config.users.UsersTableView" level="debug" additivity="false">
            <AppenderRef ref="frontendlog"/>
        </Logger>
        <Logger name="com.adva.nlms.frontend.elssso.action.AbstractConnectToELSAction" level="info" additivity="false">
            <AppenderRef ref="frontendlog"/>
        </Logger>

        <Logger name="com.adva.nlms.frontend.systemhealth.ThreadDumpClientLogCollector" level="info" additivity="false">
           <AppenderRef ref="ThreadDumplog"/>
        </Logger>
    </Loggers>
    <Appenders>
        <RollingFile name="frontendlog" fileName="${logdir}/frontend.log" filePattern="${logdir}/frontend.log.%i" append="true">
            <PatternLayout pattern="%d [%.15t] %-5p - %m%n"/>
            <DefaultRolloverStrategy max="2"/>
            <SizeBasedTriggeringPolicy size="10mb"/>
        </RollingFile>
        <RollingFile name="frontenderrorlog" fileName="${logdir}/frontend.error.log" filePattern="${logdir}/frontend.error.log.%i" append="false">
            <ThresholdFilter level="ERROR"/>
            <PatternLayout pattern="%d [%.15t] %-5p - %m%n"/>
            <DefaultRolloverStrategy max="2"/>
            <SizeBasedTriggeringPolicy size="10mb"/>
        </RollingFile>
        <RollingFile name="UIBlockingErrorslog" fileName="${logdir}/UIBlockingErrors.log" filePattern="${logdir}/UIBlockingErrors.log.%i" append="true">
            <PatternLayout pattern="%d [%.15t] %-5p - %m%n"/>
            <DefaultRolloverStrategy max="2"/>
            <SizeBasedTriggeringPolicy size="10mb"/>
        </RollingFile>
        <RollingFile name="ThreadDumplog" fileName="${logdir}/ThreadDump.log" filePattern="${logdir}/ThreadDump.log.%i" append="true">
            <PatternLayout pattern="%d [%.15t] %-5p - %m%n"/>
            <DefaultRolloverStrategy max="2"/>
            <SizeBasedTriggeringPolicy size="10mb"/>
        </RollingFile>
    </Appenders>
</Configuration>