--
-- Copyright 2023 Adtran Networks SE. All rights reserved.
--
-- Owner: ask<PERSON><PERSON>cki
--

-- Find unsupported devices
--
-- DROP FUNCTION IF EXISTS find_unsupported_devices();
CREATE OR REPLACE FUNCTION find_unsupported_devices() RET<PERSON>NS TEXT AS $$
DECLARE
  v7 BOOL DEFAULT FALSE;
  out TEXT := '';
BEGIN
  SELECT TRUE INTO v7 FROM sr_db_version_info v WHERE v.majorversion >= 7;
  IF v7 THEN
    out := out || find_unsupported_devices_db_v7('FSP 150CC-584', '1.3', 'UNSUPPORTED, PLEASE REMOVE BEFORE UPGRADE') ;
    out := out || find_unsupported_devices_db_v7('FSP 150CC-324', '1.2', 'UNSUPPORTED, PLEASE REMOVE BEFORE UPGRADE') ;
    -- out := out || find_unsupported_devices_like('FSP 150EG-X', '%5.1.1-%', 'NOT OFFICIALLY SUPPORTED') ;
    out := out || find_unsupported_devices_db_v7('HN 4000', '6.2', 'UNSUPPORTED') ;
    out := out || find_unsupported_devices_db_v7('FSP 2000', '9.6', 'UNSUPPORTED, PLEASE REMOVE BEFORE UPGRADE') ;
    out := out || find_unsupported_devices_db_v7('FSP 150CM', '3.3', 'UNSUPPORTED') ;
    out := out || find_unsupported_devices_db_v7('FSP 150CM 4U', '3.3', 'UNSUPPORTED') ;
    out := out || find_unsupported_devices_db_v7('FSP 150CP', '2.4', 'UNSUPPORTED') ;
    out := out || find_unsupported_devices_db_v7('FSP 150MX', '2.5', 'UNSUPPORTED') ;
    out := out || find_unsupported_devices_db_v7('FSP 500', '9.5', 'UNSUPPORTED, PLEASE REMOVE BEFORE UPGRADE') ;
    out := out || find_unsupported_devices_db_v7('FSP 1500', '3.0', 'UNSUPPORTED') ;
    out := out || find_unsupported_devices_db_v7('FSP 3000', '9.4', 'UNSUPPORTED, PLEASE REMOVE BEFORE UPGRADE') ;
    out := out || find_unsupported_devices_db_v7('FSP 150CC-GE206', '4.4.1', 'UNSUPPORTED') ;
    out := out || find_unsupported_devices_db_v7('FSP 150CC-GE206F', '4.4.1', 'UNSUPPORTED') ;
    out := out || find_unsupported_devices_db_v7('FSP 150CC-GE206V', '5.3.1', 'UNSUPPORTED') ;

    out := out || find_unsupported_devices_db_v7('JUNIPER PTX3000', '15.1F5', 'UNSUPPORTED, PLEASE REMOVE BEFORE UPGRADE') ;
    out := out || find_unsupported_devices_db_v7('JUNIPER PTX5000', '15.1F5', 'UNSUPPORTED, PLEASE REMOVE BEFORE UPGRADE') ;
    out := out || find_unsupported_devices_db_v7('JUNIPER PTX9000', '15.1F5', 'UNSUPPORTED, PLEASE REMOVE BEFORE UPGRADE') ;

    out := out || find_unsupported_devices_hn('UNSUPPORTED') ;
    out := out || find_unsupported_devices_825('UNSUPPORTED, PLEASE REMOVE BEFORE UPGRADE') ;
    out := out || find_unsupported_devices_r7('UNSUPPORTED') ;
    out := out || find_unsupported_modules_r7('UNSUPPORTED F7 MODULE') ;

  ELSE
    RAISE INFO 'Postgres was not used in nms older then 7.x !';
  END IF;
  RETURN out;
END; $$ LANGUAGE plpgsql;
--
--
-- DROP FUNCTION IF EXISTS find_unsupported_devices_db_v7( TEXT, TEXT, TEXT );
CREATE OR REPLACE FUNCTION find_unsupported_devices_db_v7( dev TEXT, ver TEXT, msg TEXT ) RETURNS TEXT AS $$
DECLARE
  out TEXT := '';
  row RECORD;
  currentVer text;
BEGIN
  FOR row IN
    SELECT * -- ne.name0 as network_element_name, ne.typestring as network_element_type, sw.current_swversion as current_version, sw.previous_swversion as previous_version
    FROM cn_network_element ne
    JOIN cn_network_element_swupgrade sw ON ne.swupgradene_id = sw.id
    WHERE ne.typestring = dev
  LOOP
    currentVer := row.current_swversion;
    if compareversions(ver, currentVer) > 0 then
        out := out || msg || ' : '|| row.typestring || ' : ' || row.name0 || ' : ' || row.current_swversion || ' : ' || row.previous_swversion || E'\n';
        -- RAISE info 'UNSUPPORTED: --> %', out;
    END IF;
  END LOOP;
  RETURN out;
END; $$ LANGUAGE plpgsql;
--
--
-- DROP FUNCTION IF EXISTS compareversions( TEXT, TEXT );
CREATE OR REPLACE FUNCTION compareversions(v1 text,v2 text)
    RETURNS smallint
    VOLATILE
    PARALLEL UNSAFE
    COST 100
AS $$
declare res int;
-- Set parts into variables (for now part 1 to 4 are used, 3 for version, 1 for build)
--   - Setting of part(s) to 0 in "Convert all empty or null parts to 0" below
--   - Proper tests in select/case below
-- v1
    declare version_v1 text := split_part(v1, '-', 1);
    declare v1_1 text := split_part(version_v1, '.', 1);
    declare v1_2 text := split_part(version_v1, '.', 2);
    declare v1_3 text := split_part(version_v1, '.', 3);
    declare v1_4 text := split_part(v1, '-',2);
-- v2
    declare version_v2 text := split_part(v2, '-', 1);
    declare v2_1 text := split_part(version_v2, '.', 1);
    declare v2_2 text := split_part(version_v2, '.', 2);
    declare v2_3 text := split_part(version_v2, '.', 3);
    declare v2_4 text := split_part(v2, '-', 2);

begin
    -- Convert all empty or null parts to 0
    -- v1
    if v1_1 = '' or v1_1 is null then v1_1 = '0'; end if;
    if v1_2 = '' or v1_2 is null then v1_2 = '0'; end if;
    if v1_3 = '' or v1_3 is null then v1_3 = '0'; end if;
    if v1_4 = '' or v1_4 is null then v1_4 = '0'; end if;
    -- v2
    if v2_1 = '' or v2_1 is null then v2_1 = '0'; end if;
    if v2_2 = '' or v2_2 is null then v2_2 = '0'; end if;
    if v2_3 = '' or v2_3 is null then v2_3 = '0'; end if;
    if v2_4 = '' or v2_4 is null then v2_4 = '0'; end if;

    select
        case
            -------------
            -- Compare first part:
            --  - If v1_1 is inferior to v2_1 return -1 (v1 < v2),
            --  - If v1_1 is superior to v2_1 return 1 (v1 > v2).
            when CAST(v1_1 as int) < cast(v2_1 as int) then -1
            when CAST(v1_1 as int) > cast(v2_1 as int) then 1
            -------------

            -------------
            -- v1_1 is equal to v2_1, compare second part:
            --  - If v1_2 is inferior to v2_2 return -1 (v1 < v2),
            --  - If v1_2 is superior to v2_2 return 1 (v1 > v2).
            when CAST(v1_2 as int) < cast(v2_2 as int) then -1
            when CAST(v1_2 as int) > cast(v2_2 as int) then 1
            -------------

            -------------
            -- v1_1 is equal to v2_1 and v1_2 is equal to v2_2, compare third part:
            --  - If v1_3 is inferior to v2_3 return -1 (v1 < v2),
            --  - If v1_3 is superior to v2_3 return 1 (v1 > v2).
            when CAST(v1_3 as int) < cast(v2_3 as int) then -1
            when CAST(v1_3 as int) > cast(v2_3 as int) then 1
            -------------

            -------------
            -- Etc..., continuing with fourth part:
            when CAST(v1_4 as int) < cast(v2_4 as int) then -1
            when CAST(v1_4 as int) > cast(v2_4 as int) then 1
            -------------

            -- All parts are equals, meaning v1 == v2, return 0
            else 0
            end
    into res;

    return res;
end;$$ LANGUAGE plpgsql;

--
--
-- DROP FUNCTION IF EXISTS find_unsupported_devices_hn(TEXT);
CREATE OR REPLACE FUNCTION find_unsupported_devices_hn(msg TEXT) RETURNS TEXT AS $$
DECLARE
  out TEXT := '';
  row RECORD;
BEGIN
  FOR row IN
    SELECT *
    FROM cn_network_element ne
    JOIN cn_network_element_swupgrade sw ON ne.swupgradene_id = sw.id
    WHERE ne.typestring like '%HN4000%' AND sw.current_swversion < '6.2.2'
  LOOP
    out := out || msg || ' : ' || row.typestring || ' : ' || row.name0 || ' : ' || row.current_swversion || ' : ' || row.previous_swversion || E'\n';
  END LOOP;
  RETURN out;
END; $$ LANGUAGE plpgsql;
--
--
-- DROP FUNCTION IF EXISTS find_unsupported_devices_825(TEXT);
CREATE OR REPLACE FUNCTION find_unsupported_devices_825(msg TEXT) RETURNS TEXT AS $$
DECLARE
  out TEXT := '';
  row RECORD;
BEGIN
  FOR row IN
    SELECT * -- ne.name0 as network_element_name, ne.typestring as network_element_type, sw.current_swversion as current_version, sw.previous_swversion as previous_version into c, name0, typestring, current_swversion, previous_swversion
    FROM cn_network_element ne
    JOIN cn_network_element_swupgrade sw ON ne.swupgradene_id = sw.id
    WHERE ne.typestring = 'FSP 150CCf-825'
  LOOP
    out := out || msg || ' : ' || row.typestring || ' : ' || row.name0 || ' : ' || row.current_swversion || ' : ' || row.previous_swversion || E'\n';
  END LOOP;
  RETURN out;
END; $$ LANGUAGE plpgsql;
--
--
-- DROP FUNCTION IF EXISTS find_unsupported_devices_r7(TEXT);
CREATE OR REPLACE FUNCTION find_unsupported_devices_r7(msg TEXT) RETURNS TEXT AS $$
DECLARE
  out TEXT := '';
  row RECORD;
BEGIN
  FOR row IN
    SELECT * -- ne.name0 as network_element_name, ne.typestring as network_element_type, sw.current_swversion as current_version, sw.previous_swversion as previous_version into c, name0, typestring, current_swversion, previous_swversion
    FROM cn_network_element ne
    JOIN cn_network_element_swupgrade sw ON ne.swupgradene_id = sw.id
    WHERE ne.type0 = '7'
      AND (sw.current_swversion LIKE '07%'
       OR sw.current_swversion LIKE '08_01%'
       OR sw.current_swversion LIKE '08_02%'
       OR sw.current_swversion LIKE '08_03%'
       OR sw.current_swversion LIKE '09_01%'
       OR sw.current_swversion LIKE '09_02%')
  LOOP
    out := out || msg || ' : ' || row.typestring || ' : ' || row.name0 || ' : ' || row.current_swversion || ' : ' || row.previous_swversion || E'\n';
  END LOOP;
  RETURN out;
END; $$ LANGUAGE plpgsql;

-- DROP FUNCTION IF EXISTS find_unsupported_modules_r7(TEXT);
CREATE OR REPLACE FUNCTION find_unsupported_modules_r7(msg TEXT) RETURNS TEXT AS $$
DECLARE
  out TEXT := '';
  row RECORD;
BEGIN
-- 40CSM3HU are no longer supported (since 8.2)
-- _40CSM3HU_DC_D   166
-- _40CSM3HU_DC_DI  167
-- _40CSM3HU_D      161
-- _40CSM3HU_DI     174
  FOR row IN
    SELECT  ne.typestring, ne.ipaddress, e.aidstring, e.assignedtype
    FROM cn_network_element ne join cn_entity e on (e.ne_id = ne.id)
    WHERE ne.type0 = '7' and e.assignedtype in (166, 167, 161, 174)
  LOOP
    out := out || msg || ' : ' ||  row.ipaddress || ' : ' || row.aidstring  || ' : ' || row.assignedtype || E'\n';
  END LOOP;
  RETURN out;
END; $$ LANGUAGE plpgsql;


--
--
-- DROP FUNCTION IF EXISTS find_unsupported_devices_like(TEXT, TEXT, TEXT);
CREATE OR REPLACE FUNCTION find_unsupported_devices_like(dev TEXT, ver TEXT, msg TEXT ) RETURNS TEXT AS $$
DECLARE
  out TEXT := '';
  row RECORD;
BEGIN
  FOR row IN
    SELECT * -- ne.name0 as network_element_name, ne.typestring as network_element_type, sw.current_swversion as current_version, sw.previous_swversion as previous_version into c, name0, typestring, current_swversion, previous_swversion
    FROM cn_network_element ne
    JOIN cn_network_element_swupgrade sw ON ne.swupgradene_id = sw.id
    WHERE ne.typestring = dev AND  sw.current_swversion like ver
  LOOP
    out := out || msg || ' : ' || row.typestring || ' : ' || row.name0 || ' : ' || row.current_swversion || ' : ' || row.previous_swversion || E'\n';
  END LOOP;
  RETURN out;
END; $$ LANGUAGE plpgsql;

--
--
-- DROP FUNCTION IF EXISTS check_entity_db_impl_relations();
CREATE OR REPLACE FUNCTION check_entity_db_impl_relations() returns TEXT AS $$
DECLARE
  v7 BOOL DEFAULT FALSE;
  out TEXT := '';
  row RECORD;
BEGIN
  SELECT TRUE INTO v7 FROM sr_db_version_info v WHERE v.majorversion >= 7;
  IF v7 THEN
    FOR row IN
      SELECT e1.id,
             mo.jdoclass,
             e1.containedin,
             e1.entityindex,
             e1.aidstring,
             ne1.name0
      FROM cn_managed_object mo
      JOIN cn_entity e1 ON mo.id = e1.id
      JOIN cn_network_element ne1 ON e1.ne_id = ne1.id
      WHERE e1.containedin <> '0'
        AND NOT EXISTS
          (SELECT e2.*
           FROM cn_entity e2
           JOIN cn_network_element ne2 ON e2.ne_id = ne2.id
           WHERE e2.entityindex = e1.containedin
             AND ne1.id = ne2.id)
        AND mo.jdoclass NOT IN ('com.adva.nlms.mediation.config.ShelfDBImpl',
                                'com.adva.nlms.mediation.config.fsp_r7.PtpFSP_R7DBImpl',
                                'com.adva.nlms.mediation.config.fsp_r7.entity.fibermap.TerminationPointFSP_R7DBImpl',
                                'com.adva.nlms.mediation.config.fsp_r7.entity.opticalline.PortFSP_R7VirtualDBImpl',
                                'com.adva.nlms.mediation.config.fsp1500.CPEUnitDBImpl',
                                'com.adva.nlms.mediation.config.RackDBImpl',
                                'ShelfDBImpl',
                                'PtpFSP_R7DBImpl',
                                'TerminationPointFSP_R7DBImpl',
                                'PortFSP_R7VirtualDBImpl',
                                'CPEUnitDBImpl',
                                'RackDBImpl')
    LOOP
      out := out || 'ERROR: Parent entity not found for entity: ' || row.id || ' : ' || row.jdoclass || ' : ' || row.containedin || ' : '|| row.entityindex || ' : ' || row.name0 || E'\n';
    END LOOP;
  ELSE
    RAISE INFO 'Postgres was not used in nms older then 7.x !';
  END IF;
  RETURN out;
END; $$ LANGUAGE plpgsql;
--
--
-- DROP FUNCTION IF EXISTS check_aps_group_inconsistecies()
CREATE OR REPLACE FUNCTION check_aps_group_inconsistecies() returns TEXT AS $$

DECLARE
  supported_version BOOL DEFAULT FALSE;
  out TEXT := '';
  row RECORD;
BEGIN
  SELECT TRUE INTO supported_version FROM sr_db_version_info v WHERE (v.majorversion = 8 and v.minorversion >= 2) or (v.majorversion = 9 and v.minorversion < 6);
  IF supported_version THEN
    FOR row IN
      select ae.aidstring as apsgroup_aid, 'workingport_id' as port from cn_apsgroup a
        join cn_entity ae on ae.id = a.id
        left join cn_entity e on a.workingport_id = e.id
      where a.workingport_id > 0 and e.id is null
      union
      select ae.aidstring as apsgroup_aid, 'protectionport_id' as port from cn_apsgroup a
        join cn_entity ae on ae.id = a.id
        left join cn_entity e on a.protectionport_id = e.id
      where a.protectionport_id > 0 and e.id is null
    LOOP
      out := out || 'ERROR: inconsistent port on APS GROUP: ' || row.apsgroup_aid || ' ,port: ' || row.port || ' ' || E'\n';
    END LOOP;
  END IF;
  RETURN out;
END; $$ LANGUAGE plpgsql;
--
CREATE OR REPLACE FUNCTION check_mac_address_duplications() returns TEXT AS $$
DECLARE
  supported_version BOOL DEFAULT FALSE;
  out TEXT := '';
  row RECORD;
BEGIN
  SELECT TRUE INTO supported_version FROM sr_db_version_info v WHERE (v.majorversion = 8 and v.minorversion >= 4) or v.majorversion >= 9;
  IF supported_version THEN
    FOR row IN
      select lldp.chassisid mac_address, ne.name0 ne_name, ne.ipaddress
	      from cn_lldp_system_data lldp,
	           cn_network_element ne,
            (select
              ll.chassisid, ll.chassisidsubtype, count(*) repetitions_count
            from cn_lldp_system_data ll
            group by ll.chassisid, ll.chassisidsubtype
            HAVING count(*) > 1) macs
        where
          lldp.chassisid = macs.chassisid
          and ne.id = lldp.ne_id
        order by lldp.chassisid
    LOOP
      out := out || 'ERROR: duplicate mac address: ' || row.mac_address || ' ,NE name: ' || row.ne_name || ' ,IP: ' || row.ipaddress || E'\n';
    END LOOP;
  END IF;
  RETURN out;
END; $$ LANGUAGE plpgsql;
--
-- DROP FUNCTION IF EXISTS check_cn_network_element_table();
CREATE OR REPLACE FUNCTION  check_cn_network_element_table() returns TEXT AS $$
DECLARE
  row1 RECORD;
  row2 RECORD;
  row3 RECORD;
  out TEXT := '';
BEGIN
  IF Not EXISTS(SELECT * FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS rc WHERE rc.CONSTRAINT_NAME ='cn_network_element_fk_p2l' AND rc.TABLE_CATALOG='fnm' AND rc.TABLE_NAME='cn_network_element') THEN
    out := out || E'Peer with broken relation to local:\n';
    out := out || E'A) NEs in cycle (ne.local_id=ne.id):\n';
    FOR row1 IN
      SELECT ne.id,
             ne.ipaddress,
             ne.name0,
             ne.local_id
      FROM cn_network_element ne
      WHERE local_id = id
    LOOP
      out := out || row1.id || ' : ' || row1.ipaddress || ' : ' || row1.name0 || ' : ' || row1.local_id || E'\n';
    END LOOP;

    out := out || E'B) PEER NE points to not existing LOCAL NE:\n';
    FOR row2 IN
      SELECT ne.id,
             ne.ipaddress,
             ne.name0,
             ne.local_id,
             ne.jdoclass
      FROM cn_network_element ne
      WHERE ne.jdoclass LIKE '%EFM%'
        AND ne.local_id NOT IN
          (SELECT id
           FROM cn_network_element)
    LOOP
      out := out ||  row2.id || ' : ' || row2.ipaddress || ' : ' || row2.name0 || ' : ' || row2.local_id || ' : ' || row2.jdoclass || E'\n';
    END LOOP;

    out := out || E'C) PEER NE has no reference to LOCAL NE:\n';
    FOR row3 IN
      SELECT ne.id,
             ne.ipaddress,
             ne.name0,
             ne.local_id,
             ne.jdoclass
      FROM cn_network_element ne
      WHERE ne.jdoclass LIKE '%EFM%'
        AND local_id IS NULL
    LOOP
      out := out ||  row3.id || ' : ' || row3.ipaddress || ' : ' || row3.name0 || ' : ' || row3.local_id || ' : ' || row3.jdoclass || E'\n';
    END LOOP;
  END IF;
  RETURN out;
END;  $$ LANGUAGE plpgsql;
--
--
-- DROP FUNCTION IF EXISTS find_unsupported_devices_hn(TEXT);
CREATE OR REPLACE FUNCTION find_entities_with_invalid_ne_reference() RETURNS TEXT AS $$
DECLARE
  v8 BOOL DEFAULT FALSE;
  out TEXT := '';
  row RECORD;
BEGIN
  SELECT TRUE INTO v8 FROM sr_db_version_info v WHERE v.majorversion >= 8;
  IF v8 THEN
    FOR row IN
      SELECT * FROM cn_managed_object mo WHERE ne_id<=0
    LOOP
      out := out || 'ERROR: Invalid ne_id found for managed_object: ' || ' : ' || row.id || ' : ' || row.jdoclass || ' : ' || row.entityindex ||  ' : ' || row.ne_id || ' ' || '\n';
    END LOOP;
  ELSE
    FOR row IN
      SELECT m.id,m.jdoclass,m.ne_id,coalesce(e.entityindex, '') as eid FROM cn_entity e RIGHT OUTER JOIN cn_managed_object m  ON (e.id=m.id) WHERE m.ne_id<=0
    LOOP
      out := out || 'ERROR: Invalid ne_id found for managed_object: ' || ' : ' || row.id || ' : ' || row.jdoclass || ' : ' || row.eid ||  ' : ' || row.ne_id || ' ' || '\n';
    END LOOP;
  END IF;
  RETURN out;
END; $$ LANGUAGE plpgsql;
--
\echo
\echo '=========================================================================================='
\echo '=                                                                                        ='
\echo '=  SEARCHING FOR DB INCONSISTENCIES. PLEASE CONTACT TECH SUPPORT TEAM IF ANY ARE FOUND!  ='
\echo '=                                                                                        ='
\echo '=========================================================================================='
\echo
\echo 'Searching for not supported devices:'
\echo
\pset footer off
-- Call prepared functions
SELECT find_unsupported_devices();
SELECT check_aps_group_inconsistecies();

\echo '=========================================================================================='
\echo '=                                                                                        ='
\echo '=  SEARCHING FOR ORPHAN ENTITIES. FNM UPGRADE WILL FAIL IF THERE ARE ANY!                ='
\echo '=                                                                                        ='
\echo '=========================================================================================='
\echo
--
SELECT check_entity_db_impl_relations();
SELECT check_cn_network_element_table();
SELECT find_entities_with_invalid_ne_reference();
SELECT check_mac_address_duplications();

\echo '====================================================================================='
-- print optical channels on F7 nodes which are missing network ptp information
\echo 'Services on FSP3000 R7 nodes where the optical channels are missing network ptp information:'

--
--
-- DROP FUNCTION IF EXISTS check_FSP3000R7_services_missing_network_port_ptp;
CREATE OR REPLACE FUNCTION check_FSP3000R7_services_missing_network_port_ptp() returns TEXT AS $$
DECLARE
  aid_check BOOL DEFAULT FALSE;
  id_check BOOL DEFAULT FALSE;
  out TEXT := '';
  row RECORD;
BEGIN
  SELECT TRUE INTO aid_check FROM sr_db_version_info v WHERE v.majorversion =8 and v.minorversion >=4;
  SELECT TRUE INTO id_check FROM sr_db_version_info v WHERE v.majorversion <=8 and v.minorversion <4;
  IF aid_check THEN
    FOR row IN
       SELECT c.label,
              o.subchconn_id
       FROM cn_connection c,
            cn_conn_ochconns o
       WHERE c.id = o.subchconn_id
         AND o.optchconn_id IN
           (SELECT id
            FROM cn_connection
            WHERE jdoclass LIKE '%OCh%'
              AND ((startmodule_id IN
                      (SELECT id
                       FROM cn_entity
                       WHERE ne_id IN
                           (SELECT id
                            FROM cn_network_element
                            WHERE type0 = '7')))
                   OR (peermodule_id IN
                         (SELECT id
                          FROM cn_entity
                          WHERE ne_id IN
                              (SELECT id
                               FROM cn_network_element
                               WHERE type0 = '7'))))
              AND (start_network_ptp_aid IS NULL
                   OR peer_network_ptp_aid IS NULL))
     LOOP
       out := out || 'Track ADM OCS: ' || row.label || ' : ' || row.subchconn_id || ', is missing network port ptp information\n';
     END LOOP;
  END IF;
  IF id_check THEN
    FOR row IN
            SELECT c.label,
                   o.subchconn_id
            FROM cn_connection c,
                 cn_conn_ochconns o
            WHERE c.id = o.subchconn_id
              AND o.optchconn_id IN
                (SELECT id
                 FROM cn_connection
                 WHERE jdoclass LIKE '%OCh%'
                   AND ((startmodule_id IN
                           (SELECT id
                            FROM cn_entity
                            WHERE ne_id IN
                                (SELECT id
                                 FROM cn_network_element
                                 WHERE type0 = '7')))
                        OR (peermodule_id IN
                              (SELECT id
                               FROM cn_entity
                               WHERE ne_id IN
                                   (SELECT id
                                    FROM cn_network_element
                                    WHERE type0 = '7'))))
                   AND (start_network_ptp_id IS NULL
                                    OR start_network_ptp_id = 0
                                    OR peer_network_ptp_id IS NULL
                                    OR peer_network_ptp_id = 0))
    LOOP
      out := out || 'Track ADM Service: ' || row.label || ' : ' || row.subchconn_id || ', is missing network port ptp information\n';
    END LOOP;
  END IF;
  RETURN out;
END; $$ LANGUAGE plpgsql;

select check_FSP3000R7_services_missing_network_port_ptp();

\echo '====================================================================================='

-- print connections missing port or module information
\echo 'Services containing optical channels which are missing port or module information:'

SELECT c.label,
       o.subchconn_id
FROM cn_connection c,
     cn_conn_ochconns o
WHERE c.id = o.subchconn_id
  AND o.optchconn_id IN
    (SELECT id
     FROM cn_connection
     WHERE((startmodule_id IN
              (SELECT id
               FROM cn_entity
               WHERE ne_id IN
                   (SELECT id
                    FROM cn_network_element
                    WHERE type0 <> '1500'
                      AND type0 <> '500')))
           OR (peermodule_id IN
                 (SELECT id
                  FROM cn_entity
                  WHERE ne_id IN
                      (SELECT id
                       FROM cn_network_element
                       WHERE type0 <> '1500'
                         AND type0 <> '500'))))
       AND (peermodule_id IS NULL
            OR peermodule_id = 0
            OR startmodule_id IS NULL
            OR startmodule_id = 0));

\echo '====================================================================================='
-- print connections missing port or module information
\echo 'Services which are missing port or module information:'

SELECT label,
       id
FROM cn_connection
WHERE (jdoclass LIKE '%Subch%'
       AND ((startmodule_id IN
               (SELECT id
                FROM cn_entity
                WHERE ne_id IN
                    (SELECT id
                     FROM cn_network_element
                     WHERE type0 <> '1500'
                       AND type0 <> '500')))
            OR (peermodule_id IN
                  (SELECT id
                   FROM cn_entity
                   WHERE ne_id IN
                       (SELECT id
                        FROM cn_network_element
                        WHERE type0 <> '1500'
                          AND type0 <> '500'))))
       AND (peermodule_id IS NULL
            OR peermodule_id = 0
            OR startmodule_id IS NULL
            OR startmodule_id = 0
            OR startport_id IS NULL
            OR startport_id = 0
            OR peerport_id IS NULL
            OR peerport_id = 0));

\echo '====================================================================================='

-- Invalid connection references
\echo 'Modules referencing services which do not exist:'

SELECT n.name0,
       e.aidstring,
       e.id
FROM cn_network_element n,
     cn_entity e
WHERE e.id IN
    (SELECT module_id
     FROM cn_conn_info
     WHERE conn_id NOT IN
         (SELECT id
          FROM cn_connection))
  AND n.id = e.ne_id;

\echo '====================================================================================='

-- print duplicated aid strings
\echo 'List of duplicated aids:'
SELECT ne_id,
       aidstring,
       COUNT(*) AS count
FROM cn_entity
WHERE aidstring IS NOT NULL
GROUP BY ne_id,
         aidstring HAVING COUNT(*)>1;

-- print duplicate entities
\echo '====================================================================================='
\echo 'Duplicate entities have such ids:'
SELECT t2.name0,
       t0.id,
       t1.id
FROM cn_entity t0,
     cn_entity t1,
     cn_network_element t2
WHERE t0.ne_id=t1.ne_id
  AND t0.entityindex=t1.entityindex
  AND t0.id!=t1.id
  AND t2.id=t0.ne_id
GROUP BY t1.id,
         t0.id,
         t2.name0
ORDER BY t2.name0;

-- print cycles of size 1,
\echo 'Entities in cycle size 1 have such ids:'
SELECT id
FROM cn_entity
WHERE containedin=entityindex;

-- print cycles of size 2,
\echo 'Entities in cycle size 2 have such ids:'
SELECT t0.id,
       t1.id
FROM cn_entity AS t0
JOIN cn_entity AS t1 ON t0.ne_id=t1.ne_id
WHERE t0.containedin=t1.entityindex
  AND t1.containedin=t0.entityindex
  AND t0.id!=t1.id
GROUP BY t0.id,
         t1.id;

-- print cycles of size 3, eg. port in slot, slot in module, and module in port
\echo 'Entities in cycle size 3 have such ids:'
SELECT t0.id,
       t1.id,
       t2.id
FROM cn_entity AS t0
JOIN cn_entity AS t1 ON t0.ne_id=t1.ne_id
JOIN cn_entity AS t2 ON t0.ne_id=t2.ne_id
WHERE t0.containedin=t1.entityindex
  AND t1.containedin=t2.entityindex
  AND t2.containedin=t0.entityindex
  AND t0.id!=t1.id
  AND t0.id!=t2.id
GROUP BY t0.id,
         t1.id,
         t2.id;

-- print cycles of size 4
\echo 'Entities in cycle size 4 have such ids:'
SELECT t0.id,
       t1.id,
       t2.id,
       t3.id
FROM cn_entity AS t0
JOIN cn_entity AS t1 ON t0.ne_id=t1.ne_id
JOIN cn_entity AS t2 ON t0.ne_id=t2.ne_id
JOIN cn_entity AS t3 ON t0.ne_id=t3.ne_id
WHERE t0.containedin=t1.entityindex
  AND t1.containedin=t2.entityindex
  AND t2.containedin=t3.entityindex
  AND t3.containedin=t0.entityindex
  AND t0.id!=t1.id
  AND t0.id!=t2.id
  AND t0.id!=t3.id
GROUP BY t0.id,
         t1.id,
         t2.id,
         t3.id;



\echo '====================================================================================='
\echo 'Fdfr ends without parent:'
select m.id, m.shortdescription
from cn_fdfr_end e join cn_managed_object m on e.id=m.id
where e.id not in (
select aend_jdoid from cn_fdfr) and e.id not in (select zend_jdoid from cn_fdfr);

\echo '====================================================================================='
\echo 'Duplicated PG Ports:'
select shortdescription, ne_id, count(*) from cn_managed_object m join cn_pgport_f3 pgp on pgp.id=m.id GROUP by m.shortdescription, m.ne_id having count(*) > 1;

\echo '====================================================================================='
\echo 'Entities which have reference to non existent Network Element:'
select m.id, m.ne_id, m.shortdescription, m.jdoclass from cn_managed_object m WHERE m.ne_id not in (select id from cn_network_element);

-- Causes upgrade failure to 8.4.1 when creating Mtosi name for flow from '/shelf=1/slot=1 ... ' + entity alias
--\echo 'List of entities with too long entity alias'
-- Remove checking of entityAlias length since issue exist in 8.4.1.
-- We do not support direct upgrade from 8.4.1 and earlier releases to current R14.3.1.
-- In additional entityAlias is a text data type which means unlimited char length.
--select id from cn_managed_object where length(entityalias) > 215;

\echo '====================================================================================='
\echo 'List of network elements with duplicate names:'
select name0 as ne_name, count(id) as occurances
FROM cn_network_element
GROUP BY name0
HAVING count(id) > 1
ORDER BY count(id);

DROP FUNCTION IF EXISTS find_unsupported_devices();
DROP FUNCTION IF EXISTS find_unsupported_devices_db_v7( TEXT, TEXT,TEXT );
DROP FUNCTION IF EXISTS find_unsupported_devices_r7(TEXT);
DROP FUNCTION IF EXISTS find_unsupported_devices_825(TEXT);
DROP FUNCTION IF EXISTS find_unsupported_devices_like(TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS find_unsupported_devices_hn(TEXT);
DROP FUNCTION IF EXISTS check_entity_db_impl_relations();
DROP FUNCTION IF EXISTS check_cn_network_element_table();
DROP FUNCTION IF EXISTS find_entities_with_invalid_ne_reference();
DROP FUNCTION IF EXISTS check_FSP3000R7_services_missing_network_port_ptp();
DROP FUNCTION IF EXISTS check_mac_address_duplications();
DROP FUNCTION IF EXISTS check_aps_group_inconsistecies();
