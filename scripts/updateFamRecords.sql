--
-- Copyright 2023 Adtran Networks SE. All rights reserved.
--
-- Owner: and<PERSON><PERSON>s
--
CREATE OR REPLACE FUNCTION  add_point(fr fam_record) RETURNS TEXT AS $$
DECLARE
out TEXT := '';
next_sequence_id INT DEFAULT 0;
point fam_point % ROWTYPE;
ne_name TEXT := '';
ip_address VARCHAR(255) := '';
aid VARCHAR(255) := '';
alias VARCHAR(255) := '';
BEGIN
SELECT INTO point * FROM fam_point WHERE neid = fr.neid AND portid = fr.portid LIMIT 1;

IF point IS NULL THEN
  out := out || format(E'No Fam point, creating new one for ne(%s) port(%s) \n', fr.neid, fr.portid);
  SELECT
    ne.name0,
    ne.ipaddress,
    (CASE WHEN e.aidstring IS NULL THEN '' ELSE e.aidstring END),
    mo.entityalias
  FROM cn_network_element ne
    JOIN cn_managed_object mo ON mo.ne_id = ne.id
    JOIN cn_entity e ON e.id = mo.id
    JOIN cn_port p ON p.id = e.id
  WHERE ne.id = fr.neid AND p.portno = fr.portid
  INTO ne_name, ip_address, aid, alias;

  SELECT sequence_value FROM jdo_sequence WHERE id = 'DEFAULT' INTO next_sequence_id;

	IF fr.type = 0 THEN
		INSERT INTO fam_point(id, neid, portid, ip_address, ne_name, port_aid, port_name, fp_record, fa_record)
		  VALUES(next_sequence_id, fr.neid, fr.portid, ip_address, ne_name, aid, alias, fr.id, NULL);
	ELSE
		INSERT INTO fam_point(id, neid, portid, ip_address, ne_name, port_aid, port_name, fp_record, fa_record)
		  VALUES(next_sequence_id, fr.neid, fr.portid, ip_address, ne_name, aid, alias, NULL, fr.id);
	END IF;

	next_sequence_id = next_sequence_id + 1;
	UPDATE jdo_sequence SET sequence_value = next_sequence_id WHERE id = 'DEFAULT';
ELSE
	IF fr.type = 0 THEN
		out := out || format(E'Updating existing point(%s) with FP(%s)\n', point.id, fr.id);
		UPDATE fam_point SET fp_record = fr.id WHERE id = point.id;
	ELSE
		out := out || format(E'Updating existing point(%s) with FA(%s)\n', point.id, fr.id);
		UPDATE fam_point SET fa_record = fr.id WHERE id = point.id;
	END IF;
END IF;

RETURN out;
END;$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_fam() RETURNS TEXT AS $$
DECLARE
out TEXT := '';
r fam_record % ROWTYPE;
curs CURSOR FOR SELECT * FROM fam_record;

BEGIN
OPEN curs;

LOOP
  FETCH curs INTO r;
  EXIT WHEN NOT FOUND;
  out := out || add_point(r);
END LOOP;

CLOSE curs;
out := out || 'DONE, Will now exit.';
RETURN out;
END;$$ LANGUAGE plpgsql;

SELECT update_fam();
DROP FUNCTION IF EXISTS update_fam();
DROP FUNCTION IF EXISTS add_point(fr fam_record);