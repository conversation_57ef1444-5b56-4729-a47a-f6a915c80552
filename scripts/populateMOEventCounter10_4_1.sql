CREATE OR REPLACE FUNCTION selectLastIdForNESystem(localNeId integer) RETURNS INTEGER AS
$BODY$
DECLARE
result integer;
BEGIN
  select max(id) from ev_event where sourceneid in (
    select id from cn_network_element where id = localNeId
	  union select id from cn_network_element where local_id=localNeId
	  )
	INTO result;
	RAISE NOTICE 'selectLastIdForNESystem for NE % %', localNeId, result;
	Return result;
END
$BODY$
LANGUAGE 'plpgsql' ;

CREATE OR REPLACE FUNCTION updateMoEventProcessed(neType VARCHAR) RETURNS VOID AS
$BODY$
DECLARE
    r cn_network_element%rowtype;
BEGIN
    RAISE NOTICE 'updateMoEventProcessed for NE %', neType;
    FOR r IN SELECT * FROM cn_network_element where type0 = neType
    LOOP
      IF r.local_id IS NULL THEN
	      UPDATE cn_sync_event_data set events_processed = selectLastIdForNESystem(r.id) WHERE ne_id = r.id;
	    END IF;
    END LOOP;
    RETURN;
END
$BODY$
LANGUAGE 'plpgsql' ;

SELECT updateMoEventProcessed('153');

DROP FUNCTION IF EXISTS selectLastIdForNESystem(integer);
DROP FUNCTION IF EXISTS updateMoEventProcessed(VARCHAR);