--
-- Copyright 2023 Adtran Networks SE. All rights reserved.
--
-- Owner: tomaszm
--
-- $Id: updateContainedIn.sql 91650 2015-08-12 17:22:29Z msibila $
--
CREATE TABLE tmp_entity_idx_data(mo_id int, entityIdx varchar(255), containedIn varchar(255));

CREATE OR REPLACE FUNCTION create_contained_in(ei_category integer, no_of_idx integer, entity_index VARCHAR(255), last_idx_offset integer)
  RETURNS VARCHAR(255)
AS $$
DECLARE
  result VARCHAR(255);
  split_index text[];
BEGIN
  result := concat(ei_category, '^');
  split_index := regexp_split_to_array(substring(entity_index from position('^' in entity_index)+1), '\.');

  FOR i IN 1..no_of_idx LOOP
    IF last_idx_offset <> 0 AND i = no_of_idx THEN split_index[i] := cast(cast(split_index[i] as INTEGER) + last_idx_offset as TEXT);
    END IF;
    IF i=1 THEN
      result := concat(result, split_index[i]);
    ELSE
      result := concat_ws('.', result, split_index[i]);
    END IF;
  END LOOP;

  RETURN result;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE FUNCTION get_contained_in(t_id integer, ne_type varchar(255), entity_index varchar(255))
  RETURNS varchar(255)
AS $$
DECLARE
  resultContainedIn varchar(255);
BEGIN

  IF entity_index like '38^1.1.254.%'
    THEN
      IF ne_type in ('153', '154', '5000', '115', '5411', '201', '2010', '206', '2060', '2061', '1804', '3204') THEN
        resultContainedIn := create_contained_in(86, 4, concat('38^1.1.1.', substring(entity_index FROM '.$')), 3);
      ELSE
        resultContainedIn := create_contained_in(86, 4, concat('38^1.1.1.', substring(entity_index FROM '.$')), 0);
      End IF;

  ELSEIF entity_index like '38^%' AND ne_type in ('153', '154')
    THEN resultContainedIn := create_contained_in(86, 4, entity_index, 2);
  ELSEIF entity_index like '38^%' AND ne_type in ('5000')
    THEN resultContainedIn := create_contained_in(86, 4, entity_index, 3);
  -- entity index: 38^1.1.16.1, containedIn: 14^1.1.11
  ELSEIF entity_index like '38^1.1.1.%' AND ne_type in ('115', '5411', '201', '2010', '206', '2060', '2061', '1804', '3204')
    THEN resultContainedIn := create_contained_in(86, 4, entity_index, 3);

  -- entity index: 38^1.1.11.2, containedIn: 14^1.1.11
  ELSEIF entity_index like '38^%'
    THEN resultContainedIn := create_contained_in(86, 4, entity_index, 0);
  -- entity index: 41^1.1.11.2, containedIn: 14^1.1.11

  ELSEIF entity_index like '41^1.1.254.%'
    THEN resultContainedIn := create_contained_in(86, 4, concat('41^1.1.1.', substring(entity_index FROM '.$')), 0);
  ELSEIF entity_index like '41^%'
    THEN resultContainedIn := create_contained_in(86, 4, entity_index, 0);
  ELSE
    resultContainedIn := null;
  END IF;

  RETURN resultContainedIn;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION generate_and_store_contained_in_tmp_table()
  RETURNS VOID
AS $$
BEGIN
  INSERT INTO tmp_entity_idx_data
    SELECT
      m.id,
      m.entityindex,
      get_contained_in(m.id, n.type0, m.entityindex)
    FROM cn_managed_object m JOIN cn_entity e ON m.id = e.id
      JOIN cn_network_element n ON m.ne_id = n.id
    WHERE
      n.type0 in ('206', '2060', '2061', '201', '2010', '110', '4660', '112', '114', '1140', '1141', '1142', '1143', '115', '210', '153', '154')
      AND
      m.jdoclass in ('PortF3AccDBImpl', 'PortFSPGE20XAccDBImpl', 'PortF3NetDBImpl', 'PortFSPGE20XNetDBImpl');
  RETURN;
END;
$$ LANGUAGE plpgsql;

-- main

SELECT generate_and_store_contained_in_tmp_table();
-- SELECT * from tmp_entity_idx_data;

UPDATE cn_entity e
SET containedin=tmp.containedIn
FROM tmp_entity_idx_data tmp
WHERE tmp.mo_id=e.id and tmp.containedIn is not null;


DROP FUNCTION IF EXISTS create_contained_in(integer, integer, VARCHAR(255), integer);
DROP FUNCTION IF EXISTS get_contained_in(integer, varchar(255), varchar(255));
DROP FUNCTION IF EXISTS generate_and_store_contained_in_tmp_table();
DROP TABLE IF EXISTS tmp_entity_idx_data;

