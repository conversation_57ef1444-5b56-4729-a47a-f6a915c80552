create or replace function create_constraint_if_not_exists (t_name text, c_name text, constraint_sql text) returns void AS
$$
begin
    if not exists (select constraint_name
                   from information_schema.constraint_column_usage
                   where table_name = t_name  and constraint_name = c_name) then
        execute constraint_sql;
    end if;
end;
$$ language 'plpgsql';

CREATE TABLE IF NOT EXISTS activemq_msgs (id bigint NOT NULL,container character varying(250),msgid_prod character varying(250),msgid_seq bigint,expiration bigint, msg bytea,priority bigint,xid bytea);
ALTER TABLE public.activemq_msgs OWNER TO adva;
SELECT create_constraint_if_not_exists('activemq_msgs','activemq_msgs_pkey','ALTER TABLE ONLY activemq_msgs ADD CONSTRAINT activemq_msgs_pkey PRIMARY KEY (id);');


CREATE TABLE IF NOT EXISTS activemq_acks (container character varying(250) NOT NULL,sub_dest character varying(250), client_id character varying(250) NOT NULL, sub_name character varying(250) NOT NULL,selector character varying(250),last_acked_id bigint, priority bigint DEFAULT 5 NOT NULL, xid bytea);
ALTER TABLE public.activemq_acks OWNER TO adva;
SELECT create_constraint_if_not_exists('activemq_acks','activemq_acks_pkey','ALTER TABLE ONLY activemq_acks ADD CONSTRAINT activemq_acks_pkey PRIMARY KEY (container, client_id, sub_name, priority);');


CREATE TABLE IF NOT EXISTS activemq_lock (id bigint NOT NULL,"time" bigint,broker_name character varying(250));
ALTER TABLE public.activemq_lock OWNER TO adva;
SELECT create_constraint_if_not_exists('activemq_lock','activemq_lock_pkey','ALTER TABLE ONLY activemq_lock ADD CONSTRAINT activemq_lock_pkey PRIMARY KEY (id);');

DROP FUNCTION create_constraint_if_not_exists (t_name text, c_name text, constraint_sql text);


