DELETE FROM ml_cp_mon_layers;
DELETE FROM ml_vlan_values;
DELETE from ml_bw_resources;
DELETE from ml_monitoring_section;
DELETE from ml_monitoring_section_entities;
DELETE from ml_service_entities;
DELETE from ml_topology_element;
DELETE from ml_path_seq;
DELETE from ml_path;
DELETE from ml_connection;
DELETE from ml_adaptation;
DELETE from ml_adaptation_client;
DELETE from ml_adaptation_server;
DELETE from ml_connection_point;
DELETE from ml_connection_reservation;
DELETE from ml_connection_resource;
DELETE from ml_cp_term_layers;
DELETE from ml_cp_mon_layers;
DELETE from ml_node;
DELETE from ml_module;
DELETE from ml_service;
DELETE from ml_mtp_conn_point;
DELETE from ml_te_property;
DELETE from ml_topo_mo_ref;
DELETE from ml_vlan_values;
DELETE from ml_res_ts_pair;
DELETE from ml_res_tp_pair;
DELETE from ml_segment_adaptation;
DELETE from ml_segment_client;
DELETE from ml_segment_path;
DELETE from ml_ui_service;
DELETE FROM ml_ser_view_lines;
DELETE FROM ml_ser_view_nes;
DELETE FROM ml_ser_view_subnets;

DELETE from ml_bw_profile;
DELETE FROM ml_cfm_entry;
DELETE FROM ml_cfmentry_pvids;
DELETE FROM ml_cfmentry_rmeps;
DELETE FROM ml_intent_conn_point_entry;
DELETE FROM ml_service_intent;
DELETE FROM ml_sip_services;
DELETE FROM ml_sips;
DELETE FROM ml_layer_extension;
DELETE FROM ml_service_endpoint;
DELETE from ml_channel_values;
