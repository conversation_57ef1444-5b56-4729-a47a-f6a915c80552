-- recalculation of Service operational state counters for the Service tree
-- recalculation of severity counters for NEs and Subnets
--
-- Host: localhost    Database: fnm
-- ------------------------------------------------------

-- -------------------------------------------------------------------------------------

SELECT 'OPER-STATE SG/CUST/CG COUNTER';

INSERT INTO cn_conn_state_counter (id, jdoversion, okcnt, degradedcnt, failedcnt, niscnt, unknowncnt)
SELECT grpid AS id, 1 AS jdoversion, 0 AS okcnt, 0 AS degradedcnt, 0 AS failedcnt, 0 AS niscnt, 0 AS unknowncnt
 FROM (SELECT COALESCE(c.servicegroup_id, c.customer_id) grpid FROM cn_connection c WHERE COALESCE(c.servicegroup_id, c.customer_id) > 0 AND ishidden=false
     UNION
     SELECT servicegroup_id grpid FROM ml_service s WHERE s.servicegroup_id > 0
     UNION
     SELECT serviceintent_id grpid FROM cn_connection c WHERE c.serviceintent_id > 0 AND ishidden=false
     UNION
     SELECT contract_id grpid FROM ml_service s WHERE s.contract_id > 0
    ) AS services LEFT OUTER JOIN cn_conn_state_counter cntr ON (services.grpid=cntr.id)
 WHERE cntr.id IS NULL;

UPDATE cn_conn_state_counter SET niscnt=cntr.nis, failedcnt=cntr.faulted, degradedcnt=cntr.degraded, okcnt=cntr.ok, unknowncnt=cntr.unknown
FROM (
   SELECT grpid,
       COUNT(*) FILTER (WHERE oper_state=0) AS nis,
       COUNT(*) FILTER (WHERE oper_state=1) AS faulted,
       COUNT(*) FILTER (WHERE oper_state=2) AS degraded,
       COUNT(*) FILTER (WHERE oper_state=3) AS ok,
       COUNT(*) FILTER (WHERE oper_state=4) AS unknown
   FROM (SELECT c.id,COALESCE(c.servicegroup_id, c.customer_id) grpid, operational_status oper_state FROM cn_connection c join sm_operational_status o ON (c.oper_state=o.id) WHERE COALESCE(c.servicegroup_id, c.customer_id) > 0 AND ishidden=false
       UNION
       SELECT id,servicegroup_id grpid, s.oper_state FROM ml_service s WHERE s.servicegroup_id > 0
       UNION
       SELECT c.id,c.serviceintent_id grpid, operational_status oper_state FROM cn_connection c JOIN sm_operational_status o ON (c.oper_state=o.id) WHERE c.serviceintent_id > 0 AND ishidden=false
       UNION
       SELECT id,contract_id grpid, s.oper_state FROM ml_service s WHERE s.contract_id > 0
       UNION
       SELECT 0 AS id, id AS grpid, 6 AS oper_state FROM cn_conn_state_counter
      ) AS services
   GROUP BY grpid) AS cntr
 WHERE id = cntr.grpid;
-- -------------------------------------------------------------------------------------

SELECT 'ALARM NETWORK COUNTER';

-- -------------------------------------------------------------------------------------

-- clear counters and reset highest severity
UPDATE ev_counter c
SET    cr_total = 0, cr_unackn = 0,
       mj_total = 0, mj_unackn = 0,
       mn_total = 0, mn_unackn = 0,
       highestseveritytype=6
WHERE  c.objtype=7;

-- PostgreSQL command
UPDATE ev_counter c
SET    cr_total = evt.cr_total, cr_unackn = evt.cr_unackn,
        mj_total = evt.mj_total, mj_unackn = evt.mj_unackn,
        mn_total = evt.mn_total, mn_unackn = evt.mn_unackn,
        highestseveritytype=evt.hs
FROM   (SELECT e.sourceneid AS objID,
                SUM(CASE WHEN e.severity = 0 THEN 1 ELSE 0 END)                                                        AS cr_total,
                SUM(CASE WHEN e.severity = 0 AND ( e.acknowledge = false OR e.acknowledge IS NULL ) THEN 1 ELSE 0 END) AS cr_unackn,
                SUM(CASE WHEN e.severity = 1 THEN 1 ELSE 0 END)                                                        AS mj_total,
                SUM(CASE WHEN e.severity = 1 AND ( e.acknowledge = false OR e.acknowledge IS NULL ) THEN 1 ELSE 0 END) AS mj_unackn,
                SUM(CASE WHEN e.severity = 2 THEN 1 ELSE 0 END)                                                        AS mn_total,
                SUM(CASE WHEN e.severity = 2 AND ( e.acknowledge = false OR e.acknowledge IS NULL ) THEN 1 ELSE 0 END) AS mn_unackn,
                CASE WHEN SUM(CASE WHEN e.severity = 0 THEN 1 ELSE 0 END) > 0 THEN 0
                     WHEN SUM(CASE WHEN e.severity = 1 THEN 1 ELSE 0 END) > 0 THEN 1
                     WHEN SUM(CASE WHEN e.severity = 2 THEN 1 ELSE 0 END) > 0 THEN 2 ELSE 6 END AS hs
         FROM   ev_event e
         WHERE  e.typ = 0 AND e.sourceneid > 0 AND severity < 3
         GROUP  BY e.sourceneid) AS evt
WHERE  c.objid = evt.objID;

-- -------------------------------------------------------------------------------------

SELECT 'ALARM SUBNET COUNTER';

-- -------------------------------------------------------------------------------------

-- clear counters and reset highest severity
UPDATE ev_counter c
SET    cr_total = 0, cr_unackn = 0,
       mj_total = 0, mj_unackn = 0,
       mn_total = 0, mn_unackn = 0,
       highestseveritytype=6
WHERE  c.objtype=8;

-- PostgreSQL command
UPDATE ev_counter c
SET    cr_total = evt.cr_total, cr_unackn = evt.cr_unackn,
        mj_total = evt.mj_total, mj_unackn = evt.mj_unackn,
        mn_total = evt.mn_total, mn_unackn = evt.mn_unackn,
        highestseveritytype=evt.hs
FROM   (SELECT e.subnetid AS objID,
                SUM(CASE WHEN e.severity = 0 THEN 1 ELSE 0 END)                                                        AS cr_total,
                SUM(CASE WHEN e.severity = 0 AND ( e.acknowledge = false OR e.acknowledge IS NULL ) THEN 1 ELSE 0 END) AS cr_unackn,
                SUM(CASE WHEN e.severity = 1 THEN 1 ELSE 0 END)                                                        AS mj_total,
                SUM(CASE WHEN e.severity = 1 AND ( e.acknowledge = false OR e.acknowledge IS NULL ) THEN 1 ELSE 0 END) AS mj_unackn,
                SUM(CASE WHEN e.severity = 2 THEN 1 ELSE 0 END)                                                        AS mn_total,
                SUM(CASE WHEN e.severity = 2 AND ( e.acknowledge = false OR e.acknowledge IS NULL ) THEN 1 ELSE 0 END) AS mn_unackn,
                CASE WHEN SUM(CASE WHEN e.severity = 0 THEN 1 ELSE 0 END) > 0 THEN 0
                     WHEN SUM(CASE WHEN e.severity = 1 THEN 1 ELSE 0 END) > 0 THEN 1
                     WHEN SUM(CASE WHEN e.severity = 2 THEN 1 ELSE 0 END) > 0 THEN 2 ELSE 6 END AS hs
         FROM   ev_event e
         WHERE  e.typ = 0 AND e.subnetid > 0 AND severity < 3
         GROUP  BY e.subnetid) AS evt
WHERE  c.objid = evt.objID;

-- -------------------------------------------------------------------------------------

SELECT 'ALARM LINE COUNTER';

-- -------------------------------------------------------------------------------------

-- clear counters and reset highest severity
UPDATE ev_counter c
SET    cr_total = 0, cr_unackn = 0,
       mj_total = 0, mj_unackn = 0,
       mn_total = 0, mn_unackn = 0,
       highestseveritytype=6
WHERE  c.objtype=11;

-- PostgreSQL command
UPDATE ev_counter c
SET    cr_total = evt.cr_total, cr_unackn = evt.cr_unackn,
        mj_total = evt.mj_total, mj_unackn = evt.mj_unackn,
        mn_total = evt.mn_total, mn_unackn = evt.mn_unackn,
        highestseveritytype=evt.hs
FROM   (SELECT e.lineid AS objID,
                SUM(CASE WHEN e.severity = 0 THEN 1 ELSE 0 END)                                                        AS cr_total,
                SUM(CASE WHEN e.severity = 0 AND ( e.acknowledge = false OR e.acknowledge IS NULL ) THEN 1 ELSE 0 END) AS cr_unackn,
                SUM(CASE WHEN e.severity = 1 THEN 1 ELSE 0 END)                                                        AS mj_total,
                SUM(CASE WHEN e.severity = 1 AND ( e.acknowledge = false OR e.acknowledge IS NULL ) THEN 1 ELSE 0 END) AS mj_unackn,
                SUM(CASE WHEN e.severity = 2 THEN 1 ELSE 0 END)                                                        AS mn_total,
                SUM(CASE WHEN e.severity = 2 AND ( e.acknowledge = false OR e.acknowledge IS NULL ) THEN 1 ELSE 0 END) AS mn_unackn,
                CASE WHEN SUM(CASE WHEN e.severity = 0 THEN 1 ELSE 0 END) > 0 THEN 0
                     WHEN SUM(CASE WHEN e.severity = 1 THEN 1 ELSE 0 END) > 0 THEN 1
                     WHEN SUM(CASE WHEN e.severity = 2 THEN 1 ELSE 0 END) > 0 THEN 2 ELSE 6 END AS hs
         FROM   ev_event e
         WHERE  e.typ = 0 AND e.lineid > 0 AND severity < 3
         GROUP  BY e.lineid) AS evt
WHERE  c.objid = evt.objID;
