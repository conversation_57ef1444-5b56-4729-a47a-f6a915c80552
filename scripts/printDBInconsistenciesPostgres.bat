::
:: Copyright 2023 Adtran Networks SE. All rights reserved.
::
:: Owner: ask<PERSON><PERSON><PERSON>
::

@echo off
SET PGDIR="%CURRENTDIR%..\postgres"
SET PG_PASSWORD_FILE="%CURRENTDIR%..\dbaccess.txt"
SET PGPASSWORD=

SETLOCAL ENABLEEXTENSIONS ENABLEDELAYEDEXPANSION
SET PGPASSWORD=NeverChange
SET counter=0
SET max_tries=4
IF EXIST %PG_PASSWORD_FILE% (
    :authenticate_loop
    SET PGPASSWORD=
    SET /A counter=counter+1

    IF %counter% GTR %max_tries% (
        ECHO You provided invalid DB Password too many times. Exiting...
        EXIT /B 1
    )

    SET /P INPUT_PGPASSWORD="Enter DB Password: "
    SET PGPASSWORD=!INPUT_PGPASSWORD!
    %PGDIR%\bin\psql -U adva -c "" fnm

    IF ERRORLEVEL 1 GOTO authenticate_loop
)

%PGDIR%\bin\psql -q -U adva -f printDBInconsistenciesPostgres.sql fnm

pause