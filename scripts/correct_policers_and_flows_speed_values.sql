--
-- Copyright 2023 Adtran Networks SE. All rights reserved.
--
-- Owner: tomaszm
--

CREATE TABLE tmp_entities_resync(
  id integer, ne_id integer, shortdescription varchar(255), jdoclass varchar(255)
);

CREATE OR REPLACE FUNCTION change_entity()
  RETURNS VOID
AS $$
DECLARE
  entity_jdoclass varchar(255);
BEGIN

  FOR entity_jdoclass IN SELECT DISTINCT jdoclass FROM tmp_entities_resync
  LOOP
    IF entity_jdoclass = 'FlowF3DBImpl'
    THEN update cn_flow_cm set guaranteeda2nbandwidthlo=0 where id in
      (SELECT id from tmp_entities_resync t where t.jdoclass = entity_jdoclass);
    ELSEIF entity_jdoclass = 'QOSFlowPolicerDBImpl'
      THEN update cn_eth_flow_policer_f3 set eir=0, cir=0 where id in
      (SELECT id from tmp_entities_resync t where t.jdoclass = entity_jdoclass);
    END IF;
  END LOOP;

END;
$$ LANGUAGE plpgsql;

INSERT INTO tmp_entities_resync
  select m.id, m.ne_id, m.shortdescription, m.jdoclass
  from cn_eth_flow_policer_f3 p join cn_managed_object m on m.id=p.id where cir < 0 or eir < 0;

INSERT INTO tmp_entities_resync
  select m.id, m.ne_id, m.shortdescription, m.jdoclass
  from cn_flow_cm f join cn_managed_object m on m.id=f.id where f.guaranteeda2nbandwidthlo < 0;

SELECT change_entity();

-- mark nes for inventory
UPDATE cn_network_element set coldstarttime=0 where id in (SELECT DISTINCT ne_id from tmp_entities_resync where ne_id is not null);

-- display results
SELECT id, shortdescription as entity_to_be_changed, ne_id as ne_to_inventory, jdoclass from tmp_entities_resync;

DROP TABLE IF EXISTS tmp_entities_resync;

