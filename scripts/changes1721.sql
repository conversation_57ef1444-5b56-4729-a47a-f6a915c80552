ALTER TABLE public.cn_f3timeclock
    ADD COLUMN lock_progress integer,
    ADD COLUMN holdover_ready_progress integer,
    ADD COLUMN time_left_in_eprtc_holdover integer;

ALTER TABLE public.cn_network_element
    ADD COLUMN specifictypestring character varying(255);

CREATE TABLE public.cn_opt_router_plug
(
    id                        integer NOT NULL,
    nrl                       character varying(255),
    unified_admin_state       character varying(255),
    unified_operational_state character varying(255),
    vendor_part_number        character varying(255)
);

CREATE TABLE public.cn_opt_router_port
(
    id                        integer NOT NULL,
    layer_qualifiers          bytea,
    nrl                       character varying(255),
    target_output_power       numeric(20, 2),
    unified_admin_state       character varying(255),
    unified_operational_state character varying(255)
);

CREATE TABLE public.cn_optrouterport_frequencyslot
(
    center_frequency numeric(20, 6),
    port_id          integer,
    slot_width       numeric(20, 2)
);

CREATE TABLE public.cn_optrouterport_wavelength
(
    port_id    integer,
    wavelength numeric(20, 5)
);

ALTER TABLE ONLY public.cn_opt_router_plug
    ADD CONSTRAINT cn_opt_router_plug_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_opt_router_port
    ADD CONSTRAINT cn_opt_router_port_pkey PRIMARY KEY (id);

CREATE INDEX cnptrouterportfrequencyslotfkcnoptrouterportfrequencyslotportid ON public.cn_optrouterport_frequencyslot USING btree (port_id);
CREATE INDEX cnoptrouterportwavelengthfk_cn_optrouterport_wavelength_port_id ON public.cn_optrouterport_wavelength USING btree (port_id);

ALTER TABLE public.reflector_fam_record
    ADD COLUMN inspect_loss1_distance double precision;

ALTER TABLE public.cn_remote_auth_server
    ADD COLUMN certificate_identity character varying(255);

ALTER TABLE public.pd_bw_profile
    ADD COLUMN buffersize bigint,
    ADD COLUMN policingenabled boolean;

ALTER TABLE public.cn_port_f7
    ADD COLUMN physicalportconstellation integer,
    ADD COLUMN physicalportfreqoffset integer,
    ADD COLUMN physicalportodutriblayout integer;

CREATE TABLE public.cn_ptp_system_slaves (
     curr_num_static_slaves integer,
     id integer NOT NULL,
     max_static_slaves_allowed integer,
     max_uni_dyn_slaves_allowed integer,
     ne_index integer,
     ptp_system_slave_index integer,
     shelf_index integer,
     slaves_warn_threshold integer,
     slot_index integer,
     system_slaves_config_enabled integer
);

ALTER TABLE ONLY public.cn_ptp_system_slaves
    ADD CONSTRAINT cn_ptp_system_slaves_pkey PRIMARY KEY (id);