CREATE OR REPLACE FUNCTION createDefaultNIConfiguration() RETURNS VOID AS
$BODY$
DECLARE
    idoffset NUMERIC :=(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT');
BEGIN
 	insert into cn_ni_controller_commons(id, jdoversion) values(idoffset + 1, 1);
	insert into cn_ni_controller(id,jdoversion, current, protocoltype, portnumber, nictrlcommons_id) values(idoffset + 2,1, true, 'HTTP', 8080, idoffset + 1);

	UPDATE JDO_SEQUENCE SET SEQUENCE_VALUE = idoffset + 3 WHERE ID = 'DEFAULT';
    RETURN;
END
$BODY$
LANGUAGE 'plpgsql' ;

select createDefaultNIConfiguration();

DROP FUNCTION IF EXISTS createDefaultNIConfiguration();