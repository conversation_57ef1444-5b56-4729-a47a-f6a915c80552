update ml_topo_mo_ref
set
  descr=
    case
      when reftypestr in ('{NeMOReference}','{ServiceMOReference}','{ServiceTopologyNodeMOReference}','{EntityIndexMOReference}','{PvNeMOReference}','{PvMOReference}')
        then concat(reftypestr, substring(descrstr, '\{\w+\}(\{[^}]+\})'))
      when reftypestr in ('{AidMOReference}')
        then concat(reftypestr, substring(descrstr, '\{\w+\}(\{\d+\})'), substring(descrstr, '\{\w+\}\{\d+\}\{\w+\}(\{[^}]+\})'))
      when reftypestr in ('{LineMOReference}','{CcMOReference}','{IntraNeConnMOReference}','{PvLineMOReference}')
        then concat(reftypestr, substring(descrstr, '\{\w+\}(\{[^}]+\})'), substring(descrstr, '\{\w+\}\{[^}]+\}\{\w+\}(\{[^}]+\})'), substring(descrstr, '\{\w+\}\{[^}]+\}\{\w+\}\{[^}]+\}\{\w+\}(\{[^}]+\})'), substring(descrstr, '\{\w+\}\{[^}]+\}\{\w+\}\{[^}]+\}\{\w+\}\{[^}]+\}\{\w+\}(\{[^}]+\})'))
      else descrstr
    end
from (select id,substring(descr, '^\{\w+\}(\{\w+\})') as reftypestr, substring(descr, '^\{\w+\}\{\w+\}(.*)$') as descrstr from ml_topo_mo_ref)  as tab where ml_topo_mo_ref.id=tab.id;

update ml_connection_point
set
  as_mo_ref=
    case
      when reftypestr in ('{AidMOReference}')
        then concat(reftypestr, substring(descrstr, '\{\w+\}(\{\d+\})'), substring(descrstr, '\{\w+\}\{\d+\}\{\w+\}(\{[^}]+\})'))
      else descrstr
    end
from (select id,substring(as_mo_ref, '^\{\w+\}(\{\w+\})') as reftypestr, substring(as_mo_ref, '^\{\w+\}\{\w+\}(.*)$') as descrstr from ml_connection_point WHERE as_mo_ref IS NOT NULL) as tab where ml_connection_point.id=tab.id;
