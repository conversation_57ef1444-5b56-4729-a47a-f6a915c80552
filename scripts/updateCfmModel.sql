
CREATE OR REPLACE FUNCTION createMaNetAndMaCompForNE(neId integer) RETURNS VOID AS
$BODY$
DECLARE
    r cn_ma%rowtype;
    mo cn_managed_object%rowtype;
    idoffset NUMERIC :=(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT');
    j integer :=1;
    entityIndex varchar;
BEGIN
    FOR r IN SELECT * FROM cn_ma
    WHERE ne_id = neId
    LOOP
	    RAISE NOTICE 'split for indices % % % %', r.mdindex, r.maindex, idoffset, r.id;
	    INSERT INTO cn_ma_net(id, ccminterval, maindex, mdindex, meplist, name, name_format, ne_id) VALUES(
		    idoffset+j, r.ccminterval, r.maindex, r.mdindex, r.meplist, r.name, r.name_format, r.ne_id);
	    SELECT * INTO mo from cn_managed_object WHERE id = r.id;
	    UPDATE cn_managed_object set entityindex = concat('-', cn_managed_object.entityindex) WHERE id = r.id;
	    INSERT INTO cn_managed_object(creationdate, entityalias, entityindex, fulldescription, id, jdoclass, jdoversion, mtosiname, ne_id, pmid, shortdescription, snmpindex)
		    VALUES(mo.creationdate,'', (SELECT concat('96^', r.mdindex,'.', r.maindex)), (SELECT concat('CFM MANET ', r.maindex, ' on MD ', r.mdindex)), idoffset+j, 'MANetDBImpl',1, 'mtosiname', r.ne_id,1, (SELECT concat('CFM MANET-',r.mdindex, '-', r.maindex)),'2'  );

	    INSERT INTO cn_ma_comp(id, componentid, macompnumber, maindex, mdindex, mipcreationcontrol, ne_id, numberofvids, primaryvid) VALUES(
		    idoffset+j+1, r.componentid, 0, r.maindex, r.mdindex, 0, r.ne_id, r.numberofvids, r.primaryvid);
	    INSERT INTO cn_managed_object(creationdate, entityalias, entityindex, fulldescription, id, jdoclass, jdoversion, mtosiname, ne_id, pmid, shortdescription, snmpindex)
		    VALUES(mo.creationdate,'', (SELECT concat('109^', r.mdindex,'.', r.maindex)), (SELECT concat('CFM MACOMP ', r.maindex, ' on MD ', r.mdindex)), idoffset+j+1, 'MACompDBImpl', 1, 'mtosiname', r.ne_id,1, (SELECT concat('CFM MACOMP-',r.mdindex, '-', r.maindex)),'2'  );
      DELETE FROM cn_managed_object where id = r.id;
	    j := j + 2;
	    UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 2 WHERE id LIKE 'DEFAULT';
    END LOOP;
    RETURN;
END
$BODY$
LANGUAGE 'plpgsql' ;



CREATE OR REPLACE FUNCTION getNetworkElementId(neType varchar) RETURNS VOID AS
$BODY$
DECLARE
    r cn_network_element%rowtype;
BEGIN
    FOR r IN SELECT * FROM cn_network_element
    WHERE type0 = neType
    LOOP
	    RAISE NOTICE 'split for NE %', r.ipaddress;
	    PERFORM createMaNetAndMaCompForNE(r.id);
	    DELETE FROM cn_ma where ne_id = r.id;
    END LOOP;
    RETURN;
END
$BODY$
LANGUAGE 'plpgsql' ;


update cn_mep set mafsp150cc825dbimpl = null, mdfsp150cc825dbimpl= null where id in(select id from cn_mep where ne_id in (select id from cn_network_element where type0 = '210'));

update cn_managed_object set jdoclass = 'MEPDBImpl' where id in(
select id from cn_mep where ne_id in (select id from cn_network_element where type0 in('210', '206','2060','2061','201','2010'))
);

update cn_managed_object set jdoclass = 'MDDBImpl' where id in(
select id from cn_md where ne_id in (select id from cn_network_element where type0 in('210', '206','2060','2061','201','2010'))
);


SELECT getNetworkElementId('210');
SELECT getNetworkElementId('206');
SELECT getNetworkElementId('2060');
SELECT getNetworkElementId('2061');
SELECT getNetworkElementId('201');
SELECT getNetworkElementId('2010');

DROP FUNCTION IF EXISTS createMaNetAndMaCompForNE(integer);
DROP FUNCTION IF EXISTS getNetworkElementId(varchar);
