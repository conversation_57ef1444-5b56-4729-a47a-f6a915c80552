ALTER TABLE cn_spectrum_inventory
    ADD COLUMN degree_entity_id integer;

ALTER TABLE public.cn_flowpoint_f4
ADD COLUMN esi_enabled boolean,
ADD COLUMN ethernet_segment_identifier character varying(255);

CREATE TABLE public.cn_l3_flowpoint_f4 (
    ctag character varying(255),
    ctagctl character varying(255),
    egress_shaping_type character varying(255),
    entity character varying(255),
    guaranteed_bw bigint,
    hcosenabled boolean,
    id integer NOT NULL,
    ingress_untag_control boolean,
    max_bw bigint,
    mcosenabled boolean,
    stag character varying(255),
    stagctl character varying(255),
    userlabel character varying(255)
);


CREATE TABLE public.cn_l3_flowpoint_ip_f4 (
    active_ip_v4_addrs bytea,
    active_ip_v6_addrs bytea,
    entity character varying(255),
    fpktfwd boolean,
    id integer NOT NULL,
    ip_v4_addrs bytea,
    ip_v6_addrs bytea,
    ipintf_asscsnc character varying(255),
    ipintf_type character varying(255),
    lastchg character varying(255),
    ldp_enabled boolean,
    mpls_enabled boolean,
    name character varying(255),
    userlabel character varying(255)
);


CREATE TABLE public.cn_vrf_f4 (
    direction character varying(255),
    entity_name character varying(255),
    id integer NOT NULL,
    l3_enable boolean,
    layer character varying(255),
    name character varying(255),
    snc_type character varying(255)
);


CREATE TABLE public.cn_vrf_tplist_f4 (
    index integer,
    tp_list character varying(255),
    vrf_id integer
);


ALTER TABLE ONLY public.cn_l3_flowpoint_f4
    ADD CONSTRAINT cn_l3_flowpoint_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_l3_flowpoint_ip_f4
    ADD CONSTRAINT cn_l3_flowpoint_ip_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_vrf_f4
    ADD CONSTRAINT cn_vrf_f4_pkey PRIMARY KEY (id);


CREATE TABLE public.cn_evpn_f4 (
    arp_snooping_enabled boolean,
    evi integer,
    fld_ukn_unicast_suprr boolean,
    id integer NOT NULL,
    local_bum_label integer,
    local_route_distinguisher character varying(255),
    local_unicast_label integer,
    name character varying(255)
);


CREATE TABLE public.cn_evpn_flows_f4 (
    evpn_id integer,
    flows character varying(255),
    index integer
);


CREATE TABLE public.cn_evpn_local_route_target_f4 (
    evpn_id integer,
    index integer,
    local_route_target character varying(255)
);


ALTER TABLE ONLY public.cn_evpn_f4
    ADD CONSTRAINT cn_evpn_f4_pkey PRIMARY KEY (id);

CREATE TABLE public.cn_l3_qos_policer_f4 (
    adminstate integer,
    cos integer,
    envelope_id character varying(255),
    envelope_rank_id integer,
    id integer NOT NULL,
    operationalstate integer,
    polprof character varying(255),
    secondarystate integer
);


ALTER TABLE ONLY public.cn_l3_qos_policer_f4
    ADD CONSTRAINT cn_l3_qos_policer_f4_pkey PRIMARY KEY (id);

CREATE TABLE public.cn_l3_queue_f4 (
    adminstate integer,
    avg_frame_size integer,
    bandwidth_profile character varying(255),
    buffersize_profile character varying(255),
    cos integer,
    id integer NOT NULL,
    operationalstate integer,
    secondarystate integer,
    type character varying(255)
);

ALTER TABLE public.cn_twamp_session_sender
ADD COLUMN fnm character varying(255),
ADD COLUMN ip_interface character varying(255),
ADD COLUMN reflector_ip character varying(255),
ADD COLUMN session_id character varying(255),
ADD COLUMN source_ip character varying(255);

CREATE TABLE public.cn_ip_interface_f4 (
    active_ip_v4_addrs bytea,
    active_ip_v6_addrs bytea,
    alias character varying(255),
    fnm character varying(255),
    id integer NOT NULL,
    if_type character varying(255),
    ip_v4_addrs bytea,
    ip_v6_addrs bytea,
    mtu integer,
    name character varying(255)
);


ALTER TABLE ONLY public.cn_l3_queue_f4
    ADD CONSTRAINT cn_l3_queue_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_ip_interface_f4
    ADD CONSTRAINT cn_ip_interface_f4_pkey PRIMARY KEY (id);

CREATE INDEX ix_cn_vrf_tplist_f4_fk_cn_vrf_tplist_f4_vrf_id ON public.cn_vrf_tplist_f4 USING btree (vrf_id);

CREATE INDEX cn_spectrum_inventory_fk_cn_spectrum_inventory_degree_entity_id ON public.cn_spectrum_inventory USING btree (degree_entity_id);

CREATE TABLE public.cn_card_cluster_f8 (
    card_list bytea,
    id integer NOT NULL,
    name character varying(255),
    type character varying(255),
    user_label character varying(255)
);


ALTER TABLE ONLY public.cn_card_cluster_f8
    ADD CONSTRAINT cn_card_cluster_f8_pkey PRIMARY KEY (id);
CREATE INDEX ix_cn_evpn_flows_f4_fk_cn_evpn_flows_f4_evpn_id ON public.cn_evpn_flows_f4 USING btree (evpn_id);
CREATE INDEX cnevpnlocalroutetargetf4fkcn_evpn_local_route_target_f4_evpn_id ON public.cn_evpn_local_route_target_f4 USING btree (evpn_id);

ALTER TABLE cn_ec_ctp
    ADD COLUMN orginal_oper_state character varying(255);

ALTER TABLE cn_ec_ptp
    ADD COLUMN orginal_oper_state character varying(255);

UPDATE se_action SET name0 = REPLACE(name0 , 'Ensemble', 'Mosaic') where name0 like '%Ensemble%';
UPDATE se_role SET name0 = REPLACE(name0 , 'Ensemble', 'Mosaic') where name0 like '%Ensemble%';

ALTER TABLE public.cn_card_cluster_f8
    DROP COLUMN IF EXISTS card_list;

CREATE TABLE public.cn_card_cluster_f8_card_list (
    card_list character varying(255),
    cn_card_cluster_f8_id integer
);


CREATE INDEX cncrdclusterf8cardlistfkcncrdclusterf8cardlistcncardclusterf8id ON public.cn_card_cluster_f8_card_list USING btree (cn_card_cluster_f8_id);

ALTER TABLE cn_ec_ptp
    ADD COLUMN err_forward_mode integer;

CREATE SCHEMA IF NOT EXISTS mnc_mpd_layer3;


CREATE TABLE mnc_mpd_layer3.pdl3_ipvpn_service (
    customer_group_uuid uuid,
    customer_uuid uuid,
    id integer NOT NULL,
    lifecycle_state integer,
    parent_id integer,
    performancetemplateid integer,
    service_name character varying(255),
    service_mode integer,
    uuid uuid,
    version integer
);


ALTER TABLE ONLY mnc_mpd_layer3.pdl3_ipvpn_service
    ADD CONSTRAINT pdl3_ipvpn_service_pkey PRIMARY KEY (id);

ALTER TABLE ONLY mnc_mpd_layer3.pdl3_ipvpn_service
    ADD CONSTRAINT pdl3_ipvpn_service_name_key UNIQUE (service_name);

CREATE TABLE mnc_mpd_layer3.pdl3_ipvpn_service_entity_list (
    entity_id integer,
    index integer,
    ipvpn_service_id integer
);


CREATE INDEX pdl3pvpnsrvceentitylistfkpdl3pvpnsrviceentitylistipvpnserviceid ON mnc_mpd_layer3.pdl3_ipvpn_service_entity_list USING btree (ipvpn_service_id);

ALTER TABLE ml_end_point
    ADD COLUMN prot_types character varying(120);

CREATE TABLE public.sm_diversity (
    diversity_type integer,
    id integer NOT NULL,
    trail1_endpoint_a character varying(255),
    trail1_endpoint_z character varying(255),
    trail1_layer integer,
    trail1_ne_a integer,
    trail1_ne_z integer,
    trail2_endpoint_a character varying(255),
    trail2_endpoint_z character varying(255),
    trail2_layer integer,
    trail2_ne_a integer,
    trail2_ne_z integer
);


ALTER TABLE ONLY public.sm_diversity
    ADD CONSTRAINT sm_diversity_pkey PRIMARY KEY (id);

ALTER TABLE public.cn_croma_slc
    ADD COLUMN a_end_slc_inventory character varying(1500),
    ADD COLUMN z_end_slc_inventory character varying(1500);

ALTER TABLE public.ui_usr ALTER COLUMN name TYPE varchar(64);
ALTER TABLE public.ui_usr_sec ALTER COLUMN section TYPE varchar(64);
ALTER TABLE public.cn_ne_backup_properties ADD COLUMN encryption_pwd bytea;
ALTER TABLE public.cn_ne_backup ADD COLUMN encrypted boolean;

ALTER TABLE public.cn_f3timeclock
    ADD COLUMN ho_recovery_threshold bigint,
    ADD COLUMN ho_suspend_time bigint,
    ADD COLUMN max_slew_rate character varying(255),
    ADD COLUMN smooth_ho_recovery integer,
    ADD COLUMN weighting_mode integer;


CREATE TABLE public.pd_evpn_service_entity_list (
    entity_id integer,
    evpn_service_id integer,
    index integer
);

CREATE INDEX pdevpnserviceentitylistfkpdevpnserviceentitylistevpn_service_id ON public.pd_evpn_service_entity_list USING btree (evpn_service_id);

ALTER TABLE ml_otn_link RENAME TO ml_ni_link;

ALTER TABLE public.ml_ni_link ADD COLUMN linktype integer;

ALTER TABLE ml_ni_link RENAME CONSTRAINT ml_otn_link_pkey TO ml_ni_link_pkey;

ALTER INDEX ix_ml_otn_link_fk_ml_otn_link_a_end_id RENAME TO ix_ml_ni_link_fk_ml_ni_link_a_end_id;

ALTER INDEX ix_ml_otn_link_fk_ml_otn_link_z_end_id RENAME TO ix_ml_ni_link_fk_ml_ni_link_z_end_id;

ALTER INDEX ix_ml_service_fk_ml_service_otn_link_id RENAME TO ix_ml_service_fk_ml_service_ni_link_id;

ALTER TABLE public.ml_service RENAME COLUMN otn_link_id TO ni_link_id;

ALTER TABLE public.ml_end_point ADD COLUMN id integer NOT NULL;
ALTER TABLE ONLY public.ml_end_point ADD CONSTRAINT ml_end_point_pkey PRIMARY KEY (id);

ALTER TABLE public.cn_ec_ctp ADD COLUMN constel character varying(255);
ALTER TABLE public.cn_ec_ctp ADD COLUMN freqdet integer;

ALTER TABLE public.cn_ec_ptp_otsi
    ADD COLUMN constel character varying(255),
    ADD COLUMN filter_shape character varying(255),
    ADD COLUMN freqdet integer;

alter table public.cn_ec_ctp
    add column afltrshp character varying(255),
    add column rate integer,
    add column sop character varying(255);

ALTER TABLE public.sm_service
    ADD COLUMN admin_state_eod CHARACTER VARYING(255);
ALTER TABLE public.sm_service
    ADD COLUMN layer_protocol_eod CHARACTER VARYING(255);

CREATE TABLE public.cn_phyots_oms_ctp_f8 (
    actgain integer,
    acttilt integer,
    actvoa integer,
    autogain integer,
    autostatus boolean,
    autotilt integer,
    autovoa integer,
    gain integer,
    gaintype character varying(255),
    id integer NOT NULL,
    nwfbrtyp character varying(255),
    tilt integer,
    voa integer
);

ALTER TABLE ONLY public.cn_phyots_oms_ctp_f8
    ADD CONSTRAINT cn_phyots_oms_ctp_f8_pkey PRIMARY KEY (id);

ALTER TABLE public.cn_ec_ctp
ADD COLUMN sk_id integer,
ADD COLUMN so_id integer;

CREATE INDEX ix_cn_ec_ctp_fk_cn_ec_ctp_sk_id ON public.cn_ec_ctp USING btree (sk_id);
CREATE INDEX ix_cn_ec_ctp_fk_cn_ec_ctp_so_id ON public.cn_ec_ctp USING btree (so_id);

CREATE TABLE public.cn_planner_pm (
    attribute character varying(255),
    entity_id integer,
    historical_index integer,
    id integer NOT NULL,
    module_id integer,
    name character varying(255),
    ne_id integer,
    time_stamp character varying(255),
    uri character varying(255),
    value character varying(255)
);

ALTER TABLE ONLY public.cn_planner_pm
    ADD CONSTRAINT cn_planner_pm_pkey PRIMARY KEY (id);

CREATE INDEX index_cn_planner_pm_ne_id ON public.cn_planner_pm USING btree (ne_id);

ALTER TABLE public.cn_phyots_oms_ctp_f8
ADD COLUMN entityindex character varying(500) DEFAULT NULL::character varying;

ALTER TABLE cn_gps_receiver
    ADD COLUMN port_info character varying(255);

ALTER TABLE public.cn_f3timeclock_ref
    ADD COLUMN weightpercent bigint,
    ADD COLUMN actualweight bigint;

ALTER TABLE public.fam_record ADD COLUMN linklength double precision;
ALTER TABLE public.fam_record ADD COLUMN linkloss double precision;

ALTER TABLE public.cn_ec_ptp_otsi
    ADD COLUMN optical_bandwidth integer,
    ADD COLUMN ec_ctp_otsi_id integer;

CREATE TABLE public.cn_ec_otsi_cpmgt (
    attenuation integer,
    id integer NOT NULL,
    path_loss integer,
    setpoint integer,
    setpoint_deviation integer
);

ALTER TABLE ONLY public.cn_ec_otsi_cpmgt
    ADD CONSTRAINT cn_ec_otsi_cpmgt_pkey PRIMARY KEY (id);

CREATE INDEX ix_cn_ec_ptp_otsi_fk_cn_ec_ptp_otsi_ec_ctp_otsi_id ON public.cn_ec_ptp_otsi USING btree (ec_ctp_otsi_id);

ALTER TABLE public.cn_ec_ptp_otsi
    ADD COLUMN entity_index character varying(500) DEFAULT NULL::character varying,
    ADD COLUMN otsi_id integer;


ALTER TABLE public.cn_ptp_clock_probe
    ADD COLUMN ip_protocol integer,
    ADD COLUMN master_ipv6 character varying(255),
    ADD COLUMN slave_ipv6 character varying(255);

ALTER TABLE public.cn_ptp_network_probe
    ADD COLUMN ip_protocol integer,
    ADD COLUMN master_ipv6 character varying(255),
    ADD COLUMN slave_ipv6 character varying(255);

ALTER TABLE public.cn_ptp_clock_probe_history
    ADD COLUMN ip_protocol integer,
    ADD COLUMN master_ipv6 character varying(255),
    ADD COLUMN slave_ipv6 character varying(255);

ALTER TABLE public.cn_port_clock
    ADD COLUMN parent_wr_calibrated integer,
    ADD COLUMN parent_wr_config integer,
    ADD COLUMN parent_wr_mode_on integer,
    ADD COLUMN wr_calibrated integer,
    ADD COLUMN wr_config integer,
    ADD COLUMN wr_mode integer,
    ADD COLUMN wr_mode_on integer,
    ADD COLUMN wr_port_state integer;


CREATE TABLE public.ml_log_link_dev_endpoints
(
    aid         character varying(255),
    id          bigint  NOT NULL,
    jdoversion  integer,
    log_link_id character varying(255),
    node_id     integer NOT NULL
);

ALTER TABLE ONLY public.ml_log_link_dev_endpoints
    ADD CONSTRAINT ml_log_link_dev_endpoints_pkey PRIMARY KEY (id);

CREATE INDEX mlloglink_dev_endpointsfk_ml_log_link_dev_endpoints_log_link_id ON public.ml_log_link_dev_endpoints USING btree (log_link_id);

CREATE EXTENSION IF NOT EXISTS pg_stat_statements;


CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';

ALTER TABLE se_user
    ADD COLUMN IF NOT EXISTS uuid CHARACTER VARYING(255);
ALTER TABLE public.cn_apsgroup
    ADD COLUMN holdoff integer;

ALTER TABLE public.cn_port_cm
    ADD COLUMN happsfinetuning integer,
    ADD COLUMN ppsinternaldelay integer;