/*
 This script preforms recalculate view procedure.
 It closely follows CustomerViewHdlrImpl.recalculateViews.
*/

create or replace function getVisibleNodes(customerViewId INTEGER, restrictedNodes INTEGER[]) returns INTEGER[] as $$
declare
    subnetId INTEGER;
    subnetIds INTEGER[];
    visible INTEGER[];
    child INTEGER;
    groupChildren INTEGER[];

begin
    subnetIds := ARRAY(SELECT t0.element FROM se_view t1 LEFT OUTER JOIN se_mountedtreenodes t0 ON (t0.id = t1.id) WHERE (t1.id = customerViewId));
    while array_length(subnetIds, 1) > 0 loop
        foreach subnetId in array subnetIds loop
            if array_length(visible,1) IS NULL or not (subnetid= any(visible)) then
                visible:=array_append(visible, subnetId);
            end if;
            subnetIds := array_remove(subnetIds, subnetId);
            select children from groupParentChildIds into groupChildren where idParent=subnetId;
            if found then
                foreach child in array groupChildren loop
                    if array_length(restrictedNodes,1) IS NULL or not (child = any(restrictedNodes)) then
                        subnetIds:=array_append(subnetIds, child);
                    end if;
                end loop;
            end if;
        end loop;
    end loop;
    return visible;
end $$
    language plpgsql;

create or replace function getServiceVisibleNodes(visibleServiceGroups INTEGER[], restrictedNodes INTEGER[]) returns INTEGER[] as $$
declare
    subnetId INTEGER;
    subnetIds INTEGER[];
    visible INTEGER[];
    child INTEGER;
    groupChildren INTEGER[];

begin
    subnetIds := visibleServiceGroups;
    while array_length(subnetIds, 1) > 0 loop
        foreach subnetId in array subnetIds loop
            if array_length(visible,1) IS NULL or not (subnetid= any(visible)) then
                visible:=array_append(visible, subnetId);
            end if;
            subnetIds := array_remove(subnetIds, subnetId);
            select children from serviceGroupParentChildIds into groupChildren where idParent=subnetId;
            if found then
                foreach child in array groupChildren loop
                    if array_length(restrictedNodes,1) is NULL or not (child = any(restrictedNodes)) then
                        subnetIds:=array_append(subnetIds, child);
                    end if;
                end loop;
            end if;
        end loop;
    end loop;
    return visible;
end $$
    language plpgsql;

create or replace function getVisibleCustomers(visibleNodes INTEGER[], restrictedNodes INTEGER[]) returns INTEGER[] as $$
declare
    customers INTEGER[];
begin
    if array_length(restrictedNodes,1) is NULL then
        customers := ARRAY(SELECT id FROM sm_customers WHERE parent_id=any(visibleNodes) AND jdoclass='CustomerDBImpl');
    else
        customers := ARRAY(SELECT id FROM sm_customers WHERE parent_id=any(visibleNodes) AND jdoclass='CustomerDBImpl' AND not id=any(restrictedNodes));
    end if;
    return customers;
end $$
    language plpgsql;

DO $$
    DECLARE
        node INTEGER;
        rootFolderId INTEGER;
        topLevelCustomerFolderId INTEGER;
        customerViewId INTEGER;
        groupParentId INTEGER;
        groupChildren INTEGER[];
        visibleNodes INTEGER[];
        visibleCustomers INTEGER[];
        restrictedNodes INTEGER[];
        visibleServiceGroups INTEGER[];
        jdoclasses TEXT[] := '{CustomerServiceGroupDBImpl, ContractDBImpl, ServiceIntentDBImpl, TrailGroupDBImpl}';

    BEGIN
        SELECT csg.id into rootFolderId FROM sm_customer_service_group csg WHERE parentid = 0;
        SELECT csg.id into topLevelCustomerFolderId FROM sm_customer_service_group csg WHERE parentid = rootFolderId and group_name='Services';

        CREATE TABLE groupParentChildIds(idParent INTEGER,children INTEGER[]);
        FOR groupParentId IN SELECT DISTINCT parentid FROM sm_customer_service_group WHERE iscustomergroup = 'true' AND parentid <> 0 AND group_type <> 5 LOOP
            groupChildren := ARRAY (SELECT id FROM sm_customer_service_group WHERE iscustomergroup = 'true' AND parentid = groupParentId AND group_type <> 5);
            INSERT INTO groupParentChildIds VALUES (groupParentId, groupChildren);
        END LOOP;

        CREATE TABLE serviceGroupParentChildIds(idParent INTEGER,children INTEGER[]);
        FOR groupParentId IN SELECT DISTINCT parentid FROM sm_customer_service_group WHERE iscustomergroup = 'false' AND parentid <> 0 AND group_type <> 5 AND jdoclass=ANY(jdoclasses) LOOP
            groupChildren := ARRAY (SELECT id FROM sm_customer_service_group WHERE iscustomergroup = 'false' AND parentid = groupParentId AND group_type <> 5  AND jdoclass=ANY(jdoclasses));
            INSERT INTO serviceGroupParentChildIds VALUES (groupParentId, groupChildren);
        END LOOP;

        FOR customerViewId IN SELECT DISTINCT t1.id FROM se_visibletreenodes t0, se_view t1 WHERE t0.temp_element = topLevelCustomerFolderId AND t0.id = t1.id LOOP
            restrictedNodes := ARRAY(SELECT DISTINCT t0.element FROM se_view t1 LEFT OUTER JOIN se_restrictedtreenodes t0 ON (t0.id = t1.id) WHERE (t1.id = customerViewId));
            if TRUE = ANY (SELECT unnest(restrictedNodes) IS NULL) then
                restrictedNodes := array[]::integer[];
            end if;

            visibleNodes := getVisibleNodes(customerViewId, restrictedNodes);
            RAISE NOTICE 'View id: %, visible nodes: %', customerViewId, array_length(visibleNodes, 1);

            visibleCustomers := getVisibleCustomers(visibleNodes, restrictedNodes);
            RAISE NOTICE 'View id: %, visible customer groups nodes: %', customerViewId, array_length(visibleCustomers, 1);

            visibleServiceGroups := getServiceVisibleNodes(visibleCustomers, restrictedNodes);
            RAISE NOTICE 'View id: %, visible service groups nodes: %', customerViewId, array_length(visibleServiceGroups, 1);

            foreach node in array visibleCustomers loop
                if not (node = any(visibleNodes)) then
                    visibleNodes:=array_append(visibleNodes, node);
                end if;
            end loop;

            foreach node in array visibleServiceGroups loop
                if not (node = any(visibleNodes)) then
                    visibleNodes:=array_append(visibleNodes, node);
                end if;
            end loop;

            RAISE NOTICE 'View id: %, set nodes size: %', customerViewId, array_length(visibleNodes, 1);
            delete from se_visibletreenodes where id=customerViewId;
            foreach node in array visibleNodes loop
                insert into se_visibletreenodes (element, id, temp_element) values (node, customerViewId, node);
            end loop;

        END LOOP;
        DROP TABLE IF EXISTS groupParentChildIds;
        DROP TABLE IF EXISTS serviceGroupParentChildIds;
        DROP FUNCTION IF EXISTS getVisibleCustomers(visibleNodes INTEGER[], restrictedNodes INTEGER[]);
        DROP FUNCTION IF EXISTS getServiceVisibleNodes(visibleServiceGroups INTEGER[], restrictedNodes INTEGER[]);
        DROP FUNCTION IF EXISTS getVisibleNodes(customerViewId INTEGER, restrictedNodes INTEGER[]);
    END $$