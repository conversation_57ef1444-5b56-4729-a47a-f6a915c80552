--
-- Copyright 2023 Adtran Networks SE. All rights reserved.
--
-- Owner: tomaszm
--

CREATE TABLE tmp_duplicated_data(
  shortdescription varchar(255), ne_id integer
);

CREATE TABLE tmp_duplicated_ids(
  dup_id integer
);

INSERT INTO tmp_duplicated_data
  select m.shortdescription, m.ne_id
  from cn_managed_object m
    join cn_pgport_f3 pgp on pgp.id=m.id
  GROUP by m.shortdescription, m.ne_id having count(*) > 1;

INSERT INTO tmp_duplicated_ids
  select m.id
  from cn_managed_object m
    join tmp_duplicated_data dd on dd.shortdescription=m.shortdescription and dd.ne_id=m.ne_id;


delete from cn_pgport_f3 where id in (SELECT dup_id from tmp_duplicated_ids);
update cn_network_element set coldstarttime=0 where id in (SELECT DISTINCT ne_id from tmp_duplicated_data);

-- SELECT * from tmp_duplicated_ids;
SELECT shortdescription as removed_pg_port, ne_id as ne_marked_for_inventory from tmp_duplicated_data;

DROP TABLE IF EXISTS tmp_duplicated_data;
DROP TABLE IF EXISTS tmp_duplicated_ids;
