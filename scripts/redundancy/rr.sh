#!/bin/bash
# Owner: gfilip
#


# Part of High Availability framework.
# Updates database from dump file and restarts mediation server.

. /etc/setenv.sh

# Settings
JAVA="$NMS_HOME/share/jre/bin/java"
finishsequence='execution of script finished'
appDir=$NMS_HOME/fsp_nm
rrlogpath=$appDir/var/log/rr.log
mediationLogPath=$appDir/var/log/mediation.log
db_filename=dbfnm_rm.tar.gz
db_backup_dir=$appDir/var/db.backup
postgres_dir=$appDir/postgres
reportdb_dir=$appDir/ws/webapps/reportdb
password_file="dbaccess.txt"

ECHO=echo
if [ -n "$1" ]; then
  export MNC_HOST=$1
fi

if [ -n "$2" ]; then
  export ENC_TOKEN=$2
fi
#
GREP_OPTIONS='-v '^[[:blank:]]*#''
NI_PROPERTY=com.adva.nlms.mediation.sm.prov.ni.controller
NI_REST_PROPERTY=use.rest.for.nmsadmin.ni.operations
nmsadminapp=$appDir/bin/nmsadmin.sh
nidb=$db_backup_dir/dbni.tgz
NI_SETTINGS_FILE="${db_backup_dir}/ni.properties"
ENC_TAPI_INSTALLED=false
ENC_TAPI_CONTAINER_NAME='enc-tapi'
TAPI_SCRIPT_DIR="$NMS_HOME/tapi"
#

finish(){
    echo $finishsequence
}

error(){
    echo "Failure, see $rrlogpath for details"
    echo result failure
    finish
    exit 1
}

success(){
    echo result success
    finish
    exit 0
}

StartCSM() {
  $NMS_HOME/stacks/csm/csm-ctl.sh status > /dev/null 2>&1
  if [ $? -eq 1 ]; then
    echo "Starting CSM services." >> $rrlogpath
    $NMS_HOME/stacks/csm/csm-ctl.sh start
  fi
}

StopCSM() {
  $NMS_HOME/stacks/csm/csm-ctl.sh status > /dev/null 2>&1
  if [ $? -eq 0 ]; then
    echo "Shutting down CSM services." >> $rrlogpath
    $NMS_HOME/stacks/csm/csm-ctl.sh stop
  fi
}

StartCore() {
  $NMS_HOME/stacks/core/core-ctl.sh status > /dev/null 2>&1
  if [ $? -eq 1 ]; then
    echo "Starting Core services." >> $rrlogpath
    $NMS_HOME/stacks/core/core-ctl.sh start
    sleep 20
  fi
}

StopCore() {
  $NMS_HOME/stacks/core/core-ctl.sh status > /dev/null 2>&1
  if [ $? -eq 0 ]; then
    echo "Shutting down Core services." >> $rrlogpath
    $NMS_HOME/stacks/core/core-ctl.sh stop
  fi
}

CleanKafkaStorage(){
  if [ -f "$NMS_HOME/stacks/core/core-ctl.sh" ]; then
    echo "Cleaning Kafka service data." >> $rrlogpath
    $NMS_HOME/stacks/core/core-ctl.sh clean_kafka_data
  else
    echo "Cannot clean Kafka service data." >> $rrlogpath
  fi
}

find_tapi_service_name() {
  local name
  local tapi_service_name="enc-tapi"
  name=$(docker service ls -f "name=${tapi_service_name}" --format "{{.Name}}")
  if [[ -n "$name" ]]; then
    echo "$name"
  fi
}

StopTapi() {
  if service_name=$(find_tapi_service_name); then
    cd $TAPI_SCRIPT_DIR
    bash $TAPI_SCRIPT_DIR/stop_service.sh
    echo "Tapi service stopped." >> "$rrlogpath"
    ENC_TAPI_INSTALLED=true
  else
    echo "TAPI Service doesn't exist - skipping"  >> "$rrlogpath"
  fi
}

StartTapi() {
  if [[ "$ENC_TAPI_INSTALLED" == true ]]; then
    echo "Tapi service starting ..." >> "$rrlogpath"
    cd $TAPI_SCRIPT_DIR
    bash $TAPI_SCRIPT_DIR/utils/check_launcher.sh $mediationLogPath >> "$rrlogpath"
    script_start="start_service.sh -e $MNC_HOST"
    bash $TAPI_SCRIPT_DIR/$script_start
    echo "Tapi service started successfully." >> "$rrlogpath"
  else
    echo "Skipping step. Tapi not installed." >> "$rrlogpath"
  fi
}

# Preparation
niEnabled=`grep $GREP_OPTIONS $appDir/fnm.properties | grep -e "$NI_PROPERTY=true" -e "$NI_PROPERTY=enabled" -e "$NI_PROPERTY=yes"`
niRestEnabled=`grep $GREP_OPTIONS $appDir/fnm.properties | grep -e "$NI_REST_PROPERTY=true" -e "$NI_REST_PROPERTY=enabled" -e "$NI_REST_PROPERTY=yes"`
echo Ni enabled : $niEnabled >> $rrlogpath
echo Ni REST enabled : $niRestEnabled >> $rrlogpath
echo Restarting remote server script log `date` > $rrlogpath
echo AppDir: $appDir >> $rrlogpath
echo NMS Dir: $NMS_HOME >> $rrlogpath
echo JAVA Dir: $JAVA >> $rrlogpath
echo DB name: $db_filename >> $rrlogpath
echo DB backup dir: $db_backup_dir >> $rrlogpath
echo PostgreSQL home dir: $postgres_dir >> $rrlogpath

# Stop services
'StopTapi'
'StopCSM'
$appDir/bin/fnm.server stop continue >> $rrlogpath 2>&1

# Restore NI
if [ -n "$niEnabled" ]; then
  if [ -n "$niRestEnabled" ]; then
      'StartCore'
      echo "Starting CPc services." >> $rrlogpath
      sudo $NMS_HOME/fsp_nm_ni/ni-ctl.sh start
  fi
  sh "$nmsadminapp" qnir "${NI_SETTINGS_FILE}" >> $rrlogpath 2>&1
  if [ -n "$niRestEnabled" ]; then
      echo "Shutting down CPc services." >> $rrlogpath
      sudo $NMS_HOME/fsp_nm_ni/ni-ctl.sh stop
  fi
fi

# Stop postgres and clean kafka
'StopCore'
$appDir/postgres/support-files/postgres.server stop >> $rrlogpath 2>&1
sleep 20
'CleanKafkaStorage'

# Extract db files
echo Removing temporary dir if exists $postgres_dir/data_new >> $rrlogpath
rm -rf $postgres_dir/data_new >> $rrlogpath 2>&1
echo Making temporary dir $postgres_dir/data_new >> $rrlogpath
mkdir $postgres_dir/data_new >> $rrlogpath 2>&1 || error
echo Extracting db files to $postgres_dir/data_new >> $rrlogpath
cd $postgres_dir/data_new >> $rrlogpath 2>&1 || error
gzip -dc $db_backup_dir/$db_filename | tar xpf - >> $rrlogpath 2>&1 || error
echo Deleting db archive $db_backup_dir/$db_filename >> $rrlogpath
rm -rf $db_backup_dir/$db_filename >> $rrlogpath 2>&1
cd $appDir >> $rrlogpath 2>&1 || error
# Swap DBs
echo Removing old data dir $postgres_dir/data_old >> $rrlogpath
rm -rf $postgres_dir/data_old >> $rrlogpath 2>&1
# ------------------------ CRITICAL ---------------------------
echo Backing up original db dir $postgres_dir/data to data_old >> $rrlogpath
mv $postgres_dir/data $postgres_dir/data_old >> $rrlogpath 2>&1
echo Renaming new db dir from $postgres_dir/data_new to data >> $rrlogpath
mv $postgres_dir/data_new $postgres_dir/data >> $rrlogpath 2>&1 || error
#  -------------------------------------------------------------

# Replace original settings and logs
echo Replacing original settings *.conf >> $rrlogpath
rm -f  $postgres_dir/data/*.conf >> $rrlogpath 2>&1
cp -r $postgres_dir/data_old/*.conf $postgres_dir/data >> $rrlogpath 2>&1
echo Replacing original db server log dir from $postgres_dir/data_old/pg_log >> $rrlogpath
rm -rf  $postgres_dir/data/pg_log >> $rrlogpath 2>&1
cp -r $postgres_dir/data_old/pg_log $postgres_dir/data/pg_log  >> $rrlogpath 2>&1
echo Setting permissions >> $rrlogpath
chmod -R 700 $postgres_dir/data >> $rrlogpath 2>&1 || error
chown -Rf postgres:postgres $postgres_dir/data >> $rrlogpath 2>&1

# Extract settings
echo Extracting settings.jar >> $rrlogpath
$JAVA -jar $appDir/var/db.backup/settings.jar >> $rrlogpath 2>&1

echo Deleting old settings >> $rrlogpath
rm -rf $appDir/${password_file}.bak >> $rrlogpath 2>&1
echo Deleting archive settings.jar >> $rrlogpath
rm -rf $appDir/var/db.backup/settings.jar >> $rrlogpath 2>&1
echo Removing new data dir if exists $postgres_dir/data_new >> $rrlogpath
rm -rf $postgres_dir/data_new >> $rrlogpath 2>&1
echo Removing old data dir if exists $postgres_dir/data_old >> $rrlogpath
rm -rf $postgres_dir/data_old >> $rrlogpath 2>&1

# Start postgres
nohup $appDir/postgres/support-files/postgres.server start >> $rrlogpath 2>&1 || error

# Restore NI settings
if [ -n "$niEnabled" ]; then
  sh "$nmsadminapp" rnis "${NI_SETTINGS_FILE}" >> $rrlogpath 2>&1
fi
# Remove NI settings file
rm -rf $NI_SETTINGS_FILE >> $rrlogpath 2>&1

# Start services
'StartCore'
nohup $appDir/bin/fnm.server start continue >> $rrlogpath 2>&1 || error
'StartCSM'
if [ -n "$niEnabled" ]; then
  if [ -n "$niRestEnabled" ]; then
      echo "Starting CPc services." >> $rrlogpath
      sudo $NMS_HOME/fsp_nm_ni/ni-ctl.sh start
  fi
fi
'StartTapi'

success