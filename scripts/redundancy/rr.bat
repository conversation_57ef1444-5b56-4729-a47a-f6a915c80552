::
:: Copyright 2023 Adtran Networks SE. All rights reserved.
::
:: Owner: gfilip
::

@echo off
rem Restarting remote server script

:: Settings
set finishsequence=execution of script finished
set rrlogpath=var\log\rr.log
set extractor=bin\bsdtar.exe
set db_filename=dbfnm_rm.tar.gz
set db_backup_dir=var\db.backup
set postgres_dir=postgres
set reportdb_dir=ws\webapps\reportdb
set password_file="dbaccess.txt"

:: Preparation
echo Restarting remote server script log %date% %time% > %rrlogpath%
echo Extractor: %extractor% >> %rrlogpath%
echo DB name: %db_filename% >> %rrlogpath%
echo DB backup dir: %db_backup_dir% >> %rrlogpath%
echo PostgreSQL home dir: %postgres_dir% >> %rrlogpath%

:: Stop services
echo Stop mediation service %date% %time% >> %rrlogpath%
%SystemRoot%\system32\net stop advams  >> %rrlogpath% 2>&1

SET counter=0
SET max_tries=100

GOTO StoppingService
:StoppingServiceDelay
SET /A counter=counter+1
IF %counter% GTR %max_tries% (
   ECHO Could not stop the mediation service %date% %time% >> %rrlogpath%
   GOTO ServiceStopped
)
echo Waiting for advams to stop >> %rrlogpath%
PING localhost -n 3 >NUL
:StoppingService
SC query "advams" | FIND "STATE" | FIND "STOPPED" >NUL
IF errorlevel 1 GOTO StoppingServiceDelay
:ServiceStopped


echo Stop jms service %date% %time% >> %rrlogpath%
%SystemRoot%\system32\net stop advajms  >> %rrlogpath% 2>&1
echo Stop PostgreSQL service >> %rrlogpath%
%SystemRoot%\system32\net stop postgres /yes >> %rrlogpath% 2>&1

:: Extract db files
echo Removing temporary data dir if exists %postgres_dir%\data_new >> %rrlogpath%
rmdir /S /Q  %postgres_dir%\data_new >> %rrlogpath% 2>&1
echo Making temporary dir %postgres_dir%\data_new >> %rrlogpath%
mkdir %postgres_dir%\data_new >> %rrlogpath% 2>&1 || goto Error
echo Extracting db files to %postgres_dir%\data_new >> %rrlogpath%
%extractor% -x -f %db_backup_dir%\%db_filename% -C %postgres_dir%\data_new >> %rrlogpath% 2>&1 || goto Error
echo Deleting db archive %db_backup_dir%\%db_filename% >> %rrlogpath%
del /F /Q %db_backup_dir%\%db_filename% >> %rrlogpath% 2>&1

:: Swap DBs
echo Removing old data dir %postgres_dir%\data_old >> %rrlogpath%
rmdir /S /Q  %postgres_dir%\data_old >> %rrlogpath% 2>&1
:: ------------------------ CRITICAL ---------------------------
echo Backing up original db dir %postgres_dir%\data to data_old >> %rrlogpath%
rename %postgres_dir%\data data_old >> %rrlogpath% 2>&1
echo Renaming new db dir from %postgres_dir%\data_new to data >> %rrlogpath%
rename %postgres_dir%\data_new data >> %rrlogpath% 2>&1 || goto Error
:: -------------------------------------------------------------

:: Replace original settings and logs
echo Replacing original settings *.conf >> %rrlogpath% 2>&1
del /F /Q  %postgres_dir%\data\*.conf >> %rrlogpath% 2>&1
xcopy /i /e /y %postgres_dir%\data_old\*.conf %postgres_dir%\data  >> %rrlogpath% 2>&1
echo Replacing original db server log dir from %postgres_dir%\data_old\pg_log >> %rrlogpath%
rmdir /S /Q  %postgres_dir%\data\pg_log >> %rrlogpath% 2>&1
xcopy /i /e /y %postgres_dir%\data_old\pg_log %postgres_dir%\data\pg_log  >> %rrlogpath% 2>&1

:: Extract settings
echo Extracting settings.jar >> %rrlogpath%
jre64\bin\java -jar var\db.backup\settings.jar >> %rrlogpath% 2>&1

:: Swap settings
echo Removing old settings >> %rrlogpath%
del /F /S /Q %password_file%.bak  >> %rrlogpath% 2>&1
echo Backing up original settings >> %rrlogpath%
rename %password_file% %password_file%.bak >> %rrlogpath% 2>&1
echo Copying new var\db.backup\%password_file% to . >> %rrlogpath%
xcopy /i /e /y var\db.backup\%password_file% . >> %rrlogpath% 2>&1
echo Deleting archive settings.jar >> %rrlogpath%
del /F /S /Q var\db.backup\settings.jar >> %rrlogpath% 2>&1
echo Deleting var\db.backup\%password_file% >> %rrlogpath%
del /F /S /Q  var\db.backup\%password_file% >> %rrlogpath% 2>&1
echo Removing new data dir if exists %postgres_dir%\data_new >> %rrlogpath%
rmdir /S /Q  %postgres_dir%\data_new >> %rrlogpath% 2>&1
echo Removing old data dir if exists %postgres_dir%\data_old >> %rrlogpath%
rmdir /S /Q  %postgres_dir%\data_old >> %rrlogpath% 2>&1

:: Starting services
echo Start PostgreSQL service >> %rrlogpath%
%SystemRoot%\system32\sc start postgres >> %rrlogpath% 2>&1
echo Start JMS service >> %rrlogpath%
%SystemRoot%\system32\sc start advajms >> %rrlogpath% 2>&1
echo Start mediation service >> %rrlogpath%
%SystemRoot%\system32\sc start advams >> %rrlogpath% 2>&1 

rem exit 0
@echo result success
echo SUCCESS >> %rrlogpath%
goto End

:Error
@echo Failure, see %rrlogpath% for details
@echo result failure
echo FAILURE >> %rrlogpath%
rem exit 1

:End
@echo %finishsequence%
echo Exit >> %rrlogpath%
