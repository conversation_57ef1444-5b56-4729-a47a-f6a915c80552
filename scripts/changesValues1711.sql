UPDATE public.cn_subnet SET uuid = gen_random_uuid() WHERE uuid IS NULL;
UPDATE ml_service SET uuid = gen_random_uuid() WHERE uuid IS NULL;
INSERT INTO cn_ne_config_file (id, globalsettings, neid)
SELECT nextval('configFileSequence'), true, ne.id
FROM cn_network_element ne;
UPDATE public.re_ha_properites SET ha_paused = false WHERE ha_paused IS NULL;

UPDATE cn_managed_object mo
SET jdoclass = 'L3FlowPointF4DBImpl'
    FROM cn_network_element ne
WHERE
  mo.entityindex like '%/l3fp-%'
  and mo.jdoclass = 'ConnectionTerminationPointEcDBImpl'
  and ne.id = mo.ne_id
  and ne.type0 IN ('404', '418', '480');

INSERT INTO jdo_sequence (id, sequence_value) SELECT 'DEFAULT', 0 WHERE NOT EXISTS (select * from jdo_sequence where id='DEFAULT');
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','certificate-renewal-failed','CRNWFLD','Certificate-renewal-failed','certificate-renewal-failed','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','certificate-renewal-succeed','CRNWSCD','Certificate-renewal-succeed','certificate-renewal-succeed','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','diagnostics-generation-started','DGENSTRT','Diagnostics Generation Started','diagnostics-generation-started','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','system-log-stop','LOGSSP','System-log-stop','system-log-stop','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','system-log-start','LOGSST','System-log-start','system-log-start','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','rollback-notification','RLBKNTF','Rollback-notification','rollback-notification','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','sftp-server-stop','SFTPSP','Sftp-server-stop','sftp-server-stop','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','sftp-server-start','SFTPST','Sftp-server-start','sftp-server-start','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','spq-scnrslt-notification','SPQSCNTF','Spq-scnrslt-notification','spq-scnrslt-notification','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','user-locked-out','ULCKO','User-locked-out','user-locked-out','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','user-unlocked','ULCKU','User-unlocked','user-unlocked','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','flex-ocm-state-notification','OCMNOTFY','Flex-ocm-state-notification','flex-ocm-state-notification','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','spq-flex-scnrslt-notification','SPQFLNTF','Spq-flex-scnrslt-notification','spq-flex-scnrslt-notification','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,severity_working,severity_protecting,severity_no_service,alarm_id,raise_clear_name,raise_clear_number)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'ALARM_EC','firmware-fault','FWFLT','Plug Firmware Fault','NE_DEFINED','NE_DEFINED','NE_DEFINED',38,'firmware-fault',38);
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,severity_working,severity_protecting,severity_no_service,alarm_id,raise_clear_name,raise_clear_number)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'ALARM_EC','auto-tuning-inprogress','AUTOTUN','Auto-tuning in progress','NE_DEFINED','NE_DEFINED','NE_DEFINED',5328,'auto-tuning-inprogress',5328);
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';

update nc_preferences set nemaxconfigbackupfiles = 4;

UPDATE pm_template_identifier SET uuid = gen_random_uuid() WHERE uuid IS NULL;

DELETE FROM reflector_fam_record
WHERE (ne_id, port_id,c_time) NOT IN (
    SELECT ne_id, port_id, MAX(c_time)
    FROM reflector_fam_record WHERE trace_type='FA'
    GROUP BY ne_id, port_id
);

CREATE OR REPLACE FUNCTION insert_if_not_exists_f8_alarm() RETURNS VOID AS $$
BEGIN
    IF NOT EXISTS ( SELECT 1 FROM cn_notification_ec WHERE short_name = 'CEREVOK')
    THEN
        INSERT INTO cn_notification_ec (id, notification_ec_type, name, short_name, message, severity_working, severity_protecting, severity_no_service, alarm_id, raise_clear_name, raise_clear_number) VALUES ((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'), 'ALARM_EC', 'certificate-revoked', 'CEREVOK', 'Certificate Revoked', 'NE_DEFINED', 'NE_DEFINED', 'NE_DEFINED', 5320, 'certificate-revoked', 5320);
        INSERT INTO cn_notification_ne_type_list (ne_type_list, notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8', (SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
        UPDATE JDO_SEQUENCE SET sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
    END IF;
END
$$ LANGUAGE 'plpgsql';
select insert_if_not_exists_f8_alarm();
DROP FUNCTION IF EXISTS insert_if_not_exists_f8_alarm();

UPDATE se_user SET UUID = uuid_generate_v4() WHERE uuid='';