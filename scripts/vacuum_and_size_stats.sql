
\pset pager off
\echo '==========================================================================================' 
\echo 'Vacuum and analyze statistics:' 
\echo '==========================================================================================' 
WITH table_opts AS
(SELECT pg_class.oid,
        relname,
        nspname,
        array_to_string(reloptions, '') AS relopts
 FROM pg_class
 INNER JOIN pg_namespace ns ON relnamespace = ns.oid),
vacuum_settings AS
(SELECT oid,
        relname,
        nspname,
        CASE
            WHEN relopts LIKE '%autovacuum_vacuum_threshold%' THEN cast(regexp_replace(relopts, '.*autovacuum_vacuum_threshold=([0-9.]+).*', '\1') AS integer)
            ELSE cast(current_setting('autovacuum_vacuum_threshold') AS integer)
        END AS autovacuum_vacuum_threshold,
        CASE
            WHEN relopts LIKE '%autovacuum_vacuum_scale_factor%' THEN cast(regexp_replace(relopts, '.*autovacuum_vacuum_scale_factor=([0-9.]+).*', '\1') AS real)
            ELSE cast(current_setting('autovacuum_vacuum_scale_factor') AS real)
        END AS autovacuum_vacuum_scale_factor,
        CASE
            WHEN relopts LIKE '%autovacuum_analyze_threshold%' THEN cast(regexp_replace(relopts, '.*autovacuum_analyze_threshold=([0-9.]+).*', '\1') AS integer)
            ELSE cast(current_setting('autovacuum_analyze_threshold') AS integer)
        END AS autovacuum_analyze_threshold,
        CASE
            WHEN relopts LIKE '%autovacuum_analyze_scale_factor%' THEN cast(regexp_replace(relopts, '.*autovacuum_analyze_scale_factor=([0-9.]+).*', '\1') AS real)
            ELSE cast(current_setting('autovacuum_analyze_scale_factor') AS real)
        END AS autovacuum_analyze_scale_factor
FROM table_opts)
SELECT vacuum_settings.nspname AS SCHEMA,
       vacuum_settings.relname AS TABLE,
       psut.last_autoanalyze AS last_autoanalyze,
       psut.last_autovacuum AS last_autovacuum,
       pg_class.reltuples AS rowcount,
       psut.n_dead_tup AS dead_rowcount,
       autovacuum_vacuum_threshold + (cast(autovacuum_vacuum_scale_factor AS numeric) * pg_class.reltuples) AS autovacuum_threshold,
       CASE
           WHEN autovacuum_vacuum_threshold + (cast(autovacuum_vacuum_scale_factor AS numeric) * pg_class.reltuples) < psut.n_dead_tup THEN 'yes'
       END AS expect_autovacuum,
       autovacuum_analyze_threshold + (cast(autovacuum_analyze_scale_factor AS numeric) * pg_class.reltuples) AS autoanalyze_threshold,
       CASE
           WHEN autovacuum_analyze_threshold + (cast(autovacuum_analyze_scale_factor AS numeric) * pg_class.reltuples) < psut.n_dead_tup THEN 'yes'
       END AS expect_autoanalyze,
       psut.last_analyze AS last_analyze,
       psut.last_vacuum AS last_vacuum,
       psut.vacuum_count AS vacuum_count,
       psut.autovacuum_count AS autovacuum_count,
       psut.analyze_count AS analyze_count,
       psut.autoanalyze_count AS autoanalyze_count
FROM pg_stat_user_tables psut
INNER JOIN pg_class ON psut.relid = pg_class.oid
INNER JOIN vacuum_settings ON pg_class.oid = vacuum_settings.oid
ORDER BY dead_rowcount DESC;


\echo '=========================================================================================='
\echo 'Bloats (ver. 1):'
\echo '==========================================================================================' 
WITH constants AS
(SELECT cast(current_setting('block_size') AS numeric) AS bs,
        23 AS hdr,
        4 AS ma),
bloat_info AS
(SELECT ma,
        bs,
        schemaname,
        tablename,
        CAST ((datawidth+(hdr+ma-(CASE WHEN hdr%ma=0 THEN ma ELSE hdr%ma END))) AS numeric) AS datahdr,
             (maxfracsum*(nullhdr+ma-(CASE WHEN nullhdr%ma=0 THEN ma ELSE nullhdr%ma END))) AS nullhdr2
 FROM
   (SELECT schemaname,
           tablename,
           hdr,
           ma,
           bs,
           SUM((1-null_frac)*avg_width) AS datawidth,
           MAX(null_frac) AS maxfracsum,
           hdr+
      (SELECT 1+count(*)/8
       FROM pg_stats s2
       WHERE null_frac<>0
         AND s2.schemaname = s.schemaname
         AND s2.tablename = s.tablename) AS nullhdr
    FROM pg_stats s,
                  constants
    GROUP BY 1,
             2,
             3,
             4,
             5) AS foo),
table_bloat AS
(SELECT schemaname,
        tablename,
        cc.relpages,
        bs,
        CEIL((cc.reltuples*((datahdr+ma- (CASE WHEN datahdr%ma=0 THEN ma ELSE datahdr%ma END))+nullhdr2+4))/(CAST(bs-20 AS float))) AS otta
 FROM bloat_info
 JOIN pg_class cc ON cc.relname = bloat_info.tablename
 JOIN pg_namespace nn ON cc.relnamespace = nn.oid
 AND nn.nspname = bloat_info.schemaname
 AND nn.nspname <> 'information_schema'),
index_bloat AS
(SELECT schemaname,
        tablename,
        bs,
        COALESCE(c2.relname,'?') AS iname,
        COALESCE(c2.reltuples,0) AS ituples,
        COALESCE(c2.relpages,0) AS ipages,
        COALESCE(CEIL((c2.reltuples*(datahdr-12))/(CAST(bs-20 AS float))),0) AS iotta
 FROM bloat_info
 JOIN pg_class cc ON cc.relname = bloat_info.tablename
 JOIN pg_namespace nn ON cc.relnamespace = nn.oid
 AND nn.nspname = bloat_info.schemaname
 AND nn.nspname <> 'information_schema'
 JOIN pg_index i ON indrelid = cc.oid
 JOIN pg_class c2 ON c2.oid = i.indexrelid)
SELECT TYPE,
       schemaname,
       object_name,
       bloat,
       pg_size_pretty(raw_waste) AS waste
FROM
(SELECT 'table' AS TYPE,
        schemaname,
        tablename AS object_name,
        ROUND(CASE WHEN otta=0 THEN 0.0 ELSE table_bloat.relpages/CAST(otta AS numeric) END,1) AS bloat,
        CASE
            WHEN relpages < otta THEN '0'
            ELSE CAST((bs*CAST((table_bloat.relpages-otta) AS bigint)) AS bigint)
        END AS raw_waste
 FROM table_bloat
 UNION SELECT 'index' AS TYPE,
              schemaname,
              tablename || '::' || iname AS object_name,
              ROUND(CASE WHEN iotta=0
                    OR ipages=0 THEN 0.0 ELSE CAST(ipages/iotta AS numeric) END,1) AS bloat,
              CASE
                  WHEN ipages < iotta THEN '0'
                  ELSE CAST((bs*(ipages-iotta)) AS bigint)
              END AS raw_waste
 FROM index_bloat) bloat_summary
WHERE schemaname = 'public'
ORDER BY raw_waste DESC,
         bloat DESC;

		 
\echo '=========================================================================================='
\echo 'Bloats (ver. 2):'
\echo '=========================================================================================='
SELECT current_database(),
       schemaname,
       tablename,
       ROUND(CASE WHEN otta=0 THEN 0.0 ELSE sml.relpages/cast(otta AS numeric) END,1) AS tbloat,
       CASE
           WHEN relpages < otta THEN 0
           ELSE bs*cast((sml.relpages-otta) AS bigint)
       END AS wastedbytes,
       iname,
       ROUND(CASE WHEN iotta=0
             OR ipages=0 THEN 0.0 ELSE ipages/cast(iotta AS numeric) END,1) AS ibloat,
       CASE
           WHEN ipages < iotta THEN 0
           ELSE bs*(ipages-iotta)
       END AS wastedibytes
FROM
(SELECT schemaname,
        tablename,
        cc.reltuples,
        cc.relpages,
        bs,
        CEIL((cc.reltuples*((datahdr+ma- (CASE WHEN datahdr%ma=0 THEN ma ELSE datahdr%ma END))+nullhdr2+4))/(cast(bs-20 AS float))) AS otta,
        COALESCE(c2.relname,'?') AS iname,
        COALESCE(c2.reltuples,0) AS ituples,
        COALESCE(c2.relpages,0) AS ipages,
        COALESCE(CEIL((c2.reltuples*(datahdr-12))/(cast(bs-20 AS float))),0) AS iotta
 FROM
   (SELECT ma,
           bs,
           schemaname,
           tablename,
           cast((datawidth+(hdr+ma-(CASE WHEN hdr%ma=0 THEN ma ELSE hdr%ma END))) AS numeric) AS datahdr,
           (maxfracsum*(nullhdr+ma-(CASE WHEN nullhdr%ma=0 THEN ma ELSE nullhdr%ma END))) AS nullhdr2
    FROM
      (SELECT schemaname,
              tablename,
              hdr,
              ma,
              bs,
              SUM((1-null_frac)*avg_width) AS datawidth,
              MAX(null_frac) AS maxfracsum,
              hdr+
         (SELECT 1+count(*)/8
          FROM pg_stats s2
          WHERE null_frac<>0
            AND s2.schemaname = s.schemaname
            AND s2.tablename = s.tablename) AS nullhdr
       FROM pg_stats s,

         (SELECT
            (SELECT cast(current_setting('block_size') AS numeric)) AS bs,
                 CASE WHEN substring(v,12,3) IN ('8.0',
                                                 '8.1',
                                                 '8.2') THEN 27 ELSE 23 END AS hdr,
                                                                               CASE WHEN v ~ 'mingw32' THEN 8 ELSE 4 END AS ma
          FROM
            (SELECT version() AS v) AS foo) AS constants
       GROUP BY 1,
                2,
                3,
                4,
                5) AS foo) AS rs
 JOIN pg_class cc ON cc.relname = rs.tablename
 JOIN pg_namespace nn ON cc.relnamespace = nn.oid
 AND nn.nspname = rs.schemaname
 AND nn.nspname <> 'information_schema'
 LEFT JOIN pg_index i ON indrelid = cc.oid
 LEFT JOIN pg_class c2 ON c2.oid = i.indexrelid) AS sml
ORDER BY wastedbytes DESC;


\echo '=========================================================================================='
\echo 'Table specific options:'
\echo '=========================================================================================='
SELECT nspname AS schema,
       relname AS table,
       array_to_string(reloptions, '') AS table_options
FROM pg_class
INNER JOIN pg_namespace ns ON relnamespace = ns.oid
WHERE array_to_string(reloptions, '') IS NOT NULL;


\echo '=========================================================================================='
\echo 'Size of DB tables:'
\echo '=========================================================================================='
SELECT TABLE_NAME,
       pg_size_pretty(table_size) AS table_size,
       pg_size_pretty(indexes_size) AS indexes_size,
       pg_size_pretty(total_size) AS total_size
FROM
(SELECT TABLE_NAME,
        pg_table_size(TABLE_NAME) AS table_size,
        pg_indexes_size(TABLE_NAME) AS indexes_size,
        pg_total_relation_size(TABLE_NAME) AS total_size
 FROM
   (SELECT ('"' || table_schema || '"."' || TABLE_NAME || '"') AS TABLE_NAME
    FROM information_schema.tables) AS all_tables
 ORDER BY total_size DESC) AS pretty_sizes;

 
\echo '=========================================================================================='
\echo 'Size of FNM DB:'
\echo '=========================================================================================='
SELECT now(),
       pg_size_pretty(pg_database_size('fnm'));


\echo '=========================================================================================='
\echo 'Size and usage statistics of indexes and tables:'
\echo '=========================================================================================='

       SELECT
    t.tablename,
    indexname,
    c.reltuples AS num_rows,
    pg_size_pretty(pg_relation_size(quote_ident(t.tablename)::text)) AS table_size,
    pg_size_pretty(pg_relation_size(quote_ident(indexrelname)::text)) AS index_size,
    CASE WHEN indisunique THEN 'Y'
       ELSE 'N'
    END AS UNIQUE,
    idx_scan AS number_of_scans,
    idx_tup_read AS tuples_read,
    idx_tup_fetch AS tuples_fetched
FROM pg_tables t
LEFT OUTER JOIN pg_class c ON t.tablename=c.relname
LEFT OUTER JOIN
    ( SELECT c.relname AS ctablename, ipg.relname AS indexname, x.indnatts AS number_of_columns, idx_scan, idx_tup_read, idx_tup_fetch, indexrelname, indisunique FROM pg_index x
           JOIN pg_class c ON c.oid = x.indrelid
           JOIN pg_class ipg ON ipg.oid = x.indexrelid
           JOIN pg_stat_all_indexes psai ON x.indexrelid = psai.indexrelid AND psai.schemaname = 'public' )
    AS foo
    ON t.tablename = foo.ctablename
WHERE t.schemaname='public'
ORDER BY 1,2;