ALTER TABLE public.cn_subnet
    ADD COLUMN uuid CHARACTER VARYING(255);

UPDATE cn_network_element SET coldstarttime = 0 where type0 in ('490', '480', '418', '118', '404');

ALTER TABLE public.cn_remote_slave_connectivity
    ADD COLUMN suppress_alarms_events boolean DEFAULT false;

ALTER TABLE public.nc_preferences
    ADD COLUMN ntpsecuritymode integer;

ALTER TABLE public.cn_ntp_clock
    ADD COLUMN clock_precision bigint,
    ADD COLUMN leap_indicator bigint,
    ADD COLUMN ntp_mode integer,
    ADD COLUMN ntp_type integer,
    ADD COLUMN raw_data_transfer_interval integer,
    ADD COLUMN ref_clock_id character varying(255),
    ADD COLUMN server_state integer,
    ADD COLUMN stratum_level bigint,
    ADD COLUMN time_clock character varying(255),
    ADD COLUMN time_scale character varying(255);

ALTER TABLE public.cn_ntp_clock_interface
    ADD COLUMN ip_protocol integer,
    ADD COLUMN ip_v4_address character varying(255),
    ADD COLUMN ip_v6_address character varying(255),
    ADD COLUMN association_mode integer,
    ADD COLUMN broaadcast_ntp_version integer,
    ADD COLUMN broadcast_interval integer,
    ADD COLUMN broadcast_ip_v4_address character varying(255),
    ADD COLUMN broadcast_ip_v6_address character varying(255),
    ADD COLUMN broadcast_max_hops integer,
    ADD COLUMN broadcast_state integer,
    ADD COLUMN broadcast_sym_key_id integer,
    ADD COLUMN daytime_service_control integer,
    ADD COLUMN default_gateway_control integer,
    ADD COLUMN dscp bigint,
    ADD COLUMN hardened_responder_control integer,
    ADD COLUMN if_name character varying(255),
    ADD COLUMN ip_v4_gateway character varying(255),
    ADD COLUMN ip_v4_subnet_mask character varying(255),
    ADD COLUMN ip_v6_address_prefix_length integer,
    ADD COLUMN ip_v6_gateway character varying(255),
    ADD COLUMN time_scale integer,
    ADD COLUMN time_service_control integer,
    ADD COLUMN udp_checksum integer;

CREATE TABLE public.cn_ntpfiletransfer_properties (
                                                      collection_cycle integer,
                                                      connection_timeout integer,
                                                      file_filter character varying(255),
                                                      id integer NOT NULL,
                                                      jdoversion integer,
                                                      last_connection_time timestamp without time zone,
                                                      number_of_retries integer,
                                                      read_timeout integer,
                                                      ntp_secure_mode integer
);

ALTER TABLE ONLY public.cn_ntpfiletransfer_properties
    ADD CONSTRAINT cn_ntpfiletransfer_properties_pkey PRIMARY KEY (id);


DELETE FROM public.pm_record WHERE intervalno<>0;

DROP INDEX IF EXISTS idx_pm_record_ent_tt_;
ALTER TABLE ONLY public.pm_record DROP CONSTRAINT IF EXISTS constraint_ts_tt_ne_ent_int;

ALTER TABLE public.pm_manager_cfg DROP COLUMN serie15_1,DROP COLUMN serie24_1;
ALTER TABLE public.pm_record DROP COLUMN intervalno;

ALTER TABLE public.pm_serie DROP COLUMN ser15_2,DROP COLUMN ser24_2;

ALTER TABLE ONLY public.pm_record ADD CONSTRAINT constraint_ts_tt_ne_ent_int UNIQUE ("timestamp", id_entity, id_ne, timetype);

CREATE INDEX idx_pm_record_ent_tt_ ON public.pm_record USING btree (id_entity, timetype);

ALTER TABLE public.cn_croma_service_path
    ADD COLUMN service_path_index integer NOT NULL DEFAULT 0;
ALTER TABLE IF EXISTS ml_log_link_dev_endpoints RENAME TO ml_log_link_dev_endpoint;

ALTER TABLE IF EXISTS ml_log_link_dev_endpoint RENAME CONSTRAINT ml_log_link_dev_endpoints_pkey TO ml_log_link_dev_endpoint_pkey;

ALTER INDEX IF EXISTS mlloglink_dev_endpointsfk_ml_log_link_dev_endpoints_log_link_id RENAME TO ml_log_link_dev_endpointfk_ml_log_link_dev_endpoint_log_link_id;

CREATE TABLE public.ml_log_link_oms_prt_path (
                                                 dev_endpoint_id bigint,
                                                 dev_endpoint_id_order integer,
                                                 id character varying(255)
);

CREATE TABLE public.cn_ntp_dynamic_client (
    client_first_request_time timestamp without time zone NOT NULL,
    client_ip character varying(255) NOT NULL,
    client_last_request_time timestamp without time zone,
    client_mode integer,
    client_poll_interval integer,
    client_requests_count bigint,
    client_version integer,
    id integer NOT NULL,
    jdoversion integer,
    ne_id integer,
    ntp_clock character varying(255),
    ntp_clock_interface character varying(255),
    ntp_clock_interface_ip character varying(255) NOT NULL,
    subnet_id integer,
    ignore boolean
);
CREATE TABLE public.ml_log_link_oms_wkg_path (
                                                 dev_endpoint_id bigint,
                                                 dev_endpoint_id_order integer,
                                                 id character varying(255)
);

ALTER TABLE ONLY public.cn_ntp_dynamic_client
    ADD CONSTRAINT cn_ntp_dynamic_client_pkey PRIMARY KEY (client_first_request_time, ntp_clock_interface_ip, client_ip);
CREATE INDEX ix_ml_log_link_oms_prt_path_fk_ml_log_link_oms_prt_path_id ON public.ml_log_link_oms_prt_path USING btree (id);

CREATE INDEX ix_ml_log_link_oms_wkg_path_fk_ml_log_link_oms_wkg_path_id ON public.ml_log_link_oms_wkg_path USING btree (id);

CREATE TABLE public.sm_tags (
    id integer NOT NULL,
    tag_resource character varying(255),
    tag_resource_type integer,
    user_id character varying(255)
);

ALTER TABLE ONLY public.sm_tags
    ADD CONSTRAINT sm_tags_pkey PRIMARY KEY (id);

ALTER TABLE public.cn_prot_group_f8
    ADD COLUMN holdoff integer,
    ADD COLUMN is_cccp boolean,
    ADD COLUMN revertive boolean,
    ADD COLUMN switch_on_peer_comm_fail boolean,
    ADD COLUMN user_label character varying(255),
    ADD COLUMN wait_to_restore integer,
    ADD COLUMN cccp_identifier character varying(255);

ALTER TABLE public.cn_ec_ctp
    ADD COLUMN autoneg boolean,
    ADD COLUMN cfgspeed character varying(10),
    ADD COLUMN dupmode character varying(10),
    ADD COLUMN mdix character varying(10),
    ADD COLUMN trlayout character varying(10);


CREATE TABLE public.efd_hist_segment (
    alm_ip_address character varying(255),
    hist_segment_length integer,
    hist_segment_loss integer,
    hist_segment_loss_per_km integer,
    hist_segment_timestamp bigint,
    hist_segment_uncertain boolean,
    id integer NOT NULL,
    port character varying(255),
    segment_number integer
);

CREATE TABLE public.cn_bfd_multi_hop_session_f4 (
    dest_address character varying(255),
    entity_name character varying(255),
    id integer NOT NULL,
    local_multiplier integer,
    min_rx_interval bigint,
    min_tx_interval bigint,
    source_address character varying(255),
    vrf_name character varying(255)
);

CREATE TABLE public.cn_route_proto_f4 (
    entity_name character varying(255),
    id integer NOT NULL,
    type character varying(255),
    vrf_name character varying(255)
);

CREATE TABLE public.cn_bfd_mhop_session_status_f4 (
    id integer NOT NULL
);

ALTER TABLE ONLY public.cn_bfd_multi_hop_session_f4
    ADD CONSTRAINT cn_bfd_multi_hop_session_f4_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.cn_route_proto_f4
    ADD CONSTRAINT cn_route_proto_f4_pkey PRIMARY KEY (id);
  
ALTER TABLE ONLY public.cn_bfd_mhop_session_status_f4
    ADD CONSTRAINT cn_bfd_mhop_session_status_f4_pkey PRIMARY KEY (id);

CREATE SEQUENCE public.efd_hist_segment_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.efd_hist_segment_id_seq OWNED BY public.efd_hist_segment.id;

ALTER TABLE ONLY public.efd_hist_segment ALTER COLUMN id SET DEFAULT nextval('public.efd_hist_segment_id_seq'::regclass);

SELECT pg_catalog.setval('public.efd_hist_segment_id_seq', 1, false);

ALTER TABLE ONLY public.efd_hist_segment
    ADD CONSTRAINT efd_hist_segment_ip_port_nr_timestamp UNIQUE (alm_ip_address, port, segment_number, hist_segment_timestamp);

ALTER TABLE ONLY public.efd_hist_segment
    ADD CONSTRAINT efd_hist_segment_pkey PRIMARY KEY (id);

ALTER TABLE public.ml_log_link
    ADD COLUMN availability character varying(20),
    ADD COLUMN last_update bigint;
ALTER TABLE public.cn_spectrum_inventory
    ADD COLUMN croma_conn_id integer;
ALTER TABLE ONLY public.cn_croma_conn_map
    ADD CONSTRAINT unq_cn_croma_conn_map_0 UNIQUE (id);
CREATE INDEX ix_cn_spectrum_inventory_fk_cn_spectrum_inventory_croma_conn_id ON public.cn_spectrum_inventory USING btree (croma_conn_id);

CREATE TABLE public.ml_topology_layer_custom_names (
                                                       a_end_ne_id integer,
                                                       a_end_te character varying(1000),
                                                       id integer NOT NULL,
                                                       jdoversion integer,
                                                       layer integer,
                                                       mismatch_timestamp bigint,
                                                       name character varying(1000),
                                                       type integer,
                                                       z_end_ne_id integer,
                                                       z_end_te character varying(1000)
);

ALTER TABLE ONLY public.ml_topology_layer_custom_names
    ADD CONSTRAINT ml_topology_layer_custom_names_pkey PRIMARY KEY (id);

ALTER TABLE public.reflector_fam_record
    ADD COLUMN cleaved boolean,
    ADD COLUMN target_insertion_loss integer,
    ADD COLUMN loss_guidance_type character varying(255);

ALTER TABLE public.efd_loss_guidance
    ADD COLUMN cleaved boolean,
    ADD COLUMN target_insertion_loss integer,
    ADD COLUMN type character varying(255);

UPDATE public.reflector_fam_record
    SET cleaved = false
    WHERE cleaved is null;

UPDATE public.reflector_fam_record
    SET target_insertion_loss = 25
    WHERE target_insertion_loss is null;

UPDATE public.reflector_fam_record
    SET loss_guidance_type = 'lg'
    WHERE loss_guidance_type is null;

ALTER TABLE public.re_ha_properites
    ADD COLUMN ha_paused boolean;
CREATE TABLE public.cn_ne_config_file (
                                          globalsettings boolean,
                                          id bigint NOT NULL,
                                          neid integer
);
ALTER TABLE ONLY public.cn_ne_config_file
    ADD CONSTRAINT cn_ne_config_file_pkey PRIMARY KEY (id);

ALTER TABLE nc_preferences ADD COLUMN nemaxconfigbackupfiles INTEGER;

CREATE SEQUENCE public.configfilesequence
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
SELECT pg_catalog.setval('public.configfilesequence', 1, false);

ALTER TABLE public.reflector_fam_record
    ADD COLUMN comment character varying(255);

ALTER TABLE public.cn_clock_probe
    ADD COLUMN mtierestarttime bigint;
ALTER TABLE public.cn_ptp_clock_probe
    ADD COLUMN mtierestarttime bigint;
ALTER TABLE public.sync_test
    ADD COLUMN mtie_restart_time character varying(255) DEFAULT 'N/A'::character varying;

ALTER TABLE public.pm_template_identifier
    ADD COLUMN uuid uuid;

ALTER TABLE public.ml_end_point
    ADD COLUMN term_type character varying(10);

CREATE TABLE public.cn_ntp_server_activity (
    ne_id integer NOT NULL,
    ntp_clock character varying(255) NOT NULL,
    time_stamp timestamp without time zone NOT NULL,
    num_requests bigint,
    PRIMARY KEY (ne_id, ntp_clock, time_stamp)
);
ALTER TABLE public.cn_prot_group_f8
    ADD COLUMN epte_opp_1 character varying(255),
    ADD COLUMN epte_opp_2 character varying(255),
    ADD COLUMN epte_opp_3 character varying(255),
    ADD COLUMN epte_opp_4 character varying(255);
alter table public.nc_preferences
    add column brmodeconfigfile integer;
alter table public.cn_ec_ctp
    add column maintenance_signal character varying(10);

ALTER TABLE mnc_mpd_layer3.pdl3_ipvpn_service
    ADD COLUMN ackd_at bigint,
    ADD COLUMN ackd_by character varying(255),
    ADD COLUMN acknowledged boolean DEFAULT false,
    ADD COLUMN oper_state integer DEFAULT 5,
    ADD COLUMN reason_code integer DEFAULT 0,
    ADD COLUMN secondary_state character varying(255),
    ADD COLUMN timestamp_degraded bigint,
    ADD COLUMN timestamp_faulted bigint;

ALTER TABLE public.ml_end_point ALTER COLUMN resources TYPE character varying(500);

CREATE TABLE public.se_sab_event (
                                     count integer NOT NULL,
                                     creation_time bigint NOT NULL,
                                     id integer NOT NULL,
                                     operation_type character varying(255) NOT NULL,
                                     resource_type character varying(255) NOT NULL,
                                     user_group character varying(255) NOT NULL,
                                     user_id integer NOT NULL
);

ALTER TABLE ONLY public.se_sab_event
    ADD CONSTRAINT se_sab_event_pkey PRIMARY KEY (id);

ALTER TABLE public.cn_ntp_tracked_client DROP COLUMN IF EXISTS alias;

CREATE TABLE public.cn_ntp_clock_collection_info (
                                     failed_file_name character varying(255),
                                     last_file_timestamp timestamp without time zone,
                                     ne_id integer NOT NULL,
                                     ntp_clock_id integer NOT NULL,
                                     retry_count integer
);

ALTER TABLE ONLY public.cn_ntp_clock_collection_info
    ADD CONSTRAINT cn_ntp_clock_collection_info_pkey PRIMARY KEY (ne_id, ntp_clock_id);

CREATE TABLE public.efd_last_change_time (
                                             entity_name character varying(255),
                                             id integer NOT NULL,
                                             last_change bigint
);

CREATE SEQUENCE public.efd_last_change_time_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.efd_last_change_time_id_seq OWNED BY public.efd_last_change_time.id;

ALTER TABLE ONLY public.efd_last_change_time ALTER COLUMN id SET DEFAULT nextval('public.efd_last_change_time_id_seq'::regclass);

SELECT pg_catalog.setval('public.efd_last_change_time_id_seq', 1, false);

ALTER TABLE ONLY public.efd_last_change_time
    ADD CONSTRAINT efd_last_change_time_pkey PRIMARY KEY (id);

ALTER TABLE ONLY public.efd_last_change_time
    ADD CONSTRAINT efd_last_time_change_entity_key UNIQUE (entity_name);




ALTER TABLE public.fam_event add column customid varchar(255);
ALTER TABLE public.fam_event add column custom_event bool;
ALTER TABLE public.fam_event add column typeOntSplitter varchar(255);
ALTER TABLE public.fam_event add column status varchar(255);
alter table public.cn_entity add column single_fiber_location integer;

ALTER TABLE public.ml_log_link ADD COLUMN unique_srlg_of_fiber_ll integer;

CREATE TABLE public.ml_log_link_fiber_srlg (
                                               id integer NOT NULL
);

CREATE SEQUENCE public.ml_log_link_fiber_srlg_seq
    START WITH 268435456
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

SELECT pg_catalog.setval('public.ml_log_link_fiber_srlg_seq', 268435456, false);

ALTER TABLE ONLY public.ml_log_link_fiber_srlg
    ADD CONSTRAINT ml_log_link_fiber_srlg_pkey PRIMARY KEY (id);

CREATE INDEX ix_ml_log_link_fk_ml_log_link_unique_srlg_of_fiber_ll ON public.ml_log_link USING btree (unique_srlg_of_fiber_ll);

ALTER TABLE public.pd_connection_resource DROP COLUMN tp_pair_id,DROP COLUMN ts_type;

ALTER TABLE public.ml_log_link
    ALTER COLUMN id TYPE integer using (id::integer),
    ADD COLUMN uuid character varying(255);

ALTER TABLE public.ml_log_link
    DROP COLUMN computed_te_attributes_id,
    DROP COLUMN te_attributes_id,
    ADD COLUMN computed_cost integer DEFAULT 0,
    ADD COLUMN computed_srlg character varying(1000),
    ADD COLUMN user_cost integer DEFAULT 0,
    ADD COLUMN user_srlg character varying(1000);

DROP TABLE IF EXISTS public.ml_log_link_comp_te_attributes;
DROP TABLE IF EXISTS public.ml_log_link_te_attributes;
DROP TABLE IF EXISTS public.ml_log_link_srlg;
DROP INDEX IF EXISTS ix_ml_log_link_fk_ml_log_link_computed_te_attributes_id;
DROP INDEX IF EXISTS ix_ml_log_link_fk_ml_log_link_te_attributes_id;
DROP INDEX IF EXISTS ix_ml_log_link_fk_ml_log_link_unique_srlg_of_fiber_ll;
DROP INDEX IF EXISTS ix_ml_log_link_srlg_fk_ml_log_link_srlg_te_attribute_id;


ALTER TABLE public.ml_log_link_dev_endpoint ALTER COLUMN log_link_id TYPE integer using (id::integer);

ALTER TABLE public.ml_log_link_oms_prt_path ALTER COLUMN id TYPE integer using (id::integer);

ALTER TABLE public.ml_log_link_oms_wkg_path ALTER COLUMN id TYPE integer using (id::integer);

ALTER TABLE public.ml_log_link_sup_link_resource ALTER COLUMN log_link_id TYPE integer using (id::integer);

ALTER TABLE public.pd_topology_element DROP COLUMN lifecycle_primary_state;

ALTER TABLE public.reflector_fam_record
    ADD COLUMN mode character varying(255),
    ADD COLUMN fp_feeder_loss double precision,
    ADD COLUMN inspect_loss1 double precision;

ALTER TABLE public.efd_trace_template ADD COLUMN mode character varying(255);

UPDATE public.efd_trace_template
    SET mode = 'std'
    WHERE trace_template_type = 'DPA' AND mode is null;

ALTER TABLE public.cn_ptp_clock ADD COLUMN clock_state character varying(255);
