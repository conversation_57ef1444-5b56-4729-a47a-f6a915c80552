CREATE OR REPLACE FUNCTION selectLastIdForNESystem(localNeId integer) RETURNS INTEGER AS
$BODY$
DECLARE
result integer;
BEGIN
  select max(id) from ev_event where sourceneid in (
    select id from cn_network_element where id = localNeId
	  union select id from cn_network_element where local_id=localNeId
	  )
	INTO result;
	RAISE NOTICE 'selectLastIdForNESystem for NE % %', localNeId, result;
	Return result;
END
$BODY$
LANGUAGE 'plpgsql' ;

CREATE OR REPLACE FUNCTION updateMoEventProcessed(neType VARCHAR) RETURNS VOID AS
$BODY$
DECLARE
    r cn_network_element%rowtype;
BEGIN
    FOR r IN SELECT * FROM cn_network_element where type0 = neType
    LOOP
	    UPDATE cn_network_element set moeventsprocessed = selectLastIdForNESystem(r.id) WHERE id = r.id;
    END LOOP;
    RETURN;
END
$BODY$
LANGUAGE 'plpgsql' ;

SELECT updateMoEventProcessed('112');
SELECT updateMoEventProcessed('114');
SELECT updateMoEventProcessed('1140');
SELECT updateMoEventProcessed('1141');
SELECT updateMoEventProcessed('1142');
SELECT updateMoEventProcessed('1143');
SELECT updateMoEventProcessed('1144');
SELECT updateMoEventProcessed('1145');
SELECT updateMoEventProcessed('1146');
SELECT updateMoEventProcessed('1147');
SELECT updateMoEventProcessed('1148');
SELECT updateMoEventProcessed('210');
SELECT updateMoEventProcessed('212');
SELECT updateMoEventProcessed('1120');
SELECT updateMoEventProcessed('1121');
SELECT updateMoEventProcessed('1122');
DROP FUNCTION IF EXISTS selectLastIdForNESystem(integer);
DROP FUNCTION IF EXISTS updateMoEventProcessed(VARCHAR);