ALTER TABLE public.cn_network_element_swupgrade
    ADD COLUMN priority integer;

CREATE TABLE public.gnss_ne_block_list (
   gnss_firewall_disable boolean DEFAULT true,
   jdoversion integer,
   ml_alarms_disable boolean DEFAULT true,
   ne_id integer NOT NULL,
   rca_disable boolean DEFAULT true
);


ALTER TABLE ONLY public.gnss_ne_block_list
    ADD CONSTRAINT gnss_ne_block_list_pkey PRIMARY KEY (ne_id);

ALTER TABLE public.cn_port_clock
    ADD COLUMN if_name character varying(255),
    ADD COLUMN synce_enabled integer;
ALTER TABLE public.cn_l3_port_clock
    ADD COLUMN synce_enabled integer;
ALTER TABLE public.cn_ntp_remote_server
    ADD COLUMN selection_status integer;

ALTER TABLE public.pd_service
    ADD COLUMN performancetemplateid integer;

ALTER TABLE public.cn_module_ec
        ADD COLUMN partner_card bytea;

alter table public.cn_ec_ctp
    add column odu_mux_type character varying(10),
    add column payload_type integer DEFAULT 0 NOT NULL;

CREATE TABLE public.cn_secure_entity_f4 (
    associatedmep character varying(500) DEFAULT NULL::character varying,
    ciphersuite character varying(255),
    id integer NOT NULL,
    keyexchangechannel character varying(500) DEFAULT NULL::character varying,
    keyexchangeinterval bigint,
    keyexchangeprofile character varying(500) DEFAULT NULL::character varying,
    keyinjectflowpoint character varying(500) DEFAULT NULL::character varying,
    name character varying(255),
    remoteendlagpeerenabled boolean,
    remotemacaddr character varying(255),
    remotemacaddrenabled boolean,
    replayprotectionenabled boolean,
    replayprotectionwindow bigint,
    tagsclear integer
);

ALTER TABLE ONLY public.cn_secure_entity_f4 ADD CONSTRAINT cn_secure_entity_f4_pkey PRIMARY KEY (id);

CREATE TABLE public.cn_key_exchange_profile_f4 (
    id integer NOT NULL,
    name character varying(255),
    mode character varying(255),
    keylen character varying(255)
);

ALTER TABLE ONLY public.cn_key_exchange_profile_f4 ADD CONSTRAINT cn_key_exchange_profile_f4_pkey PRIMARY KEY (id);

ALTER TABLE public.cn_flowpoint_f4
ADD COLUMN crypto_key_exchange_enable boolean,
ADD COLUMN crypto_secure_block_enabled boolean DEFAULT false,
ADD COLUMN crypto_secure_block_status character varying(255),
ADD COLUMN crypto_secure_entity character varying(500) DEFAULT NULL::character varying,
ADD COLUMN crypto_outer_ether_type character varying(255),
ADD COLUMN crypto_outer_vlan_tag character varying(255),
ADD COLUMN crypto_inner1_ether_type character varying(255),
ADD COLUMN crypto_inner1_vlan_tag character varying(255);

ALTER TABLE public.cn_ec_ctp
ADD COLUMN encryption_enabled boolean DEFAULT false;

CREATE TABLE public.cm_cua (
                               id integer NOT NULL,
                               password bytea,
                               role text,
                               scheduled_time bigint,
                               username text
);

ALTER TABLE ONLY public.cm_cua
    ADD CONSTRAINT cm_cua_pkey PRIMARY KEY (id);

CREATE TABLE public.cm_cua_ne_ids (
                                      id integer NOT NULL,
                                      ne_ids integer NOT NULL,
                                      ne_order integer NOT NULL
);


CREATE TABLE public.cm_cua_subnet_ids (
                                          id integer NOT NULL,
                                          subnet_ids integer NOT NULL,
                                          subnet_order integer NOT NULL
);


CREATE INDEX ix_cm_cua_ne_ids_fk_cm_cua_ne_ids_id ON public.cm_cua_ne_ids USING btree (id);
CREATE INDEX ix_cm_cua_subnet_ids_fk_cm_cua_subnet_ids_id ON public.cm_cua_subnet_ids USING btree (id);

alter table cn_network_element add column original_serial_num character varying(100) DEFAULT ''::character varying;
alter table cn_entity add column original_serial_num character varying(255);

CREATE TABLE public.gnss_rca_settings(
    dtype character varying(31),
    id integer NOT NULL,
    gnss_rca_enabled boolean DEFAULT false,
    rca_event_burst_idle_time integer,
    rca_idle_time integer,
    rca_max_idle_time integer
);


ALTER TABLE ONLY public.gnss_rca_settings
    ADD CONSTRAINT gnss_rca_settings_pkey PRIMARY KEY (id);

ALTER TABLE public.gnss_settings
    DROP COLUMN IF EXISTS gnss_rca_enabled,
    DROP COLUMN IF EXISTS rca_correlation_max_time_gap,
    DROP COLUMN IF EXISTS rca_idle_time,
    DROP COLUMN IF EXISTS rca_max_idle_time,
    DROP COLUMN IF EXISTS rca_event_burst_idle_time;

ALTER TABLE public.cn_stl_receiver
    DROP COLUMN IF EXISTS subscription_start_date,
    DROP COLUMN IF EXISTS subscription_end_date;

ALTER TABLE public.cn_croma_entity_ec ADD COLUMN shared_spectrum_org character varying(500);

ALTER TABLE public.ev_event_assoc ADD COLUMN objectuuid character varying(255);
ALTER TABLE public.ev_event_hist_assoc ADD COLUMN objectuuid character varying(255);
ALTER TABLE public.cn_network_element ADD COLUMN encryptionmode character varying(255);

CREATE TABLE public.cn_secy_f4 (
    id integer NOT NULL,
    name character varying(255),
    secureentity character varying(500) DEFAULT NULL::character varying,
    secyid integer
);


ALTER TABLE ONLY public.cn_secy_f4
    ADD CONSTRAINT cn_secy_f4_pkey PRIMARY KEY (id);

CREATE TABLE public.cn_cgrxsc_f4 (
    createtime character varying(500) DEFAULT NULL::character varying,
    id integer NOT NULL,
    name character varying(255),
    cursaid integer,
    sci character varying(500) DEFAULT NULL::character varying,
    startedtime character varying(500) DEFAULT NULL::character varying,
    state character varying(500) DEFAULT NULL::character varying,
    stoppedtime character varying(500) DEFAULT NULL::character varying
);


ALTER TABLE ONLY public.cn_cgrxsc_f4
    ADD CONSTRAINT cn_cgrxsc_f4_pkey PRIMARY KEY (id);

CREATE TABLE public.cn_cgtxsc_f4 (
    createtime character varying(500) DEFAULT NULL::character varying,
    id integer NOT NULL,
    name character varying(255),
    cursaid integer,
    sci character varying(500) DEFAULT NULL::character varying,
    startedtime character varying(500) DEFAULT NULL::character varying,
    state character varying(500) DEFAULT NULL::character varying,
    stoppedtime character varying(500) DEFAULT NULL::character varying
);


ALTER TABLE ONLY public.cn_cgtxsc_f4
    ADD CONSTRAINT cn_cgtxsc_f4_pkey PRIMARY KEY (id);

CREATE TABLE public.efd_segment (
    alm_ip_address character varying(255),
    id integer NOT NULL,
    port character varying(255),
    segment_fp_length integer,
    segment_fp_loss integer,
    segment_fp_loss_per_km integer,
    segment_number integer,
    segment_remark character varying(255),
    segment_row_status integer,
    segment_start_correction integer,
    segment_start_event_id integer,
    segment_stop_correction integer,
    segment_stop_event_id integer
);


CREATE SEQUENCE public.efd_segment_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.efd_segment_id_seq OWNED BY public.efd_segment.id;

ALTER TABLE ONLY public.efd_segment ALTER COLUMN id SET DEFAULT nextval('public.efd_segment_id_seq'::regclass);

SELECT pg_catalog.setval('public.efd_segment_id_seq', 1, false);

ALTER TABLE ONLY public.efd_segment
    ADD CONSTRAINT efd_segment_ip_port_segment_key UNIQUE (alm_ip_address, port, segment_number);

ALTER TABLE ONLY public.efd_segment
    ADD CONSTRAINT efd_segment_pkey PRIMARY KEY (id);

ALTER TABLE cn_entity
    ADD COLUMN assignedshelftype character varying(255);

ALTER TABLE ml_end_point
    ADD COLUMN if_type integer;

ALTER TABLE public.efd_trace_attribute
    ADD COLUMN dpa_mode integer;

ALTER TABLE public.ui_usr ALTER COLUMN name TYPE varchar(64);
ALTER TABLE public.ui_usr_sec ALTER COLUMN section TYPE varchar(64);

ALTER TABLE public.reflector_fam_record
    ADD COLUMN dpa_mode integer;

CREATE TABLE public.ev_subscription (
    id integer NOT NULL,
    jdoversion integer,
    topicname character varying(255)
);
CREATE TABLE public.ev_subscription_filter (
    id integer,
    type character varying(255)
);
ALTER TABLE ONLY public.ev_subscription ADD CONSTRAINT ev_subscription_pkey PRIMARY KEY (id);
CREATE INDEX ix_ev_subscription_filter_fk_ev_subscription_filter_id ON public.ev_subscription_filter USING btree (id);

UPDATE se_action SET name0 = REPLACE(name0 , 'Ensemble', 'Mosaic') where name0 like '%Ensemble%';
UPDATE se_role SET name0 = REPLACE(name0 , 'Ensemble', 'Mosaic') where name0 like '%Ensemble%';