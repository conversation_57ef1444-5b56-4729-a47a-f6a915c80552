update cn_network_element set uuid = gen_random_uuid() where uuid is null;
update sm_customers set uuid = gen_random_uuid() where uuid is null;
update sm_customer_service_group set uuid = gen_random_uuid() where uuid is null and group_type in (1,2);

-- Delete deprecated TE properties
DELETE FROM ml_te_property WHERE key in (101, 302);

UPDATE sm_service SET admin_state_eod = 'UP' WHERE admin_state = 0 AND shadow_copy IS TRUE;
UPDATE sm_service SET admin_state_eod = 'DOWN' WHERE admin_state = 1 AND shadow_copy IS TRUE;
UPDATE sm_service SET admin_state_eod = 'MAINTENANCE' WHERE admin_state = 2 AND shadow_copy IS TRUE;
UPDATE sm_service SET admin_state_eod = 'NA' WHERE admin_state_eod IS NULL;
UPDATE sm_service SET admin_state = NULL WHERE shadow_copy IS TRUE;

UPDATE sm_service SET layerprotocol = 0 WHERE shadow_copy IS TRUE;

UPDATE ui_template_props
SET prop_value = replace(prop_value, 'com.adva.common.util.filter.StringOperator;', 'com.adva.common.util.filter.StringOperatorInsensitive;')
WHERE templatedatadbimpl_id IN (SELECT id FROM ui_templates WHERE pagename = 'ALARMS_NETWORK' or pagename='EVENTS_NETWORK');

INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','equalize-inf','SLCEQNIF','Equalize Notification','equalize-inf','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO jdo_sequence (id, sequence_value) SELECT 'DEFAULT', 0 WHERE NOT EXISTS (select * from jdo_sequence where id='DEFAULT');
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','active-pilot-add-fail','ACTPLAF','Active-pilot-add-fail','active-pilot-add-fail','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','adjust-restore','ADJRST','Adjust-restore','adjust-restore','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','block-fail','BLCKFAIL','Block-fail','block-fail','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','block-pass','BLCKPASS','Block-pass','block-pass','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','sys-boot-completed','BOOTCMPL','Sys-boot-completed','sys-boot-completed','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','certificate-trust-changed','CERTTRST','Certificate-trust-changed','certificate-trust-changed','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','controller-factory-reset-notif','CFCTRESNTF','Controller-factory-reset-notif','controller-factory-reset-notif','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','pkey-set-extractable','PKSEXT','Pkey-set-extractable','pkey-set-extractable','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','scheduled-db-backup-notif','SDBBNTF','Scheduled-db-backup-notif','scheduled-db-backup-notif','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','sys-shutdown-initiated','SHTDNINIT','Sys-shutdown-initiated','sys-shutdown-initiated','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','software-downgrade','SWDWNGR','Software-downgrade','software-downgrade','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','switch-fail','SWTFAIL','Switch-fail','switch-fail','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','switch-pass','SWTPASS','Switch-pass','switch-pass','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,severity_working,severity_protecting,severity_no_service,alarm_id,raise_clear_name,raise_clear_number)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'ALARM_EC','temperature-high-warning','TEMPHW','Temperature high warning','NE_DEFINED','NE_DEFINED','NE_DEFINED',37,'temperature-high-warning',37);
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,severity_working,severity_protecting,severity_no_service,alarm_id,raise_clear_name,raise_clear_number)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'ALARM_EC','masterslave-cfg-fault','MTSLCFGF','Master/Slave Clock Config Fault','NE_DEFINED','NE_DEFINED','NE_DEFINED',327,'masterslave-cfg-fault',327);
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,severity_working,severity_protecting,severity_no_service,alarm_id,raise_clear_name,raise_clear_number)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'ALARM_EC','pilot-add-loss','PLADDLOS','Pilot Add Loss','NE_DEFINED','NE_DEFINED','NE_DEFINED',5324,'pilot-add-loss',5324);
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';

UPDATE fam_record SET linkloss=(SELECT value::DOUBLE PRECISION FROM fam_link_properties WHERE fam_record.id=fam_link_properties.id AND key='Link Loss');
UPDATE fam_record SET linklength=(SELECT value::DOUBLE PRECISION FROM fam_link_properties WHERE fam_record.id=fam_link_properties.id AND key='Link Length');

UPDATE cn_notification_ec
SET message = 'server-signal-adjustment'
WHERE short_name = 'SSA' AND notification_ec_type = 'ALARM_EC';


UPDATE se_user
SET UUID = uuid_generate_v4();

UPDATE cn_ne_backup SET ne_iden = REPLACE(REPLACE(ne_iden, '( ', '('), ' )', ')');

INSERT INTO cn_ne_backup (id,jdoversion,last_backup,last_error,ne_id,ne_iden,next_auto_backup,next_manual_backup,result,status,use_global)
SELECT ne.id,1,null,null, ne.id, concat(ne.name0,' (',ne.ipaddress,')'),null,0,2,0,true
FROM cn_network_element ne
         LEFT JOIN cn_ne_backup nb ON ne.id = nb.ne_id
WHERE nb.ne_id IS NULL;

UPDATE pd_eth_service_intent set lifecycle_state=0 where lifecycle_state is null;

delete from cn_ne_backup where ne_id not in (select id from cn_network_element);

INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','install-end','INSTEND','Install-end','install-end','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','install-fail','INSTFAIL','Install-fail','install-fail','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','install-pass','INSTPASS','Install-pass','install-pass','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','install-start','INSTSTR','Install-start','install-start','*******.4.1.2544.********.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,severity_working,severity_protecting,severity_no_service,alarm_id,raise_clear_name,raise_clear_number)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'ALARM_EC','certificate-revoked','CEREVOK','Certificate Revoked','NE_DEFINED','NE_DEFINED','NE_DEFINED',5320,'certificate-revoked',5320);
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
