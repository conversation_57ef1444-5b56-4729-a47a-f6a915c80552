--
-- Copyright 2023 Adtran Networks SE. All rights reserved.
--
-- Owner: ask<PERSON><PERSON><PERSON>
--
CREATE OR REPLACE FUNCTION changeips() RETURNS TEXT AS $$

DECLARE
  out          TEXT := '';
  third_octet  INT DEFAULT 0;
  fourth_octet INT DEFAULT 1;
  last_ip      INT DEFAULT 255;
  start_ip     TEXT := '192.168.';
  new_ip       TEXT := '';
  count        INT := 0;
  nes          CURSOR FOR SELECT ne.* FROM cn_network_element ne WHERE ne.ipaddress <> '' and ne.ipaddress is not null and ne.type0 not in ('350000', '2') and ne.local_id is null;
  ne RECORD;
BEGIN
  FOR ne IN nes LOOP
    new_ip := start_ip || third_octet || '.' || fourth_octet;

    update cn_network_element set ipaddress = new_ip where id = ne.id;
    raise notice '% - > %', ne.ipaddress, new_ip;
    count := count + 1;
    fourth_octet := fourth_octet + 1;
    IF fourth_octet = last_ip
    THEN
      fourth_octet := 1;
      third_octet := third_octet + 1;
    END IF;
    IF third_octet > last_ip THEN
      RAISE INFO 'No more ip adresses';
    END IF;
  END LOOP;

  RETURN count || ' ip addresses updated.';

END;$$

LANGUAGE plpgsql;

SELECT changeips();

DROP FUNCTION IF EXISTS changeips();