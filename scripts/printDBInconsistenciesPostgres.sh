#!/bin/bash

. /etc/setenv.sh

LD_LIBRARY_PATH=$NMS_HOME/fsp_nm/postgres/lib:$LD_LIBRARY_PATH
export LD_LIBRARY_PATH

COUNTER=0
MAX_TRIES=4

if [ -f $NMS_HOME/fsp_nm/dbaccess.txt ]; then
    while [ $COUNTER -lt $MAX_TRIES ]
    do
        COUNTER=`expr $COUNTER + 1`

        PGPASSWORD=
        OLD_STTY=`stty -g`
        echo "Enter DB Password: "
        stty -echo
        read PGPASSWORD
        stty echo
        stty $OLD_STTY
        echo ""

        #Trim
        TEST_PASSWORD=`echo "$PGPASSWORD" | tr -d ' '`
        if [ -z "$TEST_PASSWORD" ]; then
            continue
        fi

        export PGPASSWORD
        $NMS_HOME/fsp_nm/postgres/bin/psql -U adva -c "" fnm

        if [ $? -eq 0 ]; then
            COUNTER=-1
            break
        fi
    done

    if [ $COUNTER -ne -1 ]; then
        PGPASSWORD=
        unset PGPASSWORD
        echo "You provided invalid DB Password too many times. Exiting..."
        exit 1
    fi
else
    PGPASSWORD='NeverChange'
    export PGPASSWORD
fi

$NMS_HOME/fsp_nm/postgres/bin/psql -q -U adva -f printDBInconsistenciesPostgres.sql fnm

unset PGPASSWORD