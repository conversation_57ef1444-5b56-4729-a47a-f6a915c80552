ALTER TABLE cn_port DROP COLUMN IF EXISTS allocation,
                    DROP COLUMN IF EXISTS acceptripv2pkts,
                    DROP COLUMN IF EXISTS cvlanethtype,
                    DROP COLUMN IF EXISTS clearwtrtimer,
                    DROP COLUMN IF EXISTS dhcpenabled,
                    DROP COLUMN IF EXISTS ffpdelay,
                    DROP COLUMN IF EXISTS ffptype,
                    DROP COLUMN IF EXISTS h4enabed,
                    DROP COLUMN IF EXISTS holdofftimer,
                    DROP COLUMN IF EXISTS innervlanloopback1,
                    DROP COLUMN IF EXISTS innervlanloopback2,
                    DROP COLUMN IF EXISTS innervlanloopback3,
                    DROP COLUMN IF EXISTS innervlanloopbackmask,
                    DROP COLUMN IF EXISTS ipaddress,
                    DROP COLUMN IF EXISTS lcasenabled,
                    DROP COLUMN IF EXISTS loopbackswapsada,
                    DROP COLUMN IF EXISTS mediatype,
                    DROP COLUMN IF EXISTS mgmtmactunnelenabled,
                    DROP COLUMN IF EXISTS mgmtsvlanenabled,
                    DROP COLUMN IF EXISTS mgmtsvlanid,
                    DROP COLUMN IF EXISTS mgmttunnelaclenabled,
                    DROP COLUMN IF EXISTS mgmttunnelencapsulation,
                    DROP COLUMN IF EXISTS mgmtvlanid,
                    DROP COLUMN IF EXISTS mgmtvlantunnelenabled,
                    DROP COLUMN IF EXISTS numconfiguredfdfrs,
                    DROP COLUMN IF EXISTS paerole,
                    DROP COLUMN IF EXISTS portloopback,
                    DROP COLUMN IF EXISTS portloopbacktime,
                    DROP COLUMN IF EXISTS portnegotiatedspeed,
                    DROP COLUMN IF EXISTS portspeed,
                    DROP COLUMN IF EXISTS rxpauseframesenabled,
                    DROP COLUMN IF EXISTS sendripv2pkts,
                    DROP COLUMN IF EXISTS shapingbandwidth,
                    DROP COLUMN IF EXISTS shapingenabled,
                    DROP COLUMN IF EXISTS subnetmask,
                    DROP COLUMN IF EXISTS svlanethtype,
                    DROP COLUMN IF EXISTS type,
                    DROP COLUMN IF EXISTS txpauseframesenabled,
                    DROP COLUMN IF EXISTS userstage1,
                    DROP COLUMN IF EXISTS userstage2,
                    DROP COLUMN IF EXISTS vlanloopback1,
                    DROP COLUMN IF EXISTS vlanloopback2,
                    DROP COLUMN IF EXISTS vlanloopback3,
                    DROP COLUMN IF EXISTS vlanloopbackmask,
                    DROP COLUMN IF EXISTS vcgindex,
                    DROP COLUMN IF EXISTS wtrtimer;
ALTER TABLE cn_esa_probe_mep_cos DROP COLUMN IF EXISTS probeindex825;
DROP TABLE IF EXISTS  cn_eth_flow;
DROP TABLE IF EXISTS cn_eth_flow_policer;
DROP TABLE IF EXISTS cn_eth_prio_map;
DROP TABLE IF EXISTS cn_eth_qos;
ALTER TABLE cn_eth_service DROP COLUMN IF EXISTS acceptripv2pkts,
                           DROP COLUMN IF EXISTS afptype,
                           DROP COLUMN IF EXISTS circuitname,
                           DROP COLUMN IF EXISTS dhcpenabled,
                           DROP COLUMN IF EXISTS egresspopenabled,
                           DROP COLUMN IF EXISTS egresspopenabled,
                           DROP COLUMN IF EXISTS fdfrexistoncomismatch,
                           DROP COLUMN IF EXISTS ffpdelay,
                           DROP COLUMN IF EXISTS ingresspushenabled,
                           DROP COLUMN IF EXISTS ipaddress,
                           DROP COLUMN IF EXISTS lanffptype,
                           DROP COLUMN IF EXISTS locallinkid,
                           DROP COLUMN IF EXISTS macbridgeaction,
                           DROP COLUMN IF EXISTS macbridgeaginginterval,
                           DROP COLUMN IF EXISTS macbridgeflushentries,
                           DROP COLUMN IF EXISTS macbridgetablesize,
                           DROP COLUMN IF EXISTS mediatype,
                           DROP COLUMN IF EXISTS mgmtmactunnelenabled,
                           DROP COLUMN IF EXISTS mgmttunnelaclenabled,
                           DROP COLUMN IF EXISTS mgmtvlanid,
                           DROP COLUMN IF EXISTS mgmtvlantunnelenabled,
                           DROP COLUMN IF EXISTS mgmttunnelencapsulation,
                           DROP COLUMN IF EXISTS numconfiguredfdfrs,
                           DROP COLUMN IF EXISTS portloopback,
                           DROP COLUMN IF EXISTS portloopbacktime,
                           DROP COLUMN IF EXISTS portmode,
                           DROP COLUMN IF EXISTS portnegotiatedspeed,
                           DROP COLUMN IF EXISTS portspeed,
                           DROP COLUMN IF EXISTS priomapmode,
                           DROP COLUMN IF EXISTS priority,
                           DROP COLUMN IF EXISTS priovid,
                           DROP COLUMN IF EXISTS remotelinkids,
                           DROP COLUMN IF EXISTS rxpauseenabled,
                           DROP COLUMN IF EXISTS sendripv2pkts,
                           DROP COLUMN IF EXISTS subnetmask,
                           DROP COLUMN IF EXISTS swapsada,
                           DROP COLUMN IF EXISTS tagenabled,
                           DROP COLUMN IF EXISTS taggedframesenabled,
                           DROP COLUMN IF EXISTS txpauseenabled,
                           DROP COLUMN IF EXISTS type,
                           DROP COLUMN IF EXISTS untaggedframesenabled,
                           DROP COLUMN IF EXISTS vlanethtype,
                           DROP COLUMN IF EXISTS vlanid,
                           DROP COLUMN IF EXISTS vlanloopback1,
                           DROP COLUMN IF EXISTS vlanloopback2,
                           DROP COLUMN IF EXISTS vlanloopback3,
                           DROP COLUMN IF EXISTS vlanloopbackmask,
                           DROP COLUMN IF EXISTS vlantrunking,
                           DROP COLUMN IF EXISTS waninterface;
ALTER TABLE cn_fdfr DROP COLUMN IF EXISTS charnamemismatch,
                    DROP COLUMN IF EXISTS  fdfr_name_mismatch;
ALTER TABLE cn_fdfr_end DROP COLUMN IF EXISTS ethernetservicedbimpl;
DROP TABLE IF EXISTS cn_fdfr_to_aend;
DROP TABLE IF EXISTS cn_fdfr_to_zend;
ALTER TABLE cn_ftp DROP COLUMN IF EXISTS ftpdeletedmismatch,
                   DROP COLUMN IF EXISTS  lag,
                   DROP COLUMN IF EXISTS  lagnamemismatch;
DROP TABLE IF EXISTS cn_lag;
DROP TABLE IF EXISTS cn_lagport;

DROP TABLE IF EXISTS ev_event_parent;