DO
$$
    DECLARE
        faultAnalysisRecordIds     INTEGER[];
        fingerprintRecordIds       INTEGER[];
        famRecordIdsToDelete       INTEGER[];
        famRecordIdToDelete        INTEGER;
        ownerDelete                INTEGER[];
        owner                      INTEGER;
        loopcounter                INTEGER;

    BEGIN
        RAISE NOTICE 'Clean FAM records start';

        faultAnalysisRecordIds := ARRAY(
                SELECT fa_record
                FROM fam_point
                WHERE fa_record IS NOT NULL
            );
        RAISE NOTICE 'faultAnalysisRecordIds found: %', array_length(faultAnalysisRecordIds, 1);

        fingerprintRecordIds := ARRAY(
                SELECT fp_record
                FROM fam_point
                WHERE fp_record IS NOT NULL
            );
        RAISE NOTICE 'fingerprintRecordIds found: %', array_length(fingerprintRecordIds, 1);

        famRecordIdsToDelete := ARRAY(
                SELECT id
                FROM fam_record
                WHERE id NOT IN (
                    SELECT fa_record
                    FROM fam_point
                    WHERE fa_record IS NOT NULL
                )
                  AND id NOT IN (
                    SELECT fp_record
                    FROM fam_point
                    WHERE fp_record IS NOT NULL
                )
            );
        RAISE NOTICE 'famRecordIdsToDelete found: %', array_length(famRecordIdsToDelete, 1);
        loopcounter := 1;

        foreach famRecordIdToDelete in array famRecordIdsToDelete
            loop
                -- RAISE NOTICE 'DELTE fam_event FOR %', famRecordIdToDelete;
                DELETE FROM fam_event WHERE owner_id = famRecordIdToDelete;
                -- RAISE NOTICE 'DELTE fam_link_properties FOR %', famRecordIdToDelete;
                DELETE FROM fam_link_properties WHERE id = famRecordIdToDelete;

                -- RAISE NOTICE 'CREATE ARRAY fam_trace.id FOR %', famRecordIdToDelete;
                ownerDelete := ARRAY(
                        SELECT fam_trace.id FROM fam_trace WHERE fam_trace.owner_id = famRecordIdToDelete
                    );

                RAISE NOTICE 'ownerDelete: % famRecordIdToDelete: % Durchlauf: %', array_length(ownerDelete, 1), famRecordIdToDelete, loopcounter;

                foreach owner in array ownerDelete
                    loop
                        DELETE FROM fam_trace_data WHERE fam_trace_data.owner_id = owner;
                        DELETE FROM fam_trace_settings WHERE fam_trace_settings.owner_id = owner;
                    end loop;

                DELETE FROM fam_trace WHERE fam_trace.owner_id = famRecordIdToDelete;
                DELETE FROM fam_record WHERE id = famRecordIdToDelete;
                loopcounter := loopcounter + 1;
            end loop;
        RAISE NOTICE 'Clean FAM records done';
    END
$$
