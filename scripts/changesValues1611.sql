INSERT INTO jdo_sequence (id, sequence_value) SELECT 'DEFAULT', 0 WHERE NOT EXISTS (select * from jdo_sequence where id='DEFAULT');
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','certificate-from-pem-created','CRTPEMCR','Certificate-from-pem-created','certificate-from-pem-created','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','csr-activated','CSRACT','Csr-activated','csr-activated','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','csr-exported','CSREXP','Csr-exported','csr-exported','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','csr-key-pair-removed','CSRREM','Csr-key-pair-removed','csr-key-pair-removed','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','minimum-attenuation-deny','MINATDNY','Minimum-attenuation-deny','minimum-attenuation-deny','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','minimum-attenuation-fail','MINATFL','Minimum-attenuation-fail','minimum-attenuation-fail','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,trap_name,trap_enterprise)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'EVENT_EC','minimum-attenuation-pass','MINATPAS','Minimum-attenuation-pass','minimum-attenuation-pass','1.3.6.1.4.1.2544.1.20.1.2.1');
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,severity_working,severity_protecting,severity_no_service,alarm_id,raise_clear_name,raise_clear_number)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'ALARM_EC','plug-2-host-interface-fail','P2HIFF','Plug-to-host Interface Failure','NE_DEFINED','NE_DEFINED','NE_DEFINED',35,'plug-2-host-interface-fail',35);
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,severity_working,severity_protecting,severity_no_service,alarm_id,raise_clear_name,raise_clear_number)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'ALARM_EC','host-2-plug-interface-fail','H2PIFF','Host-to-plug Interface Failure','NE_DEFINED','NE_DEFINED','NE_DEFINED',36,'host-2-plug-interface-fail',36);
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,severity_working,severity_protecting,severity_no_service,alarm_id,raise_clear_name,raise_clear_number)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'ALARM_EC','bundle-power-sum-alert','PWRSUM','Bundle power sum alart','NE_DEFINED','NE_DEFINED','NE_DEFINED',100,'bundle-power-sum-alert',100);
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,severity_working,severity_protecting,severity_no_service,alarm_id,raise_clear_name,raise_clear_number)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'ALARM_EC','pump-force-remain-on','PUMPON','pump-force-remain-on','NE_DEFINED','NE_DEFINED','NE_DEFINED',5319,'pump-force-remain-on',5319);
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,severity_working,severity_protecting,severity_no_service,alarm_id,raise_clear_name,raise_clear_number)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'ALARM_EC','pilot-rx-suspended','PRXSUSP','Pilot Rx Suspended','NE_DEFINED','NE_DEFINED','NE_DEFINED',5321,'pilot-rx-suspended',5321);
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,severity_working,severity_protecting,severity_no_service,alarm_id,raise_clear_name,raise_clear_number)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'ALARM_EC','local-degrade-indication','LDEGI','Local Degrade Indication','NE_DEFINED','NE_DEFINED','NE_DEFINED',5322,'local-degrade-indication',5322);
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';
INSERT INTO cn_notification_ec (id,notification_ec_type,name,short_name,message,severity_working,severity_protecting,severity_no_service,alarm_id,raise_clear_name,raise_clear_number)
VALUES
((SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),'ALARM_EC','remote-degrade-indication','RDEGI','Remote Degrade Indication','NE_DEFINED','NE_DEFINED','NE_DEFINED',5323,'remote-degrade-indication',5323);
INSERT INTO cn_notification_ne_type_list (ne_type_list,notification_ec_id) VALUES ('NETWORK_ELEMENT_TYPE_F8',(SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'));
UPDATE JDO_SEQUENCE set sequence_value = sequence_value + 1 WHERE id LIKE 'DEFAULT';

-- Delete MODE property from ML connection points
DELETE FROM ml_te_property WHERE KEY = 204 AND te_id IN (SELECT mtp.te_id FROM ml_te_property mtp JOIN ml_connection_point mcp ON mtp.te_id = mcp.id WHERE mtp.key = 204);

-- Delete deprecated TE properties
DELETE FROM ml_te_property WHERE key in (616, 617, 619, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633, 634, 635);

-- Update priority for SW Upgrade with default value
UPDATE cn_network_element_swupgrade SET priority=5 WHERE priority IS NULL;

------- Start: FNMD-100882 Removal of ML connection references in events
start transaction;

-- Auxiliary table to store connection ids mapped to events
create table events_to_update_ids(
                                     id serial primary key,
                                     event_id bigint,
                                     connection_id int

);
-- Auxiliary table to store trail names associated to events
create table event_trail_names(
                                  id serial primary key,
                                  event_id bigint,
                                  label varchar(1000)
);

-- Populate events_to_update_ids with events that are mapped to ML connections that are not network connections
insert into events_to_update_ids (event_id, connection_id) select eea.id, eea.objectid
from ev_event_assoc eea, ml_connection mc
where eea.objectid = mc.id and mc.conntype != 6;

-- Get names of the trails that are mapped to events with connection references
insert into event_trail_names (event_id, label) select eea.id, mte.label
from ev_event_assoc eea, ml_service ms, ml_topology_element mte
where eea.objectid = ms.id and mte.id = ms.id
  and eea.id in (select event_id from events_to_update_ids);

-- Replace servicename (Connections column) with trail names if it is set
update ev_event
set (servicename) = (select label from event_trail_names where ev_event.id = event_trail_names.event_id limit 1)
where id in (select event_id from event_trail_names)
  and servicename like 'CC-%';

-- Add " ..." to events that have more than one trail association
update ev_event set servicename = servicename || ' ...' where id in (select distinct event_id FROM event_trail_names GROUP BY event_id HAVING COUNT(event_id) > 1);

-- Set value to empty if there was no trail associated with event
update ev_event
set servicename = ''
where id not in (select event_id from event_trail_names)
  and servicename like 'CC-%';

-- Delete MLT_SERVICE references for ML connections
delete from ev_event_assoc where objectid in (select connection_id from events_to_update_ids) and objecttype = 304;

drop table event_trail_names;
drop table events_to_update_ids;

commit;
------- End: FNMD-100882 Removal of ML connection references in events

-- Replace ev_event_parent with ev_event_assoc SERVICE_FOLDER entries
INSERT INTO ev_event_assoc (id, objectid, objecttype) SELECT event_id, parent_id, 102 FROM ev_event_parent;

-- Fix UI service view representation for system select wavelength values
update ml_ui_service set wavelength = 'Selected by System' where wavelength = 'Selected by System/0G';
-- Fix FNMD-104620

update se_role
set state = 1
where id = (SELECT id
            from se_role
            where id IN (SELECT children_id
                         from se_role_children src
                         where src.id IN (SELECT children_id
                                          from se_role_children src
                                          where src.id = (SELECT role.id
                                                          FROM se_role role
                                                                   JOIN se_group grp ON role.id = grp.roleID
                                                          WHERE grp.id = (SELECT id from se_group where name0 = 'Configurator'))))
              and classid = 'PushPROFSNMP')
  and 1 = (SELECT state
           from se_role
           where id IN (SELECT children_id
                        from se_role_children src
                        where src.id IN (SELECT children_id
                                         from se_role_children src
                                         where src.id = (SELECT role.id
                                                         FROM se_role role
                                                                  JOIN se_group grp ON role.id = grp.roleID
                                                         WHERE grp.id = (SELECT id from se_group where name0 = 'Configurator'))))
             and classid = 'GetCfm')
