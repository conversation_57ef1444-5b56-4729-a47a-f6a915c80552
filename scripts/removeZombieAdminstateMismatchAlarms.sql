--
--  Copyright 2023 Adtran Networks SE. All rights reserved.
--
--  Owner: marcelc
--

-- This script removes zombie adminstate mismatch alarms, i.e., mismatch alarms for hidden classic connections,
-- that are incorrect, and that can not be cleared by the user in any way, but are visible in the alarms tab
-- of the network view.
DO
$$

	DECLARE
	
		mismatch_alarms_WITH_any_ML_associations bigint[];
		mismatch_alarms_WITHOUT_any_ML_associations bigint[];
		hidden_classic_connections_with_mismatch_alarms bigint[];
		zombie_alarm_candidates bigint[];
		zombie_alarms bigint[];
		
	
	BEGIN
		
		-- AdminState mismatch alarms that are associated with any ML service
		mismatch_alarms_WITH_any_ML_associations := ARRAY(
			SELECT DISTINCT alarm.id
				FROM ev_event alarm
				LEFT JOIN ev_event_assoc assoc ON alarm.id = assoc.id
					WHERE alarm.trapid IN (300030,300028)
					AND assoc.objecttype = 304                     -- 304 == MLT_SERVICE
		);
		
		
		-- AdminState mismatch alarms that are NOT associated with any ML service
		mismatch_alarms_WITHOUT_any_ML_associations := ARRAY(
			SELECT DISTINCT alarm.id
				FROM ev_event alarm
					WHERE alarm.trapid IN (300030,300028)
					AND NOT alarm.id = ANY(mismatch_alarms_WITH_any_ML_associations)
		);
		RAISE NOTICE 'Number of AdminState mismatch alarms that are NOT associated with ANY ML trail:';
		RAISE NOTICE '%', array_length(mismatch_alarms_WITHOUT_any_ML_associations, 1);
		
		
		-- the IDs of hidden classic connections with AdminState mismatch alarms
		hidden_classic_connections_with_mismatch_alarms := ARRAY(
            SELECT DISTINCT con.id
				FROM cn_connection con
				JOIN ev_event_conns eventCon ON con.id = eventCon.val
				JOIN ev_event alarm ON alarm.id = eventCon.id
					WHERE con.ishidden = true
					AND alarm.trapid IN (300030,300028)
		);
		RAISE NOTICE 'Number of hidden classic connections with AdminState mismatch alarms:';
		RAISE NOTICE '%', array_length(hidden_classic_connections_with_mismatch_alarms, 1);
		
		
		-- mismatch alarms related to a hidden classic connection
		zombie_alarm_candidates := ARRAY(
			SELECT DISTINCT alarm.id
				FROM ev_event alarm
				JOIN ev_event_conns eventCon ON alarm.id = eventCon.id
					WHERE alarm.trapid IN (300030,300028)
					AND eventCon.val = ANY(hidden_classic_connections_with_mismatch_alarms)
		);
		RAISE NOTICE 'Number of mismatch alarms related to a hidden classic connection:';
		RAISE NOTICE '%', array_length(zombie_alarm_candidates, 1);
		
		
		-- zombie alarms, i.e., candidates, which also have no associated ML trail
		zombie_alarms := ARRAY(
			SELECT DISTINCT alarm.id
				FROM ev_event alarm
					WHERE alarm.id = ANY(zombie_alarm_candidates)
					AND alarm.id = ANY(mismatch_alarms_WITHOUT_any_ML_associations)
		);
		RAISE NOTICE 'Zombie AdminState mismatch alarms found and removed:';
		RAISE NOTICE '%', array_length(zombie_alarms, 1);
		RAISE NOTICE '%', zombie_alarms;

		-- delete zombies from all related tables
		DELETE FROM ev_event WHERE id = ANY(zombie_alarms);
		DELETE FROM ev_event_conns WHERE id = ANY(zombie_alarms);
		DELETE FROM ev_event_assoc WHERE id = ANY(zombie_alarms);
END;
$$