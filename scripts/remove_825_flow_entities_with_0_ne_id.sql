begin;
CREATE TEMP TABLE orphan_shaper_entities_ids AS
  (SELECT f.id FROM cn_eth_qos f JOIN cn_managed_object mo ON f.id=mo.id WHERE mo.ne_id = 0);

DELETE FROM cn_eth_qos f WHERE f.id IN (SELECT id from orphan_shaper_entities_ids);
DELETE FROM cn_managed_object mo WHERE mo.id IN (SELECT id from orphan_shaper_entities_ids);

DROP TABLE IF EXISTS orphan_shaper_entities_ids;

DROP TABLE IF EXISTS orphan_policer_entities_ids;

CREATE TEMP TABLE orphan_policer_entities_ids AS
  (SELECT f.id FROM cn_eth_flow_policer f JOIN cn_managed_object mo ON f.id=mo.id WHERE mo.ne_id = 0);

DELETE FROM cn_eth_flow_policer f WHERE f.id IN (SELECT id from orphan_policer_entities_ids);
DELETE FROM cn_managed_object mo WHERE mo.id IN (SELECT id from orphan_policer_entities_ids);

DROP TABLE IF EXISTS orphan_policer_entities_ids;

DROP TABLE IF EXISTS orphan_flow_entities_ids;
CREATE TEMP TABLE orphan_flow_entities_ids AS
  (SELECT f.id FROM cn_eth_flow f JOIN cn_managed_object mo ON f.id=mo.id WHERE mo.ne_id = 0);

DELETE FROM cn_eth_flow f WHERE f.id IN (SELECT id from orphan_flow_entities_ids);
DELETE FROM cn_managed_object mo WHERE mo.id IN (SELECT id from orphan_flow_entities_ids);

DROP TABLE IF EXISTS orphan_flow_entities_ids;

DROP TABLE IF EXISTS orphan_shaper_entities_ids;

end;