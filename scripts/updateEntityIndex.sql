--
-- Copyright 2023 Adtran Networks SE. All rights reserved.
--
-- Owner: tomaszm
--
CREATE TYPE entity_idx_type AS (prefix int, idx varchar(255));
CREATE TABLE tmp_entity_idx_data(mo_id int, entityIdx varchar(255), containedIn varchar(255));

CREATE OR REPLACE FUNCTION createEntityIndex(category integer , indexes VARCHAR(255))
  RETURNS entity_idx_type
AS $$
DECLARE
  result entity_idx_type;
BEGIN
  SELECT category, indexes INTO result;
  RETURN result;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION entityIdxToString(ei entity_idx_type)
  RETURNS VARCHAR(255)
AS $$
BEGIN
  IF ei IS null THEN RETURN null;
  END IF;
  RETURN concat_ws('^', ei.prefix, ei.idx);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION getCategoryAndIndexes(t_id integer, t_jdoclass varchar(255))
  RETURNS entity_idx_type
AS $$
DECLARE
  resultEntityIdx entity_idx_type;
BEGIN

  IF t_jdoclass='LagF3DBImpl'                               THEN resultEntityIdx := createEntityIndex(1,  (SELECT concat_ws('.', neindex, lag_index)                FROM cn_lag_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='LagPortF3DBImpl'                       THEN resultEntityIdx := createEntityIndex(2,  (SELECT concat_ws('.', neindex, lagindex, lagportindex)   FROM cn_lagport_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='StsVcPathF3DBImpl'                     THEN resultEntityIdx := createEntityIndex(3,  (SELECT concat_ws('.', neindex, shelfindex, slotindex, ocnstmindex, parentifindex, vcpathindex)   FROM cn_logicalport_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='VtVcPathF3DBImpl'                      THEN resultEntityIdx := createEntityIndex(4,  (SELECT concat_ws('.', neindex, shelfindex, slotindex, ocnstmindex, parentifindex, vcpathindex)   FROM cn_logicalport_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='E1T1F3DBImpl'                          THEN resultEntityIdx := createEntityIndex(5,  (SELECT concat_ws('.', neindex, shelfindex, slotindex, ocnstmindex, parentifindex, portindex)   FROM cn_logicalport_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='E3T3F3DBImpl'                          THEN resultEntityIdx := createEntityIndex(6,  (SELECT concat_ws('.', neindex, shelfindex, slotindex, ocnstmindex, parentifindex, portindex)   FROM cn_logicalport_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='VcgF3DBImpl'                           THEN resultEntityIdx := createEntityIndex(7,  (SELECT concat_ws('.', neindex, shelfindex, slotindex, vcgportindex)   FROM cn_logicalport_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='ShelfDBImpl'                           THEN resultEntityIdx := createEntityIndex(13, (SELECT concat_ws('.', neindex, shelfindex)   FROM cn_shelf WHERE id=t_id));
  ELSEIF t_jdoclass='ShelfF3DBImpl'                         THEN resultEntityIdx := createEntityIndex(13, (SELECT concat_ws('.', neindex, shelfindex)   FROM cn_shelf WHERE id=t_id));
  ELSEIF t_jdoclass='ShelfFSPGE20XDBImpl'                   THEN resultEntityIdx := createEntityIndex(13, (SELECT concat_ws('.', neindex, shelfindex)   FROM cn_shelf WHERE id=t_id));
  ELSEIF t_jdoclass='Ethernet1x10GCardDBImpl'               THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
 ELSEIF t_jdoclass='Ethernet1x10GHighPerformanceCardDBImpl' THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='Ethernet10x1GCardDBImpl'               THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
 ELSEIF t_jdoclass='Ethernet10x1GHighPerformanceCardDBImpl' THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetGE4ECCCardDBImpl'              THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetFE36ECardDBImpl'               THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetGE4ECCCardDBImpl'              THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetGE4SCCCardDBImpl'              THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetGE8SCCCardDBImpl'              THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetXG1SCCCardDBImpl'              THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetXG1XCCCardDBImpl'              THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='PseudoWireE1T1CardDBImpl'              THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='PseudoWireOcnStmCardDBImpl'            THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='ScuFSP150CMDBImpl'                     THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='ScuTFSP150CMDBImpl'                    THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='AmiF3DBImpl'                           THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='NemiF3DBImpl'                          THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='NteF3DBImpl'                           THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='NTEFSPGE20XDBImpl'                     THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='NTEFSPXG210DBImpl'                     THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='NTUFSP150CMDBImpl'                     THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='OcStmCardF3DBImpl'                     THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='StiF3DBImpl'                           THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='StuF3DBImpl'                           THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='SwfF3DBImpl'                           THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='ModuleCPMRFSP150CMDBImpl'              THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));

  -- psu of cpmr is artificially created entry and need to be handled differently.
  ELSEIF t_jdoclass='PowerSupplyDBImpl' AND exists (select 1 from cn_entity e join cn_network_element n on e.ne_id= n.id where e.id = t_id and n.local_id is not null) THEN resultEntityIdx := createEntityIndex(102, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_entity WHERE id=t_id));
  ELSEIF t_jdoclass='PowerSupplyF3DBImpl' AND exists (select 1 from cn_entity e join cn_network_element n on e.ne_id= n.id where e.id = t_id and n.local_id is not null) THEN resultEntityIdx := createEntityIndex(102, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_entity WHERE id=t_id));
  ELSEIF t_jdoclass='PowerSupplyDBImpl'                     THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_entity WHERE id=t_id));
  ELSEIF t_jdoclass='PowerSupplyF3DBImpl'                   THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_entity WHERE id=t_id));
    -- fan of cpmr is artificially created entry and need to be handled differently.
  ELSEIF t_jdoclass='FanDBImpl' AND exists (select 1 from cn_entity e join cn_network_element n on e.ne_id= n.id where e.id = t_id and n.local_id is not null) THEN resultEntityIdx := createEntityIndex(103, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_entity WHERE id=t_id));
  ELSEIF t_jdoclass='FanF3DBImpl' AND exists (select 1 from cn_entity e join cn_network_element n on e.ne_id= n.id where e.id = t_id and n.local_id is not null) THEN resultEntityIdx := createEntityIndex(103, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_entity WHERE id=t_id));
  ELSEIF t_jdoclass='FanDBImpl'                             THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_entity WHERE id=t_id));
  ELSEIF t_jdoclass='FanF3DBImpl'                           THEN resultEntityIdx := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_entity WHERE id=t_id));

  ELSEIF t_jdoclass='PortF3AccDBImpl'                       THEN resultEntityIdx := createEntityIndex(38, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex) FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='PortFSPGE20XAccDBImpl'                 THEN resultEntityIdx := createEntityIndex(38, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex) FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='FlowF3DBImpl'                          THEN resultEntityIdx := createEntityIndex(39, (SELECT concat_ws('.', neindex, shelfindex, slotindex, accportindex, flowindex) FROM cn_flow_cm WHERE id=t_id));
  ELSEIF t_jdoclass='QOSShaperF3DBImpl'                     THEN resultEntityIdx := createEntityIndex(40, (SELECT concat_ws('.', neindex, shelfindex, slotindex, accportindex, flowindex, typeindex, shaperindex) FROM cn_shaper_cm WHERE id=t_id));
  ELSEIF t_jdoclass='PortF3NetDBImpl'                       THEN resultEntityIdx := createEntityIndex(41, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex)   FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='PortFSPGE20XNetDBImpl'                 THEN resultEntityIdx := createEntityIndex(41, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex)   FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='QOSFlowPolicerDBImpl'                  THEN resultEntityIdx := createEntityIndex(43, (SELECT concat_ws('.', neindex, shelfindex, slotindex, accportindex, flow_index, typeindex, policerindex) FROM cn_eth_flow_policer_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='AccPortQOSShaperDBImpl'                THEN resultEntityIdx := createEntityIndex(44, (SELECT concat_ws('.', neindex, shelfindex, slotindex, accportindex, shaperindex) FROM cn_shaper_cm WHERE id=t_id));
  ELSEIF t_jdoclass='PortBitsF3DBImpl'                      THEN resultEntityIdx := createEntityIndex(45, (SELECT concat_ws('.', neindex, shelfindex, slotindex, typeindex, portindex)   FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='Gps10MHzPortDBImpl'                    THEN resultEntityIdx := createEntityIndex(46, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex)   FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetTrafficPortF3DBImpl'           THEN resultEntityIdx := createEntityIndex(47, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex)   FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='WANEthernetTrafficPortF3DBImpl'        THEN resultEntityIdx := createEntityIndex(47, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex)   FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='ElineFlowF3DBImpl'                     THEN resultEntityIdx := createEntityIndex(48, (SELECT concat_ws('.', neindex, elineindex)   FROM cn_elineflow_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='FlowPointF3DBImpl'                     THEN resultEntityIdx := createEntityIndex(49, (SELECT concat_ws('.', neindex, shelfindex, slotindex, accportindex, flowpointindex) FROM cn_flowpoint_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='FlowPointOAMF3DBImpl'                  THEN resultEntityIdx := createEntityIndex(50, (SELECT concat_ws('.', neindex, shelfindex, slotindex, accportindex, flowpointindex) FROM cn_flowpoint_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='QosPolicerProfileDBImpl'               THEN resultEntityIdx := createEntityIndex(51, (SELECT concat_ws('.', index) FROM cn_policer_profile WHERE id=t_id));
  ELSEIF t_jdoclass='QosQueueProfileDBImpl'                 THEN resultEntityIdx := createEntityIndex(52, (SELECT concat_ws('.', index) FROM cn_queue_profile WHERE id=t_id));
  ELSEIF t_jdoclass='QOSPolicerV2F3DBImpl'                  THEN resultEntityIdx := createEntityIndex(53, (SELECT concat_ws('.', neindex, shelfindex, slotindex, accportindex, flow_index, policerindex) FROM cn_eth_flow_policer_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='QOSShaperV2F3DBImpl'                   THEN resultEntityIdx := createEntityIndex(54, (SELECT concat_ws('.', neindex, shelfindex, slotindex, accportindex, flowindex, shaperindex) FROM cn_shaper_cm WHERE id=t_id));
  ELSEIF t_jdoclass='QOSTrafficPortShaperF3DBImpl'          THEN resultEntityIdx := createEntityIndex(55, (SELECT concat_ws('.', neindex, shelfindex, slotindex, accportindex, shaperindex) FROM cn_shaper_cm WHERE id=t_id));
  ELSEIF t_jdoclass='NetPortQOSShaperDBImpl'                THEN resultEntityIdx := createEntityIndex(56, (SELECT concat_ws('.', neindex, shelfindex, slotindex, accportindex, shaperindex) FROM cn_shaper_cm WHERE id=t_id));
  ELSEIF t_jdoclass='OcnStmLineF3DBImpl'                    THEN resultEntityIdx := createEntityIndex(57, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex) FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='F3PulsePerSecondPortDBImpl'            THEN resultEntityIdx := createEntityIndex(58, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex) FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='F3TimeOfDayPortDBImpl'                 THEN resultEntityIdx := createEntityIndex(59, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex) FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='F3GPSReceiverPortDBImpl'               THEN resultEntityIdx := createEntityIndex(60, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex) FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='ProtectionGroupF3DBImpl'               THEN resultEntityIdx := createEntityIndex(61, (SELECT concat_ws('.', neindex, shelfindex, slotindex, protectiongroupindex) FROM cn_pg_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='ESAProbeF3MIBDBImpl'                   THEN resultEntityIdx := createEntityIndex(62, (SELECT concat_ws('.', neindex, shelfindex, slotindex, esaindex) FROM cn_esa_probe_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='ESAProbeCosConfigF3DBImpl'             THEN resultEntityIdx := createEntityIndex(64, (SELECT concat_ws('.', neindex, shelfindex, slotindex, probeindex, cosindex) FROM cn_esa_probe_mep_cos WHERE id=t_id));
  ELSEIF t_jdoclass='ESAProbeMultiMepF3DBImpl'              THEN resultEntityIdx := createEntityIndex(65, (SELECT concat_ws('.', neindex, shelfindex, slotindex, probeindex, mepindex) FROM cn_esa_probe_mep_cos WHERE id=t_id));
  ELSEIF t_jdoclass='StaticRouteDBImpl'                     THEN resultEntityIdx := createEntityIndex(66, (SELECT concat_ws('.', index) FROM cn_static_route WHERE id=t_id));
  ELSEIF t_jdoclass='ManagementTunnelDBImpl'                THEN resultEntityIdx := createEntityIndex(67, (SELECT concat_ws('.', managementtunnelindex) FROM cn_management_tunnel WHERE id=t_id));
  ELSEIF t_jdoclass='F3SyncDBImpl'                          THEN resultEntityIdx := createEntityIndex(68, (SELECT concat_ws('.', neindex, shelfindex, slotindex, syncindex) FROM cn_f3sync WHERE id=t_id));
  ELSEIF t_jdoclass='F3SyncRefDBImpl'                       THEN resultEntityIdx := createEntityIndex(69, (SELECT concat_ws('.', neindex, shelfindex, slotindex, syncindex, refindex) FROM cn_f3sync_ref WHERE id=t_id));
  ELSEIF t_jdoclass='TCDBImpl'                              THEN resultEntityIdx := createEntityIndex(70, (SELECT concat_ws('.', ne_index, tc_index) FROM cn_tc WHERE id=t_id));
  ELSEIF t_jdoclass='TCVirtualPortDBImpl'                   THEN resultEntityIdx := createEntityIndex(71, (SELECT concat_ws('.', ne_index, tc_index, tc_vp_index) FROM cn_tc_vp WHERE id=t_id));
  ELSEIF t_jdoclass='OCSlaveDBImpl'                         THEN resultEntityIdx := createEntityIndex(72, (SELECT concat_ws('.', ne_index, slave_index) FROM cn_telecom_slave WHERE id=t_id));
  ELSEIF t_jdoclass='SOOCDBImpl'                            THEN resultEntityIdx := createEntityIndex(73, (SELECT concat_ws('.', ne_index, slave_index, sooc_index) FROM cn_sooc WHERE id=t_id));
  ELSEIF t_jdoclass='OCSlaveVirtualPortDBImpl'              THEN resultEntityIdx := createEntityIndex(74, (SELECT concat_ws('.', ne_index, slave_index, sooc_index, ocs_port_index) FROM cn_slave_vp WHERE id=t_id));
  ELSEIF t_jdoclass='PTPAccFlowPointDBImpl'                 THEN resultEntityIdx := createEntityIndex(75, (SELECT concat_ws('.', ne_index, shelf_index, slot_index, port_index, ptp_flow_index) FROM cn_ptp_flowpoint WHERE id=t_id));
  ELSEIF t_jdoclass='PTPNetFlowPointDBImpl'                 THEN resultEntityIdx := createEntityIndex(76, (SELECT concat_ws('.', ne_index, shelf_index, slot_index, port_index, ptp_flow_index) FROM cn_ptp_flowpoint WHERE id=t_id));
  ELSEIF t_jdoclass='SatopDBImpl'                           THEN resultEntityIdx := createEntityIndex(77, (SELECT concat_ws('.', neindex, shelfindex, slotindex, index) FROM cn_generic_mo_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='ClockProbeDBImpl'                      THEN resultEntityIdx := createEntityIndex(78, (SELECT concat_ws('.', ne_index, probe_index) FROM cn_sj_probe WHERE id=t_id));
  ELSEIF t_jdoclass='ClockProbeHistoryDBImpl'               THEN resultEntityIdx := createEntityIndex(79, (SELECT concat_ws('.', ne_index, probe_index, history_index) FROM cn_sj_probe_history WHERE id=t_id));
  ELSEIF t_jdoclass='PTPClockProbeDBImpl'                   THEN resultEntityIdx := createEntityIndex(80, (SELECT concat_ws('.', ne_index, probe_index) FROM cn_sj_probe WHERE id=t_id));
  ELSEIF t_jdoclass='PTPClockProbeHistoryDBImpl'            THEN resultEntityIdx := createEntityIndex(81, (SELECT concat_ws('.', ne_index, probe_index, history_index) FROM cn_sj_probe_history WHERE id=t_id));
  ELSEIF t_jdoclass='PTPNetworkProbeDBImpl'                 THEN resultEntityIdx := createEntityIndex(82, (SELECT concat_ws('.', ne_index, probe_index) FROM cn_sj_probe WHERE id=t_id));
  ELSEIF t_jdoclass='SJScheduleGroupDBImpl'                 THEN resultEntityIdx := createEntityIndex(83, (SELECT concat_ws('.', neindex, shelfindex, slotindex, index) FROM cn_sj_scheduler WHERE id=t_id));
  ELSEIF t_jdoclass='AMPConfigDBImpl'                       THEN resultEntityIdx := createEntityIndex(84, (SELECT concat_ws('.', ampconfigindex) FROM cn_amp_config WHERE id=t_id));
  ELSEIF t_jdoclass='ErpGroupF3DBImpl'                      THEN resultEntityIdx := createEntityIndex(85, (SELECT concat_ws('.', neindex, erp_index) FROM cn_erp_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='AccNetSfpModuleF3DBImpl'               THEN resultEntityIdx := createEntityIndex(86, (SELECT concat_ws('.', neindex, shelfindex, slotindex, sfpindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='AccNetSfpModuleFSPGE11XDBImpl'         THEN resultEntityIdx := createEntityIndex(86, (SELECT concat_ws('.', neindex, shelfindex, slotindex, sfpindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='AccNetSfpModuleFSPSyncProbDBImpl'      THEN resultEntityIdx := createEntityIndex(86, (SELECT concat_ws('.', neindex, shelfindex, slotindex, sfpindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='AccNetSfpModuleFSPXG210DBImpl'         THEN resultEntityIdx := createEntityIndex(86, (SELECT concat_ws('.', neindex, shelfindex, slotindex, sfpindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetTrafficSfpF3DBImpl'            THEN resultEntityIdx := createEntityIndex(86, (SELECT concat_ws('.', neindex, shelfindex, slotindex, sfpindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='OcnStmLineSfpF3DBImpl'                 THEN resultEntityIdx := createEntityIndex(86, (SELECT concat_ws('.', neindex, shelfindex, slotindex, sfpindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='ESAProbeMepCosF3DBImpl'                THEN resultEntityIdx := createEntityIndex(90, (SELECT concat_ws('.', neindex, shelfindex, slotindex, probeindex, mepindex, cosindex)   FROM cn_esa_probe_mep_cos WHERE id=t_id));
  ELSEIF t_jdoclass='ProtectionGroupPortF3DBImpl'           THEN resultEntityIdx := createEntityIndex(91, (SELECT concat_ws('.', neindex, shelfindex, slotindex, groupindex, unitindex)   FROM cn_pgport_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='ClockMTIEResultDBImpl'                 THEN resultEntityIdx := createEntityIndex(92, (SELECT concat_ws('.', ne_index, probe_index, history_index, mtie_value_index)   FROM cn_sj_probe_mtie_result WHERE id=t_id));
  ELSEIF t_jdoclass='PTPClockMTIEResultDBImpl'              THEN resultEntityIdx := createEntityIndex(93, (SELECT concat_ws('.', ne_index, probe_index, history_index, mtie_value_index)   FROM cn_sj_probe_mtie_result WHERE id=t_id));
  ELSEIF t_jdoclass='SlotF3DBImpl'                          THEN resultEntityIdx := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_slot WHERE id=t_id));
  ELSEIF t_jdoclass='MaintenanceDomainDBImpl'               THEN resultEntityIdx := createEntityIndex(95, (SELECT concat_ws('.', mdindex)   FROM cn_md WHERE id=t_id));
  ELSEIF t_jdoclass='MaintenanceAssociationDBImpl'          THEN resultEntityIdx := createEntityIndex(96, (SELECT concat_ws('.', mdindex, maindex)   FROM cn_ma WHERE id=t_id));
  ELSEIF t_jdoclass='MaintenanceEndPointDBImpl'             THEN resultEntityIdx := createEntityIndex(97, (SELECT concat_ws('.', mdindex, maindex, identifier)   FROM cn_mep WHERE id=t_id));
  ELSEIF t_jdoclass='MspProtectionGroupF3DBImpl'            THEN resultEntityIdx := createEntityIndex(98, (SELECT concat_ws('.', neindex, protectiongroupindex) FROM cn_msppg_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='MspProtectionGroupPortF3DBImpl'        THEN resultEntityIdx := createEntityIndex(99, (SELECT concat_ws('.', neindex, groupindex, unitindex)   FROM cn_msppgport_f3 WHERE id=t_id));
  ELSEIF t_jdoclass='AccIngressPrioMapDBImpl'               THEN resultEntityIdx := createEntityIndex(100, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex, priomapindex) FROM cn_prio_map WHERE id=t_id));
  ELSEIF t_jdoclass='NetEgressPrioMapDBImpl'                THEN resultEntityIdx := createEntityIndex(101, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex, priomapindex) FROM cn_prio_map WHERE id=t_id));


-- last because of heavy if
--   ELSEIF t_jdoclass='NetEgressPrioMapDBImpl'                THEN resultEntityIdx := createEntityIndex(101, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex, priomapindex) FROM cn_prio_map WHERE id=t_id));

--     AccIngressPrioMapDBImpl              prop category
--     FDFrACCEndFSP150CMDBImpl             fake
--     FDFrFSP150CMDBImpl                   fake
--     FDFrFTPEndFSP150CMDBImpl             fake
--     FDFrNETEndFSP150CMDBImpl             fake
--     FTPFSP150CMDBImpl                    fake
--     MaintenanceAssociationDBImpl         prop category
--     MaintenanceDomainDBImpl              prop category
--     MaintenanceEndPointDBImpl            prop category
--     MspProtectionGroupF3DBImpl           prop category
--     MspProtectionGroupPortF3DBImpl       prop category
--     NetEgressPrioMapDBImpl               prop category
--     PTPClockMTIEResultDBImpl             prop category
--     ClockMTIEResultDBImpl                prop category
--     SlotF3DBImpl                         prop category

  ELSE
    resultEntityIdx := null;
  END IF;

  RETURN resultEntityIdx;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION getAllAffectedMORows()
  RETURNS SETOF cn_managed_object
AS $$
BEGIN
  RETURN QUERY
  SELECT m.* FROM cn_managed_object m JOIN cn_network_element n ON m.ne_id=n.id
  WHERE n.type0 in ('206', '2060', '2061', '201', '2010', '110', '4660', '112', '114', '1140', '1141', '1142', '1143', '115', '210', '153', '154');
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION generateAndStoreEntityIndexInTmpTable()
  RETURNS VOID
AS $$
DECLARE
  resultEntityIdx entity_idx_type;
BEGIN
  INSERT INTO tmp_entity_idx_data SELECT mo.id, entityIdxToString(getCategoryAndIndexes(id, jdoclass)), 0 FROM getAllAffectedMORows() mo;
RETURN ;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION getNewEntityIndexForContainedIn(mo_id integer)
  RETURNS entity_idx_type
AS $$
DECLARE
  resultEntityIdx entity_idx_type;
  neId integer;
  oldContainedIn varchar(120);
  containedInId integer;
  containedInJdoClass varchar(255);
BEGIN
  SELECT ne_id, containedin FROM cn_entity WHERE id=mo_id into neId, oldContainedIn;
  SELECT m.id, m.jdoclass FROM cn_entity e JOIN cn_managed_object m on e.id=m.id WHERE e.ne_id=neId AND e.entityindex=oldContainedIn INTO containedInId, containedInJdoClass;
  resultEntityIdx := getCategoryAndIndexes(containedInId, containedInJdoClass);
  RETURN resultEntityIdx;
EXCEPTION WHEN others THEN
  raise notice '% %', SQLERRM, SQLSTATE;
  RETURN null;
END;
$$ LANGUAGE plpgsql;


CREATE OR REPLACE FUNCTION generateAndStoreContainedInInTmpTable()
  RETURNS VOID
AS $$
DECLARE
  resultEntityIdx entity_idx_type;
BEGIN
  UPDATE tmp_entity_idx_data
    SET containedIn = entityIdxToString(getNewEntityIndexForContainedIn(mo_id));
RETURN ;
END;
$$ LANGUAGE plpgsql;

-- main

SELECT generateAndStoreEntityIndexInTmpTable();
SELECT generateAndStoreContainedInInTmpTable();
UPDATE tmp_entity_idx_data SET containedIn = '0' where containedIn is null;

update ev_event as e
set moduleindex = tmp.entityIdx
FROM tmp_entity_idx_data tmp
  inner join cn_managed_object m
    on m.id = tmp.mo_id
where e.moduleindex = m.entityindex and e.sourceneid = m.ne_id;

update ev_event as e
set objectindex = tmp.entityIdx
FROM tmp_entity_idx_data tmp
  inner join cn_managed_object m
    on m.id = tmp.mo_id
where e.objectindex = m.entityindex and e.sourceneid = m.ne_id;

update ev_event as e
set portindex = tmp.entityIdx
FROM tmp_entity_idx_data tmp
  inner join cn_managed_object m
    on m.id = tmp.mo_id
where e.portindex = m.entityindex and e.sourceneid = m.ne_id;

UPDATE cn_entity e
SET entityindex=tmp.entityIdx, containedin=tmp.containedIn
FROM tmp_entity_idx_data tmp
WHERE tmp.mo_id=e.id and tmp.entityIdx is not null;

UPDATE cn_managed_object m
SET entityindex=tmp.entityIdx
FROM tmp_entity_idx_data tmp
WHERE tmp.mo_id=m.id and tmp.entityIdx is not null;


DROP FUNCTION IF EXISTS getCategoryAndIndexes(integer, varchar(255));
DROP FUNCTION IF EXISTS createEntityIndex(integer , VARCHAR(255));
DROP FUNCTION IF EXISTS generateAndStoreEntityIndexInTmpTable();
DROP FUNCTION IF EXISTS generateAndStoreContainedInInTmpTable();
DROP FUNCTION IF EXISTS getNewEntityIndexForContainedIn(integer);
DROP FUNCTION IF EXISTS entityIdxToString(entity_idx_type);
DROP FUNCTION IF EXISTS getAllAffectedMORows();

DROP TABLE IF EXISTS tmp_entity_idx_data;
DROP TYPE IF EXISTS entity_idx_type;






















/*
public static final int TYPE_GE206          = 206;
public static final int TYPE_GE206F         = 2060;
public static final int TYPE_GE206V         = 2061;
public static final int TYPE_GE201          = 201;
public static final int TYPE_GE201SE        = 2010;
public static final int TYPE_JUNIPER_MX     = 108;
public static final int TYPE_SYMMETRICOM    = 110;
public static final int TYPE_150EGX         = 4660;
public static final int TYPE_GE112          = 112;
public static final int TYPE_GE114          = 114;
public static final int TYPE_GE114S         = 1140;
public static final int TYPE_GE114SH        = 1141;
public static final int TYPE_GE114PH        = 1143;
public static final int TYPE_GE114H         = 1142;
public static final int TYPE_SYNC_PROB      = 115;
public static final int TYPE_XG210          = 210;
public static final int TYPE_FSP_150CM 	    = 153;
public static final int TYPE_FSP_150CM_CPMR = 154;
*/