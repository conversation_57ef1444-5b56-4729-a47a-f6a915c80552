CREATE OR REPLACE FUNCTION updatePathElements() RETURNS void
    LANGUAGE plpgsql
    AS $$
DECLARE
	pel RECORD;
	next_sequence_id INT DEFAULT 0;
BEGIN

  ALTER TABLE ONLY cn_path_elements DROP CONSTRAINT cn_path_elements_pkey;
  DROP INDEX ix_cn_path_elements_fk_cn_path_elements_path_id;

  CREATE SEQUENCE temporary_path_element_pkeys START 1;

  CREATE TEMP TABLE unique_path_elements AS
  (SELECT path_id,
          follow,
          ip,
          ipmonitor,
          lid,
          lifip,
          line,
          linestring,
          pathid,
          sequencenumber,
          tid,
          tidmonitor,
          tie,
          tet,
          channelup,
          channeldown,
          nextval('temporary_path_element_pkeys') as id
   FROM cn_path_elements
   GROUP BY follow,
            ip,
            ipmonitor,
            lid,
            lifip,
            line,
            linestring,
            pathid,
            sequencenumber,
            tid,
            tidmonitor,
            tie,
            tet,
            channelup,
            channeldown,
            path_id);


  DELETE FROM cn_path_elements;


  INSERT INTO cn_path_elements  (SELECT path_id, follow, ip, ipmonitor, lid, lifip, line, linestring, pathid, sequencenumber, tid, tidmonitor, tie, tet, 0, 2, id FROM unique_path_elements ORDER BY path_id, sequencenumber ASC);


	SELECT SEQUENCE_VALUE FROM JDO_SEQUENCE WHERE ID = 'DEFAULT' INTO next_sequence_id;

	FOR pel IN SELECT * FROM cn_path_elements LOOP
	  UPDATE cn_path_elements set id = next_sequence_id WHERE ctid=pel.ctid;
	  next_sequence_id = next_sequence_id + 1;
	END LOOP;

  DROP SEQUENCE temporary_path_element_pkeys;

  ALTER TABLE ONLY cn_path_elements ADD CONSTRAINT cn_path_elements_pkey PRIMARY KEY (id);
  CREATE INDEX ix_cn_path_elements_fk_cn_path_elements_path_id ON cn_path_elements USING btree (path_id);

END;
$$;

SELECT updatePathElements();

DROP FUNCTION IF EXISTS updatePathElements();