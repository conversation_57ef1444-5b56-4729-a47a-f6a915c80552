--
-- Copyright 2023 Adtran Networks SE. All rights reserved.
--
-- Owner: ask<PERSON><PERSON><PERSON>
--
CREATE OR REPLACE FUNCTION update_pm_bookmarks() RETURNS TEXT AS $$

DECLARE

out TEXT := '';
next_sequence_id INT DEFAULT 0;
row_count INT DEFAULT 1;
row_record pm_csv_bookmark % ROWTYPE;

BEGIN
	SELECT count(*) FROM pm_csv_bookmark INTO row_count;

	ALTER TABLE pm_csv_bookmark ADD COLUMN last_record_id BIGINT, ADD COLUMN last_record_ts BIGINT, ADD COLUMN time_type INT;

	IF row_count = 0 THEN
		out := out || 'There are no bookmarks in pm_csv_bookmark table.' || E'\n';
		ALTER TABLE pm_csv_bookmark ALTER COLUMN time_type SET NOT NULL;
		ALTER TABLE ONLY pm_csv_bookmark ADD CONSTRAINT pm_csv_bookmark_time_type_key UNIQUE (time_type);
		RETURN out;
	END IF;

	IF row_count > 1 THEN
		out := out || 'There is more than one record in pm_csv_bookmark table. This is inconsistent. Table content will be deleted.' || E'\n';
		DELETE FROM pm_csv_bookmark;
		ALTER TABLE pm_csv_bookmark ALTER COLUMN time_type SET NOT NULL;
		ALTER TABLE ONLY pm_csv_bookmark ADD CONSTRAINT pm_csv_bookmark_time_type_key UNIQUE (time_type);
		RETURN out;
	END IF ;

	out := out || 'There is exactly one bookmark in pm_csv_bookmark table. The database is consistent.' || E'\n';
	out := out || 'Copying data to new columns...' || E'\n';

	SELECT INTO row_record * FROM pm_csv_bookmark;

	SELECT SEQUENCE_VALUE FROM JDO_SEQUENCE WHERE ID = 'DEFAULT' INTO next_sequence_id;

	INSERT INTO pm_csv_bookmark (id_bookmark, last_record_id, last_record_ts, time_type, jdoversion)
	VALUES (next_sequence_id, row_record.last15m_record_id, row_record.last15m_record_ts, 1, 1);

	next_sequence_id = next_sequence_id + 1;

	INSERT INTO pm_csv_bookmark (id_bookmark, last_record_id, last_record_ts, time_type, jdoversion)
	VALUES (next_sequence_id, row_record.last24h_record_id, row_record.last24h_record_ts, 2, 1);

	next_sequence_id = next_sequence_id + 1;

	UPDATE JDO_SEQUENCE SET SEQUENCE_VALUE = NEXT_SEQUENCE_ID WHERE ID = 'DEFAULT';

	DELETE FROM pm_csv_bookmark WHERE id_bookmark = row_record.id_bookmark;
	
	out := out || 'Applying constraints...' || E'\n';
	
	ALTER TABLE pm_csv_bookmark ALTER COLUMN time_type SET NOT NULL;
	ALTER TABLE ONLY pm_csv_bookmark ADD CONSTRAINT pm_csv_bookmark_time_type_key UNIQUE (time_type);
	
	out := out || 'DONE, Will now exit.';

	RETURN out;

END;$$

LANGUAGE plpgsql;

SELECT update_pm_bookmarks();

DROP FUNCTION IF EXISTS update_pm_bookmarks();