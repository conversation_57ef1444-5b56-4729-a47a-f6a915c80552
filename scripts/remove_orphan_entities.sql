--
-- Copyright 2023 Adtran Networks SE. All rights reserved.
--
-- Owner: tomaszm
--

CREATE TABLE tmp_orphan_entities(
  id integer, ne_id integer, shortdescription varchar(255), jdoclass varchar(255)
);

CREATE OR REPLACE FUNCTION remove_entity()
  RETURNS VOID
AS $$
DECLARE
  entity_jdoclass varchar(255);
BEGIN

  FOR entity_jdoclass IN
  SELECT DISTINCT jdoclass
  FROM tmp_orphan_entities

  LOOP
    IF entity_jdoclass = 'ProtectedFlowF3DBImpl' 
      THEN 
        delete from cn_erp_protflow_f3 where id in (SELECT id from tmp_orphan_entities t where t.jdoclass = entity_jdoclass);
        delete from cn_managed_object where id in (SELECT id from tmp_orphan_entities where jdoclass = entity_jdoclass);
    ELSEIF entity_jdoclass = 'QOSFlowPolicerDBImpl' 
      THEN 
        delete from cn_eth_flow_policer_f3 where id in (SELECT id from tmp_orphan_entities t where t.jdoclass = entity_jdoclass);
        delete from cn_managed_object where id in (SELECT id from tmp_orphan_entities where jdoclass = entity_jdoclass);
    ELSEIF entity_jdoclass = 'QOSShaperF3DBImpl' OR entity_jdoclass = 'QOSShaperV2F3DBImpl' OR entity_jdoclass = 'QOSTrafficPortShaperF3DBImpl'
      THEN
        delete from cn_shaper_cm where id in (SELECT id from tmp_orphan_entities t where t.jdoclass = entity_jdoclass);
        delete from cn_managed_object where id in (SELECT id from tmp_orphan_entities where jdoclass = entity_jdoclass);
    ELSEIF entity_jdoclass = 'MDDBImpl' 
      THEN 
        delete from cn_md where id in (SELECT id from tmp_orphan_entities t where t.jdoclass = entity_jdoclass);
        delete from cn_managed_object where id in (SELECT id from tmp_orphan_entities where jdoclass = entity_jdoclass);
    ELSEIF entity_jdoclass = 'MANetDBImpl' 
      THEN 
        delete from cn_ma_net where id in (SELECT id from tmp_orphan_entities t where t.jdoclass = entity_jdoclass);
        delete from cn_managed_object where id in (SELECT id from tmp_orphan_entities where jdoclass = entity_jdoclass);
    ELSEIF entity_jdoclass = 'MACompDBImpl' 
      THEN 
        delete from cn_ma_comp where id in (SELECT id from tmp_orphan_entities t where t.jdoclass = entity_jdoclass);
        delete from cn_managed_object where id in (SELECT id from tmp_orphan_entities where jdoclass = entity_jdoclass);
    ELSEIF entity_jdoclass = 'TwampSessionSenderDBImpl'
      THEN
        delete from cn_twamp_session_sender where id in (SELECT id from tmp_orphan_entities t where t.jdoclass = entity_jdoclass);
        delete from cn_managed_object where id in (SELECT id from tmp_orphan_entities where jdoclass = entity_jdoclass);
    END IF;
  END LOOP;

END;
$$ LANGUAGE plpgsql;

-- detect entities needed to be removed
INSERT INTO tmp_orphan_entities
  select m.id, NULL , m.shortdescription, m.jdoclass
  from cn_managed_object m WHERE m.ne_id not in (select id from cn_network_element);

-- remove not wanted entities
SELECT remove_entity();

-- mark nes for inventory
UPDATE cn_network_element set coldstarttime=0 where id in (SELECT DISTINCT ne_id from tmp_orphan_entities where ne_id is not null);

-- display results
SELECT id, shortdescription as entity_to_be_removed, ne_id as ne_to_inventory, jdoclass from tmp_orphan_entities;

DROP TABLE IF EXISTS tmp_orphan_entities;

