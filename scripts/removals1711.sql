DROP TABLE IF EXISTS pd_otn_link;
ALTER TABLE public.pd_service DROP COLUMN IF EXISTS otn_link_id;
ALTER TABLE public.pd_connection_resource DROP COLUMN IF EXISTS channels;
DROP TABLE IF EXISTS ml_cfm_entry;
DROP TABLE IF EXISTS ml_cfmentry_pvids;
DROP TABLE IF EXISTS ml_cfmentry_rmeps;
DROP TABLE IF EXISTS ml_service_intent;
DROP TABLE IF EXISTS ml_layer_ext_ml_conn_point;
ALTER TABLE public.ml_intent_conn_point_entry DROP COLUMN IF EXISTS intent_id;
DROP TABLE IF EXISTS ml_intent_conn_point_entry;
DROP TABLE IF EXISTS  ml_l2cp_entry;
DROP TABLE IF EXISTS ml_cos_entry;
DROP TABLE IF EXISTS ml_cos_pcp_values;
DROP TABLE IF EXISTS ml_tag_action_entry;
ALTER TABLE  public.ml_layer_extension DROP COLUMN IF EXISTS ctag_control,
                                       DROP COLUMN IF EXISTS ctag_vlan_id,
                                       DROP COLUMN IF EXISTS ctag_vlan_priority,
                                       DROP COLUMN IF EXISTS default_cos,
                                       DROP COLUMN IF EXISTS default_member_enabled,
                                       DROP COLUMN IF EXISTS interface_name,
                                       DROP COLUMN IF EXISTS mtu,
                                       DROP COLUMN IF EXISTS port_service_type,
                                       DROP COLUMN IF EXISTS stag_control,
                                       DROP COLUMN IF EXISTS stag_vlan_id,
                                       DROP COLUMN IF EXISTS stag_vlan_priority,
                                       DROP COLUMN IF EXISTS untagged_frames_enabled,
                                       DROP COLUMN IF EXISTS vlan_member_list;
ALTER TABLE public.ml_connection_resource DROP COLUMN IF EXISTS child_vlan_resource,
                                          DROP COLUMN IF EXISTS vlans;

DROP TABLE IF EXISTS alert CASCADE;
DROP TABLE IF EXISTS event CASCADE;
DROP TABLE IF EXISTS network CASCADE;
DROP TABLE IF EXISTS node CASCADE;
DROP TABLE IF EXISTS snmpnode CASCADE;
DROP TABLE IF EXISTS topoobject CASCADE;
DROP TABLE IF EXISTS fdnmupgradetftpstatus CASCADE;
DROP TABLE IF EXISTS fdlineupgradetftpstatus CASCADE;
DROP TABLE IF EXISTS fdconfigtransferstatus CASCADE;
DROP TABLE IF EXISTS fdfecpmthresholds CASCADE;
DROP TABLE IF EXISTS fdotnpmthresholds CASCADE;
DROP TABLE IF EXISTS lscdeviceinfo CASCADE;
DROP TABLE IF EXISTS lscsettings CASCADE;
DROP TABLE IF EXISTS healthcategory CASCADE;
DROP TABLE IF EXISTS healthdata CASCADE;
DROP TABLE IF EXISTS healthindicator CASCADE;
DROP TABLE IF EXISTS customerreport CASCADE;
DROP TABLE IF EXISTS fdosahistoricalchannel CASCADE;
DROP TABLE IF EXISTS fdosahistoricalchannelv2 CASCADE;
DROP TABLE IF EXISTS opmvlaninterface CASCADE;
DROP TABLE IF EXISTS opvlaninterface CASCADE;
DROP TABLE IF EXISTS opvlanintfmember CASCADE;
DROP TABLE IF EXISTS optrunk CASCADE;
DROP TABLE IF EXISTS opconfigsftpstatus CASCADE;
DROP TABLE IF EXISTS opticalservicestatus CASCADE;
DROP TABLE IF EXISTS opupgradesftpstatus CASCADE;
DROP TABLE IF EXISTS osconfigsftpstatus CASCADE;
DROP TABLE IF EXISTS osupgradesftpstatus CASCADE;
DROP TABLE IF EXISTS rsvpproduct CASCADE;
DROP TABLE IF EXISTS rtmstaticroute CASCADE;
DROP TABLE IF EXISTS apndeviceconfiguration CASCADE;
DROP TABLE IF EXISTS apnprofile CASCADE;
DROP TABLE IF EXISTS devicealarm CASCADE;
DROP TABLE IF EXISTS deviceevent CASCADE;
DROP TABLE IF EXISTS deviceeventfilter CASCADE;
DROP TABLE IF EXISTS deviceobject CASCADE;
DROP TABLE IF EXISTS erpconfig CASCADE;
DROP TABLE IF EXISTS fan CASCADE;
DROP TABLE IF EXISTS fanassembly CASCADE;
DROP TABLE IF EXISTS fdchassis CASCADE;
DROP TABLE IF EXISTS fdcrosspoint CASCADE;
DROP TABLE IF EXISTS fdmodule CASCADE;
DROP TABLE IF EXISTS fdport CASCADE;
DROP TABLE IF EXISTS fdport2 CASCADE;
DROP TABLE IF EXISTS fdtrapconfig CASCADE;
DROP TABLE IF EXISTS fspchassis CASCADE;
DROP TABLE IF EXISTS fspmodule CASCADE;
DROP TABLE IF EXISTS fspport CASCADE;
DROP TABLE IF EXISTS fsptrunkport CASCADE;
DROP TABLE IF EXISTS iproute CASCADE;
DROP TABLE IF EXISTS ldchassis CASCADE;
DROP TABLE IF EXISTS ldmodule CASCADE;
DROP TABLE IF EXISTS ldport CASCADE;
DROP TABLE IF EXISTS ldport2 CASCADE;
DROP TABLE IF EXISTS linkhistutilization CASCADE;
DROP TABLE IF EXISTS mclag CASCADE;
DROP TABLE IF EXISTS mclagrg CASCADE;
DROP TABLE IF EXISTS mdmodule CASCADE;
DROP TABLE IF EXISTS mdport CASCADE;
DROP TABLE IF EXISTS mdport2 CASCADE;
DROP TABLE IF EXISTS module CASCADE;
DROP TABLE IF EXISTS netconfconfig CASCADE;
DROP TABLE IF EXISTS nni CASCADE;
DROP TABLE IF EXISTS opchassis CASCADE;
DROP TABLE IF EXISTS opmodule CASCADE;
DROP TABLE IF EXISTS opport CASCADE;
DROP TABLE IF EXISTS opport2 CASCADE;
DROP TABLE IF EXISTS optiport CASCADE;
DROP TABLE IF EXISTS optiport2 CASCADE;
DROP TABLE IF EXISTS optiportprotect CASCADE;
DROP TABLE IF EXISTS optiportprotectmep CASCADE;
DROP TABLE IF EXISTS optiportreflect CASCADE;
DROP TABLE IF EXISTS optiportshaper CASCADE;
DROP TABLE IF EXISTS optiswitch CASCADE;
DROP TABLE IF EXISTS osbfd CASCADE;
DROP TABLE IF EXISTS osdummyif CASCADE;
DROP TABLE IF EXISTS osiptunnel CASCADE;
DROP TABLE IF EXISTS osldprouter CASCADE;
DROP TABLE IF EXISTS osldptargetpeer CASCADE;
DROP TABLE IF EXISTS osmodule CASCADE;
DROP TABLE IF EXISTS ossynceclocksource CASCADE;
DROP TABLE IF EXISTS ostrunkport CASCADE;
DROP TABLE IF EXISTS osvif CASCADE;
DROP TABLE IF EXISTS physicallink CASCADE;
DROP TABLE IF EXISTS port CASCADE;
DROP TABLE IF EXISTS port2 CASCADE;
DROP TABLE IF EXISTS portsfp CASCADE;
DROP TABLE IF EXISTS powersupply CASCADE;
DROP TABLE IF EXISTS pvmaplink CASCADE;
DROP TABLE IF EXISTS snmpdevice CASCADE;
DROP TABLE IF EXISTS snmpdeviceport CASCADE;
DROP TABLE IF EXISTS snmpdeviceport2 CASCADE;
DROP TABLE IF EXISTS trunk CASCADE;
DROP TABLE IF EXISTS uni CASCADE;
DROP TABLE IF EXISTS bandwidthprofile CASCADE;
DROP TABLE IF EXISTS cfmprofile CASCADE;
DROP TABLE IF EXISTS customer CASCADE;
DROP TABLE IF EXISTS dot1agmaconfig CASCADE;
DROP TABLE IF EXISTS eserviceendpointstatus CASCADE;
DROP TABLE IF EXISTS eservicemapping CASCADE;
DROP TABLE IF EXISTS ethernetservice CASCADE;
DROP TABLE IF EXISTS ethernetserviceendpoint CASCADE;
DROP TABLE IF EXISTS ethernetserviceendpoint2 CASCADE;
DROP TABLE IF EXISTS ethernetservicestatus CASCADE;
DROP TABLE IF EXISTS flowmap CASCADE;
DROP TABLE IF EXISTS flowprofile CASCADE;
DROP TABLE IF EXISTS flowprofile2 CASCADE;
DROP TABLE IF EXISTS l2cptemplate CASCADE;
DROP TABLE IF EXISTS maintenancedomain CASCADE;
DROP TABLE IF EXISTS mgmtvif CASCADE;
DROP TABLE IF EXISTS ophqoscosprofile CASCADE;
DROP TABLE IF EXISTS ophqosmapprofile CASCADE;
DROP TABLE IF EXISTS ophqosprioutmapprofile CASCADE;
DROP TABLE IF EXISTS ophqosunifiedmapprofile CASCADE;
DROP TABLE IF EXISTS opl2cpprofile CASCADE;
DROP TABLE IF EXISTS optagaction CASCADE;
DROP TABLE IF EXISTS optagactionprofile CASCADE;
DROP TABLE IF EXISTS performanceprofile CASCADE;
DROP TABLE IF EXISTS rfc2544profile CASCADE;
DROP TABLE IF EXISTS services CASCADE;
DROP TABLE IF EXISTS soambinprofile CASCADE;
DROP TABLE IF EXISTS soampmdmprofile CASCADE;
DROP TABLE IF EXISTS soampmdmtholdprofile CASCADE;
DROP TABLE IF EXISTS soampmgenprofile CASCADE;
DROP TABLE IF EXISTS soampmgroupprofile CASCADE;
DROP TABLE IF EXISTS soampmlmprofile CASCADE;
DROP TABLE IF EXISTS soampmlmtholdprofile CASCADE;
DROP TABLE IF EXISTS soampmtestconfig CASCADE;
DROP TABLE IF EXISTS tagaction CASCADE;
DROP TABLE IF EXISTS tagexception CASCADE;
DROP TABLE IF EXISTS y1564profile CASCADE;
DROP TABLE IF EXISTS fdroadmchannel CASCADE;
DROP TABLE IF EXISTS fdroadmcommittedchannel CASCADE;
DROP TABLE IF EXISTS impairmentstemplate CASCADE;
DROP TABLE IF EXISTS inputsignalcharact CASCADE;
DROP TABLE IF EXISTS opticalinterface CASCADE;
DROP TABLE IF EXISTS opticalmonitoringservice CASCADE;
DROP TABLE IF EXISTS otpmfechistorydata CASCADE;
DROP TABLE IF EXISTS otpmotnhistorydata CASCADE;
DROP TABLE IF EXISTS otpmthresholdstemplate CASCADE;
DROP TABLE IF EXISTS otservice CASCADE;
DROP TABLE IF EXISTS roadmservicecfg CASCADE;
DROP TABLE IF EXISTS roadmservicepath CASCADE;
DROP TABLE IF EXISTS actionremark CASCADE;
DROP TABLE IF EXISTS actionsmtp CASCADE;
DROP TABLE IF EXISTS actionsnmp CASCADE;
DROP TABLE IF EXISTS actionsuppress CASCADE;
DROP TABLE IF EXISTS actionsystemcmd CASCADE;
DROP TABLE IF EXISTS archive CASCADE;
DROP TABLE IF EXISTS authaudit2 CASCADE;
DROP TABLE IF EXISTS customfielddata CASCADE;
DROP TABLE IF EXISTS customfieldprofile CASCADE;
DROP TABLE IF EXISTS customtrap CASCADE;
DROP TABLE IF EXISTS deviceeventaction CASCADE;
DROP TABLE IF EXISTS devicetag CASCADE;
DROP TABLE IF EXISTS filetransferprofile CASCADE;
DROP TABLE IF EXISTS healthmonitoring CASCADE;
DROP TABLE IF EXISTS geomapsite CASCADE;
DROP TABLE IF EXISTS pvnet CASCADE;
DROP TABLE IF EXISTS pvrole CASCADE;
DROP TABLE IF EXISTS pvsyslogserver CASCADE;
DROP TABLE IF EXISTS pvtrapreceiver CASCADE;
DROP TABLE IF EXISTS regionalfilter CASCADE;
DROP TABLE IF EXISTS regionalmap CASCADE;
DROP TABLE IF EXISTS scheduledoperation2 CASCADE;
DROP TABLE IF EXISTS scheduleoperation CASCADE;
DROP TABLE IF EXISTS serverconfig CASCADE;
DROP TABLE IF EXISTS smtpserver CASCADE;
DROP TABLE IF EXISTS snmptrapforwarder CASCADE;
DROP TABLE IF EXISTS upgrade CASCADE;
DROP TABLE IF EXISTS upgrademodule CASCADE;
DROP TABLE IF EXISTS upgradeserver CASCADE;
DROP TABLE IF EXISTS usertable CASCADE;
DROP TABLE IF EXISTS wpuser CASCADE;
DROP TABLE IF EXISTS ztstartupconfig CASCADE;
DROP TABLE IF EXISTS ztupgradeimage CASCADE;
DROP TABLE IF EXISTS backuptaskdata CASCADE;
DROP TABLE IF EXISTS restoretaskdata CASCADE;
DROP TABLE IF EXISTS taskdata CASCADE;
DROP TABLE IF EXISTS taskhistory CASCADE;
DROP TABLE IF EXISTS upgrademoduletaskdata CASCADE;
DROP TABLE IF EXISTS upgradetaskdata CASCADE;
DROP TABLE IF EXISTS configaudit CASCADE;
DROP TABLE IF EXISTS configbackupprofile CASCADE;
DROP TABLE IF EXISTS exportdata CASCADE;
DROP TABLE IF EXISTS featurelicense CASCADE;
DROP TABLE IF EXISTS ldportstatsdata CASCADE;
DROP TABLE IF EXISTS ldtmgm2statsdata CASCADE;
DROP TABLE IF EXISTS ldtmgm8daccessstatsdata CASCADE;
DROP TABLE IF EXISTS ldtmgm8dtrunkstatsdata CASCADE;
DROP TABLE IF EXISTS ldtmgm8xfpaccessportstatsdata CASCADE;
DROP TABLE IF EXISTS ldtmgm8xfptrunkstatsdata CASCADE;
DROP TABLE IF EXISTS mib2portstatsdata CASCADE;
DROP TABLE IF EXISTS osportstatsdata CASCADE;
DROP TABLE IF EXISTS tablepreference CASCADE;
DROP TABLE IF EXISTS managedobject CASCADE;
DROP TABLE IF EXISTS grouptable CASCADE;
DROP TABLE IF EXISTS qrtz_blob_triggers CASCADE;
DROP TABLE IF EXISTS qrtz_calendars CASCADE;
DROP TABLE IF EXISTS qrtz_cron_triggers CASCADE;
DROP TABLE IF EXISTS qrtz_fired_triggers CASCADE;
DROP TABLE IF EXISTS qrtz_job_details CASCADE;
DROP TABLE IF EXISTS qrtz_locks CASCADE;
DROP TABLE IF EXISTS qrtz_paused_trigger_grps CASCADE;
DROP TABLE IF EXISTS qrtz_scheduler_state CASCADE;
DROP TABLE IF EXISTS qrtz_simple_triggers CASCADE;
DROP TABLE IF EXISTS qrtz_simprop_triggers CASCADE;
DROP TABLE IF EXISTS qrtz_triggers CASCADE;
DROP TABLE IF EXISTS pd_vlan_values CASCADE;
DROP TABLE IF EXISTS pd_tag_action_entry CASCADE;
DROP TABLE IF EXISTS pd_ser_view_lines CASCADE;
DROP TABLE IF EXISTS pd_adaptation CASCADE;
DROP TABLE IF EXISTS pd_adaptation_client CASCADE;
DROP TABLE IF EXISTS pd_adaptation_server CASCADE;
DROP TABLE IF EXISTS pd_res_ts_pair CASCADE;
DROP TABLE IF EXISTS pd_res_tp_pair CASCADE;
DROP TABLE IF EXISTS pd_cp_mon_layers CASCADE;
DROP TABLE IF EXISTS pd_cos_pcp_values CASCADE;
DROP TABLE IF EXISTS pd_monitoring_section CASCADE;
DROP TABLE IF EXISTS pd_monitoring_section_entities CASCADE;
DROP TABLE IF EXISTS pd_cos_entry CASCADE;
DROP TABLE IF EXISTS pd_l2cp_entry CASCADE;
DROP TABLE IF EXISTS pd_segment_adaptation CASCADE;
DROP TABLE IF EXISTS pd_segment_client CASCADE;
DROP TABLE IF EXISTS pd_segment_entities CASCADE;
DROP TABLE IF EXISTS pd_segment_path CASCADE;
DROP TABLE IF EXISTS pd_connection_reservation CASCADE;

ALTER TABLE cn_croma_uris_ec DROP COLUMN IF EXISTS index;
