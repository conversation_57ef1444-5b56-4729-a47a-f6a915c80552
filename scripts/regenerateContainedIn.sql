--
-- Copyright 2023 Adtran Networks SE. All rights reserved.
--
-- Owner: tomaszm
--
CREATE TYPE entity_idx_type AS (prefix int, idx varchar(255));
CREATE TABLE tmp_entity_idx_data(mo_id int, ne_id int, containedIn varchar(255), jdoclass varchar(255));

CREATE OR REPLACE FUNCTION createEntityIndex(category integer , indexes VARCHAR(255))
  RETURNS entity_idx_type
AS $$
DECLARE
  result entity_idx_type;
BEGIN
  SELECT category, indexes INTO result;
  RETURN result;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION entityIdxToString(ei entity_idx_type)
  RETURNS VARCHAR(255)
AS $$
BEGIN
  IF ei IS null THEN RETURN null;
  END IF;
  RETURN concat_ws('^', ei.prefix, ei.idx);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION getContainedIn(t_id integer, t_jdoclass varchar(255))
  RETURNS entity_idx_type
AS $$
DECLARE
  resultContainedIn entity_idx_type;
BEGIN

  IF t_jdoclass='Ethernet1x10GCardDBImpl'                 THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
 ELSEIF t_jdoclass='Ethernet1x10GHighPerformanceCardDBImpl'   THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='Ethernet10x1GCardDBImpl'                 THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
 ELSEIF t_jdoclass='Ethernet10x1GHighPerformanceCardDBImpl'   THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetGE4ECCCardDBImpl'                THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetFE36ECardDBImpl'                 THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetGE4ECCCardDBImpl'                THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetGE4SCCCardDBImpl'                THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetGE8SCCCardDBImpl'                THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetXG1SCCCardDBImpl'                THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetXG1XCCCardDBImpl'                THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='PseudoWireE1T1CardDBImpl'                THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='PseudoWireOcnStmCardDBImpl'              THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='ScuFSP150CMDBImpl'                       THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='ScuTFSP150CMDBImpl'                      THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='AmiF3DBImpl'                             THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='NemiF3DBImpl'                            THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='NteF3DBImpl'                             THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='NTEFSPGE20XDBImpl'                       THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='NTEFSPXG210DBImpl'                       THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='NTUFSP150CMDBImpl'                       THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='OcStmCardF3DBImpl'                       THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='StiF3DBImpl'                             THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='StuF3DBImpl'                             THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='SwfF3DBImpl'                             THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='ModuleCPMRFSP150CMDBImpl'                THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));

  ELSEIF t_jdoclass='PowerSupplyDBImpl'                       THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_entity WHERE id=t_id));
  ELSEIF t_jdoclass='PowerSupplyF3DBImpl'                     THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_entity WHERE id=t_id));
  ELSEIF t_jdoclass='FanDBImpl'                               THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_entity WHERE id=t_id));
  ELSEIF t_jdoclass='FanF3DBImpl'                             THEN resultContainedIn := createEntityIndex(94, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_entity WHERE id=t_id));


  ELSEIF t_jdoclass='OcnStmLineF3DBImpl'                      THEN resultContainedIn := createEntityIndex(86, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex) FROM cn_port_cm WHERE id=t_id));

  ELSEIF t_jdoclass='WANEthernetTrafficPortF3DBImpl'          THEN resultContainedIn := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='PortBitsF3DBImpl'                        THEN resultContainedIn := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='Gps10MHzPortDBImpl'                      THEN resultContainedIn := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='F3PulsePerSecondPortDBImpl'              THEN resultContainedIn := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex) FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='F3TimeOfDayPortDBImpl'                   THEN resultContainedIn := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex) FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='F3GPSReceiverPortDBImpl'                 THEN resultContainedIn := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex) FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='AccNetSfpModuleF3DBImpl'                 THEN resultContainedIn := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='AccNetSfpModuleFSPGE11XDBImpl'           THEN resultContainedIn := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='AccNetSfpModuleFSPSyncProbDBImpl'        THEN resultContainedIn := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='AccNetSfpModuleFSPXG210DBImpl'           THEN resultContainedIn := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetTrafficSfpF3DBImpl'              THEN resultContainedIn := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));
  ELSEIF t_jdoclass='OcnStmLineSfpF3DBImpl'                   THEN resultContainedIn := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, slotindex)   FROM cn_module WHERE id=t_id));

  ELSEIF t_jdoclass='SlotF3DBImpl'                            THEN resultContainedIn := createEntityIndex(13, (SELECT concat_ws('.', neindex, shelfindex)   FROM cn_slot WHERE id=t_id));


  ELSEIF t_jdoclass='EthernetTrafficPortF3DBImpl' AND EXISTS (select 1 from cn_port_cm where id=t_id and slotindex=254) THEN resultContainedIn := createEntityIndex(13, (SELECT concat_ws('.', 1, 1)   FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='EthernetTrafficPortF3DBImpl'             THEN resultContainedIn := createEntityIndex(86, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex)   FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='PortF3NetDBImpl' AND EXISTS (select 1 from cn_port_cm where id=t_id and slotindex=254) THEN resultContainedIn := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, 1) FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='PortF3NetDBImpl'                         THEN resultContainedIn := createEntityIndex(86, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex)   FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='PortFSPGE20XNetDBImpl' AND EXISTS (select 1 from cn_port_cm where id=t_id and slotindex=254) THEN resultContainedIn := createEntityIndex(14, (SELECT concat_ws('.', neindex, shelfindex, 1) FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='PortFSPGE20XNetDBImpl'                   THEN resultContainedIn := createEntityIndex(86, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex)   FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='PortF3AccDBImpl' AND EXISTS (select 1 from cn_entity e join cn_network_element n on e.ne_id= n.id where e.id = t_id and n.type0 in ('115', '201', '2010', '206', '2060', '2061', '153', '154', '1804', '3204')) THEN resultContainedIn := createEntityIndex(86, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex+3) FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='PortF3AccDBImpl'                         THEN resultContainedIn := createEntityIndex(86, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex) FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='PortFSPGE20XAccDBImpl' AND EXISTS (select 1 from cn_entity e join cn_network_element n on e.ne_id= n.id where e.id = t_id and n.type0 in ('115', '201', '2010', '206', '2060', '2061', '153', '154', '1804', '3204')) THEN resultContainedIn := createEntityIndex(86, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex+3) FROM cn_port_cm WHERE id=t_id));
  ELSEIF t_jdoclass='PortFSPGE20XAccDBImpl'                   THEN resultContainedIn := createEntityIndex(86, (SELECT concat_ws('.', neindex, shelfindex, slotindex, portindex) FROM cn_port_cm WHERE id=t_id));


  ELSE
    resultContainedIn := null;
  END IF;

  RETURN resultContainedIn;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION getAllAffectedMORows()
  RETURNS SETOF cn_managed_object
AS $$
BEGIN
  RETURN QUERY
  SELECT m.* FROM cn_managed_object m
    JOIN cn_network_element n ON m.ne_id=n.id
    JOIN cn_entity e ON e.id=m.id
  WHERE n.type0 in ('206', '2060', '2061', '201', '2010', '107', '108', '109', '110', '4660', '112', '114', '1140', '1141', '1142', '1143', '115', '210', '153', '154')
  AND (e.containedin = '0' OR e.containedin IS NULL);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION generateAndStoreContainedInInTmpTable()
  RETURNS VOID
AS $$
DECLARE
  resultContainedIn entity_idx_type;
BEGIN
  INSERT INTO tmp_entity_idx_data SELECT mo.id, mo.ne_id, entityIdxToString(getContainedIn(id, jdoclass)), jdoclass FROM getAllAffectedMORows() mo;
RETURN ;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION fixPortContainedIn(id integer, idx varchar(255), par_ne_id integer)
  RETURNS VARCHAR(255)
AS $$
DECLARE
  result entity_idx_type;
BEGIN
  IF NOT EXISTS (select 1 from cn_entity e where e.ne_id = par_ne_id  and e.entityindex=idx) THEN RETURN entityIdxToString(createEntityIndex(14, trim(both '^.' from substring(idx from '\^.*\.'))));
  ELSE RETURN idx;
  END IF;

END;
$$ LANGUAGE plpgsql;

-- main

SELECT generateAndStoreContainedInInTmpTable();
-- UPDATE tmp_entity_idx_data SET containedIn = '0' where containedIn is null;


UPDATE tmp_entity_idx_data SET containedIn = fixPortContainedIn(mo_id, containedIn, ne_id)
where jdoclass in ('PortF3AccDBImpl', 'PortFSPGE20XAccDBImpl', 'PortF3AccDBImpl', 'PortFSPGE20XAccDBImpl', 'PortF3NetDBImpl', 'PortFSPGE20XNetDBImpl', 'EthernetTrafficPortF3DBImpl');

-- SELECT fixPortContainedIn(mo_id, containedIn, ne_id) from tmp_entity_idx_data where jdoclass in ('PortF3AccDBImpl', 'PortFSPGE20XAccDBImpl', 'PortF3AccDBImpl', 'PortFSPGE20XAccDBImpl', 'PortF3NetDBImpl', 'PortFSPGE20XNetDBImpl');



UPDATE cn_entity e
SET containedin=tmp.containedIn
FROM tmp_entity_idx_data tmp
WHERE tmp.mo_id=e.id and tmp.containedIn is not null;


DROP FUNCTION IF EXISTS getContainedIn(integer, varchar(255));
DROP FUNCTION IF EXISTS createEntityIndex(integer , VARCHAR(255));
DROP FUNCTION IF EXISTS generateAndStoreContainedInInTmpTable();
DROP FUNCTION IF EXISTS entityIdxToString(entity_idx_type);
DROP FUNCTION IF EXISTS getAllAffectedMORows();
DROP FUNCTION IF EXISTS fixportcontainedin(integer,character varying,integer);

DROP TABLE IF EXISTS tmp_entity_idx_data;
DROP TYPE IF EXISTS entity_idx_type;






















/*
public static final int TYPE_GE206          = 206;
public static final int TYPE_GE206F         = 2060;
public static final int TYPE_GE206V         = 2061;
public static final int TYPE_GE201          = 201;
public static final int TYPE_GE201SE        = 2010;
public static final int TYPE_JUNIPER_MX     = 108;
public static final int TYPE_SYMMETRICOM    = 110;
public static final int TYPE_150EGX         = 4660;
public static final int TYPE_GE112          = 112;
public static final int TYPE_GE114          = 114;
public static final int TYPE_GE114S         = 1140;
public static final int TYPE_GE114SH        = 1141;
public static final int TYPE_GE114PH        = 1143;
public static final int TYPE_GE114H         = 1142;
public static final int TYPE_SYNC_PROB      = 115;
public static final int TYPE_XG210          = 210;
public static final int TYPE_FSP_150CM 	    = 153;
public static final int TYPE_FSP_150CM_CPMR = 154;
*/
