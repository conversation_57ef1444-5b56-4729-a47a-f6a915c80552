#
# Copyright 2023 Adtran Networks SE. All rights reserved.
#
# Owner: adammig
#
# $Id: $
#
#
# This config file provisions the alarm configuration information on Juniper devices
# so that the alarm history can be created and is accessible from the SNMP interface
# Steps for executing the configuration
# 1. ftp to the Juniper device
# 2. enter configuration mode
# 3. execute the command "load merge JuniperAlarm.config"
# 4. commit
#
snmp {
    alarm-management {
        alarm-list-name aotn {
            alarm-id 1 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 1;
                    description "OTN Loss of Signal";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 1;
                    description "OTN Loss of Signal";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 2 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 2;
                    description "OTN Loss of Frame";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 2;
                    description "OTN Loss of Frame";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 3 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 3;
                    description "OTN Loss of Multi Frame";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 3;
                    description "OTN Loss of Multi Frame";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 4 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 4;
                    description "OTN SSF";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 4;
                    description "OTN SSF";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 5 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 5;
                    description "OTN OTU BDI";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 5;
                    description "OTN OTU BDI";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 6 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 6;
                    description "OTN OTU Trail Trace Mismatch";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 6;
                    description "OTN OTU Trail Trace Mismatch";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 7 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 7;
                    description "OTN OTU IAE";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 7;
                    description "OTN OTU IAE";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 8 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 8;
                    description "OTN OTU BIAE";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 8;
                    description "OTN OTU BIAE";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 9 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 9;
                    description "OTN TSF";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 9;
                    description "OTN TSF";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 10 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 10;
                    description "OTN OTU Degraded";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 10;
                    description "OTN OTU Degraded";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 11 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 11;
                    description "OTN OTU FEC Excessive Errors";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 11;
                    description "OTN OTU FEC Excessive Errors";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 12 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 12;
                    description "OTN OTU BBE Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 12;
                    description "OTN OTU BBE Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 13 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 13;
                    description "OTN OTU ES Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 13;
                    description "OTN OTU ES Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 14 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 14;
                    description "OTN OTU SES Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 14;
                    description "OTN OTU SES Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 15 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 15;
                    description "OTN OTU UAS Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 15;
                    description "OTN OTU UAS Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 16 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 16;
                    description "OTN OTU Bip8 Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 16;
                    description "OTN OTU Bip8 Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 17 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 17;
                    description "OTN  FEC Uncorrected Words Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 17;
                    description "OTN  FEC Uncorrected Words Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 18 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 18;
                    description "OTN Pre FEC BER Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 18;
                    description "OTN Pre FEC BER Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 19 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 19;
                    description "OTN OTU 24 hour BBE Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 19;
                    description "OTN OTU 24 hour BBE Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 20 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 20;
                    description "OTN OTU 24 hour ES Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 20;
                    description "OTN OTU 24 hour ES Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 21 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 21;
                    description "OTN OTU 24 hour SES Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 21;
                    description "OTN OTU 24 hour SES Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 22 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 22;
                    description "OTN OTU 24 hour UAS Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 22;
                    description "OTN OTU 24 hour UAS Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 23 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 23;
                    description "OTN OTU 24 hour Bip8 Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 23;
                    description "OTN OTU 24 hour Bip8 Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 24 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 24;
                    description "OTN  Pre FEC BER 24 hour Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 24;
                    description "OTN  Pre FEC BER 24 hour Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 25 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNOChOTUkAlarmClear;
                    varbind-index 5;
                    varbind-value 25;
                    description "OTN OTU AIS";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNOChOTUkAlarmSet;
                    varbind-index 5;
                    varbind-value 25;
                    description "OTN OTU AIS";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
        }
	alarm-list-name aodu {
            alarm-id 1 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 1;
                    description "OTN ODU/TCM OCI";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 1;
                    description "OTN ODU/TCM OCI";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 2 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 2;
                    description "OTN ODU/TCM LCK";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 2;
                    description "OTN ODU/TCM LCK";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 3 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 3;
                    description "OTN ODU/TCM BDI";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 3;
                    description "OTN ODU/TCM BDI";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 4 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 4;
                    description "OTN ODU/TCM Trail Trace mismatch";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 4;
                    description "OTN ODU/TCM Trail Trace mismatch";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 5 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 5;
                    description "OTN ODU/TCM Degraded";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 5;
                    description "OTN ODU/TCM Degraded";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 6 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 6;
                    description "OTN ODU IAE";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 6;
                    description "OTN ODU IAE";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 7 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 7;
                    description "OTN ODU/TCM Loss of Tandem Connection";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 7;
                    description "OTN ODU/TCM Loss of Tandem Connection";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 8 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 8;
                    description "OTN ODU/TCM CSF";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 8;
                    description "OTN ODU/TCM CSF";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 9 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 9;
                    description "OTN ODU/TCM SSF";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 9;
                    description "OTN ODU/TCM SSF";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 10 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 10;
                    description "OTN ODU/TCM TSF";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 10;
                    description "OTN ODU/TCM TSF";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 11 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 11;
                    description "OTN ODU BBE Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 11;
                    description "OTN ODU BBE Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 12 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 12;
                    description "OTN ODU ES Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 12;
                    description "OTN ODU ES Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 13 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 13;
                    description "OTN ODU SES Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 13;
                    description "OTN ODU SES Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 14 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 14;
                    description "OTN ODU UAS Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 14;
                    description "OTN ODU UAS Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 15 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 15;
                    description "OTN ODU Bip8 Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 15;
                    description "OTN ODU Bip8 Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 16 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 16;
                    description "OTN ODU Ais";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 16;
                    description "OTN ODU Ais";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 17 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 17;
                    description "OTN ODU Payload Type Mismatch";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 17;
                    description "OTN ODU Payload Type Mismatch";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 18 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 18;
                    description "OTN ODU 24 hour BBE Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 18;
                    description "OTN ODU 24 hour BBE Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 19 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 19;
                    description "OTN ODU 24 hour ES Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 19;
                    description "OTN ODU 24 hour ES Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 20 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 20;
                    description "OTN ODU SES Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 20;
                    description "OTN ODU SES Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 21 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 21;
                    description "OTN ODU 24 hour UAS Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 21;
                    description "OTN ODU 24 hour UAS Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 22 {
                alarm-state 1 {
                    notification-id jnxoptIfOTNODUkTcmAlarmClear;
                    varbind-index 5;
                    varbind-value 22;
                    description "OTN ODU 24 Hour Bip8 Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxoptIfOTNODUkTcmAlarmSet;
                    varbind-index 5;
                    varbind-value 22;
                    description "OTN ODU 24 Hour Bip8 Threshold";
                    varbind-subtree jnxoptIfOTNAlarmSeverity;
                    resource-prefix 0.0;
                }
            }
        }
        alarm-list-name aoptics {
            alarm-id 1 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 1;
                    description "Optics Loss of Signal";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 1;
                    description "Optics Loss of Signal";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 2 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 2;
                    description "Optics Wavelength Lock Error";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 2;
                    description "Optics Wavelength Lock Error";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 3 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 3;
                    description "Optics Tx Power High";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 3;
                    description "Optics Tx Power High";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 4 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 4;
                    description "Optics Tx Power Low";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 4;
                    description "Optics Tx Power Low";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 5 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 5;
                    description "Optics Bias Current High";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 5;
                    description "Optics Bias Current High";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 6 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 6;
                    description "Optics Bias Current Low";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 6;
                    description "Optics Bias Current Low";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 7 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 7;
                    description "Optics Temperature High";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 7;
                    description "Optics Temperature High";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 8 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 8;
                    description "Optics Temperature Low";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 8;
                    description "Optics Temperature Low";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 9 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 9;
                    description "Optics Tx PLL Lock";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 9;
                    description "Optics Tx PLL Lock";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 10 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 10;
                    description "Optics Rx PLL Lock";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 10;
                    description "Optics Rx PLL Lock";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 11 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 11;
                    description "Optics Average Power";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 11;
                    description "Optics Average Power";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 12 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 12;
                    description "Optics Rx Loss Average Power";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 12;
                    description "Optics Rx Loss Average Power";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 13 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 13;
                    description "Optics Loss of AC Power";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 13;
                    description "Optics Loss of AC Power";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 14 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 14;
                    description "Optics Tx Power High Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 14;
                    description "Optics Tx Power High Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 15 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 15;
                    description "Optics Tx Power Low Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 15;
                    description "Optics Tx Power Low Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 16 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 16;
                    description "Optics Rx Power High Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 16;
                    description "Optics Rx Power High Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 17 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 17;
                    description "Optics Rx Power Low Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 17;
                    description "Optics Rx Power Low Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 18 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 18;
                    description "Optics Module Temperature High Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 18;
                    description "Optics Module Temperature High Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 19 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 19;
                    description "Optics Module Temperature Low Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 19;
                    description "Optics Module Temperature Low Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 20 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 20;
                    description "Optics 24 Hour Tx Power High Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 20;
                    description "Optics 24 Hour Tx Power High Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 21 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 21;
                    description "Optics 24 Hour Tx Power Low Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 21;
                    description "Optics 24 Hour Tx Power Low Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 22 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 22;
                    description "Optics 24 Hour Rx Power High Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 22;
                    description "Optics 24 Hour Rx Power High Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 23 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 23;
                    description "Optics 24 Hour Rx Power Low Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 23;
                    description "Optics 24 Hour Rx Power Low Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 24 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 24;
                    description "Optics 24 Hour Module Temperature High Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 24;
                    description "Optics 24 Hour Module Temperature High Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 25 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 25;
                    description "Optics 24 Hour Module Temperature Low Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 25;
                    description "Optics 24 Hour Module Temperature Low Threshold";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 26 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 26;
                    description "Optics Rx Power High";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 26;
                    description "Optics Rx Power High";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 27 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 27;
                    description "Optics Rx Power Low";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 27;
                    description "Optics Rx Power Low";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 28 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 28;
                    description "Optics Tx Power High Warning";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 28;
                    description "Optics Tx Power High Warning";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 29 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 29;
                    description "Optics Tx Power Low Warning";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 29;
                    description "Optics Tx Power Low Warning";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 30 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 30;
                    description "Optics Rx Power High Warning";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 30;
                    description "Optics Rx Power High Warning";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 31 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 31;
                    description "Optics Rx Power Low Warning";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 31;
                    description "Optics Rx Power Low Warning";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 32 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 32;
                    description "Optics Module Temperature High Warning";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 32;
                    description "Optics Module Temperature High Warning";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 33 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 33;
                    description "Optics Module Temperature Low Warning";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 33;
                    description "Optics Module Temperature High Warning";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 34 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 34;
                    description "Optics rx Carrier Frequency High";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 34;
                    description "Optics rx Carrier Frequency High";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 35 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 35;
                    description "Optics rx Carrier Frequency Low";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 35;
                    description "Optics rx Carrier Frequency Low";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 36 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 36;
                    description "Chromatic Dispersion High";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 36;
                    description "Chromatic Dispersion High";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 37 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 37;
                    description "Chromatic Dispersion Low";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 37;
                    description "Chromatic Dispersion Low";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 38 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 38;
                    description "Q Low Warning";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 38;
                    description "Q Low Warning";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 39 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 39;
                    description "OSNR Low Warning";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 39;
                    description "OSNR Low Warning";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 40 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 40;
                    description "Carrier Frequency High";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 40;
                    description "Carrier Frequency High";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 41 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 41;
                    description "Carrier Frequency Low";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 41;
                    description "Carrier Frequency Low";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 42 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 42;
                    description "24H Carrier Frequency High";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 42;
                    description "24H Carrier Frequency High";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
            alarm-id 43 {
                alarm-state 1 {
                    notification-id jnxOpticsNotificationCleared;
                    varbind-index 4;
                    varbind-value 43;
                    description "24H Carrier Frequency Low";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOpticsNotificationSet;
                    varbind-index 4;
                    varbind-value 43;
                    description "24H Carrier Frequency Low";
                    varbind-subtree jnxOpticsNotificationSeverity;
                    resource-prefix 0.0;
                }
            }
        }
        alarm-list-name achassis {
            alarm-id 1 {
                alarm-state 1 {
                    notification-id jnxPowerSupplyOK;
                    varbind-index 0;
                    varbind-value 0;
                    description "Power Supply OK";
                    varbind-subtree jnxContentsDescr;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxPowerSupplyFailure;
                    varbind-index 0;
                    varbind-value 0;
                    description "Power Supply Failure";
                    varbind-subtree jnxContentsDescr;
                    resource-prefix 0.0;
                }
            }
            alarm-id 2 {
                alarm-state 1 {
                    notification-id jnxFanOK;
                    varbind-index 0;
                    varbind-value 0;
                    description "Fan Failure";
                    varbind-subtree jnxContentsDescr;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxFanFailure;
                    varbind-index 0;
                    varbind-value 0;
                    description "Fan Failure";
                    varbind-subtree jnxContentsDescr;
                    resource-prefix 0.0;
                }
            }
            alarm-id 3 {
                alarm-state 1 {
                    notification-id jnxTemperatureOK;
                    varbind-index 0;
                    varbind-value 0;
                    description "Over Temperature";
                    varbind-subtree jnxContentsDescr;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOverTemperature;
                    varbind-index 0;
                    varbind-value 0;
                    description "Over Temperature";
                    varbind-subtree jnxContentsDescr;
                    resource-prefix 0.0;
                }
            }
            alarm-id 4 {
                alarm-state 1 {
                    notification-id jnxRedundancySwitchover;
                    varbind-index 7;
                    varbind-value 3;
                    description "Redundancy Switchover";
                    varbind-subtree jnxRedundancyDescr;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxRedundancySwitchover;
                    varbind-index 7;
                    varbind-value 2;
                    description "Redundancy Switchover";
                    varbind-subtree jnxRedundancyDescr;
                    resource-prefix 0.0;
                }
            }
            alarm-id 5 {
                alarm-state 1 {
                    notification-id jnxFruOK;
                    varbind-index 0;
                    varbind-value 0;
                    description "FRU Failure";
                    varbind-subtree jnxFruName;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxFruFailed;
                    varbind-index 0;
                    varbind-value 0;
                    description "FRU Failure";
                    varbind-subtree jnxFruName;
                    resource-prefix 0.0;
                }
            }
        }
        alarm-list-name apmon {
            alarm-id 1 {
                alarm-state 1 {
                    notification-id jnxPMonOverloadCleared;
                    varbind-index 3;
                    varbind-value 1;
                    description "PMon Memory Overload";
                    varbind-subtree jnxPMonLastOverloadDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxPMonOverloadSet;
                    varbind-index 3;
                    varbind-value 1;
                    description "PMon Memory Overload";
                    varbind-subtree jnxPMonLastOverloadDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 2 {
                alarm-state 1 {
                    notification-id jnxPMonOverloadCleared;
                    varbind-index 3;
                    varbind-value 2;
                    description "PMon PPS Overload";
                    varbind-subtree jnxPMonLastOverloadDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxPMonOverloadSet;
                    varbind-index 3;
                    varbind-value 2;
                    description "PMon PPS Overload";
                    varbind-subtree jnxPMonLastOverloadDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 3 {
                alarm-state 1 {
                    notification-id jnxPMonOverloadCleared;
                    varbind-index 3;
                    varbind-value 4;
                    description "PMon BPS Overload";
                    varbind-subtree jnxPMonLastOverloadDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxPMonOverloadSet;
                    varbind-index 3;
                    varbind-value 4;
                    description "PMon BPS Overload";
                    varbind-subtree jnxPMonLastOverloadDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 4 {
                alarm-state 1 {
                    notification-id jnxPMonOverloadCleared;
                    varbind-index 3;
                    varbind-value 8;
                    description "PMon Memory Warning";
                    varbind-subtree jnxPMonLastOverloadDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxPMonOverloadSet;
                    varbind-index 3;
                    varbind-value 8;
                    description "PMon Memory Warning";
                    varbind-subtree jnxPMonLastOverloadDate;
                    resource-prefix 0.0;
                }
            }
        }
        alarm-list-name asonet {
            alarm-id 100 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 0;
                    description "Sonet Loss of light";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 0;
                    description "Sonet Loss of light";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 1 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 1;
                    description "Sonet PLL lock";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 1;
                    description "Sonet PLL lock";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 2 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 2;
                    description "Sonet Loss of frame";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 2;
                    description "Sonet Loss of frame";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 3 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 3;
                    description "Sonet Loss of signal";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 3;
                    description "Sonet Loss of signal";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 4 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 4;
                    description "Sonet Severely errored frame";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 4;
                    description "Sonet Severely errored frame";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 5 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 5;
                    description "Sonet Line AIS";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 5;
                    description "Sonet Line AIS";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 6 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 6;
                    description "Sonet Path AIS";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 6;
                    description "Sonet Path AIS";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 7 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 7;
                    description "Sonet Loss of pointer";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 7;
                    description "Sonet Loss of pointer";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 8 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 8;
                    description "Sonet bit error rate defect";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 8;
                    description "Sonet bit error rate defect";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 9 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 9;
                    description "Sonet bit error rate fault";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 9;
                    description "Sonet bit error rate fault";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 10 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 10;
                    description "Sonet Line Remote Defect Indication";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 10;
                    description "Sonet Line Remote Defect Indication";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 11 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 11;
                    description "Sonet Path Remote Defect Indication";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 11;
                    description "Sonet Path Remote Defect Indication";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 12 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 12;
                    description "Sonet Remote Error Indication";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 12;
                    description "Sonet Remote Error Indication";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 13 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 13;
                    description "Sonet Unequipped";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 13;
                    description "Sonet Unequipped";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 14 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 14;
                    description "Sonet Path mismatch";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 14;
                    description "Sonet Path mismatch";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 15 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 15;
                    description "Sonet Loss of Cell delineation";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 15;
                    description "Sonet Loss of Cell delineation";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 16 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 16;
                    description "Sonet VT AIS";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 16;
                    description "Sonet VT AIS";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 17 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 17;
                    description "Sonet VT Loss Of Pointer";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 17;
                    description "Sonet VT Loss Of Pointer";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 18 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 18;
                    description "Sonet VT Remote Defect Indication";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 18;
                    description "Sonet VT Remote Defect Indication";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 19 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 19;
                    description "Sonet VT Unequipped";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 19;
                    description "Sonet VT Unequipped";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 20 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 20;
                    description "Sonet VT label mismatch error";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 20;
                    description "Sonet VT label mismatch error";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 21 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 21;
                    description "Sonet VT Loss of Cell delineation";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 21;
                    description "Sonet VT Loss of Cell delineation";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 22 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 22;
                    description "SDH Loss of light";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 22;
                    description "SDH Loss of light";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 23 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 23;
                    description "SDH PLL lock";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 23;
                    description "SDH PLL lock";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 24 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 24;
                    description "SDH Loss of frame";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 24;
                    description "SDH Loss of frame";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 25 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 25;
                    description "SDH Loss of signal";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 25;
                    description "SDH Loss of signal";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 26 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 26;
                    description "SDH Out of frame";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 26;
                    description "SDH Out of frame";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 27 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 27;
                    description "SDH Multiplex Section AIS";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 27;
                    description "SDH Multiplex Section AIS";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 28 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 28;
                    description "SDH HP AIS";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 28;
                    description "SDH HP AIS";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 29 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 29;
                    description "SDH Loss of pointer";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 29;
                    description "SDH Loss of pointer";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 30 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 30;
                    description "SDH bit err. rate defect";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 30;
                    description "SDH bit err. rate defect";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 31 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 31;
                    description "SDH bit err. rate fault";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 31;
                    description "SDH bit err. rate fault";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 32 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 32;
                    description "SDH Multiplex Section FERF";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 32;
                    description "SDH Multiplex Section FERF";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 33 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 33;
                    description "SDH HP FERF";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 33;
                    description "SDH HP FERF";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 34 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 34;
                    description "SDH Multiplex Section FEBE";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 34;
                    description "SDH Multiplex Section FEBE";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 35 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 35;
                    description "SDH HP Unequipped";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 35;
                    description "SDH HP Unequipped";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 36 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 36;
                    description "SDH HP mismatch";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 36;
                    description "SDH HP mismatch";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
            alarm-id 37 {
                alarm-state 1 {
                    notification-id jnxSonetAlarmCleared;
                    varbind-index 2;
                    varbind-value 37;
                    description "SDH Loss of Cell delineation";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxSonetAlarmSet;
                    varbind-index 2;
                    varbind-value 37;
                    description "SDH Loss of Cell delineation";
                    varbind-subtree jnxSonetLastAlarmDate;
                    resource-prefix 0.0;
                }
            }
        }
        alarm-list-name aotn2 {
            alarm-id 100 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 0;
                    description "OTN Loss of signal";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 0;
                    description "OTN Loss of signal";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 1 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 1;
                    description "OTN Loss of frame";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 1;
                    description "OTN Loss of frame";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 2 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 2;
                    description "OTN Loss of multi frame";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 2;
                    description "OTN Loss of multi frame";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 3 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 3;
                    description "OTN wavelength lock";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 3;
                    description "OTN wavelength lock";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 4 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 4;
                    description "OTN AIS";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 4;
                    description "OTN AIS";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 5 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 5;
                    description "OTN OTU BDI";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 5;
                    description "OTN OTU BDI";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 6 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 6;
                    description "OTN OTU TTIM";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 6;
                    description "OTN OTU TTIM";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 7 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 7;
                    description "OTN OTU IAE";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 7;
                    description "OTN OTU IAE";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 8 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 8;
                    description "OTN OTU bit error rate defect";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 8;
                    description "OTN OTU bit error rate defect";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 9 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 9;
                    description "OTN OTU  bit error rate fault";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 9;
                    description "OTN OTU  bit error rate fault";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 10 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 10;
                    description "OTN OTU Fec Excessive Errors";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 10;
                    description "OTN OTU Fec Excessive Errors";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 11 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 11;
                    description "OTN OTU Fec Degraded Errors";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 11;
                    description "OTN OTU Fec Degraded Errors";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 12 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 12;
                    description "OTN OTU BBE Threshold";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 12;
                    description "OTN OTU BBE Threshold";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 13 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 13;
                    description "OTN OTU ES Threshold";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 13;
                    description "OTN OTU ES Threshold";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 14 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 14;
                    description "OTN OTU SES Threshold";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 14;
                    description "OTN OTU SES Threshold";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 15 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 15;
                    description "OTN OTU UAS Threshold";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 15;
                    description "OTN OTU UAS Threshold";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 16 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 16;
                    description "OTN ODU AIS";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 16;
                    description "OTN ODU AIS";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 17 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 17;
                    description "OTN ODU OCI";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 17;
                    description "OTN ODU OCI";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 18 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 18;
                    description "OTN ODU LCK";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 18;
                    description "OTN ODU LCK";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 19 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 19;
                    description "OTN ODU BDI";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 19;
                    description "OTN ODU BDI";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 20 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 20;
                    description "OTN ODU TTIM";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 20;
                    description "OTN ODU TTIM";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 21 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 21;
                    description "OTN ODU bit error rate defect";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 21;
                    description "OTN ODU bit error rate defect";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 22 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 22;
                    description "OTN ODU  bit error rate fault";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 22;
                    description "OTN ODU  bit error rate fault";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 23 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 23;
                    description "OTN ODU Rx APS Change";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 23;
                    description "OTN ODU Rx APS Change";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 24 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 24;
                    description "OTN ODU BBE Threshold";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 24;
                    description "OTN ODU BBE Threshold";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 25 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 25;
                    description "OTN OTU ES Threshold";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 25;
                    description "OTN OTU ES Threshold";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 26 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 26;
                    description "OTN OTU SES Threshold";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 26;
                    description "OTN OTU SES Threshold";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 27 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 27;
                    description "OTN ODU UAS Threshold";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 27;
                    description "OTN ODU UAS Threshold";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
            alarm-id 28 {
                alarm-state 1 {
                    notification-id jnxOtnAlarmCleared;
                    varbind-index 5;
                    varbind-value 28;
                    description "OTN OPU Payload Mismatch";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
                alarm-state 2 {
                    notification-id jnxOtnAlarmSet;
                    varbind-index 5;
                    varbind-value 28;
                    description "OTN OPU Payload Mismatch";
                    varbind-subtree jnxOtnCurrentAlarms;
                    resource-prefix 0.0;
                }
            }
        }
    }
}
