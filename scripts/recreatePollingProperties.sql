--
-- Copyright 2023 Adtran Networks SE. All rights reserved.
--
-- Owner: <PERSON><PERSON><PERSON><PERSON>
--
CREATE OR REPLACE FUNCTION recreate_polling_properties()
  RETURNS VOID AS $$
DECLARE
  -- 2 dimensional array, each entry contains 2 values, first is polling type, second is default polling interval
  pts           INTEGER [] := ARRAY [[4, 900000], [5, 86400000], [6, 300000], [36, 300000], [7, 86400000], [8, 86400000],
  [9, 86400000], [35, 86400000], [11, 86400000], [91, 86400000], [120, 86400000], [179, 300000],
  [175, 300000], [127, 86400000], [134, 600000], [163, 604800000], [190, 86400000], [191, 86400000], [202, 86400000]];
  -- array of device types not supporting backup
  udt           INTEGER [] := ARRAY [350000, 2, 11111, 108, 109, 107, 7504, 500, 110];
  m             INTEGER [];
  ne_id         INTEGER;
  ip            VARCHAR;
  pm_id         INTEGER;
  status        INTEGER;
  ver           INTEGER := 0;
  point_of_time INTEGER := 0;
  duration      INTEGER := 0;
  ne_name       VARCHAR;
  ne_ip         VARCHAR;
  ne_type       INTEGER;
  counter       INTEGER;
  test          BOOL;
  nta           INTEGER [];
  backupType    INTEGER :=8;
BEGIN
  FOREACH m SLICE 1 IN ARRAY pts
  LOOP
    counter := 0;
    FOR pm_id, ne_name, ne_ip, ne_type IN SELECT
                                            ne.pollingmanagerid,
                                            ne.name0,
                                            ne.ipaddress,
                                            ne.type0
                                          FROM cn_network_element ne
                                          WHERE NOT exists(SELECT jdoversion
                                                           FROM pl_polling_type_properties p
                                                           WHERE p.pollingmanagerid = ne.pollingmanagerid AND p.pollingtype = m [1])
    LOOP
      counter := counter + 1;
      nta := ARRAY [ne_type];
      test := (m [1] = backupType) AND (SELECT nta && udt);
      IF test
      THEN
        status :=1;
      ELSE
        status :=0;
      END IF;
      INSERT INTO pl_polling_type_properties (pollingmanagerid, pollingtype, durationtime, intervall, pointoftime, status, jdoversion)
      VALUES (pm_id, m [1], duration, m [2], point_of_time, status, ver);
    END LOOP;
    IF counter > 0
    THEN
      RAISE NOTICE 'added % entries for polling %', counter, m [1];
    END IF;

  END LOOP;

  UPDATE pl_polling_type_properties
  SET status = 1
  WHERE pollingtype = 8 AND pollingmanagerid IN (
    SELECT p.pollingmanagerid
    FROM pl_polling_type_properties p JOIN cn_network_element ne ON ne.pollingmanagerid = p.pollingmanagerid
    WHERE p.pollingtype = 8 AND p.status = 0 AND ne.type0 IN ('350000', '2', '11111', '108', '109', '107', '7504', '500', '110')
  );

  UPDATE pl_polling_type_properties
  SET status = 1
  WHERE pollingtype = 8 AND pollingmanagerid IN (SELECT pollingmanagerid
                                                 FROM cn_network_element ne
                                                 WHERE ne.type0 IN ('151', '153') AND ne.local_id IS NOT NULL);

END;
$$ LANGUAGE plpgsql;
SELECT recreate_polling_properties();

DROP FUNCTION IF EXISTS recreate_polling_properties();