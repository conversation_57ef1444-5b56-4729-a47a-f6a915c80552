CREATE OR REPLACE PROCEDURE insert_alarm_ec(
    p_name TEXT,
    p_short_name TEXT,
    p_message TEXT,
    p_alarm_id INT
)
LANGUAGE plpgsql AS $$
BEGIN
IF NOT EXISTS ( SELECT 1 FROM cn_notification_ec WHERE short_name = p_short_name)
THEN
    INSERT INTO cn_notification_ec (
        id, notification_ec_type, name, short_name, message,
        severity_working, severity_protecting, severity_no_service,
        alarm_id, raise_clear_name, raise_clear_number
    )
    VALUES (
        (SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),
        'ALARM_EC', p_name, p_short_name, p_message,
        'NE_DEFINED', 'NE_DEFINED', 'NE_DEFINED',
        p_alarm_id, p_name, p_alarm_id
    );

    INSERT INTO cn_notification_ne_type_list (
        ne_type_list, notification_ec_id
    )
    VALUES (
        'NETWORK_ELEMENT_TYPE_F8',
        (SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT')
    );

    UPDATE JDO_SEQUENCE
    SET sequence_value = sequence_value + 1
    WHERE id LIKE 'DEFAULT';
END IF;
END;
$$;

CREATE OR REPLACE PROCEDURE insert_event_ec(
    p_name TEXT,
    p_short_name TEXT,
    p_message TEXT
)
LANGUAGE plpgsql AS $$
BEGIN
IF NOT EXISTS ( SELECT 1 FROM cn_notification_ec WHERE short_name = p_short_name)
THEN
    INSERT INTO cn_notification_ec (
        id, notification_ec_type, name, short_name, message, trap_name, trap_enterprise
    )
    VALUES (
        (SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT'),
        'EVENT_EC', p_name, p_short_name, p_message,
        p_name, '1.3.6.1.4.1.2544.1.20.1.2.1'
    );

    INSERT INTO cn_notification_ne_type_list (
        ne_type_list, notification_ec_id
    )
    VALUES (
        'NETWORK_ELEMENT_TYPE_F8',
        (SELECT sequence_value FROM JDO_SEQUENCE WHERE id LIKE 'DEFAULT')
    );

    UPDATE JDO_SEQUENCE
    SET sequence_value = sequence_value + 1
    WHERE id LIKE 'DEFAULT';
END IF;
END;
$$;

CALL insert_alarm_ec('volt-anr', 'VOLTANR', 'Voltage abnormal', 1);
CALL insert_alarm_ec('remove', 'RMV', 'Removed', 2);
CALL insert_alarm_ec('fault', 'FLT', 'Equipment Fault', 3);
CALL insert_alarm_ec('mea', 'MEA', 'Mismatch', 4);
CALL insert_alarm_ec('eqp-incompathw', 'INCMPTHW', 'Incompatible HW according to HSC-ID', 5);
CALL insert_alarm_ec('mea-approve', 'MEAAPRV', 'Equipped plug not approved', 6);
CALL insert_alarm_ec('mea-accept', 'MEAACPT', 'Equipped plug not accepted', 7);
CALL insert_alarm_ec('mea-capability', 'MEACAP', 'Capability mismatch', 8);
CALL insert_alarm_ec('fwp-mism-nsa', 'FWPM', 'Firmware mismatch - Upgrade SA', 9);
CALL insert_alarm_ec('high-temp-gradient', 'HTMPGRD', 'Fast Temperature Change', 10);
CALL insert_alarm_ec('bpl-comm-fail', 'BPLCOMMF', 'Communication Failure', 11);
CALL insert_alarm_ec('database-mismatch', 'DATABM', 'Database mismatch', 12);
CALL insert_alarm_ec('high-temp-shutdown', 'HTMPSDN', 'High Temperature Shutdown', 13);
CALL insert_alarm_ec('psu-redundancy-mismatch', 'PREDMISS', 'Power supply redundancy mismatch', 14);
CALL insert_alarm_ec('insufficient-power', 'INSUFPWR', 'Insufficient power', 15);
CALL insert_alarm_ec('bpl-eeprom-comm-fail', 'BPLEPF', 'Backplane EEPROM Read Failure', 16);
CALL insert_alarm_ec('power-feed-undervoltage', 'FDVOLT', 'Power feed under-voltage', 17);
CALL insert_alarm_ec('power-feed-a-fail', 'FDAF', 'Power feed A failure', 18);
CALL insert_alarm_ec('power-feed-b-fail', 'FDBF', 'Power feed B failure', 19);
CALL insert_alarm_ec('output-power-fail', 'OPWRF', 'Power supply output power failure', 20);
CALL insert_alarm_ec('fwp-mism-sa', 'FWPMSA', 'FWP Mismatch (SA)', 21);
CALL insert_alarm_ec('temperature-high', 'TEMPH', 'Temperature high', 22);
CALL insert_alarm_ec('temperature-low', 'TEMPL', 'Temperature low', 23);
CALL insert_alarm_ec('output-overcurrent', 'OCURR', '12V output overcurrent warning', 24);
CALL insert_alarm_ec('fan-fault', 'FANFLT', 'Power supply fan fault', 25);
CALL insert_alarm_ec('fan-filter-clean', 'FFCLEAN', 'Clean Filter', 26);
CALL insert_alarm_ec('fan-filter-replace', 'FFREPL', 'Fan filter needs replacement', 27);
CALL insert_alarm_ec('reboot-inprogress', 'RBINPRG', 'Reboot in progress', 28);
CALL insert_alarm_ec('hardware-busy', 'HWBUSY', 'Hardware Busy', 29);
CALL insert_alarm_ec('plug-comm-fail', 'PLGCOMMF', 'Communication Failure to plug', 30);
CALL insert_alarm_ec('root-certificate-missing', 'RCERTMIS', 'Root Certifcate Missing', 31);
CALL insert_alarm_ec('certificate-mismatch', 'CRTMISM', 'Certificate Mismatch', 32);
CALL insert_alarm_ec('svm', 'SVM', 'Software Mismatch', 101);
CALL insert_alarm_ec('lotfw', 'LTF', 'Software Package Mismatch (SA)', 102);
CALL insert_alarm_ec('manifest-incomplete', 'MFINC', 'Manifest incomplete', 103);
CALL insert_alarm_ec('manifest-mismatch', 'MFM', 'Manifest mismatch', 104);
CALL insert_alarm_ec('prbs-generation-inprogress', 'GEN', 'PRBS generation in progress', 110);
CALL insert_alarm_ec('prbs-detection-inprogress', 'DET', 'PRBS detection in progress', 111);
CALL insert_alarm_ec('terminal-loopback-inprogress', 'TERM', 'Terminal loopback in progress', 120);
CALL insert_alarm_ec('facility-loopback-inprogress', 'FAC', 'Facility loopback in progress', 121);
CALL insert_alarm_ec('ntp-server-unavailable', 'NTPSRVUN', 'server-unavailable', 201);
CALL insert_alarm_ec('loss-of-signal', 'LOS', 'Loss of Signal payload and overhead', 211);
CALL insert_alarm_ec('laser-fail', 'LSRFAIL', 'Summary source laser failure any lane', 212);
CALL insert_alarm_ec('force-laser-on', 'FLSRON', 'Force all lasers on', 213);
CALL insert_alarm_ec('force-transmit-shutdown', 'FTS', 'Force transmit shutdown all lanes', 214);
CALL insert_alarm_ec('loss-of-clock', 'LOC', 'Loss of Clock', 215);
CALL insert_alarm_ec('loss-of-overhead', 'LOOH', 'Loss of Overhead', 216);
CALL insert_alarm_ec('loss-of-prbs-pattern', 'LSS', 'PRBS loss of sequence synchronization', 217);
CALL insert_alarm_ec('laser-on-delay', 'LSRDLY', 'Laser on delay', 218);
CALL insert_alarm_ec('auto-power-reduction', 'APR', 'Automatic power reduction', 219);
CALL insert_alarm_ec('laser-temperature-hi', 'LSRTMPH', 'Laser temperature high', 220);
CALL insert_alarm_ec('supporting-equipment-failure', 'SGEF', 'Supporting equipment failure', 221);
CALL insert_alarm_ec('tuned-freq-mismatch', 'MEAFREQ', 'Frequency mismatch', 222);
CALL insert_alarm_ec('loss-of-lane', 'LOLN', 'Loss of Lane', 223);
CALL insert_alarm_ec('laser-bias-cur-abnormal', 'LBCANR', 'Laser bias current abnormal', 224);
CALL insert_alarm_ec('laser-temperature-lo', 'LSRTMPL', 'Laser temperature low', 225);
CALL insert_alarm_ec('local-oscillator-fail', 'LOFAIL', 'Local Oscillator Fail', 226);
CALL insert_alarm_ec('local-oscillator-temperature-hi', 'LOTMPH', 'LO Laser temperature high', 227);
CALL insert_alarm_ec('local-oscillator-temperature-lo', 'LOTMPL', 'LO Laser temperature low', 228);
CALL insert_alarm_ec('local-oscillator-bias-cur-abnormal', 'LOCANR', 'LO Laser bias current abnormal', 229);
CALL insert_alarm_ec('port-config-mismatch', 'PCFGM', 'Port configuration mismatch', 230);
CALL insert_alarm_ec('gcc-unavailable-recoverable', 'GCCUAR', 'GCC HW resource unavailable (rec.)', 231);
CALL insert_alarm_ec('tca-opt-local-oscillator-bias-cur-hi', 'TLOBCH', 'TCA Local Oscillator Bias Current High', 232);
CALL insert_alarm_ec('tca-local-oscillator-temperature-lo', 'TLOTMPL', 'TCA Local Oscillator Temperature Low', 233);
CALL insert_alarm_ec('tca-local-oscillator-temperature-hi', 'TLOTMPH', 'TCA Local Oscillator Temperature High', 234);
CALL insert_alarm_ec('transmit-signal-fail', 'TXSF', 'TX Signal Fail', 235);
CALL insert_alarm_ec('loss-of-light', 'LOL', 'Loss of Light', 236);
CALL insert_alarm_ec('license-server-disconnect', 'LMSDISC', 'Raised against lm-sys-fm entity', 241);
CALL insert_alarm_ec('license-missing', 'LMMIS', 'Raised against lm mo /lm-fm-entity', 242);
CALL insert_alarm_ec('license-server-config-missing', 'LMSCFGM', 'License server url not configured', 246);
CALL insert_alarm_ec('license-file-missing', 'LMFILM', 'It is raised in case of node lock policy when license file is not available in NE', 247);
CALL insert_alarm_ec('optical-input-power-too-high', 'OIPTOHI', 'Optical Input Power Too High', 248);
CALL insert_alarm_ec('backup-license-server-disconnect', 'BKDISC', 'Raised against lm-sys-fm entity', 249);
CALL insert_alarm_ec('optical-amplifier-pump-bias-hi', 'OAPMPBH', 'Optical Amplifier pump bias high', 250);
CALL insert_alarm_ec('optical-amplifier-pump-bias-lo', 'OAPMPBL', 'Optical Amplifier pump bias low', 251);
CALL insert_alarm_ec('oa-pump-bias-cur-abn-warning', 'OAPBCWRN', 'Amp pump bias abnormal warning', 252);
CALL insert_alarm_ec('ne-els-time-out-of-sync', 'LMTMOS', 'Time Out of Sync with Node', 255);
CALL insert_alarm_ec('cold-reboot-required', 'COLDRR', 'Cold-reboot-required', 300);
CALL insert_alarm_ec('link-fail', 'LNKFAIL', 'Ethernet link failure', 301);
CALL insert_alarm_ec('link-down-cable-fault', 'LNKCBL', 'Link Down-Cable Fault', 302);
CALL insert_alarm_ec('link-down-cable-rmvd', 'LNKRMV', 'Link Down-Cable Removed', 303);
CALL insert_alarm_ec('link-down-autoneg-fail', 'LNKNEG', 'Link Down-Auto Negotiation Fail', 304);
CALL insert_alarm_ec('link-down-noroot-cause', 'LNKNORC', 'Link Down-No Root Cause', 305);
CALL insert_alarm_ec('fend-duplex-mode-unknown', 'FEDUPUNK', 'Far End Duplex Mode-Unknown', 306);
CALL insert_alarm_ec('jabber-thold-exced', 'JABTH', 'Jabber Threshold Exceeded', 307);
CALL insert_alarm_ec('loss-of-sync', 'LOSYNC', 'Loss of character Synchronization', 308);
CALL insert_alarm_ec('rx-local-fault', 'RXLFS', 'Rx Local Fault', 309);
CALL insert_alarm_ec('tx-local-fault', 'TXLFS', 'Tx Local Fault', 310);
CALL insert_alarm_ec('loss-of-block-lock', 'LOB', 'Loss of Block Lock', 311);
CALL insert_alarm_ec('hi-ber', 'HIBER', 'High BER detected', 312);
CALL insert_alarm_ec('loss-of-alignment', 'LOA', 'Loss of Alignment', 313);
CALL insert_alarm_ec('loss-of-block-lock-lane', 'LOBL', 'Loss of Block Lock lane', 314);
CALL insert_alarm_ec('loss-of-lane-alignment-marker-lane', 'LAML', 'Loss of Lane Alignment Marker Lane', 316);
CALL insert_alarm_ec('link-down-deactivated', 'LNKDACT', 'Link Down-Deactivated', 317);
CALL insert_alarm_ec('negotiated-bw-exceed', 'NEGBWEXD', 'Negotiated Bandwidth Exceeded', 318);
CALL insert_alarm_ec('rx-server-signal-fail', 'RXSSF', 'Rx Server Signal Fail', 319);
CALL insert_alarm_ec('tx-server-signal-fail', 'TXSSF', 'Tx Server Signal Fail', 320);
CALL insert_alarm_ec('loopback-active', 'LPBKACT', 'Loopback Active', 321);
CALL insert_alarm_ec('mea-phy-changed', 'PHYCHG', 'MEA Phy changed Alarm ', 322);
CALL insert_alarm_ec('ztp-in-progress', 'ZTPPRG', 'ZTP In Progress', 325);
CALL insert_alarm_ec('ztp-failed', 'ZTPFLD', 'ZTP Fail', 326);
CALL insert_alarm_ec('efm-remote-dying-gasp', 'DYINGASP', 'The port receives efm oam dying gasp from remote node', 356);
CALL insert_alarm_ec('efm-fail', 'EFMFAIL', 'The efm oam of the port fails', 357);
CALL insert_alarm_ec('efm-remote-critical-event', 'EFMRCE', 'The port receives efm oam remote critical event from remote node', 358);
CALL insert_alarm_ec('efm-remote-link-down', 'EFMRLD', 'The port receives efm oam remote link down from remote node', 359);
CALL insert_alarm_ec('efm-remote-loopback-fail', 'RLBFAIL', 'The port receives efm oam remote loopback fail information from remote node', 360);
CALL insert_alarm_ec('efm-remote-loopback-request', 'RLBREQ', 'The port request efm oam remote loopback successfully', 361);
CALL insert_alarm_ec('efm-remote-link-status', 'EFMRLS', 'The port receives efm oam remote link status fault information from remote node', 362);
CALL insert_alarm_ec('self-test-inprogress', 'SLFTST', 'Self-test in progress', 401);
CALL insert_alarm_ec('self-test-failed', 'SLFTSTFL', 'Self-test failed', 402);
CALL insert_alarm_ec('tamper', 'TAMPER', 'Card tampered', 403);
CALL insert_alarm_ec('battery-low', 'BATLOW', 'Battery low', 404);
CALL insert_alarm_ec('key-exchange-inprogress', 'KXINPRG', 'Key-exchange in progress', 410);
CALL insert_alarm_ec('key-exchange-degrade', 'KXDEG', 'Key-exchange degraded', 411);
CALL insert_alarm_ec('key-lifetime-expired', 'KXLFTF', 'Key Lifetime expired', 412);
CALL insert_alarm_ec('internal-encryption-fail', 'ENCFAIL', 'Internal Encryption failed', 413);
CALL insert_alarm_ec('key-exchange-auth-missing', 'AUTHMIS', 'Authentication password missing', 414);
CALL insert_alarm_ec('crypto-password-missing', 'CRYPWMIS', 'Crypto Password missing', 415);
CALL insert_alarm_ec('crypto-temporary-lockout', 'CRYPAUSE', 'Crypto Lockout', 416);
CALL insert_alarm_ec('key-exchange-channel-fail', 'KEXCHF', 'Key-exchange channel failure', 417);
CALL insert_alarm_ec('fingerprint-auth-missing', 'FPAUMIS', 'Key-exchange fingerprint authentication missing', 418);
CALL insert_alarm_ec('key-exchange-mode-mismatch', 'KXMDMIS', 'Key Exchange Mode Mismatch', 419);
CALL insert_alarm_ec('license-expires-in-28days', 'LEXP28D', 'License Expires in 28 Days', 430);
CALL insert_alarm_ec('license-expires-in-7days', 'LEXP7D', 'License Expires in 7 Days', 431);
CALL insert_alarm_ec('auto-cdc-in-progress', 'AUTOCDCP', 'Auto-CDC calibration in progress', 502);
CALL insert_alarm_ec('rx-not-operational-sequence', 'RXNOS', 'Rx Not-Operational Sequence', 522);
CALL insert_alarm_ec('ms-line-ais', 'MSAIS', 'Alarm Indication Signal', 551);
CALL insert_alarm_ec('excessive-error', 'EXC', 'Excessive Errors', 552);
CALL insert_alarm_ec('remote-defect-indication', 'RDI', 'Remote Defect Indication', 553);
CALL insert_alarm_ec('tca-loss-fast-deviation-hi', 'TLFDH', 'TCA reported when deviation loss observed during short/ fast period goes below fast deviation hight
                threshold
            ', 601);
CALL insert_alarm_ec('tca-medium-deviation-hi', 'TLMDH', 'TCA reported when deviation loss observed during medium long period goes below fast deviation hight threshold', 602);
CALL insert_alarm_ec('tca-slow-deviation-hi', 'TLSDH', 'TCA reported when deviation loss observed during slow i.e. long period goes below fast deviation hight threshold', 604);
CALL insert_alarm_ec('tca-link-loss-hi', 'TLLH', 'Link loss High', 605);
CALL insert_alarm_ec('tca-link-loss-warning-hi', 'TLLWH', 'Raised Link loss threshold falls below close-to-budget threhold', 606);
CALL insert_alarm_ec('destination-unresolved', 'DSTUNRE', 'Destionation unresolved', 701);
CALL insert_alarm_ec('no-active-route', 'NOACTRT', 'There is no IP interface, no route or no active route', 751);
CALL insert_alarm_ec('dmac2diptable-full', 'MACTBLFU', 'DMAC To DIP table is full', 752);
CALL insert_alarm_ec('remote-initiated-sat', 'RMTINIT', 'Remote Initiated SAT', 780);
CALL insert_alarm_ec('alarm-indication-signal-defect', 'DAIS', 'Alarm Indication Signal (AIS) Defect', 800);
CALL insert_alarm_ec('backward-defect-indicator', 'BDI', 'Backward Defect Indication', 801);
CALL insert_alarm_ec('backward-defect-indicator-payload', 'BDIP', 'Backward Defect Indication overhead', 802);
CALL insert_alarm_ec('backward-defect-indicator-overhead', 'BDIO', 'Backward Defect Indication payload', 803);
CALL insert_alarm_ec('client-signal-fail', 'CSF', 'Client Signal Fail', 804);
CALL insert_alarm_ec('degraded-signal', 'DEG', 'Degraded Signal', 805);
CALL insert_alarm_ec('forward-defect-indicator-payload', 'FDIP', 'Forward Defect Indicator Payload', 806);
CALL insert_alarm_ec('forward-defect-indicator-overhead', 'FDIO', 'Forward Defect Indicator Overhead', 807);
CALL insert_alarm_ec('incoming-alignment-error', 'DIAE', 'Incoming Alignment Error', 808);
CALL insert_alarm_ec('backward-incoming-alignment-error', 'DBIAE', 'Backward Incoming Alignment Error', 809);
CALL insert_alarm_ec('loss-of-frame', 'LOF', 'Loss of Frame', 810);
CALL insert_alarm_ec('loss-of-multiframe', 'LOM', 'Loss of Multiframe', 812);
CALL insert_alarm_ec('loss-of-frame-and-loss-of-multiframe', 'LOFLOM', 'Loss of Frame or Loss of Multiframe', 813);
CALL insert_alarm_ec('locked-condition', 'LCK', 'Administrative lock', 814);
CALL insert_alarm_ec('loss-of-tandem-connection', 'LTC', 'Loss of Tandem Connection', 815);
CALL insert_alarm_ec('multiplex-structure-identifier-mismatch', 'MSIM', 'Multiplex Structure Identifier Mismatch', 816);
CALL insert_alarm_ec('open-connection-indication', 'OCI', 'Open Connection Indication', 817);
CALL insert_alarm_ec('payload-mismatch', 'PLM', 'Payload mismatch', 818);
CALL insert_alarm_ec('payload-missing-indication', 'PMI', 'Payload Missing', 819);
CALL insert_alarm_ec('server-signal-fail', 'SSF', 'Server Signal Fail', 820);
CALL insert_alarm_ec('server-signal-fail-payload', 'SSFP', 'Server Signal Fail payload', 821);
CALL insert_alarm_ec('server-signal-fail-overhead', 'SSFO', 'Server Signal Fail overhead', 822);
CALL insert_alarm_ec('trail-signal-fail', 'TSF', 'The cTSF condition is raised in response to defects in the adaptation function. The AOS model
                merges the trail termination and adaptation functions into singular entities
            ', 823);
CALL insert_alarm_ec('trail-signal-fail-payload', 'TSFP', 'The cTSF-P condition is raised in response to defects related to the payload in the adaptation
                function. The AOS model merges the trail termination and adaptation functions into singular entities
            ', 824);
CALL insert_alarm_ec('trail-signal-fail-overhead', 'TSFO', 'The cTSF-O condition is raised in response to defects related to the overhead in the adaptation
                function. The AOS model merges the trail termination and adaptation functions into singular entities
            ', 825);
CALL insert_alarm_ec('trail-trace-identifier-mismatch', 'TIM', 'Trail Trace Identifier mismatch', 826);
CALL insert_alarm_ec('tx-alarm-indication-signal', 'TXAIS', 'Tx Alarm Indication Signal', 827);
CALL insert_alarm_ec('loss-of-signal-payload', 'LOSP', 'Loss of Signal payload', 828);
CALL insert_alarm_ec('loss-of-signal-overhead', 'LOSO', 'Loss of Signal OSC', 829);
CALL insert_alarm_ec('pump-end-of-life', 'PUMPEOL', 'Pump laser end of life warning', 830);
CALL insert_alarm_ec('midstage-loss-high', 'MLOSSHI', 'Mid stage loss high', 831);
CALL insert_alarm_ec('amp-ctrl-abnormal', 'CTRLANR', 'Abnormal amplifier control setting', 832);
CALL insert_alarm_ec('auto-power-shutdown', 'APS', 'Automatic power shutdown', 833);
CALL insert_alarm_ec('voa-ctrl-fail', 'VOACTRLF', 'Attenuator (VOA) Control Fail', 834);
CALL insert_alarm_ec('gain-ctrl-fail', 'GAINCTRF', 'Gain Control Fail', 835);
CALL insert_alarm_ec('tilt-ctrl-fail', 'TILTCTRF', 'Tilt Control Fail', 836);
CALL insert_alarm_ec('opt-limit-ht', 'OPTLMTHT', 'M1 high limit threshold', 837);
CALL insert_alarm_ec('opt-limit-ht-ex', 'OPTLMTEX', 'Optical Power Tx Limited', 838);
CALL insert_alarm_ec('osc-laser-fail', 'OSCLSRF', 'OSC laser failure', 839);
CALL insert_alarm_ec('osc-power-set', 'OSCPWRS', 'OSC optical power set failure', 840);
CALL insert_alarm_ec('loss-of-signal-midstage', 'LOSM', 'Midstage los', 841);
CALL insert_alarm_ec('loss-of-opu-multiframe-id', 'LOOMFI', 'Loss of OPU multiframe identifier', 842);
CALL insert_alarm_ec('pump-laser-temperature', 'PLSRTMP', 'Pump Temperature', 843);
CALL insert_alarm_ec('defect-open-connection-indication-source', 'DTXOCI', 'Open Connection Indication (OCI)', 844);
CALL insert_alarm_ec('defect-alarm-indication-signal-source', 'DTXAIS', 'Alarm Indication Signal (AIS)', 845);
CALL insert_alarm_ec('defect-administratively-locked-source', 'DTXLCK', 'Source Administratively Locked', 846);
CALL insert_alarm_ec('defect-odu-missing', 'DODUMISS', 'Defect ODU Missing', 847);
CALL insert_alarm_ec('intra-node-apr', 'INAPR', 'intra node apr', 848);
CALL insert_alarm_ec('loss-of-modem-sync', 'MSLOSS', 'Modem synchronization loss', 850);
CALL insert_alarm_ec('loss-of-coupling-alignment', 'CALOSS', 'Loss of modem coupling alignment', 851);
CALL insert_alarm_ec('auto-chromatic-dispersion-compensation-failure', 'CDCFAIL', 'Auto CD compensation failure', 852);
CALL insert_alarm_ec('hardware-abnormal', 'HWANR', 'Hardware Abnormal', 880);
CALL insert_alarm_ec('hardware-initializing', 'HWINIT', 'Hardware Initializing', 881);
CALL insert_alarm_ec('ocm-fault', 'OCMFLT', 'ocm-fault', 882);
CALL insert_alarm_ec('opr-damage', 'OPRDMG', 'OPR Damage', 883);
CALL insert_alarm_ec('opt-damage', 'OPTDMG', 'OPT Damage', 884);
CALL insert_alarm_ec('fiber-connection-attenuation-high', 'FCATTHI', 'Fiber Connection Attenutation High', 885);
CALL insert_alarm_ec('optical-power-transmit-signal-fail', 'OPTSF', 'Optical Power Transmit Signal Fail', 886);
CALL insert_alarm_ec('blocked-power-failure', 'BLKPWRF', 'Blocked Power Failure', 887);
CALL insert_alarm_ec('transmitter-structure-identifier-mismatch', 'TSIM', 'Transmitter Structure Identifier Mismatch', 888);
CALL insert_alarm_ec('adjust-process', 'ADJPROC', 'Adjust Process', 889);
CALL insert_alarm_ec('adjust-power', 'ADJPWR', 'Adjust Power', 890);
CALL insert_alarm_ec('link-down', 'LNKDWN', 'PPP LCP failure', 901);
CALL insert_alarm_ec('lockout-condition-identity', 'LOCKOUT', 'Lockout', 911);
CALL insert_alarm_ec('force-switch-condition-identity', 'FRSWTCH', 'Force Switch', 912);
CALL insert_alarm_ec('manual-switch-condition-identity', 'MANSWTCH', 'Manual Switch', 913);
CALL insert_alarm_ec('wait-to-restore-condition-identity', 'WTR', 'Wait to Restore', 914);
CALL insert_alarm_ec('no-mgroup-resource', 'NMGROUPR', 'No multicast group resource', 922);
CALL insert_alarm_ec('remote-rdi-condition-identity', 'CFMRDI', 'Some RDI', 1000);
CALL insert_alarm_ec('remote-mac-error-condition-identity', 'RMACERR', 'Some MAC Status', 1001);
CALL insert_alarm_ec('remote-invalid-ccm-condition-identity', 'RINVCCM', 'Some Remote CCM', 1002);
CALL insert_alarm_ec('invalid-ccm-condition-identity', 'INVCCM', 'Error CCM', 1003);
CALL insert_alarm_ec('cross-connect-ccm-condition-identity', 'XCONCCM', 'Cross Connect CCM', 1004);
CALL insert_alarm_ec('eth-ais-condition-identity', 'ETHAIS', 'ETH-AIS', 1005);
CALL insert_alarm_ec('eth-lock-condition-identity', 'ETHLCK', 'ETH-LOCK', 1006);
CALL insert_alarm_ec('no-peer-condition-identity', 'NOPEER', 'no-peer-condition-identity', 1020);
CALL insert_alarm_ec('provision-fail-condition-identity', 'PROVFAIL', 'provision-fail-condition-identity', 1021);
CALL insert_alarm_ec('config-fail-condition-identity', 'CFGFAIL', 'config-fail-condition-identity', 1022);
CALL insert_alarm_ec('failure-of-protocol-provision-mismatch', 'FOPPM', 'FOP - Provision Mismatch', 1201);
CALL insert_alarm_ec('failure-of-protocol-timeout', 'FOPTO', 'FOP - Timeout', 1202);
CALL insert_alarm_ec('blockport0-rpl', 'BLK0RPL', 'ERP-BLOCKPORT0-RPL', 1203);
CALL insert_alarm_ec('blockport0-sf', 'BLK0SF', 'ERP-BLOCKPORT0-SF', 1204);
CALL insert_alarm_ec('blockport0-ms', 'BLK0MS', 'ERP-BLOCKPORT0-MS', 1205);
CALL insert_alarm_ec('blockport0-fs', 'BLK0FS', 'ERP-BLOCKPORT0-FS', 1206);
CALL insert_alarm_ec('blockport0-wtr', 'BLK0WTR', 'ERP-BLOCKPORT0-WTR', 1207);
CALL insert_alarm_ec('blockport1-rpl', 'BLK1RPL', 'ERP-BLOCKPORT1-RPL', 1208);
CALL insert_alarm_ec('blockport1-sf', 'BLK1SF', 'ERP-BLOCKPORT1-SF', 1209);
CALL insert_alarm_ec('blockport1-ms', 'BLK1MS', 'ERP-BLOCKPORT1-MS', 1210);
CALL insert_alarm_ec('blockport1-fs', 'BLK1FS', 'ERP-BLOCKPORT1-FS', 1211);
CALL insert_alarm_ec('blockport1-wtr', 'BLK1WTR', 'ERP-BLOCKPORT1-WTR', 1212);
CALL insert_alarm_ec('vm-crash', 'CRASH', 'Virutual Machine Crash Alarm', 1301);
CALL insert_alarm_ec('vm-resume-failed', 'RSUMFLD', 'Virutual Machine Resume Failed Alarm', 1302);
CALL insert_alarm_ec('tca-red-frame-drop', 'RFDROP', 'Number of red frame dropped', 1512);
CALL insert_alarm_ec('tca-eth-packet-sent', 'ETHTXPKT', 'Number of frames transmitted', 1513);
CALL insert_alarm_ec('tca-eth-packet-rcvd', 'ETHRXPKT', 'Number of frames received', 1514);
CALL insert_alarm_ec('ql-invalid', 'QLINVLD', 'QL Invalid', 1601);
CALL insert_alarm_ec('ql-mismatch', 'QLMISMT', 'QL Mismatch', 1602);
CALL insert_alarm_ec('timing-source-locked-out', 'TSLCKOUT', 'timing-source-locked-out', 1603);
CALL insert_alarm_ec('timing-source-forced-switch', 'TSFS', 'Sync Reference Forced Switch', 1604);
CALL insert_alarm_ec('timing-source-manual-switch', 'TSMS', 'Sync Reference Manual Switch', 1605);
CALL insert_alarm_ec('timing-source-wtr', 'TSWTR', 'Sync Reference WTR', 1606);
CALL insert_alarm_ec('syncref-failed', 'SYNCFAIL', 'Sync Reference Failed', 1607);
CALL insert_alarm_ec('frequency-offset', 'FREQOFF', 'Frequency Offset', 1608);
CALL insert_alarm_ec('squelch', 'SQUELCH', 'Squelched', 1609);
CALL insert_alarm_ec('ais', 'AIS', 'AIS', 1611);
CALL insert_alarm_ec('bits-lof', 'BITSLOF', 'Loss of Frame', 1612);
CALL insert_alarm_ec('all-sync-refs-failed', 'ALLREFF', 'All Sync References Failed', 1620);
CALL insert_alarm_ec('free-run', 'FREERUN', 'Freerun', 1621);
CALL insert_alarm_ec('fast-acquisition', 'FASTACQ', 'Fast Acquisition', 1622);
CALL insert_alarm_ec('holdover', 'HOLDOVER', 'Holdover', 1623);
CALL insert_alarm_ec('loss-of-lock', 'LOSSOFLK', 'Loss of Lock', 1624);
CALL insert_alarm_ec('avg-holdover-freq-not-ready', 'FREQNRD', 'Average Holdover Frequency Not Ready', 1625);
CALL insert_alarm_ec('no-satellite-received', 'NOSATRCV', 'No statellite received', 1630);
CALL insert_alarm_ec('gnss-antenna-alarm', 'ANTENALM', 'GNSS Antenna Alarm', 1631);
CALL insert_alarm_ec('pps-not-generated', 'PPSNOGEN', 'PPS Not Generated', 1632);
CALL insert_alarm_ec('gnss-firmware-upgrading', 'FMUPG', 'GNSS Firmware Updating', 1633);
CALL insert_alarm_ec('time-clock-not-locked', 'UNLOCK', 'Not Locked', 1650);
CALL insert_alarm_ec('time-not-traceable', 'TIMNOTT', 'Time not traceable', 1651);
CALL insert_alarm_ec('time-holdover', 'TIMHLDV', 'Time Holdover', 1653);
CALL insert_alarm_ec('timeref-unavailable', 'TIMENA', 'Time Reference Unavailable', 1662);
CALL insert_alarm_ec('timeref-unavailable-wtr', 'TIMNAWTR', 'Time Reference Unavailable WTR', 1663);
CALL insert_alarm_ec('timeref-degraded', 'TIMEDEG', 'Time Reference Switched', 1664);
CALL insert_alarm_ec('timeref-degraded-wtr', 'DEGWTR', 'Time Degraded WTR', 1665);
CALL insert_alarm_ec('time-freerun', 'TIMEFRUN', 'Time Freerun', 1670);
CALL insert_alarm_ec('freq-freerun', 'FREQFRUN', 'Frequency Freerun', 1671);
CALL insert_alarm_ec('freq-holdover', 'FREQHDOV', 'Frequency Holdver', 1672);
CALL insert_alarm_ec('freq-not-tracebale', 'FREQNTRA', 'Frequency Not Traceable', 1673);
CALL insert_alarm_ec('announce-timeout', 'ANNTO', 'Announce-timeout', 1680);
CALL insert_alarm_ec('sync-timeout', 'SYNCTO', 'Sync Timeout', 1681);
CALL insert_alarm_ec('delay-resp-timeout', 'DLRPTO', 'Delay Response Timeout', 1682);
CALL insert_alarm_ec('multiple-peers', 'MULTPEER', 'Multiple Peers', 1683);
CALL insert_alarm_ec('wrong-domain', 'XDOM', 'Wrong Domain', 1684);
CALL insert_alarm_ec('no-traffic-flowpoint', 'NOFP', 'Ptp port has not accociated to a traffic flow point', 1685);
CALL insert_alarm_ec('esmc-fail', 'ESMCFAIL', 'ESMC Failure', 1701);
CALL insert_alarm_ec('linkdown-masterslave-cfg', 'LKDNMSCF', 'Linkdown Master Slave Configuration', 1720);
CALL insert_alarm_ec('auto-masterslave-cfg', 'AUTOMSCF', 'Auto Negotiation Master Salve Configuration', 1721);
CALL insert_alarm_ec('tca-average-offset-from-master', 'TAVGOFM', 'TCA of Average offset from master(ns)', 1821);
CALL insert_alarm_ec('tca-minimum-offset-from-master', 'TMINOFM', 'TCA of Minimum offset from master(ns)', 1822);
CALL insert_alarm_ec('tca-maximum-offset-from-master', 'TMAXOFM', 'TCA of Maximum offset from master(ns)', 1823);
CALL insert_alarm_ec('tca-average-sync-path-delay', 'TAVGSPD', 'TCA of Average sync path delay(ns)', 1824);
CALL insert_alarm_ec('tca-minimum-sync-path-delay', 'TMINSPD', 'TCA of Minimum sync path delay(ns)', 1825);
CALL insert_alarm_ec('tca-maximum-sync-path-delay', 'TMAXSPD', 'TCA of Maximum sync path delay(ns)', 1826);
CALL insert_alarm_ec('tca-average-mean-path-delay', 'TAVGMPD', 'TCA of Average mean path delay(ns)', 1827);
CALL insert_alarm_ec('tca-minimum-mean-path-delay', 'TMINMPD', 'TCA of Minimum mean path delay(ns)', 1828);
CALL insert_alarm_ec('tca-maximum-mean-path-delay', 'TMAXMPD', 'TCA of Maximum mean path delay(ns)', 1829);
CALL insert_alarm_ec('tca-announce-rx', 'TRXANNCE', 'TCA of PTP Announce Messages Received', 1841);
CALL insert_alarm_ec('tca-announce-tx', 'TTXANNCE', 'TCA of PTP Announce Messages Transmitted', 1842);
CALL insert_alarm_ec('tca-sync-rx', 'TRXSYNC', 'TCA of PTP Sync Messages Received', 1843);
CALL insert_alarm_ec('tca-sync-tx', 'TTXSYNC', 'TCA of PTP Sync Messages Transmitted', 1844);
CALL insert_alarm_ec('tca-follow-up-rx', 'TRXFOLUP', 'TCA of PTP Follow_Up Messages Received', 1845);
CALL insert_alarm_ec('tca-follow-up-tx', 'TTXFOLUP', 'TCA of PTP Follow_Up Messages Transmitted', 1846);
CALL insert_alarm_ec('tca-delay-req-rx', 'TRXDREQ', 'TCA of PTP Delay_Req Messages Received', 1847);
CALL insert_alarm_ec('tca-delay-req-tx', 'TTXDREQ', 'TCA of PTP Delay_Req Messages Transmitted', 1848);
CALL insert_alarm_ec('tca-delay-resp-rx', 'TRXDRSP', 'TCA of PTP Delay_Resp Messages Received', 1849);
CALL insert_alarm_ec('tca-delay-resp-tx', 'TTXDRSP', 'TCA of PTP Delay_Resp Messages Transmitted', 1850);
CALL insert_alarm_ec('tca-pdelay-req-rx', 'TRXPDRQ', 'TCA of PTP Pdelay_Req Messages Received', 1851);
CALL insert_alarm_ec('tca-pdelay-req-tx', 'TTXPDRQ', 'TCA of PTP Pdelay_Req Messages Transmitted', 1852);
CALL insert_alarm_ec('tca-pdelay-resp-rx', 'TRXPDRSP', 'TCA of PTP Pdelay_Resp Messages Received', 1853);
CALL insert_alarm_ec('tca-pdelay-resp-tx', 'TTXPDRSP', 'TCA of PTP Pdelay_Resp Messages Transmitted', 1854);
CALL insert_alarm_ec('tca-pdelay-resp-follow-up-rx', 'TRXPDUP', 'TCA of PTP Pdelay_Resp_Follow_Up Messages Received', 1855);
CALL insert_alarm_ec('tca-pdelay-resp-follow-up-tx', 'TTXPDUP', 'TCA of PTP Pdelay_Resp_Follow_Up Messages Transmitted', 1856);
CALL insert_alarm_ec('tca-signaling-rx', 'TRXSGNL', 'TCA of PTP Signaling Messages Received', 1857);
CALL insert_alarm_ec('tca-signaling-tx', 'TTXSGNL', 'TCA of PTP Signaling Messages Transmitted', 1858);
CALL insert_alarm_ec('tca-management-rx', 'TRXMGMT', 'TCA of PTP Management Messages Received', 1859);
CALL insert_alarm_ec('tca-management-tx', 'TTXMGMT', 'TCA of PTP Management Messages Transmitted', 1860);
CALL insert_alarm_ec('tca-ptp-unknown-rx', 'TRXUKN', 'TCA of PTP Unknown Messages Received', 1861);
CALL insert_alarm_ec('tca-ptp-unknown-tx', 'TTXUKN', 'TCA of PTP Unknown Messages Transmitted', 1862);
CALL insert_alarm_ec('tca-average-residence-time-sync', 'TAVGRTS', 'TCA of Average Residence Time For PTP Sync Messages(ns)', 1863);
CALL insert_alarm_ec('tca-maximum-residence-time-sync', 'TMAXRTS', 'TCA of Maximum Residence Time For PTP Sync Messages(ns)', 1864);
CALL insert_alarm_ec('tca-minimum-residence-time-sync', 'TMINRTS', 'TCA of Minimum Residence Time For PTP Sync Messages(ns)', 1865);
CALL insert_alarm_ec('tca-average-residence-time-delay-req', 'TAVGRTDR', 'TCA of Average Residence Time For PTP Delay_Req Messages(ns)', 1866);
CALL insert_alarm_ec('tca-maximum-residence-time-delay-req', 'TMAXRTDR', 'TCA of Maximum Residence Time For PTP Delay_Req Messages(ns)', 1867);
CALL insert_alarm_ec('tca-minimum-residence-time-delay-req', 'TMINRTDR', 'TCA of Minimum Residence Time For PTP Delay_Req Messages(ns)', 1868);
CALL insert_alarm_ec('tca-average-residence-time-pdelay-req', 'TAVGRTPR', 'TCA of Average Residence Time For PTP Pdelay_Req Messages(ns)', 1869);
CALL insert_alarm_ec('tca-maximum-residence-time-pdelay-req', 'TMAXRTPR', 'TCA of Maximum Residence Time For PTP Pdelay_Req Messages(ns)', 1870);
CALL insert_alarm_ec('tca-minimum-residence-time-pdelay-req', 'TMINRTPR', 'TCA of Minimum Residence Time For PTP Pdelay_Req Messages(ns)', 1871);
CALL insert_alarm_ec('tca-average-residence-time-pdelay-resp', 'TAVGRTPP', 'TCA of Average Residence Time For PTP Pdelay_Resp Messages(ns)', 1872);
CALL insert_alarm_ec('tca-maximum-residence-time-pdelay-resp', 'TMAXRTPP', 'TCA of Maximum Residence Time For PTP Pdelay_Resp Messages(ns)', 1873);
CALL insert_alarm_ec('tca-minimum-residence-time-pdelay-resp', 'TMINRTPP', 'TCA of Minimum Residence Time For PTP Pdelay_Resp Messages(ns)', 1874);
CALL insert_alarm_ec('tca-average-announce-message-rate', 'TAVGANRT', 'TCA of Average rate of announce message', 1875);
CALL insert_alarm_ec('tca-average-sync-message-rate', 'TAVGSYNRT', 'TCA of Average rate of sync message', 1876);
CALL insert_alarm_ec('tca-average-delay-req-message-rate', 'TAVGDRRT', 'TCA of Average rate of delay-request message', 1877);
CALL insert_alarm_ec('tca-average-delay-resp-message-rate', 'TAVGDPRT', 'TCA of Average rate of delay-response message', 1878);
CALL insert_alarm_ec('tca-tag-mismatched-discard', 'TXTAGDIS', 'TCA of Discarded message duo to tag not matchning', 1879);
CALL insert_alarm_ec('tca-domain-mismatch-discard', 'TXDOMDIS', 'TCA of Discarded message duo to domain number not matchning', 1880);
CALL insert_alarm_ec('tca-wrong-type-discard', 'TWRGTYPE', 'TCA of Discarded message duo to wrong type', 1881);
CALL insert_alarm_ec('tca-wrong-length-discard', 'TXLENDIS', 'TCA of Discarded message duo to wrong length', 1882);
CALL insert_alarm_ec('tca-unknown-master-discard', 'TXUKMD', 'TCA of Discarded message duo to unknown master', 1883);
CALL insert_alarm_ec('tca-misc-discard', 'TMISCDIS', 'TCA of Discarded message duo to other miscellous reason', 1884);
CALL insert_alarm_ec('tca-output-current-hi', 'TCURRH', 'TCA Output Current High', 2005);
CALL insert_alarm_ec('tca-power-consumption-hi', 'TPWRH', 'TCA power high', 2008);
CALL insert_alarm_ec('tca-temperature-hi', 'TTEMPH', 'TCA temperature high', 2011);
CALL insert_alarm_ec('tca-output-power-hi', 'TOPWRH', 'TCA output power high', 2015);
CALL insert_alarm_ec('tca-opt-rcv-pwr-hi', 'TOPRH', 'TCA optical power receive high', 2211);
CALL insert_alarm_ec('tca-opt-trmt-pwr-hi', 'TOPTH', 'TCA optical power transmit high', 2214);
CALL insert_alarm_ec('tca-opt-laser-bias-cur-hi', 'TLBCH', 'TCA Laser Bias Current High', 2215);
CALL insert_alarm_ec('tca-laser-temperature-hi', 'TLSRTMPH', 'TCA Laser Temperature High', 2220);
CALL insert_alarm_ec('tca-out-of-frame-second-hi', 'TOOFSH', 'TCA out-of-frame seconds high', 2551);
CALL insert_alarm_ec('tca-unavailable-seconds-hi', 'TUASH', 'TCA unavailable seconds high', 2803);
CALL insert_alarm_ec('tca-errored-second-hi', 'TESH', 'TCA errored seconds high', 2804);
CALL insert_alarm_ec('tca-severely-errored-second-hi', 'TSESH', 'TCA severily errored seconds high', 2805);
CALL insert_alarm_ec('tca-background-block-errors-hi', 'TBBEH', 'TCA background block erros high', 2806);
CALL insert_alarm_ec('tca-osc-opt-laser-bias-cur-hi', 'TOSCLBCH', 'TCA OSC laser bias current high', 2826);
CALL insert_alarm_ec('tca-osc-laser-temperature-hi', 'TOSCLTH', 'TCA OSC laser temperature high', 2827);
CALL insert_alarm_ec('tca-osc-opt-rcv-pwr-hi', 'TOSCOPRH', 'TCA OSC optical power receive high', 2828);
CALL insert_alarm_ec('tca-osc-opt-tmt-pwr-hi', 'TOSCOPTH', 'TCA OSC optical power transmit high', 2832);
CALL insert_alarm_ec('tca-ses-payload-hi', 'TSESPH', 'TCA severily errored seconds payload high', 2836);
CALL insert_alarm_ec('tca-ses-overhead-hi', 'TSESOH', 'TCA severily errored seconds overhead high', 2837);
CALL insert_alarm_ec('tca-uas-payload-hi', 'TUASPH', 'TCA unavailable seconds payload high', 2838);
CALL insert_alarm_ec('tca-uas-overhead-hi', 'TUASOH', 'TCA unavailable seconds overhead high', 2839);
CALL insert_alarm_ec('tca-differential-group-delay-hi', 'TDGDH', 'TCA diff. group delay high', 2850);
CALL insert_alarm_ec('tca-chromatic-dispersion-compensation-hi', 'TCDCH', 'TCA chromatic dispersion high', 2852);
CALL insert_alarm_ec('tca-carrier-frequency-offset-hi', 'TCFOTH', 'TCA carrier frequency offset high', 2853);
CALL insert_alarm_ec('wearout-level-warning', 'WEAROUT', 'Smart storage disk wear out warning', 3001);
CALL insert_alarm_ec('encrypted-storage-mismatch', 'ENCRYMEA', 'Smart storage encryption mismatch', 3002);
CALL insert_alarm_ec('tca-opt-rcv-pwr-lo', 'TOPRL', 'TCA optical power receive low', 4211);
CALL insert_alarm_ec('tca-opt-trmt-pwr-lo', 'TOPTL', 'TCA optical power transmit low', 4214);
CALL insert_alarm_ec('tca-laser-temperature-lo', 'TLSRTMPL', 'TCA Laser Temperature Low', 4220);
CALL insert_alarm_ec('tca-osc-laser-temperature-lo', 'TOSCLTL', 'TCA OSC laser temperature low', 4827);
CALL insert_alarm_ec('tca-osc-opt-rcv-pwr-lo', 'TOSCOPRL', 'TCA OSC optical power receive low', 4828);
CALL insert_alarm_ec('tca-osc-opt-tmt-pwr-lo', 'TOSCOPTL', 'TCA OSC optical power transmit low', 4832);
CALL insert_alarm_ec('tca-signal-to-noise-ratio-lo', 'TSNRL', 'TCA signal-to-noise ratio low', 4851);
CALL insert_alarm_ec('tca-chromatic-dispersion-compensation-lo', 'TCDCL', 'TCA chromatic dispersion low', 4852);
CALL insert_alarm_ec('tca-carrier-frequency-offset-lo', 'TCFOTL', 'TCA carrier frequency offset low', 4853);
CALL insert_alarm_ec('tca-q-factor-lo', 'TQFACTL', 'TCA Q-factor low', 4866);
CALL insert_alarm_ec('tca-polarization-dependent-loss-hi', 'TPDLH', 'TCA Polarization Dependent Loss High', 4867);
CALL insert_alarm_ec('tca-state-of-polarization-change-rate-hi', 'TSOPTH', 'TCA state-of-polarization-tracking-rate high', 4868);
CALL insert_alarm_ec('tca-optical-signal-to-noise-ratio-lo', 'TOSNRL', 'TCA Optical Signal to Noise Ratio Low', 4869);
CALL insert_alarm_ec('db-mirror-unavailable', 'DBMRUAV', 'DB mirror unavailable', 5102);
CALL insert_alarm_ec('db-mirror-incompatible-sw', 'DBMRINSW', 'DB mirror incompatible SW', 5103);
CALL insert_alarm_ec('db-mirror-failure', 'DBMRF', 'DB mirror failure', 5104);
CALL insert_alarm_ec('failure-of-protocol-no-response', 'FOPNR', 'FOP - No Response', 5107);
CALL insert_alarm_ec('failure-of-protocol-configuration-mismatch', 'FOPCM', 'FOP - Channel Mismatch', 5108);
CALL insert_alarm_ec('failure-of-protocol-aps-enable-mismatch', 'FOPAM', 'FOP - APS Enable Mismatch', 5110);
CALL insert_alarm_ec('failure-of-protocol-dirn-mismatch', 'FOPSM', 'FOP - Direction Mismatch', 5111);
CALL insert_alarm_ec('failure-of-protocol-bridge-type-mismatch', 'FOPBM', 'FOP - Bridge Type Mismatch', 5112);
CALL insert_alarm_ec('failure-of-protocol-switch-revertive-mismatch', 'FOPOM', 'FOP - Switch Revertive Mismatch', 5113);
CALL insert_alarm_ec('raman-characterization-fault', 'RCF', 'Raman Characterization Fault', 5114);
CALL insert_alarm_ec('raman-characterization-support', 'RCS', 'Raman Characterization Support', 5115);
CALL insert_alarm_ec('key-exchange-config-mismatch', 'KXCFGMIS', 'Key-exchange config mismatch', 5116);
CALL insert_alarm_ec('aps-path-defect', 'APSPATH', 'aps-path-defect', 5118);
CALL insert_alarm_ec('host-unreachable', 'HSTUNRCH', 'Host Unreachable Alarm', 5119);
CALL insert_alarm_ec('resource-busy', 'RSCBUSY', 'Resource Busy Alarm', 5120);
CALL insert_alarm_ec('protection-command-applied', 'PCA', 'Protection Command Applied', 5121);
CALL insert_alarm_ec('remote-server-unreachable', 'RMTSUNV', 'Remote auth server unreachable', 5122);
CALL insert_alarm_ec('component-temperature-high', 'CTEMPH', 'Component Temperature High', 5123);
CALL insert_alarm_ec('ntp-loss-of-server', 'NTPLOSS', 'NTP Loss of Server', 5124);
CALL insert_alarm_ec('adjust-in-progress', 'ADJIP', 'Adjust In Progress', 5125);
CALL insert_alarm_ec('target-address-unreachable', 'TGTUNV', 'SNMP target address unreachable', 5126);
CALL insert_alarm_ec('callhome-server-unreachable', 'CLUNRCH', 'Call Home server unreachable', 5127);
CALL insert_alarm_ec('raman-characterization-active', 'RCA', 'Raman Characterization Active', 5129);
CALL insert_alarm_ec('opt-limit-high-threshold', 'OPTLHT', 'Remote Optical Limit High', 5130);
CALL insert_alarm_ec('tca-dropped-ttl', 'TDROPTTL', 'TCA Dropped TTL', 5131);
CALL insert_alarm_ec('tca-dropped-no-route', 'TDROPNR', 'TCA Dropped No Route', 5132);
CALL insert_alarm_ec('tca-dropped-fragmented', 'TDROPFRA', 'TCA Dropped Fragmented', 5133);
CALL insert_alarm_ec('tca-membership-fail-counts', 'TMBFAIL', 'TCA Membership Fail Counts', 5134);
CALL insert_alarm_ec('tca-control-packets-trapped', 'TCTRAPS', 'TCA Control Packets Trapped', 5135);
CALL insert_alarm_ec('tca-dhcp-packets-dropped', 'TDHCPDRP', 'TCA DHCP Packets Dropped', 5136);
CALL insert_alarm_ec('arp-table-full', 'ARPFULL', 'ARP Table Full', 5137);
CALL insert_alarm_ec('ip-address-conflict', 'IPCFLCT', 'IP Address Conflict', 5138);
CALL insert_alarm_ec('ip-interface-outage', 'IPIFOUT', 'IP Interface Outage', 5139);
CALL insert_alarm_ec('no-route-resource', 'NORTRSC', 'No route resource', 5140);
CALL insert_alarm_ec('wss-fault', 'WSSFLT', 'WSS Fault', 5141);
CALL insert_alarm_ec('bgp-link-down', 'BLD', 'BGP Link Down', 5142);
CALL insert_alarm_ec('otu4-fec-not-apply', 'FECNAPPL', 'Fec type not applicable', 5147);
CALL insert_alarm_ec('accessible-emission-limit', 'AEL', 'accessable emission limit', 5148);
CALL insert_alarm_ec('accessible-emission-limit-reduce', 'AELRDUCE', 'Accessable emission limit reduce', 5149);
CALL insert_alarm_ec('accessible-emission-limit-reduce-exceeded', 'AELREDUX', 'Raised when equipment attemts to control edfa power have not reduced power within M1 safety limits. This
                indication shall result in an APS shutdown of the edfa. This indication is used as a defect and is not presented
                as a condition or alarm to the user interface. Instead it is an input to theAPS action and alarm
            ', 5150);
CALL insert_alarm_ec('tca-frame-marked-green', 'TFMG', 'TCA for frame marked green', 5151);
CALL insert_alarm_ec('tca-frame-marked-yellow', 'TFMY', 'TCA for frame marked yellow', 5152);
CALL insert_alarm_ec('timing-license-missing', 'TIMLM', 'timing-license-missing', 5153);
CALL insert_alarm_ec('eompls-license-missing', 'EOMPLSLM', 'eompls-license-missing', 5154);
CALL insert_alarm_ec('full-capability-license-missing', 'FCLM', 'full-capability-license-missing', 5155);
CALL insert_alarm_ec('elephant-flow-license-missing', 'EFLM', 'elephant-flow-license-missing', 5156);
CALL insert_alarm_ec('tca-average-bits-rate-passed', 'TAVGBRP', 'tca-average-bits-rate-passed', 5157);
CALL insert_alarm_ec('tca-bytes-passed', 'TBPASS', 'tca-bytes-passed', 5158);
CALL insert_alarm_ec('tca-bytes-dropped', 'TBDROP', 'tca-bytes-dropped', 5159);
CALL insert_alarm_ec('tca-frame-passed', 'TFPASS', 'tca-frame-passed', 5160);
CALL insert_alarm_ec('tca-frame-dropped', 'TFDROP', 'tca-frame-dropped', 5161);
CALL insert_alarm_ec('control-plane-license-missing', 'CPLM', 'control-plane-license-missing', 5162);
CALL insert_alarm_ec('l3-license-missing', 'L3LM', 'l3-license-missing', 5163);
CALL insert_alarm_ec('pim-license-missing', 'PIMLM', 'pim-license-missing', 5164);
CALL insert_alarm_ec('segment-routing-license-missing', 'SRLM', 'segment-routing-license-missing', 5165);
CALL insert_alarm_ec('source-ip-address-unavailable', 'SIPUNA', 'Source IP Address Unavailable', 5166);
CALL insert_alarm_ec('sf-working-pds', 'SFPDSWK', 'Signal Fail Working PDS', 5169);
CALL insert_alarm_ec('sf-protection-pds', 'SFPDSPT', 'Signal Fail Protection PDS', 5170);
CALL insert_alarm_ec('sd-working-pds', 'SDPDSWK', 'Signal Degrade Working PDS', 5171);
CALL insert_alarm_ec('sd-protection-pds', 'SDPDSPT', 'Signal Degrade Protection PDS', 5172);
CALL insert_alarm_ec('oppm-aid-mismatch', 'OAIDM', 'OPPM AID Mismatch (e/w)', 5173);
CALL insert_alarm_ec('pilot-peer-comm-fail', 'PLCOMMF', 'Communication failed to peer module holding used pilot signal', 5174);
CALL insert_alarm_ec('component-temperature-high-shutdown', 'CTEMPSD', 'Component Temperature Shutdown', 5187);
CALL insert_alarm_ec('component-temperature-high-warning', 'CTEMPW', 'Component Temperature High Warning', 5188);
CALL insert_alarm_ec('peer-entity-removed', 'PERMV', 'Protection Peer Removed', 5190);
CALL insert_alarm_ec('peer-entity-comm-fail', 'PECOMMF', 'Protection Peer Communication Fail', 5191);
CALL insert_alarm_ec('key-exchange-stopped', 'KYEXST', 'Condition is raised when key exchange is stopped', 5196);
CALL insert_alarm_ec('tca-in-pkts-untagged', 'TINPKTUN', 'TCA of connect guard monitored types', 5197);
CALL insert_alarm_ec('tca-in-pkts-no-tag', 'TINPKTNO', 'TCA of connect guard monitored types', 5198);
CALL insert_alarm_ec('tca-in-pkts-bad-tag', 'TINPKBTG', 'TCA of connect guard monitored types', 5199);
CALL insert_alarm_ec('tca-in-pkts-no-sa', 'TINPKNSA', 'TCA of connect guard monitored types', 5200);
CALL insert_alarm_ec('authen-fail', 'PAFAIL', 'Port authentication failed', 5201);
CALL insert_alarm_ec('tca-in-pkts-overrun', 'TINPKOVR', 'TCA of connect guard monitored types', 5202);
CALL insert_alarm_ec('tca-in-octets-validated', 'TINOCTVA', 'TCA of connect guard monitored types', 5203);
CALL insert_alarm_ec('tca-in-octets-decrypted', 'TINOCTDE', 'TCA of connect guard monitored types', 5204);
CALL insert_alarm_ec('tca-out-pkts-untagged', 'TOUTPKUN', 'TCA of connect guard monitored types', 5205);
CALL insert_alarm_ec('tca-out-pkts-too-long', 'TOUPKTOL', 'TCA of connect guard monitored types', 5206);
CALL insert_alarm_ec('tca-out-octets-protected', 'TOUTOCPR', 'TCA of connect guard monitored types', 5207);
CALL insert_alarm_ec('tca-out-octets-encrypted', 'TOUTOCEN', 'TCA of connect guard monitored types', 5208);
CALL insert_alarm_ec('tca-out-pkts-protected', 'TOUTPKPR', 'TCA of connect guard monitored types', 5209);
CALL insert_alarm_ec('tca-out-pkts-encrypted', 'TOUTPKEN', 'TCA of connect guard monitored types', 5210);
CALL insert_alarm_ec('tca-in-pkts-ok', 'TINPKTOK', 'TCA of connect guard monitored types', 5211);
CALL insert_alarm_ec('tca-in-pkts-unchecked', 'TINPKUNC', 'TCA of connect guard monitored types', 5212);
CALL insert_alarm_ec('tca-in-pkts-delayed', 'TINPKTDE', 'TCA of connect guard monitored types', 5213);
CALL insert_alarm_ec('tca-in-pkts-late', 'TINPKTLA', 'TCA of connect guard monitored types', 5214);
CALL insert_alarm_ec('tca-in-pkts-invalid', 'TINPKINV', 'TCA of connect guard monitored types', 5215);
CALL insert_alarm_ec('tca-in-pkts-not-valid', 'TINPKNVA', 'TCA of connect guard monitored types', 5216);
CALL insert_alarm_ec('tca-in-pkts-no-sa-error', 'TINPKNSE', 'TCA of connect guard monitored types', 5217);
CALL insert_alarm_ec('tca-span-loss-rcv-hi', 'TSPLRH', 'TCA span loss high', 5218);
CALL insert_alarm_ec('tca-span-loss-rcv-lo', 'TSPLRL', 'TCA span loss low', 5219);
CALL insert_alarm_ec('pds-sf-indication', 'PDSSFI', 'pds-sf-indication', 5220);
CALL insert_alarm_ec('pds-sd-indication', 'PDSSDI', 'pds-sd-indication', 5221);
CALL insert_alarm_ec('crypto-config-error', 'CRYCONER', 'Crypto Configuration error', 5222);
CALL insert_alarm_ec('packet-number-rollover', 'PKTNUMRO', 'Condition is raised when packet number rolls over', 5223);
CALL insert_alarm_ec('vpn-prefix-execeed', 'VPNPREXE', 'vpn-prefix-execeed', 5224);
CALL insert_alarm_ec('overload-level-1', 'OL1', 'overload-level-1', 5225);
CALL insert_alarm_ec('overload-level-2', 'OL2', 'overload-level-2', 5226);
CALL insert_alarm_ec('tca-frame-assembly-ok', 'TFASOK', 'tca-frame-assembly-ok', 5227);
CALL insert_alarm_ec('tca-frame-assembly-err', 'TFASERR', 'tca-frame-assembly-err', 5228);
CALL insert_alarm_ec('tca-frame-smd-err', 'TFSMDERR', 'tca-frame-smd-err', 5229);
CALL insert_alarm_ec('tca-rx-frag', 'TEXPRXF', 'tca-rx-frag', 5230);
CALL insert_alarm_ec('tca-tx-frag', 'TEXPTXF', 'tca-tx-frag', 5231);
CALL insert_alarm_ec('tca-hold', 'TEXPHOLD', 'tca-hold', 5232);
CALL insert_alarm_ec('tca-broadcast-frames-discarded', 'TBRFRD', 'TCA for broadcast frames discarded', 5233);
CALL insert_alarm_ec('tca-multicast-frames-discarded', 'TMUFRD', 'TCA for multicast frames discarded', 5234);
CALL insert_alarm_ec('crypto-config-mismatch', 'CRCFGMIS', 'Crypto Configuration Mismatch', 5235);
CALL insert_alarm_ec('no-ip-address-resources', 'NOIPADRR', 'no-ip-address-resources', 5236);
CALL insert_alarm_ec('no-ip-multicast-resources', 'NOIPMULA', 'no-ip-multicast-resources', 5237);
CALL insert_alarm_ec('mac-table-full', 'MACTFULL', 'mac-table-full', 5238);
CALL insert_alarm_ec('optical-bandwidth-mismatch', 'MEABW', 'Optical Bandwidth Mismatch', 5239);
CALL insert_alarm_ec('tca-l2cp-frames-discarded', 'TL2CPFRD', 'TCA for L2CP frames discarded', 5240);
CALL insert_alarm_ec('tca-l2pt-encap-frames-rcvd', 'TL2PTEFR', 'TCA for L2PT encapuslated frames received', 5241);
CALL insert_alarm_ec('tca-l2pt-decap-frames-sent', 'TL2PTDFS', 'TCA for L2PT decapuslated frames sent', 5242);
CALL insert_alarm_ec('vpn-route-resource-exhausted', 'VRREXH', 'vpn-route-resource-exhausted', 5243);
CALL insert_alarm_ec('lsp-resource-exhausted', 'LREXH', 'lsp-resource-exhausted', 5244);
CALL insert_alarm_ec('loss-code-word-sync', 'LCWSYNC', 'Loss of Code-word Synchronization', 5245);
CALL insert_alarm_ec('port-fenced', 'PORTFNCD', 'Port Fenced', 5246);
CALL insert_alarm_ec('tx-not-operational-sequence', 'TXNOS', 'Tx Not-Operational Sequence', 5247);
CALL insert_alarm_ec('evpn-dual-home-resources-used-up', 'EVPNDHRU', 'evpn-dual-home-resources-used-up', 5248);
CALL insert_alarm_ec('dual-trigger-mismatch', 'DTM', 'dual trigger mismatch', 5249);
CALL insert_alarm_ec('execeed-reserve-bw', 'EXCRSVBW', 'execeed-reserve-bw', 5250);
CALL insert_alarm_ec('epte-1-peer-comm-fail', 'E1COMMF', 'Comm Fail to Module with EPTE 1', 5251);
CALL insert_alarm_ec('epte-2-peer-comm-fail', 'E2COMMF', 'Comm Fail to Module with EPTE 2', 5252);
CALL insert_alarm_ec('epte-3-peer-comm-fail', 'E3COMMF', 'Comm Fail to Module with EPTE 3', 5253);
CALL insert_alarm_ec('epte-4-peer-comm-fail', 'E4COMMF', 'Comm Fail to Module with EPTE 4', 5254);
CALL insert_alarm_ec('epte-east-peer-comm-fail', 'EECOMMF', 'epte-east-peer-comm-fail', 5255);
CALL insert_alarm_ec('epte-west-peer-comm-fail', 'EWCOMMF', 'epte-west-peer-comm-fail', 5256);
CALL insert_alarm_ec('all-lanes-summary-outage', 'ALSO', 'all-lanes-summary-outage', 5257);
CALL insert_alarm_ec('mea-portal-sync', 'MISPTS', 'mea-portal-sync', 5258);
CALL insert_alarm_ec('spaneq-spectrum-change', 'SPECCHG', 'Span Equalization Spectrum Change', 5259);
CALL insert_alarm_ec('server-connectivity-failure', 'SRVCONFL', 'server-connectivity-failure', 5260);
CALL insert_alarm_ec('software-package-missing', 'SWPKGMIS', 'software-package-missing', 5261);
CALL insert_alarm_ec('traffic-crc-error', 'TCRCERR', 'traffic-crc-error', 5262);
CALL insert_alarm_ec('rx-link-fault', 'RXLNKFLT', 'Rx Link Fault', 5263);
CALL insert_alarm_ec('tx-link-fault', 'TXLNKFLT', 'Tx Link Fault', 5264);
CALL insert_alarm_ec('transmit-oci', 'OCITX', 'Transmit Open Connection Indication', 5265);
CALL insert_alarm_ec('generic-alarm-indication-signal-defect', 'DGAIS', 'generic-alarm-indication-signal-defect', 5266);
CALL insert_alarm_ec('sf-working-epte', 'SFEPTEWK', 'sf-working-epte', 5269);
CALL insert_alarm_ec('sf-protection-epte', 'SFEPTEPT', 'sf-protection-epte', 5270);
CALL insert_alarm_ec('alternate-loss-of-signal-payload-defect', 'DLOSPA', 'alternate-loss-of-signal-payload-defect', 5273);
CALL insert_alarm_ec('service-discarded', 'SRVDISC', 'Lag alarm and condition types', 6002);
CALL insert_alarm_ec('bw-exceed-port-speed', 'BWEX', 'Lag alarm and condition types', 6003);
CALL insert_alarm_ec('mea-portal-address', 'MISPADDR', 'Lag alarm and condition types', 6004);
CALL insert_alarm_ec('mea-portal-pri', 'MISPPRI', 'Lag alarm and condition types', 6005);
CALL insert_alarm_ec('mea-three-portal', 'MIS3P', 'Lag alarm and condition types', 6006);
CALL insert_alarm_ec('mea-portal-sys-number', 'MISPSNUM', 'Lag alarm and condition types', 6007);
CALL insert_alarm_ec('mea-actor-admin-key', 'MISAAKEY', 'Lag alarm and condition types', 6008);
CALL insert_alarm_ec('mea-port-digest', 'MISPDGT', 'Lag alarm and condition types', 6009);
CALL insert_alarm_ec('mea-gw-digest', 'MISGWDGT', 'Lag alarm and condition types', 6010);
CALL insert_alarm_ec('tca-rx-error-block', 'TRXERBLK', 'TCA Rx Error Block', 7000);
CALL insert_alarm_ec('tca-rx-error-bip', 'TRXERBIP', 'TCA Rx Error BIP', 7001);
CALL insert_alarm_ec('tca-tx-error-block', 'TTXERBLK', 'TCA Tx Error Block', 7002);
CALL insert_alarm_ec('tca-tx-error-bip', 'TTXERBIP', 'TCA Tx Error BIP', 7003);
CALL insert_alarm_ec('tca-byte-sent', 'TTXBYTE', 'TCA for number of bytes transmitted', 7004);
CALL insert_alarm_ec('tca-byte-rcvd', 'TRXBYTE', 'TCA for number of bytes received', 7005);
CALL insert_alarm_ec('tca-frame-sent', 'TTXFRAME', 'TCA Frame Tx', 7006);
CALL insert_alarm_ec('tca-frame-rcvd', 'TRXFRAME', 'TCA Frame Rx', 7007);
CALL insert_alarm_ec('tca-total-byte-rcvd', 'TTORXBYT', 'TCA Total Byte Rx', 7008);
CALL insert_alarm_ec('tca-total-byte-sent', 'TTOTXBYT', 'TCA Total Byte Tx', 7009);
CALL insert_alarm_ec('tca-broadcast-frame-rcvd', 'TRXBFRAM', 'TCA Broadcast Frame Rx', 7010);
CALL insert_alarm_ec('tca-multicast-frame-rcvd', 'TRXMFRAM', 'TCA Multicast Frame Rx', 7011);
CALL insert_alarm_ec('tca-broadcast-frame-sent', 'TTXBFRAM', 'TCA Broadcast Frame Tx', 7012);
CALL insert_alarm_ec('tca-multicast-frame-sent', 'TTXMFRAM', 'TCA Multicast Frame Tx', 7013);
CALL insert_alarm_ec('tca-rx-crc-errored-pkt', 'TRXCRCER', 'TCA Rx CRC Errored Packet', 7014);
CALL insert_alarm_ec('tca-oversized-frame-discarded', 'TOVSZDIS', 'TCA Oversized Frame Discarded', 7015);
CALL insert_alarm_ec('tca-oversized-pkt-rcvd', 'TRXOVRSZ', 'TCA Oversized Packet Rx', 7016);
CALL insert_alarm_ec('tca-undersized-pkt-rcvd', 'TRXUNDER', 'TCA Undersized Packet Rx', 7017);
CALL insert_alarm_ec('tca-drop-event', 'TDROPEVT', 'Drop Events High', 7018);
CALL insert_alarm_ec('tca-fragment-rcv', 'TRXFRAG', 'TCA Fragement Rx', 7019);
CALL insert_alarm_ec('tca-collision-rcv', 'TRXCOLSN', 'TCA Collision Rx', 7020);
CALL insert_alarm_ec('tca-pkts-rcv', 'TRXPKT', 'TCA Packets Rx', 7021);
CALL insert_alarm_ec('tca-octets-rcv', 'TRXOCT', 'TCA Octets Rx', 7022);
CALL insert_alarm_ec('tca-pkts64-octets-rcv', 'TRXOCT64', 'TCA for total number of 1 to 64 octets frame received', 7023);
CALL insert_alarm_ec('tca-pkts65to127-octets-rcv', 'TR65T127', 'TCA Pkts 65to127 Octets Rx', 7024);
CALL insert_alarm_ec('tca-pkts128to255-octets-rcv', 'TR128255', 'TCA Pkts 128to255 Octets Rx', 7025);
CALL insert_alarm_ec('tca-pkts256to511-octets-rcv', 'TR256511', 'TCA Pkts 256to511 Octets Rx', 7026);
CALL insert_alarm_ec('tca-pkts512to1023-octets-rcv', 'TR512T1K', 'TCA Pkts 512to1023 Octets Rx', 7027);
CALL insert_alarm_ec('tca-pkts1024to1518-octets-rcv', 'TR1K1P5', 'TCA Pkts 1024to1518 Octets Rx', 7028);
CALL insert_alarm_ec('tca-pkts1519tomtu-octets-rcv', 'TR1P5MTU', 'TCA Pkts 1519toMTU Octets Rx', 7029);
CALL insert_alarm_ec('tca-tx-crc-errored-pkt', 'TTXCRCER', 'TCA Tx CRC Errored Packet', 7030);
CALL insert_alarm_ec('tca-octets-send', 'TTXOCT', 'TCA Octets Tx', 7031);
CALL insert_alarm_ec('tca-unicast-frame-rcvd', 'TRXUFRAM', 'TCA for number of unicast frames received', 7032);
CALL insert_alarm_ec('tca-unicast-frame-sent', 'TTXUFRAM', 'TCA Unicast Frame Tx', 7033);
CALL insert_alarm_ec('tca-average-bit-rate-rcvd', 'TRXAVGR', 'Average Bit Rate Rx High', 7034);
CALL insert_alarm_ec('tca-average-bit-rate-sent', 'TTXAVGR', 'Average Bit Rate Tx High', 7035);
CALL insert_alarm_ec('tca-byte-random-early-discard-dropped', 'TBREDDP', 'Random Early Drop Bytes High', 7036);
CALL insert_alarm_ec('tca-frame-random-early-discard-dropped', 'TFREDDP', 'Random Early Drop Frames High', 7037);
CALL insert_alarm_ec('tca-byte-tail-dropped', 'TBTAILDP', 'Tail Drop Bytes High', 7038);
CALL insert_alarm_ec('tca-frame-tail-dropped', 'TFTAILDP', 'Tail Drop Frames High', 7039);
CALL insert_alarm_ec('tca-afp-tagged-frame-dropped', 'TTAGDP', 'AFP Tagged Frames Drop High', 7040);
CALL insert_alarm_ec('tca-afp-untagged-frame-dropped', 'TUNTAGDP', 'AFP Untagged Frames Drop High', 7041);
CALL insert_alarm_ec('tca-afp-pri-tagged-frame-dropped', 'TPRTAGDP', 'AFP Priority Tagged Frames Drop High', 7042);
CALL insert_alarm_ec('tca-frame-priority-dropped', 'TFPRD', 'Priority Frame Drop High', 7043);
CALL insert_alarm_ec('tca-green-byte-drop', 'TGBDROP', 'TCA for number of green bytes dropped', 7044);
CALL insert_alarm_ec('tca-frame-mark-green-passed', 'TFMGPASS', 'TCA for number of green frame transmitted', 7045);
CALL insert_alarm_ec('tca-frame-mark-green-dropped', 'TFMGDROP', 'TCA for number of green frame dropped', 7046);
CALL insert_alarm_ec('tca-frame-mark-yellow-passed', 'TFMYPASS', 'TCA for number of yellow frame transmitted', 7047);
CALL insert_alarm_ec('tca-frame-mark-yellow-dropped', 'TFMYDROP', 'TCA for number of yellow frame dropped', 7048);
CALL insert_alarm_ec('tca-frame-mark-red-dropped', 'TFMRDROP', 'Number of red frame dropped', 7049);
CALL insert_alarm_ec('tca-ef-frame-discarded', 'TEFFD', 'Elephant flow frame discarded', 7050);
CALL insert_alarm_ec('tca-ef-byte-discarded', 'TEFBD', 'Elephant flow byte discarded', 7051);
CALL insert_alarm_ec('tca-twamp-s2r-packets', 'TS2RPKTS', 'This shows the number of packets sent from the Session Sender to the Session Reflector', 8000);
CALL insert_alarm_ec('tca-twamp-r2s-packets', 'TR2SPKTS', 'This shows the number of packets sent from the Session Reflector to Session Sender', 8001);
CALL insert_alarm_ec('tca-twamp-s2r-lost-packets', 'TS2RLOST', 'This shows the number of packets sent by Session Sender that did not reach the Session Reflector', 8002);
CALL insert_alarm_ec('tca-twamp-r2s-lost-packets', 'TR2SLOST', 'This shows the number of packets send by Session Reflector that did not reach the Session Sender', 8003);
CALL insert_alarm_ec('tca-twamp-s2r-sync-errors', 'TS2RSYNC', 'This indicates the Sender and Reflector are out of sync. This happens when Sender to Reflector delay is being computed and Reflector timestamp value is lower than the Sender timestamp', 8004);
CALL insert_alarm_ec('tca-twamp-r2s-sync-errors', 'TR2SSYNC', 'This indicates the Sender and Reflector are out of sync. This happens when Reflector to Sender delay is being computed and Sender timestamp value is lower than the Reflector timestamp', 8005);
CALL insert_alarm_ec('tca-twamp-out-of-sequence-errors', 'TSEQERR', 'This shows the number of occurrences where the sequence number of the received packet at the Session Sender is less than the previously received frame', 8006);
CALL insert_alarm_ec('tca-twamp-sequence-gap-errors', 'TGAPERR', 'This shows the number of occurrences where a packet is received before one or more of previously sent packets resulting in a gap in the sequence numbers', 8007);
CALL insert_alarm_ec('tca-twamp-min-two-way-path-delay', 'TMINTWPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for two-way packet delay. Average value may also be computed as follows: (twampSumOfTwoWayFD)/ (twampS2RPkts + twampS2RLostPkts + twampR2SLostPkts) On the user interfaces this shall be displayed as microseconds', 8008);
CALL insert_alarm_ec('tca-twamp-max-two-way-path-delay', 'TMAXTWPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for two-way packet delay. Average value may also be computed as follows: (twampSumOfTwoWayFD)/ (twampS2RPkts + twampS2RLostPkts + twampR2SLostPkts) On the user interfaces this shall be displayed as microseconds', 8009);
CALL insert_alarm_ec('tca-twamp-avg-two-way-path-delay', 'TAVGTWPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for two-way packet delay. Average value may also be computed as follows: (twampSumOfTwoWayFD)/ (twampS2RPkts + twampS2RLostPkts + twampR2SLostPkts) On the user interfaces this shall be displayed as microseconds', 8010);
CALL insert_alarm_ec('tca-twamp-sum-two-way-path-delay', 'TSUMTWPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for two-way packet delay. Average value may also be computed as follows: (twampSumOfTwoWayFD)/ (twampS2RPkts + twampS2RLostPkts + twampR2SLostPkts) On the user interfaces this shall be displayed as microseconds', 8011);
CALL insert_alarm_ec('tca-twamp-sum-of-squares-two-way-path-delay', 'TSOSTWPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for two-way packet delay. Average value may also be computed as follows: (twampSumOfTwoWayFD)/ (twampS2RPkts + twampS2RLostPkts + twampR2SLostPkts) On the user interfaces this shall be displayed as microseconds', 8012);
CALL insert_alarm_ec('tca-twamp-num-two-way-path-delay', 'TNUMTWPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for two-way packet delay. Average value may also be computed as follows: (twampSumOfTwoWayFD)/ (twampS2RPkts + twampS2RLostPkts + twampR2SLostPkts) On the user interfaces this shall be displayed as microseconds', 8013);
CALL insert_alarm_ec('tca-twamp-min-one-way-s2r-path-delay', 'TMINSRPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for one-way Session Sender to Session Reflector packet delay. Average value may also be computed as follows: (twampSumOfOneWayS2RPD)/ (twampS2RPkts + twampS2RLostPkts + twampS2RSyncErrs) On the user interfaces this shall be displayed as microseconds', 8014);
CALL insert_alarm_ec('tca-twamp-max-one-way-s2r-path-delay', 'TMAXSRPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for one-way Session Sender to Session Reflector packet delay. Average value may also be computed as follows: (twampSumOfOneWayS2RPD)/ (twampS2RPkts + twampS2RLostPkts + twampS2RSyncErrs) On the user interfaces this shall be displayed as microseconds', 8015);
CALL insert_alarm_ec('tca-twamp-avg-one-way-s2r-path-delay', 'TAVGSRPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for one-way Session Sender to Session Reflector packet delay. Average value may also be computed as follows: (twampSumOfOneWayS2RPD)/ (twampS2RPkts + twampS2RLostPkts + twampS2RSyncErrs) On the user interfaces this shall be displayed as microseconds', 8016);
CALL insert_alarm_ec('tca-twamp-sum-one-way-s2r-path-delay', 'TSUMSRPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for one-way Session Sender to Session Reflector packet delay. Average value may also be computed as follows: (twampSumOfOneWayS2RPD)/ (twampS2RPkts + twampS2RLostPkts + twampS2RSyncErrs) On the user interfaces this shall be displayed as microseconds', 8017);
CALL insert_alarm_ec('tca-twamp-sum-of-squares-one-way-s2r-path-delay', 'TSOSSRPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for one-way Session Sender to Session Reflector packet delay. Average value may also be computed as follows: (twampSumOfOneWayS2RPD)/ (twampS2RPkts + twampS2RLostPkts + twampS2RSyncErrs) On the user interfaces this shall be displayed as microseconds', 8018);
CALL insert_alarm_ec('tca-twamp-num-one-way-s2r-path-delay', 'TNUMSRPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for one-way Session Sender to Session Reflector packet delay. Average value may also be computed as follows: (twampSumOfOneWayS2RPD)/ (twampS2RPkts + twampS2RLostPkts + twampS2RSyncErrs) On the user interfaces this shall be displayed as microseconds', 8019);
CALL insert_alarm_ec('tca-twamp-min-one-way-r2s-path-delay', 'TMINRSPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for one-way Session Sender to Session Reflector packet delay. Average value may also be computed as follows: (twampSumOfOneWayr2sPD)/ (twampr2sPkts + twampr2sLostPkts + twampr2sSyncErrs) On the user interfaces this shall be displayed as microseconds', 8020);
CALL insert_alarm_ec('tca-twamp-max-one-way-r2s-path-delay', 'TMAXRSPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for one-way Session Sender to Session Reflector packet delay. Average value may also be computed as follows: (twampSumOfOneWayr2sPD)/ (twampr2sPkts + twampr2sLostPkts + twampr2sSyncErrs) On the user interfaces this shall be displayed as microseconds', 8021);
CALL insert_alarm_ec('tca-twamp-avg-one-way-r2s-path-delay', 'TAVGRSPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for one-way Session Sender to Session Reflector packet delay. Average value may also be computed as follows: (twampSumOfOneWayr2sPD)/ (twampr2sPkts + twampr2sLostPkts + twampr2sSyncErrs) On the user interfaces this shall be displayed as microseconds', 8022);
CALL insert_alarm_ec('tca-twamp-sum-one-way-r2s-path-delay', 'TSUMRSPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for one-way Session Sender to Session Reflector packet delay. Average value may also be computed as follows: (twampSumOfOneWayr2sPD)/ (twampr2sPkts + twampr2sLostPkts + twampr2sSyncErrs) On the user interfaces this shall be displayed as microseconds', 8023);
CALL insert_alarm_ec('tca-twamp-sum-of-squares-one-way-r2s-path-delay', 'TSOSRSPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for one-way Session Sender to Session Reflector packet delay. Average value may also be computed as follows: (twampSumOfOneWayr2sPD)/ (twampr2sPkts + twampr2sLostPkts + twampr2sSyncErrs) On the user interfaces this shall be displayed as microseconds', 8024);
CALL insert_alarm_ec('tca-twamp-num-one-way-r2s-path-delay', 'TNUMRSPD', 'These are the minimum, maximum, average, sum of all delay samples, sum of squares of all delay samples and number of samples for one-way Session Sender to Session Reflector packet delay. Average value may also be computed as follows: (twampSumOfOneWayr2sPD)/ (twampr2sPkts + twampr2sLostPkts + twampr2sSyncErrs) On the user interfaces this shall be displayed as microseconds', 8025);
CALL insert_alarm_ec('tca-twamp-min-one-way-s2r-abs-path-delay-variation', 'TMINSRAV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the absolute value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RAbsPDV/ twampNumOneWayS2RAbsPDV On the user interfaces this shall be displayed as microseconds', 8026);
CALL insert_alarm_ec('tca-twamp-max-one-way-s2r-abs-path-delay-variation', 'TMAXSRAV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the absolute value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RAbsPDV/ twampNumOneWayS2RAbsPDV On the user interfaces this shall be displayed as microseconds', 8027);
CALL insert_alarm_ec('tca-twamp-avg-one-way-s2r-abs-path-delay-variation', 'TAVGSRAV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the absolute value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RAbsPDV/ twampNumOneWayS2RAbsPDV On the user interfaces this shall be displayed as microseconds', 8028);
CALL insert_alarm_ec('tca-twamp-sum-one-way-s2r-abs-path-delay-variation', 'TSUMSRAV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the absolute value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RAbsPDV/ twampNumOneWayS2RAbsPDV On the user interfaces this shall be displayed as microseconds', 8029);
CALL insert_alarm_ec('tca-twamp-sum-of-squares-one-way-s2r-abs-path-delay-variation', 'TSOSSRAV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the absolute value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RAbsPDV/ twampNumOneWayS2RAbsPDV On the user interfaces this shall be displayed as microseconds', 8030);
CALL insert_alarm_ec('tca-twamp-num-one-way-s2r-abs-path-delay-variation', 'TNUMSRAV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the absolute value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RAbsPDV/ twampNumOneWayS2RAbsPDV On the user interfaces this shall be displayed as microseconds', 8031);
CALL insert_alarm_ec('tca-twamp-min-one-way-r2s-abs-path-delay-variation', 'TMINRSAV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the absolute value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SAbsPDV/ twampNumOneWayR2SAbsPDV On the user interfaces this shall be displayed as microseconds', 8032);
CALL insert_alarm_ec('tca-twamp-max-one-way-r2s-abs-path-delay-variation', 'TMAXRSAV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the absolute value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SAbsPDV/ twampNumOneWayR2SAbsPDV On the user interfaces this shall be displayed as microseconds', 8033);
CALL insert_alarm_ec('tca-twamp-avg-one-way-r2s-abs-path-delay-variation', 'TAVGRSAV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the absolute value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SAbsPDV/ twampNumOneWayR2SAbsPDV On the user interfaces this shall be displayed as microseconds', 8034);
CALL insert_alarm_ec('tca-twamp-sum-one-way-r2s-abs-path-delay-variation', 'TSUMRSAV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the elay samples and number of samples for the positive value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SPosPDV/ twampNumOneWayR2SPosPDV On the user interfaces this shall be displayed as microseconds', 8035);
CALL insert_alarm_ec('tca-twamp-sum-of-squares-one-way-r2s-abs-path-delay-variation', 'TSOSRSAV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the absolute value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SAbsPDV/ twampNumOneWayR2SAbsPDV On the user interfaces this shall be displayed as microseconds', 8036);
CALL insert_alarm_ec('tca-twamp-num-one-way-r2s-abs-path-delay-variation', 'TNUMRSAV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the absolute value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SAbsPDV/ twampNumOneWayR2SAbsPDV On the user interfaces this shall be displayed as microseconds', 8037);
CALL insert_alarm_ec('tca-twamp-min-one-way-s2r-negative-path-delay-variation', 'TMINSRNV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the negative value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RNegPDV/ twampNumOneWayS2RNegPDV On the user interfaces this shall be displayed as microseconds', 8038);
CALL insert_alarm_ec('tca-twamp-max-one-way-s2r-negative-path-delay-variation', 'TMAXSRNV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the negative value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RNegPDV/ twampNumOneWayS2RNegPDV On the user interfaces this shall be displayed as microseconds', 8039);
CALL insert_alarm_ec('tca-twamp-avg-one-way-s2r-negative-path-delay-variation', 'TAVGSRNV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the negative value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RNegPDV/ twampNumOneWayS2RNegPDV On the user interfaces this shall be displayed as microseconds', 8040);
CALL insert_alarm_ec('tca-twamp-sum-one-way-s2r-negative-path-delay-variation', 'TSUMSRNV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the negative value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RNegPDV/ twampNumOneWayS2RNegPDV On the user interfaces this shall be displayed as microseconds', 8041);
CALL insert_alarm_ec('tca-twamp-sum-of-squares-one-way-s2r-negative-path-delay-variation', 'TSOSSRNV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the negative value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RNegPDV/ twampNumOneWayS2RNegPDV On the user interfaces this shall be displayed as microseconds', 8042);
CALL insert_alarm_ec('tca-twamp-num-one-way-s2r-negative-path-delay-variation', 'TNUMSRNV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the negative value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RNegPDV/ twampNumOneWayS2RNegPDV On the user interfaces this shall be displayed as microseconds', 8043);
CALL insert_alarm_ec('tca-twamp-min-one-way-r2s-negative-path-delay-variation', 'TMINRSNV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the negative value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SNegPDV/ twampNumOneWayR2SNegPDV On the user interfaces this shall be displayed as microseconds', 8044);
CALL insert_alarm_ec('tca-twamp-max-one-way-r2s-negative-path-delay-variation', 'TMAXRSNV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the negative value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SNegPDV/ twampNumOneWayR2SNegPDV On the user interfaces this shall be displayed as microseconds', 8045);
CALL insert_alarm_ec('tca-twamp-avg-one-way-r2s-negative-path-delay-variation', 'TAVGRSNV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the negative value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SNegPDV/ twampNumOneWayR2SNegPDV On the user interfaces this shall be displayed as microseconds', 8046);
CALL insert_alarm_ec('tca-twamp-sum-one-way-r2s-negative-path-delay-variation', 'TSUMRSNV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the negative value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SNegPDV/ twampNumOneWayR2SNegPDV On the user interfaces this shall be displayed as microseconds', 8047);
CALL insert_alarm_ec('tca-twamp-sum-of-squares-one-way-r2s-negative-path-delay-variation', 'TSOSRSNV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the negative value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SNegPDV/ twampNumOneWayR2SNegPDV On the user interfaces this shall be displayed as microseconds', 8048);
CALL insert_alarm_ec('tca-twamp-num-one-way-r2s-negative-path-delay-variation', 'TNUMRSNV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the negative value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SNegPDV/ twampNumOneWayR2SNegPDV On the user interfaces this shall be displayed as microseconds', 8049);
CALL insert_alarm_ec('tca-twamp-min-one-way-s2r-positive-path-delay-variation', 'TMINSRPV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the positive value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RPosPDV/ twampNumOneWayS2RPosPDV On the user interfaces this shall be displayed as microseconds', 8050);
CALL insert_alarm_ec('tca-twamp-max-one-way-s2r-positive-path-delay-variation', 'TMAXSRPV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the positive value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RPosPDV/ twampNumOneWayS2RPosPDV On the user interfaces this shall be displayed as microseconds', 8051);
CALL insert_alarm_ec('tca-twamp-avg-one-way-s2r-positive-path-delay-variation', 'TAVGSRPV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the positive value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RPosPDV/ twampNumOneWayS2RPosPDV On the user interfaces this shall be displayed as microseconds', 8052);
CALL insert_alarm_ec('tca-twamp-sum-one-way-s2r-positive-path-delay-variation', 'TSUMSRPV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the positive value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RPosPDV/ twampNumOneWayS2RPosPDV On the user interfaces this shall be displayed as microseconds', 8053);
CALL insert_alarm_ec('tca-twamp-sum-of-squares-one-way-s2r-positive-path-delay-variation', 'TSOSSRPV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the positive value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RPosPDV/ twampNumOneWayS2RPosPDV On the user interfaces this shall be displayed as microseconds', 8054);
CALL insert_alarm_ec('tca-twamp-num-one-way-s2r-positive-path-delay-variation', 'TNUMSRPV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the positive value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayS2RPosPDV/ twampNumOneWayS2RPosPDV On the user interfaces this shall be displayed as microseconds', 8055);
CALL insert_alarm_ec('tca-twamp-min-one-way-r2s-positive-path-delay-variation', 'TMINRSPV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the positive value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SPosPDV/ twampNumOneWayR2SPosPDV On the user interfaces this shall be displayed as microseconds', 8056);
CALL insert_alarm_ec('tca-twamp-max-one-way-r2s-positive-path-delay-variation', 'TMAXRSPV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the positive value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SPosPDV/ twampNumOneWayR2SPosPDV On the user interfaces this shall be displayed as microseconds', 8057);
CALL insert_alarm_ec('tca-twamp-avg-one-way-r2s-positive-path-delay-variation', 'TAVGRSPV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the positive value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SPosPDV/ twampNumOneWayR2SPosPDV On the user interfaces this shall be displayed as microseconds', 8058);
CALL insert_alarm_ec('tca-twamp-sum-one-way-r2s-positive-path-delay-variation', 'TSUMRSPV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the positive value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SPosPDV/ twampNumOneWayR2SPosPDV On the user interfaces this shall be displayed as microseconds', 8059);
CALL insert_alarm_ec('tca-twamp-sum-of-squares-one-way-r2s-positive-path-delay-variation', 'TSOSRSPV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the positive value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SPosPDV/ twampNumOneWayR2SPosPDV On the user interfaces this shall be displayed as microseconds', 8060);
CALL insert_alarm_ec('tca-twamp-num-one-way-r2s-positive-path-delay-variation', 'TNUMRSPV', 'These are the minimum, maximum,average, sum of all delay samples, sum of squares of all delay samples and number of samples for the positive value of the one-way Session Sender to Session Reflector packet delay variation. Average value may also be computed as follows: twampSumOfOneWayR2SPosPDV/ twampNumOneWayR2SPosPDV On the user interfaces this shall be displayed as microseconds', 8061);
CALL insert_alarm_ec('tca-lm-forward-transmitted-frames-pm-monitored-type', 'TLMFWTXF', 'The number of frames transmitted in the forward direction', 9001);
CALL insert_alarm_ec('tca-lm-forward-received-frames-pm-monitored-type', 'TLMFWRXF', 'The number of frames received in the forward direction', 9002);
CALL insert_alarm_ec('tca-lm-forward-min-flr-pm-monitored-type', 'TFWMNFLR', 'The minimum one-way frame loss ratio in the forward direction', 9003);
CALL insert_alarm_ec('tca-lm-forward-max-flr-pm-monitored-type', 'TFWMXFLR', 'The maximum one-way frame loss ratio in the forward direction', 9004);
CALL insert_alarm_ec('tca-lm-forward-average-flr-pm-monitored-type', 'TFWAGFLR', 'The average one-way frame loss ratio in the forward direction', 9005);
CALL insert_alarm_ec('tca-lm-backward-transmitted-frames-pm-monitored-type', 'TLMBWTXF', 'The number of frames transmitted in the backward direction', 9006);
CALL insert_alarm_ec('tca-lm-backward-received-frames-pm-monitored-type', 'TLMBWRXF', 'The number of frames received in the backward direction', 9007);
CALL insert_alarm_ec('tca-lm-backward-min-flr-pm-monitored-type', 'TBWMNFLR', 'The minimum one-way frame loss ratio in the backward direction', 9008);
CALL insert_alarm_ec('tca-lm-backward-max-flr-pm-monitored-type', 'TBWMXFLR', 'The maximum one-way frame loss ratio in the backward direction', 9009);
CALL insert_alarm_ec('tca-lm-backward-average-flr-pm-monitored-type', 'TBWAGFLR', 'The average one-way frame loss ratio in the backward direction', 9010);
CALL insert_alarm_ec('tca-lm-soam-pdus-received-pm-monitored-type', 'TLMSPDUR', 'The number of SOAM PDUs received', 9012);
CALL insert_alarm_ec('tca-lm-availability-forward-high-loss-pm-monitored-type', 'TAFWDHLP', 'The number of high loss intervals (HLI) during a small time interval available or not in the forward direction', 9013);
CALL insert_alarm_ec('tca-lm-availability-consecutive-forward-high-loss-pm-monitored-type', 'TACFWHLP', 'The number of consecutive high loss intervals (CHLI) during a small time interval available or not in the forward direction', 9014);
CALL insert_alarm_ec('tca-lm-availability-forward-available-pm-monitored-type', 'TAFWDAP', 'The number of availability indicators during a small time interval evaluated as available (low frame loss) in the forward direction', 9015);
CALL insert_alarm_ec('tca-lm-availability-forward-unavailable-pm-monitored-type', 'TAFWDUAP', 'The number of availability indicators during a small time interval evaluated as unavailable (high frame loss) in the forward direction', 9016);
CALL insert_alarm_ec('tca-lm-availability-forward-min-flr-pm-monitored-type', 'TAFMNFLR', 'The minimum one-way frame loss ratio during a small time interval available or not in the forward direction', 9017);
CALL insert_alarm_ec('tca-lm-availability-forward-max-flr-pm-monitored-type', 'TAFMXFLR', 'The maximum one-way frame loss ratio during a small time interval available or not in the forward direction', 9018);
CALL insert_alarm_ec('tca-lm-availability-forward-average-flr-pm-monitored-type', 'TAFAGFLR', 'The average one-way frame loss ratio during a small time interval available or not in the forward direction', 9019);
CALL insert_alarm_ec('tca-lm-availability-backward-high-loss-pm-monitored-type', 'TABWDHLP', 'The number of high loss intervals (HLI) during a small time interval available or not in the backward direction', 9020);
CALL insert_alarm_ec('tca-lm-availability-consecutive-backward-high-loss-pm-monitored-type', 'TACBWDHL', 'The number of consecutive high loss intervals (CHLI) during a small time interval available or not in the backward direction', 9021);
CALL insert_alarm_ec('tca-lm-availability-backward-available-pm-monitored-type', 'TAVLBWDA', 'The number of availability indicators during a small time interval evaluated as available (low frame loss) in the backward direction', 9022);
CALL insert_alarm_ec('tca-lm-availability-backward-unavailable-pm-monitored-type', 'TAVLBDUA', 'The number of availability indicators during a small time interval evaluated as unavailable (high frame loss) in the backward direction', 9023);
CALL insert_alarm_ec('tca-lm-availability-backward-min-flr-pm-monitored-type', 'TABMNFLR', 'The minimum one-way frame loss ratio during a small time interval available or not in the backward direction', 9024);
CALL insert_alarm_ec('tca-lm-availability-backward-max-flr-pm-monitored-type', 'TABMXFLR', 'The maximum one-way frame loss ratio during a small time interval available or not in the backward direction', 9025);
CALL insert_alarm_ec('tca-lm-availability-backward-average-flr-pm-monitored-type', 'TABAGFLR', 'The average one-way frame loss ratio during a small time interval available or not in the backward direction', 9026);
CALL insert_alarm_ec('tca-dm-soam-pdus-sent-pm-monitored-type', 'TDMSPDUT', 'The number of soam pdus transmitted', 9051);
CALL insert_alarm_ec('tca-dm-soam-pdus-received-pm-monitored-type', 'TDMSPDUR', 'The number of soam pdus received', 9052);
CALL insert_alarm_ec('tca-dm-frame-delay-two-way-min-pm-monitored-type', 'TDFDTWMN', 'The minimum two-way frame delay', 9053);
CALL insert_alarm_ec('tca-dm-frame-delay-two-way-max-pm-monitored-type', 'TDFDTWMX', 'The maximum two-way frame delay', 9054);
CALL insert_alarm_ec('tca-dm-frame-delay-two-way-average-pm-monitored-type', 'TDFDTWAG', 'The average two-way frame delay', 9055);
CALL insert_alarm_ec('tca-dm-frame-delay-forward-min-pm-monitored-type', 'TDFDFWMN', 'The minimum frame delay in the forward direction', 9056);
CALL insert_alarm_ec('tca-dm-frame-delay-forward-max-pm-monitored-type', 'TDFDFWMX', 'The maximum frame delay in the forward direction', 9057);
CALL insert_alarm_ec('tca-dm-frame-delay-forward-average-pm-monitored-type', 'TDFDFWAG', 'The average frame delay in the forward direction', 9058);
CALL insert_alarm_ec('tca-dm-frame-delay-backward-min-pm-monitored-type', 'TDFDBWMN', 'The minimum frame delay in the backward direction', 9059);
CALL insert_alarm_ec('tca-dm-frame-delay-backward-max-pm-monitored-type', 'TDFDBWMX', 'The maximum frame delay in the backward direction', 9060);
CALL insert_alarm_ec('tca-dm-frame-delay-backward-average-pm-monitored-type', 'TDFDBWAG', 'The average frame delay in the backward direction', 9061);
CALL insert_alarm_ec('tca-dm-inter-frame-delay-variation-forward-min-pm-monitored-type', 'TIFDFWMN', 'The minimum inter frame delay variation in the forward direction', 9062);
CALL insert_alarm_ec('tca-dm-inter-frame-delay-variation-forward-max-pm-monitored-type', 'TIFDFWMX', 'The maximum inter frame delay variation in the forward direction', 9063);
CALL insert_alarm_ec('tca-dm-inter-frame-delay-variation-forward-average-pm-monitored-type', 'TIFDFWAG', 'The average inter frame delay variation in the forward direction', 9064);
CALL insert_alarm_ec('tca-dm-inter-frame-delay-variation-backward-min-pm-monitored-type', 'TIFDBWMN', 'The minimum inter frame delay variation in the backward direction', 9065);
CALL insert_alarm_ec('tca-dm-inter-frame-delay-variation-backward-max-pm-monitored-type', 'TIFDBWMX', 'The maximum inter frame delay variation in the backward direction', 9066);
CALL insert_alarm_ec('tca-dm-inter-frame-delay-variation-backward-average-pm-monitored-type', 'TIFVBWAG', 'The average inter frame delay variation in the backward direction', 9067);
CALL insert_alarm_ec('tca-dm-inter-frame-delay-variation-two-way-min-pm-monitored-type', 'TIFDTWMN', 'The two ways minimum inter frame delay variation', 9068);
CALL insert_alarm_ec('tca-dm-inter-frame-delay-variation-two-way-max-pm-monitored-type', 'TIFDTWMX', 'The two way maximum inter frame delay variation', 9069);
CALL insert_alarm_ec('tca-dm-inter-frame-delay-variation-two-way-average-pm-monitored-type', 'TIFDTWAG', 'The two way average inter frame delay variation ', 9070);
CALL insert_alarm_ec('tca-dm-frame-delay-range-forward-max-pm-monitored-type', 'TFDRFWMX', 'The maximum frame delay range in the forward direction', 9071);
CALL insert_alarm_ec('tca-dm-frame-delay-range-forward-average-pm-monitored-type', 'TFDRFWAG', 'The average frame delay range in the forward direction', 9072);
CALL insert_alarm_ec('tca-dm-frame-delay-range-backward-max-pm-monitored-type', 'TFDRBWMX', 'The maximum frame delay range in the backward direction', 9073);
CALL insert_alarm_ec('tca-dm-frame-delay-range-backward-average-pm-monitored-type', 'TFDRBWAG', 'The average frame delay range in the backward direction', 9074);
CALL insert_alarm_ec('tca-dm-frame-delay-range-two-way-max-pm-monitored-type', 'TFDRTWMX', 'The maximum two way frame delay range', 9075);
CALL insert_alarm_ec('tca-dm-frame-delay-range-two-way-average-pm-monitored-type', 'TFDRTWAG', 'The average two way frame delay range', 9076);
CALL insert_alarm_ec('tca-sync-errors-forward-pm-monitored-type', 'TSYNCEFW', 'The sync erors in forward direction', 9077);
CALL insert_alarm_ec('tca-sync-errors-backward-pm-monitored-type', 'TSYNCEBW', 'The sync erors in backward direction', 9078);
CALL insert_alarm_ec('tca-lm-soam-pdus-sent-pm-monitored-type', 'TLMSPDUT', 'The number of SOAM PDUs sent', 9090);
CALL insert_alarm_ec('accessible-emission-limit-ex', 'AELEX', 'Raised when client output power exceeds the 1M limit Replaced by accessible-emission-limit', 18678);
CALL insert_alarm_ec('accessible-emission-limit-ht', 'AELMTHT', 'Raised as a laser safety response to far end assessable emission limit indication (aelex) which causes the near end EDFA to operate with reduced power. There is a 30 seconds release hysteresis timeronce the far end indication has cleared. Replaced by accessible-emission-limit-reduce.', 18679);
CALL insert_alarm_ec('accessible-emission-limit-ht-ex', 'AELMTEX', 'Raised when equipment attempts to control edfa power have not reduced power within M1 safety limits. This indication shall result in an APS shutdown of the edfa. This indication is used as a defect and is not presented as a condition or alarm to the user interface. Instead of it is an input to theAPS action and alarm. Replaced by accessible-emission-limit-reduce-exceeded.', 18680);
CALL insert_alarm_ec('server-signal-adjustment', 'SSA', 'server-signal-adjustment', 18681);
CALL insert_alarm_ec('syslog-server-unreachable', 'LOGSUNV', 'Log server unreachable', 5128);
CALL insert_alarm_ec('group-identification-mismatch', 'GIDM', 'Group Id Mismatch', 5167);
CALL insert_alarm_ec('flexo-map-mismatch', 'FMM', 'FlexO Map Mismatch', 5168);
CALL insert_alarm_ec('tca-incoming-network-utilization-hi', 'TRXNWUTH', 'High TCA for incoming pkts throughput', 5176);
CALL insert_alarm_ec('tca-outgoing-network-utilization-hi', 'TTXNWUTH', 'High TCA for outgoing pkts throughput', 5177);
CALL insert_alarm_ec('tca-outgoing-network-utilization-lo', 'TTXNWUTL', 'Low TCA for outgoing pkts throughput', 5178);
CALL insert_alarm_ec('tca-incoming-network-utilization-lo', 'TRXNWUTL', 'Low TCA for incoming pkts throughput', 5179);
CALL insert_alarm_ec('last-key-in-use', 'LASTKEY', 'Last key in the key-chain is in use', 5180);
CALL insert_alarm_ec('last-key-expired', 'KEYEXPRD', 'Last key in the key-chain has expired', 5181);
CALL insert_alarm_ec('storage-wearout', 'STWEOUT', 'Storage wearout alarm reported on ssd', 5184);
CALL insert_alarm_ec('spaneq-agates-incomplete', 'SPEQICMP', 'Span Equalization AGATES Incomplete', 5185);
CALL insert_alarm_ec('tca-optical-back-reflection-high', 'TOBRH', 'TCA Optical Back Reflection High', 5186);
CALL insert_alarm_ec('standby-invalid', 'STBINV', 'Standby bank is invalid', 5189);
CALL insert_alarm_ec('acp-peer-comm-fail', 'ACPCOMMF', 'Comm Fail to Module with ACP', 5194);
CALL insert_alarm_ec('auto-power-reduction-shutdown', 'APRSA', 'Automatic power reduction shutdown', 5276);
CALL insert_alarm_ec('bfd-fail', 'BFDFAIL', 'bfd-fail', 5275);
CALL insert_alarm_ec('crypto-sw-mismatch', 'CRSWMIS', 'crypto-sw-mismatch', 5280);
CALL insert_alarm_ec('elmi-not-operational', 'ELMINOPR', 'elmi-not-operational', 5284);
CALL insert_alarm_ec('elmi-sequence-number-mismatch', 'ELMISNM', 'elmi-sequence-number-mismatch', 5283);
CALL insert_alarm_ec('key-exchange-failed-no-resource', 'KXFLNOR', 'key-exchange-failed-no-resource', 5274);
CALL insert_alarm_ec('max-expected-slaves', 'MAXSLVS', 'max-expected-slaves', 5282);
CALL insert_alarm_ec('master-profile-inactive', 'MPINACT', 'master-profile-inactive', 34);
CALL insert_alarm_ec('output-current-warning', 'OCWAR', '12V output current warning', 5291);
CALL insert_alarm_ec('ptsf-unusable', 'PTSFUNS', 'ptsf-unusable', 5281);
CALL insert_alarm_ec('tca-announce-cancelled-events-rx', 'TANNCANR', 'tca-announce-cancelled-events-rx', 5307);
CALL insert_alarm_ec('tca-announce-cancelled-events-tx', 'TANNCANT', 'tca-announce-cancelled-events-tx', 5301);
CALL insert_alarm_ec('tca-announce-denied-events-rx', 'TANNDENR', 'tca-announce-denied-events-rx', 5304);
CALL insert_alarm_ec('tca-announce-denied-events-tx', 'TANNDENT', 'tca-announce-denied-events-tx', 5298);
CALL insert_alarm_ec('tca-times-announce-lease-expired', 'TANNLEXP', 'tca-times-announce-lease-expired', 5315);
CALL insert_alarm_ec('tca-announce-out-of-seq', 'TANNOOS', 'tca-announce-out-of-seq', 5295);
CALL insert_alarm_ec('tca-delay-resp-cancelled-events-rx', 'TDRPCANR', 'tca-delay-resp-cancelled-events-rx', 5309);
CALL insert_alarm_ec('tca-delay-resp-cancelled-events-tx', 'TDRPCANT', 'tca-delay-resp-cancelled-events-tx', 5303);
CALL insert_alarm_ec('tca-delay-resp-denied-events-rx', 'TDRPDENR', 'tca-delay-resp-denied-events-rx', 5306);
CALL insert_alarm_ec('tca-delay-resp-denied-events-tx', 'TDRPDENT', 'tca-delay-resp-denied-events-tx', 5300);
CALL insert_alarm_ec('tca-times-delay-resp-lease-expired', 'TDRPLEXP', 'tca-times-delay-resp-lease-expired', 5317);
CALL insert_alarm_ec('tca-delay-req-dropped', 'TDRQDROP', 'tca-delay-req-dropped', 5312);
CALL insert_alarm_ec('tca-fiber-loss-rcv-high', 'TFLRCVH', 'TCA fiber-loss receive high', 5271);
CALL insert_alarm_ec('tca-fiber-loss-tmt-high', 'TFLTMTH', 'TCA fiber-loss transmit high', 5272);
CALL insert_alarm_ec('tca-invalid-tlv-len-discard', 'TITLVDIS', 'tca-invalid-tlv-len-discard', 5313);
CALL insert_alarm_ec('tca-mgmt-frames-discard', 'TMGTDISC', 'tca-mgmt-frames-discard', 5297);
CALL insert_alarm_ec('tca-dynamic-slaves-dropped-count', 'TSLVDROP', 'tca-dynamic-slaves-dropped-count', 5311);
CALL insert_alarm_ec('tca-dynamic-slaves-learnt-count', 'TSLVLERN', 'tca-dynamic-slaves-learnt-count', 5310);
CALL insert_alarm_ec('tca-sync-cancelled-events-rx', 'TSYNCANR', 'tca-sync-cancelled-events-rx', 5308);
CALL insert_alarm_ec('tca-sync-cancelled-events-tx', 'TSYNCANT', 'tca-sync-cancelled-events-tx', 5302);
CALL insert_alarm_ec('tca-sync-denied-events-rx', 'TSYNDENR', 'tca-sync-denied-events-rx', 5305);
CALL insert_alarm_ec('tca-sync-denied-events-tx', 'TSYNDENT', 'tca-sync-denied-events-tx', 5299);
CALL insert_alarm_ec('tca-times-sync-lease-expired', 'TSYNLEXP', 'tca-times-sync-lease-expired', 5316);
CALL insert_alarm_ec('tca-sync-out-of-seq', 'TSYNOOS', 'tca-sync-out-of-seq', 5296);
CALL insert_alarm_ec('tca-unknown-tlv-type-discard', 'TUTLVDIS', 'tca-unknown-tlv-type-discard', 5314);
CALL insert_alarm_ec('cccp-unselected-defect', 'DCPUNS', 'cccp-unselected-defect', 5277);
CALL insert_alarm_ec('certificate-valid-in-future', 'CEVLDFU', 'Certificate Valid In Future', 33);
CALL insert_alarm_ec('certificate-expiring', 'CEXPNG', 'Certificate About To Expire', 5278);
CALL insert_alarm_ec('certificate-expired', 'CEXPRD', 'Certificate Expired', 5279);
CALL insert_event_ec('get-next-slcid', 'GETNXSID', 'Get Next Slcid');
CALL insert_event_ec('equalize', 'SLCEQLZ', 'Equalize');
CALL insert_event_ec('activate-end', 'ACTEND', 'Activate End');
CALL insert_event_ec('activate-fail', 'ACTFAIL', 'Activate Fail');
CALL insert_event_ec('activate-pass', 'ACTPASS', 'Activate Pass');
CALL insert_event_ec('activate-start', 'ACTSTR', 'Activate Start');
CALL insert_event_ec('activate-notif', 'ACTVNTF', 'Activate');
CALL insert_event_ec('adjust-process-begin', 'ADJBEGIN', 'Adjust Process Begin');
CALL insert_event_ec('adjust-pre-emphasis-prohibit', 'ADJPEPRH', 'Adjust Pre Emphasis Prohibit');
CALL insert_event_ec('adjust-prohibit-fail', 'ADJPRHF', 'Adjust Prohibit Fail');
CALL insert_event_ec('adjust-prohibit-pass', 'ADJPRHP', 'Adjust Prohibit Pass');
CALL insert_event_ec('config-file-generation-completed', 'CFGENCMT', 'Config File Generation Completed');
CALL insert_event_ec('config-file-generation-started', 'CFGENSTR', 'Config File Generation Started');
CALL insert_event_ec('adjust-complete', 'ADJCMPL', 'Adjust Complete');
CALL insert_event_ec('adjust-correction', 'ADJCOR', 'Adjust Correction');
CALL insert_event_ec('adjust-deny', 'ADJDENY', 'Adjust Deny');
CALL insert_event_ec('adjust-process-end', 'ADJEND', 'Adjust Process End');
CALL insert_event_ec('adjust-fail', 'ADJFAIL', 'Adjust Fail');
CALL insert_event_ec('adjust-null', 'ADJNULL', 'Adjust Null');
CALL insert_event_ec('adjust-pass', 'ADJPASS', 'Adjust Pass');
CALL insert_event_ec('adjust-power-begin', 'ADJPWRB', 'Adjust Power Begin');
CALL insert_event_ec('adjust-power-end', 'ADJPWRE', 'Adjust Power End');
CALL insert_event_ec('adjust-scheduled', 'ADJSCHED', 'Adjust Scheduled');
CALL insert_event_ec('adjust-start', 'ADJSTRT', 'Adjust Start');
CALL insert_event_ec('adjust-wait', 'ADJWAIT', 'Adjust Wait');
CALL insert_event_ec('adjust-pre-emphasis', 'AJTPREM', 'Adjust Pre Emphasis');
CALL insert_event_ec('alarm-notif', 'ALMNTF', 'Alarm');
CALL insert_event_ec('aps-update-notif', 'APSNTF', 'Aps-update-notif');
CALL insert_event_ec('autocdc-todc-change-event', 'ATODCCHG', 'Autocdc Todc Change Event');
CALL insert_event_ec('availability-change-alarm', 'AVCHGALM', 'Availability Change Alarm');
CALL insert_event_ec('backup-current-notif', 'BKCRNNTF', 'Backup Current');
CALL insert_event_ec('copy-certificate-completed', 'CERTCP', 'Copy-certificate-completed');
CALL insert_event_ec('configuration-error-assert', 'CERRASRT', 'Configuration Error Assert');
CALL insert_event_ec('configuration-error-clear', 'CERRCLR', 'Configuration Error Clear');
CALL insert_event_ec('cfm-llf-clear', 'CFMLLFC', 'Cfm Llf Clear');
CALL insert_event_ec('cfm-llf-raise', 'CFMLLFR', 'Cfm Llf Raise');
CALL insert_event_ec('cfm-rmep-mac-change', 'CFMRMPC', 'Cfm Rmep Mac Change');
CALL insert_event_ec('cfm-sf-clear', 'CFMSFC', 'Cfm Sf Clear');
CALL insert_event_ec('cfm-sf-raise', 'CFMSFR', 'Cfm Sf Raise');
CALL insert_event_ec('ptp-clock-state-changed', 'PCLKCHG', 'Ptp Clock State Changed');
CALL insert_event_ec('clear-log-notif', 'CLRLOG', 'Clear Log');
CALL insert_event_ec('cold-restart', 'COLD', 'Cold Restart');
CALL insert_event_ec('copy-to-remote-notif', 'CP2RMTNT', 'Copy To Remote');
CALL insert_event_ec('copy-database-certificate-notif', 'CPCERT', 'Copy-database-certificate-notif');
CALL insert_event_ec('copy-root-certificate-notif', 'CPCRTNTF', 'Copy-root-certificate-notif');
CALL insert_event_ec('crypto-parameters-reset-completed', 'CPRSTCMP', 'Crypto Parameters Reset Completed');
CALL insert_event_ec('crypto-service-config-change', 'CRPSVCCH', 'Crypto-service-config-change');
CALL insert_event_ec('crypto-user-condition', 'CRPUCOND', 'Crypto-user-condition');
CALL insert_event_ec('crypto-user-config-change', 'CRPUSRCH', 'Crypto-user-config-change');
CALL insert_event_ec('crypto-access-failed', 'CRYACCF', 'Crypto Access Failed');
CALL insert_event_ec('crypto-password-changed', 'CRYPWCHG', 'Crypto Password Changed');
CALL insert_event_ec('restore-db-notif', 'DBRSTNTF', 'Restore Db');
CALL insert_event_ec('certificate-signing-request-completed', 'CSRCP', 'Certificate-signing-request-completed');
CALL insert_event_ec('delay-session-start-stop-alarm', 'DSTSPALM', 'Delay Session Start Stop Alarm');
CALL insert_event_ec('entity-configuration-change-notif', 'ECCHG', 'Entity Configuration Change');
CALL insert_event_ec('entity-creation-notif', 'ECRT', 'Entity Creation');
CALL insert_event_ec('entity-deletion-notif', 'EDLT', 'Entity Deletion');
CALL insert_event_ec('entity-discovery-notif', 'EDSC', 'Entity Discovery');
CALL insert_event_ec('equalization-auto', 'EQAUTO', 'Equalization Auto');
CALL insert_event_ec('equalization-complete', 'EQCOMPL', 'Equalization Complete');
CALL insert_event_ec('equalization-deny', 'EQDENY', 'Equalization Deny');
CALL insert_event_ec('equalization-fail', 'EQFAIL', 'Equalization Fail');
CALL insert_event_ec('equalization-inprogress', 'EQINPRG', 'Equalization Inprogress');
CALL insert_event_ec('equalization-null', 'EQNULL', 'Equalization Null');
CALL insert_event_ec('equalization-pass', 'EQPASS', 'Equalization Pass');
CALL insert_event_ec('equalization-adjustment-raman', 'EQRAMAN', 'Equalization Adjustment Raman');
CALL insert_event_ec('equalization-start', 'EQSTART', 'Equalization Start');
CALL insert_event_ec('entity-state-change-notif', 'ESCHG', 'Entity State Change');
CALL insert_event_ec('eth-link-status', 'ETHLNKST', 'Eth Link Status');
CALL insert_event_ec('card-inventory-available', 'INVAVAIL', 'Card-inventory-available');
CALL insert_event_ec('invalid-file', 'INVFILE', 'Invalid File');
CALL insert_event_ec('invalid-pkg', 'INVPKG', 'Invalid Pkg');
CALL insert_event_ec('image-download-notif', 'IMGDWNLD', 'Image Download');
CALL insert_event_ec('kex-msg-notif', 'KEXNTF', 'Kex Msg');
CALL insert_event_ec('kexl2-data-notif', 'KEXL2DAT', 'Kexl2 Data');
CALL insert_event_ec('key-pair-accepted', 'KPACPTD', 'Key Pair Accepted');
CALL insert_event_ec('key-pair-completed', 'KPCMPLD', 'Key Pair Completed');
CALL insert_event_ec('kex-msg-request-notif', 'KEXRQNTF', 'Kex-msg-request-notif');
CALL insert_event_ec('csr-key-pair-generated', 'KEYPGEN', 'Csr-key-pair-generated');
CALL insert_event_ec('key-exchange-auth-changed', 'KXAUCHG', 'Key Exchange Auth Changed');
CALL insert_event_ec('key-exchange-fail-count-reset', 'KXCNRST', 'Key Exchange Fail Count Reset');
CALL insert_event_ec('kexl2-connection-request-notif', 'KXCONREQ', 'Kexl2-connection-request-notif');
CALL insert_event_ec('key-exchange-profile-config-change', 'KXPROFCH', 'Key-exchange-profile-config-change');
CALL insert_event_ec('key-exchange-failure', 'KXFAIL', 'Key Exchange Failure');
CALL insert_event_ec('load-config-notif', 'LDCFGNTF', 'Load Config');
CALL insert_event_ec('copy-to-remote-notif', 'LGCPTORM', 'Copy To Remote');
CALL insert_event_ec('login-failed-notif', 'LGNFN', 'Login Failed');
CALL insert_event_ec('login-success-notif', 'LGNSN', 'Login Success');
CALL insert_event_ec('syslog-server-certificate-activated', 'LGSRVACT', 'Syslog-server-certificate-activated');
CALL insert_event_ec('logout-notif', 'LGTN', 'Logout');
CALL insert_event_ec('lamp-test-button-pressed', 'LMPTSTP', 'Lamp-test-button-pressed');
CALL insert_event_ec('eth-loopback-operate', 'LPBKOPR', 'Eth-loopback-operate');
CALL insert_event_ec('eth-loopback-release', 'LPBKRLS', 'Eth Loopback Release');
CALL insert_event_ec('eth-loopback-release', 'RLSLPBK', 'Eth Loopback Release');
CALL insert_event_ec('eth-loopback-operate', 'OPRLPBK', 'Eth Loopback Operate');
CALL insert_event_ec('laser-off', 'LSROFF', 'Laser Off');
CALL insert_event_ec('laser-on', 'LSRON', 'Laser On');
CALL insert_event_ec('copy-from-remote', 'MPCPFROM', 'Copy-from-remote');
CALL insert_event_ec('created', 'MPCRTD', 'Created');
CALL insert_event_ec('deleted', 'MPDLTD', 'Deleted');
CALL insert_event_ec('locked', 'MPLCKD', 'Locked');
CALL insert_event_ec('load-completed', 'MPLDCMPL', 'Load-completed');
CALL insert_event_ec('load-failed', 'MPLDFLD', 'Load-failed');
CALL insert_event_ec('load-started', 'MPLDST', 'Load-started');
CALL insert_event_ec('unlocked', 'MPULCKD', 'Unlocked');
CALL insert_event_ec('loss-session-start-stop-alarm', 'LSTSPALM', 'Loss Session Start Stop Alarm');
CALL insert_event_ec('migration-notif', 'MIGRNTF', 'Migration');
CALL insert_event_ec('ntp-server-switch', 'NTPSRVSW', 'Ntp Server Switch');
CALL insert_event_ec('ocm-complete-notification', 'OCMNTF', 'Ocm Complete Notification');
CALL insert_event_ec('oper-sec-change-notif', 'OPSTCHG', 'Oper Sec Change');
CALL insert_event_ec('otdr-measurement-notification', 'OTDRMNTF', 'Otdr Measurement Notification');
CALL insert_event_ec('otdr-measurement-progress-notification', 'OTDRPRG', 'Otdr-measurement-progress-notification');
CALL insert_event_ec('otdr-start-notification', 'OTSTART', 'Otdr Start Notification');
CALL insert_event_ec('otdr-stop-notification', 'OTSTOP', 'Otdr Stop Notification');
CALL insert_event_ec('pace-condition', 'PACECOND', 'Pace-condition');
CALL insert_event_ec('authentication-failed', 'PAAUFAIL', 'Authentication Failed');
CALL insert_event_ec('pds-update-notif', 'PDSNTF', 'Pds Update');
CALL insert_event_ec('ready-to-poll-pm', 'PMPOL', 'Ready To Poll Pm');
CALL insert_event_ec('ping-notif', 'PNGNTF', 'Ping');
CALL insert_event_ec('protection-switch-event', 'PSWEVT', 'Protection Switch Event');
CALL insert_event_ec('reboot-notif', 'RBOOTNTF', 'Reboot');
CALL insert_event_ec('raman-characterization-complete', 'RCDONE', 'Raman Characterization Complete');
CALL insert_event_ec('equipment-reboot', 'REBOOT', 'Equipment Reboot');
CALL insert_event_ec('equipment-reboot-complete', 'REBOTCMP', 'Equipment Reboot Complete');
CALL insert_event_ec('replicate-end', 'REPEND', 'Replicate End');
CALL insert_event_ec('replicate-fail', 'REPFAIL', 'Replicate Fail');
CALL insert_event_ec('replicate-pass', 'REPPASS', 'Replicate Pass');
CALL insert_event_ec('replicate-start', 'REPSTR', 'Replicate Start');
CALL insert_event_ec('restore-factory-defaults-notif', 'RESFCNTF', 'Restore Factory Defaults');
CALL insert_event_ec('restore-system-defaults-notif', 'RESYSNTF', 'Restore System Defaults');
CALL insert_event_ec('remote-loopback-operate', 'RLPBKOPR', 'Remote-loopback-operate');
CALL insert_event_ec('remote-loopback-release', 'RLPBKRLS', 'Remote-loopback-release');
CALL insert_event_ec('remove-database-certificate-notif', 'RMCERT', 'Remove-database-certificate-notif');
CALL insert_event_ec('remote-changes', 'RMTCHG', 'Remote Changes');
CALL insert_event_ec('reset-system-defaults-notif', 'RSSYSNTF', 'Reset System Defaults');
CALL insert_event_ec('bfd-singlehop-notification', 'SHNOTIFY', 'Bfd Singlehop Notification');
CALL insert_event_ec('shutdown-notif', 'SHNTF', 'Shutdown');
CALL insert_event_ec('span-eq-init-result-event', 'SPEQEVT', 'Span Eq Init Result Event');
CALL insert_event_ec('syncref-switched', 'SREFSW', 'Syncref Switched');
CALL insert_event_ec('start-notif', 'STARTNTF', 'Start');
CALL insert_event_ec('ptp-port-state-changed', 'PPORTCHG', 'Ptp Port State Changed');
CALL insert_event_ec('state-change-notif', 'STNTF', 'State Change');
CALL insert_event_ec('stop-notif', 'STOPNTF', 'Stop');
CALL insert_event_ec('save-as-system-defaults-notif', 'SVSYSNTF', 'Save As System Defaults');
CALL insert_event_ec('tca-notif', 'TCA', 'Tca');
CALL insert_event_ec('trace-route-notif', 'TRCNTF', 'Trace Route');
CALL insert_event_ec('timeref-switched', 'TREFSW', 'Timeref Switched');
CALL insert_event_ec('time-zone-updated', 'TZUPDATD', 'Time Zone Updated');
CALL insert_event_ec('validate-end', 'VALEND', 'Validate End');
CALL insert_event_ec('validate-fail', 'VALFAIL', 'Validate Fail');
CALL insert_event_ec('validate-pass', 'VALPASS', 'Validate Pass');
CALL insert_event_ec('validate-start', 'VALSTR', 'Validate Start');
CALL insert_event_ec('volatile-trigger-notif', 'VTRGNTF', 'Volatile-trigger-notif');
CALL insert_event_ec('warm-restart', 'WARM', 'Warm Restart');
CALL insert_event_ec('dual-bank-device-status', 'DBDEVSTS', 'Dual Bank Device Status');
CALL insert_event_ec('diagnostics-generation-started', 'DGENSTART', 'Diagnostics Generation Started');
CALL insert_event_ec('diagnostics-generation-completed', 'DGENSTOP', 'Diagnostics Generation Completed');
CALL insert_event_ec('dh-hybrid-1-auto-condition', 'DH1ACOND', 'Dh-hybrid-1-auto-condition');
CALL insert_event_ec('mh-session-notification', 'MHSN', 'Mh Session Notification');
CALL insert_event_ec('bgp-nbr-restart', 'NBRRESTART', 'Bgp Nbr Restart');
CALL insert_event_ec('otdr-measurement-failed-notification', 'OTDRMFL', 'Otdr Measurement Failed Notification');
CALL insert_event_ec('sh-session-notification', 'SHSN', 'Sh Session Notification');
CALL insert_event_ec('bfd-session-notification', 'BFDN', 'Bfd Session Notification');
CALL insert_event_ec('connect-guard-config-change', 'CGCFGCH', 'Connect-guard-config-change');
CALL insert_event_ec('connect-guard-condition', 'CGRDCOND', 'Connect-guard-condition');
CALL insert_event_ec('autocdc-todc-busy-event', 'TODCBUSY', 'Autocdc Todc Busy Event');
CALL insert_event_ec('tx-originating-domain-id-changed', 'TXDIDCHG', 'Tx Originating Domain Id Changed');
CALL insert_event_ec('tx-originating-port-index-changed', 'TXPIXCHG', 'Tx Originating Port Index Changed');
CALL insert_event_ec('new-master', 'NMST', 'New Master');
CALL insert_event_ec('auto-copy-pkg-from-remote-end', 'ACPEND', 'Auto Copy Pkg From Remote End');
CALL insert_event_ec('span-eq-agates-event', 'SPQAGEVT', 'Span Eq Agates Event');
CALL insert_event_ec('refresh-ca', 'REFRCA', 'Refresh Ca');
CALL insert_event_ec('rx-originating-port-index-changed', 'RXPIXCHG', 'Rx Originating Port Index Changed');
CALL insert_event_ec('secure-entity-config-change', 'SECENTCH', 'Secure-entity-config-change');
CALL insert_event_ec('secure-entity-condition', 'SECNTCND', 'Secure-entity-condition');
CALL insert_event_ec('self-signed-certificate-activated', 'SLFSGNAC', 'Self-signed-certificate-activated');
CALL insert_event_ec('self-signed-certificate-generated', 'SLFSGNCR', 'Self-signed-certificate-generated');
CALL insert_event_ec('traffic-crc-notification', 'TRFCRC', 'Traffic Crc Notification');
CALL insert_event_ec('auto-copy-pkg-from-remote-start', 'ACPSTRT', 'Auto Copy Pkg From Remote Start');
CALL insert_event_ec('auto-install-and-activate-start', 'AUPGSTRT', 'Auto Install And Activate Start');
CALL insert_event_ec('rx-originating-domain-id-changed', 'RXDIDCHG', 'Rx Originating Domain Id Changed');
CALL insert_event_ec('adjacency-state-change', 'ADJSC', 'Adjacency State Change');
CALL insert_event_ec('llb-controller-session-event', 'LLBCSE', 'Llb Controller Session Event');
CALL insert_event_ec('auto-copy-pkg-from-remote-fail', 'ACPFAIL', 'Auto Copy Pkg From Remote Fail');
CALL insert_event_ec('llb-controller-event', 'LLBCE', 'Llb Controller Event');
CALL insert_event_ec('auto-copy-pkg-from-remote-pass', 'ACPPASS', 'Auto Copy Pkg From Remote Pass');
CALL insert_event_ec('auto-install-and-activate-end', 'AUPGEND', 'Auto Install And Activate End');
CALL insert_event_ec('adjacency-lost', 'ADJLOST', 'Adjacency Lost');
CALL insert_event_ec('auto-install-and-activate-pass', 'AUPGPASS', 'Auto Install And Activate Pass');
CALL insert_event_ec('auto-install-and-activate-fail', 'AUPGFAIL', 'Auto Install And Activate Fail');
CALL insert_event_ec('agates-complete', 'AGTSDONE', 'Agates Complete');
CALL insert_event_ec('activate-database-certificate-notif', 'ACTCERT', 'Activate-database-certificate-notif');
CALL insert_event_ec('ethernet-secure-entity-config-change', 'ETHSECCH', 'Ethernet Secure Entity Config Change');
CALL insert_event_ec('sat-start-notif', 'SATSTANT', 'Sat Start');
CALL insert_event_ec('sat-stop-notif', 'SATSTONT', 'Sat Stop');
CALL insert_event_ec('certificate-removed', 'CERTRMV', 'Certificate Removed');
CALL insert_event_ec('detected-media-info', 'DETMEDIA', 'Detected Media Info');
CALL insert_event_ec('generation-completed', 'FBRMPCPD', 'Generation Completed');
CALL insert_event_ec('generation-started', 'FBRMPSTR', 'Generation Started');
CALL insert_event_ec('file-transfer-notif', 'FILETNF', 'File Transfer Notif');
CALL insert_alarm_ec('timing-control-comm-fail', 'TMCTLCF', 'Timing and/or Control Comm Failure', 891);
CALL insert_alarm_ec('port-1m-hazard-potential', 'P1MHP', 'port-1m-hazzard-potential', 5292);
CALL insert_alarm_ec('port-1m-hazard-shutdown', 'P1MHS', 'port-1m-hazzard-shutdown', 5293);
CALL insert_alarm_ec('hw-resource-unavailable-recoverable', 'HWUAR', 'HW resource unavailable (rec.)', 5318);
CALL insert_event_ec('certificate-from-pem-created', 'CRTPEMCR', 'Certificate-from-pem-created');
CALL insert_event_ec('csr-activated', 'CSRACT', 'Csr-activated');
CALL insert_event_ec('csr-exported', 'CSREXP', 'Csr-exported');
CALL insert_event_ec('csr-key-pair-removed', 'CSRREM', 'Csr-key-pair-removed');
CALL insert_event_ec('minimum-attenuation-deny', 'MINATDNY', 'Minimum-attenuation-deny');
CALL insert_event_ec('minimum-attenuation-fail', 'MINATFL', 'Minimum-attenuation-fail');
CALL insert_event_ec('minimum-attenuation-pass', 'MINATPAS', 'Minimum-attenuation-pass');
CALL insert_alarm_ec('plug-2-host-interface-fail', 'P2HIFF', 'Plug-to-host Interface Failure', 35);
CALL insert_alarm_ec('host-2-plug-interface-fail', 'H2PIFF', 'Host-to-plug Interface Failure', 36);
CALL insert_alarm_ec('bundle-power-sum-alert', 'PWRSUM', 'Bundle power sum alert', 100);
CALL insert_alarm_ec('pump-force-remain-on', 'PUMPON', 'pump-force-remain-on', 5319);
CALL insert_alarm_ec('pilot-rx-suspended', 'PRXSUSP', 'Pilot Rx Suspended', 5321);
CALL insert_alarm_ec('local-degrade-indication', 'LDEGI', 'Local Degrade Indication', 5322);
CALL insert_alarm_ec('remote-degrade-indication', 'RDEGI', 'Remote Degrade Indication', 5323);
CALL insert_event_ec('meta-data-changed', 'MPMDCHD', 'Meta-data-changed');
CALL insert_event_ec('slc-equalize-notif', 'SLCEQNIF', 'Slc-equalize-notif');
CALL insert_event_ec('active-pilot-add-fail', 'ACTPLAF', 'Active-pilot-add-fail');
CALL insert_event_ec('adjust-restore', 'ADJRST', 'Adjust-restore');
CALL insert_event_ec('block-fail', 'BLCKFAIL', 'Block-fail');
CALL insert_event_ec('block-pass', 'BLCKPASS', 'Block-pass');
CALL insert_event_ec('sys-boot-completed', 'BOOTCMPL', 'Sys-boot-completed');
CALL insert_event_ec('certificate-trust-changed', 'CERTTRST', 'Certificate-trust-changed');
CALL insert_event_ec('controller-factory-reset-notif', 'CFCTRESNTF', 'Controller-factory-reset-notif');
CALL insert_event_ec('pkey-set-extractable', 'PKSEXT', 'Pkey-set-extractable');
CALL insert_event_ec('scheduled-db-backup-notif', 'SDBBNTF', 'Scheduled-db-backup-notif');
CALL insert_event_ec('sys-shutdown-initiated', 'SHTDNINIT', 'Sys-shutdown-initiated');
CALL insert_event_ec('software-downgrade', 'SWDWNGR', 'Software-downgrade');
CALL insert_event_ec('switch-fail', 'SWTFAIL', 'Switch-fail');
CALL insert_event_ec('switch-pass', 'SWTPASS', 'Switch-pass');
CALL insert_alarm_ec('temperature-high-warning', 'TEMPHW', 'Temperature high warning', 37);
CALL insert_alarm_ec('masterslave-cfg-fault', 'MTSLCFGF', 'Master/Slave Clock Config Fault', 327);
CALL insert_alarm_ec('pilot-add-loss', 'PLADDLOS', 'Pilot Add Loss', 5324);
CALL insert_event_ec('install-end', 'INSTEND', 'Install-end');
CALL insert_event_ec('install-fail', 'INSTFAIL', 'Install-fail');
CALL insert_event_ec('install-pass', 'INSTPASS', 'Install-pass');
CALL insert_event_ec('install-start', 'INSTSTR', 'Install-start');
CALL insert_event_ec('certificate-renewal-failed', 'CRNWFLD', 'Certificate-renewal-failed');
CALL insert_event_ec('certificate-renewal-succeed', 'CRNWSCD', 'Certificate-renewal-succeed');
CALL insert_event_ec('diagnostics-generation-started', 'DGENSTRT', 'Diagnostics Generation Started');
CALL insert_event_ec('system-log-stop', 'LOGSSP', 'System-log-stop');
CALL insert_event_ec('system-log-start', 'LOGSST', 'System-log-start');
CALL insert_event_ec('rollback-notification', 'RLBKNTF', 'Rollback-notification');
CALL insert_event_ec('sftp-server-stop', 'SFTPSP', 'Sftp-server-stop');
CALL insert_event_ec('sftp-server-start', 'SFTPST', 'Sftp-server-start');
CALL insert_event_ec('spq-scnrslt-notification', 'SPQSCNTF', 'Spq-scnrslt-notification');
CALL insert_event_ec('user-locked-out', 'ULCKO', 'User-locked-out');
CALL insert_event_ec('user-unlocked', 'ULCKU', 'User-unlocked');
CALL insert_event_ec('flex-ocm-state-notification', 'OCMNOTFY', 'Flex-ocm-state-notification');
CALL insert_event_ec('spq-flex-scnrslt-notification', 'SPQFLNTF', 'Spq-flex-scnrslt-notification');
CALL insert_alarm_ec('firmware-fault', 'FWFLT', 'Plug Firmware Fault', 38);
CALL insert_alarm_ec('auto-tuning-inprogress', 'AUTOTUN', 'Auto-tuning in progress', 5328);
CALL insert_alarm_ec('certificate-revoked', 'CEREVOK', 'Certificate Revoked', 5320);
CALL insert_event_ec('equipment-inserted', 'EQINS', 'Equipment-inserted');
CALL insert_event_ec('equipment-removed', 'EQRMV', 'Equipment-removed');
CALL insert_event_ec('file-renamed', 'FILERENAMED', 'File-renamed');
CALL insert_event_ec('valid-pkg', 'VALPKG', 'Valid-pkg');