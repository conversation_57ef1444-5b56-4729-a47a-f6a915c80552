CREATE INDEX i_cn_virtch_opt ON cn_conn_ochvirtchannels USING btree (optchconn_virtch_id);
CREATE INDEX i_cn_virtch_virtid ON cn_conn_ochvirtchannels USING btree (virtch_id);
CREATE INDEX i_cn_peer_vc ON cn_conn_peer_vc USING btree (vc_index);
CREATE INDEX i_cn_start_vc ON cn_conn_start_vc USING btree (vc_index);
CREATE INDEX i_cn_entity_ne_id_contained_in ON cn_entity USING btree (ne_id, containedin);
CREATE INDEX i_cn_mjct_pmid ON cn_managed_object USING btree (pmid);
CREATE INDEX i_cn_network_element_name on cn_network_element(lower(name0));
CREATE INDEX i_cn_shaper_flow ON cn_shaper_cm USING btree (flowfsp150cmdbimpl);
CREATE INDEX i_cn_shaper_port ON cn_shaper_cm USING btree (portfsp150cmaccdbimpl);
CREATE INDEX i_ev_con_val ON ev_event_conns USING btree (val);
CREATE INDEX i_ev_muvar_order ON ev_event_multivar USING btree (event_multivar_order);
CREATE INDEX i_ev_filter_node ON ev_filter_node USING btree (node_id);
CREATE INDEX i_ev_sev ON ev_severity USING btree (severity);
CREATE INDEX i_ev_trash ON ev_trash USING btree (trash);
CREATE INDEX i_synctst_dtype ON sync_test USING btree (dtype);

CREATE UNIQUE index unq_ne_idaidstring on cn_entity(ne_id, aidstring);

CREATE UNIQUE INDEX unq_mo_neid_entityindex ON cn_managed_object (ne_id, entityindex) WHERE entityindex IS NOT NULL;
CREATE INDEX ne_timestamp_1 ON ev_event USING btree (netimestamp, id);
CREATE INDEX ne_timestamp_2 ON ev_event USING btree (netimestamp desc, id desc);
CREATE INDEX nms_timestamp_1 ON ev_event USING btree (nmstimestamp, id);
CREATE INDEX nms_timestamp_2 ON ev_event USING btree (nmstimestamp asc, id desc);
CREATE INDEX IF NOT EXISTS ix_ev_event_assoc_fk_ev_event_assoc_id2 ON public.ev_event_assoc USING btree (objectid);

CREATE UNIQUE INDEX unq_ne_id_onn_id_mo_aid ON cn_conn_info USING btree (ne_id, conn_id, mo_aid) WHERE (parent_conn_info_id IS NULL);
CREATE UNIQUE INDEX unq_ne_id_onn_id_mo_aid_parent_conn_info_id ON cn_conn_info USING btree (ne_id, conn_id, mo_aid, parent_conn_info_id) WHERE (parent_conn_info_id IS NOT NULL);
CREATE INDEX i_conn_info_ne_id_mo_aid ON cn_conn_info USING btree (ne_id, mo_aid);

ALTER TABLE ONLY ml_te_property ADD CONSTRAINT unq_te_prop UNIQUE (te_id, key);

ALTER TABLE pm_record SET (autovacuum_vacuum_scale_factor = 0.005);
ALTER TABLE pm_manager_cfg SET (autovacuum_vacuum_scale_factor = 0.005);

ALTER TABLE ev_event SET (autovacuum_vacuum_scale_factor = 0.005);
ALTER TABLE ev_event_multivar SET (autovacuum_vacuum_scale_factor = 0.005);
ALTER TABLE ev_event_assoc SET (autovacuum_vacuum_scale_factor = 0.005);
ALTER TABLE ev_event_conns SET (autovacuum_vacuum_scale_factor = 0.005);
ALTER TABLE ev_event_sync_node SET (autovacuum_vacuum_scale_factor = 0.005);
ALTER TABLE ev_event_sync_ncd SET (autovacuum_vacuum_scale_factor = 0.005);
ALTER TABLE ev_event_history SET (autovacuum_vacuum_scale_factor = 0.005);
ALTER TABLE ev_event_hist_assoc SET (autovacuum_vacuum_scale_factor = 0.005);

CREATE UNIQUE INDEX unq_mac ON cn_network_element(mac) WHERE identkey=2;
CREATE UNIQUE INDEX unq_serial ON cn_network_element(serial) WHERE identkey=1;

ALTER TABLE ml_path SET (autovacuum_vacuum_scale_factor = 0.005);

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";