<!--
    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
-->
<beans  xmlns="http://www.springframework.org/schema/beans"
        xmlns:amq="http://activemq.apache.org/schema/core"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://activemq.apache.org/schema/core
        http://activemq.apache.org/schema/core/activemq-core.xsd">

    <bean id="encryptor" class="com.adva.nlms.mediation.security.crypto.props.EncryptableProperties">
        <property name="location"  value="activemq/conf/jms.properties" />
    </bean>

    <!-- Allows us to use system properties as variables in this configuration file -->
    <bean class="org.springframework.context.support.PropertySourcesPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>file:fnm.properties</value>
                <value>file:activemq/conf/jms.properties</value>
            </list>
        </property>
        <property name="properties" ref="encryptor"/>
    </bean>

    <broker xmlns="http://activemq.apache.org/schema/core" brokerName="${jms.brokerName}"
            dataDirectory="${activemq.data}"
            persistent="${activemq.persistent}"
            useJmx="${activemq.useJMX}" advisorySupport="true" dedicatedTaskRunner="false" enableStatistics="true">


        <persistenceAdapter>
            <kahaDB directory="${activemq.data}" ignoreMissingJournalfiles="true" checkForCorruptJournalFiles="true"  checksumJournalFiles="true" journalMaxFileLength="100mb" enableJournalDiskSyncs="false" concurrentStoreAndDispatchQueues="false" indexWriteBatchSize="5000" indexCacheSize="2000"/>
        </persistenceAdapter>

        <destinationPolicy>
            <policyMap>
                <policyEntries>
                    <policyEntry topic="topic.eventUpdate" producerFlowControl="false"  optimizedDispatch="true" topicPrefetch="500">
                        <pendingSubscriberPolicy>
                            <fileCursor/>
                        </pendingSubscriberPolicy>
                        <messageEvictionStrategy>
                            <oldestMessageEvictionStrategy/>
                        </messageEvictionStrategy>
                        <pendingMessageLimitStrategy>
                            <constantPendingMessageLimitStrategy limit="500"/>
                        </pendingMessageLimitStrategy>
                        <deadLetterStrategy>
                          <sharedDeadLetterStrategy processExpired="false" processNonPersistent="false"/>
                        </deadLetterStrategy>
                    </policyEntry>
                    <policyEntry topic=">" producerFlowControl="false"  optimizedDispatch="true" topicPrefetch="1000">
                        <pendingSubscriberPolicy>
                            <fileCursor/>
                        </pendingSubscriberPolicy>
                        <messageEvictionStrategy>
                            <oldestMessageEvictionStrategy/>
                        </messageEvictionStrategy>
                        <pendingMessageLimitStrategy>
                            <constantPendingMessageLimitStrategy limit="500"/>
                        </pendingMessageLimitStrategy>
                    </policyEntry>
                    <policyEntry queue=">" strictOrderDispatch="false" />
                </policyEntries>
            </policyMap>
        </destinationPolicy>

        <plugins>
            <statisticsBrokerPlugin/>
            <discardingDLQBrokerPlugin dropAll="true" dropTemporaryTopics="true" dropTemporaryQueues="true"/>
            <bean xmlns="http://www.springframework.org/schema/beans" id="authenticator"
                  class="com.adva.nlms.mediation.security.api.activemq.BrokerAuthPlugin">
                <property name="keyStore" value="certs/auth.ks"/>
                <property name="keyStorePassword" value="${keystorepassword}"/>
                <property name="keyStoreKeyPassword" value="${keystorekeypassword}"/>
            </bean>
            <bean xmlns="http://www.springframework.org/schema/beans" id="rmiInit"
                  class="com.adva.nlms.mediation.security.activemq.EncRmiConfigurationPlugin">
                <property name="connectorPort" value="${activemq.jmx.port}"/>
                <property name="rmiServerPort" value="${activemq.jmx.port}"/>
                <property name="connectorHost" value="localhost"/>
            </bean>
            <authorizationPlugin>
                <map>
                    <authorizationMap>
                        <authorizationEntries>
                            <authorizationEntry queue=">" read="admins,readonlys" write="admins" admin="admins,readonlys" />
                            <authorizationEntry topic=">" read="admins,readonlys" write="admins" admin="admins,readonlys" />
                            <authorizationEntry queue="ActiveMQ.Advisory.>" read="admins,readonlys" write="admins" admin="admins,readonlys"/>
                            <authorizationEntry topic="ActiveMQ.Advisory.>" read="admins,readonlys" write="admins" admin="admins,readonlys"/>
                        </authorizationEntries>
                        <tempDestinationAuthorizationEntry>
                            <tempDestinationAuthorizationEntry read="admins,readonlys" write="admins" admin="admins,readonlys"/>
                        </tempDestinationAuthorizationEntry>
                    </authorizationMap>
                </map>
            </authorizationPlugin>
        </plugins>

        <systemUsage>
            <systemUsage sendFailIfNoSpaceAfterTimeout="100" sendFailIfNoSpace="true">
                <memoryUsage>
                    <memoryUsage limit="400 mb"/>
                </memoryUsage>
                <storeUsage>
                    <storeUsage limit="3 gb"/>
                </storeUsage>
                <tempUsage>
                    <tempUsage limit="300 mb"/>
                </tempUsage>
            </systemUsage>
        </systemUsage>

        <sslContext>
            <sslContext
                    keyStore="certs/fnmserver.ks"
                    keyStorePassword="${keystorepassword}"
                    keyStoreKeyPassword="${keystorekeypassword}"/>
        </sslContext>

       <transportConnectors>
           <transportConnector name="openwire" uri="${jms.transportProtocol}://${jms.url}:${jms.port}?transport.enabledProtocols=TLSv1.2,TLSv1.3&amp;transport.enabledCipherSuites=TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_AES_256_GCM_SHA384&amp;wireFormat.maxInactivityDuration=0${jms.additional.args}"/>
        </transportConnectors>

    </broker>
</beans>
