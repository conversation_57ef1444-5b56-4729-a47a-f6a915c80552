#pool properties
pool.maxConnections=8
pool.maximumActive=500
pool.idleTimeout=60000
#persistency properties
activemq.persistent=true
activemq.data=./var/queue

jms.useCompression=true

infpool.maxConnections=12
infpool.maximumActive=500
infpool.idleTimeout=60000
infpool.prefetchSize=20

# In the following section, you may define the passphrases that protect the keystore and the private key that is used by ENC.
#
# In order to define ENCRYPTED passphrases, run the encrypt_passphrase script (.sh for linux, .bat for windows)
# and encrypt the passphrases that protect the keystore and the private key.
#
# Here are the needed properties to define the passphrases (uncomment by deleting the # character in the start of each line):
# Note: the ? character denotes that the provided passphrase is encrypted (using the encrypt_passphrase script).
# If for some reason you need to enter PLAINTEXT passphrases, please delete the ? character.

#?keystorepassword=PASTE THE PASSPHRASE HERE
#?keystorekeypassword=PASTE THE PASSPHRASE HERE
