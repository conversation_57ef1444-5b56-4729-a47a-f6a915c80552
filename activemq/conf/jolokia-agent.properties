#
# Copyright 2009-2014  <PERSON>, <PERSON><PERSON><PERSON>
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# ===============================================================
# Configuration properties for the JVM jolokia-agent
#

# Host address to bind to.
# Default: localhost, determined dynamically
# via InetAddress.getLocalHost()
host=localhost

# Port to listen to
port=8999

# Context path
agentContext=/jolokia

# Backlog of request to keep when queue
#backlog=10

# Executor class to use. By default, requests are processed
# sequentially in the same thread

# Possible values:
#  * "fixed"  : Thread pool with at max nrThreads
#  * "single" : A single thread serves all requests (default)
#  * "cached" : A thread pool which reuses threads and creates threads
#               on demand (unbounded)
# executor=fixed
# nrThreads=5

# User and password for basic authentication
# user=bragg
# password=secret

# How many entroes to keep in the history
#historyMaxEntries=10

# Switch on debugging
#debug=false

# How many debug entries to keep on the server
# side which can be queried by JMX
#debugMaxEntries=100

# Maximum traversal depth for serialization of complex objects.
# (default: 0. Use 0 for no truncation)
#maxDepth=15

# Maximum size of collections returned during serialization.
# If larger, the collection is truncated
# (default: 0. Use 0 for no truncation)
#maxCollectionSize=0

# Maximum number of objects returned by serialization
# (default: 0. Use 0 for no truncation)
#maxObjects=0

# Whether multicast discovery is enabled or not.
#discoveryEnabled=true

# HTTPS related setting
#secureSocketProtocol=TLS
#keyStoreType=JKS
#serverKeyAlgorithm=RSA
