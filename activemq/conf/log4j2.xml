<?xml version="1.0" encoding="UTF-8"?>
<Configuration name="AMQPropertiesConfig" strict="true">

<Properties>
	<Property name="logdir">var/log</Property>
</Properties>

<!--LOGGERS-->
<Loggers>
	<Root level="INFO">
		<AppenderRef ref="messaging"/>
	</Root>
	 <Logger name="org.apache" level="warn"  additivity="false" >
		<AppenderRef ref="messaging" />
	 </Logger>
	 <Logger name="org.apache.activemq" level="info"  additivity="false" >
		<AppenderRef ref="messaging" />
	 </Logger>
	 <Logger name="org.springframework" level="warn"  additivity="false" >
		<AppenderRef ref="messaging" />
	 </Logger>
</Loggers>

<!--APPENDERS-->
	<Appenders>
		<Appender name="messaging" type="RollingFile" fileName="${logdir}/messaging.log" filePattern="${logdir}/messaging.log.%i" append="true"  >
			<Layout type="PatternLayout" pattern="%d [%.15t] %-5p - %m%n" />
			<DefaultRolloverStrategy max="12" />
			<SizeBasedTriggeringPolicy size="50mb" />
		</Appender>
	</Appenders>
</Configuration>
<!-- END OF FILE -->
