::
:: Copyright 2023 Adtran Networks SE. All rights reserved.
::
:: Owner: lukaszre
::
:: $Id: ActiveMqImx.bat  2018-02-22 16:01:48Z lukaszre $
::

@echo off

cd ../../

set CURRENTDIR=%~dp0
set STARTPATH="%CURRENTDIR:~0,-1%"
set "NMS_HOME=%cd%"
set NMS_LIB=lib
set ACTIVEMQ_HOME=activemq
set ACTIVEMQ_LIB=%ACTIVEMQ_HOME%\lib
set ACTIVEMQ_DATA=%ACTIVEMQ_HOME%\data
set ACTIVEMQ_CONF=%ACTIVEMQ_HOME%\conf

set NMS_BIN=%NMS_HOME%\bin
set ACTIVEMQ_CLASSPATH=%NMS_LIB%\activemqLauncher.jar

if exist "%NMS_HOME%\jre64\bin\java.exe" (
set PRUN_HOME="%NMS_HOME%\prunsrv64.exe"
) else (
set PRUN_HOME="%NMS_HOME%\prunsrv32.exe"
)

set JMX_SUPPORT="false"
:: check property file
For /F "tokens=1* delims==" %%A IN (fnm.properties) DO (
    IF "%%A"=="activemq.useJMX" set JMX_SUPPORT=%%B
)
echo "JMX support: %JMX_SUPPORT%"
if "%JMX_SUPPORT%" == "true" (

if "%1%" == "false" (

    echo "Removing JMX agent from activemq"
    echo "Stopping jms service"
    %PRUN_HOME% //SS//advajms
    echo "Stopped"

    echo "Removing Jolokia agent from jms service"
    %PRUN_HOME% //US//advajms --Classpath="%ACTIVEMQ_CLASSPATH%" --JvmOptions=-Xmx512M ++JvmOptions=-Dorg.apache.activemq.SERIALIZABLE_PACKAGES="java.lang,java.util,org.apache.activemq,org.fusesource.hawtbuf,com.thoughtworks.xstream.mapper,ni,java.time,org.apache.commons.lang3.tuple,com.google.common.collect,java.beans,com.adva"

) else (
    echo "Adding JMX agent to activemq"
    echo "Stopping jms service"
    %PRUN_HOME% //SS//advajms
    echo "Stopped"

    echo "Updating jms service with Jolokia agent"
    set PROP_PATH="config=activemq/conf/jolokia-agent.properties"

    %PRUN_HOME% //US//advajms --Classpath="%ACTIVEMQ_CLASSPATH%" ++JvmOptions=-javaagent:lib\jolokia-jvm-1.7.1.jar=%PROP_PATH% ++JvmOptions=-Dorg.apache.activemq.SERIALIZABLE_PACKAGES="java.lang,java.util,org.apache.activemq,org.fusesource.hawtbuf,com.thoughtworks.xstream.mapper,ni,java.time,org.apache.commons.lang3.tuple,com.google.common.collect,java.beans,com.adva"
)
echo "Starting jms"
%PRUN_HOME% //ES//advajms

) else (
echo "JMX activemq.useJMX not set"
)