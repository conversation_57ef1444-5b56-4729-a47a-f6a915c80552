::
:: Copyright 2023 Adtran Networks SE. All rights reserved.
::
:: Owner: arongas
::

::  		 Basic script to perform some actions on ENC server
::			 Must be located under FSP Network Manager folder
::			 Can be run by a user with administrator rights

@ECHO OFF

pushd %~dp0

cd ../../
set "NMS_HOME=%cd%"
set BROKER_NAME=nmsServer

IF EXIST .\jre64\bin\java.exe (
  set "JAVA=%CD%\jre64\bin\javaw.exe" ) ELSE (
  set "JAVA=%CD%\jre\bin\javaw.exe"
)

set JAVASRV="%JAVA%" -server


set ACTIVEMQ_HOME=%NMS_HOME%\activemq
set ACTIVEMQ_LIB=%ACTIVEMQ_HOME%\lib
set ACTIVEMQ_DATA=%ACTIVEMQ_HOME%\data
set ACTIVEMQ_CONF=%ACTIVEMQ_HOME%\conf
set NMS_LIB=%NMS_HOME%\lib
set NMS_BIN=%NMS_HOME%\bin

set "ACTIVEMQ_CLASSPATH=%NMS_LIB%\activemqLauncher.jar"

set state=
for /f "tokens=5" %%a in ('netstat -aon ^|find " 0.0.0.0:33028 " ^|find /i " TCP " ') do set state=%%a

	if "%1%"=="start"  GOTO START
	if "%1%"=="stop"   GOTO STOP
	if "%1%"=="status" GOTO STATUS
	GOTO EXIT


:STATUS
IF '%state%'=='' (
  echo JMS server is NOT running
) ELSE (
  echo JMS server is running, PID = %state%
)
GOTO exit

:STOP
IF '%state%'=='' (
  echo JMS server is NOT running
) ELSE (
  taskkill /F /PID %state%
)
GOTO exit


:START
IF '%state%'=='' (
  start /b "" %JAVASRV% -Xmx1G -Dactivemq.home="%ACTIVEMQ_HOME%" -Dactivemq.base="%ACTIVEMQ_HOME%" -Dactivemq.conf="%ACTIVEMQ_CONF%" -Dactivemq.data="%ACTIVEMQ_DATA%" -Dlog4j.configurationFile=file:"%ACTIVEMQ_HOME%\conf\log4j2.xml" -jar "%ACTIVEMQ_CLASSPATH%" start
) ELSE (
  echo JMS server is running, PID = %state%
)
GOTO exit

:exit
pause
EXIT