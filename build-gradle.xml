<!--
  -  Copyright 2023 Adtran Networks SE. All rights reserved.
  -
  -  Owner: tdennler
  -
  -  $Id: $
  -->

<!-- Ant wrapper script to translate ant tasks to gradle tasks for production build -->
<project name="Ant.gradle.wrapper" default="jar" >

    <!-- Common gradle JVM definitions -->
    <property name="gradleDaemon" value="false"/>
    <!-- <property name="gradleJvmArgs" value="-Xms2g -Xmx4g"/> -->
    <property name="gradleJvmArgs" value="-Xmx4g"/>

    <condition property="isWindows">
        <os family="windows"/>
    </condition>

    <macrodef name="gradleCommon" description="Defines the parameters to pass to the gradle invocation">
        <attribute name="cmd" default="gradlew"/>
        <element name="args"/>
        <sequential>
            <echo>Launching gradle build using JRE ${java.home}</echo>
            <exec executable="@{cmd}" dir=".">
                <!--
                <env key="org.gradle.daemon" value="$gradleDaemon"/>
                <env key="org.gradle.jvmargs" value="$gradleJvmArgs"/>
                -->
                <env key="JAVA_HOME" value="${java.home}/.."/>

                <arg value="-Dorg.gradle.daemon=${gradleDaemon}"/>
                <arg value="-Dorg.gradle.jvmargs='${gradleJvmArgs}'"/>

                <!-- Team city does not set JAVA_HOME, use use parent dir of JRE dir which should be the JDK -->
                <arg value="-DJDK=${java.home}/.."/>

                <!--
                 -  Pass on team city parameters
                 -->
                <arg value="-Dbuild.number=${build.number}"/>
                <arg value="-Dbuild.vcs.number.build_fnm_trunk=${build.vcs.number.build_fnm_trunk}"/>
                <arg value="-Dbuild.vcs.number.nms_fnm_trunk_dist_FnmTrunk=${build.vcs.number.nms_fnm_trunk_dist_FnmTrunk}"/>
                <arg value="-Dfile.encoding=${file.encoding}"/>
                <arg value="-Dincrease.build.number=${increase.build.number}"/>
                <arg value="-Dfnm_svn_revision=${fnm_svn_revision}"/>
                <arg value="-Dcac_svn_revision=${cac_svn_revision}"/>

                <arg value="--stacktrace"/>
                <arg value="--info"/>
                <arg value="--parallel"/>

                <args/>
            </exec>
        </sequential>
    </macrodef>

    <macrodef name="gradleNix" description="Macro used for running gradle targets on NIX platforms (linux/solaris)">
        <element name="tasks"/>
        <sequential>
            <gradleCommon cmd="./gradlew">
                <args>
                    <tasks/>
                </args>
            </gradleCommon>
        </sequential>
    </macrodef>

    <macrodef name="gradleWin" description="Macro used for running gradle targets on windows platforms">
        <element name="tasks"/>
        <sequential>
            <gradleCommon cmd="cmd">
                <args>
                    <arg value="/c"/>
                    <arg value="gradlew.bat"/>
                    <tasks/>
                </args>
            </gradleCommon>
        </sequential>
    </macrodef>

    <target name="init"  description="Setup for build">
        <echo>Running init (NO-OP on gradle)</echo>
    </target>

    <target name="configure"  description="no-op">
        <echo>Running configure (NO-OP on gradle)</echo>
    </target>

    <target name="installExternalArtifacts.windows" if="isWindows">
        <echo>Executing gradlew all legacy</echo>
        <gradleWin>
            <tasks>
                <arg value="installExternalArtifacts"/>
            </tasks>
        </gradleWin>

    </target>

    <target name="installExternalArtifacts.nix" unless="isWindows">
        <echo>Executing gradlew all legacy</echo>
        <gradleNix>
            <tasks>
                <arg value="installExternalArtifacts"/>
            </tasks>
        </gradleNix>
    </target>

    <target name="installExternalArtifacts"  depends="installExternalArtifacts.windows,installExternalArtifacts.nix"
            description="Delegate to gradle to update workspace external dependencies">
    </target>

    <target name="build"  description="no-op">
        <echo>Running build (NO-OP on gradle)</echo>
    </target>

    <target name="jar.windows" if="isWindows">
        <echo>Executing gradlew all legacy</echo>
        <gradleWin>
            <tasks>
                <arg value="all"/>
                <arg value="legacy"/>
            </tasks>
        </gradleWin>

    </target>

    <target name="jar.nix" unless="isWindows">
        <echo>Executing gradlew all legacy</echo>
        <gradleNix>
            <tasks>
                <arg value="all"/>
                <arg value="legacy"/>
            </tasks>
        </gradleNix>
    </target>

    <target name="jar" depends="jar.windows,jar.nix"
            description="Delegate to gradle to build and place jars in locations for production">
    </target>

    <target name="makearchive.windows" if="isWindows">
        <echo>Executing gradlew makeArchive</echo>
        <gradleWin>
            <tasks>
                <arg value="makeArchive"/>
            </tasks>
        </gradleWin>

    </target>

    <target name="makearchive.nix" unless="isWindows">
        <echo>Executing gradlew makeArchive</echo>
        <gradleNix>
            <tasks>
                <arg value="makeArchive"/>
            </tasks>
        </gradleNix>
    </target>

    <target name="jar.makeArchive" depends="makearchive.windows,makearchive.nix"
            description="Delegate to gradle to create the archive.zip file">
    </target>

</project>
